### Ohio (DR) Stacks

Synth

```
cdk synth --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" ecs-cluster --profile monolith-prod
cdk synth --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" database --profile monolith-prod
cdk synth --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" ecs-load-balancer --profile monolith-prod
cdk synth --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" monolith-api --profile monolith-prod
```

Diff

```
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" ecs-cluster --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" database --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" ecs-load-balancer --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" monolith-api --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" api-events --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" tasks-dead-letter-queue --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" claimmd-queue --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" documentation-transcriptions-queue --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" elasticache --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" carepatron-events --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" tasks-queue --profile monolith-prod
cdk diff --app "npx ts-node --prefer-ts-exts bin/ohio-stacks.ts" service-discovery --profile monolith-prod
```

### Cognito Stacks

Synth

```
cdk synth --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-local-user
cdk synth --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-auth-certs
cdk synth --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-lambdas
cdk synth --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-user-pool
cdk synth --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" monolith-cognito-client
```

Diff

```
cdk diff --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-local-user --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-auth-certs --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-lambdas --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" cognito-user-pool --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/cognito-stacks.ts" monolith-cognito-client --profile monolith-local
```

### Chime Stacks

Synth

```
cdk synth --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-local-user
cdk synth --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-media-capture-bucket
cdk synth --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-media-pipeline
```

Diff

```
cdk diff --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-local-user --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-media-capture-bucket --profile monolith-local
cdk diff --app "npx ts-node --prefer-ts-exts bin/chime-stacks.ts" chime-media-pipeline --profile monolith-local
```

### Lambda Stacks

Synth

```
cdk synth --app "npx ts-node --prefer-ts-exts bin/lambda-stacks.ts" waf-ip-sets
cdk synth --app "npx ts-node --prefer-ts-exts bin/lambda-stacks.ts" app-cloudfront-access-logs
```

Diff

```
cdk diff --app "npx ts-node --prefer-ts-exts bin/lambda-stacks.ts" waf-ip-sets --profile monolith-staging
cdk diff --app "npx ts-node --prefer-ts-exts bin/lambda-stacks.ts" app-cloudfront-access-logs --profile monolith-staging
```

### Replayer Cognito Stacks

```
cdk synth --app "npx ts-node --prefer-ts-exts bin/replayer-cognito-stacks.ts" session-replayer-cognito
```

Diff

```
cdk diff --app "npx ts-node --prefer-ts-exts bin/replayer-cognito-stacks.ts" session-replayer-cognito --profile monolith-staging
```
