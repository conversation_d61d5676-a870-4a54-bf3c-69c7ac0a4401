#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { BaseConfig, getBaseConfig } from '../context/baseConfig';
import { getEcsClusterConfig } from '../context/ecsClusterConfig';
import { EcsClusterStack } from '../lib/ecs-cluster-stack';
import { getMonolithApiConfig } from '../context/monolithApiConfig';
import { ApiServiceStack } from '../lib/api-service-stack';
import { getEcsLoadBalancerConfig } from '../context/ecsLoadBalancerConfig';
import { EcsLoadBalancerStack } from '../lib/ecs-load-balancer-stack';
import { getDatabaseConfig } from '../context/databaseConfig';
import { DatabaseStack } from '../lib/database-stack';
import { getApiEventsConfig } from '../context/events/apiEventsConfig';
import { ApiEventsStack } from '../lib/events/api-events-stack';
import { getTasksDeadLetterQueueConfig } from '../context/sqs/tasksDeadLetterQueueConfig';
import { TasksDeadLetterQueueStack } from '../lib/sqs/tasks-dead-letter-queue';
import { getClaimMDQueueConfig } from '../context/sqs/claimMdQueueConfig';
import { QueueStack } from '../lib/sqs/queue-stack';
import { getDocumentationTranscriptionsQueueConfig } from '../context/sqs/documentationTranscriptionsQueueConfig';
import { DocumentationTranscriptionsQueueStack } from '../lib/sqs/documentation-transcriptions-queue';
import { getElastiCacheConfig } from '../context/elasticache/getElastiCacheConfig';
import { ElastiCacheStack } from '../lib/elasticache/elasticache-stack';
import { getCarepatronEventsConfig } from '../context/events/carepatronEventsConfig';
import { CarepatronEventsStack } from '../lib/events/carepatron-events-stack';
import { getTasksQueueConfig } from '../context/sqs/tasksQueueConfig';
import { TasksQueueStack } from '../lib/sqs/tasks-queue';
import { getServiceDiscoveryConfig } from '../context/serviceDiscoveryConfig';
import { ServiceDiscoveryStack } from '../lib/service-discovery-stack';

const baseConfig: BaseConfig = getBaseConfig();
const app = new cdk.App();

const ecsClusterConfig = getEcsClusterConfig();
const ecsCluster = new EcsClusterStack(app, 'ecs-cluster', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${ecsClusterConfig.AWSRegion}-ecs-cluster`,
	description: `${baseConfig.AccountName} ${baseConfig.AWSEnvironment} ${ecsClusterConfig.AWSRegion} ECS Cluster`,
	env: {
		account: baseConfig.AWSAccountID,
		region: ecsClusterConfig.AWSRegion,
	},
	config: ecsClusterConfig,
	environment: baseConfig.AWSEnvironment,
	accountName: baseConfig.AccountName,
});

const loadBalancerConfig = getEcsLoadBalancerConfig();
const ecsLoadBalancer = new EcsLoadBalancerStack(app, 'ecs-load-balancer', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${loadBalancerConfig.AWSRegion}-${loadBalancerConfig.ServiceName}`,
	description: `${baseConfig.AccountName} ${baseConfig.AWSEnvironment} ${loadBalancerConfig.AWSRegion} ECS Load Balancer`,
	env: {
		account: baseConfig.AWSAccountID,
		region: loadBalancerConfig.AWSRegion,
	},
	config: loadBalancerConfig,
	environment: baseConfig.AWSEnvironment,
	accountName: baseConfig.AccountName,
	serviceName: loadBalancerConfig.ServiceName,
});

const monolithApiServiceConfig = getMonolithApiConfig();
new ApiServiceStack(app, 'monolith-api', {
	stackName: `${monolithApiServiceConfig.ServiceName}-${baseConfig.AWSEnvironment}-${monolithApiServiceConfig.AWSRegion}`,
	description: `Carepatron monolith API service.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: monolithApiServiceConfig.AWSRegion,
	},
	cluster: ecsCluster.cluster,
	config: monolithApiServiceConfig,
	environment: baseConfig.AWSEnvironment,
	accountName: baseConfig.AccountName,
	serviceName: monolithApiServiceConfig.ServiceName,
	loadBalancer: ecsLoadBalancer.loadBalancer,
	loadBalancerHttpsListener: ecsLoadBalancer.loadBalancerHttpsListener,
	loadBalancerFullName: ecsLoadBalancer.loadBalancerFullName,
});

const databaseConfig = getDatabaseConfig();
new DatabaseStack(app, 'database', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${databaseConfig.AWSRegion}-database`,
	description: 'Carepatron Aurora PostgreSQL database',
	env: {
		account: baseConfig.AWSAccountID,
		region: databaseConfig.AWSRegion,
	},
	config: databaseConfig,
	environment: baseConfig.AWSEnvironment,
	accountName: baseConfig.AccountName,
	serviceName: 'database',
});

const apiEventsConfig = getApiEventsConfig();
new ApiEventsStack(app, 'api-events', {
	stackName: apiEventsConfig.StackName,
	description: `Creates an API Connection and destination secured with an API Key in KMS.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: apiEventsConfig.AWSRegion,
	},
	config: apiEventsConfig,
});

const tasksDeadLetterQueueConfig = getTasksDeadLetterQueueConfig();
new TasksDeadLetterQueueStack(app, 'tasks-dead-letter-queue', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${tasksDeadLetterQueueConfig.AWSRegion}-tasks-dead-letter-queue`,
	description: `Dead letter SQS queue for tasks.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: tasksDeadLetterQueueConfig.AWSRegion,
	},
	config: tasksDeadLetterQueueConfig,
	environment: baseConfig.AWSEnvironment,
});

const claimMDQueueConfig = getClaimMDQueueConfig();
new QueueStack(app, 'claimmd-queue', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${claimMDQueueConfig.AWSRegion}-claimmd-queue`,
	description: `SQS queue for ClaimMD processing.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: claimMDQueueConfig.AWSRegion,
	},
	config: claimMDQueueConfig,
	environment: baseConfig.AWSEnvironment,
});

const documentationTranscriptionsQueueServiceConfig = getDocumentationTranscriptionsQueueConfig();
new DocumentationTranscriptionsQueueStack(app, 'documentation-transcriptions-queue', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${documentationTranscriptionsQueueServiceConfig.AWSRegion}-documentation-transcriptions-queue`,
	description: `${baseConfig.AccountName} ${baseConfig.AWSEnvironment} ${documentationTranscriptionsQueueServiceConfig.AWSRegion} documentation-transcriptions-queue`,
	env: {
		account: baseConfig.AWSAccountID,
		region: documentationTranscriptionsQueueServiceConfig.AWSRegion,
	},
	config: documentationTranscriptionsQueueServiceConfig,
	environment: baseConfig.AWSEnvironment,
});

const elasticacheConfig = getElastiCacheConfig();
new ElastiCacheStack(app, 'elasticache', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${elasticacheConfig.AWSRegion}-elasticache`,
	description: `Elasticache cluster for Carepatron applications`,
	env: {
		account: baseConfig.AWSAccountID,
		region: elasticacheConfig.AWSRegion,
	},
	config: elasticacheConfig,
	environment: baseConfig.AWSEnvironment,
	accountName: baseConfig.AccountName,
	serviceName: 'elasticache',
});

const tasksQueueConfig = getTasksQueueConfig();
new TasksQueueStack(app, 'tasks-queue', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${tasksQueueConfig.AWSRegion}-tasks-queue`,
	description: `SQS queue for tasks.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: tasksQueueConfig.AWSRegion,
	},
	config: tasksQueueConfig,
	environment: baseConfig.AWSEnvironment,
});

const carepatronEventsConfig = getCarepatronEventsConfig();
new CarepatronEventsStack(app, 'carepatron-events', {
	stackName: carepatronEventsConfig.StackName,
	description: `Provisions an event bus and associated archive.`,
	env: {
		account: baseConfig.AWSAccountID,
		region: carepatronEventsConfig.AWSRegion,
	},
	config: carepatronEventsConfig,
});

const serviceDiscoveryConfig = getServiceDiscoveryConfig();
new ServiceDiscoveryStack(app, 'service-discovery', {
	stackName: `${baseConfig.AccountName}-${baseConfig.AWSEnvironment}-${serviceDiscoveryConfig.AWSRegion}-service-discovery`,
	description: `Service discovery stack for Carepatron microservices communication`,
	env: {
		account: baseConfig.AWSAccountID,
		region: serviceDiscoveryConfig.AWSRegion,
	},
	config: serviceDiscoveryConfig,
});
