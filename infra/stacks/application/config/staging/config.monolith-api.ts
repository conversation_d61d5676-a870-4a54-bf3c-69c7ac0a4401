import { ApiServiceConfig } from '../../context/apiServiceConfig';
import { IamManagedPolicyNames, IamPrincipalTypes, IamPrincipals } from '../../context/helpers/buildIamRoles';
import { AWSRegions, VPC } from '../../enums';
import * as ecs from 'aws-cdk-lib/aws-ecs';

export const stagingConfig: ApiServiceConfig = {
	ImageTag: 'latest',
	VpcId: VPC.VPC_ID_STAGING,
	Subnets: VPC.PRIVATE_SUBNETS_STAGING,
	AWSRegion: AWSRegions.SYDNEY,
	ServiceName: 'monolith-api',
	EcrRepositoryName: 'carepatron-app/monolith-api',
	ContainerCpu: 1024,
	ContainerSoftMemoryLimitMiB: 2048,
	ContainerHardMemoryLimitMiB: 2048,
	CpuArchitecture: ecs.CpuArchitecture.X86_64,
	AwsLogsMultilinePattern: `^{"Timestamp"`,
	ServiceMinimumCount: 2,
	ServiceMaximumCount: 6,
	ServiceMaxHealthyPercent: 200,
	ServiceScalingTargetCpuUtilizationPercent: 80,
	ServiceScalingTargetMemoryUtilizationPercent: 80,
	ContainerPort: 80,
	ContianerHealthCheckPath: '/api/ping',
	ContinerHealthCheckInterval: 130,
	ContainerHealthCheckTimeout: 120,
	ContainerHealthyThresholdCount: 3,
	ContainerUnhealthyThresholdCount: 2,
	ContainerHealthCheckSuccessCodes: '200-299',
	LogGroupName: 'monolith/api',
	UnHealthyHostCountAlarmThreshold: 1,
	TargetResponseTimeSecondsAlarmThreshold: 2,
	ServiceDiscoveryConfiguration: {
		NamespaceId: 'ns-ysj5aistadvgtzvg',
		NamespaceName: 'carepatron-staging.internal',
		NamespaceArn: 'arn:aws:servicediscovery:ap-southeast-2:907270438193:namespace/ns-ysj5aistadvgtzvg',
		ServiceName: 'api',
		ServiceTtlSeconds: 60,
	},
	EnvironmentVariables: {
		ASPNETCORE_ENVIRONMENT: 'Staging',
		AWS_DEFAULT_REGION: 'ap-southeast-2',
		Logging__LogGroup: 'monolith/api',
		CorsPolicy__Origins: "['https://app.carepatron-staging.com', 'https://book.carepatron-staging.com']", // passing array in like this might be a problem
		CognitoConfig__PoolId: 'ap-southeast-2_tONoqY2PZ',
		CognitoConfig__ClientId: '1oiv6vrbpftkf4eldhd47j3p37',
		ChimeConfig__CallUrl: 'https://app.carepatron-staging.com/Calls/{id}',
		ChimeConfig__ClientCallUrl: 'https://app.carepatron-staging.com/ClientCalls/{id}',
		SimpleEmailConfig__AcceptInviteUrl: 'https://app.carepatron-staging.com/Register',
		SimpleEmailConfig__FilesBucketUrl: 'https://carepatron-staging-files.s3-ap-southeast-2.amazonaws.com/',
		SimpleEmailConfig__GoingLink:
			'https://app.carepatron-staging.com/AppointmentResponse/{id}/Accept?cId={cId}&startDate={startDate}',
		SimpleEmailConfig__CantGoLink:
			'https://app.carepatron-staging.com/AppointmentResponse/{id}/Reject?cId={cId}&startDate={startDate}',
		SimpleEmailConfig__VideoCallLink: 'https://app.carepatron-staging.com/ClientCalls?email={email}&type=client',
		SimpleEmailConfig__PayNowLink: 'https://app.carepatron-staging.com/Invoices/{id}/Pay',
		SimpleEmailConfig__ClientEnrolmentLink:
			'https://app.carepatron-staging.com/ClientOnboard/{contactId}?token={token}',
		SimpleEmailConfig__ViewInvoiceLink: 'https://app.carepatron-staging.com/Invoices/{id}/View',
		SimpleEmailConfig__ViewPaidInvoiceLink: 'https://app.carepatron-staging.com/Invoices/{id}',
		SimpleEmailConfig__ChangeEmailVerificationLink:
			'https://app.carepatron-staging.com/VerifyEmail?pid={0}&token={1}',
		StripeConfig__ReturnUrl: 'https://app.carepatron-staging.com',
		CalendarConfig__RedirectUri: 'https://app.carepatron-staging.com/api/connectedapps/oauth/callback',
		CalendarConfig__CarepatronClient: 'https://app.carepatron-staging.com/Calendar',
		CalendarConfig__CallsUri: 'https://app.carepatron-staging.com/Calls/{callId}',
		CalendarConfig__MicrosoftCarepatronNotificationsUri:
			'https://app.carepatron-staging.com/api/connectedapps/notifications/microsoft',
		CalendarConfig__GoogleCarepatronNotificationsUri:
			'https://app.carepatron-staging.com/api/connectedapps/notifications/google',
		SqsConfiguration__TaskQueueUrl:
			'https://sqs.ap-southeast-2.amazonaws.com/907270438193/tasks-queue-staging.fifo',
		CommonConfiguration__CarepatronApiUri: 'https://app.carepatron-staging.com/',
		CommonConfiguration__CarepatronUiUri: 'https://app.carepatron-staging.com/',
		CommonConfiguration__CarepatronBookingUiUri: 'https://book.carepatron-staging.com/',
		ApiDestinationConfiguration__ApiDestinationArn:
			'arn:aws:events:ap-southeast-2:907270438193:api-destination/staging-carepatron-api-destination/a142dbe7-ee85-4f2b-89ab-f6156d231a68',
		ApiDestinationConfiguration__InvokeApiDestinationRoleArn:
			'arn:aws:iam::907270438193:role/staging-api-events-stack-InvokeApiRole-RdHN5psBmxeg',
		ApiDestinationConfiguration__DeadLetterQueueArn:
			'arn:aws:sqs:ap-southeast-2:907270438193:staging-api-events-stack-ApiDestinationDLQ-N73ed2wmVZD0',
		NotificationsClient__BaseUrl: 'http://notifications-api.carepatron-staging.internal', //use the internal url via http that is resolved by AWS Cloud Map
		NotificationsClient__Issuer: 'https://auth.carepatron-staging.com',
		NotificationsClient__ClientId: 'odk329ij0kievtgprmgr3lv8',
	},
	SSMEnvironmentVariables: [
		{
			EnvironmentVariableName: 'ConnectionStrings__CarePatronDb',
			ParameterName: '/monolith/database/connection-string',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__WebhookSecret',
			ParameterName: '/monolith/stripe/webhook-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__PartnerWebhookSecret',
			ParameterName: '/monolith/stripe/partner-webhook-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__ApiKey',
			ParameterName: '/monolith/stripe/api-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'RecaptchaConfig__Secret',
			ParameterName: '/monolith/api/recaptcha-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__WebhookConnectSecret',
			ParameterName: '/monolith/stripe/webhook-connect-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__GoogleClientSecret',
			ParameterName: '/monolith/calendar/google-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__MicrosoftClientSecret',
			ParameterName: '/monolith/calendar/microsoft-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__ZoomClientSecret',
			ParameterName: '/monolith/calendar/zoom-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleVertexAIConfiguration__PrivateKey',
			ParameterName: '/monolith/google-vertex-ai/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleTranscriptionConfiguration__PrivateKey',
			ParameterName: '/monolith/google/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'NotificationsClient__ClientSecret',
			ParameterName: '/monolith/notifications-client/client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__PublicKey',
			ParameterName: '/monolith/partnerstack/public-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__SecretKey',
			ParameterName: '/monolith/partnerstack/secret-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__ConversionToken',
			ParameterName: '/monolith/partnerstack/conversion-token',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleAgentConfiguration__PrivateKey',
			ParameterName: '/monolith/google-agent/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'MicrosoftGraph__SecretKey',
			ParameterName: '/monolith/api/microsoft-graph-secret-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'ClaimMdConfiguration__ApiKey',
			ParameterName: '/shared/claim-md/api-key',
			Version: 0,
		},
	],
	SecretsManagerEnvironmentVariables: [
		// not used as an environment variable, adding it here will generate IAM policy permissions
		{
			EnvironmentVariableName: 'ApiKeySecretId',
			SecretName: 'staging-carepatron-api-api-secret',
			UniqueIdentifier: 'jGtYHK',
		},
		{
			EnvironmentVariableName: 'Redis__ElastiCacheAuthToken',
			SecretName: 'staging-elasticache-auth-secret',
			UniqueIdentifier: 'UzZySJ',
		},
	],
	Route53Configs: [
		{
			HostedZoneId: 'Z05235681QASGEL7C4X62',
			HostedZoneName: 'app.carepatron-staging.com',
			ARecord: {
				HostedZoneId: 'Z2FDTNDATAQYW2', // This is the hosted zone ID for all CloudFront distributions, it doesn't change
				DnsName: 'd2a5r7q2kfc397.cloudfront.net',
			},
		},
		{
			HostedZoneId: 'Z05235681QASGEL7C4X62',
			HostedZoneName: 'book.carepatron-staging.com',
			ARecord: {
				HostedZoneId: 'Z2FDTNDATAQYW2', // This is the hosted zone ID for all CloudFront distributions, it doesn't change
				DnsName: 'd35rppqegk7esa.cloudfront.net',
			},
		},
	],
	ServiceSecurityGroups: [
		{
			AllowAllOutbound: true,
			Description: 'Monolith Api Service Security Group',
			IngressRules: [
				{
					SecurityGroupExportName: 'monolith-staging-ap-southeast-2-alb-sg-id',
					Port: 80,
					Description: 'allow http traffic in from load balancer security group',
				},
				{
					Cidr: VPC.PRIVATE_SUBNETS_CIDR_STAGING[0],
					Port: 80,
					Description: 'allow http traffic in from private subnet 1',
				},
				{
					Cidr: VPC.PRIVATE_SUBNETS_CIDR_STAGING[1],
					Port: 80,
					Description: 'allow http traffic in from private subnet 2',
				},
			],
		},
	],
	LoadbalancerListenerRules: [
		{
			Priority: 11,
			Conditions: {
				HostHeaders: ['book.carepatron-staging.com'],
				PathPatterns: ['/api/*'],
				HttpHeaders: [
					{
						HttpHeaderName: 'X-CloudFront-Origin',
						Values: ['/shared/cloudfront/x-cloudfront-origin'],
						FromSecretsManager: true,
					},
				],
			},
		},
		{
			Priority: 12,
			Conditions: {
				HostHeaders: ['app.carepatron-staging.com'],
				PathPatterns: ['/api/*'],
				HttpHeaders: [
					{
						HttpHeaderName: 'X-CloudFront-Origin',
						Values: ['/shared/cloudfront/x-cloudfront-origin'],
						FromSecretsManager: true,
					},
				],
			},
		},
	],
	TaskRole: {
		AssumedByPrincipal: {
			PrincipalType: IamPrincipalTypes.Service,
			Principal: IamPrincipals.ECSTaskRole,
		},
		ManagedPolicies: [
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonChimeSDK,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonCognitoPowerUser,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonEventBridgeFullAccess,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonS3FullAccess,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonSESFullAccess,
			},
		],
		InlinePolicies: [
			{
				PolicyName: 'SecretsManagerFullReadAccess',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'FullReadAccess',
							Actions: ['secretsmanager:GetSecretValue', 'secretsmanager:DescribeSecret'],
							Resources: ['arn:aws:secretsmanager:*:907270438193:secret:*'],
						},
					],
				},
			},
			{
				PolicyName: 'KMS',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'ListAndDescribe',
							Actions: ['kms:DescribeKey', 'kms:ListAliases'],
							Resources: ['*'],
						},
					],
				},
			},
			{
				PolicyName: 'InvokePdfUtilsLambda',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'InvokeLambda',
							Actions: ['lambda:InvokeFunction'],
							Resources: [
								'arn:aws:lambda:ap-southeast-2:907270438193:function:carepatron-pdf-utils-function',
							],
						},
					],
				},
			},
			{
				PolicyName: 'InvokeTemplateImportUtilsLambda',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'InvokeLambda',
							Actions: ['lambda:InvokeFunction'],
							Resources: [
								'arn:aws:lambda:ap-southeast-2:907270438193:function:carepatron-template-import-utils-function',
							],
						},
					],
				},
			},
		],
	},
	TaskExecutionRole: {
		AssumedByPrincipal: {
			PrincipalType: IamPrincipalTypes.Service,
			Principal: IamPrincipals.ECSTaskRole,
		},
		ManagedPolicies: [
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonECSTaskExecutionRolePolicy,
			},
		],
		InlinePolicies: [
			{
				PolicyName: 'KMSKeyDecryptAccess',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'KmsDecrypt', // todo: check if this is needed, the kms key doesn't seem to be in use by any ssm or secrets manager passwords
							Actions: ['kms:Decrypt'],
							Resources: [
								'arn:aws:kms:ap-southeast-2:907270438193:key/mrk-c3aa1966a5d044448f4722b5d0035193',
							],
						},
					],
				},
			},
		],
	},
};
