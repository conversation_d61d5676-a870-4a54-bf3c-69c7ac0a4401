import { ApiServiceConfig } from '../../context/apiServiceConfig';
import { IamManagedPolicyNames, IamPrincipalTypes, IamPrincipals } from '../../context/helpers/buildIamRoles';
import { AWSRegions, VPC } from '../../enums';
import * as applicationautoscaling from 'aws-cdk-lib/aws-applicationautoscaling';
import * as ecs from 'aws-cdk-lib/aws-ecs';

export const ohioConfig: ApiServiceConfig = {
	ImageTag: 'latest',
	VpcId: VPC.VPC_ID_OHIO,
	Subnets: VPC.PRIVATE_SUBNETS_OHIO,
	AWSRegion: AWSRegions.OHIO,
	ServiceName: 'monolith-api',
	EcrRepositoryName: 'carepatron-app/monolith-api',
	ContainerCpu: 2048,
	ContainerSoftMemoryLimitMiB: 7168,
	ContainerHardMemoryLimitMiB: 8192,
	CpuArchitecture: ecs.CpuArchitecture.X86_64,
	AwsLogsMultilinePattern: `^{"Timestamp"`,
	ServiceDesiredCount: 0,
	ServiceMinimumCount: 0,
	ServiceMaximumCount: 0,
	ServiceMaxHealthyPercent: 200,
	ServiceScalingTargetCpuUtilizationPercent: 50,
	ServiceScalingTargetMemoryUtilizationPercent: 60,
	ContainerPort: 80,
	ContianerHealthCheckPath: '/api/ping',
	ContinerHealthCheckInterval: 130,
	ContainerHealthCheckTimeout: 120,
	ContainerHealthyThresholdCount: 3,
	ContainerUnhealthyThresholdCount: 2,
	LogGroupName: 'monolith/api',
	ContainerHealthCheckSuccessCodes: '200-299',
	UnHealthyHostCountAlarmThreshold: 1,
	TargetResponseTimeSecondsAlarmThreshold: 2,
	ServiceDiscoveryConfiguration: {
		NamespaceArn: 'arn:aws:servicediscovery:us-east-2:418502688622:namespace/ns-qiiuhgwcp752x6rn',
		NamespaceId: 'ns-qiiuhgwcp752x6rn',
		NamespaceName: 'ohio.carepatron.internal',
		ServiceName: 'api',
		ServiceTtlSeconds: 60,
	},
	EnvironmentVariables: {
		ASPNETCORE_ENVIRONMENT: 'DisasterRecovery',
		AWS_DEFAULT_REGION: 'us-east-2',
		Logging__LogGroup: 'monolith/api',
		GoogleVertexAIConfiguration__PrivateKeyId: '1d6da999a8d1c9d5279537209f993d7413641f41',
		NotificationsClient__BaseUrl: 'http://notifications-api.ohio.carepatron.internal', //use the internal url via http that is resolved by AWS Cloud Map
		NotificationsClient__Issuer: 'https://auth.carepatron.com',
		NotificationsClient__ClientId: '4l2i1ratp6jcf98plgdlegmh5e',
		Redis__ConnectionString: 'master.ohio-cache-cluster.swilef.use2.cache.amazonaws.com:6379', // DR situation: Might need to change when cache cluster is created
	},
	SSMEnvironmentVariables: [
		{
			// This value needs to be updated in SSM Parameter Store when the database is created in a DR scenario, before the API is started.
			EnvironmentVariableName: 'ConnectionStrings__CarePatronDb',
			ParameterName: '/monolith/database/connection-string',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__WebhookSecret',
			ParameterName: '/monolith/stripe/webhook-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__PartnerWebhookSecret',
			ParameterName: '/monolith/stripe/partner-webhook-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__ApiKey',
			ParameterName: '/monolith/stripe/api-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'RecaptchaConfig__Secret',
			ParameterName: '/monolith/api/recaptcha-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'StripeConfig__WebhookConnectSecret',
			ParameterName: '/monolith/stripe/webhook-connect-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__GoogleClientSecret',
			ParameterName: '/monolith/calendar/google-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__MicrosoftClientSecret',
			ParameterName: '/monolith/calendar/microsoft-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'CalendarConfig__ZoomClientSecret',
			ParameterName: '/monolith/calendar/zoom-client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleVertexAIConfiguration__PrivateKey',
			ParameterName: '/monolith/google-vertex-ai/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleTranscriptionConfiguration__PrivateKey',
			ParameterName: '/monolith/google/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'NotificationsClient__ClientSecret',
			ParameterName: '/monolith/notifications-client/client-secret',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__PublicKey',
			ParameterName: '/monolith/partnerstack/public-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__SecretKey',
			ParameterName: '/monolith/partnerstack/secret-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'PartnerStackConfiguration__ConversionToken',
			ParameterName: '/monolith/partnerstack/conversion-token',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'GoogleAgentConfiguration__PrivateKey',
			ParameterName: '/monolith/google-agent/private-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'MicrosoftGraph__SecretKey',
			ParameterName: '/monolith/api/microsoft-graph-secret-key',
			Version: 0,
		},
		{
			EnvironmentVariableName: 'ClaimMdConfiguration__ApiKey',
			ParameterName: '/shared/claim-md/api-key',
			Version: 0,
		},
	],
	SecretsManagerEnvironmentVariables: [
		// not used as an environment variable, adding it here will generate IAM policy permissions
		{
			EnvironmentVariableName: 'Redis__ElastiCacheAuthToken',
			SecretName: 'ohio-elasticache-auth-secret',
			UniqueIdentifier: 'rDthx4',
		},
	],
	// Route53Configs: [
	// 	{
	// 		HostedZoneId: 'Z05235681QASGEL7C4X62',
	// 		HostedZoneName: 'events-ohio.carepatron.com',
	// 		ARecord: {
	// 			HostedZoneId: 'Z2FDTNDATAQYW2', // This is the hosted zone ID for all CloudFront distributions, it doesn't change
	// 			DnsName: 'd2a5r7q2kfc397.cloudfront.net', // todo
	// 		},
	// 	},
	// ],
	ServiceSecurityGroups: [
		{
			AllowAllOutbound: true,
			Description: 'Monolith Api Service Security Group',
			IngressRules: [
				{
					SecurityGroupExportName: 'monolith-ohio-us-east-2-alb-sg-id',
					Port: 80,
					Description: 'allow http traffic in from load balancer security group',
				},
				{
					Cidr: VPC.PRIVATE_SUBNETS_CIDR_OHIO[0],
					Port: 80,
					Description: 'allow http traffic in from private subnet 1',
				},
				{
					Cidr: VPC.PRIVATE_SUBNETS_CIDR_OHIO[1],
					Port: 80,
					Description: 'allow http traffic in from private subnet 2',
				},
			],
		},
	],
	LoadbalancerListenerRules: [
		{
			Priority: 11,
			Conditions: {
				HostHeaders: ['book.carepatron.com'],
				PathPatterns: ['/api/*'],
				HttpHeaders: [
					{
						HttpHeaderName: 'X-CloudFront-Origin',
						Values: ['/shared/cloudfront/x-cloudfront-origin'],
						FromSecretsManager: true,
					},
				],
			},
		},
		{
			Priority: 12,
			Conditions: {
				HostHeaders: ['app.carepatron.com'],
				PathPatterns: ['/api/*'],
				HttpHeaders: [
					{
						HttpHeaderName: 'X-CloudFront-Origin',
						Values: ['/shared/cloudfront/x-cloudfront-origin'],
						FromSecretsManager: true,
					},
				],
			},
		},
		{
			Priority: 13,
			Conditions: {
				HostHeaders: ['events-ohio.carepatron.com'],
				PathPatterns: ['/api/*'],
				HttpHeaders: [
					{
						HttpHeaderName: 'X-CloudFront-Origin',
						Values: ['/shared/cloudfront/x-cloudfront-origin'],
						FromSecretsManager: true,
					},
				],
			},
		},
	],
	TaskRole: {
		AssumedByPrincipal: {
			PrincipalType: IamPrincipalTypes.Service,
			Principal: IamPrincipals.ECSTaskRole,
		},
		ManagedPolicies: [
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonChimeSDK,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonCognitoPowerUser,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonEventBridgeFullAccess,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonS3FullAccess,
			},
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonSESFullAccess,
			},
		],
		InlinePolicies: [
			{
				PolicyName: 'SecretsManagerFullReadAccess',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'FullReadAccess',
							Actions: ['secretsmanager:GetSecretValue', 'secretsmanager:DescribeSecret'],
							Resources: ['arn:aws:secretsmanager:*:418502688622:secret:*'],
						},
					],
				},
			},
			{
				PolicyName: 'KMS',
				PolicyDocument: {
					Statements: [
						{
							Sid: 'ListAndDescribe',
							Actions: ['kms:DescribeKey', 'kms:ListAliases'],
							Resources: ['*'],
						},
					],
				},
			},
			// {
			// 	PolicyName: 'InvokePdfUtilsLambda',
			// 	PolicyDocument: {
			// 		Statements: [
			// 			{
			// 				Sid: 'InvokeLambda',
			// 				Actions: ['lambda:InvokeFunction'],
			// 				Resources: [
			// 					'arn:aws:lambda:ap-southeast-2:418502688622:function:carepatron-pdf-utils-function',
			// 				],
			// 			},
			// 		],
			// 	},
			// },
			// {
			// 	PolicyName: 'InvokeTemplateImportUtilsLambda',
			// 	PolicyDocument: {
			// 		Statements: [
			// 			{
			// 				Sid: 'InvokeLambda',
			// 				Actions: ['lambda:InvokeFunction'],
			// 				Resources: [
			// 					'arn:aws:lambda:ap-southeast-2:418502688622:function:carepatron-template-import-utils-function',
			// 				],
			// 			},
			// 		],
			// 	},
			// },
		],
	},
	TaskExecutionRole: {
		AssumedByPrincipal: {
			PrincipalType: IamPrincipalTypes.Service,
			Principal: IamPrincipals.ECSTaskRole,
		},
		ManagedPolicies: [
			{
				ManagedPolicyName: IamManagedPolicyNames.AmazonECSTaskExecutionRolePolicy,
			},
		],
	},
};
