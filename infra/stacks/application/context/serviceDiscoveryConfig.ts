import { productionConfig } from '../config/production/config.service-discovery';
import { stagingConfig } from '../config/staging/config.service-discovery';
import { ohioConfig } from '../config/ohio/config.service-discovery';
import { AWSRegions } from '../enums';
import { getConfig, getConfig_V2 } from './helpers/getConfig';

export type ServiceDiscoveryConfig = {
	VpcId: string;
	AWSRegion: AWSRegions;
	PrivateNamespaces: ServiceDiscoveryNamespaces[];
};

export type ServiceDiscoveryNamespaces = {
	Name: string;
	Description: string;
};

export function getServiceDiscoveryConfig(): ServiceDiscoveryConfig {
	return getConfig_V2({ staging: stagingConfig, production: productionConfig, ohio: ohioConfig });
}
