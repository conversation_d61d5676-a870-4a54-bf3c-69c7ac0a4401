#!/bin/bash
export AWS_PAGER=""
# ----------------------------------------------------
# This script is used to create the local setup
# for the template-import-utils-function in localstack.
# ----------------------------------------------------

# Delete existing function
aws --endpoint-url=http://localhost.localstack.cloud:4566 lambda delete-function \
--function-name carepatron-template-import-utils-function \
--region=ap-southeast-2

# Build function and zip
cd ../../infra/stacks/application/lib/template-import-utils/lambda
npm install
npm run build
cd dist
zip -r function.zip .
cd ../../../../../../../build/local
cp ../../infra/stacks/application/lib/template-import-utils/lambda/dist/function.zip files/template-import-utils.zip

# Create function using zip file
aws --endpoint-url=http://localhost.localstack.cloud:4566 \
--region=ap-southeast-2 lambda create-function \
--timeout 300 \
--function-name carepatron-template-import-utils-function \
--runtime nodejs20.x \
--role arn:aws:iam::000000000000:role/lambda-role \
--handler index.handler \
--zip-file fileb://files/template-import-utils.zip \
--environment Variables="{SourceBucket=carepatron-monolith-internal-blob-storage-bucket-local,DestinationBucket=carepatron-files,AWSRegion=ap-southeast-2,AWSAccessKeyId=test,AWSSecretAccessKey=test,AWSEndpoint=http://localhost.localstack.cloud:4566}" \
--tags '{"_custom_id_":"carepatron-template-import-utils-function"}'  

rm files/template-import-utils.zip
