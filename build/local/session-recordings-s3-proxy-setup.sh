#!/bin/bash
export AWS_PAGER=""

# ----------------------------------------------------
# This script sets up the
# carepatron-session-recordings-s3-proxy lambda
# in localstack.
# ----------------------------------------------------

FUNCTION_NAME="carepatron-session-recordings-s3-proxy"
REGION="ap-southeast-2"
ENDPOINT_URL="http://localhost.localstack.cloud:4566"

# Delete existing function
aws --endpoint-url=$ENDPOINT_URL lambda delete-function \
  --function-name $FUNCTION_NAME \
  --region=$REGION

# Build function and zip
cd ../../infra/stacks/application/lib/session-recordings/lambda/s3-proxy
npm install
npm run build
cd dist
zip -r function.zip .
pwd
cd ../../../../../../../../build/local
mkdir -p files
cp ../../infra/stacks/application/lib/session-recordings/lambda/s3-proxy/dist/function.zip files/s3-proxy.zip

aws --endpoint-url=$ENDPOINT_URL logs create-log-group --log-group-name ReplayerUserActivityLogGroup

# Create function using zip file
aws --endpoint-url=$ENDPOINT_URL \
  --region=$REGION lambda create-function \
  --function-name $FUNCTION_NAME \
  --runtime nodejs20.x \
  --role arn:aws:iam::000000000000:role/lambda-role \
  --handler index.handler \
  --zip-file fileb://files/s3-proxy.zip \
  --environment Variables="{BUCKET_NAME=carepatron-session-recordings-local,USER_ACTIVITY_LOG_GROUP_NAME=ReplayerUserActivityLogGroup,USER_ACTIVITY_LOG_STREAM_NAME=user-activity-stream,COGNITO_USER_POOL_ID=ap-southeast-2_9FMYu6pOj,COGNITO_CLIENT_ID=je63u0k3549a0pon6cuq0tsud}" \
  --tags '{"_custom_id_":"carepatron-session-recordings-s3-proxy"}'

# Cleanup
rm files/s3-proxy.zip

# Create function URL
aws --endpoint-url=$ENDPOINT_URL \
  --region=$REGION lambda create-function-url-config \
  --function-name $FUNCTION_NAME \
  --auth-type AWS_IAM
