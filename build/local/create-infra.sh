# Store current working directory.
# Ensure we are always in current working directory when running this script.
CWD=$(PWD)
export AWS_PAGER=""
## s3 bucket
echo "Creating local infrastructure"

echo "creating carepatron-files.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files --key 8aff2499-d369-4f7e-888d-f2241a436c47 --body files/8aff2499-d369-4f7e-888d-f2241a436c47.png
echo "..done"

echo "creating carepatron-files-public.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-public --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-public --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-public --key 8aff2499-d369-4f7e-888d-f2241a436c47 --body files/8aff2499-d369-4f7e-888d-f2241a436c47.png
echo "..done"

echo "creating carepatron-files-private.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-private --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-private --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-files-private --key 8aff2499-d369-4f7e-888d-f2241a436c47 --body files/8aff2499-d369-4f7e-888d-f2241a436c47.png
echo "..done"

echo "creating carepatron-transcription-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-transcription-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-transcription-bucket-local --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-transcription-bucket-local --key 8aff2499-d369-4f7e-888d-f2241a436c47 --body files/8aff2499-d369-4f7e-888d-f2241a436c47.png
echo "..done"

echo "creating carepatron-chime-media-capture-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-chime-media-capture-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-chime-media-capture-bucket-local --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-chime-media-capture-bucket-local --key 8aff2499-d369-4f7e-888d-f2241a436c47 --body files/8aff2499-d369-4f7e-888d-f2241a436c47.png
echo "..done"

echo "creating carepatron-client-import-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-import-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-import-bucket-local --cors-configuration file://cors.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-import-bucket-local --key 9f8b5a8e-3f73-45a7-a4a7-f7a3f0a561bc --body files/athenahealth-import.zip
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-import-bucket-local --key 3cb6bfcd-d4e5-4352-8943-28fca03d3245 --body files/simple-practice-import.zip
echo "..done"

echo "creating carepatron-client-export-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-export-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-client-export-bucket-local --cors-configuration file://cors.json
echo "..done"

echo "creating carepatron-data-export-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-data-export-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-data-export-bucket-local --cors-configuration file://cors.json
echo "..done"

echo "creating carepatron-monolith-internal-blob-storage-bucket-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --key workspace-defaults/default-services.json --body files/default-services.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --key workspace-defaults/default-templates.json --body files/default-templates.json
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --key insurance-forms/cms1500_0212.pdf --body ../../api/resources/insurance-forms/cms1500_0212.pdf
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --key insurance-forms/cms1500_0212_blank.pdf --body ../../api/resources/insurance-forms/cms1500_0212_blank.pdf
aws s3api put-object --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-monolith-internal-blob-storage-bucket-local --key security-deny-lists/email-domain-deny-list.json --body ../../security/deny-lists/email-domain-deny-list.json
echo "..done"

echo "creating carepatron-session-recordings-local.."
aws s3api create-bucket --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-session-recordings-local --create-bucket-configuration LocationConstraint=ap-southeast-2
aws s3api put-bucket-cors --region ap-southeast-2 --endpoint-url=http://localhost.localstack.cloud:4566 --bucket=carepatron-session-recordings-local --cors-configuration file://cors.json
echo "..done"

echo "creating dynamodb table for session recordings.."
aws dynamodb create-table --endpoint-url=http://localhost.localstack.cloud:4566 \
    --table-name SessionRecordingsTableLocal \
    --attribute-definitions AttributeName=recordingId,AttributeType=S \
    --key-schema AttributeName=recordingId,KeyType=HASH
echo "..done"

echo "creating identities.."
aws ses verify-email-identity --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --email-address <EMAIL>
echo "..done"

echo "creating queues.."
aws sqs create-queue --queue-name test_queue.fifo --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --attributes '{"FifoQueue": "True", "ContentBasedDeduplication":"true"}'
aws sqs create-queue --queue-name test_documentation_transcriptions_queue --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566
aws sqs create-queue --queue-name notification_queue.fifo --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --attributes '{"FifoQueue": "True", "ContentBasedDeduplication":"true"}'
aws sqs create-queue --queue-name test_claimmd-queue.fifo --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --attributes '{"FifoQueue": "True", "ContentBasedDeduplication":"true"}'

aws sqs purge-queue --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --queue-url https://sqs.ap-southeast-2.localhost.localstack.cloud:4566/000000000000/test_queue.fifo
aws sqs purge-queue --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --queue-url https://sqs.ap-southeast-2.localhost.localstack.cloud:4566/000000000000/test_documentation_transcriptions_queue
aws sqs purge-queue --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --queue-url https://sqs.ap-southeast-2.localhost.localstack.cloud:4566/000000000000/notification_queue.fifo
aws sqs purge-queue --region ap-southeast-2 --endpoint-url http://localhost.localstack.cloud:4566 --queue-url https://sqs.ap-southeast-2.localhost.localstack.cloud:4566/000000000000/test_claimmd-queue.fifo
echo "..done"

echo "creating event bus.."
aws --endpoint-url http://localhost.localstack.cloud:4566 --region ap-southeast-2 events delete-event-bus --name dev-carepatron-events 
aws --endpoint-url http://localhost.localstack.cloud:4566 --region ap-southeast-2 events create-event-bus --name dev-carepatron-events --query EventBusArn
echo "creating connection..."
EB_CONNECTION_ARN=$(aws --endpoint-url http://localhost.localstack.cloud:4566 --region ap-southeast-2 events create-connection --name dev-carepatron-api-connection --authorization-type API_KEY --auth-parameters '{"ApiKeyAuthParameters": {"ApiKeyName":"X-Api-Key", "ApiKeyValue":"localhost-super-secret-api-key-123"}}' --query ConnectionArn) 
echo ${EB_CONNECTION_ARN}

echo "creating destination..."
# The invocation endpoint here is deliberately slightly different from what's configured in AWS.
# - Uses HTTP rather than HTTPS due to untrusted self signed certs when localstack calls the api.
# - Uses `/api/eventhooks` rather than `/api/eventhooks/*` due localstack not replacing wildcard placeholders, and appending path parameters instead.
EB_DESTINATION_ARN=$(aws --endpoint-url http://localhost.localstack.cloud:4566 --region ap-southeast-2 events create-api-destination --name dev-carepatron-api-destination --connection-arn $EB_CONNECTION_ARN --http-method POST --invocation-endpoint "http://host.docker.internal:5000/api/eventHooks" --no-verify-ssl --query ApiDestinationArn) 
echo ${EB_DESTINATION_ARN//\"}
echo "... adding to dotnet user secrets"
dotnet user-secrets set "ApiDestinationConfiguration:ApiDestinationArn" ${EB_DESTINATION_ARN//\"} --project "../../api/carepatron.api/"
echo "..done"

echo "creating secrets... "
aws --endpoint-url=http://localhost.localstack.cloud:4566 --region=ap-southeast-2 secretsmanager delete-secret --secret-id dev-api-secret --force-delete-without-recovery
aws --endpoint-url=http://localhost.localstack.cloud:4566 --region=ap-southeast-2 secretsmanager create-secret --name dev-api-secret --secret-string "localhost-super-secret-api-key-123"
echo "..done"

echo "creating lambda functions.."
sh ./pdf-utils-setup.sh
sh ./template-import-utils-setup.sh
sh ./session-recordings-s3-proxy-setup.sh
cd $CWD
echo "..done"
