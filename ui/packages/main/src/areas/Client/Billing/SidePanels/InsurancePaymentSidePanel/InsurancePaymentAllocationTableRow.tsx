import React, { MouseEvent, useCallback, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';

import langIds from 'lang/langIds';
import { getClaimDetailsRoute } from 'util/routes';

import { AllocationTableRow } from '../common/AllocationTableRow';

import { IInsurancePaymentForm } from './hooks/useInsuranceForm';
import { InsuranceAllocationItem } from './InsurancePaymentAllocationTable';

interface IProps {
	contactId: string;
	totalUnallocatedAmount: number;
	currencyCode?: string;
	allocationItem: InsuranceAllocationItem;
	index: number;
	isEditing?: boolean;
	hasDateColumn?: boolean;
	onClose: () => void;
}

export const InsurancePaymentAllocationTableRow = ({
	contactId,
	totalUnallocatedAmount,
	currencyCode,
	allocationItem,
	index,
	isEditing,
	hasDateColumn,
	onClose,
}: IProps) => {
	const { $t } = useIntl();
	const navigate = useNavigate();

	const allocatedFieldName = `allocations.${index}.allocated` as const;
	const selectedFieldName = `allocations.${index}.selected` as const;

	const claimRoute = getClaimDetailsRoute(contactId, allocationItem.claim?.type, allocationItem.claim?.id);
	const claimNumberLabel = allocationItem.claim?.number
		? $t({ id: langIds.ClaimNumberFormat }, { number: allocationItem.claim.number })
		: '';

	const relatedAllocatedFieldNames = useMemo(
		() => allocationItem.relatedFieldIndexes.map((i) => `allocations.${i}.allocated` as const),
		[allocationItem.relatedFieldIndexes]
	);

	const {
		setValue,
		control,
		trigger,
		formState: { errors, isSubmitted },
	} = useFormContext<IInsurancePaymentForm & { totalAmount: number }>();

	const relatedAllocatedFields = useWatch({ control, name: relatedAllocatedFieldNames });
	const allocated = useWatch({ control, name: allocatedFieldName, defaultValue: 0 });
	const isSelected = useWatch({ control, name: selectedFieldName, defaultValue: false });

	const errorMessage = errors?.allocations?.[index]?.allocated?.message;

	const paidAmount = useMemo(() => {
		if (!isEditing) return allocationItem.paid;

		const totalAllocated = relatedAllocatedFields.reduce((sum, fieldValue) => sum + (fieldValue ?? 0), allocated);

		return allocationItem.paid - allocationItem.totalAdjustment + totalAllocated;
	}, [isEditing, allocationItem.paid, allocationItem.totalAdjustment, relatedAllocatedFields, allocated]);

	const handleOnRowClicked = useCallback(() => {
		const amountToAllocate =
			isSelected || totalUnallocatedAmount <= 0
				? 0
				: Math.min(totalUnallocatedAmount, allocationItem.amount - paidAmount);

		setValue(selectedFieldName, !isSelected);
		setValue(allocatedFieldName, isSelected ? 0 : amountToAllocate);

		// Manually trigger validation of all related fields
		if (isSubmitted) {
			trigger([allocatedFieldName, 'totalAmount', 'amount', ...relatedAllocatedFieldNames]);
		}
	}, [
		isSelected,
		totalUnallocatedAmount,
		allocationItem.amount,
		paidAmount,
		setValue,
		selectedFieldName,
		allocatedFieldName,
		isSubmitted,
		trigger,
		relatedAllocatedFieldNames,
	]);

	const handleOnAmountChanged = useCallback(
		(value: number) => {
			setValue(allocatedFieldName, value);
			// Manually trigger validation of all related fields
			if (isSubmitted) {
				trigger([allocatedFieldName, ...relatedAllocatedFieldNames]);
			}
		},
		[allocatedFieldName, isSubmitted, setValue, trigger, relatedAllocatedFieldNames]
	);

	const handleOnClick = useCallback(
		(e: MouseEvent<HTMLAnchorElement>) => {
			if (!claimRoute) return;
			e.preventDefault();
			navigate(claimRoute);
			onClose();
		},
		[claimRoute, navigate, onClose]
	);

	return (
		<AllocationTableRow
			isEditing={isEditing}
			errorMessage={errorMessage}
			isSelected={isSelected}
			hasDateColumn={!!hasDateColumn}
			allocation={{ ...allocationItem, allocated }}
			currencyCode={currencyCode}
			allocatedFormValue={allocated}
			paidAmount={paidAmount}
			itemNumber={claimNumberLabel}
			{...(!!claimRoute && { itemNumberProps: { href: claimRoute, onClick: handleOnClick } })}
			onRowClicked={handleOnRowClicked}
			onAmountChanged={handleOnAmountChanged}
		/>
	);
};
