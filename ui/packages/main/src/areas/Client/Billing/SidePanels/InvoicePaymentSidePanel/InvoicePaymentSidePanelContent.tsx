import React, { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { useSelector } from 'react-redux';
import { Skeleton, Stack, Typography } from '@mui/material';

import * as SidePanel from '@carepatron/components/src/SidePanel';

import langIds from 'lang/langIds';
import { selectCurrencyCode } from 'store/api/billingSettings/selectors';
import { localizeDate } from 'util/date';
import { getPaymentTypeLabel } from 'util/invoice';
import useFormatPrice from 'util/useFormatPrice';

import { calculateTotalUnallocated, getIsPaymentUnallocated } from '../../BillingAndPayments/Payments/utils';
import { AdjustmentsTable } from '../common/AdjustmentsTable';
import { AllocationTotalWarningBanner } from '../common/AllocationTotalWarningBanner';
import { UnallocatedWarningBanner } from '../common/UnallocatedWarningBanner';

import { InvoicePaymentAllocationTable } from './InvoicePaymentAllocationTable';
import { IPaymentAllocationForm } from './types';

interface IProps {
	isEditing: boolean;
	unallocatedItems: IAllocationItem[];
	paymentDetails?: IPaymentDetails;
	isFetching?: boolean;
	onClose: () => void;
}

export const PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS = {
	refundTableRoot: 'payment-side-panel-content-refund-table-root',
	refundTableRow: 'payment-side-panel-content-refund-table-row',
	otherAdjustmentTableRoot: 'payment-side-panel-content-other-adjustment-table-root',
	otherAdjustmentTableRow: 'payment-side-panel-content-other-adjustment-table-row',
	allocationAmount: 'payment-side-panel-content-allocation-amount',
};

export const InvoicePaymentSidePanelContent = ({
	isEditing,
	paymentDetails,
	isFetching,
	unallocatedItems,
	onClose,
}: IProps) => {
	const formatPrice = useFormatPrice();
	const workspaceCurrencyCode = useSelector(selectCurrencyCode);
	const currencyCode = paymentDetails?.currencyCode || workspaceCurrencyCode;

	const {
		control,
		formState: { errors },
	} = useFormContext<IPaymentAllocationForm>();
	const allocatedFormValues = useWatch({ control, name: 'allocations' });

	const totalUnallocated = useMemo(() => {
		const unallocatedAmount = calculateTotalUnallocated(
			allocatedFormValues,
			paymentDetails?.availableToAllocate ?? 0
		);

		return isEditing ? unallocatedAmount : (paymentDetails?.unallocated ?? 0);
	}, [allocatedFormValues, isEditing, paymentDetails?.unallocated, paymentDetails?.availableToAllocate]);

	const isUnallocated = useMemo(() => {
		const totalAllocated = allocatedFormValues.reduce((acc, item) => {
			return acc + (item.selected ? item.allocated : 0);
		}, 0);
		const availableToAllocate = paymentDetails?.availableToAllocate ?? 0;

		const totalUnallocated = availableToAllocate - totalAllocated;

		return getIsPaymentUnallocated(availableToAllocate, totalUnallocated);
	}, [allocatedFormValues, paymentDetails?.availableToAllocate]);

	return (
		<SidePanel.Content>
			<Stack spacing={3}>
				<Stack spacing={1.25}>
					<SidePanel.ContentSubHeader>
						<FormattedMessage id={langIds.Details} />
					</SidePanel.ContentSubHeader>

					<Stack direction='row' spacing={1.25}>
						<Stack flex={1}>
							<Typography variant='body2' color='text.secondary'>
								<FormattedMessage id={langIds.Date} />
							</Typography>
							{isFetching ? (
								<Skeleton width='60px' />
							) : (
								<Typography variant='body2'>
									{!!paymentDetails?.paymentDate && localizeDate(paymentDetails?.paymentDate)}
								</Typography>
							)}
						</Stack>
						<Stack flex={1}>
							<Typography variant='body2' color='text.secondary'>
								<FormattedMessage id={langIds.Method} />
							</Typography>
							<Typography variant='body2'>
								{isFetching ? (
									<Skeleton width='60px' />
								) : (
									getPaymentTypeLabel(paymentDetails?.paymentMethod)
								)}
							</Typography>
						</Stack>
						<Stack flex={1}>
							<Typography variant='body2' color='text.secondary'>
								<FormattedMessage id={langIds.Amount} />
							</Typography>
							<Typography variant='body2'>
								{isFetching ? (
									<Skeleton width='60px' />
								) : (
									formatPrice(paymentDetails?.chargeAmount || 0, currencyCode)
								)}
							</Typography>
						</Stack>
						<Stack flex={1}>
							<Typography variant='body2' color='text.secondary'>
								<FormattedMessage id={langIds.Unallocated} />
							</Typography>
							<Typography
								variant='body2'
								data-testid={PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS.allocationAmount}
							>
								{isFetching ? <Skeleton width='60px' /> : formatPrice(totalUnallocated, currencyCode)}
							</Typography>
						</Stack>
					</Stack>
				</Stack>

				{!isEditing && paymentDetails && totalUnallocated !== 0 && !isFetching && (
					<UnallocatedWarningBanner isUnallocated={isUnallocated} />
				)}

				{isEditing && <AllocationTotalWarningBanner shouldRenderBanner={!!errors.totalAmount?.message} />}

				<InvoicePaymentAllocationTable
					isEditing={isEditing}
					isFetching={isFetching}
					currencyCode={currencyCode}
					allocations={paymentDetails?.allocations}
					totalUnallocated={totalUnallocated}
					unallocatedItems={unallocatedItems}
					isCreditNote={(paymentDetails?.availableToAllocate ?? 0) < 0}
					onClose={onClose}
				/>

				<AdjustmentsTable
					titleId={langIds.OtherAdjustments}
					emptyStateId={langIds.OtherAdjustmentsTableEmptyState}
					currencyCode={currencyCode}
					isFetching={isFetching}
					credits={paymentDetails?.issuedCredits}
					tableContainerProps={{
						'data-testid': PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS.otherAdjustmentTableRoot,
					}}
					itemTableRowProps={{
						'data-testid': PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS.otherAdjustmentTableRow,
					}}
				/>

				<AdjustmentsTable
					titleId={langIds.Refunds}
					emptyStateId={langIds.RefundsTableEmptyState}
					currencyCode={currencyCode}
					isFetching={isFetching}
					refunds={paymentDetails?.refunds}
					tableContainerProps={{ 'data-testid': PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS.refundTableRoot }}
					itemTableRowProps={{ 'data-testid': PAYMENTS_SIDE_PANEL_CONTENT_DATA_TEST_IDS.refundTableRow }}
				/>
			</Stack>
		</SidePanel.Content>
	);
};
