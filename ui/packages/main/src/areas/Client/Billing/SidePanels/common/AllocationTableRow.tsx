import { Box, Checkbox, Link, LinkProps, Stack, TableBody, TableCell, TableRow, Typography } from '@mui/material';

import DecimalTextField from 'components/common/DecimalTextField';
import { getCurrencySymbol } from 'util/currencies';
import { localizeDate } from 'util/date';
import useFormatPrice from 'util/useFormatPrice';

export type AllocationTableRowItem = Pick<
	IPaymentAllocation,
	'date' | 'paid' | 'allocated' | 'description' | 'amount' | 'invoice'
>;

interface IAllocationTableRowProps {
	allocation: AllocationTableRowItem;
	currencyCode?: string;
	itemNumber: string;
	itemNumberProps?: LinkProps;
	errorMessage?: string;
	allocatedFormValue: number;
	paidAmount: number;
	isEditing?: boolean;
	isSelected?: boolean;
	hasDateColumn: boolean;
	onRowClicked?: () => void;
	onAmountChanged?: (value: number) => void;
}

export const ALLOCATION_TABLE_ROW_DATA_TEST_IDS = {
	root: 'allocation-table-row-root',
	allocatedCell: 'allocation-table-row-allocated-cell',
	paidCell: 'allocation-table-row-paid-cell',
};

export const AllocationTableRow = ({
	allocation,
	currencyCode,
	itemNumber,
	itemNumberProps,
	allocatedFormValue,
	paidAmount,
	errorMessage,
	isEditing,
	isSelected,
	hasDateColumn,
	onRowClicked,
	onAmountChanged,
}: IAllocationTableRowProps) => {
	const formatPrice = useFormatPrice();

	const getAllocatedItemsColSpan = () => {
		let colSpan = 1;

		if (!hasDateColumn) colSpan++;
		if (!isEditing) colSpan++;

		return colSpan;
	};

	return (
		<TableBody
			sx={{
				'&:hover': {
					backgroundColor: 'action.hover',
					cursor: isEditing ? 'pointer' : 'auto',
				},
			}}
			data-testid={ALLOCATION_TABLE_ROW_DATA_TEST_IDS.root}
			{...(isEditing && { onClick: onRowClicked })}
		>
			<TableRow
				sx={{
					'& .MuiTableCell-root': {
						pt: 2,
						pb: errorMessage ? 0 : 2,
						borderBottom: errorMessage ? 'none' : 'auto',
					},
				}}
			>
				{isEditing && (
					<TableCell sx={{ width: '0.1%', verticalAlign: 'top', '&.MuiTableCell-root': { pl: 2, pr: 1 } }}>
						<Checkbox sx={{ p: 0, mt: '-3px' }} checked={isSelected} />
					</TableCell>
				)}

				{hasDateColumn && (
					<TableCell
						sx={{
							width: '0.1%',
							verticalAlign: 'top',
							'&.MuiTableCell-root': { pr: 2, pl: isEditing ? 0 : 2 },
						}}
					>
						{!!allocation.date && <Box whiteSpace='nowrap'>{localizeDate(allocation.date, 'noYear')}</Box>}
					</TableCell>
				)}
				<TableCell
					colSpan={getAllocatedItemsColSpan()}
					sx={{
						verticalAlign: 'top',
						'&.MuiTableCell-root': { pr: 2, pl: isEditing && !hasDateColumn ? 0 : 2 },
					}}
				>
					<Stack>
						<Typography variant='body2'>{allocation.description}</Typography>
						<Link
							variant='subtitle2'
							{...itemNumberProps}
							sx={{
								width: 'fit-content',
								cursor: itemNumberProps?.onClick || itemNumberProps?.href ? 'pointer' : 'auto',
								...itemNumberProps?.sx,
							}}
							onClick={(e) => {
								e.stopPropagation();
								itemNumberProps?.onClick?.(e);
							}}
						>
							{itemNumber}
						</Link>
					</Stack>
				</TableCell>

				<TableCell
					sx={{
						textAlign: 'center',
						verticalAlign: 'top',
						whiteSpace: 'nowrap',
						'&.MuiTableCell-root': { px: 2 },
					}}
				>
					{formatPrice(allocation.amount, currencyCode)}
				</TableCell>

				<TableCell
					sx={{
						textAlign: 'center',
						verticalAlign: 'top',
						whiteSpace: 'nowrap',
						'&.MuiTableCell-root': { px: 2 },
					}}
					data-testid={ALLOCATION_TABLE_ROW_DATA_TEST_IDS.paidCell}
				>
					{formatPrice(paidAmount, currencyCode)}
				</TableCell>

				<TableCell
					sx={{
						textAlign: 'center',
						verticalAlign: 'top',
						whiteSpace: 'nowrap',
						'&.MuiTableCell-root': { px: 2 },
					}}
					data-testid={ALLOCATION_TABLE_ROW_DATA_TEST_IDS.allocatedCell}
				>
					{isEditing ? (
						<DecimalTextField
							value={isSelected ? allocatedFormValue : 0}
							variant='standard'
							disabled={!isSelected}
							className='standard-shaded'
							error={!!errorMessage}
							sx={{
								'&.standard-shaded': {
									border: 1,
									borderColor: errorMessage ? 'error.main' : 'border',
									backgroundColor: 'transparent',
								},
							}}
							InputProps={{ disableUnderline: true }}
							onClick={(e) => e.stopPropagation()}
							onChange={onAmountChanged}
							{...(!!currencyCode && { prefix: getCurrencySymbol(currencyCode) })}
						/>
					) : (
						formatPrice(allocation.allocated, currencyCode)
					)}
				</TableCell>
			</TableRow>
			{!!errorMessage && (
				<TableRow sx={{ '& .MuiTableCell-root': { pt: 0, px: 2 } }}>
					<TableCell colSpan={6} align='right'>
						<Typography textAlign='right' variant='helper' color='error'>
							{errorMessage}
						</Typography>
					</TableCell>
				</TableRow>
			)}
		</TableBody>
	);
};
