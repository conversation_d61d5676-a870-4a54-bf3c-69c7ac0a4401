import { useCallback, useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';
import { useIntl } from 'react-intl';

import langIds from 'lang/langIds';
import { useAppDispatch } from 'store';
import { openInvoice } from 'store/api/invoices';
import { roundNumberUsingCurrencyCode } from 'util/currencies';

import { AllocationSkeletonTableRow } from '../common/AllocationSkeletonTableRow';
import { AllocationTableContainer } from '../common/AllocationTableContainer';
import { AllocationTableEmptyState } from '../common/AllocationTableEmptyState';
import { AllocationTableHeader } from '../common/AllocationTableHeader';
import { AllocationTableHeaderCell } from '../common/AllocationTableHeaderCell';
import { AllocationTableRow, AllocationTableRowItem } from '../common/AllocationTableRow';

import { IPaymentAllocationForm } from './types';

interface IAllocationTableProps {
	totalUnallocated: number;
	isEditing: boolean;
	isFetching?: boolean;
	isCreditNote?: boolean;
	currencyCode: string;
	allocations?: IPaymentAllocation[];
	unallocatedItems?: IAllocationItem[];
	onClose: () => void;
}

interface IPaymentAllocationTableRowProps {
	index: number;
	totalUnallocated: number;
	currencyCode: string;
	isEditing?: boolean;
	isCreditNote?: boolean;
	hasDateColumn?: boolean;
	allocation: AllocationTableRowItem;
	onClose: () => void;
}

export const PaymentAllocationTableRow = ({
	index,
	totalUnallocated,
	isEditing,
	allocation,
	isCreditNote,
	hasDateColumn,
	currencyCode,
	onClose,
}: IPaymentAllocationTableRowProps) => {
	const { $t } = useIntl();
	const dispatch = useAppDispatch();

	const selectedFieldName = `allocations.${index}.selected` as const;
	const allocatedFieldName = `allocations.${index}.allocated` as const;
	const invoiceNumberLabel = allocation.invoice?.invoiceNumber
		? $t({ id: langIds.InvoiceNumberFormat }, { number: allocation.invoice.invoiceNumber })
		: '';

	const {
		control,
		setValue,
		formState: { errors, isSubmitted },
		trigger,
	} = useFormContext<IPaymentAllocationForm>();
	const isSelected = useWatch({ control, name: selectedFieldName });
	const allocated = useWatch({ control, name: allocatedFieldName });

	const errorMessage = errors?.allocations?.[index]?.allocated?.message;

	const paidAmount = useMemo(() => {
		if (!isEditing) return allocation.paid;
		return allocation.paid - allocation.allocated + allocated;
	}, [isEditing, allocated, allocation.paid, allocation.allocated]);

	const handleOnRowClicked = useCallback(() => {
		if (!isEditing) return;

		const updatedValue = !isSelected;
		setValue(selectedFieldName, updatedValue);

		let amountToAllocate = 0;
		if (updatedValue) {
			// This prevents the total from being over-allocated
			// negative values can still make it further unallocated
			amountToAllocate = isCreditNote
				? Math.max(totalUnallocated < 0 ? totalUnallocated : 0, allocation.amount - paidAmount)
				: Math.min(totalUnallocated > 0 ? totalUnallocated : 0, allocation.amount - paidAmount);
		}

		setValue(allocatedFieldName, roundNumberUsingCurrencyCode(amountToAllocate, currencyCode));

		if (isSubmitted) {
			trigger([allocatedFieldName, 'totalAmount']);
		}
	}, [
		allocatedFieldName,
		allocation.amount,
		currencyCode,
		isCreditNote,
		isEditing,
		isSelected,
		isSubmitted,
		paidAmount,
		selectedFieldName,
		setValue,
		totalUnallocated,
		trigger,
	]);

	const handleOnAmountChanged = useCallback(
		(value: number) => {
			if (isSelected) {
				setValue(allocatedFieldName, value);
				if (isSubmitted) {
					trigger([allocatedFieldName, 'totalAmount']);
				}
			}
		},
		[allocatedFieldName, isSelected, isSubmitted, setValue, trigger]
	);

	const handleOpenInvoice = useCallback(() => {
		if (!allocation.invoice?.invoiceId) return;
		dispatch(openInvoice({ invoiceId: allocation.invoice.invoiceId, isAddPaymentOpen: true }));
		onClose();
	}, [allocation.invoice?.invoiceId, dispatch, onClose]);

	return (
		<AllocationTableRow
			allocation={allocation}
			currencyCode={currencyCode}
			errorMessage={errorMessage}
			allocatedFormValue={allocated}
			paidAmount={paidAmount}
			isEditing={isEditing}
			isSelected={isSelected}
			hasDateColumn={!!hasDateColumn}
			onRowClicked={handleOnRowClicked}
			onAmountChanged={handleOnAmountChanged}
			itemNumber={invoiceNumberLabel}
			itemNumberProps={{
				onClick: handleOpenInvoice,
				sx: { pointerEvents: allocation.invoice?.invoiceId ? 'auto' : 'none' },
			}}
		/>
	);
};

export const InvoicePaymentAllocationTable = ({
	totalUnallocated,
	isFetching,
	isEditing,
	currencyCode,
	isCreditNote,
	allocations = [],
	unallocatedItems = [],
	onClose,
}: IAllocationTableProps) => {
	const hasDateColumn = useMemo(() => allocations.some((allocation) => !!allocation.date), [allocations]);
	const allocationItems = useMemo(() => {
		if (!isEditing) return allocations;

		return [...allocations, ...unallocatedItems.map((item) => ({ ...item, allocated: 0 }))];
	}, [allocations, isEditing, unallocatedItems]);

	const renderTableContent = () => {
		if (isFetching) {
			return [...Array(3)].map((_, index) => <AllocationSkeletonTableRow key={index} />);
		}

		if (!allocationItems.length) {
			return <AllocationTableEmptyState messageId={langIds.AllocationTableEmptyState} colSpan={6} />;
		}

		return allocationItems.map((allocation, index) => (
			<PaymentAllocationTableRow
				key={allocation.invoiceLineItemId}
				index={index}
				allocation={allocation}
				hasDateColumn={hasDateColumn}
				currencyCode={currencyCode}
				isEditing={isEditing}
				totalUnallocated={totalUnallocated}
				isCreditNote={isCreditNote}
				onClose={onClose}
			/>
		));
	};

	return (
		<AllocationTableContainer>
			<AllocationTableHeader>
				<AllocationTableHeaderCell colSpan={3} labelId={langIds.AllocatedItems} />
				<AllocationTableHeaderCell labelId={langIds.Amount} />
				<AllocationTableHeaderCell labelId={langIds.Paid} />
				<AllocationTableHeaderCell labelId={langIds.Allocated} isDividerHidden />
			</AllocationTableHeader>
			{renderTableContent()}
		</AllocationTableContainer>
	);
};
