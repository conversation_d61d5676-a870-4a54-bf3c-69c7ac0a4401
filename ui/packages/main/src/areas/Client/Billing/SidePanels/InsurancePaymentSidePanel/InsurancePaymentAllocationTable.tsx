import React, { useMemo } from 'react';
import { useFormContext, useWatch } from 'react-hook-form';

import langIds from 'lang/langIds';

import { AllocationSkeletonTableRow } from '../common/AllocationSkeletonTableRow';
import { AllocationTableContainer } from '../common/AllocationTableContainer';
import { AllocationTableEmptyState } from '../common/AllocationTableEmptyState';
import { AllocationTableHeader } from '../common/AllocationTableHeader';
import { AllocationTableHeaderCell } from '../common/AllocationTableHeaderCell';

import { IInsurancePaymentForm } from './hooks/useInsuranceForm';
import { InsurancePaymentAllocationTableRow } from './InsurancePaymentAllocationTableRow';

interface IProps {
	contactId: string;
	currencyCode?: string;
	paymentAllocations?: IPaymentAllocation[];
	unallocatedItems: IAllocationItem[];
	isFormVisible?: boolean;
	isFetching?: boolean;
	onClose: () => void;
}

export type InsuranceAllocationItem = (IAllocationItem | IPaymentAllocation) & {
	relatedFieldIndexes: number[];
	totalAdjustment: number;
};

export const InsurancePaymentAllocationTable = ({
	currencyCode,
	paymentAllocations,
	unallocatedItems,
	isFormVisible,
	isFetching,
	contactId,
	onClose,
}: IProps) => {
	const { control } = useFormContext<IInsurancePaymentForm>();
	const allocations = useWatch({ control, name: 'allocations' });
	const amount = useWatch({ control, name: 'amount', defaultValue: 0 });

	const allocatedItems: InsuranceAllocationItem[] = useMemo(() => {
		const payments = paymentAllocations ?? [];
		const items: (IAllocationItem | IPaymentAllocation)[] = isFormVisible
			? [...payments, ...unallocatedItems]
			: payments;

		return items.map((item) => {
			// Service lines can show up multiple times in the list but with different claimLineIds
			const relatedFields = items
				.map((filteredItem, index) => ({ ...filteredItem, index }))
				.filter(
					(filteredItem) =>
						filteredItem.billableItemId === item.billableItemId &&
						filteredItem.claimLineId !== item.claimLineId
				);
			const totalAdjustment = relatedFields.reduce(
				(acc, relatedItem) => acc + ('allocated' in relatedItem ? relatedItem.allocated : 0),
				'allocated' in item ? item.allocated : 0
			);
			const relatedFieldIndexes = relatedFields.map(({ index }) => index);

			return { ...item, relatedFieldIndexes, totalAdjustment };
		});
	}, [isFormVisible, paymentAllocations, unallocatedItems]);

	const hasDateColumn = useMemo(() => allocatedItems.some(({ date }) => date), [allocatedItems]);
	const totalUnallocatedAmount = useMemo(
		() => amount - allocations.reduce((acc, { selected, allocated }) => acc + (selected ? allocated : 0), 0),
		[allocations, amount]
	);

	const renderTableRow = () => {
		if (isFetching) {
			return [...Array(3)].map((_, index) => <AllocationSkeletonTableRow key={index} />);
		}

		if (!allocatedItems.length) {
			return <AllocationTableEmptyState messageId={langIds.AllocationTableEmptyState} colSpan={6} />;
		}

		return allocatedItems.map((allocation, index) => (
			<InsurancePaymentAllocationTableRow
				key={allocation.claimLineId ?? allocation.billableItemId}
				index={index}
				allocationItem={allocation}
				hasDateColumn={hasDateColumn}
				isEditing={isFormVisible}
				totalUnallocatedAmount={totalUnallocatedAmount}
				currencyCode={currencyCode}
				onClose={onClose}
				contactId={contactId}
			/>
		));
	};

	return (
		<AllocationTableContainer>
			<AllocationTableHeader>
				<AllocationTableHeaderCell labelId={langIds.AllocatedItems} colSpan={3} />
				<AllocationTableHeaderCell labelId={langIds.InsuranceAmount} />
				<AllocationTableHeaderCell labelId={langIds.InsurancePaid} />
				<AllocationTableHeaderCell labelId={langIds.Allocated} isDividerHidden />
			</AllocationTableHeader>

			{renderTableRow()}
		</AllocationTableContainer>
	);
};
