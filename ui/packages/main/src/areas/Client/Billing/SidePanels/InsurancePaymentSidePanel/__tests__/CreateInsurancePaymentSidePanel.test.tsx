import { paymentHandler, render, setupServerHandlers } from 'tests';
import { AllocationBuilder } from 'tests/api-mocks/builders/entities/AllocationBuilder';
import { insuranceClaimsHandlers } from 'tests/api-mocks/handlers/insuranceClaims';

import { useSidePanel } from 'components/sidePanels/useSidePanel';

import { CreateInsurancePaymentSidePanel } from '../CreateInsurancePaymentSidePanel';

jest.mock('components/sidePanels/useSidePanel', () => ({
	useSidePanel: jest.fn(),
}));

const serverHandlers = setupServerHandlers(
	paymentHandler.getUnallocatedPaymentAllocations.handler(),
	insuranceClaimsHandlers.getInsuranceClaim.handler(),
	insuranceClaimsHandlers.getContactInsuranceClaims.handler()
);
serverHandlers.run();

const allocationBuilder = new AllocationBuilder();

const renderInsurancePaymentSidePanel = () => {
	return render(<CreateInsurancePaymentSidePanel contactId=':contactId' origin='claim form' />, {
		enableRouter: true,
	});
};

describe('CreateInsurancePaymentSidePanel', () => {
	beforeEach(() => {
		(useSidePanel as jest.Mock).mockReturnValue({
			isOpen: true,
			closeSidePanel: jest.fn(),
		});
	});

	it('should render the side panel with the correct initial state', async () => {
		const billableItemId = 'billable-item-id';
		const allocations = [
			allocationBuilder.withClaim().build(),
			allocationBuilder.withClaim().withBillableItemId(billableItemId).build(),
			allocationBuilder.build(),
			allocationBuilder.withClaim().withBillableItemId(billableItemId).build(),
		];
		serverHandlers.server.use(
			paymentHandler.getUnallocatedPaymentAllocations.handler(async (_, res, ctx) =>
				res(ctx.status(200), ctx.json(allocations))
			)
		);

		const { findAllByTestId } = renderInsurancePaymentSidePanel();

		const allocationRows = await findAllByTestId('allocation-table-row-root');
		expect(allocationRows).toHaveLength(4);

		allocationRows.forEach((row, index) => {
			expect(row).toHaveTextContent(allocations[index].description);
			expect(row).toHaveTextContent(allocations[index].amount.toString());
			if (allocations[index].claim) {
				expect(row).toHaveTextContent(allocations[index].claim!.number);
			}
		});
	});
});
