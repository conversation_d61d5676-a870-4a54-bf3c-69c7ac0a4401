import { AppStateBuilder, render } from 'tests';
import { PaymentDetailsBuilder } from 'tests/api-mocks/builders/entities/PaymentDetailsBuilder';

import { intl } from 'lang';
import langIds from 'lang/langIds';

import {
	AllocationSidePanelFooter,
	PAYMENT_SIDE_PANEL_FOOTER_DATA_TEST_IDS,
} from '../common/AllocationSidePanelFooter';

const paymentDetailsBuilder = new PaymentDetailsBuilder();
const paymentDetails = paymentDetailsBuilder.build();
const preloadedStateBuilder = new AppStateBuilder();
const preloadedState = preloadedStateBuilder.withFeatureFlags(['payments-and-billing']).build();

const renderAllocationSidePanelFooter = (paymentDetails: IPaymentDetails, isInsurance: boolean) => {
	return render(
		<AllocationSidePanelFooter
			contactId=':contactId'
			isEditing={false}
			isFetching={false}
			isSubmitting={false}
			isEditDisabled={false}
			isInsurance={isInsurance}
			onCancel={jest.fn}
			onClose={jest.fn}
			onEdit={jest.fn}
			paymentDetails={paymentDetails}
		/>,
		{ preloadedState }
	);
};

describe('AllocationSidePanelFooter', () => {
	it.each([
		{
			paymentDetails: {
				...paymentDetails,
				refundStatus: undefined,
				chargeAmount: 20,
				refunds: [],
				canDelete: false,
			},
			isInsurance: false,
			expectedAction: intl.formatMessage({ id: langIds.Refund }),
		},
		{
			paymentDetails: {
				...paymentDetails,
				refundStatus: undefined,
				chargeAmount: 20,
				refunds: [],
				canDelete: false,
			},
			isInsurance: true,
			expectedAction: null,
		},
		{
			paymentDetails: {
				...paymentDetails,
				refundStatus: 'Failed',
				canDelete: true,
			},
			isInsurance: false,
			expectedAction: intl.formatMessage({ id: langIds.Delete }),
		},
		{
			paymentDetails: {
				...paymentDetails,
				refundStatus: undefined,
				chargeAmount: 20,
				refunds: [],
				canDelete: true,
			},
			isInsurance: true,
			expectedAction: intl.formatMessage({ id: langIds.Delete }),
		},
	])(
		'should render the footer with the correct menu actions',
		async ({ paymentDetails, isInsurance, expectedAction }) => {
			const { findByRole, findByTestId, queryByTestId, user } = renderAllocationSidePanelFooter(
				paymentDetails,
				isInsurance
			);

			if (!expectedAction) {
				const moreActionsButton = queryByTestId(PAYMENT_SIDE_PANEL_FOOTER_DATA_TEST_IDS.overflowMenu);

				expect(moreActionsButton).not.toBeInTheDocument();
			} else {
				const moreActionsButton = await findByTestId(PAYMENT_SIDE_PANEL_FOOTER_DATA_TEST_IDS.overflowMenu);

				await user.click(moreActionsButton);

				const action = await findByRole('menuitem', { name: expectedAction });

				expect(action).toBeInTheDocument();
			}
		}
	);
});
