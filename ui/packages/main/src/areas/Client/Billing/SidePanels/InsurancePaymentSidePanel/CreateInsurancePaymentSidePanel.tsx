import { useCallback, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import MonetizationOnRoundedIcon from '@mui/icons-material/MonetizationOnRounded';
import { Button, Stack, Typography } from '@mui/material';

import { SidePanel } from '@carepatron/components';

import { useSidePanel } from 'components/sidePanels/useSidePanel';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { selectCurrencyCode } from 'store/api/billingSettings/selectors';
import { useGetContactInsuranceClaimsQuery, useGetInsuranceClaimQuery } from 'store/api/insuranceClaims';
import { useCreateClaimPaymentMutation, useGetUnallocatedPaymentAllocationsQuery } from 'store/api/payments';
import { AddClaimPaymentOrigins, useInsuranceAnalytics } from 'util/analytics/useInsuranceAnalytics';

import { IconContainer } from '../../BillingAndPayments/common/IconContainer';

import { IInsurancePaymentForm, useInsuranceForm } from './hooks/useInsuranceForm';
import { InsurancePaymentAllocationTable } from './InsurancePaymentAllocationTable';
import { InsurancePaymentDetailFields } from './InsurancePaymentDetailFields';
import { OverallocatedWarningBanner } from './OverallocatedWarningBanner';

export interface ICreateInsurancePaymentSidePanelProps {
	contactId: string;
	claimId?: string;
	defaultServiceLines?: IClaimServiceLine[];
	defaultPayer?: IInsuranceClaimPayer;
	hideClaimField?: boolean;
	origin: AddClaimPaymentOrigins;
}

export const CreateInsurancePaymentSidePanel = ({
	contactId,
	claimId,
	defaultPayer,
	hideClaimField,
	origin,
}: ICreateInsurancePaymentSidePanelProps) => {
	const dispatch = useDispatch();
	const { isOpen, closeSidePanel } = useSidePanel('CreateInsurancePaymentSidePanel');
	const currencyCode = useSelector(selectCurrencyCode);
	const [claimsSearchTerm, setClaimsSearchTerm] = useState<string | undefined>();
	const [selectedClaimId, setSelectedClaimId] = useState<string | undefined>(claimId);
	const { trackAddInsurancePayment } = useInsuranceAnalytics();
	const { data: claimDetail, isLoading: isLoadingClaim } = useGetInsuranceClaimQuery(
		{ id: selectedClaimId },
		{ skip: !selectedClaimId }
	);

	const payer = defaultPayer ?? claimDetail?.payer;
	const { unallocatedItems, isFetching: isFetchingUnallocatedItems } = useGetUnallocatedPaymentAllocationsQuery(
		{ contactId, query: { payer: 'insurance', currencyCode } },
		{
			selectFromResult: (result) => ({
				...result,
				unallocatedItems:
					[...(result.data ?? [])].sort((a, b) => {
						if (a.claimLineId && !b.claimLineId) return -1;
						if (!a.claimLineId && b.claimLineId) return 1;
						return 0;
					}) ?? [],
			}),
		}
	);

	const { data: claims, isLoading: isLoadingClaims } = useGetContactInsuranceClaimsQuery(
		{ contactId, query: { status: ['Accepted', 'Submitted'], searchTerm: claimsSearchTerm } },
		{ skip: hideClaimField }
	);

	const isLoading = isFetchingUnallocatedItems || isLoadingClaims || isLoadingClaim;
	const form = useInsuranceForm({ claimId: selectedClaimId, payer, unallocatedItems });
	const { isSubmitting } = form.formState;

	const [createClaimPayment] = useCreateClaimPaymentMutation();

	const handleFormSubmit = useCallback(
		async (values: IInsurancePaymentForm) => {
			if (!contactId || !currencyCode || !unallocatedItems) return;

			const allocations = values.allocations
				.map((allocation, index) => {
					if (!allocation.selected) return null;

					return {
						billableItemId: unallocatedItems[index].billableItemId,
						claimLineId: unallocatedItems[index].claimLineId,
						amount: allocation.allocated,
					};
				})
				.filter(Boolean) as { billableItemId: string; claimLineId?: string; amount: number }[];

			try {
				await createClaimPayment({
					contactId,
					dto: {
						...values,
						paymentDate: values.paymentDate as Date,
						currencyCode,
						allocations,
						claimId: values.claimId,
					},
				}).unwrap();
				trackAddInsurancePayment({
					origin,
					payerId: claimDetail?.payer?.payerNumber,
					submissionMethod: claimDetail?.submissionMethod,
				});
				closeSidePanel();
			} catch (e) {
				dispatch(addErrorSnackbar(e));
			}
		},
		[
			contactId,
			currencyCode,
			unallocatedItems,
			createClaimPayment,
			trackAddInsurancePayment,
			origin,
			claimDetail?.payer?.payerNumber,
			claimDetail?.submissionMethod,
			closeSidePanel,
			dispatch,
		]
	);

	const handleClaimChange = (claimId?: string) => {
		setSelectedClaimId(claimId);
	};

	return (
		<SidePanel.Root open={isOpen} closeLabelId={langIds.Close} sx={{ zIndex: 1300 }} onClose={closeSidePanel}>
			<SidePanel.Header>
				<Stack spacing={1} direction='row' alignItems='center'>
					<IconContainer>
						<MonetizationOnRoundedIcon color='default' />
					</IconContainer>
					<Typography variant='h6'>
						<FormattedMessage id={langIds.InsurancePayment} />
					</Typography>
				</Stack>
			</SidePanel.Header>

			<SidePanel.Content>
				<FormProvider {...form}>
					<Stack
						id='createInsurancePaymentContentForm'
						component='form'
						spacing={3}
						onSubmit={form.handleSubmit(handleFormSubmit)}
					>
						<InsurancePaymentDetailFields
							isEditing={true}
							currencyCode={currencyCode}
							isFetching={isLoading}
							contactId={contactId}
							claims={claims?.items ?? []}
							hideClaimField={hideClaimField}
							onClaimChange={handleClaimChange}
							onClaimSearch={(value) => setClaimsSearchTerm(value)}
						/>

						<OverallocatedWarningBanner />

						<InsurancePaymentAllocationTable
							isFormVisible={true}
							currencyCode={currencyCode}
							unallocatedItems={unallocatedItems}
							isFetching={isLoading}
							onClose={closeSidePanel}
							contactId={contactId}
						/>
					</Stack>
				</FormProvider>
			</SidePanel.Content>

			<SidePanel.Footer>
				<Stack direction={['column-reverse', 'row']} gap={1} flex={1}>
					<Button variant='outlined' fullWidth onClick={closeSidePanel}>
						<FormattedMessage id={langIds.Cancel} />
					</Button>

					<Button
						variant='contained'
						fullWidth
						type='submit'
						form='createInsurancePaymentContentForm'
						disabled={isSubmitting}
					>
						<FormattedMessage id={langIds.Save} />
					</Button>
				</Stack>
			</SidePanel.Footer>

			<SidePanel.Actions />
		</SidePanel.Root>
	);
};
