import { useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { yupResolver } from '@hookform/resolvers/yup';
import { Stack } from '@mui/material';
import { skipToken } from '@reduxjs/toolkit/dist/query';
import * as Yup from 'yup';

import * as SidePanel from '@carepatron/components/src/SidePanel';

import { useSidePanel } from 'components/sidePanels/useSidePanel';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import {
	useAllocatePaymentMutation,
	useGetPaymentDetailsQuery,
	useGetUnallocatedPaymentAllocationsQuery,
} from 'store/api/payments';
import { useCurrentProviderId } from 'store/api/providers/hooks';
import { useClientPermissions } from 'util/hooks/useClientPermissions';

import { getClientFee } from '../../BillingAndPayments/Payments/utils';
import { AllocationPaymentDetailsHeader } from '../common/AllocationPaymentDetailsHeader';
import { AllocationSidePanelFooter } from '../common/AllocationSidePanelFooter';

import { InvoicePaymentSidePanelContent } from './InvoicePaymentSidePanelContent';
import { IPaymentAllocationForm } from './types';

const validationSchema = Yup.object().shape({
	allocations: Yup.array()
		.of(
			Yup.object()
				.shape({
					selected: Yup.boolean().required(),
					allocated: Yup.number()
						.required()
						.when('$items', ([items], schema, context) => {
							const typedOptions = context as typeof context & { index: number };

							if (
								!typedOptions ||
								typeof typedOptions?.index !== 'number' ||
								!typedOptions.parent.selected
							)
								return schema;

							const typedItem = items[typedOptions.index] as IPaymentAllocation | IAllocationItem;

							return typedItem.amount >= 0
								? schema.min(0).max(typedItem.amount)
								: schema.min(typedItem.amount).max(0);
						}),
				})
				.required()
		)
		.required()
		.min(0),
	totalAmount: Yup.number()
		.required()
		.test((totalAmount, { options }) => {
			const typedParent = (options as any)?.parent as IPaymentAllocationForm | undefined;
			const allocations = (typedParent?.allocations ?? []) as IPaymentAllocationForm['allocations'];
			const totalAmountAllocated = allocations.reduce(
				(acc, item) => acc + (item.selected ? item.allocated : 0),
				0
			);
			const isCreditNote = totalAmount < 0;

			return isCreditNote ? totalAmount <= totalAmountAllocated : totalAmount >= totalAmountAllocated;
		}),
});

export interface IInvoicePaymentSidePanelProps {
	contactId: string;
	paymentId: string;
}

export const InvoicePaymentSidePanel = ({ contactId, paymentId }: IInvoicePaymentSidePanelProps) => {
	const dispatch = useDispatch();

	const [isEditing, setIsEditing] = useState(false);

	const { currentProviderId } = useCurrentProviderId();
	const hasEditInvoicesPermission = useClientPermissions('invoicesEdit', contactId);
	const { isOpen, closeSidePanel } = useSidePanel('InvoicePaymentSidePanel');

	const {
		data: paymentDetails,
		paymentDetailsAllocations,
		isFetching: isFetchingPaymentDetails,
	} = useGetPaymentDetailsQuery(!!(paymentId && contactId) ? { paymentId, contactId } : skipToken, {
		selectFromResult: (result) => ({ ...result, paymentDetailsAllocations: result.data?.allocations || [] }),
	});

	const { unallocatedItems, isFetching: isFetchingUnalloctedItems } = useGetUnallocatedPaymentAllocationsQuery(
		!!contactId ? { contactId, query: { payer: 'selfpay' } } : skipToken,
		{
			selectFromResult: (result) => ({
				...result,
				unallocatedItems:
					(result.data ?? []).filter((item) => {
						if (!item) return false;

						const existing = item.id
							? paymentDetailsAllocations.some((accItem) => accItem.id === item.id)
							: paymentDetailsAllocations.some(
									(accItem) =>
										accItem.invoiceLineItemId === item.invoiceLineItemId &&
										accItem.billableItemId === item.billableItemId
								);

						return !existing;
					}) ?? [],
			}),
		}
	);

	const [allocatePayment] = useAllocatePaymentMutation();

	const form = useForm<IPaymentAllocationForm>({
		values: {
			allocations: [
				...paymentDetailsAllocations.map((allocation) => ({
					selected: true,
					allocated: allocation.allocated,
				})),
				...unallocatedItems.map(() => ({
					selected: false,
					allocated: 0,
				})),
			],
			totalAmount: (paymentDetails?.chargeAmount ?? 0) - getClientFee(paymentDetails),
		},
		context: {
			items: [...paymentDetailsAllocations, ...unallocatedItems],
		},
		resolver: yupResolver(validationSchema),
	});

	const { isSubmitting } = form.formState;

	const isEditDisabled = isFetchingPaymentDetails;
	const isFetching = isFetchingPaymentDetails || isFetchingUnalloctedItems;

	const handleOnCancel = () => {
		setIsEditing(false);
		form.reset();
	};

	const handleOnEdit = () => {
		setIsEditing(true);
	};

	const handleOnSubmit = async (values: IPaymentAllocationForm) => {
		if (!paymentId || !contactId || !paymentDetails) return;

		try {
			const updatedAllocations: IPaymentAllocation[] = [];

			paymentDetailsAllocations.forEach((allocation, index) => {
				const formValue = values.allocations[index];
				if (formValue.selected) {
					updatedAllocations.push({ ...allocation, allocated: formValue.allocated });
				}
			});
			unallocatedItems.forEach((item, index) => {
				const unallocatedIndex = index + paymentDetailsAllocations.length;
				const formValue = values.allocations[unallocatedIndex];

				if (formValue.selected) {
					updatedAllocations.push({
						...item,
						paymentId,
						contactId,
						allocated: formValue.allocated,
						providerId: currentProviderId,
					});
				}
			});

			await allocatePayment({
				contactId,
				paymentId,
				dto: {
					amount: paymentDetails.chargeAmount,
					paymentDate: paymentDetails.paymentDate,
					allocations: updatedAllocations,
				},
			}).unwrap();

			setIsEditing(false);
			form.reset();
		} catch (e) {
			dispatch(addErrorSnackbar(e));
		}
	};

	return (
		<SidePanel.Root open={isOpen} onClose={closeSidePanel} closeLabelId={langIds.Close}>
			<FormProvider {...form}>
				<Stack component='form' height='100%' onSubmit={form.handleSubmit(handleOnSubmit)}>
					<AllocationPaymentDetailsHeader paymentDetails={paymentDetails} isFetching={isFetching} />
					<InvoicePaymentSidePanelContent
						isEditing={isEditing}
						unallocatedItems={unallocatedItems}
						paymentDetails={paymentDetails}
						isFetching={isFetching}
						onClose={closeSidePanel}
					/>
					{hasEditInvoicesPermission && (
						<AllocationSidePanelFooter
							contactId={contactId}
							paymentDetails={paymentDetails}
							isEditing={isEditing}
							isEditDisabled={isEditDisabled}
							isFetching={isFetching}
							isSubmitting={isSubmitting}
							onCancel={handleOnCancel}
							onEdit={handleOnEdit}
							onClose={closeSidePanel}
						/>
					)}
					<SidePanel.Actions />
				</Stack>
			</FormProvider>
		</SidePanel.Root>
	);
};
