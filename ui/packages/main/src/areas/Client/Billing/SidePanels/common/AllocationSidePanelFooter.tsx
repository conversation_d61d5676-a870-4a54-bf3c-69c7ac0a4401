import React, { MouseEvent, useMemo, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { Button, Divider, Menu, MenuItem, MenuItemProps, Skeleton, Stack } from '@mui/material';

import { SquareIconButton } from '@carepatron/components';
import * as SidePanel from '@carepatron/components/src/SidePanel';

import { useSidePanel } from 'components/sidePanels/useSidePanel';
import langIds, { TranslationKey } from 'lang/langIds';
import useBreakpoint from 'util/hooks/useBreakpoint';

import { useDeletePayment } from '../../BillingAndPayments/Payments/hooks';
import { calculateTotalAmount, getCanRefundStatus } from '../../BillingAndPayments/Payments/utils';

interface IProps {
	formId?: string;
	contactId: string;
	paymentDetails?: IPaymentDetails;
	isEditing: boolean;
	isEditDisabled: boolean;
	isFetching: boolean;
	isSubmitting: boolean;
	isInsurance?: boolean;
	onEdit: () => void;
	onCancel: () => void;
	onClose: () => void;
}

type MenuAction = {
	labelId: TranslationKey;
	menuItemProps?: Omit<MenuItemProps, 'onClick'>;
	isHidden?: boolean;
	onClick: () => void;
};

export const PAYMENT_SIDE_PANEL_FOOTER_DATA_TEST_IDS = {
	overflowMenu: 'payment-side-panel-footer-overflow-menu',
};

export const AllocationSidePanelFooter = ({
	formId,
	contactId,
	paymentDetails,
	isEditDisabled,
	isEditing,
	isFetching,
	isSubmitting,
	isInsurance,
	onEdit,
	onCancel,
	onClose,
}: IProps) => {
	const isMobile = useBreakpoint('sm');

	const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
	const { openSidePanel: openRefundSidePanel } = useSidePanel('RefundSidePanel');
	const deletePayment = useDeletePayment();

	const handleOnEdit = (e: MouseEvent<HTMLButtonElement>) => {
		onEdit();
		e.preventDefault();
	};

	const menuActions: MenuAction[] = useMemo(() => {
		if (!paymentDetails || !contactId) return [];

		const totalRefundAmount = calculateTotalAmount(paymentDetails.refunds);
		const canRefund = getCanRefundStatus({
			refundStatus: paymentDetails.refundStatus,
			amount: paymentDetails.chargeAmount,
			totalRefundAmount,
			paymentProvider: paymentDetails.paymentProvider,
			isInsurance,
		});

		return [
			{
				labelId: langIds.Refund,
				isHidden: !canRefund,
				onClick: () => {
					openRefundSidePanel({
						contactId,
						paymentId: paymentDetails.id,
						paymentProvider: paymentDetails.paymentProvider,
					});
				},
			},
		].filter(({ isHidden }) => !isHidden);
	}, [contactId, openRefundSidePanel, paymentDetails, isInsurance]);

	const secondaryMenuActions: MenuAction[] = useMemo(() => {
		if (!paymentDetails || !contactId) return [];

		return [
			{
				labelId: langIds.Delete,
				isHidden: !paymentDetails.canDelete,
				onClick: () =>
					deletePayment({
						paymentId: paymentDetails.id,
						onSuccess: onClose,
						date: paymentDetails.paymentDate,
						referenceNumber: paymentDetails.reference,
						paymentMethod: paymentDetails.paymentMethod,
					}),
				menuItemProps: {
					sx: {
						borderTop: '1px',
						color: 'error.main',
					},
				},
			},
		].filter(({ isHidden }) => !isHidden);
	}, [contactId, deletePayment, onClose, paymentDetails]);

	return (
		<SidePanel.Footer>
			<Stack gap={1} flex={1} direction={['column-reverse', 'row']}>
				{isFetching ? (
					<>
						<Skeleton width='40px' />
						<Skeleton width='100%' />
					</>
				) : isEditing ? (
					<>
						<Button variant='outlined' fullWidth onClick={onCancel}>
							<FormattedMessage id={langIds.Cancel} />
						</Button>
						<Button
							variant='contained'
							fullWidth
							type='submit'
							disabled={isSubmitting}
							{...(!!formId && { form: formId })}
						>
							<FormattedMessage id={langIds.Save} />
						</Button>
					</>
				) : (
					<>
						<Menu
							open={!!anchorEl}
							anchorEl={anchorEl}
							onClose={() => setAnchorEl(null)}
							anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
							transformOrigin={{ vertical: 'bottom', horizontal: 'right' }}
							sx={{ '& .MuiMenuItem-root': { minWidth: '136px' } }}
						>
							{menuActions.map((action) => {
								return (
									<MenuItem
										key={action.labelId}
										onClick={() => {
											action.onClick();
											setAnchorEl(null);
										}}
										{...action.menuItemProps}
									>
										<FormattedMessage id={action.labelId} />
									</MenuItem>
								);
							})}
							{!!menuActions.length && !!secondaryMenuActions.length && <Divider />}
							{secondaryMenuActions.map((action) => {
								return (
									<MenuItem
										key={action.labelId}
										onClick={() => {
											action.onClick();
											setAnchorEl(null);
										}}
										{...action.menuItemProps}
									>
										<FormattedMessage id={action.labelId} />
									</MenuItem>
								);
							})}
						</Menu>

						{(!!menuActions.length || !!secondaryMenuActions.length) &&
							(isMobile ? (
								<Button
									variant='outlined'
									fullWidth
									onClick={(e) => {
										setAnchorEl(e.currentTarget);
									}}
								>
									<FormattedMessage id={langIds.MoreActions} />
								</Button>
							) : (
								<SquareIconButton
									variant='outlined'
									icon={<MoreVertIcon />}
									onClick={(e) => {
										setAnchorEl(e.currentTarget);
									}}
									data-testid={PAYMENT_SIDE_PANEL_FOOTER_DATA_TEST_IDS.overflowMenu}
								/>
							))}

						{isMobile && (
							<Button variant='outlined' fullWidth onClick={onClose}>
								<FormattedMessage id={langIds.Cancel} />
							</Button>
						)}

						<Button variant='contained' fullWidth onClick={handleOnEdit} disabled={isEditDisabled}>
							<FormattedMessage id={langIds.EditPaymentDetails} />
						</Button>
					</>
				)}
			</Stack>
		</SidePanel.Footer>
	);
};
