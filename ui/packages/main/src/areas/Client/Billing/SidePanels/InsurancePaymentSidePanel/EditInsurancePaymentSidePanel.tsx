import { useCallback, useMemo, useState } from 'react';
import { FormProvider } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import ArrowBackRounded from '@mui/icons-material/ArrowBackRounded';
import { Button, Stack } from '@mui/material';
import { skipToken } from '@reduxjs/toolkit/dist/query';

import { SidePanel } from '@carepatron/components';

import { useSidePanel } from 'components/sidePanels/useSidePanel';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import {
	useAllocatePaymentMutation,
	useGetPaymentDetailsQuery,
	useGetUnallocatedPaymentAllocationsQuery,
} from 'store/api/payments';
import { useCurrentProviderId } from 'store/api/providers/hooks';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { useClientPermissions } from 'util/hooks/useClientPermissions';

import { AllocationPaymentDetailsHeader } from '../common/AllocationPaymentDetailsHeader';
import { AllocationSidePanelFooter } from '../common/AllocationSidePanelFooter';
import { UnallocatedWarningBanner } from '../common/UnallocatedWarningBanner';

import { IInsurancePaymentForm, useInsuranceForm } from './hooks/useInsuranceForm';
import { InsurancePaymentAllocationTable } from './InsurancePaymentAllocationTable';
import { InsurancePaymentDetailFields } from './InsurancePaymentDetailFields';
import { OverallocatedWarningBanner } from './OverallocatedWarningBanner';

export interface IEditInsurancePaymentSidePanelProps {
	paymentId: string;
	contactId: string;
	claim?: {
		id: string;
		number: string;
	};
}

const FORM_ID = 'editInsurancePaymentForm';

export const EditInsurancePaymentSidePanel = ({ paymentId, contactId, claim }: IEditInsurancePaymentSidePanelProps) => {
	const dispatch = useDispatch();
	const { currentProviderId } = useCurrentProviderId();
	const { isOpen: isPaymentSidePanelOpen, closeSidePanel: closePaymentSidePanel } = useSidePanel(
		'EditInsurancePaymentSidePanel'
	);
	const { openSidePanel: openClaimHistorySidePanel } = useSidePanel('ClaimHistorySidePanel');
	const hasInvoicesEditPermission = useClientPermissions('invoicesEdit', contactId);
	const isMobile = useBreakpoint('sm');

	const [isEditing, setIsEditing] = useState(false);

	const { data: paymentDetails, isFetching: isFetchingPaymentDetails } = useGetPaymentDetailsQuery({
		paymentId,
		contactId,
	});
	const { data: unallocatedPayments, isFetching: isFetchingUnallocatedItems } =
		useGetUnallocatedPaymentAllocationsQuery(
			paymentDetails?.currencyCode
				? { contactId, query: { payer: 'insurance', currencyCode: paymentDetails.currencyCode } }
				: skipToken
		);

	const [allocatePayment] = useAllocatePaymentMutation();

	const isManuallyRecordedPayment =
		paymentDetails?.paymentMethod === 'Insurance' && paymentDetails?.paymentProvider === 'Insurance';

	const unallocatedItems = useMemo(() => {
		const allocations = paymentDetails?.allocations ?? [];
		const unallocated = unallocatedPayments ?? [];

		return unallocated.filter((unallocatedPayment) =>
			allocations.every((allocation) => allocation.billableItemId !== unallocatedPayment.billableItemId)
		);
	}, [paymentDetails?.allocations, unallocatedPayments]);

	const form = useInsuranceForm({ unallocatedItems, paymentDetails });
	const { reset, formState, handleSubmit } = form;

	const { isSubmitting } = formState;
	const isFetching = isFetchingPaymentDetails || isFetchingUnallocatedItems;

	const handleOnCancel = () => {
		setIsEditing(false);
		reset();
	};

	const handleOnEdit = () => {
		setIsEditing(true);
	};

	const handleOnBack = () => {
		closePaymentSidePanel();

		if (claim) {
			openClaimHistorySidePanel({ claimId: claim.id, contactId, claimNumber: claim.number });
		}
	};

	const handleOnSubmit = useCallback(
		async (values: IInsurancePaymentForm) => {
			try {
				const updatedAllocations: IPaymentAllocation[] = [];
				const paymentDetailAllocations = paymentDetails?.allocations ?? [];

				paymentDetailAllocations.forEach((allocation, index) => {
					const formValue = values.allocations[index];
					if (formValue.selected) {
						updatedAllocations.push({ ...allocation, allocated: formValue.allocated });
					}
				});

				unallocatedItems.forEach((item, index) => {
					const unallocatedIndex = index + paymentDetailAllocations.length;
					const formValue = values.allocations[unallocatedIndex];

					if (formValue.selected) {
						updatedAllocations.push({
							...item,
							paymentId,
							contactId,
							allocated: formValue.allocated,
							providerId: currentProviderId,
						});
					}
				});

				await allocatePayment({
					contactId,
					paymentId,
					dto: { ...values, paymentDate: values.paymentDate as Date, allocations: updatedAllocations },
				}).unwrap();

				setIsEditing(false);
				reset();
			} catch (e) {
				dispatch(addErrorSnackbar(e));
			}
		},
		[
			reset,
			allocatePayment,
			contactId,
			currentProviderId,
			dispatch,
			paymentDetails?.allocations,
			paymentId,
			unallocatedItems,
		]
	);

	return (
		<SidePanel.Root open={isPaymentSidePanelOpen} onClose={closePaymentSidePanel} closeLabelId={langIds.Close}>
			<AllocationPaymentDetailsHeader
				paymentDetails={paymentDetails}
				isFetching={isFetching}
				{...(!!claim &&
					isMobile && {
						startAction: (
							<Button startIcon={<ArrowBackRounded />} onClick={handleOnBack}>
								<FormattedMessage id={langIds.Back} />
							</Button>
						),
					})}
			/>

			<SidePanel.Actions>
				{!!claim && (
					<SidePanel.Action
						icon={<ArrowBackRounded />}
						label={<FormattedMessage id={langIds.Back} />}
						onClick={handleOnBack}
					/>
				)}
			</SidePanel.Actions>

			<SidePanel.Content>
				<FormProvider {...form}>
					<Stack id={FORM_ID} component='form' spacing={3} onSubmit={handleSubmit(handleOnSubmit)}>
						<InsurancePaymentDetailFields
							paymentDetails={paymentDetails}
							currencyCode={paymentDetails?.currencyCode}
							isFetching={isFetching}
							isEditing={isManuallyRecordedPayment && isEditing}
							contactId={contactId}
							hideClaimField
							shouldRenderDetailFields
						/>

						{!isEditing && paymentDetails && !!paymentDetails.unallocated && !isFetching && (
							<UnallocatedWarningBanner isUnallocated={paymentDetails.unallocated >= 0} />
						)}

						{isEditing && <OverallocatedWarningBanner />}

						<InsurancePaymentAllocationTable
							paymentAllocations={paymentDetails?.allocations}
							currencyCode={paymentDetails?.currencyCode}
							unallocatedItems={unallocatedItems}
							isFetching={isFetching}
							isFormVisible={isEditing}
							onClose={closePaymentSidePanel}
							contactId={contactId}
						/>
					</Stack>
				</FormProvider>
			</SidePanel.Content>

			{hasInvoicesEditPermission && (
				<AllocationSidePanelFooter
					formId={FORM_ID}
					contactId={contactId}
					paymentDetails={paymentDetails}
					isEditing={isEditing}
					isEditDisabled={isFetchingPaymentDetails}
					isFetching={isFetching}
					onEdit={handleOnEdit}
					onCancel={handleOnCancel}
					isSubmitting={isSubmitting}
					onClose={closePaymentSidePanel}
					isInsurance={true}
				/>
			)}
		</SidePanel.Root>
	);
};
