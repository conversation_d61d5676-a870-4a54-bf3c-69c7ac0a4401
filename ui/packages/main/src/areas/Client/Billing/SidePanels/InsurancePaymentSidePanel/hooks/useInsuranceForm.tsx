import { useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { pullAt } from 'lodash';
import * as Yup from 'yup';

import { intl } from 'lang';
import langIds from 'lang/langIds';

import { IFormAllocation } from '../../InvoicePaymentSidePanel/types';

export interface IInsurancePaymentForm {
	paymentDate: Date | null;
	amount: number;
	claimId?: string;
	payerName?: string;
	payerId?: string;
	reference?: string;
	allocations: IFormAllocation[];
}

const getValidationSchema = () =>
	Yup.object<IInsurancePaymentForm>().shape({
		paymentDate: Yup.date()
			.required()
			.nullable()
			.notOneOf([null], intl.$t({ id: langIds.ValidationMixedRequired })),
		amount: Yup.number().moreThan(0).required(),
		claimId: Yup.string(),
		payerName: Yup.string(),
		payerId: Yup.string(),
		reference: Yup.string(),
		allocations: Yup.array()
			.of(
				Yup.object<IFormAllocation>().shape({
					selected: Yup.boolean().required(),
					allocated: Yup.number()
						.required()
						.when('$items', ([items], schema, context) => {
							const typedItems = items as IAllocationItem[];
							const { index, parent, from } =
								(context as typeof context & { index: number; from: any }) || {};
							const allAllocations = (from?.[1]?.value?.allocations ?? []) as IFormAllocation[];

							if (typeof index !== 'number' || !parent.selected) {
								return schema;
							}

							const currentItem = typedItems[index];
							const remainingAmount = currentItem.amount - currentItem.paid;

							// Finds and sums up all allocated amounts that has the same billable item id
							const relatedBillableItemIndexes = typedItems
								.map((item, index) => ({ ...item, index }))
								.filter(
									(relatedItem) =>
										relatedItem.billableItemId === currentItem.billableItemId &&
										relatedItem.claimLineId !== currentItem.claimLineId
								)
								.map((item) => item.index);
							const relatedBillableItems = pullAt([...allAllocations], relatedBillableItemIndexes);
							const totalRelatedBillableAllocation = relatedBillableItems.reduce(
								(acc, relatedItem) => acc + (relatedItem.selected ? relatedItem.allocated : 0),
								0
							);

							return schema
								.min(0)
								.max(remainingAmount)
								.test(
									'total-amount-check',
									intl.formatMessage({ id: langIds.ValidationNumberMax }, { max: remainingAmount }),
									(value) => totalRelatedBillableAllocation + value <= remainingAmount
								);
						}),
				})
			)
			.required(),
		// Add separate field for validating if total amount is greater than the allocated items
		// so that the error message won't conflict with the banner showing
		totalAmount: Yup.number().test((_, context) => {
			const amount = context.parent.amount as number;
			const allocations = context.parent.allocations as IFormAllocation[];

			const totalAllocations = allocations.reduce(
				(acc, allocation) => acc + (allocation.selected ? allocation.allocated : 0),
				0
			);

			return amount >= totalAllocations;
		}),
	});

interface IProps {
	claimId?: string;
	payer?: IInsuranceClaimPayer;
	paymentDetails?: IPaymentDetails;
	unallocatedItems: IAllocationItem[];
}

export const useInsuranceForm = ({ claimId, payer, paymentDetails, unallocatedItems }: IProps) => {
	const allocationItems = useMemo(() => {
		return [...(paymentDetails?.allocations ?? []), ...unallocatedItems];
	}, [paymentDetails?.allocations, unallocatedItems]);

	const claimItemsAmount = unallocatedItems.reduce((acc, item) => {
		if (!!claimId && item.claim?.id === claimId) {
			return acc + item.amount;
		}
		return acc;
	}, 0);

	const allocations = useMemo(() => {
		return [
			...(paymentDetails?.allocations ?? []).map((a) => ({
				selected: true,
				allocated: a.allocated,
			})),
			...unallocatedItems.map((item) => {
				const isClaimItem = !!claimId && item.claim?.id === claimId;
				return {
					selected: isClaimItem,
					allocated: isClaimItem ? item.amount : 0,
				};
			}),
		];
	}, [claimId, paymentDetails?.allocations, unallocatedItems]);

	const paymentDate = useMemo(() => {
		if (paymentDetails?.paymentDate) {
			return new Date(paymentDetails.paymentDate);
		}
		return new Date();
	}, [paymentDetails?.paymentDate]);

	return useForm<IInsurancePaymentForm>({
		values: {
			paymentDate,
			amount: claimItemsAmount || paymentDetails?.chargeAmount || 0,
			claimId,
			payerName: payer?.name ?? paymentDetails?.payerName ?? '',
			payerId: payer?.id ?? '',
			reference: paymentDetails?.reference ?? '',
			allocations,
		},
		resolver: yupResolver(getValidationSchema()),
		context: { items: allocationItems },
	});
};
