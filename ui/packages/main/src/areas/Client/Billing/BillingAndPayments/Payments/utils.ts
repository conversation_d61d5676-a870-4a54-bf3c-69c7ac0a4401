import { intl } from 'lang';
import langIds from 'lang/langIds';
import { getPaymentTypeLabel } from 'util/invoice';

import { IPaymentAllocationForm } from '../../SidePanels/InvoicePaymentSidePanel/types';

export const calculateTotalAmount = (args: { amount: number }[]) => {
	const totalAmount = args.reduce((acc, item) => {
		return acc + item.amount;
	}, 0);

	return parseFloat(totalAmount.toFixed(2));
};

export const getRefundStatus = ({
	amount,
	refunds = [],
	refundStatus,
	paymentType,
	isUnallocated,
}: {
	amount: number;
	refundStatus?: string;
	paymentType: string;
	isUnallocated?: boolean;
	refunds: IRefundReference[];
}) => {
	const paymentTypeLabel = getPaymentTypeLabel(paymentType);
	const totalRefundAmount = calculateTotalAmount(refunds);

	let refundLabel = '';

	if (refundStatus === 'Refunded' || (refundStatus === 'Pending' && totalRefundAmount >= amount)) {
		refundLabel = intl.formatMessage({ id: langIds.Refunded });
	} else if (refundStatus === 'PartiallyRefunded') {
		refundLabel = intl.formatMessage({ id: langIds.PartiallyRefunded });
	} else if (refundStatus === 'Failed') {
		refundLabel = intl.formatMessage({ id: langIds.RefundFailed });
	}

	return [paymentTypeLabel, isUnallocated && intl.formatMessage({ id: langIds.Unallocated }), refundLabel]
		.filter(Boolean)
		.join(' • ');
};

export const getCanRefundStatus = (args: {
	refundStatus?: string;
	amount: number;
	totalRefundAmount: number;
	paymentProvider: string;
	isInsurance?: boolean;
}) => {
	return (
		!args.isInsurance &&
		args.paymentProvider !== 'CustomerBalance' &&
		args.refundStatus !== 'Refunded' &&
		args.refundStatus !== 'Failed' &&
		args.amount > args.totalRefundAmount
	);
};

export const getCanIssueCreditsStatus = (args: { unallocated: number }) => {
	return args.unallocated !== 0;
};

export const getClientFee = (args?: Pick<IPaymentDetails, 'fee' | 'isClientChargedFee'>) => {
	return args?.isClientChargedFee ? (args?.fee ?? 0) : 0;
};

export const calculateTotalUnallocated = (
	allocatedFormValues: IPaymentAllocationForm['allocations'],
	availableToAllocate: number
) => {
	const totalFormAllocated = Object.values(allocatedFormValues).reduce((prev, curr) => {
		if (!curr?.selected) return prev;
		return prev + (curr?.allocated ?? 0);
	}, 0);
	return availableToAllocate - totalFormAllocated;
};

export const getIsPaymentUnallocated = (totalAmount: number, unallocatedAmount: number) => {
	const isCreditNote = totalAmount < 0;
	return isCreditNote ? unallocatedAmount <= 0 : unallocatedAmount >= 0;
};
