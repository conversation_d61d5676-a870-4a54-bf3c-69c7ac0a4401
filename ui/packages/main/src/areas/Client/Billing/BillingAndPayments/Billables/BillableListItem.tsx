import React, { useState } from 'react';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { IconButton } from '@mui/material';

import { AmountItemColumn } from '../common/AmountItemColumn';
import { DetailsColumnItem } from '../common/DetailsColumnItem';
import { ItemContainer } from '../common/ItemContainer';
import { ItemLayout } from '../common/ItemLayout';

import { BillableExpandedContent } from './BillableExpandedContent';
import { BillableIcon } from './BillableIcon';
import { BillableOverflowActions } from './BillableOverflowActions';
import { getTitleIdByBillableType } from './utils';

interface IProps extends IBillableEntry {
	hasInvoicesEditPermission: boolean;
	isDefaultExpanded?: boolean;
}

export const BILLABLE_LIST_ITEM_TEST_IDS = {
	root: 'billable-list-item-root',
};

export const BillableListItem = ({ isDefaultExpanded, ...restProps }: IProps) => {
	const {
		claims,
		type,
		uncharged,
		unclaimed,
		date,
		details,
		paid,
		currencyCode,
		unpaid,
		writeOff,
		issuedCredits,
		invoices,
		insuranceUnpaid,
		insurancePaid,
	} = restProps;

	const [isExpanded, setIsExpanded] = React.useState(isDefaultExpanded);
	const [baseOverflowAnchorEl, setBaseOverflowAnchorEl] = useState<HTMLDivElement | null>(null);

	const hasStatusBanner = !!uncharged || !!unclaimed || !!writeOff;
	const hasRelatedItems = claims?.length > 0 || invoices?.length > 0 || issuedCredits?.length > 0;
	const showWarningIcon = !!uncharged || !!unclaimed;

	const isExpandable = hasStatusBanner || hasRelatedItems;

	const onExpandToggle = () => setIsExpanded((prev) => !prev);

	const totalUnpaid = unpaid + (insuranceUnpaid ?? 0);
	const totalPaid = paid + (insurancePaid ?? 0);

	return (
		<ItemContainer
			data-testid={BILLABLE_LIST_ITEM_TEST_IDS.root}
			isExpanded={isExpanded}
			expandedContent={<BillableExpandedContent {...restProps} />}
		>
			<ItemLayout
				onClick={() => isExpandable && onExpandToggle()}
				onMouseEnter={(e) => setBaseOverflowAnchorEl(e.currentTarget)}
				onMouseLeave={() => setBaseOverflowAnchorEl(null)}
				dateColumn={<BillableIcon type={type} showWarningIcon={showWarningIcon} />}
				detailsColumn={
					<DetailsColumnItem date={date} title={getTitleIdByBillableType(type)} description={details} />
				}
				amountColumn={
					<>
						<AmountItemColumn
							price={totalUnpaid}
							currencyCode={currencyCode}
							shouldRenderText={Math.abs(totalUnpaid) > 0}
						/>
						<AmountItemColumn
							price={totalPaid}
							currencyCode={currencyCode}
							shouldRenderText={Math.abs(totalPaid) > 0}
						/>
					</>
				}
				actionsColumn={
					<>
						{isExpandable && (
							<IconButton>{isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}</IconButton>
						)}
						<BillableOverflowActions {...restProps} overflowAnchorEl={baseOverflowAnchorEl} />
					</>
				}
				sx={{
					cursor: isExpandable ? 'pointer' : 'default',
					'&:hover': (theme) => ({
						backgroundColor: theme.palette.action.hover,
					}),
				}}
			/>
		</ItemContainer>
	);
};
