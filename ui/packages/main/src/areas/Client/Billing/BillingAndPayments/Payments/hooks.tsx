import { useCallback } from 'react';
import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import { CurrencyExchange, ReceiptLongRounded } from '@mui/icons-material';
import Delete from '@mui/icons-material/Delete';
import { Box } from '@mui/material';

import { IOverflowAction, IOverflowSecondaryAction } from 'components/common/OverflowActionsTooltip';
import { createOverflowAction } from 'components/common/ResponsiveOverflowActions/utils';
import { useSidePanel } from 'components/sidePanels/useSidePanel';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { GetContactPaymentsApiArg, useDeletePaymentMutation, useLazyGetContactPaymentsQuery } from 'store/api/payments';
import { addModal } from 'store/slices/modals/slice';
import { formatDateToISOString, localizeDate } from 'util/date';
import { getPaymentTypeLabel } from 'util/invoice';

import { BILLING_PAGINATION_QUERY_LIMIT } from '../constants';

import { calculateTotalAmount, getCanRefundStatus } from './utils';

export const usePaginateContactPayments = () => {
	const dispatch = useDispatch();
	const [getContactPayments, { data, isFetching, originalArgs }] = useLazyGetContactPaymentsQuery();
	const hasMore = data?.pagination?.hasMore ?? false;
	const offset = data?.pagination?.offset ?? 0;
	const items = data?.items ?? [];

	const initList = useCallback(
		async (query: GetContactPaymentsApiArg['query']) => {
			if (!query?.contactId) return;

			try {
				await getContactPayments({
					query: {
						...query,
						limit: BILLING_PAGINATION_QUERY_LIMIT,
						offset: 0,
					},
				}).unwrap();
			} catch (error) {
				dispatch(addErrorSnackbar(error));
			}
		},
		[dispatch, getContactPayments]
	);

	const loadMore = useCallback(async () => {
		if (!hasMore || !originalArgs?.query) return;
		const nextOffset = offset + BILLING_PAGINATION_QUERY_LIMIT;
		try {
			await getContactPayments({
				query: {
					...originalArgs.query,
					limit: BILLING_PAGINATION_QUERY_LIMIT,
					offset: nextOffset,
				},
			}).unwrap();
		} catch (error) {
			dispatch(addErrorSnackbar(error));
		}
	}, [dispatch, getContactPayments, hasMore, offset, originalArgs?.query]);

	return { initList, loadMore, hasMore, isFetching, payments: items };
};

export const useDeletePayment = () => {
	const dispatch = useDispatch();
	const [deletePayment] = useDeletePaymentMutation();

	return useCallback(
		(args: {
			paymentId: string;
			date: string;
			paymentMethod: string;
			referenceNumber?: string;
			onSuccess?: () => void;
		}) => {
			const localizedDate = localizeDate(args.date, 'short');
			const localizedType = getPaymentTypeLabel(args.paymentMethod);

			dispatch(
				addModal({
					type: 'DeleteRestorableItem',
					data: {
						type: 'Payment',
						context: <strong>{args.referenceNumber || `${localizedType} • ${localizedDate}`}</strong>,
						onDelete: async () => {
							await deletePayment({ paymentId: args.paymentId }).unwrap();
							args.onSuccess?.();
						},
						onError: (e) => dispatch(addErrorSnackbar(e)),
					},
				})
			);
		},
		[deletePayment, dispatch]
	);
};

export const usePaymentOverflowActions = () => {
	const deletePayment = useDeletePayment();

	const { openSidePanel: openRefundSidePanel } = useSidePanel('RefundSidePanel');
	const { openSidePanel: openInvoicePaymentSidePanel } = useSidePanel('InvoicePaymentSidePanel');
	const { openSidePanel: openInsurancePaymentSidePanel } = useSidePanel('EditInsurancePaymentSidePanel');

	return useCallback(
		({
			payment,
			hasInvoicesEditPermission,
		}: {
			payment: IContactPayment | IPayment;
			hasInvoicesEditPermission?: boolean;
		}) => {
			const paymentRefunds = payment.refunds ?? [];
			const totalRefundAmount = calculateTotalAmount(paymentRefunds);
			const canRefund = getCanRefundStatus({
				refundStatus: payment.refundStatus,
				amount: payment.amount,
				totalRefundAmount,
				paymentProvider: payment.paymentProvider,
				isInsurance:
					payment.type === 'Insurance' || ('payerType' in payment && payment.payerType === 'Insurance'),
			});

			const primaryOverflowActions: IOverflowAction[] = [];
			const secondaryOverflowActions: IOverflowSecondaryAction[] = [];

			if (payment.isBillingV2 && payment.id) {
				primaryOverflowActions.push(
					createOverflowAction(
						'viewReceipt',
						<ReceiptLongRounded />,
						<FormattedMessage id={langIds.ViewDetails} />,
						() => {
							const sidePanelProps = { contactId: payment.contactId, paymentId: payment.id! };

							if (payment.type === 'Insurance') {
								openInsurancePaymentSidePanel(sidePanelProps);
							} else {
								openInvoicePaymentSidePanel(sidePanelProps);
							}
						}
					)
				);
			}

			// Render only if the status isn't yet refunded and the amount is over 0
			if (hasInvoicesEditPermission && canRefund && payment.id) {
				const handleRefund = () => {
					openRefundSidePanel({
						contactId: payment.contactId,
						paymentId: payment.id!,
						paymentProvider: payment.paymentProvider,
					});
				};

				primaryOverflowActions.push(
					createOverflowAction(
						'refund',
						<CurrencyExchange />,
						<FormattedMessage id={langIds.Refund} />,
						handleRefund
					)
				);
			}

			if (hasInvoicesEditPermission && payment.canDelete && payment.id) {
				secondaryOverflowActions.push(
					createOverflowAction(
						'delete',
						<Delete />,
						<Box sx={(theme) => ({ color: theme.palette.error.main })}>
							<FormattedMessage id={langIds.Delete} />
						</Box>,
						() =>
							deletePayment({
								paymentId: payment.id!,
								paymentMethod: payment.type,
								date:
									typeof payment.paymentDate === 'string'
										? payment.paymentDate
										: formatDateToISOString(payment.paymentDate),
								referenceNumber: 'reference' in payment ? payment.reference : undefined,
							})
					)
				);
			}

			return { primaryOverflowActions, secondaryOverflowActions };
		},
		[deletePayment, openInsurancePaymentSidePanel, openInvoicePaymentSidePanel, openRefundSidePanel]
	);
};
