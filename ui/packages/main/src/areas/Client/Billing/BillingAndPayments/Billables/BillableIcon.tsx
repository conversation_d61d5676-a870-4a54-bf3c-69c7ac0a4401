import WarningRoundedIcon from '@mui/icons-material/WarningRounded';

import { IconContainer } from '../common/IconContainer';

import { getIconByBillableType } from './utils';

interface IProps {
	type: IBillableEntry['type'];
	showWarningIcon?: boolean;
}

export const BillableIcon = ({ type, showWarningIcon }: IProps) => {
	const IconComponent = showWarningIcon ? WarningRoundedIcon : getIconByBillableType(type);
	const backgroundColor = showWarningIcon ? 'background.warning' : 'background.default';
	const iconColor = showWarningIcon ? 'warning' : 'default';

	return (
		<IconContainer sx={{ backgroundColor }}>
			<IconComponent color={iconColor} />
		</IconContainer>
	);
};
