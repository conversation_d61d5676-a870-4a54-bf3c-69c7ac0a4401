import { TranslationKey } from 'lang/langIds';

export type ClientBillingTab = 'billables' | 'invoices' | 'claims' | 'payments' | 'superbills';

export type BillablesFilterOption =
	| 'All'
	| 'Paid'
	| 'Uncharged'
	| 'Unpaid'
	| 'Unclaimed'
	| 'Overpaid'
	| 'InsuranceUnpaid';

export type PaymentsFilterOption = 'All' | 'Unallocated';

export type InvoiceFilterOptions = 'All' | Exclude<InvoiceStatus, 'Invalid'>;

export type ClaimsFilterOption = Exclude<ClaimStatus, 'Unknown' | 'Archived'> | 'All';

export type PaymentsStatusFilterOptions = 'All' | 'Insurance' | 'Online' | 'External' | 'Credit' | 'ClaimMD';

export type LinkedItemProps = {
	contactId: string;
	providerId: string;
	hasInvoicesEditPermission: boolean;
};

export type BillingOptionDisplayLabel = { label: string } | { labelId: TranslationKey };
