import { useEffect } from 'react';

import { BillingListContainer } from '../common/BillingListContainer';
import { BillingSkeletonLoader } from '../common/BillingSkeletonLoader';
import { useGetBillingSearchParams } from '../hooks';
import { PaymentsFilterOption, PaymentsStatusFilterOptions } from '../types';

import { usePaginateContactPayments } from './hooks';
import { PaymentEmptyState } from './PaymentEmptyState';
import PaymentItem from './PaymentItem';

interface IProps {
	contactId: string;
	hasInvoicesEditPermission?: boolean;
}

export const queryByPaymentStatus: Partial<Record<PaymentsFilterOption, Object>> = {
	Unallocated: {
		isUnallocated: true,
	},
};

export const queryByPaymentType: Partial<Record<PaymentsStatusFilterOptions, Object>> = {
	Insurance: {
		paymentProvider: 'Insurance',
	},
	Online: {
		paymentProvider: 'Stripe',
	},
	External: {
		paymentProvider: 'Manual',
	},
	Credit: {
		paymentProvider: 'CustomerBalance',
	},
	ClaimMD: {
		paymentProvider: 'ClaimMD',
	},
};

export const PaymentsList = ({ contactId, hasInvoicesEditPermission }: IProps) => {
	const { queryDateRange, type, status, searchTerm } = useGetBillingSearchParams();

	const { payments, initList, isFetching: isLoadingPayments, hasMore, loadMore } = usePaginateContactPayments();

	useEffect(() => {
		if (!contactId) return;

		const queryStatus = status ? queryByPaymentStatus[status] : undefined;
		const paymentType = type ? queryByPaymentType[type] : undefined;

		initList({ contactId, ...queryDateRange, ...queryStatus, ...paymentType, searchTerm });
	}, [initList, contactId, type, queryDateRange, status, searchTerm]);

	const getBodyComponent = () => {
		if (isLoadingPayments && !payments.length) {
			return (
				<>
					<BillingSkeletonLoader />
					<BillingSkeletonLoader />
					<BillingSkeletonLoader />
				</>
			);
		}

		if (!payments.length) {
			return <PaymentEmptyState />;
		}

		return (
			<>
				{payments.map((payment) => (
					<PaymentItem
						key={payment.id}
						hasInvoicesEditPermission={hasInvoicesEditPermission}
						payment={payment}
					/>
				))}
				{isLoadingPayments && <BillingSkeletonLoader />}
			</>
		);
	};

	return (
		<BillingListContainer hasMore={hasMore} loadMore={loadMore}>
			{getBodyComponent()}
		</BillingListContainer>
	);
};
