import { useCallback, useMemo } from 'react';
import { useIntl } from 'react-intl';
import { useSearchParams } from 'react-router-dom';
import { endOfDay, startOfDay } from 'date-fns';

import { useLegacyFeatureModule } from 'store/slices/features/hooks';
import { parseDateWithFallback } from 'util/date';

import { BillingOptionDisplayLabel } from './types';
import { getBillingActiveTab, getBillingDateRangeFilterOptions } from './utils';

export const useGetBillingSearchParams = () => {
	const [searchParams] = useSearchParams();

	return useMemo(() => {
		const dateId = searchParams.get('dateId');
		const toDate = parseDateWithFallback(searchParams.get('toDate'), undefined);
		const fromDate = parseDateWithFallback(searchParams.get('fromDate'), undefined);
		const status = searchParams.get('status');
		const type = searchParams.get('type');
		const searchTerm = searchParams.get('searchTerm');
		const unverifiedActiveTab = searchParams.get('activeTab');
		const activeTab = getBillingActiveTab(unverifiedActiveTab);

		const formatDateRangeForQuery = () => {
			const dateRangeOptions = getBillingDateRangeFilterOptions(activeTab);
			const dateRangeValue = dateId ? dateRangeOptions.find((option) => option.id === dateId) : null;

			// Make sure these are formatted otherwise it will cause rtk to keep querying since the dates keep changing
			if (dateRangeValue?.toDate && dateRangeValue.fromDate) {
				return { toDate: endOfDay(dateRangeValue.toDate), fromDate: startOfDay(dateRangeValue.fromDate) };
			}

			return toDate && fromDate ? { toDate, fromDate } : {};
		};

		const queryDateRange = formatDateRangeForQuery();

		return { type, status, toDate, fromDate, dateId, searchTerm, queryDateRange, activeTab };
	}, [searchParams]);
};

export const useGetBillingFilterOptions = () => {
	const { $t } = useIntl();
	const hasInsuranceModule = useLegacyFeatureModule('InsuranceBilling');
	const hasClaimsModule = useLegacyFeatureModule('InsuranceClaims');

	return useCallback(
		(
			currentActiveTab: string,
			optionsMap: {
				[key: string]: ({
					value: string;
					isInsuranceModule?: boolean;
					isClaimsModule?: boolean;
				} & BillingOptionDisplayLabel)[];
			}
		) =>
			(optionsMap[currentActiveTab] ?? [])
				.filter(({ isInsuranceModule, isClaimsModule }) => {
					if (isInsuranceModule) return hasInsuranceModule;
					if (isClaimsModule) return hasClaimsModule;

					return true;
				})
				.map((option) => ({
					id: option.value,
					label: 'labelId' in option ? $t({ id: option.labelId }) : option.label,
					value: option.value,
				})),
		[$t, hasInsuranceModule, hasClaimsModule]
	);
};

export const useHandleQueryParamChange = () => {
	const [searchParams, setSearchParams] = useSearchParams();

	return useCallback(
		(field: string, value: string) => {
			const updatedSearchParams = new URLSearchParams(searchParams.toString());

			if (value) {
				updatedSearchParams.set(field, value);
			} else {
				updatedSearchParams.delete(field);
			}

			setSearchParams(updatedSearchParams);
		},
		[searchParams, setSearchParams]
	);
};
