import { addDays, addMonths, endOfMonth, startOfMonth } from 'date-fns';

import { getCurrentDate, IDateRangeOption } from 'areas/Billing/Invoices/DateRangePicker';
import { intl } from 'lang';
import langIds, { TranslationKey } from 'lang/langIds';

import {
	BillablesFilterOption,
	BillingOptionDisplayLabel,
	ClaimsFilterOption,
	ClientBillingTab,
	InvoiceFilterOptions,
	PaymentsFilterOption,
	PaymentsStatusFilterOptions,
} from './types';

export const BILLING_PAGINATION_QUERY_LIMIT = 20;

export const BILLABLE_STATUS_OPTIONS: {
	value: BillablesFilterOption;
	labelId: TranslationKey;
	isInsuranceModule?: boolean;
	isClaimsModule?: boolean;
}[] = [
	{ value: 'All', labelId: langIds.AllStatuses },
	{ value: 'Paid', labelId: langIds.Paid },
	{ value: 'Uncharged', labelId: langIds.Uninvoiced },
	{ value: 'Unpaid', labelId: langIds.Unpaid },
	{ value: 'Unclaimed', labelId: langIds.Unclaimed, isInsuranceModule: true },
	{ value: 'Overpaid', labelId: langIds.Overpaid },
	{ value: 'InsuranceUnpaid', labelId: langIds.InsuranceUnpaid, isClaimsModule: true },
];

export const PAYMENT_STATUS_OPTIONS: { value: PaymentsFilterOption; labelId: TranslationKey }[] = [
	{ value: 'All', labelId: langIds.AllStatuses },
	{ value: 'Unallocated', labelId: langIds.Unallocated },
];

const CLAIM_STATUS_RECORD: Record<ClaimsFilterOption, { value: ClaimsFilterOption; labelId: TranslationKey }> = {
	All: { value: 'All', labelId: langIds.AllStatuses },
	Draft: { value: 'Draft', labelId: langIds.Draft },
	Validated: { value: 'Validated', labelId: langIds.Validated },
	Submitted: { value: 'Submitted', labelId: langIds.Submitted },
	Rejected: { value: 'Rejected', labelId: langIds.Rejected },
	Denied: { value: 'Denied', labelId: langIds.Denied },
	Accepted: { value: 'Accepted', labelId: langIds.Accepted },
	Paid: { value: 'Paid', labelId: langIds.Paid },
	PartiallyPaid: { value: 'PartiallyPaid', labelId: langIds.PartiallyPaid },
	Closed: { value: 'Closed', labelId: langIds.Closed },
};

export const CLAIM_STATUS_OPTIONS: { value: ClaimsFilterOption; labelId: TranslationKey }[] =
	Object.values(CLAIM_STATUS_RECORD);

export const INVOICE_STATUS_OPTIONS: { value: InvoiceFilterOptions; labelId: TranslationKey }[] = [
	{ value: 'All', labelId: langIds.AllStatuses },
	{ value: 'Paid', labelId: langIds.Paid },
	{ value: 'Unpaid', labelId: langIds.Unpaid },
	{ value: 'Sent', labelId: langIds.Sent },
	{ value: 'Void', labelId: langIds.Void },
	{ value: 'Processing', labelId: langIds.Processing },
];

export const PAYMENT_TYPE_OPTIONS: ({
	value: PaymentsStatusFilterOptions;
	isInsuranceModule?: boolean;
} & BillingOptionDisplayLabel)[] = [
	{ value: 'All', labelId: langIds.AllTypes },
	{ value: 'Insurance', labelId: langIds.Insurance, isInsuranceModule: true },
	{ value: 'Online', labelId: langIds.Online },
	{ value: 'External', labelId: langIds.External },
	{ value: 'Credit', labelId: langIds.Credit },
	{ value: 'ClaimMD', label: 'ClaimMD' },
];

export const BILLING_TYPES_WITH_SEARCH_FILTER: ClientBillingTab[] = ['invoices', 'payments', 'claims'];

export const getBillableDateRangeOptions = (): IDateRangeOption[] => [
	{
		id: 'all',
		label: intl.formatMessage({ id: langIds.All }),
	},
	{
		id: 'default',
		label: intl.formatMessage({ id: langIds.Default }),
		fromDate: addMonths(getCurrentDate(), -2),
		toDate: addDays(getCurrentDate(), 14),
	},
	{
		id: 'last14days',
		label: intl.formatMessage({ id: langIds.LastNDays }, { number: 14 }),
		fromDate: addDays(getCurrentDate(), -13),
		toDate: getCurrentDate(),
	},
	{
		id: 'lastmonth',
		label: intl.formatMessage({ id: langIds.LastMonth }),
		fromDate: startOfMonth(addMonths(getCurrentDate(), -1)),
		toDate: endOfMonth(addMonths(getCurrentDate(), -1)),
	},
	{
		id: 'thismonth',
		label: intl.formatMessage({ id: langIds.ThisMonth }),
		fromDate: startOfMonth(getCurrentDate()),
		toDate: endOfMonth(getCurrentDate()),
	},
	{
		id: 'next14days',
		label: intl.formatMessage({ id: langIds.NextNDays }, { number: 14 }),
		fromDate: getCurrentDate(),
		toDate: addDays(getCurrentDate(), 13),
	},
	{
		id: 'custom',
		label: intl.formatMessage({ id: langIds.CustomRange }),
	},
];

export const getBillingDateRangeOptions = (): IDateRangeOption[] => [
	{
		id: 'all',
		label: intl.formatMessage({ id: langIds.All }),
	},
	{
		id: 'last14days',
		label: intl.formatMessage({ id: langIds.LastNDays }, { number: 14 }),
		fromDate: addDays(getCurrentDate(), -13),
		toDate: getCurrentDate(),
	},
	{
		id: 'lastmonth',
		label: intl.formatMessage({ id: langIds.LastMonth }),
		fromDate: startOfMonth(addMonths(getCurrentDate(), -1)),
		toDate: endOfMonth(addMonths(getCurrentDate(), -1)),
	},
	{
		id: 'thismonth',
		label: intl.formatMessage({ id: langIds.ThisMonth }),
		fromDate: startOfMonth(getCurrentDate()),
		toDate: endOfMonth(getCurrentDate()),
	},
	{
		id: 'custom',
		label: intl.formatMessage({ id: langIds.CustomRange }),
	},
];
