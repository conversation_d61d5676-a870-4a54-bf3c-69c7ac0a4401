import { useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import ShieldRoundedIcon from '@mui/icons-material/ShieldRounded';
import { StackProps } from '@mui/material';

import { useClaimOverflowActions } from 'areas/Insurance/Claims/hooks/useClaimOverflowActions';
import { ResponsiveOverflowActions } from 'components/common/ResponsiveOverflowActions';
import langIds from 'lang/langIds';
import { selectCurrencyCode } from 'store/api/billingSettings/selectors';
import { formatDateRange } from 'util/date';
import { getClaimDetailsRoute } from 'util/routes';

import { AmountItemColumn } from '../common/AmountItemColumn';
import { DetailsColumnItem } from '../common/DetailsColumnItem';
import { IconContainer } from '../common/IconContainer';
import { ItemContainer } from '../common/ItemContainer';
import { ItemLayout } from '../common/ItemLayout';

interface IClaimsListProps extends StackProps {
	claim: IInsuranceClaimReference;
	contactId: string;
	hasInvoicesEditPermission?: boolean;
	disableActions?: boolean;
}

const ClaimItem = ({ claim, contactId, hasInvoicesEditPermission, disableActions, ...rest }: IClaimsListProps) => {
	const { $t } = useIntl();
	const navigate = useNavigate();
	const currencyCode = useSelector(selectCurrencyCode) || 'USD';

	const [baseOverflowAnchorEl, setBaseOverflowAnchorEl] = useState<HTMLDivElement | null>(null);
	const getClaimOverflowActions = useClaimOverflowActions();
	const actions = useMemo(
		() =>
			getClaimOverflowActions({
				contactId,
				hasInvoicesEditPermission,
				claim: {
					id: claim.id,
					claimStatus: claim.status,
					type: claim.type,
					date: claim.fromDate || '',
					number: claim.number,
					submissionMethod: claim.submissionMethod,
				},
			}),
		[
			claim.fromDate,
			claim.id,
			claim.number,
			claim.status,
			claim.type,
			contactId,
			getClaimOverflowActions,
			hasInvoicesEditPermission,
			claim.submissionMethod,
		]
	);
	const { number, status, fromDate, toDate, amount } = claim;

	const formattedServiceDates = useMemo(() => {
		const dateRange: Date[] = [];
		if (!!fromDate) {
			dateRange.push(new Date(fromDate));
		}
		if (toDate) {
			dateRange.push(new Date(toDate));
		}
		return formatDateRange(dateRange);
	}, [fromDate, toDate]);

	const handleViewClaim = () => {
		navigate(getClaimDetailsRoute(contactId, claim.type, claim.id));
	};

	return (
		<ItemContainer data-testid={`claim-item-${claim.id}`}>
			<ItemLayout
				onMouseEnter={(e) => setBaseOverflowAnchorEl(e.currentTarget)}
				onMouseLeave={() => setBaseOverflowAnchorEl(null)}
				dateColumn={
					<IconContainer>
						<ShieldRoundedIcon color='default' />
					</IconContainer>
				}
				detailsColumn={
					<DetailsColumnItem
						title={$t({ id: langIds.Claim })}
						date={fromDate}
						status={status}
						description={formattedServiceDates}
						detailNumber={number}
						onDetailNumberClick={handleViewClaim}
					/>
				}
				amountColumn={<AmountItemColumn price={amount} currencyCode={currencyCode} />}
				hideEmptyActions={!!disableActions}
				actionsColumn={
					!disableActions && (
						<ResponsiveOverflowActions
							anchorEl={baseOverflowAnchorEl}
							overflowActions={actions?.overflowActions ?? []}
							overflowSecondaryActions={actions?.overflowSecondaryActions ?? []}
						/>
					)
				}
				sx={{
					'&:hover': (theme) => ({
						backgroundColor: theme.palette.action.hover,
					}),
				}}
				{...rest}
			/>
		</ItemContainer>
	);
};

export default ClaimItem;
