import { createPath, createSearchParams } from 'react-router-dom';
import { providerHandlers, render, setupServerHandlers, within } from 'tests';

import { intl } from 'lang';

import { BillingFilterToolbar } from '../BillingFiltersToolbar';
import {
	BILLABLE_STATUS_OPTIONS,
	BILLING_TYPES_WITH_SEARCH_FILTER,
	CLAIM_STATUS_OPTIONS,
	INVOICE_STATUS_OPTIONS,
	PAYMENT_STATUS_OPTIONS,
	PAYMENT_TYPE_OPTIONS,
} from '../constants';
import { ClientBillingTab } from '../types';

const handlers = setupServerHandlers(providerHandlers.getProviders.handler());
handlers.run();

const renderBillingFilterToolbar = (searchParams?: Record<string, string>) => {
	const pathname = '/Client/:contactId/Billing';

	const router = createPath({ pathname, search: createSearchParams(searchParams).toString() });

	return render(<BillingFilterToolbar />, {
		enableRouter: true,
		routerType: 'memory',
		memoryRouterProp: {
			initialEntries: [router],
		},
	});
};

describe('BillingFilterToolbar', () => {
	it('should render the correct payment type filter options', async () => {
		const { getByRole, user } = renderBillingFilterToolbar({ activeTab: 'payments' });

		const allFilterButton = getByRole('button', { name: /All types filter button/i });
		await user.click(allFilterButton);

		const popover = getByRole('presentation');

		for (const option of PAYMENT_TYPE_OPTIONS.filter((opt) => !opt.isInsuranceModule)) {
			const optionEl = within(popover).getByText(
				'labelId' in option ? intl.$t({ id: option.labelId }) : option.label
			);
			expect(optionEl).toBeInTheDocument();
		}
	});

	it.each([
		{ activeTab: 'billables', options: BILLABLE_STATUS_OPTIONS },
		{ activeTab: 'payments', options: PAYMENT_STATUS_OPTIONS },
		{ activeTab: 'claims', options: CLAIM_STATUS_OPTIONS },
		{ activeTab: 'invoices', options: INVOICE_STATUS_OPTIONS },
	])(
		'should render the correct status filter options depending on the active tab',
		async ({ activeTab, options }) => {
			const { getByRole, user } = renderBillingFilterToolbar({ activeTab });

			const allFilterButton = getByRole('button', { name: /All statuses filter button/i });
			await user.click(allFilterButton);

			const popover = getByRole('presentation');

			for (const option of options) {
				const optionEl = within(popover).getByText(intl.$t({ id: option.labelId }));
				expect(optionEl).toBeInTheDocument();
			}
		}
	);

	it('should not render the filter by type button if theres no options preset', () => {
		const { queryByRole } = renderBillingFilterToolbar({ activeTab: 'superbills' });

		const allFilterButton = queryByRole('button', { name: /All filter button/i });

		expect(allFilterButton).not.toBeInTheDocument();
	});

	it.each(['billables', 'payments', 'claims', 'invoices', 'superbills'] as ClientBillingTab[])(
		'should render the searchbar if it supports it',
		(activeTab) => {
			const { queryByPlaceholderText } = renderBillingFilterToolbar({ activeTab });

			const hasSearchbar = BILLING_TYPES_WITH_SEARCH_FILTER.includes(activeTab);

			const searchbar = queryByPlaceholderText('Search items');

			if (hasSearchbar) {
				expect(searchbar).toBeInTheDocument();
			} else {
				expect(searchbar).not.toBeInTheDocument();
			}
		}
	);
});
