import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AppStateBuilder, configureStoreForTesting, PaymentBuilder, renderHook } from 'tests';

import AppProviders from 'components/AppProviders';
import ModalsRoot from 'components/modals';

import { usePaymentOverflowActions } from '../hooks';

const preloadedState = new AppStateBuilder().withFeatureFlags(['payments-and-billing']).build();
const paymentBuilder = new PaymentBuilder();
const { payment } = paymentBuilder.build();

const wrapper = ({ children }) => (
	<BrowserRouter>
		<AppProviders store={configureStoreForTesting({ preloadedState })}>
			<ModalsRoot />
			{children}
		</AppProviders>
	</BrowserRouter>
);

const renderPaymentOverflowHook = () => {
	return renderHook(usePaymentOverflowActions, { wrapper });
};

describe('usePaymentOverflowActions', () => {
	it.each(['viewReceipt', 'refund'])('should return the primary overflow actions', (actionKey) => {
		const { result } = renderPaymentOverflowHook();
		const hasAction = result
			.current({ payment, hasInvoicesEditPermission: true })
			.primaryOverflowActions.some((e) => e.key === actionKey);
		expect(hasAction).toBe(true);
	});
	it('should render the delete action', () => {
		const { result } = renderPaymentOverflowHook();

		const noDeleteActionResult = result
			.current({ payment: { ...payment, canDelete: false }, hasInvoicesEditPermission: true })
			.secondaryOverflowActions.some((e) => e.key === 'delete');

		expect(noDeleteActionResult).toBe(false);

		const hasDeleteActionResult = result
			.current({ payment: { ...payment, canDelete: true }, hasInvoicesEditPermission: true })
			.secondaryOverflowActions.some((e) => e.key === 'delete');

		expect(hasDeleteActionResult).toBe(true);
	});
	it('should not render the refund action when payment type or payer type is insurance', () => {
		const { result } = renderPaymentOverflowHook();
		const hasAction = result
			.current({ payment: { ...payment, type: 'Insurance' }, hasInvoicesEditPermission: true })
			.primaryOverflowActions.some((e) => e.key === 'refund');

		const hasAction2 = result
			.current({ payment: { ...payment, payerType: 'Insurance' }, hasInvoicesEditPermission: true })
			.primaryOverflowActions.some((e) => e.key === 'refund');

		expect(hasAction).toBe(false);
		expect(hasAction2).toBe(false);
	});
});
