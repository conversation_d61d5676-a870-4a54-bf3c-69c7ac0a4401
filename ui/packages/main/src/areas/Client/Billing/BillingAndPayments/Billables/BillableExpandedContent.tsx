import { CreditBanner } from '../common/CreditBanner';
import { LinkedClaim } from '../common/LinkedClaim';
import { LinkedInvoice } from '../common/LinkedInvoice';

import { BillableUnchargedAmountBanner } from './BillableUnchargedBanner';
import { BillableUnclaimedBanner } from './BillableUnclaimedBanner';
import { BillableWriteOffBanner } from './BillableWriteOffBannerRow';

export interface IBillableExpandedContentProps extends IBillableEntry {
	hasInvoicesEditPermission: boolean;
}

export const BillableExpandedContent = (props: IBillableExpandedContentProps) => {
	const {
		providerId,
		contactId,
		date,
		invoices,
		issuedCredits,
		writeOff,
		writeOffReason,
		uncharged,
		currencyCode,
		unclaimed,
		hasInvoicesEditPermission,
		claims,
	} = props;

	// Note: make sure to update isExandable in BillableListItem if adding/removing components
	return (
		<>
			{issuedCredits?.map((credit) => {
				return (
					<CreditBanner
						key={credit.id}
						amount={credit.amount}
						currencyCode={credit.currencyCode}
						description={credit.description}
						issuedDateUtc={credit.issuedDateUtc}
						transactionDate={date}
					/>
				);
			})}
			{!!uncharged && <BillableUnchargedAmountBanner {...props} />}
			{!!unclaimed && <BillableUnclaimedBanner {...props} />}
			{!!writeOff && (
				<BillableWriteOffBanner
					writeOff={writeOff}
					currencyCode={currencyCode}
					writeOffReason={writeOffReason}
				/>
			)}
			{invoices?.map((invoice) => (
				<LinkedInvoice
					key={invoice.id}
					contactId={contactId}
					providerId={providerId}
					hasInvoicesEditPermission={hasInvoicesEditPermission}
					{...invoice}
				/>
			))}
			{claims?.map((claim) => (
				<LinkedClaim
					key={claim.id}
					claim={claim}
					contactId={contactId}
					providerId={providerId}
					hasInvoicesEditPermission={hasInvoicesEditPermission}
				/>
			))}
		</>
	);
};
