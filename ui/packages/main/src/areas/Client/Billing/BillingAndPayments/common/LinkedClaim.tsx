import { useMemo, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import ShieldRoundedIcon from '@mui/icons-material/ShieldRounded';
import { Box, Link, Stack, Typography } from '@mui/material';

import { useClaimOverflowActions } from 'areas/Insurance/Claims/hooks/useClaimOverflowActions';
import { ResponsiveOverflowActions } from 'components/common/ResponsiveOverflowActions';
import StatusTag from 'components/StatusTag';
import langIds from 'lang/langIds';
import { localizeDate } from 'util/date';
import { getClaimDetailsRoute } from 'util/routes';

import { LinkedItemProps } from '../types';

import { ItemLayout } from './ItemLayout';

type Props = LinkedItemProps & {
	claim: IClaimReference;
};

export const LinkedClaim = ({ claim, contactId, hasInvoicesEditPermission }: Props) => {
	const { id, type, claimStatus, number, date } = claim;
	const [baseOverflowAnchorEl, setBaseOverflowAnchorEl] = useState<HTMLDivElement | null>(null);

	const getClaimOverflowActions = useClaimOverflowActions();
	const actions = useMemo(
		() =>
			getClaimOverflowActions({
				contactId,
				claim,
				hasInvoicesEditPermission,
			}),
		[claim, contactId, getClaimOverflowActions, hasInvoicesEditPermission]
	);

	return (
		<ItemLayout
			onMouseEnter={(e) => setBaseOverflowAnchorEl(e.currentTarget)}
			onMouseLeave={() => setBaseOverflowAnchorEl(null)}
			dateColumn={!!date ? <Typography variant='body2'>{localizeDate(date, 'noYear')}</Typography> : undefined}
			detailsColumn={
				<Stack spacing={1} direction='row' alignItems='center'>
					<ShieldRoundedIcon color='default' sx={{ width: 16 }} />
					<Typography
						variant='body2'
						sx={{
							fontWeight: (theme) => theme.typography.fontWeightBold,
							lineHeight: (theme) => theme.typography.body2.fontSize,
							alignItems: 'center',
							display: 'flex',
						}}
					>
						<Box component='span' sx={{ fontWeight: 'bold', mr: 0.5 }}>
							<FormattedMessage id={langIds.Claim} />
						</Box>
						<Link href={getClaimDetailsRoute(contactId, type, id)}>{number}</Link>
					</Typography>
					<StatusTag status={claimStatus} size='extraSmall' />
				</Stack>
			}
			amountColumn={null}
			actionsColumn={
				<ResponsiveOverflowActions
					key='base-overflow-actions'
					anchorEl={baseOverflowAnchorEl}
					overflowActions={actions?.overflowActions ?? []}
					overflowSecondaryActions={actions?.overflowSecondaryActions ?? []}
				/>
			}
			sx={{
				cursor: 'pointer',
				'&:hover': (theme) => ({
					backgroundColor: theme.palette.action.hover,
				}),
			}}
		/>
	);
};
