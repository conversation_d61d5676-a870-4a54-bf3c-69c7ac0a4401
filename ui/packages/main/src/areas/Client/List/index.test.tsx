import { produce } from 'immer';
import {
	App<PERSON><PERSON><PERSON><PERSON><PERSON>,
	ContactBuilder,
	contactHandlers,
	contacts,
	fireEvent,
	getDataCellInRowByHeaderText,
	getRowByColumn,
	mockStatusProperty,
	onboardingHandlers,
	personHandlers,
	render,
	routerState,
	schemaHandlers,
	screen,
	setupServerHandlers,
	tagHandlers,
	templateHandlers,
	waitFor,
	within,
} from 'tests';
import OptionSetValueBuilder from 'tests/api-mocks/builders/entities/OptionSetValueBuilder';
import { billingHandlers } from 'tests/api-mocks/handlers/billing';
import { contactImportHandlers } from 'tests/api-mocks/handlers/contactImport';
import { permissionsHandlers } from 'tests/api-mocks/handlers/permissions';
import { providerHandlers } from 'tests/api-mocks/handlers/provider';
import testIds from 'tests/testIds';

import { Header } from 'components/Header';
import ModalsRoot from 'components/modals';
import { RenderArrayValue } from 'components/SchemaForms/FormControl/Controls/OptionSetPropertyControl/OptionSetPropertyView';
import SnackbarRoot from 'components/snackbar';
import { intl } from 'lang';
import langIds from 'lang/langIds';
import { ClientDuplicateResult } from 'store/api/contacts';
import { toFullName } from 'util/person';

import { getClientColumnValue } from './util/getClientColumnValue';
import ClientsTable from '.';

const preloadedState = new AppStateBuilder().build();

const serverHandlers = setupServerHandlers(
	contactHandlers.getContacts.handler((_, res, ctx) => {
		const mockContacts: IContactDetail[] = [contacts[0], contacts[1], contacts[2]];

		const paginatedContactsResult = produce(contactHandlers.getContacts.response, (draft) => {
			draft.items = mockContacts;
			draft.totalCount = mockContacts.length;
			draft.pagination.limit = mockContacts.length;
		});

		return res(ctx.status(200), ctx.json(paginatedContactsResult));
	}),
	contactHandlers.getContacts.handler(),
	contactHandlers.bulkUpdateContactStatus.handler(),
	contactHandlers.bulkDeleteContacts.handler(),
	contactHandlers.assignStaffToContacts.handler(),
	contactHandlers.addTagsToContacts.handler(),
	contactHandlers.createContact.handler(),
	contactHandlers.checkClientDuplicate.handler(),
	contactHandlers.getContact.handler(),
	tagHandlers.fetchTags.handler(),
	personHandlers.getCaregivers.handler(),
	schemaHandlers.getSchema.handler(),
	schemaHandlers.getSchemaLayout.handler(),
	onboardingHandlers.getOnboarding.handler(),
	contactImportHandlers.getContactImportSummary.handler(),
	templateHandlers.fetchTemplates.handler(),
	contactHandlers.getContact.handler(),
	permissionsHandlers.getStaffPermissions.handler(),
	billingHandlers.getBillingAccount.handler(),
	providerHandlers.getProviders.handler(),
	schemaHandlers.getSchemaField.handler((_, res, ctx) => res(ctx.status(200), ctx.json(mockStatusProperty)))
);

serverHandlers.run();

jest.useRealTimers();

// This is the column in the Clients table where the user can select/unselect a row through checkbox and do some actions like
// updating the status, assigning staff, etc. The associated data cells of the column are using the `contact.id` as the accessible name.
const CONTACT_ROW_SELECT_ACTION_COLUMN = 'select-all-clients';

describe('Client row', () => {
	xit('should update the status of a client', async () => {
		const { user } = render(
			<>
				<ClientsTable />
				<SnackbarRoot />
			</>,
			{ preloadedState }
		);
		const clientsTable = await screen.findByRole('table', {
			name: intl.formatMessage({ id: langIds.ClientsTable }),
		});

		const { items: contacts } = contactHandlers.getContacts.response;
		const contact = contacts[0];

		const { selectedContactRowActions, contactRow } = await selectContactRowAndShowRowsBulkActions({
			clientsTable,
			user,
			contact,
		});
		const statusDataCell = getDataCellInRowByHeaderText(clientsTable, contactRow, /Status/i);

		const updateStatusButton = within(selectedContactRowActions).getByRole('button', {
			name: intl.formatMessage({ id: langIds.UpdateStatus }),
		});

		await user.click(updateStatusButton);

		const updateStatusDialog = screen.getByRole('dialog', {
			name: new RegExp(intl.formatMessage({ id: langIds.UpdateStatus }), 'i'),
		});

		// Clicking update status button will show a form for updating the status.
		const updateStatusSelector = within(updateStatusDialog).getByRole('combobox', {
			name: new RegExp(intl.formatMessage({ id: langIds.ClientTableStatus }), 'i'),
		});

		// By default, the update status selector is Active.
		expect(updateStatusSelector).toHaveValue('Active');

		// Update to make it inactive.
		await user.click(updateStatusSelector);
		const inactiveOption = screen.getByLabelText('Inactive');
		await user.click(inactiveOption);

		// Save to update the status to "Inactive".
		await user.click(
			within(updateStatusDialog).getByRole('button', {
				name: intl.formatMessage({ id: langIds.Save }),
			})
		);

		// If successfull, the status of the contact should be updated to "Inactive" and an element that shows a success message should be visible.
		// expect(statusDataCell).toHaveTextContent('Inactive');
		expect(statusDataCell).toBeInTheDocument();
	});

	test.skip('should delete a client', async () => {
		const { user, findByRole } = render(
			<>
				<ClientsTable />
				<SnackbarRoot />
			</>,
			{
				enableRouter: true,
				preloadedState: new AppStateBuilder()
					.withFeatureFlags()
					.produce((draft) => {
						draft.router = routerState;
						draft.router.location.pathname = `Clients`;
					})
					.build(),
			}
		);
		const clientsTable = await findByRole('table', {
			name: intl.formatMessage({ id: langIds.ClientsTable }),
		});

		const { items: contacts } = contactHandlers.getContacts.response;
		const contact = contacts[0];

		const { selectedContactRowActions } = await selectContactRowAndShowRowsBulkActions({
			clientsTable,
			user,
			contact,
		});

		expect(within(selectedContactRowActions).getByText(/1 Selected/i)).toBeInTheDocument();

		const deleteClientsButton = within(selectedContactRowActions).getByRole('button', {
			name: intl.formatMessage({ id: langIds.DeleteClients }),
		});

		// Clicking delete clients button will show a form for deleting clients.
		await user.click(deleteClientsButton);

		// Show moved to trash snackbar directly after deleting a client
		const snackbar = await screen.findByText(
			intl.formatMessage(
				{ id: langIds.TrashSuccessfullyDeletedItem },
				{
					type: intl.formatMessage({ id: langIds.Client }).toLowerCase(),
				}
			)
		);
		expect(snackbar).toBeInTheDocument();
	});

	test.skip('should assign a staff to a client', async () => {
		const { user } = render(
			<>
				<ClientsTable />
				<SnackbarRoot />
			</>
		);
		const clientsTable = await screen.findByRole('table', {
			name: intl.formatMessage({ id: langIds.ClientsTable }),
		});
		const { items: contacts } = contactHandlers.getContacts.response;
		const contact = contacts[0];
		const { items: staffMembers } = personHandlers.getCaregivers.response;
		const staffMember = staffMembers[1];
		const staffMemberFullName = toFullName(staffMember.firstName, staffMember.lastName);

		const { selectedContactRowActions, contactRow } = await selectContactRowAndShowRowsBulkActions({
			clientsTable,
			user,
			contact: contact,
		});

		// Clicking assign staff button will show a form for assigning staff.
		const assignStaffButton = within(selectedContactRowActions).getByRole('button', {
			name: new RegExp(intl.formatMessage({ id: langIds.AssignTeam }), 'i'),
		});

		await user.click(assignStaffButton);

		const assignStaffDialog = screen.getByRole('dialog', {
			name: new RegExp(intl.formatMessage({ id: langIds.AssignTeam }), 'i'),
		});
		const assignStaffCombobox = within(assignStaffDialog).getByRole('combobox', {
			name: new RegExp(intl.formatMessage({ id: langIds.AssignTeamMember }), 'i'),
		});

		// Clicking assign staff combobox shows staff member options inside listbox.
		await user.click(assignStaffCombobox);
		const listbox = screen.getByRole('listbox', {
			name: new RegExp(intl.formatMessage({ id: langIds.AssignTeamMember }), 'i'),
		});

		// Choose the staff member and save.
		await user.click(
			within(listbox).getByRole('option', {
				name: new RegExp(staffMemberFullName, 'i'),
			})
		);
		await user.click(
			within(assignStaffDialog).getByRole('button', {
				name: intl.formatMessage({ id: langIds.Save }),
			})
		);

		// Now staff is added on the assigned staff column of the selected contact row.
		expect(
			within(getDataCellInRowByHeaderText(clientsTable, contactRow, /Assigned Staff/i)).getByLabelText(
				staffMemberFullName
			)
		).toBeInTheDocument();
		// Shows a success message.
		expect(
			screen.getByText(intl.formatMessage({ id: langIds.ClientBulkStaffAssignedSuccessSnackbar }, { count: 1 }))
		).toBeInTheDocument();
	});

	test.skip('should add tags', async () => {
		const { user } = render(
			<>
				<ClientsTable />
				<SnackbarRoot />
			</>
		);
		const clientsTable = await screen.findByRole('table', {
			name: intl.formatMessage({ id: langIds.ClientsTable }),
		});
		const { items: contacts } = contactHandlers.getContacts.response;
		const { items: tags } = tagHandlers.fetchTags.response;
		const tag = tags[1];

		const { selectedContactRowActions, contactRow } = await selectContactRowAndShowRowsBulkActions({
			clientsTable,
			user,
			contact: contacts[0],
		});
		let tagsDataCell = getDataCellInRowByHeaderText(clientsTable, contactRow, /Tags/i);

		// tag is not yet assigned to the contact.
		expect(within(tagsDataCell).queryByText(tag.title)).not.toBeInTheDocument();

		// Clicking add tags button will show a form for adding tags.
		const addTagsButton = within(selectedContactRowActions).getByRole('button', {
			name: new RegExp(intl.formatMessage({ id: langIds.AddTags }), 'i'),
		});

		await user.click(addTagsButton);

		const addTagsDialog = screen.getByRole('dialog', {
			name: new RegExp(intl.formatMessage({ id: langIds.AddTags }), 'i'),
		});
		const addTagsCombobox = within(addTagsDialog).getByRole('combobox', {
			name: new RegExp(intl.formatMessage({ id: langIds.Tags }), 'i'),
		});

		// Clicking tags combobox shows tag options inside listbox.
		await user.click(addTagsCombobox);
		const listbox = screen.getByRole('listbox');

		// Choose the tag and save.
		await user.click(
			within(listbox).getByRole('option', {
				name: new RegExp(tag.title, 'i'),
			})
		);
		// Save the chosen tags
		await user.click(within(addTagsDialog).getByRole('button', { name: intl.formatMessage({ id: langIds.Save }) }));

		// Now tag is added on the tags column of the selected contact row.
		tagsDataCell = getDataCellInRowByHeaderText(clientsTable, contactRow, /Tags/i);
		expect(within(tagsDataCell).getByText(tag.title)).toBeInTheDocument();
		// Shows a success message.
		expect(
			screen.getByText(intl.formatMessage({ id: langIds.ClientBulkTagsAddedSuccessSnackbar }))
		).toBeInTheDocument();
	});
});

describe('Clients table modals', () => {
	it("should open (and be able to close) the 'CreateClient' modal when the 'New client' button is clicked", async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();
		const { user } = render(
			<>
				<ModalsRoot />
				<Header />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act - click the 'New client' button
		const newClientButton = await screen.findByRole('button', {
			name: intl.formatMessage({ id: langIds.ClientListCreateButton }),
		});
		await user.click(newClientButton);

		// Assert - verify that the 'CreateClient' modal is open
		const createClientModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.CreateClientModalTitle }),
		});
		expect(createClientModal).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert - verify that the 'CreateClient' modal is closed
		expect(createClientModal).not.toBeInTheDocument();
	});

	it("should close the 'CreateClient' modal upon submitting a valid create client form", async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();
		const { user } = render(
			<>
				<ModalsRoot />
				<Header />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act - click the 'New client' button
		const newClientButton = screen.getByRole('button', {
			name: intl.formatMessage({ id: langIds.ClientListCreateButton }),
		});
		await user.click(newClientButton);

		// Assert - verify that the 'CreateClient' modal is open
		const createClientModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.CreateClientModalTitle }),
		});
		expect(createClientModal).toBeInTheDocument();

		// Act - type a valid first & last name and then submit the form
		const firstNameInput = screen.getByRole('textbox', {
			name: intl.formatMessage({ id: langIds.FirstName }),
		});
		const mockedFirstName = 'Kent C';
		await user.type(firstNameInput, mockedFirstName);
		const lastNameInput = screen.getByRole('textbox', {
			name: intl.formatMessage({ id: langIds.LastName }),
		});
		const mockedLastName = 'Strait';
		await user.type(lastNameInput, mockedLastName);
		const createNewClientButton = screen.getByRole('button', {
			name: intl.formatMessage({ id: langIds.Create }),
		});
		await user.click(createNewClientButton);

		// Assert - verify that the 'CreateClient' modal is closed by checking for the next modal that appears
		const nextStepsModal = await screen.getByTestId('NewClientNextStepsModal');
		expect(nextStepsModal).toBeInTheDocument();
	});

	it('should open the `PotentialClientDuplicateModal` modal when there is a conflict', async () => {
		// Arrange

		const mockedDuplicateClients: ClientDuplicateResult['clients'] = [
			{
				id: '1234567890',
				email: '<EMAIL>',
				firstName: 'Kent C',
				lastName: 'Strait',
				fullName: 'Kent C Strait',
				isAssigned: false,
				isClient: true,
			},
		];

		serverHandlers.server.use(
			contactHandlers.checkClientDuplicate.handler(async (_, res, ctx) =>
				res(
					ctx.status(200),
					ctx.json({
						conflict: true,
						clients: mockedDuplicateClients,
					})
				)
			)
		);

		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { user } = render(
			<>
				<ModalsRoot />
				<Header />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act - click the 'New client' button
		const newClientButton = screen.getByRole('button', {
			name: intl.formatMessage({ id: langIds.ClientListCreateButton }),
		});
		await user.click(newClientButton);

		// Assert - verify that the 'CreateClient' modal is open
		const createClientModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.CreateClientModalTitle }),
		});
		expect(createClientModal).toBeInTheDocument();

		// Act - type a valid first & last name and then submit the form
		const firstNameInput = screen.getByRole('textbox', {
			name: intl.formatMessage({ id: langIds.FirstName }),
		});
		await user.type(firstNameInput, mockedDuplicateClients[0].firstName);
		const lastNameInput = screen.getByRole('textbox', {
			name: intl.formatMessage({ id: langIds.LastName }),
		});
		await user.type(lastNameInput, mockedDuplicateClients[0].lastName);
		const emailInput = screen.getByRole('textbox', {
			name: intl.formatMessage({ id: langIds.Email }),
		});
		await user.type(emailInput, mockedDuplicateClients[0].email);
		const createNewClientButton = screen.getByRole('button', {
			name: intl.formatMessage({ id: langIds.Create }),
		});
		await user.click(createNewClientButton);

		await waitFor(() => screen.findByRole('dialog'));
		const dialog = screen.getByRole('dialog');

		expect(dialog).toBeInTheDocument();

		const nextModalPotentialClientDuplicate = await within(dialog).findByText(
			intl.formatMessage({ id: langIds.PotentialClientDuplicateTitle })
		);

		// Assert - verify that the 'CreateClient' modal is closed by checking for the next modal that appears
		expect(nextModalPotentialClientDuplicate).toBeInTheDocument();
	});

	it('should open (and be able to close) "AssignStaff" modal when clicking "Assign staff" button in Clients > client row > overflow actions', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { items: MOCKED_ITEMS } = contactHandlers.getContacts.response;

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const firstClient = MOCKED_ITEMS[0];
		const firstClientName = toFullName(firstClient.firstName, firstClient.lastName);
		const clientRowItem = await screen.findByText(firstClientName);
		await user.hover(clientRowItem);

		const overflowActionsPopover = await screen.findByTestId(testIds.OverflowActionsPopper);
		const overflowActionsMoreButton = await within(overflowActionsPopover).findByRole('button', {
			name: 'more',
		});
		fireEvent.click(overflowActionsMoreButton);

		const assignStaffButton = await screen.findByRole('menuitem', {
			name: intl.formatMessage({ id: langIds.AssignTeam }),
		});
		fireEvent.click(assignStaffButton);

		// Assert - verify that the 'CreateClient' modal is open
		const assignStaffModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.AssignTeam }),
		});
		expect(assignStaffModal).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert - verify that the 'CreateClient' modal is closed
		expect(assignStaffModal).not.toBeInTheDocument();
	});

	it('should open (and be able to close) "EditClientTags" modal when clicking "Edit tags" button in Clients > client row > hover overflow actions', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { items: MOCKED_ITEMS } = contactHandlers.getContacts.response;

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const firstClient = MOCKED_ITEMS[0];
		const firstClientName = toFullName(firstClient.firstName, firstClient.lastName);
		const clientRowItem = await screen.findByText(firstClientName);
		await user.hover(clientRowItem);

		const overflowActionsPopover = await screen.findByTestId(testIds.OverflowActionsPopper);
		const editTagsIconButton = await within(overflowActionsPopover).findByRole('button', {
			name: intl.formatMessage({ id: langIds.EditTags }),
		});
		fireEvent.click(editTagsIconButton);

		// Assert - verify that the 'CreateClient' modal is open
		const editClientTagsModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.EditTags }),
		});
		expect(editClientTagsModal).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert
		expect(editClientTagsModal).not.toBeInTheDocument();
	});

	it.skip('should open (and be able to close) ManageTags modal when clicking "Manage tags" button in tags filter under search and filter toolbar', async () => {
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		const tagsFilterButton = await screen.findByLabelText(
			`${intl.formatMessage({ id: langIds.Tags })} filter button`
		);

		// Click the tags filter button to open the popover
		await user.click(tagsFilterButton);

		// In status filter, there should be a manage statuses button in the footer
		const manageTagsButton = await screen.findByRole('button', {
			name: intl.formatMessage({ id: langIds.ManageTags }),
		});

		// Click the manage tags button to open the manage tags modal
		await user.click(manageTagsButton);

		// Act
		const editTagsDialog = screen.getByRole('dialog', {
			name: new RegExp(intl.formatMessage({ id: langIds.ManageTags }), 'i'),
		});

		// Assert
		expect(editTagsDialog).toBeInTheDocument();

		// Act
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert
		expect(editTagsDialog).not.toBeInTheDocument();
	});

	it.skip('should open (and be able to close) ManageTags modal when clicking Manage tags button in Clients > on a row > hover overflow actions > Edit tags icon button > under Tags dropdown footer', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { items: MOCKED_ITEMS } = contactHandlers.getContacts.response;

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const firstClient = MOCKED_ITEMS[0];
		const firstClientName = toFullName(firstClient.firstName, firstClient.lastName);
		const clientRowItem = await screen.findByText(firstClientName);
		await user.hover(clientRowItem);

		const overflowActionsPopover = await screen.findByTestId(testIds.OverflowActionsPopper);
		const editTagsIconButton = await within(overflowActionsPopover).findByRole('button', {
			name: intl.formatMessage({ id: langIds.EditTags }),
		});
		fireEvent.click(editTagsIconButton);

		// Assert
		const editClientTagsModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.EditTags }),
		});
		expect(editClientTagsModal).toBeInTheDocument();

		// Act
		// Activate tags input
		const tagsAutocompleteInput = await screen.findByRole('combobox', {
			name: intl.formatMessage({ id: langIds.Tags }),
		});
		await user.click(tagsAutocompleteInput);

		// Click "Manage tags" on the footer of tags input
		const manageTagsButton = await screen.findByRole('button', {
			name: intl.formatMessage({ id: langIds.ManageTags }),
		});
		await user.click(manageTagsButton);

		// Assert
		const manageTagsModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.ManageTags }),
		});
		expect(manageTagsModal).toBeInTheDocument();

		// Act
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert
		expect(editClientTagsModal).not.toBeInTheDocument();
	});

	it('should open (and be able to close) SendClientIntake modal when Send intake button on hoverable overflow actions tooltip button in Clients > client row', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { items: MOCKED_ITEMS } = contactHandlers.getContacts.response;

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const firstClient = MOCKED_ITEMS[0];
		const firstClientName = toFullName(firstClient.firstName, firstClient.lastName);
		const clientRowItem = await screen.findByText(firstClientName);
		await user.hover(clientRowItem);

		const overflowActionsPopover = await screen.findByTestId(testIds.OverflowActionsPopper);
		const sendIntakeIconButton = await within(overflowActionsPopover).findByRole('button', {
			name: intl.formatMessage({ id: langIds.SendIntake }),
		});
		fireEvent.click(sendIntakeIconButton);

		// Assert - verify that the 'CreateClient' modal is open
		const sendIntakeModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.ClientIntakeModalTitle }, { name: firstClient.firstName }),
		});
		expect(sendIntakeModal).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert - verify that the 'CreateClient' modal is closed
		expect(sendIntakeModal).not.toBeInTheDocument();
	});

	it('should open (and be able to close) InviteToPortal modal when clicking Invite to portal button on a client row', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		const { items: MOCKED_ITEMS } = contactHandlers.getContacts.response;

		const { user } = render(
			<>
				<ModalsRoot />
				<ClientsTable />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const firstClient = MOCKED_ITEMS[0];
		const firstClientName = toFullName(firstClient.firstName, firstClient.lastName);
		const clientRowItem = await screen.findByText(firstClientName);
		await user.hover(clientRowItem);

		const overflowActionsPopover = await screen.findByTestId(testIds.OverflowActionsPopper);
		const inviteToPortalIconButton = await within(overflowActionsPopover).findByRole('button', {
			name: intl.formatMessage({ id: langIds.InviteToPortal }),
		});
		fireEvent.click(inviteToPortalIconButton);

		// Assert - verify that the 'CreateClient' modal is open
		const inviteToPortalModal = screen.queryByRole('dialog', {
			name: intl.formatMessage({ id: langIds.InviteToPortalModalTitle }, { name: firstClient.firstName }),
		});
		expect(inviteToPortalModal).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert - verify that the 'CreateClient' modal is closed
		expect(inviteToPortalModal).not.toBeInTheDocument();
	});

	it('should open (and be able to close) ImportClients modal when clicking Import button in Clients > header', async () => {
		// Arrange
		const preloadedState = new AppStateBuilder()
			.produce((draft) => {
				draft.router = routerState;
				draft.router.location.pathname = `Clients`;
			})
			.build();

		serverHandlers.server.use(
			contactImportHandlers.getContactImportSummary.handler(async (_, res, ctx) => {
				return res(ctx.status(200), ctx.json({ items: [] }));
			})
		);

		const { user } = render(
			<>
				<ModalsRoot />
				<Header />
			</>,
			{
				enableRouter: true,
				preloadedState,
			}
		);

		// Act
		const importButton = await screen.findByRole('button', {
			name: intl.formatMessage({ id: langIds.Import }),
		});
		await user.click(importButton);

		// Assert
		const importClientsModalHeading = screen.queryByRole('heading', {
			name: intl.formatMessage({ id: langIds.ImportClients }),
		});
		expect(importClientsModalHeading).toBeInTheDocument();

		// Act - close the modal by clicking the close icon
		const closeIconButton = await screen.findByRole('button', { name: 'close' });
		await user.click(closeIconButton);

		// Assert
		expect(importClientsModalHeading).not.toBeInTheDocument();
	});
});

describe('getColumnLabel', () => {
	const customFields = {
		'Floor / Room': '500',
		'Operation Date': '2024-03-05T16:00:00Z',
		'Vacation Date': {
			endDate: '2024-03-03T04:00:00Z',
			startDate: '2024-03-01T04:00:00Z',
		},
		Fruits: [
			new OptionSetValueBuilder({ orderIndex: 0 }).build(),
			new OptionSetValueBuilder({ orderIndex: 1 }).build(),
		],
		Fruit: new OptionSetValueBuilder({ colorHex: '' }).build(),
		'Colored Fruit': new OptionSetValueBuilder().build(),
	};
	const contact = new ContactBuilder({ fields: customFields }).build();
	const contactDetails: IContactDetail = { ...contact, relationshipAccessType: 'Careprovider_Admin' };

	it('should return the correct numeric column value', () => {
		const floorOrRoomValue = getClientColumnValue({
			key: 'Floor / Room',
			client: contactDetails,
			customColumnValue: {},
		});
		expect(floorOrRoomValue).toBe('500');
	});

	it('should return the correct date column value', () => {
		const operationDateValue = getClientColumnValue({
			key: 'Operation Date',
			client: contactDetails,
			customColumnValue: {},
		});
		expect(operationDateValue).toBe('03/05/2024');
	});

	it('should return the correct date range column value', () => {
		const vacationDateValue = getClientColumnValue({
			key: 'Vacation Date',
			client: contactDetails,
			customColumnValue: {},
		});
		expect(vacationDateValue).toBe('1 - 3 Mar 2024');
	});

	it('should return the correct multiple option sets column value', () => {
		const fruitsValue = getClientColumnValue({ key: 'Fruits', client: contactDetails, customColumnValue: {} });
		expect(fruitsValue).toStrictEqual(<RenderArrayValue value={customFields.Fruits} />);
	});

	it('should return the correct single option set column value', () => {
		const fruitValue = getClientColumnValue({ key: 'Fruit', client: contactDetails, customColumnValue: {} });
		expect(fruitValue).toStrictEqual(customFields['Fruit'].displayName);
	});

	it('should return the correct single option set with colorhex column value', () => {
		const fruitWithColorValue = getClientColumnValue({
			key: 'Colored Fruit',
			client: contactDetails,
			customColumnValue: {},
		});
		expect(fruitWithColorValue).toStrictEqual(<RenderArrayValue value={[customFields['Colored Fruit']]} />);
	});
});

/**
 * This selects the contact row based on the provided arguments. It will do an assertion on the process
 * of selecting a row. It returns elements including selected actions and contact row and other data
 * that are relavant to select contact row.
 * */
async function selectContactRowAndShowRowsBulkActions({
	clientsTable,
	user,
	contact,
}: {
	clientsTable: HTMLElement;
	user: any;
	contact: IContactDetail;
}) {
	// Query the row based on the first column of the table where the header name is empty. Columns
	// are set to id with checkbox as child element.
	const contactRow = getRowByColumn(clientsTable, [CONTACT_ROW_SELECT_ACTION_COLUMN, new RegExp(contact.id, 'i')]);
	const contactRowCheckbox = within(
		getDataCellInRowByHeaderText(clientsTable, contactRow, CONTACT_ROW_SELECT_ACTION_COLUMN)
	).getByRole('checkbox', {
		name: new RegExp(contact.id, 'i'),
	});

	// In default, the checkbox is unchecked and selectedContactRowActions is not visible
	expect(contactRowCheckbox).not.toBeChecked();
	expect(screen.queryByTestId('selected-contacts-actions')).not.toBeInTheDocument();

	await user.click(contactRowCheckbox);

	expect(contactRowCheckbox).toBeChecked();
	expect(screen.getByTestId('selected-contacts-actions')).toBeInTheDocument();

	return {
		selectedContactRowActions: screen.getByTestId('selected-contacts-actions'),
		contactRow,
		contactRowCheckbox,
	};
}
