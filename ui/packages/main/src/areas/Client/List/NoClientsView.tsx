import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import AddRounded from '@mui/icons-material/AddRounded';
import { Button } from '@mui/material';
import { ClientImportSummaryWrapper } from 'features/Client/components/ClientImportSummaryWrapper';

import { useBreakpoint } from '@carepatron/components';
import * as EmptyState from '@carepatron/components/src/EmptyStates';

import langIds from 'lang/langIds';
import { getWebsiteImageUrl } from 'util/getWebsiteImageUrl';

const imageUrl = getWebsiteImageUrl('no-clients-image.svg');

type NoClientsViewProps = {
	onCreateClient: () => void;
};

export const NoClientsView: FC<NoClientsViewProps> = ({ onCreateClient }) => {
	const belowSm = useBreakpoint('sm');

	return (
		<EmptyState.Root>
			<EmptyState.Image url={imageUrl} alt='No clients' />
			<EmptyState.Title>
				<FormattedMessage id={langIds.NoClientsHeading} />
			</EmptyState.Title>
			<EmptyState.Action>
				{/* Client import is not supported on mobile */}
				{!belowSm && (
					<ClientImportSummaryWrapper>
						{({ importButtonIcon, importButtonText, onImportClients }) => (
							<Button variant='outlined' startIcon={importButtonIcon} onClick={onImportClients}>
								{importButtonText}
							</Button>
						)}
					</ClientImportSummaryWrapper>
				)}
				<Button variant='contained' startIcon={<AddRounded />} onClick={onCreateClient}>
					<FormattedMessage id={langIds.NewClient} />
				</Button>
			</EmptyState.Action>
		</EmptyState.Root>
	);
};
