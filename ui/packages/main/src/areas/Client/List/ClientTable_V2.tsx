import { ChangeEvent, FC, SyntheticEvent, useEffect, useRef, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { batch, useDispatch } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import ArchiveRoundedIcon from '@mui/icons-material/ArchiveRounded';
import AssignmentIndRoundedIcon from '@mui/icons-material/AssignmentIndRounded';
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import EditIcon from '@mui/icons-material/Edit';
import LabelRoundedIcon from '@mui/icons-material/LabelRounded';
import SendRoundedIcon from '@mui/icons-material/SendRounded';
import TableChartRoundedIcon from '@mui/icons-material/TableChartRounded';
import UnarchiveRoundedIcon from '@mui/icons-material/UnarchiveRounded';
import { Box, Button, Checkbox, Chip, Link, Stack, Tooltip, Typography } from '@mui/material';
import { xor } from 'lodash';

import BulkAssignStaff from 'components/BulkAssignStaff';
import StaffAvatar from 'components/common/avatar/StaffAvatar';
import StandardAvatarGroup from 'components/common/avatar/StandardAvatarGroup';
import { BulkActionFooter } from 'components/common/BulkActionFooter';
import { EmailTextDisplay } from 'components/common/EmailTextDisplay';
import LocalizedDate from 'components/common/LocalizedDate';
import OverflowActionsTooltip from 'components/common/OverflowActionsTooltip';
import PhoneTextDisplay from 'components/common/PhoneTextDisplay';
import { filterOverflowActions } from 'components/common/ResponsiveOverflowActions/utils';
import Table, { TableRef } from 'components/common/Table';
import { TableWrapper } from 'components/common/Table/TableWrapper';
import { DemoTag } from 'components/DemoTag';
import { useNoteEditorModalFromQuery } from 'components/EditorV2/Note/hooks/useNoteEditorModalFromQuery';
import EmailHyperlink from 'components/Inbox/common/EmailHyperlink';
import NoSearchResults from 'components/NoSearchResults';
import { Client, ClientDocumentation, ClientRelationships } from 'components/routes/lazyLoadedRoutes';
import { contactsSchemaId } from 'components/SchemaForms/constants';
import { addErrorSnackbarWithLangId } from 'components/snackbar/utils';
import { StatusTag } from 'components/StatusSelector/StatusTag';
import { intl } from 'lang';
import langIds from 'lang/langIds';
import { useExportContactsMutation } from 'store/api/contactExports/hooks';
import {
	useBulkArchiveContactsMutation,
	useBulkDeleteContactsMutation,
	useDeleteContactMutation,
	useGetDuplicateContactsQuery,
	useUpdateContactMutation,
} from 'store/api/contacts';
import { useArchiveContact } from 'store/api/contacts/hooks';
import { selectDuplicateContactsCountFromResult } from 'store/api/contacts/selectors';
import { useHasPermission } from 'store/api/permissions/hooks';
import { useDefaultContactStatus } from 'store/api/schemas/hooks';
import { useGetSchemaQuery } from 'store/api/schemas/service';
import { selectVisibleClientsColumns } from 'store/slices/contacts/selectors';
import { contactsInitialState, resetContactsBulkOptions } from 'store/slices/contacts/slice';
import { useContactsBulkOptions } from 'store/slices/contacts/useContactsBulkOptions';
import { addConfirmationModal, addModal } from 'store/slices/modals/slice';
import { push } from 'store/slices/router/slice';
import AutomationId, { ClientListAutomdationId } from 'util/automationIds';
import { getDisplayName } from 'util/contact';
import { fallbackToEmptyObject } from 'util/fallbackToEmptyArray';
import { useParamSelector } from 'util/hooks';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { toFullName } from 'util/person';
import { routes } from 'util/routes';

import { useClientList } from './hooks/useClientList';
import { useClientStatusProperty } from './hooks/useClientStatus';
import useOpenImport from './hooks/useOpenImport';
import { getClientColumnValue } from './util/getClientColumnValue';
import { transformContactsFilterToBulkContactsFilter } from './util/transformContactsFilterToBulkContactsFilter';
import BulkAddTags from './BulkAddTags';
import BulkChangeStatus from './BulkChangeStatus';
import { ClientExportSnackbar } from './ClientExportSnackbar';
import { ClientImportSnackbar } from './ClientImportSnackbar';
import { ClientSearchFilterToolbar } from './ClientSearchFilterToolbar_V2';
import ClientStatusSelector from './ClientStatusSelector';
import { ClientTableHeaderCheckbox, getCheckboxStates } from './ClientTableHeaderCheckbox';
import { ClientTableSkeleton } from './ClientTableSkeleton';
import { DuplicateBanner } from './DuplicateBanner';
import { NoClientsView } from './NoClientsView';

const renderTags = (tags: ITag[], isMediumScreenDown: boolean) => {
	const countOver3 = tags.slice(3, tags.length).length;

	if (tags.length === 0) {
		return '-';
	}

	return isMediumScreenDown ? (
		<div>
			{tags.slice(0, 3).map((tag) => (
				<Chip
					key={tag.id || tag.title}
					size='small'
					sx={{
						backgroundColor: tag.colorHex,
						mt: 0.5,
						mr: 1,
						color: 'common.black',
					}}
					label={tag.title}
				/>
			))}
			{!!countOver3 && (
				<Tooltip
					placement='bottom-start'
					title={tags
						.slice(3, tags.length)
						.map((x) => x.title)
						.join(', ')}
				>
					<Typography color='primary' m={1}>{`+${countOver3}`}</Typography>
				</Tooltip>
			)}
		</div>
	) : (
		<div>
			{tags.map((tag) => (
				<Chip
					key={tag.id || tag.title}
					size='small'
					sx={{
						backgroundColor: tag.colorHex,
						mt: 0.5,
						mr: 1,
						color: 'common.black',
					}}
					label={tag.title}
				/>
			))}
		</div>
	);
};

type AssignedStaffCellProps = { value: ISimplePerson[]; max?: number };

export const AssignedStaffCell: FC<AssignedStaffCellProps> = ({ value, max = 3 }) => {
	return (
		<Tooltip
			arrow
			placement='top'
			title={value.map((x) => (
				<div>{toFullName(x.firstName, x.lastName)}</div>
			))}
		>
			<Box width='fit-content'>
				<StandardAvatarGroup max={max} size='xs' sx={{ justifyContent: 'flex-end' }}>
					{value.map((staff) => (
						<StaffAvatar key={staff.id} personId={staff.id} size='xs' />
					))}
				</StandardAvatarGroup>
			</Box>
		</Tooltip>
	);
};

const ClientTable: FC = () => {
	const tableRef = useRef<TableRef>(null);

	useNoteEditorModalFromQuery();
	const dispatch = useDispatch();
	const location = useLocation();
	const navigate = useNavigate();

	const belowLg = useBreakpoint('lg');
	const belowMd = useBreakpoint('md');

	const clientsTableColumns = useParamSelector(selectVisibleClientsColumns);

	const { data: schema } = useGetSchemaQuery({ id: contactsSchemaId });
	const { statuses, groups: statusGroups } = useClientStatusProperty();

	const {
		clients,
		filter: clientsFilter,
		hasActiveFilter: hasActiveClientsFilter,
		isFetching: isFetchingClients,
		isLoading: isLoadingClients,
		isSuccess: isSuccessFetchingClients,
		hasMore: hasMoreClients,
		totalCount: totalClientsCount,
		loadMore: loadMoreClients,
		updateFilter: updateClientsFilter,
		updateSearchTerm: updateClientsSearchTerm,
	} = useClientList();

	const {
		bulkContactsOptions: bulkClientsOptions,
		getSelectedContacts: getSelectedClients,
		getSelectedContactsCount: getSelectedClientsCount,
		isContactSelected: isClientSelected,
		updateBulkOptions,
	} = useContactsBulkOptions();

	const [triggerUpdateContact] = useUpdateContactMutation();
	const [triggerDeleteContact] = useDeleteContactMutation();
	const [triggerBulkDeleteContacts] = useBulkDeleteContactsMutation();
	const [triggerBulkArchiveContacts] = useBulkArchiveContactsMutation();

	const hasViewSchedulingPermission = useHasPermission('schedulingView');

	const [isBulkAssignStaffModalOpen, setBulkAssignStaffModalOpen] = useState<boolean>(false);
	const openBulkAssignStaffModal = () => setBulkAssignStaffModalOpen(true);
	const closeBulkAssignStaffModal = () => setBulkAssignStaffModalOpen(false);

	const [isBulkAddTagsModalOpen, setBulkAddTagsModalOpen] = useState<boolean>(false);
	const openBulkAddTagsModal = () => setBulkAddTagsModalOpen(true);
	const closeBulkAddTagsModal = () => setBulkAddTagsModalOpen(false);

	const [isBulkUpdateStatusModalOpen, setUpdateStatusModalOpen] = useState<boolean>(false);
	const openBulkUpdateStatusModal = () => setUpdateStatusModalOpen(true);
	const closeBulkUpdateStatusModal = () => setUpdateStatusModalOpen(false);

	const { defaultStatus = { id: '' } } = useDefaultContactStatus();

	const { toggleArchiveContact, showArchiveSnackbar } = useArchiveContact();

	useOpenImport();

	const handleSort = (field: string, direction: SortDirection) => {
		updateClientsFilter({ sort: { field: field as ClientSortingFieldName, direction } });
	};

	const handleClientClick = (personId: string) => (event: SyntheticEvent) => {
		event.preventDefault();
		navigateToClient(personId, { subroute: hasViewSchedulingPermission ? 'Overview' : 'Profile' });
	};

	const handleBulkDeleteClients = () => {
		dispatch(
			addModal({
				type: 'DeleteRestorableItem',
				data: {
					type: 'Contact',
					isClient: true,
					count: getSelectedClientsCount({ totalContactsCount: totalClientsCount }),
					onDelete: async () =>
						triggerBulkDeleteContacts({
							dto: {
								bulkContactFilter: transformContactsFilterToBulkContactsFilter({
									filter: clientsFilter,
									options: bulkClientsOptions,
								}),
								contactIds: getSelectedClients({ contacts: clients }).map((client) => client.id),
								isClient: true,
							},
						}).then(handleBulkActionCancellation),
				},
			})
		);
	};

	const handleDropAccepted = (clientId: string) => (files: File[]) => {
		dispatch(addModal({ type: 'AddClientFiles', data: { clientId, files } }));
	};

	const handleStatusChange = (client: IContactDetail) => async (status: string) => {
		try {
			const dto = {
				...client,
				fields: fallbackToEmptyObject(client.fields),
				isArchived: status === 'Archived',
				status: status === 'Archived' ? (client.status ?? defaultStatus.id) : status,
			};

			await triggerUpdateContact({ id: client.id, dto });

			if (status === 'Archived' || client.isArchived) {
				showArchiveSnackbar({ contact: dto });
			}
		} catch (e) {}
	};

	const handleBulkActionCancellation = () => {
		dispatch(resetContactsBulkOptions());
	};

	const handleDeleteClient = (id: string) => {
		const currentClient = clients.find((client) => client.id === id);

		dispatch(
			addModal({
				type: 'DeleteRestorableItem',
				data: {
					type: 'Contact',
					isClient: true,
					context: <strong>{currentClient ? getDisplayName(currentClient) : ''}</strong>,
					onDelete: async () =>
						triggerDeleteContact({
							id,
							isClient: true,
						}).unwrap(),
				},
			})
		);
	};

	const handleArchiveClient = (client: IContactDetail) => async () => {
		toggleArchiveContact({ contact: client });
	};

	const handleBulkArchiveClients = () => {
		const selectedClients = getSelectedClients({ contacts: clients });
		dispatch(
			addConfirmationModal({
				primaryActionMessageId: langIds.YesArchive,
				titleMessageId: langIds.ArchiveClients,
				titleIcon: <ArchiveRoundedIcon color='default' />,
				descriptionMessageId: langIds.BulkArchiveClientsDescription,
				asyncActions: [
					{
						action: async () => {
							await triggerBulkArchiveContacts({
								dto: {
									isArchived: true,
									contactIds: selectedClients.map((client) => client.id),
									isClient: true,
									bulkContactFilter: transformContactsFilterToBulkContactsFilter({
										filter: clientsFilter,
										options: bulkClientsOptions,
									}),
								},
								context: {
									contacts: selectedClients,
								},
							}).then(handleBulkActionCancellation);
						},
					},
				],
			})
		);
	};

	const [exportContacts] = useExportContactsMutation();

	const handleExport = () => {
		dispatch(
			addModal({
				type: 'ExportClients',
				data: {
					onExport: async () => {
						await exportContacts({
							dto: {
								contactIds: bulkClientsOptions.selectedIds,
								bulkContactFilter: transformContactsFilterToBulkContactsFilter({
									filter: clientsFilter,
									options: bulkClientsOptions,
								}),
							},
						}).unwrap();
						updateBulkOptions({
							selectAll: false,
							selectedIds: [],
							excludedIds: [],
						});
					},
				},
			})
		);
	};

	const handleMerge = () => {
		const selectedClients = getSelectedClients({ contacts: clients });

		if (selectedClients.length > 4) {
			return dispatch(addErrorSnackbarWithLangId(langIds.MergeLimitExceeded));
		}

		navigate(`${routes('Clients')}/Duplicates/Merge`, {
			state: { contacts: selectedClients },
		});
	};

	const openActionModal = (type: ModalType, client: IContact, cb?: () => void) => () => {
		dispatch(addModal({ type, data: { client } }));
		cb?.();
	};

	const navigateToClient = (personId: string, options?: { subroute: string }) => {
		dispatch(push(`${routes('Clients')}/${personId}/${options?.subroute || 'Profile'}`));
	};

	const bulkSelectClient = (e) => {
		const id = e.target.id;
		updateBulkOptions({
			...(bulkClientsOptions.selectAll
				? { excludedIds: xor(bulkClientsOptions.excludedIds, [id]), selectedIds: [] }
				: { selectedIds: xor(bulkClientsOptions.selectedIds, [id]), excludedIds: [] }),
		});
	};

	const composeTableRowItems = (client: IContactDetail) => {
		if (clientsTableColumns.length === 0) return [];

		// Customize the value of the column label to be shown within the table row.
		const customColumnValue = {
			Name: (
				<Stack direction='row' alignItems='center' spacing={1}>
					<Link
						fontWeight='fontWeightMedium'
						href={`${routes('Clients')}/${client.id}`}
						onClick={handleClientClick(client.id)}
						data-automationid={`${AutomationId.ClientListPageItem}${client.firstName}-${client.lastName}`}
						sx={(theme) => ({
							color: client.isArchived ? `${theme.palette.text.secondary} !important` : undefined,
						})}
					>
						{getDisplayName(client)}
					</Link>
					{!!client.isDemo && <DemoTag />}
				</Stack>
			),
			Email: (
				<EmailTextDisplay email={client.email}>
					<EmailHyperlink contactDetail={client} />
				</EmailTextDisplay>
			),
			Tags: renderTags(client.tags || [], belowLg),
			IsArchived: (
				<Chip
					variant='tag'
					size='extraSmall'
					label={<FormattedMessage id={!client.isArchived ? langIds.Active : langIds.Inactive} />}
					color={!client.isArchived ? 'success' : 'default'}
				/>
			),
			AssignedStaff: client.assignedStaff?.length ? <AssignedStaffCell value={client.assignedStaff} /> : '-',
			Status: client.isArchived ? (
				<StatusTag id='Archived' />
			) : (
				<ClientStatusSelector
					status={client.status ?? ''}
					statuses={statuses}
					groups={statusGroups}
					onChange={handleStatusChange(client)}
				/>
			),
			PhoneNumber: <PhoneTextDisplay value={client.phoneNumber} />,
			BirthDate: <LocalizedDate date={client.birthDate} format='default' />,
		};

		const checkboxColumn = {
			key: 'select',
			label: (
				<ClientRowCheckbox id={client.id} checked={isClientSelected(client.id)} onClick={bulkSelectClient} />
			),
			onClick: (e) => e.stopPropagation(),
		};

		if (belowMd) {
			const status = client.isArchived ? 'Archived' : client.status;

			return [
				checkboxColumn,
				{
					key: 'Name',
					label: (
						<Stack direction='row' alignItems='center' spacing={1}>
							<Link
								fontWeight='fontWeightMedium'
								href={`${routes('Clients')}/${client.id}`}
								onClick={handleClientClick(client.id)}
								data-automationid={`${AutomationId.ClientListPageItem}${client.firstName}-${client.lastName}`}
								pr={1}
							>
								{getDisplayName(client)}
							</Link>
							{!!client.isDemo && <DemoTag size='extraSmall' />}
							{status && <StatusTag id={status} />}
						</Stack>
					),
				},
				...clientsTableColumns
					.filter((column) => column.key !== 'Name')
					.map((column) => ({
						key: column.key,
						label: getClientColumnValue({ key: column.key, client, customColumnValue, schema }),
						onClick: column.key === 'Status' ? (e) => e.stopPropagation() : undefined,
					})),
			];
		}

		return [
			checkboxColumn,
			...clientsTableColumns.map((column) => ({
				key: column.key,
				label: getClientColumnValue({ key: column.key, client, customColumnValue, schema }),
				onClick: column.key === 'Status' ? (e) => e.stopPropagation() : undefined,
			})),
		];
	};

	const handleCreateClient = () => dispatch(addModal('CreateClient'));

	useEffect(() => {
		Client.preload();
		ClientRelationships.preload();
		ClientDocumentation.preload();
	}, []);

	const tableColumns: ITableColumn[] =
		clientsTableColumns.length > 0
			? [
					{
						key: 'select',
						label: <ClientTableHeaderCheckbox totalCount={totalClientsCount} />,
						hideSortIcon: true,
					},
					...clientsTableColumns,
				]
			: [];

	const tableRows: ITableRow[] = clients.map((client) => ({
		key: client.id,
		automationId: `${AutomationId.ClientListPageItem}${client.firstName}-${client.lastName}`,
		disabled: client.isArchived,
		selected: isClientSelected(client.id),
		onRowClick: () =>
			navigateToClient(client.id, { subroute: hasViewSchedulingPermission ? 'Overview' : 'Profile' }),
		onDropAccepted: handleDropAccepted(client.id),
		items: composeTableRowItems(client),
		overflowActions: client.isArchived
			? [
					{
						key: 'unarchive',
						icon: <UnarchiveRoundedIcon />,
						label: intl.$t({ id: langIds.Unarchive }),
						onClick: handleArchiveClient(client),
					},
				]
			: [
					{
						key: 'edit',
						icon: <EditIcon />,
						label: intl.$t({ id: langIds.Edit }),
						onClick: () => navigateToClient(client.id, { subroute: 'Profile' }),
					},
					{
						key: 'send-intake',
						icon: <TableChartRoundedIcon />,
						label: intl.$t({ id: langIds.SendIntake }),
						onClick: openActionModal('SendClientIntake', client),
					},
					{
						key: 'invite-to-portal',
						icon: <SendRoundedIcon />,
						label: intl.$t({ id: langIds.InviteToPortal }),
						onClick: openActionModal('InviteToPortal', client, () => {}),
					},
					{
						key: 'editTags',
						icon: <LabelRoundedIcon />,
						label: intl.$t({ id: langIds.EditTags }),
						onClick: openActionModal('EditClientTags', client),
					},
					{
						key: 'assignStaff',
						icon: <AssignmentIndRoundedIcon />,
						label: intl.$t({ id: langIds.AssignTeam }),
						onClick: openActionModal('AssignStaff', client),
					},
				],
		overflowSecondaryActions: [
			...(!client.isArchived
				? [
						{
							key: 'archive',
							label: intl.$t({ id: langIds.Archive }),
							onClick: handleArchiveClient(client),
						},
					]
				: []),
			{
				key: 'delete',
				icon: <DeleteForeverRoundedIcon />,
				label: intl.$t({ id: langIds.Delete }),
				automationId: AutomationId.ClientListPageItemMoreMenuRemoveAccess,
				menuItemProps: { sx: (theme) => ({ color: theme.palette.error.main }) },
				onClick: () => handleDeleteClient(client.id),
			},
		],
	}));

	const isEmpty = isSuccessFetchingClients && !hasActiveClientsFilter && clients.length === 0;
	const noSearchResults = isSuccessFetchingClients && tableRows.length === 0 && hasActiveClientsFilter;
	const showTable = isSuccessFetchingClients && tableRows.length > 0 && tableColumns.length > 0;
	const showMergeButton = getSelectedClientsCount({ totalContactsCount: totalClientsCount }) > 1;

	const hasWorkspaceViewPermission = useHasPermission('workspaceSettingsView', 'Everything');
	const hasWorkspaceEditPermission = useHasPermission('workspaceSettingsEdit', 'Everything');
	const bulkOverflowActions = filterOverflowActions([
		showMergeButton && {
			key: langIds.Merge,
			label: <FormattedMessage id={langIds.Merge} />,
			onClick: handleMerge,
		},
		hasWorkspaceViewPermission &&
			hasWorkspaceEditPermission && {
				key: langIds.Export,
				label: <FormattedMessage id={langIds.Export} />,
				onClick: handleExport,
			},
		{
			key: langIds.DeleteClients,
			label: <FormattedMessage id={langIds.DeleteClients} />,
			menuItemProps: { sx: (theme) => ({ color: theme.palette.error.main }) },
			onClick: handleBulkDeleteClients,
		},
	]);
	const hasOverflowActions = !!bulkOverflowActions.length;

	const skipInitPersistScroll = !location?.state?.prevLocation?.pathname?.startsWith(routes('Clients'));

	const lastSeenDuplicateRecordsCount = localStorage.getItem('lastSeenDuplicateRecordsCount');

	const { duplicateRecordsCount } = useGetDuplicateContactsQuery(
		{},
		{
			selectFromResult: (result) => ({ duplicateRecordsCount: selectDuplicateContactsCountFromResult(result) }),
		}
	);

	const shouldShowDuplicateBanner =
		duplicateRecordsCount > 0 &&
		(!lastSeenDuplicateRecordsCount || duplicateRecordsCount > +lastSeenDuplicateRecordsCount);

	const handleDuplicateBannerCloseAlert = () => {
		// We need to use requestAnimationFrame to ensure the table is resized after the banner is closed
		// Otherwise, the table will not resize correctly.
		requestAnimationFrame(() => tableRef.current?.handleResize());
	};

	useEffect(() => {
		return () => {
			batch(() => {
				dispatch(resetContactsBulkOptions());
				updateClientsFilter({
					searchTerm: contactsInitialState.clientsFilter.searchTerm,
					sort: contactsInitialState.clientsFilter.sort,
					isUnassigned: contactsInitialState.clientsFilter.isUnassigned,
				});
			});
		};
	}, [dispatch, updateClientsFilter]);

	const selectedClientsCount = getSelectedClientsCount({ totalContactsCount: totalClientsCount });

	return (
		<TableWrapper
			data-automationid={ClientListAutomdationId.ClientListPage}
			includeBottomGap={selectedClientsCount > 0}
		>
			{!isEmpty && (
				<>
					<ClientSearchFilterToolbar
						totalCount={totalClientsCount}
						filter={clientsFilter}
						onSearch={updateClientsSearchTerm}
					/>

					{shouldShowDuplicateBanner && <DuplicateBanner onCloseAlert={handleDuplicateBannerCloseAlert} />}

					{showTable && (
						<Table
							ref={tableRef}
							id='client-table'
							aria-label={intl.formatMessage({ id: langIds.ClientsTable })}
							columns={tableColumns}
							rows={tableRows}
							hasMore={hasMoreClients}
							isLoading={isFetchingClients}
							sorting={clientsFilter.sort}
							smallScreenColumnCount={2}
							stickyHeader
							seamless
							onSort={handleSort}
							loadMore={loadMoreClients}
							TableContainerProps={{
								sx: (theme) => ({
									'& .MuiTableCell-head': {
										backgroundColor: theme.palette.background.default,
									},
									'& .MuiTableRow-root > .MuiTableCell-root:first-child': {
										width: 24,
									},
								}),
							}}
							disablePositionCheck
							usePersistScroll={{
								skipInit: skipInitPersistScroll,
							}}
						/>
					)}

					{noSearchResults && (
						<Box flexGrow={1}>
							<NoSearchResults />
						</Box>
					)}

					{isLoadingClients && <ClientTableSkeleton />}

					<BulkActionFooter
						selectedCount={selectedClientsCount}
						indeterminate={getCheckboxStates(bulkClientsOptions, totalClientsCount).indeterminate}
						onClear={handleBulkActionCancellation}
					>
						<Button variant='outlined' color='primary' onClick={openBulkUpdateStatusModal}>
							<FormattedMessage id={langIds.UpdateStatus} />
						</Button>
						<Button variant='outlined' color='primary' onClick={openBulkAddTagsModal}>
							<FormattedMessage id={langIds.AddTags} />
						</Button>
						<Button variant='outlined' color='primary' onClick={openBulkAssignStaffModal}>
							<FormattedMessage id={langIds.AssignTeam} />
						</Button>
						<Button variant='outlined' onClick={handleBulkArchiveClients}>
							<FormattedMessage id={langIds.Archive} />
						</Button>
						{hasOverflowActions && (
							<OverflowActionsTooltip
								key='more-bulk-actions'
								overflowActions={bulkOverflowActions}
								buttonProps={{
									sx: { borderRadius: 0 },
								}}
							/>
						)}
						{isBulkAssignStaffModalOpen && (
							<BulkAssignStaff
								open={isBulkAssignStaffModalOpen}
								filter={clientsFilter}
								type='clients'
								contacts={clients}
								onClose={closeBulkAssignStaffModal}
							/>
						)}
						{isBulkAddTagsModalOpen && (
							<BulkAddTags
								open={isBulkAddTagsModalOpen}
								filter={clientsFilter}
								contacts={clients}
								onClose={closeBulkAddTagsModal}
							/>
						)}
						{isBulkUpdateStatusModalOpen && (
							<BulkChangeStatus
								open={isBulkUpdateStatusModalOpen}
								filter={clientsFilter}
								contacts={clients}
								onClose={closeBulkUpdateStatusModal}
							/>
						)}
					</BulkActionFooter>
				</>
			)}

			{isEmpty && <NoClientsView onCreateClient={handleCreateClient} />}

			<Stack
				gap={2}
				sx={(theme) => ({
					position: 'fixed',
					right: theme.spacing(3),
					bottom: theme.spacing(3),
				})}
			>
				<ClientImportSnackbar />
				<ClientExportSnackbar />
			</Stack>
		</TableWrapper>
	);
};

export default ClientTable;

type ClientRowCheckboxProps = {
	id: string;
	checked: boolean;
	onClick: (e: ChangeEvent<HTMLInputElement>) => void;
};

const ClientRowCheckbox: FC<ClientRowCheckboxProps> = ({ id, checked, onClick }) => {
	return (
		<Checkbox
			id={id}
			checked={checked}
			inputProps={{ 'aria-label': `check ${id}` }}
			sx={{ padding: '0 !important' }}
			onChange={onClick}
		/>
	);
};
