import type { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import FileDownloadRoundedIcon from '@mui/icons-material/FileDownloadRounded';
import FileUploadRoundedIcon from '@mui/icons-material/FileUploadRounded';
import {
	IconButton,
	List,
	Snackbar,
	SnackbarContent,
	snackbarContentClasses,
	type SnackbarProps,
	Stack,
	type SxProps,
	Typography,
} from '@mui/material';

import { FileUploadListItem, IFileUploadListItemProps } from 'components/common/FileUpload/FileUploadListItem';
import langIds from 'lang/langIds';

type TransferType = 'import' | 'export';

export type FileTransferItem = Partial<
	Pick<IFileUploadListItemProps, 'fileName' | 'fileSize' | 'errorMessage' | 'successMessage' | 'truncateFileName'>
> & {
	id: string;
	status: 'Pending' | 'Running' | 'Successful' | 'Failed';
};

export interface IFileTransferSnackbarProps extends SnackbarProps {
	showHeader?: boolean;
	transferType?: TransferType;
	layout?: 'group' | 'stack';
	files?: FileTransferItem[];
	disableFixedPosition?: boolean;
	onDismissTransfer?: (fileId: string) => void;
	onDownloadItem?: (fileId: string) => void;
}

const FileTransferSnackbar: FC<IFileTransferSnackbarProps> = ({
	files = [],
	showHeader,
	layout = 'group',
	transferType = 'import',
	disableFixedPosition,
	onDismissTransfer,
	onDownloadItem,
	...props
}) => {
	const { formatMessage } = useIntl();
	const isExporting = transferType === 'export';
	const isImporting = transferType === 'import';
	const isGroupLayout = layout === 'group';
	const transferingLabelLookup: Record<TransferType, string> = {
		import: langIds.Importing,
		export: langIds.Exporting,
	};
	const transferingDataLabelLookup: Record<TransferType, string> = {
		import: langIds.ImportingData,
		export: langIds.ExportingData,
	};
	const TransferIcon = isImporting ? FileDownloadRoundedIcon : FileUploadRoundedIcon;
	const shouldShowHeader = showHeader && files.length > 0 && isGroupLayout;
	const maxSizePx: SxProps = {
		maxWidth: '500px',
		maxHeight: '500px',
	};

	return (
		<Snackbar
			{...props}
			ClickAwayListenerProps={{ onClickAway: () => {} }}
			anchorOrigin={{
				vertical: 'bottom',
				horizontal: 'right',
			}}
			sx={(theme) => ({
				zIndex: theme.zIndex.snackbar,
				[`.${snackbarContentClasses.root}`]: !isGroupLayout && {
					boxShadow: 'none',
				},
				...(disableFixedPosition && {
					'&&&': {
						width: '100%',
						position: 'relative',
						right: 'auto',
						bottom: 'auto',
						left: 'auto',
					},
				}),
			})}
		>
			<SnackbarContent
				sx={(theme) => ({
					...maxSizePx,
					width: '100%',
					overflowY: 'auto',
					backgroundColor: theme.palette.background.paper,
					paddingBottom: 0,
					paddingTop: 0,
					[`.${snackbarContentClasses.message}`]: {
						width: '100%',
						padding: 0,
					},
				})}
				message={
					<Stack>
						{shouldShowHeader && (
							<Stack
								direction='row'
								alignItems='center'
								gap={0.5}
								sx={(theme) => ({
									position: 'sticky',
									top: 0,
									marginTop: theme.spacing(1),
									paddingY: theme.spacing(1),
									backgroundColor: theme.palette.background.paper,
									zIndex: 1,
								})}
							>
								<TransferIcon color='default' />
								<Typography variant='subtitle1'>
									<FormattedMessage id={transferingDataLabelLookup[transferType]} />
									...
								</Typography>
							</Stack>
						)}
						<List
							sx={
								!isGroupLayout
									? {
											gap: 2,
											display: 'flex',
											flexDirection: 'column',
										}
									: {}
							}
						>
							{files.map(
								({
									id,
									fileName = '',
									fileSize,
									status,
									errorMessage,
									successMessage,
									truncateFileName,
								}) => {
									const isSuccess = status === 'Successful';
									const isFailed = status === 'Failed';
									const isTransfering = ['Pending', 'Running'].includes(status);
									const shouldShowDismissButton =
										(isExporting && isSuccess) ||
										(isImporting && (isTransfering || isSuccess)) ||
										isFailed;
									return (
										<FileUploadListItem
											key={id}
											fileName={fileName}
											fileSize={fileSize}
											loading={isTransfering}
											success={isSuccess}
											successMessage={successMessage}
											error={isFailed}
											errorMessage={errorMessage}
											loadingMessage={formatMessage({ id: transferingLabelLookup[transferType] })}
											truncateFileName={truncateFileName}
											actions={[
												isExporting && isSuccess && (
													<IconButton
														key='download'
														aria-label='download'
														size='small'
														onClick={() => onDownloadItem?.(id)}
													>
														<FileDownloadRoundedIcon fontSize='inherit' />
													</IconButton>
												),
												shouldShowDismissButton && (
													<IconButton
														key='close'
														aria-label='close'
														size='small'
														onClick={() => onDismissTransfer?.(id)}
													>
														<CloseRoundedIcon fontSize='inherit' />
													</IconButton>
												),
											].filter(Boolean)}
											sx={
												!isGroupLayout
													? (theme) => ({
															padding: 2,
															boxShadow: theme.shadows[3],
															...maxSizePx,
														})
													: {}
											}
										/>
									);
								}
							)}
						</List>
					</Stack>
				}
			/>
		</Snackbar>
	);
};

export default FileTransferSnackbar;
