import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { endOfDay, startOfDay } from 'date-fns';

import { useGetContactImportSummaryQuery, useUpdateContactImportSummaryMutation } from 'store/api/contactImports/hooks';
import { selectVisibleContactImportSummariesFromResult } from 'store/api/contactImports/selectors';
import { useHasPermission } from 'store/api/permissions/hooks';
import { selectUserId } from 'store/api/user/selectors';
import { refetchAfterImport } from 'store/slices/contacts/slice';

import { hasFileTransferItemStatus } from './util/hasFileTransferItemStatus';
import FileTransferSnackbar, { FileTransferItem } from './FileTransferSnackbar';

const POLLING_INTERVAL_IN_MS = 10000;

export const ClientImportSnackbar = () => {
	const dispatch = useDispatch();
	const hasViewWorkspaceSettingsPermission = useHasPermission('workspaceSettingsView');

	const currentUserId = useSelector(selectUserId);

	const [dismissedContactImportSummariesIds, setDismissedContactImportSummariesIds] = useState<string[]>([]);
	const [pollingInterval, setPollingInterval] = useState<number>(POLLING_INTERVAL_IN_MS);

	const handleSuccessImport = useCallback(() => {
		dispatch(refetchAfterImport());
	}, [dispatch]);

	const { contactImportSummaries } = useGetContactImportSummaryQuery(
		{
			query: {
				fromDate: startOfDay(new Date()),
				toDate: endOfDay(new Date()),
			},
			onSuccess: handleSuccessImport,
		},
		{
			selectFromResult: (result) => ({
				contactImportSummaries: selectVisibleContactImportSummariesFromResult(
					result,
					currentUserId || ''
				).filter((item) => !dismissedContactImportSummariesIds.includes(item.id)),
			}),
			pollingInterval: pollingInterval,
			skip: !hasViewWorkspaceSettingsPermission,
		}
	);

	const [triggerUpdateContactImportSummary] = useUpdateContactImportSummaryMutation();

	const handleDismissSnackbar = (importContactSummaryId: string) => {
		if (!currentUserId) return;

		setDismissedContactImportSummariesIds((prev) => [...prev, importContactSummaryId]);

		triggerUpdateContactImportSummary({
			id: importContactSummaryId,
			dto: {
				lastStatusSeenBy: [currentUserId],
			},
		});
	};

	useEffect(() => {
		// If there are any pending or running imports, keep polling
		const pendingOrRunningImports = contactImportSummaries.filter((summary) =>
			['Pending', 'Running'].includes(summary.status)
		);

		setPollingInterval(pendingOrRunningImports.length > 0 ? POLLING_INTERVAL_IN_MS : 0);
	}, [contactImportSummaries]);

	const files = useMemo(() => {
		return contactImportSummaries.reduce<FileTransferItem[]>((acc, summary) => {
			if (hasFileTransferItemStatus(summary.status)) {
				acc.push({
					...summary,
					fileName: summary.originalFileName || summary.fileName,
					status: summary.status,
				});
			}

			return acc;
		}, []);
	}, [contactImportSummaries]);

	return (
		<FileTransferSnackbar
			showHeader
			disableFixedPosition
			data-testid={clientImportSnackbarTestIds.snackbar}
			open={contactImportSummaries.length > 0}
			files={files}
			onDismissTransfer={handleDismissSnackbar}
		/>
	);
};

export const clientImportSnackbarTestIds = {
	snackbar: 'client-import-snackbar',
};
