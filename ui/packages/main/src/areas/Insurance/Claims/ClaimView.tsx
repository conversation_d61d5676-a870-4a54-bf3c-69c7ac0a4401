import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogProps } from '@mui/material';

import NoPermissions from 'components/NoPermissions';
import { useHasPermission } from 'store/api/permissions/hooks';

import { ClaimFormContentProps } from './ClaimForm/types';
import ClaimForm from './ClaimForm';
import { ClaimNotFound } from './ClaimNotFound';
import { ClaimTypeMap } from './constants';

export type ClaimViewProps<TData extends IInsuranceClaim> = {
	claim?: TData;
	claimType: ClaimType;
	isLoading?: boolean;
	dialogProps?: DialogProps;
	renderContent: (props: ClaimFormContentProps) => React.ReactNode;
};

const ClaimView = <TData extends IInsuranceClaim>({
	claim,
	claimType,
	isLoading,
	dialogProps,
	...props
}: ClaimViewProps<TData>) => {
	const navigate = useNavigate();

	// @TODO: Check if this is the correct permission
	const hasEditPermission = useHasPermission('invoicesEdit');
	const hasViewPermission = useHasPermission('invoicesView');

	// @TODO: implement close
	const handleClose = useCallback(() => {}, []);

	const handleGoToNewClaims = () => navigate(`/Insurance/Claims/${ClaimTypeMap[claimType]}/new`);

	if (!hasViewPermission) {
		return <NoPermissions />;
	}

	if (!claim && !isLoading) {
		return <ClaimNotFound onCreateClaim={hasEditPermission ? handleGoToNewClaims : undefined} />;
	}

	return (
		<Dialog
			fullScreen
			open={true}
			onClose={handleClose}
			disableEnforceFocus
			sx={{
				'& .MuiDialog-container': {
					'& > .MuiPaper-root': {
						height: '100%',
						flexGrow: 1,
					},
				},
			}}
			{...dialogProps}
		>
			<ClaimForm claim={claim} isLoading={isLoading} claimType={claimType} {...props} />
		</Dialog>
	);
};

export default ClaimView;
