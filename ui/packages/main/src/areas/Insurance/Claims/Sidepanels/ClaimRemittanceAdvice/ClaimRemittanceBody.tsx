import React, { Fragment, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Grid, Stack, TableBody, TableCell, TableRow, Typography } from '@mui/material';

import * as SidePanel from '@carepatron/components/src/SidePanel';

import { AllocationTableContainer } from 'areas/Client/Billing/SidePanels/common/AllocationTableContainer';
import { AllocationTableEmptyState } from 'areas/Client/Billing/SidePanels/common/AllocationTableEmptyState';
import { AllocationTableHeader } from 'areas/Client/Billing/SidePanels/common/AllocationTableHeader';
import { AllocationTableHeaderCell } from 'areas/Client/Billing/SidePanels/common/AllocationTableHeaderCell';
import langIds, { TranslationKey } from 'lang/langIds';
import { localizeDate } from 'util/date';
import useFormatPrice from 'util/useFormatPrice';

interface IProps {
	remittance?: IInsuranceClaimRemittance;
}

const TABLE_HEADER_KEYS: TranslationKey[] = [
	langIds.ItemsAndAdjustments,
	langIds.Fee,
	langIds.Adjustment,
	langIds.Paid,
];

export const ClaimRemittanceBody = ({ remittance }: IProps) => {
	const { $t } = useIntl();
	const formatPrice = useFormatPrice();

	const remittanceServiceLines = remittance?.serviceLines ?? [];
	const remittanceGlossaryItems = remittance?.glossary ?? [];

	// Null just means to render an empty grid for spacing
	const detailColumns: ({ label: string; value: string } | null)[] = useMemo(() => {
		return [
			{
				label: $t({ id: langIds.DateReceived }),
				value: remittance ? localizeDate(remittance.remittanceDetail.receivedDateTimeUtc, 'short') : '-',
			},
			{
				label: $t({ id: langIds.TotalBilled }),
				value: remittance ? formatPrice(remittance.totalAmount, remittance.remittanceDetail.currencyCode) : '-',
			},
			{ label: $t({ id: langIds.Payer }), value: remittance?.remittanceDetail.payerName ?? '-' },
			{
				label: $t({ id: langIds.TotalAdjustments }),
				value: remittance
					? formatPrice(remittance.totalAdjustmentAmount, remittance.remittanceDetail.currencyCode)
					: '-',
			},
			{
				label: $t({ id: langIds.ClearingHouseReference }),
				value: remittance?.remittanceDetail.externalClearingHouseId ?? '-',
			},
			{
				label: $t({ id: langIds.ClientResponsibility }),
				value: remittance
					? formatPrice(remittance.selfPayAmount, remittance.remittanceDetail.currencyCode)
					: '-',
			},
			null,
			{
				label: $t({ id: langIds.PaymentAmount }),
				value: remittance ? formatPrice(remittance.totalPaid, remittance.remittanceDetail.currencyCode) : '-',
			},
		];
	}, [$t, formatPrice, remittance]);

	return (
		<SidePanel.Content>
			<Stack spacing={3}>
				<Stack spacing={1.25}>
					<SidePanel.ContentSubHeader>
						<FormattedMessage id={langIds.Details} />
					</SidePanel.ContentSubHeader>

					<Grid
						container
						columns={2}
						columnSpacing={1}
						rowSpacing={1.25}
						sx={{ '& .MuiGrid-item:nth-of-type(odd)': { px: 0 } }}
					>
						{detailColumns.map((column, index) => (
							<Grid item xs={1} key={column?.label ?? index}>
								{!!column && (
									<>
										<Typography variant='body2' color='text.secondary'>
											{column.label}
										</Typography>
										<Typography
											variant='body2'
											data-testid={`claim-remittance-detail-value-${column.label}`}
										>
											{column.value}
										</Typography>
									</>
								)}
							</Grid>
						))}
					</Grid>
				</Stack>

				<AllocationTableContainer sx={{ maxHeight: 350 }}>
					<AllocationTableHeader>
						{TABLE_HEADER_KEYS.map((header, index) => (
							<AllocationTableHeaderCell
								key={header}
								labelId={header}
								isDividerHidden={index + 1 === TABLE_HEADER_KEYS.length}
								{...(!index && { colSpan: 2 })}
								{...(!!index && { sx: { minWidth: '15%' } })}
							/>
						))}
					</AllocationTableHeader>
					<TableBody
						sx={
							remittanceServiceLines.length
								? {
										'& .MuiTableCell-root': { px: 2, verticalAlign: 'top' },
										'& .MuiTableCell-root:nth-of-type(1)': { pr: 1.25 },
										'& .MuiTableCell-root:nth-of-type(2)': { pl: 0 },
										'& .MuiTableCell-root:nth-of-type(n+3)': { textAlign: 'center' },
									}
								: { '& .MuiTableCell-root': { px: 0 } }
						}
					>
						{!!remittanceServiceLines.length ? (
							remittanceServiceLines.map((serviceLine) => (
								<Fragment key={serviceLine.id}>
									<TableRow data-testid='claim-remittance-table-row'>
										<TableCell sx={{ width: '0.1%', whiteSpace: 'nowrap' }}>
											{localizeDate(serviceLine.date, 'short')}
										</TableCell>
										<TableCell>{serviceLine.description}</TableCell>
										<TableCell>
											{formatPrice(serviceLine.amount, serviceLine.currencyCode)}
										</TableCell>
										<TableCell />
										<TableCell>{formatPrice(serviceLine.paid, serviceLine.currencyCode)}</TableCell>
									</TableRow>
									{serviceLine.adjustments.map((adjustment) => (
										<TableRow key={adjustment.id} data-testid='claim-remittance-table-row'>
											<TableCell />
											<TableCell>
												{adjustment.groupCode}-{adjustment.reasonCode}
											</TableCell>
											<TableCell />
											<TableCell>
												{formatPrice(adjustment.amount, serviceLine.currencyCode)}
											</TableCell>
											<TableCell />
										</TableRow>
									))}
								</Fragment>
							))
						) : (
							<AllocationTableEmptyState
								messageId={langIds.NoUnclaimedItemsFound}
								colSpan={TABLE_HEADER_KEYS.length + 1}
							/>
						)}
					</TableBody>
				</AllocationTableContainer>

				<Stack spacing={1.25}>
					<SidePanel.ContentSubHeader>{$t({ id: langIds.Glossary })}</SidePanel.ContentSubHeader>

					{!!remittanceGlossaryItems.length ? (
						remittanceGlossaryItems.map((glossaryItem) => (
							<Typography
								key={`${glossaryItem.groupCode}${glossaryItem.reasonCode}`}
								variant='body2'
								color='text.secondary'
								data-testid='claim-glossary-item'
							>
								<b>
									{glossaryItem.groupCode}-{glossaryItem.reasonCode}:
								</b>{' '}
								{glossaryItem.description}
							</Typography>
						))
					) : (
						<Typography variant='body2' color='text.secondary'>
							<FormattedMessage id={langIds.NoGlossaryItems} />
						</Typography>
					)}
				</Stack>
			</Stack>
		</SidePanel.Content>
	);
};
