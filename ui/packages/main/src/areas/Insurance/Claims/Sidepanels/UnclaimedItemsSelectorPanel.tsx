import { useEffect, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import PaymentsIcon from '@mui/icons-material/Payments';
import { Button, Stack, Typography } from '@mui/material';

import * as SidePanel from '@carepatron/components/src/SidePanel';

import { useSidePanel } from 'components/sidePanels/useSidePanel';
import SquareIcon from 'components/SquareIcon';
import UnclaimedItemsSelector from 'components/UnclaimedItemsSelector';
import langIds from 'lang/langIds';

export interface IBillableItemsProps {
	items: IBillableItem[];
	allowedCurrencyCodes?: string[];
	onChange: (selectedItems: IBillableItem[]) => void;
}

const UnclaimedItemSelectorSidePanel = ({ items = [], allowedCurrencyCodes = [], onChange }: IBillableItemsProps) => {
	const { $t } = useIntl();
	const { isOpen, closeSidePanel } = useSidePanel('UnclaimedItemSelectorSidePanel');
	const [selectedItems, setSelectedItems] = useState<IBillableItem[]>([]);

	const isItemSelected = (option: IBillableItem) => {
		return selectedItems.some((selectedOption) => selectedOption.id === option.id);
	};

	const handleOptionClick = (selectedOption: IBillableItem) => {
		const option = items.find((option) => option.id === selectedOption.id);
		if (!option) return;

		if (isItemSelected(option)) {
			setSelectedItems(selectedItems.filter((selectedOption) => selectedOption.id !== option.id));
			return;
		}

		setSelectedItems([...selectedItems, option]);
	};

	const handleChange = () => {
		onChange(selectedItems);
		closeSidePanel();
	};

	useEffect(() => {
		setSelectedItems([]);
	}, [items, setSelectedItems]);

	return (
		<SidePanel.Root
			open={isOpen}
			sx={{ zIndex: 1300 }}
			onClose={closeSidePanel}
			closeLabel={$t({ id: langIds.Close })}
		>
			<SidePanel.Header>
				<Stack direction='row' spacing={2} alignItems='center'>
					<SquareIcon icon={<PaymentsIcon />} />
					<Typography variant='h6'>
						<FormattedMessage id={langIds.UnclaimedItems} />
					</Typography>
				</Stack>
			</SidePanel.Header>
			<SidePanel.Content>
				<UnclaimedItemsSelector
					items={items}
					selectedItems={selectedItems}
					allowedCurrencyCodes={allowedCurrencyCodes}
					onChange={handleOptionClick}
				/>
			</SidePanel.Content>
			<SidePanel.Footer>
				<Button variant='outlined' fullWidth onClick={closeSidePanel}>
					<FormattedMessage id={langIds.Cancel} />
				</Button>
				<Button variant='contained' fullWidth disabled={selectedItems.length === 0} onClick={handleChange}>
					<FormattedMessage id={langIds.AddToClaim} />
				</Button>
			</SidePanel.Footer>
			<SidePanel.Actions />
		</SidePanel.Root>
	);
};

export default UnclaimedItemSelectorSidePanel;
