import { useCallback } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useNavigate } from 'react-router-dom';
import PrintIcon from '@mui/icons-material//Print';
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import DownloadIcon from '@mui/icons-material/Download';
import EditIcon from '@mui/icons-material/Edit';
import { Box, useTheme } from '@mui/material';

import { IOverflowAction, IOverflowSecondaryAction } from 'components/common/OverflowActionsTooltip';
import { filterOverflowActions } from 'components/common/ResponsiveOverflowActions/utils';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { useAppDispatch } from 'store';
import { addModal } from 'store/slices/modals/slice';
import { useClaimAnalytics } from 'util/analytics/useClaimAnalytics';
import { getClaimDetailsRoute } from 'util/routes';

import { getCanDeleteClaim, useClaimActions } from '../utils';

export const CLAIM_ACTION_OVERFLOW_KEYS = {
	view: 'view',
	export: 'export',
	print: 'print',
	delete: 'delete',
};

type UseClaimOverflowActionArgs = {
	contactId: string;
	claim: IClaimReference;
	hasInvoicesEditPermission?: boolean;
};

export const useClaimOverflowActions = () => {
	const { $t } = useIntl();
	const theme = useTheme();
	const dispatch = useAppDispatch();
	const navigate = useNavigate();

	const getClaimActions = useClaimActions();
	const { trackClaimDeleted, trackClaimExported } = useClaimAnalytics();

	return useCallback(
		({ contactId, claim, hasInvoicesEditPermission }: UseClaimOverflowActionArgs) => {
			if (!claim) return {};

			const { onExportClaim, onDeleteClaim, onPrintClaim } = getClaimActions(claim.type);

			const canDeleteClaim = getCanDeleteClaim({
				hasInvoiceEditPermission: hasInvoicesEditPermission,
				status: claim.claimStatus,
				submissionMethod: claim.submissionMethod,
			});

			const handleViewClaim = () => {
				if (!claim) return;
				const { id, type } = claim;
				navigate(getClaimDetailsRoute(contactId, type, id));
			};

			const handleDeleteClaim = () => {
				if (!contactId) return;

				dispatch(
					addModal({
						type: 'DeleteRestorableItem',
						data: {
							type: 'InsuranceClaim',
							context: (
								<strong>
									<FormattedMessage id={langIds.Claim} />
									<Box component='span' ml={0.5}>
										#{claim.number}
									</Box>
								</strong>
							),
							onDelete: async () => {
								try {
									await onDeleteClaim(claim.id);
									trackClaimDeleted({
										id: claim.id,
										type: claim.type,
										submissionMethod: claim.submissionMethod,
									});
								} catch (error) {
									dispatch(addErrorSnackbar(error));
								}
							},
						},
					})
				);
			};

			const handleOnExport = async () => {
				try {
					await onExportClaim(claim.id, contactId);
					trackClaimExported({
						id: claim.id,
						type: claim.type,
						submissionMethod: claim.submissionMethod,
						exportType: 'pdf',
					});
				} catch (e) {
					dispatch(addErrorSnackbar(e));
				}
			};

			const handleOnPrintClaim = async () => {
				try {
					await onPrintClaim(claim.id, contactId);
					trackClaimExported({
						id: claim.id,
						type: claim.type,
						submissionMethod: claim.submissionMethod,
						exportType: 'text',
					});
				} catch (e) {
					dispatch(addErrorSnackbar(e));
				}
			};

			const overflowActions: IOverflowAction[] = filterOverflowActions([
				!!hasInvoicesEditPermission && {
					key: CLAIM_ACTION_OVERFLOW_KEYS.view,
					icon: <EditIcon />,
					label: $t({ id: langIds.Edit }),
					onClick: handleViewClaim,
				},
				{
					key: CLAIM_ACTION_OVERFLOW_KEYS.export,
					icon: <DownloadIcon />,
					label: $t({ id: langIds.Export }),
					onClick: handleOnExport,
				},
				{
					key: CLAIM_ACTION_OVERFLOW_KEYS.print,
					icon: <PrintIcon />,
					label: $t({ id: langIds.Print }),
					onClick: handleOnPrintClaim,
				},
			]);

			const overflowSecondaryActions: IOverflowSecondaryAction[] = filterOverflowActions([
				canDeleteClaim && {
					key: CLAIM_ACTION_OVERFLOW_KEYS.delete,
					icon: <DeleteForeverRoundedIcon />,
					label: $t({ id: langIds.Delete }),
					menuItemProps: { sx: { color: theme.palette.error.main } },
					onClick: handleDeleteClaim,
				},
			]);

			return {
				overflowActions,
				overflowSecondaryActions,
			};
		},
		[getClaimActions, $t, theme.palette.error.main, navigate, dispatch, trackClaimDeleted, trackClaimExported]
	);
};
