import { render, setupServerHandlers } from 'tests';
import { PAYERS_RESPONSE, providerInsurancePayerHandlers } from 'tests/api-mocks/handlers/insurancePayers';

import { intl } from 'lang';
import langIds from 'lang/langIds';

import { AvailablePayerContent } from '../AvailablePayerContent';

const renderAddAvailablePayerPanel = () => {
	return render(<AvailablePayerContent />);
};

const serverHandlers = setupServerHandlers(
	providerInsurancePayerHandlers.getAvailableProviderInsurancePayers.handler()
);
serverHandlers.run();

describe('AvailablePayerContent', () => {
	it('should render the list of available payers', async () => {
		const { user, getByText, findByTestId } = renderAddAvailablePayerPanel();
		const availablePayerDropdown = await findByTestId('available-payer-autocomplete');

		await user.click(availablePayerDropdown);

		for (const payer of PAYERS_RESPONSE.items) {
			const payerId = getByText(payer.payerId);
			const payerName = getByText(payer.name);

			expect(payerId).toBeInTheDocument();
			expect(payerName).toBeInTheDocument();
		}
	});
	it('should render the selected payers to the list once selected', async () => {
		const { user, getByTestId, findByTestId, getAllByRole } = renderAddAvailablePayerPanel();
		const availablePayerDropdown = await findByTestId('available-payer-autocomplete');

		await user.click(availablePayerDropdown);

		const options = getAllByRole('option');

		await user.click(options[0]);

		expect(getByTestId('selected-payers-list-root').childElementCount).toBe(1);
	});
	it('should render "No payers selected" if no payers are selected', async () => {
		const { findByText } = renderAddAvailablePayerPanel();

		const emptyStateText = await findByText('No payers selected');

		expect(emptyStateText).toBeInTheDocument();
	});
	it.skip('should render a skeleton loader while payer list is loading', async () => {
		const { findByTestId } = renderAddAvailablePayerPanel();

		const loadingState = await findByTestId('add-payer-skeleton');

		expect(loadingState).toBeInTheDocument();
	});
	it('should disable the save button if no payers are selected', async () => {
		const { findByRole } = renderAddAvailablePayerPanel();

		const saveButton = await findByRole('button', { name: 'Save' });

		expect(saveButton).toBeDisabled();
	});
	it('should not render a "already added" tag if its a new item', async () => {
		const { queryByText, findByTestId, user } = renderAddAvailablePayerPanel();

		const availablePayerDropdown = await findByTestId('available-payer-autocomplete');

		await user.click(availablePayerDropdown);

		const addedToWorkspaceElement = queryByText(intl.formatMessage({ id: langIds.AlreadyAdded }));

		expect(addedToWorkspaceElement).not.toBeInTheDocument();
	});
	it('should render a "already added" tag if it was already added before', async () => {
		const updatedResponse = {
			...PAYERS_RESPONSE,
			items: PAYERS_RESPONSE.items.map((item, index) => ({ ...item, addedToWorkspace: index === 0 })),
		};

		serverHandlers.server.use(
			providerInsurancePayerHandlers.getAvailableProviderInsurancePayers.handler((_, res, ctx) =>
				res(ctx.status(200), ctx.json(updatedResponse))
			)
		);

		const { findByText, findByTestId, user } = renderAddAvailablePayerPanel();

		const availablePayerDropdown = await findByTestId('available-payer-autocomplete');

		await user.click(availablePayerDropdown);

		const addedToWorkspaceElement = await findByText(intl.formatMessage({ id: langIds.AlreadyAdded }));

		expect(addedToWorkspaceElement).toBeInTheDocument();
	});
});
