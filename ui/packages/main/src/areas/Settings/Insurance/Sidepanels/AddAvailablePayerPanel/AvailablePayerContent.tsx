import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useIntl } from 'react-intl';
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import { Box, Button, Chip, IconButton, InputAdornment, Stack, Typography } from '@mui/material';
import { debounce } from 'lodash';

import { SidePanel } from '@carepatron/components';
import { SIDE_PANEL_PADDING } from '@carepatron/components/src/SidePanel/SidePanel';

import Autocompleter, { IProps } from 'components/common/automcompleter/Autocomplete';
import { BottomElement } from 'components/common/Table/BottomElement';
import { SearchStartAdornment } from 'components/SearchFilterToolbar';
import { useSidePanel } from 'components/sidePanels/useSidePanel';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { useAppDispatch } from 'store';
import { useImportProviderInsurancePayerMutation } from 'store/api/providerInsurancePayers';

import { PayerItem } from '../../Payers/PayerItem';

import { AddPayerContentSkeleton } from './AddPayerContentSkeleton';
import { useFetchAvailableInsurancePayers } from './hooks';

type AutoCompleteRenderOptionArg = Parameters<NonNullable<IProps<IAvailableInsurancePayer>['renderOption']>>;

export const AvailablePayerContent = () => {
	const { $t } = useIntl();
	const dispatch = useAppDispatch();
	const endRef = useRef<HTMLDivElement>(null);
	const [searchTerm, setSearchTerm] = useState('');
	const [selectedInsurancePayers, setSelectedInsurancePayers] = useState<IAvailableInsurancePayer[]>([]);

	const { closeSidePanel } = useSidePanel('AddAvailablePayerPanel');
	const {
		handleLoadMore,
		availablePayers,
		handleInitList,
		hasMore,
		isLoading: isLoadingPayers,
	} = useFetchAvailableInsurancePayers();

	const [importProvider, { isLoading: isImportingPayer }] = useImportProviderInsurancePayerMutation();

	useEffect(() => {
		handleInitList({ searchTerm });
	}, [handleInitList, searchTerm]);

	const debouncedSearchTerm = debounce(setSearchTerm, 300);

	const handleOnSearch = useCallback(
		(text: string) => {
			debouncedSearchTerm.cancel();
			debouncedSearchTerm(text);
		},
		[debouncedSearchTerm]
	);

	const handleRemovePayer = useCallback((payer: IAvailableInsurancePayer) => {
		setSelectedInsurancePayers((prev) => prev.filter((p) => p.payerId !== payer.payerId));
	}, []);

	const handleSave = useCallback(async () => {
		try {
			await importProvider({
				dto: {
					payers: selectedInsurancePayers.map((insurancePayer) => ({
						clearingHouse: insurancePayer.clearingHouse,
						payerId: insurancePayer.payerId,
					})),
				},
			}).unwrap();

			closeSidePanel();
		} catch (e) {
			dispatch(addErrorSnackbar(e));
		}
	}, [closeSidePanel, dispatch, importProvider, selectedInsurancePayers]);

	const renderOption = useCallback(
		(
			props: AutoCompleteRenderOptionArg[0],
			option: AutoCompleteRenderOptionArg[1],
			state: AutoCompleteRenderOptionArg[2]
		) => {
			const isBottomComponent = availablePayers.length - selectedInsurancePayers.length === state.index + 1;

			return (
				<li
					{...props}
					style={{
						...props.style,
						width: '100%',
						overflow: 'hidden',
						cursor: option.addedToWorkspace ? 'not-allowed' : 'auto',
					}}
					onClick={option.addedToWorkspace ? undefined : props.onClick}
				>
					<PayerItem
						payer={option}
						rootProps={{ sx: { overflow: 'hidden' } }}
						{...(option.addedToWorkspace && {
							subContent: (
								<Chip
									variant='tag'
									label={$t({ id: langIds.AlreadyAdded })}
									size='extraSmall'
									sx={{ width: 'fit-content', pointerEvents: 'none' }}
								/>
							),
						})}
					/>

					{isBottomComponent && (
						<BottomElement
							endRef={endRef}
							hasMore={hasMore}
							loadMore={handleLoadMore}
							infiniteScrollOptions={{ rootMargin: '10px' }}
						/>
					)}
				</li>
			);
		},
		[$t, availablePayers.length, handleLoadMore, hasMore, selectedInsurancePayers.length]
	);

	return (
		<>
			<SidePanel.Content disablePadding>
				{isLoadingPayers ? (
					<AddPayerContentSkeleton />
				) : (
					<Stack spacing={2} overflow='hidden' sx={{ py: SIDE_PANEL_PADDING }}>
						<Typography color='text.secondary' variant='body2' sx={{ px: SIDE_PANEL_PADDING }}>
							{$t({ id: langIds.AddAvailablePayersDescription })}
						</Typography>

						<Autocompleter
							multiple
							variantOutlined
							options={availablePayers}
							inputLabel={$t({ id: langIds.SelectPayers })}
							getOptionLabel={(option) => option.payerId}
							rootProps={{
								sx: { px: SIDE_PANEL_PADDING },
							}}
							value={selectedInsurancePayers}
							InputProps={{
								'data-testId': 'available-payer-autocomplete',
								startAdornment: (
									<InputAdornment position='start' sx={{ ml: 1, mr: 0 }}>
										<SearchStartAdornment />
									</InputAdornment>
								),
								placeholder: $t({ id: langIds.ChoosePayer }),
							}}
							disableClearable
							disableCloseOnSelect
							onInputChange={handleOnSearch}
							// Make sure to reset search term when the panel closes
							onClose={() => setSearchTerm('')}
							// Don't filter options since we're basing it off the API results
							filterOptions={(options) => options}
							isOptionEqualToValue={(option, value) => option.payerId === value.payerId}
							// Don't render tags since we're popping them on the list view
							renderTags={() => null}
							onChange={setSelectedInsurancePayers}
							renderOption={renderOption}
						/>

						{!!selectedInsurancePayers.length ? (
							<Stack
								spacing={2}
								overflow='auto'
								sx={{ px: SIDE_PANEL_PADDING }}
								data-testId='selected-payers-list-root'
							>
								{selectedInsurancePayers.map((selectedPayer) => (
									<PayerItem
										key={selectedPayer.id}
										payer={selectedPayer}
										endComponent={
											<IconButton onClick={() => handleRemovePayer(selectedPayer)}>
												<CloseRoundedIcon color='default' />
											</IconButton>
										}
									/>
								))}
							</Stack>
						) : (
							<Box sx={{ px: SIDE_PANEL_PADDING }}>
								<Typography variant='body2' color='text.secondary'>
									{$t({ id: langIds.AvailablePayersEmptyState })}
								</Typography>
							</Box>
						)}
					</Stack>
				)}
			</SidePanel.Content>
			<SidePanel.Footer>
				<Stack direction='row' gap={1} flex={1}>
					<Button fullWidth variant='outlined' onClick={closeSidePanel}>
						{$t({ id: langIds.Cancel })}
					</Button>
					<Button
						fullWidth
						variant='contained'
						disabled={!selectedInsurancePayers.length || isImportingPayer}
						onClick={handleSave}
					>
						{$t({ id: langIds.Save })}
					</Button>
				</Stack>
			</SidePanel.Footer>
		</>
	);
};
