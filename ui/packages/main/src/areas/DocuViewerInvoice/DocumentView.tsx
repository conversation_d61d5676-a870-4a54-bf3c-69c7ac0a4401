import { FC, useCallback, useEffect } from 'react';
import { useIntl } from 'react-intl';
import { useDispatch, useSelector } from 'react-redux';
import { Font } from '@react-pdf/renderer';
import { chunk } from 'lodash';

import DocumentSkeleton from 'areas/DocuViewerSuperbill/DocumentSkeleton';
import { useManualPaymentMethod } from 'areas/StripePayment/util/useManualPaymentMethod';
import DocumentViewer from 'components/DocumentViewer';
import ContentWithSidebar from 'components/layout/ContentWithSidebar';
import { addErrorSnackbar, addErrorSnackbarWithErrorCode, addErrorSnackbarWithLangId } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import {
	invoiceService,
	openInvoice,
	selectOpenedInvoice,
	setOpenAddPayment,
	useDeleteInvoiceMutation,
	useLazyGetInvoiceQuery,
	useUpdateInvoiceStatusMutation,
} from 'store/api/invoices';
import { selectCurrentProviderId } from 'store/api/providers/selectors';
import { addModal } from 'store/slices/modals/slice';
import { push } from 'store/slices/router/slice';
import { unwrapError } from 'util/errors';

import ContemporaryTemplate from './templates/ContemporaryTemplate';
import ModernTemplate from './templates/ModernTemplate';
import SimpleTemplate from './templates/SimpleTemplate';
import { useReadOnlyInvoice, useReadOnlyInvoicePDF } from './util/hooks';
import DocumentHeader from './DocumentHeader';
import DocumentNotFound from './InvoiceNotFound';
import TemplateRenderer from './TemplateRenderer';

Font.registerHyphenationCallback((word) => {
	// Consider breaking words only if it's really long
	if (word.length > 30) {
		return word.split('');
	}
	return [word];
});

export const getTemplateComponent = (template: InvoiceTemplateDesign) => {
	const templateComponents: Record<
		string,
		FC<React.PropsWithChildren<IInvoiceTemplateProps & { renderAsPDF: boolean }>>
	> = {
		Simple: SimpleTemplate,
		Contemporary: ContemporaryTemplate,
		Modern: ModernTemplate,
	};

	return templateComponents[template];
};

const DocumentViewInvoice = () => {
	const dispatch = useDispatch();
	const { formatMessage } = useIntl();

	const currentProviderId = useSelector(selectCurrentProviderId);
	const { invoiceId, isAddPaymentOpen, isLogoCacheIgnored } = useSelector(selectOpenedInvoice);

	const [getInvoice, { isFetching: isFetchingInvoice }] = useLazyGetInvoiceQuery();
	const [updateInvoiceStatus] = useUpdateInvoiceStatusMutation();
	const [deleteInvoice] = useDeleteInvoiceMutation();

	const { invoice, provider, providerAddress, closeRoute, logoUrl } = useReadOnlyInvoice({ isLogoCacheIgnored });
	const { download, print } = useReadOnlyInvoicePDF({
		provider,
		providerAddress,
	});

	const { handleCreateManualPayment, isLoading: isCreatingPayment } = useManualPaymentMethod({
		invoice,
		paymentMethod: 'credit_balance',
		providerId: provider?.id ?? '',
	});

	const isSyncingInvoice = invoiceService.endpoints.getInvoice.useQueryState({
		id: invoiceId || invoice?.id || '',
		options: {
			polling: {
				enabled: true,
				expectedStates: ['Paid', 'Processing'],
			},
		},
		context: {
			// passed in provider id to handle sync invoice
			providerId: provider?.id,
		},
	}).isLoading;

	const isLoading = isFetchingInvoice || isSyncingInvoice || isCreatingPayment;

	useEffect(() => {
		if (!invoice && !!invoiceId) {
			// Skip unnecessary query if user is not authenticated
			if (currentProviderId) {
				getInvoice({ id: invoiceId });
			} else {
				dispatch(addErrorSnackbarWithErrorCode(401));
			}
		}
	}, [getInvoice, invoiceId, currentProviderId, dispatch, invoice]);

	const handleClose = useCallback(() => {
		dispatch(openInvoice({ invoiceId: null, isAddPaymentOpen: false }));
		dispatch(push(closeRoute));
	}, [closeRoute, dispatch]);

	const onEdit = (invoice: IInvoice) =>
		dispatch(addModal({ type: 'CreateInvoice', data: { invoice, origin: 'document view' } }));

	const onDelete = useCallback(() => {
		if (!invoice) return;

		const deleteInvoiceAction = async () => {
			try {
				await deleteInvoice({ id: invoice.id, context: { invoice } }).unwrap();
				handleClose();
			} catch (error) {
				const { errorCode } = unwrapError(error);
				if (errorCode === 'Unauthorised') {
					dispatch(addErrorSnackbarWithLangId(langIds.UnauthorisedInvoiceSnackbar));
				} else {
					dispatch(addErrorSnackbar(error));
				}
			}
		};

		dispatch(
			addModal({
				type: 'DeleteRestorableItem',
				data: {
					type: 'Invoice',
					context: (
						<strong>
							{formatMessage({ id: langIds.InvoiceNumberFormat }, { number: invoice.number })}
						</strong>
					),
					onDelete: deleteInvoiceAction,
				},
			})
		);
	}, [invoice, dispatch, formatMessage, deleteInvoice, handleClose]);

	const onEmail = (invoice: IInvoice, sendOnlinePayment: boolean) =>
		dispatch(addModal({ type: 'EmailInvoice', data: { invoice, sendOnlinePayment } }));

	const onSetOpenAddPayment = (value: boolean) => dispatch(setOpenAddPayment(value));

	const onMarkAsVoid = useCallback(async () => {
		if (!invoice) return;
		return updateInvoiceStatus({
			id: invoice.id,
			dto: {
				status: 'Void' as InvoiceStatus,
			},
			context: {
				invoice,
			},
		});
	}, [invoice, updateInvoiceStatus]);

	const onMarkAsUnpaid = useCallback(async () => {
		if (!invoice) return;

		try {
			return await updateInvoiceStatus({
				id: invoice.id,
				dto: { status: 'Unpaid' as InvoiceStatus },
				context: { invoice },
			}).unwrap();
		} catch (e) {
			dispatch(addErrorSnackbar(e));
			return;
		}
	}, [dispatch, invoice, updateInvoiceStatus]);

	return (
		<DocumentViewer.Dialog isOpen={!!invoiceId} handleClose={handleClose}>
			{invoice && provider && (
				<DocumentHeader
					invoice={invoice!}
					isAddPaymentOpen={isAddPaymentOpen}
					isLoading={isLoading}
					onClose={handleClose}
					onDownloadPdf={() => download(invoice, logoUrl)}
					onPrint={() => print(invoice, logoUrl)}
					onEdit={() => onEdit(invoice!)}
					onDelete={onDelete}
					onEmail={() => onEmail(invoice!, false)}
					onSendOnlinePayment={() => onEmail(invoice!, true)}
					onSetOpenAddPayment={onSetOpenAddPayment}
					onMarkAsVoid={onMarkAsVoid}
					onMarkAsUnpaid={onMarkAsUnpaid}
					onMarkAsPaid={handleCreateManualPayment}
					onIssueCredit={handleCreateManualPayment}
				/>
			)}
			<ContentWithSidebar layout='Invoice' removeSidebarsOnUnmount>
				<DocumentViewer.Content
					hasReducedPadding
					DocumentLoading={<DocumentSkeleton />}
					DocumentNotFound={<DocumentNotFound type='invoice' resourceId={invoiceId} />}
					documentStatus={
						isLoading || !invoiceId ? 'Loading' : !invoice || !provider ? 'NotFound' : 'Success'
					}
				>
					{invoice && provider && (
						<TemplateRenderer
							Template={getTemplateComponent(invoice.theme.layout)}
							renderAs='HTML'
							templateProps={{
								logoUrl,
								invoice,
								provider,
								providerAddress,
							}}
						/>
					)}
				</DocumentViewer.Content>
			</ContentWithSidebar>
		</DocumentViewer.Dialog>
	);
};

export default DocumentViewInvoice;
