import { FilterKeyMappings } from './hooks';
import { setDateRangeFilterSearchParam } from './utils';

export const CLAIMS_LIST_MAPPING_FILTER_KEYS: FilterKeyMappings = {
	claimStatusFilter: { keys: ['status'] },
	clientsFilter: { keys: ['contactIds'] },
	staffFilter: { keys: ['staffIds'] },
	dateRange: { keys: ['toDate', 'fromDate', 'dateId'], onFilter: setDateRangeFilterSearchParam },
	payersFilter: { keys: ['payerIds'] },
};

export const PAYMENTS_LIST_MAPPING_FILTER_KEYS: FilterKeyMappings = {
	clientsFilter: { keys: ['contactIds'] },
	paymentStatusFilter: { keys: ['status'] },
	typesFilter: { keys: ['type'] },
	providersFilter: { keys: ['provider'] },
	amount: { keys: ['amountType', 'fromAmount', 'toAmount'], onFilter: () => {} },
	dateRange: { keys: ['toDate', 'fromDate', 'dateId'], onFilter: setDateRangeFilterSearchParam },
};

export const INVOICE_LIST_MAPPING_FILTER_KEYS: FilterKeyMappings = {
	contacts: { keys: ['contactIds'] },
	status: { keys: ['status'] },
	staff: { keys: ['staffIds'] },
	dateRange: { keys: ['toDate', 'fromDate', 'dateId'], onFilter: setDateRangeFilterSearchParam },
};

export const INVOICE_STATUSES: InvoiceStatus[] = ['Sent', 'Unpaid', 'Processing', 'Paid', 'Void'];

export const PAYMENT_STATUSES: PayoutStatus[] = ['Pending', 'InTransit', 'Paid', 'Failed'];
export const PAYMENT_PROVIDERS: PaymentProvider[] = ['Manual', 'Stripe', 'CustomerBalance', 'Insurance', 'ClaimMD'];
export const PAYMENT_TYPES = [
	'Internet Banking',
	'card',
	'link',
	'Cash',
	'us_bank_account',
	'credit_balance',
	'Insurance',
	'Other',
];

type ClaimStatusOption = Exclude<ClaimStatus, 'Unknown' | 'Archived'>;

const CLAIM_STATUS_RECORD: Record<ClaimStatusOption, ClaimStatusOption> = {
	Draft: 'Draft',
	Validated: 'Validated',
	Submitted: 'Submitted',
	Rejected: 'Rejected',
	Accepted: 'Accepted',
	PartiallyPaid: 'PartiallyPaid',
	Paid: 'Paid',
	Denied: 'Denied',
	Closed: 'Closed',
};

export const CLAIM_LIST_STATUSES: ClaimStatus[] = Object.values(CLAIM_STATUS_RECORD);

export const INVOICES_LIST_SORT_KEYS: InvoiceListSortKey[] = ['issueDate', 'number', 'dueDate', 'paymentDate'];

export const CLAIM_LIST_SORT_KEYS: ClaimListSortKey[] = ['fromDate', 'number', 'lastSubmittedDateTimeUtc'];

export const INVOICE_LIST_SORT_MAPPER: Partial<Record<InvoiceListColumnKey, InvoiceListSortKey>> = {
	issueDate: 'issueDate',
	invoiceNumber: 'number',
	dueDate: 'dueDate',
	dateReceived: 'paymentDate',
};

export const CLAIM_LIST_SORT_MAPPER: Partial<Record<ClaimListColumnType, ClaimListSortKey>> = {
	date: 'fromDate',
	claimNumber: 'number',
	submittedDate: 'lastSubmittedDateTimeUtc',
};

export const MOBILE_CLAIM_LIST_COLUMN_KEYS: ClaimListColumnType[] = [
	'client',
	'date',
	'payer',
	'amount',
	'submittedDate',
	'method',
	'team',
];

export const MOBILE_PAYMENT_LIST_COLUMN_KEYS: PaymentListColumnKey[] = [
	'client',
	'paymentDate',
	'provider',
	'reference',
	'amount',
	'payoutsStatus',
	'payoutDate',
];

export const MOBILE_INVOICE_COLUMN_KEYS: InvoiceListColumnKey[] = [
	'client',
	'invoiceNumber',
	'services',
	'price',
	'dueDate',
	'team',
	'dateReceived',
];
