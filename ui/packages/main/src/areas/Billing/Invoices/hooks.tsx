import React, { useCallback, useMemo, useState } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { useSearchParams } from 'react-router-dom';
import { ContentCopyRounded } from '@mui/icons-material';
import AttachEmailRoundedIcon from '@mui/icons-material/AttachEmailRounded';
import BlockRoundedIcon from '@mui/icons-material/BlockRounded';
import CreditCardOffRoundedIcon from '@mui/icons-material/CreditCardOffRounded';
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import DownloadRoundedIcon from '@mui/icons-material/DownloadRounded';
import EditIcon from '@mui/icons-material/Edit';
import EmailRoundedIcon from '@mui/icons-material/EmailRounded';
import PaymentRoundedIcon from '@mui/icons-material/PaymentRounded';
import ReceiptIcon from '@mui/icons-material/Receipt';
import { useTheme } from '@mui/material';
import { addDays } from 'date-fns';
import { zonedTimeToUtc } from 'date-fns-tz';
import { omit } from 'lodash';

import { logger } from '@carepatron/utilities';

import { useReadOnlyInvoice, useReadOnlyInvoicePDF } from 'areas/DocuViewerInvoice/util/hooks';
import { IOverflowAction, IOverflowSecondaryAction } from 'components/common/OverflowActionsTooltip';
import { convertBillableItemsToInvoiceLineItems } from 'components/forms/CreateInvoiceForm/util';
import { parseDateRangeFilterValue } from 'components/SearchFilterToolbar/utils';
import { addErrorSnackbar, addErrorSnackbarWithLangId } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { getStore, useAppDispatch } from 'store';
import { logNonRtkQError } from 'store/api';
import { useLazyGetBillablesQuery } from 'store/api/billables/hooks';
import { GetBillablesResponse } from 'store/api/billables/service';
import { selectBillingSettings, selectCurrencyCode } from 'store/api/billingSettings/selectors';
import {
	DeleteInvoiceApiArg,
	GetInvoicesApiArg,
	openInvoice,
	selectInvoiceEntities,
	selectInvoicesList,
	UpdateInvoiceStatusApiArg,
	useCreateInvoiceMutation,
	useDeleteInvoiceMutation,
	useLazyGetInvoiceQuery,
	useLazyGetInvoicesQuery,
	useUpdateInvoiceStatusMutation,
} from 'store/api/invoices';
import { useLazyGetInvoiceTemplatesQuery } from 'store/api/invoiceTemplates/hooks';
import { useLazyGetPaymentLinkQuery } from 'store/api/payments';
import { useLazyGetTaskQuery, useSplitTaskOccurrenceMutation } from 'store/api/tasks/hooks';
import { selectGetTasksQueryCacheEntries, selectTaskByIdAndContactId } from 'store/api/tasks/selectors';
import { TasksGetApiArg, tasksService } from 'store/api/tasks/service';
import { modifyInvoicesInTaskCache } from 'store/api/tasks/utils';
import { useLazyGetTaxRatesQuery } from 'store/api/taxRates/hooks';
import { useLegacyFeatureModule } from 'store/slices/features/hooks';
import { addModal } from 'store/slices/modals/slice';
import { addSnackbar } from 'store/slices/snackbars/slice';
import { selectCalendarSettings, selectTasksFilterQuery } from 'store/slices/tasks/selectors';
import { InvoiceTrackingOrigin, useInvoiceAnalytics } from 'util/analytics/useInvoiceAnalytics';
import { unwrapError } from 'util/errors';
import { createUrlWithToken, routes } from 'util/routes';

import { INVOICES_LIST_SORT_KEYS } from '../constants';
import { getHasActiveFilters } from '../utils';

import { getDefaultDateRangeOptions } from './DateRangePicker';
import { formatItemSnapshotToInvoiceLineItem, getItemSnapshotsTotal } from './utils';

export type GetInvoiceArgs =
	| {
			invoice: Pick<IInvoice, 'contactId' | 'status' | 'providerId' | 'id' | 'taskId'> | IContactInvoice;
			isPartialInvoice: true;
	  }
	| { invoice: IInvoice; isPartialInvoice?: false };

export const INVOICE_ACTION_OVERFLOW_KEYS = {
	createSuperbillReceipt: 'createSuperbillReceipt',
	edit: 'edit',
	addPayment: 'addPayment',
	sendOnlinePayment: 'sendOnlinePayment',
	markAsVoid: 'markAsVoid',
	markAsUnpaid: 'markAsUnpaid',
	email: 'email',
	downloadPdf: 'downloadPdf',
	copy: 'copy',
	delete: 'delete',
};

export const ACTIONS_REQUIRING_PERMISSIONS = [
	INVOICE_ACTION_OVERFLOW_KEYS.createSuperbillReceipt,
	INVOICE_ACTION_OVERFLOW_KEYS.edit,
	INVOICE_ACTION_OVERFLOW_KEYS.addPayment,
	INVOICE_ACTION_OVERFLOW_KEYS.markAsVoid,
	INVOICE_ACTION_OVERFLOW_KEYS.markAsUnpaid,
	INVOICE_ACTION_OVERFLOW_KEYS.delete,
];

export const useCopyLink = () => {
	const dispatch = useAppDispatch();
	const { $t } = useIntl();
	const { trackSendInvoice } = useInvoiceAnalytics();

	const [getPaymentLink, { isLoading }] = useLazyGetPaymentLinkQuery();

	const onCopyLink = useCallback(
		async (invoice: Pick<IInvoice, 'id' | 'providerId' | 'status'>) => {
			if (isLoading) return;

			try {
				const { value: token } = await getPaymentLink({
					invoiceId: invoice.id,
					providerId: invoice.providerId,
				}).unwrap();

				const link = createUrlWithToken(
					`${window.location.origin}${routes('Invoices')}/${invoice.id}/Pay`,
					token
				);

				navigator.clipboard
					.writeText(link)
					.then(() => {
						dispatch(
							addSnackbar({
								message: $t({ id: langIds.CopyLinkSuccessSnackbar }),
								id: langIds.CopyLinkSuccessSnackbar,
								variant: 'success',
							})
						);
					})
					.catch(() => {
						dispatch(addErrorSnackbarWithLangId(langIds.CopyToClipboardError));
					});

				trackSendInvoice({ invoice, method: 'Link' });
			} catch (error) {
				dispatch(addErrorSnackbar(error));
			}
		},
		[isLoading, getPaymentLink, trackSendInvoice, dispatch, $t]
	);

	return { onCopyLink };
};

export const useInvoiceOverflowActions = (args: {
	origin: InvoiceTrackingOrigin;
	hasInvoicesEditPermission?: boolean;
}) => {
	const dispatch = useAppDispatch();
	const { $t } = useIntl();
	const theme = useTheme();

	const hasSuperbillsModule = useLegacyFeatureModule('Superbills');

	const invoiceEntities = useSelector(selectInvoiceEntities);
	const { logoUrl, provider, providerAddress, isPaymentEnabled } = useReadOnlyInvoice();

	const { download } = useReadOnlyInvoicePDF({ provider, providerAddress });
	const { onCopyLink } = useCopyLink();

	const [updateInvoiceStatus] = useUpdateInvoiceStatusMutation();
	const [deleteInvoice] = useDeleteInvoiceMutation();

	const [getInvoice] = useLazyGetInvoiceQuery();

	const onDownloadInvoice = useCallback(
		async (args: { invoiceId: string; invoice?: IInvoice }) => {
			try {
				const invoice =
					args.invoice ||
					invoiceEntities[args.invoiceId] ||
					(await getInvoice({ id: args.invoiceId, context: { skipQueryFn: true } }).unwrap());

				download(invoice, logoUrl);
			} catch (e) {}
		},
		[download, getInvoice, invoiceEntities, logoUrl]
	);

	const onEdit = useCallback(
		(args: { invoice?: IInvoice; invoiceId?: string; origin?: string }) =>
			dispatch(addModal({ type: 'CreateInvoice', data: args })),
		[dispatch]
	);

	const addPayment = useCallback(
		(args: { invoiceId: string }) => dispatch(openInvoice({ invoiceId: args.invoiceId, isAddPaymentOpen: true })),
		[dispatch]
	);

	const onUpdateStatus = useCallback(
		async (args: UpdateInvoiceStatusApiArg) => {
			try {
				await updateInvoiceStatus(args).unwrap();
			} catch (e) {
				dispatch(addErrorSnackbar(e));
			}
		},
		[dispatch, updateInvoiceStatus]
	);

	const onDelete = React.useCallback(
		(args: DeleteInvoiceApiArg) => {
			const deleteInvoiceAction = async () => {
				try {
					await deleteInvoice(args).unwrap();
				} catch (error) {
					const { errorCode } = unwrapError(error);
					if (errorCode === 'Unauthorised') {
						dispatch(addErrorSnackbarWithLangId(langIds.UnauthorisedInvoiceSnackbar));
					} else {
						dispatch(addErrorSnackbar(error));
					}
				}
			};

			dispatch(
				addModal({
					type: 'DeleteRestorableItem',
					data: {
						type: 'Invoice',
						context: (
							<strong>
								{$t({ id: langIds.InvoiceNumberFormat }, { number: args.context?.invoice?.number })}
							</strong>
						),
						onDelete: deleteInvoiceAction,
					},
				})
			);
		},
		[$t, deleteInvoice, dispatch]
	);

	const onCreateSuperbillReceipt = useCallback(
		(args: { invoice: IInvoice; contactId: string }) => () =>
			dispatch(addModal({ type: 'CreateSuperbillReceipt', data: args })),
		[dispatch]
	);

	const onEmailInvoice = useCallback(
		(args: { invoice?: IInvoice; invoiceId: string; sendOnlinePayment: boolean }) => () =>
			dispatch(addModal({ type: 'EmailInvoice', data: args })),
		[dispatch]
	);

	const getInvoiceActions = useCallback(
		({
			invoice,
			isPartialInvoice,
		}: GetInvoiceArgs): {
			overflowActions: IOverflowAction[];
			overflowSecondaryActions: IOverflowSecondaryAction[];
		} => {
			const isPaid = invoice.status === 'Paid';
			const isUnpaid = invoice.status === 'Unpaid';
			const isVoided = invoice.status === 'Void';
			const isSent = invoice.status === 'Sent';
			const isProcessing = invoice.status === 'Processing';

			const hasServiceReceipts = !isPartialInvoice && !invoice.serviceReceiptIds.length;

			const filterActionsWithoutPermission = (actions: IOverflowAction[]) =>
				actions.filter(
					({ key }) =>
						(ACTIONS_REQUIRING_PERMISSIONS.includes(key) && args.hasInvoicesEditPermission) ||
						!ACTIONS_REQUIRING_PERMISSIONS.includes(key)
				);

			return {
				overflowActions: filterActionsWithoutPermission([
					...(hasSuperbillsModule && isPaid && hasServiceReceipts
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.createSuperbillReceipt,
									icon: <ReceiptIcon />,
									label: $t({ id: langIds.CreateSuperbillReceipt }),
									onClick: onCreateSuperbillReceipt({
										invoice: invoice as IInvoice,
										contactId: invoice.contactId,
									}),
								},
							]
						: []),
					...(!isPaid && !isVoided && !isProcessing
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.edit,
									icon: <EditIcon />,
									label: $t({ id: langIds.Edit }),
									onClick: () =>
										onEdit({
											...(isPartialInvoice ? { invoiceId: invoice.id } : { invoice }),
											origin: args.origin,
										}),
								},
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.addPayment,
									icon: <PaymentRoundedIcon />,
									label: $t({ id: langIds.AddPayment }),
									onClick: () => addPayment({ invoiceId: invoice.id }),
								},
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.sendOnlinePayment,
									icon: <AttachEmailRoundedIcon />,
									label: $t({ id: langIds.SendPaymentLink }),
									onClick: onEmailInvoice({
										invoiceId: invoice.id,
										sendOnlinePayment: true,
										...(!isPartialInvoice && { invoice }),
									}),
								},
							]
						: []),
					...(isUnpaid || isSent
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.markAsVoid,
									icon: <BlockRoundedIcon />,
									label: $t({ id: langIds.MarkAsVoid }),
									onClick: () =>
										onUpdateStatus({
											id: invoice.id,
											dto: { status: 'Void' as const },
											...(!isPartialInvoice && { context: { invoice } }),
										}),
								},
							]
						: []),
					...(isVoided
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.markAsUnpaid,
									icon: <CreditCardOffRoundedIcon />,
									label: $t({ id: langIds.MarkAsUnpaid }),
									onClick: () =>
										onUpdateStatus({
											id: invoice.id,
											dto: { status: 'Unpaid' as const },
											...(!isPartialInvoice && { context: { invoice } }),
										}),
								},
							]
						: []),
					...(!isVoided
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.email,
									icon: <EmailRoundedIcon />,
									label: $t({ id: langIds.Email }),
									onClick: onEmailInvoice({
										invoiceId: invoice.id,
										sendOnlinePayment: false,
										...(!isPartialInvoice && { invoice }),
									}),
								},
							]
						: []),
					{
						key: INVOICE_ACTION_OVERFLOW_KEYS.downloadPdf,
						icon: <DownloadRoundedIcon />,
						label: $t({ id: langIds.DownloadPDF }),
						onClick: () =>
							onDownloadInvoice({ invoiceId: invoice.id, ...(!isPartialInvoice && { invoice }) }),
					},
					...(!isProcessing && !isVoided && isPaymentEnabled
						? [
								{
									key: INVOICE_ACTION_OVERFLOW_KEYS.copy,
									icon: <ContentCopyRounded />,
									label: $t({ id: langIds.CopyPaymentLink }),
									onClick: () => onCopyLink(invoice),
								},
							]
						: []),
				]),
				// CP-16281 - Remove delete action for invoices with Stripe payments
				overflowSecondaryActions: isProcessing
					? []
					: filterActionsWithoutPermission([
							{
								key: INVOICE_ACTION_OVERFLOW_KEYS.delete,
								icon: <DeleteForeverRoundedIcon />,
								label: $t({ id: langIds.Delete }),
								menuItemProps: { sx: { color: theme.palette.error.main } },
								onClick: () =>
									onDelete({
										id: invoice.id,
										context: {
											contactId: invoice.contactId,
											...(isPartialInvoice ? {} : { invoice }),
										},
									}),
							},
						]),
			};
		},
		[
			hasSuperbillsModule,
			$t,
			onCreateSuperbillReceipt,
			onEmailInvoice,
			isPaymentEnabled,
			theme.palette.error.main,
			args.hasInvoicesEditPermission,
			args.origin,
			onEdit,
			addPayment,
			onUpdateStatus,
			onDownloadInvoice,
			onCopyLink,
			onDelete,
		]
	);

	return {
		getInvoiceActions,
	};
};

const INVOICE_PAGINATION_QUERY_LIMIT = 20;

export const usePaginateInvoices = (limit = INVOICE_PAGINATION_QUERY_LIMIT) => {
	const [fetchInvoices, { isFetching, data }, { lastArg }] = useLazyGetInvoicesQuery();

	const [hasLoaded, setHasLoaded] = useState(false);

	const invoices = useSelector(selectInvoicesList);

	const hasMore = data?.pagination.hasMore ?? false;
	const offset = data?.pagination.offset ?? 0;
	const totalCount = data?.totalCount ?? 0;

	const initList = useCallback(
		async (query?: GetInvoicesApiArg['query']) => {
			try {
				return await fetchInvoices({
					query: { limit, ...query, offset: 0 },
					context: { isInitialLoad: true },
				}).unwrap();
			} finally {
				setHasLoaded(true);
			}
		},
		[fetchInvoices, limit]
	);

	// Shouldn't be called if hook invoices hasn't been initialized yet
	const loadMore = useCallback(() => {
		if (!hasMore || !lastArg?.query) return;
		const nextOffset = offset + limit;

		return fetchInvoices({
			// ensures we're using the args used during the init but with modified offset
			query: { ...lastArg.query, offset: nextOffset },
			context: { isInitialLoad: false },
		});
	}, [fetchInvoices, hasMore, lastArg.query, limit, offset]);

	const hasActiveFilters = useMemo(() => {
		if (!lastArg?.query) return false;

		const dateRange = lastArg.query.dateRange;

		const query = omit(lastArg.query, ['offset', 'limit', 'sort', 'dateRange']);

		return getHasActiveFilters({
			...query,
			toDate: dateRange?.toDate,
			fromDate: dateRange?.fromDate,
		});
	}, [lastArg?.query]);

	const hasNoResults = !invoices.length && !isFetching && hasActiveFilters;
	const isEmpty = !invoices.length && !isFetching && !hasActiveFilters;

	return {
		invoices,
		initList,
		loadMore,
		hasMore,
		isFetching,
		totalCount,
		isUninitializedOrLoading: !hasLoaded,
		hasNoResults,
		isEmpty,
	};
};

export const useGenerateInvoice = () => {
	const [isGeneratingInvoice, setIsGeneratingInvoice] = useState(false);

	const billingSettings = useSelector(selectBillingSettings);
	const currencyCode = useSelector(selectCurrencyCode);

	const getTasksQueriesCacheEntries = useSelector(selectGetTasksQueryCacheEntries);

	const { timezones } = useSelector(selectCalendarSettings);

	const dispatch = useAppDispatch();
	const getDefaultInvoiceTemplate = useGetDefaultInvoiceTemplate();
	const getDefaultTaxRate = useGetDefaultTaxRate();

	const [splitTaskOccurrence] = useSplitTaskOccurrenceMutation();
	const [createInvoice] = useCreateInvoiceMutation();
	const [fetchBillables] = useLazyGetBillablesQuery();
	const [fetchTask] = useLazyGetTaskQuery();

	const { trackCreatedInvoice } = useInvoiceAnalytics();

	const handleGenerateInvoice = async (payload: IGenerateInvoiceFromApptRequest) => {
		try {
			if (!billingSettings) throw new Error('No billing settings found');
			const { taxName, taxNumber } = billingSettings;

			setIsGeneratingInvoice(true);

			const applicationState = getStore().getState();
			const taskQueryFilter = selectTasksFilterQuery(applicationState);
			const cachedTask = selectTaskByIdAndContactId(
				applicationState,
				payload.taskId,
				payload.contactId,
				taskQueryFilter
			);

			const updatedTask: ITask = !!payload.rRule
				? await splitTaskOccurrence({
						taskId: payload.taskId,
						dto: { occurrenceDateTimeUtc: zonedTimeToUtc(payload.startDate, timezones?.primary || '') },
					}).unwrap()
				: (cachedTask ?? (await fetchTask({ id: payload.taskId }).unwrap()));

			const defaultInvoiceTemplate = await getDefaultInvoiceTemplate();
			const defaultTaxRate = await getDefaultTaxRate();

			const billables: GetBillablesResponse['items'] = [];

			if (updatedTask.isBillingV2) {
				try {
					const isSplitTask = updatedTask.id !== payload.taskId;
					const amountIsNotZero = getItemSnapshotsTotal(updatedTask) !== 0;
					const shouldPollUntilNonEmptyResponse = isSplitTask && amountIsNotZero;

					const billableResponse = await fetchBillables({
						query: {
							limit: 1,
							status: 'uncharged',
							contactId: payload.contactId,
							taskId: updatedTask.id,
						},
						polling: { shouldPollUntilNonEmptyResponse },
					}).unwrap();

					billables.push(...billableResponse.items);
				} catch (error) {
					logger.error(error);
				}
			}

			const billable = billables.find((billable) => billable.taskId === updatedTask.id);

			const getLineItems = () => {
				if (updatedTask.isBillingV2) {
					const freeServices = (updatedTask.itemsSnapshot ?? [])
						.filter((item) => item.price === 0)
						.map((item) =>
							formatItemSnapshotToInvoiceLineItem({
								task: updatedTask,
								service: item,
								taxRate: defaultTaxRate,
								defaultCurrencyCode: currencyCode,
							})
						);

					return [
						...convertBillableItemsToInvoiceLineItems(
							billable?.items ?? [],
							updatedTask.itemsSnapshot
						).filter((item) => item.price !== 0),
						...freeServices,
					];
				}

				if (updatedTask.itemsSnapshot) {
					return updatedTask.itemsSnapshot.map((item) =>
						formatItemSnapshotToInvoiceLineItem({
							task: updatedTask,
							service: item,
							taxRate: defaultTaxRate,
							defaultCurrencyCode: currencyCode,
						})
					);
				}

				return [];
			};

			const lineItems: ILineItem[] = getLineItems();

			const saveInvoiceRequest: ISaveInvoiceRequest = {
				title: defaultInvoiceTemplate?.title ?? '',
				issueDate: new Date(),
				contactIds: [payload.contactId],
				lineItems,
				staffIds: updatedTask.staff ? updatedTask.staff.map((x) => x.id) : [],
				taskId: updatedTask.id,
				dueDate: addDays(new Date(), defaultInvoiceTemplate?.overdueTerm ?? 0),
				description: defaultInvoiceTemplate?.description ?? '',
				currencyCode: lineItems?.[0]?.currencyCode || currencyCode,
				taxName,
				taxNumber,
				posoNumber: payload.posoNumber,
				isBillingV2: updatedTask.isBillingV2,
			};

			const createInvoiceResult = await createInvoice({ dto: saveInvoiceRequest }).unwrap();

			trackCreatedInvoice(createInvoiceResult[0], 'appointment', { taskId: updatedTask.id });

			getTasksQueriesCacheEntries.forEach((cache) => {
				dispatch(
					tasksService.util.updateQueryData('getTasks', cache.args as TasksGetApiArg, (draft) =>
						modifyInvoicesInTaskCache.onCreate(draft, createInvoiceResult)
					)
				);
			});

			dispatch(tasksService.util.invalidateTags([{ type: 'Task', id: updatedTask.id }]));

			dispatch(
				openInvoice({
					invoiceId: createInvoiceResult[0].id,
					isAddPaymentOpen: payload.isAddPaymentOpen,
					isLogoCacheIgnored: true,
				})
			);

			return { task: updatedTask, invoice: createInvoiceResult[0] };
		} catch (error) {
			const { errorCode } = unwrapError(error);
			if (errorCode === 'Unauthorised') {
				dispatch(addErrorSnackbarWithLangId(langIds.UnauthorisedInvoiceSnackbar));
			} else {
				dispatch(addErrorSnackbar(error));
			}
			logNonRtkQError(error);
		} finally {
			setIsGeneratingInvoice(false);
		}
	};

	return {
		handleGenerateInvoice,
		isGeneratingInvoice,
	};
};

export const useGetDefaultInvoiceTemplate = () => {
	const [fetchInvoiceTemplates, { data: invoiceTemplates }] = useLazyGetInvoiceTemplatesQuery({});

	return useCallback(async () => {
		if (invoiceTemplates) {
			return invoiceTemplates.items.find(({ isDefault }) => isDefault);
		}

		// Fetch invoice templates if it was never initialized
		const invoiceTemplatesResponse = await fetchInvoiceTemplates({}).unwrap();
		return invoiceTemplatesResponse.items.find(({ isDefault }) => isDefault);
	}, [fetchInvoiceTemplates, invoiceTemplates]);
};

export const useGetDefaultTaxRate = () => {
	const [fetchTaxRates, { data: taxRates }] = useLazyGetTaxRatesQuery({});

	return useCallback(async () => {
		if (taxRates) {
			return taxRates.find(({ isDefault }) => isDefault);
		}

		// Fetch tax rates if it was never initialized
		const taxRatesResponse = await fetchTaxRates({}).unwrap();
		return taxRatesResponse.find(({ isDefault }) => isDefault);
	}, [fetchTaxRates, taxRates]);
};

export const useGetBillingInvoiceListSearchParams = () => {
	const [searchParams] = useSearchParams();

	return useMemo(() => {
		const searchTerm = searchParams.get('searchTerm') ?? '';
		const contactIds = searchParams.getAll('contactIds');
		const status = searchParams.get('status') as InvoiceStatusFilter;
		const staffIds = searchParams.getAll('staffIds');

		const dateId = searchParams.get('dateId');
		const toDate = searchParams.get('toDate');
		const fromDate = searchParams.get('fromDate');

		const dateRangeOptions = getDefaultDateRangeOptions();

		const sortField = searchParams.get('sortField') as InvoiceListSortKey | null;
		const sortDirection = searchParams.get('sortDirection');

		const validatedSortField: InvoiceListSortKey =
			!!sortField && INVOICES_LIST_SORT_KEYS.includes(sortField) ? sortField : 'issueDate';
		const validatedSortDirection: SortDirection =
			!!sortDirection && (sortDirection === 'asc' || sortDirection === 'desc') ? sortDirection : 'desc';

		const sort = { field: validatedSortField, direction: validatedSortDirection };

		return {
			sort,
			searchTerm,
			status,
			contactIds,
			staffIds,
			queryDateRange: parseDateRangeFilterValue(dateRangeOptions, dateId, toDate, fromDate),
		};
	}, [searchParams]);
};
