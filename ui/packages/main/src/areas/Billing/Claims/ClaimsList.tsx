import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useIntl } from 'react-intl';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router';
import { Link as ReactRouterLink, useSearchParams } from 'react-router-dom';
import { Box, Chip, Link, Stack, Tooltip, Typography } from '@mui/material';
import { differenceInMonths } from 'date-fns';
import { pick, upperFirst } from 'lodash';

import StaffAvatar from 'components/common/avatar/StaffAvatar';
import StandardAvatarGroup from 'components/common/avatar/StandardAvatarGroup';
import LocalizedDate from 'components/common/LocalizedDate';
import Table from 'components/common/Table';
import NoSearchResults from 'components/NoSearchResults';
import { CustomActiveToolbarFilter, SearchFilterToolbar } from 'components/SearchFilterToolbar';
import { useDateRangeFilter, useStaffFilter } from 'components/SearchFilterToolbar/hooks';
import { useClientsFilter } from 'components/SearchFilterToolbar/hooks/useClientsFilter';
import { useSelectFilter } from 'components/SearchFilterToolbar/hooks/useSelectFilter';
import { useShowFilter } from 'components/SearchFilterToolbar/hooks/useShowFilter';
import { MobileDateRangeComponent } from 'components/SearchFilterToolbar/MobileDateRangeFilter';
import { TabletDateRangeFilter } from 'components/SearchFilterToolbar/TabletDateRangeFilter';
import { IFilterOption } from 'components/SearchFilterToolbar/types';
import { getTranslatedLabel } from 'components/SearchFilterToolbar/utils';
import { useSidePanel } from 'components/sidePanels/useSidePanel';
import StatusTag from 'components/StatusTag';
import langIds from 'lang/langIds';
import { useAppDispatch } from 'store';
import { selectCurrencyCode } from 'store/api/billingSettings/selectors';
import { selectInsuranceClaimsColumns } from 'store/api/insuranceClaims/selectors';
import {
	DEFAULT_INSURANCE_CLAIM_COLUMNS,
	resetInsuranceClaimColumns,
	updateInsuranceClaimColumns,
} from 'store/api/insuranceClaims/slice';
import { useHasPermission } from 'store/api/permissions/hooks';
import { compareArrayObjectKeys } from 'util/compareArrayObjectKeys';
import { useAssignedStaffIdsQueryByPermission } from 'util/hooks/useAssignedStaffIdsQueryByPermission';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { getClaimDetailsRoute } from 'util/routes';
import useFormatPrice from 'util/useFormatPrice';

import { DefaultClaimType } from '../../Insurance/Claims/constants';
import { useClaimOverflowActions } from '../../Insurance/Claims/hooks/useClaimOverflowActions';
import { BillingClientTableCell } from '../BillingClientTableCell';
import { CLAIM_LIST_SORT_MAPPER, CLAIMS_LIST_MAPPING_FILTER_KEYS, MOBILE_CLAIM_LIST_COLUMN_KEYS } from '../constants';
import { useBillingListsQueryFilter } from '../hooks';
import { NoBillingItemsView } from '../NoBillingItemsView';
import { TotalItemsCount } from '../TotalItemsCount';
import { UninitializedBillingView } from '../UninitializedBillingView';
import { setDateRangeFilterSearchParam } from '../utils';

import { useGetBillingClaimsListSearchParams, useGetPaginateBillingClaimsList, usePayersFilter } from './hooks';
import { getClaimStatusFilterOptions } from './utils';

const ClaimsList = () => {
	const isXSmallScreenDown = useBreakpoint('sm');
	const isSmallScreenDown = useBreakpoint('md');
	const isMediumScreenDown = useBreakpoint('lg');

	const { $t } = useIntl();
	const navigate = useNavigate();
	const dispatch = useAppDispatch();
	const formatPrice = useFormatPrice();
	const currencyCode = useSelector(selectCurrencyCode);
	const hasInvoicesEditPermission = useHasPermission('invoicesEdit');
	const assignedStaff = useAssignedStaffIdsQueryByPermission('invoicesView');

	const { openSidePanel: openCreateClaimSidePanel } = useSidePanel('CreateClaimSidePanel');

	const [, setSearchParams] = useSearchParams();

	const { searchTerm, status, staffIds, contactIds, queryDateRange, sort, payerIds } =
		useGetBillingClaimsListSearchParams();
	const { handleOnFilter, handleOnDateRangeChange, handleOnSearch, handleOnColumnSort, handleClearFields } =
		useBillingListsQueryFilter(CLAIMS_LIST_MAPPING_FILTER_KEYS);
	const {
		claimItems,
		hasMore,
		isFetchingClaims,
		handleLoadMore,
		totalCount,
		handleGetInsuranceClaims,
		isEmpty,
		hasNoResults,
		isUninitializedOrLoading,
	} = useGetPaginateBillingClaimsList();
	const getClaimOverflowActions = useClaimOverflowActions();

	const insuranceClaimColumns = useSelector(selectInsuranceClaimsColumns);
	const insuranceClaimColumnsWithLabel = useMemo(() => {
		const columns = isSmallScreenDown
			? MOBILE_CLAIM_LIST_COLUMN_KEYS.map((key) =>
					DEFAULT_INSURANCE_CLAIM_COLUMNS.find((column) => column.key === key)
				).filter((column): column is IInsuranceClaimsColumnType => Boolean(column))
			: insuranceClaimColumns;

		const areKeysEqual = compareArrayObjectKeys(DEFAULT_INSURANCE_CLAIM_COLUMNS, insuranceClaimColumns);
		const finalColumns = areKeysEqual || isSmallScreenDown ? columns : DEFAULT_INSURANCE_CLAIM_COLUMNS;

		if (!areKeysEqual) {
			dispatch(resetInsuranceClaimColumns());
		}

		return finalColumns.map((column) => ({
			...column,
			label: getTranslatedLabel(upperFirst(column.key)),
			sortFieldName: CLAIM_LIST_SORT_MAPPER[column.key],
		}));
	}, [dispatch, insuranceClaimColumns, isSmallScreenDown]);
	const filteredInsuranceClaimColumns = useMemo(
		() => insuranceClaimColumnsWithLabel.filter((column) => !column.hidden),
		[insuranceClaimColumnsWithLabel]
	);

	const insuranceClaimRows: ITableRow[] = useMemo(() => {
		return claimItems.map((claimItem) => {
			const isMoreThanAMonthOld =
				!!claimItem.lastSubmittedDateTimeUtc &&
				differenceInMonths(new Date(claimItem.lastSubmittedDateTimeUtc), new Date()) > 1;

			const rowMapper: { [key: string]: ITableItem } = {
				date: { key: 'date', label: <LocalizedDate date={claimItem.fromDate} /> },
				claimNumber: {
					key: 'claimNumber',
					label: (
						<Link
							sx={{ fontWeight: 'medium' }}
							component={ReactRouterLink}
							to={getClaimDetailsRoute(claimItem.contactId, claimItem.type, claimItem.id)}
						>
							{claimItem.number}
						</Link>
					),
				},
				client: {
					key: 'client',
					label: isSmallScreenDown ? (
						<>
							<Stack spacing={1} direction='row' alignItems='center'>
								<BillingClientTableCell fullName={claimItem.client?.fullName} />

								<StatusTag status={claimItem.status} size='extraSmall' />
							</Stack>
							<Link
								sx={{ fontWeight: 'medium' }}
								component={ReactRouterLink}
								to={getClaimDetailsRoute(claimItem.contactId, claimItem.type, claimItem.id)}
							>
								{claimItem.number}
							</Link>
						</>
					) : (
						<BillingClientTableCell fullName={claimItem.client?.fullName} />
					),
				},
				payer: { key: 'payer', label: claimItem.payer?.name },
				amount: { key: 'amount', label: formatPrice(claimItem.amount, claimItem.currencyCode ?? currencyCode) },
				status: {
					key: 'status',
					label: <StatusTag status={claimItem.status} size={isXSmallScreenDown ? 'small' : 'extraSmall'} />,
				},
				submittedDate: {
					key: 'submittedDate',
					label: !!claimItem.lastSubmittedDateTimeUtc ? (
						<Typography
							variant={isSmallScreenDown ? 'body1' : 'tableCell'}
							{...(isMoreThanAMonthOld && { color: 'error' })}
						>
							<LocalizedDate date={claimItem.lastSubmittedDateTimeUtc} />
						</Typography>
					) : null,
				},
				method: {
					key: 'method',
					label: (
						<Chip
							variant='tag'
							label={getTranslatedLabel(claimItem.submissionMethod)}
							size={isXSmallScreenDown ? 'small' : 'extraSmall'}
						/>
					),
				},
				team: {
					key: 'team',
					label: !!claimItem.staff?.length ? (
						<Tooltip
							title={
								<Stack>
									{claimItem.staff.map((staff) => (
										<Box key={staff.id} component='span'>
											{staff.fullName}
										</Box>
									))}
								</Stack>
							}
						>
							<StandardAvatarGroup max={4} size='xs' sx={{ justifyContent: 'flex-end' }}>
								{claimItem.staff.map((staff) =>
									staff.id ? <StaffAvatar key={staff.id} personId={staff.id} size='xs' /> : null
								)}
							</StandardAvatarGroup>
						</Tooltip>
					) : null,
				},
			};

			return {
				key: claimItem.id,
				id: claimItem.id,
				items: filteredInsuranceClaimColumns.map((column) => {
					const mappedValue = rowMapper[column.key];

					return {
						...mappedValue,
						label: mappedValue.label ?? (isSmallScreenDown ? '-' : ''),
					};
				}),
				onRowClick: () => navigate(getClaimDetailsRoute(claimItem.contactId, claimItem.type, claimItem.id)),
				...getClaimOverflowActions({
					contactId: claimItem.contactId,
					hasInvoicesEditPermission,
					claim: {
						id: claimItem.id,
						claimStatus: claimItem.status,
						type: claimItem.type,
						date: claimItem.fromDate || '',
						number: claimItem.number,
						submissionMethod: claimItem.submissionMethod,
					},
				}),
			};
		});
	}, [
		claimItems,
		currencyCode,
		filteredInsuranceClaimColumns,
		formatPrice,
		getClaimOverflowActions,
		hasInvoicesEditPermission,
		isSmallScreenDown,
		navigate,
		isXSmallScreenDown,
	]);

	const claimStatusFilterOptions = useMemo(() => getClaimStatusFilterOptions(), []);

	const handleOnColumnReset = useCallback(() => dispatch(resetInsuranceClaimColumns()), [dispatch]);
	const handleOnColumnChange = useCallback(
		(columns: ColumnType[]) =>
			dispatch(
				updateInsuranceClaimColumns(
					columns.map(({ key, hidden }) => ({ key: key as ClaimListColumnType, hidden }))
				)
			),
		[dispatch]
	);

	const payersFilter = usePayersFilter({ value: payerIds });
	const claimStatusFilter = useSelectFilter({
		value: status,
		labelId: langIds.Status,
		options: claimStatusFilterOptions,
		multiple: true,
	});
	const clientsFilter = useClientsFilter({ value: contactIds, contactArgs: { assignedStaff } });
	const staffFilter = useStaffFilter({ value: staffIds });
	const dateRangeFilter = useDateRangeFilter({ value: queryDateRange, onChange: handleOnDateRangeChange });
	const showFilter = useShowFilter({
		columns: insuranceClaimColumnsWithLabel,
		hiddenVisibilityToggle: ['number'],
		onChange: handleOnColumnChange,
		onUseDefault: handleOnColumnReset,
	});

	const mobileDateRangeValueRef = useRef<IFilterOption | undefined>(dateRangeFilter.value[0]);

	const currentFilters = useMemo(
		() => ({
			clientsFilter,
			staffFilter,
			claimStatusFilter,
			payersFilter,
			...(!isMediumScreenDown ? { dateRange: dateRangeFilter, show: showFilter } : {}),
		}),
		[claimStatusFilter, clientsFilter, dateRangeFilter, isMediumScreenDown, payersFilter, showFilter, staffFilter]
	);

	const customActiveFilters = useMemo(() => {
		const activeFilters: CustomActiveToolbarFilter[] = [];

		if (queryDateRange.id !== 'all') {
			activeFilters.push({
				label: queryDateRange.label,
				onClear: () => handleClearFields(CLAIMS_LIST_MAPPING_FILTER_KEYS.dateRange.keys),
			});
		}

		return activeFilters;
	}, [handleClearFields, queryDateRange.id, queryDateRange.label]);

	const handleMobileOnClose = useCallback(() => {
		mobileDateRangeValueRef.current = dateRangeFilter.value[0];
	}, [dateRangeFilter.value]);

	const handleMobileParamsUpdate = useCallback(
		(updatedParams: URLSearchParams) => {
			setDateRangeFilterSearchParam(mobileDateRangeValueRef.current, updatedParams);
			setSearchParams(updatedParams);
		},
		[setSearchParams]
	);

	const handleClaimOnFilter = useCallback(
		(value: Parameters<typeof handleOnFilter>[0], reason: Parameters<typeof handleOnFilter>[1] = 'change') => {
			const callback = isMediumScreenDown ? handleMobileParamsUpdate : undefined;
			handleOnFilter(value, reason, callback);
		},
		[handleMobileParamsUpdate, handleOnFilter, isMediumScreenDown]
	);

	useEffect(() => {
		handleGetInsuranceClaims({
			searchTerm,
			status,
			staffIds,
			contactIds,
			sort,
			payerIds,
			...pick(queryDateRange, ['fromDate', 'toDate']),
		});
	}, [contactIds, handleGetInsuranceClaims, payerIds, queryDateRange, searchTerm, sort, staffIds, status]);

	if (isUninitializedOrLoading) {
		return <UninitializedBillingView />;
	}

	if (isEmpty) {
		return (
			<NoBillingItemsView
				headerId={langIds.NoClaimsHeading}
				action={{
					labelId: langIds.NewClaim,
					onClick: () => openCreateClaimSidePanel({ type: DefaultClaimType, origin: 'claim list' }),
				}}
			/>
		);
	}

	return (
		<Box display='flex' flexDirection='column' height='100%'>
			<SearchFilterToolbar
				isMobile={isXSmallScreenDown}
				isTablet={isMediumScreenDown}
				searchTerm={searchTerm}
				searchPlaceholder={$t({ id: langIds.SearchClaims })}
				startAdornment={<TotalItemsCount totalCountLabelId={langIds.NumberOfClaims} totalCount={totalCount} />}
				filters={currentFilters}
				customFilters={{
					mobile: [
						{
							label: $t({ id: langIds.DateRange }),
							filterKey: 'dateRange',
							component: (
								<MobileDateRangeComponent
									ref={mobileDateRangeValueRef}
									defaultValue={mobileDateRangeValueRef.current}
								/>
							),
						},
					],
					tablet: [
						{
							label: $t({ id: langIds.DateRange }),
							filterKey: 'dateRange',
							component: (
								<TabletDateRangeFilter
									ref={mobileDateRangeValueRef}
									filter={dateRangeFilter}
									defaultValue={mobileDateRangeValueRef.current}
								/>
							),
						},
					],
				}}
				onSearch={handleOnSearch}
				onFilter={handleClaimOnFilter}
				customActiveFilters={customActiveFilters}
				onMobileClose={handleMobileOnClose}
			/>
			{hasNoResults ? (
				<Box flexGrow={1}>
					<NoSearchResults />
				</Box>
			) : (
				<Table
					columns={filteredInsuranceClaimColumns}
					rows={insuranceClaimRows}
					hasMore={!!hasMore}
					isLoading={isFetchingClaims}
					stickyHeader
					seamless
					sorting={sort}
					onSort={handleOnColumnSort}
					loadMore={handleLoadMore}
				/>
			)}
		</Box>
	);
};

export default ClaimsList;
