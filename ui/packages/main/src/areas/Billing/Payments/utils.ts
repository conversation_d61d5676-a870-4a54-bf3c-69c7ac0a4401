import { IFilterOption } from 'components/SearchFilterToolbar/types';
import { getTranslatedLabel, transformToFilterOption } from 'components/SearchFilterToolbar/utils';
import { intl } from 'lang';
import langIds from 'lang/langIds';
import { localizeDate } from 'util/date';
import { getEstimatedArrivalDate, getPaymentTypeLabel } from 'util/invoice';

import { PAYMENT_PROVIDERS, PAYMENT_STATUSES, PAYMENT_TYPES } from '../constants';

export const getPaymentProviderLabel = (paymentProvider: PaymentProvider) => {
	const paymentProviderMap = {
		Manual: intl.formatMessage({ id: langIds.External }),
		CustomerBalance: intl.formatMessage({ id: langIds.CustomerBalance }),
		Insurance: intl.formatMessage({ id: langIds.Insurance }),
		// Don't translate these since these are proper nouns
		Stripe: 'Stripe',
		ChangeHealthcare: 'Change Healthcare',
		ClaimMD: 'ClaimMD',
	};

	return paymentProviderMap[paymentProvider] ?? paymentProvider;
};

export const getPaymentLabel = (payoutStatus: PayoutStatus) => {
	const labels = {
		Pending: intl.formatMessage({ id: langIds.Pending }),
		InTransit: intl.formatMessage({ id: langIds.InTransit }),
		Paid: intl.formatMessage({ id: langIds.PaidOut }),
		Failed: intl.formatMessage({ id: langIds.Failed }),
	};

	return payoutStatus ? labels[payoutStatus] || payoutStatus : '';
};

export const getPaymentDate = ({ type, payoutDateUtc, paymentDate }: IPayment) => {
	if (type === 'card' && payoutDateUtc) {
		return getEstimatedArrivalDate(payoutDateUtc);
	}

	if (type !== 'card') {
		return localizeDate(paymentDate, 'short');
	}

	return null;
};

export const getPaymentStatusFilterOptions = (): IFilterOption[] =>
	transformToFilterOption(PAYMENT_STATUSES).map((option) => {
		const label = getTranslatedLabel(`${option.label}`);
		return { ...option, label };
	});

export const getPaymentTypeFilterOptions = (): IFilterOption[] =>
	transformToFilterOption(PAYMENT_TYPES).map((option) => {
		const label =
			option.label === 'Other'
				? intl.formatMessage({ id: langIds.Other })
				: getPaymentTypeLabel(`${option.label}`);
		return { ...option, label };
	});

export const getPaymentProviderFilterOptions = (): IFilterOption[] =>
	transformToFilterOption(PAYMENT_PROVIDERS).map((option) => {
		const label = getPaymentProviderLabel(option.label as PaymentProvider);
		return { ...option, label };
	});
