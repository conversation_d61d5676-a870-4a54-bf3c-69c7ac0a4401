export type TranslationLanguage = {
	[property in keyof typeof langIds]: string;
};

export type TranslationKey = keyof typeof langIds;

function createSamePairedObject<T extends Readonly<Record<PropertyKey, PropertyKey>>>(
	obj: T,
	..._lockParams: T extends { [K in keyof T]: K } ? [] : ['INVALID_PAIRED_OBJECT']
): T {
	return obj as any;
}

const langIds = createSamePairedObject({
	QuickThemeSwitcherColorLapis: 'QuickThemeSwitcherColorLapis',
	QuickThemeSwitcherColorFushcia: 'QuickThemeSwitcherColorFushcia',
	QuickThemeSwitcherColorSquash: 'QuickThemeSwitcherColorSquash',
	QuickThemeSwitcherColorMoss: 'QuickThemeSwitcherColorMoss',
	QuickThemeSwitcherColorBasil: 'QuickThemeSwitcherColorBasil',
	QuickThemeSwitcherColorRose: 'QuickThemeSwitcherColorRose',
	QuickThemeSwitcherColorBlueberry: 'QuickThemeSwitcherColorBlueberry',
	ThemeColorSystemMode: 'ThemeColorSystemMode',
	Asking: 'Asking',
	And: 'And',
	Address: 'Address',
	AddSignature: 'AddSignature',
	AddCaption: 'AddCaption',
	AddQuestion: 'AddQuestion',
	AddColumn: 'AddColumn',
	AddRow: 'AddRow',
	DuplicateColumn: 'DuplicateColumn',
	DeleteColumn: 'DeleteColumn',
	ChangeLayout: 'ChangeLayout',
	Pen: 'Pen',
	Marker: 'Marker',
	Eraser: 'Eraser',
	Highlighter: 'Highlighter',
	MoveUp: 'MoveUp',
	MoveDown: 'MoveDown',
	Reset: 'Reset',
	UntitledSection: 'UntitledSection',
	AddSignaturePlaceholder: 'AddSignaturePlaceholder',
	FormsAndAgreementsValidationMessage: 'FormsAndAgreementsValidationMessage',
	SetTemplateAsDefaultIntakeSuccess: 'SetTemplateAsDefaultIntakeSuccess',
	RemoveTemplateAsDefaultIntakeSuccess: 'RemoveTemplateAsDefaultIntakeSuccess',
	SectionFormSecondaryText: 'SectionFormSecondaryText',
	Crop: 'Crop',
	Caption: 'Caption',
	SignaturePlaceholder: 'SignaturePlaceholder',
	ElectronicSignature: 'ElectronicSignature',
	SignatureInfoTooltip: 'SignatureInfoTooltip',
	ProcessingRequest: 'ProcessingRequest',
	SignatureCaptureError: 'SignatureCaptureError',
	SetAsIntakeDefault: 'SetAsIntakeDefault',
	RemoveFromIntakeDefault: 'RemoveFromIntakeDefault',
	UnableToCompleteAction: 'UnableToCompleteAction',
	ConfirmationModalDeleteSectionMessage: 'ConfirmationModalDeleteSectionMessage',
	Or: 'Or',
	CreateNewTemplate: 'CreateNewTemplate',
	NewNote: 'NewNote',
	Clear: 'Clear',
	MoreActions: 'MoreActions',
	More: 'More',
	Thin: 'Thin',
	Thick: 'Thick',
	UploadFiles: 'UploadFiles',
	LoadMore: 'LoadMore',
	AddRelationship: 'AddRelationship',
	TemplateGallery: 'TemplateGallery',
	MyTemplates: 'MyTemplates',
	NoTemplate: 'NoTemplate',
	NoteDuplicateSuccess: 'NoteDuplicateSuccess',
	PracticeTemplate: 'PracticeTemplate',
	TemplateTitle: 'TemplateTitle',
	TemplateType: 'TemplateType',
	UseTemplate: 'UseTemplate',
	CopyTemplateLink: 'CopyTemplateLink',
	TryAgain: 'TryAgain',
	DuplicateTemplateSuccess: 'DuplicateTemplateSuccess',
	CopyTemplateLinkSuccess: 'CopyTemplateLinkSuccess',
	AddTag: 'AddTag',
	AddField: 'AddField',
	AddCustomField: 'AddCustomField',
	AddNewSection: 'AddNewSection',
	AddDisplayName: 'AddDisplayName',
	AddDescription: 'AddDescription',
	Attachment: 'Attachment',
	Template: 'Template',
	SelectTemplate: 'SelectTemplate',
	DefinedTemplateType: 'DefinedTemplateType',
	DisplayName: 'DisplayName',
	Description: 'Description',
	SaveTemplate: 'SaveTemplate',
	TranscriptionText: 'TranscriptionText',
	SelectTags: 'SelectTags',
	VisibleTo: 'VisibleTo',
	OrderBy: 'OrderBy',
	CollectionName: 'CollectionName',
	DeleteTemplateTitle: 'DeleteTemplateTitle',
	DeletePublicTemplateContent: 'DeletePublicTemplateContent',
	DeleteTemplateContent: 'DeleteTemplateContent',
	DeleteCollection: 'DeleteCollection',
	SaveCollection: 'SaveCollection',
	AddCollection: 'AddCollection',
	EditTemplate: 'EditTemplate',
	ShareWithCommunity: 'ShareWithCommunity',
	SeeTemplateLibrary: 'SeeTemplateLibrary',
	RecentlyUsed: 'RecentlyUsed',
	Filter: 'Filter',
	Filters: 'Filters',
	SaveAndClose: 'SaveAndClose',
	SaveAndExit: 'SaveAndExit',
	CopyToTeamTemplates: 'CopyToTeamTemplates',
	RemoveFromCommunity: 'RemoveFromCommunity',
	RemoveTemplateFromCommunity: 'RemoveTemplateFromCommunity',
	Collection: 'Collection',
	Collections: 'Collections',
	CapturingSignature: 'CapturingSignature',
	Edited: 'Edited',
	Type: 'Type',
	Repeats: 'Repeats',
	Location: 'Location',
	LocationDescription: 'LocationDescription',
	NoLocationsFound: 'NoLocationsFound',
	OptimizeServiceTimes: 'OptimizeServiceTimes',
	BufferBeforeTime: 'BufferBeforeTime',
	BufferTime: 'BufferTime',
	BufferAfterTime: 'BufferAfterTime',
	BufferAndLabel: 'BufferAndLabel',
	BufferAppointmentLabel: 'BufferAppointmentLabel',
	BufferTimeViewLabel: 'BufferTimeViewLabel',
	StartTimeIncrements: 'StartTimeIncrements',
	StartTimeIncrementsView: 'StartTimeIncrementsView',
	LocationOfService: 'LocationOfService',
	LocationOfServiceRecommendedActionInfo: 'LocationOfServiceRecommendedActionInfo',
	AvailableLocations: 'AvailableLocations',
	Locations: 'Locations',
	Remove: 'Remove',
	By: 'By',
	Search: 'Search',
	Create: 'Create',
	Close: 'Close',
	CreateNew: 'CreateNew',
	CreateNewField: 'CreateNewField',
	Cancel: 'Cancel',
	Client: 'Client',
	Contact: 'Contact',
	Clients: 'Clients',
	Contacts: 'Contacts',
	Relationship: 'Relationship',
	Relationships: 'Relationships',
	RelationshipDetails: 'RelationshipDetails',
	New: 'New',
	Next: 'Next',
	All: 'All',
	VideoCall: 'VideoCall',
	VideoCalls: 'VideoCalls',
	AddVideoCall: 'AddVideoCall',
	AddVideoOrVoiceCall: 'AddVideoOrVoiceCall',
	Zoom: 'Zoom',
	CarepatronApp: 'CarepatronApp',
	ClientMustHavePortalAccessErrorText: 'ClientMustHavePortalAccessErrorText',
	ClientMustHaveEmaillAccessErrorText: 'ClientMustHaveEmaillAccessErrorText',
	ClientMustHaveZoomAppConnectedErrorText: 'ClientMustHaveZoomAppConnectedErrorText',
	Add: 'Add',
	Edit: 'Edit',
	View: 'View',
	EditTags: 'EditTags',
	AddTags: 'AddTags',
	FileTags: 'FileTags',
	FileTagsHelper: 'FileTagsHelper',
	UpdateStatus: 'UpdateStatus',
	Done: 'Done',
	EditDetails: 'EditDetails',
	EditPersonalDetails: 'EditPersonalDetails',
	EditArrangements: 'EditArrangements',
	EditContactDetails: 'EditContactDetails',
	EditStaffDetails: 'EditStaffDetails',
	EditStaffDetailsCantUpdatedEmailTooltip: 'EditStaffDetailsCantUpdatedEmailTooltip',
	EditProviderDetails: 'EditProviderDetails',
	Goto: 'Goto',
	ManageTags: 'ManageTags',
	ManageAllClientTags: 'ManageAllClientTags',
	ManageAllNoteTags: 'ManageAllNoteTags',
	ManageAllTemplateTags: 'ManageAllTemplateTags',
	TagSelectorNoOptionsText: 'TagSelectorNoOptionsText',
	Customize: 'Customize',
	Billing: 'Billing',
	Save: 'Save',
	Saved: 'Saved',
	AppointmentSaved: 'AppointmentSaved',
	AppointmentUndone: 'AppointmentUndone',
	Publish: 'Publish',
	Complete: 'Complete',
	CompleteAndLock: 'CompleteAndLock',
	ActiveTags: 'ActiveTags',
	PublishedNotesAreNotAutosaved: 'PublishedNotesAreNotAutosaved',
	SaveChanges: 'SaveChanges',
	SaveAndLock: 'SaveAndLock',
	Download: 'Download',
	Open: 'Open',
	Preview: 'Preview',
	Print: 'Print',
	Delete: 'Delete',
	Invite: 'Invite',
	Title: 'Title',
	Name: 'Name',
	Disconnect: 'Disconnect',
	Connect: 'Connect',
	ConnectExistingClientOrContact: 'ConnectExistingClientOrContact',
	Attendees: 'Attendees',
	TeamMembers: 'TeamMembers',
	TeamMember: 'TeamMember',
	Admin: 'Admin',
	Staff: 'Staff',
	AssignedTeam: 'AssignedTeam',
	AssignedClients: 'AssignedClients',
	AssignNewClients: 'AssignNewClients',
	AssignClients: 'AssignClients',
	Confirm: 'Confirm',
	Country: 'Country',
	JobTitle: 'JobTitle',
	LicenseNumber: 'LicenseNumber',
	Files: 'Files',
	UploadingFiles: 'UploadingFiles',
	AssignTeam: 'AssignTeam',
	AssignTeamMember: 'AssignTeamMember',
	AccessType: 'AccessType',
	ViewableBy: 'ViewableBy',
	ViewableByHelper: 'ViewableByHelper',
	HideDetails: 'HideDetails',
	ShowDetails: 'ShowDetails',
	Tags: 'Tags',
	FilterBy: 'FilterBy',
	FilterByTags: 'FilterByTags',
	FilterByService: 'FilterByService',
	FilterByTeam: 'FilterByTeam',
	FilterByStatus: 'FilterByStatus',
	FilterByClient: 'FilterByClient',
	FilterByLocation: 'FilterByLocation',
	ImportExportButton: 'ImportExportButton',
	Import: 'Import',
	ImportFailed: 'ImportFailed',
	ImportTemplates: 'ImportTemplates',
	ImportsInProgress: 'ImportsInProgress',
	UploadYourTemplates: 'UploadYourTemplates',
	Canceled: 'Canceled',
	RelationshipType: 'RelationshipType',
	MinuteAbbreviated: 'MinuteAbbreviated',
	Unit: 'Unit',
	Interval: 'Interval',
	IntervalDays: 'IntervalDays',
	IntervalHours: 'IntervalHours',
	Date: 'Date',
	Time: 'Time',
	Amount: 'Amount',
	Continue: 'Continue',
	Days: 'Days',
	Assessment: 'Assessment',
	Enrolment: 'Enrolment',
	Report: 'Report',
	SearchContacts: 'SearchContacts',
	SearchMultipleContacts: 'SearchMultipleContacts',
	SearchMultipleContactsOptional: 'SearchMultipleContactsOptional',
	UploadPhoto: 'UploadPhoto',
	BusinessName: 'BusinessName',
	EmergencyContact: 'EmergencyContact',
	FullName: 'FullName',
	FirstName: 'FirstName',
	LastName: 'LastName',
	PreferredName: 'PreferredName',
	PreferredLanguage: 'PreferredLanguage',
	MiddleNames: 'MiddleNames',
	Email: 'Email',
	Username: 'Username',
	Gender: 'Gender',
	Female: 'Female',
	Male: 'Male',
	PhoneNumber: 'PhoneNumber',
	MobileNumber: 'MobileNumber',
	PhoneNumberOptional: 'PhoneNumberOptional',
	MobileNumberOptional: 'MobileNumberOptional',
	Signup: 'Signup',
	CreateAccount: 'CreateAccount',
	DigitalSign: 'DigitalSign',
	DigitalSignHelp: 'DigitalSignHelp',
	Ethnicity: 'Ethnicity',
	Occupation: 'Occupation',
	EmploymentStatus: 'EmploymentStatus',
	LivingArrangements: 'LivingArrangements',
	RelationshipStatus: 'RelationshipStatus',
	Arrangements: 'Arrangements',
	Role: 'Role',
	Active: 'Active',
	Inactive: 'Inactive',
	ShowMore: 'ShowMore',
	ShowLess: 'ShowLess',
	InProgress: 'InProgress',
	Incomplete: 'Incomplete',
	StaffSelectorAdminRole: 'StaffSelectorAdminRole',
	StaffSelectorStaffRole: 'StaffSelectorStaffRole',
	StaffAccessDescriptionStaff: 'StaffAccessDescriptionStaff',
	StaffAccessDescriptionAdmin: 'StaffAccessDescriptionAdmin',
	SignupProfession: 'SignupProfession',
	AlreadyHasAccount: 'AlreadyHasAccount',
	InitialSignupPageProviderTitle: 'InitialSignupPageProviderTitle',
	InitialSignupPageClientFamilyTitle: 'InitialSignupPageClientFamilyTitle',
	SignupPageProviderWarning: 'SignupPageProviderWarning',
	SignupPageProviderWarningLink: 'SignupPageProviderWarningLink',
	ReviewsFirstQuote: 'ReviewsFirstQuote',
	ReviewsSecondQuote: 'ReviewsSecondQuote',
	ReviewsSecondName: 'ReviewsSecondName',
	ReviewsSecondJobTitle: 'ReviewsSecondJobTitle',
	ReviewsThirdQuote: 'ReviewsThirdQuote',
	ReviewsThirdName: 'ReviewsThirdName',
	ReviewsThirdJobTitle: 'ReviewsThirdJobTitle',
	RelationshipSelectorProviderAdmin: 'RelationshipSelectorProviderAdmin',
	RelationshipSelectorProviderStaff: 'RelationshipSelectorProviderStaff',
	RelationshipSelectorFamilyAdmin: 'RelationshipSelectorFamilyAdmin',
	RelationshipSelectorFamilyMember: 'RelationshipSelectorFamilyMember',
	RelationshipSelectorSupportNetworkPrimary: 'RelationshipSelectorSupportNetworkPrimary',
	RelationshipSelectorSupportNetworkSecondary: 'RelationshipSelectorSupportNetworkSecondary',
	ClientModalTitle: 'ClientModalTitle',
	CreateNewTeamMember: 'CreateNewTeamMember',
	CreateNewClient: 'CreateNewClient',
	CreateNewContact: 'CreateNewContact',
	DuplicateClientBannerTitle: 'DuplicateClientBannerTitle',
	DuplicateClientBannerDescription: 'DuplicateClientBannerDescription',
	DuplicateClientBannerAction: 'DuplicateClientBannerAction',
	Password: 'Password',
	OldPassword: 'OldPassword',
	NewPassword: 'NewPassword',
	ConfirmPassword: 'ConfirmPassword',
	ChangePassword: 'ChangePassword',
	ChangePasswordHelperInfo: 'ChangePasswordHelperInfo',
	ChoosePassword: 'ChoosePassword',
	LoginEmail: 'LoginEmail',
	LoginPassword: 'LoginPassword',
	LoginForgotPasswordLink: 'LoginForgotPasswordLink',
	LoginButton: 'LoginButton',
	DontHaveAccount: 'DontHaveAccount',
	Resend: 'Resend',
	ResendConfirmationCode: 'ResendConfirmationCode',
	ResendConfirmationCodeSuccess: 'ResendConfirmationCodeSuccess',
	ResendConfirmationCodeDescription: 'ResendConfirmationCodeDescription',
	LogoutButton: 'LogoutButton',
	RegisterFirstName: 'RegisterFirstName',
	RegisterLastName: 'RegisterLastName',
	RegisterEmail: 'RegisterEmail',
	RegisterPassword: 'RegisterPassword',
	RegisterButton: 'RegisterButton',
	SignupInvitationForYou: 'SignupInvitationForYou',
	SettingsNewUserPasswordTitle: 'SettingsNewUserPasswordTitle',
	SettingsNewUserPasswordDescription: 'SettingsNewUserPasswordDescription',
	InviteUserTitle: 'InviteUserTitle',
	InviteUserDescription: 'InviteUserDescription',
	ProviderDetailsName: 'ProviderDetailsName',
	ProviderDetailsPhoneNumber: 'ProviderDetailsPhoneNumber',
	ProviderDetailsAddress: 'ProviderDetailsAddress',
	ClientProfileId: 'ClientProfileId',
	ClientProfileIdentificationNumber: 'ClientProfileIdentificationNumber',
	ClientProfileDOB: 'ClientProfileDOB',
	ClientProfileAddress: 'ClientProfileAddress',
	ClientProfileEmailHelperText: 'ClientProfileEmailHelperText',
	ClientProfileEmailHelperTextMoreInfo: 'ClientProfileEmailHelperTextMoreInfo',
	ClientEmailChangeWarningDescription: 'ClientEmailChangeWarningDescription',
	CreateClientModalTitle: 'CreateClientModalTitle',
	CreateContactModalTitle: 'CreateContactModalTitle',
	CreateContactWithRelationshipFormAccessType: 'CreateContactWithRelationshipFormAccessType',
	InvalidEmailFormat: 'InvalidEmailFormat',
	DuplicateEmailError: 'DuplicateEmailError',
	AddRelationshipModalTitle: 'AddRelationshipModalTitle',
	AddRelationshipModalTitleNewClient: 'AddRelationshipModalTitleNewClient',
	ContactAccessTypeHelperText: 'ContactAccessTypeHelperText',
	ContactAccessTypeHelperTextMoreInfo: 'ContactAccessTypeHelperTextMoreInfo',
	EditRelationshipModalTitle: 'EditRelationshipModalTitle',
	SignupAgreements: 'SignupAgreements',
	SignupBusinessAgreements: 'SignupBusinessAgreements',
	SignupTermsOfUse: 'SignupTermsOfUse',
	SignupPrivacy: 'SignupPrivacy',
	SignupBAA: 'SignupBAA',
	OnboardingBusinessAgreement: 'OnboardingBusinessAgreement',
	SignupSuccessTitle: 'SignupSuccessTitle',
	SignupSuccessDescription: 'SignupSuccessDescription',
	ConfirmIfUserIsClientTitle: 'ConfirmIfUserIsClientTitle',
	ConfirmIfUserIsClientDescription: 'ConfirmIfUserIsClientDescription',
	ConfirmIfUserIsClientYesButton: 'ConfirmIfUserIsClientYesButton',
	ConfirmIfUserIsClientNoButton: 'ConfirmIfUserIsClientNoButton',
	ConfirmSignupTitle: 'ConfirmSignupTitle',
	ConfirmSignupSubTitle: 'ConfirmSignupSubTitle',
	ConfirmSignupDescription: 'ConfirmSignupDescription',
	ConfirmSignupUsername: 'ConfirmSignupUsername',
	ConfirmSignupAccessCode: 'ConfirmSignupAccessCode',
	ConfirmSignupButtom: 'ConfirmSignupButtom',
	VerifyEmailTitle: 'VerifyEmailTitle',
	VerifyEmailSubTitle: 'VerifyEmailSubTitle',
	VerifyEmailAccessCode: 'VerifyEmailAccessCode',
	VerifyEmailButton: 'VerifyEmailButton',
	SendVerificationEmail: 'SendVerificationEmail',
	ResendVerificationEmail: 'ResendVerificationEmail',
	CopyLinkSuccessSnackbar: 'CopyLinkSuccessSnackbar',
	CopyPhoneNumberSuccessSnackbar: 'CopyPhoneNumberSuccessSnackbar',
	CopyEmailAddressSuccessSnackbar: 'CopyEmailAddressSuccessSnackbar',
	CopyAddressSuccessSnackbar: 'CopyAddressSuccessSnackbar',
	ConfirmSignupSuccessSnackbar: 'ConfirmSignupSuccessSnackbar',
	ClientSavedSuccessSnackbar: 'ClientSavedSuccessSnackbar',
	ClientRemovedSuccessSnackbar: 'ClientRemovedSuccessSnackbar',
	ChangePasswordSuccessfulSnackbar: 'ChangePasswordSuccessfulSnackbar',
	ChangePasswordFailureSnackbar: 'ChangePasswordFailureSnackbar',
	UpdateUserInfoSuccessSnackbar: 'UpdateUserInfoSuccessSnackbar',
	UpdateUserSettingsSuccessSnackbar: 'UpdateUserSettingsSuccessSnackbar',
	AddnNewWorkspaceSuccessSnackbar: 'AddnNewWorkspaceSuccessSnackbar',
	VerifyEmailSuccessSnackbar: 'VerifyEmailSuccessSnackbar',
	VerifyEmailSuccessLogOutSnackbar: 'VerifyEmailSuccessLogOutSnackbar',
	ClientIntakeSuccessSnackbar: 'ClientIntakeSuccessSnackbar',
	ClientIntakeSkipPasswordSuccessSnackbar: 'ClientIntakeSkipPasswordSuccessSnackbar',
	GenericFailureSnackbar: 'GenericFailureSnackbar',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar: 'DowngradingPricingPlanTooManyStaffErrorCodeSnackbar',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'ProviderHasExistingBillingAccountErrorCodeSnackbar',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: 'OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar',
	StripeMinimumPaymentErrorCodeSnackbar: 'StripeMinimumPaymentErrorCodeSnackbar',
	MinimumPaymentError: 'MinimumPaymentError',
	OnlinePaymentsNotSupportedForCurrency: 'OnlinePaymentsNotSupportedForCurrency',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'PaymentIntentAlreadyProcessedErrorCodeSnackbar',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'PaymentIntentAlreadyPaidErrorCodeSnackbar',
	PaymentIntentSyncTimeoutSnackbar: 'PaymentIntentSyncTimeoutSnackbar',
	PaymentIntentAmountMismatchSnackbar: 'PaymentIntentAmountMismatchSnackbar',
	InvalidPaymentMethodCode: 'InvalidPaymentMethodCode',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'DuplicateInvoiceNumberErrorCodeSnackbar',
	InsufficientScopeErrorCodeSnackbar: 'InsufficientScopeErrorCodeSnackbar',
	InsufficientCalendarScopesSnackbar: 'InsufficientCalendarScopesSnackbar',
	InsufficientInboxScopesSnackbar: 'InsufficientInboxScopesSnackbar',
	InboxAccountAlreadyConnected: 'InboxAccountAlreadyConnected',
	IntakeExpiredErrorCodeSnackbar: 'IntakeExpiredErrorCodeSnackbar',
	IntakeNotFoundErrorSnackbar: 'IntakeNotFoundErrorSnackbar',
	DuplicateContactFieldSettingErrorSnackbar: 'DuplicateContactFieldSettingErrorSnackbar',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'DuplicateContactFieldSettingFieldErrorSnackbar',
	Duplicate: 'Duplicate',
	AddToCollection: 'AddToCollection',
	AddServiceToCollections: 'AddServiceToCollections',
	AddServiceToOneOrMoreCollections: 'AddServiceToOneOrMoreCollections',
	RemoveFromCollection: 'RemoveFromCollection',
	UnauthorisedSnackbar: 'UnauthorisedSnackbar',
	NotFoundSnackbar: 'NotFoundSnackbar',
	EmailNotVerifiedErrorCodeSnackbar: 'EmailNotVerifiedErrorCodeSnackbar',
	UnauthorisedInvoiceSnackbar: 'UnauthorisedInvoiceSnackbar',
	InvalidZoomTokenErrorCodeSnackbar: 'InvalidZoomTokenErrorCodeSnackbar',
	ZoomUserNotInAccountErrorCodeSnackbar: 'ZoomUserNotInAccountErrorCodeSnackbar',
	ProviderPaymentSuccessSnackbar: 'ProviderPaymentSuccessSnackbar',
	RelationshipSavedSuccessSnackbar: 'RelationshipSavedSuccessSnackbar',
	StaffSavedSuccessSnackbar: 'StaffSavedSuccessSnackbar',
	ClientNotePublishedSuccessSnackbar: 'ClientNotePublishedSuccessSnackbar',
	ClientNotePublishedAndLockSuccessSnackbar: 'ClientNotePublishedAndLockSuccessSnackbar',
	ClientFileSavedSuccessSnackbar: 'ClientFileSavedSuccessSnackbar',
	ClientBulkStaffAssignedSuccessSnackbar: 'ClientBulkStaffAssignedSuccessSnackbar',
	ClientBulkStaffUnassignedSuccessSnackbar: 'ClientBulkStaffUnassignedSuccessSnackbar',
	ClientBulkTagsAddedSuccessSnackbar: 'ClientBulkTagsAddedSuccessSnackbar',
	GenericSavedSuccessSnackbar: 'GenericSavedSuccessSnackbar',
	VerifyEmailSentSnackbar: 'VerifyEmailSentSnackbar',
	InstallCarepatronOnYourIphone1: 'InstallCarepatronOnYourIphone1',
	InstallCarepatronOnYourIphone2: 'InstallCarepatronOnYourIphone2',
	NavigationDrawerSettings: 'NavigationDrawerSettings',
	NavigationDrawerProfile: 'NavigationDrawerProfile',
	NavigationDrawerProviderSettings: 'NavigationDrawerProviderSettings',
	NavigationDrawerInvoices: 'NavigationDrawerInvoices',
	NavigationDrawerTemplates: 'NavigationDrawerTemplates',
	NavigationDrawerTemplatesV2: 'NavigationDrawerTemplatesV2',
	Profile: 'Profile',
	HeaderAccountSettings: 'HeaderAccountSettings',
	HeaderClients: 'HeaderClients',
	HeaderCalendar: 'HeaderCalendar',
	HeaderStaff: 'HeaderStaff',
	YourTeam: 'YourTeam',
	HeaderCalls: 'HeaderCalls',
	HeaderMoreOptions: 'HeaderMoreOptions',
	HeaderHelp: 'HeaderHelp',
	BottomNavBilling: 'BottomNavBilling',
	BottomNavMore: 'BottomNavMore',
	BottomNavGettingStarted: 'BottomNavGettingStarted',
	BottomNavNotes: 'BottomNavNotes',
	HeaderClientAppMyDocumentation: 'HeaderClientAppMyDocumentation',
	ClientAppSubHeaderMyDocumentation: 'ClientAppSubHeaderMyDocumentation',
	HeaderClientAppMyRelationships: 'HeaderClientAppMyRelationships',
	HeaderClientAppCalls: 'HeaderClientAppCalls',
	HeaderClientAppAccountSettings: 'HeaderClientAppAccountSettings',
	ClientAppCallsPageNoOptionsText: 'ClientAppCallsPageNoOptionsText',
	ClientHeaderOverview: 'ClientHeaderOverview',
	ClientHeaderProfile: 'ClientHeaderProfile',
	ClientHeaderRelationships: 'ClientHeaderRelationships',
	ClientHeaderDocumentation: 'ClientHeaderDocumentation',
	ClientHeaderDocuments: 'ClientHeaderDocuments',
	ClientHeaderHistory: 'ClientHeaderHistory',
	ClientHeaderBillingAndReceipts: 'ClientHeaderBillingAndReceipts',
	ClientHeaderBilling: 'ClientHeaderBilling',
	ClientHeaderFile: 'ClientHeaderFile',
	ClientHeaderNote: 'ClientHeaderNote',
	ClientHeaderRelationship: 'ClientHeaderRelationship',
	ClientHeaderInbox: 'ClientHeaderInbox',
	RelationshipTypeProviderAdmin: 'RelationshipTypeProviderAdmin',
	RelationshipTypeProviderStaff: 'RelationshipTypeProviderStaff',
	RelationshipTypeFamilyAdmin: 'RelationshipTypeFamilyAdmin',
	RelationshipTypeFamilyMember: 'RelationshipTypeFamilyMember',
	RelationshipTypeFriendOrSupport: 'RelationshipTypeFriendOrSupport',
	RelationshipTypeClientOwner: 'RelationshipTypeClientOwner',
	ClientListCreateButton: 'ClientListCreateButton',
	ClientListEmptyState: 'ClientListEmptyState',
	ContactListCreateButton: 'ContactListCreateButton',
	YearsPlural: 'YearsPlural',
	MonthsPlural: 'MonthsPlural',
	WeeksPlural: 'WeeksPlural',
	DaysPlural: 'DaysPlural',
	HoursPlural: 'HoursPlural',
	MinutesPlural: 'MinutesPlural',
	CaregiverListCreateButton: 'CaregiverListCreateButton',
	CaregiverListEmptyState: 'CaregiverListEmptyState',
	CaregiverListCantAddStaffInfoTitle: 'CaregiverListCantAddStaffInfoTitle',
	MissingPaymentMethod: 'MissingPaymentMethod',
	CaregiverCreateModalTitle: 'CaregiverCreateModalTitle',
	CaregiverCreateModalDescription: 'CaregiverCreateModalDescription',
	RemoveAccess: 'RemoveAccess',
	ManageStaffRelationshipsAddButton: 'ManageStaffRelationshipsAddButton',
	ManageStaffRelationshipsEmptyStateText: 'ManageStaffRelationshipsEmptyStateText',
	ManageStaffRelationshipsModalTitle: 'ManageStaffRelationshipsModalTitle',
	ManageStaffRelationshipsModalDescription: 'ManageStaffRelationshipsModalDescription',
	ClientRelationshipsEmptyStateText: 'ClientRelationshipsEmptyStateText',
	ClientRelationshipsAddStaffButton: 'ClientRelationshipsAddStaffButton',
	ClientRelationshipsAddFamilyButton: 'ClientRelationshipsAddFamilyButton',
	ClientRelationshipsAddClientOwnerButton: 'ClientRelationshipsAddClientOwnerButton',
	RelationshipPageAccessTypeColumnName: 'RelationshipPageAccessTypeColumnName',
	ClientNotesEmptyStateText: 'ClientNotesEmptyStateText',
	ClientNoteItemDeleteConfirmationModalDescription: 'ClientNoteItemDeleteConfirmationModalDescription',
	DeleteFileConfirmationModalPrompt: 'DeleteFileConfirmationModalPrompt',
	AddClientNoteButton: 'AddClientNoteButton',
	AddClientNoteModalTitle: 'AddClientNoteModalTitle',
	AddClientNoteModalDescription: 'AddClientNoteModalDescription',
	EditClientNoteModalTitle: 'EditClientNoteModalTitle',
	EditClientNoteModalDescription: 'EditClientNoteModalDescription',
	ClientNoteFormContent: 'ClientNoteFormContent',
	ClientNotFormAccessLevel: 'ClientNotFormAccessLevel',
	ClientNotFormAccessLevelDescription: 'ClientNotFormAccessLevelDescription',
	ClientFileFormAccessLevelDescription: 'ClientFileFormAccessLevelDescription',
	ClientNoteFormAddFileButton: 'ClientNoteFormAddFileButton',
	ClientNoteFormChooseAClient: 'ClientNoteFormChooseAClient',
	Note: 'Note',
	Draft: 'Draft',
	DraftResponses: 'DraftResponses',
	Saving: 'Saving',
	DraftSaved: 'DraftSaved',
	TemplateSaved: 'TemplateSaved',
	File: 'File',
	OpenFile: 'OpenFile',
	AddStaffClientRelationshipsModalTitle: 'AddStaffClientRelationshipsModalTitle',
	AddStaffClientRelationshipsModalDescription: 'AddStaffClientRelationshipsModalDescription',
	AddFamilyClientRelationshipModalTitle: 'AddFamilyClientRelationshipModalTitle',
	AddFamilyClientRelationshipModalDescription: 'AddFamilyClientRelationshipModalDescription',
	AddClientOwnerRelationshipModalTitle: 'AddClientOwnerRelationshipModalTitle',
	AddClientOwnerRelationshipModalDescription: 'AddClientOwnerRelationshipModalDescription',
	CreateContactRelationshipButton: 'CreateContactRelationshipButton',
	CreateContactSelectorDefaultOption: 'CreateContactSelectorDefaultOption',
	CaregiversListItemRemoveStaff: 'CaregiversListItemRemoveStaff',
	ClientListPageItemRemoveAccess: 'ClientListPageItemRemoveAccess',
	ClientListPageItemArchive: 'ClientListPageItemArchive',
	ClientTableClientName: 'ClientTableClientName',
	ClientTablePhone: 'ClientTablePhone',
	ClientTableStatus: 'ClientTableStatus',
	Selected: 'Selected',
	ErrorRegisteredExistingUser: 'ErrorRegisteredExistingUser',
	ErrorInviteStaffExistingUser: 'ErrorInviteStaffExistingUser',
	ErrorInviteExistingProviderStaffCode: 'ErrorInviteExistingProviderStaffCode',
	ErrorUserSignInIncorrectCredentials: 'ErrorUserSignInIncorrectCredentials',
	ErrorUserSigninGeneric: 'ErrorUserSigninGeneric',
	ErrorUserSigninUserNotConfirmed: 'ErrorUserSigninUserNotConfirmed',
	ErrorFileUploadMaxFileSize: 'ErrorFileUploadMaxFileSize',
	ErrorFileUploadMaxFileCount: 'ErrorFileUploadMaxFileCount',
	ErrorFileUploadCustomMaxFileSize: 'ErrorFileUploadCustomMaxFileSize',
	ErrorFileUploadCustomMaxFileCount: 'ErrorFileUploadCustomMaxFileCount',
	ErrorFileUploadInvalidFileType: 'ErrorFileUploadInvalidFileType',
	ErrorFileUploadNoFileSelected: 'ErrorFileUploadNoFileSelected',
	ErrorCallNotFound: 'ErrorCallNotFound',
	ErrorCannotAccessCallUninvitedCode: 'ErrorCannotAccessCallUninvitedCode',
	ErrorOnlySingleCallAllowed: 'ErrorOnlySingleCallAllowed',
	ErrorProfilePhotoMaxFileSize: 'ErrorProfilePhotoMaxFileSize',
	ConfirmationModalTitle: 'ConfirmationModalTitle',
	ConfirmationModalDescriptionDeleteClient: 'ConfirmationModalDescriptionDeleteClient',
	ConfirmationModalDescriptionRemoveMyAccessFromClient: 'ConfirmationModalDescriptionRemoveMyAccessFromClient',
	ConfirmationModalDescriptionRemoveRelationshipToClient: 'ConfirmationModalDescriptionRemoveRelationshipToClient',
	ConfirmationModalDescriptionRemoveStaff: 'ConfirmationModalDescriptionRemoveStaff',
	ConfirmationModalBulkDeleteClientsTitleId: 'ConfirmationModalBulkDeleteClientsTitleId',
	ConfirmationModalBulkDeleteClientsDescriptionId: 'ConfirmationModalBulkDeleteClientsDescriptionId',
	ConfirmationModalBulkDeleteContactsTitleId: 'ConfirmationModalBulkDeleteContactsTitleId',
	ConfirmationModalBulkDeleteContactsDescriptionId: 'ConfirmationModalBulkDeleteContactsDescriptionId',
	ConfirmationModalDeleteClientField: 'ConfirmationModalDeleteClientField',
	ConfirmationModalDeleteService: 'ConfirmationModalDeleteService',
	ConfirmationModalDeleteServiceGroup: 'ConfirmationModalDeleteServiceGroup',
	ConfirmDraftResponseTitle: 'ConfirmDraftResponseTitle',
	ConfirmDraftResponseDescription: 'ConfirmDraftResponseDescription',
	ConfirmDraftResponseContinue: 'ConfirmDraftResponseContinue',
	ConfirmDraftResponseSubmitResponse: 'ConfirmDraftResponseSubmitResponse',
	Discard: 'Discard',
	DiscardDrafts: 'DiscardDrafts',
	DiscardChanges: 'DiscardChanges',
	ContinueEditing: 'ContinueEditing',
	DeleteClients: 'DeleteClients',
	DeleteCategory: 'DeleteCategory',
	ForgotPasswordPageTitle: 'ForgotPasswordPageTitle',
	ForgotPasswordPageDescription: 'ForgotPasswordPageDescription',
	ForgotPasswordPageButton: 'ForgotPasswordPageButton',
	ForgotPasswordSuccessPageTitle: 'ForgotPasswordSuccessPageTitle',
	ForgotPasswordSuccessPageDescription: 'ForgotPasswordSuccessPageDescription',
	ForgotPasswordConfirmPageTitle: 'ForgotPasswordConfirmPageTitle',
	ForgotPasswordConfirmPageDescription: 'ForgotPasswordConfirmPageDescription',
	ForgotPasswordConfirmAccessCode: 'ForgotPasswordConfirmAccessCode',
	ForgotPasswordConfirmNewPassword: 'ForgotPasswordConfirmNewPassword',
	PricingPlanPerMonth: 'PricingPlanPerMonth',
	PricingPlanPerYear: 'PricingPlanPerYear',
	NewZealand: 'NewZealand',
	Australia: 'Australia',
	UnitedStates: 'UnitedStates',
	Other: 'Other',
	CapeTown: 'CapeTown',
	Tokyo: 'Tokyo',
	Seoul: 'Seoul',
	Mumbai: 'Mumbai',
	Singapore: 'Singapore',
	Sydney: 'Sydney',
	Central: 'Central',
	Frankfurt: 'Frankfurt',
	Stockholm: 'Stockholm',
	Milan: 'Milan',
	Ireland: 'Ireland',
	London: 'London',
	Paris: 'Paris',
	SaoPaulo: 'SaoPaulo',
	NVirginia: 'NVirginia',
	Ohio: 'Ohio',
	NCalifornia: 'NCalifornia',
	Oregon: 'Oregon',
	ProviderPaymentTitle: 'ProviderPaymentTitle',
	ProviderPaymentFormPlanTitle: 'ProviderPaymentFormPlanTitle',
	ProviderPaymentStepName: 'ProviderPaymentStepName',
	ProviderPaymentFormPlanTotalTitle: 'ProviderPaymentFormPlanTotalTitle',
	ProviderPaymentFormCardHolderTitle: 'ProviderPaymentFormCardHolderTitle',
	ProviderPaymentFormCardHolderAddressTitle: 'ProviderPaymentFormCardHolderAddressTitle',
	ProviderPaymentFormCardDetailsTitle: 'ProviderPaymentFormCardDetailsTitle',
	ProviderPaymentFormCardHolderName: 'ProviderPaymentFormCardHolderName',
	ProviderPaymentFormBillingAddressLine1: 'ProviderPaymentFormBillingAddressLine1',
	ProviderPaymentFormBillingAddressCity: 'ProviderPaymentFormBillingAddressCity',
	ProviderPaymentFormBillingAddressCountry: 'ProviderPaymentFormBillingAddressCountry',
	ProviderPaymentFormBillingAddressPostalCode: 'ProviderPaymentFormBillingAddressPostalCode',
	ProviderPaymentFormBillingEmail: 'ProviderPaymentFormBillingEmail',
	ProviderPaymentFormCardNumber: 'ProviderPaymentFormCardNumber',
	ProviderPaymentFormCardExpiry: 'ProviderPaymentFormCardExpiry',
	ProviderPaymentFormCardCvc: 'ProviderPaymentFormCardCvc',
	ProviderPaymentFormBackButton: 'ProviderPaymentFormBackButton',
	ProviderPaymentFormSaveButton: 'ProviderPaymentFormSaveButton',
	ProviderPaymentFreePlanDescription: 'ProviderPaymentFreePlanDescription',
	ProviderBillingPlanExpansionManageButton: 'ProviderBillingPlanExpansionManageButton',
	CallsListEmptyState: 'CallsListEmptyState',
	CallsListCreateButton: 'CallsListCreateButton',
	CallsListItemEndCall: 'CallsListItemEndCall',
	CreateCallModalTitle: 'CreateCallModalTitle',
	CreateCallModalDescription: 'CreateCallModalDescription',
	CreateCallFormInviteOnly: 'CreateCallFormInviteOnly',
	CreateCallFormInviteOnlyMoreInfo: 'CreateCallFormInviteOnlyMoreInfo',
	CreateCallFormButton: 'CreateCallFormButton',
	CreateCallFormRegion: 'CreateCallFormRegion',
	CreateCallFormRecipients: 'CreateCallFormRecipients',
	CreateCallNoPersonIdToolTip: 'CreateCallNoPersonIdToolTip',
	PublicCallDialogTitle: 'PublicCallDialogTitle',
	PublicCallDialogTitlePlaceholder: 'PublicCallDialogTitlePlaceholder',
	CopyLinkForCall: 'CopyLinkForCall',
	LinkForCallCopied: 'LinkForCallCopied',
	CallNoAttendees: 'CallNoAttendees',
	Canada: 'Canada',
	Europe: 'Europe',
	MiddleEast: 'MiddleEast',
	SouthAmerica: 'SouthAmerica',
	UnitedStatesEast: 'UnitedStatesEast',
	UnitedStatesWest: 'UnitedStatesWest',
	UnitedKingdom: 'UnitedKingdom',
	CreateCallModalTitleLabel: 'CreateCallModalTitleLabel',
	CreateCallModalAddStaffSelectorLabel: 'CreateCallModalAddStaffSelectorLabel',
	CreateCallModalAddClientContactSelectorLabel: 'CreateCallModalAddClientContactSelectorLabel',
	CreateCallModalAddStaffSelectorPlaceholder: 'CreateCallModalAddStaffSelectorPlaceholder',
	CreateCallModalAddClientContactSelectorPlaceholder: 'CreateCallModalAddClientContactSelectorPlaceholder',
	EndCallConfirmationForCreator: 'EndCallConfirmationForCreator',
	EndCallConfirmationHasActiveAttendees: 'EndCallConfirmationHasActiveAttendees',
	EndCall: 'EndCall',
	EndCallForAll: 'EndCallForAll',
	LeaveCall: 'LeaveCall',

	Devices: 'Devices',
	Maximize: 'Maximize',
	Minimize: 'Minimize',
	Roster: 'Roster',
	VideoQual360: 'VideoQual360',
	VideoQual540: 'VideoQual540',
	VideoQual720: 'VideoQual720',
	CameraQuality: 'CameraQuality',
	CameraSource: 'CameraSource',
	MicSource: 'MicSource',
	SpeakerSource: 'SpeakerSource',
	AddClientFilesModalTitle: 'AddClientFilesModalTitle',
	EditFileModalTitle: 'EditFileModalTitle',
	AddClientFilesModalDescription: 'AddClientFilesModalDescription',
	ClientFilesPageUploadFileButton: 'ClientFilesPageUploadFileButton',
	ClientFilesPageEmptyStateText: 'ClientFilesPageEmptyStateText',
	EditClientFileModalTitle: 'EditClientFileModalTitle',
	EditClientFileModalDescription: 'EditClientFileModalDescription',
	JoinNow: 'JoinNow',
	Join: 'Join',
	MeetingEnded: 'MeetingEnded',
	MeetingEnd: 'MeetingEnd',
	MicOn: 'MicOn',
	MicOff: 'MicOff',
	VideoOff: 'VideoOff',
	VideoOn: 'VideoOn',
	CameraAndMicIssueModalTitle: 'CameraAndMicIssueModalTitle',
	CameraAndMicIssueModalDescription: 'CameraAndMicIssueModalDescription',
	Settings: 'Settings',
	Camera: 'Camera',
	Microphone: 'Microphone',
	Speaker: 'Speaker',
	Speakers: 'Speakers',
	ShareScreen: 'ShareScreen',
	ShareYourScreen: 'ShareYourScreen',
	YouArePresenting: 'YouArePresenting',
	ApplyVisualEffects: 'ApplyVisualEffects',
	ApplyVisualEffectsNotSupported: 'ApplyVisualEffectsNotSupported',
	VisualEffects: 'VisualEffects',
	IntenseBlur: 'IntenseBlur',
	SlightBlur: 'SlightBlur',
	NoEffect: 'NoEffect',
	ReplaceBackground: 'ReplaceBackground',
	StopSharing: 'StopSharing',
	StopSharingLabel: 'StopSharingLabel',
	ShareScreenNotSupported: 'ShareScreenNotSupported',
	VoiceFocus: 'VoiceFocus',
	VoiceFocusLabel: 'VoiceFocusLabel',
	ShareScreenWithId: 'ShareScreenWithId',
	AttendeeWithId: 'AttendeeWithId',
	MicCamWarningTitle: 'MicCamWarningTitle',
	MicCamWarningMessage: 'MicCamWarningMessage',
	MicWarningMessage: 'MicWarningMessage',
	CamWarningMessage: 'CamWarningMessage',
	MeetingOpenChat: 'MeetingOpenChat',
	Troubleshoot: 'Troubleshoot',
	People: 'People',
	Person: 'Person',
	Husband: 'Husband',
	Wife: 'Wife',
	Father: 'Father',
	Mother: 'Mother',
	StepFather: 'StepFather',
	StepMother: 'StepMother',
	LegalGuardian: 'LegalGuardian',
	Son: 'Son',
	Daughter: 'Daughter',
	Brother: 'Brother',
	Sister: 'Sister',
	Grandparent: 'Grandparent',
	Grandfather: 'Grandfather',
	Grandmother: 'Grandmother',
	Grandson: 'Grandson',
	Granddaughter: 'Granddaughter',
	Uncle: 'Uncle',
	Aunt: 'Aunt',
	Cousin: 'Cousin',
	Nephew: 'Nephew',
	Niece: 'Niece',
	FatherInLaw: 'FatherInLaw',
	MotherInLaw: 'MotherInLaw',
	BrotherInLaw: 'BrotherInLaw',
	SisterInLaw: 'SisterInLaw',
	ExtendedFamilyMember: 'ExtendedFamilyMember',
	Nurse: 'Nurse',
	NurseAssistant: 'NurseAssistant',
	DiversionalTherapist: 'DiversionalTherapist',
	Dietician: 'Dietician',
	Physician: 'Physician',
	GeneralPractitioner: 'GeneralPractitioner',
	Midwife: 'Midwife',
	Caregiver: 'Caregiver',
	CareManager: 'CareManager',
	MentalHealthProfessional: 'MentalHealthProfessional',
	HomeCaregiver: 'HomeCaregiver',
	CommunityHealthLead: 'CommunityHealthLead',
	CareAssistant: 'CareAssistant',
	Physiotherapist: 'Physiotherapist',
	Patient: 'Patient',
	Doctors: 'Doctors',
	Surgeons: 'Surgeons',
	ThisPerson: 'ThisPerson',
	IsA: 'IsA',
	Of: 'Of',
	None: 'None',
	StartedBy: 'StartedBy',
	PleaseWait: 'PleaseWait',
	PleaseWaitForHostToStart: 'PleaseWaitForHostToStart',
	PleaseWaitForHostToJoin: 'PleaseWaitForHostToJoin',
	MeetingReady: 'MeetingReady',
	You: 'You',
	Yes: 'Yes',
	No: 'No',
	EditContactFormIsClientLabel: 'EditContactFormIsClientLabel',
	EditContactIsClientCheckboxWarning: 'EditContactIsClientCheckboxWarning',
	EditContactIsClientWanringModal: 'EditContactIsClientWanringModal',
	PersonalDetails: 'PersonalDetails',
	ContactDetails: 'ContactDetails',
	ProviderDetails: 'ProviderDetails',
	TeamMembersDetails: 'TeamMembersDetails',
	PortalAccess: 'PortalAccess',
	NoteModalAttachmentButton: 'NoteModalAttachmentButton',
	NoteModalPhotoButton: 'NoteModalPhotoButton',
	NoteModalTrascribeButton: 'NoteModalTrascribeButton',
	RelationshipEmptyStateTitle: 'RelationshipEmptyStateTitle',
	Admins: 'Admins',
	AdminsOnly: 'AdminsOnly',
	JustYou: 'JustYou',
	AllServices: 'AllServices',
	BackToLogin: 'BackToLogin',
	BackToAppointment: 'BackToAppointment',
	LogoutAreYouSure: 'LogoutAreYouSure',
	MyRelationships: 'MyRelationships',
	ChooseAnAccount: 'ChooseAnAccount',
	ScheduleNew: 'ScheduleNew',
	Reschedule: 'Reschedule',
	DeleteClientEventConfirmationDescription: 'DeleteClientEventConfirmationDescription',
	ClientAppointment: 'ClientAppointment',
	Appointment: 'Appointment',
	Task: 'Task',
	Reminder: 'Reminder',
	Meeting: 'Meeting',
	Tasks: 'Tasks',
	Reminders: 'Reminders',
	Meetings: 'Meetings',
	OutOfOffice: 'OutOfOffice',
	NotifyAttendeesOfTaskModalTitle: 'NotifyAttendeesOfTaskModalTitle',
	NotifyAttendeesOfTaskDeletedModalTitle: 'NotifyAttendeesOfTaskDeletedModalTitle',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'NotifyAttendeesOfTaskConfirmationModalTitle',
	NotifyAttendeesOfTaskConfirmationModalDescription: 'NotifyAttendeesOfTaskConfirmationModalDescription',
	NotifyAttendeesOfTaskCancellationModalTitle: 'NotifyAttendeesOfTaskCancellationModalTitle',
	NotifyAttendeesOfTaskCancellationModalDescription: 'NotifyAttendeesOfTaskCancellationModalDescription',
	NotifyAttendeesOfTaskSnackbar: 'NotifyAttendeesOfTaskSnackbar',
	NotifyAttendeesOfTaskMissingEmails: 'NotifyAttendeesOfTaskMissingEmails',
	NotifyAttendeesOfTaskMissingEmailsV2: 'NotifyAttendeesOfTaskMissingEmailsV2',
	PosoNumber: 'PosoNumber',
	SelectAttendees: 'SelectAttendees',
	SelectAssignees: 'SelectAssignees',
	AssignServices: 'AssignServices',
	AppointmentDetails: 'AppointmentDetails',
	Every: 'Every',
	Ends: 'Ends',
	Daily: 'Daily',
	Weekly: 'Weekly',
	Every2Weeks: 'Every2Weeks',
	MonthlyOn: 'MonthlyOn',
	Custom: 'Custom',
	Occurrences: 'Occurrences',
	EditRecurringAppointmentModalTitle: 'EditRecurringAppointmentModalTitle',
	EditRecurringEventModalTitle: 'EditRecurringEventModalTitle',
	EditRecurringReminderModalTitle: 'EditRecurringReminderModalTitle',
	EditRecurringTaskModalTitle: 'EditRecurringTaskModalTitle',
	ThisAppointment: 'ThisAppointment',
	ThisAndFollowingAppointments: 'ThisAndFollowingAppointments',
	AllAppointments: 'AllAppointments',
	ThisTask: 'ThisTask',
	ThisAndFollowingTasks: 'ThisAndFollowingTasks',
	AllTasks: 'AllTasks',
	ThisReminder: 'ThisReminder',
	ThisAndFollowingReminders: 'ThisAndFollowingReminders',
	AllReminders: 'AllReminders',
	ThisMeeting: 'ThisMeeting',
	ThisAndFollowingMeetings: 'ThisAndFollowingMeetings',
	AllMeetings: 'AllMeetings',
	NoLimit: 'NoLimit',

	DeleteRecurringAppointmentModalTitle: 'DeleteRecurringAppointmentModalTitle',
	DeleteRecurringEventModalTitle: 'DeleteRecurringEventModalTitle',
	DeleteRecurringReminderModalTitle: 'DeleteRecurringReminderModalTitle',
	DeleteRecurringTaskModalTitle: 'DeleteRecurringTaskModalTitle',
	Attending: 'Attending',
	AddContacts: 'AddContacts',
	'Service/s': 'Service/s',
	AddNote: 'AddNote',
	AddMessage: 'AddMessage',
	BookAgain: 'BookAgain',
	RejectAppointmentFormTitle: 'RejectAppointmentFormTitle',
	CurrentEventTime: 'CurrentEventTime',
	ProposeNewDateTime: 'ProposeNewDateTime',
	TimeRangeFormula: 'TimeRangeFormula',
	ReasonForChange: 'ReasonForChange',
	Send: 'Send',
	SendAndClose: 'SendAndClose',
	DoNotSend: 'DoNotSend',
	ThanksForLettingKnow: 'ThanksForLettingKnow',
	ThankYouForYourFeedback: 'ThankYouForYourFeedback',
	RespondToHistoricAppointmentError: 'RespondToHistoricAppointmentError',
	AppointmentNotFound: 'AppointmentNotFound',
	PracticeSettingsGeneralTab: 'PracticeSettingsGeneralTab',
	PracticeSettingsServicesTab: 'PracticeSettingsServicesTab',
	PracticeSettingsAvailabilityTab: 'PracticeSettingsAvailabilityTab',
	PracticeSettingsOnlineBookingTab: 'PracticeSettingsOnlineBookingTab',
	PracticeSettingsBillingTab: 'PracticeSettingsBillingTab',
	PracticeSettingsClientSettingsTab: 'PracticeSettingsClientSettingsTab',
	PracticeSettingsTaxRatesTab: 'PracticeSettingsTaxRatesTab',
	Currency: 'Currency',
	BillingAddress: 'BillingAddress',
	Street: 'Street',
	SuburbOrState: 'SuburbOrState',
	InvoiceSettings: 'InvoiceSettings',
	OverdueTerm: 'OverdueTerm',
	DefaultInvoiceTitle: 'DefaultInvoiceTitle',
	DefaultDescription: 'DefaultDescription',
	Logo: 'Logo',
	NationalProviderId: 'NationalProviderId',
	NPI: 'NPI',
	AttachLogo: 'AttachLogo',
	ChangeLogo: 'ChangeLogo',
	UploadLogo: 'UploadLogo',
	PracticeInfoHeader: 'PracticeInfoHeader',
	NewLocation: 'NewLocation',
	NewLocationSuccess: 'NewLocationSuccess',
	NewLocationFailure: 'NewLocationFailure',
	EditLocationSucess: 'EditLocationSucess',
	EditLocationFailure: 'EditLocationFailure',
	DeleteLocationConfirmation: 'DeleteLocationConfirmation',
	NewReminder: 'NewReminder',
	DeleteReminderConfirmation: 'DeleteReminderConfirmation',
	CreateNewLocation: 'CreateNewLocation',
	ManuallyTypeLocation: 'ManuallyTypeLocation',
	PaymentSettings: 'PaymentSettings',
	ChargesStatus: 'ChargesStatus',
	ChargesEnabled: 'ChargesEnabled',
	ChargesDisabled: 'ChargesDisabled',
	PayoutsStatus: 'PayoutsStatus',
	PayoutsEnabled: 'PayoutsEnabled',
	PayoutsDisabled: 'PayoutsDisabled',
	ViewStripeDashboard: 'ViewStripeDashboard',
	SetupPayments: 'SetupPayments',
	StripeChargesInfoToolTip: 'StripeChargesInfoToolTip',
	StripePayoutsInfoToolTip: 'StripePayoutsInfoToolTip',
	ProcessingFeeToolTip: 'ProcessingFeeToolTip',
	ProcessingFee: 'ProcessingFee',
	ClientIsChargedProcessingFee: 'ClientIsChargedProcessingFee',
	ProviderIsChargedProcessingFee: 'ProviderIsChargedProcessingFee',
	StatementDescriptorToolTip: 'StatementDescriptorToolTip',
	StatementDescriptor: 'StatementDescriptor',
	ReminderEditDisclaimer: 'ReminderEditDisclaimer',
	Simple: 'Simple',
	Contemporary: 'Contemporary',
	Modern: 'Modern',
	ServiceName: 'ServiceName',
	ServiceDetails: 'ServiceDetails',
	ServiceDuration: 'ServiceDuration',
	ServiceRate: 'ServiceRate',
	ServiceCode: 'ServiceCode',
	SaveService: 'SaveService',
	NewService: 'NewService',
	NewServiceSuccess: 'NewServiceSuccess',
	NewServiceFailure: 'NewServiceFailure',
	EditService: 'EditService',
	EditServiceSuccess: 'EditServiceSuccess',
	EditServiceFailure: 'EditServiceFailure',
	DeleteServiceSuccess: 'DeleteServiceSuccess',
	DeleteServiceFailure: 'DeleteServiceFailure',
	DuplicateServiceSuccess: 'DuplicateServiceSuccess',
	DuplicateServiceFailure: 'DuplicateServiceFailure',
	ReorderServicesSuccess: 'ReorderServicesSuccess',
	ReorderServicesFailure: 'ReorderServicesFailure',
	ServiceType: 'ServiceType',
	ServiceSalesTax: 'ServiceSalesTax',
	SalesTaxIncluded: 'SalesTaxIncluded',
	SalesTaxNotIncluded: 'SalesTaxNotIncluded',
	IncludeSalesTax: 'IncludeSalesTax',
	DefaultService: 'DefaultService',
	SalesTaxHelp: 'SalesTaxHelp',
	SelectServices: 'SelectServices',
	Price: 'Price',
	PricePerClient: 'PricePerClient',
	AddService: 'AddService',
	AddServices: 'AddServices',
	AddDetail: 'AddDetail',
	DetailDurationWithStaff: 'DetailDurationWithStaff',
	AddAnother: 'AddAnother',
	AddAnotherAccount: 'AddAnotherAccount',
	AddAnotherContact: 'AddAnotherContact',
	AddAnotherTeamMember: 'AddAnotherTeamMember',
	AddEmail: 'AddEmail',
	AddPhoneNumber: 'AddPhoneNumber',
	AddAddress: 'AddAddress',
	AddLanguage: 'AddLanguage',
	CreateNewService: 'CreateNewService',
	CreateNewServiceMenu: 'CreateNewServiceMenu',
	CreateNewServiceGroupMenu: 'CreateNewServiceGroupMenu',
	EditServiceGroup: 'EditServiceGroup',
	CreateNewServiceGroupSuccess: 'CreateNewServiceGroupSuccess',
	CreateNewServiceGroupFailure: 'CreateNewServiceGroupFailure',
	EditServiceGroupSuccess: 'EditServiceGroupSuccess',
	EditServiceGroupFailure: 'EditServiceGroupFailure',
	ReorderServiceGroupSuccess: 'ReorderServiceGroupSuccess',
	ReorderServiceGroupFailure: 'ReorderServiceGroupFailure',
	ServiceAlreadyExistsInCollection: 'ServiceAlreadyExistsInCollection',
	CannotMoveServiceOutsideCollections: 'CannotMoveServiceOutsideCollections',
	NameIsRequired: 'NameIsRequired',
	CustomerNameIsRequired: 'CustomerNameIsRequired',
	ServiceBookableOnlineIndicator: 'ServiceBookableOnlineIndicator',
	ServiceAllowNewClientsIndicator: 'ServiceAllowNewClientsIndicator',
	ServiceEmptyState: 'ServiceEmptyState',
	NoServicesApplied: 'NoServicesApplied',
	BookingLink: 'BookingLink',
	Booking: 'Booking',
	ServiceColour: 'ServiceColour',
	CalendarDescription: 'CalendarDescription',
	CalendarScheduleNew: 'CalendarScheduleNew',
	StartVideoCall: 'StartVideoCall',
	StartCall: 'StartCall',
	InviteRelationships: 'InviteRelationships',
	ReminderSettings: 'ReminderSettings',
	AppointmentReminders: 'AppointmentReminders',
	NoRemindersFound: 'NoRemindersFound',
	AppointmentRemindersInfo: 'AppointmentRemindersInfo',
	InvoiceReminder: 'InvoiceReminder',
	InvoiceReminderSettings: 'InvoiceReminderSettings',
	InvoiceReminderSettingsInfo: 'InvoiceReminderSettingsInfo',
	DefaultInPerson: 'DefaultInPerson',
	InvoiceReminders: 'InvoiceReminders',
	InvoiceRemindersInfo: 'InvoiceRemindersInfo',
	DefaultVideo: 'DefaultVideo',
	BeforeAppointment: 'BeforeAppointment',
	InvoiceReminderSentence: 'InvoiceReminderSentence',
	DeliveryMethod: 'DeliveryMethod',
	NewInvoice: 'NewInvoice',
	GoToInvoiceTemplates: 'GoToInvoiceTemplates',
	InvoicePageHeader: 'InvoicePageHeader',
	InvoiceNumber: 'InvoiceNumber',
	Invoice: 'Invoice',
	ServiceDate: 'ServiceDate',
	InvoiceDate: 'InvoiceDate',
	Due: 'Due',
	DueDate: 'DueDate',
	PaymentDate: 'PaymentDate',
	PayoutDate: 'PayoutDate',
	InvoiceDetails: 'InvoiceDetails',
	DeleteInvoiceConfirmationDescription: 'DeleteInvoiceConfirmationDescription',
	CreateNote: 'CreateNote',
	CreateInvoice: 'CreateInvoice',
	CreateClaim: 'CreateClaim',
	IncludeLineItems: 'IncludeLineItems',
	BillTo: 'BillTo',
	ContactInformation: 'ContactInformation',
	AddDate: 'AddDate',
	AddCode: 'AddCode',
	AddDxCode: 'AddDxCode',
	Export: 'Export',
	Exporting: 'Exporting',
	ExportingData: 'ExportingData',
	ExportFailed: 'ExportFailed',
	ExportClientsModalTitle: 'ExportClientsModalTitle',
	ExportClientsModalDescription: 'ExportClientsModalDescription',
	ExportTransactions: 'ExportTransactions',
	ExportInvoiceFileName: 'ExportInvoiceFileName',
	ExportClientsFailureSnackbarTitle: 'ExportClientsFailureSnackbarTitle',
	ExportClientsFailureSnackbarDescription: 'ExportClientsFailureSnackbarDescription',
	ExportClientsDownloadFailureSnackbarTitle: 'ExportClientsDownloadFailureSnackbarTitle',
	ExportClientsDownloadFailureSnackbarDescription: 'ExportClientsDownloadFailureSnackbarDescription',
	Dates: 'Dates',
	Status: 'Status',
	StaffMembers: 'StaffMembers',
	AddPayment: 'AddPayment',
	ChoosePaymentMethod: 'ChoosePaymentMethod',
	EmailInvoice: 'EmailInvoice',
	EditInvoice: 'EditInvoice',
	SendOnlinePayment: 'SendOnlinePayment',
	SendPaymentLink: 'SendPaymentLink',
	CopyPaymentLink: 'CopyPaymentLink',
	StripeFeesDescription: 'StripeFeesDescription',
	StripeFeesDescriptionItem1: 'StripeFeesDescriptionItem1',
	StripeFeesDescriptionItem2: 'StripeFeesDescriptionItem2',
	StripeFeesLinkToRatesText: 'StripeFeesLinkToRatesText',
	TaxRatesDescription: 'TaxRatesDescription',
	UseAsDefault: 'UseAsDefault',
	NewTaxRate: 'NewTaxRate',
	SendOnlinePaymentTooltipTitleAdmin: 'SendOnlinePaymentTooltipTitleAdmin',
	SendOnlinePaymentTooltipTitleStaff: 'SendOnlinePaymentTooltipTitleStaff',
	SetupOnlineStripePayments: 'SetupOnlineStripePayments',
	Setup: 'Setup',
	GoToPaymentSettings: 'GoToPaymentSettings',
	CreditDebitCard: 'CreditDebitCard',
	DirectDebit: 'DirectDebit',
	StripeLink: 'StripeLink',
	Cash: 'Cash',
	InternetBanking: 'InternetBanking',
	Online: 'Online',
	Pay: 'Pay',
	PayNow: 'PayNow',
	PayValue: 'PayValue',
	TotalPaid: 'TotalPaid',
	TotalPaidTooltip: 'TotalPaidTooltip',
	TotalUnpaid: 'TotalUnpaid',
	TotalUnpaidTooltip: 'TotalUnpaidTooltip',
	TotalOverdue: 'TotalOverdue',
	TotalOverdueTooltip: 'TotalOverdueTooltip',
	StripeBalance: 'StripeBalance',
	Invoices: 'Invoices',
	IssueDate: 'IssueDate',
	SaveCardForFuturePayments: 'SaveCardForFuturePayments',
	AllowToUseSavedCard: 'AllowToUseSavedCard',
	FuturePaymentsSavePaymentMethod: 'FuturePaymentsSavePaymentMethod',
	FuturePaymentsAuthoriseProvider: 'FuturePaymentsAuthoriseProvider',
	PaymentMethodLabelCard: 'PaymentMethodLabelCard',
	PaymentMethodLabelFallback: 'PaymentMethodLabelFallback',
	PaymentMethodLabelBank: 'PaymentMethodLabelBank',
	LogInToSaveOrAuthoriseCard: 'LogInToSaveOrAuthoriseCard',
	LogInToSaveOrAuthorisePayment: 'LogInToSaveOrAuthorisePayment',
	SkipLogin: 'SkipLogin',
	Skip: 'Skip',
	InvoiceNotFoundTitle: 'InvoiceNotFoundTitle',
	InvoiceNotFoundDescription: 'InvoiceNotFoundDescription',
	SuperbillNotFoundTitle: 'SuperbillNotFoundTitle',
	SuperbillNotFoundDescription: 'SuperbillNotFoundDescription',
	SuperbillNumberAlreadyExists: 'SuperbillNumberAlreadyExists',
	InvoiceTemplateNotFoundTitle: 'InvoiceTemplateNotFoundTitle',
	InvoiceTemplateNotFoundDescription: 'InvoiceTemplateNotFoundDescription',
	PaymentSuccessful: 'PaymentSuccessful',
	PaymentProcessing: 'PaymentProcessing',
	PaymentMethods: 'PaymentMethods',
	DefaultPaymentMethod: 'DefaultPaymentMethod',
	DefaultValue: 'DefaultValue',
	AuthoriseProvider: 'AuthoriseProvider',
	DeleteCardConfirmation: 'DeleteCardConfirmation',
	AuthorisedProviders: 'AuthorisedProviders',
	NoPaymentMethods: 'NoPaymentMethods',
	StripePaymentsUnavailable: 'StripePaymentsUnavailable',
	StripePaymentsUnavailableDescription: 'StripePaymentsUnavailableDescription',
	StripeAccountNotCompleteErrorCode: 'StripeAccountNotCompleteErrorCode',
	ClientPaymentMethodDescription: 'ClientPaymentMethodDescription',
	CreditCardExpire: 'CreditCardExpire',
	CreditCard: 'CreditCard',
	Unrecognised: 'Unrecognised',
	UnrecognisedDescription: 'UnrecognisedDescription',
	SetAsDefault: 'SetAsDefault',
	RemoveAsDefault: 'RemoveAsDefault',
	Uninvoiced: 'Uninvoiced',
	UninvoicedAmount: 'UninvoicedAmount',
	UnclaimedAmount: 'UnclaimedAmount',
	Unpaid: 'Unpaid',
	Paid: 'Paid',
	Sent: 'Sent',
	AnyStatus: 'AnyStatus',
	Void: 'Void',
	Processing: 'Processing',
	PaidMultiple: 'PaidMultiple',
	UnpaidMultiple: 'UnpaidMultiple',
	InvoiceNumberMustEndWithDigit: 'InvoiceNumberMustEndWithDigit',
	ClientOnboardingRegistrationInstructions: 'ClientOnboardingRegistrationInstructions',
	ClientOnboardingStepYourDetails: 'ClientOnboardingStepYourDetails',
	ClientOnboardingStepHealthDetails: 'ClientOnboardingStepHealthDetails',
	ClientOnboardingStepFormsAndAgreements: 'ClientOnboardingStepFormsAndAgreements',
	ClientOnboardingStepFormsAndAgreementsDesc1: 'ClientOnboardingStepFormsAndAgreementsDesc1',
	ClientOnboardingStepPassword: 'ClientOnboardingStepPassword',
	ClientOnboardingProviderIntroTitle: 'ClientOnboardingProviderIntroTitle',
	ClientOnboardingProviderIntroSignupButton: 'ClientOnboardingProviderIntroSignupButton',
	ClientOnboardingProviderIntroSignupFamilyButton: 'ClientOnboardingProviderIntroSignupFamilyButton',
	ClientOnboardingCompleteIntake: 'ClientOnboardingCompleteIntake',
	ClientOnboardingDashboardButton: 'ClientOnboardingDashboardButton',
	ClientOnboardingRegistrationTitle: 'ClientOnboardingRegistrationTitle',
	ClientOnboardingChoosePasswordTitle1: 'ClientOnboardingChoosePasswordTitle1',
	ClientOnboardingChoosePasswordTitle2: 'ClientOnboardingChoosePasswordTitle2',
	ClientOnboardingPasswordRequirements: 'ClientOnboardingPasswordRequirements',
	ClientOnboardingPasswordRequirementsConditions1: 'ClientOnboardingPasswordRequirementsConditions1',
	ClientOnboardingHealthRecordsTitle: 'ClientOnboardingHealthRecordsTitle',
	ClientOnboardingHealthRecordsDesc1: 'ClientOnboardingHealthRecordsDesc1',
	ClientOnboardingHealthRecordsDescription: 'ClientOnboardingHealthRecordsDescription',
	ClientOnboardingConfirmationScreenTitle: 'ClientOnboardingConfirmationScreenTitle',
	ClientOnboardingConfirmationScreenText: 'ClientOnboardingConfirmationScreenText',
	PaymentMethod: 'PaymentMethod',
	SavePaymentMethod: 'SavePaymentMethod',
	SavePaymentMethodDescription: 'SavePaymentMethodDescription',
	SavePaymentMethodSetupInvoiceLater: 'SavePaymentMethodSetupInvoiceLater',
	SavePaymentMethodSetupError: 'SavePaymentMethodSetupError',
	DragDropText: 'DragDropText',
	InboxChatDragDropText: 'InboxChatDragDropText',
	DragToMove: 'DragToMove',
	DateOfService: 'DateOfService',
	DateIssued: 'DateIssued',
	DateDue: 'DateDue',
	From: 'From',
	Service: 'Service',
	ServicesCount: 'ServicesCount',
	Cost: 'Cost',
	ServicesProvidedBy: 'ServicesProvidedBy',
	Total: 'Total',
	Tax: 'Tax',
	TaxName: 'TaxName',
	TaxNumber: 'TaxNumber',
	Taxable: 'Taxable',
	GST: 'GST',
	VAT: 'VAT',
	SalesTax: 'SalesTax',
	Submit: 'Submit',
	SubmitFormValidationError: 'SubmitFormValidationError',
	ThePaymentMethod: 'ThePaymentMethod',
	SpecifyPaymentMethod: 'SpecifyPaymentMethod',
	NameOnCard: 'NameOnCard',
	CustomerName: 'CustomerName',
	CardInformation: 'CardInformation',
	ClientProcessingFee: 'ClientProcessingFee',
	PaymentProcessingFee: 'PaymentProcessingFee',
	To: 'To',
	Subject: 'Subject',
	Body: 'Body',
	SendMeACopy: 'SendMeACopy',
	FillOut: 'FillOut',
	NewUIUpdateBannerTitle: 'NewUIUpdateBannerTitle',
	NewUIUpdateBannerButton: 'NewUIUpdateBannerButton',
	SendIntake: 'SendIntake',
	ClientIntakeModalTitle: 'ClientIntakeModalTitle',
	ClientIntakeModalDescription: 'ClientIntakeModalDescription',
	ClientIntakeFormsNotSupported: 'ClientIntakeFormsNotSupported',
	ConsentDocumentation: 'ConsentDocumentation',
	ConsentDocumentationPublicTemplateError: 'ConsentDocumentationPublicTemplateError',
	SubscriptionUnpaidBannerTitle: 'SubscriptionUnpaidBannerTitle',
	SubscriptionUnpaidBannerDescription: 'SubscriptionUnpaidBannerDescription',
	SubscriptionUnpaidBannerButton: 'SubscriptionUnpaidBannerButton',
	SubscriptionUnavailableOverStorageLimit: 'SubscriptionUnavailableOverStorageLimit',
	ChangeSubscription: 'ChangeSubscription',
	CancelSubscription: 'CancelSubscription',
	WhatMadeYouCancel: 'WhatMadeYouCancel',
	AdditionalFeedback: 'AdditionalFeedback',
	OurResearchTeamReachOut: 'OurResearchTeamReachOut',
	YesImFineWithThat: 'YesImFineWithThat',
	DidntProvideEnoughValue: 'DidntProvideEnoughValue',
	DoesntWorkWellWithExistingTools: 'DoesntWorkWellWithExistingTools',
	InviteToPortal: 'InviteToPortal',
	InviteToPortalModalTitle: 'InviteToPortalModalTitle',
	InviteToPortalModalDescription: 'InviteToPortalModalDescription',
	ConnectedApps: 'ConnectedApps',
	ConnectedAppsGoogleDescription: 'ConnectedAppsGoogleDescription',
	ConnectedAppsMicrosoftDescription: 'ConnectedAppsMicrosoftDescription',
	ConnectedAppsGMailDescription: 'ConnectedAppsGMailDescription',
	ConnectedAppsGoogleCalendarDescription: 'ConnectedAppsGoogleCalendarDescription',
	ConnectedCalendars: 'ConnectedCalendars',
	Product: 'Product',
	SyncCalendar: 'SyncCalendar',
	ConnectZoom: 'ConnectZoom',
	EditConnectedAppButton: 'EditConnectedAppButton',
	ManageConnections: 'ManageConnections',
	EditConnections: 'EditConnections',
	Google: 'Google',
	Microsoft: 'Microsoft',
	MicrosoftOutlook: 'MicrosoftOutlook',
	MicrosoftCalendar: 'MicrosoftCalendar',
	ManageConnectionsMicrosoftCalendarDescription: 'ManageConnectionsMicrosoftCalendarDescription',
	ManageConnectionsOutlookDescription: 'ManageConnectionsOutlookDescription',
	Account: 'Account',
	PushToCalendar: 'PushToCalendar',
	DisplayCalendar: 'DisplayCalendar',
	AllowVideoCalls: 'AllowVideoCalls',
	DisconnectAppConfirmation: 'DisconnectAppConfirmation',
	DisconnectAppConfirmationTitle: 'DisconnectAppConfirmationTitle',
	DisconnectAppConfirmationDescription: 'DisconnectAppConfirmationDescription',
	SubscriptionsAndPayments: 'SubscriptionsAndPayments',
	SubscriptionSettings: 'SubscriptionSettings',
	SubscriptionSummary: 'SubscriptionSummary',
	Plan: 'Plan',
	Users: 'Users',
	Storage: 'Storage',
	UpgradePlan: 'UpgradePlan',
	UpgradeYourPlan: 'UpgradeYourPlan',
	CancelPlan: 'CancelPlan',
	PaymentDetails: 'PaymentDetails',
	EnterSubscriptionPaymentDetails: 'EnterSubscriptionPaymentDetails',
	EditSubscriptionUpgradePlanTitle: 'EditSubscriptionUpgradePlanTitle',
	EditSubscriptionUpgradeAdjustTeamBanner: 'EditSubscriptionUpgradeAdjustTeamBanner',
	EditSubscriptionUpgradeContent: 'EditSubscriptionUpgradeContent',
	EditSubscriptionBilledQuantity: 'EditSubscriptionBilledQuantity',
	EditSubscriptionBilledQuantityValue: 'EditSubscriptionBilledQuantityValue',
	EditSubscriptionLimitedTimeOffer: 'EditSubscriptionLimitedTimeOffer',
	CancelPlanConfirmation: 'CancelPlanConfirmation',
	ManagePlan: 'ManagePlan',
	PricePerUserPerPeriod: 'PricePerUserPerPeriod',
	StaffMembersNumber: 'StaffMembersNumber',
	Starter: 'Starter',
	Free: 'Free',
	SubProfessional: 'SubProfessional',
	SubOrganization: 'SubOrganization',
	SubEssential: 'SubEssential',
	SubPlus: 'SubPlus',
	SubAdvanced: 'SubAdvanced',
	ChoosePlan: 'ChoosePlan',
	DowngradeTo: 'DowngradeTo',
	SubscriptionSettingsUnlimitedStorage: 'SubscriptionSettingsUnlimitedStorage',
	SubscriptionSettingsStorageUsed: 'SubscriptionSettingsStorageUsed',
	SubscriptionSettingsPercentageUsed: 'SubscriptionSettingsPercentageUsed',
	MonthlyCost: 'MonthlyCost',
	SubscribePerMonth: 'SubscribePerMonth',
	ConfirmSubscriptionUpdate: 'ConfirmSubscriptionUpdate',
	PaymentForUsersPerMonth: 'PaymentForUsersPerMonth',
	EnterPaymentDetailsDescription: 'EnterPaymentDetailsDescription',
	PaymentAccountUpgraded: 'PaymentAccountUpgraded',
	PaymentAccountUpdated: 'PaymentAccountUpdated',
	StorageFullTitle: 'StorageFullTitle',
	StorageFullDescription: 'StorageFullDescription',
	StorageAlmostFullTitle: 'StorageAlmostFullTitle',
	StorageAlmostFullDescription: 'StorageAlmostFullDescription',
	UpgradeNow: 'UpgradeNow',
	Get50PercentOff: 'Get50PercentOff',
	BillingPeriod: 'BillingPeriod',
	Monthly: 'Monthly',
	Annually: 'Annually',
	SyncCalendarModalDescription: 'SyncCalendarModalDescription',
	SyncCalendarModalSyncWithCalendar: 'SyncCalendarModalSyncWithCalendar',
	SyncCalendarModalDisplayCalendar: 'SyncCalendarModalDisplayCalendar',
	SyncCalendarModalSyncToCarepatron: 'SyncCalendarModalSyncToCarepatron',
	ConnectZoomModalDescription: 'ConnectZoomModalDescription',
	PromoCode: 'PromoCode',
	Check: 'Check',
	ApplyDiscount: 'ApplyDiscount',
	ProfilePopoverTitle: 'ProfilePopoverTitle',
	ProfilePopoverSubTitle: 'ProfilePopoverSubTitle',
	ManageProfile: 'ManageProfile',
	ClientView: 'ClientView',
	AddnNewWorkspace: 'AddnNewWorkspace',
	WorkspaceName: 'WorkspaceName',
	WorkspaceBranding: 'WorkspaceBranding',
	WorkspaceBrandingDescription: 'WorkspaceBrandingDescription',
	CloseImportClientsModal: 'CloseImportClientsModal',
	ImportSpreadsheetTitle: 'ImportSpreadsheetTitle',
	ImportSpreadsheetDescription: 'ImportSpreadsheetDescription',
	ImportingSpreadsheetTitle: 'ImportingSpreadsheetTitle',
	ImportingSpreadsheetDescription: 'ImportingSpreadsheetDescription',
	ChooseFileButton: 'ChooseFileButton',
	CancelImportButton: 'CancelImportButton',
	SelectCorrespondingAttributes: 'SelectCorrespondingAttributes',
	YourSpreadsheetColumns: 'YourSpreadsheetColumns',
	CarepatronFields: 'CarepatronFields',
	SpreadsheetColumnExample: 'SpreadsheetColumnExample',
	DoNotImport: 'DoNotImport',
	RequiredField: 'RequiredField',
	YouCanSelectMultiple: 'YouCanSelectMultiple',
	YouCanChooseMultiple: 'YouCanChooseMultiple',
	FieldOptionsWholeField: 'FieldOptionsWholeField',
	FieldOptionsFirstPart: 'FieldOptionsFirstPart',
	FieldOptionsSecondPart: 'FieldOptionsSecondPart',
	FieldOptionsMiddlePart: 'FieldOptionsMiddlePart',
	SpreadsheetUploaded: 'SpreadsheetUploaded',
	SpreadsheetUploading: 'SpreadsheetUploading',
	RearrangeClientFields: 'RearrangeClientFields',
	GoToClientSettings: 'GoToClientSettings',
	CarepatronFieldFirstName: 'CarepatronFieldFirstName',
	CarepatronFieldLastName: 'CarepatronFieldLastName',
	CarepatronFieldMiddleNames: 'CarepatronFieldMiddleNames',
	CarepatronFieldBirthDate: 'CarepatronFieldBirthDate',
	CarepatronFieldGender: 'CarepatronFieldGender',
	CarepatronFieldEthnicity: 'CarepatronFieldEthnicity',
	CarepatronFieldPhoneNumber: 'CarepatronFieldPhoneNumber',
	CarepatronFieldEmail: 'CarepatronFieldEmail',
	CarepatronFieldLabel: 'CarepatronFieldLabel',
	CarepatronFieldAddress: 'CarepatronFieldAddress',
	CarepatronFieldIdentificationNumber: 'CarepatronFieldIdentificationNumber',
	CarepatronFieldStatus: 'CarepatronFieldStatus',
	CarepatronFieldStatusHelperText: 'CarepatronFieldStatusHelperText',
	CarepatronFieldOccupation: 'CarepatronFieldOccupation',
	CarepatronFieldEmploymentStatus: 'CarepatronFieldEmploymentStatus',
	CarepatronFieldLivingArrangements: 'CarepatronFieldLivingArrangements',
	CarepatronFieldRelationshipStatus: 'CarepatronFieldRelationshipStatus',
	CarepatronFieldIsArchived: 'CarepatronFieldIsArchived',
	CarepatronFieldAssignedStaff: 'CarepatronFieldAssignedStaff',
	CarepatronFieldTags: 'CarepatronFieldTags',

	ContactEmailLabelPersonal: 'ContactEmailLabelPersonal',
	ContactEmailLabelWork: 'ContactEmailLabelWork',
	ContactEmailLabelSchool: 'ContactEmailLabelSchool',
	ContactEmailLabelOthers: 'ContactEmailLabelOthers',
	ContactPhoneLabelMobile: 'ContactPhoneLabelMobile',
	ContactPhoneLabelHome: 'ContactPhoneLabelHome',
	ContactPhoneLabelWork: 'ContactPhoneLabelWork',
	ContactPhoneLabelSchool: 'ContactPhoneLabelSchool',
	ContactAddressLabelHome: 'ContactAddressLabelHome',
	ContactAddressLabelBilling: 'ContactAddressLabelBilling',
	ContactAddressLabelWork: 'ContactAddressLabelWork',
	ContactAddressLabelOthers: 'ContactAddressLabelOthers',

	ChooseColor: 'ChooseColor',
	InvoiceColorTheme: 'InvoiceColorTheme',
	InvoiceColorPickerDescription: 'InvoiceColorPickerDescription',
	ContinueWithGoogle: 'ContinueWithGoogle',
	ContinueWithApple: 'ContinueWithApple',
	SignInWithGoogle: 'SignInWithGoogle',
	SignInWithApple: 'SignInWithApple',
	TemplatesPublicTemplatesFilter: 'TemplatesPublicTemplatesFilter',
	TemplatesCategoriesFilter: 'TemplatesCategoriesFilter',
	GettingStartedNavItem: 'GettingStartedNavItem',
	GettingStartedPageTitle: 'GettingStartedPageTitle',
	GettingStartedVideo: 'GettingStartedVideo',
	GettingStartedClients: 'GettingStartedClients',
	GettingStartedAppointmentTypes: 'GettingStartedAppointmentTypes',
	GettingStartedSyncCalendars: 'GettingStartedSyncCalendars',
	GettingStartedInvoices: 'GettingStartedInvoices',
	GettingStartedPayments: 'GettingStartedPayments',
	GettingStartedMobileApp: 'GettingStartedMobileApp',
	GettingStartedVideoTitle: 'GettingStartedVideoTitle',
	GettingStartedVideoDescription: 'GettingStartedVideoDescription',
	GettingStartedClientsTitle: 'GettingStartedClientsTitle',
	GettingStartedClientsDescription: 'GettingStartedClientsDescription',
	GettingStartedAppointmentTypesTitle: 'GettingStartedAppointmentTypesTitle',
	GettingStartedAppointmentTypesDescription: 'GettingStartedAppointmentTypesDescription',
	GettingStartedSyncCalendarsTitle: 'GettingStartedSyncCalendarsTitle',
	GettingStartedSyncCalendarsDescription: 'GettingStartedSyncCalendarsDescription',
	GettingStartedInvoicesTitle: 'GettingStartedInvoicesTitle',
	GettingStartedInvoicesDescription: 'GettingStartedInvoicesDescription',
	GettingStartedPaymentsTitle: 'GettingStartedPaymentsTitle',
	GettingStartedPaymentsDescription: 'GettingStartedPaymentsDescription',
	GettingStartedMobileAppTitle: 'GettingStartedMobileAppTitle',
	GettingStartedMobileAppDescription: 'GettingStartedMobileAppDescription',
	GettingStartedSaveBranding: 'GettingStartedSaveBranding',
	GettingStartedImportClients: 'GettingStartedImportClients',
	GettingStartedCreateClient: 'GettingStartedCreateClient',
	GetttingStartedGetMobileDownload: 'GetttingStartedGetMobileDownload',
	GetttingStartedGetMobileNoDownload: 'GetttingStartedGetMobileNoDownload',
	Permissions: 'Permissions',
	Permission: 'Permission',
	PermissionsViewAccess: 'PermissionsViewAccess',
	PermissionsEditAccess: 'PermissionsEditAccess',
	PermissionsClientAndContactProfiles: 'PermissionsClientAndContactProfiles',
	PermissionsClientAndContactDocumentation: 'PermissionsClientAndContactDocumentation',
	PermissionsInvoicesAndPayments: 'PermissionsInvoicesAndPayments',
	PermissionsScheduling: 'PermissionsScheduling',
	PermissionsWorkspaceSettings: 'PermissionsWorkspaceSettings',
	PermissionsValuesAssigned: 'PermissionsValuesAssigned',
	PermissionsValuesEverything: 'PermissionsValuesEverything',
	PermissionsValuesNone: 'PermissionsValuesNone',
	PermissionsValuesOwnCalendar: 'PermissionsValuesOwnCalendar',
	PermissionsUnassignClients: 'PermissionsUnassignClients',
	PermissionsUnassignClientsConfirmation: 'PermissionsUnassignClientsConfirmation',
	Owner: 'Owner',
	Team: 'Team',
	ServicesAndAvailability: 'ServicesAndAvailability',
	AssignedServices: 'AssignedServices',
	AssignedServicesDescription: 'AssignedServicesDescription',
	CustomRate: 'CustomRate',
	FallbackPageTitle: 'FallbackPageTitle',
	FallbackPageDescription: 'FallbackPageDescription',
	FallbackPageDescriptionUpdateButton: 'FallbackPageDescriptionUpdateButton',
	ValidationMixedDefault: 'ValidationMixedDefault',
	ValidationMixedRequired: 'ValidationMixedRequired',
	ValidationStringEmail: 'ValidationStringEmail',
	ValidationStringMin: 'ValidationStringMin',
	ValidationStringMax: 'ValidationStringMax',
	ValidationDateMin: 'ValidationDateMin',
	ValidationDateMax: 'ValidationDateMax',
	ValidationNumberMin: 'ValidationNumberMin',
	ValidationNumberMax: 'ValidationNumberMax',
	ValidationNumberInteger: 'ValidationNumberInteger',
	ValidationDateRange: 'ValidationDateRange',
	ValidationPasswordNotMatching: 'ValidationPasswordNotMatching',
	ValidationConfirmPasswordRequired: 'ValidationConfirmPasswordRequired',
	ValidationStringPhoneNumber: 'ValidationStringPhoneNumber',
	ValidationPrimaryPhoneNumberIsRequired: 'ValidationPrimaryPhoneNumberIsRequired',
	ValidationPrimaryAddressIsRequired: 'ValidationPrimaryAddressIsRequired',
	ValidationEndDateMustBeAfterStartDate: 'ValidationEndDateMustBeAfterStartDate',
	DateAndTime: 'DateAndTime',
	ClientBilling: 'ClientBilling',
	ClientBillingAndPaymentHistory: 'ClientBillingAndPaymentHistory',
	ClientBillingAndPaymentIssueDate: 'ClientBillingAndPaymentIssueDate',
	ClientBillingAndPaymentInvoices: 'ClientBillingAndPaymentInvoices',
	ClientBillingAndPaymentServices: 'ClientBillingAndPaymentServices',
	ClientBillingAndPaymentPrice: 'ClientBillingAndPaymentPrice',
	ClientBillingAndPaymentDueDate: 'ClientBillingAndPaymentDueDate',
	ClientBillingAndPaymentStatus: 'ClientBillingAndPaymentStatus',
	ClientBillingAndPaymentReceipt: 'ClientBillingAndPaymentReceipt',
	ClientBillingAddPaymentMethodDescription: 'ClientBillingAddPaymentMethodDescription',
	SuperbillNumber: 'SuperbillNumber',
	SuperbillReceipts: 'SuperbillReceipts',
	CreateSuperbillReceipt: 'CreateSuperbillReceipt',
	EditSuperbillReceipt: 'EditSuperbillReceipt',
	SaveAsDraft: 'SaveAsDraft',
	StatementNumber: 'StatementNumber',
	AddPaidInvoices: 'AddPaidInvoices',
	Provider: 'Provider',
	BillingSettings: 'BillingSettings',
	Practice: 'Practice',
	DownloadPDF: 'DownloadPDF',
	Dx: 'Dx',
	DxCode: 'DxCode',
	LastDxCode: 'LastDxCode',
	DiagnosisCode: 'DiagnosisCode',
	DiagnosticCodes: 'DiagnosticCodes',
	DiagnosisCodeSelectorPlaceholder: 'DiagnosisCodeSelectorPlaceholder',
	ServiceCodeSelectorPlaceholder: 'ServiceCodeSelectorPlaceholder',
	ServicesAndDiagnosisCodesHeader: 'ServicesAndDiagnosisCodesHeader',
	DiagnosisCodeErrorMessage: 'DiagnosisCodeErrorMessage',
	DateErrorMessage: 'DateErrorMessage',
	ServiceErrorMessage: 'ServiceErrorMessage',
	CodeErrorMessage: 'CodeErrorMessage',
	PosCodeErrorMessage: 'PosCodeErrorMessage',
	DXErrorMessage: 'DXErrorMessage',
	CostErrorMessage: 'CostErrorMessage',
	TemplatePreviewErrorMessage: 'TemplatePreviewErrorMessage',
	ServiceCodeErrorMessage: 'ServiceCodeErrorMessage',
	PriceError: 'PriceError',
	PaidAmountRequiredError: 'PaidAmountRequiredError',
	PaidAmountMinimumValueError: 'PaidAmountMinimumValueError',
	DiagnosisCodeSelectorTooltip: 'DiagnosisCodeSelectorTooltip',
	PreviewAndSend: 'PreviewAndSend',
	DeleteSuperbillConfirmationDescription: 'DeleteSuperbillConfirmationDescription',
	DeleteSuperbillSuccess: 'DeleteSuperbillSuccess',
	DeleteSuperbillFailure: 'DeleteSuperbillFailure',
	EmailSuperbillReceipt: 'EmailSuperbillReceipt',
	EmailSuperbillReceiptSuccess: 'EmailSuperbillReceiptSuccess',
	EmailSuperbillReceiptFailure: 'EmailSuperbillReceiptFailure',
	UpdateSuperbillReceiptSuccess: 'UpdateSuperbillReceiptSuccess',
	UpdateSuperbillReceiptFailure: 'UpdateSuperbillReceiptFailure',
	CreateSuperbillReceiptSuccess: 'CreateSuperbillReceiptSuccess',
	Statement: 'Statement',
	PracticeDetails: 'PracticeDetails',
	Code: 'Code',
	SuperbillProviderHelperText: 'SuperbillProviderHelperText',
	SuperbillClientHelperText: 'SuperbillClientHelperText',
	SuperbillPracticeHelperText: 'SuperbillPracticeHelperText',
	BillingAndReceiptsUnauthorisedMessage: 'BillingAndReceiptsUnauthorisedMessage',
	NoRecordsToDisplay: 'NoRecordsToDisplay',
	BillingDocuments: 'BillingDocuments',
	BillingDocumentsEmptyState: 'BillingDocumentsEmptyState',
	BillingDocumentsInvoicesTab: 'BillingDocumentsInvoicesTab',
	BillingDocumentsSuperbillsTab: 'BillingDocumentsSuperbillsTab',
	BillingDocumentsClaimsTab: 'BillingDocumentsClaimsTab',
	AutogenerateBillings: 'AutogenerateBillings',
	AutogenerateBillingsDescription: 'AutogenerateBillingsDescription',
	AutomaticallySendSuperbill: 'AutomaticallySendSuperbill',
	AutomaticallySendSuperbillHelperText: 'AutomaticallySendSuperbillHelperText',
	SuperbillAutomationNotActive: 'SuperbillAutomationNotActive',
	SuperbillAutomationMonthly: 'SuperbillAutomationMonthly',
	SuperbillAutomationUpdateSuccess: 'SuperbillAutomationUpdateSuccess',
	SuperbillAutomationUpdateFailure: 'SuperbillAutomationUpdateFailure',
	SuperbillAutomationNoEmail: 'SuperbillAutomationNoEmail',
	ClientNotes: 'ClientNotes',
	ChooseCustomDate: 'ChooseCustomDate',
	DangerZone: 'DangerZone',
	Transfer: 'Transfer',
	TransferOwnership: 'TransferOwnership',
	TransferOwnershipDescription: 'TransferOwnershipDescription',
	TransferOwnershipConfirmationModalDescription: 'TransferOwnershipConfirmationModalDescription',
	ChooseNewOwner: 'ChooseNewOwner',
	TransferOwnershipSuccessSnackbar: 'TransferOwnershipSuccessSnackbar',
	PaymentType: 'PaymentType',
	IssueCredit: 'IssueCredit',
	MarkAsPaid: 'MarkAsPaid',
	MarkAsVoid: 'MarkAsVoid',
	MarkAsUnpaid: 'MarkAsUnpaid',
	DurationInMinutes: 'DurationInMinutes',
	Availability: 'Availability',
	Timezone: 'Timezone',
	PrimaryTimezone: 'PrimaryTimezone',
	SecondaryTimezone: 'SecondaryTimezone',
	AddASecondTimezone: 'AddASecondTimezone',
	SearchTimezone: 'SearchTimezone',
	Unavailable: 'Unavailable',
	ShareBookingLink: 'ShareBookingLink',
	BookingLinkModalTitle: 'BookingLinkModalTitle',
	BookingLinkModalDescription: 'BookingLinkModalDescription',
	BookingLinkModalHelpText: 'BookingLinkModalHelpText',
	BookingLinkModalCopyText: 'BookingLinkModalCopyText',
	RescheduleBookingLinkModalTitle: 'RescheduleBookingLinkModalTitle',
	RescheduleBookingLinkModalDescription: 'RescheduleBookingLinkModalDescription',
	BookingFormatLink: 'BookingFormatLink',
	Format: 'Format',
	BookingFormatLinkButtonTitle: 'BookingFormatLinkButtonTitle',
	FormatLinkButtonColor: 'FormatLinkButtonColor',
	BookingDirectTextLink: 'BookingDirectTextLink',
	BookingDirectTextLinkDescription: 'BookingDirectTextLinkDescription',
	BookingButtonEmbed: 'BookingButtonEmbed',
	BookingButtonEmbedDescription: 'BookingButtonEmbedDescription',
	BookingInlineEmbed: 'BookingInlineEmbed',
	BookingInlineEmbedDescription: 'BookingInlineEmbedDescription',
	OnlineBookings: 'OnlineBookings',
	BookedOnline: 'BookedOnline',
	ManualAppointment: 'ManualAppointment',
	OnlineBookingsHelper: 'OnlineBookingsHelper',
	BookableOnline: 'BookableOnline',
	BookableOnlineHelper: 'BookableOnlineHelper',
	AllowNewClient: 'AllowNewClient',
	AllowNewClientHelper: 'AllowNewClientHelper',
	AddOnlineBookingDetails: 'AddOnlineBookingDetails',
	AddServiceLocation: 'AddServiceLocation',
	AllLocations: 'AllLocations',
	GeneralAvailability: 'GeneralAvailability',
	GeneralAvailabilityDescription: 'GeneralAvailabilityDescription',
	GeneralAvailabilityInfo: 'GeneralAvailabilityInfo',
	VideoCallOptionInfo: 'VideoCallOptionInfo',
	CreateAvailabilityScheduleSuccess: 'CreateAvailabilityScheduleSuccess',
	UpdateAvailabilityScheduleSuccess: 'UpdateAvailabilityScheduleSuccess',
	DuplicateAvailabilityScheduleSuccess: 'DuplicateAvailabilityScheduleSuccess',
	DeleteAvailabilityScheduleSuccess: 'DeleteAvailabilityScheduleSuccess',
	CreateAvailabilityScheduleFailure: 'CreateAvailabilityScheduleFailure',
	UpdateAvailabilityScheduleFailure: 'UpdateAvailabilityScheduleFailure',
	DuplicateAvailabilityScheduleFailure: 'DuplicateAvailabilityScheduleFailure',
	DeleteAvailabilityScheduleFailure: 'DeleteAvailabilityScheduleFailure',
	CustomizeAppearance: 'CustomizeAppearance',
	CustomizeAppearanceDesc: 'CustomizeAppearanceDesc',
	ShowServiceGroups: 'ShowServiceGroups',
	ShowServiceGroupsDesc: 'ShowServiceGroupsDesc',
	ShowServiceDescription: 'ShowServiceDescription',
	ShowServiceDescriptionDesc: 'ShowServiceDescriptionDesc',
	ProfilePhotoFileSizeLimit: 'ProfilePhotoFileSizeLimit',
	OutsideWorkingHoursHelper: 'OutsideWorkingHoursHelper',
	OutOfOfficeHelper: 'OutOfOfficeHelper',
	DescriptionMustNotExceed: 'DescriptionMustNotExceed',
	OnlinePaymentSettingsStripeNote: 'OnlinePaymentSettingsStripeNote',
	OnlinePaymentSettingCustomInfo: 'OnlinePaymentSettingCustomInfo',
	OnlinePaymentSettingsInfo: 'OnlinePaymentSettingsInfo',
	OnlinePaymentSettingsPaymentsDisabled: 'OnlinePaymentSettingsPaymentsDisabled',
	StripePaymentsDisabled: 'StripePaymentsDisabled',
	ProcessAtTimeOfBookingLabel: 'ProcessAtTimeOfBookingLabel',
	ProcessAtTimeOfBookingDesc: 'ProcessAtTimeOfBookingDesc',
	RequirePaymentMethodLabel: 'RequirePaymentMethodLabel',
	RequirePaymentMethodDesc: 'RequirePaymentMethodDesc',
	ConnectToStripe: 'ConnectToStripe',
	CompleteStripeSetup: 'CompleteStripeSetup',
	BookingAndCancellationPolicies: 'BookingAndCancellationPolicies',
	CancellationPolicy: 'CancellationPolicy',
	NoPolicy: 'NoPolicy',
	MinCancellationTimeViewModeDescription: 'MinCancellationTimeViewModeDescription',
	MinCancellationTimeEditModeDescription: 'MinCancellationTimeEditModeDescription',
	MinimumCancellationTime: 'MinimumCancellationTime',
	DontAllowClientsToCancel: 'DontAllowClientsToCancel',
	AllowClientsToCancelAnytime: 'AllowClientsToCancelAnytime',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'AllowToCancelAtLeastBlankHoursBeforeAppointment',
	MinCancellationTimeUnset: 'MinCancellationTimeUnset',
	OnlyYou: 'OnlyYou',
	Share: 'Share',
	UntitledNote: 'UntitledNote',
	UntitledTemplate: 'UntitledTemplate',
	UntitledConversation: 'UntitledConversation',
	EditorPlaceholder: 'EditorPlaceholder',
	EditorTemplatePlaceholder: 'EditorTemplatePlaceholder',
	ParagraphStyles: 'ParagraphStyles',
	FontFamily: 'FontFamily',
	TypographyParagraph: 'TypographyParagraph',
	TypographyHeading1: 'TypographyHeading1',
	TypographyHeading2: 'TypographyHeading2',
	TypographyHeading3: 'TypographyHeading3',
	TypographyHeading4: 'TypographyHeading4',
	TypographyHeading5: 'TypographyHeading5',
	TypographyP: 'TypographyP',
	TypographyH1: 'TypographyH1',
	TypographyH2: 'TypographyH2',
	TypographyH3: 'TypographyH3',
	TypographyH4: 'TypographyH4',
	TypographyH5: 'TypographyH5',
	AddFormField: 'AddFormField',
	AddSmartDataChips: 'AddSmartDataChips',
	MostCommonlyUsed: 'MostCommonlyUsed',
	Text: 'Text',
	FormBlocks: 'FormBlocks',
	LegalAndConsent: 'LegalAndConsent',
	AddImages: 'AddImages',
	TextColor: 'TextColor',
	ListStyles: 'ListStyles',
	BulletedList: 'BulletedList',
	NumberedList: 'NumberedList',
	CheckList: 'CheckList',
	LineSpacing: 'LineSpacing',
	InsertTable: 'InsertTable',
	TranscribeLiveAudio: 'TranscribeLiveAudio',
	Transcribing: 'Transcribing',
	TemplateDescription: 'TemplateDescription',
	ChooseCollection: 'ChooseCollection',
	ItemsRemaining: 'ItemsRemaining',
	TextAlign: 'TextAlign',
	LeftAlign: 'LeftAlign',
	CenterAlign: 'CenterAlign',
	RightAlign: 'RightAlign',
	Justify: 'Justify',
	IncreaseIndent: 'IncreaseIndent',
	DecreaseIndent: 'DecreaseIndent',
	RecentAppointments: 'RecentAppointments',
	ShareNotesWithClients: 'ShareNotesWithClients',
	Message: 'Message',
	Viewer: 'Viewer',
	Editor: 'Editor',
	Responder: 'Responder',
	SuccessShareNote: 'SuccessShareNote',
	SuccessShareDocument: 'SuccessShareDocument',
	SuccessSavedNoteChanges: 'SuccessSavedNoteChanges',
	ShareNoteTitle: 'ShareNoteTitle',
	UnsavedNoteChangesWarning: 'UnsavedNoteChangesWarning',
	UnsavedTemplateChangesWarning: 'UnsavedTemplateChangesWarning',
	UnsavedChanges: 'UnsavedChanges',
	UnsavedChangesPromptTitle: 'UnsavedChangesPromptTitle',
	UnsavedChangesPromptContent: 'UnsavedChangesPromptContent',
	EmailNotFound: 'EmailNotFound',
	NoAccessGiven: 'NoAccessGiven',
	NoContactsGivenAccess: 'NoContactsGivenAccess',
	AccessGiven: 'AccessGiven',
	AllClients: 'AllClients',
	AllContacts: 'AllContacts',
	AllContactsOf: 'AllContactsOf',
	ClientNotRegistered: 'ClientNotRegistered',
	AlreadyHasAccess: 'AlreadyHasAccess',
	AccessPermissions: 'AccessPermissions',
	Restricted: 'Restricted',
	EveryoneInWorkspace: 'EveryoneInWorkspace',
	JustMe: 'JustMe',
	Unconfirmed: 'Unconfirmed',
	Confirmed: 'Confirmed',
	Attended: 'Attended',
	DidNotAttend: 'DidNotAttend',
	Cancelled: 'Cancelled',
	ChooseTags: 'ChooseTags',
	DxCodeSelectPlaceholder: 'DxCodeSelectPlaceholder',
	ChooseDxCodes: 'ChooseDxCodes',
	Appointments: 'Appointments',
	UpcomingAppointments: 'UpcomingAppointments',
	Past: 'Past',
	Today: 'Today',
	Yesterday: 'Yesterday',
	Upcoming: 'Upcoming',
	ThisWeek: 'ThisWeek',
	LastWeek: 'LastWeek',
	ThisMonth: 'ThisMonth',
	LastMonth: 'LastMonth',
	LastNDays: 'LastNDays',
	NextNDays: 'NextNDays',
	CustomRange: 'CustomRange',
	BookAppointment: 'BookAppointment',
	NoAppointmentsFound: 'NoAppointmentsFound',
	SeeMoreUpcomingAppointments: 'SeeMoreUpcomingAppointments',
	SeeLessUpcomingAppointments: 'SeeLessUpcomingAppointments',
	Repeating: 'Repeating',
	SeeLess: 'SeeLess',
	SeeMore: 'SeeMore',
	GoToAppointment: 'GoToAppointment',
	ClientAppointmentsEmptyStateTitle: 'ClientAppointmentsEmptyStateTitle',
	ClientAppointmentsEmptyStateDescription: 'ClientAppointmentsEmptyStateDescription',
	LegacyBillingItemsNotAvailable: 'LegacyBillingItemsNotAvailable',
	LegacyBillingItemsNotAvailableTitle: 'LegacyBillingItemsNotAvailableTitle',
	EndOfLine: 'EndOfLine',
	DefaultEndOfLine: 'DefaultEndOfLine',
	CustomiseBookingLink: 'CustomiseBookingLink',
	CustomiseBookingLinkServicesLabel: 'CustomiseBookingLinkServicesLabel',
	CustomiseBookingLinkServicesInfo: 'CustomiseBookingLinkServicesInfo',
	CopyLink: 'CopyLink',
	UpdateLink: 'UpdateLink',
	CopyCode: 'CopyCode',
	RescheduleLink: 'RescheduleLink',
	SetAvailability: 'SetAvailability',
	Calendar: 'Calendar',
	GoToAvailability: 'GoToAvailability',
	TeamMembersColour: 'TeamMembersColour',
	OnlinePaymentSettings: 'OnlinePaymentSettings',
	UseWorkspaceDefault: 'UseWorkspaceDefault',
	ViewBy: 'ViewBy',
	Default: 'Default',
	UseCustom: 'UseCustom',
	Sunday: 'Sunday',
	Monday: 'Monday',
	Tuesday: 'Tuesday',
	Wednesday: 'Wednesday',
	Thursday: 'Thursday',
	Friday: 'Friday',
	Saturday: 'Saturday',
	StartWeekOn: 'StartWeekOn',
	ShowWeekends: 'ShowWeekends',
	ColourCalendarBy: 'ColourCalendarBy',
	OtherServices: 'OtherServices',
	Services: 'Services',
	CountSelected: 'CountSelected',
	OnlineBookingColorTheme: 'OnlineBookingColorTheme',
	BookingPolicies: 'BookingPolicies',
	BookingPoliciesDescription: 'BookingPoliciesDescription',
	MinimumBookingTime: 'MinimumBookingTime',
	MaximumBookingTime: 'MaximumBookingTime',
	MinMaxBookingTimeUnset: 'MinMaxBookingTimeUnset',
	MinBookingTimeLabel: 'MinBookingTimeLabel',
	MaxBookingTimeLabel: 'MaxBookingTimeLabel',
	MinBookingTimeDescription1: 'MinBookingTimeDescription1',
	MinBookingTimeDescription2: 'MinBookingTimeDescription2',
	MaxBookingTimeDescription1: 'MaxBookingTimeDescription1',
	MaxBookingTimeDescription2: 'MaxBookingTimeDescription2',
	BookingTimeUnitMinutes: 'BookingTimeUnitMinutes',
	BookingTimeUnitHours: 'BookingTimeUnitHours',
	BookingTimeUnitDays: 'BookingTimeUnitDays',
	BookingTimeUnitWeeks: 'BookingTimeUnitWeeks',
	BookingTimeUnitMonths: 'BookingTimeUnitMonths',
	DeletePracticeWorkspace: 'DeletePracticeWorkspace',
	DeletePracticeWorkspaceDescription: 'DeletePracticeWorkspaceDescription',
	DeleteWorkspace: 'DeleteWorkspace',
	DeletePracticeWorkspaceModalTitle: 'DeletePracticeWorkspaceModalTitle',
	DeletePracticeWorkspaceModalDescription: 'DeletePracticeWorkspaceModalDescription',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'DeletePracticeWorkspaceModalCancelSubscriptionDescription',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'DeletePracticeWorkspaceModalReasonFormGroupLabel',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'DeletePracticeWorkspaceModalSpecificReasonFieldLabel',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder',
	DeletePracticeWorkspaceModalConfirmButton: 'DeletePracticeWorkspaceModalConfirmButton',
	DeletePracticeWorkspaceModalCancelButton: 'DeletePracticeWorkspaceModalCancelButton',
	DeletePracticeWorkspaceSuccessSnackbarTitle: 'DeletePracticeWorkspaceSuccessSnackbarTitle',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'DeletePracticeWorkspaceSuccessSnackbarDescription',
	DeletePracticeWorkspaceFailedSnackbar: 'DeletePracticeWorkspaceFailedSnackbar',
	SwitchingToADifferentPlatform: 'SwitchingToADifferentPlatform',
	DidNotProviderEnoughValue: 'DidNotProviderEnoughValue',
	TooExpensive: 'TooExpensive',
	MissingFeatures: 'MissingFeatures',
	TooHardToSetUp: 'TooHardToSetUp',
	ExpiredVerificationLink: 'ExpiredVerificationLink',
	ExpiredVerificationLinkDescription: 'ExpiredVerificationLinkDescription',
	RequestANewVerificationLink: 'RequestANewVerificationLink',
	PoweredBy: 'PoweredBy',
	EmailVerificationNotification: 'EmailVerificationNotification',
	NothingToSeeHere: 'NothingToSeeHere',
	SignupTitleOne: 'SignupTitleOne',
	SignupTitleTwo: 'SignupTitleTwo',
	SignupTitleThree: 'SignupTitleThree',
	SignupTitleLast: 'SignupTitleLast',
	SignupSubtitle: 'SignupSubtitle',
	SignupTitleClient: 'SignupTitleClient',
	SignUpTitleReferralUpgrade: 'SignUpTitleReferralUpgrade',
	SignUpTitleReferralDefault: 'SignUpTitleReferralDefault',
	CreateFreeAccount: 'CreateFreeAccount',
	AreYouAClient: 'AreYouAClient',
	Login: 'Login',
	VerifyEmailAddress: 'VerifyEmailAddress',
	EmailVerificationDescription: 'EmailVerificationDescription',
	EmailVerificationSuccess: 'EmailVerificationSuccess',
	ResendNewEmailVerificationSuccess: 'ResendNewEmailVerificationSuccess',
	OnboardingTitle: 'OnboardingTitle',
	DisplayedToClients: 'DisplayedToClients',
	YourDisplayName: 'YourDisplayName',
	Profession: 'Profession',
	Professions: 'Professions',
	TeamSize: 'TeamSize',
	Solo: 'Solo',
	TwoToTen: 'TwoToTen',
	MoreThanTen: 'MoreThanTen',
	ExploreFeature: 'ExploreFeature',
	ScheduleAppointmentsAndOnlineServices: 'ScheduleAppointmentsAndOnlineServices',
	CustomiseClientRecordsAndWorkspace: 'CustomiseClientRecordsAndWorkspace',
	CreateAndSignNotes: 'CreateAndSignNotes',
	FastTrackInvoicingAndBilling: 'FastTrackInvoicingAndBilling',
	OptimiseTelehealthCalls: 'OptimiseTelehealthCalls',
	OtherProducts: 'OtherProducts',
	GetStarted: 'GetStarted',
	ExploreOptions: 'ExploreOptions',
	SuperbillAndInsuranceBilling: 'SuperbillAndInsuranceBilling',
	ClientManagementAndEHR: 'ClientManagementAndEHR',
	OnlineScheduling: 'OnlineScheduling',
	TelehealthAndVideoCalls: 'TelehealthAndVideoCalls',
	SendIntakeAndForms: 'SendIntakeAndForms',
	AutomateWorkflows: 'AutomateWorkflows',
	InvoicingAndPayment: 'InvoicingAndPayment',
	SMSAndEmailReminder: 'SMSAndEmailReminder',
	NotesAndForms: 'NotesAndForms',
	Notification: 'Notification',
	ClientPortal: 'ClientPortal',
	Integration: 'Integration',
	ClientInAppMessaging: 'ClientInAppMessaging',
	PracticeLocation: 'PracticeLocation',
	ProfessionExample: 'ProfessionExample',
	ProfessionPlaceholder: 'ProfessionPlaceholder',
	ToolsExample: 'ToolsExample',
	WelcomeName: 'WelcomeName',
	WelcomeBackName: 'WelcomeBackName',
	WelcomeBack: 'WelcomeBack',
	LastLoggedIn: 'LastLoggedIn',
	OnboardingLoadingProfession: 'OnboardingLoadingProfession',
	OnboardingLoadingPsychologist: 'OnboardingLoadingPsychologist',
	OnboardingLoadingOccupationalTherapist: 'OnboardingLoadingOccupationalTherapist',
	OnboardingLoadingSubtitleTwo: 'OnboardingLoadingSubtitleTwo',
	OnboardingLoadingSubtitleThree: 'OnboardingLoadingSubtitleThree',
	OnboardingLoadingSubtitleFour: 'OnboardingLoadingSubtitleFour',
	OnboardingLoadingSubtitleFive: 'OnboardingLoadingSubtitleFive',
	OnboardingReviewOne: 'OnboardingReviewOne',
	OnboardingReviewTwo: 'OnboardingReviewTwo',
	OnboardingReviewThree: 'OnboardingReviewThree',
	OnboardingReviewNameOne: 'OnboardingReviewNameOne',
	OnboardingReviewNameTwo: 'OnboardingReviewNameTwo',
	OnboardingReviewNameThree: 'OnboardingReviewNameThree',
	OnboardingReviewLocationOne: 'OnboardingReviewLocationOne',
	OnboardingReviewLocationTwo: 'OnboardingReviewLocationTwo',
	OnboardingReviewLocationThree: 'OnboardingReviewLocationThree',
	DisabledEmailInfo: 'DisabledEmailInfo',
	PageNotFound: 'PageNotFound',
	PageNotFoundDescription: 'PageNotFoundDescription',
	PageUnauthorised: 'PageUnauthorised',
	PageUnauthorisedDescription: 'PageUnauthorisedDescription',
	FileNotFound: 'FileNotFound',
	FileNotFoundDescription: 'FileNotFoundDescription',

	BackHome: 'BackHome',
	InvalidToken: 'InvalidToken',
	EmailAlreadyExists: 'EmailAlreadyExists',
	CustomServiceAvailability: 'CustomServiceAvailability',
	GeneralServiceAvailabilityInfo: 'GeneralServiceAvailabilityInfo',
	NoCustomServiceSchedule: 'NoCustomServiceSchedule',
	AttachFiles: 'AttachFiles',
	CreateDocumentDnDPrompt: 'CreateDocumentDnDPrompt',
	CreateDocumentSizeLimit: 'CreateDocumentSizeLimit',
	UploadIndividually: 'UploadIndividually',
	EnableCustomServiceAvailability: 'EnableCustomServiceAvailability',
	EnableCustomServiceAvailabilityDescription: 'EnableCustomServiceAvailabilityDescription',
	ToYourWorkspace: 'ToYourWorkspace',
	DateReceived: 'DateReceived',
	OnlinePayment: 'OnlinePayment',
	PreviewUnavailable: 'PreviewUnavailable',
	DateUploaded: 'DateUploaded',
	SendEmail: 'SendEmail',
	Upgrade: 'Upgrade',
	UpgradeForSMSReminder: 'UpgradeForSMSReminder',
	ProviderUsedStoragePercentage: 'ProviderUsedStoragePercentage',
	UpgradeSubscriptionAlertTitle: 'UpgradeSubscriptionAlertTitle',
	UpgradeSubscriptionAlertDescription: 'UpgradeSubscriptionAlertDescription',
	UpgradeSubscriptionAlertDescriptionNoPermission: 'UpgradeSubscriptionAlertDescriptionNoPermission',
	SubscriptionPlanDetailsHeader: 'SubscriptionPlanDetailsHeader',
	SubscriptionPlanDetailsSubheader: 'SubscriptionPlanDetailsSubheader',
	ProfessionalPlanInclusionHeader: 'ProfessionalPlanInclusionHeader',
	OrganizationPlanInclusionHeader: 'OrganizationPlanInclusionHeader',
	ProfessionalPlanInclusion1: 'ProfessionalPlanInclusion1',
	ProfessionalPlanInclusion2: 'ProfessionalPlanInclusion2',
	ProfessionalPlanInclusion3: 'ProfessionalPlanInclusion3',
	ProfessionalPlanInclusion4: 'ProfessionalPlanInclusion4',
	ProfessionalPlanInclusion5: 'ProfessionalPlanInclusion5',
	OrganizationPlanInclusion1: 'OrganizationPlanInclusion1',
	OrganizationPlanInclusion2: 'OrganizationPlanInclusion2',
	OrganizationPlanInclusion3: 'OrganizationPlanInclusion3',
	ChooseProfessional: 'ChooseProfessional',
	ChooseOrganization: 'ChooseOrganization',
	ShowMenu: 'ShowMenu',
	HideMenu: 'HideMenu',
	NoPermissionError: 'NoPermissionError',
	Midwives: 'Midwives',
	Physicians: 'Physicians',
	Dieticians: 'Dieticians',
	Psychiatrists: 'Psychiatrists',
	Estheticians: 'Estheticians',
	SpeechTherapists: 'SpeechTherapists',
	Psychotherapists: 'Psychotherapists',
	Therapists: 'Therapists',
	Acupuncturists: 'Acupuncturists',
	Nutritionists: 'Nutritionists',
	Dentists: 'Dentists',
	Psychologists: 'Psychologists',
	Counselors: 'Counselors',
	PhysicalTherapists: 'PhysicalTherapists',
	OccupationalTherapists: 'OccupationalTherapists',
	Nurses: 'Nurses',
	MassageTherapists: 'MassageTherapists',
	MentalHealthPractitioners: 'MentalHealthPractitioners',
	LifeCoaches: 'LifeCoaches',
	HealthCoaches: 'HealthCoaches',
	GeneralPractitioners: 'GeneralPractitioners',
	Chiropractors: 'Chiropractors',
	PaymentMethodDescription: 'PaymentMethodDescription',
	CreditCardNumber: 'CreditCardNumber',
	NewPaymentMethod: 'NewPaymentMethod',
	OtherPaymentMethod: 'OtherPaymentMethod',
	ManualPayment: 'ManualPayment',
	SavedCards: 'SavedCards',
	SavedPaymentMethods: 'SavedPaymentMethods',
	Manage: 'Manage',
	EditPaymentMethod: 'EditPaymentMethod',
	UseThisCard: 'UseThisCard',
	EstimatedArrivalDate: 'EstimatedArrivalDate',
	InTransit: 'InTransit',
	InTransitTooltip: 'InTransitTooltip',
	PaidOut: 'PaidOut',
	AddRowAbove: 'AddRowAbove',
	AddRowBelow: 'AddRowBelow',
	AddColBefore: 'AddColBefore',
	AddColAfter: 'AddColAfter',
	RemoveRow: 'RemoveRow',
	RemoveCol: 'RemoveCol',
	ToggleHeaderRow: 'ToggleHeaderRow',
	ToggleHeaderCol: 'ToggleHeaderCol',
	ToggleHeaderCell: 'ToggleHeaderCell',
	RemoveTable: 'RemoveTable',
	TypeHere: 'TypeHere',
	SearchInvoiceNumber: 'SearchInvoiceNumber',
	SelectProfile: 'SelectProfile',
	SearchByName: 'SearchByName',
	SelectTeamOrCommunity: 'SelectTeamOrCommunity',
	SelectCollection: 'SelectCollection',
	AllTags: 'AllTags',
	Loading: 'Loading',
	EmailBody: 'EmailBody',
	SearchOrCreateATag: 'SearchOrCreateATag',
	SelectType: 'SelectType',
	ClientInfoPlaceholder: 'ClientInfoPlaceholder',
	ProviderInfoPlaceholder: 'ProviderInfoPlaceholder',
	PracticeInfoPlaceholder: 'PracticeInfoPlaceholder',
	EmailInvoicePaidBody: 'EmailInvoicePaidBody',
	EmailInvoiceVoidBody: 'EmailInvoiceVoidBody',
	EmailInvoiceUnpaidBody: 'EmailInvoiceUnpaidBody',
	EmailInvoiceOverdueBody: 'EmailInvoiceOverdueBody',
	EmailInvoiceProcessingBody: 'EmailInvoiceProcessingBody',
	InvoiceFrom: 'InvoiceFrom',
	EmailSuperbillReceiptSubject: 'EmailSuperbillReceiptSubject',
	EmailSuperbillReceiptBody: 'EmailSuperbillReceiptBody',
	EmailInviteToPortalBody: 'EmailInviteToPortalBody',
	EmailInviteToPortalSubject: 'EmailInviteToPortalSubject',
	EmailSendClientIntakeBody: 'EmailSendClientIntakeBody',
	EmailSendClientIntakeSubject: 'EmailSendClientIntakeSubject',
	SubscriptionPlans: 'SubscriptionPlans',
	AcceptAppointment: 'AcceptAppointment',
	EnterFieldLabel: 'EnterFieldLabel',
	EnterSectionName: 'EnterSectionName',
	AddALocation: 'AddALocation',
	PayWithOtherCard: 'PayWithOtherCard',
	AcceptTermsAndConditionsRequired: 'AcceptTermsAndConditionsRequired',
	NoTitle: 'NoTitle',
	CardNumberRequired: 'CardNumberRequired',
	ExpiryDateRequired: 'ExpiryDateRequired',
	CVCRequired: 'CVCRequired',
	MostPopular: 'MostPopular',
	MaximumBookingTimeError: 'MaximumBookingTimeError',
	ErrorBoundaryTitle: 'ErrorBoundaryTitle',
	ErrorBoundaryDescription: 'ErrorBoundaryDescription',
	ErrorBoundaryAction: 'ErrorBoundaryAction',
	Use: 'Use',
	Acupuncture: 'Acupuncture',
	BehavioralHealthTherapy: 'BehavioralHealthTherapy',
	Chiropractic: 'Chiropractic',
	Coaching: 'Coaching',
	Counseling: 'Counseling',
	DieteticsOrNutrition: 'DieteticsOrNutrition',
	FunctionalMedicineOrNaturopath: 'FunctionalMedicineOrNaturopath',
	HealthCoach: 'HealthCoach',
	LactationConsulting: 'LactationConsulting',
	MassageTherapy: 'MassageTherapy',
	Medicine: 'Medicine',
	Nursing: 'Nursing',
	OccupationalTherapy: 'OccupationalTherapy',
	PersonalTraining: 'PersonalTraining',
	PhysicalTherapy: 'PhysicalTherapy',
	Psychiatry: 'Psychiatry',
	Psychology: 'Psychology',
	SocialWork: 'SocialWork',
	SpeechLanguagePathology: 'SpeechLanguagePathology',
	Accountant: 'Accountant',
	Actor: 'Actor',
	Acupuncturist: 'Acupuncturist',
	AddictionCounselor: 'AddictionCounselor',
	AdvertisingManager: 'AdvertisingManager',
	AerospaceEngineer: 'AerospaceEngineer',
	ArtTherapist: 'ArtTherapist',
	Artist: 'Artist',
	AthleticTrainer: 'AthleticTrainer',
	Audiologist: 'Audiologist',
	Banker: 'Banker',
	BehavioralAnalyst: 'BehavioralAnalyst',
	Biller: 'Biller',
	BiomedicalEngineer: 'BiomedicalEngineer',
	CardiacRehabilitationSpecialist: 'CardiacRehabilitationSpecialist',
	Cardiologist: 'Cardiologist',
	Chef: 'Chef',
	Chiropractor: 'Chiropractor',
	CivilEngineer: 'CivilEngineer',
	ClinicalPsychologist: 'ClinicalPsychologist',
	CommunityHealthWorker: 'CommunityHealthWorker',
	ComputerSystemsAnalyst: 'ComputerSystemsAnalyst',
	ConstructionWorker: 'ConstructionWorker',
	Consultant: 'Consultant',
	Cosmetologist: 'Cosmetologist',
	Counselor: 'Counselor',
	CustomerServiceRepresentative: 'CustomerServiceRepresentative',
	DanceTherapist: 'DanceTherapist',
	DentalAssistant: 'DentalAssistant',
	DentalHygienist: 'DentalHygienist',
	Dentist: 'Dentist',
	Dietitian: 'Dietitian',
	DramaTherapist: 'DramaTherapist',
	Economist: 'Economist',
	ElectricalEngineer: 'ElectricalEngineer',
	ElementarySchoolTeacher: 'ElementarySchoolTeacher',
	EnvironmentalScientist: 'EnvironmentalScientist',
	Epidemiologist: 'Epidemiologist',
	ExercisePhysiologist: 'ExercisePhysiologist',
	FashionDesigner: 'FashionDesigner',
	FinancialAnalyst: 'FinancialAnalyst',
	Firefighter: 'Firefighter',
	GeneticCounselor: 'GeneticCounselor',
	Gerontologist: 'Gerontologist',
	GraphicDesigner: 'GraphicDesigner',
	HairStylist: 'HairStylist',
	HealthEducator: 'HealthEducator',
	HealthInformationTechnician: 'HealthInformationTechnician',
	HealthPolicyExpert: 'HealthPolicyExpert',
	HealthServicesAdministrator: 'HealthServicesAdministrator',
	HolisticHealthPractitioner: 'HolisticHealthPractitioner',
	HomeHealthAide: 'HomeHealthAide',
	HumanResourcesManager: 'HumanResourcesManager',
	Hypnotherapist: 'Hypnotherapist',
	InteriorDesigner: 'InteriorDesigner',
	Journalist: 'Journalist',
	Lawyer: 'Lawyer',
	Librarian: 'Librarian',
	LifeCoach: 'LifeCoach',
	MarketingManager: 'MarketingManager',
	MassageTherapist: 'MassageTherapist',
	MechanicalEngineer: 'MechanicalEngineer',
	MedicalAssistant: 'MedicalAssistant',
	MedicalCoder: 'MedicalCoder',
	MedicalDoctor: 'MedicalDoctor',
	MedicalIllustrator: 'MedicalIllustrator',
	MedicalInterpreter: 'MedicalInterpreter',
	MedicalTechnologist: 'MedicalTechnologist',
	MultiSpeciality: 'MultiSpeciality',
	MusicTherapist: 'MusicTherapist',
	NaturopathicDoctor: 'NaturopathicDoctor',
	Neurologist: 'Neurologist',
	NuclearMedicineTechnologist: 'NuclearMedicineTechnologist',
	NurseAnesthetist: 'NurseAnesthetist',
	NurseEducator: 'NurseEducator',
	NurseMidwife: 'NurseMidwife',
	NursePractitioner: 'NursePractitioner',
	Nutritionist: 'Nutritionist',
	ObstetricianOrGynecologist: 'ObstetricianOrGynecologist',
	OccupationalTherapist: 'OccupationalTherapist',
	Oncologist: 'Oncologist',
	Ophthalmologist: 'Ophthalmologist',
	Optometrist: 'Optometrist',
	Orthodontist: 'Orthodontist',
	Orthotist: 'Orthotist',
	Pathologist: 'Pathologist',
	Pediatrician: 'Pediatrician',
	PersonalTrainer: 'PersonalTrainer',
	Pharmacist: 'Pharmacist',
	PhysicalTherapist: 'PhysicalTherapist',
	PhysicianAssistant: 'PhysicianAssistant',
	PoliceOfficer: 'PoliceOfficer',
	PrivatePracticeConsultant: 'PrivatePracticeConsultant',
	Prosthetist: 'Prosthetist',
	Psychiatrist: 'Psychiatrist',
	Psychoanalyst: 'Psychoanalyst',
	Psychologist: 'Psychologist',
	Psychometrician: 'Psychometrician',
	PsychosocialRehabilitationSpecialist: 'PsychosocialRehabilitationSpecialist',
	PublicHealthInspector: 'PublicHealthInspector',
	RadiationTherapist: 'RadiationTherapist',
	Radiologist: 'Radiologist',
	RealEstateAgent: 'RealEstateAgent',
	RegisteredNurse: 'RegisteredNurse',
	RehabilitationCounselor: 'RehabilitationCounselor',
	RespiratoryTherapist: 'RespiratoryTherapist',
	SalesRepresentative: 'SalesRepresentative',
	SocialWorker: 'SocialWorker',
	SoftwareDeveloper: 'SoftwareDeveloper',
	SpeechTherapist: 'SpeechTherapist',
	SportsMedicinePhysician: 'SportsMedicinePhysician',
	Surgeon: 'Surgeon',
	SurgicalTechnologist: 'SurgicalTechnologist',
	TeacherAssistant: 'TeacherAssistant',
	Therapist: 'Therapist',
	Veterinarian: 'Veterinarian',
	WebDeveloper: 'WebDeveloper',
	Writer: 'Writer',
	YogaInstructor: 'YogaInstructor',
	MyofunctionalTherapist: 'MyofunctionalTherapist',
	PreventionSpecialist: 'PreventionSpecialist',
	DogWalker: 'DogWalker',
	PsychiatricNursePractitioner: 'PsychiatricNursePractitioner',
	Esthetician: 'Esthetician',
	SpeechTherapy: 'SpeechTherapy',
	Psychotheraphy: 'Psychotheraphy',
	Therapy: 'Therapy',
	Dental: 'Dental',
	MentalHealth: 'MentalHealth',
	Doctor: 'Doctor',
	Psychotherapy: 'Psychotherapy',
	RemoveGuides: 'RemoveGuides',
	DateOfPayment: 'DateOfPayment',
	FeatureBannerClientsTitle: 'FeatureBannerClientsTitle',
	FeatureBannerClientsTile1Title: 'FeatureBannerClientsTile1Title',
	FeatureBannerClientsTile1Description: 'FeatureBannerClientsTile1Description',
	FeatureBannerClientsTile1ActionLabel: 'FeatureBannerClientsTile1ActionLabel',
	FeatureBannerClientsTile2Title: 'FeatureBannerClientsTile2Title',
	FeatureBannerClientsTile2Description: 'FeatureBannerClientsTile2Description',
	FeatureBannerClientsTile2ActionLabel: 'FeatureBannerClientsTile2ActionLabel',
	FeatureBannerClientsTile3Title: 'FeatureBannerClientsTile3Title',
	FeatureBannerInvoicesTitle: 'FeatureBannerInvoicesTitle',
	FeatureBannerInvoicesTile1Title: 'FeatureBannerInvoicesTile1Title',
	FeatureBannerInvoicesTile1Description: 'FeatureBannerInvoicesTile1Description',
	FeatureBannerInvoicesTile1ActionLabel: 'FeatureBannerInvoicesTile1ActionLabel',
	FeatureBannerInvoicesTile2Title: 'FeatureBannerInvoicesTile2Title',
	FeatureBannerInvoicesTile2Description: 'FeatureBannerInvoicesTile2Description',
	FeatureBannerInvoicesTile2ActionLabel: 'FeatureBannerInvoicesTile2ActionLabel',
	FeatureBannerInvoicesTile3Title: 'FeatureBannerInvoicesTile3Title',
	FeatureBannerTemplatesTitle: 'FeatureBannerTemplatesTitle',
	FeatureBannerTemplatesTile1Title: 'FeatureBannerTemplatesTile1Title',
	FeatureBannerTemplatesTile1Description: 'FeatureBannerTemplatesTile1Description',
	FeatureBannerTemplatesTile1ActionLabel: 'FeatureBannerTemplatesTile1ActionLabel',
	FeatureBannerTemplatesTile2Title: 'FeatureBannerTemplatesTile2Title',
	FeatureBannerTemplatesTile2Description: 'FeatureBannerTemplatesTile2Description',
	FeatureBannerTemplatesTile2ActionLabel: 'FeatureBannerTemplatesTile2ActionLabel',
	FeatureBannerTemplatesTile3Title: 'FeatureBannerTemplatesTile3Title',
	FeatureBannerTeamTitle: 'FeatureBannerTeamTitle',
	FeatureBannerTeamTile1Title: 'FeatureBannerTeamTile1Title',
	FeatureBannerTeamTile1Description: 'FeatureBannerTeamTile1Description',
	FeatureBannerTeamTile1ActionLabel: 'FeatureBannerTeamTile1ActionLabel',
	FeatureBannerTeamTile2Title: 'FeatureBannerTeamTile2Title',
	FeatureBannerTeamTile2Description: 'FeatureBannerTeamTile2Description',
	FeatureBannerTeamTile2ActionLabel: 'FeatureBannerTeamTile2ActionLabel',
	FeatureBannerTeamTile3Title: 'FeatureBannerTeamTile3Title',
	FeatureBannerTeamTile3Description: 'FeatureBannerTeamTile3Description',
	FeatureBannerTeamTile3ActionLabel: 'FeatureBannerTeamTile3ActionLabel',
	FeatureBannerCallsTitle: 'FeatureBannerCallsTitle',
	FeatureBannerCallsTile1Title: 'FeatureBannerCallsTile1Title',
	FeatureBannerCallsTile1Description: 'FeatureBannerCallsTile1Description',
	FeatureBannerCallsTile1ActionLabel: 'FeatureBannerCallsTile1ActionLabel',
	FeatureBannerCallsTile2Title: 'FeatureBannerCallsTile2Title',
	FeatureBannerCallsTile2Description: 'FeatureBannerCallsTile2Description',
	FeatureBannerCallsTile2ActionLabel: 'FeatureBannerCallsTile2ActionLabel',
	FeatureBannerCallsTile3Title: 'FeatureBannerCallsTile3Title',
	FeatureBannerCalendarTitle: 'FeatureBannerCalendarTitle',
	FeatureBannerCalendarTile1Title: 'FeatureBannerCalendarTile1Title',
	FeatureBannerCalendarTile1Description: 'FeatureBannerCalendarTile1Description',
	FeatureBannerCalendarTile1ActionLabel: 'FeatureBannerCalendarTile1ActionLabel',
	FeatureBannerCalendarTile2Title: 'FeatureBannerCalendarTile2Title',
	FeatureBannerCalendarTile2Description: 'FeatureBannerCalendarTile2Description',
	FeatureBannerCalendarTile2ActionLabel: 'FeatureBannerCalendarTile2ActionLabel',
	FeatureBannerCalendarTile3Title: 'FeatureBannerCalendarTile3Title',
	RemoveAllGuidesPopoverTitle: 'RemoveAllGuidesPopoverTitle',
	RemoveAllGuidesPopoverBody: 'RemoveAllGuidesPopoverBody',
	RemoveAllGuidesBtn: 'RemoveAllGuidesBtn',
	GotIt: 'GotIt',

	Undo: 'Undo',
	Redo: 'Redo',
	Bold: 'Bold',
	Italic: 'Italic',
	Underline: 'Underline',
	Single: 'Single',
	Double: 'Double',
	SwitchWorkspace: 'SwitchWorkspace',
	ScrollToTop: 'ScrollToTop',
	ShareNoteDefaultMessage: 'ShareNoteDefaultMessage',
	ShareNoteMessage: 'ShareNoteMessage',

	LocationType: 'LocationType',
	LocationRemote: 'LocationRemote',
	AddPhysicalOrVirtualLocations: 'AddPhysicalOrVirtualLocations',
	InPersonMeeting: 'InPersonMeeting',
	SetAPhysicalAddress: 'SetAPhysicalAddress',
	PhoneCall: 'PhoneCall',
	InboundOrOutboundCalls: 'InboundOrOutboundCalls',
	TelehealthVideoCall: 'TelehealthVideoCall',
	SetAVirtualLocation: 'SetAVirtualLocation',
	GoogleMeet: 'GoogleMeet',
	WebConference: 'WebConference',
	WebConferenceOrVirtualLocation: 'WebConferenceOrVirtualLocation',
	DoxyMe: 'DoxyMe',
	MicrosoftTeams: 'MicrosoftTeams',
	PhysicalAddress: 'PhysicalAddress',
	SuburbOrProvince: 'SuburbOrProvince',
	City: 'City',
	State: 'State',
	ZipCode: 'ZipCode',
	Link: 'Link',
	EditLocation: 'EditLocation',
	Call: 'Call',
	JoinWithVideoCall: 'JoinWithVideoCall',
	CopyMeetingLink: 'CopyMeetingLink',
	JoinVideoCall: 'JoinVideoCall',
	JoinProduct: 'JoinProduct',
	CaptureNameFieldLabel: 'CaptureNameFieldLabel',
	MeetingHost: 'MeetingHost',
	ListPeopleInThisMeeting: 'ListPeopleInThisMeeting',
	OthersPeople: 'OthersPeople',
	SendReaction: 'SendReaction',
	MoreSettings: 'MoreSettings',
	MeetingPersonRaisedHand: 'MeetingPersonRaisedHand',
	MeetingRaiseHand: 'MeetingRaiseHand',
	MeetingLowerHand: 'MeetingLowerHand',
	ShowMeetingTimers: 'ShowMeetingTimers',

	TeamTemplates: 'TeamTemplates',
	PublicTemplates: 'PublicTemplates',
	SuggestedAITemplates: 'SuggestedAITemplates',

	OtherTemplates: 'OtherTemplates',
	CarepatronCommunity: 'CarepatronCommunity',
	SearchInputPlaceholder: 'SearchInputPlaceholder',
	SearchClients: 'SearchClients',
	SearchInvoices: 'SearchInvoices',
	Category: 'Category',
	SortBy: 'SortBy',
	DateRange: 'DateRange',
	AllCategories: 'AllCategories',
	AllIndustries: 'AllIndustries',
	AllProfessions: 'AllProfessions',
	SelectAll: 'SelectAll',
	UnselectAll: 'UnselectAll',

	Assessments: 'Assessments',
	Emails: 'Emails',
	Forms: 'Forms',
	Intake: 'Intake',
	Notes: 'Notes',
	MostDownloaded: 'MostDownloaded',
	Newest: 'Newest',
	Oldest: 'Oldest',
	NewestUnreplied: 'NewestUnreplied',
	OldestUnreplied: 'OldestUnreplied',
	FeatureBannerHeader: 'FeatureBannerHeader',
	FeatureBannerSubheader: 'FeatureBannerSubheader',
	PublishTemplateFeatureBannerSubheader: 'PublishTemplateFeatureBannerSubheader',
	PublishTemplateHeader: 'PublishTemplateHeader',
	PublishedBy: 'PublishedBy',
	CategoryInputPlaceholder: 'CategoryInputPlaceholder',
	TagsInputPlaceholder: 'TagsInputPlaceholder',

	NumberOfResources: 'NumberOfResources',
	NumberOfClients: 'NumberOfClients',
	NumberOfContacts: 'NumberOfContacts',
	NumberOfTrashItems: 'NumberOfTrashItems',
	Downloads: 'Downloads',

	YesDelete: 'YesDelete',
	YesRemove: 'YesRemove',
	YesRestore: 'YesRestore',
	InvalidURLErrorText: 'InvalidURLErrorText',
	PublishToCommunity: 'PublishToCommunity',
	PublishToCommunitySuccessMessage: 'PublishToCommunitySuccessMessage',
	PublishedOnCarepatronCommunity: 'PublishedOnCarepatronCommunity',
	UnpublishTemplateConfirmationModalPrompt: 'UnpublishTemplateConfirmationModalPrompt',
	UnpublishToCommunitySuccessMessage: 'UnpublishToCommunitySuccessMessage',
	Published: 'Published',

	Day: 'Day',
	Week: 'Week',
	Month: 'Month',
	ThreeDay: 'ThreeDay',

	POS: 'POS',
	POSCode: 'POSCode',
	AddPOS: 'AddPOS',
	PlaceOfService: 'PlaceOfService',

	Show: 'Show',
	AllStatuses: 'AllStatuses',
	ShowColumns: 'ShowColumns',
	Assigned: 'Assigned',
	Unassigned: 'Unassigned',

	RemovePaymentMethodDescription: 'RemovePaymentMethodDescription',
	AreYouSure: 'AreYouSure',

	SuccessfullySubmitted: 'SuccessfullySubmitted',
	PersonalHealthcareHistoryStoreDescription: 'PersonalHealthcareHistoryStoreDescription',
	FormAnswersSentToEmailNotification: 'FormAnswersSentToEmailNotification',
	ReturnToForm: 'ReturnToForm',
	SignInWithMicrosoft: 'SignInWithMicrosoft',
	TemplateNotFound: 'TemplateNotFound',

	// Form field elements
	DateFormPrimaryText: 'DateFormPrimaryText',
	DateFormSecondaryText: 'DateFormSecondaryText',
	ClientInfoFormPrimaryText: 'ClientInfoFormPrimaryText',
	ClientInfoFormSecondaryText: 'ClientInfoFormSecondaryText',
	PaymentInfoFormPrimaryText: 'PaymentInfoFormPrimaryText',
	PaymentInfoFormSecondaryText: 'PaymentInfoFormSecondaryText',
	GroupInputFieldsFormPrimaryText: 'GroupInputFieldsFormPrimaryText',
	GroupInputFieldsFormSecondaryText: 'GroupInputFieldsFormSecondaryText',
	ShortTextFormPrimaryText: 'ShortTextFormPrimaryText',
	ShortTextFormSecondaryText: 'ShortTextFormSecondaryText',
	LongTextFormPrimaryText: 'LongTextFormPrimaryText',
	LongTextFormSecondaryText: 'LongTextFormSecondaryText',
	DropdownFormPrimaryText: 'DropdownFormPrimaryText',
	DropdownFormSecondaryText: 'DropdownFormSecondaryText',
	MultipleChoiceFormPrimaryText: 'MultipleChoiceFormPrimaryText',
	MultipleChoiceFormSecondaryText: 'MultipleChoiceFormSecondaryText',
	MultipleChoiceGridFormPrimaryText: 'MultipleChoiceGridFormPrimaryText',
	MultipleChoiceGridFormSecondaryText: 'MultipleChoiceGridFormSecondaryText',
	YesOrNoFormPrimaryText: 'YesOrNoFormPrimaryText',
	YesOrNoFormSecondaryText: 'YesOrNoFormSecondaryText',
	SingleChoiceFormPrimaryText: 'SingleChoiceFormPrimaryText',
	SingleChoiceFormSecondaryText: 'SingleChoiceFormSecondaryText',
	LinearScaleFormPrimaryText: 'LinearScaleFormPrimaryText',
	LinearScaleFormSecondaryText: 'LinearScaleFormSecondaryText',
	SignatureFormPrimaryText: 'SignatureFormPrimaryText',
	SignatureFormSecondaryText: 'SignatureFormSecondaryText',
	DateRangeFormPrimaryText: 'DateRangeFormPrimaryText',
	DateRangeFormSecondaryText: 'DateRangeFormSecondaryText',
	LegalConsentFormPrimaryText: 'LegalConsentFormPrimaryText',
	LegalConsentFormSecondaryText: 'LegalConsentFormSecondaryText',
	FormFieldAddOption: 'FormFieldAddOption',
	FormFieldAddOtherOption: 'FormFieldAddOtherOption',
	FormFieldOptionPlaceholder: 'FormFieldOptionPlaceholder',
	SignedBy: 'SignedBy',
	AddQuestionOrTitle: 'AddQuestionOrTitle',
	AnswerExceeded: 'AnswerExceeded',
	LongTextAnswer: 'LongTextAnswer',
	QuestionOrTitle: 'QuestionOrTitle',
	ShortTextAnswer: 'ShortTextAnswer',
	YourAnswer: 'YourAnswer',
	Required: 'Required',
	Question: 'Question',
	AddADescription: 'AddADescription',
	EditorAlertTitle: 'EditorAlertTitle',
	EditorAlertDescription: 'EditorAlertDescription',
	ClearSearchFilter: 'ClearSearchFilter',
	SearchTags: 'SearchTags',
	Highlight: 'Highlight',
	ColorTheme: 'ColorTheme',
	ThemeColor: 'ThemeColor',
	AllTeamMembers: 'AllTeamMembers',
	AllTypes: 'AllTypes',
	CustomiseClientSettings: 'CustomiseClientSettings',
	NewField: 'NewField',
	FieldName: 'FieldName',
	SearchClientFields: 'SearchClientFields',
	ShowHideFields: 'ShowHideFields',
	ShowField: 'ShowField',
	HideField: 'HideField',
	ShowSection: 'ShowSection',
	HideSection: 'HideSection',
	ClientFieldTextLabel: 'ClientFieldTextLabel',
	ClientFieldTextDescription: 'ClientFieldTextDescription',
	ClientFieldLongTextLabel: 'ClientFieldLongTextLabel',
	ClientFieldLongTextDescription: 'ClientFieldLongTextDescription',
	ClientFieldEmailLabel: 'ClientFieldEmailLabel',
	ClientFieldEmailDescription: 'ClientFieldEmailDescription',
	ClientFieldPhoneNumberLabel: 'ClientFieldPhoneNumberLabel',
	ClientFieldPhoneNumberDescription: 'ClientFieldPhoneNumberDescription',
	ClientFieldDateLabel: 'ClientFieldDateLabel',
	ClientFieldDateDescription: 'ClientFieldDateDescription',
	ClientFieldDateShowDateDescription: 'ClientFieldDateShowDateDescription',
	ClientFieldDateRangeLabel: 'ClientFieldDateRangeLabel',
	ClientFieldDateRangeDescription: 'ClientFieldDateRangeDescription',
	ClientFieldDateShowDateRangeDescription: 'ClientFieldDateShowDateRangeDescription',
	ClientFieldSingleChoiceDropdownLabel: 'ClientFieldSingleChoiceDropdownLabel',
	ClientFieldSingleChoiceDropdownDescription: 'ClientFieldSingleChoiceDropdownDescription',
	ClientFieldMultipleChoiceDropdownLabel: 'ClientFieldMultipleChoiceDropdownLabel',
	ClientFieldMultipleChoiceDropdownDescription: 'ClientFieldMultipleChoiceDropdownDescription',
	ClientFieldYesOrNoLabel: 'ClientFieldYesOrNoLabel',
	ClientFieldYesOrNoDescription: 'ClientFieldYesOrNoDescription',
	ClientFieldLinearScaleLabel: 'ClientFieldLinearScaleLabel',
	ClientFieldLinearScaleDescription: 'ClientFieldLinearScaleDescription',
	ClientFieldLocationLabel: 'ClientFieldLocationLabel',
	ClientFieldLocationDescription: 'ClientFieldLocationDescription',
	ShowDateDurationLabel: 'ShowDateDurationLabel',
	ShowDateDurationDescription: 'ShowDateDurationDescription',
	YesOrNoAnswerTypeDescription: 'YesOrNoAnswerTypeDescription',
	Section: 'Section',
	FieldType: 'FieldType',
	SaveField: 'SaveField',
	EnterAName: 'EnterAName',
	ChangesNotAllowed: 'ChangesNotAllowed',
	EditValue: 'EditValue',
	Items: 'Items',
	SuccessfullyCreatedValue: 'SuccessfullyCreatedValue',
	SuccessfullyUpdatedValue: 'SuccessfullyUpdatedValue',
	SuccessfullyDeletedValue: 'SuccessfullyDeletedValue',
	InvalidDisplayName: 'InvalidDisplayName',
	CreateLink: 'CreateLink',
	Insert: 'Insert',
	Replace: 'Replace',
	Rephrase: 'Rephrase',
	UrlLink: 'UrlLink',
	EditLink: 'EditLink',
	ValidUrl: 'ValidUrl',
	RequiredUrl: 'RequiredUrl',
	LabelOptional: 'LabelOptional',
	DragToMoveOrActivate: 'DragToMoveOrActivate',
	SuccessfullyUpdatedClientSettings: 'SuccessfullyUpdatedClientSettings',
	DatePickerFormPrimaryText: 'DatePickerFormPrimaryText',
	DatePickerFormSecondaryText: 'DatePickerFormSecondaryText',
	ContactInformationText: 'ContactInformationText',
	InputPhraseToConfirm: 'InputPhraseToConfirm',

	AllInboxes: 'AllInboxes',
	Inbox: 'Inbox',
	InboxSettings: 'InboxSettings',
	InboxNoConnectionHeading: 'InboxNoConnectionHeading',
	InboxNoConnectionDescription: 'InboxNoConnectionDescription',
	InboxConnectedHeading: 'InboxConnectedHeading',
	InboxConnectedHeadingClientView: 'InboxConnectedHeadingClientView',
	InboxConnectedDescription: 'InboxConnectedDescription',
	InboxEmptyCongratsHeading: 'InboxEmptyCongratsHeading',
	InboxEmptyBinHeading: 'InboxEmptyBinHeading',
	InboxEmptyScheduledHeading: 'InboxEmptyScheduledHeading',
	InboxEmptyBinDescription: 'InboxEmptyBinDescription',
	InboxEmptyArchiveDescription: 'InboxEmptyArchiveDescription',
	InboxEmptySentDescription: 'InboxEmptySentDescription',
	InboxEmptyDraftHeading: 'InboxEmptyDraftHeading',
	InboxEmptyDraftDescription: 'InboxEmptyDraftDescription',
	InboxEmptyOtherDescription: 'InboxEmptyOtherDescription',
	InboxConnectAccountButton: 'InboxConnectAccountButton',
	InboxCreateFirstInboxButton: 'InboxCreateFirstInboxButton',
	InboxComposeMessageFrom: 'InboxComposeMessageFrom',
	InboxComposeMessageRecipientTo: 'InboxComposeMessageRecipientTo',
	InboxComposeMessageRecipientCc: 'InboxComposeMessageRecipientCc',
	InboxComposeMessageRecipientBcc: 'InboxComposeMessageRecipientBcc',
	InboxComposeMessageSubject: 'InboxComposeMessageSubject',
	InboxEmailNotFound: 'InboxEmailNotFound',
	InboxGroupClientsLabel: 'InboxGroupClientsLabel',
	InboxGroupClientsOverviewLabel: 'InboxGroupClientsOverviewLabel',
	InboxGroupClientsSelectedItemPrefix: 'InboxGroupClientsSelectedItemPrefix',
	InboxGroupStaffsLabel: 'InboxGroupStaffsLabel',
	InboxGroupStaffsOverviewLabel: 'InboxGroupStaffsOverviewLabel',
	InboxGroupStaffsSelectedItemPrefix: 'InboxGroupStaffsSelectedItemPrefix',
	InboxGroupTagsLabel: 'InboxGroupTagsLabel',
	InboxGroupTagsOverviewLabel: 'InboxGroupTagsOverviewLabel',
	InboxGroupTagsSelectedItemPrefix: 'InboxGroupTagsSelectedItemPrefix',
	InboxGroupStatusLabel: 'InboxGroupStatusLabel',
	InboxGroupStatusOverviewLabel: 'InboxGroupStatusOverviewLabel',
	InboxGroupStatusSelectedItemPrefix: 'InboxGroupStatusSelectedItemPrefix',
	InboxCompose: 'InboxCompose',
	InboxComposeEmail: 'InboxComposeEmail',
	InboxComposeChat: 'InboxComposeChat',
	InboxBulkComposeModalTitle: 'InboxBulkComposeModalTitle',
	InboxComposeBulk: 'InboxComposeBulk',
	InboxSendMessageSuccess: 'InboxSendMessageSuccess',
	InboxScheduleSendMessageSuccessTitle: 'InboxScheduleSendMessageSuccessTitle',
	InboxScheduleSendMessageSuccessDescription: 'InboxScheduleSendMessageSuccessDescription',
	InboxScheduleSendCancelSendSuccess: 'InboxScheduleSendCancelSendSuccess',
	InboxEmailDraft: 'InboxEmailDraft',
	InboxEmailComposeReplyEmail: 'InboxEmailComposeReplyEmail',
	InboxReply: 'InboxReply',
	InboxReplyAll: 'InboxReplyAll',
	InboxForward: 'InboxForward',
	InboxEmailSubjectFieldInformation: 'InboxEmailSubjectFieldInformation',
	InboxMessageBodyPlaceholder: 'InboxMessageBodyPlaceholder',
	InboxAddAttachments: 'InboxAddAttachments',
	InboxDeleteAttachment: 'InboxDeleteAttachment',
	InboxCreationSuccess: 'InboxCreationSuccess',
	InboxStaffRoleAdminDescription: 'InboxStaffRoleAdminDescription',
	InboxStaffRoleResponderDescription: 'InboxStaffRoleResponderDescription',
	InboxStaffRoleViewerDescription: 'InboxStaffRoleViewerDescription',
	InboxSwitchToOtherInbox: 'InboxSwitchToOtherInbox',
	InboxEditDraft: 'InboxEditDraft',
	InboxAccessRestricted: 'InboxAccessRestricted',
	InboxComposeDisabledNoPermissionTooltip: 'InboxComposeDisabledNoPermissionTooltip',
	InboxComposeDisabledNoConnection: 'InboxComposeDisabledNoConnection',
	InboxMessageAllLabelRecipientsCount: 'InboxMessageAllLabelRecipientsCount',
	InboxMessageShowMoreRecipients: 'InboxMessageShowMoreRecipients',

	ManageInboxAccountPanelTitle: 'ManageInboxAccountPanelTitle',
	ManageInboxAccountButton: 'ManageInboxAccountButton',
	ManageInboxAccountEdit: 'ManageInboxAccountEdit',
	ManageInboxNewInboxTitle: 'ManageInboxNewInboxTitle',
	ManageInboxBasicInfoName: 'ManageInboxBasicInfoName',
	ManageInboxBasicInfoNamePlaceholder: 'ManageInboxBasicInfoNamePlaceholder',
	ManageInboxBasicInfoDescription: 'ManageInboxBasicInfoDescription',
	ManageInboxBasicInfoDescriptionPlaceholder: 'ManageInboxBasicInfoDescriptionPlaceholder',
	ManageInboxBasicInfoColor: 'ManageInboxBasicInfoColor',
	ManageInboxConnectAppSubtitle: 'ManageInboxConnectAppSubtitle',
	ManageInboxConnectAppEmail: 'ManageInboxConnectAppEmail',
	ManageInboxConnectAppConnect: 'ManageInboxConnectAppConnect',
	ManageInboxConnectAppSignInWith: 'ManageInboxConnectAppSignInWith',
	ManageInboxConnectAppAlreadyConnectedError: 'ManageInboxConnectAppAlreadyConnectedError',
	ManageInboxConnectAppContinue: 'ManageInboxConnectAppContinue',
	ManageInboxConnectAppConnectedInfo: 'ManageInboxConnectAppConnectedInfo',
	ManageInboxAssignTeamPlaceholder: 'ManageInboxAssignTeamPlaceholder',

	InboxSettingsDetailsTitle: 'InboxSettingsDetailsTitle',
	InboxSettingsDetailsDesc: 'InboxSettingsDetailsDesc',
	InboxSettingsStaffTitle: 'InboxSettingsStaffTitle',
	InboxSettingsStaffDesc: 'InboxSettingsStaffDesc',
	InboxSettingsAppsTitle: 'InboxSettingsAppsTitle',
	InboxSettingsAppsDesc: 'InboxSettingsAppsDesc',
	InboxSettingsAppsNewConnectedApp: 'InboxSettingsAppsNewConnectedApp',
	InboxSettingsDeleteInboxWarning: 'InboxSettingsDeleteInboxWarning',

	InboxSettingsDeleteInboxSuccess: 'InboxSettingsDeleteInboxSuccess',
	InboxSettingsDeleteInboxFailed: 'InboxSettingsDeleteInboxFailed',
	InboxSettingsUpdateInboxDetailsSuccess: 'InboxSettingsUpdateInboxDetailsSuccess',
	InboxSettingsUpdateInboxDetailsFailed: 'InboxSettingsUpdateInboxDetailsFailed',
	InboxSettingsUpdateInboxStaffsSuccess: 'InboxSettingsUpdateInboxStaffsSuccess',
	InboxSettingsUpdateInboxStaffsFailed: 'InboxSettingsUpdateInboxStaffsFailed',
	InboxSettingsDeleteAccountWarning: 'InboxSettingsDeleteAccountWarning',
	InboxSettingsDeleteAccountSuccess: 'InboxSettingsDeleteAccountSuccess',
	InboxSettingsDeleteAccountFailed: 'InboxSettingsDeleteAccountFailed',
	InboxSettingsUpdateReplyFormatSuccess: 'InboxSettingsUpdateReplyFormatSuccess',
	InboxSettingsUpdateReplyFormatFailed: 'InboxSettingsUpdateReplyFormatFailed',

	InboxSettingsReplyFormatTitle: 'InboxSettingsReplyFormatTitle',
	InboxSettingsReplyFormatDesc: 'InboxSettingsReplyFormatDesc',
	InboxSettingsSendFromLabel: 'InboxSettingsSendFromLabel',
	InboxSettingsEmailSignatureLabel: 'InboxSettingsEmailSignatureLabel',

	AttachmentUploadItemLoading: 'AttachmentUploadItemLoading',
	AttachmentUploadItemComplete: 'AttachmentUploadItemComplete',
	AttachmentUploadItemError: 'AttachmentUploadItemError',
	AttachmentBlockedFileType: 'AttachmentBlockedFileType',
	AttachmentTooLargeFileSize: 'AttachmentTooLargeFileSize',

	HiddenSectionsAndFields: 'HiddenSectionsAndFields',
	HiddenFields: 'HiddenFields',
	HiddenSections: 'HiddenSections',
	Fields: 'Fields',
	CustomizeClientFields: 'CustomizeClientFields',
	NewSection: 'NewSection',
	NewSectionOld: 'NewSectionOld',
	NewSectionWithGrid: 'NewSectionWithGrid',
	SectionName: 'SectionName',
	SaveSection: 'SaveSection',
	DeleteSection: 'DeleteSection',
	DeleteSectionInfo: 'DeleteSectionInfo',
	DeleteSectionWarning: 'DeleteSectionWarning',
	YesDeleteSection: 'YesDeleteSection',
	DuplicateHeadingName: 'DuplicateHeadingName',
	DropdownFormFieldPlaceHolder: 'DropdownFormFieldPlaceHolder',
	DropdownTextFieldPlaceholder: 'DropdownTextFieldPlaceholder',
	DropdownTextFieldError: 'DropdownTextFieldError',
	CreateNewConnection: 'CreateNewConnection',
	ConnectInboxModalTitle: 'ConnectInboxModalTitle',
	ConnectInboxModalDescription: 'ConnectInboxModalDescription',
	ConnectInboxModalExistingTitle: 'ConnectInboxModalExistingTitle',
	ConnectInboxModalExistingDescription: 'ConnectInboxModalExistingDescription',
	ConnectInboxGoogleDescription: 'ConnectInboxGoogleDescription',
	ConnectInboxMicrosoftDescription: 'ConnectInboxMicrosoftDescription',
	ShowColumnsMenu: 'ShowColumnsMenu',
	HideColumnButton: 'HideColumnButton',
	ShowColumnButton: 'ShowColumnButton',
	TableRowLabel: 'TableRowLabel',
	ClientsTable: 'ClientsTable',
	SyncInbox: 'SyncInbox',
	ManageConnectionsGoogleCalendarDescription: 'ManageConnectionsGoogleCalendarDescription',
	ManageConnectionsGmailDescription: 'ManageConnectionsGmailDescription',
	ManageConnectionsInboxSyncHelperText: 'ManageConnectionsInboxSyncHelperText',
	ChooseInbox: 'ChooseInbox',
	Gmail: 'Gmail',
	GoogleCalendar: 'GoogleCalendar',
	MediaGallery: 'MediaGallery',
	SearchUnsplashPlaceholder: 'SearchUnsplashPlaceholder',
	Image: 'Image',
	MyGallery: 'MyGallery',
	ManageStatuses: 'ManageStatuses',
	ManageStatusesDescription: 'ManageStatusesDescription',
	ManageStatusesActiveStatusHelperText: 'ManageStatusesActiveStatusHelperText',
	NewStatus: 'NewStatus',
	ManageStatusesSuccessSnackbar: 'ManageStatusesSuccessSnackbar',
	Rename: 'Rename',
	StatusFieldPlaceholder: 'StatusFieldPlaceholder',
	WhatIsCarepatron: 'WhatIsCarepatron',
	TransferStatusTitle: 'TransferStatusTitle',
	TransferStatusDescription: 'TransferStatusDescription',
	DeletedStatusLabel: 'DeletedStatusLabel',
	TransferStatusLabel: 'TransferStatusLabel',
	TransferStatusPlaceholder: 'TransferStatusPlaceholder',
	TransferStatusAlert: 'TransferStatusAlert',
	TransferAndDelete: 'TransferAndDelete',
	Back: 'Back',
	Summarise: 'Summarise',
	HowToUseAiSummarise: 'HowToUseAiSummarise',
	AskAI: 'AskAI',
	Seen: 'Seen',
	SummarisingContent: 'SummarisingContent',
	Dashboard: 'Dashboard',
	TimeAgoSeconds: 'TimeAgoSeconds',
	TimeAgoMinutes: 'TimeAgoMinutes',
	TimeAgoHours: 'TimeAgoHours',
	TimeAgoDays: 'TimeAgoDays',
	Conversation: 'Conversation',
	TotalConversations: 'TotalConversations',
	AddColor: 'AddColor',
	RemoveColor: 'RemoveColor',
	AddAnotherOption: 'AddAnotherOption',
	Options: 'Options',
	StartDate: 'StartDate',
	EndDate: 'EndDate',
	Deleted: 'Deleted',
	DeletedOn: 'DeletedOn',
	DeletedBy: 'DeletedBy',
	Lock: 'Lock',
	Locked: 'Locked',
	NoteLockSuccess: 'NoteLockSuccess',
	NoteUnlockSuccess: 'NoteUnlockSuccess',
	Unlock: 'Unlock',
	LockedNote: 'LockedNote',
	UnlockNoteHelper: 'UnlockNoteHelper',
	InboxDeleteConversationSuccess: 'InboxDeleteConversationSuccess',
	Archive: 'Archive',
	Unarchive: 'Unarchive',
	Archived: 'Archived',
	InboxCloseConversationSuccess: 'InboxCloseConversationSuccess',
	InboxReopenConversationSuccess: 'InboxReopenConversationSuccess',
	InboxIgnoreConversationSuccess: 'InboxIgnoreConversationSuccess',
	AddValue: 'AddValue',
	MoveToInbox: 'MoveToInbox',
	DeleteForever: 'DeleteForever',
	Restore: 'Restore',
	InboxRestoreConversationSuccess: 'InboxRestoreConversationSuccess',
	DeleteConversationConfirmationTitle: 'DeleteConversationConfirmationTitle',
	DeleteConversationConfirmationDescription: 'DeleteConversationConfirmationDescription',
	AddLabel: 'AddLabel',
	EmptyBin: 'EmptyBin',
	EmptyBinConfirmationTitle: 'EmptyBinConfirmationTitle',
	EmptyBinConfirmationDescription: 'EmptyBinConfirmationDescription',
	BulkPermanentDeleteTitle: 'BulkPermanentDeleteTitle',
	BulkPermanentDeleteDescription: 'BulkPermanentDeleteDescription',
	InboxEmptyBinSuccess: 'InboxEmptyBinSuccess',
	MarkAsUnread: 'MarkAsUnread',
	MarkAsRead: 'MarkAsRead',
	GroupBy: 'GroupBy',
	ColorAppointmentsBy: 'ColorAppointmentsBy',
	AppointmentStatus: 'AppointmentStatus',
	InvoiceStatus: 'InvoiceStatus',
	CustomiseAppointments: 'CustomiseAppointments',
	SearchConversations: 'SearchConversations',
	NoResultsFound: 'NoResultsFound',
	NoResultsFoundDescription: 'NoResultsFoundDescription',
	NoNoteFound: 'NoNoteFound',
	NoBillingItemsFound: 'NoBillingItemsFound',
	NoInvoicesFound: 'NoInvoicesFound',
	NoClaimsFound: 'NoClaimsFound',
	Others: 'Others',
	MessageAttachments: 'MessageAttachments',
	OrAttachSingleFile: 'OrAttachSingleFile',
	SeenByName: 'SeenByName',
	SentByName: 'SentByName',
	ScheduleSendByName: 'ScheduleSendByName',
	PlusOthers: 'PlusOthers',
	ReaderMaxDepthHasBeenExceededCode: 'ReaderMaxDepthHasBeenExceededCode',
	MustContainOneLetterError: 'MustContainOneLetterError',
	CannotContainSpecialCharactersError: 'CannotContainSpecialCharactersError',
	GoToClientRecord: 'GoToClientRecord',
	PhotoBy: 'PhotoBy',
	On: 'On',
	InvalidStatementDescriptor: 'InvalidStatementDescriptor',
	ClientPortalDashboardEmptyTitle: 'ClientPortalDashboardEmptyTitle',
	ClientPortalDashboardEmptyDescription: 'ClientPortalDashboardEmptyDescription',
	PotentialClientDuplicateTitle: 'PotentialClientDuplicateTitle',
	PotentialClientDuplicateWarning: 'PotentialClientDuplicateWarning',
	ExistingClients: 'ExistingClients',
	ViewRecord: 'ViewRecord',
	PermissionRequired: 'PermissionRequired',
	SearchTeamMembers: 'SearchTeamMembers',
	NumberOfTeamMembers: 'NumberOfTeamMembers',
	ConfirmationModalBulkDeleteMembersTitleId: 'ConfirmationModalBulkDeleteMembersTitleId',
	ConfirmationModalBulkDeleteMembersDescriptionId: 'ConfirmationModalBulkDeleteMembersDescriptionId',
	FailedToJoinTheMeeting: 'FailedToJoinTheMeeting',
	PleaseRefreshThisPageToTryAgain: 'PleaseRefreshThisPageToTryAgain',
	RefreshPage: 'RefreshPage',
	RetryingConnectionAttempt: 'RetryingConnectionAttempt',
	AttemptingToReconnect: 'AttemptingToReconnect',
	SearchRelationships: 'SearchRelationships',
	NumberOfRelationships: 'NumberOfRelationships',
	HiddenColumns: 'HiddenColumns',
	UseDefault: 'UseDefault',
	UseDefaultFilters: 'UseDefaultFilters',
	GroupFilterLabel: 'GroupFilterLabel',
	ShowColumn: 'ShowColumn',
	HideColumn: 'HideColumn',
	SearchContactsPlaceholder: 'SearchContactsPlaceholder',
	LearnMore: 'LearnMore',
	IntakeProcessLearnMoreInstructions: 'IntakeProcessLearnMoreInstructions',
	IntakeTemplateSelectorPlaceholder: 'IntakeTemplateSelectorPlaceholder',
	AreYouStillThere: 'AreYouStillThere',
	RemainingTime: 'RemainingTime',
	HaveBeenWaiting: 'HaveBeenWaiting',
	CallIdlePrompt: 'CallIdlePrompt',
	JoinCall: 'JoinCall',
	KeepWaiting: 'KeepWaiting',
	WaitingforMins: 'WaitingforMins',
	Leave: 'Leave',
	CallSessionExpiredError: 'CallSessionExpiredError',
	SubscriptionDiscountDescription: 'SubscriptionDiscountDescription',
	SubscriptionFreeTrialDescription: 'SubscriptionFreeTrialDescription',
	ScheduleName: 'ScheduleName',
	GeneralAvailabilityDescription2: 'GeneralAvailabilityDescription2',
	GeneralAvailabilityInfo2: 'GeneralAvailabilityInfo2',
	ChooseServices: 'ChooseServices',
	SmartDataChips: 'SmartDataChips',
	SearchPrepopulatedData: 'SearchPrepopulatedData',
	InvalidPromotionCode: 'InvalidPromotionCode',
	ExpiredPromotionCode: 'ExpiredPromotionCode',
	PromotionCodeApplied: 'PromotionCodeApplied',
	LinkToClient: 'LinkToClient',
	LearnToSetupInbox: 'LearnToSetupInbox',
	Beta: 'Beta',
	OlderMessages: 'OlderMessages',
	ImportClientsModalTitle: 'ImportClientsModalTitle',
	ImportClientsModalDescription: 'ImportClientsModalDescription',
	ImportClientsModalStep1Label: 'ImportClientsModalStep1Label',
	ImportClientsModalStep2Label: 'ImportClientsModalStep2Label',
	ImportClientsModalStep3Label: 'ImportClientsModalStep3Label',
	ImportClientsModalFileUploadHelperText: 'ImportClientsModalFileUploadHelperText',
	ImportClientsModalImportGuideLabel: 'ImportClientsModalImportGuideLabel',
	ImportDataSourceSelectorLabel: 'ImportDataSourceSelectorLabel',
	ImportDataSourceSelectorPlaceholder: 'ImportDataSourceSelectorPlaceholder',
	UploadFile: 'UploadFile',
	UploadFileMaxSizeError: 'UploadFileMaxSizeError',
	ImportClientSuccessSnackbarTitle: 'ImportClientSuccessSnackbarTitle',
	ImportClientSuccessSnackbarDescription: 'ImportClientSuccessSnackbarDescription',
	ConfirmDeleteScheduleDescription: 'ConfirmDeleteScheduleDescription',
	NewSchedule: 'NewSchedule',
	Classic: 'Classic',
	NewClient: 'NewClient',
	NewContact: 'NewContact',
	NewClientSuccess: 'NewClientSuccess',
	NewContactSuccess: 'NewContactSuccess',
	WebsiteUrl: 'WebsiteUrl',
	OtherEvents: 'OtherEvents',
	UntitledSchedule: 'UntitledSchedule',
	ConflictTimezoneWarningMessage: 'ConflictTimezoneWarningMessage',
	NewClients: 'NewClients',
	ImportClientsInProgressSnackbarTitle: 'ImportClientsInProgressSnackbarTitle',
	ImportClientsInProgressSnackbarDescription: 'ImportClientsInProgressSnackbarDescription',
	ImportClientsFailureSnackbarTitle: 'ImportClientsFailureSnackbarTitle',
	ImportClientsFailureSnackbarDescription: 'ImportClientsFailureSnackbarDescription',
	ImportingData: 'ImportingData',
	UseValue: 'UseValue',
	FileUploadInProgress: 'FileUploadInProgress',
	FileUploadComplete: 'FileUploadComplete',
	FileUploadFailed: 'FileUploadFailed',
	Importing: 'Importing',
	Uploading: 'Uploading',
	StandardColor: 'StandardColor',
	ReminderColor: 'ReminderColor',
	EventColor: 'EventColor',
	OutOfOfficeColor: 'OutOfOfficeColor',
	DateOverrideColor: 'DateOverrideColor',
	GoogleColor: 'GoogleColor',
	MicrosoftColor: 'MicrosoftColor',
	SyncGoogleCalendar: 'SyncGoogleCalendar',
	SyncOutlookCalendar: 'SyncOutlookCalendar',
	AddUnrelatedContactWarning: 'AddUnrelatedContactWarning',
	DeleteTaxRateConfirmationDescription: 'DeleteTaxRateConfirmationDescription',
	VideoCallChatBanner: 'VideoCallChatBanner',
	VideoCallChatTitle: 'VideoCallChatTitle',
	VideoCallChatSendBtn: 'VideoCallChatSendBtn',
	WelcomeToCarepatron: 'WelcomeToCarepatron',
	ChooseAccountTypeHeader: 'ChooseAccountTypeHeader',
	ForPractitioners: 'ForPractitioners',
	ForClients: 'ForClients',
	ForPractitionersDetails: 'ForPractitionersDetails',
	ForClientsDetails: 'ForClientsDetails',
	ClientFieldPlaceholder: 'ClientFieldPlaceholder',
	ClientFieldLabel: 'ClientFieldLabel',
	ClientInfoAddRow: 'ClientInfoAddRow',
	ClientInfoAddField: 'ClientInfoAddField',
	ClientInfoAlertMessage: 'ClientInfoAlertMessage',
	RemoveField: 'RemoveField',
	Uncategorized: 'Uncategorized',
	GroupEvent: 'GroupEvent',
	GroupEventHelper: 'GroupEventHelper',
	MaximumAttendeeLimit: 'MaximumAttendeeLimit',
	AttendeesCount: 'AttendeesCount',
	DateSpecificHours: 'DateSpecificHours',
	DateSpecificHoursDescription: 'DateSpecificHoursDescription',
	NewDateOverrideButton: 'NewDateOverrideButton',
	AddTimezone: 'AddTimezone',
	AddLocation: 'AddLocation',
	DeleteStaffScheduleOverrideDescription: 'DeleteStaffScheduleOverrideDescription',
	YesDeleteOverride: 'YesDeleteOverride',
	NameMustNotBeAnEmail: 'NameMustNotBeAnEmail',
	NameMustNotBeAWebsite: 'NameMustNotBeAWebsite',
	NameMustNotContainHTMLTags: 'NameMustNotContainHTMLTags',
	NameMustNotContainAtSign: 'NameMustNotContainAtSign',
	NameMustNotContainSpecialCharacters: 'NameMustNotContainSpecialCharacters',
	EarnReferralCredit: 'EarnReferralCredit',
	ManageReferralsModalTitle: 'ManageReferralsModalTitle',
	ManageReferralsModalDescription: 'ManageReferralsModalDescription',
	ReferralEmailDefaultSubject: 'ReferralEmailDefaultSubject',
	ReferralEmailDefaultBody: 'ReferralEmailDefaultBody',
	ShareYourReferralLink: 'ShareYourReferralLink',
	ReferralRewardBanner: 'ReferralRewardBanner',
	Copy: 'Copy',
	Merge: 'Merge',
	CopiedToClipboard: 'CopiedToClipboard',
	ReferredUserBenefitTitle: 'ReferredUserBenefitTitle',
	ReferredUserBenefitSubtitle: 'ReferredUserBenefitSubtitle',
	ReferringUserBenefitTitle: 'ReferringUserBenefitTitle',
	ReferringUserBenefitSubtitle: 'ReferringUserBenefitSubtitle',
	HowDoesReferralWork: 'HowDoesReferralWork',
	AmountOfReferralRequests: 'AmountOfReferralRequests',
	ReferralProgress: 'ReferralProgress',
	CreditAdded: 'CreditAdded',
	Closed: 'Closed',
	Reopen: 'Reopen',
	ReferralHasSignedUpDescription: 'ReferralHasSignedUpDescription',
	ReferralHasNotSignedUpDescription: 'ReferralHasNotSignedUpDescription',
	ExpiredReferralDescription: 'ExpiredReferralDescription',
	InvalidReferralDescription: 'InvalidReferralDescription',
	ReferralListErrorDescription: 'ReferralListErrorDescription',
	AppNotifications: 'AppNotifications',
	AppNotificationsEmptyHeading: 'AppNotificationsEmptyHeading',
	AppNotificationsEmptySubtext: 'AppNotificationsEmptySubtext',
	AppNotificationsUnread: 'AppNotificationsUnread',
	MarkAllAsRead: 'MarkAllAsRead',
	PartiallyFull: 'PartiallyFull',
	MaxCapacity: 'MaxCapacity',
	DateOverride: 'DateOverride',
	DateOverrides: 'DateOverrides',
	DateOverrideInfo: 'DateOverrideInfo',
	AllDay: 'AllDay',
	Practitioner: 'Practitioner',
	MustHaveAtLeastXItems: 'MustHaveAtLeastXItems',
	MustEndWithANumber: 'MustEndWithANumber',
	UnableToPrintDocument: 'UnableToPrintDocument',
	SafetyRedirectHeading: 'SafetyRedirectHeading',
	SafetyRedirectSubtext: 'SafetyRedirectSubtext',
	ComingSoon: 'ComingSoon',
	SuggestedActions: 'SuggestedActions',
	RecentTemplates: 'RecentTemplates',
	BasicBlocks: 'BasicBlocks',
	PreviousNotes: 'PreviousNotes',
	UpcomingDateOverridesEmpty: 'UpcomingDateOverridesEmpty',
	PastDateOverridesEmpty: 'PastDateOverridesEmpty',
	DateOverrideInfoBanner: 'DateOverrideInfoBanner',
	ExternalEventInfoBanner: 'ExternalEventInfoBanner',
	Language: 'Language',
	GuideToImproveVideoQuality: 'GuideToImproveVideoQuality',
	Referrals: 'Referrals',
	Invited: 'Invited',
	LocalizationPanelTitle: 'LocalizationPanelTitle',
	LocalizationPanelDescription: 'LocalizationPanelDescription',
	ClientLocalizationPanelTitle: 'ClientLocalizationPanelTitle',
	ClientLocalizationPanelDescription: 'ClientLocalizationPanelDescription',
	ClientPreferredNotificationPanelTitle: 'ClientPreferredNotificationPanelTitle',
	ClientPreferredNotificationPanelDescription: 'ClientPreferredNotificationPanelDescription',
	SMS: 'SMS',
	Someone: 'Someone',
	ServiceWorkerForceUIUpdateDialogTitle: 'ServiceWorkerForceUIUpdateDialogTitle',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'ServiceWorkerForceUIUpdateDialogSubTitle',
	ServiceWorkerForceUIUpdateDialogDescription: 'ServiceWorkerForceUIUpdateDialogDescription',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'ServiceWorkerForceUIUpdateDialogReloadButton',
	InvoiceTemplates: 'InvoiceTemplates',
	InvoiceTemplatesDescription: 'InvoiceTemplatesDescription',
	InvoiceTheme: 'InvoiceTheme',
	CustomizeInvoiceTemplate: 'CustomizeInvoiceTemplate',
	InvoiceFieldsPlaceholder: 'InvoiceFieldsPlaceholder',
	FieldLabelTooltip: 'FieldLabelTooltip',
	EditClient: 'EditClient',
	EditPractitioner: 'EditPractitioner',
	EditProvider: 'EditProvider',
	EditBillTo: 'EditBillTo',
	EditInvoiceDetails: 'EditInvoiceDetails',
	SimplifyBillToDetails: 'SimplifyBillToDetails',
	SimplifyBillToHelperText: 'SimplifyBillToHelperText',
	InvoiceTemplateDescriptionPlaceholder: 'InvoiceTemplateDescriptionPlaceholder',
	InvoiceTemplateEmploymentStatusPlaceholder: 'InvoiceTemplateEmploymentStatusPlaceholder',
	InvoiceTemplateEthnicityPlaceholder: 'InvoiceTemplateEthnicityPlaceholder',
	InvoiceTemplateAddressPlaceholder: 'InvoiceTemplateAddressPlaceholder',
	ShowServiceCode: 'ShowServiceCode',
	ShowUnits: 'ShowUnits',
	ShowTax: 'ShowTax',

	// Settings options
	NavigationDrawerPersonal: 'NavigationDrawerPersonal',
	NavigationDrawerPersonalInfo: 'NavigationDrawerPersonalInfo',
	NavigationDrawerWorkspace: 'NavigationDrawerWorkspace',
	NavigationDrawerWorkspaceInfo: 'NavigationDrawerWorkspaceInfo',
	NavigationDrawerBilling: 'NavigationDrawerBilling',
	NavigationDrawerBillingInfo: 'NavigationDrawerBillingInfo',
	NavigationDrawerInsurance: 'NavigationDrawerInsurance',
	NavigationDrawerInsuranceInfo: 'NavigationDrawerInsuranceInfo',
	NavigationDrawerScheduling: 'NavigationDrawerScheduling',
	NavigationDrawerSchedulingInfo: 'NavigationDrawerSchedulingInfo',
	NavigationDrawerCommunication: 'NavigationDrawerCommunication',
	NavigationDrawerCommunicationInfo: 'NavigationDrawerCommunicationInfo',
	NavigationDrawerTrash: 'NavigationDrawerTrash',
	NavigationDrawerTrashInfo: 'NavigationDrawerTrashInfo',
	SettingsTabDetails: 'SettingsTabDetails',
	SettingsTabServicesAndAvailability: 'SettingsTabServicesAndAvailability',
	SettingsTabConnectedApps: 'SettingsTabConnectedApps',
	SettingsTabNotifications: 'SettingsTabNotifications',
	SettingsTabSubscriptions: 'SettingsTabSubscriptions',
	SettingsTabCustomFields: 'SettingsTabCustomFields',
	SettingsTabReminders: 'SettingsTabReminders',
	SettingsTabBillingDetails: 'SettingsTabBillingDetails',
	SettingsTabInvoices: 'SettingsTabInvoices',
	SettingsTabAutomation: 'SettingsTabAutomation',
	SettingsTabPayers: 'SettingsTabPayers',
	SettingsTabLocations: 'SettingsTabLocations',
	SettingsTabServices: 'SettingsTabServices',
	SettingsTabOnlineBooking: 'SettingsTabOnlineBooking',
	SettingsTabWorkflowTemplates: 'SettingsTabWorkflowTemplates',
	SettingsTabWorkflowAutomations: 'SettingsTabWorkflowAutomations',
	SettingsTabWorkflowReminders: 'SettingsTabWorkflowReminders',
	// end of settings options
	CircularProgressWithLabel: 'CircularProgressWithLabel',
	InvoiceUpdateVersionMessage: 'InvoiceUpdateVersionMessage',
	SwitchToMyPortal: 'SwitchToMyPortal',
	SwitchToMyPortalTooltip: 'SwitchToMyPortalTooltip',
	MyPortal: 'MyPortal',

	// Referrals empty state
	NoActiveReferrals: 'NoActiveReferrals',
	NoCompletedReferrals: 'NoCompletedReferrals',
	NoInvitedReferrals: 'NoInvitedReferrals',

	OnlyVisibleToYou: 'OnlyVisibleToYou',
	Units: 'Units',
	UnitsIsRequired: 'UnitsIsRequired',
	UnitsMustBeGreaterThanZero: 'UnitsMustBeGreaterThanZero',
	TaxRate: 'TaxRate',
	Subtotal: 'Subtotal',
	MeetingTimerMessage: 'MeetingTimerMessage',
	Overtime: 'Overtime',
	Remaining: 'Remaining',

	GridView: 'GridView',
	AgendaView: 'AgendaView',
	TranscribeLanguageSelector: 'TranscribeLanguageSelector',
	Theme: 'Theme',
	CustomizeInvoiceTemplateDescription: 'CustomizeInvoiceTemplateDescription',
	EmptyAgendaViewDescription: 'EmptyAgendaViewDescription',
	AboutClient: 'AboutClient',
	UploadYourLogo: 'UploadYourLogo',
	AvailabilityToggle: 'AvailabilityToggle',
	AvailabilityEnabled: 'AvailabilityEnabled',
	AvailabilityDisabled: 'AvailabilityDisabled',
	AvailabilityEnableSchedule: 'AvailabilityEnableSchedule',
	AvailabilityDisableSchedule: 'AvailabilityDisableSchedule',
	AvailabilityNoActiveConfirmationTitle: 'AvailabilityNoActiveConfirmationTitle',
	AvailabilityNoActiveConfirmationDescription: 'AvailabilityNoActiveConfirmationDescription',
	AvailabilityNoActiveConfirmationProceed: 'AvailabilityNoActiveConfirmationProceed',
	AvailabilityNoActiveBanner: 'AvailabilityNoActiveBanner',
	AvailabilityUnsetDate: 'AvailabilityUnsetDate',
	Group: 'Group',
	CountNotesAdded: 'CountNotesAdded',
	CountInvoicesAdded: 'CountInvoicesAdded',
	AdvancedSettings: 'AdvancedSettings',

	CannotDeleteInvoice: 'CannotDeleteInvoice',

	MicrophonePermissionError: 'MicrophonePermissionError',
	MicrophonePermissionPrompt: 'MicrophonePermissionPrompt',
	MicrophonePermissionBlocked: 'MicrophonePermissionBlocked',
	MicrophonePermissionBlockedDescription: 'MicrophonePermissionBlockedDescription',
	InsurancePayers: 'InsurancePayers',
	InsurancePayersDescription: 'InsurancePayersDescription',
	AvailablePayers: 'AvailablePayers',
	AddPayer: 'AddPayer',
	GrantPortalAccess: 'GrantPortalAccess',
	NumberOfInvoices: 'NumberOfInvoices',
	ShowFullAddress: 'ShowFullAddress',
	HideFullAddress: 'HideFullAddress',
	Insurance: 'Insurance',
	InsuranceSelfPay: 'InsuranceSelfPay',
	Payers: 'Payers',
	DeletePayer: 'DeletePayer',
	PayerDetails: 'PayerDetails',
	PayerDetailsDescription: 'PayerDetailsDescription',
	PayerId: 'PayerId',
	PayerCoverage: 'PayerCoverage',
	NewManualPayer: 'NewManualPayer',
	PayerID: 'PayerID',
	PayerName: 'PayerName',
	CoverageType: 'CoverageType',
	AddingManualPayerDisclaimer: 'AddingManualPayerDisclaimer',
	GuideToManagingPayers: 'GuideToManagingPayers',

	Actions: 'Actions',
	NewAppointment: 'NewAppointment',
	NewTask: 'NewTask',
	ErrorPayerNotFound: 'ErrorPayerNotFound',

	BillingProfilesNewHeader: 'BillingProfilesNewHeader',
	BillingProfilesEditHeader: 'BillingProfilesEditHeader',
	BillingProfileOverridesHeader: 'BillingProfileOverridesHeader',
	BillingProfileOverridesDescription: 'BillingProfileOverridesDescription',
	BillingProfileProviderType: 'BillingProfileProviderType',
	BillingProfileTypeOrganisation: 'BillingProfileTypeOrganisation',
	BillingProfileTypeOrganisationSubLabel: 'BillingProfileTypeOrganisationSubLabel',
	BillingProfileTypeIndividual: 'BillingProfileTypeIndividual',
	BillingProfileTypeIndividualSubLabel: 'BillingProfileTypeIndividualSubLabel',
	CopyToClipboardError: 'CopyToClipboardError',
	TaxonomyCode: 'TaxonomyCode',
	SSN: 'SSN',
	EIN: 'EIN',
	ABN: 'ABN',
	IVA: 'IVA',
	HST: 'HST',
	TaxID: 'TaxID',
	TFN: 'TFN',
	TRN: 'TRN',
	TaxNumberTypeInvalid: 'TaxNumberTypeInvalid',
	HideYourView: 'HideYourView',
	ShowYourView: 'ShowYourView',
	ReorderYourServiceList: 'ReorderYourServiceList',
	ReorderYourServiceListDescription: 'ReorderYourServiceListDescription',
	BillingProfilesSectionDescription: 'BillingProfilesSectionDescription',
	AdditionalBillingProfilesSectionDescription: 'AdditionalBillingProfilesSectionDescription',
	DefaultBillingProfile: 'DefaultBillingProfile',
	AdditionalBillingProfiles: 'AdditionalBillingProfiles',
	BillingProfile: 'BillingProfile',
	BillingProfiles: 'BillingProfiles',

	Payment: 'Payment',
	Purchase: 'Purchase',
	OverpaidAmount: 'OverpaidAmount',
	CreditIssued: 'CreditIssued',

	Details: 'Details',
	BillableItems: 'BillableItems',
	UnpaidItems: 'UnpaidItems',
	PaidItems: 'PaidItems',
	Payments: 'Payments',
	UnallocatedPayments: 'UnallocatedPayments',
	Superbill: 'Superbill',
	WriteOff: 'WriteOff',
	BillableItemsEmptyState: 'BillableItemsEmptyState',

	Ignored: 'Ignored',
	IgnoreSender: 'IgnoreSender',
	IgnoreSenders: 'IgnoreSenders',
	IgnoreSenderDescription: 'IgnoreSenderDescription',
	IgnoreSendersSuccess: 'IgnoreSendersSuccess',
	StopIgnoringSenders: 'StopIgnoringSenders',
	StopIgnoreSendersDescription: 'StopIgnoreSendersDescription',
	StopIgnoringSendersSuccess: 'StopIgnoringSendersSuccess',
	StopIgnoring: 'StopIgnoring',
	IgnoreOnce: 'IgnoreOnce',
	AlwaysIgnore: 'AlwaysIgnore',
	YesStopIgnoring: 'YesStopIgnoring',

	InboxSuggestMoveToBulkComposeMessageTitle: 'InboxSuggestMoveToBulkComposeMessageTitle',
	InboxSuggestMoveToBulkComposeMessageContent: 'InboxSuggestMoveToBulkComposeMessageContent',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'InboxSuggestMoveToBulkComposeMessageActionConfirm',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'InboxSuggestMoveToBulkComposeMessageActionCancel',

	InboxUndoSendMessageSuccess: 'InboxUndoSendMessageSuccess',

	EmailNotVerifiedTitle: 'EmailNotVerifiedTitle',
	VerifyNow: 'VerifyNow',
	NewRelationship: 'NewRelationship',
	ImportClients: 'ImportClients',
	NoAppointmentsHeading: 'NoAppointmentsHeading',
	NoDocumentationHeading: 'NoDocumentationHeading',
	NoRecordsHeading: 'NoRecordsHeading',
	NoRelationshipsHeading: 'NoRelationshipsHeading',
	NoClientsHeading: 'NoClientsHeading',
	NoInvoicesHeading: 'NoInvoicesHeading',
	NoContactsHeading: 'NoContactsHeading',
	NoConnectionsHeading: 'NoConnectionsHeading',
	NoTemplatesHeading: 'NoTemplatesHeading',
	NoTrashItemsHeading: 'NoTrashItemsHeading',
	NoDuplicateRecordsHeading: 'NoDuplicateRecordsHeading',
	PortalNoAppointmentsHeading: 'PortalNoAppointmentsHeading',
	PortalNoRelationshipsHeading: 'PortalNoRelationshipsHeading',
	PortalNoDocumentationHeading: 'PortalNoDocumentationHeading',

	SendingFailed: 'SendingFailed',
	Error: 'Error',

	NotRequiredField: 'NotRequiredField',

	Warning: 'Warning',
	EmailContainsIgnoredDescription: 'EmailContainsIgnoredDescription',
	SendAndStopIgnore: 'SendAndStopIgnore',

	AccountCredit: 'AccountCredit',
	UnpaidInvoices: 'UnpaidInvoices',
	PaymentsUnallocated: 'PaymentsUnallocated',

	GenericErrorTitle: 'GenericErrorTitle',
	GenericErrorDescription: 'GenericErrorDescription',
	ClientBalance: 'ClientBalance',
	Retry: 'Retry',

	Verify: 'Verify',
	VerificationCode: 'VerificationCode',
	VerificationEmailTitle: 'VerificationEmailTitle',
	VerificationEmailSubtitle: 'VerificationEmailSubtitle',
	VerificationEmailDescription: 'VerificationEmailDescription',
	DeleteBillingProfileConfirmationMessage: 'DeleteBillingProfileConfirmationMessage',

	CallChangeLayoutTextContent: 'CallChangeLayoutTextContent',
	CallLayoutOptionAuto: 'CallLayoutOptionAuto',
	CallLayoutOptionTiled: 'CallLayoutOptionTiled',
	CallLayoutOptionSpotlight: 'CallLayoutOptionSpotlight',
	CallLayoutOptionSidebar: 'CallLayoutOptionSidebar',

	Transcribe: 'Transcribe',
	Dictate: 'Dictate',
	Dictation: 'Dictation',

	Paused: 'Paused',
	Resume: 'Resume',
	Recording: 'Recording',
	Pause: 'Pause',
	EndSession: 'EndSession',
	Finish: 'Finish',

	ConfirmationModalEndSession: 'ConfirmationModalEndSession',
	YesEnd: 'YesEnd',
	PayerPhoneNumber: 'PayerPhoneNumber',

	DeleteInsurancePayerConfirmationMessage: 'DeleteInsurancePayerConfirmationMessage',
	DeleteInsurancePayerFailure: 'DeleteInsurancePayerFailure',
	Transcript: 'Transcript',
	Generate: 'Generate',
	GenerateNote: 'GenerateNote',
	DownloadAsPdf: 'DownloadAsPdf',
	DeleteTranscript: 'DeleteTranscript',
	Timestamp: 'Timestamp',
	ShowTimestamp: 'ShowTimestamp',
	ShowSpeakers: 'ShowSpeakers',
	ShowOnTranscript: 'ShowOnTranscript',

	ConfirmationModalDeleteTranscript: 'ConfirmationModalDeleteTranscript',
	GuideToSubscriptionsBilling: 'GuideToSubscriptionsBilling',
	AddingTeamMembersIncreaseCostAlert: 'AddingTeamMembersIncreaseCostAlert',
	TranscriptSuccessSnackbar: 'TranscriptSuccessSnackbar',

	Recurring: 'Recurring',
	Trash: 'Trash',
	EmptyTrash: 'EmptyTrash',
	SearchTrashItems: 'SearchTrashItems',
	TrashRestoredItems: 'TrashRestoredItems',
	TrashRestoredAllItems: 'TrashRestoredAllItems',
	TrashRestoredItemsFailure: 'TrashRestoredItemsFailure',
	TrashDeletedItems: 'TrashDeletedItems',
	TrashDeletedAllItems: 'TrashDeletedAllItems',
	TrashDeletedItemsFailure: 'TrashDeletedItemsFailure',
	TrashLocationContactType: 'TrashLocationContactType',
	TrashLocationAppointmentType: 'TrashLocationAppointmentType',
	TrashLocationNoteType: 'TrashLocationNoteType',
	TrashLocationBillingAndPaymentsType: 'TrashLocationBillingAndPaymentsType',
	TrashDeleteItemsModalTitle: 'TrashDeleteItemsModalTitle',
	TrashDeleteItemsModalDescription: 'TrashDeleteItemsModalDescription',
	TrashRestoreItemsModalTitle: 'TrashRestoreItemsModalTitle',
	TrashRestoreItemsModalDescription: 'TrashRestoreItemsModalDescription',
	TrashDeleteItemsModalConfirm: 'TrashDeleteItemsModalConfirm',
	TrashSuccessfullyDeletedItem: 'TrashSuccessfullyDeletedItem',
	RestorableItemModalTitle: 'RestorableItemModalTitle',
	RestorableItemModalDescription: 'RestorableItemModalDescription',

	StartRecordErrorTitle: 'StartRecordErrorTitle',
	RecordingMicrophoneAccessErrorMessage: 'RecordingMicrophoneAccessErrorMessage',
	GuideToTroubleshooting: 'GuideToTroubleshooting',
	StartRecording: 'StartRecording',
	StartDictating: 'StartDictating',
	WhichBestDescribesYou: 'WhichBestDescribesYou',
	WhatsYourTeamSize: 'WhatsYourTeamSize',
	WhatsYourTeamSizeDescription: 'WhatsYourTeamSizeDescription',
	WhatsYourBusinessName: 'WhatsYourBusinessName',
	SetYourLocation: 'SetYourLocation',
	WhatServicesDoYouOffer: 'WhatServicesDoYouOffer',
	WhatsYourAvailability: 'WhatsYourAvailability',
	StyleYourWorkspace: 'StyleYourWorkspace',
	WebsiteOptional: 'WebsiteOptional',
	BusinessAddress: 'BusinessAddress',
	BusinessAddressOptional: 'BusinessAddressOptional',
	VideoConferencing: 'VideoConferencing',
	VirtualLocation: 'VirtualLocation',
	SetYourLocationDescription: 'SetYourLocationDescription',
	WhatServicesDoYouOfferDescription: 'WhatServicesDoYouOfferDescription',
	NoServicesAdded: 'NoServicesAdded',
	NoServicesWillBeAdded: 'NoServicesWillBeAdded',
	PersonalizingYourWorkspace: 'PersonalizingYourWorkspace',
	StandardAppointment: 'StandardAppointment',
	InitialAssessment: 'InitialAssessment',
	FinalAppointment: 'FinalAppointment',
	MyAvailability: 'MyAvailability',
	WhatsYourAvailabilityDescription: 'WhatsYourAvailabilityDescription',
	StyleYourWorkspaceDescription1: 'StyleYourWorkspaceDescription1',
	StyleYourWorkspaceDescription2: 'StyleYourWorkspaceDescription2',
	ContactChangeConfirmation: 'ContactChangeConfirmation',
	UploadingAudio: 'UploadingAudio',
	TranscriptRecordingCompleteInfo: 'TranscriptRecordingCompleteInfo',
	ClientInsuranceTabLabel: 'ClientInsuranceTabLabel',
	DoItLater: 'DoItLater',
	SheHer: 'SheHer',
	HeHim: 'HeHim',
	ThemThey: 'ThemThey',
	FirstPerson: 'FirstPerson',
	ThirdPerson: 'ThirdPerson',
	ClinicalFormat: 'ClinicalFormat',
	RecordingInProgress: 'RecordingInProgress',
	VideoCallTranscriptionRecordingNote: 'VideoCallTranscriptionRecordingNote',
	VideoCallTranscriptionTitle: 'VideoCallTranscriptionTitle',
	VideoCallTranscriptionFormHeading: 'VideoCallTranscriptionFormHeading',
	VideoCallTranscriptionFormDescription: 'VideoCallTranscriptionFormDescription',
	VideoCallTranscriptionFormNoteTemplateField: 'VideoCallTranscriptionFormNoteTemplateField',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'VideoCallTranscriptionFormNoteTemplateFieldPlaceholder',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'VideoCallTranscriptionFormNoteTemplateFieldEmptyText',
	VideoCallTranscriptionFormLanguageField: 'VideoCallTranscriptionFormLanguageField',
	VideoCallTranscriptionReferClientField: 'VideoCallTranscriptionReferClientField',
	VideoCallTranscriptionReferPractitionerField: 'VideoCallTranscriptionReferPractitionerField',
	VideoCallTranscriptionPronounField: 'VideoCallTranscriptionPronounField',
	VideoCallTranscriptionWritingPerspectiveField: 'VideoCallTranscriptionWritingPerspectiveField',
	VideoCallDisconnectedMessage: 'VideoCallDisconnectedMessage',

	ChoosePhysicalOrRemoteLocations: 'ChoosePhysicalOrRemoteLocations',
	ChooseYourProvider: 'ChooseYourProvider',
	ChooseAContact: 'ChooseAContact',
	Spouse: 'Spouse',
	Parent: 'Parent',
	AllContactPolicySelectorLabel: 'AllContactPolicySelectorLabel',
	PolicyHolder: 'PolicyHolder',
	PolicyHoldersAddress: 'PolicyHoldersAddress',
	InsurancePoliciesListTitle: 'InsurancePoliciesListTitle',
	InsurancePoliciesListSubtitle: 'InsurancePoliciesListSubtitle',
	InsurancePoliciesDetailsTitle: 'InsurancePoliciesDetailsTitle',
	InsurancePoliciesDetailsSubtitle: 'InsurancePoliciesDetailsSubtitle',
	NewPolicy: 'NewPolicy',
	Primary: 'Primary',
	Secondary: 'Secondary',
	InsuranceType: 'InsuranceType',
	PolicyStatus: 'PolicyStatus',
	InsurancePayer: 'InsurancePayer',
	GroupId: 'GroupId',
	PlanId: 'PlanId',
	MemberId: 'MemberId',
	PolicyDates: 'PolicyDates',
	CopayOrCoinsurance: 'CopayOrCoinsurance',
	Deductibles: 'Deductibles',
	Verified: 'Verified',
	Unverified: 'Unverified',
	PrimaryInsurance: 'PrimaryInsurance',
	SecondaryInsurance: 'SecondaryInsurance',
	EffectiveStartEndDate: 'EffectiveStartEndDate',

	DeleteInsurancePolicyConfirmationMessage: 'DeleteInsurancePolicyConfirmationMessage',

	Credit: 'Credit',
	Refund: 'Refund',
	Refunds: 'Refunds',

	CreditAdjustment: 'CreditAdjustment',
	Debit: 'Debit',
	CreditAdjustmentReasonPlaceholder: 'CreditAdjustmentReasonPlaceholder',
	CreditAdjustmentReasonHelperText: 'CreditAdjustmentReasonHelperText',
	Reason: 'Reason',
	AdjustmentType: 'AdjustmentType',
	CurrentCredit: 'CurrentCredit',
	Partner: 'Partner',
	DeFacto: 'DeFacto',
	RelationshipTypeSelectorPlaceholder: 'RelationshipTypeSelectorPlaceholder',

	StartTranscription: 'StartTranscription',
	StartTranscribing: 'StartTranscribing',
	StartTranscribingNotes: 'StartTranscribingNotes',
	ContactName: 'ContactName',
	AddContactRelationship: 'AddContactRelationship',
	EditContactRelationship: 'EditContactRelationship',
	Sex: 'Sex',
	ContactRelationshipGrantAccessInfo: 'ContactRelationshipGrantAccessInfo',
	SexSelectorPlaceholder: 'SexSelectorPlaceholder',
	ContactRelationshipFormAccessType: 'ContactRelationshipFormAccessType',
	ImportingCalendarProductEvents: 'ImportingCalendarProductEvents',
	SyncingCalendarName: 'SyncingCalendarName',
	SyncingFailed: 'SyncingFailed',

	GmailSendMessagesLimitWarning: 'GmailSendMessagesLimitWarning',

	GeneratingTranscript: 'GeneratingTranscript',
	GeneratingYourTranscript: 'GeneratingYourTranscript',
	GeneratingTranscriptDescription: 'GeneratingTranscriptDescription',

	ExportAppointments: 'ExportAppointments',

	Scheduled: 'Scheduled',
	CancelSend: 'CancelSend',
	SendScheduledForDate: 'SendScheduledForDate',
	SchedulingSend: 'SchedulingSend',
	ScheduleSendAlertInfo: 'ScheduleSendAlertInfo',
	ScheduleSend: 'ScheduleSend',
	GeneralHoursPlural: 'GeneralHoursPlural',
	TomorrowMorning: 'TomorrowMorning',
	TomorrowAfternoon: 'TomorrowAfternoon',
	ChooseDateAndTime: 'ChooseDateAndTime',
	TodayInMinsAbbreviated: 'TodayInMinsAbbreviated',
	TodayInHoursPlural: 'TodayInHoursPlural',
	Tomorrow: 'Tomorrow',

	GenerateNoteFor: 'GenerateNoteFor',

	ApplyAccountCredit: 'ApplyAccountCredit',
	CreditsUsed: 'CreditsUsed',
	CreditBalance: 'CreditBalance',
	NetworkQualityFair: 'NetworkQualityFair',
	NetworkQualityGood: 'NetworkQualityGood',
	NetworkQualityPoor: 'NetworkQualityPoor',
	SubscriptionPlansDescription: 'SubscriptionPlansDescription',
	SubscriptionPlansDescriptionNoPermission: 'SubscriptionPlansDescriptionNoPermission',
	AgeYearsOld: 'AgeYearsOld',
	VideoCallTilePaused: 'VideoCallTilePaused',
	ServiceCoverageTitle: 'ServiceCoverageTitle',
	ServiceCoverageDescription: 'ServiceCoverageDescription',
	ServiceCoverageNoServicesDescription: 'ServiceCoverageNoServicesDescription',
	ServiceCoverageNoServicesLabel: 'ServiceCoverageNoServicesLabel',
	ServiceCoverageGoToServices: 'ServiceCoverageGoToServices',

	UploadAudio: 'UploadAudio',
	ClickToUpload: 'ClickToUpload',
	DragAndDrop: 'DragAndDrop',
	AiScribeUploadFormat: 'AiScribeUploadFormat',
	AiScribeUploadSizeLimit: 'AiScribeUploadSizeLimit',

	UploadToCarepatron: 'UploadToCarepatron',
	InvalidFileType: 'InvalidFileType',
	AiAskSupportedFileTypes: 'AiAskSupportedFileTypes',

	FileInvalidType: 'FileInvalidType',
	FileTooLarge: 'FileTooLarge',
	FileTooSmall: 'FileTooSmall',
	TooManyFiles: 'TooManyFiles',

	StripeAccountRejectedError: 'StripeAccountRejectedError',
	Covered: 'Covered',
	NotCovered: 'NotCovered',
	PrimaryPolicy: 'PrimaryPolicy',
	SecondaryPolicy: 'SecondaryPolicy',
	OtherPolicy: 'OtherPolicy',
	NoCopayOrCoinsurance: 'NoCopayOrCoinsurance',
	Copay: 'Copay',
	Coinsurance: 'Coinsurance',
	AiSmartPromptPrimaryText: 'AiSmartPromptPrimaryText',
	AiSmartPromptSecondaryText: 'AiSmartPromptSecondaryText',
	AiSmartPromptNodePlaceholderText: 'AiSmartPromptNodePlaceholderText',
	RequestCoverageReport: 'RequestCoverageReport',
	VideoCallAudioInputFailed: 'VideoCallAudioInputFailed',
	VideoCallAudioInputFailedMessage: 'VideoCallAudioInputFailedMessage',
	OpenSettings: 'OpenSettings',

	IssueCreditAdjustment: 'IssueCreditAdjustment',
	DeletedUserTooltip: 'DeletedUserTooltip',
	InvoiceContactDeleted: 'InvoiceContactDeleted',

	WriteOffReasonHelperText: 'WriteOffReasonHelperText',
	WriteOffReasonPlaceholder: 'WriteOffReasonPlaceholder',
	WriteOffModalTitle: 'WriteOffModalTitle',
	WriteOffModalDescription: 'WriteOffModalDescription',
	WriteOffTotal: 'WriteOffTotal',

	RefundAmount: 'RefundAmount',
	RefundReasonDescription: 'RefundReasonDescription',
	RefundAcknowledgement: 'RefundAcknowledgement',
	RefundContent: 'RefundContent',
	RefundNonStripePaymentContent: 'RefundNonStripePaymentContent',
	RefundError: 'RefundError',
	ContactSupport: 'ContactSupport',
	RefundExceedTotalValidationError: 'RefundExceedTotalValidationError',
	Lineitems: 'Lineitems',
	NoPermissions: 'NoPermissions',
	TotalAccountCredit: 'TotalAccountCredit',
	NumberOfLineitemsToCredit: 'NumberOfLineitemsToCredit',
	TaxPercentageOfAmount: 'TaxPercentageOfAmount',
	TotalAmountToCreditInCurrency: 'TotalAmountToCreditInCurrency',
	PartialRefundAmount: 'PartialRefundAmount',
	Failed: 'Failed',
	PartiallyRefunded: 'PartiallyRefunded',
	RefundCouldNotBeProcessed: 'RefundCouldNotBeProcessed',
	RefundFailed: 'RefundFailed',
	RefundFailedTooltip: 'RefundFailedTooltip',
	Refunded: 'Refunded',
	Activity: 'Activity',
	NotificationSettingChannelSummary: 'NotificationSettingChannelSummary',
	NotificationSettingPanelTitle: 'NotificationSettingPanelTitle',
	NotificationSettingPanelDescription: 'NotificationSettingPanelDescription',
	NotificationSettingInApp: 'NotificationSettingInApp',
	NotificationSettingEmail: 'NotificationSettingEmail',
	NotificationSettingSchedulingTitle: 'NotificationSettingSchedulingTitle',
	NotificationSettingSchedulingDescription: 'NotificationSettingSchedulingDescription',
	NotificationSettingBillingTitle: 'NotificationSettingBillingTitle',
	NotificationSettingBillingDescription: 'NotificationSettingBillingDescription',
	NotificationSettingClientDocumentationTitle: 'NotificationSettingClientDocumentationTitle',
	NotificationSettingClientDocumentationDescription: 'NotificationSettingClientDocumentationDescription',
	NotificationSettingWorkspaceTitle: 'NotificationSettingWorkspaceTitle',
	NotificationSettingWorkspaceDescription: 'NotificationSettingWorkspaceDescription',
	NotificationSettingCommunicationsTitle: 'NotificationSettingCommunicationsTitle',
	NotificationSettingCommunicationsDescription: 'NotificationSettingCommunicationsDescription',
	NotificationSettingWhereYouReceiveNotifications: 'NotificationSettingWhereYouReceiveNotifications',
	NotificationSettingUpdateSuccess: 'NotificationSettingUpdateSuccess',
	ContactRelationship: 'ContactRelationship',

	UpdateUnpaidInvoices: 'UpdateUnpaidInvoices',
	DeleteAppointment: 'DeleteAppointment',
	DeleteAppointmentDescription: 'DeleteAppointmentDescription',
	DeleteExternalEventDescription: 'DeleteExternalEventDescription',
	GeneratingNote: 'GeneratingNote',
	TranscriptsPending: 'TranscriptsPending',

	DeleteBillable: 'DeleteBillable',
	DeleteBillableConfirmationMessage: 'DeleteBillableConfirmationMessage',
	SearchBasicBlocks: 'SearchBasicBlocks',
	SearchCommandNotFound: 'SearchCommandNotFound',
	Demo: 'Demo',

	AIPrompts: 'AIPrompts',
	FormStructures: 'FormStructures',
	PageFormat: 'PageFormat',
	Table: 'Table',
	MarkAsManualSubmission: 'MarkAsManualSubmission',
	SubmitElectronically: 'SubmitElectronically',
	IncludesAiSmartPrompt: 'IncludesAiSmartPrompt',

	VideoCallTranscriptionVerbosityField: 'VideoCallTranscriptionVerbosityField',
	VerbosityStandard: 'VerbosityStandard',
	VerbosityDetailed: 'VerbosityDetailed',
	VerbositySuperDetailed: 'VerbositySuperDetailed',
	VerbosityConcise: 'VerbosityConcise',

	PeopleCount: 'PeopleCount',
	CreateANewClient: 'CreateANewClient',
	LinkToAnExistingClient: 'LinkToAnExistingClient',

	DuplicateRecords: 'DuplicateRecords',
	ClientDuplicatesPageMergeHeader: 'ClientDuplicatesPageMergeHeader',
	ClientInformation: 'ClientInformation',
	ReferralInformation: 'ReferralInformation',
	ClaimIncidentInformation: 'ClaimIncidentInformation',
	RenderingProvider: 'RenderingProvider',
	ReferringProvider: 'ReferringProvider',
	ServiceFacility: 'ServiceFacility',
	InsuranceInformation: 'InsuranceInformation',
	BillingInformation: 'BillingInformation',
	MiscellaneousInformation: 'MiscellaneousInformation',
	DiagnosisAndBillingItems: 'DiagnosisAndBillingItems',
	Validating: 'Validating',
	Validated: 'Validated',
	Submitted: 'Submitted',
	Rejected: 'Rejected',
	Denied: 'Denied',
	PartiallyPaid: 'PartiallyPaid',
	NewClaim: 'NewClaim',
	ClientDuplicatesReviewHeader: 'ClientDuplicatesReviewHeader',
	ClientDuplicatesDeviewDescription: 'ClientDuplicatesDeviewDescription',
	PossibleClientDuplicate: 'PossibleClientDuplicate',
	KeepSeparate: 'KeepSeparate',
	Review: 'Review',
	Medicare: 'Medicare',
	Medicaid: 'Medicaid',
	TRICARE: 'TRICARE',
	CHAMPVA: 'CHAMPVA',
	GroupHealthPlan: 'GroupHealthPlan',
	FECABlackLung: 'FECABlackLung',
	GoToNotificationSettings: 'GoToNotificationSettings',
	Unread: 'Unread',
	Read: 'Read',
	TranscribingIn: 'TranscribingIn',
	DictatingIn: 'DictatingIn',
	CapturingAudio: 'CapturingAudio',
	DoYouWantToEndSession: 'DoYouWantToEndSession',
	InvalidDate: 'InvalidDate',
	AppNotificationsIgnoredCount: 'AppNotificationsIgnoredCount',
	IgnoreNotification: 'IgnoreNotification',
	NotificationIgnoredMessage: 'NotificationIgnoredMessage',
	NotificationRestoredMessage: 'NotificationRestoredMessage',
	AllNotificationsRestoredMessage: 'AllNotificationsRestoredMessage',
	RestoreAll: 'RestoreAll',
	ClaimSubtitle: 'ClaimSubtitle',
	InsuranceClaimNotFound: 'InsuranceClaimNotFound',
	CreateNewClaim: 'CreateNewClaim',
	AppNotificationsClearanceHeading: 'AppNotificationsClearanceHeading',
	ClaimSexSelectorPlaceholder: 'ClaimSexSelectorPlaceholder',
	EditorWithSlashCommandPlaceholder: 'EditorWithSlashCommandPlaceholder',
	Claim: 'Claim',
	ConfirmDeleteAccountDescription: 'ConfirmDeleteAccountDescription',
	DeleteAccountPanelInfoAlert: 'DeleteAccountPanelInfoAlert',
	DeleteAccountTitle: 'DeleteAccountTitle',
	DeleteAccountDescription: 'DeleteAccountDescription',
	DeleteAccountButton: 'DeleteAccountButton',
	NoActivePolicies: 'NoActivePolicies',
	NoArchivedPolicies: 'NoArchivedPolicies',
	ClaimServiceFacility: 'ClaimServiceFacility',
	ClaimServiceFacilityPlaceholder: 'ClaimServiceFacilityPlaceholder',
	ClaimPlaceOfService: 'ClaimPlaceOfService',
	ClaimPlaceOfServicePlaceholder: 'ClaimPlaceOfServicePlaceholder',
	ClaimOtherIdQualifier: 'ClaimOtherIdQualifier',
	ClaimOtherIdQualifierPlaceholder: 'ClaimOtherIdQualifierPlaceholder',
	ClaimOtherId: 'ClaimOtherId',
	ClaimOtherIdPlaceholder: 'ClaimOtherIdPlaceholder',
	Pharmacy: 'Pharmacy',
	TelehealthProvidedOtherThanInPatientCare: 'TelehealthProvidedOtherThanInPatientCare',
	School: 'School',
	HomelessShelter: 'HomelessShelter',
	IndianHealthServiceFreeStandingFacility: 'IndianHealthServiceFreeStandingFacility',
	IndianHealthServiceProviderFacility: 'IndianHealthServiceProviderFacility',
	StateLicenseNumber: 'StateLicenseNumber',
	BlueShieldProviderNumber: 'BlueShieldProviderNumber',
	MedicareProviderNumber: 'MedicareProviderNumber',
	MedicaidProviderNumber: 'MedicaidProviderNumber',
	ProviderUPINNumber: 'ProviderUPINNumber',
	CHAMPUSIdentificationNumber: 'CHAMPUSIdentificationNumber',
	EmployeesIdentificationNumber: 'EmployeesIdentificationNumber',
	ProviderCommercialNumber: 'ProviderCommercialNumber',
	LocationNumber: 'LocationNumber',
	ProviderPlanNetworkIdentificationNumber: 'ProviderPlanNetworkIdentificationNumber',
	SocialSecurityNumber: 'SocialSecurityNumber',
	StateIndustrialAccidentProviderNumber: 'StateIndustrialAccidentProviderNumber',
	ProviderTaxonomy: 'ProviderTaxonomy',
	InitialTreatment: 'InitialTreatment',
	LatestVisitOrConsultation: 'LatestVisitOrConsultation',
	AcuteManifestationOfAChronicCondition: 'AcuteManifestationOfAChronicCondition',
	Accident: 'Accident',
	LastXRay: 'LastXRay',
	Prescription: 'Prescription',
	OnsetOfCurrentSymptomsOrIllness: 'OnsetOfCurrentSymptomsOrIllness',
	LastMenstrualPeriod: 'LastMenstrualPeriod',
	GuideTo: 'GuideTo',
	NumberOfErrors: 'NumberOfErrors',
	NumberOfDataEntriesFound: 'NumberOfDataEntriesFound',
	CreatedAt: 'CreatedAt',
	RemoveMfaConfirmationTitle: 'RemoveMfaConfirmationTitle',
	RemoveMfaConfirmationDescription: 'RemoveMfaConfirmationDescription',
	MfaPanelTitle: 'MfaPanelTitle',
	MfaPanelDescription: 'MfaPanelDescription',
	MfaPanelNotAuthorizedError: 'MfaPanelNotAuthorizedError',
	MfaPanelRecommendationTitle: 'MfaPanelRecommendationTitle',
	MfaPanelRecommendationDescription: 'MfaPanelRecommendationDescription',
	SetUpMfaButton: 'SetUpMfaButton',
	MfaHasBeenSetUpText: 'MfaHasBeenSetUpText',
	MfaAvailabilityDisclaimer: 'MfaAvailabilityDisclaimer',
	MfaPanelVerifyEmailFirstAlert: 'MfaPanelVerifyEmailFirstAlert',
	MfaRemovedSnackbarTitle: 'MfaRemovedSnackbarTitle',
	TotpSetUpSuccess: 'TotpSetUpSuccess',
	TotpSetUpModalTitle: 'TotpSetUpModalTitle',
	TotpSetUpModalDescription: 'TotpSetUpModalDescription',
	TotpSetUpManualEntryInstruction: 'TotpSetUpManualEntryInstruction',
	TotpSetupEnterAuthenticatorCodeInstruction: 'TotpSetupEnterAuthenticatorCodeInstruction',
	AuthenticationCode: 'AuthenticationCode',
	BrowseOrDragFileHere: 'BrowseOrDragFileHere',
	BrowseOrDragFileHereDescription: 'BrowseOrDragFileHereDescription',
	ClearAll: 'ClearAll',
	AddressNumberStreet: 'AddressNumberStreet',
	ClaimFieldClient: 'ClaimFieldClient',
	ClaimFieldClientSubtitle: 'ClaimFieldClientSubtitle',
	ClaimFieldClientDescription: 'ClaimFieldClientDescription',
	ClaimFieldClientSexDescription: 'ClaimFieldClientSexDescription',
	ClaimFieldClientDateOfBirth: 'ClaimFieldClientDateOfBirth',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'ClaimFieldClientDateOfBirthAndSexSubtitle',
	ClaimFieldClientDateOfBirthDescription: 'ClaimFieldClientDateOfBirthDescription',
	ClaimFieldClientAddress: 'ClaimFieldClientAddress',
	ClaimFieldClientAddressSubtitle: 'ClaimFieldClientAddressSubtitle',
	ClaimFieldClientAddressDescription: 'ClaimFieldClientAddressDescription',
	DuplicateRecordsMinimumError: 'DuplicateRecordsMinimumError',
	DuplicateRecordsRequired: 'DuplicateRecordsRequired',
	GoToClientList: 'GoToClientList',
	Grid: 'Grid',
	Agenda: 'Agenda',
	ClaimServiceFacilityLocationHelpLabel: 'ClaimServiceFacilityLocationHelpLabel',
	ClaimServiceFacilityLocationHelpTitle: 'ClaimServiceFacilityLocationHelpTitle',
	ClaimServiceFacilityLocationHelpSubtitle: 'ClaimServiceFacilityLocationHelpSubtitle',
	ClaimServiceFacilityLocationHelpContent: 'ClaimServiceFacilityLocationHelpContent',
	ClaimIncidentConditionRelatedTo: 'ClaimIncidentConditionRelatedTo',
	ClaimIncidentConditionRelatedToHelpTitle: 'ClaimIncidentConditionRelatedToHelpTitle',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'ClaimIncidentConditionRelatedToHelpSubtitle',
	ClaimIncidentConditionRelatedToHelpContent: 'ClaimIncidentConditionRelatedToHelpContent',
	ClaimIncidentEmploymentRelated: 'ClaimIncidentEmploymentRelated',
	ClaimIncidentEmploymentRelatedDesc: 'ClaimIncidentEmploymentRelatedDesc',
	ClaimIncidentAutoAccident: 'ClaimIncidentAutoAccident',
	ClaimIncidentOtherAccident: 'ClaimIncidentOtherAccident',
	ClaimIncidentCurrentIllness: 'ClaimIncidentCurrentIllness',
	ClaimIncidentCurrentIllnessHelpTitle: 'ClaimIncidentCurrentIllnessHelpTitle',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'ClaimIncidentCurrentIllnessHelpSubtitle',
	ClaimIncidentCurrentIllnessHelpContent: 'ClaimIncidentCurrentIllnessHelpContent',
	ClaimIncidentOtherAssociatedDate: 'ClaimIncidentOtherAssociatedDate',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'ClaimIncidentOtherAssociatedDateHelpTitle',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'ClaimIncidentOtherAssociatedDateHelpSubtitle',
	ClaimIncidentOtherAssociatedDateHelpContent: 'ClaimIncidentOtherAssociatedDateHelpContent',
	ClaimIncidentDate: 'ClaimIncidentDate',
	ClaimIncidentDateFrom: 'ClaimIncidentDateFrom',
	ClaimIncidentDateTo: 'ClaimIncidentDateTo',
	ClaimIncidentQualifier: 'ClaimIncidentQualifier',
	ClaimIncidentQualifierPlaceholder: 'ClaimIncidentQualifierPlaceholder',
	ClaimIncidentUnableToWorkDatesLabel: 'ClaimIncidentUnableToWorkDatesLabel',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'ClaimIncidentUnableToWorkDatesLabelHelpTitle',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'ClaimIncidentUnableToWorkDatesLabelHelpSubtitle',
	ClaimIncidentUnableToWorkDatesLabelHelpContent: 'ClaimIncidentUnableToWorkDatesLabelHelpContent',
	ClaimIncidentHospitalizationDatesLabel: 'ClaimIncidentHospitalizationDatesLabel',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'ClaimIncidentHospitalizationDatesLabelHelpTitle',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'ClaimIncidentHospitalizationDatesLabelHelpSubtitle',
	ClaimIncidentHospitalizationDatesLabelHelpContent: 'ClaimIncidentHospitalizationDatesLabelHelpContent',
	ClaimMiscClaimCodes: 'ClaimMiscClaimCodes',
	ClaimMiscAdditionalClaimInformation: 'ClaimMiscAdditionalClaimInformation',
	ClaimMiscResubmissionCode: 'ClaimMiscResubmissionCode',
	ClaimMiscResubmissionCodeHelpTitle: 'ClaimMiscResubmissionCodeHelpTitle',
	ClaimMiscResubmissionCodeHelpSubtitle: 'ClaimMiscResubmissionCodeHelpSubtitle',
	ClaimMiscResubmissionCodeHelpContent: 'ClaimMiscResubmissionCodeHelpContent',
	ClaimMiscOriginalReferenceNumber: 'ClaimMiscOriginalReferenceNumber',
	ClaimMiscPatientsAccountNumber: 'ClaimMiscPatientsAccountNumber',
	ClaimMiscPatientsAccountNumberHelpTitle: 'ClaimMiscPatientsAccountNumberHelpTitle',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'ClaimMiscPatientsAccountNumberHelpSubtitle',
	ClaimMiscPatientsAccountNumberHelpContent: 'ClaimMiscPatientsAccountNumberHelpContent',
	ClaimMiscPriorAuthorizationNumber: 'ClaimMiscPriorAuthorizationNumber',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'ClaimMiscPriorAuthorizationNumberHelpTitle',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'ClaimMiscPriorAuthorizationNumberHelpSubtitle',
	ClaimMiscPriorAuthorizationNumberHelpContent: 'ClaimMiscPriorAuthorizationNumberHelpContent',
	InsuranceClaim: 'InsuranceClaim',

	NewClientNextStepsModalTitle: 'NewClientNextStepsModalTitle',
	NewClientNextStepsModalDescription: 'NewClientNextStepsModalDescription',
	NewClientNextStepsModalAddAnotherClient: 'NewClientNextStepsModalAddAnotherClient',
	NewClientNextStepsModalCompleteBasicInformation: 'NewClientNextStepsModalCompleteBasicInformation',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'NewClientNextStepsModalCompleteBasicInformationDescription',
	NewClientNextStepsModalSendIntake: 'NewClientNextStepsModalSendIntake',
	NewClientNextStepsModalSendIntakeDescription: 'NewClientNextStepsModalSendIntakeDescription',
	NewClientNextStepsModalBookAppointment: 'NewClientNextStepsModalBookAppointment',
	NewClientNextStepsModalBookAppointmentDescription: 'NewClientNextStepsModalBookAppointmentDescription',
	NewClientNextStepsModalCreateNote: 'NewClientNextStepsModalCreateNote',
	NewClientNextStepsModalCreateNoteDescription: 'NewClientNextStepsModalCreateNoteDescription',
	NewClientNextStepsModalSendMessage: 'NewClientNextStepsModalSendMessage',
	NewClientNextStepsModalSendMessageDescription: 'NewClientNextStepsModalSendMessageDescription',
	NewClientNextStepsModalCreateInvoice: 'NewClientNextStepsModalCreateInvoice',
	NewClientNextStepsModalCreateInvoiceDescription: 'NewClientNextStepsModalCreateInvoiceDescription',

	NewContactNextStepsModalTitle: 'NewContactNextStepsModalTitle',
	NewContactNextStepsModalDescription: 'NewContactNextStepsModalDescription',
	NewContactNextStepsModalCompleteProfile: 'NewContactNextStepsModalCompleteProfile',
	NewContactNextStepsModalCompleteProfileDescription: 'NewContactNextStepsModalCompleteProfileDescription',
	NewContactNextStepsModalInviteToPortal: 'NewContactNextStepsModalInviteToPortal',
	NewContactNextStepsModalInviteToPortalDescription: 'NewContactNextStepsModalInviteToPortalDescription',
	NewContactNextStepsModalBookAppointment: 'NewContactNextStepsModalBookAppointment',
	NewContactNextStepsModalBookAppointmentDescription: 'NewContactNextStepsModalBookAppointmentDescription',
	NewContactNextStepsModalCreateNote: 'NewContactNextStepsModalCreateNote',
	NewContactNextStepsModalCreateNoteDescription: 'NewContactNextStepsModalCreateNoteDescription',
	NewContactNextStepsModalAddRelationship: 'NewContactNextStepsModalAddRelationship',
	NewContactNextStepsModalAddRelationshipDescription: 'NewContactNextStepsModalAddRelationshipDescription',

	NewTeamMemberNextStepsModalTitle: 'NewTeamMemberNextStepsModalTitle',
	NewTeamMemberNextStepsModalDescription: 'NewTeamMemberNextStepsModalDescription',
	NewTeamMemberNextStepsModalCompleteProfile: 'NewTeamMemberNextStepsModalCompleteProfile',
	NewTeamMemberNextStepsModalCompleteProfileDescription: 'NewTeamMemberNextStepsModalCompleteProfileDescription',
	NewTeamMemberNextStepsModalEditPermissions: 'NewTeamMemberNextStepsModalEditPermissions',
	NewTeamMemberNextStepsModalEditPermissionsDescription: 'NewTeamMemberNextStepsModalEditPermissionsDescription',
	NewTeamMemberNextStepsModalSetAvailability: 'NewTeamMemberNextStepsModalSetAvailability',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'NewTeamMemberNextStepsModalSetAvailabilityDescription',
	NewTeamMemberNextStepsModalAssignClients: 'NewTeamMemberNextStepsModalAssignClients',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'NewTeamMemberNextStepsModalAssignClientsDescription',
	NewTeamMemberNextStepsModalAssignServices: 'NewTeamMemberNextStepsModalAssignServices',
	NewTeamMemberNextStepsModalAssignServicesDescription: 'NewTeamMemberNextStepsModalAssignServicesDescription',
	NewTeamMemberNextStepsModalBookAppointment: 'NewTeamMemberNextStepsModalBookAppointment',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'NewTeamMemberNextStepsModalBookAppointmentDescription',

	ClientMergeResultSummaryTitle: 'ClientMergeResultSummaryTitle',
	ClientMergeResultSummaryBanner: 'ClientMergeResultSummaryBanner',
	ShowMergeSummarySidebar: 'ShowMergeSummarySidebar',
	HideMergeSummarySidebar: 'HideMergeSummarySidebar',
	SeparateDuplicateClientsDescription: 'SeparateDuplicateClientsDescription',
	ConfirmKeepSeparate: 'ConfirmKeepSeparate',
	KeepSeparateSuccessMessage: 'KeepSeparateSuccessMessage',
	ImportGuide: 'ImportGuide',
	ExportGuide: 'ExportGuide',
	TaxIdType: 'TaxIdType',
	ChooseBillingProfile: 'ChooseBillingProfile',
	ChooseTaxName: 'ChooseTaxName',
	ChooseAnOption: 'ChooseAnOption',
	OrganisationOrIndividual: 'OrganisationOrIndividual',
	OtherIdQualifier: 'OtherIdQualifier',
	OtherId: 'OtherId',
	ClaimBillingProfileTypeOrganisation: 'ClaimBillingProfileTypeOrganisation',
	ClaimBillingProfileTypeIndividual: 'ClaimBillingProfileTypeIndividual',
	CalendarDetails: 'CalendarDetails',
	CalendarDetailsDescription: 'CalendarDetailsDescription',
	CalendarSettings: 'CalendarSettings',
	GoToApps: 'GoToApps',
	ConnectedAppSyncDescription: 'ConnectedAppSyncDescription',
	TimezoneDisplay: 'TimezoneDisplay',
	TimezoneDisplayDescription: 'TimezoneDisplayDescription',
	TimeslotSize: 'TimeslotSize',
	TimeIncrement: 'TimeIncrement',
	ShowIcons: 'ShowIcons',
	ClientNameFormat: 'ClientNameFormat',
	Label: 'Label',
	Small: 'Small',
	Medium: 'Medium',
	Large: 'Large',
	ExtraLarge: 'ExtraLarge',
	ValueMinutes: 'ValueMinutes',
	ClientId: 'ClientId',
	FirstNameLastInitial: 'FirstNameLastInitial',
	Initials: 'Initials',
	LastNameFirstInitial: 'LastNameFirstInitial',
	Referrer: 'Referrer',
	ClaimIncludeReferrerInformation: 'ClaimIncludeReferrerInformation',
	ClaimReferringProviderHelpTitle: 'ClaimReferringProviderHelpTitle',
	ClaimReferringProviderHelpSubtitle: 'ClaimReferringProviderHelpSubtitle',
	ClaimReferringProviderHelpContent: 'ClaimReferringProviderHelpContent',
	ClaimReferringProviderQualifier: 'ClaimReferringProviderQualifier',
	ClaimReferringProviderQualifierPlaceholder: 'ClaimReferringProviderQualifierPlaceholder',
	ClaimDoIncludeReferrerInformation: 'ClaimDoIncludeReferrerInformation',
	ClaimReferringProvider: 'ClaimReferringProvider',
	ClaimOrderingProvider: 'ClaimOrderingProvider',
	ClaimSupervisingProvider: 'ClaimSupervisingProvider',
	ClaimPolicyInformation: 'ClaimPolicyInformation',
	ClaimPolicyHolderRelationship: 'ClaimPolicyHolderRelationship',
	ClaimClientInsurancePolicies: 'ClaimClientInsurancePolicies',
	ClainInsuranceTelephone: 'ClainInsuranceTelephone',
	ClaimPolicyTelephone: 'ClaimPolicyTelephone',
	ClaimInsurancePayer: 'ClaimInsurancePayer',
	MiddleName: 'MiddleName',
	ClaimInsuranceMemberIdHelpTitle: 'ClaimInsuranceMemberIdHelpTitle',
	ClaimInsuranceMemberIdHelpSubtitle: 'ClaimInsuranceMemberIdHelpSubtitle',
	ClaimInsuranceMemberIdHelpContent: 'ClaimInsuranceMemberIdHelpContent',
	ClaimInsuranceGroupIdHelpTitle: 'ClaimInsuranceGroupIdHelpTitle',
	ClaimInsuranceGroupIdHelpSubtitle: 'ClaimInsuranceGroupIdHelpSubtitle',
	ClaimInsuranceGroupIdHelpContent: 'ClaimInsuranceGroupIdHelpContent',
	ClaimInsuranceCoverageTypeHelpTitle: 'ClaimInsuranceCoverageTypeHelpTitle',
	ClaimInsuranceCoverageTypeHelpSubtitle: 'ClaimInsuranceCoverageTypeHelpSubtitle',
	ClaimInsuranceCoverageTypeHelpContent: 'ClaimInsuranceCoverageTypeHelpContent',
	CreateNewAppointment: 'CreateNewAppointment',
	ComposeSms: 'ComposeSms',
	ClaimRenderingProviderOrTeamMember: 'ClaimRenderingProviderOrTeamMember',
	ClaimAddRenderingProvider: 'ClaimAddRenderingProvider',
	ClaimAddReferringProvider: 'ClaimAddReferringProvider',
	ClaimReferringProviderEmpty: 'ClaimReferringProviderEmpty',
	ClaimRenderingProviderIdNumber: 'ClaimRenderingProviderIdNumber',
	ClaimServiceLinesEmptyError: 'ClaimServiceLinesEmptyError',
	ClaimDiagnosticCodesEmptyError: 'ClaimDiagnosticCodesEmptyError',
	ClaimChooseRenderingProviderOrTeamMember: 'ClaimChooseRenderingProviderOrTeamMember',
	Diagnosis: 'Diagnosis',
	NewDiagnosis: 'NewDiagnosis',
	ClaimDiagnosisCodeSelectorPlaceholder: 'ClaimDiagnosisCodeSelectorPlaceholder',
	ClaimDiagnosisSelectorHelpTitle: 'ClaimDiagnosisSelectorHelpTitle',
	ClaimDiagnosisSelectorHelpSubtitle: 'ClaimDiagnosisSelectorHelpSubtitle',
	ClaimDiagnosisSelectorHelpContent: 'ClaimDiagnosisSelectorHelpContent',
	OnsetDate: 'OnsetDate',
	BillingItems: 'BillingItems',
	Fee: 'Fee',
	EPSDT: 'EPSDT',
	FamilyPlanningService: 'FamilyPlanningService',
	Modifiers: 'Modifiers',
	POSPlaceholder: 'POSPlaceholder',
	ServicesPlaceholder: 'ServicesPlaceholder',
	CodePlaceholder: 'CodePlaceholder',
	UnitsPlaceholder: 'UnitsPlaceholder',
	DXCodePlaceholder: 'DXCodePlaceholder',
	ModifiersPlaceholder: 'ModifiersPlaceholder',
	EPSDTPlaceholder: 'EPSDTPlaceholder',
	AmountPaid: 'AmountPaid',
	ClaimAmountPaidHelpTitle: 'ClaimAmountPaidHelpTitle',
	ClaimAmountPaidHelpSubtitle: 'ClaimAmountPaidHelpSubtitle',
	ClaimAmountPaidHelpContent: 'ClaimAmountPaidHelpContent',
	BillingBillablesTab: 'BillingBillablesTab',
	BillingInvoicesTab: 'BillingInvoicesTab',
	BillingClaimsTab: 'BillingClaimsTab',
	BillingPaymentsTab: 'BillingPaymentsTab',
	BillingSuperbillsTab: 'BillingSuperbillsTab',
	MergeClientsSuccess: 'MergeClientsSuccess',
	MfaEmailOtpSentSnackbar: 'MfaEmailOtpSentSnackbar',
	MfaEmailOtpSendFailureSnackbar: 'MfaEmailOtpSendFailureSnackbar',
	MfaEmailOtpVerificationFailedSnackbar: 'MfaEmailOtpVerificationFailedSnackbar',
	MfaDeviceLostPanelTitle: 'MfaDeviceLostPanelTitle',
	MfaDeviceLostPanelDescription: 'MfaDeviceLostPanelDescription',
	MfaDidntReceiveEmailCode: 'MfaDidntReceiveEmailCode',
	MfaSendEmailCode: 'MfaSendEmailCode',
	MfaVerifyYourIdentityPanelTitle: 'MfaVerifyYourIdentityPanelTitle',
	MfaVerifyYourIdentityPanelDescription: 'MfaVerifyYourIdentityPanelDescription',
	MfaVerifyIdentityLostDeviceButton: 'MfaVerifyIdentityLostDeviceButton',
	MfaRecommendationBannerTitle: 'MfaRecommendationBannerTitle',
	MfaRecommendationBannerDescription: 'MfaRecommendationBannerDescription',
	MfaRecommendationBannerPrimaryAction: 'MfaRecommendationBannerPrimaryAction',
	MergeClientRecords: 'MergeClientRecords',
	MergeClientRecordsDescription: 'MergeClientRecordsDescription',
	MergeClientRecordsDescription2: 'MergeClientRecordsDescription2',
	MergeClientRecordsItem1: 'MergeClientRecordsItem1',
	MergeClientRecordsItem2: 'MergeClientRecordsItem2',
	MergeClientRecordsItem3: 'MergeClientRecordsItem3',
	MergeClientRecordsItem4: 'MergeClientRecordsItem4',
	ConfirmMerge: 'ConfirmMerge',

	Popular: 'Popular',
	PricePerUser: 'PricePerUser',
	PricePerUserBilledAnnually: 'PricePerUserBilledAnnually',
	PricingGuide: 'PricingGuide',

	FreeSubscriptionPlanTitle: 'FreeSubscriptionPlanTitle',
	FreeSubscriptionPlanSubtitle: 'FreeSubscriptionPlanSubtitle',
	FreePlanInclusionHeader: 'FreePlanInclusionHeader',
	FreePlanInclusionOne: 'FreePlanInclusionOne',
	FreePlanInclusionTwo: 'FreePlanInclusionTwo',
	FreePlanInclusionThree: 'FreePlanInclusionThree',
	FreePlanInclusionFour: 'FreePlanInclusionFour',
	FreePlanInclusionFive: 'FreePlanInclusionFive',
	FreePlanInclusionSix: 'FreePlanInclusionSix',

	EssentialSubscriptionPlanTitle: 'EssentialSubscriptionPlanTitle',
	EssentialSubscriptionPlanSubtitle: 'EssentialSubscriptionPlanSubtitle',
	EssentialPlanInclusionHeader: 'EssentialPlanInclusionHeader',
	EssentialPlanInclusionOne: 'EssentialPlanInclusionOne',
	EssentialPlanInclusionTwo: 'EssentialPlanInclusionTwo',
	EssentialPlanInclusionThree: 'EssentialPlanInclusionThree',
	EssentialPlanInclusionFour: 'EssentialPlanInclusionFour',
	EssentialPlanInclusionFive: 'EssentialPlanInclusionFive',
	EssentialPlanInclusionSix: 'EssentialPlanInclusionSix',

	PlusSubscriptionPlanTitle: 'PlusSubscriptionPlanTitle',
	PlusSubscriptionPlanSubtitle: 'PlusSubscriptionPlanSubtitle',
	PlusPlanInclusionHeader: 'PlusPlanInclusionHeader',
	PlusPlanInclusionOne: 'PlusPlanInclusionOne',
	PlusPlanInclusionTwo: 'PlusPlanInclusionTwo',
	PlusPlanInclusionThree: 'PlusPlanInclusionThree',
	PlusPlanInclusionFour: 'PlusPlanInclusionFour',
	PlusPlanInclusionFive: 'PlusPlanInclusionFive',
	PlusPlanInclusionSix: 'PlusPlanInclusionSix',

	AdvancedSubscriptionPlanTitle: 'AdvancedSubscriptionPlanTitle',
	AdvancedSubscriptionPlanSubtitle: 'AdvancedSubscriptionPlanSubtitle',
	AdvancedPlanInclusionHeader: 'AdvancedPlanInclusionHeader',
	AdvancedPlanInclusionOne: 'AdvancedPlanInclusionOne',
	AdvancedPlanInclusionTwo: 'AdvancedPlanInclusionTwo',
	AdvancedPlanInclusionThree: 'AdvancedPlanInclusionThree',
	AdvancedPlanInclusionFour: 'AdvancedPlanInclusionFour',
	AdvancedPlanInclusionFive: 'AdvancedPlanInclusionFive',
	AdvancedPlanInclusionSix: 'AdvancedPlanInclusionSix',

	Unallocated: 'Unallocated',
	PaymentsEmptyStateDescription: 'PaymentsEmptyStateDescription',
	Brands: 'Brands',
	ClaimServiceLineServiceHelpTitle: 'ClaimServiceLineServiceHelpTitle',
	ClaimServiceLineServiceHelpSubtitle: 'ClaimServiceLineServiceHelpSubtitle',
	ClaimServiceLineServiceHelpContent: 'ClaimServiceLineServiceHelpContent',
	Invalid: 'Invalid',
	SuperbillsEmptyStateDescription: 'SuperbillsEmptyStateDescription',
	InvoicesEmptyStateDescription: 'InvoicesEmptyStateDescription',
	BillingSearchPlaceholder: 'BillingSearchPlaceholder',
	SmartSuggestNewClient: 'SmartSuggestNewClient',

	// region Notifications subjects
	ClientUpdatedNoteNotificationSubject: 'ClientUpdatedNoteNotificationSubject',
	ClientAddedNoteNotificationSubject: 'ClientAddedNoteNotificationSubject',
	AppointmentAssignedNotificationSubject: 'AppointmentAssignedNotificationSubject',
	ClientAppointmentBookedNotificationSubject: 'ClientAppointmentBookedNotificationSubject',
	AppointmentCancelledNotificationSubject: 'AppointmentCancelledNotificationSubject',
	AppointmentConfirmedNotificationSubject: 'AppointmentConfirmedNotificationSubject',
	AppointmentRescheduledNotificationSubject: 'AppointmentRescheduledNotificationSubject',
	FileUploadedNotificationSubject: 'FileUploadedNotificationSubject',
	ExportContactFailedNotificationSubject: 'ExportContactFailedNotificationSubject',
	ImportContactFailedNotificationSubject: 'ImportContactFailedNotificationSubject',
	InvoicePaidNotificationSubject: 'InvoicePaidNotificationSubject',
	NoteFormSubmittedNotificationSubject: 'NoteFormSubmittedNotificationSubject',
	PublicFormSubmittedNotificationSubject: 'PublicFormSubmittedNotificationSubject',
	TemplateImportCompletedNotificationSubject: 'TemplateImportCompletedNotificationSubject',
	TemplateImportFailedNotificationSubject: 'TemplateImportFailedNotificationSubject',
	ReferralCreditedNotificationSubject: 'ReferralCreditedNotificationSubject',
	ReferralJoinedNotificationSubject: 'ReferralJoinedNotificationSubject',
	SubscriptionPaymentFailedNotificationSubject: 'SubscriptionPaymentFailedNotificationSubject',
	PaymentsAccountDisabledNotificationSubject: 'PaymentsAccountDisabledNotificationSubject',
	ServiceReceiptRequiresReviewNotificationSubject: 'ServiceReceiptRequiresReviewNotificationSubject',
	ConnectedAppDisconnectedNotificationSubject: 'ConnectedAppDisconnectedNotificationSubject',
	StaffContactAssignedSubject: 'StaffContactAssignedSubject',
	StaffInboxAssignedNotificationSubject: 'StaffInboxAssignedNotificationSubject',
	StaffInboxUnassignedNotificationSubject: 'StaffInboxUnassignedNotificationSubject',
	CallAttendeeJoinAttemptedNotificationSubject: 'CallAttendeeJoinAttemptedNotificationSubject',
	// endregion Notifications subjects

	Validate: 'Validate',
	RevertToDraft: 'RevertToDraft',
	Resubmit: 'Resubmit',
	LinkClientModalTitle: 'LinkClientModalTitle',
	LinkClientFormSearchClientLabel: 'LinkClientFormSearchClientLabel',
	AddToExisting: 'AddToExisting',
	LinkClientSuccessTitle: 'LinkClientSuccessTitle',
	LinkClientSuccessDescription: 'LinkClientSuccessDescription',

	Unclaimed: 'Unclaimed',
	MuteAudio: 'MuteAudio',
	UnmuteAudio: 'UnmuteAudio',
	RemoveFromCall: 'RemoveFromCall',
	RemoveFromCallDescription: 'RemoveFromCallDescription',
	AttendeeBeingMutedTooltip: 'AttendeeBeingMutedTooltip',
	MuteEveryone: 'MuteEveryone',
	UnmuteEveryone: 'UnmuteEveryone',

	********************************: '********************************',

	PageBlockerTitle: 'PageBlockerTitle',
	PageBlockerDescription: 'PageBlockerDescription',
	Proceed: 'Proceed',

	PaymentMethodRequired: 'PaymentMethodRequired',
	CurrentPlan: 'CurrentPlan',
	ClaimsEmptyStateDescription: 'ClaimsEmptyStateDescription',
	CallWithPractitioner: 'CallWithPractitioner',
	YouHaveOngoingTranscription: 'YouHaveOngoingTranscription',
	ConfirmationModalCloseOnGoingTranscription: 'ConfirmationModalCloseOnGoingTranscription',
	ClaimServiceLabChargesHelpTitle: 'ClaimServiceLabChargesHelpTitle',
	ClaimServiceLabChargesHelpSubtitle: 'ClaimServiceLabChargesHelpSubtitle',
	ClaimServiceLabChargesHelpContent: 'ClaimServiceLabChargesHelpContent',
	ClaimServiceSupplementaryInfoHelpTitle: 'ClaimServiceSupplementaryInfoHelpTitle',
	ClaimServiceSupplementaryInfoHelpSubtitle: 'ClaimServiceSupplementaryInfoHelpSubtitle',
	ClaimServiceSupplementaryInfoHelpContent: 'ClaimServiceSupplementaryInfoHelpContent',
	ClaimSupplementaryInfo: 'ClaimSupplementaryInfo',
	ClaimSupplementaryInfoPlaceholder: 'ClaimSupplementaryInfoPlaceholder',
	OutsideLabCharges: 'OutsideLabCharges',
	EMG: 'EMG',
	AiPoweredTemplates: 'AiPoweredTemplates',
	NonAiTemplates: 'NonAiTemplates',
	AiTemplates: 'AiTemplates',
	SmartChips: 'SmartChips',
	Information: 'Information',
	YesEndTranscription: 'YesEndTranscription',

	ShowReactions: 'ShowReactions',
	CloseReactions: 'CloseReactions',
	UpdateSettings: 'UpdateSettings',

	ClaimSettingsTitle: 'ClaimSettingsTitle',
	ClaimSettingsDescription: 'ClaimSettingsDescription',
	ClaimSettingsSelfPayTitle: 'ClaimSettingsSelfPayTitle',
	ClaimSettingsSelfPayDescription: 'ClaimSettingsSelfPayDescription',
	ClaimSettingsInsuranceTitle: 'ClaimSettingsInsuranceTitle',
	ClaimSettingsInsuranceDescription: 'ClaimSettingsInsuranceDescription',
	ClaimSettingsConsentLabel: 'ClaimSettingsConsentLabel',
	ClaimSettingsBillingMethodTitle: 'ClaimSettingsBillingMethodTitle',
	ClaimSettingsClientSignatureTitle: 'ClaimSettingsClientSignatureTitle',
	ClaimSettingsClientSignatureDescription: 'ClaimSettingsClientSignatureDescription',
	ClaimSettingsPolicyHolderSignatureTitle: 'ClaimSettingsPolicyHolderSignatureTitle',
	ClaimSettingsPolicyHolderSignatureDescription: 'ClaimSettingsPolicyHolderSignatureDescription',
	ClaimSettingsNoPoliciesAlertDescription: 'ClaimSettingsNoPoliciesAlertDescription',
	ClaimSettingsHasActivePoliciesAlertDescription: 'ClaimSettingsHasActivePoliciesAlertDescription',
	DefaultNotificationSubject: 'DefaultNotificationSubject',
	InvalidTotpSetupVerificationCode: 'InvalidTotpSetupVerificationCode',
	Append: 'Append',
	GenerateNoteConfirmationModalDescription: 'GenerateNoteConfirmationModalDescription',
	NewNoteCreated: 'NewNoteCreated',
	ExportCms1500: 'ExportCms1500',
	PrintToCms1500: 'PrintToCms1500',

	AskAiSomethingWentWrong: 'AskAiSomethingWentWrong',
	AskAiSuccessfullyInsertedResponse: 'AskAiSuccessfullyInsertedResponse',
	AskAiSuccessfullyReplacedResponse: 'AskAiSuccessfullyReplacedResponse',
	AskAiSuccessfullyCopiedResponse: 'AskAiSuccessfullyCopiedResponse',
	AskAiGreeting: 'AskAiGreeting',
	AskAiOpenPreviousNote: 'AskAiOpenPreviousNote',
	AskAiSummarizeNote: 'AskAiSummarizeNote',
	AskAiSummariseTextIntoBulletPoints: 'AskAiSummariseTextIntoBulletPoints',
	AskAiWriteProfessionalNoteUsingTemplate: 'AskAiWriteProfessionalNoteUsingTemplate',
	AskAiSeeMore: 'AskAiSeeMore',
	AskAiInsert: 'AskAiInsert',
	AskAiReplace: 'AskAiReplace',
	AskAskAiAnything: 'AskAskAiAnything',
	AskAiToday: 'AskAiToday',
	AskAiSuggested: 'AskAiSuggested',
	AskAiThinking: 'AskAiThinking',
	AskAiPondering: 'AskAiPondering',
	AskAiRuminating: 'AskAiRuminating',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiFixSpellingAndGrammar: 'AskAiFixSpellingAndGrammar',
	AskAiSimplifyLanguage: 'AskAiSimplifyLanguage',
	AskAiChangeFormality: 'AskAiChangeFormality',
	AskAiMakeItMoreCasual: 'AskAiMakeItMoreCasual',
	AskAiMoreProfessional: 'AskAiMoreProfessional',
	AskAiGiveItAFriendlyTone: 'AskAiGiveItAFriendlyTone',
	AskAiChangeToneToBeMoreProfessional: 'AskAiChangeToneToBeMoreProfessional',
	AskAiAddFormField: 'AskAiAddFormField',
	AskAiExplainWhatThisImageIsAbout: 'AskAiExplainWhatThisImageIsAbout',
	AskAiMakeThisTextMoreConcise: 'AskAiMakeThisTextMoreConcise',
	AskAiGenerateACaptionForThisImage: 'AskAiGenerateACaptionForThisImage',
	AskAiExplainWhatThisDocumentIsAbout: 'AskAiExplainWhatThisDocumentIsAbout',
	AskAiWhatDoYouWantToDoWithThisForm: 'AskAiWhatDoYouWantToDoWithThisForm',
	AskAiGetStarted: 'AskAiGetStarted',
	AiCreateNewConversation: 'AiCreateNewConversation',
	AiShowConversationHistory: 'AiShowConversationHistory',
	AiWorkBetterWithAi: 'AiWorkBetterWithAi',
	AiEnhanceYourProductivity: 'AiEnhanceYourProductivity',
	AiAssistantAtYourFingertips: 'AiAssistantAtYourFingertips',
	AskAiGenerateFromThisPage: 'AskAiGenerateFromThisPage',
	AskAiReviewOrEditSelection: 'AskAiReviewOrEditSelection',
	AskAiStartWithATemplate: 'AskAiStartWithATemplate',
	AskAiHowCanIHelpWithYourContent: 'AskAiHowCanIHelpWithYourContent',
	AiCopilotDisclaimer: 'AiCopilotDisclaimer',
	AskWriteSearchAnything: 'AskWriteSearchAnything',
	WhatCanIHelpWith: 'WhatCanIHelpWith',
	SectionCannotBeEmpty: 'SectionCannotBeEmpty',
	ExportTextOnly: 'ExportTextOnly',
	ExportPrintWaitMessage: 'ExportPrintWaitMessage',
	ExportPrintCompletedMessage: 'ExportPrintCompletedMessage',
	Discount: 'Discount',
	SelfPay: 'SelfPay',
	BookingAnalyticsIntegrationPanelTitle: 'BookingAnalyticsIntegrationPanelTitle',
	BookingAnalyticsIntegrationPanelDescription: 'BookingAnalyticsIntegrationPanelDescription',
	GoogleTagManagerContainerId: 'GoogleTagManagerContainerId',
	ContainerIdNotSet: 'ContainerIdNotSet',
	CalendarAppSyncPanelTitle: 'CalendarAppSyncPanelTitle',
	CalendarAppSyncFormDescription: 'CalendarAppSyncFormDescription',
	NewConnectedApp: 'NewConnectedApp',
	NoCalendarsSynced: 'NoCalendarsSynced',
	NewPayer: 'NewPayer',
	ClientArchivedSuccessfulSnackbar: 'ClientArchivedSuccessfulSnackbar',
	ClientUnarchivedSuccessfulSnackbar: 'ClientUnarchivedSuccessfulSnackbar',
	AddUnclaimedItems: 'AddUnclaimedItems',
	UnclaimedItems: 'UnclaimedItems',
	AddToClaim: 'AddToClaim',
	YesArchive: 'YesArchive',
	ArchiveClients: 'ArchiveClients',
	BulkArchiveClientsDescription: 'BulkArchiveClientsDescription',
	BulkArchiveSuccess: 'BulkArchiveSuccess',
	BulkUnarchiveSuccess: 'BulkUnarchiveSuccess',
	BulkArchiveUndone: 'BulkArchiveUndone',
	InboxBulkUnreadSuccess: 'InboxBulkUnreadSuccess',
	InboxBulkReadSuccess: 'InboxBulkReadSuccess',
	InboxBulkReopenSuccess: 'InboxBulkReopenSuccess',
	InboxBulkCloseSuccess: 'InboxBulkCloseSuccess',
	InboxBulkDeleteSuccess: 'InboxBulkDeleteSuccess',
	UnclaimedItemsMustBeInCurrency: 'UnclaimedItemsMustBeInCurrency',

	Method: 'Method',
	UnallocatedPaymentTitle: 'UnallocatedPaymentTitle',
	UnallocatedPaymentDescription: 'UnallocatedPaymentDescription',
	OverallocatedPaymentTitle: 'OverallocatedPaymentTitle',
	OverallocatedPaymentDescription: 'OverallocatedPaymentDescription',
	EditPaymentDetails: 'EditPaymentDetails',
	AllocatedItems: 'AllocatedItems',
	Allocated: 'Allocated',
	AllocationTableEmptyState: 'AllocationTableEmptyState',
	RefundsTableEmptyState: 'RefundsTableEmptyState',
	OtherAdjustmentsTableEmptyState: 'OtherAdjustmentsTableEmptyState',
	OtherAdjustments: 'OtherAdjustments',
	ViewDetails: 'ViewDetails',
	Reprocess: 'Reprocess',
	TranscriptionIncompleteNotice: 'TranscriptionIncompleteNotice',
	TranscriptionInProcess: 'TranscriptionInProcess',
	TranscriptionFailedNotice: 'TranscriptionFailedNotice',
	TranscriptionIdleMessage: 'TranscriptionIdleMessage',
	DoesNotRepeat: 'DoesNotRepeat',

	// region WorkflowAutomations
	AiSmartReminders: 'AiSmartReminders',
	UseAiToAutomateYourWorkflow: 'UseAiToAutomateYourWorkflow',
	SearchRemindersAndWorkflows: 'SearchRemindersAndWorkflows',
	EmailAndSms: 'EmailAndSms',
	Automation: 'Automation',
	Automations: 'Automations',
	AutomationAndReminders: 'AutomationAndReminders',
	WorkflowsManagement: 'WorkflowsManagement',
	WorkflowsAndReminders: 'WorkflowsAndReminders',
	Trigger: 'Trigger',
	ChooseTrigger: 'ChooseTrigger',
	NoTriggerConfigured: 'NoTriggerConfigured',
	WhenThisHappens: 'WhenThisHappens',
	Action: 'Action',
	ChooseAction: 'ChooseAction',
	NoActionConfigured: 'NoActionConfigured',
	ReminderDetails: 'ReminderDetails',
	EventName: 'EventName',
	WorkflowDescription: 'WorkflowDescription',
	AppointmentReminder: 'AppointmentReminder',
	NoDescription: 'NoDescription',
	Then: 'Then',
	AddNewAction: 'AddNewAction',
	EventType: 'EventType',
	ChooseEventType: 'ChooseEventType',
	ChooseMethod: 'ChooseMethod',
	AutomationDisable: 'AutomationDisable',
	AutomationEnable: 'AutomationEnable',
	AutomationPublishWarningTooltip: 'AutomationPublishWarningTooltip',
	AutomationPublishSuccessMessage: 'AutomationPublishSuccessMessage',
	AutomationDeletedSuccessMessage: 'AutomationDeletedSuccessMessage',
	ConfirmDeleteActionDescription: 'ConfirmDeleteActionDescription',
	ConfirmDeleteAutomationDescription: 'ConfirmDeleteAutomationDescription',
	AutomationTriggerEventCancelledLabel: 'AutomationTriggerEventCancelledLabel',
	AutomationTriggerEventCancelledDescription: 'AutomationTriggerEventCancelledDescription',
	AutomationTriggerEventEndedLabel: 'AutomationTriggerEventEndedLabel',
	AutomationTriggerEventEndedDescription: 'AutomationTriggerEventEndedDescription',
	AutomationTriggerEventStartsLabel: 'AutomationTriggerEventStartsLabel',
	AutomationTriggerEventStartsDescription: 'AutomationTriggerEventStartsDescription',
	AutomationTriggerEventCreatedLabel: 'AutomationTriggerEventCreatedLabel',
	AutomationTriggerEventCreatedDescription: 'AutomationTriggerEventCreatedDescription',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'AutomationTriggerEventCreatedOrUpdatedLabel',
	AutomationTriggerEventCreatedOrUpdatedDescription: 'AutomationTriggerEventCreatedOrUpdatedDescription',
	AutomationActionSendEmailLabel: 'AutomationActionSendEmailLabel',
	AutomationActionSendSMSLabel: 'AutomationActionSendSMSLabel',
	AutomationParams_timeValue: 'AutomationParams_timeValue',
	AutomationParams_timeUnit: 'AutomationParams_timeUnit',
	AutomationParams_timeTrigger: 'AutomationParams_timeTrigger',
	// endregion WorkflowAutomations

	// region WorkflowTemplates
	WorkflowTemplateTemplateEditor: 'WorkflowTemplateTemplateEditor',
	WorkflowTemplateEditorDetailsTab: 'WorkflowTemplateEditorDetailsTab',
	WorkflowTemplateEditorEmailContentTab: 'WorkflowTemplateEditorEmailContentTab',
	WorkflowTemplateEditorThemeTab: 'WorkflowTemplateEditorThemeTab',
	WorkflowTemplateEditorEmailContent: 'WorkflowTemplateEditorEmailContent',
	WorkflowTemplateTemplateDetailsPanelDescription: 'WorkflowTemplateTemplateDetailsPanelDescription',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'WorkflowTemplateAutomatedWorkflowsPanelTitle',
	WorkflowTemplateAutomatedWorkflowsPanelDescription: 'WorkflowTemplateAutomatedWorkflowsPanelDescription',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription',
	WorkflowTemplatePreviewerAlert: 'WorkflowTemplatePreviewerAlert',
	WorkflowTemplateSendTestEmail: 'WorkflowTemplateSendTestEmail',
	WorkflowTemplateSendTestEmailDialogTitle: 'WorkflowTemplateSendTestEmailDialogTitle',
	WorkflowTemplateSendTestEmailDialogDescription: 'WorkflowTemplateSendTestEmailDialogDescription',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'WorkflowTemplateSendTestEmailDialogRecipientEmail',
	WorkflowTemplateSendTestEmailDialogSendButton: 'WorkflowTemplateSendTestEmailDialogSendButton',
	WorkflowTemplateSendTestEmailSuccess: 'WorkflowTemplateSendTestEmailSuccess',
	WorkflowTemplateDeleteConfirmationTitle: 'WorkflowTemplateDeleteConfirmationTitle',
	WorkflowTemplateDeleteConfirmationMessage: 'WorkflowTemplateDeleteConfirmationMessage',
	WorkflowTemplateDeletedSuccess: 'WorkflowTemplateDeletedSuccess',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'WorkflowTemplateDeleteLocalisationDialogTitle',
	WorkflowTemplateDeleteLocalisationDialogDescription: 'WorkflowTemplateDeleteLocalisationDialogDescription',
	WorkflowTemplateResetEmailContentDialogTitle: 'WorkflowTemplateResetEmailContentDialogTitle',
	WorkflowTemplateResetEmailContentDialogDescription: 'WorkflowTemplateResetEmailContentDialogDescription',
	WorkflowTemplateTranslateLocaleSuccess: 'WorkflowTemplateTranslateLocaleSuccess',
	WorkflowTemplateTranslateLocaleError: 'WorkflowTemplateTranslateLocaleError',
	WorkflowTemplateConfigKey_Subject: 'WorkflowTemplateConfigKey_Subject',
	WorkflowTemplateConfigKey_Header: 'WorkflowTemplateConfigKey_Header',
	WorkflowTemplateConfigKey_Title: 'WorkflowTemplateConfigKey_Title',
	WorkflowTemplateConfigKey_Body: 'WorkflowTemplateConfigKey_Body',
	WorkflowTemplateConfigKey_Footer: 'WorkflowTemplateConfigKey_Footer',
	WorkflowTemplateConfigKey_SecurityFooter: 'WorkflowTemplateConfigKey_SecurityFooter',
	WorkflowTemplateConfigKey_Content: 'WorkflowTemplateConfigKey_Content',
	WorkflowTemplateConfigKey_Header_IsVisible: 'WorkflowTemplateConfigKey_Header_IsVisible',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'WorkflowTemplateConfigKey_Branding_IsVisible',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'WorkflowTemplateConfigKey_SecurityFooter_IsVisible',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'WorkflowTemplateConfigKey_Footer_IsVisible',
	// endregion WorkflowTemplates

	TaxAmount: 'TaxAmount',
	UpdateTaskBillingDetails: 'UpdateTaskBillingDetails',
	UpdateTaskBillingDetailsDescription: 'UpdateTaskBillingDetailsDescription',
	TemplateEditModeViewSwitcherDescription: 'TemplateEditModeViewSwitcherDescription',
	TemplateResponderModeViewSwitcherDescription: 'TemplateResponderModeViewSwitcherDescription',
	TemplateResponderModeViewSwitcherTooltipTitle: 'TemplateResponderModeViewSwitcherTooltipTitle',
	NoteEditModeViewSwitcherDescription: 'NoteEditModeViewSwitcherDescription',
	NoteViewModeViewSwitcherDescription: 'NoteViewModeViewSwitcherDescription',
	NoteResponderModeViewSwitcherDescription: 'NoteResponderModeViewSwitcherDescription',
	NoteResponderModeViewSwitcherTooltipTitle: 'NoteResponderModeViewSwitcherTooltipTitle',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'NoteResponderModeViewSwitcherTooltipTitleAsClient',
	UninvoicedAmounts: 'UninvoicedAmounts',
	InvoiceUninvoicedAmounts: 'InvoiceUninvoicedAmounts',
	NumberOfUninvoicedAmounts: 'NumberOfUninvoicedAmounts',
	OverinvoicedAmount: 'OverinvoicedAmount',
	AllocationTotalWarningMessage: 'AllocationTotalWarningMessage',
	CreateNewClaimForAClient: 'CreateNewClaimForAClient',
	NoUnclaimedItemsFound: 'NoUnclaimedItemsFound',
	Overallocated: 'Overallocated',
	ClientAndRelationshipSelectorTitle: 'ClientAndRelationshipSelectorTitle',
	ClientAndRelationshipSelectorTitle1: 'ClientAndRelationshipSelectorTitle1',
	ClientAndRelationshipSelectorPlaceholder: 'ClientAndRelationshipSelectorPlaceholder',
	AppointmentTotalDuration: 'AppointmentTotalDuration',
	AnalyzingAudio: 'AnalyzingAudio',
	ValidatingTranscripts: 'ValidatingTranscripts',
	AnalyzingRequest: 'AnalyzingRequest',
	AnalyzingInputContent: 'AnalyzingInputContent',
	AnalyzingTemplateContent: 'AnalyzingTemplateContent',
	GeneratingContent: 'GeneratingContent',
	ValidatingContent: 'ValidatingContent',
	DontSend: 'DontSend',
	SendNotificationLabel: 'SendNotificationLabel',
	AddInsurance: 'AddInsurance',
	TranscriptionOvertimeWarning: 'TranscriptionOvertimeWarning',
	Transcription: 'Transcription',
	RecordingEnded: 'RecordingEnded',
	InvalidGTMContainerId: 'InvalidGTMContainerId',

	NegativeBalanceNotSupported: 'NegativeBalanceNotSupported',
	CreditAmount: 'CreditAmount',

	TranscriptionPartDeleteMessage: 'TranscriptionPartDeleteMessage',
	SuccessfullyDeletedTranscriptionPart: 'SuccessfullyDeletedTranscriptionPart',
	SuccessfullyUpdatedTranscriptionPart: 'SuccessfullyUpdatedTranscriptionPart',
	ContinueTranscription: 'ContinueTranscription',
	EndTranscription: 'EndTranscription',
	EndDictation: 'EndDictation',
	ContinueDictating: 'ContinueDictating',
	ThemePanelTitle: 'ThemePanelTitle',
	ThemePanelDescription: 'ThemePanelDescription',
	ThemeColorModePickerTitle: 'ThemeColorModePickerTitle',
	ThemeColorLightMode: 'ThemeColorLightMode',
	ThemeColorDarkMode: 'ThemeColorDarkMode',
	ThemeCpColorPickerTitle: 'ThemeCpColorPickerTitle',
	ThemeAllColorsPickerTitle: 'ThemeAllColorsPickerTitle',
	ChooseTheme: 'ChooseTheme',
	AmountDue: 'AmountDue',
	OutsideOfWorkingHours: 'OutsideOfWorkingHours',
	TranscriptionEmptyHelperMessage: 'TranscriptionEmptyHelperMessage',
	TranscriptionEmpty: 'TranscriptionEmpty',
	Workspaces: 'Workspaces',
	Subscriptions: 'Subscriptions',
	Additional: 'Additional',
	ReferFriends: 'ReferFriends',
	GetHelp: 'GetHelp',
	ViewAppointment: 'ViewAppointment',
	GenerateBillingItemsBannerContent: 'GenerateBillingItemsBannerContent',
	SendNotificationEmailWarning: 'SendNotificationEmailWarning',
	UpdatePrimaryEmailWarningTitle: 'UpdatePrimaryEmailWarningTitle',
	UpdatePrimaryEmailWarningDescription: 'UpdatePrimaryEmailWarningDescription',
	ServiceAdjustment: 'ServiceAdjustment',
	CreateClaimCompletedMessage: 'CreateClaimCompletedMessage',
	AddPaymentAdjustment: 'AddPaymentAdjustment',
	AddPaymentAdjustmentEnabledDescription: 'AddPaymentAdjustmentEnabledDescription',
	AddPaymentAdjustmentDisabledDescription: 'AddPaymentAdjustmentDisabledDescription',
	StartAndEndTime: 'StartAndEndTime',
	BillingDetails: 'BillingDetails',
	AddInvoice: 'AddInvoice',
	AddClaim: 'AddClaim',
	MergeLimitExceeded: 'MergeLimitExceeded',
	NewFolder: 'NewFolder',
	ViewAll: 'ViewAll',
	Workspace: 'Workspace',

	Button: 'Button',
	DirectTextLink: 'DirectTextLink',
	InlineEmbed: 'InlineEmbed',

	ShareTemplateAsPublicFormTrigger: 'ShareTemplateAsPublicFormTrigger',
	ShareTemplateAsPublicFormModalTitle: 'ShareTemplateAsPublicFormModalTitle',
	ShareTemplateAsPublicFormModalDescription: 'ShareTemplateAsPublicFormModalDescription',
	ShareTemplateAsPublicFormSaved: 'ShareTemplateAsPublicFormSaved',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription:
		'ShareTemplateAsPublicFormVerificationOptionNeverDescription',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription:
		'ShareTemplateAsPublicFormVerificationOptionExistingDescription',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'ShareTemplateAsPublicFormVerificationOptionAlwaysDescription',
	ShareTemplateAsPublicFormSectionCustomization: 'ShareTemplateAsPublicFormSectionCustomization',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'ShareTemplateAsPublicFormUseWorkspaceBranding',
	ShareTemplateAsPublicFormShowPoweredBy: 'ShareTemplateAsPublicFormShowPoweredBy',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage:
		'ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'ShareTemplateAsPublicFormShowPoweredByDisabledMessage',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning',

	CopyCodeToClipboardSuccess: 'CopyCodeToClipboardSuccess',

	RecurringAppointment: 'RecurringAppointment',
	CreateBillingItems: 'CreateBillingItems',
	Anonymous: 'Anonymous',
	CommunityUser: 'CommunityUser',
	InvoiceInvalidCredit: 'InvoiceInvalidCredit',
	InboxComposeCarepatronChat: 'InboxComposeCarepatronChat',
	RecurringAppointmentsLimitedBannerText: 'RecurringAppointmentsLimitedBannerText',
	CopyToWorkspace: 'CopyToWorkspace',
	SetAsIntake: 'SetAsIntake',
	RemoveAsIntake: 'RemoveAsIntake',
	PublishTemplate: 'PublishTemplate',
	AiTemplateBannerTitle: 'AiTemplateBannerTitle',
	ViewTemplate: 'ViewTemplate',
	ViewTemplates: 'ViewTemplates',
	AssessmentsCategoryDescription: 'AssessmentsCategoryDescription',
	FormsCategoryDescription: 'FormsCategoryDescription',
	NotesCategoryDescription: 'NotesCategoryDescription',
	PlansAndReportsCategoryDescription: 'PlansAndReportsCategoryDescription',
	WorksheetsAndHandoutsDescription: 'WorksheetsAndHandoutsDescription',
	GuidelinesCategoryDescription: 'GuidelinesCategoryDescription',
	ListsAndTrackersCategoryDescription: 'ListsAndTrackersCategoryDescription',
	LettersCategoryDescription: 'LettersCategoryDescription',
	ChartsAndDiagramsCategoryDescription: 'ChartsAndDiagramsCategoryDescription',
	ViewCollection: 'ViewCollection',
	TeamTemplatesSectionDescription: 'TeamTemplatesSectionDescription',
	CommunityTemplatesSectionTitle: 'CommunityTemplatesSectionTitle',
	CommunityTemplatesSectionDescription: 'CommunityTemplatesSectionDescription',
	RecommendedTemplates: 'RecommendedTemplates',
	Recommended: 'Recommended',
	InboxMessageMarkedAsRead: 'InboxMessageMarkedAsRead',
	InboxMessageMarkedAsUnread: 'InboxMessageMarkedAsUnread',
	InboxAreYouSureDeleteMessage: 'InboxAreYouSureDeleteMessage',
	InboxMessageDeleted: 'InboxMessageDeleted',
	InboxDeleteMessage: 'InboxDeleteMessage',
	AiScribeNoDeviceFoundErrorMessage: 'AiScribeNoDeviceFoundErrorMessage',
	GenerateItems: 'GenerateItems',
	MoveTemplateToFolder: 'MoveTemplateToFolder',
	MoveTemplateToNewFolder: 'MoveTemplateToNewFolder',
	MoveToNewFolder: 'MoveToNewFolder',
	Favorite: 'Favorite',
	AddToStarred: 'AddToStarred',
	TypeToConfirm: 'TypeToConfirm',
	YesTransfer: 'YesTransfer',
	MinimizeConfirmationTitle: 'MinimizeConfirmationTitle',
	MinimizeConfirmationDescription: 'MinimizeConfirmationDescription',
	YesProceed: 'YesProceed',
	InsuranceAmount: 'InsuranceAmount',
	InsurancePaid: 'InsurancePaid',
	RepeatsEvery: 'RepeatsEvery',
	WeekPlural: 'WeekPlural',
	DayPlural: 'DayPlural',
	Copayment: 'Copayment',
	Overpaid: 'Overpaid',
	Created: 'Created',
	Folders: 'Folders',
	TransferOwnershipToMember: 'TransferOwnershipToMember',

	QuickActions: 'QuickActions',
	InsurancePayment: 'InsurancePayment',
	EndsOnDate: 'EndsOnDate',
	RecurringEventListDescription: 'RecurringEventListDescription',
	FeatureLimitBannerTitle: 'FeatureLimitBannerTitle',
	FeatureLimitBannerDescription: 'FeatureLimitBannerDescription',
	RecentServices: 'RecentServices',
	Payer: 'Payer',
	ChoosePayer: 'ChoosePayer',
	Reference: 'Reference',
	RefundAcknowledgementValidationMessage: 'RefundAcknowledgementValidationMessage',
	UpdateInvoicesOrClaimsAgainstBillable: 'UpdateInvoicesOrClaimsAgainstBillable',
	CompleteSetup: 'CompleteSetup',
	MarkAsCompleted: 'MarkAsCompleted',
	InboxNoDirectMessage: 'InboxNoDirectMessage',
	External: 'External',
	BlankInvoice: 'BlankInvoice',
	InboxDirectMessage: 'InboxDirectMessage',

	AppointmentLocation: 'AppointmentLocation',
	AppointmentLocationDescription: 'AppointmentLocationDescription',

	Manual: 'Manual',
	CustomerBalance: 'CustomerBalance',
	Pending: 'Pending',
	DeletedContact: 'DeletedContact',
	NumberOfPayments: 'NumberOfPayments',
	SearchPayments: 'SearchPayments',

	TotalWorkflows: 'TotalWorkflows',
	Apply: 'Apply',
	FilterByAmount: 'FilterByAmount',
	IsEqualTo: 'IsEqualTo',
	IsBetween: 'IsBetween',
	IsGreaterThan: 'IsGreaterThan',
	IsLessThan: 'IsLessThan',
	IsGreaterThanOrEqualTo: 'IsGreaterThanOrEqualTo',
	IsLessThanOrEqualTo: 'IsLessThanOrEqualTo',
	AvailableTimes: 'AvailableTimes',
	RequestingDevicePermissions: 'RequestingDevicePermissions',
	ExportPaymentsFilename: 'ExportPaymentsFilename',

	Community: 'Community',
	Disable: 'Disable',
	Enable: 'Enable',
	TemplateDetails: 'TemplateDetails',
	TemplateName: 'TemplateName',
	NotificationTemplateUpdateSuccess: 'NotificationTemplateUpdateSuccess',
	NotificationTemplateUpdateFailed: 'NotificationTemplateUpdateFailed',
	NewTemplateFolderDescription: 'NewTemplateFolderDescription',
	FolderName: 'FolderName',
	UntitledFolder: 'UntitledFolder',
	EditFolder: 'EditFolder',
	EditFolderDescription: 'EditFolderDescription',

	CreateTemplateFolderSuccessMessage: 'CreateTemplateFolderSuccessMessage',
	EditTemplateFolderSuccessMessage: 'EditTemplateFolderSuccessMessage',
	AutoDeclinePrimaryText: 'AutoDeclinePrimaryText',
	AutoDeclineSecondaryText: 'AutoDeclineSecondaryText',
	AutoDeclineAllFutureOption: 'AutoDeclineAllFutureOption',
	AutoDeclineAllOption: 'AutoDeclineAllOption',

	DeleteFolder: 'DeleteFolder',
	DeleteFolderConfirmationMessage: 'DeleteFolderConfirmationMessage',
	DuplicateTemplateFolderSuccessMessage: 'DuplicateTemplateFolderSuccessMessage',
	ChooseClaim: 'ChooseClaim',

	Unfavorite: 'Unfavorite',

	ClaimNumber: 'ClaimNumber',
	NumberOfClaims: 'NumberOfClaims',
	SearchClaims: 'SearchClaims',
	SubmittedDate: 'SubmittedDate',
	MoveToChosenFolder: 'MoveToChosenFolder',
	MoveToSelectedFolder: 'MoveToSelectedFolder',
	ExistingFolders: 'ExistingFolders',
	ChooseFolder: 'ChooseFolder',
	MoveToFolder: 'MoveToFolder',
	RemoveFromFolder: 'RemoveFromFolder',
	RemoveFromFolderConfirmationDescription: 'RemoveFromFolderConfirmationDescription',
	History: 'History',
	Unknown: 'Unknown',
	Electronic: 'Electronic',

	UpdateTemplateFolderSuccessMessage: 'UpdateTemplateFolderSuccessMessage',

	PublicFormConfirmSubmissionHeader: 'PublicFormConfirmSubmissionHeader',
	PublicFormSubmissionSuccess: 'PublicFormSubmissionSuccess',
	PublicFormSubmissionError: 'PublicFormSubmissionError',
	PublicFormSubmittedTitle: 'PublicFormSubmittedTitle',
	PublicFormSubmittedSubtitle: 'PublicFormSubmittedSubtitle',
	PublicFormBackToForm: 'PublicFormBackToForm',
	PublicFormsInvalidConfirmationCode: 'PublicFormsInvalidConfirmationCode',
	PublicFormNotFoundTitle: 'PublicFormNotFoundTitle',
	PublicFormNotFoundDescription: 'PublicFormNotFoundDescription',
	PublicFormVerifyClientEmailDialogSubtitle: 'PublicFormVerifyClientEmailDialogSubtitle',

	VerifyAndSubmit: 'VerifyAndSubmit',

	TaxNumberType: 'TaxNumberType',

	MaximumMinimizedPanelsReachedTitle: 'MaximumMinimizedPanelsReachedTitle',
	MaximumMinimizedPanelsReachedDescription: 'MaximumMinimizedPanelsReachedDescription',
	YesMinimize: 'YesMinimize',

	InsuranceUnpaid: 'InsuranceUnpaid',

	MoveTemplateToIntakeFolderSuccessMessage: 'MoveTemplateToIntakeFolderSuccessMessage',
	MoveTemplateToFolderSuccess: 'MoveTemplateToFolderSuccess',

	ExportPayments: 'ExportPayments',
	ExportClaims: 'ExportClaims',
	ExportClaimsFilename: 'ExportClaimsFilename',
	NoAvailableTimes: 'NoAvailableTimes',
	HourAbbreviation: 'HourAbbreviation',
	MinuteAbbreviation: 'MinuteAbbreviation',

	CareAiNoConversations: 'CareAiNoConversations',
	CareAiNoConversationsDescription: 'CareAiNoConversationsDescription',
	TimeFormat: 'TimeFormat',

	Never: 'Never',
	Always: 'Always',
	Existing: 'Existing',
	VerificationOption: 'VerificationOption',
	Templates: 'Templates',
	FeatureRequiresUpgrade: 'FeatureRequiresUpgrade',

	PublishTemplateToCommunity: 'PublishTemplateToCommunity',
	RepublishTemplateToCommunity: 'RepublishTemplateToCommunity',

	NoClaimsHeading: 'NoClaimsHeading',
	SetupGuide: 'SetupGuide',
	SetupGuideVerifyEmailLabel: 'SetupGuideVerifyEmailLabel',
	SetupGuideVerifyEmailSubtitle: 'SetupGuideVerifyEmailSubtitle',
	SetupGuideSetLocationTitle: 'SetupGuideSetLocationTitle',
	SetupGuideAddServicesTitle: 'SetupGuideAddServicesTitle',
	SetupGuideSyncCalendarTitle: 'SetupGuideSyncCalendarTitle',
	SetupGuideEnableOnlinePaymentsTitle: 'SetupGuideEnableOnlinePaymentsTitle',
	SetupGuideSetLocationSubtitle: 'SetupGuideSetLocationSubtitle',
	SetupGuideAddServicesSubtitle: 'SetupGuideAddServicesSubtitle',
	SetupGuideSyncCalendarSubtitle: 'SetupGuideSyncCalendarSubtitle',
	SetupGuideEnableOnlinePaymentsSubtitle: 'SetupGuideEnableOnlinePaymentsSubtitle',
	SetupGuideSetLocationActionLabel: 'SetupGuideSetLocationActionLabel',
	SetupGuideAddServicesActionLabel: 'SetupGuideAddServicesActionLabel',
	SetupGuideSyncCalendarActionLabel: 'SetupGuideSyncCalendarActionLabel',
	SetupGuideEnableOnlinePaymentsActionLabel: 'SetupGuideEnableOnlinePaymentsActionLabel',
	SetupGuideSuggestedUseAIAssistantTitle: 'SetupGuideSuggestedUseAIAssistantTitle',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'SetupGuideSuggestedUseAIAssistantSubtitle',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'SetupGuideSuggestedUseAIAssistantActionLabel',
	SetupGuideSuggestedUseAIAssistantTag: 'SetupGuideSuggestedUseAIAssistantTag',
	SetupGuideSuggestedStartVideoCallTitle: 'SetupGuideSuggestedStartVideoCallTitle',
	SetupGuideSuggestedStartVideoCallSubtitle: 'SetupGuideSuggestedStartVideoCallSubtitle',
	SetupGuideSuggestedStartVideoCallActionLabel: 'SetupGuideSuggestedStartVideoCallActionLabel',
	SetupGuideSuggestedStartVideoCallTag: 'SetupGuideSuggestedStartVideoCallTag',
	SetupGuideSuggestedImportClientsTitle: 'SetupGuideSuggestedImportClientsTitle',
	SetupGuideSuggestedImportClientsSubtitle: 'SetupGuideSuggestedImportClientsSubtitle',
	SetupGuideSuggestedImportClientsActionLabel: 'SetupGuideSuggestedImportClientsActionLabel',
	SetupGuideSuggestedImportClientsTag: 'SetupGuideSuggestedImportClientsTag',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'SetupGuideSuggestedPersonalizeRemindersTitle',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'SetupGuideSuggestedPersonalizeRemindersSubtitle',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'SetupGuideSuggestedPersonalizeRemindersActionLabel',
	SetupGuideSuggestedEditAvailabilityTitle: 'SetupGuideSuggestedEditAvailabilityTitle',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'SetupGuideSuggestedEditAvailabilitySubtitle',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'SetupGuideSuggestedEditAvailabilityActionLabel',
	SetupGuideSuggestedEditAvailabilityTag: 'SetupGuideSuggestedEditAvailabilityTag',
	SetupGuideSuggestedCustomizeBrandTitle: 'SetupGuideSuggestedCustomizeBrandTitle',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'SetupGuideSuggestedCustomizeBrandSubtitle',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'SetupGuideSuggestedCustomizeBrandActionLabel',
	SetupGuideSuggestedAddTeamMembersTitle: 'SetupGuideSuggestedAddTeamMembersTitle',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'SetupGuideSuggestedAddTeamMembersSubtitle',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'SetupGuideSuggestedAddTeamMembersActionLabel',
	SetupGuideSuggestedAddTeamMembersTag: 'SetupGuideSuggestedAddTeamMembersTag',
	SetupGuideSuggestedDownloadMobileAppTitle: 'SetupGuideSuggestedDownloadMobileAppTitle',
	SetupGuideSuggestedDownloadMobileAppSubtitle: 'SetupGuideSuggestedDownloadMobileAppSubtitle',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'SetupGuideSuggestedDownloadMobileAppActionLabel',
	SetupGuideSuggestedDownloadMobileAppTag: 'SetupGuideSuggestedDownloadMobileAppTag',
	LearnMoreTipsToGettingStarted: 'LearnMoreTipsToGettingStarted',
	SetupGuideSuggestedTryActionsTitle: 'SetupGuideSuggestedTryActionsTitle',
	VisitOurHelpCentre: 'VisitOurHelpCentre',
	JoinWebinar: 'JoinWebinar',
	WatchAVideo: 'WatchAVideo',
	Articles: 'Articles',
	CompleteSetupSuccessTitle: 'CompleteSetupSuccessTitle',
	CompleteSetupSuccessDescription: 'CompleteSetupSuccessDescription',
	CompleteSetupSuccessDescription2: 'CompleteSetupSuccessDescription2',
	UserIsTyping: 'UserIsTyping',

	RemoveTemplateFromFolder: 'RemoveTemplateFromFolder',
	SyncMyCalendarToCarepatron: 'SyncMyCalendarToCarepatron',
	SyncCarepatronAppointmentsWithMyCalendar: 'SyncCarepatronAppointmentsWithMyCalendar',
	ClaimValidationFailure: 'ClaimValidationFailure',
	ChooseACollection: 'ChooseACollection',
	BackToTemplates: 'BackToTemplates',
	Unpublish: 'Unpublish',
	PolicyMemberId: 'PolicyMemberId',
	CountTimes: 'CountTimes',
	Yearly: 'Yearly',
	Hourly: 'Hourly',
	Minutely: 'Minutely',
	Secondly: 'Secondly',
	Until: 'Until',
	ChooseTeamMembers: 'ChooseTeamMembers',
	ATeamMemberIsRequired: 'ATeamMemberIsRequired',
	InboxShowQuotedText: 'InboxShowQuotedText',
	InboxHideQuotedText: 'InboxHideQuotedText',
	WhichPlatforms: 'WhichPlatforms',
	Deactivated: 'Deactivated',
	ValidationServiceMustBeNotBeFuture: 'ValidationServiceMustBeNotBeFuture',
	CanModifyReadOnlyEvent: 'CanModifyReadOnlyEvent',
	ReadOnlyEventBanner: 'ReadOnlyEventBanner',
	ReadOnlyAppointment: 'ReadOnlyAppointment',
	SyncedFromExternalCalendar: 'SyncedFromExternalCalendar',

	AddTemplate: 'AddTemplate',

	Form: 'Form',
	PlanAndReport: 'PlanAndReport',
	WorksheetAndHandout: 'WorksheetAndHandout',
	Letter: 'Letter',
	ListAndTracker: 'ListAndTracker',
	ChartAndDiagram: 'ChartAndDiagram',
	Guidelines: 'Guidelines',
	ReadOnly: 'ReadOnly',
	CustomRecurrence: 'CustomRecurrence',
	EditRecurrence: 'EditRecurrence',
	RepeatEvery: 'RepeatEvery',
	RepeatOn: 'RepeatOn',
	RecurrenceEndNever: 'RecurrenceEndNever',
	RecurrenceEndOn: 'RecurrenceEndOn',
	RecurrenceEndAfter: 'RecurrenceEndAfter',
	RecurrenceYearly: 'RecurrenceYearly',
	RecurrenceMonthly: 'RecurrenceMonthly',
	RecurrenceWeekly: 'RecurrenceWeekly',
	RecurrenceDaily: 'RecurrenceDaily',
	RecurrenceEvery: 'RecurrenceEvery',
	RecurrenceOn: 'RecurrenceOn',
	RecurrenceUntil: 'RecurrenceUntil',
	RecurrenceCount: 'RecurrenceCount',
	RecurrenceOnAllDays: 'RecurrenceOnAllDays',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'ClaimMiscAdditionalClaimInformationHelpTitle',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'ClaimMiscAdditionalClaimInformationHelpSubtitle',
	ClaimMiscAdditionalClaimInformationHelpContent: 'ClaimMiscAdditionalClaimInformationHelpContent',
	HelpArticles: 'HelpArticles',

	ClaimFiling: 'ClaimFiling',
	PaymentReports: 'PaymentReports',
	Eligibility: 'Eligibility',
	NotAvailable: 'NotAvailable',
	Enrol: 'Enrol',
	Enrolments: 'Enrolments',
	EnrolmentsDescription: 'EnrolmentsDescription',
	NoEnrolmentProfilesFound: 'NoEnrolmentProfilesFound',

	ProviderRemindersSettingsBannerTitle: 'ProviderRemindersSettingsBannerTitle',
	ProviderRemindersSettingsBannerDescription: 'ProviderRemindersSettingsBannerDescription',
	ProviderRemindersSettingsBannerAction: 'ProviderRemindersSettingsBannerAction',

	UsageCount: 'UsageCount',
	InboxMessageSentViaChat: 'InboxMessageSentViaChat',

	DeleteTemplateSuccessMessage: 'DeleteTemplateSuccessMessage',
	DeleteTemplateFolderSuccessMessage: 'DeleteTemplateFolderSuccessMessage',
	VerifyEmail: 'VerifyEmail',
	ViewSettings: 'ViewSettings',
	YesDisconnect: 'YesDisconnect',
	SystemGenerated: 'SystemGenerated',
	SearchAndConvertToLanguage: 'SearchAndConvertToLanguage',

	AddAvailablePayers: 'AddAvailablePayers',
	AddAvailablePayersDescription: 'AddAvailablePayersDescription',
	AddManually: 'AddManually',
	PlusAdd: 'PlusAdd',
	AvailablePayersEmptyState: 'AvailablePayersEmptyState',
	SelectPayers: 'SelectPayers',
	SettingUpPayers: 'SettingUpPayers',
	SuggestedLocations: 'SuggestedLocations',
	CapturePaymentMethod: 'CapturePaymentMethod',
	InboxRecentConversations: 'InboxRecentConversations',
	InboxSearchForConversations: 'InboxSearchForConversations',
	InsuranceClaimAiSubtitle: 'InsuranceClaimAiSubtitle',
	InsuranceClaimAiChatPlaceholder: 'InsuranceClaimAiChatPlaceholder',
	InsuranceClaimAiClaimNumber: 'InsuranceClaimAiClaimNumber',
	SmartReply: 'SmartReply',
	Unlimited: 'Unlimited',
	Limited: 'Limited',
	PerUser: 'PerUser',
	PerMonth: 'PerMonth',
	UsageValue: 'UsageValue',
	UsageLimitValue: 'UsageLimitValue',
	AiTokens: 'AiTokens',
	AdvancedPlanMessage: 'AdvancedPlanMessage',
	OtherPlanMessage: 'OtherPlanMessage',
	ActiveUsers: 'ActiveUsers',
	InvoiceTotal: 'InvoiceTotal',
	NextInvoiceIssueDate: 'NextInvoiceIssueDate',
	ChooseSource: 'ChooseSource',
	MapColumns: 'MapColumns',
	StartImport: 'StartImport',
	OrDragAndDrop: 'OrDragAndDrop',
	Sections: 'Sections',
	SpreadsheetColumns: 'SpreadsheetColumns',
	SmartSuggestedFieldDescription: 'SmartSuggestedFieldDescription',
	ImportClientsPreviewClientsReadyForImport: 'ImportClientsPreviewClientsReadyForImport',
	UploadFileTileLabel: 'UploadFileTileLabel',
	UploadFileTileDescription: 'UploadFileTileDescription',
	ImportFromAnotherPlatformTileLabel: 'ImportFromAnotherPlatformTileLabel',
	ImportFromAnotherPlatformTileDescription: 'ImportFromAnotherPlatformTileDescription',
	DownloadTemplateTileLabel: 'DownloadTemplateTileLabel',
	DownloadTemplateTileDescription: 'DownloadTemplateTileDescription',
	DownloadTemplateFileName: 'DownloadTemplateFileName',
	ChooseSourceDescription: 'ChooseSourceDescription',
	UploadFileDescription: 'UploadFileDescription',
	SwitchFromAnotherPlatform: 'SwitchFromAnotherPlatform',
	ImportClientsGuide: 'ImportClientsGuide',
	ClaimSubmittedAction: 'ClaimSubmittedAction',
	ClaimReceivedAction: 'ClaimReceivedAction',
	ClaimStatusChangedAction: 'ClaimStatusChangedAction',
	ClaimTrashedAction: 'ClaimTrashedAction',
	ClaimRestoredAction: 'ClaimRestoredAction',
	ClaimCreatedAction: 'ClaimCreatedAction',
	ClaimRejectedAction: 'ClaimRejectedAction',
	ClaimDeniedAction: 'ClaimDeniedAction',
	ClaimManualPaymentAction: 'ClaimManualPaymentAction',
	ClaimElectronicPaymentAction: 'ClaimElectronicPaymentAction',
	ClaimExportedAction: 'ClaimExportedAction',
	ClaimERAReceivedAction: 'ClaimERAReceivedAction',
	ClaimHistorySubtitle: 'ClaimHistorySubtitle',
	PayerClaimId: 'PayerClaimId',
	ClearingHouseClaimId: 'ClearingHouseClaimId',
	Errors: 'Errors',
	HistoryTitle: 'HistoryTitle',
	HistoryItemFooter: 'HistoryItemFooter',
	HistorySidePanelEmptyState: 'HistorySidePanelEmptyState',
	ViewPayment: 'ViewPayment',
	SetupGuidePersonalizeWorkspaceTitle: 'SetupGuidePersonalizeWorkspaceTitle',
	SetupGuidePersonalizeWorkspaceSubtitle: 'SetupGuidePersonalizeWorkspaceSubtitle',
	SetupGuidePersonalizeWorkspaceActionLabel: 'SetupGuidePersonalizeWorkspaceActionLabel',
	PersonalizeWorkspace: 'PersonalizeWorkspace',
	Suggestions: 'Suggestions',
	NoLocationsWillBeAdded: 'NoLocationsWillBeAdded',
	SetupGuideImportClientsTitle: 'SetupGuideImportClientsTitle',
	SetupGuideImportClientsSubtitle: 'SetupGuideImportClientsSubtitle',
	SetupGuideImportClientsActionLabel: 'SetupGuideImportClientsActionLabel',
	SetupGuideImportTemplatesTitle: 'SetupGuideImportTemplatesTitle',
	SetupGuideImportTemplatesSubtitle: 'SetupGuideImportTemplatesSubtitle',
	SetupGuideImportTemplatesActionLabel: 'SetupGuideImportTemplatesActionLabel',
	NoTemplatesInFolder: 'NoTemplatesInFolder',
	ErrorInvalidNationalProviderId: 'ErrorInvalidNationalProviderId',
	ErrorInvalidTaxNumber: 'ErrorInvalidTaxNumber',
	ErrorInvalidPayerId: 'ErrorInvalidPayerId',
	InsuranceClaimErrorTitle: 'InsuranceClaimErrorTitle',
	InsuranceClaimErrorDescription: 'InsuranceClaimErrorDescription',
	InsuranceClaimErrorGuideLink: 'InsuranceClaimErrorGuideLink',
	ChatTypeMessageTo: 'ChatTypeMessageTo',
	InboxMessageWasDeleted: 'InboxMessageWasDeleted',
	SuggestedAIPoweredTemplates: 'SuggestedAIPoweredTemplates',
	EnrollmentRejectedSubject: 'EnrollmentRejectedSubject',
	ClaimMd: 'ClaimMd',
	ManagedClaimMd: 'ManagedClaimMd',
	ClearingHouse: 'ClearingHouse',
	ViewEnrollment: 'ViewEnrollment',
	ChatEditMessage: 'ChatEditMessage',
	ChatReplyTo: 'ChatReplyTo',
	RevertClaimStatus: 'RevertClaimStatus',
	RevertClaimStatusDescriptionTitle: 'RevertClaimStatusDescriptionTitle',
	RevertClaimStatusDescriptionBody: 'RevertClaimStatusDescriptionBody',
	ConfirmRevertClaim: 'ConfirmRevertClaim',
	RevertClaimStatusError: 'RevertClaimStatusError',
	WatchDemoVideo: 'WatchDemoVideo',
	Resources: 'Resources',
	Regenerate: 'Regenerate',
	SubmitFeedback: 'SubmitFeedback',
	HowDidWeDo: 'HowDidWeDo',
	HowCanWeImprove: 'HowCanWeImprove',
	EditorTemplateWithSlashCommandPlaceholder: 'EditorTemplateWithSlashCommandPlaceholder',
	ClearingHouseGeneralError: 'ClearingHouseGeneralError',
	ClearingHouseUnavailableError: 'ClearingHouseUnavailableError',
	SearchTemplatePlaceholder: 'SearchTemplatePlaceholder',
	BackToUploadFile: 'BackToUploadFile',
	BackToMapColumns: 'BackToMapColumns',
	FinalizeImport: 'FinalizeImport',
	InboxChatGroupCreateModalTitle: 'InboxChatGroupCreateModalTitle',
	InboxChatGroupCreateModalDescription: 'InboxChatGroupCreateModalDescription',
	InboxChatGroupUpdateModalTitle: 'InboxChatGroupUpdateModalTitle',
	InboxChatGroupModalGroupNameFieldLabel: 'InboxChatGroupModalGroupNameFieldLabel',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'InboxChatGroupModalGroupNameFieldPlaceholder',
	InboxChatGroupModalMembersFieldLabel: 'InboxChatGroupModalMembersFieldLabel',
	InboxChatGroupModalMembersFieldPlaceholder: 'InboxChatGroupModalMembersFieldPlaceholder',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'InboxChatGroupModalMembersFieldErrorMinimumOne',
	InboxChatGroupMembers: 'InboxChatGroupMembers',
	InboxChatManageGroup: 'InboxChatManageGroup',
	InboxChatCreateGroup: 'InboxChatCreateGroup',
	InboxChatLeaveGroup: 'InboxChatLeaveGroup',
	InboxChatDiscardDraft: 'InboxChatDiscardDraft',
	InboxChatLeaveGroupModalTitle: 'InboxChatLeaveGroupModalTitle',
	InboxChatLeaveGroupModalDescription: 'InboxChatLeaveGroupModalDescription',
	YesLeave: 'YesLeave',
	InboxChatDeleteGroupModalTitle: 'InboxChatDeleteGroupModalTitle',
	InboxChatDeleteGroupModalDescription: 'InboxChatDeleteGroupModalDescription',
	InboxChatLeftGroupMessage: 'InboxChatLeftGroupMessage',
	Accepted: 'Accepted',
	ERAAdjustment: 'ERAAdjustment',
	ViewRemittanceAdvice: 'ViewRemittanceAdvice',
	ViewRemittanceAdviceHeader: 'ViewRemittanceAdviceHeader',
	ViewRemittanceAdviceSubheader: 'ViewRemittanceAdviceSubheader',
	Adjustment: 'Adjustment',
	PaymentAmount: 'PaymentAmount',
	ClientResponsibility: 'ClientResponsibility',
	TotalAdjustments: 'TotalAdjustments',
	TotalBilled: 'TotalBilled',
	Glossary: 'Glossary',
	ClearingHouseReference: 'ClearingHouseReference',
	DownloadERA: 'DownloadERA',
	WhatDidYouLikeResponse: 'WhatDidYouLikeResponse',
	HowCanWeImproveResponse: 'HowCanWeImproveResponse',
	InsuranceClaims: 'InsuranceClaims',
	TeamMemberDoubleBookedTooltip: 'TeamMemberDoubleBookedTooltip',
	UploadFileSizeLimit: 'UploadFileSizeLimit',
	FileDownloaded: 'FileDownloaded',
	SearchServices: 'SearchServices',
	InboxChatSearchParticipants: 'InboxChatSearchParticipants',
	InboxChatGroupConversation: 'InboxChatGroupConversation',
	InboxChatGroupModalGroupNameFieldRequired: 'InboxChatGroupModalGroupNameFieldRequired',
	NoGlossaryItems: 'NoGlossaryItems',
	Completed: 'Completed',
	DidNotComplete: 'DidNotComplete',
	ManageTaskAttendeeStatus: 'ManageTaskAttendeeStatus',
	ManageTaskAttendeeStatusSubtitle: 'ManageTaskAttendeeStatusSubtitle',
	ManageTaskAttendeeStatusDescription: 'ManageTaskAttendeeStatusDescription',
	ManageTaskAttendeeStatusHelperText: 'ManageTaskAttendeeStatusHelperText',
	TransferTaskAttendeeStatusTitle: 'TransferTaskAttendeeStatusTitle',
	TransferTaskAttendeeStatusSubtitle: 'TransferTaskAttendeeStatusSubtitle',
	TransferTaskAttendeeStatusDescription: 'TransferTaskAttendeeStatusDescription',
	TransferTaskAttendeeStatusAlert: 'TransferTaskAttendeeStatusAlert',
	TaskAttendeeStatusUpdatedSuccess: 'TaskAttendeeStatusUpdatedSuccess',
	InvalidDateFormat: 'InvalidDateFormat',
	InsuranceClaimRejectedSubject: 'InsuranceClaimRejectedSubject',
	InsuranceClaimPaidSubject: 'InsuranceClaimPaidSubject',
	InsuranceClaimDeniedSubject: 'InsuranceClaimDeniedSubject',
	ResubmissionCodeReferenceNumber: 'ResubmissionCodeReferenceNumber',
	ReplacementOfPriorClaim: 'ReplacementOfPriorClaim',
	VoidCancelPriorClaim: 'VoidCancelPriorClaim',
	ClaimAmount: 'ClaimAmount',
	PaidAmount: 'PaidAmount',
	CannotMoveCarepatronStatusOutsideGroup: 'CannotMoveCarepatronStatusOutsideGroup',
	PayYearly: 'PayYearly',
	PayMonthly: 'PayMonthly',
	PayYearlyPercentOff: 'PayYearlyPercentOff',
	ScheduleSetupCall: 'ScheduleSetupCall',
	NavigateToWorkspaceSettings: 'NavigateToWorkspaceSettings',
	NavigateToSubscriptionSettings: 'NavigateToSubscriptionSettings',
	NavigateToPersonalSettings: 'NavigateToPersonalSettings',
	NavigateToYourTeam: 'NavigateToYourTeam',
	AlreadyAdded: 'AlreadyAdded',
	ViewClaim: 'ViewClaim',
	FiltersAppliedToView: 'FiltersAppliedToView',
	ItemsAndAdjustments: 'ItemsAndAdjustments',
	InvoiceNumberFormat: 'InvoiceNumberFormat',
	ClaimNumberFormat: 'ClaimNumberFormat',
	ImportActivity: 'ImportActivity',
	ReadyForMapping: 'ReadyForMapping',
	ClientImportReadyForMappingNotificationSubject: 'ClientImportReadyForMappingNotificationSubject',
	DoThisLater: 'DoThisLater',
	ClientImportReadyForMappingDescription: 'ClientImportReadyForMappingDescription',
	ClientImportProcessingDescription: 'ClientImportProcessingDescription',
	MappingRequired: 'MappingRequired',
	ImportInProgress: 'ImportInProgress',
	RunInBackground: 'RunInBackground',
	CancelClientImportTitle: 'CancelClientImportTitle',
	CancelClientImportDescription: 'CancelClientImportDescription',
	CancelClientImportPrimaryAction: 'CancelClientImportPrimaryAction',
	CancelClientImportSecondaryAction: 'CancelClientImportSecondaryAction',
	ImportProcessing: 'ImportProcessing',
	ContinueImport: 'ContinueImport',
} as const);

export default langIds;
