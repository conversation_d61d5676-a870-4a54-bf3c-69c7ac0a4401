import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'Org.nr.',
	AIPrompts: 'AI frågar',
	ATeamMemberIsRequired: 'En teammedlem behövs',
	AboutClient: 'Om klienten',
	AcceptAppointment: 'Tack för att du bekräftade ditt möte',
	AcceptTermsAndConditionsRequired: 'Acceptera villkoren ',
	Accepted: 'Accepterad',
	AccessGiven: 'Tillträde ges',
	AccessPermissions: 'Åtkomstbehörigheter',
	AccessType: 'Åt<PERSON>msttyp',
	Accident: 'Olycka',
	Account: 'Konto',
	AccountCredit: 'Kontokredit',
	Accountant: 'Revisor',
	Action: 'Handling',
	Actions: 'Åtgärder',
	Active: 'Aktiv',
	ActiveTags: 'Aktiva taggar',
	ActiveUsers: 'Aktiva användare',
	Activity: 'Aktivitet',
	Actor: '<PERSON>k<PERSON><PERSON>pel<PERSON>',
	Acupuncture: 'Akupunktur',
	Acupuncturist: 'Akupunktör',
	Acupuncturists: 'Akupunktörer',
	AcuteManifestationOfAChronicCondition: 'Akut manifestation av ett kroniskt tillstånd',
	Add: 'Tillägga',
	AddADescription: 'Lägg till en beskrivning',
	AddALocation: 'Lägg till plats',
	AddASecondTimezone: 'Lägg till en andra tidszon',
	AddAddress: 'Lägg till adress',
	AddAnother: '  Lägg till en till',
	AddAnotherAccount: 'Lägg till ett annat konto',
	AddAnotherContact: 'Lägg till en annan kontakt',
	AddAnotherOption: 'Lägg till ett annat alternativ',
	AddAnotherTeamMember: 'Lägg till en annan teammedlem',
	AddAvailablePayers: '+ Lägg till tillgängliga betalsätt',
	AddAvailablePayersDescription:
		'Sök betalare för att lägga till i din arbetsytas lista över betalare. När du har lagt till dem kan du hantera registreringar eller justera betalaruppgifterna vid behov.',
	AddCaption: 'Lägg till bildtext',
	AddClaim: 'Lägg till anspråk',
	AddClientFilesModalDescription: 'För att begränsa åtkomsten, välj alternativen i kryssrutorna "Visas av".',
	AddClientFilesModalTitle: 'Ladda upp filer för {name}',
	AddClientNoteButton: 'Lägg till anteckning',
	AddClientNoteModalDescription:
		'Lägg till innehåll i din anteckning. Använd avsnittet "Visas av" för att välja en eller flera grupper som kan se denna specifika anteckning.',
	AddClientNoteModalTitle: 'Lägg till anteckning',
	AddClientOwnerRelationshipModalDescription:
		'Genom att bjuda in klienten kan de hantera sin egen profilinformation och hantera användaråtkomst till sin profilinformation.',
	AddClientOwnerRelationshipModalTitle: 'Bjud in kunden',
	AddCode: 'Lägg till kod',
	AddColAfter: 'Lägg till kolumn efter',
	AddColBefore: 'Lägg till kolumn innan',
	AddCollection: 'Lägg till samling',
	AddColor: 'Lägg till färg',
	AddColumn: 'Lägg till kolumn',
	AddContactRelationship: 'Lägg till kontaktrelation',
	AddContacts: 'Lägg till kontakter',
	AddCustomField: 'Lägg till anpassat fält',
	AddDate: 'Lägg till datum',
	AddDescription: 'Lägg till beskrivning',
	AddDetail: 'Lägg till detaljer',
	AddDisplayName: 'Lägg till visningsnamn',
	AddDxCode: 'Lägg till diagnoskod',
	AddEmail: 'Lägg till e-post',
	AddFamilyClientRelationshipModalDescription:
		'Genom att bjuda in en familjemedlem kan de se vårdberättelser och klientens profilinformation. Om de bjuds in som administratörer får de tillgång till att uppdatera klientens profilinformation och hantera användaråtkomst.',
	AddFamilyClientRelationshipModalTitle: 'Bjud in familjemedlem',
	AddField: 'Lägg till fält',
	AddFormField: 'Lägg till formulärfält',
	AddImages: 'Lägg till bilder',
	AddInsurance: 'Lägg till försäkring',
	AddInvoice: 'Lägg till faktura',
	AddLabel: 'Lägg till etikett',
	AddLanguage: 'Lägg till språk',
	AddLocation: 'Lägg till plats',
	AddManually: 'Lägg till manuellt',
	AddMessage: 'Lägg till meddelande',
	AddNewAction: 'Lägg till ny åtgärd',
	AddNewSection: 'Lägg till nytt avsnitt',
	AddNote: 'Lägg till anteckning',
	AddOnlineBookingDetails: 'Lägg till information om onlinebokning',
	AddPOS: 'Lägg till POS',
	AddPaidInvoices: 'Lägg till betalda fakturor',
	AddPayer: 'Lägg till betalare',
	AddPayment: 'Lägg till betalning',
	AddPaymentAdjustment: 'Lägg till betalningsjustering',
	AddPaymentAdjustmentDisabledDescription: 'Betalningsfördelningen kommer inte att ändras.',
	AddPaymentAdjustmentEnabledDescription: 'Det belopp som är tillgängligt att fördela kommer att minskas.',
	AddPhoneNumber: 'Lägg till telefonnummer',
	AddPhysicalOrVirtualLocations: 'Lägg till fysiska eller virtuella platser',
	AddQuestion: 'Lägg till fråga',
	AddQuestionOrTitle: 'Lägg till en fråga eller rubrik',
	AddRelationship: 'Lägg till relation',
	AddRelationshipModalTitle: 'Anslut befintlig kontakt',
	AddRelationshipModalTitleNewClient: 'Anslut ny kontakt',
	AddRow: 'Lägg till rad',
	AddRowAbove: 'Lägg till rad ovan',
	AddRowBelow: 'Lägg till rad nedan',
	AddService: 'Lägg till tjänst',
	AddServiceLocation: 'Lägg till serviceplats',
	AddServiceToCollections: 'Lägg till tjänst till samlingar',
	AddServiceToOneOrMoreCollections: 'Lägg till tjänst i en eller flera samlingar',
	AddServices: 'Lägg till tjänster',
	AddSignature: 'Lägg till signatur',
	AddSignaturePlaceholder: 'Ange ytterligare detaljer som ska inkluderas med din signatur',
	AddSmartDataChips: 'Lägg till smarta datachips',
	AddStaffClientRelationshipsModalDescription:
		'Genom att välja personal kan de skapa och se vårdberättelser för denna klient. De kommer också att kunna se kundinformation.',
	AddStaffClientRelationshipsModalTitle: 'Lägg till personalrelationer',
	AddTag: 'Lägg till en tagg',
	AddTags: 'Lägg till taggar',
	AddTemplate: 'Lägg till mall',
	AddTimezone: 'Lägg till tidszon',
	AddToClaim: 'Lägg till anspråk',
	AddToCollection: 'Lägg till samlingen',
	AddToExisting: 'Lägg till i befintliga',
	AddToStarred: 'Lägg till i stjärnmärkt',
	AddUnclaimedItems: 'Lägg till objekt som inte gjorts anspråk på',
	AddUnrelatedContactWarning:
		'Du har lagt till någon som inte är en kontakt till {kontakt}. Se till att innehållet är relevant innan du fortsätter med delning.',
	AddValue: 'Lägg till "{value}"',
	AddVideoCall: 'Lägg till videosamtal',
	AddVideoOrVoiceCall: 'Lägg till video- eller röstsamtal',
	AddictionCounselor: 'Beroenderådgivare',
	AddingManualPayerDisclaimer:
		'Att lägga till en betalare manuellt till din leverantörslista skapar inte en elektronisk anspråksanslutning med den betalaren, men kan användas för att skapa anspråk manuellt.',
	AddingTeamMembersIncreaseCostAlert: 'Om du lägger till nya teammedlemmar ökar din månatliga prenumeration.',
	Additional: 'Ytterligare',
	AdditionalBillingProfiles: 'Ytterligare faktureringsprofiler',
	AdditionalBillingProfilesSectionDescription:
		'Åsidosätt standardfaktureringsinformationen som används för specifika teammedlemmar, betalare eller fakturamallar.',
	AdditionalFeedback: 'Ytterligare feedback',
	AddnNewWorkspace: 'Ny arbetsyta',
	AddnNewWorkspaceSuccessSnackbar: 'Arbetsytan har skapats!',
	Address: 'Adress',
	AddressNumberStreet: 'Adress (Nej, gata)',
	Adjustment: 'Justering',
	AdjustmentType: 'Justeringstyp',
	Admin: 'Administration',
	Admins: 'Administratörer',
	AdminsOnly: 'Endast administratörer',
	AdvancedPlanInclusionFive: 'Kontoansvarig',
	AdvancedPlanInclusionFour: 'Google Analytics',
	AdvancedPlanInclusionHeader: 'Allt i Plus  ',
	AdvancedPlanInclusionOne: 'Roller ',
	AdvancedPlanInclusionSix: 'Stöd för dataimport',
	AdvancedPlanInclusionThree: 'Vit märkning',
	AdvancedPlanInclusionTwo: '90 dagars raderad datalagring',
	AdvancedPlanMessage: 'Håll koll på vad ditt företag behöver. Granska din nuvarande plan och övervaka användningen.',
	AdvancedSettings: 'Avancerade inställningar',
	AdvancedSubscriptionPlanSubtitle: 'Utöka din träning med alla funktioner',
	AdvancedSubscriptionPlanTitle: 'Avancerad',
	AdvertisingManager: 'Annonsansvarig',
	AerospaceEngineer: 'Flygtekniker',
	AgeYearsOld: '{age} år gammal',
	Agenda: 'Dagordning',
	AgendaView: 'Agendavy',
	AiAskSupportedFileTypes: 'Filtyper som stöds: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'En assistent till hands',
	AiCopilotDisclaimer: 'AI Copilot kan göra misstag. Kontrollera viktig information.',
	AiCreateNewConversation: 'Skapa ny konversation',
	AiEnhanceYourProductivity: 'Öka din produktivitet',
	AiPoweredTemplates: 'AI-drivna mallar',
	AiScribeNoDeviceFoundErrorMessage:
		'Det verkar som att din webbläsare inte stöder den här funktionen, eller så finns inga kompatibla enheter tillgängliga.',
	AiScribeUploadFormat: 'Filtyper som stöds: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'endast 1 fil åt gången',
	AiShowConversationHistory: 'Visa konversationshistorik',
	AiSmartPromptNodePlaceholderText: 'Skriv din egna prompt här för att generera exakta och personliga AI-resultat.',
	AiSmartPromptPrimaryText: 'Ai smart prompt',
	AiSmartPromptSecondaryText: 'Infoga anpassad AI-smart prompt',
	AiSmartReminders: 'AI smarta påminnelser',
	AiTemplateBannerTitle: 'Förenkla ditt arbete med AI-drivna mallar',
	AiTemplates: 'AI-mallar',
	AiTokens: 'AI-token',
	AiWorkBetterWithAi: 'Arbeta bättre med AI',
	All: 'Alla',
	AllAppointments: 'Alla möten',
	AllCategories: 'Alla kategorier',
	AllClients: 'Alla kunder',
	AllContactPolicySelectorLabel: 'Alla kontakter till <mark>{client}</mark>',
	AllContacts: 'Alla kontakter',
	AllContactsOf: 'Alla kontakter till ‘{name}’',
	AllDay: 'Hela dagen',
	AllInboxes: 'Alla inkorgar',
	AllIndustries: 'Alla branscher',
	AllLocations: 'Alla platser',
	AllMeetings: 'Alla möten',
	AllNotificationsRestoredMessage: 'Alla aviseringar återställda',
	AllProfessions: 'Alla yrken',
	AllReminders: 'Alla påminnelser',
	AllServices: 'Alla tjänster',
	AllStatuses: 'Alla statusar',
	AllTags: 'Alla taggar',
	AllTasks: 'Alla uppgifter',
	AllTeamMembers: 'Alla lagmedlemmar',
	AllTypes: 'Alla typer',
	Allocated: 'Tilldelad',
	AllocatedItems: 'Tilldelade föremål',
	AllocationTableEmptyState: 'Inga betalningstilldelningar hittades',
	AllocationTotalWarningMessage: `Det tilldelade beloppet överstiger det totala betalningsbeloppet.
 Granska raderna nedan.`,
	AllowClientsToCancelAnytime: 'Tillåt kunder att avbryta när som helst',
	AllowNewClient: 'Tillåt nya kunder',
	AllowNewClientHelper: 'Nya kunder kan boka denna tjänst',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Tillåt minst {hours} timmar innan mötet',
	AllowToUseSavedCard: 'Tillåt {provider} att använda det sparade kortet i framtiden',
	AllowVideoCalls: 'Tillåt videosamtal',
	AlreadyAdded: 'Redan tillagd',
	AlreadyHasAccess: 'Har tillgång',
	AlreadyHasAccount: 'Har du redan ett konto?',
	Always: 'Alltid',
	AlwaysIgnore: 'Ignorera alltid',
	Amount: 'Belopp',
	AmountDue: 'Förfallna belopp',
	AmountOfReferralRequests: '{amount, plural, one {# remissförfrågan} other {# remissförfrågningar}}',
	AmountPaid: 'Betalt belopp',
	AnalyzingAudio: 'Analyserar ljud...',
	AnalyzingInputContent: 'Analyserar inmatat innehåll...',
	AnalyzingRequest: 'Analyserar begäran...',
	AnalyzingTemplateContent: 'Analyserar mallinnehåll...',
	And: 'och',
	Annually: 'Årligen',
	Anonymous: 'Anonym',
	AnswerExceeded: 'Ditt svar måste vara mindre än 300 tecken.',
	AnyStatus: 'Vilken status som helst',
	AppNotifications: 'Aviseringar',
	AppNotificationsClearanceHeading: 'Bra jobbat! Du har rensat all aktivitet',
	AppNotificationsEmptyHeading: 'Din arbetsplatsaktivitet kommer att visas här inom kort',
	AppNotificationsEmptySubtext: 'Det finns inga åtgärder att vidta för närvarande',
	AppNotificationsIgnoredCount: '{total} ignorerade',
	AppNotificationsUnread: '{total} olästa',
	Append: 'Bifoga',
	Apply: 'Tillämpas',
	ApplyAccountCredit: 'Använd kontokredit',
	ApplyDiscount: 'Använd rabatt',
	ApplyVisualEffects: 'Använd visuella effekter',
	ApplyVisualEffectsNotSupported: 'Använd visuella effekter som inte stöds',
	Appointment: 'Utnämning',
	AppointmentAssignedNotificationSubject: '{actorProfileName} har tilldelat dig {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} har avbokat {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} har bekräftat {appointmentName}',
	AppointmentDetails: 'Utnämningsdetaljer',
	AppointmentLocation: 'Mottagningsställe',
	AppointmentLocationDescription:
		'Hantera dina standardvirtuella och fysiska platser. När en tid bokas kommer dessa platser att tillämpas automatiskt.',
	AppointmentNotFound: 'Mötet hittades inte',
	AppointmentReminder: 'Påminnelse om möte',
	AppointmentReminders: 'Påminnelser om möte',
	AppointmentRemindersInfo:
		'Ställ in automatiska påminnelser för kundmöten för att undvika uteblivna möten och avbokningar',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} har omgjort {appointmentName}',
	AppointmentSaved: 'Mötet sparat',
	AppointmentStatus: 'Utnämningsstatus',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}tim {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}tim {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Mötet har ångrats',
	Appointments: 'Utnämningar',
	Archive: 'Arkiv',
	ArchiveClients: 'Arkivera kunder',
	Archived: 'Arkiverad',
	AreYouAClient: 'Är du kund?',
	AreYouStillThere: 'Är du kvar?',
	AreYouSure: 'Är du säker?',
	Arrangements: 'Arrangemang',
	ArtTherapist: 'Konstterapeut',
	Articles: 'Artiklar',
	Artist: 'Konstnär',
	AskAI: 'Fråga AI',
	AskAiAddFormField: 'Lägg till ett formulärfält',
	AskAiChangeFormality: 'Ändra formalitet',
	AskAiChangeToneToBeMoreProfessional: 'Ändra ton för att bli mer professionell',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Förklara vad detta dokument handlar om',
	AskAiExplainWhatThisImageIsAbout: 'Förklara vad den här bilden handlar om',
	AskAiFixSpellingAndGrammar: 'Fixa stavning och grammatik',
	AskAiGenerateACaptionForThisImage: 'Skapa en bildtext för den här bilden',
	AskAiGenerateFromThisPage: 'Generera från den här sidan',
	AskAiGetStarted: 'Kom igång',
	AskAiGiveItAFriendlyTone: 'Ge det en vänlig ton',
	AskAiGreeting: 'Hej {firstName}! Hur kan jag hjälpa dig idag?',
	AskAiHowCanIHelpWithYourContent: 'Hur kan jag hjälpa till med ditt innehåll?',
	AskAiInsert: 'Infoga',
	AskAiMakeItMoreCasual: 'Gör det mer avslappnat',
	AskAiMakeThisTextMoreConcise: 'Gör den här texten mer kortfattad',
	AskAiMoreProfessional: 'Mer professionell',
	AskAiOpenPreviousNote: 'Öppna föregående anteckning',
	AskAiPondering: 'Funderar',
	AskAiReplace: 'Ersätta',
	AskAiReviewOrEditSelection: 'Granska eller redigera urval',
	AskAiRuminating: 'Idisslande',
	AskAiSeeMore: 'Se mer',
	AskAiSimplifyLanguage: 'Förenkla språket',
	AskAiSomethingWentWrong: 'Något gick fel. Om problemet kvarstår, vänligen kontakta oss via vårt hjälpcenter.',
	AskAiStartWithATemplate: 'Börja med en mall',
	AskAiSuccessfullyCopiedResponse: 'AI-svar har kopierats',
	AskAiSuccessfullyInsertedResponse: 'AI-svar har infogats',
	AskAiSuccessfullyReplacedResponse: 'AI-svar har ersatts',
	AskAiSuggested: 'Föreslog',
	AskAiSummariseTextIntoBulletPoints: 'Sammanfatta text i punkter',
	AskAiSummarizeNote: 'Sammanfatta anteckning',
	AskAiThinking: 'Tänkande',
	AskAiToday: 'Idag {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Vad vill du göra med det här formuläret?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Skriv en professionell anteckning med hjälp av mallen',
	AskAskAiAnything: 'Fråga AI vad som helst',
	AskWriteSearchAnything: `Fråga, skriv '@' eller sök efter vad som helst...`,
	Asking: 'Frågar',
	Assessment: 'Värdering',
	Assessments: 'Bedömningar',
	AssessmentsCategoryDescription: 'För att registrera klientutvärderingar',
	AssignClients: 'Tilldela kunder',
	AssignNewClients: 'Tilldela kunder',
	AssignServices: 'Tilldela tjänster',
	AssignTeam: 'Tilldela team',
	AssignTeamMember: 'Tilldela en gruppmedlem',
	Assigned: 'Tilldelad',
	AssignedClients: 'Tilldelade kunder',
	AssignedServices: 'Tilldelade tjänster',
	AssignedServicesDescription:
		'Visa och hantera dina tilldelade tjänster, justera priserna för att återspegla dina anpassade priser. ',
	AssignedTeam: 'Tilldelat team',
	AthleticTrainer: 'Atletisk tränare',
	AttachFiles: 'Bifoga filer',
	AttachLogo: 'Bifoga',
	Attachment: 'Fastsättning',
	AttachmentBlockedFileType: 'Blockerad av säkerhetsskäl!',
	AttachmentTooLargeFileSize: 'Filen är för stor',
	AttachmentUploadItemComplete: 'Komplett',
	AttachmentUploadItemError: 'Uppladdningen misslyckades',
	AttachmentUploadItemLoading: 'Belastning',
	AttemptingToReconnect: 'Försöker återansluta...',
	Attended: 'Deltog',
	AttendeeBeingMutedTooltip: 'Värden har stängt av dig. Använd "räck upp handen" för att begära att stänga av ljudet',
	AttendeeWithId: 'Deltagare {attendeeId}',
	Attendees: 'Deltagare',
	AttendeesCount: '{count} deltagare',
	Attending: 'Deltar',
	Audiologist: 'Audionom',
	Aunt: 'Moster',
	Australia: 'Australien',
	AuthenticationCode: 'Autentiseringskod',
	AuthoriseProvider: 'Autentisera {provider}',
	AuthorisedProviders: 'Auktoriserade leverantörer',
	AutoDeclineAllFutureOption: 'Endast nya händelser eller möten',
	AutoDeclineAllOption: 'Nya och befintliga evenemang eller möten',
	AutoDeclinePrimaryText: 'Automatiskt avböja evenemang',
	AutoDeclineSecondaryText: 'Evenemang under din frånvaroperiod kommer att automatiskt avböjas.',
	AutogenerateBillings: 'Autogenerera faktureringsdokument',
	AutogenerateBillingsDescription:
		'Automatiska faktureringsdokument kommer att genereras den sista dagen i månaden. Fakturor och superbillkvitton kan skapas manuellt när som helst.',
	AutomateWorkflows: 'Automatisera arbetsflöden',
	AutomaticallySendSuperbill: 'Skicka superbillkvitton automatiskt',
	AutomaticallySendSuperbillHelperText:
		'En superbill är ett detaljerat kvitto på de tjänster som tillhandahålls en kund för försäkringsersättning',
	Automation: 'Automatisering',
	AutomationActionSendEmailLabel: 'Skicka e-post',
	AutomationActionSendSMSLabel: 'Skicka SMS',
	AutomationAndReminders: 'Automatisering ',
	AutomationDeletedSuccessMessage: 'Automatiseringen har raderats',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Tidshändelse',
	AutomationParams_timeUnit: 'Enhet',
	AutomationParams_timeValue: 'Antal',
	AutomationPublishSuccessMessage: 'Automation publicerades framgångsrikt',
	AutomationPublishWarningTooltip:
		'Kontrollera automatiseringskonfigurationen igen och se till att den har konfigurerats korrekt',
	AutomationTriggerEventCancelledDescription: 'Utlöses när en händelse avbryts eller raderas',
	AutomationTriggerEventCancelledLabel: 'Evenemanget inställt',
	AutomationTriggerEventCreatedDescription: 'Utlöses när en händelse skapas',
	AutomationTriggerEventCreatedLabel: 'Nytt evenemang',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Utlöses när en händelse skapas eller uppdateras (förutom när den avbryts)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Ny eller uppdaterad händelse',
	AutomationTriggerEventEndedDescription: 'Utlöses när en händelse slutar',
	AutomationTriggerEventEndedLabel: 'Händelsen avslutades',
	AutomationTriggerEventStartsDescription: 'Utlöses när en viss tid innan en händelse startar',
	AutomationTriggerEventStartsLabel: 'Eventet startar',
	Automations: 'Automatiseringar',
	Availability: 'Tillgänglighet',
	AvailabilityDisableSchedule: 'Inaktivera schema',
	AvailabilityDisabled: 'Inaktiverad',
	AvailabilityEnableSchedule: 'Aktivera schema',
	AvailabilityEnabled: 'Aktiverad',
	AvailabilityNoActiveBanner:
		'Du har stängt av alla dina scheman. Kunder kan inte boka dig online och alla framtida möten måste bekräftas manuellt.',
	AvailabilityNoActiveConfirmationDescription:
		'Om du inaktiverar denna tillgänglighet kommer det att resultera i att du inte har några aktiva scheman. Kunder kommer inte att kunna boka dig online, och alla bokningar som görs av utövare kommer att falla utanför din arbetstid.',
	AvailabilityNoActiveConfirmationProceed: 'Ja, fortsätt',
	AvailabilityNoActiveConfirmationTitle: 'Inga aktiva scheman',
	AvailabilityToggle: 'Aktiverat schema',
	AvailabilityUnsetDate: 'Inget datum satt',
	AvailableLocations: 'Tillgängliga platser',
	AvailablePayers: 'Tillgängliga betalare',
	AvailablePayersEmptyState: 'Inga betalade valda',
	AvailableTimes: 'Tillgängliga tider',
	Back: 'Tillbaka',
	BackHome: 'Hemma igen',
	BackToAppointment: 'Tillbaka till bokning',
	BackToLogin: 'Tillbaka till inloggning',
	BackToMapColumns: 'Tillbaka till kartkolumner',
	BackToTemplates: 'Tillbaka till Mallar',
	BackToUploadFile: 'Tillbaka till Ladda upp fil',
	Banker: 'Bankman',
	BasicBlocks: 'Grundläggande block',
	BeforeAppointment: 'Skicka {deliveryType} påminnelse {interval} {unit} innan mötet',
	BehavioralAnalyst: 'Beteendeanalytiker',
	BehavioralHealthTherapy: 'Beteendehälsoterapi',
	Beta: 'Beta',
	BillTo: 'Bill till',
	BillableItems: 'Fakturerbara föremål',
	BillableItemsEmptyState: 'Inga fakturerbara objekt har hittats',
	Biller: 'Biller',
	Billing: 'Fakturering',
	BillingAddress: 'Faktureringsadress',
	BillingAndReceiptsUnauthorisedMessage:
		'Åtkomst till fakturor och betalningar krävs för att få tillgång till denna information.',
	BillingBillablesTab: 'Fakturerbara',
	BillingClaimsTab: 'Anspråk',
	BillingDetails: 'Faktureringsinformation',
	BillingDocuments: 'Faktureringsdokument',
	BillingDocumentsClaimsTab: 'Anspråk',
	BillingDocumentsEmptyState: 'Inga {tabType} har hittats',
	BillingDocumentsInvoicesTab: 'Fakturor',
	BillingDocumentsSuperbillsTab: 'Superbills',
	BillingInformation: 'Faktureringsinformation',
	BillingInvoicesTab: 'Fakturor',
	BillingItems: 'Faktureringsartiklar',
	BillingPaymentsTab: 'Betalningar',
	BillingPeriod: 'Faktureringsperiod',
	BillingProfile: 'Faktureringsprofil',
	BillingProfileOverridesDescription:
		'Begränsa användningen av den här fakturerings profilen till specifika teammedlemmar',
	BillingProfileOverridesHeader: 'Begränsa åtkomst',
	BillingProfileProviderType: 'Typ av leverantör',
	BillingProfileTypeIndividual: 'Praktiker',
	BillingProfileTypeIndividualSubLabel: 'Typ 1 NPI',
	BillingProfileTypeOrganisation: 'Organisation',
	BillingProfileTypeOrganisationSubLabel: 'Typ 2 NPI',
	BillingProfiles: 'Faktureringsprofiler',
	BillingProfilesEditHeader: 'Redigera {name} fakturerings profil',
	BillingProfilesNewHeader: 'Ny faktureringsprofil',
	BillingProfilesSectionDescription:
		'Hantera din faktureringsinformation för utövare och försäkringsbetalare genom att ställa in faktureringsprofiler som kan tillämpas på fakturor och försäkringsutbetalningar.',
	BillingSearchPlaceholder: 'Sök föremål',
	BillingSettings: 'Faktureringsinställningar',
	BillingSuperbillsTab: 'Superbills',
	BiomedicalEngineer: 'Biomedicinsk ingenjör',
	BlankInvoice: 'Tom faktura',
	BlueShieldProviderNumber: 'Blue Shield-leverantörsnummer',
	Body: 'Kropp',
	Bold: 'Djärv',
	BookAgain: 'Boka igen',
	BookAppointment: 'Boka tid',
	BookableOnline: 'Bokas online',
	BookableOnlineHelper: 'Kunder kan boka denna tjänst online',
	BookedOnline: 'Bokad online',
	Booking: 'Bokning',
	BookingAnalyticsIntegrationPanelDescription:
		'Ställ in Google Tag Manager för att spåra nyckelåtgärder och konverteringar i ditt bokningsflöde online. Samla in värdefull data om användarinteraktioner för att förbättra marknadsföringsinsatser och optimera bokningsupplevelsen.',
	BookingAnalyticsIntegrationPanelTitle: 'Analytics integration',
	BookingAndCancellationPolicies: 'Bokning ',
	BookingButtonEmbed: 'Knapp',
	BookingButtonEmbedDescription: 'Lägger till en onlinebokningsknapp på din webbplats',
	BookingDirectTextLink: 'Direkt textlänk',
	BookingDirectTextLinkDescription: 'Öppnar bokningssidan online',
	BookingFormatLink: 'Formatera länk',
	BookingFormatLinkButtonTitle: 'Knappens titel',
	BookingInlineEmbed: 'Inbäddad inbäddning',
	BookingInlineEmbedDescription: 'Laddar onlinebokningssidan direkt på din hemsida',
	BookingLink: 'Bokningslänk',
	BookingLinkModalCopyText: 'Kopiera',
	BookingLinkModalDescription: 'Tillåt kunder med den här länken att boka en gruppmedlem eller tjänster',
	BookingLinkModalHelpText: 'Lär dig hur du ställer in onlinebokningar',
	BookingLinkModalTitle: 'Dela din bokningslänk',
	BookingPolicies: 'Bokningspolicy',
	BookingPoliciesDescription: 'Ställ in när onlinebokningar kan göras av kunder',
	BookingTimeUnitDays: 'dagar',
	BookingTimeUnitHours: 'timmar',
	BookingTimeUnitMinutes: 'minuter',
	BookingTimeUnitMonths: 'månader',
	BookingTimeUnitWeeks: 'veckor',
	BottomNavBilling: 'Fakturering',
	BottomNavGettingStarted: 'Hem',
	BottomNavMore: 'Mer',
	BottomNavNotes: 'Anteckningar',
	Brands: 'Varumärken',
	Brother: 'Bror',
	BrotherInLaw: 'Svåger',
	BrowseOrDragFileHere: '<link>Bläddra</link> eller dra filen hit',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} min efter',
	BufferAndLabel: 'och',
	BufferAppointmentLabel: 'ett möte',
	BufferBeforeTime: '{time} min före',
	BufferTime: 'Buffertid',
	BufferTimeViewLabel: '{bufferBefore} min före och {bufferAfter} min efter möten',
	BulkArchiveClientsDescription:
		'Är du säker på att du vill arkivera dessa klienter? Du kan återaktivera dem senare.',
	BulkArchiveSuccess: 'Kunder har arkiverats framgångsrikt',
	BulkArchiveUndone: 'Massarkiv ångrat',
	BulkPermanentDeleteDescription: 'Detta kommer att radera **{count} konversationer**. Denna åtgärd kan inte ångras.',
	BulkPermanentDeleteTitle: 'Radera konversationer för alltid',
	BulkUnarchiveSuccess: 'Klienter har avarkiverats framgångsrikt',
	BulletedList: 'Punktlista',
	BusinessAddress: 'Företagsadress',
	BusinessAddressOptional: 'Företagsadress <span>(valfritt)</span>',
	BusinessName: 'Företagsnamn',
	Button: 'Knapp',
	By: 'Av',
	CHAMPUSIdentificationNumber: 'CHAMPUS identifikationsnummer',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC krävs',
	Calendar: 'Kalender',
	CalendarAppSyncFormDescription: 'Synkronisera Carepatron-evenemang till',
	CalendarAppSyncPanelTitle: 'Ansluten app synk',
	CalendarDescription: 'Hantera dina möten eller ställ in personliga uppgifter och påminnelser',
	CalendarDetails: 'Kalenderdetaljer',
	CalendarDetailsDescription: 'Hantera din kalender och visningsinställningar för möten.',
	CalendarScheduleNew: 'Schema nytt',
	CalendarSettings: 'Kalenderinställningar',
	Call: 'Samtal',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> har anslutit till videosamtalet',
	CallChangeLayoutTextContent: 'Urvalet sparas för framtida möten',
	CallIdlePrompt: 'Föredrar du att fortsätta vänta på att gå med, eller vill du försöka igen senare?',
	CallLayoutOptionAuto: 'Bil',
	CallLayoutOptionSidebar: 'Sidofält',
	CallLayoutOptionSpotlight: 'Strålkastare',
	CallLayoutOptionTiled: 'Kaklat',
	CallNoAttendees: 'Inga deltagare i mötet.',
	CallSessionExpiredError: 'Sessionen har löpt ut. Samtalet har avslutats. Försök att gå med igen.',
	CallWithPractitioner: 'Samtal med {practitioner}',
	CallsListCreateButton: 'Nytt samtal',
	CallsListEmptyState: 'Inga aktiva samtal',
	CallsListItemEndCall: 'Avsluta samtalet',
	CamWarningMessage: 'Ett problem har upptäckts med din kamera',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Vänligen aktivera Carepatron-åtkomst till din kamera och mikrofon.
 För mer information, <a>följ den här guiden</a>`,
	CameraAndMicIssueModalTitle: 'Kamera och mikrofon är blockerade',
	CameraQuality: 'Kamerakvalitet',
	CameraSource: 'Kamerakälla',
	CanModifyReadOnlyEvent: 'Du kan inte ändra den här händelsen',
	Canada: 'Kanada',
	Cancel: 'Avboka',
	CancelClientImportDescription: 'Är du säker på att du vill avbryta denna import?',
	CancelClientImportPrimaryAction: 'Ja, avbryt import',
	CancelClientImportSecondaryAction: 'Fortsätt redigera',
	CancelClientImportTitle: 'Avbryt import av klienter',
	CancelImportButton: 'Avbryt import',
	CancelPlan: 'Avbryt plan',
	CancelPlanConfirmation: `Om du avbryter planen debiteras ditt konto automatiskt med alla utestående saldon du har för den här månaden.
 Om du vill nedgradera dina fakturerade användare kan du helt enkelt ta bort teammedlemmar så uppdaterar Carepatron automatiskt ditt prenumerationspris.`,
	CancelSend: 'Avbryt skicka',
	CancelSubscription: 'Avsluta prenumerationen',
	Canceled: 'Avbokad',
	CancellationPolicy: 'Avbokningsregler',
	Cancelled: 'Avbruten',
	CannotContainSpecialCharactersError: 'Kan inte innehålla {specialCharacters}',
	CannotDeleteInvoice: 'Fakturor som betalas via onlinebetalningar kan inte raderas',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> kan inte flyttas ut ur gruppen <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Tjänsten kan inte flyttas utanför samlingarna',
	CapeTown: 'kapstaden',
	Caption: 'Rubrik',
	CaptureNameFieldLabel: 'Namnet du vill att andra ska referera till dig',
	CapturePaymentMethod: 'Fånga betalningsmetod',
	CapturingAudio: 'Fångar ljud',
	CapturingSignature: 'Fångar signatur...',
	CardInformation: 'Kortinformation',
	CardNumberRequired: 'Kortnummer krävs',
	CardiacRehabilitationSpecialist: 'Hjärtrehabiliteringsspecialist',
	Cardiologist: 'Kardiolog',
	CareAiNoConversations: 'Inga konversationer ännu',
	CareAiNoConversationsDescription: 'Starta en konversation med {aiName} för att komma igång',
	CareAssistant: 'Vårdassistent',
	CareManager: 'Vårdchef',
	Caregiver: 'Vårdgivare',
	CaregiverCreateModalDescription:
		'Genom att lägga till personal som administratörer kan de skapa och hantera vårdberättelser. Det ger dem också full tillgång att skapa och hantera klienter.',
	CaregiverCreateModalTitle: 'Ny lagmedlem',
	CaregiverListCantAddStaffInfoTitle:
		'Du har nått det maximala antalet anställda för ditt abonnemang. Uppgradera din plan för att lägga till fler anställda.',
	CaregiverListCreateButton: 'Ny lagmedlem',
	CaregiverListEmptyState: 'Inga vårdgivare har lagts till',
	CaregiversListItemRemoveStaff: 'Ta bort personal',
	CarepatronApp: 'Carepatron app',
	CarepatronCommunity: 'Gemenskap',
	CarepatronFieldAddress: 'Adress',
	CarepatronFieldAssignedStaff: 'Tilldelad personal',
	CarepatronFieldBirthDate: 'Födelsedatum',
	CarepatronFieldEmail: 'E-post',
	CarepatronFieldEmploymentStatus: 'Anställningsstatus',
	CarepatronFieldEthnicity: 'Etnicitet',
	CarepatronFieldFirstName: 'Förnamn',
	CarepatronFieldGender: 'Kön',
	CarepatronFieldIdentificationNumber: 'Identifikationsnummer',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Märka',
	CarepatronFieldLastName: 'Efternamn',
	CarepatronFieldLivingArrangements: 'Boende arrangemang',
	CarepatronFieldMiddleNames: 'Mellannamn',
	CarepatronFieldOccupation: 'Ockupation',
	CarepatronFieldPhoneNumber: 'Telefonnummer',
	CarepatronFieldRelationshipStatus: 'Relationsstatus',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 statusar max.',
	CarepatronFieldTags: 'Taggar',
	CarepatronFields: 'Vårdnadshavare fält',
	Cash: 'Kontanter',
	Category: 'Kategori',
	CategoryInputPlaceholder: 'Välj en mallkategori',
	CenterAlign: 'Centrera',
	Central: 'Central',
	ChangeLayout: 'Ändra layout',
	ChangeLogo: 'Ändra',
	ChangePassword: 'Byt lösenord',
	ChangePasswordFailureSnackbar:
		'Tyvärr, ditt lösenord har inte ändrats. Kontrollera att ditt gamla lösenord är korrekt.',
	ChangePasswordHelperInfo: 'Minsta längd på {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Lösenordet har ändrats framgångsrikt! Nästa gång du loggar in, se till att använda det lösenordet.',
	ChangeSubscription: 'Ändra prenumeration',
	ChangesNotAllowed: 'Det går inte att göra ändringar i det här fältet',
	ChargesDisabled: 'Avgifter inaktiverade',
	ChargesEnabled: 'Avgifter aktiverade',
	ChargesStatus: 'Laddningsstatus',
	ChartAndDiagram: 'Diagram/Diagram',
	ChartsAndDiagramsCategoryDescription: 'För att illustrera klientdata och framsteg',
	ChatEditMessage: 'Redigera meddelande',
	ChatReplyTo: 'Svara {name}',
	ChatTypeMessageTo: 'Meddelande {name}',
	Check: 'Kontrollera',
	CheckList: 'Checklista',
	Chef: 'Kock',
	Chiropractic: 'Kiropraktik',
	Chiropractor: 'Kiropraktor',
	Chiropractors: 'Kiropraktorer',
	ChooseACollection: 'Välj en samling',
	ChooseAContact: 'Välj en kontakt',
	ChooseAccountTypeHeader: 'Vilken beskriver dig bäst?',
	ChooseAction: 'Välj åtgärd',
	ChooseAnAccount: 'Välj ett konto',
	ChooseAnOption: 'Välj ett alternativ',
	ChooseBillingProfile: 'Välj faktureringsprofil',
	ChooseClaim: 'Välj anspråk',
	ChooseCollection: 'Välj samling',
	ChooseColor: 'Välj färg',
	ChooseCustomDate: 'Välj anpassat datum',
	ChooseDateAndTime: 'Välj datum och tid',
	ChooseDxCodes: 'Välj diagnoskoder',
	ChooseEventType: 'Välj händelsetyp',
	ChooseFileButton: 'Välj en fil',
	ChooseFolder: 'Välj mapp',
	ChooseInbox: 'Välj inkorg',
	ChooseMethod: 'Välj metod',
	ChooseNewOwner: 'Välj ny ägare',
	ChooseOrganization: 'Välj Organisation',
	ChoosePassword: 'Välj lösenord',
	ChoosePayer: 'Välj betalare',
	ChoosePaymentMethod: 'Välj en betalningsmetod',
	ChoosePhysicalOrRemoteLocations: 'Ange eller välj plats',
	ChoosePlan: 'Välj {plan}',
	ChooseProfessional: 'Välj Professional',
	ChooseServices: 'Välj tjänster',
	ChooseSource: 'Välj källa',
	ChooseSourceDescription:
		'Välj varifrån du importerar klienter – om det är från en fil eller en annan mjukvaruplattform.',
	ChooseTags: 'Välj taggar',
	ChooseTaxName: 'Välj skattenamn',
	ChooseTeamMembers: 'Välj teammedlemmar',
	ChooseTheme: 'Välj tema',
	ChooseTrigger: 'Välj trigger',
	ChooseYourProvider: 'Välj din leverantör',
	CircularProgressWithLabel: '{value}%',
	City: 'Stad',
	CivilEngineer: 'Civilingenjör',
	Claim: 'Krav',
	ClaimAddReferringProvider: 'Lägg till remitterande vårdgivare',
	ClaimAddRenderingProvider: 'Lägg till renderingsleverantör',
	ClaimAmount: 'Kravbelopp',
	ClaimAmountPaidHelpContent:
		'Beloppet som betalas är den betalning som erhållits från patienten eller andra betalare. Ange det totala belopp som patienten och/eller andra betalare endast betalade för de täckta tjänsterna.',
	ClaimAmountPaidHelpSubtitle: 'Fält 29',
	ClaimAmountPaidHelpTitle: 'Betalt belopp',
	ClaimBillingProfileTypeIndividual: 'Enskild',
	ClaimBillingProfileTypeOrganisation: 'Organisation',
	ClaimChooseRenderingProviderOrTeamMember: 'Välj renderingsleverantör eller teammedlem',
	ClaimClientInsurancePolicies: 'Kundförsäkringar',
	ClaimCreatedAction: '<mark>Anspråk {claimNumber}</mark> skapat',
	ClaimDeniedAction: '<mark>Anspråk {claimNumber}</mark> avslogs av <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Sök ICD 10 diagnoskoder',
	ClaimDiagnosisSelectorHelpContent: `"Diagnosen eller skadan" är tecknet, symtomet, klagomålet eller tillståndet hos patienten som relaterar till tjänsten/tjänsterna i anspråket.
 Upp till 12 ICD 10-diagnoskoder kan väljas.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Fält 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnos eller skada',
	ClaimDiagnosticCodesEmptyError: 'Minst en diagnoskod krävs',
	ClaimDoIncludeReferrerInformation: 'Inkludera hänvisningsinformation om CMS1500',
	ClaimERAReceivedAction: 'Elektronisk remittering mottagen från <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Elektronisk inbetalning mottagen	<mark>Betalning {paymentReference}</mark> för <b>{paymentAmount}</b> från <b>{payerNumber} {payerName}</b> har registrerats',
	ClaimExportedAction: '<mark>Anspråk {claimNumber}</mark> exporterades som <b>{attachmentType}</b>',
	ClaimFieldClient: 'Kundens eller kontaktens namn',
	ClaimFieldClientAddress: 'Kundadress',
	ClaimFieldClientAddressDescription:
		'Ange kundens adress. Den första raden är för gatuadressen. Använd inte skiljetecken (komma eller punkter) eller några symboler i adressen. Om du rapporterar en utländsk adress, kontakta betalaren för specifika rapporteringsinstruktioner.',
	ClaimFieldClientAddressSubtitle: 'Fält 5',
	ClaimFieldClientDateOfBirth: 'Kundens födelsedatum',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Fält 3',
	ClaimFieldClientDateOfBirthDescription:
		'Ange kundens 8-siffriga födelsedatum (MM/DD/ÅÅÅÅ). Klientens födelsedatum är information som kommer att identifiera kunden och det särskiljer personer med liknande namn.',
	ClaimFieldClientDescription: 'Klientens namn är namnet på den person som fick behandlingen eller förnödenheterna.',
	ClaimFieldClientSexDescription:
		'"Sex" är information som kommer att identifiera klienten och som särskiljer personer med liknande namn.',
	ClaimFieldClientSubtitle: 'Fält 2',
	ClaimFiling: 'Anspråkshantering',
	ClaimHistorySubtitle: 'Försäkring • Anspråk {number}',
	ClaimIncidentAutoAccident: 'Bilolycka?',
	ClaimIncidentConditionRelatedTo: 'Är klientens tillstånd relaterat till',
	ClaimIncidentConditionRelatedToHelpContent:
		'Denna information indikerar om klientens sjukdom eller skada är relaterad till anställningen, bilolyckan eller annan olycka. Anställning (nuvarande eller tidigare) skulle indikera att tillståndet är relaterat till klientens arbete eller arbetsplats. Bilolycka skulle indikera att förhållandena är resultatet av en bilolycka. Annan olycka skulle tyda på att tillståndet är resultatet av någon annan typ av olycka.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Fält 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Är klientens tillstånd relaterat till',
	ClaimIncidentCurrentIllness: 'Aktuell sjukdom, skada eller graviditet',
	ClaimIncidentCurrentIllnessHelpContent:
		'Datumet för aktuell sjukdom, skada eller graviditet identifierar det första datumet för sjukdomsdebut, det faktiska datumet för skadan eller LMP för graviditet.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Fält 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Datum för aktuell sjukdom, skada eller graviditet (LMP)',
	ClaimIncidentDate: 'Datum',
	ClaimIncidentDateFrom: 'Datum från',
	ClaimIncidentDateTo: 'Dejta till',
	ClaimIncidentEmploymentRelated: 'Sysselsättning',
	ClaimIncidentEmploymentRelatedDesc: '(Nuvarande eller tidigare)',
	ClaimIncidentHospitalizationDatesLabel: 'Sjukhusinläggningsdatum relaterade till nuvarande tjänster',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Sjukhusinläggningsdatum relaterade till aktuella tjänster avser en klientvistelse och angav intagnings- och utskrivningsdatum som är associerade med tjänsten/tjänsterna på skadeanmälan.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Fält 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Sjukhusinläggningsdatum relaterade till nuvarande tjänster',
	ClaimIncidentInformation: 'Incidentinformation',
	ClaimIncidentOtherAccident: 'Annan olycka?',
	ClaimIncidentOtherAssociatedDate: 'Annat associerat datum',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Det andra datumet identifierar ytterligare datuminformation om klientens tillstånd eller behandling.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Fält 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Annat datum',
	ClaimIncidentQualifier: 'Kval',
	ClaimIncidentQualifierPlaceholder: 'Välj kval',
	ClaimIncidentUnableToWorkDatesLabel: 'Kunden kunde inte arbeta i nuvarande yrke',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Datum klienten var oförmögen att arbeta i nuvarande sysselsättning är den tidsperiod klienten är eller var oförmögen att arbeta',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Fält 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Datum klienten kunde inte arbeta i nuvarande yrke',
	ClaimIncludeReferrerInformation: 'Inkludera hänvisningsinformation om CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Den typ av sjukförsäkring som gäller för detta anspråk. Annat indikerar sjukförsäkring inklusive HMO, kommersiell försäkring, bilolycka, ansvar eller arbetsskadeersättning.
 Denna information riktar anspråket till rätt program och kan fastställa primärt ansvar.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Fält 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Täckningstyp',
	ClaimInsuranceGroupIdHelpContent: `Ange den försäkrades försäkrings- eller gruppnummer som det står på den försäkrades vårdlegitimation.

 "Försäkrades försäkrings-, grupp- eller FECA-nummer" är den alfanumeriska identifieraren för hälso-, bil- eller annan försäkringsplan. FECA-numret är den 9-teckens alfanumeriska identifieraren som tilldelas en patient som hävdar arbetsrelaterat tillstånd.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Fält 11',
	ClaimInsuranceGroupIdHelpTitle: 'Försäkrades försäkrings-, grupp- eller FECA-nummer',
	ClaimInsuranceMemberIdHelpContent: `Ange den försäkrades ID-nummer som framgår av den försäkrades ID-kort för den betalare som reklamationen ställs till.
 Om patienten har ett unikt medlemsidentifikationsnummer tilldelat av betalaren anger du det numret i detta fält.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Fält 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Försäkrades medlems-ID',
	ClaimInsurancePayer: 'Försäkringsbetalare',
	ClaimManualPaymentAction: '<mark>Betalning {paymentReference}</mark> för <b>{paymentAmount}</b> registrerad',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Ytterligare information om anspråk',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Vänligen se de aktuella instruktionerna från den offentliga eller privata betalaren angående användningen av detta fält. Rapportera lämplig kvalificerare, om tillgänglig, för den information som anges.Ange inte ett mellanslag, bindestreck eller annan separator mellan kvalificeraren och informationen.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Fält 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Ytterligare kravuppgifter',
	ClaimMiscClaimCodes: 'Anspråk koder',
	ClaimMiscOriginalReferenceNumber: 'Ursprungligt referensnummer',
	ClaimMiscPatientsAccountNumber: 'Patientens kontonummer',
	ClaimMiscPatientsAccountNumberHelpContent:
		'Patientens kontonummer är den identifierare som tilldelats av leverantören.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Fält 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Patientens kontonummer',
	ClaimMiscPriorAuthorizationNumber: 'Förhandsauktoriseringsnummer',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Förhandsauktorisationsnumret är det nummer som betalaren tilldelas som auktoriserar tjänsten/tjänsterna.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Fält 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Förhandsauktoriseringsnummer',
	ClaimMiscResubmissionCode: 'Återinlämningskod',
	ClaimMiscResubmissionCodeHelpContent:
		'Återinlämning betyder koden och det ursprungliga referensnumret som tilldelats av destinationsbetalaren eller mottagaren för att indikera ett tidigare inlämnat krav eller möte.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Fält 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Återinlämning och/eller originalreferensnummer',
	ClaimNumber: 'Ansökningsnummer',
	ClaimNumberFormat: 'Skadeanmälan #{number}',
	ClaimOrderingProvider: 'Beställningsleverantör',
	ClaimOtherId: 'Annat ID',
	ClaimOtherIdPlaceholder: 'Välj ett alternativ',
	ClaimOtherIdQualifier: 'Annan ID-kvalificerare',
	ClaimOtherIdQualifierPlaceholder: 'Välj ID-kvalificerare',
	ClaimPlaceOfService: 'Plats för tjänst',
	ClaimPlaceOfServicePlaceholder: 'Lägg till POS',
	ClaimPolicyHolderRelationship: 'Försäkringstagarens förhållande',
	ClaimPolicyInformation: 'Policyinformation',
	ClaimPolicyTelephone: 'Telefon (inkludera riktnummer)',
	ClaimReceivedAction: '<mark>Anspråk {claimNumber}</mark> mottaget av <b>{payerNumber} {payerName}</b>',
	ClaimReferringProvider: 'Remitterande leverantör',
	ClaimReferringProviderEmpty: 'Ingen remitterande läkare/er tillagd',
	ClaimReferringProviderHelpContent:
		'Det angivna namnet är den hänvisande leverantören, beställningsleverantören eller övervakande leverantören som hänvisade, beställde eller övervakade tjänsten/tjänsterna eller leveranserna i anspråket. Kvalificeringen anger rollen för den leverantör som rapporteras.',
	ClaimReferringProviderHelpSubtitle: 'Fält 17',
	ClaimReferringProviderHelpTitle: 'Namn på hänvisande leverantör eller källa',
	ClaimReferringProviderQualifier: 'Kval',
	ClaimReferringProviderQualifierPlaceholder: 'Välj kval',
	ClaimRejectedAction: '<mark>Anspråk {claimNumber}</mark> avvisades av <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'ID-nummer',
	ClaimRenderingProviderOrTeamMember: 'Återgivningsleverantör eller teammedlem',
	ClaimRestoredAction: '<mark>Anspråk {claimNumber}</mark> återställdes',
	ClaimServiceFacility: 'Serviceanläggning',
	ClaimServiceFacilityLocationHelpContent:
		'Namnet och adressen till anläggningen där tjänsterna utfördes identifierar platsen där tjänsten/tjänsterna tillhandahölls.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a och 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Fält 32, 32a och 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Serviceanläggning',
	ClaimServiceFacilityPlaceholder: 'Välj serviceanläggning eller plats',
	ClaimServiceLabChargesHelpContent: `Fyll i det här fältet när du gör anspråk på köpta tjänster som tillhandahålls av en annan enhet än faktureringsleverantören.
 Varje köpt tjänst måste rapporteras på ett separat krav eftersom endast en debitering kan anges på CMS1500-formuläret.`,
	ClaimServiceLabChargesHelpSubtitle: 'Fält 20',
	ClaimServiceLabChargesHelpTitle: 'Utanför labbavgifter',
	ClaimServiceLineServiceHelpContent:
		'"Procedurer, tjänster eller förnödenheter" identifierar de medicinska tjänster och procedurer som tillhandahålls patienten.',
	ClaimServiceLineServiceHelpSubtitle: 'Fält 24d',
	ClaimServiceLineServiceHelpTitle: 'Rutiner, tjänster eller tillbehör',
	ClaimServiceLinesEmptyError: 'Minst en tjänstlinje krävs',
	ClaimServiceSupplementaryInfoHelpContent: `Lägg till ytterligare beskrivning av tjänster som tillhandahålls med tillämpliga kvalificerare.
 Ange inte ett mellanslag, bindestreck eller annan avgränsare mellan kvalet och informationen.

 För fullständiga instruktioner om hur du lägger till ytterligare information, läs instruktionerna för CMS 1500-anspråksformuläret.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Fält 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Kompletterande info',
	ClaimSettingsBillingMethodTitle: 'Kundens faktureringsmetod',
	ClaimSettingsClientSignatureDescription:
		'Jag har samtycke till att lämna ut medicinsk eller annan information som är nödvändig för att behandla försäkringsanspråk.',
	ClaimSettingsClientSignatureTitle: 'Klientsignatur på fil',
	ClaimSettingsConsentLabel: 'Samtycke krävs för att behandla försäkringskrav:',
	ClaimSettingsDescription: 'Välj klientens faktureringsmetod för att säkerställa smidig betalningshantering:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} har en aktiv försäkringspolis. För att aktivera försäkringsfakturering, uppdatera klientens faktureringsmetod till Försäkring.',
	ClaimSettingsInsuranceDescription: 'Kostnader som ersätts av försäkringen',
	ClaimSettingsInsuranceTitle: 'Försäkring',
	ClaimSettingsNoPoliciesAlertDescription: 'Lägg till en försäkring för att möjliggöra försäkringskrav.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Jag har samtycke till att ta emot försäkringsersättningar för tillhandahållna tjänster.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Försäkringstagarens underskrift på fil',
	ClaimSettingsSelfPayDescription: 'Kunden betalar för möten',
	ClaimSettingsSelfPayTitle: 'Egen betalning',
	ClaimSettingsTitle: 'Anspråksinställningar',
	ClaimSexSelectorPlaceholder: 'Man / Kvinna',
	ClaimStatusChangedAction: '<mark>Anspråk {claimNumber}</mark> status uppdaterad',
	ClaimSubmittedAction:
		'<mark>Anspråk {claimNumber}</mark> skickat till <b>{payerClearingHouse}</b> för <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Anspråk #{claimNumber}',
	ClaimSupervisingProvider: 'Övervakande leverantör',
	ClaimSupplementaryInfo: 'Kompletterande info',
	ClaimSupplementaryInfoPlaceholder: 'Lägg till kompletterande information',
	ClaimTrashedAction: '<mark>Ärende {claimNumber}</mark> togs bort',
	ClaimValidationFailure: 'Misslyckades med att validera anspråk',
	ClaimsEmptyStateDescription: 'Inga anspråk har hittats.',
	ClainInsuranceTelephone: 'Försäkringstelefon (inkludera riktnummer)',
	Classic: 'Klassisk',
	Clear: 'Rensa',
	ClearAll: 'Rensa allt',
	ClearSearchFilter: 'Rensa',
	ClearingHouse: 'Rensningshus',
	ClearingHouseClaimId: 'Claim.MD-ID',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Rensningshusreferens',
	ClearingHouseUnavailableError: 'Clearinghouset är för närvarande inte tillgängligt. Försök igen senare.',
	ClickToUpload: 'Klicka för att ladda upp',
	Client: 'Klient',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} lade till {noteTitle, select, undefined { en anteckning } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Välj kunder och deras relationer',
	ClientAndRelationshipSelectorTitle: 'Alla kunder och deras relationer',
	ClientAndRelationshipSelectorTitle1: 'Alla relationer för ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Om du väntar på ett videosamtal kommer det att dyka upp här inom kort. Om du har några problem, kontakta den person som initierade det.',
	ClientAppSubHeaderMyDocumentation: 'Min dokumentation',
	ClientAppointment: 'Kundutnämning',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} har bokat {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Inga möten har hittats',
	ClientAppointmentsEmptyStateTitle: 'Håll koll på dina kunders kommande och historiska möten och deras närvaro',
	ClientArchivedSuccessfulSnackbar: 'Framgångsrikt arkiverat <b>{name}</b>',
	ClientBalance: 'Kundsaldot',
	ClientBilling: 'Fakturering',
	ClientBillingAddPaymentMethodDescription:
		'Lägg till och hantera din kunds betalningsmetoder för att effektivisera deras fakturering och fakturering.',
	ClientBillingAndPaymentDueDate: 'Förfallodatum',
	ClientBillingAndPaymentHistory: 'Fakturerings- och betalningshistorik',
	ClientBillingAndPaymentInvoices: 'Fakturor',
	ClientBillingAndPaymentIssueDate: 'Utgivningsdatum',
	ClientBillingAndPaymentPrice: 'Pris',
	ClientBillingAndPaymentReceipt: 'Mottagande',
	ClientBillingAndPaymentServices: 'Tjänster',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Team {count, plural, one {medlem} other {medlemmar}} tilldelad!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Team {count, plural, one {medlem} other {medlemmar}} otilldelade!',
	ClientBulkTagsAddedSuccessSnackbar: 'Taggar har lagts till!',
	ClientDuplicatesDeviewDescription:
		'Slå samman flera klientposter till en för att förena all data – anteckningar, dokument, möten, fakturor och konversationer.',
	ClientDuplicatesPageMergeHeader: 'Välj vilken data du vill behålla',
	ClientDuplicatesReviewHeader: 'Jämför potentiella dubbletter av poster för sammanslagning',
	ClientEmailChangeWarningDescription:
		'Uppdatering av klientens e-post kommer att ta bort deras åtkomst till all delad dokumentation och ger åtkomst till användaren med den nya e-postadressen',
	ClientFieldDateDescription: 'Formatera datum',
	ClientFieldDateLabel: 'Datum',
	ClientFieldDateRangeDescription: 'En rad datum',
	ClientFieldDateRangeLabel: 'Datumintervall',
	ClientFieldDateShowDateDescription: 'ex 29 år',
	ClientFieldDateShowDateRangeDescription: 'ex 2 veckor',
	ClientFieldEmailDescription: 'E-postadress',
	ClientFieldEmailLabel: 'E-post',
	ClientFieldLabel: 'Fältetikett',
	ClientFieldLinearScaleDescription: 'Skalalternativ 1-10',
	ClientFieldLinearScaleLabel: 'Linjär skala',
	ClientFieldLocationDescription: 'Fysisk adress eller postadress',
	ClientFieldLocationLabel: 'Plats',
	ClientFieldLongTextDescription: 'Långt textområde',
	ClientFieldLongTextLabel: 'Stycke',
	ClientFieldMultipleChoiceDropdownDescription: 'Välj flera alternativ från listan',
	ClientFieldMultipleChoiceDropdownLabel: 'Flervalsrullgardinsmeny',
	ClientFieldPhoneNumberDescription: 'Telefonnummer',
	ClientFieldPhoneNumberLabel: 'Telefon',
	ClientFieldPlaceholder: 'Välj en klientfältstyp',
	ClientFieldSingleChoiceDropdownDescription: 'Välj bara ett alternativ från listan',
	ClientFieldSingleChoiceDropdownLabel: 'Envalsrullgardinsmeny',
	ClientFieldTextDescription: 'Textinmatningsfält',
	ClientFieldTextLabel: 'Text',
	ClientFieldYesOrNoDescription: 'Välj mellan ja eller nej alternativ',
	ClientFieldYesOrNoLabel: 'Ja | Inga',
	ClientFileFormAccessLevelDescription:
		'Du och teamet har alltid tillgång till filer du laddar upp. Du kan välja att dela denna fil med klienten och/eller deras relationer',
	ClientFileSavedSuccessSnackbar: 'Filen sparad!',
	ClientFilesPageEmptyStateText: 'Inga filer laddade upp',
	ClientFilesPageUploadFileButton: 'Ladda upp filer',
	ClientHeaderBilling: 'Fakturering',
	ClientHeaderBillingAndReceipts: 'Fakturering ',
	ClientHeaderDocumentation: 'Dokumentation',
	ClientHeaderDocuments: 'Dokument',
	ClientHeaderFile: 'Dokumentera',
	ClientHeaderHistory: 'Medicinsk historia',
	ClientHeaderInbox: 'Inkorg',
	ClientHeaderNote: 'Notera',
	ClientHeaderOverview: 'Översikt',
	ClientHeaderProfile: 'Personlig',
	ClientHeaderRelationship: 'Relation',
	ClientHeaderRelationships: 'Relationer',
	ClientId: 'Klient-ID',
	ClientImportProcessingDescription: 'Filen bearbetas fortfarande. Vi meddelar dig när detta är klart.',
	ClientImportReadyForMappingDescription:
		'Vi har slutfört förbehandlingen av din fil. Vill du mappa kolumner för att slutföra denna import?',
	ClientImportReadyForMappingNotificationSubject:
		'Klientimportförbehandlingen är klar. Filen är nu redo för mappning.',
	ClientInAppMessaging: 'Klientmeddelanden i appen',
	ClientInfoAddField: 'Lägg till ytterligare ett fält',
	ClientInfoAddRow: 'Lägg till rad',
	ClientInfoAlertMessage: 'All information som fylls i i detta avsnitt kommer att fylla i klientposten.',
	ClientInfoFormPrimaryText: 'Kundinformation',
	ClientInfoFormSecondaryText: 'Samla kontaktuppgifter',
	ClientInfoPlaceholder: `Kundens namn, e-postadress, telefonnummer
 Fysisk adress,
 Födelsedatum`,
	ClientInformation: 'Kundinformation',
	ClientInsuranceTabLabel: 'Försäkring',
	ClientIntakeFormsNotSupported: `Formulärmallar stöds för närvarande inte genom kundintag.
 Skapa och dela dem som klientanteckningar istället.`,
	ClientIntakeModalDescription:
		'Ett e-postmeddelande kommer att skickas till din klient och ber dem att fylla i sin profil, ladda upp relevanta medicinska eller remissdokument. De kommer att få åtkomst till klientportalen.',
	ClientIntakeModalTitle: 'Skicka intag till {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Framgång! Ditt intag har sparats.',
	ClientIntakeSuccessSnackbar: 'Framgång! Ditt intag har sparats och ett bekräftelsemail har skickats.',
	ClientIsChargedProcessingFee: 'Dina kunder kommer att betala hanteringsavgiften',
	ClientListCreateButton: 'Ny kund',
	ClientListEmptyState: 'Inga kunder har lagts till',
	ClientListPageItemArchive: 'Ta bort klienten',
	ClientListPageItemRemoveAccess: 'Ta bort min åtkomst',
	ClientLocalizationPanelDescription: 'Klientens föredragna språk och tidszon.',
	ClientLocalizationPanelTitle: 'Språk och tidszon',
	ClientManagementAndEHR: 'Kundhantering ',
	ClientMergeResultSummaryBanner:
		'Sammanfoga poster samlar all klientdata, inklusive anteckningar, dokument, möten, fakturor och konversationer. Verifiera noggrannhet innan du fortsätter.',
	ClientMergeResultSummaryTitle: 'Sammanfattning av sammanslagning',
	ClientModalTitle: 'Ny kund',
	ClientMustHaveEmaillAccessErrorText: 'Kunder/kontakter utan e-post',
	ClientMustHavePortalAccessErrorText: 'Kunder/kontakter kommer att krävas för att registrera sig',
	ClientMustHaveZoomAppConnectedErrorText: 'Anslut Zoom via Inställningar > Anslutna appar',
	ClientNameFormat: 'Klientnamnsformat',
	ClientNotFormAccessLevel: 'Visas av:',
	ClientNotFormAccessLevelDescription:
		'Du och teamet har alltid tillgång till anteckningar som ni publicerar. Du kan välja att dela denna anteckning med kunden och/eller deras relationer',
	ClientNotRegistered: 'Ej registrerad',
	ClientNoteFormAddFileButton: 'Bifoga filer',
	ClientNoteFormChooseAClient: 'Välj en kund/kontakt för att fortsätta',
	ClientNoteFormContent: 'Innehåll',
	ClientNoteItemDeleteConfirmationModalDescription:
		'När den väl har tagits bort kan du inte hämta den här anteckningen igen.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Anteckning publicerad och låst.',
	ClientNotePublishedSuccessSnackbar: 'Obs publicerad!',
	ClientNotes: 'Kundanteckningar',
	ClientNotesEmptyStateText:
		'För att lägga till anteckningar, gå till en kunds profil och klicka på fliken Anteckningar.',
	ClientOnboardingChoosePasswordTitle1: 'Nästan klart!',
	ClientOnboardingChoosePasswordTitle2: 'Välj ett lösenord',
	ClientOnboardingCompleteIntake: 'Fullständigt intag',
	ClientOnboardingConfirmationScreenText:
		'Du har tillhandahållit all information som {providerName} behöver.	Bekräfta din e-postadress för att starta din onboarding. Om du inte får den direkt, kolla din skräppostmapp.',
	ClientOnboardingConfirmationScreenTitle: 'Stor! Kontrollera din inkorg.',
	ClientOnboardingDashboardButton: 'Gå till Dashboard',
	ClientOnboardingHealthRecordsDesc1: 'Vill du dela några rekommendationsbrev, dokument med {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Lägg till beskrivning (valfritt)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentation',
	ClientOnboardingPasswordRequirements: 'Krav',
	ClientOnboardingPasswordRequirementsConditions1: 'Minst 9 tecken krävs',
	ClientOnboardingProviderIntroSignupButton: 'Anmäl mig själv',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Registrera dig för en familjemedlem',
	ClientOnboardingProviderIntroTitle: '{name} har bjudit in dig att gå med i deras Carepatron-plattform',
	ClientOnboardingRegistrationInstructions: 'Ange dina personuppgifter nedan.',
	ClientOnboardingRegistrationTitle: 'Först behöver vi lite personliga detaljer',
	ClientOnboardingStepFormsAndAgreements: 'Blanketter och avtal',
	ClientOnboardingStepFormsAndAgreementsDesc1: 'Vänligen fyll i följande formulär för {providerName}s intagsprocess',
	ClientOnboardingStepHealthDetails: 'Hälsoinformation',
	ClientOnboardingStepPassword: 'Lösenord',
	ClientOnboardingStepYourDetails: 'Dina detaljer',
	ClientPaymentMethodDescription:
		'Spara en betalningsmetod mot din profil för att göra din nästa mötesbokning och fakturering snabbare och säkrare.',
	ClientPortal: 'Kundportal',
	ClientPortalDashboardEmptyDescription: 'Din möteshistorik och din närvaro kommer att visas här.',
	ClientPortalDashboardEmptyTitle:
		'Håll koll på alla kommande, begärda och tidigare möten tillsammans med din närvaro',
	ClientPreferredNotificationPanelDescription:
		'Hantera din kunds föredragna metod för att ta emot uppdateringar och aviseringar via:',
	ClientPreferredNotificationPanelTitle: 'Föredragen aviseringsmetod',
	ClientProcessingFee: 'Betalningen inkluderar ({currencyCode}) {amount} i transaktionsavgift',
	ClientProfileAddress: 'Adress',
	ClientProfileDOB: 'Födelsedatum',
	ClientProfileEmailHelperText: 'Att lägga till ett e-postmeddelande ger portalåtkomst',
	ClientProfileEmailHelperTextMoreInfo:
		'Genom att ge klienten åtkomst till portalen kan teammedlemmar dela anteckningar, filer och annan dokumentation',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Identifikationsnummer',
	ClientRelationshipsAddClientOwnerButton: 'Bjud in kunden',
	ClientRelationshipsAddFamilyButton: 'Bjud in familjemedlem',
	ClientRelationshipsAddStaffButton: 'Lägg till personalåtkomst',
	ClientRelationshipsEmptyStateText: 'Inga relationer har lagts till',
	ClientRemovedSuccessSnackbar: 'Klienten har tagits bort.',
	ClientResponsibility: 'Klientansvar',
	ClientSavedSuccessSnackbar: 'Klienten har sparats.',
	ClientTableClientName: 'Kundens namn',
	ClientTablePhone: 'Telefon',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Framgångsrikt oarkiverade **{name}**',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} redigerade {noteTitle, select, undefined { en anteckning } other {{noteTitle}}}',
	ClientView: 'Kundvy',
	Clients: 'Kunder',
	ClientsTable: 'Kundtabell',
	ClinicalFormat: 'Kliniskt format',
	ClinicalPsychologist: 'Klinisk psykolog',
	Close: 'Nära',
	CloseImportClientsModal: 'Är du säker på att du vill avbryta import av klienter?',
	CloseReactions: 'Nära reaktioner',
	Closed: 'Stängd',
	Coaching: 'Coaching',
	Code: 'Koda',
	CodeErrorMessage: 'Kod krävs',
	CodePlaceholder: 'Koda',
	Coinsurance: 'Samförsäkring',
	Collection: 'Samling',
	CollectionName: 'Samlingens namn',
	Collections: 'Samlingar',
	ColorAppointmentsBy: 'Färgbokningar av',
	ColorTheme: 'Färgtema',
	ColourCalendarBy: 'Färgkalender av',
	ComingSoon: 'Kommer snart',
	Community: 'Gemenskap',
	CommunityHealthLead: 'Samhällshälsoledare',
	CommunityHealthWorker: 'Socialvårdare',
	CommunityTemplatesSectionDescription: 'Skapad av Carepatron-communityn',
	CommunityTemplatesSectionTitle: 'Gemenskap',
	CommunityUser: 'Community-användare',
	Complete: 'Komplett',
	CompleteAndLock: 'Komplettera och lås',
	CompleteSetup: 'Slutför installationen',
	CompleteSetupSuccessDescription: 'Du har slutfört några viktiga steg mot att bemästra Carepatron.',
	CompleteSetupSuccessDescription2: 'Lås upp fler sätt att effektivisera din verksamhet och stödja dina klienter.',
	CompleteSetupSuccessTitle: 'Succé! Du gör fantastiskt!',
	CompleteStripeSetup: 'Komplett Stripe-installationen',
	Completed: 'Klar',
	ComposeSms: 'Skriv SMS',
	ComputerSystemsAnalyst: 'Datorsystemanalytiker',
	Confirm: 'Bekräfta',
	ConfirmDeleteAccountDescription:
		'Du är på väg att radera ditt konto. Denna åtgärd kan inte ångras. Om du vill fortsätta, vänligen bekräfta nedan.',
	ConfirmDeleteActionDescription: 'Är du säker på att du vill ta bort den här åtgärden? Detta kan inte ångras',
	ConfirmDeleteAutomationDescription:
		'Är du säker på att du vill ta bort denna automatisering? Denna åtgärd kan inte ångras.',
	ConfirmDeleteScheduleDescription:
		'Att ta bort schemat <strong>{scheduleName}</strong> kommer att ta bort det från dina scheman och det kan ändra din tillgängliga onlinetjänst. Denna åtgärd kan inte ångras.',
	ConfirmDraftResponseContinue: 'Fortsätt med svar',
	ConfirmDraftResponseDescription:
		'Om du stänger den här sidan förblir ditt svar som ett utkast. Du kan komma tillbaka och fortsätta när som helst.',
	ConfirmDraftResponseSubmitResponse: 'Skicka svar',
	ConfirmDraftResponseTitle: 'Ditt svar har inte skickats',
	ConfirmIfUserIsClientDescription: `Registreringsformuläret du fyllde i är för leverantörer (dvs. hälsoteam/organisation).
 Om detta är ett misstag kan du välja "Fortsätt som kund" så fixar vi din kundportal`,
	ConfirmIfUserIsClientNoButton: 'Registrera dig som leverantör',
	ConfirmIfUserIsClientTitle: 'Det ser ut som att du är en kund',
	ConfirmIfUserIsClientYesButton: 'Fortsätt som kund',
	ConfirmKeepSeparate: 'Bekräfta att hålla separat',
	ConfirmMerge: 'Bekräfta sammanslagning',
	ConfirmPassword: 'Bekräfta lösenord',
	ConfirmRevertClaim: 'Ja, återställ status',
	ConfirmSignupAccessCode: 'Bekräftelsekod',
	ConfirmSignupButtom: 'Bekräfta',
	ConfirmSignupDescription: 'Vänligen ange din e-postadress och bekräftelsekoden som vi just har skickat till dig.',
	ConfirmSignupSubTitle: 'Kontrollera skräppostmappen - om e-postmeddelandet inte har kommit',
	ConfirmSignupSuccessSnackbar:
		'Bra, vi har bekräftat ditt konto! Nu kan du logga in med din e-postadress och ditt lösenord',
	ConfirmSignupTitle: 'Bekräfta konto',
	ConfirmSignupUsername: 'E-post',
	ConfirmSubscriptionUpdate: 'Bekräfta prenumeration {price} {isMonthly, select, true {per månad} other {per år}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'När klienterna har raderats kommer du inte längre att kunna komma åt deras information.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Radera {count, plural, one {# klient} other {# klienter}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'När kontakterna har raderats kommer du inte längre att kunna komma åt deras information.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Radera {count, plural, one {# kontakt} other {# kontakter}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Detta är en permanent åtgärd. När teammedlemmarna har tagits bort kommer du inte längre att kunna komma åt deras information.',
	ConfirmationModalBulkDeleteMembersTitleId: 'Radera {count, plural, one {# teammedlem} other {# teammedlemmar}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Om du stänger den här anteckningen avslutas alla pågående transkriptioner. Är du säker på att du vill fortsätta?',
	ConfirmationModalDeleteClientField:
		'Detta är en permanent åtgärd. När fältet har tagits bort kommer det inte längre att vara tillgängligt på dina återstående klienter.',
	ConfirmationModalDeleteSectionMessage:
		'När de har raderats kommer alla frågor i det här avsnittet att tas bort. Denna åtgärd kan inte ångras.',
	ConfirmationModalDeleteService:
		'Detta är en permanent åtgärd. När tjänsten har tagits bort kommer den inte längre att vara tillgänglig på din arbetsyta.',
	ConfirmationModalDeleteServiceGroup:
		'Om du tar bort en samling tas alla tjänster bort från gruppen och återgår till din tjänstlista. Den här åtgärden kan inte ångras.',
	ConfirmationModalDeleteTranscript: 'Är du säker på att du vill ta bort transkription?',
	ConfirmationModalDescriptionDeleteClient:
		'När klienten är raderad kommer du inte längre att kunna komma åt klientinformationen.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'När du tar bort din åtkomst kommer du inte längre att kunna se kundinformationen.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Deras profil kommer inte att tas bort, bara bort som en relation till denna klient.',
	ConfirmationModalDescriptionRemoveStaff: 'Är du säker på att du vill ta bort den här personen från leverantören?',
	ConfirmationModalEndSession: 'Är du säker på att du vill avsluta sessionen?',
	ConfirmationModalTitle: 'Är du säker?',
	Confirmed: 'Bekräftad',
	ConflictTimezoneWarningMessage: 'Konflikter kan uppstå på grund av flera tidszoner',
	Connect: 'Ansluta',
	ConnectExistingClientOrContact: 'Skapa ny kund/kontakt',
	ConnectInboxGoogleDescription: 'Lägg till ett Gmail-konto eller en Google-grupplista',
	ConnectInboxMicrosoftDescription: 'Lägg till ett Outlook-, Office365- eller Exchange-konto',
	ConnectInboxModalDescription:
		'Anslut dina appar för att sömlöst skicka, ta emot och spåra all din kommunikation på en centraliserad plats.',
	ConnectInboxModalExistingDescription:
		'Använd en befintlig anslutning från dina anslutna appars inställningar för att effektivisera konfigurationsprocessen.',
	ConnectInboxModalExistingTitle: 'Befintlig ansluten app i Carepatron',
	ConnectInboxModalTitle: 'Anslut inkorgen',
	ConnectToStripe: 'Anslut till Stripe',
	ConnectZoom: 'Anslut Zoom',
	ConnectZoomModalDescription: 'Tillåt Carepatron att hantera videosamtal för dina möten.',
	ConnectedAppDisconnectedNotificationSubject:
		'Vi har tappat anslutningen till {account}-kontot. Var vänlig anslut om igen',
	ConnectedAppSyncDescription:
		'Hantera anslutna appar för att skapa händelser i tredje parts kalendrar direkt från Carepatron.',
	ConnectedApps: 'Anslutna appar',
	ConnectedAppsGMailDescription: 'Lägg till Gmail-konton eller Google Grupp-lista',
	ConnectedAppsGoogleCalendarDescription: 'Lägg till kalendrar, konton eller Google Grupplista',
	ConnectedAppsGoogleDescription: 'Lägg till ditt Gmail-konto och synkronisera Google-kalendrar',
	ConnectedAppsMicrosoftDescription: 'Lägg till ett Outlook-, Office365- eller Exchange-konto',
	ConnectedCalendars: 'Anslutna kalendrar',
	ConsentDocumentation: 'Blanketter och avtal',
	ConsentDocumentationPublicTemplateError:
		'Av säkerhetsskäl kan du bara välja mallar från ditt team (icke-offentliga).',
	ConstructionWorker: 'Byggnadsarbetare',
	Consultant: 'Konsult',
	Contact: 'Kontakta',
	ContactAccessTypeHelperText: 'Tillåter familjeadministratörer att uppdatera information',
	ContactAccessTypeHelperTextMoreInfo: 'Detta gör att du kan dela anteckningar/dokument om {clientFirstName}',
	ContactAddressLabelBilling: 'Fakturering',
	ContactAddressLabelHome: 'Hem',
	ContactAddressLabelOthers: 'Andra',
	ContactAddressLabelWork: 'Arbete',
	ContactChangeConfirmation:
		'Att ändra fakturakontakten kommer att ta bort alla rader som är relaterade till <mark>{contactName}</mark>',
	ContactDetails: 'Kontaktuppgifter',
	ContactEmailLabelOthers: 'Andra',
	ContactEmailLabelPersonal: 'Personlig',
	ContactEmailLabelSchool: 'Skola',
	ContactEmailLabelWork: 'Arbete',
	ContactInformation: 'Kontaktinformation',
	ContactInformationText: 'Kontaktinformation',
	ContactListCreateButton: 'Ny kontakt',
	ContactName: 'Kontaktnamn',
	ContactPhoneLabelHome: 'Hem',
	ContactPhoneLabelMobile: 'Mobil',
	ContactPhoneLabelSchool: 'Skola',
	ContactPhoneLabelWork: 'Arbete',
	ContactRelationship: 'Kontaktförhållande',
	ContactRelationshipFormAccessType: 'Ge tillgång till delad information',
	ContactRelationshipGrantAccessInfo: 'Detta gör att du kan dela anteckningar ',
	ContactSupport: 'Kontakta supporten',
	Contacts: 'Kontakter',
	ContainerIdNotSet: 'Behållar-ID inte angivet',
	Contemporary: 'Samtida',
	Continue: 'Fortsätta',
	ContinueDictating: 'Fortsätt diktera',
	ContinueEditing: 'Fortsätt redigera',
	ContinueImport: 'Fortsätt importera',
	ContinueTranscription: 'Fortsätt transkription',
	ContinueWithApple: 'Fortsätt med Apple',
	ContinueWithGoogle: 'Fortsätt med Google',
	Conversation: 'Konversation',
	Copay: 'Co-pay',
	CopayOrCoinsurance: 'Co-pay eller Co-assurance',
	Copayment: 'Medbetalning',
	CopiedToClipboard: 'Kopierade till urklipp',
	Copy: 'Kopiera',
	CopyAddressSuccessSnackbar: 'Kopierad adress till urklipp',
	CopyCode: 'Kopiera kod',
	CopyCodeToClipboardSuccess: 'Kod kopierad till urklipp',
	CopyEmailAddressSuccessSnackbar: 'Kopierad e-postadress till urklipp',
	CopyLink: 'Kopiera länk',
	CopyLinkForCall: 'Kopiera denna länk för att dela detta samtal:',
	CopyLinkSuccessSnackbar: 'Kopierade länken till urklipp',
	CopyMeetingLink: 'Kopiera möteslänk',
	CopyPaymentLink: 'Kopiera betalningslänk',
	CopyPhoneNumberSuccessSnackbar: 'Kopierat telefonnummer till urklipp',
	CopyTemplateLink: 'Kopiera länken till mallen',
	CopyTemplateLinkSuccess: 'Kopierade länk till urklipp',
	CopyToClipboardError: 'Det gick inte att kopiera till urklipp. Försök igen.',
	CopyToTeamTemplates: 'Kopiera till Team-mallar',
	CopyToWorkspace: 'Kopiera till arbetsytan',
	Cosmetologist: 'Kosmetolog',
	Cost: 'Kosta',
	CostErrorMessage: 'Kostnad krävs',
	Counseling: 'Rådgivning',
	Counselor: 'Rådgivare',
	Counselors: 'Rådgivare',
	CountInvoicesAdded: '{count, plural, one {# Faktura tillagd} other {# Fakturor tillagda}}',
	CountNotesAdded: '{count, plural, one {# Notering tillagd} other {# Noteringar tillagda}}',
	CountSelected: '{count} valda',
	CountTimes: '{count} gånger',
	Country: 'Land',
	Cousin: 'Kusin',
	CoverageType: 'Täckningstyp',
	Covered: 'Täckt',
	Create: 'Skapa',
	CreateANewClient: 'Skapa en ny klient',
	CreateAccount: 'Skapa konto',
	CreateAndSignNotes: 'Skapa och signera anteckningar med kunder',
	CreateAvailabilityScheduleFailure: 'Det gick inte att skapa nytt tillgänglighetsschema',
	CreateAvailabilityScheduleSuccess: 'Nytt tillgänglighetsschema har skapats',
	CreateBillingItems: 'Skapa faktureringsobjekt',
	CreateCallFormButton: 'Starta samtalet',
	CreateCallFormInviteOnly: 'Endast inbjudan',
	CreateCallFormInviteOnlyMoreInfo:
		'Endast personer som är inbjudna till det här samtalet kan gå med. För att dela detta samtal med andra, avmarkera helt enkelt detta och kopiera/klistra in länken på nästa sida',
	CreateCallFormRecipients: 'Mottagare',
	CreateCallFormRegion: 'Värdregion',
	CreateCallModalAddClientContactSelectorLabel: 'Kundkontakter',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Sök efter kundnamn',
	CreateCallModalAddStaffSelectorLabel: 'Teammedlemmar (valfritt)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Sök på personalens namn',
	CreateCallModalDescription:
		'Starta ett samtal och bjud in personal och/eller kontakter. Alternativt kan du avmarkera rutan "Privat" för att göra det här samtalet delbart för alla med Carepatron',
	CreateCallModalTitle: 'Starta ett samtal',
	CreateCallModalTitleLabel: 'Titel (valfritt)',
	CreateCallNoPersonIdToolTip: 'Endast kontakter/klienter med portalåtkomst kan ansluta till samtal',
	CreateClaim: 'Skapa anspråk',
	CreateClaimCompletedMessage: 'Ditt anspråk har skapats.',
	CreateClientModalTitle: 'Ny kund',
	CreateContactModalTitle: 'Ny kontakt',
	CreateContactRelationshipButton: 'Lägg till relation',
	CreateContactSelectorDefaultOption: '  Skapa kontakt',
	CreateContactWithRelationshipFormAccessType: 'Ge åtkomst till delad information ',
	CreateDocumentDnDPrompt: 'Dra och släpp för att ladda upp filer',
	CreateDocumentSizeLimit: 'Storleksgräns per fil {size}MB. {total} filer totalt.',
	CreateFreeAccount: 'Skapa ett gratis konto',
	CreateInvoice: 'Skapa faktura',
	CreateLink: 'Skapa länk',
	CreateNew: 'Skapa nytt',
	CreateNewAppointment: 'Skapa nytt möte',
	CreateNewClaim: 'Skapa ett nytt anspråk',
	CreateNewClaimForAClient: 'Skapa ett nytt ärende för en klient.',
	CreateNewClient: 'Skapa ny klient',
	CreateNewConnection: 'Ny anslutning',
	CreateNewContact: 'Skapa ny kontakt',
	CreateNewField: 'Skapa nytt fält',
	CreateNewLocation: 'Ny plats',
	CreateNewService: 'Skapa ny tjänst',
	CreateNewServiceGroupFailure: 'Det gick inte att skapa ny samling',
	CreateNewServiceGroupMenu: 'Ny kollektion',
	CreateNewServiceGroupSuccess: 'Ny samling har skapats',
	CreateNewServiceMenu: 'Ny tjänst',
	CreateNewTeamMember: 'Skapa ny teammedlem',
	CreateNewTemplate: 'Ny mall',
	CreateNote: 'Skapa anteckning',
	CreateSuperbillReceipt: 'Ny superbill',
	CreateSuperbillReceiptSuccess: 'Superbill-kvitto har skapats',
	CreateTemplateFolderSuccessMessage: 'Skapade {folderTitle}',
	Created: 'Skapad',
	CreatedAt: 'Skapad {timestamp}',
	Credit: 'Kreditera',
	CreditAdded: 'Kredit tillämpas',
	CreditAdjustment: 'Kreditjustering',
	CreditAdjustmentReasonHelperText: 'Detta är en intern anteckning och kommer inte att vara synlig för din klient.',
	CreditAdjustmentReasonPlaceholder:
		'Att lägga till en justeringsanledning kan vara till hjälp när du granskar fakturerbara transaktioner',
	CreditAmount: '{amount} KN',
	CreditBalance: 'Kreditsaldo',
	CreditCard: 'Kreditkort',
	CreditCardExpire: 'Gäller till {exp_month}/{exp_year}',
	CreditCardNumber: 'Kreditkortsnummer',
	CreditDebitCard: 'Kort',
	CreditIssued: 'Kredit utfärdad',
	CreditsUsed: 'Krediter används',
	Crop: 'Beskära',
	Currency: 'Valuta',
	CurrentCredit: 'Nuvarande kredit',
	CurrentEventTime: 'Aktuell händelsetid',
	CurrentPlan: 'Nuvarande plan',
	Custom: 'Beställnings',
	CustomRange: 'Anpassat sortiment',
	CustomRate: 'Anpassat pris',
	CustomRecurrence: 'Anpassad återkommande',
	CustomServiceAvailability: 'Tjänstens tillgänglighet',
	CustomerBalance: 'Kundbalans',
	CustomerName: 'Kundens namn',
	CustomerNameIsRequired: 'Kundens namn krävs',
	CustomerServiceRepresentative: 'Kundtjänstrepresentant',
	CustomiseAppointments: 'Anpassa möten',
	CustomiseBookingLink: 'Anpassa bokningsalternativ',
	CustomiseBookingLinkServicesInfo: 'Kunder kan endast välja bokningsbara tjänster',
	CustomiseBookingLinkServicesLabel: 'Tjänster',
	CustomiseClientRecordsAndWorkspace: 'Anpassa dina kundregister och arbetsyta',
	CustomiseClientSettings: 'Anpassa klientinställningar',
	Customize: 'Anpassa',
	CustomizeAppearance: 'Anpassa utseendet',
	CustomizeAppearanceDesc:
		'Anpassa din onlinebokningsutseende för att matcha ditt varumärke och optimera hur dina tjänster visas för kunder.',
	CustomizeClientFields: 'Anpassa klientfält',
	CustomizeInvoiceTemplate: 'Anpassa fakturamall',
	CustomizeInvoiceTemplateDescription: 'Skapa enkelt professionella fakturor som speglar ditt varumärke.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX krävs',
	Daily: 'Dagligen',
	DanceTherapist: 'Dansterapeut',
	DangerZone: 'Riskzon',
	Dashboard: 'Instrumentpanel',
	Date: 'Datum',
	DateAndTime: 'Datum ',
	DateDue: 'Förfallodatum',
	DateErrorMessage: 'Datum krävs',
	DateFormPrimaryText: 'Datum',
	DateFormSecondaryText: 'Välj från en datumväljare',
	DateIssued: 'Datum utfärdat',
	DateOfPayment: 'Betalningsdag',
	DateOfService: 'Datum för delgivning',
	DateOverride: 'Åsidosätt datum',
	DateOverrideColor: 'Färg för åsidosättande av datum',
	DateOverrideInfo:
		'Åsidosättande av datum gör det möjligt för utövare att manuellt justera sin tillgänglighet för specifika datum genom att åsidosätta vanliga scheman.',
	DateOverrideInfoBanner:
		'Endast specificerade tjänster för detta datum åsidosättande kan bokas i dessa tidsluckor; inga andra onlinebokningar tillåtna.',
	DateOverrides: 'Datum åsidosätter',
	DatePickerFormPrimaryText: 'Datum',
	DatePickerFormSecondaryText: 'Välj ett datum',
	DateRange: 'Datumintervall',
	DateRangeFormPrimaryText: 'Datumintervall',
	DateRangeFormSecondaryText: 'Välj ett datumintervall',
	DateReceived: 'Mottaget datum',
	DateSpecificHours: 'Datumspecifika tider',
	DateSpecificHoursDescription:
		'Lägg till datum när din tillgänglighet ändras från dina schemalagda timmar eller för att erbjuda en tjänst på ett specifikt datum.',
	DateUploaded: 'Uppladdad {date, date, medium}',
	Dates: 'Datum',
	Daughter: 'Dotter',
	Day: 'Dag',
	DayPlural: '{count, plural, one {dag} other {dagar}}',
	Days: 'dagar',
	DaysPlural: '{age, plural, one {# dag} other {# dagar}}',
	DeFacto: 'De facto',
	Deactivated: 'Inaktiverad',
	Debit: 'Debitera',
	DecreaseIndent: 'Minska indraget',
	Deductibles: 'Självrisker',
	Default: 'Standard',
	DefaultBillingProfile: 'Standardfaktureringsprofil',
	DefaultDescription: 'Standardbeskrivning',
	DefaultEndOfLine: 'Inga fler föremål',
	DefaultInPerson: 'Kundmöten',
	DefaultInvoiceTitle: 'Standardtitel',
	DefaultNotificationSubject: 'Du har fått en ny notis för {notificationType}',
	DefaultPaymentMethod: 'Standard betalningsmetod',
	DefaultService: 'Standardtjänst',
	DefaultValue: 'Standard',
	DefaultVideo: 'Klient video möte e-post',
	DefinedTemplateType: '{invoiceTemplate} mall',
	Delete: 'Radera',
	DeleteAccountButton: 'Ta bort konto',
	DeleteAccountDescription: 'Ta bort ditt konto från plattformen',
	DeleteAccountPanelInfoAlert:
		'Du måste ta bort dina arbetsytor innan du tar bort din profil. För att fortsätta byter du till en arbetsyta och väljer Inställningar > Arbetsyteinställningar.',
	DeleteAccountTitle: 'Ta bort konto',
	DeleteAppointment: 'Ta bort möte',
	DeleteAppointmentDescription: 'Är du säker på att du vill ta bort det här mötet? Du kan återställa det senare.',
	DeleteAvailabilityScheduleFailure: 'Det gick inte att ta bort tillgänglighetsschemat',
	DeleteAvailabilityScheduleSuccess: 'Tillgänglighetsschema har tagits bort',
	DeleteBillable: 'Ta bort fakturerbar',
	DeleteBillableConfirmationMessage:
		'Är du säker på att du vill ta bort den här fakturerbara? Denna åtgärd kan inte ångras.',
	DeleteBillingProfileConfirmationMessage: 'Detta tar bort faktureringsprofilen permanent.',
	DeleteCardConfirmation:
		'Detta är en permanent åtgärd. När kortet har raderats kommer du inte längre att kunna komma åt det.',
	DeleteCategory: 'Ta bort kategori (detta är inte permanent om inte ändringar sparas)',
	DeleteClientEventConfirmationDescription: 'Detta kommer att tas bort permanent.',
	DeleteClients: 'Ta bort klienter',
	DeleteCollection: 'Ta bort samling',
	DeleteColumn: 'Ta bort kolumn',
	DeleteConversationConfirmationDescription:
		'Radera den här konversationen för alltid. Den här åtgärden kan inte ångras.',
	DeleteConversationConfirmationTitle: 'Radera konversationen för alltid',
	DeleteExternalEventDescription: 'Är du säker på att du vill ta bort det här mötet?',
	DeleteFileConfirmationModalPrompt: 'När den väl har tagits bort kan du inte hämta den här filen igen.',
	DeleteFolder: 'Ta bort mapp',
	DeleteFolderConfirmationMessage:
		'Är du säker på att du vill ta bort den här mappen {name}? Alla objekt i den här mappen kommer också att tas bort. Du kan återställa den här senare.',
	DeleteForever: 'Radera för alltid',
	DeleteInsurancePayerConfirmationMessage:
		'Att ta bort {payer} kommer att radera det från din lista med försäkringsbetalare. Den här åtgärden är permanent och kan inte återställas.',
	DeleteInsurancePayerFailure: 'Det gick inte att ta bort försäkringsbetalare',
	DeleteInsurancePolicyConfirmationMessage: 'Detta kommer att ta bort försäkringen permanent.',
	DeleteInvoiceConfirmationDescription:
		'Denna åtgärd kan inte ångras. Det kommer att ta bort fakturan och alla betalningar som är kopplade till den permanent.',
	DeleteLocationConfirmation:
		'Att ta bort en plats är en permanent åtgärd. När du har tagit bort den har du inte längre tillgång till den. Denna åtgärd kan inte ångras.',
	DeletePayer: 'Ta bort betalare',
	DeletePracticeWorkspace: 'Ta bort övningsarbetsytan',
	DeletePracticeWorkspaceDescription: 'Ta bort denna övningsarbetsyta permanent',
	DeletePracticeWorkspaceFailedSnackbar: 'Det gick inte att ta bort arbetsytan',
	DeletePracticeWorkspaceModalCancelButton: 'Ja, avsluta min prenumeration',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Innan du fortsätter med raderingen av din arbetsyta måste du avsluta din prenumeration först.',
	DeletePracticeWorkspaceModalConfirmButton: 'Ja, ta bort arbetsytan permanent',
	DeletePracticeWorkspaceModalDescription:
		'{name} arbetsyta kommer att raderas permanent och alla teammedlemmar kommer att förlora åtkomst. Ladda ner alla viktiga data eller meddelanden som du kan behöva innan raderingen sker. Denna åtgärd kan inte ångras.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Detta beslut fattades på grund av:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Resonera',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Berätta för oss varför du vill ta bort ditt konto.',
	DeletePracticeWorkspaceModalTitle: 'Är du säker?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Alla lagmedlemmars åtkomst har tagits bort',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} har tagits bort.',
	DeletePublicTemplateContent: 'Detta tar bara bort den offentliga mallen och inte ditt teams mall.',
	DeleteRecurringAppointmentModalTitle: 'Ta bort återkommande möte',
	DeleteRecurringEventModalTitle: 'Ta bort upprepande möte',
	DeleteRecurringReminderModalTitle: 'Ta bort upprepad påminnelse',
	DeleteRecurringTaskModalTitle: 'Ta bort upprepad uppgift',
	DeleteReminderConfirmation:
		'Detta är en permanent åtgärd. När påminnelsen är raderad kommer du inte längre att kunna komma åt den. Kommer endast att påverka nya utnämningar',
	DeleteSection: 'Ta bort avsnitt',
	DeleteSectionInfo:
		'Att ta bort avsnittet **{section}** kommer dölja alla befintliga fält inom det. Den här åtgärden kan inte ångras.',
	DeleteSectionWarning:
		'Kärnfälten kan inte raderas och kommer att flyttas till det befintliga avsnittet **{section}**.',
	DeleteServiceFailure: 'Det gick inte att ta bort tjänsten',
	DeleteServiceSuccess: 'Tjänsten har raderats',
	DeleteStaffScheduleOverrideDescription:
		'Att ta bort detta datumöverstyrning på {value} kommer att ta bort det från dina scheman och det kan ändra din online-tjänst som är tillgänglig. Den här åtgärden kan inte ångras.',
	DeleteSuperbillConfirmationDescription:
		'Denna åtgärd kan inte ångras. Det kommer att ta bort Superbill-kvittot permanent.',
	DeleteSuperbillFailure: 'Det gick inte att ta bort Superbill-kvitto',
	DeleteSuperbillSuccess: 'Superbill-kvitto har raderats',
	DeleteTaxRateConfirmationDescription: 'Är du säker på att du vill ta bort denna skattesats?',
	DeleteTemplateContent: 'Denna åtgärd kan inte ångras',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} borttagen',
	DeleteTemplateSuccessMessage: '{templateTitle} borttagen',
	DeleteTemplateTitle: 'Är du säker på att du vill ta bort den här mallen?',
	DeleteTranscript: 'Ta bort avskrift',
	DeleteWorkspace: 'Ta bort arbetsyta',
	Deleted: 'Raderad',
	DeletedBy: 'Raderad av',
	DeletedContact: 'Raderad kontakt',
	DeletedOn: 'Raderad på',
	DeletedStatusLabel: 'Raderad status',
	DeletedUserTooltip: 'Denna klient har tagits bort',
	DeliveryMethod: 'Leveransmetod',
	Demo: 'Demo',
	Denied: 'Nekad',
	Dental: 'Dental',
	DentalAssistant: 'Tandläkarassistent',
	DentalHygienist: 'Tandhygienist',
	Dentist: 'Tandläkare',
	Dentists: 'Tandläkare',
	Description: 'Beskrivning',
	DescriptionMustNotExceed: 'Beskrivning får inte överstiga {max} tecken',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { med {staffName}}}',
	Details: 'Detaljer',
	Devices: 'Enheter',
	Diagnosis: 'Diagnos',
	DiagnosisAndBillingItems: 'Diagnos ',
	DiagnosisCode: 'Diagnoskod',
	DiagnosisCodeErrorMessage: 'En diagnoskod krävs',
	DiagnosisCodeSelectorPlaceholder: 'Sök och lägg till från ICD-10 diagnostiska koder',
	DiagnosisCodeSelectorTooltip:
		'Diagnoskoder används för att automatisera superbills kvitton för försäkringsersättning',
	DiagnosticCodes: 'Diagnostiska koder',
	Dictate: 'Diktera',
	DictatingIn: 'Dikterar in',
	Dictation: 'Diktering',
	DidNotAttend: 'Deltog inte',
	DidNotComplete: 'Avslutade inte',
	DidNotProviderEnoughValue: 'Tillhandahöll inte tillräckligt med värde',
	DidntProvideEnoughValue: 'Tillhandahöll inte tillräckligt med värde',
	DieteticsOrNutrition: 'Diet eller näring',
	Dietician: 'Dietist',
	Dieticians: 'Dietister',
	Dietitian: 'Dietist',
	DigitalSign: 'Skriv under här:',
	DigitalSignHelp: '(Klicka/tryck ner för att rita)',
	DirectDebit: 'Autogiro',
	DirectTextLink: 'Direkt textlänk',
	Disable: 'Inaktivera',
	DisabledEmailInfo: 'Vi kan inte uppdatera din e-postadress eftersom ditt konto inte hanteras av oss',
	Discard: 'Kassera',
	DiscardChanges: 'Släng ändringar',
	DiscardDrafts: 'Släng utkast',
	Disconnect: 'Koppla från',
	DisconnectAppConfirmation: 'Vill du koppla bort den här appen?',
	DisconnectAppConfirmationDescription: 'Är du säker på att du vill koppla bort den här appen?',
	DisconnectAppConfirmationTitle: 'Koppla från appen',
	Discount: 'Rabatt',
	DisplayCalendar: 'Visa i Carepatron',
	DisplayName: 'Visningsnamn',
	DisplayedToClients: 'Visas för kunder',
	DiversionalTherapist: 'Avledningsterapeut',
	DoItLater: 'Gör det senare',
	DoNotImport: 'Importera inte',
	DoNotSend: 'Skicka inte',
	DoThisLater: 'Gör detta senare.',
	DoYouWantToEndSession: 'Vill du fortsätta eller avsluta din session nu?',
	Doctor: 'Läkare',
	Doctors: 'Läkare',
	DoesNotRepeat: 'Upprepar inte',
	DoesntWorkWellWithExistingTools: 'Fungerar inte bra med våra befintliga verktyg eller arbetsflöden',
	DogWalker: 'Dog Walker',
	Done: 'Klart',
	DontAllowClientsToCancel: 'Tillåt inte kunder att avbryta',
	DontHaveAccount: 'Har du inget konto?',
	DontSend: 'Skicka inte',
	Double: 'Dubbel',
	DowngradeTo: 'Nedgradera till {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Du kan tyvärr inte nedgradera din plan eftersom du har för många teammedlemmar. Ta bort några från din leverantör och försök igen.',
	Download: 'Ladda ner',
	DownloadAsPdf: 'Ladda ner som PDF',
	DownloadERA: 'Ladda ner ERA',
	DownloadPDF: 'Ladda ner PDF',
	DownloadTemplateFileName: 'Carepatron Bytesmallmall.csv',
	DownloadTemplateTileDescription: 'Använd vår kalkylmall för att organisera och ladda upp dina klienter.',
	DownloadTemplateTileLabel: 'Ladda ner mall',
	Downloads: '{number, plural, one {<span>#</span> Nedladdning} other {<span>#</span> Nedladdningar}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Förslag',
	DraftResponses: 'Utkast till svar',
	DraftSaved: 'Sparade ändringar',
	DragAndDrop: 'dra och släpp',
	DragDropText: 'Dra och släpp hälsodokument',
	DragToMove: 'Dra för att flytta',
	DragToMoveOrActivate: 'Dra för att flytta eller aktivera',
	DramaTherapist: 'Dramaterapeut',
	DropdownFormFieldPlaceHolder: 'Välj alternativ från listan',
	DropdownFormPrimaryText: 'Dropdown',
	DropdownFormSecondaryText: 'Välj från en lista med alternativ',
	DropdownTextFieldError: 'Texten i rullgardinsmenyn får inte vara tom',
	DropdownTextFieldPlaceholder: 'Lägg till ett dropdown-alternativ',
	Due: 'Förfallodatum',
	DueDate: 'Förfallodatum',
	Duplicate: 'Duplicera',
	DuplicateAvailabilityScheduleFailure: 'Det gick inte att duplicera tillgänglighetsschemat',
	DuplicateAvailabilityScheduleSuccess: 'Framgångsrikt duplicerat {name} schemaläggning',
	DuplicateClientBannerAction: 'Recension',
	DuplicateClientBannerDescription:
		'Genom att slå samman dubbletter av klientposter konsolideras dem till en, och all unik kundinformation behålls.',
	DuplicateClientBannerTitle: '{count} Dubletter hittades',
	DuplicateColumn: 'Duplicera kolumn',
	DuplicateContactFieldSettingErrorSnackbar: 'Kan inte ha dubbletter av avsnittsnamn',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Kan inte ha dubbletter av fältnamn',
	DuplicateEmailError: 'Dubblett e-post',
	DuplicateHeadingName: 'Avsnitt {name} finns redan',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'En faktura med samma "fakturanummer" finns redan.',
	DuplicateRecords: 'Duplicera poster',
	DuplicateRecordsMinimumError: 'Minst 2 poster måste väljas',
	DuplicateRecordsRequired: 'Välj minst 1 post att separera',
	DuplicateServiceFailure: 'Misslyckades med att duplicera <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Framgångsrikt duplicerat <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Mappen har kopierats',
	DuplicateTemplateSuccess: 'Mallen har duplicerats',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'DX-kod',
	DxCodeSelectPlaceholder: 'Sök och lägg till från ICD-10-koder',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Ingen',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>innehåller justeringar</i>} other {}}',
	EarnReferralCredit: 'Tjäna ${creditAmount}',
	Economist: 'Ekonom',
	Edit: 'Redigera',
	EditArrangements: 'Redigera arrangemang',
	EditBillTo: 'Redigera Bill till',
	EditClient: 'Redigera klient',
	EditClientFileModalDescription:
		'Redigera åtkomsten till den här filen genom att välja alternativen i kryssrutorna "Visas av".',
	EditClientFileModalTitle: 'Redigera fil',
	EditClientNoteModalDescription:
		'Redigera innehållet i anteckningen. Använd avsnittet Visas av för att ändra vem som kan se anteckningen.',
	EditClientNoteModalTitle: 'Redigera anteckning',
	EditConnectedAppButton: 'Redigera',
	EditConnections: 'Redigera anslutningar{account, select, null { } undefined { } other { för {account}}}',
	EditContactDetails: 'Redigera kontaktuppgifter',
	EditContactFormIsClientLabel: 'Konvertera till klient',
	EditContactIsClientCheckboxWarning: 'Att konvertera en kontakt till en klient kan inte ångras',
	EditContactIsClientWanringModal:
		'Konverteringen av denna kontakt till en klient kan inte ångras. Men alla relationer kommer fortfarande att finnas kvar och du kommer nu att ha tillgång till deras anteckningar, filer och annan dokumentation.',
	EditContactRelationship: 'Redigera kontaktförhållande',
	EditDetails: 'Redigera detaljer',
	EditFileModalTitle: 'Redigera fil för {name}',
	EditFolder: 'Redigera mapp',
	EditFolderDescription: 'Byt namn på mappen till...',
	EditInvoice: 'Redigera faktura',
	EditInvoiceDetails: 'Redigera fakturainformation',
	EditLink: 'Redigera länk',
	EditLocation: 'Redigera plats',
	EditLocationFailure: 'Det gick inte att uppdatera platsen',
	EditLocationSucess: 'Platsen har uppdaterats',
	EditPaymentDetails: 'Redigera betalningsinformation',
	EditPaymentMethod: 'Redigera betalningsmetod',
	EditPersonalDetails: 'Redigera personliga uppgifter',
	EditPractitioner: 'Redigera utövare',
	EditProvider: 'Redigera leverantör',
	EditProviderDetails: 'Redigera leverantörsinformation',
	EditRecurrence: 'Redigera återkommande',
	EditRecurringAppointmentModalTitle: 'Redigera återkommande möte',
	EditRecurringEventModalTitle: 'Redigera upprepande möte',
	EditRecurringReminderModalTitle: 'Redigera upprepad påminnelse',
	EditRecurringTaskModalTitle: 'Redigera upprepad uppgift',
	EditRelationshipModalTitle: 'Redigera relation',
	EditService: 'Redigera tjänst',
	EditServiceFailure: 'Det gick inte att uppdatera den nya tjänsten',
	EditServiceGroup: 'Redigera samling',
	EditServiceGroupFailure: 'Det gick inte att uppdatera samlingen',
	EditServiceGroupSuccess: 'Uppdaterad samling framgångsrikt',
	EditServiceSuccess: 'Ny tjänst har uppdaterats framgångsrikt',
	EditStaffDetails: 'Redigera personaluppgifter',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Det går inte att uppdatera e-postadressen. Skapa en ny teammedlem med en ny e-postadress.',
	EditSubscriptionBilledQuantity: 'Fakturerade antal',
	EditSubscriptionBilledQuantityValue: '{billedUsers} teammedlemmar',
	EditSubscriptionLimitedTimeOffer: 'Begränsat tidserbjudande! 50 % rabatt i 6 månader.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Din prenumerationskostnad kommer att justeras när du lägger till eller tar bort teammedlemmar.',
	EditSubscriptionUpgradeContent:
		'Ditt konto kommer att uppdateras omedelbart till den nya planen och faktureringsperioden. Eventuella prisändringar kommer att debiteras automatiskt till din sparade betalningsmetod eller krediteras till ditt konto.',
	EditSubscriptionUpgradePlanTitle: 'Uppgradera prenumerationspaket',
	EditSuperbillReceipt: 'Redigera superbill',
	EditTags: 'Redigera taggar',
	EditTemplate: 'Redigera mall',
	EditTemplateFolderSuccessMessage: 'Mallmapp har uppdaterats',
	EditValue: 'Redigera {value}',
	Edited: 'Redigerat',
	Editor: 'Redaktör',
	EditorAlertDescription: 'Ett format som inte stöds har upptäckts. Ladda om appen eller kontakta vårt supportteam.',
	EditorAlertTitle: 'Vi har problem med att visa detta innehåll',
	EditorPlaceholder:
		'Börja skriva, välj en mall eller lägg till grundläggande block för att fånga svar från dina kunder.',
	EditorTemplatePlaceholder: 'Börja skriva eller lägg till komponenter för att bygga en mall',
	EditorTemplateWithSlashCommandPlaceholder:
		'Börja skriva eller lägg till grundläggande block för att fånga in kundens svar. Använd slash-kommandon (/) för snabba åtgärder.',
	EditorWithSlashCommandPlaceholder:
		'Börja skriva, välj en mall eller lägg till grundläggande block för att fånga klientsvar. Använd snedstreckkommandon ( / ) för snabba åtgärder.',
	EffectiveStartEndDate: 'Gällande start- och slutdatum',
	ElectricalEngineer: 'Elektroingenjör',
	Electronic: 'Elektronisk',
	ElectronicSignature: 'Elektronisk signatur',
	ElementarySchoolTeacher: 'Folkskollärare',
	Eligibility: 'Behörighet',
	Email: 'E-post',
	EmailAlreadyExists: 'E-postadressen finns redan',
	EmailAndSms: 'E-post ',
	EmailBody: 'E-posttext',
	EmailContainsIgnoredDescription:
		'Följande e-postmeddelande innehåller en avsändares e-postmeddelande som för närvarande ignoreras. Vill du fortsätta?',
	EmailInviteToPortalBody: `Hej {contactName},
Följ den här länken för att logga in på din säkra klientportal och enkelt hantera din vård.

Vänliga hälsningar,

{providerName}`,
	EmailInviteToPortalSubject: 'Välkommen till {providerName}',
	EmailInvoice: 'E-posta faktura',
	EmailInvoiceOverdueBody: `Hej {contactName}
Din faktura {invoiceNumber} är förfallen.
Vänligen betala din faktura online via länken nedan.

Om du har några frågor, vänligen meddela oss.

Tack,
{providerName}`,
	EmailInvoicePaidBody: `Hej {contactName}
Din faktura {invoiceNumber} är betald.
För att visa och ladda ner en kopia av din faktura följ länken nedan.

Om du har några frågor, vänligen meddela oss.

Tack,
{providerName}`,
	EmailInvoiceProcessingBody: `Hej {contactName}
Din faktura {invoiceNumber} är klar.
Följ länken nedan för att se din faktura.

Om du har några frågor, vänligen meddela oss.

Tack,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hej {contactName}
Din faktura {invoiceNumber} är klar och ska betalas senast {dueDate}.
För att visa och betala din faktura online följ länken nedan.

Om du har några frågor, vänligen meddela oss.

Tack,
{providerName}`,
	EmailInvoiceVoidBody: `Hej {contactName}
Din faktura {invoiceNumber} är ogiltig.
För att visa denna faktura följ länken nedan.

Om du har några frågor, vänligen meddela oss.

Tack,
{providerName}`,
	EmailNotFound: 'E-post hittades inte',
	EmailNotVerifiedErrorCodeSnackbar: 'Det går inte att utföra åtgärden. Du måste verifiera din e-postadress.',
	EmailNotVerifiedTitle: 'Din e-postadress är inte verifierad. Vissa funktioner kommer att vara begränsade.',
	EmailSendClientIntakeBody: `Hej {contactName},
{providerName} vill att du ska lämna viss information och granska viktiga dokument. Följ länken nedan för att komma igång.

Med vänliga hälsningar,

{providerName}`,
	EmailSendClientIntakeSubject: 'Välkommen till {providerName}',
	EmailSuperbillReceipt: 'E-posta superbill',
	EmailSuperbillReceiptBody: `Hej {contactName},
{providerName} har skickat dig en kopia av din ersättningskvitto {date}.

Du kan ladda ner och skicka in den direkt till ditt försäkringsbolag.`,
	EmailSuperbillReceiptFailure: 'Det gick inte att skicka Superbill-kvitto',
	EmailSuperbillReceiptSubject: '{providerName} har skickat ett kvitto på ersättning',
	EmailSuperbillReceiptSuccess: 'Superbill-kvitto har skickats',
	EmailVerificationDescription: 'Vi <span>verifierar</span> ditt konto nu',
	EmailVerificationNotification: 'Ett verifieringsmejl har skickats till {email}',
	EmailVerificationSuccess: 'Din e-postadress har ändrats till {email}',
	Emails: 'E-postmeddelanden',
	EmergencyContact: 'Nödkontakt',
	EmployeesIdentificationNumber: 'Anställdas identifikationsnummer',
	EmploymentStatus: 'Anställningsstatus',
	EmptyAgendaViewDescription: 'Inga händelser att visa.<mark> Skapa ett möte nu</mark>',
	EmptyBin: 'Tom papperskorg',
	EmptyBinConfirmationDescription:
		'Tomt papperskorg kommer att radera alla **{total} konversationer** i Raderade. Den här åtgärden kan inte ångras.',
	EmptyBinConfirmationTitle: 'Radera konversationer för alltid',
	EmptyTrash: 'Töm papperskorgen',
	Enable: 'Aktivera',
	EnableCustomServiceAvailability: 'Aktivera tjänsttillgänglighet',
	EnableCustomServiceAvailabilityDescription: 'T.ex. Inledande möten kan endast bokas varje dag kl. 9-10',
	EndCall: 'Avsluta samtalet',
	EndCallConfirmationForCreator:
		'Du kommer att avsluta detta för alla eftersom du är initiativtagaren till samtalet.',
	EndCallConfirmationHasActiveAttendees:
		'Du håller på att avsluta samtalet men klient(er) har redan anslutit sig. Vill du också vara med?',
	EndCallForAll: 'Avsluta samtalet för alla',
	EndDate: 'Slutdatum',
	EndDictation: 'Avsluta diktat',
	EndOfLine: 'Inga fler möten',
	EndSession: 'Avsluta sessionen',
	EndTranscription: 'Avsluta transkription',
	Ends: 'Ends',
	EndsOnDate: 'Avslutas den {date}',
	Enrol: 'Registrera',
	EnrollmentRejectedSubject: 'Din registrering med {payerName} har avvisats',
	Enrolment: 'Intag',
	Enrolments: 'Inskrivningar',
	EnrolmentsDescription: 'Visa och hantera leverantörsregistreringar med betalaren.',
	EnterAName: 'Ange ett namn...',
	EnterFieldLabel: 'Ange fältetikett...',
	EnterPaymentDetailsDescription:
		'Din prenumerationskostnad kommer automatiskt att justeras när du lägger till eller tar bort användare.',
	EnterSectionName: 'Ange avsnittsnamn...',
	EnterSubscriptionPaymentDetails: 'Ange betalningsinformation',
	EnvironmentalScientist: 'Miljövetare',
	Epidemiologist: 'Epidemiolog',
	Eraser: 'Suddgummi',
	Error: 'Fel',
	ErrorBoundaryAction: 'Ladda om sidan',
	ErrorBoundaryDescription: 'Uppdatera sidan och försök igen.',
	ErrorBoundaryTitle: 'hoppsan! Något gick fel',
	ErrorCallNotFound: 'Samtalet kan inte hittas. Det kan ha gått ut eller skaparen har avslutat det.',
	ErrorCannotAccessCallUninvitedCode: 'Tyvärr, det verkar som om du inte har blivit inbjuden till det här samtalet.',
	ErrorFileUploadCustomMaxFileCount: 'Kan inte ladda upp mer än {count} filer samtidigt',
	ErrorFileUploadCustomMaxFileSize: 'Filstorleken får inte överstiga {mb} MB',
	ErrorFileUploadInvalidFileType: 'Ogiltig filtyp som kan innehålla potentiella virus och skadlig programvara',
	ErrorFileUploadMaxFileCount: 'Kan inte ladda upp mer än 150 filer samtidigt',
	ErrorFileUploadMaxFileSize: 'Filstorleken får inte överstiga 100 MB',
	ErrorFileUploadNoFileSelected: 'Välj filer att ladda upp',
	ErrorInvalidNationalProviderId: 'Det angivna nationella leverantörs-ID:t är ogiltigt',
	ErrorInvalidPayerId: 'Det angivna Betalar-ID:t är ogiltigt',
	ErrorInvalidTaxNumber: 'Det angivna skattenumret är inte giltigt',
	ErrorInviteExistingProviderStaffCode: 'Denna användare finns redan i arbetsytan.',
	ErrorInviteStaffExistingUser: 'Tyvärr, det verkar som om användaren du lade till redan finns i vårt system.',
	ErrorOnlySingleCallAllowed:
		'Du kan bara ha ett samtal åt gången. Avsluta det pågående samtalet för att starta ett nytt.',
	ErrorPayerNotFound: 'Betalaren hittades inte',
	ErrorProfilePhotoMaxFileSize: 'Uppladdningen misslyckades! Filstorleksgränsen har nåtts - 5 MB',
	ErrorRegisteredExistingUser: 'Tyvärr, det verkar som att du redan är registrerad.',
	ErrorUserSignInIncorrectCredentials: 'Ogiltig e-postadress eller lösenord. Försök igen.',
	ErrorUserSigninGeneric: 'Tyvärr, något gick fel.',
	ErrorUserSigninUserNotConfirmed:
		'Tyvärr, du måste bekräfta ditt konto innan du loggar in. Kontrollera din inkorg för instruktioner.',
	Errors: 'Fel',
	EssentialPlanInclusionFive: 'Mallimport',
	EssentialPlanInclusionFour: '5 GB lagringsutrymme',
	EssentialPlanInclusionHeader: 'Allt gratis  ',
	EssentialPlanInclusionOne: 'Automatiska och anpassade påminnelser',
	EssentialPlanInclusionSix: 'Prioriterat stöd',
	EssentialPlanInclusionThree: 'Videochat',
	EssentialPlanInclusionTwo: '2-vägs kalendersynkronisering',
	EssentialSubscriptionPlanSubtitle: 'Förenkla din övning med det väsentliga',
	EssentialSubscriptionPlanTitle: 'Grundläggande',
	Esthetician: 'Estetiker',
	Estheticians: 'Estetiker',
	EstimatedArrivalDate: 'Beräknad ankomst {numberOfDaysFromNow}',
	Ethnicity: 'Etnicitet',
	Europe: 'Europa',
	EventColor: 'Mötesfärg',
	EventName: 'Händelsenamn',
	EventType: 'Händelsetyp',
	Every: 'Varje',
	Every2Weeks: 'Varannan vecka',
	EveryoneInWorkspace: 'Alla på arbetsplatsen',
	ExercisePhysiologist: 'Träningsfysiolog',
	Existing: 'Befintlig',
	ExistingClients: 'Befintliga kunder',
	ExistingFolders: 'Befintliga mappar',
	ExpiredPromotionCode: 'Kampanjkoden har löpt ut',
	ExpiredReferralDescription: 'Remissen har löpt ut',
	ExpiredVerificationLink: 'Förfallen verifieringslänk',
	ExpiredVerificationLinkDescription: `Vi är ledsna, men verifieringslänken du klickade på har upphört att gälla. Detta kan hända om du väntat längre än 24 timmar med att klicka på länken eller om du redan har använt länken för att verifiera din e-postadress.

 Begär en ny verifieringslänk för att verifiera din e-postadress.`,
	ExpiryDateRequired: 'Utgångsdatum krävs',
	ExploreFeature: 'Vad skulle du vilja utforska först?',
	ExploreOptions: 'Välj ett eller flera alternativ att utforska...',
	Export: 'Exportera',
	ExportAppointments: 'Exportera möten',
	ExportClaims: 'Exportera anspråk',
	ExportClaimsFilename: 'Anspråk {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Din fil kunde inte laddas ned på grund av ett fel.',
	ExportClientsDownloadFailureSnackbarTitle: 'Nedladdningen misslyckades',
	ExportClientsFailureSnackbarDescription: 'Din fil kunde inte exporteras på grund av ett fel.',
	ExportClientsFailureSnackbarTitle: 'Exporten misslyckades',
	ExportClientsModalDescription: `Denna dataexportprocess kan ta några minuter beroende på mängden data som exporteras. Du kommer att få ett e-postmeddelande med en länk när det är klart för nedladdning.

 Vill du fortsätta med att exportera klientdata?`,
	ExportClientsModalTitle: 'Exportera klientdata',
	ExportCms1500: 'Exportera CMS1500',
	ExportContactFailedNotificationSubject: 'Din dataexport misslyckades',
	ExportFailed: 'Exporten misslyckades',
	ExportGuide: 'Exportguide',
	ExportInvoiceFileName: 'Transaktioner {fromDate}-{toDate}.csv',
	ExportPayments: 'Exportera betalningar',
	ExportPaymentsFilename: 'Betalningar {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Ditt dokument är redo att laddas ned.',
	ExportPrintWaitMessage: 'Förbereder ditt dokument. Vänligen vänta...',
	ExportTextOnly: 'Exportera endast text',
	ExportTransactions: 'Exporttransaktioner',
	Exporting: 'Exporterar',
	ExportingData: 'Exporterar data',
	ExtendedFamilyMember: 'Utökad familjemedlem',
	External: 'Extern',
	ExternalEventInfoBanner: 'Detta möte kommer från en synkroniserad kalender och kan sakna objekt.',
	ExtraLarge: 'Extra large',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Misslyckades',
	FailedToJoinTheMeeting: 'Det gick inte att gå med i mötet.',
	FallbackPageDescription: `Det ser ut som om den här sidan inte finns, du kanske behöver {refreshButton} den här sidan för att få de senaste ändringarna.
Annars, kontakta Carepatron support.`,
	FallbackPageDescriptionUpdateButton: 'uppdatera',
	FallbackPageTitle: 'Hoppsan...',
	FamilyPlanningService: 'Familjeplaneringstjänst',
	FashionDesigner: 'Modedesigner',
	FastTrackInvoicingAndBilling: 'Snabbspåra din fakturering och fakturering',
	Father: 'Far',
	FatherInLaw: 'Svärfar',
	Favorite: 'Favorit',
	FeatureBannerCalendarTile1ActionLabel: 'Onlinebokning • 2 min',
	FeatureBannerCalendarTile1Description: 'E-posta, sms:a eller lägg till tillgänglighet på din webbplats',
	FeatureBannerCalendarTile1Title: 'Gör det möjligt för dina kunder att boka online',
	FeatureBannerCalendarTile2ActionLabel: 'Automatisera påminnelser • 2 min',
	FeatureBannerCalendarTile2Description: 'Öka kundnärvaro med automatiska påminnelser',
	FeatureBannerCalendarTile2Title: 'Minska uteblivna ankomster',
	FeatureBannerCalendarTile3Title: 'Schemaläggning och arbetsflöde',
	FeatureBannerCalendarTitle: 'Gör schemaläggning enkelt',
	FeatureBannerCallsTile1ActionLabel: 'Starta telehälsosamtal',
	FeatureBannerCallsTile1Description: 'Klientåtkomst med bara en länk. Inga inloggningar, lösenord eller krångel',
	FeatureBannerCallsTile1Title: 'Starta ett videosamtal var som helst',
	FeatureBannerCallsTile2ActionLabel: 'Anslut appar • 4 min',
	FeatureBannerCallsTile2Description: 'Anslut sömlöst andra föredragna telehälsoleverantörer',
	FeatureBannerCallsTile2Title: 'Anslut dina telehälsoappar',
	FeatureBannerCallsTile3Title: 'Samtal',
	FeatureBannerCallsTitle: 'Få kontakt med kunder – var som helst, när som helst',
	FeatureBannerClientsTile1ActionLabel: 'Importera nu • 2 min',
	FeatureBannerClientsTile1Description: 'Kom igång snabbt med vårt automatiska klientimportverktyg',
	FeatureBannerClientsTile1Title: 'Har du många kunder?',
	FeatureBannerClientsTile2ActionLabel: 'Anpassa intaget • 2 min',
	FeatureBannerClientsTile2Description: 'Ta bort intagspapper och förbättra kundupplevelser',
	FeatureBannerClientsTile2Title: 'Gå papperslös',
	FeatureBannerClientsTile3Title: 'Kundportal',
	FeatureBannerClientsTitle: 'Allt börjar med dina kunder',
	FeatureBannerHeader: 'Av gemenskapen, för gemenskapen!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatisera betalningar • 2 min',
	FeatureBannerInvoicesTile1Description: 'Undvik obekväma konversationer med automatiserade betalningar',
	FeatureBannerInvoicesTile1Title: 'Få betalt 2 gånger snabbare',
	FeatureBannerInvoicesTile2ActionLabel: 'Spåra kassaflöde • 2 min',
	FeatureBannerInvoicesTile2Description: 'Minska obetalda fakturor och håll koll på din inkomst',
	FeatureBannerInvoicesTile2Title: 'Spåra din inkomst, smärtfritt',
	FeatureBannerInvoicesTile3Title: 'Fakturering och betalningar',
	FeatureBannerInvoicesTitle: 'En sak mindre att oroa sig för',
	FeatureBannerSubheader:
		'Vårdbeskyddarmallar gjorda av vårt team och community. Prova nya resurser eller dela dina egna!',
	FeatureBannerTeamTile1ActionLabel: 'Bjud in nu',
	FeatureBannerTeamTile1Description: 'Bjud in teammedlemmar till ditt konto och gör samarbetet enkelt',
	FeatureBannerTeamTile1Title: 'Samla ditt team',
	FeatureBannerTeamTile2ActionLabel: 'Ställ in tillgänglighet • 2 min',
	FeatureBannerTeamTile2Description: 'Hantera ditt teams tillgänglighet för att undvika dubbelbokningar',
	FeatureBannerTeamTile2Title: 'Ställ in din tillgänglighet',
	FeatureBannerTeamTile3ActionLabel: 'Ange behörigheter • 2 min',
	FeatureBannerTeamTile3Description: 'Kontrollera åtkomst till känsliga data och verktyg för efterlevnad',
	FeatureBannerTeamTile3Title: 'Anpassa behörigheter och åtkomst',
	FeatureBannerTeamTitle: 'Inget stort uppnås ensam',
	FeatureBannerTemplatesTile1ActionLabel: 'Utforska biblioteket • 2 min',
	FeatureBannerTemplatesTile1Description: 'Välj från ett fantastiskt bibliotek med anpassningsbara resurser ',
	FeatureBannerTemplatesTile1Title: 'Minska din arbetsbelastning',
	FeatureBannerTemplatesTile2ActionLabel: 'Skicka nu • 2 min',
	FeatureBannerTemplatesTile2Description: 'Skicka vackra mallar till kunder för komplettering',
	FeatureBannerTemplatesTile2Title: 'Gör dokumentationen rolig',
	FeatureBannerTemplatesTile3Title: 'Mallar',
	FeatureBannerTemplatesTitle: 'Mallar för absolut vad som helst',
	FeatureLimitBannerDescription:
		'Uppgradera nu för att fortsätta skapa och hantera {featureName} utan avbrott och få ut mesta möjliga av Carepatron!',
	FeatureLimitBannerTitle: 'Du är {percentage}% på väg till din {featureName}-gräns',
	FeatureRequiresUpgrade: 'Den här funktionen kräver en uppgradering',
	Fee: 'Avgift',
	Female: 'Kvinnlig',
	FieldLabelTooltip: '{isHidden, select, true {Visa} other {Dölj}} fältlabel',
	FieldName: 'Fältnamn',
	FieldOptionsFirstPart: 'Första ordet',
	FieldOptionsMiddlePart: 'Mellanord',
	FieldOptionsSecondPart: 'Sista ordet',
	FieldOptionsWholeField: 'Hela fältet',
	FieldType: 'Fälttyp',
	Fields: 'Fält',
	File: 'Fil',
	FileDownloaded: '<strong>{fileName}</strong> nedladdad',
	FileInvalidType: 'Filen stöds inte.',
	FileNotFound: 'Filen hittades inte',
	FileNotFoundDescription: 'Filen du letar efter är inte tillgänglig eller har tagits bort',
	FileTags: 'Filtaggar',
	FileTagsHelper: 'Taggar kommer att tillämpas på alla filer',
	FileTooLarge: 'Filen är för stor.',
	FileTooSmall: 'Filen är för liten.',
	FileUploadComplete: 'Komplett',
	FileUploadFailed: 'Misslyckades',
	FileUploadInProgress: 'Belastning',
	FileUploadedNotificationSubject: '{actorProfileName} har laddat upp en fil',
	Files: 'Filer',
	FillOut: 'Fylla i',
	Filter: 'Filtrera',
	FilterBy: 'Filtrera efter',
	FilterByAmount: 'Filtrera efter mängd',
	FilterByClient: 'Filtrera efter klient',
	FilterByLocation: 'Filtrera efter plats',
	FilterByService: 'Filtrera efter tjänst',
	FilterByStatus: 'Filtrera efter status',
	FilterByTags: 'Filtrera efter taggar',
	FilterByTeam: 'Filtrera efter lag',
	Filters: 'Filter',
	FiltersAppliedToView: 'Filter tillämpade på vyn',
	FinalAppointment: 'Slutlig utnämning',
	FinalizeImport: 'Avsluta import',
	FinancialAnalyst: 'Finansanalytiker',
	Finish: 'Avsluta',
	Firefighter: 'Brandman',
	FirstName: 'Förnamn',
	FirstNameLastInitial: 'Förnamn, efterinitial',
	FirstPerson: '1:a person',
	FolderName: 'Mappnamn',
	Folders: 'Mappar',
	FontFamily: 'Typsnittsfamilj',
	ForClients: 'För kunder',
	ForClientsDetails: 'Jag får vård eller hälsorelaterade tjänster',
	ForPractitioners: 'För utövare',
	ForPractitionersDetails: 'Hantera och utveckla din praktik',
	ForgotPasswordConfirmAccessCode: 'Bekräftelsekod',
	ForgotPasswordConfirmNewPassword: 'Nytt lösenord',
	ForgotPasswordConfirmPageDescription:
		'Vänligen ange din e-postadress, ett nytt lösenord och bekräftelsekoden vi just har skickat till dig.',
	ForgotPasswordConfirmPageTitle: 'Återställ lösenord',
	ForgotPasswordPageButton: 'Skicka återställningslänk',
	ForgotPasswordPageDescription: 'Ange din e-postadress så skickar vi en länk för att återställa ditt lösenord.',
	ForgotPasswordPageTitle: 'Glömt lösenord',
	ForgotPasswordSuccessPageDescription: 'Kontrollera din inkorg efter din återställningslänk.',
	ForgotPasswordSuccessPageTitle: 'Återställ länk skickad!',
	Form: 'Form',
	FormAnswersSentToEmailNotification: 'Vi har skickat en kopia av dina svar till',
	FormBlocks: 'Forma block',
	FormFieldAddOption: 'Lägg till alternativ',
	FormFieldAddOtherOption: 'Lägg till "annat"',
	FormFieldOptionPlaceholder: 'Alternativ {index}',
	FormStructures: 'Forma strukturer',
	Format: 'Format',
	FormatLinkButtonColor: 'Knappen färg',
	Forms: 'Blanketter',
	FormsAndAgreementsValidationMessage:
		'Alla formulär och avtal måste fyllas i för att fortsätta med intagningsprocessen.',
	FormsCategoryDescription: 'För att samla in och organisera patientuppgifter',
	Frankfurt: 'Frankfurt',
	Free: 'Gratis',
	FreePlanInclusionFive: 'Automatiserad fakturering ',
	FreePlanInclusionFour: 'Kundportal',
	FreePlanInclusionHeader: 'Kom igång med',
	FreePlanInclusionOne: 'Obegränsade kunder',
	FreePlanInclusionSix: 'Live support',
	FreePlanInclusionThree: '1 GB lagringsutrymme',
	FreePlanInclusionTwo: 'Telehälsa',
	FreeSubscriptionPlanSubtitle: 'Gratis för alla',
	FreeSubscriptionPlanTitle: 'Gratis',
	Friday: 'fredag',
	From: 'Från',
	FullName: 'Fullständigt namn',
	FunctionalMedicineOrNaturopath: 'Funktionell medicin eller naturläkare',
	FuturePaymentsAuthoriseProvider: 'Tillåt {provider} att använda den sparade betalningen i framtiden',
	FuturePaymentsSavePaymentMethod: 'Spara {paymentMethod} för framtida betalningar',
	GST: 'moms',
	Gender: 'Kön',
	GeneralAvailability: 'Allmän tillgänglighet',
	GeneralAvailabilityDescription:
		'Ställ in när du är regelbundet tillgänglig. Kunder kommer endast att kunna boka dina tjänster under tillgängliga tider.',
	GeneralAvailabilityDescription2:
		'Skapa scheman baserat på din tillgänglighet och önskade tjänsteerbjudanden vid specifika tider för att avgöra din tillgänglighet för onlinebokningar.',
	GeneralAvailabilityInfo: 'Dina tillgängliga öppettider avgör tillgängligheten för din onlinebokning',
	GeneralAvailabilityInfo2:
		'Tjänster som erbjuder gruppevenemang bör använda ett nytt schema för att minska antalet tillgängliga timmar som det kan bokas av kunder online.',
	GeneralHoursPlural: '{count} {count, plural, one {timme} other {timmar}}',
	GeneralPractitioner: 'Allmänläkare',
	GeneralPractitioners: 'Allmänläkare',
	GeneralServiceAvailabilityInfo: 'Detta schema kommer att åsidosätta beteendet för tilldelade teammedlemmar',
	Generate: 'Generera',
	GenerateBillingItemsBannerContent: 'Faktureringsposter skapas inte automatiskt för återkommande möten.',
	GenerateItems: 'Skapa objekt',
	GenerateNote: 'Skapa anteckning',
	GenerateNoteConfirmationModalDescription:
		'Vad skulle du vilja göra? Skapa en ny genererad anteckning, lägga till den befintliga eller ersätta dess innehåll?',
	GenerateNoteFor: 'Skapa anteckning för',
	GeneratingContent: 'Genererar innehåll...',
	GeneratingNote: 'Genererar din anteckning...',
	GeneratingTranscript: 'Genererar avskrift',
	GeneratingTranscriptDescription: 'Detta kan ta några minuter att bearbeta',
	GeneratingYourTranscript: 'Genererar ditt avskrift',
	GenericErrorDescription: '{module} kunde inte laddas. Försök igen senare.',
	GenericErrorTitle: 'Oväntat fel inträffade',
	GenericFailureSnackbar: 'Tyvärr hände något oväntat. Uppdatera sidan och försök igen.',
	GenericSavedSuccessSnackbar: 'Framgång! Ändringar sparade',
	GeneticCounselor: 'Genetisk rådgivare',
	Gerontologist: 'Gerontolog',
	Get50PercentOff: 'Få 50% rabatt!',
	GetHelp: 'Få hjälp',
	GetStarted: 'Kom igång',
	GettingStartedAppointmentTypes: 'Skapa mötestyper',
	GettingStartedAppointmentTypesDescription:
		'Effektivisera din schemaläggning och fakturering genom att anpassa dina tjänster, priser och faktureringskoder',
	GettingStartedAppointmentTypesTitle: 'Schema ',
	GettingStartedClients: 'Lägg till dina kunder',
	GettingStartedClientsDescription: 'Kom igång med kunder för framtida möten, anteckningar och betalningar',
	GettingStartedClientsTitle: 'Allt börjar med kunderna',
	GettingStartedCreateClient: 'Skapa klient',
	GettingStartedImportClients: 'Importera kunder',
	GettingStartedInvoices: 'Fakturera som ett proffs',
	GettingStartedInvoicesDescription: `Det är enkelt att skapa professionella fakturor.
 Lägg till din logotyp, plats och betalningsvillkor`,
	GettingStartedInvoicesTitle: 'Sätt din bästa fot framåt',
	GettingStartedMobileApp: 'Skaffa mobilappen',
	GettingStartedMobileAppDescription:
		'Du kan ladda ner Carepatron på din iOS-, Android- eller stationära enhet för enkel åtkomst när du är på språng',
	GettingStartedMobileAppTitle: 'Arbeta var som helst',
	GettingStartedNavItem: 'Komma igång',
	GettingStartedPageTitle: 'Komma igång med Carepatron',
	GettingStartedPayments: 'Acceptera onlinebetalningar',
	GettingStartedPaymentsDescription: `Få betalt snabbare genom att låta dina kunder betala online.
 Se alla dina fakturor och betalningar på ett ställe`,
	GettingStartedPaymentsTitle: 'Gör betalningar enkelt',
	GettingStartedSaveBranding: 'Spara varumärke',
	GettingStartedSyncCalendars: 'Synkronisera andra kalendrar',
	GettingStartedSyncCalendarsDescription:
		'Carepatron kontrollerar din kalender för konflikter, så möten är bara schemalagda när du är tillgänglig',
	GettingStartedSyncCalendarsTitle: 'Håll dig alltid uppdaterad',
	GettingStartedVideo: 'Se en introduktionsvideo',
	GettingStartedVideoDescription: 'De första allt-i-ett-arbetsplatserna för sjukvård för små team och deras kunder',
	GettingStartedVideoTitle: 'Välkommen till Carepatron',
	GetttingStartedGetMobileDownload: 'Ladda ner appen',
	GetttingStartedGetMobileNoDownload:
		'Inte kompatibel med den här webbläsaren. Om du använder iPhone eller iPad, öppna den här sidan i Safari. Testa annars att öppna den i Chrome.',
	Glossary: 'Ordlista',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail tillåter bara att 500 meddelanden skickas från ditt konto på en dag. Vissa av meddelandena kan misslyckas. Vill du fortsätta?',
	GoToAppointment: 'Gå till möte',
	GoToApps: 'Gå till appar',
	GoToAvailability: 'Gå till tillgänglighet',
	GoToClientList: 'Gå till kundlistan',
	GoToClientRecord: 'Gå till kundpost',
	GoToClientSettings: 'Gå till klientinställningarna nu',
	GoToInvoiceTemplates: 'Gå till fakturamallar',
	GoToNotificationSettings: 'Gå till aviseringsinställningar',
	GoToPaymentSettings: 'Gå till betalningsinställningar',
	Google: 'Google',
	GoogleCalendar: 'Google Kalender',
	GoogleColor: 'Google kalenderfärg',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Tag Manager Container ID',
	GotIt: 'Jag förstår!',
	Goto: 'Gå till',
	Granddaughter: 'Sondotter',
	Grandfather: 'Farfar',
	Grandmother: 'Mormor',
	Grandparent: 'Morförälder',
	Grandson: 'Sonson',
	GrantPortalAccess: 'Ge portalåtkomst',
	GraphicDesigner: 'Grafisk formgivare',
	Grid: 'Rutnät',
	GridView: 'Rutnätsvy',
	Group: 'Grupp',
	GroupBy: 'Gruppera efter',
	GroupEvent: 'Grupphändelse',
	GroupEventHelper: 'Ställ in en deltagaregräns för tjänsten',
	GroupFilterLabel: 'Alla {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Grupp-ID',
	GroupInputFieldsFormPrimaryText: 'Gruppinmatningsfält',
	GroupInputFieldsFormSecondaryText: 'Välj eller lägg till anpassade fält',
	GuideTo: 'Guide till {value}',
	GuideToImproveVideoQuality: 'Guide för att förbättra videokvaliteten',
	GuideToManagingPayers: 'Hantera betalare',
	GuideToSubscriptionsBilling: 'Guide till fakturering av prenumerationer',
	GuideToTroubleshooting: 'Guide till felsökning',
	Guidelines: 'Riktlinjer',
	GuidelinesCategoryDescription: 'För vägledning av kliniskt beslutsfattande',
	HST: 'HST',
	HairStylist: 'Hårstylist',
	HaveBeenWaiting: 'Du har väntat länge',
	HeHim: 'Han/honom',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Kalender',
	HeaderCalls: 'Samtal',
	HeaderClientAppAccountSettings: 'Kontoinställningar',
	HeaderClientAppCalls: 'Samtal',
	HeaderClientAppMyDocumentation: 'Dokumentation',
	HeaderClientAppMyRelationships: 'Mina relationer',
	HeaderClients: 'Kunder',
	HeaderHelp: 'Hjälp',
	HeaderMoreOptions: 'Fler alternativ',
	HeaderStaff: 'Personal',
	HealthCoach: 'Hälsocoach',
	HealthCoaches: 'Hälsocoacher',
	HealthEducator: 'Hälsopedagog',
	HealthInformationTechnician: 'Hälsoinformationstekniker',
	HealthPolicyExpert: 'Hälsopolitisk expert',
	HealthServicesAdministrator: 'Hälsovårdsadministratör',
	HelpArticles: 'Hjälpartiklar',
	HiddenColumns: 'Dolda kolumner',
	HiddenFields: 'Dolda fält',
	HiddenSections: 'Dolda avsnitt',
	HiddenSectionsAndFields: 'Dolda sektioner/fält',
	HideColumn: 'Dölj kolumn',
	HideColumnButton: 'Dölj kolumn {value}-knapp',
	HideDetails: 'Dölj detaljer',
	HideField: 'Göm fält',
	HideFullAddress: 'Dölja',
	HideMenu: 'Dölj menyn',
	HideMergeSummarySidebar: 'Dölj sammanfattning av sammanslagning',
	HideSection: 'Dölj avsnitt',
	HideYourView: 'Dölj din syn',
	Highlight: 'Markera färg',
	Highlighter: 'Highlighter',
	History: 'Historia',
	HistoryItemFooter: '{actors, select, undefined {{date} kl. {time}} other {Av {actors} • {date} kl. {time}}}',
	HistorySidePanelEmptyState: 'Inga historikposter hittades',
	HistoryTitle: 'Aktivitetslogg',
	HolisticHealthPractitioner: 'Holistic Health Practitioner',
	HomeCaregiver: 'Hemvårdare',
	HomeHealthAide: 'Hemvårdsassistent',
	HomelessShelter: 'Hemlösa skydd',
	HourAbbreviation: '{count} {count, plural, one {tim} other {timmar}}',
	Hourly: 'Timvis',
	HoursPlural: '{age, plural, one {# timme} other {# timmar}}',
	HowCanWeImprove: 'Hur kan vi förbättra detta?',
	HowCanWeImproveResponse: 'Hur kan vi förbättra detta svar?',
	HowDidWeDo: 'Hur gick det?',
	HowDoesReferralWork: 'Guide till remissprogrammet',
	HowToUseAiSummarise: 'Hur man använder AI Summarize',
	HumanResourcesManager: 'Personalchef',
	Husband: 'Man',
	Hypnotherapist: 'Hypnosterapeut',
	IVA: 'IVA',
	IgnoreNotification: 'Ignorera avisering',
	IgnoreOnce: 'Ignorera en gång',
	IgnoreSender: 'Ignorera avsändaren',
	IgnoreSenderDescription:
		'Framtida konversationer från den här avsändaren kommer automatiskt att flyttas till "Annat". Är du säker på att du vill ignorera dessa avsändare?',
	IgnoreSenders: 'Ignorera avsändare',
	IgnoreSendersSuccess: 'Ignorerad e-postadress <mark>{addresses}</mark>',
	Ignored: 'Ignorerade',
	Image: 'Bild',
	Import: 'Importera',
	ImportActivity: 'Importera aktivitet',
	ImportClientSuccessSnackbarDescription: 'Din fil har importerats',
	ImportClientSuccessSnackbarTitle: 'Importen lyckades!',
	ImportClients: 'Importera kunder',
	ImportClientsFailureSnackbarDescription: 'Din fil kunde inte importeras på grund av ett fel.',
	ImportClientsFailureSnackbarTitle: 'Importen misslyckades!',
	ImportClientsGuide: 'Guide till import av klienter',
	ImportClientsInProgressSnackbarDescription: 'Detta bör bara ta upp till en minut att slutföra.',
	ImportClientsInProgressSnackbarTitle: 'Importerar {fileName}',
	ImportClientsModalDescription:
		'Välj var din data kommer ifrån – oavsett om det är en fil på din enhet, en tredjepartstjänst eller en annan mjukvaruplattform.',
	ImportClientsModalFileUploadHelperText: 'Stödjer {fileTypes}. Storleksgräns {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Guide för att importera klientdata',
	ImportClientsModalStep1Label: 'Välj datakälla',
	ImportClientsModalStep2Label: 'Ladda upp fil',
	ImportClientsModalStep3Label: 'Granska fält',
	ImportClientsModalTitle: 'Importera din kunddata',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {klient} other {klienter}} redo för import',
	ImportContactFailedNotificationSubject: 'Din dataimport misslyckades',
	ImportDataSourceSelectorLabel: 'Importera datakälla från',
	ImportDataSourceSelectorPlaceholder: 'Sök eller välj importera datakälla',
	ImportExportButton: 'Import/Export',
	ImportFailed: 'Importen misslyckades',
	ImportFromAnotherPlatformTileDescription: 'Ladda ner en export av dina klientfiler och ladda upp dem här.',
	ImportFromAnotherPlatformTileLabel: 'Importera från en annan plattform',
	ImportGuide: 'Importguide',
	ImportInProgress: 'Import pågår',
	ImportProcessing: 'Import bearbetning...',
	ImportSpreadsheetDescription:
		'Du kan importera din befintliga kundlista till Carepatron genom att ladda upp en kalkylarksfil med tabelldata, som .CSV, .XLS eller .XLSX',
	ImportSpreadsheetTitle: 'Importera din kalkylarksfil',
	ImportTemplates: 'Importera mallar',
	Importing: 'Importerar',
	ImportingCalendarProductEvents: 'Importera {product}-händelser',
	ImportingData: 'Importerar data',
	ImportingSpreadsheetDescription: 'Detta bör bara ta upp till en minut att slutföra',
	ImportingSpreadsheetTitle: 'Importerar ditt kalkylblad',
	ImportsInProgress: 'Importer pågår',
	InPersonMeeting: 'Personligt möte',
	InProgress: 'Pågår',
	InTransit: 'I transit',
	InTransitTooltip:
		'In Transit-saldo inkluderar alla betalda fakturautbetalningar från Stripe till ditt bankkonto. Dessa medel tar vanligtvis 3-5 dagar att lösa.',
	Inactive: 'Inaktiv',
	InboundOrOutboundCalls: 'Inkommande eller utgående samtal',
	Inbox: 'Inkorg',
	InboxAccessRestricted: 'Åtkomst begränsad. Kontakta inkorgens ägare för behörigheter.',
	InboxAccountAlreadyConnected: 'Kanalen du försökte ansluta är redan ansluten till Carepatron',
	InboxAddAttachments: 'Lägg till bilagor',
	InboxAreYouSureDeleteMessage: 'Är du säker på att du vill ta bort det här meddelandet?',
	InboxBulkCloseSuccess: '{count, plural, one {Stängde # konversationen} other {Stängde # konversationer}}',
	InboxBulkComposeModalTitle: 'Skriv massmeddelande',
	InboxBulkDeleteSuccess: '{count, plural, one {Har raderat # konversation} other {Har raderat # konversationer}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Markerade # konversation som läst} other {Markerade # konversationer som lästa}}',
	InboxBulkReopenSuccess: '{count, plural, one {Återöppnade # konversationen} other {Återöppnade # konversationer}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Markerade # konversation som oläst} other {Markerade # konversationer som olästa}}',
	InboxChatCreateGroup: 'Skapa grupp',
	InboxChatDeleteGroupModalDescription:
		'Är du säker på att du vill radera den här gruppen? Alla meddelanden och bilagor kommer att raderas.',
	InboxChatDeleteGroupModalTitle: 'Radera grupp',
	InboxChatDiscardDraft: 'Kasta utkast',
	InboxChatDragDropText: 'Släpp filer här för att ladda upp',
	InboxChatGroupConversation: 'Gruppkonversation',
	InboxChatGroupCreateModalDescription:
		'Starta en ny grupp för att skicka meddelanden och samarbeta med ditt team, klienter eller community.',
	InboxChatGroupCreateModalTitle: 'Skapa grupp',
	InboxChatGroupMembers: 'Gruppmedlemmar',
	InboxChatGroupModalGroupNameFieldLabel: 'Gruppnamn',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'T.ex. kundsupport, admin',
	InboxChatGroupModalGroupNameFieldRequired: 'Detta fält är obligatoriskt',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minst en medlem krävs',
	InboxChatGroupModalMembersFieldLabel: 'Välj gruppmedlemmar',
	InboxChatGroupModalMembersFieldPlaceholder: 'Välj medlemmar',
	InboxChatGroupUpdateModalTitle: 'Hantera grupp',
	InboxChatLeaveGroup: 'Lämna grupp',
	InboxChatLeaveGroupModalDescription:
		'Är du säker på att du vill lämna den här gruppen? Du kommer inte längre att få meddelanden eller uppdateringar.',
	InboxChatLeaveGroupModalTitle: 'Lämna grupp',
	InboxChatLeftGroupMessage: 'Vänster gruppmeddelande',
	InboxChatManageGroup: 'Hantera grupp',
	InboxChatSearchParticipants: 'Välj mottagare',
	InboxCloseConversationSuccess: 'Konversationen avslutades framgångsrikt',
	InboxCompose: 'Komponera',
	InboxComposeBulk: 'Massmeddelande',
	InboxComposeCarepatronChat: 'Budbärare',
	InboxComposeChat: 'Komponera chatt',
	InboxComposeDisabledNoConnection: 'Anslut ett e-postkonto för att skicka meddelanden',
	InboxComposeDisabledNoPermissionTooltip: 'Du har inte behörighet att skicka meddelanden från den här inkorgen',
	InboxComposeEmail: 'Skriv e-post',
	InboxComposeMessageFrom: 'Från',
	InboxComposeMessageRecipientBcc: 'Hemlig kopia',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'Till',
	InboxComposeMessageSubject: 'Ämne:',
	InboxConnectAccountButton: 'Anslut din e-post',
	InboxConnectedDescription: 'Din inkorg har ingen kommunikation',
	InboxConnectedHeading: 'Dina konversationer kommer att visas här så snart du börjar utbyta kommunikation',
	InboxConnectedHeadingClientView: 'Effektivisera din kundkommunikation',
	InboxCreateFirstInboxButton: 'Skapa din första inkorg',
	InboxCreationSuccess: 'Inkorgen har skapats',
	InboxDeleteAttachment: 'Ta bort bilaga',
	InboxDeleteConversationSuccess: 'Konversationen har raderats',
	InboxDeleteMessage: 'Ta bort meddelande?',
	InboxDirectMessage: 'Direktmeddelande',
	InboxEditDraft: 'Redigera utkast',
	InboxEmailComposeReplyEmail: 'Skriv svar',
	InboxEmailDraft: 'Förslag',
	InboxEmailNotFound: 'E-post hittades inte',
	InboxEmailSubjectFieldInformation: 'Om du ändrar ämnesraden skapas ett nytt trådat e-postmeddelande.',
	InboxEmptyArchiveDescription: 'Ingen arkiverad konversation har hittats',
	InboxEmptyBinDescription: 'Ingen raderad konversation har hittats',
	InboxEmptyBinHeading: 'Allt klart, inget att se här',
	InboxEmptyBinSuccess: 'Konversationer har raderats',
	InboxEmptyCongratsHeading: 'Bra jobbat! Luta dig tillbaka och koppla av tills nästa samtal',
	InboxEmptyDraftDescription: 'Inget utkast till konversation har hittats',
	InboxEmptyDraftHeading: 'Allt klart, inget att se här',
	InboxEmptyOtherDescription: 'Ingen annan konversation har hittats',
	InboxEmptyScheduledHeading: 'Allt är klart, inga konversationer planerade att skickas',
	InboxEmptySentDescription: 'Ingen skickad konversation har hittats',
	InboxForward: 'Fram',
	InboxGroupClientsLabel: 'Alla kunder',
	InboxGroupClientsOverviewLabel: 'Kunder',
	InboxGroupClientsSelectedItemPrefix: 'Klient',
	InboxGroupStaffsLabel: 'Hela laget',
	InboxGroupStaffsOverviewLabel: 'Team',
	InboxGroupStaffsSelectedItemPrefix: 'Team',
	InboxGroupStatusLabel: 'Alla status',
	InboxGroupStatusOverviewLabel: 'Skicka till en status',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Alla taggar',
	InboxGroupTagsOverviewLabel: 'Skicka till en tagg',
	InboxGroupTagsSelectedItemPrefix: 'Märka',
	InboxHideQuotedText: 'Dölj citerad text',
	InboxIgnoreConversationSuccess: 'Konversationen ignorerades framgångsrikt',
	InboxMessageAllLabelRecipientsCount: 'Alla {label} mottagare ({count})',
	InboxMessageBodyPlaceholder: 'Lägg till ditt meddelande',
	InboxMessageDeleted: 'Meddelandet raderat',
	InboxMessageMarkedAsRead: 'Meddelandet markerat som läst',
	InboxMessageMarkedAsUnread: 'Meddelande markerat som oläst',
	InboxMessageSentViaChat: '**Skickat via chat**  • {time} av {name}',
	InboxMessageShowMoreRecipients: '+{count} fler',
	InboxMessageWasDeleted: 'Detta meddelande har tagits bort',
	InboxNoConnectionDescription: 'Anslut ditt e-postkonto eller skapa inkorgar med flera e-postmeddelanden',
	InboxNoConnectionHeading: 'Integrera din kundkommunikation',
	InboxNoDirectMessage: 'Inga nya meddelanden',
	InboxRecentConversations: 'Senaste',
	InboxReopenConversationSuccess: 'Konversationen har öppnats igen',
	InboxReply: 'Svar',
	InboxReplyAll: 'Svara alla',
	InboxRestoreConversationSuccess: 'Konversationen har återställts',
	InboxScheduleSendCancelSendSuccess: 'Schemalagd sändning avbröts och meddelandet har återställts till utkast',
	InboxScheduleSendMessageSuccessDescription: 'Skickad schemalagd för {date}',
	InboxScheduleSendMessageSuccessTitle: 'Schemalägg skicka',
	InboxSearchForConversations: 'Sök efter "{query}"',
	InboxSendMessageSuccess: 'Skickade konversationen',
	InboxSettings: 'Inkorgsinställningar',
	InboxSettingsAppsDesc:
		'Hantera anslutna appar för den här delade inkorgen: lägg till eller ta bort anslutningar efter behov.',
	InboxSettingsAppsNewConnectedApp: 'Ny ansluten app',
	InboxSettingsAppsTitle: 'Anslutna appar',
	InboxSettingsDeleteAccountFailed: 'Det gick inte att ta bort inkorgskontot',
	InboxSettingsDeleteAccountSuccess: 'Inkorgen har tagits bort',
	InboxSettingsDeleteAccountWarning:
		'Att ta bort {email} kommer att koppla bort den från inkorg {inboxName} och kommer att stoppa meddelanden från att synkroniseras.',
	InboxSettingsDeleteInboxFailed: 'Det gick inte att ta bort inkorgen',
	InboxSettingsDeleteInboxSuccess: 'Inkorgen har raderats',
	InboxSettingsDeleteInboxWarning:
		'Att ta bort {inboxName} kommer att koppla från alla anslutna kanaler och ta bort alla meddelanden som är associerade med den här inkorg.		Denna åtgärd är permanent och kan inte ångras.',
	InboxSettingsDetailsDesc: 'Kommunikationsinkorg för ditt team att hantera klientmeddelanden effektivt.',
	InboxSettingsDetailsTitle: 'Inkorgen detaljer',
	InboxSettingsEmailSignatureLabel: 'Standard för e-postsignatur',
	InboxSettingsReplyFormatDesc:
		'Ställ in din standardsvarsadress och e-postsignatur så att den visas konsekvent, oavsett vem som skickar e-postmeddelandet.',
	InboxSettingsReplyFormatTitle: 'Svarsformat',
	InboxSettingsSendFromLabel: 'Ställ in ett standardsvar från ',
	InboxSettingsStaffDesc: 'Hantera åtkomst för teammedlemmar till denna delade inkorg för smidigt samarbete.',
	InboxSettingsStaffTitle: 'Tilldela gruppmedlemmar',
	InboxSettingsUpdateInboxDetailsFailed: 'Det gick inte att uppdatera inkorgen',
	InboxSettingsUpdateInboxDetailsSuccess: 'Inkorgens detaljer har uppdaterats',
	InboxSettingsUpdateInboxStaffsFailed: 'Det gick inte att uppdatera inkorgsteammedlemmar',
	InboxSettingsUpdateInboxStaffsSuccess: 'Inkorgsteammedlemmarna har uppdaterats framgångsrikt',
	InboxSettingsUpdateReplyFormatFailed: 'Det gick inte att uppdatera svarsformatet',
	InboxSettingsUpdateReplyFormatSuccess: 'Svarsformatet har uppdaterats',
	InboxShowQuotedText: 'Visa citerad text',
	InboxStaffRoleAdminDescription: 'Visa, svara och hantera inkorgar',
	InboxStaffRoleResponderDescription: 'Se och svara',
	InboxStaffRoleViewerDescription: 'Endast visning',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Fortsätt redigera',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Ja, byt till masssändning',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Du har valt fler än {count} mottagare. Vill du skicka det som ett massutskick?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Varning',
	InboxSwitchToOtherInbox: 'Byt till en annan inkorg',
	InboxUndoSendMessageSuccess: 'Sändning har ångrats',
	IncludeLineItems: 'Inkludera rader',
	IncludeSalesTax: 'Beskattningsbar',
	IncludesAiSmartPrompt: 'Inkluderar AI-smarta uppmaningar',
	Incomplete: 'Ofullständig',
	IncreaseIndent: 'Öka indraget',
	IndianHealthServiceFreeStandingFacility: 'Fristående anläggning för Indian Health Service',
	IndianHealthServiceProviderFacility: 'Indisk vårdgivare-baserad anläggning',
	Information: 'Information',
	InitialAssessment: 'Inledande bedömning',
	InitialSignupPageClientFamilyTitle: 'Kund eller familjemedlem',
	InitialSignupPageProviderTitle: 'Hälsa ',
	InitialTreatment: 'Inledande behandling',
	Initials: 'Initialer',
	InlineEmbed: 'Inbäddat inline',
	InputPhraseToConfirm: 'För att bekräfta, skriv {confirmationPhrase}.',
	Insert: 'Infoga',
	InsertTable: 'Sätt in tabell',
	InstallCarepatronOnYourIphone1: 'Installera Carepatron på din iOS: tryck',
	InstallCarepatronOnYourIphone2: 'och sedan Lägg till på startskärmen',
	InsufficientCalendarScopesSnackbar:
		'Synkronisering misslyckades - vänligen tillåt kalenderbehörigheter till Carepatron',
	InsufficientInboxScopesSnackbar: 'Synkronisering misslyckades - vänligen tillåt e-postbehörigheter till Carepatron',
	InsufficientScopeErrorCodeSnackbar:
		'Synkronisering misslyckades - vänligen tillåt alla behörigheter till Carepatron',
	Insurance: 'Försäkring',
	InsuranceAmount: 'Försäkringsbelopp',
	InsuranceClaim: 'Skadeanmälan',
	InsuranceClaimAiChatPlaceholder: 'Fråga om försäkringsanspråket...',
	InsuranceClaimAiClaimNumber: 'Anspråk {number}',
	InsuranceClaimAiSubtitle: 'Försäkringsfakturering • Kravvalidering',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber} som skickades till {payerNumber} {payerName} nekades',
	InsuranceClaimErrorDescription:
		'Anspråket innehåller fel som rapporterats av betalaren eller clearinghuset. Granska följande felmeddelanden och skicka in anspråket igen.',
	InsuranceClaimErrorGuideLink: 'Guide till försäkringsanspråk',
	InsuranceClaimErrorTitle: 'Fel vid inlämning av anspråk',
	InsuranceClaimNotFound: 'Försäkringskravet hittades inte',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {En delbetalning på {paymentAmount}} other {En betalning på {paymentAmount}}} för fordran {claimNumber} av {payerNumber} {payerName} har registrerats',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber} som skickades till {payerNumber} {payerName} avvisades',
	InsuranceClaims: 'Försäkringsanspråk',
	InsuranceInformation: 'Försäkringsinformation',
	InsurancePaid: 'Försäkring betald',
	InsurancePayer: 'Försäkringsbetalare',
	InsurancePayers: 'Försäkringsbetalare',
	InsurancePayersDescription: 'Visa de betalare som har lagts till på ditt konto och hantera registrering.',
	InsurancePayment: 'Försäkringsbetalning',
	InsurancePoliciesDetailsSubtitle: 'Lägg till kundförsäkringsinformation för att stödja anspråk.',
	InsurancePoliciesDetailsTitle: 'Policydetaljer',
	InsurancePoliciesListSubtitle: 'Lägg till kundförsäkringsinformation för att stödja anspråk.',
	InsurancePoliciesListTitle: 'Försäkringar',
	InsuranceSelfPay: 'Självbetalning',
	InsuranceType: 'Typ av försäkring',
	InsuranceUnpaid: 'Försäkring obetald',
	Intake: 'Intag',
	IntakeExpiredErrorCodeSnackbar: 'Detta intag har gått ut. Kontakta din leverantör för att skicka ett nytt intag.',
	IntakeNotFoundErrorSnackbar:
		'Detta intag kunde inte hittas. Kontakta din leverantör för att skicka ett nytt intag.',
	IntakeProcessLearnMoreInstructions: 'Guide för att ställa in dina intagsformulär',
	IntakeTemplateSelectorPlaceholder: 'Välj formulär och avtal att skicka till din kund för att fylla i',
	Integration: 'Integration',
	IntenseBlur: 'Gör din bakgrund intensivt suddig',
	InteriorDesigner: 'Inredningsarkitekt',
	InternetBanking: 'Banköverföring',
	Interval: 'Intervall',
	IntervalDays: 'Intervall (dagar)',
	IntervalHours: 'Intervall (timmar)',
	Invalid: 'Ogiltig',
	InvalidDate: 'Ogiltigt datum',
	InvalidDateFormat: 'Datum måste vara i {format}-format',
	InvalidDisplayName: 'Visningsnamn får inte innehålla {value}',
	InvalidEmailFormat: 'Ogiltigt e-postformat',
	InvalidFileType: 'Ogiltig filtyp',
	InvalidGTMContainerId: 'Ogiltigt format för GTM-behållare-ID',
	InvalidPaymentMethodCode: 'Den valda betalningsmetoden är inte giltig. Välj en annan.',
	InvalidPromotionCode: 'Kampanjkoden är ogiltig',
	InvalidReferralDescription: 'Använder redan Carepatron',
	InvalidStatementDescriptor: `Utsagnsbeskrivning måste vara mellan 5 och 22 tecken lång och endast innehålla bokstäver, siffror, mellanslag och får inte innehålla <, >, \\, ', ", *`,
	InvalidToken: 'Ogiltig token',
	InvalidTotpSetupVerificationCode: 'Ogiltig verifieringskod.',
	InvalidURLErrorText: 'Detta måste vara en giltig URL',
	InvalidZoomTokenErrorCodeSnackbar: 'Zoomtoken har gått ut. Anslut din Zoom-app igen och försök igen.',
	Invite: 'Bjuda',
	InviteRelationships: 'Bjud in relationer',
	InviteToPortal: 'Bjud in till portalen',
	InviteToPortalModalDescription:
		'Ett inbjudningsmeddelande kommer att skickas till din kund för att registrera sig hos Carepatron.',
	InviteToPortalModalTitle: 'Bjud in {name} till Carepatron-portalen',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Bjud in ny användare',
	Invited: 'Inbjudna',
	Invoice: 'Faktura',
	InvoiceColorPickerDescription: 'Färgtema som ska användas i fakturan',
	InvoiceColorTheme: 'Faktura färgtema',
	InvoiceContactDeleted: 'Fakturakontakten har tagits bort och denna faktura kan inte uppdateras.',
	InvoiceDate: 'Datum utfärdat',
	InvoiceDetails: 'Fakturadetaljer',
	InvoiceFieldsPlaceholder: 'Sök efter fält...',
	InvoiceFrom: 'Faktura {number} från {fromProvider}',
	InvoiceInvalidCredit: 'Ogiltigt kreditbelopp, kreditbeloppet får inte överstiga fakturasumman',
	InvoiceNotFoundDescription:
		'Kontakta din leverantör och be dem om mer information eller för att skicka om fakturan.',
	InvoiceNotFoundTitle: 'Fakturan hittades inte',
	InvoiceNumber: 'Faktura #',
	InvoiceNumberFormat: 'Faktura #{number}',
	InvoiceNumberMustEndWithDigit: 'Fakturanummer måste sluta med en siffra (0-9)',
	InvoicePageHeader: 'Fakturor',
	InvoicePaidNotificationSubject: 'Faktura {invoiceNumber} betald',
	InvoiceReminder: 'Fakturapåminnelser',
	InvoiceReminderSentence: 'Skicka {deliveryType} påminnelse {interval} {unit} {beforeAfter} fakturadatum',
	InvoiceReminderSettings: 'Inställningar för fakturapåminnelse',
	InvoiceReminderSettingsInfo: 'Påminnelser gäller endast fakturor skickade på Carepatron',
	InvoiceReminders: 'Fakturapåminnelser',
	InvoiceRemindersInfo:
		'Ställ in automatiska påminnelser för fakturans förfallodatum. Påminnelser gäller endast fakturor som skickas via Carepatron',
	InvoiceSettings: 'Fakturainställningar',
	InvoiceStatus: 'Fakturastatus',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, USA',
	InvoiceTemplateDescriptionPlaceholder:
		'Lägg till anteckningar, banköverföringsuppgifter eller villkor för alternativa betalningar',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Egen företagare',
	InvoiceTemplateEthnicityPlaceholder: 'kaukasiska',
	InvoiceTemplateNotFoundDescription: 'Kontakta din leverantör och be dem om mer information.',
	InvoiceTemplateNotFoundTitle: 'Fakturamall hittades inte',
	InvoiceTemplates: 'Fakturamallar',
	InvoiceTemplatesDescription:
		'Skräddarsy dina fakturamallar för att spegla ditt varumärke, uppfylla regulatoriska krav och tillgodose kundens preferenser med våra användarvänliga mallar.',
	InvoiceTheme: 'Fakturatema',
	InvoiceTotal: 'Faktura total',
	InvoiceUninvoicedAmounts: 'Fakturera ofakturerade belopp',
	InvoiceUpdateVersionMessage:
		'För att redigera denna faktura krävs den senaste versionen. Ladda om Carepatron och försök igen.',
	Invoices: '{count, plural, one {Faktura} other {Fakturor}}',
	InvoicesEmptyStateDescription: 'Inga fakturor har hittats',
	InvoicingAndPayment: 'Fakturering ',
	Ireland: 'Irland',
	IsA: 'är en',
	IsBetween: 'är mellan',
	IsEqualTo: 'är lika med',
	IsGreaterThan: 'är större än',
	IsGreaterThanOrEqualTo: 'är större än eller lika med',
	IsLessThan: 'är mindre än',
	IsLessThanOrEqualTo: 'är mindre än eller lika med',
	IssueCredit: 'Ge kredit',
	IssueCreditAdjustment: 'Utfärda kreditjustering',
	IssueDate: 'Utgivningsdatum',
	Italic: 'Kursiv',
	Items: 'Föremål',
	ItemsAndAdjustments: 'Artiklar och justeringar',
	ItemsRemaining: '+{count} objekt kvar',
	JobTitle: 'Arbetstitel',
	Join: 'Ansluta sig till',
	JoinCall: 'Gå med i samtalet',
	JoinNow: 'Gå med nu',
	JoinProduct: 'Gå med i {product}',
	JoinVideoCall: 'Gå med i videosamtal',
	JoinWebinar: 'Gå med i webbinarium',
	JoinWithVideoCall: 'Gå med i {product}',
	Journalist: 'Journalist',
	JustMe: 'Bara jag',
	JustYou: 'Bara du',
	Justify: 'Rättfärdiga',
	KeepSeparate: 'Håll separat',
	KeepSeparateSuccessMessage: 'Du har lyckats hålla separata register för {clientNames}',
	KeepWaiting: 'Fortsätt vänta',
	Label: 'Märka',
	LabelOptional: 'Etikett (valfritt)',
	LactationConsulting: 'Amningsrådgivning',
	Language: 'Språk',
	Large: 'Stor',
	LastDxCode: 'Sista DX-kod',
	LastLoggedIn: 'Senast inloggad {date} kl. {time}',
	LastMenstrualPeriod: 'Sista menstruationen',
	LastMonth: 'Förra månaden',
	LastNDays: 'Senaste {number} dagarna',
	LastName: 'Efternamn',
	LastNameFirstInitial: 'Efternamn, första initial',
	LastWeek: 'Förra veckan',
	LastXRay: 'Sista röntgen',
	LatestVisitOrConsultation: 'Senaste besök eller konsultation',
	Lawyer: 'Advokat',
	LearnMore: 'Läs mer',
	LearnMoreTipsToGettingStarted: 'Lär dig mer om att komma igång',
	LearnToSetupInbox: 'Guide för att ställa in ett inkorgskonto',
	Leave: 'Lämna',
	LeaveCall: 'Lämna samtalet',
	LeftAlign: 'Vänsterjustera',
	LegacyBillingItemsNotAvailable:
		'Individuella fakturaposter är ännu inte tillgängliga för detta möte. Du kan fortfarande fakturera det normalt.',
	LegacyBillingItemsNotAvailableTitle: 'Arvodesfakturering',
	LegalAndConsent: 'Lagligt och samtycke',
	LegalConsentFormPrimaryText: 'Lagligt samtycke',
	LegalConsentFormSecondaryText: 'Acceptera eller avvisa alternativ',
	LegalGuardian: 'Förmyndare',
	Letter: 'Brev',
	LettersCategoryDescription: 'För att skapa klinisk och administrativ korrespondens',
	Librarian: 'Bibliotekarie',
	LicenseNumber: 'Licensnummer',
	LifeCoach: 'Livscoach',
	LifeCoaches: 'Livscoacher',
	Limited: 'Begränsad',
	LineSpacing: 'Rad- och styckeavstånd',
	LinearScaleFormPrimaryText: 'Linjär skala',
	LinearScaleFormSecondaryText: 'Skalalternativ 1-10',
	Lineitems: 'Rader',
	Link: 'Länk',
	LinkClientFormSearchClientLabel: 'Sök efter en kund',
	LinkClientModalTitle: 'Länk till befintlig klient',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> kontaktinformation läggs till i <strong>{existingName}’s</strong> post.',
	LinkClientSuccessTitle: 'Länkad till befintlig kontakt',
	LinkForCallCopied: 'Länken kopierad!',
	LinkToAnExistingClient: 'Länka till en befintlig klient',
	LinkToClient: 'Länk till klient',
	ListAndTracker: 'Lista/Spårare',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} är i det här samtalet}
			other {{attendees} är i det här samtalet}
		}`,
	ListStyles: 'Lista stilar',
	ListsAndTrackersCategoryDescription: 'För att organisera och spåra arbete',
	LivingArrangements: 'Livsarrangemang',
	LoadMore: 'Ladda mer',
	Loading: 'Belastning...',
	LocalizationPanelDescription: 'Hantera inställningar för ditt språk och din tidszon',
	LocalizationPanelTitle: 'Språk och tidszon',
	Location: 'Plats',
	LocationDescription:
		'Ställ in fysiska och virtuella platser med specifika adresser, rumsnamn och typer av virtuella utrymmen för att göra det enklare att schemalägga möten och videosamtal.',
	LocationNumber: 'Platsnummer',
	LocationOfService: 'Plats för tjänsten',
	LocationOfServiceRecommendedActionInfo:
		'Att lägga till en specifik plats till denna tjänst kan påverka din tillgänglighet.',
	LocationRemote: 'Fjärrstyrd',
	LocationType: 'Platstyp',
	Locations: 'Platser',
	Lock: 'Låsa',
	Locked: 'Låst',
	LockedNote: 'Låst lapp',
	LogInToSaveOrAuthoriseCard: 'Logga in för att spara eller auktorisera kortet',
	LogInToSaveOrAuthorisePayment: 'Logga in för att spara eller godkänna betalningen',
	Login: 'Logga in',
	LoginButton: 'Logga in',
	LoginEmail: 'E-post',
	LoginForgotPasswordLink: 'Glömt lösenord',
	LoginPassword: 'Lösenord',
	Logo: 'Logotyp',
	LogoutAreYouSure: 'Logga ut från den här enheten.',
	LogoutButton: 'Logga ut',
	London: 'London',
	LongTextAnswer: 'Långt textsvar',
	LongTextFormPrimaryText: 'Lång text',
	LongTextFormSecondaryText: 'Alternativ för styckestil',
	Male: 'Manlig',
	Manage: 'Hantera',
	ManageAllClientTags: 'Hantera alla klienttaggar',
	ManageAllNoteTags: 'Hantera alla anteckningstaggar',
	ManageAllTemplateTags: 'Hantera alla malltaggar',
	ManageConnections: 'Hantera anslutningar',
	ManageConnectionsGmailDescription: 'Andra teammedlemmar kommer inte att kunna se din synkroniserade Gmail.',
	ManageConnectionsGoogleCalendarDescription:
		'Andra teammedlemmar kommer inte att kunna se dina synkroniserade kalendrar. Kundens möten kan endast uppdateras eller raderas från Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Vänligen gå till sidan Inkorg för att hantera inställningarna för Sync Inbox.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Andra teammedlemmar kommer inte att kunna se dina synkroniserade kalendrar. Kundens möten kan endast uppdateras eller raderas från Carepatron.',
	ManageConnectionsOutlookDescription:
		'Andra teammedlemmar kommer inte att kunna se din synkroniserade Microsoft Outlook.',
	ManageInboxAccountButton: 'Ny inkorg',
	ManageInboxAccountEdit: 'Hantera inkorgen',
	ManageInboxAccountPanelTitle: 'Inkorgar',
	ManageInboxAssignTeamPlaceholder: 'Välj gruppmedlemmar för åtkomst till inkorgen',
	ManageInboxBasicInfoColor: 'Färg',
	ManageInboxBasicInfoDescription: 'Beskrivning',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Vad kommer du eller ditt team att använda den här inkorgen till?',
	ManageInboxBasicInfoName: 'Inkorgens namn',
	ManageInboxBasicInfoNamePlaceholder: 'T ex kundsupport, admin',
	ManageInboxConnectAppAlreadyConnectedError: 'Kanalen du försökte ansluta är redan ansluten till Carepatron',
	ManageInboxConnectAppConnect: 'Ansluta',
	ManageInboxConnectAppConnectedInfo: 'Ansluten till ett konto',
	ManageInboxConnectAppContinue: 'Fortsätta',
	ManageInboxConnectAppEmail: 'E-post',
	ManageInboxConnectAppSignInWith: 'Logga in med',
	ManageInboxConnectAppSubtitle:
		'Anslut dina appar för att sömlöst skicka, ta emot och spåra all din kommunikation på en centraliserad plats.',
	ManageInboxNewInboxTitle: 'Ny inkorg',
	ManagePlan: 'Hantera plan',
	ManageProfile: 'Hantera profil',
	ManageReferralsModalDescription: 'Hjälp oss att sprida budskapet om vår sjukvårdsplattform och tjäna belöningar.',
	ManageReferralsModalTitle: 'Värva en vän, tjäna belöningar!',
	ManageStaffRelationshipsAddButton: 'Hantera relationer',
	ManageStaffRelationshipsEmptyStateText: 'Inga relationer har lagts till',
	ManageStaffRelationshipsModalDescription:
		'Om du väljer klienter läggs nya relationer till, medan om du avmarkerar dem tas befintliga relationer bort.',
	ManageStaffRelationshipsModalTitle: 'Hantera relationer',
	ManageStatuses: 'Hantera statusar',
	ManageStatusesActiveStatusHelperText: 'Minst en aktiv status krävs',
	ManageStatusesDescription: 'Anpassa dina statusetiketter och välj färger som passar ditt arbetsflöde.',
	ManageStatusesSuccessSnackbar: 'Statuser har uppdaterats',
	ManageTags: 'Hantera taggar',
	ManageTaskAttendeeStatus: 'Hantera mötesstatus',
	ManageTaskAttendeeStatusDescription: 'Anpassa dina mötesstatusar för att stämma överens med ditt arbetsflöde.',
	ManageTaskAttendeeStatusHelperText: 'Minst en status krävs',
	ManageTaskAttendeeStatusSubtitle: 'Anpassade statusar',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manuell',
	ManualAppointment: 'Manuell bokning',
	ManualPayment: 'Manuell betalning',
	ManuallyTypeLocation: 'Ange plats manuellt',
	MapColumns: 'Karta kolumner',
	MappingRequired: 'Kartläggning krävs',
	MarkAllAsRead: 'Markera alla som lästa',
	MarkAsCompleted: 'Markera som klar',
	MarkAsManualSubmission: 'Markera som inlämnad',
	MarkAsPaid: 'Markera som betald',
	MarkAsRead: 'Markera som läst',
	MarkAsUnpaid: 'Markera som obetalt',
	MarkAsUnread: 'Markera som oläst',
	MarkAsVoid: 'Markera som ogiltig',
	Marker: 'Markör',
	MarketingManager: 'Marknadschef',
	MassageTherapist: 'Massageterapeut',
	MassageTherapists: 'Massageterapeuter',
	MassageTherapy: 'Massageterapi',
	MaxBookingTimeDescription1: 'Kunder kan schemalägga upp till',
	MaxBookingTimeDescription2: 'in i framtiden',
	MaxBookingTimeLabel: '{timePeriod} i förväg',
	MaxCapacity: 'Max kapacitet',
	Maximize: 'Maximera',
	MaximumAttendeeLimit: 'Maxgräns',
	MaximumBookingTime: 'Maximal bokningstid',
	MaximumBookingTimeError: 'Maximal bokningstid får inte överstiga {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Du kan minimera upp till {count} sidpaneler åt gången. Om du fortsätter kommer den äldsta minimerade panelen att stängas. Vill du fortsätta?',
	MaximumMinimizedPanelsReachedTitle: 'Du har för många paneler öppna.',
	MechanicalEngineer: 'Maskiningenjör',
	MediaGallery: 'Mediegalleri',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid-leverantörsnummer',
	MedicalAssistant: 'Medicinsk assistent',
	MedicalCoder: 'Medicinsk kodare',
	MedicalDoctor: 'Läkare',
	MedicalIllustrator: 'Medicinsk illustratör',
	MedicalInterpreter: 'Medicinsk tolk',
	MedicalTechnologist: 'Medicinsk teknolog',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare-leverantörens nummer',
	Medicine: 'Medicin',
	Medium: 'Medium',
	Meeting: 'Möte',
	MeetingEnd: 'Avsluta mötet',
	MeetingEnded: 'Mötet avslutat',
	MeetingHost: 'Mötesvärd',
	MeetingLowerHand: 'Sänk handen',
	MeetingOpenChat: 'Öppna Chatt',
	MeetingPersonRaisedHand: '{name} höjde sin hand',
	MeetingRaiseHand: 'Räck upp handen',
	MeetingReady: 'Möte redo',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Möten',
	MemberId: 'Medlems-ID',
	MentalHealth: 'Mental hälsa',
	MentalHealthPractitioners: 'Psykiatriker',
	MentalHealthProfessional: 'Psykolog',
	Merge: 'Slå ihop',
	MergeClientRecords: 'Släpp samman klientuppgifter',
	MergeClientRecordsDescription: 'Sammanfoga klientuppgifter kommer att kombinera all deras data, inklusive:',
	MergeClientRecordsDescription2: 'Vill du fortsätta med sammanslagningen? Den här åtgärden går inte att ångra.',
	MergeClientRecordsItem1: 'Anteckningar och dokument',
	MergeClientRecordsItem2: 'Bokningar',
	MergeClientRecordsItem3: 'Fakturor',
	MergeClientRecordsItem4: 'Samtal',
	MergeClientsSuccess: 'Framgångsrikt slås kundregistret samman',
	MergeLimitExceeded: 'Du kan bara slå samman upp till 4 klienter åt gången.',
	Message: 'Meddelande',
	MessageAttachments: '{total} bilagor',
	Method: 'Metod',
	MfaAvailabilityDisclaimer:
		'MFA är endast tillgängligt för e-post- och lösenordsinloggningar. För att göra ändringar i dina MFA-inställningar, logga in med din e-postadress och ditt lösenord.',
	MfaDeviceLostPanelDescription: 'Alternativt kan du verifiera din identitet genom att få en kod via e-post.',
	MfaDeviceLostPanelTitle: 'Har du tappat bort din MFA-enhet?',
	MfaDidntReceiveEmailCode: 'Fick du ingen kod? Kontakta supporten',
	MfaEmailOtpSendFailureSnackbar: 'Det gick inte att skicka e-post OTP.',
	MfaEmailOtpSentSnackbar: 'En kod har skickats till {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Det gick inte att verifiera OTP för e-post.',
	MfaHasBeenSetUpText: 'Du har ställt in MFA',
	MfaPanelDescription:
		'Säkra ditt konto genom att aktivera Multi-Factor Authentication (MFA) för ett extra lager av skydd. Verifiera din identitet genom en sekundär metod för att förhindra obehörig åtkomst.',
	MfaPanelNotAuthorizedError: 'Du måste vara inloggad med användarnamn ',
	MfaPanelRecommendationDescription:
		'Du loggade nyligen in med en alternativ metod för att verifiera din identitet. För att hålla ditt konto säkert, överväg att konfigurera en ny MFA-enhet.',
	MfaPanelRecommendationTitle: '**Rekommenderat:** Uppdatera din MFA-enhet',
	MfaPanelTitle: 'Multi-Factor Authentication (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'Du måste verifiera din e-post innan du kan uppdatera dina MFA-inställningar.',
	MfaRecommendationBannerDescription:
		'Du loggade nyligen in med en alternativ metod för att verifiera din identitet. För att hålla ditt konto säkert, överväg att konfigurera en ny MFA-enhet.',
	MfaRecommendationBannerPrimaryAction: 'Ställ in MFA',
	MfaRecommendationBannerTitle: 'Rekommenderad',
	MfaRemovedSnackbarTitle: 'MFA har tagits bort.',
	MfaSendEmailCode: 'Skicka kod',
	MfaVerifyIdentityLostDeviceButton: 'Jag förlorade åtkomsten till min MFA-enhet',
	MfaVerifyYourIdentityPanelDescription: 'Kontrollera din autentiseringsapp efter koden och ange den nedan.',
	MfaVerifyYourIdentityPanelTitle: 'Verifiera din identitet',
	MicCamWarningMessage:
		'Avblockera kamera och mikrofon genom att klicka på blockerade ikoner i webbläsarens adressfält.',
	MicCamWarningTitle: 'Kamera och mikrofon är blockerade',
	MicOff: 'Mikrofonen är avstängd',
	MicOn: 'Mikrofonen är på',
	MicSource: 'Mikrofonkälla',
	MicWarningMessage: 'Ett problem har upptäckts med din mikrofon',
	Microphone: 'Mikrofon',
	MicrophonePermissionBlocked: 'Mikrofonåtkomst blockerad',
	MicrophonePermissionBlockedDescription: 'Uppdatera dina mikrofonbehörigheter för att börja spela in.',
	MicrophonePermissionError: 'Vänligen ge mikrofonbehörighet i webbläsaren dina inställningar för att fortsätta',
	MicrophonePermissionPrompt: 'Tillåt mikrofonåtkomst för att fortsätta',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlook-kalenderfärg',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Teams',
	MiddleEast: 'Mellanöstern',
	MiddleName: 'Mellannamn',
	MiddleNames: 'Mellannamn',
	Midwife: 'Barnmorska',
	Midwives: 'Barnmorskor',
	Milan: 'Milano',
	MinBookingTimeDescription1: 'Kunder kan inte schemalägga inom',
	MinBookingTimeDescription2: 'av ett mötes starttid',
	MinBookingTimeLabel: '{timePeriod} före mötet',
	MinCancellationTimeEditModeDescription: 'Ställ in hur många timmar en klient kan avbryta utan straffavgift',
	MinCancellationTimeUnset: 'Ingen minsta avbokningstid inställd',
	MinCancellationTimeViewModeDescription: 'Avbeställningstid utan påföljd',
	MinMaxBookingTimeUnset: 'Ingen tid inställd',
	Minimize: 'Minimera',
	MinimizeConfirmationDescription:
		'Du har en aktiv minimerad panel. Om du fortsätter kommer den att stängas, och du kan förlora osparade data.',
	MinimizeConfirmationTitle: 'Stäng ihop det minimerade paneler?',
	MinimumBookingTime: 'Minsta bokningstid',
	MinimumCancellationTime: 'Minsta avbokningstid',
	MinimumPaymentError: 'En minimi avgift på {minimumAmount} krävs för onlinebetalningar.',
	MinuteAbbreviated: 'min',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minutvis',
	MinutesPlural: '{age, plural, one {# minut} other {# minuter}}',
	MiscellaneousInformation: 'Diverse information',
	MissingFeatures: 'Saknade funktioner',
	MissingPaymentMethod: 'Lägg till en betalningsmetod till din prenumeration för att lägga till fler anställda.',
	MobileNumber: 'Mobilnummer',
	MobileNumberOptional: 'Mobilnummer (valfritt)',
	Modern: 'Modern',
	Modifiers: 'Modifierare',
	ModifiersPlaceholder: 'Modifierare',
	Monday: 'måndag',
	Month: 'Månad',
	Monthly: 'Månatlig',
	MonthlyCost: 'Månadskostnad',
	MonthlyOn: 'Månadsvis den {date}',
	MonthsPlural: '{age, plural, one {# månad} other {# månader}}',
	More: 'Mer',
	MoreActions: 'Fler åtgärder',
	MoreSettings: 'Fler inställningar',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Mest använda',
	MostDownloaded: 'Mest nedladdade',
	MostPopular: 'Mest populära',
	Mother: 'Mor',
	MotherInLaw: 'Svärmor',
	MoveDown: 'Flytta ner',
	MoveInboxConfirmationDescription:
		'Om du tilldelar om den här appans anslutning kommer den att tas bort från <strong>{currentInboxName}</strong>-inkorgen.',
	MoveTemplateToFolder: 'Flytta `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} flyttades till {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Flyttad till standardintagningsmappen',
	MoveTemplateToNewFolder: 'Skapa en ny mapp för att flytta det här objektet till.',
	MoveToChosenFolder: 'Välj en mapp att flytta det här objektet till. Du kan skapa en ny mapp om det behövs.',
	MoveToFolder: 'Flytta till mapp',
	MoveToInbox: 'Flytta till inkorgen',
	MoveToNewFolder: 'Flytta till ny mapp',
	MoveToSelectedFolder:
		'När objektet har flyttats kommer det att organiseras under den valda mappen och kommer inte längre att visas på sin nuvarande plats.',
	MoveUp: 'Flytta upp',
	MultiSpeciality: 'Multispecialitet',
	MultipleChoiceFormPrimaryText: 'Flerval',
	MultipleChoiceFormSecondaryText: 'Välj flera alternativ',
	MultipleChoiceGridFormPrimaryText: 'Flervalsrutnät',
	MultipleChoiceGridFormSecondaryText: 'Välj alternativ från en matris',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Musikterapeut',
	MustContainOneLetterError: 'Måste innehålla minst en bokstav',
	MustEndWithANumber: 'Måste avslutas med ett nummer',
	MustHaveAtLeastXItems: 'Måste ha minst {count, plural, one {# objekt} other {# objekt}}',
	MuteAudio: 'Stäng av ljudet',
	MuteEveryone: 'Stäng av ljudet för alla',
	MyAvailability: 'Min tillgänglighet',
	MyGallery: 'Mitt galleri',
	MyPortal: 'Min portal',
	MyRelationships: 'Mina relationer',
	MyTemplates: 'Team mallar',
	MyofunctionalTherapist: 'Myofunktionell terapeut',
	NCalifornia: 'Norra Kalifornien',
	NPI: 'NPI',
	NVirginia: 'North Virginia',
	Name: 'Namn',
	NameIsRequired: 'Namn krävs',
	NameMustNotBeAWebsite: 'Namnet får inte vara en webbplats',
	NameMustNotBeAnEmail: 'Namnet får inte vara ett e-postmeddelande',
	NameMustNotContainAtSign: 'Namnet får inte innehålla @-tecken',
	NameMustNotContainHTMLTags: 'Namnet får inte innehålla HTML-taggar',
	NameMustNotContainSpecialCharacters: 'Namnet får inte innehålla specialtecken',
	NameOnCard: 'Namn på kortet',
	NationalProviderId: 'Nationell leverantörsidentifierare (NPI)',
	NaturopathicDoctor: 'Naturläkare',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Prenumerationsinställningar',
	NavigateToWorkspaceSettings: 'Arbetsplatsinställningar',
	NavigateToYourTeam: 'Hantera team',
	NavigationDrawerBilling: 'Fakturering',
	NavigationDrawerBillingInfo: 'Faktureringsinformation, fakturor och Stripe',
	NavigationDrawerCommunication: 'Kommunikation',
	NavigationDrawerCommunicationInfo: 'Aviseringar och mallar',
	NavigationDrawerInsurance: 'Försäkring',
	NavigationDrawerInsuranceInfo: 'Försäkringsbetalare och skador',
	NavigationDrawerInvoices: 'Fakturering',
	NavigationDrawerPersonal: 'Min profil',
	NavigationDrawerPersonalInfo: 'Dina personliga uppgifter',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Inställningar',
	NavigationDrawerScheduling: 'Schemaläggning',
	NavigationDrawerSchedulingInfo: 'Servicedetaljer och bokningar',
	NavigationDrawerSettings: 'Inställningar',
	NavigationDrawerTemplates: 'Mallar',
	NavigationDrawerTemplatesV2: 'Mallar V2',
	NavigationDrawerTrash: 'Skräp',
	NavigationDrawerTrashInfo: 'Återställ raderade objekt',
	NavigationDrawerWorkspace: 'Inställningar för arbetsyta',
	NavigationDrawerWorkspaceInfo: 'Prenumeration och arbetsyta info',
	NegativeBalanceNotSupported: 'Negativa kontosaldon stöds inte',
	Nephew: 'Brorson',
	NetworkQualityFair: 'Rättvis anslutning',
	NetworkQualityGood: 'Bra anslutning',
	NetworkQualityPoor: 'Dålig anslutning',
	Neurologist: 'Neurolog',
	Never: 'Aldrig',
	New: 'Ny',
	NewAppointment: 'Nytt möte',
	NewClaim: 'Nytt påstående',
	NewClient: 'Ny kund',
	NewClientNextStepsModalAddAnotherClient: 'Lägg till en annan klient',
	NewClientNextStepsModalBookAppointment: 'Boka tid',
	NewClientNextStepsModalBookAppointmentDescription: 'Boka ett kommande möte eller skapa en uppgift.',
	NewClientNextStepsModalCompleteBasicInformation: 'Komplett kundregister',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'Lägg till kundinformation och fånga nästa steg.',
	NewClientNextStepsModalCreateInvoice: 'Skapa faktura',
	NewClientNextStepsModalCreateInvoiceDescription: 'Lägg till kundbetalningsinformation eller skapa en faktura.',
	NewClientNextStepsModalCreateNote: 'Skapa anteckning eller ladda upp dokument',
	NewClientNextStepsModalCreateNoteDescription: 'Fånga klientanteckningar och dokumentation.',
	NewClientNextStepsModalDescription: 'Här är några åtgärder att vidta nu när du har skapat en kundpost.',
	NewClientNextStepsModalSendIntake: 'Skicka intag',
	NewClientNextStepsModalSendIntakeDescription:
		'Samla in kundinformation och skicka ytterligare formulär för ifyllande och signering.',
	NewClientNextStepsModalSendMessage: 'Skicka meddelande',
	NewClientNextStepsModalSendMessageDescription: 'Skriv och skicka ett meddelande till din klient.',
	NewClientNextStepsModalTitle: 'Nästa steg',
	NewClientSuccess: 'Ny klient har skapats',
	NewClients: 'Nya kunder',
	NewConnectedApp: 'Ny ansluten app',
	NewContact: 'Ny kontakt',
	NewContactNextStepsModalAddRelationship: 'Lägg till relation',
	NewContactNextStepsModalAddRelationshipDescription: 'Länka denna kontakt till relaterade klienter eller grupper.',
	NewContactNextStepsModalBookAppointment: 'Boka tid',
	NewContactNextStepsModalBookAppointmentDescription: 'Boka ett kommande möte eller skapa en uppgift.',
	NewContactNextStepsModalCompleteProfile: 'Fullständig profil',
	NewContactNextStepsModalCompleteProfileDescription: 'Lägg till kontaktinformation och fånga nästa steg.',
	NewContactNextStepsModalCreateNote: 'Skapa anteckning eller ladda upp dokument',
	NewContactNextStepsModalCreateNoteDescription: 'Fånga klientanteckningar och dokumentation.',
	NewContactNextStepsModalDescription: 'Här är några åtgärder att vidta nu när du har skapat en kontakt.',
	NewContactNextStepsModalInviteToPortal: 'Inbjudan till portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Skicka en inbjudan till åtkomst till portalen.',
	NewContactNextStepsModalTitle: 'Nästa steg',
	NewContactSuccess: 'Ny kontakt har skapats',
	NewDateOverrideButton: 'Åsidosätt nytt datum',
	NewDiagnosis: 'Lägg till diagnos',
	NewField: 'Nytt fält',
	NewFolder: 'Ny mapp',
	NewInvoice: 'Ny faktura',
	NewLocation: 'Ny plats',
	NewLocationFailure: 'Det gick inte att skapa en ny plats',
	NewLocationSuccess: 'Ny plats har skapats',
	NewManualPayer: 'Ny manuell betalare',
	NewNote: 'Ny lapp',
	NewNoteCreated: 'Ny anteckning har skapats',
	NewPassword: 'Nytt lösenord',
	NewPayer: 'Ny betalare',
	NewPaymentMethod: 'Ny betalningsmetod',
	NewPolicy: 'Ny policy',
	NewRelationship: 'Ny relation',
	NewReminder: 'Ny påminnelse',
	NewSchedule: 'Nytt schema',
	NewSection: 'Nytt avsnitt',
	NewSectionOld: 'Nytt avsnitt [GAMMEL]',
	NewSectionWithGrid: 'Ny sektion med rutnät',
	NewService: 'Ny tjänst',
	NewServiceFailure: 'Det gick inte att skapa ny tjänst',
	NewServiceSuccess: 'Ny tjänst har skapats',
	NewStatus: 'Ny status',
	NewTask: 'Ny uppgift',
	NewTaxRate: 'Ny skattesats',
	NewTeamMemberNextStepsModalAssignClients: 'Tilldela klienter',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Tilldela specifika klienter till din teammedlem.',
	NewTeamMemberNextStepsModalAssignServices: 'Tilldela tjänster',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Hantera sina tilldelade tjänster och justera priserna vid behov.',
	NewTeamMemberNextStepsModalBookAppointment: 'Boka tid',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Boka en kommande tid eller skapa en uppgift.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Fullständig profil',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Lägg till information om din teammedlem för att slutföra deras profil.',
	NewTeamMemberNextStepsModalDescription: 'Här är några åtgärder att vidta nu när du har skapat en teammedlem.',
	NewTeamMemberNextStepsModalEditPermissions: 'Redigeringsbehörigheter',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Justera deras åtkomstnivåer för att säkerställa att de har rätt behörigheter.',
	NewTeamMemberNextStepsModalSetAvailability: 'Ställ in tillgänglighet',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Konfigurera deras tillgänglighet för att skapa scheman.',
	NewTeamMemberNextStepsModalTitle: 'Nästa steg',
	NewTemplateFolderDescription: 'Skapa en ny mapp för att organisera din dokumentation.',
	NewUIUpdateBannerButton: 'Ladda om appen',
	NewUIUpdateBannerTitle: 'En ny uppdatering är klar!',
	NewZealand: 'Nya Zeeland',
	Newest: 'Nyaste',
	NewestUnreplied: 'Nyaste obesvarade',
	Next: 'Nästa',
	NextInvoiceIssueDate: 'Nästa fakturadatum',
	NextNDays: 'Nästa {number} dagar',
	Niece: 'Niece',
	No: 'Inga',
	NoAccessGiven: 'Ingen tillgång ges',
	NoActionConfigured: 'Ingen åtgärd konfigurerad',
	NoActivePolicies: 'Ingen aktiv politik',
	NoActiveReferrals: 'Du har inga aktiva hänvisningar',
	NoAppointmentsFound: 'Inga möten har hittats',
	NoAppointmentsHeading: 'Hantera kundmöten och aktivitet',
	NoArchivedPolicies: 'Inga arkiverade policyer',
	NoAvailableTimes: 'Inga tillgängliga tider hittades.',
	NoBillingItemsFound: 'Inga faktureringsposter hittades',
	NoCalendarsSynced: 'Inga kalendrar synkroniserade',
	NoClaimsFound: 'Inga anspråk hittades',
	NoClaimsHeading: 'Effektivisera inlämnande av ersättningsanspråk',
	NoClientsHeading: 'Samla dina kundregister',
	NoCompletedReferrals: 'Du har inga fullständiga hänvisningar',
	NoConnectionsHeading: 'Effektivisera din kundkommunikation',
	NoContactsGivenAccess: 'Inga kunder eller kontakter har fått tillgång till denna anteckning',
	NoContactsHeading: 'Håll kontakten med dem som stödjer din praktik',
	NoCopayOrCoinsurance: 'Ingen co-pay eller co-assurance',
	NoCustomServiceSchedule: 'Inget anpassat schema satt – tillgängligheten beror på teammedlemmens tillgänglighet',
	NoDescription: 'Ingen beskrivning',
	NoDocumentationHeading: 'Skapa och lagra anteckningar på ett säkert sätt',
	NoDuplicateRecordsHeading: 'Din kundpost är fri från dubbletter',
	NoEffect: 'Ingen effekt',
	NoEnrolmentProfilesFound: 'Inga inregistreringsprofiler hittades',
	NoGlossaryItems: 'Inga ordlisteposter',
	NoInvitedReferrals: 'Du har inga inbjudna hänvisningar',
	NoInvoicesFound: 'Inga fakturor hittades',
	NoInvoicesHeading: 'Automatisera din fakturering och betalningar',
	NoLimit: 'Ingen gräns',
	NoLocationsFound: 'Inga platser har hittats',
	NoLocationsWillBeAdded: 'Inga platser kommer att läggas till.',
	NoNoteFound: 'Ingen anteckning hittades',
	NoPaymentMethods: 'Du har inga sparade betalningsmetoder, du kan lägga till en när du gör en betalning.',
	NoPermissionError: 'Du har inte behörighet',
	NoPermissions: 'Du har inte behörighet att se den här sidan',
	NoPolicy: 'Ingen avbokningsregler har lagts till',
	NoRecordsHeading: 'Anpassa dina kundregister',
	NoRecordsToDisplay: 'Inget {resource} att visa',
	NoRelationshipsHeading: 'Håll kontakten med dem som stöttar din klient',
	NoRemindersFound: 'Inga påminnelser hittades',
	NoResultsFound: 'Inga resultat hittades',
	NoResultsFoundDescription: 'Vi kan inte hitta några objekt som matchar din sökning',
	NoServicesAdded: 'Inga tjänster tillagda',
	NoServicesApplied: 'Inga tjänster tillämpas',
	NoServicesWillBeAdded: 'Inga tjänster kommer att läggas till.',
	NoTemplate: 'Du har inga övningsmallar sparade',
	NoTemplatesHeading: 'Skapa dina egna mallar',
	NoTemplatesInFolder: 'Inga mallar i den här mappen',
	NoTitle: 'Ingen titel',
	NoTrashItemsHeading: 'Inget borttaget objekt hittades',
	NoTriggerConfigured: 'Ingen trigger konfigurerad',
	NoUnclaimedItemsFound: 'Inga outtagna föremål hittades.',
	NonAiTemplates: 'Icke-AI-mallar',
	None: 'Ingen',
	NotAvailable: 'Inte tillgänglig',
	NotCovered: 'Ej täckt',
	NotFoundSnackbar: 'Resurs hittades inte.',
	NotRequiredField: 'Krävs inte',
	Note: 'Notera',
	NoteDuplicateSuccess: 'Anteckningen har duplicerats',
	NoteEditModeViewSwitcherDescription: 'Skapa och redigera anteckning',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} skickade in formuläret {noteTitle}',
	NoteLockSuccess: '{title} har blivit låst',
	NoteModalAttachmentButton: 'Lägg till bilagor',
	NoteModalPhotoButton: 'Lägg till/fånga foton',
	NoteModalTrascribeButton: 'Transkribera liveljud',
	NoteResponderModeViewSwitcherDescription: 'Skicka formulär och granska svar',
	NoteResponderModeViewSwitcherTooltipTitle: 'Svara och skicka in formulär för dina kunders räkning',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Fyll i och skicka in formulär som kund',
	NoteUnlockSuccess: '{title} har blivit upplåst',
	NoteViewModeViewSwitcherDescription: 'Endast visningsåtkomst',
	Notes: 'Anteckningar',
	NotesAndForms: 'Anteckningar och formulär',
	NotesCategoryDescription: 'För att dokumentera klientsamtal',
	NothingToSeeHere: 'Inget att se här',
	Notification: 'Underrättelse',
	NotificationIgnoredMessage: 'Alla {notificationType} notiser kommer att ignoreras',
	NotificationRestoredMessage: 'Alla {notificationType}-notiser återställda',
	NotificationSettingBillingDescription: 'Ta emot aviseringar om uppdateringar och påminnelser om kundbetalningar.',
	NotificationSettingBillingTitle: 'Fakturering och betalning',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} endast} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Ta emot aviseringar om uppdateringar och påminnelser om kundbetalningar.',
	NotificationSettingClientDocumentationTitle: 'Kund och dokumentation',
	NotificationSettingCommunicationsDescription:
		'Få aviseringar för inkorgen och uppdateringar från dina anslutna kanaler',
	NotificationSettingCommunicationsTitle: 'Kommunikationer',
	NotificationSettingEmail: 'E-post',
	NotificationSettingInApp: 'I appen',
	NotificationSettingPanelDescription: 'Välj vilka aviseringar du vill ta emot för aktiviteter och rekommendationer.',
	NotificationSettingPanelTitle: 'Aviseringsinställningar',
	NotificationSettingSchedulingDescription:
		'Få meddelanden när en gruppmedlem eller kund bokar, ombokar eller avbokar sitt möte.',
	NotificationSettingSchedulingTitle: 'Schemaläggning',
	NotificationSettingUpdateSuccess: 'Aviseringsinställningar uppdaterade',
	NotificationSettingWhereYouReceiveNotifications: 'Var du vill ta emot dessa aviseringar',
	NotificationSettingWorkspaceDescription:
		'Ta emot aviseringar om systemförändringar, problem, dataöverföringar och prenumerationspåminnelser.',
	NotificationSettingWorkspaceTitle: 'Arbetsyta',
	NotificationTemplateUpdateFailed: 'Det gick inte att uppdatera meddelandemallen',
	NotificationTemplateUpdateSuccess: 'Aviseringsmall har uppdaterats',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Vill du skicka ett e-postmeddelande om avbokning till deltagarna?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Skicka avbokning',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Vill du skicka ett bekräftelsemeddelande via e-post till deltagarna?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Skicka bekräftelse',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Vill du skicka e-postmeddelanden om avbokning till deltagare?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {har} other {har}} inte en e-postadress och kommer därför inte att få automatiska meddelanden och påminnelser.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {har} other {har}} inte en e-postadress, så de kommer inte att få automatiska meddelanden och påminnelser.',
	NotifyAttendeesOfTaskModalTitle: 'Vill du skicka ett e-postmeddelande till deltagarna?',
	NotifyAttendeesOfTaskSnackbar: 'Skickar avisering',
	NuclearMedicineTechnologist: 'Nukleärmedicinsk teknolog',
	NumberOfClaims: '{number, plural, one {# Anspråk} other {# Anspråk}}',
	NumberOfClients: '{number, plural, one {# Klient} other {# Klienter}}',
	NumberOfContacts: '{number, plural, one {# Kontakt} other {# Kontakter}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {post} other {poster}} hittades',
	NumberOfErrors: '{count, plural, one {# fel} other {# fel}}',
	NumberOfInvoices: '{number, plural, one {# Faktura} other {# Fakturor}}',
	NumberOfLineitemsToCredit:
		'Du har <mark>{count} {count, plural, one {rad} other {rader}}</mark> att utfärda en kredit för.',
	NumberOfPayments: '{number, plural, one {# Betalning} other {# Betalningar}}',
	NumberOfRelationships: '{number, plural, one {# Relation} other {# Relationer}}',
	NumberOfResources: '{number, plural, one {# Resurs} other {# Resurser}}',
	NumberOfTeamMembers: '{number, plural, one {# Lagmedlem} other {# Lagmedlemmar}}',
	NumberOfTrashItems: '{number, plural, one {# objekt} other {# objekt}}',
	NumberOfUninvoicedAmounts:
		'Du har <mark>{count} ofakturerade {count, plural, one {belopp} other {belopp}}</mark> som ska faktureras',
	NumberedList: 'Numrerad lista',
	Nurse: 'Sjuksköterska',
	NurseAnesthetist: 'Narkosläkare',
	NurseAssistant: 'Undersköterska assistent',
	NurseEducator: 'Sjuksköterskepedagog',
	NurseMidwife: 'Sjuksköterska barnmorska',
	NursePractitioner: 'Sjuksköterska',
	Nurses: 'Sjuksköterskor',
	Nursing: 'Amning',
	Nutritionist: 'Nutritionist',
	Nutritionists: 'Nutritionister',
	ObstetricianOrGynecologist: 'Förlossningsläkare/Gynekolog',
	Occupation: 'Ockupation',
	OccupationalTherapist: 'Arbetsterapeut',
	OccupationalTherapists: 'Arbetsterapeuter',
	OccupationalTherapy: 'Arbetsterapi',
	Occurrences: 'Händelser',
	Of: 'av',
	Ohio: 'Ohio',
	OldPassword: 'Gammalt lösenord',
	OlderMessages: '{count} äldre meddelanden',
	Oldest: 'Äldst',
	OldestUnreplied: 'Äldsta obesvarade',
	On: 'på',
	OnboardingBusinessAgreement: 'På uppdrag av mig själv och företaget godkänner jag {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Arbetsterapeuter</mark> utgör en fjärdedel av våra kunder på Carepatron',
	OnboardingLoadingProfession: 'Vi har massor av <mark>{profession}</mark> som använder och trivs på Carepatron.',
	OnboardingLoadingPsychologist: '<mark>Psykologer</mark> utgör över hälften av våra kunder på Carepatron',
	OnboardingLoadingSubtitleFive: 'Vårt uppdrag är att göra<mark> vårdprogramvara tillgänglig</mark> till alla.',
	OnboardingLoadingSubtitleFour:
		'<mark>Förenklad hälsoprogramvara</mark> för mer än 10 000 människor över hela världen.',
	OnboardingLoadingSubtitleThree:
		'Spara<mark> 1 dag per vecka</mark> på administrativa uppgifter med hjälp av Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Spara<mark> 2 timmar</mark> dagligen på administrativa uppgifter med hjälp av Carepatron.',
	OnboardingReviewLocationOne: 'Holland Park Mental Health Center',
	OnboardingReviewLocationThree: 'Praktik sjuksköterska, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Life House Clinic',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron är superintuitivt att använda. Det hjälper oss att driva vår praktik så bra att vi inte ens behöver ett team av administratörer längre."',
	OnboardingReviewThree:
		'"Det är den bästa lösningen jag har använt både när det gäller funktioner och kostnad. Den har allt jag behöver för att växa mitt företag"',
	OnboardingReviewTwo:
		'"Jag älskar också carepatron-appen. Hjälper mig att hålla reda på mina kunder och arbeta när jag är på språng."',
	OnboardingTitle: `Låt oss komma till<mark> veta
 du bättre</mark>`,
	Oncologist: 'Onkolog',
	Online: 'Online',
	OnlineBookingColorTheme: 'Online bokning färgtema',
	OnlineBookings: 'Onlinebokningar',
	OnlineBookingsHelper: 'Välj när onlinebokningar kan göras och av vilken typ av kunder',
	OnlinePayment: 'Online betalning',
	OnlinePaymentSettingCustomInfo:
		'Inställningar för onlinebetalning för den här tjänsten skiljer sig från de globala bokningsinställningarna.',
	OnlinePaymentSettings: 'Inställningar för onlinebetalning',
	OnlinePaymentSettingsInfo:
		'Samla in betalningar för tjänster vid tidpunkten för onlinebokning för att säkra och effektivisera betalningar',
	OnlinePaymentSettingsPaymentsDisabled:
		'Betalningar är inaktiverade och kan därför inte samlas in under onlinebokning. Kontrollera dina betalningsinställningar för att aktivera betalningar.',
	OnlinePaymentSettingsStripeNote:
		'{action} för att ta emot onlinebokningsbetalningar och effektivisera din betalningsprocess',
	OnlinePaymentsNotSupportedForCurrency: 'Onlinebetalningar stöds inte i {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'Tyvärr, onlinebetalningar stöds inte i denna valuta',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: 'Tyvärr, onlinebetalningar stöds ännu inte i ditt land',
	OnlineScheduling: 'Schemaläggning online',
	OnlyVisibleToYou: 'Endast synlig för dig',
	OnlyYou: 'Bara du',
	OnsetDate: 'Startdatum',
	OnsetOfCurrentSymptomsOrIllness: 'Debut av aktuella symtom eller sjukdom',
	Open: 'Öppna',
	OpenFile: 'Öppna filen',
	OpenSettings: 'Öppna inställningar',
	Ophthalmologist: 'Ögonläkare',
	OptimiseTelehealthCalls: 'Optimera dina Telehealth-samtal',
	OptimizeServiceTimes: 'Optimera servicetider',
	Options: 'Alternativ',
	Optometrist: 'Optiker',
	Or: 'eller',
	OrAttachSingleFile: 'bifoga en fil',
	OrDragAndDrop: 'eller dra och släpp',
	OrderBy: 'Beställ efter',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisation eller individ',
	OrganizationPlanInclusion1: 'Avancerade behörigheter',
	OrganizationPlanInclusion2: 'Gratis stöd för import av klientdata',
	OrganizationPlanInclusion3: 'Dedikerad framgångschef',
	OrganizationPlanInclusionHeader: 'Allt inom Professional, plus...',
	Orthodontist: 'Ortodontist',
	Orthotist: 'Ortotist',
	Other: 'Andra',
	OtherAdjustments: 'Övriga justeringar',
	OtherAdjustmentsTableEmptyState: 'Inga justeringar hittades',
	OtherEvents: 'Andra evenemang',
	OtherId: 'Annat ID',
	OtherIdQualifier: 'Annan ID-kvalificerare',
	OtherPaymentMethod: 'Annat betalningssätt',
	OtherPlanMessage:
		'Håll koll på din praktikens behov. Granska din nuvarande plan, övervaka användningen och utforska uppgraderingsalternativ för att låsa upp fler funktioner när ditt team växer.',
	OtherPolicy: 'Övriga försäkringar',
	OtherProducts: 'Vilka andra produkter eller verktyg använder du?',
	OtherServices: 'Övriga tjänster',
	OtherTemplates: 'Andra mallar',
	Others: 'Andra',
	OthersPeople: `{n, plural, 		one {1 annan person}
		other {# andra personer}
	}`,
	OurResearchTeamReachOut:
		'Kan vårt forskarteam nå ut för att lära sig mer om hur Carepatron kunde ha varit bättre för dina behov?',
	OutOfOffice: 'Utanför kontoret',
	OutOfOfficeColor: 'Out of office färg',
	OutOfOfficeHelper: 'Vissa utvalda teammedlemmar är frånvarande',
	OutsideLabCharges: 'Utanför labbavgifter',
	OutsideOfWorkingHours: 'Utanför arbetstid',
	OutsideWorkingHoursHelper: 'Vissa teammedlemmar som valts ut är utanför arbetstid',
	Overallocated: 'Övertilldelad',
	OverallocatedPaymentDescription: `Denna betalning har allokerats över till fakturerbara poster.
 Lägg till en allokering till obetalda artiklar eller utfärda en kredit eller återbetalning.`,
	OverallocatedPaymentTitle: 'Övertilldelad betalning',
	OverdueTerm: 'Försenad termin (dagar)',
	OverinvoicedAmount: 'Överfakturerat belopp',
	Overpaid: 'Överbetalt',
	OverpaidAmount: 'Överbetalt belopp',
	Overtime: 'övertid',
	Owner: 'Ägare',
	POS: 'POS',
	POSCode: 'POS-kod',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Osparade ändringar kommer att gå förlorade. Vill du fortfarande lämna?',
	PageBlockerTitle: 'Släpp ändringarna?',
	PageFormat: 'Sidformat',
	PageNotFound: 'Sidan hittades inte',
	PageNotFoundDescription: 'Du har inte längre tillgång till den här sidan eller så kan den inte hittas',
	PageUnauthorised: 'Obehörig åtkomst',
	PageUnauthorisedDescription: 'Du har inte behörighet att komma åt den här sidan',
	Paid: 'Betalt',
	PaidAmount: 'Betalt belopp',
	PaidAmountMinimumValueError: 'Betalt belopp måste vara större än 0',
	PaidAmountRequiredError: 'Betalt belopp krävs',
	PaidItems: 'Betalda föremål',
	PaidMultiple: 'Betalt',
	PaidOut: 'Utbetalt',
	ParagraphStyles: 'Styckestilar',
	Parent: 'Förälder',
	Paris: 'Paris',
	PartialRefundAmount: 'Delvis återbetalad ({amount} kvar)',
	PartiallyFull: 'Delvis full',
	PartiallyPaid: 'Delvis betald',
	PartiallyRefunded: 'Återbetalas delvis',
	Partner: 'Partner',
	Password: 'Lösenord',
	Past: 'Förbi',
	PastDateOverridesEmpty: 'Dina datum åsidosättningar kommer att visas här så snart händelsen har passerat',
	Pathologist: 'Patolog',
	Patient: 'Patient',
	Pause: 'Paus',
	Paused: 'Pausad',
	Pay: 'Betala',
	PayMonthly: 'Betala månadsvis',
	PayNow: 'Betala nu',
	PayValue: 'Betala {showPrice, select, true {{price}} other {nu}}',
	PayWithOtherCard: 'Betala med annat kort',
	PayYearly: 'Betala årligen',
	PayYearlyPercentOff: 'Betala årligen <mark>{percent}% rabatt</mark>',
	Payer: 'Betalare',
	PayerClaimId: 'Betalningskravs-ID',
	PayerCoverage: 'Rapportering',
	PayerDetails: 'Betalaruppgifter',
	PayerDetailsDescription: 'Se betalarinformationen som har lagts till på ditt konto och hantera registreringen.',
	PayerID: 'Betalar-ID',
	PayerId: 'Betalar-ID',
	PayerName: 'Betalarens namn',
	PayerPhoneNumber: 'Betalarens telefonnummer',
	Payers: 'Betalare',
	Payment: 'Betalning',
	PaymentAccountUpdated: 'Ditt konto har uppdaterats!',
	PaymentAccountUpgraded: 'Ditt konto har uppgraderats!',
	PaymentAmount: 'Betalningsbelopp',
	PaymentDate: 'Betalningsdatum',
	PaymentDetails: 'Betalningsuppgifter',
	PaymentForUsersPerMonth: 'Betalning för {billedUsers, plural, one {# användare} other {# användare}} per månad',
	PaymentInfoFormPrimaryText: 'Betalningsinformation',
	PaymentInfoFormSecondaryText: 'Samla betalningsuppgifter',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Denna faktura har redan betalats.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Denna faktura behandlas redan.',
	PaymentIntentAmountMismatchSnackbar:
		'Det totala beloppet på fakturan har ändrats. Granska ändringarna innan du betalar.',
	PaymentIntentSyncTimeoutSnackbar:
		'Din betalning lyckades men en timeout inträffade. Uppdatera sidan och kontakta supporten om din betalning inte visas.',
	PaymentMethod: 'Betalningsmetod',
	PaymentMethodDescription:
		'Lägg till och hantera din praktiska betalningsmetod för att effektivisera faktureringsprocessen för din prenumeration.',
	PaymentMethodLabelBank: 'bankkonto',
	PaymentMethodLabelCard: 'kort',
	PaymentMethodLabelFallback: 'betalningsmetod',
	PaymentMethodRequired: 'Lägg till en betalningsmetod innan du ändrar prenumeration',
	PaymentMethods: 'Betalningsmetoder',
	PaymentProcessing: 'Betalningshantering!',
	PaymentProcessingFee: 'Betalningen inkluderar en behandlingsavgift på {amount}',
	PaymentReports: 'Betalningsrapporter (ERA)',
	PaymentSettings: 'Betalningsinställningar',
	PaymentSuccessful: 'Betalningen lyckades!',
	PaymentType: 'Betalningstyp',
	Payments: 'Betalningar',
	PaymentsAccountDisabledNotificationSubject: `Onlinebetalningar via {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} har inaktiverats.
Kontrollera dina betalningsinställningar för att aktivera betalningar.`,
	PaymentsEmptyStateDescription: 'Inga betalningar har hittats.',
	PaymentsUnallocated: 'Ofördelade betalningar',
	PayoutDate: 'Utbetalningsdatum',
	PayoutsDisabled: 'Utbetalningar inaktiverade',
	PayoutsEnabled: 'Utbetalningar aktiverade',
	PayoutsStatus: 'Utbetalningsstatus',
	Pediatrician: 'Barnläkare',
	Pen: 'Penna',
	Pending: 'I avvaktan på',
	People: '{rosterSize } personer',
	PeopleCount: 'Personer ({count})',
	PerMonth: '/ Månad',
	PerUser: 'Per användare',
	Permission: 'Tillstånd',
	PermissionRequired: 'Tillstånd krävs',
	Permissions: 'Behörigheter',
	PermissionsClientAndContactDocumentation: 'Klient ',
	PermissionsClientAndContactProfiles: 'Klient ',
	PermissionsEditAccess: 'Redigera åtkomst',
	PermissionsInvoicesAndPayments: 'Fakturor ',
	PermissionsScheduling: 'Schemaläggning',
	PermissionsUnassignClients: 'Ta bort tilldelning av klienter',
	PermissionsUnassignClientsConfirmation: 'Är du säker på att du vill ta bort tilldelningen av dessa klienter?',
	PermissionsValuesAssigned: 'Endast tilldelad',
	PermissionsValuesEverything: 'Allt',
	PermissionsValuesNone: 'Ingen',
	PermissionsValuesOwnCalendar: 'Egen kalender',
	PermissionsViewAccess: 'Visa åtkomst',
	PermissionsWorkspaceSettings: 'Inställningar för arbetsyta',
	Person: '{rosterSize} person',
	PersonalDetails: 'Personliga uppgifter',
	PersonalHealthcareHistoryStoreDescription: 'Svara och lagra din personliga vårdhistorik på ett säkert ställe',
	PersonalTrainer: 'Personlig tränare',
	PersonalTraining: 'Personlig träning',
	PersonalizeWorkspace: 'Personifiera din arbetsyta',
	PersonalizingYourWorkspace: 'Anpassa din arbetsyta',
	Pharmacist: 'Apotekare',
	Pharmacy: 'Apotek',
	PhoneCall: 'Telefonsamtal',
	PhoneNumber: 'Telefonnummer',
	PhoneNumberOptional: 'Telefonnummer (valfritt)',
	PhotoBy: 'Foto av',
	PhysicalAddress: 'Fysisk adress',
	PhysicalTherapist: 'Fysioterapeut',
	PhysicalTherapists: 'Fysioterapeuter',
	PhysicalTherapy: 'Sjukgymnastik',
	Physician: 'Läkare',
	PhysicianAssistant: 'Läkarassistent',
	Physicians: 'Läkare',
	Physiotherapist: 'Fysioterapeut',
	PlaceOfService: 'Plats för tjänst',
	Plan: 'Planera',
	PlanAndReport: 'Plan/Rapport',
	PlanId: 'Plan-ID',
	PlansAndReportsCategoryDescription: 'För behandlingsplanering och sammanfattning av resultat',
	PleaseRefreshThisPageToTryAgain: 'Vänligen uppdatera sidan för att försöka igen.',
	PleaseWait: 'Vänligen vänta...',
	PleaseWaitForHostToJoin: 'Väntar på att värd ska gå med...',
	PleaseWaitForHostToStart: 'Vänta tills värden startar detta möte.',
	PlusAdd: '+ Lägg till',
	PlusOthers: '+{count} andra',
	PlusPlanInclusionFive: 'Delade inkorgar',
	PlusPlanInclusionFour: 'Gruppvideosamtal',
	PlusPlanInclusionHeader: 'Allt i Essential  ',
	PlusPlanInclusionOne: 'Obegränsad AI',
	PlusPlanInclusionSix: 'Anpassat varumärke',
	PlusPlanInclusionThree: 'Gruppschemaläggning',
	PlusPlanInclusionTwo: 'Obegränsad lagring ',
	PlusSubscriptionPlanSubtitle: 'För metoder för att optimera och växa',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Polis',
	PolicyDates: 'Policydatum',
	PolicyHolder: 'Försäkringstagaren',
	PolicyHoldersAddress: 'Försäkringstagarens adress',
	PolicyMemberId: 'Policymedlem-ID',
	PolicyStatus: 'Policystatus',
	Popular: 'Populär',
	PortalAccess: 'Portalåtkomst',
	PortalNoAppointmentsHeading: 'Håll koll på alla kommande och tidigare möten',
	PortalNoDocumentationHeading: 'Skapa och lagra dina dokument på ett säkert sätt',
	PortalNoRelationshipsHeading: 'Samla de som stöttar din resa',
	PosCodeErrorMessage: 'POS-kod krävs',
	PosoNumber: 'PO/SO-nummer',
	PossibleClientDuplicate: 'Möjlig klientduplikat',
	PotentialClientDuplicateTitle: 'Potentiell dubblett av klientposten',
	PotentialClientDuplicateWarning:
		'Denna kundinformation kan redan finnas i din kundlista. Vänligen verifiera och uppdatera den befintliga posten vid behov eller fortsätt att skapa ny klient.',
	PoweredBy: 'Drivs av',
	Practice: 'Öva',
	PracticeDetails: 'Öva detaljer',
	PracticeInfoHeader: 'Företagsinformation',
	PracticeInfoPlaceholder: `Öva namn,
 Nationell leverantörsidentifierare,
 Arbetsgivarens identifikationsnummer`,
	PracticeLocation: 'Det verkar som om din träning är inne',
	PracticeSettingsAvailabilityTab: 'Tillgänglighet',
	PracticeSettingsBillingTab: 'Faktureringsinställningar',
	PracticeSettingsClientSettingsTab: 'Klientinställningar',
	PracticeSettingsGeneralTab: 'Allmän',
	PracticeSettingsOnlineBookingTab: 'Onlinebokning',
	PracticeSettingsServicesTab: 'Tjänster',
	PracticeSettingsTaxRatesTab: 'Skattesatser',
	PracticeTemplate: 'Övningsmall',
	Practitioner: 'Praktiker',
	PreferredLanguage: 'Föredraget språk',
	PreferredName: 'Föredraget namn',
	Prescription: 'Recept',
	PreventionSpecialist: 'Preventionsspecialist',
	Preview: 'Förhandsvisning',
	PreviewAndSend: 'Förhandsgranska och skicka',
	PreviewUnavailable: 'Förhandsgranskning är inte tillgänglig för den här filtypen',
	PreviousNotes: 'Tidigare anteckningar',
	Price: 'Pris',
	PriceError: 'Priset måste vara högre än 0',
	PricePerClient: 'Pris per kund',
	PricePerUser: 'Per användare',
	PricePerUserBilledAnnually: 'Per användare faktureras årligen',
	PricePerUserPerPeriod: '{price} per användare / {isMonthly, select, true {månad} other {år}}',
	PricingGuide: 'Guide till prisplaner',
	PricingPlanPerMonth: '/ månad',
	PricingPlanPerYear: '/ år',
	Primary: 'Primär',
	PrimaryInsurance: 'Primär försäkring',
	PrimaryPolicy: 'Primär försäkring',
	PrimaryTimezone: 'Primär tidszon',
	Print: 'Skriva ut',
	PrintToCms1500: 'Skriv ut till CMS1500',
	PrivatePracticeConsultant: 'Privatpraktiserande konsult',
	Proceed: 'Fortsätt',
	ProcessAtTimeOfBookingDesc: 'Kunder måste betala hela servicepriset för att boka online',
	ProcessAtTimeOfBookingLabel: 'Behandla betalningar vid bokningstillfället',
	Processing: 'Bearbetning',
	ProcessingFee: 'Behandlingsavgift',
	ProcessingFeeToolTip: `Carepatron låter dig ta ut hanteringsavgifterna till dina kunder.
 I vissa jurisdiktioner är det förbjudet att ta ut bearbetningsavgifter av dina kunder. Det är ditt ansvar att följa tillämpliga lagar.`,
	ProcessingRequest: 'Bearbetar begäran...',
	Product: 'Produkt',
	Profession: 'Yrke',
	ProfessionExample: 'Terapeut, nutritionist, tandläkare',
	ProfessionPlaceholder: 'Börja skriva ditt yrke eller välj från listan',
	ProfessionalPlanInclusion1: 'Obegränsad lagring',
	ProfessionalPlanInclusion2: 'Obegränsade uppgifter',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 kundsupport',
	ProfessionalPlanInclusion5: 'SMS-påminnelser',
	ProfessionalPlanInclusionHeader: 'Allt i Starter, plus...',
	Professions: 'Yrken',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: '5 MB filstorleksgräns',
	ProfilePopoverSubTitle: 'Du är inloggad som <strong>{email}</strong>',
	ProfilePopoverTitle: 'Dina arbetsytor',
	PromoCode: 'Kampanjkod',
	PromotionCodeApplied: '{promo} tillämpad',
	ProposeNewDateTime: 'Föreslå ett nytt datum/tid',
	Prosthetist: 'Protetiker',
	Provider: 'Leverantör',
	ProviderBillingPlanExpansionManageButton: 'Hantera plan',
	ProviderCommercialNumber: 'Leverantörens kommersiella nummer',
	ProviderDetails: 'Leverantörsuppgifter',
	ProviderDetailsAddress: 'Adress',
	ProviderDetailsName: 'Namn',
	ProviderDetailsPhoneNumber: 'Telefonnummer',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Tyvärr, den här leverantören har redan ett befintligt faktureringskonto',
	ProviderInfoPlaceholder: `Personalens namn,
 E-postadress,
 Telefonnummer,
 Nationell leverantörsidentifierare,
 Licensnummer`,
	ProviderIsChargedProcessingFee: 'Du betalar handläggningsavgiften',
	ProviderPaymentFormBackButton: 'Tillbaka',
	ProviderPaymentFormBillingAddressCity: 'Stad',
	ProviderPaymentFormBillingAddressCountry: 'Land',
	ProviderPaymentFormBillingAddressLine1: 'Linje 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Postnummer',
	ProviderPaymentFormBillingEmail: 'E-post',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Kreditkortsuppgifter',
	ProviderPaymentFormCardExpiry: 'Upphörande',
	ProviderPaymentFormCardHolderAddressTitle: 'Adress',
	ProviderPaymentFormCardHolderName: 'Kortinnehavarens namn',
	ProviderPaymentFormCardHolderTitle: 'Korthållarens detaljer',
	ProviderPaymentFormCardNumber: 'Kortnummer',
	ProviderPaymentFormPlanTitle: 'Vald plan',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}):',
	ProviderPaymentFormSaveButton: 'Spara prenumeration',
	ProviderPaymentFreePlanDescription:
		'Att välja den kostnadsfria planen kommer att ta bort alla anställdas tillgång till sina kunder hos din leverantör. Din åtkomst kommer dock att finnas kvar och du kommer fortfarande att kunna använda plattformen.',
	ProviderPaymentStepName: 'Recension ',
	ProviderPaymentSuccessSnackbar: 'Stor! Din nya plan har sparats.',
	ProviderPaymentTitle: 'Recension ',
	ProviderPlanNetworkIdentificationNumber: 'Leverantörsplanens nätverksidentifikationsnummer',
	ProviderRemindersSettingsBannerAction: 'Gå till Arbetsflödeshantering',
	ProviderRemindersSettingsBannerDescription:
		'Hitta alla påminnelser under den nya fliken **Arbetsflödeshantering** i **Inställningar**. Den här uppdateringen ger kraftfulla nya funktioner, förbättrad mallhantering och smartare automatiseringsverktyg för att öka din produktivitet. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Din påminnelseupplevelse blir bättre',
	ProviderTaxonomy: 'Leverantörs taxonomi',
	ProviderUPINNumber: 'Leverantörens UPIN-nummer',
	ProviderUsedStoragePercentage: '{providerName} lagring är {usedStoragePercentage}% full!',
	PsychiatricNursePractitioner: 'Psykiatrisk sjuksköterska',
	Psychiatrist: 'Psykiater',
	Psychiatrists: 'Psykiatriker',
	Psychiatry: 'Psykiatri',
	Psychoanalyst: 'Psykoanalytiker',
	Psychologist: 'Psykolog',
	Psychologists: 'Psykologer',
	Psychology: 'Psykologi',
	Psychometrician: 'Psykometriker',
	PsychosocialRehabilitationSpecialist: 'Specialist i psykosocial rehabilitering',
	Psychotheraphy: 'Psykoterapi',
	Psychotherapists: 'Psykoterapeuter',
	Psychotherapy: 'Psykoterapi',
	PublicCallDialogTitle: 'Videosamtal med ',
	PublicCallDialogTitlePlaceholder: 'Videosamtal drivs av Carepatron',
	PublicFormBackToForm: 'Skicka ett annat svar',
	PublicFormConfirmSubmissionHeader: 'Bekräfta inlämning',
	PublicFormNotFoundDescription:
		'Formuläret du letar efter kan ha raderats eller så kan länken vara felaktig. Kontrollera webbadressen och försök igen.',
	PublicFormNotFoundTitle: 'Formulär inte hittat',
	PublicFormSubmissionError: 'Inlämning misslyckades. Försök igen.',
	PublicFormSubmissionSuccess: 'Formuläret har skickats',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} skickade in {noteTitle} offentligt formulär',
	PublicFormSubmittedSubtitle: 'Ditt bidrag har tagits emot.',
	PublicFormSubmittedTitle: 'Tack!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Vi har skickat en bekräftelsekod till din e-post',
	PublicFormsInvalidConfirmationCode: 'Ogiltigt bekräftelse-kod',
	PublicHealthInspector: 'Folkhälsoinspektör',
	PublicTemplates: 'Offentliga mallar',
	Publish: 'Publicera',
	PublishTemplate: 'Publicera mall',
	PublishTemplateFeatureBannerSubheader: 'Mallar utformade för att gynna samhället',
	PublishTemplateHeader: 'Publicera {title}',
	PublishTemplateToCommunity: 'Publicera mall till community',
	PublishToCommunity: 'Publicera till gemenskapen',
	PublishToCommunitySuccessMessage: 'Publicerad till communityn',
	Published: 'Publicerad',
	PublishedBy: 'Publicerad av {name}',
	PublishedNotesAreNotAutosaved: 'Publicerade anteckningar kommer inte att sparas automatiskt',
	PublishedOnCarepatronCommunity: 'Publicerad på Carepatron community',
	Purchase: 'Köpa',
	PushToCalendar: 'Tryck till kalendern',
	Question: 'Fråga',
	QuestionOrTitle: 'Fråga eller titel',
	QuickActions: 'Snabba åtgärder',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Blåbär',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Mossa',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Strålterapeut',
	Radiologist: 'Radiolog',
	Read: 'Läsa',
	ReadOnly: 'Skrivskyddad',
	ReadOnlyAppointment: 'Endast läsbar tid',
	ReadOnlyEventBanner: 'Denna tid är synkroniserad från en skrivskyddad kalender och kan inte redigeras.',
	ReaderMaxDepthHasBeenExceededCode: 'Anteckningen är för kapslad. Försök att ta bort några föremål.',
	ReadyForMapping: 'Redo för mappning',
	RealEstateAgent: 'Fastighetsmäklare',
	RearrangeClientFields: 'Ordna om klientfält i klientinställningar',
	Reason: 'Resonera',
	ReasonForChange: 'Anledning till förändring',
	RecentAppointments: 'Senaste möten',
	RecentServices: 'Senaste tjänster',
	RecentTemplates: 'Senaste mallar',
	RecentlyUsed: 'Nyligen använd',
	Recommended: 'Rekommenderad',
	RecommendedTemplates: 'Rekommenderade mallar',
	Recording: 'Inspelning',
	RecordingEnded: 'Inspelningen avslutades',
	RecordingInProgress: 'Inspelning pågår',
	RecordingMicrophoneAccessErrorMessage:
		'Tillåt mikrofonåtkomst i din webbläsare och uppdatera för att börja spela in.',
	RecurrenceCount: ', {count, plural, one {en gång} other {# gånger}}',
	RecurrenceDaily: '{count, plural, one {Dagligen} other {Dagar}}',
	RecurrenceEndAfter: 'Efter',
	RecurrenceEndNever: 'Aldrig',
	RecurrenceEndOn: 'På',
	RecurrenceEvery: 'Varje {description}',
	RecurrenceMonthly: '{count, plural, one {Månadsvis} other {Månader}}',
	RecurrenceOn: 'på {description}',
	RecurrenceOnAllDays: 'på alla dagar',
	RecurrenceUntil: 'tills {description}',
	RecurrenceWeekly: '{count, plural, one {Vecka} other {Veckor}}',
	RecurrenceYearly: '{count, plural, one {Årsvis} other {År}}',
	Recurring: 'Återkommande',
	RecurringAppointment: 'Återkommande möte',
	RecurringAppointmentsLimitedBannerText:
		'Alla återkommande möten visas inte. Minska datumintervallet för att se alla återkommande möten för perioden.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# evenemang} other {# evenemang}}</b> kommer att skapas vid följande datum',
	Redo: 'Göra om',
	ReferFriends: 'Hänvisa vänner',
	Reference: 'Hänvisning',
	ReferralCreditedNotificationSubject: 'Din referenskredit på {currency} {amount} har lagts till',
	ReferralEmailDefaultBody: `Tack vare {name} har du fått en GRATIS 3 månaders uppgradering till Carepatron. Gå med i vårt community med över 3 miljoner vårdpersonal, byggt för ett nytt sätt att arbeta!
Tack,
Carepatron-teamet`,
	ReferralEmailDefaultSubject: 'Du har blivit inbjuden att gå med i Carepatron',
	ReferralHasNotSignedUpDescription: 'Din vän har inte registrerat sig ännu',
	ReferralHasSignedUpDescription: 'Din vän har registrerat sig.',
	ReferralInformation: 'Remissinformation',
	ReferralJoinedNotificationSubject: '{actorProfileName} har anslutit sig till Carepatron',
	ReferralListErrorDescription: 'Referenslistan kunde inte laddas.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {månad} other {månader}}</b> aktiv',
	ReferralRewardBanner: 'Registrera dig och hämta din remissbelöning!',
	Referrals: 'Remisser',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} månad {percentOff, select, 100 {gratis betald} other {{percentOff}% rabatt}} {type, select, SubscriptionUpgrade {uppgradering} other {}}',
	ReferredUserBenefitTitle: 'De får!',
	Referrer: 'Referent',
	ReferringProvider: 'Remitterande leverantör',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} kredit när <mark>3 vänner</mark> aktiverar.',
	ReferringUserBenefitTitle: 'Du får!',
	RefreshPage: 'Uppdatera sidan',
	Refund: 'Återbetalning',
	RefundAcknowledgement: 'Jag har återbetalat {clientName} utanför Carepatron',
	RefundAcknowledgementValidationMessage: 'Vänligen bekräfta att du har återbetalat detta belopp',
	RefundAmount: 'Återbetalningsbelopp',
	RefundContent:
		'Det tar 7–10 dagar innan återbetalningar visas på din kunds konto. Betalningsavgifter återbetalas inte, men det tillkommer inga extra avgifter för återbetalningar. Återbetalningar kan inte annulleras, och vissa kan behöva granskas innan bearbetning.',
	RefundCouldNotBeProcessed: 'Återbetalningen kunde inte behandlas',
	RefundError:
		'Denna återbetalning kan för närvarande inte behandlas automatiskt. Kontakta Carepatrons support för att begära återbetalning av denna betalning.',
	RefundExceedTotalValidationError: 'Beloppet får inte överstiga det totala betalda beloppet',
	RefundFailed: 'Återbetalning misslyckades',
	RefundFailedTooltip:
		'Återbetalningen av denna betalning misslyckades tidigare och kan inte göras om. Kontakta supporten.',
	RefundNonStripePaymentContent:
		'Denna betalning gjordes med en metod utanför Carepatron (t.ex. kontanter, internetbank). Utfärdande av en återbetalning inom Carepatron kommer inte att returnera några medel till kunden.',
	RefundReasonDescription:
		'Att lägga till ett skäl för återbetalning kan hjälpa dig när du granskar dina kunders transaktioner',
	Refunded: 'Återbetalas',
	Refunds: 'Återbetalningar',
	RefundsTableEmptyState: 'Inga återbetalningar hittades',
	Regenerate: 'Generera om',
	RegisterButton: 'Register',
	RegisterEmail: 'E-post',
	RegisterFirstName: 'Förnamn',
	RegisterLastName: 'Efternamn',
	RegisterPassword: 'Lösenord',
	RegisteredNurse: 'legitimerad sjuksköterska',
	RehabilitationCounselor: 'Rehabiliteringsrådgivare',
	RejectAppointmentFormTitle: 'Kan du inte göra det? Låt oss veta varför och föreslå en ny tid.',
	Rejected: 'Avvisad',
	Relationship: 'Relation',
	RelationshipDetails: 'Förhållandedetaljer',
	RelationshipEmptyStateTitle: 'Håll kontakten med dem som stöttar din klient',
	RelationshipPageAccessTypeColumnName: 'Profilåtkomst',
	RelationshipSavedSuccessSnackbar: 'Förhållandet har sparats!',
	RelationshipSelectorFamilyAdmin: 'Familj',
	RelationshipSelectorFamilyMember: 'Familjemedlem',
	RelationshipSelectorProviderAdmin: 'Leverantörsadministratör',
	RelationshipSelectorProviderStaff: 'Leverantörspersonal',
	RelationshipSelectorSupportNetworkPrimary: 'Vän',
	RelationshipSelectorSupportNetworkSecondary: 'Supportnätverk',
	RelationshipStatus: 'Relationsstatus',
	RelationshipType: 'Relationstyp',
	RelationshipTypeClientOwner: 'Klient',
	RelationshipTypeFamilyAdmin: 'Relationer',
	RelationshipTypeFamilyMember: 'Familj',
	RelationshipTypeFriendOrSupport: 'Vän eller supportnätverk',
	RelationshipTypeProviderAdmin: 'Leverantörsadministratör',
	RelationshipTypeProviderStaff: 'Personal',
	RelationshipTypeSelectorPlaceholder: 'Sök relationstyper',
	Relationships: 'Relationer',
	Remaining: 'återstående',
	RemainingTime: '{time} kvar',
	Reminder: 'Påminnelse',
	ReminderColor: 'Påminnelse färg',
	ReminderDetails: 'Påminnelsedetaljer',
	ReminderEditDisclaimer: 'Ändringar kommer endast att återspeglas i nya möten',
	ReminderSettings: 'Inställningar för mötespåminnelser',
	Reminders: 'Påminnelser',
	Remove: 'Ta bort',
	RemoveAccess: 'Ta bort åtkomst',
	RemoveAllGuidesBtn: 'Ta bort alla guider',
	RemoveAllGuidesPopoverBody:
		'När du är klar med introduktionsguiderna använder du bara knappen för att ta bort guider på varje panel.',
	RemoveAllGuidesPopoverTitle: 'Behöver du inte längre dina onboardingguider?',
	RemoveAsDefault: 'Ta bort som standard',
	RemoveAsIntake: 'Ta bort som intag',
	RemoveCol: 'Ta bort kolumn',
	RemoveColor: 'Ta bort färg',
	RemoveField: 'Ta bort fält',
	RemoveFromCall: 'Ta bort från samtalet',
	RemoveFromCallDescription:
		'Är du säker på att du vill ta bort <mark>{attendeeName}</mark> från det här videosamtalet?',
	RemoveFromCollection: 'Ta bort från samlingen',
	RemoveFromCommunity: 'Ta bort från gruppen',
	RemoveFromFolder: 'Ta bort från mapp',
	RemoveFromFolderConfirmationDescription:
		'Är du säker på att du vill ta bort den här mallen från den här mappen? Den här åtgärden går inte att ångra, men du kan välja att flytta tillbaka den senare.',
	RemoveFromIntakeDefault: 'Ta bort från standardintag',
	RemoveGuides: 'Ta bort guider',
	RemoveMfaConfirmationDescription:
		'Om du tar bort Multi-Factor Authentication (MFA) minskar säkerheten för ditt konto. Vill du fortsätta?',
	RemoveMfaConfirmationTitle: 'Ta bort MFA?',
	RemovePaymentMethodDescription: `Detta tar bort all åtkomst och framtida användning av denna betalningsmetod.
 Den här åtgärden kan inte ångras.`,
	RemoveRow: 'Ta bort raden',
	RemoveTable: 'Ta bort bordet',
	RemoveTemplateAsDefaultIntakeSuccess: 'Framgångsrikt borttagen {templateTitle} som standardmall för intag',
	RemoveTemplateFromCommunity: 'Ta bort mallen från gruppen',
	RemoveTemplateFromFolder: '{templateTitle} borttagen från {folderTitle}',
	Rename: 'Döpa om',
	RenderingProvider: 'Återgivningsleverantör',
	Reopen: 'Öppna igen',
	ReorderServiceGroupFailure: 'Det gick inte att beställa om samlingen',
	ReorderServiceGroupSuccess: 'Samlingen har omordnats',
	ReorderServicesFailure: 'Det gick inte att ombeställa tjänster',
	ReorderServicesSuccess: 'Ombeställda tjänster',
	ReorderYourServiceList: 'Ordna om din tjänstelista',
	ReorderYourServiceListDescription:
		'Hur du organiserar dina tjänster och samlingar kommer att återspeglas på din bokningssida online så att alla dina kunder kan se!',
	RepeatEvery: 'Upprepa varje',
	RepeatOn: 'Upprepa på',
	Repeating: 'Upprepa',
	Repeats: 'Upprepas',
	RepeatsEvery: 'Upprepas var',
	Rephrase: 'Omformulera',
	Replace: 'Ersätta',
	ReplaceBackground: 'Byt ut bakgrund',
	ReplacementOfPriorClaim: 'Ersättning av tidigare anspråk',
	Report: 'Rapportera',
	Reprocess: 'Upparbeta',
	RepublishTemplateToCommunity: 'Publicera mall till community',
	RequestANewVerificationLink: 'Begär en ny verifieringslänk',
	RequestCoverageReport: 'Begär täckningsrapport',
	RequestingDevicePermissions: 'Begär enhetsbehörigheter...',
	RequirePaymentMethodDesc: 'Kunder måste ange sina kreditkortsuppgifter för att boka online',
	RequirePaymentMethodLabel: 'Kräv kreditkortsuppgifter',
	Required: 'nödvändig',
	RequiredField: 'Nödvändig',
	RequiredUrl: 'URL krävs.',
	Reschedule: 'Boka om',
	RescheduleBookingLinkModalDescription: 'Din kund kan ändra sitt mötesdatum och tid med den här länken.',
	RescheduleBookingLinkModalTitle: 'Boka om bokningslänk',
	RescheduleLink: 'Boka om länken',
	Resend: 'Skicka igen',
	ResendConfirmationCode: 'Skicka bekräftelsekoden igen',
	ResendConfirmationCodeDescription: 'Vänligen ange din e-postadress så skickar vi en annan bekräftelsekod till dig',
	ResendConfirmationCodeSuccess: 'Bekräftelsekoden har skickats igen, kontrollera din inkorg',
	ResendNewEmailVerificationSuccess: 'Ny verifieringslänk har skickats till {email}',
	ResendVerificationEmail: 'Skicka verifieringse-post igen',
	Reset: 'Återställa',
	Resources: 'Resurser',
	RespiratoryTherapist: 'Andningsterapeut',
	RespondToHistoricAppointmentError: 'Detta är ett historiskt möte, kontakta din läkare om du har frågor.',
	Responder: 'Svara',
	RestorableItemModalDescription:
		'Är du säker på att du vill ta bort {context}?{canRestore, select, true { Du kan återställa den senare.} other {}}',
	RestorableItemModalTitle: 'Radera {type}',
	Restore: 'Återställa',
	RestoreAll: 'Återställ alla',
	Restricted: 'Begränsad',
	ResubmissionCodeReferenceNumber: 'Resubmissionskod och referensnummer',
	Resubmit: 'Skicka in igen',
	Resume: 'Resume',
	Retry: 'Försöka igen',
	RetryingConnectionAttempt: 'Försöker återansluta... (Försök {retryCount} av {maxRetries})',
	ReturnToForm: 'Återgå till formuläret',
	RevertClaimStatus: 'Återställ anspråksstatus',
	RevertClaimStatusDescriptionBody:
		'Detta krav har länkade betalningar, och att ändra statusen kan påverka spårning eller bearbetning av betalningar, vilket kan kräva manuell avstämning.',
	RevertClaimStatusDescriptionTitle: 'Är du säker på att du vill återgå till {status}?',
	RevertClaimStatusError: 'Misslyckades med att återställa anspråkets status',
	RevertToDraft: 'Återgå till utkast',
	Review: 'Recension',
	ReviewsFirstQuote: 'Utnämningar var som helst, när som helst',
	ReviewsSecondJobTitle: 'Lifehouse klinik',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Jag älskar också carepatron-appen. Hjälper mig att hålla reda på mina kunder och arbeta när jag är på språng.',
	ReviewsThirdJobTitle: 'Manila Bay Clinic',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'Den enkla navigeringen och det vackra användargränssnittet ger mig ett leende varje dag.',
	RightAlign: 'Högerjustera',
	Role: 'Roll',
	Roster: 'Deltagare',
	RunInBackground: 'Kör i bakgrunden',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Du lämnar Carepatron',
	SafetyRedirectSubtext: 'Om du litar på den här länken, välj den för att fortsätta',
	SalesRepresentative: 'Försäljningsrepresentant',
	SalesTax: 'Omsättningsskatt',
	SalesTaxHelp: 'Inkluderar moms på genererade fakturor',
	SalesTaxIncluded: 'Ja',
	SalesTaxNotIncluded: 'Inga',
	SaoPaulo: 'São Paulo',
	Saturday: 'lördag',
	Save: 'Spara',
	SaveAndClose: 'Spara ',
	SaveAndExit: 'Spara ',
	SaveAndLock: 'Spara och lås',
	SaveAsDraft: 'Spara som utkast',
	SaveCardForFuturePayments: 'Spara kort för framtida betalningar',
	SaveChanges: 'Spara ändringar',
	SaveCollection: 'Spara samling',
	SaveField: 'Spara fält',
	SavePaymentMethod: 'Spara betalningsmetod',
	SavePaymentMethodDescription: 'Du kommer inte att debiteras förrän ditt första möte.',
	SavePaymentMethodSetupError: 'Ett oväntat fel inträffade och vi kunde inte konfigurera betalningar just nu.',
	SavePaymentMethodSetupInvoiceLater: 'Betalningar kan ställas in och sparas när du betalar din första faktura.',
	SaveSection: 'Spara avsnitt',
	SaveService: 'Skapa ny tjänst',
	SaveTemplate: 'Spara mall',
	Saved: 'Sparad',
	SavedCards: 'Sparade kort',
	SavedPaymentMethods: 'Sparad',
	Saving: 'Sparande...',
	ScheduleAppointmentsAndOnlineServices: 'Boka möten och onlinetjänster',
	ScheduleName: 'Schemanamn',
	ScheduleNew: 'Schema nytt',
	ScheduleSend: 'Schema skicka',
	ScheduleSendAlertInfo: 'Konversationer i schemalagda kommer att skickas vid schemalagd tid.',
	ScheduleSendByName: '**Schemalägg skickning** • {time} av {displayName}',
	ScheduleSetupCall: 'Schemalägg ett uppstartssamtal',
	Scheduled: 'Schemalagt',
	SchedulingSend: 'Schemalägg skicka',
	School: 'Skola',
	ScrollToTop: 'Bläddra till toppen',
	Search: 'Söka',
	SearchAndConvertToLanguage: 'Sök och konvertera till språk',
	SearchBasicBlocks: 'Sök grundläggande block',
	SearchByName: 'Sök på namn',
	SearchClaims: 'Sök anspråk',
	SearchClientFields: 'Sök i klientfält',
	SearchClients: 'Sök på klientnamn, klient-ID eller telefonnummer',
	SearchCommandNotFound: 'Inga resultat hittades för "{searchTerm}"',
	SearchContacts: 'Kund eller kontakt',
	SearchContactsPlaceholder: 'Sök kontakter',
	SearchConversations: 'Sök konversationer',
	SearchInputPlaceholder: 'Sök i alla resurser',
	SearchInvoiceNumber: 'Sök efter fakturanummer',
	SearchInvoices: 'Sök efter fakturor',
	SearchMultipleContacts: 'Kunder eller kontakter',
	SearchMultipleContactsOptional: 'Kunder eller kontakter (valfritt)',
	SearchOrCreateATag: 'Sök eller skapa en tagg',
	SearchPayments: 'Sök efter betalningar',
	SearchPrepopulatedData: 'Sök i förväg ifyllda datafält',
	SearchRelationships: 'Sök relationer',
	SearchRemindersAndWorkflows: 'Sök påminnelser och arbetsflöden',
	SearchServices: 'Sök tjänster',
	SearchTags: 'Sök taggar',
	SearchTeamMembers: 'Sök gruppmedlemmar',
	SearchTemplatePlaceholder: 'Sök {templateCount}+ resurser',
	SearchTimezone: 'Sök tidszon...',
	SearchTrashItems: 'Sök föremål',
	SearchUnsplashPlaceholder: 'Sök gratis högupplösta bilder från Unsplash',
	Secondary: 'Sekundär',
	SecondaryInsurance: 'Sekundärförsäkring',
	SecondaryPolicy: 'Sekundärförsäkring',
	SecondaryTimezone: 'Sekundär tidszon',
	Secondly: 'För det andra',
	Section: 'Avsnitt',
	SectionCannotBeEmpty: 'En sektion måste ha minst en rad',
	SectionFormSecondaryText: 'Avsnittstitel och beskrivning',
	SectionName: 'Sektionens namn',
	Sections: 'Avsnitt',
	SeeLess: 'Se mindre',
	SeeLessUpcomingAppointments: 'Se färre kommande möten',
	SeeMore: 'Se mer',
	SeeMoreUpcomingAppointments: 'Se fler kommande möten',
	SeeTemplateLibrary: 'Se mallbibliotek',
	Seen: 'Sett',
	SeenByName: '<strong>Sett</strong> • {time} av {displayName}',
	SelectAll: 'Välj alla',
	SelectAssignees: 'Välj tilldelade',
	SelectAttendees: 'Välj deltagare',
	SelectCollection: 'Välj Samling',
	SelectCorrespondingAttributes: 'Välj motsvarande attribut',
	SelectPayers: 'Välj betalare',
	SelectProfile: 'Välj profil',
	SelectServices: 'Välj tjänster',
	SelectTags: 'Välj Taggar',
	SelectTeamOrCommunity: 'Välj Team eller Community',
	SelectTemplate: 'Välj Mall',
	SelectType: 'Välj typ',
	Selected: 'Vald',
	SelfPay: 'Egen betalning',
	Send: 'Skicka',
	SendAndClose: 'Skicka ',
	SendAndStopIgnore: 'Skicka och sluta ignorera',
	SendEmail: 'Skicka e-post',
	SendIntake: 'Skicka intag',
	SendIntakeAndForms: 'Skicka intag ',
	SendMeACopy: 'Skicka mig en kopia',
	SendNotificationEmailWarning:
		'Några deltagare har ingen e-postadress och kommer inte att få automatiska meddelanden och påminnelser.',
	SendNotificationLabel: 'Välj deltagare att meddela via e-post',
	SendOnlinePayment: 'Skicka onlinebetalning',
	SendOnlinePaymentTooltipTitleAdmin: 'Lägg till dina föredragna utbetalningsinställningar',
	SendOnlinePaymentTooltipTitleStaff: 'Be ägaren till leverantören att ställa in onlinebetalningar.',
	SendPaymentLink: 'Skicka betalningslänk',
	SendReaction: 'Skicka en reaktion',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Skicka verifieringsmail',
	SendingFailed: 'Det gick inte att skicka',
	Sent: 'Skickat',
	SentByName: '<strong>Skickad</strong> • {time} av {displayName}',
	Seoul: 'Seoul',
	SeparateDuplicateClientsDescription:
		'De valda klientposterna kommer att förbli separata från resten om du inte väljer att slå samman dem',
	Service: 'Service',
	'Service/s': 'Tjänst/er',
	ServiceAdjustment: 'Servicejustering',
	ServiceAllowNewClientsIndicator: 'Tillåt nya kunder',
	ServiceAlreadyExistsInCollection: 'Tjänsten finns redan i samlingen',
	ServiceBookableOnlineIndicator: 'Bokas online',
	ServiceCode: 'Koda',
	ServiceCodeErrorMessage: 'En servicekod krävs',
	ServiceCodeSelectorPlaceholder: 'Lägg till en servicekod',
	ServiceColour: 'Servicefärg',
	ServiceCoverageDescription: 'Välj de kvalificerade tjänsterna och betala för denna försäkring.',
	ServiceCoverageGoToServices: 'Gå till tjänster',
	ServiceCoverageNoServicesDescription:
		'Anpassa tjänstens co-pay belopp för att åsidosätta standard policy co-pay. Inaktivera täckning för att förhindra att tjänster görs anspråk på försäkringen.',
	ServiceCoverageNoServicesLabel: 'Inga tjänster har hittats.',
	ServiceCoverageTitle: 'Servicetäckning',
	ServiceDate: 'Datum för delgivning',
	ServiceDetails: 'Servicedetaljer',
	ServiceDuration: 'Varaktighet',
	ServiceEmptyState: 'Det finns inga tjänster ännu',
	ServiceErrorMessage: 'Service krävs',
	ServiceFacility: 'Serviceanläggning',
	ServiceName: 'Tjänstens namn',
	ServiceRate: 'Hastighet',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superfaktura {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} för {serviceReceiptNumber, select, undefined {användare} other {{clientName}}} kräver ytterligare information',
	ServiceSalesTax: 'Omsättningsskatt',
	ServiceType: 'Service',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Tryck på ladda om för att uppdatera och få de senaste Carepatron-uppdateringarna.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Ladda om',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Du använder en äldre version',
	ServiceWorkerForceUIUpdateDialogTitle: 'Välkommen tillbaka!',
	Services: 'Tjänster',
	ServicesAndAvailability: 'Tjänster ',
	ServicesAndDiagnosisCodesHeader: 'Lägg till tjänster och diagnoskoder',
	ServicesCount: '{count,plural,=0{Tjänster}one{Tjänst}other{Tjänster}}',
	ServicesPlaceholder: 'Tjänster',
	ServicesProvidedBy: 'Tjänst/er tillhandahållna av',
	SetAPhysicalAddress: 'Ange en fysisk adress',
	SetAVirtualLocation: 'Ställ in en virtuell plats',
	SetAsDefault: 'Ställ in som standard',
	SetAsIntake: 'Ställ in som intag',
	SetAsIntakeDefault: 'Ställ in som standardintag',
	SetAvailability: 'Ställ in tillgänglighet',
	SetTemplateAsDefaultIntakeSuccess: 'Framgångsrikt angav {templateTitle} som standardmall för intag',
	SetUpMfaButton: 'Ställ in MFA',
	SetYourLocation: 'Ställ in din<mark> plats</mark>',
	SetYourLocationDescription: 'Jag har ingen företagsadress <span>(endast online- och mobiltjänster)</span>',
	SettingUpPayers: 'Konfigurera betalare',
	Settings: 'Inställningar',
	SettingsNewUserPasswordDescription:
		'När du har registrerat dig skickar vi dig en bekräftelsekod som du kan använda för att bekräfta ditt konto',
	SettingsNewUserPasswordTitle: 'Anmäl dig till Carepatron',
	SettingsTabAutomation: 'Automatisering',
	SettingsTabBillingDetails: 'Faktureringsinformation',
	SettingsTabConnectedApps: 'Anslutna appar',
	SettingsTabCustomFields: 'Anpassade fält',
	SettingsTabDetails: 'Detaljer',
	SettingsTabInvoices: 'Fakturor',
	SettingsTabLocations: 'Platser',
	SettingsTabNotifications: 'Aviseringar',
	SettingsTabOnlineBooking: 'Onlinebokning',
	SettingsTabPayers: 'Betalare',
	SettingsTabReminders: 'Påminnelser',
	SettingsTabServices: 'Tjänster',
	SettingsTabServicesAndAvailability: 'Tjänster och tillgänglighet',
	SettingsTabSubscriptions: 'Prenumerationer',
	SettingsTabWorkflowAutomations: 'Automatiseringar',
	SettingsTabWorkflowReminders: 'Grundläggande påminnelser',
	SettingsTabWorkflowTemplates: 'Mallar',
	Setup: 'Inrätta',
	SetupGuide: 'Installationsguide',
	SetupGuideAddServicesActionLabel: 'Start',
	SetupGuideAddServicesSubtitle: '4 steg • 2 min',
	SetupGuideAddServicesTitle: 'Lägg till dina tjänster',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Start',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 steg • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Aktivera onlinebetalningar',
	SetupGuideImportClientsActionLabel: 'Start',
	SetupGuideImportClientsSubtitle: '4 steg • 3 min',
	SetupGuideImportClientsTitle: 'Importera dina klienter',
	SetupGuideImportTemplatesActionLabel: 'Start',
	SetupGuideImportTemplatesSubtitle: '2 steg • 1 min',
	SetupGuideImportTemplatesTitle: 'Importera dina mallar',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Start',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 steg • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personifiera din arbetsyta',
	SetupGuideSetLocationActionLabel: 'Start',
	SetupGuideSetLocationSubtitle: '4 steg • 1 min',
	SetupGuideSetLocationTitle: 'Ställ in din plats',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Bjud in team',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'Bjud in ditt team för att kommunicera och hantera uppgifter utan ansträngning.',
	SetupGuideSuggestedAddTeamMembersTag: 'Inställningar',
	SetupGuideSuggestedAddTeamMembersTitle: 'Lägg till teammedlemmar',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Anpassa',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Känn dig professionell med din unika logotyp och varumärkesfärger.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Anpassa varumärke',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Hämta',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Få åtkomst till din arbetsyta var som helst, när som helst och på vilken enhet som helst.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Inställningar',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Ladda ner appen',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Ställ in tillgänglighet',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Förebygg dubbelbokningar genom att ställa in din tillgänglighet.',
	SetupGuideSuggestedEditAvailabilityTag: 'Schemaläggning',
	SetupGuideSuggestedEditAvailabilityTitle: 'Redigera tillgänglighet',
	SetupGuideSuggestedImportClientsActionLabel: 'Importera',
	SetupGuideSuggestedImportClientsSubtitle: 'Ladda upp dina befintliga kunddata direkt med ett klick.',
	SetupGuideSuggestedImportClientsTag: 'Inställningar',
	SetupGuideSuggestedImportClientsTitle: 'Importera klienter',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Redigera påminnelser',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'Minska uteblivna möten med automatiska påminnelser om möten.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Personliga påminnelser',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Starta samtal',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Värd en samtalsomgång och skapa kontakt med kunder med hjälp av våra AI-drivna videverktyg.',
	SetupGuideSuggestedStartVideoCallTag: 'Telehälsa',
	SetupGuideSuggestedStartVideoCallTitle: 'Starta videosamtal',
	SetupGuideSuggestedTryActionsTitle: 'Saker att prova 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Prova AI-hjälp',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Få omedelbara svar på alla dina arbetsrelaterade frågor.',
	SetupGuideSuggestedUseAIAssistantTag: 'Ny',
	SetupGuideSuggestedUseAIAssistantTitle: 'Använd AI-assistent',
	SetupGuideSyncCalendarActionLabel: 'Start',
	SetupGuideSyncCalendarSubtitle: '1 steg • mindre än 1 min',
	SetupGuideSyncCalendarTitle: 'Synkronisera din kalender',
	SetupGuideVerifyEmailLabel: 'Verifiera',
	SetupGuideVerifyEmailSubtitle: '2 steg • 2 min',
	SetupOnlineStripePayments: 'Använd Stripe för onlinebetalningar',
	SetupPayments: 'Ställ in betalningar',
	Sex: 'Sex',
	SexSelectorPlaceholder: 'Man / Kvinna / Föredrar att inte säga',
	Share: 'Dela',
	ShareBookingLink: 'Dela bokningslänk',
	ShareNoteDefaultMessage: `Hej{name} har delat "{documentName}" med dig.

Tack,
{practiceName}`,
	ShareNoteMessage: `Hej
{name} har delat "{documentName}" {isResponder, select, true {med några frågor som du behöver fylla i.} other {med dig.}}

Tack,
{practiceName}`,
	ShareNoteTitle: 'Dela ‘{noteTitle}’',
	ShareNotesWithClients: 'Dela med kunder eller kontakter',
	ShareScreen: 'Dela skärm',
	ShareScreenNotSupported: 'Din enhet/webbläsare stöder inte funktionen för dela skärm',
	ShareScreenWithId: 'Skärm {screenId}',
	ShareTemplateAsPublicFormModalDescription: 'Låt andra se den här mallen och skicka in den som ett formulär.',
	ShareTemplateAsPublicFormModalTitle: 'Dela länk för ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Offentlig formulärkonfiguration uppdaterades',
	ShareTemplateAsPublicFormSectionCustomization: 'Anpassning',
	ShareTemplateAsPublicFormShowPoweredBy: 'Visa "Powered by Carepatron" på mitt formulär',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Visa/dölj “Powered by Carepatron” på mitt formulär',
	ShareTemplateAsPublicFormTrigger: 'Dela',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Använd arbetsytevarumärke',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Visa/dölj arbetsytevarumärke',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Skickar kod för befintliga och icke-befintliga klienter',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Signaturer kräver alltid att e-postadressen verifieras',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Sänder endast kod för befintliga kunder',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Skickar aldrig kod',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'Att välja "Aldrig" kan tillåta obekräftade användare att skriva över klientdata om de använder en befintlig klients e-postadress.',
	ShareWithCommunity: 'Dela med gemenskapen',
	ShareYourReferralLink: 'Dela din hänvisningslänk',
	ShareYourScreen: 'Dela din skärm',
	SheHer: 'Hon/Henne',
	ShortTextAnswer: 'Kort textsvar',
	ShortTextFormPrimaryText: 'Kort text',
	ShortTextFormSecondaryText: 'Svar på mindre än 300 tecken',
	Show: 'Visa',
	ShowColumn: 'Visa kolumn',
	ShowColumnButton: 'Visa kolumn {value} -knapp',
	ShowColumns: 'Visa kolumner',
	ShowColumnsMenu: 'Visa kolumnmenyn',
	ShowDateDurationDescription: 'till exempel. 29 år gammal',
	ShowDateDurationLabel: 'Visa datumets varaktighet',
	ShowDetails: 'Visa detaljer',
	ShowField: 'Visa fält',
	ShowFullAddress: 'Visa adress',
	ShowHideFields: 'Visa / Dölj fält',
	ShowIcons: 'Visa ikoner',
	ShowLess: 'Visa mindre',
	ShowMeetingTimers: 'Visa mötestimer',
	ShowMenu: 'Visa meny',
	ShowMergeSummarySidebar: 'Visa sammanfattning av sammanslagning',
	ShowMore: 'Visa mer',
	ShowOnTranscript: 'Visa på utskrift',
	ShowReactions: 'Visa reaktioner',
	ShowSection: 'Visa avsnitt',
	ShowServiceCode: 'Visa servicekod',
	ShowServiceDescription: 'Visa beskrivning på tjänstebokningar',
	ShowServiceDescriptionDesc: 'Kunder kan se tjänstebeskrivningar vid bokning',
	ShowServiceGroups: 'Visa samlingar',
	ShowServiceGroupsDesc: 'Kunder kommer att se tjänster grupperade efter samling vid bokning',
	ShowSpeakers: 'Visa högtalare',
	ShowTax: 'Visa skatt',
	ShowTimestamp: 'Visa tidsstämpel',
	ShowUnits: 'Visa enheter',
	ShowWeekends: 'Visa helger',
	ShowYourView: 'Visa din vy',
	SignInWithApple: 'Logga in med Apple',
	SignInWithGoogle: 'Logga in med Google',
	SignInWithMicrosoft: 'Logga in med Microsoft',
	SignUpTitleReferralDefault: '<mark>Anmäl dig</mark> och hämta din hänvisningsbelöning',
	SignUpTitleReferralUpgrade:
		'Starta din {durationInMonths} månaders <mark>{percentOff, select, 100 {gratis} other {{percentOff}% rabatt}} uppgradering</mark>',
	SignatureCaptureError: 'Det går inte att fånga signaturen. Försök igen.',
	SignatureFormPrimaryText: 'Signatur',
	SignatureFormSecondaryText: 'Skaffa en digital signatur',
	SignatureInfoTooltip: 'Denna visuella representation är inte en giltig elektronisk signatur.',
	SignaturePlaceholder: 'Rita din signatur här',
	SignedBy: 'Signerad av',
	Signup: 'Anmäl dig',
	SignupAgreements: 'Jag godkänner {termsOfUse} och {privacyStatement} för mitt konto.',
	SignupBAA: 'Affärspartneravtal',
	SignupBusinessAgreements:
		'På uppdrag av mig själv och företaget godkänner jag {businessAssociateAgreement}, {termsOfUse} och {privacyStatement} för mitt konto.',
	SignupInvitationForYou: 'Du har blivit inbjuden att använda Carepatron.',
	SignupPageProviderWarning:
		'Om din administratör redan har skapat ett konto måste du be dem att bjuda in dig till den leverantören. Använd inte detta registreringsformulär. För mer information se',
	SignupPageProviderWarningLink: 'denna länk.',
	SignupPrivacy: 'Sekretesspolicy',
	SignupProfession: 'Vilket yrke beskriver dig bäst?',
	SignupSubtitle:
		'Carepatrons mjukvara för övningshantering är gjord för ensamutövare och team. Sluta betala för höga avgifter och bli en del av revolutionen.',
	SignupSuccessDescription:
		'Bekräfta din e-postadress för att starta onboarding. Om du inte får det direkt, kolla din skräppostmapp.',
	SignupSuccessTitle: 'Kontrollera din e-post',
	SignupTermsOfUse: 'Användarvillkor',
	SignupTitleClient: '<mark>Hantera din hälsa</mark> från ett ställe',
	SignupTitleLast: 'och allt arbete du gör! – Det är gratis',
	SignupTitleOne: '<mark>Drar dig</mark> , ',
	SignupTitleThree: '<mark>Styrka dina kunder</mark> , ',
	SignupTitleTwo: '<mark>Styr ditt team</mark> , ',
	Simple: 'Enkel',
	SimplifyBillToDetails: 'Förenkla räkningen till detaljer',
	SimplifyBillToHelperText: 'Endast den första raden används när den matchar klienten',
	Singapore: 'Singapore',
	Single: 'Enda',
	SingleChoiceFormPrimaryText: 'Enstaka val',
	SingleChoiceFormSecondaryText: 'Välj bara ett alternativ',
	Sister: 'Syster',
	SisterInLaw: 'Svägerska',
	Skip: 'Hoppa',
	SkipLogin: 'Hoppa över inloggning',
	SlightBlur: 'Gör din bakgrund något suddig',
	Small: 'Små',
	SmartChips: 'Smartchips',
	SmartDataChips: 'Smarta datachips',
	SmartReply: 'Smart svar',
	SmartSuggestNewClient: '**Smart Suggest** skapa {name} som en ny klient',
	SmartSuggestedFieldDescription: 'Detta fält är ett smart förslag',
	SocialSecurityNumber: 'Personnummer',
	SocialWork: 'Socialt arbete',
	SocialWorker: 'Socialarbetare',
	SoftwareDeveloper: 'Mjukvaruutvecklare',
	Solo: 'Solo',
	Someone: 'Någon',
	Son: 'Son',
	SortBy: 'Sortera efter',
	SouthAmerica: 'Sydamerika',
	Speaker: 'Högtalare',
	SpeakerSource: 'Högtalarkälla',
	Speakers: 'Högtalare',
	SpecifyPaymentMethod: 'Ange betalningsmetod',
	SpeechLanguagePathology: 'Tal-språk patologi',
	SpeechTherapist: 'Logoped',
	SpeechTherapists: 'Logopeder',
	SpeechTherapy: 'Talterapi',
	SportsMedicinePhysician: 'Idrottsmedicinsk läkare',
	Spouse: 'Make',
	SpreadsheetColumnExample: 'till exempel ',
	SpreadsheetColumns: 'Spreadsheetkolumner',
	SpreadsheetUploaded: 'Kalkylblad laddat upp',
	SpreadsheetUploading: 'Laddar upp...',
	Staff: 'Personal',
	StaffAccessDescriptionAdmin: 'Administratörer kan hantera allt på plattformen.',
	StaffAccessDescriptionStaff: `Personalmedlemmar kan hantera klienter, anteckningar och dokumentation som de har skapat eller har delats
 med dem, boka möten, hantera fakturor.`,
	StaffContactAssignedSubject:
		'{actorProfileName} har tilldelat {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} och {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { och 1 annan klient} other { och # andra klienter}} till dig',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} har delat inkorg {inboxName} med dig',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} har tagit bort din åtkomst till {inboxName}-inkorgen',
	StaffMembers: 'Personalmedlemmar',
	StaffMembersNumber: '{billedUsers, plural, one {# teammedlem} other {# teammedlemmar}}',
	StaffSavedSuccessSnackbar: 'Teammedlemmars information har sparats!',
	StaffSelectorAdminRole: 'Administratör',
	StaffSelectorStaffRole: 'Personalmedlem',
	StandardAppointment: 'Standardutnämning',
	StandardColor: 'Uppgiftsfärg',
	StartAndEndTime: 'Start- och sluttid',
	StartCall: 'Starta samtalet',
	StartDate: 'Startdatum',
	StartDictating: 'Börja diktera',
	StartImport: 'Starta import',
	StartRecordErrorTitle: 'Det gick inte att starta din inspelning',
	StartRecording: 'Börja spela in',
	StartTimeIncrements: 'Starttiden ökar',
	StartTimeIncrementsView: '{startTimeIncrements} min intervall',
	StartTranscribing: 'Börja transkribera',
	StartTranscribingNotes:
		'Välj de klienter som du vill generera anteckningar för. Klicka sedan på knappen "Starta transkription" för att starta inspelningen',
	StartTranscription: 'Starta transkription',
	StartVideoCall: 'Starta videosamtal',
	StartWeekOn: 'Börja veckan på',
	StartedBy: 'Började av ',
	Starter: 'Starter',
	State: 'Ange',
	StateIndustrialAccidentProviderNumber: 'Ange nummer för leverantör av arbetsolyckor',
	StateLicenseNumber: 'Ange licensnummer',
	Statement: 'Påstående',
	StatementDescriptor: 'Uttalande deskriptor',
	StatementDescriptorToolTip:
		'Utdragsbeskrivningen visas på dina kunders bank- eller kreditkortsutdrag. Det måste vara mellan 5 och 22 tecken och återspegla ditt företagsnamn.',
	StatementNumber: 'Uttalande #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Ange en statusetikett',
	StepFather: 'Styvfar',
	StepMother: 'Styvmor',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription:
		'Genom att sluta ignorera dessa avsändare kommer framtida konversationer att skickas till "Inkorgen". Är du säker på att du vill sluta ignorera dessa avsändare?',
	StopIgnoring: 'Sluta ignorera',
	StopIgnoringSenders: 'Sluta ignorera avsändare',
	StopIgnoringSendersSuccess: 'Slutade ignorera e-postadressen <mark>{addresses}</mark>',
	StopSharing: 'Sluta dela',
	StopSharingLabel: 'carepatron.com delar din skärm.',
	Storage: 'Lagring',
	StorageAlmostFullDescription: '🚀 Uppgradera nu för att hålla ditt konto igång smidigt.',
	StorageAlmostFullTitle: 'Du har använt {percentage}% av din lagringsgräns för arbetsyta!',
	StorageFullDescription: 'Få mer lagringsutrymme genom att uppgradera din prenumeration.',
	StorageFullTitle: '	Ditt lagringsutrymme är fullt.',
	Street: 'Gata',
	StripeAccountNotCompleteErrorCode:
		'Onlinebetalningar är inte {hasProviderName, select, true {inställda för {providerName}} other {aktiverade för den här leverantören}}.',
	StripeAccountRejectedError: 'Stripe-kontot har avvisats. Kontakta supporten.',
	StripeBalance: 'Stripe Balance',
	StripeChargesInfoToolTip: 'Låter dig debitera debitering ',
	StripeFeesDescription:
		'Carepatron använder Stripe för att få dig betalt snabbt och hålla din betalningsinformation säker. Tillgängliga betalningsmetoder varierar beroende på region, alla större debiteringar ',
	StripeFeesDescriptionItem1: 'Bearbetningsavgifter tillämpas på varje lyckad transaktion, du kan {link}.',
	StripeFeesDescriptionItem2: 'Utbetalningar sker dagligen men hålls kvar i upp till 4 dagar.',
	StripeFeesLinkToRatesText: 'se våra priser här',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Tyvärr, det finns ett minsta belopp på {minimumAmount} som krävs för fakturor som använder onlinebetalningar.',
	StripePaymentsDisabled: 'Onlinebetalningar inaktiverade. Kontrollera dina betalningsinställningar.',
	StripePaymentsUnavailable: 'Betalningar är inte tillgängliga',
	StripePaymentsUnavailableDescription: 'Ett fel uppstod när betalningar skulle laddas. Försök igen senare.',
	StripePayoutsInfoToolTip: 'Gör att du kan få utbetalning till ditt bankkonto',
	StyleYourWorkspace: '<mark>Styla</mark> din arbetsyta',
	StyleYourWorkspaceDescription1:
		'Vi hämtade varumärkestillgångarna från din webbplats. Känn dig fri att redigera dem eller fortsätt till din Carepatron-arbetsyta',
	StyleYourWorkspaceDescription2:
		'Använd dina varumärkestillgångar för att anpassa fakturor och onlinebokningar för en sömlös kundupplevelse',
	SubAdvanced: 'Avancerad',
	SubEssential: 'Viktigt',
	SubOrganization: 'Organisation',
	SubPlus: 'Plus',
	SubProfessional: 'Professionell',
	Subject: 'Ämne',
	Submit: 'Överlämna',
	SubmitElectronically: 'Skicka in elektroniskt',
	SubmitFeedback: 'Skicka feedback',
	SubmitFormValidationError: 'Se till att alla obligatoriska fält är korrekt ifyllda och försök skicka igen.',
	Submitted: 'Inlämnad',
	SubmittedDate: 'Inlämningsdatum',
	SubscribePerMonth: 'Prenumerera {price} {isMonthly, select, true {per månad} other {per år}}',
	SubscriptionDiscountDescription:
		'{percentOff}% rabatt {months, select, null { } other { {months, plural, one {för # månad} other {för # månader}}}}',
	SubscriptionFreeTrialDescription: 'Gratis till {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Vi kunde inte slutföra betalningen för din prenumeration. Kontrollera dina betalningsuppgifter',
	SubscriptionPlanDetailsHeader: 'Per användare/månadsfakturerad årligen',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} debiteras månadsvis (USD)',
	SubscriptionPlans: 'Prenumerationsplaner',
	SubscriptionPlansDescription:
		'Uppgradera din plan för att låsa upp ytterligare fördelar och hålla din praktik igång smidigt.',
	SubscriptionPlansDescriptionNoPermission:
		'Det ser ut som att du inte har tillgång till att uppgradera just nu — kontakta din administratör för hjälp.',
	SubscriptionSettings: 'Prenumerationsinställningar',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> av lagringsutrymmet används',
	SubscriptionSettingsStorageUsed: '{använda} av {gräns} använda',
	SubscriptionSettingsUnlimitedStorage: 'Obegränsat lagringsutrymme tillgängligt',
	SubscriptionSummary: 'Prenumerationssammanfattning',
	SubscriptionUnavailableOverStorageLimit: 'Din nuvarande användning överstiger lagringsgränsen för den här planen.',
	SubscriptionUnpaidBannerButton: 'Gå till prenumerationer',
	SubscriptionUnpaidBannerDescription: 'Kontrollera att dina betalningsuppgifter är korrekta och försök igen',
	SubscriptionUnpaidBannerTitle: 'Vi kunde inte slutföra betalningen för din prenumeration.',
	Subscriptions: 'Prenumerationer',
	SubscriptionsAndPayments: 'Prenumerationer ',
	Subtotal: 'Delsumma',
	SuburbOrProvince: 'Förort/provins',
	SuburbOrState: 'Förort/stat',
	SuccessSavedNoteChanges: 'Anteckningsändringar har sparats',
	SuccessShareDocument: 'Dokumentet har delats',
	SuccessShareNote: 'Delade anteckningen',
	SuccessfullyCreatedValue: 'Skapade {value}',
	SuccessfullyDeletedTranscriptionPart: 'Transkriptionsdelen har raderats',
	SuccessfullyDeletedValue: 'Framgångsrikt raderat {value}',
	SuccessfullySubmitted: 'Skickat ',
	SuccessfullyUpdatedClientSettings: 'Klientinställningar har uppdaterats',
	SuccessfullyUpdatedTranscriptionPart: 'Transkriptionsdelen har uppdaterats',
	SuccessfullyUpdatedValue: 'Uppdaterat {value}',
	SuggestedAIPoweredTemplates: 'Förslag på AI-drivna mallar',
	SuggestedAITemplates: 'Föreslagna AI-mallar',
	SuggestedActions: 'Föreslagna åtgärder',
	SuggestedLocations: 'Föreslagna platser',
	Suggestions: 'Förslag',
	Summarise: 'AI sammanfattning',
	SummarisingContent: 'Sammanfatta {title}',
	Sunday: 'söndag',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superbill ',
	SuperbillAutomationMonthly: 'Aktiv • Sista dagen i månaden',
	SuperbillAutomationNoEmail:
		'För att skicka automatiska faktureringsdokument, lägg till en e-postadress för den här klienten',
	SuperbillAutomationNotActive: 'Inte aktiv',
	SuperbillAutomationUpdateFailure: 'Det gick inte att uppdatera Superbill-automatiseringsinställningarna',
	SuperbillAutomationUpdateSuccess: 'Superbill automationsinställningar har uppdaterats framgångsrikt',
	SuperbillClientHelperText: 'Denna information är förfylld från kundinformationen',
	SuperbillNotFoundDescription:
		'Kontakta din leverantör och be dem om mer information eller för att skicka om supernotan.',
	SuperbillNotFoundTitle: 'Superbill hittades inte',
	SuperbillNumber: 'Superfaktura #{number}',
	SuperbillNumberAlreadyExists: 'Superbill-kvittonummer finns redan',
	SuperbillPracticeHelperText: 'Denna information är ifylld i förväg från övningsfaktureringsinställningarna',
	SuperbillProviderHelperText: 'Denna information är förfylld från personaluppgifterna',
	SuperbillReceipts: 'Superbill kvitton',
	SuperbillsEmptyStateDescription: 'Inga supernäbbar har hittats.',
	Surgeon: 'Kirurg',
	Surgeons: 'Kirurger',
	SurgicalTechnologist: 'Kirurgisk teknolog',
	SwitchFromAnotherPlatform: 'Jag byter från en annan plattform',
	SwitchToMyPortal: 'Byt till Min portal',
	SwitchToMyPortalTooltip: `Få tillgång till din egen personliga portal,
 gör det möjligt för dig att utforska din
 kundens portalupplevelse.`,
	SwitchWorkspace: 'Byt arbetsyta',
	SwitchingToADifferentPlatform: 'Byter till en annan plattform',
	Sydney: 'Sydney',
	SyncCalendar: 'Synkronisera kalender',
	SyncCalendarModalDescription:
		'Andra teammedlemmar kommer inte att kunna se dina synkroniserade kalendrar. Kundbeställningar kan endast uppdateras eller raderas inifrån Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Visa min kalender i Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Synkronisera min kalender med Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Synkronisera vårdnadshavares möten med min kalender',
	SyncCarepatronAppointmentsWithMyCalendar: 'Synka Carepatron-möten med min kalender',
	SyncGoogleCalendar: 'Synkronisera Google kalender',
	SyncInbox: 'Synkronisera inkorgen med Carepatron',
	SyncMyCalendarToCarepatron: 'Synka min kalender till Carepatron',
	SyncOutlookCalendar: 'Synkronisera Outlook-kalendern',
	SyncedFromExternalCalendar: 'Synkroniserad från extern kalender',
	SyncingCalendarName: 'Synkar kalender {calendarName}',
	SyncingFailed: 'Synkroniseringen misslyckades',
	SystemGenerated: 'Systemgenererat',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tabell',
	TableRowLabel: 'Tabellrad för {value}',
	TagSelectorNoOptionsText: 'Klicka på "skapa ny" för att lägga till ny tagg',
	Tags: 'Taggar',
	TagsInputPlaceholder: 'Sök eller skapa taggar',
	Task: 'Uppgift',
	TaskAttendeeStatusUpdatedSuccess: 'Framgångsrikt uppdaterade mötesstatusar',
	Tasks: 'Uppgifter',
	Tax: 'Beskatta',
	TaxAmount: 'Skattebelopp',
	TaxID: 'Organisationsnummer',
	TaxIdType: 'Typ av skatte-ID',
	TaxName: 'Skattenamn',
	TaxNumber: 'Skattenummer',
	TaxNumberType: 'Momsnummertyp',
	TaxNumberTypeInvalid: '{type} är ogiltigt',
	TaxPercentageOfAmount: '{taxName} ({percentage}% av {amount})',
	TaxRate: 'Skattesats',
	TaxRatesDescription: 'Hantera skattesatserna som kommer att tillämpas på dina fakturarader.',
	Taxable: 'Beskattningsbar',
	TaxonomyCode: 'Taxonomikod',
	TeacherAssistant: 'Lärarassistent',
	Team: 'Team',
	TeamMember: 'Teammedlem',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {är} other {är}} redan bokad vid denna tidpunkt.{br}Välj en ny tid för att undvika dubbelbokning.',
	TeamMembers: 'Teammedlemmar',
	TeamMembersColour: 'Lagmedlemmarnas färg',
	TeamMembersDetails: 'Teammedlemmars detaljer',
	TeamSize: 'Hur många personer finns i ditt team?',
	TeamTemplates: 'Team mallar',
	TeamTemplatesSectionDescription: 'Skapad av dig och ditt team',
	TelehealthAndVideoCalls: 'Telehälsa ',
	TelehealthProvidedOtherThanInPatientCare: 'Telehälsa tillhandahålls för annat än slutenvård',
	TelehealthVideoCall: 'Telehälsa videosamtal',
	Template: 'Mall',
	TemplateDescription: 'Mallbeskrivning',
	TemplateDetails: 'Malldetaljer',
	TemplateEditModeViewSwitcherDescription: 'Skapa och redigera mall',
	TemplateGallery: 'Gemenskapsmallar',
	TemplateImportCompletedNotificationSubject: 'Mall import färdig! {templateTitle} är redo att användas.',
	TemplateImportFailedNotificationSubject: 'Misslyckades med att importera filen {fileName}.',
	TemplateName: 'Mallnamn',
	TemplateNotFound: 'Det gick inte att hitta mallen.',
	TemplatePreviewErrorMessage: 'Ett fel uppstod när förhandsgranskningen av mallen laddades',
	TemplateResponderModeViewSwitcherDescription: 'Förhandsgranska och interagera med formulär',
	TemplateResponderModeViewSwitcherTooltipTitle: 'Kontrollera hur dina formulär ser ut när de fylls i av svararna',
	TemplateSaved: 'Sparade ändringar',
	TemplateTitle: 'Mallens titel',
	TemplateType: 'Malltyp',
	Templates: 'Mallar',
	TemplatesCategoriesFilter: 'Filtrera efter kategori',
	TemplatesPublicTemplatesFilter: ' Filtrera efter gemenskap/lag',
	Text: 'Text',
	TextAlign: 'Textjustering',
	TextColor: 'Textfärg',
	ThankYouForYourFeedback: 'Tack för din feedback!',
	ThanksForLettingKnow: 'Tack för att du meddelade oss.',
	ThePaymentMethod: 'Betalningsmetoden',
	ThemThey: 'Dem/De',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Fler teman',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Mörk',
	ThemeColorLightMode: 'Ljus',
	ThemeColorModePickerTitle: 'Färgläge',
	ThemeColorSystemMode: 'System',
	ThemeCpColorPickerTitle: 'Carepatron-teman',
	ThemePanelDescription: 'Välj mellan ljust och mörkt läge och anpassa dina temapreferenser',
	ThemePanelTitle: 'Utseende',
	Then: 'Sedan',
	Therapist: 'Terapeut',
	Therapists: 'Terapeuter',
	Therapy: 'Terapi',
	Thick: 'Tjock',
	Thin: 'Tunn',
	ThirdPerson: '3:e person',
	ThisAndFollowingAppointments: 'Detta och följande möten',
	ThisAndFollowingMeetings: 'Detta och följande möten',
	ThisAndFollowingReminders: 'Denna och följande påminnelser',
	ThisAndFollowingTasks: 'Detta och följande uppgifter',
	ThisAppointment: 'Detta möte',
	ThisMeeting: 'Detta möte',
	ThisMonth: 'Denna månad',
	ThisPerson: 'Den här personen',
	ThisReminder: 'Denna påminnelse',
	ThisTask: 'Denna uppgift',
	ThisWeek: 'Denna vecka',
	ThreeDay: '3 dagar',
	Thursday: 'torsdag',
	Time: 'Tid',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Tidsformat',
	TimeIncrement: 'Tidsökning',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {fm} other {em}}',
	TimeslotSize: 'Tidsluckans storlek',
	Timestamp: 'Tidsstämpel',
	Timezone: 'Tidszon',
	TimezoneDisplay: 'Visning av tidszon',
	TimezoneDisplayDescription: 'Hantera dina tidszonsvisningsinställningar.',
	Title: 'Titel',
	To: 'Till',
	ToYourWorkspace: 'till din arbetsplats',
	Today: 'I dag',
	TodayInHoursPlural: 'Idag i {count} {count, plural, one {timme} other {timmar}}',
	TodayInMinsAbbreviated: 'Idag på {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Växla rubrikcell',
	ToggleHeaderCol: 'Växla rubrikkolumn',
	ToggleHeaderRow: 'Växla rubrikrad',
	Tokyo: 'Tokyo',
	Tomorrow: 'I morgon',
	TomorrowAfternoon: 'Imorgon eftermiddag',
	TomorrowMorning: 'I morgon bitti',
	TooExpensive: 'För dyrt',
	TooHardToSetUp: 'För svårt att ställa in',
	TooManyFiles: 'Mer än 1 fil har upptäckts.',
	ToolsExample: 'Enkel övning, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Total',
	TotalAccountCredit: 'Total kontokredit',
	TotalAdjustments: 'Totala justeringar',
	TotalAmountToCreditInCurrency: 'Totalt belopp att kreditera ({currency})',
	TotalBilled: 'Totalt fakturerat',
	TotalConversations: '{total} {total, plural, =0 {konversation} one {konversation} other {konversationer}}',
	TotalOverdue: 'Totalt förfallna',
	TotalOverdueTooltip:
		'Totalt förfallna saldo inkluderar alla obetalda fakturor, oavsett datumintervall, som varken annulleras eller behandlas.',
	TotalPaid: 'Totalt betalt',
	TotalPaidTooltip:
		'Totalt betalat saldo inkluderar alla belopp från fakturor som har betalats inom det angivna datumintervallet.',
	TotalUnpaid: 'Totalt obetalt',
	TotalUnpaidTooltip:
		'Totalt obetalt saldo inkluderar alla utestående belopp från bearbetning, obetalda och skickade fakturor som förfaller inom det angivna datumintervallet.',
	TotalWorkflows: '{count} {count, plural, one {arbetsflöde} other {arbetsflöden}}',
	TotpSetUpManualEntryInstruction: 'Alternativt kan du manuellt ange koden nedan i appen:',
	TotpSetUpModalDescription:
		'Skanna QR-koden med din autentiseringsapp för att ställa in Multi-Factor Authentication.',
	TotpSetUpModalTitle: 'Konfigurera MFA-enhet',
	TotpSetUpSuccess: 'Du är redo! MFA har aktiverats.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Ange koden som genereras av din autentiseringsapp',
	Transcribe: 'Transkribera',
	TranscribeLanguageSelector: 'Välj inmatningsspråk',
	TranscribeLiveAudio: 'Transkribera liveljud',
	Transcribing: 'Transkriberar ljud...',
	TranscribingIn: 'Transkriberar in',
	Transcript: 'Avskrift',
	TranscriptRecordingCompleteInfo: 'Du kommer att se din utskrift här när inspelningen är klar.',
	TranscriptSuccessSnackbar: 'Bearbetad avskrift.',
	Transcription: 'Transkription',
	TranscriptionEmpty: 'Ingen transkription tillgänglig',
	TranscriptionEmptyHelperMessage:
		'Den här transkriptionen plockade inte upp någonting. Starta om den och försök igen.',
	TranscriptionFailedNotice: 'Denna transkription bearbetades inte',
	TranscriptionIdleMessage:
		'Vi hör inget ljud. Om du behöver mer tid, svara inom {timeValue} sekunder, annars kommer sessionen att avslutas.',
	TranscriptionInProcess: 'Transkribering pågår...',
	TranscriptionIncompleteNotice: 'Vissa delar av denna transkription bearbetades inte framgångsrikt',
	TranscriptionOvertimeWarning: '{scribeType} session ends in <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Är du säker på att du vill ta bort den här transkriptionsdelen?',
	TranscriptionText: 'Röst till sms',
	TranscriptsPending: 'Din utskrift kommer att finnas tillgänglig här efter sessionens slut.',
	Transfer: 'Överföra',
	TransferAndDelete: 'Överför och radera',
	TransferOwnership: 'Överlåta ägande',
	TransferOwnershipConfirmationModalDescription:
		'Den här åtgärden kan bara ångras om de överför äganderätten tillbaka till dig.',
	TransferOwnershipDescription: 'Överför ägandet av den här arbetsytan till en annan teammedlem.',
	TransferOwnershipSuccessSnackbar: 'Äganderätten har överförts!',
	TransferOwnershipToMember: 'Är du säker på att du vill överföra det här arbetsytan till {staff}?',
	TransferStatusAlert:
		'Att ta bort {numberOfStatuses, plural, one {denna status} other {dessa statusar}} kommer att påverka {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} klientstatus.</strong>} other {<strong>{numberOfAffectedRecords} klientstatusar.</strong>}}',
	TransferStatusDescription:
		'Välj en annan status för dessa klienter innan du fortsätter med borttagningen. Den här åtgärden kan inte ångras.',
	TransferStatusLabel: 'Överför till ny status',
	TransferStatusPlaceholder: 'Välj en befintlig status',
	TransferStatusTitle: 'Överför status före radering',
	TransferTaskAttendeeStatusAlert:
		'Att ta bort denna status kommer att påverka <strong>{number} framtida möten {number, plural, one {status} other {statusar}}. </strong>',
	TransferTaskAttendeeStatusDescription:
		'Välj en annan status för dessa klienter innan du fortsätter med raderingen. Den här åtgärden kan inte ångras.',
	TransferTaskAttendeeStatusSubtitle: 'Mötestid',
	TransferTaskAttendeeStatusTitle: 'Överföringsstatus innan radering',
	Trash: 'Skräp',
	TrashDeleteItemsModalConfirm: 'För att bekräfta, skriv {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Följande {count, plural, one {objekt} other {objekt}} kommer att raderas permanent och kan inte återställas.',
	TrashDeleteItemsModalTitle: 'Radera {count, plural, one {artikel} other {artiklar}} för alltid',
	TrashDeletedAllItems: 'Raderade alla objekt',
	TrashDeletedItems: 'Raderade {count, plural, one {objekt} other {objekt}}',
	TrashDeletedItemsFailure: 'Det gick inte att ta bort objekt från papperskorgen',
	TrashLocationAppointmentType: 'Kalender',
	TrashLocationBillingAndPaymentsType: 'Fakturering & betalningar',
	TrashLocationContactType: 'Kunder',
	TrashLocationNoteType: 'Anteckningar ',
	TrashRestoreItemsModalDescription:
		'Följande {count, plural, one {artikel} other {artiklar}} kommer att återställas.',
	TrashRestoreItemsModalTitle: 'Återställ {count, plural, one {objekt} other {objekt}}',
	TrashRestoredAllItems: 'Återställd alla objekt',
	TrashRestoredItems: 'Återställda {count, plural, one {artikel} other {artiklar}}',
	TrashRestoredItemsFailure: 'Det gick inte att återställa objekt från papperskorgen',
	TrashSuccessfullyDeletedItem: 'Framgångsrikt borttaget {type}',
	Trigger: 'Utlösare',
	Troubleshoot: 'Felsökning',
	TryAgain: 'Försök igen',
	Tuesday: 'tisdag',
	TwoToTen: '2 - 10',
	Type: 'Typ',
	TypeHere: 'Skriv här...',
	TypeToConfirm: 'För att bekräfta, skriv {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Rubrik 1',
	TypographyHeading2: 'Rubrik 2',
	TypographyHeading3: 'Rubrik 3',
	TypographyHeading4: 'Rubrik 4',
	TypographyHeading5: 'Rubrik 5',
	TypographyP: 'P',
	TypographyParagraph: 'Stycke',
	UnableToCompleteAction: 'Det gick inte att slutföra åtgärden.',
	UnableToPrintDocument: 'Det gick inte att skriva ut dokumentet. Försök igen senare.',
	Unallocated: 'Ej allokerad',
	UnallocatedPaymentDescription: `Denna betalning har inte allokerats till fakturerbara poster.
 Lägg till en tilldelning till obetalda artiklar, eller utfärda en kredit eller återbetalning.`,
	UnallocatedPaymentTitle: 'Ej allokerad betalning',
	UnallocatedPayments: 'Ofördelade betalningar',
	Unarchive: 'Avarkivera',
	Unassigned: 'Otilldelad',
	UnauthorisedInvoiceSnackbar: 'Du har inte åtkomst att hantera fakturor för den här klienten.',
	UnauthorisedSnackbar: 'Du har inte behörighet att göra detta.',
	Unavailable: 'Inte tillgänglig',
	Uncategorized: 'Okategoriserad',
	Unclaimed: 'Ohämtat',
	UnclaimedAmount: 'Obestående belopp',
	UnclaimedItems: 'Ohämtade föremål',
	UnclaimedItemsMustBeInCurrency: 'Endast objekt i följande valutor stöds: {currencies}',
	Uncle: 'Farbror',
	Unconfirmed: 'Obekräftad',
	Underline: 'Betona',
	Undo: 'Ångra',
	Unfavorite: 'Ogilla',
	Uninvoiced: 'Ej fakturerad',
	UninvoicedAmount: 'Ofaktuerad summa',
	UninvoicedAmounts:
		'{count, plural, =0 {Inga outfakturerade belopp} one {Outfakturerat belopp} other {Outfakturerade belopp}}',
	Unit: 'Enhet',
	UnitedKingdom: 'Storbritannien',
	UnitedStates: 'USA',
	UnitedStatesEast: 'USA - Öst',
	UnitedStatesWest: 'USA - Väst',
	Units: 'Enheter',
	UnitsIsRequired: 'Enheter krävs',
	UnitsMustBeGreaterThanZero: 'Enheterna måste vara större än 0',
	UnitsPlaceholder: '1',
	Unknown: 'Okänd',
	Unlimited: 'Obegränsad',
	Unlock: 'Låsa upp',
	UnlockNoteHelper: 'Innan du gör några nya ändringar måste redaktörerna låsa upp anteckningen.',
	UnmuteAudio: 'Slå på ljudet',
	UnmuteEveryone: 'Slå på ljudet för alla',
	Unpaid: 'Obetald',
	UnpaidInvoices: 'Obetalda fakturor',
	UnpaidItems: 'Obetalda föremål',
	UnpaidMultiple: 'Obetald',
	Unpublish: 'Avpublicera',
	UnpublishTemplateConfirmationModalPrompt:
		'Att ta bort <span>{title}</span> kommer att ta bort denna resurs från Carepatron-communityn. Den här åtgärden kan inte ångras.',
	UnpublishToCommunitySuccessMessage: 'Framgångsrikt borttaget ‛{title}’ från communityn',
	Unread: 'Oläst',
	Unrecognised: 'Okänd',
	UnrecognisedDescription:
		'Denna betalningsmetod känns inte igen av din nuvarande applikationsversion. Uppdatera din webbläsare för att få den senaste versionen för att se och redigera denna betalningsmetod.',
	UnsavedChanges: 'Osparade ändringar',
	UnsavedChangesPromptContent: 'Vill du spara dina ändringar innan du stänger?',
	UnsavedChangesPromptTitle: 'Du har osparade ändringar',
	UnsavedNoteChangesWarning: 'Ändringar du gjort kanske inte sparas',
	UnsavedTemplateChangesWarning: 'Ändringar du gjort kanske inte sparas',
	UnselectAll: 'Avmarkera allt',
	Until: 'Tills',
	UntitledConversation: 'Onamnad konversation',
	UntitledFolder: 'Namnlös mapp',
	UntitledNote: 'Obetitelad anteckning',
	UntitledSchedule: 'Namnlöst schema',
	UntitledSection: 'Namnlös sektion',
	UntitledTemplate: 'Namnlös mall',
	Unverified: 'Ej verifierad',
	Upcoming: 'Kommande',
	UpcomingAppointments: 'Kommande möten',
	UpcomingDateOverridesEmpty: 'Inga datumöverskridanden har hittats',
	UpdateAvailabilityScheduleFailure: 'Det gick inte att uppdatera tillgänglighetsschemat',
	UpdateAvailabilityScheduleSuccess: 'Tillgänglighetsschemat har uppdaterats',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Vill du att den nya prissättningen ska tillämpas på deltagarens fakturor och anspråk?',
	UpdateLink: 'Uppdatera länk',
	UpdatePrimaryEmailWarningDescription:
		'Att ändra din klients e-postadress kommer att leda till att de förlorar tillgången till sina befintliga möten och anteckningar.',
	UpdatePrimaryEmailWarningTitle: 'Klientens e-postadress ändring',
	UpdateSettings: 'Uppdatera inställningar',
	UpdateStatus: 'Uppdatera status',
	UpdateSuperbillReceiptFailure: 'Det gick inte att uppdatera Superbill-kvitto',
	UpdateSuperbillReceiptSuccess: 'Superbill-kvitto har uppdaterats',
	UpdateTaskBillingDetails: 'Uppdatera faktureringsinformation',
	UpdateTaskBillingDetailsDescription:
		'Utnämningspriset har ändrats. Vill du att den nya prissättningen ska tillämpas på deltagarens faktureringsartiklar, fakturor och anspråk? Välj de uppdateringar du vill fortsätta med.',
	UpdateTemplateFolderSuccessMessage: 'Mappen uppdaterades framgångsrikt',
	UpdateUnpaidInvoices: 'Uppdatera obetalda fakturor',
	UpdateUserInfoSuccessSnackbar: 'Användarinformationen har uppdaterats framgångsrikt!',
	UpdateUserSettingsSuccessSnackbar: 'Användarinställningar har uppdaterats framgångsrikt!',
	Upgrade: 'Uppgradera',
	UpgradeForSMSReminder: 'Uppgradera till <b>Professional</b> för obegränsade SMS-påminnelser',
	UpgradeNow: 'Uppgradera nu',
	UpgradePlan: 'Uppgradera plan',
	UpgradeSubscriptionAlertDescription:
		'Du har nästan slut på lagringsutrymme. Uppgradera din plan för att låsa upp extra lagringsutrymme och hålla din praktik igång smidigt!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Du har snart slut på lagring. Fråga någon i din praktik med <span>administratörsåtkomst</span> om att uppgradera din plan för att låsa upp extra lagring och hålla din praktik igång smidigt!',
	UpgradeSubscriptionAlertTitle: 'Det är dags att uppgradera ditt abonnemang',
	UpgradeYourPlan: 'Uppgradera din plan',
	UploadAudio: 'Ladda upp ljud',
	UploadFile: 'Ladda upp fil',
	UploadFileDescription: 'Vilken mjukvaruplattform byter du från?',
	UploadFileMaxSizeError: 'Filen är för stor. Maximal filstorlek är {fileSizeLimit}.',
	UploadFileSizeLimit: 'Storleksgräns {size}MB',
	UploadFileTileDescription: 'Använd CSV-, XLS-, XLSX- eller ZIP-filer för att ladda upp dina klienter.',
	UploadFileTileLabel: 'Ladda upp en fil',
	UploadFiles: 'Ladda upp filer',
	UploadIndividually: 'Ladda upp filer individuellt',
	UploadLogo: 'Ladda upp logotyp',
	UploadPhoto: 'Ladda upp foto',
	UploadToCarepatron: 'Ladda upp till Carepatron',
	UploadYourLogo: 'Ladda upp din logotyp',
	UploadYourTemplates: 'Ladda upp dina mallar så konverterar vi dem åt dig',
	Uploading: 'Laddar upp',
	UploadingAudio: 'Laddar upp ditt ljud...',
	UploadingFiles: 'Laddar upp filer',
	UrlLink: 'URL-länk',
	UsageCount: 'Använd {count} gånger',
	UsageLimitValue: '{used} av {limit} använda',
	UsageValue: '{använd} använd',
	Use: 'Använda',
	UseAiToAutomateYourWorkflow: 'Använd AI för att automatisera ditt arbetsflöde!',
	UseAsDefault: 'Använd som standard',
	UseCustom: 'Använd anpassad',
	UseDefault: 'Använd standard',
	UseDefaultFilters: 'Använd standardfilter',
	UseTemplate: 'Använd mall',
	UseThisCard: 'Använd detta kort',
	UseValue: 'Använd "{value}"',
	UseWorkspaceDefault: 'Använd arbetsyta som standard',
	UserIsTyping: '{name} skriver...',
	Username: 'Användarnamn',
	Users: 'Användare',
	VAT: 'MOMS',
	ValidUrl: 'URL-länken måste vara en giltig URL.',
	Validate: 'Bekräfta',
	Validated: 'Validerad',
	Validating: 'Validerar',
	ValidatingContent: 'Validerar innehåll...',
	ValidatingTranscripts: 'Validerar avskrifter...',
	ValidationConfirmPasswordRequired: 'Bekräfta lösenord krävs',
	ValidationDateMax: 'Måste vara före {max}',
	ValidationDateMin: 'Måste vara efter {min}',
	ValidationDateRange: 'Start- och slutdatum krävs',
	ValidationEndDateMustBeAfterStartDate: 'Slutdatum måste vara efter startdatum',
	ValidationMixedDefault: 'Detta är ogiltigt',
	ValidationMixedRequired: 'Detta krävs',
	ValidationNumberInteger: 'Måste vara ett heltal',
	ValidationNumberMax: 'Måste vara {max} eller mindre',
	ValidationNumberMin: 'Måste vara {min} eller mer',
	ValidationPasswordNotMatching: 'Lösenord matchar inte',
	ValidationPrimaryAddressIsRequired: 'Adress krävs när den är inställd som standard',
	ValidationPrimaryPhoneNumberIsRequired: 'Telefonnummer krävs när det är inställt som standard',
	ValidationServiceMustBeNotBeFuture: 'Tjänsten får inte vara idag eller i framtiden',
	ValidationStringEmail: 'Måste vara en giltig e-postadress',
	ValidationStringMax: 'Måste vara {max} tecken eller färre',
	ValidationStringMin: 'Måste vara {min} tecken eller fler',
	ValidationStringPhoneNumber: 'Måste vara ett giltigt telefonnummer',
	ValueMinutes: '{value} minuter',
	VerbosityConcise: 'Koncis',
	VerbosityDetailed: 'Detaljerad',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Super detaljerad',
	VerificationCode: 'Verifieringskod',
	VerificationEmailDescription: 'Ange din e-postadress och verifieringskoden som vi just har skickat till dig.',
	VerificationEmailSubtitle: 'Kontrollera skräppostmappen - om mejlet inte har kommit',
	VerificationEmailTitle: 'Verifiera e-post',
	VerificationOption: 'E-postverifiering',
	Verified: 'Verifierad',
	Verify: 'Kontrollera',
	VerifyAndSubmit: 'Verifiera & skicka in',
	VerifyEmail: 'Verifiera e-post',
	VerifyEmailAccessCode: 'Bekräftelsekod',
	VerifyEmailAddress: 'Verifiera e-postadress',
	VerifyEmailButton: 'Verifiera och logga ut',
	VerifyEmailSentSnackbar: 'Verifieringsmail har skickats. Kontrollera din inkorg.',
	VerifyEmailSubTitle: 'Kontrollera skräppostmappen om e-postmeddelandet inte har kommit',
	VerifyEmailSuccessLogOutSnackbar: 'Framgång! Logga ut för att tillämpa ändringarna.',
	VerifyEmailSuccessSnackbar: 'Framgång! E-post verifierad. Logga in för att fortsätta som ett verifierat konto.',
	VerifyEmailTitle: 'Verifiera din e-post',
	VerifyNow: 'Verifiera nu',
	Veterinarian: 'Veterinär',
	VideoCall: 'Videosamtal',
	VideoCallAudioInputFailed: 'Ljudindataenhet fungerar inte',
	VideoCallAudioInputFailedMessage: 'Öppna inställningarna och kontrollera om du har mikrofonkällan korrekt inställd',
	VideoCallChatBanner: 'Meddelanden kan ses av alla i det här samtalet och kommer att raderas när samtalet avslutas.',
	VideoCallChatSendBtn: 'Skicka ett meddelande',
	VideoCallChatTitle: 'Chatta',
	VideoCallDisconnectedMessage: 'Du förlorade din nätverksanslutning. Försöker ansluta igen',
	VideoCallOptionInfo: 'Carepatron kommer att hantera videosamtal för dina möten om Zoom inte har anslutits',
	VideoCallTilePaused: 'Den här videon är pausad på grund av problem med ditt nätverk',
	VideoCallTranscriptionFormDescription: 'Du kan justera dessa inställningar när som helst',
	VideoCallTranscriptionFormHeading: 'Anpassa din AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'Det genererade utdataspråket',
	VideoCallTranscriptionFormNoteTemplateField: 'Ställ in standardanteckningsmall',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Inga mallar med AI hittades',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Välj en mall',
	VideoCallTranscriptionPronounField: 'Dina pronomen',
	VideoCallTranscriptionRecordingNote:
		'I slutet av sessionen får du en genererad **{noteTemplate} anteckning** och ett utskrift.',
	VideoCallTranscriptionReferClientField: 'Se Kund som',
	VideoCallTranscriptionReferPractitionerField: 'Se utövare som',
	VideoCallTranscriptionTitle: 'AI-skrivare',
	VideoCallTranscriptionVerbosityField: 'Ordrikedom',
	VideoCallTranscriptionWritingPerspectiveField: 'Skrivperspektiv',
	VideoCalls: 'Videosamtal',
	VideoConferencing: 'Videokonferenser',
	VideoOff: 'Videon är avstängd',
	VideoOn: 'Videon är avstängd',
	VideoQual360: 'Låg kvalitet (360p)',
	VideoQual540: 'Medium kvalitet (540p)',
	VideoQual720: 'Hög kvalitet (720p)',
	View: 'Se',
	ViewAll: 'Visa alla',
	ViewAppointment: 'Visa möte',
	ViewBy: 'Visa efter',
	ViewClaim: 'Visa anspråk',
	ViewCollection: 'Se samlingen',
	ViewDetails: 'Visa detaljer',
	ViewEnrollment: 'Visa registrering',
	ViewPayment: 'Visa betalning',
	ViewRecord: 'Visa post',
	ViewRemittanceAdvice: 'Visa betalningsavisering',
	ViewRemittanceAdviceHeader: 'Krav på betalningsavis',
	ViewRemittanceAdviceSubheader: 'Anspråk {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Visa inställningar',
	ViewStripeDashboard: 'Visa Stripe instrumentpanel',
	ViewTemplate: 'Visa mall',
	ViewTemplates: 'Visa mallar',
	ViewableBy: 'Visas av',
	ViewableByHelper:
		'Du och teamet har alltid tillgång till anteckningar som ni publicerar. Du kan välja att dela denna anteckning med kunden och/eller deras relationer',
	Viewer: 'Visare',
	VirtualLocation: 'Virtuell plats',
	VisibleTo: 'Synlig för',
	VisitOurHelpCentre: 'Besök vårt hjälpcenter',
	VisualEffects: 'Visuella effekter',
	VoiceFocus: 'Röstfokus',
	VoiceFocusLabel: 'Filtrerar bort ljud från din mikrofon som inte är tal',
	Void: 'Ogiltig',
	VoidCancelPriorClaim: 'Makulera/Avbryt tidigare krav',
	WaitingforMins: 'Väntar i {count} min.',
	Warning: 'Varning',
	WatchAVideo: 'titta på en video',
	WatchDemoVideo: 'Se demovideo',
	WebConference: 'Webbkonferens',
	WebConferenceOrVirtualLocation: 'Webbkonferens / virtuell plats',
	WebDeveloper: 'Webbutvecklare',
	WebsiteOptional: 'Webbplats <span>(valfritt)</span>',
	WebsiteUrl: 'Webbplatsens URL',
	Wednesday: 'onsdag',
	Week: 'Vecka',
	WeekPlural: '{count, plural, one {vecka} other {veckor}}',
	Weekly: 'Varje vecka',
	WeeksPlural: '{age, plural, one {# vecka} other {# veckor}}',
	WelcomeBack: 'Välkommen tillbaka',
	WelcomeBackName: 'Välkommen tillbaka, {name}',
	WelcomeName: 'Välkommen {name}',
	WelcomeToCarepatron: 'Välkommen till Carepatron',
	WhatCanIHelpWith: 'Vad kan jag hjälpa till med?',
	WhatDidYouLikeResponse: 'Vad gillade du med det här svaret?',
	WhatIsCarepatron: 'Vad är Carepatron?',
	WhatMadeYouCancel: `Vad fick dig att säga upp din plan?
 Markera allt som gäller.`,
	WhatServicesDoYouOffer: 'Vad<mark> tjänster</mark> erbjuder du?',
	WhatServicesDoYouOfferDescription: 'Du kan redigera eller lägga till fler tjänster senare.',
	WhatsYourAvailability: 'Vad är din <mark>tillgänglighet?</mark>',
	WhatsYourAvailabilityDescription: 'Du kan lägga till fler scheman senare.',
	WhatsYourBusinessName: 'Vad är din<mark> företagsnamn?</mark>',
	WhatsYourTeamSize: 'Vad är din<mark> lagstorlek?</mark>',
	WhatsYourTeamSizeDescription: 'Detta hjälper oss att ställa in din arbetsyta korrekt.',
	WhenThisHappens: 'När detta händer:',
	WhichBestDescribesYou: 'Vilket bäst<mark> beskriver dig?</mark>',
	WhichPlatforms: 'Vilka plattformar?',
	Wife: 'Fru',
	WorkflowDescription: 'Arbetsflödesbeskrivning',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Mallar kan länka till arbetsflöden för smidigare processer. Visa länkade arbetsflöden för att spåra och uppdatera dem enkelt.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Koppla ihop dina SMS + e-postmeddelanden baserat på vanliga utlösare',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Arbetsflödesautomatiseringar',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Automatiserade arbetsflöden',
	WorkflowTemplateConfigKey_Body: 'Kropp',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Visa varumärke',
	WorkflowTemplateConfigKey_Content: 'Innehåll',
	WorkflowTemplateConfigKey_Footer: 'Fot',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Visa sidfot',
	WorkflowTemplateConfigKey_Header: 'Rubrik',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Visa rubrik',
	WorkflowTemplateConfigKey_SecurityFooter: 'Säkerhetsfot',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Visa säkerhetsfotnot',
	WorkflowTemplateConfigKey_Subject: 'Ämne',
	WorkflowTemplateConfigKey_Title: 'Titel',
	WorkflowTemplateDeleteConfirmationMessage:
		'Är du säker på att du vill ta bort den här mallen? Den här åtgärden kan inte ångras.',
	WorkflowTemplateDeleteConfirmationTitle: 'Radera meddelandemall',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Är du säker? Detta kommer endast att ta bort {locale}-versionen – andra språk kommer inte att påverkas. Denna åtgärd kan inte ångras.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Radera ‘{locale}’ mall',
	WorkflowTemplateDeletedSuccess: 'Meddelandemallen har tagits bort.',
	WorkflowTemplateEditorDetailsTab: 'Malldetaljer',
	WorkflowTemplateEditorEmailContent: 'E-postinnehåll',
	WorkflowTemplateEditorEmailContentTab: 'E-postinnehåll',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert:
		'Förhandsgranskningar använder provdata för att visa vad dina klienter kommer att se.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Är du säker? Detta kommer att återställa versionen till systemets standardsmall. Den här åtgärden kan inte ångras.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Återställ mall',
	WorkflowTemplateSendTestEmail: 'Skicka testmejl',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Prova din e-postkonfiguration genom att skicka ett testmeddelande till dig själv.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Mottagarens e-post',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Skicka test',
	WorkflowTemplateSendTestEmailDialogTitle: 'Skicka ett testmeddelande',
	WorkflowTemplateSendTestEmailSuccess:
		'Success! Ditt <mark>{templateName}</mark> test-e-postmeddelande har skickats.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Hantera dina mallar och lägg till flera språkversioner för att kommunicera effektivt med kunder.',
	WorkflowTemplateTemplateEditor: 'Mallredigerare',
	WorkflowTemplateTranslateLocaleError: 'Något gick fel vid översättning av innehåll',
	WorkflowTemplateTranslateLocaleSuccess: 'Framgångsrikt översatt innehållet till <strong>{locale}</strong>',
	WorkflowsAndReminders: 'Arbetsflöden ',
	WorkflowsManagement: 'Arbetsflödeshantering',
	WorksheetAndHandout: 'Arbetsblad/Handout',
	WorksheetsAndHandoutsDescription: 'För kundengagemang och utbildning',
	Workspace: 'Arbetsyta',
	WorkspaceBranding: 'Varumärke på arbetsplatsen',
	WorkspaceBrandingDescription: `Märk enkelt din arbetsyta med en sammanhållen stil som speglar din
 professionalism och personlighet. Anpassa fakturor till onlinebokning för en vacker
 kundupplevelse.`,
	WorkspaceName: 'Arbetsytans namn',
	Workspaces: 'Arbetsytor',
	WriteOff: 'Avskrivning',
	WriteOffModalDescription: 'Du har <mark>{count} {count, plural, one {rad} other {rader}}</mark> att avskriva',
	WriteOffModalTitle: 'Avskrivningsjustering',
	WriteOffReasonHelperText: 'Detta är en intern anteckning och kommer inte att vara synlig för din klient.',
	WriteOffReasonPlaceholder:
		'Att lägga till en avskrivningsorsak kan hjälpa när du granskar fakturerbara transaktioner',
	WriteOffTotal: 'Total avskrivning ({currencyCode})',
	Writer: 'Författare',
	Yearly: 'Årlig',
	YearsPlural: '{age, plural, one {# år} other {# år}}',
	Yes: 'Ja',
	YesArchive: 'Ja, arkiv',
	YesDelete: 'Ja, radera',
	YesDeleteOverride: 'Ja, ta bort åsidosättande',
	YesDeleteSection: 'Ja, radera',
	YesDisconnect: 'Ja, koppla bort',
	YesEnd: 'Ja, slut',
	YesEndTranscription: 'Ja, avsluta transkriptionen',
	YesImFineWithThat: 'Ja, jag mår bra av det',
	YesLeave: 'Ja, lämna',
	YesMinimize: 'Ja, minimera',
	YesOrNoAnswerTypeDescription: 'Konfigurera svarstyp',
	YesOrNoFormPrimaryText: 'Ja | Inga',
	YesOrNoFormSecondaryText: 'Välj ja eller nej alternativ',
	YesProceed: 'Ja, fortsätt',
	YesRemove: 'Ja, ta bort',
	YesRestore: 'Ja, återställ',
	YesStopIgnoring: 'Ja, sluta ignorera',
	YesTransfer: 'Ja, överför',
	Yesterday: 'I går',
	YogaInstructor: 'Yogainstruktör',
	You: 'Du',
	YouArePresenting: 'Du presenterar',
	YouCanChooseMultiple: 'Du kan välja flera',
	YouCanSelectMultiple: 'Du kan välja flera',
	YouHaveOngoingTranscription: 'Du har en pågående transkription',
	YourAnswer: 'Ditt svar',
	YourDisplayName: 'Ditt visningsnamn',
	YourSpreadsheetColumns: 'Dina kalkylbladskolumner',
	YourTeam: 'Ditt team',
	ZipCode: 'Postnummer',
	Zoom: 'Zoom',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Du kan inte lägga till ett Zoom-samtal för den här teammedlemmen. Se <a>supportdokumentationen för mer information.</a>',
};

export default items;
