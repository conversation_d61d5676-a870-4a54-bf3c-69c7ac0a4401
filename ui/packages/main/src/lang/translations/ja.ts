import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'AIプロンプト',
	ATeamMemberIsRequired: 'チームメンバーが必要です。',
	AboutClient: 'クライアントについて',
	AcceptAppointment: 'ご予約をご確認いただきありがとうございます',
	AcceptTermsAndConditionsRequired: '利用規約に同意する ',
	Accepted: '承諾済み',
	AccessGiven: 'アクセスを許可',
	AccessPermissions: 'アクセス権限',
	AccessType: 'アクセスタイプ',
	Accident: '事故',
	Account: 'アカウント',
	AccountCredit: 'アカウントクレジット',
	Accountant: '会計士',
	Action: 'アクション',
	Actions: '行動',
	Active: 'アクティブ',
	ActiveTags: 'アクティブタグ',
	ActiveUsers: 'アクティブユーザー',
	Activity: '活動',
	Actor: '俳優',
	Acupuncture: '鍼',
	Acupuncturist: '鍼灸師',
	Acupuncturists: '鍼灸師',
	AcuteManifestationOfAChronicCondition: '慢性疾患の急性症状',
	Add: '追加',
	AddADescription: '説明を追加',
	AddALocation: '場所を追加',
	AddASecondTimezone: '2番目のタイムゾーンを追加する',
	AddAddress: '住所を追加',
	AddAnother: '  追加する',
	AddAnotherAccount: '別のアカウントを追加',
	AddAnotherContact: '別の連絡先を追加する',
	AddAnotherOption: '別のオプションを追加',
	AddAnotherTeamMember: 'もう一人のチームメンバーを追加する',
	AddAvailablePayers: '+ 利用可能な支払人を追加する',
	AddAvailablePayersDescription:
		'ワークスペースのペイアーリストに追加するペイアーを検索してください。追加後、必要に応じて登録を管理したり、ペイアーの詳細を調整したりできます。',
	AddCaption: '字幕を追加',
	AddClaim: 'クレームを追加',
	AddClientFilesModalDescription: 'アクセスを制限するには、「閲覧可能」チェックボックスのオプションを選択します',
	AddClientFilesModalTitle: '{name} 用のファイルをアップロードする',
	AddClientNoteButton: 'メモを追加',
	AddClientNoteModalDescription:
		'メモにコンテンツを追加します。[表示可能] セクションを使用して、この特定のメモを表示できる 1 つ以上のグループを選択します。',
	AddClientNoteModalTitle: 'メモを追加',
	AddClientOwnerRelationshipModalDescription:
		'クライアントを招待すると、クライアントは自分のプロフィール情報を管理し、自分のプロフィール情報へのユーザー アクセスを管理できるようになります。',
	AddClientOwnerRelationshipModalTitle: 'クライアントを招待する',
	AddCode: 'コードを追加',
	AddColAfter: '列を追加',
	AddColBefore: '前に列を追加',
	AddCollection: 'コレクションを追加',
	AddColor: '色を追加する',
	AddColumn: '列を追加',
	AddContactRelationship: '連絡先の関係を追加',
	AddContacts: '連絡先を追加',
	AddCustomField: 'カスタムフィールドを追加',
	AddDate: '日付を追加',
	AddDescription: '説明を追加',
	AddDetail: '詳細を追加',
	AddDisplayName: '表示名を追加',
	AddDxCode: '診断コードを追加',
	AddEmail: 'メールを追加',
	AddFamilyClientRelationshipModalDescription:
		'家族を招待すると、ケアストーリーやクライアントのプロフィール情報を見ることができます。管理者として招待された場合は、クライアントのプロフィール情報を更新したり、ユーザー アクセスを管理したりすることができます。',
	AddFamilyClientRelationshipModalTitle: '家族を招待する',
	AddField: 'フィールドを追加',
	AddFormField: 'フォームフィールドを追加',
	AddImages: '画像を追加',
	AddInsurance: '保険を追加する',
	AddInvoice: '請求書を追加',
	AddLabel: 'ラベルを追加',
	AddLanguage: '言語を追加',
	AddLocation: '場所を追加',
	AddManually: '手動で追加',
	AddMessage: 'メッセージを追加',
	AddNewAction: '新しいアクションを追加',
	AddNewSection: '新しいセクションを追加',
	AddNote: 'メモを追加',
	AddOnlineBookingDetails: 'オンライン予約の詳細を追加する',
	AddPOS: 'POSを追加',
	AddPaidInvoices: '支払済み請求書を追加する',
	AddPayer: '支払人を追加',
	AddPayment: '支払いを追加',
	AddPaymentAdjustment: '支払い調整を追加',
	AddPaymentAdjustmentDisabledDescription: '支払いの割り当ては変更されません。',
	AddPaymentAdjustmentEnabledDescription: '割り当て可能な金額が削減されます。',
	AddPhoneNumber: '電話番号を追加',
	AddPhysicalOrVirtualLocations: '物理的な場所または仮想的な場所を追加する',
	AddQuestion: '質問を追加',
	AddQuestionOrTitle: '質問またはタイトルを追加する',
	AddRelationship: '関係を追加',
	AddRelationshipModalTitle: '既存の連絡先を接続する',
	AddRelationshipModalTitleNewClient: '新しい連絡先を接続する',
	AddRow: '行を追加する',
	AddRowAbove: '上に行を追加',
	AddRowBelow: '下に行を追加',
	AddService: 'サービスを追加',
	AddServiceLocation: 'サービス場所を追加',
	AddServiceToCollections: 'サービスをコレクションに追加',
	AddServiceToOneOrMoreCollections: 'サービスを1つ以上のコレクションに追加',
	AddServices: 'サービスを追加する',
	AddSignature: '署名を追加',
	AddSignaturePlaceholder: '署名に含める追加の詳細を入力してください',
	AddSmartDataChips: 'スマートデータチップを追加する',
	AddStaffClientRelationshipsModalDescription:
		'スタッフを選択すると、そのスタッフはこのクライアントのケアストーリーを作成および表示できるようになります。また、クライアント情報も表示できるようになります。',
	AddStaffClientRelationshipsModalTitle: 'スタッフ関係を追加する',
	AddTag: 'タグを追加する',
	AddTags: 'タグを追加する',
	AddTemplate: 'テンプレートを追加する',
	AddTimezone: 'タイムゾーンを追加',
	AddToClaim: '請求に追加',
	AddToCollection: 'コレクションに追加',
	AddToExisting: '既存のものに追加',
	AddToStarred: 'スターに追加',
	AddUnclaimedItems: '未請求アイテムを追加する',
	AddUnrelatedContactWarning:
		'{contact} の連絡先に含まれていない人を追加しました。共有する前に、コンテンツが関連していることを確認してください。',
	AddValue: '"{value}" を追加します。',
	AddVideoCall: 'ビデオ通話を追加',
	AddVideoOrVoiceCall: 'ビデオ通話または音声通話を追加する',
	AddictionCounselor: '依存症カウンセラー',
	AddingManualPayerDisclaimer:
		'支払者をプロバイダー リストに手動で追加しても、その支払者との電子請求提出接続は設定されませんが、手動で請求を作成するために使用できます。',
	AddingTeamMembersIncreaseCostAlert: '新しいチームメンバーを追加すると、毎月のサブスクリプションが増加します。',
	Additional: '追加',
	AdditionalBillingProfiles: '追加の請求プロファイル',
	AdditionalBillingProfilesSectionDescription:
		'特定のチーム メンバー、支払人、または請求書テンプレートに使用されるデフォルトの請求情報を上書きします。',
	AdditionalFeedback: '追加のフィードバック',
	AddnNewWorkspace: '新しいワークスペース',
	AddnNewWorkspaceSuccessSnackbar: 'ワークスペースが作成されました!',
	Address: '住所',
	AddressNumberStreet: '住所（番地、通り）',
	Adjustment: '調整',
	AdjustmentType: '調整タイプ',
	Admin: '管理者',
	Admins: '管理者',
	AdminsOnly: '管理者のみ',
	AdvancedPlanInclusionFive: 'アカウントマネージャー',
	AdvancedPlanInclusionFour: 'Googleアナリティクス',
	AdvancedPlanInclusionHeader: 'すべてがPlusに  ',
	AdvancedPlanInclusionOne: '役割 ',
	AdvancedPlanInclusionSix: 'データインポートのサポート',
	AdvancedPlanInclusionThree: 'ホワイトラベル',
	AdvancedPlanInclusionTwo: '削除されたデータの90日間の保持',
	AdvancedPlanMessage: '診療所のニーズを管理しましょう。現在のプランを確認し、使用状況を監視します。',
	AdvancedSettings: '高度な設定',
	AdvancedSubscriptionPlanSubtitle: 'すべての機能を活用して練習を拡張しましょう',
	AdvancedSubscriptionPlanTitle: '高度な',
	AdvertisingManager: '宣伝部長',
	AerospaceEngineer: '航空宇宙エンジニア',
	AgeYearsOld: '{age} 歳',
	Agenda: '議題',
	AgendaView: '議題ビュー',
	AiAskSupportedFileTypes: 'サポートされているファイル形式: JPEG、PNG、PDF、Word',
	AiAssistantAtYourFingertips: '指先にあるアシスタント',
	AiCopilotDisclaimer: 'AI Copilot は間違いを犯す可能性があります。重要な情報を確認してください。',
	AiCreateNewConversation: '新しい会話を作成する',
	AiEnhanceYourProductivity: '生産性を高めましょう',
	AiPoweredTemplates: 'AI搭載テンプレート',
	AiScribeNoDeviceFoundErrorMessage:
		'お使いのブラウザはこの機能をサポートしていないか、互換性のあるデバイスが利用できないようです。',
	AiScribeUploadFormat: 'サポートされているファイル形式: MP3、WAV、MP4',
	AiScribeUploadSizeLimit: '一度に1ファイルのみ',
	AiShowConversationHistory: '会話履歴を表示する',
	AiSmartPromptNodePlaceholderText:
		'正確でパーソナライズされたAI結果を生成するために、ここでカスタムプロンプトを入力してください。',
	AiSmartPromptPrimaryText: 'AI スマートプロンプト',
	AiSmartPromptSecondaryText: 'カスタムAIスマートプロンプトを挿入する',
	AiSmartReminders: 'AIスマートリマインダー',
	AiTemplateBannerTitle: 'AIを活用したテンプレートで作業を簡素化',
	AiTemplates: 'AIテンプレート',
	AiTokens: 'AIトークン',
	AiWorkBetterWithAi: 'AI でより良く働く',
	All: '全て',
	AllAppointments: 'すべての予定',
	AllCategories: 'すべてのカテゴリ',
	AllClients: 'すべてのクライアント',
	AllContactPolicySelectorLabel: '<mark>{client}</mark> のすべての連絡先',
	AllContacts: 'すべての連絡先',
	AllContactsOf: `'{name}' のすべての連絡先`,
	AllDay: '一日中',
	AllInboxes: 'すべての受信トレイ',
	AllIndustries: '全産業',
	AllLocations: 'すべての場所',
	AllMeetings: 'すべての会議',
	AllNotificationsRestoredMessage: 'すべての通知が復元されました',
	AllProfessions: 'すべての職業',
	AllReminders: 'すべてのリマインダー',
	AllServices: 'すべてのサービス',
	AllStatuses: 'すべてのステータス',
	AllTags: 'すべてのタグ',
	AllTasks: 'すべてのタスク',
	AllTeamMembers: 'チームメンバー全員',
	AllTypes: 'いろんなタイプ',
	Allocated: '割り当て済み',
	AllocatedItems: '割り当てられたアイテム',
	AllocationTableEmptyState: '支払い割り当てが見つかりません',
	AllocationTotalWarningMessage: `割り当てられた金額が合計支払額を超えています。
以下の項目を確認してください。`,
	AllowClientsToCancelAnytime: 'クライアントがいつでもキャンセルできるようにする',
	AllowNewClient: '新規クライアントを許可する',
	AllowNewClientHelper: '新規のお客様はこのサービスを予約できます',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: '予約の{hours}時間前までに余裕を持ってください。',
	AllowToUseSavedCard: '次回、{provider}が保存したカードを使用できるようにする',
	AllowVideoCalls: 'ビデオ通話を許可する',
	AlreadyAdded: '既に追加されました',
	AlreadyHasAccess: 'アクセス権あり',
	AlreadyHasAccount: 'すでにアカウントをお持ちですか？',
	Always: '常に',
	AlwaysIgnore: '常に無視',
	Amount: '額',
	AmountDue: '支払金額',
	AmountOfReferralRequests: '{amount, plural, one {# 紹介リクエスト} other {# 紹介リクエスト}}',
	AmountPaid: '支払金額',
	AnalyzingAudio: 'オーディオを分析しています...',
	AnalyzingInputContent: '入力コンテンツを分析しています...',
	AnalyzingRequest: 'リクエストを分析しています...',
	AnalyzingTemplateContent: 'テンプレートのコンテンツを分析しています...',
	And: 'そして',
	Annually: '毎年',
	Anonymous: '匿名',
	AnswerExceeded: '回答は 300 文字未満でなければなりません。',
	AnyStatus: 'あらゆるステータス',
	AppNotifications: '通知',
	AppNotificationsClearanceHeading: 'よくできました！すべてのアクティビティをクリアしました',
	AppNotificationsEmptyHeading: 'ワークスペースのアクティビティがすぐにここに表示されます',
	AppNotificationsEmptySubtext: '現時点では何も対策はありません',
	AppNotificationsIgnoredCount: '{total} 無視',
	AppNotificationsUnread: '{total} 未読',
	Append: '追加',
	Apply: '適用する',
	ApplyAccountCredit: 'アカウントクレジットを適用する',
	ApplyDiscount: '割引を適用する',
	ApplyVisualEffects: '視覚効果を適用する',
	ApplyVisualEffectsNotSupported: '視覚効果の適用はサポートされていません',
	Appointment: '予定',
	AppointmentAssignedNotificationSubject: '{actorProfileName} は {appointmentName} をあなたに割り当てました。',
	AppointmentCancelledNotificationSubject: '{actorProfileName} は {appointmentName} をキャンセルしました。',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} は {appointmentName} を確認しました。',
	AppointmentDetails: '予約の詳細',
	AppointmentLocation: '予約場所',
	AppointmentLocationDescription:
		'既定の仮想および物理的な場所を管理します。予約がスケジュールされると、これらの場所は自動的に適用されます。',
	AppointmentNotFound: '予約が見つかりません',
	AppointmentReminder: '予約リマインダー',
	AppointmentReminders: '予約リマインダー',
	AppointmentRemindersInfo: '顧客との予約に自動リマインダーを設定して、無断キャンセルやキャンセルを回避します。',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} は {appointmentName} を再スケジュールしました。',
	AppointmentSaved: '予約を保存しました',
	AppointmentStatus: '予約状況',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0分} other {{minutes}分}} } one {{hours}時間 {minutes, plural, =0 {} other {{minutes}分}}} other {{hours}時間 {minutes, plural, =0 {} other {{minutes}分}}} }',
	AppointmentUndone: '予約を取り消しました',
	Appointments: '予定',
	Archive: 'アーカイブ',
	ArchiveClients: 'アーカイブクライアント',
	Archived: 'アーカイブ済み',
	AreYouAClient: 'あなたはクライアントですか?',
	AreYouStillThere: 'まだそこにいますか？',
	AreYouSure: '本気ですか？',
	Arrangements: '段取り',
	ArtTherapist: 'アートセラピスト',
	Articles: '記事',
	Artist: 'アーティスト',
	AskAI: 'AIに聞く',
	AskAiAddFormField: 'フォームフィールドを追加する',
	AskAiChangeFormality: 'Change formality',
	AskAiChangeToneToBeMoreProfessional: 'よりプロフェッショナルな口調に変えましょう',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'この文書の内容を説明する',
	AskAiExplainWhatThisImageIsAbout: 'この画像が何であるか説明してください',
	AskAiFixSpellingAndGrammar: 'スペルと文法を修正する',
	AskAiGenerateACaptionForThisImage: 'この画像のキャプションを生成する',
	AskAiGenerateFromThisPage: 'Generate from this page',
	AskAiGetStarted: 'Get started',
	AskAiGiveItAFriendlyTone: 'Give it a friendly tone',
	AskAiGreeting: 'こんにちは {firstName}さん！今日は何かお困りですか？',
	AskAiHowCanIHelpWithYourContent: 'あなたのコンテンツに関してどのようにお手伝いできますか?',
	AskAiInsert: '入れる',
	AskAiMakeItMoreCasual: 'Make it more casual',
	AskAiMakeThisTextMoreConcise: 'このテキストをもっと簡潔にする',
	AskAiMoreProfessional: 'More professional',
	AskAiOpenPreviousNote: '前のメモを開く',
	AskAiPondering: '熟考する',
	AskAiReplace: '交換する',
	AskAiReviewOrEditSelection: 'Review or edit selection',
	AskAiRuminating: '反芻する',
	AskAiSeeMore: 'もっと見る',
	AskAiSimplifyLanguage: 'Simplify language',
	AskAiSomethingWentWrong:
		'何か問題が発生しました。この問題が解決しない場合は、ヘルプセンターからお問い合わせください。',
	AskAiStartWithATemplate: 'テンプレートから始める',
	AskAiSuccessfullyCopiedResponse: 'AI応答を正常にコピーしました',
	AskAiSuccessfullyInsertedResponse: 'AI応答が正常に挿入されました',
	AskAiSuccessfullyReplacedResponse: 'AI応答の置き換えに成功しました',
	AskAiSuggested: '提案',
	AskAiSummariseTextIntoBulletPoints: '文章を箇条書きで要約する',
	AskAiSummarizeNote: 'Summarize note',
	AskAiThinking: '考え',
	AskAiToday: '本日 {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'このフォームで何をしたいですか?',
	AskAiWriteProfessionalNoteUsingTemplate: 'テンプレートを使用してプロフェッショナルなメモを書く',
	AskAskAiAnything: 'AIに何でも聞いてください',
	AskWriteSearchAnything: '質問したり、「@」を書いたり、何でも検索したりしてください...',
	Asking: '尋ねる',
	Assessment: '評価',
	Assessments: '評価',
	AssessmentsCategoryDescription: 'クライアント評価の記録用',
	AssignClients: 'クライアントを割り当てる',
	AssignNewClients: 'クライアントを割り当てる',
	AssignServices: 'サービスの割り当て',
	AssignTeam: 'チームの割り当て',
	AssignTeamMember: 'チームメンバーの割り当て',
	Assigned: '割り当て済み',
	AssignedClients: '割り当てられたクライアント',
	AssignedServices: '割り当てられたサービス',
	AssignedServicesDescription:
		'割り当てられたサービスを表示および管理し、カスタム料金を反映するように価格を調整します。 ',
	AssignedTeam: '割り当てられたチーム',
	AthleticTrainer: 'アスレチックトレーナー',
	AttachFiles: 'ファイルを添付する',
	AttachLogo: '添付',
	Attachment: '添付ファイル',
	AttachmentBlockedFileType: 'セキュリティ上の理由によりブロックされました!',
	AttachmentTooLargeFileSize: 'あまりにも大きなファイル',
	AttachmentUploadItemComplete: '完了',
	AttachmentUploadItemError: 'アップロードに失敗しました',
	AttachmentUploadItemLoading: '読み込み中',
	AttemptingToReconnect: '再接続を試みています...',
	Attended: '出席した',
	AttendeeBeingMutedTooltip:
		'主催者があなたをミュートしました。ミュート解除をリクエストするには「挙手」を使用してください',
	AttendeeWithId: '参加者 {attendeeId}',
	Attendees: '出席者',
	AttendeesCount: '{count} 名の参加者',
	Attending: '出席する',
	Audiologist: '聴覚学者',
	Aunt: '叔母',
	Australia: 'オーストラリア',
	AuthenticationCode: '認証コード',
	AuthoriseProvider: '{provider} を承認する',
	AuthorisedProviders: '認定プロバイダー',
	AutoDeclineAllFutureOption: '新規のイベントまたは予約のみ',
	AutoDeclineAllOption: '新規および既存のイベントまたは予約',
	AutoDeclinePrimaryText: '自動的にイベントを拒否する',
	AutoDeclineSecondaryText: '不在期間中のイベントは自動的に辞退されます。',
	AutogenerateBillings: '請求書の自動生成',
	AutogenerateBillingsDescription:
		'自動請求書は月末に生成されます。請求書とスーパービル領収書はいつでも手動で作成できます。',
	AutomateWorkflows: 'ワークフローの自動化',
	AutomaticallySendSuperbill: 'スーパービルの領収書を自動的に送信する',
	AutomaticallySendSuperbillHelperText:
		'スーパービルとは、保険金の払い戻しのために顧客に提供されたサービスの詳細な領収書です。',
	Automation: 'オートメーション',
	AutomationActionSendEmailLabel: 'メールを送信',
	AutomationActionSendSMSLabel: 'SMSを送信',
	AutomationAndReminders: 'オートメーション ',
	AutomationDeletedSuccessMessage: '自動化が正常に削除されました',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: '時間イベント',
	AutomationParams_timeUnit: 'ユニット',
	AutomationParams_timeValue: '番号',
	AutomationPublishSuccessMessage: '自動化が正常に公開されました',
	AutomationPublishWarningTooltip: '自動化設定を再度確認し、正しく設定されていることを確認してください',
	AutomationTriggerEventCancelledDescription: 'イベントがキャンセルまたは削除されたときにトリガーされます',
	AutomationTriggerEventCancelledLabel: 'イベントはキャンセルされました',
	AutomationTriggerEventCreatedDescription: 'イベントが作成されたときにトリガーされます',
	AutomationTriggerEventCreatedLabel: '新しいイベント',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'イベントが作成または更新されたときにトリガーされます（キャンセルされた場合を除く）',
	AutomationTriggerEventCreatedOrUpdatedLabel: '新規または更新されたイベント',
	AutomationTriggerEventEndedDescription: 'イベント終了時にトリガーされます',
	AutomationTriggerEventEndedLabel: 'イベント終了',
	AutomationTriggerEventStartsDescription: 'イベント開始前に指定された時間が経過するとトリガーされます',
	AutomationTriggerEventStartsLabel: 'イベント開始',
	Automations: '自動化',
	Availability: '可用性',
	AvailabilityDisableSchedule: 'スケジュールを無効にする',
	AvailabilityDisabled: '無効',
	AvailabilityEnableSchedule: 'スケジュールを有効にする',
	AvailabilityEnabled: '有効',
	AvailabilityNoActiveBanner:
		'すべてのスケジュールがオフになっています。クライアントはオンラインで予約することができず、今後の予約はすべて手動で確認する必要があります。',
	AvailabilityNoActiveConfirmationDescription:
		'この空き状況を無効にすると、アクティブなスケジュールがなくなります。クライアントはオンラインで予約できなくなり、施術者による予約はあなたの勤務時間外になります。',
	AvailabilityNoActiveConfirmationProceed: 'はい、続行します',
	AvailabilityNoActiveConfirmationTitle: 'アクティブなスケジュールはありません',
	AvailabilityToggle: 'スケジュールが有効',
	AvailabilityUnsetDate: '日付未定',
	AvailableLocations: '利用可能な場所',
	AvailablePayers: '利用可能な支払者',
	AvailablePayersEmptyState: '支払者を選択していません。',
	AvailableTimes: '利用可能な時間',
	Back: '戻る',
	BackHome: '家に帰って',
	BackToAppointment: '予約に戻る',
	BackToLogin: 'ログイン画面に戻る',
	BackToMapColumns: 'マップ列に戻る',
	BackToTemplates: 'テンプレートに戻る',
	BackToUploadFile: 'ファイルのアップロードに戻る',
	Banker: 'バンカー',
	BasicBlocks: '基本ブロック',
	BeforeAppointment: '予約の {interval} {unit} 前に{deliveryType} リマインダーを送信する',
	BehavioralAnalyst: '行動分析家',
	BehavioralHealthTherapy: '行動健康療法',
	Beta: 'ベータ',
	BillTo: '請求先',
	BillableItems: '請求対象項目',
	BillableItemsEmptyState: '請求可能な項目が見つかりませんでした',
	Biller: 'ビラー',
	Billing: '請求する',
	BillingAddress: '請求先住所',
	BillingAndReceiptsUnauthorisedMessage: 'この情報にアクセスするには、請求書と支払いの表示アクセス権が必要です。',
	BillingBillablesTab: '請求可能額',
	BillingClaimsTab: 'クレーム',
	BillingDetails: '請求の詳細',
	BillingDocuments: '請求書類',
	BillingDocumentsClaimsTab: '請求',
	BillingDocumentsEmptyState: '{tabType}が見つかりません',
	BillingDocumentsInvoicesTab: '請求書',
	BillingDocumentsSuperbillsTab: 'スーパービル',
	BillingInformation: '請求情報',
	BillingInvoicesTab: '請求書',
	BillingItems: '請求項目',
	BillingPaymentsTab: '支払い',
	BillingPeriod: '支払請求周期',
	BillingProfile: '請求プロファイル',
	BillingProfileOverridesDescription: 'この請求プロファイルの使用を特定のチームメンバーに制限する',
	BillingProfileOverridesHeader: 'アクセス制限',
	BillingProfileProviderType: 'プロバイダー タイプ',
	BillingProfileTypeIndividual: '開業医',
	BillingProfileTypeIndividualSubLabel: 'タイプ 1 NPI',
	BillingProfileTypeOrganisation: '組織',
	BillingProfileTypeOrganisationSubLabel: 'タイプ 2 NPI',
	BillingProfiles: '「請求プロファイル」',
	BillingProfilesEditHeader: '{name} の請求プロファイルを編集',
	BillingProfilesNewHeader: '新しい請求プロファイル',
	BillingProfilesSectionDescription:
		'請求書や保険金支払いに適用できる請求プロファイルを設定して、医療従事者や保険支払者に対する請求情報を管理します。',
	BillingSearchPlaceholder: '検索項目',
	BillingSettings: '課金設定',
	BillingSuperbillsTab: 'スーパービル',
	BiomedicalEngineer: 'バイオメディカルエンジニア',
	BlankInvoice: '空白の請求書',
	BlueShieldProviderNumber: 'ブルーシールドプロバイダー番号',
	Body: '体',
	Bold: '大胆な',
	BookAgain: '再度予約する',
	BookAppointment: '予約する',
	BookableOnline: 'オンラインで予約可能',
	BookableOnlineHelper: 'クライアントはこのサービスをオンラインで予約できます',
	BookedOnline: 'オンライン予約済み',
	Booking: '予約',
	BookingAnalyticsIntegrationPanelDescription:
		'Google タグ マネージャーを設定して、オンライン予約フローにおける主要なアクションとコンバージョンを追跡します。ユーザー インタラクションに関する貴重なデータを収集して、マーケティング活動を改善し、予約エクスペリエンスを最適化します。',
	BookingAnalyticsIntegrationPanelTitle: '分析統合',
	BookingAndCancellationPolicies: '予約 ',
	BookingButtonEmbed: 'ボタン',
	BookingButtonEmbedDescription: 'ウェブサイトにオンライン予約ボタンを追加します',
	BookingDirectTextLink: '直接テキストリンク',
	BookingDirectTextLinkDescription: 'オンライン予約ページを開きます',
	BookingFormatLink: 'フォーマットリンク',
	BookingFormatLinkButtonTitle: 'ボタンタイトル',
	BookingInlineEmbed: 'インライン埋め込み',
	BookingInlineEmbedDescription: 'オンライン予約ページをウェブサイトに直接読み込みます',
	BookingLink: '予約リンク',
	BookingLinkModalCopyText: 'コピー',
	BookingLinkModalDescription: 'このリンクを持つクライアントがチームメンバーやサービスを予約できるようにします',
	BookingLinkModalHelpText: 'オンライン予約の設定方法を学ぶ',
	BookingLinkModalTitle: '予約リンクを共有する',
	BookingPolicies: '予約ポリシー',
	BookingPoliciesDescription: '顧客がオンライン予約を行える時間を設定する',
	BookingTimeUnitDays: '日々',
	BookingTimeUnitHours: '時間',
	BookingTimeUnitMinutes: '分',
	BookingTimeUnitMonths: 'ヶ月',
	BookingTimeUnitWeeks: '週間',
	BottomNavBilling: '請求する',
	BottomNavGettingStarted: '家',
	BottomNavMore: 'もっと',
	BottomNavNotes: 'ノート',
	Brands: 'ブランド',
	Brother: '兄弟',
	BrotherInLaw: '義理の兄',
	BrowseOrDragFileHere: '<link>参照</link> またはファイルをここにドラッグしてください',
	BrowseOrDragFileHereDescription: 'PNG、JPG ({limit} まで)',
	BufferAfterTime: '{time} 分後',
	BufferAndLabel: 'そして',
	BufferAppointmentLabel: '約束',
	BufferBeforeTime: '{time} 分前',
	BufferTime: 'バッファ時間',
	BufferTimeViewLabel: '{bufferBefore} 分前に、{bufferAfter} 分後に予約',
	BulkArchiveClientsDescription:
		'これらのクライアントをアーカイブしてもよろしいですか? 後で再度アクティブ化できます。',
	BulkArchiveSuccess: 'クライアントを正常にアーカイブしました',
	BulkArchiveUndone: '一括アーカイブを取り消しました',
	BulkPermanentDeleteDescription: 'これは **{count} 件の会話** を削除します。この操作は取り消せません。',
	BulkPermanentDeleteTitle: '会話を完全に削除する',
	BulkUnarchiveSuccess: 'クライアントのアーカイブ解除に成功しました',
	BulletedList: '箇条書きリスト',
	BusinessAddress: 'ビジネスの住所',
	BusinessAddressOptional: 'ビジネスの住所<span>(オプション)</span>',
	BusinessName: '商号',
	Button: 'ボタン',
	By: 'による',
	CHAMPUSIdentificationNumber: 'CHAMPUS識別番号',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVCが必要です',
	Calendar: 'カレンダー',
	CalendarAppSyncFormDescription: 'Carepatronイベントを同期する',
	CalendarAppSyncPanelTitle: '接続されたアプリの同期',
	CalendarDescription: '予定を管理したり、個人的なタスクやリマインダーを設定したりします',
	CalendarDetails: 'カレンダーの詳細',
	CalendarDetailsDescription: 'カレンダーと予定の表示設定を管理します。',
	CalendarScheduleNew: '新しいスケジュール',
	CalendarSettings: 'カレンダー設定',
	Call: '通話',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong>がビデオ通話に参加しました',
	CallChangeLayoutTextContent: '選択は今後のミーティングのために保存されます',
	CallIdlePrompt: '参加するまでお待ちいただくか、後でもう一度お試しください。',
	CallLayoutOptionAuto: '自動',
	CallLayoutOptionSidebar: 'サイドバー',
	CallLayoutOptionSpotlight: 'スポットライト',
	CallLayoutOptionTiled: 'タイル',
	CallNoAttendees: '会議に出席者がいません。',
	CallSessionExpiredError: 'セッションの有効期限が切れました。通話は終了しました。もう一度参加してください。',
	CallWithPractitioner: '{practitioner} との通話',
	CallsListCreateButton: '新しい呼び出し',
	CallsListEmptyState: 'アクティブな通話はありません',
	CallsListItemEndCall: '通話終了',
	CamWarningMessage: 'カメラに問題が検出されました',
	Camera: 'カメラ',
	CameraAndMicIssueModalDescription: `カメラとマイクへのCarepatronアクセスを有効にしてください。
詳細については、<a>このガイドをご覧ください。</a>`,
	CameraAndMicIssueModalTitle: 'カメラとマイクがブロックされています',
	CameraQuality: 'カメラの品質',
	CameraSource: 'カメラソース',
	CanModifyReadOnlyEvent: 'このイベントは変更できません。',
	Canada: 'カナダ',
	Cancel: 'キャンセル',
	CancelClientImportDescription: '本当にこのインポートをキャンセルしますか？',
	CancelClientImportPrimaryAction: 'はい、インポートをキャンセル',
	CancelClientImportSecondaryAction: '編集を続ける',
	CancelClientImportTitle: 'インポート処理をキャンセル',
	CancelImportButton: 'インポートをキャンセル',
	CancelPlan: 'プランをキャンセル',
	CancelPlanConfirmation: `プランをキャンセルすると、その月の未払い残高が自動的にアカウントに請求されます。
課金対象ユーザーをダウングレードしたい場合は、チーム メンバーを削除するだけで、Carepatron がサブスクリプション価格を自動的に更新します。`,
	CancelSend: '送信をキャンセル',
	CancelSubscription: 'サブスクリプションをキャンセルする',
	Canceled: 'キャンセル済み',
	CancellationPolicy: '取り消し規約',
	Cancelled: 'キャンセル',
	CannotContainSpecialCharactersError: '{specialCharacters} を含めることができません。',
	CannotDeleteInvoice: 'オンライン決済で支払われた請求書は削除できません',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> は <b>{group}</b> グループから移動できません',
	CannotMoveServiceOutsideCollections: 'サービスをコレクション外に移動することはできない',
	CapeTown: 'ケープタウン',
	Caption: 'キャプション',
	CaptureNameFieldLabel: '他の人に呼んでもらいたい名前',
	CapturePaymentMethod: '支払い方法の取得',
	CapturingAudio: 'オーディオのキャプチャ',
	CapturingSignature: '署名をキャプチャしています...',
	CardInformation: 'カード情報',
	CardNumberRequired: 'カード番号が必要です',
	CardiacRehabilitationSpecialist: '心臓リハビリテーション専門医',
	Cardiologist: '心臓専門医',
	CareAiNoConversations: 'まだ会話がありません。',
	CareAiNoConversationsDescription: '{aiName} との会話を開始して始めましょう。',
	CareAssistant: '介護アシスタント',
	CareManager: 'ケアマネージャー',
	Caregiver: '介護者',
	CaregiverCreateModalDescription:
		'スタッフを管理者として追加すると、ケアストーリーの作成と管理が可能になります。また、クライアントの作成と管理に対する完全なアクセス権も付与されます。',
	CaregiverCreateModalTitle: '新しいチームメンバー',
	CaregiverListCantAddStaffInfoTitle:
		'サブスクリプションのスタッフ数の上限に達しました。スタッフを追加するにはプランをアップグレードしてください。',
	CaregiverListCreateButton: '新しいチームメンバー',
	CaregiverListEmptyState: '介護者を追加していません',
	CaregiversListItemRemoveStaff: 'スタッフを削除',
	CarepatronApp: 'ケアパトロンアプリ',
	CarepatronCommunity: 'コミュニティ',
	CarepatronFieldAddress: '住所',
	CarepatronFieldAssignedStaff: '割り当てられたスタッフ',
	CarepatronFieldBirthDate: '生年月日',
	CarepatronFieldEmail: 'Eメール',
	CarepatronFieldEmploymentStatus: '雇用状況',
	CarepatronFieldEthnicity: '民族',
	CarepatronFieldFirstName: 'ファーストネーム',
	CarepatronFieldGender: '性別',
	CarepatronFieldIdentificationNumber: '識別番号',
	CarepatronFieldIsArchived: '状態',
	CarepatronFieldLabel: 'ラベル',
	CarepatronFieldLastName: '苗字',
	CarepatronFieldLivingArrangements: '居住環境',
	CarepatronFieldMiddleNames: 'ミドルネーム',
	CarepatronFieldOccupation: '職業',
	CarepatronFieldPhoneNumber: '電話番号',
	CarepatronFieldRelationshipStatus: '関係ステータス',
	CarepatronFieldStatus: '状態',
	CarepatronFieldStatusHelperText: 'ステータスは最大10個。',
	CarepatronFieldTags: 'タグ',
	CarepatronFields: 'ケアパトロンフィールド',
	Cash: '現金',
	Category: 'カテゴリー',
	CategoryInputPlaceholder: 'テンプレートのカテゴリを選択',
	CenterAlign: '中央揃え',
	Central: '中央',
	ChangeLayout: 'レイアウトを変更する',
	ChangeLogo: '変化',
	ChangePassword: 'パスワードを変更する',
	ChangePasswordFailureSnackbar:
		'申し訳ございませんが、パスワードは変更されませんでした。古いパスワードが正しいかどうか確認してください。',
	ChangePasswordHelperInfo: '最小長さは {minLength} です。',
	ChangePasswordSuccessfulSnackbar:
		'パスワードの変更に成功しました。次回ログインするときは、必ずそのパスワードを使用してください。',
	ChangeSubscription: '購読を変更する',
	ChangesNotAllowed: 'このフィールドは変更できません',
	ChargesDisabled: '請求が無効になりました',
	ChargesEnabled: '課金が有効',
	ChargesStatus: '請求状況',
	ChartAndDiagram: 'チャート/ダイアグラム',
	ChartsAndDiagramsCategoryDescription: 'クライアントデータと進捗を説明するために',
	ChatEditMessage: 'メッセージを編集する',
	ChatReplyTo: '{name} さんへの返信',
	ChatTypeMessageTo: 'メッセージ {name}',
	Check: 'チェック',
	CheckList: 'チェックリスト',
	Chef: 'シェフ',
	Chiropractic: 'カイロプラクティック',
	Chiropractor: 'カイロプラクター',
	Chiropractors: 'カイロプラクター',
	ChooseACollection: 'コレクションを選択してください。',
	ChooseAContact: '連絡先を選択',
	ChooseAccountTypeHeader: 'あなたに一番当てはまるものはどれですか?',
	ChooseAction: 'アクションを選択',
	ChooseAnAccount: 'アカウントを選択',
	ChooseAnOption: 'オプションを選択',
	ChooseBillingProfile: '請求プロファイルを選択',
	ChooseClaim: '請求を選択',
	ChooseCollection: 'コレクションを選択',
	ChooseColor: '色を選択',
	ChooseCustomDate: 'カスタム日付を選択',
	ChooseDateAndTime: '日付と時間を選択',
	ChooseDxCodes: '診断コードを選択',
	ChooseEventType: 'イベントの種類を選択',
	ChooseFileButton: 'ファイルを選択',
	ChooseFolder: 'フォルダを選択してください',
	ChooseInbox: '受信トレイを選択',
	ChooseMethod: '方法を選択',
	ChooseNewOwner: '新しい所有者を選択',
	ChooseOrganization: '組織を選択',
	ChoosePassword: 'パスワードを選ぶ',
	ChoosePayer: '支払人を選択',
	ChoosePaymentMethod: 'お支払い方法を選択してください',
	ChoosePhysicalOrRemoteLocations: '場所を入力または選択してください',
	ChoosePlan: '{plan} を選んでください',
	ChooseProfessional: 'プロフェッショナルを選択',
	ChooseServices: 'サービスを選択',
	ChooseSource: 'ソースを選択する',
	ChooseSourceDescription:
		'クライアントのインポート元を選択してください - ファイルか別のソフトウェアプラットフォームかを指定してください。',
	ChooseTags: 'タグを選択',
	ChooseTaxName: '税金名を選択',
	ChooseTeamMembers: 'チームメンバーを選びます',
	ChooseTheme: 'テーマを選択する',
	ChooseTrigger: 'トリガーを選択',
	ChooseYourProvider: 'プロバイダーを選択してください',
	CircularProgressWithLabel: '{value}%',
	City: '市',
	CivilEngineer: '土木技師',
	Claim: '請求',
	ClaimAddReferringProvider: '紹介医療機関を追加する',
	ClaimAddRenderingProvider: 'レンダリングプロバイダを追加',
	ClaimAmount: '請求金額',
	ClaimAmountPaidHelpContent:
		'支払額は患者または他の支払者から受け取った支払いです。対象サービスに対して患者または他の支払者が支払った合計金額のみを入力します。',
	ClaimAmountPaidHelpSubtitle: 'フィールド29',
	ClaimAmountPaidHelpTitle: '支払金額',
	ClaimBillingProfileTypeIndividual: '個人',
	ClaimBillingProfileTypeOrganisation: '組織',
	ClaimChooseRenderingProviderOrTeamMember: 'レンダリングプロバイダーまたはチームメンバーを選択',
	ClaimClientInsurancePolicies: '顧客の保険契約',
	ClaimCreatedAction: '<mark>請求 {claimNumber}</mark> 作成',
	ClaimDeniedAction: '<mark>請求 {claimNumber}</mark> は <b>{payerNumber} {payerName}</b> によって拒否されました。',
	ClaimDiagnosisCodeSelectorPlaceholder: 'ICD 10診断コードを検索',
	ClaimDiagnosisSelectorHelpContent: `「診断または傷害」とは、請求書に記載されたサービスに関連する患者の兆候、症状、苦情、または状態を指します。
最大 12 個の ICD 10 診断コードを選択できます。`,
	ClaimDiagnosisSelectorHelpSubtitle: 'フィールド21',
	ClaimDiagnosisSelectorHelpTitle: '診断または傷害',
	ClaimDiagnosticCodesEmptyError: '少なくとも1つの診断コードが必要です。',
	ClaimDoIncludeReferrerInformation: 'CMS1500にリファラー情報を含める',
	ClaimERAReceivedAction: '<b>{payerNumber} {payerName}</b>から電子送金を受け取りました。',
	ClaimElectronicPaymentAction:
		'電子送金を受信しました	<mark>支払 {paymentReference}</mark> を <b>{paymentAmount}</b> について、<b>{payerNumber} {payerName}</b> からの支払いが記録されました',
	ClaimExportedAction: '<mark>請求 {claimNumber}</mark> は <b>{attachmentType}</b> としてエクスポートされました。',
	ClaimFieldClient: '顧客または連絡先名',
	ClaimFieldClientAddress: 'クライアントアドレス',
	ClaimFieldClientAddressDescription:
		'クライアントの住所を入力します。最初の行は番地です。住所には句読点 (カンマやピリオド) や記号を使用しないでください。外国の住所を報告する場合は、支払者に連絡して具体的な報告手順を確認してください。',
	ClaimFieldClientAddressSubtitle: 'フィールド5',
	ClaimFieldClientDateOfBirth: '顧客の生年月日',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'フィールド3',
	ClaimFieldClientDateOfBirthDescription:
		'クライアントの 8 桁の生年月日 (MM/DD/YYYY) を入力します。クライアントの生年月日は、クライアントを識別する情報であり、類似した名前を持つ人々を区別します。',
	ClaimFieldClientDescription: '「クライアント名」とは、治療や物資の提供を受けた人の名前です。',
	ClaimFieldClientSexDescription: '「性別」はクライアントを識別する情報であり、似た名前を持つ人々を区別します。',
	ClaimFieldClientSubtitle: 'フィールド2',
	ClaimFiling: '請求の提出',
	ClaimHistorySubtitle: '保険 • クレーム {number}',
	ClaimIncidentAutoAccident: '自動車事故？',
	ClaimIncidentConditionRelatedTo: 'クライアントの状態は',
	ClaimIncidentConditionRelatedToHelpContent:
		'この情報は、クライアントの病気や怪我が雇用、自動車事故、またはその他の事故に関連しているかどうかを示します。雇用 (現在または過去) は、症状がクライアントの仕事または職場に関連していることを示します。自動車事故は、症状が自動車事故の結果であることを示します。その他の事故は、症状がその他の種類の事故の結果であることを示します。',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'フィールド 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'クライアントの状態は',
	ClaimIncidentCurrentIllness: '現在の病気、怪我、妊娠',
	ClaimIncidentCurrentIllnessHelpContent:
		'現在の病気、怪我、または妊娠の日付は、病気の発症日、実際の怪我の日付、または妊娠の最終出産予定日を特定します。',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'フィールド14',
	ClaimIncidentCurrentIllnessHelpTitle: '現在の病気、怪我、または妊娠の日付（LMP）',
	ClaimIncidentDate: '日付',
	ClaimIncidentDateFrom: '日付から',
	ClaimIncidentDateTo: '日付',
	ClaimIncidentEmploymentRelated: '雇用',
	ClaimIncidentEmploymentRelatedDesc: '(現在または過去)',
	ClaimIncidentHospitalizationDatesLabel: '現在のサービスに関連する入院日',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'現在のサービスに関連する入院日は、クライアントの滞在を指し、請求書のサービスに関連する入院日と退院日を示します。',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'フィールド18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: '現在のサービスに関連する入院日',
	ClaimIncidentInformation: '事件情報',
	ClaimIncidentOtherAccident: 'その他の事故ですか?',
	ClaimIncidentOtherAssociatedDate: 'その他の関連日付',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'その他の日付は、クライアントの状態または治療に関する追加の日付情報を識別します。',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'フィールド15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'その他の日付',
	ClaimIncidentQualifier: '予選',
	ClaimIncidentQualifierPlaceholder: '修飾語を選択',
	ClaimIncidentUnableToWorkDatesLabel: 'クライアントは現在の職業に就くことができなかった',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'クライアントが現在の職業で働けなかった日付は、クライアントが働けなかった、または働けなかった期間です。',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'フィールド16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'クライアントが現在の職業に就くことができなかった日付',
	ClaimIncludeReferrerInformation: 'CMS1500にリファラー情報を含める',
	ClaimInsuranceCoverageTypeHelpContent: `この請求に適用される健康保険の種類。その他は、HMO、商業保険、自動車事故、賠償責任、または労働者災害補償を含む健康保険を示します。
この情報により、請求が適切なプログラムに誘導され、主な責任が確立される可能性があります。`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'フィールド 1',
	ClaimInsuranceCoverageTypeHelpTitle: '補償タイプ',
	ClaimInsuranceGroupIdHelpContent: `被保険者の健康保険証に記載されている保険証番号またはグループ番号を入力します。

 「被保険者の保険証書、グループ、または FECA 番号」は、健康保険、自動車保険、またはその他の保険プランの補償範囲を表す英数字の識別子です。FECA 番号は、仕事関連の症状を訴える患者に割り当てられる 9 文字の英数字の識別子です。`,
	ClaimInsuranceGroupIdHelpSubtitle: 'フィールド11',
	ClaimInsuranceGroupIdHelpTitle: '被保険者の保険証券、グループ、または FECA 番号',
	ClaimInsuranceMemberIdHelpContent: `請求が提出される支払人の被保険者の ID カードに記載されている被保険者の ID 番号を入力します。
患者に支払者によって割り当てられた固有の会員識別番号がある場合は、このフィールドにその番号を入力します。`,
	ClaimInsuranceMemberIdHelpSubtitle: 'フィールド 1a',
	ClaimInsuranceMemberIdHelpTitle: '被保険者の会員ID',
	ClaimInsurancePayer: '保険支払者',
	ClaimManualPaymentAction: '<mark>支払 {paymentReference}</mark> の <b>{paymentAmount}</b> が記録されました。',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: '追加の請求情報',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'このフィールドの使用に関しては、公的または私的支払者からの現在の指示を参照してください。入力される情報に対して、該当する修飾子がある場合は、それを報告してください。修飾子と情報の間には、スペース、ハイフン、またはその他の区切り文字を入力しないでください。',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'フィールド 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: '追加の請求情報',
	ClaimMiscClaimCodes: 'クレームコード',
	ClaimMiscOriginalReferenceNumber: '元の参照番号',
	ClaimMiscPatientsAccountNumber: '患者の口座番号',
	ClaimMiscPatientsAccountNumberHelpContent: '患者のアカウント番号は、プロバイダーによって割り当てられた識別子です。',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'フィールド26',
	ClaimMiscPatientsAccountNumberHelpTitle: '患者の口座番号',
	ClaimMiscPriorAuthorizationNumber: '事前承認番号',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'事前承認番号は、サービスに承認を与える支払者によって割り当てられた番号です。',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'フィールド23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: '事前承認番号',
	ClaimMiscResubmissionCode: '再送信コード',
	ClaimMiscResubmissionCodeHelpContent:
		'再提出とは、以前に提出された請求または遭遇を示すために、宛先の支払人または受取人によって割り当てられたコードと元の参照番号を意味します。',
	ClaimMiscResubmissionCodeHelpSubtitle: 'フィールド22',
	ClaimMiscResubmissionCodeHelpTitle: '再提出および/または元の参照番号',
	ClaimNumber: '請求番号',
	ClaimNumberFormat: '請求書 #{number}',
	ClaimOrderingProvider: '発注元',
	ClaimOtherId: 'その他のID',
	ClaimOtherIdPlaceholder: 'オプションを選択',
	ClaimOtherIdQualifier: 'その他のID修飾子',
	ClaimOtherIdQualifierPlaceholder: 'ID修飾子を選択',
	ClaimPlaceOfService: 'サービス場所',
	ClaimPlaceOfServicePlaceholder: 'POSを追加',
	ClaimPolicyHolderRelationship: '保険契約者との関係',
	ClaimPolicyInformation: 'ポリシー情報',
	ClaimPolicyTelephone: '電話番号（市外局番を含む）',
	ClaimReceivedAction: '<mark>請求 {claimNumber}</mark> は <b>{name}</b> によって受け取られました。',
	ClaimReferringProvider: '紹介プロバイダー',
	ClaimReferringProviderEmpty: '紹介するプロバイダーは追加されていません。',
	ClaimReferringProviderHelpContent:
		'入力された名前は、請求のサービスまたは供給品を紹介、注文、または監督した紹介元プロバイダー、発注元プロバイダー、または監督元プロバイダーです。修飾子は、報告されるプロバイダーの役割を示します。',
	ClaimReferringProviderHelpSubtitle: 'フィールド17',
	ClaimReferringProviderHelpTitle: '紹介元または提供元の名前',
	ClaimReferringProviderQualifier: '予選',
	ClaimReferringProviderQualifierPlaceholder: '修飾語を選択',
	ClaimRejectedAction: '<mark>請求 {claimNumber}</mark> は <b>{name}</b> によって拒否されました。',
	ClaimRenderingProviderIdNumber: 'ID番号',
	ClaimRenderingProviderOrTeamMember: 'レンダリングプロバイダーまたはチームメンバー',
	ClaimRestoredAction: '<mark>クレーム {claimNumber}</mark> は復元されました',
	ClaimServiceFacility: 'サービス施設',
	ClaimServiceFacilityLocationHelpContent:
		'サービスが提供された施設の名前と住所により、サービスが提供された場所が特定されます。',
	ClaimServiceFacilityLocationHelpLabel: '32、32a、32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'フィールド32、32a、32b',
	ClaimServiceFacilityLocationHelpTitle: 'サービス施設',
	ClaimServiceFacilityPlaceholder: 'サービス施設または場所を選択してください',
	ClaimServiceLabChargesHelpContent: `請求プロバイダー以外のエンティティによって提供された購入サービスに対して請求する場合は、このフィールドに入力します。
 CMS1500 フォームには 1 つの料金しか入力できないため、購入した各サービスは個別の請求で報告する必要があります。`,
	ClaimServiceLabChargesHelpSubtitle: 'フィールド20',
	ClaimServiceLabChargesHelpTitle: '外部ラボ費用',
	ClaimServiceLineServiceHelpContent:
		'「手順、サービス、または供給品」は、患者に提供される医療サービスと手順を識別します。',
	ClaimServiceLineServiceHelpSubtitle: 'フィールド 24d',
	ClaimServiceLineServiceHelpTitle: '手順、サービス、または供給',
	ClaimServiceLinesEmptyError: '少なくとも 1 つのサービスラインが必要です。',
	ClaimServiceSupplementaryInfoHelpContent: `該当する修飾語を使用して、提供されるサービスに関する追加の説明文を追加します。
修飾子と情報の間にスペース、ハイフン、またはその他の区切り文字を入力しないでください。

補足情報を追加する詳細な手順については、CMS 1500 請求書フォームの手順を確認してください。`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'フィールド24',
	ClaimServiceSupplementaryInfoHelpTitle: '補足情報',
	ClaimSettingsBillingMethodTitle: 'クライアントの請求方法',
	ClaimSettingsClientSignatureDescription:
		'保険金請求の処理に必要な医療情報やその他の情報を開示することに同意します。',
	ClaimSettingsClientSignatureTitle: 'ファイルに保存されたクライアントの署名',
	ClaimSettingsConsentLabel: '保険金請求を処理するには同意が必要です:',
	ClaimSettingsDescription: 'スムーズな支払い処理を確実に行うために、クライアントの請求方法を選択してください。',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name}は有効な保険証を持っています。保険請求を有効にするには、クライアントの請求方法を保険に変更してください。',
	ClaimSettingsInsuranceDescription: '保険で補償される費用',
	ClaimSettingsInsuranceTitle: '保険',
	ClaimSettingsNoPoliciesAlertDescription: '保険金請求を可能にするために保険証券を追加します。',
	ClaimSettingsPolicyHolderSignatureDescription: '提供されたサービスに対する保険金を受け取ることに同意します。',
	ClaimSettingsPolicyHolderSignatureTitle: '保険契約者の署名がファイルに保存されている',
	ClaimSettingsSelfPayDescription: '予約料はクライアントが支払う',
	ClaimSettingsSelfPayTitle: '自己負担',
	ClaimSettingsTitle: 'クレーム設定',
	ClaimSexSelectorPlaceholder: '男性 / 女性',
	ClaimStatusChangedAction: '<mark>請求 {claimNumber}</mark> ステータス更新',
	ClaimSubmittedAction:
		'<mark>請求 {claimNumber}</mark> は <b>{payerClearingHouse}</b> に <b>{payerNumber} {payerName}</b> のために提出されました。',
	ClaimSubtitle: '請求 #{claimNumber}',
	ClaimSupervisingProvider: '監督プロバイダー',
	ClaimSupplementaryInfo: '補足情報',
	ClaimSupplementaryInfoPlaceholder: '補足情報を追加する',
	ClaimTrashedAction: '<mark>請求 {claimNumber}</mark> は削除されました。',
	ClaimValidationFailure: 'クレームの検証に失敗しました。',
	ClaimsEmptyStateDescription: 'クレームは見つかりませんでした。',
	ClainInsuranceTelephone: '保険電話番号（市外局番を含む）',
	Classic: 'クラシック',
	Clear: 'クリア',
	ClearAll: 'すべてクリア',
	ClearSearchFilter: 'クリア',
	ClearingHouse: '片付け',
	ClearingHouseClaimId: 'Claim.MD ID',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: '住宅参照',
	ClearingHouseUnavailableError: '現在、決済代行サービスは利用できません。後でもう一度お試しください。',
	ClickToUpload: 'クリックしてアップロード',
	Client: 'クライアント',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} は {noteTitle, select, undefined { メモ } other {{noteTitle}}} を追加しました。',
	ClientAndRelationshipSelectorPlaceholder: '顧客とその関係を選択する',
	ClientAndRelationshipSelectorTitle: 'すべての顧客とその関係',
	ClientAndRelationshipSelectorTitle1: '‘{name}’ のすべての関係',
	ClientAppCallsPageNoOptionsText:
		'ビデオ通話を待機している場合は、まもなくここに表示されます。問題が発生した場合は、ビデオ通話を開始した人に問い合わせてください。',
	ClientAppSubHeaderMyDocumentation: '私のドキュメント',
	ClientAppointment: 'クライアントの予約',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} は {appointmentName} を予約しました。',
	ClientAppointmentsEmptyStateDescription: '予約が見つかりませんでした',
	ClientAppointmentsEmptyStateTitle: '顧客の今後の予定や過去の予定、出席状況を追跡します',
	ClientArchivedSuccessfulSnackbar: '正常にアーカイブされました <b>{name}</b>',
	ClientBalance: '顧客残高',
	ClientBilling: '請求する',
	ClientBillingAddPaymentMethodDescription:
		'クライアントの支払い方法の追加と管理を行い、請求書発行と課金のプロセスを効率化します。',
	ClientBillingAndPaymentDueDate: '期日',
	ClientBillingAndPaymentHistory: '請求と支払い履歴',
	ClientBillingAndPaymentInvoices: '請求書',
	ClientBillingAndPaymentIssueDate: '発行日',
	ClientBillingAndPaymentPrice: '価格',
	ClientBillingAndPaymentReceipt: 'レシート',
	ClientBillingAndPaymentServices: 'サービス',
	ClientBillingAndPaymentStatus: '状態',
	ClientBulkStaffAssignedSuccessSnackbar:
		'チーム{count, plural, one {メンバー} other {メンバー}} 割り当てられました！',
	ClientBulkStaffUnassignedSuccessSnackbar: 'チーム{count, plural, one {メンバー} other {メンバー}} 未割り当て！',
	ClientBulkTagsAddedSuccessSnackbar: 'タグが追加されました!',
	ClientDuplicatesDeviewDescription:
		'複数のクライアント レコードを 1 つに結合して、メモ、ドキュメント、予定、請求書、会話など、すべてのデータを統合します。',
	ClientDuplicatesPageMergeHeader: '保存したいデータを選択してください',
	ClientDuplicatesReviewHeader: 'マージのために重複する可能性のあるレコードを比較する',
	ClientEmailChangeWarningDescription:
		'クライアントのメールを更新すると、共有ドキュメントへのアクセスが削除され、新しいメールを持つユーザーにアクセス権が付与されます。',
	ClientFieldDateDescription: '日付のフォーマット',
	ClientFieldDateLabel: '日付',
	ClientFieldDateRangeDescription: '日付の範囲',
	ClientFieldDateRangeLabel: '日付範囲',
	ClientFieldDateShowDateDescription: '例：29歳',
	ClientFieldDateShowDateRangeDescription: '例：2週間',
	ClientFieldEmailDescription: '電子メールアドレス',
	ClientFieldEmailLabel: 'Eメール',
	ClientFieldLabel: 'フィールドラベル',
	ClientFieldLinearScaleDescription: 'スケールオプション 1-10',
	ClientFieldLinearScaleLabel: '線形スケール',
	ClientFieldLocationDescription: '住所または郵便番号',
	ClientFieldLocationLabel: '位置',
	ClientFieldLongTextDescription: '長いテキストエリア',
	ClientFieldLongTextLabel: '段落',
	ClientFieldMultipleChoiceDropdownDescription: 'リストから複数のオプションを選択してください',
	ClientFieldMultipleChoiceDropdownLabel: '複数選択ドロップダウン',
	ClientFieldPhoneNumberDescription: '電話番号',
	ClientFieldPhoneNumberLabel: '電話',
	ClientFieldPlaceholder: 'クライアントフィールドタイプを選択',
	ClientFieldSingleChoiceDropdownDescription: 'リストからオプションを 1 つだけ選択してください',
	ClientFieldSingleChoiceDropdownLabel: '単一選択ドロップダウン',
	ClientFieldTextDescription: 'テキスト入力フィールド',
	ClientFieldTextLabel: '文章',
	ClientFieldYesOrNoDescription: 'はいまたはいいえのオプションから選択してください',
	ClientFieldYesOrNoLabel: 'はい | いいえ',
	ClientFileFormAccessLevelDescription:
		'あなたとチームは、アップロードしたファイルにいつでもアクセスできます。このファイルをクライアントやその関係者と共有することを選択できます。',
	ClientFileSavedSuccessSnackbar: 'ファイルを保存しました!',
	ClientFilesPageEmptyStateText: 'ファイルがアップロードされていません',
	ClientFilesPageUploadFileButton: 'ファイルをアップロードする',
	ClientHeaderBilling: '請求',
	ClientHeaderBillingAndReceipts: '請求する ',
	ClientHeaderDocumentation: 'ドキュメント',
	ClientHeaderDocuments: '文書',
	ClientHeaderFile: '書類',
	ClientHeaderHistory: '病歴',
	ClientHeaderInbox: '受信トレイ',
	ClientHeaderNote: '注記',
	ClientHeaderOverview: '概要',
	ClientHeaderProfile: '個人的',
	ClientHeaderRelationship: '関係',
	ClientHeaderRelationships: '人間関係',
	ClientId: 'クライアントID',
	ClientImportProcessingDescription: 'ファイルはまだ処理中です。完了したら通知します。',
	ClientImportReadyForMappingDescription:
		'お客様のファイルの事前処理が完了しました。このインポートを完了するために、列をマッピングしますか？',
	ClientImportReadyForMappingNotificationSubject:
		'Client import pre-processing is complete. ファイルのマッピング準備が完了しました。',
	ClientInAppMessaging: 'クライアントアプリ内メッセージング',
	ClientInfoAddField: '別のフィールドを追加',
	ClientInfoAddRow: '行を追加する',
	ClientInfoAlertMessage: 'このセクションに入力された情報はすべてクライアント レコードに入力されます。',
	ClientInfoFormPrimaryText: '顧客情報',
	ClientInfoFormSecondaryText: '連絡先の詳細を収集する',
	ClientInfoPlaceholder: `顧客名、メールアドレス、電話番号
実在住所、
生年月日`,
	ClientInformation: '顧客情報',
	ClientInsuranceTabLabel: 'クライアントの保険',
	ClientIntakeFormsNotSupported: `フォーム テンプレートは現在、クライアント インテイクではサポートされていません。
代わりにクライアントのメモとして作成して共有します。`,
	ClientIntakeModalDescription:
		'クライアントに、プロフィールの記入、関連する医療文書または紹介文書のアップロードを依頼する受付メールが送信されます。クライアントにはクライアント ポータルへのアクセス権が付与されます。',
	ClientIntakeModalTitle: '{name} に摂取を送信する',
	ClientIntakeSkipPasswordSuccessSnackbar: '成功しました。摂取量が保存されました。',
	ClientIntakeSuccessSnackbar: '成功しました。入力内容が保存され、確認メールが送信されました。',
	ClientIsChargedProcessingFee: '顧客が手数料を支払う',
	ClientListCreateButton: '新規クライアント',
	ClientListEmptyState: 'クライアントが追加されていません',
	ClientListPageItemArchive: 'クライアントを削除',
	ClientListPageItemRemoveAccess: 'アクセスを削除する',
	ClientLocalizationPanelDescription: 'クライアントの希望する言語とタイムゾーン。',
	ClientLocalizationPanelTitle: '言語とタイムゾーン',
	ClientManagementAndEHR: 'クライアント管理 ',
	ClientMergeResultSummaryBanner:
		'レコードの統合は、ノート、書類、予約、請求書、会話など、すべてのクライアントデータを統合します。続行する前に、正確性を確認してください。',
	ClientMergeResultSummaryTitle: 'マージ結果サマリー',
	ClientModalTitle: '新規クライアント',
	ClientMustHaveEmaillAccessErrorText: 'メールのないクライアント/連絡先',
	ClientMustHavePortalAccessErrorText: 'クライアント/連絡先はサインアップする必要があります',
	ClientMustHaveZoomAppConnectedErrorText: '設定 &gt; 接続されたアプリからZoomに接続します',
	ClientNameFormat: 'クライアント名の形式',
	ClientNotFormAccessLevel: '閲覧可能:',
	ClientNotFormAccessLevelDescription:
		'あなたとチームは、あなたが公開したメモにいつでもアクセスできます。このメモをクライアントやその関係者と共有することを選択できます。',
	ClientNotRegistered: '未登録',
	ClientNoteFormAddFileButton: 'ファイルを添付する',
	ClientNoteFormChooseAClient: '続行するにはクライアント/連絡先を選択してください',
	ClientNoteFormContent: 'コンテンツ',
	ClientNoteItemDeleteConfirmationModalDescription: '一度削除すると、このメモを再度取得することはできません。',
	ClientNotePublishedAndLockSuccessSnackbar: 'メモが公開され、ロックされました。',
	ClientNotePublishedSuccessSnackbar: 'ノートを公開しました！',
	ClientNotes: 'クライアントのメモ',
	ClientNotesEmptyStateText: 'メモを追加するには、クライアントのプロフィールに移動し、「メモ」タブをクリックします。',
	ClientOnboardingChoosePasswordTitle1: 'ほぼ完了しました！',
	ClientOnboardingChoosePasswordTitle2: 'パスワードを決めて下さい',
	ClientOnboardingCompleteIntake: '完全な摂取',
	ClientOnboardingConfirmationScreenText:
		'{providerName} が要求する情報はすべて提供されました。	オンボーディングを開始するには、メールアドレスを確認してください。すぐに届かない場合は、スパムフォルダを確認してください。',
	ClientOnboardingConfirmationScreenTitle: '素晴らしい！受信トレイを確認してください。',
	ClientOnboardingDashboardButton: 'ダッシュボードに進め',
	ClientOnboardingHealthRecordsDesc1: '{providerName} に紹介状や書類を共有しますか？',
	ClientOnboardingHealthRecordsDescription: '説明を追加（オプション）',
	ClientOnboardingHealthRecordsTitle: 'ドキュメンテーション',
	ClientOnboardingPasswordRequirements: '要件',
	ClientOnboardingPasswordRequirementsConditions1: '最低9文字が必要です',
	ClientOnboardingProviderIntroSignupButton: '自分用にサインアップ',
	ClientOnboardingProviderIntroSignupFamilyButton: '家族会員登録',
	ClientOnboardingProviderIntroTitle: '{name} は Carepatron プラットフォームへの参加を招待しました。',
	ClientOnboardingRegistrationInstructions: '以下に個人情報を入力してください。',
	ClientOnboardingRegistrationTitle: 'まず個人情報が必要です',
	ClientOnboardingStepFormsAndAgreements: 'フォームと契約',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'{providerName} の受け入れプロセスのため、以下のフォームにご記入ください。',
	ClientOnboardingStepHealthDetails: '健康の詳細',
	ClientOnboardingStepPassword: 'パスワード',
	ClientOnboardingStepYourDetails: 'あなたの詳細',
	ClientPaymentMethodDescription:
		'次回の予約と請求をより迅速かつ安全に行うために、お支払い方法をプロフィールに保存してください。',
	ClientPortal: 'クライアントポータル',
	ClientPortalDashboardEmptyDescription: '予約履歴と出欠状況がここに表示されます。',
	ClientPortalDashboardEmptyTitle: '今後の予定、リクエスト済みの予定、過去の予定をすべて、出席とともに記録します。',
	ClientPreferredNotificationPanelDescription:
		'以下の方法でクライアントが希望する更新情報や通知の受信方法を管理します。',
	ClientPreferredNotificationPanelTitle: '優先通知方法',
	ClientProcessingFee: '支払いには ({currencyCode}) {amount} の処理手数料が含まれます。',
	ClientProfileAddress: '住所',
	ClientProfileDOB: '生年月日',
	ClientProfileEmailHelperText: '電子メールを追加するとポータルへのアクセスが許可されます',
	ClientProfileEmailHelperTextMoreInfo:
		'クライアントにポータルへのアクセスを許可すると、チームメンバーはメモ、ファイル、その他のドキュメントを共有できるようになります。',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: '識別番号',
	ClientRelationshipsAddClientOwnerButton: 'クライアントを招待する',
	ClientRelationshipsAddFamilyButton: '家族を招待する',
	ClientRelationshipsAddStaffButton: 'スタッフのアクセスを追加',
	ClientRelationshipsEmptyStateText: '関係が追加されていません',
	ClientRemovedSuccessSnackbar: 'クライアントが正常に削除されました。',
	ClientResponsibility: 'クライアントの責任',
	ClientSavedSuccessSnackbar: 'クライアントは正常に保存されました。',
	ClientTableClientName: 'クライアント名',
	ClientTablePhone: '電話',
	ClientTableStatus: '状態',
	ClientUnarchivedSuccessfulSnackbar: '正常にアーカイブ解除されました <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} は {noteTitle, select, undefined { メモ } other {{noteTitle}}} を編集しました。',
	ClientView: 'クライアントビュー',
	Clients: 'クライアント',
	ClientsTable: 'クライアントテーブル',
	ClinicalFormat: '臨床形式',
	ClinicalPsychologist: '臨床心理士',
	Close: '近い',
	CloseImportClientsModal: 'クライアントのインポートをキャンセルしてもよろしいですか?',
	CloseReactions: '近い反応',
	Closed: '閉まっている',
	Coaching: 'コーチング',
	Code: 'コード',
	CodeErrorMessage: 'コードが必要です',
	CodePlaceholder: 'コード',
	Coinsurance: '共同保険',
	Collection: 'コレクション',
	CollectionName: 'コレクション名',
	Collections: 'コレクション',
	ColorAppointmentsBy: 'カラーアポイントメント',
	ColorTheme: 'カラーテーマ',
	ColourCalendarBy: 'カラーカレンダー',
	ComingSoon: '近日公開',
	Community: 'コミュニティ',
	CommunityHealthLead: 'コミュニティヘルスリーダー',
	CommunityHealthWorker: 'コミュニティヘルスワーカー',
	CommunityTemplatesSectionDescription: 'Carepatron コミュニティによって作成されました',
	CommunityTemplatesSectionTitle: 'コミュニティ',
	CommunityUser: 'コミュニティユーザー',
	Complete: '完了',
	CompleteAndLock: '完了してロック',
	CompleteSetup: 'セットアップを完了する',
	CompleteSetupSuccessDescription: 'あなたは、Carepatron の習得に向けて、重要なステップをいくつか完了しました。',
	CompleteSetupSuccessDescription2: '診療の効率化とクライアントのサポートのための方法をさらに増やしましょう。',
	CompleteSetupSuccessTitle: '成功！あなたは素晴らしいことをしています！',
	CompleteStripeSetup: 'Stripeのセットアップを完了する',
	Completed: '完了',
	ComposeSms: 'SMSを作成',
	ComputerSystemsAnalyst: 'コンピュータシステムアナリスト',
	Confirm: '確認する',
	ConfirmDeleteAccountDescription:
		'アカウントを削除しようとしています。この操作は元に戻せません。続行する場合は、以下を確認してください。',
	ConfirmDeleteActionDescription: 'この操作を削除してもよろしいですか? 元に戻すことはできません',
	ConfirmDeleteAutomationDescription: 'この自動化を削除してもよろしいですか? この操作は元に戻せません。',
	ConfirmDeleteScheduleDescription:
		'<strong>{scheduleName}</strong> スケジュールを削除すると、スケジュールから削除され、オンラインサービスの利用可能性が変更される可能性があります。この操作は元に戻せません。',
	ConfirmDraftResponseContinue: '応答を続ける',
	ConfirmDraftResponseDescription: 'このページを閉じると、回答は下書きとして残ります。いつでも戻って続行できます。',
	ConfirmDraftResponseSubmitResponse: '回答を送信',
	ConfirmDraftResponseTitle: '回答が送信されていません',
	ConfirmIfUserIsClientDescription: `記入した登録フォームはプロバイダー（医療チーム/組織など）向けです。
これが間違いである場合は、「クライアントとして続行」を選択すると、クライアントポータルの設定が行われます。`,
	ConfirmIfUserIsClientNoButton: 'プロバイダーとして登録',
	ConfirmIfUserIsClientTitle: 'あなたはクライアントのようです',
	ConfirmIfUserIsClientYesButton: 'クライアントとして続行',
	ConfirmKeepSeparate: '別々に保管することを確認する',
	ConfirmMerge: 'マージを確認する',
	ConfirmPassword: 'パスワードを認証する',
	ConfirmRevertClaim: 'はい、ステータスを元に戻します。',
	ConfirmSignupAccessCode: '確認コード',
	ConfirmSignupButtom: '確認する',
	ConfirmSignupDescription: 'メールアドレスと、先ほど送信した確認コードを入力してください。',
	ConfirmSignupSubTitle: '迷惑メールフォルダを確認してください - メールが届いていない場合は',
	ConfirmSignupSuccessSnackbar:
		'素晴らしいです。アカウントを確認しました。メールアドレスとパスワードを使用してログインできます。',
	ConfirmSignupTitle: 'アカウントを確認',
	ConfirmSignupUsername: 'Eメール',
	ConfirmSubscriptionUpdate: '購読を確認する {price} {isMonthly, select, true {月額} other {年間}}',
	ConfirmationModalBulkDeleteClientsDescriptionId: 'クライアントが削除されると、その情報にアクセスできなくなります。',
	ConfirmationModalBulkDeleteClientsTitleId:
		'{count, plural, one {# クライアント} other {# クライアント}} を削除しますか？',
	ConfirmationModalBulkDeleteContactsDescriptionId: '連絡先が削除されると、その情報にアクセスできなくなります。',
	ConfirmationModalBulkDeleteContactsTitleId: '{count, plural, one {# 連絡先} other {# 連絡先}} を削除しますか？',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'これは永続的なアクションです。チーム メンバーが削除されると、そのメンバーの情報にアクセスできなくなります。',
	ConfirmationModalBulkDeleteMembersTitleId:
		'{count, plural, one {# チームメンバー} other {# チームメンバー}} を削除しますか？',
	ConfirmationModalCloseOnGoingTranscription:
		'このメモを閉じると、進行中の転写はすべて終了します。続行してもよろしいですか?',
	ConfirmationModalDeleteClientField:
		'これは永続的なアクションです。フィールドが削除されると、残りのクライアントではアクセスできなくなります。',
	ConfirmationModalDeleteSectionMessage:
		'削除すると、このセクションのすべての質問が削除されます。この操作は元に戻せません。',
	ConfirmationModalDeleteService:
		'これは永続的なアクションです。サービスが削除されると、ワークスペースでアクセスできなくなります。',
	ConfirmationModalDeleteServiceGroup:
		'コレクションを削除すると、グループからすべてのサービスが削除され、サービス リストに戻ります。この操作は元に戻せません。',
	ConfirmationModalDeleteTranscript: 'トランスクリプトを削除してもよろしいですか?',
	ConfirmationModalDescriptionDeleteClient: 'クライアントを削除すると、クライアント情報にアクセスできなくなります。',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'アクセスを削除すると、クライアント情報を表示できなくなります。',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'プロフィールは削除されず、このクライアントの関係として削除されるだけです。',
	ConfirmationModalDescriptionRemoveStaff: 'この人をプロバイダーから削除してもよろしいですか?',
	ConfirmationModalEndSession: '本当にセッションを終了してもよろしいですか?',
	ConfirmationModalTitle: '本気ですか？',
	Confirmed: '確認済み',
	ConflictTimezoneWarningMessage: '複数のタイムゾーンにより競合が発生する場合があります',
	Connect: '接続する',
	ConnectExistingClientOrContact: '新しいクライアント/連絡先を作成する',
	ConnectInboxGoogleDescription: 'GmailアカウントまたはGoogleグループリストを追加する',
	ConnectInboxMicrosoftDescription: 'Outlook、Office365、またはExchangeアカウントを追加する',
	ConnectInboxModalDescription: 'アプリを接続すると、すべての通信を 1 か所でシームレスに送信、受信、追跡できます。',
	ConnectInboxModalExistingDescription:
		'接続されているアプリの設定から既存の接続を使用して、構成プロセスを効率化します。',
	ConnectInboxModalExistingTitle: 'Carepatron の既存の接続アプリ',
	ConnectInboxModalTitle: '受信トレイを接続',
	ConnectToStripe: 'Stripeに接続する',
	ConnectZoom: 'ズームに接続',
	ConnectZoomModalDescription: 'Carepatron が予約のビデオ通話を管理できるようにします。',
	ConnectedAppDisconnectedNotificationSubject: '{account} アカウントとの接続が失われました。再接続してください。',
	ConnectedAppSyncDescription:
		'接続されたアプリを管理して、Carepatron から直接サードパーティのカレンダーにイベントを作成します。',
	ConnectedApps: '接続されたアプリ',
	ConnectedAppsGMailDescription: 'Gmail アカウントまたは Google グループ リストを追加する',
	ConnectedAppsGoogleCalendarDescription: 'カレンダーアカウントまたはGoogleグループリストを追加する',
	ConnectedAppsGoogleDescription: 'Gmailアカウントを追加してGoogleカレンダーを同期する',
	ConnectedAppsMicrosoftDescription: 'Outlook、Office365、またはExchangeアカウントを追加する',
	ConnectedCalendars: '接続されたカレンダー',
	ConsentDocumentation: 'フォームと契約',
	ConsentDocumentationPublicTemplateError:
		'セキュリティ上の理由から、チームからのテンプレート（非公開）のみを選択できます。',
	ConstructionWorker: '建設作業員',
	Consultant: 'コンサルタント',
	Contact: '接触',
	ContactAccessTypeHelperText: 'ファミリー管理者が情報を更新できるようにする',
	ContactAccessTypeHelperTextMoreInfo: 'これにより、{clientFirstName}に関するメモ/ドキュメントを共有できます。',
	ContactAddressLabelBilling: '請求する',
	ContactAddressLabelHome: '家',
	ContactAddressLabelOthers: 'その他',
	ContactAddressLabelWork: '仕事',
	ContactChangeConfirmation:
		'請求書の連絡先を変更すると、<mark>{contactName}</mark> に関連するすべての項目が削除されます',
	ContactDetails: '連絡先',
	ContactEmailLabelOthers: 'その他',
	ContactEmailLabelPersonal: '個人的',
	ContactEmailLabelSchool: '学校',
	ContactEmailLabelWork: '仕事',
	ContactInformation: '連絡先',
	ContactInformationText: '連絡先',
	ContactListCreateButton: '新しい連絡先',
	ContactName: '連絡先名',
	ContactPhoneLabelHome: '家',
	ContactPhoneLabelMobile: '携帯',
	ContactPhoneLabelSchool: '学校',
	ContactPhoneLabelWork: '仕事',
	ContactRelationship: '連絡先関係',
	ContactRelationshipFormAccessType: '共有情報へのアクセスを許可',
	ContactRelationshipGrantAccessInfo: 'これにより、メモとドキュメントを共有できます',
	ContactSupport: 'サポートに問い合わせる',
	Contacts: '連絡先',
	ContainerIdNotSet: 'コンテナIDが設定されていません',
	Contemporary: 'コンテンポラリー',
	Continue: '続く',
	ContinueDictating: '口述を続ける',
	ContinueEditing: '編集を続ける',
	ContinueImport: 'インポートを続行',
	ContinueTranscription: '転写を続ける',
	ContinueWithApple: 'Appleで続ける',
	ContinueWithGoogle: 'Googleで続行',
	Conversation: '会話',
	Copay: '共同支払い',
	CopayOrCoinsurance: '共同支払または共同保険',
	Copayment: '共同支払い',
	CopiedToClipboard: 'クリップボードにコピーされました',
	Copy: 'コピー',
	CopyAddressSuccessSnackbar: 'クリップボードにアドレスをコピーしました。',
	CopyCode: 'コピーコード',
	CopyCodeToClipboardSuccess: 'クリップボードにコードをコピーしました。',
	CopyEmailAddressSuccessSnackbar: 'クリップボードにメールアドレスをコピーしました',
	CopyLink: 'リンクをコピーする',
	CopyLinkForCall: 'この通話を共有するには、このリンクをコピーしてください:',
	CopyLinkSuccessSnackbar: 'リンクをクリップボードにコピーしました',
	CopyMeetingLink: '会議リンクをコピー',
	CopyPaymentLink: '支払いリンクをコピー',
	CopyPhoneNumberSuccessSnackbar: 'クリップボードに電話番号をコピーしました。',
	CopyTemplateLink: 'テンプレートへのリンクをコピー',
	CopyTemplateLinkSuccess: 'リンクをクリップボードにコピーしました',
	CopyToClipboardError: 'クリップボードにコピーできませんでした。もう一度お試しください。',
	CopyToTeamTemplates: 'チームテンプレートにコピー',
	CopyToWorkspace: 'ワークスペースにコピー',
	Cosmetologist: '美容師',
	Cost: '料金',
	CostErrorMessage: '費用は必要',
	Counseling: 'カウンセリング',
	Counselor: 'カウンセラー',
	Counselors: 'カウンセラー',
	CountInvoicesAdded: '{count, plural, one {# 請求書を追加} other {# 請求書を追加}}',
	CountNotesAdded: '{count, plural, one {# 注釈を追加} other {# 注釈を追加}}',
	CountSelected: '{count} 選択済み',
	CountTimes: '{count} 回',
	Country: '国',
	Cousin: 'いとこ',
	CoverageType: '補償タイプ',
	Covered: 'カバー',
	Create: '作成する',
	CreateANewClient: '新しいクライアントを作成する',
	CreateAccount: 'アカウントを作成する',
	CreateAndSignNotes: 'クライアントとのメモの作成と署名',
	CreateAvailabilityScheduleFailure: '新しい空きスケジュールを作成できませんでした',
	CreateAvailabilityScheduleSuccess: '新しい空きスケジュールが正常に作成されました',
	CreateBillingItems: '請求項目を作成する',
	CreateCallFormButton: '通話を開始',
	CreateCallFormInviteOnly: '招待者のみ',
	CreateCallFormInviteOnlyMoreInfo:
		'この通話に招待された人だけが参加できます。この通話を他の人と共有するには、このチェックを外して、次のページにリンクをコピー/貼り付けてください。',
	CreateCallFormRecipients: '受信者',
	CreateCallFormRegion: '開催地域',
	CreateCallModalAddClientContactSelectorLabel: 'クライアントの連絡先',
	CreateCallModalAddClientContactSelectorPlaceholder: 'クライアント名で検索',
	CreateCallModalAddStaffSelectorLabel: 'チームメンバー（オプション）',
	CreateCallModalAddStaffSelectorPlaceholder: 'スタッフ名で検索',
	CreateCallModalDescription:
		'通話を開始し、スタッフや連絡先を招待します。または、「プライベート」ボックスのチェックを外して、この通話をCarepatronのユーザー全員と共有できるようにすることもできます。',
	CreateCallModalTitle: '通話を開始する',
	CreateCallModalTitleLabel: 'タイトル（オプション）',
	CreateCallNoPersonIdToolTip: 'ポータルにアクセスできる連絡先/クライアントのみが通話に参加できます',
	CreateClaim: 'クレームを作成する',
	CreateClaimCompletedMessage: 'クレームが作成されました。',
	CreateClientModalTitle: '新規クライアント',
	CreateContactModalTitle: '新しい連絡先',
	CreateContactRelationshipButton: '関係を追加',
	CreateContactSelectorDefaultOption: '  連絡先を作成',
	CreateContactWithRelationshipFormAccessType: '共有情報へのアクセスを許可する ',
	CreateDocumentDnDPrompt: 'ドラッグアンドドロップでファイルをアップロードします',
	CreateDocumentSizeLimit: 'ファイルあたりのサイズ制限は {size}MB です。合計 {total} ファイルです。',
	CreateFreeAccount: '無料アカウントを作成',
	CreateInvoice: '請求書を作成する',
	CreateLink: 'リンクを作成',
	CreateNew: '新しく作る',
	CreateNewAppointment: '新しい予定を作成',
	CreateNewClaim: '新しいクレームを作成する',
	CreateNewClaimForAClient: 'クライアントのために新しい請求を作成する。',
	CreateNewClient: '新しいクライアントを作成する',
	CreateNewConnection: '新しい接続',
	CreateNewContact: '新しい連絡先を作成',
	CreateNewField: '新しいフィールドを作成',
	CreateNewLocation: '新しい場所',
	CreateNewService: '新しいサービスを作成する',
	CreateNewServiceGroupFailure: '新しいコレクションを作成できませんでした',
	CreateNewServiceGroupMenu: '新しいコレクション',
	CreateNewServiceGroupSuccess: '新しいコレクションを作成しました',
	CreateNewServiceMenu: '新サービス',
	CreateNewTeamMember: '新しいチームメンバーを作成する',
	CreateNewTemplate: '新しいテンプレート',
	CreateNote: 'ノート作成',
	CreateSuperbillReceipt: '新しいスーパービル',
	CreateSuperbillReceiptSuccess: 'スーパービル領収書が正常に作成されました',
	CreateTemplateFolderSuccessMessage: '{folderTitle} を正常に作成しました。',
	Created: '作成',
	CreatedAt: '作成日 {timestamp}',
	Credit: 'クレジット',
	CreditAdded: 'クレジット適用',
	CreditAdjustment: '信用調整',
	CreditAdjustmentReasonHelperText: 'これは内部メモであり、クライアントには表示されません。',
	CreditAdjustmentReasonPlaceholder: '調整理由を追加すると、請求可能なトランザクションを確認するときに役立ちます',
	CreditAmount: '{amount} クレ',
	CreditBalance: '信用残高',
	CreditCard: 'クレジットカード',
	CreditCardExpire: '有効期限 {exp_month}/{exp_year}',
	CreditCardNumber: 'クレジットカード番号',
	CreditDebitCard: 'カード',
	CreditIssued: 'クレジットの発行',
	CreditsUsed: '使用したクレジット',
	Crop: '作物',
	Currency: '通貨',
	CurrentCredit: '現在のクレジット',
	CurrentEventTime: '現在のイベント時間',
	CurrentPlan: '現在の計画',
	Custom: 'カスタム',
	CustomRange: 'カスタム範囲',
	CustomRate: 'カスタム料金',
	CustomRecurrence: 'カスタム再発',
	CustomServiceAvailability: 'サービスの可用性',
	CustomerBalance: '顧客残高',
	CustomerName: '顧客名',
	CustomerNameIsRequired: '顧客名は必須です',
	CustomerServiceRepresentative: '顧客サービス担当者',
	CustomiseAppointments: '予定をカスタマイズする',
	CustomiseBookingLink: '予約オプションをカスタマイズする',
	CustomiseBookingLinkServicesInfo: 'クライアントは予約可能なサービスのみを選択できます',
	CustomiseBookingLinkServicesLabel: 'サービス',
	CustomiseClientRecordsAndWorkspace: 'クライアントの記録とワークスペースをカスタマイズする',
	CustomiseClientSettings: 'クライアント設定をカスタマイズする',
	Customize: 'カスタマイズ',
	CustomizeAppearance: '外観をカスタマイズする',
	CustomizeAppearanceDesc:
		'ブランドに合わせてオンライン予約の外観をカスタマイズし、サービスがクライアントに表示される方法を最適化します。',
	CustomizeClientFields: 'クライアントフィールドをカスタマイズする',
	CustomizeInvoiceTemplate: '請求書テンプレートをカスタマイズする',
	CustomizeInvoiceTemplateDescription: 'ブランドを反映したプロフェッショナルな請求書を簡単に作成できます。',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DXが必要です',
	Daily: '毎日',
	DanceTherapist: 'ダンスセラピスト',
	DangerZone: '危険区域',
	Dashboard: 'ダッシュボード',
	Date: '日付',
	DateAndTime: '日付 ',
	DateDue: '期日',
	DateErrorMessage: '日付は必須です',
	DateFormPrimaryText: '日付',
	DateFormSecondaryText: '日付ピッカーから選択',
	DateIssued: '日付発行',
	DateOfPayment: '支払日',
	DateOfService: 'サービス日',
	DateOverride: '日付の上書き',
	DateOverrideColor: '日付オーバーライド色',
	DateOverrideInfo:
		'日付のオーバーライドにより、実務者は通常のスケジュールをオーバーライドして、特定の日付の空き状況を手動で調整できます。',
	DateOverrideInfoBanner:
		'これらの時間帯には、この日付のオーバーライドに指定されたサービスのみを予約できます。その他のオンライン予約は許可されません。',
	DateOverrides: '日付の上書き',
	DatePickerFormPrimaryText: '日付',
	DatePickerFormSecondaryText: '日付を選択',
	DateRange: '日付範囲',
	DateRangeFormPrimaryText: '日付範囲',
	DateRangeFormSecondaryText: '日付範囲を選択してください',
	DateReceived: '受領日',
	DateSpecificHours: '日付指定時間',
	DateSpecificHoursDescription:
		'予定の時間帯から空き状況が変わる場合や、特定の日付にサービスを提供する場合は、日付を追加します。',
	DateUploaded: 'アップロード済み {date, date, medium}',
	Dates: '日付',
	Daughter: '娘',
	Day: '日',
	DayPlural: '{count, plural, one {日} other {日}}',
	Days: '日々',
	DaysPlural: '{age, plural, one {# 日} other {# 日}}',
	DeFacto: '事実上',
	Deactivated: '非アクティブ',
	Debit: 'デビット',
	DecreaseIndent: 'インデントを減らす',
	Deductibles: '控除額',
	Default: 'デフォルト',
	DefaultBillingProfile: 'デフォルトの請求プロファイル',
	DefaultDescription: 'デフォルトの説明',
	DefaultEndOfLine: 'アイテムはもうありません',
	DefaultInPerson: 'クライアントとの面談',
	DefaultInvoiceTitle: 'デフォルトのタイトル',
	DefaultNotificationSubject: '{notificationType} の新しい通知を受け取りました。',
	DefaultPaymentMethod: 'デフォルトの支払い方法',
	DefaultService: 'デフォルトサービス',
	DefaultValue: 'デフォルト',
	DefaultVideo: 'クライアントのビデオ予約メール',
	DefinedTemplateType: '{invoiceTemplate} テンプレート',
	Delete: '消去',
	DeleteAccountButton: 'アカウントを削除',
	DeleteAccountDescription: 'プラットフォームからアカウントを削除する',
	DeleteAccountPanelInfoAlert:
		'プロフィールを削除する前に、ワークスペースを削除する必要があります。続行するには、ワークスペースに切り替えて、[設定] > [ワークスペース設定] を選択します。',
	DeleteAccountTitle: 'アカウントを削除',
	DeleteAppointment: '予定を削除',
	DeleteAppointmentDescription: 'この予定を削除してもよろしいですか? 後で復元できます。',
	DeleteAvailabilityScheduleFailure: '空きスケジュールの削除に失敗しました',
	DeleteAvailabilityScheduleSuccess: '空きスケジュールを正常に削除しました',
	DeleteBillable: '請求対象を削除',
	DeleteBillableConfirmationMessage: 'この請求対象を削除してもよろしいですか? この操作は元に戻せません。',
	DeleteBillingProfileConfirmationMessage: 'これにより、請求プロファイルが完全に削除されます。',
	DeleteCardConfirmation: 'これは永続的な操作です。カードが削除されると、アクセスできなくなります。',
	DeleteCategory: 'カテゴリを削除します（変更を保存しない限り、削除は永続的ではありません）',
	DeleteClientEventConfirmationDescription: 'これは永久に削除されます。',
	DeleteClients: 'クライアントを削除する',
	DeleteCollection: 'コレクションを削除',
	DeleteColumn: '列を削除',
	DeleteConversationConfirmationDescription: 'この会話を完全に削除します。この操作は元に戻せません。',
	DeleteConversationConfirmationTitle: '会話を完全に削除',
	DeleteExternalEventDescription: 'この予約を削除してもよろしいですか？',
	DeleteFileConfirmationModalPrompt: '一度削除すると、このファイルを再度取得することはできません。',
	DeleteFolder: 'フォルダーを削除',
	DeleteFolderConfirmationMessage:
		'このフォルダ {name} を削除してもよろしいですか？ このフォルダ内のすべてのアイテムも削除されます。 後で復元できます。',
	DeleteForever: '永久に削除',
	DeleteInsurancePayerConfirmationMessage:
		'{payer} を削除すると、保険支払者のリストから削除されます。この操作は永続的であり、復元できません。',
	DeleteInsurancePayerFailure: '保険支払者の削除に失敗しました',
	DeleteInsurancePolicyConfirmationMessage: 'これにより、保険契約は永久に削除されます。',
	DeleteInvoiceConfirmationDescription:
		'この操作は元に戻せません。請求書とそれに関連するすべての支払いが完全に削除されます。',
	DeleteLocationConfirmation:
		'場所の削除は永久的な操作です。削除すると、その場所にアクセスできなくなります。この操作は元に戻せません。',
	DeletePayer: '支払人を削除',
	DeletePracticeWorkspace: '練習ワークスペースを削除する',
	DeletePracticeWorkspaceDescription: 'この練習ワークスペースを完全に削除します',
	DeletePracticeWorkspaceFailedSnackbar: 'ワークスペースの削除に失敗しました',
	DeletePracticeWorkspaceModalCancelButton: 'はい、サブスクリプションをキャンセルします',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'ワークスペースの削除を続行する前に、まずサブスクリプションをキャンセルする必要があります。',
	DeletePracticeWorkspaceModalConfirmButton: 'はい、ワークスペースを完全に削除します',
	DeletePracticeWorkspaceModalDescription:
		'{name} ワークスペースは完全に削除され、すべてのチームメンバーはアクセスできなくなります。削除が行われる前に、必要な重要なデータやメッセージをダウンロードしてください。この操作は元に戻せません。',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'この決定は、以下の理由により行われました。',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: '理由',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'アカウントを削除する理由を教えてください。',
	DeletePracticeWorkspaceModalTitle: '本気ですか？',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'チームメンバー全員のアクセスが削除されました',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} は正常に削除されました。',
	DeletePublicTemplateContent:
		'これにより、パブリック テンプレートのみが削除され、チームのテンプレートは削除されません。',
	DeleteRecurringAppointmentModalTitle: '繰り返し予定を削除する',
	DeleteRecurringEventModalTitle: '繰り返し会議を削除する',
	DeleteRecurringReminderModalTitle: '繰り返しリマインダーを削除する',
	DeleteRecurringTaskModalTitle: '繰り返しタスクを削除する',
	DeleteReminderConfirmation:
		'これは永続的な操作です。リマインダーを削除すると、アクセスできなくなります。新しい予定にのみ影響します。',
	DeleteSection: 'セクションを削除',
	DeleteSectionInfo:
		'セクション **{section}** を削除すると、その中のすべての既存のフィールドが非表示になります。 この操作は元に戻せません。',
	DeleteSectionWarning: 'コアフィールドは削除できません。既存のセクション**{section}**に移動されます。',
	DeleteServiceFailure: 'サービスの削除に失敗しました',
	DeleteServiceSuccess: 'サービスを削除しました',
	DeleteStaffScheduleOverrideDescription:
		'{value} の日付上書きを削除すると、スケジュールから削除され、オンラインサービスの提供状況が変わる可能性があります。この操作は取り消すことができません。',
	DeleteSuperbillConfirmationDescription: 'この操作は元に戻せません。Superbill の領収書は永久に削除されます。',
	DeleteSuperbillFailure: 'スーパービルの領収書を削除できませんでした',
	DeleteSuperbillSuccess: 'スーパービルの領収書を削除しました',
	DeleteTaxRateConfirmationDescription: 'この税率を削除してもよろしいですか?',
	DeleteTemplateContent: 'この操作は元に戻せません',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} は正常に削除されました。',
	DeleteTemplateSuccessMessage: '{templateTitle} は正常に削除されました。',
	DeleteTemplateTitle: 'このテンプレートを削除してもよろしいですか?',
	DeleteTranscript: 'トランスクリプトを削除',
	DeleteWorkspace: 'ワークスペースを削除',
	Deleted: '削除されました',
	DeletedBy: '削除者',
	DeletedContact: '削除された連絡先',
	DeletedOn: '削除日',
	DeletedStatusLabel: '削除ステータス',
	DeletedUserTooltip: 'このクライアントは削除されました',
	DeliveryMethod: '配送方法',
	Demo: 'デモ',
	Denied: '拒否されました',
	Dental: '歯科',
	DentalAssistant: '歯科助手',
	DentalHygienist: '歯科衛生士',
	Dentist: '歯医者',
	Dentists: '歯科医',
	Description: '説明',
	DescriptionMustNotExceed: '説明は {max} 文字を超えてはいけません。',
	DetailDurationWithStaff: '{duration} 分{staffName, select, null {} other { {staffName} と共に}}',
	Details: '詳細',
	Devices: 'デバイス',
	Diagnosis: '診断',
	DiagnosisAndBillingItems: '診断 ',
	DiagnosisCode: '診断コード',
	DiagnosisCodeErrorMessage: '診断コードが必要です',
	DiagnosisCodeSelectorPlaceholder: 'ICD-10診断コードから検索して追加する',
	DiagnosisCodeSelectorTooltip: '診断コードは、保険償還のためのスーパービルの領収書を自動化するために使用されます。',
	DiagnosticCodes: '診断コード',
	Dictate: '口述する',
	DictatingIn: 'ディクテーション',
	Dictation: 'ディクテーション',
	DidNotAttend: '出席しなかった',
	DidNotComplete: '完了しませんでした',
	DidNotProviderEnoughValue: '十分な価値を提供しなかった',
	DidntProvideEnoughValue: '十分な価値を提供しなかった',
	DieteticsOrNutrition: '栄養学または栄養学',
	Dietician: '栄養士',
	Dieticians: '栄養士',
	Dietitian: '栄養士',
	DigitalSign: 'ここにサインしてください：',
	DigitalSignHelp: '(クリック/押し下げて描画します)',
	DirectDebit: '自動引き落とし',
	DirectTextLink: '直接テキストリンク',
	Disable: '無効にする',
	DisabledEmailInfo: 'あなたのアカウントは当社によって管理されていないため、メールアドレスを更新することはできません',
	Discard: '破棄',
	DiscardChanges: '変更を破棄',
	DiscardDrafts: '下書きを破棄',
	Disconnect: '切断',
	DisconnectAppConfirmation: 'このアプリを切断しますか?',
	DisconnectAppConfirmationDescription: 'このアプリを切断してもよろしいですか？',
	DisconnectAppConfirmationTitle: 'アプリを切断する',
	Discount: '割引',
	DisplayCalendar: 'Carepatron での表示',
	DisplayName: '表示名',
	DisplayedToClients: 'クライアントに表示される',
	DiversionalTherapist: '娯楽療法士',
	DoItLater: '後でやる',
	DoNotImport: '輸入しない',
	DoNotSend: '送信しない',
	DoThisLater: 'これは後でやってください。',
	DoYouWantToEndSession: 'セッションを続行しますか、それとも今すぐ終了しますか?',
	Doctor: '医者',
	Doctors: '医師',
	DoesNotRepeat: '繰り返さない',
	DoesntWorkWellWithExistingTools: '既存のツールやワークフローとうまく連携しない',
	DogWalker: '犬の散歩係',
	Done: '完了',
	DontAllowClientsToCancel: 'クライアントのキャンセルを許可しない',
	DontHaveAccount: 'アカウントをお持ちではありませんか?',
	DontSend: '送らないでください。',
	Double: 'ダブル',
	DowngradeTo: '{plan} にダウングレードする',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'申し訳ございませんが、チーム メンバーが多すぎるため、プランをダウングレードすることはできません。プロバイダーからメンバーの一部を削除して、もう一度お試しください。',
	Download: 'ダウンロード',
	DownloadAsPdf: 'PDFとしてダウンロード',
	DownloadERA: 'ERA をダウンロードする',
	DownloadPDF: 'PDFをダウンロード',
	DownloadTemplateFileName: 'ケアパトロン切り替えテンプレート.csv',
	DownloadTemplateTileDescription:
		'クライアントを整理してアップロードするには、スプレッドシートテンプレートを使用してください。',
	DownloadTemplateTileLabel: 'テンプレートをダウンロードする',
	Downloads: '{number, plural, one {<span>#</span> ダウンロード} other {<span>#</span> ダウンロード}}',
	DoxyMe: 'ドキシ',
	Draft: '下書き',
	DraftResponses: '回答案',
	DraftSaved: '保存された変更',
	DragAndDrop: 'ドラッグアンドドロップ',
	DragDropText: 'ドラッグアンドドロップによる健康関連文書',
	DragToMove: 'ドラッグして移動',
	DragToMoveOrActivate: 'ドラッグして移動またはアクティブ化します',
	DramaTherapist: 'ドラマセラピスト',
	DropdownFormFieldPlaceHolder: 'リストからオプションを選択してください',
	DropdownFormPrimaryText: '落ちる',
	DropdownFormSecondaryText: 'オプションのリストから選択',
	DropdownTextFieldError: 'ドロップダウンオプションのテキストは空にできません',
	DropdownTextFieldPlaceholder: 'ドロップダウンオプションを追加する',
	Due: '期日',
	DueDate: '期日',
	Duplicate: '重複',
	DuplicateAvailabilityScheduleFailure: '空きスケジュールの複製に失敗しました',
	DuplicateAvailabilityScheduleSuccess: '{name} スケジュールを正常に複製しました。',
	DuplicateClientBannerAction: 'レビュー',
	DuplicateClientBannerDescription:
		'重複するクライアント レコードをマージすると、すべての一意のクライアント情報が保持され、1 つに統合されます。',
	DuplicateClientBannerTitle: '{count} 重複が見つかりました',
	DuplicateColumn: '重複した列',
	DuplicateContactFieldSettingErrorSnackbar: 'セクション名が重複することはできません',
	DuplicateContactFieldSettingFieldErrorSnackbar: '重複したフィールド名は使用できない',
	DuplicateEmailError: '重複メール',
	DuplicateHeadingName: 'セクション {name} はすでに存在します。',
	DuplicateInvoiceNumberErrorCodeSnackbar: '同じ「請求書番号」の請求書がすでに存在します。',
	DuplicateRecords: '重複レコード',
	DuplicateRecordsMinimumError: '少なくとも 2 つのレコードを選択する必要があります',
	DuplicateRecordsRequired: '分離するレコードを少なくとも1つ選択してください',
	DuplicateServiceFailure: '<strong>{title}</strong> の複製に失敗しました。',
	DuplicateServiceSuccess: '正常に複製されました <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'フォルダの複製に成功しました。',
	DuplicateTemplateSuccess: 'テンプレートの複製に成功しました',
	DurationInMinutes: '{duration}分',
	Dx: 'DX',
	DxCode: 'DXコード',
	DxCodeSelectPlaceholder: 'ICD-10コードから検索して追加する',
	EIN: 'EIN',
	EMG: '筋電図',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'なし',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>調整が含まれています</i>} other {}}',
	EarnReferralCredit: '${creditAmount} を獲得する',
	Economist: 'エコノミスト',
	Edit: '編集',
	EditArrangements: 'アレンジを編集する',
	EditBillTo: '請求書を編集する',
	EditClient: 'クライアントを編集',
	EditClientFileModalDescription:
		'「閲覧可能」チェックボックスのオプションを選択して、このファイルへのアクセスを編集します',
	EditClientFileModalTitle: 'ファイルを編集',
	EditClientNoteModalDescription:
		'メモの内容を編集します。メモを表示できるユーザーを変更するには、「閲覧可能」セクションを使用します。',
	EditClientNoteModalTitle: 'メモを編集',
	EditConnectedAppButton: '編集',
	EditConnections: '接続を編集{account, select, null { } undefined { } other { {account} の}}',
	EditContactDetails: '連絡先の詳細を編集する',
	EditContactFormIsClientLabel: 'クライアントに変換',
	EditContactIsClientCheckboxWarning: '連絡先をクライアントに変換すると元に戻すことはできません',
	EditContactIsClientWanringModal:
		'この連絡先をクライアントに変換すると、元に戻すことはできません。ただし、関係はすべてそのまま残り、メモ、ファイル、その他のドキュメントにアクセスできるようになります。',
	EditContactRelationship: '連絡先の関係を編集',
	EditDetails: '詳細を編集する',
	EditFileModalTitle: '{name} のファイルを編集する',
	EditFolder: 'フォルダを編集',
	EditFolderDescription: 'フォルダの名前を次のように変更します...',
	EditInvoice: '請求書を編集',
	EditInvoiceDetails: '請求書の詳細を編集する',
	EditLink: 'リンクを編集',
	EditLocation: '場所を編集',
	EditLocationFailure: '位置情報を更新できませんでした',
	EditLocationSucess: '場所の更新に成功しました',
	EditPaymentDetails: '支払いの詳細を編集する',
	EditPaymentMethod: '支払い方法を編集',
	EditPersonalDetails: '個人情報を編集する',
	EditPractitioner: '実践者を編集',
	EditProvider: 'プロバイダーを編集',
	EditProviderDetails: 'プロバイダーの詳細を編集する',
	EditRecurrence: '再発編集',
	EditRecurringAppointmentModalTitle: '繰り返し予定を編集する',
	EditRecurringEventModalTitle: '繰り返し会議を編集',
	EditRecurringReminderModalTitle: '繰り返しリマインダーを編集',
	EditRecurringTaskModalTitle: '繰り返しタスクを編集する',
	EditRelationshipModalTitle: '関係を編集',
	EditService: '編集サービス',
	EditServiceFailure: '新しいサービスを更新できませんでした',
	EditServiceGroup: 'コレクションを編集',
	EditServiceGroupFailure: 'コレクションの更新に失敗しました',
	EditServiceGroupSuccess: 'コレクションを更新しました',
	EditServiceSuccess: '新しいサービスの更新に成功しました',
	EditStaffDetails: 'スタッフの詳細を編集',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'電子メール アドレスを更新できません。新しい電子メール アドレスで新しいチーム メンバーを作成してください。',
	EditSubscriptionBilledQuantity: '請求数量',
	EditSubscriptionBilledQuantityValue: '{billedUsers} チームメンバー',
	EditSubscriptionLimitedTimeOffer: '期間限定！6か月間50％オフ。',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'チームメンバーを追加または削除すると、サブスクリプション料金が調整されます。',
	EditSubscriptionUpgradeContent:
		'新しいプランと請求期間にアカウントがすぐに更新されます。価格の変更は、保存されたお支払い方法に自動的に請求されるか、アカウントにクレジットされます。',
	EditSubscriptionUpgradePlanTitle: 'サブスクリプションプランのアップグレード',
	EditSuperbillReceipt: 'スーパービルを編集',
	EditTags: 'タグを編集',
	EditTemplate: 'テンプレートを編集',
	EditTemplateFolderSuccessMessage: 'テンプレートフォルダが正常に編集されました',
	EditValue: '{value} を編集する',
	Edited: '編集済み',
	Editor: '編集者',
	EditorAlertDescription:
		'サポートされていない形式が検出されました。アプリを再読み込みするか、サポート チームにお問い合わせください。',
	EditorAlertTitle: 'このコンテンツを表示できません',
	EditorPlaceholder:
		'書き始め、テンプレートを選択するか、基本ブロックを追加して、クライアントからの回答を取得します。',
	EditorTemplatePlaceholder: '書き始めるか、コンポーネントを追加してテンプレートを構築します',
	EditorTemplateWithSlashCommandPlaceholder:
		'クライアントの回答を収集するために、書き始めたり、基本的なブロックを追加したりします。  クイックアクションにはスラッシュコマンド (/) を使用します。',
	EditorWithSlashCommandPlaceholder:
		'書き始めるか、テンプレートを選択するか、基本ブロックを追加してクライアントの応答を取得します。クイックアクションにはスラッシュコマンド ( / ) を使用します。',
	EffectiveStartEndDate: '有効開始日 - 終了日',
	ElectricalEngineer: '電気技師',
	Electronic: '電子',
	ElectronicSignature: '電子署名',
	ElementarySchoolTeacher: '小学校教師',
	Eligibility: '適格性',
	Email: 'Eメール',
	EmailAlreadyExists: 'メールアドレスはすでに存在します',
	EmailAndSms: 'メール ',
	EmailBody: 'メール本文',
	EmailContainsIgnoredDescription:
		'次のメールには、現在無視されている {count, plural, one {送信者} other {送信者}} のメールが含まれています。続行しますか?',
	EmailInviteToPortalBody: `こんにちは {contactName} 様、
以下のリンクから安全なクライアントポータルにログインし、簡単にケアを管理してください。

敬具

{providerName}`,
	EmailInviteToPortalSubject: '{providerName}へようこそ',
	EmailInvoice: '請求書をメールで送信',
	EmailInvoiceOverdueBody: `こんにちは {contactName}
請求書 {invoiceNumber} の支払期限が過ぎています。
下記のリンクからオンラインで請求書をお支払いください。

ご質問がある場合は、お知らせください。

よろしくお願いいたします。
{providerName}`,
	EmailInvoicePaidBody: `こんにちは {contactName}
請求書 {invoiceNumber} は支払われました。
請求書の副本を表示およびダウンロードするには、以下のリンクをクリックしてください。

ご質問がありましたら、お知らせください。

敬具
{providerName}`,
	EmailInvoiceProcessingBody: `こんにちは {contactName}
請求書 {invoiceNumber} が準備できました。
下記のリンクから請求書をご覧いただけます。

ご質問がありましたら、お知らせください。

よろしくお願いいたします。
{providerName}`,
	EmailInvoiceUnpaidBody: `こんにちは {contactName}様
請求書 {invoiceNumber} は、{dueDate} までにお支払いください。
請求書をオンラインでご覧いただき、お支払いいただくには、以下のリンクをクリックしてください。

ご質問等ございましたら、お気軽にお問い合わせください。

よろしくお願いいたします。
{providerName}`,
	EmailInvoiceVoidBody: `こんにちは {contactName} 様
請求書 {invoiceNumber} は無効です。
この請求書を見るには、下のリンクをクリックしてください。

ご不明な点がございましたら、お気軽にお問い合わせください。

敬具
{providerName}`,
	EmailNotFound: 'メールアドレスが見つかりません',
	EmailNotVerifiedErrorCodeSnackbar: 'アクションを実行できません。メール アドレスを確認する必要があります。',
	EmailNotVerifiedTitle: 'メールアドレスが確認されていません',
	EmailSendClientIntakeBody: `こんにちは {contactName} 様,
{providerName} から、情報提供と重要な書類の確認をお願いしたいことがございます。下記のリンクから開始してください。

敬具,

{providerName}`,
	EmailSendClientIntakeSubject: '{providerName}へようこそ',
	EmailSuperbillReceipt: 'スーパービルにメールを送信',
	EmailSuperbillReceiptBody: `こんにちは {contactName}様、
{providerName}より、{date}の償還領収書のコピーをお送りしました。

この書類は直接保険会社にダウンロードして提出できます。`,
	EmailSuperbillReceiptFailure: 'スーパービルの領収書を送信できませんでした',
	EmailSuperbillReceiptSubject: '{providerName} は償還領収書の明細書を送信しました。',
	EmailSuperbillReceiptSuccess: 'スーパービルの領収書が正常に送信されました',
	EmailVerificationDescription: '現在、アカウント<span>を確認中</span>です',
	EmailVerificationNotification: '確認メールが {email} に送信されました。',
	EmailVerificationSuccess: 'メールアドレスが {email} に変更されました。',
	Emails: 'メール',
	EmergencyContact: '緊急連絡',
	EmployeesIdentificationNumber: '従業員識別番号',
	EmploymentStatus: '雇用状況',
	EmptyAgendaViewDescription: '表示するイベントはありません。<mark>今すぐ予約する</mark>',
	EmptyBin: '空のビン',
	EmptyBinConfirmationDescription:
		'空のゴミ箱は、削除済みにある **{total} 件の会話** をすべて削除します。この操作は取り消せません。',
	EmptyBinConfirmationTitle: '会話を完全に削除する',
	EmptyTrash: 'ゴミ箱を空にする',
	Enable: '有効にする',
	EnableCustomServiceAvailability: 'サービスの可用性を有効にする',
	EnableCustomServiceAvailabilityDescription: '例：初回の予約は毎日午前9時から10時までのみ予約できます',
	EndCall: '通話終了',
	EndCallConfirmationForCreator: 'あなたは通話の発信者なので、全員に対して通話を終了することになります。',
	EndCallConfirmationHasActiveAttendees:
		'通話を終了しようとしていますが、クライアントがすでに参加しています。あなたも参加しますか?',
	EndCallForAll: '全員の通話終了',
	EndDate: '終了日',
	EndDictation: 'ディクテーション終了',
	EndOfLine: 'もう予約は不要',
	EndSession: 'セッション終了',
	EndTranscription: '転写終了',
	Ends: '終了',
	EndsOnDate: '{date} までです。',
	Enrol: '登録',
	EnrollmentRejectedSubject: '{payerName}への登録は拒否されました。',
	Enrolment: '摂取',
	Enrolments: '登録',
	EnrolmentsDescription: '支払者とのプロバイダー登録の閲覧と管理。',
	EnterAName: '名前を入力してください...',
	EnterFieldLabel: 'フィールドラベルを入力してください...',
	EnterPaymentDetailsDescription: 'ユーザーを追加または削除すると、サブスクリプション料金は自動的に調整されます。',
	EnterSectionName: 'セクション名を入力してください...',
	EnterSubscriptionPaymentDetails: '支払いの詳細を入力してください',
	EnvironmentalScientist: '環境科学者',
	Epidemiologist: '疫学者',
	Eraser: '消しゴム',
	Error: 'エラー',
	ErrorBoundaryAction: 'ページを再読み込み',
	ErrorBoundaryDescription: 'ページを更新してもう一度お試しください。',
	ErrorBoundaryTitle: 'おっと！問題が発生しました',
	ErrorCallNotFound: '通話が見つかりません。有効期限が切れたか、作成者が通話を終了した可能性があります。',
	ErrorCannotAccessCallUninvitedCode: '申し訳ありませんが、この通話に招待されていないようです。',
	ErrorFileUploadCustomMaxFileCount: '一度に {count} ファイル以上アップロードできません。',
	ErrorFileUploadCustomMaxFileSize: 'ファイルサイズは{mb} MBを超えることはできません。',
	ErrorFileUploadInvalidFileType: 'ウイルスや有害なソフトウェアが含まれている可能性のある無効なファイルタイプ',
	ErrorFileUploadMaxFileCount: '一度に150以上のファイルをアップロードすることはできません',
	ErrorFileUploadMaxFileSize: 'ファイルサイズは100 MBを超えることはできません',
	ErrorFileUploadNoFileSelected: 'アップロードするファイルを選択してください',
	ErrorInvalidNationalProviderId: '提供された National Provider Id は無効です。',
	ErrorInvalidPayerId: '提供された支払者 ID は無効です。',
	ErrorInvalidTaxNumber: '入力された税務番号が無効です。',
	ErrorInviteExistingProviderStaffCode: 'このユーザーはすでにワークスペースにいます。',
	ErrorInviteStaffExistingUser: '申し訳ございませんが、追加したユーザーは既にシステム内に存在しているようです。',
	ErrorOnlySingleCallAllowed: '同時に1つの通話のみを開始できます。',
	ErrorPayerNotFound: '支払人が見つかりません',
	ErrorProfilePhotoMaxFileSize: 'アップロードに失敗しました。ファイルサイズの制限に達しました - 5MB',
	ErrorRegisteredExistingUser: '申し訳ございませんが、すでに登録されているようです。',
	ErrorUserSignInIncorrectCredentials: 'メールアドレスまたはパスワードが無効です。もう一度お試しください。',
	ErrorUserSigninGeneric: '申し訳ございません。問題が発生しました。',
	ErrorUserSigninUserNotConfirmed:
		'申し訳ありませんが、サインインする前にアカウントを確認する必要があります。手順については受信トレイを確認してください。',
	Errors: 'エラー',
	EssentialPlanInclusionFive: 'テンプレートのインポート',
	EssentialPlanInclusionFour: '5 GBのストレージ',
	EssentialPlanInclusionHeader: 'すべて無料  ',
	EssentialPlanInclusionOne: '自動およびカスタムリマインダー',
	EssentialPlanInclusionSix: '優先サポート',
	EssentialPlanInclusionThree: 'ビデオチャット',
	EssentialPlanInclusionTwo: '双方向カレンダー同期',
	EssentialSubscriptionPlanSubtitle: '必需品で練習をシンプルに',
	EssentialSubscriptionPlanTitle: '不可欠',
	Esthetician: 'エステティシャン',
	Estheticians: 'エステティシャン',
	EstimatedArrivalDate: '予定到着日 {numberOfDaysFromNow} 日後',
	Ethnicity: '民族',
	Europe: 'ヨーロッパ',
	EventColor: '会議カラー',
	EventName: 'イベント名',
	EventType: 'イベントタイプ',
	Every: 'すべての',
	Every2Weeks: '2週間ごと',
	EveryoneInWorkspace: '職場の全員',
	ExercisePhysiologist: '運動生理学者',
	Existing: '既存',
	ExistingClients: '既存のクライアント',
	ExistingFolders: '既存のフォルダ',
	ExpiredPromotionCode: 'プロモーションコードの有効期限が切れました',
	ExpiredReferralDescription: '紹介期限が切れました',
	ExpiredVerificationLink: '確認リンクの期限が切れました',
	ExpiredVerificationLinkDescription: `申し訳ございませんが、クリックした確認リンクの有効期限が切れています。リンクをクリックするまでに 24 時間以上待った場合、またはすでにリンクを使用してメール アドレスを確認している場合に、この状態になることがあります。

メールアドレスを確認するには、新しい確認リンクをリクエストしてください。`,
	ExpiryDateRequired: '有効期限が必要です',
	ExploreFeature: 'まず何を探検したいですか?',
	ExploreOptions: '探索するオプションを 1 つ以上選択します...',
	Export: '輸出',
	ExportAppointments: '予定をエクスポート',
	ExportClaims: '輸出請求',
	ExportClaimsFilename: '請求 {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'エラーのため、ファイルをダウンロードできませんでした。',
	ExportClientsDownloadFailureSnackbarTitle: 'ダウンロードに失敗しました',
	ExportClientsFailureSnackbarDescription: '後でもう一度お試しいただくか、サポートにお問い合わせください。',
	ExportClientsFailureSnackbarTitle: 'エクスポートに失敗しました',
	ExportClientsModalDescription: `このデータ エクスポート プロセスは、エクスポートするデータの量に応じて数分かかる場合があります。ダウンロードの準備が整うと、リンクが記載されたメール通知が届きます。

クライアントデータのエクスポートを続行しますか?`,
	ExportClientsModalTitle: 'クライアントデータをエクスポートする',
	ExportCms1500: 'CMS1500のエクスポート',
	ExportContactFailedNotificationSubject: 'データのエクスポートに失敗しました',
	ExportFailed: 'エクスポートに失敗しました',
	ExportGuide: '輸出ガイド',
	ExportInvoiceFileName: '取引 {fromDate}-{toDate}.csv',
	ExportPayments: '輸出支払い',
	ExportPaymentsFilename: '支払 {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'ドキュメントをダウンロードする準備ができました。',
	ExportPrintWaitMessage: 'ドキュメントを準備しています。お待ちください...',
	ExportTextOnly: 'テキストのみエクスポート',
	ExportTransactions: '輸出取引',
	Exporting: 'エクスポート',
	ExportingData: 'データのエクスポート',
	ExtendedFamilyMember: '拡大家族',
	External: '外部の',
	ExternalEventInfoBanner: 'この予約は同期されたカレンダーからのもので、項目が不足している可能性があります。',
	ExtraLarge: '特大',
	FECABlackLung: 'FECA Black Lung',
	Failed: '失敗した',
	FailedToJoinTheMeeting: '会議に参加できませんでした。',
	FallbackPageDescription: `このページは存在しないようです。最新の情報に更新するには、{refreshButton} をクリックしてください。
それ以外の場合は、Carepatron サポートにご連絡ください。`,
	FallbackPageDescriptionUpdateButton: 'リフレッシュ',
	FallbackPageTitle: 'おっと...',
	FamilyPlanningService: '家族計画サービス',
	FashionDesigner: 'ファッションデザイナー',
	FastTrackInvoicingAndBilling: '請求書と請求を迅速に処理',
	Father: '父親',
	FatherInLaw: '義父',
	Favorite: 'お気に入り',
	FeatureBannerCalendarTile1ActionLabel: 'オンライン予約 • 2分',
	FeatureBannerCalendarTile1Description: 'メールやテキストメッセージ、またはウェブサイトに空き状況を追加するだけです',
	FeatureBannerCalendarTile1Title: '顧客がオンラインで予約できるようにする',
	FeatureBannerCalendarTile2ActionLabel: 'リマインダーを自動化する • 2 分',
	FeatureBannerCalendarTile2Description: '自動リマインダーでクライアントの出席率を向上',
	FeatureBannerCalendarTile2Title: '欠席者を減らす',
	FeatureBannerCalendarTile3Title: 'スケジュールとワークフロー',
	FeatureBannerCalendarTitle: 'スケジュールを簡単に作成',
	FeatureBannerCallsTile1ActionLabel: '遠隔医療通話を開始する',
	FeatureBannerCallsTile1Description:
		'リンクだけでクライアントにアクセスできます。ログインやパスワードなどの手間は不要です。',
	FeatureBannerCallsTile1Title: 'どこからでもビデオ通話を開始',
	FeatureBannerCallsTile2ActionLabel: 'アプリを接続 • 4 分',
	FeatureBannerCallsTile2Description: '他の優先遠隔医療プロバイダーとシームレスに接続',
	FeatureBannerCallsTile2Title: '遠隔医療アプリを接続する',
	FeatureBannerCallsTile3Title: '通話',
	FeatureBannerCallsTitle: 'いつでもどこでもクライアントとつながる',
	FeatureBannerClientsTile1ActionLabel: '今すぐインポート • 2 分',
	FeatureBannerClientsTile1Description: '自動クライアントインポートツールですぐに始めましょう',
	FeatureBannerClientsTile1Title: '顧客はたくさんいますか?',
	FeatureBannerClientsTile2ActionLabel: '摂取量をカスタマイズする • 2 分',
	FeatureBannerClientsTile2Description: '受付書類をなくし、クライアントの体験を向上',
	FeatureBannerClientsTile2Title: 'ペーパーレス化',
	FeatureBannerClientsTile3Title: 'クライアントポータル',
	FeatureBannerClientsTitle: 'すべては顧客から始まります',
	FeatureBannerHeader: 'コミュニティによる、コミュニティのための！',
	FeatureBannerInvoicesTile1ActionLabel: '支払いを自動化する • 2 分',
	FeatureBannerInvoicesTile1Description: '自動支払いで気まずい会話を避ける',
	FeatureBannerInvoicesTile1Title: '2倍早く支払いを受ける',
	FeatureBannerInvoicesTile2ActionLabel: 'キャッシュフローを追跡する • 2 分',
	FeatureBannerInvoicesTile2Description: '未払いの請求書を減らし、収入を把握しましょう',
	FeatureBannerInvoicesTile2Title: '収入を簡単に追跡',
	FeatureBannerInvoicesTile3Title: '請求と支払い',
	FeatureBannerInvoicesTitle: '心配事が一つ減る',
	FeatureBannerSubheader:
		'私たちのチームとコミュニティによって作成された Carepatron テンプレート。新しいリソースを試したり、独自のリソースを共有したりしてください。',
	FeatureBannerTeamTile1ActionLabel: '今すぐ招待',
	FeatureBannerTeamTile1Description: 'チームメンバーをアカウントに招待して共同作業を簡単にしましょう',
	FeatureBannerTeamTile1Title: 'チームをまとめる',
	FeatureBannerTeamTile2ActionLabel: '空き状況を設定する • 2 分',
	FeatureBannerTeamTile2Description: 'チームの空き状況を管理して二重予約を回避',
	FeatureBannerTeamTile2Title: '空き状況を設定する',
	FeatureBannerTeamTile3ActionLabel: '権限の設定 • 2 分',
	FeatureBannerTeamTile3Description: 'コンプライアンスのために機密データとツールへのアクセスを制御する',
	FeatureBannerTeamTile3Title: '権限とアクセスをカスタマイズする',
	FeatureBannerTeamTitle: '一人では偉大なことは達成できない',
	FeatureBannerTemplatesTile1ActionLabel: 'ライブラリを探索 • 2 分',
	FeatureBannerTemplatesTile1Description: 'カスタマイズ可能なリソースの素晴らしいライブラリから選択 ',
	FeatureBannerTemplatesTile1Title: '作業負荷を軽減',
	FeatureBannerTemplatesTile2ActionLabel: '今すぐ送信 • 2 分',
	FeatureBannerTemplatesTile2Description: '美しいテンプレートをクライアントに送信して完成させる',
	FeatureBannerTemplatesTile2Title: 'ドキュメントを楽しくする',
	FeatureBannerTemplatesTile3Title: 'テンプレート',
	FeatureBannerTemplatesTitle: 'あらゆる用途のテンプレート',
	FeatureLimitBannerDescription:
		'今すぐアップグレードして、{featureName} の作成と管理を中断することなく続け、Carepatron を最大限に活用しましょう！',
	FeatureLimitBannerTitle: 'あなたは {percentage}% {featureName} 制限に達しています。',
	FeatureRequiresUpgrade: 'この機能はアップグレードが必要です。',
	Fee: '手数料',
	Female: '女性',
	FieldLabelTooltip: '{isHidden, select, true {表示} other {非表示}} フィールド ラベル',
	FieldName: 'フィールド名',
	FieldOptionsFirstPart: '最初の言葉',
	FieldOptionsMiddlePart: '中間語',
	FieldOptionsSecondPart: '最後の言葉',
	FieldOptionsWholeField: 'フィールド全体',
	FieldType: 'フィールドタイプ',
	Fields: '田畑',
	File: 'ファイル',
	FileDownloaded: '<strong>{fileName}</strong> ダウンロード済み',
	FileInvalidType: 'ファイルはサポートされていません。',
	FileNotFound: 'ファイルが見つかりません',
	FileNotFoundDescription: 'お探しのファイルは利用できませんか、削除されました',
	FileTags: 'ファイルのタグ',
	FileTagsHelper: 'タグはすべてのファイルに適用されます',
	FileTooLarge: 'ファイルが大きすぎます。',
	FileTooSmall: 'ファイルが小さすぎます。',
	FileUploadComplete: '完了',
	FileUploadFailed: '失敗した',
	FileUploadInProgress: '読み込み中',
	FileUploadedNotificationSubject: '{actorProfileName} はファイルをアップロードしました。',
	Files: 'ファイル',
	FillOut: '記入してください',
	Filter: 'フィルター',
	FilterBy: 'フィルター',
	FilterByAmount: '金額で絞り込む',
	FilterByClient: 'クライアントでフィルタリング',
	FilterByLocation: '場所で絞り込む',
	FilterByService: 'サービスで絞り込む',
	FilterByStatus: 'ステータスでフィルタリング',
	FilterByTags: 'タグでフィルタリング',
	FilterByTeam: 'チームでフィルタリング',
	Filters: 'フィルター',
	FiltersAppliedToView: '適用されたフィルターを表示します',
	FinalAppointment: '最終予約',
	FinalizeImport: 'インポートを完了する',
	FinancialAnalyst: '金融アナリスト',
	Finish: '仕上げる',
	Firefighter: '消防士',
	FirstName: 'ファーストネーム',
	FirstNameLastInitial: '名、姓のイニシャル',
	FirstPerson: '1人目',
	FolderName: 'フォルダ名',
	Folders: 'フォルダ',
	FontFamily: 'フォントファミリー',
	ForClients: 'クライアント向け',
	ForClientsDetails: '介護や健康関連のサービスを受けている',
	ForPractitioners: '実践者向け',
	ForPractitionersDetails: '診療所を管理し、成長させる',
	ForgotPasswordConfirmAccessCode: '確認コード',
	ForgotPasswordConfirmNewPassword: '新しいパスワード',
	ForgotPasswordConfirmPageDescription:
		'メールアドレス、新しいパスワード、そして先ほど送信した確認コードを入力してください。',
	ForgotPasswordConfirmPageTitle: 'パスワードを再設定する',
	ForgotPasswordPageButton: 'リセットリンクを送信',
	ForgotPasswordPageDescription: 'メールアドレスを入力すると、パスワードをリセットするためのリンクが送信されます。',
	ForgotPasswordPageTitle: 'パスワードを忘れました',
	ForgotPasswordSuccessPageDescription: '受信トレイでリセット リンクを確認してください。',
	ForgotPasswordSuccessPageTitle: 'リセットリンクを送信しました!',
	Form: 'フォーム',
	FormAnswersSentToEmailNotification: '回答のコピーを下記までお送りしました。',
	FormBlocks: 'フォームブロック',
	FormFieldAddOption: 'オプションを追加',
	FormFieldAddOtherOption: '「その他」を追加',
	FormFieldOptionPlaceholder: 'オプション {index}',
	FormStructures: 'フォーム構造',
	Format: 'フォーマット',
	FormatLinkButtonColor: 'ボタンの色',
	Forms: 'フォーム',
	FormsAndAgreementsValidationMessage: '入学手続きを続行するには、すべてのフォームと同意書を完了する必要があります。',
	FormsCategoryDescription: '患者情報の収集と整理のため',
	Frankfurt: 'フランクフルト',
	Free: '無料',
	FreePlanInclusionFive: '自動請求 ',
	FreePlanInclusionFour: 'クライアントポータル',
	FreePlanInclusionHeader: '始める',
	FreePlanInclusionOne: '無制限のクライアント',
	FreePlanInclusionSix: 'ライブサポート',
	FreePlanInclusionThree: '1 GBのストレージ',
	FreePlanInclusionTwo: '遠隔医療',
	FreeSubscriptionPlanSubtitle: '誰でも無料で利用可能',
	FreeSubscriptionPlanTitle: '無料',
	Friday: '金曜日',
	From: 'から',
	FullName: 'フルネーム',
	FunctionalMedicineOrNaturopath: '機能医学または自然療法',
	FuturePaymentsAuthoriseProvider: '今後、{provider} が保存された支払情報を使用できるようにする',
	FuturePaymentsSavePaymentMethod: '将来の支払い用に {paymentMethod} を保存する',
	GST: 'GST',
	Gender: '性別',
	GeneralAvailability: '一般販売開始',
	GeneralAvailabilityDescription:
		'定期的に対応可能な時間を設定します。クライアントは対応可能な時間内にのみサービスを予約できます。',
	GeneralAvailabilityDescription2:
		'特定の時間における空き状況と希望するサービス提供に基づいてスケジュールを作成し、オンライン予約の空き状況を決定します。',
	GeneralAvailabilityInfo: '予約可能な時間によってオンライン予約の可否が決まります',
	GeneralAvailabilityInfo2:
		'グループ イベントを提供するサービスでは、新しいスケジュールを使用して、クライアントがオンラインで予約できる利用可能時間を減らす必要があります。',
	GeneralHoursPlural: '{count} {count, plural, one {時間} other {時間}}',
	GeneralPractitioner: '一般開業医',
	GeneralPractitioners: '一般開業医',
	GeneralServiceAvailabilityInfo: 'このスケジュールは、割り当てられたチームメンバーの動作を上書きします',
	Generate: '生成する',
	GenerateBillingItemsBannerContent: '定期的な予定に対しては、請求項目は自動的に作成されません。',
	GenerateItems: 'アイテムを生成する',
	GenerateNote: 'メモを生成する',
	GenerateNoteConfirmationModalDescription:
		'何をしますか? 新しいメモを作成しますか、既存のメモに追加しますか、それともその内容を置き換えますか?',
	GenerateNoteFor: 'のためのメモを生成する',
	GeneratingContent: 'コンテンツを生成しています...',
	GeneratingNote: 'メモを作成しています…',
	GeneratingTranscript: 'トランスクリプトを生成しています',
	GeneratingTranscriptDescription: '処理には数分かかる場合があります',
	GeneratingYourTranscript: 'トランスクリプトの生成',
	GenericErrorDescription: '{module} を読み込めませんでした。後でもう一度お試しください。',
	GenericErrorTitle: '予期しないエラーが発生しました',
	GenericFailureSnackbar:
		'申し訳ありませんが、予期しない問題が発生しました。ページを更新してもう一度お試しください。',
	GenericSavedSuccessSnackbar: '成功しました。変更を保存しました',
	GeneticCounselor: '遺伝カウンセラー',
	Gerontologist: '老年学者',
	Get50PercentOff: '50% オフ！',
	GetHelp: 'ヘルプを受ける',
	GetStarted: '始める',
	GettingStartedAppointmentTypes: '予約タイプを作成する',
	GettingStartedAppointmentTypesDescription:
		'サービス、価格、請求コードをカスタマイズして、スケジュールと請求を効率化します',
	GettingStartedAppointmentTypesTitle: 'スケジュール ',
	GettingStartedClients: 'クライアントを追加する',
	GettingStartedClientsDescription: '今後の予定、メモ、支払いのためにクライアントと連携しましょう',
	GettingStartedClientsTitle: 'すべては顧客から始まります',
	GettingStartedCreateClient: 'クライアントを作成',
	GettingStartedImportClients: 'クライアントをインポートする',
	GettingStartedInvoices: 'プロのように請求書を作成する',
	GettingStartedInvoicesDescription: `プロフェッショナルな請求書を簡単に作成できます。
ロゴ、所在地、支払い条件を追加します`,
	GettingStartedInvoicesTitle: 'ベストを尽くす',
	GettingStartedMobileApp: 'モバイルアプリを入手する',
	GettingStartedMobileAppDescription:
		'CarepatronをiOS、Android、デスクトップデバイスにダウンロードして、外出先でも簡単にアクセスできます。',
	GettingStartedMobileAppTitle: 'どこからでも仕事ができる',
	GettingStartedNavItem: 'はじめる',
	GettingStartedPageTitle: 'Carepatronを始める',
	GettingStartedPayments: 'オンライン決済を受け入れる',
	GettingStartedPaymentsDescription: `顧客がオンラインで支払いを行えるようにすることで、より早く支払いを受けることができます。
すべての請求書と支払いを一か所で確認`,
	GettingStartedPaymentsTitle: '支払いを簡単に',
	GettingStartedSaveBranding: 'ブランディングを保存',
	GettingStartedSyncCalendars: '他のカレンダーを同期する',
	GettingStartedSyncCalendarsDescription:
		'Carepatronはあなたのカレンダーの競合をチェックし、あなたが空いているときにのみ予約をスケジュールします',
	GettingStartedSyncCalendarsTitle: '常に最新の情報を入手',
	GettingStartedVideo: '紹介ビデオを見る',
	GettingStartedVideoDescription: '小規模チームとそのクライアント向けの初のオールインワンヘルスケアワークスペース',
	GettingStartedVideoTitle: 'Carepatronへようこそ',
	GetttingStartedGetMobileDownload: 'アプリをダウンロード',
	GetttingStartedGetMobileNoDownload:
		'このブラウザには対応していません。iPhone または iPad を使用している場合は、このページを Safari で開いてください。それ以外の場合は、Chrome で開いてみてください。',
	Glossary: '用語集',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmailでは1日にアカウントから送信できるメッセージは500件までです。一部のメッセージが送信できない可能性があります。続行しますか？',
	GoToAppointment: '予約へ進む',
	GoToApps: 'アプリへ移動',
	GoToAvailability: '空室状況を見る',
	GoToClientList: 'クライアントリストへ移動',
	GoToClientRecord: 'クライアントレコードに移動',
	GoToClientSettings: '今すぐクライアント設定に移動',
	GoToInvoiceTemplates: '請求書テンプレートへ移動',
	GoToNotificationSettings: '通知設定に移動',
	GoToPaymentSettings: '支払い設定に移動',
	Google: 'グーグル',
	GoogleCalendar: 'Googleカレンダー',
	GoogleColor: 'Googleカレンダーの色',
	GoogleMeet: 'Google ミート',
	GoogleTagManagerContainerId: 'Google タグ マネージャー コンテナ ID',
	GotIt: 'わかった！',
	Goto: 'へ移動',
	Granddaughter: '孫娘',
	Grandfather: '祖父',
	Grandmother: '祖母',
	Grandparent: '祖父母',
	Grandson: '孫',
	GrantPortalAccess: 'ポータルへのアクセスを許可する',
	GraphicDesigner: 'グラフィックデザイナー',
	Grid: 'グリッド',
	GridView: 'グリッドビュー',
	Group: 'グループ',
	GroupBy: 'グループ化',
	GroupEvent: 'グループイベント',
	GroupEventHelper: 'サービスの参加者制限を設定する',
	GroupFilterLabel: 'すべて {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'グループID',
	GroupInputFieldsFormPrimaryText: 'グループ入力フィールド',
	GroupInputFieldsFormSecondaryText: 'カスタムフィールドを選択または追加する',
	GuideTo: '{value} ガイド',
	GuideToImproveVideoQuality: 'ビデオ品質を向上させるためのガイド',
	GuideToManagingPayers: '支払者の管理',
	GuideToSubscriptionsBilling: 'サブスクリプションの請求ガイド',
	GuideToTroubleshooting: 'トラブルシューティングガイド',
	Guidelines: 'ガイドライン',
	GuidelinesCategoryDescription: '臨床意思決定の指針として',
	HST: 'HST',
	HairStylist: 'ヘアスタイリスト',
	HaveBeenWaiting: '長い間待っていたね',
	HeHim: '彼/彼',
	HeaderAccountSettings: 'プロフィール',
	HeaderCalendar: 'カレンダー',
	HeaderCalls: '通話',
	HeaderClientAppAccountSettings: 'アカウント設定',
	HeaderClientAppCalls: '通話',
	HeaderClientAppMyDocumentation: 'ドキュメント',
	HeaderClientAppMyRelationships: '私の人間関係',
	HeaderClients: 'クライアント',
	HeaderHelp: 'ヘルプ',
	HeaderMoreOptions: 'より多くのオプション',
	HeaderStaff: 'スタッフ',
	HealthCoach: 'ヘルスコーチ',
	HealthCoaches: 'ヘルスコーチ',
	HealthEducator: '健康教育者',
	HealthInformationTechnician: '医療情報技術者',
	HealthPolicyExpert: '健康政策専門家',
	HealthServicesAdministrator: '保健サービス管理者',
	HelpArticles: 'ヘルプ記事',
	HiddenColumns: '非表示の列',
	HiddenFields: '隠しフィールド',
	HiddenSections: '非表示のセクション',
	HiddenSectionsAndFields: '隠しセクション/フィールド',
	HideColumn: '列を非表示',
	HideColumnButton: '列 {value} ボタンを隠す',
	HideDetails: '詳細を隠す',
	HideField: 'フィールドを非表示',
	HideFullAddress: '隠れる',
	HideMenu: 'メニューを非表示',
	HideMergeSummarySidebar: 'マージ概要を隠す',
	HideSection: 'セクションを非表示',
	HideYourView: 'ビューを非表示にする',
	Highlight: 'ハイライトカラー',
	Highlighter: '蛍光ペン',
	History: '履歴',
	HistoryItemFooter: '{actors, select, undefined {{date} at {time}} other {• {date} at {time} {actors}によって}}',
	HistorySidePanelEmptyState: '履歴記録が見つかりません。',
	HistoryTitle: 'アクティビティログ',
	HolisticHealthPractitioner: 'ホリスティックヘルス専門家',
	HomeCaregiver: '在宅介護者',
	HomeHealthAide: 'ホームヘルパー',
	HomelessShelter: 'ホームレスシェルター',
	HourAbbreviation: '{count} {count, plural, one {時間} other {時間}}',
	Hourly: '時間単位',
	HoursPlural: '{age, plural, one {# 時間} other {# 時間}}',
	HowCanWeImprove: 'どのように改善できますか？',
	HowCanWeImproveResponse: 'この回答をどのように改善できますか？',
	HowDidWeDo: 'どうでしたか？',
	HowDoesReferralWork: '紹介プログラムのガイド',
	HowToUseAiSummarise: 'AI Summarizeの使い方',
	HumanResourcesManager: '人事マネージャー',
	Husband: '夫',
	Hypnotherapist: '催眠療法士',
	IVA: 'IVA',
	IgnoreNotification: '通知を無視する',
	IgnoreOnce: '一度無視',
	IgnoreSender: '送信者を無視',
	IgnoreSenderDescription:
		'この送信者からのメッセージを無視して、受信トレイから削除します。この操作は取り消せません。',
	IgnoreSenders: '送信者を無視',
	IgnoreSendersSuccess: '無視されたメールアドレス <mark>{addresses}</mark>',
	Ignored: '無視された',
	Image: '画像',
	Import: '輸入',
	ImportActivity: 'インポートアクティビティ',
	ImportClientSuccessSnackbarDescription: 'ファイルは正常にインポートされました',
	ImportClientSuccessSnackbarTitle: 'インポートに成功しました!',
	ImportClients: 'クライアントをインポート',
	ImportClientsFailureSnackbarDescription: 'エラーのため、ファイルを正常にインポートできませんでした。',
	ImportClientsFailureSnackbarTitle: 'インポートに失敗しました。',
	ImportClientsGuide: 'クライアントのインポートガイド',
	ImportClientsInProgressSnackbarDescription: '完了するまでに 1 分程度しかかかりません。',
	ImportClientsInProgressSnackbarTitle: '{fileName} のインポート',
	ImportClientsModalDescription:
		'データの取得元（デバイス上のファイル、サードパーティのサービス、別のソフトウェア プラットフォームなど）を選択します。',
	ImportClientsModalFileUploadHelperText:
		'サポートされているファイルタイプは{fileTypes}です。 サイズ制限は{fileSizeLimit}です。',
	ImportClientsModalImportGuideLabel: 'クライアントデータのインポートガイド',
	ImportClientsModalStep1Label: 'データソースを選択',
	ImportClientsModalStep2Label: 'ファイルをアップロードする',
	ImportClientsModalStep3Label: 'レビューフィールド',
	ImportClientsModalTitle: 'クライアントデータのインポート',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {クライアント} other {クライアント}} インポート準備完了',
	ImportContactFailedNotificationSubject: 'データのインポートに失敗しました',
	ImportDataSourceSelectorLabel: 'データソースをインポート',
	ImportDataSourceSelectorPlaceholder: 'インポートデータソースを検索または選択',
	ImportExportButton: 'インポート・エクスポート',
	ImportFailed: 'インポートに失敗しました',
	ImportFromAnotherPlatformTileDescription:
		'お客様のファイルのエクスポートをダウンロードし、こちらにアップロードしてください。',
	ImportFromAnotherPlatformTileLabel: '別のプラットフォームからインポートする',
	ImportGuide: '輸入ガイド',
	ImportInProgress: 'インポート処理中',
	ImportProcessing: 'インポート処理中...',
	ImportSpreadsheetDescription:
		'.CSV、.XLS、.XLSXなどの表形式のデータを含むスプレッドシートファイルをアップロードすることで、既存のクライアントリストをCarepatronにインポートできます。',
	ImportSpreadsheetTitle: 'スプレッドシートファイルをインポートする',
	ImportTemplates: 'テンプレートのインポート',
	Importing: 'インポート',
	ImportingCalendarProductEvents: '{product}のイベントをインポート中',
	ImportingData: 'データのインポート',
	ImportingSpreadsheetDescription: '完了するまでに1分ほどかかります',
	ImportingSpreadsheetTitle: 'スプレッドシートをインポートする',
	ImportsInProgress: 'インポート処理中です。',
	InPersonMeeting: '対面ミーティング',
	InProgress: '進行中',
	InTransit: '輸送中',
	InTransitTooltip:
		'輸送中の残高には、Stripe からお客様の銀行口座に支払われたすべての請求書の支払いが含まれます。これらの資金の決済には通常 3 ～ 5 日かかります。',
	Inactive: '非活性',
	InboundOrOutboundCalls: '着信または発信',
	Inbox: '受信トレイ',
	InboxAccessRestricted: 'アクセスが制限されています。権限については受信トレイの所有者にお問い合わせください。',
	InboxAccountAlreadyConnected: '接続しようとしたチャンネルはすでにCarepatronに接続されています',
	InboxAddAttachments: '添付ファイルを追加する',
	InboxAreYouSureDeleteMessage: 'このメッセージを削除してもよろしいですか?',
	InboxBulkCloseSuccess: '{count, plural, one {会話 # を正常に終了しました} other {会話 # を正常に終了しました}}',
	InboxBulkComposeModalTitle: '一括メッセージ作成',
	InboxBulkDeleteSuccess:
		'{count, plural, one {会話 # 件を正常に削除しました} other {会話 # 件を正常に削除しました}}',
	InboxBulkReadSuccess:
		'{count, plural, one {会話 # 件を読み済みとしてマークしました} other {会話 # 件を読み済みとしてマークしました}}',
	InboxBulkReopenSuccess: '{count, plural, one {会話 # を正常に再開しました} other {会話 # を正常に再開しました}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {会話 # を未読としてマークしました} other {会話 # を未読としてマークしました}}',
	InboxChatCreateGroup: 'グループを作成する',
	InboxChatDeleteGroupModalDescription:
		'このグループを削除してもよろしいですか？ すべてのメッセージと添付ファイルが削除されます。',
	InboxChatDeleteGroupModalTitle: 'グループ削除',
	InboxChatDiscardDraft: '下書きを破棄する',
	InboxChatDragDropText: 'ここにファイルをドロップしてアップロード',
	InboxChatGroupConversation: 'グループ会話',
	InboxChatGroupCreateModalDescription:
		'チーム、クライアント、またはコミュニティとメッセージを送信して共同作業を行う新しいグループを開始します。',
	InboxChatGroupCreateModalTitle: 'グループを作成する',
	InboxChatGroupMembers: 'グループメンバー',
	InboxChatGroupModalGroupNameFieldLabel: 'グループ名',
	InboxChatGroupModalGroupNameFieldPlaceholder: '例：カスタマーサポート、管理者',
	InboxChatGroupModalGroupNameFieldRequired: 'このフィールドは必須です。',
	InboxChatGroupModalMembersFieldErrorMinimumOne: '最低限1人のメンバーが必要です。',
	InboxChatGroupModalMembersFieldLabel: 'グループメンバーを選択してください',
	InboxChatGroupModalMembersFieldPlaceholder: 'メンバーを選択する',
	InboxChatGroupUpdateModalTitle: 'グループ管理',
	InboxChatLeaveGroup: 'グループを脱退する',
	InboxChatLeaveGroupModalDescription:
		'本当にこのグループを退会しますか？メッセージやアップデートは届かなくなります。',
	InboxChatLeaveGroupModalTitle: 'グループを脱退する',
	InboxChatLeftGroupMessage: '左のグループメッセージ',
	InboxChatManageGroup: 'グループ管理',
	InboxChatSearchParticipants: '参加者を選ぶ',
	InboxCloseConversationSuccess: '会話を終了しました',
	InboxCompose: '作曲する',
	InboxComposeBulk: '一括メッセージ',
	InboxComposeCarepatronChat: 'メッセンジャー',
	InboxComposeChat: 'チャットを作成する',
	InboxComposeDisabledNoConnection: 'メッセージを送信するには、メールアカウントを接続してください。',
	InboxComposeDisabledNoPermissionTooltip: 'この受信トレイからメッセージを送信する権限がありません。',
	InboxComposeEmail: 'Eメールの作成',
	InboxComposeMessageFrom: 'から',
	InboxComposeMessageRecipientBcc: 'Bcc',
	InboxComposeMessageRecipientCc: 'CC',
	InboxComposeMessageRecipientTo: 'に',
	InboxComposeMessageSubject: '主題：',
	InboxConnectAccountButton: 'メールを接続',
	InboxConnectedDescription: '受信トレイに通信がありません',
	InboxConnectedHeading: 'コミュニケーションを開始するとすぐに会話がここに表示されます',
	InboxConnectedHeadingClientView: '顧客とのコミュニケーションを効率化',
	InboxCreateFirstInboxButton: '最初の受信トレイを作成する',
	InboxCreationSuccess: '受信トレイが正常に作成されました',
	InboxDeleteAttachment: '添付ファイルを削除',
	InboxDeleteConversationSuccess: '会話を削除しました',
	InboxDeleteMessage: 'メッセージを削除しますか?',
	InboxDirectMessage: 'ダイレクトメッセージ',
	InboxEditDraft: '下書きを編集',
	InboxEmailComposeReplyEmail: '返信を作成',
	InboxEmailDraft: '下書き',
	InboxEmailNotFound: 'メールアドレスが見つかりません',
	InboxEmailSubjectFieldInformation: '件名を変更すると、新しいスレッド化された電子メールが作成されます。',
	InboxEmptyArchiveDescription: 'アーカイブされた会話は見つかりませんでした',
	InboxEmptyBinDescription: '削除された会話は見つかりませんでした',
	InboxEmptyBinHeading: 'すべてクリア、何も見えません',
	InboxEmptyBinSuccess: '会話を削除しました',
	InboxEmptyCongratsHeading: 'お疲れ様です！次の会話までゆっくりおくつろぎください。',
	InboxEmptyDraftDescription: 'ドラフト会話は見つかりませんでした',
	InboxEmptyDraftHeading: 'すべてクリア、何も見えません',
	InboxEmptyOtherDescription: 'その他の会話は見つかりませんでした',
	InboxEmptyScheduledHeading: 'すべてクリア、送信予定の会話はありません',
	InboxEmptySentDescription: '送信された会話は見つかりませんでした',
	InboxForward: 'フォワード',
	InboxGroupClientsLabel: 'すべての顧客',
	InboxGroupClientsOverviewLabel: '顧客',
	InboxGroupClientsSelectedItemPrefix: '顧客',
	InboxGroupStaffsLabel: 'すべてのチーム',
	InboxGroupStaffsOverviewLabel: 'チーム',
	InboxGroupStaffsSelectedItemPrefix: 'チーム',
	InboxGroupStatusLabel: 'すべてのステータス',
	InboxGroupStatusOverviewLabel: 'ステータスに送信',
	InboxGroupStatusSelectedItemPrefix: 'ステータス',
	InboxGroupTagsLabel: 'すべてのタグ',
	InboxGroupTagsOverviewLabel: 'タグに送信',
	InboxGroupTagsSelectedItemPrefix: 'タグ',
	InboxHideQuotedText: '引用されたテキストを隠す',
	InboxIgnoreConversationSuccess: '会話を無視しました',
	InboxMessageAllLabelRecipientsCount: 'すべての {label} 受信者 ({count})',
	InboxMessageBodyPlaceholder: 'メッセージを追加',
	InboxMessageDeleted: 'メッセージを削除しました',
	InboxMessageMarkedAsRead: 'メッセージを既読としてマーク',
	InboxMessageMarkedAsUnread: '未読としてマークされたメッセージ',
	InboxMessageSentViaChat: '**チャットで送信**  • {time} by {name}',
	InboxMessageShowMoreRecipients: '+{count} もっと',
	InboxMessageWasDeleted: 'このメッセージは削除されました。',
	InboxNoConnectionDescription: 'メールアカウントを接続するか、複数のメールを含む受信トレイを作成します',
	InboxNoConnectionHeading: 'クライアントとのコミュニケーションを統合する',
	InboxNoDirectMessage: '最近のメッセージはありません',
	InboxRecentConversations: '最近の',
	InboxReopenConversationSuccess: '会話を再開しました',
	InboxReply: '返事',
	InboxReplyAll: '全員に返信',
	InboxRestoreConversationSuccess: '会話が正常に復元されました',
	InboxScheduleSendCancelSendSuccess: '送信予定がキャンセルされ、メッセージが下書きに戻されました',
	InboxScheduleSendMessageSuccessDescription: '{date} にスケジュール送信',
	InboxScheduleSendMessageSuccessTitle: '送信スケジュール',
	InboxSearchForConversations: '"{query}" を検索する',
	InboxSendMessageSuccess: '会話を送信しました',
	InboxSettings: '受信トレイの設定',
	InboxSettingsAppsDesc:
		'この共有受信トレイに接続されているアプリを管理します。必要に応じて接続を追加または削除します。',
	InboxSettingsAppsNewConnectedApp: '新しい接続アプリ',
	InboxSettingsAppsTitle: '接続されたアプリ',
	InboxSettingsDeleteAccountFailed: '受信トレイアカウントの削除に失敗しました',
	InboxSettingsDeleteAccountSuccess: '受信トレイアカウントが正常に削除されました',
	InboxSettingsDeleteAccountWarning:
		'{email} を削除すると、受信トレイ {inboxName} から切断され、メッセージの同期が停止します。',
	InboxSettingsDeleteInboxFailed: '受信トレイを削除できませんでした',
	InboxSettingsDeleteInboxSuccess: '受信トレイが正常に削除されました',
	InboxSettingsDeleteInboxWarning:
		'{inboxName} を削除すると、接続されているすべてのチャネルが切断され、この受信箱に関連付けられているすべてのメッセージが削除されます。		この操作は永続的で元に戻すことはできません。',
	InboxSettingsDetailsDesc: 'チームがクライアントのメッセージを効率的に管理するためのコミュニケーション受信トレイ。',
	InboxSettingsDetailsTitle: '受信トレイの詳細',
	InboxSettingsEmailSignatureLabel: 'メール署名のデフォルト',
	InboxSettingsReplyFormatDesc:
		'誰がメールを送信したかに関係なく、デフォルトの返信先アドレスと電子メール署名が常に表示されるように設定します。',
	InboxSettingsReplyFormatTitle: '返信フォーマット',
	InboxSettingsSendFromLabel: 'デフォルトの返信先を設定する ',
	InboxSettingsStaffDesc:
		'シームレスなコラボレーションを実現するために、この共有受信トレイへのチーム メンバーのアクセスを管理します。',
	InboxSettingsStaffTitle: 'チームメンバーを割り当てる',
	InboxSettingsUpdateInboxDetailsFailed: '受信トレイの詳細を更新できませんでした',
	InboxSettingsUpdateInboxDetailsSuccess: '受信トレイの詳細が正常に更新されました',
	InboxSettingsUpdateInboxStaffsFailed: '受信トレイのチームメンバーを更新できませんでした',
	InboxSettingsUpdateInboxStaffsSuccess: '受信トレイチームメンバーを正常に更新しました',
	InboxSettingsUpdateReplyFormatFailed: '返信形式の更新に失敗しました',
	InboxSettingsUpdateReplyFormatSuccess: '返信フォーマットが正常に更新されました',
	InboxShowQuotedText: '引用を表示する',
	InboxStaffRoleAdminDescription: '受信トレイの表示、返信、管理',
	InboxStaffRoleResponderDescription: '表示して返信する',
	InboxStaffRoleViewerDescription: '表示のみ',
	InboxSuggestMoveToBulkComposeMessageActionCancel: '編集を続ける',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'はい、大量送信に切り替える',
	InboxSuggestMoveToBulkComposeMessageContent: '{count}以上の受信者を選択しました。大量メールとして送信しますか？',
	InboxSuggestMoveToBulkComposeMessageTitle: '警告',
	InboxSwitchToOtherInbox: '別の受信トレイに切り替える',
	InboxUndoSendMessageSuccess: '送信が取り消されました',
	IncludeLineItems: '明細項目を含める',
	IncludeSalesTax: '課税対象',
	IncludesAiSmartPrompt: 'AIスマートプロンプトを搭載',
	Incomplete: '不完全な',
	IncreaseIndent: 'インデントを増やします',
	IndianHealthServiceFreeStandingFacility: 'インディアン保健サービスの独立施設',
	IndianHealthServiceProviderFacility: 'インディアンヘルスサービスプロバイダーベースの施設',
	Information: '情報',
	InitialAssessment: '初期評価',
	InitialSignupPageClientFamilyTitle: '顧客または家族',
	InitialSignupPageProviderTitle: '健康 ',
	InitialTreatment: '初期治療',
	Initials: 'イニシャル',
	InlineEmbed: 'インライン埋め込み',
	InputPhraseToConfirm: '確認のため、{confirmationPhrase} と入力してください。',
	Insert: '入れる',
	InsertTable: '表を挿入',
	InstallCarepatronOnYourIphone1: 'iOSにCarepatronをインストールするには、タップします',
	InstallCarepatronOnYourIphone2: 'ホーム画面に追加',
	InsufficientCalendarScopesSnackbar: '同期に失敗しました - Carepatron にカレンダーの権限を許可してください',
	InsufficientInboxScopesSnackbar: '同期に失敗しました - Carepatron へのメール権限を許可してください',
	InsufficientScopeErrorCodeSnackbar: '同期に失敗しました - Carepatron へのすべての権限を許可してください',
	Insurance: '保険',
	InsuranceAmount: '保険金額',
	InsuranceClaim: '保険金請求',
	InsuranceClaimAiChatPlaceholder: '保険請求についてお尋ねください。',
	InsuranceClaimAiClaimNumber: '請求 {number}',
	InsuranceClaimAiSubtitle: '保険請求  •  請求確認',
	InsuranceClaimDeniedSubject:
		'Claim {claimNumber} が {payerNumber} {payerName} に提出されましたが、拒否されました。',
	InsuranceClaimErrorDescription:
		'請求には、支払人またはクリアリングハウスから報告されたエラーが含まれています。次のエラーメッセージを確認して、請求を再提出してください。',
	InsuranceClaimErrorGuideLink: '保険請求ガイド',
	InsuranceClaimErrorTitle: 'クレーム提出エラー',
	InsuranceClaimNotFound: '保険金請求が見つかりません',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {{paymentAmount} の一部入金} other {{paymentAmount} の入金}}が請求番号 {claimNumber} に対し、{payerNumber} {payerName} によって記録されました',
	InsuranceClaimRejectedSubject: `Claim {claimNumber} submitted to {payerNumber} {payerName} was rejected		
Claim {claimNumber} は {payerNumber} {payerName} に送信されましたが、拒否されました。`,
	InsuranceClaims: '保険請求',
	InsuranceInformation: '保険情報',
	InsurancePaid: '保険支払済み',
	InsurancePayer: '保険支払者',
	InsurancePayers: '保険支払者',
	InsurancePayersDescription: 'アカウントに追加された支払い者を表示し、登録を管理します。',
	InsurancePayment: '保険金支払い',
	InsurancePoliciesDetailsSubtitle: '請求をサポートするためにクライアントの保険情報を追加します。',
	InsurancePoliciesDetailsTitle: 'ポリシーの詳細',
	InsurancePoliciesListSubtitle: '請求をサポートするためにクライアントの保険情報を追加します。',
	InsurancePoliciesListTitle: '保険証券',
	InsuranceSelfPay: '自己負担',
	InsuranceType: '保険の種類',
	InsuranceUnpaid: '保険未払い',
	Intake: '摂取',
	IntakeExpiredErrorCodeSnackbar:
		'この受付の有効期限は切れています。別の受付を再送信するには、プロバイダーにお問い合わせください。',
	IntakeNotFoundErrorSnackbar:
		'この入力情報は見つかりませんでした。別の入力情報を再送信するには、プロバイダーにお問い合わせください。',
	IntakeProcessLearnMoreInstructions: '受付フォームの設定ガイド',
	IntakeTemplateSelectorPlaceholder: '顧客に送付して完了してもらうフォームと契約書を選択してください',
	Integration: '統合',
	IntenseBlur: '背景を強烈にぼかす',
	InteriorDesigner: 'インテリアデザイナー',
	InternetBanking: '銀行振込',
	Interval: '間隔',
	IntervalDays: '間隔（日数）',
	IntervalHours: '間隔（時間）',
	Invalid: '無効',
	InvalidDate: '日付が無効です',
	InvalidDateFormat: '日付は{format}形式である必要があります。',
	InvalidDisplayName: '表示名は {value} を含めることはできません。',
	InvalidEmailFormat: '無効なメール形式',
	InvalidFileType: '無効なファイルタイプ',
	InvalidGTMContainerId: 'GTM コンテナ ID の形式が無効です',
	InvalidPaymentMethodCode: '選択された支払い方法は無効です。別の方法を選択してください。',
	InvalidPromotionCode: 'プロモーションコードは無効です',
	InvalidReferralDescription: 'すでにCarepatronをご利用中',
	InvalidStatementDescriptor: `ステートメント記述子は 5 文字から 22 文字までで、文字、数字、スペースのみで構成され、<、>、 \\、'、 "、* は使用できません。`,
	InvalidToken: '無効なトークン',
	InvalidTotpSetupVerificationCode: '認証コードが無効です。',
	InvalidURLErrorText: '有効なURLである必要があります',
	InvalidZoomTokenErrorCodeSnackbar:
		'Zoom トークンの有効期限が切れています。Zoom アプリを再接続してもう一度お試しください。',
	Invite: '招待する',
	InviteRelationships: '関係を誘う',
	InviteToPortal: 'ポータルに招待',
	InviteToPortalModalDescription: 'Carepatron にサインアップするための招待メールがクライアントに送信されます。',
	InviteToPortalModalTitle: '{name} さんを Carepatron ポータルに招待する',
	InviteUserDescription: ' ',
	InviteUserTitle: '新規ユーザーを招待',
	Invited: '招待',
	Invoice: '請求書',
	InvoiceColorPickerDescription: '請求書で使用するカラーテーマ',
	InvoiceColorTheme: '請求書のカラーテーマ',
	InvoiceContactDeleted: '請求書の連絡先が削除されているため、この請求書を更新できません。',
	InvoiceDate: '日付発行',
	InvoiceDetails: '請求の詳細',
	InvoiceFieldsPlaceholder: 'フィールドを検索...',
	InvoiceFrom: '請求書 {number} {fromProvider}より',
	InvoiceInvalidCredit: 'クレジット金額が無効です。クレジット金額は請求書の合計額を超えることはできません。',
	InvoiceNotFoundDescription: 'プロバイダーに連絡して詳細情報を問い合わせるか、請求書を再送信してもらってください。',
	InvoiceNotFoundTitle: '請求書が見つかりません',
	InvoiceNumber: '請求書 {ハッシュタグ}',
	InvoiceNumberFormat: '請求書 #{number}',
	InvoiceNumberMustEndWithDigit: '請求書番号は数字（0～9）で終わる必要があります',
	InvoicePageHeader: '請求書',
	InvoicePaidNotificationSubject: '請求書 {invoiceNumber} 支払済み',
	InvoiceReminder: '請求書リマインダー',
	InvoiceReminderSentence:
		'{deliveryType} リマインダーを請求書支払い期限の {interval} {unit} {beforeAfter} に送信する',
	InvoiceReminderSettings: '請求書リマインダー設定',
	InvoiceReminderSettingsInfo: 'リマインダーはCarepatronで送信された請求書にのみ適用されます',
	InvoiceReminders: '請求書リマインダー',
	InvoiceRemindersInfo:
		'請求書の支払期日を自動通知するよう設定します。通知はCarepatron経由で送信された請求書にのみ適用されます。',
	InvoiceSettings: '請求書設定',
	InvoiceStatus: '請求書のステータス',
	InvoiceTemplateAddressPlaceholder: '123 メイン ストリート、エニータウン、米国',
	InvoiceTemplateDescriptionPlaceholder: '代替支払いに関するメモ、銀行振込の詳細、または利用規約を追加します',
	InvoiceTemplateEmploymentStatusPlaceholder: '自営業',
	InvoiceTemplateEthnicityPlaceholder: '白人',
	InvoiceTemplateNotFoundDescription: '詳細についてはプロバイダーに問い合わせてください。',
	InvoiceTemplateNotFoundTitle: '請求書テンプレートが見つかりません',
	InvoiceTemplates: '請求書テンプレート',
	InvoiceTemplatesDescription:
		'弊社の使いやすいテンプレートを使用して、請求書テンプレートをカスタマイズし、ブランドを反映し、規制要件を満たし、顧客の好みに合わせることができます。',
	InvoiceTheme: '請求書テーマ',
	InvoiceTotal: '請求金額',
	InvoiceUninvoicedAmounts: '未請求金額を請求する',
	InvoiceUpdateVersionMessage:
		'この請求書を編集するには最新バージョンが必要です。Carepatron を再読み込みしてもう一度お試しください。',
	Invoices: '{count, plural, one {請求書} other {請求書}}',
	InvoicesEmptyStateDescription: '請求書が見つかりません',
	InvoicingAndPayment: '請求書発行 ',
	Ireland: 'アイルランド',
	IsA: 'は',
	IsBetween: '間にある',
	IsEqualTo: '等しい',
	IsGreaterThan: 'より大きい',
	IsGreaterThanOrEqualTo: 'より大きいか等しい',
	IsLessThan: 'より小さい',
	IsLessThanOrEqualTo: '以下か等しい',
	IssueCredit: '発行クレジット',
	IssueCreditAdjustment: '発行クレジット調整',
	IssueDate: '発行日',
	Italic: 'イタリック',
	Items: 'アイテム',
	ItemsAndAdjustments: 'アイテムと調整',
	ItemsRemaining: '+{count} 個のアイテムが残っています',
	JobTitle: '役職',
	Join: '参加する',
	JoinCall: '通話に参加',
	JoinNow: '今すぐ参加',
	JoinProduct: '{product} に参加する',
	JoinVideoCall: 'ビデオ通話に参加する',
	JoinWebinar: 'ウェビナーに参加する',
	JoinWithVideoCall: '{product} に参加する',
	Journalist: 'ジャーナリスト',
	JustMe: '私だけ',
	JustYou: 'あなただけ',
	Justify: '正当化する',
	KeepSeparate: '別々に保管する',
	KeepSeparateSuccessMessage: '{clientNames} の別々の記録を正常に保持しました。',
	KeepWaiting: '待ってて',
	Label: 'ラベル',
	LabelOptional: 'ラベル（オプション）',
	LactationConsulting: '授乳コンサルティング',
	Language: '言語',
	Large: '大きい',
	LastDxCode: '最新のDXコード',
	LastLoggedIn: '最終ログイン: {date} {time}',
	LastMenstrualPeriod: '最終月経',
	LastMonth: '先月',
	LastNDays: '過去 {number} 日間',
	LastName: '苗字',
	LastNameFirstInitial: '姓、名の頭文字',
	LastWeek: '先週',
	LastXRay: '最後のX線',
	LatestVisitOrConsultation: '最近の訪問または相談',
	Lawyer: '弁護士',
	LearnMore: 'もっと詳しく知る',
	LearnMoreTipsToGettingStarted: '始めるためのヒントをもっと学ぶ',
	LearnToSetupInbox: '受信トレイアカウントの設定方法を学ぶ',
	Leave: '離れる',
	LeaveCall: '通話を終了する',
	LeftAlign: '左揃え',
	LegacyBillingItemsNotAvailable:
		'この予約では、個別の請求項目はまだ利用できません。通常通り請求することができます。',
	LegacyBillingItemsNotAvailableTitle: 'レガシー課金',
	LegalAndConsent: '法律と同意',
	LegalConsentFormPrimaryText: '法的同意',
	LegalConsentFormSecondaryText: 'オプションを承認または拒否する',
	LegalGuardian: '法定後見人',
	Letter: '手紙',
	LettersCategoryDescription: '臨床および管理対応の作成のため',
	Librarian: '司書',
	LicenseNumber: 'ライセンス番号',
	LifeCoach: 'ライフコーチ',
	LifeCoaches: 'ライフコーチ',
	Limited: '限定',
	LineSpacing: '行と段落の間隔',
	LinearScaleFormPrimaryText: '線形スケール',
	LinearScaleFormSecondaryText: 'スケールオプション 1-10',
	Lineitems: '項目',
	Link: 'リンク',
	LinkClientFormSearchClientLabel: 'クライアントを検索',
	LinkClientModalTitle: '既存のクライアントへのリンク',
	LinkClientSuccessDescription:
		'<strong>{newName}</strong> の連絡先情報が <strong>{existingName}</strong> のレコードに追加されました。',
	LinkClientSuccessTitle: '既存の連絡先へのリンクに成功しました',
	LinkForCallCopied: 'リンクをコピーしました!',
	LinkToAnExistingClient: '既存のクライアントへのリンク',
	LinkToClient: 'クライアントへのリンク',
	ListAndTracker: 'リスト/トラッカー',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} はこの電話会議に参加しています}
			other {{attendees} はこの電話会議に参加しています}
		}`,
	ListStyles: 'リストスタイル',
	ListsAndTrackersCategoryDescription: '仕事 (しごと) を整理 (せいり) し、追跡 (ついせき) するため',
	LivingArrangements: '居住環境',
	LoadMore: 'もっと読み込む',
	Loading: '読み込み中...',
	LocalizationPanelDescription: '言語とタイムゾーンの設定を管理する',
	LocalizationPanelTitle: '言語とタイムゾーン',
	Location: '位置',
	LocationDescription:
		'特定の住所、部屋名、仮想スペースの種類を使用して物理的な場所と仮想的な場所を設定すると、予定やビデオ通話のスケジュール設定が容易になります。',
	LocationNumber: '場所番号',
	LocationOfService: 'サービスの場所',
	LocationOfServiceRecommendedActionInfo:
		'このサービスに特定の場所を追加すると、利用可能時間に影響を与える可能性があります。',
	LocationRemote: 'リモート',
	LocationType: '場所の種類',
	Locations: '場所',
	Lock: 'ロック',
	Locked: 'ロックされています',
	LockedNote: 'ロックされたメモ',
	LogInToSaveOrAuthoriseCard: 'カードを保存または承認するにはログインしてください',
	LogInToSaveOrAuthorisePayment: '支払いを保存または承認するにはログインしてください',
	Login: 'ログイン',
	LoginButton: 'サインイン',
	LoginEmail: 'Eメール',
	LoginForgotPasswordLink: 'パスワードをお忘れですか',
	LoginPassword: 'パスワード',
	Logo: 'ロゴ',
	LogoutAreYouSure: 'このデバイスからサインアウトします。',
	LogoutButton: 'サインアウト',
	London: 'ロンドン',
	LongTextAnswer: '長いテキストの回答',
	LongTextFormPrimaryText: '長いテキスト',
	LongTextFormSecondaryText: '段落スタイルオプション',
	Male: '男',
	Manage: '管理',
	ManageAllClientTags: 'すべてのクライアントタグを管理する',
	ManageAllNoteTags: 'すべてのノートタグを管理する',
	ManageAllTemplateTags: 'すべてのテンプレートタグを管理する',
	ManageConnections: '接続を管理する',
	ManageConnectionsGmailDescription: '他のチームメンバーは同期された Gmail を見ることができません。',
	ManageConnectionsGoogleCalendarDescription:
		'他のチーム メンバーは同期されたカレンダーを表示できません。クライアントの予定は Carepatron 内からのみ更新または削除できます。',
	ManageConnectionsInboxSyncHelperText: '受信トレイの同期設定を管理するには、受信トレイ ページに移動してください。',
	ManageConnectionsMicrosoftCalendarDescription:
		'他のチーム メンバーは同期されたカレンダーを表示できません。クライアントの予定は Carepatron 内からのみ更新または削除できます。',
	ManageConnectionsOutlookDescription: '他のチーム メンバーは、同期された Microsoft Outlook を表示できません。',
	ManageInboxAccountButton: '新しい受信トレイ',
	ManageInboxAccountEdit: '受信トレイの管理',
	ManageInboxAccountPanelTitle: '受信トレイ',
	ManageInboxAssignTeamPlaceholder: '受信トレイにアクセスするチームメンバーを選択する',
	ManageInboxBasicInfoColor: '色',
	ManageInboxBasicInfoDescription: '説明',
	ManageInboxBasicInfoDescriptionPlaceholder: 'あなたやチームはこの受信トレイを何に使用しますか？',
	ManageInboxBasicInfoName: '受信トレイ名',
	ManageInboxBasicInfoNamePlaceholder: '例: カスタマーサポート、管理者',
	ManageInboxConnectAppAlreadyConnectedError: '接続しようとしたチャンネルはすでにCarepatronに接続されています',
	ManageInboxConnectAppConnect: '接続する',
	ManageInboxConnectAppConnectedInfo: 'アカウントに接続しました',
	ManageInboxConnectAppContinue: '続く',
	ManageInboxConnectAppEmail: 'Eメール',
	ManageInboxConnectAppSignInWith: 'サインイン',
	ManageInboxConnectAppSubtitle: 'アプリを接続すると、すべての通信を 1 か所でシームレスに送信、受信、追跡できます。',
	ManageInboxNewInboxTitle: '新しい受信トレイ',
	ManagePlan: 'プラン管理',
	ManageProfile: 'プロフィールの管理',
	ManageReferralsModalDescription: '当社のヘルスケア プラットフォームを広めて報酬を獲得するお手伝いをしてください。',
	ManageReferralsModalTitle: '友達を紹介して報酬を獲得しましょう！',
	ManageStaffRelationshipsAddButton: '関係を管理する',
	ManageStaffRelationshipsEmptyStateText: '関係が追加されていません',
	ManageStaffRelationshipsModalDescription:
		'クライアントを選択すると新しい関係が追加され、選択解除すると既存の関係が削除されます。',
	ManageStaffRelationshipsModalTitle: '関係を管理する',
	ManageStatuses: 'ステータスを管理する',
	ManageStatusesActiveStatusHelperText: '少なくとも 1 つのアクティブ ステータスが必要です',
	ManageStatusesDescription: 'ステータス ラベルをカスタマイズし、ワークフローに合わせて色を選択します。',
	ManageStatusesSuccessSnackbar: 'ステータスが正常に更新されました',
	ManageTags: 'タグを管理する',
	ManageTaskAttendeeStatus: '予約状況を管理する',
	ManageTaskAttendeeStatusDescription: 'ワークフローに合わせて予約ステータスをカスタマイズしてください。',
	ManageTaskAttendeeStatusHelperText: '少なくとも1つのステータスが必要です。',
	ManageTaskAttendeeStatusSubtitle: 'カスタムステータス',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'マニュアル',
	ManualAppointment: '手動予約',
	ManualPayment: '手動支払い',
	ManuallyTypeLocation: '場所を手動で入力',
	MapColumns: 'マップ列',
	MappingRequired: 'マッピングが必要です',
	MarkAllAsRead: 'すべてを既読にする',
	MarkAsCompleted: '完了としてマーク',
	MarkAsManualSubmission: '提出済みとしてマークする',
	MarkAsPaid: '支払済みとしてマーク',
	MarkAsRead: '既読にする',
	MarkAsUnpaid: '未払いとしてマーク',
	MarkAsUnread: '未読としてマーク',
	MarkAsVoid: '無効としてマーク',
	Marker: 'マーカー',
	MarketingManager: 'マーケティング・マネージャー',
	MassageTherapist: 'マッサージセラピスト',
	MassageTherapists: 'マッサージセラピスト',
	MassageTherapy: 'マッサージ療法',
	MaxBookingTimeDescription1: 'クライアントは最大',
	MaxBookingTimeDescription2: '未来へ',
	MaxBookingTimeLabel: '{timePeriod} 前に',
	MaxCapacity: '最大容量',
	Maximize: '最大化',
	MaximumAttendeeLimit: '上限',
	MaximumBookingTime: '最大予約時間',
	MaximumBookingTimeError: '最大予約時間は {valueUnit} を超えてはなりません。',
	MaximumMinimizedPanelsReachedDescription:
		'一度に最大 {count} 個のサイドパネルを最小化できます。続行すると、最小化されたパネルのうち最も古いものが閉じられます。続行しますか？',
	MaximumMinimizedPanelsReachedTitle: 'パネルが開きすぎている。',
	MechanicalEngineer: '機械工学士',
	MediaGallery: 'メディアギャラリー',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'メディケイドプロバイダー番号',
	MedicalAssistant: '医療助手',
	MedicalCoder: '医療コーダー',
	MedicalDoctor: '医師',
	MedicalIllustrator: '医療イラストレーター',
	MedicalInterpreter: '医療通訳',
	MedicalTechnologist: '医療技術者',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'メディケアプロバイダー番号',
	Medicine: '薬',
	Medium: '中くらい',
	Meeting: 'ミーティング',
	MeetingEnd: '会議終了',
	MeetingEnded: '会議終了',
	MeetingHost: '会議主催者',
	MeetingLowerHand: '下手',
	MeetingOpenChat: 'チャットを開く',
	MeetingPersonRaisedHand: '{name} は手を挙げた。',
	MeetingRaiseHand: '手を上げる',
	MeetingReady: '会議準備完了',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {分} other {分}} {status}',
	Meetings: 'ミーティング',
	MemberId: '会員ID',
	MentalHealth: 'メンタルヘルス',
	MentalHealthPractitioners: 'メンタルヘルス専門家',
	MentalHealthProfessional: 'メンタルヘルス専門家',
	Merge: 'マージ',
	MergeClientRecords: 'クライアントレコードをマージする',
	MergeClientRecordsDescription:
		'クライアントレコードをマージすると、すべてのデータが統合されます。これには以下が含まれます。',
	MergeClientRecordsDescription2: 'マージを続行しますか？ この操作は元に戻せません。',
	MergeClientRecordsItem1: 'ノートとドキュメント',
	MergeClientRecordsItem2: '予約',
	MergeClientRecordsItem3: '請求書',
	MergeClientRecordsItem4: '会話',
	MergeClientsSuccess: 'クライアント記録の統合が完了しました。',
	MergeLimitExceeded: '一度に最大4つのクライアントをマージできます。',
	Message: 'メッセージ',
	MessageAttachments: '{total} 個の添付ファイル',
	Method: '方法',
	MfaAvailabilityDisclaimer:
		'MFA は、メールとパスワードによるログインでのみ利用できます。MFA 設定を変更するには、メールとパスワードを使用してログインしてください。',
	MfaDeviceLostPanelDescription: 'または、電子メールでコードを受信して本人確認を行うこともできます。',
	MfaDeviceLostPanelTitle: 'MFA デバイスを紛失しましたか?',
	MfaDidntReceiveEmailCode: 'コードが届きませんか? サポートにお問い合わせください',
	MfaEmailOtpSendFailureSnackbar: '電子メール OTP の送信に失敗しました。',
	MfaEmailOtpSentSnackbar: '{maskedEmail} にコードが送信されました。',
	MfaEmailOtpVerificationFailedSnackbar: '電子メール OTP の確認に失敗しました。',
	MfaHasBeenSetUpText: 'MFAを設定しました',
	MfaPanelDescription:
		'多要素認証 (MFA) を有効にして、保護層を追加し、アカウントを保護します。不正アクセスを防ぐために、2 次的な方法で ID を確認します。',
	MfaPanelNotAuthorizedError: 'ユーザー名でサインインする必要があります ',
	MfaPanelRecommendationDescription:
		'最近、別の方法で本人確認を行い、サインインしました。アカウントのセキュリティを確保するには、新しい MFA デバイスの設定を検討してください。',
	MfaPanelRecommendationTitle: '<strong>推奨:</strong> MFA デバイスを更新する',
	MfaPanelTitle: '多要素認証 (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'MFA 設定を更新する前に、メールを確認する必要があります。',
	MfaRecommendationBannerDescription:
		'最近、別の方法で本人確認を行ってサインインしました。アカウントのセキュリティを確保するには、新しい MFA デバイスの設定を検討してください。',
	MfaRecommendationBannerPrimaryAction: 'MFAを設定する',
	MfaRecommendationBannerTitle: '推奨',
	MfaRemovedSnackbarTitle: 'MFA が削除されました。',
	MfaSendEmailCode: 'コードを送信',
	MfaVerifyIdentityLostDeviceButton: 'MFAデバイスにアクセスできなくなりました',
	MfaVerifyYourIdentityPanelDescription: '認証アプリでコードを確認し、以下に入力してください。',
	MfaVerifyYourIdentityPanelTitle: '本人確認',
	MicCamWarningMessage:
		'ブラウザのアドレスバーにあるブロックされたアイコンをクリックして、カメラとマイクのブロックを解除します。',
	MicCamWarningTitle: 'カメラとマイクがブロックされています',
	MicOff: 'マイクがオフになっています',
	MicOn: 'マイクがオンになっています',
	MicSource: 'マイクソース',
	MicWarningMessage: 'マイクに問題が検出されました',
	Microphone: 'マイクロフォン',
	MicrophonePermissionBlocked: 'マイクへのアクセスがブロックされています',
	MicrophonePermissionBlockedDescription: '録音を開始するには、マイクの権限を更新してください。',
	MicrophonePermissionError: '続行するにはブラウザの設定でマイクの許可を与えてください',
	MicrophonePermissionPrompt: '続行するにはマイクへのアクセスを許可してください',
	Microsoft: 'マイクロソフト',
	MicrosoftCalendar: 'マイクロソフト',
	MicrosoftColor: 'Outlook カレンダーの色',
	MicrosoftOutlook: 'マイクロソフトアウトルック',
	MicrosoftTeams: 'マイクロソフトチーム',
	MiddleEast: '中東',
	MiddleName: 'ミドルネーム',
	MiddleNames: 'ミドルネーム',
	Midwife: '助産師',
	Midwives: '助産師',
	Milan: 'ミラノ',
	MinBookingTimeDescription1: 'クライアントはスケジュールを設定できません',
	MinBookingTimeDescription2: '予約の開始時間',
	MinBookingTimeLabel: '予約の{timePeriod}前',
	MinCancellationTimeEditModeDescription: 'クライアントがペナルティなしでキャンセルできる時間数を設定する',
	MinCancellationTimeUnset: '最低キャンセル時間は設定されていません',
	MinCancellationTimeViewModeDescription: 'ペナルティなしのキャンセル期間',
	MinMaxBookingTimeUnset: '時間設定なし',
	Minimize: '最小化',
	MinimizeConfirmationDescription:
		'アクティブな最小化されたパネルがあります。続行すると、パネルが閉じられ、保存されていないデータが失われる可能性があります。',
	MinimizeConfirmationTitle: '最小化されたパネルを閉じる？',
	MinimumBookingTime: '最短予約時間',
	MinimumCancellationTime: '最短キャンセル時間',
	MinimumPaymentError: 'オンライン決済には、{minimumAmount} の最低請求額が必要です。',
	MinuteAbbreviated: '分',
	MinuteAbbreviation: '{count} {count, plural, one {分} other {分}}',
	Minutely: '毎分',
	MinutesPlural: '{age, plural, one {# 分} other {# 分}}',
	MiscellaneousInformation: 'その他情報',
	MissingFeatures: '欠けている機能',
	MissingPaymentMethod: 'スタッフを追加するには、サブスクリプションに支払い方法を追加してください。',
	MobileNumber: '携帯電話番号',
	MobileNumberOptional: '携帯電話番号（オプション）',
	Modern: 'モダンな',
	Modifiers: '修飾子',
	ModifiersPlaceholder: '修飾子',
	Monday: '月曜日',
	Month: '月',
	Monthly: '月次',
	MonthlyCost: '月額費用',
	MonthlyOn: '毎月 {date} 日に',
	MonthsPlural: '{age, plural, one {# ヶ月} other {# か月}}',
	More: 'もっと',
	MoreActions: 'その他のアクション',
	MoreSettings: 'その他の設定',
	MoreThanTen: '10 ',
	MostCommonlyUsed: '最もよく使われる',
	MostDownloaded: '最もダウンロードされたもの',
	MostPopular: '最も人気のある',
	Mother: '母親',
	MotherInLaw: '義理の母',
	MoveDown: '下に移動',
	MoveInboxConfirmationDescription:
		'このアプリ接続を再割り当てすると、<strong>{currentInboxName}</strong> インボックスから削除されます。',
	MoveTemplateToFolder: '`{templateTitle}` を移動します。',
	MoveTemplateToFolderSuccess: '{templateTitle} は {folderTitle} に移動しました。',
	MoveTemplateToIntakeFolderSuccessMessage: 'デフォルトの受付フォルダに正常に移動しました。',
	MoveTemplateToNewFolder: 'このアイテムを移動するための新しいフォルダを作成します。',
	MoveToChosenFolder: 'このアイテムを移動するフォルダを選択してください。必要に応じて新しいフォルダを作成できます。',
	MoveToFolder: 'フォルダに移動',
	MoveToInbox: '受信トレイに移動',
	MoveToNewFolder: '新しいフォルダに移動する',
	MoveToSelectedFolder: '移動させると、アイテムは選択したフォルダに整理され、現在の場所には表示されなくなります。',
	MoveUp: '上に移動',
	MultiSpeciality: '多専門分野',
	MultipleChoiceFormPrimaryText: '複数の選択肢',
	MultipleChoiceFormSecondaryText: '複数のオプションを選択',
	MultipleChoiceGridFormPrimaryText: '複数選択グリッド',
	MultipleChoiceGridFormSecondaryText: 'マトリックスからオプションを選択する',
	Mumbai: 'ムンバイ',
	MusicTherapist: '音楽療法士',
	MustContainOneLetterError: '少なくとも1文字を含める必要があります',
	MustEndWithANumber: '数字で終わる必要があります',
	MustHaveAtLeastXItems: '少なくとも{count, plural, one {# 個} other {# 個}}が必要です。',
	MuteAudio: '音声をミュートする',
	MuteEveryone: '全員をミュートする',
	MyAvailability: '私のアベイラビリティ',
	MyGallery: '私のギャラリー',
	MyPortal: 'マイポータル',
	MyRelationships: '私の人間関係',
	MyTemplates: 'チームテンプレート',
	MyofunctionalTherapist: '筋機能療法士',
	NCalifornia: '北カリフォルニア',
	NPI: 'インド',
	NVirginia: 'ノースバージニア',
	Name: '名前',
	NameIsRequired: '名前は必須です',
	NameMustNotBeAWebsite: '名前はウェブサイトであってはなりません',
	NameMustNotBeAnEmail: '名前はメールアドレスであってはなりません',
	NameMustNotContainAtSign: '名前に@記号を含めることはできません',
	NameMustNotContainHTMLTags: '名前にHTMLタグを含めることはできません',
	NameMustNotContainSpecialCharacters: '名前に特殊文字を含めることはできません',
	NameOnCard: 'カード名',
	NationalProviderId: '国家プロバイダー識別子 (NPI)',
	NaturopathicDoctor: '自然療法医',
	NavigateToPersonalSettings: 'プロフィール',
	NavigateToSubscriptionSettings: 'サブスクリプションの設定',
	NavigateToWorkspaceSettings: 'ワークスペースの設定',
	NavigateToYourTeam: 'チームを管理',
	NavigationDrawerBilling: '請求',
	NavigationDrawerBillingInfo: '請求情報、請求書、Stripe',
	NavigationDrawerCommunication: 'コミュニケーション',
	NavigationDrawerCommunicationInfo: '通知とテンプレート',
	NavigationDrawerInsurance: '保険',
	NavigationDrawerInsuranceInfo: '保険金支払者と保険金請求',
	NavigationDrawerInvoices: '請求する',
	NavigationDrawerPersonal: '私のプロフィール',
	NavigationDrawerPersonalInfo: '個人情報',
	NavigationDrawerProfile: 'プロフィール',
	NavigationDrawerProviderSettings: '設定',
	NavigationDrawerScheduling: 'スケジュール',
	NavigationDrawerSchedulingInfo: 'サービスの詳細と予約',
	NavigationDrawerSettings: '設定',
	NavigationDrawerTemplates: 'テンプレート',
	NavigationDrawerTemplatesV2: 'テンプレート V2',
	NavigationDrawerTrash: 'ゴミ箱',
	NavigationDrawerTrashInfo: '削除されたアイテムを復元する',
	NavigationDrawerWorkspace: 'ワークスペース設定',
	NavigationDrawerWorkspaceInfo: 'サブスクリプションとワークスペースの情報',
	NegativeBalanceNotSupported: 'マイナスの口座残高はサポートされていません',
	Nephew: '甥',
	NetworkQualityFair: 'フェアな接続',
	NetworkQualityGood: '良好な接続',
	NetworkQualityPoor: '接続が悪い',
	Neurologist: '神経科医',
	Never: '決して',
	New: '新しい',
	NewAppointment: '新規任命',
	NewClaim: '新たな主張',
	NewClient: '新規クライアント',
	NewClientNextStepsModalAddAnotherClient: '別のクライアントを追加',
	NewClientNextStepsModalBookAppointment: '予約する',
	NewClientNextStepsModalBookAppointmentDescription: '今後の予定を予約するか、タスクを作成します。',
	NewClientNextStepsModalCompleteBasicInformation: '完全な顧客記録',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'クライアント情報を追加し、次の手順を記録します。',
	NewClientNextStepsModalCreateInvoice: '請求書を作成する',
	NewClientNextStepsModalCreateInvoiceDescription: 'クライアントの支払い情報を追加するか、請求書を作成します。',
	NewClientNextStepsModalCreateNote: 'メモを作成するか、ドキュメントをアップロードする',
	NewClientNextStepsModalCreateNoteDescription: 'クライアントのメモとドキュメントをキャプチャします。',
	NewClientNextStepsModalDescription: 'クライアント レコードを作成したら、次に実行するアクションをいくつか示します。',
	NewClientNextStepsModalSendIntake: '摂取量を送信',
	NewClientNextStepsModalSendIntakeDescription:
		'クライアント情報を収集し、記入と署名のために追加のフォームを送信します。',
	NewClientNextStepsModalSendMessage: 'メッセージを送信',
	NewClientNextStepsModalSendMessageDescription: 'メッセージを作成してクライアントに送信します。',
	NewClientNextStepsModalTitle: '次のステップ',
	NewClientSuccess: '新しいクライアントが正常に作成されました',
	NewClients: '新規クライアント',
	NewConnectedApp: '新しい接続アプリ',
	NewContact: '新しい連絡先',
	NewContactNextStepsModalAddRelationship: '関係を追加する',
	NewContactNextStepsModalAddRelationshipDescription:
		'この連絡先を関連するクライアントまたはグループにリンクします。',
	NewContactNextStepsModalBookAppointment: '予約する',
	NewContactNextStepsModalBookAppointmentDescription: '今後の予約を予約するか、タスクを作成します。',
	NewContactNextStepsModalCompleteProfile: 'プロフィールを完成させる',
	NewContactNextStepsModalCompleteProfileDescription: '連絡先情報を追加して、次のステップを把握します。',
	NewContactNextStepsModalCreateNote: 'ノートを作成するか、ドキュメントをアップロードします。',
	NewContactNextStepsModalCreateNoteDescription: 'クライアントのメモとドキュメントをキャプチャします。',
	NewContactNextStepsModalDescription: '連絡先を作成したので、次に取るべき行動をいくつかご紹介します。',
	NewContactNextStepsModalInviteToPortal: 'ポータルへの招待',
	NewContactNextStepsModalInviteToPortalDescription: 'ポータルへのアクセスを招待してください。',
	NewContactNextStepsModalTitle: '次のステップ',
	NewContactSuccess: '新しい連絡先を作成しました',
	NewDateOverrideButton: '新しい日付の上書き',
	NewDiagnosis: '診断を追加',
	NewField: '新しいフィールド',
	NewFolder: '新しいフォルダ',
	NewInvoice: '新しい請求書',
	NewLocation: '新しい場所',
	NewLocationFailure: '新しい場所を作成できませんでした',
	NewLocationSuccess: '新しい場所が正常に作成されました',
	NewManualPayer: '新しい手動支払者',
	NewNote: '新しいメモ',
	NewNoteCreated: '新しいメモを作成しました',
	NewPassword: '新しいパスワード',
	NewPayer: '新規支払者',
	NewPaymentMethod: '新しい支払い方法',
	NewPolicy: '新しいポリシー',
	NewRelationship: '新しい関係',
	NewReminder: '新しいリマインダー',
	NewSchedule: '新しいスケジュール',
	NewSection: '新しいセクション',
	NewSectionOld: '新しいセクション [旧]',
	NewSectionWithGrid: 'グリッド付きの新しいセクション',
	NewService: '新サービス',
	NewServiceFailure: '新しいサービスを作成できませんでした',
	NewServiceSuccess: '新しいサービスの作成に成功しました',
	NewStatus: '新しいステータス',
	NewTask: '新しい仕事',
	NewTaxRate: '新しい税率',
	NewTeamMemberNextStepsModalAssignClients: 'クライアントを割り当てる',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'チームメンバーに特定のクライアントを割り当てます。',
	NewTeamMemberNextStepsModalAssignServices: 'サービスの割り当て',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'割り当てられたサービスを管理し、必要に応じて価格を調整します。',
	NewTeamMemberNextStepsModalBookAppointment: '予約する',
	NewTeamMemberNextStepsModalBookAppointmentDescription: '今後の予約を予約するか、タスクを作成します。',
	NewTeamMemberNextStepsModalCompleteProfile: 'プロフィールを完成させる',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'チームメンバーの詳細を追加して、プロフィールを完成させてください。',
	NewTeamMemberNextStepsModalDescription: 'チームメンバーを作成したので、次にすべきことをいくつかご紹介します。',
	NewTeamMemberNextStepsModalEditPermissions: '編集権限',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'彼らのアクセスレベルを調整して、適切な権限を持っていることを確認してください。',
	NewTeamMemberNextStepsModalSetAvailability: '利用可能性を設定する',
	NewTeamMemberNextStepsModalSetAvailabilityDescription:
		'スケジュールを作成するために、彼らの利用可能性を設定します。',
	NewTeamMemberNextStepsModalTitle: '次のステップ',
	NewTemplateFolderDescription: 'ドキュメントを整理するための新しいフォルダーを作成します。',
	NewUIUpdateBannerButton: 'アプリを再読み込み',
	NewUIUpdateBannerTitle: '新しいアップデートが準備できました!',
	NewZealand: 'ニュージーランド',
	Newest: '最新',
	NewestUnreplied: '最新未返信',
	Next: '次',
	NextInvoiceIssueDate: '次の請求書発行日',
	NextNDays: '次の {number} 日',
	Niece: '姪',
	No: 'いいえ',
	NoAccessGiven: 'アクセスできません',
	NoActionConfigured: 'アクションが設定されていません',
	NoActivePolicies: '有効なポリシーはありません',
	NoActiveReferrals: 'アクティブな紹介はありません',
	NoAppointmentsFound: '予約が見つかりませんでした',
	NoAppointmentsHeading: 'クライアントの予約と活動を管理する',
	NoArchivedPolicies: 'アーカイブされたポリシーはありません',
	NoAvailableTimes: '時間が見つかりませんでした。',
	NoBillingItemsFound: '請求項目が見つかりません。',
	NoCalendarsSynced: 'カレンダーが同期されていません',
	NoClaimsFound: '請求が見つかりません',
	NoClaimsHeading: '払い戻しの請求提出を合理化します',
	NoClientsHeading: 'クライアントの記録をまとめる',
	NoCompletedReferrals: '完全な紹介はありません',
	NoConnectionsHeading: 'クライアントとのコミュニケーションを効率化する',
	NoContactsGivenAccess: 'このメモへのアクセス権を付与されたクライアントや連絡先はありません',
	NoContactsHeading: 'あなたの実践をサポートする人々とつながり続ける',
	NoCopayOrCoinsurance: '自己負担や共同保険はありません',
	NoCustomServiceSchedule:
		'カスタムスケジュールは設定されていません。空き状況はチームメンバーの空き状況に依存します。',
	NoDescription: '説明なし',
	NoDocumentationHeading: 'ノートを安全に作成して保存',
	NoDuplicateRecordsHeading: '顧客レコードに重複はありません',
	NoEffect: '無効',
	NoEnrolmentProfilesFound: '登録プロファイルが見つかりません',
	NoGlossaryItems: '用語集の項目はありません',
	NoInvitedReferrals: '招待された紹介者はいません',
	NoInvoicesFound: '請求書が見つかりません。',
	NoInvoicesHeading: '請求書と支払いを自動化する',
	NoLimit: '制限なし',
	NoLocationsFound: '場所が見つかりませんでした',
	NoLocationsWillBeAdded: '場所を追加することはできません。',
	NoNoteFound: 'ノートが見つかりません',
	NoPaymentMethods: '保存された支払い方法がありません。支払い時に支払い方法を追加できます。',
	NoPermissionError: '権限がありません',
	NoPermissions: 'このページを表示する権限がありません',
	NoPolicy: 'キャンセルポリシーは追加されていません',
	NoRecordsHeading: 'クライアントの記録をパーソナライズする',
	NoRecordsToDisplay: '表示する {resource} がありません。',
	NoRelationshipsHeading: 'クライアントをサポートする人々とつながり続ける',
	NoRemindersFound: 'リマインダーが見つかりません',
	NoResultsFound: '結果が見つかりません',
	NoResultsFoundDescription: '検索に一致するアイテムが見つかりません',
	NoServicesAdded: 'サービスは追加されていません。',
	NoServicesApplied: '適用されたサービスはありません',
	NoServicesWillBeAdded: 'サービスは追加されません。',
	NoTemplate: '練習テンプレートが保存されていません',
	NoTemplatesHeading: '自分のテンプレートを作成する',
	NoTemplatesInFolder: 'このフォルダにはテンプレートがありません。',
	NoTitle: 'タイトルなし',
	NoTrashItemsHeading: '削除されたアイテムが見つかりません',
	NoTriggerConfigured: 'トリガーが設定されていません',
	NoUnclaimedItemsFound: '未請求のアイテムは見つかりませんでした。',
	NonAiTemplates: '非AIテンプレート',
	None: 'なし',
	NotAvailable: '利用できません',
	NotCovered: '対象外',
	NotFoundSnackbar: 'リソースが見つかりません。',
	NotRequiredField: '不要',
	Note: '注記',
	NoteDuplicateSuccess: 'メモを複製しました',
	NoteEditModeViewSwitcherDescription: 'メモの作成と編集',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} は {noteTitle} フォームを送信しました。',
	NoteLockSuccess: '{title} はロックされました。',
	NoteModalAttachmentButton: '添付ファイルを追加する',
	NoteModalPhotoButton: '写真を追加/撮影',
	NoteModalTrascribeButton: 'ライブ音声を書き起こす',
	NoteResponderModeViewSwitcherDescription: 'フォームを送信し、回答を確認する',
	NoteResponderModeViewSwitcherTooltipTitle: '顧客に代わってフォームに回答し、送信する',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'クライアントとしてフォームに記入して送信する',
	NoteUnlockSuccess: '{title} はアンロックされました',
	NoteViewModeViewSwitcherDescription: '閲覧のみのアクセス',
	Notes: 'ノート',
	NotesAndForms: 'メモとフォーム',
	NotesCategoryDescription: 'クライアントとのやり取りを記録するために',
	NothingToSeeHere: 'ここには何も見られません',
	Notification: '通知',
	NotificationIgnoredMessage: 'すべての{notificationType}通知は無視されます',
	NotificationRestoredMessage: 'すべての {notificationType} 通知が復元されました',
	NotificationSettingBillingDescription: 'クライアントの支払いの更新とリマインダーに関する通知を受け取ります。',
	NotificationSettingBillingTitle: '請求と支払い',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} のみ} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'クライアントの支払いの更新とリマインダーに関する通知を受け取ります。',
	NotificationSettingClientDocumentationTitle: 'クライアントとドキュメント',
	NotificationSettingCommunicationsDescription: '接続したチャンネルから受信トレイの通知と更新を受け取る',
	NotificationSettingCommunicationsTitle: 'コミュニケーション',
	NotificationSettingEmail: 'メール',
	NotificationSettingInApp: 'アプリ内',
	NotificationSettingPanelDescription: 'アクティビティや推奨事項の通知を選択してください。',
	NotificationSettingPanelTitle: '通知の設定',
	NotificationSettingSchedulingDescription:
		'チーム メンバーまたはクライアントが予約、再スケジュール、または予約をキャンセルしたときに通知を受け取ります。',
	NotificationSettingSchedulingTitle: 'スケジューリング',
	NotificationSettingUpdateSuccess: '通知設定が正常に更新されました',
	NotificationSettingWhereYouReceiveNotifications: 'これらの通知を受け取りたい場所',
	NotificationSettingWorkspaceDescription:
		'システムの変更、問題、データ転送、およびサブスクリプションのリマインダーに関する通知を受け取ります。',
	NotificationSettingWorkspaceTitle: 'ワークスペース',
	NotificationTemplateUpdateFailed: '通知テンプレートの更新に失敗しました',
	NotificationTemplateUpdateSuccess: '通知テンプレートが正常に更新されました',
	NotifyAttendeesOfTaskCancellationModalDescription: '参加者にキャンセル通知メールを送信しますか?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'キャンセルを送信',
	NotifyAttendeesOfTaskConfirmationModalDescription: '参加者に確認通知メールを送信しますか?',
	NotifyAttendeesOfTaskConfirmationModalTitle: '確認を送信',
	NotifyAttendeesOfTaskDeletedModalTitle: '参加者にキャンセルメールを送信しますか?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {は} other {は}} メールアドレスを持っていないため、自動通知やリマインダーを受信しません。',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {は} other {は}} メールアドレスを持っていないため、自動通知とリマインダーを受け取りません。',
	NotifyAttendeesOfTaskModalTitle: '参加者に通知メールを送信しますか?',
	NotifyAttendeesOfTaskSnackbar: '通知を送信しています',
	NuclearMedicineTechnologist: '核医学技師',
	NumberOfClaims: '{number, plural, one {# 請求} other {# 請求}}',
	NumberOfClients: '{number, plural, one {# クライアント} other {# クライアント}}',
	NumberOfContacts: '{number, plural, one {# 連絡先} other {# 連絡先}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {エントリ} other {エントリ}} 見つかりました',
	NumberOfErrors: '{count, plural, one {# エラー} other {# エラー}}',
	NumberOfInvoices: '{number, plural, one {# 請求書} other {# 請求書}}',
	NumberOfLineitemsToCredit:
		'あなたは <mark>{count} {count, plural, one {行項目} other {行項目}}</mark> を発行することができます。',
	NumberOfPayments: '{number, plural, one {# 支払} other {# 支払}}',
	NumberOfRelationships: '{number, plural, one {# 関係} other {# 関係}}',
	NumberOfResources: '{number, plural, one {# リソース} other {# リソース}}',
	NumberOfTeamMembers: '{number, plural, one {# チームメンバー} other {# チームメンバー}}',
	NumberOfTrashItems: '{number, plural, one {# アイテム} other {# アイテム}}',
	NumberOfUninvoicedAmounts:
		'あなたは <mark>{count} 請求されていない {count, plural, one {金額} other {金額}}</mark> を請求する必要があります。',
	NumberedList: '番号付きリスト',
	Nurse: '看護師',
	NurseAnesthetist: '麻酔科看護師',
	NurseAssistant: '看護助手',
	NurseEducator: '看護教育者',
	NurseMidwife: '助産師',
	NursePractitioner: '看護師',
	Nurses: '看護師',
	Nursing: '看護',
	Nutritionist: '栄養士',
	Nutritionists: '栄養士',
	ObstetricianOrGynecologist: '産婦人科医',
	Occupation: '職業',
	OccupationalTherapist: '作業療法士',
	OccupationalTherapists: '作業療法士',
	OccupationalTherapy: '作業療法',
	Occurrences: '発生事例',
	Of: 'の',
	Ohio: 'オハイオ州',
	OldPassword: '以前のパスワード',
	OlderMessages: '{count} 件の古いメッセージ',
	Oldest: '最古',
	OldestUnreplied: '最も古い未返信',
	On: 'の上',
	OnboardingBusinessAgreement: '私と会社を代表して、私は {businessAssociateAgreement} に同意します。',
	OnboardingLoadingOccupationalTherapist: '<mark>作業療法士</mark>Carepatronの顧客の4分の1を占めています',
	OnboardingLoadingProfession: 'Carepatron を使って成功している <mark>{profession}</mark> はたくさんいます。',
	OnboardingLoadingPsychologist: '<mark>心理学者</mark>Carepatronの顧客の半数以上を占めています',
	OnboardingLoadingSubtitleFive: '私たちの使命は<mark>ヘルスケアソフトウェアが利用可能</mark>みんなに。',
	OnboardingLoadingSubtitleFour:
		'<mark>簡素化された健康ソフトウェア</mark>世界中で10,000人以上の人々にご利用いただいています。',
	OnboardingLoadingSubtitleThree: '保存<mark>週1日</mark>Carepatron の助けを借りて管理タスクを実行します。',
	OnboardingLoadingSubtitleTwo: '保存<mark>2時間</mark>Carepatron の助けを借りて、毎日管理タスクを実行します。',
	OnboardingReviewLocationOne: 'ホランドパークメンタルヘルスセンター',
	OnboardingReviewLocationThree: 'マウント・エデン・ヘルスケアの看護師',
	OnboardingReviewLocationTwo: 'ライフハウスクリニック',
	OnboardingReviewNameOne: 'アヌル P',
	OnboardingReviewNameThree: 'アリスE',
	OnboardingReviewNameTwo: 'クララW',
	OnboardingReviewOne:
		'「Carepatron は非常に直感的に使用できます。これにより、私たちの診療所の運営が非常にスムーズになり、管理者チームも必要なくなります。」',
	OnboardingReviewThree:
		'「機能とコストの両面で、これまで使用した中で最高のソリューションです。ビジネスの成長に必要なものがすべて揃っています。」',
	OnboardingReviewTwo:
		'「私は carepatron アプリも気に入っています。外出中でもクライアントや仕事を追跡するのに役立ちます。」',
	OnboardingTitle: `始めましょう<mark>知る
君の方がいい</mark>`,
	Oncologist: '腫瘍専門医',
	Online: 'オンライン',
	OnlineBookingColorTheme: 'オンライン予約のカラーテーマ',
	OnlineBookings: 'オンライン予約',
	OnlineBookingsHelper: 'オンライン予約が可能な時間帯と対象顧客を選択する',
	OnlinePayment: 'オンライン支払い',
	OnlinePaymentSettingCustomInfo: 'このサービスのオンライン支払い設定は、グローバル予約設定とは異なります。',
	OnlinePaymentSettings: 'オンライン支払い設定',
	OnlinePaymentSettingsInfo: 'オンライン予約時にサービスの支払いを収集し、支払いを安全かつ効率的に行う',
	OnlinePaymentSettingsPaymentsDisabled:
		'オンライン予約中は、支払いが無効になっているため、収集できません。支払いを有効にするには、支払い設定を確認してください。',
	OnlinePaymentSettingsStripeNote:
		'オンライン予約の支払いを<span style="font-weight:bold;">受け取る</span>ための{action}で、決済プロセスを効率化しましょう。',
	OnlinePaymentsNotSupportedForCurrency: 'オンライン決済は {currency} ではサポートされていません。',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'申し訳ございませんが、この通貨ではオンライン決済はサポートされていません',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'申し訳ございませんが、お住まいの国ではオンライン決済はまだサポートされていません',
	OnlineScheduling: 'オンラインスケジュール',
	OnlyVisibleToYou: 'あなただけに表示されます',
	OnlyYou: 'あなただけ',
	OnsetDate: '発症日',
	OnsetOfCurrentSymptomsOrIllness: '現在の症状または病気の発症',
	Open: '開ける',
	OpenFile: 'ファイルを開く',
	OpenSettings: '設定を開く',
	Ophthalmologist: '眼科医',
	OptimiseTelehealthCalls: '遠隔医療通話を最適化する',
	OptimizeServiceTimes: 'サービス時間の最適化',
	Options: 'オプション',
	Optometrist: '検眼医',
	Or: 'または',
	OrAttachSingleFile: 'ファイルを添付する',
	OrDragAndDrop: 'または、ドラッグアンドドロップ',
	OrderBy: '並び替え',
	Oregon: 'オレゴン',
	OrganisationOrIndividual: '組織または個人',
	OrganizationPlanInclusion1: '高度な権限',
	OrganizationPlanInclusion2: '無料のクライアントデータインポートサポート',
	OrganizationPlanInclusion3: '専任のサクセスマネージャー',
	OrganizationPlanInclusionHeader: 'Professional のすべてに加えて...',
	Orthodontist: '矯正歯科医',
	Orthotist: '整形外科医',
	Other: '他の',
	OtherAdjustments: 'その他の調整',
	OtherAdjustmentsTableEmptyState: '調整が見つかりません',
	OtherEvents: 'その他のイベント',
	OtherId: 'その他のID',
	OtherIdQualifier: 'その他のID修飾子',
	OtherPaymentMethod: 'その他の支払い方法',
	OtherPlanMessage:
		'診療所のニーズを管理しましょう。現在のプランを見直し、利用状況を監視し、チームの成長に合わせて、より多くの機能を解放するためのアップグレードオプションを検討してください。',
	OtherPolicy: 'その他の保険',
	OtherProducts: '他にはどのような製品やツールを使用していますか?',
	OtherServices: 'その他のサービス',
	OtherTemplates: 'その他のテンプレート',
	Others: 'その他',
	OthersPeople: `{n, plural, 	one {1 人}
	other {# 人}
}`,
	OurResearchTeamReachOut:
		'Carepatron がお客様のニーズにどのように応えられるかについて、弊社の研究チームが詳しくお聞きしてもよろしいでしょうか?',
	OutOfOffice: 'オフィスの外',
	OutOfOfficeColor: '不在時の色',
	OutOfOfficeHelper: '選ばれたチームメンバーの一部は不在です',
	OutsideLabCharges: '外部ラボ費用',
	OutsideOfWorkingHours: '勤務時間外',
	OutsideWorkingHoursHelper: '選ばれたチームメンバーの中には勤務時間外の人もいる',
	Overallocated: '割り当て超過',
	OverallocatedPaymentDescription: `この支払いは請求対象項目に過剰に割り当てられています。
未払いの項目に割り当てを追加するか、クレジットまたは払い戻しを発行します。`,
	OverallocatedPaymentTitle: '割り当て超過支払い',
	OverdueTerm: '延滞期間（日数）',
	OverinvoicedAmount: '請求額超過',
	Overpaid: '過払い',
	OverpaidAmount: '過払い金',
	Overtime: '時間とともに',
	Owner: '所有者',
	POS: 'POS',
	POSCode: 'POSコード',
	POSPlaceholder: 'POS',
	PageBlockerDescription: '保存されていない変更は失われます。それでも退出しますか？',
	PageBlockerTitle: '変更を破棄しますか？',
	PageFormat: 'ページフォーマット',
	PageNotFound: 'ページが見つかりません',
	PageNotFoundDescription: 'このページにアクセスできなくなったか、見つかりません',
	PageUnauthorised: 'アクセス権がありません',
	PageUnauthorisedDescription: 'このページにアクセスする権限がありません',
	Paid: '有料',
	PaidAmount: '支払い金額',
	PaidAmountMinimumValueError: '支払額は0より大きくなければなりません',
	PaidAmountRequiredError: '支払金額が必要です',
	PaidItems: '有料アイテム',
	PaidMultiple: '有料',
	PaidOut: '支払い済み',
	ParagraphStyles: '段落スタイル',
	Parent: '親',
	Paris: 'パリ',
	PartialRefundAmount: '部分返金済み（残額 {amount}）',
	PartiallyFull: '一部満杯',
	PartiallyPaid: '一部支払い済み',
	PartiallyRefunded: '一部返金',
	Partner: 'パートナー',
	Password: 'パスワード',
	Past: '過去',
	PastDateOverridesEmpty: 'イベントが終了するとすぐに日付の上書きがここに表示されます',
	Pathologist: '病理学者',
	Patient: '忍耐強い',
	Pause: '一時停止',
	Paused: '一時停止',
	Pay: '支払う',
	PayMonthly: '毎月払い',
	PayNow: '今払う',
	PayValue: 'お支払い {showPrice, select, true {{price}} other {now}}',
	PayWithOtherCard: '他のカードで支払う',
	PayYearly: '年間払い',
	PayYearlyPercentOff: '年払い <mark>{percent}% オフ</mark>',
	Payer: '支払者',
	PayerClaimId: '支払者請求ID',
	PayerCoverage: 'カバレッジ',
	PayerDetails: '支払者の詳細',
	PayerDetailsDescription: 'アカウントに追加された支払人の詳細を表示し、登録を管理します。',
	PayerID: '支払人ID',
	PayerId: '支払人ID',
	PayerName: '支払人名',
	PayerPhoneNumber: '支払者の電話番号',
	Payers: '支払者',
	Payment: '支払い',
	PaymentAccountUpdated: 'アカウントが更新されました！',
	PaymentAccountUpgraded: 'アカウントがアップグレードされました!',
	PaymentAmount: '支払額',
	PaymentDate: '支払期日',
	PaymentDetails: 'お支払い情報',
	PaymentForUsersPerMonth: '{billedUsers, plural, one {# 人} other {# 人}} 1 か月分の支払い',
	PaymentInfoFormPrimaryText: '支払情報',
	PaymentInfoFormSecondaryText: '支払いの詳細を収集する',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'この請求書はすでに支払われています。',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'この請求書はすでに処理中です。',
	PaymentIntentAmountMismatchSnackbar: '請求書の合計金額が変更されました。お支払い前に変更内容をご確認ください。',
	PaymentIntentSyncTimeoutSnackbar:
		'お支払いは完了しましたが、タイムアウトが発生しました。ページを更新してください。お支払いが表示されない場合は、サポートにお問い合わせください。',
	PaymentMethod: '支払方法',
	PaymentMethodDescription: '実践的な支払い方法を追加して管理し、サブスクリプションの請求プロセスを効率化します。',
	PaymentMethodLabelBank: '銀行口座',
	PaymentMethodLabelCard: 'カード',
	PaymentMethodLabelFallback: '支払方法',
	PaymentMethodRequired: 'サブスクリプションを変更する前に支払い方法を追加してください',
	PaymentMethods: 'お支払い方法',
	PaymentProcessing: '支払い手続き！',
	PaymentProcessingFee: '支払いには {amount} の処理手数料が含まれます。',
	PaymentReports: '支払レポート (ERA)',
	PaymentSettings: '支払い設定',
	PaymentSuccessful: '支払い完了！',
	PaymentType: '払いの種類',
	Payments: '支払い',
	PaymentsAccountDisabledNotificationSubject: `{paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} を通じたオンライン決済は無効になっています。
支払いを有効にするには、支払い設定を確認してください。`,
	PaymentsEmptyStateDescription: '支払いが見つかりませんでした。',
	PaymentsUnallocated: '未割り当ての支払い',
	PayoutDate: '支払日',
	PayoutsDisabled: '支払いが無効になっています',
	PayoutsEnabled: '支払いが有効',
	PayoutsStatus: '支払い状況',
	Pediatrician: '小児科医',
	Pen: 'ペン',
	Pending: '保留中',
	People: '{rosterSize } 人',
	PeopleCount: '人 ({count})',
	PerMonth: '/ 月',
	PerUser: 'ユーザーごと',
	Permission: '許可',
	PermissionRequired: '許可が必要',
	Permissions: '権限',
	PermissionsClientAndContactDocumentation: 'クライアント ',
	PermissionsClientAndContactProfiles: 'クライアント ',
	PermissionsEditAccess: '編集アクセス',
	PermissionsInvoicesAndPayments: '請求書 ',
	PermissionsScheduling: 'スケジュール',
	PermissionsUnassignClients: 'クライアントの割り当てを解除',
	PermissionsUnassignClientsConfirmation: 'これらのクライアントの割り当てを解除してもよろしいですか?',
	PermissionsValuesAssigned: '割り当てのみ',
	PermissionsValuesEverything: 'すべて',
	PermissionsValuesNone: 'なし',
	PermissionsValuesOwnCalendar: '独自のカレンダー',
	PermissionsViewAccess: 'アクセスを表示',
	PermissionsWorkspaceSettings: 'ワークスペース設定',
	Person: '{rosterSize} 人',
	PersonalDetails: '個人情報',
	PersonalHealthcareHistoryStoreDescription: '個人の健康履歴を1か所にまとめて安全に保管',
	PersonalTrainer: '個人トレーナー',
	PersonalTraining: 'パーソナルトレーニング',
	PersonalizeWorkspace: 'ワークスペースをパーソナライズする',
	PersonalizingYourWorkspace: 'ワークスペースのパーソナライズ',
	Pharmacist: '薬剤師',
	Pharmacy: '薬局',
	PhoneCall: '電話',
	PhoneNumber: '電話番号',
	PhoneNumberOptional: '電話番号（オプション）',
	PhotoBy: '写真提供',
	PhysicalAddress: '実在住所',
	PhysicalTherapist: '理学療法士',
	PhysicalTherapists: '理学療法士',
	PhysicalTherapy: '理学療法',
	Physician: '医師',
	PhysicianAssistant: '医師アシスタント',
	Physicians: '医師',
	Physiotherapist: '理学療法士',
	PlaceOfService: 'サービス提供場所',
	Plan: 'プラン',
	PlanAndReport: '計画/レポート',
	PlanId: 'プランID',
	PlansAndReportsCategoryDescription: '治療計画と結果の要約のために',
	PleaseRefreshThisPageToTryAgain: 'このページをリフレッシュして、もう一度お試しください。',
	PleaseWait: 'お待ちください...',
	PleaseWaitForHostToJoin: 'ホストの参加を待っています...',
	PleaseWaitForHostToStart: '主催者がこの会議を開始するまでお待ちください。',
	PlusAdd: '+ 追加',
	PlusOthers: '+{count} 人',
	PlusPlanInclusionFive: '共有受信トレイ',
	PlusPlanInclusionFour: 'グループビデオ通話',
	PlusPlanInclusionHeader: 'Essentialのすべて  ',
	PlusPlanInclusionOne: '無制限のAI',
	PlusPlanInclusionSix: 'カスタムブランディング',
	PlusPlanInclusionThree: 'グループスケジュール',
	PlusPlanInclusionTwo: '無制限のストレージ ',
	PlusSubscriptionPlanSubtitle: '実践を最適化し成長させるために',
	PlusSubscriptionPlanTitle: 'プラス',
	PoliceOfficer: '警察官',
	PolicyDates: '保険期間',
	PolicyHolder: '保険契約者',
	PolicyHoldersAddress: '保険契約者住所',
	PolicyMemberId: 'ポリシー会員ID',
	PolicyStatus: 'ポリシーステータス',
	Popular: '人気のある',
	PortalAccess: 'ポータルアクセス',
	PortalNoAppointmentsHeading: '今後および過去のすべての予約を追跡する',
	PortalNoDocumentationHeading: 'ドキュメントを安全に作成および保存する',
	PortalNoRelationshipsHeading: 'あなたの旅をサポートする人々をまとめる',
	PosCodeErrorMessage: 'POS コードが必要です。',
	PosoNumber: 'PO/SO番号',
	PossibleClientDuplicate: 'クライアントが重複している可能性があります',
	PotentialClientDuplicateTitle: '重複する可能性のあるクライアントレコード',
	PotentialClientDuplicateWarning:
		'このクライアント情報は、すでにクライアント リストに存在している可能性があります。必要に応じて既存のレコードを確認して更新するか、新しいクライアントの作成を続行してください。',
	PoweredBy: '搭載',
	Practice: '練習する',
	PracticeDetails: '練習の詳細',
	PracticeInfoHeader: 'ビジネス情報',
	PracticeInfoPlaceholder: `診療名、
国別プロバイダー識別子、
雇用者識別番号`,
	PracticeLocation: 'あなたの練習は',
	PracticeSettingsAvailabilityTab: '可用性',
	PracticeSettingsBillingTab: '課金設定',
	PracticeSettingsClientSettingsTab: 'クライアント設定',
	PracticeSettingsGeneralTab: '一般的な',
	PracticeSettingsOnlineBookingTab: 'オンライン予約',
	PracticeSettingsServicesTab: 'サービス',
	PracticeSettingsTaxRatesTab: '税率',
	PracticeTemplate: '練習用テンプレート',
	Practitioner: '実践者',
	PreferredLanguage: '優先言語',
	PreferredName: '好ましい名称',
	Prescription: '処方箋',
	PreventionSpecialist: '予防スペシャリスト',
	Preview: 'プレビュー',
	PreviewAndSend: 'プレビューして送信',
	PreviewUnavailable: 'このファイル形式はプレビューできません',
	PreviousNotes: '前回のメモ',
	Price: '価格',
	PriceError: '価格は0より大きくなければなりません',
	PricePerClient: '顧客あたりの価格',
	PricePerUser: 'ユーザーあたり',
	PricePerUserBilledAnnually: 'ユーザーごとに年間請求',
	PricePerUserPerPeriod: '{price} ユーザーあたり / {isMonthly, select, true {月} other {年}}',
	PricingGuide: '料金プランガイド',
	PricingPlanPerMonth: '/ 月',
	PricingPlanPerYear: '/ 年',
	Primary: '主要な',
	PrimaryInsurance: '主な保険',
	PrimaryPolicy: '主な保険',
	PrimaryTimezone: 'プライマリタイムゾーン',
	Print: '印刷',
	PrintToCms1500: 'CMS1500に印刷',
	PrivatePracticeConsultant: '個人開業コンサルタント',
	Proceed: '続行する',
	ProcessAtTimeOfBookingDesc: 'オンラインで予約するには、サービス料金全額を支払う必要があります。',
	ProcessAtTimeOfBookingLabel: '予約時に支払い手続きを行う',
	Processing: '処理',
	ProcessingFee: '手数料',
	ProcessingFeeToolTip: `Carepatron を使用すると、処理手数料を顧客に請求できます。
一部の法域では、顧客に処理手数料を請求することが禁止されています。適用される法律を遵守するのはお客様の責任です。`,
	ProcessingRequest: 'リクエストを処理中...',
	Product: '製品',
	Profession: '職業',
	ProfessionExample: 'セラピスト、栄養士、歯科医',
	ProfessionPlaceholder: '職業を入力するか、リストから選択してください',
	ProfessionalPlanInclusion1: '無制限のストレージ',
	ProfessionalPlanInclusion2: '無制限のタスク',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24時間年中無休のカスタマーサポート',
	ProfessionalPlanInclusion5: 'SMSリマインダー',
	ProfessionalPlanInclusionHeader: 'Starter のすべてに加えて...',
	Professions: '職業',
	Profile: 'プロフィール',
	ProfilePhotoFileSizeLimit: 'ファイルサイズ制限5MB',
	ProfilePopoverSubTitle: 'あなたは <strong>{email}</strong> としてサインインしています。',
	ProfilePopoverTitle: 'あなたのワークスペース',
	PromoCode: 'プロモーションコード',
	PromotionCodeApplied: '{promo} 適用済み',
	ProposeNewDateTime: '新しい日付/時間を提案する',
	Prosthetist: '義肢装具士',
	Provider: 'プロバイダー',
	ProviderBillingPlanExpansionManageButton: 'プランの管理',
	ProviderCommercialNumber: 'プロバイダ商用番号',
	ProviderDetails: 'プロバイダーの詳細',
	ProviderDetailsAddress: '住所',
	ProviderDetailsName: '名前',
	ProviderDetailsPhoneNumber: '電話番号',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'申し訳ございませんが、このプロバイダにはすでに請求先アカウントがあります',
	ProviderInfoPlaceholder: `スタッフ名、
電子メールアドレス、
電話番号、
国別プロバイダー識別子、
ライセンス番号`,
	ProviderIsChargedProcessingFee: '手数料はお客様負担となります',
	ProviderPaymentFormBackButton: '戻る',
	ProviderPaymentFormBillingAddressCity: '市',
	ProviderPaymentFormBillingAddressCountry: '国',
	ProviderPaymentFormBillingAddressLine1: 'ライン1',
	ProviderPaymentFormBillingAddressPostalCode: '郵便番号',
	ProviderPaymentFormBillingEmail: 'Eメール',
	ProviderPaymentFormCardCvc: 'VC',
	ProviderPaymentFormCardDetailsTitle: 'クレジットカードの詳細',
	ProviderPaymentFormCardExpiry: '有効期限',
	ProviderPaymentFormCardHolderAddressTitle: '住所',
	ProviderPaymentFormCardHolderName: 'クレジットカード名義人氏名',
	ProviderPaymentFormCardHolderTitle: 'カード会員の詳細',
	ProviderPaymentFormCardNumber: 'カード番号',
	ProviderPaymentFormPlanTitle: '選択したプラン',
	ProviderPaymentFormPlanTotalTitle: '合計 ({currency}):',
	ProviderPaymentFormSaveButton: 'サブスクリプションを保存',
	ProviderPaymentFreePlanDescription:
		'無料プランを選択すると、各スタッフ メンバーはプロバイダー内のクライアントにアクセスできなくなります。ただし、アクセスは維持され、プラットフォームを引き続き使用できます。',
	ProviderPaymentStepName: 'レビュー ',
	ProviderPaymentSuccessSnackbar: '素晴らしい! 新しいプランが正常に保存されました。',
	ProviderPaymentTitle: 'レビュー ',
	ProviderPlanNetworkIdentificationNumber: 'プロバイダープランネットワーク識別番号',
	ProviderRemindersSettingsBannerAction: 'ワークフロー管理へ行く',
	ProviderRemindersSettingsBannerDescription:
		'新し<b >ワークフロー管理</b>タブの<b >設定</b>で、すべてのリマインダーを見つけます。 このアップデートにより、強力な新機能、改善されたテンプレート、スマートな自動化ツールが導入され、生産性が向上します。 🚀',
	ProviderRemindersSettingsBannerTitle: 'あなたのリマインダー体験は向上しています',
	ProviderTaxonomy: 'プロバイダー分類',
	ProviderUPINNumber: 'プロバイダーUPIN番号',
	ProviderUsedStoragePercentage: '{providerName} ストレージは {usedStoragePercentage}% 満杯です！',
	PsychiatricNursePractitioner: '精神科看護師',
	Psychiatrist: '精神科医',
	Psychiatrists: '精神科医',
	Psychiatry: '精神医学',
	Psychoanalyst: '精神分析医',
	Psychologist: '心理学者',
	Psychologists: '心理学者',
	Psychology: '心理学',
	Psychometrician: '心理測定学者',
	PsychosocialRehabilitationSpecialist: '心理社会的リハビリテーション専門家',
	Psychotheraphy: '心理療法',
	Psychotherapists: '心理療法士',
	Psychotherapy: '心理療法',
	PublicCallDialogTitle: 'ビデオ通話 ',
	PublicCallDialogTitlePlaceholder: 'Carepatron によるビデオ通話',
	PublicFormBackToForm: '別の回答を送信',
	PublicFormConfirmSubmissionHeader: '送信を確認する',
	PublicFormNotFoundDescription:
		'お探しのフォームは削除されているか、リンクが間違っている可能性があります。URLを確認して、もう一度お試しください。',
	PublicFormNotFoundTitle: 'フォームが見つかりません。',
	PublicFormSubmissionError: '提出に失敗しました。もう一度お試しください。',
	PublicFormSubmissionSuccess: 'フォームが正常に送信されました',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} は {noteTitle} 公開フォームを送信しました。',
	PublicFormSubmittedSubtitle: 'あなたの提出物は受理されました。',
	PublicFormSubmittedTitle: 'ありがとう！',
	PublicFormVerifyClientEmailDialogSubtitle: '確認コードをメールアドレスに送信しました。',
	PublicFormsInvalidConfirmationCode: '無効な確認コード',
	PublicHealthInspector: '公衆衛生検査官',
	PublicTemplates: '公開テンプレート',
	Publish: '公開',
	PublishTemplate: 'テンプレートを公開',
	PublishTemplateFeatureBannerSubheader: 'コミュニティに利益をもたらすように設計されたテンプレート',
	PublishTemplateHeader: '{title} を公開する',
	PublishTemplateToCommunity: 'コミュニティにテンプレートを公開する',
	PublishToCommunity: 'コミュニティに公開',
	PublishToCommunitySuccessMessage: 'コミュニティに公開されました',
	Published: '公開済み',
	PublishedBy: '{name} によって公開',
	PublishedNotesAreNotAutosaved: '公開されたメモは自動保存されません',
	PublishedOnCarepatronCommunity: 'Carepatronコミュニティに掲載',
	Purchase: '購入',
	PushToCalendar: 'カレンダーにプッシュ',
	Question: '質問',
	QuestionOrTitle: '質問またはタイトル',
	QuickActions: 'クイックアクション',
	QuickThemeSwitcherColorBasil: 'バジル',
	QuickThemeSwitcherColorBlueberry: 'ブルーベリー',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'モス',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'スカッシュ',
	RadiationTherapist: '放射線治療師',
	Radiologist: '放射線科医',
	Read: '読む',
	ReadOnly: '閲覧のみ',
	ReadOnlyAppointment: '予約のみ',
	ReadOnlyEventBanner: 'この予約は読み取り専用の予定表から同期されており、編集できません。',
	ReaderMaxDepthHasBeenExceededCode: 'メモがネストされすぎています。一部の項目のインデントを解除してみてください。',
	ReadyForMapping: '準備はいいですか？',
	RealEstateAgent: '不動産業者',
	RearrangeClientFields: 'クライアント設定でクライアントフィールドを並べ替える',
	Reason: '理由',
	ReasonForChange: '変更理由',
	RecentAppointments: '最近の予定',
	RecentServices: '最近のサービス',
	RecentTemplates: '最近のテンプレート',
	RecentlyUsed: '最近使用された',
	Recommended: '推奨',
	RecommendedTemplates: '推奨テンプレート',
	Recording: '録音',
	RecordingEnded: '録音終了',
	RecordingInProgress: '録音中',
	RecordingMicrophoneAccessErrorMessage: '録音を開始するには、ブラウザでマイクのアクセスを許可し、更新してください。',
	RecurrenceCount: ', {count, plural, one {一度} other {# 回}}',
	RecurrenceDaily: '{count, plural, one {毎日} other {日}}',
	RecurrenceEndAfter: '後',
	RecurrenceEndNever: '決して',
	RecurrenceEndOn: 'On',
	RecurrenceEvery: 'すべての {description}',
	RecurrenceMonthly: '{count, plural, one {月次} other {月}}',
	RecurrenceOn: '{description}について',
	RecurrenceOnAllDays: 'すべての日',
	RecurrenceUntil: 'まで {description}',
	RecurrenceWeekly: '{count, plural, one {毎週} other {週間}}',
	RecurrenceYearly: '{count, plural, one {年} other {年}}',
	Recurring: '定期的',
	RecurringAppointment: '定期的な予約',
	RecurringAppointmentsLimitedBannerText:
		'すべての定期的な予定が表示されるわけではありません。期間内のすべての定期的な予定を表示するには、日付範囲を狭めてください。',
	RecurringEventListDescription:
		'<b>{count, plural, one {# イベント} other {# イベント}}</b> は、次の日付に作成されます。',
	Redo: 'やり直す',
	ReferFriends: '友達を紹介する',
	Reference: '参照',
	ReferralCreditedNotificationSubject: '{currency} {amount} の紹介クレジットが適用されました',
	ReferralEmailDefaultBody: `{name}様のおかげで、Carepatronの無料3か月アップグレードをご利用いただけます。新しい働き方を求める300万人以上の医療従事者からなるコミュニティに参加しましょう！
敬具
Carepatronチーム`,
	ReferralEmailDefaultSubject: 'Carepatronへの参加に招待されました',
	ReferralHasNotSignedUpDescription: 'あなたの友達はまだ登録していません',
	ReferralHasSignedUpDescription: 'お友達が登録しました。',
	ReferralInformation: '紹介情報',
	ReferralJoinedNotificationSubject: '{actorProfileName} は Carepatron に参加しました。',
	ReferralListErrorDescription: '紹介リストを読み込めませんでした。',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {月} other {か月}}</b> アクティブ',
	ReferralRewardBanner: 'サインアップして紹介報酬を獲得しましょう!',
	Referrals: '紹介',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} か月 {percentOff, select, 100 {無料} other {{percentOff}% 割引}} {type, select, SubscriptionUpgrade {アップグレード} other {}}',
	ReferredUserBenefitTitle: '彼らは手に入れます！',
	Referrer: 'リファラー',
	ReferringProvider: '紹介プロバイダー',
	ReferringUserBenefitSubtitle:
		'USD${creditAmount} クレジットは、<mark>友だち3人</mark> がアクティブになったら付与されます。',
	ReferringUserBenefitTitle: 'ゲットだぜ！',
	RefreshPage: 'ページを更新',
	Refund: '返金',
	RefundAcknowledgement: '私は {clientName} に Carepatron 以外で返金しました。',
	RefundAcknowledgementValidationMessage: 'この金額を返金したことを確認してください',
	RefundAmount: '返金額',
	RefundContent:
		'払い戻しがクライアントのアカウントに表示されるまでには 7 ～ 10 日かかります。支払い手数料は払い戻されませんが、払い戻しに追加料金はかかりません。払い戻しはキャンセルできず、処理前に確認が必要になる場合もあります。',
	RefundCouldNotBeProcessed: '払い戻しを処理できませんでした',
	RefundError:
		'この払い戻しは現時点では自動的に処理できません。この支払いの払い戻しをリクエストするには、Carepatron サポートにお問い合わせください。',
	RefundExceedTotalValidationError: '支払総額を超えない金額',
	RefundFailed: '払い戻しに失敗しました',
	RefundFailedTooltip: 'この支払いの払い戻しは以前失敗しており、再試行できません。サポートにお問い合わせください。',
	RefundNonStripePaymentContent:
		'この支払いは、Carepatron 以外の方法 (現金、インターネット バンキングなど) を使用して行われました。Carepatron 内で払い戻しを行っても、クライアントに資金は返金されません。',
	RefundReasonDescription: '返金理由を追加すると、顧客の取引を確認するときに役立ちます。',
	Refunded: '返金',
	Refunds: '払い戻し',
	RefundsTableEmptyState: '払い戻しは見つかりませんでした',
	Regenerate: '再生成',
	RegisterButton: '登録する',
	RegisterEmail: 'Eメール',
	RegisterFirstName: 'ファーストネーム',
	RegisterLastName: '苗字',
	RegisterPassword: 'パスワード',
	RegisteredNurse: '看護師',
	RehabilitationCounselor: 'リハビリテーションカウンセラー',
	RejectAppointmentFormTitle: '来られない場合は、理由をお知らせの上、新しい日時を提案してください。',
	Rejected: '拒否',
	Relationship: '関係',
	RelationshipDetails: '関係の詳細',
	RelationshipEmptyStateTitle: 'クライアントをサポートする人々とのつながりを維持する',
	RelationshipPageAccessTypeColumnName: 'プロフィールアクセス',
	RelationshipSavedSuccessSnackbar: '関係が正常に保存されました!',
	RelationshipSelectorFamilyAdmin: '家族',
	RelationshipSelectorFamilyMember: '家族の一員',
	RelationshipSelectorProviderAdmin: 'プロバイダー管理者',
	RelationshipSelectorProviderStaff: 'プロバイダースタッフ',
	RelationshipSelectorSupportNetworkPrimary: '友人',
	RelationshipSelectorSupportNetworkSecondary: 'サポートネットワーク',
	RelationshipStatus: '関係ステータス',
	RelationshipType: '関係タイプ',
	RelationshipTypeClientOwner: 'クライアント',
	RelationshipTypeFamilyAdmin: '人間関係',
	RelationshipTypeFamilyMember: '家族',
	RelationshipTypeFriendOrSupport: '友人またはサポートネットワーク',
	RelationshipTypeProviderAdmin: 'プロバイダー管理者',
	RelationshipTypeProviderStaff: 'スタッフ',
	RelationshipTypeSelectorPlaceholder: '関係の種類を検索',
	Relationships: '人間関係',
	Remaining: '残り',
	RemainingTime: '残り{time}',
	Reminder: 'リマインダー',
	ReminderColor: 'リマインダーの色',
	ReminderDetails: 'リマインダーの詳細',
	ReminderEditDisclaimer: '変更は新しい予定にのみ反映されます',
	ReminderSettings: '予約リマインダー設定',
	Reminders: 'リマインダー',
	Remove: '取り除く',
	RemoveAccess: 'アクセスを削除',
	RemoveAllGuidesBtn: 'すべてのガイドを削除',
	RemoveAllGuidesPopoverBody:
		'オンボーディング ガイドの使用が終了したら、各パネルのガイド削除ボタンを使用するだけです。',
	RemoveAllGuidesPopoverTitle: 'オンボーディング ガイドはもう必要ありませんか?',
	RemoveAsDefault: 'デフォルトとして削除',
	RemoveAsIntake: '摂取量として除去',
	RemoveCol: '列を削除',
	RemoveColor: '色を削除',
	RemoveField: 'フィールドを削除',
	RemoveFromCall: '通話から削除',
	RemoveFromCallDescription: '<mark>{attendeeName}</mark> をこのビデオ通話から削除してもよろしいですか？',
	RemoveFromCollection: 'コレクションから削除',
	RemoveFromCommunity: 'コミュニティから削除',
	RemoveFromFolder: 'フォルダから削除',
	RemoveFromFolderConfirmationDescription:
		'このテンプレートをこのフォルダから削除してもよろしいですか？この操作は元に戻せません。ただし、後で移動することは可能です。',
	RemoveFromIntakeDefault: '摂取デフォルトから削除',
	RemoveGuides: 'ガイドを削除する',
	RemoveMfaConfirmationDescription:
		'多要素認証 (MFA) を削除すると、アカウントのセキュリティが低下します。続行しますか?',
	RemoveMfaConfirmationTitle: 'MFAを削除しますか?',
	RemovePaymentMethodDescription: `これにより、この支払い方法へのアクセスと今後の使用がすべて削除されます。
この操作は元に戻せません。`,
	RemoveRow: '行を削除',
	RemoveTable: 'テーブルを削除',
	RemoveTemplateAsDefaultIntakeSuccess: 'デフォルトの受付テンプレートとして {templateTitle} を正常に削除しました',
	RemoveTemplateFromCommunity: 'コミュニティからテンプレートを削除する',
	RemoveTemplateFromFolder: '{templateTitle} は {folderTitle} から正常に削除されました。',
	Rename: '名前を変更',
	RenderingProvider: 'レンダリングプロバイダー',
	Reopen: '再開',
	ReorderServiceGroupFailure: 'コレクションの並べ替えに失敗しました',
	ReorderServiceGroupSuccess: 'コレクションの並べ替えに成功しました',
	ReorderServicesFailure: 'サービスの並べ替えに失敗しました',
	ReorderServicesSuccess: 'サービスの並べ替えに成功しました',
	ReorderYourServiceList: 'サービスリストを並べ替える',
	ReorderYourServiceListDescription:
		'サービスとコレクションを整理する方法は、オンライン予約ページに反映され、すべてのクライアントに表示されます。',
	RepeatEvery: '繰り返す毎に',
	RepeatOn: '繰り返し on',
	Repeating: '繰り返し',
	Repeats: '繰り返し',
	RepeatsEvery: '繰り返します every',
	Rephrase: '言い換え',
	Replace: '交換する',
	ReplaceBackground: '背景を置き換える',
	ReplacementOfPriorClaim: '以前の請求の置換',
	Report: '報告',
	Reprocess: '再処理',
	RepublishTemplateToCommunity: 'コミュニティへの再公開テンプレート',
	RequestANewVerificationLink: '新しい認証リンクをリクエストする',
	RequestCoverageReport: 'カバレッジレポートをリクエストする',
	RequestingDevicePermissions: 'デバイスの権限をリクエストしています...',
	RequirePaymentMethodDesc: 'オンラインで予約するにはクレジットカードの詳細を入力する必要があります',
	RequirePaymentMethodLabel: 'クレジットカード情報の入力が必要',
	Required: '必須',
	RequiredField: '必須',
	RequiredUrl: 'URLは必須です。',
	Reschedule: '再スケジュール',
	RescheduleBookingLinkModalDescription: 'クライアントはこのリンクを使用して予約日時を変更できます。',
	RescheduleBookingLinkModalTitle: '予約変更リンク',
	RescheduleLink: 'スケジュール変更リンク',
	Resend: '再送信',
	ResendConfirmationCode: '確認コードを再送信',
	ResendConfirmationCodeDescription: 'メールアドレスを入力すると、別の確認コードをメールでお送りします',
	ResendConfirmationCodeSuccess: '確認コードが再送信されました。受信トレイを確認してください',
	ResendNewEmailVerificationSuccess: '新しい確認リンクが {email} に送信されました。',
	ResendVerificationEmail: '確認メールを再送',
	Reset: 'リセット',
	Resources: 'リソース',
	RespiratoryTherapist: '呼吸療法士',
	RespondToHistoricAppointmentError: 'これは歴史的な予約ですので、質問がある場合は担当医にご連絡ください。',
	Responder: '対応者',
	RestorableItemModalDescription:
		'{context} を削除してもよろしいですか？{canRestore, select, true { 後で復元できます。} other {}}',
	RestorableItemModalTitle: '{type}を削除',
	Restore: '復元',
	RestoreAll: 'すべてを復元',
	Restricted: '制限付き',
	ResubmissionCodeReferenceNumber: '再提出コードと参照番号',
	Resubmit: '再送信',
	Resume: '再開する',
	Retry: '再試行',
	RetryingConnectionAttempt: '接続を再試行中...（{maxRetries}回中{retryCount}回目）',
	ReturnToForm: '調子を取り戻す',
	RevertClaimStatus: 'クレームステータスを戻す',
	RevertClaimStatusDescriptionBody:
		'この請求書には関連付けられた支払いが含まれており、ステータスを変更すると支払い追跡または処理に影響を与える可能性があり、手動での調整が必要になる場合があります。',
	RevertClaimStatusDescriptionTitle: '{status} に戻しますか？',
	RevertClaimStatusError: 'クレームステータスを元に戻せませんでした。',
	RevertToDraft: '下書きに戻す',
	Review: 'レビュー',
	ReviewsFirstQuote: 'いつでもどこでも予約可能',
	ReviewsSecondJobTitle: 'ライフハウスクリニック',
	ReviewsSecondName: 'クララW',
	ReviewsSecondQuote:
		'私は carepatron アプリも気に入っています。外出中でもクライアントや仕事を追跡するのに役立ちます。',
	ReviewsThirdJobTitle: 'マニラベイクリニック',
	ReviewsThirdName: 'ジャッキーH.',
	ReviewsThirdQuote: 'ナビゲーションのしやすさと美しいユーザー インターフェイスのおかげで、毎日笑顔になれます。',
	RightAlign: '右揃え',
	Role: '役割',
	Roster: '出席者',
	RunInBackground: 'バックグラウンドで実行',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Carepatronを退会します',
	SafetyRedirectSubtext: 'このリンクを信頼する場合は、選択して続行してください',
	SalesRepresentative: '営業担当者',
	SalesTax: '消費税',
	SalesTaxHelp: '生成された請求書には消費税が含まれます',
	SalesTaxIncluded: 'はい',
	SalesTaxNotIncluded: 'いいえ',
	SaoPaulo: 'サンパウロ',
	Saturday: '土曜日',
	Save: '保存',
	SaveAndClose: '保存 ',
	SaveAndExit: '保存 ',
	SaveAndLock: '保存してロック',
	SaveAsDraft: '下書きとして保存',
	SaveCardForFuturePayments: '今後の支払いのためにカードを保存する',
	SaveChanges: '変更内容を保存',
	SaveCollection: 'コレクションを保存',
	SaveField: 'フィールドを保存',
	SavePaymentMethod: '支払い方法を保存する',
	SavePaymentMethodDescription: '最初の予約までは料金は発生しません。',
	SavePaymentMethodSetupError: '予期しないエラーが発生したため、現時点では支払いを設定できませんでした。',
	SavePaymentMethodSetupInvoiceLater: '最初の請求書を支払うときに支払いを設定して保存できます。',
	SaveSection: 'セクションを保存',
	SaveService: '新しいサービスを作成する',
	SaveTemplate: 'テンプレートを保存',
	Saved: '保存しました',
	SavedCards: '保存したカード',
	SavedPaymentMethods: '保存しました',
	Saving: '保存中...',
	ScheduleAppointmentsAndOnlineServices: '予約とオンラインサービスのスケジュール',
	ScheduleName: 'スケジュール名',
	ScheduleNew: '新しいスケジュール',
	ScheduleSend: '予定を送信',
	ScheduleSendAlertInfo: '予定されたメッセージは、予定された時間に送信されます。',
	ScheduleSendByName: '<strong>スケジュール送信</strong> • {time} by {displayName}',
	ScheduleSetupCall: 'スケジュール設定の電話',
	Scheduled: '予定',
	SchedulingSend: '送信を予定',
	School: '学校',
	ScrollToTop: 'トップにスクロールします',
	Search: '検索',
	SearchAndConvertToLanguage: '言語を検索して変換する',
	SearchBasicBlocks: '基本ブロックを検索',
	SearchByName: '名前で検索',
	SearchClaims: 'クレーム検索',
	SearchClientFields: '検索クライアントフィールド',
	SearchClients: 'クライアント名、クライアントID、電話番号で検索',
	SearchCommandNotFound: '"{searchTerm}" に一致する結果が見つかりませんでした。',
	SearchContacts: 'クライアントまたは連絡先',
	SearchContactsPlaceholder: '連絡先を検索',
	SearchConversations: '会話を検索',
	SearchInputPlaceholder: 'すべてのリソースを検索',
	SearchInvoiceNumber: '請求書番号を検索',
	SearchInvoices: '請求書を検索',
	SearchMultipleContacts: '顧客または連絡先',
	SearchMultipleContactsOptional: '顧客または連絡先（オプション）',
	SearchOrCreateATag: 'タグを検索または作成する',
	SearchPayments: '支払いを検索',
	SearchPrepopulatedData: '事前入力されたデータフィールドを検索する',
	SearchRelationships: '関係を検索',
	SearchRemindersAndWorkflows: '検索リマインダーとワークフロー',
	SearchServices: '検索サービス',
	SearchTags: 'タグを検索',
	SearchTeamMembers: '検索チームメンバー',
	SearchTemplatePlaceholder: '検索 {templateCount}+ リソース',
	SearchTimezone: 'タイムゾーンを検索...',
	SearchTrashItems: 'アイテムを検索',
	SearchUnsplashPlaceholder: 'Unsplashから無料の高解像度写真を検索',
	Secondary: '二次',
	SecondaryInsurance: '二次保険',
	SecondaryPolicy: '二次保険',
	SecondaryTimezone: 'セカンダリタイムゾーン',
	Secondly: '第二に',
	Section: 'セクション',
	SectionCannotBeEmpty: 'セクションには少なくとも 1 行が必要です',
	SectionFormSecondaryText: 'セクションのタイトルと説明',
	SectionName: 'セクション名',
	Sections: 'セクション',
	SeeLess: 'もっと見る',
	SeeLessUpcomingAppointments: '今後の予定を表示しない',
	SeeMore: '続きを見る',
	SeeMoreUpcomingAppointments: '今後の予定をもっと見る',
	SeeTemplateLibrary: 'テンプレートライブラリを見る',
	Seen: '見た',
	SeenByName: '<strong>確認済み</strong> • {time} by {displayName}',
	SelectAll: 'すべて選択',
	SelectAssignees: '割り当て先を選択',
	SelectAttendees: '参加者を選択',
	SelectCollection: 'コレクションを選択',
	SelectCorrespondingAttributes: '対応する属性を選択',
	SelectPayers: '支払者を選択する',
	SelectProfile: 'プロフィールを選択',
	SelectServices: 'サービスを選択',
	SelectTags: 'タグを選択',
	SelectTeamOrCommunity: 'チームまたはコミュニティを選択',
	SelectTemplate: 'テンプレートを選択',
	SelectType: 'タイプを選択',
	Selected: '選択済み',
	SelfPay: '自己負担',
	Send: '送信',
	SendAndClose: '送信 ',
	SendAndStopIgnore: '送信して無視をやめる',
	SendEmail: 'メールを送る',
	SendIntake: '摂取量を送信',
	SendIntakeAndForms: '受信を送信 ',
	SendMeACopy: 'コピーを送ってください',
	SendNotificationEmailWarning:
		'一部の参加者にはメールアドレスが登録されておらず、自動通知とリマインダーを受信できません。',
	SendNotificationLabel: 'メールで通知する参加者を選択してください。',
	SendOnlinePayment: 'オンライン支払いを送信',
	SendOnlinePaymentTooltipTitleAdmin: 'ご希望の支払い設定を追加してください',
	SendOnlinePaymentTooltipTitleStaff: 'プロバイダーの所有者にオンライン支払いの設定を依頼してください。',
	SendPaymentLink: '支払いリンクを送信',
	SendReaction: '反応を送る',
	SendScheduledForDate: '{date}に予定された送信',
	SendVerificationEmail: '確認メールを送信',
	SendingFailed: '送信失敗',
	Sent: '送信済み',
	SentByName: '**送信済み** • {time} by {displayName}',
	Seoul: 'ソウル',
	SeparateDuplicateClientsDescription:
		'選択したクライアントレコードは、マージしない限り、他のレコードとは別々に保持されます。',
	Service: 'サービス',
	'Service/s': 'サービス',
	ServiceAdjustment: 'サービス調整',
	ServiceAllowNewClientsIndicator: '新規クライアントを許可する',
	ServiceAlreadyExistsInCollection: 'サービスはコレクション内に既に存在します',
	ServiceBookableOnlineIndicator: 'オンラインで予約可能',
	ServiceCode: 'コード',
	ServiceCodeErrorMessage: 'サービスコードが必要です',
	ServiceCodeSelectorPlaceholder: 'サービスコードを追加する',
	ServiceColour: 'サービスカラー',
	ServiceCoverageDescription: 'この保険契約の対象となるサービスと自己負担額を選択してください。',
	ServiceCoverageGoToServices: 'サービスへ移動',
	ServiceCoverageNoServicesDescription:
		'サービスの共同支払額をカスタマイズして、デフォルトのポリシー共同支払額を上書きします。ポリシーに対してサービスが請求されないように、補償を無効にします。',
	ServiceCoverageNoServicesLabel: 'サービスが見つかりませんでした。',
	ServiceCoverageTitle: 'サービス範囲',
	ServiceDate: 'サービス日',
	ServiceDetails: 'サービス詳細',
	ServiceDuration: '間隔',
	ServiceEmptyState: 'まだサービスはありません',
	ServiceErrorMessage: 'サービスが必要です',
	ServiceFacility: 'サービス施設',
	ServiceName: 'サービス名',
	ServiceRate: 'レート',
	ServiceReceiptRequiresReviewNotificationSubject:
		'スーパービル{serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} for {serviceReceiptNumber, select, undefined {user} other {{clientName}}} requires additional information',
	ServiceSalesTax: '消費税',
	ServiceType: 'サービス',
	ServiceWorkerForceUIUpdateDialogDescription:
		'「リロード」をクリックして更新し、Carepatron の最新情報を入手してください。',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'リロード',
	ServiceWorkerForceUIUpdateDialogSubTitle: '古いバージョンを使用しています',
	ServiceWorkerForceUIUpdateDialogTitle: 'おかえり！',
	Services: 'サービス',
	ServicesAndAvailability: 'サービス ',
	ServicesAndDiagnosisCodesHeader: 'サービスと診断コードを追加する',
	ServicesCount: '{count,plural,=0{サービス}one{サービス}other{サービス}}',
	ServicesPlaceholder: 'サービス',
	ServicesProvidedBy: '提供サービス',
	SetAPhysicalAddress: '物理アドレスを設定する',
	SetAVirtualLocation: '仮想ロケーションを設定する',
	SetAsDefault: 'デフォルトとして設定',
	SetAsIntake: '摂取量として設定',
	SetAsIntakeDefault: '摂取量のデフォルトとして設定',
	SetAvailability: '空き状況を設定する',
	SetTemplateAsDefaultIntakeSuccess: '{templateTitle} をデフォルトの受付テンプレートとして設定しました。',
	SetUpMfaButton: 'MFAを設定する',
	SetYourLocation: '<mark>場所を設定</mark>',
	SetYourLocationDescription: 'ビジネスアドレスはありません <span>(オンラインとモバイルサービスのみ</span>',
	SettingUpPayers: '支払者の設定',
	Settings: '設定',
	SettingsNewUserPasswordDescription: '登録が完了すると、アカウントの確認に使用できる確認コードが送信されます。',
	SettingsNewUserPasswordTitle: 'Carepatronに登録する',
	SettingsTabAutomation: 'オートメーション',
	SettingsTabBillingDetails: '支払明細',
	SettingsTabConnectedApps: '接続されたアプリ',
	SettingsTabCustomFields: 'カスタムフィールド',
	SettingsTabDetails: '詳細',
	SettingsTabInvoices: '請求書',
	SettingsTabLocations: '場所',
	SettingsTabNotifications: '通知',
	SettingsTabOnlineBooking: 'オンライン予約',
	SettingsTabPayers: '支払者',
	SettingsTabReminders: 'リマインダー',
	SettingsTabServices: 'サービス',
	SettingsTabServicesAndAvailability: 'サービスと利用可能状況',
	SettingsTabSubscriptions: 'サブスクリプション',
	SettingsTabWorkflowAutomations: '自動化',
	SettingsTabWorkflowReminders: '基本のリマインダー',
	SettingsTabWorkflowTemplates: 'テンプレート',
	Setup: '設定',
	SetupGuide: '設定ガイド',
	SetupGuideAddServicesActionLabel: '開始',
	SetupGuideAddServicesSubtitle: '4 ステップ • 2 分',
	SetupGuideAddServicesTitle: 'サービスを追加する',
	SetupGuideEnableOnlinePaymentsActionLabel: '開始',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 ステップ • 3 分',
	SetupGuideEnableOnlinePaymentsTitle: 'オンライン決済を有効にする',
	SetupGuideImportClientsActionLabel: '開始',
	SetupGuideImportClientsSubtitle: '4 ステップ • 3 分',
	SetupGuideImportClientsTitle: 'クライアントをインポートする',
	SetupGuideImportTemplatesActionLabel: '開始',
	SetupGuideImportTemplatesSubtitle: '2 ステップ • 1 分',
	SetupGuideImportTemplatesTitle: 'テンプレートをインポートします',
	SetupGuidePersonalizeWorkspaceActionLabel: '開始',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 ステップ • 2 分',
	SetupGuidePersonalizeWorkspaceTitle: 'ワークスペースをパーソナライズ',
	SetupGuideSetLocationActionLabel: '開始',
	SetupGuideSetLocationSubtitle: '4 ステップ • 1 分',
	SetupGuideSetLocationTitle: '場所を設定する',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'チーム招待',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'チームを招待して、簡単にコミュニケーションを取り、タスクを管理しましょう。',
	SetupGuideSuggestedAddTeamMembersTag: '設定',
	SetupGuideSuggestedAddTeamMembersTitle: 'チームメンバーを追加',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'カスタマイズ',
	SetupGuideSuggestedCustomizeBrandSubtitle: '独自のロゴとブランドカラーでプロフェッショナルな印象に。',
	SetupGuideSuggestedCustomizeBrandTitle: 'ブランドをカスタマイズする',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'ダウンロード',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'どこでも、いつでも、どんなデバイスでも、ワークスペースにアクセスできます。',
	SetupGuideSuggestedDownloadMobileAppTag: '設定',
	SetupGuideSuggestedDownloadMobileAppTitle: 'アプリをダウンロードする',
	SetupGuideSuggestedEditAvailabilityActionLabel: '<h1>利用可能時間の設定</h1>',
	SetupGuideSuggestedEditAvailabilitySubtitle: '予約の重複を防ぐために、利用可能な時間帯を設定してください。',
	SetupGuideSuggestedEditAvailabilityTag: 'スケジュール',
	SetupGuideSuggestedEditAvailabilityTitle: '編集可能な時間枠',
	SetupGuideSuggestedImportClientsActionLabel: 'インポート',
	SetupGuideSuggestedImportClientsSubtitle: 'たった1クリックで既存の顧客データをすぐにアップロードできます。',
	SetupGuideSuggestedImportClientsTag: '設定',
	SetupGuideSuggestedImportClientsTitle: '顧客をインポートする',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: '編集リマインダー',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: '自動予約リマインダーで無断キャンセルを減らしましょう。',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'リマインダーをパーソナライズ',
	SetupGuideSuggestedStartVideoCallActionLabel: '電話開始',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'AI 搭載のビデオツールを使用して、電話会議を開催し、クライアントとつながりましょう。',
	SetupGuideSuggestedStartVideoCallTag: '遠隔医療',
	SetupGuideSuggestedStartVideoCallTitle: 'ビデオ通話開始',
	SetupGuideSuggestedTryActionsTitle: '試してみるもの 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'AI アシスタントを試す',
	SetupGuideSuggestedUseAIAssistantSubtitle: '仕事に関する質問にすぐに答えを得ましょう。',
	SetupGuideSuggestedUseAIAssistantTag: '新規',
	SetupGuideSuggestedUseAIAssistantTitle: 'AIアシスタントを使用する',
	SetupGuideSyncCalendarActionLabel: '開始',
	SetupGuideSyncCalendarSubtitle: '1 ステップ • 1 分未満',
	SetupGuideSyncCalendarTitle: 'カレンダーを同期する',
	SetupGuideVerifyEmailLabel: '確認',
	SetupGuideVerifyEmailSubtitle: '2 ステップ • 2 分',
	SetupOnlineStripePayments: 'オンライン決済にはStripeを使用する',
	SetupPayments: '支払い設定',
	Sex: '性別',
	SexSelectorPlaceholder: '男性 / 女性 / 答えたくない',
	Share: '共有',
	ShareBookingLink: '予約リンクを共有する',
	ShareNoteDefaultMessage: `こんにちは{name} さんが "{documentName}" を共有しました。

ありがとうございました。
{practiceName}`,
	ShareNoteMessage: `こんにちは
{name} さんが "{documentName}" を {isResponder, select, true {ご質問と共に共有しました。} other {共有しました。}}

よろしくお願いいたします。
{practiceName}`,
	ShareNoteTitle: '‘{noteTitle}’ を共有する',
	ShareNotesWithClients: '顧客や連絡先と共有する',
	ShareScreen: '画面を共有',
	ShareScreenNotSupported: 'お使いのデバイス/ブラウザは画面共有機能をサポートしていません',
	ShareScreenWithId: '画面 {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'このテンプレートを他のユーザーが閲覧して、フォームとして提出できるようにします。',
	ShareTemplateAsPublicFormModalTitle: '‘{title}’ の共有リンク',
	ShareTemplateAsPublicFormSaved: '公開フォームの設定が正常に更新されました。',
	ShareTemplateAsPublicFormSectionCustomization: 'カスタマイズ',
	ShareTemplateAsPublicFormShowPoweredBy: '私のフォームに「Powered by Carepatron」を表示する',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'フォームの「Powered by Carepatron」の表示/非表示を切り替え',
	ShareTemplateAsPublicFormTrigger: '共有',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'ワークスペースのブランディングを使用する',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'ワークスペースのブランドを表示/非表示にする',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: '既存のお客様と新規のお客様にコードを送信します。',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: '署名は常にメールの確認を必要とします。',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: '既存の顧客のみコードを送信します。',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'コードを送信しません。',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'「Never」を選択すると、既存のクライアントのメールアドレスを使用した場合、未確認のユーザーがクライアントデータを上書きできてしまう可能性があります。',
	ShareWithCommunity: 'コミュニティと共有',
	ShareYourReferralLink: '紹介リンクを共有する',
	ShareYourScreen: '画面を共有する',
	SheHer: '彼女',
	ShortTextAnswer: '短いテキスト回答',
	ShortTextFormPrimaryText: '短いテキスト',
	ShortTextFormSecondaryText: '300文字未満の回答',
	Show: '見せる',
	ShowColumn: '列を表示',
	ShowColumnButton: '{value} 列のボタンを表示する',
	ShowColumns: '列を表示',
	ShowColumnsMenu: '列メニューを表示',
	ShowDateDurationDescription: '例：29歳',
	ShowDateDurationLabel: '日付の期間を表示',
	ShowDetails: '詳細を表示',
	ShowField: 'フィールドを表示',
	ShowFullAddress: '住所を表示',
	ShowHideFields: 'フィールドの表示/非表示',
	ShowIcons: 'アイコンを表示',
	ShowLess: '表示を減らす',
	ShowMeetingTimers: '会議タイマーを表示',
	ShowMenu: 'メニューを表示',
	ShowMergeSummarySidebar: 'マージの概要を表示する',
	ShowMore: 'もっと見せる',
	ShowOnTranscript: 'トランスクリプトで表示',
	ShowReactions: '反応を表示',
	ShowSection: 'セクションを表示',
	ShowServiceCode: 'サービスコードを表示',
	ShowServiceDescription: 'サービス予約の説明を表示',
	ShowServiceDescriptionDesc: 'クライアントは予約時にサービスの説明を見ることができます',
	ShowServiceGroups: 'コレクションを表示',
	ShowServiceGroupsDesc: '予約時にコレクション別にグループ化されたサービスがクライアントに表示されます',
	ShowSpeakers: 'スピーカーを表示',
	ShowTax: '税金を表示',
	ShowTimestamp: 'タイムスタンプを表示',
	ShowUnits: '単位を表示',
	ShowWeekends: '週末を表示',
	ShowYourView: 'あなたの意見を見せる',
	SignInWithApple: 'Appleでサインイン',
	SignInWithGoogle: 'Googleでログイン',
	SignInWithMicrosoft: 'Microsoftでサインイン',
	SignUpTitleReferralDefault: '<mark>サインアップ</mark>紹介報酬を受け取る',
	SignUpTitleReferralUpgrade:
		'{durationInMonths} か月間の <mark>{percentOff, select, 100 {無料} other {{percentOff}% 割引}} アップグレード</mark> を開始する',
	SignatureCaptureError: '署名をキャプチャできません。もう一度お試しください。',
	SignatureFormPrimaryText: 'サイン',
	SignatureFormSecondaryText: 'デジタル署名を取得する',
	SignatureInfoTooltip: 'この視覚的表現は有効な電子署名ではありません。',
	SignaturePlaceholder: 'ここに署名を記入してください',
	SignedBy: 'によって署名されました',
	Signup: 'サインアップ',
	SignupAgreements: '私は自分のアカウントの {termsOfUse} および {privacyStatement} に同意します。',
	SignupBAA: 'ビジネスアソシエイト契約',
	SignupBusinessAgreements:
		'私と会社を代表して、アカウントの {businessAssociateAgreement}、{termsOfUse}、および {privacyStatement} に同意します。',
	SignupInvitationForYou: 'Carepatron を使用するよう招待されました。',
	SignupPageProviderWarning:
		'管理者がすでにアカウントを作成している場合は、そのプロバイダーに招待してもらう必要があります。このサインアップフォームは使用しないでください。詳細については、',
	SignupPageProviderWarningLink: 'このリンク。',
	SignupPrivacy: 'プライバシーポリシー',
	SignupProfession: 'あなたに最も当てはまる職業は何ですか?',
	SignupSubtitle:
		'Carepatron の診療管理ソフトウェアは、個人開業医やチーム向けに作られています。過剰な料金を支払うのをやめて、革命に参加しましょう。',
	SignupSuccessDescription:
		'オンボーディングを開始するには、メールアドレスを確認してください。すぐにメールが届かない場合は、スパム フォルダーを確認してください。',
	SignupSuccessTitle: 'メールを確認してください',
	SignupTermsOfUse: '利用規約',
	SignupTitleClient: '<mark>健康を管理する</mark>一つの場所から',
	SignupTitleLast: 'そしてあなたが行うすべての仕事！—無料です',
	SignupTitleOne: '<mark>あなたに力を与える</mark>、 ',
	SignupTitleThree: '<mark>クライアントに力を与える</mark>、 ',
	SignupTitleTwo: '<mark>チームを強化する</mark>、 ',
	Simple: '単純',
	SimplifyBillToDetails: '請求書の詳細を簡素化',
	SimplifyBillToHelperText: 'クライアントに一致する場合は最初の行のみが使用されます',
	Singapore: 'シンガポール',
	Single: 'シングル',
	SingleChoiceFormPrimaryText: '単一選択',
	SingleChoiceFormSecondaryText: '1つのオプションのみを選択してください',
	Sister: '妹',
	SisterInLaw: '義理の姉妹',
	Skip: 'スキップ',
	SkipLogin: 'ログインをスキップ',
	SlightBlur: '背景を少しぼかす',
	Small: '小さい',
	SmartChips: 'スマートチップ',
	SmartDataChips: 'スマートデータチップ',
	SmartReply: 'スマートリプライ',
	SmartSuggestNewClient: '<strong>スマートサジェスト</strong> {name} を新規クライアントとして作成する',
	SmartSuggestedFieldDescription: 'このフィールドはスマートサジェスチョンです。',
	SocialSecurityNumber: '社会保障番号',
	SocialWork: '社会事業',
	SocialWorker: 'ソーシャルワーカー',
	SoftwareDeveloper: 'ソフトウェア開発者',
	Solo: 'ソロ',
	Someone: '誰か',
	Son: '息子',
	SortBy: '並べ替え',
	SouthAmerica: '南アメリカ',
	Speaker: 'スピーカー',
	SpeakerSource: 'スピーカーソース',
	Speakers: 'スピーカー',
	SpecifyPaymentMethod: '支払い方法を指定する',
	SpeechLanguagePathology: '言語聴覚療法',
	SpeechTherapist: '言語聴覚士',
	SpeechTherapists: '言語聴覚士',
	SpeechTherapy: '言語療法',
	SportsMedicinePhysician: 'スポーツ医学医師',
	Spouse: '配偶者',
	SpreadsheetColumnExample: '例えば ',
	SpreadsheetColumns: 'スプレッドシートの列',
	SpreadsheetUploaded: 'スプレッドシートをアップロードしました',
	SpreadsheetUploading: 'アップロード中...',
	Staff: 'スタッフ',
	StaffAccessDescriptionAdmin: '管理者はプラットフォーム上のすべてを管理できます。',
	StaffAccessDescriptionStaff: `スタッフメンバーは、自分が作成した、または共有したクライアント、メモ、ドキュメントを管理できます。
彼らと協力して、予定を立てたり、請求書を管理したりします。`,
	StaffContactAssignedSubject:
		'{actorProfileName}は{totalCount, plural, =1 {{contactName1}} =2 {{contactName1}と{contactName2}} other {{contactName1}、{contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 {と他1名} other {と他#名}}を担当者として割り当てました',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} は {inboxName} インボックスをあなたと共有しました。',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} は {inboxName} インボックスへのアクセス権を削除しました。',
	StaffMembers: 'スタッフ',
	StaffMembersNumber: '{billedUsers, plural, one {# チームメンバー} other {# チームメンバー}}',
	StaffSavedSuccessSnackbar: 'チームメンバー情報が正常に保存されました。',
	StaffSelectorAdminRole: '管理者',
	StaffSelectorStaffRole: 'スタッフ',
	StandardAppointment: '標準予約',
	StandardColor: 'タスクの色',
	StartAndEndTime: '開始時間と終了時間',
	StartCall: '通話を開始',
	StartDate: '開始日',
	StartDictating: 'ディクテーションを開始する',
	StartImport: 'インポート開始',
	StartRecordErrorTitle: '録画の開始中にエラーが発生しました',
	StartRecording: '録音を開始',
	StartTimeIncrements: '開始時間の増加',
	StartTimeIncrementsView: '{startTimeIncrements} 分間隔',
	StartTranscribing: '転記を開始',
	StartTranscribingNotes:
		'録音を開始して、自動転記を開始します。転記が完了すると、ここでトランスクリプトが表示されます。',
	StartTranscription: '開始転写',
	StartVideoCall: 'ビデオ通話を開始',
	StartWeekOn: '週の始まり',
	StartedBy: '開始者 ',
	Starter: 'スターター',
	State: '州',
	StateIndustrialAccidentProviderNumber: '州労働災害プロバイダ番号',
	StateLicenseNumber: '州ライセンス番号',
	Statement: '声明',
	StatementDescriptor: 'ステートメント記述子',
	StatementDescriptorToolTip:
		'明細書記述子は、顧客の銀行またはクレジットカードの明細書に表示されます。5 文字から 22 文字までで、会社名を反映する必要があります。',
	StatementNumber: '声明 {ハッシュタグ}',
	Status: '状態',
	StatusFieldPlaceholder: 'ステータスラベルを入力してください',
	StepFather: '継父',
	StepMother: '継母',
	Stockholm: 'ストックホルム',
	StopIgnoreSendersDescription:
		'この送信者を無視するのをやめて、受信トレイに再び表示します。この操作は取り消せません。',
	StopIgnoring: '無視をやめる',
	StopIgnoringSenders: '送信者の無視をやめる',
	StopIgnoringSendersSuccess: '電子メール アドレス <mark>{addresses}</mark> を無視しなくなりました',
	StopSharing: '共有を停止',
	StopSharingLabel: 'carepatron.com があなたの画面を共有しています。',
	Storage: 'ストレージ',
	StorageAlmostFullDescription: '🚀 アカウントをスムーズに運用し続けるために、今すぐアップグレードしてください。',
	StorageAlmostFullTitle: 'あなたはワークスペースストレージ制限の{percentage}%を使用しました！',
	StorageFullDescription: 'プランをアップグレードすると、ストレージ容量が増えます。',
	StorageFullTitle: '	ストレージがいっぱいです。',
	Street: '通り',
	StripeAccountNotCompleteErrorCode:
		'オンライン決済は、{hasProviderName, select, true { {providerName} で設定されています} other {このプロバイダーでは有効になっていません}}.',
	StripeAccountRejectedError: 'Stripe アカウントが拒否されsました。サポートにお問い合わせください。',
	StripeBalance: 'ストライプバランス',
	StripeChargesInfoToolTip: 'デビットカードでチャージできます ',
	StripeFeesDescription:
		'CarepatronはStripeを使用して支払いを迅速に行い、支払い情報を安全に保ちます。利用可能な支払い方法は地域によって異なりますが、すべての主要デビットカードで利用可能です。 ',
	StripeFeesDescriptionItem1: '処理手数料は成功した取引ごとに適用されます。{link}できます。',
	StripeFeesDescriptionItem2: '支払いは毎日行われますが、最大 4 日間保留されます。',
	StripeFeesLinkToRatesText: '料金表はこちらをご覧ください',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'申し訳ありませんが、オンライン決済を利用する請求書には、最低 {minimumAmount} の金額が必要です。',
	StripePaymentsDisabled: 'オンライン決済が無効になっています。お支払い設定をご確認ください。',
	StripePaymentsUnavailable: '支払いは利用できません',
	StripePaymentsUnavailableDescription:
		'支払いの読み込み中にエラーが発生しました。しばらくしてからもう一度お試しください。',
	StripePayoutsInfoToolTip: '銀行口座に支払いを受け取れる',
	StyleYourWorkspace: '<mark>スタイル</mark> あなたのワークスペース',
	StyleYourWorkspaceDescription1:
		'ウェブサイトからブランドアセットを取得しました。編集したり、Carepatron ワークスペースに進んだりしてください。',
	StyleYourWorkspaceDescription2:
		'ブランドアセットを使用して、シームレスな顧客体験を実現する請求書とオンライン予約をカスタマイズします。',
	SubAdvanced: '高度な',
	SubEssential: '必須',
	SubOrganization: '組織',
	SubPlus: 'プラス',
	SubProfessional: 'プロ',
	Subject: '主題',
	Submit: '提出する',
	SubmitElectronically: '電子的に提出してください',
	SubmitFeedback: 'フィードバックを送信する',
	SubmitFormValidationError: 'すべての必須フィールドが正しく入力されていることを確認して、もう一度送信してください。',
	Submitted: '提出済み',
	SubmittedDate: '提出日',
	SubscribePerMonth: '購読する {price} {isMonthly, select, true {月額} other {年間}}',
	SubscriptionDiscountDescription:
		'{percentOff}% 割引 {months, select, null { } other { {months, plural, one { # か月間} other { # か月間}}}}',
	SubscriptionFreeTrialDescription: '{endDate} まで無料',
	SubscriptionPaymentFailedNotificationSubject:
		'サブスクリプションの支払いを完了できませんでした。お支払いの詳細を確認してください。',
	SubscriptionPlanDetailsHeader: 'ユーザーあたり/月額、年間請求',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} 月額請求 (USD)',
	SubscriptionPlans: 'サブスクリプションプラン',
	SubscriptionPlansDescription: 'プランをアップグレードして追加の特典をアンロックし、診療所を円滑に運営しましょう。',
	SubscriptionPlansDescriptionNoPermission:
		'アップグレードへのアクセス権がありません。管理者にお問い合わせください。',
	SubscriptionSettings: 'サブスクリプション設定',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> のストレージを使用しています',
	SubscriptionSettingsStorageUsed: '{used} の {limit} 使用済み',
	SubscriptionSettingsUnlimitedStorage: '無制限のストレージを利用可能',
	SubscriptionSummary: 'サブスクリプションの概要',
	SubscriptionUnavailableOverStorageLimit: '現在の使用量が、このプランのストレージ制限を超えています。',
	SubscriptionUnpaidBannerButton: '定期購読へ進む',
	SubscriptionUnpaidBannerDescription: 'お支払い情報が正しいことを確認して、もう一度お試しください',
	SubscriptionUnpaidBannerTitle: 'サブスクリプションの支払いを完了できませんでした。',
	Subscriptions: 'サブスクリプション',
	SubscriptionsAndPayments: 'サブスクリプション ',
	Subtotal: '小計',
	SuburbOrProvince: '郊外/地方',
	SuburbOrState: '郊外/州',
	SuccessSavedNoteChanges: 'メモの変更が正常に保存されました',
	SuccessShareDocument: 'ドキュメントの共有に成功しました',
	SuccessShareNote: 'メモを共有しました',
	SuccessfullyCreatedValue: '{value} を正常に作成しました。',
	SuccessfullyDeletedTranscriptionPart: '転写部分を削除しました',
	SuccessfullyDeletedValue: '{value} を正常に削除しました。',
	SuccessfullySubmitted: '送信が完了しました ',
	SuccessfullyUpdatedClientSettings: 'クライアント設定が正常に更新されました',
	SuccessfullyUpdatedTranscriptionPart: '転写部分が正常に更新されました',
	SuccessfullyUpdatedValue: '{value} を正常に更新しました。',
	SuggestedAIPoweredTemplates: '提案されたAI搭載テンプレート',
	SuggestedAITemplates: '推奨される AI テンプレート',
	SuggestedActions: '推奨されるアクション',
	SuggestedLocations: '推奨場所',
	Suggestions: '提案',
	Summarise: 'AI要約',
	SummarisingContent: '{title} の要約',
	Sunday: '日曜日',
	Superbill: 'スーパービル',
	SuperbillAndInsuranceBilling: 'スーパービル ',
	SuperbillAutomationMonthly: 'アクティブ • 月の最終日',
	SuperbillAutomationNoEmail: '自動請求書を正常に送信するには、このクライアントのメールアドレスを追加してください',
	SuperbillAutomationNotActive: '非活動中',
	SuperbillAutomationUpdateFailure: 'Superbill 自動化設定の更新に失敗しました',
	SuperbillAutomationUpdateSuccess: 'Superbill の自動化設定が正常に更新されました',
	SuperbillClientHelperText: 'この情報はクライアントの詳細から事前に入力されます',
	SuperbillNotFoundDescription:
		'プロバイダーに連絡して、詳細情報を問い合わせるか、スーパービルを再送信するよう依頼してください。',
	SuperbillNotFoundTitle: 'スーパービルが見つかりません',
	SuperbillNumber: 'スーパービル #{number}',
	SuperbillNumberAlreadyExists: 'スーパービル領収書番号はすでに存在します',
	SuperbillPracticeHelperText: 'この情報は診療所の請求設定から事前に入力されています',
	SuperbillProviderHelperText: 'この情報はスタッフの詳細から事前に入力されています',
	SuperbillReceipts: 'スーパービルの領収書',
	SuperbillsEmptyStateDescription: 'スーパービルは発見されませんでした。',
	Surgeon: '外科医',
	Surgeons: '外科医',
	SurgicalTechnologist: '外科技術者',
	SwitchFromAnotherPlatform: '私は別のプラットフォームから移行します。',
	SwitchToMyPortal: 'マイポータルに切り替える',
	SwitchToMyPortalTooltip: `あなた自身の個人ポータルにアクセスし、
あなたの
クライアントのポータルエクスペリエンス。`,
	SwitchWorkspace: 'ワークスペースを切り替える',
	SwitchingToADifferentPlatform: '別のプラットフォームへの切り替え',
	Sydney: 'シドニー',
	SyncCalendar: 'カレンダーを同期',
	SyncCalendarModalDescription:
		'他のチーム メンバーは同期されたカレンダーを表示できません。クライアントの予定は Carepatron 内からのみ更新または削除できます。',
	SyncCalendarModalDisplayCalendar: 'Carepatronでカレンダーを表示する',
	SyncCalendarModalSyncToCarepatron: 'カレンダーをCarepatronに同期する',
	SyncCalendarModalSyncWithCalendar: 'Carepatronの予定を自分のカレンダーと同期する',
	SyncCarepatronAppointmentsWithMyCalendar: 'Carepatron の予定を私のカレンダーと同期する',
	SyncGoogleCalendar: 'Googleカレンダーを同期する',
	SyncInbox: 'Carepatronと受信トレイを同期する',
	SyncMyCalendarToCarepatron: 'Carepatron に私のカレンダーを同期する',
	SyncOutlookCalendar: 'Outlookカレンダーを同期する',
	SyncedFromExternalCalendar: '外部カレンダーから同期済み',
	SyncingCalendarName: '{calendarName}カレンダーを同期中',
	SyncingFailed: '同期に失敗しました',
	SystemGenerated: 'システム生成',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'テーブル',
	TableRowLabel: '{value} のためのテーブル行',
	TagSelectorNoOptionsText: '新しいタグを追加するには「新規作成」をクリックします',
	Tags: 'タグ',
	TagsInputPlaceholder: 'タグを検索または作成する',
	Task: 'タスク',
	TaskAttendeeStatusUpdatedSuccess: '予約ステータスが正常に更新されました。',
	Tasks: 'タスク',
	Tax: '税',
	TaxAmount: '税額',
	TaxID: '税務番号',
	TaxIdType: '納税者番号の種類',
	TaxName: '税名',
	TaxNumber: '税番号',
	TaxNumberType: '税番号の種類',
	TaxNumberTypeInvalid: '{type} は無効です。',
	TaxPercentageOfAmount: '{taxName} ({percentage}% の {amount})',
	TaxRate: '税率',
	TaxRatesDescription: '請求書の明細項目に適用される税率を管理します。',
	Taxable: '課税対象',
	TaxonomyCode: '分類コード',
	TeacherAssistant: '教師アシスタント',
	Team: 'チーム',
	TeamMember: 'チームメンバー',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {は} other {は}} この時間帯にすでに予約されています。{br}二重予約を避けるために、新しい時間を選択してください。',
	TeamMembers: 'チームメンバー',
	TeamMembersColour: 'チームメンバーの色',
	TeamMembersDetails: 'チームメンバーの詳細',
	TeamSize: 'あなたのチームは何人いますか？',
	TeamTemplates: 'チームテンプレート',
	TeamTemplatesSectionDescription: 'あなたとチームによって作成されました',
	TelehealthAndVideoCalls: '遠隔医療 ',
	TelehealthProvidedOtherThanInPatientCare: '入院治療以外の目的で遠隔医療を提供',
	TelehealthVideoCall: '遠隔医療ビデオ通話',
	Template: 'テンプレート',
	TemplateDescription: 'テンプレートの説明',
	TemplateDetails: 'テンプレートの詳細',
	TemplateEditModeViewSwitcherDescription: 'テンプレートの作成と編集',
	TemplateGallery: 'コミュニティテンプレート',
	TemplateImportCompletedNotificationSubject:
		'テンプレートのインポートが完了しました！{templateTitle} を使用できます。',
	TemplateImportFailedNotificationSubject: 'ファイル {fileName} のインポートに失敗しました。',
	TemplateName: 'テンプレート名',
	TemplateNotFound: 'テンプレートが見つかりませんでした。',
	TemplatePreviewErrorMessage: 'テンプレートのプレビューの読み込み中にエラーが発生しました',
	TemplateResponderModeViewSwitcherDescription: 'フォームをプレビューして操作する',
	TemplateResponderModeViewSwitcherTooltipTitle: '回答者がフォームに入力したときにどのように表示されるかを確認します',
	TemplateSaved: '保存された変更',
	TemplateTitle: 'テンプレートタイトル',
	TemplateType: 'テンプレートタイプ',
	Templates: 'テンプレート',
	TemplatesCategoriesFilter: 'カテゴリーで絞り込む',
	TemplatesPublicTemplatesFilter: ' コミュニティ/チームでフィルタリング',
	Text: '文章',
	TextAlign: 'テキストの配置',
	TextColor: 'テキストの色',
	ThankYouForYourFeedback: 'フィードバックありがとうございます！',
	ThanksForLettingKnow: '私たちに知らせていただきありがとうございます。',
	ThePaymentMethod: 'お支払い方法',
	ThemThey: '彼ら/彼ら',
	Theme: 'テーマ',
	ThemeAllColorsPickerTitle: 'もっとテーマ',
	ThemeColor: 'テーマ',
	ThemeColorDarkMode: '暗い',
	ThemeColorLightMode: '光',
	ThemeColorModePickerTitle: 'カラーモード',
	ThemeColorSystemMode: 'システム',
	ThemeCpColorPickerTitle: `Carepatron themes		
Carepatronのテーマ`,
	ThemePanelDescription: 'ライトモードとダークモードから選択して、テーマ設定をカスタマイズします。',
	ThemePanelTitle: '外観',
	Then: 'それから',
	Therapist: 'セラピスト',
	Therapists: 'セラピスト',
	Therapy: '治療',
	Thick: '厚い',
	Thin: '薄い',
	ThirdPerson: '3人目',
	ThisAndFollowingAppointments: 'この予定と次の予定',
	ThisAndFollowingMeetings: 'この会議と次の会議',
	ThisAndFollowingReminders: 'これと次のリマインダー',
	ThisAndFollowingTasks: 'このタスクと次のタスク',
	ThisAppointment: 'この任命',
	ThisMeeting: 'この会議',
	ThisMonth: '今月',
	ThisPerson: 'この人',
	ThisReminder: 'このリマインダー',
	ThisTask: 'この仕事',
	ThisWeek: '今週',
	ThreeDay: '3日間',
	Thursday: '木曜日',
	Time: '時間',
	TimeAgoDays: '{number}日',
	TimeAgoHours: '{number}時間',
	TimeAgoMinutes: '{number}{number, plural, one {分} other {分}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: '時間形式',
	TimeIncrement: '時間増分',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {午前} other {午後}}',
	TimeslotSize: 'タイムスロットのサイズ',
	Timestamp: 'タイムスタンプ',
	Timezone: 'タイムゾーン',
	TimezoneDisplay: 'タイムゾーン表示',
	TimezoneDisplayDescription: 'タイムゾーンの表示設定を管理します。',
	Title: 'タイトル',
	To: 'に',
	ToYourWorkspace: 'あなたのワークスペースに',
	Today: '今日',
	TodayInHoursPlural: '今日、{count} {count, plural, one {時間} other {時間}}で',
	TodayInMinsAbbreviated: '今日 {count} {count, plural, one {分} other {分}}',
	ToggleHeaderCell: 'ヘッダーセルを切り替える',
	ToggleHeaderCol: 'ヘッダー列を切り替える',
	ToggleHeaderRow: 'ヘッダー行を切り替える',
	Tokyo: '東京',
	Tomorrow: '明日',
	TomorrowAfternoon: '明日の午後',
	TomorrowMorning: '明日の朝',
	TooExpensive: '高すぎる',
	TooHardToSetUp: 'セットアップが難しすぎる',
	TooManyFiles: '1 つ以上のファイルが検出されました。',
	ToolsExample: 'シンプルな練習、Microsoft、Calendly、Asana、Doxy.me ...',
	Total: '合計',
	TotalAccountCredit: '合計アカウントクレジット',
	TotalAdjustments: '合計調整',
	TotalAmountToCreditInCurrency: 'クレジットする総額 ({currency})',
	TotalBilled: '請求合計',
	TotalConversations: '{total} {total, plural, =0 {会話} one {会話} other {会話}}',
	TotalOverdue: '延滞総額',
	TotalOverdueTooltip:
		'合計延滞残高には、日付範囲に関係なく、無効化も処理もされていないすべての未払い請求書が含まれます。',
	TotalPaid: '支払総額',
	TotalPaidTooltip: '合計支払残高には、指定された日付範囲内で支払われた請求書のすべての金額が含まれます。',
	TotalUnpaid: '未払い総額',
	TotalUnpaidTooltip:
		'未払い残高合計には、指定された日付範囲内で支払期限が到来する処理中、未払い、および送信済みの請求書のすべての未払い金額が含まれます。',
	TotalWorkflows: '{count} {count, plural, one {ワークフロー} other {ワークフロー}}',
	TotpSetUpManualEntryInstruction: 'または、以下のコードをアプリに手動で入力することもできます。',
	TotpSetUpModalDescription: '多要素認証を設定するには、認証アプリで QR コードをスキャンします。',
	TotpSetUpModalTitle: 'MFAデバイスを設定する',
	TotpSetUpSuccess: '準備完了です。MFA が有効になりました。',
	TotpSetupEnterAuthenticatorCodeInstruction: '認証アプリで生成されたコードを入力してください',
	Transcribe: '転記',
	TranscribeLanguageSelector: '入力言語を選択',
	TranscribeLiveAudio: 'ライブ音声を書き起こす',
	Transcribing: '音声を書き起こしています...',
	TranscribingIn: '転写中',
	Transcript: 'トランスクリプト',
	TranscriptRecordingCompleteInfo: '録音が完了すると、ここでトランスクリプトが表示されます。',
	TranscriptSuccessSnackbar: 'トランスクリプトが正常に処理されました。',
	Transcription: '転写',
	TranscriptionEmpty: '転写はありません',
	TranscriptionEmptyHelperMessage:
		'このトランスクリプションでは何も拾えませんでした。再起動してもう一度お試しください。',
	TranscriptionFailedNotice: 'この転写は正常に処理されませんでした',
	TranscriptionIdleMessage:
		'音声が入ってきません。もっと時間が必要な場合は、{timeValue}秒以内に返信してください。それ以外の場合は、セッションが終了します。',
	TranscriptionInProcess: '転写処理中…',
	TranscriptionIncompleteNotice: 'この転写の一部は正常に処理されませんでした',
	TranscriptionOvertimeWarning: '{scribeType} セッションは **{timeValue} {unit}** で終了します。',
	TranscriptionPartDeleteMessage: 'この転写部分を削除してもよろしいですか?',
	TranscriptionText: '音声をテキストに変換',
	TranscriptsPending: 'セッション終了後、トランスクリプトはここで閲覧可能になります。',
	Transfer: '移行',
	TransferAndDelete: '転送と削除',
	TransferOwnership: '所有権の譲渡',
	TransferOwnershipConfirmationModalDescription:
		'このアクションは、所有権があなたに返された場合にのみ元に戻すことができます。',
	TransferOwnershipDescription: 'このワークスペースの所有権を別のチーム メンバーに譲渡します。',
	TransferOwnershipSuccessSnackbar: '所有権の譲渡に成功しました!',
	TransferOwnershipToMember: '{staff} にこのワークスペースを転送してもよろしいですか？',
	TransferStatusAlert:
		'{numberOfStatuses, plural, one {このステータス} other {これらのステータス}} を削除すると、{numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} クライアントステータス。</strong>} other {<strong>{numberOfAffectedRecords} クライアントステータス。</strong>}} に影響を与えます。',
	TransferStatusDescription:
		'削除を続行する前に、これらのクライアントの別のステータスを選択してください。この操作は元に戻せません。',
	TransferStatusLabel: '新しいステータスへの移行',
	TransferStatusPlaceholder: '既存のステータスを選択',
	TransferStatusTitle: '削除前の転送ステータス',
	TransferTaskAttendeeStatusAlert:
		'このステータスを削除すると、<strong>{number} 件の今後の予約 {number, plural, one {状態} other {ステータス}}</strong> に影響が出ます。',
	TransferTaskAttendeeStatusDescription:
		'削除を続行する前に、これらのクライアントの別のステータスを選択してください。この操作は元に戻せません。',
	TransferTaskAttendeeStatusSubtitle: '予約状況',
	TransferTaskAttendeeStatusTitle: '削除前の転送状態',
	Trash: 'ゴミ箱',
	TrashDeleteItemsModalConfirm: '確認するには、{confirmationText}と入力してください',
	TrashDeleteItemsModalDescription:
		'以下の{count、複数形、one {アイテム} other {アイテム}}は永久に削除され、復元できません。',
	TrashDeleteItemsModalTitle: 'アイテムを永久に削除する',
	TrashDeletedAllItems: 'すべてのアイテムを削除しました',
	TrashDeletedItems: '{count}件のアイテムを削除しました',
	TrashDeletedItemsFailure: 'ゴミ箱からアイテムを削除できませんでした',
	TrashLocationAppointmentType: 'カレンダー',
	TrashLocationBillingAndPaymentsType: '請求と支払い',
	TrashLocationContactType: 'クライアント',
	TrashLocationNoteType: 'ノートとドキュメント',
	TrashRestoreItemsModalDescription: '以下の{count, plural, one {item} other {items}}が復元されます。',
	TrashRestoreItemsModalTitle: '{count, plural, one {アイテム} other {アイテム}} を復元します。',
	TrashRestoredAllItems: 'すべてのアイテムを復元しました',
	TrashRestoredItems: '{count}個のアイテムを復元しました',
	TrashRestoredItemsFailure: 'ゴミ箱からアイテムを復元できませんでした',
	TrashSuccessfullyDeletedItem: '{type} が正常に削除されました',
	Trigger: 'トリガー',
	Troubleshoot: 'トラブルシューティング',
	TryAgain: 'もう一度やり直してください',
	Tuesday: '火曜日',
	TwoToTen: '2 - 10',
	Type: 'タイプ',
	TypeHere: 'ここに入力...',
	TypeToConfirm: '確認のため、{keyword}と入力してください。',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: '見出し1',
	TypographyHeading2: '見出し2',
	TypographyHeading3: '見出し3',
	TypographyHeading4: '見出し4',
	TypographyHeading5: '見出し5',
	TypographyP: 'ポ',
	TypographyParagraph: '段落',
	UnableToCompleteAction: 'アクションを完了できません。',
	UnableToPrintDocument: 'ドキュメントを印刷できません。しばらくしてからもう一度お試しください。',
	Unallocated: '未割り当て',
	UnallocatedPaymentDescription: `この支払いは請求対象項目に完全に割り当てられていません。
未払いの項目に割り当てを追加するか、クレジットまたは払い戻しを発行します。`,
	UnallocatedPaymentTitle: '未割り当ての支払い',
	UnallocatedPayments: '未割り当ての支払い',
	Unarchive: 'アーカイブ解除',
	Unassigned: '未割り当て',
	UnauthorisedInvoiceSnackbar: 'このクライアントの請求書を管理するアクセス権がありません。',
	UnauthorisedSnackbar: 'これを実行する権限がありません。',
	Unavailable: '利用不可',
	Uncategorized: '未分類',
	Unclaimed: '未請求',
	UnclaimedAmount: '未請求額',
	UnclaimedItems: '未請求アイテム',
	UnclaimedItemsMustBeInCurrency: '以下の通貨でのみアイテムがサポートされています: {currencies}',
	Uncle: '叔父',
	Unconfirmed: '未確認',
	Underline: '下線',
	Undo: '元に戻す',
	Unfavorite: 'お気に入りから外す',
	Uninvoiced: '未請求',
	UninvoicedAmount: '未請求額',
	UninvoicedAmounts:
		'{count, plural, =0 {請求されていない金額はありません} one {請求されていない金額} other {請求されていない金額}}',
	Unit: 'ユニット',
	UnitedKingdom: 'イギリス',
	UnitedStates: 'アメリカ',
	UnitedStatesEast: 'アメリカ - 東部',
	UnitedStatesWest: 'アメリカ - 西部',
	Units: 'ユニット',
	UnitsIsRequired: '単位は必須です',
	UnitsMustBeGreaterThanZero: '単位は0より大きくなければなりません',
	UnitsPlaceholder: '1',
	Unknown: '不明',
	Unlimited: '無制限',
	Unlock: 'ロック解除',
	UnlockNoteHelper: '新しい変更を加える前に、編集者はノートのロックを解除する必要があります。',
	UnmuteAudio: '音声のミュートを解除',
	UnmuteEveryone: '全員のミュートを解除',
	Unpaid: '未払い',
	UnpaidInvoices: '未払いの請求書',
	UnpaidItems: '未払いの商品',
	UnpaidMultiple: '未払い',
	Unpublish: '非公開にする',
	UnpublishTemplateConfirmationModalPrompt:
		'<span>{title}</span> を削除すると、このリソースは Carepatron コミュニティから削除されます。この操作は取り消せません。',
	UnpublishToCommunitySuccessMessage: 'コミュニティから「{title}」を正常に削除しました。',
	Unread: '未読',
	Unrecognised: '認識されない',
	UnrecognisedDescription:
		'この支払い方法は、現在のアプリケーション バージョンでは認識されません。この支払い方法を表示および編集するには、ブラウザを更新して最新バージョンを入手してください。',
	UnsavedChanges: '保存されていない変更',
	UnsavedChangesPromptContent: '閉じる前に変更を保存しますか?',
	UnsavedChangesPromptTitle: '保存されていない変更があります',
	UnsavedNoteChangesWarning: '変更内容は保存されない可能性があります',
	UnsavedTemplateChangesWarning: '変更内容は保存されない可能性があります',
	UnselectAll: 'すべて選択解除',
	Until: 'まで',
	UntitledConversation: '無題の会話',
	UntitledFolder: '無題のフォルダ',
	UntitledNote: '無題のメモ',
	UntitledSchedule: '無題のスケジュール',
	UntitledSection: '無題セクション',
	UntitledTemplate: '無題のテンプレート',
	Unverified: '未確認',
	Upcoming: '今後の予定',
	UpcomingAppointments: '今後の予定',
	UpcomingDateOverridesEmpty: '日付の上書きは見つかりませんでした',
	UpdateAvailabilityScheduleFailure: '空き状況スケジュールの更新に失敗しました',
	UpdateAvailabilityScheduleSuccess: '空き状況スケジュールが正常に更新されました',
	UpdateInvoicesOrClaimsAgainstBillable: '新しい価格設定を参加者の請求書と請求に適用しますか?',
	UpdateLink: '更新リンク',
	UpdatePrimaryEmailWarningDescription:
		'クライアントのメールアドレスを変更すると、既存の予約とメモへのアクセスが失われます。',
	UpdatePrimaryEmailWarningTitle: 'クライアントメール変更',
	UpdateSettings: '設定を更新する',
	UpdateStatus: '最新状況',
	UpdateSuperbillReceiptFailure: 'スーパービルの領収書を更新できませんでした',
	UpdateSuperbillReceiptSuccess: 'スーパービルの領収書が正常に更新されました',
	UpdateTaskBillingDetails: '請求の詳細を更新する',
	UpdateTaskBillingDetailsDescription:
		'予約の価格が変更されました。新しい価格を出席者の請求項目、請求書、請求に適用しますか? 続行する更新を選択してください。',
	UpdateTemplateFolderSuccessMessage: 'フォルダの更新が完了しました。',
	UpdateUnpaidInvoices: '未払い請求書を更新する',
	UpdateUserInfoSuccessSnackbar: 'ユーザー情報が正常に更新されました。',
	UpdateUserSettingsSuccessSnackbar: 'ユーザー設定が正常に更新されました。',
	Upgrade: 'アップグレード',
	UpgradeForSMSReminder: '<b>プロフェッショナル</b>にアップグレードすると、SMSリマインダーが無制限に送信されます',
	UpgradeNow: '今すぐアップグレード',
	UpgradePlan: 'アップグレードプラン',
	UpgradeSubscriptionAlertDescription:
		'ストレージが不足しています。プランをアップグレードして追加のストレージをロック解除し、診療所をスムーズに運営しましょう！',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'ストレージが不足しています。 <span>管理者アクセス</span>権限を持つ診療所のメンバーに連絡して、プランをアップグレードして追加のストレージをロック解除し、診療所をスムーズに運用できるようにしましょう！',
	UpgradeSubscriptionAlertTitle: 'サブスクリプションをアップグレードする時期です',
	UpgradeYourPlan: 'プランをアップグレードする',
	UploadAudio: 'オーディオをアップロード',
	UploadFile: 'ファイルをアップロードする',
	UploadFileDescription: 'どのソフトウェアプラットフォームから移行されますか？',
	UploadFileMaxSizeError: 'ファイルサイズが大きすぎます。最大ファイルサイズは {fileSizeLimit} です。',
	UploadFileSizeLimit: 'サイズ制限 {size}MB',
	UploadFileTileDescription:
		'クライアントをアップロードするには、CSV、XLS、XLSX、またはZIPファイルを使用してください。',
	UploadFileTileLabel: 'ファイルのアップロード',
	UploadFiles: 'ファイルをアップロードする',
	UploadIndividually: 'ファイルを個別にアップロードする',
	UploadLogo: 'ロゴをアップロード',
	UploadPhoto: '写真をアップする',
	UploadToCarepatron: 'Carepatronにアップロード',
	UploadYourLogo: 'ロゴをアップロードする',
	UploadYourTemplates: 'テンプレートをアップロードしてください。変換いたします。',
	Uploading: 'アップロード中',
	UploadingAudio: 'オーディオをアップロードしています...',
	UploadingFiles: 'ファイルのアップロード',
	UrlLink: 'URLリンク',
	UsageCount: '{count} 回使用しました',
	UsageLimitValue: '{使用済み} の {制限} 使用済み',
	UsageValue: '{used} 使用済み',
	Use: '使用',
	UseAiToAutomateYourWorkflow: 'AI を使用してワークフローを自動化しましょう。',
	UseAsDefault: 'デフォルトとして使用',
	UseCustom: 'カスタムを使用する',
	UseDefault: 'デフォルトを使用します',
	UseDefaultFilters: 'デフォルトのフィルターを使用する',
	UseTemplate: 'テンプレートを使用する',
	UseThisCard: 'このカードを使う',
	UseValue: '"{value}" を使用してください。',
	UseWorkspaceDefault: 'ワークスペースのデフォルトを使用する',
	UserIsTyping: '{name} さんは入力しています…',
	Username: 'ユーザー名',
	Users: 'ユーザー',
	VAT: 'バット',
	ValidUrl: 'URL リンクは有効な URL である必要があります。',
	Validate: '検証',
	Validated: '検証済み',
	Validating: '検証中',
	ValidatingContent: 'コンテンツを検証しています...',
	ValidatingTranscripts: 'トランスクリプトを検証しています...',
	ValidationConfirmPasswordRequired: 'パスワードの確認が必要です',
	ValidationDateMax: '{max} より前にある必要があります。',
	ValidationDateMin: '{min} 以降でなければなりません。',
	ValidationDateRange: '開始日と終了日は必須です',
	ValidationEndDateMustBeAfterStartDate: '終了日は開始日より後にする必要があります。',
	ValidationMixedDefault: 'これは無効です',
	ValidationMixedRequired: 'これは必須です',
	ValidationNumberInteger: '整数である必要があります',
	ValidationNumberMax: '{max} 以下でなければなりません。',
	ValidationNumberMin: '{min} 以上でなければなりません。',
	ValidationPasswordNotMatching: 'パスワードが一致しません',
	ValidationPrimaryAddressIsRequired: 'デフォルトとして設定する場合はアドレスが必要です',
	ValidationPrimaryPhoneNumberIsRequired: 'デフォルトとして設定する場合は電話番号が必要です',
	ValidationServiceMustBeNotBeFuture: 'サービスは当日または将来の日付にしてはなりません。',
	ValidationStringEmail: '有効なメールアドレスである必要があります',
	ValidationStringMax: '{max} 文字以下でなければなりません。',
	ValidationStringMin: '{min} 文字以上である必要があります。',
	ValidationStringPhoneNumber: '有効な電話番号を入力してください',
	ValueMinutes: '{value} 分',
	VerbosityConcise: '簡潔',
	VerbosityDetailed: '詳細',
	VerbosityStandard: '標準',
	VerbositySuperDetailed: '非常に詳細',
	VerificationCode: '確認コード',
	VerificationEmailDescription: 'メールアドレスと、送信された確認コードを入力してください。',
	VerificationEmailSubtitle: 'スパムフォルダを確認してください - メールが届いていない場合',
	VerificationEmailTitle: 'メールを確認する',
	VerificationOption: 'メールの確認',
	Verified: '検証済み',
	Verify: '確認する',
	VerifyAndSubmit: '確認して送信する',
	VerifyEmail: 'メールアドレスを確認する',
	VerifyEmailAccessCode: '確認コード',
	VerifyEmailAddress: 'メールアドレスの確認',
	VerifyEmailButton: '確認してログアウト',
	VerifyEmailSentSnackbar: '確認メールを送信しました。受信トレイを確認してください。',
	VerifyEmailSubTitle: 'メールが届かない場合は迷惑メールフォルダを確認してください',
	VerifyEmailSuccessLogOutSnackbar: '成功しました。変更を適用するにはログアウトしてください。',
	VerifyEmailSuccessSnackbar:
		'成功しました。メールが確認されました。確認済みのアカウントとして続行するにはログインしてください。',
	VerifyEmailTitle: 'あなたの電子メールを確認します',
	VerifyNow: '今すぐ確認',
	Veterinarian: '獣医',
	VideoCall: 'ビデオ通話',
	VideoCallAudioInputFailed: '音声入力デバイスが機能していません',
	VideoCallAudioInputFailedMessage: '設定を開いて、マイクのソースが正しく設定されているか確認してください。',
	VideoCallChatBanner: 'メッセージはこの通話の参加者全員が見ることができ、通話が終了すると削除されます。',
	VideoCallChatSendBtn: 'メッセージを送ります',
	VideoCallChatTitle: 'チャット',
	VideoCallDisconnectedMessage: 'ネットワーク接続が失われました。再接続を試行しています',
	VideoCallOptionInfo: 'Zoomが接続されていない場合、Carepatronがビデオ通話の予約を管理します',
	VideoCallTilePaused: 'ネットワークの問題により、このビデオは一時停止されています',
	VideoCallTranscriptionFormDescription: 'これらの設定はいつでも調整できます',
	VideoCallTranscriptionFormHeading: 'AI Scribeをカスタマイズする',
	VideoCallTranscriptionFormLanguageField: '生成された出力言語',
	VideoCallTranscriptionFormNoteTemplateField: 'デフォルトのノートテンプレートを設定する',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'AI を含むテンプレートが見つかりません',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'テンプレートを選択',
	VideoCallTranscriptionPronounField: 'あなたの代名詞',
	VideoCallTranscriptionRecordingNote:
		'セッションの終了時に、生成された <strong>{noteTemplate} メモ</strong> とトランスクリプトを受け取ります。',
	VideoCallTranscriptionReferClientField: 'クライアントの参照先',
	VideoCallTranscriptionReferPractitionerField: '専門家を次のように呼ぶ',
	VideoCallTranscriptionTitle: 'AI スクライブ',
	VideoCallTranscriptionVerbosityField: '冗長性',
	VideoCallTranscriptionWritingPerspectiveField: '執筆の視点',
	VideoCalls: 'ビデオ通話',
	VideoConferencing: 'ビデオ会議',
	VideoOff: 'ビデオはオフです',
	VideoOn: 'ビデオはオフです',
	VideoQual360: '低画質（360p）',
	VideoQual540: '中画質（540p）',
	VideoQual720: '高画質（720p）',
	View: 'ビュー',
	ViewAll: 'すべて表示',
	ViewAppointment: '予約を見る',
	ViewBy: '見る者',
	ViewClaim: '請求を表示',
	ViewCollection: 'コレクションを見る',
	ViewDetails: '詳細を表示',
	ViewEnrollment: '登録を見る',
	ViewPayment: '支払いを見る',
	ViewRecord: '記録を表示',
	ViewRemittanceAdvice: '振込明細書を見る',
	ViewRemittanceAdviceHeader: '振込通知書請求',
	ViewRemittanceAdviceSubheader: '請求 {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: '設定を見る',
	ViewStripeDashboard: 'Stripeダッシュボードを表示',
	ViewTemplate: 'テンプレートを表示',
	ViewTemplates: 'テンプレートを表示',
	ViewableBy: '閲覧可能',
	ViewableByHelper:
		'あなたとチームは、あなたが公開したメモにいつでもアクセスできます。このメモをクライアントやその関係者と共有することを選択できます。',
	Viewer: '閲覧者',
	VirtualLocation: 'バーチャルロケーション',
	VisibleTo: '閲覧可能',
	VisitOurHelpCentre: 'ヘルプセンターにアクセスしてください',
	VisualEffects: '視覚効果',
	VoiceFocus: '音声フォーカス',
	VoiceFocusLabel: 'マイクから発せられる音声以外の音をフィルタリングします',
	Void: '空所',
	VoidCancelPriorClaim: '無効/以前の請求の取り消し',
	WaitingforMins: '{count} 分お待ちください',
	Warning: '警告',
	WatchAVideo: 'ビデオを見る',
	WatchDemoVideo: 'デモ動画を見る',
	WebConference: 'ウェブ会議',
	WebConferenceOrVirtualLocation: 'ウェブ会議/仮想ロケーション',
	WebDeveloper: 'ウェブ開発者',
	WebsiteOptional: 'ウェブサイト<span>(オプション)</span>',
	WebsiteUrl: 'ウェブサイトのURL',
	Wednesday: '水曜日',
	Week: '週',
	WeekPlural: '{count, plural, one {週} other {週}}',
	Weekly: '毎週',
	WeeksPlural: '{age, plural, one {# 週間} other {# 週間}}',
	WelcomeBack: 'おかえり',
	WelcomeBackName: 'ようこそ、{name}',
	WelcomeName: 'ようこそ {name}',
	WelcomeToCarepatron: 'Carepatronへようこそ',
	WhatCanIHelpWith: '何をお手伝いすればいいでしょうか?',
	WhatDidYouLikeResponse: 'この回答について、気に入った点は？',
	WhatIsCarepatron: 'Carepatronとは何ですか?',
	WhatMadeYouCancel: `計画をキャンセルした理由は何ですか?
当てはまるもの全てをご確認ください。`,
	WhatServicesDoYouOffer: 'どのような<mark>サービス</mark>を提供していますか？',
	WhatServicesDoYouOfferDescription: '後でサービスを編集または追加することができます。',
	WhatsYourAvailability: 'あなたの<mark>空き状況</mark>はどうですか。',
	WhatsYourAvailabilityDescription: '後でスケジュールを追加できます。',
	WhatsYourBusinessName: 'あなたの<mark>ビジネスの名前</mark>は？',
	WhatsYourTeamSize: 'あなたの<mark>チームの規模</mark>は？',
	WhatsYourTeamSizeDescription: 'これにより、ワークスペースを適切に設定するのに役立ちます。',
	WhenThisHappens: 'このような状況が発生すると、',
	WhichBestDescribesYou: '最も<mark>あなたを表すもの</mark>はどれですか？',
	WhichPlatforms: 'どのプラットフォーム？',
	Wife: '妻',
	WorkflowDescription: 'ワークフローの説明',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'テンプレートは、よりスムーズなプロセスを実現するためにワークフローにリンクできます。リンクされたワークフローを表示して、簡単に追跡および更新します。',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: '共通のトリガーに基づいて SMS とメールを接続します。',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'ワークフローの自動化',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: '自動化されたワークフロー',
	WorkflowTemplateConfigKey_Body: '本文',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'ブランドを表示する',
	WorkflowTemplateConfigKey_Content: 'コンテンツ',
	WorkflowTemplateConfigKey_Footer: 'フッター',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'フッターを表示する',
	WorkflowTemplateConfigKey_Header: '見出し',
	WorkflowTemplateConfigKey_Header_IsVisible: 'ヘッダーを表示する',
	WorkflowTemplateConfigKey_SecurityFooter: 'セキュリティフッター',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'セキュリティ フッターを表示する',
	WorkflowTemplateConfigKey_Subject: '件名',
	WorkflowTemplateConfigKey_Title: 'タイトル',
	WorkflowTemplateDeleteConfirmationMessage:
		'このテンプレートを削除してもよろしいですか？ この操作は元に戻すことができません。',
	WorkflowTemplateDeleteConfirmationTitle: '削除通知テンプレート',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'よろしいですか？ これにより、{locale} バージョンのみが削除されます。他の言語は影響を受けません。この操作は取り消せません。',
	WorkflowTemplateDeleteLocalisationDialogTitle: '‘{locale}’ テンプレートを削除します。',
	WorkflowTemplateDeletedSuccess: '通知テンプレートが正常に削除されました。',
	WorkflowTemplateEditorDetailsTab: 'テンプレートの詳細',
	WorkflowTemplateEditorEmailContent: 'メール内容',
	WorkflowTemplateEditorEmailContentTab: 'メールの内容',
	WorkflowTemplateEditorThemeTab: 'テーマ',
	WorkflowTemplatePreviewerAlert:
		'プレビューは、サンプルデータを使用して、クライアントがどのように表示されるかを示します。',
	WorkflowTemplateResetEmailContentDialogDescription:
		'よろしいでしょうか？これにより、バージョンがシステムのデフォルトテンプレートにリセットされます。この操作は元に戻すことができません。',
	WorkflowTemplateResetEmailContentDialogTitle: 'テンプレートのリセット',
	WorkflowTemplateSendTestEmail: 'テストメールを送信する',
	WorkflowTemplateSendTestEmailDialogDescription:
		'自分自身にテストメールを送信して、メール設定を試してみてください。',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: '受信者メールアドレス',
	WorkflowTemplateSendTestEmailDialogSendButton: 'テスト送信',
	WorkflowTemplateSendTestEmailDialogTitle: 'テストメールを送信する',
	WorkflowTemplateSendTestEmailSuccess: '成功！あなたの <mark>{templateName}</mark> テストメールが送信されました。',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'テンプレートを管理して、複数の言語バージョンを追加することで、クライアントと効果的にコミュニケーションを取ることができます。',
	WorkflowTemplateTemplateEditor: 'テンプレートエディター',
	WorkflowTemplateTranslateLocaleError: 'コンテンツの翻訳中に問題が発生しました。',
	WorkflowTemplateTranslateLocaleSuccess: 'コンテンツを **{locale}** に正常に翻訳しました。',
	WorkflowsAndReminders: 'ワークフロー ',
	WorkflowsManagement: 'ワークフロー管理',
	WorksheetAndHandout: 'ワークシート/ハンドアウト',
	WorksheetsAndHandoutsDescription: 'クライアントエンゲージメントと教育のため',
	Workspace: 'ワークスペース',
	WorkspaceBranding: 'ワークスペースのブランディング',
	WorkspaceBrandingDescription: `あなたのワークスペースを、あなたの個性を反映した統一感のあるスタイルで簡単にブランディングしましょう。
プロフェッショナリズムと個性。請求書をオンライン予約にカスタマイズして美しい
顧客体験。`,
	WorkspaceName: 'ワークスペース名',
	Workspaces: 'ワークスペース',
	WriteOff: '帳消し',
	WriteOffModalDescription:
		'あなたは <mark>{count} {count, plural, one {行項目} other {行項目}}</mark> を償却する必要があります。',
	WriteOffModalTitle: '償却調整',
	WriteOffReasonHelperText: 'これは社内メモであり、クライアントには表示されません。',
	WriteOffReasonPlaceholder: '減額理由を追加すると、請求可能な取引を確認するときに役立ちます。',
	WriteOffTotal: '全額償却 ({currencyCode})',
	Writer: 'ライター',
	Yearly: '年次',
	YearsPlural: '{age, plural, one {# 年} other {# 年}}',
	Yes: 'はい',
	YesArchive: 'はい、アーカイブ',
	YesDelete: 'はい、削除します',
	YesDeleteOverride: 'はい、オーバーライドを削除します',
	YesDeleteSection: 'はい、削除します',
	YesDisconnect: 'はい、切断します。',
	YesEnd: 'はい、終わります',
	YesEndTranscription: 'はい、転写終了',
	YesImFineWithThat: 'はい、それで大丈夫です',
	YesLeave: 'はい、出て行ってください。',
	YesMinimize: 'はい、最小化',
	YesOrNoAnswerTypeDescription: '回答タイプを設定する',
	YesOrNoFormPrimaryText: 'はい | いいえ',
	YesOrNoFormSecondaryText: 'はいまたはいいえのオプションを選択してください',
	YesProceed: 'はい、続行してください。',
	YesRemove: 'はい、削除します',
	YesRestore: 'はい、復元します',
	YesStopIgnoring: 'はい、無視をやめる',
	YesTransfer: 'はい、転送します。',
	Yesterday: '昨日',
	YogaInstructor: 'ヨガインストラクター',
	You: 'あなた',
	YouArePresenting: 'あなたはプレゼンテーション中です',
	YouCanChooseMultiple: '複数選択可能',
	YouCanSelectMultiple: '複数選択可能',
	YouHaveOngoingTranscription: '転写が進行中です',
	YourAnswer: 'あなたの答え',
	YourDisplayName: '表示名',
	YourSpreadsheetColumns: 'スプレッドシートの列',
	YourTeam: 'あなたのチーム',
	ZipCode: '郵便番号',
	Zoom: 'ズーム',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'このチーム メンバーの Zoom 通話を追加することはできません。<a>詳細については、サポート ドキュメントを参照してください。</a>',
};

export default items;
