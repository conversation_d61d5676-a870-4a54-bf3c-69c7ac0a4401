import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'AI 提示',
	ATeamMemberIsRequired: '需要一名团队成员',
	AboutClient: '关于客户',
	AcceptAppointment: '感谢您确认预约',
	AcceptTermsAndConditionsRequired: '接受条款 ',
	Accepted: '已接受',
	AccessGiven: '已授予访问权限',
	AccessPermissions: '访问权限',
	AccessType: '访问类型',
	Accident: '事故',
	Account: '帐户',
	AccountCredit: '帐户信用',
	Accountant: '会计',
	Action: '行动',
	Actions: '操作',
	Active: '积极的',
	ActiveTags: '活动标签',
	ActiveUsers: '活跃用户',
	Activity: '活动',
	Actor: '演员',
	Acupuncture: '针刺',
	Acupuncturist: '针灸师',
	Acupuncturists: '针灸师',
	AcuteManifestationOfAChronicCondition: '慢性病的急性表现',
	Add: '添加',
	AddADescription: '添加描述',
	AddALocation: '添加位置',
	AddASecondTimezone: '添加第二个时区',
	AddAddress: '添加地址',
	AddAnother: '  添加另一个',
	AddAnotherAccount: '添加其他帐户',
	AddAnotherContact: '添加另一个联系人',
	AddAnotherOption: '添加另一个选项',
	AddAnotherTeamMember: '添加另一名团队成员',
	AddAvailablePayers: '+ 添加可用的付款人',
	AddAvailablePayersDescription:
		'搜索需要添加到您的工作空间付款人列表的付款人。添加后，您可以管理注册或根据需要调整付款人详细信息。',
	AddCaption: '添加标题',
	AddClaim: '添加声明',
	AddClientFilesModalDescription: '要限制访问，请选择“可查看”复选框中的选项',
	AddClientFilesModalTitle: '上传 {name} 的文件',
	AddClientNoteButton: '添加注释',
	AddClientNoteModalDescription: '向您的笔记添加内容。使用“可查看者”部分选择一个或多个可查看此特定笔记的群组。',
	AddClientNoteModalTitle: '添加注释',
	AddClientOwnerRelationshipModalDescription:
		'邀请客户将允许他们管理自己的个人资料信息并管理用户对其个人资料信息的访问。',
	AddClientOwnerRelationshipModalTitle: '邀请客户',
	AddCode: '添加代码',
	AddColAfter: '在后面添加列',
	AddColBefore: '在前面添加列',
	AddCollection: '添加收藏夹',
	AddColor: '添加颜色',
	AddColumn: '添加列',
	AddContactRelationship: '添加联系关系',
	AddContacts: '添加联系人',
	AddCustomField: '添加自定义字段',
	AddDate: '添加日期',
	AddDescription: '添加描述',
	AddDetail: '添加详细信息',
	AddDisplayName: '添加显示名称',
	AddDxCode: '添加诊断代码',
	AddEmail: '添加电子邮件',
	AddFamilyClientRelationshipModalDescription:
		'邀请家庭成员将允许他们查看护理故事和客户的个人资料信息。如果他们被邀请为管理员，他们将有权更新客户的个人资料信息并管理用户访问权限。',
	AddFamilyClientRelationshipModalTitle: '邀请家庭成员',
	AddField: '添加字段',
	AddFormField: '添加表单字段',
	AddImages: '添加图像',
	AddInsurance: '添加保险',
	AddInvoice: '添加发票',
	AddLabel: '添加标签',
	AddLanguage: '添加语言',
	AddLocation: '添加位置',
	AddManually: '手动添加',
	AddMessage: '添加消息',
	AddNewAction: '添加新动作',
	AddNewSection: '添加新部分',
	AddNote: '添加注释',
	AddOnlineBookingDetails: '添加在线预订详情',
	AddPOS: '添加 POS',
	AddPaidInvoices: '添加已付发票',
	AddPayer: '添加付款人',
	AddPayment: '添加付款',
	AddPaymentAdjustment: '添加付款调整',
	AddPaymentAdjustmentDisabledDescription: '付款分配不会改变。',
	AddPaymentAdjustmentEnabledDescription: '可供分配的金额将会减少。',
	AddPhoneNumber: '添加电话号码',
	AddPhysicalOrVirtualLocations: '添加物理或虚拟位置',
	AddQuestion: '添加问题',
	AddQuestionOrTitle: '添加问题或标题',
	AddRelationship: '添加关系',
	AddRelationshipModalTitle: '连接现有联系人',
	AddRelationshipModalTitleNewClient: '连接新联系人',
	AddRow: '添加行',
	AddRowAbove: '在上方添加行',
	AddRowBelow: '在下面添加行',
	AddService: '添加服务',
	AddServiceLocation: '添加服务地点',
	AddServiceToCollections: '将服务添加到收藏夹',
	AddServiceToOneOrMoreCollections: '将服务添加到一个或多个集合',
	AddServices: '添加服务',
	AddSignature: '添加签名',
	AddSignaturePlaceholder: '键入要包含在签名中的其他详细信息',
	AddSmartDataChips: '添加智能数据芯片',
	AddStaffClientRelationshipsModalDescription:
		'选择员工将允许他们为该客户创建和查看护理故事。他们还可以查看客户信息。',
	AddStaffClientRelationshipsModalTitle: '添加员工关系',
	AddTag: '添加标签',
	AddTags: '添加标签',
	AddTemplate: '添加模板',
	AddTimezone: '添加时区',
	AddToClaim: '添加至申领',
	AddToCollection: '添加到收藏夹',
	AddToExisting: '添加到现有',
	AddToStarred: '添加到星标',
	AddUnclaimedItems: '添加无人认领的物品',
	AddUnrelatedContactWarning: '您添加了非 {contact} 联系人的人员。请确保内容相关后再进行分享。',
	AddValue: '添加 "{value}"',
	AddVideoCall: '添加视频通话',
	AddVideoOrVoiceCall: '添加视频或语音通话',
	AddictionCounselor: '成瘾顾问',
	AddingManualPayerDisclaimer:
		'将付款人手动添加到提供商列表中并不会与该付款人建立电子索赔申请连接，但可用于手动创建索赔。',
	AddingTeamMembersIncreaseCostAlert: '添加新团队成员将增加您的每月订阅费用。',
	Additional: '额外的',
	AdditionalBillingProfiles: '其他计费配置文件',
	AdditionalBillingProfilesSectionDescription: '覆盖特定团队成员、付款人或发票模板使用的默认账单信息。',
	AdditionalFeedback: '其他反馈',
	AddnNewWorkspace: '新工作区',
	AddnNewWorkspaceSuccessSnackbar: '工作区已创建！',
	Address: '地址',
	AddressNumberStreet: '地址（街道号）',
	Adjustment: '调整',
	AdjustmentType: '调整类型',
	Admin: '行政',
	Admins: '管理员',
	AdminsOnly: '仅限管理员',
	AdvancedPlanInclusionFive: '客户经理',
	AdvancedPlanInclusionFour: 'Google 分析',
	AdvancedPlanInclusionHeader: 'Plus 中的一切  ',
	AdvancedPlanInclusionOne: '角色 ',
	AdvancedPlanInclusionSix: '数据导入支持',
	AdvancedPlanInclusionThree: '白色标签',
	AdvancedPlanInclusionTwo: '90 天已删除数据保留',
	AdvancedPlanMessage: '掌控您的诊所需求。查看您当前的计划并监控使用情况。',
	AdvancedSettings: '高级设置',
	AdvancedSubscriptionPlanSubtitle: '利用所有功能扩展您的业务实践',
	AdvancedSubscriptionPlanTitle: '先进的',
	AdvertisingManager: '广告经理',
	AerospaceEngineer: '航空工程师',
	AgeYearsOld: '{age} 岁',
	Agenda: '议程',
	AgendaView: '议程视图',
	AiAskSupportedFileTypes: '支持的文件类型：JPEG、PNG、PDF、Word',
	AiAssistantAtYourFingertips: '指尖上的助手',
	AiCopilotDisclaimer: 'AI Copilot 可能会出错。检查重要信息。',
	AiCreateNewConversation: '创建新对话',
	AiEnhanceYourProductivity: '提高你的工作效率',
	AiPoweredTemplates: '人工智能模板',
	AiScribeNoDeviceFoundErrorMessage: '您的浏览器似乎不支持此功能，或者没有可用的兼容设备。',
	AiScribeUploadFormat: '支持的文件类型：MP3、WAV、MP4',
	AiScribeUploadSizeLimit: '每次只能处理 1 个文件',
	AiShowConversationHistory: '显示对话历史记录',
	AiSmartPromptNodePlaceholderText: '在这里输入您的自定义提示，以帮助生成准确且个性化的 AI 结果。',
	AiSmartPromptPrimaryText: 'AI 智能提示',
	AiSmartPromptSecondaryText: '插入自定义 AI 智能提示',
	AiSmartReminders: 'AI智能提醒',
	AiTemplateBannerTitle: '使用 AI 模板简化您的工作',
	AiTemplates: 'AI 模板',
	AiTokens: 'AI 代币',
	AiWorkBetterWithAi: '与 AI 协同工作',
	All: '全部',
	AllAppointments: '所有预约',
	AllCategories: '所有类别',
	AllClients: '所有客户端',
	AllContactPolicySelectorLabel: '<mark>{client}</mark> 的所有联系人',
	AllContacts: '所有联系人',
	AllContactsOf: '所有‘{name}’ 的联系人',
	AllDay: '全天',
	AllInboxes: '所有收件箱',
	AllIndustries: '所有行业',
	AllLocations: '所有地点',
	AllMeetings: '所有会议',
	AllNotificationsRestoredMessage: '所有通知均已恢复',
	AllProfessions: '所有职业',
	AllReminders: '所有提醒',
	AllServices: '所有服务',
	AllStatuses: '所有状态',
	AllTags: '所有标签',
	AllTasks: '所有任务',
	AllTeamMembers: '所有团队成员',
	AllTypes: '所有类型',
	Allocated: '已分配',
	AllocatedItems: '已分配项目',
	AllocationTableEmptyState: '未找到付款分配',
	AllocationTotalWarningMessage: `分配金额超出总付款金额。
请查看下面的项目。`,
	AllowClientsToCancelAnytime: '允许客户随时取消',
	AllowNewClient: '允许新客户',
	AllowNewClientHelper: '新客户可以预订此服务',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: '预约前至少留出 {hours} 小时',
	AllowToUseSavedCard: '允许 {provider} 在未来使用保存的卡片',
	AllowVideoCalls: '允许视频通话',
	AlreadyAdded: '已添加',
	AlreadyHasAccess: '有访问权限',
	AlreadyHasAccount: '已有账户？',
	Always: '始终',
	AlwaysIgnore: '总是忽略',
	Amount: '数量',
	AmountDue: '应付金额',
	AmountOfReferralRequests: '{amount, plural, one {# 推荐请求} other {# 推荐请求}}',
	AmountPaid: '已付金额',
	AnalyzingAudio: '正在分析音频...',
	AnalyzingInputContent: '正在分析输入内容...',
	AnalyzingRequest: '正在分析请求...',
	AnalyzingTemplateContent: '正在分析模板内容...',
	And: '和',
	Annually: '每年',
	Anonymous: '匿名的',
	AnswerExceeded: '您的回答必须少于 300 个字符。',
	AnyStatus: '任何状态',
	AppNotifications: '通知',
	AppNotificationsClearanceHeading: '干得好！您已清除所有活动',
	AppNotificationsEmptyHeading: '您的工作区活动将很快显示在这里',
	AppNotificationsEmptySubtext: '目前无需采取任何行动',
	AppNotificationsIgnoredCount: '{total} 忽略',
	AppNotificationsUnread: '{total} 未读',
	Append: '附加',
	Apply: '申请',
	ApplyAccountCredit: '申请账户信用',
	ApplyDiscount: '申请折扣',
	ApplyVisualEffects: '应用视觉效果',
	ApplyVisualEffectsNotSupported: '应用不支持的视觉效果',
	Appointment: '预约',
	AppointmentAssignedNotificationSubject: '{actorProfileName} 已为您安排了 {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} 已取消 {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} 已确认 {appointmentName}',
	AppointmentDetails: '预约详情',
	AppointmentLocation: '预约地点',
	AppointmentLocationDescription: '管理您的默认虚拟和物理位置。安排预约时，这些位置将自动应用。',
	AppointmentNotFound: '未找到预约',
	AppointmentReminder: '预约提醒',
	AppointmentReminders: '预约提醒',
	AppointmentRemindersInfo: '为客户预约设置自动提醒，以避免缺席或取消预约',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} 已将 {appointmentName} 重新安排',
	AppointmentSaved: '已保存预约',
	AppointmentStatus: '预约状态',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0分钟} other {{minutes}分钟}} } one {{hours}小时 {minutes, plural, =0 {} other {{minutes}分钟}}} other {{hours}小时 {minutes, plural, =0 {} other {{minutes}分钟}}} }',
	AppointmentUndone: '取消预约',
	Appointments: '预约',
	Archive: '档案',
	ArchiveClients: '存档客户端',
	Archived: '已归档',
	AreYouAClient: '您是客戶嗎？',
	AreYouStillThere: '你还在吗？',
	AreYouSure: '你确定吗？',
	Arrangements: '安排',
	ArtTherapist: '艺术治疗师',
	Articles: '文章',
	Artist: '艺术家',
	AskAI: '询问人工智能',
	AskAiAddFormField: '添加表单字段',
	AskAiChangeFormality: '变更手续',
	AskAiChangeToneToBeMoreProfessional: '改变语气以显得更专业',
	AskAiExplainThis: '问问人工智能解释一下',
	AskAiExplainWhatThisDocumentIsAbout: '解释一下这个文档的内容',
	AskAiExplainWhatThisImageIsAbout: '解释一下这张图片的意思',
	AskAiFixSpellingAndGrammar: '修正拼写和语法',
	AskAiGenerateACaptionForThisImage: '为该图片生成标题',
	AskAiGenerateFromThisPage: '从此页面生成',
	AskAiGetStarted: '开始使用',
	AskAiGiveItAFriendlyTone: '用友好的语气',
	AskAiGreeting: '嗨 {firstName}！今天有什么可以帮到您的吗？',
	AskAiHowCanIHelpWithYourContent: '我能为您的内容提供什么帮助？',
	AskAiInsert: '插入',
	AskAiMakeItMoreCasual: '让其更加随意',
	AskAiMakeThisTextMoreConcise: '使此文本更简洁',
	AskAiMoreProfessional: '更专业',
	AskAiOpenPreviousNote: '打开上一条笔记',
	AskAiPondering: '思考',
	AskAiReplace: '代替',
	AskAiReviewOrEditSelection: '审阅或编辑选择',
	AskAiRuminating: '反思',
	AskAiSeeMore: '查看更多',
	AskAiSimplifyLanguage: '简化语言',
	AskAiSomethingWentWrong: '出了点问题。如果问题持续存在，请通过我们的帮助中心联系我们。',
	AskAiStartWithATemplate: '从模板开始',
	AskAiSuccessfullyCopiedResponse: '成功复制AI响应',
	AskAiSuccessfullyInsertedResponse: '成功插入AI响应',
	AskAiSuccessfullyReplacedResponse: '成功更换AI响应',
	AskAiSuggested: '建议',
	AskAiSummariseTextIntoBulletPoints: '将文本总结为要点',
	AskAiSummarizeNote: '总结笔记',
	AskAiThinking: '思维',
	AskAiToday: '今天 {time}',
	AskAiWhatDoYouWantToDoWithThisForm: '您想用这个表单做什么？',
	AskAiWriteProfessionalNoteUsingTemplate: '使用模板撰写专业笔记',
	AskAskAiAnything: '向人工智能询问任何事情',
	AskWriteSearchAnything: '询问、写‘@’或搜索任何内容...',
	Asking: '询问',
	Assessment: '评估',
	Assessments: '评估',
	AssessmentsCategoryDescription: '用于记录客户评估',
	AssignClients: '分配客户',
	AssignNewClients: '分配客户',
	AssignServices: '分配服务',
	AssignTeam: '分配团队',
	AssignTeamMember: '指派团队成员',
	Assigned: '已分配',
	AssignedClients: '指定客户',
	AssignedServices: '指定服务',
	AssignedServicesDescription: '查看和管理您分配的服务，调整价格以反映您的自定义费率。 ',
	AssignedTeam: '指派团队',
	AthleticTrainer: '运动训练师',
	AttachFiles: '附加文件',
	AttachLogo: '附',
	Attachment: '依恋',
	AttachmentBlockedFileType: '由于安全原因已被阻止！',
	AttachmentTooLargeFileSize: '文件太大',
	AttachmentUploadItemComplete: '完全的',
	AttachmentUploadItemError: '上传失败',
	AttachmentUploadItemLoading: '加载中',
	AttemptingToReconnect: '正在尝试重新连接…',
	Attended: '已参加',
	AttendeeBeingMutedTooltip: '主持人已将您静音。使用“举手”请求取消静音',
	AttendeeWithId: '参会者 {attendeeId}',
	Attendees: '出席者',
	AttendeesCount: '{count} 位与会者',
	Attending: '参加',
	Audiologist: '听力学家',
	Aunt: '阿姨',
	Australia: '澳大利亚',
	AuthenticationCode: '验证码',
	AuthoriseProvider: '授权 {provider}',
	AuthorisedProviders: '授权供应商',
	AutoDeclineAllFutureOption: '仅限新事件或预约',
	AutoDeclineAllOption: '新旧活动或预约',
	AutoDeclinePrimaryText: '自动拒绝活动',
	AutoDeclineSecondaryText: '您不在办公室期间的活动将自动拒绝',
	AutogenerateBillings: '自动生成账单文件',
	AutogenerateBillingsDescription: '自动计费文件将在每月最后一天生成。发票和超级账单收据可随时手动创建。',
	AutomateWorkflows: '自动化工作流程',
	AutomaticallySendSuperbill: '自动发送超级账单收据',
	AutomaticallySendSuperbillHelperText: '超级账单是一份向客户提供保险报销服务的详细收据',
	Automation: '自动化',
	AutomationActionSendEmailLabel: '发送邮件',
	AutomationActionSendSMSLabel: '发送短信',
	AutomationAndReminders: '自动化 ',
	AutomationDeletedSuccessMessage: '已成功删除自动化',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: '时间事件',
	AutomationParams_timeUnit: '单元',
	AutomationParams_timeValue: '数字',
	AutomationPublishSuccessMessage: '自动化发布成功',
	AutomationPublishWarningTooltip: '请重新检查自动化配置并确保其配置正确',
	AutomationTriggerEventCancelledDescription: '当事件被取消或删除时触发',
	AutomationTriggerEventCancelledLabel: '活动取消',
	AutomationTriggerEventCreatedDescription: '事件创建时触发',
	AutomationTriggerEventCreatedLabel: '新活动',
	AutomationTriggerEventCreatedOrUpdatedDescription: '在创建或更新事件时触发（取消事件除外）',
	AutomationTriggerEventCreatedOrUpdatedLabel: '新活动或更新活动',
	AutomationTriggerEventEndedDescription: '事件结束时触发',
	AutomationTriggerEventEndedLabel: '活动已结束',
	AutomationTriggerEventStartsDescription: '在事件开始前指定的时间内触发',
	AutomationTriggerEventStartsLabel: '活动开始',
	Automations: '自动化',
	Availability: '可用性',
	AvailabilityDisableSchedule: '禁用计划',
	AvailabilityDisabled: '已禁用',
	AvailabilityEnableSchedule: '启用计划',
	AvailabilityEnabled: '已启用',
	AvailabilityNoActiveBanner: '您已关闭所有日程安排。客户无法在线为您预约，所有未来预约都需要手动确认。',
	AvailabilityNoActiveConfirmationDescription:
		'禁用此可用性将导致没有活动时间表。客户将无法在线预约您，并且从业者的任何预约都将超出您的工作时间。',
	AvailabilityNoActiveConfirmationProceed: '是，继续',
	AvailabilityNoActiveConfirmationTitle: '没有活动时间表',
	AvailabilityToggle: '已启用计划',
	AvailabilityUnsetDate: '尚未确定日期',
	AvailableLocations: '可用位置',
	AvailablePayers: '可用的付款人',
	AvailablePayersEmptyState: '没有选择的付款人',
	AvailableTimes: '可用时间',
	Back: '后退',
	BackHome: '回到家',
	BackToAppointment: '返回预约',
	BackToLogin: '返回登录',
	BackToMapColumns: '返回地图列',
	BackToTemplates: '返回模板',
	BackToUploadFile: '返回上传文件',
	Banker: '银行家',
	BasicBlocks: '基本块',
	BeforeAppointment: '在预约前 {interval} {unit} 发送 {deliveryType} 提醒',
	BehavioralAnalyst: '行为分析师',
	BehavioralHealthTherapy: '行为健康治疗',
	Beta: '测试版',
	BillTo: '记账到',
	BillableItems: '计费项目',
	BillableItemsEmptyState: '未找到可计费项目',
	Biller: '比勒',
	Billing: '计费',
	BillingAddress: '帐单地址',
	BillingAndReceiptsUnauthorisedMessage: '需要发票和付款查看权限才能访问此信息。',
	BillingBillablesTab: '可计费',
	BillingClaimsTab: '索赔',
	BillingDetails: '账单详细信息',
	BillingDocuments: '账单文件',
	BillingDocumentsClaimsTab: '索赔',
	BillingDocumentsEmptyState: '未找到任何 {tabType}',
	BillingDocumentsInvoicesTab: '发票',
	BillingDocumentsSuperbillsTab: '超级钞票',
	BillingInformation: '账单信息',
	BillingInvoicesTab: '发票',
	BillingItems: '计费项目',
	BillingPaymentsTab: '付款',
	BillingPeriod: '计费周期',
	BillingProfile: '计费配置文件Billing profile',
	BillingProfileOverridesDescription: '将此账单资料的使用限制在特定团队成员身上',
	BillingProfileOverridesHeader: '限制访问',
	BillingProfileProviderType: '提供者类型',
	BillingProfileTypeIndividual: '从业者',
	BillingProfileTypeIndividualSubLabel: '1 类新产品信息',
	BillingProfileTypeOrganisation: '组织',
	BillingProfileTypeOrganisationSubLabel: '2 类新产品信息',
	BillingProfiles: '计费配置文件Billing Profiles',
	BillingProfilesEditHeader: '编辑 {name} 账单资料',
	BillingProfilesNewHeader: '新的帐单资料',
	BillingProfilesSectionDescription:
		'通过设置可应用于发票和保险赔付的账单配置文件来管理从业人员和保险付款人的账单信息。',
	BillingSearchPlaceholder: '搜索项目',
	BillingSettings: '帐单设置',
	BillingSuperbillsTab: '超级钞票',
	BiomedicalEngineer: '生物医学工程师',
	BlankInvoice: '空白发票',
	BlueShieldProviderNumber: '蓝盾提供商号码',
	Body: '身体',
	Bold: '大胆的',
	BookAgain: '再次预订',
	BookAppointment: '預約',
	BookableOnline: '可在线预订',
	BookableOnlineHelper: '客户可以在线预订此服务',
	BookedOnline: '在线预订',
	Booking: '預訂',
	BookingAnalyticsIntegrationPanelDescription:
		'设置 Google Tag Manager 来跟踪在线预订流程中的关键操作和转化。收集有关用户互动的宝贵数据，以改进营销工作并优化预订体验。',
	BookingAnalyticsIntegrationPanelTitle: '分析集成',
	BookingAndCancellationPolicies: '預訂 ',
	BookingButtonEmbed: '按钮',
	BookingButtonEmbedDescription: '在您的网站上添加在线预订按钮',
	BookingDirectTextLink: '直接文本链接',
	BookingDirectTextLinkDescription: '打开在线预订页面',
	BookingFormatLink: '格式链接',
	BookingFormatLinkButtonTitle: '按钮标题',
	BookingInlineEmbed: '内联嵌入',
	BookingInlineEmbedDescription: '直接在您的网站上加载在线预订页面',
	BookingLink: '预订链接',
	BookingLinkModalCopyText: '复制',
	BookingLinkModalDescription: '允许通过此链接的客户预订任何团队成员或服务',
	BookingLinkModalHelpText: '了解如何设置在线预订',
	BookingLinkModalTitle: '分享您的预订链接',
	BookingPolicies: '预订政策',
	BookingPoliciesDescription: '设置客户何时可以进行在线预订',
	BookingTimeUnitDays: '天',
	BookingTimeUnitHours: '小时',
	BookingTimeUnitMinutes: '分钟',
	BookingTimeUnitMonths: '个月',
	BookingTimeUnitWeeks: '周',
	BottomNavBilling: '计费',
	BottomNavGettingStarted: '家',
	BottomNavMore: '更多的',
	BottomNavNotes: '笔记',
	Brands: '品牌',
	Brother: '兄弟',
	BrotherInLaw: '小舅子',
	BrowseOrDragFileHere: '<link>浏览</link> 或将文件拖到这里',
	BrowseOrDragFileHereDescription: 'PNG、JPG（最大 {limit}）',
	BufferAfterTime: '{time} 分钟后',
	BufferAndLabel: '和',
	BufferAppointmentLabel: '预约',
	BufferBeforeTime: '{time} 分钟前',
	BufferTime: '缓冲时间',
	BufferTimeViewLabel: '{bufferBefore} 分钟前和 {bufferBefore} 分钟后预约',
	BulkArchiveClientsDescription: '您确实要存档这些客户端吗？您可以稍后重新激活它们。',
	BulkArchiveSuccess: '成功存档客户',
	BulkArchiveUndone: '批量存档已撤消',
	BulkPermanentDeleteDescription: '这将删除 **{count} 个对话**。此操作无法撤消。',
	BulkPermanentDeleteTitle: '永久删除对话',
	BulkUnarchiveSuccess: '成功取消存档客户端',
	BulletedList: '项目符号列表',
	BusinessAddress: '营业地址',
	BusinessAddressOptional: '公司地址<span>（可选）</span>',
	BusinessName: '公司名称',
	Button: '按钮',
	By: '经过',
	CHAMPUSIdentificationNumber: 'CHAMPUS 识别号码',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: '需要 CVC',
	Calendar: '日历',
	CalendarAppSyncFormDescription: '将 Carepatron 事件同步至',
	CalendarAppSyncPanelTitle: '已连接的应用同步',
	CalendarDescription: '管理您的约会或设置个人任务和提醒',
	CalendarDetails: '日历详细信息',
	CalendarDetailsDescription: '管理您的日历和约会显示设置。',
	CalendarScheduleNew: '安排新',
	CalendarSettings: '日历设置',
	Call: '称呼',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong>已加入视频通话',
	CallChangeLayoutTextContent: '保存选择以供将来的会议使用',
	CallIdlePrompt: '您是否希望继续等待加入，还是愿意稍后再试？',
	CallLayoutOptionAuto: '汽车',
	CallLayoutOptionSidebar: '侧边栏',
	CallLayoutOptionSpotlight: '聚光灯',
	CallLayoutOptionTiled: '平铺',
	CallNoAttendees: '会议无人出席。',
	CallSessionExpiredError: '会话已过期。通话已结束。请尝试再次加入。',
	CallWithPractitioner: '与 {practitioner} 通话',
	CallsListCreateButton: '新呼叫',
	CallsListEmptyState: '没有正在进行的通话',
	CallsListItemEndCall: '结束通话',
	CamWarningMessage: '检测到您的相机存在问题',
	Camera: '相机',
	CameraAndMicIssueModalDescription: `请允许 Carepatron 访问您的相机和麦克风。
如需更多信息，<a>请遵循本指南</a>`,
	CameraAndMicIssueModalTitle: '摄像头和麦克风被遮挡',
	CameraQuality: '相机质量',
	CameraSource: '相机源',
	CanModifyReadOnlyEvent: '您无法修改此事件。',
	Canada: '加拿大',
	Cancel: '取消',
	CancelClientImportDescription: '您确定要取消本次导入吗？',
	CancelClientImportPrimaryAction: '是的，取消导入',
	CancelClientImportSecondaryAction: '继续编辑',
	CancelClientImportTitle: '取消导入客户',
	CancelImportButton: '取消导入',
	CancelPlan: '取消计划',
	CancelPlanConfirmation: `取消该计划将自动从您的帐户中扣除本月的所有未结余额。
如果您想降低付费用户的级别，您只需删除团队成员，Carepatron 就会自动更新您的订阅价格。`,
	CancelSend: '取消发送',
	CancelSubscription: '取消订阅',
	Canceled: '已取消',
	CancellationPolicy: '取消政策',
	Cancelled: '取消',
	CannotContainSpecialCharactersError: '不能包含 {specialCharacters}',
	CannotDeleteInvoice: '通过网上支付支付的发票无法删除',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> 无法移出 <b>{group}</b> 组',
	CannotMoveServiceOutsideCollections: '服务不能移出集合',
	CapeTown: '开普敦',
	Caption: '标题',
	CaptureNameFieldLabel: '您希望别人称呼您的名字',
	CapturePaymentMethod: '捕获付款方式',
	CapturingAudio: '捕获音频',
	CapturingSignature: '正在捕获签名...',
	CardInformation: '卡信息',
	CardNumberRequired: '需要卡号',
	CardiacRehabilitationSpecialist: '心脏康复专家',
	Cardiologist: '心脏病专家',
	CareAiNoConversations: '还没有对话',
	CareAiNoConversationsDescription: '开始与{aiName}对话以开始',
	CareAssistant: '护理助理',
	CareManager: '护理经理',
	Caregiver: '照顾者',
	CaregiverCreateModalDescription:
		'添加员工作为管理员将允许他们创建和管理护理故事。他们还拥有创建和管理客户的完全权限。',
	CaregiverCreateModalTitle: '新团队成员',
	CaregiverListCantAddStaffInfoTitle: '您的订阅已达到员工人数上限。请升级您的计划以添加更多员工。',
	CaregiverListCreateButton: '新团队成员',
	CaregiverListEmptyState: '未添加护理人员',
	CaregiversListItemRemoveStaff: '移除员工',
	CarepatronApp: 'Carepatron 应用程序',
	CarepatronCommunity: '社区',
	CarepatronFieldAddress: '地址',
	CarepatronFieldAssignedStaff: '指定工作人员',
	CarepatronFieldBirthDate: '出生日期',
	CarepatronFieldEmail: '电子邮件',
	CarepatronFieldEmploymentStatus: '就业状况',
	CarepatronFieldEthnicity: '种族',
	CarepatronFieldFirstName: '名',
	CarepatronFieldGender: '性别',
	CarepatronFieldIdentificationNumber: '识别号',
	CarepatronFieldIsArchived: '地位',
	CarepatronFieldLabel: '标签',
	CarepatronFieldLastName: '姓',
	CarepatronFieldLivingArrangements: '生活安排',
	CarepatronFieldMiddleNames: '中间名字',
	CarepatronFieldOccupation: '职业',
	CarepatronFieldPhoneNumber: '电话号码',
	CarepatronFieldRelationshipStatus: '感情状态',
	CarepatronFieldStatus: '地位',
	CarepatronFieldStatusHelperText: '最多 10 个状态。',
	CarepatronFieldTags: '标签',
	CarepatronFields: 'Carepatron 领域',
	Cash: '现金',
	Category: '类别',
	CategoryInputPlaceholder: '选择模板类别',
	CenterAlign: '居中对齐',
	Central: '中央',
	ChangeLayout: '更改布局',
	ChangeLogo: '改变',
	ChangePassword: '更改密码',
	ChangePasswordFailureSnackbar: '抱歉，您的密码未更改。请检查您的旧密码是否正确。',
	ChangePasswordHelperInfo: '最小长度为 {minLength}',
	ChangePasswordSuccessfulSnackbar: '密码更改成功！下次登录时，请务必使用该密码。',
	ChangeSubscription: '更改订阅',
	ChangesNotAllowed: '无法更改此字段',
	ChargesDisabled: '已禁用收费',
	ChargesEnabled: '已启用收费',
	ChargesStatus: '收费状态',
	ChartAndDiagram: '图表/图示',
	ChartsAndDiagramsCategoryDescription: '用于展示客户数据和进度',
	ChatEditMessage: '编辑消息',
	ChatReplyTo: '回复 {name}',
	ChatTypeMessageTo: '消息 {name}',
	Check: '查看',
	CheckList: '检查清单',
	Chef: '厨师',
	Chiropractic: '脊椎按摩疗法',
	Chiropractor: '脊椎按摩师',
	Chiropractors: '脊椎按摩师',
	ChooseACollection: '选择一个收藏',
	ChooseAContact: '选择联系人',
	ChooseAccountTypeHeader: '哪一个最能描述您？',
	ChooseAction: '选择操作',
	ChooseAnAccount: '选择一个帐户',
	ChooseAnOption: '选择一个选项',
	ChooseBillingProfile: '选择计费配置文件',
	ChooseClaim: '选择索赔',
	ChooseCollection: '选择收藏',
	ChooseColor: '选择颜色',
	ChooseCustomDate: '选择自定义日期',
	ChooseDateAndTime: '选择日期和时间',
	ChooseDxCodes: '选择诊断代码',
	ChooseEventType: '选择活动类型',
	ChooseFileButton: '选择文件',
	ChooseFolder: '选择文件夹',
	ChooseInbox: '选择收件箱',
	ChooseMethod: '选择方法',
	ChooseNewOwner: '选择新主人',
	ChooseOrganization: '选择组织',
	ChoosePassword: '选择密码',
	ChoosePayer: '选择付款人',
	ChoosePaymentMethod: '选择付款方式',
	ChoosePhysicalOrRemoteLocations: '输入或选择位置',
	ChoosePlan: '选择 {plan}',
	ChooseProfessional: '选择专业',
	ChooseServices: '选择服务',
	ChooseSource: '选择来源',
	ChooseSourceDescription: '选择您要导入客户的来源 - 是文件还是其他软件平台。',
	ChooseTags: '选择标签',
	ChooseTaxName: '选择税名',
	ChooseTeamMembers: '选择团队成员',
	ChooseTheme: '选择主题',
	ChooseTrigger: '选择触发器',
	ChooseYourProvider: '选择您的提供商',
	CircularProgressWithLabel: '{value}%',
	City: '城市',
	CivilEngineer: '土木工程师',
	Claim: '宣称',
	ClaimAddReferringProvider: '添加转诊医生',
	ClaimAddRenderingProvider: '添加渲染提供程序',
	ClaimAmount: '索赔金额',
	ClaimAmountPaidHelpContent:
		'已付金额是从患者或其他付款人处收到的付款。请输入患者和/或其他付款人就承保服务支付的总金额。',
	ClaimAmountPaidHelpSubtitle: '字段 29',
	ClaimAmountPaidHelpTitle: '已付金额',
	ClaimBillingProfileTypeIndividual: '个人',
	ClaimBillingProfileTypeOrganisation: '组织',
	ClaimChooseRenderingProviderOrTeamMember: '选择渲染提供商或团队成员',
	ClaimClientInsurancePolicies: '客户保险政策',
	ClaimCreatedAction: '<mark>索赔 {claimNumber}</mark> 已创建',
	ClaimDeniedAction: '<mark>索赔 {claimNumber}</mark> 被 <b>{payerNumber} {payerName}</b> 拒绝了。',
	ClaimDiagnosisCodeSelectorPlaceholder: '搜索 ICD 10 诊断代码',
	ClaimDiagnosisSelectorHelpContent: `“诊断或损伤”是患者与索赔中的服务有关的体征、症状、投诉或状况。
最多可以选择 12 个 ICD 10 诊断代码。`,
	ClaimDiagnosisSelectorHelpSubtitle: '字段 21',
	ClaimDiagnosisSelectorHelpTitle: '诊断或损伤',
	ClaimDiagnosticCodesEmptyError: '至少需要一个诊断代码',
	ClaimDoIncludeReferrerInformation: '请在 CMS1500 上包含引荐来源信息',
	ClaimERAReceivedAction: '收到了来自 <b>{payerNumber} {payerName}</b> 的电子汇款',
	ClaimElectronicPaymentAction:
		'电子汇款已收到	<mark>付款 {paymentReference}</mark>  来自 <b>{payerNumber} {payerName}</b> 的 <b>{paymentAmount}</b> 已记录',
	ClaimExportedAction: '<mark>索赔 {claimNumber}</mark> 已导出为 <b>{attachmentType}</b>',
	ClaimFieldClient: '客户或联系人姓名',
	ClaimFieldClientAddress: '客户端地址',
	ClaimFieldClientAddressDescription:
		'输入客户的地址。第一行是街道地址。地址中不要使用标点符号（逗号或句号）或任何符号。如果报告国外地址，请联系付款人获取具体报告说明。',
	ClaimFieldClientAddressSubtitle: '字段 5',
	ClaimFieldClientDateOfBirth: '客户的出生日期',
	ClaimFieldClientDateOfBirthAndSexSubtitle: '字段 3',
	ClaimFieldClientDateOfBirthDescription:
		'输入客户的 8 位数出生日期 (MM/DD/YYYY)。客户的出生日期是可以识别客户的信息，可以区分姓名相似的人员。',
	ClaimFieldClientDescription: '“客户姓名”是接受治疗或供给的人员的姓名。',
	ClaimFieldClientSexDescription: '“性别”是可以识别客户的信息，可以区分姓名相似的人。',
	ClaimFieldClientSubtitle: '字段 2',
	ClaimFiling: '索赔申报',
	ClaimHistorySubtitle: '保险 • 索赔 {number}',
	ClaimIncidentAutoAccident: '车祸？',
	ClaimIncidentConditionRelatedTo: '客户的状况是否与',
	ClaimIncidentConditionRelatedToHelpContent:
		'此信息表明客户的疾病或受伤是否与工作、车祸或其他事故有关。工作（当前或以前的）表明该状况与客户的工作或工作场所有关。车祸表明该状况是车祸导致的。其他事故表明该状况是任何其他类型事故导致的。',
	ClaimIncidentConditionRelatedToHelpSubtitle: '字段 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: '客户的状况是否与',
	ClaimIncidentCurrentIllness: '当前疾病、受伤或怀孕',
	ClaimIncidentCurrentIllnessHelpContent:
		'当前患病、受伤或怀孕的日期是指首次发病的日期、实际受伤的日期或怀孕的末次月经日期。',
	ClaimIncidentCurrentIllnessHelpSubtitle: '字段 14',
	ClaimIncidentCurrentIllnessHelpTitle: '当前患病、受伤或怀孕的日期 (LMP)',
	ClaimIncidentDate: '日期',
	ClaimIncidentDateFrom: '日期',
	ClaimIncidentDateTo: '日期至',
	ClaimIncidentEmploymentRelated: '就业',
	ClaimIncidentEmploymentRelatedDesc: '（当前或之前）',
	ClaimIncidentHospitalizationDatesLabel: '与当前服务相关的住院日期',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'与当前服务相关的住院日期是指客户住院，并注明与索赔服务相关的入院和出院日期。',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: '字段 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: '与当前服务相关的住院日期',
	ClaimIncidentInformation: '事件信息',
	ClaimIncidentOtherAccident: '其他事故？',
	ClaimIncidentOtherAssociatedDate: '其他相关日期',
	ClaimIncidentOtherAssociatedDateHelpContent: '另一个日期标识了有关客户状况或治疗的附加日期信息。',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: '字段 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: '其他日期',
	ClaimIncidentQualifier: '限定符',
	ClaimIncidentQualifierPlaceholder: '选择限定词',
	ClaimIncidentUnableToWorkDatesLabel: '客户无法从事当前职业',
	ClaimIncidentUnableToWorkDatesLabelHelpContent: '客户在当前职业中无法工作的日期是客户现在或过去无法工作的时间跨度',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: '字段 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: '客户无法从事当前职业的日期',
	ClaimIncludeReferrerInformation: '在 CMS1500 上包含引荐来源信息',
	ClaimInsuranceCoverageTypeHelpContent: `适用于此索赔的健康保险类型。其他表示包括 HMO、商业保险、汽车事故、责任或工伤赔偿在内的健康保险。
此信息将索赔引导至正确的计划，并可能确定主要责任。`,
	ClaimInsuranceCoverageTypeHelpSubtitle: '字段 1',
	ClaimInsuranceCoverageTypeHelpTitle: '覆盖类型',
	ClaimInsuranceGroupIdHelpContent: `输入被保险人的医疗保健身份证上显示的保单号码或团体号码。

 “受保人的保单、团体或 FECA 号码”是健康、汽车或其他保险计划承保范围的字母数字标识符。FECA 号码是分配给申请工作相关疾病的患者的一个 9 位字母数字标识符。`,
	ClaimInsuranceGroupIdHelpSubtitle: '字段 11',
	ClaimInsuranceGroupIdHelpTitle: '被保险人的保单、团体或 FECA 号码',
	ClaimInsuranceMemberIdHelpContent: `输入提交索赔的付款人的被保险人的身份证号码，该号码与被保险人的身份证号码一致。
如果患者具有付款人指定的唯一会员识别号，则在此字段中输入该号码。`,
	ClaimInsuranceMemberIdHelpSubtitle: '字段 1a',
	ClaimInsuranceMemberIdHelpTitle: '受保人的会员 ID',
	ClaimInsurancePayer: '保险付款人',
	ClaimManualPaymentAction: '<mark>付款 {paymentReference}</mark> 计入 <b>{paymentAmount}</b>',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: '附加索偿信息',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'请参阅公共或私有支付方关于使用此字段的当前说明。当可用时，报告与所输入信息相符的限定符。不要在限定符和信息之间输入空格、连字符或其他分隔符。',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: '字段 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: '额外索赔信息',
	ClaimMiscClaimCodes: '索取代码',
	ClaimMiscOriginalReferenceNumber: '原始参考编号',
	ClaimMiscPatientsAccountNumber: '患者账号',
	ClaimMiscPatientsAccountNumberHelpContent: '患者的账号是医疗服务提供者指定的标识符。',
	ClaimMiscPatientsAccountNumberHelpSubtitle: '字段 26',
	ClaimMiscPatientsAccountNumberHelpTitle: '患者账号',
	ClaimMiscPriorAuthorizationNumber: '事先授权号码',
	ClaimMiscPriorAuthorizationNumberHelpContent: '事先授权号码是付款人指定的授权服务的号码。',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: '字段 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: '事先授权号码',
	ClaimMiscResubmissionCode: '重新提交代码',
	ClaimMiscResubmissionCodeHelpContent:
		'重新提交是指目的地付款人或收款人指定的代码和原始参考编号，以指示先前提交的索赔或遭遇。',
	ClaimMiscResubmissionCodeHelpSubtitle: '字段 22',
	ClaimMiscResubmissionCodeHelpTitle: '重新提交和/或原始参考编号',
	ClaimNumber: '索赔编号',
	ClaimNumberFormat: '索赔 #{number}',
	ClaimOrderingProvider: '订购提供商',
	ClaimOtherId: '其他身份证',
	ClaimOtherIdPlaceholder: '选择一个选项',
	ClaimOtherIdQualifier: '其他 ID 限定符',
	ClaimOtherIdQualifierPlaceholder: '选择 ID 限定符',
	ClaimPlaceOfService: '服务地点',
	ClaimPlaceOfServicePlaceholder: '添加 POS',
	ClaimPolicyHolderRelationship: '保单持有人关系',
	ClaimPolicyInformation: '政策信息',
	ClaimPolicyTelephone: '电话（包括区号）',
	ClaimReceivedAction: '<mark>索赔 {claimNumber}</mark> 由 <b>{name}</b> 接收',
	ClaimReferringProvider: '引荐提供者',
	ClaimReferringProviderEmpty: '未添加转诊提供者',
	ClaimReferringProviderHelpContent:
		'输入的名称是推荐、订购或监督索赔中的服务或供应的推荐提供商、订购提供商或监督提供商。限定词表示被报告的提供商的角色。',
	ClaimReferringProviderHelpSubtitle: '字段 17',
	ClaimReferringProviderHelpTitle: '转介提供者或来源的名称',
	ClaimReferringProviderQualifier: '限定符',
	ClaimReferringProviderQualifierPlaceholder: '选择限定词',
	ClaimRejectedAction: '<mark>索赔 {claimNumber}</mark> 被 <b>{name}</b> 拒绝了',
	ClaimRenderingProviderIdNumber: '身份证号码',
	ClaimRenderingProviderOrTeamMember: '渲染提供商或团队成员',
	ClaimRestoredAction: '<mark>索赔 {claimNumber}</mark> 已恢复',
	ClaimServiceFacility: '服务设施',
	ClaimServiceFacilityLocationHelpContent: '提供服务的设施的名称和地址标识了提供服务的地点。',
	ClaimServiceFacilityLocationHelpLabel: '32、32a 和 32b',
	ClaimServiceFacilityLocationHelpSubtitle: '字段 32、32a 和 32b',
	ClaimServiceFacilityLocationHelpTitle: '服务设施',
	ClaimServiceFacilityPlaceholder: '选择服务设施或地点',
	ClaimServiceLabChargesHelpContent: `当索取由计费提供商以外的实体提供的购买服务时，请填写此字段。
每项购买的服务都必须在单独的索赔中报告，因为 CMS1500 表格上只能输入一项费用。`,
	ClaimServiceLabChargesHelpSubtitle: '字段 20',
	ClaimServiceLabChargesHelpTitle: '外部实验室费用',
	ClaimServiceLineServiceHelpContent: '“程序、服务或供应”指明了向患者提供的医疗服务和程序。',
	ClaimServiceLineServiceHelpSubtitle: '字段 24d',
	ClaimServiceLineServiceHelpTitle: '程序、服务或供应',
	ClaimServiceLinesEmptyError: '至少需要一个服务线',
	ClaimServiceSupplementaryInfoHelpContent: `使用适用的限定词添加所提供服务的额外叙述性描述。
请勿在限定符和信息之间输入空格、连字符或其他分隔符。

有关添加补充信息的完整说明，请查看 CMS 1500 索赔表格说明。`,
	ClaimServiceSupplementaryInfoHelpSubtitle: '字段 24',
	ClaimServiceSupplementaryInfoHelpTitle: '补充信息',
	ClaimSettingsBillingMethodTitle: '客户计费方式',
	ClaimSettingsClientSignatureDescription: '我同意发布处理保险索赔所需的医疗或其他信息。',
	ClaimSettingsClientSignatureTitle: '客户在文件上签名',
	ClaimSettingsConsentLabel: '处理保险索赔所需的同意：',
	ClaimSettingsDescription: '选择客户计费方式以确保顺利处理付款：',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} 有一个有效的保险单。要启用保险计费，请将客户账单方式更新为保险。',
	ClaimSettingsInsuranceDescription: '保险报销的费用',
	ClaimSettingsInsuranceTitle: '保险',
	ClaimSettingsNoPoliciesAlertDescription: '添加保险单以实现保险索赔。',
	ClaimSettingsPolicyHolderSignatureDescription: '我同意接受所提供服务的保险赔偿。',
	ClaimSettingsPolicyHolderSignatureTitle: '保单持有人签名',
	ClaimSettingsSelfPayDescription: '客户将支付预约费用',
	ClaimSettingsSelfPayTitle: '自付',
	ClaimSettingsTitle: '声明设置',
	ClaimSexSelectorPlaceholder: '男 / 女',
	ClaimStatusChangedAction: '<mark>索赔 {claimNumber}</mark> 状态已更新',
	ClaimSubmittedAction:
		'<mark>索赔 {claimNumber}</mark> 已提交至 <b>{payerClearingHouse}</b>，供 <b>{payerNumber} {payerName}</b> 处理。',
	ClaimSubtitle: '索赔 #{claimNumber}',
	ClaimSupervisingProvider: '监督提供者',
	ClaimSupplementaryInfo: '补充信息',
	ClaimSupplementaryInfoPlaceholder: '添加补充信息',
	ClaimTrashedAction: '<mark>索赔 {claimNumber}</mark> 已被删除',
	ClaimValidationFailure: '验证索赔失败',
	ClaimsEmptyStateDescription: '未发现任何索赔。',
	ClainInsuranceTelephone: '保险电话（含区号）',
	Classic: '经典的',
	Clear: '清除',
	ClearAll: '全部清除',
	ClearSearchFilter: '清除',
	ClearingHouse: '清算所',
	ClearingHouseClaimId: 'Claim.MD编号',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: '房屋结算参考',
	ClearingHouseUnavailableError: '结算中心当前不可用。请稍后再试。',
	ClickToUpload: '点击上传',
	Client: '客户',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} 添加了 {noteTitle, select, undefined { 一个笔记 } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: '选择客户及其关系',
	ClientAndRelationshipSelectorTitle: '所有客户及其关系',
	ClientAndRelationshipSelectorTitle1: '所有与 ‘{name}’ 相关的内容',
	ClientAppCallsPageNoOptionsText:
		'如果您正在等待视频通话，它将很快出现在这里。如果您遇到任何问题，请联系发起通话的人。',
	ClientAppSubHeaderMyDocumentation: '我的文档',
	ClientAppointment: '客户预约',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} 已预约 {appointmentName}',
	ClientAppointmentsEmptyStateDescription: '未找到任何预约',
	ClientAppointmentsEmptyStateTitle: '跟踪客户即将到来的和历史预约以及他们的出勤情况',
	ClientArchivedSuccessfulSnackbar: '成功归档 **{name}**',
	ClientBalance: '客户余额',
	ClientBilling: '计费',
	ClientBillingAddPaymentMethodDescription: '添加和管理客户的付款方式，以简化他们的发票和账单流程。',
	ClientBillingAndPaymentDueDate: '到期日',
	ClientBillingAndPaymentHistory: '账单和付款历史记录',
	ClientBillingAndPaymentInvoices: '发票',
	ClientBillingAndPaymentIssueDate: '签发日期',
	ClientBillingAndPaymentPrice: '价格',
	ClientBillingAndPaymentReceipt: '收据',
	ClientBillingAndPaymentServices: '服务',
	ClientBillingAndPaymentStatus: '地位',
	ClientBulkStaffAssignedSuccessSnackbar: '团队已分配 {count, plural, one {成员} other {成员}}！',
	ClientBulkStaffUnassignedSuccessSnackbar: '团队 {count, plural, one {成员} other {成员}} 未分配！',
	ClientBulkTagsAddedSuccessSnackbar: '标签已添加！',
	ClientDuplicatesDeviewDescription: '将多个客户记录合并为一个，以统一所有数据（笔记、文档、约会、发票和对话）。',
	ClientDuplicatesPageMergeHeader: '选择您想要保留的数据',
	ClientDuplicatesReviewHeader: '比较潜在的重复记录以进行合并',
	ClientEmailChangeWarningDescription:
		'更新客户的电子邮件将删除他们对任何共享文档的访问权限，并将向具有新电子邮件的用户授予访问权限',
	ClientFieldDateDescription: '格式化日期',
	ClientFieldDateLabel: '日期',
	ClientFieldDateRangeDescription: '日期范围',
	ClientFieldDateRangeLabel: '日期范围',
	ClientFieldDateShowDateDescription: '例如 29 年',
	ClientFieldDateShowDateRangeDescription: '例如 2 周',
	ClientFieldEmailDescription: '电子邮件',
	ClientFieldEmailLabel: '电子邮件',
	ClientFieldLabel: '字段标签',
	ClientFieldLinearScaleDescription: '比例选项 1-10',
	ClientFieldLinearScaleLabel: '线性刻度',
	ClientFieldLocationDescription: '实际地址或邮寄地址',
	ClientFieldLocationLabel: '地点',
	ClientFieldLongTextDescription: '长文本区域',
	ClientFieldLongTextLabel: '段落',
	ClientFieldMultipleChoiceDropdownDescription: '从列表中选择多个选项',
	ClientFieldMultipleChoiceDropdownLabel: '多选下拉列表',
	ClientFieldPhoneNumberDescription: '电话号码',
	ClientFieldPhoneNumberLabel: '电话',
	ClientFieldPlaceholder: '选择客户端字段类型',
	ClientFieldSingleChoiceDropdownDescription: '从列表中仅选择一个选项',
	ClientFieldSingleChoiceDropdownLabel: '单选下拉列表',
	ClientFieldTextDescription: '文本输入字段',
	ClientFieldTextLabel: '文本',
	ClientFieldYesOrNoDescription: '从是或否选项中选择',
	ClientFieldYesOrNoLabel: '是 | 否',
	ClientFileFormAccessLevelDescription: '您和团队始终可以访问您上传的文件。您可以选择与客户和/或他们的关系共享此文件',
	ClientFileSavedSuccessSnackbar: '文件已保存！',
	ClientFilesPageEmptyStateText: '沒有上传文件',
	ClientFilesPageUploadFileButton: '上传文件',
	ClientHeaderBilling: '计费',
	ClientHeaderBillingAndReceipts: '计费 ',
	ClientHeaderDocumentation: '文档',
	ClientHeaderDocuments: '文件',
	ClientHeaderFile: '文档',
	ClientHeaderHistory: '病史',
	ClientHeaderInbox: '收件箱',
	ClientHeaderNote: '笔记',
	ClientHeaderOverview: '概述',
	ClientHeaderProfile: '个人的',
	ClientHeaderRelationship: '关系',
	ClientHeaderRelationships: '关系',
	ClientId: '客户端 ID',
	ClientImportProcessingDescription: '文件仍在处理中。完成后我们会通知您。',
	ClientImportReadyForMappingDescription: '我们已经完成了对您的文件的预处理。您想映射列来完成此导入吗？',
	ClientImportReadyForMappingNotificationSubject:
		'Client import pre-processing is complete. The file is now ready for mapping.Client import 预处理已完成。该文件现在可以映射。',
	ClientInAppMessaging: '客户端应用内消息传递',
	ClientInfoAddField: '添加另一个字段',
	ClientInfoAddRow: '添加行',
	ClientInfoAlertMessage: '此部分填写的任何信息都将填充客户记录。',
	ClientInfoFormPrimaryText: '客户信息',
	ClientInfoFormSecondaryText: '收集联系方式',
	ClientInfoPlaceholder: `客户姓名、电子邮件地址、电话号码
实际地址，
出生日期`,
	ClientInformation: '客户信息',
	ClientInsuranceTabLabel: '保险',
	ClientIntakeFormsNotSupported: `表单模板目前不受客户端支持。
创建并共享它们作为客户笔记。`,
	ClientIntakeModalDescription:
		'我们将向您的客户发送一封接收电子邮件，要求他们填写个人资料、上传相关医疗或转诊文件。客户将获得客户门户访问权限。',
	ClientIntakeModalTitle: '将摄入发送到 {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: '成功！您的摄入量已保存。',
	ClientIntakeSuccessSnackbar: '成功！您的摄入量已保存，确认电子邮件已发送。',
	ClientIsChargedProcessingFee: '您的客户将支付手续费',
	ClientListCreateButton: '新客户',
	ClientListEmptyState: '未添加任何客户',
	ClientListPageItemArchive: '移除客户端',
	ClientListPageItemRemoveAccess: '删除我的访问权限',
	ClientLocalizationPanelDescription: '客户的首选语言和时区。',
	ClientLocalizationPanelTitle: '语言和时区',
	ClientManagementAndEHR: '客户管理 ',
	ClientMergeResultSummaryBanner:
		'合并记录会整合所有客户数据，包括笔记、文件、预约、发票和对话。请在继续之前验证准确性。',
	ClientMergeResultSummaryTitle: '合并结果摘要',
	ClientModalTitle: '新客户',
	ClientMustHaveEmaillAccessErrorText: '没有电子邮件的客户/联系人',
	ClientMustHavePortalAccessErrorText: '客户/联系人需要注册',
	ClientMustHaveZoomAppConnectedErrorText: '通过“设置”>“已连接的应用程序”连接 Zoom',
	ClientNameFormat: '客户端名称格式',
	ClientNotFormAccessLevel: '可查看者：',
	ClientNotFormAccessLevelDescription: '您和团队始终可以访问您发布的笔记。您可以选择与客户和/或他们的关系分享此笔记',
	ClientNotRegistered: '未注册',
	ClientNoteFormAddFileButton: '附加文件',
	ClientNoteFormChooseAClient: '选择客户/联系人以继续',
	ClientNoteFormContent: '内容',
	ClientNoteItemDeleteConfirmationModalDescription: '一旦删除，您将无法再次检索此注释。',
	ClientNotePublishedAndLockSuccessSnackbar: '注释已发布并锁定。',
	ClientNotePublishedSuccessSnackbar: '注释已发布！',
	ClientNotes: '客户备注',
	ClientNotesEmptyStateText: '要添加注释，请转到客户的个人资料并单击“注释”选项卡。',
	ClientOnboardingChoosePasswordTitle1: '快完成了！',
	ClientOnboardingChoosePasswordTitle2: '选择密码',
	ClientOnboardingCompleteIntake: '完全摄入',
	ClientOnboardingConfirmationScreenText:
		'您已提供 {providerName} 所需的所有信息。	请确认您的电子邮件地址以开始入职。如果您没有立即收到，请检查您的垃圾邮件文件夹。',
	ClientOnboardingConfirmationScreenTitle: '太棒了！查看您的收件箱。',
	ClientOnboardingDashboardButton: '转至仪表板',
	ClientOnboardingHealthRecordsDesc1: '您想与 {providerName} 共享任何推荐信或文件吗？',
	ClientOnboardingHealthRecordsDescription: '添加描述（可选）',
	ClientOnboardingHealthRecordsTitle: '文档',
	ClientOnboardingPasswordRequirements: '要求',
	ClientOnboardingPasswordRequirementsConditions1: '至少需要 9 个字符',
	ClientOnboardingProviderIntroSignupButton: '为自己报名',
	ClientOnboardingProviderIntroSignupFamilyButton: '为家庭成员报名',
	ClientOnboardingProviderIntroTitle: '{name} 已邀请您加入他们的 Carepatron 平台',
	ClientOnboardingRegistrationInstructions: '在下面输入您的个人信息。',
	ClientOnboardingRegistrationTitle: '首先我们需要一些个人信息',
	ClientOnboardingStepFormsAndAgreements: '表格和协议',
	ClientOnboardingStepFormsAndAgreementsDesc1: '请完成以下表格，以进行 {providerName} 的评估流程。',
	ClientOnboardingStepHealthDetails: '健康详情',
	ClientOnboardingStepPassword: '密码',
	ClientOnboardingStepYourDetails: '您的详细信息',
	ClientPaymentMethodDescription: '保存您个人资料中的付款方式，使您的下一次预约和开具发票更快、更安全。',
	ClientPortal: '客户端门户',
	ClientPortalDashboardEmptyDescription: '您的预约历史和出勤情况将显示在这里。',
	ClientPortalDashboardEmptyTitle: '跟踪所有即将到来的、已请求的和过去的预约以及您的出勤情况',
	ClientPreferredNotificationPanelDescription: '通过以下方式管理您的客户接收更新和通知的首选方法：',
	ClientPreferredNotificationPanelTitle: '首选通知方法',
	ClientProcessingFee: '付款包含 ({currencyCode}) {amount} 处理费',
	ClientProfileAddress: '地址',
	ClientProfileDOB: '出生日期',
	ClientProfileEmailHelperText: '添加电子邮件即可授予门户访问权限',
	ClientProfileEmailHelperTextMoreInfo: '授予客户门户访问权限可让团队成员共享笔记、文件和其他文档',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: '识别号',
	ClientRelationshipsAddClientOwnerButton: '邀请客户',
	ClientRelationshipsAddFamilyButton: '邀请家庭成员',
	ClientRelationshipsAddStaffButton: '添加员工访问权限',
	ClientRelationshipsEmptyStateText: '未添加任何关系',
	ClientRemovedSuccessSnackbar: '客户端已成功删除。',
	ClientResponsibility: '客户责任',
	ClientSavedSuccessSnackbar: '客户端已成功保存。',
	ClientTableClientName: '客户名称',
	ClientTablePhone: '电话',
	ClientTableStatus: '地位',
	ClientUnarchivedSuccessfulSnackbar: '已成功取消归档 **{name}**',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} 编辑了 {noteTitle, select, undefined { 一篇笔记 } other {{noteTitle}}}',
	ClientView: '客户端视图',
	Clients: '客户',
	ClientsTable: '客户表',
	ClinicalFormat: '临床形式',
	ClinicalPsychologist: '临床心理学家',
	Close: '关闭',
	CloseImportClientsModal: '您确实要取消导入客户吗？',
	CloseReactions: '密切反应',
	Closed: '关闭',
	Coaching: '辅导',
	Code: '代码',
	CodeErrorMessage: '需要代码',
	CodePlaceholder: '代码',
	Coinsurance: '共同保险',
	Collection: '收藏',
	CollectionName: '集合名称',
	Collections: '收藏',
	ColorAppointmentsBy: '色彩预约',
	ColorTheme: '颜色主题',
	ColourCalendarBy: '彩色日历',
	ComingSoon: '即将推出',
	Community: '社区',
	CommunityHealthLead: '社区健康主管',
	CommunityHealthWorker: '社区卫生工作者',
	CommunityTemplatesSectionDescription: '由 Carepatron 社区创建',
	CommunityTemplatesSectionTitle: '社区',
	CommunityUser: '社区用户',
	Complete: '完全的',
	CompleteAndLock: '完成并锁定',
	CompleteSetup: '完成设置',
	CompleteSetupSuccessDescription: '您已经完成了一些掌握 Carepatron 的关键步骤。',
	CompleteSetupSuccessDescription2: '解锁更多方式，帮助您优化您的实践并支持您的客户。',
	CompleteSetupSuccessTitle: '成功！你做得太棒了！',
	CompleteStripeSetup: '完成 Stripe 设置',
	Completed: '已完成',
	ComposeSms: '撰写短信',
	ComputerSystemsAnalyst: '计算机系统分析师',
	Confirm: '确认',
	ConfirmDeleteAccountDescription: '您即将删除您的帐户。此操作无法撤消。如果您希望继续，请在下面确认。',
	ConfirmDeleteActionDescription: '您确定要删除此操作吗？此操作无法撤消',
	ConfirmDeleteAutomationDescription: '您确定要删除此自动化吗？此操作无法撤消。',
	ConfirmDeleteScheduleDescription:
		'删除 **{scheduleName}** 时刻表将从您的时刻表中移除，并可能更改您的在线服务可用性。此操作无法撤消。',
	ConfirmDraftResponseContinue: '继续响应',
	ConfirmDraftResponseDescription: '如果您关闭此页面，您的回复将保留为草稿。您可以随时返回并继续。',
	ConfirmDraftResponseSubmitResponse: '提交回复',
	ConfirmDraftResponseTitle: '您的回复尚未提交',
	ConfirmIfUserIsClientDescription: `您填写的注册表是针对提供者（即健康团队/组织）的。
如果这是一个错误，您可以选择“继续作为客户”，我们将为您设置客户门户`,
	ConfirmIfUserIsClientNoButton: '注册成为提供商',
	ConfirmIfUserIsClientTitle: '看起来你是客户',
	ConfirmIfUserIsClientYesButton: '继续作为客户',
	ConfirmKeepSeparate: '确认保持分开',
	ConfirmMerge: '确认合并',
	ConfirmPassword: '确认密码',
	ConfirmRevertClaim: '是的，恢复状态',
	ConfirmSignupAccessCode: '验证码',
	ConfirmSignupButtom: '确认',
	ConfirmSignupDescription: '请输入您的电子邮件地址和我们刚刚发送给您的确认码。',
	ConfirmSignupSubTitle: '检查垃圾邮件文件夹 - 如果电子邮件尚未到达',
	ConfirmSignupSuccessSnackbar: '太好了，我们已确认您的帐户！现在您可以使用您的电子邮件和密码登录',
	ConfirmSignupTitle: '确认账户',
	ConfirmSignupUsername: '电子邮件',
	ConfirmSubscriptionUpdate: '确认订阅 {price} {isMonthly, select, true {每月} other {每年}}',
	ConfirmationModalBulkDeleteClientsDescriptionId: '一旦删除客户，您将无法再访问他们的信息。',
	ConfirmationModalBulkDeleteClientsTitleId: '删除 {count, plural, one {# 个客户} other {# 个客户}}？',
	ConfirmationModalBulkDeleteContactsDescriptionId: '一旦删除联系人，您将无法再访问他们的信息。',
	ConfirmationModalBulkDeleteContactsTitleId: '删除{count, plural, one {# 个联系} other {# 个联系人}}？',
	ConfirmationModalBulkDeleteMembersDescriptionId: '这是永久性操作。一旦删除团队成员，您将无法再访问他们的信息。',
	ConfirmationModalBulkDeleteMembersTitleId: '删除 {count, plural, one {# 个团队成员} other {# 个团队成员}}？',
	ConfirmationModalCloseOnGoingTranscription: '关闭此注释将结束所有正在进行的转录。您确定要继续吗？',
	ConfirmationModalDeleteClientField: '这是永久性操作。一旦删除该字段，您的剩余客户端将无法再访问它。',
	ConfirmationModalDeleteSectionMessage: '一旦删除，此部分的所有问题都将被移除。此操作无法撤消。',
	ConfirmationModalDeleteService: '这是永久性操作。一旦删除该服务，您将无法再在工作区中访问它。',
	ConfirmationModalDeleteServiceGroup: '删除集合将从组中删除所有服务并返回到您的服务列表。此操作无法撤消。',
	ConfirmationModalDeleteTranscript: '您确实要删除成绩单吗？',
	ConfirmationModalDescriptionDeleteClient: '一旦客户被删除，您将无法再访问客户信息。',
	ConfirmationModalDescriptionRemoveMyAccessFromClient: '一旦您删除访问权限，您将无法再查看客户信息。',
	ConfirmationModalDescriptionRemoveRelationshipToClient: '他们的个人资料不会被删除，只是作为该客户的关系而被删除。',
	ConfirmationModalDescriptionRemoveStaff: '您确实要从提供商中删除此人吗？',
	ConfirmationModalEndSession: '您确实要结束会话吗？',
	ConfirmationModalTitle: '你确定吗？',
	Confirmed: '确认的',
	ConflictTimezoneWarningMessage: '由于多个时区可能会发生冲突',
	Connect: '连接',
	ConnectExistingClientOrContact: '创建新客户/联系人',
	ConnectInboxGoogleDescription: '添加 Gmail 帐户或 Google 群组列表',
	ConnectInboxMicrosoftDescription: '添加 Outlook、Office365 或 Exchange 帐户',
	ConnectInboxModalDescription: '连接您的应用程序以便在一个集中位置无缝发送、接收和跟踪您的所有通信。',
	ConnectInboxModalExistingDescription: '使用连接的应用程序设置中的现有连接来简化配置过程。',
	ConnectInboxModalExistingTitle: 'Carepatron 中现有的连接应用程序',
	ConnectInboxModalTitle: '连接收件箱',
	ConnectToStripe: '连接到 Stripe',
	ConnectZoom: '连接 Zoom',
	ConnectZoomModalDescription: '允许 Carepatron 管理您的预约视频通话。',
	ConnectedAppDisconnectedNotificationSubject: '我们已断开与 {account} 帐户的连接。请重新连接。',
	ConnectedAppSyncDescription: '管理连接的应用程序以直接从 Carepatron 在第三方日历中创建事件。',
	ConnectedApps: '连接的应用程序',
	ConnectedAppsGMailDescription: '添加 Gmail 帐户或 Google 群组列表',
	ConnectedAppsGoogleCalendarDescription: '添加日历帐户或 Google 群组列表',
	ConnectedAppsGoogleDescription: '添加您的 Gmail 帐户并同步 Google 日历',
	ConnectedAppsMicrosoftDescription: '添加 Outlook、Office365 或 Exchange 帐户',
	ConnectedCalendars: '连接日历',
	ConsentDocumentation: '表格和协议',
	ConsentDocumentationPublicTemplateError: '出于安全原因，您只能选择来自您的团队的模板（非公开）。',
	ConstructionWorker: '建筑工人',
	Consultant: '顾问',
	Contact: '接触',
	ContactAccessTypeHelperText: '允许家庭管理员更新信息',
	ContactAccessTypeHelperTextMoreInfo: '这将允许您分享关于 {clientFirstName} 的笔记/文档。',
	ContactAddressLabelBilling: '计费',
	ContactAddressLabelHome: '家',
	ContactAddressLabelOthers: '其他的',
	ContactAddressLabelWork: '工作',
	ContactChangeConfirmation: '更改发票联系人将删除与 <mark>{contactName}</mark> 相关的所有行项目。',
	ContactDetails: '联系方式',
	ContactEmailLabelOthers: '其他的',
	ContactEmailLabelPersonal: '个人的',
	ContactEmailLabelSchool: '学校',
	ContactEmailLabelWork: '工作',
	ContactInformation: '联系信息',
	ContactInformationText: '联系信息',
	ContactListCreateButton: '新联系方式',
	ContactName: '联系人姓名',
	ContactPhoneLabelHome: '家',
	ContactPhoneLabelMobile: '移动的',
	ContactPhoneLabelSchool: '学校',
	ContactPhoneLabelWork: '工作',
	ContactRelationship: '联系关系',
	ContactRelationshipFormAccessType: '授予共享信息的访问权限',
	ContactRelationshipGrantAccessInfo: '这将允许你共享笔记 ',
	ContactSupport: '联系支持人员',
	Contacts: '联系方式',
	ContainerIdNotSet: '未设置容器 ID',
	Contemporary: '当代的',
	Continue: '继续',
	ContinueDictating: '继续口述',
	ContinueEditing: '继续编辑',
	ContinueImport: '继续导入',
	ContinueTranscription: '继续转录',
	ContinueWithApple: '继续使用 Apple',
	ContinueWithGoogle: '继续使用 Google',
	Conversation: '对话',
	Copay: '共付额',
	CopayOrCoinsurance: '共付额或共同保险',
	Copayment: '共付额',
	CopiedToClipboard: '已复制到剪贴板',
	Copy: '复制',
	CopyAddressSuccessSnackbar: '已复制地址到剪贴板',
	CopyCode: '复制代码',
	CopyCodeToClipboardSuccess: '代码已复制到剪贴板',
	CopyEmailAddressSuccessSnackbar: '已复制电子邮件地址到剪贴板',
	CopyLink: '复制链接',
	CopyLinkForCall: '复制此链接即可分享此通话：',
	CopyLinkSuccessSnackbar: '已将链接复制到剪贴板',
	CopyMeetingLink: '复制会议链接',
	CopyPaymentLink: '复制付款链接',
	CopyPhoneNumberSuccessSnackbar: '已将电话号码复制到剪贴板',
	CopyTemplateLink: '复制模板链接',
	CopyTemplateLinkSuccess: '已将链接复制到剪贴板',
	CopyToClipboardError: '无法复制到剪贴板。请重试。',
	CopyToTeamTemplates: '复制到团队模板',
	CopyToWorkspace: '复制到工作区',
	Cosmetologist: '美容师',
	Cost: '成本',
	CostErrorMessage: '需支付费用',
	Counseling: '咨询',
	Counselor: '顾问',
	Counselors: '辅导员',
	CountInvoicesAdded: '{count, plural, one {# 添加了 1 张发票} other {# 添加了 {count} 张发票}}',
	CountNotesAdded: '{count, plural, one {# 注意已添加} other {# 注意已添加}}',
	CountSelected: '{count} 已选中',
	CountTimes: '{count} 次',
	Country: '国家',
	Cousin: '表哥',
	CoverageType: '覆盖类型',
	Covered: '覆盖',
	Create: '创造',
	CreateANewClient: '创建新客户端',
	CreateAccount: '创建账户',
	CreateAndSignNotes: '创建并与客户签署说明',
	CreateAvailabilityScheduleFailure: '无法创建新的可用性计划',
	CreateAvailabilityScheduleSuccess: '成功创建新的可用性计划',
	CreateBillingItems: '创建计费项目',
	CreateCallFormButton: '开始通话',
	CreateCallFormInviteOnly: '仅限邀请',
	CreateCallFormInviteOnlyMoreInfo:
		'只有受邀参加此通话的人才能加入。要与其他人共享此通话，只需取消选中此选项并在下一页复制/粘贴链接即可',
	CreateCallFormRecipients: '收件人',
	CreateCallFormRegion: '主办地区',
	CreateCallModalAddClientContactSelectorLabel: '客户联系方式',
	CreateCallModalAddClientContactSelectorPlaceholder: '按客户名称搜索',
	CreateCallModalAddStaffSelectorLabel: '团队成员（可选）',
	CreateCallModalAddStaffSelectorPlaceholder: '按员工姓名搜索',
	CreateCallModalDescription:
		'开始通话并邀请工作人员和/或联系人。或者，您可以取消选中“私人”框，以使此通话可与任何使用 Carepatron 的人共享',
	CreateCallModalTitle: '开始通话',
	CreateCallModalTitleLabel: '标题（可选）',
	CreateCallNoPersonIdToolTip: '只有具有门户访问权限的联系人/客户才能加入通话',
	CreateClaim: '创建索赔',
	CreateClaimCompletedMessage: '您的索赔已创建。',
	CreateClientModalTitle: '新客户',
	CreateContactModalTitle: '新联系方式',
	CreateContactRelationshipButton: '添加关系',
	CreateContactSelectorDefaultOption: '  创建联系人',
	CreateContactWithRelationshipFormAccessType: '授予共享信息的访问权限 ',
	CreateDocumentDnDPrompt: '拖放即可上传文件',
	CreateDocumentSizeLimit: '每个文件大小限制为 {size}MB。总共 {total} 个文件。',
	CreateFreeAccount: '创建免费账户',
	CreateInvoice: '创建发票',
	CreateLink: '创建链接',
	CreateNew: '创建新的',
	CreateNewAppointment: '创建新约会',
	CreateNewClaim: '创建新申索',
	CreateNewClaimForAClient: '为客户创建新索赔。',
	CreateNewClient: '创建新客户',
	CreateNewConnection: '新连接',
	CreateNewContact: '创建新联系人',
	CreateNewField: '创建新字段',
	CreateNewLocation: '新地点',
	CreateNewService: '创建新服务',
	CreateNewServiceGroupFailure: '无法创建新的收藏集',
	CreateNewServiceGroupMenu: '新品',
	CreateNewServiceGroupSuccess: '成功创建新收藏集',
	CreateNewServiceMenu: '新服务',
	CreateNewTeamMember: '创建新的团队成员',
	CreateNewTemplate: '新模板',
	CreateNote: '创建笔记',
	CreateSuperbillReceipt: '新超级钞票',
	CreateSuperbillReceiptSuccess: '成功创建超级账单收据',
	CreateTemplateFolderSuccessMessage: '成功创建 {folderTitle}',
	Created: '创建',
	CreatedAt: '创建于 {timestamp}',
	Credit: '信用',
	CreditAdded: '信用已申请',
	CreditAdjustment: '信用调整',
	CreditAdjustmentReasonHelperText: '这是内部说明，您的客户看不到。',
	CreditAdjustmentReasonPlaceholder: '添加调整原因有助于审查计费交易',
	CreditAmount: '{amount} 信',
	CreditBalance: '贷方余额',
	CreditCard: '信用卡',
	CreditCardExpire: '有效期至 {exp_month}/{exp_year}',
	CreditCardNumber: '信用卡号码',
	CreditDebitCard: '卡片',
	CreditIssued: '已发放信用证',
	CreditsUsed: '已使用积分',
	Crop: '庄稼',
	Currency: '货币',
	CurrentCredit: '当前信用',
	CurrentEventTime: '当前活动时间',
	CurrentPlan: '当前计划',
	Custom: '风俗',
	CustomRange: '定制范围',
	CustomRate: '自定义费率',
	CustomRecurrence: '自定义重复',
	CustomServiceAvailability: '服务可用性',
	CustomerBalance: '客户余额',
	CustomerName: '客户名称',
	CustomerNameIsRequired: '必填项为客户姓名',
	CustomerServiceRepresentative: '客户服务代表',
	CustomiseAppointments: '自定义约会',
	CustomiseBookingLink: '自定义预订选项',
	CustomiseBookingLinkServicesInfo: '客户只能选择可预订的服务',
	CustomiseBookingLinkServicesLabel: '服务',
	CustomiseClientRecordsAndWorkspace: '自定义您的客户记录和工作区',
	CustomiseClientSettings: '自定义客户端设置',
	Customize: '定制',
	CustomizeAppearance: '自定义外观',
	CustomizeAppearanceDesc: '自定义您的在线预订外观以匹配您的品牌并优化向客户展示服务的方式。',
	CustomizeClientFields: '自定义客户字段',
	CustomizeInvoiceTemplate: '自定义发票模板',
	CustomizeInvoiceTemplateDescription: '轻松创建反映您品牌的专业发票。',
	DXCodePlaceholder: '1.',
	DXErrorMessage: '需要 DX',
	Daily: '日常的',
	DanceTherapist: '舞蹈治疗师',
	DangerZone: '危险区域',
	Dashboard: '仪表板',
	Date: '日期',
	DateAndTime: '日期 ',
	DateDue: '到期日期',
	DateErrorMessage: '日期为必填项',
	DateFormPrimaryText: '日期',
	DateFormSecondaryText: '从日期选择器中选择',
	DateIssued: '发行日期',
	DateOfPayment: '付款日期',
	DateOfService: '服务日期',
	DateOverride: '日期覆盖',
	DateOverrideColor: '日期覆盖颜色',
	DateOverrideInfo: '日期覆盖允许从业者通过覆盖常规时间表来手动调整特定日期的可用性。',
	DateOverrideInfoBanner: '在这些时间段内，仅可预订此日期覆盖的指定服务；不允许其他在线预订。',
	DateOverrides: '日期覆盖',
	DatePickerFormPrimaryText: '日期',
	DatePickerFormSecondaryText: '选择日期',
	DateRange: '日期范围',
	DateRangeFormPrimaryText: '日期范围',
	DateRangeFormSecondaryText: '选择日期范围',
	DateReceived: '收到日期',
	DateSpecificHours: '特定日期时间',
	DateSpecificHoursDescription: '当您的可用时间与预定时间有变化时添加日期，或在特定日期提供服务。',
	DateUploaded: '上传于 {date, date, medium}',
	Dates: '日期',
	Daughter: '女儿',
	Day: '天',
	DayPlural: '{count, plural, one {天} other {天}}',
	Days: '天',
	DaysPlural: '{age, plural, one {# 天} other {# 天}}',
	DeFacto: '事实上',
	Deactivated: '已停用',
	Debit: '借方',
	DecreaseIndent: '减少缩进',
	Deductibles: '免赔额',
	Default: '默认',
	DefaultBillingProfile: '默认计费配置文件',
	DefaultDescription: '默认描述',
	DefaultEndOfLine: '没有更多商品',
	DefaultInPerson: '客户预约',
	DefaultInvoiceTitle: '默认标题',
	DefaultNotificationSubject: '您收到了一个新的 {notificationType} 通知',
	DefaultPaymentMethod: '默认付款方式',
	DefaultService: '默认服务',
	DefaultValue: '默认',
	DefaultVideo: '客户视频预约邮件',
	DefinedTemplateType: '{invoiceTemplate} 模板',
	Delete: '删除',
	DeleteAccountButton: '删除帐户',
	DeleteAccountDescription: '从平台上删除您的账户',
	DeleteAccountPanelInfoAlert:
		'您必须先删除工作区，然后才能删除您的个人资料。要继续，请切换到工作区并选择“设置”>“工作区设置”。',
	DeleteAccountTitle: '删除帐户',
	DeleteAppointment: '删除预约',
	DeleteAppointmentDescription: '您确定要删除此约会吗？您可以稍后恢复。',
	DeleteAvailabilityScheduleFailure: '无法删除可用时间表',
	DeleteAvailabilityScheduleSuccess: '已成功删除可用性计划',
	DeleteBillable: '删除计费',
	DeleteBillableConfirmationMessage: '您确定要删除此计费项目吗？此操作无法撤消。',
	DeleteBillingProfileConfirmationMessage: '这将永久删除该帐单资料。',
	DeleteCardConfirmation: '这是永久性操作。一旦删除该卡，您将无法再访问它。',
	DeleteCategory: '删除类别（除非保存更改，否则这不是永久性的）',
	DeleteClientEventConfirmationDescription: '这将被永久删除。',
	DeleteClients: '删除客户端',
	DeleteCollection: '删除收藏夹',
	DeleteColumn: '删除列',
	DeleteConversationConfirmationDescription: '永久删除此对话。此操作无法撤消。',
	DeleteConversationConfirmationTitle: '永久删除对话',
	DeleteExternalEventDescription: '您确定要删除此预约吗？',
	DeleteFileConfirmationModalPrompt: '一旦删除，您将无法再次检索此文件。',
	DeleteFolder: '删除文件夹',
	DeleteFolderConfirmationMessage:
		'您确定要删除此文件夹 {name} 吗？此文件夹内的所有项目也将被删除。您可以在稍后恢复它。',
	DeleteForever: '永久删除',
	DeleteInsurancePayerConfirmationMessage:
		'删除 {payer} 将会把它从您的保险支付方列表中删除。此操作是永久性的，无法恢复。',
	DeleteInsurancePayerFailure: '删除保险付款人失败',
	DeleteInsurancePolicyConfirmationMessage: '这将永久取消保险单。',
	DeleteInvoiceConfirmationDescription: '此操作无法撤消。它将永久删除发票和与其相关的所有付款。',
	DeleteLocationConfirmation: '删除位置是永久性操作。删除后，您将无法再访问它。此操作无法撤消。',
	DeletePayer: '删除付款人',
	DeletePracticeWorkspace: '删除练习工作区',
	DeletePracticeWorkspaceDescription: '永久删除此练习工作区',
	DeletePracticeWorkspaceFailedSnackbar: '删除工作区失败',
	DeletePracticeWorkspaceModalCancelButton: '是的，取消我的订阅',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription: '在继续删除工作区之前，您必须先取消订阅。',
	DeletePracticeWorkspaceModalConfirmButton: '是的，永久删除工作区',
	DeletePracticeWorkspaceModalDescription:
		'{name} 工作区将被永久删除，所有团队成员将失去访问权限。请在删除之前下载您可能需要的任何重要数据或消息。此操作无法撤消。',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: '做出此决定的原因是：',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: '原因',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: '请告诉我们您想要删除帐户的原因。',
	DeletePracticeWorkspaceModalTitle: '你确定吗？',
	DeletePracticeWorkspaceSuccessSnackbarDescription: '所有团队成员的访问权限已被删除',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} 已成功删除',
	DeletePublicTemplateContent: '这只会删除公共模板，而不会删除您团队的模板。',
	DeleteRecurringAppointmentModalTitle: '删除重复约会',
	DeleteRecurringEventModalTitle: '删除重复会议',
	DeleteRecurringReminderModalTitle: '删除重复提醒',
	DeleteRecurringTaskModalTitle: '删除重复任务',
	DeleteReminderConfirmation: '这是永久性操作。一旦删除提醒，您将无法再访问它。只会影响新的约会',
	DeleteSection: '删除部分',
	DeleteSectionInfo: '删除部分 **{section}** 将隐藏其内所有现有字段。此操作无法撤消。',
	DeleteSectionWarning: '核心字段无法删除，并将移至现有部分 **{section}**。',
	DeleteServiceFailure: '删除服务失败',
	DeleteServiceSuccess: '成功删除服务',
	DeleteStaffScheduleOverrideDescription:
		'删除 {value} 上的日期覆盖将从您的日程中删除它，并可能改变您在线服务的可用性。此操作无法撤销。',
	DeleteSuperbillConfirmationDescription: '此操作无法撤消。它将永久删除 Superbill 收据。',
	DeleteSuperbillFailure: '无法删除超级账单收据',
	DeleteSuperbillSuccess: '已成功删除 Superbill 收据',
	DeleteTaxRateConfirmationDescription: '您确实要删除该税率吗？',
	DeleteTemplateContent: '此操作无法撤消',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} 已成功删除',
	DeleteTemplateSuccessMessage: '{templateTitle} 已成功删除',
	DeleteTemplateTitle: '您确实要删除该模板吗？',
	DeleteTranscript: '删除记录',
	DeleteWorkspace: '删除工作区',
	Deleted: '已删除',
	DeletedBy: '删除者',
	DeletedContact: '删除联系人',
	DeletedOn: '删除日期',
	DeletedStatusLabel: '已删除状态',
	DeletedUserTooltip: '该客户端已被删除',
	DeliveryMethod: '送货方式',
	Demo: '演示',
	Denied: '被拒',
	Dental: '牙科',
	DentalAssistant: '牙科助理',
	DentalHygienist: '牙齿卫生员',
	Dentist: '牙医',
	Dentists: '牙医',
	Description: '描述',
	DescriptionMustNotExceed: '描述不得超过 {max} 个字符',
	DetailDurationWithStaff: '{duration} 分钟{staffName, select, null {} other { 与 {staffName}}}',
	Details: '细节',
	Devices: '设备',
	Diagnosis: '诊断',
	DiagnosisAndBillingItems: '诊断 ',
	DiagnosisCode: '诊断代码',
	DiagnosisCodeErrorMessage: '需要诊断代码',
	DiagnosisCodeSelectorPlaceholder: '从ICD-10诊断代码中搜索并添加',
	DiagnosisCodeSelectorTooltip: '诊断代码用于自动生成保险报销的超级账单收据',
	DiagnosticCodes: '诊断代码',
	Dictate: '听写',
	DictatingIn: '口述',
	Dictation: '听写',
	DidNotAttend: '没有参加',
	DidNotComplete: '未完成',
	DidNotProviderEnoughValue: '没有提供足够的价值',
	DidntProvideEnoughValue: '没有提供足够的价值',
	DieteticsOrNutrition: '营养学或营养学',
	Dietician: '营养师',
	Dieticians: '营养师',
	Dietitian: '营养师',
	DigitalSign: '在此签名：',
	DigitalSignHelp: '（单击/按下即可绘制）',
	DirectDebit: '直接付款',
	DirectTextLink: '直接文本链接',
	Disable: '禁用',
	DisabledEmailInfo: '我们无法更新您的电子邮件地址，因为您的帐户不受我们管理',
	Discard: '丢弃',
	DiscardChanges: '放弃更改',
	DiscardDrafts: '丢弃草稿',
	Disconnect: '断开',
	DisconnectAppConfirmation: '您要断开此应用程序吗？',
	DisconnectAppConfirmationDescription: '您确定要断开此应用程序的连接吗？',
	DisconnectAppConfirmationTitle: '断开应用程序',
	Discount: '折扣',
	DisplayCalendar: '在 Carepatron 中显示',
	DisplayName: '显示名称',
	DisplayedToClients: '展示给客户',
	DiversionalTherapist: '娱乐治疗师',
	DoItLater: '稍后再做',
	DoNotImport: '不导入',
	DoNotSend: '不发送',
	DoThisLater: '稍后做这件事',
	DoYouWantToEndSession: '您想继续还是立即结束会话？',
	Doctor: '医生',
	Doctors: '医生',
	DoesNotRepeat: '不重复',
	DoesntWorkWellWithExistingTools: '与我们现有的工具或工作流程不太兼容',
	DogWalker: '遛狗者',
	Done: '完成',
	DontAllowClientsToCancel: '不允许客户取消',
	DontHaveAccount: '沒有帳戶？',
	DontSend: '不要发送',
	Double: '双倍的',
	DowngradeTo: '降级到 {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'抱歉，您无法降级您的计划，因为您的团队成员太多。请从您的提供商中删除一些成员，然后重试。',
	Download: '下载',
	DownloadAsPdf: '下载为 PDF',
	DownloadERA: '下载 ERA',
	DownloadPDF: '下载 PDF',
	DownloadTemplateFileName: 'Carepatron 切换模板.csv',
	DownloadTemplateTileDescription: '使用我们的电子表格模板来组织和上传您的客户。',
	DownloadTemplateTileLabel: '下载模板',
	Downloads: '{number, plural, one {<span>#</span> 下载} other {<span>#</span> 下载}}',
	DoxyMe: 'Doxy.me',
	Draft: '草稿',
	DraftResponses: '回应草稿',
	DraftSaved: '已保存更改',
	DragAndDrop: '拖放',
	DragDropText: '拖放健康文件',
	DragToMove: '拖拽移动',
	DragToMoveOrActivate: '拖动以移动或激活',
	DramaTherapist: '戏剧治疗师',
	DropdownFormFieldPlaceHolder: '从列表中选择选项',
	DropdownFormPrimaryText: '下拉列表',
	DropdownFormSecondaryText: '从选项列表中选择',
	DropdownTextFieldError: '下拉选项文本不能为空',
	DropdownTextFieldPlaceholder: '添加下拉选项',
	Due: '到期日',
	DueDate: '到期日',
	Duplicate: '复制',
	DuplicateAvailabilityScheduleFailure: '无法复制可用性计划',
	DuplicateAvailabilityScheduleSuccess: '成功复制了 {name} 的日程表',
	DuplicateClientBannerAction: '审查',
	DuplicateClientBannerDescription: '合并重复的客户记录将它们合并为一个，并保留所有唯一的客户信息。',
	DuplicateClientBannerTitle: '找到 {count} 个重复项',
	DuplicateColumn: '重复列',
	DuplicateContactFieldSettingErrorSnackbar: '不能有重复的节名称',
	DuplicateContactFieldSettingFieldErrorSnackbar: '不能有重复的字段名称',
	DuplicateEmailError: '重复电子邮件',
	DuplicateHeadingName: '部分 {name} 已经存在',
	DuplicateInvoiceNumberErrorCodeSnackbar: '具有相同“发票号”的发票已存在。',
	DuplicateRecords: '重复记录',
	DuplicateRecordsMinimumError: '必须至少选择 2 条记录',
	DuplicateRecordsRequired: '至少选择 1 条记录进行分隔',
	DuplicateServiceFailure: '无法复制 **{title}**',
	DuplicateServiceSuccess: '成功复制 **{title}**',
	DuplicateTemplateFolderSuccessMessage: '成功复制文件夹',
	DuplicateTemplateSuccess: '成功复制模板',
	DurationInMinutes: '{duration}分钟',
	Dx: '远程控制',
	DxCode: 'DX 代码',
	DxCodeSelectPlaceholder: '从 ICD-10 代码搜索和添加',
	EIN: '艾因',
	EMG: '肌电图',
	EPSDT: '电力供应中断',
	EPSDTPlaceholder: '没有任何',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>包含调整</i>} other {}}',
	EarnReferralCredit: '赚取 ${creditAmount}',
	Economist: '经济学家',
	Edit: '编辑',
	EditArrangements: '编辑安排',
	EditBillTo: '编辑账单至',
	EditClient: '編輯客戶',
	EditClientFileModalDescription: '通过选择“可查看者”复选框中的选项来编辑对此文件的访问权限',
	EditClientFileModalTitle: '编辑文件',
	EditClientNoteModalDescription: '编辑笔记中的内容。使用“可查看者”部分来更改谁可以查看该笔记。',
	EditClientNoteModalTitle: '編輯識',
	EditConnectedAppButton: '编辑',
	EditConnections: '编辑连接{account, select, null { } undefined { } other { 为 {account}}}',
	EditContactDetails: '编辑联系方式',
	EditContactFormIsClientLabel: '转换为客户端',
	EditContactIsClientCheckboxWarning: '将联系人转换为客户的操作无法撤销',
	EditContactIsClientWanringModal:
		'将此联系人转换为客户的操作无法撤销。但是，所有关系仍将保留，您现在可以访问他们的笔记、文件和其他文档。',
	EditContactRelationship: '编辑联系关系',
	EditDetails: '编辑详细信息',
	EditFileModalTitle: '编辑 {name} 的文件',
	EditFolder: '编辑文件夹',
	EditFolderDescription: '将文件夹重命名为...',
	EditInvoice: '编辑发票',
	EditInvoiceDetails: '编辑发票详细信息',
	EditLink: '編輯鏈接',
	EditLocation: '编辑位置',
	EditLocationFailure: '无法更新位置',
	EditLocationSucess: '已成功更新位置',
	EditPaymentDetails: '编辑付款详情',
	EditPaymentMethod: '编辑付款方式',
	EditPersonalDetails: '编辑个人信息',
	EditPractitioner: '編輯执业者',
	EditProvider: '編輯提供商',
	EditProviderDetails: '编辑提供商详细信息',
	EditRecurrence: '编辑重复',
	EditRecurringAppointmentModalTitle: '编辑重复预约',
	EditRecurringEventModalTitle: '编辑重复会议',
	EditRecurringReminderModalTitle: '编辑重复提醒',
	EditRecurringTaskModalTitle: '编辑重复任务',
	EditRelationshipModalTitle: '编辑关系',
	EditService: '编辑服务',
	EditServiceFailure: '无法更新新服务',
	EditServiceGroup: '編輯集',
	EditServiceGroupFailure: '无法更新收藏',
	EditServiceGroupSuccess: '已成功更新合集',
	EditServiceSuccess: '成功更新新服务',
	EditStaffDetails: '编辑员工详细信息',
	EditStaffDetailsCantUpdatedEmailTooltip: '无法更新电子邮件地址。请使用新的电子邮件地址创建新的团队成员。',
	EditSubscriptionBilledQuantity: '计费数量',
	EditSubscriptionBilledQuantityValue: '{billedUsers} 团队成员',
	EditSubscriptionLimitedTimeOffer: '限时优惠！6 个月享 50% 折扣。',
	EditSubscriptionUpgradeAdjustTeamBanner: '当您添加或移除团队成员时，您的订阅费用将进行调整。',
	EditSubscriptionUpgradeContent:
		'您的帐户将立即更新到新的计划和计费周期。任何价格变化将自动从您的已保存的付款方式中扣除或记入您的帐户。',
	EditSubscriptionUpgradePlanTitle: '升级订阅计划',
	EditSuperbillReceipt: '编辑超级账单',
	EditTags: '编辑标签',
	EditTemplate: '编辑模板',
	EditTemplateFolderSuccessMessage: '模板文件夹已成功编辑',
	EditValue: '编辑 {value}',
	Edited: '已编辑',
	Editor: '编辑',
	EditorAlertDescription: '检测到不支持的格式。请重新加载应用程序或联系我们的支持团队。',
	EditorAlertTitle: '我们无法显示此内容',
	EditorPlaceholder: '开始写作，选择一个模板或添加基本块来获取客户的答案。',
	EditorTemplatePlaceholder: '开始编写或添加组件来构建模板',
	EditorTemplateWithSlashCommandPlaceholder:
		'开始写作或添加基本模块来捕捉客户的回复。使用斜杠命令 (/) 进行快速操作。',
	EditorWithSlashCommandPlaceholder:
		'开始写作，选择模板或添加基本块以捕获客户响应。使用斜线命令 ( / ) 进行快速操作。',
	EffectiveStartEndDate: '有效开始 - 结束日期',
	ElectricalEngineer: '电气工程师',
	Electronic: '电子',
	ElectronicSignature: '电子签名',
	ElementarySchoolTeacher: '小学教师',
	Eligibility: '资格',
	Email: '电子邮件',
	EmailAlreadyExists: '电子邮件地址已存在',
	EmailAndSms: '电子邮件 ',
	EmailBody: '电子邮件正文',
	EmailContainsIgnoredDescription: '以下电子邮件包含当前被忽略的发件人电子邮件。要继续吗？',
	EmailInviteToPortalBody: `您好 {contactName}，
请点击以下链接登录您的安全客户门户，轻松管理您的护理。

此致，

{providerName}`,
	EmailInviteToPortalSubject: '欢迎使用 {providerName}',
	EmailInvoice: '电子邮件发票',
	EmailInvoiceOverdueBody: `您好 {contactName}
您的发票 {invoiceNumber} 已过期。
请使用以下链接在线支付您的发票。

如有任何问题，请告知我们。

谢谢，
{providerName}`,
	EmailInvoicePaidBody: `您好 {contactName}
您的发票 {invoiceNumber} 已付款。
请点击以下链接查看和下载您的发票副本。

如果您有任何问题，请告知我们。

谢谢，
{providerName}`,
	EmailInvoiceProcessingBody: `您好 {contactName}
您的发票 {invoiceNumber} 已准备好。
请点击下方链接查看您的发票。

如果您有任何问题，请告知我们。

谢谢，
{providerName}`,
	EmailInvoiceUnpaidBody: `您好 {contactName}
您的发票 {invoiceNumber} 已准备好，请在 {dueDate} 前支付。
要在线查看和支付您的发票，请点击以下链接。

如果您有任何问题，请告知我们。

感谢，
{providerName}`,
	EmailInvoiceVoidBody: `您好 {contactName}
您的发票 {invoiceNumber} 已作废。
请点击以下链接查看此发票。

如有任何疑问，请告知我们。

感谢，
{providerName}`,
	EmailNotFound: '未找到电子邮件',
	EmailNotVerifiedErrorCodeSnackbar: '无法执行操作。您需要验证您的电子邮件地址。',
	EmailNotVerifiedTitle: '您的电子邮件尚未验证。部分功能将受到限制。',
	EmailSendClientIntakeBody: `您好 {contactName}，
{providerName} 希望您提供一些信息并查看重要文件。请点击以下链接开始。

此致，

{providerName}`,
	EmailSendClientIntakeSubject: '欢迎来到 {providerName}',
	EmailSuperbillReceipt: '电子邮件超级账单',
	EmailSuperbillReceiptBody: `您好 {contactName}，
{providerName} 已为您发送一份报销收据 {date} 的副本。

您可以直接下载并将其提交给您的保险公司。`,
	EmailSuperbillReceiptFailure: '无法发送超级账单收据',
	EmailSuperbillReceiptSubject: '{providerName} 已发送报销收据',
	EmailSuperbillReceiptSuccess: '成功发送 Superbill 收据',
	EmailVerificationDescription: '我们正在<span>验证</span>你的帐户',
	EmailVerificationNotification: '已向 {email} 发送验证邮件。',
	EmailVerificationSuccess: '您的电子邮件地址已成功更改为 {email}',
	Emails: '电子邮件',
	EmergencyContact: '紧急联系方式',
	EmployeesIdentificationNumber: '员工身份证号码',
	EmploymentStatus: '就业状况',
	EmptyAgendaViewDescription: '没有可显示的事件。<mark>立即创建预约</mark>',
	EmptyBin: '清空垃圾桶',
	EmptyBinConfirmationDescription: '清空垃圾箱将删除已删除中的所有**{total} 条对话**。此操作不可撤消。',
	EmptyBinConfirmationTitle: '永久删除对话',
	EmptyTrash: '清空垃圾',
	Enable: '使能够',
	EnableCustomServiceAvailability: '实现服务可用性',
	EnableCustomServiceAvailabilityDescription: '例如，初次预约只能在每天上午 9 点至 10 点进行',
	EndCall: '结束通话',
	EndCallConfirmationForCreator: '您将为所有人结束此通话，因为您是通话的发起者。',
	EndCallConfirmationHasActiveAttendees: '您即将结束通话，但客户已加入。您也想加入吗？',
	EndCallForAll: '结束所有人的通话',
	EndDate: '结束日期',
	EndDictation: '结束听写',
	EndOfLine: '没有更多预约',
	EndSession: '结束会话',
	EndTranscription: '结束转录',
	Ends: '结束',
	EndsOnDate: '结束于 {date}',
	Enrol: '报名',
	EnrollmentRejectedSubject: '您在 {payerName} 的注册已被拒绝。',
	Enrolment: '摄入量',
	Enrolments: '报名',
	EnrolmentsDescription: '查看并管理与支付方合作的提供者注册信息。',
	EnterAName: '输入名称...',
	EnterFieldLabel: '输入字段标签...',
	EnterPaymentDetailsDescription: '添加或删除用户时，您的订阅费用将自动调整。',
	EnterSectionName: '输入部分名称...',
	EnterSubscriptionPaymentDetails: '输入付款详细信息',
	EnvironmentalScientist: '环境科学家',
	Epidemiologist: '流行病学家',
	Eraser: '橡皮',
	Error: '错误',
	ErrorBoundaryAction: '刷新页面',
	ErrorBoundaryDescription: '请刷新页面并重试。',
	ErrorBoundaryTitle: '哎呀！出了点问题',
	ErrorCallNotFound: '找不到该通话。该通话可能已过期或创建者已结束通话。',
	ErrorCannotAccessCallUninvitedCode: '抱歉，看来您尚未被邀请参加此次电话会议。',
	ErrorFileUploadCustomMaxFileCount: '一次最多只能上传 {count} 个文件。',
	ErrorFileUploadCustomMaxFileSize: '文件大小不能超过 {mb} MB',
	ErrorFileUploadInvalidFileType: '无效的文件类型可能包含潜在病毒和有害软件',
	ErrorFileUploadMaxFileCount: '一次无法上传超过 150 个文件',
	ErrorFileUploadMaxFileSize: '文件大小不能超过 100 MB',
	ErrorFileUploadNoFileSelected: '请选择要上传的文件',
	ErrorInvalidNationalProviderId: '提供的国家医疗服务提供者 ID 无效',
	ErrorInvalidPayerId: '提供的付款人 ID 无效',
	ErrorInvalidTaxNumber: '提供的税号无效',
	ErrorInviteExistingProviderStaffCode: '此用户已在工作区中。',
	ErrorInviteStaffExistingUser: '抱歉，您添加的用户似乎已经存在于我们的系统中。',
	ErrorOnlySingleCallAllowed: '您每次只能拨打一个电话。请结束当前通话以开始新的通话。',
	ErrorPayerNotFound: '未找到付款人',
	ErrorProfilePhotoMaxFileSize: '上传失败！文件大小已达上限 - 5MB',
	ErrorRegisteredExistingUser: '抱歉，您似乎已经注册。',
	ErrorUserSignInIncorrectCredentials: '邮箱或密码无效。请重试。',
	ErrorUserSigninGeneric: '抱歉，出了点问题。',
	ErrorUserSigninUserNotConfirmed: '抱歉，您需要在登录之前确认您的帐户。请查看您的收件箱以获取说明。',
	Errors: '错误',
	EssentialPlanInclusionFive: '模板导入',
	EssentialPlanInclusionFour: '5 GB 存储空间',
	EssentialPlanInclusionHeader: '一切都免费  ',
	EssentialPlanInclusionOne: '自动和自定义提醒',
	EssentialPlanInclusionSix: '优先支持',
	EssentialPlanInclusionThree: '视频聊天',
	EssentialPlanInclusionTwo: '双向日历同步',
	EssentialSubscriptionPlanSubtitle: '利用基本要素简化你的练习',
	EssentialSubscriptionPlanTitle: '基本的',
	Esthetician: '美容师',
	Estheticians: '美容师',
	EstimatedArrivalDate: '预计抵达 {numberOfDaysFromNow}',
	Ethnicity: '种族',
	Europe: '欧洲',
	EventColor: '会议颜色',
	EventName: '活动名称',
	EventType: '事件类型',
	Every: '每',
	Every2Weeks: '每两周',
	EveryoneInWorkspace: '工作区中的每个人',
	ExercisePhysiologist: '运动生理学家',
	Existing: '现有的',
	ExistingClients: '现有客户',
	ExistingFolders: '现有的文件夹',
	ExpiredPromotionCode: '促销代码已过期',
	ExpiredReferralDescription: '推荐已过期',
	ExpiredVerificationLink: '验证链接已过期',
	ExpiredVerificationLinkDescription: `很抱歉，您点击的验证链接已过期。如果您等待超过 24 小时才点击该链接，或者您已经使用该链接验证了您的电子邮件地址，则可能会发生这种情况。

请请求新的验证链接来验证您的电子邮件地址。`,
	ExpiryDateRequired: '必须注明有效期',
	ExploreFeature: '您想首先探索什么？',
	ExploreOptions: '选择一个或多个选项进行探索...',
	Export: '出口',
	ExportAppointments: '导出约会',
	ExportClaims: '导出索赔',
	ExportClaimsFilename: '索赔 {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: '由于错误，无法下载您的文件。',
	ExportClientsDownloadFailureSnackbarTitle: '下载失败',
	ExportClientsFailureSnackbarDescription: '由于错误，您的文件无法成功导出。',
	ExportClientsFailureSnackbarTitle: '导出失败',
	ExportClientsModalDescription: `根据导出的数据量，此数据导出过程可能需要几分钟。一旦数据可供下载，您将收到一封带有链接的电子邮件通知。

您是否希望继续导出客户数据？`,
	ExportClientsModalTitle: '导出客户数据',
	ExportCms1500: '导出 CMS1500',
	ExportContactFailedNotificationSubject: '您的数据导出失败',
	ExportFailed: '导出失败',
	ExportGuide: '出口指南',
	ExportInvoiceFileName: '交易 {fromDate}-{toDate}.csv',
	ExportPayments: '导出付款',
	ExportPaymentsFilename: '付款 {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: '您的文档已准备好下载。',
	ExportPrintWaitMessage: '正在准备您的文档。请稍候...',
	ExportTextOnly: '仅导出文本',
	ExportTransactions: '出口交易',
	Exporting: '出口',
	ExportingData: '导出数据',
	ExtendedFamilyMember: '大家庭成员',
	External: '外部的',
	ExternalEventInfoBanner: '此预约来自同步的日历，可能缺少一些项目。',
	ExtraLarge: '特大',
	FECABlackLung: 'FECA Black Lung',
	Failed: '失败的',
	FailedToJoinTheMeeting: '无法加入会议。',
	FallbackPageDescription: `看起来此页面不存在，您可能需要 {refreshButton} 此页面以获取最新更改。
否则，请联系 Carepatron 支持。`,
	FallbackPageDescriptionUpdateButton: '刷新',
	FallbackPageTitle: '哎呀……',
	FamilyPlanningService: '计划生育服务',
	FashionDesigner: '时装设计师',
	FastTrackInvoicingAndBilling: '快速开具发票和账单',
	Father: '父亲',
	FatherInLaw: '岳父',
	Favorite: '收藏',
	FeatureBannerCalendarTile1ActionLabel: '在线预订 • 2 分钟',
	FeatureBannerCalendarTile1Description: '只需发送电子邮件、短信或在您的网站上添加可用性',
	FeatureBannerCalendarTile1Title: '让您的客户能够在线预订',
	FeatureBannerCalendarTile2ActionLabel: '自动提醒 • 2 分钟',
	FeatureBannerCalendarTile2Description: '通过自动提醒提高客户出席率',
	FeatureBannerCalendarTile2Title: '减少缺席情况',
	FeatureBannerCalendarTile3Title: '调度和工作流程',
	FeatureBannerCalendarTitle: '让调度变得简单',
	FeatureBannerCallsTile1ActionLabel: '开始远程医疗呼叫',
	FeatureBannerCallsTile1Description: '客户端只需一个链接即可访问。无需登录、密码或麻烦',
	FeatureBannerCallsTile1Title: '随时随地开始视频通话',
	FeatureBannerCallsTile2ActionLabel: '连接应用程序 • 4 分钟',
	FeatureBannerCallsTile2Description: '无缝连接其他首选远程医疗提供商',
	FeatureBannerCallsTile2Title: '连接您的远程医疗应用',
	FeatureBannerCallsTile3Title: '呼叫',
	FeatureBannerCallsTitle: '随时随地与客户联系',
	FeatureBannerClientsTile1ActionLabel: '立即导入 • 2 分钟',
	FeatureBannerClientsTile1Description: '使用我们的自动客户导入工具快速开始',
	FeatureBannerClientsTile1Title: '有很多客户吗？',
	FeatureBannerClientsTile2ActionLabel: '自定义摄入量 • 2 分钟',
	FeatureBannerClientsTile2Description: '取消入职文书工作并改善客户体验',
	FeatureBannerClientsTile2Title: '无纸化',
	FeatureBannerClientsTile3Title: '客户端门户',
	FeatureBannerClientsTitle: '一切从您的客户开始',
	FeatureBannerHeader: '由社区组成，服务于社区！',
	FeatureBannerInvoicesTile1ActionLabel: '自动付款 • 2 分钟',
	FeatureBannerInvoicesTile1Description: '避免使用自动付款进行尴尬的对话',
	FeatureBannerInvoicesTile1Title: '付款速度提高 2 倍',
	FeatureBannerInvoicesTile2ActionLabel: '追踪现金流 • 2 分钟',
	FeatureBannerInvoicesTile2Description: '减少未付发票并密切关注您的收入',
	FeatureBannerInvoicesTile2Title: '轻松追踪您的收入',
	FeatureBannerInvoicesTile3Title: '账单和付款',
	FeatureBannerInvoicesTitle: '少一件需要担心的事',
	FeatureBannerSubheader: 'Carepatron 模板由我们的团队和社区制作。尝试新资源或分享您自己的资源！',
	FeatureBannerTeamTile1ActionLabel: '立即邀请',
	FeatureBannerTeamTile1Description: '邀请团队成员加入您的帐户，让协作变得轻松',
	FeatureBannerTeamTile1Title: '让你的团队团结起来',
	FeatureBannerTeamTile2ActionLabel: '设置可用性 • 2 分钟',
	FeatureBannerTeamTile2Description: '管理团队的可用性以避免重复预订',
	FeatureBannerTeamTile2Title: '设置您的空闲时间',
	FeatureBannerTeamTile3ActionLabel: '设置权限 • 2 分钟',
	FeatureBannerTeamTile3Description: '控制对敏感数据和工具的访问以确保合规性',
	FeatureBannerTeamTile3Title: '自定义权限和访问',
	FeatureBannerTeamTitle: '没有什么伟大的事情是单独完成的',
	FeatureBannerTemplatesTile1ActionLabel: '探索图书馆 • 2 分钟',
	FeatureBannerTemplatesTile1Description: '从令人惊叹的可定制资源库中进行选择 ',
	FeatureBannerTemplatesTile1Title: '减少工作量',
	FeatureBannerTemplatesTile2ActionLabel: '立即发送 • 2 分钟',
	FeatureBannerTemplatesTile2Description: '将精美的模板发送给客户以供完成',
	FeatureBannerTemplatesTile2Title: '让文档变得有趣',
	FeatureBannerTemplatesTile3Title: '模板',
	FeatureBannerTemplatesTitle: '适用于任何内容的模板',
	FeatureLimitBannerDescription: '立即升级，继续无缝创建和管理 {featureName}，并充分利用 Carepatron！',
	FeatureLimitBannerTitle: '你已经完成了 {percentage}% 的 {featureName} 限额',
	FeatureRequiresUpgrade: '此功能需要升级',
	Fee: '费用',
	Female: '女性',
	FieldLabelTooltip: '{isHidden, select, true {显示} other {隐藏}} 字段标签',
	FieldName: '字段名称',
	FieldOptionsFirstPart: '第一个词',
	FieldOptionsMiddlePart: '中间词',
	FieldOptionsSecondPart: '最后一句话',
	FieldOptionsWholeField: '全场',
	FieldType: '字段类型',
	Fields: '字段',
	File: '文件',
	FileDownloaded: '<strong>{fileName}</strong> 已下载',
	FileInvalidType: '文件不受支持。',
	FileNotFound: '未找到文件',
	FileNotFoundDescription: '您要查找的文件不可用或已被删除',
	FileTags: '文件标签',
	FileTagsHelper: '标签将应用于所有文件',
	FileTooLarge: '文件太大。',
	FileTooSmall: '文件太小。',
	FileUploadComplete: '完全的',
	FileUploadFailed: '失败的',
	FileUploadInProgress: '加载中',
	FileUploadedNotificationSubject: '{actorProfileName} 已上传文件',
	Files: '文件',
	FillOut: '填写',
	Filter: '筛选',
	FilterBy: '筛选条件',
	FilterByAmount: '按金额过滤',
	FilterByClient: '按客户筛选',
	FilterByLocation: '按位置过滤',
	FilterByService: '按服务筛选',
	FilterByStatus: '按状态过滤',
	FilterByTags: '按标签过滤',
	FilterByTeam: '按团队过滤',
	Filters: '筛选器',
	FiltersAppliedToView: '已应用于视图的筛选器',
	FinalAppointment: '最终任命',
	FinalizeImport: '完成导入',
	FinancialAnalyst: '财务分析师',
	Finish: '结束',
	Firefighter: '消防队员',
	FirstName: '名',
	FirstNameLastInitial: '名字，姓氏首字母',
	FirstPerson: '第一人称',
	FolderName: '文件夹名称',
	Folders: '文件夹',
	FontFamily: '字体系列',
	ForClients: '对于客户',
	ForClientsDetails: '我接受护理或健康相关服务',
	ForPractitioners: '对于从业者',
	ForPractitionersDetails: '管理和发展你的业务',
	ForgotPasswordConfirmAccessCode: '验证码',
	ForgotPasswordConfirmNewPassword: '新密码',
	ForgotPasswordConfirmPageDescription: '请输入您的电子邮件地址、新密码以及我们刚刚发送给您的确认码。',
	ForgotPasswordConfirmPageTitle: '重置密码',
	ForgotPasswordPageButton: '发送重置链接',
	ForgotPasswordPageDescription: '输入您的电子邮件，我们将向您发送重置密码的链接。',
	ForgotPasswordPageTitle: '忘记密码',
	ForgotPasswordSuccessPageDescription: '检查您的收件箱以查找重置链接。',
	ForgotPasswordSuccessPageTitle: '重置链接已发送！',
	Form: '表单',
	FormAnswersSentToEmailNotification: '我们已将您的答案副本发送至',
	FormBlocks: '表单块',
	FormFieldAddOption: '添加选项',
	FormFieldAddOtherOption: '添加“其他”',
	FormFieldOptionPlaceholder: '选项 {index}',
	FormStructures: '表单结构',
	Format: '格式',
	FormatLinkButtonColor: '按钮颜色',
	Forms: '表格',
	FormsAndAgreementsValidationMessage: '必须填写所有表格和协议才能继续接收流程。',
	FormsCategoryDescription: '用于收集和整理患者信息',
	Frankfurt: '法兰克福',
	Free: '自由的',
	FreePlanInclusionFive: '自动计费 ',
	FreePlanInclusionFour: '客户端门户',
	FreePlanInclusionHeader: '开始使用',
	FreePlanInclusionOne: '无限客户端',
	FreePlanInclusionSix: '实时支持',
	FreePlanInclusionThree: '1 GB 存储空间',
	FreePlanInclusionTwo: '远程医疗',
	FreeSubscriptionPlanSubtitle: '对所有人免费',
	FreeSubscriptionPlanTitle: '自由的',
	Friday: '星期五',
	From: '从',
	FullName: '姓名',
	FunctionalMedicineOrNaturopath: '功能医学或自然疗法',
	FuturePaymentsAuthoriseProvider: '允许 {provider} 在未来使用保存的支付方式',
	FuturePaymentsSavePaymentMethod: '保存 {paymentMethod} 以便将来付款',
	GST: '消费税',
	Gender: '性别',
	GeneralAvailability: '正式发布',
	GeneralAvailabilityDescription: '设置您通常有空的时间。客户只能在有空的时间内预订您的服务。',
	GeneralAvailabilityDescription2: '根据您的可用时间和特定时间所需的服务创建时间表，以确定您的在线预订可用性。',
	GeneralAvailabilityInfo: '您的可用时间将决定您的在线预订可用性',
	GeneralAvailabilityInfo2: '提供团体活动的服务应使用新的时间表，以减少客户可在线预订的可用时间。',
	GeneralHoursPlural: '{count} {count, plural, one {小时} other {小时}}',
	GeneralPractitioner: '全科医生',
	GeneralPractitioners: '全科医生',
	GeneralServiceAvailabilityInfo: '此时间表将覆盖指定团队成员的行为',
	Generate: '产生',
	GenerateBillingItemsBannerContent: '对于定期预约，不会自动创建账单项目。',
	GenerateItems: '生成项目',
	GenerateNote: '生成注释',
	GenerateNoteConfirmationModalDescription: '您想做什么？创建新的注释、添加到现有注释或替换其内容？',
	GenerateNoteFor: '生成注释',
	GeneratingContent: '正在生成内容...',
	GeneratingNote: '正在生成您的笔记...',
	GeneratingTranscript: '生成成绩单',
	GeneratingTranscriptDescription: '这可能需要几分钟才能处理',
	GeneratingYourTranscript: '生成成绩单',
	GenericErrorDescription: '{module} 无法加载。请稍后再试。',
	GenericErrorTitle: '发生意外错误',
	GenericFailureSnackbar: '抱歉，发生了意外情况。请刷新页面并重试。',
	GenericSavedSuccessSnackbar: '成功！更改已保存',
	GeneticCounselor: '遗传咨询师',
	Gerontologist: '老年病学家',
	Get50PercentOff: '立享五折优惠！',
	GetHelp: '获取帮助',
	GetStarted: '开始使用',
	GettingStartedAppointmentTypes: '创建预约类型',
	GettingStartedAppointmentTypesDescription: '通过自定义服务、价格和账单代码来简化您的日程安排和账单',
	GettingStartedAppointmentTypesTitle: '日程 ',
	GettingStartedClients: '添加您的客户',
	GettingStartedClientsDescription: '让您能够与客户一起进行未来的约会、记录和付款',
	GettingStartedClientsTitle: '一切从客户开始',
	GettingStartedCreateClient: '创建客户端',
	GettingStartedImportClients: '导入客户端',
	GettingStartedInvoices: '像专业人士一样开具发票',
	GettingStartedInvoicesDescription: `创建专业发票很简单。
添加您的徽标、位置和付款条款`,
	GettingStartedInvoicesTitle: '尽你最大的努力',
	GettingStartedMobileApp: '获取移动应用程序',
	GettingStartedMobileAppDescription: '您可以在 iOS、Android 或桌面设备上下载 Carepatron，以便随时随地轻松访问',
	GettingStartedMobileAppTitle: '随时随地工作',
	GettingStartedNavItem: '入门',
	GettingStartedPageTitle: '开始使用 Carepatron',
	GettingStartedPayments: '接受网上支付',
	GettingStartedPaymentsDescription: `通过让您的客户在线支付，更快地获得付款。
在一个地方查看所有发票和付款`,
	GettingStartedPaymentsTitle: '让付款变得轻而易举',
	GettingStartedSaveBranding: '保存品牌',
	GettingStartedSyncCalendars: '同步其他日历',
	GettingStartedSyncCalendarsDescription: 'Carepatron 会检查您的日历是否有冲突，因此仅在您有空时安排预约',
	GettingStartedSyncCalendarsTitle: '始终保持最新状态',
	GettingStartedVideo: '观看介绍视频',
	GettingStartedVideoDescription: '首个面向小型团队及其客户的一体化医疗保健工作空间',
	GettingStartedVideoTitle: '欢迎来到 Carepatron',
	GetttingStartedGetMobileDownload: '下载应用程序',
	GetttingStartedGetMobileNoDownload:
		'与该浏览器不兼容。如果您使用的是 iPhone 或 iPad，请在 Safari 中打开此页面。否则，请尝试在 Chrome 中打开。',
	Glossary: '术语表',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning: 'Gmail 每天仅允许从您的帐户发送 500 封邮件。部分邮件可能会发送失败。是否要继续？',
	GoToAppointment: '前往预约',
	GoToApps: '转至应用程序',
	GoToAvailability: '查看可用性',
	GoToClientList: '转至客户列表',
	GoToClientRecord: '转到客户记录',
	GoToClientSettings: '立即转到客户端设置',
	GoToInvoiceTemplates: '转到发票模板',
	GoToNotificationSettings: '转到通知设置',
	GoToPaymentSettings: '前往付款设置',
	Google: '谷歌',
	GoogleCalendar: 'Google 日历',
	GoogleColor: '谷歌日历颜色',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google 跟踪代码管理器容器 ID',
	GotIt: '知道了！',
	Goto: '转至',
	Granddaughter: '孙女',
	Grandfather: '祖父',
	Grandmother: '祖母',
	Grandparent: '祖父母',
	Grandson: '孙子',
	GrantPortalAccess: '授予门户访问权限',
	GraphicDesigner: '平面设计师',
	Grid: '网格',
	GridView: '网格视图',
	Group: '团体',
	GroupBy: '分组依据',
	GroupEvent: '团体活动',
	GroupEventHelper: '设置服务的参加者限制',
	GroupFilterLabel: '所有 {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: '组 ID',
	GroupInputFieldsFormPrimaryText: '组输入字段',
	GroupInputFieldsFormSecondaryText: '选择或添加自定义字段',
	GuideTo: '{value} 指南',
	GuideToImproveVideoQuality: '提高视频质量的指南',
	GuideToManagingPayers: '管理付款方',
	GuideToSubscriptionsBilling: '订阅计费指南',
	GuideToTroubleshooting: '故障排除指南',
	Guidelines: '指南',
	GuidelinesCategoryDescription: '用于指导临床决策',
	HST: 'HST',
	HairStylist: '发型师',
	HaveBeenWaiting: '你已经等待很久了',
	HeHim: '他/他',
	HeaderAccountSettings: '轮廓',
	HeaderCalendar: '日历',
	HeaderCalls: '呼叫',
	HeaderClientAppAccountSettings: '帐户设置',
	HeaderClientAppCalls: '呼叫',
	HeaderClientAppMyDocumentation: '文档',
	HeaderClientAppMyRelationships: '我的关系',
	HeaderClients: '客户',
	HeaderHelp: '帮助',
	HeaderMoreOptions: '更多选项',
	HeaderStaff: '职员',
	HealthCoach: '健康教练',
	HealthCoaches: '健康教练',
	HealthEducator: '健康教育者',
	HealthInformationTechnician: '健康信息技术员',
	HealthPolicyExpert: '健康政策专家',
	HealthServicesAdministrator: '健康服务管理员',
	HelpArticles: '帮助文章',
	HiddenColumns: '隐藏列',
	HiddenFields: '隐藏字段',
	HiddenSections: '隐藏部分',
	HiddenSectionsAndFields: '隐藏部分/字段',
	HideColumn: '隐藏列',
	HideColumnButton: '隐藏列 {value} 按钮',
	HideDetails: '隐藏详细信息',
	HideField: '隐藏字段',
	HideFullAddress: '隐藏',
	HideMenu: '隐藏菜单',
	HideMergeSummarySidebar: '隐藏合并摘要',
	HideSection: '隐藏部分',
	HideYourView: '隐藏视图',
	Highlight: '突出显示颜色',
	Highlighter: '荧光笔',
	History: '历史',
	HistoryItemFooter: '{actors, select, undefined {{date} 在 {time}} other {由 {actors} • {date} 在 {time}}}',
	HistorySidePanelEmptyState: '没有找到历史记录',
	HistoryTitle: '活动日志',
	HolisticHealthPractitioner: '整体健康从业者',
	HomeCaregiver: '家庭护理员',
	HomeHealthAide: '家庭健康助理',
	HomelessShelter: '无家可归者收容所',
	HourAbbreviation: '{count} {count, plural, one {小时} other {小时}}',
	Hourly: '每小时',
	HoursPlural: '{age, plural, one {# 小时} other {# 小时}}',
	HowCanWeImprove: '我们如何改进它？',
	HowCanWeImproveResponse: '我们如何改进这个回复？',
	HowDidWeDo: '我们做得怎么样？',
	HowDoesReferralWork: '推荐计划指南',
	HowToUseAiSummarise: '如何使用 AI 汇总',
	HumanResourcesManager: '人力资源经理',
	Husband: '丈夫',
	Hypnotherapist: '催眠治疗师',
	IVA: '增值税',
	IgnoreNotification: '忽略通知',
	IgnoreOnce: '忽略一次',
	IgnoreSender: '忽略发件人',
	IgnoreSenderDescription: '来自此发件人的未来对话将自动移至“其他”。您确定要忽略这些发件人吗？',
	IgnoreSenders: '忽略发件人',
	IgnoreSendersSuccess: '忽略的电子邮件地址 <mark>{addresses}</mark>',
	Ignored: '被忽略',
	Image: '图像',
	Import: '进口',
	ImportActivity: '导入活动',
	ImportClientSuccessSnackbarDescription: '您的文件已成功导入',
	ImportClientSuccessSnackbarTitle: '导入成功！',
	ImportClients: '导入客户端',
	ImportClientsFailureSnackbarDescription: '由于错误，无法成功导入您的文件。',
	ImportClientsFailureSnackbarTitle: '导入不成功！',
	ImportClientsGuide: '导入客户指南',
	ImportClientsInProgressSnackbarDescription: '这仅需一分钟即可完成。',
	ImportClientsInProgressSnackbarTitle: '导入 {fileName}',
	ImportClientsModalDescription: '选择您的数据来源 – 无论它是您设备上的文件、第三方服务还是其他软件平台。',
	ImportClientsModalFileUploadHelperText: '支持 {fileTypes}。大小限制 {fileSizeLimit}。',
	ImportClientsModalImportGuideLabel: '客户资料导入指南',
	ImportClientsModalStep1Label: '选择数据源',
	ImportClientsModalStep2Label: '上传文件',
	ImportClientsModalStep3Label: '审核字段',
	ImportClientsModalTitle: '导入您的客户数据',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {个客户} other {个客户}} 准备导入',
	ImportContactFailedNotificationSubject: '您的数据导入失败',
	ImportDataSourceSelectorLabel: '导入数据源',
	ImportDataSourceSelectorPlaceholder: '搜索或选择导入数据源',
	ImportExportButton: '导入/导出',
	ImportFailed: '导入失败',
	ImportFromAnotherPlatformTileDescription: '下载您客户文件的导出文件，并在此处上传。',
	ImportFromAnotherPlatformTileLabel: '从其他平台导入',
	ImportGuide: '导入指南',
	ImportInProgress: '导入进行中',
	ImportProcessing: '导入处理...',
	ImportSpreadsheetDescription:
		'您可以通过上传包含表格数据的电子表格文件（例如 .CSV、.XLS 或 .XLSX）将现有客户列表导入 Carepatron',
	ImportSpreadsheetTitle: '导入电子表格文件',
	ImportTemplates: '导入模板',
	Importing: '输入',
	ImportingCalendarProductEvents: '导入 {product} 事件',
	ImportingData: '导入数据',
	ImportingSpreadsheetDescription: '这最多只需要一分钟即可完成',
	ImportingSpreadsheetTitle: '导入电子表格',
	ImportsInProgress: '正在导入',
	InPersonMeeting: '面对面会议',
	InProgress: '进行中',
	InTransit: '在途中',
	InTransitTooltip: '在途余额包括 Stripe 向您的银行账户支付的所有已付款发票款项。这些资金通常需要 3-5 天才能结算。',
	Inactive: '不活跃',
	InboundOrOutboundCalls: '入站或出站呼叫',
	Inbox: '收件箱',
	InboxAccessRestricted: '访问受限。请联系收件箱所有者获取权限。',
	InboxAccountAlreadyConnected: '您尝试连接的频道已经连接到 Carepatron',
	InboxAddAttachments: '添加附件',
	InboxAreYouSureDeleteMessage: '您确实要删除此消息吗？',
	InboxBulkCloseSuccess: '{count, plural, one {成功关闭 # 个对话} other {成功关闭 # 个对话}}',
	InboxBulkComposeModalTitle: '撰写批量消息',
	InboxBulkDeleteSuccess: '{count, plural, one {成功删除了 # 条对话} other {成功删除了 # 条对话}}',
	InboxBulkReadSuccess: '{count, plural, one {已成功将 # 条对话标记为已读} other {已成功将 # 条对话标记为已读}}',
	InboxBulkReopenSuccess: '{count, plural, one {已成功重新打开 # 个对话} other {已成功重新打开 # 个对话}}',
	InboxBulkUnreadSuccess: '{count, plural, one {已成功将 # 个对话标记为未读} other {已成功将 # 个对话标记为未读}}',
	InboxChatCreateGroup: '创建群组',
	InboxChatDeleteGroupModalDescription: '您确定要删除此群组吗？所有消息和附件将被删除。',
	InboxChatDeleteGroupModalTitle: '删除组',
	InboxChatDiscardDraft: '丢弃草稿',
	InboxChatDragDropText: '将文件拖放到此处以上传',
	InboxChatGroupConversation: '群聊',
	InboxChatGroupCreateModalDescription: '开始一个新群组，与您的团队、客户或社区进行消息交流和协作。',
	InboxChatGroupCreateModalTitle: '创建群组',
	InboxChatGroupMembers: '小组成员',
	InboxChatGroupModalGroupNameFieldLabel: '组名',
	InboxChatGroupModalGroupNameFieldPlaceholder: '例如客户支持，管理员',
	InboxChatGroupModalGroupNameFieldRequired: '此字段必填',
	InboxChatGroupModalMembersFieldErrorMinimumOne: '至少需要一名成员',
	InboxChatGroupModalMembersFieldLabel: '选择组员',
	InboxChatGroupModalMembersFieldPlaceholder: '选择要添加到此组的团队成员',
	InboxChatGroupUpdateModalTitle: '管理群组',
	InboxChatLeaveGroup: '离开群组',
	InboxChatLeaveGroupModalDescription: '您确定要离开此群组吗？您将不再收到消息或更新。',
	InboxChatLeaveGroupModalTitle: '离开群组',
	InboxChatLeftGroupMessage: '左侧群聊',
	InboxChatManageGroup: '管理组',
	InboxChatSearchParticipants: '选择接收者',
	InboxCloseConversationSuccess: '成功关闭对话',
	InboxCompose: '撰写',
	InboxComposeBulk: '群发信息',
	InboxComposeCarepatronChat: '信使',
	InboxComposeChat: '撰写聊天',
	InboxComposeDisabledNoConnection: '连接电子邮件帐户以发送消息',
	InboxComposeDisabledNoPermissionTooltip: '您无权从此收件箱发送邮件',
	InboxComposeEmail: '撰写电子邮件',
	InboxComposeMessageFrom: '从',
	InboxComposeMessageRecipientBcc: '密件抄送',
	InboxComposeMessageRecipientCc: '抄送',
	InboxComposeMessageRecipientTo: '到',
	InboxComposeMessageSubject: '主题：',
	InboxConnectAccountButton: '连接您的电子邮件',
	InboxConnectedDescription: '您的收件箱未收到任何通讯',
	InboxConnectedHeading: '当你开始交流时，你的对话就会出现在这里',
	InboxConnectedHeadingClientView: '简化与客户的沟通',
	InboxCreateFirstInboxButton: '创建您的第一个收件箱',
	InboxCreationSuccess: '成功创建收件箱',
	InboxDeleteAttachment: '删除附件',
	InboxDeleteConversationSuccess: '成功删除对话',
	InboxDeleteMessage: '删除信息？',
	InboxDirectMessage: '私信',
	InboxEditDraft: '编辑草稿',
	InboxEmailComposeReplyEmail: '撰写回复',
	InboxEmailDraft: '草稿',
	InboxEmailNotFound: '未找到电子邮件',
	InboxEmailSubjectFieldInformation: '更改主题行将创建一个新的主题电子邮件。',
	InboxEmptyArchiveDescription: '未找到存档对话',
	InboxEmptyBinDescription: '未找到已删除的对话',
	InboxEmptyBinHeading: '一切正常，这里没什么可看的',
	InboxEmptyBinSuccess: '已成功删除对话',
	InboxEmptyCongratsHeading: '做得好！坐下来放松，等待下一次对话',
	InboxEmptyDraftDescription: '未找到任何对话草稿',
	InboxEmptyDraftHeading: '一切正常，这里没什么可看的',
	InboxEmptyOtherDescription: '未找到其他对话',
	InboxEmptyScheduledHeading: '一切正常，没有安排发送对话',
	InboxEmptySentDescription: '未找到已发送的对话',
	InboxForward: '向前',
	InboxGroupClientsLabel: '所有客户端',
	InboxGroupClientsOverviewLabel: '客户',
	InboxGroupClientsSelectedItemPrefix: '客户',
	InboxGroupStaffsLabel: '所有团队',
	InboxGroupStaffsOverviewLabel: '团队',
	InboxGroupStaffsSelectedItemPrefix: '团队',
	InboxGroupStatusLabel: '所有状态',
	InboxGroupStatusOverviewLabel: '发送状态',
	InboxGroupStatusSelectedItemPrefix: '地位',
	InboxGroupTagsLabel: '所有标签',
	InboxGroupTagsOverviewLabel: '发送到标签',
	InboxGroupTagsSelectedItemPrefix: '标签',
	InboxHideQuotedText: '隐藏引用文本',
	InboxIgnoreConversationSuccess: '成功忽略对话',
	InboxMessageAllLabelRecipientsCount: '所有 {label} 接收者 ({count})',
	InboxMessageBodyPlaceholder: '添加您的消息',
	InboxMessageDeleted: '消息已删除',
	InboxMessageMarkedAsRead: '消息标记为已读',
	InboxMessageMarkedAsUnread: '消息标记为未读',
	InboxMessageSentViaChat: '<strong>通过聊天发送</strong>  • {time} 由 {name}',
	InboxMessageShowMoreRecipients: '+{count} 个更多',
	InboxMessageWasDeleted: '此消息已删除',
	InboxNoConnectionDescription: '连接您的电子邮件帐户或创建包含多个电子邮件的收件箱',
	InboxNoConnectionHeading: '整合您的客户沟通',
	InboxNoDirectMessage: '没有最近的消息',
	InboxRecentConversations: '最近',
	InboxReopenConversationSuccess: '成功重新开启对话',
	InboxReply: '回复',
	InboxReplyAll: '回复全部',
	InboxRestoreConversationSuccess: '成功恢复对话',
	InboxScheduleSendCancelSendSuccess: '已取消预定的发送并将邮件恢复为草稿',
	InboxScheduleSendMessageSuccessDescription: '发送时间已安排为 {date}',
	InboxScheduleSendMessageSuccessTitle: '安排发送',
	InboxSearchForConversations: '搜索“{query}”',
	InboxSendMessageSuccess: '对话发送成功',
	InboxSettings: '收件箱设置',
	InboxSettingsAppsDesc: '管理此共享收件箱的连接应用程序：根据需要添加或删除连接。',
	InboxSettingsAppsNewConnectedApp: '新的连接应用程序',
	InboxSettingsAppsTitle: '连接的应用程序',
	InboxSettingsDeleteAccountFailed: '无法删除收件箱帐户',
	InboxSettingsDeleteAccountSuccess: '成功删除收件箱帐户',
	InboxSettingsDeleteAccountWarning: '删除 {email} 将会使其与收件箱 {inboxName} 断开连接，并且会停止邮件同步。',
	InboxSettingsDeleteInboxFailed: '无法删除收件箱',
	InboxSettingsDeleteInboxSuccess: '成功删除收件箱',
	InboxSettingsDeleteInboxWarning:
		'删除 {inboxName} 将断开所有连接的频道并删除与该收件箱关联的所有消息。		此操作是永久性的，无法撤消。',
	InboxSettingsDetailsDesc: '您的团队可使用通讯收件箱高效管理客户消息。',
	InboxSettingsDetailsTitle: '收件箱详情',
	InboxSettingsEmailSignatureLabel: '电子邮件签名默认',
	InboxSettingsReplyFormatDesc: '设置您的默认回复地址和电子邮件签名，使其始终显示，无论是谁发送电子邮件。',
	InboxSettingsReplyFormatTitle: '回复格式',
	InboxSettingsSendFromLabel: '设置默认回复 ',
	InboxSettingsStaffDesc: '管理团队成员对此共享收件箱的访问，实现无缝协作。',
	InboxSettingsStaffTitle: '分配团队成员',
	InboxSettingsUpdateInboxDetailsFailed: '无法更新收件箱详情',
	InboxSettingsUpdateInboxDetailsSuccess: '已成功更新收件箱详情',
	InboxSettingsUpdateInboxStaffsFailed: '无法更新收件箱团队成员',
	InboxSettingsUpdateInboxStaffsSuccess: '已成功更新收件箱团队成员',
	InboxSettingsUpdateReplyFormatFailed: '无法更新回复格式',
	InboxSettingsUpdateReplyFormatSuccess: '成功更新回复格式',
	InboxShowQuotedText: '显示引用文本',
	InboxStaffRoleAdminDescription: '查看、回复和管理收件箱',
	InboxStaffRoleResponderDescription: '查看并回复',
	InboxStaffRoleViewerDescription: '仅查看',
	InboxSuggestMoveToBulkComposeMessageActionCancel: '继续编辑',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: '是的，切换到批量发送',
	InboxSuggestMoveToBulkComposeMessageContent: '您已选择超过 {count} 个收件人。您要以群发邮件形式发送吗？',
	InboxSuggestMoveToBulkComposeMessageTitle: '警告',
	InboxSwitchToOtherInbox: '切换到另一个收件箱',
	InboxUndoSendMessageSuccess: '发送已撤消',
	IncludeLineItems: '包括订单项',
	IncludeSalesTax: '应纳税',
	IncludesAiSmartPrompt: '包含AI智能提示',
	Incomplete: '不完整',
	IncreaseIndent: '增加缩进',
	IndianHealthServiceFreeStandingFacility: '印度健康服务独立设施',
	IndianHealthServiceProviderFacility: '印度健康服务提供者设施',
	Information: '信息',
	InitialAssessment: '初步评估',
	InitialSignupPageClientFamilyTitle: '客户或家庭成员',
	InitialSignupPageProviderTitle: '健康 ',
	InitialTreatment: '初始治疗',
	Initials: '首字母',
	InlineEmbed: '内嵌',
	InputPhraseToConfirm: '请确认并输入 {confirmationPhrase}。',
	Insert: '插入',
	InsertTable: '插入表格',
	InstallCarepatronOnYourIphone1: '在您的 iOS 上安装 Carepatron：点击',
	InstallCarepatronOnYourIphone2: '然后添加到主屏幕',
	InsufficientCalendarScopesSnackbar: '同步失败 - 请允许 Carepatron 拥有日历权限',
	InsufficientInboxScopesSnackbar: '同步失败 - 请允许 Carepatron 发送电子邮件权限',
	InsufficientScopeErrorCodeSnackbar: '同步失败 - 请允许 Carepatron 获得所有权限',
	Insurance: '保险',
	InsuranceAmount: '保险金额',
	InsuranceClaim: '保险索赔',
	InsuranceClaimAiChatPlaceholder: '咨询保险索赔...',
	InsuranceClaimAiClaimNumber: '索赔 {number}',
	InsuranceClaimAiSubtitle: '保险账单 • 索赔验证',
	InsuranceClaimDeniedSubject: '理赔 {claimNumber} 已提交至 {payerNumber} {payerName}，但被拒绝。',
	InsuranceClaimErrorDescription: '该索赔包含来自支付方或清算所报告的错误。请查看以下错误消息并重新提交索赔。',
	InsuranceClaimErrorGuideLink: '保险索赔指南',
	InsuranceClaimErrorTitle: '索赔提交错误',
	InsuranceClaimNotFound: '未找到保险索赔',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {针对理赔 {claimNumber}, 由 {payerNumber} {payerName} 支付的 {paymentAmount} 已记录部分付款} other {针对理赔 {claimNumber}, 由 {payerNumber} {payerName} 支付的 {paymentAmount} 付款已记录}}',
	InsuranceClaimRejectedSubject: '提交给 {payerNumber} {payerName} 的理赔 {claimNumber} 已被拒绝',
	InsuranceClaims: '保险索赔',
	InsuranceInformation: '保险信息',
	InsurancePaid: '保险已支付',
	InsurancePayer: '保险付款人',
	InsurancePayers: '保险支付者',
	InsurancePayersDescription: '查看已添加到您帐户的付款方并管理注册。',
	InsurancePayment: '保险支付',
	InsurancePoliciesDetailsSubtitle: '添加客户保险信息以支持索赔。',
	InsurancePoliciesDetailsTitle: '政策详情',
	InsurancePoliciesListSubtitle: '添加客户保险信息以支持索赔。',
	InsurancePoliciesListTitle: '保险政策',
	InsuranceSelfPay: '自费',
	InsuranceType: '保险类型',
	InsuranceUnpaid: '保险未支付',
	Intake: '摄入量',
	IntakeExpiredErrorCodeSnackbar: '此录取已过期。请联系您的提供商重新发送另一份录取。',
	IntakeNotFoundErrorSnackbar: '无法找到此摄入量。请联系您的提供商重新发送另一份摄入量。',
	IntakeProcessLearnMoreInstructions: '入学表格设置指南',
	IntakeTemplateSelectorPlaceholder: '选择要发送给客户并需要填写的表格和协议',
	Integration: '一体化',
	IntenseBlur: '强烈模糊背景',
	InteriorDesigner: '室内设计师',
	InternetBanking: '银行转账',
	Interval: '间隔',
	IntervalDays: '间隔（天）',
	IntervalHours: '间隔（小时）',
	Invalid: '无效的',
	InvalidDate: '日期无效',
	InvalidDateFormat: '日期必须为 {format} 格式',
	InvalidDisplayName: '显示名称不能包含 {value}',
	InvalidEmailFormat: '电子邮件格式无效',
	InvalidFileType: '文件类型无效',
	InvalidGTMContainerId: 'GTM 容器 ID 格式无效',
	InvalidPaymentMethodCode: '所选付款方式无效。请选择其他方式。',
	InvalidPromotionCode: '促销代码无效',
	InvalidReferralDescription: '已在使用 Carepatron',
	InvalidStatementDescriptor: `语句描述符必须介于 5 到 22 个字符之间，并且只能包含字母、数字、空格，并且不得包含 <、>、\\、'、"、*`,
	InvalidToken: '令牌无效',
	InvalidTotpSetupVerificationCode: '验证码无效。',
	InvalidURLErrorText: '这必须是有效的 URL',
	InvalidZoomTokenErrorCodeSnackbar: 'Zoom 令牌已过期。请重新连接您的 Zoom 应用程序并重试。',
	Invite: '邀请',
	InviteRelationships: '邀请关系',
	InviteToPortal: '邀请加入门户',
	InviteToPortalModalDescription: '我们将向您的客户发送一封邀请电子邮件，以便您注册 Carepatron。',
	InviteToPortalModalTitle: '邀请 {name} 加入 Carepatron 门户网站',
	InviteUserDescription: ' ',
	InviteUserTitle: '邀请新用户',
	Invited: '受邀',
	Invoice: '发票',
	InvoiceColorPickerDescription: '发票中使用的颜色主题',
	InvoiceColorTheme: '发票颜色主题',
	InvoiceContactDeleted: '发票联系人已被删除，此发票无法更新。',
	InvoiceDate: '发行日期',
	InvoiceDetails: '发票详细信息',
	InvoiceFieldsPlaceholder: '搜索字段...',
	InvoiceFrom: '发票 {number} 来自 {fromProvider}',
	InvoiceInvalidCredit: '信用额度无效，信用额度不能超过发票总额',
	InvoiceNotFoundDescription: '请联系您的供应商并要求他们提供更多信息或重新发送发票。',
	InvoiceNotFoundTitle: '未找到发票',
	InvoiceNumber: '发票 #',
	InvoiceNumberFormat: '发票 #{number}',
	InvoiceNumberMustEndWithDigit: '发票号码必须以数字结尾（0-9）',
	InvoicePageHeader: '发票',
	InvoicePaidNotificationSubject: '发票 {invoiceNumber} 已支付',
	InvoiceReminder: '发票提醒',
	InvoiceReminderSentence: '发送 {deliveryType} 提醒，在发票到期日 {beforeAfter} {interval} {unit}',
	InvoiceReminderSettings: '发票提醒设置',
	InvoiceReminderSettingsInfo: '提醒仅适用于通过 Carepatron 发送的发票',
	InvoiceReminders: '发票提醒',
	InvoiceRemindersInfo: '设置发票到期日的自动提醒。提醒仅适用于通过 Carepatron 发送的发票',
	InvoiceSettings: '发票设置',
	InvoiceStatus: '发票状态',
	InvoiceTemplateAddressPlaceholder: '123 Main St，Anytown，美国',
	InvoiceTemplateDescriptionPlaceholder: '添加注释、银行转账详情或替代付款的条款和条件',
	InvoiceTemplateEmploymentStatusPlaceholder: '自雇',
	InvoiceTemplateEthnicityPlaceholder: '高加索人',
	InvoiceTemplateNotFoundDescription: '请联系您的提供商并向他们询问更多信息。',
	InvoiceTemplateNotFoundTitle: '未找到发票模板',
	InvoiceTemplates: '发票模板',
	InvoiceTemplatesDescription: '使用我们用户友好的模板定制您的发票模板来反映您的品牌、满足监管要求并迎合客户偏好。',
	InvoiceTheme: '发票主题',
	InvoiceTotal: '发票总计',
	InvoiceUninvoicedAmounts: '开具未开票金额的发票',
	InvoiceUpdateVersionMessage: '编辑此发票需要最新版本。请重新加载 Carepatron 并重试。',
	Invoices: '{count, plural, one {发票} other {发票}}',
	InvoicesEmptyStateDescription: '未找到发票',
	InvoicingAndPayment: '发票 ',
	Ireland: '爱尔兰',
	IsA: '是',
	IsBetween: '介于',
	IsEqualTo: '等于',
	IsGreaterThan: '大于',
	IsGreaterThanOrEqualTo: '大于或等于',
	IsLessThan: '小于',
	IsLessThanOrEqualTo: '小于或等于',
	IssueCredit: '发放信用证',
	IssueCreditAdjustment: '发行信用调整',
	IssueDate: '签发日期',
	Italic: '斜体',
	Items: '项目',
	ItemsAndAdjustments: '项目和调整',
	ItemsRemaining: '+{count} 项剩余',
	JobTitle: '职称',
	Join: '加入',
	JoinCall: '加入通话',
	JoinNow: '立即加入',
	JoinProduct: '加入 {product}',
	JoinVideoCall: '加入视频通话',
	JoinWebinar: '加入网络研讨会',
	JoinWithVideoCall: '加入 {product}',
	Journalist: '记者',
	JustMe: '只有我',
	JustYou: '只有你',
	Justify: '证明合法',
	KeepSeparate: '保持分开',
	KeepSeparateSuccessMessage: '您已成功为 {clientNames}  保存了独立的记录。',
	KeepWaiting: '继续等待',
	Label: '标签',
	LabelOptional: '标签（可选）',
	LactationConsulting: '哺乳咨询',
	Language: '语言',
	Large: '大的',
	LastDxCode: '最新 DX 代码',
	LastLoggedIn: '最后登录时间：{date} {time}',
	LastMenstrualPeriod: '最后一次月经',
	LastMonth: '上个月',
	LastNDays: '最近 {number} 天',
	LastName: '姓',
	LastNameFirstInitial: '姓氏，名字首字母',
	LastWeek: '上星期',
	LastXRay: '最后一次 X 光检查',
	LatestVisitOrConsultation: '最近一次的访问或咨询',
	Lawyer: '律师',
	LearnMore: '了解更多',
	LearnMoreTipsToGettingStarted: '了解更多入门技巧',
	LearnToSetupInbox: '收件箱账户设置指南',
	Leave: '离开',
	LeaveCall: '留下电话',
	LeftAlign: '左对齐',
	LegacyBillingItemsNotAvailable: '单个计费项目尚不可用，您可以照常开具发票。',
	LegacyBillingItemsNotAvailableTitle: '传统计费',
	LegalAndConsent: '法律和同意',
	LegalConsentFormPrimaryText: '法律同意',
	LegalConsentFormSecondaryText: '接受或拒绝选项',
	LegalGuardian: '法定监护人',
	Letter: '信件',
	LettersCategoryDescription: '用于创建临床和管理信函',
	Librarian: '图书管理员',
	LicenseNumber: '執照號碼',
	LifeCoach: '生活教练',
	LifeCoaches: '生活教练',
	Limited: '有限',
	LineSpacing: '行距和段落间距',
	LinearScaleFormPrimaryText: '线性刻度',
	LinearScaleFormSecondaryText: '比例选项 1-10',
	Lineitems: '订单项',
	Link: '关联',
	LinkClientFormSearchClientLabel: '寻找客户',
	LinkClientModalTitle: '链接到现有客户',
	LinkClientSuccessDescription:
		'<strong>{newName}</strong> 的联系信息已添加到 <strong>{existingName}</strong> 的记录中。',
	LinkClientSuccessTitle: '成功链接到现有联系人',
	LinkForCallCopied: '链接已复制！',
	LinkToAnExistingClient: '链接到现有客户',
	LinkToClient: '链接到客户端',
	ListAndTracker: '列表/跟踪器',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} 正在此通话}
			other {{attendees} 正在此通话}
		}`,
	ListStyles: '列表样式',
	ListsAndTrackersCategoryDescription: '用于组织和跟踪工作',
	LivingArrangements: '居住安排',
	LoadMore: '加载更多',
	Loading: '加载中...',
	LocalizationPanelDescription: '管理你的语言和时区设置',
	LocalizationPanelTitle: '语言和时区',
	Location: '地点',
	LocationDescription: '设置具有特定地址、房间名称和虚拟空间类型的物理和虚拟位置，以便更轻松地安排约会和视频通话。',
	LocationNumber: '位置编号',
	LocationOfService: '服务地点',
	LocationOfServiceRecommendedActionInfo: '为该服务添加特定位置可能会影响您的可用性。',
	LocationRemote: '远程',
	LocationType: '位置类型',
	Locations: '位置',
	Lock: '锁',
	Locked: '已锁定',
	LockedNote: '锁定的笔记',
	LogInToSaveOrAuthoriseCard: '登录以保存或授权该卡',
	LogInToSaveOrAuthorisePayment: '登录以保存或授权付款',
	Login: '登录',
	LoginButton: '登入',
	LoginEmail: '电子邮件',
	LoginForgotPasswordLink: '忘记密码',
	LoginPassword: '密码',
	Logo: '标识',
	LogoutAreYouSure: '退出此设备。',
	LogoutButton: '登出',
	London: '伦敦',
	LongTextAnswer: '长文本答案',
	LongTextFormPrimaryText: '长文本',
	LongTextFormSecondaryText: '段落样式选项',
	Male: '男性',
	Manage: '管理',
	ManageAllClientTags: '管理所有客户端标签',
	ManageAllNoteTags: '管理所有笔记标签',
	ManageAllTemplateTags: '管理所有模板标签',
	ManageConnections: '管理连接',
	ManageConnectionsGmailDescription: '其他团队成员将无法看到您同步的 Gmail。',
	ManageConnectionsGoogleCalendarDescription:
		'其他团队成员将无法看到您同步的日历。客户预约只能在 Carepatron 内更新或删除。',
	ManageConnectionsInboxSyncHelperText: '请转到收件箱页面以管理同步收件箱设置。',
	ManageConnectionsMicrosoftCalendarDescription:
		'其他团队成员将无法看到您同步的日历。客户预约只能在 Carepatron 内更新或删除。',
	ManageConnectionsOutlookDescription: '其他团队成员将无法看到您同步的 Microsoft Outlook。',
	ManageInboxAccountButton: '新收件箱',
	ManageInboxAccountEdit: '管理收件箱',
	ManageInboxAccountPanelTitle: '收件箱',
	ManageInboxAssignTeamPlaceholder: '选择团队成员访问收件箱',
	ManageInboxBasicInfoColor: '颜色',
	ManageInboxBasicInfoDescription: '描述',
	ManageInboxBasicInfoDescriptionPlaceholder: '您或您的团队将使用此收件箱做什么？',
	ManageInboxBasicInfoName: '收件箱名称',
	ManageInboxBasicInfoNamePlaceholder: '例如客户支持、管理员',
	ManageInboxConnectAppAlreadyConnectedError: '您尝试连接的频道已经连接到 Carepatron',
	ManageInboxConnectAppConnect: '连接',
	ManageInboxConnectAppConnectedInfo: '已关联账户',
	ManageInboxConnectAppContinue: '继续',
	ManageInboxConnectAppEmail: '电子邮件',
	ManageInboxConnectAppSignInWith: '使用以下方式登录',
	ManageInboxConnectAppSubtitle: '连接您的应用程序以便在一个集中位置无缝发送、接收和跟踪您的所有通信。',
	ManageInboxNewInboxTitle: '新收件箱',
	ManagePlan: '管理计划',
	ManageProfile: '管理个人资料',
	ManageReferralsModalDescription: '帮助我们宣传我们的医疗保健平台并获得奖励。',
	ManageReferralsModalTitle: '推荐朋友，赚取奖励！',
	ManageStaffRelationshipsAddButton: '管理关系',
	ManageStaffRelationshipsEmptyStateText: '未添加任何关系',
	ManageStaffRelationshipsModalDescription: '选择客户将添加新的关系，而取消选择客户将删除现有的关系。',
	ManageStaffRelationshipsModalTitle: '管理关系',
	ManageStatuses: '管理状态',
	ManageStatusesActiveStatusHelperText: '至少需要一个活跃状态',
	ManageStatusesDescription: '自定义您的状态标签并选择与您的工作流程相符的颜色。',
	ManageStatusesSuccessSnackbar: '成功更新状态',
	ManageTags: '管理标签',
	ManageTaskAttendeeStatus: '管理预约状态',
	ManageTaskAttendeeStatusDescription: '自定义您的预约状态以符合您的工作流程。',
	ManageTaskAttendeeStatusHelperText: '至少需要一个状态',
	ManageTaskAttendeeStatusSubtitle: '自定义状态',
	ManagedClaimMd: 'Claim.MD',
	Manual: '手动的',
	ManualAppointment: '手动预约',
	ManualPayment: '手动付款',
	ManuallyTypeLocation: '手动输入位置',
	MapColumns: '地图列',
	MappingRequired: '需要映射',
	MarkAllAsRead: '全部标记为已读',
	MarkAsCompleted: '标记为已完成',
	MarkAsManualSubmission: '标记为已提交',
	MarkAsPaid: '标记为已付款',
	MarkAsRead: '标记为已读',
	MarkAsUnpaid: '标记为未付款',
	MarkAsUnread: '标记为未读',
	MarkAsVoid: '标记为无效',
	Marker: '标记',
	MarketingManager: '营销经理',
	MassageTherapist: '按摩治疗师',
	MassageTherapists: '按摩治疗师',
	MassageTherapy: '按摩疗法',
	MaxBookingTimeDescription1: '客户最多可以安排',
	MaxBookingTimeDescription2: '走向未来',
	MaxBookingTimeLabel: '提前{timePeriod}',
	MaxCapacity: '最大容量',
	Maximize: '最大化',
	MaximumAttendeeLimit: '最大限制',
	MaximumBookingTime: '最长预订时间',
	MaximumBookingTimeError: '最大预订时间不得超过 {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'您最多可以同时最小化 {count} 个侧边栏。继续操作将关闭最早最小化的面板。您要继续吗？',
	MaximumMinimizedPanelsReachedTitle: '你打开了太多面板。',
	MechanicalEngineer: '机械工程师',
	MediaGallery: '媒体图库',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: '医疗补助提供者编号',
	MedicalAssistant: '医疗助理',
	MedicalCoder: '医疗编码员',
	MedicalDoctor: '医生',
	MedicalIllustrator: '医学插画师',
	MedicalInterpreter: '医疗口译员',
	MedicalTechnologist: '医学技师',
	Medicare: 'Medicare',
	MedicareProviderNumber: '医疗保险提供者编号',
	Medicine: '药品',
	Medium: '中等的',
	Meeting: '会议',
	MeetingEnd: '结束会议',
	MeetingEnded: '会议结束',
	MeetingHost: '会议主持人',
	MeetingLowerHand: '下手',
	MeetingOpenChat: '打开聊天',
	MeetingPersonRaisedHand: '{name} 举起了手',
	MeetingRaiseHand: '举手',
	MeetingReady: '会议准备就绪',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {分钟} other {分钟}} {status}',
	Meetings: '会议',
	MemberId: '会员编号',
	MentalHealth: '心理健康',
	MentalHealthPractitioners: '心理健康从业者',
	MentalHealthProfessional: '心理健康专家',
	Merge: '合并',
	MergeClientRecords: '合并客户记录',
	MergeClientRecordsDescription: '合并客户记录将整合所有数据，包括：',
	MergeClientRecordsDescription2: '您希望继续合并吗？此操作无法撤销。',
	MergeClientRecordsItem1: '笔记和文档',
	MergeClientRecordsItem2: '预约',
	MergeClientRecordsItem3: '发票',
	MergeClientRecordsItem4: '对话',
	MergeClientsSuccess: '成功合并客户记录',
	MergeLimitExceeded: '您一次最多可以合并 4 个客户。',
	Message: '信息',
	MessageAttachments: '{total} 个附件',
	Method: '方法',
	MfaAvailabilityDisclaimer: 'MFA 仅适用于电子邮件和密码登录。要更改 MFA 设置，请使用您的电子邮件和密码登录。',
	MfaDeviceLostPanelDescription: '或者，您可以通过电子邮件接收代码来验证您的身份。',
	MfaDeviceLostPanelTitle: '丢失了您的 MFA 设备？',
	MfaDidntReceiveEmailCode: '没有收到代码？请联系支持人员',
	MfaEmailOtpSendFailureSnackbar: '无法发送电子邮件 OTP。',
	MfaEmailOtpSentSnackbar: '一个代码已发送至 {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: '无法验证电子邮件 OTP。',
	MfaHasBeenSetUpText: '您已设置 MFA',
	MfaPanelDescription:
		'启用多重身份验证 (MFA) 为您的帐户提供额外保护，从而确保帐户安全。通过辅助方法验证您的身份，以防止未经授权的访问。',
	MfaPanelNotAuthorizedError: '您必须使用用户名登录 ',
	MfaPanelRecommendationDescription:
		'您最近使用其他方法登录以验证您的身份。为了保证您的帐户安全，请考虑设置新的 MFA 设备。',
	MfaPanelRecommendationTitle: '**推荐：** 更新您的 MFA 设备',
	MfaPanelTitle: '多重身份验证 (MFA)',
	MfaPanelVerifyEmailFirstAlert: '您需要先验证您的电子邮件，然后才能更新您的 MFA 设置。',
	MfaRecommendationBannerDescription:
		'您最近使用其他方法登录以验证您的身份。为了保证您的帐户安全，请考虑设置新的 MFA 设备。',
	MfaRecommendationBannerPrimaryAction: '设置 MFA',
	MfaRecommendationBannerTitle: '受到推崇的',
	MfaRemovedSnackbarTitle: 'MFA 已被删除。',
	MfaSendEmailCode: '发送代码',
	MfaVerifyIdentityLostDeviceButton: '我无法访问我的 MFA 设备',
	MfaVerifyYourIdentityPanelDescription: '检查您的身份验证器应用程序中的代码并在下面输入。',
	MfaVerifyYourIdentityPanelTitle: '验证您的身份',
	MicCamWarningMessage: '点击浏览器地址栏中的被阻止图标，解除对摄像头和麦克风的阻止。',
	MicCamWarningTitle: '摄像头和麦克风被遮挡',
	MicOff: '麦克风已关闭',
	MicOn: '麦克风已打开',
	MicSource: '麦克风源',
	MicWarningMessage: '检测到您的麦克风存在问题',
	Microphone: '麦克风',
	MicrophonePermissionBlocked: '麦克风访问被阻止',
	MicrophonePermissionBlockedDescription: '更新您的麦克风权限以开始录制。',
	MicrophonePermissionError: '请在浏览器设置中授予麦克风权限以继续',
	MicrophonePermissionPrompt: '请允许麦克风访问继续',
	Microsoft: '微软',
	MicrosoftCalendar: '微软',
	MicrosoftColor: 'Outlook 日历颜色',
	MicrosoftOutlook: '微软 Outlook',
	MicrosoftTeams: '微软团队',
	MiddleEast: '中东',
	MiddleName: '中间名字',
	MiddleNames: '中间名字',
	Midwife: '助产士',
	Midwives: '助产士',
	Milan: '米兰',
	MinBookingTimeDescription1: '客户无法在以下时间内安排',
	MinBookingTimeDescription2: '预约的开始时间',
	MinBookingTimeLabel: '{timePeriod} 之前预约',
	MinCancellationTimeEditModeDescription: '设定客户可以取消多少小时而不会受到处罚',
	MinCancellationTimeUnset: '未设置最低取消时间',
	MinCancellationTimeViewModeDescription: '取消期限无罚金',
	MinMaxBookingTimeUnset: '未设定时间',
	Minimize: '最小化',
	MinimizeConfirmationDescription: '您有一个正在最小化的活动面板。如果您继续，它将关闭，您可能会丢失未保存的数据。',
	MinimizeConfirmationTitle: '关闭最小化面板？',
	MinimumBookingTime: '最短预订时间',
	MinimumCancellationTime: '最短取消时间',
	MinimumPaymentError: '在线支付需要最低 {minimumAmount} 的费用',
	MinuteAbbreviated: '分钟',
	MinuteAbbreviation: '{count} {count, plural, one {分钟} other {分钟}}',
	Minutely: '分秒',
	MinutesPlural: '{age, plural, one {# 分钟} other {# 分钟}}',
	MiscellaneousInformation: '其他信息',
	MissingFeatures: '缺少的功能',
	MissingPaymentMethod: '请在您的订阅中添加付款方式以添加更多工作人员。',
	MobileNumber: '手机号码',
	MobileNumberOptional: '手机号码（可选）',
	Modern: '现代的',
	Modifiers: '修饰符',
	ModifiersPlaceholder: '修饰符',
	Monday: '周一',
	Month: '月',
	Monthly: '每月',
	MonthlyCost: '每月费用',
	MonthlyOn: '每月 {date}',
	MonthsPlural: '{age, plural, one {# 个月} other {# 个月}}',
	More: '更多的',
	MoreActions: '更多操作',
	MoreSettings: '更多设置',
	MoreThanTen: '10 ',
	MostCommonlyUsed: '最常用',
	MostDownloaded: '下载次数最多',
	MostPopular: '最受欢迎',
	Mother: '母亲',
	MotherInLaw: '岳母',
	MoveDown: '下移',
	MoveInboxConfirmationDescription: '重新分配此应用程序连接将将其从 **{currentInboxName}** 收件箱中移除。',
	MoveTemplateToFolder: '移动 `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} 移动到 {folderTitle}。',
	MoveTemplateToIntakeFolderSuccessMessage: '成功移至默认收件箱文件夹',
	MoveTemplateToNewFolder: '创建一个新文件夹来移动此项。',
	MoveToChosenFolder: '选择一个文件夹将此项目移动到。您可以在需要时创建一个新文件夹。',
	MoveToFolder: '移动到文件夹',
	MoveToInbox: '移至收件箱',
	MoveToNewFolder: '移动到新文件夹',
	MoveToSelectedFolder: '移动后，该项目将被整理到选定的文件夹下，并且将不再出现在其当前位置。',
	MoveUp: '向上移动',
	MultiSpeciality: '多专业',
	MultipleChoiceFormPrimaryText: '多项选择',
	MultipleChoiceFormSecondaryText: '选择多个选项',
	MultipleChoiceGridFormPrimaryText: '多项选择网格',
	MultipleChoiceGridFormSecondaryText: '从矩阵中选择选项',
	Mumbai: '孟买',
	MusicTherapist: '音乐治疗师',
	MustContainOneLetterError: '必须至少包含一个字母',
	MustEndWithANumber: '必须以数字结尾',
	MustHaveAtLeastXItems: '必须至少有 {count, plural, one {# 个项目} other {# 个项目}}',
	MuteAudio: '静音',
	MuteEveryone: '全部静音',
	MyAvailability: 'My availability',
	MyGallery: '我的画廊',
	MyPortal: '我的门户',
	MyRelationships: '我的关系',
	MyTemplates: '团队模板',
	MyofunctionalTherapist: '肌功能治疗师',
	NCalifornia: '北加州',
	NPI: '新产品导入',
	NVirginia: '北弗吉尼亚州',
	Name: '姓名',
	NameIsRequired: '姓名为必填项',
	NameMustNotBeAWebsite: '名称不能是网站',
	NameMustNotBeAnEmail: '姓名不能是电子邮件',
	NameMustNotContainAtSign: '名称不能包含@符号',
	NameMustNotContainHTMLTags: '名称不得包含 HTML 标签',
	NameMustNotContainSpecialCharacters: '名称不得包含特殊字符',
	NameOnCard: '卡上姓名',
	NationalProviderId: '国家提供商标识符 (NPI)',
	NaturopathicDoctor: '自然疗法医生',
	NavigateToPersonalSettings: '个人资料',
	NavigateToSubscriptionSettings: '订阅设置',
	NavigateToWorkspaceSettings: '工作区设置',
	NavigateToYourTeam: '管理团队',
	NavigationDrawerBilling: '计费',
	NavigationDrawerBillingInfo: '账单信息、发票和 Stripe',
	NavigationDrawerCommunication: '沟通',
	NavigationDrawerCommunicationInfo: '通知和模板',
	NavigationDrawerInsurance: '保险',
	NavigationDrawerInsuranceInfo: '保险付款人和索赔',
	NavigationDrawerInvoices: '计费',
	NavigationDrawerPersonal: '我的个人资料',
	NavigationDrawerPersonalInfo: '您的个人信息',
	NavigationDrawerProfile: '轮廓',
	NavigationDrawerProviderSettings: '设置',
	NavigationDrawerScheduling: '调度',
	NavigationDrawerSchedulingInfo: '服务详情及预订',
	NavigationDrawerSettings: '设置',
	NavigationDrawerTemplates: '模板',
	NavigationDrawerTemplatesV2: '模板 V2',
	NavigationDrawerTrash: '垃圾',
	NavigationDrawerTrashInfo: '恢复已删除的项目',
	NavigationDrawerWorkspace: '工作区设置',
	NavigationDrawerWorkspaceInfo: '订阅和工作区信息',
	NegativeBalanceNotSupported: '不支持负账户余额',
	Nephew: '侄子',
	NetworkQualityFair: '公平连接',
	NetworkQualityGood: '连接良好',
	NetworkQualityPoor: '连接不良',
	Neurologist: '神经科医生',
	Never: '从不',
	New: '新的',
	NewAppointment: '新任命',
	NewClaim: '新主张',
	NewClient: '新客户',
	NewClientNextStepsModalAddAnotherClient: '添加另一个客户端',
	NewClientNextStepsModalBookAppointment: '預約',
	NewClientNextStepsModalBookAppointmentDescription: '预订即将到来的约会或创建任务。',
	NewClientNextStepsModalCompleteBasicInformation: '完整的客户记录',
	NewClientNextStepsModalCompleteBasicInformationDescription: '添加客户信息并记录后续步骤。',
	NewClientNextStepsModalCreateInvoice: '创建发票',
	NewClientNextStepsModalCreateInvoiceDescription: '添加客户付款信息或创建发票。',
	NewClientNextStepsModalCreateNote: '创建笔记或上传文档',
	NewClientNextStepsModalCreateNoteDescription: '记录客户笔记和文档。',
	NewClientNextStepsModalDescription: '现在您已经创建了客户记录，请采取以下一些操作。',
	NewClientNextStepsModalSendIntake: '发送摄入量',
	NewClientNextStepsModalSendIntakeDescription: '收集客户信息并发送其他表格以供填写和签署。',
	NewClientNextStepsModalSendMessage: '发送消息',
	NewClientNextStepsModalSendMessageDescription: '撰写并发送一条消息给你的客户。',
	NewClientNextStepsModalTitle: '后续步骤Next steps',
	NewClientSuccess: '成功创建新客户端',
	NewClients: '新客户',
	NewConnectedApp: '新的连接应用程序',
	NewContact: '新联系方式',
	NewContactNextStepsModalAddRelationship: '添加关系',
	NewContactNextStepsModalAddRelationshipDescription: '将此联系人链接到相关的客户或群组。',
	NewContactNextStepsModalBookAppointment: '预约',
	NewContactNextStepsModalBookAppointmentDescription: '预订即将到来的预约或创建任务。',
	NewContactNextStepsModalCompleteProfile: '完整资料',
	NewContactNextStepsModalCompleteProfileDescription: '添加联系信息并记录下一步。',
	NewContactNextStepsModalCreateNote: '创建笔记或上传文档',
	NewContactNextStepsModalCreateNoteDescription: '捕获客户笔记和文档。',
	NewContactNextStepsModalDescription: '以下是您创建联系人后可以采取的一些操作。',
	NewContactNextStepsModalInviteToPortal: '邀请门户',
	NewContactNextStepsModalInviteToPortalDescription: '发送一个访问门户的邀请。',
	NewContactNextStepsModalTitle: '下一步',
	NewContactSuccess: '成功创建新联系人',
	NewDateOverrideButton: '新日期覆盖',
	NewDiagnosis: '添加诊断',
	NewField: '新领域',
	NewFolder: '新建文件夹',
	NewInvoice: '新发票',
	NewLocation: '新地点',
	NewLocationFailure: '无法创建新位置',
	NewLocationSuccess: '成功创建新位置',
	NewManualPayer: '新的手动付款人',
	NewNote: '新笔记',
	NewNoteCreated: '成功创建新笔记',
	NewPassword: '新密码',
	NewPayer: '新付款人',
	NewPaymentMethod: '新的付款方式',
	NewPolicy: '新政策',
	NewRelationship: '新关系',
	NewReminder: '新提醒',
	NewSchedule: '新时间表',
	NewSection: '新部分',
	NewSectionOld: '新部分 [旧]',
	NewSectionWithGrid: '带有网格的新部分',
	NewService: '新服务',
	NewServiceFailure: '无法创建新服务',
	NewServiceSuccess: '成功创建新服务',
	NewStatus: '新状态',
	NewTask: '新任务',
	NewTaxRate: '新税率',
	NewTeamMemberNextStepsModalAssignClients: '分配客户',
	NewTeamMemberNextStepsModalAssignClientsDescription: '为您的团队成员分配特定客户。',
	NewTeamMemberNextStepsModalAssignServices: '分配服务',
	NewTeamMemberNextStepsModalAssignServicesDescription: '管理他们分配的服务并根据需要调整价格。',
	NewTeamMemberNextStepsModalBookAppointment: '预约',
	NewTeamMemberNextStepsModalBookAppointmentDescription: '预订即将到来的预约或创建任务。',
	NewTeamMemberNextStepsModalCompleteProfile: '完善资料',
	NewTeamMemberNextStepsModalCompleteProfileDescription: '添加有关您的团队成员的详细信息以完善他们的个人资料。',
	NewTeamMemberNextStepsModalDescription: '以下是您创建团队成员后可采取的一些操作。',
	NewTeamMemberNextStepsModalEditPermissions: '编辑权限',
	NewTeamMemberNextStepsModalEditPermissionsDescription: '调整他们的访问级别，以确保他们拥有正确的权限。',
	NewTeamMemberNextStepsModalSetAvailability: '设置可用性',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: '配置他们的可用时间以创建排班。',
	NewTeamMemberNextStepsModalTitle: '下一步',
	NewTemplateFolderDescription: '创建一个新文件夹来组织您的文档。',
	NewUIUpdateBannerButton: '重新加载应用程序',
	NewUIUpdateBannerTitle: '有新的更新已准备好！',
	NewZealand: '新西兰',
	Newest: '最新',
	NewestUnreplied: '最新未回复',
	Next: '下一个',
	NextInvoiceIssueDate: '下次发票开具日期',
	NextNDays: '接下来 {number} 天',
	Niece: '侄女',
	No: '不',
	NoAccessGiven: '没有授予访问权限',
	NoActionConfigured: '未配置任何操作',
	NoActivePolicies: '没有有效政策',
	NoActiveReferrals: '您没有活跃的推荐人',
	NoAppointmentsFound: '未找到任何预约',
	NoAppointmentsHeading: '管理客户预约和活动',
	NoArchivedPolicies: '没有存档政策',
	NoAvailableTimes: '没有找到可用的时间。',
	NoBillingItemsFound: '未找到计费项目',
	NoCalendarsSynced: '未同步日历',
	NoClaimsFound: '未找到索赔',
	NoClaimsHeading: '简化报销申报流程',
	NoClientsHeading: '汇总您的客户记录',
	NoCompletedReferrals: '您没有完整的推荐',
	NoConnectionsHeading: '简化与客户的沟通',
	NoContactsGivenAccess: '尚无任何客户或联系人有权访问此笔记',
	NoContactsHeading: '与支持您实践的人保持联系',
	NoCopayOrCoinsurance: '无需共付额或共同保险',
	NoCustomServiceSchedule: '没有设置自定义时间表——可用性取决于团队成员的可用性',
	NoDescription: '没有描述',
	NoDocumentationHeading: '安全地创建和存储笔记',
	NoDuplicateRecordsHeading: '您的客户记录没有重复',
	NoEffect: '没有效果',
	NoEnrolmentProfilesFound: '没有找到报名资料',
	NoGlossaryItems: '没有词汇表项目',
	NoInvitedReferrals: '您没有邀请推荐人',
	NoInvoicesFound: '未找到发票',
	NoInvoicesHeading: '自动化您的帐单和付款',
	NoLimit: '无限制',
	NoLocationsFound: '未找到任何地点',
	NoLocationsWillBeAdded: '不会添加任何位置。',
	NoNoteFound: '未找到笔记',
	NoPaymentMethods: '您尚未保存付款方式，您可以在付款时添加。',
	NoPermissionError: '您没有权限',
	NoPermissions: '您无权查看此页面',
	NoPolicy: '未添加取消政策',
	NoRecordsHeading: '个性化您的客户记录',
	NoRecordsToDisplay: '没有 {resource} 可显示',
	NoRelationshipsHeading: '与支持您的客户的人保持联系',
	NoRemindersFound: '未找到提醒',
	NoResultsFound: '未找到结果',
	NoResultsFoundDescription: '我们找不到符合您搜索条件的任何商品',
	NoServicesAdded: '未添加服务',
	NoServicesApplied: '沒有使用任何服務',
	NoServicesWillBeAdded: '不会添加任何服务。',
	NoTemplate: '您尚未保存练习模板',
	NoTemplatesHeading: '创建自己的模板',
	NoTemplatesInFolder: '此文件夹中没有模板',
	NoTitle: '无标题',
	NoTrashItemsHeading: '未找到已删除的项目',
	NoTriggerConfigured: '未配置触发器',
	NoUnclaimedItemsFound: '未发现无人认领的物品。',
	NonAiTemplates: '非 AI 模板',
	None: '没有任何',
	NotAvailable: '不可用',
	NotCovered: '未涵盖',
	NotFoundSnackbar: '未找到资源。',
	NotRequiredField: '不要求',
	Note: '笔记',
	NoteDuplicateSuccess: '已成功复制笔记',
	NoteEditModeViewSwitcherDescription: '创建和编辑笔记',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} 提交了 {noteTitle} 表单',
	NoteLockSuccess: '{title} 已锁定',
	NoteModalAttachmentButton: '添加附件',
	NoteModalPhotoButton: '添加/拍摄照片',
	NoteModalTrascribeButton: '转录现场音频',
	NoteResponderModeViewSwitcherDescription: '发送表格并审核回复',
	NoteResponderModeViewSwitcherTooltipTitle: '代表您的客户回复并提交表格',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: '以客户身份填写并提交表格',
	NoteUnlockSuccess: '{title} 已解锁',
	NoteViewModeViewSwitcherDescription: '仅限查看权限',
	Notes: '笔记',
	NotesAndForms: '备注和表格',
	NotesCategoryDescription: '用于记录客户互动',
	NothingToSeeHere: '这里没有可看的内容',
	Notification: '通知',
	NotificationIgnoredMessage: '所有 {notificationType} 通知将被忽略',
	NotificationRestoredMessage: '所有 {notificationType} 通知已恢复',
	NotificationSettingBillingDescription: '接收客户付款更新和提醒的通知。',
	NotificationSettingBillingTitle: '账单和付款',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} 仅限} other{{channels}} }',
	NotificationSettingClientDocumentationDescription: '接收客户付款更新和提醒的通知。',
	NotificationSettingClientDocumentationTitle: '客户与文件',
	NotificationSettingCommunicationsDescription: '接收收件箱通知和来自连接频道的更新',
	NotificationSettingCommunicationsTitle: '通讯',
	NotificationSettingEmail: '电子邮件',
	NotificationSettingInApp: '应用程序中',
	NotificationSettingPanelDescription: '选择要接收活动和推荐的通知。',
	NotificationSettingPanelTitle: '通知偏好',
	NotificationSettingSchedulingDescription: '当团队成员或客户预订、重新安排或取消预约时接收通知。',
	NotificationSettingSchedulingTitle: '安排',
	NotificationSettingUpdateSuccess: '通知设置已成功更新',
	NotificationSettingWhereYouReceiveNotifications: '您希望在哪里接收这些通知',
	NotificationSettingWorkspaceDescription: '接收有关系统更改、问题、数据传输和订阅提醒的通知。',
	NotificationSettingWorkspaceTitle: '工作区',
	NotificationTemplateUpdateFailed: '更新通知模板失败',
	NotificationTemplateUpdateSuccess: '成功更新通知模板',
	NotifyAttendeesOfTaskCancellationModalDescription: '您想向与会者发送取消通知电子邮件吗？',
	NotifyAttendeesOfTaskCancellationModalTitle: '发送取消信息',
	NotifyAttendeesOfTaskConfirmationModalDescription: '您想向与会者发送确认通知电子邮件吗？',
	NotifyAttendeesOfTaskConfirmationModalTitle: '发送确认',
	NotifyAttendeesOfTaskDeletedModalTitle: '您想向与会者发送取消电子邮件吗？',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {没有} other {没有}} 电子邮件地址，因此不会收到自动通知和提醒。',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {没有} other {没有}} 电子邮件地址，因此不会收到自动通知和提醒。',
	NotifyAttendeesOfTaskModalTitle: '您想向与会者发送通知电子邮件吗？',
	NotifyAttendeesOfTaskSnackbar: '发送通知',
	NuclearMedicineTechnologist: '核医学技术员',
	NumberOfClaims: '{number, plural, one {# 索赔} other {# 索赔}}',
	NumberOfClients: '{number, plural, one {# 客户} other {# 客户}}',
	NumberOfContacts: '{number, plural, one {# 联系} other {# 联系}}',
	NumberOfDataEntriesFound: '找到 {count} {count, plural, one {entry} other {entries}}',
	NumberOfErrors: '{count, plural, one {# 错误} other {# 错误}}',
	NumberOfInvoices: '{number, plural, one {# 发票} other {# 发票}}',
	NumberOfLineitemsToCredit:
		'您有 <mark>{count} {count, plural, one {行项目} other {行项目}}</mark> 可以开具信用额度。',
	NumberOfPayments: '{number, plural, one {# 支付} other {# 支付}}',
	NumberOfRelationships: '{number, plural, one {# 关系} other {# 关系}}',
	NumberOfResources: '{number, plural, one {# 资源} other {# 资源}}',
	NumberOfTeamMembers: '{number, plural, one {# 团队成员} other {# 团队成员}}',
	NumberOfTrashItems: '{number, plural, one {# 个项目} other {# 个项目}}',
	NumberOfUninvoicedAmounts:
		'您有 <mark>{count} 未开具发票的 {count, plural, one {金额} other {金额}}</mark> 需要开具发票',
	NumberedList: '编号列表',
	Nurse: '护士',
	NurseAnesthetist: '麻醉护士',
	NurseAssistant: '护士助理',
	NurseEducator: '护士教育者',
	NurseMidwife: '护士助产士',
	NursePractitioner: '执业护士',
	Nurses: '护士',
	Nursing: '护理',
	Nutritionist: '营养师',
	Nutritionists: '营养师',
	ObstetricianOrGynecologist: '产科医生/妇科医生',
	Occupation: '职业',
	OccupationalTherapist: '职业治疗师',
	OccupationalTherapists: '职业治疗师',
	OccupationalTherapy: '职业治疗',
	Occurrences: '发生',
	Of: '的',
	Ohio: '俄亥俄州',
	OldPassword: '旧密码',
	OlderMessages: '{count} 条更早的消息',
	Oldest: '最古老的',
	OldestUnreplied: '最早未回复',
	On: '在',
	OnboardingBusinessAgreement: '我代表我和公司，同意 {businessAssociateAgreement}。',
	OnboardingLoadingOccupationalTherapist: '<mark>职业治疗师</mark>占 Carepatron 客户的四分之一',
	OnboardingLoadingProfession: '我们有大量的 <mark>{profession}</mark> 在使用 Carepatron 并取得了成功。',
	OnboardingLoadingPsychologist: '<mark>心理学家</mark>占 Carepatron 客户的一半以上',
	OnboardingLoadingSubtitleFive: '我们的使命是<mark>医疗保健软件可访问</mark>对每个人来说。',
	OnboardingLoadingSubtitleFour: '<mark>简化的健康软件</mark>为全球超过 10,000 人提供服务。',
	OnboardingLoadingSubtitleThree: '节省<mark>每周 1 天</mark>在Carepatron的帮助下完成管理任务。',
	OnboardingLoadingSubtitleTwo: '节省<mark>2小时</mark>每天在 Carepatron 的帮助下完成管理任务。',
	OnboardingReviewLocationOne: '荷兰公园心理健康中心',
	OnboardingReviewLocationThree: '伊甸山医疗保健中心执业护士',
	OnboardingReviewLocationTwo: '生命之家诊所',
	OnboardingReviewNameOne: '阿努尔·P',
	OnboardingReviewNameThree: '爱丽丝',
	OnboardingReviewNameTwo: '克拉拉·W',
	OnboardingReviewOne: '“Carepatron 使用起来非常直观。它帮助我们很好地开展业务，我们甚至不再需要一个管理团队”',
	OnboardingReviewThree: '“无论从功能还是成本来看，这都是我用过的最佳实践解决方案。它具备我发展业务所需的一切”',
	OnboardingReviewTwo: '“我也很喜欢 carepatron 应用程序。它可以帮助我在旅途中跟踪我的客户和工作。”',
	OnboardingTitle: `让我们开始吧<mark>知道
你最好</mark>`,
	Oncologist: '肿瘤科医生',
	Online: '在线的',
	OnlineBookingColorTheme: '在线预订颜色主题',
	OnlineBookings: '网上预订',
	OnlineBookingsHelper: '选择何时可以进行在线预订以及预订的客户类型',
	OnlinePayment: '网上支付',
	OnlinePaymentSettingCustomInfo: '此服务的在线支付设置与全球预订设置不同。',
	OnlinePaymentSettings: '网上支付设置',
	OnlinePaymentSettingsInfo: '在网上预订时收取服务费用，以确保付款安全并简化付款流程',
	OnlinePaymentSettingsPaymentsDisabled:
		'在线预订期间无法使用支付功能，因此无法收取费用。请检查您的支付设置以启用支付功能。',
	OnlinePaymentSettingsStripeNote: '{action} 以接收在线预订付款并简化您的支付流程',
	OnlinePaymentsNotSupportedForCurrency: '{currency} 不支持在线支付。',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: '抱歉，不支持该货币的在线支付',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: '抱歉，您所在的国家/地区尚不支持在线支付',
	OnlineScheduling: '在线调度',
	OnlyVisibleToYou: '仅您可见',
	OnlyYou: '只有你',
	OnsetDate: '发病日期',
	OnsetOfCurrentSymptomsOrIllness: '出现当前症状或疾病',
	Open: '打开',
	OpenFile: '打开文件',
	OpenSettings: '打开设置',
	Ophthalmologist: '眼科医生',
	OptimiseTelehealthCalls: '优化远程医疗呼叫',
	OptimizeServiceTimes: '优化服务时间',
	Options: '选项',
	Optometrist: '验光师',
	Or: '或者',
	OrAttachSingleFile: '附加文件',
	OrDragAndDrop: '或拖放',
	OrderBy: '排序依据',
	Oregon: '俄勒冈州',
	OrganisationOrIndividual: '组织或个人',
	OrganizationPlanInclusion1: '高级权限',
	OrganizationPlanInclusion2: '免费客户数据导入支持',
	OrganizationPlanInclusion3: '专职成功经理',
	OrganizationPlanInclusionHeader: '专业版中的所有内容，另外还有...',
	Orthodontist: '牙齿矫正医师',
	Orthotist: '矫形师',
	Other: '其他',
	OtherAdjustments: '其他调整',
	OtherAdjustmentsTableEmptyState: '未发现任何调整',
	OtherEvents: '其他活动',
	OtherId: '其他身份证',
	OtherIdQualifier: '其他 ID 限定符',
	OtherPaymentMethod: '其他付款方式',
	OtherPlanMessage:
		'掌控您的诊所需求。回顾您的当前计划，监控使用情况，并探索升级选项，以在您的团队发展时解锁更多功能。',
	OtherPolicy: '其他保险',
	OtherProducts: '您还使用哪些其他产品或工具？',
	OtherServices: '其他服务',
	OtherTemplates: '其他模板',
	Others: '其他的',
	OthersPeople: `{n, plural, 		one {1 个其他人}
		other {# 个其他人}
	}`,
	OurResearchTeamReachOut: '我们的研究团队能否与您联系，进一步了解 Carepatron 如何更好地满足您的需求？',
	OutOfOffice: '不在办公室',
	OutOfOfficeColor: '外出时的颜色',
	OutOfOfficeHelper: '部分入选团队成员不在职',
	OutsideLabCharges: '外部实验室费用',
	OutsideOfWorkingHours: '工作时间以外',
	OutsideWorkingHoursHelper: '部分团队成员入选时间不在工作时间',
	Overallocated: '过度分配',
	OverallocatedPaymentDescription: `此付款已超额分配给可计费项目。
为未付款项目添加分配，或发放信用或退款。`,
	OverallocatedPaymentTitle: '超额分配付款',
	OverdueTerm: '逾期期限（天）',
	OverinvoicedAmount: '发票金额过高',
	Overpaid: '付得过多',
	OverpaidAmount: '多付金额',
	Overtime: '随着时间的推移',
	Owner: '所有者',
	POS: '销售点',
	POSCode: 'POS 代码',
	POSPlaceholder: '销售点',
	PageBlockerDescription: '未保存的更改将会丢失。您仍然要离开吗？',
	PageBlockerTitle: '丢弃更改？',
	PageFormat: '页面格式',
	PageNotFound: '未找到页面',
	PageNotFoundDescription: '您不再有权访问此页面或无法找到该页面',
	PageUnauthorised: '未经授权的访问',
	PageUnauthorisedDescription: '您无权访问此页面',
	Paid: '有薪酬的',
	PaidAmount: '已付金额',
	PaidAmountMinimumValueError: '付款金额必须大于0',
	PaidAmountRequiredError: '需要支付金额',
	PaidItems: '付费项目',
	PaidMultiple: '有薪酬的',
	PaidOut: '已付款',
	ParagraphStyles: '段落样式',
	Parent: '父母',
	Paris: '巴黎',
	PartialRefundAmount: '部分退款（剩余 {amount}）',
	PartiallyFull: '部分满',
	PartiallyPaid: '部分付款',
	PartiallyRefunded: '部分退款',
	Partner: '伙伴',
	Password: '密码',
	Past: '过去的',
	PastDateOverridesEmpty: '活动结束后，您的日期覆盖将立即显示在此处',
	Pathologist: '病理学家',
	Patient: '病人',
	Pause: '暂停',
	Paused: '已暂停',
	Pay: '支付',
	PayMonthly: '每月支付',
	PayNow: '立即付款',
	PayValue: '支付 {showPrice, select, true {{price}} other {now}}',
	PayWithOtherCard: '使用其他卡支付',
	PayYearly: '按年支付',
	PayYearlyPercentOff: '按年支付 <mark>{percent}% off</mark>',
	Payer: '付款人',
	PayerClaimId: '付款人索赔 ID',
	PayerCoverage: '覆盖范围',
	PayerDetails: '付款人详细信息',
	PayerDetailsDescription: '查看已添加到您帐户的付款人详细信息并管理注册。',
	PayerID: '付款人 ID',
	PayerId: '付款人 ID',
	PayerName: '付款人姓名',
	PayerPhoneNumber: '付款人电话号码',
	Payers: '付款人',
	Payment: '支付',
	PaymentAccountUpdated: '您的帐户已更新！',
	PaymentAccountUpgraded: '您的帳戶已升級！',
	PaymentAmount: '支付金额',
	PaymentDate: '付款日期',
	PaymentDetails: '支付详情',
	PaymentForUsersPerMonth: '每月为 {billedUsers, plural, one {# 个用户} other {# 个用户}} 付款',
	PaymentInfoFormPrimaryText: '付款信息',
	PaymentInfoFormSecondaryText: '收集付款详细信息',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: '该发票已经付款。',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: '该发票已在处理中。',
	PaymentIntentAmountMismatchSnackbar: '发票总金额已修改。请在付款前检查更改。',
	PaymentIntentSyncTimeoutSnackbar: '您的付款已成功，但发生超时。请刷新页面，如果没有显示您的付款，请联系客服。',
	PaymentMethod: '付款方式',
	PaymentMethodDescription: '添加和管理您的实践付款方式，以简化您的订阅计费流程。',
	PaymentMethodLabelBank: '银行账户',
	PaymentMethodLabelCard: '卡片',
	PaymentMethodLabelFallback: '付款方式',
	PaymentMethodRequired: '更改订阅前请添加付款方式',
	PaymentMethods: '付款方式',
	PaymentProcessing: '付款正在处理！',
	PaymentProcessingFee: '支付包含 {amount} 处理费',
	PaymentReports: '付款报告 (ERA)',
	PaymentSettings: '付款设置',
	PaymentSuccessful: '付款成功！',
	PaymentType: '付款方式',
	Payments: '付款',
	PaymentsAccountDisabledNotificationSubject: `通过 {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} 的在线支付已被禁用。
请检查您的支付设置以启用支付。`,
	PaymentsEmptyStateDescription: '未找到任何付款。',
	PaymentsUnallocated: '未分配付款',
	PayoutDate: '付款日期',
	PayoutsDisabled: '付款已禁用',
	PayoutsEnabled: '已启用付款',
	PayoutsStatus: '付款状态',
	Pediatrician: '儿科医生',
	Pen: '笔',
	Pending: '待办的',
	People: '{rosterSize } 人',
	PeopleCount: '人员 ({count})',
	PerMonth: '/ 月',
	PerUser: '每位用户',
	Permission: '允许',
	PermissionRequired: '需要许可',
	Permissions: '权限',
	PermissionsClientAndContactDocumentation: '客户 ',
	PermissionsClientAndContactProfiles: '客户 ',
	PermissionsEditAccess: '编辑访问权限',
	PermissionsInvoicesAndPayments: '发票 ',
	PermissionsScheduling: '调度',
	PermissionsUnassignClients: '取消分配客户',
	PermissionsUnassignClientsConfirmation: '您确实要取消分配这些客户吗？',
	PermissionsValuesAssigned: '仅分配',
	PermissionsValuesEverything: '一切',
	PermissionsValuesNone: '没有任何',
	PermissionsValuesOwnCalendar: '自己的日历',
	PermissionsViewAccess: '查看访问权限',
	PermissionsWorkspaceSettings: '工作区设置',
	Person: '{rosterSize} 人',
	PersonalDetails: '个人信息',
	PersonalHealthcareHistoryStoreDescription: '回答并安全地存储您的个人医疗历史记录',
	PersonalTrainer: '私人教练',
	PersonalTraining: '个人培训',
	PersonalizeWorkspace: '个性化您的工作空间',
	PersonalizingYourWorkspace: '个性化您的工作空间',
	Pharmacist: '药剂师',
	Pharmacy: '药店',
	PhoneCall: '电话',
	PhoneNumber: '电话号码',
	PhoneNumberOptional: '电话号码（可选）',
	PhotoBy: '摄影',
	PhysicalAddress: '地址',
	PhysicalTherapist: '物理治疗师',
	PhysicalTherapists: '物理治疗师',
	PhysicalTherapy: '物理治疗',
	Physician: '医生',
	PhysicianAssistant: '医师助理',
	Physicians: '医生',
	Physiotherapist: '物理治疗师',
	PlaceOfService: '服务地点',
	Plan: '计划',
	PlanAndReport: '计划/报告',
	PlanId: '计划编号',
	PlansAndReportsCategoryDescription: '用于治疗计划和总结结果',
	PleaseRefreshThisPageToTryAgain: '请刷新此页面以重试。',
	PleaseWait: '请稍等...',
	PleaseWaitForHostToJoin: '等待主持人加入...',
	PleaseWaitForHostToStart: '请等待主持人开始本次会议。',
	PlusAdd: '+ 添加',
	PlusOthers: '+{count} 其他',
	PlusPlanInclusionFive: '共享收件箱',
	PlusPlanInclusionFour: '群组视频通话',
	PlusPlanInclusionHeader: '基本功能  ',
	PlusPlanInclusionOne: '无限人工智能',
	PlusPlanInclusionSix: '定制品牌',
	PlusPlanInclusionThree: '群组调度',
	PlusPlanInclusionTwo: '无限存储 ',
	PlusSubscriptionPlanSubtitle: '为了实践的优化和发展',
	PlusSubscriptionPlanTitle: '加',
	PoliceOfficer: '警官',
	PolicyDates: '政策日期',
	PolicyHolder: '保单持有人',
	PolicyHoldersAddress: '保单持有人地址',
	PolicyMemberId: '政策会员ID',
	PolicyStatus: '策略状态',
	Popular: '受欢迎的',
	PortalAccess: '门户访问',
	PortalNoAppointmentsHeading: '跟踪所有即将到来的和过去的预约',
	PortalNoDocumentationHeading: '安全地创建和存储您的文档',
	PortalNoRelationshipsHeading: '召集那些支持你的旅程的人',
	PosCodeErrorMessage: '需要 POS 代码',
	PosoNumber: 'PO/SO 编号',
	PossibleClientDuplicate: '可能存在客户端重复',
	PotentialClientDuplicateTitle: '可能重复的客户记录',
	PotentialClientDuplicateWarning:
		'此客户信息可能已存在于您的客户列表中。请验证并更新现有记录（如有必要）或继续创建新客户。',
	PoweredBy: '供电',
	Practice: '实践',
	PracticeDetails: '实践细节',
	PracticeInfoHeader: '商业信息',
	PracticeInfoPlaceholder: `实践名称，
国家提供商标识符，
雇主识别号`,
	PracticeLocation: '看起来你的做法是',
	PracticeSettingsAvailabilityTab: '可用性',
	PracticeSettingsBillingTab: '帐单设置',
	PracticeSettingsClientSettingsTab: '客户端设置',
	PracticeSettingsGeneralTab: '一般的',
	PracticeSettingsOnlineBookingTab: '网上预订',
	PracticeSettingsServicesTab: '服务',
	PracticeSettingsTaxRatesTab: '税率',
	PracticeTemplate: '练习模板',
	Practitioner: '从业者',
	PreferredLanguage: '首选语言',
	PreferredName: '首选名称',
	Prescription: '处方',
	PreventionSpecialist: '预防专家',
	Preview: '预览',
	PreviewAndSend: '预览并发送',
	PreviewUnavailable: '无法预览此文件类型',
	PreviousNotes: '先前的注释',
	Price: '价格',
	PriceError: '价格必须大于 0',
	PricePerClient: '每位客户的价格',
	PricePerUser: '每用户',
	PricePerUserBilledAnnually: '按用户每年计费',
	PricePerUserPerPeriod: '{price} 每用户 / {isMonthly, select, true {月} other {年}}',
	PricingGuide: '定价计划指南',
	PricingPlanPerMonth: '/ 月',
	PricingPlanPerYear: '/ 年',
	Primary: '基本的',
	PrimaryInsurance: '主要保险',
	PrimaryPolicy: '主要保险',
	PrimaryTimezone: '主要时区',
	Print: '打印',
	PrintToCms1500: '打印至 CMS1500',
	PrivatePracticeConsultant: '私人执业顾问',
	Proceed: '继续',
	ProcessAtTimeOfBookingDesc: '客户必须支付全额服务费才能在线预订',
	ProcessAtTimeOfBookingLabel: '预订时处理付款',
	Processing: '加工',
	ProcessingFee: '加工费',
	ProcessingFeeToolTip: `Carepatron 允许您向客户收取手续费。
在某些司法管辖区，禁止向您的客户收取手续费。您有责任遵守适用法律。`,
	ProcessingRequest: '正在处理请求...',
	Product: '产品',
	Profession: '职业',
	ProfessionExample: '治疗师、营养师、牙医',
	ProfessionPlaceholder: '开始输入您的职业或从列表中选择',
	ProfessionalPlanInclusion1: '无限存储',
	ProfessionalPlanInclusion2: '无限任务',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7客户支持',
	ProfessionalPlanInclusion5: '短信提醒',
	ProfessionalPlanInclusionHeader: 'Starter 中的所有内容，另外还有...',
	Professions: '职业',
	Profile: '轮廓',
	ProfilePhotoFileSizeLimit: '文件大小限制为 5MB',
	ProfilePopoverSubTitle: '您已登录，用户名为 <strong>{email}</strong>',
	ProfilePopoverTitle: '您的工作空间',
	PromoCode: '促销代码',
	PromotionCodeApplied: '{promo} 已应用',
	ProposeNewDateTime: '建议新的日期/时间',
	Prosthetist: '假肢技师',
	Provider: '提供者',
	ProviderBillingPlanExpansionManageButton: '管理计划',
	ProviderCommercialNumber: '提供商商业号码',
	ProviderDetails: '提供商详细信息',
	ProviderDetailsAddress: '地址',
	ProviderDetailsName: '姓名',
	ProviderDetailsPhoneNumber: '电话号码',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: '抱歉，此提供商已有一个结算帐户',
	ProviderInfoPlaceholder: `员工姓名，
电子邮件，
电话号码，
国家提供商标识符，
執照號碼`,
	ProviderIsChargedProcessingFee: '您将支付手续费',
	ProviderPaymentFormBackButton: '后退',
	ProviderPaymentFormBillingAddressCity: '城市',
	ProviderPaymentFormBillingAddressCountry: '国家',
	ProviderPaymentFormBillingAddressLine1: '1号线',
	ProviderPaymentFormBillingAddressPostalCode: '邮政编码',
	ProviderPaymentFormBillingEmail: '电子邮件',
	ProviderPaymentFormCardCvc: '中央静脉导管',
	ProviderPaymentFormCardDetailsTitle: '信用卡详细信息',
	ProviderPaymentFormCardExpiry: '到期',
	ProviderPaymentFormCardHolderAddressTitle: '地址',
	ProviderPaymentFormCardHolderName: '持卡人姓名',
	ProviderPaymentFormCardHolderTitle: '持卡人详细信息',
	ProviderPaymentFormCardNumber: '卡号',
	ProviderPaymentFormPlanTitle: '选择计划',
	ProviderPaymentFormPlanTotalTitle: '总计 ({currency}):',
	ProviderPaymentFormSaveButton: '保存订阅',
	ProviderPaymentFreePlanDescription:
		'选择免费计划将删除每个员工访问您提供商中客户的权限。但是，您的访问权限将保留，并且您仍然可以使用该平台。',
	ProviderPaymentStepName: '审查 ',
	ProviderPaymentSuccessSnackbar: '太棒了！您的新计划已成功保存。',
	ProviderPaymentTitle: '审查 ',
	ProviderPlanNetworkIdentificationNumber: '提供商计划网络识别号',
	ProviderRemindersSettingsBannerAction: '转到工作流管理',
	ProviderRemindersSettingsBannerDescription:
		'在**设置**中的新**工作流程管理**标签下找到所有提醒。此更新带来了强大的新功能、改进的模板和更智能的自动化工具，以提高您的工作效率。🚀',
	ProviderRemindersSettingsBannerTitle: '您的提醒体验正在变得更好',
	ProviderTaxonomy: '提供商分类',
	ProviderUPINNumber: '提供商 UPIN 号码',
	ProviderUsedStoragePercentage: '{providerName} 存储已使用 {usedStoragePercentage}%！',
	PsychiatricNursePractitioner: '精神科执业护士',
	Psychiatrist: '精神科医生',
	Psychiatrists: '精神科医生',
	Psychiatry: '精神病学',
	Psychoanalyst: '精神分析师',
	Psychologist: '心理学家',
	Psychologists: '心理学家',
	Psychology: '心理学',
	Psychometrician: '心理测量学家',
	PsychosocialRehabilitationSpecialist: '心理康复专家',
	Psychotheraphy: '心理治疗',
	Psychotherapists: '心理治疗师',
	Psychotherapy: '心理治疗',
	PublicCallDialogTitle: '与 ',
	PublicCallDialogTitlePlaceholder: '由 Carepatron 支持的视频通话',
	PublicFormBackToForm: '提交另一个回复',
	PublicFormConfirmSubmissionHeader: '确认提交',
	PublicFormNotFoundDescription: '您要查找的表单可能已被删除，或链接可能不正确。请检查 URL 并重试。',
	PublicFormNotFoundTitle: '找不到表格',
	PublicFormSubmissionError: '提交失败。请重试。',
	PublicFormSubmissionSuccess: '表单提交成功',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} 提交了 {noteTitle} 公共表单',
	PublicFormSubmittedSubtitle: '您的提交已收到。',
	PublicFormSubmittedTitle: '谢谢你！',
	PublicFormVerifyClientEmailDialogSubtitle: '我们已将确认码发送到您的邮箱。',
	PublicFormsInvalidConfirmationCode: '无效的确认码',
	PublicHealthInspector: '公共卫生督察',
	PublicTemplates: '公共模板',
	Publish: '发布',
	PublishTemplate: '发布模板',
	PublishTemplateFeatureBannerSubheader: '旨在造福社区的模板',
	PublishTemplateHeader: '发布 {title}',
	PublishTemplateToCommunity: '发布模板到社区',
	PublishToCommunity: '发布到社区',
	PublishToCommunitySuccessMessage: '成功发布到社区',
	Published: '发布',
	PublishedBy: '由 {name} 发布',
	PublishedNotesAreNotAutosaved: '已发布的笔记不会自动保存',
	PublishedOnCarepatronCommunity: '发布在 Carepatron 社区',
	Purchase: '购买',
	PushToCalendar: '推送至日历',
	Question: '问题',
	QuestionOrTitle: '问题或标题',
	QuickActions: '快速操作',
	QuickThemeSwitcherColorBasil: '罗勒',
	QuickThemeSwitcherColorBlueberry: '蓝莓',
	QuickThemeSwitcherColorFushcia: '紫红色',
	QuickThemeSwitcherColorLapis: '青金石',
	QuickThemeSwitcherColorMoss: '苔藓',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: '壁球',
	RadiationTherapist: '放射治疗师',
	Radiologist: '放射科医生',
	Read: '读',
	ReadOnly: '只读',
	ReadOnlyAppointment: '只读预约',
	ReadOnlyEventBanner: '此预约已从只读日历同步，无法编辑。',
	ReaderMaxDepthHasBeenExceededCode: '注释嵌套过多。请尝试取消缩进某些项目。',
	ReadyForMapping: '准备好进行映射',
	RealEstateAgent: '房地产经纪人',
	RearrangeClientFields: '在客户端设置中重新排列客户端字段',
	Reason: '原因',
	ReasonForChange: '更改原因',
	RecentAppointments: '近期任命',
	RecentServices: '最近的服务',
	RecentTemplates: '最近使用的模板',
	RecentlyUsed: '最近使用',
	Recommended: '推荐',
	RecommendedTemplates: '推荐模板',
	Recording: '记录',
	RecordingEnded: '录制结束',
	RecordingInProgress: '正在录制',
	RecordingMicrophoneAccessErrorMessage: '请在浏览器中允许麦克风访问，然后刷新以开始录音。',
	RecurrenceCount: ', {count, plural, one {一次} other {# 次}}',
	RecurrenceDaily: '{count, plural, one {每天} other {天}}',
	RecurrenceEndAfter: '之后',
	RecurrenceEndNever: '从不',
	RecurrenceEndOn: '在',
	RecurrenceEvery: '每个 {description}',
	RecurrenceMonthly: '{count, plural, one {每月} other {月}}',
	RecurrenceOn: '在 {description}',
	RecurrenceOnAllDays: '所有日子',
	RecurrenceUntil: '直到 {description}',
	RecurrenceWeekly: '{count, plural, one {每周} other {周}}',
	RecurrenceYearly: '{count, plural, one {每年} other {年}}',
	Recurring: '再次发生的',
	RecurringAppointment: '定期预约',
	RecurringAppointmentsLimitedBannerText: '并非所有定期约会均会显示。缩小日期范围可查看该期间的所有定期约会。',
	RecurringEventListDescription: '<b>{count, plural, one {# 个事件} other {# 个事件}}</b> 将在以下日期创建',
	Redo: '重做',
	ReferFriends: '推荐朋友',
	Reference: '参考',
	ReferralCreditedNotificationSubject: '您的推荐积分 {currency} {amount} 已被应用',
	ReferralEmailDefaultBody: `感谢 {name}，您已获得 Carepatron 3 个月的免费升级。加入我们拥有超过 300 万名医疗从业人员的社区，体验全新的工作方式！
感谢，
Carepatron 团队`,
	ReferralEmailDefaultSubject: '您已受邀加入 Carepatron',
	ReferralHasNotSignedUpDescription: '您的朋友尚未注册',
	ReferralHasSignedUpDescription: '您的朋友已报名。',
	ReferralInformation: '转介信息',
	ReferralJoinedNotificationSubject: '{actorProfileName} 已加入 Carepatron',
	ReferralListErrorDescription: '无法加载引荐列表。',
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {个月} other {个月}}</b> 活跃',
	ReferralRewardBanner: '注册并领取您的推荐奖励！',
	Referrals: '推荐',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} 个月 {percentOff, select, 100 {免费支付} other {{percentOff}% 折扣}} {type, select, SubscriptionUpgrade {升级} other {}}',
	ReferredUserBenefitTitle: '他们得到了！',
	Referrer: '引荐来源',
	ReferringProvider: '引荐提供者',
	ReferringUserBenefitSubtitle: '当<mark>3 位朋友</mark>激活时，获得 USD${creditAmount} 积分。',
	ReferringUserBenefitTitle: '你明白了！',
	RefreshPage: '刷新页面',
	Refund: '退款',
	RefundAcknowledgement: '我已经在 Carepatron 之外为 {clientName} 退款了',
	RefundAcknowledgementValidationMessage: '请确认您已退还此金额',
	RefundAmount: '退款金额',
	RefundContent:
		'退款需要 7-10 天才能显示在您的客户帐户中。付款费用不会退还，但退款无需额外费用。退款无法取消，部分退款可能需要审核后才能处理。',
	RefundCouldNotBeProcessed: '无法处理退款',
	RefundError: '此退款目前无法自动处理。请联系 Carepatron 支持部门申请退还此付款。',
	RefundExceedTotalValidationError: '金额不得超过已付总额',
	RefundFailed: '退款失败',
	RefundFailedTooltip: '此付款的退款先前失败，无法重试。请联系支持人员。',
	RefundNonStripePaymentContent:
		'此付款是使用 Carepatron 以外的方式（例如现金、网上银行）进行的。在 Carepatron 内发出退款不会向客户退还任何资金。',
	RefundReasonDescription: '添加退款原因有助于审查客户交易',
	Refunded: '已退款',
	Refunds: '退款',
	RefundsTableEmptyState: '未找到退款',
	Regenerate: '重新生成',
	RegisterButton: '登记',
	RegisterEmail: '电子邮件',
	RegisterFirstName: '名',
	RegisterLastName: '姓',
	RegisterPassword: '密码',
	RegisteredNurse: '注册护士',
	RehabilitationCounselor: '康复顾问',
	RejectAppointmentFormTitle: '不能参加？请告知我们原因并建议新的时间。',
	Rejected: '已拒绝',
	Relationship: '关系',
	RelationshipDetails: '关系详细信息',
	RelationshipEmptyStateTitle: '与支持您的客户的人保持联系',
	RelationshipPageAccessTypeColumnName: '个人资料访问',
	RelationshipSavedSuccessSnackbar: '关系保存成功！',
	RelationshipSelectorFamilyAdmin: '家庭',
	RelationshipSelectorFamilyMember: '家属',
	RelationshipSelectorProviderAdmin: '提供商管理员',
	RelationshipSelectorProviderStaff: '供应商人员',
	RelationshipSelectorSupportNetworkPrimary: '朋友',
	RelationshipSelectorSupportNetworkSecondary: '支持网络',
	RelationshipStatus: '感情状态',
	RelationshipType: '关系类型',
	RelationshipTypeClientOwner: '客户',
	RelationshipTypeFamilyAdmin: '关系',
	RelationshipTypeFamilyMember: '家庭',
	RelationshipTypeFriendOrSupport: '朋友或支持网络',
	RelationshipTypeProviderAdmin: '提供商管理员',
	RelationshipTypeProviderStaff: '职员',
	RelationshipTypeSelectorPlaceholder: '搜索关系类型',
	Relationships: '关系',
	Remaining: '其余的',
	RemainingTime: '剩余{time}',
	Reminder: '提醒',
	ReminderColor: '提醒颜色',
	ReminderDetails: '提醒详情',
	ReminderEditDisclaimer: '变化只会反映在新的任命中',
	ReminderSettings: '预约提醒设置',
	Reminders: '提醒',
	Remove: '消除',
	RemoveAccess: '删除访问权限',
	RemoveAllGuidesBtn: '删除所有指南',
	RemoveAllGuidesPopoverBody: '完成入职指南后，只需使用每个面板上的删除指南按钮即可。',
	RemoveAllGuidesPopoverTitle: '不再需要您的入职指南吗？',
	RemoveAsDefault: '删除默认',
	RemoveAsIntake: '移除作为摄入量',
	RemoveCol: '删除列',
	RemoveColor: '移除颜色',
	RemoveField: '移除字段',
	RemoveFromCall: '从通话中删除',
	RemoveFromCallDescription: '您确定要从该视频通话中删除 <mark>{attendeeName}</mark> 吗？',
	RemoveFromCollection: '从收藏夹中移除',
	RemoveFromCommunity: '从社区中移除',
	RemoveFromFolder: '从文件夹中删除',
	RemoveFromFolderConfirmationDescription:
		'您确定要从该文件夹中删除此模板吗？ 此操作不可撤消，但您可以在以后选择将其移回。',
	RemoveFromIntakeDefault: '从进气默认值中删除',
	RemoveGuides: '移除参考线',
	RemoveMfaConfirmationDescription: '删除多重身份验证 (MFA) 将降低您帐户的安全性。要继续吗？',
	RemoveMfaConfirmationTitle: '删除 MFA？',
	RemovePaymentMethodDescription: `这将删除此付款方式的所有访问和未来使用。
此操作无法撤消。`,
	RemoveRow: '删除行',
	RemoveTable: '移除表格',
	RemoveTemplateAsDefaultIntakeSuccess: '已成功移除 {templateTitle} 作为默认入职模板',
	RemoveTemplateFromCommunity: '从社区中删除模板',
	RemoveTemplateFromFolder: '{templateTitle} 已成功从 {folderTitle} 中删除',
	Rename: '重命名',
	RenderingProvider: '渲染提供者',
	Reopen: '重新开放',
	ReorderServiceGroupFailure: '无法重新排序收藏',
	ReorderServiceGroupSuccess: '已成功重新排序收藏集',
	ReorderServicesFailure: '无法重新排序服务',
	ReorderServicesSuccess: '成功重新排序服务',
	ReorderYourServiceList: '重新排序您的服务列表',
	ReorderYourServiceListDescription: '您组织服务和收藏的方式将反映在您的在线预订页面上，供所有客户查看！',
	RepeatEvery: '重复每',
	RepeatOn: '重复',
	Repeating: '重复',
	Repeats: '重复',
	RepeatsEvery: '每 <br>',
	Rephrase: '重新表述',
	Replace: '代替',
	ReplaceBackground: '替换背景',
	ReplacementOfPriorClaim: '替换先前的索赔',
	Report: '报告',
	Reprocess: '重新处理',
	RepublishTemplateToCommunity: '重新发布模板到社区',
	RequestANewVerificationLink: '请求新的验证链接',
	RequestCoverageReport: '请求覆盖率报告',
	RequestingDevicePermissions: '正在请求设备权限...',
	RequirePaymentMethodDesc: '客户必须输入信用卡详细信息才能在线预订',
	RequirePaymentMethodLabel: '需要信用卡详细信息',
	Required: '必需的',
	RequiredField: '必需的',
	RequiredUrl: '需要 URL。',
	Reschedule: '重新安排',
	RescheduleBookingLinkModalDescription: '您的客户可以使用此链接更改他们的预约日期和时间。',
	RescheduleBookingLinkModalTitle: '重新安排预订链接',
	RescheduleLink: '重新安排链接',
	Resend: '重新发送',
	ResendConfirmationCode: '重新发送确认码',
	ResendConfirmationCodeDescription: '请输入您的电子邮件地址，我们将通过电子邮件向您发送另一个确认码',
	ResendConfirmationCodeSuccess: '确认码已重新发送，请检查您的收件箱',
	ResendNewEmailVerificationSuccess: '新的验证链接已发送至 {email}',
	ResendVerificationEmail: '重新发送验证邮件',
	Reset: '重置',
	Resources: '资源',
	RespiratoryTherapist: '呼吸治疗师',
	RespondToHistoricAppointmentError: '这是一次历史性的约会，如果您有任何疑问，请联系您的医生。',
	Responder: '响应者',
	RestorableItemModalDescription:
		'您确定要删除 {context} 吗？{canRestore, select, true { 您可以在之后恢复它。} other {}}',
	RestorableItemModalTitle: '删除 {type}',
	Restore: '恢复',
	RestoreAll: '全部恢复',
	Restricted: '受限制的',
	ResubmissionCodeReferenceNumber: '重新提交代码和参考编号',
	Resubmit: '重新提交',
	Resume: '恢复',
	Retry: '重试',
	RetryingConnectionAttempt: '正在重试连接… (第 {retryCount} 次尝试，共 {maxRetries} 次)',
	ReturnToForm: '返回表格',
	RevertClaimStatus: '撤销索赔状态',
	RevertClaimStatusDescriptionBody: '该索赔已链接到付款，更改状态可能会影响付款跟踪或处理，这可能需要手动对账。',
	RevertClaimStatusDescriptionTitle: '您确定要还原到 {status} 吗？',
	RevertClaimStatusError: '未能撤销索赔状态',
	RevertToDraft: '恢复为草稿',
	Review: '审查',
	ReviewsFirstQuote: '随时随地预约',
	ReviewsSecondJobTitle: '生命之家诊所',
	ReviewsSecondName: '克拉拉·W.',
	ReviewsSecondQuote: '我也很喜欢 carepatron 应用程序。它帮助我随时随地跟踪客户和工作。',
	ReviewsThirdJobTitle: '马尼拉湾诊所',
	ReviewsThirdName: '杰基 H.',
	ReviewsThirdQuote: '便捷的导航和美观的用户界面每天都让我笑容满面。',
	RightAlign: '右对齐',
	Role: '角色',
	Roster: '出席者',
	RunInBackground: '在后台运行',
	SMS: '短信',
	SMSAndEmailReminder: '短信 ',
	SSN: '社保号',
	SafetyRedirectHeading: '您将离开 Carepatron',
	SafetyRedirectSubtext: '如果您信任此链接，请选择它以继续',
	SalesRepresentative: '销售代表',
	SalesTax: '销售税',
	SalesTaxHelp: '生成的发票包含销售税',
	SalesTaxIncluded: '是的',
	SalesTaxNotIncluded: '不',
	SaoPaulo: '圣保罗',
	Saturday: '周六',
	Save: '保存',
	SaveAndClose: '保存并关闭',
	SaveAndExit: '保存并退出',
	SaveAndLock: '保存并锁定',
	SaveAsDraft: '保存为草稿',
	SaveCardForFuturePayments: '保存卡以便将来付款',
	SaveChanges: '保存更改',
	SaveCollection: '保存收藏',
	SaveField: '保存字段',
	SavePaymentMethod: '保存付款方式',
	SavePaymentMethodDescription: '在您第一次预约之前我们不会向您收取任何费用。',
	SavePaymentMethodSetupError: '发生意外错误，我们目前无法配置付款。',
	SavePaymentMethodSetupInvoiceLater: '在支付第一张发票时可以设置并保存付款。',
	SaveSection: '保存部分',
	SaveService: '创建新服务',
	SaveTemplate: '保存模板',
	Saved: '已保存',
	SavedCards: '已保存的卡片',
	SavedPaymentMethods: '已保存',
	Saving: '保存...',
	ScheduleAppointmentsAndOnlineServices: '安排预约和在线服务',
	ScheduleName: '计划名称',
	ScheduleNew: '安排新',
	ScheduleSend: '安排发送',
	ScheduleSendAlertInfo: '预定的对话将会在预定的时间发送。',
	ScheduleSendByName: '<strong>计划发送</strong> • {time} 由 {displayName}',
	ScheduleSetupCall: '安排设置通话',
	Scheduled: '已安排',
	SchedulingSend: '安排发送',
	School: '学校',
	ScrollToTop: '滚动至顶部',
	Search: '搜索',
	SearchAndConvertToLanguage: '搜索和转换语言',
	SearchBasicBlocks: '搜索基本块',
	SearchByName: '按名称搜索',
	SearchClaims: '搜索索赔',
	SearchClientFields: '搜索客户字段',
	SearchClients: '按客户姓名、客户 ID 或电话号码搜索',
	SearchCommandNotFound: '未找到与“{searchTerm}”匹配的结果',
	SearchContacts: '客户或联系人',
	SearchContactsPlaceholder: '搜索联系人',
	SearchConversations: '搜索对话',
	SearchInputPlaceholder: '搜索所有资源',
	SearchInvoiceNumber: '搜索发票号码',
	SearchInvoices: '搜索发票',
	SearchMultipleContacts: '客户或联系人',
	SearchMultipleContactsOptional: '客户或联系人（可选）',
	SearchOrCreateATag: '搜索或创建标签',
	SearchPayments: '搜索付款',
	SearchPrepopulatedData: '搜索预填充的数据字段',
	SearchRelationships: '搜索关系',
	SearchRemindersAndWorkflows: '搜索提醒和工作流程',
	SearchServices: '搜索服务',
	SearchTags: '搜索标签',
	SearchTeamMembers: '搜索团队成员',
	SearchTemplatePlaceholder: '搜索 {templateCount}+ 资源',
	SearchTimezone: '搜索时区...',
	SearchTrashItems: '搜索项目',
	SearchUnsplashPlaceholder: '从 Unsplash 搜索免费的高分辨率照片',
	Secondary: '次要',
	SecondaryInsurance: '次要保险',
	SecondaryPolicy: '次要保险',
	SecondaryTimezone: '次要时区',
	Secondly: '其次',
	Section: '部分',
	SectionCannotBeEmpty: '每个部分必须至少有一行',
	SectionFormSecondaryText: '章节标题和说明',
	SectionName: '部分名称',
	Sections: '部分',
	SeeLess: '查看较少内容',
	SeeLessUpcomingAppointments: '查看较少的近期预约',
	SeeMore: '查看更多',
	SeeMoreUpcomingAppointments: '查看更多即将到来的预约',
	SeeTemplateLibrary: '查看模板库',
	Seen: '已看过',
	SeenByName: '**已查看** • {time} 由 {displayName}',
	SelectAll: '选择全部',
	SelectAssignees: '选择受让人',
	SelectAttendees: '选择与会者',
	SelectCollection: '选择收藏',
	SelectCorrespondingAttributes: '选择相应属性',
	SelectPayers: '选择支付方',
	SelectProfile: '选择个人资料',
	SelectServices: '选择服务',
	SelectTags: '选择标签',
	SelectTeamOrCommunity: '选择团队或社区',
	SelectTemplate: '选择模板',
	SelectType: '选择类型',
	Selected: '选定',
	SelfPay: '自付',
	Send: '发送',
	SendAndClose: '发送 ',
	SendAndStopIgnore: '发送并停止忽略',
	SendEmail: '发送邮件',
	SendIntake: '发送摄入量',
	SendIntakeAndForms: '发送摄入量 ',
	SendMeACopy: '发给我一份',
	SendNotificationEmailWarning: '一些与会者没有电子邮件地址，将不会收到自动通知和提醒。',
	SendNotificationLabel: '选择要通过电子邮件通知的与会者',
	SendOnlinePayment: '发送网上付款',
	SendOnlinePaymentTooltipTitleAdmin: '请添加您喜欢的付款设置',
	SendOnlinePaymentTooltipTitleStaff: '请要求提供商所有者设置在线支付。',
	SendPaymentLink: '发送付款链接',
	SendReaction: '发送回应',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: '发送验证电子邮件',
	SendingFailed: '发送失败',
	Sent: '发送',
	SentByName: '**已发送** • {time}  由 {displayName}',
	Seoul: '汉城',
	SeparateDuplicateClientsDescription: '除非您选择合并所选客户记录，否则所选客户记录将与其他记录分开',
	Service: '服务',
	'Service/s': '服务',
	ServiceAdjustment: '服务调整',
	ServiceAllowNewClientsIndicator: '允许新客户',
	ServiceAlreadyExistsInCollection: '服务已存在于集合中',
	ServiceBookableOnlineIndicator: '可在线预订',
	ServiceCode: '代码',
	ServiceCodeErrorMessage: '需要服务代码',
	ServiceCodeSelectorPlaceholder: '添加服务代码',
	ServiceColour: '服务颜色',
	ServiceCoverageDescription: '选择此保险单的合格服务并共同支付。',
	ServiceCoverageGoToServices: '前往服务',
	ServiceCoverageNoServicesDescription: '自定义服务共付额以覆盖默认保单共付额。禁用保险范围以防止根据保单索赔服务。',
	ServiceCoverageNoServicesLabel: '未找到任何服务。',
	ServiceCoverageTitle: '服务范围',
	ServiceDate: '服务日期',
	ServiceDetails: '服务详情',
	ServiceDuration: '期间',
	ServiceEmptyState: '尚无任何服务',
	ServiceErrorMessage: '需要服务',
	ServiceFacility: '服务设施',
	ServiceName: '服务名称',
	ServiceRate: '速度',
	ServiceReceiptRequiresReviewNotificationSubject:
		'超级账单 {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}}  为 {serviceReceiptNumber, select, undefined {用户} other {{clientName}}} 需要额外信息',
	ServiceSalesTax: '销售税',
	ServiceType: '服务',
	ServiceWorkerForceUIUpdateDialogDescription: '点击重新加载以刷新并获取最新的 Carepatron 更新。',
	ServiceWorkerForceUIUpdateDialogReloadButton: '重新加载',
	ServiceWorkerForceUIUpdateDialogSubTitle: '您正在使用旧版本',
	ServiceWorkerForceUIUpdateDialogTitle: '欢迎回来！',
	Services: '服务',
	ServicesAndAvailability: '服务 ',
	ServicesAndDiagnosisCodesHeader: '添加服务和诊断代码',
	ServicesCount: '{count,plural,=0{服务}one{服务}other{服务}}',
	ServicesPlaceholder: '服务',
	ServicesProvidedBy: '服务提供者',
	SetAPhysicalAddress: '设置物理地址',
	SetAVirtualLocation: '设置虚拟位置',
	SetAsDefault: '设为默认值',
	SetAsIntake: '设置为进气',
	SetAsIntakeDefault: '设置为进气默认值',
	SetAvailability: '设置可用性',
	SetTemplateAsDefaultIntakeSuccess: '成功将 {templateTitle} 设置为默认收件模板',
	SetUpMfaButton: '设置 MFA',
	SetYourLocation: '设置您的<mark>地点</mark>',
	SetYourLocationDescription: '我没有公司地址<span>（仅限在线和移动服务）</span>',
	SettingUpPayers: '设置付款人',
	Settings: '设置',
	SettingsNewUserPasswordDescription: '注册后，我们将向您发送确认码，您可以使用该确认码来确认您的帐户',
	SettingsNewUserPasswordTitle: '注册 Carepatron',
	SettingsTabAutomation: '自动化',
	SettingsTabBillingDetails: '账单详细信息',
	SettingsTabConnectedApps: '连接的应用程序',
	SettingsTabCustomFields: '自定义字段',
	SettingsTabDetails: '细节',
	SettingsTabInvoices: '发票',
	SettingsTabLocations: '位置',
	SettingsTabNotifications: '通知',
	SettingsTabOnlineBooking: '网上预订',
	SettingsTabPayers: '付款人',
	SettingsTabReminders: '提醒',
	SettingsTabServices: '服务',
	SettingsTabServicesAndAvailability: '服务和可用性',
	SettingsTabSubscriptions: '订阅',
	SettingsTabWorkflowAutomations: '自动化',
	SettingsTabWorkflowReminders: '基本提醒',
	SettingsTabWorkflowTemplates: '模板',
	Setup: '设置',
	SetupGuide: '设置指南',
	SetupGuideAddServicesActionLabel: '开始',
	SetupGuideAddServicesSubtitle: '4 步 • 2 分钟',
	SetupGuideAddServicesTitle: '添加您的服务',
	SetupGuideEnableOnlinePaymentsActionLabel: '开始',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 步 • 3 分钟',
	SetupGuideEnableOnlinePaymentsTitle: '启用在线支付',
	SetupGuideImportClientsActionLabel: '开始',
	SetupGuideImportClientsSubtitle: '4 步 • 3 分钟',
	SetupGuideImportClientsTitle: '导入您的客户',
	SetupGuideImportTemplatesActionLabel: '开始',
	SetupGuideImportTemplatesSubtitle: '2 步 • 1 分钟',
	SetupGuideImportTemplatesTitle: '导入您的模板',
	SetupGuidePersonalizeWorkspaceActionLabel: '开始',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 步 • 2 分钟',
	SetupGuidePersonalizeWorkspaceTitle: '个性化您的工作区',
	SetupGuideSetLocationActionLabel: '开始',
	SetupGuideSetLocationSubtitle: '4 步 • 1 分钟',
	SetupGuideSetLocationTitle: '设置您的位置',
	SetupGuideSuggestedAddTeamMembersActionLabel: '邀请团队',
	SetupGuideSuggestedAddTeamMembersSubtitle: '邀请您的团队轻松地沟通和管理任务。',
	SetupGuideSuggestedAddTeamMembersTag: '设置',
	SetupGuideSuggestedAddTeamMembersTitle: '添加团队成员',
	SetupGuideSuggestedCustomizeBrandActionLabel: '自定义',
	SetupGuideSuggestedCustomizeBrandSubtitle: '使用您独特的徽标和品牌颜色，展现专业风范。',
	SetupGuideSuggestedCustomizeBrandTitle: '定制品牌',
	SetupGuideSuggestedDownloadMobileAppActionLabel: '下载',
	SetupGuideSuggestedDownloadMobileAppSubtitle: '随时随地，在任何设备上访问您的工作区。',
	SetupGuideSuggestedDownloadMobileAppTag: '设置',
	SetupGuideSuggestedDownloadMobileAppTitle: '下载应用程序',
	SetupGuideSuggestedEditAvailabilityActionLabel: '设置可用性',
	SetupGuideSuggestedEditAvailabilitySubtitle: '设置您的可用时间，防止双重预订。',
	SetupGuideSuggestedEditAvailabilityTag: '排班',
	SetupGuideSuggestedEditAvailabilityTitle: '编辑可用性',
	SetupGuideSuggestedImportClientsActionLabel: '导入',
	SetupGuideSuggestedImportClientsSubtitle: '只需点击一下，即可立即上传您现有的客户数据。',
	SetupGuideSuggestedImportClientsTag: '设置',
	SetupGuideSuggestedImportClientsTitle: '导入客户',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: '编辑提醒',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: '通过自动预约提醒减少爽约。',
	SetupGuideSuggestedPersonalizeRemindersTitle: '个性化提醒',
	SetupGuideSuggestedStartVideoCallActionLabel: '开始通话',
	SetupGuideSuggestedStartVideoCallSubtitle: '使用我们的 AI 驱动的视频工具来主持通话并与客户建立联系。',
	SetupGuideSuggestedStartVideoCallTag: '远程医疗',
	SetupGuideSuggestedStartVideoCallTitle: '开始视频通话',
	SetupGuideSuggestedTryActionsTitle: '尝试一下 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: '尝试 AI 助手',
	SetupGuideSuggestedUseAIAssistantSubtitle: '获取所有工作问题的即时答案。',
	SetupGuideSuggestedUseAIAssistantTag: '新',
	SetupGuideSuggestedUseAIAssistantTitle: '使用 AI 助手',
	SetupGuideSyncCalendarActionLabel: '开始',
	SetupGuideSyncCalendarSubtitle: '1 步 • 少于 1 分钟',
	SetupGuideSyncCalendarTitle: '同步您的日历',
	SetupGuideVerifyEmailLabel: '验证',
	SetupGuideVerifyEmailSubtitle: '2 步 • 2 分钟',
	SetupOnlineStripePayments: '使用 Stripe 进行在线支付',
	SetupPayments: '设置付款',
	Sex: '性别',
	SexSelectorPlaceholder: '男 / 女 / 不愿透露',
	Share: '分享',
	ShareBookingLink: '分享预订链接',
	ShareNoteDefaultMessage: `您好{name} 与您分享了 "{documentName}"。

谢谢，
{practiceName}`,
	ShareNoteMessage: `您好
{name} 分享了 "{documentName}" {isResponder, select, true {，其中包含一些需要您填写的问题。} other {给您。}}

谢谢，
{practiceName}`,
	ShareNoteTitle: '分享“{noteTitle}”',
	ShareNotesWithClients: '与客户或联系人共享',
	ShareScreen: '共享屏幕',
	ShareScreenNotSupported: '您的设备/浏览器不支持共享屏幕功能',
	ShareScreenWithId: '屏幕 {screenId}',
	ShareTemplateAsPublicFormModalDescription: '允许其他人查看此模板并将其提交为表单。',
	ShareTemplateAsPublicFormModalTitle: '分享 ‘{title}’ 的链接',
	ShareTemplateAsPublicFormSaved: '公共表单配置已成功更新',
	ShareTemplateAsPublicFormSectionCustomization: '自定义',
	ShareTemplateAsPublicFormShowPoweredBy: '在表单上显示“Powered by Carepatron”',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: '在表单上显示/隐藏“Powered by Carepatron”',
	ShareTemplateAsPublicFormTrigger: '分享',
	ShareTemplateAsPublicFormUseWorkspaceBranding: '使用工作区品牌',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: '显示/隐藏工作区品牌',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: '发送现有和不存在的客户的代码',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: '签名始终需要验证电子邮件。',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: '仅为现有客户发送代码',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: '从不发送代码',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'选择“从不”可能会允许未经验证的用户使用现有客户的电子邮件地址覆盖客户数据。',
	ShareWithCommunity: '与社区分享',
	ShareYourReferralLink: '分享您的推荐链接',
	ShareYourScreen: '共享您的屏幕',
	SheHer: '她/她',
	ShortTextAnswer: '简短的文字答案',
	ShortTextFormPrimaryText: '短文本',
	ShortTextFormSecondaryText: '少于 300 个字符的答案',
	Show: '展示',
	ShowColumn: '显示栏',
	ShowColumnButton: '显示列 {value} 按钮',
	ShowColumns: '显示列',
	ShowColumnsMenu: '显示列菜单',
	ShowDateDurationDescription: '例如 29 岁',
	ShowDateDurationLabel: '显示日期时长',
	ShowDetails: '显示详细信息',
	ShowField: '展示场地',
	ShowFullAddress: '显示地址',
	ShowHideFields: '显示/隐藏字段',
	ShowIcons: '显示图标',
	ShowLess: '显示较少',
	ShowMeetingTimers: '显示会议计时器',
	ShowMenu: '显示菜单',
	ShowMergeSummarySidebar: '显示合并摘要',
	ShowMore: '显示更多',
	ShowOnTranscript: '在成绩单上显示',
	ShowReactions: '显示反应',
	ShowSection: '显示部分',
	ShowServiceCode: '显示服务代码',
	ShowServiceDescription: '显示服务预订说明',
	ShowServiceDescriptionDesc: '客户可以在预订时查看服务说明',
	ShowServiceGroups: '显示系列',
	ShowServiceGroupsDesc: '预订时，客户将看到按集合分组的服务',
	ShowSpeakers: '显示发言人',
	ShowTax: '显示税',
	ShowTimestamp: '显示时间戳',
	ShowUnits: '显示单位',
	ShowWeekends: '显示周末',
	ShowYourView: '展现你的观点',
	SignInWithApple: '使用 Apple 登录',
	SignInWithGoogle: '使用 Google 登录',
	SignInWithMicrosoft: '使用 Microsoft 登录',
	SignUpTitleReferralDefault: '<mark>报名</mark>并领取您的推荐奖励',
	SignUpTitleReferralUpgrade:
		'开始您 {durationInMonths} 个月的 <mark>{percentOff, select, 100 {免费} other {{percentOff}% 折扣}} 升级</mark>',
	SignatureCaptureError: '无法捕获签名。请重试。',
	SignatureFormPrimaryText: '签名',
	SignatureFormSecondaryText: '获取数字签名',
	SignatureInfoTooltip: '该视觉表现形式不是有效的电子签名。',
	SignaturePlaceholder: '在这里签名',
	SignedBy: '签署人',
	Signup: '报名',
	SignupAgreements: '我同意我的账户的 {termsOfUse} 和 {privacyStatement}。',
	SignupBAA: '商业伙伴协议',
	SignupBusinessAgreements:
		'我代表我自己和公司，同意我的帐户的 {businessAssociateAgreement}、{termsOfUse} 和 {privacyStatement}。',
	SignupInvitationForYou: '您已被邀请使用 Carepatron。',
	SignupPageProviderWarning:
		'如果您的管理员已经创建了帐户，您需要请他们邀请您加入该提供商。请勿使用此注册表单。有关更多信息，请参阅',
	SignupPageProviderWarningLink: '这个链接。',
	SignupPrivacy: '隐私政策',
	SignupProfession: '哪种职业最适合描述您？',
	SignupSubtitle: 'Carepatron 的执业管理软件专为个人执业者和团队而设计。停止支付过高的费用，加入这场革命吧。',
	SignupSuccessDescription: '确认您的电子邮件地址以开始入职。如果您没有立即收到，请检查您的垃圾邮件文件夹。',
	SignupSuccessTitle: '请查看你的电子邮件',
	SignupTermsOfUse: '使用条款',
	SignupTitleClient: '<mark>管理你的健康</mark>从一个地方',
	SignupTitleLast: '以及您所做的所有工作！——免费',
	SignupTitleOne: '<mark>为您提供动力</mark>， ',
	SignupTitleThree: '<mark>为您的客户提供支持</mark>， ',
	SignupTitleTwo: '<mark>为你的团队提供动力</mark>， ',
	Simple: '简单的',
	SimplifyBillToDetails: '简化账单明细',
	SimplifyBillToHelperText: '仅当与客户端匹配时才使用第一行',
	Singapore: '新加坡',
	Single: '单身的',
	SingleChoiceFormPrimaryText: '单选',
	SingleChoiceFormSecondaryText: '仅选择一个选项',
	Sister: '姐姐',
	SisterInLaw: '嫂子',
	Skip: '跳过',
	SkipLogin: '跳过登录',
	SlightBlur: '稍微模糊一下背景',
	Small: '小的',
	SmartChips: '智能芯片',
	SmartDataChips: '智能数据芯片',
	SmartReply: '智能回复',
	SmartSuggestNewClient: '<strong>智能推荐</strong> 创建 {name} 作为新客户',
	SmartSuggestedFieldDescription: '这个字段是智能建议',
	SocialSecurityNumber: '社会保障号码',
	SocialWork: '社会工作',
	SocialWorker: '社会工作者',
	SoftwareDeveloper: '软件开发人员',
	Solo: '独奏',
	Someone: '某人',
	Son: '儿子',
	SortBy: '排序方式',
	SouthAmerica: '南美洲',
	Speaker: '扬声器',
	SpeakerSource: '扬声器源',
	Speakers: '演讲嘉宾',
	SpecifyPaymentMethod: '指定付款方式',
	SpeechLanguagePathology: '言语病理学',
	SpeechTherapist: '言语治疗师',
	SpeechTherapists: '言语治疗师',
	SpeechTherapy: '言语治疗',
	SportsMedicinePhysician: '运动医学医师',
	Spouse: '配偶',
	SpreadsheetColumnExample: '例如 ',
	SpreadsheetColumns: '电子表格列',
	SpreadsheetUploaded: '电子表格已上传',
	SpreadsheetUploading: '正在上传…',
	Staff: '职员',
	StaffAccessDescriptionAdmin: '管理员可以管理平台上的一切。',
	StaffAccessDescriptionStaff: `员工可以管理他们创建或共享的客户、笔记和文档
与他们一起安排约会、管理发票。`,
	StaffContactAssignedSubject:
		'{actorProfileName}已将{totalCount, plural, =1 {{contactName1}} =2 {{contactName1}和{contactName2}} other {{contactName1}、{contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 {和其他1位客户} other {和其他#位客户}}分配给您',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} 已与您共享 {inboxName} 收件箱',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} 已撤销您对 {inboxName} 收件箱的访问权限。',
	StaffMembers: '工作人员',
	StaffMembersNumber: '{billedUsers, plural, one {# 团队成员} other {# 团队成员}}',
	StaffSavedSuccessSnackbar: '团队成员信息保存成功！',
	StaffSelectorAdminRole: '行政人员',
	StaffSelectorStaffRole: '职员',
	StandardAppointment: '标准预约',
	StandardColor: '任务颜色',
	StartAndEndTime: '开始和结束时间',
	StartCall: '开始通话',
	StartDate: '开始日期',
	StartDictating: '开始听写',
	StartImport: '开始导入',
	StartRecordErrorTitle: '开始录音时出错',
	StartRecording: '开始录制',
	StartTimeIncrements: '开始时间增量',
	StartTimeIncrementsView: '{startTimeIncrements} 分钟间隔',
	StartTranscribing: '开始转录',
	StartTranscribingNotes: '请选择您要为其生成笔记的客户。然后单击“开始转录”按钮开始录音',
	StartTranscription: '开始转录',
	StartVideoCall: '开始视频通话',
	StartWeekOn: '开始一周',
	StartedBy: '发起者 ',
	Starter: '起动机',
	State: '状态',
	StateIndustrialAccidentProviderNumber: '州工业事故提供者编号',
	StateLicenseNumber: '州执照号码',
	Statement: '陈述',
	StatementDescriptor: '语句描述符',
	StatementDescriptorToolTip:
		'对帐单描述符会显示在客户的银行或信用卡对帐单上。它必须介于 5 到 22 个字符之间，并反映您的企业名称。',
	StatementNumber: '声明 #',
	Status: '地位',
	StatusFieldPlaceholder: '输入状态标签',
	StepFather: '继父',
	StepMother: '后妈',
	Stockholm: '斯德哥尔摩',
	StopIgnoreSendersDescription:
		'通过停止忽略这些发件人，将来的对话将被发送到“收件箱”。您确定要停止忽略这些发件人吗？',
	StopIgnoring: '不要忽视',
	StopIgnoringSenders: '停止忽略发件人',
	StopIgnoringSendersSuccess: '停止忽略电子邮件地址 <mark>{addresses}</mark>',
	StopSharing: '停止共享',
	StopSharingLabel: 'carepatron.com 正在共享您的屏幕。',
	Storage: '贮存',
	StorageAlmostFullDescription: '🚀 立即升级以确保您的帐户顺利运行。',
	StorageAlmostFullTitle: '您已经使用了 {percentage}% 的工作区存储空间限制！',
	StorageFullDescription: '通过升级您的计划来获取更多存储空间。',
	StorageFullTitle: '	您的存储空间已满。',
	Street: '街道',
	StripeAccountNotCompleteErrorCode:
		'在线支付尚未 {hasProviderName, select, true {为 {providerName} 设置} other {为此提供商启用}}.',
	StripeAccountRejectedError: 'Stripe 帐户已被拒绝。请联系支持人员。',
	StripeBalance: '条纹平衡',
	StripeChargesInfoToolTip: '允许您扣款 ',
	StripeFeesDescription:
		'Carepatron 使用 Stripe 快速为您付款并确保您的付款信息安全。可用的付款方式因地区而异，所有主要借记卡 ',
	StripeFeesDescriptionItem1: '处理费适用于每次成功交易，您可以 {link}。',
	StripeFeesDescriptionItem2: '付款每天都会进行，但最多会保留 4 天。',
	StripeFeesLinkToRatesText: '在此查看我们的费率',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar: '抱歉，使用在线支付的发票需要至少 {minimumAmount}。',
	StripePaymentsDisabled: '在线支付已禁用。请检查您的支付设置。',
	StripePaymentsUnavailable: '无法付款',
	StripePaymentsUnavailableDescription: '加载付款时出错。请稍后重试。',
	StripePayoutsInfoToolTip: '允许您将款项转入您的银行账户',
	StyleYourWorkspace: '<mark>风格化</mark> 您的工作区',
	StyleYourWorkspaceDescription1:
		'我们从您的网站上获取了品牌资产。您可以随意编辑它们，或继续进入 Carepatron 工作区。',
	StyleYourWorkspaceDescription2: '使用您的品牌资产来定制发票和在线预订，以提供无缝的客户体验',
	SubAdvanced: '高级',
	SubEssential: '必要',
	SubOrganization: '组织',
	SubPlus: '加',
	SubProfessional: '专业的',
	Subject: '主题',
	Submit: '提交',
	SubmitElectronically: '电子提交',
	SubmitFeedback: '提交反馈',
	SubmitFormValidationError: '请确保所有必填字段均正确填写，然后尝试再次提交。',
	Submitted: '已提交',
	SubmittedDate: '提交日期',
	SubscribePerMonth: '订阅 {price} {isMonthly, select, true {每月} other {每年}}',
	SubscriptionDiscountDescription:
		'{percentOff}% off {months, select, null { } other { {months, plural, one {持续 # 个月} other {持续 # 个月}}}}',
	SubscriptionFreeTrialDescription: '免费至 {endDate}',
	SubscriptionPaymentFailedNotificationSubject: '我们无法完成您的订阅付款。请检查您的付款详情',
	SubscriptionPlanDetailsHeader: '按用户/月按年计费',
	SubscriptionPlanDetailsSubheader: '每月 {pricePerMonth} 美元 (USD) 账单',
	SubscriptionPlans: '订阅计划',
	SubscriptionPlansDescription: '升级您的计划以解锁更多福利并保持您的实践顺利运行。',
	SubscriptionPlansDescriptionNoPermission: '看起来您现在无权升级 - 请联系您的管理员寻求帮助。',
	SubscriptionSettings: '订阅设置',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> 已使用存储空间',
	SubscriptionSettingsStorageUsed: '已用 {used} / {limit}',
	SubscriptionSettingsUnlimitedStorage: '无限存储空间',
	SubscriptionSummary: '订阅摘要',
	SubscriptionUnavailableOverStorageLimit: '您的当前使用量超过了此计划的存储限制。',
	SubscriptionUnpaidBannerButton: '前往订阅',
	SubscriptionUnpaidBannerDescription: '请检查您的付款详细信息是否正确，然后重试',
	SubscriptionUnpaidBannerTitle: '我们无法完成您的订阅付款。',
	Subscriptions: '订阅',
	SubscriptionsAndPayments: '订阅 ',
	Subtotal: '小计',
	SuburbOrProvince: '郊区/省份',
	SuburbOrState: '郊区/州',
	SuccessSavedNoteChanges: '成功保存注释更改',
	SuccessShareDocument: '成功分享文档',
	SuccessShareNote: '成功分享笔记',
	SuccessfullyCreatedValue: '成功创建 {value}',
	SuccessfullyDeletedTranscriptionPart: '成功删除转录部分',
	SuccessfullyDeletedValue: '成功删除 {value}',
	SuccessfullySubmitted: '已成功提交 ',
	SuccessfullyUpdatedClientSettings: '成功更新客户端设置',
	SuccessfullyUpdatedTranscriptionPart: '成功更新转录部分',
	SuccessfullyUpdatedValue: '成功更新 {value}',
	SuggestedAIPoweredTemplates: '建议的 AI 驱动的模板',
	SuggestedAITemplates: '建议的 AI 模板',
	SuggestedActions: '建议采取的措施',
	SuggestedLocations: '建议地点',
	Suggestions: '建议',
	Summarise: 'AI总结',
	SummarisingContent: '总结 {title}',
	Sunday: '星期日',
	Superbill: '超级钞票',
	SuperbillAndInsuranceBilling: '超级钞票 ',
	SuperbillAutomationMonthly: '活跃 • 该月最后一天',
	SuperbillAutomationNoEmail: '要成功发送自动账单文件，请为该客户添加电子邮件地址',
	SuperbillAutomationNotActive: '不活跃',
	SuperbillAutomationUpdateFailure: '无法更新 Superbill 自动化设置',
	SuperbillAutomationUpdateSuccess: '成功更新 Superbill 自动化设置',
	SuperbillClientHelperText: '此信息是从客户详细信息中预先填充的',
	SuperbillNotFoundDescription: '请联系您的提供商并要求他们提供更多信息或重新发送超级账单。',
	SuperbillNotFoundTitle: '未找到 Superbill',
	SuperbillNumber: '超级账单 #{number}',
	SuperbillNumberAlreadyExists: 'Superbill 收据号码已存在',
	SuperbillPracticeHelperText: '此信息是从实践计费设置中预先填充的',
	SuperbillProviderHelperText: '此信息是从员工详细信息中预先填充的',
	SuperbillReceipts: '超级账单收据',
	SuperbillsEmptyStateDescription: '沒有發現超級钞票。',
	Surgeon: '外科医生',
	Surgeons: '外科医生',
	SurgicalTechnologist: '外科技师',
	SwitchFromAnotherPlatform: '我正在从另一个平台切换过来',
	SwitchToMyPortal: '切换到我的门户',
	SwitchToMyPortalTooltip: `访问您自己的个人门户，
让您探索您的
客户的门户体验。`,
	SwitchWorkspace: '切换工作区',
	SwitchingToADifferentPlatform: '切换到其他平台',
	Sydney: '悉尼',
	SyncCalendar: '同步日历',
	SyncCalendarModalDescription: '其他团队成员将无法看到您同步的日历。客户预约只能在 Carepatron 内更新或删除。',
	SyncCalendarModalDisplayCalendar: '在 Carepatron 中显示我的日历',
	SyncCalendarModalSyncToCarepatron: '将我的日历同步至 Carepatron',
	SyncCalendarModalSyncWithCalendar: '将 Carepatron 预约与我的日历同步',
	SyncCarepatronAppointmentsWithMyCalendar: '将 Carepatron 约会同步到我的日历',
	SyncGoogleCalendar: '同步 Google 日历',
	SyncInbox: '与 Carepatron 同步收件箱',
	SyncMyCalendarToCarepatron: '将我的日历同步到 Carepatron',
	SyncOutlookCalendar: '同步 Outlook 日历',
	SyncedFromExternalCalendar: '来自外部日历同步',
	SyncingCalendarName: '同步 {calendarName} 日历',
	SyncingFailed: '同步失败',
	SystemGenerated: '系统生成',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: '桌子',
	TableRowLabel: '{value} 的表格行',
	TagSelectorNoOptionsText: '点击“新建”添加新标签',
	Tags: '标签',
	TagsInputPlaceholder: '搜索或创建标签',
	Task: '任务',
	TaskAttendeeStatusUpdatedSuccess: '成功更新预约状态',
	Tasks: '任务',
	Tax: '税',
	TaxAmount: '税额',
	TaxID: '税号',
	TaxIdType: '税号类型',
	TaxName: '税务名称',
	TaxNumber: '税号',
	TaxNumberType: '税号类型',
	TaxNumberTypeInvalid: '{type} 无效',
	TaxPercentageOfAmount: '{taxName} ({percentage}% 的 {amount})',
	TaxRate: '税率',
	TaxRatesDescription: '管理适用于您的发票项目的税率。',
	Taxable: '应纳税',
	TaxonomyCode: '分类代码',
	TeacherAssistant: '教师助理',
	Team: '团队',
	TeamMember: '队员',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {已经} other {已经}} 预订了这个时间。{br}选择一个新的时间来避免重复预订。',
	TeamMembers: '团队成员',
	TeamMembersColour: '团队成员颜色',
	TeamMembersDetails: '团队成员详细信息',
	TeamSize: '你的团队有多少人？',
	TeamTemplates: '团队模板',
	TeamTemplatesSectionDescription: '由您和您的团队创建',
	TelehealthAndVideoCalls: '远程医疗 ',
	TelehealthProvidedOtherThanInPatientCare: '除住院护理外，还提供远程医疗服务',
	TelehealthVideoCall: '远程医疗视频通话',
	Template: '模板',
	TemplateDescription: '模板描述',
	TemplateDetails: '模板详细信息Template details',
	TemplateEditModeViewSwitcherDescription: '创建和编辑模板',
	TemplateGallery: '社区模板',
	TemplateImportCompletedNotificationSubject: '模板导入完成！{templateTitle} 已经可以使用了。',
	TemplateImportFailedNotificationSubject: '无法导入文件 {fileName}。',
	TemplateName: '模板名称',
	TemplateNotFound: '找不到模板。',
	TemplatePreviewErrorMessage: '加载模板预览时出错',
	TemplateResponderModeViewSwitcherDescription: '预览并与表单交互',
	TemplateResponderModeViewSwitcherTooltipTitle: '检查响应者填写表单时的显示方式',
	TemplateSaved: '已保存更改',
	TemplateTitle: '模板标题',
	TemplateType: '模板类型',
	Templates: '模板',
	TemplatesCategoriesFilter: '按类别过滤',
	TemplatesPublicTemplatesFilter: ' 按社区/团队过滤',
	Text: '文本',
	TextAlign: '文本对齐',
	TextColor: '文本颜色',
	ThankYouForYourFeedback: '感谢您的反馈！',
	ThanksForLettingKnow: '感谢您告知我们。',
	ThePaymentMethod: '付款方式',
	ThemThey: '他们',
	Theme: '主题',
	ThemeAllColorsPickerTitle: '更多主题',
	ThemeColor: '主题',
	ThemeColorDarkMode: '深色',
	ThemeColorLightMode: '光',
	ThemeColorModePickerTitle: '颜色模式',
	ThemeColorSystemMode: '系统',
	ThemeCpColorPickerTitle: 'Carepatron themes',
	ThemePanelDescription: '选择浅色模式或深色模式，并自定义主题偏好',
	ThemePanelTitle: '外观',
	Then: '然后',
	Therapist: '治疗师',
	Therapists: '治疗师',
	Therapy: '治疗',
	Thick: '厚的',
	Thin: '薄的',
	ThirdPerson: '第三人称',
	ThisAndFollowingAppointments: '此次及后续任命',
	ThisAndFollowingMeetings: '本次会议及后续会议',
	ThisAndFollowingReminders: '此提醒及以下提醒',
	ThisAndFollowingTasks: '此任务及后续任务',
	ThisAppointment: '此次任命',
	ThisMeeting: '本次会议',
	ThisMonth: '本月',
	ThisPerson: '这个人',
	ThisReminder: '此提醒',
	ThisTask: '此任务',
	ThisWeek: '本星期',
	ThreeDay: '3 天',
	Thursday: '周四',
	Time: '时间',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {分钟} other {分钟}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: '时间格式',
	TimeIncrement: '时间增量',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {上午} other {下午}}',
	TimeslotSize: '时隙大小',
	Timestamp: '时间戳',
	Timezone: '时区',
	TimezoneDisplay: '时区显示',
	TimezoneDisplayDescription: '管理您的时区显示设置。',
	Title: '标题',
	To: '到',
	ToYourWorkspace: '到您的工作区',
	Today: '今天',
	TodayInHoursPlural: '今天在 {count} {count, plural, one {小时} other {小时}}',
	TodayInMinsAbbreviated: '今天在 {count} {count, plural, one {分钟} other {分钟}}',
	ToggleHeaderCell: '切换标题单元格',
	ToggleHeaderCol: '切换标题列',
	ToggleHeaderRow: '切换标题行',
	Tokyo: '东京',
	Tomorrow: '明天',
	TomorrowAfternoon: '明天下午',
	TomorrowMorning: '明天早上',
	TooExpensive: '太贵了',
	TooHardToSetUp: '设置起来太困难',
	TooManyFiles: '检测到超过 1 个文件。',
	ToolsExample: '简单的做法，微软，Calendly，Asana，Doxy.me......',
	Total: '全部的',
	TotalAccountCredit: '账户总信用额',
	TotalAdjustments: '总调整',
	TotalAmountToCreditInCurrency: '应付金额（{currency}）',
	TotalBilled: '总计账单',
	TotalConversations: '{total} {total, plural, =0 {对话} one {对话} other {对话}}',
	TotalOverdue: '总逾期',
	TotalOverdueTooltip: '总逾期余额包括所有未付发票（无论日期范围如何），这些发票既未作废也未处理。',
	TotalPaid: '总付款',
	TotalPaidTooltip: '总付款余额包括在指定日期范围内支付的所有发票金额。',
	TotalUnpaid: '未付总额',
	TotalUnpaidTooltip: '总未付余额包括指定日期范围内到期的处理、未付和已发送发票的所有未付金额。',
	TotalWorkflows: '{count} {count, plural, one {工作流} other {工作流}}',
	TotpSetUpManualEntryInstruction: '或者，您也可以手动将以下代码输入到应用程序中：',
	TotpSetUpModalDescription: '使用您的身份验证器应用程序扫描二维码以设置多重身份验证。',
	TotpSetUpModalTitle: '设置 MFA 设备',
	TotpSetUpSuccess: '一切就绪！MFA 已启用。',
	TotpSetupEnterAuthenticatorCodeInstruction: '输入您的身份验证器应用生成的代码',
	Transcribe: '录制',
	TranscribeLanguageSelector: '选择输入语言',
	TranscribeLiveAudio: '转录现场音频',
	Transcribing: '正在转录音频...',
	TranscribingIn: '转录',
	Transcript: '脚本',
	TranscriptRecordingCompleteInfo: '录音完成后，您将在这里看到您的成绩单。',
	TranscriptSuccessSnackbar: '成功处理成绩单。',
	Transcription: '转录',
	TranscriptionEmpty: '没有可用的转录',
	TranscriptionEmptyHelperMessage: '此次转录未收到任何结果。请重新启动并重试。',
	TranscriptionFailedNotice: '此转录未成功处理',
	TranscriptionIdleMessage: '我们没有听到任何音频。如果您需要更多时间，请在 {timeValue} 秒内回复，否则会话将结束。',
	TranscriptionInProcess: '转录中...',
	TranscriptionIncompleteNotice: '此转录的某些部分未成功处理',
	TranscriptionOvertimeWarning: '{scribeType} 会话将在 **{timeValue} {unit}** 结束',
	TranscriptionPartDeleteMessage: '您确定要删除此转录部分吗？',
	TranscriptionText: '语音转文本',
	TranscriptsPending: '会议结束后，您的成绩单将在这里提供。',
	Transfer: '转移',
	TransferAndDelete: '转移和删除',
	TransferOwnership: '转让所有权',
	TransferOwnershipConfirmationModalDescription: '只有他们将所有权转回给您时，此操作才可撤销。',
	TransferOwnershipDescription: '将此工作区的所有权转移给另一个团队成员。',
	TransferOwnershipSuccessSnackbar: '所有权转移成功！',
	TransferOwnershipToMember: '您确定要将此工作区转移到 {staff} 吗？',
	TransferStatusAlert:
		'删除 {numberOfStatuses, plural, one {此状态} other {这些状态}} 将会影响 {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} 个客户状态。</strong>} other {<strong>{numberOfAffectedRecords} 个客户状态。</strong>}}',
	TransferStatusDescription: '继续删除之前，请为这些客户选择其他状态。此操作无法撤消。',
	TransferStatusLabel: '转移到新状态',
	TransferStatusPlaceholder: '选择现有状态',
	TransferStatusTitle: '删除前的传输状态',
	TransferTaskAttendeeStatusAlert:
		'移除此状态将影响 <strong>{number} 个未来预约的 {number, plural, one {地位} other {状态}}</strong>。',
	TransferTaskAttendeeStatusDescription: '在继续删除之前，请为这些客户选择其他状态。此操作无法撤消。',
	TransferTaskAttendeeStatusSubtitle: '预约状态',
	TransferTaskAttendeeStatusTitle: '删除前的转移状态',
	Trash: '垃圾',
	TrashDeleteItemsModalConfirm: '要确认，请输入{confirmationText}',
	TrashDeleteItemsModalDescription: '以下将被永久删除，且无法恢复 {count, plural, one {item} other {items}}.',
	TrashDeleteItemsModalTitle: '永久删除 {count, plural, one {item} other {items}}',
	TrashDeletedAllItems: '已删除所有项目',
	TrashDeletedItems: '已删除 {count, plural, one {项} other {项}}',
	TrashDeletedItemsFailure: '无法从垃圾箱中删除项目',
	TrashLocationAppointmentType: '日历',
	TrashLocationBillingAndPaymentsType: '账单与支付',
	TrashLocationContactType: '客户',
	TrashLocationNoteType: '笔记 ',
	TrashRestoreItemsModalDescription: '将恢复以下 {count, plural, one {item} other {items}}。',
	TrashRestoreItemsModalTitle: '还原 {count, plural, one {项} other {项}}',
	TrashRestoredAllItems: '恢复所有物品',
	TrashRestoredItems: '已恢复 {count, plural, one {项} other {项}}',
	TrashRestoredItemsFailure: '无法从垃圾箱中恢复项目',
	TrashSuccessfullyDeletedItem: '成功删除 {type}',
	Trigger: '扳机',
	Troubleshoot: '故障排除',
	TryAgain: '再试一次',
	Tuesday: '周二',
	TwoToTen: '2 - 10',
	Type: '类型',
	TypeHere: '在此处输入...',
	TypeToConfirm: '确认，请输入 {keyword}',
	TypographyH1: 'H1',
	TypographyH2: '氢气',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: '标题 1',
	TypographyHeading2: '标题 2',
	TypographyHeading3: '标题 3',
	TypographyHeading4: '标题 4',
	TypographyHeading5: '标题 5',
	TypographyP: '磷',
	TypographyParagraph: '段落',
	UnableToCompleteAction: '无法完成操作。',
	UnableToPrintDocument: '无法打印文档。请稍后重试。',
	Unallocated: '未分配',
	UnallocatedPaymentDescription: `该付款尚未完全分配至可计费项目。
为未付款项目添加分配，或发放信用或退款。`,
	UnallocatedPaymentTitle: '未分配付款',
	UnallocatedPayments: '未分配付款',
	Unarchive: '取消存档',
	Unassigned: '未分配',
	UnauthorisedInvoiceSnackbar: '您无权管理此客户的发票。',
	UnauthorisedSnackbar: '您无权执行此操作。',
	Unavailable: '不可用',
	Uncategorized: '未分类',
	Unclaimed: '无人认领',
	UnclaimedAmount: '未认领金额',
	UnclaimedItems: '无人认领的物品',
	UnclaimedItemsMustBeInCurrency: '仅支持以下货币的商品：{currencies}',
	Uncle: '叔叔',
	Unconfirmed: '未证实',
	Underline: '强调',
	Undo: '撤消',
	Unfavorite: '取消收藏',
	Uninvoiced: '未开票',
	UninvoicedAmount: '未开票金额',
	UninvoicedAmounts: '{count, plural, =0 {未开票金额} one {未开票金额} other {未开票金额}}',
	Unit: '单元',
	UnitedKingdom: '英国',
	UnitedStates: '美国',
	UnitedStatesEast: '美国 - 东部',
	UnitedStatesWest: '美国 - 西部',
	Units: '单位',
	UnitsIsRequired: '单位为必填项',
	UnitsMustBeGreaterThanZero: '单位必须大于 0',
	UnitsPlaceholder: '1',
	Unknown: '未知',
	Unlimited: '无限',
	Unlock: '开锁',
	UnlockNoteHelper: '在进行任何新的更改之前，编辑者必须解锁注释。',
	UnmuteAudio: '取消静音',
	UnmuteEveryone: '取消所有人静音',
	Unpaid: '未付',
	UnpaidInvoices: '未付发票',
	UnpaidItems: '未付款项目',
	UnpaidMultiple: '未付',
	Unpublish: '取消发布',
	UnpublishTemplateConfirmationModalPrompt:
		'删除 <span>{title}</span> 将会从 Carepatron 社区移除此资源。此操作无法撤销。',
	UnpublishToCommunitySuccessMessage: '成功从社区中删除了“{title}”。',
	Unread: '未读',
	Unrecognised: '未被认可',
	UnrecognisedDescription:
		'您当前的应用程序版本无法识别此付款方式。请刷新浏览器以获取最新版本，以便查看和编辑此付款方式。',
	UnsavedChanges: '未保存的更改',
	UnsavedChangesPromptContent: '您想在关闭之前保存更改吗？',
	UnsavedChangesPromptTitle: '您有未保存的更改',
	UnsavedNoteChangesWarning: '你所做的更改可能无法保存',
	UnsavedTemplateChangesWarning: '你所做的更改可能无法保存',
	UnselectAll: '全选取消',
	Until: '直到',
	UntitledConversation: '无标题对话',
	UntitledFolder: '无标题文件夹',
	UntitledNote: '无标题笔记',
	UntitledSchedule: '无标题时间表',
	UntitledSection: '无标题部分',
	UntitledTemplate: '无标题模板',
	Unverified: '未经验证',
	Upcoming: '即将推出',
	UpcomingAppointments: '即将到来的约会',
	UpcomingDateOverridesEmpty: '未找到日期覆盖',
	UpdateAvailabilityScheduleFailure: '无法更新可用时间表',
	UpdateAvailabilityScheduleSuccess: '已成功更新可用性计划',
	UpdateInvoicesOrClaimsAgainstBillable: '您是否希望将新的定价应用于与会者的发票和索赔？',
	UpdateLink: '更新链接',
	UpdatePrimaryEmailWarningDescription: '更改客户的电子邮件地址会导致他们失去对现有预约和笔记的访问权限。',
	UpdatePrimaryEmailWarningTitle: '客户邮箱变更',
	UpdateSettings: '更新设置',
	UpdateStatus: '更新状态',
	UpdateSuperbillReceiptFailure: '无法更新超级账单收据',
	UpdateSuperbillReceiptSuccess: '成功更新 Superbill 收据',
	UpdateTaskBillingDetails: '更新帐单详细信息',
	UpdateTaskBillingDetailsDescription:
		'预约定价已更改。您是否希望将新定价应用于与会者的账单项目、发票和索赔？选择要继续的更新。',
	UpdateTemplateFolderSuccessMessage: '成功更新文件夹',
	UpdateUnpaidInvoices: '更新未付发票',
	UpdateUserInfoSuccessSnackbar: '成功更新用户信息！',
	UpdateUserSettingsSuccessSnackbar: '成功更新用户设置！',
	Upgrade: '升级',
	UpgradeForSMSReminder: '升级至<b>专业版，</b>即可享受无限量短信提醒',
	UpgradeNow: '立即升级',
	UpgradePlan: '升级计划',
	UpgradeSubscriptionAlertDescription:
		'您的存储空间即将用完。升级您的计划以解锁更多存储空间，并保持您的实践顺利运行！',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'您的存储空间不足。请咨询您诊所内拥有<span>管理员权限</span>的人员，了解升级计划以解锁更多存储空间，并确保您的诊所正常运行！',
	UpgradeSubscriptionAlertTitle: '是时候升级你的订阅了',
	UpgradeYourPlan: '升级您的计划',
	UploadAudio: '上传音频',
	UploadFile: '上传文件',
	UploadFileDescription: '您正在从哪个软件平台迁移？',
	UploadFileMaxSizeError: '文件过大。最大文件大小为 {fileSizeLimit}。',
	UploadFileSizeLimit: '大小限制 {size} MB',
	UploadFileTileDescription: '使用 CSV、XLS、XLSX 或 ZIP 文件上传您的客户。',
	UploadFileTileLabel: '上传文件',
	UploadFiles: '上传文件',
	UploadIndividually: '单独上传文件',
	UploadLogo: '上传徽标',
	UploadPhoto: '上传照片',
	UploadToCarepatron: '上传至 Carepatron',
	UploadYourLogo: '上传您的徽标',
	UploadYourTemplates: '上传您的模板，我们会帮您进行转换。',
	Uploading: '上传',
	UploadingAudio: '正在上传您的音频...',
	UploadingFiles: '上传文件',
	UrlLink: 'URL 链接',
	UsageCount: '已使用 {count} 次',
	UsageLimitValue: '已使用 {used} 个，共 {limit} 个',
	UsageValue: '{used} 使用过',
	Use: '使用',
	UseAiToAutomateYourWorkflow: '使用人工智能来自动化您的工作流程！',
	UseAsDefault: '用作默认值',
	UseCustom: '使用自定义',
	UseDefault: '使用默认',
	UseDefaultFilters: '使用默认过滤器',
	UseTemplate: '使用模板',
	UseThisCard: '使用此卡',
	UseValue: '使用 "{value}"',
	UseWorkspaceDefault: '使用工作区默认设置',
	UserIsTyping: '{name} 正在输入...',
	Username: '用户名',
	Users: '用户',
	VAT: '增值税',
	ValidUrl: 'URL 链接必须是有效的 URL。',
	Validate: '证实',
	Validated: '已验证',
	Validating: '验证',
	ValidatingContent: '正在验证内容...',
	ValidatingTranscripts: '正在验证成绩单...',
	ValidationConfirmPasswordRequired: '需要确认密码',
	ValidationDateMax: '必须在 {max} 之前',
	ValidationDateMin: '必须在 {min} 之后',
	ValidationDateRange: '需要开始和结束日期',
	ValidationEndDateMustBeAfterStartDate: '结束日期必须在开始日期之后',
	ValidationMixedDefault: '这是无效的',
	ValidationMixedRequired: '这是必需的',
	ValidationNumberInteger: '必须是整数',
	ValidationNumberMax: '必须为 {max} 或更少',
	ValidationNumberMin: '必须至少为 {min}',
	ValidationPasswordNotMatching: '密码不匹配',
	ValidationPrimaryAddressIsRequired: '设置为默认地址时需要',
	ValidationPrimaryPhoneNumberIsRequired: '设置为默认时需要电话号码',
	ValidationServiceMustBeNotBeFuture: '服务不能是今天或将来。',
	ValidationStringEmail: '必须是有效的电子邮件',
	ValidationStringMax: '必须少于 {max} 个字符。',
	ValidationStringMin: '必须至少为 {min} 个字符',
	ValidationStringPhoneNumber: '必须是有效的电话号码',
	ValueMinutes: '{value} 分钟',
	VerbosityConcise: '简洁的',
	VerbosityDetailed: '详细的',
	VerbosityStandard: '标准',
	VerbositySuperDetailed: '超详细',
	VerificationCode: '验证码',
	VerificationEmailDescription: '请输入您的电子邮件地址和我们刚刚发送给您的验证码。',
	VerificationEmailSubtitle: '检查垃圾邮件文件夹 - 如果电子邮件尚未到达',
	VerificationEmailTitle: '验证电子邮件',
	VerificationOption: '电子邮件验证',
	Verified: '已验证',
	Verify: '核实',
	VerifyAndSubmit: '验证并提交',
	VerifyEmail: '验证电子邮件',
	VerifyEmailAccessCode: '验证码',
	VerifyEmailAddress: '验证电子邮件地址',
	VerifyEmailButton: '验证并注销',
	VerifyEmailSentSnackbar: '验证邮件已发送。请检查您的收件箱。',
	VerifyEmailSubTitle: '如果电子邮件未到达，请检查垃圾邮件文件夹',
	VerifyEmailSuccessLogOutSnackbar: '成功！请注销以应用更改。',
	VerifyEmailSuccessSnackbar: '成功！电子邮件已验证。请登录以继续使用已验证的帐户。',
	VerifyEmailTitle: '验证您的电子邮件',
	VerifyNow: '立即验证',
	Veterinarian: '兽医',
	VideoCall: '视频电话',
	VideoCallAudioInputFailed: '音频输入设备无法使用',
	VideoCallAudioInputFailedMessage: '打开设置并检查您是否已正确设置麦克风源。',
	VideoCallChatBanner: '通话中的每个人都可以看到消息，并且通话结束后消息将被删除。',
	VideoCallChatSendBtn: '发送消息',
	VideoCallChatTitle: '聊天',
	VideoCallDisconnectedMessage: '您丢失了网络连接。正在尝试重新连接',
	VideoCallOptionInfo: '如果 Zoom 尚未连接，Carepatron 将管理您的预约视频通话',
	VideoCallTilePaused: '由于您的网络问题，该视频已暂停',
	VideoCallTranscriptionFormDescription: '您可以随时调整这些设置',
	VideoCallTranscriptionFormHeading: '自定义你的 AI 抄写员',
	VideoCallTranscriptionFormLanguageField: '生成的输出语言',
	VideoCallTranscriptionFormNoteTemplateField: '设置默认笔记模板',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: '未找到带有 AI 的模板',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: '选择模板',
	VideoCallTranscriptionPronounField: '你的代词',
	VideoCallTranscriptionRecordingNote:
		'在会议结束时，您将收到一个生成的<strong>{noteTemplate} 笔记</strong>和文字记录。',
	VideoCallTranscriptionReferClientField: '请参阅客户',
	VideoCallTranscriptionReferPractitionerField: '请参阅从业者',
	VideoCallTranscriptionTitle: '人工智能抄写员',
	VideoCallTranscriptionVerbosityField: '冗长',
	VideoCallTranscriptionWritingPerspectiveField: '写作视角',
	VideoCalls: '视频通话',
	VideoConferencing: '视频会议',
	VideoOff: '视频已关闭',
	VideoOn: '视频已关闭',
	VideoQual360: '低质量（360p）',
	VideoQual540: '中等质量（540p）',
	VideoQual720: '高品质（720p）',
	View: '看法',
	ViewAll: '查看全部',
	ViewAppointment: '查看预约',
	ViewBy: '查看方式',
	ViewClaim: '查看理赔',
	ViewCollection: '查看系列',
	ViewDetails: '查看详细信息',
	ViewEnrollment: '查看注册',
	ViewPayment: '查看付款',
	ViewRecord: '查看记录',
	ViewRemittanceAdvice: '查看汇款通知',
	ViewRemittanceAdviceHeader: '索赔汇款通知',
	ViewRemittanceAdviceSubheader: '索赔 {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: '查看设置',
	ViewStripeDashboard: '查看 Stripe 仪表板',
	ViewTemplate: '查看模板',
	ViewTemplates: '查看模板',
	ViewableBy: '可查看',
	ViewableByHelper: '您和团队始终可以访问您发布的笔记。您可以选择与客户和/或他们的关系分享此笔记',
	Viewer: '查看器',
	VirtualLocation: '虚拟位置',
	VisibleTo: '可见至',
	VisitOurHelpCentre: '访问我们的帮助中心',
	VisualEffects: '视觉效果',
	VoiceFocus: '语音焦点',
	VoiceFocusLabel: '滤除麦克风中非语音的声音',
	Void: '空白',
	VoidCancelPriorClaim: '撤销/取消先前的理赔',
	WaitingforMins: '等待 {count} 分钟',
	Warning: '警告',
	WatchAVideo: '观看视频',
	WatchDemoVideo: '观看演示视频',
	WebConference: '网络会议',
	WebConferenceOrVirtualLocation: '网络会议/虚拟位置',
	WebDeveloper: 'Web 开发人员',
	WebsiteOptional: '网站<span>（可选）</span>',
	WebsiteUrl: '网站网址',
	Wednesday: '周三',
	Week: '星期',
	WeekPlural: '{count, plural, one {周} other {周}}',
	Weekly: '每周',
	WeeksPlural: '{age, plural, one {# 周} other {# 周}}',
	WelcomeBack: '欢迎回来',
	WelcomeBackName: '欢迎回来，{name}',
	WelcomeName: '欢迎 {name}',
	WelcomeToCarepatron: '欢迎来到 Carepatron',
	WhatCanIHelpWith: '我可以帮你什么忙？',
	WhatDidYouLikeResponse: '您喜欢这个回复的什么？',
	WhatIsCarepatron: '什么是 Carepatron？',
	WhatMadeYouCancel: `什么原因导致你取消了计划？
勾选所有适用项。`,
	WhatServicesDoYouOffer: '什么<mark>服务</mark>你们提供什么？',
	WhatServicesDoYouOfferDescription: '您可以稍后编辑或添加更多服务。',
	WhatsYourAvailability: '您的<mark>空闲时间</mark>如何？',
	WhatsYourAvailabilityDescription: '您可以稍后添加更多计划。',
	WhatsYourBusinessName: '你的<mark>公司名称？</mark>',
	WhatsYourTeamSize: '你的<mark>团队规模？</mark>',
	WhatsYourTeamSizeDescription: '这将帮助我们正确设置您的工作区。',
	WhenThisHappens: '发生这种情况时：',
	WhichBestDescribesYou: '哪一个最好<mark>描述你？</mark>',
	WhichPlatforms: '哪些平台？',
	Wife: '妻子',
	WorkflowDescription: '工作流程描述',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'模板可以链接到工作流，以便更流畅地处理流程。查看链接的工作流，以便轻松跟踪和更新它们。',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: '基于共同触发器连接您的短信 + 电子邮件',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: '工作流自动化',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: '自动化工作流',
	WorkflowTemplateConfigKey_Body: '正文',
	WorkflowTemplateConfigKey_Branding_IsVisible: '显示品牌',
	WorkflowTemplateConfigKey_Content: '内容',
	WorkflowTemplateConfigKey_Footer: '页脚',
	WorkflowTemplateConfigKey_Footer_IsVisible: '显示页脚',
	WorkflowTemplateConfigKey_Header: '标题',
	WorkflowTemplateConfigKey_Header_IsVisible: '显示标题',
	WorkflowTemplateConfigKey_SecurityFooter: '安全页脚',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: '显示安全页脚',
	WorkflowTemplateConfigKey_Subject: '主题',
	WorkflowTemplateConfigKey_Title: '标题',
	WorkflowTemplateDeleteConfirmationMessage: '您确定要删除此模板吗？此操作无法撤销。',
	WorkflowTemplateDeleteConfirmationTitle: '删除通知模板',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'您确定吗？这将仅删除 {locale} 版本，其他语言不会受到影响。此操作无法撤消。',
	WorkflowTemplateDeleteLocalisationDialogTitle: '删除 ‘{locale}’ 模板',
	WorkflowTemplateDeletedSuccess: '通知模板已成功删除',
	WorkflowTemplateEditorDetailsTab: '模板详情',
	WorkflowTemplateEditorEmailContent: '电子邮件内容',
	WorkflowTemplateEditorEmailContentTab: '电子邮件内容',
	WorkflowTemplateEditorThemeTab: '主题',
	WorkflowTemplatePreviewerAlert: '预览使用示例数据来展示您的客户将看到的内容。',
	WorkflowTemplateResetEmailContentDialogDescription: '确定吗？这将重置版本回系统的默认模板。此操作无法撤消。',
	WorkflowTemplateResetEmailContentDialogTitle: '重置模板',
	WorkflowTemplateSendTestEmail: '发送测试邮件',
	WorkflowTemplateSendTestEmailDialogDescription: '通过给自己发送一封测试邮件来试用您的电子邮件设置。',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: '收件人邮箱',
	WorkflowTemplateSendTestEmailDialogSendButton: '发送测试',
	WorkflowTemplateSendTestEmailDialogTitle: '发送测试邮件',
	WorkflowTemplateSendTestEmailSuccess: '成功！您的 <mark>{templateName}</mark> 测试邮件已发送。',
	WorkflowTemplateTemplateDetailsPanelDescription: '管理您的模板并添加多种语言版本，以便与客户有效沟通。',
	WorkflowTemplateTemplateEditor: '模板编辑器',
	WorkflowTemplateTranslateLocaleError: '内容翻译时出现错误',
	WorkflowTemplateTranslateLocaleSuccess: '成功将内容翻译成 **{locale}**',
	WorkflowsAndReminders: '工作流程 ',
	WorkflowsManagement: '工作流管理',
	WorksheetAndHandout: '工作表/讲义',
	WorksheetsAndHandoutsDescription: '为客户参与和教育',
	Workspace: '工作区',
	WorkspaceBranding: '工作空间品牌化',
	WorkspaceBrandingDescription: `轻松打造一个能反映您工作空间的统一风格
专业和个性。定制发票在线预订，享受美丽
客户体验。`,
	WorkspaceName: '工作区名称',
	Workspaces: '工作区',
	WriteOff: '注销',
	WriteOffModalDescription: '您有 <mark>{count} {count, plural, one {行项目} other {行项目}}</mark> 需要注销',
	WriteOffModalTitle: '注销调整',
	WriteOffReasonHelperText: '这是内部说明，您的客户看不到。',
	WriteOffReasonPlaceholder: '添加注销原因有助于审查计费交易',
	WriteOffTotal: '总计核销 ({currencyCode})',
	Writer: '作家',
	Yearly: '年度',
	YearsPlural: '{age, plural, one {# 年} other {# 年}}',
	Yes: '是的',
	YesArchive: '是的，存档',
	YesDelete: '是，删除',
	YesDeleteOverride: '是的，删除覆盖',
	YesDeleteSection: '是，删除',
	YesDisconnect: '是的，断开连接',
	YesEnd: '是的，结束',
	YesEndTranscription: '是，结束转录',
	YesImFineWithThat: '是的，我同意',
	YesLeave: '是的，离开',
	YesMinimize: '是，最小化',
	YesOrNoAnswerTypeDescription: '配置答案类型',
	YesOrNoFormPrimaryText: '是 | 否',
	YesOrNoFormSecondaryText: '选择是或否选项',
	YesProceed: '是的，继续',
	YesRemove: '是的，删除',
	YesRestore: '是的，恢复',
	YesStopIgnoring: '是的，不要再忽视',
	YesTransfer: '是的，转移',
	Yesterday: '昨天',
	YogaInstructor: '瑜伽教练',
	You: '你',
	YouArePresenting: '您正在演示',
	YouCanChooseMultiple: '您可以选择多个',
	YouCanSelectMultiple: '您可以选择多个',
	YouHaveOngoingTranscription: '您有一个正在进行的转录',
	YourAnswer: '您的答案',
	YourDisplayName: '您的显示名称',
	YourSpreadsheetColumns: '您的电子表格列',
	YourTeam: '你的团队',
	ZipCode: '邮政编码',
	Zoom: '飞涨',
	ZoomUserNotInAccountErrorCodeSnackbar: '您无法为该团队成员添加 Zoom 通话。请参阅<a>支持文档了解更多信息。</a>',
};

export default items;
