import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'Y-tunnus',
	AIPrompts: 'AI kehottaa',
	ATeamMemberIsRequired: '<PERSON>r<PERSON><PERSON><PERSON> tiimijäsentä',
	AboutClient: 'Tietoja asiakkaasta',
	AcceptAppointment: '<PERSON><PERSON><PERSON>, että vahvistit nimityksen',
	AcceptTermsAndConditionsRequired: 'Hyväksy ehdot ',
	Accepted: 'Hyväksytty',
	AccessGiven: 'P<PERSON><PERSON>sy annettu',
	AccessPermissions: 'Käyttöoikeudet',
	AccessType: 'K<PERSON>yttöoikeustyyppi',
	Accident: 'Onnettomuus',
	Account: 'Tili',
	AccountCredit: 'Tilin luotto',
	Accountant: 'Kirjanpitäj<PERSON>',
	Action: 'Toiminta',
	Actions: 'Toiminnot',
	Active: 'Aktiivinen',
	ActiveTags: 'Aktiiviset tunnisteet',
	ActiveUsers: 'Aktiiviset käyttäjät',
	Activity: 'Toimint<PERSON>',
	Actor: '<PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON>',
	Acupuncture: 'Akupunkti<PERSON>',
	Acupuncturist: 'Akupunktiohoitaja',
	Acupuncturists: 'Akupunktiohoitajat',
	AcuteManifestationOfAChronicCondition: 'Kroonisen sairauden akuutti ilmentymä',
	Add: 'Lisätä',
	AddADescription: 'Lisää kuvaus',
	AddALocation: 'Lisää sijainti',
	AddASecondTimezone: 'Lisää toinen aikavyöhyke',
	AddAddress: 'Lisää osoite',
	AddAnother: '  Lisää toinen',
	AddAnotherAccount: 'Lisää toinen tili',
	AddAnotherContact: 'Lisää toinen yhteyshenkilö',
	AddAnotherOption: 'Lisää toinen vaihtoehto',
	AddAnotherTeamMember: 'Lisää toinen tiimijäsen',
	AddAvailablePayers: '+ Lisää käytettävissä olevat maksajat',
	AddAvailablePayersDescription:
		'Etsi maksajat, jotka haluat lisätä työtilasi maksajaluetteloon. Kun olet lisännyt ne, voit hallinnoida rekisteröintejä tai muokata maksajatietoja tarpeen mukaan.',
	AddCaption: 'Lisää kuvateksti',
	AddClaim: 'Lisää vaatimus',
	AddClientFilesModalDescription:
		'Jos haluat rajoittaa käyttöoikeutta, valitse vaihtoehdot Katselualue -valintaruuduista',
	AddClientFilesModalTitle: 'Lataa tiedostot {name}lle',
	AddClientNoteButton: 'Lisää muistiinpano',
	AddClientNoteModalDescription:
		'Lisää sisältöä muistiinpanoosi. Valitse "Katsottava" -osiosta yksi tai useampi ryhmä, joka voi nähdä tämän huomautuksen.',
	AddClientNoteModalTitle: 'Lisää muistiinpano',
	AddClientOwnerRelationshipModalDescription:
		'Asiakkaan kutsuminen antaa hänelle mahdollisuuden hallita omia profiilitietojaan ja hallita käyttäjien pääsyä profiilitietoihinsa.',
	AddClientOwnerRelationshipModalTitle: 'Kutsu asiakas',
	AddCode: 'Lisää koodi',
	AddColAfter: 'Lisää sarake perään',
	AddColBefore: 'Lisää sarake ennen',
	AddCollection: 'Lisää kokoelma',
	AddColor: 'Lisää väriä',
	AddColumn: 'Lisää sarake',
	AddContactRelationship: 'Lisää kontaktisuhde',
	AddContacts: 'Lisää yhteystietoja',
	AddCustomField: 'Lisää mukautettu kenttä',
	AddDate: 'Lisää päivämäärä',
	AddDescription: 'Lisää kuvaus',
	AddDetail: 'Lisää yksityiskohtia',
	AddDisplayName: 'Lisää näyttönimi',
	AddDxCode: 'Lisää diagnoosikoodi',
	AddEmail: 'Lisää sähköposti',
	AddFamilyClientRelationshipModalDescription:
		'Perheenjäsenen kutsuminen antaa heille mahdollisuuden nähdä hoitotarinoita ja asiakkaan profiilitiedot. Jos heidät kutsutaan ylläpitäjiksi, heillä on oikeus päivittää asiakkaan profiilitietoja ja hallita käyttäjien käyttöoikeuksia.',
	AddFamilyClientRelationshipModalTitle: 'Kutsu perheenjäsen',
	AddField: 'Lisää kenttä',
	AddFormField: 'Lisää lomakekenttä',
	AddImages: 'Lisää kuvia',
	AddInsurance: 'Lisää vakuutus',
	AddInvoice: 'Lisää lasku',
	AddLabel: 'Lisää tunniste',
	AddLanguage: 'Lisää kieli',
	AddLocation: 'Lisää sijainti',
	AddManually: 'Lisää manuaalisesti',
	AddMessage: 'Lisää viesti',
	AddNewAction: 'Lisää uusi toiminto',
	AddNewSection: 'Lisää uusi osio',
	AddNote: 'Lisää muistiinpano',
	AddOnlineBookingDetails: 'Lisää online-varaustiedot',
	AddPOS: 'Lisää POS',
	AddPaidInvoices: 'Lisää maksetut laskut',
	AddPayer: 'Lisää maksaja',
	AddPayment: 'Lisää maksu',
	AddPaymentAdjustment: 'Lisää maksun oikaisu',
	AddPaymentAdjustmentDisabledDescription: 'Maksujakoa ei muuteta.',
	AddPaymentAdjustmentEnabledDescription: 'Käytettävissä olevaa määrää vähennetään.',
	AddPhoneNumber: 'Lisää puhelinnumero',
	AddPhysicalOrVirtualLocations: 'Lisää fyysisiä tai virtuaalisia sijainteja',
	AddQuestion: 'Lisää kysymys',
	AddQuestionOrTitle: 'Lisää kysymys tai otsikko',
	AddRelationship: 'Lisää suhde',
	AddRelationshipModalTitle: 'Yhdistä olemassa oleva yhteystieto',
	AddRelationshipModalTitleNewClient: 'Yhdistä uusi yhteystieto',
	AddRow: 'Lisää rivi',
	AddRowAbove: 'Lisää rivi yläpuolelle',
	AddRowBelow: 'Lisää rivi alle',
	AddService: 'Lisää palvelu',
	AddServiceLocation: 'Lisää palvelupaikka',
	AddServiceToCollections: 'Lisää palvelu kokoelmiin',
	AddServiceToOneOrMoreCollections: 'Lisää palvelu yhteen tai useampaan kokoelmaan',
	AddServices: 'Lisää palveluita',
	AddSignature: 'Lisää allekirjoitus',
	AddSignaturePlaceholder: 'Kirjoita allekirjoitukseesi lisättävät lisätiedot',
	AddSmartDataChips: 'Lisää älykkäitä datasiruja',
	AddStaffClientRelationshipsModalDescription:
		'Henkilökunnan valitseminen antaa heille mahdollisuuden luoda ja tarkastella hoitotarinoita tälle asiakkaalle. He voivat myös tarkastella asiakastietoja.',
	AddStaffClientRelationshipsModalTitle: 'Lisää henkilöstösuhteita',
	AddTag: 'Lisää tunniste',
	AddTags: 'Lisää tunnisteita',
	AddTemplate: 'Lisää malli',
	AddTimezone: 'Lisää aikavyöhyke',
	AddToClaim: 'Lisää vaatimukseen',
	AddToCollection: 'Lisää kokoelmaan',
	AddToExisting: 'Lisää olemassa olevaan',
	AddToStarred: 'Lisää tähdellä merkittyihin',
	AddUnclaimedItems: 'Lisää lunastamattomia kohteita',
	AddUnrelatedContactWarning:
		'Olet lisännyt jonkun, joka ei ole {contact}:n yhteyshenkilö. Varmista, että sisältö on relevantti ennen kuin jatkat jakamista.',
	AddValue: 'Lisää "{value}"',
	AddVideoCall: 'Lisää videopuhelu',
	AddVideoOrVoiceCall: 'Lisää video- tai äänipuhelu',
	AddictionCounselor: 'Päivystysneuvoja',
	AddingManualPayerDisclaimer:
		'Maksajan lisääminen manuaalisesti palveluntarjoajaluetteloon ei luo sähköistä korvausilmoitusyhteyttä kyseiseen maksajaan, mutta sitä voidaan käyttää korvausten luomiseen manuaalisesti.',
	AddingTeamMembersIncreaseCostAlert: 'Uusien tiimin jäsenten lisääminen lisää kuukausitilaustasi.',
	Additional: 'Lisätiedot',
	AdditionalBillingProfiles: 'Lisälaskutusprofiilit',
	AdditionalBillingProfilesSectionDescription:
		'Ohita tietyille tiimin jäsenille, maksajille tai laskumalleille käytetyt oletuslaskutustiedot.',
	AdditionalFeedback: 'Lisäpalautetta',
	AddnNewWorkspace: 'Uusi työtila',
	AddnNewWorkspaceSuccessSnackbar: 'Työtila on luotu!',
	Address: 'Osoite',
	AddressNumberStreet: 'Osoite (nro, katu)',
	Adjustment: 'Säädä',
	AdjustmentType: 'Säätötyyppi',
	Admin: 'Admin',
	Admins: 'Järjestelmänvalvojat',
	AdminsOnly: 'Vain järjestelmänvalvojat',
	AdvancedPlanInclusionFive: 'Tilivastaava',
	AdvancedPlanInclusionFour: 'Googlen analytiikka',
	AdvancedPlanInclusionHeader: 'Plussassa kaikki  ',
	AdvancedPlanInclusionOne: 'Roolit ',
	AdvancedPlanInclusionSix: 'Tietojen tuonnin tuki',
	AdvancedPlanInclusionThree: 'Valkoinen merkintä',
	AdvancedPlanInclusionTwo: '90 päivää poistettujen tietojen säilytys',
	AdvancedPlanMessage: 'Pidä käytäntösi tarpeet hallinnassa. Tarkista nykyinen suunnitelmasi ja seuraa käyttöä.',
	AdvancedSettings: 'Lisäasetukset',
	AdvancedSubscriptionPlanSubtitle: 'Laajenna harjoitteluasi kaikilla ominaisuuksilla',
	AdvancedSubscriptionPlanTitle: 'Edistynyt',
	AdvertisingManager: 'Mainospäällikkö',
	AerospaceEngineer: 'Ilmailu-insinööri',
	AgeYearsOld: '{age} vuotta vanha',
	Agenda: 'Agenda',
	AgendaView: 'Esityslistanäkymä',
	AiAskSupportedFileTypes: 'Tuetut tiedostotyypit: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Apuri käden ulottuvilla',
	AiCopilotDisclaimer: 'AI Copilot voi tehdä virheitä. Tarkista tärkeät tiedot.',
	AiCreateNewConversation: 'Luo uusi keskustelu',
	AiEnhanceYourProductivity: 'Tehosta tuottavuuttasi',
	AiPoweredTemplates: 'AI-pohjaiset mallit',
	AiScribeNoDeviceFoundErrorMessage:
		'Näyttää siltä, että selaimesi ei tue tätä ominaisuutta tai yhteensopivia laitteita ei ole saatavilla.',
	AiScribeUploadFormat: 'Tuetut tiedostotyypit: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'vain 1 tiedosto kerrallaan',
	AiShowConversationHistory: 'Näytä keskustelun historia',
	AiSmartPromptNodePlaceholderText:
		'Kirjoita oma mukautettu kehote tähän, jotta voit luoda tarkkoja ja henkilökohtaisia AI-tuloksia.',
	AiSmartPromptPrimaryText: 'Ai älykäs kehote',
	AiSmartPromptSecondaryText: 'Lisää mukautettu tekoälyn älykkäät kehotteet',
	AiSmartReminders: 'Älykkäät tekoälymuistutukset',
	AiTemplateBannerTitle: 'Yksinkertaista työtäsi tekoälypohjaisilla malleilla',
	AiTemplates: 'AI-malleja',
	AiTokens: 'AI-merkit',
	AiWorkBetterWithAi: 'Työskentele tehokkaammin AI:n avulla',
	All: 'Kaikki',
	AllAppointments: 'Kaikki tapaamiset',
	AllCategories: 'Kaikki luokat',
	AllClients: 'Kaikki asiakkaat',
	AllContactPolicySelectorLabel: 'Kaikki <mark>{client}</mark>:n yhteystiedot',
	AllContacts: 'Kaikki yhteystiedot',
	AllContactsOf: 'Kaikki ‘{name}’n kontaktit',
	AllDay: 'Koko päivän',
	AllInboxes: 'Kaikki postilaatikot',
	AllIndustries: 'Kaikki toimialat',
	AllLocations: 'Kaikki paikat',
	AllMeetings: 'Kaikki kokoukset',
	AllNotificationsRestoredMessage: 'Kaikki ilmoitukset palautettu',
	AllProfessions: 'Kaikki ammatit',
	AllReminders: 'Kaikki muistutukset',
	AllServices: 'Kaikki palvelut',
	AllStatuses: 'Kaikki tilat',
	AllTags: 'Kaikki tunnisteet',
	AllTasks: 'Kaikki tehtävät',
	AllTeamMembers: 'Kaikki joukkueen jäsenet',
	AllTypes: 'Kaikki tyypit',
	Allocated: 'Jaettu',
	AllocatedItems: 'Määrätyt kohteet',
	AllocationTableEmptyState: 'Maksumääräyksiä ei löytynyt',
	AllocationTotalWarningMessage: `Jaettu summa ylittää maksun kokonaissumman.
 Tarkista alla olevat rivikohdat.`,
	AllowClientsToCancelAnytime: 'Salli asiakkaiden peruuttaa milloin tahansa',
	AllowNewClient: 'Salli uusia asiakkaita',
	AllowNewClientHelper: 'Uudet asiakkaat voivat varata tämän palvelun',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Varau aika vähintään {hours} tuntia ennen tapaamista.',
	AllowToUseSavedCard: 'Salli {provider}n käyttää tallennettua korttia tulevaisuudessa',
	AllowVideoCalls: 'Salli videopuhelut',
	AlreadyAdded: 'Jo lisätty',
	AlreadyHasAccess: 'On pääsy',
	AlreadyHasAccount: 'Onko sinulla jo tili?',
	Always: 'Aina',
	AlwaysIgnore: 'Aina sivuuttaa',
	Amount: 'Määrä',
	AmountDue: 'Erääntyvä summa',
	AmountOfReferralRequests: '{amount, plural, one {# suosittelupyyntö} other {# suosittelupyyntöjä}}',
	AmountPaid: 'Maksettu summa',
	AnalyzingAudio: 'Analysoidaan ääntä...',
	AnalyzingInputContent: 'Analysoidaan syötettyä sisältöä...',
	AnalyzingRequest: 'Analysoidaan pyyntöä...',
	AnalyzingTemplateContent: 'Analysoidaan mallin sisältöä...',
	And: 'ja',
	Annually: 'Vuosittain',
	Anonymous: 'Nimetön',
	AnswerExceeded: 'Vastauksesi saa olla alle 300 merkkiä pitkä.',
	AnyStatus: 'Mikä tahansa tila',
	AppNotifications: 'Ilmoitukset',
	AppNotificationsClearanceHeading: 'Hienoa työtä! Olet tyhjentänyt kaiken toiminnan',
	AppNotificationsEmptyHeading: 'Työtilatoimintasi näkyvät täällä pian',
	AppNotificationsEmptySubtext: 'Toistaiseksi ei ole ryhdyttävä toimiin',
	AppNotificationsIgnoredCount: '{total} jätetty huomiotta',
	AppNotificationsUnread: '{total} lukematon',
	Append: 'Liitä',
	Apply: 'Käytä',
	ApplyAccountCredit: 'Käytä tilihyvitystä',
	ApplyDiscount: 'Käytä alennusta',
	ApplyVisualEffects: 'Käytä visuaalisia tehosteita',
	ApplyVisualEffectsNotSupported: 'Visuaalisia tehosteita ei tueta',
	Appointment: 'Nimittäminen',
	AppointmentAssignedNotificationSubject: '{actorProfileName} on antanut sinulle {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} on peruutettu {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} on vahvistanut {appointmentName}',
	AppointmentDetails: 'Ajanvaraustiedot',
	AppointmentLocation: 'Ajanvarauksen sijainti',
	AppointmentLocationDescription:
		'Hallitse oletusarvoisia virtuaalisia ja fyysisiä sijaintejasi. Kun tapaaminen on aikataulutettu, nämä sijainnit otetaan automaattisesti käyttöön.',
	AppointmentNotFound: 'Ajanvarausta ei löytynyt',
	AppointmentReminder: 'Muistutus tapaamisesta',
	AppointmentReminders: 'Ajanvarausmuistutukset',
	AppointmentRemindersInfo:
		'Aseta automaattiset muistutukset asiakkaiden tapaamisille välttääksesi saapumatta jättämisen ja peruutukset',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} on siirtänyt {appointmentName}n.',
	AppointmentSaved: 'Tapaaminen tallennettu',
	AppointmentStatus: 'Tapaamisen tila',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}h {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}h {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Tapaaminen peruttu',
	Appointments: 'Tapaamiset',
	Archive: 'Arkisto',
	ArchiveClients: 'Arkistoi asiakkaat',
	Archived: 'Arkistoitu',
	AreYouAClient: 'Oletko asiakas?',
	AreYouStillThere: 'Oletko vielä siellä?',
	AreYouSure: 'Oletko varma?',
	Arrangements: 'Järjestelyt',
	ArtTherapist: 'Taideterapeutti',
	Articles: 'Artikkelit',
	Artist: 'Taiteilija',
	AskAI: 'Kysy AI',
	AskAiAddFormField: 'Lisää lomakekenttä',
	AskAiChangeFormality: 'Muuta muodollisuutta',
	AskAiChangeToneToBeMoreProfessional: 'Muuta sävyä ammattimaisemmaksi',
	AskAiExplainThis: 'KysyAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Selitä, mistä tässä asiakirjassa on kyse',
	AskAiExplainWhatThisImageIsAbout: 'Selitä, mistä tässä kuvassa on kyse',
	AskAiFixSpellingAndGrammar: 'Korjaa oikeinkirjoitus ja kielioppi',
	AskAiGenerateACaptionForThisImage: 'Luo tälle kuvalle kuvateksti',
	AskAiGenerateFromThisPage: 'Luo tältä sivulta',
	AskAiGetStarted: 'Aloita',
	AskAiGiveItAFriendlyTone: 'Anna sille ystävällinen sävy',
	AskAiGreeting: 'Hei {firstName}! Miten voin auttaa tänään?',
	AskAiHowCanIHelpWithYourContent: 'Kuinka voin auttaa sisältösi kanssa?',
	AskAiInsert: 'Lisää',
	AskAiMakeItMoreCasual: 'Tee siitä rennompaa',
	AskAiMakeThisTextMoreConcise: 'Tee tästä tekstistä ytimekkäämpi',
	AskAiMoreProfessional: 'Ammattimaisempaa',
	AskAiOpenPreviousNote: 'Avaa edellinen muistiinpano',
	AskAiPondering: 'Pohdintaa',
	AskAiReplace: 'Korvata',
	AskAiReviewOrEditSelection: 'Tarkista tai muokkaa valintaa',
	AskAiRuminating: 'märehtiä',
	AskAiSeeMore: 'Katso lisää',
	AskAiSimplifyLanguage: 'Yksinkertaistaa kieltä',
	AskAiSomethingWentWrong: 'Jotain meni pieleen. Jos ongelma jatkuu, ota yhteyttä meihin ohjekeskuksemme kautta.',
	AskAiStartWithATemplate: 'Aloita mallista',
	AskAiSuccessfullyCopiedResponse: 'AI-vastauksen kopioiminen onnistui',
	AskAiSuccessfullyInsertedResponse: 'AI-vastauksen lisäys onnistui',
	AskAiSuccessfullyReplacedResponse: 'AI-vastauksen korvaaminen onnistui',
	AskAiSuggested: 'Ehdotettu',
	AskAiSummariseTextIntoBulletPoints: 'Tiivistä teksti luettelomerkkikohtiin',
	AskAiSummarizeNote: 'Yhteenveto huomautus',
	AskAiThinking: 'Ajattelu',
	AskAiToday: 'Tänään {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Mitä haluat tehdä tällä lomakkeella?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Kirjoita ammattimainen muistiinpano mallin avulla',
	AskAskAiAnything: 'Kysy AI:ltä mitä tahansa',
	AskWriteSearchAnything: 'Kysy, kirjoita @ tai hae mitä tahansa...',
	Asking: 'kysyy',
	Assessment: 'Arviointi',
	Assessments: 'Arvioinnit',
	AssessmentsCategoryDescription: 'Asiakasarvioiden tallentamiseen',
	AssignClients: 'Määritä asiakkaita',
	AssignNewClients: 'Määritä asiakkaita',
	AssignServices: 'Määritä palvelut',
	AssignTeam: 'Määritä joukkue',
	AssignTeamMember: 'Nimeä tiimin jäsen',
	Assigned: 'Määrätty',
	AssignedClients: 'Määrätyt asiakkaat',
	AssignedServices: 'Määrätyt palvelut',
	AssignedServicesDescription:
		'Tarkastele ja hallinnoi palveluitasi säätämällä hintoja mukautettuja hintojasi vastaaviksi. ',
	AssignedTeam: 'Määrätty joukkue',
	AthleticTrainer: 'Athletic Trainer',
	AttachFiles: 'Liitä tiedostot',
	AttachLogo: 'Liittää',
	Attachment: 'Liite',
	AttachmentBlockedFileType: 'Estetty turvallisuussyistä!',
	AttachmentTooLargeFileSize: 'Tiedosto liian suuri',
	AttachmentUploadItemComplete: 'Täydellinen',
	AttachmentUploadItemError: 'Lataus epäonnistui',
	AttachmentUploadItemLoading: 'Ladataan',
	AttemptingToReconnect: 'Yritetään yhdistää uudelleen...',
	Attended: 'Osallistui',
	AttendeeBeingMutedTooltip: 'Isäntä on mykisttänyt sinut. Käytä "nostaa kättä" pyytääksesi mykistyksen poistamista',
	AttendeeWithId: 'Osallistuja {attendeeId}',
	Attendees: 'Osallistujat',
	AttendeesCount: '{count} osallistujaa',
	Attending: 'Osallistumassa',
	Audiologist: 'Audiologi',
	Aunt: 'Täti',
	Australia: 'Australia',
	AuthenticationCode: 'Todennuskoodi',
	AuthoriseProvider: 'Valtuuta {provider}',
	AuthorisedProviders: 'Valtuutetut palveluntarjoajat',
	AutoDeclineAllFutureOption: 'Vain uudet tapahtumat tai tapaamiset',
	AutoDeclineAllOption: 'Uudet ja olemassa olevat tapahtumat tai tapaamiset',
	AutoDeclinePrimaryText: 'Automaattisesti hylkää tapahtumat',
	AutoDeclineSecondaryText: 'Poissaolon aikana tapahtuvat tapahtumat hylätään automaattisesti',
	AutogenerateBillings: 'Luo laskutusasiakirjat automaattisesti',
	AutogenerateBillingsDescription:
		'Automaattiset laskutusasiakirjat luodaan kuun viimeisenä päivänä. Laskut ja superlaskukuitit voidaan luoda manuaalisesti milloin tahansa.',
	AutomateWorkflows: 'Automatisoi työnkulut',
	AutomaticallySendSuperbill: 'Lähetä superlaskukuitit automaattisesti',
	AutomaticallySendSuperbillHelperText:
		'Superlasku on yksityiskohtainen kuitti asiakkaalle vakuutuskorvausta varten tarjotuista palveluista',
	Automation: 'Automaatio',
	AutomationActionSendEmailLabel: 'Lähetä Sähköposti',
	AutomationActionSendSMSLabel: 'Lähetä SMS',
	AutomationAndReminders: 'Automaatio ',
	AutomationDeletedSuccessMessage: 'Automaatio poistettu onnistuneesti',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Aikatapahtuma',
	AutomationParams_timeUnit: 'Yksikkö',
	AutomationParams_timeValue: 'Määrä',
	AutomationPublishSuccessMessage: 'Automaatio julkaistu onnistuneesti',
	AutomationPublishWarningTooltip:
		'Tarkista automaation asetukset uudelleen ja varmista, että se on määritetty oikein',
	AutomationTriggerEventCancelledDescription: 'Laukaisee, kun tapahtuma peruutetaan tai poistetaan',
	AutomationTriggerEventCancelledLabel: 'Tapahtuma peruttu',
	AutomationTriggerEventCreatedDescription: 'Laukaisee, kun tapahtuma luodaan',
	AutomationTriggerEventCreatedLabel: 'Uusi tapahtuma',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Laukaisee, kun tapahtuma luodaan tai päivitetään (paitsi kun se peruutetaan)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Uusi tai päivitetty tapahtuma',
	AutomationTriggerEventEndedDescription: 'Laukaisee, kun tapahtuma päättyy',
	AutomationTriggerEventEndedLabel: 'Tapahtuma päättyi',
	AutomationTriggerEventStartsDescription: 'Laukaisee, kun tietty aika ennen tapahtuman alkamista',
	AutomationTriggerEventStartsLabel: 'Tapahtuma alkaa',
	Automations: 'Automaatiot',
	Availability: 'Saatavuus',
	AvailabilityDisableSchedule: 'Poista aikataulu käytöstä',
	AvailabilityDisabled: 'Ei käytössä',
	AvailabilityEnableSchedule: 'Ota aikataulu käyttöön',
	AvailabilityEnabled: 'Käytössä',
	AvailabilityNoActiveBanner:
		'Olet poistanut kaikki aikataulusi käytöstä. Asiakkaat eivät voi varata sinua verkossa, ja kaikki tulevat ajat on vahvistettava manuaalisesti.',
	AvailabilityNoActiveConfirmationDescription:
		'Tämän saatavuuden poistaminen käytöstä johtaa siihen, ettei sinulla ole aktiivisia aikatauluja. Asiakkaat eivät voi varata sinua verkossa, ja kaikki ammatinharjoittajien tekemät varaukset jäävät työaikasi ulkopuolelle.',
	AvailabilityNoActiveConfirmationProceed: 'Kyllä, jatka',
	AvailabilityNoActiveConfirmationTitle: 'Ei aktiivisia aikatauluja',
	AvailabilityToggle: 'Aikataulu käytössä',
	AvailabilityUnsetDate: 'Ei päivämäärää asetettu',
	AvailableLocations: 'Käytettävissä olevat paikat',
	AvailablePayers: 'Saatavilla olevat maksajat',
	AvailablePayersEmptyState: 'Ei valittu maksajia',
	AvailableTimes: 'Saatavilla olevat ajat',
	Back: 'Takaisin',
	BackHome: 'Takaisin kotiin',
	BackToAppointment: 'Takaisin ajanvaraukseen',
	BackToLogin: 'Takaisin kirjautumiseen',
	BackToMapColumns: 'Takaisin karttapylväisiin',
	BackToTemplates: 'Takaisin malleihin',
	BackToUploadFile: 'Takaisin tiedoston lataukseen',
	Banker: 'Pankkiiri',
	BasicBlocks: 'Peruslohkot',
	BeforeAppointment: 'Lähetä {deliveryType} muistutus {interval} {unit} ennen tapaamista',
	BehavioralAnalyst: 'Käyttäytymisanalyytikko',
	BehavioralHealthTherapy: 'Käyttäytymisterveysterapia',
	Beta: 'Beeta',
	BillTo: 'Laskuta',
	BillableItems: 'Laskutettavat tuotteet',
	BillableItemsEmptyState: 'Laskutettavaa tuotetta ei löytynyt',
	Biller: 'Laskuttaja',
	Billing: 'Laskutus',
	BillingAddress: 'Laskutusosoite',
	BillingAndReceiptsUnauthorisedMessage: 'Näiden tietojen käyttö edellyttää laskujen ja maksujen katseluoikeutta.',
	BillingBillablesTab: 'Laskutettavat',
	BillingClaimsTab: 'Väitteet',
	BillingDetails: 'Laskutustiedot',
	BillingDocuments: 'Laskutusasiakirjat',
	BillingDocumentsClaimsTab: 'Väitteet',
	BillingDocumentsEmptyState: 'Ei löydetty {tabType}jä',
	BillingDocumentsInvoicesTab: 'Laskut',
	BillingDocumentsSuperbillsTab: 'Superlaskut',
	BillingInformation: 'Laskutustiedot',
	BillingInvoicesTab: 'Laskut',
	BillingItems: 'Laskutuskohteet',
	BillingPaymentsTab: 'Maksut',
	BillingPeriod: 'Laskutuskausi',
	BillingProfile: 'Laskutusprofiili',
	BillingProfileOverridesDescription:
		'Ohita tietyille tiimin jäsenille, maksajille tai laskumalleille käytetyt oletuslaskutustiedot.',
	BillingProfileOverridesHeader: 'Rajaa pääsy',
	BillingProfileProviderType: 'Palveluntarjoajan tyyppi',
	BillingProfileTypeIndividual: 'Harjoittaja',
	BillingProfileTypeIndividualSubLabel: 'Tyyppi 1 NPI',
	BillingProfileTypeOrganisation: 'Organisaatio',
	BillingProfileTypeOrganisationSubLabel: 'Tyyppi 2 NPI',
	BillingProfiles: 'Laskutusprofiilit',
	BillingProfilesEditHeader: 'Muokkaa {name} laskutusprofiilia',
	BillingProfilesNewHeader: 'Uusi laskutusprofiili',
	BillingProfilesSectionDescription:
		'Hallinnoi ammatinharjoittajien ja vakuutusten maksajien laskutustietoja määrittämällä laskutusprofiileja, joita voidaan soveltaa laskuihin ja vakuutusmaksuihin.',
	BillingSearchPlaceholder: 'Etsi kohteita',
	BillingSettings: 'Laskutusasetukset',
	BillingSuperbillsTab: 'Superlaskut',
	BiomedicalEngineer: 'Biolääketieteen insinööri',
	BlankInvoice: 'Tyhjä lasku',
	BlueShieldProviderNumber: 'Blue Shield -palveluntarjoajan numero',
	Body: 'Runko',
	Bold: 'Lihavoitu',
	BookAgain: 'Varaa uudelleen',
	BookAppointment: 'Varaa tapaaminen',
	BookableOnline: 'Varattavissa netistä',
	BookableOnlineHelper: 'Asiakkaat voivat varata tämän palvelun verkossa',
	BookedOnline: 'Varattu verkossa',
	Booking: 'Varaus',
	BookingAnalyticsIntegrationPanelDescription:
		'Määritä Google Tag Manager seuraamaan avaintoimintoja ja konversioita online-varauskulussasi. Kerää arvokasta tietoa käyttäjien vuorovaikutuksista parantaaksesi markkinointia ja optimoidaksesi varauskokemusta.',
	BookingAnalyticsIntegrationPanelTitle: 'Analyticsin integrointi',
	BookingAndCancellationPolicies: 'Varaus ',
	BookingButtonEmbed: 'Painike',
	BookingButtonEmbedDescription: 'Lisää verkkovarauspainikkeen verkkosivustollesi',
	BookingDirectTextLink: 'Suora tekstilinkki',
	BookingDirectTextLinkDescription: 'Avaa online-varaussivun',
	BookingFormatLink: 'Muotoile linkki',
	BookingFormatLinkButtonTitle: 'Painikkeen otsikko',
	BookingInlineEmbed: 'Upotus upotettuna',
	BookingInlineEmbedDescription: 'Lataa online-varaussivun suoraan verkkosivustollesi',
	BookingLink: 'Varauslinkki',
	BookingLinkModalCopyText: 'Kopioida',
	BookingLinkModalDescription:
		'Salli tämän linkin saaneiden asiakkaiden varata minkä tahansa tiimin jäsenen tai palveluita',
	BookingLinkModalHelpText: 'Opi tekemään online-varauksia',
	BookingLinkModalTitle: 'Jaa varauslinkkisi',
	BookingPolicies: 'Varauskäytännöt',
	BookingPoliciesDescription: 'Aseta, milloin asiakkaat voivat tehdä online-varauksia',
	BookingTimeUnitDays: 'päivää',
	BookingTimeUnitHours: 'tuntia',
	BookingTimeUnitMinutes: 'minuuttia',
	BookingTimeUnitMonths: 'kuukautta',
	BookingTimeUnitWeeks: 'viikkoa',
	BottomNavBilling: 'Laskutus',
	BottomNavGettingStarted: 'Kotiin',
	BottomNavMore: 'Lisää',
	BottomNavNotes: 'Huomautuksia',
	Brands: '<h1>Brändit</h1>',
	Brother: 'Veli',
	BrotherInLaw: 'lanko',
	BrowseOrDragFileHere: '<link>Selaa</link> tai vedä tiedosto tänne',
	BrowseOrDragFileHereDescription: 'PNG, JPG (enintään {limit})',
	BufferAfterTime: '{time} min. jälkeen',
	BufferAndLabel: 'ja',
	BufferAppointmentLabel: 'tapaaminen',
	BufferBeforeTime: '{time} min ennen',
	BufferTime: 'Puskurin aika',
	BufferTimeViewLabel: '{bufferBefore} min ennen ja {bufferAfter} min jälkeen tapaamisia',
	BulkArchiveClientsDescription:
		'Haluatko varmasti arkistoida nämä asiakkaat? Voit aktivoida ne uudelleen myöhemmin.',
	BulkArchiveSuccess: 'Asiakkaiden arkistointi onnistui',
	BulkArchiveUndone: 'Joukkoarkistointi kumottu',
	BulkPermanentDeleteDescription: 'Tämä poistaa **{count} keskustelua**. Tätä toimintoa ei voi peruuttaa.',
	BulkPermanentDeleteTitle: 'Poista keskustelut pysyvästi',
	BulkUnarchiveSuccess: 'Asiakkaiden arkistoinnin poistaminen onnistui',
	BulletedList: 'Luettelomerkitty luettelo',
	BusinessAddress: 'Yrityksen osoite',
	BusinessAddressOptional: 'Yrityksen osoite <span>(valinnainen)</span>',
	BusinessName: 'Yrityksen nimi',
	Button: 'Painike',
	By: 'Tekijä:',
	CHAMPUSIdentificationNumber: 'CHAMPUS-tunnistenumero',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC vaaditaan',
	Calendar: 'Kalenteri',
	CalendarAppSyncFormDescription: 'Synkronoi Carepatron-tapahtumat kohteeseen',
	CalendarAppSyncPanelTitle: 'Yhdistettyjen sovellusten synkronointi',
	CalendarDescription: 'Hallitse tapaamisiasi tai aseta henkilökohtaisia tehtäviä ja muistutuksia',
	CalendarDetails: 'Kalenterin tiedot',
	CalendarDetailsDescription: 'Hallinnoi kalenterin ja tapaamisten näyttöasetuksia.',
	CalendarScheduleNew: 'Aikataulu uusi',
	CalendarSettings: 'Kalenterin asetukset',
	Call: 'Soittaa',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> on liittynyt videopuheluun',
	CallChangeLayoutTextContent: 'Valinta tallennetaan tulevia tapaamisia varten',
	CallIdlePrompt: 'Haluatko mieluummin odottaa liittymistä vai haluatko yrittää myöhemmin uudelleen?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Sivupalkki',
	CallLayoutOptionSpotlight: 'Valokeila',
	CallLayoutOptionTiled: 'Kaakeloitu',
	CallNoAttendees: 'Ei osallistujia kokouksessa.',
	CallSessionExpiredError: 'Istunto vanhentunut. Puhelu on päättynyt. Yritä liittyä uudelleen.',
	CallWithPractitioner: 'Soitto {practitioner} kanssa',
	CallsListCreateButton: 'Uusi puhelu',
	CallsListEmptyState: 'Ei aktiivisia puheluita',
	CallsListItemEndCall: 'Lopeta puhelu',
	CamWarningMessage: 'Kamerassasi on havaittu ongelma',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Salli Carepatronin pääsy kameraan ja mikrofoniin.
 Saat lisätietoja <a>noudattamalla tätä opasta</a>`,
	CameraAndMicIssueModalTitle: 'Kamera ja mikrofoni ovat tukossa',
	CameraQuality: 'Kameran laatu',
	CameraSource: 'Kameran lähde',
	CanModifyReadOnlyEvent: 'Et voi muokata tätä tapahtumaa',
	Canada: 'Kanada',
	Cancel: 'Peruuttaa',
	CancelClientImportDescription: 'Oletko varma, että haluat peruuttaa tämän tuonnin?',
	CancelClientImportPrimaryAction: 'Kyllä, peruuta tuonti',
	CancelClientImportSecondaryAction: 'Jatka muokkaamista',
	CancelClientImportTitle: 'Peruuta asiakkaiden tuonti',
	CancelImportButton: 'Peruuta tuonti',
	CancelPlan: 'Peruuta suunnitelma',
	CancelPlanConfirmation: `Sopimuksen peruuttaminen veloittaa tililtäsi automaattisesti tämän kuukauden erääntyneet saldot.
 Jos haluat laskea laskutetut käyttäjät, voit yksinkertaisesti poistaa tiimin jäsenet ja Carepatron päivittää tilaushintasi automaattisesti.`,
	CancelSend: 'Peruuta lähetys',
	CancelSubscription: 'Peruuta tilaus',
	Canceled: 'Peruutettu',
	CancellationPolicy: 'Peruutusehdot',
	Cancelled: 'Peruutettu',
	CannotContainSpecialCharactersError: 'Ei voi sisältää {specialCharacters}',
	CannotDeleteInvoice: 'Verkkomaksuilla maksettuja laskuja ei voi poistaa',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> ei voida siirtää pois <b>{group}</b>-ryhmästä',
	CannotMoveServiceOutsideCollections: 'Palvelua ei voi siirtää kokoelmien ulkopuolelle',
	CapeTown: 'Kapkaupunki',
	Caption: 'Kuvateksti',
	CaptureNameFieldLabel: 'Nimi, johon haluat muiden viittaavan sinuun',
	CapturePaymentMethod: 'Tallenna maksutapa',
	CapturingAudio: 'Äänen taltiointi',
	CapturingSignature: 'Tallennetaan allekirjoitusta...',
	CardInformation: 'Kortin tiedot',
	CardNumberRequired: 'Kortin numero vaaditaan',
	CardiacRehabilitationSpecialist: 'Sydämen kuntoutuksen asiantuntija',
	Cardiologist: 'Kardiologi',
	CareAiNoConversations: 'Ei vielä keskusteluja',
	CareAiNoConversationsDescription: 'Aloita keskustelu {aiName}:n kanssa aloittaaksesi',
	CareAssistant: 'Hoitoassistentti',
	CareManager: 'Hoitopäällikkö',
	Caregiver: 'Omaishoitaja',
	CaregiverCreateModalDescription:
		'Henkilökunnan lisääminen ylläpitäjiksi antaa heille mahdollisuuden luoda ja hallita hoitotarinoita. Se antaa heille myös täyden pääsyn asiakkaiden luomiseen ja hallintaan.',
	CaregiverCreateModalTitle: 'Uusi tiimin jäsen',
	CaregiverListCantAddStaffInfoTitle:
		'Olet saavuttanut tilauksesi enimmäishenkilömäärän. Päivitä suunnitelmaasi lisätäksesi henkilöstöä.',
	CaregiverListCreateButton: 'Uusi tiimin jäsen',
	CaregiverListEmptyState: 'Hoitajia ei lisätty',
	CaregiversListItemRemoveStaff: 'Poista henkilökunta',
	CarepatronApp: 'Carepatron-sovellus',
	CarepatronCommunity: 'yhteisössä',
	CarepatronFieldAddress: 'Osoite',
	CarepatronFieldAssignedStaff: 'Määrätty henkilökunta',
	CarepatronFieldBirthDate: 'Syntymäpäivä',
	CarepatronFieldEmail: 'Sähköposti',
	CarepatronFieldEmploymentStatus: 'Työllisyystilanne',
	CarepatronFieldEthnicity: 'Etnisyys',
	CarepatronFieldFirstName: 'Etunimi',
	CarepatronFieldGender: 'Sukupuoli',
	CarepatronFieldIdentificationNumber: 'Tunnistusnumero',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Label',
	CarepatronFieldLastName: 'Sukunimi',
	CarepatronFieldLivingArrangements: 'Asumisjärjestelyt',
	CarepatronFieldMiddleNames: 'Toinen nimi',
	CarepatronFieldOccupation: 'Ammatti',
	CarepatronFieldPhoneNumber: 'Puhelinnumero',
	CarepatronFieldRelationshipStatus: 'Suhteen tila',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 tilaa max.',
	CarepatronFieldTags: 'Tunnisteet',
	CarepatronFields: 'Carepatron kentät',
	Cash: 'käteistä',
	Category: 'Luokka',
	CategoryInputPlaceholder: 'Valitse malliluokka',
	CenterAlign: 'Keskitä',
	Central: 'Keski',
	ChangeLayout: 'Muuta asettelua',
	ChangeLogo: 'Muuttaa',
	ChangePassword: 'Vaihda salasana',
	ChangePasswordFailureSnackbar:
		'Valitettavasti salasanaasi ei vaihdettu. Tarkista, että vanha salasanasi on oikein.',
	ChangePasswordHelperInfo: 'Vähintään {minLength} merkkiä',
	ChangePasswordSuccessfulSnackbar:
		'Salasanan vaihto onnistui! Kun seuraavan kerran kirjaudut sisään, muista käyttää tätä salasanaa.',
	ChangeSubscription: 'Muuta tilaus',
	ChangesNotAllowed: 'Tähän kenttään ei voi tehdä muutoksia',
	ChargesDisabled: 'Maksut pois käytöstä',
	ChargesEnabled: 'Veloitukset käytössä',
	ChargesStatus: 'Maksujen tila',
	ChartAndDiagram: 'Kaavio/Diagrammi',
	ChartsAndDiagramsCategoryDescription: 'Asiakastietojen ja edistymisen havainnollistamiseksi',
	ChatEditMessage: 'Muokkaa viestiä',
	ChatReplyTo: 'Vastaa {name}lle',
	ChatTypeMessageTo: 'Viesti {name}',
	Check: 'Tarkista',
	CheckList: 'Tarkistuslista',
	Chef: 'Kokki',
	Chiropractic: 'Kiropraktiikka',
	Chiropractor: 'Kiropraktikko',
	Chiropractors: 'Kiropraktikot',
	ChooseACollection: 'Valitse kokoelma',
	ChooseAContact: 'Valitse yhteystieto',
	ChooseAccountTypeHeader: 'Mikä kuvaa sinua parhaiten?',
	ChooseAction: 'Valitse toiminto',
	ChooseAnAccount: 'Valitse tili',
	ChooseAnOption: 'Valitse vaihtoehto',
	ChooseBillingProfile: 'Valitse laskutusprofiili',
	ChooseClaim: 'Valitse vaatimus',
	ChooseCollection: 'Valitse kokoelma',
	ChooseColor: 'Valitse väri',
	ChooseCustomDate: 'Valitse mukautettu päivämäärä',
	ChooseDateAndTime: 'Valitse päivämäärä ja aika',
	ChooseDxCodes: 'Valitse diagnoosikoodit',
	ChooseEventType: 'Valitse tapahtumatyyppi',
	ChooseFileButton: 'Valitse tiedosto',
	ChooseFolder: 'Valitse kansio',
	ChooseInbox: 'Valitse postilaatikko',
	ChooseMethod: 'Valitse menetelmä',
	ChooseNewOwner: 'Valitse uusi omistaja',
	ChooseOrganization: 'Valitse Organisaatio',
	ChoosePassword: 'Valitse salasana',
	ChoosePayer: 'Valitse maksaja',
	ChoosePaymentMethod: 'Valitse maksutapa',
	ChoosePhysicalOrRemoteLocations: 'Kirjoita tai valitse sijainti',
	ChoosePlan: 'Valitse {plan}',
	ChooseProfessional: 'Valitse Ammattilainen',
	ChooseServices: 'Valitse palvelut',
	ChooseSource: 'Valitse lähde',
	ChooseSourceDescription:
		'Valitse, mistä tuodaksesi asiakkaat – tiedostosta vai jostakin muusta ohjelmistoplatformista.',
	ChooseTags: 'Valitse tunnisteet',
	ChooseTaxName: 'Valitse veron nimi',
	ChooseTeamMembers: 'Valitse tiimijäsenet',
	ChooseTheme: 'Valitse teema',
	ChooseTrigger: 'Valitse laukaisin',
	ChooseYourProvider: 'Valitse palveluntarjoajasi',
	CircularProgressWithLabel: '{value}%',
	City: 'Kaupunki',
	CivilEngineer: 'Rakennusinsinööri',
	Claim: 'Väite',
	ClaimAddReferringProvider: 'Lisää lähettävä hoitotaho',
	ClaimAddRenderingProvider: 'Lisää renderöintipalveluntarjoaja',
	ClaimAmount: 'Väitesumma',
	ClaimAmountPaidHelpContent:
		'Maksettu summa on potilaalta tai muilta maksajilta saatu maksu. Syötä kokonaissumma, jonka potilas ja/tai muut maksajat maksoivat vain katetuista palveluista.',
	ClaimAmountPaidHelpSubtitle: 'Kenttä 29',
	ClaimAmountPaidHelpTitle: 'Maksettu summa',
	ClaimBillingProfileTypeIndividual: 'Yksilöllinen',
	ClaimBillingProfileTypeOrganisation: 'Organisaatio',
	ClaimChooseRenderingProviderOrTeamMember: 'Valitse renderöinnin tarjoaja tai tiimin jäsen',
	ClaimClientInsurancePolicies: 'Asiakkaan vakuutukset',
	ClaimCreatedAction: '<mark>Vaatimus {claimNumber}</mark> luotu',
	ClaimDeniedAction: '<mark>Vakuutus {claimNumber}</mark> hylättiin <b>{payerNumber} {payerName}</b>:n toimesta',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Etsi ICD 10 -diagnoosikoodeja',
	ClaimDiagnosisSelectorHelpContent: `"Diagnoosi tai vamma" on potilaan merkki, oire, valitus tai tila, joka liittyy vaatimuksen mukaisiin palveluihin.
 Enintään 12 ICD 10 -diagnoosikoodia voidaan valita.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Kenttä 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnoosi tai loukkaantuminen',
	ClaimDiagnosticCodesEmptyError: 'Vähintään yksi diagnoosikoodi on pakollinen',
	ClaimDoIncludeReferrerInformation: 'Sisällytä CMS1500:n viitetiedot',
	ClaimERAReceivedAction: 'Sähköinen tilisiirto vastaanotettu lähettäjältä <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Sähköinen maksu vastaanotettu	<mark>Maksu {paymentReference}</mark> summasta <b>{paymentAmount}</b> maksajalta <b>{payerNumber} {payerName}</b> kirjattiin',
	ClaimExportedAction: '<mark>Vakuutus {claimNumber}</mark> viettiin <b>{attachmentType}</b>:na',
	ClaimFieldClient: 'Asiakkaan tai yhteyshenkilön nimi',
	ClaimFieldClientAddress: 'Asiakkaan osoite',
	ClaimFieldClientAddressDescription:
		'Syötä asiakkaan osoite. Ensimmäinen rivi on katuosoite. Älä käytä osoitteissa välimerkkejä (pilkkuja tai pisteitä) tai muita symboleja. Jos ilmoitat ulkomaan osoitteen, ota yhteyttä maksajaan saadaksesi tarkat ilmoitusohjeet.',
	ClaimFieldClientAddressSubtitle: 'Kenttä 5',
	ClaimFieldClientDateOfBirth: 'Asiakkaan syntymäaika',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Kenttä 3',
	ClaimFieldClientDateOfBirthDescription:
		'Syötä asiakkaan 8-numeroinen syntymäaika (KK/PP/VVVV). Asiakkaan syntymäaika on tieto, joka tunnistaa asiakkaan ja erottaa samannimiset henkilöt.',
	ClaimFieldClientDescription: `Asiakkaan nimi' on hoidon tai tarvikkeita saaneen henkilön nimi.`,
	ClaimFieldClientSexDescription: '"Sukupuoli" on tieto, joka tunnistaa asiakkaan ja erottaa samannimiset henkilöt.',
	ClaimFieldClientSubtitle: 'Kenttä 2',
	ClaimFiling: 'Vaatimuksen tekeminen',
	ClaimHistorySubtitle: 'Vakuutus • Vaatimus {number}',
	ClaimIncidentAutoAccident: 'Auto-onnettomuus?',
	ClaimIncidentConditionRelatedTo: 'Liittyykö asiakkaan tila',
	ClaimIncidentConditionRelatedToHelpContent:
		'Nämä tiedot osoittavat, liittyykö asiakkaan sairaus tai vamma työsuhteeseen, auto-onnettomuuteen tai muuhun tapaturmaan. Työllisyys (nykyinen tai edellinen) osoittaisi, että tila liittyy asiakkaan työhön tai työpaikkaan. Auto-onnettomuus osoittaisi, että olosuhteet ovat seurausta auto-onnettomuudesta. Muu onnettomuus osoittaisi, että tila on seurausta minkä tahansa muun tyyppisestä onnettomuudesta.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Kentät 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Liittyykö asiakkaan tila',
	ClaimIncidentCurrentIllness: 'Nykyinen sairaus, vamma tai raskaus',
	ClaimIncidentCurrentIllnessHelpContent:
		'Nykyisen sairauden, vamman tai raskauden päivämäärä ilmaisee ensimmäisen sairauden alkamispäivän, todellisen vamman päivämäärän tai raskauden LMP:n.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Kenttä 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Nykyisen sairauden, vamman tai raskauden päivämäärät (LMP)',
	ClaimIncidentDate: 'Päivämäärä',
	ClaimIncidentDateFrom: 'Päivämäärä alkaen',
	ClaimIncidentDateTo: 'Päivämäärä',
	ClaimIncidentEmploymentRelated: 'Työllisyys',
	ClaimIncidentEmploymentRelatedDesc: '(nykyinen tai edellinen)',
	ClaimIncidentHospitalizationDatesLabel: 'Nykyisiin palveluihin liittyvät sairaalahoitopäivät',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Nykyisiin palveluihin liittyvät sairaalahoitopäivämäärät viittaavat asiakkaan oleskeluun ja ilmoittavat hakemuksessa palveluun/palveluihin liittyvät vastaanotto- ja kotiutuspäivämäärät.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Kenttä 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Nykyisiin palveluihin liittyvät sairaalahoitopäivät',
	ClaimIncidentInformation: 'Tapahtumatiedot',
	ClaimIncidentOtherAccident: 'Muu onnettomuus?',
	ClaimIncidentOtherAssociatedDate: 'Muu asiaan liittyvä päivämäärä',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Toinen päivämäärä ilmaisee lisätietoa asiakkaan tilasta tai hoidosta.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Kenttä 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Muu päivämäärä',
	ClaimIncidentQualifier: 'Karsinta',
	ClaimIncidentQualifierPlaceholder: 'Valitse karsinta',
	ClaimIncidentUnableToWorkDatesLabel: 'Asiakas ei pystynyt työskentelemään nykyisessä ammatissa',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Päivämäärät, jolloin asiakas ei voinut työskennellä nykyisessä ammatissa, on ajanjakso, jolloin asiakas on tai ei pystynyt työskentelemään',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Kenttä 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Päivämäärät asiakas ei voinut työskennellä nykyisessä ammatissa',
	ClaimIncludeReferrerInformation: 'Sisällytä CMS1500:n viitetiedot',
	ClaimInsuranceCoverageTypeHelpContent: `Tähän vaatimukseen sovellettava sairausvakuutuksen tyyppi. Muu osoittaa sairausvakuutuksen, mukaan lukien HMO:t, kaupalliset vakuutukset, auto-onnettomuudet, vastuut tai työntekijöiden korvaukset.
 Nämä tiedot ohjaavat vaatimuksen oikeaan ohjelmaan ja voivat luoda ensisijaisen vastuun.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Kenttä 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Peittotyyppi',
	ClaimInsuranceGroupIdHelpContent: `Syötä vakuutetun vakuutus- tai ryhmänumero sellaisena kuin se on vakuutetun terveydenhuollon henkilökortissa.

 "Vakuutetun vakuutus-, ryhmä- tai FECA-numero" on aakkosnumeerinen tunniste sairaus-, auto- tai muun vakuutuksen kattavuudelle. FECA-numero on 9-merkkinen aakkosnumeerinen tunniste, joka on määritetty työhön liittyvän sairauden ilmoittavalle potilaalle.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Kenttä 11',
	ClaimInsuranceGroupIdHelpTitle: 'Vakuutetun vakuutus-, ryhmä- tai FECA-numero',
	ClaimInsuranceMemberIdHelpContent: `Syötä sen maksajan vakuutetun henkilöllisyystodistus, jolle korvaushakemus esitetään.
 Jos potilaalla on maksajan myöntämä yksilöllinen jäsennumero, kirjoita se tähän kenttään.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Kenttä 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Vakuutetun jäsentunnus',
	ClaimInsurancePayer: 'Vakuutuksen maksaja',
	ClaimManualPaymentAction: '<mark>Maksu {paymentReference}</mark> <b>{paymentAmount}</b>:lle rekisteröity',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Vaatimuksen lisätiedot',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Ole hyvä ja katso tämän kentän käytöstä annettavat ohjeet julkisilta tai yksityisiltä maksajilta. Raportoi sopiva määrite, jos sellainen on saatavilla, syötetylle tiedolle.Älä syötä välilyöntiä, viivaa tai muuta erotinta määritteen ja tiedon väliin.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Kenttä 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Lisätietoja vaatimuksesta',
	ClaimMiscClaimCodes: 'Hakemuskoodit',
	ClaimMiscOriginalReferenceNumber: 'Alkuperäinen viitenumero',
	ClaimMiscPatientsAccountNumber: 'Potilaan tilinumero',
	ClaimMiscPatientsAccountNumberHelpContent: 'Potilaan tilinumero on palveluntarjoajan antama tunniste.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Kenttä 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Potilaan tilinumero',
	ClaimMiscPriorAuthorizationNumber: 'Ennakkolupanumero',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Aiempi valtuutusnumero on maksajalle annettu numero, joka valtuuttaa palvelun/palvelut.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Kenttä 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Ennakkolupanumero',
	ClaimMiscResubmissionCode: 'Uudelleenlähetyskoodi',
	ClaimMiscResubmissionCodeHelpContent:
		'Uudelleenlähetyksellä tarkoitetaan kohdemaksajan tai vastaanottajan antamaa koodia ja alkuperäistä viitenumeroa, joka osoittaa aiemmin lähetetyn vaatimuksen tai tapaamisen.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Kenttä 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Uudelleenlähetys ja/tai alkuperäinen viitenumero',
	ClaimNumber: 'Vahinkonumero',
	ClaimNumberFormat: 'Väite #{number}',
	ClaimOrderingProvider: 'Tilaustoimittaja',
	ClaimOtherId: 'Muu tunnus',
	ClaimOtherIdPlaceholder: 'Valitse vaihtoehto',
	ClaimOtherIdQualifier: 'Muu tunnuksen tarkenne',
	ClaimOtherIdQualifierPlaceholder: 'Valitse tunnuksen tarkenne',
	ClaimPlaceOfService: 'Palvelun paikka',
	ClaimPlaceOfServicePlaceholder: 'Lisää POS',
	ClaimPolicyHolderRelationship: 'Vakuutuksenottajan suhde',
	ClaimPolicyInformation: 'Käytäntötiedot',
	ClaimPolicyTelephone: 'Puhelin (sisällytä suuntanumero)',
	ClaimReceivedAction: '<mark>Vakuutus {claimNumber}</mark> vastaanotettu <b>{name}</b>:ltä',
	ClaimReferringProvider: 'Viittaava palveluntarjoaja',
	ClaimReferringProviderEmpty: 'Ei lisättyjä lähettejä',
	ClaimReferringProviderHelpContent:
		'Syötetty nimi on viittaava palveluntarjoaja, tilaustoimittaja tai valvova palveluntarjoaja, joka viittasi, tilasi tai valvoi vaatimuksen kohteena olevia palveluita tai toimituksia. Tarkenne ilmaisee raportoitavan palveluntarjoajan roolin.',
	ClaimReferringProviderHelpSubtitle: 'Kenttä 17',
	ClaimReferringProviderHelpTitle: 'Viittaavan palveluntarjoajan tai lähteen nimi',
	ClaimReferringProviderQualifier: 'Karsinta',
	ClaimReferringProviderQualifierPlaceholder: 'Valitse karsinta',
	ClaimRejectedAction: '<mark>Vaatimus {claimNumber}</mark> hylättiin <b>{name}</b>:n toimesta',
	ClaimRenderingProviderIdNumber: 'ID-numero',
	ClaimRenderingProviderOrTeamMember: 'Renderöinnin tarjoaja tai tiimin jäsen',
	ClaimRestoredAction: '<mark>Vakuutus {claimNumber}</mark> palautettiin',
	ClaimServiceFacility: 'Palvelukeskus',
	ClaimServiceFacilityLocationHelpContent:
		'Palveluntarjoajan nimi ja osoite osoittavat paikan, jossa palvelu(t) tarjottiin.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a ja 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Kentät 32, 32a ja 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Palvelukeskus',
	ClaimServiceFacilityPlaceholder: 'Valitse palvelupiste tai paikka',
	ClaimServiceLabChargesHelpContent: `Täytä tämä kenttä, kun haet ostettuja palveluita, joita tarjoaa muu taho kuin laskutuspalveluntarjoaja.
 Jokaisesta ostetusta palvelusta tulee ilmoittaa erillisessä reklamaatiossa, koska CMS1500-lomakkeelle voidaan syöttää vain yksi maksu.`,
	ClaimServiceLabChargesHelpSubtitle: 'Kenttä 20',
	ClaimServiceLabChargesHelpTitle: 'Laboratoriomaksujen ulkopuolella',
	ClaimServiceLineServiceHelpContent:
		'"Toimenpiteet, palvelut tai tarvikkeet" yksilöivät potilaalle tarjottavat lääketieteelliset palvelut ja toimenpiteet.',
	ClaimServiceLineServiceHelpSubtitle: 'Kenttä 24d',
	ClaimServiceLineServiceHelpTitle: 'Menettelyt, palvelut tai tarvikkeet',
	ClaimServiceLinesEmptyError: 'Vähintään yhden palvelualan on oltava',
	ClaimServiceSupplementaryInfoHelpContent: `Lisää selosteellinen kuvaus palveluista, joita tarjotaan soveltuvilla määritteillä.
 Älä kirjoita välilyöntiä, tavuviivaa tai muuta erotinta tarkenteen ja tiedon väliin.

 Katso täydelliset ohjeet lisätietojen lisäämisestä CMS 1500 -vaatimuslomakkeen ohjeista.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Kenttä 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Lisätiedot',
	ClaimSettingsBillingMethodTitle: 'Asiakkaan laskutustapa',
	ClaimSettingsClientSignatureDescription:
		'Minulla on lupa luovuttaa lääketieteellisiä tai muita vakuutuskorvausten käsittelyyn tarvittavia tietoja.',
	ClaimSettingsClientSignatureTitle: 'Asiakkaan allekirjoitus tiedostossa',
	ClaimSettingsConsentLabel: 'Vakuutusvaatimusten käsittelyyn vaaditaan suostumus:',
	ClaimSettingsDescription: 'Valitse asiakkaan laskutustapa varmistaaksesi maksujen sujuvan käsittelyn:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name}lla on voimassa oleva vakuutus. Vakuutuslaskun käyttöönottamiseksi päivitä Asiakkaan laskutusmenetelmä Vakuutukseksi.',
	ClaimSettingsInsuranceDescription: 'Kustannukset korvataan vakuutuksesta',
	ClaimSettingsInsuranceTitle: 'Vakuutus',
	ClaimSettingsNoPoliciesAlertDescription: 'Lisää vakuutus vakuutuskorvausten mahdollistamiseksi.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Olen suostunut vastaanottamaan vakuutusmaksuja tarjoamistani palveluista.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Vakuutuksenottajan allekirjoitus tiedostossa',
	ClaimSettingsSelfPayDescription: 'Asiakas maksaa tapaamisista',
	ClaimSettingsSelfPayTitle: 'Itse maksava',
	ClaimSettingsTitle: 'Vaatimusasetukset',
	ClaimSexSelectorPlaceholder: 'Mies / Nainen',
	ClaimStatusChangedAction: '<mark>Vaatimus {claimNumber}</mark> -tilanne on päivitetty',
	ClaimSubmittedAction:
		'<mark>Vaatimus {claimNumber}</mark> lähetetty <b>{payerClearingHouse}</b>:lle <b>{payerNumber} {payerName}</b>:lle',
	ClaimSubtitle: 'Vakuutus #{claimNumber}',
	ClaimSupervisingProvider: 'Valvojan tarjoaja',
	ClaimSupplementaryInfo: 'Lisätiedot',
	ClaimSupplementaryInfoPlaceholder: 'Lisää lisätiedot',
	ClaimTrashedAction: '<mark>Vaatimus {claimNumber}</mark> poistettiin',
	ClaimValidationFailure: 'Vaatimuksen validointi epäonnistui',
	ClaimsEmptyStateDescription: 'Vaatimuksia ei löytynyt.',
	ClainInsuranceTelephone: 'Vakuutuspuhelin (sisältää suuntanumeron)',
	Classic: 'Klassinen',
	Clear: 'Selkeä',
	ClearAll: 'Tyhjennä kaikki',
	ClearSearchFilter: 'Selkeä',
	ClearingHouse: 'Talon siivoaminen',
	ClearingHouseClaimId: 'Claim.MD-tunnus',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Talon viitenumero',
	ClearingHouseUnavailableError:
		'Selvitystalon palvelut eivät ole tällä hetkellä käytettävissä. Yritä uudelleen myöhemmin.',
	ClickToUpload: 'Lataa napsauttamalla',
	Client: 'Asiakas',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} lisäsi {noteTitle, select, undefined {muistiinpanon} other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Valitse asiakkaat ja heidän suhteensa',
	ClientAndRelationshipSelectorTitle: 'Kaikki asiakkaat ja heidän suhteensa',
	ClientAndRelationshipSelectorTitle1: 'Kaikki ‘{name}’n suhteet',
	ClientAppCallsPageNoOptionsText:
		'Jos odotat videopuhelua, se näkyy pian täällä. Jos sinulla on ongelmia, ota yhteyttä asian aloitteentekijään.',
	ClientAppSubHeaderMyDocumentation: 'Minun dokumentaationi',
	ClientAppointment: 'Asiakasvaraus',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} on varannut {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Aikoja ei ole löytynyt',
	ClientAppointmentsEmptyStateTitle:
		'Pidä kirjaa asiakkaidesi tulevista ja historiallisista tapaamisista ja heidän osallistumisestaan',
	ClientArchivedSuccessfulSnackbar: 'Onnistuneesti arkistoitu <b>{name}</b>',
	ClientBalance: 'Asiakkaan saldo',
	ClientBilling: 'Laskutus',
	ClientBillingAddPaymentMethodDescription:
		'Lisää ja hallitse asiakkaasi maksutapoja yksinkertaistaaksesi heidän laskutus- ja laskutusprosessiaan.',
	ClientBillingAndPaymentDueDate: 'Eräpäivä',
	ClientBillingAndPaymentHistory: 'Laskutus- ja maksuhistoria',
	ClientBillingAndPaymentInvoices: 'Laskut',
	ClientBillingAndPaymentIssueDate: 'Julkaisupäivämäärä',
	ClientBillingAndPaymentPrice: 'Hinta',
	ClientBillingAndPaymentReceipt: 'Kuitti',
	ClientBillingAndPaymentServices: 'Palvelut',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Tiimiin {count, plural, one {jäsen} other {jäsenet}} määrätty!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Tiimi {count, plural, one {jäsen} other {jäsenet}} ei ole osoitettu!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tunnisteet lisätty!',
	ClientDuplicatesDeviewDescription:
		'Yhdistä useita asiakastietueita yhdeksi yhdistääksesi kaikki tiedot – muistiinpanot, asiakirjat, tapaamiset, laskut ja keskustelut.',
	ClientDuplicatesPageMergeHeader: 'Valitse tiedot, jotka haluat säilyttää',
	ClientDuplicatesReviewHeader: 'Vertaa mahdollisia päällekkäisiä tietueita yhdistämistä varten',
	ClientEmailChangeWarningDescription:
		'Asiakkaan sähköpostin päivittäminen poistaa hänen pääsynsä jaettuun dokumentaatioon ja antaa käyttöoikeuden käyttäjälle, jolla on uusi sähköposti.',
	ClientFieldDateDescription: 'Muotoile päivämäärä',
	ClientFieldDateLabel: 'Päivämäärä',
	ClientFieldDateRangeDescription: 'Päivämäärät',
	ClientFieldDateRangeLabel: 'Ajanjakso',
	ClientFieldDateShowDateDescription: 'esim 29 vuotta',
	ClientFieldDateShowDateRangeDescription: 'esim 2 viikkoa',
	ClientFieldEmailDescription: 'Sähköpostiosoite',
	ClientFieldEmailLabel: 'Sähköposti',
	ClientFieldLabel: 'Kentän etiketti',
	ClientFieldLinearScaleDescription: 'Mittakaavavaihtoehdot 1-10',
	ClientFieldLinearScaleLabel: 'Lineaarinen asteikko',
	ClientFieldLocationDescription: 'Fyysinen tai postiosoite',
	ClientFieldLocationLabel: 'Sijainti',
	ClientFieldLongTextDescription: 'Pitkä tekstialue',
	ClientFieldLongTextLabel: 'Kohta',
	ClientFieldMultipleChoiceDropdownDescription: 'Valitse luettelosta useita vaihtoehtoja',
	ClientFieldMultipleChoiceDropdownLabel: 'Monivalintavalikko',
	ClientFieldPhoneNumberDescription: 'Puhelinnumero',
	ClientFieldPhoneNumberLabel: 'Puhelin',
	ClientFieldPlaceholder: 'Valitse asiakaskentän tyyppi',
	ClientFieldSingleChoiceDropdownDescription: 'Valitse luettelosta vain yksi vaihtoehto',
	ClientFieldSingleChoiceDropdownLabel: 'Yhden valinnan pudotusvalikko',
	ClientFieldTextDescription: 'Tekstinsyöttökenttä',
	ClientFieldTextLabel: 'Teksti',
	ClientFieldYesOrNoDescription: 'Valitse kyllä tai ei vaihtoehdoista',
	ClientFieldYesOrNoLabel: 'Kyllä | Ei',
	ClientFileFormAccessLevelDescription:
		'Sinulla ja tiimillä on aina pääsy lataamiisi tiedostoihin. Voit halutessasi jakaa tämän tiedoston asiakkaan ja/tai hänen suhteensa kanssa',
	ClientFileSavedSuccessSnackbar: 'Tiedosto tallennettu!',
	ClientFilesPageEmptyStateText: 'Ei ladattuja tiedostoja',
	ClientFilesPageUploadFileButton: 'Lataa tiedostoja',
	ClientHeaderBilling: 'Laskutus',
	ClientHeaderBillingAndReceipts: 'Laskutus ',
	ClientHeaderDocumentation: 'Dokumentaatio',
	ClientHeaderDocuments: 'Asiakirjat',
	ClientHeaderFile: 'Asiakirja',
	ClientHeaderHistory: 'Lääketieteellinen historia',
	ClientHeaderInbox: 'Saapuneet',
	ClientHeaderNote: 'Huom',
	ClientHeaderOverview: 'Yleiskatsaus',
	ClientHeaderProfile: 'Henkilökohtainen',
	ClientHeaderRelationship: 'Suhde',
	ClientHeaderRelationships: 'Suhteet',
	ClientId: 'Asiakastunnus',
	ClientImportProcessingDescription: 'Tiedostoa käsitellään yhä. Ilmoitamme sinulle, kun se on valmis.',
	ClientImportReadyForMappingDescription:
		'Olemme saaneet tiedostosi esikäsittelyn valmiiksi. Haluatko yhdistää sarakkeet suorittaaksesi tämän tuonnin?',
	ClientImportReadyForMappingNotificationSubject:
		'Asiakkaan tuonnin esikäsittely on valmis. Tiedosto on nyt valmis yhdistämistä varten.',
	ClientInAppMessaging: 'Asiakassovelluksen sisäinen viestintä',
	ClientInfoAddField: 'Lisää toinen kenttä',
	ClientInfoAddRow: 'Lisää rivi',
	ClientInfoAlertMessage: 'Kaikki tässä osiossa täytetyt tiedot täyttävät asiakastietueen.',
	ClientInfoFormPrimaryText: 'Asiakastiedot',
	ClientInfoFormSecondaryText: 'Kerää yhteystiedot',
	ClientInfoPlaceholder: `Asiakkaan nimi, sähköpostiosoite, puhelinnumero
 fyysinen osoite,
 Syntymäaika`,
	ClientInformation: 'Asiakastiedot',
	ClientInsuranceTabLabel: 'Vakuutus',
	ClientIntakeFormsNotSupported: `Lomakemalleja ei tällä hetkellä tueta asiakastulojen kautta.
 Luo ja jaa ne sen sijaan asiakasmuistiinpanoina.`,
	ClientIntakeModalDescription:
		'Asiakkaallesi lähetetään vastaanottosähköposti, jossa häntä pyydetään täydentämään profiilinsa, lataamaan asiaankuuluvat lääketieteelliset tai läheteasiakirjat. Heille annetaan pääsy asiakasportaaliin.',
	ClientIntakeModalTitle: 'Lähetä vastaanotto {name}lle',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Menestys! Saantisi on tallennettu.',
	ClientIntakeSuccessSnackbar: 'Menestys! Saavutuksesi on tallennettu ja vahvistussähköposti lähetetty.',
	ClientIsChargedProcessingFee: 'Asiakkaasi maksavat käsittelymaksun',
	ClientListCreateButton: 'Uusi asiakas',
	ClientListEmptyState: 'Asiakkaita ei lisätty',
	ClientListPageItemArchive: 'Poista asiakas',
	ClientListPageItemRemoveAccess: 'Poista pääsyni',
	ClientLocalizationPanelDescription: 'Asiakkaan suosima kieli ja aikavyöhyke.',
	ClientLocalizationPanelTitle: 'Kieli ja aikavyöhyke',
	ClientManagementAndEHR: 'Asiakashallinta ',
	ClientMergeResultSummaryBanner:
		'Tietueiden yhdistäminen yhdistää kaikki asiakastiedot, mukaan lukien muistiinpanot, asiakirjat, tapaamiset, laskut ja keskustelut. Tarkista tarkkuus ennen jatkamista.',
	ClientMergeResultSummaryTitle: 'Yhdistämistuloksen yhteenveto',
	ClientModalTitle: 'Uusi asiakas',
	ClientMustHaveEmaillAccessErrorText: 'Asiakkaat/yhteyshenkilöt ilman sähköposteja',
	ClientMustHavePortalAccessErrorText: 'Ilmoittautumiseen vaaditaan asiakkaita/yhteyshenkilöitä',
	ClientMustHaveZoomAppConnectedErrorText: 'Yhdistä Zoom valitsemalla Asetukset > Yhdistetyt sovellukset',
	ClientNameFormat: 'Asiakkaan nimen muoto',
	ClientNotFormAccessLevel: 'Katsoja:',
	ClientNotFormAccessLevelDescription:
		'Sinulla ja tiimillä on aina pääsy julkaisemiisi muistiinpanoihin. Voit halutessasi jakaa tämän muistiinpanon asiakkaan ja/tai hänen suhteensa kanssa',
	ClientNotRegistered: 'Ei rekisteröity',
	ClientNoteFormAddFileButton: 'Liitä tiedostot',
	ClientNoteFormChooseAClient: 'Valitse asiakas/yhteyshenkilö jatkaaksesi',
	ClientNoteFormContent: 'Sisältö',
	ClientNoteItemDeleteConfirmationModalDescription: 'Kun muistiinpano on poistettu, et voi palauttaa sitä uudelleen.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Huomautus julkaistu ja lukittu.',
	ClientNotePublishedSuccessSnackbar: 'Huomautus julkaistu!',
	ClientNotes: 'Asiakkaan muistiinpanot',
	ClientNotesEmptyStateText:
		'Voit lisätä muistiinpanoja siirtymällä asiakkaan profiiliin ja napsauttamalla Muistiinpanot-välilehteä.',
	ClientOnboardingChoosePasswordTitle1: 'Melkein valmis!',
	ClientOnboardingChoosePasswordTitle2: 'Valitse salasana',
	ClientOnboardingCompleteIntake: 'Täydellinen saanti',
	ClientOnboardingConfirmationScreenText:
		'Olet antanut kaikki tiedot, joita {providerName} tarvitsee.	Vahvista sähköpostiosoitteesi aloittaaksesi perehdytyksen. Jos et saa sitä heti, tarkista roskapostikansiosi.',
	ClientOnboardingConfirmationScreenTitle: 'Hienoa! Tarkista postilaatikkosi.',
	ClientOnboardingDashboardButton: 'Siirry Dashboardiin',
	ClientOnboardingHealthRecordsDesc1: 'Haluatko jakaa viitekirjeitä tai asiakirjoja {providerName}:n kanssa?',
	ClientOnboardingHealthRecordsDescription: 'Lisää kuvaus (valinnainen)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentaatio',
	ClientOnboardingPasswordRequirements: 'Vaatimukset',
	ClientOnboardingPasswordRequirementsConditions1: 'Vähintään 9 merkkiä vaaditaan',
	ClientOnboardingProviderIntroSignupButton: 'Ilmoittaudu itselleni',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Rekisteröidy perheenjäseneksi',
	ClientOnboardingProviderIntroTitle: '{name} on kutsunut sinut liittymään heidän Carepatron-alustaansa',
	ClientOnboardingRegistrationInstructions: 'Anna henkilötietosi alle.',
	ClientOnboardingRegistrationTitle: 'Ensin tarvitsemme henkilökohtaisia tietoja',
	ClientOnboardingStepFormsAndAgreements: 'Lomakkeet ja sopimukset',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Täytä seuraavat lomakkeet {providerName} -vastaanoton prosessia varten',
	ClientOnboardingStepHealthDetails: 'Terveystiedot',
	ClientOnboardingStepPassword: 'Salasana',
	ClientOnboardingStepYourDetails: 'Tietosi',
	ClientPaymentMethodDescription:
		'Tallenna maksutapa profiiliisi, jotta seuraava tapaamisen varaus ja laskutus ovat nopeampia ja turvallisempia.',
	ClientPortal: 'Asiakasportaali',
	ClientPortalDashboardEmptyDescription: 'Tapaamishistoriasi ja osallistumisesi näkyvät täällä.',
	ClientPortalDashboardEmptyTitle:
		'Pidä kirjaa kaikista tulevista, pyydettyistä ja menneistä tapaamisista sekä osallistumisestasi',
	ClientPreferredNotificationPanelDescription:
		'Hallinnoi asiakkaasi haluamaa tapaa saada päivityksiä ja ilmoituksia seuraavien kautta:',
	ClientPreferredNotificationPanelTitle: 'Suositeltu ilmoitustapa',
	ClientProcessingFee: 'Maksu sisältää ({currencyCode}) {amount} käsittelymaksun',
	ClientProfileAddress: 'Osoite',
	ClientProfileDOB: 'Syntymäaika',
	ClientProfileEmailHelperText: 'Sähköpostin lisääminen antaa portaalin käyttöoikeuden',
	ClientProfileEmailHelperTextMoreInfo:
		'Kun asiakkaalle myönnetään pääsy portaaliin, tiimin jäsenet voivat jakaa muistiinpanoja, tiedostoja ja muita asiakirjoja',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Tunnistusnumero',
	ClientRelationshipsAddClientOwnerButton: 'Kutsu asiakas',
	ClientRelationshipsAddFamilyButton: 'Kutsu perheenjäsen',
	ClientRelationshipsAddStaffButton: 'Lisää henkilökunnan käyttöoikeudet',
	ClientRelationshipsEmptyStateText: 'Suhteita ei lisätty',
	ClientRemovedSuccessSnackbar: 'Asiakas poistettu onnistuneesti.',
	ClientResponsibility: 'Asiakkaan vastuu',
	ClientSavedSuccessSnackbar: 'Asiakas tallennettu onnistuneesti.',
	ClientTableClientName: 'Asiakkaan nimi',
	ClientTablePhone: 'Puhelin',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Arkistointi peruttu onnistuneesti <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} muokkasi {noteTitle, select, undefined { muistiinpanon } other {{noteTitle}}}',
	ClientView: 'Asiakasnäkymä',
	Clients: 'Asiakkaat',
	ClientsTable: 'Asiakastaulukko',
	ClinicalFormat: 'Kliininen muoto',
	ClinicalPsychologist: 'Kliininen psykologi',
	Close: 'Lähellä',
	CloseImportClientsModal: 'Haluatko varmasti peruuttaa asiakkaiden tuonnin?',
	CloseReactions: 'Lähireaktiot',
	Closed: 'Suljettu',
	Coaching: 'Valmennus',
	Code: 'Koodi',
	CodeErrorMessage: 'Koodi vaaditaan',
	CodePlaceholder: 'Koodi',
	Coinsurance: 'Rinnakkaisvakuutus',
	Collection: 'Kokoelma',
	CollectionName: 'Kokoelman nimi',
	Collections: 'Kokoelmat',
	ColorAppointmentsBy: 'Värivaraukset mennessä',
	ColorTheme: 'Väri teema',
	ColourCalendarBy: 'Värillinen kalenteri',
	ComingSoon: 'Tulossa pian',
	Community: 'yhteisössä',
	CommunityHealthLead: 'Yhteisön terveysjohtaja',
	CommunityHealthWorker: 'Yhteisön terveystyöntekijä',
	CommunityTemplatesSectionDescription: 'Luotu Carepatron-yhteisön toimesta',
	CommunityTemplatesSectionTitle: 'yhteisössä',
	CommunityUser: 'Yhteisön käyttäjä',
	Complete: 'Täydellinen',
	CompleteAndLock: 'Täydellinen ja lukittu',
	CompleteSetup: 'Täydellinen asennus',
	CompleteSetupSuccessDescription: 'Olet suorittanut joitain tärkeitä askelia Carepatronin hallinnan suuntaan.',
	CompleteSetupSuccessDescription2: 'Avaa lisää tapoja tehostaa käytäntöäsi ja tukea asiakkaitasi.',
	CompleteSetupSuccessTitle: 'Onnistui! Olet uskomattoman hyvä!',
	CompleteStripeSetup: 'Täydellinen Stripen asennus',
	Completed: 'Valmis',
	ComposeSms: 'Kirjoita tekstiviesti',
	ComputerSystemsAnalyst: 'Tietokonejärjestelmäanalyytikko',
	Confirm: 'Vahvistaa',
	ConfirmDeleteAccountDescription:
		'Olet poistamassa tilisi. Tätä toimintoa ei voi kumota. Jos haluat jatkaa, vahvista alla.',
	ConfirmDeleteActionDescription: 'Haluatko varmasti poistaa tämän toiminnon? Tätä ei voi kumota',
	ConfirmDeleteAutomationDescription: 'Haluatko varmasti poistaa tämän automaation? Tätä toimintoa ei voi kumota.',
	ConfirmDeleteScheduleDescription:
		'Poistamalla <strong>{scheduleName}</strong>-aikataulun se poistetaan aikataulustasi ja se saattaa muuttaa saatavilla olevaa verkkopalvelua. Tätä toimenpidettä ei voi peruuttaa.',
	ConfirmDraftResponseContinue: 'Jatka vastauksella',
	ConfirmDraftResponseDescription:
		'Jos suljet tämän sivun, vastauksesi säilyy luonnoksena. Voit palata ja jatkaa milloin tahansa.',
	ConfirmDraftResponseSubmitResponse: 'Lähetä vastaus',
	ConfirmDraftResponseTitle: 'Vastaustasi ei ole lähetetty',
	ConfirmIfUserIsClientDescription: `Täyttämäsi ilmoittautumislomake on tarkoitettu Palveluntarjoajille (eli terveystiimeille/organisaatioille).
 Jos tämä on virhe, voit valita "Jatka asiakkaana", niin me hoidamme asiakasportaalisi asetukset.`,
	ConfirmIfUserIsClientNoButton: 'Rekisteröidy Palveluntarjoajaksi',
	ConfirmIfUserIsClientTitle: 'Vaikuttaa siltä, että olet asiakas',
	ConfirmIfUserIsClientYesButton: 'Jatka asiakkaana',
	ConfirmKeepSeparate: 'Vahvista pitäminen erillään',
	ConfirmMerge: 'Vahvista yhdistäminen',
	ConfirmPassword: 'Vahvista salasana',
	ConfirmRevertClaim: 'Kyllä, palauta tila',
	ConfirmSignupAccessCode: 'Vahvistuskoodi',
	ConfirmSignupButtom: 'Vahvistaa',
	ConfirmSignupDescription: 'Anna sähköpostiosoitteesi ja vahvistuskoodi, jonka juuri lähetimme sinulle.',
	ConfirmSignupSubTitle: 'Tarkista roskapostikansio - jos sähköposti ei ole saapunut',
	ConfirmSignupSuccessSnackbar:
		'Hienoa, olemme vahvistaneet tilisi! Nyt voit kirjautua sisään sähköpostiosoitteellasi ja salasanallasi',
	ConfirmSignupTitle: 'Vahvista tili',
	ConfirmSignupUsername: 'Sähköposti',
	ConfirmSubscriptionUpdate: 'Vahvista tilaus {price} {isMonthly, select, true {kuukaudessa} other {vuodessa}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Kun asiakkaat on poistettu, et voi enää käyttää heidän tietojaan.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Poista {count, plural, one {# asiakas} other {# asiakkaat}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Kun yhteystiedot on poistettu, et voi enää käyttää heidän tietojaan.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Poista {count, plural, one {# yhteystieto} other {# yhteystietoja}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Tämä on pysyvä toimenpide. Kun tiimin jäsenet on poistettu, et voi enää käyttää heidän tietojaan.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'Poista {count, plural, one {# joukkueen jäsen} other {# joukkueen jäsentä}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Tämän muistiinpanon sulkeminen lopettaa kaikki käynnissä olevat transkriptiot. Haluatko varmasti jatkaa?',
	ConfirmationModalDeleteClientField:
		'Tämä on pysyvä toimenpide. Kun kenttä on poistettu, se ei ole enää käytettävissä jäljellä olevilla asiakkaillasi.',
	ConfirmationModalDeleteSectionMessage:
		'Kun kysymykset on poistettu, kaikki tämän osion kysymykset poistetaan. Tätä toimintoa ei voi kumota.',
	ConfirmationModalDeleteService:
		'Tämä on pysyvä toimenpide. Kun palvelu on poistettu, se ei ole enää käytettävissä työtilassasi.',
	ConfirmationModalDeleteServiceGroup:
		'Kokoelman poistaminen poistaa kaikki palvelut ryhmästä ja palaa palveluluetteloosi. Tätä toimintoa ei voi kumota.',
	ConfirmationModalDeleteTranscript: 'Haluatko varmasti poistaa transkription?',
	ConfirmationModalDescriptionDeleteClient: 'Kun asiakas on poistettu, et voi enää käyttää asiakastietoja.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Kun poistat käyttöoikeutesi, et voi enää tarkastella asiakastietoja.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Heidän profiiliaan ei poisteta, se vain poistetaan tämän asiakkaan suhteen.',
	ConfirmationModalDescriptionRemoveStaff: 'Haluatko varmasti poistaa tämän henkilön palveluntarjoajalta?',
	ConfirmationModalEndSession: 'Oletko varma, että haluat lopettaa istunnon?',
	ConfirmationModalTitle: 'Oletko varma?',
	Confirmed: 'Vahvistettu',
	ConflictTimezoneWarningMessage: 'Ristiriidat voivat johtua useista aikavyöhykkeistä',
	Connect: 'Yhdistä',
	ConnectExistingClientOrContact: 'Luo uusi asiakas/yhteyshenkilö',
	ConnectInboxGoogleDescription: 'Lisää Gmail-tili tai Google-ryhmäluettelo',
	ConnectInboxMicrosoftDescription: 'Lisää Outlook-, Office365- tai Exchange-tili',
	ConnectInboxModalDescription:
		'Yhdistä sovelluksesi saumattomasti lähettääksesi, vastaanottaaksesi ja seurataksesi kaikkea viestintääsi yhdessä keskitetyssä paikassa.',
	ConnectInboxModalExistingDescription:
		'Käytä olemassa olevaa yhteyttä yhdistettyjen sovellusten asetuksista virtaviivaistaaksesi määritysprosessia.',
	ConnectInboxModalExistingTitle: 'Olemassa oleva yhdistetty sovellus Carepatronissa',
	ConnectInboxModalTitle: 'Yhdistä postilaatikko',
	ConnectToStripe: 'Yhdistä Stripeen',
	ConnectZoom: 'Yhdistä Zoom',
	ConnectZoomModalDescription: 'Salli Carepatronin hallita tapaamisiasi koskevia videopuheluita.',
	ConnectedAppDisconnectedNotificationSubject: 'Olemme menettäneet yhteyden {account} -tiliin. Yhdistä uudelleen',
	ConnectedAppSyncDescription:
		'Hallitse yhdistettyjä sovelluksia luodaksesi tapahtumia kolmannen osapuolen kalentereihin suoraan Carepatronilta.',
	ConnectedApps: 'Yhdistetyt sovellukset',
	ConnectedAppsGMailDescription: 'Lisää Gmail-tilejä tai Google-ryhmien luettelo',
	ConnectedAppsGoogleCalendarDescription: 'Lisää kalenteritilejä tai Google-ryhmäluetteloita',
	ConnectedAppsGoogleDescription: 'Lisää Gmail-tilisi ja synkronoi Google-kalenterit',
	ConnectedAppsMicrosoftDescription: 'Lisää Outlook-, Office365- tai Exchange-tili',
	ConnectedCalendars: 'Yhdistetyt kalenterit',
	ConsentDocumentation: 'Lomakkeet ja sopimukset',
	ConsentDocumentationPublicTemplateError: 'Turvallisuussyistä voit valita malleja vain tiimiltäsi (ei julkisia).',
	ConstructionWorker: 'Rakennustyöntekijä',
	Consultant: 'Konsultti',
	Contact: 'Ota yhteyttä',
	ContactAccessTypeHelperText: 'Antaa perheen ylläpitäjien päivittää tietoja',
	ContactAccessTypeHelperTextMoreInfo: 'Tämä antaa sinun jakaa muistiinpanoja/asiakirjoja {clientFirstName}sta.',
	ContactAddressLabelBilling: 'Laskutus',
	ContactAddressLabelHome: 'Kotiin',
	ContactAddressLabelOthers: 'muut',
	ContactAddressLabelWork: 'Työ',
	ContactChangeConfirmation:
		'Laskun yhteyshenkilön muuttaminen poistaa kaikki <mark>{contactName}</mark>-n liittyvät rivi-kohdat',
	ContactDetails: 'Yhteystiedot',
	ContactEmailLabelOthers: 'muut',
	ContactEmailLabelPersonal: 'Henkilökohtainen',
	ContactEmailLabelSchool: 'Koulu',
	ContactEmailLabelWork: 'Työ',
	ContactInformation: 'Yhteystiedot',
	ContactInformationText: 'Yhteystiedot',
	ContactListCreateButton: 'Uusi kontakti',
	ContactName: 'Yhteyshenkilön nimi',
	ContactPhoneLabelHome: 'Kotiin',
	ContactPhoneLabelMobile: 'mobiili',
	ContactPhoneLabelSchool: 'Koulu',
	ContactPhoneLabelWork: 'Työ',
	ContactRelationship: 'Yhteyssuhde',
	ContactRelationshipFormAccessType: 'Myönnä pääsy jaettuun tietoon',
	ContactRelationshipGrantAccessInfo: 'Näin voit jakaa muistiinpanoja ',
	ContactSupport: 'Ota yhteyttä tukeen',
	Contacts: 'Yhteystiedot',
	ContainerIdNotSet: 'Säilön tunnusta ei ole asetettu',
	Contemporary: 'Nykyaikainen',
	Continue: 'Jatkaa',
	ContinueDictating: 'Jatka sanelua',
	ContinueEditing: 'Jatka muokkaamista',
	ContinueImport: 'Jatka tuontia',
	ContinueTranscription: 'Jatka transkriptiota',
	ContinueWithApple: 'Jatka Applella',
	ContinueWithGoogle: 'Jatka Googlella',
	Conversation: 'Keskustelu',
	Copay: 'Yhteismaksu',
	CopayOrCoinsurance: 'Yhteismaksu tai yhteisvakuutus',
	Copayment: 'Yhteismaksu',
	CopiedToClipboard: 'Kopioitu leikepöydälle',
	Copy: 'Kopioida',
	CopyAddressSuccessSnackbar: 'Kopioitu osoite leikepöydälle',
	CopyCode: 'Kopioi koodi',
	CopyCodeToClipboardSuccess: 'Koodi kopioitu leikepöydälle',
	CopyEmailAddressSuccessSnackbar: 'Kopioitu sähköpostiosoite leikepöydälle',
	CopyLink: 'Kopioi linkki',
	CopyLinkForCall: 'Jaa tämä puhelu kopioimalla tämä linkki:',
	CopyLinkSuccessSnackbar: 'Kopioitu linkki leikepöydälle',
	CopyMeetingLink: 'Kopioi kokouksen linkki',
	CopyPaymentLink: 'Kopioi maksulinkki',
	CopyPhoneNumberSuccessSnackbar: 'Puhelinnumero kopioitu leikepöydälle',
	CopyTemplateLink: 'Kopioi linkki malliin',
	CopyTemplateLinkSuccess: 'Kopioitu linkki leikepöydälle',
	CopyToClipboardError: 'Ei voitu kopioida leikepöydälle. Yritä uudelleen.',
	CopyToTeamTemplates: 'Kopioi tiimimalleihin',
	CopyToWorkspace: 'Kopioi työtilaan',
	Cosmetologist: 'Kosmetologi',
	Cost: 'Maksaa',
	CostErrorMessage: 'Kustannus vaaditaan',
	Counseling: 'Neuvonta',
	Counselor: 'Neuvonantaja',
	Counselors: 'Neuvonantajat',
	CountInvoicesAdded: '{count, plural, one {# Lasku lisätty} other {# Laskuja lisätty}}',
	CountNotesAdded: '{count, plural, one {# Huomautus lisätty} other {# Huomautuksia lisätty}}',
	CountSelected: '{count} valittua',
	CountTimes: '{count} kertaa',
	Country: 'Maa',
	Cousin: 'Serkku',
	CoverageType: 'Peittotyyppi',
	Covered: 'Peitetty',
	Create: 'Luoda',
	CreateANewClient: 'Luo uusi asiakas',
	CreateAccount: 'Luo tili',
	CreateAndSignNotes: 'Luo ja allekirjoita muistiinpano asiakkaiden kanssa',
	CreateAvailabilityScheduleFailure: 'Uuden saatavuusaikataulun luominen epäonnistui',
	CreateAvailabilityScheduleSuccess: 'Uuden saatavuusaikataulun luominen onnistui',
	CreateBillingItems: 'Luo laskutuskohteita',
	CreateCallFormButton: 'Aloita puhelu',
	CreateCallFormInviteOnly: 'Vain kutsu',
	CreateCallFormInviteOnlyMoreInfo:
		'Vain tähän puheluun kutsutut ihmiset voivat liittyä. Jos haluat jakaa tämän puhelun muiden kanssa, poista valinta ja kopioi/liitä linkki seuraavalle sivulle',
	CreateCallFormRecipients: 'Vastaanottajat',
	CreateCallFormRegion: 'Isännöintialue',
	CreateCallModalAddClientContactSelectorLabel: 'Asiakkaiden yhteystiedot',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Hae asiakkaan nimellä',
	CreateCallModalAddStaffSelectorLabel: 'Ryhmän jäsenet (valinnainen)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Hae henkilökunnan nimellä',
	CreateCallModalDescription:
		'Aloita puhelu ja kutsu henkilökunnan jäseniä ja/tai yhteyshenkilöitä. Vaihtoehtoisesti voit poistaa valinnan Yksityinen-ruudusta, jotta tämä puhelu voidaan jakaa kenelle tahansa Carepatronin kanssa.',
	CreateCallModalTitle: 'Aloita puhelu',
	CreateCallModalTitleLabel: 'Otsikko (valinnainen)',
	CreateCallNoPersonIdToolTip: 'Vain yhteyshenkilöt/asiakkaat, joilla on pääsy portaaliin, voivat liittyä puheluihin',
	CreateClaim: 'Luo vaatimus',
	CreateClaimCompletedMessage: 'Vaatimuksesi on luotu.',
	CreateClientModalTitle: 'Uusi asiakas',
	CreateContactModalTitle: 'Uusi kontakti',
	CreateContactRelationshipButton: 'Lisää suhde',
	CreateContactSelectorDefaultOption: '  Luo yhteystieto',
	CreateContactWithRelationshipFormAccessType: 'Myönnä pääsy jaettuun tietoon ',
	CreateDocumentDnDPrompt: 'Lataa tiedostoja vetämällä ja pudottamalla',
	CreateDocumentSizeLimit: 'Tiedostokohtainen kokoa raja {size}Mt. Yhteensä {total} tiedostoa.',
	CreateFreeAccount: 'Luo ilmainen tili',
	CreateInvoice: 'Luo lasku',
	CreateLink: 'Luo linkki',
	CreateNew: 'Luo uusi',
	CreateNewAppointment: 'Luo uusi tapaaminen',
	CreateNewClaim: 'Luo uusi vaatimus',
	CreateNewClaimForAClient: 'Luo uusi vaatimus asiakkaalle.',
	CreateNewClient: 'Luo uusi asiakas',
	CreateNewConnection: 'Uusi yhteys',
	CreateNewContact: 'Luo uusi yhteystieto',
	CreateNewField: 'Luo uusi kenttä',
	CreateNewLocation: 'Uusi sijainti',
	CreateNewService: 'Luo uusi palvelu',
	CreateNewServiceGroupFailure: 'Uuden kokoelman luominen epäonnistui',
	CreateNewServiceGroupMenu: 'Uusi kokoelma',
	CreateNewServiceGroupSuccess: 'Uuden kokoelman luominen onnistui',
	CreateNewServiceMenu: 'Uusi palvelu',
	CreateNewTeamMember: 'Luo uusi tiimin jäsen',
	CreateNewTemplate: 'Uusi malli',
	CreateNote: 'Luo muistiinpano',
	CreateSuperbillReceipt: 'Uusi superbill',
	CreateSuperbillReceiptSuccess: 'Superbill-kuitin luominen onnistui',
	CreateTemplateFolderSuccessMessage: 'Onnistuneesti luotu {folderTitle}',
	Created: 'Luotu',
	CreatedAt: 'Luotu {timestamp}',
	Credit: 'Luotto',
	CreditAdded: 'Luotto käytetty',
	CreditAdjustment: 'Luoton oikaisu',
	CreditAdjustmentReasonHelperText: 'Tämä on sisäinen huomautus, eikä se näy asiakkaallesi.',
	CreditAdjustmentReasonPlaceholder: 'Oikaisun syyn lisääminen voi auttaa laskutettavia tapahtumia tarkasteltaessa',
	CreditAmount: '{amount} KN',
	CreditBalance: 'Luottosaldo',
	CreditCard: 'Luottokortti',
	CreditCardExpire: 'Voimassa {exp_month}/{exp_year}',
	CreditCardNumber: 'Luottokortin numero',
	CreditDebitCard: 'Kortti',
	CreditIssued: 'Luotto myönnetty',
	CreditsUsed: 'Luotto käytetty',
	Crop: 'Rajaa',
	Currency: 'Valuutta',
	CurrentCredit: 'Nykyinen luotto',
	CurrentEventTime: 'Tapahtuman nykyinen aika',
	CurrentPlan: 'Nykyinen suunnitelma',
	Custom: 'Mukautettu',
	CustomRange: 'Mukautettu valikoima',
	CustomRate: 'Mukautettu hinta',
	CustomRecurrence: '<h1>Mukautettu toistuminen</h1>',
	CustomServiceAvailability: 'Palvelun saatavuus',
	CustomerBalance: 'Asiakkaan saldo',
	CustomerName: 'Asiakkaan nimi',
	CustomerNameIsRequired: 'Asiakkaan nimi vaaditaan',
	CustomerServiceRepresentative: 'Asiakaspalvelun edustaja',
	CustomiseAppointments: 'Mukauta tapaamisia',
	CustomiseBookingLink: 'Mukauta varausvaihtoehtoja',
	CustomiseBookingLinkServicesInfo: 'Asiakkaat voivat valita vain varattavissa olevia palveluita',
	CustomiseBookingLinkServicesLabel: 'Palvelut',
	CustomiseClientRecordsAndWorkspace: 'Mukauta asiakastietueitasi ja työtilaasi',
	CustomiseClientSettings: 'Mukauta asiakasasetuksia',
	Customize: 'Mukauta',
	CustomizeAppearance: 'Mukauta ulkonäköä',
	CustomizeAppearanceDesc:
		'Mukauta online-varauksesi ulkoasu vastaamaan brändiäsi ja optimoi, miten palvelusi näytetään asiakkaille.',
	CustomizeClientFields: 'Mukauta asiakaskenttiä',
	CustomizeInvoiceTemplate: 'Mukauta laskumallia',
	CustomizeInvoiceTemplateDescription: 'Luo vaivattomasti ammattimaisia laskuja, jotka kuvastavat brändiäsi.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX vaaditaan',
	Daily: 'Päivittäin',
	DanceTherapist: 'Tanssiterapeutti',
	DangerZone: 'Vaaravyöhyke',
	Dashboard: 'Kojelauta',
	Date: 'Päivämäärä',
	DateAndTime: 'Päivämäärä ',
	DateDue: 'Eräpäivä',
	DateErrorMessage: 'Päivämäärä vaaditaan',
	DateFormPrimaryText: 'Päivämäärä',
	DateFormSecondaryText: 'Valitse päivämäärävalitsimesta',
	DateIssued: 'Julkaisupäivämäärä',
	DateOfPayment: 'Maksupäivä',
	DateOfService: 'Palvelun päivämäärä',
	DateOverride: 'Päivämäärän ohitus',
	DateOverrideColor: 'Päivämäärän ohitusväri',
	DateOverrideInfo:
		'Päivämäärän ohituksen avulla harjoittajat voivat manuaalisesti säätää saatavuuttaan tiettyinä päivinä ohittamalla säännölliset aikataulut.',
	DateOverrideInfoBanner:
		'Näissä aikaväleissä voidaan varata vain tietyt palvelut tälle päivämäärän ohitukselle; muita online-varauksia ei sallita.',
	DateOverrides: 'Päivämäärä ohittaa',
	DatePickerFormPrimaryText: 'Päivämäärä',
	DatePickerFormSecondaryText: 'Valitse päivämäärä',
	DateRange: 'Ajanjakso',
	DateRangeFormPrimaryText: 'Ajanjakso',
	DateRangeFormSecondaryText: 'Valitse ajanjakso',
	DateReceived: 'Vastaanottopäivämäärä',
	DateSpecificHours: 'Päivämääräkohtaiset tunnit',
	DateSpecificHoursDescription:
		'Lisää päivämäärät, jolloin saatavuutesi muuttuu aikataulun mukaisista aukioloajoistasi tai tarjotaksesi palvelua tiettynä päivänä.',
	DateUploaded: 'Ladattu {date, date, medium}',
	Dates: 'Päivämäärät',
	Daughter: 'Tytär',
	Day: 'Päivä',
	DayPlural: '{count, plural, one {päivä} other {päivää}}',
	Days: 'päivää',
	DaysPlural: '{age, plural, one {# päivä} other {# päivää}}',
	DeFacto: 'De facto',
	Deactivated: 'Poistettu käytöstä',
	Debit: 'Veloittaa',
	DecreaseIndent: 'Pienennä sisennystä',
	Deductibles: 'Omavastuut',
	Default: 'Oletus',
	DefaultBillingProfile: 'Oletuslaskutusprofiili',
	DefaultDescription: 'Oletuskuvaus',
	DefaultEndOfLine: 'Ei enää kohteita',
	DefaultInPerson: 'Asiakasvaraukset',
	DefaultInvoiceTitle: 'Oletusotsikko',
	DefaultNotificationSubject: 'Olet saanut uuden ilmoituksen {notificationType}',
	DefaultPaymentMethod: 'Oletusmaksutapa',
	DefaultService: 'Oletuspalvelu',
	DefaultValue: 'Oletus',
	DefaultVideo: 'Asiakkaan videotapaamissähköposti',
	DefinedTemplateType: '{invoiceTemplate} malli',
	Delete: 'Poistaa',
	DeleteAccountButton: 'Poista tili',
	DeleteAccountDescription: 'Poista tilisi alustalta',
	DeleteAccountPanelInfoAlert:
		'Sinun on poistettava työtilasi ennen profiilisi poistamista. Jatka siirtymällä työtilaan ja valitsemalla Asetukset > Työtilan asetukset.',
	DeleteAccountTitle: 'Poista tili',
	DeleteAppointment: 'Poista tapaaminen',
	DeleteAppointmentDescription: 'Haluatko varmasti poistaa tämän tapaamisen? Voit palauttaa sen myöhemmin.',
	DeleteAvailabilityScheduleFailure: 'Saatavuusaikataulun poistaminen epäonnistui',
	DeleteAvailabilityScheduleSuccess: 'Saatavuusaikataulun poistaminen onnistui',
	DeleteBillable: 'Poista laskutettava',
	DeleteBillableConfirmationMessage: 'Haluatko varmasti poistaa tämän laskutettavan? Tätä toimintoa ei voi kumota.',
	DeleteBillingProfileConfirmationMessage: 'Tämä poistaa laskutusprofiilin pysyvästi.',
	DeleteCardConfirmation: 'Tämä on pysyvä toimenpide. Kun kortti on poistettu, et voi enää käyttää sitä.',
	DeleteCategory: 'Poista luokka (tämä ei ole pysyvä, ellei muutoksia ole tallennettu)',
	DeleteClientEventConfirmationDescription: 'Tämä poistetaan pysyvästi.',
	DeleteClients: 'Poista asiakkaat',
	DeleteCollection: 'Poista kokoelma',
	DeleteColumn: 'Poista sarake',
	DeleteConversationConfirmationDescription: 'Poista tämä keskustelu pysyvästi. Tätä toimintoa ei voi kumota.',
	DeleteConversationConfirmationTitle: 'Poista keskustelu pysyvästi',
	DeleteExternalEventDescription: 'Oletko varma, että haluat poistaa tämän ajanvarauksen?',
	DeleteFileConfirmationModalPrompt: 'Kun tiedosto on poistettu, et voi palauttaa sitä uudelleen.',
	DeleteFolder: 'Poista kansio',
	DeleteFolderConfirmationMessage:
		'Oletko varma, että haluat poistaa tämän kansion {name}? Kaikki tämän kansion sisältämät kohteet poistetaan myös. Voit palauttaa tämän myöhemmin.',
	DeleteForever: 'Poista ikuisesti',
	DeleteInsurancePayerConfirmationMessage:
		'{payer}n poistaminen poistaa sen vakuutusmaksajien luettelostasi. Tämä toimenpide on pysyvä eikä sitä voi peruuttaa.',
	DeleteInsurancePayerFailure: 'Vakuutuksen maksajan poistaminen epäonnistui',
	DeleteInsurancePolicyConfirmationMessage: 'Tämä poistaa vakuutuksen pysyvästi.',
	DeleteInvoiceConfirmationDescription:
		'Tätä toimintoa ei voi kumota. Se poistaa pysyvästi laskun ja kaikki siihen liittyvät maksut.',
	DeleteLocationConfirmation:
		'Sijainnin poistaminen on pysyvä toimenpide. Kun poistat sen, et voi enää käyttää sitä. Tätä toimintoa ei voi kumota.',
	DeletePayer: 'Poista maksaja',
	DeletePracticeWorkspace: 'Poista harjoitustyötila',
	DeletePracticeWorkspaceDescription: 'Poista tämä harjoitustyötila pysyvästi',
	DeletePracticeWorkspaceFailedSnackbar: 'Työtilan poistaminen epäonnistui',
	DeletePracticeWorkspaceModalCancelButton: 'Kyllä, peruuta tilaukseni',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Ennen kuin jatkat työtilan poistamista, sinun on ensin peruutettava tilauksesi.',
	DeletePracticeWorkspaceModalConfirmButton: 'Kyllä, poista työtila pysyvästi',
	DeletePracticeWorkspaceModalDescription:
		'{name} työtila poistetaan pysyvästi ja kaikki tiimijäsenet menettävät pääsyn siihen. Lataa kaikki tärkeät tiedot tai viestit, joita saatat tarvita ennen poistamista. Tätä toimenpidettä ei voi peruuttaa.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Tämä päätös tehtiin seuraavista syistä:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Syy',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Kerro meille, miksi haluat poistaa tilisi.',
	DeletePracticeWorkspaceModalTitle: 'Oletko varma?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Kaikkien tiimin jäsenten käyttöoikeudet on poistettu',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} on poistettu onnistuneesti',
	DeletePublicTemplateContent: 'Tämä poistaa vain julkisen mallin, ei tiimisi mallia.',
	DeleteRecurringAppointmentModalTitle: 'Poista toistuva tapaaminen',
	DeleteRecurringEventModalTitle: 'Poista toistuva kokous',
	DeleteRecurringReminderModalTitle: 'Poista toistuva muistutus',
	DeleteRecurringTaskModalTitle: 'Poista toistuva tehtävä',
	DeleteReminderConfirmation:
		'Tämä on pysyvä toimenpide. Kun muistutus on poistettu, et voi enää käyttää sitä. Vaikuttaa vain uusiin tapaamisiin',
	DeleteSection: 'Poista osio',
	DeleteSectionInfo:
		'Osion <strong>{section}</strong> poistaminen piilottaa kaikki sen sisällä olevat kentät. Toimintaa ei voi peruuttaa.',
	DeleteSectionWarning:
		'Ydintiedot eivät voi olla poistettavissa, ja ne siirretään olemassa olevaan osioon **{section}**.',
	DeleteServiceFailure: 'Palvelun poistaminen epäonnistui',
	DeleteServiceSuccess: 'Palvelun poistaminen onnistui',
	DeleteStaffScheduleOverrideDescription:
		'Tämän päivämääräkorvauksen poistaminen {value}sta poistaa sen aikatauluistasi ja se voi muuttaa verkko-palvelun saatavuuttasi. Tätä toimenpidettä ei voi peruuttaa.',
	DeleteSuperbillConfirmationDescription: 'Tätä toimintoa ei voi kumota. Se poistaa Superbill-kuitin pysyvästi.',
	DeleteSuperbillFailure: 'Superbill-kuitin poistaminen epäonnistui',
	DeleteSuperbillSuccess: 'Superbill-kuitin poistaminen onnistui',
	DeleteTaxRateConfirmationDescription: 'Haluatko varmasti poistaa tämän veroprosentin?',
	DeleteTemplateContent: 'Tätä toimintoa ei voi kumota',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} onnistuneesti poistettu',
	DeleteTemplateSuccessMessage: '{templateTitle} onnistuneesti poistettu',
	DeleteTemplateTitle: 'Haluatko varmasti poistaa tämän mallin?',
	DeleteTranscript: 'Poista transkriptio',
	DeleteWorkspace: 'Poista työtila',
	Deleted: 'Poistettu',
	DeletedBy: 'Poisti',
	DeletedContact: 'Yhteystieto poistettu',
	DeletedOn: 'Poistettu',
	DeletedStatusLabel: 'Poistettu tila',
	DeletedUserTooltip: 'Tämä asiakas on poistettu',
	DeliveryMethod: 'Toimitusmenetelmä',
	Demo: 'Demo',
	Denied: 'Kielletty',
	Dental: 'Hammaslääketiede',
	DentalAssistant: 'Hammaslääkärin avustaja',
	DentalHygienist: 'Hammashygienisti',
	Dentist: 'Hammaslääkäri',
	Dentists: 'Hammaslääkärit',
	Description: 'Kuvaus',
	DescriptionMustNotExceed: 'Kuvaus ei saa ylittää {max} merkkiä.',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other {  {staffName} kanssa }}',
	Details: 'Yksityiskohdat',
	Devices: 'Laitteet',
	Diagnosis: 'Diagnoosi',
	DiagnosisAndBillingItems: 'Diagnoosi ',
	DiagnosisCode: 'Diagnoosi koodi',
	DiagnosisCodeErrorMessage: 'Diagnoosikoodi vaaditaan',
	DiagnosisCodeSelectorPlaceholder: 'Etsi ja lisää ICD-10-diagnostiikkakoodeja',
	DiagnosisCodeSelectorTooltip:
		'Diagnoosikoodeja käytetään automatisoimaan superlaskujen kuitit vakuutuskorvausta varten',
	DiagnosticCodes: 'Diagnostiset koodit',
	Dictate: 'Sanella',
	DictatingIn: 'Sanelu sisään',
	Dictation: 'Sanelu',
	DidNotAttend: 'Ei osallistunut',
	DidNotComplete: 'Ei suoritettu',
	DidNotProviderEnoughValue: 'Ei tarjonnut tarpeeksi arvoa',
	DidntProvideEnoughValue: 'Ei tarjonnut tarpeeksi arvoa',
	DieteticsOrNutrition: 'Dietologia tai ravitsemus',
	Dietician: 'Ravitsemusterapeutti',
	Dieticians: 'Ravitsemusterapeutit',
	Dietitian: 'Ravitsemusterapeutti',
	DigitalSign: 'Allekirjoita tästä:',
	DigitalSignHelp: '(Klikkaa/paina alas piirtääksesi)',
	DirectDebit: 'Suoraveloitus',
	DirectTextLink: 'Suora tekstilinkki',
	Disable: 'Poista käytöstä',
	DisabledEmailInfo: 'Emme voi päivittää sähköpostiosoitettasi, koska emme hallinnoi tiliäsi',
	Discard: 'Hävitä',
	DiscardChanges: 'Hylkää muutokset',
	DiscardDrafts: 'Hylkää luonnokset',
	Disconnect: 'Katkaise yhteys',
	DisconnectAppConfirmation: 'Haluatko katkaista tämän sovelluksen yhteyden?',
	DisconnectAppConfirmationDescription: 'Oletko varma, että haluat katkaista tämän sovelluksen yhteyden?',
	DisconnectAppConfirmationTitle: 'Irrita sovellus',
	Discount: 'Alennus',
	DisplayCalendar: 'Näytä Carepatronissa',
	DisplayName: 'Näyttönimi',
	DisplayedToClients: 'Näytetään asiakkaille',
	DiversionalTherapist: 'Kiertoterapeutti',
	DoItLater: 'Tee se myöhemmin',
	DoNotImport: 'Älä tuo maahan',
	DoNotSend: 'Älä lähetä',
	DoThisLater: 'Tee tämä myöhemmin.',
	DoYouWantToEndSession: 'Haluatko jatkaa vai lopettaa istunnon nyt?',
	Doctor: 'Lääkäri',
	Doctors: 'Lääkärit',
	DoesNotRepeat: 'Ei toistu',
	DoesntWorkWellWithExistingTools: 'Ei toimi hyvin olemassa olevien työkalujemme tai työnkulkujemme kanssa',
	DogWalker: 'Koiran kulkija',
	Done: 'Valmis',
	DontAllowClientsToCancel: 'Älä anna asiakkaiden peruuttaa',
	DontHaveAccount: 'Eikö sinulla ole tiliä?',
	DontSend: 'Älä lähetä',
	Double: 'Kaksinkertainen',
	DowngradeTo: 'Alenna {plan}-suunnitelmaan',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Valitettavasti et voi alentaa suunnitelmaasi, koska sinulla on liian monta tiimin jäsentä. Poista joitakin palveluntarjoajaltasi ja yritä uudelleen.',
	Download: 'Lataa',
	DownloadAsPdf: 'Lataa PDF-muodossa',
	DownloadERA: 'Lataa ERA',
	DownloadPDF: 'Lataa PDF',
	DownloadTemplateFileName: 'Carepatron Vaihtopohja.csv',
	DownloadTemplateTileDescription: 'Käytä laskentataulukkopohjaamme asiakkaidesi järjestämiseen ja lataamiseen.',
	DownloadTemplateTileLabel: 'Lataa malli',
	Downloads: '{number, plural, one {<span>#</span> Lataus} other {<span>#</span> Latausta}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Luonnos',
	DraftResponses: 'Vastausluonnos',
	DraftSaved: 'Tallennetut muutokset',
	DragAndDrop: 'vedä ja pudota',
	DragDropText: 'Vedä ja pudota terveysasiakirjoja',
	DragToMove: 'Siirrä vetämällä',
	DragToMoveOrActivate: 'Siirrä tai aktivoi vetämällä',
	DramaTherapist: 'Draamaterapeutti',
	DropdownFormFieldPlaceHolder: 'Valitse vaihtoehdot luettelosta',
	DropdownFormPrimaryText: 'Pudotusvalikko',
	DropdownFormSecondaryText: 'Valitse vaihtoehto luettelosta',
	DropdownTextFieldError: 'Pudotusvalikon teksti ei voi olla tyhjä',
	DropdownTextFieldPlaceholder: 'Lisää avattava vaihtoehto',
	Due: 'Eräpäivä',
	DueDate: 'Eräpäivä',
	Duplicate: 'Kopioi',
	DuplicateAvailabilityScheduleFailure: 'Saatavuusaikataulun kopioiminen epäonnistui',
	DuplicateAvailabilityScheduleSuccess: 'Onnistuneesti kopioitu {name} -aikataulu',
	DuplicateClientBannerAction: 'Arvostelu',
	DuplicateClientBannerDescription:
		'Päällekkäisten asiakastietueiden yhdistäminen yhdistää ne yhdeksi ja säilyttää kaikki ainutlaatuiset asiakastiedot.',
	DuplicateClientBannerTitle: '{count} Kaksoiskappaletta löytyi',
	DuplicateColumn: 'Kopioi sarake',
	DuplicateContactFieldSettingErrorSnackbar: 'Osion nimiä ei voi olla päällekkäisiä',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Kenttien nimissä ei voi olla päällekkäisiä nimiä',
	DuplicateEmailError: 'Kopioi sähköposti',
	DuplicateHeadingName: 'Osio {name} on jo olemassa',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Lasku, jolla on sama "laskunumero", on jo olemassa.',
	DuplicateRecords: 'Päällekkäiset tietueet',
	DuplicateRecordsMinimumError: 'Vähintään 2 tietuetta on valittava',
	DuplicateRecordsRequired: 'Valitse vähintään yksi tietue erotettavaksi',
	DuplicateServiceFailure: 'Virhe: <strong>{title}</strong>:n kopiointi epäonnistui',
	DuplicateServiceSuccess: 'Onnistuneesti kopioitu <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Kansion kopiointi onnistui',
	DuplicateTemplateSuccess: 'Mallin kopiointi onnistui',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'DX koodi',
	DxCodeSelectPlaceholder: 'Etsi ja lisää ICD-10-koodeista',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Ei mitään',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>sisältää muutoksia</i>} other {}}',
	EarnReferralCredit: 'Tuloa ${creditAmount}',
	Economist: 'Ekonomisti',
	Edit: 'Muokata',
	EditArrangements: 'Muokkaa järjestelyjä',
	EditBillTo: 'Muokkaa laskutusta',
	EditClient: 'Muokkaa asiakasta',
	EditClientFileModalDescription: 'Muokkaa tämän tiedoston käyttöoikeuksia valitsemalla vaihtoehdot Katseluruuduissa',
	EditClientFileModalTitle: 'Muokkaa tiedostoa',
	EditClientNoteModalDescription:
		'Muokkaa muistiinpanon sisältöä. Voit muuttaa, kuka näkee muistiinpanon Katseluoikeus-osiossa.',
	EditClientNoteModalTitle: 'Muokkaa muistiinpanoa',
	EditConnectedAppButton: 'Muokata',
	EditConnections: 'Muokkaa yhteyksiä{account, select, null { } undefined { } other { for {account}}}',
	EditContactDetails: 'Muokkaa yhteystietoja',
	EditContactFormIsClientLabel: 'Muunna asiakkaaksi',
	EditContactIsClientCheckboxWarning: 'Yhteyshenkilön muuntamista asiakkaaksi ei voi kumota',
	EditContactIsClientWanringModal:
		'Tämän yhteystiedon muuntamista asiakkaaksi ei voi kumota. Kaikki suhteet säilyvät kuitenkin edelleen, ja sinulla on nyt pääsy heidän muistiinpanoihinsa, tiedostoihinsa ja muihin asiakirjoihin.',
	EditContactRelationship: 'Muokkaa kontaktisuhdetta',
	EditDetails: 'Muokkaa tietoja',
	EditFileModalTitle: 'Muokkaa tiedostoa {name}lle',
	EditFolder: 'Muokkaa kansiota',
	EditFolderDescription: 'Nimeä kansio uudelleen nimellä...',
	EditInvoice: 'Muokkaa laskua',
	EditInvoiceDetails: 'Muokkaa laskun tietoja',
	EditLink: 'Muokkaa linkkiä',
	EditLocation: 'Muokkaa sijaintia',
	EditLocationFailure: 'Sijainnin päivittäminen epäonnistui',
	EditLocationSucess: 'Sijainnin päivitys onnistui',
	EditPaymentDetails: 'Muokkaa maksutietoja',
	EditPaymentMethod: 'Muokkaa maksutapaa',
	EditPersonalDetails: 'Muokkaa henkilötietoja',
	EditPractitioner: 'Muokkaa harjoittajaa',
	EditProvider: 'Muokkaa toimittajaa',
	EditProviderDetails: 'Muokkaa palveluntarjoajan tietoja',
	EditRecurrence: 'Muokkaa toistuvuutta',
	EditRecurringAppointmentModalTitle: 'Muokkaa toistuvaa tapaamista',
	EditRecurringEventModalTitle: 'Muokkaa toistuvaa kokousta',
	EditRecurringReminderModalTitle: 'Muokkaa toistuvaa muistutusta',
	EditRecurringTaskModalTitle: 'Muokkaa toistuvaa tehtävää',
	EditRelationshipModalTitle: 'Muokkaa suhdetta',
	EditService: 'Muokkaa palvelua',
	EditServiceFailure: 'Uuden palvelun päivittäminen epäonnistui',
	EditServiceGroup: 'Muokkaa kokoelmaa',
	EditServiceGroupFailure: 'Kokoelman päivittäminen epäonnistui',
	EditServiceGroupSuccess: 'Kokoelman päivitys onnistui',
	EditServiceSuccess: 'Uusi palvelu päivitetty onnistuneesti',
	EditStaffDetails: 'Muokkaa henkilöstön tietoja',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Sähköpostiosoitetta ei voi päivittää. Luo uusi tiimin jäsen uudella sähköpostiosoitteella.',
	EditSubscriptionBilledQuantity: 'Laskutettu määrä',
	EditSubscriptionBilledQuantityValue: '{billedUsers} tiimijäseniä',
	EditSubscriptionLimitedTimeOffer: 'Rajallinen tarjous! 50% alennus 6 kuukaudeksi.',
	EditSubscriptionUpgradeAdjustTeamBanner: 'Tilauksesi hinta muuttuu, kun ryhmään lisätään tai poistetaan jäseniä.',
	EditSubscriptionUpgradeContent:
		'Tilisi päivitetään välittömästi uuteen suunnitelmaan ja laskutusjaksoon. Kaikki hintojen muutokset veloitetaan automaattisesti tallennetusta maksutavastasi tai hyvitetään tilillesi.',
	EditSubscriptionUpgradePlanTitle: 'Päivitä tilaussuunnitelma',
	EditSuperbillReceipt: 'Muokkaa superlaskua',
	EditTags: 'Muokkaa tunnisteita',
	EditTemplate: 'Muokkaa mallia',
	EditTemplateFolderSuccessMessage: 'Mallikansion muokkaus onnistui',
	EditValue: 'Muokkaa {value}',
	Edited: 'Muokattu',
	Editor: 'Toimittaja',
	EditorAlertDescription:
		'On havaittu muoto, jota ei tueta. Lataa sovellus uudelleen tai ota yhteyttä tukitiimiimme.',
	EditorAlertTitle: 'Meillä on ongelmia tämän sisällön näyttämisessä',
	EditorPlaceholder:
		'Aloita kirjoittaminen, valitse malli tai lisää peruslohkoja saadaksesi vastauksia asiakkailtasi.',
	EditorTemplatePlaceholder: 'Aloita kirjoittaminen tai lisää komponentteja mallin luomiseksi',
	EditorTemplateWithSlashCommandPlaceholder:
		'Aloita kirjoittaminen tai lisää peruslohkoja asiakkaan vastausten tallentamiseksi. Käytä vinoviiva komentoja (/) nopeisiin toimiin.',
	EditorWithSlashCommandPlaceholder:
		'Aloita kirjoittaminen, valitse malli tai lisää peruslohkoja asiakkaan vastausten tallentamiseksi. Käytä vinoviivakomentoja ( / ) nopeisiin toimiin.',
	EffectiveStartEndDate: 'Voimassa alkamis- ja päättymispäivä',
	ElectricalEngineer: 'Sähköinsinööri',
	Electronic: 'Elektroninen',
	ElectronicSignature: 'Sähköinen allekirjoitus',
	ElementarySchoolTeacher: 'Peruskoulun opettaja',
	Eligibility: 'Kelpoisuus',
	Email: 'Sähköposti',
	EmailAlreadyExists: 'Sähköpostiosoite on jo olemassa',
	EmailAndSms: 'Sähköposti ',
	EmailBody: 'Sähköpostin runko',
	EmailContainsIgnoredDescription:
		'Seuraava sähköposti sisältää lähettäjän sähköpostin, jota ei tällä hetkellä huomioida. Haluatko jatkaa?',
	EmailInviteToPortalBody: `Hei {contactName},
Seuraa tätä linkkiä kirjautuaksesi turvalliseen asiakasportaaliisi ja hallitsemaan hoitoasi helposti.

Ystävällisin terveisin,

{providerName}`,
	EmailInviteToPortalSubject: 'Tervetuloa {providerName}iin',
	EmailInvoice: 'Sähköposti lasku',
	EmailInvoiceOverdueBody: `Hei {contactName}
Laskusi {invoiceNumber} on erääntynyt.
Maksa laskusi verkossa alla olevan linkin avulla.

Jos sinulla on kysyttävää, ota yhteyttä.

Kiitos,
{providerName}`,
	EmailInvoicePaidBody: `Hei {contactName}
Laskusi {invoiceNumber} on maksettu.
Voit katsella ja ladata laskun kopion alla olevasta linkistä.

Jos sinulla on kysyttävää, ota rohkeasti yhteyttä.

Kiitos,
{providerName}`,
	EmailInvoiceProcessingBody: `Hei {contactName}
Laskusi {invoiceNumber} on valmis.
Seuraa alla olevaa linkkiä laskun tarkastelemiseksi.

Jos sinulla on kysyttävää, ota meihin yhteyttä.

Kiitos,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hei {contactName}
Laskusi {invoiceNumber} on valmis ja se on maksettava {dueDate} mennessä. 
Voit katsoa ja maksaa laskun verkossa seuraavan linkin kautta.

Jos sinulla on kysyttävää, ota yhteyttä.

Kiitos,
{providerName}`,
	EmailInvoiceVoidBody: `Hei {contactName}
Laskusi {invoiceNumber} on mitätöity.
Voit katsoa laskun klikkaamalla alla olevaa linkkiä.

Jos sinulla on kysyttävää, ota yhteyttä.

Kiitos,
{providerName}`,
	EmailNotFound: 'Sähköpostia ei löydy',
	EmailNotVerifiedErrorCodeSnackbar: 'Toimintoa ei voi suorittaa. Sinun on vahvistettava sähköpostiosoitteesi.',
	EmailNotVerifiedTitle: 'Sähköpostiasi ei ole vahvistettu. Jotkut ominaisuudet ovat rajoitettuja.',
	EmailSendClientIntakeBody: `Hei {contactName},
{providerName} haluaisi sinun antavan joitakin tietoja ja tarkastavan tärkeitä asiakirjoja. Seuraa alla olevaa linkkiä aloittaaksesi.

Ystävällisin terveisin,

{providerName}`,
	EmailSendClientIntakeSubject: 'Tervetuloa {providerName}',
	EmailSuperbillReceipt: 'Sähköposti superlasku',
	EmailSuperbillReceiptBody: `Hei {contactName},
{providerName} on lähettänyt sinulle kopion korvauskuitin {date}.

Voit ladata ja lähettää tämän suoraan vakuutusyhtiösi.`,
	EmailSuperbillReceiptFailure: 'Superbill-kuitin lähettäminen epäonnistui',
	EmailSuperbillReceiptSubject: '{providerName} on lähettänyt korvauskuitin',
	EmailSuperbillReceiptSuccess: 'Superbill-kuitin lähetys onnistui',
	EmailVerificationDescription: '<span>Vahvistamme</span> tiliäsi nyt',
	EmailVerificationNotification: 'Vahvistusviesti on lähetetty osoitteeseen {email}',
	EmailVerificationSuccess: 'Sähköpostiosoitteesi on muutettu onnistuneesti osoitteeksi {email}',
	Emails: 'Sähköpostit',
	EmergencyContact: 'Hätäyhteys',
	EmployeesIdentificationNumber: 'Työntekijöiden tunnusnumero',
	EmploymentStatus: 'Työllisyystilanne',
	EmptyAgendaViewDescription: 'Ei näytettäviä tapahtumia.<mark> Varaa tapaaminen nyt</mark>',
	EmptyBin: 'Tyhjä roskakori',
	EmptyBinConfirmationDescription:
		'Tyhjä roskakori poistaa kaikki **{total} keskustelua** Poistetut-kansiosta. Tätä toimintoa ei voi peruuttaa.',
	EmptyBinConfirmationTitle: 'Poista keskustelut pysyvästi',
	EmptyTrash: 'Tyhjennä roskakori',
	Enable: 'Ota käyttöön',
	EnableCustomServiceAvailability: 'Ota palvelun saatavuus käyttöön',
	EnableCustomServiceAvailabilityDescription: 'Esim. Alkuaikoja voi varata vain arkisin klo 9-10',
	EndCall: 'Lopeta puhelu',
	EndCallConfirmationForCreator: 'Päätät tämän kaikille, koska olet puhelun aloittaja.',
	EndCallConfirmationHasActiveAttendees:
		'Olet lopettamassa puhelua, mutta asiakas(t) on jo liittynyt. Haluatko sinäkin liittyä?',
	EndCallForAll: 'Lopeta puhelu kaikille',
	EndDate: 'Päättymispäivämäärä',
	EndDictation: 'Lopeta sanelu',
	EndOfLine: 'Ei enää tapaamisia',
	EndSession: 'Lopeta istunto',
	EndTranscription: 'Lopeta transkriptio',
	Ends: 'Loppu',
	EndsOnDate: 'Päättyy {date}',
	Enrol: 'Rekisteröidy',
	EnrollmentRejectedSubject: 'Rekisteröintisi {payerName}:lle on hylätty',
	Enrolment: 'Saanti',
	Enrolments: 'Ilmoittautumiset',
	EnrolmentsDescription: 'Näytä ja hallitse palveluntarjoajien rekisteröintejä maksajan kanssa.',
	EnterAName: 'Anna nimi...',
	EnterFieldLabel: 'Anna kentän tunniste...',
	EnterPaymentDetailsDescription: 'Tilaushintaasi muokataan automaattisesti, kun lisäät tai poistat käyttäjiä.',
	EnterSectionName: 'Anna osion nimi...',
	EnterSubscriptionPaymentDetails: 'Anna maksutiedot',
	EnvironmentalScientist: 'Ympäristötutkija',
	Epidemiologist: 'Epidemiologi',
	Eraser: 'Pyyhekumi',
	Error: 'Virhe',
	ErrorBoundaryAction: 'Lataa sivu uudelleen',
	ErrorBoundaryDescription: 'Päivitä sivu ja yritä uudelleen.',
	ErrorBoundaryTitle: 'Oho! Jotain meni pieleen',
	ErrorCallNotFound: 'Puhelua ei löydy. Se voi olla vanhentunut tai tekijä on lopettanut sen.',
	ErrorCannotAccessCallUninvitedCode: 'Valitettavasti sinua ei ilmeisesti ole kutsuttu tähän puheluun.',
	ErrorFileUploadCustomMaxFileCount: 'Et voi ladata kerralla enempää kuin {count} tiedostoa.',
	ErrorFileUploadCustomMaxFileSize: 'Tiedoston koko ei voi ylittää {mb} Mt',
	ErrorFileUploadInvalidFileType:
		'Virheellinen tiedostotyyppi, joka voi sisältää mahdollisia viruksia ja haitallisia ohjelmistoja',
	ErrorFileUploadMaxFileCount: 'Ei voi ladata enempää kuin 150 tiedostoa kerralla',
	ErrorFileUploadMaxFileSize: 'Tiedoston koko ei saa ylittää 100 Mt',
	ErrorFileUploadNoFileSelected: 'Valitse ladattavat tiedostot',
	ErrorInvalidNationalProviderId: 'Annettu kansallinen palveluntarjoajan tunnus ei ole kelvollinen',
	ErrorInvalidPayerId: 'Annettu maksajan tunnus ei ole kelvollinen',
	ErrorInvalidTaxNumber: 'Annettu veronumero ei ole kelvollinen',
	ErrorInviteExistingProviderStaffCode: 'Tämä käyttäjä on jo työtilassa.',
	ErrorInviteStaffExistingUser: 'Valitettavasti lisäämäsi käyttäjä näyttää jo olevan järjestelmässämme.',
	ErrorOnlySingleCallAllowed:
		'Voit soittaa vain yhden puhelun kerrallaan. Lopeta nykyinen puhelu aloittaaksesi uuden.',
	ErrorPayerNotFound: 'Maksajaa ei löydy',
	ErrorProfilePhotoMaxFileSize: 'Lataus epäonnistui! Tiedoston kokorajoitus saavutettu - 5 Mt',
	ErrorRegisteredExistingUser: 'Anteeksi, olet ilmeisesti jo rekisteröitynyt.',
	ErrorUserSignInIncorrectCredentials: 'Virheellinen sähköpostiosoite tai salasana. Yritä uudelleen.',
	ErrorUserSigninGeneric: 'Jotain meni pieleen.',
	ErrorUserSigninUserNotConfirmed:
		'Valitettavasti sinun on vahvistettava tilisi ennen kirjautumista. Tarkista ohjeet postilaatikostasi.',
	Errors: 'Virheet',
	EssentialPlanInclusionFive: 'Mallin tuonti',
	EssentialPlanInclusionFour: '5 Gt tallennustilaa',
	EssentialPlanInclusionHeader: 'Kaikki ilmaiseksi  ',
	EssentialPlanInclusionOne: 'Automaattiset ja mukautetut muistutukset',
	EssentialPlanInclusionSix: 'Ensisijainen tuki',
	EssentialPlanInclusionThree: 'Videokeskustelu',
	EssentialPlanInclusionTwo: '2-suuntainen kalenterin synkronointi',
	EssentialSubscriptionPlanSubtitle: 'Yksinkertaista harjoitteluasi olennaisella',
	EssentialSubscriptionPlanTitle: 'Olennaista',
	Esthetician: 'Esteetikko',
	Estheticians: 'Esteetikot',
	EstimatedArrivalDate: 'Arvioitu saapuminen {numberOfDaysFromNow}',
	Ethnicity: 'Etnisyys',
	Europe: 'Euroopassa',
	EventColor: 'Kokouksen väri',
	EventName: 'Tapahtuman nimi',
	EventType: 'Tapahtuman tyyppi',
	Every: 'Jokainen',
	Every2Weeks: '2 viikon välein',
	EveryoneInWorkspace: 'Kaikki työtilassa',
	ExercisePhysiologist: 'Liikuntafysiologi',
	Existing: 'Olemassa oleva',
	ExistingClients: 'Olemassa olevat asiakkaat',
	ExistingFolders: 'Olemassa olevat kansiot',
	ExpiredPromotionCode: 'Tarjouskoodi on vanhentunut',
	ExpiredReferralDescription: 'Suositus on vanhentunut',
	ExpiredVerificationLink: 'Vanhentunut vahvistuslinkki',
	ExpiredVerificationLinkDescription: `Olemme pahoillamme, mutta napsauttamasi vahvistuslinkki on vanhentunut. Näin voi käydä, jos olet odottanut yli 24 tuntia ennen kuin napsautit linkkiä tai jos olet jo käyttänyt linkkiä sähköpostiosoitteesi vahvistamiseen.

 Pyydä uusi vahvistuslinkki sähköpostiosoitteesi vahvistamiseksi.`,
	ExpiryDateRequired: 'Viimeinen voimassaolopäivä vaaditaan',
	ExploreFeature: 'Mitä haluaisit tutkia ensin?',
	ExploreOptions: 'Valitse yksi tai useampi vaihtoehto tutkittavaksi...',
	Export: 'Viedä',
	ExportAppointments: 'Vie tapaamiset',
	ExportClaims: 'Vientivaatimukset',
	ExportClaimsFilename: `Vaa'annukset {fromDate}-{toDate}.csv`,
	ExportClientsDownloadFailureSnackbarDescription: 'Tiedostoasi ei voitu ladata virheen vuoksi.',
	ExportClientsDownloadFailureSnackbarTitle: 'Lataus epäonnistui',
	ExportClientsFailureSnackbarDescription: 'Tiedostoasi ei voitu viedä onnistuneesti virheen vuoksi.',
	ExportClientsFailureSnackbarTitle: 'Vienti epäonnistui',
	ExportClientsModalDescription: `Tämä tietojen vientiprosessi voi kestää muutaman minuutin vietävän tiedon määrästä riippuen. Saat linkin sisältävän sähköposti-ilmoituksen, kun se on valmis ladattavaksi.

 Haluatko jatkaa asiakastietojen vientiä?`,
	ExportClientsModalTitle: 'Vie asiakastiedot',
	ExportCms1500: 'Vie CMS1500',
	ExportContactFailedNotificationSubject: 'Tietojen vienti epäonnistui',
	ExportFailed: 'Vienti epäonnistui',
	ExportGuide: 'Vientiopas',
	ExportInvoiceFileName: 'Transaktiot {fromDate}-{toDate}.csv',
	ExportPayments: 'Vientimaksut',
	ExportPaymentsFilename: 'Maksutiedot {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Asiakirjasi on valmis ladattavaksi.',
	ExportPrintWaitMessage: 'Asiakirjan valmistelu. Odota...',
	ExportTextOnly: 'Vie vain tekstiä',
	ExportTransactions: 'Vientitapahtumat',
	Exporting: 'Vientiä',
	ExportingData: 'Viedään tietoja',
	ExtendedFamilyMember: 'Laajennettu perheenjäsen',
	External: 'Ulkoinen',
	ExternalEventInfoBanner: 'Tämä tapaaminen on synkronoidusta kalenterista ja siinä saattaa puuttua tietoja.',
	ExtraLarge: 'Erittäin suuri',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Epäonnistui',
	FailedToJoinTheMeeting: 'Kokoukseen liittyminen epäonnistui.',
	FallbackPageDescription: `Näyttää siltä, että tätä sivua ei ole olemassa. Saatat joutua {refreshButton} päivittämään tämän sivun saadaksesi uusimmat muutokset.
Muussa tapauksessa ota yhteyttä Carepatronin tukeen.`,
	FallbackPageDescriptionUpdateButton: 'päivitä',
	FallbackPageTitle: 'Oho...',
	FamilyPlanningService: 'Perhesuunnittelupalvelu',
	FashionDesigner: 'Muotisuunnittelija',
	FastTrackInvoicingAndBilling: 'Seuraa laskutustasi ja laskutustasi nopeasti',
	Father: 'Isä',
	FatherInLaw: 'Anoppi',
	Favorite: 'Suosikkini',
	FeatureBannerCalendarTile1ActionLabel: 'Online-varaus • 2 min',
	FeatureBannerCalendarTile1Description:
		'Lähetä vain sähköpostia, tekstiviesti tai lisää saatavuus verkkosivustollesi',
	FeatureBannerCalendarTile1Title: 'Anna asiakkaillesi mahdollisuus tehdä varaus verkossa',
	FeatureBannerCalendarTile2ActionLabel: 'Automatisoi muistutukset • 2 min',
	FeatureBannerCalendarTile2Description: 'Lisää asiakkaiden läsnäoloa automaattisilla muistutuksilla',
	FeatureBannerCalendarTile2Title: `Vähennä ei-show'ta`,
	FeatureBannerCalendarTile3Title: 'Aikataulutus ja työnkulku',
	FeatureBannerCalendarTitle: 'Tee aikataulutuksesta helppoa',
	FeatureBannerCallsTile1ActionLabel: 'Aloita etäterveyspuhelu',
	FeatureBannerCallsTile1Description: 'Asiakkaan pääsy pelkällä linkillä. Ei kirjautumisia, salasanoja tai vaivaa',
	FeatureBannerCallsTile1Title: 'Aloita videopuhelu mistä tahansa',
	FeatureBannerCallsTile2ActionLabel: 'Yhdistä sovellukset • 4 min',
	FeatureBannerCallsTile2Description: 'Yhdistä saumattomasti muut ensisijaiset etäterveyspalvelujen tarjoajat',
	FeatureBannerCallsTile2Title: 'Yhdistä etäterveyssovelluksesi',
	FeatureBannerCallsTile3Title: 'Puhelut',
	FeatureBannerCallsTitle: 'Ota yhteyttä asiakkaisiin – missä ja milloin tahansa',
	FeatureBannerClientsTile1ActionLabel: 'Tuo nyt • 2 min',
	FeatureBannerClientsTile1Description: 'Aloita nopeasti automaattisen asiakastuontityökalumme avulla',
	FeatureBannerClientsTile1Title: 'Onko sinulla paljon asiakkaita?',
	FeatureBannerClientsTile2ActionLabel: 'Mukauta saantia • 2 min',
	FeatureBannerClientsTile2Description: 'Poista vastaanottopaperit ja paranna asiakaskokemusta',
	FeatureBannerClientsTile2Title: 'Mene paperittomaksi',
	FeatureBannerClientsTile3Title: 'Asiakasportaali',
	FeatureBannerClientsTitle: 'Kaikki alkaa asiakkaistasi',
	FeatureBannerHeader: 'Yhteisön puolesta, yhteisön puolesta!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatisoi maksut • 2 min',
	FeatureBannerInvoicesTile1Description: 'Vältä kiusallisia keskusteluja automaattisilla maksuilla',
	FeatureBannerInvoicesTile1Title: 'Saat maksun 2x nopeammin',
	FeatureBannerInvoicesTile2ActionLabel: 'Seuraa kassavirtaa • 2 min',
	FeatureBannerInvoicesTile2Description: 'Vähennä maksamattomia laskuja ja pidä tulojasi silmällä',
	FeatureBannerInvoicesTile2Title: 'Seuraa tulojasi kivuttomasti',
	FeatureBannerInvoicesTile3Title: 'Laskutus ja maksut',
	FeatureBannerInvoicesTitle: 'Yksi huolenaihe vähemmän',
	FeatureBannerSubheader: 'Tiimimme ja yhteisömme tekemät Carepatron-mallit. Kokeile uusia resursseja tai jaa omasi!',
	FeatureBannerTeamTile1ActionLabel: 'Kutsu nyt',
	FeatureBannerTeamTile1Description: 'Kutsu tiimin jäseniä tilillesi ja tee yhteistyöstä helppoa',
	FeatureBannerTeamTile1Title: 'Tuo tiimisi yhteen',
	FeatureBannerTeamTile2ActionLabel: 'Sarjan saatavuus • 2 min',
	FeatureBannerTeamTile2Description: 'Hallinnoi tiimisi saatavuutta välttääksesi kaksoisvaraukset',
	FeatureBannerTeamTile2Title: 'Aseta saatavuus',
	FeatureBannerTeamTile3ActionLabel: 'Määritä käyttöoikeudet • 2 min',
	FeatureBannerTeamTile3Description:
		'Hallitse arkaluonteisten tietojen ja työkalujen käyttöä vaatimustenmukaisuuden varmistamiseksi',
	FeatureBannerTeamTile3Title: 'Mukauta käyttöoikeuksia ja käyttöoikeuksia',
	FeatureBannerTeamTitle: 'Mitään suurta ei saavuteta yksin',
	FeatureBannerTemplatesTile1ActionLabel: 'Tutustu kirjastoon • 2 min',
	FeatureBannerTemplatesTile1Description: 'Valitse hämmästyttävästä mukautettavien resurssien kirjastosta ',
	FeatureBannerTemplatesTile1Title: 'Vähennä työmäärääsi',
	FeatureBannerTemplatesTile2ActionLabel: 'Lähetä nyt • 2 min',
	FeatureBannerTemplatesTile2Description: 'Lähetä kauniita malleja asiakkaille valmiiksi',
	FeatureBannerTemplatesTile2Title: 'Tee dokumentoinnista hauskaa',
	FeatureBannerTemplatesTile3Title: 'Mallit',
	FeatureBannerTemplatesTitle: 'Mallit mitä tahansa',
	FeatureLimitBannerDescription:
		'Päivitä nyt, jotta voit jatkaa {featureName}in luomista ja hallintaa keskeytyksettä ja hyödyntää Carepatronin kaikki ominaisuudet!',
	FeatureLimitBannerTitle: 'Olet {percentage}% matkalla kohti {featureName}-rajaa.',
	FeatureRequiresUpgrade: 'Tämä ominaisuus vaatii päivityksen',
	Fee: 'Maksu',
	Female: 'Naaras',
	FieldLabelTooltip: '{isHidden, select, true {Näytä} other {Piilota}} kentän otsikko',
	FieldName: 'Kentän nimi',
	FieldOptionsFirstPart: 'Ensimmäinen sana',
	FieldOptionsMiddlePart: 'Keskimmäiset sanat',
	FieldOptionsSecondPart: 'Viimeinen sana',
	FieldOptionsWholeField: 'Koko kenttä',
	FieldType: 'Kentän tyyppi',
	Fields: 'Kentät',
	File: 'Tiedosto',
	FileDownloaded: '<strong>{fileName}</strong> ladattu',
	FileInvalidType: 'Tiedostoa ei tueta.',
	FileNotFound: 'Tiedostoa ei löydy',
	FileNotFoundDescription: 'Etsimäsi tiedosto ei ole saatavilla tai se on poistettu',
	FileTags: 'Tiedostotunnisteet',
	FileTagsHelper: 'Tunnisteet lisätään kaikkiin tiedostoihin',
	FileTooLarge: 'Tiedosto liian suuri.',
	FileTooSmall: 'Tiedosto liian pieni.',
	FileUploadComplete: 'Täydellinen',
	FileUploadFailed: 'Epäonnistui',
	FileUploadInProgress: 'Ladataan',
	FileUploadedNotificationSubject: '{actorProfileName} on ladattu tiedosto',
	Files: 'Tiedostot',
	FillOut: 'Täyttää',
	Filter: 'Suodattaa',
	FilterBy: 'Suodatusperuste',
	FilterByAmount: 'Suodata määrän mukaan',
	FilterByClient: 'Suodata asiakkaan mukaan',
	FilterByLocation: 'Suodata sijainnin mukaan',
	FilterByService: 'Suodata palvelun mukaan',
	FilterByStatus: 'Suodata tilan mukaan',
	FilterByTags: 'Suodata tunnisteiden mukaan',
	FilterByTeam: 'Suodata joukkueen mukaan',
	Filters: 'Suodattimet',
	FiltersAppliedToView: 'Suodattimet käytössä näkymässä',
	FinalAppointment: 'Lopullinen tapaaminen',
	FinalizeImport: 'Viimeistele tuonti',
	FinancialAnalyst: 'Talousanalyytikko',
	Finish: 'Valmis',
	Firefighter: 'Palomies',
	FirstName: 'Etunimi',
	FirstNameLastInitial: 'Etunimi, sukunimi',
	FirstPerson: '1. henkilö',
	FolderName: 'Kansion nimi',
	Folders: 'Kansiot',
	FontFamily: 'Fonttiperhe',
	ForClients: 'Asiakkaille',
	ForClientsDetails: 'Saan hoitoon tai terveyteen liittyviä palveluita',
	ForPractitioners: 'Harjoittelijoille',
	ForPractitionersDetails: 'Hallitse ja kehitä harjoituksiasi',
	ForgotPasswordConfirmAccessCode: 'Vahvistuskoodi',
	ForgotPasswordConfirmNewPassword: 'Uusi salasana',
	ForgotPasswordConfirmPageDescription:
		'Anna sähköpostiosoitteesi, uusi salasana ja vahvistuskoodi, jonka olemme juuri lähettäneet sinulle.',
	ForgotPasswordConfirmPageTitle: 'Palauta salasana',
	ForgotPasswordPageButton: 'Lähetä nollauslinkki',
	ForgotPasswordPageDescription: 'Anna sähköpostiosoitteesi, niin lähetämme sinulle linkin salasanan vaihtoon.',
	ForgotPasswordPageTitle: 'Salasana unohtunut',
	ForgotPasswordSuccessPageDescription: 'Tarkista postilaatikostasi palautuslinkki.',
	ForgotPasswordSuccessPageTitle: 'Nollauslinkki lähetetty!',
	Form: 'Lomake',
	FormAnswersSentToEmailNotification: 'Olemme lähettäneet kopion vastauksistasi osoitteeseen',
	FormBlocks: 'Muotoile lohkoja',
	FormFieldAddOption: 'Lisää vaihtoehto',
	FormFieldAddOtherOption: 'Lisää "muu"',
	FormFieldOptionPlaceholder: 'Vaihtoehto {index}',
	FormStructures: 'Muotorakenteet',
	Format: 'Muoto',
	FormatLinkButtonColor: 'Painikkeen väri',
	Forms: 'Lomakkeet',
	FormsAndAgreementsValidationMessage:
		'Kaikki lomakkeet ja sopimukset on täytettävä, jotta vastaanottoprosessia voidaan jatkaa.',
	FormsCategoryDescription: 'Potilastietojen keräämiseen ja järjestämiseen',
	Frankfurt: 'Frankfurt',
	Free: 'Ilmainen',
	FreePlanInclusionFive: 'Automaattinen laskutus ',
	FreePlanInclusionFour: 'Asiakasportaali',
	FreePlanInclusionHeader: 'Aloita kanssa',
	FreePlanInclusionOne: 'Rajoittamaton määrä asiakkaita',
	FreePlanInclusionSix: 'Live-tuki',
	FreePlanInclusionThree: '1 Gt tallennustilaa',
	FreePlanInclusionTwo: 'Teleterveys',
	FreeSubscriptionPlanSubtitle: 'Ilmainen kaikille',
	FreeSubscriptionPlanTitle: 'Ilmainen',
	Friday: 'perjantai',
	From: 'From',
	FullName: 'Koko nimi',
	FunctionalMedicineOrNaturopath: 'Funktionaalinen lääketiede tai naturopaatti',
	FuturePaymentsAuthoriseProvider: 'Anna {provider} käyttää tallennettua maksutapaa tulevaisuudessa',
	FuturePaymentsSavePaymentMethod: 'Tallenna {paymentMethod} tulevia maksuja varten',
	GST: 'GST',
	Gender: 'Sukupuoli',
	GeneralAvailability: 'Yleinen saatavuus',
	GeneralAvailabilityDescription:
		'Aseta, milloin olet säännöllisesti tavoitettavissa. Asiakkaat voivat varata palvelujasi vain aukioloaikoina.',
	GeneralAvailabilityDescription2:
		'Luo aikatauluja saatavuuden ja haluttujen palvelutarjousten perusteella tiettyinä aikoina määrittääksesi online-varauksesi saatavuuden.',
	GeneralAvailabilityInfo: 'Käytettävissäsi olevat aukioloajat määräävät online-varauksesi saatavuuden',
	GeneralAvailabilityInfo2:
		'Ryhmätilaisuuksia tarjoavien palvelujen tulisi käyttää uutta aikataulua, jotta asiakkaiden verkossa varattavissa olevat tunnit vähenevät.',
	GeneralHoursPlural: '{count} {count, plural, one {aika} other {aikaa}}',
	GeneralPractitioner: 'Yleislääkäri',
	GeneralPractitioners: 'Yleislääkärit',
	GeneralServiceAvailabilityInfo: 'Tämä aikataulu ohittaa määrättyjen ryhmän jäsenten toiminnan',
	Generate: 'Luo',
	GenerateBillingItemsBannerContent: 'Laskutuskohteita ei luoda automaattisesti toistuvia tapaamisia varten.',
	GenerateItems: 'Luo kohteita',
	GenerateNote: 'Luo muistiinpano',
	GenerateNoteConfirmationModalDescription:
		'Mitä haluaisit tehdä? Luodaanko uusi luotu muistiinpano, lisätäänkö olemassa olevaan muistiinpanoon vai korvataanko sen sisältö?',
	GenerateNoteFor: 'Luo muistiinpano kohteelle',
	GeneratingContent: 'Luodaan sisältöä...',
	GeneratingNote: 'Luodaan muistiinpanoasi...',
	GeneratingTranscript: 'Luodaan transkriptiota',
	GeneratingTranscriptDescription: 'Tämän käsittely voi kestää muutaman minuutin',
	GeneratingYourTranscript: 'Luodaan transkriptiotasi',
	GenericErrorDescription: '{module}a ei voitu ladata. Yritä uudelleen myöhemmin.',
	GenericErrorTitle: 'Tapahtui odottamaton virhe',
	GenericFailureSnackbar: 'Anteeksi, tapahtui jotain odottamatonta. Päivitä sivu ja yritä uudelleen.',
	GenericSavedSuccessSnackbar: 'Menestys! Muutokset tallennettu',
	GeneticCounselor: 'Geneettinen neuvonantaja',
	Gerontologist: 'Gerontologi',
	Get50PercentOff: 'Saat 50 % alennusta!',
	GetHelp: 'Hanki apua',
	GetStarted: 'Aloita',
	GettingStartedAppointmentTypes: 'Luo tapaamistyyppejä',
	GettingStartedAppointmentTypesDescription:
		'Virtaviivaista ajoitustasi ja laskutustasi mukauttamalla palveluitasi, hintojasi ja laskutuskoodejasi',
	GettingStartedAppointmentTypesTitle: 'Ajoittaa ',
	GettingStartedClients: 'Lisää asiakkaasi',
	GettingStartedClientsDescription:
		'Valmistaudu asiakkaiden kanssa tulevia tapaamisia, muistiinpanoja ja maksuja varten',
	GettingStartedClientsTitle: 'Kaikki alkaa asiakkaista',
	GettingStartedCreateClient: 'Luo asiakas',
	GettingStartedImportClients: 'Tuo asiakkaita',
	GettingStartedInvoices: 'Lasku kuin ammattilainen',
	GettingStartedInvoicesDescription: `Ammattimaisten laskujen luominen on helppoa.
 Lisää logosi, sijaintisi ja maksuehtosi`,
	GettingStartedInvoicesTitle: 'Laita paras jalkasi eteenpäin',
	GettingStartedMobileApp: 'Hanki mobiilisovellus',
	GettingStartedMobileAppDescription:
		'Voit ladata Carepatronin iOS-, Android- tai pöytätietokoneellesi, jotta voit käyttää sitä helposti liikkeellä ollessasi',
	GettingStartedMobileAppTitle: 'Työskentele mistä tahansa',
	GettingStartedNavItem: 'Aloitus',
	GettingStartedPageTitle: 'Carepatronin käytön aloittaminen',
	GettingStartedPayments: 'Hyväksy verkkomaksut',
	GettingStartedPaymentsDescription: `Saat maksut nopeammin antamalla asiakkaillesi mahdollisuuden maksaa verkossa.
 Näet kaikki laskusi ja maksusi yhdessä paikassa`,
	GettingStartedPaymentsTitle: 'Tee maksuista helppoa',
	GettingStartedSaveBranding: 'Tallenna brändäys',
	GettingStartedSyncCalendars: 'Synkronoi muut kalenterit',
	GettingStartedSyncCalendarsDescription:
		'Carepatron tarkistaa kalenteristasi ristiriitoja, joten tapaamisia sovitaan vain, kun olet tavoitettavissa',
	GettingStartedSyncCalendarsTitle: 'Pysy aina ajan tasalla',
	GettingStartedVideo: 'Katso esittelyvideo',
	GettingStartedVideoDescription:
		'Ensimmäiset all-in-one terveydenhuollon työtilat pienille ryhmille ja heidän asiakkailleen',
	GettingStartedVideoTitle: 'Tervetuloa Carepatroniin',
	GetttingStartedGetMobileDownload: 'Lataa sovellus',
	GetttingStartedGetMobileNoDownload:
		'Ei yhteensopiva tämän selaimen kanssa. Jos käytät iPhonea tai iPadia, avaa tämä sivu Safarissa. Muussa tapauksessa yritä avata se Chromessa.',
	Glossary: 'Sanasto',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail sallii vain 500 viestin lähettämisen tililtäsi päivässä. Jotkut viestit saattavat epäonnistua. Haluatko jatkaa?',
	GoToAppointment: 'Mene tapaamiseen',
	GoToApps: 'Siirry sovelluksiin',
	GoToAvailability: 'Siirry saatavuuteen',
	GoToClientList: 'Siirry asiakasluetteloon',
	GoToClientRecord: 'Siirry asiakastietueeseen',
	GoToClientSettings: 'Siirry asiakasasetuksiin nyt',
	GoToInvoiceTemplates: 'Siirry laskun malleihin',
	GoToNotificationSettings: 'Siirry ilmoitusasetuksiin',
	GoToPaymentSettings: 'Siirry maksuasetuksiin',
	Google: 'Google',
	GoogleCalendar: 'Google-kalenteri',
	GoogleColor: 'Google-kalenterin väri',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Tag Managerin säilön tunnus',
	GotIt: 'Selvä!',
	Goto: 'Siirry osoitteeseen',
	Granddaughter: 'Tyttärentytär',
	Grandfather: 'Isoisä',
	Grandmother: 'Isoäiti',
	Grandparent: 'Isovanhempi',
	Grandson: 'Pojanpoika',
	GrantPortalAccess: 'Myönnä portaalin käyttöoikeus',
	GraphicDesigner: 'Graafinen suunnittelija',
	Grid: 'Ruudukko',
	GridView: 'Ruudukkonäkymä',
	Group: 'ryhmä',
	GroupBy: 'Ryhmittelyperuste',
	GroupEvent: 'Ryhmätapahtuma',
	GroupEventHelper: 'Aseta osallistujarajat palvelulle',
	GroupFilterLabel: 'Kaikki {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Ryhmän tunnus',
	GroupInputFieldsFormPrimaryText: 'Ryhmän syöttökentät',
	GroupInputFieldsFormSecondaryText: 'Valitse tai lisää mukautettuja kenttiä',
	GuideTo: 'Opas {value}',
	GuideToImproveVideoQuality: 'Ohje videon laadun parantamiseen',
	GuideToManagingPayers: 'Maksujen hallinta',
	GuideToSubscriptionsBilling: 'Opas tilausten laskutukseen',
	GuideToTroubleshooting: 'Ohje vianetsintään',
	Guidelines: 'Ohjeet',
	GuidelinesCategoryDescription: 'Kliinisen päätöksenteon ohjaamiseen',
	HST: 'HST',
	HairStylist: 'Hiusstylisti',
	HaveBeenWaiting: 'Olet odottanut pitkään',
	HeHim: 'Hän/Hän',
	HeaderAccountSettings: 'Profiili',
	HeaderCalendar: 'Kalenteri',
	HeaderCalls: 'Puhelut',
	HeaderClientAppAccountSettings: 'Tilin asetukset',
	HeaderClientAppCalls: 'Puhelut',
	HeaderClientAppMyDocumentation: 'Dokumentaatio',
	HeaderClientAppMyRelationships: 'Minun ihmissuhteeni',
	HeaderClients: 'Asiakkaat',
	HeaderHelp: 'Auttaa',
	HeaderMoreOptions: 'Lisää vaihtoehtoja',
	HeaderStaff: 'Henkilökunta',
	HealthCoach: 'Terveysvalmentaja',
	HealthCoaches: 'Terveysvalmentajat',
	HealthEducator: 'Terveyskasvattaja',
	HealthInformationTechnician: 'Terveystietoteknikko',
	HealthPolicyExpert: 'Terveyspolitiikan asiantuntija',
	HealthServicesAdministrator: 'Terveyspalvelujen johtaja',
	HelpArticles: 'Ohjeet',
	HiddenColumns: 'Piilotetut sarakkeet',
	HiddenFields: 'Piilotetut kentät',
	HiddenSections: 'Piilotetut osiot',
	HiddenSectionsAndFields: 'Piilotetut osiot/kentät',
	HideColumn: 'Piilota sarake',
	HideColumnButton: 'Piilota sarake {value} -painike',
	HideDetails: 'Piilota tiedot',
	HideField: 'Piilota kenttä',
	HideFullAddress: 'Piilottaa',
	HideMenu: 'Piilota valikko',
	HideMergeSummarySidebar: 'Piilota yhdistämisyhteenveto',
	HideSection: 'Piilota osio',
	HideYourView: 'Piilota näkymäsi',
	Highlight: 'Korosta väri',
	Highlighter: 'Korostin',
	History: 'Historia',
	HistoryItemFooter: '{actors, select, undefined {{date} klo {time}} other {• {actors} • {date} klo {time}}}',
	HistorySidePanelEmptyState: 'Historiatietoja ei löytynyt',
	HistoryTitle: 'Aktiviteettiloki',
	HolisticHealthPractitioner: 'Holistinen terveydenhuollon ammattilainen',
	HomeCaregiver: 'Kotihoitaja',
	HomeHealthAide: 'Kotiterveysapulainen',
	HomelessShelter: 'Asunnottomien turvakoti',
	HourAbbreviation: '{count} {count, plural, one {h} other {h}}',
	Hourly: 'Tuntihintainen',
	HoursPlural: '{age, plural, one {# tunti} other {# tuntia}}',
	HowCanWeImprove: 'Miten voimme parantaa tätä?',
	HowCanWeImproveResponse: 'Miten voimme parantaa tätä vastausta?',
	HowDidWeDo: 'Miten me pärjäsimme?',
	HowDoesReferralWork: 'Opas viittausohjelmaan',
	HowToUseAiSummarise: 'Kuinka käyttää AI Summarizea',
	HumanResourcesManager: 'Henkilöstöpäällikkö',
	Husband: 'Aviomies',
	Hypnotherapist: 'Hypnoterapeutti',
	IVA: 'ALV',
	IgnoreNotification: 'Ohita ilmoitus',
	IgnoreOnce: 'Ohita kerran',
	IgnoreSender: 'Ohita lähettäjä',
	IgnoreSenderDescription:
		'Tämän lähettäjän tulevat keskustelut siirretään automaattisesti kohtaan Muu. Haluatko varmasti jättää nämä lähettäjät huomioimatta?',
	IgnoreSenders: 'Ohita lähettäjät',
	IgnoreSendersSuccess: 'Jätettiin huomiotta sähköpostiosoite <mark>{addresses}</mark>',
	Ignored: 'Ohitettu',
	Image: 'Kuva',
	Import: 'Tuoda',
	ImportActivity: 'Tuo toiminto',
	ImportClientSuccessSnackbarDescription: 'Tiedostosi tuonti onnistui',
	ImportClientSuccessSnackbarTitle: 'Tuonti onnistui!',
	ImportClients: 'Tuo asiakkaita',
	ImportClientsFailureSnackbarDescription: 'Tiedostoasi ei voitu tuoda onnistuneesti virheen vuoksi.',
	ImportClientsFailureSnackbarTitle: 'Tuonti epäonnistui!',
	ImportClientsGuide: 'Opas asiakastietojen tuontiin',
	ImportClientsInProgressSnackbarDescription: 'Tämä saa kestää vain minuutin.',
	ImportClientsInProgressSnackbarTitle: 'Tuo {fileName}',
	ImportClientsModalDescription:
		'Valitse, mistä tietosi ovat peräisin – onko se laitteessasi oleva tiedosto, kolmannen osapuolen palvelu tai jokin muu ohjelmistoalusta.',
	ImportClientsModalFileUploadHelperText: 'Tukee {fileTypes}. Kokoa raja {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Opas asiakastietojen tuontiin',
	ImportClientsModalStep1Label: 'Valitse tietolähde',
	ImportClientsModalStep2Label: 'Lataa tiedosto',
	ImportClientsModalStep3Label: 'Tarkista kentät',
	ImportClientsModalTitle: 'Tuodaan asiakastietojasi',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {asiakas} other {asiakkaat}} valmis tuontiin',
	ImportContactFailedNotificationSubject: 'Tietojen tuonti epäonnistui',
	ImportDataSourceSelectorLabel: 'Tuo tietolähde kohteesta',
	ImportDataSourceSelectorPlaceholder: 'Hae tai valitse tuontitietolähde',
	ImportExportButton: 'Tuo/Vie',
	ImportFailed: 'Tuonti epäonnistui',
	ImportFromAnotherPlatformTileDescription: 'Lataa asiakastietojesi vienti ja lataa ne tähän.',
	ImportFromAnotherPlatformTileLabel: 'Tuo muualta',
	ImportGuide: 'Tuo opas',
	ImportInProgress: 'Tuonti on käynnissä',
	ImportProcessing: 'Tuonnin käsittely...',
	ImportSpreadsheetDescription:
		'Voit tuoda olemassa olevan asiakasluettelosi Carepatroniin lataamalla taulukkolaskentatiedoston, jossa on taulukkotietoja, kuten .CSV, .XLS tai .XLSX.',
	ImportSpreadsheetTitle: 'Tuo laskentataulukkotiedostosi',
	ImportTemplates: 'Tuo templatet',
	Importing: 'Tuodaan',
	ImportingCalendarProductEvents: 'Tuodaan {product} -tapahtumia',
	ImportingData: 'Tuodaan tietoja',
	ImportingSpreadsheetDescription: 'Tämän suorittaminen kestää vain minuutin',
	ImportingSpreadsheetTitle: 'Tuodaan laskentataulukkoasi',
	ImportsInProgress: 'Tuonnit käynnissä',
	InPersonMeeting: 'Henkilökohtainen tapaaminen',
	InProgress: 'Käynnissä',
	InTransit: 'Kuljetuksessa',
	InTransitTooltip:
		'In Transit -saldo sisältää kaikki Stripeltä pankkitilillesi maksetut laskut. Näiden varojen maksaminen kestää yleensä 3–5 päivää.',
	Inactive: 'Ei-aktiivinen',
	InboundOrOutboundCalls: 'Saapuvat tai lähtevät puhelut',
	Inbox: 'Saapuneet',
	InboxAccessRestricted: 'Pääsy rajoitettu. Ota yhteyttä postilaatikon omistajaan lupia varten.',
	InboxAccountAlreadyConnected: 'Kanava, jota yritit yhdistää, on jo yhdistetty Carepatroniin',
	InboxAddAttachments: 'Lisää liitteitä',
	InboxAreYouSureDeleteMessage: 'Haluatko varmasti poistaa tämän viestin?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Onnistuneesti suljettu # keskustelu} other {Onnistuneesti suljettu # keskustelua}}',
	InboxBulkComposeModalTitle: 'Kirjoita joukkoviesti',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Poistettiin # keskustelu onnistuneesti} other {Poistettiin # keskustelua onnistuneesti}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Merkittiin # keskustelu luetuksi} other {Merkittiin # keskustelua luetuiksi}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Avaamistiedosto # keskustelun uudelleen} other {Avaamistiedosto # keskustelujen uudelleen}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Merkittiin # keskustelu lukemattomaksi} other {Merkittiin # keskustelua lukemattomina}}',
	InboxChatCreateGroup: 'Luo ryhmä',
	InboxChatDeleteGroupModalDescription:
		'Oletko varma, että haluat poistaa tämän ryhmän? Kaikki viestit ja liitteet poistetaan.',
	InboxChatDeleteGroupModalTitle: 'Poista ryhmä',
	InboxChatDiscardDraft: 'Hylkää luonnos',
	InboxChatDragDropText: 'Pudota tiedostot tähän ladataksesi ne',
	InboxChatGroupConversation: 'Ryhmäkeskustelu',
	InboxChatGroupCreateModalDescription:
		'Aloita uusi ryhmä viestimiseen ja yhteistyöhön tiimisi, asiakkaidesi tai yhteisösi kanssa.',
	InboxChatGroupCreateModalTitle: 'Luo ryhmä',
	InboxChatGroupMembers: 'Ryhmän jäsenet',
	InboxChatGroupModalGroupNameFieldLabel: 'Ryhmän nimi',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Esim. asiakastuki, ylläpito',
	InboxChatGroupModalGroupNameFieldRequired: 'Tämä kenttä on pakollinen',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Vähintään yksi jäsen vaaditaan',
	InboxChatGroupModalMembersFieldLabel: 'Valitse ryhmän jäsenet',
	InboxChatGroupModalMembersFieldPlaceholder: 'Valitse jäsenet',
	InboxChatGroupUpdateModalTitle: 'Hallitse ryhmää',
	InboxChatLeaveGroup: 'Poistu ryhmästä',
	InboxChatLeaveGroupModalDescription:
		'Oletko varma, että haluat poistua tästä ryhmästä? Et enää vastaanota viestejä tai päivityksiä.',
	InboxChatLeaveGroupModalTitle: 'Poistu ryhmästä',
	InboxChatLeftGroupMessage: 'Vasen ryhmäviesti',
	InboxChatManageGroup: 'Hallitse ryhmää',
	InboxChatSearchParticipants: 'Valitse vastaanottajat',
	InboxCloseConversationSuccess: 'Keskustelun päättyminen onnistui',
	InboxCompose: 'Säveltää',
	InboxComposeBulk: 'Joukkoviesti',
	InboxComposeCarepatronChat: 'Messenger',
	InboxComposeChat: 'Kirjoita chat',
	InboxComposeDisabledNoConnection: 'Yhdistä sähköpostitili lähettääksesi viestejä',
	InboxComposeDisabledNoPermissionTooltip: 'Sinulla ei ole lupaa lähettää viestejä tästä postilaatikosta',
	InboxComposeEmail: 'Kirjoita sähköposti',
	InboxComposeMessageFrom: 'From',
	InboxComposeMessageRecipientBcc: 'Piilokopio',
	InboxComposeMessageRecipientCc: 'Kopio',
	InboxComposeMessageRecipientTo: 'Vastaanottaja',
	InboxComposeMessageSubject: 'Aihe:',
	InboxConnectAccountButton: 'Yhdistä sähköpostisi',
	InboxConnectedDescription: 'Saapuneet-kansiossasi ei ole yhteyttä',
	InboxConnectedHeading: 'Keskustelusi näkyvät täällä heti, kun aloitat viestien vaihtamisen',
	InboxConnectedHeadingClientView: 'Virtaviivaista asiakasviestintääsi',
	InboxCreateFirstInboxButton: 'Luo ensimmäinen postilaatikkosi',
	InboxCreationSuccess: 'Postilaatikon luominen onnistui',
	InboxDeleteAttachment: 'Poista liite',
	InboxDeleteConversationSuccess: 'Keskustelun poistaminen onnistui',
	InboxDeleteMessage: 'Poistetaanko viesti?',
	InboxDirectMessage: 'Suora viesti',
	InboxEditDraft: 'Muokkaa luonnosta',
	InboxEmailComposeReplyEmail: 'Kirjoita vastaus',
	InboxEmailDraft: 'Luonnos',
	InboxEmailNotFound: 'Sähköpostia ei löydy',
	InboxEmailSubjectFieldInformation: 'Aiherivin muuttaminen luo uuden ketjutetun sähköpostin.',
	InboxEmptyArchiveDescription: 'Arkistoitua keskustelua ei löytynyt',
	InboxEmptyBinDescription: 'Poistettua keskustelua ei löytynyt',
	InboxEmptyBinHeading: 'Kaikki selvä, täällä ei ole mitään nähtävää',
	InboxEmptyBinSuccess: 'Keskustelujen poistaminen onnistui',
	InboxEmptyCongratsHeading: 'Hienoa työtä! Istu alas ja rentoudu seuraavaan keskusteluun asti',
	InboxEmptyDraftDescription: 'Keskusteluluonnosta ei löytynyt',
	InboxEmptyDraftHeading: 'Kaikki selvä, täällä ei ole mitään nähtävää',
	InboxEmptyOtherDescription: 'Muuta keskustelua ei löytynyt',
	InboxEmptyScheduledHeading: 'Kaikki selkeä, keskusteluja ei ole ajoitettu lähetettäväksi',
	InboxEmptySentDescription: 'Lähetettyä keskustelua ei löytynyt',
	InboxForward: 'Eteenpäin',
	InboxGroupClientsLabel: 'Kaikki asiakkaat',
	InboxGroupClientsOverviewLabel: 'Asiakkaat',
	InboxGroupClientsSelectedItemPrefix: 'Asiakas',
	InboxGroupStaffsLabel: 'Koko joukkue',
	InboxGroupStaffsOverviewLabel: 'Joukkue',
	InboxGroupStaffsSelectedItemPrefix: 'Joukkue',
	InboxGroupStatusLabel: 'Kaikki tila',
	InboxGroupStatusOverviewLabel: 'Lähetä tilaan',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Kaikki tunnisteet',
	InboxGroupTagsOverviewLabel: 'Lähetä tunnisteelle',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Piilota lainattu teksti',
	InboxIgnoreConversationSuccess: 'Keskustelun ohittaminen onnistui',
	InboxMessageAllLabelRecipientsCount: 'Kaikki {label} -saajat ({count})',
	InboxMessageBodyPlaceholder: 'Lisää viestisi',
	InboxMessageDeleted: 'Viesti poistettu',
	InboxMessageMarkedAsRead: 'Viesti merkitty luetuksi',
	InboxMessageMarkedAsUnread: 'Viesti merkitty lukemattomaksi',
	InboxMessageSentViaChat: '**Lähetetty chatin kautta**  • {time} {name}n toimesta',
	InboxMessageShowMoreRecipients: '+{count} lisää',
	InboxMessageWasDeleted: 'Tämä viesti on poistettu',
	InboxNoConnectionDescription: 'Yhdistä sähköpostitilisi tai luo postilaatikoita, joissa on useita sähköposteja',
	InboxNoConnectionHeading: 'Integroi asiakasviestintäsi',
	InboxNoDirectMessage: 'Ei uusia viestejä',
	InboxRecentConversations: 'Viimeaikainen',
	InboxReopenConversationSuccess: 'Keskustelun uudelleen avaaminen onnistui',
	InboxReply: 'Vastata',
	InboxReplyAll: 'Vastaa kaikkiin',
	InboxRestoreConversationSuccess: 'Keskustelun palautus onnistui',
	InboxScheduleSendCancelSendSuccess: 'Ajoitettu lähetys peruttiin ja viesti palautettiin luonnokseksi',
	InboxScheduleSendMessageSuccessDescription: 'Lähetetään aikataulutettuna {date}',
	InboxScheduleSendMessageSuccessTitle: 'Lähetyksen ajoitus',
	InboxSearchForConversations: 'Etsi "{query}"',
	InboxSendMessageSuccess: 'Keskustelun lähetys onnistui',
	InboxSettings: 'Saapuneet-kansion asetukset',
	InboxSettingsAppsDesc:
		'Hallinnoi tähän jaettuun postilaatikkoon yhdistettyjä sovelluksia: lisää tai poista yhteyksiä tarpeen mukaan.',
	InboxSettingsAppsNewConnectedApp: 'Uusi yhdistetty sovellus',
	InboxSettingsAppsTitle: 'Yhdistetyt sovellukset',
	InboxSettingsDeleteAccountFailed: 'Saapuneet-tilin poistaminen epäonnistui',
	InboxSettingsDeleteAccountSuccess: 'Saapuneet-tilin poistaminen onnistui',
	InboxSettingsDeleteAccountWarning:
		'{email} :n poistaminen katkaisee sen yhteyden postilaatikkoon {inboxName} ja pysäyttää viestien synkronoinnin.',
	InboxSettingsDeleteInboxFailed: 'Postilaatikon poistaminen epäonnistui',
	InboxSettingsDeleteInboxSuccess: 'Saapuneet-kansion poistaminen onnistui',
	InboxSettingsDeleteInboxWarning: `Poistamalla {inboxName} katkaiset kaikkien yhdistettyjen kanavien yhteyden ja poistat kaikki tähän postilaatikkoon liittyvät viestit. 
		Tämä toimenpide on pysyvä eikä sitä voi perua.`,
	InboxSettingsDetailsDesc: 'Viestintäpostilaatikko tiimillesi, jotta voit hallita asiakasviestejä tehokkaasti.',
	InboxSettingsDetailsTitle: 'Saapuneet-kansion tiedot',
	InboxSettingsEmailSignatureLabel: 'Sähköpostin allekirjoituksen oletusarvo',
	InboxSettingsReplyFormatDesc:
		'Määritä oletusvastausosoitteesi ja sähköpostin allekirjoitus näkymään jatkuvasti riippumatta siitä, kuka sähköpostin lähettää.',
	InboxSettingsReplyFormatTitle: 'Vastausmuoto',
	InboxSettingsSendFromLabel: 'Aseta oletusvastaus lähettäjältä ',
	InboxSettingsStaffDesc:
		'Hallitse tiimin jäsenten pääsyä tähän jaettuun postilaatikkoon saumattoman yhteistyön varmistamiseksi.',
	InboxSettingsStaffTitle: 'Nimeä tiimin jäseniä',
	InboxSettingsUpdateInboxDetailsFailed: 'Postilaatikon tietojen päivittäminen epäonnistui',
	InboxSettingsUpdateInboxDetailsSuccess: 'Postilaatikon tiedot päivitetty onnistuneesti',
	InboxSettingsUpdateInboxStaffsFailed: 'Saapuneet-tiimin jäsenten päivittäminen epäonnistui',
	InboxSettingsUpdateInboxStaffsSuccess: 'Saapuneet-tiimin jäsenten päivitys onnistui',
	InboxSettingsUpdateReplyFormatFailed: 'Vastausmuodon päivittäminen epäonnistui',
	InboxSettingsUpdateReplyFormatSuccess: 'Vastausmuodon päivitys onnistui',
	InboxShowQuotedText: 'Näytä lainattu teksti',
	InboxStaffRoleAdminDescription: 'Tarkastele, vastaa ja hallinnoi postilaatikoita',
	InboxStaffRoleResponderDescription: 'Katso ja vastaa',
	InboxStaffRoleViewerDescription: 'Näytä vain',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Jatka muokkaamista',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Kyllä, vaihda joukkolähetykseen',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Olet valinnut yli {count} vastaanottajaa. Haluatko lähettää sen massasähköpostina?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Varoitus',
	InboxSwitchToOtherInbox: 'Vaihda toiseen postilaatikkoon',
	InboxUndoSendMessageSuccess: 'Lähetys kumottu',
	IncludeLineItems: 'Sisällytä rivikohdat',
	IncludeSalesTax: 'Verollinen',
	IncludesAiSmartPrompt: 'Sisältää AI-älykehotteet',
	Incomplete: 'Epätäydellinen',
	IncreaseIndent: 'Lisää sisennystä',
	IndianHealthServiceFreeStandingFacility: 'Indian Health Service irrallinen laitos',
	IndianHealthServiceProviderFacility: 'Intian terveyspalvelun tarjoajapohjainen laitos',
	Information: 'Tiedot',
	InitialAssessment: 'Alkuarviointi',
	InitialSignupPageClientFamilyTitle: 'Asiakas tai perheenjäsen',
	InitialSignupPageProviderTitle: 'Terveys ',
	InitialTreatment: 'Alkuhoito',
	Initials: 'Nimikirjaimet',
	InlineEmbed: 'Sisäkkäinen upotus',
	InputPhraseToConfirm: 'Vahvistaaksesi, kirjoita {confirmationPhrase}.',
	Insert: 'Lisää',
	InsertTable: 'Lisää taulukko',
	InstallCarepatronOnYourIphone1: 'Asenna Carepatron iOS-laitteeseen: napauta',
	InstallCarepatronOnYourIphone2: 'ja sitten Lisää aloitusnäyttöön',
	InsufficientCalendarScopesSnackbar: 'Synkronointi epäonnistui – anna kalenterin käyttöoikeudet Carepatronille',
	InsufficientInboxScopesSnackbar: 'Synkronointi epäonnistui – salli sähköpostin käyttöoikeudet Carepatronille',
	InsufficientScopeErrorCodeSnackbar: 'Synkronointi epäonnistui – anna kaikki käyttöoikeudet Carepatronille',
	Insurance: 'Vakuutus',
	InsuranceAmount: 'Vakuutusmäärä',
	InsuranceClaim: 'Vakuutuskorvaus',
	InsuranceClaimAiChatPlaceholder: 'Kysy vakuutuskorvauksesta...',
	InsuranceClaimAiClaimNumber: 'Vaatimus {number}',
	InsuranceClaimAiSubtitle: 'Vakuutuslaskujen käsittely • Vaatimusten tarkistus',
	InsuranceClaimDeniedSubject:
		'Väite {claimNumber}, joka on lähetetty kohteelle {payerNumber} {payerName}, hylättiin',
	InsuranceClaimErrorDescription:
		'Vakuutusvaatimuksessa on virheitä, jotka maksaja tai clearing house on ilmoittanut. Tarkista seuraavat virheilmoitukset ja lähetä vaatimus uudelleen.',
	InsuranceClaimErrorGuideLink: 'Vakuutusvaatimusten opas',
	InsuranceClaimErrorTitle: 'Vaatimusten lähetyksen virheet',
	InsuranceClaimNotFound: 'Vakuutusvaatimusta ei löydy',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Osittainen maksu, jonka suuruus on {paymentAmount}} other {{paymentAmount} suuruinen maksu}} korvausvaatimukselle {claimNumber} maksajalta {payerNumber} ({payerName}) on kirjattu',
	InsuranceClaimRejectedSubject:
		'Väite {claimNumber}, joka lähetettiin vastaanottajalle {payerNumber} {payerName}, hylättiin',
	InsuranceClaims: 'Vakuutusvaatimukset',
	InsuranceInformation: 'Vakuutustiedot',
	InsurancePaid: 'Vakuutus maksettu',
	InsurancePayer: 'Vakuutuksen maksaja',
	InsurancePayers: 'Vakuutuksen maksajat',
	InsurancePayersDescription: 'Näytä tilillesi lisätyt maksajat ja hallitse rekisteröintiä.',
	InsurancePayment: 'Vakuutusmaksu',
	InsurancePoliciesDetailsSubtitle: 'Lisää asiakasvakuutustiedot korvausten tueksi.',
	InsurancePoliciesDetailsTitle: 'Käytäntöjen tiedot',
	InsurancePoliciesListSubtitle: 'Lisää asiakasvakuutustiedot korvausten tueksi.',
	InsurancePoliciesListTitle: 'Vakuutukset',
	InsuranceSelfPay: 'Omavastainen maksu',
	InsuranceType: 'Vakuutustyyppi',
	InsuranceUnpaid: 'Vakuutus maksamatta',
	Intake: 'Saanti',
	IntakeExpiredErrorCodeSnackbar:
		'Tämä otto on vanhentunut. Ota yhteyttä palveluntarjoajaasi, jos haluat lähettää uuden vastaanoton.',
	IntakeNotFoundErrorSnackbar:
		'Tätä ottoa ei löytynyt. Ota yhteyttä palveluntarjoajaasi, jos haluat lähettää uuden vastaanoton.',
	IntakeProcessLearnMoreInstructions: 'Opas vastaanottolomakkeiden asettamiseen',
	IntakeTemplateSelectorPlaceholder: 'Valitse lomakkeet ja sopimukset, jotka lähetetään asiakkaallesi täytettäväksi',
	Integration: 'Integrointi',
	IntenseBlur: 'Sumentaa taustasi voimakkaasti',
	InteriorDesigner: 'Sisustussuunnittelija',
	InternetBanking: 'Pankkisiirto',
	Interval: 'Intervalli',
	IntervalDays: 'Aikaväli (päiviä)',
	IntervalHours: 'Aikaväli (tuntia)',
	Invalid: 'Virheellinen',
	InvalidDate: 'Virheellinen päivämäärä',
	InvalidDateFormat: 'Päivämäärän on oltava muodossa {format}',
	InvalidDisplayName: 'Näyttönimi ei voi sisältää {value}',
	InvalidEmailFormat: 'Virheellinen sähköpostimuoto',
	InvalidFileType: 'Virheellinen tiedostotyyppi',
	InvalidGTMContainerId: 'Virheellinen GTM-säilön tunnuksen muoto',
	InvalidPaymentMethodCode: 'Valittu maksutapa ei kelpaa. Valitse toinen.',
	InvalidPromotionCode: 'Tarjouskoodi on virheellinen',
	InvalidReferralDescription: 'Käytän jo Carepatronia',
	InvalidStatementDescriptor: `Lausekkeen kuvaajan tulee olla 5–22 merkkiä pitkä ja sisältää vain kirjaimia, numeroita, välilyöntejä, eikä se saa sisältää <, >, \\, ', ", *`,
	InvalidToken: 'Virheellinen tunnus',
	InvalidTotpSetupVerificationCode: 'Virheellinen vahvistuskoodi.',
	InvalidURLErrorText: 'Tämän on oltava kelvollinen URL-osoite',
	InvalidZoomTokenErrorCodeSnackbar:
		'Zoom-tunnus on vanhentunut. Yhdistä Zoom-sovellus uudelleen ja yritä uudelleen.',
	Invite: 'Kutsu',
	InviteRelationships: 'Kutsu suhteita',
	InviteToPortal: 'Kutsu portaaliin',
	InviteToPortalModalDescription: 'Asiakkaallesi lähetetään kutsusähköposti Carepatroniin rekisteröitymistä varten.',
	InviteToPortalModalTitle: 'Kutsu {name} Carepatron-portaaliin',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Kutsu uusi käyttäjä',
	Invited: 'Kutsuttu',
	Invoice: 'Laskuttaa',
	InvoiceColorPickerDescription: 'Laskussa käytettävä väriteema',
	InvoiceColorTheme: 'Laskun väriteema',
	InvoiceContactDeleted: 'Laskun yhteystieto on poistettu, eikä tätä laskua voi päivittää.',
	InvoiceDate: 'Julkaisupäivämäärä',
	InvoiceDetails: 'Laskun tiedot',
	InvoiceFieldsPlaceholder: 'Etsi kenttiä...',
	InvoiceFrom: 'Lasku {number} {fromProvider}lta',
	InvoiceInvalidCredit: 'Virheellinen luottosumma, luottosumma ei voi ylittää laskun loppusummaa',
	InvoiceNotFoundDescription:
		'Ota yhteyttä palveluntarjoajaasi ja pyydä heiltä lisätietoja tai lähetä lasku uudelleen.',
	InvoiceNotFoundTitle: 'Laskua ei löydy',
	InvoiceNumber: 'Lasku #',
	InvoiceNumberFormat: 'Lasku #{number}',
	InvoiceNumberMustEndWithDigit: 'Laskun numeron tulee päättyä numeroon (0-9)',
	InvoicePageHeader: 'Laskut',
	InvoicePaidNotificationSubject: 'Lasku {invoiceNumber} maksettu',
	InvoiceReminder: 'Laskumuistutukset',
	InvoiceReminderSentence: 'Lähetä {deliveryType} -muistutus {interval} {unit} {beforeAfter} laskun eräpäivää',
	InvoiceReminderSettings: 'Laskumuistutusasetukset',
	InvoiceReminderSettingsInfo: 'Muistutukset koskevat vain Carepatronissa lähetettyjä laskuja',
	InvoiceReminders: 'Laskumuistutukset',
	InvoiceRemindersInfo:
		'Aseta automaattiset muistutukset laskun eräpäiville. Muistutukset koskevat vain Carepatronin kautta lähetettyjä laskuja',
	InvoiceSettings: 'Laskutusasetukset',
	InvoiceStatus: 'Laskun tila',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, Yhdysvallat',
	InvoiceTemplateDescriptionPlaceholder: 'Lisää muistiinpanoja, pankkisiirtotietoja tai vaihtoehtoisia maksuehtoja',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Itsenäinen ammatinharjoittaja',
	InvoiceTemplateEthnicityPlaceholder: 'Kaukasialainen',
	InvoiceTemplateNotFoundDescription: 'Ota yhteyttä palveluntarjoajaasi ja kysy heiltä lisätietoja.',
	InvoiceTemplateNotFoundTitle: 'Laskumallia ei löydy',
	InvoiceTemplates: 'Laskumallit',
	InvoiceTemplatesDescription:
		'Räätälöi laskumallisi vastaamaan brändiäsi, täyttämään säädösten vaatimukset ja palvelemaan asiakkaiden toiveita käyttäjäystävällisillä malleillamme.',
	InvoiceTheme: 'Laskun teema',
	InvoiceTotal: 'Laskun summa',
	InvoiceUninvoicedAmounts: 'Laskuta laskuttamattomat summat',
	InvoiceUpdateVersionMessage:
		'Tämän laskun muokkaaminen vaatii uusimman version. Lataa Carepatron uudelleen ja yritä uudelleen.',
	Invoices: '{count, plural, one {Lasku} other {Laskut}}',
	InvoicesEmptyStateDescription: 'Laskuja ei löytynyt',
	InvoicingAndPayment: 'Laskutus ',
	Ireland: 'Irlanti',
	IsA: 'on a',
	IsBetween: 'on välillä',
	IsEqualTo: 'on yhtä suuri kuin',
	IsGreaterThan: 'on suurempi kuin',
	IsGreaterThanOrEqualTo: 'on suurempi tai yhtä suuri kuin',
	IsLessThan: 'on pienempi kuin',
	IsLessThanOrEqualTo: 'on pienempi tai yhtä suuri kuin',
	IssueCredit: 'Myönnä luotto',
	IssueCreditAdjustment: 'Luoton oikaisu',
	IssueDate: 'Julkaisupäivä',
	Italic: 'Kursiivi',
	Items: 'Tuotteet',
	ItemsAndAdjustments: 'Kohteet ja oikaisut',
	ItemsRemaining: '+{count} kohdetta jäljellä',
	JobTitle: 'Työnimike',
	Join: 'Liity',
	JoinCall: 'Liity puheluun',
	JoinNow: 'Liity nyt',
	JoinProduct: 'Liity {product}',
	JoinVideoCall: 'Liity videopuheluun',
	JoinWebinar: 'Liity webinaariin',
	JoinWithVideoCall: 'Liity {product}in kanssa',
	Journalist: 'Toimittaja',
	JustMe: 'vain minä',
	JustYou: 'Vain sinä',
	Justify: 'Perustella',
	KeepSeparate: 'Pidä erillään',
	KeepSeparateSuccessMessage: 'Olet onnistuneesti pitänyt {clientNames} erillään.',
	KeepWaiting: 'Jatka odottamista',
	Label: 'Label',
	LabelOptional: 'Tunniste (valinnainen)',
	LactationConsulting: 'Imetysneuvonta',
	Language: 'Kieli',
	Large: 'Suuri',
	LastDxCode: 'Viimeisin DX-koodi',
	LastLoggedIn: 'Viimeksi kirjautunut sisään {date} kello {time}',
	LastMenstrualPeriod: 'Viimeiset kuukautiset',
	LastMonth: 'Viime kuussa',
	LastNDays: 'Viimeiset {number} päivää',
	LastName: 'Sukunimi',
	LastNameFirstInitial: 'Sukunimi, etunimi',
	LastWeek: 'Viime viikolla',
	LastXRay: 'Viimeinen röntgen',
	LatestVisitOrConsultation: 'Viimeisin käynti tai konsultaatio',
	Lawyer: 'Lakimies',
	LearnMore: 'Lue lisää',
	LearnMoreTipsToGettingStarted: 'Lue lisää vinkkejä aloittamiseen',
	LearnToSetupInbox: 'Opas postilaatikkotilin määrittämiseen',
	Leave: 'Lähde',
	LeaveCall: 'Jätä puhelu',
	LeftAlign: 'Tasaa vasemmalle',
	LegacyBillingItemsNotAvailable:
		'Yksittäisiä laskutuskohteita ei ole vielä saatavilla tälle varaukselle. Voit silti laskuttaa sen normaalisti.',
	LegacyBillingItemsNotAvailableTitle: 'Perintälaskutus',
	LegalAndConsent: 'Laki ja suostumus',
	LegalConsentFormPrimaryText: 'Laillinen suostumus',
	LegalConsentFormSecondaryText: 'Hyväksy tai hylkää vaihtoehdot',
	LegalGuardian: 'Laillinen huoltaja',
	Letter: 'Kirje',
	LettersCategoryDescription: 'Kliinisen ja hallinnollisen kirjeenvaihdon luomiseen',
	Librarian: 'Kirjastonhoitaja',
	LicenseNumber: 'Lisenssin numero',
	LifeCoach: 'Life Coach',
	LifeCoaches: 'Elämänvalmentajat',
	Limited: 'Rajallinen',
	LineSpacing: 'Rivi- ja kappalevälit',
	LinearScaleFormPrimaryText: 'Lineaarinen asteikko',
	LinearScaleFormSecondaryText: 'Mittakaavavaihtoehdot 1-10',
	Lineitems: 'Rivikohdat',
	Link: 'Linkki',
	LinkClientFormSearchClientLabel: 'Etsi asiakas',
	LinkClientModalTitle: 'Linkki olemassa olevaan asiakkaaseen',
	LinkClientSuccessDescription:
		'<strong>{newName}n</strong> yhteystiedot lisätään <strong>{existingName}n</strong> tietueeseen.',
	LinkClientSuccessTitle: 'Linkitys olemassa olevaan yhteystietoon onnistui',
	LinkForCallCopied: 'Linkki kopioitu!',
	LinkToAnExistingClient: 'Linkki olemassa olevaan asiakkaaseen',
	LinkToClient: 'Linkki asiakkaaseen',
	ListAndTracker: 'Luettelo/Seuranta',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} on tässä puhelussa}
			other {{attendees} ovat tässä puhelussa}
		}`,
	ListStyles: 'Listaa tyylit',
	ListsAndTrackersCategoryDescription: 'Työn järjestämiseen ja seurantaan',
	LivingArrangements: 'Asumisjärjestelyt',
	LoadMore: 'Lataa lisää',
	Loading: 'Ladataan...',
	LocalizationPanelDescription: 'Hallinnoi kielen ja aikavyöhykkeen asetuksia',
	LocalizationPanelTitle: 'Kieli ja aikavyöhyke',
	Location: 'Sijainti',
	LocationDescription:
		'Määritä fyysisiä ja virtuaalisia sijainteja tietyillä osoitteilla, huoneiden nimillä ja virtuaalitilojen tyypeillä, jotta tapaamisten ja videopuheluiden ajoittaminen on helpompaa.',
	LocationNumber: 'Sijaintinumero',
	LocationOfService: 'Palvelun sijainti',
	LocationOfServiceRecommendedActionInfo: 'Tämän palvelun tietyn sijainnin lisääminen voi vaikuttaa saatavuuteesi.',
	LocationRemote: 'Etä',
	LocationType: 'Sijaintityyppi',
	Locations: 'Sijainnit',
	Lock: 'Lukko',
	Locked: 'Lukittu',
	LockedNote: 'Lukittu muistiinpano',
	LogInToSaveOrAuthoriseCard: 'Kirjaudu sisään tallentaaksesi tai valtuuttaaksesi kortin',
	LogInToSaveOrAuthorisePayment: 'Kirjaudu sisään tallentaaksesi tai valtuuttaaksesi maksun',
	Login: 'Kirjaudu sisään',
	LoginButton: 'Kirjaudu sisään',
	LoginEmail: 'Sähköposti',
	LoginForgotPasswordLink: 'Salasana unohtunut',
	LoginPassword: 'Salasana',
	Logo: 'Logo',
	LogoutAreYouSure: 'Kirjaudu ulos tästä laitteesta.',
	LogoutButton: 'Kirjaudu ulos',
	London: 'Lontoo',
	LongTextAnswer: 'Pitkä teksti vastaus',
	LongTextFormPrimaryText: 'Pitkä teksti',
	LongTextFormSecondaryText: 'Kappaletyylivaihtoehdot',
	Male: 'Uros',
	Manage: 'Hallitse',
	ManageAllClientTags: 'Hallitse kaikkia asiakastunnisteita',
	ManageAllNoteTags: 'Hallitse kaikkia muistiinpanotunnisteita',
	ManageAllTemplateTags: 'Hallitse kaikkia mallitunnisteita',
	ManageConnections: 'Hallitse yhteyksiä',
	ManageConnectionsGmailDescription: 'Muut tiimin jäsenet eivät näe synkronoitua Gmailiasi.',
	ManageConnectionsGoogleCalendarDescription:
		'Muut tiimin jäsenet eivät näe synkronoituja kalentereitasi. Asiakasaikoja voidaan päivittää tai poistaa vain Carepatronissa.',
	ManageConnectionsInboxSyncHelperText: 'Siirry Saapuneet-sivulle hallitaksesi Sync Inbox -asetuksia.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Muut tiimin jäsenet eivät voi nähdä synkronoituja kalentereitasi. Asiakasaikoja voidaan päivittää tai poistaa vain Carepatronissa.',
	ManageConnectionsOutlookDescription: 'Muut tiimin jäsenet eivät näe synkronoitua Microsoft Outlookia.',
	ManageInboxAccountButton: 'Uusi postilaatikko',
	ManageInboxAccountEdit: 'Hallinnoi postilaatikkoa',
	ManageInboxAccountPanelTitle: 'Saapuneet',
	ManageInboxAssignTeamPlaceholder: 'Valitse tiimin jäsenet postilaatikkoon pääsyä varten',
	ManageInboxBasicInfoColor: 'Väri',
	ManageInboxBasicInfoDescription: 'Kuvaus',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Mihin sinä tai tiimisi käytät tätä postilaatikkoa?',
	ManageInboxBasicInfoName: 'Saapuneet-kansion nimi',
	ManageInboxBasicInfoNamePlaceholder: 'Esim. asiakastuki, järjestelmänvalvoja',
	ManageInboxConnectAppAlreadyConnectedError: 'Kanava, jota yritit yhdistää, on jo yhdistetty Carepatroniin',
	ManageInboxConnectAppConnect: 'Yhdistä',
	ManageInboxConnectAppConnectedInfo: 'Yhdistetty tiliin',
	ManageInboxConnectAppContinue: 'Jatkaa',
	ManageInboxConnectAppEmail: 'Sähköposti',
	ManageInboxConnectAppSignInWith: 'Kirjaudu sisään tunnuksella',
	ManageInboxConnectAppSubtitle:
		'Yhdistä sovelluksesi saumattomasti lähettääksesi, vastaanottaaksesi ja seurataksesi kaikkea viestintääsi yhdessä keskitetyssä paikassa.',
	ManageInboxNewInboxTitle: 'Uusi postilaatikko',
	ManagePlan: 'Hallitse suunnitelmaa',
	ManageProfile: 'Hallinnoi profiilia',
	ManageReferralsModalDescription:
		'Auta meitä levittämään sanaa terveydenhuoltoalustastamme ja ansaitsemaan palkintoja.',
	ManageReferralsModalTitle: 'Suosittele ystävääsi, ansaitse palkintoja!',
	ManageStaffRelationshipsAddButton: 'Hallitse suhteita',
	ManageStaffRelationshipsEmptyStateText: 'Suhteita ei lisätty',
	ManageStaffRelationshipsModalDescription:
		'Asiakkaiden valitseminen lisää uusia suhteita, kun taas valinnan poistaminen poistaa olemassa olevat suhteet.',
	ManageStaffRelationshipsModalTitle: 'Hallitse suhteita',
	ManageStatuses: 'Hallinnoi tiloja',
	ManageStatusesActiveStatusHelperText: 'Vähintään yksi aktiivinen tila vaaditaan',
	ManageStatusesDescription: 'Mukauta tilatarrojasi ja valitse värit työnkulkusi mukaisiksi.',
	ManageStatusesSuccessSnackbar: 'Tilat on päivitetty',
	ManageTags: 'Hallitse tunnisteita',
	ManageTaskAttendeeStatus: 'Hallitse ajanvaraustiloja',
	ManageTaskAttendeeStatusDescription: 'Muokkaa tapaamistilojen asetuksia työnkulkusi mukaiseksi.',
	ManageTaskAttendeeStatusHelperText: 'Vähintään yhden tilan on oltava',
	ManageTaskAttendeeStatusSubtitle: '<h1>Mukautetut tilat</h1>',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manuaalinen',
	ManualAppointment: 'Käsinvaraus',
	ManualPayment: 'Manuaalinen maksu',
	ManuallyTypeLocation: 'Kirjoita sijainti manuaalisesti',
	MapColumns: 'Kartta-sarakkeet',
	MappingRequired: 'Vaaditaan yhdistämistä',
	MarkAllAsRead: 'Merkitse kaikki luetuiksi',
	MarkAsCompleted: 'Merkitse valmiiksi',
	MarkAsManualSubmission: 'Merkitse lähetetyksi',
	MarkAsPaid: 'Merkitse maksetuksi',
	MarkAsRead: 'Merkitse luetuksi',
	MarkAsUnpaid: 'Merkitse maksamattomaksi',
	MarkAsUnread: 'Merkitse lukemattomaksi',
	MarkAsVoid: 'Merkitse mitättömäksi',
	Marker: 'Merkki',
	MarketingManager: 'Markkinointipäällikkö',
	MassageTherapist: 'Hierontaterapeutti',
	MassageTherapists: 'Hierontaterapeutit',
	MassageTherapy: 'Hierontaterapia',
	MaxBookingTimeDescription1: 'Asiakkaat voivat ajoittaa jopa',
	MaxBookingTimeDescription2: 'tulevaisuuteen',
	MaxBookingTimeLabel: '{timePeriod} etukäteen',
	MaxCapacity: 'Max kapasiteetti',
	Maximize: 'Maksimoida',
	MaximumAttendeeLimit: 'Enimmäisraja',
	MaximumBookingTime: 'Varausaika maksimissaan',
	MaximumBookingTimeError: 'Maksimikokoelma-aika ei saa ylittää {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Voit pienentää kerrallaan enintään {count} sivupaneelin. Jatkamalla suljetaan aikaisin pienennetty paneeli. Haluatko jatkaa?',
	MaximumMinimizedPanelsReachedTitle: 'Sinulla on liikaa paneeleja auki.',
	MechanicalEngineer: 'koneinsinööri',
	MediaGallery: 'Media galleria',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid-palveluntarjoajan numero',
	MedicalAssistant: 'Lääkärin assistentti',
	MedicalCoder: 'Lääketieteellinen koodaaja',
	MedicalDoctor: 'Lääkäri',
	MedicalIllustrator: 'Lääketieteellinen kuvittaja',
	MedicalInterpreter: 'Lääketieteellinen tulkki',
	MedicalTechnologist: 'Lääketieteellinen teknologia',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare-palveluntarjoajan numero',
	Medicine: 'Lääke',
	Medium: 'Keskikokoinen',
	Meeting: 'Kokous',
	MeetingEnd: 'Päätä kokous',
	MeetingEnded: 'Kokous päättyi',
	MeetingHost: 'Kokouksen isäntä',
	MeetingLowerHand: 'Alempi käsi',
	MeetingOpenChat: 'Avaa Chat',
	MeetingPersonRaisedHand: '{name} nosti kättään',
	MeetingRaiseHand: 'Nosta käsi',
	MeetingReady: 'Kokous valmiina',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Kokoukset',
	MemberId: 'Jäsentunnus',
	MentalHealth: 'Mielenterveys',
	MentalHealthPractitioners: 'Mielenterveysalan ammattilaiset',
	MentalHealthProfessional: 'Mielenterveyden ammattilainen',
	Merge: 'Yhdistää',
	MergeClientRecords: 'Yhdistä asiakastietojen tallenteet',
	MergeClientRecordsDescription: 'Asiakastietojen yhdistäminen yhdistää kaikki heidän tietoonsa, mukaan lukien:',
	MergeClientRecordsDescription2: 'Haluatko jatkaa yhdistämistä? Toimintaa ei voi peruuttaa.',
	MergeClientRecordsItem1: 'Huomautukset ja asiakirjat',
	MergeClientRecordsItem2: 'Ajanvaraukset',
	MergeClientRecordsItem3: 'Laskut',
	MergeClientRecordsItem4: 'Keskustelut',
	MergeClientsSuccess: 'Asiakkaan tiedot yhdistettiin onnistuneesti',
	MergeLimitExceeded: 'Voit voit yhdistää kerrallaan enintään 4 asiakasta.',
	Message: 'Viesti',
	MessageAttachments: '{total} liitettä',
	Method: 'Menetelmä',
	MfaAvailabilityDisclaimer:
		'MFA on käytettävissä vain sähköposti- ja salasanakirjautumisissa. Voit tehdä muutoksia MFA-asetuksiin kirjautumalla sisään sähköpostiosoitteellasi ja salasanallasi.',
	MfaDeviceLostPanelDescription:
		'Vaihtoehtoisesti voit vahvistaa henkilöllisyytesi vastaanottamalla koodin sähköpostitse.',
	MfaDeviceLostPanelTitle: 'Oletko hukannut MFA-laitteesi?',
	MfaDidntReceiveEmailCode: 'Etkö saanut koodia? Ota yhteyttä tukeen',
	MfaEmailOtpSendFailureSnackbar: 'Sähköpostin lähettäminen OTP epäonnistui.',
	MfaEmailOtpSentSnackbar: 'Koodi on lähetetty osoitteeseen {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Sähköpostin OTP:n vahvistaminen epäonnistui.',
	MfaHasBeenSetUpText: 'Olet määrittänyt MFA:n',
	MfaPanelDescription:
		'Suojaa tilisi ottamalla käyttöön Multi-Factor Authentication (MFA) lisäsuojausta varten. Vahvista henkilöllisyytesi toissijaisella menetelmällä estääksesi luvattoman käytön.',
	MfaPanelNotAuthorizedError: 'Sinun on kirjauduttava sisään käyttäjätunnuksella ',
	MfaPanelRecommendationDescription:
		'Kirjauduit äskettäin sisään jollakin muulla tapaa vahvistaa henkilöllisyytesi. Pidä tilisi turvassa harkitsemalla uuden MFA-laitteen asentamista.',
	MfaPanelRecommendationTitle: '<strong>Suositeltava:</strong> Päivitä MFA-laitteesi',
	MfaPanelTitle: 'Multi-Factor Authentication (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'Sinun on vahvistettava sähköpostiosoitteesi ennen kuin voit päivittää MFA-asetuksiasi.',
	MfaRecommendationBannerDescription:
		'Kirjauduit äskettäin sisään käyttämällä vaihtoehtoista tapaa vahvistaa henkilöllisyytesi. Pidä tilisi suojattuna harkitsemalla uuden MFA-laitteen asentamista.',
	MfaRecommendationBannerPrimaryAction: 'Määritä MFA',
	MfaRecommendationBannerTitle: 'Suositeltava',
	MfaRemovedSnackbarTitle: 'MFA on poistettu.',
	MfaSendEmailCode: 'Lähetä koodi',
	MfaVerifyIdentityLostDeviceButton: 'Menetin pääsyn MFA-laitteeseeni',
	MfaVerifyYourIdentityPanelDescription: 'Tarkista todennussovelluksestasi koodi ja kirjoita se alle.',
	MfaVerifyYourIdentityPanelTitle: 'Vahvista henkilöllisyytesi',
	MicCamWarningMessage:
		'Poista kameran ja mikrofonin esto napsauttamalla estettyjä kuvakkeita selaimen osoitepalkissa.',
	MicCamWarningTitle: 'Kamera ja mikrofoni ovat tukossa',
	MicOff: 'Mikrofoni on pois päältä',
	MicOn: 'Mikrofoni on päällä',
	MicSource: 'Mikrofonin lähde',
	MicWarningMessage: 'Mikrofonissasi on havaittu ongelma',
	Microphone: 'Mikrofoni',
	MicrophonePermissionBlocked: 'Mikrofonin käyttö estetty',
	MicrophonePermissionBlockedDescription: 'Päivitä mikrofonin käyttöoikeutesi aloittaaksesi tallennuksen.',
	MicrophonePermissionError: 'Jatka antamalla mikrofonin käyttöoikeus asetuksissasi',
	MicrophonePermissionPrompt: 'Salli mikrofonin käyttö jatkaaksesi',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlookin kalenterin väri',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Teams',
	MiddleEast: 'Lähi-idässä',
	MiddleName: 'Toinen nimi',
	MiddleNames: 'Toinen nimi',
	Midwife: 'Kätilö',
	Midwives: 'Kätilöt',
	Milan: 'Milano',
	MinBookingTimeDescription1: 'Asiakkaat eivät voi aikatauluttaa sisällä',
	MinBookingTimeDescription2: 'tapaamisen alkamisajasta',
	MinBookingTimeLabel: '{timePeriod} ennen tapaamista',
	MinCancellationTimeEditModeDescription: 'Aseta kuinka monta tuntia asiakas voi peruuttaa ilman rangaistusta',
	MinCancellationTimeUnset: 'Minimi peruutusaikaa ei ole asetettu',
	MinCancellationTimeViewModeDescription: 'Peruutusaika ilman sakkoja',
	MinMaxBookingTimeUnset: 'Aikaa ei ole asetettu',
	Minimize: 'Minimoida',
	MinimizeConfirmationDescription:
		'Sinulla on avoin pienennetty paneeli. Jos jatkat, se sulkeutuu ja saatat menettää tallentamattomia tietoja.',
	MinimizeConfirmationTitle: 'Sulje minimiin pienennetty paneeli?',
	MinimumBookingTime: 'Minimi varausaika',
	MinimumCancellationTime: 'Minimi peruutusaika',
	MinimumPaymentError: 'Verkkopankkimaksuille on asetettu {minimumAmount} minimimaksu.',
	MinuteAbbreviated: 'min',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minuutti',
	MinutesPlural: '{age, plural, one {# minuutti} other {# minuuttia}}',
	MiscellaneousInformation: 'Sekalaista tietoa',
	MissingFeatures: 'Puuttuvat ominaisuudet',
	MissingPaymentMethod: 'Lisää tilaukseesi maksutapa, jos haluat lisätä henkilökunnan jäseniä.',
	MobileNumber: 'Matkapuhelinnumero',
	MobileNumberOptional: 'Matkapuhelinnumero (valinnainen)',
	Modern: 'Moderni',
	Modifiers: 'Muokkaimet',
	ModifiersPlaceholder: 'Muokkaimet',
	Monday: 'maanantai',
	Month: 'Kuukausi',
	Monthly: 'Kuukausittain',
	MonthlyCost: 'Kuukausikulut',
	MonthlyOn: 'Kuukausi {date}',
	MonthsPlural: '{age, plural, one {# kuukausi} other {# kuukautta}}',
	More: 'Lisää',
	MoreActions: 'Lisää toimintoja',
	MoreSettings: 'Lisää asetuksia',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Yleisimmin käytetty',
	MostDownloaded: 'Useimmat ladatut',
	MostPopular: 'Suosituin',
	Mother: 'Äiti',
	MotherInLaw: 'Anoppi',
	MoveDown: 'Siirry alas',
	MoveInboxConfirmationDescription:
		'Tämän sovellusliitäntä-asetuksen uudelleenmääritys poistaa sen <strong>{currentInboxName}</strong>-postilaatikosta.',
	MoveTemplateToFolder: 'Siirrä `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} siirrettiin {folderTitle}:een.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Siirretty onnistuneesti oletusottokansion.',
	MoveTemplateToNewFolder: 'Luo uusi kansio, johon tämä kohde voidaan siirtää.',
	MoveToChosenFolder: 'Valitse kansio, johon tämä kohde siirretään. Voit tarvittaessa luoda uuden kansion.',
	MoveToFolder: 'Siirrä kansioon',
	MoveToInbox: 'Siirrä postilaatikkoon',
	MoveToNewFolder: 'Siirry uuteen kansioon',
	MoveToSelectedFolder:
		'Siirtämisen jälkeen kohde järjestetään valitun kansion alle ja se ei enää näy nykyisessä sijainnissaan.',
	MoveUp: 'Siirrä ylös',
	MultiSpeciality: 'Monipuolinen erikoisuus',
	MultipleChoiceFormPrimaryText: 'Monivalinta',
	MultipleChoiceFormSecondaryText: 'Valitse useita vaihtoehtoja',
	MultipleChoiceGridFormPrimaryText: 'Monivalintaruudukko',
	MultipleChoiceGridFormSecondaryText: 'Valitse vaihtoehdot matriisista',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Musiikkiterapeutti',
	MustContainOneLetterError: 'Täytyy sisältää vähintään yksi kirjain',
	MustEndWithANumber: 'Sen tulee päättyä numeroon',
	MustHaveAtLeastXItems: 'Oltava vähintään {count, plural, one {# tuote} other {# tuotetta}}',
	MuteAudio: 'Mykistä ääni',
	MuteEveryone: 'Mykistä kaikki',
	MyAvailability: 'Saatavuus',
	MyGallery: 'Oma galleria',
	MyPortal: 'Oma portaali',
	MyRelationships: 'Minun ihmissuhteeni',
	MyTemplates: 'Joukkueen mallit',
	MyofunctionalTherapist: 'Myofunktionaalinen terapeutti',
	NCalifornia: 'Pohjois-Kalifornia',
	NPI: 'NPI',
	NVirginia: 'Pohjois-Virginia',
	Name: 'Nimi',
	NameIsRequired: 'Nimi vaaditaan',
	NameMustNotBeAWebsite: 'Nimi ei saa olla verkkosivusto',
	NameMustNotBeAnEmail: 'Nimi ei saa olla sähköpostiosoite',
	NameMustNotContainAtSign: 'Nimi ei saa sisältää @-merkkiä',
	NameMustNotContainHTMLTags: 'Nimi ei saa sisältää HTML-tageja',
	NameMustNotContainSpecialCharacters: 'Nimi ei saa sisältää erikoismerkkejä',
	NameOnCard: 'Nimi kortissa',
	NationalProviderId: 'Kansallinen palveluntarjoajan tunniste (NPI)',
	NaturopathicDoctor: 'Naturopaattinen lääkäri',
	NavigateToPersonalSettings: 'Profiili',
	NavigateToSubscriptionSettings: 'Tilausasetukset',
	NavigateToWorkspaceSettings: 'Työtilan asetukset',
	NavigateToYourTeam: 'Hallitse tiimiä',
	NavigationDrawerBilling: 'Laskutus',
	NavigationDrawerBillingInfo: 'Laskutustiedot, laskut ja Stripe',
	NavigationDrawerCommunication: 'Viestintä',
	NavigationDrawerCommunicationInfo: 'Ilmoitukset ja mallit',
	NavigationDrawerInsurance: 'Vakuutus',
	NavigationDrawerInsuranceInfo: 'Vakuutuksen maksajat ja korvaukset',
	NavigationDrawerInvoices: 'Laskutus',
	NavigationDrawerPersonal: 'Oma profiili',
	NavigationDrawerPersonalInfo: 'Henkilötietosi',
	NavigationDrawerProfile: 'Profiili',
	NavigationDrawerProviderSettings: 'Asetukset',
	NavigationDrawerScheduling: 'Ajoitus',
	NavigationDrawerSchedulingInfo: 'Palvelun tiedot ja varaukset',
	NavigationDrawerSettings: 'Asetukset',
	NavigationDrawerTemplates: 'Mallit',
	NavigationDrawerTemplatesV2: 'Mallit V2',
	NavigationDrawerTrash: 'Roskakori',
	NavigationDrawerTrashInfo: 'Palauta poistetut kohteet',
	NavigationDrawerWorkspace: 'Työtilan asetukset',
	NavigationDrawerWorkspaceInfo: 'Tilaus- ja työtilatiedot',
	NegativeBalanceNotSupported: 'Tilin negatiivisia saldoja ei tueta',
	Nephew: 'Veljenpoika',
	NetworkQualityFair: 'Reilu yhteys',
	NetworkQualityGood: 'Hyvä yhteys',
	NetworkQualityPoor: 'Huono yhteys',
	Neurologist: 'Neurologi',
	Never: 'Koskaan',
	New: 'Uusi',
	NewAppointment: 'Uusi tapaaminen',
	NewClaim: 'Uusi väite',
	NewClient: 'Uusi asiakas',
	NewClientNextStepsModalAddAnotherClient: 'Lisää toinen asiakas',
	NewClientNextStepsModalBookAppointment: 'Varaa tapaaminen',
	NewClientNextStepsModalBookAppointmentDescription: 'Varaa tuleva tapaaminen tai luo tehtävä.',
	NewClientNextStepsModalCompleteBasicInformation: 'Täydellinen asiakasrekisteri',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'Lisää asiakastiedot ja tallenna seuraavat vaiheet.',
	NewClientNextStepsModalCreateInvoice: 'Luo lasku',
	NewClientNextStepsModalCreateInvoiceDescription: 'Lisää asiakkaan maksutiedot tai luo lasku.',
	NewClientNextStepsModalCreateNote: 'Luo muistiinpano tai lataa asiakirja',
	NewClientNextStepsModalCreateNoteDescription: 'Tallenna asiakkaan muistiinpanot ja asiakirjat.',
	NewClientNextStepsModalDescription:
		'Tässä on joitain toimia, jotka sinun on suoritettava, kun olet luonut asiakastietueen.',
	NewClientNextStepsModalSendIntake: 'Lähetä sisäänotto',
	NewClientNextStepsModalSendIntakeDescription:
		'Kerää asiakastiedot ja lähetä lisälomakkeita täytettäväksi ja allekirjoitettavaksi.',
	NewClientNextStepsModalSendMessage: 'Lähetä viesti',
	NewClientNextStepsModalSendMessageDescription: 'Kirjoita ja lähetä viesti asiakkaallesi.',
	NewClientNextStepsModalTitle: 'Seuraavat vaiheet',
	NewClientSuccess: 'Uuden asiakkaan luominen onnistui',
	NewClients: 'Uusia asiakkaita',
	NewConnectedApp: 'Uusi yhdistetty sovellus',
	NewContact: 'Uusi kontakti',
	NewContactNextStepsModalAddRelationship: 'Lisää suhde',
	NewContactNextStepsModalAddRelationshipDescription: 'Liitä tämä yhteyshenkilö liittyviin asiakkaisiin tai ryhmiin.',
	NewContactNextStepsModalBookAppointment: 'Varaa aika',
	NewContactNextStepsModalBookAppointmentDescription: 'Varaa tuleva aika tai luo tehtävä.',
	NewContactNextStepsModalCompleteProfile: 'Täydellinen profiili',
	NewContactNextStepsModalCompleteProfileDescription: 'Lisää yhteystiedot ja tallenna seuraavat vaiheet.',
	NewContactNextStepsModalCreateNote: 'Luo muistiinpano tai lataa asiakirja',
	NewContactNextStepsModalCreateNoteDescription: 'Tallenna asiakkaan muistiinpanoja ja dokumentaatiota.',
	NewContactNextStepsModalDescription:
		'Tässä on joitakin toimia, joita voit tehdä nyt, kun olet luonut yhteystiedon.',
	NewContactNextStepsModalInviteToPortal: 'Kutsu portaaliin',
	NewContactNextStepsModalInviteToPortalDescription: 'Lähetä kutsu portaaliin.',
	NewContactNextStepsModalTitle: 'Seuraavat vaiheet',
	NewContactSuccess: 'Uuden kontaktin luominen onnistui',
	NewDateOverrideButton: 'Uuden päivämäärän ohitus',
	NewDiagnosis: 'Lisää diagnoosi',
	NewField: 'Uusi kenttä',
	NewFolder: 'Uusi kansio',
	NewInvoice: 'Uusi lasku',
	NewLocation: 'Uusi sijainti',
	NewLocationFailure: 'Uuden sijainnin luominen epäonnistui',
	NewLocationSuccess: 'Uuden sijainnin luominen onnistui',
	NewManualPayer: 'Uusi manuaalinen maksaja',
	NewNote: 'Uusi muistiinpano',
	NewNoteCreated: 'Uuden muistiinpanon luominen onnistui',
	NewPassword: 'Uusi salasana',
	NewPayer: 'Uusi maksaja',
	NewPaymentMethod: 'Uusi maksutapa',
	NewPolicy: 'Uusi politiikka',
	NewRelationship: 'Uusi suhde',
	NewReminder: 'Uusi muistutus',
	NewSchedule: 'Uusi aikataulu',
	NewSection: 'Uusi jakso',
	NewSectionOld: 'Uusi osio [OLD]',
	NewSectionWithGrid: 'Uusi osa ruudukolla',
	NewService: 'Uusi palvelu',
	NewServiceFailure: 'Uuden palvelun luominen epäonnistui',
	NewServiceSuccess: 'Uuden palvelun luominen onnistui',
	NewStatus: 'Uusi tila',
	NewTask: 'Uusi tehtävä',
	NewTaxRate: 'Uusi veroprosentti',
	NewTeamMemberNextStepsModalAssignClients: 'Määritä asiakkaat',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Assign specific clients to your team member.',
	NewTeamMemberNextStepsModalAssignServices: 'Määritä palvelut',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Hallinnoivat heille osoitettuja palveluja ja säätelevät hinnoittelua tarpeen mukaan.',
	NewTeamMemberNextStepsModalBookAppointment: 'Varaa aika',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Varaa tuleva aika tai luo tehtävä.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Täydellinen profiili',
	NewTeamMemberNextStepsModalCompleteProfileDescription: 'Lisää tietoja tiimijäsenestäsi täydentääksesi profiiliaan.',
	NewTeamMemberNextStepsModalDescription:
		'Tässä on muutamia toimia, jotka sinun on nyt tehtävä, kun olet luonut tiimijäsenen.',
	NewTeamMemberNextStepsModalEditPermissions: 'Muokkausluvat',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Säädä heidän käyttöoikeustasojaan varmistaaksesi, että heillä on oikeat käyttöoikeudet.',
	NewTeamMemberNextStepsModalSetAvailability: 'Aseta saatavuus',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Määritä heidän saatavuutensa aikataulujen luomiseksi.',
	NewTeamMemberNextStepsModalTitle: 'Seuraavat vaiheet',
	NewTemplateFolderDescription: 'Luo uusi kansio dokumentaatiosi järjestämistä varten.',
	NewUIUpdateBannerButton: 'Lataa sovellus uudelleen',
	NewUIUpdateBannerTitle: 'Uusi päivitys on valmis!',
	NewZealand: 'Uusi-Seelanti',
	Newest: 'Uusin',
	NewestUnreplied: 'Uusin vastaamaton',
	Next: 'Seuraavaksi',
	NextInvoiceIssueDate: 'Seuraavan laskun laskutuspäivä',
	NextNDays: 'Seuraavat {number} päivää',
	Niece: 'Veljentytär',
	No: 'Ei',
	NoAccessGiven: 'Ei pääsyä annettu',
	NoActionConfigured: 'Toimintoa ei ole määritetty',
	NoActivePolicies: 'Ei aktiivisia käytäntöjä',
	NoActiveReferrals: 'Sinulla ei ole aktiivisia viittauksia',
	NoAppointmentsFound: 'Aikoja ei ole löytynyt',
	NoAppointmentsHeading: 'Hallitse asiakkaiden tapaamisia ja aktiviteetteja',
	NoArchivedPolicies: 'Ei arkistoituja käytäntöjä',
	NoAvailableTimes: 'Aikoja ei löytynyt.',
	NoBillingItemsFound: 'Laskutuskohteita ei löytynyt',
	NoCalendarsSynced: 'Kalentereita ei ole synkronoitu',
	NoClaimsFound: 'Löytyi 0 vaatimusta',
	NoClaimsHeading: 'Virtaviivaista korvaushakemusten jättäminen',
	NoClientsHeading: 'Kokoa asiakastietosi',
	NoCompletedReferrals: 'Sinulla ei ole täydellisiä viittauksia',
	NoConnectionsHeading: 'Virtaviivaista asiakasviestintääsi',
	NoContactsGivenAccess: 'Asiakkaille tai yhteyshenkilöille ei ole annettu pääsyä tähän muistiinpanoon',
	NoContactsHeading: 'Pysy yhteydessä niihin, jotka tukevat harjoitteluasi',
	NoCopayOrCoinsurance: 'Ei omavastuuta tai yhteisvakuutusta',
	NoCustomServiceSchedule: 'Mukautettua aikataulua ei ole asetettu – saatavuus riippuu tiimin jäsenen saatavuudesta',
	NoDescription: 'Ei kuvausta',
	NoDocumentationHeading: 'Luo ja tallenna muistiinpanoja turvallisesti',
	NoDuplicateRecordsHeading: 'Asiakasrekisterissäsi ei ole kaksoiskappaleita',
	NoEffect: 'Ei vaikutusta',
	NoEnrolmentProfilesFound: 'Rekisteröintiprofiileja ei löytynyt',
	NoGlossaryItems: 'Ei sanastokohteita',
	NoInvitedReferrals: 'Sinulla ei ole kutsuttuja suosituksia',
	NoInvoicesFound: 'Laskuja ei löytynyt',
	NoInvoicesHeading: 'Automatisoi laskutus ja maksut',
	NoLimit: 'Ei rajaa',
	NoLocationsFound: 'Paikkoja ei löytynyt',
	NoLocationsWillBeAdded: 'Paikkoja ei lisätä.',
	NoNoteFound: 'Löytyi ei yhtään muistiinpanoa',
	NoPaymentMethods: 'Sinulla ei ole tallennettuja maksutapoja, voit lisätä sellaisen maksua tehdessäsi.',
	NoPermissionError: 'Sinulla ei ole lupaa',
	NoPermissions: 'Sinulla ei ole lupaa tarkastella tätä sivua',
	NoPolicy: 'Peruutusehtoja ei ole lisätty',
	NoRecordsHeading: 'Mukauta asiakastietosi',
	NoRecordsToDisplay: 'Ei näytettäviä {resource}ja',
	NoRelationshipsHeading: 'Pysy yhteydessä niihin, jotka tukevat asiakastasi',
	NoRemindersFound: 'Muistutuksia ei löytynyt',
	NoResultsFound: 'Tuloksia ei löytynyt',
	NoResultsFoundDescription: 'Emme löydä hakuasi vastaavia kohteita',
	NoServicesAdded: 'Ei palveluita lisätty',
	NoServicesApplied: 'Palveluita ei ole käytössä',
	NoServicesWillBeAdded: 'Palveluita ei lisätä.',
	NoTemplate: 'Sinulla ei ole tallennettuja harjoitusmalleja',
	NoTemplatesHeading: 'Luo omat mallit',
	NoTemplatesInFolder: 'Ei malleja tässä kansiossa',
	NoTitle: 'Ei otsikkoa',
	NoTrashItemsHeading: 'Poistettua kohdetta ei löytynyt',
	NoTriggerConfigured: 'Liipaisinta ei ole määritetty',
	NoUnclaimedItemsFound: 'Lunastamattomia tuotteita ei löytynyt.',
	NonAiTemplates: 'Ei-AI-mallit',
	None: 'Ei mitään',
	NotAvailable: 'Ei saatavilla',
	NotCovered: 'Ei katettu',
	NotFoundSnackbar: 'Resurssia ei löydy.',
	NotRequiredField: 'Ei vaadita',
	Note: 'Huom',
	NoteDuplicateSuccess: 'Muistiinpanon kopiointi onnistui',
	NoteEditModeViewSwitcherDescription: 'Luo ja muokkaa muistiinpanoa',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} lähetti {noteTitle}-lomakkeen',
	NoteLockSuccess: '{title} on lukittu',
	NoteModalAttachmentButton: 'Lisää liitteitä',
	NoteModalPhotoButton: 'Lisää/ota kuvia',
	NoteModalTrascribeButton: 'Litteroi live-ääni',
	NoteResponderModeViewSwitcherDescription: 'Lähetä lomakkeita ja tarkista vastaukset',
	NoteResponderModeViewSwitcherTooltipTitle: 'Vastaa ja lähetä lomakkeita asiakkaidesi puolesta',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Täytä ja lähetä lomakkeita asiakkaana',
	NoteUnlockSuccess: '{title} on avattu',
	NoteViewModeViewSwitcherDescription: 'Vain katseluoikeus',
	Notes: 'Huomautuksia',
	NotesAndForms: 'Muistiinpanot ja lomakkeet',
	NotesCategoryDescription: 'Asiakaskohtaamisten dokumentointiin',
	NothingToSeeHere: 'Täällä ei ole mitään nähtävää',
	Notification: 'Ilmoitus',
	NotificationIgnoredMessage: 'Kaikki {notificationType}-ilmoitukset jätetään huomiotta',
	NotificationRestoredMessage: 'Kaikki {notificationType}-ilmoitukset palautettu',
	NotificationSettingBillingDescription: 'Saat ilmoituksia asiakkaan maksupäivityksistä ja muistutuksista.',
	NotificationSettingBillingTitle: 'Laskutus ja maksu',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} vain} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Saat ilmoituksia asiakkaan maksupäivityksistä ja muistutuksista.',
	NotificationSettingClientDocumentationTitle: 'Asiakirjat ja asiakkaat',
	NotificationSettingCommunicationsDescription:
		'Vastaanota ilmoituksia postilaatikosta ja päivityksistä yhdistetyiltä kanavilta',
	NotificationSettingCommunicationsTitle: 'Viestintä',
	NotificationSettingEmail: 'Sähköposti',
	NotificationSettingInApp: 'Sovelluksessa',
	NotificationSettingPanelDescription:
		'Valitse ilmoitukset, jotka haluat vastaanottaa toiminnoista ja suosituksista.',
	NotificationSettingPanelTitle: 'Ilmoitusasetukset',
	NotificationSettingSchedulingDescription:
		'Vastaanota ilmoituksia, kun tiimin jäsen tai asiakas varaa, muuttaa tai peruuttaa tapaamisen.',
	NotificationSettingSchedulingTitle: 'Aikataulutus',
	NotificationSettingUpdateSuccess: 'Ilmoitusasetukset päivitetty onnistuneesti',
	NotificationSettingWhereYouReceiveNotifications: 'Missä haluat vastaanottaa nämä ilmoitukset',
	NotificationSettingWorkspaceDescription:
		'Saat ilmoituksia järjestelmän muutoksista, ongelmista, tietojen siirroista ja tilausmuistutuksista.',
	NotificationSettingWorkspaceTitle: 'Työtila',
	NotificationTemplateUpdateFailed: 'Ilmoitusmallin päivittäminen epäonnistui',
	NotificationTemplateUpdateSuccess: 'Ilmoitusmallin päivitys onnistui',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Haluatko lähettää peruutusilmoituksen osallistujille sähköpostitse?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Lähetä peruutus',
	NotifyAttendeesOfTaskConfirmationModalDescription: 'Haluatko lähettää vahvistusviestin osallistujille?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Lähetä vahvistus',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Haluatko lähettää peruutussähköposteja osallistujille?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {ei} other {eivät}}  omista sähköpostiosoitetta, joten he eivät saa automaattisia ilmoituksia ja muistutuksia.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {ei ole} other {eivät ole}} sähköpostiosoitetta, joten he eivät saa automaattisia ilmoituksia ja muistutuksia.',
	NotifyAttendeesOfTaskModalTitle: 'Haluatko lähettää ilmoituksen osallistujille?',
	NotifyAttendeesOfTaskSnackbar: 'Lähetetään ilmoitusta',
	NuclearMedicineTechnologist: 'Ydinlääketieteen teknologi',
	NumberOfClaims: '{number, plural, one {# Vaatimus} other {# Vaatimukset}}',
	NumberOfClients: '{number, plural, one {# Asiakas} other {# Asiakkaat}}',
	NumberOfContacts: '{number, plural, one {# Yhteyshenkilö} other {# Yhteyshenkilöt}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {merkintä} other {merkintää}} löytyi',
	NumberOfErrors: '{count, plural, one {# virhe} other {# virheitä}}',
	NumberOfInvoices: '{number, plural, one {# Lasku} other {# Laskut}}',
	NumberOfLineitemsToCredit:
		'Sinulla on <mark>{count} {count, plural, one {rivi} other {rivit}}</mark> hyvitystä varten.',
	NumberOfPayments: '{number, plural, one {# Maksu} other {# Maksuja}}',
	NumberOfRelationships: '{number, plural, one {# Suhde} other {# Suhteet}}',
	NumberOfResources: '{number, plural, one {# Resurssi} other {# Resurssit}}',
	NumberOfTeamMembers: '{number, plural, one {# Tiimijäsen} other {# Tiimijäsenet}}',
	NumberOfTrashItems: '{number, plural, one {# tuote} other {# tuotetta}}',
	NumberOfUninvoicedAmounts:
		'Sinulla on <mark>{count} laskuttamatonta {count, plural, one {summa} other {summat}}</mark> laskutettavaksi',
	NumberedList: 'Numeroitu luettelo',
	Nurse: 'Sairaanhoitaja',
	NurseAnesthetist: 'Sairaanhoitaja anestesialääkäri',
	NurseAssistant: 'Sairaanhoitajan assistentti',
	NurseEducator: 'Sairaanhoitajan kouluttaja',
	NurseMidwife: 'Sairaanhoitaja kätilö',
	NursePractitioner: 'Sairaanhoitaja',
	Nurses: 'Sairaanhoitajat',
	Nursing: 'Hoitotyö',
	Nutritionist: 'Ravitsemusterapeutti',
	Nutritionists: 'Ravitsemusasiantuntijat',
	ObstetricianOrGynecologist: 'Synnytyslääkäri/gynekologi',
	Occupation: 'Ammatti',
	OccupationalTherapist: 'Toimintaterapeutti',
	OccupationalTherapists: 'Toimintaterapeutit',
	OccupationalTherapy: 'Toimintaterapia',
	Occurrences: 'Tapahtumia',
	Of: '/',
	Ohio: 'Ohio',
	OldPassword: 'Vanha salasana',
	OlderMessages: '{count} vanhempaa viestiä',
	Oldest: 'Vanhin',
	OldestUnreplied: 'Vanhin vastaamaton',
	On: 'päällä',
	OnboardingBusinessAgreement:
		'Olen sekä itseni että yrityksen puolesta samaa mieltä sopimuksesta {businessAssociateAgreement}.',
	OnboardingLoadingOccupationalTherapist: '<mark>Toimintaterapeutit</mark> neljäsosa Carepatronin asiakkaistamme',
	OnboardingLoadingProfession:
		'Meillä on paljon <mark>{profession}</mark>, jotka käyttävät ja menestyvät Carepatronissa.',
	OnboardingLoadingPsychologist: '<mark>Psykologit</mark> yli puolet asiakkaistamme Carepatronissa',
	OnboardingLoadingSubtitleFive: 'Missiomme on tehdä<mark> terveydenhuollon ohjelmistot saatavilla</mark> kaikille.',
	OnboardingLoadingSubtitleFour:
		'<mark>Yksinkertaistettu terveysohjelmisto</mark> yli 10 000 ihmiselle maailmanlaajuisesti.',
	OnboardingLoadingSubtitleThree: 'Tallentaa<mark> 1 päivä viikossa</mark> hallintotehtävistä Carepatronin avulla.',
	OnboardingLoadingSubtitleTwo: 'Tallentaa<mark> 2 tuntia</mark> päivittäin hallintotehtävissä Carepatronin avulla.',
	OnboardingReviewLocationOne: 'Holland Parkin mielenterveyskeskus',
	OnboardingReviewLocationThree: 'Sairaanhoitaja, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Life House -klinikka',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron on erittäin intuitiivinen käyttää. Se auttaa meitä hoitamaan käytäntömme niin hyvin, että emme enää tarvitse edes järjestelmänvalvojatiimiä."',
	OnboardingReviewThree:
		'"Se on paras käytäntö, jota olen käyttänyt sekä ominaisuuksien että kustannusten suhteen. Siinä on kaikki mitä tarvitsen liiketoiminnan kasvattamiseen."',
	OnboardingReviewTwo:
		'"Rakastan myös Carpatron-sovellusta. Se auttaa minua seuraamaan asiakkaitani ja työskentelemään liikkeellä ollessani."',
	OnboardingTitle: `Mennään<mark> tietää
 sinä parempi</mark>`,
	Oncologist: 'Onkologi',
	Online: 'verkossa',
	OnlineBookingColorTheme: 'Online-varaus väriteema',
	OnlineBookings: 'Online-varaukset',
	OnlineBookingsHelper: 'Valitse milloin online-varauksia voidaan tehdä ja minkä tyyppisten asiakkaiden mukaan',
	OnlinePayment: 'Online maksu',
	OnlinePaymentSettingCustomInfo: 'Tämän palvelun verkkomaksuasetukset poikkeavat yleisistä varausasetuksista.',
	OnlinePaymentSettings: 'Verkkomaksuasetukset',
	OnlinePaymentSettingsInfo:
		'Kerää maksut palveluista verkkovarauksen yhteydessä turvataksesi ja tehostaaksesi maksuja',
	OnlinePaymentSettingsPaymentsDisabled:
		'Maksuja ei ole otettu käyttöön, joten niitä ei voi kerätä verkossa varattaessa. Tarkista maksuasetuksesi ja ota maksut käyttöön.',
	OnlinePaymentSettingsStripeNote: '{action} vastaanottaa online-varauksia ja tehostaa maksuprosessiasi',
	OnlinePaymentsNotSupportedForCurrency: 'Verkkopankkimaksut eivät ole käytettävissä {valuutassa}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'Valitettavasti verkkomaksuja ei tueta tässä valuutassa',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: 'Valitettavasti verkkomaksuja ei vielä tueta maassasi',
	OnlineScheduling: 'Online-aikataulu',
	OnlyVisibleToYou: 'Näkyy vain sinulle',
	OnlyYou: 'Vain sinä',
	OnsetDate: 'Aloituspäivä',
	OnsetOfCurrentSymptomsOrIllness: 'Nykyisten oireiden tai sairauden alkaminen',
	Open: 'Avata',
	OpenFile: 'Avaa tiedosto',
	OpenSettings: 'Avaa asetukset',
	Ophthalmologist: 'Silmälääkäri',
	OptimiseTelehealthCalls: 'Optimoi Telehealth-puhelusi',
	OptimizeServiceTimes: 'Optimoi palveluajat',
	Options: 'Vaihtoehdot',
	Optometrist: 'Optometristi',
	Or: 'tai',
	OrAttachSingleFile: 'liitä tiedosto',
	OrDragAndDrop: 'tai vedä ja pudota',
	OrderBy: 'Tilauksen mukaan',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisaatio tai henkilö',
	OrganizationPlanInclusion1: 'Lisäkäyttöoikeudet',
	OrganizationPlanInclusion2: 'Ilmainen asiakastietojen tuontituki',
	OrganizationPlanInclusion3: 'Omistautunut menestysjohtaja',
	OrganizationPlanInclusionHeader: 'Kaikki ammattitaidolla, plus...',
	Orthodontist: 'Oikomislääkäri',
	Orthotist: 'Ortopedi',
	Other: 'Muut',
	OtherAdjustments: 'Muut säädöt',
	OtherAdjustmentsTableEmptyState: 'Säätöjä ei löytynyt',
	OtherEvents: 'Muut tapahtumat',
	OtherId: 'Muu tunnus',
	OtherIdQualifier: 'Muu tunnuksen tarkenne',
	OtherPaymentMethod: 'Muu maksutapa',
	OtherPlanMessage:
		'Pidä käytäntösi tarpeet hallinnassa. Tarkista nykyinen suunnitelmasi, seuraa käyttöä ja tutustu päivitysvaihtoehtoihin, jotta voit avata lisää ominaisuuksia tiimisi kasvaessa.',
	OtherPolicy: 'Muut vakuutukset',
	OtherProducts: 'Mitä muita tuotteita tai työkaluja käytät?',
	OtherServices: 'Muut palvelut',
	OtherTemplates: 'Muut mallit',
	Others: 'muut',
	OthersPeople: `{n, plural, 		one {1 muu henkilö}
		other {# muuta ihmistä}
	}`,
	OurResearchTeamReachOut:
		'Voiko tutkimusryhmämme ottaa yhteyttä saadakseen lisätietoja siitä, kuinka Carepatron olisi voinut olla parempi tarpeisiisi?',
	OutOfOffice: 'Poissa toimistosta',
	OutOfOfficeColor: 'Toimiston ulkopuolinen väri',
	OutOfOfficeHelper: 'Jotkut valitut tiimin jäsenet ovat poissa virastaan',
	OutsideLabCharges: 'Laboratoriomaksujen ulkopuolella',
	OutsideOfWorkingHours: 'Työajan ulkopuolella',
	OutsideWorkingHoursHelper: 'Jotkut valitut tiimin jäsenet ovat työajan ulkopuolella',
	Overallocated: 'Ylimääräinen',
	OverallocatedPaymentDescription: `Tämä maksu on kohdistettu liikaa laskutettaviin tuotteisiin.
 Lisää määräraha maksamattomille tuotteille tai myönnä hyvitys tai hyvitys.`,
	OverallocatedPaymentTitle: 'Ylimääräinen maksu',
	OverdueTerm: 'Erääntynyt määrä (päivää)',
	OverinvoicedAmount: 'Ylilaskettu summa',
	Overpaid: 'Ylimaksettu',
	OverpaidAmount: 'Ylimaksettu summa',
	Overtime: 'ylitöitä',
	Owner: 'Omistaja',
	POS: 'POS',
	POSCode: 'POS-koodi',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Tallentamattomat muutokset menetetään. Haluatko silti poistua?',
	PageBlockerTitle: 'Hylätä muutokset?',
	PageFormat: 'Sivun muoto',
	PageNotFound: 'Sivua ei löydy',
	PageNotFoundDescription: 'Sinulla ei ole enää pääsyä tälle sivulle tai sitä ei löydy',
	PageUnauthorised: 'Luvaton pääsy',
	PageUnauthorisedDescription: 'Sinulla ei ole lupaa käyttää tätä sivua',
	Paid: 'Maksettu',
	PaidAmount: 'Maksettu summa',
	PaidAmountMinimumValueError: 'Maksetun summan on oltava suurempi kuin 0',
	PaidAmountRequiredError: 'Maksettu summa vaaditaan',
	PaidItems: 'Maksulliset kohteet',
	PaidMultiple: 'Maksettu',
	PaidOut: 'Maksettu',
	ParagraphStyles: 'Kappaletyylit',
	Parent: 'Vanhempi',
	Paris: 'Pariisi',
	PartialRefundAmount: 'Osittain hyvitetty ({amount} jäljellä)',
	PartiallyFull: 'Osittain täynnä',
	PartiallyPaid: 'Osittain maksettu',
	PartiallyRefunded: 'Osittain palautettu',
	Partner: 'Kumppani',
	Password: 'Salasana',
	Past: 'Mennyt',
	PastDateOverridesEmpty: 'Päivämääräsi ohitukset näkyvät täällä heti, kun tapahtuma on ohi',
	Pathologist: 'Patologi',
	Patient: 'Potilas',
	Pause: 'Tauko',
	Paused: 'Keskeytetty',
	Pay: 'Maksaa',
	PayMonthly: 'Maksa kuukausittain',
	PayNow: 'Maksa nyt',
	PayValue: 'Maksa {showPrice, select, true {{price}} other {nyt}}',
	PayWithOtherCard: 'Maksa toisella kortilla',
	PayYearly: 'Maksa vuosittain',
	PayYearlyPercentOff: 'Maksa vuosittain <mark>{percent}% alennus</mark>',
	Payer: 'Maksaja',
	PayerClaimId: 'Maksajan vaatimuksen ID',
	PayerCoverage: 'Kattavuus',
	PayerDetails: 'Maksajan tiedot',
	PayerDetailsDescription: 'Tarkastele tilillesi lisättyjä maksajien tietoja ja hallinnoi ilmoittautumista.',
	PayerID: 'Maksajan tunnus',
	PayerId: 'Maksajan tunnus',
	PayerName: 'Maksajan nimi',
	PayerPhoneNumber: 'Maksajan puhelinnumero',
	Payers: 'Maksajat',
	Payment: 'Maksu',
	PaymentAccountUpdated: 'Tilisi on päivitetty!',
	PaymentAccountUpgraded: 'Tilisi on päivitetty!',
	PaymentAmount: 'Maksumaksu',
	PaymentDate: 'Maksupäivä',
	PaymentDetails: 'Maksutiedot',
	PaymentForUsersPerMonth: 'Maksaminen {billedUsers, plural, one {# käyttäjälle} other {# käyttäjille}} kuukaudessa',
	PaymentInfoFormPrimaryText: 'Maksutiedot',
	PaymentInfoFormSecondaryText: 'Kerää maksutiedot',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Tämä lasku on jo maksettu.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Tätä laskua käsitellään jo.',
	PaymentIntentAmountMismatchSnackbar: 'Laskun kokonaissummaa on muutettu. Tarkista muutokset ennen maksamista.',
	PaymentIntentSyncTimeoutSnackbar:
		'Maksusi onnistui, mutta aikakatkaisu tapahtui. Päivitä sivu ja jos maksusi ei näy, ota yhteyttä tukeen.',
	PaymentMethod: 'Maksutapa',
	PaymentMethodDescription:
		'Lisää ja hallinnoi harjoitusmaksutapaasi yksinkertaistaaksesi tilauksen laskutusprosessia.',
	PaymentMethodLabelBank: 'pankkitili',
	PaymentMethodLabelCard: 'kortti',
	PaymentMethodLabelFallback: 'maksutapa',
	PaymentMethodRequired: 'Lisää maksutapa ennen tilausten vaihtamista',
	PaymentMethods: 'Maksutavat',
	PaymentProcessing: 'Maksun käsittely!',
	PaymentProcessingFee: 'Maksuun sisältyy {amount} käsittelymaksu',
	PaymentReports: 'Maksutiedot (ERA)',
	PaymentSettings: 'Maksuasetukset',
	PaymentSuccessful: 'Maksu onnistui!',
	PaymentType: 'Maksutyyppi',
	Payments: 'Maksut',
	PaymentsAccountDisabledNotificationSubject: `Verkkop maksut {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} kautta on poistettu käytöstä.
Tarkista maksuasetuksesi ottaaksesi maksut käyttöön.`,
	PaymentsEmptyStateDescription: 'Maksuja ei löytynyt.',
	PaymentsUnallocated: 'Kohdistamattomat maksut',
	PayoutDate: 'Maksupäivä',
	PayoutsDisabled: 'Maksut poistettu käytöstä',
	PayoutsEnabled: 'Maksut käytössä',
	PayoutsStatus: 'Maksun tila',
	Pediatrician: 'Lastenlääkäri',
	Pen: 'Kynä',
	Pending: 'Odottaa',
	People: '{rosterSize } ihmistä',
	PeopleCount: 'Ihmiset ({count})',
	PerMonth: '/ Kuukausi',
	PerUser: 'Käyttäjää kohti',
	Permission: 'Lupa',
	PermissionRequired: 'Lupa vaaditaan',
	Permissions: 'Käyttöoikeudet',
	PermissionsClientAndContactDocumentation: 'Asiakas ',
	PermissionsClientAndContactProfiles: 'Asiakas ',
	PermissionsEditAccess: 'Muokkaa käyttöoikeuksia',
	PermissionsInvoicesAndPayments: 'Laskut ',
	PermissionsScheduling: 'Ajoitus',
	PermissionsUnassignClients: 'Poista asiakkaiden määritys',
	PermissionsUnassignClientsConfirmation: 'Haluatko varmasti poistaa näiden asiakkaiden määrityksen?',
	PermissionsValuesAssigned: 'Vain määrätty',
	PermissionsValuesEverything: 'Kaikki',
	PermissionsValuesNone: 'Ei mitään',
	PermissionsValuesOwnCalendar: 'Oma kalenteri',
	PermissionsViewAccess: 'Näytä pääsy',
	PermissionsWorkspaceSettings: 'Työtilan asetukset',
	Person: '{rosterSize} henkilö',
	PersonalDetails: 'Henkilötiedot',
	PersonalHealthcareHistoryStoreDescription:
		'Vastaa ja tallenna henkilökohtainen terveydenhuoltohistoriasi turvallisesti yhteen paikkaan',
	PersonalTrainer: 'Personal Trainer',
	PersonalTraining: 'Personal Training',
	PersonalizeWorkspace: '<h1>Henkilökohdista työtilaasi</h1>',
	PersonalizingYourWorkspace: 'Työtilasi personointi',
	Pharmacist: 'Farmaseutti',
	Pharmacy: 'Apteekki',
	PhoneCall: 'Puhelinsoitto',
	PhoneNumber: 'Puhelinnumero',
	PhoneNumberOptional: 'Puhelinnumero (valinnainen)',
	PhotoBy: 'Kuvaaja',
	PhysicalAddress: 'Fyysinen osoite',
	PhysicalTherapist: 'Fysioterapeutti',
	PhysicalTherapists: 'Fysioterapeutit',
	PhysicalTherapy: 'Fysioterapia',
	Physician: 'Lääkäri',
	PhysicianAssistant: 'Lääkärin assistentti',
	Physicians: 'Lääkärit',
	Physiotherapist: 'Fysioterapeutti',
	PlaceOfService: 'Palvelun paikka',
	Plan: 'Suunnitelma',
	PlanAndReport: 'Suunnitelma/Raportti',
	PlanId: 'Suunnitelman tunnus',
	PlansAndReportsCategoryDescription: 'Hoitosuunnittelun ja tulosten yhteenvedon tekemiseen',
	PleaseRefreshThisPageToTryAgain: 'Ole hyvä ja päivitä tämä sivu yrittääksesi uudelleen.',
	PleaseWait: 'Odota...',
	PleaseWaitForHostToJoin: 'Odotetaan isännän liittymistä...',
	PleaseWaitForHostToStart: 'Odota, että isäntä aloittaa tämän kokouksen.',
	PlusAdd: '+ Lisää',
	PlusOthers: '+{count} muuta',
	PlusPlanInclusionFive: 'Jaetut postilaatikot',
	PlusPlanInclusionFour: 'Ryhmävideopuhelut',
	PlusPlanInclusionHeader: 'Kaikki Essentialissa  ',
	PlusPlanInclusionOne: 'Rajoittamaton AI',
	PlusPlanInclusionSix: 'Mukautettu brändäys',
	PlusPlanInclusionThree: 'Ryhmäaikataulu',
	PlusPlanInclusionTwo: 'Rajoittamaton tallennustila ',
	PlusSubscriptionPlanSubtitle: 'Käytännöt optimoida ja kasvaa',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Poliisi',
	PolicyDates: 'Politiikan päivämäärät',
	PolicyHolder: 'Vakuutuksenottaja',
	PolicyHoldersAddress: 'Vakuutetun osoite',
	PolicyMemberId: 'Käytäntöjäsenen tunniste',
	PolicyStatus: 'Käytännön tila',
	Popular: 'Suosittu',
	PortalAccess: 'Portaalin käyttö',
	PortalNoAppointmentsHeading: 'Pidä kirjaa kaikista tulevista ja menneistä tapaamisista',
	PortalNoDocumentationHeading: 'Luo ja tallenna asiakirjoja turvallisesti',
	PortalNoRelationshipsHeading: 'Kokoa yhteen ne, jotka tukevat matkaasi',
	PosCodeErrorMessage: 'POS-koodi vaaditaan',
	PosoNumber: 'PO/SO numero',
	PossibleClientDuplicate: 'Mahdollinen asiakaskopio',
	PotentialClientDuplicateTitle: 'Mahdollinen asiakastietueen kaksoiskappale',
	PotentialClientDuplicateWarning:
		'Nämä asiakastiedot voivat jo olla asiakasluettelossasi. Tarkista ja päivitä olemassa oleva tietue tarvittaessa tai jatka uuden asiakkaan luomista.',
	PoweredBy: 'Voimanlähteenä',
	Practice: 'Harjoitella',
	PracticeDetails: 'Harjoittelun yksityiskohdat',
	PracticeInfoHeader: 'Yritystiedot',
	PracticeInfoPlaceholder: `Harjoituksen nimi,
 Kansallinen palveluntarjoajan tunniste,
 Työnantajan tunnusnumero`,
	PracticeLocation: 'Näyttää siltä, että harjoituksesi on käynnissä',
	PracticeSettingsAvailabilityTab: 'Saatavuus',
	PracticeSettingsBillingTab: 'Laskutusasetukset',
	PracticeSettingsClientSettingsTab: 'Asiakasasetukset',
	PracticeSettingsGeneralTab: 'Kenraali',
	PracticeSettingsOnlineBookingTab: 'Online varaus',
	PracticeSettingsServicesTab: 'Palvelut',
	PracticeSettingsTaxRatesTab: 'Veroprosentit',
	PracticeTemplate: 'Harjoitusmalli',
	Practitioner: 'Harjoittaja',
	PreferredLanguage: 'Suosittu kieli',
	PreferredName: 'Ensisijainen nimi',
	Prescription: 'Resepti',
	PreventionSpecialist: 'Ennaltaehkäisyn asiantuntija',
	Preview: 'Esikatselu',
	PreviewAndSend: 'Esikatsele ja lähetä',
	PreviewUnavailable: 'Esikatselu ei ole käytettävissä tälle tiedostotyypille',
	PreviousNotes: 'Edelliset muistiinpanot',
	Price: 'Hinta',
	PriceError: 'Hinnan on oltava suurempi kuin 0',
	PricePerClient: 'Hinta per asiakas',
	PricePerUser: 'per käyttäjä',
	PricePerUserBilledAnnually: 'Vuosittain laskutettua käyttäjää kohti',
	PricePerUserPerPeriod: '{price} käyttäjää kohti / {isMonthly, select, true {kuukausi} other {vuosi}}',
	PricingGuide: 'Opas hinnoittelusuunnitelmiin',
	PricingPlanPerMonth: '/ kuukausi',
	PricingPlanPerYear: '/ vuotta',
	Primary: 'Ensisijainen',
	PrimaryInsurance: 'Ensisijainen vakuutus',
	PrimaryPolicy: 'Ensisijainen vakuutus',
	PrimaryTimezone: 'Ensisijainen aikavyöhyke',
	Print: 'Painaa',
	PrintToCms1500: 'Tulosta CMS1500:een',
	PrivatePracticeConsultant: 'Yksityislääkärin konsultti',
	Proceed: 'Jatka',
	ProcessAtTimeOfBookingDesc: 'Asiakkaiden on maksettava koko palvelun hinta tehdäkseen varauksen verkossa',
	ProcessAtTimeOfBookingLabel: 'Käsittele maksut varauksen yhteydessä',
	Processing: 'Käsittely',
	ProcessingFee: 'Käsittelymaksu',
	ProcessingFeeToolTip: `Carepatronin avulla voit veloittaa käsittelykulut asiakkailtasi.
 Joillakin lainkäyttöalueilla on kiellettyä periä käsittelymaksuja asiakkailtasi. Sinun vastuullasi on noudattaa sovellettavia lakeja.`,
	ProcessingRequest: 'Käsitellään pyyntöä...',
	Product: 'Tuote',
	Profession: 'Ammatti',
	ProfessionExample: 'Terapeutti, ravitsemusterapeutti, hammaslääkäri',
	ProfessionPlaceholder: 'Aloita ammattisi kirjoittaminen tai valitse luettelosta',
	ProfessionalPlanInclusion1: 'Rajoittamaton tallennustila',
	ProfessionalPlanInclusion2: 'Rajattomasti tehtäviä',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 asiakastuki',
	ProfessionalPlanInclusion5: 'SMS-muistutukset',
	ProfessionalPlanInclusionHeader: 'Kaikki Starterissa, plus...',
	Professions: 'Ammatit',
	Profile: 'Profiili',
	ProfilePhotoFileSizeLimit: '5 Mt tiedostokokorajoitus',
	ProfilePopoverSubTitle: 'Olet kirjautunut sisään nimellä <strong>{email}</strong>',
	ProfilePopoverTitle: 'Omat työtilat',
	PromoCode: 'Tarjouskoodi',
	PromotionCodeApplied: '{promo} käytetty',
	ProposeNewDateTime: 'Ehdota uutta päivämäärää/aikaa',
	Prosthetist: 'Proteesisti',
	Provider: 'Palveluntarjoaja',
	ProviderBillingPlanExpansionManageButton: 'Hallinnoi suunnitelmaa',
	ProviderCommercialNumber: 'Tarjoajan kaupallinen numero',
	ProviderDetails: 'Palveluntarjoajan tiedot',
	ProviderDetailsAddress: 'Osoite',
	ProviderDetailsName: 'Nimi',
	ProviderDetailsPhoneNumber: 'Puhelinnumero',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'Valitettavasti tällä palveluntarjoajalla on jo laskutustili',
	ProviderInfoPlaceholder: `Henkilökunnan nimi,
 Sähköpostiosoite,
 puhelinnumero,
 Kansallinen palveluntarjoajan tunniste,
 Lisenssin numero`,
	ProviderIsChargedProcessingFee: 'Maksat käsittelymaksun',
	ProviderPaymentFormBackButton: 'Takaisin',
	ProviderPaymentFormBillingAddressCity: 'Kaupunki',
	ProviderPaymentFormBillingAddressCountry: 'Maa',
	ProviderPaymentFormBillingAddressLine1: 'Rivi1',
	ProviderPaymentFormBillingAddressPostalCode: 'Postinumero',
	ProviderPaymentFormBillingEmail: 'Sähköposti',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Luottokortin tiedot',
	ProviderPaymentFormCardExpiry: 'Vanhentuminen',
	ProviderPaymentFormCardHolderAddressTitle: 'Osoite',
	ProviderPaymentFormCardHolderName: 'Kortin haltijan nimi',
	ProviderPaymentFormCardHolderTitle: 'Kortinhaltijan tiedot',
	ProviderPaymentFormCardNumber: 'Kortin numero',
	ProviderPaymentFormPlanTitle: 'Valittu suunnitelma',
	ProviderPaymentFormPlanTotalTitle: 'Yhteensä ({currency}):',
	ProviderPaymentFormSaveButton: 'Tallenna tilaus',
	ProviderPaymentFreePlanDescription:
		'Ilmaisen suunnitelman valitseminen poistaa jokaiselta henkilöstön jäseneltä pääsyn asiakkailleen palveluntarjoajaltasi. Käyttöoikeutesi säilyy kuitenkin ja voit edelleen käyttää alustaa.',
	ProviderPaymentStepName: 'Arvostelu ',
	ProviderPaymentSuccessSnackbar: 'Hienoa! Uusi suunnitelmasi tallennettiin.',
	ProviderPaymentTitle: 'Arvostelu ',
	ProviderPlanNetworkIdentificationNumber: 'Palveluntarjoajan suunnitelman verkon tunnusnumero',
	ProviderRemindersSettingsBannerAction: 'Siirry Työnkulun hallintaan',
	ProviderRemindersSettingsBannerDescription:
		'Löydä kaikki muistutukset uudesta **Työvirran hallinta** -välilehdestä **Asetukset**-osiossa. Tämä päivitys tuo mukanaan tehokkaita uusia ominaisuuksia, parannettuja malleja ja älykkäämpiä automaatiotyökaluja tehokkuuden parantamiseksi. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Muistutukset toimivat nyt paremmin',
	ProviderTaxonomy: 'Tarjoajan taksonomia',
	ProviderUPINNumber: 'Tarjoajan UPIN-numero',
	ProviderUsedStoragePercentage: '{providerName} -tallennustila on {usedStoragePercentage}% täynnä!',
	PsychiatricNursePractitioner: 'Psykiatrinen sairaanhoitaja',
	Psychiatrist: 'Psykiatri',
	Psychiatrists: 'Psykiatrit',
	Psychiatry: 'Psykiatria',
	Psychoanalyst: 'Psykoanalyytikko',
	Psychologist: 'Psykologi',
	Psychologists: 'Psykologit',
	Psychology: 'Psykologia',
	Psychometrician: 'Psykometri',
	PsychosocialRehabilitationSpecialist: 'Psykososiaalisen kuntoutuksen asiantuntija',
	Psychotheraphy: 'Psykoterapia',
	Psychotherapists: 'Psykoterapeutit',
	Psychotherapy: 'Psykoterapia',
	PublicCallDialogTitle: 'Videopuhelu kanssa ',
	PublicCallDialogTitlePlaceholder: 'Videopuhelun tarjoaa Carepatron',
	PublicFormBackToForm: 'Lähetä toinen vastaus',
	PublicFormConfirmSubmissionHeader: 'Vahvista lähetys',
	PublicFormNotFoundDescription:
		'Etsimäsi lomake on saattanut poistua tai linkki on virheellinen. Tarkista URL ja yritä uudelleen.',
	PublicFormNotFoundTitle: 'Lomaketta ei löytynyt',
	PublicFormSubmissionError: 'Lähetys epäonnistui. Yritä uudelleen.',
	PublicFormSubmissionSuccess: 'Lomake lähetetty onnistuneesti',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} lähetti {noteTitle} julkisen lomakkeen',
	PublicFormSubmittedSubtitle: 'Lähetyksesi on vastaanotettu.',
	PublicFormSubmittedTitle: 'Kiitos!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Olemme lähettäneet vahvistuskoodin sähköpostiisi',
	PublicFormsInvalidConfirmationCode: 'Virheellinen vahvistuskoodi',
	PublicHealthInspector: 'kansanterveystarkastaja',
	PublicTemplates: 'Julkiset mallit',
	Publish: 'Julkaista',
	PublishTemplate: 'Julkaise malli',
	PublishTemplateFeatureBannerSubheader: 'Malleja, jotka on suunniteltu hyödyttämään yhteisöä',
	PublishTemplateHeader: 'Julkaise {title}',
	PublishTemplateToCommunity: 'Julkaise malli yhteisölle',
	PublishToCommunity: 'Julkaise yhteisössä',
	PublishToCommunitySuccessMessage: 'Julkaistiin yhteisölle onnistuneesti',
	Published: 'Julkaistu',
	PublishedBy: 'Julkaissut {name}',
	PublishedNotesAreNotAutosaved: 'Julkaistuja muistiinpanoja ei tallenneta automaattisesti',
	PublishedOnCarepatronCommunity: 'Julkaistu Carepatron-yhteisössä',
	Purchase: 'Ostaa',
	PushToCalendar: 'Paina kalenteriin',
	Question: 'Kysymys',
	QuestionOrTitle: 'Kysymys tai otsikko',
	QuickActions: 'Nopeita toimia',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Mustikka',
	QuickThemeSwitcherColorFushcia: 'Fuksia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Sammal',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Sädeterapeutti',
	Radiologist: 'radiologi',
	Read: 'Lukea',
	ReadOnly: 'Vain lukemista varten',
	ReadOnlyAppointment: 'Vain luku -aika',
	ReadOnlyEventBanner: 'Tämä tapaaminen on synkronoitu vain luku -kalenterista eikä sitä voi muokata.',
	ReaderMaxDepthHasBeenExceededCode: 'Huomautus on liian sisäkkäinen. Yritä poistaa joidenkin kohteiden sisennys.',
	ReadyForMapping: 'Valmiina kartoitukseen',
	RealEstateAgent: 'Kiinteistönvälittäjä',
	RearrangeClientFields: 'Järjestä asiakaskentät uudelleen asiakkaan asetuksissa',
	Reason: 'Syy',
	ReasonForChange: 'Syy muutokseen',
	RecentAppointments: 'Viimeaikaiset tapaamiset',
	RecentServices: 'Viimeaikaiset palvelut',
	RecentTemplates: 'Viimeaikaiset mallit',
	RecentlyUsed: 'Äskettäin käytetty',
	Recommended: 'Suositeltava',
	RecommendedTemplates: 'Suositellut mallit',
	Recording: 'Tallennus',
	RecordingEnded: 'Tallennus päättyi',
	RecordingInProgress: 'Tallennus käynnissä',
	RecordingMicrophoneAccessErrorMessage: 'Salli selaimesi mikrofonin käyttö ja päivitä aloittaaksesi nauhoituksen.',
	RecurrenceCount: ', {count, plural, one {kerran} other {# kertaa}}',
	RecurrenceDaily: '{count, plural, one {Päivittäin} other {Päiviä}}',
	RecurrenceEndAfter: 'Jälkeen',
	RecurrenceEndNever: 'Ei koskaan',
	RecurrenceEndOn: 'On',
	RecurrenceEvery: 'Jokainen {description}',
	RecurrenceMonthly: '{count, plural, one {Kuukausittain} other {Kuukaudet}}',
	RecurrenceOn: 'on {description}',
	RecurrenceOnAllDays: 'kaikkina päivinä',
	RecurrenceUntil: 'kunnes {description}',
	RecurrenceWeekly: '{count, plural, one {Viikoittain} other {Viikkoa}}',
	RecurrenceYearly: '{count, plural, one {Vuosittain} other {Vuotta}}',
	Recurring: 'Toistuva',
	RecurringAppointment: 'Toistuva tapaaminen',
	RecurringAppointmentsLimitedBannerText:
		'Kaikkia toistuvia tapaamisia ei näytetä. Lyhennä ajanjaksoa nähdäksesi kaikki toistuvat tapaamiset kyseiseltä ajanjaksolta.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# tapahtuma} other {# tapahtumaa}}</b> luodaan seuraavina päivinä',
	Redo: 'Toista',
	ReferFriends: 'Suosittele ystäviä',
	Reference: 'Viite',
	ReferralCreditedNotificationSubject: 'Viitehyvityksesi {currency} {amount} on käytetty',
	ReferralEmailDefaultBody: `Kiitos {name}, sinulle on lähetetty ILMAINEN 3 kuukauden Carepatron-päivitys. Liity yli 3 miljoonan terveydenhuollon ammattilaisen yhteisöömme, joka on rakennettu uutta työskentelytapaa varten!
Kiitos,
Carepatron-tiimi`,
	ReferralEmailDefaultSubject: 'Sinut on kutsuttu liittymään Carepatroniin',
	ReferralHasNotSignedUpDescription: 'Ystäväsi ei ole vielä rekisteröitynyt',
	ReferralHasSignedUpDescription: 'Ystäväsi on rekisteröitynyt.',
	ReferralInformation: 'Viittaustiedot',
	ReferralJoinedNotificationSubject: '{actorProfileName} on liittynyt Carepatroniin',
	ReferralListErrorDescription: 'Viittausluetteloa ei voitu ladata.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {kuukausi} other {kuukautta}}</b> aktiivinen',
	ReferralRewardBanner: 'Rekisteröidy ja lunasta suosituspalkintosi!',
	Referrals: 'Viittaukset',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} kuukauden {percentOff, select, 100 {ilmainen maksettu} other {{percentOff}% alennusta}} {type, select, SubscriptionUpgrade {päivitys} other {}}',
	ReferredUserBenefitTitle: 'He saavat!',
	Referrer: 'Viittaaja',
	ReferringProvider: 'Viittaava palveluntarjoaja',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} krediitti, kun <mark>3 ystävää</mark> aktivoituu.',
	ReferringUserBenefitTitle: 'Saat!',
	RefreshPage: 'Päivitä sivu',
	Refund: 'Palauttaa',
	RefundAcknowledgement: 'Olen hyvittänyt {clientName}n Carepatronin ulkopuolella.',
	RefundAcknowledgementValidationMessage: 'Vahvista, että olet palauttanut tämän summan',
	RefundAmount: 'Hyvityssumma',
	RefundContent:
		'Hyvitykset näkyvät asiakkaasi tilillä 7–10 päivän kuluessa. Maksuja ei palauteta, mutta palautuksista ei peritä ylimääräisiä kuluja. Hyvitystä ei voi peruuttaa, ja jotkut saattavat joutua tarkistamaan ennen käsittelyä.',
	RefundCouldNotBeProcessed: 'Hyvitystä ei voitu käsitellä',
	RefundError:
		'Tätä hyvitystä ei voida käsitellä automaattisesti tällä hetkellä. Ota yhteyttä Carepatron-tukeen pyytääksesi tämän maksun palautusta.',
	RefundExceedTotalValidationError: 'Summa ei saa ylittää maksetun kokonaissumman',
	RefundFailed: 'Hyvitys epäonnistui',
	RefundFailedTooltip:
		'Tämän maksun hyvitys epäonnistui aiemmin, eikä sitä voi yrittää uudelleen. Ota yhteyttä tukeen.',
	RefundNonStripePaymentContent:
		'Tämä maksu suoritettiin Carepatronin ulkopuolisella menetelmällä (esim. käteinen, verkkopankki). Hyvityksen myöntäminen Carepatronissa ei palauta varoja asiakkaalle.',
	RefundReasonDescription: 'Hyvityssyyn lisääminen voi auttaa asiakkaidesi tapahtumien tarkastelussa',
	Refunded: 'Hyvitetty',
	Refunds: 'Hyvitykset',
	RefundsTableEmptyState: 'Hyvitystä ei löytynyt',
	Regenerate: 'Uudelleenluo',
	RegisterButton: 'Rekisteröidy',
	RegisterEmail: 'Sähköposti',
	RegisterFirstName: 'Etunimi',
	RegisterLastName: 'Sukunimi',
	RegisterPassword: 'Salasana',
	RegisteredNurse: 'Rekisteröity sairaanhoitaja',
	RehabilitationCounselor: 'Kuntoutusneuvoja',
	RejectAppointmentFormTitle: 'Etkö selviä? Kerro meille syy ja ehdota uutta aikaa.',
	Rejected: 'Hylätty',
	Relationship: 'Suhde',
	RelationshipDetails: 'Suhteen tiedot',
	RelationshipEmptyStateTitle: 'Pysy yhteydessä niihin, jotka tukevat asiakastasi',
	RelationshipPageAccessTypeColumnName: 'Profiilin käyttöoikeus',
	RelationshipSavedSuccessSnackbar: 'Suhde tallennettu onnistuneesti!',
	RelationshipSelectorFamilyAdmin: 'Perhe',
	RelationshipSelectorFamilyMember: 'Perheenjäsen',
	RelationshipSelectorProviderAdmin: 'Palveluntarjoajan ylläpitäjä',
	RelationshipSelectorProviderStaff: 'Palveluntarjoajan henkilökunta',
	RelationshipSelectorSupportNetworkPrimary: 'ystävä',
	RelationshipSelectorSupportNetworkSecondary: 'Tukiverkosto',
	RelationshipStatus: 'Suhteen tila',
	RelationshipType: 'Suhteen tyyppi',
	RelationshipTypeClientOwner: 'Asiakas',
	RelationshipTypeFamilyAdmin: 'Suhteet',
	RelationshipTypeFamilyMember: 'Perhe',
	RelationshipTypeFriendOrSupport: 'Ystävä tai tukiverkosto',
	RelationshipTypeProviderAdmin: 'Palveluntarjoajan järjestelmänvalvoja',
	RelationshipTypeProviderStaff: 'Henkilökunta',
	RelationshipTypeSelectorPlaceholder: 'Hae suhdetyyppejä',
	Relationships: 'Suhteet',
	Remaining: 'jäljellä',
	RemainingTime: '{time} jäljellä',
	Reminder: 'Muistutus',
	ReminderColor: 'Muistutuksen väri',
	ReminderDetails: 'Muistutuksen tiedot',
	ReminderEditDisclaimer: 'Muutokset näkyvät vain uusissa tapaamisissa',
	ReminderSettings: 'Tapaamismuistutusasetukset',
	Reminders: 'Muistutukset',
	Remove: 'Poistaa',
	RemoveAccess: 'Poista käyttöoikeus',
	RemoveAllGuidesBtn: 'Poista kaikki ohjaimet',
	RemoveAllGuidesPopoverBody:
		'Kun olet valmis aloittamaan opasteet, käytä kunkin paneelin opasteiden poistamispainiketta.',
	RemoveAllGuidesPopoverTitle: 'Etkö enää tarvitse perehdytysoppaitasi?',
	RemoveAsDefault: 'Poista oletuksena',
	RemoveAsIntake: 'Poista imuna',
	RemoveCol: 'Poista sarake',
	RemoveColor: 'Poista väri',
	RemoveField: 'Poista kenttä',
	RemoveFromCall: 'Poista puhelusta',
	RemoveFromCallDescription: 'Oletko varma, että haluat poistaa <mark>{attendeeName}</mark> tästä videopuhelusta?',
	RemoveFromCollection: 'Poista kokoelmasta',
	RemoveFromCommunity: 'Poista yhteisöstä',
	RemoveFromFolder: 'Poista kansiosta',
	RemoveFromFolderConfirmationDescription:
		'Oletko varma, että haluat poistaa tämän mallin tästä kansiosta? Tätä toimintoa ei voi peruuttaa, mutta voit siirtää sen myöhemmin takaisin.',
	RemoveFromIntakeDefault: 'Poista oletusarvo',
	RemoveGuides: 'Poista ohjaimet',
	RemoveMfaConfirmationDescription:
		'Multi-Factor Authentication (MFA) -todennuksen poistaminen heikentää tilisi turvallisuutta. Haluatko jatkaa?',
	RemoveMfaConfirmationTitle: 'Poistetaanko MFA?',
	RemovePaymentMethodDescription: `Tämä poistaa kaiken tämän maksutavan pääsyn ja tulevan käytön.
 Tätä toimintoa ei voi kumota.`,
	RemoveRow: 'Poista rivi',
	RemoveTable: 'Poista pöytä',
	RemoveTemplateAsDefaultIntakeSuccess: 'Otettu {templateTitle} pois oletusvastaanottopohjana onnistuneesti',
	RemoveTemplateFromCommunity: 'Poista malli yhteisöstä',
	RemoveTemplateFromFolder: '{templateTitle} poistettiin onnistuneesti kansiosta {folderTitle}',
	Rename: 'Nimeä uudelleen',
	RenderingProvider: 'Renderöinnin tarjoaja',
	Reopen: 'Avaa uudelleen',
	ReorderServiceGroupFailure: 'Kokoelman uudelleenjärjestäminen epäonnistui',
	ReorderServiceGroupSuccess: 'Kokoelman uudelleenjärjestäminen onnistui',
	ReorderServicesFailure: 'Palveluiden uudelleen tilaaminen epäonnistui',
	ReorderServicesSuccess: 'Palveluiden uudelleen tilaus onnistui',
	ReorderYourServiceList: 'Järjestä palveluluettelosi uudelleen',
	ReorderYourServiceListDescription:
		'Tapa, jolla järjestät palvelusi ja kokoelmasi, näkyy online-varaussivullasi kaikkien asiakkaidesi nähtävillä!',
	RepeatEvery: 'Toista joka',
	RepeatOn: 'Toista',
	Repeating: 'Toistaa',
	Repeats: 'Toistaa',
	RepeatsEvery: 'Toistuu joka',
	Rephrase: 'Muotoile uudelleen',
	Replace: 'Korvata',
	ReplaceBackground: 'Vaihda tausta',
	ReplacementOfPriorClaim: 'Korvaa aiemman vaateen',
	Report: 'Raportoi',
	Reprocess: 'Käsittele uudelleen',
	RepublishTemplateToCommunity: 'Julkaise malli yhteisölle',
	RequestANewVerificationLink: 'Pyydä uusi vahvistuslinkki',
	RequestCoverageReport: 'Pyydä kattavuusraportti',
	RequestingDevicePermissions: 'Pyydetään laitteen käyttöoikeuksia...',
	RequirePaymentMethodDesc: 'Asiakkaiden on annettava luottokorttitietonsa tehdäkseen varauksen verkossa',
	RequirePaymentMethodLabel: 'Vaadi luottokorttitiedot',
	Required: 'tarvitaan',
	RequiredField: 'Pakollinen',
	RequiredUrl: 'URL-osoite vaaditaan.',
	Reschedule: 'Aikatauluta uudelleen',
	RescheduleBookingLinkModalDescription: 'Asiakkaasi voi muuttaa tapaamispäivää ja -aikaa tämän linkin kautta.',
	RescheduleBookingLinkModalTitle: 'Aikatauluta varaus -linkki',
	RescheduleLink: 'Aikatauluta linkki',
	Resend: 'Lähetä uudelleen',
	ResendConfirmationCode: 'Lähetä vahvistuskoodi uudelleen',
	ResendConfirmationCodeDescription:
		'Anna sähköpostiosoitteesi, niin lähetämme sinulle sähköpostitse toisen vahvistuskoodin',
	ResendConfirmationCodeSuccess: 'Vahvistuskoodi on lähetetty uudelleen, tarkista postilaatikkosi',
	ResendNewEmailVerificationSuccess: 'Uusi vahvistuslinkki on lähetetty osoitteeseen {email}',
	ResendVerificationEmail: 'Lähetä vahvistussähköposti uudelleen',
	Reset: 'Nollaa',
	Resources: 'Resurssit',
	RespiratoryTherapist: 'Hengitysterapeutti',
	RespondToHistoricAppointmentError:
		'Tämä on historiallinen tapaaminen. Ota yhteyttä lääkäriisi, jos sinulla on kysyttävää.',
	Responder: 'Vastaaja',
	RestorableItemModalDescription:
		'Oletko varma, että haluat poistaa {context}?{canRestore, select, true { Voit palauttaa sen myöhemmin.} other {}}',
	RestorableItemModalTitle: 'Poista {type}',
	Restore: 'Palauttaa',
	RestoreAll: 'Palauta kaikki',
	Restricted: 'Rajoitettu',
	ResubmissionCodeReferenceNumber: 'Uudelleenlähetyskoodi ja viitenumero',
	Resubmit: 'Lähetä uudelleen',
	Resume: 'Jatkaa',
	Retry: 'Yritä uudelleen',
	RetryingConnectionAttempt: 'Yritetään yhteyttä uudelleen... (Yritys {retryCount}/{maxRetries})',
	ReturnToForm: 'Palaa lomakkeeseen',
	RevertClaimStatus: 'Peruuta vaatimuksen tila',
	RevertClaimStatusDescriptionBody:
		'Tämä vaatimus on liitetty maksuihin, ja tilan muuttaminen voi vaikuttaa maksujen seurantaan tai käsittelyyn, mikä voi vaatia manuaalista sovittamista.',
	RevertClaimStatusDescriptionTitle: 'Oletko varma, että haluat palauttaa {status} -tilaan?',
	RevertClaimStatusError: 'Vaatimuksen tilan palauttaminen epäonnistui',
	RevertToDraft: 'Palaa luonnokseen',
	Review: 'Arvostelu',
	ReviewsFirstQuote: 'Ajanvaraus missä tahansa, milloin tahansa',
	ReviewsSecondJobTitle: 'Lifehousen klinikka',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Pidän myös carepatron-sovelluksesta. Auttaa minua seuraamaan asiakkaitani ja työskentelemään liikkeellä ollessani.',
	ReviewsThirdJobTitle: 'Manila Bay Clinic',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'Navigoinnin helppous ja kaunis käyttöliittymä saa hymyn huulilleni joka päivä.',
	RightAlign: 'Oikea tasaus',
	Role: 'Rooli',
	Roster: 'Osallistujat',
	RunInBackground: 'Suorita taustalla',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Jätät Carepatronin',
	SafetyRedirectSubtext: 'Jos luotat tähän linkkiin, jatka valitsemalla se',
	SalesRepresentative: 'Myyntiedustaja',
	SalesTax: 'Myyntivero',
	SalesTaxHelp: 'Sisältää arvonlisäveron tuotetuista laskuista',
	SalesTaxIncluded: 'Kyllä',
	SalesTaxNotIncluded: 'Ei',
	SaoPaulo: 'São Paulo',
	Saturday: 'lauantai',
	Save: 'Tallentaa',
	SaveAndClose: 'Tallentaa ',
	SaveAndExit: 'Tallentaa ',
	SaveAndLock: 'Tallenna ja lukitse',
	SaveAsDraft: 'Tallenna luonnoksena',
	SaveCardForFuturePayments: 'Säästä kortti tulevia maksuja varten',
	SaveChanges: 'Tallenna muutokset',
	SaveCollection: 'Tallenna kokoelma',
	SaveField: 'Tallenna kenttä',
	SavePaymentMethod: 'Tallenna maksutapa',
	SavePaymentMethodDescription: 'Sinua ei veloiteta ennen ensimmäistä tapaamistasi.',
	SavePaymentMethodSetupError: 'Tapahtui odottamaton virhe, emmekä voineet määrittää maksuja tällä hetkellä.',
	SavePaymentMethodSetupInvoiceLater:
		'Maksut voidaan määrittää ja tallentaa ensimmäisen laskun maksamisen yhteydessä.',
	SaveSection: 'Tallenna osio',
	SaveService: 'Luo uusi palvelu',
	SaveTemplate: 'Tallenna malli',
	Saved: 'Tallennettu',
	SavedCards: 'Tallennetut kortit',
	SavedPaymentMethods: 'Tallennettu',
	Saving: 'Tallennetaan...',
	ScheduleAppointmentsAndOnlineServices: 'Ajanvaraus ja verkkopalvelut',
	ScheduleName: 'Aikataulun nimi',
	ScheduleNew: 'Aikataulu uusi',
	ScheduleSend: 'Aikatauluta lähetys',
	ScheduleSendAlertInfo: 'Aikataulun mukaiset keskustelut lähetetään sovittuna aikana.',
	ScheduleSendByName: '<strong>Aikataulu lähetetään</strong> • {time} käyttäjän {displayName} toimesta',
	ScheduleSetupCall: 'Ajoita aloituspuhelu',
	Scheduled: 'Aikataulutettu',
	SchedulingSend: 'Lähetyksen ajoitus',
	School: 'Koulu',
	ScrollToTop: 'Vieritä ylös',
	Search: 'Haku',
	SearchAndConvertToLanguage: 'Hae ja käännä kielelle',
	SearchBasicBlocks: 'Hae peruslohkoja',
	SearchByName: 'Hae nimellä',
	SearchClaims: 'Hakemuksen etsiminen',
	SearchClientFields: 'Hae asiakaskenttiä',
	SearchClients: 'Hae asiakkaan nimellä, asiakastunnuksella tai puhelinnumerolla',
	SearchCommandNotFound: 'Ei löytynyt tuloksia "{searchTerm}"-haulle.',
	SearchContacts: 'Asiakas tai yhteyshenkilö',
	SearchContactsPlaceholder: 'Etsi yhteystietoja',
	SearchConversations: 'Hae keskusteluja',
	SearchInputPlaceholder: 'Hae kaikista resursseista',
	SearchInvoiceNumber: 'Hae laskun numero',
	SearchInvoices: 'Hae laskuja',
	SearchMultipleContacts: 'Asiakkaat tai yhteyshenkilöt',
	SearchMultipleContactsOptional: 'Asiakkaat tai yhteyshenkilöt (valinnainen)',
	SearchOrCreateATag: 'Hae tai luo tunniste',
	SearchPayments: 'Hae maksuja',
	SearchPrepopulatedData: 'Hae valmiiksi täytetyistä tietokentistä',
	SearchRelationships: 'Etsi suhteita',
	SearchRemindersAndWorkflows: 'Hae muistutuksia ja työnkulkuja',
	SearchServices: 'Hakupalvelut',
	SearchTags: 'Hae tunnisteita',
	SearchTeamMembers: 'Etsi tiimin jäseniä',
	SearchTemplatePlaceholder: 'Etsi {templateCount}+ resurssia',
	SearchTimezone: 'Hae aikavyöhykettä...',
	SearchTrashItems: 'Etsi kohteita',
	SearchUnsplashPlaceholder: 'Hae ilmaisia korkearesoluutioisia valokuvia Unsplashista',
	Secondary: 'Toissijainen',
	SecondaryInsurance: 'Toissijainen vakuutus',
	SecondaryPolicy: 'Toissijainen vakuutus',
	SecondaryTimezone: 'Toissijainen aikavyöhyke',
	Secondly: 'Toiseksi',
	Section: 'osio',
	SectionCannotBeEmpty: 'Osassa on oltava vähintään yksi rivi',
	SectionFormSecondaryText: 'Osion otsikko ja kuvaus',
	SectionName: 'Osion nimi',
	Sections: 'Osiot',
	SeeLess: 'Katso vähemmän',
	SeeLessUpcomingAppointments: 'Katso vähemmän tulevia tapaamisia',
	SeeMore: 'Katso lisää',
	SeeMoreUpcomingAppointments: 'Katso lisää tulevia tapaamisia',
	SeeTemplateLibrary: 'Katso mallikirjasto',
	Seen: 'Nähty',
	SeenByName: '<strong>Nähty</strong> • {time} by {displayName}',
	SelectAll: 'Valitse kaikki',
	SelectAssignees: 'Valitse valtuutetut',
	SelectAttendees: 'Valitse osallistujat',
	SelectCollection: 'Valitse Kokoelma',
	SelectCorrespondingAttributes: 'Valitse vastaavat attribuutit',
	SelectPayers: 'Valitse maksajat',
	SelectProfile: 'Valitse profiili',
	SelectServices: 'Valitse palvelut',
	SelectTags: 'Valitse Tunnisteet',
	SelectTeamOrCommunity: 'Valitse Tiimi tai Yhteisö',
	SelectTemplate: 'Valitse Malli',
	SelectType: 'Valitse tyyppi',
	Selected: 'Valittu',
	SelfPay: 'Itse maksava',
	Send: 'Lähetä',
	SendAndClose: 'Lähetä ',
	SendAndStopIgnore: 'Lähetä ja lopeta välittäminen',
	SendEmail: 'Lähetä sähköpostia',
	SendIntake: 'Lähetä sisäänotto',
	SendIntakeAndForms: 'Lähetä sisäänotto ',
	SendMeACopy: 'Lähetä minulle kopio',
	SendNotificationEmailWarning:
		'Jotkut osallistujista eivät ole antaneet sähköpostiosoitetta ja he eivät saa automaattisia ilmoituksia ja muistutuksia.',
	SendNotificationLabel: 'Valitse sähköpostilla ilmoitettavat osallistujat',
	SendOnlinePayment: 'Lähetä verkkomaksu',
	SendOnlinePaymentTooltipTitleAdmin: 'Lisää haluamasi maksuasetukset',
	SendOnlinePaymentTooltipTitleStaff: 'Pyydä palveluntarjoajan omistajaa määrittämään verkkomaksut.',
	SendPaymentLink: 'Lähetä maksulinkki',
	SendReaction: 'Lähetä reaktio',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Lähetä vahvistussähköposti',
	SendingFailed: 'Lähetys epäonnistui',
	Sent: 'Lähetetty',
	SentByName: '<strong>Lähetetty</strong> • {time} by {displayName}',
	Seoul: 'Soul',
	SeparateDuplicateClientsDescription: 'Valitut asiakastietueet pysyvät erillään muista, ellet yhdistä niitä',
	Service: 'Palvelu',
	'Service/s': 'Palvelut',
	ServiceAdjustment: 'Palvelun säätö',
	ServiceAllowNewClientsIndicator: 'Salli uudet asiakkaat',
	ServiceAlreadyExistsInCollection: 'Palvelu on jo kokoelmassa',
	ServiceBookableOnlineIndicator: 'Varattavissa netistä',
	ServiceCode: 'Koodi',
	ServiceCodeErrorMessage: 'Palvelukoodi vaaditaan',
	ServiceCodeSelectorPlaceholder: 'Lisää palvelukoodi',
	ServiceColour: 'Palvelun väri',
	ServiceCoverageDescription: 'Valitse sopivat palvelut ja maksa tämä vakuutus.',
	ServiceCoverageGoToServices: 'Mene palveluihin',
	ServiceCoverageNoServicesDescription:
		'Räätälöi palvelun omavastuusummat ohittaaksesi oletusarvoisen vakuutusmaksun. Poista vakuutus käytöstä, jotta palveluja ei vaadita vakuutusta vastaan.',
	ServiceCoverageNoServicesLabel: 'Palveluita ei löytynyt.',
	ServiceCoverageTitle: 'Palvelun kattavuus',
	ServiceDate: 'Palvelun päivämäärä',
	ServiceDetails: 'Palvelun tiedot',
	ServiceDuration: 'Kesto',
	ServiceEmptyState: 'Palveluita ei vielä ole',
	ServiceErrorMessage: 'Palvelu vaaditaan',
	ServiceFacility: 'Palvelukeskus',
	ServiceName: 'Palvelun nimi',
	ServiceRate: 'Rate',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} {serviceReceiptNumber, select, undefined {käyttäjälle} other {{clientName}}} vaatii lisätietoja',
	ServiceSalesTax: 'Myyntivero',
	ServiceType: 'Palvelu',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Päivitä ja hanki uusimmat Carepatron-päivitykset napsauttamalla Reload.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Lataa uudelleen',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Käytät vanhempaa versiota',
	ServiceWorkerForceUIUpdateDialogTitle: 'Tervetuloa takaisin!',
	Services: 'Palvelut',
	ServicesAndAvailability: 'Palvelut ',
	ServicesAndDiagnosisCodesHeader: 'Lisää palveluita ja diagnoosikoodeja',
	ServicesCount: '{count,plural,=0{Palvelut}one{Palvelu}other{Palvelut}}',
	ServicesPlaceholder: 'Palvelut',
	ServicesProvidedBy: 'Palvelu(t) tarjoaa',
	SetAPhysicalAddress: 'Aseta fyysinen osoite',
	SetAVirtualLocation: 'Aseta virtuaalinen sijainti',
	SetAsDefault: 'Aseta oletukseksi',
	SetAsIntake: 'Aseta sisääntuloksi',
	SetAsIntakeDefault: 'Aseta sisäänoton oletusarvoksi',
	SetAvailability: 'Aseta saatavuus',
	SetTemplateAsDefaultIntakeSuccess: 'Asetettu {templateTitle} oletusarvoiseksi vastaanottopohjaksi',
	SetUpMfaButton: 'Määritä MFA',
	SetYourLocation: 'Aseta oma<mark> sijainti</mark>',
	SetYourLocationDescription: 'Minulla ei ole yritysosoitetta <span>(vain verkko- ja mobiilipalvelut)</span>',
	SettingUpPayers: 'Maksujen määrittäminen',
	Settings: 'Asetukset',
	SettingsNewUserPasswordDescription:
		'Kun olet rekisteröitynyt, lähetämme sinulle vahvistuskoodin, jolla voit vahvistaa tilisi',
	SettingsNewUserPasswordTitle: 'Rekisteröidy Carepatroniin',
	SettingsTabAutomation: 'Automaatio',
	SettingsTabBillingDetails: 'Laskutustiedot',
	SettingsTabConnectedApps: 'Yhdistetyt sovellukset',
	SettingsTabCustomFields: 'Mukautetut kentät',
	SettingsTabDetails: 'Yksityiskohdat',
	SettingsTabInvoices: 'Laskut',
	SettingsTabLocations: 'Sijainnit',
	SettingsTabNotifications: 'Ilmoitukset',
	SettingsTabOnlineBooking: 'Online-varaus',
	SettingsTabPayers: 'Maksajat',
	SettingsTabReminders: 'Muistutukset',
	SettingsTabServices: 'Palvelut',
	SettingsTabServicesAndAvailability: 'Palvelut ja saatavuus',
	SettingsTabSubscriptions: 'Tilaukset',
	SettingsTabWorkflowAutomations: 'Automaatiot',
	SettingsTabWorkflowReminders: 'Perusmuistutukset',
	SettingsTabWorkflowTemplates: 'Mallit',
	Setup: 'Määritä',
	SetupGuide: 'Asennusohjeet',
	SetupGuideAddServicesActionLabel: 'Aloita',
	SetupGuideAddServicesSubtitle: '4 vaihetta • 2 min',
	SetupGuideAddServicesTitle: 'Lisää palvelusi',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Aloita',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 vaihetta • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Ota käyttöön verkkolaskutus',
	SetupGuideImportClientsActionLabel: 'Aloita',
	SetupGuideImportClientsSubtitle: '4 vaihetta • 3 min',
	SetupGuideImportClientsTitle: 'Tuo asiakkaasi',
	SetupGuideImportTemplatesActionLabel: 'Aloita',
	SetupGuideImportTemplatesSubtitle: '2 vaihetta • 1 min',
	SetupGuideImportTemplatesTitle: 'Tuo templatisi',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Aloita',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 vaihetta • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: '<h1>Personoi työtilaasi</h1>',
	SetupGuideSetLocationActionLabel: 'Aloita',
	SetupGuideSetLocationSubtitle: '4 vaihetta • 1 min',
	SetupGuideSetLocationTitle: 'Aseta sijaintisi',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Kutsu tiimi',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Kutsu tiimisi kommunikoimaan ja hallitsemaan tehtäviä vaivattomasti.',
	SetupGuideSuggestedAddTeamMembersTag: 'Asetus',
	SetupGuideSuggestedAddTeamMembersTitle: 'Kutsu tiimi',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Muokkaa',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Tuntuu ammattimaiselta ainutlaatuisen logon ja brändivärien avulla.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Muokkaa brändiä',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Lataa',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Pääset työtilaasi mistä tahansa, milloin tahansa ja millä tahansa laitteella.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Asetus',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Lataa sovellus',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Aseta saatavuus',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Estä päällekkäiset varaukset asettamalla saatavuutesi.',
	SetupGuideSuggestedEditAvailabilityTag: 'Ajanvaraus',
	SetupGuideSuggestedEditAvailabilityTitle: 'Muokkaa saatavuutta',
	SetupGuideSuggestedImportClientsActionLabel: 'Tuo',
	SetupGuideSuggestedImportClientsSubtitle: 'Lataa olemassa olevat asiakastietosi yhdellä napsautuksella.',
	SetupGuideSuggestedImportClientsTag: 'Asetus',
	SetupGuideSuggestedImportClientsTitle: 'Tuo asiakkaat',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Muokkaa muistutuksia',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'Vähentää peruutuksia automaattisella muistutuksella.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Henkilökohtaiset muistutukset',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Aloita puhelu',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Järjestä puhelu ja ota yhteyttä asiakkaisiin tekoälyllä toimivien videotyökalujemme avulla.',
	SetupGuideSuggestedStartVideoCallTag: 'Teleterveys',
	SetupGuideSuggestedStartVideoCallTitle: 'Aloita videopuhelu',
	SetupGuideSuggestedTryActionsTitle: 'Kokeile näitä 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Kokeile AI-avustusta',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Saat välittömiä vastauksia kaikkiin työhösi liittyviin kysymyksiin.',
	SetupGuideSuggestedUseAIAssistantTag: 'Uusi',
	SetupGuideSuggestedUseAIAssistantTitle: 'Kokeile AI-avustusta',
	SetupGuideSyncCalendarActionLabel: 'Aloita',
	SetupGuideSyncCalendarSubtitle: '1 vaihe • alle 1 min',
	SetupGuideSyncCalendarTitle: 'Synkronoi kalenterisi',
	SetupGuideVerifyEmailLabel: 'Tarkista',
	SetupGuideVerifyEmailSubtitle: '2 vaihetta • 2 min',
	SetupOnlineStripePayments: 'Käytä raitaa verkkomaksuissa',
	SetupPayments: 'Määritä maksut',
	Sex: 'seksiä',
	SexSelectorPlaceholder: 'Mies / Nainen / Ei halua sanoa',
	Share: 'Jakaa',
	ShareBookingLink: 'Jaa varauslinkki',
	ShareNoteDefaultMessage: `Hei{name} on jakanut "{documentName}" kanssasi.

Kiitos,
{practiceName}`,
	ShareNoteMessage: `Hei
{name} on jakanut "{documentName}" {isResponder, select, true {sinulla on joitakin kysymyksiä täytettäväksi.} other {kanssasi.}}

Kiitos,
{practiceName}`,
	ShareNoteTitle: 'Jaa ‘{noteTitle}’',
	ShareNotesWithClients: 'Jaa asiakkaiden tai kontaktien kanssa',
	ShareScreen: 'Jaa näyttö',
	ShareScreenNotSupported: 'Laitteesi/selaimesi ei tue näytön jakamistoimintoa',
	ShareScreenWithId: 'Näyttö {screenId}',
	ShareTemplateAsPublicFormModalDescription: 'Salli muiden katsella tätä mallia ja lähettää sen lomakkeena.',
	ShareTemplateAsPublicFormModalTitle: 'Jaa linkki ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Julkinen lomakekonfiguraatio päivitettiin onnistuneesti',
	ShareTemplateAsPublicFormSectionCustomization: 'Muokkaus',
	ShareTemplateAsPublicFormShowPoweredBy: 'Näytä "Powered by Carepatron" lomakkeellani',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Näytä/piilota “Powered by Carepatron” lomakkeessani',
	ShareTemplateAsPublicFormTrigger: 'Jaa',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Käytä työtilan brändäystä',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Näytä/piilota työtilan brändäys',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Lähettää koodia olemassa oleville ja ei olemassa oleville asiakkaille',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Allekirjoitukset vaativat aina sähköpostin vahvistamisen',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription:
		'Lähettää koodia vain olemassa oleville asiakkaille',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Ei koskaan lähetä koodia',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `'Never' -vaihtoehdon valinta saattaa antaa varmentamattomille käyttäjille mahdollisuuden kirjoittaa yli asiakastietoja, jos he käyttävät jo olemassa olevan asiakkaan sähköpostiosoitetta.`,
	ShareWithCommunity: 'Jaa yhteisön kanssa',
	ShareYourReferralLink: 'Jaa suosituslinkkisi',
	ShareYourScreen: 'Jaa näyttösi',
	SheHer: 'Hän/Hän',
	ShortTextAnswer: 'Lyhyt tekstivastaus',
	ShortTextFormPrimaryText: 'Lyhyt teksti',
	ShortTextFormSecondaryText: 'Alle 300 merkin vastaus',
	Show: 'Show',
	ShowColumn: 'Näytä sarake',
	ShowColumnButton: 'Näytä sarake {value} -painike',
	ShowColumns: 'Näytä sarakkeet',
	ShowColumnsMenu: 'Näytä sarakkeet-valikko',
	ShowDateDurationDescription: 'esim. 29 vuotta vanha',
	ShowDateDurationLabel: 'Näytä päivämäärän kesto',
	ShowDetails: 'Näytä tiedot',
	ShowField: 'Näytä kenttä',
	ShowFullAddress: 'Näytä osoite',
	ShowHideFields: 'Näytä / Piilota kentät',
	ShowIcons: 'Näytä kuvakkeet',
	ShowLess: 'Näytä vähemmän',
	ShowMeetingTimers: 'Näytä kokousajastimet',
	ShowMenu: 'Näytä valikko',
	ShowMergeSummarySidebar: 'Näytä yhdistämisen yhteenveto',
	ShowMore: 'Näytä lisää',
	ShowOnTranscript: 'Näytä transkriptiossa',
	ShowReactions: 'Näytä reaktiot',
	ShowSection: 'Näytä osio',
	ShowServiceCode: 'Näytä palvelukoodi',
	ShowServiceDescription: 'Näytä kuvaus palveluvarauksista',
	ShowServiceDescriptionDesc: 'Asiakkaat voivat tarkastella palvelukuvauksia varauksenteon yhteydessä',
	ShowServiceGroups: 'Näytä kokoelmat',
	ShowServiceGroupsDesc: 'Asiakkaille näytetään palvelut ryhmiteltynä kokoelman mukaan varauksen yhteydessä',
	ShowSpeakers: 'Näytä kaiuttimet',
	ShowTax: 'Näytä vero',
	ShowTimestamp: 'Näytä aikaleima',
	ShowUnits: 'Näytä yksiköt',
	ShowWeekends: 'Näyttelyviikonloput',
	ShowYourView: 'Näytä näkemyksesi',
	SignInWithApple: 'Kirjaudu sisään Applella',
	SignInWithGoogle: 'Kirjaudu sisään Googlella',
	SignInWithMicrosoft: 'Kirjaudu sisään Microsoftilla',
	SignUpTitleReferralDefault: '<mark>Rekisteröidy</mark> ja lunasta suosituspalkintosi',
	SignUpTitleReferralUpgrade:
		'Aloita {durationInMonths} kuukauden <mark>{percentOff, select, 100 {ilmainen} other {{percentOff}% alennus}} päivitys</mark>',
	SignatureCaptureError: 'Allekirjoitusta ei voi siepata. Yritä uudelleen.',
	SignatureFormPrimaryText: 'Allekirjoitus',
	SignatureFormSecondaryText: 'Hanki digitaalinen allekirjoitus',
	SignatureInfoTooltip: 'Tämä visuaalinen esitys ei ole kelvollinen sähköinen allekirjoitus.',
	SignaturePlaceholder: 'Piirrä allekirjoituksesi tähän',
	SignedBy: 'Allekirjoittanut',
	Signup: 'Rekisteröidy',
	SignupAgreements: 'Hyväksyn {termsOfUse} ja {privacyStatement} tilini käyttöä varten.',
	SignupBAA: 'Liikekumppanisopimus',
	SignupBusinessAgreements:
		'Oma ja yrityksen puolesta hyväksyn {businessAssociateAgreement}, {termsOfUse} ja {privacyStatement} tiliäni varten.',
	SignupInvitationForYou: 'Sinut on kutsuttu käyttämään Carepatronia.',
	SignupPageProviderWarning:
		'Jos järjestelmänvalvojasi on jo luonut tilin, sinun on pyydettävä häntä kutsumaan sinut kyseiseen palveluntarjoajaan. Älä käytä tätä ilmoittautumislomaketta. Katso lisätietoja',
	SignupPageProviderWarningLink: 'tämä linkki.',
	SignupPrivacy: 'Tietosuojakäytäntö',
	SignupProfession: 'Mikä ammatti kuvaa sinua parhaiten?',
	SignupSubtitle:
		'Carepatronin harjoittelunhallintaohjelmisto on tehty yksinammattilaisille ja ryhmille. Lopeta liiallisten maksujen maksaminen ja ole osa vallankumousta.',
	SignupSuccessDescription:
		'Vahvista sähköpostiosoitteesi aloittaaksesi perehtymisen. Jos et saa sitä heti, tarkista roskapostikansiosi.',
	SignupSuccessTitle: 'Tarkista sähköpostisi',
	SignupTermsOfUse: 'Käyttöehdot',
	SignupTitleClient: '<mark>Hallitse terveyttäsi</mark> yhdestä paikasta',
	SignupTitleLast: 'ja kaikki tekemäsi työ! – Se on ilmainen',
	SignupTitleOne: '<mark>Tehostaa sinua</mark> , ',
	SignupTitleThree: '<mark>Tehosta asiakkaitasi</mark> , ',
	SignupTitleTwo: '<mark>Tehosta tiimiäsi</mark> , ',
	Simple: 'Yksinkertainen',
	SimplifyBillToDetails: 'Yksinkertaista lasku yksityiskohtiin',
	SimplifyBillToHelperText: 'Vain ensimmäistä riviä käytetään, kun se vastaa asiakasta',
	Singapore: 'Singapore',
	Single: 'Sinkku',
	SingleChoiceFormPrimaryText: 'Yksittäinen valinta',
	SingleChoiceFormSecondaryText: 'Valitse vain yksi vaihtoehto',
	Sister: 'Sisko',
	SisterInLaw: 'Käly',
	Skip: 'Ohita',
	SkipLogin: 'Ohita kirjautuminen',
	SlightBlur: 'Sumenna hieman taustaasi',
	Small: 'Pieni',
	SmartChips: 'Älysirut',
	SmartDataChips: 'Älykkäät datasirut',
	SmartReply: 'Älyvastaukset',
	SmartSuggestNewClient: '<strong>Älykkäät Ehdotukset</strong> luo {name} uutena asiakkaana',
	SmartSuggestedFieldDescription: 'Tämä kenttä on älykäs ehdotus',
	SocialSecurityNumber: 'Sosiaaliturvatunnus',
	SocialWork: 'Sosiaalityö',
	SocialWorker: 'Sosiaalityöntekijä',
	SoftwareDeveloper: 'Ohjelmistojen kehittäjä',
	Solo: 'Yksin',
	Someone: 'Joku',
	Son: 'Poika',
	SortBy: 'Lajitteluperuste',
	SouthAmerica: 'Etelä-Amerikka',
	Speaker: 'Kaiutin',
	SpeakerSource: 'Kaiuttimen lähde',
	Speakers: 'Kaiuttimet',
	SpecifyPaymentMethod: 'Määritä maksutapa',
	SpeechLanguagePathology: 'Puhe-kielen patologia',
	SpeechTherapist: 'Puheterapeutti',
	SpeechTherapists: 'Puheterapeutit',
	SpeechTherapy: 'Puheterapia',
	SportsMedicinePhysician: 'Urheilulääketieteen lääkäri',
	Spouse: 'puoliso',
	SpreadsheetColumnExample: 'esim ',
	SpreadsheetColumns: 'Taulukon sarakkeet',
	SpreadsheetUploaded: 'Laskentataulukko ladattu',
	SpreadsheetUploading: 'Ladataan...',
	Staff: 'Henkilökunta',
	StaffAccessDescriptionAdmin: 'Järjestelmänvalvojat voivat hallita kaikkea alustalla.',
	StaffAccessDescriptionStaff: `Henkilökunnan jäsenet voivat hallita luomiaan tai jakamiaan asiakkaita, muistiinpanoja ja dokumentaatiota
 heidän kanssaan, ajoita tapaamisia, hallitse laskuja.`,
	StaffContactAssignedSubject:
		'{actorProfileName} on määrännyt {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} ja {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { ja 1 muu asiakas} other { ja # muuta asiakasta}} sinulle',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} on jakanut kanssasi {inboxName}-postilaatikon',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} on poistanut sinun pääsyn {inboxName} -postilaatikkoon.',
	StaffMembers: 'Henkilökunnan jäsenet',
	StaffMembersNumber: '{billedUsers, plural, one {# tiimijäsentä} other {# tiimijäsentä}}',
	StaffSavedSuccessSnackbar: 'Joukkueen jäsentiedot tallennettu onnistuneesti!',
	StaffSelectorAdminRole: 'Järjestelmänvalvoja',
	StaffSelectorStaffRole: 'Henkilökunnan jäsen',
	StandardAppointment: 'Tavallinen tapaaminen',
	StandardColor: 'Tehtävän väri',
	StartAndEndTime: 'Aloitus- ja päättymisaika',
	StartCall: 'Aloita puhelu',
	StartDate: 'Aloituspäivämäärä',
	StartDictating: 'Aloita sanelu',
	StartImport: 'Aloita tuonti',
	StartRecordErrorTitle: 'Nauhoituksen aloittamisessa tapahtui virhe',
	StartRecording: 'Aloita tallennus',
	StartTimeIncrements: 'Aloitusajan lisäykset',
	StartTimeIncrementsView: '{startTimeIncrements} min välein',
	StartTranscribing: 'Aloita litterointi',
	StartTranscribingNotes:
		'Valitse asiakkaat, joille haluat luoda muistiinpanon. Napsauta sitten "Aloita litterointi" -painiketta aloittaaksesi tallennus',
	StartTranscription: 'Aloita transkriptio',
	StartVideoCall: 'Aloita videopuhelu',
	StartWeekOn: 'Aloita viikko',
	StartedBy: 'Aloitti ',
	Starter: 'Käynnistin',
	State: 'Osavaltio',
	StateIndustrialAccidentProviderNumber: 'Valtion työtapaturmayhtiön numero',
	StateLicenseNumber: 'Valtion lisenssinumero',
	Statement: 'lausunto',
	StatementDescriptor: 'Lausuntokuvaaja',
	StatementDescriptorToolTip:
		'Tiliotteen kuvaaja näkyy asiakkaasi pankki- tai luottokorttiotteissa. Sen on oltava 5–22 merkin pituinen ja heijastettava yrityksesi nimeä.',
	StatementNumber: 'Lausunto #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Anna tilatunniste',
	StepFather: 'Isäpuoli',
	StepMother: 'Äitipuoli',
	Stockholm: 'Tukholma',
	StopIgnoreSendersDescription:
		'Jos lopetat näiden lähettäjien huomioimisen, tulevat keskustelut lähetetään Saapuneet-kansioon. Haluatko varmasti lopettaa näiden lähettäjien huomioimisen?',
	StopIgnoring: 'Lopeta huomioimatta jättäminen',
	StopIgnoringSenders: 'Lopeta lähettäjien huomioiminen',
	StopIgnoringSendersSuccess: 'Lopetettiin sähköpostiosoitteen <mark>{addresses}</mark> jättäminen huomiotta',
	StopSharing: 'Lopeta jakaminen',
	StopSharingLabel: 'carepatron.com jakaa näyttösi.',
	Storage: 'Varastointi',
	StorageAlmostFullDescription: '🚀 Päivitä nyt, jotta tilisi toimii sujuvasti.',
	StorageAlmostFullTitle: 'Olet käyttänyt {percentage}% työtilasi tallennustilasta!',
	StorageFullDescription: 'Hanki lisää tallennustilaa päivittämällä pakettisi.',
	StorageFullTitle: '	Tallennustila on täynnä.',
	Street: 'Street',
	StripeAccountNotCompleteErrorCode:
		'Verkkopalkkiot eivät ole {hasProviderName, select, true {asennettu {providerName}ille}} other {mahdollisia tällä palveluntarjoajalla}}.',
	StripeAccountRejectedError: 'Stripe-tili on hylästy. Ota yhteyttä tukeen.',
	StripeBalance: 'Stripe Balance',
	StripeChargesInfoToolTip: 'Voit veloittaa debit ',
	StripeFeesDescription:
		'Carepatron käyttää Stripeä saadakseen sinulle maksut nopeasti ja pitämään maksutietosi turvassa. Käytettävissä olevat maksutavat vaihtelevat alueittain, kaikki yleisimmät veloitukset ',
	StripeFeesDescriptionItem1: 'Käsittelymaksuja sovelletaan jokaiseen onnistuneeseen tapahtumaan, voit {link}.',
	StripeFeesDescriptionItem2: 'Maksut suoritetaan päivittäin, mutta niitä pidetään enintään 4 päivää.',
	StripeFeesLinkToRatesText: 'katso hintamme täältä',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Pahoittelemme, mutta laskuille, joissa käytetään verkkomaksuja, on oltava vähintään {minimumAmount}',
	StripePaymentsDisabled: 'Verkkomaksut ovat poissa käytöstä. Tarkista maksuasetuksesi.',
	StripePaymentsUnavailable: 'Maksut eivät ole käytettävissä',
	StripePaymentsUnavailableDescription: 'Maksuja ladattaessa tapahtui virhe. Yritä myöhemmin uudelleen.',
	StripePayoutsInfoToolTip: 'Mahdollistaa maksun saamisen pankkitilillesi',
	StyleYourWorkspace: '<mark>Tyyli</mark> työtilaasi',
	StyleYourWorkspaceDescription1:
		'Haeimme brändiomaisuutesi verkkosivustoltasi. Voit muokata niitä vapaasti tai jatkaa Carepatron-työtilaasi.',
	StyleYourWorkspaceDescription2:
		'Käytä brändiomaisuuttasi laskujen ja verkkovarauksien mukauttamiseen saumattoman asiakaskokemuksen aikaansaamiseksi',
	SubAdvanced: 'Edistynyt',
	SubEssential: 'Välttämätön',
	SubOrganization: 'Organisaatio',
	SubPlus: 'Lisäksi',
	SubProfessional: 'Ammattilainen',
	Subject: 'Aihe',
	Submit: 'Lähetä',
	SubmitElectronically: 'Lähetä sähköisesti',
	SubmitFeedback: 'Lähetä palautetta',
	SubmitFormValidationError: 'Varmista, että kaikki vaaditut kentät on täytetty oikein, ja yritä lähettää uudelleen.',
	Submitted: 'Lähetetty',
	SubmittedDate: 'Lähetetty päivä',
	SubscribePerMonth: 'Tilaa {price} {isMonthly, select, true {kuukaudessa} other {vuodessa}}',
	SubscriptionDiscountDescription:
		'{percentOff}% alennusta {months, select, null { } other { {months, plural, one {yhden kuukauden ajan} other {useiden kuukausien ajan}}}}',
	SubscriptionFreeTrialDescription: 'Ilmainen  {endDate} asti',
	SubscriptionPaymentFailedNotificationSubject: 'Emme voineet suorittaa tilauksesi maksamista. Tarkista maksutietosi',
	SubscriptionPlanDetailsHeader: 'Käyttäjää kohden / kuukausittain laskutetaan vuosittain',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} laskutetaan kuukausittain (USD)',
	SubscriptionPlans: 'Tilaussuunnitelmat',
	SubscriptionPlansDescription:
		'Päivitä suunnitelmaasi avataksesi lisäetuja ja varmistaaksesi käytäntösi sujuvan toiminnan.',
	SubscriptionPlansDescriptionNoPermission:
		'Näyttää siltä, että sinulla ei ole oikeuksia päivityksen käyttämiseen tällä hetkellä – ota yhteyttä järjestelmänvalvojaan saadaksesi apua.',
	SubscriptionSettings: 'Tilausasetukset',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> tallennustilaa käytetty',
	SubscriptionSettingsStorageUsed: '{käytetty} of {raja} käytetty',
	SubscriptionSettingsUnlimitedStorage: 'Rajoittamaton tallennustila käytettävissä',
	SubscriptionSummary: 'Tilauksen yhteenveto',
	SubscriptionUnavailableOverStorageLimit: 'Nykyinen käytönsi ylittää tämän paketin tallennustilavuuden.',
	SubscriptionUnpaidBannerButton: 'Siirry tilauksiin',
	SubscriptionUnpaidBannerDescription: 'Tarkista, että maksutietosi ovat oikein, ja yritä uudelleen',
	SubscriptionUnpaidBannerTitle: 'Emme voineet suorittaa tilauksesi maksamista.',
	Subscriptions: 'Tilaukset',
	SubscriptionsAndPayments: 'Tilaukset ',
	Subtotal: 'Välisumma',
	SuburbOrProvince: 'Esikaupunki/maakunta',
	SuburbOrState: 'Esikaupunki/osavaltio',
	SuccessSavedNoteChanges: 'Muistiinpanon muutosten tallennus onnistui',
	SuccessShareDocument: 'Asiakirjan jakaminen onnistui',
	SuccessShareNote: 'Muistiinpanon jakaminen onnistui',
	SuccessfullyCreatedValue: 'Luotu {value} onnistuneesti',
	SuccessfullyDeletedTranscriptionPart: 'Transkriptio-osan poistaminen onnistui',
	SuccessfullyDeletedValue: 'Poistettiin onnistuneesti {value}',
	SuccessfullySubmitted: 'Lähetetty onnistuneesti ',
	SuccessfullyUpdatedClientSettings: 'Asiakasasetusten päivitys onnistui',
	SuccessfullyUpdatedTranscriptionPart: 'Transkriptio-osan päivitys onnistui',
	SuccessfullyUpdatedValue: 'Päivitetty onnistuneesti {value}',
	SuggestedAIPoweredTemplates: 'Ehdotuksia tekoälyllä tuotetuista malleista',
	SuggestedAITemplates: 'Ehdotetut AI-mallit',
	SuggestedActions: 'Ehdotetut toimet',
	SuggestedLocations: 'Ehdotetut sijainnit',
	Suggestions: 'Ehdotukset',
	Summarise: 'AI yhteenveto',
	SummarisingContent: 'Yhteenveto {title}ista',
	Sunday: 'sunnuntai',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superbill ',
	SuperbillAutomationMonthly: 'Aktiivinen • Kuukauden viimeinen päivä',
	SuperbillAutomationNoEmail:
		'Jos haluat lähettää automaattiset laskutusasiakirjat onnistuneesti, lisää sähköpostiosoite tälle asiakkaalle',
	SuperbillAutomationNotActive: 'Ei aktiivinen',
	SuperbillAutomationUpdateFailure: 'Superbill-automaatioasetusten päivittäminen epäonnistui',
	SuperbillAutomationUpdateSuccess: 'Superbill-automaatioasetusten päivitys onnistui',
	SuperbillClientHelperText: 'Nämä tiedot on esitäytetty asiakkaan tiedoista',
	SuperbillNotFoundDescription:
		'Ota yhteyttä palveluntarjoajaasi ja kysy heiltä lisätietoja tai lähetä superlasku uudelleen.',
	SuperbillNotFoundTitle: 'Superbilliä ei löytynyt',
	SuperbillNumber: 'Superlasku #{number}',
	SuperbillNumberAlreadyExists: 'Superlaskun kuittinumero on jo olemassa',
	SuperbillPracticeHelperText: 'Nämä tiedot on esitäytetty harjoituslaskutusasetuksista',
	SuperbillProviderHelperText: 'Nämä tiedot on esitäytetty henkilöstötiedoista',
	SuperbillReceipts: 'Superlaskukuitit',
	SuperbillsEmptyStateDescription: 'Ylimääräisiä laskuja ei ole löytynyt.',
	Surgeon: 'Kirurgi',
	Surgeons: 'Kirurgit',
	SurgicalTechnologist: 'Kirurginen teknologi',
	SwitchFromAnotherPlatform: 'Vaihdan toiselta alustalta',
	SwitchToMyPortal: 'Vaihda Omaan portaaliin',
	SwitchToMyPortalTooltip: `Käytä omaa henkilökohtaista portaaliasi,
 joiden avulla voit tutkia omaasi
 asiakkaan portaalikokemus.`,
	SwitchWorkspace: 'Vaihda työtilaa',
	SwitchingToADifferentPlatform: 'Vaihtaminen toiselle alustalle',
	Sydney: 'Sydney',
	SyncCalendar: 'Synkronoi kalenteri',
	SyncCalendarModalDescription:
		'Muut tiimin jäsenet eivät näe synkronoituja kalentereitasi. Asiakasaikoja voi päivittää tai poistaa vain Carepatronissa.',
	SyncCalendarModalDisplayCalendar: 'Näytä kalenterini Carepatronissa',
	SyncCalendarModalSyncToCarepatron: 'Synkronoi kalenterini Carepatroniin',
	SyncCalendarModalSyncWithCalendar: 'Synkronoi Carepatron-ajat kalenterin kanssa',
	SyncCarepatronAppointmentsWithMyCalendar: 'Synkronoi **Carepatron**-tapaamiset kalenterini kanssa',
	SyncGoogleCalendar: 'Synkronoi Google-kalenteri',
	SyncInbox: 'Synkronoi postilaatikko Carepatronin kanssa',
	**************************: 'Synkronoi kalenterini Carepatronin kanssa',
	SyncOutlookCalendar: 'Synkronoi Outlook-kalenteri',
	SyncedFromExternalCalendar: 'Synkronoitu ulkoisesta kalenterista',
	SyncingCalendarName: 'Synkronoidaan {calendarName} -kalenteria',
	SyncingFailed: 'Synkronointi epäonnistui',
	SystemGenerated: 'Järjestelmän luoma',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Taulukko',
	TableRowLabel: 'Taulukon rivi arvolle {value}',
	TagSelectorNoOptionsText: 'Napsauta "luo uusi" lisätäksesi uuden tunnisteen',
	Tags: 'Tunnisteet',
	TagsInputPlaceholder: 'Etsi tai luo tunnisteita',
	Task: 'Tehtävä',
	TaskAttendeeStatusUpdatedSuccess: 'Ajanvaraustilausten päivitys onnistui',
	Tasks: 'Tehtävät',
	Tax: 'Verottaa',
	TaxAmount: 'Veron määrä',
	TaxID: 'Veronumero',
	TaxIdType: 'Verotunnuksen tyyppi',
	TaxName: 'Veronimi',
	TaxNumber: 'Veronumero',
	TaxNumberType: 'Veronumerotyypi',
	TaxNumberTypeInvalid: '{type} on virheellinen',
	TaxPercentageOfAmount: '{taxName} ({percentage}% {amount}sta)',
	TaxRate: 'Veroprosentti',
	TaxRatesDescription: 'Hallinnoi veroprosentteja, joita sovelletaan laskusi rivikohtiin.',
	Taxable: 'Verollinen',
	TaxonomyCode: 'Taksonomiakoodi',
	TeacherAssistant: 'Opettajan assistentti',
	Team: 'Joukkue',
	TeamMember: 'Joukkueen jäsen',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {on} other {ovat}} jo varattuina tällä hetkellä.{br}Valitse uusi aika välttääksesi kaksoisvarauksen.',
	TeamMembers: 'Joukkueen jäsenet',
	TeamMembersColour: 'Joukkueen jäsenten väri',
	TeamMembersDetails: 'Joukkueen jäsenten tiedot',
	TeamSize: 'Kuinka monta henkilöä tiimissäsi on?',
	TeamTemplates: 'Joukkueen mallit',
	TeamTemplatesSectionDescription: 'Luotu sinun ja tiimisi toimesta',
	TelehealthAndVideoCalls: 'Teleterveys ',
	TelehealthProvidedOtherThanInPatientCare: 'Etäterveydenhuolto muuhun kuin laitoshoitoon',
	TelehealthVideoCall: 'Telehealth videopuhelu',
	Template: 'Malli',
	TemplateDescription: 'Mallin kuvaus',
	TemplateDetails: 'Mallin tiedot',
	TemplateEditModeViewSwitcherDescription: 'Luo ja muokkaa mallia',
	TemplateGallery: 'Yhteisömallit',
	TemplateImportCompletedNotificationSubject: 'Malli tuotu! {templateTitle} on valmis käytettäväksi.',
	TemplateImportFailedNotificationSubject: 'Tiedoston {fileName} tuonti epäonnistui.',
	TemplateName: 'Mallin nimi',
	TemplateNotFound: 'Mallia ei löytynyt.',
	TemplatePreviewErrorMessage: 'Mallin esikatselua ladattaessa tapahtui virhe',
	TemplateResponderModeViewSwitcherDescription: 'Esikatsele lomakkeita ja käytä niitä',
	TemplateResponderModeViewSwitcherTooltipTitle: 'Tarkista, miltä lomakkeesi näyttävät vastaajien täyttäessä ne',
	TemplateSaved: 'Tallennetut muutokset',
	TemplateTitle: 'Mallin otsikko',
	TemplateType: 'Mallin tyyppi',
	Templates: 'Mallit',
	TemplatesCategoriesFilter: 'Suodata luokan mukaan',
	TemplatesPublicTemplatesFilter: ' Suodata yhteisön/joukkueen mukaan',
	Text: 'Teksti',
	TextAlign: 'Tekstin tasaus',
	TextColor: 'Tekstin väri',
	ThankYouForYourFeedback: 'Kiitos palautteestasi!',
	ThanksForLettingKnow: 'Kiitos, että ilmoitit meille.',
	ThePaymentMethod: 'Maksutapa',
	ThemThey: 'He/He',
	Theme: 'Teema',
	ThemeAllColorsPickerTitle: 'Lisää teemoja',
	ThemeColor: 'Teema',
	ThemeColorDarkMode: 'Tumma',
	ThemeColorLightMode: 'Valo',
	ThemeColorModePickerTitle: 'Värimoodi',
	ThemeColorSystemMode: 'Järjestelmä',
	ThemeCpColorPickerTitle: 'Carepatron-teemat',
	ThemePanelDescription: 'Valitse valo- ja tumma tila ja muokkaa teemasi asetuksia',
	ThemePanelTitle: 'Ulkoasu',
	Then: 'Sitten',
	Therapist: 'Terapeutti',
	Therapists: 'terapeutit',
	Therapy: 'Terapia',
	Thick: 'Paksu',
	Thin: 'Ohut',
	ThirdPerson: '3. henkilö',
	ThisAndFollowingAppointments: 'Tämä ja seuraavat tapaamiset',
	ThisAndFollowingMeetings: 'Tämä ja seuraavat kokoukset',
	ThisAndFollowingReminders: 'Tämä ja seuraavat muistutukset',
	ThisAndFollowingTasks: 'Tämä ja seuraavat tehtävät',
	ThisAppointment: 'Tämä tapaaminen',
	ThisMeeting: 'Tämä kokous',
	ThisMonth: 'Tämä kuukausi',
	ThisPerson: 'Tämä henkilö',
	ThisReminder: 'Tämä muistutus',
	ThisTask: 'Tämä tehtävä',
	ThisWeek: 'Tällä viikolla',
	ThreeDay: '3 päivää',
	Thursday: 'torstai',
	Time: 'Aika',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {min}}',
	TimeAgoSeconds: '{number}t',
	TimeFormat: 'Aikamuoto',
	TimeIncrement: 'Ajan lisäys',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {ap} other {ip}}',
	TimeslotSize: 'Aikavälin koko',
	Timestamp: 'Aikaleima',
	Timezone: 'Aikavyöhyke',
	TimezoneDisplay: 'Aikavyöhykkeen näyttö',
	TimezoneDisplayDescription: 'Hallinnoi aikavyöhykkeen näyttöasetuksia.',
	Title: 'Otsikko',
	To: 'Vastaanottaja',
	ToYourWorkspace: 'työtilallesi',
	Today: 'Tänään',
	TodayInHoursPlural: 'Tänään {count} {count, plural, one {tunti} other {tuntia}}',
	TodayInMinsAbbreviated: 'Tänään {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Vaihda otsikkosolua',
	ToggleHeaderCol: 'Vaihda otsikkosaraketta',
	ToggleHeaderRow: 'Vaihda otsikkorivi',
	Tokyo: 'Tokio',
	Tomorrow: 'Huomenna',
	TomorrowAfternoon: 'Huomenna iltapäivällä',
	TomorrowMorning: 'Huomisaamuna',
	TooExpensive: 'Liian kallis',
	TooHardToSetUp: 'Liian vaikea määrittää',
	TooManyFiles: 'Yli 1 tiedosto havaittu.',
	ToolsExample: 'Yksinkertainen harjoitus, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Kokonais',
	TotalAccountCredit: 'Tilin saldo yhteensä',
	TotalAdjustments: 'Yhteensä muutokset',
	TotalAmountToCreditInCurrency: 'Hyvitettävä kokonaissumma ({currency})',
	TotalBilled: 'Yhteensä laskutettu',
	TotalConversations: '{total} {total, plural, =0 {keskustelu} one {keskustelu} other {keskusteluja}}',
	TotalOverdue: 'Myöhästyneet yhteensä',
	TotalOverdueTooltip:
		'Erääntynyt kokonaissaldo sisältää kaikki maksamattomat laskut ajanjaksosta riippumatta, joita ei ole mitätöity tai käsitelty.',
	TotalPaid: 'Maksettu yhteensä',
	TotalPaidTooltip:
		'Maksettu kokonaissaldo sisältää kaikki määrät laskuista, jotka on maksettu määritetyn ajanjakson aikana.',
	TotalUnpaid: 'Yhteensä maksamaton',
	TotalUnpaidTooltip:
		'Maksamaton kokonaissaldo sisältää kaikki erät käsittelystä, maksamattomista ja lähetetyistä laskuista, jotka erääntyvät määritetyn ajanjakson sisällä.',
	TotalWorkflows: '{count} {count, plural, one {työnkulku} other {työnkulut}}',
	TotpSetUpManualEntryInstruction: 'Vaihtoehtoisesti voit syöttää alla olevan koodin manuaalisesti sovellukseen:',
	TotpSetUpModalDescription: 'Skannaa QR-koodi todennussovelluksellasi määrittääksesi monivaiheisen todennuksen.',
	TotpSetUpModalTitle: 'Asenna MFA-laite',
	TotpSetUpSuccess: 'Kaikki on valmista! MFA on otettu käyttöön.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Anna todennussovelluksesi luoma koodi',
	Transcribe: 'Literoi',
	TranscribeLanguageSelector: 'Valitse syöttökieli',
	TranscribeLiveAudio: 'Litteroi live-ääni',
	Transcribing: 'Transkriptoidaan ääntä...',
	TranscribingIn: 'Transkriptio sisään',
	Transcript: 'Transkriptio',
	TranscriptRecordingCompleteInfo: 'Näet transkription täällä, kun tallennus on valmis.',
	TranscriptSuccessSnackbar: 'Transkriptio on käsitelty onnistuneesti.',
	Transcription: 'Transkriptio',
	TranscriptionEmpty: 'Transkriptiota ei ole saatavilla',
	TranscriptionEmptyHelperMessage: 'Tämä transkriptio ei poiminut mitään. Käynnistä se uudelleen ja yritä uudelleen.',
	TranscriptionFailedNotice: 'Tämän transkription käsittely ei onnistunut',
	TranscriptionIdleMessage:
		'Emme kuule ääntä. Jos tarvitset lisää aikaa, vastaa {timeValue} sekunnin kuluessa, muuten istunto päättyy.',
	TranscriptionInProcess: 'Kirjoitus käynnissä...',
	TranscriptionIncompleteNotice: 'Joitakin tämän transkription osia ei käsitelty onnistuneesti',
	TranscriptionOvertimeWarning: '{scribeType} -istunto päättyy <strong>{timeValue} {unit}</strong>ssa',
	TranscriptionPartDeleteMessage: 'Haluatko varmasti poistaa tämän transkriptioosan?',
	TranscriptionText: 'Ääni tekstiksi',
	TranscriptsPending: 'Tekstisi on saatavilla täällä istunnon päätyttyä.',
	Transfer: 'Siirtää',
	TransferAndDelete: 'Siirrä ja poista',
	TransferOwnership: 'Siirrä omistajuus',
	TransferOwnershipConfirmationModalDescription:
		'Tämä toiminto voidaan kumota vain, jos he siirtävät omistajuuden takaisin sinulle.',
	TransferOwnershipDescription: 'Siirrä tämän työtilan omistajuus toiselle tiimin jäsenelle.',
	TransferOwnershipSuccessSnackbar: 'Omistusoikeus siirretty onnistuneesti!',
	TransferOwnershipToMember: 'Oletko varma, että haluat siirtää tämän työtilan {staff}lle?',
	TransferStatusAlert:
		'{numberOfStatuses, plural, one {Tämän tilan} other {Näiden tilojen}} poistaminen vaikuttaa {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} asiakkaan tilaan.</strong>} other {<strong>{numberOfAffectedRecords} asiakkaan tilaan.</strong>}}',
	TransferStatusDescription:
		'Valitse näille asiakkaille toinen tila ennen kuin jatkat poistamista. Tätä toimintoa ei voi kumota.',
	TransferStatusLabel: 'Siirrä uuteen tilaan',
	TransferStatusPlaceholder: 'Valitse olemassa oleva tila',
	TransferStatusTitle: 'Siirron tila ennen poistamista',
	TransferTaskAttendeeStatusAlert:
		'Tämän tilan poistaminen vaikuttaa <strong>{number} tulevaan {number, plural, one {tilan} other {tilojen}}. </strong>',
	TransferTaskAttendeeStatusDescription:
		'Valitse näille asiakkaille toinen tila ennen poistamisen jatkamista. Tätä toimintoa ei voi peruuttaa.',
	TransferTaskAttendeeStatusSubtitle: 'Ajan tila',
	TransferTaskAttendeeStatusTitle: 'Siirtotila ennen poistamista',
	Trash: 'Roskakori',
	TrashDeleteItemsModalConfirm: 'Vahvista kirjoittamalla {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Seuraavat {count, plural, one {kohde} other {kohteet}} poistetaan pysyvästi, eikä niitä voi palauttaa.',
	TrashDeleteItemsModalTitle: 'Poista {count, plural, one {kohde} other {kohteet}} ikuisesti',
	TrashDeletedAllItems: 'Poistettu kaikki kohteet',
	TrashDeletedItems: 'Poistettu {count, plural, one {kohde} other {kohteet}}',
	TrashDeletedItemsFailure: 'Kohteiden poistaminen roskakorista epäonnistui',
	TrashLocationAppointmentType: 'Kalenteri',
	TrashLocationBillingAndPaymentsType: 'Laskutus ja maksut',
	TrashLocationContactType: 'Asiakkaat',
	TrashLocationNoteType: 'Huomautuksia ',
	TrashRestoreItemsModalDescription: 'Seuraavat {count, plural, one {kohde} other {kohteet}} palautetaan.',
	TrashRestoreItemsModalTitle: 'Palauta {count, plural, one {kohde} other {kohteet}}',
	TrashRestoredAllItems: 'Palautettiin kaikki kohteet',
	TrashRestoredItems: 'Palautettu {count, plural, one {kohde} other {kohteet}}',
	TrashRestoredItemsFailure: 'Kohteiden palauttaminen roskakorista epäonnistui',
	TrashSuccessfullyDeletedItem: 'Onnistuneesti poistettu {type}',
	Trigger: 'Laukaista',
	Troubleshoot: 'Vianetsintä',
	TryAgain: 'Yritä uudelleen',
	Tuesday: 'tiistai',
	TwoToTen: '2-10',
	Type: 'Tyyppi',
	TypeHere: 'Kirjoita tähän...',
	TypeToConfirm: 'Vahvistaaksesi, kirjoita {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Otsikko 1',
	TypographyHeading2: 'Otsikko 2',
	TypographyHeading3: 'Otsikko 3',
	TypographyHeading4: 'Otsikko 4',
	TypographyHeading5: 'Otsikko 5',
	TypographyP: 'P',
	TypographyParagraph: 'Kohta',
	UnableToCompleteAction: 'Toimintoa ei voi suorittaa loppuun.',
	UnableToPrintDocument: 'Asiakirjaa ei voi tulostaa. Yritä myöhemmin uudelleen.',
	Unallocated: 'Kohdistamaton',
	UnallocatedPaymentDescription: `Tätä maksua ei ole kohdistettu kokonaan laskutettaviin tuotteisiin.
 Lisää määräraha maksamattomille tuotteille tai myönnä hyvitys tai hyvitys.`,
	UnallocatedPaymentTitle: 'Kohdistamaton maksu',
	UnallocatedPayments: 'Kohdistamattomat maksut',
	Unarchive: 'Poista arkistosta',
	Unassigned: 'Ei määritetty',
	UnauthorisedInvoiceSnackbar: 'Sinulla ei ole oikeutta hallinnoida tämän asiakkaan laskuja.',
	UnauthorisedSnackbar: 'Sinulla ei ole lupaa tehdä tätä.',
	Unavailable: 'Ei saatavilla',
	Uncategorized: 'Luokittelematon',
	Unclaimed: 'Lunastamaton',
	UnclaimedAmount: 'Vaatimaton määrä',
	UnclaimedItems: 'Lunastamattomat tuotteet',
	UnclaimedItemsMustBeInCurrency: 'Vain seuraavien valuuttojen kohteet ovat tuettuja: {currencies}',
	Uncle: 'Setä',
	Unconfirmed: 'Vahvistamaton',
	Underline: 'Korostaa',
	Undo: 'Kumoa',
	Unfavorite: 'Poista suosikeista',
	Uninvoiced: 'Laskuttamaton',
	UninvoicedAmount: 'Maksuta muista',
	UninvoicedAmounts:
		'{count, plural, =0 {Ei laskutettuja summia} one {Laskuttamaton summa} other {Laskuttamattomia summia}}',
	Unit: 'Yksikkö',
	UnitedKingdom: 'Yhdistynyt kuningaskunta',
	UnitedStates: 'Yhdysvallat',
	UnitedStatesEast: 'Yhdysvallat - Itä',
	UnitedStatesWest: 'Yhdysvallat - Länsi',
	Units: 'Yksiköt',
	UnitsIsRequired: 'Yksiköt vaaditaan',
	UnitsMustBeGreaterThanZero: 'Yksiköiden on oltava suurempia kuin 0',
	UnitsPlaceholder: '1',
	Unknown: 'Tuntematon',
	Unlimited: 'Rajaton',
	Unlock: 'Avata',
	UnlockNoteHelper: 'Ennen uusien muutosten tekemistä toimittajien on avattava muistiinpanon lukitus.',
	UnmuteAudio: 'Poista mykistys',
	UnmuteEveryone: 'Poista kaikkien mykistys',
	Unpaid: 'Palkaton',
	UnpaidInvoices: 'Maksamattomat laskut',
	UnpaidItems: 'Maksamattomat tuotteet',
	UnpaidMultiple: 'Palkaton',
	Unpublish: 'Poista julkaisu',
	UnpublishTemplateConfirmationModalPrompt:
		'Poistamalla <span>{title}</span> poistat tämän resurssin Carepatron-yhteisöstä. Tätä toimea ei voi peruuttaa.',
	UnpublishToCommunitySuccessMessage: 'Onnistuneesti poistettu ‛{title}’ yhteisöstä',
	Unread: 'Lukematon',
	Unrecognised: 'Tuntematon',
	UnrecognisedDescription:
		'Nykyinen sovellusversiosi ei tunnista tätä maksutapaa. Päivitä selaimesi saadaksesi uusimman version nähdäksesi ja muokataksesi tätä maksutapaa.',
	UnsavedChanges: 'Tallentamattomat muutokset',
	UnsavedChangesPromptContent: 'Haluatko tallentaa tekemäsi muutokset ennen sulkemista?',
	UnsavedChangesPromptTitle: 'Sinulla on tallentamattomia muutoksia',
	UnsavedNoteChangesWarning: 'Tekemiäsi muutoksia ei välttämättä tallenneta',
	UnsavedTemplateChangesWarning: 'Tekemiäsi muutoksia ei välttämättä tallenneta',
	UnselectAll: 'Poista valinta kaikista',
	Until: 'Kunnes',
	UntitledConversation: 'Nimeämätön keskustelu',
	UntitledFolder: 'Nimetön kansio',
	UntitledNote: 'Nimetön muistiinpano',
	UntitledSchedule: 'Nimetön aikataulu',
	UntitledSection: 'Nimetön jakso',
	UntitledTemplate: 'Nimetön malli',
	Unverified: 'Vahvistamaton',
	Upcoming: 'Tuleva',
	UpcomingAppointments: 'Tulevia tapaamisia',
	UpcomingDateOverridesEmpty: 'Päivämäärän ohituksia ei löytynyt',
	UpdateAvailabilityScheduleFailure: 'Saatavuusaikataulun päivittäminen epäonnistui',
	UpdateAvailabilityScheduleSuccess: 'Saatavuusaikataulu päivitetty onnistuneesti',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Haluatko, että uutta hinnoittelua sovelletaan osallistujien laskuihin ja reklamaatioihin?',
	UpdateLink: 'Päivitä linkki',
	UpdatePrimaryEmailWarningDescription:
		'Asiakkaan sähköpostiosoitteen muuttaminen johtaa heidän menettävän pääsyn olemassa oleviin tapaamisiin ja muistiinpanoihin.',
	UpdatePrimaryEmailWarningTitle: 'Asiakkaan sähköpostiosoitteen muutos',
	UpdateSettings: 'Päivitä asetukset',
	UpdateStatus: 'Päivitä tila',
	UpdateSuperbillReceiptFailure: 'Superbill-kuitin päivittäminen epäonnistui',
	UpdateSuperbillReceiptSuccess: 'Superbill-kuitin päivitys onnistui',
	UpdateTaskBillingDetails: 'Päivitä laskutustiedot',
	UpdateTaskBillingDetailsDescription:
		'Tapaamisen hinnoittelu on muuttunut. Haluatko, että uutta hinnoittelua sovelletaan osallistujien laskutuskohteisiin, laskuihin ja reklamaatioihin? Valitse päivitykset, joiden kanssa haluat jatkaa.',
	UpdateTemplateFolderSuccessMessage: 'Kansion päivitys onnistui',
	UpdateUnpaidInvoices: 'Päivitä maksamattomat laskut',
	UpdateUserInfoSuccessSnackbar: 'Käyttäjätiedot päivitetty onnistuneesti!',
	UpdateUserSettingsSuccessSnackbar: 'Käyttäjäasetukset päivitetty onnistuneesti!',
	Upgrade: 'Päivitä',
	UpgradeForSMSReminder: 'Päivitä <b>Professionaliin,</b> niin saat rajattomasti tekstiviestimuistutuksia',
	UpgradeNow: 'Päivitä nyt',
	UpgradePlan: 'Päivityssuunnitelma',
	UpgradeSubscriptionAlertDescription:
		'Varastosi on vähissä. Päivitä suunnitelmasi avaaksesi lisää tallennustilaa ja varmista käytäntösi sujuva toiminta!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Sinulla on vähän tallennustilaa jäljellä. Pyydä jotakuta käytännön henkilökunnasta, jolla on <span>hallinnointioikeudet</span>, päivittämään suunnitelmaasi lisätallennustilan avaamiseksi ja käytännön sujuvan toiminnan varmistamiseksi!',
	UpgradeSubscriptionAlertTitle: 'On aika päivittää tilauksesi',
	UpgradeYourPlan: 'Päivitä suunnitelmasi',
	UploadAudio: 'Lataa ääni',
	UploadFile: 'Lataa tiedosto',
	UploadFileDescription: 'Mistä ohjelmistoplatformista olet vaihtumassa?',
	UploadFileMaxSizeError: 'Tiedosto on liian suuri. Suurin sallittu tiedostokoko on {fileSizeLimit}.',
	UploadFileSizeLimit: 'Koko raja {size} Mt',
	UploadFileTileDescription: 'Käytä CSV-, XLS-, XLSX- tai ZIP-tiedostoja asiakkaiden lataamiseen.',
	UploadFileTileLabel: 'Lataa tiedosto',
	UploadFiles: 'Lataa tiedostoja',
	UploadIndividually: 'Lataa tiedostoja yksitellen',
	UploadLogo: 'Lataa logo',
	UploadPhoto: 'Lataa valokuva',
	UploadToCarepatron: 'Lataa Carepatroniin',
	UploadYourLogo: 'Lataa logosi',
	UploadYourTemplates: 'Lataa mallisi ja me muunnamme ne puolestasi',
	Uploading: 'Lähetetään',
	UploadingAudio: 'Ladataan ääntä...',
	UploadingFiles: 'Tiedostojen lataaminen',
	UrlLink: 'URL-linkki',
	UsageCount: 'Käytetty {count} kertaa',
	UsageLimitValue: '{käytetty} of {raja} käytetty',
	UsageValue: '{käytetty} käytetty',
	Use: 'Käyttää',
	UseAiToAutomateYourWorkflow: 'Käytä tekoälyä automatisoidaksesi työnkulkusi!',
	UseAsDefault: 'Käytä oletuksena',
	UseCustom: 'Käytä mukautettua',
	UseDefault: 'Käytä oletusarvoa',
	UseDefaultFilters: 'Käytä oletussuodattimia',
	UseTemplate: 'Käytä mallia',
	UseThisCard: 'Käytä tätä korttia',
	UseValue: 'Käytä "{value}"',
	UseWorkspaceDefault: 'Käytä työtilan oletusarvoa',
	UserIsTyping: '{name} kirjoittaa...',
	Username: 'Käyttäjätunnus',
	Users: 'Käyttäjät',
	VAT: 'alv.',
	ValidUrl: 'URL-linkin on oltava kelvollinen URL-osoite.',
	Validate: 'Vahvista',
	Validated: 'Hyväksytty',
	Validating: 'Vahvistetaan',
	ValidatingContent: 'Vahvistetaan sisältöä...',
	ValidatingTranscripts: 'Tarkistetaan transkriptioita...',
	ValidationConfirmPasswordRequired: 'Vahvista salasana vaaditaan',
	ValidationDateMax: 'Oltava ennen {max}',
	ValidationDateMin: 'Oltava {min} jälkeen',
	ValidationDateRange: 'Aloitus- ja lopetuspäivät vaaditaan',
	ValidationEndDateMustBeAfterStartDate: 'Loppupäivämäärän on oltava aloituspäivän jälkeen',
	ValidationMixedDefault: 'Tämä on virheellinen',
	ValidationMixedRequired: 'Tämä on pakollinen',
	ValidationNumberInteger: 'Täytyy olla kokonaisluku',
	ValidationNumberMax: 'Oltava {max} tai vähemmän',
	ValidationNumberMin: 'On oltava vähintään {min}.',
	ValidationPasswordNotMatching: 'Salasanat eivät täsmää',
	ValidationPrimaryAddressIsRequired: 'Osoite vaaditaan, kun se on asetettu oletusarvoksi',
	ValidationPrimaryPhoneNumberIsRequired: 'Puhelinnumero vaaditaan, kun se on asetettu oletukseksi',
	ValidationServiceMustBeNotBeFuture: 'Palvelun ei tule olla tämän päivän tai tulevaisuuden',
	ValidationStringEmail: 'Täytyy olla voimassa oleva sähköpostiosoite',
	ValidationStringMax: 'Oltava {max} merkkiä tai vähemmän',
	ValidationStringMin: 'Oltava vähintään {min} merkkiä',
	ValidationStringPhoneNumber: 'On oltava kelvollinen puhelinnumero',
	ValueMinutes: '{value} minuuttia',
	VerbosityConcise: 'Lyhyt',
	VerbosityDetailed: 'Yksityiskohtainen',
	VerbosityStandard: 'Vakio',
	VerbositySuperDetailed: 'Super yksityiskohtainen',
	VerificationCode: 'Vahvistuskoodi',
	VerificationEmailDescription: 'Anna sähköpostiosoitteesi ja vahvistuskoodi, jonka juuri lähetimme sinulle.',
	VerificationEmailSubtitle: 'Tarkista roskapostikansio - jos sähköposti ei ole saapunut',
	VerificationEmailTitle: 'Vahvista sähköpostiosoite',
	VerificationOption: 'Sähköpostin vahvistus',
	Verified: 'Vahvistettu',
	Verify: 'Vahvista',
	VerifyAndSubmit: 'Tarkista ja lähetä',
	VerifyEmail: 'Vahvista sähköposti',
	VerifyEmailAccessCode: 'Vahvistuskoodi',
	VerifyEmailAddress: 'Vahvista sähköpostiosoite',
	VerifyEmailButton: 'Tarkista ja kirjaudu ulos',
	VerifyEmailSentSnackbar: 'Vahvistussähköposti lähetetty. Tarkista postilaatikkosi.',
	VerifyEmailSubTitle: 'Tarkista roskapostikansio, jos sähköposti ei ole saapunut',
	VerifyEmailSuccessLogOutSnackbar: 'Menestys! Kirjaudu ulos ottaaksesi muutokset käyttöön.',
	VerifyEmailSuccessSnackbar: 'Menestys! Sähköposti vahvistettu. Kirjaudu sisään jatkaaksesi vahvistettuna tilinä.',
	VerifyEmailTitle: 'Vahvista sähköpostiosoitteesi',
	VerifyNow: 'Vahvista nyt',
	Veterinarian: 'Eläinlääkäri',
	VideoCall: 'Videopuhelu',
	VideoCallAudioInputFailed: 'Ääni-syöttölaite ei toimi',
	VideoCallAudioInputFailedMessage: 'Avaa asetukset ja tarkista, että mikrofonin lähde on asetettu oikein',
	VideoCallChatBanner: 'Kaikki tämän puhelun osallistujat näkevät viestit, ja ne poistetaan, kun puhelu päättyy.',
	VideoCallChatSendBtn: 'Lähetä viesti',
	VideoCallChatTitle: 'Chat',
	VideoCallDisconnectedMessage: 'Katkaisit verkkoyhteytesi. Yritetään muodostaa yhteys uudelleen',
	VideoCallOptionInfo: 'Carepatron hoitaa tapaamisten videopuhelut, jos Zoomia ei ole yhdistetty',
	VideoCallTilePaused: 'Tämä video on keskeytetty verkko-ongelmien vuoksi',
	VideoCallTranscriptionFormDescription: 'Voit muuttaa näitä asetuksia milloin tahansa',
	VideoCallTranscriptionFormHeading: 'Mukauta tekoälykirjoittajasi',
	VideoCallTranscriptionFormLanguageField: 'Luotu tulostuskieli',
	VideoCallTranscriptionFormNoteTemplateField: 'Aseta oletusmuistiinpanomalli',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Tekoälyä sisältäviä malleja ei löytynyt',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Valitse malli',
	VideoCallTranscriptionPronounField: 'Sinun pronominisi',
	VideoCallTranscriptionRecordingNote:
		'Istunnon lopussa saat <strong>{noteTemplate}-muistiinpanon</strong> ja transkriptin.',
	VideoCallTranscriptionReferClientField: 'Katso asiakas nimellä',
	VideoCallTranscriptionReferPractitionerField: 'Katso harjoittaja as',
	VideoCallTranscriptionTitle: 'AI Scribe',
	VideoCallTranscriptionVerbosityField: 'Monisanaisuus',
	VideoCallTranscriptionWritingPerspectiveField: 'Kirjoittamisen näkökulma',
	VideoCalls: 'Videopuhelut',
	VideoConferencing: 'Videoneuvottelut',
	VideoOff: 'Video on pois päältä',
	VideoOn: 'Video on pois päältä',
	VideoQual360: 'Huono laatu (360p)',
	VideoQual540: 'Keskilaatuinen (540p)',
	VideoQual720: 'Korkea laatu (720p)',
	View: 'Näytä',
	ViewAll: 'Näytä kaikki',
	ViewAppointment: 'Näytä aika',
	ViewBy: 'Näytä',
	ViewClaim: 'Näytä korvausvaatimus',
	ViewCollection: 'Näytä kokoelma',
	ViewDetails: 'Näytä tiedot',
	ViewEnrollment: 'Näytä rekisteröinti',
	ViewPayment: 'Näytä maksu',
	ViewRecord: 'Näytä tietue',
	ViewRemittanceAdvice: 'Näytä maksukuitti',
	ViewRemittanceAdviceHeader: 'Vaatimusten maksun vahvistus',
	ViewRemittanceAdviceSubheader: 'Vaatimus {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Näytä asetukset',
	ViewStripeDashboard: 'Näytä Stripe-hallintapaneeli',
	ViewTemplate: 'Näytä malli',
	ViewTemplates: 'Näytä mallit',
	ViewableBy: 'Näkyy',
	ViewableByHelper:
		'Sinulla ja tiimillä on aina pääsy julkaisemiisi muistiinpanoihin. Voit halutessasi jakaa tämän muistiinpanon asiakkaan ja/tai hänen suhteensa kanssa',
	Viewer: 'Katsoja',
	VirtualLocation: 'Virtuaalinen sijainti',
	VisibleTo: 'Näkyy:',
	VisitOurHelpCentre: 'Käy apukeskuksessamme',
	VisualEffects: 'Visuaaliset tehosteet',
	VoiceFocus: 'Äänen tarkennus',
	VoiceFocusLabel: 'Suodattaa mikrofonistasi äänen, joka ei ole puhetta',
	Void: 'Tyhjä',
	VoidCancelPriorClaim: 'Mitätöi/Peru aiempi korvausvaatimus',
	WaitingforMins: 'Odotetaan {count} min.',
	Warning: 'Varoitus',
	WatchAVideo: 'katso video',
	WatchDemoVideo: 'Katso demovideo',
	WebConference: 'Web-konferenssi',
	WebConferenceOrVirtualLocation: 'Verkkokonferenssi / virtuaalinen sijainti',
	WebDeveloper: 'Web-kehittäjä',
	WebsiteOptional: 'Verkkosivusto <span>(valinnainen)</span>',
	WebsiteUrl: 'Verkkosivuston URL-osoite',
	Wednesday: 'keskiviikko',
	Week: 'Viikko',
	WeekPlural: '{count, plural, one {viikko} other {viikkoa}}',
	Weekly: 'Viikoittain',
	WeeksPlural: '{age, plural, one {# viikko} other {# viikkoa}}',
	WelcomeBack: 'Tervetuloa takaisin',
	WelcomeBackName: 'Tervetuloa takaisin, {name}',
	WelcomeName: 'Tervetuloa {name}',
	WelcomeToCarepatron: 'Tervetuloa Carepatroniin',
	WhatCanIHelpWith: 'Mitä voin auttaa?',
	WhatDidYouLikeResponse: 'Mitä pidit tästä vastauksesta?',
	WhatIsCarepatron: 'Mikä on Carepatron?',
	WhatMadeYouCancel: `Mikä sai sinut perumaan suunnitelmasi?
 Tarkista kaikki sopivat.`,
	WhatServicesDoYouOffer: 'Mitä<mark> palvelut</mark> tarjoatko sinä?',
	WhatServicesDoYouOfferDescription: 'Voit muokata tai lisätä palveluita myöhemmin.',
	WhatsYourAvailability: 'Mikä on <mark>saatavuutesi?</mark>',
	WhatsYourAvailabilityDescription: 'Voit voit lisätä lisää aikatauluja myöhemmin.',
	WhatsYourBusinessName: 'Mikä on sinun<mark> yrityksen nimi?</mark>',
	WhatsYourTeamSize: 'Mikä on sinun<mark> joukkueen koko?</mark>',
	WhatsYourTeamSizeDescription: 'Tämä auttaa meitä määrittämään työtilasi oikein.',
	WhenThisHappens: 'Kun tämä tapahtuu:',
	WhichBestDescribesYou: 'Mikä paras<mark> kuvaa sinua?</mark>',
	WhichPlatforms: 'Mitkä alustat?',
	Wife: 'Vaimo',
	WorkflowDescription: 'Työnkulun kuvaus',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Malleja voidaan linkittää työnkulkeihin sujuvampaa toimintaa varten. Katso linkitettyjä työnkulkuja niiden seuraamista ja päivittämistä varten.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Yhdistä SMS-viestisi + sähköpostisi yhteisten laukaisevien tekijöiden perusteella',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Työvirran automatisoinnit',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Automatisoidut työnkulut',
	WorkflowTemplateConfigKey_Body: 'Ruumis',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Näytä brändäys',
	WorkflowTemplateConfigKey_Content: 'Sisältö',
	WorkflowTemplateConfigKey_Footer: 'Alaviite',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Näytä alatunniste',
	WorkflowTemplateConfigKey_Header: 'Otsikko',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Näytä otsikko',
	WorkflowTemplateConfigKey_SecurityFooter: 'Tietosuojajalka',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Näytä tietoturva-alaosa',
	WorkflowTemplateConfigKey_Subject: 'Aihe',
	WorkflowTemplateConfigKey_Title: 'Otsikko',
	WorkflowTemplateDeleteConfirmationMessage:
		'Oletko varma, että haluat poistaa tämän mallin? Tätä toimintoa ei voi peruuttaa.',
	WorkflowTemplateDeleteConfirmationTitle: 'Poista ilmoitusmalli',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Oletko varma? Tämä poistaa vain {locale}-version – muihin kieliin ei vaikuteta. Toimintaa ei voi peruuttaa.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Poista ‘{locale}’ malli',
	WorkflowTemplateDeletedSuccess: 'Ilmoitusmalli poistettiin onnistuneesti',
	WorkflowTemplateEditorDetailsTab: 'Mallinetiedot',
	WorkflowTemplateEditorEmailContent: 'Sähköpostin sisältö',
	WorkflowTemplateEditorEmailContentTab: 'Sähköpostin sisältö',
	WorkflowTemplateEditorThemeTab: 'Teema',
	WorkflowTemplatePreviewerAlert: 'Esikatselut käyttävät näytetietoja näyttääkseen mitä asiakkaasi näkevät.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Oletko varma? Tämä palauttaa version järjestelmän oletuspohjaan. Tätä toimintoa ei voi peruuttaa.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Nollaa malli',
	WorkflowTemplateSendTestEmail: 'Lähetä testi sähköposti',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Kokeile sähköpostien asetuksiasi lähettämällä testiviesti itsellesi.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Vastaanottajan sähköposti',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Lähetä testi',
	WorkflowTemplateSendTestEmailDialogTitle: 'Lähetä testi sähköposti',
	WorkflowTemplateSendTestEmailSuccess: 'Onnistui! <mark>{templateName}</mark> -testisähköpostisi on lähetetty.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Hallitse malleja ja lisää useita kieliversioita tehokkaaseen viestintään asiakkaiden kanssa.',
	WorkflowTemplateTemplateEditor: 'Mallieditori',
	WorkflowTemplateTranslateLocaleError: 'Sisällön kääntämisessä tapahtui virhe',
	WorkflowTemplateTranslateLocaleSuccess: 'Sisältö käännetty onnistuneesti <strong>{locale}</strong>-kielelle',
	WorkflowsAndReminders: 'Työnkulut ',
	WorkflowsManagement: 'Työnkulkujen hallinta',
	WorksheetAndHandout: 'Työlehti/Käsikirjoitus',
	WorksheetsAndHandoutsDescription: 'Asiakassuhteiden luomiseen ja koulutukseen',
	Workspace: 'Työtila',
	WorkspaceBranding: 'Työtilan brändäys',
	WorkspaceBrandingDescription: `Merkitse työtilasi vaivattomasti yhtenäisellä tyylillä, joka heijastaa sinua
 ammattitaito ja persoonallisuus. Räätälöi laskut verkkovaraukseen kauniista
 asiakaskokemus.`,
	WorkspaceName: 'Työtilan nimi',
	Workspaces: 'Työtilat',
	WriteOff: 'Poisto',
	WriteOffModalDescription:
		'Sinulla on <mark>{count} {count, plural, one {rivi} other {riviä}}</mark> poistettavaksi',
	WriteOffModalTitle: 'Poiston oikaisu',
	WriteOffReasonHelperText: 'Tämä on sisäinen huomautus, eikä se näy asiakkaallesi.',
	WriteOffReasonPlaceholder: 'Poistosyyn lisääminen voi auttaa laskutettavia tapahtumia tarkasteltaessa',
	WriteOffTotal: 'Kokonaan arvonalennus ({currencyCode})',
	Writer: 'Kirjailija',
	Yearly: 'Vuosittain',
	YearsPlural: '{age, plural, one {# vuosi} other {# vuotta}}',
	Yes: 'Kyllä',
	YesArchive: 'Kyllä, arkistoi',
	YesDelete: 'Kyllä, poista',
	YesDeleteOverride: 'Kyllä, poista ohitus',
	YesDeleteSection: 'Kyllä, poista',
	YesDisconnect: 'Kyllä, katkaise yhteys',
	YesEnd: 'Kyllä, loppu',
	YesEndTranscription: 'Kyllä, lopeta transkriptio',
	YesImFineWithThat: 'Kyllä, se sopii minulle',
	YesLeave: 'Kyllä, poistu',
	YesMinimize: 'Kyllä, minimoi',
	YesOrNoAnswerTypeDescription: 'Määritä vastaustyyppi',
	YesOrNoFormPrimaryText: 'Kyllä | Ei',
	YesOrNoFormSecondaryText: 'Valitse kyllä tai ei vaihtoehdot',
	YesProceed: 'Kyllä, jatka',
	YesRemove: 'Kyllä, poista',
	YesRestore: 'Kyllä, palauta',
	YesStopIgnoring: 'Kyllä, lopeta välittäminen',
	YesTransfer: 'Kyllä, siirrä',
	Yesterday: 'Eilen',
	YogaInstructor: 'Jooga-ohjaaja',
	You: 'sinä',
	YouArePresenting: 'Esität',
	YouCanChooseMultiple: 'Voit valita useita',
	YouCanSelectMultiple: 'Voit valita useita',
	YouHaveOngoingTranscription: 'Sinulla on käynnissä transkriptio',
	YourAnswer: 'Sinun vastauksesi',
	YourDisplayName: 'Näyttönimesi',
	YourSpreadsheetColumns: 'Laskentataulukon sarakkeet',
	YourTeam: 'Sinun tiimisi',
	ZipCode: 'Postinumero',
	Zoom: 'Zoomaa',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Et voi lisätä Zoom-puhelua tälle tiimin jäsenelle. Katso <a>lisätietoja tukiasiakirjoista.</a>',
};

export default items;
