import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'AI-aanwijzingen',
	ATeamMemberIsRequired: 'Er is een teamlid nodig',
	AboutClient: 'Over de klant',
	AcceptAppointment: 'Bedankt voor het bevestigen van uw afspraak',
	AcceptTermsAndConditionsRequired: 'Accepteer de voorwaarden ',
	Accepted: 'Geaccepteerd',
	AccessGiven: 'Toegang verleend',
	AccessPermissions: 'Toegangsrechten',
	AccessType: 'Toegangstype',
	Accident: 'Ongeluk',
	Account: 'Rekening',
	AccountCredit: 'Rekeningkrediet',
	Accountant: 'Accountant',
	Action: 'Actie',
	Actions: 'Acties',
	Active: 'Actief',
	ActiveTags: 'Actieve tags',
	ActiveUsers: 'Actieve gebruikers',
	Activity: 'Activiteit',
	Actor: 'Acteur',
	Acupuncture: 'Acupunctuur',
	Acupuncturist: 'Acupuncturist',
	Acupuncturists: 'Acupuncturisten',
	AcuteManifestationOfAChronicCondition: 'Acute manifestatie van een chronische aandoening',
	Add: 'Toevoegen',
	AddADescription: 'Voeg een beschrijving toe',
	AddALocation: 'Locatie toevoegen',
	AddASecondTimezone: 'Een tweede tijdzone toevoegen',
	AddAddress: 'Adres toevoegen',
	AddAnother: '  Voeg nog een toe',
	AddAnotherAccount: 'Nog een account toevoegen',
	AddAnotherContact: 'Voeg een ander contact toe',
	AddAnotherOption: 'Voeg een andere optie toe',
	AddAnotherTeamMember: 'Voeg een ander teamlid toe',
	AddAvailablePayers: '+ Beschikbare betalers toevoegen',
	AddAvailablePayersDescription:
		'Zoek betalers om toe te voegen aan uw werkruimtebetalerslijst. Nadat u ze hebt toegevoegd, kunt u aanmeldingen beheren of de betalergegevens indien nodig aanpassen.',
	AddCaption: 'Bijschrift toevoegen',
	AddClaim: 'Claim toevoegen',
	AddClientFilesModalDescription: `Om de toegang te beperken, kiest u de opties in de selectievakjes 'Zichtbaar voor'`,
	AddClientFilesModalTitle: 'Bestanden uploaden voor {name}',
	AddClientNoteButton: 'Notitie toevoegen',
	AddClientNoteModalDescription: `Voeg inhoud toe aan uw notitie. Gebruik de sectie 'Viewable by' om een of meer groepen te selecteren die deze specifieke notitie kunnen zien.`,
	AddClientNoteModalTitle: 'Notitie toevoegen',
	AddClientOwnerRelationshipModalDescription:
		'Als u de klant uitnodigt, kan deze zijn eigen profielgegevens beheren en de toegang van gebruikers tot zijn profielgegevens beheren.',
	AddClientOwnerRelationshipModalTitle: 'Nodig de klant uit',
	AddCode: 'Code toevoegen',
	AddColAfter: 'Kolom toevoegen na',
	AddColBefore: 'Kolom toevoegen voor',
	AddCollection: 'Collectie toevoegen',
	AddColor: 'Kleur toevoegen',
	AddColumn: 'Kolom toevoegen',
	AddContactRelationship: 'Contactrelatie toevoegen',
	AddContacts: 'Contacten toevoegen',
	AddCustomField: 'Aangepast veld toevoegen',
	AddDate: 'Datum toevoegen',
	AddDescription: 'Voeg beschrijving toe',
	AddDetail: 'Voeg details toe',
	AddDisplayName: 'Weergavenaam toevoegen',
	AddDxCode: 'Diagnosecode toevoegen',
	AddEmail: 'E-mailadres toevoegen',
	AddFamilyClientRelationshipModalDescription:
		'Door een familielid uit te nodigen, kunnen ze zorgverhalen en de profielinformatie van de cliënt bekijken. Als ze als beheerders worden uitgenodigd, hebben ze toegang om de profielinformatie van de cliënt bij te werken en gebruikerstoegang te beheren.',
	AddFamilyClientRelationshipModalTitle: 'Nodig een familielid uit',
	AddField: 'Veld toevoegen',
	AddFormField: 'Formulierveld toevoegen',
	AddImages: 'Afbeeldingen toevoegen',
	AddInsurance: 'Verzekering toevoegen',
	AddInvoice: 'Factuur toevoegen',
	AddLabel: 'Label toevoegen',
	AddLanguage: 'Taal toevoegen',
	AddLocation: 'Locatie toevoegen',
	AddManually: 'Handmatig toevoegen',
	AddMessage: 'Bericht toevoegen',
	AddNewAction: 'Nieuwe actie toevoegen',
	AddNewSection: 'Nieuwe sectie toevoegen',
	AddNote: 'Notitie toevoegen',
	AddOnlineBookingDetails: 'Voeg online boekingsgegevens toe',
	AddPOS: 'POS toevoegen',
	AddPaidInvoices: 'Betaalde facturen toevoegen',
	AddPayer: 'Betaler toevoegen',
	AddPayment: 'Betaling toevoegen',
	AddPaymentAdjustment: 'Betalingsaanpassing toevoegen',
	AddPaymentAdjustmentDisabledDescription: 'De betalingstoewijzingen worden niet gewijzigd.',
	AddPaymentAdjustmentEnabledDescription: 'Het beschikbare bedrag voor toewijzing wordt verlaagd.',
	AddPhoneNumber: 'Voeg telefoonnummer toe',
	AddPhysicalOrVirtualLocations: 'Fysieke of virtuele locaties toevoegen',
	AddQuestion: 'Vraag toevoegen',
	AddQuestionOrTitle: 'Voeg een vraag of titel toe',
	AddRelationship: 'Relatie toevoegen',
	AddRelationshipModalTitle: 'Bestaande contacten verbinden',
	AddRelationshipModalTitleNewClient: 'Nieuw contact verbinden',
	AddRow: 'Rij toevoegen',
	AddRowAbove: 'Rij boven toevoegen',
	AddRowBelow: 'Rij hieronder toevoegen',
	AddService: 'Service toevoegen',
	AddServiceLocation: 'Servicelocatie toevoegen',
	AddServiceToCollections: 'Service toevoegen aan collecties',
	AddServiceToOneOrMoreCollections: 'Voeg een service toe aan een of meer collecties',
	AddServices: 'Diensten toevoegen',
	AddSignature: 'Handtekening toevoegen',
	AddSignaturePlaceholder: 'Typ aanvullende details die u bij uw handtekening wilt voegen',
	AddSmartDataChips: 'Voeg slimme datachips toe',
	AddStaffClientRelationshipsModalDescription:
		'Door personeel te selecteren kunnen ze zorgverhalen voor deze cliënt maken en bekijken. Ze kunnen ook cliëntinformatie bekijken.',
	AddStaffClientRelationshipsModalTitle: 'Voeg personeelsrelaties toe',
	AddTag: 'Voeg een tag toe',
	AddTags: 'Labels toevoegen',
	AddTemplate: 'Sjabloon toevoegen',
	AddTimezone: 'Tijdzone toevoegen',
	AddToClaim: 'Toevoegen aan claim',
	AddToCollection: 'Toevoegen aan collectie',
	AddToExisting: 'Toevoegen aan bestaande',
	AddToStarred: 'Toevoegen aan ster',
	AddUnclaimedItems: 'Niet-geclaimde items toevoegen',
	AddUnrelatedContactWarning:
		'Je hebt iemand toegevoegd die geen contact is van {contact}. Zorg ervoor dat de inhoud relevant is voordat je doorgaat met delen.',
	AddValue: 'Voeg "{value}" toe',
	AddVideoCall: 'Videogesprek toevoegen',
	AddVideoOrVoiceCall: 'Video- of spraakoproep toevoegen',
	AddictionCounselor: 'Verslavingsadviseur',
	AddingManualPayerDisclaimer:
		'Als u een betaler handmatig toevoegt aan uw lijst met zorgverleners, wordt er geen elektronische verbinding voor het indienen van claims met die betaler tot stand gebracht. U kunt de claims wel handmatig aanmaken.',
	AddingTeamMembersIncreaseCostAlert:
		'Als u nieuwe teamleden toevoegt, wordt uw maandelijkse abonnementsbedrag verhoogd.',
	Additional: 'Aanvullend',
	AdditionalBillingProfiles: 'Extra factureringsprofielen',
	AdditionalBillingProfilesSectionDescription:
		'Overschrijf de standaardfactuurgegevens die worden gebruikt voor specifieke teamleden, betalers of factuursjablonen.',
	AdditionalFeedback: 'Aanvullende feedback',
	AddnNewWorkspace: 'Nieuwe werkruimte',
	AddnNewWorkspaceSuccessSnackbar: 'Werkruimte is aangemaakt!',
	Address: 'Adres',
	AddressNumberStreet: 'Adres (nr., straat)',
	Adjustment: 'Aanpassing',
	AdjustmentType: 'Aanpassingstype',
	Admin: 'Beheerder',
	Admins: 'Beheerders',
	AdminsOnly: 'Alleen beheerders',
	AdvancedPlanInclusionFive: 'Accountmanager',
	AdvancedPlanInclusionFour: 'Google-analyse',
	AdvancedPlanInclusionHeader: 'Alles in Plus  ',
	AdvancedPlanInclusionOne: 'Rollen ',
	AdvancedPlanInclusionSix: 'Ondersteuning voor gegevensimport',
	AdvancedPlanInclusionThree: 'Wit labelen',
	AdvancedPlanInclusionTwo: '90 dagen verwijderde gegevensbehoud',
	AdvancedPlanMessage:
		'Houd de controle over de behoeften van uw praktijk. Bekijk uw huidige plan en controleer het gebruik.',
	AdvancedSettings: 'Geavanceerde instellingen',
	AdvancedSubscriptionPlanSubtitle: 'Breid uw praktijk uit met alle functies',
	AdvancedSubscriptionPlanTitle: 'Geavanceerd',
	AdvertisingManager: 'Advertentiemanager',
	AerospaceEngineer: 'Luchtvaartingenieur',
	AgeYearsOld: '{age} jaar oud',
	Agenda: 'Agenda',
	AgendaView: 'Agendaweergave',
	AiAskSupportedFileTypes: 'Ondersteunde bestandstypen: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Een assistent binnen handbereik',
	AiCopilotDisclaimer: 'AI Copilot kan fouten maken. Controleer belangrijke info.',
	AiCreateNewConversation: 'Nieuw gesprek starten',
	AiEnhanceYourProductivity: 'Verbeter uw productiviteit',
	AiPoweredTemplates: 'AI-aangedreven sjablonen',
	AiScribeNoDeviceFoundErrorMessage:
		'Het lijkt erop dat uw browser deze functie niet ondersteunt, of dat er geen compatibele apparaten beschikbaar zijn.',
	AiScribeUploadFormat: 'Ondersteunde bestandstypen: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'slechts 1 bestand tegelijk',
	AiShowConversationHistory: 'Gesprekgeschiedenis weergeven',
	AiSmartPromptNodePlaceholderText:
		'Typ hier uw aangepaste prompt in om nauwkeurige en persoonlijke AI-resultaten te genereren.',
	AiSmartPromptPrimaryText: 'Ai slimme prompt',
	AiSmartPromptSecondaryText: 'Voeg aangepaste AI slimme prompt in',
	AiSmartReminders: 'Slimme AI-herinneringen',
	AiTemplateBannerTitle: 'Vereenvoudig uw werk met AI-gestuurde sjablonen',
	AiTemplates: 'AI-sjablonen',
	AiTokens: 'AI-tokens',
	AiWorkBetterWithAi: 'Werk beter met AI',
	All: 'Alle',
	AllAppointments: 'Alle afspraken',
	AllCategories: 'Alle categorieën',
	AllClients: 'Alle klanten',
	AllContactPolicySelectorLabel: 'Alle contacten van <mark>{client}</mark>',
	AllContacts: 'Alle contacten',
	AllContactsOf: 'Alle contacten van ‘{name}’',
	AllDay: 'Hele dag',
	AllInboxes: 'Alle inboxen',
	AllIndustries: 'Alle industrieën',
	AllLocations: 'Alle locaties',
	AllMeetings: 'Alle bijeenkomsten',
	AllNotificationsRestoredMessage: 'Alle meldingen hersteld',
	AllProfessions: 'Alle beroepen',
	AllReminders: 'Alle herinneringen',
	AllServices: 'Alle diensten',
	AllStatuses: 'Alle statussen',
	AllTags: 'Alle tags',
	AllTasks: 'Alle taken',
	AllTeamMembers: 'Alle teamleden',
	AllTypes: 'Alle soorten',
	Allocated: 'Toegewezen',
	AllocatedItems: 'Toegewezen items',
	AllocationTableEmptyState: 'Geen betalingstoewijzingen gevonden',
	AllocationTotalWarningMessage: `Het toegewezen bedrag overschrijdt het totale te betalen bedrag.
 Bekijk de onderstaande posten.`,
	AllowClientsToCancelAnytime: 'Klanten de mogelijkheid bieden om op elk gewenst moment te annuleren',
	AllowNewClient: 'Nieuwe klanten toestaan',
	AllowNewClientHelper: 'Nieuwe klanten kunnen deze service boeken',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Reken op minimaal {hours} uur voor de afspraak',
	AllowToUseSavedCard: 'Sta {provider} toe om de opgeslagen kaart in de toekomst te gebruiken',
	AllowVideoCalls: 'Videogesprekken toestaan',
	AlreadyAdded: 'Al reeds toegevoegd',
	AlreadyHasAccess: 'Heeft toegang',
	AlreadyHasAccount: 'Heeft u al een account?',
	Always: 'Altijd',
	AlwaysIgnore: 'Altijd negeren',
	Amount: 'Hoeveelheid',
	AmountDue: 'Te betalen bedrag',
	AmountOfReferralRequests: '{amount, plural, one {# verwijzingsverzoek} other {# verwijzingsverzoeken}}',
	AmountPaid: 'Betaald bedrag',
	AnalyzingAudio: 'Audio analyseren...',
	AnalyzingInputContent: 'Invoerinhoud analyseren...',
	AnalyzingRequest: 'Verzoek analyseren...',
	AnalyzingTemplateContent: 'Sjablooninhoud analyseren...',
	And: 'En',
	Annually: 'Jaarlijks',
	Anonymous: 'Anoniem',
	AnswerExceeded: 'Uw antwoord moet korter zijn dan 300 tekens.',
	AnyStatus: 'Elke status',
	AppNotifications: 'Meldingen',
	AppNotificationsClearanceHeading: 'Goed werk! Je hebt alle activiteit gewist',
	AppNotificationsEmptyHeading: 'Uw werkruimteactiviteit zal hier binnenkort verschijnen',
	AppNotificationsEmptySubtext: 'Er zijn momenteel geen acties die ondernomen moeten worden',
	AppNotificationsIgnoredCount: '{total} genegeerd',
	AppNotificationsUnread: '{total} ongelezen',
	Append: 'Toevoegen',
	Apply: 'Toepassen',
	ApplyAccountCredit: 'Rekeningkrediet aanvragen',
	ApplyDiscount: 'Korting toepassen',
	ApplyVisualEffects: 'Visuele effecten toepassen',
	ApplyVisualEffectsNotSupported: 'Visuele effecten toepassen wordt niet ondersteund',
	Appointment: 'Afspraak',
	AppointmentAssignedNotificationSubject: '{actorProfileName} heeft je {appointmentName} toegewezen',
	AppointmentCancelledNotificationSubject: '{actorProfileName} heeft {appointmentName} geannuleerd',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} heeft {appointmentName} bevestigd',
	AppointmentDetails: 'Afspraakgegevens',
	AppointmentLocation: 'Afspraaklocatie',
	AppointmentLocationDescription:
		'Beheer uw standaard virtuele en fysieke locaties. Wanneer een afspraak wordt ingepland, worden deze locaties automatisch toegepast.',
	AppointmentNotFound: 'Afspraak niet gevonden',
	AppointmentReminder: 'Herinnering afspraak',
	AppointmentReminders: 'Afspraakherinneringen',
	AppointmentRemindersInfo:
		'Stel automatische herinneringen in voor afspraken met cliënten om no-shows en annuleringen te voorkomen',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} heeft {appointmentName} verplaatst',
	AppointmentSaved: 'Afspraak opgeslagen',
	AppointmentStatus: 'Benoemingsstatus',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}uur {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}uur {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Afspraak ongedaan gemaakt',
	Appointments: 'Afspraken',
	Archive: 'Archief',
	ArchiveClients: 'Archiefklanten',
	Archived: 'Gearchiveerd',
	AreYouAClient: 'Bent u een klant?',
	AreYouStillThere: 'Ben je er nog?',
	AreYouSure: 'Weet je het zeker?',
	Arrangements: 'Regelingen',
	ArtTherapist: 'Kunsttherapeut',
	Articles: 'Artikelen',
	Artist: 'Artiest',
	AskAI: 'Vraag AI',
	AskAiAddFormField: 'Een formulierveld toevoegen',
	AskAiChangeFormality: 'Verander formaliteit',
	AskAiChangeToneToBeMoreProfessional: 'Verander de toon om professioneler over te komen',
	AskAiExplainThis: 'VraagAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Leg uit waar dit document over gaat',
	AskAiExplainWhatThisImageIsAbout: 'Leg uit waar deze afbeelding over gaat',
	AskAiFixSpellingAndGrammar: 'Spelling en grammatica corrigeren',
	AskAiGenerateACaptionForThisImage: 'Genereer een onderschrift voor deze afbeelding',
	AskAiGenerateFromThisPage: 'Genereren vanaf deze pagina',
	AskAiGetStarted: 'Aan de slag',
	AskAiGiveItAFriendlyTone: 'Geef het een vriendelijke toon',
	AskAiGreeting: 'Hoi {firstName}! Hoe kan ik je vandaag helpen?',
	AskAiHowCanIHelpWithYourContent: 'Hoe kan ik u helpen met uw content?',
	AskAiInsert: 'Invoegen',
	AskAiMakeItMoreCasual: 'Maak het wat informeler',
	AskAiMakeThisTextMoreConcise: 'Maak deze tekst bondiger',
	AskAiMoreProfessional: 'Professioneler',
	AskAiOpenPreviousNote: 'Open vorige notitie',
	AskAiPondering: 'Nadenken',
	AskAiReplace: 'Vervangen',
	AskAiReviewOrEditSelection: 'Selectie bekijken of bewerken',
	AskAiRuminating: 'Herkauwen',
	AskAiSeeMore: 'Bekijk meer',
	AskAiSimplifyLanguage: 'Vereenvoudig taal',
	AskAiSomethingWentWrong:
		'Er is iets mis gegaan. Als dit probleem aanhoudt, neem dan contact met ons op via ons helpcenter.',
	AskAiStartWithATemplate: 'Begin met een sjabloon',
	AskAiSuccessfullyCopiedResponse: 'AI-reactie succesvol gekopieerd',
	AskAiSuccessfullyInsertedResponse: 'AI-reactie succesvol ingevoegd',
	AskAiSuccessfullyReplacedResponse: 'AI-respons succesvol vervangen',
	AskAiSuggested: 'Voorgesteld',
	AskAiSummariseTextIntoBulletPoints: 'Vat de tekst samen in opsommingstekens',
	AskAiSummarizeNote: 'Samenvatting notitie',
	AskAiThinking: 'Denken',
	AskAiToday: 'Vandaag {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Wat wilt u met dit formulier doen?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Schrijf een professionele notitie met behulp van de sjabloon',
	AskAskAiAnything: 'Vraag AI alles',
	AskWriteSearchAnything: `Stel een vraag, typ '@' of zoek naar iets...`,
	Asking: 'Vragen',
	Assessment: 'Onderzoek',
	Assessments: 'Beoordelingen',
	AssessmentsCategoryDescription: 'Voor het vastleggen van cliëntevaluaties',
	AssignClients: 'Klanten toewijzen',
	AssignNewClients: 'Klanten toewijzen',
	AssignServices: 'Diensten toewijzen',
	AssignTeam: 'Team toewijzen',
	AssignTeamMember: 'Teamlid toewijzen',
	Assigned: 'Toegewezen',
	AssignedClients: 'Toegewezen klanten',
	AssignedServices: 'Toegewezen diensten',
	AssignedServicesDescription:
		'Bekijk en beheer de aan u toegewezen services en pas de prijzen aan op basis van uw eigen tarieven. ',
	AssignedTeam: 'Toegewezen team',
	AthleticTrainer: 'Atletiektrainer',
	AttachFiles: 'Bestanden bijvoegen',
	AttachLogo: 'Bijvoegen',
	Attachment: 'Bijlage',
	AttachmentBlockedFileType: 'Geblokkeerd om veiligheidsredenen!',
	AttachmentTooLargeFileSize: 'Bestand te groot',
	AttachmentUploadItemComplete: 'Compleet',
	AttachmentUploadItemError: 'Uploaden mislukt',
	AttachmentUploadItemLoading: 'Laden',
	AttemptingToReconnect: 'Poging tot opnieuw verbinden...',
	Attended: 'Bijgewoond',
	AttendeeBeingMutedTooltip: `Host heeft je gedempt. Gebruik 'raise hand' om het dempen ongedaan te maken.`,
	AttendeeWithId: 'Deelnemer {attendeeId}',
	Attendees: 'Aanwezigen',
	AttendeesCount: '{count} deelnemers',
	Attending: 'Aanwezig',
	Audiologist: 'Audioloog',
	Aunt: 'Tante',
	Australia: 'Australië',
	AuthenticationCode: 'Authenticatiecode',
	AuthoriseProvider: 'Autoriseer {provider}',
	AuthorisedProviders: 'Geautoriseerde aanbieders',
	AutoDeclineAllFutureOption: 'Alleen nieuwe evenementen of afspraken',
	AutoDeclineAllOption: 'Nieuwe en bestaande evenementen of afspraken',
	AutoDeclinePrimaryText: 'Automatisch evenementen afwijzen',
	AutoDeclineSecondaryText: 'Evenementen tijdens uw afwezigheid worden automatisch geweigerd',
	AutogenerateBillings: 'Automatisch factuurdocumenten genereren',
	AutogenerateBillingsDescription:
		'Geautomatiseerde factureringsdocumenten worden gegenereerd op de laatste dag van de maand. Facturen en superbill-ontvangsten kunnen op elk gewenst moment handmatig worden gemaakt.',
	AutomateWorkflows: 'Automatiseer workflows',
	AutomaticallySendSuperbill: 'Automatisch superfactuurontvangsten versturen',
	AutomaticallySendSuperbillHelperText:
		'Een superbill is een gedetailleerde bon van de diensten die aan een klant zijn geleverd voor vergoeding door de verzekering.',
	Automation: 'Automatisering',
	AutomationActionSendEmailLabel: 'E-mail verzenden',
	AutomationActionSendSMSLabel: 'Stuur SMS',
	AutomationAndReminders: 'Automatisering ',
	AutomationDeletedSuccessMessage: 'Automatisering succesvol verwijderd',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Tijdgebeurtenis',
	AutomationParams_timeUnit: 'Eenheid',
	AutomationParams_timeValue: 'Nummer',
	AutomationPublishSuccessMessage: 'Automatisering succesvol gepubliceerd',
	AutomationPublishWarningTooltip:
		'Controleer de automatiseringsconfiguratie opnieuw en zorg ervoor dat deze correct is geconfigureerd',
	AutomationTriggerEventCancelledDescription:
		'Wordt geactiveerd wanneer een evenement wordt geannuleerd of verwijderd',
	AutomationTriggerEventCancelledLabel: 'Evenement geannuleerd',
	AutomationTriggerEventCreatedDescription: 'Wordt geactiveerd wanneer een gebeurtenis wordt gemaakt',
	AutomationTriggerEventCreatedLabel: 'Nieuw evenement',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Wordt geactiveerd wanneer een gebeurtenis wordt gemaakt of bijgewerkt (behalve wanneer deze wordt geannuleerd)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Nieuw of bijgewerkt evenement',
	AutomationTriggerEventEndedDescription: 'Wordt geactiveerd wanneer een gebeurtenis eindigt',
	AutomationTriggerEventEndedLabel: 'Evenement afgelopen',
	AutomationTriggerEventStartsDescription:
		'Wordt geactiveerd wanneer een bepaalde tijd verstrijkt voordat een gebeurtenis begint',
	AutomationTriggerEventStartsLabel: 'Evenement start',
	Automations: 'Automatiseringen',
	Availability: 'Beschikbaarheid',
	AvailabilityDisableSchedule: 'Schema uitschakelen',
	AvailabilityDisabled: 'Gehandicapt',
	AvailabilityEnableSchedule: 'Schema inschakelen',
	AvailabilityEnabled: 'Ingeschakeld',
	AvailabilityNoActiveBanner: `U hebt al uw schema's uitgeschakeld. Klanten kunnen u niet online boeken en alle toekomstige afspraken moeten handmatig worden bevestigd.`,
	AvailabilityNoActiveConfirmationDescription: `Als u deze beschikbaarheid uitschakelt, heeft u geen actieve schema's. Cliënten kunnen u niet online boeken en boekingen van beoefenaars vallen buiten uw werkuren.`,
	AvailabilityNoActiveConfirmationProceed: 'Ja, doorgaan',
	AvailabilityNoActiveConfirmationTitle: `Geen actieve schema's`,
	AvailabilityToggle: 'Ingeschakeld schema',
	AvailabilityUnsetDate: 'Geen datum vastgesteld',
	AvailableLocations: 'Beschikbare locaties',
	AvailablePayers: 'Beschikbare betalers',
	AvailablePayersEmptyState: 'Geen betalers geselecteerd',
	AvailableTimes: 'Beschikbare tijden',
	Back: 'Rug',
	BackHome: 'Terug naar huis',
	BackToAppointment: 'Terug naar afspraak',
	BackToLogin: 'Terug naar inloggen',
	BackToMapColumns: 'Terug naar Kaartkolommen',
	BackToTemplates: 'Terug naar sjablonen',
	BackToUploadFile: 'Terug naar bestand uploaden',
	Banker: 'Bankier',
	BasicBlocks: 'Basisblokken',
	BeforeAppointment: 'Stuur {deliveryType} herinnering {interval} {unit} voor afspraak',
	BehavioralAnalyst: 'Gedragsanalist',
	BehavioralHealthTherapy: 'Gedragsmatige gezondheidstherapie',
	Beta: 'Bèta',
	BillTo: 'Rekening aan',
	BillableItems: 'Factureerbare items',
	BillableItemsEmptyState: 'Er zijn geen factureerbare items gevonden',
	Biller: 'Factureerder',
	Billing: 'Facturering',
	BillingAddress: 'Factuuradres',
	BillingAndReceiptsUnauthorisedMessage:
		'Om toegang te krijgen tot deze informatie, is het recht om facturen en betalingen te bekijken vereist.',
	BillingBillablesTab: 'Factureerbare bedragen',
	BillingClaimsTab: 'Claims',
	BillingDetails: 'Factureringsgegevens',
	BillingDocuments: 'Facturatiedocumenten',
	BillingDocumentsClaimsTab: 'Claims',
	BillingDocumentsEmptyState: 'Geen {tabType} zijn gevonden',
	BillingDocumentsInvoicesTab: 'Facturen',
	BillingDocumentsSuperbillsTab: 'Superrekeningen',
	BillingInformation: 'Factureringsinformatie',
	BillingInvoicesTab: 'Facturen',
	BillingItems: 'Factureringsitems',
	BillingPaymentsTab: 'Betalingen',
	BillingPeriod: 'Facturatieperiode',
	BillingProfile: 'Factureringsprofiel',
	BillingProfileOverridesDescription: 'Beperk het gebruik van deze factuurprofiel tot specifieke teamleden',
	BillingProfileOverridesHeader: 'Toegang beperken',
	BillingProfileProviderType: 'Leverancierstype',
	BillingProfileTypeIndividual: 'Beoefenaar',
	BillingProfileTypeIndividualSubLabel: 'Type 1 NPI',
	BillingProfileTypeOrganisation: 'Organisatie',
	BillingProfileTypeOrganisationSubLabel: 'Type 2 NPI',
	BillingProfiles: 'Factureringsprofielen',
	BillingProfilesEditHeader: 'Bewerk {name} factureringsgegevens',
	BillingProfilesNewHeader: 'Nieuw factureringsprofiel',
	BillingProfilesSectionDescription:
		'Beheer uw factureringsgegevens voor artsen en verzekeringsbetalers door factureringsprofielen in te stellen die kunnen worden toegepast op facturen en verzekeringsuitkeringen.',
	BillingSearchPlaceholder: 'Zoek items',
	BillingSettings: 'Factureringsinstellingen',
	BillingSuperbillsTab: 'Superrekeningen',
	BiomedicalEngineer: 'Biomedisch ingenieur',
	BlankInvoice: 'Blanco factuur',
	BlueShieldProviderNumber: 'Blue Shield-providernummer',
	Body: 'Lichaam',
	Bold: 'Vetgedrukt',
	BookAgain: 'Opnieuw boeken',
	BookAppointment: 'Afspraak maken',
	BookableOnline: 'Online te boeken',
	BookableOnlineHelper: 'Klanten kunnen deze service online boeken',
	BookedOnline: 'Online geboekt',
	Booking: 'Reservering',
	BookingAnalyticsIntegrationPanelDescription:
		'Stel Google Tag Manager in om belangrijke acties en conversies in uw online boekingsstroom bij te houden. Verzamel waardevolle gegevens over gebruikersinteracties om marketinginspanningen te verbeteren en de boekingservaring te optimaliseren.',
	BookingAnalyticsIntegrationPanelTitle: 'Analytics-integratie',
	BookingAndCancellationPolicies: 'Reservering ',
	BookingButtonEmbed: 'Knop',
	BookingButtonEmbedDescription: 'Voegt een online boekingsknop toe aan uw website',
	BookingDirectTextLink: 'Directe tekstlink',
	BookingDirectTextLinkDescription: 'Opent de online boekingspagina',
	BookingFormatLink: 'Formaat link',
	BookingFormatLinkButtonTitle: 'Knoptitel',
	BookingInlineEmbed: 'Inline insluiten',
	BookingInlineEmbedDescription: 'Laadt de online boekingspagina rechtstreeks op uw website',
	BookingLink: 'Boekingslink',
	BookingLinkModalCopyText: 'Kopiëren',
	BookingLinkModalDescription: 'Geef klanten met deze link de mogelijkheid om teamleden of diensten te boeken',
	BookingLinkModalHelpText: 'Leer hoe u online boekingen kunt instellen',
	BookingLinkModalTitle: 'Deel uw boekingslink',
	BookingPolicies: 'Boekingsvoorwaarden',
	BookingPoliciesDescription: 'Instellen wanneer online boekingen door klanten kunnen worden gemaakt',
	BookingTimeUnitDays: 'dagen',
	BookingTimeUnitHours: 'uren',
	BookingTimeUnitMinutes: 'notulen',
	BookingTimeUnitMonths: 'maanden',
	BookingTimeUnitWeeks: 'weken',
	BottomNavBilling: 'Facturering',
	BottomNavGettingStarted: 'Thuis',
	BottomNavMore: 'Meer',
	BottomNavNotes: 'Notities',
	Brands: 'Merken',
	Brother: 'Broer',
	BrotherInLaw: 'Schoonbroer',
	BrowseOrDragFileHere: '<link>Bladeren</link> of sleep bestand hier',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} min na',
	BufferAndLabel: 'En',
	BufferAppointmentLabel: 'een afspraak',
	BufferBeforeTime: '{time} min voor',
	BufferTime: 'Buffertijd',
	BufferTimeViewLabel: '{bufferBefore} min voor en {bufferAfter} min na afspraken',
	BulkArchiveClientsDescription:
		'Weet u zeker dat u deze clients wilt archiveren? U kunt ze later opnieuw activeren.',
	BulkArchiveSuccess: 'Succesvol gearchiveerde klanten',
	BulkArchiveUndone: 'Bulkarchief ongedaan gemaakt',
	BulkPermanentDeleteDescription:
		'Dit zal <strong>{count} gesprekken</strong> verwijderen. Deze actie kan niet ongedaan gemaakt worden.',
	BulkPermanentDeleteTitle: 'Gesprekken permanent verwijderen',
	BulkUnarchiveSuccess: 'Clients succesvol uit het archief gehaald',
	BulletedList: 'Opsommingstekenlijst',
	BusinessAddress: 'Zakelijk adres',
	BusinessAddressOptional: 'Zakelijk adres <span>(optioneel)</span>',
	BusinessName: 'Bedrijfsnaam',
	Button: 'Knop',
	By: 'Door',
	CHAMPUSIdentificationNumber: 'CHAMPUS-identificatienummer',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC is vereist',
	Calendar: 'Kalender',
	CalendarAppSyncFormDescription: 'Synchroniseer Carepatron-gebeurtenissen met',
	CalendarAppSyncPanelTitle: 'Verbonden app-synchronisatie',
	CalendarDescription: 'Beheer uw afspraken of stel persoonlijke taken en herinneringen in',
	CalendarDetails: 'Kalenderdetails',
	CalendarDetailsDescription: 'Beheer uw agenda- en afspraakweergave-instellingen.',
	CalendarScheduleNew: 'Nieuw schema',
	CalendarSettings: 'Kalenderinstellingen',
	Call: 'Telefoongesprek',
	CallAttendeeJoinAttemptedNotificationSubject:
		'<strong>{attendeeName}</strong> heeft deelgenomen aan het videogesprek',
	CallChangeLayoutTextContent: 'Selectie wordt bewaard voor toekomstige vergaderingen',
	CallIdlePrompt: 'Wilt u nog even wachten met deelnemen of wilt u het later nog eens proberen?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Zijbalk',
	CallLayoutOptionSpotlight: 'In de schijnwerpers',
	CallLayoutOptionTiled: 'Betegeld',
	CallNoAttendees: 'Er waren geen aanwezigen bij de vergadering.',
	CallSessionExpiredError: 'Sessie verlopen. Oproep is beëindigd. Probeer opnieuw deel te nemen.',
	CallWithPractitioner: 'Bel met {practitioner}',
	CallsListCreateButton: 'Nieuwe oproep',
	CallsListEmptyState: 'Geen actieve oproepen',
	CallsListItemEndCall: 'Einde gesprek',
	CamWarningMessage: 'Er is een probleem gedetecteerd met uw camera',
	Camera: 'Camera',
	CameraAndMicIssueModalDescription: `Geef Carepatron toegang tot uw camera en microfoon.
 Voor meer informatie, <a>volg deze gids</a>`,
	CameraAndMicIssueModalTitle: 'Camera en microfoon zijn geblokkeerd',
	CameraQuality: 'Camerakwaliteit',
	CameraSource: 'Camerabron',
	CanModifyReadOnlyEvent: 'Je kunt deze gebeurtenis niet wijzigen',
	Canada: 'Canada',
	Cancel: 'Annuleren',
	CancelClientImportDescription: 'Weet je zeker dat je deze import wilt annuleren?',
	CancelClientImportPrimaryAction: 'Ja, import annuleren',
	CancelClientImportSecondaryAction: 'Blijf bewerken',
	CancelClientImportTitle: 'Annuleer het importeren van cliënten',
	CancelImportButton: 'Import annuleren',
	CancelPlan: 'Plan annuleren',
	CancelPlanConfirmation: `Als u het abonnement opzegt, worden eventuele openstaande bedragen voor deze maand automatisch van uw rekening afgeschreven.
 Als u uw gefactureerde gebruikers wilt downgraden, kunt u eenvoudig teamleden verwijderen. Carepatron zal dan automatisch uw abonnementsprijs bijwerken.`,
	CancelSend: 'Annuleren verzenden',
	CancelSubscription: 'Abonnement opzeggen',
	Canceled: 'Gecannceled',
	CancellationPolicy: 'Annuleringsbeleid',
	Cancelled: 'Geannuleerd',
	CannotContainSpecialCharactersError: 'Kan geen {specialCharacters} bevatten',
	CannotDeleteInvoice: 'Facturen die via online betalingen zijn betaald, kunnen niet worden verwijderd',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> kan niet uit de <b>{group}</b> groep worden verplaatst',
	CannotMoveServiceOutsideCollections: 'Service kan niet buiten de collecties worden verplaatst',
	CapeTown: 'Kaapstad',
	Caption: 'Ondertiteling',
	CaptureNameFieldLabel: 'De naam die u wilt dat anderen naar u verwijzen',
	CapturePaymentMethod: 'Betaalmethode vastleggen',
	CapturingAudio: 'Audio vastleggen',
	CapturingSignature: 'Handtekening vastleggen...',
	CardInformation: 'Kaartinformatie',
	CardNumberRequired: 'Kaartnummer is vereist',
	CardiacRehabilitationSpecialist: 'Specialist in hartrevalidatie',
	Cardiologist: 'Cardioloog',
	CareAiNoConversations: 'Nog geen gesprekken',
	CareAiNoConversationsDescription: 'Start een gesprek met {aiName} om te beginnen',
	CareAssistant: 'Zorgassistent',
	CareManager: 'Zorgmanager',
	Caregiver: 'Verzorger',
	CaregiverCreateModalDescription:
		'Door medewerkers als beheerders toe te voegen, kunnen ze zorgverhalen maken en beheren. Het geeft ze ook volledige toegang om cliënten te maken en beheren.',
	CaregiverCreateModalTitle: 'Nieuw teamlid',
	CaregiverListCantAddStaffInfoTitle:
		'U hebt het maximum aantal medewerkers voor uw abonnement bereikt. Upgrade uw plan om meer medewerkers toe te voegen.',
	CaregiverListCreateButton: 'Nieuw teamlid',
	CaregiverListEmptyState: 'Geen verzorgers toegevoegd',
	CaregiversListItemRemoveStaff: 'Personeel verwijderen',
	CarepatronApp: 'Carepatron-app',
	CarepatronCommunity: 'Gemeenschap',
	CarepatronFieldAddress: 'Adres',
	CarepatronFieldAssignedStaff: 'Toegewezen personeel',
	CarepatronFieldBirthDate: 'Geboortedatum',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: 'Werkstatus',
	CarepatronFieldEthnicity: 'Etniciteit',
	CarepatronFieldFirstName: 'Voornaam',
	CarepatronFieldGender: 'Geslacht',
	CarepatronFieldIdentificationNumber: 'Identificatienummer',
	CarepatronFieldIsArchived: 'Staat',
	CarepatronFieldLabel: 'Label',
	CarepatronFieldLastName: 'Achternaam',
	CarepatronFieldLivingArrangements: 'Woonregelingen',
	CarepatronFieldMiddleNames: 'Middelste naam',
	CarepatronFieldOccupation: 'Bezigheid',
	CarepatronFieldPhoneNumber: 'Telefoonnummer',
	CarepatronFieldRelationshipStatus: 'Relatiestatus',
	CarepatronFieldStatus: 'Staat',
	CarepatronFieldStatusHelperText: 'Maximaal 10 statussen.',
	CarepatronFieldTags: 'Labels',
	CarepatronFields: 'Carepatron-velden',
	Cash: 'Contant geld',
	Category: 'Categorie',
	CategoryInputPlaceholder: 'Kies een sjablooncategorie',
	CenterAlign: 'Centreren',
	Central: 'Centraal',
	ChangeLayout: 'Wijzig lay-out',
	ChangeLogo: 'Wijziging',
	ChangePassword: 'Wachtwoord wijzigen',
	ChangePasswordFailureSnackbar:
		'Sorry, uw wachtwoord is niet gewijzigd. Controleer of uw oude wachtwoord correct is.',
	ChangePasswordHelperInfo: 'Minimale lengte van {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Wachtwoord succesvol gewijzigd! Zorg ervoor dat u de volgende keer dat u inlogt, dat wachtwoord gebruikt.',
	ChangeSubscription: 'Abonnement wijzigen',
	ChangesNotAllowed: 'Er kunnen geen wijzigingen worden aangebracht in dit veld',
	ChargesDisabled: 'Kosten uitgeschakeld',
	ChargesEnabled: 'Kosten ingeschakeld',
	ChargesStatus: 'Status van de kosten',
	ChartAndDiagram: 'Grafiek/Diagram',
	ChartsAndDiagramsCategoryDescription: 'Voor het illustreren van klantgegevens en voortgang',
	ChatEditMessage: 'Bericht bewerken',
	ChatReplyTo: 'Reageer op {name}',
	ChatTypeMessageTo: 'Bericht {name}',
	Check: 'Rekening',
	CheckList: 'Controlelijst',
	Chef: 'Chef',
	Chiropractic: 'Chiropractie',
	Chiropractor: 'Chiropractor',
	Chiropractors: 'Chiropractors',
	ChooseACollection: 'Kies een collectie',
	ChooseAContact: 'Kies een contactpersoon',
	ChooseAccountTypeHeader: 'Welke omschrijving past het beste bij u?',
	ChooseAction: 'Kies actie',
	ChooseAnAccount: 'Kies een account',
	ChooseAnOption: 'Kies een optie',
	ChooseBillingProfile: 'Kies factureringsprofiel',
	ChooseClaim: 'Kies claim',
	ChooseCollection: 'Kies collectie',
	ChooseColor: 'Kies kleur',
	ChooseCustomDate: 'Kies een aangepaste datum',
	ChooseDateAndTime: 'Kies datum en tijd',
	ChooseDxCodes: 'Kies diagnosecodes',
	ChooseEventType: 'Kies evenementtype',
	ChooseFileButton: 'Kies een bestand',
	ChooseFolder: 'Map kiezen',
	ChooseInbox: 'Kies inbox',
	ChooseMethod: 'Kies methode',
	ChooseNewOwner: 'Kies nieuwe eigenaar',
	ChooseOrganization: 'Kies organisatie',
	ChoosePassword: 'Kies wachtwoord',
	ChoosePayer: 'Kies betaler',
	ChoosePaymentMethod: 'Kies een betaalmethode',
	ChoosePhysicalOrRemoteLocations: 'Voer locatie in of kies locatie',
	ChoosePlan: 'Kies {plan}',
	ChooseProfessional: 'Kies Professioneel',
	ChooseServices: 'Kies diensten',
	ChooseSource: 'Bron kiezen',
	ChooseSourceDescription:
		'Kies waar u klanten importeert – of het nu vanuit een bestand is of een ander softwareplatform.',
	ChooseTags: 'Kies tags',
	ChooseTaxName: 'Kies belastingnaam',
	ChooseTeamMembers: 'Teamleden kiezen',
	ChooseTheme: 'Thema kiezen',
	ChooseTrigger: 'Kies trigger',
	ChooseYourProvider: 'Kies uw provider',
	CircularProgressWithLabel: '{value}%',
	City: 'Stad',
	CivilEngineer: 'Civiel ingenieur',
	Claim: 'Claim',
	ClaimAddReferringProvider: 'Verwijzende provider toevoegen',
	ClaimAddRenderingProvider: 'Renderprovider toevoegen',
	ClaimAmount: 'Claimbedrag',
	ClaimAmountPaidHelpContent:
		'Het betaalde bedrag is de betaling die is ontvangen van de patiënt of andere betalers. Voer het totale bedrag in dat de patiënt en/of andere betalers alleen hebben betaald voor de gedekte diensten.',
	ClaimAmountPaidHelpSubtitle: 'Veld 29',
	ClaimAmountPaidHelpTitle: 'Betaald bedrag',
	ClaimBillingProfileTypeIndividual: 'Individueel',
	ClaimBillingProfileTypeOrganisation: 'Organisatie',
	ClaimChooseRenderingProviderOrTeamMember: 'Kies een renderingprovider of teamlid',
	ClaimClientInsurancePolicies: 'Verzekeringspolissen voor cliënten',
	ClaimCreatedAction: '<mark>Claim {claimNumber}</mark> aangemaakt',
	ClaimDeniedAction: '<mark>Claim {claimNumber}</mark> werd afgewezen door <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Zoek ICD 10-diagnosecodes',
	ClaimDiagnosisSelectorHelpContent: `De “diagnose of verwonding” is het teken, symptoom, klacht of toestand van de patiënt met betrekking tot de dienst(en) op de claim.
 Er kunnen maximaal 12 ICD 10-diagnosecodes worden geselecteerd.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Veld 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnose of letsel',
	ClaimDiagnosticCodesEmptyError: 'Ten minste één diagnosecode is vereist',
	ClaimDoIncludeReferrerInformation: 'Vermeld de verwijzende informatie op CMS1500',
	ClaimERAReceivedAction: 'Elektronische overmaking ontvangen van <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Elektronische betaling ontvangen	<mark>Betaling {paymentReference}</mark> voor <b>{paymentAmount}</b> door <b>{payerNumber} {payerName}</b> is geregistreerd',
	ClaimExportedAction: '<mark>Claim {claimNumber}</mark> werd geëxporteerd als <b>{attachmentType}</b>',
	ClaimFieldClient: 'Naam van klant of contactpersoon',
	ClaimFieldClientAddress: 'Klantadres',
	ClaimFieldClientAddressDescription: `Voer het adres van de klant in. De eerste regel is voor het straatadres. Gebruik geen leestekens (komma's of punten) of symbolen in het adres. Als u een buitenlands adres opgeeft, neem dan contact op met de betaler voor specifieke instructies voor het opgeven.`,
	ClaimFieldClientAddressSubtitle: 'Veld 5',
	ClaimFieldClientDateOfBirth: 'Geboortedatum van de klant',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Veld 3',
	ClaimFieldClientDateOfBirthDescription:
		'Voer de 8-cijferige geboortedatum van de cliënt in (MM/DD/JJJJ). De geboortedatum van de cliënt is informatie waarmee de cliënt kan worden geïdentificeerd en waarmee personen met vergelijkbare namen kunnen worden onderscheiden.',
	ClaimFieldClientDescription: `Onder 'cliëntnaam' wordt verstaan de naam van de persoon die de behandeling of de benodigdheden heeft ontvangen.`,
	ClaimFieldClientSexDescription:
		'Het geslacht is informatie waarmee de cliënt kan worden geïdentificeerd en waarmee personen met vergelijkbare namen kunnen worden onderscheiden.',
	ClaimFieldClientSubtitle: 'Veld 2',
	ClaimFiling: 'Aangifte indienen',
	ClaimHistorySubtitle: 'Verzekering • Claim {number}',
	ClaimIncidentAutoAccident: 'Auto-ongeluk?',
	ClaimIncidentConditionRelatedTo: 'Heeft de toestand van de cliënt te maken met',
	ClaimIncidentConditionRelatedToHelpContent:
		'Deze informatie geeft aan of de ziekte of verwonding van de cliënt verband houdt met de werkgelegenheid, het auto-ongeluk of een ander ongeval. Werkgelegenheid (huidig of eerder) zou aangeven dat de aandoening verband houdt met de baan of werkplek van de cliënt. Auto-ongeluk zou aangeven dat de aandoening het gevolg is van een auto-ongeluk. Ander ongeval zou aangeven dat de aandoening het gevolg is van een ander type ongeval.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Velden 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Heeft de toestand van de cliënt te maken met',
	ClaimIncidentCurrentIllness: 'Huidige ziekte, blessure of zwangerschap',
	ClaimIncidentCurrentIllnessHelpContent:
		'De datum van de huidige ziekte, verwonding of zwangerschap bepaalt de eerste datum waarop de ziekte zich openbaarde, de werkelijke datum van de verwonding of de LMP voor zwangerschap.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Veld 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Data van huidige ziekte, verwonding of zwangerschap (LMP)',
	ClaimIncidentDate: 'Datum',
	ClaimIncidentDateFrom: 'Datum van',
	ClaimIncidentDateTo: 'Datum tot',
	ClaimIncidentEmploymentRelated: 'Werkgelegenheid',
	ClaimIncidentEmploymentRelatedDesc: '(huidige of vorige)',
	ClaimIncidentHospitalizationDatesLabel: 'Ziekenhuisopnamedata gerelateerd aan huidige diensten',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'De ziekenhuisopnamedata met betrekking tot de huidige diensten hebben betrekking op het verblijf van de cliënt en de opname- en ontslagdata die bij de dienst(en) horen, worden op de declaratie vermeld.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Veld 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Ziekenhuisopnamedata gerelateerd aan huidige diensten',
	ClaimIncidentInformation: 'Informatie over incidenten',
	ClaimIncidentOtherAccident: 'Ander ongeluk?',
	ClaimIncidentOtherAssociatedDate: 'Andere bijbehorende datum',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'De andere datum bevat aanvullende datuminformatie over de toestand of behandeling van de cliënt.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Veld 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Andere datum',
	ClaimIncidentQualifier: 'Kwalificatie',
	ClaimIncidentQualifierPlaceholder: 'Kies kwalificatie',
	ClaimIncidentUnableToWorkDatesLabel: 'Cliënt was niet in staat om in zijn huidige beroep te werken',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Data waarop de cliënt niet in staat was om te werken in de huidige baan is de tijdsperiode waarin de cliënt niet in staat is of was om te werken',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Veld 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle:
		'Data waarop de cliënt niet in staat was om in zijn huidige beroep te werken',
	ClaimIncludeReferrerInformation: 'Verwijzingsinformatie opnemen in CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Het type ziektekostenverzekering dat van toepassing is op deze claim. Anders geeft ziektekostenverzekering aan, waaronder HMO's, commerciële verzekeringen, auto-ongelukken, aansprakelijkheid of werknemerscompensatie.
 Met deze informatie wordt de claim doorverwezen naar het juiste programma en kan de primaire aansprakelijkheid worden vastgesteld.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Veld 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Dekkingstype',
	ClaimInsuranceGroupIdHelpContent: `Voer het polis- of groepsnummer van de verzekerde in zoals dit op de ziektekostenpas van de verzekerde staat.

 Het "Insured's Policy, Group, or FECA Number" is de alfanumerieke identificatie voor de dekking van het ziektekosten-, auto- of andere verzekeringsplan. Het FECA-nummer is de alfanumerieke identificatie van 9 tekens die is toegewezen aan een patiënt die een werkgerelateerde aandoening claimt.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Veld 11',
	ClaimInsuranceGroupIdHelpTitle: 'Polis-, groeps- of FECA-nummer van de verzekerde',
	ClaimInsuranceMemberIdHelpContent: `Voer het ID-nummer van de verzekerde in zoals vermeld op de ID-kaart van de verzekerde bij wie de claim wordt ingediend.
 Als de patiënt een uniek lidmaatschapsnummer heeft dat door de betaler is toegewezen, voert u dat nummer in dit veld in.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Veld 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Lidmaatschaps-ID van de verzekerde',
	ClaimInsurancePayer: 'Verzekeringsbetaler',
	ClaimManualPaymentAction: '<mark>Betaling {paymentReference}</mark> voor <b>{paymentAmount}</b> geregistreerd',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Aanvullende claiminformatie',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Raadpleeg de huidige instructies van de publieke of private betaler met betrekking tot het gebruik van dit veld. Meld de juiste kwalificator, indien beschikbaar, voor de informatie die wordt ingevoerd.Voer geen spatie, koppelteken of andere scheidingsteken in tussen de kwalificator en de informatie.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Veld 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Aanvullende claiminformatie',
	ClaimMiscClaimCodes: 'Claimcodes',
	ClaimMiscOriginalReferenceNumber: 'Origineel referentienummer',
	ClaimMiscPatientsAccountNumber: 'Rekeningnummer van de patiënt',
	ClaimMiscPatientsAccountNumberHelpContent:
		'Het rekeningnummer van de patiënt is de identificatiecode die door de zorgverlener wordt toegewezen.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Veld 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Rekeningnummer van de patiënt',
	ClaimMiscPriorAuthorizationNumber: 'Nummer van voorafgaande machtiging',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Het voorafgaande autorisatienummer is het nummer dat aan de betaler is toegewezen en waarmee de dienst(en) wordt/worden geautoriseerd.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Veld 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Nummer van voorafgaande toestemming',
	ClaimMiscResubmissionCode: 'Code opnieuw indienen',
	ClaimMiscResubmissionCodeHelpContent:
		'Onder herindiening wordt verstaan de code en het oorspronkelijke referentienummer die door de bestemmingsbetaler of -ontvanger zijn toegekend om een eerder ingediende claim of ontmoeting aan te geven.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Veld 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Herindiening en/of origineel referentienummer',
	ClaimNumber: 'Claimnummer',
	ClaimNumberFormat: 'Claim #{number}',
	ClaimOrderingProvider: 'Bestellende provider',
	ClaimOtherId: 'Andere ID',
	ClaimOtherIdPlaceholder: 'Kies een optie',
	ClaimOtherIdQualifier: 'Andere ID-kwalificatie',
	ClaimOtherIdQualifierPlaceholder: 'Kies ID-kwalificatie',
	ClaimPlaceOfService: 'Plaats van dienst',
	ClaimPlaceOfServicePlaceholder: 'POS toevoegen',
	ClaimPolicyHolderRelationship: 'Relatie met de verzekeringnemer',
	ClaimPolicyInformation: 'Beleidsinformatie',
	ClaimPolicyTelephone: 'Telefoon (inclusief netnummer)',
	ClaimReceivedAction: '<mark>Claim {claimNumber}</mark> ontvangen door <b>{name}</b>',
	ClaimReferringProvider: 'Verwijzende aanbieder',
	ClaimReferringProviderEmpty: 'Geen verwijzende provider(s) toegevoegd',
	ClaimReferringProviderHelpContent:
		'De ingevoerde naam is de verwijzende aanbieder, bestellende aanbieder of superviserende aanbieder die de dienst(en) of levering(en) op de claim heeft doorverwezen, besteld of gesuperviseerd. De kwalificatie geeft de rol aan van de aanbieder die wordt gerapporteerd.',
	ClaimReferringProviderHelpSubtitle: 'Veld 17',
	ClaimReferringProviderHelpTitle: 'Naam van verwijzende aanbieder of bron',
	ClaimReferringProviderQualifier: 'Kwalificatie',
	ClaimReferringProviderQualifierPlaceholder: 'Kies kwalificatie',
	ClaimRejectedAction: '<mark>Claim {claimNumber}</mark> werd afgewezen door <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'ID-nummer',
	ClaimRenderingProviderOrTeamMember: 'Weergaveprovider of teamlid',
	ClaimRestoredAction: '<mark>Claim {claimNumber}</mark> werd hersteld',
	ClaimServiceFacility: 'Servicefaciliteit',
	ClaimServiceFacilityLocationHelpContent:
		'De naam en het adres van de faciliteit waar de diensten zijn verleend, identificeren de locatie waar de dienst(en) zijn verleend.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a en 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Veld 32, 32a en 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Servicefaciliteit',
	ClaimServiceFacilityPlaceholder: 'Kies servicefaciliteit of locatie',
	ClaimServiceLabChargesHelpContent: `Vul dit veld in wanneer u een claim indient voor gekochte diensten die door een andere entiteit dan de facturerende provider zijn geleverd.
 Elke gekochte dienst moet op een aparte claim worden vermeld, omdat er slechts één kostenpost op het CMS1500-formulier kan worden vermeld.`,
	ClaimServiceLabChargesHelpSubtitle: 'Veld 20',
	ClaimServiceLabChargesHelpTitle: 'Kosten voor extern laboratorium',
	ClaimServiceLineServiceHelpContent: `Met 'Procedures, diensten of benodigdheden' worden de medische diensten en procedures bedoeld die aan de patiënt worden verleend.`,
	ClaimServiceLineServiceHelpSubtitle: 'Veld 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedures, diensten of leveringen',
	ClaimServiceLinesEmptyError: 'Ten minste één serviceregel is vereist',
	ClaimServiceSupplementaryInfoHelpContent: `Voeg een aanvullende beschrijving van de geleverde diensten toe met behulp van toepasselijke kwalificaties.
 Plaats geen spatie, koppelteken of ander scheidingsteken tussen de kwalificatie en de informatie.

 Voor volledige instructies over het toevoegen van aanvullende informatie raadpleegt u de instructies voor het CMS 1500-claimformulier.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Veld 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Aanvullende informatie',
	ClaimSettingsBillingMethodTitle: 'Factureringsmethode van de klant',
	ClaimSettingsClientSignatureDescription:
		'Ik heb toestemming om medische of andere informatie vrij te geven die nodig is voor het verwerken van verzekeringsclaims.',
	ClaimSettingsClientSignatureTitle: 'Handtekening van de klant in het bestand',
	ClaimSettingsConsentLabel: 'Toestemming vereist voor het verwerken van verzekeringsclaims:',
	ClaimSettingsDescription:
		'Kies de factureringsmethode van de klant om een vlotte betalingsverwerking te garanderen:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} heeft een actief verzekeringsbeleid. Om facturering via de verzekering in te schakelen, update de klantfactuurmethode naar Verzekering.',
	ClaimSettingsInsuranceDescription: 'Kosten vergoed door verzekering',
	ClaimSettingsInsuranceTitle: 'Verzekering',
	ClaimSettingsNoPoliciesAlertDescription: 'Voeg een verzekeringspolis toe om verzekeringsclaims mogelijk te maken.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Ik heb toestemming om verzekeringsuitkeringen te ontvangen voor geleverde diensten.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Handtekening van de verzekeringnemer in het bestand',
	ClaimSettingsSelfPayDescription: 'De cliënt betaalt voor de afspraken',
	ClaimSettingsSelfPayTitle: 'Zelf betalen',
	ClaimSettingsTitle: 'Claiminstellingen',
	ClaimSexSelectorPlaceholder: 'Man / Vrouw',
	ClaimStatusChangedAction: '<mark>Claim {claimNumber}</mark> status bijgewerkt',
	ClaimSubmittedAction:
		'<mark>Claim {claimNumber}</mark> ingediend bij <b>{payerClearingHouse}</b> voor <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Claim #{claimNumber}',
	ClaimSupervisingProvider: 'Begeleidende aanbieder',
	ClaimSupplementaryInfo: 'Aanvullende informatie',
	ClaimSupplementaryInfoPlaceholder: 'Voeg aanvullende informatie toe',
	ClaimTrashedAction: '<mark>Claim {claimNumber}</mark> is verwijderd',
	ClaimValidationFailure: 'Claim kon niet worden gevalideerd',
	ClaimsEmptyStateDescription: 'Er zijn geen claims gevonden.',
	ClainInsuranceTelephone: 'Verzekeringstelefoon (inclusief netnummer)',
	Classic: 'Klassiek',
	Clear: 'Duidelijk',
	ClearAll: 'Alles wissen',
	ClearSearchFilter: 'Duidelijk',
	ClearingHouse: 'Woningontruiming',
	ClearingHouseClaimId: 'Claim.MD-id',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Huisklaringreferentie',
	ClearingHouseUnavailableError: 'De clearing house is momenteel niet beschikbaar. Probeer het later opnieuw.',
	ClickToUpload: 'Klik om te uploaden',
	Client: 'Cliënt',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} heeft {noteTitle, select, undefined { een notitie } other {{noteTitle}}} toegevoegd',
	ClientAndRelationshipSelectorPlaceholder: 'Kies klanten en hun relaties',
	ClientAndRelationshipSelectorTitle: 'Alle cliënten en hun relaties',
	ClientAndRelationshipSelectorTitle1: 'Alle relaties van ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Als u wacht op een videogesprek, verschijnt het hier binnenkort. Als u problemen ondervindt, neem dan contact op met de persoon die het gesprek heeft geïnitieerd.',
	ClientAppSubHeaderMyDocumentation: 'Mijn documentatie',
	ClientAppointment: 'Afspraak met klant',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} heeft {appointmentName} geboekt',
	ClientAppointmentsEmptyStateDescription: 'Er zijn geen afspraken gevonden',
	ClientAppointmentsEmptyStateTitle:
		'Houd de komende en historische afspraken van uw klanten en hun aanwezigheid bij',
	ClientArchivedSuccessfulSnackbar: 'Succesvol gearchiveerd <b>{name}</b>',
	ClientBalance: 'Klantensaldo',
	ClientBilling: 'Facturering',
	ClientBillingAddPaymentMethodDescription:
		'Voeg betaalmethoden voor uw klanten toe en beheer deze om hun factureringsproces te stroomlijnen.',
	ClientBillingAndPaymentDueDate: 'Deadline',
	ClientBillingAndPaymentHistory: 'Facturatie- en betalingsgeschiedenis',
	ClientBillingAndPaymentInvoices: 'Facturen',
	ClientBillingAndPaymentIssueDate: 'Uitgiftedatum',
	ClientBillingAndPaymentPrice: 'Prijs',
	ClientBillingAndPaymentReceipt: 'Ontvangst',
	ClientBillingAndPaymentServices: 'Diensten',
	ClientBillingAndPaymentStatus: 'Staat',
	ClientBulkStaffAssignedSuccessSnackbar: 'Team {count, plural, one {lid} other {leden}} toegewezen!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Team {count, plural, one {lid} other {leden}} niet toegewezen!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tags toegevoegd!',
	ClientDuplicatesDeviewDescription:
		'Voeg meerdere klantrecords samen tot één record om alle gegevens te verenigen: notities, documenten, afspraken, facturen en gesprekken.',
	ClientDuplicatesPageMergeHeader: 'Kies de gegevens die u wilt bewaren',
	ClientDuplicatesReviewHeader: 'Vergelijk potentiële dubbele records voor samenvoeging',
	ClientEmailChangeWarningDescription:
		'Als u het e-mailadres van de klant bijwerkt, wordt de toegang tot alle gedeelde documentatie ingetrokken en krijgt de gebruiker met het nieuwe e-mailadres toegang.',
	ClientFieldDateDescription: 'Datum opmaken',
	ClientFieldDateLabel: 'Datum',
	ClientFieldDateRangeDescription: 'Een reeks data',
	ClientFieldDateRangeLabel: 'Datumbereik',
	ClientFieldDateShowDateDescription: 'bijv. 29 jaar',
	ClientFieldDateShowDateRangeDescription: 'bijv. 2 weken',
	ClientFieldEmailDescription: 'E-mailadres',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Veldlabel',
	ClientFieldLinearScaleDescription: 'Schaalopties 1-10',
	ClientFieldLinearScaleLabel: 'Lineaire schaal',
	ClientFieldLocationDescription: 'Fysiek of postadres',
	ClientFieldLocationLabel: 'Locatie',
	ClientFieldLongTextDescription: 'Lange tekstruimte',
	ClientFieldLongTextLabel: 'Paragraaf',
	ClientFieldMultipleChoiceDropdownDescription: 'Kies meerdere opties uit de lijst',
	ClientFieldMultipleChoiceDropdownLabel: 'Meerkeuze-dropdown',
	ClientFieldPhoneNumberDescription: 'Telefoonnummer',
	ClientFieldPhoneNumberLabel: 'Telefoon',
	ClientFieldPlaceholder: 'Kies een clientveldtype',
	ClientFieldSingleChoiceDropdownDescription: 'Kies slechts één optie uit de lijst',
	ClientFieldSingleChoiceDropdownLabel: 'Enkele keuze dropdown',
	ClientFieldTextDescription: 'Tekst invoerveld',
	ClientFieldTextLabel: 'Tekst',
	ClientFieldYesOrNoDescription: 'Kies uit ja of nee opties',
	ClientFieldYesOrNoLabel: 'Ja | Nee',
	ClientFileFormAccessLevelDescription:
		'U en het team hebben altijd toegang tot bestanden die u uploadt. U kunt ervoor kiezen om dit bestand te delen met de klant en/of hun relaties',
	ClientFileSavedSuccessSnackbar: 'Bestand opgeslagen!',
	ClientFilesPageEmptyStateText: 'Geen bestanden geüpload',
	ClientFilesPageUploadFileButton: 'Bestanden uploaden',
	ClientHeaderBilling: 'Facturering',
	ClientHeaderBillingAndReceipts: 'Facturering ',
	ClientHeaderDocumentation: 'Documentatie',
	ClientHeaderDocuments: 'Documenten',
	ClientHeaderFile: 'Document',
	ClientHeaderHistory: 'Medische geschiedenis',
	ClientHeaderInbox: 'Postvak IN',
	ClientHeaderNote: 'Opmerking',
	ClientHeaderOverview: 'Overzicht',
	ClientHeaderProfile: 'Persoonlijk',
	ClientHeaderRelationship: 'Relatie',
	ClientHeaderRelationships: 'Relaties',
	ClientId: 'Klant-ID',
	ClientImportProcessingDescription:
		'Bestand wordt nog verwerkt. We zullen je op de hoogte brengen wanneer dit klaar is.',
	ClientImportReadyForMappingDescription:
		'We hebben het voorbewerken van uw bestand voltooid. Wilt u kolommen toewijzen om deze import te voltooien?',
	ClientImportReadyForMappingNotificationSubject:
		'Cliënt import pre-processing is voltooid. Het bestand is nu klaar om te worden toegewezen.',
	ClientInAppMessaging: 'Berichtgeving in de app voor klanten',
	ClientInfoAddField: 'Voeg nog een veld toe',
	ClientInfoAddRow: 'Rij toevoegen',
	ClientInfoAlertMessage: 'Alle informatie die u in dit gedeelte invult, wordt toegevoegd aan het cliëntendossier.',
	ClientInfoFormPrimaryText: 'Klantinformatie',
	ClientInfoFormSecondaryText: 'Contactgegevens verzamelen',
	ClientInfoPlaceholder: `Klantnaam, e-mailadres, telefoonnummer
 Fysiek adres,
 Geboortedatum`,
	ClientInformation: 'Klantinformatie',
	ClientInsuranceTabLabel: 'Verzekering',
	ClientIntakeFormsNotSupported: `Formuliersjablonen worden momenteel niet ondersteund via cliëntintakes.
 Maak en deel ze in plaats daarvan als klantnotities.`,
	ClientIntakeModalDescription:
		'Er wordt een intake-e-mail naar uw cliënt gestuurd met het verzoek om zijn/haar profiel te voltooien, relevante medische of verwijzingsdocumenten te uploaden. Ze krijgen toegang tot de Client Portal.',
	ClientIntakeModalTitle: 'Stuur intake naar {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Succes! Uw inname is opgeslagen.',
	ClientIntakeSuccessSnackbar: 'Succes! Uw intake is opgeslagen en er is een bevestigingsmail verzonden.',
	ClientIsChargedProcessingFee: 'Uw klanten betalen de verwerkingskosten',
	ClientListCreateButton: 'Nieuwe klant',
	ClientListEmptyState: 'Geen klanten toegevoegd',
	ClientListPageItemArchive: 'Client verwijderen',
	ClientListPageItemRemoveAccess: 'Verwijder mijn toegang',
	ClientLocalizationPanelDescription: 'De voorkeurstaal en tijdzone van de klant.',
	ClientLocalizationPanelTitle: 'Taal en tijdzone',
	ClientManagementAndEHR: 'Klantenbeheer ',
	ClientMergeResultSummaryBanner:
		'Het samenvoegen van dossiers consolideert alle clientgegevens, inclusief notities, documenten, afspraken, facturen en gesprekken. Controleer de juistheid voordat u doorgaat.',
	ClientMergeResultSummaryTitle: 'Samenvatting van de samenvoeging',
	ClientModalTitle: 'Nieuwe klant',
	ClientMustHaveEmaillAccessErrorText: 'Klanten/Contacten zonder e-mailadressen',
	ClientMustHavePortalAccessErrorText: 'Klanten/contacten dienen zich aan te melden',
	ClientMustHaveZoomAppConnectedErrorText: 'Verbind Zoom via Instellingen > Verbonden apps',
	ClientNameFormat: 'Klantnaam formaat',
	ClientNotFormAccessLevel: 'Zichtbaar voor:',
	ClientNotFormAccessLevelDescription:
		'U en het team hebben altijd toegang tot de notities die u publiceert. U kunt ervoor kiezen om deze notitie te delen met de klant en/of hun relaties',
	ClientNotRegistered: 'Niet geregistreerd',
	ClientNoteFormAddFileButton: 'Bestanden bijvoegen',
	ClientNoteFormChooseAClient: 'Kies een klant/contactpersoon om verder te gaan',
	ClientNoteFormContent: 'Inhoud',
	ClientNoteItemDeleteConfirmationModalDescription:
		'Nadat u de notitie hebt verwijderd, kunt u deze niet meer ophalen.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Notitie gepubliceerd en vergrendeld.',
	ClientNotePublishedSuccessSnackbar: 'Notitie gepubliceerd!',
	ClientNotes: 'Notities van de klant',
	ClientNotesEmptyStateText:
		'Om notities toe te voegen, gaat u naar het profiel van een klant en klikt u op het tabblad Notities.',
	ClientOnboardingChoosePasswordTitle1: 'Bijna klaar!',
	ClientOnboardingChoosePasswordTitle2: 'Kies een wachtwoord',
	ClientOnboardingCompleteIntake: 'Volledige inname',
	ClientOnboardingConfirmationScreenText:
		'U heeft alle informatie die {providerName} nodig heeft verstrekt.	Bevestig uw e-mailadres om te beginnen met uw onboarding. Als u het niet direct ontvangt, controleer dan uw spammap.',
	ClientOnboardingConfirmationScreenTitle: 'Geweldig! Controleer je inbox.',
	ClientOnboardingDashboardButton: 'Ga naar Dashboard',
	ClientOnboardingHealthRecordsDesc1: 'Wil je verwijzingsbrieven, documenten delen met {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Beschrijving toevoegen (optioneel)',
	ClientOnboardingHealthRecordsTitle: 'Documentatie',
	ClientOnboardingPasswordRequirements: 'Vereisten',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimaal 9 tekens vereist',
	ClientOnboardingProviderIntroSignupButton: 'Meld je aan voor mezelf',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Meld je aan voor een familielid',
	ClientOnboardingProviderIntroTitle: '{name} heeft je uitgenodigd om lid te worden van hun Carepatron-platform',
	ClientOnboardingRegistrationInstructions: 'Vul hieronder uw persoonlijke gegevens in.',
	ClientOnboardingRegistrationTitle: 'Eerst hebben we een aantal persoonlijke gegevens nodig',
	ClientOnboardingStepFormsAndAgreements: 'Formulieren en overeenkomsten',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Vul de volgende formulieren in voor het intakeproces van {providerName}',
	ClientOnboardingStepHealthDetails: 'Gezondheidsgegevens',
	ClientOnboardingStepPassword: 'Wachtwoord',
	ClientOnboardingStepYourDetails: 'Uw gegevens',
	ClientPaymentMethodDescription:
		'Sla een betaalmethode op in uw profiel, zodat u uw volgende afspraak en facturering sneller en veiliger kunt maken.',
	ClientPortal: 'Klantenportaal',
	ClientPortalDashboardEmptyDescription: 'Hier worden uw afspraakgeschiedenis en aanwezigheid weergegeven.',
	ClientPortalDashboardEmptyTitle:
		'Houd alle komende, aangevraagde en afgelopen afspraken bij, samen met uw aanwezigheid',
	ClientPreferredNotificationPanelDescription:
		'Beheer de voorkeursmethode van uw klant om updates en meldingen te ontvangen via:',
	ClientPreferredNotificationPanelTitle: 'Voorkeursmethode voor notificatie',
	ClientProcessingFee: 'Betaling inclusief ({currencyCode}) {amount} verwerkingskosten',
	ClientProfileAddress: 'Adres',
	ClientProfileDOB: 'Geboortedatum',
	ClientProfileEmailHelperText: 'Door een e-mailadres toe te voegen, krijgt u toegang tot de portal',
	ClientProfileEmailHelperTextMoreInfo:
		'Door de cliënt toegang te verlenen tot de portal, kunnen teamleden notities, bestanden en andere documentatie delen',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Identificatienummer',
	ClientRelationshipsAddClientOwnerButton: 'Nodig de klant uit',
	ClientRelationshipsAddFamilyButton: 'Nodig een familielid uit',
	ClientRelationshipsAddStaffButton: 'Personeelstoegang toevoegen',
	ClientRelationshipsEmptyStateText: 'Geen relaties toegevoegd',
	ClientRemovedSuccessSnackbar: 'Client succesvol verwijderd.',
	ClientResponsibility: 'Klantverantwoordelijkheid',
	ClientSavedSuccessSnackbar: 'Client succesvol opgeslagen.',
	ClientTableClientName: 'Klantnaam',
	ClientTablePhone: 'Telefoon',
	ClientTableStatus: 'Staat',
	ClientUnarchivedSuccessfulSnackbar: 'Succesvol gearchiveerd <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} heeft {noteTitle, select, undefined { een notitie } other {{noteTitle}}} bewerkt',
	ClientView: 'Klantperspectief',
	Clients: 'Klanten',
	ClientsTable: 'Klanten tabel',
	ClinicalFormat: 'Klinisch formaat',
	ClinicalPsychologist: 'Klinisch psycholoog',
	Close: 'Dichtbij',
	CloseImportClientsModal: 'Weet u zeker dat u het importeren van clients wilt annuleren?',
	CloseReactions: 'Sluit reacties',
	Closed: 'Gesloten',
	Coaching: 'Begeleiding',
	Code: 'Code',
	CodeErrorMessage: 'Code is vereist',
	CodePlaceholder: 'Code',
	Coinsurance: 'Medeverzekering',
	Collection: 'Verzameling',
	CollectionName: 'Collectienaam',
	Collections: 'Collecties',
	ColorAppointmentsBy: 'Kleurafspraken door',
	ColorTheme: 'Kleurenthema',
	ColourCalendarBy: 'Kleurenkalender door',
	ComingSoon: 'Binnenkort beschikbaar',
	Community: 'Gemeenschap',
	CommunityHealthLead: 'Leidinggevende gemeenschapsgezondheid',
	CommunityHealthWorker: 'Gemeenschapsgezondheidswerker',
	CommunityTemplatesSectionDescription: 'Gemaakt door de Carepatron community',
	CommunityTemplatesSectionTitle: 'Gemeenschap',
	CommunityUser: 'Communitygebruiker',
	Complete: 'Compleet',
	CompleteAndLock: 'Compleet en vergrendelen',
	CompleteSetup: 'Volledige installatie',
	CompleteSetupSuccessDescription: 'Je hebt enkele belangrijke stappen gezet om Carepatron te beheersen.',
	CompleteSetupSuccessDescription2:
		'Ontgrendel meer manieren om uw praktijk te stroomlijnen en uw klanten te ondersteunen.',
	CompleteSetupSuccessTitle: 'Succes! Je doet het geweldig!',
	CompleteStripeSetup: 'Volledige Stripe-installatie',
	Completed: 'Voltooid',
	ComposeSms: 'SMS opstellen',
	ComputerSystemsAnalyst: 'Analist computersystemen',
	Confirm: 'Bevestigen',
	ConfirmDeleteAccountDescription:
		'U staat op het punt uw account te verwijderen. Deze actie kan niet ongedaan worden gemaakt. Als u wilt doorgaan, bevestig dan hieronder.',
	ConfirmDeleteActionDescription:
		'Weet u zeker dat u deze actie wilt verwijderen? Dit kan niet ongedaan worden gemaakt',
	ConfirmDeleteAutomationDescription:
		'Weet u zeker dat u deze automatisering wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.',
	ConfirmDeleteScheduleDescription:
		'Het verwijderen van de <strong>{scheduleName}</strong> planning verwijdert deze uit uw planningen en kan uw online service beschikbaarheid veranderen. Deze actie kan niet ongedaan worden gemaakt.',
	ConfirmDraftResponseContinue: 'Ga door met reageren',
	ConfirmDraftResponseDescription:
		'Als u deze pagina sluit, blijft uw reactie een concept. U kunt op elk gewenst moment terugkomen en doorgaan.',
	ConfirmDraftResponseSubmitResponse: 'Reactie indienen',
	ConfirmDraftResponseTitle: 'Uw reactie is niet verzonden',
	ConfirmIfUserIsClientDescription: `Het door u ingevulde aanmeldformulier is bedoeld voor aanbieders (d.w.z. gezondheidsteams/-organisaties).
 Als dit een fout is, kunt u kiezen voor "Doorgaan als klant" en wij zullen u dan instellen voor uw klantenportaal.`,
	ConfirmIfUserIsClientNoButton: 'Aanmelden als aanbieder',
	ConfirmIfUserIsClientTitle: 'Het lijkt erop dat u een klant bent',
	ConfirmIfUserIsClientYesButton: 'Doorgaan als klant',
	ConfirmKeepSeparate: 'Bevestigen apart houden',
	ConfirmMerge: 'Bevestig samenvoegen',
	ConfirmPassword: 'Bevestig wachtwoord',
	ConfirmRevertClaim: 'Ja, status terugzetten',
	ConfirmSignupAccessCode: 'Bevestigingscode',
	ConfirmSignupButtom: 'Bevestigen',
	ConfirmSignupDescription: 'Vul uw e-mailadres en de bevestigingscode in die we u zojuist hebben gestuurd.',
	ConfirmSignupSubTitle: 'Controleer de spamfolder - als de e-mail niet is aangekomen',
	ConfirmSignupSuccessSnackbar:
		'Geweldig, we hebben je account bevestigd! Nu kun je inloggen met je e-mailadres en wachtwoord',
	ConfirmSignupTitle: 'Account bevestigen',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: 'Bevestig abonnement {price} {isMonthly, select, true {per maand} other {per jaar}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Zodra de cliënten zijn verwijderd, hebt u geen toegang meer tot hun gegevens.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Verwijder {count, plural, one {# klant} other {# klanten}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Zodra de contactpersonen zijn verwijderd, hebt u geen toegang meer tot hun gegevens.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Verwijder {count, plural, one {# contact} other {# contacten}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Dit is een permanente actie. Zodra de teamleden zijn verwijderd, hebt u geen toegang meer tot hun informatie.',
	ConfirmationModalBulkDeleteMembersTitleId: 'Verwijder {count, plural, one {# teamlid} other {# teamleden}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Als u deze notitie sluit, worden alle lopende transcripties beëindigd. Weet u zeker dat u wilt doorgaan?',
	ConfirmationModalDeleteClientField:
		'Dit is een permanente actie. Zodra het veld is verwijderd, is het niet langer toegankelijk voor uw resterende clients.',
	ConfirmationModalDeleteSectionMessage:
		'Zodra verwijderd, worden alle vragen in deze sectie verwijderd. Deze actie kan niet ongedaan worden gemaakt.',
	ConfirmationModalDeleteService:
		'Dit is een permanente actie. Zodra de service is verwijderd, is deze niet langer toegankelijk op uw werkruimte.',
	ConfirmationModalDeleteServiceGroup:
		'Als u een verzameling verwijdert, worden alle services uit de groep verwijderd en keert u terug naar uw servicelijst. Deze actie kan niet ongedaan worden gemaakt.',
	ConfirmationModalDeleteTranscript: 'Weet u zeker dat u het transcript wilt verwijderen?',
	ConfirmationModalDescriptionDeleteClient:
		'Zodra de klant is verwijderd, hebt u geen toegang meer tot de klantgegevens.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Zodra u uw toegang verwijdert, kunt u de klantgegevens niet meer bekijken.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Hun profiel wordt niet verwijderd, alleen als relatie met deze klant.',
	ConfirmationModalDescriptionRemoveStaff: 'Weet u zeker dat u deze persoon uit de provider wilt verwijderen?',
	ConfirmationModalEndSession: 'Weet u zeker dat u de sessie wilt beëindigen?',
	ConfirmationModalTitle: 'Weet je het zeker?',
	Confirmed: 'Bevestigd',
	ConflictTimezoneWarningMessage: 'Conflicten kunnen optreden vanwege meerdere tijdzones',
	Connect: 'Verbinden',
	ConnectExistingClientOrContact: 'Nieuwe klant/contactpersoon aanmaken',
	ConnectInboxGoogleDescription: 'Een Gmail-account of Google-groepslijst toevoegen',
	ConnectInboxMicrosoftDescription: 'Een Outlook-, Office365- of Exchange-account toevoegen',
	ConnectInboxModalDescription:
		'Verbind uw apps om al uw communicatie naadloos te verzenden, ontvangen en volgen op één centrale plek.',
	ConnectInboxModalExistingDescription:
		'Gebruik een bestaande verbinding vanuit de instellingen van uw verbonden apps om het configuratieproces te stroomlijnen.',
	ConnectInboxModalExistingTitle: 'Bestaande verbonden app in Carepatron',
	ConnectInboxModalTitle: 'Postvak IN verbinden',
	ConnectToStripe: 'Verbinden met Stripe',
	ConnectZoom: 'Zoom verbinden',
	ConnectZoomModalDescription: 'Laat Carepatron videogesprekken voor uw afspraken beheren.',
	ConnectedAppDisconnectedNotificationSubject:
		'We hebben de verbinding met {account} verloren. Maak opnieuw verbinding.',
	ConnectedAppSyncDescription: `Beheer verbonden apps om rechtstreeks vanuit Carepatron gebeurtenissen in agenda's van derden te maken.`,
	ConnectedApps: 'Verbonden apps',
	ConnectedAppsGMailDescription: 'Gmail-accounts of Google-groepen toevoegen',
	ConnectedAppsGoogleCalendarDescription: 'Voeg kalenders, accounts of Google-groepenlijsten toe',
	ConnectedAppsGoogleDescription: `Voeg uw Gmail-account toe en synchroniseer Google-agenda's`,
	ConnectedAppsMicrosoftDescription: 'Een Outlook-, Office365- of Exchange-account toevoegen',
	ConnectedCalendars: 'Verbonden kalenders',
	ConsentDocumentation: 'Formulieren en overeenkomsten',
	ConsentDocumentationPublicTemplateError:
		'Om veiligheidsredenen kunt u alleen sjablonen van uw team kiezen (niet-openbaar).',
	ConstructionWorker: 'Bouwvakker',
	Consultant: 'Consultant',
	Contact: 'Contact',
	ContactAccessTypeHelperText: 'Hiermee kunnen familiebeheerders informatie bijwerken',
	ContactAccessTypeHelperTextMoreInfo:
		'Dit stelt je in staat om notities/documenten over {clientFirstName} te delen.',
	ContactAddressLabelBilling: 'Facturering',
	ContactAddressLabelHome: 'Thuis',
	ContactAddressLabelOthers: 'Anderen',
	ContactAddressLabelWork: 'Werk',
	ContactChangeConfirmation:
		'Het wijzigen van de factuurcontactpersoon verwijdert alle regelposten die betrekking hebben op <mark>{contactName}</mark>',
	ContactDetails: 'Contactgegevens',
	ContactEmailLabelOthers: 'Anderen',
	ContactEmailLabelPersonal: 'Persoonlijk',
	ContactEmailLabelSchool: 'School',
	ContactEmailLabelWork: 'Werk',
	ContactInformation: 'Contactgegevens',
	ContactInformationText: 'Contactgegevens',
	ContactListCreateButton: 'Nieuw contact',
	ContactName: 'Contactpersoon',
	ContactPhoneLabelHome: 'Thuis',
	ContactPhoneLabelMobile: 'Mobiel',
	ContactPhoneLabelSchool: 'School',
	ContactPhoneLabelWork: 'Werk',
	ContactRelationship: 'Contactrelatie',
	ContactRelationshipFormAccessType: 'Geef toegang tot gedeelde informatie',
	ContactRelationshipGrantAccessInfo: 'Hiermee kunt u aantekeningen delen ',
	ContactSupport: 'Neem contact op met de ondersteuning',
	Contacts: 'Contacten',
	ContainerIdNotSet: 'Container-ID niet ingesteld',
	Contemporary: 'Modern',
	Continue: 'Doorgaan',
	ContinueDictating: 'Blijf dicteren',
	ContinueEditing: 'Doorgaan met bewerken',
	ContinueImport: 'Doorgaan met importeren',
	ContinueTranscription: 'Doorgaan met transcriptie',
	ContinueWithApple: 'Doorgaan met Apple',
	ContinueWithGoogle: 'Doorgaan met Google',
	Conversation: 'Gesprek',
	Copay: 'Eigen bijdrage',
	CopayOrCoinsurance: 'Eigen bijdrage of meeverzekering',
	Copayment: 'Eigen bijdrage',
	CopiedToClipboard: 'Gekopieerd naar klembord',
	Copy: 'Kopiëren',
	CopyAddressSuccessSnackbar: 'Gekopieerd adres naar klembord',
	CopyCode: 'Kopieer code',
	CopyCodeToClipboardSuccess: 'Code gekopieerd naar klembord',
	CopyEmailAddressSuccessSnackbar: 'E-mailadres gekopieerd naar klembord',
	CopyLink: 'Link kopiëren',
	CopyLinkForCall: 'Kopieer deze link om deze oproep te delen:',
	CopyLinkSuccessSnackbar: 'Link gekopieerd naar klembord',
	CopyMeetingLink: 'Kopieer de vergaderlink',
	CopyPaymentLink: 'Kopieer betalingslink',
	CopyPhoneNumberSuccessSnackbar: 'Gekopieerd telefoonnummer naar klembord',
	CopyTemplateLink: 'Link naar sjabloon kopiëren',
	CopyTemplateLinkSuccess: 'Link gekopieerd naar klembord',
	CopyToClipboardError: 'Kon niet kopiëren naar klembord. Probeer het opnieuw.',
	CopyToTeamTemplates: 'Kopiëren naar teamsjablonen',
	CopyToWorkspace: 'Kopiëren naar werkruimte',
	Cosmetologist: 'Schoonheidsspecialiste',
	Cost: 'Kosten',
	CostErrorMessage: 'Kosten zijn vereist',
	Counseling: 'Counseling',
	Counselor: 'Raadgever',
	Counselors: 'Adviseurs',
	CountInvoicesAdded: '{count, plural, one {# Factuur toegevoegd} other {# Facturen toegevoegd}}',
	CountNotesAdded: '{count, plural, one {# Opmerking toegevoegd} other {# Opmerkingen toegevoegd}}',
	CountSelected: '{count} geselecteerd',
	CountTimes: '{count} keer',
	Country: 'Land',
	Cousin: 'Neef',
	CoverageType: 'Dekkingstype',
	Covered: 'Bedekt',
	Create: 'Creëren',
	CreateANewClient: 'Een nieuwe klant aanmaken',
	CreateAccount: 'Account aanmaken',
	CreateAndSignNotes: 'Maak en onderteken een notitie met klanten',
	CreateAvailabilityScheduleFailure: 'Het is niet gelukt om een nieuw beschikbaarheidsschema te maken',
	CreateAvailabilityScheduleSuccess: 'Nieuwe beschikbaarheidsplanning succesvol aangemaakt',
	CreateBillingItems: 'Facturatie-items aanmaken',
	CreateCallFormButton: 'Startgesprek',
	CreateCallFormInviteOnly: 'Alleen op uitnodiging',
	CreateCallFormInviteOnlyMoreInfo:
		'Alleen mensen die zijn uitgenodigd voor dit gesprek kunnen deelnemen. Om dit gesprek met anderen te delen, vinkt u dit gewoon uit en kopieert/plakt u de link op de volgende pagina',
	CreateCallFormRecipients: 'Ontvangers',
	CreateCallFormRegion: 'Hostingregio',
	CreateCallModalAddClientContactSelectorLabel: 'Klantcontacten',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Zoeken op klantnaam',
	CreateCallModalAddStaffSelectorLabel: 'Teamleden (optioneel)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Zoeken op personeelsnaam',
	CreateCallModalDescription: `Start een gesprek en nodig medewerkers en/of contactpersonen uit. U kunt ook het vakje 'Privé' uitvinken om dit gesprek deelbaar te maken voor iedereen met Carepatron`,
	CreateCallModalTitle: 'Start een gesprek',
	CreateCallModalTitleLabel: 'Titel (optioneel)',
	CreateCallNoPersonIdToolTip: 'Alleen contacten/klanten met toegang tot het portaal kunnen deelnemen aan gesprekken',
	CreateClaim: 'Claim aanmaken',
	CreateClaimCompletedMessage: 'Uw claim is aangemaakt.',
	CreateClientModalTitle: 'Nieuwe klant',
	CreateContactModalTitle: 'Nieuw contact',
	CreateContactRelationshipButton: 'Relatie toevoegen',
	CreateContactSelectorDefaultOption: '  Contactpersoon aanmaken',
	CreateContactWithRelationshipFormAccessType: 'Toegang verlenen tot gedeelde informatie ',
	CreateDocumentDnDPrompt: 'Slepen en neerzetten om bestanden te uploaden',
	CreateDocumentSizeLimit: 'Bestandsgrootte per bestand {size}MB. {total} bestanden in totaal.',
	CreateFreeAccount: 'Maak een gratis account aan',
	CreateInvoice: 'Factuur aanmaken',
	CreateLink: 'Link maken',
	CreateNew: 'Nieuw maken',
	CreateNewAppointment: 'Nieuwe afspraak maken',
	CreateNewClaim: 'Een nieuwe claim aanmaken',
	CreateNewClaimForAClient: 'Maak een nieuwe claim aan voor een klant.',
	CreateNewClient: 'Nieuwe klant aanmaken',
	CreateNewConnection: 'Nieuwe verbinding',
	CreateNewContact: 'Nieuw contact aanmaken',
	CreateNewField: 'Nieuw veld aanmaken',
	CreateNewLocation: 'Nieuwe locatie',
	CreateNewService: 'Nieuwe service maken',
	CreateNewServiceGroupFailure: 'Het is niet gelukt om een nieuwe collectie te maken',
	CreateNewServiceGroupMenu: 'Nieuwe collectie',
	CreateNewServiceGroupSuccess: 'Nieuwe collectie succesvol aangemaakt',
	CreateNewServiceMenu: 'Nieuwe dienst',
	CreateNewTeamMember: 'Nieuw teamlid aanmaken',
	CreateNewTemplate: 'Nieuwe sjabloon',
	CreateNote: 'Notitie maken',
	CreateSuperbillReceipt: 'Nieuwe superbill',
	CreateSuperbillReceiptSuccess: 'Superbill-ontvangstbewijs succesvol aangemaakt',
	CreateTemplateFolderSuccessMessage: 'Succesvol {folderTitle} aangemaakt',
	Created: 'Gemaakt',
	CreatedAt: 'Gemaakt {timestamp}',
	Credit: 'Credit',
	CreditAdded: 'Krediet toegepast',
	CreditAdjustment: 'Kredietaanpassing',
	CreditAdjustmentReasonHelperText: 'Dit is een interne notitie en is niet zichtbaar voor uw klant.',
	CreditAdjustmentReasonPlaceholder:
		'Het toevoegen van een aanpassingsreden kan helpen bij het beoordelen van factureerbare transacties',
	CreditAmount: '{amount} CR',
	CreditBalance: 'Creditsaldo',
	CreditCard: 'Creditcard',
	CreditCardExpire: 'Verloopt {exp_month}/{exp_year}',
	CreditCardNumber: 'Creditcardnummer',
	CreditDebitCard: 'Kaart',
	CreditIssued: 'Krediet uitgegeven',
	CreditsUsed: 'Gebruikte credits',
	Crop: 'Gewas',
	Currency: 'Munteenheid',
	CurrentCredit: 'Huidig krediet',
	CurrentEventTime: 'Huidige gebeurtenistijd',
	CurrentPlan: 'Huidig plan',
	Custom: 'Aangepast',
	CustomRange: 'Aangepast bereik',
	CustomRate: 'Aangepast tarief',
	CustomRecurrence: 'Aangepaste herhaling',
	CustomServiceAvailability: 'Beschikbaarheid van de service',
	CustomerBalance: 'Klantensaldo',
	CustomerName: 'Klantnaam',
	CustomerNameIsRequired: 'Klantnaam is vereist',
	CustomerServiceRepresentative: 'Klantenservicemedewerker',
	CustomiseAppointments: 'Afspraken aanpassen',
	CustomiseBookingLink: 'Boekingsopties aanpassen',
	CustomiseBookingLinkServicesInfo: 'Klanten kunnen alleen kiezen voor boekbare diensten',
	CustomiseBookingLinkServicesLabel: 'Diensten',
	CustomiseClientRecordsAndWorkspace: 'Pas uw cliëntgegevens en werkruimte aan',
	CustomiseClientSettings: 'Clientinstellingen aanpassen',
	Customize: 'Aanpassen',
	CustomizeAppearance: 'Pas het uiterlijk aan',
	CustomizeAppearanceDesc:
		'Pas de weergave van uw online boekingssysteem aan zodat deze bij uw merk past en optimaliseer de manier waarop uw diensten aan klanten worden getoond.',
	CustomizeClientFields: 'Pas clientvelden aan',
	CustomizeInvoiceTemplate: 'Factuursjabloon aanpassen',
	CustomizeInvoiceTemplateDescription: 'Maak moeiteloos professionele facturen die uw merk weerspiegelen.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX is vereist',
	Daily: 'Dagelijks',
	DanceTherapist: 'Danstherapeut',
	DangerZone: 'Gevarenzone',
	Dashboard: 'Dashboard',
	Date: 'Datum',
	DateAndTime: 'Datum ',
	DateDue: 'Vervaldatum',
	DateErrorMessage: 'Datum is vereist',
	DateFormPrimaryText: 'Datum',
	DateFormSecondaryText: 'Kies uit een datumkiezer',
	DateIssued: 'Datum van uitgifte',
	DateOfPayment: 'Datum van betaling',
	DateOfService: 'Datum van service',
	DateOverride: 'Datum overschrijven',
	DateOverrideColor: 'Datum override kleur',
	DateOverrideInfo: `Met datumoverschrijvingen kunnen zorgverleners handmatig hun beschikbaarheid voor specifieke data aanpassen door de reguliere schema's te overschrijven.`,
	DateOverrideInfoBanner:
		'In deze tijdsloten kunnen alleen de specifieke diensten voor deze datumoverschrijding worden geboekt. Er zijn geen andere online boekingen toegestaan.',
	DateOverrides: 'Datum overschrijft',
	DatePickerFormPrimaryText: 'Datum',
	DatePickerFormSecondaryText: 'Kies een datum',
	DateRange: 'Datumbereik',
	DateRangeFormPrimaryText: 'Datumbereik',
	DateRangeFormSecondaryText: 'Kies een datumbereik',
	DateReceived: 'Datum ontvangen',
	DateSpecificHours: 'Datum specifieke uren',
	DateSpecificHoursDescription:
		'Voeg data toe wanneer uw beschikbaarheid afwijkt van uw geplande uren of om een service op een specifieke datum aan te bieden.',
	DateUploaded: 'Geüpload {date, date, medium}',
	Dates: 'Data',
	Daughter: 'Dochter',
	Day: 'Dag',
	DayPlural: '{count, plural, one {dag} other {dagen}}',
	Days: 'Dagen',
	DaysPlural: '{age, plural, one {# dag} other {# dagen}}',
	DeFacto: 'De facto',
	Deactivated: 'Gedesactiveerd',
	Debit: 'Debiteren',
	DecreaseIndent: 'Inspringing verkleinen',
	Deductibles: 'Eigen risico',
	Default: 'Standaard',
	DefaultBillingProfile: 'Standaard factureringsprofiel',
	DefaultDescription: 'Standaardbeschrijving',
	DefaultEndOfLine: 'Geen items meer',
	DefaultInPerson: 'Afspraken met cliënten',
	DefaultInvoiceTitle: 'Standaardtitel',
	DefaultNotificationSubject: 'Je hebt een nieuwe melding ontvangen voor {notificationType}',
	DefaultPaymentMethod: 'Standaard betaalmethode',
	DefaultService: 'Standaard service',
	DefaultValue: 'Standaard',
	DefaultVideo: 'E-mail met video-afspraak voor cliënt',
	DefinedTemplateType: '{invoiceTemplate} sjabloon',
	Delete: 'Verwijderen',
	DeleteAccountButton: 'Account verwijderen',
	DeleteAccountDescription: 'Verwijder uw account van het platform',
	DeleteAccountPanelInfoAlert:
		'U moet uw werkruimten verwijderen voordat u uw profiel verwijdert. Om door te gaan, schakelt u over naar een werkruimte en selecteert u Instellingen > Werkruimte-instellingen.',
	DeleteAccountTitle: 'Account verwijderen',
	DeleteAppointment: 'Afspraak verwijderen',
	DeleteAppointmentDescription: 'Weet u zeker dat u deze afspraak wilt verwijderen? U kunt deze later herstellen.',
	DeleteAvailabilityScheduleFailure: 'Beschikbaarheidsschema kan niet worden verwijderd',
	DeleteAvailabilityScheduleSuccess: 'Beschikbaarheidsschema succesvol verwijderd',
	DeleteBillable: 'Factureerbaar verwijderen',
	DeleteBillableConfirmationMessage:
		'Weet u zeker dat u deze factureerbare wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt.',
	DeleteBillingProfileConfirmationMessage: 'Hiermee wordt het factureringsprofiel permanent verwijderd.',
	DeleteCardConfirmation: 'Dit is een permanente actie. Zodra de kaart is verwijderd, kunt u er niet meer bij.',
	DeleteCategory: 'Categorie verwijderen (dit is niet permanent, tenzij de wijzigingen worden opgeslagen)',
	DeleteClientEventConfirmationDescription: 'Dit wordt permanent verwijderd.',
	DeleteClients: 'Klanten verwijderen',
	DeleteCollection: 'Verzameling verwijderen',
	DeleteColumn: 'Kolom verwijderen',
	DeleteConversationConfirmationDescription:
		'Verwijder dit gesprek voorgoed. Deze actie kan niet ongedaan worden gemaakt.',
	DeleteConversationConfirmationTitle: 'Gesprek definitief verwijderen',
	DeleteExternalEventDescription: 'Weet u zeker dat u deze afspraak wilt verwijderen?',
	DeleteFileConfirmationModalPrompt: 'Nadat u het bestand hebt verwijderd, kunt u het niet meer terughalen.',
	DeleteFolder: 'Map verwijderen',
	DeleteFolderConfirmationMessage:
		'Weet u zeker dat u deze map {name} wilt verwijderen? Alle items in deze map worden ook verwijderd. U kunt dit later herstellen.',
	DeleteForever: 'Voor altijd verwijderen',
	DeleteInsurancePayerConfirmationMessage:
		'Het verwijderen van {payer} zal deze verwijderen uit uw lijst met verzekeringsbetalers. Deze actie is permanent en kan niet worden teruggedraaid.',
	DeleteInsurancePayerFailure: 'Het is niet gelukt om de verzekeringsbetaler te verwijderen',
	DeleteInsurancePolicyConfirmationMessage: 'Hiermee wordt de verzekeringspolis definitief verwijderd.',
	DeleteInvoiceConfirmationDescription:
		'Deze actie kan niet ongedaan worden gemaakt. De factuur en alle betalingen die eraan gekoppeld zijn, worden permanent verwijderd.',
	DeleteLocationConfirmation:
		'Het verwijderen van een locatie is een permanente actie. Zodra u deze verwijdert, heeft u er geen toegang meer toe. Deze actie kan niet ongedaan worden gemaakt.',
	DeletePayer: 'Betaler verwijderen',
	DeletePracticeWorkspace: 'Oefenwerkruimte verwijderen',
	DeletePracticeWorkspaceDescription: 'Deze oefenwerkruimte permanent verwijderen',
	DeletePracticeWorkspaceFailedSnackbar: 'Het is niet gelukt om de werkruimte te verwijderen',
	DeletePracticeWorkspaceModalCancelButton: 'Ja, annuleer mijn abonnement',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Voordat u uw werkruimte verwijdert, moet u eerst uw abonnement opzeggen.',
	DeletePracticeWorkspaceModalConfirmButton: 'Ja, werkruimte permanent verwijderen',
	DeletePracticeWorkspaceModalDescription:
		'{name} werkruimte wordt permanent verwijderd en alle teamleden verliezen de toegang. Download alle belangrijke gegevens of berichten die je nodig hebt voordat de verwijdering plaatsvindt. Deze actie kan niet ongedaan gemaakt worden.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Deze beslissing is genomen vanwege:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Reden',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Vertel ons waarom u uw account wilt verwijderen.',
	DeletePracticeWorkspaceModalTitle: 'Weet je het zeker?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'De toegang van alle teamleden is verwijderd',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} is met succes verwijderd',
	DeletePublicTemplateContent: 'Hiermee verwijdert u alleen de openbare sjabloon en niet de sjabloon van uw team.',
	DeleteRecurringAppointmentModalTitle: 'Herhaalde afspraak verwijderen',
	DeleteRecurringEventModalTitle: 'Herhaalde vergadering verwijderen',
	DeleteRecurringReminderModalTitle: 'Herhalende herinnering verwijderen',
	DeleteRecurringTaskModalTitle: 'Herhalende taak verwijderen',
	DeleteReminderConfirmation:
		'Dit is een permanente actie. Zodra de herinnering is verwijderd, kunt u er niet meer bij. Heeft alleen invloed op nieuwe afspraken',
	DeleteSection: 'Sectie verwijderen',
	DeleteSectionInfo:
		'Het verwijderen van de sectie <strong>{section}</strong> zal alle bestaande velden erin verbergen. Deze actie kan niet ongedaan worden gemaakt.',
	DeleteSectionWarning:
		'Kernvelden kunnen niet worden verwijderd en worden verplaatst naar de bestaande sectie **{sectie}**.',
	DeleteServiceFailure: 'Het verwijderen van de service is mislukt',
	DeleteServiceSuccess: 'Service succesvol verwijderd',
	DeleteStaffScheduleOverrideDescription: `Het verwijderen van deze datumoverruling op {value} zal deze uit uw schema's verwijderen en kan uw online service beschikbaarheid veranderen. Deze actie kan niet ongedaan worden gemaakt.`,
	DeleteSuperbillConfirmationDescription:
		'Deze actie kan niet ongedaan worden gemaakt. Het verwijdert permanent de Superbill-ontvangst.',
	DeleteSuperbillFailure: 'Het is niet gelukt om de Superbill-ontvangstbon te verwijderen',
	DeleteSuperbillSuccess: 'Superbill-ontvangstbewijs succesvol verwijderd',
	DeleteTaxRateConfirmationDescription: 'Weet u zeker dat u dit belastingtarief wilt verwijderen?',
	DeleteTemplateContent: 'Deze actie kan niet ongedaan worden gemaakt',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} succesvol verwijderd',
	DeleteTemplateSuccessMessage: '{templateTitle} succesvol verwijderd',
	DeleteTemplateTitle: 'Weet u zeker dat u deze sjabloon wilt verwijderen?',
	DeleteTranscript: 'Transcript verwijderen',
	DeleteWorkspace: 'Werkruimte verwijderen',
	Deleted: 'Verwijderd',
	DeletedBy: 'Verwijderd door',
	DeletedContact: 'Contactpersoon verwijderd',
	DeletedOn: 'Verwijderd op',
	DeletedStatusLabel: 'Verwijderde status',
	DeletedUserTooltip: 'Deze klant is verwijderd',
	DeliveryMethod: 'Bezorgmethode',
	Demo: 'Demo',
	Denied: 'Geweigerd',
	Dental: 'Tandheelkunde',
	DentalAssistant: 'Tandartsassistent',
	DentalHygienist: 'Mondhygiënist',
	Dentist: 'Tandarts',
	Dentists: 'Tandartsen',
	Description: 'Beschrijving',
	DescriptionMustNotExceed: 'Beschrijving mag {max} tekens niet overschrijden',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { met {staffName}}}',
	Details: 'Details',
	Devices: 'Apparaten',
	Diagnosis: 'Diagnose',
	DiagnosisAndBillingItems: 'Diagnose ',
	DiagnosisCode: 'Diagnosecode',
	DiagnosisCodeErrorMessage: 'Er is een diagnosecode vereist',
	DiagnosisCodeSelectorPlaceholder: 'Zoeken en toevoegen vanuit ICD-10 diagnostische codes',
	DiagnosisCodeSelectorTooltip:
		'Diagnosecodes worden gebruikt om de ontvangst van superfacturen voor verzekeringsvergoedingen te automatiseren',
	DiagnosticCodes: 'Diagnostische codes',
	Dictate: 'Dicteren',
	DictatingIn: 'Dicteren in',
	Dictation: 'Dicteren',
	DidNotAttend: 'Niet aanwezig geweest',
	DidNotComplete: 'Niet voltooid',
	DidNotProviderEnoughValue: 'Biedt niet genoeg waarde',
	DidntProvideEnoughValue: 'Biedt niet genoeg waarde',
	DieteticsOrNutrition: 'Diëtetiek of voeding',
	Dietician: 'Diëtist',
	Dieticians: 'Diëtisten',
	Dietitian: 'Diëtist',
	DigitalSign: 'Teken hier:',
	DigitalSignHelp: '(Klik/druk om te tekenen)',
	DirectDebit: 'Automatische incasso',
	DirectTextLink: 'Directe tekstlink',
	Disable: 'Uitzetten',
	DisabledEmailInfo: 'Wij kunnen uw e-mailadres niet bijwerken, omdat uw account niet door ons wordt beheerd.',
	Discard: 'Weggooien',
	DiscardChanges: 'Wijzigingen negeren',
	DiscardDrafts: 'Concepten verwijderen',
	Disconnect: 'Loskoppelen',
	DisconnectAppConfirmation: 'Wilt u deze app loskoppelen?',
	DisconnectAppConfirmationDescription: 'Weet u zeker dat u deze app wilt loskoppelen?',
	DisconnectAppConfirmationTitle: 'App loskoppelen',
	Discount: 'Korting',
	DisplayCalendar: 'Weergeven in Carepatron',
	DisplayName: 'Weergavenaam',
	DisplayedToClients: 'Getoond aan klanten',
	DiversionalTherapist: 'Diversionale therapeut',
	DoItLater: 'Doe het later',
	DoNotImport: 'Niet importeren',
	DoNotSend: 'Niet verzenden',
	DoThisLater: 'Doe dit later',
	DoYouWantToEndSession: 'Wilt u doorgaan of uw sessie nu beëindigen?',
	Doctor: 'Arts',
	Doctors: 'Doktoren',
	DoesNotRepeat: 'Herhaalt zich niet',
	DoesntWorkWellWithExistingTools: 'Werkt niet goed met onze bestaande tools of workflows',
	DogWalker: 'Hondenuitlater',
	Done: 'Klaar',
	DontAllowClientsToCancel: 'Laat klanten niet annuleren',
	DontHaveAccount: 'Heb je nog geen account?',
	DontSend: 'Niet verzenden',
	Double: 'Dubbele',
	DowngradeTo: 'Downgrade naar {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Sorry, u kunt uw plan niet downgraden omdat u te veel teamleden hebt. Verwijder er een aantal van uw provider en probeer het opnieuw.',
	Download: 'Download',
	DownloadAsPdf: 'Downloaden als PDF',
	DownloadERA: 'Download ERA',
	DownloadPDF: 'PDF downloaden',
	DownloadTemplateFileName: 'Carepatron Schakeltemplate.csv',
	DownloadTemplateTileDescription: 'Gebruik onze spreadsheet sjabloon om je klanten te organiseren en te uploaden.',
	DownloadTemplateTileLabel: 'Download sjabloon',
	Downloads: '{number, plural, one {<span>#</span> Download} other {<span>#</span> Downloads}}',
	DoxyMe: 'Doxy.mij',
	Draft: 'Voorlopige versie',
	DraftResponses: 'Conceptreactie',
	DraftSaved: 'Opgeslagen wijzigingen',
	DragAndDrop: 'slepen en neerzetten',
	DragDropText: 'Sleep en zet gezondheidsdocumenten neer',
	DragToMove: 'Slepen om te verplaatsen',
	DragToMoveOrActivate: 'Slepen om te verplaatsen of te activeren',
	DramaTherapist: 'Dramatherapeut',
	DropdownFormFieldPlaceHolder: 'Kies opties uit de lijst',
	DropdownFormPrimaryText: 'Keuzelijst',
	DropdownFormSecondaryText: 'Kies uit een lijst met opties',
	DropdownTextFieldError: 'De tekst van de dropdown-optie mag niet leeg zijn',
	DropdownTextFieldPlaceholder: 'Een vervolgkeuzemenu-optie toevoegen',
	Due: 'Deadline',
	DueDate: 'Deadline',
	Duplicate: 'Duplicaat',
	DuplicateAvailabilityScheduleFailure: 'Beschikbaarheidsschema dupliceren mislukt',
	DuplicateAvailabilityScheduleSuccess: 'Het schema van {name} is succesvol gedupliceerd',
	DuplicateClientBannerAction: 'Beoordeling',
	DuplicateClientBannerDescription:
		'Wanneer u dubbele cliëntgegevens samenvoegt, worden deze samengevoegd tot één gegevensbestand. Alle unieke cliëntgegevens blijven behouden.',
	DuplicateClientBannerTitle: '{count} Dubbele gevonden',
	DuplicateColumn: 'Dubbele kolom',
	DuplicateContactFieldSettingErrorSnackbar: 'Dubbele sectienamen zijn niet toegestaan',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Er mogen geen dubbele veldnamen zijn',
	DuplicateEmailError: 'Dubbele e-mail',
	DuplicateHeadingName: 'Sectie {name} bestaat al',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Er bestaat al een factuur met hetzelfde "factuurnummer".',
	DuplicateRecords: 'Dubbele records',
	DuplicateRecordsMinimumError: 'Er moeten minimaal 2 records worden geselecteerd',
	DuplicateRecordsRequired: 'Selecteer minimaal 1 record om te scheiden',
	DuplicateServiceFailure: 'Mislukt om <strong>{title}</strong> te dupliceren',
	DuplicateServiceSuccess: 'Geslaagd gedupliceerd <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Map succesvol gedupliceerd',
	DuplicateTemplateSuccess: 'Sjabloon succesvol gedupliceerd',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'DX-code',
	DxCodeSelectPlaceholder: 'Zoeken en toevoegen vanuit ICD-10-codes',
	EIN: 'EEN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Geen',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>bevat aanpassingen</i>} other {}}',
	EarnReferralCredit: 'Verdien ${creditAmount}',
	Economist: 'Econoom',
	Edit: 'Bewerking',
	EditArrangements: 'Bewerken van arrangementen',
	EditBillTo: 'Bewerk factuur naar',
	EditClient: 'Client bewerken',
	EditClientFileModalDescription: `Bewerk de toegang tot dit bestand door de opties in de selectievakjes 'Zichtbaar voor' te selecteren`,
	EditClientFileModalTitle: 'Bestand bewerken',
	EditClientNoteModalDescription: `Bewerk de inhoud van de notitie. Gebruik de sectie 'Viewable by' om te wijzigen wie de notitie kan zien.`,
	EditClientNoteModalTitle: 'Notitie bewerken',
	EditConnectedAppButton: 'Bewerking',
	EditConnections: 'Bewerk connecties{account, select, null { } undefined { } other { voor {account}}}',
	EditContactDetails: 'Contactgegevens bewerken',
	EditContactFormIsClientLabel: 'Converteren naar klant',
	EditContactIsClientCheckboxWarning:
		'Het omzetten van een contactpersoon in een klant kan niet ongedaan worden gemaakt',
	EditContactIsClientWanringModal:
		'Het omzetten van dit contact in een Client kan niet ongedaan worden gemaakt. Echter, alle relaties blijven bestaan en u hebt nu toegang tot hun notities, bestanden en andere documentatie.',
	EditContactRelationship: 'Contactrelatie bewerken',
	EditDetails: 'Gegevens bewerken',
	EditFileModalTitle: 'Bestand bewerken voor {name}',
	EditFolder: 'Map bewerken',
	EditFolderDescription: 'Hernoem de map naar...',
	EditInvoice: 'Factuur bewerken',
	EditInvoiceDetails: 'Factuurgegevens bewerken',
	EditLink: 'Link bewerken',
	EditLocation: 'Locatie bewerken',
	EditLocationFailure: 'Locatie kon niet worden bijgewerkt',
	EditLocationSucess: 'Locatie succesvol bijgewerkt',
	EditPaymentDetails: 'Betalingsgegevens bewerken',
	EditPaymentMethod: 'Betaalmethode bewerken',
	EditPersonalDetails: 'Persoonlijke gegevens bewerken',
	EditPractitioner: 'Bewerk beoefenaar',
	EditProvider: 'Bewerk provider',
	EditProviderDetails: 'Providergegevens bewerken',
	EditRecurrence: 'Recurrentie bewerken',
	EditRecurringAppointmentModalTitle: 'Herhaalde afspraak bewerken',
	EditRecurringEventModalTitle: 'Bewerken herhalende vergadering',
	EditRecurringReminderModalTitle: 'Herhalende herinnering bewerken',
	EditRecurringTaskModalTitle: 'Herhalende taak bewerken',
	EditRelationshipModalTitle: 'Relatie bewerken',
	EditService: 'Bewerkingsservice',
	EditServiceFailure: 'Het is niet gelukt om de nieuwe service bij te werken',
	EditServiceGroup: 'Collectie bewerken',
	EditServiceGroupFailure: 'Het is niet gelukt om de collectie bij te werken',
	EditServiceGroupSuccess: 'Collectie succesvol bijgewerkt',
	EditServiceSuccess: 'Nieuwe service succesvol bijgewerkt',
	EditStaffDetails: 'Bewerk personeelsgegevens',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Kan e-mailadres niet updaten. Maak een nieuw teamlid met een nieuw e-mailadres.',
	EditSubscriptionBilledQuantity: 'Gefactureerde hoeveelheid',
	EditSubscriptionBilledQuantityValue: '{billedUsers} teamleden',
	EditSubscriptionLimitedTimeOffer: 'Beperkte tijd aanbieding! 50% korting voor 6 maanden.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Uw abonnementskosten worden aangepast wanneer u teamleden toevoegt of verwijdert.',
	EditSubscriptionUpgradeContent:
		'Uw account wordt onmiddellijk bijgewerkt naar het nieuwe abonnement en de nieuwe factureringsperiode. Alle prijswijzigingen worden automatisch in rekening gebracht via uw opgeslagen betaalmethode of bijgeschreven op uw account.',
	EditSubscriptionUpgradePlanTitle: 'Upgrade abonnementsplan',
	EditSuperbillReceipt: 'Bewerken superbill',
	EditTags: 'Labels bewerken',
	EditTemplate: 'Sjabloon bewerken',
	EditTemplateFolderSuccessMessage: 'Sjabloonmap succesvol bijgewerkt',
	EditValue: 'Bewerk {value}',
	Edited: 'Bewerkt',
	Editor: 'Editor',
	EditorAlertDescription:
		'Er is een niet-ondersteund formaat gedetecteerd. Laad de app opnieuw of neem contact op met ons supportteam.',
	EditorAlertTitle: 'We hebben problemen met het weergeven van deze inhoud',
	EditorPlaceholder:
		'Begin met schrijven, kies een sjabloon of voeg basisblokken toe om de antwoorden van uw klanten vast te leggen.',
	EditorTemplatePlaceholder: 'Begin met schrijven of voeg componenten toe om een sjabloon te bouwen',
	EditorTemplateWithSlashCommandPlaceholder: `Begin met schrijven of voeg basisblokken toe om clientreacties vast te leggen. Gebruik slash-commando's (/) voor snelle acties.`,
	EditorWithSlashCommandPlaceholder:
		'Begin met schrijven, kies een sjabloon of voeg basisblokken toe om reacties van cliënten vast te leggen. Gebruik slash-opdrachten ( / ) voor snelle acties.',
	EffectiveStartEndDate: 'Effectieve start- en einddatum',
	ElectricalEngineer: 'Elektrotechnisch ingenieur',
	Electronic: 'Elektronisch',
	ElectronicSignature: 'Elektronische handtekening',
	ElementarySchoolTeacher: 'Leraar basisonderwijs',
	Eligibility: 'Toelatingsvoorwaarden',
	Email: 'E-mail',
	EmailAlreadyExists: 'E-mailadres bestaat al',
	EmailAndSms: 'E-mail ',
	EmailBody: 'E-mailtekst',
	EmailContainsIgnoredDescription:
		'De volgende e-mail bevat een e-mail van een afzender die momenteel wordt genegeerd. Wilt u doorgaan?',
	EmailInviteToPortalBody: `Hallo {contactName},
Volg deze link om in te loggen op uw beveiligde klantportaal en uw zorg gemakkelijk te beheren.

Met vriendelijke groeten,

{providerName}`,
	EmailInviteToPortalSubject: 'Welkom bij {providerName}',
	EmailInvoice: 'Factuur e-mailen',
	EmailInvoiceOverdueBody: `Hallo {contactName},
Uw factuur {invoiceNumber} is vervallen.
Betalen uw factuur online via de onderstaande link.

Neem contact met ons op als u vragen heeft.

Bedankt,
{providerName}`,
	EmailInvoicePaidBody: `Hallo {contactName},
Uw factuur {invoiceNumber} is betaald.
Om een kopie van uw factuur te bekijken en te downloaden, volgt u de onderstaande link.

Heeft u vragen, laat het ons dan weten.

Met vriendelijke groeten,
{providerName}`,
	EmailInvoiceProcessingBody: `Hallo {contactName},
Uw factuur {invoiceNumber} is klaar.
Volg de onderstaande link om uw factuur te bekijken.

Mocht u vragen hebben, laat het ons dan weten.

Met vriendelijke groet,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hoi {contactName}
Uw factuur {invoiceNumber} is klaar en dient te worden betaald voor {dueDate}.
Volg de onderstaande link om uw factuur online te bekijken en te betalen.

Mocht u vragen hebben, laat het ons dan weten.

Met vriendelijke groet,
{providerName}`,
	EmailInvoiceVoidBody: `Hallo {contactName},
Uw factuur {invoiceNumber} is ongeldig.
Om deze factuur te bekijken, volg de onderstaande link.

Heeft u vragen, neem dan contact met ons op.

Bedankt,
{providerName}`,
	EmailNotFound: 'E-mail niet gevonden',
	EmailNotVerifiedErrorCodeSnackbar: 'Kan actie niet uitvoeren. U moet uw e-mailadres verifiëren.',
	EmailNotVerifiedTitle: 'Je e-mailadres is niet geverifieerd. Sommige functies zijn beperkt.',
	EmailSendClientIntakeBody: `Hallo {contactName},
{providerName} wil graag dat je wat informatie verstrekt en belangrijke documenten bekijkt. Volg de onderstaande link om te beginnen.

Met vriendelijke groeten,

{providerName}`,
	EmailSendClientIntakeSubject: 'Welkom bij {providerName}',
	EmailSuperbillReceipt: 'E-mail superbill',
	EmailSuperbillReceiptBody: `Hallo {contactName},
{providerName} heeft je een kopie van je verklaring van terugbetalingsontvangst {date} gestuurd.

Je kunt deze direct downloaden en naar je verzekeringsmaatschappij sturen.`,
	EmailSuperbillReceiptFailure: 'Het is niet gelukt om de Superbill-ontvangstbon te versturen',
	EmailSuperbillReceiptSubject: '{providerName} heeft een verklaring van terugbetaling ontvangen',
	EmailSuperbillReceiptSuccess: 'Superbill-ontvangstbewijs succesvol verzonden',
	EmailVerificationDescription: 'Wij <span>verifiëren</span> nu uw account',
	EmailVerificationNotification: 'Er is een verificatie-e-mail verzonden naar {email}',
	EmailVerificationSuccess: 'Uw e-mailadres is succesvol gewijzigd naar {email}',
	Emails: 'E-mails',
	EmergencyContact: 'Noodcontact',
	EmployeesIdentificationNumber: 'Werknemersidentificatienummer',
	EmploymentStatus: 'Werkstatus',
	EmptyAgendaViewDescription: 'Er zijn geen evenementen om weer te geven.<mark> Maak nu een afspraak</mark>',
	EmptyBin: 'Lege prullenbak',
	EmptyBinConfirmationDescription:
		'Lege prullenbak zal alle <strong>{total} gesprekken</strong> in Verwijderd verwijderen. Deze actie kan niet ongedaan worden gemaakt.',
	EmptyBinConfirmationTitle: 'Gesprekken permanent verwijderen',
	EmptyTrash: 'Prullenbak leegmaken',
	Enable: 'Inschakelen',
	EnableCustomServiceAvailability: 'Beschikbaarheid van de service inschakelen',
	EnableCustomServiceAvailabilityDescription:
		'Bijvoorbeeld: eerste afspraken kunnen alleen dagelijks van 9.00 tot 10.00 uur worden geboekt.',
	EndCall: 'Einde gesprek',
	EndCallConfirmationForCreator:
		'U maakt hier voor iedereen een einde aan, omdat u degene bent die het gesprek initieert.',
	EndCallConfirmationHasActiveAttendees:
		'U staat op het punt om het gesprek te beëindigen, maar de klant(en) zijn al aangesloten. Wilt u ook aansluiten?',
	EndCallForAll: 'Einde gesprek voor iedereen',
	EndDate: 'Einddatum',
	EndDictation: 'Einde dictee',
	EndOfLine: 'Geen afspraken meer',
	EndSession: 'Sessie beëindigen',
	EndTranscription: 'Einde transcriptie',
	Ends: 'Einde',
	EndsOnDate: 'Eindigt op {date}',
	Enrol: 'Inschrijven',
	EnrollmentRejectedSubject: 'Uw inschrijving bij {payerName} is afgewezen',
	Enrolment: 'Inname',
	Enrolments: 'Inschrijvingen',
	EnrolmentsDescription: 'Bekijk en beheer de inschrijvingen van providers bij de verzekeraar.',
	EnterAName: 'Voer een naam in...',
	EnterFieldLabel: 'Voer veldlabel in...',
	EnterPaymentDetailsDescription:
		'Uw abonnementskosten worden automatisch aangepast wanneer u gebruikers toevoegt of verwijdert.',
	EnterSectionName: 'Voer een sectienaam in...',
	EnterSubscriptionPaymentDetails: 'Voer betalingsgegevens in',
	EnvironmentalScientist: 'Milieuwetenschapper',
	Epidemiologist: 'Epidemioloog',
	Eraser: 'Gom',
	Error: 'Fout',
	ErrorBoundaryAction: 'Pagina opnieuw laden',
	ErrorBoundaryDescription: 'Vernieuw de pagina en probeer het opnieuw.',
	ErrorBoundaryTitle: 'Oeps! Er is iets fout gegaan',
	ErrorCallNotFound:
		'De oproep kan niet worden gevonden. Deze is mogelijk verlopen of de maker heeft deze beëindigd.',
	ErrorCannotAccessCallUninvitedCode: 'Helaas, het lijkt erop dat u niet bent uitgenodigd voor dit gesprek.',
	ErrorFileUploadCustomMaxFileCount: 'Kan niet meer dan {count} bestanden tegelijk uploaden',
	ErrorFileUploadCustomMaxFileSize: 'Bestandsgrootte mag {mb} MB niet overschrijden',
	ErrorFileUploadInvalidFileType:
		'Ongeldig bestandstype dat potentiële virussen en schadelijke software kan bevatten',
	ErrorFileUploadMaxFileCount: 'Kan niet meer dan 150 bestanden tegelijk uploaden',
	ErrorFileUploadMaxFileSize: 'Bestandsgrootte mag niet groter zijn dan 100 MB',
	ErrorFileUploadNoFileSelected: 'Selecteer de bestanden die u wilt uploaden',
	ErrorInvalidNationalProviderId: 'De opgegeven Nationale Provider Id is ongeldig',
	ErrorInvalidPayerId: 'Het opgegeven Betaler ID is ongeldig',
	ErrorInvalidTaxNumber: 'Het opgegeven BTW-nummer is niet geldig',
	ErrorInviteExistingProviderStaffCode: 'Deze gebruiker bevindt zich al in de workspace.',
	ErrorInviteStaffExistingUser:
		'Sorry, het lijkt erop dat de gebruiker die u hebt toegevoegd al in ons systeem bestaat.',
	ErrorOnlySingleCallAllowed:
		'U kunt maar één gesprek tegelijk voeren. Beëindig het huidige gesprek om een nieuw gesprek te starten.',
	ErrorPayerNotFound: 'Betaler niet gevonden',
	ErrorProfilePhotoMaxFileSize: 'Uploaden mislukt! Bestandsgroottelimiet bereikt - 5MB',
	ErrorRegisteredExistingUser: 'Sorry, het lijkt erop dat u al geregistreerd bent.',
	ErrorUserSignInIncorrectCredentials: 'Ongeldig e-mailadres of wachtwoord. Probeer het opnieuw.',
	ErrorUserSigninGeneric: 'Sorry, er is iets fout gegaan.',
	ErrorUserSigninUserNotConfirmed:
		'Sorry, u moet uw account bevestigen voordat u zich kunt aanmelden. Controleer uw inbox voor instructies.',
	Errors: 'Fouten',
	EssentialPlanInclusionFive: 'Sjabloon importeren',
	EssentialPlanInclusionFour: '5 GB opslagruimte',
	EssentialPlanInclusionHeader: 'Alles in Gratis  ',
	EssentialPlanInclusionOne: 'Automatische en aangepaste herinneringen',
	EssentialPlanInclusionSix: 'Prioritaire ondersteuning',
	EssentialPlanInclusionThree: 'Videochat',
	EssentialPlanInclusionTwo: '2-weg kalender synchronisatie',
	EssentialSubscriptionPlanSubtitle: 'Vereenvoudig uw praktijk met de essentie',
	EssentialSubscriptionPlanTitle: 'Essentieel',
	Esthetician: 'Schoonheidsspecialiste',
	Estheticians: 'Schoonheidsspecialisten',
	EstimatedArrivalDate: 'Geschat. aankomst {numberOfDaysFromNow}',
	Ethnicity: 'Etniciteit',
	Europe: 'Europa',
	EventColor: 'Kleur ontmoeten',
	EventName: 'Naam van het evenement',
	EventType: 'Evenementtype',
	Every: 'Elke',
	Every2Weeks: 'Elke 2 weken',
	EveryoneInWorkspace: 'Iedereen op de werkvloer',
	ExercisePhysiologist: 'Fysioloog voor beweging',
	Existing: 'Bestaande',
	ExistingClients: 'Bestaande klanten',
	ExistingFolders: 'Bestaande mappen',
	ExpiredPromotionCode: 'Promotiecode is verlopen',
	ExpiredReferralDescription: 'Verwijzing is verlopen',
	ExpiredVerificationLink: 'Verlopen verificatielink',
	ExpiredVerificationLinkDescription: `Het spijt ons, maar de verificatielink waarop u hebt geklikt, is verlopen. Dit kan gebeuren als u langer dan 24 uur hebt gewacht om op de link te klikken of als u de link al hebt gebruikt om uw e-mailadres te verifiëren.

 Vraag een nieuwe verificatielink aan om uw e-mailadres te verifiëren.`,
	ExpiryDateRequired: 'Vervaldatum is vereist',
	ExploreFeature: 'Wat wil je als eerste ontdekken?',
	ExploreOptions: 'Kies een of meer opties om te verkennen...',
	Export: 'Exporteren',
	ExportAppointments: 'Exportafspraken',
	ExportClaims: 'Claims exporteren',
	ExportClaimsFilename: 'Claims {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription:
		'Er is een fout opgetreden waardoor uw bestand niet kon worden gedownload.',
	ExportClientsDownloadFailureSnackbarTitle: 'Downloaden mislukt',
	ExportClientsFailureSnackbarDescription:
		'Er is een fout opgetreden waardoor uw bestand niet succesvol kon worden geëxporteerd.',
	ExportClientsFailureSnackbarTitle: 'Exporteren mislukt',
	ExportClientsModalDescription: `Dit data-exportproces kan enkele minuten duren, afhankelijk van de hoeveelheid data die wordt geëxporteerd. U ontvangt een e-mailmelding met een link zodra het klaar is om te downloaden.

 Wilt u doorgaan met het exporteren van de klantgegevens?`,
	ExportClientsModalTitle: 'Exporteer klantgegevens',
	ExportCms1500: 'CMS1500 exporteren',
	ExportContactFailedNotificationSubject: 'Uw gegevensexport is mislukt',
	ExportFailed: 'Exporteren mislukt',
	ExportGuide: 'Exportgids',
	ExportInvoiceFileName: 'Transacties {fromDate}-{toDate}.csv',
	ExportPayments: 'Exportbetalingen',
	ExportPaymentsFilename: 'Betalingen {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Uw document is klaar om te downloaden.',
	ExportPrintWaitMessage: 'Uw document wordt voorbereid. Even geduld...',
	ExportTextOnly: 'Alleen tekst exporteren',
	ExportTransactions: 'Exporttransacties',
	Exporting: 'Exporteren',
	ExportingData: 'Gegevens exporteren',
	ExtendedFamilyMember: 'Uitgebreide familielid',
	External: 'Extern',
	ExternalEventInfoBanner: 'Deze afspraak is afkomstig van een gesynchroniseerde agenda en kan items missen.',
	ExtraLarge: 'Extra groot',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Mislukt',
	FailedToJoinTheMeeting: 'Deelnemen aan de vergadering is mislukt.',
	FallbackPageDescription: `Het lijkt erop dat deze pagina niet bestaat. Misschien moet je {refreshButton} deze pagina vernieuwen om de nieuwste wijzigingen te bekijken.
Anders neem dan contact op met de Carepatron-ondersteuning.`,
	FallbackPageDescriptionUpdateButton: 'vernieuwen',
	FallbackPageTitle: 'Oeps...',
	FamilyPlanningService: 'Gezinsplanningsdienst',
	FashionDesigner: 'Modeontwerper',
	FastTrackInvoicingAndBilling: 'Versnel uw facturering en facturatie',
	Father: 'Vader',
	FatherInLaw: 'Schoonvader',
	Favorite: 'Favoriet',
	FeatureBannerCalendarTile1ActionLabel: 'Online boeken • 2 min',
	FeatureBannerCalendarTile1Description: 'Stuur eenvoudig een e-mail, sms of voeg beschikbaarheid toe aan uw website',
	FeatureBannerCalendarTile1Title: 'Geef uw klanten de mogelijkheid om online te boeken',
	FeatureBannerCalendarTile2ActionLabel: 'Herinneringen automatiseren • 2 min.',
	FeatureBannerCalendarTile2Description: 'Verhoog de aanwezigheid van cliënten met automatische herinneringen',
	FeatureBannerCalendarTile2Title: 'Verminder no-shows',
	FeatureBannerCalendarTile3Title: 'Planning en workflow',
	FeatureBannerCalendarTitle: 'Maak het plannen eenvoudig',
	FeatureBannerCallsTile1ActionLabel: 'Telehealth-gesprek starten',
	FeatureBannerCallsTile1Description: 'Clienttoegang met slechts een link. Geen logins, wachtwoorden of gedoe',
	FeatureBannerCallsTile1Title: 'Start een videogesprek vanaf elke locatie',
	FeatureBannerCallsTile2ActionLabel: 'Apps verbinden • 4 min.',
	FeatureBannerCallsTile2Description: 'Sluit naadloos andere favoriete aanbieders van telezorg aan',
	FeatureBannerCallsTile2Title: 'Verbind uw telezorg-apps',
	FeatureBannerCallsTile3Title: 'Oproepen',
	FeatureBannerCallsTitle: 'Maak contact met klanten - overal en altijd',
	FeatureBannerClientsTile1ActionLabel: 'Nu importeren • 2 min.',
	FeatureBannerClientsTile1Description:
		'Ga snel aan de slag met onze geautomatiseerde tool voor het importeren van klanten',
	FeatureBannerClientsTile1Title: 'Hebt u veel klanten?',
	FeatureBannerClientsTile2ActionLabel: 'Inname aanpassen • 2 min',
	FeatureBannerClientsTile2Description: 'Verwijder het papierwerk bij de intake en verbeter de klantervaring',
	FeatureBannerClientsTile2Title: 'Ga papierloos',
	FeatureBannerClientsTile3Title: 'Klantenportaal',
	FeatureBannerClientsTitle: 'Het begint allemaal bij uw klanten',
	FeatureBannerHeader: 'Door de gemeenschap, voor de gemeenschap!',
	FeatureBannerInvoicesTile1ActionLabel: 'Betalingen automatiseren • 2 min.',
	FeatureBannerInvoicesTile1Description: 'Vermijd ongemakkelijke gesprekken met geautomatiseerde betalingen',
	FeatureBannerInvoicesTile1Title: 'Krijg 2x sneller betaald',
	FeatureBannerInvoicesTile2ActionLabel: 'Cashflow bijhouden • 2 min',
	FeatureBannerInvoicesTile2Description: 'Verminder onbetaalde facturen en houd uw inkomsten in de gaten',
	FeatureBannerInvoicesTile2Title: 'Volg uw inkomsten pijnloos',
	FeatureBannerInvoicesTile3Title: 'Facturering en betalingen',
	FeatureBannerInvoicesTitle: 'Eén ding minder om je zorgen over te maken',
	FeatureBannerSubheader:
		'Carepatron-sjablonen gemaakt door ons team en onze community. Probeer nieuwe bronnen of deel uw eigen!',
	FeatureBannerTeamTile1ActionLabel: 'Nu uitnodigen',
	FeatureBannerTeamTile1Description: 'Nodig teamleden uit voor uw account en maak samenwerking eenvoudig',
	FeatureBannerTeamTile1Title: 'Breng je team samen',
	FeatureBannerTeamTile2ActionLabel: 'Beschikbaarheid instellen • 2 min',
	FeatureBannerTeamTile2Description: 'Beheer de beschikbaarheid van uw teams om dubbele boekingen te voorkomen',
	FeatureBannerTeamTile2Title: 'Stel uw beschikbaarheid in',
	FeatureBannerTeamTile3ActionLabel: 'Machtigingen instellen • 2 min.',
	FeatureBannerTeamTile3Description: 'Beheer de toegang tot gevoelige gegevens en hulpmiddelen voor naleving',
	FeatureBannerTeamTile3Title: 'Pas machtigingen en toegang aan',
	FeatureBannerTeamTitle: 'Niets groots wordt alleen bereikt',
	FeatureBannerTemplatesTile1ActionLabel: 'Bibliotheek verkennen • 2 min',
	FeatureBannerTemplatesTile1Description: 'Kies uit een geweldige bibliotheek met aanpasbare bronnen ',
	FeatureBannerTemplatesTile1Title: 'Verminder uw werklast',
	FeatureBannerTemplatesTile2ActionLabel: 'Nu verzenden • 2 min.',
	FeatureBannerTemplatesTile2Description: 'Stuur prachtige sjablonen naar klanten om ze te laten voltooien',
	FeatureBannerTemplatesTile2Title: 'Maak documentatie leuk',
	FeatureBannerTemplatesTile3Title: 'Sjablonen',
	FeatureBannerTemplatesTitle: 'Sjablonen voor absoluut alles',
	FeatureLimitBannerDescription:
		'Upgrade nu om {featureName} te blijven creëren en beheren zonder onderbreking en haal het meeste uit Carepatron!',
	FeatureLimitBannerTitle: 'Je bent {percentage}% op weg naar je {featureName} limiet',
	FeatureRequiresUpgrade: 'Deze functie vereist een upgrade',
	Fee: 'Tarief',
	Female: 'Vrouwelijk',
	FieldLabelTooltip: '{isHidden, select, true {Toon} other {Verberg}} veldlabel',
	FieldName: 'Veldnaam',
	FieldOptionsFirstPart: 'Eerste woord',
	FieldOptionsMiddlePart: 'Middelste woorden',
	FieldOptionsSecondPart: 'Laatste woord',
	FieldOptionsWholeField: 'Hele veld',
	FieldType: 'Veldtype',
	Fields: 'Velden',
	File: 'Bestand',
	FileDownloaded: '<strong>{fileName}</strong> gedownload',
	FileInvalidType: 'Bestand niet ondersteund.',
	FileNotFound: 'Bestand niet gevonden',
	FileNotFoundDescription: 'Het bestand dat u zoekt is niet beschikbaar of is verwijderd',
	FileTags: 'Bestandslabels',
	FileTagsHelper: 'Tags worden op alle bestanden toegepast',
	FileTooLarge: 'Bestand te groot.',
	FileTooSmall: 'Bestand te klein.',
	FileUploadComplete: 'Compleet',
	FileUploadFailed: 'Mislukt',
	FileUploadInProgress: 'Laden',
	FileUploadedNotificationSubject: '{actorProfileName} heeft een bestand geüpload',
	Files: 'Bestanden',
	FillOut: 'Vul in',
	Filter: 'Filter',
	FilterBy: 'Filteren op',
	FilterByAmount: 'Filteren op bedrag',
	FilterByClient: 'Filteren op klant',
	FilterByLocation: 'Filteren op locatie',
	FilterByService: 'Filteren op service',
	FilterByStatus: 'Filteren op status',
	FilterByTags: 'Filteren op tags',
	FilterByTeam: 'Filteren op team',
	Filters: 'Filteren',
	FiltersAppliedToView: 'Filters toegepast op weergave',
	FinalAppointment: 'Definitieve benoeming',
	FinalizeImport: 'Finaliseer import',
	FinancialAnalyst: 'Financieel analist',
	Finish: 'Finish',
	Firefighter: 'Brandweerman',
	FirstName: 'Voornaam',
	FirstNameLastInitial: 'Voornaam, eerste letter van de achternaam',
	FirstPerson: '1e persoon',
	FolderName: 'Mapnaam',
	Folders: 'Mappen',
	FontFamily: 'Lettertypefamilie',
	ForClients: 'Voor klanten',
	ForClientsDetails: 'Ik ontvang zorg of gezondheidsgerelateerde diensten',
	ForPractitioners: 'Voor beoefenaars',
	ForPractitionersDetails: 'Beheer en laat uw praktijk groeien',
	ForgotPasswordConfirmAccessCode: 'Bevestigingscode',
	ForgotPasswordConfirmNewPassword: 'Nieuw wachtwoord',
	ForgotPasswordConfirmPageDescription:
		'Voer uw e-mailadres, een nieuw wachtwoord en de bevestigingscode in die we u zojuist hebben gestuurd.',
	ForgotPasswordConfirmPageTitle: 'Wachtwoord opnieuw instellen',
	ForgotPasswordPageButton: 'Resetlink verzenden',
	ForgotPasswordPageDescription:
		'Voer uw e-mailadres in, dan sturen wij u een link om uw wachtwoord opnieuw in te stellen.',
	ForgotPasswordPageTitle: 'Wachtwoord vergeten',
	ForgotPasswordSuccessPageDescription: 'Controleer uw inbox voor de resetlink.',
	ForgotPasswordSuccessPageTitle: 'Resetlink verzonden!',
	Form: 'Formulier',
	FormAnswersSentToEmailNotification: 'Wij hebben een kopie van uw antwoorden naar',
	FormBlocks: 'Vorm blokken',
	FormFieldAddOption: 'Optie toevoegen',
	FormFieldAddOtherOption: 'Voeg "andere" toe',
	FormFieldOptionPlaceholder: 'Optie {index}',
	FormStructures: 'Vormstructuren',
	Format: 'Formaat',
	FormatLinkButtonColor: 'Knopkleur',
	Forms: 'Formulieren',
	FormsAndAgreementsValidationMessage:
		'Alle formulieren en overeenkomsten moeten worden ingevuld om door te kunnen gaan met het intakeproces.',
	FormsCategoryDescription: 'Voor het verzamelen en ordenen van patiëntgegevens',
	Frankfurt: 'Frankfurt',
	Free: 'Vrij',
	FreePlanInclusionFive: 'Geautomatiseerde facturering ',
	FreePlanInclusionFour: 'Klantenportaal',
	FreePlanInclusionHeader: 'Aan de slag met',
	FreePlanInclusionOne: 'Onbeperkt aantal klanten',
	FreePlanInclusionSix: 'Live-ondersteuning',
	FreePlanInclusionThree: '1 GB opslagruimte',
	FreePlanInclusionTwo: 'Telezorg',
	FreeSubscriptionPlanSubtitle: 'Gratis voor iedereen',
	FreeSubscriptionPlanTitle: 'Vrij',
	Friday: 'Vrijdag',
	From: 'Van',
	FullName: 'Volledige naam',
	FunctionalMedicineOrNaturopath: 'Functionele geneeskunde of natuurgeneeskundige',
	FuturePaymentsAuthoriseProvider: 'Sta {provider} toe om de opgeslagen betaling in de toekomst te gebruiken',
	FuturePaymentsSavePaymentMethod: 'Bewaar {paymentMethod} voor toekomstige betalingen',
	GST: 'BTW',
	Gender: 'Geslacht',
	GeneralAvailability: 'Algemene beschikbaarheid',
	GeneralAvailabilityDescription:
		'Stel in wanneer u regelmatig beschikbaar bent. Klanten kunnen uw diensten alleen boeken tijdens beschikbare uren.',
	GeneralAvailabilityDescription2: `Maak schema's op basis van uw beschikbaarheid en de gewenste service-aanbiedingen op specifieke tijden om de beschikbaarheid van uw online boekingen te bepalen.`,
	GeneralAvailabilityInfo: 'Uw beschikbare uren bepalen de beschikbaarheid van uw onlineboeking',
	GeneralAvailabilityInfo2:
		'Diensten die groepsevenementen aanbieden, moeten een nieuw schema hanteren, zodat klanten minder uren online kunnen boeken.',
	GeneralHoursPlural: '{count} {count, plural, one {uur} other {uren}}',
	GeneralPractitioner: 'Huisarts',
	GeneralPractitioners: 'Huisartsen',
	GeneralServiceAvailabilityInfo: 'Dit schema zal het gedrag van toegewezen teamleden overschrijven',
	Generate: 'Genereren',
	GenerateBillingItemsBannerContent:
		'Voor terugkerende afspraken worden er niet automatisch factuuritems aangemaakt.',
	GenerateItems: 'Items genereren',
	GenerateNote: 'Notitie genereren',
	GenerateNoteConfirmationModalDescription:
		'Wat wilt u doen? Een nieuwe gegenereerde notitie maken, toevoegen aan de bestaande notitie of de inhoud ervan vervangen?',
	GenerateNoteFor: 'Notitie genereren voor',
	GeneratingContent: 'Inhoud genereren...',
	GeneratingNote: 'Uw notitie wordt gegenereerd...',
	GeneratingTranscript: 'Transcript genereren',
	GeneratingTranscriptDescription: 'Het kan enkele minuten duren voordat dit verwerkt is',
	GeneratingYourTranscript: 'Uw transcript genereren',
	GenericErrorDescription: '{module} kon niet geladen worden. Probeer het later opnieuw.',
	GenericErrorTitle: 'Er is een onverwachte fout opgetreden',
	GenericFailureSnackbar: 'Sorry, er is iets onverwachts gebeurd. Vernieuw de pagina en probeer het opnieuw.',
	GenericSavedSuccessSnackbar: 'Succes! Wijzigingen opgeslagen',
	GeneticCounselor: 'Genetisch adviseur',
	Gerontologist: 'Gerontoloog',
	Get50PercentOff: 'Krijg 50% korting!',
	GetHelp: 'Krijg hulp',
	GetStarted: 'Aan de slag',
	GettingStartedAppointmentTypes: 'Afspraaktypen maken',
	GettingStartedAppointmentTypesDescription:
		'Stroomlijn uw planning en facturering door uw diensten, prijzen en factureringscodes aan te passen',
	GettingStartedAppointmentTypesTitle: 'Schema ',
	GettingStartedClients: 'Voeg uw klanten toe',
	GettingStartedClientsDescription:
		'Zorg dat u klaar bent voor toekomstige afspraken, aantekeningen en betalingen met uw klanten',
	GettingStartedClientsTitle: 'Het begint allemaal met klanten',
	GettingStartedCreateClient: 'Klant aanmaken',
	GettingStartedImportClients: 'Klanten importeren',
	GettingStartedInvoices: 'Factureer als een professional',
	GettingStartedInvoicesDescription: `Het is eenvoudig om professionele facturen te maken.
 Voeg uw logo, locatie en betalingsvoorwaarden toe`,
	GettingStartedInvoicesTitle: 'Zet je beste beentje voor',
	GettingStartedMobileApp: 'Download de mobiele app',
	GettingStartedMobileAppDescription:
		'U kunt Carepatron downloaden op uw iOS-, Android- of desktopapparaat voor eenvoudige toegang onderweg',
	GettingStartedMobileAppTitle: 'Werk overal',
	GettingStartedNavItem: 'Aan de slag',
	GettingStartedPageTitle: 'Aan de slag met Carepatron',
	GettingStartedPayments: 'Accepteer online betalingen',
	GettingStartedPaymentsDescription: `Krijg sneller betaald door uw klanten de mogelijkheid te bieden online te betalen.
 Bekijk al uw facturen en betalingen op één plek`,
	GettingStartedPaymentsTitle: 'Maak betalingen een fluitje van een cent',
	GettingStartedSaveBranding: 'Branding opslaan',
	GettingStartedSyncCalendars: `Synchroniseer andere agenda's`,
	GettingStartedSyncCalendarsDescription:
		'Carepatron controleert uw agenda op conflicten, zodat afspraken alleen worden gepland wanneer u beschikbaar bent',
	GettingStartedSyncCalendarsTitle: 'Blijf altijd op de hoogte',
	GettingStartedVideo: 'Bekijk een introductievideo',
	GettingStartedVideoDescription:
		'De eerste alles-in-één werkplekken voor de gezondheidszorg voor kleine teams en hun cliënten',
	GettingStartedVideoTitle: 'Welkom bij Carepatron',
	GetttingStartedGetMobileDownload: 'Download de app',
	GetttingStartedGetMobileNoDownload:
		'Niet compatibel met deze browser. Als u een iPhone of iPad gebruikt, open deze pagina dan in Safari. Probeer het anders in Chrome te openen.',
	Glossary: '<h1>Glossarium</h1>',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail staat slechts 500 berichten toe die per dag vanaf uw account kunnen worden verzonden. Sommige berichten kunnen mislukken. Wilt u doorgaan?',
	GoToAppointment: 'Ga naar afspraak',
	GoToApps: 'Ga naar apps',
	GoToAvailability: 'Ga naar beschikbaarheid',
	GoToClientList: 'Ga naar klantenlijst',
	GoToClientRecord: 'Ga naar cliëntrecord',
	GoToClientSettings: 'Ga nu naar de clientinstellingen',
	GoToInvoiceTemplates: 'Ga naar factuursjablonen',
	GoToNotificationSettings: 'Ga naar de meldingsinstellingen',
	GoToPaymentSettings: 'Ga naar betalingsinstellingen',
	Google: 'Google',
	GoogleCalendar: 'Google Agenda',
	GoogleColor: 'Google agenda kleur',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Tag Manager-container-ID',
	GotIt: 'Begrepen!',
	Goto: 'Ga naar',
	Granddaughter: 'Kleindochter',
	Grandfather: 'Grootvader',
	Grandmother: 'Grootmoeder',
	Grandparent: 'Grootouder',
	Grandson: 'Kleinzoon',
	GrantPortalAccess: 'Toegang tot portaal verlenen',
	GraphicDesigner: 'Grafisch ontwerper',
	Grid: 'Rooster',
	GridView: 'Rasterweergave',
	Group: 'Groep',
	GroupBy: 'Groeperen op',
	GroupEvent: 'Groepsevenement',
	GroupEventHelper: 'Stel een deelnemerslimiet in voor de service',
	GroupFilterLabel: 'Alle {groep}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Groeps-ID',
	GroupInputFieldsFormPrimaryText: 'Groep invoervelden',
	GroupInputFieldsFormSecondaryText: 'Kies of voeg aangepaste velden toe',
	GuideTo: 'Gids voor {value}',
	GuideToImproveVideoQuality: 'Handleiding voor het verbeteren van de videokwaliteit',
	GuideToManagingPayers: 'Betalers beheren',
	GuideToSubscriptionsBilling: 'Handleiding voor facturering van abonnementen',
	GuideToTroubleshooting: 'Handleiding voor probleemoplossing',
	Guidelines: 'Richtlijnen',
	GuidelinesCategoryDescription: 'Voor het begeleiden van klinische besluitvorming',
	HST: 'HST',
	HairStylist: 'Haarstylist',
	HaveBeenWaiting: 'Je hebt lang gewacht',
	HeHim: 'Hij/Hem',
	HeaderAccountSettings: 'Profiel',
	HeaderCalendar: 'Kalender',
	HeaderCalls: 'Oproepen',
	HeaderClientAppAccountSettings: 'Accountinstellingen',
	HeaderClientAppCalls: 'Oproepen',
	HeaderClientAppMyDocumentation: 'Documentatie',
	HeaderClientAppMyRelationships: 'Mijn relaties',
	HeaderClients: 'Klanten',
	HeaderHelp: 'Hulp',
	HeaderMoreOptions: 'Meer opties',
	HeaderStaff: 'Personeel',
	HealthCoach: 'Gezondheidscoach',
	HealthCoaches: 'Gezondheidscoaches',
	HealthEducator: 'Gezondheidsvoorlichter',
	HealthInformationTechnician: 'Gezondheidsinformatietechnicus',
	HealthPolicyExpert: 'Deskundige gezondheidsbeleid',
	HealthServicesAdministrator: 'Beheerder van gezondheidsdiensten',
	HelpArticles: 'Help-artikelen',
	HiddenColumns: 'Verborgen kolommen',
	HiddenFields: 'Verborgen velden',
	HiddenSections: 'Verborgen secties',
	HiddenSectionsAndFields: 'Verborgen secties/velden',
	HideColumn: 'Kolom verbergen',
	HideColumnButton: 'Verberg kolom {value} knop',
	HideDetails: 'Details verbergen',
	HideField: 'Verberg veld',
	HideFullAddress: 'Verbergen',
	HideMenu: 'Verberg menu',
	HideMergeSummarySidebar: 'Verberg samenvatting van samenvoegen',
	HideSection: 'Sectie verbergen',
	HideYourView: 'Verberg uw zicht',
	Highlight: 'Markeer kleur',
	Highlighter: 'Markeerstift',
	History: 'Geschiedenis',
	HistoryItemFooter: '{actors, select, undefined {{date} om {time}} other {Door {actors} • {date} om {time}}}',
	HistorySidePanelEmptyState: 'Geen geschiedenisrecords gevonden',
	HistoryTitle: 'Activiteitenlog',
	HolisticHealthPractitioner: 'Holistisch gezondheidsbeoefenaar',
	HomeCaregiver: 'Thuiszorgverlener',
	HomeHealthAide: 'Thuiszorgassistent',
	HomelessShelter: 'Daklozenopvang',
	HourAbbreviation: '{count} {count, plural, one {uur} other {uur}}',
	Hourly: 'Uurlijks',
	HoursPlural: '{age, plural, one {# uur} other {# uren}}',
	HowCanWeImprove: 'Hoe kunnen we dit verbeteren?',
	HowCanWeImproveResponse: 'Hoe kunnen we deze reactie verbeteren?',
	HowDidWeDo: 'Hoe hebben we het gedaan?',
	HowDoesReferralWork: 'Gids voor het verwijzingsprogramma',
	HowToUseAiSummarise: 'Hoe AI Summarize te gebruiken',
	HumanResourcesManager: 'Manager personeelszaken',
	Husband: 'Echtgenoot',
	Hypnotherapist: 'Hypnotherapeut',
	IVA: 'BTW',
	IgnoreNotification: 'Melding negeren',
	IgnoreOnce: 'Negeer een keer',
	IgnoreSender: 'Negeer afzender',
	IgnoreSenderDescription: `Toekomstige conversaties van deze afzender worden automatisch verplaatst naar 'Overige'. Weet u zeker dat u deze afzenders wilt negeren?`,
	IgnoreSenders: 'Negeer afzenders',
	IgnoreSendersSuccess: 'Genegeerd e-mailadres <mark>{addresses}</mark>',
	Ignored: 'Genegeerd',
	Image: 'Afbeelding',
	Import: 'Importeren',
	ImportActivity: 'Importactiviteit',
	ImportClientSuccessSnackbarDescription: 'Uw bestand is succesvol geïmporteerd',
	ImportClientSuccessSnackbarTitle: 'Importeren gelukt!',
	ImportClients: 'Klanten importeren',
	ImportClientsFailureSnackbarDescription:
		'Er is een fout opgetreden waardoor uw bestand niet succesvol kon worden geïmporteerd.',
	ImportClientsFailureSnackbarTitle: 'Importeren mislukt!',
	ImportClientsGuide: 'Handleiding voor het importeren van klanten',
	ImportClientsInProgressSnackbarDescription: 'Dit duurt maximaal een minuut.',
	ImportClientsInProgressSnackbarTitle: 'Importeren van {fileName}',
	ImportClientsModalDescription:
		'Kies waar uw gegevens vandaan komen: een bestand op uw apparaat, een service van derden of een ander softwareplatform.',
	ImportClientsModalFileUploadHelperText: 'Ondersteunt {fileTypes}. Groottelimiet {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Handleiding voor het importeren van klantgegevens',
	ImportClientsModalStep1Label: 'Gegevensbron kiezen',
	ImportClientsModalStep2Label: 'Bestand uploaden',
	ImportClientsModalStep3Label: 'Velden bekijken',
	ImportClientsModalTitle: 'Uw klantgegevens importeren',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {klant} other {klanten}} klaar voor import',
	ImportContactFailedNotificationSubject: 'Uw gegevensimport is mislukt',
	ImportDataSourceSelectorLabel: 'Gegevensbron importeren uit',
	ImportDataSourceSelectorPlaceholder: 'Zoek of kies importgegevensbron',
	ImportExportButton: 'Importeren/Exporteren',
	ImportFailed: 'Importeren mislukt',
	ImportFromAnotherPlatformTileDescription: 'Download een export van uw klantbestanden en upload ze hier.',
	ImportFromAnotherPlatformTileLabel: 'Importeren van een ander platform',
	ImportGuide: 'Importgids',
	ImportInProgress: 'Import bezig',
	ImportProcessing: 'Import verwerking...',
	ImportSpreadsheetDescription:
		'U kunt uw bestaande cliëntenlijst importeren in Carepatron door een spreadsheetbestand met tabelgegevens te uploaden, zoals .CSV, .XLS of .XLSX',
	ImportSpreadsheetTitle: 'Importeer uw spreadsheetbestand',
	ImportTemplates: 'Importeer sjablonen',
	Importing: 'Importeren',
	ImportingCalendarProductEvents: 'Het importeren van {product} gebeurtenissen',
	ImportingData: 'Gegevens importeren',
	ImportingSpreadsheetDescription: 'Dit zou slechts een minuut in beslag moeten nemen',
	ImportingSpreadsheetTitle: 'Uw spreadsheet importeren',
	ImportsInProgress: 'Importen zijn in uitvoering',
	InPersonMeeting: 'Persoonlijke ontmoeting',
	InProgress: 'In uitvoering',
	InTransit: 'Onderweg',
	InTransitTooltip:
		'In Transit-saldo omvat alle betaalde factuurbetalingen van Stripe naar uw bankrekening. Deze fondsen duren doorgaans 3-5 dagen om te verwerken.',
	Inactive: 'Inactief',
	InboundOrOutboundCalls: 'Inkomende of uitgaande gesprekken',
	Inbox: 'Postvak IN',
	InboxAccessRestricted: 'Toegang beperkt. Neem contact op met de inboxeigenaar voor toestemmingen.',
	InboxAccountAlreadyConnected: 'Het kanaal dat u probeerde te verbinden, is al verbonden met Carepatron',
	InboxAddAttachments: 'Bijlagen toevoegen',
	InboxAreYouSureDeleteMessage: 'Weet u zeker dat u dit bericht wilt verwijderen?',
	InboxBulkCloseSuccess: '{count, plural, one {Gesloten # gesprek} other {Gesloten # gesprekken}}',
	InboxBulkComposeModalTitle: 'Bulkbericht opstellen',
	InboxBulkDeleteSuccess:
		'{count, plural, one {# gesprek succesvol verwijderd} other {# gesprekken succesvol verwijderd}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Geslaagde # gesprek gemarkeerd als gelezen} other {Geslaagde # gesprekken gemarkeerd als gelezen}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Gesprek # met succes heropend} other {Gesprekken # met succes heropend}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Geslaagd gemarkeerd # gesprek als ongelezen} other {Geslaagd gemarkeerd # gesprekken als ongelezen}}',
	InboxChatCreateGroup: 'Maak groep',
	InboxChatDeleteGroupModalDescription:
		'Weet u zeker dat u deze groep wilt verwijderen? Alle berichten en bijlagen worden verwijderd.',
	InboxChatDeleteGroupModalTitle: 'Groep verwijderen',
	InboxChatDiscardDraft: 'Concept verwijderen',
	InboxChatDragDropText: 'Sleep bestanden hier om te uploaden',
	InboxChatGroupConversation: 'Groepsgesprek',
	InboxChatGroupCreateModalDescription:
		'Start een nieuwe groep om berichten te verzenden en samen te werken met uw team, klanten of community.',
	InboxChatGroupCreateModalTitle: 'Groep maken',
	InboxChatGroupMembers: 'Groepsleden',
	InboxChatGroupModalGroupNameFieldLabel: 'Groepsnaam',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'B.v. klantenservice, beheerder',
	InboxChatGroupModalGroupNameFieldRequired: 'Dit veld is verplicht',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimaal één lid vereist',
	InboxChatGroupModalMembersFieldLabel: 'Kies groepsleden',
	InboxChatGroupModalMembersFieldPlaceholder: 'Kies leden',
	InboxChatGroupUpdateModalTitle: 'Groep beheren',
	InboxChatLeaveGroup: 'Verlaat groep',
	InboxChatLeaveGroupModalDescription:
		'Weet u zeker dat u deze groep wilt verlaten? U ontvangt dan geen berichten of updates meer.',
	InboxChatLeaveGroupModalTitle: 'Groep verlaten',
	InboxChatLeftGroupMessage: 'Groepsbericht verlaten',
	InboxChatManageGroup: 'Groep beheren',
	InboxChatSearchParticipants: 'Kies ontvangers',
	InboxCloseConversationSuccess: 'Gesprek succesvol afgesloten',
	InboxCompose: 'Componeren',
	InboxComposeBulk: 'Bulkbericht',
	InboxComposeCarepatronChat: 'Boodschapper',
	InboxComposeChat: 'Chat samenstellen',
	InboxComposeDisabledNoConnection: 'Verbind een e-mailaccount om berichten te versturen',
	InboxComposeDisabledNoPermissionTooltip: 'U hebt geen toestemming om berichten vanuit deze inbox te versturen',
	InboxComposeEmail: 'E-mail opstellen',
	InboxComposeMessageFrom: 'Van',
	InboxComposeMessageRecipientBcc: 'Bcc',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'Naar',
	InboxComposeMessageSubject: 'Onderwerp:',
	InboxConnectAccountButton: 'Verbind uw e-mail',
	InboxConnectedDescription: 'Er zijn geen berichten in uw inbox',
	InboxConnectedHeading: 'Zodra u begint met het uitwisselen van communicatie, verschijnen uw gesprekken hier.',
	InboxConnectedHeadingClientView: 'Stroomlijn uw klantcommunicatie',
	InboxCreateFirstInboxButton: 'Maak je eerste inbox aan',
	InboxCreationSuccess: 'Postvak IN succesvol aangemaakt',
	InboxDeleteAttachment: 'Bijlage verwijderen',
	InboxDeleteConversationSuccess: 'Gesprek succesvol verwijderd',
	InboxDeleteMessage: 'Bericht verwijderen?',
	InboxDirectMessage: 'Direct message',
	InboxEditDraft: 'Concept bewerken',
	InboxEmailComposeReplyEmail: 'Antwoord opstellen',
	InboxEmailDraft: 'Voorlopige versie',
	InboxEmailNotFound: 'E-mail niet gevonden',
	InboxEmailSubjectFieldInformation: 'Als u de onderwerpregel wijzigt, wordt er een nieuwe e-mailthread gemaakt.',
	InboxEmptyArchiveDescription: 'Er zijn geen gearchiveerde gesprekken gevonden',
	InboxEmptyBinDescription: 'Er zijn geen verwijderde gesprekken gevonden',
	InboxEmptyBinHeading: 'Alles is veilig, hier is niets te zien',
	InboxEmptyBinSuccess: 'Gesprekken succesvol verwijderd',
	InboxEmptyCongratsHeading: 'Goed werk! Leun achterover en ontspan tot het volgende gesprek',
	InboxEmptyDraftDescription: 'Er zijn geen conceptgesprekken gevonden',
	InboxEmptyDraftHeading: 'Alles is veilig, hier is niets te zien',
	InboxEmptyOtherDescription: 'Er zijn geen andere gesprekken gevonden',
	InboxEmptyScheduledHeading: 'Alles in orde, geen gesprekken gepland voor verzending',
	InboxEmptySentDescription: 'Er zijn geen verzonden conversaties gevonden',
	InboxForward: 'Vooruit',
	InboxGroupClientsLabel: 'Alle klanten',
	InboxGroupClientsOverviewLabel: 'Klanten',
	InboxGroupClientsSelectedItemPrefix: 'Cliënt',
	InboxGroupStaffsLabel: 'Het hele team',
	InboxGroupStaffsOverviewLabel: 'Team',
	InboxGroupStaffsSelectedItemPrefix: 'Team',
	InboxGroupStatusLabel: 'Alle statussen',
	InboxGroupStatusOverviewLabel: 'Verzenden naar een status',
	InboxGroupStatusSelectedItemPrefix: 'Staat',
	InboxGroupTagsLabel: 'Alle tags',
	InboxGroupTagsOverviewLabel: 'Verzenden naar een tag',
	InboxGroupTagsSelectedItemPrefix: 'Label',
	InboxHideQuotedText: 'Verberg geciteerde tekst',
	InboxIgnoreConversationSuccess: 'Gesprek succesvol genegeerd',
	InboxMessageAllLabelRecipientsCount: 'Alle {label} Ontvangers ({count})',
	InboxMessageBodyPlaceholder: 'Voeg uw bericht toe',
	InboxMessageDeleted: 'Bericht verwijderd',
	InboxMessageMarkedAsRead: 'Bericht gemarkeerd als gelezen',
	InboxMessageMarkedAsUnread: 'Bericht gemarkeerd als ongelezen',
	InboxMessageSentViaChat: '**Verstuurd via chat**  • {time} door {name}',
	InboxMessageShowMoreRecipients: '+{count} meer',
	InboxMessageWasDeleted: 'Dit bericht is verwijderd',
	InboxNoConnectionDescription: 'Verbind uw e-mailaccount of maak inboxen met meerdere e-mails',
	InboxNoConnectionHeading: 'Integreer uw klantcommunicatie',
	InboxNoDirectMessage: 'Geen recente berichten',
	InboxRecentConversations: 'Recent',
	InboxReopenConversationSuccess: 'Gesprek succesvol heropend',
	InboxReply: 'Antwoord',
	InboxReplyAll: 'Antwoord allemaal',
	InboxRestoreConversationSuccess: 'Gesprek succesvol hersteld',
	InboxScheduleSendCancelSendSuccess: 'Geplande verzending geannuleerd en bericht teruggezet naar concept',
	InboxScheduleSendMessageSuccessDescription: 'Gestuurd voor {date}',
	InboxScheduleSendMessageSuccessTitle: 'Verzenden plannen',
	InboxSearchForConversations: 'Zoek naar "{query}"',
	InboxSendMessageSuccess: 'Gesprek succesvol verzonden',
	InboxSettings: 'Postvak IN-instellingen',
	InboxSettingsAppsDesc:
		'Beheer verbonden apps voor deze gedeelde inbox: voeg indien nodig verbindingen toe of verwijder deze.',
	InboxSettingsAppsNewConnectedApp: 'Nieuwe verbonden app',
	InboxSettingsAppsTitle: 'Verbonden apps',
	InboxSettingsDeleteAccountFailed: 'Het is niet gelukt om het inbox-account te verwijderen',
	InboxSettingsDeleteAccountSuccess: 'Inbox-account succesvol verwijderd',
	InboxSettingsDeleteAccountWarning:
		'Het verwijderen van {email} zal het ontkoppelen van de inbox {inboxName} en zal ervoor zorgen dat berichten niet meer worden gesynchroniseerd.',
	InboxSettingsDeleteInboxFailed: 'Postvak IN kon niet worden verwijderd',
	InboxSettingsDeleteInboxSuccess: 'Postvak IN succesvol verwijderd',
	InboxSettingsDeleteInboxWarning:
		'Het verwijderen van {inboxName} zal alle verbonden kanalen ontkoppelen en alle berichten die aan deze inbox zijn gekoppeld verwijderen. 		Deze actie is permanent en kan niet ongedaan worden gemaakt.',
	InboxSettingsDetailsDesc: 'Communicatie-inbox waarmee uw team efficiënt berichten van klanten kan beheren.',
	InboxSettingsDetailsTitle: 'Postvak IN-gegevens',
	InboxSettingsEmailSignatureLabel: 'Standaard e-mailhandtekening',
	InboxSettingsReplyFormatDesc:
		'Stel uw standaardantwoordadres en e-mailhandtekening zo in dat deze consistent worden weergegeven, ongeacht wie de e-mail verzendt.',
	InboxSettingsReplyFormatTitle: 'Antwoordformaat',
	InboxSettingsSendFromLabel: 'Stel een standaardantwoord in van ',
	InboxSettingsStaffDesc: 'Beheer de toegang van teamleden tot deze gedeelde inbox voor naadloze samenwerking.',
	InboxSettingsStaffTitle: 'Teamleden toewijzen',
	InboxSettingsUpdateInboxDetailsFailed: 'Het is niet gelukt om de inboxgegevens bij te werken',
	InboxSettingsUpdateInboxDetailsSuccess: 'Inboxgegevens succesvol bijgewerkt',
	InboxSettingsUpdateInboxStaffsFailed: 'Het is niet gelukt om de inbox van teamleden bij te werken',
	InboxSettingsUpdateInboxStaffsSuccess: 'Inbox-teamleden succesvol bijgewerkt',
	InboxSettingsUpdateReplyFormatFailed: 'Het is niet gelukt om het antwoordformaat bij te werken',
	InboxSettingsUpdateReplyFormatSuccess: 'Antwoordformaat succesvol bijgewerkt',
	InboxShowQuotedText: 'Toon geciteerde tekst',
	InboxStaffRoleAdminDescription: 'Bekijk, beantwoord en beheer inboxen',
	InboxStaffRoleResponderDescription: 'Bekijk en reageer',
	InboxStaffRoleViewerDescription: 'Alleen bekijken',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Doorgaan met bewerken',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Ja, overschakelen naar bulkverzending',
	InboxSuggestMoveToBulkComposeMessageContent:
		'U heeft meer dan {count} ontvangers geselecteerd. Wilt u het als bulkmail versturen?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Waarschuwing',
	InboxSwitchToOtherInbox: 'Overschakelen naar een andere inbox',
	InboxUndoSendMessageSuccess: 'Ongedaan maken van verzending',
	IncludeLineItems: 'Regelposten opnemen',
	IncludeSalesTax: 'Belastbaar',
	IncludesAiSmartPrompt: 'Bevat slimme AI-prompts',
	Incomplete: 'Onvolledig',
	IncreaseIndent: 'Inspringing vergroten',
	IndianHealthServiceFreeStandingFacility: 'Vrijstaande faciliteit van de Indian Health Service',
	IndianHealthServiceProviderFacility: 'Indiase faciliteit voor gezondheidszorgaanbieders',
	Information: 'Informatie',
	InitialAssessment: 'Eerste beoordeling',
	InitialSignupPageClientFamilyTitle: 'Cliënt of familielid',
	InitialSignupPageProviderTitle: 'Gezondheid ',
	InitialTreatment: 'Initiële behandeling',
	Initials: 'Initialen',
	InlineEmbed: 'Inline-embed',
	InputPhraseToConfirm: 'Om te bevestigen, typ {confirmationPhrase}.',
	Insert: 'Invoegen',
	InsertTable: 'Tabel invoegen',
	InstallCarepatronOnYourIphone1: 'Installeer Carepatron op uw iOS: tik op',
	InstallCarepatronOnYourIphone2: 'en vervolgens Toevoegen aan startscherm',
	InsufficientCalendarScopesSnackbar: 'Synchronisatie mislukt - geef Carepatron toestemming om de agenda te beheren',
	InsufficientInboxScopesSnackbar: 'Synchronisatie mislukt - geef Carepatron toestemming om e-mails te versturen',
	InsufficientScopeErrorCodeSnackbar: 'Synchronisatie mislukt - geef alle rechten aan Carepatron',
	Insurance: 'Verzekering',
	InsuranceAmount: 'Verzekeringsbedrag',
	InsuranceClaim: 'Verzekeringsclaim',
	InsuranceClaimAiChatPlaceholder: 'Vraag naar de verzekeringsclaim...',
	InsuranceClaimAiClaimNumber: 'Claim {number}',
	InsuranceClaimAiSubtitle: 'Verzekeringsfacturering • Claimvalidatie',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber} ingediend bij {payerNumber} {payerName} is afgewezen',
	InsuranceClaimErrorDescription:
		'De claim bevat fouten die zijn gemeld door de betaler of clearinghouse. Controleer de volgende foutberichten en dien de claim opnieuw in.',
	InsuranceClaimErrorGuideLink: 'Gids voor verzekeringsclaims',
	InsuranceClaimErrorTitle: 'Foutmeldingen bij het indienen van claims',
	InsuranceClaimNotFound: 'Verzekeringsclaim niet gevonden',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Een gedeeltelijke betaling van {paymentAmount}} other {Een betaling van {paymentAmount}}} voor claim {claimNumber} door {payerNumber} {payerName} is geregistreerd.',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber} ingediend bij {payerNumber} {payerName} is afgewezen',
	InsuranceClaims: 'Verzekeringsclaims',
	InsuranceInformation: 'Verzekeringsinformatie',
	InsurancePaid: 'Verzekering betaald',
	InsurancePayer: 'Verzekeringsbetaler',
	InsurancePayers: 'Verzekeringsbetalers',
	InsurancePayersDescription: 'Bekijk de betalers die aan uw account zijn toegevoegd en beheer de inschrijving.',
	InsurancePayment: 'Verzekeringsuitkering',
	InsurancePoliciesDetailsSubtitle: 'Voeg verzekeringsinformatie van de klant toe ter ondersteuning van claims.',
	InsurancePoliciesDetailsTitle: 'Beleidsdetails',
	InsurancePoliciesListSubtitle: 'Voeg verzekeringsinformatie van de klant toe ter ondersteuning van claims.',
	InsurancePoliciesListTitle: 'Verzekeringspolissen',
	InsuranceSelfPay: 'Zelf Betalen',
	InsuranceType: 'Verzekeringstype',
	InsuranceUnpaid: 'Verzekering niet betaald',
	Intake: 'Inname',
	IntakeExpiredErrorCodeSnackbar:
		'Deze intake is verlopen. Neem contact op met uw provider om een andere intake opnieuw te versturen.',
	IntakeNotFoundErrorSnackbar:
		'Deze intake kon niet worden gevonden. Neem contact op met uw provider om een andere intake opnieuw te sturen.',
	IntakeProcessLearnMoreInstructions: 'Handleiding voor het opzetten van uw intakeformulieren',
	IntakeTemplateSelectorPlaceholder:
		'Kies formulieren en overeenkomsten die u naar uw klant wilt sturen om in te vullen',
	Integration: 'Integratie',
	IntenseBlur: 'Vervaag uw achtergrond intens',
	InteriorDesigner: 'Interieurontwerper',
	InternetBanking: 'Bankoverschrijving',
	Interval: 'Interval',
	IntervalDays: 'Interval (dagen)',
	IntervalHours: 'Interval (uren)',
	Invalid: 'Ongeldig',
	InvalidDate: 'Ongeldige datum',
	InvalidDateFormat: 'De datum moet in {format} formaat zijn',
	InvalidDisplayName: 'Weergavenaam mag {value} niet bevatten',
	InvalidEmailFormat: 'Ongeldig e-mailformaat',
	InvalidFileType: 'Ongeldig bestandstype',
	InvalidGTMContainerId: 'Ongeldige GTM-container-ID-indeling',
	InvalidPaymentMethodCode: 'De geselecteerde betaalmethode is niet geldig. Kies een andere.',
	InvalidPromotionCode: 'Promotiecode is ongeldig',
	InvalidReferralDescription: 'Gebruikt u Carepatron al?',
	InvalidStatementDescriptor: `De beschrijving van de verklaring moet tussen de 5 en 22 tekens lang zijn en mag alleen letters, cijfers en spaties bevatten, en mag geen <, >, \\, ', ", * bevatten.`,
	InvalidToken: 'Ongeldig token',
	InvalidTotpSetupVerificationCode: 'Ongeldige verificatiecode.',
	InvalidURLErrorText: 'Dit moet een geldige URL zijn',
	InvalidZoomTokenErrorCodeSnackbar:
		'Het Zoom-token is verlopen. Verbind uw Zoom-app opnieuw en probeer het opnieuw.',
	Invite: 'Uitnodiging',
	InviteRelationships: 'Nodig relaties uit',
	InviteToPortal: 'Uitnodigen voor portaal',
	InviteToPortalModalDescription:
		'Er wordt een uitnodigingsmail naar uw cliënt gestuurd om zich aan te melden bij Carepatron.',
	InviteToPortalModalTitle: 'Nodig {name} uit voor het Carepatron-portaal',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Nieuwe gebruiker uitnodigen',
	Invited: 'Uitgenodigd',
	Invoice: 'Factuur',
	InvoiceColorPickerDescription: 'Kleurenthema dat op de factuur moet worden gebruikt',
	InvoiceColorTheme: 'Factuur kleurenthema',
	InvoiceContactDeleted: 'Het factuurcontact is verwijderd en deze factuur kan niet worden bijgewerkt.',
	InvoiceDate: 'Datum van uitgifte',
	InvoiceDetails: 'Factuurgegevens',
	InvoiceFieldsPlaceholder: 'Zoeken naar velden...',
	InvoiceFrom: 'Factuur {number} van {fromProvider}',
	InvoiceInvalidCredit: 'Ongeldig kredietbedrag, kredietbedrag mag het factuurtotaal niet overschrijden',
	InvoiceNotFoundDescription:
		'Neem contact op met uw provider en vraag om meer informatie of om de factuur opnieuw te sturen.',
	InvoiceNotFoundTitle: 'Factuur niet gevonden',
	InvoiceNumber: 'Factuur #',
	InvoiceNumberFormat: 'Factuur #{number}',
	InvoiceNumberMustEndWithDigit: 'Factuurnummer moet eindigen met een cijfer (0-9)',
	InvoicePageHeader: 'Facturen',
	InvoicePaidNotificationSubject: 'Factuur {invoiceNumber} betaald',
	InvoiceReminder: 'Factuurherinneringen',
	InvoiceReminderSentence: 'Stuur {deliveryType} herinnering {interval} {unit} {beforeAfter} factuur vervaldatum',
	InvoiceReminderSettings: 'Instellingen voor factuurherinneringen',
	InvoiceReminderSettingsInfo:
		'Herinneringen zijn alleen van toepassing op facturen die via Carepatron zijn verzonden',
	InvoiceReminders: 'Factuurherinneringen',
	InvoiceRemindersInfo:
		'Stel automatische herinneringen in voor factuurvervaldata. Herinneringen gelden alleen voor facturen die via Carepatron zijn verzonden.',
	InvoiceSettings: 'Factuurinstellingen',
	InvoiceStatus: 'Factuurstatus',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, VS',
	InvoiceTemplateDescriptionPlaceholder:
		'Voeg notities, bankoverschrijvingsgegevens of algemene voorwaarden voor alternatieve betalingen toe',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Zelfstandig ondernemer',
	InvoiceTemplateEthnicityPlaceholder: 'Kaukasisch',
	InvoiceTemplateNotFoundDescription: 'Neem contact op met uw provider en vraag om meer informatie.',
	InvoiceTemplateNotFoundTitle: 'Factuursjabloon niet gevonden',
	InvoiceTemplates: 'Factuursjablonen',
	InvoiceTemplatesDescription:
		'Pas uw factuursjablonen aan zodat ze uw merk weerspiegelen, voldoen aan wettelijke vereisten en inspelen op de voorkeuren van uw klanten met onze gebruiksvriendelijke sjablonen.',
	InvoiceTheme: 'Factuur thema',
	InvoiceTotal: 'Factuur Totaal',
	InvoiceUninvoicedAmounts: 'Factuur niet-gefactureerde bedragen',
	InvoiceUpdateVersionMessage:
		'Voor het bewerken van deze factuur is de nieuwste versie vereist. Laad Carepatron opnieuw en probeer het opnieuw.',
	Invoices: '{count, plural, one {Factuur} other {Facturen}}',
	InvoicesEmptyStateDescription: 'Er zijn geen facturen gevonden',
	InvoicingAndPayment: 'Facturatie ',
	Ireland: 'Ierland',
	IsA: 'is een',
	IsBetween: 'is tussen',
	IsEqualTo: 'is gelijk aan',
	IsGreaterThan: 'is groter dan',
	IsGreaterThanOrEqualTo: 'is groter dan of gelijk aan',
	IsLessThan: 'is minder dan',
	IsLessThanOrEqualTo: 'is kleiner dan of gelijk aan',
	IssueCredit: 'Krediet uitgeven',
	IssueCreditAdjustment: 'Aanpassing van kredietuitgifte',
	IssueDate: 'Uitgiftedatum',
	Italic: 'Cursief',
	Items: 'Artikelen',
	ItemsAndAdjustments: 'Items en aanpassingen',
	ItemsRemaining: '+{count} items remaining',
	JobTitle: 'Functietitel',
	Join: 'Meedoen',
	JoinCall: 'Deelnemen aan oproep',
	JoinNow: 'Word nu lid',
	JoinProduct: 'Word {product}',
	JoinVideoCall: 'Deelnemen aan videogesprek',
	JoinWebinar: 'Neem deel aan webinar',
	JoinWithVideoCall: 'Doe mee met {product}',
	Journalist: 'Journalist',
	JustMe: 'Gewoon ik',
	JustYou: 'Alleen jij',
	Justify: 'Verantwoorden',
	KeepSeparate: 'Gescheiden houden',
	KeepSeparateSuccessMessage: 'U heeft met succes afzonderlijke registraties bijgehouden voor {clientNames}',
	KeepWaiting: 'Blijf wachten',
	Label: 'Label',
	LabelOptional: 'Etiket (optioneel)',
	LactationConsulting: 'Lactatiekundig advies',
	Language: 'Taal',
	Large: 'Groot',
	LastDxCode: 'Laatste DX-code',
	LastLoggedIn: 'Laatst ingelogd op {date} om {time}',
	LastMenstrualPeriod: 'Laatste menstruatieperiode',
	LastMonth: 'Vorige maand',
	LastNDays: 'Laatste {number} dagen',
	LastName: 'Achternaam',
	LastNameFirstInitial: 'Achternaam, eerste letter',
	LastWeek: 'Vorige week',
	LastXRay: 'Laatste röntgenfoto',
	LatestVisitOrConsultation: 'Laatste bezoek of consultatie',
	Lawyer: 'Advocaat',
	LearnMore: 'Meer informatie',
	LearnMoreTipsToGettingStarted: 'Leer meer tips om te beginnen',
	LearnToSetupInbox: 'Handleiding voor het instellen van een inbox-account',
	Leave: 'Vertrekken',
	LeaveCall: 'Oproep verlaten',
	LeftAlign: 'Links uitlijnen',
	LegacyBillingItemsNotAvailable:
		'Individuele factureringsposten zijn nog niet beschikbaar voor deze afspraak. Je kunt deze nog steeds normaal factureren.',
	LegacyBillingItemsNotAvailableTitle: 'Legacy facturering',
	LegalAndConsent: 'Juridisch en toestemming',
	LegalConsentFormPrimaryText: 'Wettelijke toestemming',
	LegalConsentFormSecondaryText: 'Opties accepteren of afwijzen',
	LegalGuardian: 'Wettelijke voogd',
	Letter: 'Brief',
	LettersCategoryDescription: 'Voor het maken van klinische en administratieve correspondentie',
	Librarian: 'Bibliothecaris',
	LicenseNumber: 'Licentienummer',
	LifeCoach: 'Levenscoach',
	LifeCoaches: 'Levenscoaches',
	Limited: 'Beperkt',
	LineSpacing: 'Regel- en alinea-afstand',
	LinearScaleFormPrimaryText: 'Lineaire schaal',
	LinearScaleFormSecondaryText: 'Schaalopties 1-10',
	Lineitems: 'Posten',
	Link: 'Link',
	LinkClientFormSearchClientLabel: 'Zoek een klant',
	LinkClientModalTitle: 'Link naar bestaande klant',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> contactgegevens zijn toegevoegd aan het record van <strong>{existingName}</strong>.',
	LinkClientSuccessTitle: 'Succesvol gekoppeld aan bestaand contact',
	LinkForCallCopied: 'Link gekopieerd!',
	LinkToAnExistingClient: 'Link naar een bestaande klant',
	LinkToClient: 'Link naar klant',
	ListAndTracker: 'Lijst/Tracker',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} is in deze oproep}
			other {{attendees} zijn in deze oproep}
		}`,
	ListStyles: 'Lijststijlen',
	ListsAndTrackersCategoryDescription: 'Voor het organiseren en bijhouden van werk',
	LivingArrangements: 'Woonregelingen',
	LoadMore: 'Meer laden',
	Loading: 'Bezig met laden...',
	LocalizationPanelDescription: 'Beheer instellingen voor uw taal en tijdzone',
	LocalizationPanelTitle: 'Taal en tijdzone',
	Location: 'Locatie',
	LocationDescription:
		'Stel fysieke en virtuele locaties in met specifieke adressen, kamernamen en typen virtuele ruimtes om het plannen van afspraken en videogesprekken eenvoudiger te maken.',
	LocationNumber: 'Locatienummer',
	LocationOfService: 'Locatie van de dienst',
	LocationOfServiceRecommendedActionInfo:
		'Het toevoegen van een specifieke locatie aan deze service kan invloed hebben op uw beschikbaarheid.',
	LocationRemote: 'Op afstand',
	LocationType: 'Locatietype',
	Locations: 'Locaties',
	Lock: 'Slot',
	Locked: 'Vergrendeld',
	LockedNote: 'Geblokkeerde notitie',
	LogInToSaveOrAuthoriseCard: 'Meld u aan om de kaart op te slaan of te autoriseren',
	LogInToSaveOrAuthorisePayment: 'Log in om de betaling op te slaan of te autoriseren',
	Login: 'Inloggen',
	LoginButton: 'Aanmelden',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Wachtwoord vergeten',
	LoginPassword: 'Wachtwoord',
	Logo: 'Logo',
	LogoutAreYouSure: 'Meld u af bij dit apparaat.',
	LogoutButton: 'Afmelden',
	London: 'Londen',
	LongTextAnswer: 'Lange tekst antwoord',
	LongTextFormPrimaryText: 'Lange tekst',
	LongTextFormSecondaryText: 'Opties voor alineastijl',
	Male: 'Mannelijk',
	Manage: 'Beheren',
	ManageAllClientTags: 'Beheer alle clienttags',
	ManageAllNoteTags: 'Beheer alle notitielabels',
	ManageAllTemplateTags: 'Beheer alle sjabloontags',
	ManageConnections: 'Verbindingen beheren',
	ManageConnectionsGmailDescription: 'Andere teamleden kunnen uw gesynchroniseerde Gmail niet zien.',
	ManageConnectionsGoogleCalendarDescription: `Andere teamleden kunnen uw gesynchroniseerde agenda's niet zien. Afspraken van cliënten kunnen alleen worden bijgewerkt of verwijderd vanuit Carepatron.`,
	ManageConnectionsInboxSyncHelperText:
		'Ga naar de pagina Postvak IN om de instellingen voor Postvak IN synchroniseren te beheren.',
	ManageConnectionsMicrosoftCalendarDescription: `Andere teamleden kunnen uw gesynchroniseerde agenda's niet zien. Afspraken van cliënten kunnen alleen worden bijgewerkt of verwijderd vanuit Carepatron.`,
	ManageConnectionsOutlookDescription: 'Andere teamleden kunnen uw gesynchroniseerde Microsoft Outlook niet zien.',
	ManageInboxAccountButton: 'Nieuwe inbox',
	ManageInboxAccountEdit: 'Inbox beheren',
	ManageInboxAccountPanelTitle: 'Postvakken IN',
	ManageInboxAssignTeamPlaceholder: 'Kies teamleden voor toegang tot de inbox',
	ManageInboxBasicInfoColor: 'Kleur',
	ManageInboxBasicInfoDescription: 'Beschrijving',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Waarvoor gaat u of uw team deze inbox gebruiken?',
	ManageInboxBasicInfoName: 'Naam inbox',
	ManageInboxBasicInfoNamePlaceholder: 'Bijvoorbeeld klantenservice, admin',
	ManageInboxConnectAppAlreadyConnectedError:
		'Het kanaal dat u probeerde te verbinden, is al verbonden met Carepatron',
	ManageInboxConnectAppConnect: 'Verbinden',
	ManageInboxConnectAppConnectedInfo: 'Verbonden met een account',
	ManageInboxConnectAppContinue: 'Doorgaan',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Meld je aan met',
	ManageInboxConnectAppSubtitle:
		'Verbind uw apps om al uw communicatie naadloos te verzenden, ontvangen en volgen op één centrale plek.',
	ManageInboxNewInboxTitle: 'Nieuwe inbox',
	ManagePlan: 'Plan beheren',
	ManageProfile: 'Profiel beheren',
	ManageReferralsModalDescription:
		'Help ons de bekendheid van ons gezondheidszorgplatform te vergroten en verdien beloningen.',
	ManageReferralsModalTitle: 'Verwijs een vriend en verdien beloningen!',
	ManageStaffRelationshipsAddButton: 'Relaties beheren',
	ManageStaffRelationshipsEmptyStateText: 'Geen relaties toegevoegd',
	ManageStaffRelationshipsModalDescription:
		'Als u cliënten selecteert, worden er nieuwe relaties toegevoegd. Als u cliënten deselecteert, worden bestaande relaties verwijderd.',
	ManageStaffRelationshipsModalTitle: 'Relaties beheren',
	ManageStatuses: 'Statussen beheren',
	ManageStatusesActiveStatusHelperText: 'Er is minimaal één actieve status vereist',
	ManageStatusesDescription: 'Pas uw statuslabels aan en kies kleuren die aansluiten bij uw workflow.',
	ManageStatusesSuccessSnackbar: 'Statussen succesvol bijgewerkt',
	ManageTags: 'Labels beheren',
	ManageTaskAttendeeStatus: 'Beheer afspraakaantallen',
	ManageTaskAttendeeStatusDescription: 'Pas uw afspraakstatussen aan om ze af te stemmen op uw workflow.',
	ManageTaskAttendeeStatusHelperText: 'Ten minste één status is vereist',
	ManageTaskAttendeeStatusSubtitle: 'Aangepaste statussen',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Handmatig',
	ManualAppointment: 'Handmatige afspraak',
	ManualPayment: 'Handmatige betaling',
	ManuallyTypeLocation: 'Handmatig locatie typen',
	MapColumns: 'Kaartkolommen',
	MappingRequired: 'Mapping vereist',
	MarkAllAsRead: 'Markeer alles als gelezen',
	MarkAsCompleted: 'Markeer als voltooid',
	MarkAsManualSubmission: 'Markeer als ingediend',
	MarkAsPaid: 'Markeren als betaald',
	MarkAsRead: 'Markeer als gelezen',
	MarkAsUnpaid: 'Markeer als onbetaald',
	MarkAsUnread: 'Markeer als ongelezen',
	MarkAsVoid: 'Markeren als ongeldig',
	Marker: 'Marker',
	MarketingManager: 'Marketingmanager',
	MassageTherapist: 'Massagetherapeut',
	MassageTherapists: 'Massagetherapeuten',
	MassageTherapy: 'Massagetherapie',
	MaxBookingTimeDescription1: 'Klanten kunnen maximaal',
	MaxBookingTimeDescription2: 'in de toekomst',
	MaxBookingTimeLabel: '{timePeriod} van tevoren',
	MaxCapacity: 'Maximale capaciteit',
	Maximize: 'Maximaliseren',
	MaximumAttendeeLimit: 'Maximale limiet',
	MaximumBookingTime: 'Maximale boekingstijd',
	MaximumBookingTimeError: 'Maximale boekingstijd mag {valueUnit} niet overschrijden',
	MaximumMinimizedPanelsReachedDescription:
		'U kunt maximaal {count} zijpanelen tegelijk minimaliseren. Doorgaan zal het vroegst geminimaliseerde paneel sluiten. Wilt u doorgaan?',
	MaximumMinimizedPanelsReachedTitle: 'Je hebt te veel panelen open.',
	MechanicalEngineer: 'Werktuigbouwkundig ingenieur',
	MediaGallery: 'Mediagalerij',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid-aanbiedernummer',
	MedicalAssistant: 'Medisch assistent',
	MedicalCoder: 'Medische Codeur',
	MedicalDoctor: 'Medisch Dokter',
	MedicalIllustrator: 'Medisch Illustrator',
	MedicalInterpreter: 'Medisch tolk',
	MedicalTechnologist: 'Medisch Technoloog',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare-aanbiedernummer',
	Medicine: 'Geneesmiddel',
	Medium: 'Medium',
	Meeting: 'Ontmoeting',
	MeetingEnd: 'Einde vergadering',
	MeetingEnded: 'Vergadering beëindigd',
	MeetingHost: 'Gastheer van de bijeenkomst',
	MeetingLowerHand: 'Onderste hand',
	MeetingOpenChat: 'Chat openen',
	MeetingPersonRaisedHand: '{name} stak zijn/haar hand op.',
	MeetingRaiseHand: 'Hand opsteken',
	MeetingReady: 'Vergadering gereed',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Vergaderingen',
	MemberId: 'Lidmaatschaps-ID',
	MentalHealth: 'Mentale gezondheid',
	MentalHealthPractitioners: 'Geestelijke gezondheidszorgbeoefenaars',
	MentalHealthProfessional: 'Professional in geestelijke gezondheidszorg',
	Merge: 'Samenvoegen',
	MergeClientRecords: 'Klantgegevens samenvoegen',
	MergeClientRecordsDescription: 'Het samenvoegen van klantgegevens combineert al hun gegevens, waaronder:',
	MergeClientRecordsDescription2: 'Wilt u doorgaan met de samenvoeging? Deze actie kan niet ongedaan worden gemaakt',
	MergeClientRecordsItem1: 'Notities en documenten',
	MergeClientRecordsItem2: 'Afspraken',
	MergeClientRecordsItem3: 'Facturen',
	MergeClientRecordsItem4: 'Gesprekken',
	MergeClientsSuccess: 'Clientrecord succesvol samengevoegd',
	MergeLimitExceeded: 'Je kunt maximaal 4 klanten tegelijk samenvoegen.',
	Message: 'Bericht',
	MessageAttachments: '{total} bijlagen',
	Method: 'Methode',
	MfaAvailabilityDisclaimer:
		'MFA is alleen beschikbaar voor e-mail- en wachtwoordlogins. Om wijzigingen aan te brengen in uw MFA-instellingen, logt u in met uw e-mail en wachtwoord.',
	MfaDeviceLostPanelDescription: 'U kunt uw identiteit ook verifiëren door een code per e-mail te ontvangen.',
	MfaDeviceLostPanelTitle: 'Bent u uw MFA-apparaat kwijt?',
	MfaDidntReceiveEmailCode: 'Geen code ontvangen? Neem contact op met support',
	MfaEmailOtpSendFailureSnackbar: 'Het verzenden van het eenmalige e-mailbericht is mislukt.',
	MfaEmailOtpSentSnackbar: 'Een code is verzonden naar {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Het verifiëren van het eenmalige e-mailadres is mislukt.',
	MfaHasBeenSetUpText: 'Je hebt MFA ingesteld',
	MfaPanelDescription:
		'Beveilig uw account door Multi-Factor Authentication (MFA) in te schakelen voor een extra beschermingslaag. Verifieer uw identiteit via een secundaire methode om ongeautoriseerde toegang te voorkomen.',
	MfaPanelNotAuthorizedError: 'U moet zijn aangemeld met gebruikersnaam ',
	MfaPanelRecommendationDescription:
		'U bent onlangs ingelogd met een alternatieve methode om uw identiteit te verifiëren. Om uw account veilig te houden, kunt u overwegen een nieuw MFA-apparaat in te stellen.',
	MfaPanelRecommendationTitle: '<strong>Aanbevolen:</strong> Werk uw MFA-apparaat bij',
	MfaPanelTitle: 'Multi-Factor Authenticatie (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'U moet uw e-mailadres verifiëren voordat u uw MFA-instellingen kunt bijwerken.',
	MfaRecommendationBannerDescription:
		'U bent onlangs ingelogd met een alternatieve methode om uw identiteit te verifiëren. Om uw account veilig te houden, kunt u overwegen een nieuw MFA-apparaat in te stellen.',
	MfaRecommendationBannerPrimaryAction: 'MFA instellen',
	MfaRecommendationBannerTitle: 'Aanbevolen',
	MfaRemovedSnackbarTitle: 'MFA is verwijderd.',
	MfaSendEmailCode: 'Code verzenden',
	MfaVerifyIdentityLostDeviceButton: 'Ik heb geen toegang meer tot mijn MFA-apparaat',
	MfaVerifyYourIdentityPanelDescription: 'Controleer de code in uw authenticator-app en voer deze hieronder in.',
	MfaVerifyYourIdentityPanelTitle: 'Verifieer uw identiteit',
	MicCamWarningMessage:
		'Deblokkeer uw camera en microfoon door op de geblokkeerde pictogrammen in de adresbalk van uw browser te klikken.',
	MicCamWarningTitle: 'Camera en microfoon zijn geblokkeerd',
	MicOff: 'Microfoon staat uit',
	MicOn: 'Microfoon staat aan',
	MicSource: 'Microfoonbron',
	MicWarningMessage: 'Er is een probleem gedetecteerd met uw microfoon',
	Microphone: 'Microfoon',
	MicrophonePermissionBlocked: 'Microfoon toegang geblokkeerd',
	MicrophonePermissionBlockedDescription: 'Update uw microfoonmachtigingen om opnames te starten.',
	MicrophonePermissionError: 'Geef toestemming voor de microfoon in uw browserinstellingen om door te gaan',
	MicrophonePermissionPrompt: 'Geef toestemming voor microfoontoegang om door te gaan',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlook-agendakleur',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft-teams',
	MiddleEast: 'Midden-Oosten',
	MiddleName: 'Middelste naam',
	MiddleNames: 'Middelste naam',
	Midwife: 'Verloskundige',
	Midwives: 'Vroedvrouwen',
	Milan: 'Milaan',
	MinBookingTimeDescription1: 'Klanten kunnen niet binnen een bepaalde tijd een afspraak maken',
	MinBookingTimeDescription2: 'van de begintijd van een afspraak',
	MinBookingTimeLabel: '{timePeriod} voor afspraak',
	MinCancellationTimeEditModeDescription: 'Stel in hoeveel uren een klant zonder boete kan annuleren',
	MinCancellationTimeUnset: 'Geen minimale annuleringstermijn vastgesteld',
	MinCancellationTimeViewModeDescription: 'Opzegtermijn zonder boete',
	MinMaxBookingTimeUnset: 'Geen tijd ingesteld',
	Minimize: 'Minimaliseren',
	MinimizeConfirmationDescription:
		'Je hebt een actief geminimaliseerd paneel. Als je doorgaat, wordt het gesloten en kan je niet-opgeslagen gegevens verliezen.',
	MinimizeConfirmationTitle: 'Geminimaliseerd paneel sluiten?',
	MinimumBookingTime: 'Minimale boekingstijd',
	MinimumCancellationTime: 'Minimale annuleringstijd',
	MinimumPaymentError: 'Een minimum bedrag van {minimumAmount} is vereist voor online betalingen',
	MinuteAbbreviated: 'minuten',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minuut voor minuut',
	MinutesPlural: '{age, plural, one {# minuut} other {# minuten}}',
	MiscellaneousInformation: 'Overige informatie',
	MissingFeatures: 'Ontbrekende functies',
	MissingPaymentMethod: 'Voeg een betaalmethode toe aan uw abonnement om meer medewerkers toe te voegen.',
	MobileNumber: 'Mobiel nummer',
	MobileNumberOptional: 'Mobiel nummer (optioneel)',
	Modern: 'Modern',
	Modifiers: 'Bepalingen',
	ModifiersPlaceholder: 'Bepalingen',
	Monday: 'Maandag',
	Month: 'Maand',
	Monthly: 'Maandelijks',
	MonthlyCost: 'Maandelijkse kosten',
	MonthlyOn: 'Maandelijks op {date}',
	MonthsPlural: '{age, plural, one {# maand} other {# maanden}}',
	More: 'Meer',
	MoreActions: 'Meer acties',
	MoreSettings: 'Meer instellingen',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Meest gebruikte',
	MostDownloaded: 'Meest gedownload',
	MostPopular: 'Meest populair',
	Mother: 'Moeder',
	MotherInLaw: 'Schoonmoeder',
	MoveDown: 'Naar beneden gaan',
	MoveInboxConfirmationDescription:
		'Het opnieuw toewijzen van deze app-verbinding zal deze verwijderen uit de <strong>{currentInboxName}</strong> inbox.',
	MoveTemplateToFolder: 'Verplaats `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} verplaatst naar {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Succesvol verplaatst naar de standaard intake map',
	MoveTemplateToNewFolder: 'Maak een nieuwe map om dit item naar te verplaatsen.',
	MoveToChosenFolder: 'Kies een map om dit item naar te verplaatsen. U kunt indien nodig een nieuwe map maken.',
	MoveToFolder: 'Verplaats naar map',
	MoveToInbox: 'Verplaatsen naar Postvak IN',
	MoveToNewFolder: 'Verplaats naar nieuwe map',
	MoveToSelectedFolder:
		'Eenmaal verplaatst, wordt het item georganiseerd onder de geselecteerde map en zal het niet langer zichtbaar zijn op de huidige locatie.',
	MoveUp: 'Omhoog gaan',
	MultiSpeciality: 'Multi-specialisme',
	MultipleChoiceFormPrimaryText: 'Meerkeuzevraag',
	MultipleChoiceFormSecondaryText: 'Kies meerdere opties',
	MultipleChoiceGridFormPrimaryText: 'Meerkeuzeraster',
	MultipleChoiceGridFormSecondaryText: 'Kies opties uit een matrix',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Muziektherapeut',
	MustContainOneLetterError: 'Moet minimaal één letter bevatten',
	MustEndWithANumber: 'Moet eindigen met een getal',
	MustHaveAtLeastXItems: 'Moet ten minste {count, plural, one {# item} other {# items}} hebben',
	MuteAudio: 'Audio dempen',
	MuteEveryone: 'Iedereen dempen',
	MyAvailability: 'Mijn beschikbaarheid',
	MyGallery: 'Mijn galerij',
	MyPortal: 'Mijn Portaal',
	MyRelationships: 'Mijn relaties',
	MyTemplates: 'Teamsjablonen',
	MyofunctionalTherapist: 'Myofunctioneel therapeut',
	NCalifornia: 'Noord-Californië',
	NPI: 'NPI',
	NVirginia: 'Noord-Virginia',
	Name: 'Naam',
	NameIsRequired: 'Naam is verplicht',
	NameMustNotBeAWebsite: 'Naam mag geen website zijn',
	NameMustNotBeAnEmail: 'Naam mag geen e-mailadres zijn',
	NameMustNotContainAtSign: 'Naam mag geen @-teken bevatten',
	NameMustNotContainHTMLTags: 'Naam mag geen HTML-tags bevatten',
	NameMustNotContainSpecialCharacters: 'Naam mag geen speciale tekens bevatten',
	NameOnCard: 'Naam op kaart',
	NationalProviderId: 'Nationale aanbieder-identificatie (NPI)',
	NaturopathicDoctor: 'Natuurgeneeskundig arts',
	NavigateToPersonalSettings: 'Profiel',
	NavigateToSubscriptionSettings: 'Abonnementsinstellingen',
	NavigateToWorkspaceSettings: 'Werkruimte-instellingen',
	NavigateToYourTeam: 'Beheer team',
	NavigationDrawerBilling: 'Facturering',
	NavigationDrawerBillingInfo: 'Factureringsgegevens, facturen en Stripe',
	NavigationDrawerCommunication: 'Mededeling',
	NavigationDrawerCommunicationInfo: 'Meldingen en sjablonen',
	NavigationDrawerInsurance: 'Verzekering',
	NavigationDrawerInsuranceInfo: 'Verzekeringsbetalers en claims',
	NavigationDrawerInvoices: 'Facturering',
	NavigationDrawerPersonal: 'Mijn profiel',
	NavigationDrawerPersonalInfo: 'Uw persoonlijke gegevens',
	NavigationDrawerProfile: 'Profiel',
	NavigationDrawerProviderSettings: 'Instellingen',
	NavigationDrawerScheduling: 'Planning',
	NavigationDrawerSchedulingInfo: 'Servicedetails en boekingen',
	NavigationDrawerSettings: 'Instellingen',
	NavigationDrawerTemplates: 'Sjablonen',
	NavigationDrawerTemplatesV2: 'Sjablonen V2',
	NavigationDrawerTrash: 'Afval',
	NavigationDrawerTrashInfo: 'Verwijderde items herstellen',
	NavigationDrawerWorkspace: 'Werkruimte-instellingen',
	NavigationDrawerWorkspaceInfo: 'Informatie over abonnementen en werkruimten',
	NegativeBalanceNotSupported: 'Negatieve rekeningsaldi worden niet ondersteund',
	Nephew: 'Neef',
	NetworkQualityFair: 'Eerlijke verbinding',
	NetworkQualityGood: 'Goede verbinding',
	NetworkQualityPoor: 'Slechte verbinding',
	Neurologist: 'Neuroloog',
	Never: 'Nooit',
	New: 'Nieuw',
	NewAppointment: 'Nieuwe afspraak',
	NewClaim: 'Nieuwe claim',
	NewClient: 'Nieuwe klant',
	NewClientNextStepsModalAddAnotherClient: 'Nog een klant toevoegen',
	NewClientNextStepsModalBookAppointment: 'Afspraak maken',
	NewClientNextStepsModalBookAppointmentDescription: 'Maak een afspraak of maak een taak aan.',
	NewClientNextStepsModalCompleteBasicInformation: 'Volledig cliëntendossier',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Voeg klantgegevens toe en leg de volgende stappen vast.',
	NewClientNextStepsModalCreateInvoice: 'Factuur aanmaken',
	NewClientNextStepsModalCreateInvoiceDescription: 'Voeg betalingsgegevens van de klant toe of maak een factuur.',
	NewClientNextStepsModalCreateNote: 'Notitie maken of document uploaden',
	NewClientNextStepsModalCreateNoteDescription: 'Leg aantekeningen en documentatie van klanten vast.',
	NewClientNextStepsModalDescription: 'Nu u een cliëntrecord hebt aangemaakt, kunt u de volgende acties ondernemen.',
	NewClientNextStepsModalSendIntake: 'Stuur inname',
	NewClientNextStepsModalSendIntakeDescription:
		'Verzamel klantgegevens en stuur aanvullende formulieren ter invulling en ondertekening.',
	NewClientNextStepsModalSendMessage: 'Bericht verzenden',
	NewClientNextStepsModalSendMessageDescription: 'Stel een bericht op en verstuur het naar uw klant.',
	NewClientNextStepsModalTitle: 'Volgende stappen',
	NewClientSuccess: 'Nieuwe klant succesvol aangemaakt',
	NewClients: 'Nieuwe klanten',
	NewConnectedApp: 'Nieuwe verbonden app',
	NewContact: 'Nieuw contact',
	NewContactNextStepsModalAddRelationship: 'Relatie toevoegen',
	NewContactNextStepsModalAddRelationshipDescription: 'Koppel dit contact aan gerelateerde klanten of groepen.',
	NewContactNextStepsModalBookAppointment: 'Maak afspraak',
	NewContactNextStepsModalBookAppointmentDescription: 'Boek een afspraak in de toekomst of maak een taak aan.',
	NewContactNextStepsModalCompleteProfile: 'Volledig profiel',
	NewContactNextStepsModalCompleteProfileDescription: 'Voeg contactgegevens toe en leg de volgende stappen vast.',
	NewContactNextStepsModalCreateNote: 'Notitie maken of document uploaden',
	NewContactNextStepsModalCreateNoteDescription: 'Klantnotities en documentatie vastleggen.',
	NewContactNextStepsModalDescription:
		'Hier zijn een aantal acties die u nu kunt ondernemen nadat u een contactpersoon hebt aangemaakt.',
	NewContactNextStepsModalInviteToPortal: 'Uitnodiging voor portaal',
	NewContactNextStepsModalInviteToPortalDescription: 'Stuur een uitnodiging om toegang te krijgen tot de portal.',
	NewContactNextStepsModalTitle: 'Volgende stappen',
	NewContactSuccess: 'Nieuw contact succesvol aangemaakt',
	NewDateOverrideButton: 'Nieuwe datum overschrijven',
	NewDiagnosis: 'Diagnose toevoegen',
	NewField: 'Nieuw veld',
	NewFolder: 'Nieuwe map',
	NewInvoice: 'Nieuwe factuur',
	NewLocation: 'Nieuwe locatie',
	NewLocationFailure: 'Het is niet gelukt om een nieuwe locatie aan te maken',
	NewLocationSuccess: 'Nieuwe locatie succesvol aangemaakt',
	NewManualPayer: 'Nieuwe handmatige betaler',
	NewNote: 'Nieuwe notitie',
	NewNoteCreated: 'Nieuwe notitie succesvol aangemaakt',
	NewPassword: 'Nieuw wachtwoord',
	NewPayer: 'Nieuwe betaler',
	NewPaymentMethod: 'Nieuwe betaalmethode',
	NewPolicy: 'Nieuw beleid',
	NewRelationship: 'Nieuwe relatie',
	NewReminder: 'Nieuwe herinnering',
	NewSchedule: 'Nieuw schema',
	NewSection: 'Nieuwe sectie',
	NewSectionOld: 'Nieuwe sectie [OUD]',
	NewSectionWithGrid: 'Nieuw gedeelte met raster',
	NewService: 'Nieuwe dienst',
	NewServiceFailure: 'Het is niet gelukt om een nieuwe service te maken',
	NewServiceSuccess: 'Nieuwe service succesvol aangemaakt',
	NewStatus: 'Nieuwe status',
	NewTask: 'Nieuwe taak',
	NewTaxRate: 'Nieuw belastingtarief',
	NewTeamMemberNextStepsModalAssignClients: 'Klienten toewijzen',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Wijs specifieke klanten toe aan uw teamlid.',
	NewTeamMemberNextStepsModalAssignServices: 'Diensten toewijzen',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Beheer hun toegewezen diensten en pas de prijzen indien nodig aan.',
	NewTeamMemberNextStepsModalBookAppointment: 'Maak afspraak',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Maak een afspraak in de toekomst of maak een taak aan.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Compleet profiel',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Voeg details toe over je teamlid om hun profiel te voltooien.',
	NewTeamMemberNextStepsModalDescription:
		'Hier zijn enkele acties om nu te ondernemen, nadat u een teamlid hebt gemaakt.',
	NewTeamMemberNextStepsModalEditPermissions: 'Bewerkingsrechten',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Pas hun toegangsrechten aan om ervoor te zorgen dat ze de juiste machtigingen hebben.',
	NewTeamMemberNextStepsModalSetAvailability: 'Beschikbaarheid instellen',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: `Configureer hun beschikbaarheid om schema's te maken.`,
	NewTeamMemberNextStepsModalTitle: 'Volgende stappen',
	NewTemplateFolderDescription: 'Maak een nieuwe map om uw documentatie te ordenen.',
	NewUIUpdateBannerButton: 'App opnieuw laden',
	NewUIUpdateBannerTitle: 'Er staat een nieuwe update klaar!',
	NewZealand: 'Nieuw-Zeeland',
	Newest: 'Nieuwste',
	NewestUnreplied: 'Nieuwste onbeantwoorde',
	Next: 'Volgende',
	NextInvoiceIssueDate: 'Volgende factuurdatum',
	NextNDays: 'Volgende {number} dagen',
	Niece: 'Nicht',
	No: 'Nee',
	NoAccessGiven: 'Geen toegang verleend',
	NoActionConfigured: 'Geen actie geconfigureerd',
	NoActivePolicies: 'Geen actief beleid',
	NoActiveReferrals: 'U heeft geen actieve verwijzingen',
	NoAppointmentsFound: 'Er zijn geen afspraken gevonden',
	NoAppointmentsHeading: 'Beheer afspraken en activiteiten van cliënten',
	NoArchivedPolicies: 'Geen gearchiveerde beleidsregels',
	NoAvailableTimes: 'Geen beschikbare tijden gevonden.',
	NoBillingItemsFound: 'Geen factuurposten gevonden',
	NoCalendarsSynced: `Geen agenda's gesynchroniseerd`,
	NoClaimsFound: 'Geen claims gevonden',
	NoClaimsHeading: 'Stroomlijn het indienen van claims voor terugbetaling',
	NoClientsHeading: 'Breng uw cliëntgegevens samen',
	NoCompletedReferrals: 'U heeft geen volledige verwijzingen',
	NoConnectionsHeading: 'Stroomlijn uw klantcommunicatie',
	NoContactsGivenAccess: 'Er zijn geen klanten of contactpersonen die toegang hebben gekregen tot deze notitie',
	NoContactsHeading: 'Blijf in contact met degenen die uw praktijk steunen',
	NoCopayOrCoinsurance: 'Geen eigen bijdrage of meeverzekering',
	NoCustomServiceSchedule:
		'Geen aangepaste planning ingesteld - de beschikbaarheid is afhankelijk van de beschikbaarheid van het teamlid',
	NoDescription: 'Geen beschrijving',
	NoDocumentationHeading: 'Maak en bewaar notities veilig',
	NoDuplicateRecordsHeading: 'Uw cliëntendossier is vrij van duplicaten',
	NoEffect: 'Geen effect',
	NoEnrolmentProfilesFound: 'Geen inschrijfprofielen gevonden',
	NoGlossaryItems: 'Geen glossary items',
	NoInvitedReferrals: 'U hebt geen uitgenodigde verwijzingen',
	NoInvoicesFound: 'Geen facturen gevonden',
	NoInvoicesHeading: 'Automatiseer uw facturering en betalingen',
	NoLimit: 'Geen limiet',
	NoLocationsFound: 'Er zijn geen locaties gevonden',
	NoLocationsWillBeAdded: 'Er worden geen locaties toegevoegd.',
	NoNoteFound: 'Geen notitie gevonden',
	NoPaymentMethods: 'U heeft geen opgeslagen betaalmethoden. U kunt er een toevoegen bij het betalen.',
	NoPermissionError: 'Je hebt geen toestemming',
	NoPermissions: 'U heeft geen toestemming om deze pagina te bekijken',
	NoPolicy: 'Geen annuleringsvoorwaarden toegevoegd',
	NoRecordsHeading: 'Personaliseer uw cliëntgegevens',
	NoRecordsToDisplay: 'Geen {resource} om weer te geven',
	NoRelationshipsHeading: 'Blijf in contact met degenen die uw cliënt ondersteunen',
	NoRemindersFound: 'Geen herinneringen gevonden',
	NoResultsFound: 'Geen resultaten gevonden',
	NoResultsFoundDescription: 'We kunnen geen items vinden die aan uw zoekopdracht voldoen',
	NoServicesAdded: 'Geen diensten toegevoegd',
	NoServicesApplied: 'Geen services toegepast',
	NoServicesWillBeAdded: 'Er worden geen diensten toegevoegd.',
	NoTemplate: 'U hebt geen oefensjablonen opgeslagen',
	NoTemplatesHeading: 'Maak uw eigen sjablonen',
	NoTemplatesInFolder: 'Geen sjablonen in deze map',
	NoTitle: 'Geen titel',
	NoTrashItemsHeading: 'Geen verwijderd item gevonden',
	NoTriggerConfigured: 'Geen trigger geconfigureerd',
	NoUnclaimedItemsFound: 'Er zijn geen niet-opgeëiste items gevonden.',
	NonAiTemplates: 'Niet-AI-sjablonen',
	None: 'Geen',
	NotAvailable: 'Niet beschikbaar',
	NotCovered: 'Niet gedekt',
	NotFoundSnackbar: 'Bron niet gevonden.',
	NotRequiredField: 'Niet vereist',
	Note: 'Opmerking',
	NoteDuplicateSuccess: 'Notitie succesvol gedupliceerd',
	NoteEditModeViewSwitcherDescription: 'Notitie maken en bewerken',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} heeft het formulier {noteTitle} ingediend',
	NoteLockSuccess: '{title} is vergrendeld',
	NoteModalAttachmentButton: 'Bijlagen toevoegen',
	NoteModalPhotoButton: `Foto's toevoegen/vastleggen`,
	NoteModalTrascribeButton: 'Live audio transcriberen',
	NoteResponderModeViewSwitcherDescription: 'Formulieren verzenden en reacties bekijken',
	NoteResponderModeViewSwitcherTooltipTitle: 'Reageer en dien formulieren in namens uw cliënten',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Formulieren invullen en indienen als klant',
	NoteUnlockSuccess: '{title} is ontgrendeld',
	NoteViewModeViewSwitcherDescription: 'Alleen-lezen toegang',
	Notes: 'Notities',
	NotesAndForms: 'Notities en formulieren',
	NotesCategoryDescription: 'Voor het documenteren van klantinteracties',
	NothingToSeeHere: 'Hier is niets te zien',
	Notification: 'Kennisgeving',
	NotificationIgnoredMessage: 'Alle {notificationType} meldingen worden genegeerd',
	NotificationRestoredMessage: 'Alle {notificationType} notificaties hersteld',
	NotificationSettingBillingDescription: 'Ontvang meldingen voor klantbetalingsupdates en herinneringen.',
	NotificationSettingBillingTitle: 'Facturering en betaling',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} alleen} other{{channels}} }',
	NotificationSettingClientDocumentationDescription: 'Ontvang meldingen voor klantbetalingsupdates en herinneringen.',
	NotificationSettingClientDocumentationTitle: 'Klant en documentatie',
	NotificationSettingCommunicationsDescription: 'Ontvang meldingen voor uw inbox en updates van uw verbonden kanalen',
	NotificationSettingCommunicationsTitle: 'Communicatie',
	NotificationSettingEmail: 'E-mail',
	NotificationSettingInApp: 'In-app',
	NotificationSettingPanelDescription: 'Kies de meldingen die je wilt ontvangen voor activiteiten en aanbevelingen.',
	NotificationSettingPanelTitle: 'Voorkeuren voor meldingen',
	NotificationSettingSchedulingDescription:
		'Ontvang meldingen wanneer een teamlid of klant een afspraak boekt, opnieuw plant of annuleert.',
	NotificationSettingSchedulingTitle: 'Planning',
	NotificationSettingUpdateSuccess: 'Meldingsinstellingen succesvol bijgewerkt',
	NotificationSettingWhereYouReceiveNotifications: 'Waar wil je deze meldingen ontvangen',
	NotificationSettingWorkspaceDescription:
		'Ontvang meldingen voor systeemwijzigingen, problemen, gegevensoverdrachten en abonnementsherinneringen.',
	NotificationSettingWorkspaceTitle: 'Werkruimte',
	NotificationTemplateUpdateFailed: 'Het is niet gelukt om de meldingsjabloon bij te werken',
	NotificationTemplateUpdateSuccess: 'Succesvol bijgewerkte notificatiesjabloon',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Wilt u een e-mailbericht met een annuleringsmelding naar de deelnemers sturen?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Annulering verzenden',
	NotifyAttendeesOfTaskConfirmationModalDescription: 'Wilt u een bevestigingsmail naar de deelnemers sturen?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Bevestiging verzenden',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Wilt u annuleringsmails naar deelnemers sturen?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {heeft} other {hebben}} geen e-mailadres en zullen daarom geen geautomatiseerde meldingen en herinneringen ontvangen.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {heeft} other {hebben}} geen e-mailadres en ontvangen daarom geen geautomatiseerde notificaties en herinneringen.',
	NotifyAttendeesOfTaskModalTitle: 'Wilt u een e-mailmelding naar deelnemers sturen?',
	NotifyAttendeesOfTaskSnackbar: 'Melding verzenden',
	NuclearMedicineTechnologist: 'Technoloog nucleaire geneeskunde',
	NumberOfClaims: '{number, plural, one {# Claim} other {# Claims}}',
	NumberOfClients: '{number, plural, one {# Klant} other {# Klanten}}',
	NumberOfContacts: '{number, plural, one {# Contact} other {# Contacten}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entry} other {entries}} gevonden',
	NumberOfErrors: '{count, plural, one {# fout} other {# fouten}}',
	NumberOfInvoices: '{number, plural, one {# Factuur} other {# Facturen}}',
	NumberOfLineitemsToCredit:
		'Je hebt <mark>{count} {count, plural, one {regel item} other {regel items}}</mark> om een ​​krediet voor uit te geven.',
	NumberOfPayments: '{number, plural, one {# Betaling} other {# Betalingen}}',
	NumberOfRelationships: '{number, plural, one {# Relatie} other {# Relaties}}',
	NumberOfResources: '{number, plural, one {# Bron} other {# Bronnen}}',
	NumberOfTeamMembers: '{number, plural, one {# Teamlid} other {# Teamleden}}',
	NumberOfTrashItems: '{number, plural, one {# item} other {# items}}',
	NumberOfUninvoicedAmounts:
		'Je hebt <mark>{count} niet-gefactureerde {count, plural, one {bedrag} other {bedragen}}</mark> om te factureren',
	NumberedList: 'Genummerde lijst',
	Nurse: 'Verpleegkundige',
	NurseAnesthetist: 'Verpleegkundige anesthesist',
	NurseAssistant: 'Verpleegkundig assistent',
	NurseEducator: 'Verpleegkundig docent',
	NurseMidwife: 'Verpleegkundige vroedvrouw',
	NursePractitioner: 'Verpleegkundig Specialist',
	Nurses: 'Verpleegkundigen',
	Nursing: 'Verpleging',
	Nutritionist: 'Voedingsdeskundige',
	Nutritionists: 'Voedingsdeskundigen',
	ObstetricianOrGynecologist: 'Verloskundige/Gynaecoloog',
	Occupation: 'Bezigheid',
	OccupationalTherapist: 'Ergotherapeut',
	OccupationalTherapists: 'Ergotherapeuten',
	OccupationalTherapy: 'Ergotherapie',
	Occurrences: 'Voorvallen',
	Of: 'van',
	Ohio: 'Ohio',
	OldPassword: 'Oud wachtwoord',
	OlderMessages: '{count} oudere berichten',
	Oldest: 'Oudste',
	OldestUnreplied: 'Oudste onbeantwoorde',
	On: 'op',
	OnboardingBusinessAgreement: 'Namens mijzelf en het bedrijf, ga ik akkoord met de {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Ergotherapeuten</mark> vormen een kwart van onze klanten op Carepatron',
	OnboardingLoadingProfession:
		'We hebben heel wat <mark>{profession}</mark> die Carepatron gebruiken en er succesvol mee zijn.',
	OnboardingLoadingPsychologist: '<mark>Psychologen</mark> vormen meer dan de helft van onze klanten op Carepatron',
	OnboardingLoadingSubtitleFive:
		'Onze missie is om<mark> toegankelijke software voor gezondheidszorg</mark> aan iedereen.',
	OnboardingLoadingSubtitleFour:
		'<mark>Vereenvoudigde gezondheidssoftware</mark> voor meer dan 10.000 mensen wereldwijd.',
	OnboardingLoadingSubtitleThree:
		'Redden<mark> 1 dag per week</mark> op administratieve taken met behulp van Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Redden<mark> 2 uur</mark> dagelijks bezig met administratieve taken met behulp van Carepatron.',
	OnboardingReviewLocationOne: 'Holland Park Geestelijke Gezondheidscentrum',
	OnboardingReviewLocationThree: 'Praktijkverpleegkundige, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Kliniek voor het Levenshuis',
	OnboardingReviewNameOne: 'Annuleren P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Klara W.',
	OnboardingReviewOne:
		'"Carepatron is super intuïtief in gebruik. Het helpt ons onze praktijk zo goed te runnen dat we niet eens meer een team van beheerders nodig hebben"',
	OnboardingReviewThree:
		'"Het is de best practice-oplossing die ik heb gebruikt, zowel qua functies als qua kosten. Het heeft alles wat ik nodig heb om mijn bedrijf te laten groeien"',
	OnboardingReviewTwo:
		'"Ik ben ook dol op de carepatron-app. Hiermee kan ik mijn cliënten en werk bijhouden terwijl ik onderweg ben."',
	OnboardingTitle: `Laten we beginnen<mark> weten
 jij beter</mark>`,
	Oncologist: 'Oncoloog',
	Online: 'Online',
	OnlineBookingColorTheme: 'Online boeking kleurenthema',
	OnlineBookings: 'Online boekingen',
	OnlineBookingsHelper: 'Kies wanneer online boekingen kunnen worden gemaakt en door welk type klanten',
	OnlinePayment: 'Online betaling',
	OnlinePaymentSettingCustomInfo:
		'De online betalingsinstellingen voor deze service verschillen van de wereldwijde boekingsinstellingen.',
	OnlinePaymentSettings: 'Online betalingsinstellingen',
	OnlinePaymentSettingsInfo:
		'Ontvang betalingen voor diensten op het moment van online boeken om betalingen te beveiligen en te stroomlijnen',
	OnlinePaymentSettingsPaymentsDisabled:
		'Betalingen zijn uitgeschakeld en kunnen dus niet worden geïnd tijdens online boeking. Controleer uw betalingsinstellingen om betalingen in te schakelen.',
	OnlinePaymentSettingsStripeNote:
		'{action} om online boekingsbetalingen te ontvangen en uw betalingsproces te stroomlijnen',
	OnlinePaymentsNotSupportedForCurrency: 'Online betalingen worden niet ondersteund in {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Sorry, online betalingen worden niet ondersteund in deze valuta',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Helaas worden online betalingen nog niet ondersteund in uw land',
	OnlineScheduling: 'Online planning',
	OnlyVisibleToYou: 'Alleen zichtbaar voor jou',
	OnlyYou: 'Alleen jij',
	OnsetDate: 'Begindatum',
	OnsetOfCurrentSymptomsOrIllness: 'Begin van huidige symptomen of ziekte',
	Open: 'Open',
	OpenFile: 'Bestand openen',
	OpenSettings: 'Instellingen openen',
	Ophthalmologist: 'Oogarts',
	OptimiseTelehealthCalls: 'Optimaliseer uw Telehealth-gesprekken',
	OptimizeServiceTimes: 'Optimaliseer servicetijden',
	Options: 'Opties',
	Optometrist: 'Oogarts',
	Or: 'of',
	OrAttachSingleFile: 'een bestand toevoegen',
	OrDragAndDrop: 'of sleep en drop',
	OrderBy: 'Bestellen op',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisatie of individu',
	OrganizationPlanInclusion1: 'Geavanceerde machtigingen',
	OrganizationPlanInclusion2: 'Gratis ondersteuning voor het importeren van klantgegevens',
	OrganizationPlanInclusion3: 'Toegewijde succesmanager',
	OrganizationPlanInclusionHeader: 'Alles in Professional, plus...',
	Orthodontist: 'Orthodontist',
	Orthotist: 'Orthesist',
	Other: 'Ander',
	OtherAdjustments: 'Andere aanpassingen',
	OtherAdjustmentsTableEmptyState: 'Geen aanpassingen gevonden',
	OtherEvents: 'Andere evenementen',
	OtherId: 'Andere ID',
	OtherIdQualifier: 'Andere ID-kwalificatie',
	OtherPaymentMethod: 'Andere betaalmethode',
	OtherPlanMessage:
		'Blijf de baas over de behoeften van uw praktijk. Bekijk uw huidige abonnement, controleer het gebruik en verken upgrade-opties om meer functies te ontgrendelen naarmate uw team groeit.',
	OtherPolicy: 'Overige verzekeringen',
	OtherProducts: 'Welke andere producten of hulpmiddelen gebruikt u?',
	OtherServices: 'Overige diensten',
	OtherTemplates: 'Andere sjablonen',
	Others: 'Anderen',
	OthersPeople: `{n, plural, 		one {1 andere persoon}
		other {# andere mensen}
	}`,
	OurResearchTeamReachOut:
		'Kan ons onderzoeksteam contact met u opnemen om meer te weten te komen over hoe Carepatron beter aan uw behoeften had kunnen voldoen?',
	OutOfOffice: 'Buiten kantoor',
	OutOfOfficeColor: 'Kleur buiten kantoor',
	OutOfOfficeHelper: 'Sommige gekozen teamleden zijn niet op kantoor',
	OutsideLabCharges: 'Kosten voor extern laboratorium',
	OutsideOfWorkingHours: 'Buiten werkuren',
	OutsideWorkingHoursHelper: 'Sommige gekozen teamleden zijn buiten werkuren actief',
	Overallocated: 'Overbezet',
	OverallocatedPaymentDescription: `Deze betaling is te veel toegewezen aan factureerbare items.
 U kunt een toewijzing doen aan onbetaalde items, of een tegoedbon of terugbetaling doen.`,
	OverallocatedPaymentTitle: 'Te veel toegewezen betaling',
	OverdueTerm: 'Te late termijn (dagen)',
	OverinvoicedAmount: 'Te hoog gefactureerd bedrag',
	Overpaid: 'Te veel betaald',
	OverpaidAmount: 'Teveel betaald bedrag',
	Overtime: 'overwerk',
	Owner: 'Eigenaar',
	POS: 'POS',
	POSCode: 'POS-code',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Niet-opgeslagen wijzigingen gaan verloren. Wil je toch vertrekken?',
	PageBlockerTitle: 'Wijzigingen verwijderen?',
	PageFormat: 'Pagina-indeling',
	PageNotFound: 'Pagina niet gevonden',
	PageNotFoundDescription: 'U heeft geen toegang meer tot deze pagina of deze kan niet worden gevonden',
	PageUnauthorised: 'Ongeautoriseerde toegang',
	PageUnauthorisedDescription: 'U hebt geen toestemming om deze pagina te openen',
	Paid: 'Betaald',
	PaidAmount: 'Betaald bedrag',
	PaidAmountMinimumValueError: 'Betaald bedrag moet groter zijn dan 0',
	PaidAmountRequiredError: 'Betaald bedrag is vereist',
	PaidItems: 'Betaalde items',
	PaidMultiple: 'Betaald',
	PaidOut: 'Uitbetaald',
	ParagraphStyles: 'Alinea-stijlen',
	Parent: 'Ouder',
	Paris: 'Parijs',
	PartialRefundAmount: 'Deels terugbetaald ({amount} resterend)',
	PartiallyFull: 'Gedeeltelijk vol',
	PartiallyPaid: 'Gedeeltelijk betaald',
	PartiallyRefunded: 'Gedeeltelijk terugbetaald',
	Partner: 'Partner',
	Password: 'Wachtwoord',
	Past: 'Verleden',
	PastDateOverridesEmpty: 'Zodra het evenement is afgelopen, worden uw datumwijzigingen hier weergegeven',
	Pathologist: 'Patholoog',
	Patient: 'Geduldig',
	Pause: 'Pauze',
	Paused: 'Gepauzeerd',
	Pay: 'Betalen',
	PayMonthly: 'Betaal maandelijks',
	PayNow: 'Betaal nu',
	PayValue: 'Betaal {showPrice, select, true {{price}} other {nu}}',
	PayWithOtherCard: 'Betalen met een andere kaart',
	PayYearly: 'Betaal jaarlijks',
	PayYearlyPercentOff: 'Betaal jaarlijks <mark>{percent}% korting</mark>',
	Payer: 'Betaler',
	PayerClaimId: 'Betaalder claim ID',
	PayerCoverage: 'Dekking',
	PayerDetails: 'Gegevens van de betaler',
	PayerDetailsDescription:
		'Bekijk de gegevens van de betalers die aan uw account zijn toegevoegd en beheer de inschrijving.',
	PayerID: 'Betaler-ID',
	PayerId: 'Betaler-ID',
	PayerName: 'Naam betaler',
	PayerPhoneNumber: 'Telefoonnummer van de betaler',
	Payers: 'Betalers',
	Payment: 'Betaling',
	PaymentAccountUpdated: 'Uw account is bijgewerkt!',
	PaymentAccountUpgraded: 'Uw account is geüpgraded!',
	PaymentAmount: 'Betaalbedrag',
	PaymentDate: 'Betaaldatum',
	PaymentDetails: 'Betalingsgegevens',
	PaymentForUsersPerMonth: 'Betaling voor {billedUsers, plural, one {# gebruiker} other {# gebruikers}} per maand',
	PaymentInfoFormPrimaryText: 'Betalingsinformatie',
	PaymentInfoFormSecondaryText: 'Verzamel betalingsgegevens',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Deze factuur is reeds betaald.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Deze factuur is al in verwerking.',
	PaymentIntentAmountMismatchSnackbar:
		'Het totaalbedrag van de factuur is gewijzigd. Bekijk de wijzigingen voordat u betaalt.',
	PaymentIntentSyncTimeoutSnackbar:
		'Uw betaling is succesvol verlopen, maar er is een time-out opgetreden. Vernieuw de pagina en neem contact op met support als uw betaling niet wordt weergegeven.',
	PaymentMethod: 'Betaalmethode',
	PaymentMethodDescription:
		'Voeg uw praktijkbetaalmethode toe en beheer deze om het factureringsproces voor uw abonnementen te stroomlijnen.',
	PaymentMethodLabelBank: 'bankrekening',
	PaymentMethodLabelCard: 'kaart',
	PaymentMethodLabelFallback: 'betaalmethode',
	PaymentMethodRequired: 'Voeg een betaalmethode toe voordat u uw abonnement wijzigt',
	PaymentMethods: 'Betaalmethoden',
	PaymentProcessing: 'Betalingsverwerking!',
	PaymentProcessingFee: 'Betaling inclusief {amount} verwerkingskosten',
	PaymentReports: 'Betaalrapporten (ERA)',
	PaymentSettings: 'Betalingsinstellingen',
	PaymentSuccessful: 'Betaling succesvol!',
	PaymentType: 'Betaalmethode',
	Payments: 'Betalingen',
	PaymentsAccountDisabledNotificationSubject: `Online betalingen via {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} zijn uitgeschakeld.
Controleer je betalingsinstellingen om betalingen in te schakelen.`,
	PaymentsEmptyStateDescription: 'Er zijn geen betalingen gevonden.',
	PaymentsUnallocated: 'Niet-toegewezen betalingen',
	PayoutDate: 'Uitbetalingsdatum',
	PayoutsDisabled: 'Uitbetalingen uitgeschakeld',
	PayoutsEnabled: 'Uitbetalingen ingeschakeld',
	PayoutsStatus: 'Uitbetalingsstatus',
	Pediatrician: 'Kinderarts',
	Pen: 'Pen',
	Pending: 'In behandeling',
	People: '{rosterSize } mensen',
	PeopleCount: 'Mensen ({count})',
	PerMonth: '/ Maand',
	PerUser: 'Per gebruiker',
	Permission: 'Toestemming',
	PermissionRequired: 'Toestemming vereist',
	Permissions: 'Machtigingen',
	PermissionsClientAndContactDocumentation: 'Cliënt ',
	PermissionsClientAndContactProfiles: 'Cliënt ',
	PermissionsEditAccess: 'Bewerkingstoegang',
	PermissionsInvoicesAndPayments: 'Facturen ',
	PermissionsScheduling: 'Planning',
	PermissionsUnassignClients: 'Clients ongedaan maken',
	PermissionsUnassignClientsConfirmation: 'Weet u zeker dat u deze clients wilt verwijderen?',
	PermissionsValuesAssigned: 'Alleen toegewezen',
	PermissionsValuesEverything: 'Alles',
	PermissionsValuesNone: 'Geen',
	PermissionsValuesOwnCalendar: 'Eigen kalender',
	PermissionsViewAccess: 'Bekijk toegang',
	PermissionsWorkspaceSettings: 'Werkruimte-instellingen',
	Person: '{rosterSize} persoon',
	PersonalDetails: 'Persoonlijke gegevens',
	PersonalHealthcareHistoryStoreDescription:
		'Beantwoord en bewaar uw persoonlijke medische geschiedenis veilig op één plek',
	PersonalTrainer: 'Persoonlijke trainer',
	PersonalTraining: 'Persoonlijke training',
	PersonalizeWorkspace: 'Personaliseer uw werkruimte',
	PersonalizingYourWorkspace: 'Personaliseer uw werkplek',
	Pharmacist: 'Apotheker',
	Pharmacy: 'Apotheek',
	PhoneCall: 'Telefoongesprek',
	PhoneNumber: 'Telefoonnummer',
	PhoneNumberOptional: 'Telefoonnummer (optioneel)',
	PhotoBy: 'Foto door',
	PhysicalAddress: 'Fysiek adres',
	PhysicalTherapist: 'Fysiotherapeut',
	PhysicalTherapists: 'Fysiotherapeuten',
	PhysicalTherapy: 'Fysiotherapie',
	Physician: 'Arts',
	PhysicianAssistant: 'Arts-assistent',
	Physicians: 'Artsen',
	Physiotherapist: 'Fysiotherapeut',
	PlaceOfService: 'Plaats van dienst',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Rapport',
	PlanId: 'Plan-ID',
	PlansAndReportsCategoryDescription: 'Voor behandelplanning en het samenvatten van resultaten',
	PleaseRefreshThisPageToTryAgain: 'Gelieve deze pagina te vernieuwen om het opnieuw te proberen.',
	PleaseWait: 'Even geduld aub...',
	PleaseWaitForHostToJoin: 'Wachten tot de host zich aansluit...',
	PleaseWaitForHostToStart: 'Wacht tot de host deze vergadering start.',
	PlusAdd: '+ Toevoegen',
	PlusOthers: '+{count} anderen',
	PlusPlanInclusionFive: 'Gedeelde inboxen',
	PlusPlanInclusionFour: 'Groepsvideogesprekken',
	PlusPlanInclusionHeader: 'Alles in Essentieel  ',
	PlusPlanInclusionOne: 'Onbeperkte AI',
	PlusPlanInclusionSix: 'Aangepaste branding',
	PlusPlanInclusionThree: 'Groepsplanning',
	PlusPlanInclusionTwo: 'Onbeperkte opslag ',
	PlusSubscriptionPlanSubtitle: 'Voor praktijken om te optimaliseren en te groeien',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Politieagent',
	PolicyDates: 'Beleidsdata',
	PolicyHolder: 'Verzekeringnemer',
	PolicyHoldersAddress: 'Adres van de verzekerde',
	PolicyMemberId: 'Beleidslid-ID',
	PolicyStatus: 'Beleidsstatus',
	Popular: 'Populair',
	PortalAccess: 'Toegang tot portaal',
	PortalNoAppointmentsHeading: 'Houd alle komende en afgelopen afspraken bij',
	PortalNoDocumentationHeading: 'Maak en bewaar uw documenten veilig',
	PortalNoRelationshipsHeading: 'Breng de mensen samen die jouw reis steunen',
	PosCodeErrorMessage: 'POS-code is vereist',
	PosoNumber: 'PO/SO-nummer',
	PossibleClientDuplicate: 'Mogelijke client duplicaat',
	PotentialClientDuplicateTitle: 'Mogelijke dubbele cliëntengegevens',
	PotentialClientDuplicateWarning:
		'Deze clientinformatie bestaat mogelijk al in uw clientlijst. Controleer en update het bestaande record indien nodig of ga door met het aanmaken van een nieuwe client.',
	PoweredBy: 'Aangedreven door',
	Practice: 'Oefening',
	PracticeDetails: 'Oefendetails',
	PracticeInfoHeader: 'Bedrijfsinformatie',
	PracticeInfoPlaceholder: `Naam van de praktijk,
 Nationale provider-identificatie,
 Werkgeversidentificatienummer`,
	PracticeLocation: 'Het lijkt erop dat uw praktijk in',
	PracticeSettingsAvailabilityTab: 'Beschikbaarheid',
	PracticeSettingsBillingTab: 'Factureringsinstellingen',
	PracticeSettingsClientSettingsTab: 'Clientinstellingen',
	PracticeSettingsGeneralTab: 'Algemeen',
	PracticeSettingsOnlineBookingTab: 'Online boeken',
	PracticeSettingsServicesTab: 'Diensten',
	PracticeSettingsTaxRatesTab: 'Belastingtarieven',
	PracticeTemplate: 'Oefen sjabloon',
	Practitioner: 'Beoefenaar',
	PreferredLanguage: 'Voorkeurstaal',
	PreferredName: 'Voorkeursnaam',
	Prescription: 'Recept',
	PreventionSpecialist: 'Preventie Specialist',
	Preview: 'Voorvertoning',
	PreviewAndSend: 'Voorvertoning en verzenden',
	PreviewUnavailable: 'Voorbeeld niet beschikbaar voor dit bestandstype',
	PreviousNotes: 'Vorige notities',
	Price: 'Prijs',
	PriceError: 'Prijs moet groter zijn dan 0',
	PricePerClient: 'Prijs per klant',
	PricePerUser: 'Per gebruiker',
	PricePerUserBilledAnnually: 'Per gebruiker, jaarlijks gefactureerd',
	PricePerUserPerPeriod: '{price} per gebruiker / {isMonthly, select, true {maand} other {jaar}}',
	PricingGuide: 'Gids voor prijsplannen',
	PricingPlanPerMonth: '/ maand',
	PricingPlanPerYear: '/ jaar',
	Primary: 'Primair',
	PrimaryInsurance: 'Primaire verzekering',
	PrimaryPolicy: 'Primaire verzekering',
	PrimaryTimezone: 'Primaire tijdzone',
	Print: 'Afdrukken',
	PrintToCms1500: 'Afdrukken naar CMS1500',
	PrivatePracticeConsultant: 'Adviseur voor privépraktijken',
	Proceed: 'Ga door',
	ProcessAtTimeOfBookingDesc: 'Klanten moeten de volledige serviceprijs betalen om online te boeken',
	ProcessAtTimeOfBookingLabel: 'Betalingen verwerken op het moment van boeking',
	Processing: 'Verwerken',
	ProcessingFee: 'Verwerkingskosten',
	ProcessingFeeToolTip: `Met Carepatron kunt u de verwerkingskosten doorberekenen aan uw klanten.
 In sommige rechtsgebieden is het verboden om verwerkingskosten in rekening te brengen aan uw klanten. Het is uw verantwoordelijkheid om te voldoen aan de toepasselijke wetten.`,
	ProcessingRequest: 'Verzoek verwerken...',
	Product: 'Product',
	Profession: 'Beroep',
	ProfessionExample: 'Therapeut, voedingsdeskundige, tandarts',
	ProfessionPlaceholder: 'Begin met het typen van uw beroep of kies uit de lijst',
	ProfessionalPlanInclusion1: 'Onbeperkte opslag',
	ProfessionalPlanInclusion2: 'Onbeperkte taken',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 klantenservice',
	ProfessionalPlanInclusion5: 'SMS-herinneringen',
	ProfessionalPlanInclusionHeader: 'Alles in Starter, plus...',
	Professions: 'Beroepen',
	Profile: 'Profiel',
	ProfilePhotoFileSizeLimit: 'Bestandsgroottelimiet van 5 MB',
	ProfilePopoverSubTitle: 'U bent aangemeld als <strong>{email}</strong>',
	ProfilePopoverTitle: 'Uw werkplekken',
	PromoCode: 'Promotiecode',
	PromotionCodeApplied: '{promo} toegepast',
	ProposeNewDateTime: 'Stel een nieuwe datum/tijd voor',
	Prosthetist: 'Prothesist',
	Provider: 'Leverancier',
	ProviderBillingPlanExpansionManageButton: 'Plan beheren',
	ProviderCommercialNumber: 'Leverancier commercieel nummer',
	ProviderDetails: 'Gegevens van de provider',
	ProviderDetailsAddress: 'Adres',
	ProviderDetailsName: 'Naam',
	ProviderDetailsPhoneNumber: 'Telefoonnummer',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Sorry, deze provider heeft al een bestaand factureringsaccount',
	ProviderInfoPlaceholder: `Naam van het personeel,
 E-mailadres,
 Telefoonnummer,
 Nationale provider-identificatie,
 Licentienummer`,
	ProviderIsChargedProcessingFee: 'U betaalt de verwerkingskosten',
	ProviderPaymentFormBackButton: 'Rug',
	ProviderPaymentFormBillingAddressCity: 'Stad',
	ProviderPaymentFormBillingAddressCountry: 'Land',
	ProviderPaymentFormBillingAddressLine1: 'Lijn1',
	ProviderPaymentFormBillingAddressPostalCode: 'Postcode',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Creditcardgegevens',
	ProviderPaymentFormCardExpiry: 'Vervaldatum',
	ProviderPaymentFormCardHolderAddressTitle: 'Adres',
	ProviderPaymentFormCardHolderName: 'Naam van de kaarthouder',
	ProviderPaymentFormCardHolderTitle: 'Gegevens van de kaarthouder',
	ProviderPaymentFormCardNumber: 'Kaartnummer',
	ProviderPaymentFormPlanTitle: 'Gekozen plan',
	ProviderPaymentFormPlanTotalTitle: 'Totaal ({currency}):',
	ProviderPaymentFormSaveButton: 'Abonnement opslaan',
	ProviderPaymentFreePlanDescription:
		'Als u het gratis plan kiest, wordt de toegang van elk personeelslid tot hun cliënten in uw provider verwijderd. Uw toegang blijft echter behouden en u kunt het platform nog steeds gebruiken.',
	ProviderPaymentStepName: 'Beoordeling ',
	ProviderPaymentSuccessSnackbar: 'Geweldig! Uw nieuwe plan is succesvol opgeslagen.',
	ProviderPaymentTitle: 'Beoordeling ',
	ProviderPlanNetworkIdentificationNumber: 'Providerplannetwerkidentificatienummer',
	ProviderRemindersSettingsBannerAction: 'Ga naar Workflow Management',
	ProviderRemindersSettingsBannerDescription:
		'Vind alle herinneringen onder het nieuwe **Workflow Management** tabblad in **Instellingen**. Deze update brengt krachtige nieuwe functies, verbeterde sjablonen en slimmere automatiseringstools om uw productiviteit te verhogen. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Uw herinneringservaring wordt beter',
	ProviderTaxonomy: 'Leveranciers taxonomie',
	ProviderUPINNumber: 'Provider UPIN-nummer',
	ProviderUsedStoragePercentage: '{providerName} opslag is {usedStoragePercentage}% vol!',
	PsychiatricNursePractitioner: 'Psychiatrisch Verpleegkundig Specialist',
	Psychiatrist: 'Psychiater',
	Psychiatrists: 'Psychiaters',
	Psychiatry: 'Psychiatrie',
	Psychoanalyst: 'Psychoanalyticus',
	Psychologist: 'Psycholoog',
	Psychologists: 'Psychologen',
	Psychology: 'Psychologie',
	Psychometrician: 'Psychometrist',
	PsychosocialRehabilitationSpecialist: 'Specialist in psychosociale revalidatie',
	Psychotheraphy: 'Psychotherapie',
	Psychotherapists: 'Psychotherapeuten',
	Psychotherapy: 'Psychotherapie',
	PublicCallDialogTitle: 'Videogesprek met ',
	PublicCallDialogTitlePlaceholder: 'Videogesprek mogelijk gemaakt door Carepatron',
	PublicFormBackToForm: 'Dien een ander antwoord in',
	PublicFormConfirmSubmissionHeader: 'Bevestig indiening',
	PublicFormNotFoundDescription:
		'Het formulier waarnaar u zoekt is mogelijk verwijderd of de link is onjuist. Controleer de URL en probeer het opnieuw.',
	PublicFormNotFoundTitle: 'Formulier niet gevonden',
	PublicFormSubmissionError: 'Indienen mislukt. Probeer het opnieuw.',
	PublicFormSubmissionSuccess: 'Formulier succesvol verzonden',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} heeft {noteTitle} openbare formulier ingediend',
	PublicFormSubmittedSubtitle: 'Uw inzending is ontvangen.',
	PublicFormSubmittedTitle: 'Bedankt!',
	PublicFormVerifyClientEmailDialogSubtitle: 'We hebben een bevestigingscode naar uw e-mailadres verzonden',
	PublicFormsInvalidConfirmationCode: 'Ongeldige bevestigingscode',
	PublicHealthInspector: 'Inspecteur Volksgezondheid',
	PublicTemplates: 'Openbare sjablonen',
	Publish: 'Publiceren',
	PublishTemplate: 'Sjabloon publiceren',
	PublishTemplateFeatureBannerSubheader: 'Sjablonen die zijn ontworpen om de gemeenschap ten goede te komen',
	PublishTemplateHeader: 'Publiceer {title}',
	PublishTemplateToCommunity: 'Template publiceren voor de community',
	PublishToCommunity: 'Publiceren voor de community',
	PublishToCommunitySuccessMessage: 'Succesvol gepubliceerd in de community',
	Published: 'Gepubliceerd',
	PublishedBy: 'Gepubliceerd door {name}',
	PublishedNotesAreNotAutosaved: 'Gepubliceerde notities worden niet automatisch opgeslagen',
	PublishedOnCarepatronCommunity: 'Gepubliceerd op Carepatron community',
	Purchase: 'Aankoop',
	PushToCalendar: 'Push naar kalender',
	Question: 'Vraag',
	QuestionOrTitle: 'Vraag of titel',
	QuickActions: 'Snelle acties',
	QuickThemeSwitcherColorBasil: 'Basilicum',
	QuickThemeSwitcherColorBlueberry: 'Blauwe bes',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Mos',
	QuickThemeSwitcherColorRose: 'Roos',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Radiotherapeut',
	Radiologist: 'Radioloog',
	Read: 'Lezen',
	ReadOnly: 'Alleen-lezen',
	ReadOnlyAppointment: 'Lees alleen afspraak',
	ReadOnlyEventBanner:
		'Deze afspraak is gesynchroniseerd vanaf een alleen-lezen kalender en kan niet worden bewerkt.',
	ReaderMaxDepthHasBeenExceededCode:
		'Opmerking is te genest. Probeer de inspringing van sommige items ongedaan te maken.',
	ReadyForMapping: 'Klaar om in kaart te brengen',
	RealEstateAgent: 'Makelaar in onroerend goed',
	RearrangeClientFields: 'Clientvelden opnieuw ordenen in clientinstellingen',
	Reason: 'Reden',
	ReasonForChange: 'Reden voor verandering',
	RecentAppointments: 'Recente benoemingen',
	RecentServices: 'Recente diensten',
	RecentTemplates: 'Recente sjablonen',
	RecentlyUsed: 'Onlangs gebruikt',
	Recommended: 'Aanbevolen',
	RecommendedTemplates: 'Aanbevolen sjablonen',
	Recording: 'Opname',
	RecordingEnded: 'Opname beëindigd',
	RecordingInProgress: 'Opname bezig',
	RecordingMicrophoneAccessErrorMessage:
		'Geef uw browser toegang tot de microfoon en vernieuw de browser om de opname te starten.',
	RecurrenceCount: ', {count, plural, one {één keer} other {# keer}}',
	RecurrenceDaily: '{count, plural, one {Dagelijks} other {Dagen}}',
	RecurrenceEndAfter: 'Na',
	RecurrenceEndNever: 'Nooit',
	RecurrenceEndOn: 'Op',
	RecurrenceEvery: 'Elke {description}',
	RecurrenceMonthly: '{count, plural, one {Maandelijks} other {Maanden}}',
	RecurrenceOn: 'op {description}',
	RecurrenceOnAllDays: 'op alle dagen',
	RecurrenceUntil: 'tot {description}',
	RecurrenceWeekly: '{count, plural, one {Wekelijks} other {Weken}}',
	RecurrenceYearly: '{count, plural, one {Jaarlijks} other {Jaren}}',
	Recurring: 'Terugkerend',
	RecurringAppointment: 'Terugkerende afspraak',
	RecurringAppointmentsLimitedBannerText:
		'Niet alle terugkerende afspraken worden getoond. Verklein het datumbereik om alle terugkerende afspraken voor de periode te zien.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# evenement} other {# evenementen}}</b> worden gecreëerd op de volgende data',
	Redo: 'Opnieuw doen',
	ReferFriends: 'Verwijs vrienden',
	Reference: 'Referentie',
	ReferralCreditedNotificationSubject: 'Je referral credit van {currency} {amount} is toegepast',
	ReferralEmailDefaultBody: `Dankzij {name} heb je een GRATIS upgrade van 3 maanden naar Carepatron ontvangen. Sluit je aan bij onze community van meer dan 3 miljoen zorgprofessionals, gebouwd voor een nieuwe manier van werken!
Met vriendelijke groet,
Het Carepatron-team`,
	ReferralEmailDefaultSubject: 'U bent uitgenodigd om lid te worden van Carepatron',
	ReferralHasNotSignedUpDescription: 'Je vriend heeft zich nog niet aangemeld',
	ReferralHasSignedUpDescription: 'Uw vriend heeft zich aangemeld.',
	ReferralInformation: 'Verwijzingsinformatie',
	ReferralJoinedNotificationSubject: '{actorProfileName} is aangesloten bij Carepatron',
	ReferralListErrorDescription: 'De verwijzingslijst kon niet worden geladen.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {maand} other {maanden}}</b> actief',
	ReferralRewardBanner: 'Meld u aan en claim uw verwijzingsbeloning!',
	Referrals: 'Verwijzingen',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} maand {percentOff, select, 100 {gratis betaald} other {{percentOff}% korting}} {type, select, SubscriptionUpgrade {upgrade} other {}}',
	ReferredUserBenefitTitle: 'Ze snappen het!',
	Referrer: 'Verwijzer',
	ReferringProvider: 'Verwijzende aanbieder',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} credit wanneer <mark>3 vrienden</mark> activeren.',
	ReferringUserBenefitTitle: 'Jij krijgt het!',
	RefreshPage: 'Vernieuw Pagina',
	Refund: 'Terugbetaling',
	RefundAcknowledgement: 'Ik heb {clientName} buiten Carepatron terugbetaald.',
	RefundAcknowledgementValidationMessage: 'Bevestig dat u dit bedrag hebt terugbetaald',
	RefundAmount: 'Terug te betalen bedrag',
	RefundContent:
		'Terugbetalingen duren 7-10 dagen voordat ze op de rekening van uw klant verschijnen. Betalingskosten worden niet terugbetaald, maar er zijn geen extra kosten voor terugbetalingen. Terugbetalingen kunnen niet worden geannuleerd en sommige moeten mogelijk worden beoordeeld voordat ze worden verwerkt.',
	RefundCouldNotBeProcessed: 'Terugbetaling kon niet worden verwerkt',
	RefundError:
		'Deze terugbetaling kan op dit moment niet automatisch worden verwerkt. Neem contact op met Carepatron support om terugbetaling van deze betaling aan te vragen.',
	RefundExceedTotalValidationError: 'Het bedrag mag het totaal betaalde bedrag niet overschrijden',
	RefundFailed: 'Terugbetaling mislukt',
	RefundFailedTooltip:
		'Terugbetalen van deze betaling is eerder mislukt en kan niet opnieuw worden geprobeerd. Neem contact op met support.',
	RefundNonStripePaymentContent:
		'Deze betaling is gedaan met een methode buiten Carepatron (bijvoorbeeld contant, internetbankieren). Als u een terugbetaling uitvoert binnen Carepatron, worden er geen gelden teruggegeven aan de klant.',
	RefundReasonDescription:
		'Het toevoegen van een reden voor restitutie kan helpen bij het beoordelen van de transacties van uw klanten',
	Refunded: 'Terugbetaald',
	Refunds: 'Terugbetalingen',
	RefundsTableEmptyState: 'Geen restituties gevonden',
	Regenerate: 'Genereren',
	RegisterButton: 'Register',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Voornaam',
	RegisterLastName: 'Achternaam',
	RegisterPassword: 'Wachtwoord',
	RegisteredNurse: 'Gediplomeerd verpleegkundige',
	RehabilitationCounselor: 'Revalidatie-adviseur',
	RejectAppointmentFormTitle: 'Kunt u niet komen? Laat ons dan weten waarom en stel een nieuw tijdstip voor.',
	Rejected: 'Afgewezen',
	Relationship: 'Relatie',
	RelationshipDetails: 'Relatiegegevens',
	RelationshipEmptyStateTitle: 'Blijf in contact met degenen die uw cliënt ondersteunen',
	RelationshipPageAccessTypeColumnName: 'Profieltoegang',
	RelationshipSavedSuccessSnackbar: 'Relatie succesvol opgeslagen!',
	RelationshipSelectorFamilyAdmin: 'Familie',
	RelationshipSelectorFamilyMember: 'Familielid',
	RelationshipSelectorProviderAdmin: 'Providerbeheerder',
	RelationshipSelectorProviderStaff: 'Personeel van de aanbieder',
	RelationshipSelectorSupportNetworkPrimary: 'Vriend',
	RelationshipSelectorSupportNetworkSecondary: 'Ondersteuningsnetwerk',
	RelationshipStatus: 'Relatiestatus',
	RelationshipType: 'Relatie type',
	RelationshipTypeClientOwner: 'Cliënt',
	RelationshipTypeFamilyAdmin: 'Relaties',
	RelationshipTypeFamilyMember: 'Familie',
	RelationshipTypeFriendOrSupport: 'Vriend of ondersteuningsnetwerk',
	RelationshipTypeProviderAdmin: 'Providerbeheer',
	RelationshipTypeProviderStaff: 'Personeel',
	RelationshipTypeSelectorPlaceholder: 'Zoek relatie typen',
	Relationships: 'Relaties',
	Remaining: 'overig',
	RemainingTime: '{time} resterend',
	Reminder: 'Herinnering',
	ReminderColor: 'Herinneringskleur',
	ReminderDetails: 'Herinneringsdetails',
	ReminderEditDisclaimer: 'Wijzigingen worden alleen weerspiegeld in nieuwe afspraken',
	ReminderSettings: 'Instellingen voor afspraakherinneringen',
	Reminders: 'Herinneringen',
	Remove: 'Verwijderen',
	RemoveAccess: 'Toegang verwijderen',
	RemoveAllGuidesBtn: 'Verwijder alle hulplijnen',
	RemoveAllGuidesPopoverBody:
		'Wanneer u klaar bent met de onboardinggidsen, klikt u eenvoudig op de knop Gidsen verwijderen op elk paneel.',
	RemoveAllGuidesPopoverTitle: 'Hebt u uw onboardinggidsen niet meer nodig?',
	RemoveAsDefault: 'Verwijderen als standaard',
	RemoveAsIntake: 'Verwijderen als inname',
	RemoveCol: 'Kolom verwijderen',
	RemoveColor: 'Kleur verwijderen',
	RemoveField: 'Veld verwijderen',
	RemoveFromCall: 'Verwijderen uit oproep',
	RemoveFromCallDescription: 'Weet u zeker dat u <mark>{attendeeName}</mark> van deze videogesprek wilt verwijderen?',
	RemoveFromCollection: 'Verwijderen uit collectie',
	RemoveFromCommunity: 'Verwijderen uit de community',
	RemoveFromFolder: 'Verwijder uit map',
	RemoveFromFolderConfirmationDescription:
		'Weet u zeker dat u deze sjabloon uit deze map wilt verwijderen? Deze actie kan niet ongedaan worden gemaakt, maar u kunt ervoor kiezen om het later terug te verplaatsen.',
	RemoveFromIntakeDefault: 'Verwijderen uit inname standaard',
	RemoveGuides: 'Verwijder gidsen',
	RemoveMfaConfirmationDescription:
		'Het verwijderen van Multi-Factor Authentication (MFA) zal de veiligheid van uw account verminderen. Wilt u doorgaan?',
	RemoveMfaConfirmationTitle: 'MFA verwijderen?',
	RemovePaymentMethodDescription: `Hiermee wordt alle toegang en toekomstig gebruik van deze betaalmethode verwijderd.
 Deze actie kan niet ongedaan worden gemaakt.`,
	RemoveRow: 'Rij verwijderen',
	RemoveTable: 'Tabel verwijderen',
	RemoveTemplateAsDefaultIntakeSuccess: 'Succesvol verwijderd {templateTitle} als standaard intake sjabloon',
	RemoveTemplateFromCommunity: 'Sjabloon uit community verwijderen',
	RemoveTemplateFromFolder: '{templateTitle} succesvol verwijderd uit {folderTitle}',
	Rename: 'Hernoemen',
	RenderingProvider: 'Rendering-provider',
	Reopen: 'Heropenen',
	ReorderServiceGroupFailure: 'Het is niet gelukt om de collectie opnieuw te ordenen',
	ReorderServiceGroupSuccess: 'Collectie succesvol opnieuw geordend',
	ReorderServicesFailure: 'Het is niet gelukt om de services opnieuw te bestellen',
	ReorderServicesSuccess: 'Diensten succesvol opnieuw besteld',
	ReorderYourServiceList: 'Uw servicelijst opnieuw ordenen',
	ReorderYourServiceListDescription:
		'De manier waarop u uw diensten en collecties organiseert, wordt zichtbaar op uw online boekingspagina, zodat al uw klanten deze kunnen zien!',
	RepeatEvery: 'Herhaal elke',
	RepeatOn: 'Herhaal op',
	Repeating: 'Herhalen',
	Repeats: 'Herhalingen',
	RepeatsEvery: 'Herhaalt elke',
	Rephrase: 'Herformuleren',
	Replace: 'Vervangen',
	ReplaceBackground: 'Achtergrond vervangen',
	ReplacementOfPriorClaim: 'Vervanging van eerdere claim',
	Report: 'Rapport',
	Reprocess: 'Herverwerken',
	RepublishTemplateToCommunity: 'Herpublicatie sjabloon voor de gemeenschap',
	RequestANewVerificationLink: 'Vraag een nieuwe verificatielink aan',
	RequestCoverageReport: 'Vraag dekkingsrapport aan',
	RequestingDevicePermissions: 'Verzoek om apparaatmachtigingen...',
	RequirePaymentMethodDesc: 'Klanten moeten hun creditcardgegevens invoeren om online te boeken',
	RequirePaymentMethodLabel: 'Creditcardgegevens nodig',
	Required: 'vereist',
	RequiredField: 'Vereist',
	RequiredUrl: 'URL is verplicht.',
	Reschedule: 'Opnieuw plannen',
	RescheduleBookingLinkModalDescription: 'Via deze link kan uw klant de datum en tijd van zijn afspraak wijzigen.',
	RescheduleBookingLinkModalTitle: 'Link voor het opnieuw plannen van een boeking',
	RescheduleLink: 'Link voor opnieuw plannen',
	Resend: 'Opnieuw verzenden',
	ResendConfirmationCode: 'Bevestigingscode opnieuw verzenden',
	ResendConfirmationCodeDescription: 'Voer uw e-mailadres in en wij sturen u een nieuwe bevestigingscode per e-mail',
	ResendConfirmationCodeSuccess: 'Bevestigingscode is opnieuw verzonden, controleer uw inbox',
	ResendNewEmailVerificationSuccess: 'Nieuwe verificatielink is verzonden naar {email}',
	ResendVerificationEmail: 'Verificatie-e-mail opnieuw verzenden',
	Reset: 'Opnieuw instellen',
	Resources: 'Middelen',
	RespiratoryTherapist: 'Ademhalingstherapeut',
	RespondToHistoricAppointmentError:
		'Dit is een historische afspraak. Neem contact op met uw behandelaar als u vragen heeft.',
	Responder: 'Beantwoorder',
	RestorableItemModalDescription:
		'Weet u zeker dat u {context} wilt verwijderen?{canRestore, select, true { U kunt het later herstellen.} other {}}',
	RestorableItemModalTitle: 'Verwijder {type}',
	Restore: 'Herstellen',
	RestoreAll: 'Alles herstellen',
	Restricted: 'Beperkt',
	ResubmissionCodeReferenceNumber: 'Herinneringscode en referentienummer',
	Resubmit: 'Opnieuw indienen',
	Resume: 'Cv',
	Retry: 'Opnieuw proberen',
	RetryingConnectionAttempt: 'Poging tot herstellen van de verbinding... (Poging {retryCount} van {maxRetries})',
	ReturnToForm: 'Terug naar formulier',
	RevertClaimStatus: 'Claimstatus terugdraaien',
	RevertClaimStatusDescriptionBody:
		'Deze claim heeft gekoppelde betalingen en het wijzigen van de status kan gevolgen hebben voor het bijhouden of verwerken van betalingen, wat handmatige afstemming kan vereisen.',
	RevertClaimStatusDescriptionTitle: 'Weet je zeker dat je terug wilt naar {status}?',
	RevertClaimStatusError: 'Claimstatus kon niet worden teruggedraaid',
	RevertToDraft: 'Terug naar concept',
	Review: 'Beoordeling',
	ReviewsFirstQuote: 'Afspraken overal en op elk moment',
	ReviewsSecondJobTitle: 'Lifehouse-kliniek',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Ik ben ook dol op de carepatron-app. Helpt me om mijn cliënten en werk bij te houden terwijl ik onderweg ben.',
	ReviewsThirdJobTitle: 'Kliniek in de Baai van Manilla',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote:
		'Het gebruiksgemak en de mooie gebruikersinterface bezorgen mij elke dag een glimlach op mijn gezicht.',
	RightAlign: 'Rechts uitlijnen',
	Role: 'Rol',
	Roster: 'Aanwezigen',
	RunInBackground: 'Draai op de achtergrond',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'BSN',
	SafetyRedirectHeading: 'Je verlaat Carepatron',
	SafetyRedirectSubtext: 'Als u deze link vertrouwt, selecteert u deze om door te gaan',
	SalesRepresentative: 'Verkoopvertegenwoordiger',
	SalesTax: 'Omzetbelasting',
	SalesTaxHelp: 'Inclusief omzetbelasting op gegenereerde facturen',
	SalesTaxIncluded: 'Ja',
	SalesTaxNotIncluded: 'Nee',
	SaoPaulo: 'Sao Paulo',
	Saturday: 'Zaterdag',
	Save: 'Redden',
	SaveAndClose: 'Redden ',
	SaveAndExit: 'Redden ',
	SaveAndLock: 'Opslaan en vergrendelen',
	SaveAsDraft: 'Opslaan als concept',
	SaveCardForFuturePayments: 'Kaart opslaan voor toekomstige betalingen',
	SaveChanges: 'Wijzigingen opslaan',
	SaveCollection: 'Bewaar verzameling',
	SaveField: 'Veld opslaan',
	SavePaymentMethod: 'Betaalmethode opslaan',
	SavePaymentMethodDescription: 'Er worden pas kosten in rekening gebracht bij uw eerste afspraak.',
	SavePaymentMethodSetupError:
		'Er is een onverwachte fout opgetreden en we kunnen op dit moment geen betalingen configureren.',
	SavePaymentMethodSetupInvoiceLater: 'U kunt betalingen instellen en opslaan wanneer u uw eerste factuur betaalt.',
	SaveSection: 'Sectie opslaan',
	SaveService: 'Nieuwe service maken',
	SaveTemplate: 'Sjabloon opslaan',
	Saved: 'Opgeslagen',
	SavedCards: 'Opgeslagen kaarten',
	SavedPaymentMethods: 'Opgeslagen',
	Saving: 'Besparing...',
	ScheduleAppointmentsAndOnlineServices: 'Afspraken en online diensten inplannen',
	ScheduleName: 'Naam schema',
	ScheduleNew: 'Nieuw schema',
	ScheduleSend: 'Schema verzenden',
	ScheduleSendAlertInfo: 'Gesprekken die gepland staan, worden op het geplande tijdstip verzonden.',
	ScheduleSendByName: '<strong>Plannen verzenden</strong> • {time} door {displayName}',
	ScheduleSetupCall: 'Plan een setup gesprek',
	Scheduled: 'Gepland',
	SchedulingSend: 'Verzenden plannen',
	School: 'School',
	ScrollToTop: 'Scroll naar boven',
	Search: 'Zoekopdracht',
	SearchAndConvertToLanguage: 'Zoeken en converteren naar taal',
	SearchBasicBlocks: 'Zoek basisblokken',
	SearchByName: 'Zoeken op naam',
	SearchClaims: 'Zoek claims',
	SearchClientFields: 'Zoek clientvelden',
	SearchClients: 'Zoeken op klantnaam, klant-ID of telefoonnummer',
	SearchCommandNotFound: 'Geen resultaten gevonden voor "{searchTerm}"',
	SearchContacts: 'Klant of contactpersoon',
	SearchContactsPlaceholder: 'Contacten zoeken',
	SearchConversations: 'Zoek gesprekken',
	SearchInputPlaceholder: 'Doorzoek alle bronnen',
	SearchInvoiceNumber: 'Zoek factuurnummer',
	SearchInvoices: 'Facturen zoeken',
	SearchMultipleContacts: 'Klanten of contacten',
	SearchMultipleContactsOptional: 'Klanten of contacten (optioneel)',
	SearchOrCreateATag: 'Zoek of maak een tag',
	SearchPayments: 'Zoek betalingen',
	SearchPrepopulatedData: 'Zoek vooraf ingevulde gegevensvelden',
	SearchRelationships: 'Zoek relaties',
	SearchRemindersAndWorkflows: 'Zoek herinneringen en workflows',
	SearchServices: 'Zoekdiensten',
	SearchTags: 'Zoek tags',
	SearchTeamMembers: 'Zoek teamleden',
	SearchTemplatePlaceholder: 'Zoek {templateCount}+ bronnen',
	SearchTimezone: 'Zoek tijdzone...',
	SearchTrashItems: 'Zoek items',
	SearchUnsplashPlaceholder: `Zoek gratis foto's met hoge resolutie van Unsplash`,
	Secondary: 'Secundair',
	SecondaryInsurance: 'Secundaire verzekering',
	SecondaryPolicy: 'Secundaire verzekering',
	SecondaryTimezone: 'Secundaire tijdzone',
	Secondly: 'Ten tweede',
	Section: 'Sectie',
	SectionCannotBeEmpty: 'Een sectie moet minimaal één rij hebben',
	SectionFormSecondaryText: 'Sectietitel en beschrijving',
	SectionName: 'Sectienaam',
	Sections: 'Secties',
	SeeLess: 'Minder weergeven',
	SeeLessUpcomingAppointments: 'Minder aankomende afspraken bekijken',
	SeeMore: 'Bekijk meer',
	SeeMoreUpcomingAppointments: 'Bekijk meer aankomende afspraken',
	SeeTemplateLibrary: 'Zie sjabloonbibliotheek',
	Seen: 'Gezien',
	SeenByName: '<strong>Gezien</strong> • {time} door {displayName}',
	SelectAll: 'Selecteer alles',
	SelectAssignees: 'Selecteer toegewezen personen',
	SelectAttendees: 'Selecteer deelnemers',
	SelectCollection: 'Selecteer collectie',
	SelectCorrespondingAttributes: 'Selecteer overeenkomstige kenmerken',
	SelectPayers: 'Betalers selecteren',
	SelectProfile: 'Selecteer profiel',
	SelectServices: 'Selecteer diensten',
	SelectTags: 'Selecteer tags',
	SelectTeamOrCommunity: 'Selecteer team of community',
	SelectTemplate: 'Selecteer sjabloon',
	SelectType: 'Selecteer type',
	Selected: 'Gekozen',
	SelfPay: 'Zelf betalen',
	Send: 'Versturen',
	SendAndClose: 'Versturen ',
	SendAndStopIgnore: 'Verzenden en niet meer negeren',
	SendEmail: 'E-mail verzenden',
	SendIntake: 'Stuur inname',
	SendIntakeAndForms: 'Stuur inname ',
	SendMeACopy: 'Stuur mij een kopie',
	SendNotificationEmailWarning:
		'Sommige deelnemers hebben geen e-mailadres en zullen geen geautomatiseerde meldingen en herinneringen ontvangen.',
	SendNotificationLabel: 'Kies deelnemers om te informeren met een e-mail',
	SendOnlinePayment: 'Online betaling verzenden',
	SendOnlinePaymentTooltipTitleAdmin: 'Voeg uw voorkeursinstellingen voor uitbetaling toe',
	SendOnlinePaymentTooltipTitleStaff: 'Vraag de eigenaar van de provider om online betalingen in te stellen.',
	SendPaymentLink: 'Betalingslink verzenden',
	SendReaction: 'Stuur een reactie',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Verificatie-e-mail verzenden',
	SendingFailed: 'Verzenden mislukt',
	Sent: 'Verstuurd',
	SentByName: '<strong>Verstuurd</strong> • {time} door {displayName}',
	Seoul: 'Seoel',
	SeparateDuplicateClientsDescription:
		'De gekozen cliëntrecords blijven gescheiden van de rest, tenzij u ervoor kiest om ze samen te voegen',
	Service: 'Dienst',
	'Service/s': 'Dienst(en)',
	ServiceAdjustment: 'Service-aanpassing',
	ServiceAllowNewClientsIndicator: 'Nieuwe klanten toestaan',
	ServiceAlreadyExistsInCollection: 'Service bestaat al in collectie',
	ServiceBookableOnlineIndicator: 'Online te boeken',
	ServiceCode: 'Code',
	ServiceCodeErrorMessage: 'Er is een servicecode vereist',
	ServiceCodeSelectorPlaceholder: 'Voeg een servicecode toe',
	ServiceColour: 'Servicekleur',
	ServiceCoverageDescription:
		'Selecteer de diensten die in aanmerking komen en betaal uw eigen bijdrage voor deze verzekering.',
	ServiceCoverageGoToServices: 'Ga naar diensten',
	ServiceCoverageNoServicesDescription:
		'Pas de service co-pay bedragen aan om de standaard policy co-pay te overschrijven. Schakel dekking uit om te voorkomen dat services worden geclaimd tegen de polis.',
	ServiceCoverageNoServicesLabel: 'Er zijn geen diensten gevonden.',
	ServiceCoverageTitle: 'Servicedekking',
	ServiceDate: 'Datum van service',
	ServiceDetails: 'Servicedetails',
	ServiceDuration: 'Duur',
	ServiceEmptyState: 'Er zijn nog geen diensten',
	ServiceErrorMessage: 'Service is vereist',
	ServiceFacility: 'Servicefaciliteit',
	ServiceName: 'Servicenaam',
	ServiceRate: 'Tarief',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} voor {serviceReceiptNumber, select, undefined {gebruiker} other {{clientName}}} vereist aanvullende informatie',
	ServiceSalesTax: 'Omzetbelasting',
	ServiceType: 'Dienst',
	ServiceWorkerForceUIUpdateDialogDescription: `Klik op 'Vernieuwen' om te vernieuwen en de nieuwste Carepatron-updates te ontvangen.`,
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Opnieuw laden',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'U gebruikt een oudere versie',
	ServiceWorkerForceUIUpdateDialogTitle: 'Welkom terug!',
	Services: 'Diensten',
	ServicesAndAvailability: 'Diensten ',
	ServicesAndDiagnosisCodesHeader: 'Services en diagnosecodes toevoegen',
	ServicesCount: '{count,plural,=0{Diensten}one{Dienst}other{Diensten}}',
	ServicesPlaceholder: 'Diensten',
	ServicesProvidedBy: 'Dienst(en) geleverd door',
	SetAPhysicalAddress: 'Stel een fysiek adres in',
	SetAVirtualLocation: 'Stel een virtuele locatie in',
	SetAsDefault: 'Instellen als standaard',
	SetAsIntake: 'Instellen als inname',
	SetAsIntakeDefault: 'Instellen als standaard inname',
	SetAvailability: 'Beschikbaarheid instellen',
	SetTemplateAsDefaultIntakeSuccess: '{templateTitle} is succesvol ingesteld als standaard intake sjabloon',
	SetUpMfaButton: 'MFA instellen',
	SetYourLocation: 'Stel uw<mark> locatie</mark>',
	SetYourLocationDescription: 'Ik heb geen bedrijfsadres <span>(alleen online en mobiele diensten)</span>',
	SettingUpPayers: 'Betalers instellen',
	Settings: 'Instellingen',
	SettingsNewUserPasswordDescription:
		'Zodra u zich heeft aangemeld, sturen wij u een bevestigingscode die u kunt gebruiken om uw account te bevestigen',
	SettingsNewUserPasswordTitle: 'Meld je aan bij Carepatron',
	SettingsTabAutomation: 'Automatisering',
	SettingsTabBillingDetails: 'Factureringsgegevens',
	SettingsTabConnectedApps: 'Verbonden apps',
	SettingsTabCustomFields: 'Aangepaste velden',
	SettingsTabDetails: 'Details',
	SettingsTabInvoices: 'Facturen',
	SettingsTabLocations: 'Locaties',
	SettingsTabNotifications: 'Meldingen',
	SettingsTabOnlineBooking: 'Online boeken',
	SettingsTabPayers: 'Betalers',
	SettingsTabReminders: 'Herinneringen',
	SettingsTabServices: 'Diensten',
	SettingsTabServicesAndAvailability: 'Diensten en beschikbaarheid',
	SettingsTabSubscriptions: 'Abonnementen',
	SettingsTabWorkflowAutomations: 'Automatiseringen',
	SettingsTabWorkflowReminders: 'Basis herinneringen',
	SettingsTabWorkflowTemplates: 'Sjablonen',
	Setup: 'Opzetten',
	SetupGuide: 'Installatiehandleiding',
	SetupGuideAddServicesActionLabel: 'Begin',
	SetupGuideAddServicesSubtitle: '4 stappen • 2 min',
	SetupGuideAddServicesTitle: 'Voeg uw diensten toe',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Begin',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 stappen • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Online betalingen inschakelen',
	SetupGuideImportClientsActionLabel: 'Begin',
	SetupGuideImportClientsSubtitle: '4 stappen • 3 min',
	SetupGuideImportClientsTitle: 'Importeer uw klanten',
	SetupGuideImportTemplatesActionLabel: 'Begin',
	SetupGuideImportTemplatesSubtitle: '2 stappen • 1 min',
	SetupGuideImportTemplatesTitle: 'Importeer je sjablonen',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Begin',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 stappen • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personaliseer uw werkruimte',
	SetupGuideSetLocationActionLabel: 'Begin',
	SetupGuideSetLocationSubtitle: '4 stappen • 1 min',
	SetupGuideSetLocationTitle: 'Stel je locatie in',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Nodig team uit',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Nodig uw team uit om moeiteloos te communiceren en taken te beheren.',
	SetupGuideSuggestedAddTeamMembersTag: 'Instellen',
	SetupGuideSuggestedAddTeamMembersTitle: 'Teamleden toevoegen',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Aanpassen',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Voel je professioneel met je unieke logo en merk kleuren.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Kies uw merk',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Download',
	SetupGuideSuggestedDownloadMobileAppSubtitle: 'Toegang tot uw werkruimte, overal, altijd en op elk apparaat.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Instellen',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Download de app',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Beschikbaarheid instellen',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Voorkom dubbele boekingen door uw beschikbaarheid in te stellen.',
	SetupGuideSuggestedEditAvailabilityTag: 'Planning',
	SetupGuideSuggestedEditAvailabilityTitle: 'Beschikbaarheid bewerken',
	SetupGuideSuggestedImportClientsActionLabel: 'Importeren',
	SetupGuideSuggestedImportClientsSubtitle: 'Upload uw bestaande klantgegevens met één klik direct.',
	SetupGuideSuggestedImportClientsTag: 'Instellen',
	SetupGuideSuggestedImportClientsTitle: 'Klanten importeren',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Herinneringen bewerken',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Verminder het aantal no-shows met automatische afspraakherinneringen.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Gepersonaliseerde herinneringen',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Start gesprek',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Host een gesprek en maak contact met klanten met behulp van onze AI-aangedreven videotools.',
	SetupGuideSuggestedStartVideoCallTag: 'Telezorg',
	SetupGuideSuggestedStartVideoCallTitle: 'Start videogesprek',
	SetupGuideSuggestedTryActionsTitle: 'Dingen om te proberen 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Probeer AI-assistentie',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Krijg direct antwoord op al je werkvragen.',
	SetupGuideSuggestedUseAIAssistantTag: 'Nieuw',
	SetupGuideSuggestedUseAIAssistantTitle: 'Gebruik AI-assistent',
	SetupGuideSyncCalendarActionLabel: 'Begin',
	SetupGuideSyncCalendarSubtitle: '1 stap • minder dan 1 min',
	SetupGuideSyncCalendarTitle: 'Synchroniseer uw agenda',
	SetupGuideVerifyEmailLabel: 'Verifieer',
	SetupGuideVerifyEmailSubtitle: '2 stappen • 2 min',
	SetupOnlineStripePayments: 'Gebruik Stripe voor online betalingen',
	SetupPayments: 'Betalingen instellen',
	Sex: 'Seks',
	SexSelectorPlaceholder: 'Man / Vrouw / Liever niet zeggen',
	Share: 'Deel',
	ShareBookingLink: 'Boekingslink delen',
	ShareNoteDefaultMessage: `Hallo{name} heeft "{documentName}" met je gedeeld.

Bedankt,
{practiceName}`,
	ShareNoteMessage: `Hallo
{name} heeft "{documentName}" {isResponder, select, true {met enkele vragen voor u om in te vullen.} other {met u gedeeld.}}

Bedankt,
{practiceName}`,
	ShareNoteTitle: 'Deel ‘{noteTitle}’',
	ShareNotesWithClients: 'Delen met klanten of contacten',
	ShareScreen: 'Scherm delen',
	ShareScreenNotSupported: 'Uw apparaat/browser ondersteunt de functie scherm delen niet',
	ShareScreenWithId: 'Scherm {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Sta anderen toe om deze sjabloon te bekijken en in te dienen als formulier.',
	ShareTemplateAsPublicFormModalTitle: 'Deel link voor ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Openbare formulierconfiguratie succesvol bijgewerkt',
	ShareTemplateAsPublicFormSectionCustomization: 'Aanpassing',
	ShareTemplateAsPublicFormShowPoweredBy: 'Toon "Powered by Carepatron" op mijn formulier',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Toon/verberg "Powered by Carepatron" op mijn formulier',
	ShareTemplateAsPublicFormTrigger: 'Delen',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Gebruik werkruimtebranding',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Toon/verberg werkruimtebranding',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Stuurt code voor bestaande en niet-bestaande klanten',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Handtekeningen vereisen altijd verificatie van het e-mailadres',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Verstuurt alleen code voor bestaande klanten',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Stuurt nooit code',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Het selecteren van 'Nooit' kan ervoor zorgen dat ongeverifieerde gebruikers klantgegevens overschrijven als ze het e-mailadres van een bestaande klant gebruiken.`,
	ShareWithCommunity: 'Delen met de community',
	ShareYourReferralLink: 'Deel uw verwijzingslink',
	ShareYourScreen: 'Deel uw scherm',
	SheHer: 'Zij/Haar',
	ShortTextAnswer: 'Kort tekstantwoord',
	ShortTextFormPrimaryText: 'Korte tekst',
	ShortTextFormSecondaryText: 'Antwoord van minder dan 300 tekens',
	Show: 'Show',
	ShowColumn: 'Kolom weergeven',
	ShowColumnButton: 'Toon kolom {value} knop',
	ShowColumns: 'Kolommen weergeven',
	ShowColumnsMenu: 'Kolommenmenu weergeven',
	ShowDateDurationDescription: 'bijv. 29 jaar oud',
	ShowDateDurationLabel: 'Datumduur weergeven',
	ShowDetails: 'Details weergeven',
	ShowField: 'Veld weergeven',
	ShowFullAddress: 'Adres weergeven',
	ShowHideFields: 'Velden weergeven/verbergen',
	ShowIcons: 'Pictogrammen weergeven',
	ShowLess: 'Minder weergeven',
	ShowMeetingTimers: 'Toon vergadertimers',
	ShowMenu: 'Menu weergeven',
	ShowMergeSummarySidebar: 'Toon samenvatting van de samenvoeging',
	ShowMore: 'Meer weergeven',
	ShowOnTranscript: 'Toon op transcript',
	ShowReactions: 'Reacties tonen',
	ShowSection: 'Sectie weergeven',
	ShowServiceCode: 'Servicecode weergeven',
	ShowServiceDescription: 'Toon beschrijving bij serviceboekingen',
	ShowServiceDescriptionDesc: 'Klanten kunnen bij het boeken de beschrijvingen van de diensten bekijken',
	ShowServiceGroups: 'Collecties tonen',
	ShowServiceGroupsDesc: 'Bij het boeken worden de diensten aan klanten gegroepeerd per collectie getoond',
	ShowSpeakers: 'Toon sprekers',
	ShowTax: 'Belasting tonen',
	ShowTimestamp: 'Tijdstempel weergeven',
	ShowUnits: 'Toon eenheden',
	ShowWeekends: 'Toon weekenden',
	ShowYourView: 'Laat je mening zien',
	SignInWithApple: 'Aanmelden met Apple',
	SignInWithGoogle: 'Aanmelden met Google',
	SignInWithMicrosoft: 'Aanmelden met Microsoft',
	SignUpTitleReferralDefault: '<mark>Aanmelden</mark> en claim uw verwijzingsbeloning',
	SignUpTitleReferralUpgrade:
		'Start uw {durationInMonths} maanden <mark>{percentOff, select, 100 {gratis} other {{percentOff}% korting}} upgrade</mark>',
	SignatureCaptureError: 'Kan handtekening niet vastleggen. Probeer het opnieuw.',
	SignatureFormPrimaryText: 'Handtekening',
	SignatureFormSecondaryText: 'Ontvang een digitale handtekening',
	SignatureInfoTooltip: 'Deze visuele weergave is geen geldige elektronische handtekening.',
	SignaturePlaceholder: 'Teken hier uw handtekening',
	SignedBy: 'Ondertekend door',
	Signup: 'Aanmelden',
	SignupAgreements: 'Ik ga akkoord met de {termsOfUse} en het {privacyStatement} voor mijn account.',
	SignupBAA: 'Zakelijke partnerovereenkomst',
	SignupBusinessAgreements:
		'Namens mijzelf en de onderneming, ga ik akkoord met de {businessAssociateAgreement}, de {termsOfUse} en de {privacyStatement} voor mijn account.',
	SignupInvitationForYou: 'U bent uitgenodigd om Carepatron te gebruiken.',
	SignupPageProviderWarning:
		'Als uw beheerder al een account heeft aangemaakt, moet u hem/haar vragen u uit te nodigen voor die provider. Gebruik dit aanmeldingsformulier niet. Voor meer informatie, zie',
	SignupPageProviderWarningLink: 'deze link.',
	SignupPrivacy: 'Privacybeleid',
	SignupProfession: 'Welk beroep beschrijft u het beste?',
	SignupSubtitle:
		'De praktijkbeheersoftware van Carepatron is gemaakt voor solo-beoefenaars en teams. Stop met het betalen van buitensporige kosten en word onderdeel van de revolutie.',
	SignupSuccessDescription:
		'Bevestig uw e-mailadres om uw onboarding te starten. Als u het niet meteen ontvangt, controleer dan uw spamfolder.',
	SignupSuccessTitle: 'Controleer uw e-mail',
	SignupTermsOfUse: 'Gebruiksvoorwaarden',
	SignupTitleClient: '<mark>Beheer uw gezondheid</mark> van één plek',
	SignupTitleLast: 'en al het werk dat je doet! — Het is gratis',
	SignupTitleOne: '<mark>Je van stroom voorzien</mark> , ',
	SignupTitleThree: '<mark>Uw klanten van stroom voorzien</mark> , ',
	SignupTitleTwo: '<mark>Geef uw team kracht</mark> , ',
	Simple: 'Eenvoudig',
	SimplifyBillToDetails: 'Vereenvoudig de factuur tot in de details',
	SimplifyBillToHelperText: 'Alleen de eerste regel wordt gebruikt als deze overeenkomt met de client',
	Singapore: 'Singapore',
	Single: 'Enkel',
	SingleChoiceFormPrimaryText: 'Enkele keuze',
	SingleChoiceFormSecondaryText: 'Kies slechts één optie',
	Sister: 'Zus',
	SisterInLaw: 'Schoonzus',
	Skip: 'Overslaan',
	SkipLogin: 'Inloggen overslaan',
	SlightBlur: 'Maak je achtergrond iets vager',
	Small: 'Klein',
	SmartChips: 'Slimme chips',
	SmartDataChips: 'Slimme datachips',
	SmartReply: 'Slim antwoord',
	SmartSuggestNewClient: '<strong>Slimme Suggestie</strong> maak {name} aan als een nieuwe klant',
	SmartSuggestedFieldDescription: 'Dit veld is een slimme suggestie',
	SocialSecurityNumber: 'Sociaal-zekerheidsnummer',
	SocialWork: 'Maatschappelijk werk',
	SocialWorker: 'Maatschappelijk werker',
	SoftwareDeveloper: 'Softwareontwikkelaar',
	Solo: 'Solo',
	Someone: 'Iemand',
	Son: 'Zoon',
	SortBy: 'Sorteren op',
	SouthAmerica: 'Zuid-Amerika',
	Speaker: 'Spreker',
	SpeakerSource: 'Sprekerbron',
	Speakers: 'Sprekers',
	SpecifyPaymentMethod: 'Betaalmethode opgeven',
	SpeechLanguagePathology: 'Logopedie',
	SpeechTherapist: 'Logopedist',
	SpeechTherapists: 'Logopedisten',
	SpeechTherapy: 'Logopedie',
	SportsMedicinePhysician: 'Sportgeneeskundig arts',
	Spouse: 'Echtgenoot',
	SpreadsheetColumnExample: 'bijv. ',
	SpreadsheetColumns: 'Spreadsheetkolommen',
	SpreadsheetUploaded: 'Spreadsheet geüpload',
	SpreadsheetUploading: 'Uploaden...',
	Staff: 'Personeel',
	StaffAccessDescriptionAdmin: 'Beheerders kunnen alles op het platform beheren.',
	StaffAccessDescriptionStaff: `Medewerkers kunnen cliënten, notities en documentatie beheren die ze hebben gemaakt of die zijn gedeeld
 met hen afspraken inplannen en facturen beheren.`,
	StaffContactAssignedSubject:
		'{actorProfileName} heeft {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} en {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { en 1 andere cliënt} other { en # andere cliënten}} aan je toegewezen',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} heeft de {inboxName} inbox met je gedeeld',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} heeft je toegang tot de {inboxName} inbox verwijderd',
	StaffMembers: 'Medewerkers',
	StaffMembersNumber: '{billedUsers, plural, one {# teamlid} other {# teamleden}}',
	StaffSavedSuccessSnackbar: 'Informatie van teamleden succesvol opgeslagen!',
	StaffSelectorAdminRole: 'Beheerder',
	StaffSelectorStaffRole: 'Medewerker',
	StandardAppointment: 'Standaard afspraak',
	StandardColor: 'Taakkleur',
	StartAndEndTime: 'Start- en eindtijd',
	StartCall: 'Startgesprek',
	StartDate: 'Startdatum',
	StartDictating: 'Begin met dicteren',
	StartImport: 'Start import',
	StartRecordErrorTitle: 'Er is een fout opgetreden bij het starten van uw opname',
	StartRecording: 'Start opname',
	StartTimeIncrements: 'Starttijdsverhogingen',
	StartTimeIncrementsView: '{startTimeIncrements} min intervallen',
	StartTranscribing: 'Begin met transcriberen',
	StartTranscribingNotes:
		'Selecteer de cliënten waarvoor u een notitie wilt genereren. Klik vervolgens op de knop "Start Transcribing" om de opname te starten',
	StartTranscription: 'Begin transcriptie',
	StartVideoCall: 'Videogesprek starten',
	StartWeekOn: 'Begin de week op',
	StartedBy: 'Begonnen door ',
	Starter: 'Starter',
	State: 'Staat',
	StateIndustrialAccidentProviderNumber: 'Nummer van de leverancier van industriële ongevallen',
	StateLicenseNumber: 'Staatslicentienummer',
	Statement: 'Stelling',
	StatementDescriptor: 'Verklaringsbeschrijving',
	StatementDescriptorToolTip:
		'Statement descriptor wordt weergegeven op de bank- of creditcardafschriften van uw klanten. Het moet tussen de 5 en 22 tekens lang zijn en uw bedrijfsnaam weergeven.',
	StatementNumber: 'Verklaring #',
	Status: 'Staat',
	StatusFieldPlaceholder: 'Voer een statuslabel in',
	StepFather: 'Stiefvader',
	StepMother: 'Stiefmoeder',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription: `Door deze afzenders niet langer te negeren, worden toekomstige conversaties naar 'Inbox' gestuurd. Weet u zeker dat u deze afzenders niet langer wilt negeren?`,
	StopIgnoring: 'Stop met negeren',
	StopIgnoringSenders: 'Stop met het negeren van afzenders',
	StopIgnoringSendersSuccess: 'Stopte met het negeren van e-mailadres <mark>{addresses}</mark>',
	StopSharing: 'Stop met delen',
	StopSharingLabel: 'carepatron.com deelt uw scherm.',
	Storage: 'Opslag',
	StorageAlmostFullDescription: '🚀 Upgrade nu om ervoor te zorgen dat uw account soepel blijft werken.',
	StorageAlmostFullTitle: 'Je hebt {percentage}% van je werkruimte opslaglimiet gebruikt!',
	StorageFullDescription: 'Krijg meer opslagruimte door uw abonnement te upgraden.',
	StorageFullTitle: '	Je opslag is vol.',
	Street: 'Straat',
	StripeAccountNotCompleteErrorCode:
		'Online betalingen zijn niet {hasProviderName, select, true {ingesteld voor {providerName}} other {ingeschakeld voor deze provider}}.',
	StripeAccountRejectedError: 'Stripe-account is afgewezen. Neem contact op met support.',
	StripeBalance: 'Streepbalans',
	StripeChargesInfoToolTip: 'Hiermee kunt u debetkaarten opladen ',
	StripeFeesDescription:
		'Carepatron gebruikt Stripe om u snel betaald te krijgen en uw betalingsgegevens veilig te houden. Beschikbare betaalmethoden variëren per regio, alle grote debetkaarten ',
	StripeFeesDescriptionItem1: 'Verwerkingskosten worden toegepast op elke geslaagde transactie, u kunt {link}.',
	StripeFeesDescriptionItem2: 'Uitbetalingen vinden dagelijks plaats, maar worden maximaal 4 dagen vastgehouden.',
	StripeFeesLinkToRatesText: 'Bekijk hier onze tarieven',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Sorry, er is een minimum van {minimumAmount} vereist voor facturen die online betalingen gebruiken',
	StripePaymentsDisabled: 'Online betalingen uitgeschakeld. Controleer uw betalingsinstellingen.',
	StripePaymentsUnavailable: 'Betalingen niet beschikbaar',
	StripePaymentsUnavailableDescription:
		'Er is een fout opgetreden tijdens het laden van betalingen. Probeer het later opnieuw.',
	StripePayoutsInfoToolTip: 'Hiermee kunt u uw geld op uw bankrekening laten storten',
	StyleYourWorkspace: '<mark>Stijl</mark> je werkruimte',
	StyleYourWorkspaceDescription1:
		'We hebben de merkactiva van uw website gehaald. U kunt ze gerust bewerken of doorgaan naar uw Carepatron-werkruimte.',
	StyleYourWorkspaceDescription2:
		'Gebruik uw merkassets om facturen en online boekingen te personaliseren voor een naadloze klantervaring',
	SubAdvanced: 'Geavanceerd',
	SubEssential: 'Essentieel',
	SubOrganization: 'Organisatie',
	SubPlus: 'Plus',
	SubProfessional: 'Professioneel',
	Subject: 'Onderwerp',
	Submit: 'Indienen',
	SubmitElectronically: 'Elektronisch indienen',
	SubmitFeedback: 'Feedback indienen',
	SubmitFormValidationError: 'Zorg ervoor dat alle vereiste velden correct zijn ingevuld en probeer het opnieuw.',
	Submitted: 'Ingediend',
	SubmittedDate: 'Ingediende datum',
	SubscribePerMonth: 'Abonneren {price} {isMonthly, select, true {per maand} other {per jaar}}',
	SubscriptionDiscountDescription:
		'{percentOff}% korting {months, select, null { } other { {months, plural, one {voor # maand} other {voor # maanden}}}}',
	SubscriptionFreeTrialDescription: 'Gratis tot {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'We konden de betaling voor uw abonnement niet voltooien. Controleer uw betalingsgegevens.',
	SubscriptionPlanDetailsHeader: 'Per gebruiker/maandelijks jaarlijks gefactureerd',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} per maand gefactureerd (USD)',
	SubscriptionPlans: 'Abonnementsplannen',
	SubscriptionPlansDescription:
		'Upgrade uw plan om extra voordelen te ontgrendelen en uw praktijk soepel te laten verlopen.',
	SubscriptionPlansDescriptionNoPermission:
		'Het lijkt erop dat je op dit moment geen toegang hebt om te upgraden - neem contact op met je beheerder voor hulp.',
	SubscriptionSettings: 'Abonnementsinstellingen',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> van opslag gebruikt',
	SubscriptionSettingsStorageUsed: '{gebruikt} van {limiet} gebruikt',
	SubscriptionSettingsUnlimitedStorage: 'Onbeperkte opslag beschikbaar',
	SubscriptionSummary: 'Abonnementsoverzicht',
	SubscriptionUnavailableOverStorageLimit: 'Je huidige gebruik overschrijdt de opslaglimiet van dit abonnement.',
	SubscriptionUnpaidBannerButton: 'Ga naar abonnementen',
	SubscriptionUnpaidBannerDescription: 'Controleer of uw betalingsgegevens correct zijn en probeer het opnieuw.',
	SubscriptionUnpaidBannerTitle: 'Wij konden de betaling voor uw abonnement niet voltooien.',
	Subscriptions: 'Abonnementen',
	SubscriptionsAndPayments: 'Abonnementen ',
	Subtotal: 'Subtotaal',
	SuburbOrProvince: 'Voorstad/Provincie',
	SuburbOrState: 'Voorstad/Staat',
	SuccessSavedNoteChanges: 'Wijzigingen in notities zijn succesvol opgeslagen',
	SuccessShareDocument: 'Document succesvol gedeeld',
	SuccessShareNote: 'Notitie succesvol gedeeld',
	SuccessfullyCreatedValue: 'Succesvol gemaakt {value}',
	SuccessfullyDeletedTranscriptionPart: 'Transcriptiedeel succesvol verwijderd',
	SuccessfullyDeletedValue: 'Succesvol verwijderd {value}',
	SuccessfullySubmitted: 'Succesvol ingediend ',
	SuccessfullyUpdatedClientSettings: 'Clientinstellingen succesvol bijgewerkt',
	SuccessfullyUpdatedTranscriptionPart: 'Transcriptiegedeelte succesvol bijgewerkt',
	SuccessfullyUpdatedValue: 'Succesvol bijgewerkt {value}',
	SuggestedAIPoweredTemplates: 'Voorgestelde sjablonen met AI-ondersteuning',
	SuggestedAITemplates: 'Voorgestelde AI-sjablonen',
	SuggestedActions: 'Voorgestelde acties',
	SuggestedLocations: 'Voorgestelde locaties',
	Suggestions: 'Suggesties',
	Summarise: 'AI samenvatting',
	SummarisingContent: 'Samenvatten {title}',
	Sunday: 'Zondag',
	Superbill: 'Superrekening',
	SuperbillAndInsuranceBilling: 'Superrekening ',
	SuperbillAutomationMonthly: 'Actief • Laatste dag van de maand',
	SuperbillAutomationNoEmail:
		'Om geautomatiseerde factuurdocumenten succesvol te versturen, voegt u een e-mailadres toe voor deze klant',
	SuperbillAutomationNotActive: 'Niet actief',
	SuperbillAutomationUpdateFailure:
		'Het is niet gelukt om de automatiseringsinstellingen van Superbill bij te werken',
	SuperbillAutomationUpdateSuccess: 'Superbill-automatiseringsinstellingen succesvol bijgewerkt',
	SuperbillClientHelperText: 'Deze informatie wordt vooraf ingevuld vanuit de klantgegevens',
	SuperbillNotFoundDescription:
		'Neem contact op met uw provider en vraag om meer informatie of om de superbill opnieuw te versturen.',
	SuperbillNotFoundTitle: 'Superbill niet gevonden',
	SuperbillNumber: 'Superbill #{number}',
	SuperbillNumberAlreadyExists: 'Superbill-ontvangstnummer bestaat al',
	SuperbillPracticeHelperText:
		'Deze informatie wordt vooraf ingevuld vanuit de factureringsinstellingen van de praktijk',
	SuperbillProviderHelperText: 'Deze informatie wordt vooraf ingevuld vanuit de personeelsgegevens',
	SuperbillReceipts: 'Superbill-ontvangsten',
	SuperbillsEmptyStateDescription: 'Er zijn geen superbills gevonden.',
	Surgeon: 'Chirurg',
	Surgeons: 'Chirurgen',
	SurgicalTechnologist: 'Chirurgisch technoloog',
	SwitchFromAnotherPlatform: 'Ik stap over van een ander platform',
	SwitchToMyPortal: 'Overschakelen naar Mijn portaal',
	SwitchToMyPortalTooltip: `Krijg toegang tot uw eigen persoonlijke portaal,
 zodat u uw
 ervaring van de klant met het portaal.`,
	SwitchWorkspace: 'Werkruimte wisselen',
	SwitchingToADifferentPlatform: 'Overschakelen naar een ander platform',
	Sydney: 'Sydney',
	SyncCalendar: 'Agenda synchroniseren',
	SyncCalendarModalDescription: `Andere teamleden kunnen uw gesynchroniseerde agenda's niet zien. Cliëntafspraken kunnen alleen worden bijgewerkt of verwijderd vanuit Carepatron.`,
	SyncCalendarModalDisplayCalendar: 'Mijn agenda weergeven in Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Synchroniseer mijn agenda met Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Synchroniseer Carepatron-afspraken met mijn agenda',
	SyncCarepatronAppointmentsWithMyCalendar: 'Synchroniseer Carepatron afspraken met mijn agenda',
	SyncGoogleCalendar: 'Google Agenda synchroniseren',
	SyncInbox: 'Synchroniseer inbox met Carepatron',
	SyncMyCalendarToCarepatron: 'Synchroneer mijn agenda met Carepatron',
	SyncOutlookCalendar: 'Outlook-agenda synchroniseren',
	SyncedFromExternalCalendar: 'Gesynchroniseerd vanuit externe agenda',
	SyncingCalendarName: 'Kalender {calendarName} synchroniseren',
	SyncingFailed: 'Synchroniseren mislukt',
	SystemGenerated: 'Systeem gegenereerd',
	TFN: 'BSN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tafel',
	TableRowLabel: 'Tabelrij voor {value}',
	TagSelectorNoOptionsText: 'Klik op "nieuw maken" om een nieuwe tag toe te voegen',
	Tags: 'Labels',
	TagsInputPlaceholder: 'Tags zoeken of maken',
	Task: 'Taak',
	TaskAttendeeStatusUpdatedSuccess: 'Afsprakenstatussen succesvol bijgewerkt',
	Tasks: 'Taken',
	Tax: 'Belasting',
	TaxAmount: 'Belastingsbedrag',
	TaxID: 'BTW-nummer',
	TaxIdType: 'Belasting-ID-type',
	TaxName: 'Belastingnaam',
	TaxNumber: 'Belastingnummer',
	TaxNumberType: 'BTW-nummertype',
	TaxNumberTypeInvalid: '{type} is ongeldig',
	TaxPercentageOfAmount: '{taxName} ({percentage}% van {amount})',
	TaxRate: 'Belastingtarief',
	TaxRatesDescription: 'Beheer de belastingtarieven die op uw factuurregels worden toegepast.',
	Taxable: 'Belastbaar',
	TaxonomyCode: 'Taxonomiecode',
	TeacherAssistant: 'Leraar-assistent',
	Team: 'Team',
	TeamMember: 'Teamlid',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {is} other {are}} al geboekt op dit moment.{br}Kies een nieuw tijdstip om een dubbele boeking te voorkomen.',
	TeamMembers: 'Teamleden',
	TeamMembersColour: 'Teamleden kleuren',
	TeamMembersDetails: 'Gegevens van teamleden',
	TeamSize: 'Hoeveel mensen zitten er in jouw team?',
	TeamTemplates: 'Teamsjablonen',
	TeamTemplatesSectionDescription: 'Gemaakt door jou en jouw team',
	TelehealthAndVideoCalls: 'Telezorg ',
	TelehealthProvidedOtherThanInPatientCare: 'Telezorg wordt aangeboden voor andere doeleinden dan klinische zorg',
	TelehealthVideoCall: 'Telehealth-videogesprek',
	Template: 'Sjabloon',
	TemplateDescription: 'Sjabloonbeschrijving',
	TemplateDetails: 'Sjabloondetails',
	TemplateEditModeViewSwitcherDescription: 'Sjabloon maken en bewerken',
	TemplateGallery: 'Community-sjablonen',
	TemplateImportCompletedNotificationSubject: 'Sjabloonimport voltooid! {templateTitle} is klaar voor gebruik.',
	TemplateImportFailedNotificationSubject: 'Bestand {fileName} kon niet worden geïmporteerd.',
	TemplateName: 'Sjabloonnaam',
	TemplateNotFound: 'Sjabloon kon niet worden gevonden.',
	TemplatePreviewErrorMessage: 'Er is een fout opgetreden bij het laden van de sjabloonvoorbeeld',
	TemplateResponderModeViewSwitcherDescription: 'Voorvertoning en interactie met formulieren',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Controleer hoe uw formulieren eruit zien wanneer ze door respondenten worden ingevuld',
	TemplateSaved: 'Opgeslagen wijzigingen',
	TemplateTitle: 'Sjabloontitel',
	TemplateType: 'Sjabloontype',
	Templates: 'Sjabloon',
	TemplatesCategoriesFilter: 'Filteren op categorie',
	TemplatesPublicTemplatesFilter: ' Filteren op Community/Team',
	Text: 'Tekst',
	TextAlign: 'Tekstuitlijning',
	TextColor: 'Tekstkleur',
	ThankYouForYourFeedback: 'Bedankt voor uw feedback!',
	ThanksForLettingKnow: 'Bedankt dat u het ons laat weten.',
	ThePaymentMethod: 'De betaalmethode',
	ThemThey: 'Zij/Zij',
	Theme: 'Thema',
	ThemeAllColorsPickerTitle: `Meer thema's`,
	ThemeColor: 'Thema',
	ThemeColorDarkMode: 'Donker',
	ThemeColorLightMode: 'Licht',
	ThemeColorModePickerTitle: 'Kleurmodus',
	ThemeColorSystemMode: 'Systeem',
	ThemeCpColorPickerTitle: `Carepatron thema's`,
	ThemePanelDescription: 'Kies tussen licht en donker thema, en pas je themavoorkeuren aan',
	ThemePanelTitle: 'Uiterlijk',
	Then: 'Dan',
	Therapist: 'Therapeut',
	Therapists: 'Therapeuten',
	Therapy: 'Therapie',
	Thick: 'Dik',
	Thin: 'Dun',
	ThirdPerson: '3e persoon',
	ThisAndFollowingAppointments: 'Deze en volgende afspraken',
	ThisAndFollowingMeetings: 'Deze en volgende bijeenkomsten',
	ThisAndFollowingReminders: 'Deze en volgende herinneringen',
	ThisAndFollowingTasks: 'Deze en volgende taken',
	ThisAppointment: 'Deze afspraak',
	ThisMeeting: 'Deze bijeenkomst',
	ThisMonth: 'Deze maand',
	ThisPerson: 'Deze persoon',
	ThisReminder: 'Deze herinnering',
	ThisTask: 'Deze taak',
	ThisWeek: 'Deze week',
	ThreeDay: '3 dagen',
	Thursday: 'Donderdag',
	Time: 'Tijd',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}u',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Tijdformaat',
	TimeIncrement: 'Tijdsverhoging',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Tijdslotgrootte',
	Timestamp: 'Tijdstempel',
	Timezone: 'Tijdzone',
	TimezoneDisplay: 'Tijdzone weergave',
	TimezoneDisplayDescription: 'Beheer uw weergave-instellingen voor de tijdzone.',
	Title: 'Titel',
	To: 'Naar',
	ToYourWorkspace: 'naar uw werkplek',
	Today: 'Vandaag',
	TodayInHoursPlural: 'Vandaag in {count} {count, plural, one {uur} other {uren}}',
	TodayInMinsAbbreviated: 'Vandaag in {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Koptekstcel omschakelen',
	ToggleHeaderCol: 'Koptekstkolom omschakelen',
	ToggleHeaderRow: 'Koptekstrij omschakelen',
	Tokyo: 'Tokio',
	Tomorrow: 'Morgen',
	TomorrowAfternoon: 'Morgenmiddag',
	TomorrowMorning: 'Morgenochtend',
	TooExpensive: 'Te duur',
	TooHardToSetUp: 'Te moeilijk om op te zetten',
	TooManyFiles: 'Er is meer dan 1 bestand gedetecteerd.',
	ToolsExample: 'Eenvoudige oefening, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Totaal',
	TotalAccountCredit: 'Totaal tegoed op de rekening',
	TotalAdjustments: 'Totale aanpassingen',
	TotalAmountToCreditInCurrency: 'Totaal bedrag om te crediteren ({currency})',
	TotalBilled: 'Totaal gefactureerd',
	TotalConversations: '{total} {total, plural, =0 {gesprek} one {gesprek} other {gesprekken}}',
	TotalOverdue: 'Totaal te laat',
	TotalOverdueTooltip:
		'Het totale openstaande saldo omvat alle onbetaalde facturen, ongeacht de datum, die niet geannuleerd of verwerkt zijn.',
	TotalPaid: 'Totaal betaald',
	TotalPaidTooltip:
		'Het totaal betaalde saldo omvat alle bedragen van facturen die binnen het opgegeven datumbereik zijn betaald.',
	TotalUnpaid: 'Totaal onbetaald',
	TotalUnpaidTooltip:
		'Het totale onbetaalde saldo omvat alle openstaande bedragen van verwerkte, onbetaalde en verzonden facturen die binnen het opgegeven datumbereik verschuldigd zijn.',
	TotalWorkflows: '{count} {count, plural, one {workflow} other {workflows}}',
	TotpSetUpManualEntryInstruction:
		'U kunt er ook voor kiezen om de onderstaande code handmatig in de app in te voeren:',
	TotpSetUpModalDescription: 'Scan de QR-code met uw authenticator-app om Multi-Factor Authenticatie in te stellen.',
	TotpSetUpModalTitle: 'MFA-apparaat instellen',
	TotpSetUpSuccess: 'U bent klaar! MFA is ingeschakeld.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Voer de code in die door uw authenticator-app is gegenereerd',
	Transcribe: 'Transcriberen',
	TranscribeLanguageSelector: 'Selecteer invoertaal',
	TranscribeLiveAudio: 'Live audio transcriberen',
	Transcribing: 'Audio transcriberen...',
	TranscribingIn: 'Transcriberen in',
	Transcript: 'Transcriptie',
	TranscriptRecordingCompleteInfo: 'Zodra de opname is voltooid, ziet u hier uw transcriptie.',
	TranscriptSuccessSnackbar: 'Transcript is succesvol verwerkt.',
	Transcription: 'Transcriptie',
	TranscriptionEmpty: 'Geen transcriptie beschikbaar',
	TranscriptionEmptyHelperMessage:
		'Deze transcriptie heeft niets opgepikt. Start het opnieuw en probeer het opnieuw.',
	TranscriptionFailedNotice: 'Deze transcriptie is niet succesvol verwerkt',
	TranscriptionIdleMessage:
		'We horen geen geluid. Als je meer tijd nodig hebt, reageer dan binnen {timeValue} seconden, anders eindigt de sessie.',
	TranscriptionInProcess: 'Transcriptie in proces...',
	TranscriptionIncompleteNotice: 'Sommige delen van deze transcriptie zijn niet succesvol verwerkt',
	TranscriptionOvertimeWarning: '{scribeType} sessie eindigt in <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Weet u zeker dat u dit transcriptiedeel wilt verwijderen?',
	TranscriptionText: 'Stem naar tekst',
	TranscriptsPending: 'Uw transcriptie is hier beschikbaar nadat de sessie is afgelopen.',
	Transfer: 'Overdracht',
	TransferAndDelete: 'Overdragen en verwijderen',
	TransferOwnership: 'Eigendom overdragen',
	TransferOwnershipConfirmationModalDescription:
		'Deze actie kan alleen ongedaan worden gemaakt als zij het eigendom weer aan u overdragen.',
	TransferOwnershipDescription: 'Draag het eigendom van deze werkruimte over aan een ander teamlid.',
	TransferOwnershipSuccessSnackbar: 'Eigendom succesvol overgedragen!',
	TransferOwnershipToMember: 'Weet u zeker dat u deze werkruimte wilt overdragen naar {staff}?',
	TransferStatusAlert:
		'Het verwijderen van {numberOfStatuses, plural, one {deze status} other {deze statussen}} zal invloed hebben op {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} client status.</strong>} other {<strong>{numberOfAffectedRecords} client statussen.</strong>}}',
	TransferStatusDescription:
		'Kies een andere status voor deze clients voordat u doorgaat met verwijderen. Deze actie kan niet ongedaan worden gemaakt.',
	TransferStatusLabel: 'Overstappen naar nieuwe status',
	TransferStatusPlaceholder: 'Kies een bestaande status',
	TransferStatusTitle: 'Overdrachtsstatus vóór verwijdering',
	TransferTaskAttendeeStatusAlert:
		'Het verwijderen van deze status heeft invloed op <strong>{number} toekomstige afspraak {number, plural, one {status} other {statussen}}. </strong>',
	TransferTaskAttendeeStatusDescription:
		'Kies een andere status voor deze klanten voordat u doorgaat met het verwijderen. Deze actie kan niet ongedaan worden gemaakt.',
	TransferTaskAttendeeStatusSubtitle: 'Afspraakstatus',
	TransferTaskAttendeeStatusTitle: 'Transferstatus vóór verwijdering',
	Trash: 'Afval',
	TrashDeleteItemsModalConfirm: 'Om te bevestigen, typt {confirmationText}',
	TrashDeleteItemsModalDescription:
		'De volgende {count, plural, one {item} other {items}} worden permanent verwijderd en kunnen niet worden hersteld.',
	TrashDeleteItemsModalTitle: 'Verwijder {count, plural, one {item} other {items}} voor altijd',
	TrashDeletedAllItems: 'Alle items verwijderd',
	TrashDeletedItems: 'Verwijderd {count, plural, one {item} other {items}}',
	TrashDeletedItemsFailure: 'Items uit de prullenbak verwijderen is mislukt',
	TrashLocationAppointmentType: 'Kalender',
	TrashLocationBillingAndPaymentsType: 'Facturering & betalingen',
	TrashLocationContactType: 'Klanten',
	TrashLocationNoteType: 'Notities ',
	TrashRestoreItemsModalDescription: 'De volgende {count, plural, one {item} other {items}} worden hersteld.',
	TrashRestoreItemsModalTitle: 'Herstel {count, plural, one {item} other {items}}',
	TrashRestoredAllItems: 'Alle items hersteld',
	TrashRestoredItems: 'Gerestaureerde {count, plural, one {item} other {items}}',
	TrashRestoredItemsFailure: 'Items uit de prullenbak konden niet worden hersteld',
	TrashSuccessfullyDeletedItem: '{type} succesvol verwijderd',
	Trigger: 'Trekker',
	Troubleshoot: 'Problemen oplossen',
	TryAgain: 'Probeer het opnieuw',
	Tuesday: 'Dinsdag',
	TwoToTen: '2 - 10',
	Type: 'Type',
	TypeHere: 'Typ hier...',
	TypeToConfirm: 'Om te bevestigen, typ {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Kop 1',
	TypographyHeading2: 'Kop 2',
	TypographyHeading3: 'Kop 3',
	TypographyHeading4: 'Kop 4',
	TypographyHeading5: 'Kop 5',
	TypographyP: 'P',
	TypographyParagraph: 'Paragraaf',
	UnableToCompleteAction: 'Kan de actie niet voltooien.',
	UnableToPrintDocument: 'Kan document niet afdrukken. Probeer het later opnieuw.',
	Unallocated: 'Niet toegewezen',
	UnallocatedPaymentDescription: `Deze betaling is niet volledig toegewezen aan factureerbare items.
 U kunt een toewijzing doen aan onbetaalde items, of een tegoedbon of terugbetaling doen.`,
	UnallocatedPaymentTitle: 'Niet-toegewezen betaling',
	UnallocatedPayments: 'Niet-toegewezen betalingen',
	Unarchive: 'Uit archief halen',
	Unassigned: 'Niet toegewezen',
	UnauthorisedInvoiceSnackbar: 'U hebt geen toegang om facturen voor deze klant te beheren.',
	UnauthorisedSnackbar: 'U heeft hiervoor geen toestemming.',
	Unavailable: 'Niet beschikbaar',
	Uncategorized: 'Ongecategoriseerd',
	Unclaimed: 'Niet geclaimd',
	UnclaimedAmount: 'Niet opgeëist bedrag',
	UnclaimedItems: 'Niet-opgeëiste items',
	UnclaimedItemsMustBeInCurrency: `Alleen items in de volgende valuta's worden ondersteund: {currencies}`,
	Uncle: 'Oom',
	Unconfirmed: 'Niet bevestigd',
	Underline: 'Onderstrepen',
	Undo: 'Ongedaan maken',
	Unfavorite: 'Niet meer als favoriet markeren',
	Uninvoiced: 'Niet gefactureerd',
	UninvoicedAmount: 'Onbetaald bedrag',
	UninvoicedAmounts:
		'{count, plural, =0 {Geen ongefactureerde bedragen} one {Ongefactureerd bedrag} other {Ongefactureerde bedragen}}',
	Unit: 'Eenheid',
	UnitedKingdom: 'Verenigd Koninkrijk',
	UnitedStates: 'Verenigde Staten',
	UnitedStatesEast: 'Verenigde Staten - Oost',
	UnitedStatesWest: 'Verenigde Staten - West',
	Units: 'Eenheden',
	UnitsIsRequired: 'Eenheden zijn vereist',
	UnitsMustBeGreaterThanZero: 'Eenheden moeten groter zijn dan 0',
	UnitsPlaceholder: '1',
	Unknown: 'Onbekend',
	Unlimited: 'Onbeperkt',
	Unlock: 'Ontgrendelen',
	UnlockNoteHelper: 'Voordat redacteuren nieuwe wijzigingen kunnen aanbrengen, moeten ze de notitie ontgrendelen.',
	UnmuteAudio: 'Audio dempen',
	UnmuteEveryone: 'Iedereen dempen',
	Unpaid: 'Onbetaald',
	UnpaidInvoices: 'Onbetaalde facturen',
	UnpaidItems: 'Onbetaalde items',
	UnpaidMultiple: 'Onbetaald',
	Unpublish: 'Niet publiceren',
	UnpublishTemplateConfirmationModalPrompt:
		'Het verwijderen van <span>{title}</span> zal deze resource uit de Carepatron-gemeenschap verwijderen. Deze actie kan niet ongedaan gemaakt worden.',
	UnpublishToCommunitySuccessMessage: 'Succesvol ‛{title}’ verwijderd uit de community',
	Unread: 'Ongelezen',
	Unrecognised: 'Niet herkend',
	UnrecognisedDescription:
		'Deze betaalmethode wordt niet herkend door uw huidige applicatieversie. Vernieuw uw browser om de nieuwste versie te krijgen om deze betaalmethode te bekijken en te bewerken.',
	UnsavedChanges: 'Niet-opgeslagen wijzigingen',
	UnsavedChangesPromptContent: 'Wilt u uw wijzigingen opslaan voordat u sluit?',
	UnsavedChangesPromptTitle: 'U hebt niet-opgeslagen wijzigingen',
	UnsavedNoteChangesWarning: 'Wijzigingen die u hebt aangebracht, worden mogelijk niet opgeslagen',
	UnsavedTemplateChangesWarning: 'Wijzigingen die u hebt aangebracht, worden mogelijk niet opgeslagen',
	UnselectAll: 'Alles deselecteren',
	Until: 'Tot',
	UntitledConversation: 'Ongetitelde conversatie',
	UntitledFolder: 'Naamloze map',
	UntitledNote: 'Ongetitelde notitie',
	UntitledSchedule: 'Ongetitelde planning',
	UntitledSection: 'Sectie zonder titel',
	UntitledTemplate: 'Ongetitelde sjabloon',
	Unverified: 'Ongeverifieerd',
	Upcoming: 'Binnenkort',
	UpcomingAppointments: 'Aankomende afspraken',
	UpcomingDateOverridesEmpty: 'Er zijn geen datumoverrides gevonden',
	UpdateAvailabilityScheduleFailure: 'Beschikbaarheidsschema kan niet worden bijgewerkt',
	UpdateAvailabilityScheduleSuccess: 'Beschikbaarheidsschema succesvol bijgewerkt',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Wilt u dat de nieuwe prijzen worden toegepast op de facturen en claims van de deelnemers?',
	UpdateLink: 'Update link',
	UpdatePrimaryEmailWarningDescription:
		'Het wijzigen van het e-mailadres van uw klant zal ertoe leiden dat deze de toegang tot hun bestaande afspraken en notities verliest.',
	UpdatePrimaryEmailWarningTitle: 'E-mailadres van klant wijzigen',
	UpdateSettings: 'Instellingen bijwerken',
	UpdateStatus: 'Status bijwerken',
	UpdateSuperbillReceiptFailure: 'Het is niet gelukt om de Superbill-ontvangstbon bij te werken',
	UpdateSuperbillReceiptSuccess: 'Superbill-ontvangstbewijs succesvol bijgewerkt',
	UpdateTaskBillingDetails: 'Factureringsgegevens bijwerken',
	UpdateTaskBillingDetailsDescription:
		'De afspraakprijzen zijn gewijzigd. Wilt u dat de nieuwe prijzen worden toegepast op de factureringsitems, facturen en claims van de deelnemer? Kies de updates waarmee u wilt doorgaan.',
	UpdateTemplateFolderSuccessMessage: 'Map succesvol bijgewerkt',
	UpdateUnpaidInvoices: 'Onbetaalde facturen bijwerken',
	UpdateUserInfoSuccessSnackbar: 'Gebruikersinformatie is succesvol bijgewerkt!',
	UpdateUserSettingsSuccessSnackbar: 'Gebruikersinstellingen zijn succesvol bijgewerkt!',
	Upgrade: 'Upgrade',
	UpgradeForSMSReminder: 'Upgrade naar <b>Professional</b> voor onbeperkte sms-herinneringen',
	UpgradeNow: 'Nu upgraden',
	UpgradePlan: 'Upgrade-plan',
	UpgradeSubscriptionAlertDescription:
		'Je hebt bijna geen opslagruimte meer. Upgrade je abonnement om extra opslagruimte te ontgrendelen en je praktijk soepel te laten draaien!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Je hebt bijna geen opslagruimte meer. Vraag iemand in je praktijk met <span>Beheerdersrechten</span> om je abonnement te upgraden om extra opslagruimte te ontgrendelen en je praktijk soepel te laten draaien!',
	UpgradeSubscriptionAlertTitle: 'Het is tijd om uw abonnement te upgraden',
	UpgradeYourPlan: 'Upgrade uw abonnement',
	UploadAudio: 'Audio uploaden',
	UploadFile: 'Bestand uploaden',
	UploadFileDescription: 'Van welk softwareplatform stapt u over?',
	UploadFileMaxSizeError: 'Bestand is te groot. Maximale bestandsgrootte is {fileSizeLimit}.',
	UploadFileSizeLimit: 'Bestandsgrootte limiet {size}MB',
	UploadFileTileDescription: 'Gebruik CSV, XLS, XLSX, of ZIP bestanden om uw klanten te uploaden.',
	UploadFileTileLabel: 'Bestand uploaden',
	UploadFiles: 'Bestanden uploaden',
	UploadIndividually: 'Bestanden individueel uploaden',
	UploadLogo: 'Logo uploaden',
	UploadPhoto: 'Foto uploaden',
	UploadToCarepatron: 'Uploaden naar Carepatron',
	UploadYourLogo: 'Upload uw logo',
	UploadYourTemplates: 'Upload uw sjablonen en wij zullen ze voor u converteren',
	Uploading: 'Uploaden',
	UploadingAudio: 'Je audio uploaden...',
	UploadingFiles: 'Bestanden uploaden',
	UrlLink: 'URL-link',
	UsageCount: 'Gebruikt {count} keer',
	UsageLimitValue: '{gebruikt} van {limiet} gebruikt',
	UsageValue: '{gebruikt} gebruikt',
	Use: 'Gebruik',
	UseAiToAutomateYourWorkflow: 'Gebruik AI om uw workflow te automatiseren!',
	UseAsDefault: 'Gebruik als standaard',
	UseCustom: 'Gebruik aangepast',
	UseDefault: 'Standaard gebruiken',
	UseDefaultFilters: 'Standaardfilters gebruiken',
	UseTemplate: 'Gebruik sjabloon',
	UseThisCard: 'Gebruik deze kaart',
	UseValue: 'Gebruik "{waarde}"',
	UseWorkspaceDefault: 'Standaard werkruimte gebruiken',
	UserIsTyping: '{name} is aan het typen...',
	Username: 'Gebruikersnaam',
	Users: 'Gebruikers',
	VAT: 'VAT',
	ValidUrl: 'De URL-link moet een geldige URL zijn.',
	Validate: 'Valideren',
	Validated: 'Gevä̈ldigd',
	Validating: 'Valideren',
	ValidatingContent: 'Inhoud valideren...',
	ValidatingTranscripts: 'Validatie van transcripten...',
	ValidationConfirmPasswordRequired: 'Bevestig wachtwoord is vereist',
	ValidationDateMax: 'Moet vóór {max} zijn',
	ValidationDateMin: 'Moet na {min} zijn',
	ValidationDateRange: 'Start- en einddatum zijn vereist',
	ValidationEndDateMustBeAfterStartDate: 'Einddatum moet na startdatum zijn',
	ValidationMixedDefault: 'Dit is ongeldig',
	ValidationMixedRequired: 'Dit is vereist',
	ValidationNumberInteger: 'Moet een geheel getal zijn',
	ValidationNumberMax: 'Moet {max} of minder zijn',
	ValidationNumberMin: 'Moet {min} of meer zijn',
	ValidationPasswordNotMatching: 'Wachtwoorden komen niet overeen',
	ValidationPrimaryAddressIsRequired: 'Adres is vereist wanneer ingesteld als standaard',
	ValidationPrimaryPhoneNumberIsRequired: 'Telefoonnummer is vereist wanneer dit als standaard is ingesteld',
	ValidationServiceMustBeNotBeFuture: 'Service mag niet van vandaag of in de toekomst zijn',
	ValidationStringEmail: 'Moet een geldig e-mailadres zijn',
	ValidationStringMax: 'Moet {max} tekens of minder zijn',
	ValidationStringMin: 'Moet {min} of meer tekens zijn',
	ValidationStringPhoneNumber: 'Moet een geldig telefoonnummer zijn',
	ValueMinutes: '{value} minuten',
	VerbosityConcise: 'Beknopt',
	VerbosityDetailed: 'Gedetailleerd',
	VerbosityStandard: 'Standaard',
	VerbositySuperDetailed: 'Super gedetailleerd',
	VerificationCode: 'Verificatiecode',
	VerificationEmailDescription: 'Voer uw e-mailadres en de verificatiecode in die we u zojuist hebben gestuurd.',
	VerificationEmailSubtitle: 'Controleer de spamfolder - als de e-mail niet is aangekomen',
	VerificationEmailTitle: 'E-mailadres verifiëren',
	VerificationOption: 'Verificatie van e-mailadres',
	Verified: 'Geverifieerd',
	Verify: 'Verifiëren',
	VerifyAndSubmit: 'Verifiëren & verzenden',
	VerifyEmail: 'E-mailadres verifiëren',
	VerifyEmailAccessCode: 'Bevestigingscode',
	VerifyEmailAddress: 'E-mailadres verifiëren',
	VerifyEmailButton: 'Verifiëren en uitloggen',
	VerifyEmailSentSnackbar: 'Verificatie e-mail verzonden. Controleer uw inbox.',
	VerifyEmailSubTitle: 'Controleer de spamfolder als de e-mail niet is aangekomen',
	VerifyEmailSuccessLogOutSnackbar: 'Succes! Meld u af om de wijzigingen toe te passen.',
	VerifyEmailSuccessSnackbar: 'Succes! E-mail geverifieerd. Meld u aan om verder te gaan als geverifieerd account.',
	VerifyEmailTitle: 'Verifieer uw e-mailadres',
	VerifyNow: 'Nu verifiëren',
	Veterinarian: 'Dierenarts',
	VideoCall: 'Videogesprek',
	VideoCallAudioInputFailed: 'Audio-invoerapparaat werkt niet',
	VideoCallAudioInputFailedMessage:
		'Open de instellingen en controleer of je de microfoonbron correct hebt ingesteld.',
	VideoCallChatBanner:
		'Berichten zijn voor iedereen in het gesprek zichtbaar en worden verwijderd zodra het gesprek is beëindigd.',
	VideoCallChatSendBtn: 'Stuur een bericht',
	VideoCallChatTitle: 'Praten',
	VideoCallDisconnectedMessage: 'Je bent je netwerkverbinding kwijt. Probeer opnieuw te verbinden',
	VideoCallOptionInfo: 'Carepatron beheert videogesprekken voor uw afspraken als Zoom niet is verbonden',
	VideoCallTilePaused: 'Deze video is gepauzeerd vanwege problemen met uw netwerk',
	VideoCallTranscriptionFormDescription: 'U kunt deze instellingen op elk gewenst moment aanpassen',
	VideoCallTranscriptionFormHeading: 'Pas uw AI Scribe aan',
	VideoCallTranscriptionFormLanguageField: 'De gegenereerde uitvoertaal',
	VideoCallTranscriptionFormNoteTemplateField: 'Standaard notitiesjabloon instellen',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Geen sjablonen met AI gevonden',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Kies een sjabloon',
	VideoCallTranscriptionPronounField: 'Jouw voornaamwoorden',
	VideoCallTranscriptionRecordingNote:
		'Aan het einde van de sessie ontvang je een gegenereerde <strong>{noteTemplate} notitie</strong> en transcript.',
	VideoCallTranscriptionReferClientField: 'Verwijs naar de klant als',
	VideoCallTranscriptionReferPractitionerField: 'Verwijs naar beoefenaar als',
	VideoCallTranscriptionTitle: 'AI-schrijver',
	VideoCallTranscriptionVerbosityField: 'Breedsprakigheid',
	VideoCallTranscriptionWritingPerspectiveField: 'Schrijfperspectief',
	VideoCalls: 'Videogesprekken',
	VideoConferencing: 'Videoconferenties',
	VideoOff: 'Video is uit',
	VideoOn: 'Video is uit',
	VideoQual360: 'Lage kwaliteit (360p)',
	VideoQual540: 'Gemiddelde kwaliteit (540p)',
	VideoQual720: 'Hoge kwaliteit (720p)',
	View: 'Weergave',
	ViewAll: 'Bekijk alles',
	ViewAppointment: 'Afspraak bekijken',
	ViewBy: 'Bekijk op',
	ViewClaim: 'Bekijk claim',
	ViewCollection: 'Bekijk collectie',
	ViewDetails: 'Bekijk details',
	ViewEnrollment: 'Inschrijving bekijken',
	ViewPayment: 'Betaling bekijken',
	ViewRecord: 'Bekijk record',
	ViewRemittanceAdvice: 'Bekijk betalingsbewijs',
	ViewRemittanceAdviceHeader: 'Claim overschrijvingsbewijs',
	ViewRemittanceAdviceSubheader: 'Claim {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Instellingen bekijken',
	ViewStripeDashboard: 'Bekijk Stripe-dashboard',
	ViewTemplate: 'Sjabloon bekijken',
	ViewTemplates: 'Bekijk sjablonen',
	ViewableBy: 'Zichtbaar voor',
	ViewableByHelper:
		'U en het team hebben altijd toegang tot de notities die u publiceert. U kunt ervoor kiezen om deze notitie te delen met de klant en/of hun relaties',
	Viewer: 'kijker',
	VirtualLocation: 'Virtuele locatie',
	VisibleTo: 'Zichtbaar voor',
	VisitOurHelpCentre: 'Bezoek ons helpcentrum',
	VisualEffects: 'Visuele effecten',
	VoiceFocus: 'Stemfocus',
	VoiceFocusLabel: 'Filtert geluid uit uw microfoon dat geen spraak is',
	Void: 'Leegte',
	VoidCancelPriorClaim: 'Ongeldig maken/Annuleren eerdere claim',
	WaitingforMins: 'Wachten op {count} minuten',
	Warning: 'Waarschuwing',
	WatchAVideo: 'bekijk een video',
	WatchDemoVideo: 'Bekijk demovideo',
	WebConference: 'Webconferentie',
	WebConferenceOrVirtualLocation: 'Webconferentie / virtuele locatie',
	WebDeveloper: 'Webontwikkelaar',
	WebsiteOptional: 'Website <span>(optioneel)</span>',
	WebsiteUrl: 'Website-URL',
	Wednesday: 'Woensdag',
	Week: 'Week',
	WeekPlural: '{count, plural, one {week} other {weken}}',
	Weekly: 'Wekelijks',
	WeeksPlural: '{age, plural, one {# week} other {# weken}}',
	WelcomeBack: 'Welkom terug',
	WelcomeBackName: 'Welkom terug, {name}',
	WelcomeName: 'Welkom {name}',
	WelcomeToCarepatron: 'Welkom bij Carepatron',
	WhatCanIHelpWith: 'Waarmee kan ik helpen?',
	WhatDidYouLikeResponse: 'Wat vond je goed aan dit antwoord?',
	WhatIsCarepatron: 'Wat is Carepatron?',
	WhatMadeYouCancel: `Wat heeft je doen besluiten je plan te annuleren?
 Selecteer alle opties die van toepassing zijn.`,
	WhatServicesDoYouOffer: 'Wat<mark> diensten</mark> biedt u aan?',
	WhatServicesDoYouOfferDescription: 'U kunt later nog diensten bewerken of toevoegen.',
	WhatsYourAvailability: 'Wat is jouw <mark>beschikbaarheid?</mark>',
	WhatsYourAvailabilityDescription: `Je kunt later meer schema's toevoegen.`,
	WhatsYourBusinessName: 'Wat is jouw<mark> bedrijfsnaam?</mark>',
	WhatsYourTeamSize: 'Wat is jouw<mark> teamgrootte?</mark>',
	WhatsYourTeamSizeDescription: 'Hiermee kunnen wij uw werkplek correct inrichten.',
	WhenThisHappens: 'Wanneer dit gebeurt:',
	WhichBestDescribesYou: 'Welke is het beste<mark> beschrijft jou?</mark>',
	WhichPlatforms: 'Welke platformen?',
	Wife: 'Vrouw',
	WorkflowDescription: 'Beschrijving van de workflow',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Sjablonen kunnen worden gekoppeld aan workflows voor vlottere processen. Bekijk gekoppelde workflows om ze gemakkelijk te volgen en bij te werken.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Verbind uw SMS + e-mails op basis van gemeenschappelijke triggers',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Workflowautomatiseringen',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Geautomatiseerde workflows',
	WorkflowTemplateConfigKey_Body: 'Lichaam',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Toon branding',
	WorkflowTemplateConfigKey_Content: 'Inhoud',
	WorkflowTemplateConfigKey_Footer: 'Voettekst',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Toon voettekst',
	WorkflowTemplateConfigKey_Header: 'Header',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Toon koptekst',
	WorkflowTemplateConfigKey_SecurityFooter: 'Beveiligingsvoetnoot',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Toon beveiligingsvoetnoot',
	WorkflowTemplateConfigKey_Subject: 'Onderwerp',
	WorkflowTemplateConfigKey_Title: 'Titel',
	WorkflowTemplateDeleteConfirmationMessage:
		'Weet u zeker dat u deze sjabloon wilt verwijderen? Deze actie kan niet ongedaan gemaakt worden.',
	WorkflowTemplateDeleteConfirmationTitle: 'Verwijder meldingssjabloon',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Weet je het zeker? Dit verwijdert alleen de {locale}-versie – andere talen worden niet beïnvloed. Deze actie kan niet ongedaan gemaakt worden.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Verwijder de ‘{locale}’-sjabloon',
	WorkflowTemplateDeletedSuccess: 'Sjabloon voor notificatie succesvol verwijderd',
	WorkflowTemplateEditorDetailsTab: 'Sjabloon details',
	WorkflowTemplateEditorEmailContent: 'E-mailinhoud',
	WorkflowTemplateEditorEmailContentTab: 'E-mailinhoud',
	WorkflowTemplateEditorThemeTab: 'Thema',
	WorkflowTemplatePreviewerAlert:
		'Voorbeelden gebruiken voorbeeldgegevens om te laten zien wat uw klanten zullen zien.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Weet u het zeker? Dit zal de versie terugzetten naar de standaard sjabloon van het systeem. Deze actie kan niet ongedaan worden gemaakt.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Template resetten',
	WorkflowTemplateSendTestEmail: 'Stuur test e-mail',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Probeer je e-mailinstellingen uit door een testmail naar jezelf te sturen.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Ontvanger e-mailadres',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Stuur test',
	WorkflowTemplateSendTestEmailDialogTitle: 'Stuur een test e-mail',
	WorkflowTemplateSendTestEmailSuccess: 'Succes! Uw <mark>{templateName}</mark> test-e-mail is verzonden.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Beheer uw sjablonen en voeg meerdere taalversies toe om effectief met klanten te communiceren.',
	WorkflowTemplateTemplateEditor: 'Sjablooneditor',
	WorkflowTemplateTranslateLocaleError: 'Er is iets misgegaan tijdens het vertalen van de inhoud',
	WorkflowTemplateTranslateLocaleSuccess: 'Succesvol de content vertaald naar **{locale}**',
	WorkflowsAndReminders: 'Werkstromen ',
	WorkflowsManagement: 'Workflowbeheer',
	WorksheetAndHandout: 'Werkblaadje/Handout',
	WorksheetsAndHandoutsDescription: 'Voor klantbetrokkenheid en educatie',
	Workspace: 'Werkruimte',
	WorkspaceBranding: 'Werkplekbranding',
	WorkspaceBrandingDescription: `Geef uw werkplek moeiteloos een consistente stijl die uw merk weerspiegelt
 professionaliteit en persoonlijkheid. Pas facturen aan voor online boekingen voor een mooie
 klantervaring.`,
	WorkspaceName: 'Werkruimtenaam',
	Workspaces: 'Werkruimtes',
	WriteOff: 'Afschrijven',
	WriteOffModalDescription:
		'Je hebt <mark>{count} {count, plural, one {regel item} other {regel items}}</mark> om af te schrijven',
	WriteOffModalTitle: 'Afschrijvingsaanpassing',
	WriteOffReasonHelperText: 'Dit is een interne notitie en is niet zichtbaar voor uw klant.',
	WriteOffReasonPlaceholder:
		'Het toevoegen van een afschrijvingsreden kan helpen bij het beoordelen van factureerbare transacties',
	WriteOffTotal: 'Totale afschrijving ({currencyCode})',
	Writer: 'Schrijver',
	Yearly: 'Jaarlijks',
	YearsPlural: '{age, plural, one {# jaar} other {# jaren}}',
	Yes: 'Ja',
	YesArchive: 'Ja, archiveren',
	YesDelete: 'Ja, verwijderen',
	YesDeleteOverride: 'Ja, verwijder override',
	YesDeleteSection: 'Ja, verwijderen',
	YesDisconnect: 'Ja, verbreken',
	YesEnd: 'Ja, einde',
	YesEndTranscription: 'Ja, einde transcriptie',
	YesImFineWithThat: 'Ja, dat vind ik prima',
	YesLeave: 'Ja, ga weg',
	YesMinimize: 'Ja, minimaliseren',
	YesOrNoAnswerTypeDescription: 'Antwoordtype configureren',
	YesOrNoFormPrimaryText: 'Ja | Nee',
	YesOrNoFormSecondaryText: 'Kies ja of nee opties',
	YesProceed: 'Ja, ga door',
	YesRemove: 'Ja, verwijderen',
	YesRestore: 'Ja, herstellen',
	YesStopIgnoring: 'Ja, stop met negeren',
	YesTransfer: 'Ja, overdragen',
	Yesterday: 'Gisteren',
	YogaInstructor: 'Yoga-instructeur',
	You: 'Jij',
	YouArePresenting: 'Je presenteert',
	YouCanChooseMultiple: 'U kunt meerdere kiezen',
	YouCanSelectMultiple: 'U kunt meerdere selecteren',
	YouHaveOngoingTranscription: 'Je hebt een lopende transcriptie',
	YourAnswer: 'Jouw antwoord',
	YourDisplayName: 'Uw weergavenaam',
	YourSpreadsheetColumns: 'Kolommen in uw spreadsheet',
	YourTeam: 'Jouw team',
	ZipCode: 'Postcode',
	Zoom: 'Zoom',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Je kunt geen Zoom-oproep toevoegen voor dit teamlid. Raadpleeg de <a>ondersteuningsdocumenten voor meer informatie.</a>',
};

export default items;
