import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'gesaan AI',
	ATeamMemberIsRequired: 'Seorang ahli pasukan diperlukan',
	AboutClient: 'Mengen<PERSON> Pelanggan',
	AcceptAppointment: 'Terima kasih kerana mengesahkan pelantikan anda',
	AcceptTermsAndConditionsRequired: 'Terima Syarat ',
	Accepted: 'Diter<PERSON>',
	AccessGiven: 'Aks<PERSON> diberikan',
	AccessPermissions: 'Kebenaran akses',
	AccessType: '<PERSON>is akses',
	Accident: 'Kemalangan',
	Account: 'Akaun',
	AccountCredit: 'Kredit akaun',
	Accountant: 'Akauntan',
	Action: 'Tindakan',
	Actions: 'Tindakan',
	Active: 'Aktif',
	ActiveTags: 'Tag aktif',
	ActiveUsers: 'Pengguna aktif',
	Activity: 'Aktiviti',
	Actor: 'pelakon',
	Acupuncture: 'A<PERSON>punk<PERSON>',
	Acupuncturist: 'Ah<PERSON> akupunktur',
	Acupuncturists: 'Ahli akupunktur',
	AcuteManifestationOfAChronicCondition: 'Manifestasi akut keadaan kronik',
	Add: 'Tambah',
	AddADescription: 'Tambah penerangan',
	AddALocation: 'Tambah lokasi',
	AddASecondTimezone: 'Tambahkan zon waktu kedua',
	AddAddress: 'Tambahkan alamat',
	AddAnother: '  Tambah satu lagi',
	AddAnotherAccount: 'Tambah akaun lain',
	AddAnotherContact: 'Tambah kenalan lain',
	AddAnotherOption: 'Tambah pilihan lain',
	AddAnotherTeamMember: 'Tambah ahli pasukan yang lain',
	AddAvailablePayers: '+ Tambah pembayar yang tersedia',
	AddAvailablePayersDescription:
		'Carian pembayar untuk ditambah ke senarai pembayar ruang kerja anda. Selepas menambahnya, anda boleh menguruskan pendaftaran atau menyesuaikan butiran pembayar seperti yang diperlukan.',
	AddCaption: 'Tambah kapsyen',
	AddClaim: 'Tambah tuntutan',
	AddClientFilesModalDescription: 'Untuk menyekat akses, pilih pilihan dalam kotak pilihan "Boleh dilihat oleh".',
	AddClientFilesModalTitle: 'Muat naik fail untuk {name}',
	AddClientNoteButton: 'Tambah nota',
	AddClientNoteModalDescription:
		'Tambahkan kandungan pada nota anda. Gunakan bahagian "Boleh dilihat oleh" untuk memilih satu atau lebih kumpulan yang boleh melihat nota khusus ini.',
	AddClientNoteModalTitle: 'Tambah nota',
	AddClientOwnerRelationshipModalDescription:
		'Menjemput pelanggan akan membolehkan mereka mengurus maklumat profil mereka sendiri dan mengurus akses pengguna kepada maklumat profil mereka.',
	AddClientOwnerRelationshipModalTitle: 'Jemput pelanggan',
	AddCode: 'Tambah kod',
	AddColAfter: 'Tambahkan lajur selepas',
	AddColBefore: 'Tambahkan lajur sebelum',
	AddCollection: 'Tambah Koleksi',
	AddColor: 'Tambah warna',
	AddColumn: 'Tambah lajur',
	AddContactRelationship: 'Tambah perhubungan kenalan',
	AddContacts: 'Tambah kenalan',
	AddCustomField: 'Tambah medan tersuai',
	AddDate: 'Tambah tarikh',
	AddDescription: 'Tambah penerangan',
	AddDetail: 'Tambah butiran',
	AddDisplayName: 'Tambah nama paparan',
	AddDxCode: 'Tambah kod diagnosis',
	AddEmail: 'Tambah e-mel',
	AddFamilyClientRelationshipModalDescription:
		'Menjemput ahli keluarga akan membolehkan mereka melihat cerita penjagaan dan maklumat profil pelanggan. Jika mereka dijemput sebagai pentadbir, mereka akan mempunyai akses untuk mengemas kini maklumat profil pelanggan dan mengurus akses pengguna.',
	AddFamilyClientRelationshipModalTitle: 'Jemput ahli keluarga',
	AddField: 'Tambah medan',
	AddFormField: 'Tambah medan borang',
	AddImages: 'Tambah imej',
	AddInsurance: 'Tambah insurans',
	AddInvoice: 'Tambah invois',
	AddLabel: 'Tambah label',
	AddLanguage: 'Tambah bahasa',
	AddLocation: 'Tambah lokasi',
	AddManually: 'Tambah secara manual',
	AddMessage: 'Tambah mesej',
	AddNewAction: 'Tambah tindakan baharu',
	AddNewSection: 'Tambah bahagian baharu',
	AddNote: 'Tambah nota',
	AddOnlineBookingDetails: 'Tambahkan butiran tempahan dalam talian',
	AddPOS: 'Tambah POS',
	AddPaidInvoices: 'Tambah invois berbayar',
	AddPayer: 'Tambah pembayar',
	AddPayment: 'Tambah bayaran',
	AddPaymentAdjustment: 'Tambah pelarasan pembayaran',
	AddPaymentAdjustmentDisabledDescription: 'Peruntukan bayaran tidak akan diubah.',
	AddPaymentAdjustmentEnabledDescription: 'Jumlah yang tersedia untuk diperuntukkan akan dikurangkan.',
	AddPhoneNumber: 'Tambahkan nombor telefon',
	AddPhysicalOrVirtualLocations: 'Tambahkan lokasi fizikal atau maya',
	AddQuestion: 'Tambah soalan',
	AddQuestionOrTitle: 'Tambah soalan atau tajuk',
	AddRelationship: 'Tambah perhubungan',
	AddRelationshipModalTitle: 'Sambungkan kenalan sedia ada',
	AddRelationshipModalTitleNewClient: 'Sambung kenalan baharu',
	AddRow: 'Tambah baris',
	AddRowAbove: 'Tambahkan baris di atas',
	AddRowBelow: 'Tambah baris di bawah',
	AddService: 'Tambah perkhidmatan',
	AddServiceLocation: 'Tambah lokasi perkhidmatan',
	AddServiceToCollections: 'Tambahkan perkhidmatan pada koleksi',
	AddServiceToOneOrMoreCollections: 'Tambahkan perkhidmatan pada satu atau lebih koleksi',
	AddServices: 'Tambah perkhidmatan',
	AddSignature: 'Tambah tandatangan',
	AddSignaturePlaceholder: 'Taip butiran tambahan untuk disertakan bersama tandatangan anda',
	AddSmartDataChips: 'Tambah cip data pintar',
	AddStaffClientRelationshipsModalDescription:
		'Memilih kakitangan akan membolehkan mereka membuat dan melihat cerita penjagaan untuk pelanggan ini. Mereka juga akan dapat melihat maklumat pelanggan.',
	AddStaffClientRelationshipsModalTitle: 'Tambah perhubungan kakitangan',
	AddTag: 'Tambah tag',
	AddTags: 'Tambah tag',
	AddTemplate: 'Tambah templat',
	AddTimezone: 'Tambah zon waktu',
	AddToClaim: 'Tambahkan pada tuntutan',
	AddToCollection: 'Tambahkan pada koleksi',
	AddToExisting: 'Tambahkan pada sedia ada',
	AddToStarred: 'Tambahkan pada berbintang',
	AddUnclaimedItems: 'Tambah item yang tidak dituntut',
	AddUnrelatedContactWarning:
		'Anda telah menambah seseorang yang bukan kenalan {contact}. Pastikan kandungannya relevan sebelum meneruskan dengan perkongsian.',
	AddValue: 'Tambah "{value}"',
	AddVideoCall: 'Tambah panggilan video',
	AddVideoOrVoiceCall: 'Tambahkan panggilan video atau suara',
	AddictionCounselor: 'Kaunselor Ketagihan',
	AddingManualPayerDisclaimer:
		'Menambah pembayar secara manual pada senarai pembekal anda tidak menyediakan sambungan pemfailan tuntutan elektronik dengan pembayar tersebut, tetapi boleh digunakan untuk membuat tuntutan secara manual.',
	AddingTeamMembersIncreaseCostAlert: 'Menambah ahli pasukan baharu akan meningkatkan langganan bulanan anda.',
	Additional: 'Tambahan',
	AdditionalBillingProfiles: 'Profil pengebilan tambahan',
	AdditionalBillingProfilesSectionDescription:
		'Gantikan maklumat pengebilan lalai yang digunakan untuk ahli pasukan, pembayar atau templat invois tertentu.',
	AdditionalFeedback: 'Maklum balas tambahan',
	AddnNewWorkspace: 'Ruang kerja baharu',
	AddnNewWorkspaceSuccessSnackbar: 'Ruang kerja telah dibuat!',
	Address: 'Alamat',
	AddressNumberStreet: 'Alamat (Tidak, jalan)',
	Adjustment: 'Pelarasan',
	AdjustmentType: 'Jenis pelarasan',
	Admin: 'Admin',
	Admins: 'Pentadbir',
	AdminsOnly: 'Admin sahaja',
	AdvancedPlanInclusionFive: 'Pengurus akaun',
	AdvancedPlanInclusionFour: 'Analitis Google',
	AdvancedPlanInclusionHeader: 'Semuanya dalam Plus  ',
	AdvancedPlanInclusionOne: 'Peranan ',
	AdvancedPlanInclusionSix: 'Sokongan import data',
	AdvancedPlanInclusionThree: 'Pelabelan putih',
	AdvancedPlanInclusionTwo: 'Pengekalan data dipadamkan selama 90 hari',
	AdvancedPlanMessage:
		'Kekalkan kawalan terhadap keperluan amalan anda. Semak pelan semasa anda dan pantau penggunaan.',
	AdvancedSettings: 'Tetapan lanjutan',
	AdvancedSubscriptionPlanSubtitle: 'Kembangkan amalan anda dengan semua ciri',
	AdvancedSubscriptionPlanTitle: 'Maju',
	AdvertisingManager: 'Pengurus Pengiklanan',
	AerospaceEngineer: 'Jurutera Aeroangkasa',
	AgeYearsOld: '{age} tahun',
	Agenda: 'Agenda',
	AgendaView: 'Pandangan agenda',
	AiAskSupportedFileTypes: 'Jenis fail yang disokong: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Seorang pembantu di hujung jari anda',
	AiCopilotDisclaimer: 'AI Copilot boleh membuat kesilapan. Semak maklumat penting.',
	AiCreateNewConversation: 'Cipta perbualan baharu',
	AiEnhanceYourProductivity: 'Tingkatkan produktiviti anda',
	AiPoweredTemplates: 'Templat berkuasa AI',
	AiScribeNoDeviceFoundErrorMessage:
		'Nampaknya penyemak imbas anda tidak menyokong ciri ini atau tiada peranti yang serasi tersedia.',
	AiScribeUploadFormat: 'Jenis fail yang disokong: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'hanya 1 fail pada satu masa',
	AiShowConversationHistory: 'Tunjukkan sejarah perbualan',
	AiSmartPromptNodePlaceholderText:
		'Taipkan arahan tersuai anda di sini untuk membantu menjana keputusan AI yang tepat dan peribadi.',
	AiSmartPromptPrimaryText: 'Prompt pintar AI',
	AiSmartPromptSecondaryText: 'Masukkan arahan bijak AI tersuai',
	AiSmartReminders: 'Peringatan pintar AI',
	AiTemplateBannerTitle: 'Permudahkan kerja anda dengan templat berkuasa AI',
	AiTemplates: 'templat AI',
	AiTokens: 'Token AI',
	AiWorkBetterWithAi: 'Bekerja dengan lebih baik menggunakan AI',
	All: 'Semua',
	AllAppointments: 'Semua temu janji',
	AllCategories: 'Semua kategori',
	AllClients: 'Semua pelanggan',
	AllContactPolicySelectorLabel: 'Semua kenalan <mark>{client}</mark>',
	AllContacts: 'Semua kenalan',
	AllContactsOf: 'Semua kenalan ‘{name}’',
	AllDay: 'Sepanjang hari',
	AllInboxes: 'Semua Peti Masuk',
	AllIndustries: 'Semua industri',
	AllLocations: 'Semua lokasi',
	AllMeetings: 'Semua mesyuarat',
	AllNotificationsRestoredMessage: 'Semua pemberitahuan dipulihkan',
	AllProfessions: 'Semua profesion',
	AllReminders: 'Semua peringatan',
	AllServices: 'Semua perkhidmatan',
	AllStatuses: 'Semua status',
	AllTags: 'Semua tag',
	AllTasks: 'Semua tugasan',
	AllTeamMembers: 'Semua ahli pasukan',
	AllTypes: 'Semua jenis',
	Allocated: 'diperuntukkan',
	AllocatedItems: 'Item yang diperuntukkan',
	AllocationTableEmptyState: 'Tiada peruntukan pembayaran ditemui',
	AllocationTotalWarningMessage: `Amaun yang diperuntukkan melebihi jumlah pembayaran.
 Sila semak item baris di bawah.`,
	AllowClientsToCancelAnytime: 'Benarkan pelanggan membatalkan pada bila-bila masa',
	AllowNewClient: 'Benarkan untuk pelanggan baharu',
	AllowNewClientHelper: 'Pelanggan baharu boleh menempah perkhidmatan ini',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Benarkan sekurang-kurangnya {hours} jam sebelum temu janji',
	AllowToUseSavedCard: 'Benarkan {provider} untuk menggunakan kad yang disimpan di masa hadapan',
	AllowVideoCalls: 'Benarkan panggilan video',
	AlreadyAdded: 'Sudah ditambah',
	AlreadyHasAccess: 'Mempunyai akses',
	AlreadyHasAccount: 'Sudah mempunyai akaun?',
	Always: 'Sentiasa',
	AlwaysIgnore: 'Sentiasa abaikan',
	Amount: 'Jumlah',
	AmountDue: 'Jumlah yang perlu dibayar',
	AmountOfReferralRequests: '{amount, plural, one {# permintaan rujukan} other {# permintaan rujukan}}',
	AmountPaid: 'Jumlah yang dibayar',
	AnalyzingAudio: 'Menganalisis audio...',
	AnalyzingInputContent: 'Menganalisis kandungan input...',
	AnalyzingRequest: 'Menganalisis permintaan...',
	AnalyzingTemplateContent: 'Menganalisis kandungan templat...',
	And: 'and',
	Annually: 'Setiap Tahun',
	Anonymous: 'Tanpa Nama',
	AnswerExceeded: 'Jawapan anda mestilah kurang daripada 300 aksara.',
	AnyStatus: 'Apa-apa status',
	AppNotifications: 'Pemberitahuan',
	AppNotificationsClearanceHeading: 'kerja bagus! Anda telah mengosongkan semua aktiviti',
	AppNotificationsEmptyHeading: 'Aktiviti ruang kerja anda akan muncul di sini sebentar lagi',
	AppNotificationsEmptySubtext: 'Tiada tindakan yang perlu diambil buat masa ini',
	AppNotificationsIgnoredCount: '{total} diabaikan',
	AppNotificationsUnread: '{total} belum dibaca',
	Append: 'Lampirkan',
	Apply: 'Mohon',
	ApplyAccountCredit: 'Gunakan kredit akaun',
	ApplyDiscount: 'Guna diskaun',
	ApplyVisualEffects: 'Gunakan kesan visual',
	ApplyVisualEffectsNotSupported: 'Gunakan kesan visual yang tidak disokong',
	Appointment: 'Temujanji',
	AppointmentAssignedNotificationSubject: '{actorProfileName} telah menetapkan anda {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} telah membatalkan {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} telah mengesahkan {appointmentName}',
	AppointmentDetails: 'Butiran janji temu',
	AppointmentLocation: 'Lokasi temu janji',
	AppointmentLocationDescription:
		'Urus lokasi maya dan fizikal lalai anda. Apabila temujanji dijadualkan, lokasi ini akan digunakan secara automatik.',
	AppointmentNotFound: 'Janji temu tidak ditemui',
	AppointmentReminder: 'Peringatan janji temu',
	AppointmentReminders: 'Peringatan janji temu',
	AppointmentRemindersInfo:
		'Tetapkan peringatan automatik untuk janji temu pelanggan bagi mengelakkan ketidakhadiran dan pembatalan',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} telah menjadualkan semula {appointmentName}',
	AppointmentSaved: 'Janji temu disimpan',
	AppointmentStatus: 'Status pelantikan',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}jam {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}jam {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Janji temu dibatalkan',
	Appointments: 'Temujanji',
	Archive: 'Arkib',
	ArchiveClients: 'Arkibkan pelanggan',
	Archived: 'Diarkibkan',
	AreYouAClient: 'Adakah anda pelanggan?',
	AreYouStillThere: 'Adakah anda masih di sana?',
	AreYouSure: 'Adakah anda pasti?',
	Arrangements: 'Susunan',
	ArtTherapist: 'Jurupulih Seni',
	Articles: 'Artikel',
	Artist: 'Artis',
	AskAI: 'Tanya AI',
	AskAiAddFormField: 'Tambah medan borang',
	AskAiChangeFormality: 'Tukar formaliti',
	AskAiChangeToneToBeMoreProfessional: 'Tukar nada menjadi lebih profesional',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Terangkan tentang apa dokumen ini',
	AskAiExplainWhatThisImageIsAbout: 'Terangkan tentang maksud imej ini',
	AskAiFixSpellingAndGrammar: 'Betulkan ejaan dan tatabahasa',
	AskAiGenerateACaptionForThisImage: 'Hasilkan kapsyen untuk imej ini',
	AskAiGenerateFromThisPage: 'Hasilkan daripada halaman ini',
	AskAiGetStarted: 'Mulakan',
	AskAiGiveItAFriendlyTone: 'Beri nada mesra',
	AskAiGreeting: 'Hi {firstName}! Bagaimanakah saya boleh membantu anda hari ini?',
	AskAiHowCanIHelpWithYourContent: 'Bagaimanakah saya boleh membantu dengan kandungan anda?',
	AskAiInsert: 'Sisipkan',
	AskAiMakeItMoreCasual: 'Jadikan ia lebih santai',
	AskAiMakeThisTextMoreConcise: 'Jadikan teks ini lebih ringkas',
	AskAiMoreProfessional: 'Lebih profesional',
	AskAiOpenPreviousNote: 'Buka nota sebelumnya',
	AskAiPondering: 'Memikirkan',
	AskAiReplace: 'Gantikan',
	AskAiReviewOrEditSelection: 'Semak atau edit pilihan',
	AskAiRuminating: 'Merenung',
	AskAiSeeMore: 'Lihat lagi',
	AskAiSimplifyLanguage: 'Permudahkan bahasa',
	AskAiSomethingWentWrong:
		'Terdapat ralat. Sekiranya masalah ini berterusan, sila hubungi kami melalui pusat bantuan kami.',
	AskAiStartWithATemplate: 'Mulakan dengan templat',
	AskAiSuccessfullyCopiedResponse: 'Berjaya menyalin respons AI',
	AskAiSuccessfullyInsertedResponse: 'Berjaya memasukkan respons AI',
	AskAiSuccessfullyReplacedResponse: 'Berjaya menggantikan respons AI',
	AskAiSuggested: 'Dicadangkan',
	AskAiSummariseTextIntoBulletPoints: 'Ringkaskan teks menjadi titik peluru',
	AskAiSummarizeNote: 'Ringkaskan nota',
	AskAiThinking: 'Berfikir',
	AskAiToday: 'Hari ini {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Apa yang anda mahu lakukan dengan borang ini?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Tulis nota profesional menggunakan templat',
	AskAskAiAnything: 'Tanya AI apa sahaja',
	AskWriteSearchAnything: `Tanya, tulis '@' atau cari apa-apa...`,
	Asking: 'Bertanya',
	Assessment: 'Penilaian',
	Assessments: 'Penilaian',
	AssessmentsCategoryDescription: 'Untuk merakamkan penilaian pelanggan',
	AssignClients: 'Tugaskan pelanggan',
	AssignNewClients: 'Tugaskan pelanggan',
	AssignServices: 'Berikan perkhidmatan',
	AssignTeam: 'Tugaskan pasukan',
	AssignTeamMember: 'Tugaskan ahli pasukan',
	Assigned: 'Ditugaskan',
	AssignedClients: 'Pelanggan yang ditugaskan',
	AssignedServices: 'Perkhidmatan yang ditugaskan',
	AssignedServicesDescription:
		'Lihat dan urus perkhidmatan yang anda berikan, laraskan harga untuk menggambarkan kadar tersuai anda. ',
	AssignedTeam: 'Pasukan yang ditugaskan',
	AthleticTrainer: 'Jurulatih Olahraga',
	AttachFiles: 'Lampirkan fail',
	AttachLogo: 'Lampirkan',
	Attachment: 'Lampiran',
	AttachmentBlockedFileType: 'Disekat atas sebab keselamatan!',
	AttachmentTooLargeFileSize: 'Fail terlalu besar',
	AttachmentUploadItemComplete: 'lengkap',
	AttachmentUploadItemError: 'Muat naik gagal',
	AttachmentUploadItemLoading: 'Memuatkan',
	AttemptingToReconnect: 'Mencuba menyambung semula...',
	Attended: 'Dihadiri',
	AttendeeBeingMutedTooltip: `Hos telah meredamkan anda. Gunakan 'angkat tangan' untuk meminta nyahredam`,
	AttendeeWithId: 'Peserta {attendeeId}',
	Attendees: 'Hadirin',
	AttendeesCount: '{count} peserta',
	Attending: 'Menghadiri',
	Audiologist: 'Pakar audio',
	Aunt: 'makcik',
	Australia: 'Australia',
	AuthenticationCode: 'Kod Pengesahan',
	AuthoriseProvider: 'Benarkan {provider}',
	AuthorisedProviders: 'Pembekal yang diberi kuasa',
	AutoDeclineAllFutureOption: 'Hanya acara atau temu janji baharu',
	AutoDeclineAllOption: 'Acara atau temujanji baharu dan sedia ada',
	AutoDeclinePrimaryText: 'Secara automatik menolak acara',
	AutoDeclineSecondaryText: 'Acara yang berlaku semasa anda tidak berada di pejabat akan ditolak secara automatik',
	AutogenerateBillings: 'Autojana dokumen pengebilan',
	AutogenerateBillingsDescription:
		'Dokumen pengebilan automatik akan dijana pada hari terakhir bulan itu. Invois dan resit superbill boleh dibuat secara manual pada bila-bila masa.',
	AutomateWorkflows: 'Automatikkan Aliran Kerja',
	AutomaticallySendSuperbill: 'Hantar resit superbill secara automatik',
	AutomaticallySendSuperbillHelperText:
		'Superbill ialah resit terperinci perkhidmatan yang diberikan kepada pelanggan untuk pembayaran balik insurans',
	Automation: 'Automasi',
	AutomationActionSendEmailLabel: 'Hantar E-mel',
	AutomationActionSendSMSLabel: 'Hantar SMS',
	AutomationAndReminders: 'Automasi ',
	AutomationDeletedSuccessMessage: 'Berjaya memadamkan automasi',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Peristiwa masa',
	AutomationParams_timeUnit: 'Unit',
	AutomationParams_timeValue: 'Nombor',
	AutomationPublishSuccessMessage: 'Automasi berjaya diterbitkan',
	AutomationPublishWarningTooltip:
		'Sila semak semula konfigurasi automasi dan pastikan ia dikonfigurasikan dengan betul',
	AutomationTriggerEventCancelledDescription: 'Tercetus apabila acara dibatalkan atau dipadamkan',
	AutomationTriggerEventCancelledLabel: 'Acara dibatalkan',
	AutomationTriggerEventCreatedDescription: 'Tercetus apabila acara dibuat',
	AutomationTriggerEventCreatedLabel: 'Acara baru',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Mencetuskan apabila acara dibuat atau dikemas kini (kecuali apabila ia dibatalkan)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Acara baharu atau dikemas kini',
	AutomationTriggerEventEndedDescription: 'Tercetus apabila acara tamat',
	AutomationTriggerEventEndedLabel: 'Acara tamat',
	AutomationTriggerEventStartsDescription: 'Dicetuskan apabila jumlah masa tertentu sebelum acara bermula',
	AutomationTriggerEventStartsLabel: 'Acara bermula',
	Automations: 'Automasi',
	Availability: 'Ketersediaan',
	AvailabilityDisableSchedule: 'Lumpuhkan jadual',
	AvailabilityDisabled: 'Dilumpuhkan',
	AvailabilityEnableSchedule: 'Dayakan jadual',
	AvailabilityEnabled: 'Didayakan',
	AvailabilityNoActiveBanner:
		'Anda telah mematikan semua jadual anda. Pelanggan tidak boleh menempah anda dalam talian, dan semua janji temu akan datang perlu disahkan secara manual.',
	AvailabilityNoActiveConfirmationDescription:
		'Melumpuhkan ketersediaan ini akan menyebabkan tiada jadual aktif. Pelanggan tidak akan dapat menempah anda dalam talian dan sebarang tempahan yang dibuat oleh pengamal akan jatuh di luar waktu kerja anda.',
	AvailabilityNoActiveConfirmationProceed: 'Ya, teruskan',
	AvailabilityNoActiveConfirmationTitle: 'Tiada jadual aktif',
	AvailabilityToggle: 'Jadual didayakan',
	AvailabilityUnsetDate: 'Tiada tarikh ditetapkan',
	AvailableLocations: 'Lokasi yang tersedia',
	AvailablePayers: 'Pembayar yang ada',
	AvailablePayersEmptyState: 'Tiada pembayar dipilih',
	AvailableTimes: 'Masa yang tersedia',
	Back: 'belakang',
	BackHome: 'Balik rumah',
	BackToAppointment: 'Kembali ke Temujanji',
	BackToLogin: 'Kembali ke log masuk',
	BackToMapColumns: 'Kembali ke Lajur Peta',
	BackToTemplates: 'Kembali ke Templat',
	BackToUploadFile: 'Kembali ke Muat naik fail',
	Banker: 'Jurubank',
	BasicBlocks: 'Blok asas',
	BeforeAppointment: 'Hantar peringatan {deliveryType} {interval} {unit} sebelum temu janji',
	BehavioralAnalyst: 'Penganalisis Tingkah Laku',
	BehavioralHealthTherapy: 'Terapi kesihatan tingkah laku',
	Beta: 'Beta',
	BillTo: 'Bil kepada',
	BillableItems: 'Item yang boleh dibilkan',
	BillableItemsEmptyState: 'Tiada item yang boleh dibilkan telah ditemui',
	Biller: 'Pengebil',
	Billing: 'Pengebilan',
	BillingAddress: 'Alamat pengebilan',
	BillingAndReceiptsUnauthorisedMessage:
		'Akses paparan invois dan pembayaran diperlukan untuk mengakses maklumat ini.',
	BillingBillablesTab: 'Boleh dibilkan',
	BillingClaimsTab: 'Tuntutan',
	BillingDetails: 'Butiran pengebilan',
	BillingDocuments: 'Dokumen pengebilan',
	BillingDocumentsClaimsTab: 'Tuntutan',
	BillingDocumentsEmptyState: 'Tiada {tabType} dijumpai',
	BillingDocumentsInvoicesTab: 'Invois',
	BillingDocumentsSuperbillsTab: 'Superbills',
	BillingInformation: 'Maklumat pengebilan',
	BillingInvoicesTab: 'Invois',
	BillingItems: 'Item pengebilan',
	BillingPaymentsTab: 'Pembayaran',
	BillingPeriod: 'Tempoh pengebilan',
	BillingProfile: 'Profil pengebilan',
	BillingProfileOverridesDescription: 'Hadkan penggunaan profil bilangan ini kepada ahli pasukan tertentu',
	BillingProfileOverridesHeader: 'Hadkan akses',
	BillingProfileProviderType: 'Jenis pembekal',
	BillingProfileTypeIndividual: 'Pengamal',
	BillingProfileTypeIndividualSubLabel: 'Jenis 1 NPI',
	BillingProfileTypeOrganisation: 'Organisasi',
	BillingProfileTypeOrganisationSubLabel: 'Jenis 2 NPI',
	BillingProfiles: 'Profil pengebilan',
	BillingProfilesEditHeader: 'Edit {name} profil bil',
	BillingProfilesNewHeader: 'Profil pengebilan baharu',
	BillingProfilesSectionDescription:
		'Urus maklumat pengebilan anda untuk pengamal dan pembayar insurans dengan menyediakan profil pengebilan yang boleh digunakan pada invois dan pembayaran insurans.',
	BillingSearchPlaceholder: 'Cari item',
	BillingSettings: 'Tetapan pengebilan',
	BillingSuperbillsTab: 'Superbills',
	BiomedicalEngineer: 'Jurutera Bioperubatan',
	BlankInvoice: 'Invois kosong',
	BlueShieldProviderNumber: 'Nombor pembekal Blue Shield',
	Body: 'Badan',
	Bold: 'berani',
	BookAgain: 'Tempah lagi',
	BookAppointment: 'Tempah janji temu',
	BookableOnline: 'Boleh ditempah dalam talian',
	BookableOnlineHelper: 'Pelanggan boleh menempah perkhidmatan ini secara dalam talian',
	BookedOnline: 'Tempahan dalam talian',
	Booking: 'Tempahan',
	BookingAnalyticsIntegrationPanelDescription:
		'Sediakan Pengurus Google Tag untuk menjejak tindakan dan penukaran penting dalam aliran tempahan dalam talian anda. Kumpulkan data berharga tentang interaksi pengguna untuk meningkatkan usaha pemasaran dan mengoptimumkan pengalaman tempahan.',
	BookingAnalyticsIntegrationPanelTitle: 'Penyepaduan analitis',
	BookingAndCancellationPolicies: 'Tempahan ',
	BookingButtonEmbed: 'Butang',
	BookingButtonEmbedDescription: 'Menambah butang tempahan dalam talian pada tapak web anda',
	BookingDirectTextLink: 'Pautan teks langsung',
	BookingDirectTextLinkDescription: 'Membuka halaman tempahan dalam talian',
	BookingFormatLink: 'Format pautan',
	BookingFormatLinkButtonTitle: 'Tajuk butang',
	BookingInlineEmbed: 'Benam sebaris',
	BookingInlineEmbedDescription: 'Memuatkan halaman tempahan dalam talian terus di tapak web anda',
	BookingLink: 'Pautan tempahan',
	BookingLinkModalCopyText: 'salin',
	BookingLinkModalDescription:
		'Benarkan pelanggan dengan pautan ini menempah mana-mana ahli pasukan atau perkhidmatan',
	BookingLinkModalHelpText: 'Ketahui cara menyediakan tempahan dalam talian',
	BookingLinkModalTitle: 'Kongsi pautan tempahan anda',
	BookingPolicies: 'Polisi tempahan',
	BookingPoliciesDescription: 'Tetapkan masa tempahan dalam talian boleh dibuat oleh pelanggan',
	BookingTimeUnitDays: 'hari',
	BookingTimeUnitHours: 'jam',
	BookingTimeUnitMinutes: 'minit',
	BookingTimeUnitMonths: 'bulan',
	BookingTimeUnitWeeks: 'minggu',
	BottomNavBilling: 'Pengebilan',
	BottomNavGettingStarted: 'Rumah',
	BottomNavMore: 'Lagi',
	BottomNavNotes: 'Nota',
	Brands: 'Jenama',
	Brother: 'Abang',
	BrotherInLaw: 'Adik ipar',
	BrowseOrDragFileHere: '<link>Semak</link> atau seret fail di sini',
	BrowseOrDragFileHereDescription: 'PNG, JPG (maks. {limit})',
	BufferAfterTime: '{time} minit selepas',
	BufferAndLabel: 'dan',
	BufferAppointmentLabel: 'temu janji',
	BufferBeforeTime: '{time} minit sebelum',
	BufferTime: 'Masa penampan',
	BufferTimeViewLabel: '{bufferBefore} min sebelum dan {bufferAfter} min selepas temu janji',
	BulkArchiveClientsDescription:
		'Adakah anda pasti mahu mengarkibkan pelanggan ini? Anda boleh mengaktifkannya semula kemudian.',
	BulkArchiveSuccess: 'Berjaya mengarkibkan pelanggan',
	BulkArchiveUndone: 'Arkib pukal dibuat asal',
	BulkPermanentDeleteDescription: 'Ini akan memadamkan **{count} perbualan**. Tindakan ini tidak boleh dibatalkan.',
	BulkPermanentDeleteTitle: 'Padamkan perbualan selama-lamanya',
	BulkUnarchiveSuccess: 'Pelanggan yang berjaya diarkibkan',
	BulletedList: 'Senarai berbulet',
	BusinessAddress: 'Alamat perniagaan',
	BusinessAddressOptional: 'Alamat perniagaan <span>(Pilihan)</span>',
	BusinessName: 'Nama perniagaan',
	Button: 'Butang',
	By: 'Oleh',
	CHAMPUSIdentificationNumber: 'Nombor pengenalan CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC diperlukan',
	Calendar: 'Kalendar',
	CalendarAppSyncFormDescription: 'Segerakkan acara Carepatron ke',
	CalendarAppSyncPanelTitle: 'Penyegerakan apl yang disambungkan',
	CalendarDescription: 'Urus janji temu anda atau tetapkan tugas dan peringatan peribadi',
	CalendarDetails: 'Butiran kalendar',
	CalendarDetailsDescription: 'Urus tetapan paparan kalendar dan janji temu anda.',
	CalendarScheduleNew: 'Jadual baru',
	CalendarSettings: 'Tetapan kalendar',
	Call: 'Panggil',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> telah menyertai panggilan video',
	CallChangeLayoutTextContent: 'Pemilihan disimpan untuk mesyuarat akan datang',
	CallIdlePrompt:
		'Adakah anda lebih suka untuk terus menunggu untuk menyertai, atau adakah anda ingin mencuba lagi nanti?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Bar sisi',
	CallLayoutOptionSpotlight: 'Sorotan',
	CallLayoutOptionTiled: 'berjubin',
	CallNoAttendees: 'Tiada peserta dalam mesyuarat itu.',
	CallSessionExpiredError: 'Sesi tamat tempoh. Panggilan telah ditamatkan. Sila cuba menyertai semula.',
	CallWithPractitioner: 'Panggil dengan {practitioner}',
	CallsListCreateButton: 'Panggilan baharu',
	CallsListEmptyState: 'Tiada panggilan aktif',
	CallsListItemEndCall: 'Tamatkan panggilan',
	CamWarningMessage: 'Isu telah dikesan dengan kamera anda',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Sila dayakan akses Carepatron kepada kamera dan mikrofon anda.
 Untuk maklumat lanjut, <a>ikuti panduan ini</a>`,
	CameraAndMicIssueModalTitle: 'Kamera dan mikrofon disekat',
	CameraQuality: 'Kualiti kamera',
	CameraSource: 'Sumber kamera',
	CanModifyReadOnlyEvent: 'Anda tidak boleh mengubah acara ini',
	Canada: 'Kanada',
	Cancel: 'Batal',
	CancelClientImportDescription: 'Adakah anda pasti mahu membatalkan import ini?',
	CancelClientImportPrimaryAction: 'Ya, batalkan import',
	CancelClientImportSecondaryAction: 'Teruskan mengedit',
	CancelClientImportTitle: 'Batalkan pengimportan klien',
	CancelImportButton: 'Batalkan import',
	CancelPlan: 'Batalkan pelan',
	CancelPlanConfirmation: `Membatalkan pelan akan mengecaj akaun anda secara automatik dengan sebarang baki tertunggak yang anda ada untuk bulan ini.
 Jika anda ingin menurunkan taraf pengguna yang dibilkan anda, anda boleh mengalih keluar ahli pasukan dan Carepatron akan mengemas kini harga langganan anda secara automatik.`,
	CancelSend: 'Batalkan penghantaran',
	CancelSubscription: 'Batalkan langganan',
	Canceled: 'Dibatalkan',
	CancellationPolicy: 'Dasar pembatalan',
	Cancelled: 'Dibatalkan',
	CannotContainSpecialCharactersError: 'Tidak boleh mengandungi {specialCharacters}',
	CannotDeleteInvoice: 'Invois yang dibayar melalui pembayaran dalam talian tidak boleh dipadamkan',
	CannotMoveCarepatronStatusOutsideGroup:
		'<b>{status}</b> tidak boleh dialihkan keluar daripada kumpulan <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Perkhidmatan tidak boleh dialihkan ke luar koleksi',
	CapeTown: 'Cape Town',
	Caption: 'Kapsyen',
	CaptureNameFieldLabel: 'Nama yang anda ingin orang lain rujuk kepada anda',
	CapturePaymentMethod: 'Rakam kaedah pembayaran',
	CapturingAudio: 'Menangkap audio',
	CapturingSignature: 'Menangkap tandatangan...',
	CardInformation: 'Maklumat kad',
	CardNumberRequired: 'Nombor kad diperlukan',
	CardiacRehabilitationSpecialist: 'Pakar Pemulihan Jantung',
	Cardiologist: 'Pakar Kardiologi',
	CareAiNoConversations: 'Tiada perbualan lagi',
	CareAiNoConversationsDescription: 'Mulakan perbualan dengan {aiName} untuk memulakan',
	CareAssistant: 'Pembantu Penjagaan',
	CareManager: 'Pengurus Penjagaan',
	Caregiver: 'Pengasuh',
	CaregiverCreateModalDescription:
		'Menambah kakitangan sebagai pentadbir akan membolehkan mereka mencipta dan mengurus cerita penjagaan. Ia juga memberi mereka akses penuh untuk mencipta dan mengurus pelanggan.',
	CaregiverCreateModalTitle: 'Ahli pasukan baru',
	CaregiverListCantAddStaffInfoTitle:
		'Anda telah mencapai bilangan maksimum kakitangan untuk langganan anda. Sila tingkatkan rancangan anda untuk menambah lebih ramai kakitangan.',
	CaregiverListCreateButton: 'Ahli pasukan baru',
	CaregiverListEmptyState: 'Tiada penjaga ditambah',
	CaregiversListItemRemoveStaff: 'Keluarkan kakitangan',
	CarepatronApp: 'Aplikasi Carepatron',
	CarepatronCommunity: 'Komuniti',
	CarepatronFieldAddress: 'Alamat',
	CarepatronFieldAssignedStaff: 'Kakitangan yang ditugaskan',
	CarepatronFieldBirthDate: 'tarikh lahir',
	CarepatronFieldEmail: 'E-mel',
	CarepatronFieldEmploymentStatus: 'Status pekerjaan',
	CarepatronFieldEthnicity: 'Etnik',
	CarepatronFieldFirstName: 'nama pertama',
	CarepatronFieldGender: 'Jantina',
	CarepatronFieldIdentificationNumber: 'Nombor pengenalan',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Label',
	CarepatronFieldLastName: 'nama keluarga',
	CarepatronFieldLivingArrangements: 'Susunan hidup',
	CarepatronFieldMiddleNames: 'nama tengah',
	CarepatronFieldOccupation: 'pekerjaan',
	CarepatronFieldPhoneNumber: 'Nombor telefon',
	CarepatronFieldRelationshipStatus: 'Status perhubungan',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 status maks.',
	CarepatronFieldTags: 'Tag',
	CarepatronFields: 'Bidang Carepatron',
	Cash: 'Tunai',
	Category: 'kategori',
	CategoryInputPlaceholder: 'Pilih kategori templat',
	CenterAlign: 'Jajaran tengah',
	Central: 'Pusat',
	ChangeLayout: 'Tukar susun atur',
	ChangeLogo: 'Berubah',
	ChangePassword: 'Tukar kata laluan',
	ChangePasswordFailureSnackbar: 'Maaf, kata laluan anda tidak ditukar. Semak sama ada kata laluan lama anda betul.',
	ChangePasswordHelperInfo: 'Panjang minimum {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Berjaya menukar kata laluan! Lain kali anda log masuk, pastikan anda menggunakan kata laluan itu.',
	ChangeSubscription: 'Tukar langganan',
	ChangesNotAllowed: 'Perubahan tidak boleh dibuat pada medan ini',
	ChargesDisabled: 'Caj dilumpuhkan',
	ChargesEnabled: 'Caj didayakan',
	ChargesStatus: 'Status caj',
	ChartAndDiagram: 'Carta/Diagram',
	ChartsAndDiagramsCategoryDescription: 'Untuk menggambarkan data dan kemajuan pelanggan',
	ChatEditMessage: 'Edit mesej',
	ChatReplyTo: 'Balas kepada {name}',
	ChatTypeMessageTo: 'Mesej {name}',
	Check: 'Semak',
	CheckList: 'Senarai semak',
	Chef: 'Chef',
	Chiropractic: 'Kiropraktik',
	Chiropractor: 'Kiropraktor',
	Chiropractors: 'Kiropraktor',
	ChooseACollection: 'Pilih koleksi',
	ChooseAContact: 'Pilih kenalan',
	ChooseAccountTypeHeader: 'Mana yang paling menggambarkan anda?',
	ChooseAction: 'Pilih tindakan',
	ChooseAnAccount: 'Pilih akaun',
	ChooseAnOption: 'Pilih satu pilihan',
	ChooseBillingProfile: 'Pilih profil pengebilan',
	ChooseClaim: 'Pilih tuntutan',
	ChooseCollection: 'Pilih koleksi',
	ChooseColor: 'Pilih warna',
	ChooseCustomDate: 'Pilih tarikh tersuai',
	ChooseDateAndTime: 'Pilih tarikh dan masa',
	ChooseDxCodes: 'Pilih kod diagnosis',
	ChooseEventType: 'Pilih jenis acara',
	ChooseFileButton: 'Pilih fail',
	ChooseFolder: 'Pilih folder',
	ChooseInbox: 'Pilih peti masuk',
	ChooseMethod: 'Pilih kaedah',
	ChooseNewOwner: 'Pilih pemilik baharu',
	ChooseOrganization: 'Pilih Organisasi',
	ChoosePassword: 'Pilih kata laluan',
	ChoosePayer: 'Pilih pembayar',
	ChoosePaymentMethod: 'Pilih kaedah pembayaran',
	ChoosePhysicalOrRemoteLocations: 'Masukkan atau pilih lokasi',
	ChoosePlan: 'Pilih {plan}',
	ChooseProfessional: 'Pilih Profesional',
	ChooseServices: 'Pilih perkhidmatan',
	ChooseSource: 'Pilih sumber',
	ChooseSourceDescription:
		'Pilih dari mana anda ingin mengimport pelanggan – sama ada dari fail atau platform perisian lain.',
	ChooseTags: 'Pilih tag',
	ChooseTaxName: 'Pilih nama cukai',
	ChooseTeamMembers: 'Pilih ahli pasukan',
	ChooseTheme: 'Pilih tema',
	ChooseTrigger: 'Pilih pencetus',
	ChooseYourProvider: 'Pilih pembekal anda',
	CircularProgressWithLabel: '{value}%',
	City: 'Bandar',
	CivilEngineer: 'Jurutera Awam',
	Claim: 'Tuntutan',
	ClaimAddReferringProvider: 'Tambah penyedia rujukan',
	ClaimAddRenderingProvider: 'Tambah penyedia rendering',
	ClaimAmount: 'Jumlah tuntutan',
	ClaimAmountPaidHelpContent:
		'Jumlah yang dibayar adalah bayaran yang diterima daripada pesakit atau pembayar lain. Masukkan jumlah keseluruhan yang dibayar oleh pesakit dan/atau pembayar lain untuk perkhidmatan yang dilindungi sahaja.',
	ClaimAmountPaidHelpSubtitle: 'Padang 29',
	ClaimAmountPaidHelpTitle: 'Jumlah yang dibayar',
	ClaimBillingProfileTypeIndividual: 'individu',
	ClaimBillingProfileTypeOrganisation: 'Organisasi',
	ClaimChooseRenderingProviderOrTeamMember: 'Pilih penyedia rendering atau ahli pasukan',
	ClaimClientInsurancePolicies: 'Polisi insurans pelanggan',
	ClaimCreatedAction: '<mark>Tuntutan {claimNumber}</mark> telah diwujudkan',
	ClaimDeniedAction: '<mark>Tuntutan {claimNumber}</mark> telah ditolak oleh <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Cari kod diagnosis ICD 10',
	ClaimDiagnosisSelectorHelpContent: `"Diagnosis atau kecederaan" ialah tanda, simptom, aduan, atau keadaan pesakit yang berkaitan dengan perkhidmatan pada tuntutan.
 Sehingga 12 kod diagnosis ICD 10 boleh dipilih.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Bidang 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnosis atau kecederaan',
	ClaimDiagnosticCodesEmptyError: 'Sekurang-kurangnya satu kod diagnosis diperlukan',
	ClaimDoIncludeReferrerInformation: 'Sertakan maklumat perujuk pada CMS1500',
	ClaimERAReceivedAction: 'Kiriman wang elektronik diterima daripada <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Pemindahan elektronik diterima	<mark>Pembayaran {paymentReference}</mark> untuk <b>{paymentAmount}</b> oleh <b>{payerNumber} {payerName}</b> telah direkodkan',
	ClaimExportedAction: '<mark>Tuntutan {claimNumber}</mark> telah dieksport sebagai <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nama pelanggan atau kenalan',
	ClaimFieldClientAddress: 'Alamat pelanggan',
	ClaimFieldClientAddressDescription:
		'Masukkan alamat pelanggan. Baris pertama adalah untuk alamat jalan. Jangan gunakan tanda baca (koma atau noktah) atau sebarang simbol dalam alamat. Jika melaporkan alamat asing, hubungi pembayar untuk mendapatkan arahan pelaporan khusus.',
	ClaimFieldClientAddressSubtitle: 'Bidang 5',
	ClaimFieldClientDateOfBirth: 'Tarikh lahir Klien',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Bidang 3',
	ClaimFieldClientDateOfBirthDescription:
		'Masukkan tarikh lahir 8 digit pelanggan (MM/DD/YYYY). Tarikh lahir klien adalah maklumat yang akan mengenal pasti klien dan ia membezakan orang yang mempunyai nama yang sama.',
	ClaimFieldClientDescription: `Nama pelanggan' ialah nama orang yang menerima rawatan atau bekalan.`,
	ClaimFieldClientSexDescription: `'seks' ialah maklumat yang akan mengenal pasti klien dan ia membezakan orang yang mempunyai nama yang serupa.`,
	ClaimFieldClientSubtitle: 'Padang 2',
	ClaimFiling: 'Pemfailan Tuntutan',
	ClaimHistorySubtitle: 'Insurans • Tuntutan {number}',
	ClaimIncidentAutoAccident: 'Kemalangan kereta?',
	ClaimIncidentConditionRelatedTo: 'Adakah keadaan klien berkaitan dengan',
	ClaimIncidentConditionRelatedToHelpContent:
		'Maklumat ini menunjukkan sama ada penyakit atau kecederaan pelanggan berkaitan dengan pekerjaan, kemalangan kereta atau kemalangan lain. Pekerjaan (semasa atau sebelumnya) akan menunjukkan bahawa keadaan itu berkaitan dengan pekerjaan atau tempat kerja pelanggan. Kemalangan kereta akan menunjukkan bahawa keadaan adalah akibat daripada kemalangan kereta. Kemalangan lain akan menunjukkan bahawa keadaan itu adalah hasil daripada sebarang jenis kemalangan lain.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Medan 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Adakah keadaan klien berkaitan dengan',
	ClaimIncidentCurrentIllness: 'Penyakit semasa, kecederaan atau kehamilan',
	ClaimIncidentCurrentIllnessHelpContent:
		'Tarikh penyakit semasa, kecederaan atau kehamilan mengenal pasti tarikh pertama permulaan penyakit, tarikh kecederaan sebenar, atau LMP untuk kehamilan.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Bidang 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Tarikh penyakit semasa, kecederaan atau kehamilan (LMP)',
	ClaimIncidentDate: 'tarikh',
	ClaimIncidentDateFrom: 'Tarikh dari',
	ClaimIncidentDateTo: 'Tarikh ke',
	ClaimIncidentEmploymentRelated: 'Pekerjaan',
	ClaimIncidentEmploymentRelatedDesc: '(Semasa atau sebelumnya)',
	ClaimIncidentHospitalizationDatesLabel: 'Tarikh kemasukan ke hospital berkaitan perkhidmatan semasa',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Tarikh kemasukan ke hospital yang berkaitan dengan perkhidmatan semasa merujuk kepada penginapan pelanggan dan menunjukkan tarikh kemasukan dan pelepasan yang berkaitan dengan perkhidmatan pada tuntutan.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Bidang 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Tarikh kemasukan ke hospital berkaitan perkhidmatan semasa',
	ClaimIncidentInformation: 'Maklumat kejadian',
	ClaimIncidentOtherAccident: 'Kemalangan lain?',
	ClaimIncidentOtherAssociatedDate: 'Tarikh lain yang berkaitan',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Tarikh lain mengenal pasti maklumat tarikh tambahan tentang keadaan atau rawatan pelanggan.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Bidang 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Tarikh lain',
	ClaimIncidentQualifier: 'Kelayakan',
	ClaimIncidentQualifierPlaceholder: 'Pilih kelayakan',
	ClaimIncidentUnableToWorkDatesLabel: 'Pelanggan tidak dapat bekerja dalam pekerjaan semasa',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Tarikh pelanggan tidak dapat bekerja dalam pekerjaan semasa ialah jangka masa pelanggan boleh atau tidak dapat bekerja',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Bidang 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Pelanggan Dates tidak dapat bekerja dalam pekerjaan semasa',
	ClaimIncludeReferrerInformation: 'Sertakan maklumat perujuk pada CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Jenis perlindungan insurans kesihatan yang terpakai untuk tuntutan ini. Lain menunjukkan insurans kesihatan termasuk HMO, insurans komersial, kemalangan kereta, liabiliti atau pampasan pekerja.
 Maklumat ini mengarahkan tuntutan kepada program yang betul dan mungkin mewujudkan liabiliti utama.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Bidang 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Jenis liputan',
	ClaimInsuranceGroupIdHelpContent: `Masukkan polisi atau nombor kumpulan insured seperti yang tertera pada kad pengenalan penjagaan kesihatan insured.

 “Nombor Polisi, Kumpulan atau FECA Diinsuranskan” ialah pengecam abjad angka untuk perlindungan pelan insurans kesihatan, kereta atau insurans lain. Nombor FECA ialah pengecam abjad angka 9 aksara yang diberikan kepada pesakit yang menuntut keadaan berkaitan kerja.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Bidang 11',
	ClaimInsuranceGroupIdHelpTitle: 'Polisi, Kumpulan atau Nombor FECA Pihak Diinsuranskan',
	ClaimInsuranceMemberIdHelpContent: `Masukkan nombor ID insured seperti yang ditunjukkan pada kad ID insured untuk pembayar yang tuntutan sedang dikemukakan.
 Jika pesakit mempunyai Nombor Pengenalan Ahli unik yang diberikan oleh pembayar, kemudian masukkan nombor tersebut dalam medan ini.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Bidang 1a',
	ClaimInsuranceMemberIdHelpTitle: 'ID Ahli Insured',
	ClaimInsurancePayer: 'Pembayar insurans',
	ClaimManualPaymentAction: '<mark>Bayaran {paymentReference}</mark> untuk <b>{paymentAmount}</b> direkodkan',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Maklumat tuntutan tambahan',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Sila rujuk kepada arahan terkini daripada pembayar awam atau persendirian mengenai penggunaan medan ini. Laporkan kelayakan yang sesuai, jika tersedia, untuk maklumat yang dimasukkan.Jangan masukkan ruang, tanda sempang, atau pemisah lain antara kelayakan dan maklumat.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Bidang 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Maklumat tuntutan tambahan',
	ClaimMiscClaimCodes: 'Kod tuntutan',
	ClaimMiscOriginalReferenceNumber: 'Nombor rujukan asal',
	ClaimMiscPatientsAccountNumber: 'Nombor akaun pesakit',
	ClaimMiscPatientsAccountNumberHelpContent: 'Nombor akaun pesakit adalah pengecam yang diberikan oleh pembekal.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Padang 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Nombor akaun pesakit',
	ClaimMiscPriorAuthorizationNumber: 'Nombor kebenaran terdahulu',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Nombor pemberian kuasa terdahulu ialah nombor yang diberikan oleh pembayar yang membenarkan perkhidmatan tersebut.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Padang 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Nombor kebenaran terdahulu',
	ClaimMiscResubmissionCode: 'Kod penyerahan semula',
	ClaimMiscResubmissionCodeHelpContent:
		'Penyerahan semula bermaksud kod dan nombor rujukan asal yang diberikan oleh pembayar atau penerima destinasi untuk menunjukkan tuntutan atau pertemuan yang dikemukakan sebelum ini.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Padang 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Penyerahan semula dan / atau Nombor Rujukan Asal',
	ClaimNumber: 'Nombor tuntutan',
	ClaimNumberFormat: 'Klaim #{number}',
	ClaimOrderingProvider: 'Penyedia pesanan',
	ClaimOtherId: 'ID lain',
	ClaimOtherIdPlaceholder: 'Pilih satu pilihan',
	ClaimOtherIdQualifier: 'Kelayakan ID lain',
	ClaimOtherIdQualifierPlaceholder: 'Pilih kelayakan ID',
	ClaimPlaceOfService: 'Tempat perkhidmatan',
	ClaimPlaceOfServicePlaceholder: 'Tambah POS',
	ClaimPolicyHolderRelationship: 'Hubungan pemegang polisi',
	ClaimPolicyInformation: 'Maklumat dasar',
	ClaimPolicyTelephone: 'Telefon (sertakan kod kawasan)',
	ClaimReceivedAction: '<mark>Tuntutan {claimNumber}</mark> diterima oleh <b>{name}</b>',
	ClaimReferringProvider: 'Pembekal yang merujuk',
	ClaimReferringProviderEmpty: 'Tiada penyedia rujukan/s ditambah',
	ClaimReferringProviderHelpContent:
		'Nama yang dimasukkan ialah penyedia perujuk, penyedia pesanan, atau penyedia penyelia yang merujuk, memesan atau menyelia perkhidmatan atau pembekalan pada tuntutan. Kelayakan menunjukkan peranan penyedia sedang dilaporkan.',
	ClaimReferringProviderHelpSubtitle: 'Bidang 17',
	ClaimReferringProviderHelpTitle: 'Nama pembekal atau sumber yang merujuk',
	ClaimReferringProviderQualifier: 'Kelayakan',
	ClaimReferringProviderQualifierPlaceholder: 'Pilih kelayakan',
	ClaimRejectedAction: '<mark>Tuntutan {claimNumber}</mark> telah ditolak oleh <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Nombor ID',
	ClaimRenderingProviderOrTeamMember: 'Pembekal rendering atau ahli pasukan',
	ClaimRestoredAction: '<mark>Tuntutan {claimNumber}</mark> telah dipulihkan',
	ClaimServiceFacility: 'Kemudahan perkhidmatan',
	ClaimServiceFacilityLocationHelpContent:
		'Nama dan alamat kemudahan di mana perkhidmatan diberikan, mengenal pasti tapak di mana perkhidmatan disediakan.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a dan 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Bidang 32, 32a dan 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Kemudahan Perkhidmatan',
	ClaimServiceFacilityPlaceholder: 'Pilih kemudahan perkhidmatan atau lokasi',
	ClaimServiceLabChargesHelpContent: `Lengkapkan medan ini apabila menuntut perkhidmatan yang dibeli yang disediakan oleh entiti selain pembekal pengebilan.
 Setiap perkhidmatan yang dibeli mesti dilaporkan pada tuntutan berasingan kerana hanya satu caj boleh dimasukkan pada borang CMS1500.`,
	ClaimServiceLabChargesHelpSubtitle: 'Bidang 20',
	ClaimServiceLabChargesHelpTitle: 'Caj makmal luar',
	ClaimServiceLineServiceHelpContent:
		'"Prosedur, Perkhidmatan atau Bekalan" mengenal pasti perkhidmatan dan prosedur perubatan yang diberikan kepada pesakit.',
	ClaimServiceLineServiceHelpSubtitle: 'Medan 24d',
	ClaimServiceLineServiceHelpTitle: 'Prosedur, Perkhidmatan atau Bekalan',
	ClaimServiceLinesEmptyError: 'Sekurang-kurangnya satu baris perkhidmatan diperlukan',
	ClaimServiceSupplementaryInfoHelpContent: `Tambahkan perihalan naratif tambahan bagi perkhidmatan yang disediakan menggunakan kelayakan yang berkenaan.
 Jangan masukkan ruang, sempang atau pemisah lain antara kelayakan dan maklumat.

 Untuk arahan lengkap tentang menambah maklumat tambahan semak arahan borang tuntutan CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Padang 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Maklumat tambahan',
	ClaimSettingsBillingMethodTitle: 'Kaedah pengebilan pelanggan',
	ClaimSettingsClientSignatureDescription:
		'Saya mempunyai kebenaran untuk mengeluarkan maklumat perubatan atau maklumat lain yang diperlukan untuk memproses tuntutan insurans.',
	ClaimSettingsClientSignatureTitle: 'Tandatangan pelanggan pada fail',
	ClaimSettingsConsentLabel: 'Persetujuan diperlukan untuk memproses tuntutan insurans:',
	ClaimSettingsDescription: 'Pilih kaedah pengebilan pelanggan untuk memastikan pemprosesan pembayaran lancar:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} mempunyai polisi insurans yang aktif. Untuk mengaktifkan bilangan insurans, kemas kini kaedah Bilangan Pelanggan kepada Insurans.',
	ClaimSettingsInsuranceDescription: 'Kos yang dibayar balik oleh insurans',
	ClaimSettingsInsuranceTitle: 'Insurans',
	ClaimSettingsNoPoliciesAlertDescription: 'Tambahkan polisi insurans untuk membolehkan tuntutan insurans.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Saya mempunyai kebenaran untuk menerima bayaran insurans untuk perkhidmatan yang disediakan.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Tandatangan pemegang polisi pada fail',
	ClaimSettingsSelfPayDescription: 'Pelanggan akan membayar untuk temujanji',
	ClaimSettingsSelfPayTitle: 'Bayar sendiri',
	ClaimSettingsTitle: 'Tetapan tuntutan',
	ClaimSexSelectorPlaceholder: 'Lelaki / Perempuan',
	ClaimStatusChangedAction: '<mark>Tuntutan {claimNumber}</mark> status dikemas kini',
	ClaimSubmittedAction:
		'<mark>Tuntutan {claimNumber}</mark> telah dikemukakan kepada <b>{payerClearingHouse}</b> untuk <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Tuntutan #{claimNumber}',
	ClaimSupervisingProvider: 'Penyelia pembekal',
	ClaimSupplementaryInfo: 'Maklumat tambahan',
	ClaimSupplementaryInfoPlaceholder: 'Tambah maklumat tambahan',
	ClaimTrashedAction: '<mark>Tuntutan {claimNumber}</mark> telah dipadamkan',
	ClaimValidationFailure: 'Gagal mengesahkan tuntutan',
	ClaimsEmptyStateDescription: 'Tiada Tuntutan ditemui.',
	ClainInsuranceTelephone: 'Telefon Insurans (termasuk kod kawasan)',
	Classic: 'Klasik',
	Clear: 'Jelas',
	ClearAll: 'Hapuskan Semua',
	ClearSearchFilter: 'Jelas',
	ClearingHouse: 'Pembersihan rumah',
	ClearingHouseClaimId: 'ID Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Rujukan Rumah Pembersihan',
	ClearingHouseUnavailableError: 'Pusat penjelasan ini kini tidak tersedia. Sila cuba lagi kemudian.',
	ClickToUpload: 'Klik untuk memuat naik',
	Client: 'Pelanggan',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} telah menambah {noteTitle, select, undefined { satu nota } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Pilih pelanggan dan hubungan mereka',
	ClientAndRelationshipSelectorTitle: 'Semua pelanggan dan hubungan mereka',
	ClientAndRelationshipSelectorTitle1: 'Semua hubungan ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Jika anda sedang menunggu panggilan video, panggilan itu akan dipaparkan di sini sebentar lagi. Jika anda menghadapi sebarang masalah sila hubungi orang yang memulakannya.',
	ClientAppSubHeaderMyDocumentation: 'Dokumentasi saya',
	ClientAppointment: 'Pelantikan Pelanggan',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} telah menempah {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Tiada janji temu ditemui',
	ClientAppointmentsEmptyStateTitle:
		'Jejaki janji temu pelanggan anda yang akan datang dan bersejarah serta kehadiran mereka',
	ClientArchivedSuccessfulSnackbar: 'Berjaya diarkibkan **{name}**',
	ClientBalance: 'Baki pelanggan',
	ClientBilling: 'Pengebilan',
	ClientBillingAddPaymentMethodDescription:
		'Tambah dan urus kaedah pembayaran pelanggan anda untuk menyelaraskan proses invois dan pengebilan mereka.',
	ClientBillingAndPaymentDueDate: 'tarikh tamat tempoh',
	ClientBillingAndPaymentHistory: 'Sejarah pengebilan dan pembayaran',
	ClientBillingAndPaymentInvoices: 'Invois',
	ClientBillingAndPaymentIssueDate: 'Tarikh keluaran',
	ClientBillingAndPaymentPrice: 'harga',
	ClientBillingAndPaymentReceipt: 'resit',
	ClientBillingAndPaymentServices: 'Perkhidmatan',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Pasukan {count, plural, one {anggota} other {anggota}} telah ditugaskan!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Pasukan {count, plural, one {ahli} other {ahli}} tidak ditugaskan!',
	ClientBulkTagsAddedSuccessSnackbar: 'Teg ditambah!',
	ClientDuplicatesDeviewDescription:
		'Gabungkan berbilang rekod pelanggan menjadi satu untuk menyatukan semua data—nota, dokumen, janji temu, invois dan perbualan.',
	ClientDuplicatesPageMergeHeader: 'Pilih data yang anda ingin simpan',
	ClientDuplicatesReviewHeader: 'Bandingkan rekod pendua yang berpotensi untuk digabungkan',
	ClientEmailChangeWarningDescription:
		'Mengemas kini e-mel pelanggan akan mengalih keluar akses mereka kepada mana-mana dokumentasi yang dikongsi dan akan memberikan akses kepada pengguna dengan e-mel baharu',
	ClientFieldDateDescription: 'Format tarikh',
	ClientFieldDateLabel: 'tarikh',
	ClientFieldDateRangeDescription: 'Pelbagai tarikh',
	ClientFieldDateRangeLabel: 'Julat tarikh',
	ClientFieldDateShowDateDescription: 'cth 29 tahun',
	ClientFieldDateShowDateRangeDescription: 'cth 2 minggu',
	ClientFieldEmailDescription: 'Alamat e-mel',
	ClientFieldEmailLabel: 'E-mel',
	ClientFieldLabel: 'Label medan',
	ClientFieldLinearScaleDescription: 'Pilihan skala 1-10',
	ClientFieldLinearScaleLabel: 'Skala linear',
	ClientFieldLocationDescription: 'Alamat fizikal atau pos',
	ClientFieldLocationLabel: 'Lokasi',
	ClientFieldLongTextDescription: 'Kawasan teks panjang',
	ClientFieldLongTextLabel: 'Perenggan',
	ClientFieldMultipleChoiceDropdownDescription: 'Pilih berbilang pilihan daripada senarai',
	ClientFieldMultipleChoiceDropdownLabel: 'lungsur turun berbilang pilihan',
	ClientFieldPhoneNumberDescription: 'Nombor telefon',
	ClientFieldPhoneNumberLabel: 'telefon',
	ClientFieldPlaceholder: 'Pilih jenis medan klien',
	ClientFieldSingleChoiceDropdownDescription: 'Pilih hanya satu pilihan daripada senarai',
	ClientFieldSingleChoiceDropdownLabel: 'lungsur pilihan tunggal',
	ClientFieldTextDescription: 'Medan input teks',
	ClientFieldTextLabel: 'Teks',
	ClientFieldYesOrNoDescription: 'Pilih daripada pilihan ya atau tidak',
	ClientFieldYesOrNoLabel: 'Ya | Tidak',
	ClientFileFormAccessLevelDescription:
		'Anda dan Pasukan sentiasa mempunyai akses kepada fail yang anda muat naik. Anda boleh memilih untuk berkongsi fail ini dengan pelanggan dan/atau perhubungan mereka',
	ClientFileSavedSuccessSnackbar: 'Fail disimpan!',
	ClientFilesPageEmptyStateText: 'Tiada fail dimuat naik',
	ClientFilesPageUploadFileButton: 'Muat naik fail',
	ClientHeaderBilling: 'Pengebilan',
	ClientHeaderBillingAndReceipts: 'Pengebilan ',
	ClientHeaderDocumentation: 'Dokumentasi',
	ClientHeaderDocuments: 'Dokumen',
	ClientHeaderFile: 'Dokumen',
	ClientHeaderHistory: 'Sejarah perubatan',
	ClientHeaderInbox: 'Peti masuk',
	ClientHeaderNote: 'Nota',
	ClientHeaderOverview: 'Gambaran keseluruhan',
	ClientHeaderProfile: 'Peribadi',
	ClientHeaderRelationship: 'Perhubungan',
	ClientHeaderRelationships: 'perhubungan',
	ClientId: 'ID Pelanggan',
	ClientImportProcessingDescription: 'Fail masih diproses. Kami akan memberitahu anda apabila ini selesai.',
	ClientImportReadyForMappingDescription:
		'Kami telah selesai memproses awal fail anda. Adakah anda ingin memetakan lajur untuk melengkapkan import ini?',
	ClientImportReadyForMappingNotificationSubject:
		'Client import pra-pemprosesan telah selesai. Fail kini sedia untuk pemetaan.',
	ClientInAppMessaging: 'Pemesejan Dalam apl Pelanggan',
	ClientInfoAddField: 'Tambah medan lain',
	ClientInfoAddRow: 'Tambah baris',
	ClientInfoAlertMessage: 'Sebarang maklumat yang diisi dalam bahagian ini akan mengisi rekod pelanggan.',
	ClientInfoFormPrimaryText: 'Maklumat pelanggan',
	ClientInfoFormSecondaryText: 'Kumpul butiran hubungan',
	ClientInfoPlaceholder: `Nama pelanggan, Alamat e-mel, Nombor telefon
 alamat fizikal,
 Tarikh lahir`,
	ClientInformation: 'Maklumat pelanggan',
	ClientInsuranceTabLabel: 'Insurans',
	ClientIntakeFormsNotSupported: `Templat borang tidak disokong pada masa ini melalui pengambilan pelanggan.
 Buat dan kongsikannya sebagai nota pelanggan.`,
	ClientIntakeModalDescription:
		'E-mel pengambilan akan dihantar kepada pelanggan anda meminta mereka melengkapkan profil mereka, memuat naik dokumen perubatan atau rujukan yang berkaitan. Mereka akan diberi akses Portal Pelanggan.',
	ClientIntakeModalTitle: 'Hantar pengambilan kepada {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Berjaya! Pengambilan anda telah disimpan.',
	ClientIntakeSuccessSnackbar: 'Berjaya! Pengambilan anda telah disimpan dan e-mel pengesahan dihantar.',
	ClientIsChargedProcessingFee: 'Pelanggan anda akan membayar yuran pemprosesan',
	ClientListCreateButton: 'Pelanggan baru',
	ClientListEmptyState: 'Tiada pelanggan ditambahkan',
	ClientListPageItemArchive: 'Alih keluar pelanggan',
	ClientListPageItemRemoveAccess: 'Alih keluar akses saya',
	ClientLocalizationPanelDescription: 'Bahasa dan zon waktu pilihan pelanggan.',
	ClientLocalizationPanelTitle: 'Bahasa dan Zon Waktu',
	ClientManagementAndEHR: 'Pengurusan Pelanggan ',
	ClientMergeResultSummaryBanner:
		'Menggabungkan rekod menggabungkan semua data klien, termasuk nota, dokumen, temu janji, invois, dan perbualan. Sila sahkan ketepatan sebelum meneruskan.',
	ClientMergeResultSummaryTitle: 'Ringkasan hasil penggabungan',
	ClientModalTitle: 'Pelanggan baru',
	ClientMustHaveEmaillAccessErrorText: 'Pelanggan/Kenalan tanpa e-mel',
	ClientMustHavePortalAccessErrorText: 'Pelanggan/Kenalan akan dikehendaki mendaftar',
	ClientMustHaveZoomAppConnectedErrorText: 'Sambung Zum melalui Tetapan > Apl Bersambung',
	ClientNameFormat: 'Format nama pelanggan',
	ClientNotFormAccessLevel: 'Boleh dilihat oleh:',
	ClientNotFormAccessLevelDescription:
		'Anda dan Pasukan sentiasa mempunyai akses kepada nota yang anda terbitkan. Anda boleh memilih untuk berkongsi nota ini dengan pelanggan dan/atau perhubungan mereka',
	ClientNotRegistered: 'Tidak berdaftar',
	ClientNoteFormAddFileButton: 'Lampirkan fail',
	ClientNoteFormChooseAClient: 'Pilih pelanggan/hubungan untuk meneruskan',
	ClientNoteFormContent: 'kandungan',
	ClientNoteItemDeleteConfirmationModalDescription:
		'Setelah dipadamkan anda tidak boleh mendapatkan semula nota ini.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Nota diterbitkan dan dikunci.',
	ClientNotePublishedSuccessSnackbar: 'Nota diterbitkan!',
	ClientNotes: 'Nota pelanggan',
	ClientNotesEmptyStateText: 'Untuk menambah nota, pergi ke profil pelanggan dan klik tab Nota.',
	ClientOnboardingChoosePasswordTitle1: 'Hampir Selesai!',
	ClientOnboardingChoosePasswordTitle2: 'Pilih kata laluan',
	ClientOnboardingCompleteIntake: 'Pengambilan lengkap',
	ClientOnboardingConfirmationScreenText:
		'Anda telah memberikan semua maklumat yang diperlukan oleh {providerName}.	Sahkan alamat e-mel anda untuk memulakan proses onboarding. Jika anda tidak menerimanya dengan segera, sila semak folder spam anda.',
	ClientOnboardingConfirmationScreenTitle: 'Hebat! Semak peti masuk anda.',
	ClientOnboardingDashboardButton: 'Pergi ke Papan Pemuka',
	ClientOnboardingHealthRecordsDesc1:
		'Adakah anda ingin berkongsi sebarang surat rujukan, dokumen dengan {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Tambah Penerangan (pilihan)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentasi',
	ClientOnboardingPasswordRequirements: 'Keperluan',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimum 9 aksara diperlukan',
	ClientOnboardingProviderIntroSignupButton: 'Daftar untuk diri saya sendiri',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Daftar untuk ahli keluarga',
	ClientOnboardingProviderIntroTitle: '{name} telah menjemput anda untuk menyertai platform Carepatron mereka',
	ClientOnboardingRegistrationInstructions: 'Masukkan butiran peribadi anda di bawah.',
	ClientOnboardingRegistrationTitle: 'Mula-mula kita memerlukan beberapa butiran peribadi',
	ClientOnboardingStepFormsAndAgreements: 'Borang dan Perjanjian',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Sila lengkapkan borang-borang berikut untuk proses pengambilan {providerName}',
	ClientOnboardingStepHealthDetails: 'Butiran Kesihatan',
	ClientOnboardingStepPassword: 'Kata laluan',
	ClientOnboardingStepYourDetails: 'Butiran Anda',
	ClientPaymentMethodDescription:
		'Simpan kaedah pembayaran pada profil anda untuk membuat tempahan janji temu dan invois anda yang seterusnya dengan lebih pantas dan selamat.',
	ClientPortal: 'Portal Pelanggan',
	ClientPortalDashboardEmptyDescription: 'Sejarah pelantikan dan kehadiran anda akan dipaparkan di sini.',
	ClientPortalDashboardEmptyTitle:
		'Jejaki semua janji temu yang akan datang, diminta dan yang lalu bersama dengan kehadiran anda',
	ClientPreferredNotificationPanelDescription:
		'Urus kaedah pilihan pelanggan anda untuk menerima kemas kini dan pemberitahuan melalui:',
	ClientPreferredNotificationPanelTitle: 'Kaedah pemberitahuan pilihan',
	ClientProcessingFee: 'Pembayaran termasuk ({currencyCode}) {amount} yuran pemprosesan',
	ClientProfileAddress: 'Alamat',
	ClientProfileDOB: 'Tarikh lahir',
	ClientProfileEmailHelperText: 'Menambah e-mel memberikan akses portal',
	ClientProfileEmailHelperTextMoreInfo:
		'Memberi pelanggan akses ke portal membolehkan ahli pasukan berkongsi nota, fail dan dokumentasi lain',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Nombor pengenalan',
	ClientRelationshipsAddClientOwnerButton: 'Jemput pelanggan',
	ClientRelationshipsAddFamilyButton: 'Jemput ahli keluarga',
	ClientRelationshipsAddStaffButton: 'Tambahkan akses kakitangan',
	ClientRelationshipsEmptyStateText: 'Tiada perhubungan ditambah',
	ClientRemovedSuccessSnackbar: 'Pelanggan berjaya dialih keluar.',
	ClientResponsibility: 'Tanggungjawab Pelanggan',
	ClientSavedSuccessSnackbar: 'Pelanggan berjaya disimpan.',
	ClientTableClientName: 'Nama pelanggan',
	ClientTablePhone: 'telefon',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Berjaya tidak diarkibkan <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} telah menyunting {noteTitle, select, undefined { satu nota } other {{noteTitle}}}',
	ClientView: 'Pandangan pelanggan',
	Clients: 'Pelanggan',
	ClientsTable: 'Jadual Pelanggan',
	ClinicalFormat: 'Format klinikal',
	ClinicalPsychologist: 'Pakar Psikologi Klinikal',
	Close: 'tutup',
	CloseImportClientsModal: 'Adakah anda pasti mahu membatalkan pengimportan pelanggan?',
	CloseReactions: 'Reaksi rapat',
	Closed: 'tertutup',
	Coaching: 'Kejurulatihan',
	Code: 'Kod',
	CodeErrorMessage: 'Kod diperlukan',
	CodePlaceholder: 'Kod',
	Coinsurance: 'Insurans bersama',
	Collection: 'Koleksi',
	CollectionName: 'Nama Koleksi',
	Collections: 'Koleksi',
	ColorAppointmentsBy: 'Temu janji warna oleh',
	ColorTheme: 'Tema warna',
	ColourCalendarBy: 'Kalendar warna oleh',
	ComingSoon: 'Akan datang',
	Community: 'Komuniti',
	CommunityHealthLead: 'Ketua Kesihatan Masyarakat',
	CommunityHealthWorker: 'Pekerja Kesihatan Masyarakat',
	CommunityTemplatesSectionDescription: 'Dihasilkan oleh komuniti',
	CommunityTemplatesSectionTitle: 'Komuniti',
	CommunityUser: 'Pengguna Komuniti',
	Complete: 'lengkap',
	CompleteAndLock: 'Lengkap dan kunci',
	CompleteSetup: 'Persediaan Lengkap',
	CompleteSetupSuccessDescription: 'Anda telah menyelesaikan beberapa langkah penting untuk menguasai Carepatron.',
	CompleteSetupSuccessDescription2:
		'Buka kunci lebih banyak cara untuk membantu melancarkan amalan anda dan menyokong pelanggan anda.',
	CompleteSetupSuccessTitle: 'Berjaya! Anda hebat!',
	CompleteStripeSetup: 'Lengkapkan persediaan Stripe',
	Completed: 'Selesai',
	ComposeSms: 'Karang SMS',
	ComputerSystemsAnalyst: 'Penganalisis Sistem Komputer',
	Confirm: 'sahkan',
	ConfirmDeleteAccountDescription:
		'Anda akan memadamkan akaun anda. Tindakan ini tidak boleh dibuat asal. Jika anda ingin meneruskan, sila sahkan di bawah.',
	ConfirmDeleteActionDescription: 'Adakah anda pasti mahu memadamkan tindakan ini? Ini tidak boleh dibuat asal',
	ConfirmDeleteAutomationDescription:
		'Adakah anda pasti mahu memadamkan automasi ini? Tindakan ini tidak boleh dibuat asal.',
	ConfirmDeleteScheduleDescription:
		'Membuang jadual <strong>{scheduleName}</strong> akan menghapuskannya dari jadual anda dan mungkin mengubah perkhidmatan dalam talian anda yang tersedia. Tindakan ini tidak boleh dibatalkan.',
	ConfirmDraftResponseContinue: 'Teruskan dengan respons',
	ConfirmDraftResponseDescription:
		'Jika anda menutup halaman ini, respons anda akan kekal sebagai draf. Anda boleh kembali dan meneruskan pada bila-bila masa.',
	ConfirmDraftResponseSubmitResponse: 'Serahkan jawapan',
	ConfirmDraftResponseTitle: 'Jawapan anda belum diserahkan',
	ConfirmIfUserIsClientDescription: `Borang pendaftaran yang anda isi adalah untuk Pembekal (iaitu pasukan kesihatan/organisasi).
 Jika ini adalah kesilapan, anda boleh memilih "Teruskan sebagai pelanggan" dan kami akan menyediakan anda untuk portal pelanggan anda`,
	ConfirmIfUserIsClientNoButton: 'Daftar sebagai Pembekal',
	ConfirmIfUserIsClientTitle: 'Nampaknya anda seorang pelanggan',
	ConfirmIfUserIsClientYesButton: 'Teruskan sebagai pelanggan',
	ConfirmKeepSeparate: 'Confirm simpan berasingan',
	ConfirmMerge: 'Sahkan penggabungan',
	ConfirmPassword: 'Sahkan kata laluan',
	ConfirmRevertClaim: 'Ya, kembalikan status',
	ConfirmSignupAccessCode: 'Kod pengesahan',
	ConfirmSignupButtom: 'sahkan',
	ConfirmSignupDescription: 'Sila masukkan alamat e-mel anda dan kod pengesahan yang baru kami hantar kepada anda.',
	ConfirmSignupSubTitle: 'Semak folder Spam - jika e-mel belum sampai',
	ConfirmSignupSuccessSnackbar:
		'Bagus, kami telah mengesahkan akaun anda! Kini anda boleh log masuk menggunakan e-mel dan kata laluan anda',
	ConfirmSignupTitle: 'Sahkan akaun',
	ConfirmSignupUsername: 'E-mel',
	ConfirmSubscriptionUpdate: 'Sahkan langganan {price} {isMonthly, select, true {sebulan} other {setahun}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Setelah pelanggan dipadamkan, anda tidak akan dapat mengakses maklumat mereka lagi.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Padamkan {count, plural, one {# pelanggan} other {# pelanggan}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Setelah kenalan dipadamkan, anda tidak akan dapat mengakses maklumat mereka lagi.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Padamkan {count, plural, one {# hubungan} other {# hubungan}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Ini adalah tindakan kekal. Setelah ahli pasukan dipadamkan, anda tidak akan dapat mengakses maklumat mereka lagi.',
	ConfirmationModalBulkDeleteMembersTitleId: 'Padamkan {count, plural, one {# ahli pasukan} other {# ahli pasukan}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Menutup nota ini akan menamatkan sebarang transkripsi yang sedang berjalan. Adakah anda pasti mahu meneruskan?',
	ConfirmationModalDeleteClientField:
		'Ini adalah tindakan kekal. Setelah medan dipadamkan, ia tidak lagi boleh diakses pada pelanggan anda yang tinggal.',
	ConfirmationModalDeleteSectionMessage:
		'Setelah dipadamkan, semua soalan dalam bahagian ini akan dialih keluar. Tindakan ini tidak boleh dibuat asal.',
	ConfirmationModalDeleteService:
		'Ini adalah tindakan kekal. Setelah perkhidmatan dipadamkan, perkhidmatan itu tidak lagi boleh diakses di ruang kerja anda.',
	ConfirmationModalDeleteServiceGroup:
		'Memadamkan koleksi akan mengalih keluar semua perkhidmatan daripada kumpulan dan akan kembali ke senarai perkhidmatan anda. Tindakan ini tidak boleh dibuat asal.',
	ConfirmationModalDeleteTranscript: 'Adakah anda pasti mahu memadamkan transkrip?',
	ConfirmationModalDescriptionDeleteClient:
		'Setelah pelanggan dipadamkan, anda tidak akan dapat mengakses maklumat pelanggan lagi.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Sebaik sahaja anda mengalih keluar akses anda, anda tidak akan dapat melihat maklumat pelanggan lagi.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Profil mereka tidak akan dipadamkan, hanya dialih keluar sebagai perhubungan pelanggan ini.',
	ConfirmationModalDescriptionRemoveStaff: 'Adakah anda pasti mahu mengalih keluar orang ini daripada pembekal?',
	ConfirmationModalEndSession: 'Adakah anda pasti mahu menamatkan sesi?',
	ConfirmationModalTitle: 'Adakah anda pasti?',
	Confirmed: 'Disahkan',
	ConflictTimezoneWarningMessage: 'Konflik mungkin berlaku disebabkan oleh berbilang zon waktu',
	Connect: 'Sambung',
	ConnectExistingClientOrContact: 'Buat pelanggan/hubungan baharu',
	ConnectInboxGoogleDescription: 'Tambahkan akaun Gmail atau senarai kumpulan Google',
	ConnectInboxMicrosoftDescription: 'Tambahkan akaun Outlook, Office365 atau Exchange',
	ConnectInboxModalDescription:
		'Sambungkan apl anda untuk menghantar, menerima dan menjejaki semua komunikasi anda dengan lancar di satu tempat terpusat.',
	ConnectInboxModalExistingDescription:
		'Gunakan sambungan sedia ada daripada tetapan apl bersambung anda untuk menyelaraskan proses konfigurasi.',
	ConnectInboxModalExistingTitle: 'Apl bersambung sedia ada dalam Carepatron',
	ConnectInboxModalTitle: 'Sambung peti masuk',
	ConnectToStripe: 'Sambung ke Stripe',
	ConnectZoom: 'Sambung Zum',
	ConnectZoomModalDescription: 'Benarkan Carepatron menguruskan panggilan video untuk janji temu anda.',
	ConnectedAppDisconnectedNotificationSubject:
		'Kami telah kehilangan sambungan ke akaun {account}. Sila sambung semula',
	ConnectedAppSyncDescription:
		'Urus apl yang disambungkan untuk membuat acara dalam kalendar pihak ketiga terus daripada Carepatron.',
	ConnectedApps: 'Apl bersambung',
	ConnectedAppsGMailDescription: 'Tambah akaun Gmail atau senarai kumpulan Google',
	ConnectedAppsGoogleCalendarDescription: 'Tambah akaun kalendar atau senarai kumpulan Google',
	ConnectedAppsGoogleDescription: 'Tambahkan akaun Gmail anda dan segerakkan kalendar Google',
	ConnectedAppsMicrosoftDescription: 'Tambahkan akaun Outlook, Office365 atau Exchange',
	ConnectedCalendars: 'Kalendar Bersambung',
	ConsentDocumentation: 'Borang dan perjanjian',
	ConsentDocumentationPublicTemplateError:
		'Atas sebab keselamatan, anda hanya boleh memilih templat daripada pasukan anda (bukan awam).',
	ConstructionWorker: 'Pekerja Binaan',
	Consultant: 'Perunding',
	Contact: 'Kenalan',
	ContactAccessTypeHelperText: 'Membenarkan pentadbir keluarga mengemas kini maklumat',
	ContactAccessTypeHelperTextMoreInfo:
		'Ini akan membolehkan anda untuk berkongsi nota/dokumen tentang {clientFirstName}',
	ContactAddressLabelBilling: 'Pengebilan',
	ContactAddressLabelHome: 'Rumah',
	ContactAddressLabelOthers: 'Lain-lain',
	ContactAddressLabelWork: 'Kerja',
	ContactChangeConfirmation:
		'Mengubah kenalan invois akan mengeluarkan semua barisan berkaitan dengan <mark>{contactName}</mark>',
	ContactDetails: 'Butiran hubungan',
	ContactEmailLabelOthers: 'Lain-lain',
	ContactEmailLabelPersonal: 'Peribadi',
	ContactEmailLabelSchool: 'Sekolah',
	ContactEmailLabelWork: 'Kerja',
	ContactInformation: 'Maklumat Hubungan',
	ContactInformationText: 'Maklumat hubungan',
	ContactListCreateButton: 'Kenalan baharu',
	ContactName: 'Nama kenalan',
	ContactPhoneLabelHome: 'Rumah',
	ContactPhoneLabelMobile: 'Mudah alih',
	ContactPhoneLabelSchool: 'Sekolah',
	ContactPhoneLabelWork: 'Kerja',
	ContactRelationship: 'Hubungan hubungan',
	ContactRelationshipFormAccessType: 'Berikan akses kepada maklumat yang dikongsi',
	ContactRelationshipGrantAccessInfo: 'Ini akan membolehkan anda berkongsi nota ',
	ContactSupport: 'Hubungi sokongan',
	Contacts: 'Kenalan',
	ContainerIdNotSet: 'ID kontena tidak ditetapkan',
	Contemporary: 'Kontemporari',
	Continue: 'teruskan',
	ContinueDictating: 'Teruskan mengimlak',
	ContinueEditing: 'Teruskan mengedit',
	ContinueImport: 'Teruskan import',
	ContinueTranscription: 'Teruskan transkripsi',
	ContinueWithApple: 'Teruskan dengan Apple',
	ContinueWithGoogle: 'Teruskan dengan Google',
	Conversation: 'Perbualan',
	Copay: 'Bayar bersama',
	CopayOrCoinsurance: 'Co-pay atau Co-insurance',
	Copayment: 'Bayaran bersama',
	CopiedToClipboard: 'Disalin ke papan keratan',
	Copy: 'salin',
	CopyAddressSuccessSnackbar: 'Alamat disalin ke papan klip',
	CopyCode: 'Salin kod',
	CopyCodeToClipboardSuccess: 'Kod disalin ke papan klip',
	CopyEmailAddressSuccessSnackbar: 'Alamat emel disalin ke papan keratan.',
	CopyLink: 'Salin pautan',
	CopyLinkForCall: 'Salin pautan ini untuk berkongsi panggilan ini:',
	CopyLinkSuccessSnackbar: 'Pautan disalin ke papan keratan',
	CopyMeetingLink: 'Salin pautan mesyuarat',
	CopyPaymentLink: 'Salin pautan pembayaran',
	CopyPhoneNumberSuccessSnackbar: 'Nombor telefon disalin ke papan klip',
	CopyTemplateLink: 'Salin pautan ke templat',
	CopyTemplateLinkSuccess: 'Pautan disalin ke papan keratan',
	CopyToClipboardError: 'Tidak dapat menyalin ke papan keratan. Sila cuba lagi.',
	CopyToTeamTemplates: 'Salin ke templat Pasukan',
	CopyToWorkspace: 'Salin ke ruang kerja',
	Cosmetologist: 'Pakar kosmetik',
	Cost: 'kos',
	CostErrorMessage: 'Kos diperlukan',
	Counseling: 'Kaunseling',
	Counselor: 'Kaunselor',
	Counselors: 'Kaunselor',
	CountInvoicesAdded: '{count, plural, one {# Bil invois ditambah} other {# Bil-bil invois ditambah}}',
	CountNotesAdded: '{count, plural, one {# Nota ditambah} other {# Nota ditambah}}',
	CountSelected: '{count} dipilih',
	CountTimes: '{count} kali',
	Country: 'Negara',
	Cousin: 'sepupu',
	CoverageType: 'Jenis liputan',
	Covered: 'bertudung',
	Create: 'Buat',
	CreateANewClient: 'Buat pelanggan baharu',
	CreateAccount: 'Buat akaun',
	CreateAndSignNotes: 'Buat dan tandatangani nota dengan pelanggan',
	CreateAvailabilityScheduleFailure: 'Gagal membuat jadual ketersediaan baharu',
	CreateAvailabilityScheduleSuccess: 'Berjaya membuat jadual ketersediaan baharu',
	CreateBillingItems: 'Buat item pengebilan',
	CreateCallFormButton: 'Mulakan panggilan',
	CreateCallFormInviteOnly: 'Jemput sahaja',
	CreateCallFormInviteOnlyMoreInfo:
		'Hanya orang yang dijemput ke panggilan ini boleh menyertai. Untuk berkongsi panggilan ini dengan orang lain, hanya nyahtanda ini dan salin/tampal pautan pada halaman seterusnya',
	CreateCallFormRecipients: 'Penerima',
	CreateCallFormRegion: 'Rantau pengehosan',
	CreateCallModalAddClientContactSelectorLabel: 'Kenalan pelanggan',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Cari mengikut nama pelanggan',
	CreateCallModalAddStaffSelectorLabel: 'Ahli pasukan (pilihan)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Cari mengikut nama kakitangan',
	CreateCallModalDescription:
		'Mulakan panggilan dan jemput kakitangan dan/atau kenalan. Sebagai alternatif, anda boleh menyahtanda kotak "Peribadi" untuk menjadikan panggilan ini boleh dikongsi kepada sesiapa sahaja yang mempunyai Carepatron',
	CreateCallModalTitle: 'Mulakan panggilan',
	CreateCallModalTitleLabel: 'Tajuk (pilihan)',
	CreateCallNoPersonIdToolTip: 'Hanya kenalan/pelanggan yang mempunyai akses portal boleh menyertai panggilan',
	CreateClaim: 'Buat tuntutan',
	CreateClaimCompletedMessage: 'Tuntutan anda telah dibuat.',
	CreateClientModalTitle: 'Pelanggan baru',
	CreateContactModalTitle: 'Kenalan baharu',
	CreateContactRelationshipButton: 'Tambah perhubungan',
	CreateContactSelectorDefaultOption: '  Buat kenalan',
	CreateContactWithRelationshipFormAccessType: 'Berikan akses kepada maklumat yang dikongsi ',
	CreateDocumentDnDPrompt: 'Seret dan lepas untuk memuat naik fail',
	CreateDocumentSizeLimit: 'Had lapan fail {size}MB. Jumlah fail {total}.',
	CreateFreeAccount: 'Buat akaun percuma',
	CreateInvoice: 'Buat invois',
	CreateLink: 'Buat pautan',
	CreateNew: 'Buat baharu',
	CreateNewAppointment: 'Buat janji temu baharu',
	CreateNewClaim: 'Buat tuntutan baharu',
	CreateNewClaimForAClient: 'Cipta tuntutan baharu untuk pelanggan.',
	CreateNewClient: 'Buat pelanggan baharu',
	CreateNewConnection: 'Sambungan baru',
	CreateNewContact: 'Buat kenalan baharu',
	CreateNewField: 'Cipta medan baharu',
	CreateNewLocation: 'Lokasi baru',
	CreateNewService: 'Buat perkhidmatan baharu',
	CreateNewServiceGroupFailure: 'Gagal membuat koleksi baharu',
	CreateNewServiceGroupMenu: 'Koleksi baru',
	CreateNewServiceGroupSuccess: 'Berjaya mencipta koleksi baharu',
	CreateNewServiceMenu: 'Perkhidmatan baru',
	CreateNewTeamMember: 'Buat ahli pasukan baharu',
	CreateNewTemplate: 'Templat baharu',
	CreateNote: 'Buat nota',
	CreateSuperbillReceipt: 'Superbill baru',
	CreateSuperbillReceiptSuccess: 'Berjaya membuat resit Superbill',
	CreateTemplateFolderSuccessMessage: 'Berjaya cipta {folderTitle}',
	Created: 'Dicipta',
	CreatedAt: 'Dibuat {timestamp}',
	Credit: 'Kredit',
	CreditAdded: 'Kredit digunakan',
	CreditAdjustment: 'Pelarasan kredit',
	CreditAdjustmentReasonHelperText: 'Ini adalah nota dalaman dan tidak akan kelihatan kepada pelanggan anda.',
	CreditAdjustmentReasonPlaceholder:
		'Menambah sebab pelarasan boleh membantu semasa menyemak transaksi yang boleh dibilkan',
	CreditAmount: '{amount} NC',
	CreditBalance: 'Baki kredit',
	CreditCard: 'Kad kredit',
	CreditCardExpire: 'Tamat {exp_month}/{exp_year}',
	CreditCardNumber: 'Nombor kad kredit',
	CreditDebitCard: 'Kad',
	CreditIssued: 'Kredit dikeluarkan',
	CreditsUsed: 'Kredit yang digunakan',
	Crop: 'tanaman',
	Currency: 'mata wang',
	CurrentCredit: 'Kredit semasa',
	CurrentEventTime: 'Masa acara semasa',
	CurrentPlan: 'Pelan semasa',
	Custom: 'Adat',
	CustomRange: 'Julat tersuai',
	CustomRate: 'Kadar tersuai',
	CustomRecurrence: 'Kitaran tersuai',
	CustomServiceAvailability: 'Ketersediaan perkhidmatan',
	CustomerBalance: 'Baki pelanggan',
	CustomerName: 'Nama pelanggan',
	CustomerNameIsRequired: 'Nama pelanggan diperlukan',
	CustomerServiceRepresentative: 'Wakil Perkhidmatan Pelanggan',
	CustomiseAppointments: 'Sesuaikan janji temu',
	CustomiseBookingLink: 'Sesuaikan pilihan tempahan',
	CustomiseBookingLinkServicesInfo: 'Pelanggan hanya boleh memilih perkhidmatan yang boleh ditempah',
	CustomiseBookingLinkServicesLabel: 'Perkhidmatan',
	CustomiseClientRecordsAndWorkspace: 'Sesuaikan rekod pelanggan dan ruang kerja anda',
	CustomiseClientSettings: 'Sesuaikan tetapan klien',
	Customize: 'Sesuaikan',
	CustomizeAppearance: 'Sesuaikan penampilan',
	CustomizeAppearanceDesc:
		'Sesuaikan penampilan tempahan dalam talian anda agar sepadan dengan jenama anda dan pilih cara perkhidmatan anda dipaparkan kepada pelanggan.',
	CustomizeClientFields: 'Sesuaikan medan pelanggan',
	CustomizeInvoiceTemplate: 'Sesuaikan templat invois',
	CustomizeInvoiceTemplateDescription: 'Buat invois profesional dengan mudah yang mencerminkan jenama anda.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX diperlukan',
	Daily: 'Setiap hari',
	DanceTherapist: 'Jurupulih Tarian',
	DangerZone: 'Zon bahaya',
	Dashboard: 'Papan pemuka',
	Date: 'tarikh',
	DateAndTime: 'tarikh ',
	DateDue: 'Tarikh tamat tempoh',
	DateErrorMessage: 'Tarikh diperlukan',
	DateFormPrimaryText: 'tarikh',
	DateFormSecondaryText: 'Pilih daripada pemetik tarikh',
	DateIssued: 'Tarikh dikeluarkan',
	DateOfPayment: 'Tarikh pembayaran',
	DateOfService: 'Tarikh perkhidmatan',
	DateOverride: 'Ganti tarikh',
	DateOverrideColor: 'Warna ganti tarikh',
	DateOverrideInfo:
		'Penggantian tarikh membolehkan pengamal melaraskan ketersediaan mereka secara manual untuk tarikh tertentu dengan mengatasi jadual biasa.',
	DateOverrideInfoBanner:
		'Hanya perkhidmatan yang ditentukan untuk penggantian tarikh ini boleh ditempah dalam slot masa ini; tiada tempahan dalam talian lain dibenarkan.',
	DateOverrides: 'Tarikh menimpa',
	DatePickerFormPrimaryText: 'tarikh',
	DatePickerFormSecondaryText: 'Pilih tarikh',
	DateRange: 'Julat tarikh',
	DateRangeFormPrimaryText: 'Julat tarikh',
	DateRangeFormSecondaryText: 'Pilih julat tarikh',
	DateReceived: 'Tarikh diterima',
	DateSpecificHours: 'Tarikh waktu tertentu',
	DateSpecificHoursDescription:
		'Tambahkan tarikh apabila ketersediaan anda berubah daripada waktu perniagaan anda yang dijadualkan atau untuk menawarkan perkhidmatan pada tarikh tertentu.',
	DateUploaded: 'Dimuat naik {date, date, medium}',
	Dates: 'kurma',
	Daughter: 'anak perempuan',
	Day: 'Hari',
	DayPlural: '{count, plural, one {hari} other {hari}}',
	Days: 'hari-hari',
	DaysPlural: '{age, plural, one {# hari} other {# hari}}',
	DeFacto: 'De facto',
	Deactivated: 'Dinyahuiakan',
	Debit: 'Debit',
	DecreaseIndent: 'Kurangkan inden',
	Deductibles: 'boleh ditolak',
	Default: 'Lalai',
	DefaultBillingProfile: 'Profil pengebilan lalai',
	DefaultDescription: 'Perihalan lalai',
	DefaultEndOfLine: 'Tiada lagi barang',
	DefaultInPerson: 'Temu janji pelanggan',
	DefaultInvoiceTitle: 'Tajuk lalai',
	DefaultNotificationSubject: 'Anda telah menerima notifikasi baru untuk {notificationType}',
	DefaultPaymentMethod: 'Kaedah pembayaran lalai',
	DefaultService: 'Perkhidmatan lalai',
	DefaultValue: 'Lalai',
	DefaultVideo: 'E-mel janji temu video pelanggan',
	DefinedTemplateType: '{invoiceTemplate} templat',
	Delete: 'Padam',
	DeleteAccountButton: 'Padam akaun',
	DeleteAccountDescription: 'Padamkan akaun anda daripada platform',
	DeleteAccountPanelInfoAlert:
		'Anda mesti memadamkan ruang kerja anda sebelum memadamkan profil anda. Untuk meneruskan, tukar ke ruang kerja dan pilih Tetapan > Tetapan Ruang Kerja.',
	DeleteAccountTitle: 'Padam akaun',
	DeleteAppointment: 'Padamkan janji temu',
	DeleteAppointmentDescription:
		'Adakah anda pasti mahu memadamkan janji temu ini? Anda boleh memulihkannya kemudian.',
	DeleteAvailabilityScheduleFailure: 'Gagal memadamkan jadual ketersediaan',
	DeleteAvailabilityScheduleSuccess: 'Berjaya memadamkan jadual ketersediaan',
	DeleteBillable: 'Padam boleh dibilkan',
	DeleteBillableConfirmationMessage:
		'Adakah anda pasti mahu memadamkan boleh dibilkan ini? Tindakan ini tidak boleh dibuat asal.',
	DeleteBillingProfileConfirmationMessage: 'Ini akan mengalih keluar profil pengebilan secara kekal.',
	DeleteCardConfirmation:
		'Ini adalah tindakan kekal. Setelah kad dipadamkan, anda tidak akan dapat mengaksesnya lagi.',
	DeleteCategory: 'Padam kategori (ini tidak kekal melainkan perubahan disimpan)',
	DeleteClientEventConfirmationDescription: 'Ini akan dialih keluar secara kekal.',
	DeleteClients: 'Padamkan pelanggan',
	DeleteCollection: 'Padamkan Koleksi',
	DeleteColumn: 'Padam lajur',
	DeleteConversationConfirmationDescription:
		'Padamkan perbualan ini selama-lamanya. Tindakan ini tidak boleh dibuat asal.',
	DeleteConversationConfirmationTitle: 'Padamkan perbualan selama-lamanya',
	DeleteExternalEventDescription: 'Anda pasti ingin memadamkan temu janji ini?',
	DeleteFileConfirmationModalPrompt: 'Setelah dipadamkan anda tidak boleh mendapatkan semula fail ini.',
	DeleteFolder: 'Padam folder',
	DeleteFolderConfirmationMessage:
		'Anda pasti ingin memadamkan folder ini {name}? Semua item di dalam folder ini juga akan dipadamkan. Anda boleh memulihkannya kemudian.',
	DeleteForever: 'Padam selama-lamanya',
	DeleteInsurancePayerConfirmationMessage:
		'Mengelak {payer} akan memadamkannya daripada senarai pembayar insurans anda. Tindakan ini kekal dan tidak boleh dipulihkan.',
	DeleteInsurancePayerFailure: 'Gagal memadam pembayar insurans',
	DeleteInsurancePolicyConfirmationMessage: 'Ini akan mengalih keluar polisi insurans secara kekal.',
	DeleteInvoiceConfirmationDescription:
		'Tindakan ini tidak boleh dibuat asal. Ia akan memadamkan invois dan semua pembayaran yang berkaitan dengannya secara kekal.',
	DeleteLocationConfirmation:
		'Memadamkan lokasi ialah tindakan kekal. Sebaik sahaja anda memadamkannya, anda tidak akan mempunyai akses kepadanya lagi. Tindakan ini tidak boleh dibuat asal.',
	DeletePayer: 'Padamkan Pembayar',
	DeletePracticeWorkspace: 'Padamkan ruang kerja latihan',
	DeletePracticeWorkspaceDescription: 'Padamkan ruang kerja amalan ini secara kekal',
	DeletePracticeWorkspaceFailedSnackbar: 'Gagal memadamkan ruang kerja',
	DeletePracticeWorkspaceModalCancelButton: 'Ya, batalkan langganan saya',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Sebelum anda meneruskan pemadaman ruang kerja anda, anda mesti membatalkan langganan anda terlebih dahulu.',
	DeletePracticeWorkspaceModalConfirmButton: 'Ya, padamkan ruang kerja secara kekal',
	DeletePracticeWorkspaceModalDescription:
		'Ruang kerja {name} akan dipadamkan secara kekal dan semua ahli pasukan akan kehilangan akses. Muat turun sebarang data atau mesej penting yang anda mungkin perlukan sebelum pemadaman berlaku. Tindakan ini tidak boleh dibatalkan.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Keputusan ini dibuat kerana:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Sebab',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Sila beritahu kami sebab anda ingin memadamkan akaun anda.',
	DeletePracticeWorkspaceModalTitle: 'Adakah anda pasti?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Semua akses ahli pasukan telah dialih keluar',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} telah berjaya dipadamkan',
	DeletePublicTemplateContent: 'Ini hanya akan memadamkan templat awam dan bukan templat pasukan anda.',
	DeleteRecurringAppointmentModalTitle: 'Padamkan janji temu berulang',
	DeleteRecurringEventModalTitle: 'Padam mesyuarat berulang',
	DeleteRecurringReminderModalTitle: 'Padamkan peringatan berulang',
	DeleteRecurringTaskModalTitle: 'Padamkan tugasan yang berulang',
	DeleteReminderConfirmation:
		'Ini adalah tindakan kekal. Setelah peringatan dipadamkan, anda tidak akan dapat mengaksesnya lagi. Hanya akan menjejaskan pelantikan baharu',
	DeleteSection: 'Padam bahagian',
	DeleteSectionInfo:
		'Membuang bahagian **{section}** akan menyembunyikan semua medan sedia ada di dalamnya. Tindakan ini tidak boleh dibatalkan.',
	DeleteSectionWarning: 'Medan teras tidak boleh dipadamkan dan akan dipindahkan ke seksyen sedia ada **{section}**.',
	DeleteServiceFailure: 'Gagal memadamkan perkhidmatan',
	DeleteServiceSuccess: 'Berjaya memadamkan perkhidmatan',
	DeleteStaffScheduleOverrideDescription:
		'Menghapus tarikh ganti ini pada {value} akan mengalih keluarnya daripada jadual anda dan ia boleh mengubah perkhidmatan dalam talian anda yang tersedia. Tindakan ini tidak boleh dibatalkan.',
	DeleteSuperbillConfirmationDescription:
		'Tindakan ini tidak boleh dibuat asal. Ia akan memadamkan resit Superbill secara kekal.',
	DeleteSuperbillFailure: 'Gagal memadamkan resit Superbill',
	DeleteSuperbillSuccess: 'Berjaya memadamkan resit Superbill',
	DeleteTaxRateConfirmationDescription: 'Adakah anda pasti mahu memadamkan kadar cukai ini?',
	DeleteTemplateContent: 'Tindakan ini tidak boleh dibuat asal',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} berjaya dipadamkan',
	DeleteTemplateSuccessMessage: '{templateTitle} berjaya dipadamkan',
	DeleteTemplateTitle: 'Adakah anda pasti mahu memadamkan templat ini?',
	DeleteTranscript: 'Padamkan transkrip',
	DeleteWorkspace: 'Padamkan ruang kerja',
	Deleted: 'Dipadamkan',
	DeletedBy: 'Dipadamkan oleh',
	DeletedContact: 'Kenalan dipadamkan',
	DeletedOn: 'Dipadamkan pada',
	DeletedStatusLabel: 'Status dipadamkan',
	DeletedUserTooltip: 'Pelanggan ini telah dipadamkan',
	DeliveryMethod: 'Kaedah Penghantaran',
	Demo: 'Demo',
	Denied: 'dinafikan',
	Dental: 'Pergigian',
	DentalAssistant: 'Pembantu Pergigian',
	DentalHygienist: 'Pakar Kebersihan Pergigian',
	Dentist: 'Doktor Gigi',
	Dentists: 'Doktor gigi',
	Description: 'Penerangan',
	DescriptionMustNotExceed: 'Penerangan tidak boleh melebihi {max} aksara',
	DetailDurationWithStaff: '{duration} minit{staffName, select, null {} other { bersama {staffName}}}',
	Details: 'Butiran',
	Devices: 'Peranti',
	Diagnosis: 'Diagnosis',
	DiagnosisAndBillingItems: 'Diagnosis ',
	DiagnosisCode: 'Kod diagnosis',
	DiagnosisCodeErrorMessage: 'Kod diagnosis diperlukan',
	DiagnosisCodeSelectorPlaceholder: 'Cari dan tambah daripada kod diagnostik ICD-10',
	DiagnosisCodeSelectorTooltip:
		'Kod diagnosis digunakan untuk mengautomasikan resit superbills untuk pembayaran balik insurans',
	DiagnosticCodes: 'Kod diagnostik',
	Dictate: 'didikte',
	DictatingIn: 'Mengimlak masuk',
	Dictation: 'Dikte',
	DidNotAttend: 'Tidak hadir',
	DidNotComplete: 'Tidak Lengkap',
	DidNotProviderEnoughValue: 'Tidak memberikan nilai yang mencukupi',
	DidntProvideEnoughValue: 'Tidak memberikan nilai yang mencukupi',
	DieteticsOrNutrition: 'Dietetik atau pemakanan',
	Dietician: 'Pakar pemakanan',
	Dieticians: 'Pakar pemakanan',
	Dietitian: 'Pakar pemakanan',
	DigitalSign: 'Tandatangan di sini:',
	DigitalSignHelp: '(Klik/tekan ke bawah untuk melukis)',
	DirectDebit: 'Debit langsung',
	DirectTextLink: 'Pautan teks langsung',
	Disable: 'Lumpuhkan',
	DisabledEmailInfo: 'Kami tidak boleh mengemas kini alamat e-mel anda kerana akaun anda tidak diuruskan oleh kami',
	Discard: 'Buang',
	DiscardChanges: 'Buang perubahan',
	DiscardDrafts: 'Buang draf',
	Disconnect: 'Putuskan sambungan',
	DisconnectAppConfirmation: 'Adakah anda mahu memutuskan sambungan apl ini?',
	DisconnectAppConfirmationDescription: 'Anda pasti ingin memutuskan sambungan aplikasi ini?',
	DisconnectAppConfirmationTitle: 'Putuskan sambungan aplikasi',
	Discount: 'Diskaun',
	DisplayCalendar: 'Paparkan dalam Carepatron',
	DisplayName: 'Nama paparan',
	DisplayedToClients: 'Dipaparkan kepada pelanggan',
	DiversionalTherapist: 'Jurupulih Lencongan',
	DoItLater: 'Buat nanti',
	DoNotImport: 'Jangan import',
	DoNotSend: 'Jangan hantar',
	DoThisLater: 'Buat ini kemudian',
	DoYouWantToEndSession: 'Adakah anda mahu meneruskan, atau menamatkan sesi anda sekarang?',
	Doctor: 'Doktor',
	Doctors: 'Doktor',
	DoesNotRepeat: 'Tidak berulang',
	DoesntWorkWellWithExistingTools: 'Tidak berfungsi dengan baik dengan alatan atau aliran kerja sedia ada kami',
	DogWalker: 'Pejalan Anjing',
	Done: 'Selesai',
	DontAllowClientsToCancel: 'Jangan benarkan pelanggan membatalkan',
	DontHaveAccount: 'Tiada akaun?',
	DontSend: 'Jangan hantar',
	Double: 'berganda',
	DowngradeTo: 'Turun taraf ke {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Maaf, anda tidak boleh menurunkan taraf pelan anda kerana anda mempunyai terlalu ramai ahli pasukan. Sila alih keluar beberapa daripada pembekal anda dan cuba lagi.',
	Download: 'Muat turun',
	DownloadAsPdf: 'Muat turun sebagai PDF',
	DownloadERA: 'Muat Turun ERA',
	DownloadPDF: 'Muat turun PDF',
	DownloadTemplateFileName: 'Templat Peralihan Carepatron.csv',
	DownloadTemplateTileDescription: 'Gunakan templat hamparan kami untuk menganjurkan dan memuat naik pelanggan anda.',
	DownloadTemplateTileLabel: 'Muat turun templat',
	Downloads: '{number, plural, one {<span>#</span> Muat turun} other {<span>#</span> Muat turun}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Draf',
	DraftResponses: 'Draf respons',
	DraftSaved: 'Perubahan disimpan',
	DragAndDrop: 'seret dan lepas',
	DragDropText: 'Seret dan lepas dokumen kesihatan',
	DragToMove: 'Seret untuk bergerak',
	DragToMoveOrActivate: 'Seret untuk mengalih atau mengaktifkan',
	DramaTherapist: 'Jurupulih Drama',
	DropdownFormFieldPlaceHolder: 'Pilih pilihan daripada senarai',
	DropdownFormPrimaryText: 'Jatuh turun',
	DropdownFormSecondaryText: 'Pilih daripada senarai pilihan',
	DropdownTextFieldError: 'Teks pilihan lungsur turun tidak boleh kosong',
	DropdownTextFieldPlaceholder: 'Tambah pilihan lungsur turun',
	Due: 'Tarikh tamat',
	DueDate: 'tarikh tamat tempoh',
	Duplicate: 'Pendua',
	DuplicateAvailabilityScheduleFailure: 'Gagal menduplikasi jadual ketersediaan',
	DuplicateAvailabilityScheduleSuccess: 'Berjaya meniru jadual {name}',
	DuplicateClientBannerAction: 'Semakan',
	DuplicateClientBannerDescription:
		'Menggabungkan rekod pelanggan pendua menyatukannya menjadi satu, menyimpan semua maklumat pelanggan yang unik.',
	DuplicateClientBannerTitle: '{count} Salinan dijumpai',
	DuplicateColumn: 'Lajur pendua',
	DuplicateContactFieldSettingErrorSnackbar: 'Tidak boleh mempunyai nama bahagian pendua',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Tidak boleh mempunyai nama medan pendua',
	DuplicateEmailError: 'E-mel pendua',
	DuplicateHeadingName: 'Bahagian {name} sudah wujud',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Invois dengan "nombor invois" yang sama sudah wujud.',
	DuplicateRecords: 'Rekod pendua',
	DuplicateRecordsMinimumError: 'Minimum 2 rekod mesti dipilih',
	DuplicateRecordsRequired: 'Pilih sekurang-kurangnya 1 rekod untuk diasingkan',
	DuplicateServiceFailure: 'Gagal untuk menduplikasi **{title}**',
	DuplicateServiceSuccess: 'Berjaya meniru <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Berjaya menduplikasi folder',
	DuplicateTemplateSuccess: 'Berjaya menduplikasi templat',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'kod DX',
	DxCodeSelectPlaceholder: 'Cari dan tambah daripada kod ICD-10',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'tiada',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>mengandungi pelarasan</i>} other {}}',
	EarnReferralCredit: 'Dapatkan ${creditAmount}',
	Economist: 'Ahli ekonomi',
	Edit: 'Sunting',
	EditArrangements: 'Edit susunan',
	EditBillTo: 'Edit Bil ke',
	EditClient: 'Edit Klien',
	EditClientFileModalDescription:
		'Edit akses kepada fail ini dengan memilih pilihan dalam kotak pilihan "Boleh dilihat oleh".',
	EditClientFileModalTitle: 'Edit fail',
	EditClientNoteModalDescription:
		'Edit kandungan dalam nota. Gunakan bahagian "Boleh dilihat oleh" untuk menukar orang yang boleh melihat nota itu.',
	EditClientNoteModalTitle: 'Edit nota',
	EditConnectedAppButton: 'Sunting',
	EditConnections: 'Edit sambungan{account, select, null { } undefined { } other { untuk {account}}}',
	EditContactDetails: 'Edit butiran hubungan',
	EditContactFormIsClientLabel: 'Tukar kepada pelanggan',
	EditContactIsClientCheckboxWarning: 'Menukar kenalan kepada pelanggan tidak boleh dibuat asal',
	EditContactIsClientWanringModal:
		'Menukar kenalan ini kepada Pelanggan tidak boleh dibuat asal. Walau bagaimanapun, semua perhubungan masih akan kekal dan anda kini akan mempunyai akses kepada nota, fail dan dokumentasi lain.',
	EditContactRelationship: 'Edit perhubungan kenalan',
	EditDetails: 'Edit butiran',
	EditFileModalTitle: 'Edit fail untuk {name}',
	EditFolder: 'Edit folder',
	EditFolderDescription: 'Namakan semula folder sebagai...',
	EditInvoice: 'Edit invois',
	EditInvoiceDetails: 'Edit butiran Invois',
	EditLink: 'Edit Pautan',
	EditLocation: 'Edit lokasi',
	EditLocationFailure: 'Gagal mengemas kini lokasi',
	EditLocationSucess: 'Berjaya mengemas kini lokasi',
	EditPaymentDetails: 'Edit butiran pembayaran',
	EditPaymentMethod: 'Edit kaedah pembayaran',
	EditPersonalDetails: 'Edit butiran peribadi',
	EditPractitioner: 'Edit Pengamal',
	EditProvider: 'Edit Pembekal',
	EditProviderDetails: 'Edit butiran pembekal',
	EditRecurrence: 'Edit ulang',
	EditRecurringAppointmentModalTitle: 'Edit janji temu berulang',
	EditRecurringEventModalTitle: 'Edit mesyuarat berulang',
	EditRecurringReminderModalTitle: 'Edit peringatan berulang',
	EditRecurringTaskModalTitle: 'Edit tugasan berulang',
	EditRelationshipModalTitle: 'Edit perhubungan',
	EditService: 'Edit perkhidmatan',
	EditServiceFailure: 'Gagal mengemas kini perkhidmatan baharu',
	EditServiceGroup: 'Edit koleksi',
	EditServiceGroupFailure: 'Gagal mengemas kini koleksi',
	EditServiceGroupSuccess: 'Berjaya mengemas kini koleksi',
	EditServiceSuccess: 'Berjaya mengemas kini perkhidmatan baharu',
	EditStaffDetails: 'Edit butiran kakitangan',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Tidak dapat mengemas kini alamat e-mel. Sila buat ahli pasukan baharu dengan alamat e-mel baharu.',
	EditSubscriptionBilledQuantity: 'Kuantiti yang Dibilkan',
	EditSubscriptionBilledQuantityValue: '{billedUsers} ahli pasukan',
	EditSubscriptionLimitedTimeOffer: 'Tawaran terhad! Diskaun 50% selama 6 bulan.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Kos subscription anda akan diselaraskan apabila menambah atau mengalih keluar ahli pasukan.',
	EditSubscriptionUpgradeContent:
		'Akaun anda akan dikemas kini serta-merta kepada pelan dan tempoh bil baharu. Sebarang perubahan harga akan dikenakan secara automatik kepada kaedah pembayaran yang disimpan atau dikreditkan kepada akaun anda.',
	EditSubscriptionUpgradePlanTitle: 'Tingkatkan pelan langganan',
	EditSuperbillReceipt: 'Edit superbill',
	EditTags: 'Edit tag',
	EditTemplate: 'Edit Templat',
	EditTemplateFolderSuccessMessage: 'Folder templat berjaya dikemas kini',
	EditValue: 'Edit {value}',
	Edited: 'disunting',
	Editor: 'Editor',
	EditorAlertDescription:
		'Format yang tidak disokong telah dikesan. Muat semula apl atau hubungi pasukan sokongan kami.',
	EditorAlertTitle: 'Kami menghadapi masalah memaparkan kandungan ini',
	EditorPlaceholder:
		'Mula menulis, pilih templat atau tambah blok asas untuk menangkap jawapan daripada pelanggan anda.',
	EditorTemplatePlaceholder: 'Mula menulis atau menambah komponen untuk membina templat',
	EditorTemplateWithSlashCommandPlaceholder:
		'Mula menulis atau tambah blok asas untuk merakam respons pelanggan. Gunakan arahan serong (/) untuk tindakan pantas.',
	EditorWithSlashCommandPlaceholder:
		'Mula menulis, pilih templat atau tambah blok asas untuk menangkap respons pelanggan. Gunakan arahan slash ( / ) untuk tindakan pantas.',
	EffectiveStartEndDate: 'Mula berkesan - tarikh tamat',
	ElectricalEngineer: 'Jurutera Elektrik',
	Electronic: 'Elektronik',
	ElectronicSignature: 'Tandatangan elektronik',
	ElementarySchoolTeacher: 'Guru Sekolah Rendah',
	Eligibility: 'Kelayakan',
	Email: 'E-mel',
	EmailAlreadyExists: 'Alamat e-mel sudah wujud',
	EmailAndSms: 'E-mel ',
	EmailBody: 'Badan e-mel',
	EmailContainsIgnoredDescription:
		'E-mel berikut mengandungi e-mel pengirim yang diabaikan pada masa ini. Adakah anda mahu meneruskan?',
	EmailInviteToPortalBody: `Salam {contactName},
Sila ikuti pautan ini untuk log masuk ke portal pelanggan selamat anda dan uruskan penjagaan anda dengan mudah.

Hormat kami,

{providerName}`,
	EmailInviteToPortalSubject: 'Selamat datang ke {providerName}',
	EmailInvoice: 'Invois e-mel',
	EmailInvoiceOverdueBody: `Hai {contactName}
Bil anda invois anda {invoiceNumber} sudah lewat tempo.
Silakan bayar invois anda secara online menggunakan tautan di bawah.

Jika ada pertanyaan, silakan hubungi kami.

Terima kasih,
{providerName}`,
	EmailInvoicePaidBody: `Hai {contactName}
Bil anda anda telah membuat pembayaran bagi invois {invoiceNumber}.
Untuk melihat dan memuat turun salinan invois anda, sila ikuti pautan di bawah.

Jika anda mempunyai sebarang pertanyaan, sila hubungi kami.

Terima kasih,
{providerName}`,
	EmailInvoiceProcessingBody: `Hai {contactName}
Bil invois anda {invoiceNumber} anda sedia.
Ikut pautan di bawah untuk melihat invois anda.

Jika anda ada sebarang pertanyaan, sila maklumkan kami.

Terima kasih,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hi {contactName}
Invoice {invoiceNumber} anda sudah siap, dan perlu dibayar sebelum {dueDate}.
Untuk melihat dan membayar invois anda secara online, silakan klik pada pautan di bawah.

Jika anda mempunyai sebarang soalan, sila maklumkan kepada kami.

Terima kasih,
{providerName}`,
	EmailInvoiceVoidBody: `Salam {contactName}
Bil {invoiceNumber} anda telah dibatalkan.
Untuk melihat bil ini, sila ikuti pautan di bawah.

Jika anda mempunyai sebarang pertanyaan, sila beritahu kami.

Terima kasih,
{providerName}`,
	EmailNotFound: 'E-mel tidak ditemui',
	EmailNotVerifiedErrorCodeSnackbar: 'Tidak dapat melakukan tindakan. Anda perlu mengesahkan alamat e-mel anda.',
	EmailNotVerifiedTitle: 'E-mel anda tidak disahkan. Sesetengah ciri akan terhad.',
	EmailSendClientIntakeBody: `Hi {contactName},
{providerName} ingin anda untuk memberikan beberapa maklumat dan menyemak dokumen penting. Sila ikuti pautan di bawah untuk memulakan.

Hormat kami,

{providerName}`,
	EmailSendClientIntakeSubject: 'Selamat datang ke {providerName}',
	EmailSuperbillReceipt: 'E-mel superbill',
	EmailSuperbillReceiptBody: `Hi {contactName},
{providerName} telah menghantar salinan penyata resit bayaran balik anda {date}.

Anda boleh muat turun dan hantar terus kepada syarikat insurans anda.`,
	EmailSuperbillReceiptFailure: 'Gagal menghantar resit Superbill',
	EmailSuperbillReceiptSubject: '{providerName} telah menghantar penyata penerimaan bayaran balik',
	EmailSuperbillReceiptSuccess: 'Berjaya menghantar resit Superbill',
	EmailVerificationDescription: 'Kami sedang <span>mengesahkan</span> akaun anda sekarang',
	EmailVerificationNotification: 'Sebuah emel pengesahan telah dihantar ke {email}',
	EmailVerificationSuccess: 'Alamat e-mel anda telah berjaya ditukar kepada {email}',
	Emails: 'E-mel',
	EmergencyContact: 'Hubungan kecemasan',
	EmployeesIdentificationNumber: 'Nombor pengenalan pekerja',
	EmploymentStatus: 'Status Pekerjaan',
	EmptyAgendaViewDescription: 'Tiada acara untuk dipaparkan.<mark> Buat janji temu sekarang</mark>',
	EmptyBin: 'Tong kosong',
	EmptyBinConfirmationDescription:
		'Bakul kosong akan memadamkan semua **{total} perbualan** dalam Dihapuskan. Tindakan ini tidak boleh dibatalkan.',
	EmptyBinConfirmationTitle: 'Padamkan perbualan selama-lamanya',
	EmptyTrash: 'Sampah kosong',
	Enable: 'Dayakan',
	EnableCustomServiceAvailability: 'Dayakan ketersediaan perkhidmatan',
	EnableCustomServiceAvailabilityDescription: 'Cth janji temu awal hanya boleh ditempah setiap hari dari 9-10 pagi',
	EndCall: 'Tamatkan panggilan',
	EndCallConfirmationForCreator: 'Anda akan menamatkan ini untuk semua orang kerana anda adalah pemula panggilan.',
	EndCallConfirmationHasActiveAttendees:
		'Anda akan menamatkan panggilan tetapi pelanggan telah pun menyertai. Adakah anda juga ingin menyertai?',
	EndCallForAll: 'Tamatkan panggilan untuk semua orang',
	EndDate: 'Tarikh tamat',
	EndDictation: 'Tamat imlak',
	EndOfLine: 'Tiada lagi temu janji',
	EndSession: 'Tamat sesi',
	EndTranscription: 'Tamatkan transkripsi',
	Ends: 'Berakhir',
	EndsOnDate: 'Berakhir pada {date}',
	Enrol: 'Daftar',
	EnrollmentRejectedSubject: 'Pendaftaran anda dengan {payerName} telah ditolak',
	Enrolment: 'Pengambilan',
	Enrolments: 'Pendaftaran',
	EnrolmentsDescription: 'Lihat dan urus pendaftaran penyedia dengan penanggung insurans.',
	EnterAName: 'Masukkan nama...',
	EnterFieldLabel: 'Masukkan label medan...',
	EnterPaymentDetailsDescription:
		'Kos langganan anda akan dilaraskan secara automatik apabila menambah atau mengalih keluar pengguna.',
	EnterSectionName: 'Masukkan nama bahagian...',
	EnterSubscriptionPaymentDetails: 'Masukkan butiran pembayaran',
	EnvironmentalScientist: 'Saintis Alam Sekitar',
	Epidemiologist: 'Pakar epidemiologi',
	Eraser: 'Pemadam',
	Error: 'ralat',
	ErrorBoundaryAction: 'Muat semula halaman',
	ErrorBoundaryDescription: 'Sila muat semula halaman dan cuba lagi.',
	ErrorBoundaryTitle: 'Alamak! Sesuatu telah berlaku',
	ErrorCallNotFound:
		'Panggilan tidak dapat ditemui. Ia mungkin telah tamat tempoh atau pencipta telah menamatkannya.',
	ErrorCannotAccessCallUninvitedCode: 'Maaf, nampaknya anda tidak dijemput ke panggilan ini.',
	ErrorFileUploadCustomMaxFileCount: 'Tidak boleh muat naik lebih daripada {count} fail pada satu masa',
	ErrorFileUploadCustomMaxFileSize: 'Saiz fail tidak boleh melebihi {mb} MB',
	ErrorFileUploadInvalidFileType:
		'Jenis fail tidak sah yang mungkin mengandungi virus yang berpotensi dan perisian berbahaya',
	ErrorFileUploadMaxFileCount: 'Tidak boleh memuat naik lebih daripada 150 fail sekali gus',
	ErrorFileUploadMaxFileSize: 'Saiz fail tidak boleh melebihi 100 MB',
	ErrorFileUploadNoFileSelected: 'Sila pilih fail untuk dimuat naik',
	ErrorInvalidNationalProviderId: 'ID Penyedia Kebangsaan yang diberikan tidak sah',
	ErrorInvalidPayerId: 'ID Pembayar yang diberikan tidak sah',
	ErrorInvalidTaxNumber: 'Nombor Cukai yang diberikan tidak sah',
	ErrorInviteExistingProviderStaffCode: 'Pengguna ini sudah berada di ruang kerja.',
	ErrorInviteStaffExistingUser: 'Maaf, nampaknya pengguna yang anda tambahkan sudah wujud dalam sistem kami.',
	ErrorOnlySingleCallAllowed:
		'Anda hanya boleh mempunyai satu panggilan pada satu masa. Sila tamatkan panggilan semasa untuk memulakan panggilan baharu.',
	ErrorPayerNotFound: 'Pembayar tidak ditemui',
	ErrorProfilePhotoMaxFileSize: 'Muat naik gagal! Had saiz fail dicapai - 5MB',
	ErrorRegisteredExistingUser: 'Maaf, nampaknya anda sudah berdaftar.',
	ErrorUserSignInIncorrectCredentials: 'E-mel atau kata laluan tidak sah. Sila cuba lagi.',
	ErrorUserSigninGeneric: 'Maaf, berlaku kesilapan.',
	ErrorUserSigninUserNotConfirmed:
		'Maaf, anda perlu mengesahkan akaun anda sebelum melog masuk. Semak peti masuk anda untuk mendapatkan arahan.',
	Errors: 'Ralat',
	EssentialPlanInclusionFive: 'Import templat',
	EssentialPlanInclusionFour: '5 GB storan',
	EssentialPlanInclusionHeader: 'Semuanya dalam Percuma  ',
	EssentialPlanInclusionOne: 'Automatik dan peringatan tersuai',
	EssentialPlanInclusionSix: 'Sokongan keutamaan',
	EssentialPlanInclusionThree: 'Sembang video',
	EssentialPlanInclusionTwo: 'penyegerakan kalendar 2 hala',
	EssentialSubscriptionPlanSubtitle: 'Permudahkan amalan anda dengan yang penting',
	EssentialSubscriptionPlanTitle: 'Penting',
	Esthetician: 'Pakar Estetik',
	Estheticians: 'Pakar Estetik',
	EstimatedArrivalDate: 'Anggaran Ketibaan {numberOfDaysFromNow}',
	Ethnicity: 'Etnik',
	Europe: 'Eropah',
	EventColor: 'Warna mesyuarat',
	EventName: 'Nama acara',
	EventType: 'Jenis acara',
	Every: 'Setiap',
	Every2Weeks: 'Setiap 2 minggu',
	EveryoneInWorkspace: 'Semua orang di ruang kerja',
	ExercisePhysiologist: 'Pakar Fisiologi Senaman',
	Existing: 'sedia ada',
	ExistingClients: 'Pelanggan sedia ada',
	ExistingFolders: 'Folder sedia ada',
	ExpiredPromotionCode: 'Kod promosi telah tamat tempoh',
	ExpiredReferralDescription: 'Rujukan telah tamat tempoh',
	ExpiredVerificationLink: 'Pautan pengesahan tamat tempoh',
	ExpiredVerificationLinkDescription: `Harap maaf, tetapi pautan pengesahan yang anda klik telah tamat tempoh. Ini boleh berlaku jika anda menunggu lebih daripada 24 jam untuk mengklik pada pautan atau jika anda telah menggunakan pautan untuk mengesahkan alamat e-mel anda.

 Sila minta pautan pengesahan baharu untuk mengesahkan alamat e-mel anda.`,
	ExpiryDateRequired: 'Tarikh luput diperlukan',
	ExploreFeature: 'Apakah yang anda ingin terokai dahulu?',
	ExploreOptions: 'Pilih satu atau lebih pilihan untuk diterokai...',
	Export: 'Eksport',
	ExportAppointments: 'Eksport janji temu',
	ExportClaims: 'Eksport tuntutan',
	ExportClaimsFilename: 'Tuntutan {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Fail anda tidak dapat dimuat turun kerana ralat.',
	ExportClientsDownloadFailureSnackbarTitle: 'Muat turun gagal',
	ExportClientsFailureSnackbarDescription: 'Fail anda tidak berjaya dieksport kerana ralat.',
	ExportClientsFailureSnackbarTitle: 'Eksport gagal',
	ExportClientsModalDescription: `Proses eksport data ini mungkin mengambil masa beberapa minit bergantung pada jumlah data yang dieksport. Anda akan menerima pemberitahuan e-mel dengan pautan setelah ia sedia untuk dimuat turun.

 Adakah anda ingin meneruskan dengan mengeksport data pelanggan?`,
	ExportClientsModalTitle: 'Eksport data pelanggan',
	ExportCms1500: 'Eksport CMS1500',
	ExportContactFailedNotificationSubject: 'Eksport data anda telah gagal',
	ExportFailed: 'Eksport gagal',
	ExportGuide: 'Panduan eksport',
	ExportInvoiceFileName: 'Transaksi {fromDate}-{toDate}.csv',
	ExportPayments: 'Pembayaran Eksport',
	ExportPaymentsFilename: 'Bayaran {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Dokumen anda sedia untuk dimuat turun.',
	ExportPrintWaitMessage: 'Menyediakan dokumen anda. Sila tunggu...',
	ExportTextOnly: 'Eksport teks sahaja',
	ExportTransactions: 'Urus niaga eksport',
	Exporting: 'Mengeksport',
	ExportingData: 'Mengeksport data',
	ExtendedFamilyMember: 'Ahli keluarga lanjutan',
	External: 'Luaran',
	ExternalEventInfoBanner: 'Temujanji ini adalah daripada kalendar yang disegerakkan dan mungkin kekurangan item.',
	ExtraLarge: 'Lebih besar',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'gagal',
	FailedToJoinTheMeeting: 'Gagal menyertai mesyuarat.',
	FallbackPageDescription: `Kelihatan seperti halaman ini tidak wujud, anda mungkin perlu {refreshButton} halaman ini untuk mendapatkan perubahan terkini.
Jika tidak, sila hubungi sokongan Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'segarkan semula',
	FallbackPageTitle: 'Aduh...',
	FamilyPlanningService: 'Perkhidmatan perancangan keluarga',
	FashionDesigner: 'Pereka Fesyen',
	FastTrackInvoicingAndBilling: 'Jejaki invois dan pengebilan anda dengan pantas',
	Father: 'Bapa',
	FatherInLaw: 'Bapa mertua',
	Favorite: 'Kegemaran',
	FeatureBannerCalendarTile1ActionLabel: 'Tempahan dalam talian • 2 minit',
	FeatureBannerCalendarTile1Description: 'Hanya e-mel, teks atau tambahkan ketersediaan pada tapak web anda',
	FeatureBannerCalendarTile1Title: 'Membolehkan pelanggan anda membuat tempahan dalam talian',
	FeatureBannerCalendarTile2ActionLabel: 'Automatikkan peringatan • 2 minit',
	FeatureBannerCalendarTile2Description: 'Tingkatkan kehadiran pelanggan dengan peringatan automatik',
	FeatureBannerCalendarTile2Title: 'Kurangkan tidak hadir',
	FeatureBannerCalendarTile3Title: 'Penjadualan dan Aliran Kerja',
	FeatureBannerCalendarTitle: 'Jadikan penjadualan mudah',
	FeatureBannerCallsTile1ActionLabel: 'Mulakan panggilan telekesihatan',
	FeatureBannerCallsTile1Description:
		'Akses pelanggan dengan hanya pautan. Tiada log masuk, kata laluan atau kerumitan',
	FeatureBannerCallsTile1Title: 'Mulakan panggilan video dari mana-mana sahaja',
	FeatureBannerCallsTile2ActionLabel: 'Sambungkan apl • 4 minit',
	FeatureBannerCallsTile2Description: 'Sambungkan penyedia telekesihatan pilihan lain dengan lancar',
	FeatureBannerCallsTile2Title: 'Sambungkan apl telekesihatan anda',
	FeatureBannerCallsTile3Title: 'Panggilan',
	FeatureBannerCallsTitle: 'Berhubung dengan pelanggan — Di Mana-mana, Pada Bila-bila Masa',
	FeatureBannerClientsTile1ActionLabel: 'Import sekarang • 2 minit',
	FeatureBannerClientsTile1Description: 'Bermula dengan cepat menggunakan alat import pelanggan automatik kami',
	FeatureBannerClientsTile1Title: 'Mempunyai ramai pelanggan?',
	FeatureBannerClientsTile2ActionLabel: 'Sesuaikan pengambilan • 2 minit',
	FeatureBannerClientsTile2Description: 'Keluarkan kertas kerja pengambilan dan tingkatkan pengalaman pelanggan',
	FeatureBannerClientsTile2Title: 'Pergi tanpa kertas',
	FeatureBannerClientsTile3Title: 'Portal Pelanggan',
	FeatureBannerClientsTitle: 'Semuanya bermula dengan pelanggan anda',
	FeatureBannerHeader: 'Oleh Komuniti, untuk Komuniti!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatikkan pembayaran • 2 minit',
	FeatureBannerInvoicesTile1Description: 'Elakkan perbualan janggal dengan pembayaran automatik',
	FeatureBannerInvoicesTile1Title: 'Dapat gaji 2x ganda lebih cepat',
	FeatureBannerInvoicesTile2ActionLabel: 'Jejaki aliran tunai • 2 minit',
	FeatureBannerInvoicesTile2Description: 'Kurangkan invois yang belum dibayar dan pantau pendapatan anda',
	FeatureBannerInvoicesTile2Title: 'Jejaki pendapatan anda, tanpa rasa sakit',
	FeatureBannerInvoicesTile3Title: 'Pengebilan dan Pembayaran',
	FeatureBannerInvoicesTitle: 'Kurang satu perkara yang perlu dibimbangkan',
	FeatureBannerSubheader:
		'Templat Carepatron yang dibuat oleh pasukan dan komuniti kami. Cuba sumber baharu atau kongsi sumber anda sendiri!',
	FeatureBannerTeamTile1ActionLabel: 'Jemput sekarang',
	FeatureBannerTeamTile1Description: 'Jemput ahli pasukan ke akaun anda dan permudahkan kerjasama',
	FeatureBannerTeamTile1Title: 'Bawa pasukan anda bersama-sama',
	FeatureBannerTeamTile2ActionLabel: 'Tetapkan ketersediaan • 2 minit',
	FeatureBannerTeamTile2Description: 'Urus ketersediaan pasukan anda untuk mengelakkan tempahan dua kali',
	FeatureBannerTeamTile2Title: 'Tetapkan ketersediaan anda',
	FeatureBannerTeamTile3ActionLabel: 'Tetapkan kebenaran • 2 minit',
	FeatureBannerTeamTile3Description: 'Kawal akses kepada data dan alatan sensitif untuk pematuhan',
	FeatureBannerTeamTile3Title: 'Sesuaikan kebenaran dan akses',
	FeatureBannerTeamTitle: 'Tiada sesuatu yang hebat dicapai seorang diri',
	FeatureBannerTemplatesTile1ActionLabel: 'Terokai perpustakaan • 2 minit',
	FeatureBannerTemplatesTile1Description:
		'Pilih daripada perpustakaan sumber yang boleh disesuaikan yang menakjubkan ',
	FeatureBannerTemplatesTile1Title: 'Kurangkan beban kerja anda',
	FeatureBannerTemplatesTile2ActionLabel: 'Hantar sekarang • 2 minit',
	FeatureBannerTemplatesTile2Description: 'Hantar templat yang cantik kepada pelanggan untuk disiapkan',
	FeatureBannerTemplatesTile2Title: 'Jadikan dokumentasi menyeronokkan',
	FeatureBannerTemplatesTile3Title: 'templat',
	FeatureBannerTemplatesTitle: 'Templat untuk apa sahaja',
	FeatureLimitBannerDescription:
		'Upgrade sekarang untuk terus mencipta dan mengurus {featureName} tanpa gangguan dan dapatkan pengalaman terbaik dengan Carepatron!',
	FeatureLimitBannerTitle: 'Anda {percentage}% daripada had {featureName} anda',
	FeatureRequiresUpgrade: 'Ciri ini memerlukan peningkatan',
	Fee: 'Yuran',
	Female: 'perempuan',
	FieldLabelTooltip: '{isHidden, select, true {Tunjukkan} other {Sembunyikan}} label medan',
	FieldName: 'Nama medan',
	FieldOptionsFirstPart: 'Perkataan pertama',
	FieldOptionsMiddlePart: 'perkataan tengah',
	FieldOptionsSecondPart: 'Kata terakhir',
	FieldOptionsWholeField: 'Seluruh bidang',
	FieldType: 'Jenis medan',
	Fields: 'Padang',
	File: 'Fail',
	FileDownloaded: '<strong>{fileName}</strong> dimuat turun',
	FileInvalidType: 'Fail tidak disokong.',
	FileNotFound: 'Fail tidak ditemui',
	FileNotFoundDescription: 'Fail yang anda cari tidak tersedia atau telah dipadamkan',
	FileTags: 'Tag fail',
	FileTagsHelper: 'Teg akan digunakan pada semua fail',
	FileTooLarge: 'Fail terlalu besar.',
	FileTooSmall: 'Fail terlalu kecil.',
	FileUploadComplete: 'lengkap',
	FileUploadFailed: 'gagal',
	FileUploadInProgress: 'Memuatkan',
	FileUploadedNotificationSubject: '{actorProfileName} telah memuat naik sebuah fail',
	Files: 'Fail',
	FillOut: 'Isi',
	Filter: 'Penapis',
	FilterBy: 'Tapis mengikut',
	FilterByAmount: 'Tapis mengikut jumlah',
	FilterByClient: 'Tapis mengikut pelanggan',
	FilterByLocation: 'Tapis mengikut lokasi',
	FilterByService: 'Tapis mengikut perkhidmatan',
	FilterByStatus: 'Tapis mengikut status',
	FilterByTags: 'Tapis mengikut tag',
	FilterByTeam: 'Tapis mengikut pasukan',
	Filters: 'Penapis',
	FiltersAppliedToView: 'Penapis digunakan pada paparan',
	FinalAppointment: 'Pelantikan Terakhir',
	FinalizeImport: 'Selesaikan import',
	FinancialAnalyst: 'Penganalisis Kewangan',
	Finish: 'Selesai',
	Firefighter: 'anggota bomba',
	FirstName: 'nama pertama',
	FirstNameLastInitial: 'Nama pertama, nama akhir',
	FirstPerson: 'orang pertama',
	FolderName: 'Nama folder',
	Folders: 'Folder',
	FontFamily: 'Keluarga fon',
	ForClients: 'Untuk pelanggan',
	ForClientsDetails: 'Saya menerima perkhidmatan penjagaan atau kesihatan yang berkaitan',
	ForPractitioners: 'Untuk pengamal',
	ForPractitionersDetails: 'Urus dan kembangkan amalan anda',
	ForgotPasswordConfirmAccessCode: 'Kod pengesahan',
	ForgotPasswordConfirmNewPassword: 'Kata laluan baharu',
	ForgotPasswordConfirmPageDescription:
		'Sila masukkan alamat e-mel anda, kata laluan baharu dan kod pengesahan yang baru kami hantar kepada anda.',
	ForgotPasswordConfirmPageTitle: 'Tetapkan semula kata laluan',
	ForgotPasswordPageButton: 'Hantar pautan set semula',
	ForgotPasswordPageDescription:
		'Masukkan e-mel anda dan kami akan menghantar pautan kepada anda untuk menetapkan semula kata laluan anda.',
	ForgotPasswordPageTitle: 'Kata laluan terlupa',
	ForgotPasswordSuccessPageDescription: 'Semak peti masuk anda untuk pautan tetapan semula anda.',
	ForgotPasswordSuccessPageTitle: 'Tetapkan semula pautan dihantar!',
	Form: 'Borang',
	FormAnswersSentToEmailNotification: 'Kami telah menghantar salinan jawapan anda kepada',
	FormBlocks: 'Borang blok',
	FormFieldAddOption: 'Tambah pilihan',
	FormFieldAddOtherOption: 'Tambah "lain"',
	FormFieldOptionPlaceholder: 'Pilihan {index}',
	FormStructures: 'Membentuk struktur',
	Format: 'Format',
	FormatLinkButtonColor: 'Warna Butang',
	Forms: 'Borang',
	FormsAndAgreementsValidationMessage:
		'Semua borang dan perjanjian mesti dilengkapkan untuk meneruskan proses pengambilan.',
	FormsCategoryDescription: 'Untuk mengumpul dan mengatur butiran pesakit',
	Frankfurt: 'Frankfurt',
	Free: 'Percuma',
	FreePlanInclusionFive: 'Pengebilan automatik ',
	FreePlanInclusionFour: 'Portal pelanggan',
	FreePlanInclusionHeader: 'Mulakan dengan',
	FreePlanInclusionOne: 'Pelanggan tanpa had',
	FreePlanInclusionSix: 'Sokongan langsung',
	FreePlanInclusionThree: '1 GB storan',
	FreePlanInclusionTwo: 'Telekesihatan',
	FreeSubscriptionPlanSubtitle: 'Percuma untuk semua orang',
	FreeSubscriptionPlanTitle: 'Percuma',
	Friday: 'Jumaat',
	From: 'daripada',
	FullName: 'Nama penuh',
	FunctionalMedicineOrNaturopath: 'Perubatan Fungsian atau Naturopath',
	FuturePaymentsAuthoriseProvider: 'Benarkan {provider} untuk menggunakan pembayaran yang disimpan pada masa hadapan',
	FuturePaymentsSavePaymentMethod: 'Simpan {paymentMethod} untuk pembayaran masa hadapan',
	GST: 'GST',
	Gender: 'Jantina',
	GeneralAvailability: 'Ketersediaan umum',
	GeneralAvailabilityDescription:
		'Tetapkan apabila anda sentiasa tersedia. Pelanggan hanya boleh menempah perkhidmatan anda pada waktu yang tersedia.',
	GeneralAvailabilityDescription2:
		'Buat jadual berdasarkan ketersediaan anda dan tawaran perkhidmatan yang diingini pada masa tertentu untuk menentukan ketersediaan tempahan dalam talian anda.',
	GeneralAvailabilityInfo: 'Waktu kerja anda yang tersedia akan menentukan ketersediaan tempahan dalam talian anda',
	GeneralAvailabilityInfo2:
		'Perkhidmatan yang menawarkan acara kumpulan harus menggunakan jadual baharu untuk mengurangkan waktu tersedia yang boleh ditempah oleh pelanggan dalam talian.',
	GeneralHoursPlural: '{count} {count, plural, one {jam} other {jam}}',
	GeneralPractitioner: 'Pengamal Am',
	GeneralPractitioners: 'Pengamal Am',
	GeneralServiceAvailabilityInfo: 'Jadual ini akan mengatasi tingkah laku untuk ahli pasukan yang ditugaskan',
	Generate: 'Menjana',
	GenerateBillingItemsBannerContent: 'Item pengebilan tidak dibuat secara automatik untuk janji temu berulang.',
	GenerateItems: 'Hasilkan item',
	GenerateNote: 'Hasilkan nota',
	GenerateNoteConfirmationModalDescription:
		'Apa yang anda ingin lakukan? Buat nota baru yang dijana, tambahkan pada yang sedia ada atau gantikan kandungannya?',
	GenerateNoteFor: 'Hasilkan nota untuk',
	GeneratingContent: 'Menjana kandungan...',
	GeneratingNote: 'Menghasilkan nota anda...',
	GeneratingTranscript: 'Menjana transkrip',
	GeneratingTranscriptDescription: 'Ini mungkin mengambil masa beberapa minit untuk diproses',
	GeneratingYourTranscript: 'Menjana transkrip anda',
	GenericErrorDescription: '{module} tidak dapat dimuat. Sila cuba lagi kemudian.',
	GenericErrorTitle: 'Ralat yang tidak dijangka berlaku',
	GenericFailureSnackbar: 'Maaf, sesuatu yang tidak dijangka berlaku. Sila muat semula halaman dan cuba lagi.',
	GenericSavedSuccessSnackbar: 'Berjaya! Perubahan disimpan',
	GeneticCounselor: 'Kaunselor Genetik',
	Gerontologist: 'Pakar Gerontologi',
	Get50PercentOff: 'Dapatkan 50% diskaun!',
	GetHelp: 'Dapatkan bantuan',
	GetStarted: 'Mulakan',
	GettingStartedAppointmentTypes: 'Buat jenis janji temu',
	GettingStartedAppointmentTypesDescription:
		'Perkemas penjadualan dan pengebilan anda dengan menyesuaikan perkhidmatan, harga dan kod pengebilan anda',
	GettingStartedAppointmentTypesTitle: 'Jadual ',
	GettingStartedClients: 'Tambah pelanggan anda',
	GettingStartedClientsDescription:
		'Dapatkan diri anda dan berjalan dengan pelanggan untuk janji temu, nota dan pembayaran akan datang',
	GettingStartedClientsTitle: 'Semuanya bermula dengan pelanggan',
	GettingStartedCreateClient: 'Buat klien',
	GettingStartedImportClients: 'Import pelanggan',
	GettingStartedInvoices: 'Invois seperti profesional',
	GettingStartedInvoicesDescription: `Mudah untuk membuat invois profesional.
 Tambahkan logo, lokasi dan syarat pembayaran anda`,
	GettingStartedInvoicesTitle: 'Letakkan kaki terbaik anda ke hadapan',
	GettingStartedMobileApp: 'Dapatkan apl mudah alih',
	GettingStartedMobileAppDescription:
		'Anda boleh memuat turun Carepatron pada peranti iOS, Android atau desktop anda untuk akses mudah semasa dalam perjalanan',
	GettingStartedMobileAppTitle: 'Bekerja dari mana-mana sahaja',
	GettingStartedNavItem: 'Bermula',
	GettingStartedPageTitle: 'Bermula dengan Carepatron',
	GettingStartedPayments: 'Terima pembayaran dalam talian',
	GettingStartedPaymentsDescription: `Dapatkan bayaran lebih cepat dengan membolehkan pelanggan anda membayar dalam talian.
 Lihat semua invois dan pembayaran anda di satu tempat`,
	GettingStartedPaymentsTitle: 'Buat pembayaran dengan mudah',
	GettingStartedSaveBranding: 'Simpan penjenamaan',
	GettingStartedSyncCalendars: 'Segerakkan kalendar lain',
	GettingStartedSyncCalendarsDescription:
		'Carepatron menyemak kalendar anda untuk sebarang konflik, jadi janji temu hanya dijadualkan apabila anda tersedia',
	GettingStartedSyncCalendarsTitle: 'Sentiasa mengikuti perkembangan terkini',
	GettingStartedVideo: 'Tonton video pengenalan',
	GettingStartedVideoDescription:
		'Ruang kerja penjagaan kesihatan semua-dalam-satu pertama untuk pasukan kecil dan pelanggan mereka',
	GettingStartedVideoTitle: 'Selamat datang ke Carepatron',
	GetttingStartedGetMobileDownload: 'Muat turun aplikasi',
	GetttingStartedGetMobileNoDownload:
		'Tidak serasi dengan penyemak imbas ini. Jika anda menggunakan iPhone atau iPad, sila buka halaman ini dalam Safari. Jika tidak, cuba buka dalam Chrome.',
	Glossary: '<p>Glosari</p>',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail hanya membenarkan 500 mesej dihantar daripada akaun anda dalam sehari. Sesetengah mesej mungkin gagal. Adakah anda mahu meneruskan?',
	GoToAppointment: 'Pergi ke temu janji',
	GoToApps: 'Pergi ke apl',
	GoToAvailability: 'Pergi ke ketersediaan',
	GoToClientList: 'Pergi ke senarai pelanggan',
	GoToClientRecord: 'Pergi ke rekod pelanggan',
	GoToClientSettings: 'Pergi ke tetapan pelanggan sekarang',
	GoToInvoiceTemplates: 'Pergi ke templat invois',
	GoToNotificationSettings: 'Pergi ke tetapan pemberitahuan',
	GoToPaymentSettings: 'Pergi ke tetapan pembayaran',
	Google: 'Google',
	GoogleCalendar: 'Kalendar Google',
	GoogleColor: 'Warna kalendar Google',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'ID Bekas Pengurus Google Tag',
	GotIt: 'faham!',
	Goto: 'Pergi ke',
	Granddaughter: 'Cucu perempuan',
	Grandfather: 'datuk',
	Grandmother: 'nenek',
	Grandparent: 'datuk nenek',
	Grandson: 'Cucu',
	GrantPortalAccess: 'Berikan akses portal',
	GraphicDesigner: 'Pereka Grafik',
	Grid: 'Grid',
	GridView: 'Pandangan grid',
	Group: 'Kumpulan',
	GroupBy: 'Kumpulan mengikut',
	GroupEvent: 'Acara berkumpulan',
	GroupEventHelper: 'Tetapkan had peserta untuk perkhidmatan',
	GroupFilterLabel: 'Semua {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'ID Kumpulan',
	GroupInputFieldsFormPrimaryText: 'Medan input kumpulan',
	GroupInputFieldsFormSecondaryText: 'Pilih atau tambah medan tersuai',
	GuideTo: 'Panduan kepada {value}',
	GuideToImproveVideoQuality: 'Panduan untuk meningkatkan kualiti video',
	GuideToManagingPayers: 'Menguruskan pembayar',
	GuideToSubscriptionsBilling: 'Panduan untuk pengebilan langganan',
	GuideToTroubleshooting: 'Panduan untuk menyelesaikan masalah',
	Guidelines: 'Garis Panduan',
	GuidelinesCategoryDescription: 'Untuk membimbing pembuatan keputusan klinikal',
	HST: 'HST',
	HairStylist: 'Penggaya Rambut',
	HaveBeenWaiting: 'Dah lama awak tunggu',
	HeHim: 'Dia/Dia',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Kalendar',
	HeaderCalls: 'Panggilan',
	HeaderClientAppAccountSettings: 'Tetapan Akaun',
	HeaderClientAppCalls: 'Panggilan',
	HeaderClientAppMyDocumentation: 'Dokumentasi',
	HeaderClientAppMyRelationships: 'hubungan saya',
	HeaderClients: 'Pelanggan',
	HeaderHelp: 'Tolong',
	HeaderMoreOptions: 'Lebih banyak pilihan',
	HeaderStaff: 'Kakitangan',
	HealthCoach: 'Jurulatih Kesihatan',
	HealthCoaches: 'Jurulatih Kesihatan',
	HealthEducator: 'Pendidik Kesihatan',
	HealthInformationTechnician: 'Juruteknik Maklumat Kesihatan',
	HealthPolicyExpert: 'Pakar Dasar Kesihatan',
	HealthServicesAdministrator: 'Pentadbir Perkhidmatan Kesihatan',
	HelpArticles: 'Artikel Bantuan',
	HiddenColumns: 'Lajur tersembunyi',
	HiddenFields: 'Medan Tersembunyi',
	HiddenSections: 'Bahagian tersembunyi',
	HiddenSectionsAndFields: 'Bahagian/medan tersembunyi',
	HideColumn: 'Sembunyikan lajur',
	HideColumnButton: 'Sembunyikan lajur {value} butang',
	HideDetails: 'Sembunyikan butiran',
	HideField: 'Sembunyikan medan',
	HideFullAddress: 'Sembunyi',
	HideMenu: 'Sembunyikan menu',
	HideMergeSummarySidebar: 'Sembunyikan ringkasan gabungan',
	HideSection: 'Sembunyikan bahagian',
	HideYourView: 'Sembunyikan pandangan anda',
	Highlight: 'Serlahkan warna',
	Highlighter: 'Penyerlah',
	History: 'Sejarah',
	HistoryItemFooter: '{actors, select, undefined {{date} pada {time}} other {Oleh {actors} • {date} pada {time}}}',
	HistorySidePanelEmptyState: 'Tiada rekod sejarah dijumpai',
	HistoryTitle: 'Log Aktiviti',
	HolisticHealthPractitioner: 'Pengamal Kesihatan Holistik',
	HomeCaregiver: 'Penjaga Rumah',
	HomeHealthAide: 'Pembantu Kesihatan Rumah',
	HomelessShelter: 'Tempat perlindungan gelandangan',
	HourAbbreviation: '{count} {count, plural, one {jam} other {jam}}',
	Hourly: 'Setiap Jam',
	HoursPlural: '{age, plural, one {# jam} other {# jam}}',
	HowCanWeImprove: 'Bagaimana kita boleh meningkatkan ini?',
	HowCanWeImproveResponse: 'Bagaimana kami boleh perbaiki respon ini?',
	HowDidWeDo: 'Bagaimana kami?',
	HowDoesReferralWork: 'Panduan kepada program rujukan',
	HowToUseAiSummarise: 'Cara menggunakan AI Summarize',
	HumanResourcesManager: 'Pengurus Sumber Manusia',
	Husband: 'Suami',
	Hypnotherapist: 'Pakar hipnotis',
	IVA: 'IVA',
	IgnoreNotification: 'Abaikan pemberitahuan',
	IgnoreOnce: 'Abaikan sekali',
	IgnoreSender: 'Abaikan pengirim',
	IgnoreSenderDescription: `Perbualan masa depan daripada pengirim ini akan dialihkan secara automatik ke 'Lain-lain'. Adakah anda pasti mahu mengabaikan pengirim ini?`,
	IgnoreSenders: 'Abaikan penghantar',
	IgnoreSendersSuccess: 'Alamat e-mel yang diabaikan <mark>{addresses}</mark>',
	Ignored: 'diabaikan',
	Image: 'Imej',
	Import: 'Import',
	ImportActivity: 'Import aktiviti',
	ImportClientSuccessSnackbarDescription: 'Fail anda telah berjaya diimport',
	ImportClientSuccessSnackbarTitle: 'Import berjaya!',
	ImportClients: 'Import pelanggan',
	ImportClientsFailureSnackbarDescription: 'Fail anda tidak berjaya diimport kerana ralat.',
	ImportClientsFailureSnackbarTitle: 'Import tidak berjaya!',
	ImportClientsGuide: 'Panduan untuk mengimport pelanggan',
	ImportClientsInProgressSnackbarDescription: 'Ini hanya perlu mengambil masa sehingga seminit untuk diselesaikan.',
	ImportClientsInProgressSnackbarTitle: 'Memuat {fileName}',
	ImportClientsModalDescription:
		'Pilih dari mana data anda datang – sama ada fail pada peranti anda, perkhidmatan pihak ketiga atau platform perisian lain.',
	ImportClientsModalFileUploadHelperText: 'Sokongan {fileTypes}. Had lapan {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Panduan untuk mengimport data pelanggan',
	ImportClientsModalStep1Label: 'Pilih sumber data',
	ImportClientsModalStep2Label: 'Muat naik fail',
	ImportClientsModalStep3Label: 'Semak semula medan',
	ImportClientsModalTitle: 'Mengimport data pelanggan anda',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {pelanggan} other {pelanggan}} sedia untuk diimport',
	ImportContactFailedNotificationSubject: 'Import data anda telah gagal',
	ImportDataSourceSelectorLabel: 'Import sumber data daripada',
	ImportDataSourceSelectorPlaceholder: 'Cari atau pilih sumber data import',
	ImportExportButton: 'Import/Eksport',
	ImportFailed: 'Import gagal',
	ImportFromAnotherPlatformTileDescription: 'Muat turun eksport fail pelanggan anda dan muat naikkannya di sini.',
	ImportFromAnotherPlatformTileLabel: 'Import dari platform lain',
	ImportGuide: 'Panduan import',
	ImportInProgress: 'Import sedang dijalankan',
	ImportProcessing: 'Import sedang diproses...',
	ImportSpreadsheetDescription:
		'Anda boleh mengimport senarai pelanggan sedia ada anda ke dalam Carepatron dengan memuat naik fail hamparan dengan data jadual, seperti .CSV, .XLS atau .XLSX',
	ImportSpreadsheetTitle: 'Import fail hamparan anda',
	ImportTemplates: 'Import templat',
	Importing: 'Mengimport',
	ImportingCalendarProductEvents: 'Mengimport acara {product}',
	ImportingData: 'Mengimport data',
	ImportingSpreadsheetDescription: 'Ini hanya perlu mengambil masa sehingga seminit untuk diselesaikan',
	ImportingSpreadsheetTitle: 'Mengimport hamparan anda',
	ImportsInProgress: 'Import sedang dijalankan',
	InPersonMeeting: 'Pertemuan secara peribadi',
	InProgress: 'Sedang berlangsung',
	InTransit: 'Dalam Transit',
	InTransitTooltip:
		'Baki dalam Transit termasuk semua pembayaran invois berbayar daripada Stripe ke akaun bank anda. Dana ini biasanya mengambil masa 3-5 hari untuk diselesaikan.',
	Inactive: 'Tidak aktif',
	InboundOrOutboundCalls: 'Panggilan masuk atau keluar',
	Inbox: 'Peti masuk',
	InboxAccessRestricted: 'Akses terhad. Sila hubungi pemilik peti masuk untuk mendapatkan kebenaran.',
	InboxAccountAlreadyConnected: 'Saluran yang anda cuba sambungkan sudah bersambung ke Carepatron',
	InboxAddAttachments: 'Tambah lampiran',
	InboxAreYouSureDeleteMessage: 'Adakah anda pasti mahu memadamkan mesej ini?',
	InboxBulkCloseSuccess: '{count, plural, one {Berjaya tutup # perbualan} other {Berjaya tutup # perbualan}}',
	InboxBulkComposeModalTitle: 'Karang mesej pukal',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Berjaya memadamkan # perbualan} other {Berjaya memadamkan # perbualan}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Berjaya menandakan # perbualan sebagai dibaca} other {Berjaya menandakan # perbualan sebagai dibaca}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Berjaya membuka semula # perbualan} other {Berjaya membuka semula # perbualan}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Berjaya menandakan # perbualan sebagai belum dibaca} other {Berjaya menandakan # perbualan sebagai belum dibaca}}',
	InboxChatCreateGroup: 'Cipta Kumpulan',
	InboxChatDeleteGroupModalDescription:
		'Anda pasti mahu memadamkan kumpulan ini? Semua mesej dan lampiran akan dipadamkan.',
	InboxChatDeleteGroupModalTitle: 'Padam kumpulan',
	InboxChatDiscardDraft: 'Buang Draf',
	InboxChatDragDropText: 'Seret fail di sini untuk memuat naik',
	InboxChatGroupConversation: 'Perbualan kumpulan',
	InboxChatGroupCreateModalDescription:
		'Mulakan kumpulan baharu untuk Mesej dan bekerjasama dengan pasukan, pelanggan, atau komuniti anda.',
	InboxChatGroupCreateModalTitle: 'Cipta kumpulan',
	InboxChatGroupMembers: 'Ahli kumpulan',
	InboxChatGroupModalGroupNameFieldLabel: 'Nama Kumpulan',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Cth. sokongan pelanggan, pentadbir',
	InboxChatGroupModalGroupNameFieldRequired: '<p>Ruangan ini diperlukan</p>',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimum satu ahli diperlukan',
	InboxChatGroupModalMembersFieldLabel: 'Pilih ahli kumpulan',
	InboxChatGroupModalMembersFieldPlaceholder: 'Pilih ahli',
	InboxChatGroupUpdateModalTitle: 'Urus kumpulan',
	InboxChatLeaveGroup: 'Keluar kumpulan',
	InboxChatLeaveGroupModalDescription:
		'Adakah anda pasti mahu meninggalkan kumpulan ini? Anda tidak akan lagi menerima mesej atau kemas kini.',
	InboxChatLeaveGroupModalTitle: 'Tinggalkan kumpulan',
	InboxChatLeftGroupMessage: 'Kumpulan mesej kiri',
	InboxChatManageGroup: 'Urus kumpulan',
	InboxChatSearchParticipants: 'Pilih penerima',
	InboxCloseConversationSuccess: 'Berjaya menutup perbualan',
	InboxCompose: 'Karang',
	InboxComposeBulk: 'Mesej pukal',
	InboxComposeCarepatronChat: 'Utusan',
	InboxComposeChat: 'Buat Sembang',
	InboxComposeDisabledNoConnection: 'Sambungkan akaun e-mel untuk menghantar mesej',
	InboxComposeDisabledNoPermissionTooltip:
		'Anda tidak mempunyai kebenaran untuk menghantar mesej daripada peti masuk ini',
	InboxComposeEmail: 'Karang e-mel',
	InboxComposeMessageFrom: 'daripada',
	InboxComposeMessageRecipientBcc: 'Skt',
	InboxComposeMessageRecipientCc: 'Sk',
	InboxComposeMessageRecipientTo: 'Kepada',
	InboxComposeMessageSubject: 'Subjek:',
	InboxConnectAccountButton: 'Sambungkan e-mel anda',
	InboxConnectedDescription: 'Peti masuk anda tidak mempunyai komunikasi',
	InboxConnectedHeading: 'Perbualan anda akan muncul di sini sebaik sahaja anda mula bertukar komunikasi',
	InboxConnectedHeadingClientView: 'Perkemas komunikasi pelanggan anda',
	InboxCreateFirstInboxButton: 'Buat peti masuk pertama anda',
	InboxCreationSuccess: 'Berjaya membuat peti masuk',
	InboxDeleteAttachment: 'Padamkan lampiran',
	InboxDeleteConversationSuccess: 'Berjaya memadamkan perbualan',
	InboxDeleteMessage: 'Padamkan mesej?',
	InboxDirectMessage: 'Mesej Langsung',
	InboxEditDraft: 'Edit draf',
	InboxEmailComposeReplyEmail: 'Karang jawapan',
	InboxEmailDraft: 'Draf',
	InboxEmailNotFound: 'E-mel tidak ditemui',
	InboxEmailSubjectFieldInformation: 'Menukar baris subjek akan mencipta e-mel berulir baharu.',
	InboxEmptyArchiveDescription: 'Tiada perbualan yang diarkibkan ditemui',
	InboxEmptyBinDescription: 'Tiada perbualan yang dipadam ditemui',
	InboxEmptyBinHeading: 'Semua jelas, tiada apa yang boleh dilihat di sini',
	InboxEmptyBinSuccess: 'Berjaya memadamkan perbualan',
	InboxEmptyCongratsHeading: 'kerja bagus! Duduk dan berehat sehingga perbualan seterusnya',
	InboxEmptyDraftDescription: 'Tiada draf perbualan ditemui',
	InboxEmptyDraftHeading: 'Semua jelas, tiada apa yang dapat dilihat di sini',
	InboxEmptyOtherDescription: 'Tiada perbualan lain ditemui',
	InboxEmptyScheduledHeading: 'Semua jelas, tiada perbualan dijadualkan untuk dihantar',
	InboxEmptySentDescription: 'Tiada perbualan yang dihantar telah ditemui',
	InboxForward: 'ke hadapan',
	InboxGroupClientsLabel: 'Semua pelanggan',
	InboxGroupClientsOverviewLabel: 'Pelanggan',
	InboxGroupClientsSelectedItemPrefix: 'Pelanggan',
	InboxGroupStaffsLabel: 'Semua pasukan',
	InboxGroupStaffsOverviewLabel: 'Pasukan',
	InboxGroupStaffsSelectedItemPrefix: 'Pasukan',
	InboxGroupStatusLabel: 'Semua Status',
	InboxGroupStatusOverviewLabel: 'Hantar ke status',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Semua tag',
	InboxGroupTagsOverviewLabel: 'Hantar ke tag',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Sembunyikan teks petikan',
	InboxIgnoreConversationSuccess: 'Berjaya mengabaikan perbualan',
	InboxMessageAllLabelRecipientsCount: 'Semua Penerima {label} ({count})',
	InboxMessageBodyPlaceholder: 'Tambah mesej anda',
	InboxMessageDeleted: 'Mesej dipadamkan',
	InboxMessageMarkedAsRead: 'Mesej ditanda sebagai dibaca',
	InboxMessageMarkedAsUnread: 'Mesej ditandai sebagai belum dibaca',
	InboxMessageSentViaChat: '<strong>Dihantar melalui sembang</strong>  • {time} oleh {name}',
	InboxMessageShowMoreRecipients: '+{count} lagi',
	InboxMessageWasDeleted: 'Mesej ini telah dipadamkan',
	InboxNoConnectionDescription: 'Sambungkan akaun e-mel anda atau buat peti masuk dengan berbilang e-mel',
	InboxNoConnectionHeading: 'Sepadukan komunikasi pelanggan anda',
	InboxNoDirectMessage: 'Tiada mesej baru',
	InboxRecentConversations: 'Terbaru',
	InboxReopenConversationSuccess: 'Berjaya membuka semula perbualan',
	InboxReply: 'Balas',
	InboxReplyAll: 'Balas semua',
	InboxRestoreConversationSuccess: 'Berjaya memulihkan perbualan',
	InboxScheduleSendCancelSendSuccess: 'Penghantaran berjadual dibatalkan dan mesej dikembalikan kepada draf',
	InboxScheduleSendMessageSuccessDescription: 'Hantar dijadualkan untuk {date}',
	InboxScheduleSendMessageSuccessTitle: 'Menjadualkan penghantaran',
	InboxSearchForConversations: 'Carian untuk "{query}"',
	InboxSendMessageSuccess: 'Berjaya menghantar perbualan',
	InboxSettings: 'Tetapan peti masuk',
	InboxSettingsAppsDesc:
		'Urus apl yang disambungkan untuk peti masuk kongsi ini: tambah atau alih keluar sambungan mengikut keperluan.',
	InboxSettingsAppsNewConnectedApp: 'Apl bersambung baharu',
	InboxSettingsAppsTitle: 'Apl bersambung',
	InboxSettingsDeleteAccountFailed: 'Gagal memadamkan akaun peti masuk',
	InboxSettingsDeleteAccountSuccess: 'Berjaya memadamkan akaun peti masuk',
	InboxSettingsDeleteAccountWarning:
		'Mengelak {email} akan memutuskan hubungannya dengan peti masuk {inboxName} dan akan menghentikan mesej daripada disegerakkan.',
	InboxSettingsDeleteInboxFailed: 'Gagal memadamkan peti masuk',
	InboxSettingsDeleteInboxSuccess: 'Berjaya memadamkan peti masuk',
	InboxSettingsDeleteInboxWarning:
		'Menghapus {inboxName} akan memutuskan semua saluran yang disambungkan dan menghapus semua mesej yang dikaitkan dengan peti masuk ini. 		Tindakan ini kekal dan tidak boleh dibatalkan.',
	InboxSettingsDetailsDesc: 'Peti masuk komunikasi untuk pasukan anda mengurus mesej pelanggan dengan cekap.',
	InboxSettingsDetailsTitle: 'Butiran peti masuk',
	InboxSettingsEmailSignatureLabel: 'lalai tandatangan e-mel',
	InboxSettingsReplyFormatDesc:
		'Sediakan alamat balasan lalai dan tandatangan e-mel anda untuk dipaparkan secara konsisten, tanpa mengira siapa yang menghantar e-mel.',
	InboxSettingsReplyFormatTitle: 'Format balasan',
	InboxSettingsSendFromLabel: 'Tetapkan balasan lalai daripada ',
	InboxSettingsStaffDesc: 'Urus akses ahli pasukan ke peti masuk kongsi ini untuk kerjasama yang lancar.',
	InboxSettingsStaffTitle: 'Tugaskan ahli pasukan',
	InboxSettingsUpdateInboxDetailsFailed: 'Gagal mengemas kini butiran peti masuk',
	InboxSettingsUpdateInboxDetailsSuccess: 'Berjaya mengemas kini butiran peti masuk',
	InboxSettingsUpdateInboxStaffsFailed: 'Gagal mengemas kini ahli pasukan peti masuk',
	InboxSettingsUpdateInboxStaffsSuccess: 'Berjaya mengemas kini ahli pasukan peti masuk',
	InboxSettingsUpdateReplyFormatFailed: 'Gagal mengemas kini format balasan',
	InboxSettingsUpdateReplyFormatSuccess: 'Berjaya mengemas kini format balasan',
	InboxShowQuotedText: 'Tunjukkan teks yang dipetik',
	InboxStaffRoleAdminDescription: 'Lihat, balas dan urus peti masuk',
	InboxStaffRoleResponderDescription: 'Lihat dan balas',
	InboxStaffRoleViewerDescription: 'Lihat sahaja',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Teruskan mengedit',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Ya, tukar kepada penghantaran pukal',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Anda telah memilih lebih daripada {count} penerima. Adakah anda mahu menghantarnya sebagai e-mel pukal?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Amaran',
	InboxSwitchToOtherInbox: 'Tukar ke peti masuk lain',
	InboxUndoSendMessageSuccess: 'Penghantaran dibuat asal',
	IncludeLineItems: 'Sertakan item baris',
	IncludeSalesTax: 'Bercukai',
	IncludesAiSmartPrompt: 'Termasuk gesaan pintar AI',
	Incomplete: 'tak lengkap',
	IncreaseIndent: 'Tingkatkan inden',
	IndianHealthServiceFreeStandingFacility: 'Kemudahan berdiri bebas Perkhidmatan Kesihatan India',
	IndianHealthServiceProviderFacility: 'Kemudahan berasaskan penyedia Perkhidmatan Kesihatan India',
	Information: 'Maklumat',
	InitialAssessment: 'Penilaian Awal',
	InitialSignupPageClientFamilyTitle: 'Pelanggan atau Ahli Keluarga',
	InitialSignupPageProviderTitle: 'Kesihatan ',
	InitialTreatment: 'Rawatan awal',
	Initials: 'Inisial',
	InlineEmbed: 'Sisipan sebaris',
	InputPhraseToConfirm: 'Untuk mengesahkan, taip {confirmationPhrase}.',
	Insert: 'Sisipkan',
	InsertTable: 'Sisipkan jadual',
	InstallCarepatronOnYourIphone1: 'Pasang Carepatron pada iOS anda: ketik',
	InstallCarepatronOnYourIphone2: 'dan kemudian Tambahkan pada Skrin Utama',
	InsufficientCalendarScopesSnackbar: 'Penyegerakan gagal - sila benarkan kebenaran kalendar kepada Carepatron',
	InsufficientInboxScopesSnackbar: 'Penyegerakan gagal - sila benarkan kebenaran e-mel kepada Carepatron',
	InsufficientScopeErrorCodeSnackbar: 'Penyegerakan gagal - sila benarkan semua kebenaran kepada Carepatron',
	Insurance: 'Insurans',
	InsuranceAmount: 'Jumlah Insurans',
	InsuranceClaim: 'Tuntutan insurans',
	InsuranceClaimAiChatPlaceholder: 'Tanya mengenai tuntutan insurans...',
	InsuranceClaimAiClaimNumber: 'Tuntutan {number}',
	InsuranceClaimAiSubtitle: 'Bilangan Insurans • Pengesahan Tuntutan',
	InsuranceClaimDeniedSubject: 'Klaim {claimNumber} yang diserahkan kepada {payerNumber} {payerName} telah ditolak',
	InsuranceClaimErrorDescription:
		'Tuntutan ini mengandungi ralat yang dilaporkan daripada pembayar atau pusat penjelasan. Sila semak mesej ralat berikut dan hantar semula tuntutan.',
	InsuranceClaimErrorGuideLink: 'Panduan tuntutan insurans',
	InsuranceClaimErrorTitle: 'Ralat penghantaran tuntutan',
	InsuranceClaimNotFound: 'Tuntutan insurans tidak ditemui',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Pembayaran separa sebanyak {paymentAmount}} other {Pembayaran {paymentAmount}}} untuk tuntutan {claimNumber} oleh {payerNumber} {payerName} telah direkodkan',
	InsuranceClaimRejectedSubject:
		'Tuntutan {claimNumber} yang dihantar kepada {payerNumber} {payerName} telah ditolak',
	InsuranceClaims: 'Tuntutan Insurans',
	InsuranceInformation: 'Maklumat insurans',
	InsurancePaid: 'Insurans Dibayar',
	InsurancePayer: 'Pembayar insurans',
	InsurancePayers: 'Pembayar insurans',
	InsurancePayersDescription: 'Lihat pembayar yang telah ditambah ke akaun anda dan uruskan pendaftaran.',
	InsurancePayment: 'Bayaran insurans',
	InsurancePoliciesDetailsSubtitle: 'Tambahkan maklumat insurans pelanggan untuk menyokong tuntutan.',
	InsurancePoliciesDetailsTitle: 'Butiran dasar',
	InsurancePoliciesListSubtitle: 'Tambahkan maklumat insurans pelanggan untuk menyokong tuntutan.',
	InsurancePoliciesListTitle: 'Polisi insurans',
	InsuranceSelfPay: 'Bayaran Sendiri',
	InsuranceType: 'Jenis insurans',
	InsuranceUnpaid: 'Insurans tidak dibayar',
	Intake: 'Pengambilan',
	IntakeExpiredErrorCodeSnackbar:
		'Pengambilan ini telah tamat tempoh. Sila hubungi pembekal anda untuk menghantar semula pengambilan lain.',
	IntakeNotFoundErrorSnackbar:
		'Pengambilan ini tidak dapat ditemui. Sila hubungi pembekal anda untuk menghantar semula pengambilan lain.',
	IntakeProcessLearnMoreInstructions: 'Panduan untuk menyediakan borang pengambilan anda',
	IntakeTemplateSelectorPlaceholder:
		'Pilih borang dan perjanjian untuk dihantar kepada pelanggan anda untuk dilengkapkan',
	Integration: 'Integrasi',
	IntenseBlur: 'Kaburkan latar belakang anda secara intensif',
	InteriorDesigner: 'Pereka Dalaman',
	InternetBanking: 'Pindahan bank',
	Interval: 'Selang waktu',
	IntervalDays: 'Selang (Hari)',
	IntervalHours: 'Selang (Jam)',
	Invalid: 'tidak sah',
	InvalidDate: 'Tarikh tidak sah',
	InvalidDateFormat: 'Tarikh mestilah dalam format {format}',
	InvalidDisplayName: 'Nama paparan tidak boleh mengandungi {value}',
	InvalidEmailFormat: 'Format e-mel tidak sah',
	InvalidFileType: 'Jenis fail tidak sah',
	InvalidGTMContainerId: 'Format ID bekas GTM tidak sah',
	InvalidPaymentMethodCode: 'Kaedah pembayaran yang dipilih tidak sah. Sila pilih yang lain.',
	InvalidPromotionCode: 'Kod promosi tidak sah',
	InvalidReferralDescription: 'Sudah menggunakan Carepatron',
	InvalidStatementDescriptor: `Deskriptor penyata mestilah antara 5 dan 22 aksara panjang dan mengandungi hanya huruf, nombor, ruang dan tidak boleh mengandungi <, >, \\, ', ", *`,
	InvalidToken: 'Token tidak sah',
	InvalidTotpSetupVerificationCode: 'Kod pengesahan tidak sah.',
	InvalidURLErrorText: 'Ini mestilah URL yang sah',
	InvalidZoomTokenErrorCodeSnackbar:
		'Token Zoom telah tamat tempoh. Sila sambungkan semula apl Zoom anda dan cuba lagi.',
	Invite: 'Jemput',
	InviteRelationships: 'Menjemput perhubungan',
	InviteToPortal: 'Jemput ke portal',
	InviteToPortalModalDescription: 'E-mel jemputan akan dihantar kepada pelanggan anda untuk mendaftar ke Carepatron.',
	InviteToPortalModalTitle: 'Jemput {name} ke Portal Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Jemput pengguna baharu',
	Invited: 'dijemput',
	Invoice: 'Invois',
	InvoiceColorPickerDescription: 'Tema warna yang akan digunakan dalam invois',
	InvoiceColorTheme: 'Tema warna invois',
	InvoiceContactDeleted: 'Hubungan invois telah dipadamkan dan invois ini tidak boleh dikemas kini.',
	InvoiceDate: 'Tarikh dikeluarkan',
	InvoiceDetails: 'Butiran invois',
	InvoiceFieldsPlaceholder: 'Cari medan...',
	InvoiceFrom: 'Invois {number} dari {fromProvider}',
	InvoiceInvalidCredit: 'Jumlah kredit tidak sah, jumlah kredit tidak boleh melebihi jumlah invois',
	InvoiceNotFoundDescription:
		'Sila hubungi pembekal anda dan minta mereka untuk mendapatkan maklumat lanjut atau menghantar semula invois.',
	InvoiceNotFoundTitle: 'Invois tidak ditemui',
	InvoiceNumber: 'Invois #',
	InvoiceNumberFormat: 'Invois #{number}',
	InvoiceNumberMustEndWithDigit: 'Nombor invois mesti berakhir dengan angka (0-9)',
	InvoicePageHeader: 'Invois',
	InvoicePaidNotificationSubject: 'Invois {invoiceNumber} dibayar',
	InvoiceReminder: 'Peringatan invois',
	InvoiceReminderSentence:
		'Hantar peringatan {deliveryType} {interval} {unit} {beforeAfter} tarikh jatuh tempo invois',
	InvoiceReminderSettings: 'Tetapan peringatan invois',
	InvoiceReminderSettingsInfo: 'Peringatan hanya digunakan pada invois yang dihantar pada Carepatron',
	InvoiceReminders: 'Peringatan invois',
	InvoiceRemindersInfo:
		'Tetapkan peringatan automatik untuk tarikh akhir invois. Peringatan hanya digunakan pada invois yang dihantar melalui Carepatron',
	InvoiceSettings: 'Tetapan invois',
	InvoiceStatus: 'Status invois',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, Amerika Syarikat',
	InvoiceTemplateDescriptionPlaceholder:
		'Tambahkan nota, butiran pindahan bank atau terma dan syarat untuk pembayaran alternatif',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Bekerja Sendiri',
	InvoiceTemplateEthnicityPlaceholder: 'Kaukasia',
	InvoiceTemplateNotFoundDescription:
		'Sila hubungi pembekal anda dan tanya mereka untuk mendapatkan maklumat lanjut.',
	InvoiceTemplateNotFoundTitle: 'Templat invois tidak ditemui',
	InvoiceTemplates: 'Templat invois',
	InvoiceTemplatesDescription:
		'Sesuaikan templat invois anda untuk mencerminkan jenama anda, memenuhi keperluan kawal selia dan memenuhi pilihan pelanggan dengan templat mesra pengguna kami.',
	InvoiceTheme: 'Tema invois',
	InvoiceTotal: 'Jumlah Bil',
	InvoiceUninvoicedAmounts: 'Invois jumlah yang tidak diinvois',
	InvoiceUpdateVersionMessage:
		'Mengedit invois ini memerlukan versi terkini. Sila muat semula Carepatron dan cuba lagi.',
	Invoices: '{count, plural, one {Invois} other {Invois}}',
	InvoicesEmptyStateDescription: 'Tiada invois ditemui',
	InvoicingAndPayment: 'Invois ',
	Ireland: 'Ireland',
	IsA: 'ialah a',
	IsBetween: 'adalah antara',
	IsEqualTo: 'adalah sama dengan',
	IsGreaterThan: 'adalah lebih besar daripada',
	IsGreaterThanOrEqualTo: 'adalah lebih besar daripada atau sama dengan',
	IsLessThan: 'adalah kurang daripada',
	IsLessThanOrEqualTo: 'adalah kurang daripada atau sama dengan',
	IssueCredit: 'Keluarkan kredit',
	IssueCreditAdjustment: 'Mengeluarkan pelarasan kredit',
	IssueDate: 'Tarikh keluaran',
	Italic: 'Italic',
	Items: 'barang',
	ItemsAndAdjustments: 'Item dan pelarasan',
	ItemsRemaining: '+{count} item yang tinggal',
	JobTitle: 'Tajuk kerja',
	Join: 'Sertai',
	JoinCall: 'Sertai panggilan',
	JoinNow: 'Sertai sekarang',
	JoinProduct: 'Sertai {product}',
	JoinVideoCall: 'Sertai panggilan video',
	JoinWebinar: 'Sertai webinar',
	JoinWithVideoCall: 'Sertai bersama {product}',
	Journalist: 'Wartawan',
	JustMe: 'Hanya saya',
	JustYou: 'Hanya awak',
	Justify: 'Mewajarkan',
	KeepSeparate: 'Simpan berasingan',
	KeepSeparateSuccessMessage: 'Anda telah berjaya menyimpan rekod berasingan untuk {clientNames}',
	KeepWaiting: 'Teruskan menunggu',
	Label: 'Label',
	LabelOptional: 'Label (Pilihan)',
	LactationConsulting: 'Perundingan laktasi',
	Language: 'Bahasa',
	Large: 'besar',
	LastDxCode: 'Kod DX terakhir',
	LastLoggedIn: 'Log masuk terakhir pada {date} pada {time}',
	LastMenstrualPeriod: 'Haid terakhir',
	LastMonth: 'bulan lepas',
	LastNDays: 'Terakhir {number} hari',
	LastName: 'nama keluarga',
	LastNameFirstInitial: 'Nama keluarga, inisial pertama',
	LastWeek: 'Minggu lepas',
	LastXRay: 'X-ray terakhir',
	LatestVisitOrConsultation: 'Lawatan atau konsultasi terkini',
	Lawyer: 'Peguam',
	LearnMore: 'Ketahui lebih lanjut',
	LearnMoreTipsToGettingStarted: 'Ketahui lebih lanjut tentang tip untuk memulakan<',
	LearnToSetupInbox: 'Panduan untuk menyediakan akaun peti masuk',
	Leave: 'cuti',
	LeaveCall: 'Tinggalkan panggilan',
	LeftAlign: 'Jajar ke kiri',
	LegacyBillingItemsNotAvailable:
		'Item bil individu belum tersedia untuk temu janji ini. Anda masih boleh mencantumkannya secara normal.',
	LegacyBillingItemsNotAvailableTitle: 'Bilangan warisan',
	LegalAndConsent: 'Undang-undang dan persetujuan',
	LegalConsentFormPrimaryText: 'Persetujuan undang-undang',
	LegalConsentFormSecondaryText: 'Terima atau tolak pilihan',
	LegalGuardian: 'Penjaga yang sah',
	Letter: 'Surat',
	LettersCategoryDescription: 'Untuk mencipta surat-menyurat klinikal dan pentadbiran',
	Librarian: 'Pustakawan',
	LicenseNumber: 'Nombor lesen',
	LifeCoach: 'Jurulatih Kehidupan',
	LifeCoaches: 'Jurulatih Kehidupan',
	Limited: 'Terhad',
	LineSpacing: 'Jarak baris dan perenggan',
	LinearScaleFormPrimaryText: 'Skala linear',
	LinearScaleFormSecondaryText: 'Pilihan skala 1-10',
	Lineitems: 'Item baris',
	Link: 'Pautan',
	LinkClientFormSearchClientLabel: 'Cari pelanggan',
	LinkClientModalTitle: 'Pautan kepada pelanggan sedia ada',
	LinkClientSuccessDescription: '**{newName}** maklumat hubungan telah ditambah kepada rekod **{existingName}**.',
	LinkClientSuccessTitle: 'Berjaya dipautkan kepada kenalan sedia ada',
	LinkForCallCopied: 'Pautan disalin!',
	LinkToAnExistingClient: 'Pautan kepada pelanggan sedia ada',
	LinkToClient: 'Pautan kepada pelanggan',
	ListAndTracker: 'Senarai/Penjejak',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} berada dalam panggilan ini}
			other {{attendees} berada dalam panggilan ini}
		}`,
	ListStyles: 'Senaraikan gaya',
	ListsAndTrackersCategoryDescription: 'Untuk mengatur dan menjejaki kerja',
	LivingArrangements: 'Susunan Hidup',
	LoadMore: 'Muatkan Lagi',
	Loading: 'Memuatkan...',
	LocalizationPanelDescription: 'Urus tetapan untuk bahasa dan zon waktu anda',
	LocalizationPanelTitle: 'Bahasa dan zon waktu',
	Location: 'Lokasi',
	LocationDescription:
		'Sediakan lokasi fizikal dan maya dengan alamat tertentu, nama bilik dan jenis ruang maya untuk memudahkan penjadualan janji temu dan panggilan video.',
	LocationNumber: 'Nombor lokasi',
	LocationOfService: 'Lokasi perkhidmatan',
	LocationOfServiceRecommendedActionInfo:
		'Menambahkan lokasi tertentu kepada perkhidmatan ini mungkin menjejaskan ketersediaan anda.',
	LocationRemote: 'Jarak Jauh',
	LocationType: 'Jenis lokasi',
	Locations: 'Lokasi',
	Lock: 'Kunci',
	Locked: 'Terkunci',
	LockedNote: 'Nota berkunci',
	LogInToSaveOrAuthoriseCard: 'Log masuk untuk menyimpan atau membenarkan kad',
	LogInToSaveOrAuthorisePayment: 'Log masuk untuk menyimpan atau membenarkan pembayaran',
	Login: 'Log masuk',
	LoginButton: 'Log masuk',
	LoginEmail: 'E-mel',
	LoginForgotPasswordLink: 'Terlupa kata laluan',
	LoginPassword: 'Kata laluan',
	Logo: 'Logo',
	LogoutAreYouSure: 'Log keluar daripada peranti ini.',
	LogoutButton: 'Log keluar',
	London: 'London',
	LongTextAnswer: 'Jawapan teks panjang',
	LongTextFormPrimaryText: 'Teks panjang',
	LongTextFormSecondaryText: 'Pilihan gaya perenggan',
	Male: 'jantan',
	Manage: 'Urus',
	ManageAllClientTags: 'Urus Semua Teg Pelanggan',
	ManageAllNoteTags: 'Uruskan Semua Teg Nota',
	ManageAllTemplateTags: 'Urus Semua Teg Templat',
	ManageConnections: 'Urus sambungan',
	ManageConnectionsGmailDescription: 'Ahli pasukan lain tidak akan dapat melihat Gmail anda yang disegerakkan.',
	ManageConnectionsGoogleCalendarDescription:
		'Ahli pasukan lain tidak akan dapat melihat kalendar anda yang disegerakkan. Janji temu pelanggan hanya boleh dikemas kini atau dipadamkan dari dalam Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Sila pergi ke halaman Peti Masuk untuk mengurus tetapan Peti Masuk Penyegerakan.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Ahli pasukan lain tidak akan dapat melihat kalendar anda yang disegerakkan. Janji temu pelanggan hanya boleh dikemas kini atau dipadamkan dari dalam Carepatron.',
	ManageConnectionsOutlookDescription:
		'Ahli pasukan lain tidak akan dapat melihat Microsoft Outlook anda yang disegerakkan.',
	ManageInboxAccountButton: 'Peti masuk baharu',
	ManageInboxAccountEdit: 'Urus Peti Masuk',
	ManageInboxAccountPanelTitle: 'Peti masuk',
	ManageInboxAssignTeamPlaceholder: 'Pilih ahli pasukan untuk akses peti masuk',
	ManageInboxBasicInfoColor: 'warna',
	ManageInboxBasicInfoDescription: 'Penerangan',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Untuk apa anda atau pasukan anda akan menggunakan peti masuk ini?',
	ManageInboxBasicInfoName: 'Nama peti masuk',
	ManageInboxBasicInfoNamePlaceholder: 'Cth sokongan pelanggan, pentadbir',
	ManageInboxConnectAppAlreadyConnectedError: 'Saluran yang anda cuba sambungkan sudah bersambung ke Carepatron',
	ManageInboxConnectAppConnect: 'Sambung',
	ManageInboxConnectAppConnectedInfo: 'Disambungkan ke akaun',
	ManageInboxConnectAppContinue: 'teruskan',
	ManageInboxConnectAppEmail: 'E-mel',
	ManageInboxConnectAppSignInWith: 'Log masuk dengan',
	ManageInboxConnectAppSubtitle:
		'Sambungkan apl anda untuk menghantar, menerima dan menjejaki semua komunikasi anda dengan lancar di satu tempat terpusat.',
	ManageInboxNewInboxTitle: 'Peti masuk baharu',
	ManagePlan: 'Urus rancangan',
	ManageProfile: 'Urus profil',
	ManageReferralsModalDescription:
		'Bantu kami menyebarkan berita tentang platform penjagaan kesihatan kami dan dapatkan ganjaran.',
	ManageReferralsModalTitle: 'Rujuk rakan, dapatkan ganjaran!',
	ManageStaffRelationshipsAddButton: 'Uruskan perhubungan',
	ManageStaffRelationshipsEmptyStateText: 'Tiada perhubungan ditambah',
	ManageStaffRelationshipsModalDescription:
		'Memilih pelanggan akan menambah perhubungan baharu, manakala menyahpilih mereka akan mengalih keluar perhubungan sedia ada.',
	ManageStaffRelationshipsModalTitle: 'Uruskan perhubungan',
	ManageStatuses: 'Urus status',
	ManageStatusesActiveStatusHelperText: 'Sekurang-kurangnya satu status aktif diperlukan',
	ManageStatusesDescription:
		'Sesuaikan label status anda dan pilih warna untuk diselaraskan dengan aliran kerja anda.',
	ManageStatusesSuccessSnackbar: 'Berjaya mengemas kini status',
	ManageTags: 'Urus tag',
	ManageTaskAttendeeStatus: 'Urus status temu janji',
	ManageTaskAttendeeStatusDescription: 'Sesuaikan status temujanji anda agar selaras dengan aliran kerja anda.',
	ManageTaskAttendeeStatusHelperText: 'Sekurang-kurangnya satu status diperlukan',
	ManageTaskAttendeeStatusSubtitle: 'Status Tersuai',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manual',
	ManualAppointment: 'Temujanji Manual',
	ManualPayment: 'Pembayaran manual',
	ManuallyTypeLocation: 'Taip lokasi secara manual',
	MapColumns: 'Lajur Peta',
	MappingRequired: 'Pemetaan diperlukan',
	MarkAllAsRead: 'Tandai semua sebagai dibaca',
	MarkAsCompleted: 'Tandai sebagai selesai',
	MarkAsManualSubmission: 'Tandakan sebagai dihantar',
	MarkAsPaid: 'Tandakan sebagai berbayar',
	MarkAsRead: 'Tandakan sebagai dibaca',
	MarkAsUnpaid: 'Tandakan sebagai belum dibayar',
	MarkAsUnread: 'Tandai sebagai belum dibaca',
	MarkAsVoid: 'Tandakan sebagai tidak sah',
	Marker: 'Penanda',
	MarketingManager: 'Pengurus Pemasaran',
	MassageTherapist: 'Tukang Urut',
	MassageTherapists: 'Tukang Urut',
	MassageTherapy: 'Terapi urut',
	MaxBookingTimeDescription1: 'Pelanggan boleh menjadualkan sehingga',
	MaxBookingTimeDescription2: 'ke masa hadapan',
	MaxBookingTimeLabel: '{timePeriod} terlebih dahulu',
	MaxCapacity: 'Kapasiti maksimum',
	Maximize: 'Maksimumkan',
	MaximumAttendeeLimit: 'Had maksimum',
	MaximumBookingTime: 'Masa tempahan maksimum',
	MaximumBookingTimeError: 'Masa tempahan maksimum tidak boleh melebihi {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Anda boleh meminimumkan sehingga {count} panel sisi pada satu masa. Teruskan akan menutup panel yang diminimumkan paling awal. Adakah anda ingin meneruskan?',
	MaximumMinimizedPanelsReachedTitle: 'Anda mempunyai terlalu banyak panel yang dibuka.',
	MechanicalEngineer: 'Jurutera Mekanikal',
	MediaGallery: 'Galeri media',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Nombor pembekal Medicaid',
	MedicalAssistant: 'Pembantu Perubatan',
	MedicalCoder: 'Pengekod Perubatan',
	MedicalDoctor: 'Doktor Perubatan',
	MedicalIllustrator: 'Ilustrator Perubatan',
	MedicalInterpreter: 'Jurubahasa Perubatan',
	MedicalTechnologist: 'Pakar Teknologi Perubatan',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Nombor pembekal Medicare',
	Medicine: 'Ubat',
	Medium: 'Sederhana',
	Meeting: 'Mesyuarat',
	MeetingEnd: 'Tamat Mesyuarat',
	MeetingEnded: 'Mesyuarat tamat',
	MeetingHost: 'Pengacara mesyuarat',
	MeetingLowerHand: 'Tangan bawah',
	MeetingOpenChat: 'Buka Sembang',
	MeetingPersonRaisedHand: '{name} mengangkat tangannya',
	MeetingRaiseHand: 'Angkat tangan',
	MeetingReady: 'Mesyuarat bersedia',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Mesyuarat',
	MemberId: 'ID ahli',
	MentalHealth: 'Kesihatan Mental',
	MentalHealthPractitioners: 'Pengamal Kesihatan Mental',
	MentalHealthProfessional: 'Profesional Kesihatan Mental',
	Merge: 'Bercantum',
	MergeClientRecords: 'Gabungkan rekod pelanggan',
	MergeClientRecordsDescription: 'Menggabungkan rekod pelanggan akan menggabungkan semua data mereka, termasuk:',
	MergeClientRecordsDescription2:
		'Adakah anda mahu meneruskan penggabungan ini? Tindakan ini tidak boleh dibatalkan.',
	MergeClientRecordsItem1: 'Nota dan dokumen',
	MergeClientRecordsItem2: 'Temujanji',
	MergeClientRecordsItem3: 'Invois',
	MergeClientRecordsItem4: 'Perbualan',
	MergeClientsSuccess: 'Berjaya menggabungkan rekod pelanggan',
	MergeLimitExceeded: 'Anda hanya boleh menggabungkan sehingga 4 pelanggan pada satu masa.',
	Message: 'Mesej',
	MessageAttachments: '{total} lampiran',
	Method: 'Kaedah',
	MfaAvailabilityDisclaimer:
		'MFA hanya tersedia untuk log masuk e-mel dan kata laluan. Untuk membuat perubahan pada tetapan MFA anda, log masuk menggunakan e-mel dan kata laluan anda.',
	MfaDeviceLostPanelDescription:
		'Sebagai alternatif, anda boleh mengesahkan identiti anda dengan menerima kod melalui e-mel.',
	MfaDeviceLostPanelTitle: 'Peranti MFA anda hilang?',
	MfaDidntReceiveEmailCode: 'Tidak menerima kod? Hubungi sokongan',
	MfaEmailOtpSendFailureSnackbar: 'Gagal menghantar e-mel OTP.',
	MfaEmailOtpSentSnackbar: 'Satu kod telah dihantar ke {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Gagal mengesahkan OTP e-mel.',
	MfaHasBeenSetUpText: 'Anda telah menyediakan MFA',
	MfaPanelDescription:
		'Lindungi akaun anda dengan mendayakan Multi-Factor Authentication (MFA) untuk lapisan perlindungan tambahan. Sahkan identiti anda melalui kaedah kedua untuk menghalang akses tanpa kebenaran.',
	MfaPanelNotAuthorizedError: 'Anda mesti log masuk dengan nama pengguna ',
	MfaPanelRecommendationDescription:
		'Baru-baru ini anda telah log masuk menggunakan kaedah alternatif untuk mengesahkan identiti anda. Untuk memastikan akaun anda selamat, pertimbangkan untuk menyediakan peranti MFA baharu.',
	MfaPanelRecommendationTitle: '**Disyorkan:** Kemas kini peranti MFA anda',
	MfaPanelTitle: 'Pengesahan Berbilang Faktor (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'Anda perlu mengesahkan e-mel anda sebelum anda boleh mengemas kini tetapan MFA anda.',
	MfaRecommendationBannerDescription:
		'Baru-baru ini anda telah log masuk menggunakan kaedah alternatif untuk mengesahkan identiti anda. Untuk memastikan akaun anda selamat, pertimbangkan untuk menyediakan peranti MFA baharu.',
	MfaRecommendationBannerPrimaryAction: 'Sediakan MFA',
	MfaRecommendationBannerTitle: 'Disyorkan',
	MfaRemovedSnackbarTitle: 'MFA telah dialih keluar.',
	MfaSendEmailCode: 'Hantar kod',
	MfaVerifyIdentityLostDeviceButton: 'Saya kehilangan akses kepada peranti MFA saya',
	MfaVerifyYourIdentityPanelDescription: 'Semak apl pengesah anda untuk kod dan masukkannya di bawah.',
	MfaVerifyYourIdentityPanelTitle: 'Sahkan identiti anda',
	MicCamWarningMessage:
		'Nyahsekat kamera dan mikrofon dengan mengklik ikon yang disekat dalam bar alamat penyemak imbas.',
	MicCamWarningTitle: 'Kamera dan mikrofon disekat',
	MicOff: 'Mikrofon dimatikan',
	MicOn: 'Mikrofon dihidupkan',
	MicSource: 'Sumber mikrofon',
	MicWarningMessage: 'Isu telah dikesan dengan mikrofon anda',
	Microphone: 'Mikrofon',
	MicrophonePermissionBlocked: 'Akses mikrofon disekat',
	MicrophonePermissionBlockedDescription: 'Kemaskini kebenaran mikrofon anda untuk mula merakam.',
	MicrophonePermissionError: 'Sila berikan kebenaran mikrofon dalam penyemak imbas tetapan anda untuk meneruskan',
	MicrophonePermissionPrompt: 'Sila benarkan akses mikrofon untuk diteruskan',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Warna kalendar Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Pasukan Microsoft',
	MiddleEast: 'Timur Tengah',
	MiddleName: 'nama tengah',
	MiddleNames: 'nama tengah',
	Midwife: 'bidan',
	Midwives: 'Bidan',
	Milan: 'Milan',
	MinBookingTimeDescription1: 'Pelanggan tidak boleh menjadualkan dalam masa',
	MinBookingTimeDescription2: 'masa mula janji temu',
	MinBookingTimeLabel: '{timePeriod} sebelum temu janji',
	MinCancellationTimeEditModeDescription: 'Tetapkan berapa jam pelanggan boleh membatalkan tanpa penalti',
	MinCancellationTimeUnset: 'Tiada masa pembatalan minimum ditetapkan',
	MinCancellationTimeViewModeDescription: 'Tempoh pembatalan tanpa penalti',
	MinMaxBookingTimeUnset: 'Tiada masa ditetapkan',
	Minimize: 'Minimumkan',
	MinimizeConfirmationDescription:
		'Anda mempunyai panel minima yang aktif. Jika anda teruskan, ia akan ditutup, dan anda mungkin kehilangan data yang tidak disimpan.',
	MinimizeConfirmationTitle: 'Tutup panel yang diminimumkan?',
	MinimumBookingTime: 'Masa tempahan minimum',
	MinimumCancellationTime: 'Masa pembatalan minimum',
	MinimumPaymentError: 'Bayaran minimum {minimumAmount} diperlukan untuk pembayaran dalam talian.',
	MinuteAbbreviated: 'min',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Setiap Minit',
	MinutesPlural: '{age, plural, one {# minit} other {# minit}}',
	MiscellaneousInformation: 'Pelbagai maklumat',
	MissingFeatures: 'Tiada ciri',
	MissingPaymentMethod: 'Sila tambahkan kaedah pembayaran pada langganan anda untuk menambah lebih ramai kakitangan.',
	MobileNumber: 'Nombor mudah alih',
	MobileNumberOptional: 'Nombor Mudah Alih (pilihan)',
	Modern: 'moden',
	Modifiers: 'Pengubah suai',
	ModifiersPlaceholder: 'Pengubah suai',
	Monday: 'Isnin',
	Month: 'bulan',
	Monthly: 'Bulanan',
	MonthlyCost: 'Kos Bulanan',
	MonthlyOn: 'Bulanan pada {date}',
	MonthsPlural: '{age, plural, one {# bulan} other {# bulan}}',
	More: 'Lagi',
	MoreActions: 'Lebih banyak tindakan',
	MoreSettings: 'Lagi tetapan',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Paling biasa digunakan',
	MostDownloaded: 'Paling banyak dimuat turun',
	MostPopular: 'Paling popular',
	Mother: 'ibu',
	MotherInLaw: 'mak mertua',
	MoveDown: 'Bergerak ke bawah',
	MoveInboxConfirmationDescription:
		'Menugaskan semula sambungan aplikasi ini akan mengeluarkannya daripada peti masuk <strong>{currentInboxName}</strong>.',
	MoveTemplateToFolder: 'Pindahkan `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} dipindahkan ke {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Berjaya dipindahkan ke folder penerimaan lalai',
	MoveTemplateToNewFolder: 'Cipta folder baru untuk memindahkan item ini.',
	MoveToChosenFolder: 'Pilih folder untuk memindahkan item ini. Anda boleh membuat folder baharu jika perlu.',
	MoveToFolder: 'Pindah ke folder',
	MoveToInbox: 'Beralih ke Peti Masuk',
	MoveToNewFolder: 'Pindah ke folder baharu',
	MoveToSelectedFolder:
		'Setelah dipindahkan, item tersebut akan disusun di bawah folder yang dipilih dan tidak akan lagi dipaparkan di lokasi semasa.',
	MoveUp: 'Bergerak ke atas',
	MultiSpeciality: 'Pelbagai kepakaran',
	MultipleChoiceFormPrimaryText: 'Pelbagai pilihan',
	MultipleChoiceFormSecondaryText: 'Pilih berbilang pilihan',
	MultipleChoiceGridFormPrimaryText: 'Grid berbilang pilihan',
	MultipleChoiceGridFormSecondaryText: 'Pilih pilihan daripada matriks',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Jurupulih Muzik',
	MustContainOneLetterError: 'Mesti mengandungi sekurang-kurangnya satu huruf',
	MustEndWithANumber: 'Mesti diakhiri dengan nombor',
	MustHaveAtLeastXItems: 'Mestilah ada sekurang-kurangnya {count, plural, one {# item} other {# items}}',
	MuteAudio: 'Redamkan audio',
	MuteEveryone: 'Redam semua orang',
	MyAvailability: 'Ketersediaan Saya',
	MyGallery: 'Galeri saya',
	MyPortal: 'Portal Saya',
	MyRelationships: 'hubungan saya',
	MyTemplates: 'Templat Pasukan',
	MyofunctionalTherapist: 'Ahli Terapi Miofungsi',
	NCalifornia: 'California Utara',
	NPI: 'NPI',
	NVirginia: 'Virginia Utara',
	Name: 'Nama',
	NameIsRequired: 'Nama diperlukan',
	NameMustNotBeAWebsite: 'Nama mestilah bukan tapak web',
	NameMustNotBeAnEmail: 'Nama mestilah bukan e-mel',
	NameMustNotContainAtSign: 'Nama tidak boleh mengandungi tanda @',
	NameMustNotContainHTMLTags: 'Nama tidak boleh mengandungi tag HTML',
	NameMustNotContainSpecialCharacters: 'Nama tidak boleh mengandungi aksara khas',
	NameOnCard: 'Nama pada kad',
	NationalProviderId: 'Pengecam penyedia nasional (NPI)',
	NaturopathicDoctor: 'Doktor Naturopatik',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Tetapan Langganan',
	NavigateToWorkspaceSettings: 'Tetapan Ruang Kerja',
	NavigateToYourTeam: 'Urus pasukan',
	NavigationDrawerBilling: 'Bil',
	NavigationDrawerBillingInfo: 'Maklumat pengebilan, invois dan Stripe',
	NavigationDrawerCommunication: 'Komunikasi',
	NavigationDrawerCommunicationInfo: 'Pemberitahuan dan templat',
	NavigationDrawerInsurance: 'Insurans',
	NavigationDrawerInsuranceInfo: 'Pembayar insurans dan tuntutan',
	NavigationDrawerInvoices: 'Pengebilan',
	NavigationDrawerPersonal: 'Profil Saya',
	NavigationDrawerPersonalInfo: 'Butiran peribadi anda',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'tetapan',
	NavigationDrawerScheduling: 'Penjadualan',
	NavigationDrawerSchedulingInfo: 'Butiran perkhidmatan dan tempahan',
	NavigationDrawerSettings: 'tetapan',
	NavigationDrawerTemplates: 'templat',
	NavigationDrawerTemplatesV2: 'Templat V2',
	NavigationDrawerTrash: 'Sampah',
	NavigationDrawerTrashInfo: 'Pulihkan item yang dipadam',
	NavigationDrawerWorkspace: 'Tetapan Ruang Kerja',
	NavigationDrawerWorkspaceInfo: 'Langganan dan maklumat ruang kerja',
	NegativeBalanceNotSupported: 'Baki akaun negatif tidak disokong',
	Nephew: 'anak saudara',
	NetworkQualityFair: 'Sambungan yang adil',
	NetworkQualityGood: 'Sambungan yang baik',
	NetworkQualityPoor: 'Sambungan yang lemah',
	Neurologist: 'Pakar neurologi',
	Never: 'Tidak',
	New: 'baru',
	NewAppointment: 'Pelantikan baru',
	NewClaim: 'Tuntutan baharu',
	NewClient: 'Pelanggan baru',
	NewClientNextStepsModalAddAnotherClient: 'Tambah pelanggan lain',
	NewClientNextStepsModalBookAppointment: 'Tempah janji temu',
	NewClientNextStepsModalBookAppointmentDescription: 'Tempah janji temu akan datang atau buat tugasan.',
	NewClientNextStepsModalCompleteBasicInformation: 'Lengkapkan rekod pelanggan',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Tambahkan maklumat pelanggan dan tangkap langkah seterusnya.',
	NewClientNextStepsModalCreateInvoice: 'Buat invois',
	NewClientNextStepsModalCreateInvoiceDescription: 'Tambahkan maklumat pembayaran pelanggan atau buat invois.',
	NewClientNextStepsModalCreateNote: 'Buat nota atau muat naik dokumen',
	NewClientNextStepsModalCreateNoteDescription: 'Tangkap nota dan dokumentasi pelanggan.',
	NewClientNextStepsModalDescription:
		'Berikut ialah beberapa tindakan yang perlu diambil sekarang anda telah mencipta rekod pelanggan.',
	NewClientNextStepsModalSendIntake: 'Hantar pengambilan',
	NewClientNextStepsModalSendIntakeDescription:
		'Kumpul maklumat pelanggan dan hantar borang tambahan untuk dilengkapkan dan ditandatangani.',
	NewClientNextStepsModalSendMessage: 'Hantar mesej',
	NewClientNextStepsModalSendMessageDescription: 'Karang dan hantar mesej kepada pelanggan anda.',
	NewClientNextStepsModalTitle: 'Langkah seterusnya',
	NewClientSuccess: 'Berjaya mencipta pelanggan baharu',
	NewClients: 'Pelanggan baru',
	NewConnectedApp: 'Apl bersambung baharu',
	NewContact: 'Kenalan baharu',
	NewContactNextStepsModalAddRelationship: 'Tambah hubungan',
	NewContactNextStepsModalAddRelationshipDescription: 'Pautan hubungan ini kepada klien atau kumpulan berkaitan.',
	NewContactNextStepsModalBookAppointment: 'Tempah Temujanji',
	NewContactNextStepsModalBookAppointmentDescription: 'Tempah temu janji yang akan datang atau cipta tugasan.',
	NewContactNextStepsModalCompleteProfile: 'Profil Lengkap',
	NewContactNextStepsModalCompleteProfileDescription: 'Tambah maklumat hubungan dan rakam langkah seterusnya.',
	NewContactNextStepsModalCreateNote: 'Cipta nota atau muat naik dokumen',
	NewContactNextStepsModalCreateNoteDescription: 'Capture nota dan dokumentasi pelanggan.',
	NewContactNextStepsModalDescription:
		'Berikut adalah beberapa tindakan yang boleh diambil sekarang anda telah mencipta kenalan.',
	NewContactNextStepsModalInviteToPortal: 'Jemputan ke portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Hantar jemputan untuk mengakses portal.',
	NewContactNextStepsModalTitle: 'Langkah seterusnya',
	NewContactSuccess: 'Berjaya membuat kenalan baharu',
	NewDateOverrideButton: 'Penggantian tarikh baharu',
	NewDiagnosis: 'Tambah diagnosis',
	NewField: 'Medan baharu',
	NewFolder: 'Folder baharu',
	NewInvoice: 'Invois baharu',
	NewLocation: 'Lokasi baru',
	NewLocationFailure: 'Gagal membuat lokasi baharu',
	NewLocationSuccess: 'Berjaya mencipta lokasi baharu',
	NewManualPayer: 'Pembayar manual baru',
	NewNote: 'Nota baru',
	NewNoteCreated: 'Berjaya mencipta nota baharu',
	NewPassword: 'Kata laluan baharu',
	NewPayer: 'Pembayar baru',
	NewPaymentMethod: 'Kaedah pembayaran baharu',
	NewPolicy: 'Dasar baharu',
	NewRelationship: 'Hubungan baru',
	NewReminder: 'Peringatan baharu',
	NewSchedule: 'Jadual baru',
	NewSection: 'Bahagian baharu',
	NewSectionOld: 'Bahagian baharu [LAMA]',
	NewSectionWithGrid: 'Bahagian baharu dengan grid',
	NewService: 'Perkhidmatan baru',
	NewServiceFailure: 'Gagal membuat perkhidmatan baharu',
	NewServiceSuccess: 'Berjaya mencipta perkhidmatan baharu',
	NewStatus: 'Status baharu',
	NewTask: 'tugas baru',
	NewTaxRate: 'Kadar cukai baharu',
	NewTeamMemberNextStepsModalAssignClients: 'Tugaskan pelanggan',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Tentukan klien tertentu untuk ahli pasukan anda.',
	NewTeamMemberNextStepsModalAssignServices: 'Tugaskan perkhidmatan',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Menguruskan perkhidmatan yang ditetapkan dan menyesuaikan harga jika diperlukan.',
	NewTeamMemberNextStepsModalBookAppointment: 'Tempah temu janji',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Tempah temu janji yang akan datang atau buat tugasan.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Profil lengkap',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Tambah butiran tentang ahli pasukan anda untuk melengkapkan profil mereka.',
	NewTeamMemberNextStepsModalDescription:
		'Berikut adalah beberapa tindakan yang perlu diambil sekarang anda telah membuat ahli pasukan.',
	NewTeamMemberNextStepsModalEditPermissions: 'Keizinan Edit',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Laraskan tahap akses mereka untuk memastikan mereka mempunyai kebenaran yang betul.',
	NewTeamMemberNextStepsModalSetAvailability: 'Tetapkan ketersediaan',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Konfigurasikan ketersediaan mereka untuk membuat jadual.',
	NewTeamMemberNextStepsModalTitle: 'Langkah seterusnya',
	NewTemplateFolderDescription: 'Buat folder baharu untuk menyusun dokumentasi anda.',
	NewUIUpdateBannerButton: 'Muat semula apl',
	NewUIUpdateBannerTitle: 'Ada kemas kini baharu sedia!',
	NewZealand: 'New Zealand',
	Newest: 'Terbaru',
	NewestUnreplied: 'Terbaru belum dibalas',
	Next: 'Seterusnya',
	NextInvoiceIssueDate: 'Tarikh keluaran invois seterusnya',
	NextNDays: 'Seterusnya {number} hari',
	Niece: 'anak saudara',
	No: 'Tidak',
	NoAccessGiven: 'Tiada akses diberikan',
	NoActionConfigured: 'Tiada tindakan dikonfigurasikan',
	NoActivePolicies: 'Tiada dasar aktif',
	NoActiveReferrals: 'Anda tidak mempunyai rujukan aktif',
	NoAppointmentsFound: 'Tiada janji temu ditemui',
	NoAppointmentsHeading: 'Urus temu janji dan aktiviti pelanggan',
	NoArchivedPolicies: 'Tiada dasar yang diarkibkan',
	NoAvailableTimes: 'Tiada masa yang tersedia dijumpai.',
	NoBillingItemsFound: 'Tiada item bilangan dijumpai',
	NoCalendarsSynced: 'Tiada kalendar disegerakkan',
	NoClaimsFound: 'Tiada tuntutan dijumpai',
	NoClaimsHeading: 'Permudahkan proses penyerahan tuntutan bagi bayaran balik',
	NoClientsHeading: 'Kumpulkan rekod pelanggan anda',
	NoCompletedReferrals: 'Anda tidak mempunyai rujukan lengkap',
	NoConnectionsHeading: 'Perkemas komunikasi pelanggan anda',
	NoContactsGivenAccess: 'Tiada pelanggan atau kenalan telah diberikan akses kepada nota ini',
	NoContactsHeading: 'Kekal berhubung dengan mereka yang menyokong amalan anda',
	NoCopayOrCoinsurance: 'Tiada bayaran bersama atau insurans bersama',
	NoCustomServiceSchedule: 'Tiada jadual tersuai ditetapkan — ketersediaan bergantung pada ketersediaan ahli pasukan',
	NoDescription: 'Tiada penerangan',
	NoDocumentationHeading: 'Buat dan simpan nota dengan selamat',
	NoDuplicateRecordsHeading: 'Rekod pelanggan anda bebas daripada pendua',
	NoEffect: 'Tiada kesan',
	NoEnrolmentProfilesFound: 'Tiada profil pendaftaran dijumpai',
	NoGlossaryItems: 'Tiada item glosari',
	NoInvitedReferrals: 'Anda tidak mempunyai rujukan yang dijemput',
	NoInvoicesFound: 'Tiada invois dijumpai',
	NoInvoicesHeading: 'Automatikkan pengebilan dan pembayaran anda',
	NoLimit: 'Tiada had',
	NoLocationsFound: 'Tiada lokasi ditemui',
	NoLocationsWillBeAdded: 'Tiada lokasi akan ditambah.',
	NoNoteFound: 'Tiada nota dijumpai',
	NoPaymentMethods:
		'Anda tidak mempunyai kaedah pembayaran yang disimpan, anda boleh menambahnya apabila membuat pembayaran.',
	NoPermissionError: 'Anda tidak mempunyai kebenaran',
	NoPermissions: 'Anda tidak mempunyai kebenaran untuk melihat halaman ini',
	NoPolicy: 'Tiada dasar pembatalan ditambah',
	NoRecordsHeading: 'Peribadikan rekod pelanggan anda',
	NoRecordsToDisplay: 'Tiada {resource} untuk dipaparkan',
	NoRelationshipsHeading: 'Kekal berhubung dengan mereka yang menyokong pelanggan anda',
	NoRemindersFound: 'Tiada peringatan ditemui',
	NoResultsFound: 'Tiada hasil ditemui',
	NoResultsFoundDescription: 'Kami tidak menemui sebarang item yang sepadan dengan carian anda',
	NoServicesAdded: 'Tiada perkhidmatan ditambah',
	NoServicesApplied: 'Tiada perkhidmatan digunakan',
	NoServicesWillBeAdded: 'Tiada perkhidmatan akan ditambah.',
	NoTemplate: 'Anda tidak mempunyai templat amalan yang disimpan',
	NoTemplatesHeading: 'Buat templat anda sendiri',
	NoTemplatesInFolder: 'Tiada templat dalam folder ini',
	NoTitle: 'Tiada tajuk',
	NoTrashItemsHeading: 'Tiada item yang dipadam ditemui',
	NoTriggerConfigured: 'Tiada pencetus dikonfigurasikan',
	NoUnclaimedItemsFound: 'Tiada item yang tidak dituntut ditemui.',
	NonAiTemplates: 'Templat bukan AI',
	None: 'tiada',
	NotAvailable: 'Tidak tersedia',
	NotCovered: 'Tidak bertutup',
	NotFoundSnackbar: 'Sumber tidak ditemui.',
	NotRequiredField: 'Tidak diperlukan',
	Note: 'Nota',
	NoteDuplicateSuccess: 'Berjaya menduplikasi nota',
	NoteEditModeViewSwitcherDescription: 'Buat dan edit nota',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} telah mengemukakan borang {noteTitle}',
	NoteLockSuccess: '{title} telah dikunci',
	NoteModalAttachmentButton: 'Tambah lampiran',
	NoteModalPhotoButton: 'Tambah/Tangkap foto',
	NoteModalTrascribeButton: 'Transkripsikan audio langsung',
	NoteResponderModeViewSwitcherDescription: 'Hantar borang dan semak jawapan',
	NoteResponderModeViewSwitcherTooltipTitle: 'Balas dan serahkan borang bagi pihak pelanggan anda',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Isi dan serahkan borang sebagai pelanggan',
	NoteUnlockSuccess: '{title} telah dibuka kunci',
	NoteViewModeViewSwitcherDescription: 'Akses lihat sahaja',
	Notes: 'Nota',
	NotesAndForms: 'Nota dan Borang',
	NotesCategoryDescription: 'Untuk mendokumenkan interaksi pelanggan',
	NothingToSeeHere: 'Tiada apa yang boleh dilihat di sini',
	Notification: 'Pemberitahuan',
	NotificationIgnoredMessage: 'Semua notifikasi {notificationType} akan diabaikan',
	NotificationRestoredMessage: 'Semua notifikasi {notificationType} telah dipulihkan',
	NotificationSettingBillingDescription: 'Terima pemberitahuan untuk kemas kini pembayaran pelanggan dan peringatan.',
	NotificationSettingBillingTitle: 'Pengebilan dan pembayaran',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} sahaja} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Terima pemberitahuan untuk kemas kini pembayaran pelanggan dan peringatan.',
	NotificationSettingClientDocumentationTitle: 'Dokumentasi dan pelanggan',
	NotificationSettingCommunicationsDescription:
		'Terima pemberitahuan untuk peti masuk dan kemas kini daripada saluran anda yang disambungkan',
	NotificationSettingCommunicationsTitle: 'Komunikasi',
	NotificationSettingEmail: 'Emel',
	NotificationSettingInApp: 'Dalam aplikasi',
	NotificationSettingPanelDescription: 'Pilih pemberitahuan yang anda ingin terima untuk aktiviti dan cadangan.',
	NotificationSettingPanelTitle: 'Keutamaan pemberitahuan',
	NotificationSettingSchedulingDescription:
		'Terima pemberitahuan apabila ahli pasukan atau pelanggan menempah, menjadualkan semula atau membatalkan janji temu mereka.',
	NotificationSettingSchedulingTitle: 'Penjadualan',
	NotificationSettingUpdateSuccess: 'Tetapan pemberitahuan berjaya dikemas kini',
	NotificationSettingWhereYouReceiveNotifications: 'Di mana anda ingin menerima pemberitahuan ini',
	NotificationSettingWorkspaceDescription:
		'Terima pemberitahuan untuk perubahan sistem, isu, pemindahan data dan peringatan langganan.',
	NotificationSettingWorkspaceTitle: 'Ruang kerja',
	NotificationTemplateUpdateFailed: 'Gagal mengemas kini templat pemberitahuan',
	NotificationTemplateUpdateSuccess: 'Templat pemberitahuan berjaya dikemas kini',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Adakah anda ingin menghantar e-mel pemberitahuan pembatalan kepada peserta?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Hantar pembatalan',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Adakah anda ingin menghantar e-mel pemberitahuan pengesahan kepada peserta?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Hantar pengesahan',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Adakah anda ingin menghantar e-mel pembatalan kepada peserta?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {tidak} other {tidak}} mempunyai alamat e-mel, jadi tidak akan menerima pemberitahuan dan peringatan automatik.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {tidak mempunyai} other {tidak mempunyai}} alamat emel jadi tidak akan menerima pemberitahuan dan peringatan automatik.',
	NotifyAttendeesOfTaskModalTitle: 'Adakah anda ingin menghantar e-mel pemberitahuan kepada peserta?',
	NotifyAttendeesOfTaskSnackbar: 'Menghantar pemberitahuan',
	NuclearMedicineTechnologist: 'Ahli Teknologi Perubatan Nuklear',
	NumberOfClaims: '{number, plural, one {# Tuntutan} other {# Tuntutan}}',
	NumberOfClients: '{number, plural, one {# Pelanggan} other {# Pelanggan}}',
	NumberOfContacts: '{number, plural, one {# Hubungi} other {# Hubungi}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entri} other {entri}} dijumpai',
	NumberOfErrors: '{count, plural, one {# ralat} other {# ralat}}',
	NumberOfInvoices: '{number, plural, one {# Bil} other {# Bil}}',
	NumberOfLineitemsToCredit:
		'Anda mempunyai <mark>{count} {count, plural, one {item baris} other {item baris}}</mark> untuk mengeluarkan kredit.',
	NumberOfPayments: '{number, plural, one {# Bayaran} other {# Bayaran}}',
	NumberOfRelationships: '{number, plural, one {# Hubungan} other {# Hubungan}}',
	NumberOfResources: '{number, plural, one {# Sumber} other {# Sumber}}',
	NumberOfTeamMembers: '{number, plural, one {# Ahli pasukan} other {# Ahli-ahli pasukan}}',
	NumberOfTrashItems: '{number, plural, one {# item} other {# item}}',
	NumberOfUninvoicedAmounts:
		'Anda mempunyai <mark>{count} {count, plural, one {jumlah} other {jumlah-jumlah}} yang belum dibil</mark> untuk dibilkan',
	NumberedList: 'Senarai bernombor',
	Nurse: 'Jururawat',
	NurseAnesthetist: 'Jururawat Bius',
	NurseAssistant: 'Pembantu Jururawat',
	NurseEducator: 'Pendidik Jururawat',
	NurseMidwife: 'Jururawat Bidan',
	NursePractitioner: 'Pengamal Jururawat',
	Nurses: 'Jururawat',
	Nursing: 'Kejururawatan',
	Nutritionist: 'Pakar pemakanan',
	Nutritionists: 'Pakar pemakanan',
	ObstetricianOrGynecologist: 'Pakar Obstetrik/Pakar Sakit Puan',
	Occupation: 'pekerjaan',
	OccupationalTherapist: 'Jurupulih Carakerja',
	OccupationalTherapists: 'Jurupulih Carakerja',
	OccupationalTherapy: 'Terapi cara kerja',
	Occurrences: 'Kejadian',
	Of: 'daripada',
	Ohio: 'Ohio',
	OldPassword: 'Kata laluan lama',
	OlderMessages: '{count} mesej lama',
	Oldest: 'Tertua',
	OldestUnreplied: 'Paling lama tidak dibalas',
	On: 'pada',
	OnboardingBusinessAgreement:
		'Atas nama saya dan perniagaan ini, saya bersetuju dengan {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Ahli terapi cara kerja</mark> membentuk satu perempat daripada pelanggan kami di Carepatron',
	OnboardingLoadingProfession:
		'Kami mempunyai banyak <mark>{profession}</mark> yang menggunakan dan berkembang di Carepatron.',
	OnboardingLoadingPsychologist:
		'<mark>ahli psikologi</mark> membentuk lebih separuh daripada pelanggan kami di Carepatron',
	OnboardingLoadingSubtitleFive:
		'Misi kami adalah untuk membuat<mark> perisian penjagaan kesihatan boleh diakses</mark> kepada semua orang.',
	OnboardingLoadingSubtitleFour:
		'<mark>Perisian kesihatan yang dipermudahkan</mark> untuk lebih daripada 10,000 orang di seluruh dunia.',
	OnboardingLoadingSubtitleThree:
		'Jimat<mark> 1 hari seminggu</mark> mengenai tugas pentadbiran dengan bantuan Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Jimat<mark> 2 jam</mark> setiap hari pada tugas pentadbiran dengan bantuan Carepatron.',
	OnboardingReviewLocationOne: 'Pusat Kesihatan Mental Holland Park',
	OnboardingReviewLocationThree: 'Jururawat Amalan, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Klinik Rumah Kehidupan',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron sangat intuitif untuk digunakan. Ia membantu kami menjalankan amalan kami dengan baik sehingga kami tidak memerlukan pasukan pentadbir lagi"',
	OnboardingReviewThree:
		'"Ia adalah penyelesaian amalan terbaik yang saya gunakan dari segi ciri dan kos. Ia mempunyai segala-galanya yang saya perlukan untuk mengembangkan perniagaan saya"',
	OnboardingReviewTwo:
		'"Saya juga menyukai aplikasi carepatron. Membantu saya menjejaki pelanggan saya dan bekerja semasa dalam perjalanan."',
	OnboardingTitle: `Jom ke<mark> tahu
 awak lebih baik</mark>`,
	Oncologist: 'Pakar onkologi',
	Online: 'dalam talian',
	OnlineBookingColorTheme: 'Tema warna tempahan dalam talian',
	OnlineBookings: 'Tempahan dalam talian',
	OnlineBookingsHelper: 'Pilih bila tempahan dalam talian boleh dibuat dan mengikut jenis pelanggan',
	OnlinePayment: 'Pembayaran dalam talian',
	OnlinePaymentSettingCustomInfo:
		'Tetapan pembayaran dalam talian untuk perkhidmatan ini berbeza daripada tetapan tempahan global.',
	OnlinePaymentSettings: 'Tetapan pembayaran dalam talian',
	OnlinePaymentSettingsInfo:
		'Kumpul bayaran untuk perkhidmatan pada masa tempahan dalam talian untuk menjamin dan menyelaraskan pembayaran',
	OnlinePaymentSettingsPaymentsDisabled:
		'Pembayaran dilumpuhkan jadi tidak boleh dikutip semasa tempahan dalam talian. Sila semak tetapan pembayaran anda untuk mendayakan pembayaran.',
	OnlinePaymentSettingsStripeNote:
		'{action} untuk menerima pembayaran tempahan dalam talian dan memperkemas proses pembayaran anda',
	OnlinePaymentsNotSupportedForCurrency: 'Pembayaran dalam talian tidak disokong dalam {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Maaf, pembayaran dalam talian tidak disokong dalam mata wang ini',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Maaf, pembayaran dalam talian belum lagi disokong di negara anda',
	OnlineScheduling: 'Penjadualan Dalam Talian',
	OnlyVisibleToYou: 'Hanya kelihatan kepada Anda',
	OnlyYou: 'hanya awak',
	OnsetDate: 'Tarikh permulaan',
	OnsetOfCurrentSymptomsOrIllness: 'Permulaan gejala atau penyakit semasa',
	Open: 'Buka',
	OpenFile: 'Buka fail',
	OpenSettings: 'Buka tetapan',
	Ophthalmologist: 'Pakar oftalmologi',
	OptimiseTelehealthCalls: 'Optimumkan panggilan Telekesihatan anda',
	OptimizeServiceTimes: 'Optimumkan masa perkhidmatan',
	Options: 'Pilihan',
	Optometrist: 'Optometrist',
	Or: 'atau',
	OrAttachSingleFile: 'lampirkan fail',
	OrDragAndDrop: 'atau seret dan lepas',
	OrderBy: 'Pesanan Oleh',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisasi atau individu',
	OrganizationPlanInclusion1: 'Keizinan lanjutan',
	OrganizationPlanInclusion2: 'Sokongan import data pelanggan percuma',
	OrganizationPlanInclusion3: 'Pengurus kejayaan yang berdedikasi',
	OrganizationPlanInclusionHeader: 'Segala-galanya dalam Profesional, ditambah...',
	Orthodontist: 'Pakar Ortodontik',
	Orthotist: 'Pakar Orthotis',
	Other: 'Lain-lain',
	OtherAdjustments: 'Pelarasan lain',
	OtherAdjustmentsTableEmptyState: 'Tiada pelarasan ditemui',
	OtherEvents: 'Acara lain',
	OtherId: 'ID lain',
	OtherIdQualifier: 'Kelayakan ID lain',
	OtherPaymentMethod: 'Kaedah pembayaran lain',
	OtherPlanMessage:
		'Kawal keperluan amalan anda. Semak pelan semasa anda, pantau penggunaan, dan terokai pilihan naik taraf untuk membuka kunci lebih banyak ciri apabila pasukan anda berkembang.',
	OtherPolicy: 'Insurans lain',
	OtherProducts: 'Apakah produk atau alat lain yang anda gunakan?',
	OtherServices: 'Perkhidmatan lain',
	OtherTemplates: 'Templat lain',
	Others: 'Lain-lain',
	OthersPeople: `{n, plural, 		one {1 orang lain}
		other {# orang lain}
	}`,
	OurResearchTeamReachOut:
		'Bolehkah pasukan penyelidik kami menghubungi untuk mengetahui lebih lanjut tentang cara Carepatron boleh menjadi lebih baik untuk keperluan anda?',
	OutOfOffice: 'Di luar pejabat',
	OutOfOfficeColor: 'Warna luar pejabat',
	OutOfOfficeHelper: 'Beberapa ahli pasukan yang dipilih tiada di pejabat',
	OutsideLabCharges: 'Caj makmal luar',
	OutsideOfWorkingHours: 'Di luar waktu bekerja',
	OutsideWorkingHoursHelper: 'Beberapa ahli pasukan yang dipilih berada di luar waktu bekerja',
	Overallocated: 'Diperuntukkan secara berlebihan',
	OverallocatedPaymentDescription: `Bayaran ini telah terlebih diperuntukkan kepada item yang boleh dibilkan.
 Tambahkan peruntukan pada item yang belum dibayar atau keluarkan kredit atau bayaran balik.`,
	OverallocatedPaymentTitle: 'Lebih bayaran yang diperuntukkan',
	OverdueTerm: 'Tempoh tertunggak (hari)',
	OverinvoicedAmount: 'Amaun terlebih invois',
	Overpaid: 'Terlebih bayar',
	OverpaidAmount: 'Jumlah terlebih bayar',
	Overtime: 'lebih masa',
	Owner: 'Pemilik',
	POS: 'POS',
	POSCode: 'Kod POS',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Perubahan yang tidak disimpan akan hilang. Masih ingin keluar?',
	PageBlockerTitle: 'Buang perubahan?',
	PageFormat: 'Format halaman',
	PageNotFound: 'Halaman tidak ditemui',
	PageNotFoundDescription: 'Anda tidak lagi mempunyai akses ke halaman ini atau halaman ini tidak dapat ditemui',
	PageUnauthorised: 'Akses tanpa kebenaran',
	PageUnauthorisedDescription: 'Anda tidak mempunyai kebenaran untuk mengakses halaman ini',
	Paid: 'Dibayar',
	PaidAmount: 'Jumlah yang Dibayar',
	PaidAmountMinimumValueError: 'Jumlah yang dibayar mestilah lebih daripada 0',
	PaidAmountRequiredError: 'Jumlah yang dibayar diperlukan',
	PaidItems: 'Barang berbayar',
	PaidMultiple: 'Dibayar',
	PaidOut: 'Dibayar',
	ParagraphStyles: 'Gaya perenggan',
	Parent: 'ibu bapa',
	Paris: 'Paris',
	PartialRefundAmount: 'Sebahagiannya dikembalikan ({amount} baki)',
	PartiallyFull: 'Separa penuh',
	PartiallyPaid: 'Separa dibayar',
	PartiallyRefunded: 'Dipulangkan sebahagiannya',
	Partner: 'rakan kongsi',
	Password: 'Kata laluan',
	Past: 'lalu',
	PastDateOverridesEmpty: 'Penggantian tarikh anda akan dipaparkan di sini sebaik sahaja acara itu berlalu',
	Pathologist: 'ahli patologi',
	Patient: 'sabar',
	Pause: 'jeda',
	Paused: 'Dijeda',
	Pay: 'bayar',
	PayMonthly: 'Bayar bulanan',
	PayNow: 'Bayar sekarang',
	PayValue: 'Bayar {showPrice, select, true {{price}} other {sekarang}}',
	PayWithOtherCard: 'Bayar dengan kad lain',
	PayYearly: 'Bayar tahunan',
	PayYearlyPercentOff: 'Bayar setiap tahun <mark>{percent}% diskaun</mark>',
	Payer: 'Pembayar',
	PayerClaimId: 'ID tuntutan pembayar',
	PayerCoverage: 'Liputan',
	PayerDetails: 'Butiran pembayar',
	PayerDetailsDescription: 'Lihat butiran pembayar yang telah ditambahkan pada akaun anda dan uruskan pendaftaran.',
	PayerID: 'ID Pembayar',
	PayerId: 'ID Pembayar',
	PayerName: 'Nama pembayar',
	PayerPhoneNumber: 'Nombor telefon pembayar',
	Payers: 'Pembayar',
	Payment: 'Bayaran',
	PaymentAccountUpdated: 'Akaun anda telah dikemas kini!',
	PaymentAccountUpgraded: 'Akaun anda telah dinaik taraf!',
	PaymentAmount: 'Jumlah pembayaran',
	PaymentDate: 'Tarikh pembayaran',
	PaymentDetails: 'Maklumat pembayaran',
	PaymentForUsersPerMonth: 'Bayaran untuk {billedUsers, plural, one {# pengguna} other {# pengguna}} sebulan',
	PaymentInfoFormPrimaryText: 'Maklumat pembayaran',
	PaymentInfoFormSecondaryText: 'Kumpul butiran pembayaran',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Invois ini telah pun dibayar.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Invois ini sedang diproses.',
	PaymentIntentAmountMismatchSnackbar:
		'Jumlah keseluruhan invois telah diubah suai. Sila semak perubahan sebelum membayar.',
	PaymentIntentSyncTimeoutSnackbar:
		'Pembayaran anda berjaya tetapi tamat masa berlaku. Sila muat semula halaman dan jika pembayaran anda tidak ditunjukkan, sila hubungi sokongan.',
	PaymentMethod: 'Kaedah pembayaran',
	PaymentMethodDescription:
		'Tambah dan urus kaedah pembayaran amalan anda untuk menyelaraskan proses pengebilan langganan anda.',
	PaymentMethodLabelBank: 'akaun bank',
	PaymentMethodLabelCard: 'kad',
	PaymentMethodLabelFallback: 'kaedah pembayaran',
	PaymentMethodRequired: 'Sila tambah kaedah pembayaran sebelum menukar langganan',
	PaymentMethods: 'Kaedah pembayaran',
	PaymentProcessing: 'Pemprosesan pembayaran!',
	PaymentProcessingFee: 'Pembayaran termasuk yuran pemprosesan {amount}',
	PaymentReports: 'Laporan pembayaran (ERA)',
	PaymentSettings: 'Tetapan pembayaran',
	PaymentSuccessful: 'Pembayaran berjaya!',
	PaymentType: 'Jenis pembayaran',
	Payments: 'Pembayaran',
	PaymentsAccountDisabledNotificationSubject: `Pembayaran dalam talian melalui {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} telah dinyahaktifkan.
Sila semak tetapan pembayaran anda untuk mengaktifkan pembayaran.`,
	PaymentsEmptyStateDescription: 'Tiada pembayaran ditemui.',
	PaymentsUnallocated: 'Bayaran tidak diperuntukkan',
	PayoutDate: 'Tarikh pembayaran',
	PayoutsDisabled: 'Bayaran dilumpuhkan',
	PayoutsEnabled: 'Pembayaran didayakan',
	PayoutsStatus: 'Status pembayaran',
	Pediatrician: 'Pakar Pediatrik',
	Pen: 'Pen',
	Pending: 'Belum selesai',
	People: '{rosterSize } orang',
	PeopleCount: 'Orang ({count})',
	PerMonth: '/ Bulan',
	PerUser: 'Per pengguna',
	Permission: 'kebenaran',
	PermissionRequired: 'Kebenaran diperlukan',
	Permissions: 'kebenaran',
	PermissionsClientAndContactDocumentation: 'Pelanggan ',
	PermissionsClientAndContactProfiles: 'Pelanggan ',
	PermissionsEditAccess: 'Edit akses',
	PermissionsInvoicesAndPayments: 'Invois ',
	PermissionsScheduling: 'Penjadualan',
	PermissionsUnassignClients: 'Nyahtugaskan pelanggan',
	PermissionsUnassignClientsConfirmation: 'Adakah anda pasti mahu membatalkan penugasan pelanggan ini?',
	PermissionsValuesAssigned: 'Ditugaskan sahaja',
	PermissionsValuesEverything: 'Semuanya',
	PermissionsValuesNone: 'tiada',
	PermissionsValuesOwnCalendar: 'Kalendar sendiri',
	PermissionsViewAccess: 'Lihat akses',
	PermissionsWorkspaceSettings: 'Tetapan ruang kerja',
	Person: '{rosterSize} orang',
	PersonalDetails: 'Butiran peribadi',
	PersonalHealthcareHistoryStoreDescription:
		'Jawab dan simpan sejarah penjagaan kesihatan peribadi anda dengan selamat di satu tempat',
	PersonalTrainer: 'Jurulatih Peribadi',
	PersonalTraining: 'Latihan Peribadi',
	PersonalizeWorkspace: 'Peribadikan ruang kerja anda',
	PersonalizingYourWorkspace: 'Memperibadikan ruang kerja anda',
	Pharmacist: 'Ahli farmasi',
	Pharmacy: 'farmasi',
	PhoneCall: 'Panggilan telefon',
	PhoneNumber: 'Nombor telefon',
	PhoneNumberOptional: 'Nombor telefon (pilihan)',
	PhotoBy: 'Foto oleh',
	PhysicalAddress: 'Alamat fizikal',
	PhysicalTherapist: 'Jurupulih Fizikal',
	PhysicalTherapists: 'Jurupulih Fizikal',
	PhysicalTherapy: 'Terapi fizikal',
	Physician: 'Doktor',
	PhysicianAssistant: 'Pembantu Pakar Perubatan',
	Physicians: 'Pakar Perubatan',
	Physiotherapist: 'Ahli fisioterapi',
	PlaceOfService: 'Tempat perkhidmatan',
	Plan: 'Rancang',
	PlanAndReport: 'Rancangan/Laporan',
	PlanId: 'ID pelan',
	PlansAndReportsCategoryDescription: 'Untuk perancangan rawatan dan meringkaskan hasil',
	PleaseRefreshThisPageToTryAgain: 'Sila muat semula halaman ini untuk mencuba lagi.',
	PleaseWait: 'Sila tunggu...',
	PleaseWaitForHostToJoin: 'Menunggu Hos untuk menyertai...',
	PleaseWaitForHostToStart: 'Sila tunggu Hos memulakan mesyuarat ini.',
	PlusAdd: '+ Tambah',
	PlusOthers: '+{count} orang lain',
	PlusPlanInclusionFive: 'Peti masuk kongsi',
	PlusPlanInclusionFour: 'Panggilan video berkumpulan',
	PlusPlanInclusionHeader: 'Semuanya dalam Essential  ',
	PlusPlanInclusionOne: 'AI tanpa had',
	PlusPlanInclusionSix: 'Penjenamaan tersuai',
	PlusPlanInclusionThree: 'Penjadualan kumpulan',
	PlusPlanInclusionTwo: 'Storan tanpa had ',
	PlusSubscriptionPlanSubtitle: 'Untuk amalan mengoptimumkan dan berkembang',
	PlusSubscriptionPlanTitle: 'Tambahan pula',
	PoliceOfficer: 'Pegawai Polis',
	PolicyDates: 'Tarikh polisi',
	PolicyHolder: 'Pemegang polisi',
	PolicyHoldersAddress: 'Alamat pemegang polisi',
	PolicyMemberId: 'ID Ahli Polisi',
	PolicyStatus: 'Status dasar',
	Popular: 'Popular',
	PortalAccess: 'Akses portal',
	PortalNoAppointmentsHeading: 'Jejaki semua janji temu yang akan datang dan yang lalu',
	PortalNoDocumentationHeading: 'Buat dan simpan dokumen anda dengan selamat',
	PortalNoRelationshipsHeading: 'Kumpulkan mereka yang menyokong perjalanan anda',
	PosCodeErrorMessage: 'Kod POS diperlukan',
	PosoNumber: 'Nombor PO/SO',
	PossibleClientDuplicate: 'Kemungkinan pendua pelanggan',
	PotentialClientDuplicateTitle: 'Rekod pelanggan pendua yang berpotensi',
	PotentialClientDuplicateWarning:
		'Maklumat pelanggan ini mungkin sudah wujud dalam senarai pelanggan anda. Sila sahkan dan kemas kini rekod sedia ada jika perlu atau teruskan buat klien baharu.',
	PoweredBy: 'Dikuasakan oleh',
	Practice: 'berlatih',
	PracticeDetails: 'Butiran latihan',
	PracticeInfoHeader: 'Maklumat perniagaan',
	PracticeInfoPlaceholder: `Nama latihan,
 Pengecam pembekal nasional,
 Nombor pengenalan majikan`,
	PracticeLocation: 'Nampaknya latihan anda sudah masuk',
	PracticeSettingsAvailabilityTab: 'Ketersediaan',
	PracticeSettingsBillingTab: 'Tetapan pengebilan',
	PracticeSettingsClientSettingsTab: 'Tetapan pelanggan',
	PracticeSettingsGeneralTab: 'Umum',
	PracticeSettingsOnlineBookingTab: 'Tempahan dalam talian',
	PracticeSettingsServicesTab: 'Perkhidmatan',
	PracticeSettingsTaxRatesTab: 'Kadar cukai',
	PracticeTemplate: 'Templat Amalan',
	Practitioner: 'Pengamal',
	PreferredLanguage: 'Bahasa pilihan',
	PreferredName: 'Nama pilihan',
	Prescription: 'preskripsi',
	PreventionSpecialist: 'Pakar Pencegahan',
	Preview: 'Pratonton',
	PreviewAndSend: 'Pratonton dan hantar',
	PreviewUnavailable: 'Pratonton tidak tersedia untuk jenis fail ini',
	PreviousNotes: 'Nota sebelumnya',
	Price: 'harga',
	PriceError: 'Harga mestilah lebih besar daripada 0',
	PricePerClient: 'Harga setiap pelanggan',
	PricePerUser: 'Setiap pengguna',
	PricePerUserBilledAnnually: 'Setiap pengguna dibilkan setiap tahun',
	PricePerUserPerPeriod: '{price} setiap pengguna / {isMonthly, select, true {bulan} other {tahun}}',
	PricingGuide: 'Panduan untuk pelan harga',
	PricingPlanPerMonth: '/ bulan',
	PricingPlanPerYear: '/ tahun',
	Primary: 'utama',
	PrimaryInsurance: 'Insurans utama',
	PrimaryPolicy: 'Insurans utama',
	PrimaryTimezone: 'Zon waktu utama',
	Print: 'Cetak',
	PrintToCms1500: 'Cetak ke CMS1500',
	PrivatePracticeConsultant: 'Perunding Amalan Swasta',
	Proceed: 'Teruskan',
	ProcessAtTimeOfBookingDesc: 'Pelanggan mesti membayar harga perkhidmatan penuh untuk membuat tempahan dalam talian',
	ProcessAtTimeOfBookingLabel: 'Memproses pembayaran semasa membuat tempahan',
	Processing: 'Memproses',
	ProcessingFee: 'Yuran pemprosesan',
	ProcessingFeeToolTip: `Carepatron membenarkan anda mengenakan bayaran pemprosesan kepada pelanggan anda.
 Dalam sesetengah bidang kuasa adalah dilarang untuk mengenakan bayaran pemprosesan kepada pelanggan anda. Adalah menjadi tanggungjawab anda untuk mematuhi undang-undang yang terpakai.`,
	ProcessingRequest: 'Memproses permintaan...',
	Product: 'produk',
	Profession: 'Profesion',
	ProfessionExample: 'Pakar Terapi, Pakar Pemakanan, Doktor Gigi',
	ProfessionPlaceholder: 'Mula menaip profesion anda atau pilih daripada senarai',
	ProfessionalPlanInclusion1: 'Storan tanpa had',
	ProfessionalPlanInclusion2: 'Tugas tanpa had',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Sokongan pelanggan 24/7',
	ProfessionalPlanInclusion5: 'Peringatan SMS',
	ProfessionalPlanInclusionHeader: 'Segala-galanya dalam Starter, ditambah...',
	Professions: 'Profesi',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: 'Had saiz fail 5MB',
	ProfilePopoverSubTitle: 'Anda telah log masuk sebagai <strong>{email}</strong>',
	ProfilePopoverTitle: 'Ruang kerja anda',
	PromoCode: 'Kod promosi',
	PromotionCodeApplied: '{promo} telah diterapkan',
	ProposeNewDateTime: 'Cadangkan tarikh/masa baharu',
	Prosthetist: 'Prostetik',
	Provider: 'Pembekal',
	ProviderBillingPlanExpansionManageButton: 'Urus rancangan',
	ProviderCommercialNumber: 'Nombor komersial pembekal',
	ProviderDetails: 'Butiran pembekal',
	ProviderDetailsAddress: 'Alamat',
	ProviderDetailsName: 'Nama',
	ProviderDetailsPhoneNumber: 'Nombor telefon',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'Maaf, pembekal ini sudah mempunyai akaun pengebilan sedia ada',
	ProviderInfoPlaceholder: `nama kakitangan,
 alamat e-mel,
 nombor telefon,
 Pengecam pembekal nasional,
 Nombor lesen`,
	ProviderIsChargedProcessingFee: 'Anda akan membayar yuran pemprosesan',
	ProviderPaymentFormBackButton: 'belakang',
	ProviderPaymentFormBillingAddressCity: 'Bandar',
	ProviderPaymentFormBillingAddressCountry: 'Negara',
	ProviderPaymentFormBillingAddressLine1: 'Baris1',
	ProviderPaymentFormBillingAddressPostalCode: 'Poskod',
	ProviderPaymentFormBillingEmail: 'E-mel',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Butiran kad kredit',
	ProviderPaymentFormCardExpiry: 'tamat tempoh',
	ProviderPaymentFormCardHolderAddressTitle: 'Alamat',
	ProviderPaymentFormCardHolderName: 'Nama pemegang kad',
	ProviderPaymentFormCardHolderTitle: 'Butiran pemegang kad',
	ProviderPaymentFormCardNumber: 'Nombor kad',
	ProviderPaymentFormPlanTitle: 'Pelan yang dipilih',
	ProviderPaymentFormPlanTotalTitle: 'Jumlah ({currency}):',
	ProviderPaymentFormSaveButton: 'Simpan langganan',
	ProviderPaymentFreePlanDescription:
		'Memilih pelan percuma akan mengalih keluar akses setiap kakitangan kepada pelanggan mereka dalam pembekal anda. Walau bagaimanapun, akses anda akan kekal dan anda masih boleh menggunakan platform tersebut.',
	ProviderPaymentStepName: 'Semakan ',
	ProviderPaymentSuccessSnackbar: 'Hebat! Pelan baharu anda telah berjaya disimpan.',
	ProviderPaymentTitle: 'Semakan ',
	ProviderPlanNetworkIdentificationNumber: 'Nombor pengenalan rangkaian pelan pembekal',
	ProviderRemindersSettingsBannerAction: 'Pergi ke Pengurusan Aliran Kerja',
	ProviderRemindersSettingsBannerDescription:
		'Cari semua peringatan di bawah tab <b>Pengurusan Aliran Kerja</b> yang baru dalam <b>Tetapan</b>. Kemas kini ini membawa ciri baharu yang hebat, templat yang dipertingkatkan, dan alat automasi yang lebih pintar untuk meningkatkan produktiviti anda. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Pengalaman peringatan anda semakin baik',
	ProviderTaxonomy: 'Taksonomi pembekal',
	ProviderUPINNumber: 'Nombor UPIN pembekal',
	ProviderUsedStoragePercentage: '{providerName} storan adalah {usedStoragePercentage}% penuh!',
	PsychiatricNursePractitioner: 'Pengamal Jururawat Psikiatri',
	Psychiatrist: 'Pakar psikiatri',
	Psychiatrists: 'Pakar psikiatri',
	Psychiatry: 'Psikiatri',
	Psychoanalyst: 'Psikoanalisis',
	Psychologist: 'Pakar psikologi',
	Psychologists: 'ahli psikologi',
	Psychology: 'Psikologi',
	Psychometrician: 'Psikometrik',
	PsychosocialRehabilitationSpecialist: 'Pakar Pemulihan Psikososial',
	Psychotheraphy: 'Psikoterapi',
	Psychotherapists: 'Psikoterapi',
	Psychotherapy: 'Psikoterapi',
	PublicCallDialogTitle: 'Panggilan video dengan ',
	PublicCallDialogTitlePlaceholder: 'Panggilan video dikuasakan oleh Carepatron',
	PublicFormBackToForm: 'Serahkan jawapan lain',
	PublicFormConfirmSubmissionHeader: 'Sahkan Penyerahan',
	PublicFormNotFoundDescription:
		'Borang yang anda cari mungkin telah dipadamkan atau pautan tersebut mungkin tidak betul. Sila semak URL dan cuba lagi.',
	PublicFormNotFoundTitle: '<h1>Borang tidak dijumpai</h1>',
	PublicFormSubmissionError: 'Penyerahan gagal. Sila cuba lagi.',
	PublicFormSubmissionSuccess: 'Borang berjaya dihantar',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} telah menghantar borang awam {noteTitle}',
	PublicFormSubmittedSubtitle: 'Penyerahan anda telah diterima.',
	PublicFormSubmittedTitle: 'terima kasih!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Kami telah menghantar kod pengesahan ke e-mel anda',
	PublicFormsInvalidConfirmationCode: 'Kod pengesahan tidak sah',
	PublicHealthInspector: 'Inspektor Kesihatan Awam',
	PublicTemplates: 'Templat awam',
	Publish: 'Terbitkan',
	PublishTemplate: 'Terbitkan templat',
	PublishTemplateFeatureBannerSubheader: 'Templat direka untuk memberi manfaat kepada komuniti',
	PublishTemplateHeader: 'Terbitkan {title}',
	PublishTemplateToCommunity: 'Terbitkan templat kepada komuniti',
	PublishToCommunity: 'Terbitkan kepada komuniti',
	PublishToCommunitySuccessMessage: 'Berjaya diterbitkan kepada masyarakat',
	Published: 'Diterbitkan',
	PublishedBy: 'Diterbitkan oleh {name}',
	PublishedNotesAreNotAutosaved: 'Nota yang diterbitkan tidak akan disimpan secara automatik',
	PublishedOnCarepatronCommunity: 'Diterbitkan pada komuniti Carepatron',
	Purchase: 'Belian',
	PushToCalendar: 'Tolak ke kalendar',
	Question: 'soalan',
	QuestionOrTitle: 'Soalan atau tajuk',
	QuickActions: 'Tindakan pantas',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Blueberry',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Lumut',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Jurupulih Sinaran',
	Radiologist: 'Pakar radiologi',
	Read: 'Baca',
	ReadOnly: 'Baca sahaja',
	ReadOnlyAppointment: 'Temujanji baca sahaja',
	ReadOnlyEventBanner: 'Temujanji ini disegerakkan daripada kalendar baca sahaja dan tidak boleh diedit.',
	ReaderMaxDepthHasBeenExceededCode: 'Nota terlalu bersarang. Cuba tanggalkan beberapa item.',
	ReadyForMapping: 'Sedia untuk pemetaan',
	RealEstateAgent: 'Ejen Hartanah',
	RearrangeClientFields: 'Susun semula medan klien dalam tetapan klien',
	Reason: 'Sebab',
	ReasonForChange: 'Sebab perubahan',
	RecentAppointments: 'Temujanji baru-baru ini',
	RecentServices: 'Perkhidmatan terkini',
	RecentTemplates: 'Templat terkini',
	RecentlyUsed: 'Baru-baru ini digunakan',
	Recommended: 'Disyorkan',
	RecommendedTemplates: 'Templat yang disyorkan',
	Recording: 'Rakaman',
	RecordingEnded: 'Rakaman tamat',
	RecordingInProgress: 'Rakaman sedang dijalankan',
	RecordingMicrophoneAccessErrorMessage:
		'Sila benarkan akses mikrofon dalam penyemak imbas anda dan muat semula untuk mula merakam.',
	RecurrenceCount: ', {count, plural, one {sekali} other {# kali}}',
	RecurrenceDaily: '{count, plural, one {Harian} other {Hari}}',
	RecurrenceEndAfter: 'Selepas',
	RecurrenceEndNever: 'Tidak Pernah',
	RecurrenceEndOn: 'Pada',
	RecurrenceEvery: 'Setiap {description}',
	RecurrenceMonthly: '{count, plural, one {Bulanan} other {Bulan}}',
	RecurrenceOn: 'pada {description}',
	RecurrenceOnAllDays: 'pada semua hari',
	RecurrenceUntil: 'sehingga {description}',
	RecurrenceWeekly: '{count, plural, one {Minggu} other {Minggu}}',
	RecurrenceYearly: '{count, plural, one {Tahunan} other {Tahun}}',
	Recurring: 'Berulang',
	RecurringAppointment: 'Temu janji berulang',
	RecurringAppointmentsLimitedBannerText:
		'Tidak semua janji temu berulang ditunjukkan. Kurangkan julat tarikh untuk melihat semua janji temu berulang untuk tempoh tersebut.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# acara} other {# acara}}</b> akan dicipta pada tarikh berikut',
	Redo: 'Buat semula',
	ReferFriends: 'Rujuk kawan',
	Reference: 'Rujukan',
	ReferralCreditedNotificationSubject: 'Kredit rujukan anda sebanyak {currency} {amount} telah digunakan',
	ReferralEmailDefaultBody: `Terima kasih kepada {name}, anda telah menerima naik taraf PERCUMA 3 bulan ke Carepatron. Sertai komuniti kami yang terdiri daripada lebih 3 juta pengamal kesihatan yang dibina untuk cara kerja yang baru!
Terima kasih,
Pasukan Carepatron`,
	ReferralEmailDefaultSubject: 'Anda telah dijemput untuk menyertai Carepatron',
	ReferralHasNotSignedUpDescription: 'Rakan anda belum mendaftar lagi',
	ReferralHasSignedUpDescription: 'Rakan anda telah mendaftar.',
	ReferralInformation: 'Maklumat rujukan',
	ReferralJoinedNotificationSubject: '{actorProfileName} telah menyertai Carepatron',
	ReferralListErrorDescription: 'Senarai rujukan tidak dapat dimuatkan.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {bulan} other {bulan}}</b> aktif',
	ReferralRewardBanner: 'Daftar dan tuntut ganjaran rujukan anda!',
	Referrals: 'Rujukan',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} bulan {percentOff, select, 100 {percuma dibayar} other {{percentOff}% diskaun}} {type, select, SubscriptionUpgrade {naik taraf} other {}}',
	ReferredUserBenefitTitle: 'Mereka dapat!',
	Referrer: 'perujuk',
	ReferringProvider: 'Pembekal yang merujuk',
	ReferringUserBenefitSubtitle: 'Kredit USD${creditAmount} apabila <mark>3 kawan</mark> mengaktifkan.',
	ReferringUserBenefitTitle: 'Anda dapat!',
	RefreshPage: 'Refresh PageSegar Semula Laman',
	Refund: 'Bayaran balik',
	RefundAcknowledgement: 'Saya telah mengembalikan wang kepada {clientName} di luar Carepatron.',
	RefundAcknowledgementValidationMessage: 'Sila sahkan anda telah membayar balik jumlah ini',
	RefundAmount: 'Jumlah bayaran balik',
	RefundContent:
		'Bayaran balik mengambil masa 7-10 hari untuk dipaparkan dalam akaun pelanggan anda. Yuran pembayaran tidak akan dikembalikan, tetapi tiada caj tambahan dikenakan untuk bayaran balik. Bayaran balik tidak boleh dibatalkan dan sesetengahnya mungkin memerlukan semakan sebelum diproses.',
	RefundCouldNotBeProcessed: 'Bayaran balik tidak dapat diproses',
	RefundError:
		'Bayaran balik ini tidak boleh diproses secara automatik pada masa ini. Sila hubungi sokongan Carepatron untuk meminta pembayaran balik pembayaran ini.',
	RefundExceedTotalValidationError: 'Jumlah tidak boleh melebihi jumlah yang dibayar',
	RefundFailed: 'Bayaran balik gagal',
	RefundFailedTooltip:
		'Membayar balik pembayaran ini sebelum ini gagal dan tidak boleh dicuba semula. Sila hubungi sokongan.',
	RefundNonStripePaymentContent:
		'Pembayaran ini dibuat menggunakan kaedah di luar Carepatron (cth, tunai, perbankan internet). Mengeluarkan bayaran balik dalam Carepatron tidak akan mengembalikan sebarang dana kepada pelanggan.',
	RefundReasonDescription: 'Menambah sebab bayaran balik boleh membantu semasa menyemak urus niaga pelanggan anda',
	Refunded: 'Dipulangkan',
	Refunds: 'Bayaran balik',
	RefundsTableEmptyState: 'Tiada bayaran balik ditemui',
	Regenerate: 'Jana semula',
	RegisterButton: 'Daftar',
	RegisterEmail: 'E-mel',
	RegisterFirstName: 'nama pertama',
	RegisterLastName: 'nama keluarga',
	RegisterPassword: 'Kata laluan',
	RegisteredNurse: 'Jururawat Berdaftar',
	RehabilitationCounselor: 'Kaunselor Pemulihan',
	RejectAppointmentFormTitle: 'Tidak boleh buat? Sila beritahu kami sebabnya dan cadangkan masa baharu.',
	Rejected: 'ditolak',
	Relationship: 'Perhubungan',
	RelationshipDetails: 'Butiran perhubungan',
	RelationshipEmptyStateTitle: 'Kekal berhubung dengan mereka yang menyokong pelanggan anda',
	RelationshipPageAccessTypeColumnName: 'Akses profil',
	RelationshipSavedSuccessSnackbar: 'Perhubungan berjaya disimpan!',
	RelationshipSelectorFamilyAdmin: 'Keluarga',
	RelationshipSelectorFamilyMember: 'ahli keluarga',
	RelationshipSelectorProviderAdmin: 'Pentadbir pembekal',
	RelationshipSelectorProviderStaff: 'Kakitangan pembekal',
	RelationshipSelectorSupportNetworkPrimary: 'kawan',
	RelationshipSelectorSupportNetworkSecondary: 'Rangkaian sokongan',
	RelationshipStatus: 'Status Perhubungan',
	RelationshipType: 'Jenis perhubungan',
	RelationshipTypeClientOwner: 'Pelanggan',
	RelationshipTypeFamilyAdmin: 'perhubungan',
	RelationshipTypeFamilyMember: 'Keluarga',
	RelationshipTypeFriendOrSupport: 'Rakan atau rangkaian sokongan',
	RelationshipTypeProviderAdmin: 'Pentadbir pembekal',
	RelationshipTypeProviderStaff: 'Kakitangan',
	RelationshipTypeSelectorPlaceholder: 'Cari jenis perhubungan',
	Relationships: 'perhubungan',
	Remaining: 'yang tinggal',
	RemainingTime: '{time} tinggal',
	Reminder: 'Peringatan',
	ReminderColor: 'Warna peringatan',
	ReminderDetails: 'Butiran peringatan',
	ReminderEditDisclaimer: 'Perubahan hanya akan ditunjukkan dalam pelantikan baharu',
	ReminderSettings: 'Tetapan peringatan janji temu',
	Reminders: 'Peringatan',
	Remove: 'Alih keluar',
	RemoveAccess: 'Alih keluar akses',
	RemoveAllGuidesBtn: 'Alih keluar semua panduan',
	RemoveAllGuidesPopoverBody:
		'Apabila anda selesai dengan panduan onboarding hanya gunakan butang alih keluar panduan pada setiap panel.',
	RemoveAllGuidesPopoverTitle: 'Tidak lagi memerlukan panduan orientasi anda?',
	RemoveAsDefault: 'Alih keluar sebagai lalai',
	RemoveAsIntake: 'Keluarkan sebagai pengambilan',
	RemoveCol: 'Alih keluar lajur',
	RemoveColor: 'Keluarkan warna',
	RemoveField: 'Alih keluar medan',
	RemoveFromCall: 'Alih keluar daripada panggilan',
	RemoveFromCallDescription:
		'Adakah anda pasti ingin mengalih keluar <mark>{attendeeName}</mark> daripada panggilan video ini?',
	RemoveFromCollection: 'Alih keluar daripada koleksi',
	RemoveFromCommunity: 'Alih keluar daripada komuniti',
	RemoveFromFolder: 'Buang dari folder',
	RemoveFromFolderConfirmationDescription:
		'Adakah anda pasti anda ingin membuang templat ini dari folder ini? Tindakan ini tidak boleh dibatalkan, tetapi anda boleh memilih untuk memindahkannya kembali kemudian.',
	RemoveFromIntakeDefault: 'Alih keluar daripada lalai pengambilan',
	RemoveGuides: 'Alih keluar panduan',
	RemoveMfaConfirmationDescription:
		'Mengalih keluar Pengesahan Berbilang Faktor (MFA) akan mengurangkan keselamatan akaun anda. Adakah anda mahu meneruskan?',
	RemoveMfaConfirmationTitle: 'Alih keluar MFA?',
	RemovePaymentMethodDescription: `Ini akan mengalih keluar semua akses dan penggunaan kaedah pembayaran ini pada masa hadapan.
 Tindakan ini tidak boleh dibuat asal.`,
	RemoveRow: 'Alih keluar baris',
	RemoveTable: 'Keluarkan jadual',
	RemoveTemplateAsDefaultIntakeSuccess: 'Berjaya alihkan {templateTitle} sebagai templat pengambilan lalai',
	RemoveTemplateFromCommunity: 'Alih keluar templat daripada komuniti',
	RemoveTemplateFromFolder: '{templateTitle} berjaya dikeluarkan dari {folderTitle}',
	Rename: 'Namakan semula',
	RenderingProvider: 'Pembekal rendering',
	Reopen: 'Buka semula',
	ReorderServiceGroupFailure: 'Gagal menyusun semula koleksi',
	ReorderServiceGroupSuccess: 'Berjaya menyusun semula koleksi',
	ReorderServicesFailure: 'Gagal menyusun semula perkhidmatan',
	ReorderServicesSuccess: 'Berjaya menyusun semula perkhidmatan',
	ReorderYourServiceList: 'Susun semula senarai perkhidmatan anda',
	ReorderYourServiceListDescription:
		'Cara anda mengatur perkhidmatan dan koleksi anda akan ditunjukkan pada halaman tempahan dalam talian anda untuk dilihat oleh semua pelanggan anda!',
	RepeatEvery: 'Ulang setiap',
	RepeatOn: 'Ulang pada',
	Repeating: 'Mengulang',
	Repeats: 'Berulang',
	RepeatsEvery: 'Ulang setiap',
	Rephrase: 'Ungkapan semula',
	Replace: 'Gantikan',
	ReplaceBackground: 'Gantikan latar belakang',
	ReplacementOfPriorClaim: 'Penggantian tuntutan sebelumnya',
	Report: 'Laporan',
	Reprocess: 'Proses semula',
	RepublishTemplateToCommunity: 'Cetak semula templat kepada komuniti',
	RequestANewVerificationLink: 'Minta pautan pengesahan baharu',
	RequestCoverageReport: 'Minta laporan liputan',
	RequestingDevicePermissions: 'Meminta kebenaran peranti...',
	RequirePaymentMethodDesc:
		'Pelanggan mesti memasukkan butiran kad kredit mereka untuk membuat tempahan dalam talian',
	RequirePaymentMethodLabel: 'Memerlukan butiran kad kredit',
	Required: 'diperlukan',
	RequiredField: 'Diperlukan',
	RequiredUrl: 'URL diperlukan.',
	Reschedule: 'Jadual semula',
	RescheduleBookingLinkModalDescription:
		'Pelanggan anda boleh menukar tarikh dan masa janji temu mereka menggunakan pautan ini.',
	RescheduleBookingLinkModalTitle: 'Jadual semula pautan tempahan',
	RescheduleLink: 'Jadual semula pautan',
	Resend: 'Hantar semula',
	ResendConfirmationCode: 'Hantar semula kod pengesahan',
	ResendConfirmationCodeDescription:
		'Sila masukkan alamat e-mel anda dan kami akan menghantar e-mel kepada anda kod pengesahan lain',
	ResendConfirmationCodeSuccess: 'Kod pengesahan telah dihantar semula, sila semak peti masuk anda',
	ResendNewEmailVerificationSuccess: 'Pautan pengesahan baharu telah dihantar ke {email}',
	ResendVerificationEmail: 'Hantar semula e-mel pengesahan',
	Reset: 'Tetapkan semula',
	Resources: 'Sumber',
	RespiratoryTherapist: 'Jurupulih Pernafasan',
	RespondToHistoricAppointmentError:
		'Ini adalah temu janji bersejarah, sila hubungi pengamal anda jika anda mempunyai soalan.',
	Responder: 'Responder',
	RestorableItemModalDescription:
		'Adakah anda pasti untuk memadamkan {context}?{canRestore, select, true { Anda boleh memulihkannya kemudian.} other {}}',
	RestorableItemModalTitle: 'Padamkan {type}',
	Restore: 'Pulihkan',
	RestoreAll: 'Pulihkan semua',
	Restricted: 'Terhad',
	ResubmissionCodeReferenceNumber: 'Kod penghantaran semula dan nombor rujukan',
	Resubmit: 'Serahkan semula',
	Resume: 'Sambung semula',
	Retry: 'Cuba semula',
	RetryingConnectionAttempt: 'Mencuba semula sambungan... (Percubaan {retryCount} daripada {maxRetries})',
	ReturnToForm: 'Kembali ke borang',
	RevertClaimStatus: 'Kembalikan status tuntutan',
	RevertClaimStatusDescriptionBody:
		'Tuntutan ini dikaitkan dengan pembayaran, dan perubahan status mungkin menjejaskan pengesanan atau pemprosesan pembayaran, yang mungkin memerlukan penyesuaian manual.',
	RevertClaimStatusDescriptionTitle: 'Adakah anda pasti ingin kembali ke {status}?',
	RevertClaimStatusError: 'Gagal untuk kembalikan status tuntutan',
	RevertToDraft: 'Kembali ke draf',
	Review: 'Semakan',
	ReviewsFirstQuote: 'Janji temu di mana-mana, pada bila-bila masa',
	ReviewsSecondJobTitle: 'Klinik Lifehouse',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Saya juga suka aplikasi carepatron. Membantu saya menjejaki pelanggan saya dan bekerja semasa dalam perjalanan.',
	ReviewsThirdJobTitle: 'Klinik Teluk Manila',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote:
		'Kemudahan navigasi dan antara muka pengguna yang cantik membawa senyuman ke wajah saya setiap hari.',
	RightAlign: 'Jajar ke kanan',
	Role: 'Peranan',
	Roster: 'Hadirin',
	RunInBackground: 'Jalankan di latar belakang',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Anda akan meninggalkan Carepatron',
	SafetyRedirectSubtext: 'Jika anda mempercayai pautan ini, pilih pautan ini untuk meneruskan',
	SalesRepresentative: 'Wakil Jualan',
	SalesTax: 'Cukai Jualan',
	SalesTaxHelp: 'Termasuk cukai jualan ke atas invois yang dijana',
	SalesTaxIncluded: 'ya',
	SalesTaxNotIncluded: 'Tidak',
	SaoPaulo: 'São Paulo',
	Saturday: 'Sabtu',
	Save: 'Jimat',
	SaveAndClose: 'Jimat ',
	SaveAndExit: 'Jimat ',
	SaveAndLock: 'Simpan dan kunci',
	SaveAsDraft: 'Simpan sebagai draf',
	SaveCardForFuturePayments: 'Simpan kad untuk pembayaran masa hadapan',
	SaveChanges: 'Simpan perubahan',
	SaveCollection: 'Simpan Koleksi',
	SaveField: 'Simpan medan',
	SavePaymentMethod: 'Simpan kaedah pembayaran',
	SavePaymentMethodDescription: 'Anda tidak akan dicaj sehingga janji temu pertama anda.',
	SavePaymentMethodSetupError:
		'Ralat yang tidak dijangka berlaku dan kami tidak dapat mengkonfigurasi pembayaran pada masa ini.',
	SavePaymentMethodSetupInvoiceLater: 'Pembayaran boleh disediakan dan disimpan semasa membayar invois pertama anda.',
	SaveSection: 'Simpan bahagian',
	SaveService: 'Buat perkhidmatan baharu',
	SaveTemplate: 'Simpan templat',
	Saved: 'Disimpan',
	SavedCards: 'Kad yang disimpan',
	SavedPaymentMethods: 'Disimpan',
	Saving: 'Menyimpan...',
	ScheduleAppointmentsAndOnlineServices: 'Jadual temu janji dan perkhidmatan dalam talian',
	ScheduleName: 'Nama jadual',
	ScheduleNew: 'Jadual baru',
	ScheduleSend: 'Jadual penghantaran',
	ScheduleSendAlertInfo: 'Perbualan dalam jadual akan dihantar pada masa yang dijadualkan.',
	ScheduleSendByName: '<strong>Hantar Jadual</strong> • {time} oleh {displayName}',
	ScheduleSetupCall: 'Jadualkan panggilan persediaan',
	Scheduled: 'Dijadualkan',
	SchedulingSend: 'Menjadualkan penghantaran',
	School: 'Sekolah',
	ScrollToTop: 'Tatal ke atas',
	Search: 'Cari',
	SearchAndConvertToLanguage: 'Carian dan penterjemahan bahasa',
	SearchBasicBlocks: 'Cari blok asas',
	SearchByName: 'Cari mengikut nama',
	SearchClaims: 'Carian tuntutan',
	SearchClientFields: 'Cari medan klien',
	SearchClients: 'Cari mengikut nama pelanggan, ID pelanggan atau nombor telefon',
	SearchCommandNotFound: 'Tiada hasil ditemui untuk "{searchTerm}"',
	SearchContacts: 'Pelanggan atau kenalan',
	SearchContactsPlaceholder: 'Cari kenalan',
	SearchConversations: 'Cari perbualan',
	SearchInputPlaceholder: 'Cari semua sumber',
	SearchInvoiceNumber: 'Cari nombor invois',
	SearchInvoices: 'Cari invois',
	SearchMultipleContacts: 'Pelanggan atau kenalan',
	SearchMultipleContactsOptional: 'Pelanggan atau kenalan (pilihan)',
	SearchOrCreateATag: 'Cari atau buat teg',
	SearchPayments: 'Cari pembayaran',
	SearchPrepopulatedData: 'Cari medan data pra-penduduk',
	SearchRelationships: 'Cari perhubungan',
	SearchRemindersAndWorkflows: 'Cari peringatan dan aliran kerja',
	SearchServices: 'Perkhidmatan carian',
	SearchTags: 'Tag carian',
	SearchTeamMembers: 'Cari ahli pasukan',
	SearchTemplatePlaceholder: 'Carian {templateCount}+ sumber',
	SearchTimezone: 'Cari zon waktu...',
	SearchTrashItems: 'Cari item',
	SearchUnsplashPlaceholder: 'Cari foto resolusi tinggi percuma daripada Unsplash',
	Secondary: 'Menengah',
	SecondaryInsurance: 'Insurans sekunder',
	SecondaryPolicy: 'Insurans sekunder',
	SecondaryTimezone: 'Zon waktu sekunder',
	Secondly: 'Kedua',
	Section: 'Bahagian',
	SectionCannotBeEmpty: 'Satu bahagian mesti mempunyai sekurang-kurangnya satu baris',
	SectionFormSecondaryText: 'Tajuk bahagian dan huraian',
	SectionName: 'Nama bahagian',
	Sections: 'Bahagian',
	SeeLess: 'Lihat lebih sedikit',
	SeeLessUpcomingAppointments: 'Lihat kurang janji temu akan datang',
	SeeMore: 'Lihat lagi',
	SeeMoreUpcomingAppointments: 'Lihat lebih banyak janji temu akan datang',
	SeeTemplateLibrary: 'Lihat perpustakaan templat',
	Seen: 'Dilihat',
	SeenByName: '**Dilihat** • {time} oleh {displayName}',
	SelectAll: 'Pilih semua',
	SelectAssignees: 'Pilih penerima tugasan',
	SelectAttendees: 'Pilih peserta',
	SelectCollection: 'Pilih Koleksi',
	SelectCorrespondingAttributes: 'Pilih atribut yang sepadan',
	SelectPayers: 'Pilih Pembayar',
	SelectProfile: 'Pilih profil',
	SelectServices: 'Pilih perkhidmatan',
	SelectTags: 'Pilih Teg',
	SelectTeamOrCommunity: 'Pilih Pasukan atau Komuniti',
	SelectTemplate: 'Pilih Templat',
	SelectType: 'Pilih jenis',
	Selected: 'Dipilih',
	SelfPay: 'Bayar sendiri',
	Send: 'Hantar',
	SendAndClose: 'Hantar ',
	SendAndStopIgnore: 'Hantar dan berhenti mengabaikan',
	SendEmail: 'Hantar e-mel',
	SendIntake: 'Hantar pengambilan',
	SendIntakeAndForms: 'Hantar Pengambilan ',
	SendMeACopy: 'Hantarkan saya salinan',
	SendNotificationEmailWarning:
		'Beberapa peserta tidak mempunyai alamat e-mel dan tidak akan menerima pemberitahuan dan peringatan automatik.',
	SendNotificationLabel: 'Pilih peserta untuk diberitahu dengan e-mel',
	SendOnlinePayment: 'Hantar pembayaran dalam talian',
	SendOnlinePaymentTooltipTitleAdmin: 'Sila tambahkan tetapan pembayaran pilihan anda',
	SendOnlinePaymentTooltipTitleStaff: 'Sila minta pemilik penyedia menyediakan pembayaran dalam talian.',
	SendPaymentLink: 'Hantar pautan pembayaran',
	SendReaction: 'Hantar reaksi',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Hantar e-mel pengesahan',
	SendingFailed: 'Penghantaran gagal',
	Sent: 'Dihantar',
	SentByName: '**Dihantar** • {time} oleh {displayName}',
	Seoul: 'Seoul',
	SeparateDuplicateClientsDescription:
		'Rekod pelanggan yang dipilih akan kekal berasingan daripada yang lain melainkan anda memilih untuk menggabungkannya',
	Service: 'Perkhidmatan',
	'Service/s': 'Perkhidmatan',
	ServiceAdjustment: 'Pelarasan perkhidmatan',
	ServiceAllowNewClientsIndicator: 'Benarkan pelanggan baharu',
	ServiceAlreadyExistsInCollection: 'Perkhidmatan sudah wujud dalam koleksi',
	ServiceBookableOnlineIndicator: 'Boleh ditempah dalam talian',
	ServiceCode: 'Kod',
	ServiceCodeErrorMessage: 'Kod perkhidmatan diperlukan',
	ServiceCodeSelectorPlaceholder: 'Tambahkan kod perkhidmatan',
	ServiceColour: 'Warna perkhidmatan',
	ServiceCoverageDescription: 'Pilih perkhidmatan yang layak dan bayar bersama untuk polisi insurans ini.',
	ServiceCoverageGoToServices: 'Pergi ke perkhidmatan',
	ServiceCoverageNoServicesDescription:
		'Sesuaikan amaun bayar bersama perkhidmatan untuk mengatasi bayar bersama dasar lalai. Lumpuhkan perlindungan untuk mengelakkan perkhidmatan dituntut terhadap polisi.',
	ServiceCoverageNoServicesLabel: 'Tiada perkhidmatan ditemui.',
	ServiceCoverageTitle: 'Liputan perkhidmatan',
	ServiceDate: 'Tarikh perkhidmatan',
	ServiceDetails: 'Butiran perkhidmatan',
	ServiceDuration: 'Tempoh',
	ServiceEmptyState: 'Tiada perkhidmatan lagi',
	ServiceErrorMessage: 'Perkhidmatan diperlukan',
	ServiceFacility: 'Kemudahan perkhidmatan',
	ServiceName: 'Nama perkhidmatan',
	ServiceRate: 'Kadar',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} untuk {serviceReceiptNumber, select, undefined {user} other {{clientName}}} memerlukan maklumat tambahan',
	ServiceSalesTax: 'Cukai jualan',
	ServiceType: 'Perkhidmatan',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Tekan muat semula untuk memuat semula dan mendapatkan kemas kini Carepatron terbaharu.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Muat semula',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Anda menggunakan versi yang lebih lama',
	ServiceWorkerForceUIUpdateDialogTitle: 'Selamat kembali!',
	Services: 'Perkhidmatan',
	ServicesAndAvailability: 'Perkhidmatan ',
	ServicesAndDiagnosisCodesHeader: 'Tambahkan perkhidmatan dan kod diagnosis',
	ServicesCount: '{count,plural,=0{Perkhidmatan}one{Perkhidmatan}other{Perkhidmatan}}',
	ServicesPlaceholder: 'Perkhidmatan',
	ServicesProvidedBy: 'Perkhidmatan disediakan oleh',
	SetAPhysicalAddress: 'Tetapkan alamat fizikal',
	SetAVirtualLocation: 'Tetapkan lokasi maya',
	SetAsDefault: 'Tetapkan sebagai lalai',
	SetAsIntake: 'Tetapkan sebagai pengambilan',
	SetAsIntakeDefault: 'Tetapkan sebagai lalai pengambilan',
	SetAvailability: 'Tetapkan ketersediaan',
	SetTemplateAsDefaultIntakeSuccess: 'Berjaya menetapkan {templateTitle} sebagai templat pengambilan lalai',
	SetUpMfaButton: 'Sediakan MFA',
	SetYourLocation: 'Tetapkan anda<mark> lokasi</mark>',
	SetYourLocationDescription:
		'Saya tidak mempunyai alamat perniagaan <span>(perkhidmatan dalam talian dan mudah alih sahaja)</span>',
	SettingUpPayers: 'Menetapkan pembayar',
	Settings: 'tetapan',
	SettingsNewUserPasswordDescription:
		'Setelah anda mendaftar, kami akan menghantar kod pengesahan yang boleh anda gunakan untuk mengesahkan akaun anda',
	SettingsNewUserPasswordTitle: 'Daftar ke Carepatron',
	SettingsTabAutomation: 'Automasi',
	SettingsTabBillingDetails: 'Butiran pengebilan',
	SettingsTabConnectedApps: 'Apl bersambung',
	SettingsTabCustomFields: 'Medan tersuai',
	SettingsTabDetails: 'Butiran',
	SettingsTabInvoices: 'Invois',
	SettingsTabLocations: 'Lokasi',
	SettingsTabNotifications: 'Pemberitahuan',
	SettingsTabOnlineBooking: 'Tempahan dalam talian',
	SettingsTabPayers: 'Pembayar',
	SettingsTabReminders: 'Peringatan',
	SettingsTabServices: 'Perkhidmatan',
	SettingsTabServicesAndAvailability: 'Perkhidmatan dan ketersediaan',
	SettingsTabSubscriptions: 'Langganan',
	SettingsTabWorkflowAutomations: 'Automasi',
	SettingsTabWorkflowReminders: 'Peringatan asas',
	SettingsTabWorkflowTemplates: 'templat',
	Setup: 'Sediakan',
	SetupGuide: 'Panduan Setup',
	SetupGuideAddServicesActionLabel: 'Mula',
	SetupGuideAddServicesSubtitle: '4 langkah • 2 minit',
	SetupGuideAddServicesTitle: 'Tambah perkhidmatan anda',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Mula',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 langkah • 3 minit',
	SetupGuideEnableOnlinePaymentsTitle: 'Dayakan pembayaran dalam talian',
	SetupGuideImportClientsActionLabel: 'Mula',
	SetupGuideImportClientsSubtitle: '4 langkah • 3 minit',
	SetupGuideImportClientsTitle: 'Import klien anda',
	SetupGuideImportTemplatesActionLabel: 'Mula',
	SetupGuideImportTemplatesSubtitle: '2 langkah • 1 minit',
	SetupGuideImportTemplatesTitle: 'Import templat anda',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Mula',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 langkah • 2 minit',
	SetupGuidePersonalizeWorkspaceTitle: '<h1>Peribadikan ruang kerja anda</h1>',
	SetupGuideSetLocationActionLabel: 'Mula',
	SetupGuideSetLocationSubtitle: '4 langkah • 1 minit',
	SetupGuideSetLocationTitle: 'Tetapkan lokasi anda',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Jemput pasukan',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'Ajak pasukan anda untuk berkomunikasi dan menguruskan tugasan dengan mudah.',
	SetupGuideSuggestedAddTeamMembersTag: 'Tetapan',
	SetupGuideSuggestedAddTeamMembersTitle: 'Tambah ahli pasukan',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Sesuaikan',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Kelihatan profesional dengan logo dan warna jenama anda yang unik.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Sesuaikan jenama',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Muat Turun',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Akses ruang kerja anda di mana-mana, bila-bila masa dan pada mana-mana peranti.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Tetapan',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Muat turun aplikasi ini',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Tetapkan ketersediaan',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Cegah tempahan berganda dengan menetapkan ketersediaan anda.',
	SetupGuideSuggestedEditAvailabilityTag: 'Penjadualan',
	SetupGuideSuggestedEditAvailabilityTitle: 'Edit ketersediaan',
	SetupGuideSuggestedImportClientsActionLabel: 'Import',
	SetupGuideSuggestedImportClientsSubtitle:
		'Muat naik data pelanggan sedia ada anda dengan serta-merta hanya dengan satu klik.',
	SetupGuideSuggestedImportClientsTag: 'Tetapan',
	SetupGuideSuggestedImportClientsTitle: 'Import klien',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Edit peringatan',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'Kurangkan tidak hadir dengan peringatan temu janji automatik.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Peribadikan peringatan',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Mula panggilan',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Hoskan panggilan dan hubungi pelanggan menggunakan alat video kami yang dikuasakan oleh AI.',
	SetupGuideSuggestedStartVideoCallTag: 'Teleperubatan',
	SetupGuideSuggestedStartVideoCallTitle: 'Mula panggilan video',
	SetupGuideSuggestedTryActionsTitle: 'Perkara yang boleh dicuba 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Cuba Bantuan AI',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Dapatkan jawapan segera kepada semua soalan kerja anda.',
	SetupGuideSuggestedUseAIAssistantTag: 'Baru',
	SetupGuideSuggestedUseAIAssistantTitle: 'Gunakan pembantu AI',
	SetupGuideSyncCalendarActionLabel: 'Mula',
	SetupGuideSyncCalendarSubtitle: '1 langkah • kurang dari 1 minit',
	SetupGuideSyncCalendarTitle: 'Segerakkan kalendar anda',
	SetupGuideVerifyEmailLabel: 'Sahkan',
	SetupGuideVerifyEmailSubtitle: '2 langkah • 2 minit',
	SetupOnlineStripePayments: 'Gunakan jalur untuk pembayaran dalam talian',
	SetupPayments: 'Tetapkan pembayaran',
	Sex: 'Seks',
	SexSelectorPlaceholder: 'Lelaki / Perempuan / Lebih suka untuk tidak mengatakan',
	Share: 'Kongsi',
	ShareBookingLink: 'Kongsi pautan tempahan',
	ShareNoteDefaultMessage: `Hai{name} telah berkongsi "{documentName}" dengan anda.

Terima kasih,
{practiceName}`,
	ShareNoteMessage: `Hai
{name} telah berkongsi "{documentName}" {isResponder, select, true {dengan beberapa soalan untuk anda isi.} other {dengan anda.}}

Terima kasih,
{practiceName}`,
	ShareNoteTitle: 'Kongsi ‘{noteTitle}’',
	ShareNotesWithClients: 'Kongsi dengan pelanggan atau kenalan',
	ShareScreen: 'Kongsi skrin',
	ShareScreenNotSupported: 'Peranti/pelayar anda tidak menyokong ciri skrin kongsi',
	ShareScreenWithId: 'Skrin {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Benarkan orang lain untuk melihat templat ini dan mengemukakannya sebagai borang.',
	ShareTemplateAsPublicFormModalTitle: 'Kongsi pautan untuk ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Konfigurasi borang awam berjaya dikemas kini',
	ShareTemplateAsPublicFormSectionCustomization: 'Penyesuaian',
	ShareTemplateAsPublicFormShowPoweredBy: 'Tunjukkan "Powered by Carepatron" pada borang saya',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage:
		'Tunjukkan/sembunyikan “Powered by Carepatron” pada borang saya',
	ShareTemplateAsPublicFormTrigger: 'Kongsi',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Gunakan penjenamaan ruang kerja',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Tunjukkan/sembunyikan jenama ruang kerja',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Hantar kod untuk pelanggan sedia ada dan pelanggan baharu',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: 'Tandatangan sentiasa memerlukan e-mel disahkan',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Hanya menghantar kod untuk pelanggan sedia ada',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Tidak pernah menghantar kod',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Memilih 'Tidak Pernah' mungkin membenarkan pengguna yang tidak disahkan untuk menulis ganti data pelanggan sekiranya mereka menggunakan alamat e-mel pelanggan sedia ada.`,
	ShareWithCommunity: 'Kongsi Dengan Komuniti',
	ShareYourReferralLink: 'Kongsi pautan rujukan anda',
	ShareYourScreen: 'Kongsi skrin anda',
	SheHer: 'Dia / Dia',
	ShortTextAnswer: 'Jawapan teks pendek',
	ShortTextFormPrimaryText: 'Teks pendek',
	ShortTextFormSecondaryText: 'Kurang daripada 300 aksara jawapan',
	Show: 'Tunjukkan',
	ShowColumn: 'Tunjukkan lajur',
	ShowColumnButton: 'Tunjukkan lajur {value} butang',
	ShowColumns: 'Tunjukkan lajur',
	ShowColumnsMenu: 'Tunjukkan menu lajur',
	ShowDateDurationDescription: 'cth. 29 tahun',
	ShowDateDurationLabel: 'Tunjukkan tempoh tarikh',
	ShowDetails: 'Tunjukkan butiran',
	ShowField: 'Tunjukkan medan',
	ShowFullAddress: 'Tunjukkan alamat',
	ShowHideFields: 'Tunjukkan / Sembunyikan medan',
	ShowIcons: 'Tunjukkan ikon',
	ShowLess: 'Tunjukkan lebih sedikit',
	ShowMeetingTimers: 'Tunjukkan pemasa mesyuarat',
	ShowMenu: 'Tunjukkan menu',
	ShowMergeSummarySidebar: 'Tunjukkan ringkasan gabungan',
	ShowMore: 'Tunjukkan lagi',
	ShowOnTranscript: 'Tunjukkan pada transkrip',
	ShowReactions: 'Tunjukkan reaksi',
	ShowSection: 'Tunjukkan bahagian',
	ShowServiceCode: 'Tunjukkan kod perkhidmatan',
	ShowServiceDescription: 'Tunjukkan penerangan tentang tempahan perkhidmatan',
	ShowServiceDescriptionDesc: 'Pelanggan boleh melihat penerangan perkhidmatan semasa membuat tempahan',
	ShowServiceGroups: 'Tunjukkan koleksi',
	ShowServiceGroupsDesc:
		'Pelanggan akan ditunjukkan perkhidmatan dikumpulkan mengikut kutipan semasa membuat tempahan',
	ShowSpeakers: 'Tunjukkan pembesar suara',
	ShowTax: 'Tunjukkan cukai',
	ShowTimestamp: 'Tunjukkan cap masa',
	ShowUnits: 'Tunjukkan unit',
	ShowWeekends: 'Tunjukkan hujung minggu',
	ShowYourView: 'Tunjukkan pandangan anda',
	SignInWithApple: 'Log masuk dengan Apple',
	SignInWithGoogle: 'Log masuk dengan Google',
	SignInWithMicrosoft: 'Log masuk dengan Microsoft',
	SignUpTitleReferralDefault: '<mark>daftar</mark> dan tuntut ganjaran rujukan anda',
	SignUpTitleReferralUpgrade:
		'Mulakan {durationInMonths} bulan anda <mark>{percentOff, select, 100 {percuma} other {{percentOff}% diskaun}} naik taraf</mark>',
	SignatureCaptureError: 'Tidak dapat menangkap tandatangan. Sila cuba lagi.',
	SignatureFormPrimaryText: 'Tandatangan',
	SignatureFormSecondaryText: 'Dapatkan tandatangan digital',
	SignatureInfoTooltip: 'Perwakilan visual ini bukan tandatangan elektronik yang sah.',
	SignaturePlaceholder: 'Lukis tandatangan anda di sini',
	SignedBy: 'Ditandatangani oleh',
	Signup: 'daftar',
	SignupAgreements: 'Saya bersetuju dengan {termsOfUse} dan {privacyStatement} untuk akaun saya.',
	SignupBAA: 'Perjanjian Bersekutu Perniagaan',
	SignupBusinessAgreements:
		'Atas nama saya dan perniagaan, saya bersetuju dengan {businessAssociateAgreement}, {termsOfUse}, dan {privacyStatement} untuk akaun saya.',
	SignupInvitationForYou: 'Anda telah dijemput untuk menggunakan Carepatron.',
	SignupPageProviderWarning:
		'Jika pentadbir anda telah membuat akaun, anda perlu meminta mereka menjemput anda ke dalam pembekal tersebut. Jangan gunakan borang pendaftaran ini. Untuk maklumat lanjut lihat',
	SignupPageProviderWarningLink: 'pautan ini.',
	SignupPrivacy: 'Dasar Privasi',
	SignupProfession: 'Apakah profesion yang paling menggambarkan anda?',
	SignupSubtitle:
		'Perisian pengurusan amalan Carepatron dibuat untuk pengamal dan pasukan solo. Berhenti membayar yuran yang berlebihan dan jadilah sebahagian daripada revolusi.',
	SignupSuccessDescription:
		'Sahkan alamat e-mel anda untuk memulakan orientasi anda. Jika anda tidak menerimanya dengan segera, sila semak folder spam anda.',
	SignupSuccessTitle: 'Sila semak e-mel anda',
	SignupTermsOfUse: 'Syarat Penggunaan',
	SignupTitleClient: '<mark>Urus kesihatan anda</mark> dari satu tempat',
	SignupTitleLast: 'dan semua kerja yang anda lakukan! - Ia percuma',
	SignupTitleOne: '<mark>Memberi kuasa kepada anda</mark> , ',
	SignupTitleThree: '<mark>Memperkasakan pelanggan anda</mark> , ',
	SignupTitleTwo: '<mark>Memperkasakan pasukan anda</mark> , ',
	Simple: 'Mudah',
	SimplifyBillToDetails: 'Permudahkan bil kepada butiran',
	SimplifyBillToHelperText: 'Hanya baris pertama digunakan apabila ia sepadan dengan pelanggan',
	Singapore: 'Singapura',
	Single: 'Bujang',
	SingleChoiceFormPrimaryText: 'Pilihan tunggal',
	SingleChoiceFormSecondaryText: 'Pilih satu pilihan sahaja',
	Sister: 'Kakak',
	SisterInLaw: 'Kakak ipar',
	Skip: 'Langkau',
	SkipLogin: 'Langkau log masuk',
	SlightBlur: 'Kaburkan sedikit latar belakang anda',
	Small: 'Kecil',
	SmartChips: 'Cip pintar',
	SmartDataChips: 'Cip data pintar',
	SmartReply: 'Balas Cepat',
	SmartSuggestNewClient: '<strong>Cadangan Pintar</strong> cipta {name} sebagai klien baharu',
	SmartSuggestedFieldDescription: 'Bidang ini ialah cadangan pintar',
	SocialSecurityNumber: 'Nombor keselamatan sosial',
	SocialWork: 'Kerja sosial',
	SocialWorker: 'Pekerja Sosial',
	SoftwareDeveloper: 'Pembangun Perisian',
	Solo: 'Solo',
	Someone: 'Seseorang',
	Son: 'nak',
	SortBy: 'Isih mengikut',
	SouthAmerica: 'Amerika Selatan',
	Speaker: 'Penceramah',
	SpeakerSource: 'Sumber pembesar suara',
	Speakers: 'Penceramah',
	SpecifyPaymentMethod: 'Nyatakan kaedah pembayaran',
	SpeechLanguagePathology: 'Patologi pertuturan-bahasa',
	SpeechTherapist: 'Jurupulih Pertuturan',
	SpeechTherapists: 'Jurupulih Pertuturan',
	SpeechTherapy: 'Terapi Pertuturan',
	SportsMedicinePhysician: 'Pakar Perubatan Sukan',
	Spouse: 'pasangan',
	SpreadsheetColumnExample: 'cth ',
	SpreadsheetColumns: 'Lajur Spreadsheet',
	SpreadsheetUploaded: 'Hamparan Dimuat Naik',
	SpreadsheetUploading: 'Memuat naik...',
	Staff: 'Kakitangan',
	StaffAccessDescriptionAdmin: 'Pentadbir boleh menguruskan segala-galanya di platform.',
	StaffAccessDescriptionStaff: `Ahli kakitangan boleh mengurus pelanggan, nota dan dokumentasi yang telah mereka buat atau telah dikongsi
 dengan mereka, jadualkan janji temu, uruskan invois.`,
	StaffContactAssignedSubject:
		'{actorProfileName} telah menetapkan {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} dan {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { dan 1 pelanggan lain} other { dan # pelanggan lain}} kepada anda',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} telah berkongsi peti masuk {inboxName} dengan anda.',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} telah mengalih keluar akses anda ke peti masuk {inboxName}',
	StaffMembers: 'Ahli Kakitangan',
	StaffMembersNumber: '{billedUsers, plural, one {# ahli pasukan} other {# ahli pasukan}}',
	StaffSavedSuccessSnackbar: 'Maklumat ahli pasukan berjaya disimpan!',
	StaffSelectorAdminRole: 'Pentadbir',
	StaffSelectorStaffRole: 'Ahli kakitangan',
	StandardAppointment: 'Pelantikan Standard',
	StandardColor: 'Warna tugas',
	StartAndEndTime: 'Masa mula dan masa tamat',
	StartCall: 'Mulakan panggilan',
	StartDate: 'Tarikh mula',
	StartDictating: 'Mula didikte',
	StartImport: 'Mulakan import',
	StartRecordErrorTitle: 'Terdapat ralat semasa memulakan rakaman anda',
	StartRecording: 'Mula merakam',
	StartTimeIncrements: 'Kenaikan masa mula',
	StartTimeIncrementsView: '{startTimeIncrements} min selang masa',
	StartTranscribing: 'Mula Transkripsi',
	StartTranscribingNotes:
		'Sila pilih pelanggan yang ingin anda hasilkan nota. Kemudian klik butang "Mula Transkripsi" untuk mula merakam',
	StartTranscription: 'Mulakan transkripsi',
	StartVideoCall: 'Mulakan panggilan video',
	StartWeekOn: 'Mulakan minggu pada',
	StartedBy: 'Dimulakan oleh ',
	Starter: 'Pemula',
	State: 'negeri',
	StateIndustrialAccidentProviderNumber: 'Nombor pembekal kemalangan industri negeri',
	StateLicenseNumber: 'Nombor lesen negeri',
	Statement: 'Kenyataan',
	StatementDescriptor: 'Deskriptor penyata',
	StatementDescriptorToolTip:
		'Deskriptor penyata ditunjukkan pada penyata bank atau kad kredit pelanggan anda. Ia mestilah antara 5 dan 22 aksara dan mencerminkan nama perniagaan anda.',
	StatementNumber: 'Penyata #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Masukkan label status',
	StepFather: 'Bapa tiri',
	StepMother: 'ibu tiri',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription: `Dengan berhenti mengabaikan pengirim ini, perbualan akan datang akan dihantar ke 'Peti Masuk'. Adakah anda pasti mahu berhenti mengabaikan pengirim ini?`,
	StopIgnoring: 'Berhenti mengabaikan',
	StopIgnoringSenders: 'Berhenti mengabaikan pengirim',
	StopIgnoringSendersSuccess: 'Berhenti mengabaikan alamat e-mel <mark>{addresses}</mark>',
	StopSharing: 'Berhenti berkongsi',
	StopSharingLabel: 'carepatron.com sedang berkongsi skrin anda.',
	Storage: 'Penyimpanan',
	StorageAlmostFullDescription: '🚀 Tingkatkan sekarang untuk memastikan akaun anda berjalan lancar.',
	StorageAlmostFullTitle: 'Anda telah menggunakan {percentage}% daripada had storan ruang kerja anda!',
	StorageFullDescription: 'Dapatkan lebih banyak storan dengan menaik taraf pelan anda.',
	StorageFullTitle: '	Storan anda penuh.',
	Street: 'Jalan',
	StripeAccountNotCompleteErrorCode:
		'Pembayaran dalam talian **tidak** {hasProviderName, select, true {dikonfigurasikan untuk {providerName}} other {diaktifkan untuk pembekal ini}}.',
	StripeAccountRejectedError: 'Akaun Stripe telah ditolak. Sila hubungi sokongan.',
	StripeBalance: 'Imbangan Belang',
	StripeChargesInfoToolTip: 'Membolehkan anda mengecaj debit ',
	StripeFeesDescription:
		'Carepatron menggunakan Stripe untuk membuat anda dibayar dengan cepat dan memastikan maklumat pembayaran anda selamat. Kaedah pembayaran yang tersedia berbeza mengikut wilayah, semua debit utama ',
	StripeFeesDescriptionItem1: 'Yuran pemprosesan dikenakan kepada setiap transaksi yang berjaya, anda boleh {link}.',
	StripeFeesDescriptionItem2: 'Pembayaran berlaku setiap hari tetapi ditahan sehingga 4 hari.',
	StripeFeesLinkToRatesText: 'lihat kadar kami di sini',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Maaf, terdapat minimum {minimumAmount} yang diperlukan untuk invois yang menggunakan pembayaran dalam talian',
	StripePaymentsDisabled: 'Pembayaran dalam talian dilumpuhkan. Sila semak tetapan pembayaran anda.',
	StripePaymentsUnavailable: 'Pembayaran tidak tersedia',
	StripePaymentsUnavailableDescription: 'Ralat berlaku semasa memuatkan pembayaran. Sila cuba lagi kemudian.',
	StripePayoutsInfoToolTip: 'Membolehkan anda dibayar ke dalam akaun bank anda',
	StyleYourWorkspace: '<mark>Gaya</mark> ruang kerja anda',
	StyleYourWorkspaceDescription1:
		'Kami telah mengambil aset jenama dari laman web anda. Jangan ragu untuk mengeditnya atau teruskan ke ruang kerja anda.',
	StyleYourWorkspaceDescription2:
		'Gunakan aset jenama anda untuk menyesuaikan invois dan tempahan dalam talian bagi pengalaman pelanggan yang lancar',
	SubAdvanced: 'Lanjutan',
	SubEssential: 'Penting',
	SubOrganization: 'Organisasi',
	SubPlus: 'Plus',
	SubProfessional: 'Profesional',
	Subject: 'Subjek',
	Submit: 'Hantar',
	SubmitElectronically: 'Hantar secara elektronik',
	SubmitFeedback: 'Hantar maklum balas',
	SubmitFormValidationError:
		'Sila pastikan semua medan yang diperlukan diisi dengan betul dan cuba serahkan sekali lagi.',
	Submitted: 'diserahkan',
	SubmittedDate: 'Tarikh penyerahan',
	SubscribePerMonth: 'Langganan {price} {isMonthly, select, true {sebulan} other {setahun}}',
	SubscriptionDiscountDescription:
		'{percentOff}% diskaun <span style="font-weight:bold">{months, select, null { } other { {months, plural, one {untuk # bulan} other {untuk # bulan}}}} </span>',
	SubscriptionFreeTrialDescription: 'Percuma sehingga {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Kami tidak dapat menyelesaikan pembayaran untuk langganan anda. Sila semak butiran pembayaran anda',
	SubscriptionPlanDetailsHeader: 'Setiap pengguna/bulanan dibilkan setiap tahun',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} dicadangkan setiap bulan (USD)',
	SubscriptionPlans: 'Pelan Langganan',
	SubscriptionPlansDescription:
		'Tingkatkan pelan anda untuk membuka kunci manfaat tambahan dan pastikan amalan anda berjalan lancar.',
	SubscriptionPlansDescriptionNoPermission:
		'Nampaknya anda tidak mempunyai akses untuk menaik taraf sekarang — sila hubungi pentadbir anda untuk bantuan.',
	SubscriptionSettings: 'Tetapan langganan',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> daripada storan digunakan',
	SubscriptionSettingsStorageUsed: '{digunakan} daripada {had} digunakan',
	SubscriptionSettingsUnlimitedStorage: 'Storan tanpa had tersedia',
	SubscriptionSummary: 'Ringkasan langganan',
	SubscriptionUnavailableOverStorageLimit: 'Penggunaan semasa anda melebihi had storan pelan ini.',
	SubscriptionUnpaidBannerButton: 'Pergi ke langganan',
	SubscriptionUnpaidBannerDescription: 'Sila semak bahawa butiran pembayaran anda adalah betul dan cuba lagi',
	SubscriptionUnpaidBannerTitle: 'Kami tidak dapat menyelesaikan pembayaran untuk langganan anda.',
	Subscriptions: 'Langganan',
	SubscriptionsAndPayments: 'Langganan ',
	Subtotal: 'Jumlah kecil',
	SuburbOrProvince: 'Pinggir Bandar/Wilayah',
	SuburbOrState: 'Pinggir Bandar/Negeri',
	SuccessSavedNoteChanges: 'Berjaya menyimpan perubahan nota',
	SuccessShareDocument: 'Berjaya berkongsi dokumen',
	SuccessShareNote: 'Berjaya berkongsi nota',
	SuccessfullyCreatedValue: 'Berjaya diwujudkan {value}',
	SuccessfullyDeletedTranscriptionPart: 'Berjaya memadamkan bahagian transkripsi',
	SuccessfullyDeletedValue: 'Berjaya dipadamkan {value}',
	SuccessfullySubmitted: 'Berjaya diserahkan ',
	SuccessfullyUpdatedClientSettings: 'Berjaya mengemas kini Tetapan Pelanggan',
	SuccessfullyUpdatedTranscriptionPart: 'Berjaya mengemas kini bahagian transkripsi',
	SuccessfullyUpdatedValue: 'Berjaya dikemas kini {value}',
	SuggestedAIPoweredTemplates: 'Templat Berkuasa AI yang Dicadangkan',
	SuggestedAITemplates: 'Templat AI yang Dicadangkan',
	SuggestedActions: 'Tindakan yang dicadangkan',
	SuggestedLocations: 'Lokasi yang Dicadangkan',
	Suggestions: 'Cadangan',
	Summarise: 'AI meringkaskan',
	SummarisingContent: 'Menghuraikan {title}',
	Sunday: 'Ahad',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superbill ',
	SuperbillAutomationMonthly: 'Aktif • Hari terakhir setiap bulan',
	SuperbillAutomationNoEmail:
		'Untuk menghantar dokumen pengebilan automatik dengan jayanya, tambahkan alamat e-mel untuk pelanggan ini',
	SuperbillAutomationNotActive: 'Tidak aktif',
	SuperbillAutomationUpdateFailure: 'Gagal mengemas kini tetapan automasi Superbill',
	SuperbillAutomationUpdateSuccess: 'Berjaya mengemas kini tetapan automasi Superbill',
	SuperbillClientHelperText: 'Maklumat ini diisi terlebih dahulu daripada butiran pelanggan',
	SuperbillNotFoundDescription:
		'Sila hubungi pembekal anda dan minta mereka untuk mendapatkan maklumat lanjut atau menghantar semula superbill tersebut.',
	SuperbillNotFoundTitle: 'Superbill tidak ditemui',
	SuperbillNumber: 'Superbil #{number}',
	SuperbillNumberAlreadyExists: 'Nombor resit Superbill sudah wujud',
	SuperbillPracticeHelperText: 'Maklumat ini diisi terlebih dahulu daripada tetapan pengebilan amalan',
	SuperbillProviderHelperText: 'Maklumat ini diisi terlebih dahulu daripada butiran kakitangan',
	SuperbillReceipts: 'Resit Superbill',
	SuperbillsEmptyStateDescription: 'Tiada superbill ditemui.',
	Surgeon: 'Pakar bedah',
	Surgeons: 'Pakar bedah',
	SurgicalTechnologist: 'Ahli Teknologi Pembedahan',
	SwitchFromAnotherPlatform: 'Saya beralih dari platform lain',
	SwitchToMyPortal: 'Tukar ke portal Saya',
	SwitchToMyPortalTooltip: `Akses portal peribadi anda sendiri,
 membolehkan anda menerokai anda
 pengalaman portal pelanggan.`,
	SwitchWorkspace: 'Tukar ruang kerja',
	SwitchingToADifferentPlatform: 'Beralih ke platform lain',
	Sydney: 'Sydney',
	SyncCalendar: 'Segerakkan kalendar',
	SyncCalendarModalDescription:
		'Ahli pasukan lain tidak akan dapat melihat kalendar anda yang disegerakkan. Janji temu pelanggan hanya boleh dikemas kini atau dipadamkan dari dalam Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Paparkan kalendar saya dalam Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Segerakkan kalendar saya ke Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Segerakkan janji temu Carepatron dengan kalendar saya',
	SyncCarepatronAppointmentsWithMyCalendar: 'Segerakkan temu janji Carepatron dengan kalendar saya',
	SyncGoogleCalendar: 'Segerakkan kalendar Google',
	SyncInbox: 'Segerakkan peti masuk dengan Carepatron',
	SyncMyCalendarToCarepatron: 'Segerakkan kalendar saya ke Carepatron',
	SyncOutlookCalendar: 'Segerakkan kalendar Outlook',
	SyncedFromExternalCalendar: 'Disinkronkan daripada kalendar luaran',
	SyncingCalendarName: 'Menyelaras kalendar {calendarName}',
	SyncingFailed: 'Penyegerakan gagal',
	SystemGenerated: 'Dihasilkan Sistem',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Jadual',
	TableRowLabel: 'Baris jadual untuk {value}',
	TagSelectorNoOptionsText: 'Klik "buat baharu" untuk menambah teg baharu',
	Tags: 'Tag',
	TagsInputPlaceholder: 'Cari atau buat tag',
	Task: 'Tugasan',
	TaskAttendeeStatusUpdatedSuccess: 'Berjaya mengemas kini status temu janji',
	Tasks: 'Tugasan',
	Tax: 'Cukai',
	TaxAmount: 'Jumlah Cukai',
	TaxID: 'ID Cukai',
	TaxIdType: 'Jenis ID cukai',
	TaxName: 'nama cukai',
	TaxNumber: 'Nombor cukai',
	TaxNumberType: 'Jenis nombor cukai',
	TaxNumberTypeInvalid: '{type} tidak sah',
	TaxPercentageOfAmount: '{taxName} ({percentage}% daripada {amount})',
	TaxRate: 'Kadar cukai',
	TaxRatesDescription: 'Uruskan kadar cukai yang akan digunakan pada item baris invois anda.',
	Taxable: 'Bercukai',
	TaxonomyCode: 'Kod taksonomi',
	TeacherAssistant: 'Pembantu Guru',
	Team: 'Pasukan',
	TeamMember: 'ahli pasukan',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {sudah} other {sudah}} ditempah pada masa ini.{br}Pilih masa baru untuk mengelakkan tempahan berganda.',
	TeamMembers: 'Ahli pasukan',
	TeamMembersColour: 'Warna ahli pasukan',
	TeamMembersDetails: 'Butiran ahli pasukan',
	TeamSize: 'Berapa ramai orang dalam pasukan anda?',
	TeamTemplates: 'Templat pasukan',
	TeamTemplatesSectionDescription: 'Dihasilkan oleh anda dan pasukan anda',
	TelehealthAndVideoCalls: 'Telekesihatan ',
	TelehealthProvidedOtherThanInPatientCare: 'Telekesihatan disediakan untuk selain daripada penjagaan pesakit dalam',
	TelehealthVideoCall: 'Panggilan video telekesihatan',
	Template: 'templat',
	TemplateDescription: 'Penerangan templat',
	TemplateDetails: 'Butiran templat',
	TemplateEditModeViewSwitcherDescription: 'Buat dan edit templat',
	TemplateGallery: 'Templat Komuniti',
	TemplateImportCompletedNotificationSubject:
		'Templat import selesai! {templateTitle} sudah bersedia untuk digunakan.',
	TemplateImportFailedNotificationSubject: 'Gagal mengimport fail {fileName}.',
	TemplateName: 'Nama templat',
	TemplateNotFound: 'Templat tidak ditemui.',
	TemplatePreviewErrorMessage: 'Ralat berlaku semasa memuatkan pratonton templat',
	TemplateResponderModeViewSwitcherDescription: 'Pratonton dan berinteraksi dengan borang',
	TemplateResponderModeViewSwitcherTooltipTitle: 'Semak cara borang anda dipaparkan apabila diisi oleh responden',
	TemplateSaved: 'Perubahan disimpan',
	TemplateTitle: 'Tajuk Templat',
	TemplateType: 'Jenis Templat',
	Templates: 'Templat',
	TemplatesCategoriesFilter: 'Tapis mengikut kategori',
	TemplatesPublicTemplatesFilter: ' Tapis mengikut Komuniti/Pasukan',
	Text: 'Teks',
	TextAlign: 'Penjajaran teks',
	TextColor: 'Warna teks',
	ThankYouForYourFeedback: 'Terima kasih atas maklum balas anda!',
	ThanksForLettingKnow: 'Terima kasih kerana memberitahu kami.',
	ThePaymentMethod: 'Kaedah pembayaran',
	ThemThey: 'Mereka/Mereka',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Lebih banyak tema',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Gelap',
	ThemeColorLightMode: 'Cahaya',
	ThemeColorModePickerTitle: 'Mod Warna',
	ThemeColorSystemMode: 'Sistem',
	ThemeCpColorPickerTitle: 'Tema Carepatron',
	ThemePanelDescription: 'Pilih antara mod terang dan gelap, dan sesuaikan pilihan tema anda',
	ThemePanelTitle: 'Rupa',
	Then: 'Kemudian',
	Therapist: 'Jurupulih',
	Therapists: 'Ahli terapi',
	Therapy: 'Terapi',
	Thick: 'tebal',
	Thin: 'Nipis',
	ThirdPerson: 'orang ke-3',
	ThisAndFollowingAppointments: 'Ini dan pelantikan berikut',
	ThisAndFollowingMeetings: 'Ini dan mesyuarat berikut',
	ThisAndFollowingReminders: 'Peringatan ini dan berikut',
	ThisAndFollowingTasks: 'Ini dan tugasan berikut',
	ThisAppointment: 'Pelantikan ini',
	ThisMeeting: 'Mesyuarat ini',
	ThisMonth: 'bulan ini',
	ThisPerson: 'Orang ini',
	ThisReminder: 'Peringatan ini',
	ThisTask: 'tugasan ini',
	ThisWeek: 'minggu ini',
	ThreeDay: '3 Hari',
	Thursday: 'Khamis',
	Time: 'Masa',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}j',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Format masa',
	TimeIncrement: 'Kenaikan masa',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {pagi} other {petang}}',
	TimeslotSize: 'Saiz slot masa',
	Timestamp: 'Cap masa',
	Timezone: 'Zon waktu',
	TimezoneDisplay: 'Paparan zon waktu',
	TimezoneDisplayDescription: 'Urus tetapan paparan zon waktu anda.',
	Title: 'Tajuk',
	To: 'Kepada',
	ToYourWorkspace: 'ke ruang kerja anda',
	Today: 'Hari ini',
	TodayInHoursPlural: 'Hari ini dalam {count} {count, plural, one {jam} other {jam}}',
	TodayInMinsAbbreviated: 'Hari ini dalam {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Togol sel pengepala',
	ToggleHeaderCol: 'Togol lajur pengepala',
	ToggleHeaderRow: 'Togol baris pengepala',
	Tokyo: 'Tokyo',
	Tomorrow: 'Esok',
	TomorrowAfternoon: 'Esok petang',
	TomorrowMorning: 'Esok pagi',
	TooExpensive: 'Terlalu mahal',
	TooHardToSetUp: 'Terlalu sukar untuk disediakan',
	TooManyFiles: 'Lebih daripada 1 fail dikesan.',
	ToolsExample: 'Amalan mudah, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Jumlah',
	TotalAccountCredit: 'Jumlah kredit akaun',
	TotalAdjustments: 'Jumlah pelarasan',
	TotalAmountToCreditInCurrency: 'Jumlah keseluruhan untuk dikreditkan ({currency})',
	TotalBilled: 'Jumlah yang dibilkan',
	TotalConversations: '{total} {total, plural, =0 {perbualan} one {perbualan} other {perbualan}}',
	TotalOverdue: 'Jumlah Tertunggak',
	TotalOverdueTooltip:
		'Jumlah baki Tertunggak termasuk semua invois yang belum dibayar, tanpa mengira julat tarikh, yang tidak dibatalkan atau diproses.',
	TotalPaid: 'Jumlah Dibayar',
	TotalPaidTooltip:
		'Jumlah baki Dibayar termasuk semua amaun daripada invois yang telah dibayar dalam julat tarikh yang ditentukan.',
	TotalUnpaid: 'Jumlah Belum Dibayar',
	TotalUnpaidTooltip:
		'Jumlah baki Belum Dibayar termasuk semua jumlah tertunggak daripada pemprosesan, invois yang belum dibayar dan dihantar dalam julat tarikh yang ditentukan.',
	TotalWorkflows: '{count} {count, plural, one {aliran kerja} other {aliran kerja}}',
	TotpSetUpManualEntryInstruction:
		'Sebagai alternatif, anda boleh memasukkan kod di bawah secara manual ke dalam apl:',
	TotpSetUpModalDescription: 'Imbas kod QR dengan apl pengesah anda untuk menyediakan Pengesahan Berbilang Faktor.',
	TotpSetUpModalTitle: 'Sediakan peranti MFA',
	TotpSetUpSuccess: 'Anda sudah bersedia! MFA telah didayakan.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Masukkan kod yang dijana oleh apl pengesah anda',
	Transcribe: 'Transkripsikan',
	TranscribeLanguageSelector: 'Pilih bahasa input',
	TranscribeLiveAudio: 'Transkripsikan audio langsung',
	Transcribing: 'Mentranskripsi audio...',
	TranscribingIn: 'Menyalin dalam',
	Transcript: 'Transkrip',
	TranscriptRecordingCompleteInfo: 'Anda akan melihat transkrip anda di sini setelah rakaman selesai.',
	TranscriptSuccessSnackbar: 'Transkrip berjaya diproses.',
	Transcription: 'Transkripsi',
	TranscriptionEmpty: 'Tiada transkripsi tersedia',
	TranscriptionEmptyHelperMessage: 'Transkripsi ini tidak mengambil apa-apa. Mulakan semula dan cuba lagi.',
	TranscriptionFailedNotice: 'Transkripsi ini tidak berjaya diproses',
	TranscriptionIdleMessage:
		'Kami tidak mendengar apa-apa audio. Jika anda memerlukan lebih masa, sila respon dalam masa {timeValue} saat, atau sesi ini akan berakhir.',
	TranscriptionInProcess: 'Transkripsi sedang dijalankan...',
	TranscriptionIncompleteNotice: 'Beberapa bahagian transkripsi ini tidak berjaya diproses',
	TranscriptionOvertimeWarning: '{scribeType} sesi berakhir dalam <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Adakah anda pasti mahu memadamkan bahagian transkripsi ini?',
	TranscriptionText: 'Suara ke teks',
	TranscriptsPending: 'Transkrip anda akan tersedia di sini selepas sesi tamat.',
	Transfer: 'Pemindahan',
	TransferAndDelete: 'Pindahkan dan padam',
	TransferOwnership: 'Memindahkan pemilikan',
	TransferOwnershipConfirmationModalDescription:
		'Tindakan ini hanya boleh dibuat asal jika mereka memindahkan pemilikan kembali kepada anda.',
	TransferOwnershipDescription: 'Pindahkan pemilikan ruang kerja ini kepada ahli pasukan yang lain.',
	TransferOwnershipSuccessSnackbar: 'Pemindahan pemilikan berjaya!',
	TransferOwnershipToMember: 'Adakah anda pasti ingin memindahkan ruang kerja ini kepada {staff}?',
	TransferStatusAlert:
		'Mengelakkan {numberOfStatuses, plural, one {status ini} other {status-status ini}} akan memberi impak kepada {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} status klien.</strong>} other {<strong>{numberOfAffectedRecords} status klien.</strong>}}',
	TransferStatusDescription:
		'Pilih status lain untuk pelanggan ini sebelum meneruskan pemadaman. Tindakan ini tidak boleh dibuat asal.',
	TransferStatusLabel: 'Pindahkan ke status baharu',
	TransferStatusPlaceholder: 'Pilih status sedia ada',
	TransferStatusTitle: 'Pindahkan status sebelum pemadaman',
	TransferTaskAttendeeStatusAlert:
		'Mengeluarkan status ini akan memberi kesan kepada <strong>{number} temujanji akan datang {number, plural, one {status} other {status}}.</strong>',
	TransferTaskAttendeeStatusDescription:
		'Pilih status lain untuk pelanggan ini sebelum meneruskan dengan penghapusan. Tindakan ini tidak dapat dibatalkan.',
	TransferTaskAttendeeStatusSubtitle: 'Status Temujanji',
	TransferTaskAttendeeStatusTitle: 'Status Pemindahan sebelum Penghapusan',
	Trash: 'Sampah',
	TrashDeleteItemsModalConfirm: 'Untuk mengesahkan, taip {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Berikut {count, plural, one {item} other {items}} akan dipadamkan secara kekal dan tidak boleh dipulihkan.',
	TrashDeleteItemsModalTitle: 'Padamkan {count, plural, one {item} other {items}} selama-lamanya',
	TrashDeletedAllItems: 'Memadam semua item',
	TrashDeletedItems: 'Dihapus {count, plural, one {item} other {items}}',
	TrashDeletedItemsFailure: 'Gagal memadamkan item daripada sampah',
	TrashLocationAppointmentType: 'Kalendar',
	TrashLocationBillingAndPaymentsType: 'Bil & Bayaran',
	TrashLocationContactType: 'Pelanggan',
	TrashLocationNoteType: 'Nota ',
	TrashRestoreItemsModalDescription: 'Berikut {count, plural, one {item} other {items}} akan dipulihkan.',
	TrashRestoreItemsModalTitle: 'Memulihkan {count, plural, one {item} other {items}}',
	TrashRestoredAllItems: 'Dipulihkan semua item',
	TrashRestoredItems: 'Dipulihkan {count, plural, one {item} other {items}}',
	TrashRestoredItemsFailure: 'Gagal memulihkan item daripada sampah',
	TrashSuccessfullyDeletedItem: 'Berjaya dipadamkan {type}',
	Trigger: 'Pencetus',
	Troubleshoot: 'Selesaikan masalah',
	TryAgain: 'Cuba lagi',
	Tuesday: 'Selasa',
	TwoToTen: '2 - 10',
	Type: 'taip',
	TypeHere: 'Taip di sini...',
	TypeToConfirm: 'Untuk mengesahkan, taip {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Tajuk 1',
	TypographyHeading2: 'Tajuk 2',
	TypographyHeading3: 'Tajuk 3',
	TypographyHeading4: 'Tajuk 4',
	TypographyHeading5: 'Tajuk 5',
	TypographyP: 'P',
	TypographyParagraph: 'Perenggan',
	UnableToCompleteAction: 'Tidak dapat menyelesaikan tindakan.',
	UnableToPrintDocument: 'Tidak dapat mencetak dokumen. Sila cuba lagi kemudian.',
	Unallocated: 'Tidak diperuntukkan',
	UnallocatedPaymentDescription: `Bayaran ini belum diperuntukkan sepenuhnya kepada item yang boleh dibilkan.
 Tambahkan peruntukan pada item yang belum dibayar atau keluarkan kredit atau bayaran balik.`,
	UnallocatedPaymentTitle: 'Bayaran tidak diperuntukkan',
	UnallocatedPayments: 'Bayaran tidak diperuntukkan',
	Unarchive: 'Nyahrkib',
	Unassigned: 'Tidak ditugaskan',
	UnauthorisedInvoiceSnackbar: 'Anda tidak mempunyai akses untuk mengurus invois untuk pelanggan ini.',
	UnauthorisedSnackbar: 'Anda tidak mempunyai kebenaran untuk melakukan ini.',
	Unavailable: 'Tidak tersedia',
	Uncategorized: 'Tidak dikategorikan',
	Unclaimed: 'Tidak dituntut',
	UnclaimedAmount: 'Jumlah Tidak Dituntut',
	UnclaimedItems: 'Barang tidak dituntut',
	UnclaimedItemsMustBeInCurrency: 'Hanya item dalam mata wang berikut yang disokong: {currencies}',
	Uncle: 'pakcik',
	Unconfirmed: 'Tidak disahkan',
	Underline: 'Garis bawah',
	Undo: 'Buat asal',
	Unfavorite: 'Buang Kegemaran',
	Uninvoiced: 'Tidak diinvois',
	UninvoicedAmount: 'Jumlah yang belum difakturkan',
	UninvoicedAmounts:
		'{count, plural, =0 {Tiada amaun yang belum difaktorkan} one {Amaun yang belum difaktorkan} other {Amaun yang belum difaktorkan}}',
	Unit: 'Unit',
	UnitedKingdom: 'United Kingdom',
	UnitedStates: 'Amerika Syarikat',
	UnitedStatesEast: 'Amerika Syarikat - Timur',
	UnitedStatesWest: 'Amerika Syarikat - Barat',
	Units: 'Unit',
	UnitsIsRequired: 'Unit diperlukan',
	UnitsMustBeGreaterThanZero: 'Unit mestilah lebih besar daripada 0',
	UnitsPlaceholder: '1',
	Unknown: 'Tidak diketahui',
	Unlimited: 'Tidak Terhad',
	Unlock: 'Buka kunci',
	UnlockNoteHelper: 'Sebelum membuat sebarang perubahan baharu, editor dikehendaki membuka kunci nota.',
	UnmuteAudio: 'Nyahredam audio',
	UnmuteEveryone: 'Nyahredam semua orang',
	Unpaid: 'Tak bergaji',
	UnpaidInvoices: 'Invois yang belum dibayar',
	UnpaidItems: 'Barang tak berbayar',
	UnpaidMultiple: 'Tak bergaji',
	Unpublish: 'Tidak diterbitkan',
	UnpublishTemplateConfirmationModalPrompt:
		'Mengalih keluar <span>{title}</span> akan mengeluarkan sumber ini daripada komuniti Carepatron. Tindakan ini tidak boleh dibatalkan.',
	UnpublishToCommunitySuccessMessage: 'Berjaya alihkan ‛{title}’ daripada komuniti',
	Unread: 'Belum dibaca',
	Unrecognised: 'Tidak dikenali',
	UnrecognisedDescription:
		'Kaedah pembayaran ini tidak dikenali oleh versi aplikasi semasa anda. Sila muat semula penyemak imbas anda untuk mendapatkan versi terkini untuk melihat dan mengedit kaedah pembayaran ini.',
	UnsavedChanges: 'Perubahan yang tidak disimpan',
	UnsavedChangesPromptContent: 'Adakah anda ingin menyimpan perubahan anda sebelum ditutup?',
	UnsavedChangesPromptTitle: 'Anda mempunyai perubahan yang belum disimpan',
	UnsavedNoteChangesWarning: 'Perubahan yang anda buat mungkin tidak disimpan',
	UnsavedTemplateChangesWarning: 'Perubahan yang anda buat mungkin tidak disimpan',
	UnselectAll: 'Buang pilihan semua',
	Until: 'Sehingga',
	UntitledConversation: 'Perbualan Tanpa Tajuk',
	UntitledFolder: 'Folder tidak bertajuk',
	UntitledNote: 'Nota tanpa tajuk',
	UntitledSchedule: 'Jadual tanpa tajuk',
	UntitledSection: 'Bahagian tidak bertajuk',
	UntitledTemplate: 'Templat tanpa tajuk',
	Unverified: 'Tidak disahkan',
	Upcoming: 'Akan datang',
	UpcomingAppointments: 'Janji temu akan datang',
	UpcomingDateOverridesEmpty: 'Tiada penggantian tarikh ditemui',
	UpdateAvailabilityScheduleFailure: 'Gagal mengemas kini jadual ketersediaan',
	UpdateAvailabilityScheduleSuccess: 'Berjaya mengemas kini jadual ketersediaan',
	UpdateInvoicesOrClaimsAgainstBillable: 'Adakah anda mahu harga baharu digunakan pada invois dan tuntutan peserta?',
	UpdateLink: 'Kemaskini pautan',
	UpdatePrimaryEmailWarningDescription:
		'Mengubah alamat e-mel pelanggan anda akan menyebabkan mereka kehilangan akses kepada temu janji dan nota sedia ada mereka.',
	UpdatePrimaryEmailWarningTitle: 'Perubahan Emel Pelanggan',
	UpdateSettings: 'Kemas kini tetapan',
	UpdateStatus: 'Kemas kini status',
	UpdateSuperbillReceiptFailure: 'Gagal mengemas kini resit Superbill',
	UpdateSuperbillReceiptSuccess: 'Berjaya mengemas kini resit Superbill',
	UpdateTaskBillingDetails: 'Kemas kini butiran pengebilan',
	UpdateTaskBillingDetailsDescription:
		'Harga pelantikan telah berubah. Adakah anda mahu harga baharu digunakan pada item pengebilan, invois dan tuntutan peserta? Pilih kemas kini yang anda mahu teruskan.',
	UpdateTemplateFolderSuccessMessage: 'Berjaya mengemas kini folder',
	UpdateUnpaidInvoices: 'Kemas kini invois yang belum dibayar',
	UpdateUserInfoSuccessSnackbar: 'Berjaya mengemas kini maklumat pengguna!',
	UpdateUserSettingsSuccessSnackbar: 'Berjaya mengemas kini tetapan pengguna!',
	Upgrade: 'Naik taraf',
	UpgradeForSMSReminder: 'Naik taraf kepada <b>Profesional</b> untuk peringatan SMS tanpa had',
	UpgradeNow: 'Tingkatkan Sekarang',
	UpgradePlan: 'Naik taraf pelan',
	UpgradeSubscriptionAlertDescription:
		'Anda kekurangan storan. Tingkatkan pelan anda untuk membuka kunci storan tambahan dan pastikan amalan anda berjalan lancar!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Anda kekurangan storan. Minta seseorang dalam praktik anda yang mempunyai <span>Akses pentadbir</span> untuk menaik taraf pelan anda untuk membuka kunci storan tambahan dan teruskan praktik anda berjalan lancar!',
	UpgradeSubscriptionAlertTitle: 'Sudah tiba masanya untuk meningkatkan langganan anda',
	UpgradeYourPlan: 'Tingkatkan rancangan anda',
	UploadAudio: 'Muat naik Audio',
	UploadFile: 'Muat naik fail',
	UploadFileDescription: 'Platform perisian manakah yang anda ingin beralih daripada?',
	UploadFileMaxSizeError: 'Fail terlalu besar. Saiz fail maksimum ialah {fileSizeLimit}.',
	UploadFileSizeLimit: 'Had lapan {size}MB',
	UploadFileTileDescription: 'Gunakan fail CSV, XLS, XLSX, atau ZIP untuk muat naik pelanggan anda.',
	UploadFileTileLabel: 'Muat naik fail',
	UploadFiles: 'Muat naik fail',
	UploadIndividually: 'Muat naik fail secara individu',
	UploadLogo: 'Muat naik logo',
	UploadPhoto: 'Muat naik foto',
	UploadToCarepatron: 'Muat naik ke Carepatron',
	UploadYourLogo: 'Muat naik logo anda',
	UploadYourTemplates: 'Muat naik templat anda dan kami akan menukarkannya untuk anda',
	Uploading: 'Memuat naik',
	UploadingAudio: 'Memuat naik audio anda...',
	UploadingFiles: 'Memuat naik fail',
	UrlLink: 'Pautan URL',
	UsageCount: 'Digunakan {count} kali',
	UsageLimitValue: '{used} daripada {limit} digunakan',
	UsageValue: '{digunakan} digunakan',
	Use: 'guna',
	UseAiToAutomateYourWorkflow: 'Gunakan AI untuk mengautomasikan aliran kerja anda!',
	UseAsDefault: 'Gunakan sebagai lalai',
	UseCustom: 'Gunakan adat',
	UseDefault: 'Gunakan lalai',
	UseDefaultFilters: 'Gunakan penapis lalai',
	UseTemplate: 'Gunakan templat',
	UseThisCard: 'Gunakan kad ini',
	UseValue: 'Gunakan "{value}"',
	UseWorkspaceDefault: 'Gunakan lalai ruang kerja',
	UserIsTyping: '{name} sedang menaip...',
	Username: 'Nama pengguna',
	Users: 'Pengguna',
	VAT: 'VAT',
	ValidUrl: 'Pautan URL mestilah URL yang sah.',
	Validate: 'Sahkan',
	Validated: 'Disahkan',
	Validating: 'Mengesahkan',
	ValidatingContent: 'Mengesahkan kandungan...',
	ValidatingTranscripts: 'Mengesahkan transkrip...',
	ValidationConfirmPasswordRequired: 'Sahkan Kata Laluan diperlukan',
	ValidationDateMax: 'Mestilah sebelum {max}',
	ValidationDateMin: 'Mestilah selepas {min}',
	ValidationDateRange: 'Tarikh mula dan tamat diperlukan',
	ValidationEndDateMustBeAfterStartDate: 'Tarikh tamat mestilah selepas tarikh mula',
	ValidationMixedDefault: 'Ini tidak sah',
	ValidationMixedRequired: 'Ini diperlukan',
	ValidationNumberInteger: 'Mesti nombor bulat',
	ValidationNumberMax: 'Mestilah {max} atau kurang',
	ValidationNumberMin: 'Mestilah {min} atau lebih',
	ValidationPasswordNotMatching: 'Kata laluan tidak sepadan',
	ValidationPrimaryAddressIsRequired: 'Alamat diperlukan apabila ditetapkan sebagai lalai',
	ValidationPrimaryPhoneNumberIsRequired: 'Nombor telefon diperlukan apabila ditetapkan sebagai lalai',
	ValidationServiceMustBeNotBeFuture: 'Perkhidmatan tidak boleh menjadi hari ini atau pada masa akan datang',
	ValidationStringEmail: 'Mestilah e-mel yang sah',
	ValidationStringMax: 'Mestilah {max} aksara atau kurang',
	ValidationStringMin: 'Mestilah {min} aksara atau lebih',
	ValidationStringPhoneNumber: 'Mesti nombor telefon yang sah',
	ValueMinutes: '{value} minit',
	VerbosityConcise: 'Ringkas',
	VerbosityDetailed: 'Terperinci',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Sangat terperinci',
	VerificationCode: 'Kod pengesahan',
	VerificationEmailDescription:
		'Sila masukkan alamat e-mel anda dan kod pengesahan yang baru kami hantar kepada anda.',
	VerificationEmailSubtitle: 'Semak folder Spam - jika e-mel belum sampai',
	VerificationEmailTitle: 'Sahkan e-mel',
	VerificationOption: 'Pengesahan emel',
	Verified: 'Disahkan',
	Verify: 'Sahkan',
	VerifyAndSubmit: 'Sahih & hantar',
	VerifyEmail: 'Sahkan emel',
	VerifyEmailAccessCode: 'Kod pengesahan',
	VerifyEmailAddress: 'Sahkan alamat e-mel',
	VerifyEmailButton: 'Sahkan dan log keluar',
	VerifyEmailSentSnackbar: 'E-mel pengesahan dihantar. Semak peti masuk anda.',
	VerifyEmailSubTitle: 'Semak folder Spam jika e-mel belum sampai',
	VerifyEmailSuccessLogOutSnackbar: 'Berjaya! Sila log keluar untuk menggunakan perubahan.',
	VerifyEmailSuccessSnackbar: 'Berjaya! E-mel disahkan. Sila log masuk untuk meneruskan sebagai akaun yang disahkan.',
	VerifyEmailTitle: 'Sahkan e-mel anda',
	VerifyNow: 'Sahkan sekarang',
	Veterinarian: 'Doktor haiwan',
	VideoCall: 'Panggilan video',
	VideoCallAudioInputFailed: 'Peranti input audio tidak berfungsi',
	VideoCallAudioInputFailedMessage: 'Buka tetapan dan semak jika anda telah menetapkan sumber mikrofon dengan betul',
	VideoCallChatBanner:
		'Mesej boleh dilihat oleh semua orang dalam panggilan ini dan akan dipadamkan apabila panggilan ditamatkan.',
	VideoCallChatSendBtn: 'Hantar mesej',
	VideoCallChatTitle: 'Sembang',
	VideoCallDisconnectedMessage: 'Anda kehilangan sambungan rangkaian anda. Cuba menyambung semula',
	VideoCallOptionInfo:
		'Carepatron akan menguruskan panggilan video untuk janji temu anda jika Zoom belum disambungkan',
	VideoCallTilePaused: 'Video ini dijeda kerana masalah dengan rangkaian anda',
	VideoCallTranscriptionFormDescription: 'Anda boleh melaraskan tetapan ini pada bila-bila masa',
	VideoCallTranscriptionFormHeading: 'Sesuaikan Jurutulis AI anda',
	VideoCallTranscriptionFormLanguageField: 'Bahasa keluaran yang dihasilkan',
	VideoCallTranscriptionFormNoteTemplateField: 'Tetapkan templat nota lalai',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Tiada templat dengan AI ditemui',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Pilih templat',
	VideoCallTranscriptionPronounField: 'Kata ganti nama awak',
	VideoCallTranscriptionRecordingNote:
		'Pada akhir sesi, anda akan menerima <strong>{noteTemplate} nota</strong> dan transkrip yang dihasilkan.',
	VideoCallTranscriptionReferClientField: 'Rujuk kepada Pelanggan sebagai',
	VideoCallTranscriptionReferPractitionerField: 'Rujuk Pengamal sebagai',
	VideoCallTranscriptionTitle: 'Jurutulis AI',
	VideoCallTranscriptionVerbosityField: 'Keterlaluan',
	VideoCallTranscriptionWritingPerspectiveField: 'Perspektif penulisan',
	VideoCalls: 'Panggilan video',
	VideoConferencing: 'Persidangan video',
	VideoOff: 'Video dimatikan',
	VideoOn: 'Video dimatikan',
	VideoQual360: 'Kualiti rendah (360p)',
	VideoQual540: 'Kualiti sederhana (540p)',
	VideoQual720: 'Kualiti tinggi (720p)',
	View: 'Lihat',
	ViewAll: 'Lihat semua',
	ViewAppointment: 'Lihat temu janji',
	ViewBy: 'Lihat oleh',
	ViewClaim: 'Lihat tuntutan',
	ViewCollection: 'Lihat koleksi',
	ViewDetails: 'Lihat butiran',
	ViewEnrollment: 'Lihat Pendaftaran',
	ViewPayment: 'Lihat Pembayaran',
	ViewRecord: 'Lihat rekod',
	ViewRemittanceAdvice: 'Lihat nasihat pengiriman wang',
	ViewRemittanceAdviceHeader: 'Tuntutan nasihat penghantaran wang',
	ViewRemittanceAdviceSubheader: 'Tuntutan {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Lihat tetapan',
	ViewStripeDashboard: 'Lihat papan pemuka Stripe',
	ViewTemplate: 'Lihat templat',
	ViewTemplates: 'Lihat templat',
	ViewableBy: 'Boleh dilihat oleh',
	ViewableByHelper:
		'Anda dan Pasukan sentiasa mempunyai akses kepada nota yang anda terbitkan. Anda boleh memilih untuk berkongsi nota ini dengan pelanggan dan/atau perhubungan mereka',
	Viewer: 'Penonton',
	VirtualLocation: 'Lokasi maya',
	VisibleTo: 'Kelihatan Kepada',
	VisitOurHelpCentre: 'Lawati pusat bantuan kami',
	VisualEffects: 'Kesan visual',
	VoiceFocus: 'Fokus suara',
	VoiceFocusLabel: 'Menapis keluar bunyi daripada mikrofon anda yang bukan pertuturan',
	Void: 'batal',
	VoidCancelPriorClaim: 'Batalkan/Batal tuntutan terdahulu',
	WaitingforMins: 'Menunggu selama {count} minit',
	Warning: 'Amaran',
	WatchAVideo: 'tonton video',
	WatchDemoVideo: 'Tonton video demo',
	WebConference: 'Persidangan web',
	WebConferenceOrVirtualLocation: 'Persidangan web / lokasi maya',
	WebDeveloper: 'Pembangun Web',
	WebsiteOptional: 'Tapak web <span>(Pilihan)</span>',
	WebsiteUrl: 'URL tapak web',
	Wednesday: 'Rabu',
	Week: 'Minggu',
	WeekPlural: '{count, plural, one {minggu} other {minggu}}',
	Weekly: 'Mingguan',
	WeeksPlural: '{age, plural, one {# minggu} other {# minggu}}',
	WelcomeBack: 'Selamat kembali',
	WelcomeBackName: 'Selamat datang kembali, {name}',
	WelcomeName: 'Selamat datang {name}',
	WelcomeToCarepatron: 'Selamat datang ke Carepatron',
	WhatCanIHelpWith: 'Apa yang boleh saya bantu?',
	WhatDidYouLikeResponse: 'Apakah yang anda suka tentang respons ini?',
	WhatIsCarepatron: 'Apa itu Carepatron?',
	WhatMadeYouCancel: `Apa yang menyebabkan anda membatalkan rancangan anda?
 Semak semua yang berkenaan.`,
	WhatServicesDoYouOffer: 'apa<mark> perkhidmatan</mark> adakah anda menawarkan',
	WhatServicesDoYouOfferDescription: 'Anda boleh mengedit atau menambah lebih banyak perkhidmatan kemudian.',
	WhatsYourAvailability: 'Apakah <mark>kebolehcapaian</mark> anda?',
	WhatsYourAvailabilityDescription: 'Anda boleh menambah lebih banyak jadual kemudian.',
	WhatsYourBusinessName: 'apa awak<mark> nama perniagaan?</mark>',
	WhatsYourTeamSize: 'apa awak<mark> saiz pasukan?</mark>',
	WhatsYourTeamSizeDescription: 'Ini akan membantu kami menyediakan ruang kerja anda dengan betul.',
	WhenThisHappens: 'Apabila ini berlaku:',
	WhichBestDescribesYou: 'Mana yang terbaik<mark> menggambarkan anda?</mark>',
	WhichPlatforms: 'Platform mana?',
	Wife: 'Isteri',
	WorkflowDescription: 'Penerangan aliran kerja',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Templat boleh dihubungkan ke aliran kerja untuk proses yang lebih lancar. Lihat aliran kerja yang dihubungkan untuk menjejaki dan mengemas kini dengan mudah.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'Sambungkan SMS + e-mel anda berdasarkan pencetus biasa',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Otomasi aliran kerja',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Aliran kerja automatik',
	WorkflowTemplateConfigKey_Body: 'Badan',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Tunjukkan jenama',
	WorkflowTemplateConfigKey_Content: 'Kandungan',
	WorkflowTemplateConfigKey_Footer: 'Kaki Laman',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Tunjukkan kaki laman',
	WorkflowTemplateConfigKey_Header: 'Header',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Tunjukkan tajuk',
	WorkflowTemplateConfigKey_SecurityFooter: 'Kaki Laman Keselamatan',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Tunjukkan kaki halaman keselamatan',
	WorkflowTemplateConfigKey_Subject: 'Subjek',
	WorkflowTemplateConfigKey_Title: 'Judul',
	WorkflowTemplateDeleteConfirmationMessage:
		'Anda pasti ingin memadamkan templat ini? Tindakan ini tidak boleh dibatalkan.',
	WorkflowTemplateDeleteConfirmationTitle: 'Padam templat notifikasi',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Anda pasti? Ini akan menghapus versi {locale} sahaja—bahasa lain tidak akan terjejas. Tindakan ini tidak boleh dibatalkan.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Padam templat ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Templat notifikasi telah dipadamkan dengan jayanya',
	WorkflowTemplateEditorDetailsTab: 'Maklumat Templat',
	WorkflowTemplateEditorEmailContent: 'Kandungan Emel',
	WorkflowTemplateEditorEmailContentTab: 'Kandungan Emel',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert:
		'Pratonton menggunakan data sampel untuk menunjukkan apa yang akan dilihat oleh pelanggan anda.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Anda pasti? Ini akan menetapkan semula versi kepada templat lalai sistem. Tindakan ini tidak boleh dibatalkan.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Tetapkan semula templat',
	WorkflowTemplateSendTestEmail: 'Hantar emel ujian',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Cuba atur e-mel anda dengan menghantar e-mel ujian kepada diri sendiri.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Emel Penerima',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Hantar ujian',
	WorkflowTemplateSendTestEmailDialogTitle: 'Hantar e-mel ujian',
	WorkflowTemplateSendTestEmailSuccess: 'Berjaya! Emel ujian <mark>{templateName}</mark> anda telah dihantar.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Urus templat anda dan tambah pelbagai versi bahasa untuk berkomunikasi dengan berkesan dengan pelanggan.',
	WorkflowTemplateTemplateEditor: 'Penyunting Templat',
	WorkflowTemplateTranslateLocaleError: 'Terdapat ralat semasa menterjemahkan kandungan',
	WorkflowTemplateTranslateLocaleSuccess: 'Berjaya menterjemahkan kandungan kepada **{locale}**',
	WorkflowsAndReminders: 'Aliran kerja ',
	WorkflowsManagement: 'Pengurusan Aliran Kerja',
	WorksheetAndHandout: 'Lembaran Kerja/Nota Edaran',
	WorksheetsAndHandoutsDescription: 'Untuk penglibatan dan pendidikan pelanggan',
	Workspace: 'Ruang kerja',
	WorkspaceBranding: 'Penjenamaan ruang kerja',
	WorkspaceBrandingDescription: `Jenamakan ruang kerja anda dengan mudah dengan gaya padu yang mencerminkan anda
 profesionalisme dan personaliti. Sesuaikan invois kepada tempahan dalam talian untuk cantik
 pengalaman pelanggan.`,
	WorkspaceName: 'Nama ruang kerja',
	Workspaces: 'Ruang kerja',
	WriteOff: 'Hapus kira',
	WriteOffModalDescription:
		'Anda mempunyai <mark>{count} {count, plural, one {item baris} other {item baris}}</mark> yang perlu ditulis-hapus',
	WriteOffModalTitle: 'Pelarasan hapus kira',
	WriteOffReasonHelperText: 'Ini ialah nota dalaman dan tidak akan kelihatan kepada pelanggan anda.',
	WriteOffReasonPlaceholder:
		'Menambah sebab hapus kira boleh membantu semasa menyemak urus niaga yang boleh dibilkan',
	WriteOffTotal: 'Jumlah potongan penuh ({currencyCode})',
	Writer: 'Penulis',
	Yearly: 'Tahunan',
	YearsPlural: '{age, plural, one {# tahun} other {# tahun}}',
	Yes: 'ya',
	YesArchive: 'Ya, arkib',
	YesDelete: 'Ya, padamkan',
	YesDeleteOverride: 'Ya, padamkan penggantian',
	YesDeleteSection: 'Ya, padamkan',
	YesDisconnect: 'Ya, putuskan sambungan',
	YesEnd: 'Ya, tamat',
	YesEndTranscription: 'Ya, tamatkan transkripsi',
	YesImFineWithThat: 'Ya, saya baik dengan itu',
	YesLeave: 'Ya, pergi',
	YesMinimize: 'Ya, kecilkan',
	YesOrNoAnswerTypeDescription: 'Konfigurasikan jenis jawapan',
	YesOrNoFormPrimaryText: 'Ya | Tidak',
	YesOrNoFormSecondaryText: 'Pilih pilihan ya atau tidak',
	YesProceed: 'Ya, teruskan',
	YesRemove: 'Ya, keluarkan',
	YesRestore: 'Ya, pulihkan',
	YesStopIgnoring: 'Ya, berhenti mengabaikan',
	YesTransfer: 'Ya, pindahkan',
	Yesterday: 'Semalam',
	YogaInstructor: 'Pengajar Yoga',
	You: 'awak',
	YouArePresenting: 'Anda sedang membentangkan',
	YouCanChooseMultiple: 'Anda boleh memilih berbilang',
	YouCanSelectMultiple: 'Anda boleh memilih berbilang',
	YouHaveOngoingTranscription: 'Anda mempunyai transkripsi yang sedang berjalan',
	YourAnswer: 'Jawapan awak',
	YourDisplayName: 'Nama paparan anda',
	YourSpreadsheetColumns: 'Lajur hamparan anda',
	YourTeam: 'Pasukan anda',
	ZipCode: 'Poskod',
	Zoom: 'Zum',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Anda tidak boleh menambah panggilan Zoom untuk ahli pasukan ini. Sila rujuk kepada <a>dokumen sokongan untuk mendapatkan maklumat lanjut.</a>',
};

export default items;
