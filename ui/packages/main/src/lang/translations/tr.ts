import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'AI istemleri',
	ATeamMemberIsRequired: 'Bir ekip üyesine ihtiyaç var',
	AboutClient: '<PERSON>üş<PERSON>i Hakkında',
	AcceptAppointment: 'Randevunuz<PERSON> onayladığınız için teşekkür ederiz',
	AcceptTermsAndConditionsRequired: 'Şartları kabul et ',
	Accepted: 'Kabul Edildi',
	AccessGiven: '<PERSON><PERSON><PERSON><PERSON> verildi',
	AccessPermissions: '<PERSON>rişim izinleri',
	AccessType: '<PERSON><PERSON><PERSON><PERSON> türü',
	Accident: 'Ka<PERSON>',
	Account: 'Hesa<PERSON>',
	AccountCredit: 'He<PERSON><PERSON>',
	Accountant: 'Muhasebeci',
	Action: 'Aksiyon',
	Actions: 'Hareketler',
	Active: 'Aktif',
	ActiveTags: 'Aktif etiketler',
	ActiveUsers: '<PERSON><PERSON><PERSON>',
	Activity: '<PERSON><PERSON><PERSON><PERSON>',
	Actor: '<PERSON><PERSON><PERSON><PERSON>',
	Acupuncture: 'Akupunktur',
	Acupuncturist: 'A<PERSON>punktur<PERSON>',
	Acupuncturists: 'Akupunktur uzmanları',
	AcuteManifestationOfAChronicCondition: 'Kronik bir durumun akut tezahürü',
	Add: 'Eklemek',
	AddADescription: 'Bir açıklama ekle',
	AddALocation: 'Konum ekle',
	AddASecondTimezone: 'İkinci bir saat dilimi ekleyin',
	AddAddress: 'Adres ekle',
	AddAnother: '  Başka birtane ekle',
	AddAnotherAccount: 'Başka bir hesap ekle',
	AddAnotherContact: 'Başka bir iletişim bilgisi ekle',
	AddAnotherOption: 'Başka bir seçenek ekle',
	AddAnotherTeamMember: 'Başka bir ekip üyesi ekle',
	AddAvailablePayers: '+ Mevcut ödeme seçeneklerini ekle',
	AddAvailablePayersDescription:
		'Çalışma alanınızın ödemeci listesine eklemek için ödemecileri arayın. Eklendikten sonra, kayıtları yönetebilir veya gerektiğinde ödemeci ayrıntılarını ayarlayabilirsiniz.',
	AddCaption: 'Altyazı ekle',
	AddClaim: 'Talep ekle',
	AddClientFilesModalDescription:
		'Erişimi kısıtlamak için &quot;Görüntüleyenler&quot; onay kutularındaki seçenekleri seçin',
	AddClientFilesModalTitle: '{name} için dosya yükle',
	AddClientNoteButton: 'Not ekle',
	AddClientNoteModalDescription:
		'Notunuza içerik ekleyin. Bu özel notu görebilecek bir veya daha fazla grubu seçmek için &quot;Görüntüleyenler&quot; bölümünü kullanın.',
	AddClientNoteModalTitle: 'Not ekle',
	AddClientOwnerRelationshipModalDescription:
		'Müşteriyi davet etmek, kendi profil bilgilerini yönetmelerine ve profil bilgilerine kullanıcı erişimini yönetmelerine olanak tanır.',
	AddClientOwnerRelationshipModalTitle: 'Müşteriyi davet et',
	AddCode: 'Kod ekle',
	AddColAfter: 'Sonrasına sütun ekle',
	AddColBefore: 'Öncesine sütun ekle',
	AddCollection: 'Koleksiyon Ekle',
	AddColor: 'Renk ekle',
	AddColumn: 'Sütun ekle',
	AddContactRelationship: 'Kişi ilişkisi ekle',
	AddContacts: 'Kişileri ekleyin',
	AddCustomField: 'Özel alan ekle',
	AddDate: 'Tarih ekle',
	AddDescription: 'Açıklama ekle',
	AddDetail: 'Ayrıntı ekle',
	AddDisplayName: 'Görünen ad ekle',
	AddDxCode: 'Teşhis kodunu ekleyin',
	AddEmail: 'E-posta ekle',
	AddFamilyClientRelationshipModalDescription:
		'Bir aile üyesinin davet edilmesi, onların bakım hikayelerini ve müşterinin profil bilgilerini görmelerine olanak tanır. Yönetici olarak davet edilirlerse müşterinin profil bilgilerini güncelleme ve kullanıcı erişimini yönetme erişimine sahip olacaklar.',
	AddFamilyClientRelationshipModalTitle: 'Aile üyesini davet et',
	AddField: 'Alan ekle',
	AddFormField: 'Form alanı ekle',
	AddImages: 'Görsel ekle',
	AddInsurance: 'Sigorta ekle',
	AddInvoice: 'Fatura ekle',
	AddLabel: 'Etiket ekle',
	AddLanguage: 'Dil ekle',
	AddLocation: 'Konum ekle',
	AddManually: 'El ile ekle',
	AddMessage: 'Mesaj ekle',
	AddNewAction: 'Yeni eylem ekle',
	AddNewSection: 'Yeni bölüm ekle',
	AddNote: 'Not ekle',
	AddOnlineBookingDetails: 'Çevrimiçi rezervasyon ayrıntılarını ekleyin',
	AddPOS: 'POS ekle',
	AddPaidInvoices: 'Ödenmiş faturaları ekleyin',
	AddPayer: 'Ödeyen ekle',
	AddPayment: 'Ödeme ekle',
	AddPaymentAdjustment: 'Ödeme ayarlaması ekle',
	AddPaymentAdjustmentDisabledDescription: 'Ödeme dağılımlarında değişiklik yapılmayacak.',
	AddPaymentAdjustmentEnabledDescription: 'Tahsis edilebilecek tutar azaltılacak.',
	AddPhoneNumber: 'Telefon numarası ekle',
	AddPhysicalOrVirtualLocations: 'Fiziksel veya sanal konumlar ekleyin',
	AddQuestion: 'Soru ekle',
	AddQuestionOrTitle: 'Soru veya başlık ekleyin',
	AddRelationship: 'İlişki ekle',
	AddRelationshipModalTitle: 'Mevcut kişiyi bağlayın',
	AddRelationshipModalTitleNewClient: 'Yeni kişiyi bağlayın',
	AddRow: 'Satır ekle',
	AddRowAbove: 'Yukarıya satır ekle',
	AddRowBelow: 'Aşağıya satır ekle',
	AddService: 'Hizmet ekle',
	AddServiceLocation: 'Hizmet konumu ekle',
	AddServiceToCollections: 'Hizmeti koleksiyonlara ekle',
	AddServiceToOneOrMoreCollections: 'Hizmeti bir veya daha fazla koleksiyona ekle',
	AddServices: 'Hizmet ekle',
	AddSignature: 'İmza ekle',
	AddSignaturePlaceholder: 'İmzanıza eklenecek ek ayrıntıları yazın',
	AddSmartDataChips: 'Akıllı veri çipleri ekleyin',
	AddStaffClientRelationshipsModalDescription:
		'Personelin seçilmesi, bu müşteri için bakım hikayeleri oluşturmalarına ve görüntülemelerine olanak tanıyacaktır. Ayrıca müşteri bilgilerini de görüntüleyebilecekler.',
	AddStaffClientRelationshipsModalTitle: 'Personel ilişkileri ekleyin',
	AddTag: 'Etiket ekle',
	AddTags: 'Etiket ekle',
	AddTemplate: 'Şablon ekle',
	AddTimezone: 'Saat dilimi ekle',
	AddToClaim: 'Talebe ekle',
	AddToCollection: 'Koleksiyona ekle',
	AddToExisting: 'Mevcut olana ekle',
	AddToStarred: 'Yıldızlılara ekle',
	AddUnclaimedItems: 'Talep edilmeyen öğeleri ekle',
	AddUnrelatedContactWarning:
		'{contact} kişisinin bir iletişim listesi olmadığı biri eklediniz. Devam etmeden önce içeriğin ilgili olduğundan emin olun.',
	AddValue: '"{value}" ekle',
	AddVideoCall: 'Video görüşmesi ekle',
	AddVideoOrVoiceCall: 'Görüntülü veya sesli arama ekleyin',
	AddictionCounselor: 'Bağımlılık Danışmanı',
	AddingManualPayerDisclaimer:
		'Sağlayıcı listenize bir ödeme yapan kişiyi manuel olarak eklemek, bu ödeme yapan kişiyle elektronik talep dosyalama bağlantısı kurmaz ancak manuel olarak talep oluşturmak için kullanılabilir.',
	AddingTeamMembersIncreaseCostAlert: 'Ekibinize yeni üyeler eklemek aylık aboneliğinizi artıracaktır.',
	Additional: 'Ek olarak',
	AdditionalBillingProfiles: 'Ek faturalama profilleri',
	AdditionalBillingProfilesSectionDescription:
		'Belirli ekip üyeleri, ödeyenler veya fatura şablonları için kullanılan varsayılan fatura bilgilerini geçersiz kılın.',
	AdditionalFeedback: 'Ek geri bildirim',
	AddnNewWorkspace: 'Yeni çalışma alanı',
	AddnNewWorkspaceSuccessSnackbar: 'Çalışma alanı oluşturuldu!',
	Address: 'Adres',
	AddressNumberStreet: 'Adres (No, cadde)',
	Adjustment: 'Ayarlama',
	AdjustmentType: 'Ayarlama türü',
	Admin: 'Yönetici',
	Admins: 'Yöneticiler',
	AdminsOnly: 'Yalnızca yöneticiler',
	AdvancedPlanInclusionFive: 'Muhasebe Müdürü',
	AdvancedPlanInclusionFour: 'Google analitik',
	AdvancedPlanInclusionHeader: `Plus'taki her şey  `,
	AdvancedPlanInclusionOne: 'Roller ',
	AdvancedPlanInclusionSix: 'Veri içe aktarma desteği',
	AdvancedPlanInclusionThree: 'Beyaz etiketleme',
	AdvancedPlanInclusionTwo: '90 gün silinmiş veri saklama',
	AdvancedPlanMessage:
		'Uygulamanızın ihtiyaçlarının kontrolünü elinizde tutun. Mevcut planınızı inceleyin ve kullanımı izleyin.',
	AdvancedSettings: 'Gelişmiş Ayarlar',
	AdvancedSubscriptionPlanSubtitle: 'Tüm özelliklerle uygulamanızı genişletin',
	AdvancedSubscriptionPlanTitle: 'Gelişmiş',
	AdvertisingManager: 'Reklam Müdürü',
	AerospaceEngineer: 'Havacılık mühendisi',
	AgeYearsOld: '{age} yaşında',
	Agenda: 'Gündem',
	AgendaView: 'Gündem görünümü',
	AiAskSupportedFileTypes: 'Desteklenen dosya türleri: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Parmaklarınızın ucunda bir asistan',
	AiCopilotDisclaimer: 'AI Copilot hata yapabilir. Önemli bilgileri kontrol edin.',
	AiCreateNewConversation: 'Yeni konuşma oluştur',
	AiEnhanceYourProductivity: 'Üretkenliğinizi artırın',
	AiPoweredTemplates: 'Yapay zeka destekli şablonlar',
	AiScribeNoDeviceFoundErrorMessage:
		'Tarayıcınızın bu özelliği desteklemediği veya uyumlu cihaz bulunmadığı anlaşılıyor.',
	AiScribeUploadFormat: 'Desteklenen dosya türleri: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'aynı anda sadece 1 dosya',
	AiShowConversationHistory: 'Konuşma geçmişini göster',
	AiSmartPromptNodePlaceholderText:
		'Doğru ve kişiselleştirilmiş AI sonuçları oluşturmanıza yardımcı olmak için özel isteminizi buraya yazın.',
	AiSmartPromptPrimaryText: 'Yapay zeka akıllı istem',
	AiSmartPromptSecondaryText: 'Özel AI akıllı istemini ekle',
	AiSmartReminders: 'Yapay zeka akıllı hatırlatıcılar',
	AiTemplateBannerTitle: 'AI destekli şablonlarla işinizi kolaylaştırın',
	AiTemplates: 'AI şablonları',
	AiTokens: 'Yapay Zeka Tokenları',
	AiWorkBetterWithAi: 'Yapay Zeka ile Daha İyi Çalışın',
	All: 'Tüm',
	AllAppointments: 'Tüm randevular',
	AllCategories: 'Tüm Kategoriler',
	AllClients: 'Tüm müşteriler',
	AllContactPolicySelectorLabel: `<mark>{client}</mark>'in tüm kişileri`,
	AllContacts: 'Tüm kişiler',
	AllContactsOf: '‘{name}’in tüm kişileri',
	AllDay: 'Tüm gün',
	AllInboxes: 'Tüm Gelen Kutuları',
	AllIndustries: 'Tüm endüstriler',
	AllLocations: 'Tüm konumlar',
	AllMeetings: 'Tüm toplantılar',
	AllNotificationsRestoredMessage: 'Tüm bildirimler geri yüklendi',
	AllProfessions: 'Tüm meslekler',
	AllReminders: 'Tüm hatırlatıcılar',
	AllServices: 'Tüm hizmetler',
	AllStatuses: 'Tüm durumlar',
	AllTags: 'Tüm etiketler',
	AllTasks: 'Tüm görevler',
	AllTeamMembers: 'Tüm ekip üyeleri',
	AllTypes: 'Her türlü',
	Allocated: 'Tahsis edilmiş',
	AllocatedItems: 'Tahsis edilen öğeler',
	AllocationTableEmptyState: 'Hiçbir ödeme tahsisi bulunamadı',
	AllocationTotalWarningMessage: `Tahsis edilen tutar toplam ödeme tutarını aşıyor.
 Lütfen aşağıdaki kalemleri inceleyin.`,
	AllowClientsToCancelAnytime: 'Müşterilerin diledikleri zaman iptal etmelerine izin ver',
	AllowNewClient: 'Yeni müşterilere izin ver',
	AllowNewClientHelper: 'Yeni müşteriler bu hizmet için rezervasyon yaptırabilir',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Randevudan en az {hours} saat önce izin verin.',
	AllowToUseSavedCard: 'Gelecekte kaydedilen kartı kullanmak için {provider}a izin ver',
	AllowVideoCalls: 'Video görüşmelerine izin ver',
	AlreadyAdded: 'Zaten eklendi',
	AlreadyHasAccess: 'Erişimi var',
	AlreadyHasAccount: 'Zaten hesabınız var mı?',
	Always: 'Daima',
	AlwaysIgnore: 'Her zaman yoksay',
	Amount: 'Miktar',
	AmountDue: 'Ödenecek tutar',
	AmountOfReferralRequests: '{amount, plural, one {# yönlendirme isteği} other {# yönlendirme istekleri}}',
	AmountPaid: 'Ödenen miktar',
	AnalyzingAudio: 'Ses analiz ediliyor...',
	AnalyzingInputContent: 'Giriş içeriği analiz ediliyor...',
	AnalyzingRequest: 'İstek analiz ediliyor...',
	AnalyzingTemplateContent: 'Şablon içeriği analiz ediliyor...',
	And: 'Ve',
	Annually: 'Yıllık Olarak',
	Anonymous: 'Anonim',
	AnswerExceeded: 'Cevabınız 300 karakterden az olmalıdır.',
	AnyStatus: 'Herhangi bir durum',
	AppNotifications: 'Bildirimler',
	AppNotificationsClearanceHeading: 'Güzel çalışma! Tüm aktiviteleri temizledin',
	AppNotificationsEmptyHeading: 'Çalışma alanı etkinliğiniz kısa süre içinde burada görünecek',
	AppNotificationsEmptySubtext: 'Şimdilik yapılacak bir işlem yok',
	AppNotificationsIgnoredCount: '{total} yoksayıldı',
	AppNotificationsUnread: '{total} okunmamış',
	Append: 'Ekle',
	Apply: 'Uygula',
	ApplyAccountCredit: 'Hesap kredisini uygula',
	ApplyDiscount: 'İndirim uygula',
	ApplyVisualEffects: 'Görsel efektler uygulayın',
	ApplyVisualEffectsNotSupported: 'Desteklenmeyen görsel efektleri uygulama',
	Appointment: 'Randevu',
	AppointmentAssignedNotificationSubject: '{actorProfileName} sana {appointmentName} atadı.',
	AppointmentCancelledNotificationSubject: '{actorProfileName} {appointmentName} randevusunu iptal etti.',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} {appointmentName} randevusunu onayladı.',
	AppointmentDetails: 'Randevu ayrıntıları',
	AppointmentLocation: 'Randevu yeri',
	AppointmentLocationDescription:
		'Varsayılan sanal ve fiziksel konumlarınızı yönetin. Bir randevu planlandığında bu konumlar otomatik olarak uygulanacaktır.',
	AppointmentNotFound: 'Randevu bulunamadı',
	AppointmentReminder: 'Randevu hatırlatıcısı',
	AppointmentReminders: 'Randevu hatırlatıcıları',
	AppointmentRemindersInfo:
		'Gelmeme ve iptallerden kaçınmak için müşteri randevuları için otomatik hatırlatıcılar ayarlayın',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} {appointmentName} randevusunu yeniden planladı.',
	AppointmentSaved: 'Randevu kaydedildi',
	AppointmentStatus: 'Randevu durumu',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0dk} other {{minutes}dk}} } one {{hours}sa {minutes, plural, =0 {} other {{minutes}dk}}} other {{hours}sa {minutes, plural, =0 {} other {{minutes}dk}}} }',
	AppointmentUndone: 'Randevu geri alındı',
	Appointments: 'Randevular',
	Archive: 'Arşiv',
	ArchiveClients: 'Arşiv istemcileri',
	Archived: 'Arşivlendi',
	AreYouAClient: 'Müşteri misiniz?',
	AreYouStillThere: 'Siz hala orada mısınız?',
	AreYouSure: 'Emin misin?',
	Arrangements: 'Düzenlemeler',
	ArtTherapist: 'Sanat Terapisti',
	Articles: 'Makaleler',
	Artist: 'Sanatçı',
	AskAI: 'Yapay zekaya sor',
	AskAiAddFormField: 'Bir form alanı ekleyin',
	AskAiChangeFormality: 'Change formality',
	AskAiChangeToneToBeMoreProfessional: 'Daha profesyonel olmak için tonu değiştirin',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Bu belgenin ne hakkında olduğunu açıklayın',
	AskAiExplainWhatThisImageIsAbout: 'Bu görselin ne hakkında olduğunu açıklayın',
	AskAiFixSpellingAndGrammar: 'Yazım ve dilbilgisini düzeltin',
	AskAiGenerateACaptionForThisImage: 'Bu resim için bir başlık oluşturun',
	AskAiGenerateFromThisPage: 'Generate from this page',
	AskAiGetStarted: 'Get started',
	AskAiGiveItAFriendlyTone: 'Give it a friendly tone',
	AskAiGreeting: 'Merhaba {firstName}! Bugün size nasıl yardımcı olabilirim?',
	AskAiHowCanIHelpWithYourContent: 'İçeriğinizle ilgili nasıl yardımcı olabilirim?',
	AskAiInsert: 'Sokmak',
	AskAiMakeItMoreCasual: 'Make it more casual',
	AskAiMakeThisTextMoreConcise: 'Bu metni daha öz hale getirin',
	AskAiMoreProfessional: 'More professional',
	AskAiOpenPreviousNote: 'Önceki notu aç',
	AskAiPondering: 'Düşünmek',
	AskAiReplace: 'Yer değiştirmek',
	AskAiReviewOrEditSelection: 'Review or edit selection',
	AskAiRuminating: 'Geviş getiren',
	AskAiSeeMore: 'Daha fazlasını gör',
	AskAiSimplifyLanguage: 'Simplify language',
	AskAiSomethingWentWrong:
		'Bir şeyler ters gitti. Sorun devam ederse lütfen yardım merkezimiz aracılığıyla bizimle iletişime geçin.',
	AskAiStartWithATemplate: 'Bir şablonla başlayın',
	AskAiSuccessfullyCopiedResponse: 'AI yanıtı başarıyla kopyalandı',
	AskAiSuccessfullyInsertedResponse: 'AI yanıtı başarıyla eklendi',
	AskAiSuccessfullyReplacedResponse: 'AI yanıtı başarıyla değiştirildi',
	AskAiSuggested: 'Önerilen',
	AskAiSummariseTextIntoBulletPoints: 'Metni maddeler halinde özetleyin',
	AskAiSummarizeNote: 'Summarize note',
	AskAiThinking: 'Düşünme',
	AskAiToday: 'Bugün {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Bu formla ne yapmak istiyorsunuz?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Şablonu kullanarak profesyonel bir not yazın',
	AskAskAiAnything: 'Yapay zekaya her şeyi sor',
	AskWriteSearchAnything: `Sor, '@' yaz veya herhangi bir şey ara...`,
	Asking: 'Sormak',
	Assessment: 'Değerlendirme',
	Assessments: 'Değerlendirmeler',
	AssessmentsCategoryDescription: 'Müşteri değerlendirmelerini kaydetmek için',
	AssignClients: 'Müşterileri atayın',
	AssignNewClients: 'Müşterileri atayın',
	AssignServices: 'Hizmetleri atayın',
	AssignTeam: 'Ekibi ata',
	AssignTeamMember: 'Ekip üyesini ata',
	Assigned: 'Atandı',
	AssignedClients: 'Atanan müşteriler',
	AssignedServices: 'Atanan hizmetler',
	AssignedServicesDescription:
		'Fiyatları özel fiyatlarınızı yansıtacak şekilde ayarlayarak, atanan hizmetlerinizi görüntüleyin ve yönetin. ',
	AssignedTeam: 'Atanan ekip',
	AthleticTrainer: 'Atletizm Antrenörü',
	AttachFiles: 'Dosyaları ekle',
	AttachLogo: 'Eklemek',
	Attachment: 'EK',
	AttachmentBlockedFileType: 'Güvenlik nedeniyle engellendi!',
	AttachmentTooLargeFileSize: 'Dosya çok büyük',
	AttachmentUploadItemComplete: 'Tamamlamak',
	AttachmentUploadItemError: 'Yükleme başarısız',
	AttachmentUploadItemLoading: 'Yükleniyor',
	AttemptingToReconnect: 'Yeniden bağlanmaya çalışılıyor...',
	Attended: 'katıldım',
	AttendeeBeingMutedTooltip: `Sunucu sizi sessize aldı. Sessizliği kaldırmak için 'el kaldır'ı kullanın`,
	AttendeeWithId: 'Katılımcı {attendeeId}',
	Attendees: 'Katılımcılar',
	AttendeesCount: '{count} katılımcı',
	Attending: 'Katılıyor',
	Audiologist: 'Odyolog',
	Aunt: 'Hala',
	Australia: 'Avustralya',
	AuthenticationCode: 'Kimlik Doğrulama Kodu',
	AuthoriseProvider: '{provider} yetkilendir',
	AuthorisedProviders: 'Yetkili sağlayıcılar',
	AutoDeclineAllFutureOption: 'Sadece yeni etkinlikler veya randevular',
	AutoDeclineAllOption: 'Yeni ve mevcut etkinlikler veya randevular',
	AutoDeclinePrimaryText: 'Otomatik olarak etkinlikleri reddet',
	AutoDeclineSecondaryText:
		'Ofis dışında olduğunuz süre boyunca gerçekleşen etkinlikler otomatik olarak reddedilecektir.',
	AutogenerateBillings: 'Faturalandırma belgelerini otomatik oluştur',
	AutogenerateBillingsDescription:
		'Otomatik fatura belgeleri ayın son gününde oluşturulacaktır. Faturalar ve süper fatura makbuzları istediğiniz zaman manuel olarak oluşturulabilir.',
	AutomateWorkflows: 'İş Akışlarını Otomatikleştirin',
	AutomaticallySendSuperbill: 'Süper fatura makbuzlarını otomatik olarak gönder',
	AutomaticallySendSuperbillHelperText:
		'Süper fatura, sigorta geri ödemesi için müşteriye sağlanan hizmetlerin ayrıntılı bir makbuzudur',
	Automation: 'Otomasyon',
	AutomationActionSendEmailLabel: 'E-posta gönder',
	AutomationActionSendSMSLabel: 'SMS gönder',
	AutomationAndReminders: 'Otomasyon ',
	AutomationDeletedSuccessMessage: 'Otomasyon başarıyla silindi',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Zaman olayı',
	AutomationParams_timeUnit: 'Birim',
	AutomationParams_timeValue: 'Sayı',
	AutomationPublishSuccessMessage: 'Otomasyon başarıyla yayınlandı',
	AutomationPublishWarningTooltip:
		'Lütfen otomasyon yapılandırmasını tekrar kontrol edin ve düzgün şekilde yapılandırıldığından emin olun',
	AutomationTriggerEventCancelledDescription: 'Bir etkinlik iptal edildiğinde veya silindiğinde tetiklenir',
	AutomationTriggerEventCancelledLabel: 'Etkinlik iptal edildi',
	AutomationTriggerEventCreatedDescription: 'Bir olay oluşturulduğunda tetiklenir',
	AutomationTriggerEventCreatedLabel: 'Yeni etkinlik',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Bir etkinlik oluşturulduğunda veya güncellendiğinde tetiklenir (iptal edildiği durumlar hariç)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Yeni veya güncellenen etkinlik',
	AutomationTriggerEventEndedDescription: 'Bir olay sona erdiğinde tetiklenir',
	AutomationTriggerEventEndedLabel: 'Etkinlik sona erdi',
	AutomationTriggerEventStartsDescription: 'Bir olayın başlamasından belirli bir süre önce tetiklenir',
	AutomationTriggerEventStartsLabel: 'Etkinlik başlıyor',
	Automations: 'Otomasyonlar',
	Availability: 'Kullanılabilirlik',
	AvailabilityDisableSchedule: 'Programı devre dışı bırak',
	AvailabilityDisabled: 'Engelli',
	AvailabilityEnableSchedule: 'Programı etkinleştir',
	AvailabilityEnabled: 'Etkinleştirilmiş',
	AvailabilityNoActiveBanner:
		'Tüm programlarınızı kapattınız. Müşteriler sizin için çevrimiçi rezervasyon yapamaz ve gelecekteki tüm randevuların manuel olarak onaylanması gerekir.',
	AvailabilityNoActiveConfirmationDescription:
		'Bu kullanılabilirliğin devre dışı bırakılması, etkin programların olmamasına neden olur. Müşteriler sizin için çevrimiçi rezervasyon yapamayacak ve uygulayıcılar tarafından yapılan rezervasyonlar sizin çalışma saatlerinizin dışında kalacaktır.',
	AvailabilityNoActiveConfirmationProceed: 'Evet, devam et',
	AvailabilityNoActiveConfirmationTitle: 'Aktif program yok',
	AvailabilityToggle: 'Etkin program',
	AvailabilityUnsetDate: 'Tarih belirtilmemiş',
	AvailableLocations: 'Mevcut konumlar',
	AvailablePayers: 'Mevcut ödeme yapanlar',
	AvailablePayersEmptyState: 'Ödemeci seçilmedi',
	AvailableTimes: 'Mevcut zamanlar',
	Back: 'Geri',
	BackHome: 'Eve dön',
	BackToAppointment: 'Randevuya geri dön',
	BackToLogin: 'Girişe Geri Dön',
	BackToMapColumns: 'Harita sütunlarına geri dön',
	BackToTemplates: 'Şablonlara Geri Dön',
	BackToUploadFile: 'Dosyayı Yüklemeye Geri Dön',
	Banker: 'Bankacı',
	BasicBlocks: 'Temel bloklar',
	BeforeAppointment: 'Randevudan {interval} {unit} önce {deliveryType} hatırlatıcısı gönder',
	BehavioralAnalyst: 'Davranış Analisti',
	BehavioralHealthTherapy: 'Davranışsal sağlık terapisi',
	Beta: 'Beta',
	BillTo: 'ya fatura edilecek',
	BillableItems: 'Faturalandırılabilir öğeler',
	BillableItemsEmptyState: 'Faturalandırılabilir hiçbir öğe bulunamadı',
	Biller: 'Faturalayan',
	Billing: 'Faturalama',
	BillingAddress: 'Fatura Adresi',
	BillingAndReceiptsUnauthorisedMessage:
		'Bu bilgilere erişmek için faturalar ve ödemeler görüntüleme erişimi gereklidir.',
	BillingBillablesTab: 'Faturalandırılabilirler',
	BillingClaimsTab: 'İddialar',
	BillingDetails: 'Fatura detayları',
	BillingDocuments: 'Faturalandırma belgeleri',
	BillingDocumentsClaimsTab: 'Talepler',
	BillingDocumentsEmptyState: '{tabType} bulunamadı',
	BillingDocumentsInvoicesTab: 'Faturalar',
	BillingDocumentsSuperbillsTab: 'Süperfaturalar',
	BillingInformation: 'Fatura bilgileri',
	BillingInvoicesTab: 'Faturalar',
	BillingItems: 'Faturalama kalemleri',
	BillingPaymentsTab: 'Ödemeler',
	BillingPeriod: 'Fatura donemi',
	BillingProfile: 'Faturalandırma profili',
	BillingProfileOverridesDescription: 'Bu fatura profilinin kullanımını belirli ekip üyelerine sınırlayın',
	BillingProfileOverridesHeader: 'Erişimi sınırla',
	BillingProfileProviderType: 'Sağlayıcı türü',
	BillingProfileTypeIndividual: 'Uygulayıcı',
	BillingProfileTypeIndividualSubLabel: 'Tip 1 NPI',
	BillingProfileTypeOrganisation: 'Organizasyon',
	BillingProfileTypeOrganisationSubLabel: 'Tip 2 NPI',
	BillingProfiles: 'Faturalama profilleri',
	BillingProfilesEditHeader: '{name} faturalama profilini düzenle',
	BillingProfilesNewHeader: 'Yeni faturalama profili',
	BillingProfilesSectionDescription:
		'Faturalara ve sigorta ödemelerine uygulanabilecek faturalama profilleri oluşturarak pratisyen hekimler ve sigorta ödeyenler için fatura bilgilerinizi yönetin.',
	BillingSearchPlaceholder: 'Öğeleri ara',
	BillingSettings: 'Faturalandırma ayarları',
	BillingSuperbillsTab: 'Süper faturalar',
	BiomedicalEngineer: 'Biyomedikal mühendisi',
	BlankInvoice: 'Boş fatura',
	BlueShieldProviderNumber: 'Blue Shield sağlayıcı numarası',
	Body: 'Vücut',
	Bold: 'Gözü pek',
	BookAgain: 'Tekrar rezervasyon yaptırın',
	BookAppointment: 'Kitap randevusu',
	BookableOnline: 'Çevrimiçi rezervasyon yapılabilir',
	BookableOnlineHelper: 'Müşteriler bu hizmet için çevrimiçi rezervasyon yapabilir',
	BookedOnline: 'Çevrimiçi Rezervasyon Yapıldı',
	Booking: 'Rezervasyon',
	BookingAnalyticsIntegrationPanelDescription: `Çevrimiçi rezervasyon akışınızdaki önemli eylemleri ve dönüşümleri izlemek için Google Etiket Yöneticisi'ni ayarlayın. Pazarlama çabalarını iyileştirmek ve rezervasyon deneyimini optimize etmek için kullanıcı etkileşimleri hakkında değerli veriler toplayın.`,
	BookingAnalyticsIntegrationPanelTitle: 'Analitik entegrasyonu',
	BookingAndCancellationPolicies: 'Rezervasyon ',
	BookingButtonEmbed: 'Düğme',
	BookingButtonEmbedDescription: 'Web sitenize çevrimiçi rezervasyon düğmesi ekler',
	BookingDirectTextLink: 'Doğrudan metin bağlantısı',
	BookingDirectTextLinkDescription: 'Çevrimiçi rezervasyon sayfasını açar',
	BookingFormatLink: 'Bağlantıyı biçimlendir',
	BookingFormatLinkButtonTitle: 'Düğme başlığı',
	BookingInlineEmbed: 'Satır içi yerleştirme',
	BookingInlineEmbedDescription: 'Çevrimiçi rezervasyon sayfasını doğrudan web sitenize yükler',
	BookingLink: 'Rezervasyon bağlantısı',
	BookingLinkModalCopyText: 'Kopyala',
	BookingLinkModalDescription:
		'Bu bağlantıya sahip müşterilerin herhangi bir ekip üyesi veya hizmet için rezervasyon yapmasına izin ver',
	BookingLinkModalHelpText: 'Çevrimiçi rezervasyonları nasıl ayarlayacağınızı öğrenin',
	BookingLinkModalTitle: 'Rezervasyon bağlantınızı paylaşın',
	BookingPolicies: 'Rezervasyon politikaları',
	BookingPoliciesDescription: 'Müşteriler tarafından çevrimiçi rezervasyonların ne zaman yapılabileceğini ayarlayın',
	BookingTimeUnitDays: 'günler',
	BookingTimeUnitHours: 'saat',
	BookingTimeUnitMinutes: 'dakika',
	BookingTimeUnitMonths: 'aylar',
	BookingTimeUnitWeeks: 'haftalar',
	BottomNavBilling: 'Faturalandırma',
	BottomNavGettingStarted: 'Ev',
	BottomNavMore: 'Daha',
	BottomNavNotes: 'Notlar',
	Brands: 'Markalar',
	Brother: 'Erkek kardeş',
	BrotherInLaw: 'Kayınbirader',
	BrowseOrDragFileHere: '<link>Gözat</link> veya dosyayı buraya sürükleyin',
	BrowseOrDragFileHereDescription: 'PNG, JPG (maks. {limit})',
	BufferAfterTime: '{time} dakika sonra',
	BufferAndLabel: 'Ve',
	BufferAppointmentLabel: 'randevu',
	BufferBeforeTime: '{time} dakika önce',
	BufferTime: 'Tampon süresi',
	BufferTimeViewLabel: '{bufferBefore} dakika öncesi ve {bufferAfter} dakika sonrasında randevular',
	BulkArchiveClientsDescription:
		'Bu istemcileri arşivlemek istediğinizden emin misiniz? Daha sonra yeniden etkinleştirebilirsiniz.',
	BulkArchiveSuccess: 'Müşteriler başarıyla arşivlendi',
	BulkArchiveUndone: 'Toplu arşiv geri alındı',
	BulkPermanentDeleteDescription: 'Bu, **{count} görüşmeyi** silecektir. Bu işlem geri alınamaz.',
	BulkPermanentDeleteTitle: 'Konuşmaları sonsuza dek sil',
	BulkUnarchiveSuccess: 'Müşteriler başarıyla arşivden çıkarıldı',
	BulletedList: 'Maddeli liste',
	BusinessAddress: 'İşletme adresi',
	BusinessAddressOptional: 'İşletme adresi<span>(İsteğe bağlı)</span>',
	BusinessName: 'İş adı',
	Button: 'Düğme',
	By: 'İle',
	CHAMPUSIdentificationNumber: 'CHAMPUS kimlik numarası',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC gereklidir',
	Calendar: 'Takvim',
	CalendarAppSyncFormDescription: 'Carepatron etkinliklerini senkronize et',
	CalendarAppSyncPanelTitle: 'Bağlı uygulama senkronizasyonu',
	CalendarDescription: 'Randevlarınızı yönetin veya kişisel görevleri ve hatırlatıcıları ayarlayın',
	CalendarDetails: 'Takvim detayları',
	CalendarDetailsDescription: 'Takviminizi ve randevu görüntüleme ayarlarınızı yönetin.',
	CalendarScheduleNew: 'Yeni planla',
	CalendarSettings: 'Takvim ayarları',
	Call: 'Çağrı',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> video görüşmesine katıldı',
	CallChangeLayoutTextContent: 'Seçim gelecekteki toplantılar için kaydedildi',
	CallIdlePrompt: 'Katılmak için beklemeyi mi tercih edersiniz yoksa daha sonra tekrar denemek ister misiniz?',
	CallLayoutOptionAuto: 'Otomatik',
	CallLayoutOptionSidebar: 'Kenar çubuğu',
	CallLayoutOptionSpotlight: 'Odak',
	CallLayoutOptionTiled: 'Döşeli',
	CallNoAttendees: 'Toplantıya katılımcı yok.',
	CallSessionExpiredError: 'Oturum süresi doldu. Çağrı sonlandırıldı. Lütfen tekrar katılmayı deneyin.',
	CallWithPractitioner: '{practitioner} ile görüşme',
	CallsListCreateButton: 'Yeni arama',
	CallsListEmptyState: 'Etkin çağrı yok',
	CallsListItemEndCall: 'Son Arama',
	CamWarningMessage: 'Kameranızla ilgili bir sorun algılandı',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Lütfen Carepatron'un kameranıza ve mikrofonunuza erişimini etkinleştirin.
 Daha fazla bilgi için <a>bu kılavuzu izleyin</a>`,
	CameraAndMicIssueModalTitle: 'Kamera ve mikrofon engellendi',
	CameraQuality: 'Kamera kalitesi',
	CameraSource: 'Kamera kaynağı',
	CanModifyReadOnlyEvent: 'Bu etkinliği değiştiremezsiniz.',
	Canada: 'Kanada',
	Cancel: 'İptal etmek',
	CancelClientImportDescription: 'Bu içe aktarmayı iptal etmek istediğinizden emin misiniz?',
	CancelClientImportPrimaryAction: 'Evet, içe aktarmayı iptal et',
	CancelClientImportSecondaryAction: 'Düzenlemeye devam et',
	CancelClientImportTitle: 'İstemci içe aktarmayı İptal Et',
	CancelImportButton: 'İçe aktarmayı iptal et',
	CancelPlan: 'Planı iptal et',
	CancelPlanConfirmation: `Planı iptal ettiğinizde, bu aya ait ödenmemiş bakiyeleriniz otomatik olarak hesabınızdan tahsil edilecektir.
 Faturalandırılan kullanıcılarınızın düzeyini düşürmek istiyorsanız ekip üyelerini kaldırmanız yeterlidir; Carepatron abonelik fiyatınızı otomatik olarak güncelleyecektir.`,
	CancelSend: 'Gönderimi iptal et',
	CancelSubscription: 'Aboneliği iptal et',
	Canceled: 'İptal Edildi',
	CancellationPolicy: 'İptal politikası',
	Cancelled: 'İptal edildi',
	CannotContainSpecialCharactersError: '{specialCharacters} içeremez',
	CannotDeleteInvoice: 'Çevrimiçi ödeme yoluyla ödenen faturalar silinemez',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b>, <b>{group}</b> grubundan çıkarılamaz.',
	CannotMoveServiceOutsideCollections: 'Hizmet, koleksiyonların dışına taşınamaz',
	CapeTown: 'Cape Town',
	Caption: 'Altyazı',
	CaptureNameFieldLabel: 'Başkalarının size hitap etmesini istediğiniz isim',
	CapturePaymentMethod: 'Ödeme yöntemini yakala',
	CapturingAudio: 'Ses yakalama',
	CapturingSignature: 'İmza alınıyor...',
	CardInformation: 'Kart bilgisi',
	CardNumberRequired: 'Kart numarası gerekli',
	CardiacRehabilitationSpecialist: 'Kardiyak Rehabilitasyon Uzmanı',
	Cardiologist: 'Kardiyolog',
	CareAiNoConversations: 'Henüz sohbet yok',
	CareAiNoConversationsDescription: '{aiName} ile sohbete başlamak için',
	CareAssistant: 'Bakım Asistanı',
	CareManager: 'Bakım Müdürü',
	Caregiver: 'BAKICI',
	CaregiverCreateModalDescription:
		'Personeli yönetici olarak eklemek, onların bakım hikayeleri oluşturup yönetmelerine olanak tanıyacaktır. Ayrıca onlara müşteri oluşturma ve yönetme konusunda tam erişim sağlar.',
	CaregiverCreateModalTitle: 'Yeni ekip üyesi',
	CaregiverListCantAddStaffInfoTitle:
		'Aboneliğiniz için maksimum personel sayısına ulaştınız. Daha fazla personel eklemek için lütfen planınızı yükseltin.',
	CaregiverListCreateButton: 'Yeni ekip üyesi',
	CaregiverListEmptyState: 'Hiçbir bakıcı eklenmedi',
	CaregiversListItemRemoveStaff: 'Personeli kaldır',
	CarepatronApp: 'Carepatron uygulaması',
	CarepatronCommunity: 'Toplum',
	CarepatronFieldAddress: 'Adres',
	CarepatronFieldAssignedStaff: 'Atanan personel',
	CarepatronFieldBirthDate: 'Doğum günü',
	CarepatronFieldEmail: 'E-posta',
	CarepatronFieldEmploymentStatus: 'Çalışma durumu',
	CarepatronFieldEthnicity: 'Etnik köken',
	CarepatronFieldFirstName: 'İlk adı',
	CarepatronFieldGender: 'Cinsiyet',
	CarepatronFieldIdentificationNumber: 'Kimlik Numarası',
	CarepatronFieldIsArchived: 'Durum',
	CarepatronFieldLabel: 'Etiket',
	CarepatronFieldLastName: 'Soy isim',
	CarepatronFieldLivingArrangements: 'Yaşam düzenlemeleri',
	CarepatronFieldMiddleNames: 'İkinci ad',
	CarepatronFieldOccupation: 'Meslek',
	CarepatronFieldPhoneNumber: 'Telefon numarası',
	CarepatronFieldRelationshipStatus: 'İlişki durumu',
	CarepatronFieldStatus: 'Durum',
	CarepatronFieldStatusHelperText: 'Maksimum 10 durum.',
	CarepatronFieldTags: 'Etiketler',
	CarepatronFields: 'Carepatron alanları',
	Cash: 'Peşin',
	Category: 'Kategori',
	CategoryInputPlaceholder: 'Bir şablon kategorisi seçin',
	CenterAlign: 'Ortaya hizala',
	Central: 'Merkezi',
	ChangeLayout: 'Düzeni değiştir',
	ChangeLogo: 'Değiştirmek',
	ChangePassword: 'Şifre değiştir',
	ChangePasswordFailureSnackbar:
		'Üzgünüz, şifreniz değiştirilmedi. Eski şifrenizin doğru olup olmadığını kontrol edin.',
	ChangePasswordHelperInfo: 'Minimum uzunluk {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Şifre başarıyla değiştirildi! Bir sonraki girişinizde bu şifreyi kullandığınızdan emin olun.',
	ChangeSubscription: 'Aboneliği Değiştir',
	ChangesNotAllowed: 'Bu alanda değişiklik yapılamaz',
	ChargesDisabled: 'Ücretlendirmeler devre dışı bırakıldı',
	ChargesEnabled: 'Ücretlendirmeler etkinleştirildi',
	ChargesStatus: 'Ücret durumu',
	ChartAndDiagram: 'Çizelge/Şema',
	ChartsAndDiagramsCategoryDescription: 'Müşteri verilerini ve ilerlemesini göstermek için',
	ChatEditMessage: 'Mesajı Düzenle',
	ChatReplyTo: '{name}’e Cevapla',
	ChatTypeMessageTo: 'Mesaj {name}',
	Check: 'Kontrol etmek',
	CheckList: 'Kontrol listesi',
	Chef: 'Şef',
	Chiropractic: 'kayropraktik',
	Chiropractor: 'Kiropraktör',
	Chiropractors: 'Kiropraktörler',
	ChooseACollection: 'Bir koleksiyon seçin',
	ChooseAContact: 'Bir kişi seçin',
	ChooseAccountTypeHeader: 'Hangisi seni en iyi tanımlıyor?',
	ChooseAction: 'Eylemi seçin',
	ChooseAnAccount: 'Bir hesap seçin',
	ChooseAnOption: 'Bir seçenek seçin',
	ChooseBillingProfile: 'Faturalama profilini seçin',
	ChooseClaim: 'İddiayı seçin',
	ChooseCollection: 'Koleksiyon seçin',
	ChooseColor: 'Renk seç',
	ChooseCustomDate: 'Özel tarih seçin',
	ChooseDateAndTime: 'Tarih ve zaman seç',
	ChooseDxCodes: 'Teşhis kodlarını seçin',
	ChooseEventType: 'Etkinlik türünü seçin',
	ChooseFileButton: 'Bir dosya seçin',
	ChooseFolder: 'Klasör seç',
	ChooseInbox: 'Gelen kutusunu seçin',
	ChooseMethod: 'Yöntemi seçin',
	ChooseNewOwner: 'Yeni sahibi seçin',
	ChooseOrganization: 'Kuruluşu Seçin',
	ChoosePassword: 'Şifre seçin',
	ChoosePayer: 'Ödeyiciyi seçin',
	ChoosePaymentMethod: 'Bir ödeme yöntemi seçin',
	ChoosePhysicalOrRemoteLocations: 'Konum girin veya seçin',
	ChoosePlan: '{plan} seçin',
	ChooseProfessional: 'Profesyoneli Seçin',
	ChooseServices: 'Hizmetleri seçin',
	ChooseSource: 'Kaynak Seç',
	ChooseSourceDescription:
		'Müşterileri nereden içe aktardığınızı seçin - bir dosya mı yoksa başka bir yazılım platformu mu.',
	ChooseTags: 'Etiketleri seçin',
	ChooseTaxName: 'Vergi adını seçin',
	ChooseTeamMembers: 'Takım Üyeleri Seçin',
	ChooseTheme: 'Tema Seç',
	ChooseTrigger: 'Tetikleyiciyi seçin',
	ChooseYourProvider: 'Sağlayıcınızı seçin',
	CircularProgressWithLabel: '{value}%',
	City: 'Şehir',
	CivilEngineer: 'İnşaat mühendisi',
	Claim: 'İddia',
	ClaimAddReferringProvider: 'Yönlendiren Sağlayıcı Ekle',
	ClaimAddRenderingProvider: 'İşleme sağlayıcısı ekle',
	ClaimAmount: 'Talep miktarı',
	ClaimAmountPaidHelpContent:
		'Ödenen tutar, hastadan veya diğer ödeyicilerden alınan ödemedir. Hastanın ve/veya diğer ödeyicilerin yalnızca kapsanan hizmetler için ödediği toplam tutarı girin.',
	ClaimAmountPaidHelpSubtitle: 'Alan 29',
	ClaimAmountPaidHelpTitle: 'Ödenen miktar',
	ClaimBillingProfileTypeIndividual: 'Bireysel',
	ClaimBillingProfileTypeOrganisation: 'Organizasyon',
	ClaimChooseRenderingProviderOrTeamMember: 'İşleme sağlayıcısını veya ekip üyesini seçin',
	ClaimClientInsurancePolicies: 'Müşteri sigorta poliçeleri',
	ClaimCreatedAction: '<mark>Talep {claimNumber}</mark> oluşturuldu',
	ClaimDeniedAction: '<mark>Talep {claimNumber}</mark> <b>{payerNumber} {payerName}</b> tarafından reddedildi.',
	ClaimDiagnosisCodeSelectorPlaceholder: 'ICD 10 tanı kodlarını arayın',
	ClaimDiagnosisSelectorHelpContent: `“Tanı veya yaralanma”, talepte yer alan hizmet(ler) ile ilgili olarak hastanın belirtisi, semptomu, şikayeti veya durumudur.
 12 adede kadar ICD 10 tanı kodu seçilebilir.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Alan 21',
	ClaimDiagnosisSelectorHelpTitle: 'Tanı veya yaralanma',
	ClaimDiagnosticCodesEmptyError: 'En az bir tanı kodu gerekli',
	ClaimDoIncludeReferrerInformation: `CMS1500'e yönlendiren bilgileri ekleyin`,
	ClaimERAReceivedAction: 'Elektronik havale <b>{payerNumber} {payerName}</b> tarafından alınmıştır.',
	ClaimElectronicPaymentAction:
		'Elektronik havale alındı	<mark>Ödeme {paymentReference}</mark> <b>{paymentAmount}</b> için <b>{payerNumber} {payerName}</b> tarafından kaydedildi',
	ClaimExportedAction: '<mark>Talep {claimNumber}</mark>  <b>{attachmentType}</b> olarak dışa aktarıldı',
	ClaimFieldClient: 'Müşteri veya iletişim adı',
	ClaimFieldClientAddress: 'Müşteri adresi',
	ClaimFieldClientAddressDescription:
		'Müşterinin adresini girin. İlk satır sokak adresi içindir. Adreste noktalama işareti (virgül veya nokta) veya herhangi bir sembol kullanmayın. Yabancı bir adresi bildiriyorsanız, belirli bildirim talimatları için ödeyiciyle iletişime geçin.',
	ClaimFieldClientAddressSubtitle: 'Alan 5',
	ClaimFieldClientDateOfBirth: 'Müşterinin Doğum Tarihi',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Alan 3',
	ClaimFieldClientDateOfBirthDescription:
		'Müşterinin 8 haneli doğum tarihini girin (AA/GG/YYYY). Müşterinin doğum tarihi, müşteriyi tanımlayacak ve benzer isimlere sahip kişileri ayırt edecek bir bilgidir.',
	ClaimFieldClientDescription: 'Müşteri adı, tedaviyi veya malzemeyi alan kişinin adıdır.',
	ClaimFieldClientSexDescription: `'Cinsiyet', danışanı tanımlayacak ve benzer isimlere sahip kişileri birbirinden ayırt etmeye yarayacak bir bilgidir.`,
	ClaimFieldClientSubtitle: 'Alan 2',
	ClaimFiling: 'Talep Dosyası',
	ClaimHistorySubtitle: 'Sigorta • Talep {number}',
	ClaimIncidentAutoAccident: 'Trafik kazası mı?',
	ClaimIncidentConditionRelatedTo: 'Müşterinin durumu aşağıdakilerle ilgili mi?',
	ClaimIncidentConditionRelatedToHelpContent:
		'Bu bilgi, müvekkilin hastalığının veya yaralanmasının istihdam, araba kazası veya başka bir kaza ile ilgili olup olmadığını gösterir. İstihdam (geçerli veya önceki), durumun müvekkilin işi veya işyeri ile ilgili olduğunu gösterir. Araba kazası, durumun bir araba kazasının sonucu olduğunu gösterir. Diğer kaza, durumun başka herhangi bir tür kazanın sonucu olduğunu gösterir.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Alanlar 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Müşterinin durumu aşağıdakilerle ilgili mi?',
	ClaimIncidentCurrentIllness: 'Mevcut hastalık, yaralanma veya hamilelik',
	ClaimIncidentCurrentIllnessHelpContent:
		'Mevcut hastalık, yaralanma veya gebelik tarihi, hastalığın ilk başlangıç tarihini, yaralanmanın gerçek tarihini veya gebelik için son doğum tarihini belirtir.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Alan 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Mevcut hastalık, yaralanma veya hamilelik tarihleri (LMP)',
	ClaimIncidentDate: 'Tarih',
	ClaimIncidentDateFrom: 'Tarihten itibaren',
	ClaimIncidentDateTo: 'Tarihe kadar',
	ClaimIncidentEmploymentRelated: 'İstihdam',
	ClaimIncidentEmploymentRelatedDesc: '(Mevcut veya önceki)',
	ClaimIncidentHospitalizationDatesLabel: 'Mevcut hizmetlere ilişkin hastane yatış tarihleri',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Mevcut hizmetlere ilişkin hastane yatış tarihleri, bir müşterinin kalış süresini ifade etmekte olup, talepte belirtilen hizmet(ler)le ilişkili yatış ve taburcu tarihleri belirtilmektedir.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Alan 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Mevcut hizmetlere ilişkin hastane yatış tarihleri',
	ClaimIncidentInformation: 'Olay bilgisi',
	ClaimIncidentOtherAccident: 'Başka bir kaza?',
	ClaimIncidentOtherAssociatedDate: 'Diğer ilişkili tarih',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Diğer tarih ise danışanın durumu veya tedavisi hakkında ek tarih bilgisini tanımlar.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Alan 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Diğer tarih',
	ClaimIncidentQualifier: 'Nitelikli',
	ClaimIncidentQualifierPlaceholder: 'Niteleyiciyi seçin',
	ClaimIncidentUnableToWorkDatesLabel: 'Müşteri mevcut mesleğinde çalışamadı',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Müşterinin mevcut mesleğinde çalışamadığı tarihler, müşterinin çalışamadığı veya çalışamadığı zaman aralığıdır.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Alan 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Müşterinin mevcut mesleğinde çalışamadığı tarihler',
	ClaimIncludeReferrerInformation: `CMS1500'e yönlendiren bilgileri ekleyin`,
	ClaimInsuranceCoverageTypeHelpContent: `Bu talep için geçerli sağlık sigortası kapsamının türü. Diğer, HMO'lar, ticari sigorta, otomobil kazası, sorumluluk veya işçi tazminatı dahil olmak üzere sağlık sigortasını belirtir.
 Bu bilgi, iddiayı doğru programa yönlendirir ve birincil sorumluluğun tespit edilmesini sağlayabilir.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Alan 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Kapsam türü',
	ClaimInsuranceGroupIdHelpContent: `Sigortalının sağlık kimlik kartında görünen poliçe veya grup numarasını girin.

 “Sigortalı Poliçesi, Grubu veya FECA Numarası” sağlık, otomobil veya diğer sigorta planı kapsamı için alfanümerik tanımlayıcıdır. FECA numarası, iş ile ilgili bir rahatsızlığı talep eden bir hastaya atanan 9 karakterli alfanümerik tanımlayıcıdır.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Alan 11',
	ClaimInsuranceGroupIdHelpTitle: 'Sigortalının Poliçesi, Grubu veya FECA Numarası',
	ClaimInsuranceMemberIdHelpContent: `Talebin iletildiği ödeyici kuruluşa ait sigortalının kimlik kartında görünen sigortalının kimlik numarasını giriniz.
 Hastanın ödeyici tarafından atanmış benzersiz bir Üye Kimlik Numarası varsa, bu numarayı bu alana girin.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Alan 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Sigortalının Üye Kimliği',
	ClaimInsurancePayer: 'Sigorta ödeyicisi',
	ClaimManualPaymentAction: '<mark>Ödeme {paymentReference}</mark> <b>{paymentAmount}</b> için kaydedildi',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Ek talep bilgisi',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Lütfen bu alanın kullanımına ilişkin kamu veya özel ödeme yapan kuruluştan gelen mevcut talimatlara bakın. Girilen bilgilere ilişkin uygun niteleyiciyi, mevcutsa, rapor edin.Niteleyici ile bilgi arasında boşluk, tire veya başka bir ayırıcı girmeyin.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Alan 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Ek talep bilgisi',
	ClaimMiscClaimCodes: 'Talep kodları',
	ClaimMiscOriginalReferenceNumber: 'Orijinal referans numarası',
	ClaimMiscPatientsAccountNumber: 'Hastanın hesap numarası',
	ClaimMiscPatientsAccountNumberHelpContent:
		'Hastanın hesap numarası, hizmet sağlayıcı tarafından atanan tanımlayıcıdır.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Alan 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Hastanın hesap numarası',
	ClaimMiscPriorAuthorizationNumber: 'Ön izin numarası',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Önceki yetkilendirme numarası, ödeyici tarafından hizmet/hizmetleri yetkilendiren numaradır.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Alan 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Ön izin numarası',
	ClaimMiscResubmissionCode: 'Yeniden gönderme kodu',
	ClaimMiscResubmissionCodeHelpContent:
		'Yeniden gönderim, daha önce gönderilmiş bir talebi veya karşılaşmayı belirtmek için hedef ödeyici veya alıcı tarafından atanan kod ve orijinal referans numarası anlamına gelir.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Alan 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Yeniden Gönderme ve/veya Orijinal Referans Numarası',
	ClaimNumber: 'Talep Numarası',
	ClaimNumberFormat: 'Talep #{number}',
	ClaimOrderingProvider: 'Sipariş sağlayıcı',
	ClaimOtherId: 'Diğer Kimlik',
	ClaimOtherIdPlaceholder: 'Bir seçenek seçin',
	ClaimOtherIdQualifier: 'Diğer kimlik niteleyicisi',
	ClaimOtherIdQualifierPlaceholder: 'Kimlik niteleyicisini seçin',
	ClaimPlaceOfService: 'Hizmet yeri',
	ClaimPlaceOfServicePlaceholder: 'POS ekle',
	ClaimPolicyHolderRelationship: 'Poliçe sahibi ilişkisi',
	ClaimPolicyInformation: 'Politika bilgisi',
	ClaimPolicyTelephone: 'Telefon (alan kodunu ekleyin)',
	ClaimReceivedAction: '<mark>Talep {claimNumber}</mark>  <b>{name}</b> tarafından alındı',
	ClaimReferringProvider: 'Yönlendiren sağlayıcı',
	ClaimReferringProviderEmpty: 'Eklenecek yönlendiren sağlayıcı/lar yok',
	ClaimReferringProviderHelpContent:
		'Girilen ad, talepteki hizmeti(leri) veya malzemeyi(leri) yönlendiren, sipariş eden veya denetleyen yönlendiren sağlayıcı, sipariş eden sağlayıcı veya denetleyen sağlayıcıdır. Niteleyici, rapor edilen sağlayıcının rolünü belirtir.',
	ClaimReferringProviderHelpSubtitle: 'Alan 17',
	ClaimReferringProviderHelpTitle: 'Yönlendiren sağlayıcının veya kaynağın adı',
	ClaimReferringProviderQualifier: 'Nitelikli',
	ClaimReferringProviderQualifierPlaceholder: 'Niteleyiciyi seçin',
	ClaimRejectedAction: '<mark>Talep {claimNumber}</mark> <b>{name}</b> tarafından reddedildi.',
	ClaimRenderingProviderIdNumber: 'Kimlik numarası',
	ClaimRenderingProviderOrTeamMember: 'Sağlayıcı veya ekip üyesi',
	ClaimRestoredAction: '<mark>İddia {claimNumber}</mark> geri yüklendi',
	ClaimServiceFacility: 'Servis tesisi',
	ClaimServiceFacilityLocationHelpContent:
		'Hizmetin sunulduğu tesisin adı ve adresi, hizmetin/hizmetlerin sunulduğu yeri belirtir.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a ve 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Alan 32, 32a ve 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Hizmet Tesisi',
	ClaimServiceFacilityPlaceholder: 'Servis tesisini veya lokasyonu seçin',
	ClaimServiceLabChargesHelpContent: `Fatura sağlayıcısı dışındaki bir kuruluş tarafından sağlanan satın alınan hizmetler için talepte bulunurken bu alanı doldurun.
 CMS1500 formuna yalnızca bir ücret girilebildiğinden satın alınan her hizmetin ayrı bir talepte bildirilmesi gerekmektedir.`,
	ClaimServiceLabChargesHelpSubtitle: 'Alan 20',
	ClaimServiceLabChargesHelpTitle: 'Dış laboratuvar ücretleri',
	ClaimServiceLineServiceHelpContent:
		'"İşlemler, Hizmetler veya Malzemeler" hastaya sağlanan tıbbi hizmetleri ve işlemleri tanımlar.',
	ClaimServiceLineServiceHelpSubtitle: 'Alan 24d',
	ClaimServiceLineServiceHelpTitle: 'Prosedürler, Hizmetler veya Malzemeler',
	ClaimServiceLinesEmptyError: 'En az bir servis hattı gereklidir',
	ClaimServiceSupplementaryInfoHelpContent: `Uygulanabilir niteleyicileri kullanarak sağlanan hizmetlerin ek anlatım açıklamasını ekleyin.
 Niteleyici ile bilgi arasına boşluk, tire veya başka bir ayırıcı koymayın.

 Ek bilgi eklemeye ilişkin tüm talimatlar için CMS 1500 talep formu talimatlarını inceleyin.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Alan 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Ek bilgi',
	ClaimSettingsBillingMethodTitle: 'Müşteri faturalandırma yöntemi',
	ClaimSettingsClientSignatureDescription:
		'Sigorta taleplerinin işlenmesi için gerekli tıbbi veya diğer bilgilerin açıklanmasına ilişkin onayım var.',
	ClaimSettingsClientSignatureTitle: 'Müşteri imzası dosyada',
	ClaimSettingsConsentLabel: 'Sigorta taleplerinin işlenmesi için onay gereklidir:',
	ClaimSettingsDescription: 'Sorunsuz ödeme işlemi sağlamak için müşteri faturalandırma yöntemini seçin:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} aktif bir sigorta poliçesine sahip. Sigorta faturalandırmasını etkinleştirmek için Müşteri fatura yöntemini Sigorta olarak güncelleyin.',
	ClaimSettingsInsuranceDescription: 'Sigorta tarafından karşılanan masraflar',
	ClaimSettingsInsuranceTitle: 'Sigorta',
	ClaimSettingsNoPoliciesAlertDescription: 'Sigorta taleplerini etkinleştirmek için bir sigorta poliçesi ekleyin.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Sağlanan hizmetler karşılığında sigorta ödemeleri almaya onay veriyorum.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Dosyadaki poliçe sahibi imzası',
	ClaimSettingsSelfPayDescription: 'Müşteri randevuların ücretini ödeyecek',
	ClaimSettingsSelfPayTitle: 'Kendi kendine ödeme',
	ClaimSettingsTitle: 'Talep ayarları',
	ClaimSexSelectorPlaceholder: 'Erkek / Kadın',
	ClaimStatusChangedAction: '<mark>Talep {claimNumber}</mark> durumu güncellendi',
	ClaimSubmittedAction: `<mark>Talep {claimNumber}</mark> <b>{payerClearingHouse}</b>'a  <b>{payerNumber} {payerName}</b> için gönderildi.`,
	ClaimSubtitle: 'Talep #{claimNumber}',
	ClaimSupervisingProvider: 'Denetleyici sağlayıcı',
	ClaimSupplementaryInfo: 'Ek bilgi',
	ClaimSupplementaryInfoPlaceholder: 'Ek bilgi ekle',
	ClaimTrashedAction: '<mark>Talep {claimNumber}</mark> silindi',
	ClaimValidationFailure: 'İddia doğrulanamadı',
	ClaimsEmptyStateDescription: 'Hiçbir Talep Bulunamadı.',
	ClainInsuranceTelephone: 'Sigorta Telefonu (alan kodunu ekleyin)',
	Classic: 'Klasik',
	Clear: 'Temizlemek',
	ClearAll: 'Tümünü Temizle',
	ClearSearchFilter: 'Temizlemek',
	ClearingHouse: 'Ev Temizliği',
	ClearingHouseClaimId: 'Claim.MD Kimliği',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Ev referansını temizleme',
	ClearingHouseUnavailableError: 'Temizleme evi şu anda kullanılamıyor. Lütfen daha sonra tekrar deneyin.',
	ClickToUpload: 'Yüklemek için tıklayın',
	Client: 'Müşteri',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} {noteTitle, select, undefined {bir not} other {{noteTitle}}} ekledi.',
	ClientAndRelationshipSelectorPlaceholder: 'Müşterileri ve onların ilişkilerini seçin',
	ClientAndRelationshipSelectorTitle: 'Tüm müşteriler ve ilişkileri',
	ClientAndRelationshipSelectorTitle1: '‘{name}’ ile ilgili tüm ilişkiler',
	ClientAppCallsPageNoOptionsText:
		'Bir video görüşmesi bekliyorsanız, kısa süre içinde burada görünecektir. Herhangi bir sorun yaşıyorsanız lütfen sorunu başlatan kişiyle iletişime geçin.',
	ClientAppSubHeaderMyDocumentation: 'Belgelerim',
	ClientAppointment: 'Müşteri Randevusu',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} {appointmentName} rezervasyonu yaptı.',
	ClientAppointmentsEmptyStateDescription: 'Randevu bulunamadı',
	ClientAppointmentsEmptyStateTitle: 'Müşterilerinizin yaklaşan ve geçmiş randevularını ve katılımlarını takip edin',
	ClientArchivedSuccessfulSnackbar: 'Başarıyla arşivlendi <b>{name}</b>',
	ClientBalance: 'Müşteri bakiyesi',
	ClientBilling: 'Faturalama',
	ClientBillingAddPaymentMethodDescription:
		'Faturalandırma ve faturalandırma süreçlerini kolaylaştırmak için müşterinizin ödeme yöntemlerini ekleyin ve yönetin.',
	ClientBillingAndPaymentDueDate: 'Bitiş tarihi',
	ClientBillingAndPaymentHistory: 'Faturalandırma ve ödeme geçmişi',
	ClientBillingAndPaymentInvoices: 'Faturalar',
	ClientBillingAndPaymentIssueDate: 'Düzenleme tarihi',
	ClientBillingAndPaymentPrice: 'Fiyat',
	ClientBillingAndPaymentReceipt: 'Fiş',
	ClientBillingAndPaymentServices: 'Hizmetler',
	ClientBillingAndPaymentStatus: 'Durum',
	ClientBulkStaffAssignedSuccessSnackbar: 'Ekip {count, plural, one {üyesi} other {üyeleri}} atandı!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Takım {count, plural, one {üyesi} other {üyesi}} atanmamış!',
	ClientBulkTagsAddedSuccessSnackbar: 'Etiketler eklendi!',
	ClientDuplicatesDeviewDescription:
		'Tüm verileri (notlar, belgeler, randevular, faturalar ve görüşmeler) birleştirmek için birden fazla müşteri kaydını tek bir kayıtta birleştirin.',
	ClientDuplicatesPageMergeHeader: 'Saklamak istediğiniz verileri seçin',
	ClientDuplicatesReviewHeader: 'Birleştirme için olası yinelenen kayıtları karşılaştırın',
	ClientEmailChangeWarningDescription:
		'Müşterinin e-postasının güncellenmesi, paylaşılan belgelere erişimini kaldıracak ve kullanıcıya yeni e-postayla erişim izni verecektir.',
	ClientFieldDateDescription: 'Tarihi biçimlendir',
	ClientFieldDateLabel: 'Tarih',
	ClientFieldDateRangeDescription: 'Bir tarih aralığı',
	ClientFieldDateRangeLabel: 'Tarih aralığı',
	ClientFieldDateShowDateDescription: 'örneğin 29 yıl',
	ClientFieldDateShowDateRangeDescription: 'örneğin 2 hafta',
	ClientFieldEmailDescription: 'E-posta adresi',
	ClientFieldEmailLabel: 'E-posta',
	ClientFieldLabel: 'Alan etiketi',
	ClientFieldLinearScaleDescription: 'Ölçek seçenekleri 1-10',
	ClientFieldLinearScaleLabel: 'Doğrusal ölçek',
	ClientFieldLocationDescription: 'Fiziksel veya posta adresi',
	ClientFieldLocationLabel: 'Konum',
	ClientFieldLongTextDescription: 'Uzun metin alanı',
	ClientFieldLongTextLabel: 'Paragraf',
	ClientFieldMultipleChoiceDropdownDescription: 'Listeden birden fazla seçenek seçin',
	ClientFieldMultipleChoiceDropdownLabel: 'Çoktan seçmeli açılır menü',
	ClientFieldPhoneNumberDescription: 'Telefon numarası',
	ClientFieldPhoneNumberLabel: 'Telefon',
	ClientFieldPlaceholder: 'Bir istemci alanı türü seçin',
	ClientFieldSingleChoiceDropdownDescription: 'Listeden yalnızca bir seçeneği seçin',
	ClientFieldSingleChoiceDropdownLabel: 'Tek seçenekli açılır menü',
	ClientFieldTextDescription: 'Metin giriş alanı',
	ClientFieldTextLabel: 'Metin',
	ClientFieldYesOrNoDescription: 'Evet veya hayır seçenekleri arasından seçim yapın',
	ClientFieldYesOrNoLabel: 'Evet | HAYIR',
	ClientFileFormAccessLevelDescription:
		'Siz ve Ekibiniz, yüklediğiniz dosyalara her zaman erişebilirsiniz. Bu dosyayı müşteriyle ve/veya ilişkileriyle paylaşmayı seçebilirsiniz.',
	ClientFileSavedSuccessSnackbar: 'Dosya kaydedildi!',
	ClientFilesPageEmptyStateText: 'Hiçbir dosya yüklenmedi',
	ClientFilesPageUploadFileButton: 'Dosyaları yükle',
	ClientHeaderBilling: 'Faturalandırma',
	ClientHeaderBillingAndReceipts: 'Faturalandırma ',
	ClientHeaderDocumentation: 'Belgeleme',
	ClientHeaderDocuments: 'Belgeler',
	ClientHeaderFile: 'Belge',
	ClientHeaderHistory: 'Tıbbi geçmiş',
	ClientHeaderInbox: 'Gelen kutusu',
	ClientHeaderNote: 'Not',
	ClientHeaderOverview: 'Genel Bakış',
	ClientHeaderProfile: 'Kişisel',
	ClientHeaderRelationship: 'İlişki',
	ClientHeaderRelationships: 'İlişkiler',
	ClientId: 'Müşteri Kimliği',
	ClientImportProcessingDescription: 'Dosya hala işleniyor. Bu tamamlandığında sizi bilgilendireceğiz.',
	ClientImportReadyForMappingDescription:
		'Dosyanızın ön işlemesini tamamladık. Bu içe aktarmayı tamamlamak için sütunları eşleştirmek ister misiniz?',
	ClientImportReadyForMappingNotificationSubject:
		'İstemci içe aktarma ön işleme tamamlandı. Dosya artık eşlemeye hazır.',
	ClientInAppMessaging: 'İstemci Uygulama içi mesajlaşma',
	ClientInfoAddField: 'Başka bir alan ekle',
	ClientInfoAddRow: 'Satır ekle',
	ClientInfoAlertMessage: 'Bu bölümde doldurulan her türlü bilgi müşteri kaydını dolduracaktır.',
	ClientInfoFormPrimaryText: 'Müşteri bilgisi',
	ClientInfoFormSecondaryText: 'İletişim ayrıntılarını toplayın',
	ClientInfoPlaceholder: `Müşteri adı, E-posta adresi, Telefon numarası
 Fiziksel adres,
 Doğum tarihi`,
	ClientInformation: 'Müşteri bilgileri',
	ClientInsuranceTabLabel: 'Sigorta',
	ClientIntakeFormsNotSupported: `Form şablonları şu anda müşteri alımları aracılığıyla desteklenmemektedir.
 Bunun yerine bunları müşteri notları olarak oluşturun ve paylaşın.`,
	ClientIntakeModalDescription:
		'Müşterinize, profilini doldurmasını, ilgili tıbbi veya sevk belgelerini yüklemesini isteyen bir giriş e-postası gönderilecektir. Onlara Müşteri Portalı erişimi verilecektir.',
	ClientIntakeModalTitle: `{name}'e giriş bilgilerini gönder.`,
	ClientIntakeSkipPasswordSuccessSnackbar: 'Başarı! Alımınız kaydedildi.',
	ClientIntakeSuccessSnackbar: 'Başarı! Alımınız kaydedildi ve bir onay e-postası gönderildi.',
	ClientIsChargedProcessingFee: 'Müşterileriniz işlem ücretini ödeyecek',
	ClientListCreateButton: 'Yeni müşteri',
	ClientListEmptyState: 'Hiçbir müşteri eklenmedi',
	ClientListPageItemArchive: 'İstemciyi kaldır',
	ClientListPageItemRemoveAccess: 'Erişimimi kaldır',
	ClientLocalizationPanelDescription: 'Müşterinin tercih ettiği dil ve zaman dilimi.',
	ClientLocalizationPanelTitle: 'Dil ve saat dilimi',
	ClientManagementAndEHR: 'Müşteri yönetimi ',
	ClientMergeResultSummaryBanner:
		'Kayıtları birleştirmek, notlar, belgeler, randevular, faturalar ve sohbetler dahil tüm müşteri verilerini tek bir yerde toplar. Devam etmeden önce doğruluğu kontrol edin.',
	ClientMergeResultSummaryTitle: 'Birleştirme Sonucu Özeti',
	ClientModalTitle: 'Yeni müşteri',
	ClientMustHaveEmaillAccessErrorText: 'E-postası olmayan Müşteriler/Kişiler',
	ClientMustHavePortalAccessErrorText: 'Müşterilerin/Kişilerin kaydolması gerekecek',
	ClientMustHaveZoomAppConnectedErrorText: `Zoom'u Ayarlar &gt; Bağlı Uygulamalar aracılığıyla bağlayın`,
	ClientNameFormat: 'Müşteri adı biçimi',
	ClientNotFormAccessLevel: 'Şu kişiler tarafından görüntülenebilir:',
	ClientNotFormAccessLevelDescription:
		'Siz ve Ekip, yayınladığınız notlara her zaman erişebilirsiniz. Bu notu müşterinizle ve/veya ilişkileriyle paylaşmayı seçebilirsiniz.',
	ClientNotRegistered: 'Kayıtlı değil',
	ClientNoteFormAddFileButton: 'Dosyaları ekle',
	ClientNoteFormChooseAClient: 'Devam etmek için bir müşteri/kişi seçin',
	ClientNoteFormContent: 'İçerik',
	ClientNoteItemDeleteConfirmationModalDescription: 'Bir kez silindikten sonra bu notu tekrar alamazsınız.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Not yayınlandı ve kilitlendi.',
	ClientNotePublishedSuccessSnackbar: 'Not yayınlandı!',
	ClientNotes: 'Müşteri notları',
	ClientNotesEmptyStateText: 'Not eklemek için müşterinin profiline gidin ve Notlar sekmesine tıklayın.',
	ClientOnboardingChoosePasswordTitle1: 'Neredeyse bitti!',
	ClientOnboardingChoosePasswordTitle2: 'Bir parola seç',
	ClientOnboardingCompleteIntake: 'Tam alım',
	ClientOnboardingConfirmationScreenText:
		'{providerName} için gereken tüm bilgileri sağladınız.	Başlangıç işlemlerine başlamak için e-posta adresinizi doğrulayın. E-posta adresinizi hemen alamazsanız, lütfen spam klasörünüzü kontrol edin.',
	ClientOnboardingConfirmationScreenTitle: 'Harika! Gelen kutunu kontrol et.',
	ClientOnboardingDashboardButton: 'Kontrol Paneline Git',
	ClientOnboardingHealthRecordsDesc1: '{providerName} ile referans mektupları, belgeler paylaşmak ister misiniz?',
	ClientOnboardingHealthRecordsDescription: 'Açıklama Ekle (isteğe bağlı)',
	ClientOnboardingHealthRecordsTitle: 'Dokümantasyon',
	ClientOnboardingPasswordRequirements: 'Gereksinimler',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimum 9 karakter gerekli',
	ClientOnboardingProviderIntroSignupButton: 'Kendi adıma kaydolun',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Bir aile üyesi için kaydolun',
	ClientOnboardingProviderIntroTitle: '{name} sizi Carepatron platformuna katılmaya davet etti.',
	ClientOnboardingRegistrationInstructions: 'Kişisel bilgilerinizi aşağıya girin.',
	ClientOnboardingRegistrationTitle: 'Öncelikle bazı kişisel ayrıntılara ihtiyacımız olacak',
	ClientOnboardingStepFormsAndAgreements: 'Formlar ve Anlaşmalar',
	ClientOnboardingStepFormsAndAgreementsDesc1: 'Lütfen {providerName} alım süreci için aşağıdaki formları doldurun',
	ClientOnboardingStepHealthDetails: 'Sağlık Detayları',
	ClientOnboardingStepPassword: 'Şifre',
	ClientOnboardingStepYourDetails: 'Detayların',
	ClientPaymentMethodDescription:
		'Bir sonraki randevu rezervasyonunuzu ve faturalandırmanızı daha hızlı ve daha güvenli hale getirmek için profilinize bir ödeme yöntemi kaydedin.',
	ClientPortal: 'Müşteri Portalı',
	ClientPortalDashboardEmptyDescription: 'Randevu geçmişiniz ve katılımınız burada gösterilecektir.',
	ClientPortalDashboardEmptyTitle:
		'Katılımınızla birlikte yaklaşan, talep edilen ve geçmiş tüm randevuları takip edin',
	ClientPreferredNotificationPanelDescription:
		'Müşterilerinizin güncellemeleri ve bildirimleri almak için tercih ettikleri yöntemi şu şekilde yönetin:',
	ClientPreferredNotificationPanelTitle: 'Tercih edilen bildirim yöntemi',
	ClientProcessingFee: 'Ödeme, ({currencyCode}) {amount} işlem ücreti içerir',
	ClientProfileAddress: 'Adres',
	ClientProfileDOB: 'Doğum tarihi',
	ClientProfileEmailHelperText: 'E-posta eklemek portal erişimi sağlar',
	ClientProfileEmailHelperTextMoreInfo:
		'Müşteriye portala erişim izni verilmesi, ekip üyelerinin notları, dosyaları ve diğer belgeleri paylaşmasına olanak tanır',
	ClientProfileId: 'Kimlik',
	ClientProfileIdentificationNumber: 'Kimlik Numarası',
	ClientRelationshipsAddClientOwnerButton: 'Müşteriyi davet et',
	ClientRelationshipsAddFamilyButton: 'Aile üyesini davet et',
	ClientRelationshipsAddStaffButton: 'Personel erişimi ekle',
	ClientRelationshipsEmptyStateText: 'Hiçbir ilişki eklenmedi',
	ClientRemovedSuccessSnackbar: 'İstemci başarıyla kaldırıldı.',
	ClientResponsibility: 'Müşteri sorumluluğu',
	ClientSavedSuccessSnackbar: 'İstemci başarıyla kaydedildi.',
	ClientTableClientName: 'Müşteri Adı',
	ClientTablePhone: 'Telefon',
	ClientTableStatus: 'Durum',
	ClientUnarchivedSuccessfulSnackbar: 'Başarıyla arşivden çıkarıldı <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} {noteTitle, select, undefined {bir notu} other {{noteTitle}}} düzenledi',
	ClientView: 'Müşteri görünümü',
	Clients: 'Müşteriler',
	ClientsTable: 'Müşteri Tablosu',
	ClinicalFormat: 'Klinik formatı',
	ClinicalPsychologist: 'Klinik Psikolog',
	Close: 'Kapalı',
	CloseImportClientsModal: 'İstemcileri içe aktarmayı iptal etmek istediğinizden emin misiniz?',
	CloseReactions: 'Yakın tepkiler',
	Closed: 'Kapalı',
	Coaching: 'Antrenörlük',
	Code: 'Kod',
	CodeErrorMessage: 'Kod gerekli',
	CodePlaceholder: 'Kod',
	Coinsurance: 'Ortak sigorta',
	Collection: 'Toplamak',
	CollectionName: 'Koleksiyon Adı',
	Collections: 'Koleksiyonlar',
	ColorAppointmentsBy: 'Renk randevuları',
	ColorTheme: 'Renk teması',
	ColourCalendarBy: 'Renk takvimi şuna göre:',
	ComingSoon: 'Yakında gelecek',
	Community: 'Toplum',
	CommunityHealthLead: 'Toplum Sağlığı Lideri',
	CommunityHealthWorker: 'Toplum Sağlığı Çalışanı',
	CommunityTemplatesSectionDescription: 'Carepatron topluluğu tarafından oluşturuldu',
	CommunityTemplatesSectionTitle: 'Toplum',
	CommunityUser: 'Topluluk Kullanıcısı',
	Complete: 'Tamamlamak',
	CompleteAndLock: 'Tamamla ve kilitle',
	CompleteSetup: 'Kurulumu Tamamla',
	CompleteSetupSuccessDescription: `Carepatron'da ustalaşmaya yönelik bazı önemli adımları tamamladınız.`,
	CompleteSetupSuccessDescription2:
		'Uygulamanızı optimize etmenin ve müşterilerinizi desteklemenin daha fazla yolunu keşfedin.',
	CompleteSetupSuccessTitle: 'Başarı! Harikalar yaratıyorsun!',
	CompleteStripeSetup: 'Stripe kurulumunu tamamlayın',
	Completed: 'Tamamlandı',
	ComposeSms: 'SMS yaz',
	ComputerSystemsAnalyst: 'Bilgisayar Sistemleri Analisti',
	Confirm: 'Onaylamak',
	ConfirmDeleteAccountDescription:
		'Hesabınızı silmek üzeresiniz. Bu işlem geri alınamaz. Devam etmek istiyorsanız lütfen aşağıdan onaylayın.',
	ConfirmDeleteActionDescription: 'Bu eylemi silmek istediğinizden emin misiniz? Bu geri alınamaz',
	ConfirmDeleteAutomationDescription: 'Bu otomasyonu silmek istediğinizden emin misiniz? Bu eylem geri alınamaz.',
	ConfirmDeleteScheduleDescription:
		'**{scheduleName}** programını silmek, programınızı programlarınızdan kaldıracak ve çevrimiçi hizmetlerinizin kullanılabilirliğini etkileyebilir. Bu işlem geri alınamaz.',
	ConfirmDraftResponseContinue: 'Yanıtla devam et',
	ConfirmDraftResponseDescription:
		'Bu sayfayı kapatırsanız yanıtınız taslak olarak kalacaktır. İstediğiniz zaman geri dönüp devam edebilirsiniz.',
	ConfirmDraftResponseSubmitResponse: 'Yanıtı gönder',
	ConfirmDraftResponseTitle: 'Yanıtınız gönderilmedi',
	ConfirmIfUserIsClientDescription: `Doldurduğunuz kayıt formu Sağlayıcılar (yani sağlık ekipleri/kuruluşu) içindir.
 Eğer bu bir hataysa &quot;Müşteri olarak devam et&quot;i seçebilirsiniz, biz de size müşteri portalınızın kurulumunu yaptırırız`,
	ConfirmIfUserIsClientNoButton: 'Sağlayıcı olarak kaydolun',
	ConfirmIfUserIsClientTitle: 'Görünüşe göre bir müşterisin',
	ConfirmIfUserIsClientYesButton: 'Müşteri olarak devam et',
	ConfirmKeepSeparate: 'Ayrı tutmayı onayla',
	ConfirmMerge: 'Birleştirmeyi onayla',
	ConfirmPassword: 'Şifreyi Onayla',
	ConfirmRevertClaim: 'Evet, durumu geri al',
	ConfirmSignupAccessCode: 'Onay kodu',
	ConfirmSignupButtom: 'Onaylamak',
	ConfirmSignupDescription: 'Lütfen e-posta adresinizi ve size gönderdiğimiz onay kodunu girin.',
	ConfirmSignupSubTitle: 'Spam klasörünü kontrol edin - e-posta ulaşmadıysa',
	ConfirmSignupSuccessSnackbar:
		'Harika, hesabınızı onayladık! Artık e-posta adresinizi ve şifrenizi kullanarak giriş yapabilirsiniz',
	ConfirmSignupTitle: 'Hesabı Onaylayın',
	ConfirmSignupUsername: 'E-posta',
	ConfirmSubscriptionUpdate: 'Aboneliği Onayla {price} {isMonthly, select, true {aylık} other {yıllık}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Müşteriler silindikten sonra artık onların bilgilerine erişemezsiniz.',
	ConfirmationModalBulkDeleteClientsTitleId: '{count, plural, one {# müşteri} other {# müşteriler}} silinsin mi?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Kişiler silindikten sonra artık onların bilgilerine erişemezsiniz.',
	ConfirmationModalBulkDeleteContactsTitleId: '{count, plural, one {# kişi} other {# kişiler}} silinsin mi?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Bu kalıcı bir eylemdir. Ekip üyeleri silindikten sonra artık onların bilgilerine erişemezsiniz.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'{count, plural, one {# ekip üyesi} other {# ekip üyeleri}} silinsin mi?',
	ConfirmationModalCloseOnGoingTranscription:
		'Bu notu kapatmak devam eden tüm transkripsiyonları sonlandıracaktır. Devam etmek istediğinizden emin misiniz?',
	ConfirmationModalDeleteClientField:
		'Bu kalıcı bir eylemdir. Alan silindikten sonra kalan istemcilerinizden artık erişilemez.',
	ConfirmationModalDeleteSectionMessage:
		'Silindikten sonra bu bölümdeki tüm sorular kaldırılacaktır. Bu işlem geri alınamaz.',
	ConfirmationModalDeleteService:
		'Bu kalıcı bir eylemdir. Hizmet silindikten sonra artık çalışma alanınızdan erişilemez.',
	ConfirmationModalDeleteServiceGroup:
		'Bir koleksiyonun silinmesi gruptaki tüm hizmetleri kaldıracak ve hizmet listenize geri dönecektir. Bu işlem geri alınamaz.',
	ConfirmationModalDeleteTranscript: 'Transkripti silmek istediğinizden emin misiniz?',
	ConfirmationModalDescriptionDeleteClient: 'Müşteri silindikten sonra artık müşteri bilgilerine erişemezsiniz.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Erişiminizi kaldırdığınızda artık müşteri bilgilerini görüntüleyemezsiniz.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Profilleri silinmeyecek, yalnızca bu müşterinin ilişkisi nedeniyle kaldırılacak.',
	ConfirmationModalDescriptionRemoveStaff: 'Bu kişiyi sağlayıcıdan çıkarmak istediğinizden emin misiniz?',
	ConfirmationModalEndSession: 'Oturumu sonlandırmak istediğinizden emin misiniz?',
	ConfirmationModalTitle: 'Emin misin?',
	Confirmed: 'Onaylanmış',
	ConflictTimezoneWarningMessage: 'Birden fazla saat dilimi nedeniyle çakışmalar meydana gelebilir',
	Connect: 'Bağlamak',
	ConnectExistingClientOrContact: 'Yeni müşteri/kişi oluştur',
	ConnectInboxGoogleDescription: 'Gmail hesabı veya Google grup listesi ekleyin',
	ConnectInboxMicrosoftDescription: 'Outlook, Office365 veya Exchange hesabı ekleme',
	ConnectInboxModalDescription:
		'Tüm iletişimlerinizi tek bir merkezi yerden sorunsuz bir şekilde göndermek, almak ve takip etmek için uygulamalarınızı bağlayın.',
	ConnectInboxModalExistingDescription:
		'Yapılandırma sürecini kolaylaştırmak için bağlı uygulama ayarlarınızdaki mevcut bağlantıyı kullanın.',
	ConnectInboxModalExistingTitle: `Carepatron'da mevcut bağlı uygulama`,
	ConnectInboxModalTitle: 'Gelen kutusunu bağla',
	ConnectToStripe: `Stripe'a bağlan`,
	ConnectZoom: 'Yakınlaştırmayı Bağla',
	ConnectZoomModalDescription: `Carepatron'un randevularınız için görüntülü aramaları yönetmesine izin verin.`,
	ConnectedAppDisconnectedNotificationSubject: '{account} hesabına bağlantımız kesildi. Lütfen yeniden bağlanın.',
	ConnectedAppSyncDescription: `Bağlı uygulamaları yöneterek doğrudan Carepatron'dan üçüncü taraf takvimlerinde etkinlikler oluşturun.`,
	ConnectedApps: 'Bağlı uygulamalar',
	ConnectedAppsGMailDescription: 'Gmail hesapları veya Google grup listesini ekle',
	ConnectedAppsGoogleCalendarDescription: 'Takvim hesapları veya Google grup listesini ekle',
	ConnectedAppsGoogleDescription: 'Gmail hesabınızı ekleyin ve Google takvimlerini senkronize edin',
	ConnectedAppsMicrosoftDescription: 'Outlook, Office365 veya Exchange hesabı ekleme',
	ConnectedCalendars: 'Bağlı Takvimler',
	ConsentDocumentation: 'Formlar ve anlaşmalar',
	ConsentDocumentationPublicTemplateError:
		'Güvenlik nedeniyle yalnızca ekibinizin (herkese açık olmayan) şablonlarını seçebilirsiniz.',
	ConstructionWorker: 'İnşaat işçisi',
	Consultant: 'Danışman',
	Contact: 'Temas etmek',
	ContactAccessTypeHelperText: 'Aile yöneticilerinin bilgileri güncellemesine izin verir',
	ContactAccessTypeHelperTextMoreInfo:
		'Bu, {clientFirstName} hakkında notlar/belgeler paylaşmanıza olanak sağlayacaktır.',
	ContactAddressLabelBilling: 'Faturalama',
	ContactAddressLabelHome: 'Ev',
	ContactAddressLabelOthers: 'Diğerleri',
	ContactAddressLabelWork: 'İş',
	ContactChangeConfirmation:
		'Fatura kişisini değiştirmek, <mark>{contactName}</mark> ile ilgili tüm satır öğelerini kaldıracaktır',
	ContactDetails: 'İletişim detayları',
	ContactEmailLabelOthers: 'Diğerleri',
	ContactEmailLabelPersonal: 'Kişisel',
	ContactEmailLabelSchool: 'Okul',
	ContactEmailLabelWork: 'İş',
	ContactInformation: 'İletişim bilgileri',
	ContactInformationText: 'İletişim bilgileri',
	ContactListCreateButton: 'Yeni bağlantı',
	ContactName: 'İletişim adı',
	ContactPhoneLabelHome: 'Ev',
	ContactPhoneLabelMobile: 'Mobil',
	ContactPhoneLabelSchool: 'Okul',
	ContactPhoneLabelWork: 'İş',
	ContactRelationship: 'İletişim ilişkisi',
	ContactRelationshipFormAccessType: 'Paylaşılan bilgilere erişim izni ver',
	ContactRelationshipGrantAccessInfo: 'Bu, notları ve belgeleri paylaşmanıza izin verecek',
	ContactSupport: 'Destek ekibiyle iletişime geçin',
	Contacts: 'Kişiler',
	ContainerIdNotSet: 'Konteyner kimliği ayarlanmadı',
	Contemporary: 'Modern',
	Continue: 'Devam etmek',
	ContinueDictating: 'Dikte etmeye devam et',
	ContinueEditing: 'Düzenlemeye devam et',
	ContinueImport: 'İçe aktarmaya devam et',
	ContinueTranscription: 'Transkripsiyonu sürdür',
	ContinueWithApple: `Apple'la devam et`,
	ContinueWithGoogle: 'Google ile devam',
	Conversation: 'Konuşma',
	Copay: 'Ortak ödeme',
	CopayOrCoinsurance: 'Ortak ödeme veya Eş sigorta',
	Copayment: 'Ortak ödeme',
	CopiedToClipboard: 'Panoya kopyalandı',
	Copy: 'Kopyala',
	CopyAddressSuccessSnackbar: 'Panoya adres kopyalandı',
	CopyCode: 'Kopyala kod',
	CopyCodeToClipboardSuccess: 'Kopyalanan kod',
	CopyEmailAddressSuccessSnackbar: 'E-posta adresi panoya kopyalandı',
	CopyLink: 'Bağlantıyı kopyala',
	CopyLinkForCall: 'Bu aramayı paylaşmak için bu bağlantıyı kopyalayın:',
	CopyLinkSuccessSnackbar: 'Bağlantı panoya kopyalandı',
	CopyMeetingLink: 'Toplantı bağlantısını kopyala',
	CopyPaymentLink: 'Ödeme bağlantısını kopyala',
	CopyPhoneNumberSuccessSnackbar: 'Telefon numarası panoya kopyalandı',
	CopyTemplateLink: 'Bağlantıyı şablona kopyala',
	CopyTemplateLinkSuccess: 'Bağlantı panoya kopyalandı',
	CopyToClipboardError: 'Panoya kopyalanamadı. Lütfen tekrar deneyin.',
	CopyToTeamTemplates: 'Ekip şablonlarına kopyala',
	CopyToWorkspace: 'Çalışma alanına kopyala',
	Cosmetologist: 'Güzellik uzmanı',
	Cost: 'Maliyet',
	CostErrorMessage: 'Maliyet gerekli',
	Counseling: 'Danışmanlık',
	Counselor: 'Danışman',
	Counselors: 'Danışmanlar',
	CountInvoicesAdded: '{count, plural, one {# Fatura eklendi} other {# Faturalar eklendi}}',
	CountNotesAdded: '{count, plural, one {# Not eklendi} other {# Notlar eklendi}}',
	CountSelected: '{count} seçili',
	CountTimes: '{count} kere',
	Country: 'Ülke',
	Cousin: 'Kuzen',
	CoverageType: 'Kapsama türü',
	Covered: 'Kapalı',
	Create: 'Yaratmak',
	CreateANewClient: 'Yeni bir müşteri oluştur',
	CreateAccount: 'Hesap oluşturmak',
	CreateAndSignNotes: 'Müşterilerle not oluşturun ve imzalayın',
	CreateAvailabilityScheduleFailure: 'Yeni kullanılabilirlik planı oluşturulamadı',
	CreateAvailabilityScheduleSuccess: 'Yeni kullanılabilirlik planı başarıyla oluşturuldu',
	CreateBillingItems: 'Faturalama öğelerini oluştur',
	CreateCallFormButton: 'Aramayı başlat',
	CreateCallFormInviteOnly: 'Yalnızca davet et',
	CreateCallFormInviteOnlyMoreInfo:
		'Yalnızca bu görüşmeye davet edilen kişiler katılabilir. Bu aramayı başkalarıyla paylaşmak için bunun işaretini kaldırmanız ve bağlantıyı bir sonraki sayfaya kopyalayıp yapıştırmanız yeterlidir.',
	CreateCallFormRecipients: 'Alıcılar',
	CreateCallFormRegion: 'Barındırma bölgesi',
	CreateCallModalAddClientContactSelectorLabel: 'Müşteri iletişim bilgileri',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Müşteri adına göre ara',
	CreateCallModalAddStaffSelectorLabel: 'Ekip üyeleri (isteğe bağlı)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Personel adına göre ara',
	CreateCallModalDescription: `Bir çağrı başlatın ve personeli ve/veya kişileri davet edin. Alternatif olarak, bu çağrının Carepatron'a sahip herkesle paylaşılabilmesini sağlamak için &quot;Özel&quot; kutusunun işaretini kaldırabilirsiniz.`,
	CreateCallModalTitle: 'Arama başlatma',
	CreateCallModalTitleLabel: 'Başlık (isteğe bağlı)',
	CreateCallNoPersonIdToolTip: 'Aramalara yalnızca portal erişimi olan kişiler/müşteriler katılabilir',
	CreateClaim: 'Talep Oluştur',
	CreateClaimCompletedMessage: 'Talebiniz oluşturuldu.',
	CreateClientModalTitle: 'Yeni müşteri',
	CreateContactModalTitle: 'Yeni bağlantı',
	CreateContactRelationshipButton: 'İlişki ekle',
	CreateContactSelectorDefaultOption: '  Temas kurmak',
	CreateContactWithRelationshipFormAccessType: 'Paylaşılan bilgilere erişim izni verin ',
	CreateDocumentDnDPrompt: 'Dosyaları yüklemek için sürükleyip bırakın',
	CreateDocumentSizeLimit: 'Dosya başına boyut sınırlaması {size}MB. Toplamda {total} dosya.',
	CreateFreeAccount: 'Ücretsiz hesap oluştur',
	CreateInvoice: 'Fatura oluşturmak',
	CreateLink: 'Bağlantı oluştur',
	CreateNew: 'Yeni oluşturmak',
	CreateNewAppointment: 'Yeni randevu oluştur',
	CreateNewClaim: 'Yeni bir talep oluştur',
	CreateNewClaimForAClient: 'Bir müşteri için yeni bir talep oluşturun.',
	CreateNewClient: 'Yeni müşteri oluştur',
	CreateNewConnection: 'Yeni bağlantı',
	CreateNewContact: 'Yeni kişi yarat',
	CreateNewField: 'Yeni alan oluştur',
	CreateNewLocation: 'Yeni konum',
	CreateNewService: 'Yeni hizmet oluştur',
	CreateNewServiceGroupFailure: 'Yeni koleksiyon oluşturulamadı',
	CreateNewServiceGroupMenu: 'Yeni koleksiyon',
	CreateNewServiceGroupSuccess: 'Yeni koleksiyon başarıyla oluşturuldu',
	CreateNewServiceMenu: 'Yeni hizmet',
	CreateNewTeamMember: 'Yeni ekip üyesi oluştur',
	CreateNewTemplate: 'Yeni şablon',
	CreateNote: 'Not oluştur',
	CreateSuperbillReceipt: 'Yeni süper fatura',
	CreateSuperbillReceiptSuccess: 'Superbill makbuzu başarıyla oluşturuldu',
	CreateTemplateFolderSuccessMessage: '{folderTitle} başarıyla oluşturuldu.',
	Created: 'Oluşturuldu',
	CreatedAt: 'Oluşturuldu {timestamp}',
	Credit: 'Kredi',
	CreditAdded: 'Kredi uygulandı',
	CreditAdjustment: 'Kredi ayarlaması',
	CreditAdjustmentReasonHelperText: 'Bu dahili bir nottur ve müşteriniz tarafından görülmeyecektir.',
	CreditAdjustmentReasonPlaceholder:
		'Faturalandırılabilir işlemleri incelerken düzenleme nedeni eklemek yardımcı olabilir',
	CreditAmount: '{amount} KK',
	CreditBalance: 'Kredi bakiyesi',
	CreditCard: 'Kredi kartı',
	CreditCardExpire: 'Son Kullanma Tarihi {exp_month}/{exp_year}',
	CreditCardNumber: 'Kredi Kartı Numarası',
	CreditDebitCard: 'Kart',
	CreditIssued: 'Verilen Kredi',
	CreditsUsed: 'Kullanılan krediler',
	Crop: 'Mahsul',
	Currency: 'Para birimi',
	CurrentCredit: 'Mevcut kredi',
	CurrentEventTime: 'Mevcut etkinlik zamanı',
	CurrentPlan: 'Mevcut plan',
	Custom: 'Gelenek',
	CustomRange: 'Özel aralık',
	CustomRate: 'Özel ücret',
	CustomRecurrence: 'Özel Tekrar',
	CustomServiceAvailability: 'Servis Uygunluğu',
	CustomerBalance: 'Müşteri bakiyesi',
	CustomerName: 'Müşteri adı',
	CustomerNameIsRequired: 'Müşteri adı gerekli',
	CustomerServiceRepresentative: 'Müşteri Hizmetleri Temsilcisi',
	CustomiseAppointments: 'Randevuları özelleştirin',
	CustomiseBookingLink: 'Rezervasyon seçeneklerini özelleştirin',
	CustomiseBookingLinkServicesInfo: 'Müşteriler yalnızca rezervasyon yapılabilir hizmetleri seçebilir',
	CustomiseBookingLinkServicesLabel: 'Hizmetler',
	CustomiseClientRecordsAndWorkspace: 'Müşteri kayıtlarınızı ve çalışma alanınızı özelleştirin',
	CustomiseClientSettings: 'İstemci ayarlarını özelleştirin',
	Customize: 'Özelleştirmek',
	CustomizeAppearance: 'Görünüşü özelleştirme',
	CustomizeAppearanceDesc:
		'Çevrimiçi rezervasyon görünümünüzü markanıza uyacak şekilde özelleştirin ve hizmetlerinizin müşterilere gösterilme biçimini optimize edin.',
	CustomizeClientFields: 'İstemci alanlarını özelleştirin',
	CustomizeInvoiceTemplate: 'Fatura şablonunu özelleştirin',
	CustomizeInvoiceTemplateDescription: 'Markanızı yansıtan profesyonel faturaları zahmetsizce oluşturun.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX gerekli',
	Daily: 'Günlük',
	DanceTherapist: 'Dans Terapisti',
	DangerZone: 'Tehlikeli bölge',
	Dashboard: 'Gösterge Paneli',
	Date: 'Tarih',
	DateAndTime: 'Tarih ',
	DateDue: 'Vadesi',
	DateErrorMessage: 'Tarih gerekli',
	DateFormPrimaryText: 'Tarih',
	DateFormSecondaryText: 'Bir tarih seçici arasından seçim yapın',
	DateIssued: 'Yayınlanma tarihi',
	DateOfPayment: 'Ödeme günü',
	DateOfService: 'Hizmet tarihi',
	DateOverride: 'Tarih geçersiz kılma',
	DateOverrideColor: 'Tarih geçersiz kılma rengi',
	DateOverrideInfo:
		'Tarih geçersiz kılmaları, uygulayıcıların düzenli programları geçersiz kılarak belirli tarihler için uygunluk durumlarını manuel olarak ayarlamalarına olanak tanır.',
	DateOverrideInfoBanner:
		'Bu zaman aralıklarında yalnızca bu tarihin geçersiz kılınması için belirtilen hizmetler için rezervasyon yapılabilir; başka çevrimiçi rezervasyona izin verilmez.',
	DateOverrides: 'Tarih geçersiz kılmaları',
	DatePickerFormPrimaryText: 'Tarih',
	DatePickerFormSecondaryText: 'Bir tarih seçin',
	DateRange: 'Tarih aralığı',
	DateRangeFormPrimaryText: 'Tarih aralığı',
	DateRangeFormSecondaryText: 'Bir tarih aralığı seçin',
	DateReceived: 'Alındığı tarih',
	DateSpecificHours: 'Tarihe özel saatler',
	DateSpecificHoursDescription:
		'Müsaitlik durumunuzun planlanan saatlerinizden farklı olduğu durumlarda veya belirli bir tarihte hizmet sunmak için tarih ekleyin.',
	DateUploaded: 'Yüklendi {date, date, medium}',
	Dates: 'Tarih',
	Daughter: 'Kız çocuğu',
	Day: 'Gün',
	DayPlural: '{count, plural, one {gün} other {günler}}',
	Days: 'Günler',
	DaysPlural: '{age, plural, one {# gün} other {# gün}}',
	DeFacto: 'Fiili',
	Deactivated: 'Deaktif edildi',
	Debit: 'Borç',
	DecreaseIndent: 'Girintiyi azalt',
	Deductibles: 'Kesintiler',
	Default: 'Varsayılan',
	DefaultBillingProfile: 'Varsayılan faturalandırma profili',
	DefaultDescription: 'Varsayılan açıklama',
	DefaultEndOfLine: 'Başka öğe yok',
	DefaultInPerson: 'Müşteri randevuları',
	DefaultInvoiceTitle: 'Varsayılan başlık',
	DefaultNotificationSubject: '{notificationType} için yeni bir bildirim aldınız.',
	DefaultPaymentMethod: 'Varsayılan Ödeme Şekli',
	DefaultService: 'Varsayılan hizmet',
	DefaultValue: 'Varsayılan',
	DefaultVideo: 'Müşteri görüntülü randevu e-postası',
	DefinedTemplateType: '{invoiceTemplate} şablonu',
	Delete: 'Silmek',
	DeleteAccountButton: 'Hesabı sil',
	DeleteAccountDescription: 'Hesabınızı platformdan silin',
	DeleteAccountPanelInfoAlert: `Profilinizi silmeden önce çalışma alanlarınızı silmelisiniz. Devam etmek için bir çalışma alanına geçin ve Ayarlar > Çalışma Alanı Ayarları'nı seçin.`,
	DeleteAccountTitle: 'Hesabı sil',
	DeleteAppointment: 'Randevuyu sil',
	DeleteAppointmentDescription: 'Bu randevuyu silmek istediğinizden emin misiniz? Daha sonra geri yükleyebilirsiniz.',
	DeleteAvailabilityScheduleFailure: 'Kullanılabilirlik planı silinemedi',
	DeleteAvailabilityScheduleSuccess: 'Kullanılabilirlik planı başarıyla silindi',
	DeleteBillable: 'Faturalandırılabilir olanı sil',
	DeleteBillableConfirmationMessage:
		'Bu faturalandırılabilir öğeyi silmek istediğinizden emin misiniz? Bu eylem geri alınamaz.',
	DeleteBillingProfileConfirmationMessage: 'Isso removerá permanentemente o perfil de cobrança.',
	DeleteCardConfirmation: 'Bu kalıcı bir eylemdir. Kart silindikten sonra artık karta erişemezsiniz.',
	DeleteCategory: 'Kategoriyi sil (değişiklikler kaydedilmediği sürece bu kalıcı değildir)',
	DeleteClientEventConfirmationDescription: 'Bu kalıcı olarak kaldırılacak.',
	DeleteClients: 'İstemcileri sil',
	DeleteCollection: 'Koleksiyonu Sil',
	DeleteColumn: 'Sütunu sil',
	DeleteConversationConfirmationDescription: 'Bu konuşmayı sonsuza kadar silin. Bu işlem geri alınamaz.',
	DeleteConversationConfirmationTitle: 'Görüşmeyi kalıcı olarak sil',
	DeleteExternalEventDescription: 'Bu randevuyu silmek istediğinizden emin misiniz?',
	DeleteFileConfirmationModalPrompt: 'Bu dosyayı bir kez sildikten sonra bir daha geri alamazsınız.',
	DeleteFolder: 'Klasörü sil',
	DeleteFolderConfirmationMessage:
		'Bu klasörü {name} silmek istediğinizden emin misiniz? Bu klasörün içindeki tüm öğeler de silinecektir. Bunu daha sonra geri yükleyebilirsiniz.',
	DeleteForever: 'Tamamen sil',
	DeleteInsurancePayerConfirmationMessage: `{payer}'yi kaldırmak onu sigorta ödeyenler listenizden silecektir. Bu eylem kalıcıdır ve geri yüklenemez.`,
	DeleteInsurancePayerFailure: 'Sigorta ödeyicisi silinemedi',
	DeleteInsurancePolicyConfirmationMessage: 'Bu, sigorta poliçesini kalıcı olarak ortadan kaldıracaktır.',
	DeleteInvoiceConfirmationDescription:
		'Bu işlem geri alınamaz. Faturayı ve onunla ilişkili tüm ödemeleri kalıcı olarak silecektir.',
	DeleteLocationConfirmation:
		'Bir konumu silmek kalıcı bir işlemdir. Bir kez sildikten sonra artık ona erişemezsiniz. Bu işlem geri alınamaz.',
	DeletePayer: 'Ödemeyi Yapan Kişiyi Sil',
	DeletePracticeWorkspace: 'Pratik çalışma alanını sil',
	DeletePracticeWorkspaceDescription: 'Bu pratik çalışma alanını kalıcı olarak sil',
	DeletePracticeWorkspaceFailedSnackbar: 'Çalışma alanı silinemedi',
	DeletePracticeWorkspaceModalCancelButton: 'Evet aboneliğimi iptal et',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Çalışma alanınızın silinmesine devam etmeden önce aboneliğinizi iptal etmeniz gerekir.',
	DeletePracticeWorkspaceModalConfirmButton: 'Evet, çalışma alanını kalıcı olarak sil',
	DeletePracticeWorkspaceModalDescription:
		'{name} çalışma alanı kalıcı olarak silinecek ve tüm ekip üyeleri erişimini kaybedecektir. Silme işlemi gerçekleşmeden önce ihtiyacınız olabilecek önemli verileri veya mesajları indirin. Bu işlem geri alınamaz.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Bu karar şu nedenlerden dolayı alınmıştır:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Sebep',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Lütfen hesabınızı neden silmek istediğinizi bize bildirin.',
	DeletePracticeWorkspaceModalTitle: 'Emin misin?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Tüm ekip üyelerinin erişimi kaldırıldı',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} başarıyla silindi',
	DeletePublicTemplateContent: 'Bu, ekibinizin şablonunu değil, yalnızca genel şablonu siler.',
	DeleteRecurringAppointmentModalTitle: 'Tekrarlanan randevuyu sil',
	DeleteRecurringEventModalTitle: 'Tekrarlanan toplantıyı sil',
	DeleteRecurringReminderModalTitle: 'Tekrarlanan hatırlatıcıyı sil',
	DeleteRecurringTaskModalTitle: 'Tekrarlanan görevi sil',
	DeleteReminderConfirmation:
		'Bu kalıcı bir eylemdir. Hatırlatıcı silindikten sonra artık ona erişemezsiniz. Yalnızca yeni randevuları etkileyecektir',
	DeleteSection: 'Bölümü sil',
	DeleteSectionInfo:
		'<strong>{section}</strong> bölümünü silmek, içindeki tüm mevcut alanları gizleyecektir. Bu işlem geri alınamaz.',
	DeleteSectionWarning: 'Temel alanlar silinemez ve mevcut bölüme **{section}** taşınacaktır.',
	DeleteServiceFailure: 'Hizmet silinemedi',
	DeleteServiceSuccess: 'Hizmet başarıyla silindi',
	DeleteStaffScheduleOverrideDescription:
		'{value} üzerindeki bu tarih geçersiz kılınması, bunu programlarınızdan kaldıracak ve çevrimiçi hizmetinizin kullanılabilirliğini değiştirebilir. Bu işlem geri alınamaz.',
	DeleteSuperbillConfirmationDescription: 'Bu işlem geri alınamaz. Superbill makbuzunu kalıcı olarak silecektir.',
	DeleteSuperbillFailure: 'Süper Fatura makbuzu silinemedi',
	DeleteSuperbillSuccess: 'Süper Fatura makbuzu başarıyla silindi',
	DeleteTaxRateConfirmationDescription: 'Bu vergi oranını silmek istediğinizden emin misiniz?',
	DeleteTemplateContent: 'Bu işlem geri alınamaz',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} başarıyla silindi',
	DeleteTemplateSuccessMessage: '{templateTitle} başarıyla silindi',
	DeleteTemplateTitle: 'Bu şablonu silmek istediğinizden emin misiniz?',
	DeleteTranscript: 'Transkripti sil',
	DeleteWorkspace: 'Çalışma alanını sil',
	Deleted: 'silindi',
	DeletedBy: 'Silindi',
	DeletedContact: 'Silinen kişi',
	DeletedOn: 'Silindi',
	DeletedStatusLabel: 'Silinmiş durum',
	DeletedUserTooltip: 'Bu istemci silindi',
	DeliveryMethod: 'Teslimat Yöntemi',
	Demo: 'Demo',
	Denied: 'Reddedildi',
	Dental: 'Diş',
	DentalAssistant: 'Diş hekimi asistanı',
	DentalHygienist: 'Diş Sağlığı',
	Dentist: 'Dişçi',
	Dentists: 'Diş Hekimleri',
	Description: 'Tanım',
	DescriptionMustNotExceed: 'Açıklama {max} karakterden fazla olmamalıdır.',
	DetailDurationWithStaff: '{duration} dk{staffName, select, null {} other { {staffName} ile}}',
	Details: 'Detaylar',
	Devices: 'Cihazlar',
	Diagnosis: 'Tanı',
	DiagnosisAndBillingItems: 'Tanı ',
	DiagnosisCode: 'Teşhis kodu',
	DiagnosisCodeErrorMessage: 'Teşhis kodu gerekli',
	DiagnosisCodeSelectorPlaceholder: 'ICD-10 teşhis kodlarından arayın ve ekleyin',
	DiagnosisCodeSelectorTooltip:
		'Teşhis kodları, sigorta geri ödemesi için süper fatura tahsilatlarını otomatikleştirmek için kullanılır',
	DiagnosticCodes: 'Teşhis kodları',
	Dictate: 'Dikte',
	DictatingIn: 'Dikte etmek',
	Dictation: 'Dikte',
	DidNotAttend: 'Katılmadı',
	DidNotComplete: 'Tamamlanmadı',
	DidNotProviderEnoughValue: 'Yeterli değeri sağlamadı',
	DidntProvideEnoughValue: 'Yeterli değeri sağlamadı',
	DieteticsOrNutrition: 'Diyetetik veya beslenme',
	Dietician: 'Diyetisyen',
	Dieticians: 'Diyetisyenler',
	Dietitian: 'Diyetisyen',
	DigitalSign: 'Burayı imzalayın:',
	DigitalSignHelp: '(Çizmek için tıklayın/aşağı basın)',
	DirectDebit: 'Otomatik ödeme',
	DirectTextLink: 'Doğrudan metin bağlantısı',
	Disable: 'Devre dışı bırakmak',
	DisabledEmailInfo: 'Hesabınız bizim tarafımızdan yönetilmediğinden e-posta adresinizi güncelleyemiyoruz',
	Discard: 'At',
	DiscardChanges: 'Değişiklikleri gözardı et',
	DiscardDrafts: 'Taslakları atın',
	Disconnect: 'Bağlantıyı kes',
	DisconnectAppConfirmation: 'Bu uygulamanın bağlantısını kesmek istiyor musunuz?',
	DisconnectAppConfirmationDescription: 'Bu uygulamayı bağlantısını kesmek istediğinizden emin misiniz?',
	DisconnectAppConfirmationTitle: 'Uygulamayı Sonlandır',
	Discount: 'İndirim',
	DisplayCalendar: `Carepatron'da görüntüle`,
	DisplayName: 'Ekran adı',
	DisplayedToClients: 'Müşterilere gösteriliyor',
	DiversionalTherapist: 'Yönlendirme Terapisti',
	DoItLater: 'Daha sonra yap',
	DoNotImport: 'İçe aktarma',
	DoNotSend: 'Gönderme',
	DoThisLater: 'Bunu sonra yap.',
	DoYouWantToEndSession: 'Devam etmek mi istiyorsunuz, yoksa oturumunuzu şimdi sonlandırmak mı istiyorsunuz?',
	Doctor: 'Doktor',
	Doctors: 'Doktorlar',
	DoesNotRepeat: 'Tekrarlanmıyor',
	DoesntWorkWellWithExistingTools: 'Mevcut araçlarımızla veya iş akışlarımızla iyi çalışmıyor',
	DogWalker: 'Köpek yürüteç',
	Done: 'Tamamlandı',
	DontAllowClientsToCancel: 'Müşterilerin iptal etmesine izin verme',
	DontHaveAccount: 'Hesabınız yok mu?',
	DontSend: 'Gönderme',
	Double: 'Çift',
	DowngradeTo: '{plan}’e Düşür',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Üzgünüz, çok fazla ekip üyeniz olduğundan planınızın düzeyini düşüremezsiniz. Lütfen sağlayıcınızdan bazılarını kaldırın ve tekrar deneyin.',
	Download: 'İndirmek',
	DownloadAsPdf: 'PDF olarak indir',
	DownloadERA: `ERA'yı İndir`,
	DownloadPDF: 'PDF İndir',
	DownloadTemplateFileName: 'Carepatron Geçiş Şablonu.csv',
	DownloadTemplateTileDescription:
		'Müşterilerinizi düzenlemek ve yüklemek için elektronik tablo şablonumuzu kullanın.',
	DownloadTemplateTileLabel: 'Şablonu indir',
	Downloads: '{number, plural, one {<span>#</span> İndirme} other {<span>#</span> İndirmeler}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Taslak',
	DraftResponses: 'Taslak yanıt',
	DraftSaved: 'Değişiklikler kaydedildi',
	DragAndDrop: 'sürükle ve bırak',
	DragDropText: 'Sağlık belgelerini sürükleyip bırakın',
	DragToMove: 'Taşımak için sürükleyin',
	DragToMoveOrActivate: 'Taşımak veya etkinleştirmek için sürükleyin',
	DramaTherapist: 'Drama Terapisti',
	DropdownFormFieldPlaceHolder: 'Listeden seçenekleri seçin',
	DropdownFormPrimaryText: 'Yıkılmak',
	DropdownFormSecondaryText: 'Seçenek listesinden seçim yapın',
	DropdownTextFieldError: 'Açılır seçenek metni boş olamaz',
	DropdownTextFieldPlaceholder: 'Açılır seçenek ekleme',
	Due: 'Bitiş tarihi',
	DueDate: 'Bitiş tarihi',
	Duplicate: 'Kopyalamak',
	DuplicateAvailabilityScheduleFailure: 'Kullanılabilirlik planı kopyalanamadı',
	DuplicateAvailabilityScheduleSuccess: '{name} takvimi başarıyla kopyalandı',
	DuplicateClientBannerAction: 'Gözden geçirmek',
	DuplicateClientBannerDescription:
		'Yinelenen müşteri kayıtlarının birleştirilmesi, bunları tek bir kayıtta birleştirir ve tüm benzersiz müşteri bilgilerini korur.',
	DuplicateClientBannerTitle: '{count} Tekrar eden öğe bulundu',
	DuplicateColumn: 'Sütunu kopyala',
	DuplicateContactFieldSettingErrorSnackbar: 'Yinelenen bölüm adları olamaz',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Yinelenen alan adları olamaz',
	DuplicateEmailError: 'Yinelenen e-posta',
	DuplicateHeadingName: 'Bölüm {name} zaten mevcut.',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Aynı &quot;fatura numarasına&quot; sahip bir fatura zaten mevcut.',
	DuplicateRecords: 'Yinelenen kayıtlar',
	DuplicateRecordsMinimumError: 'En az 2 kayıt seçilmelidir',
	DuplicateRecordsRequired: 'Ayırmak için en az 1 kayıt seçin',
	DuplicateServiceFailure: `**{title}**'ı çoğaltmak mümkün olmadı.`,
	DuplicateServiceSuccess: 'Başarıyla kopyalandı <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Başarıyla kopyalanan klasör',
	DuplicateTemplateSuccess: 'Şablon başarıyla kopyalandı',
	DurationInMinutes: '{duration}dk',
	Dx: 'DX',
	DxCode: 'DX kodu',
	DxCodeSelectPlaceholder: 'ICD-10 kodlarından arama ve ekleme',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Hiçbiri',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>ayarlamalar içerir</i>} other {}}',
	EarnReferralCredit: '${creditAmount} Kazan',
	Economist: 'İktisatçı',
	Edit: 'Düzenlemek',
	EditArrangements: 'Düzenlemeleri düzenle',
	EditBillTo: 'Faturayı şu adrese düzenle:',
	EditClient: 'İstemciyi Düzenle',
	EditClientFileModalDescription:
		'&quot;Görüntüleyenler&quot; onay kutularındaki seçenekleri belirleyerek bu dosyaya erişimi düzenleyin',
	EditClientFileModalTitle: 'Dosya düzenle',
	EditClientNoteModalDescription:
		'Nottaki içeriği düzenleyin. Notu kimlerin görebileceğini değiştirmek için &quot;Görüntüleyenler&quot; bölümünü kullanın.',
	EditClientNoteModalTitle: 'Notu düzenle',
	EditConnectedAppButton: 'Düzenlemek',
	EditConnections: 'Bağlantıları düzenle{account, select, null { } undefined { } other { için {account}}}',
	EditContactDetails: 'İletişim ayrıntılarını düzenleyin',
	EditContactFormIsClientLabel: 'İstemciye dönüştür',
	EditContactIsClientCheckboxWarning: 'Bir kişiyi müşteriye dönüştürme işlemi geri alınamaz',
	EditContactIsClientWanringModal:
		'Bu kişiyi Müşteriye dönüştürme işlemi geri alınamaz. Ancak tüm ilişkiler hala aynı kalacak ve artık onların notlarına, dosyalarına ve diğer belgelerine erişebileceksiniz.',
	EditContactRelationship: 'Kişi ilişkisini düzenle',
	EditDetails: 'Detayları düzenle',
	EditFileModalTitle: '{name} için dosyayı düzenle',
	EditFolder: 'Klasörü düzenle',
	EditFolderDescription: 'Klasörün adını şu şekilde değiştirin...',
	EditInvoice: 'Faturayı düzenle',
	EditInvoiceDetails: 'Fatura ayrıntılarını düzenle',
	EditLink: 'Bağlantıyı Düzenle',
	EditLocation: 'Konumunu düzenle',
	EditLocationFailure: 'Konum güncellenemedi',
	EditLocationSucess: 'Konum başarıyla güncellendi',
	EditPaymentDetails: 'Ödeme ayrıntılarını düzenle',
	EditPaymentMethod: 'Ödeme yöntemini düzenle',
	EditPersonalDetails: 'Kişisel ayrıntıları düzenle',
	EditPractitioner: 'Uygulayıcıyı Düzenle',
	EditProvider: 'Sağlayıcıyı Düzenle',
	EditProviderDetails: 'Sağlayıcı ayrıntılarını düzenleyin',
	EditRecurrence: 'Tekrarı düzenle',
	EditRecurringAppointmentModalTitle: 'Tekrarlanan randevuyu düzenle',
	EditRecurringEventModalTitle: 'Tekrarlanan toplantıyı düzenle',
	EditRecurringReminderModalTitle: 'Tekrarlanan hatırlatıcıyı düzenle',
	EditRecurringTaskModalTitle: 'Tekrarlanan görevi düzenle',
	EditRelationshipModalTitle: 'İlişkiyi düzenle',
	EditService: 'Hizmeti düzenle',
	EditServiceFailure: 'Yeni hizmet güncellenemedi',
	EditServiceGroup: 'Koleksiyonu düzenle',
	EditServiceGroupFailure: 'Koleksiyon güncellenemedi',
	EditServiceGroupSuccess: 'Koleksiyon başarıyla güncellendi',
	EditServiceSuccess: 'Yeni hizmet başarıyla güncellendi',
	EditStaffDetails: 'Personel ayrıntılarını düzenleyin',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'E-posta adresi güncellenemiyor. Lütfen yeni bir e-posta adresiyle yeni bir ekip üyesi oluşturun.',
	EditSubscriptionBilledQuantity: 'Fatura edilen miktar',
	EditSubscriptionBilledQuantityValue: '{billedUsers} ekip üyeleri',
	EditSubscriptionLimitedTimeOffer: 'Sınırlı süreli fırsat! 6 ay boyunca %50 indirim.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Ekip üyeleri eklendiğinde veya çıkarıldığında abonelik ücretiniz ayarlanacaktır.',
	EditSubscriptionUpgradeContent:
		'Hesabınız yeni plana ve fatura dönemine hemen güncellenecektir. Fiyat değişiklikleri, kaydedilmiş ödeme yönteminizden otomatik olarak tahsil edilecek veya hesabınıza aktarılacaktır.',
	EditSubscriptionUpgradePlanTitle: 'Abonelik planını yükselt',
	EditSuperbillReceipt: 'Süper faturayı düzenle',
	EditTags: 'Etiketleri düzenle',
	EditTemplate: 'Şablonu Düzenle',
	EditTemplateFolderSuccessMessage: 'Şablon klasörü başarıyla düzenlendi',
	EditValue: `{value}&#8203;'yi Düzenle`,
	Edited: 'Düzenlendi',
	Editor: 'Editör',
	EditorAlertDescription:
		'Desteklenmeyen bir format algılandı. Uygulamayı yeniden yükleyin veya destek ekibimizle iletişime geçin.',
	EditorAlertTitle: 'Bu içeriği görüntülemede sorun yaşıyoruz',
	EditorPlaceholder:
		'Müşterilerinizden gelen yanıtları yakalamak için yazmaya başlayın, bir şablon seçin veya temel bloklar ekleyin.',
	EditorTemplatePlaceholder: 'Şablon oluşturmak için yazmaya başlayın veya bileşenler ekleyin',
	EditorTemplateWithSlashCommandPlaceholder:
		'Müşteri yanıtlarını yakalamak için yazmaya başlayın veya temel bloklar ekleyin. Hızlı eylemler için eğik çizgi komutları (/) kullanın.',
	EditorWithSlashCommandPlaceholder:
		'Yazmaya başlayın, bir şablon seçin veya istemci yanıtlarını yakalamak için temel bloklar ekleyin. Hızlı eylemler için eğik çizgi komutlarını ( / ) kullanın.',
	EffectiveStartEndDate: 'Etkin başlangıç - bitiş tarihi',
	ElectricalEngineer: 'Elektrik mühendisi',
	Electronic: 'Elektronik',
	ElectronicSignature: 'Elektronik İmza',
	ElementarySchoolTeacher: 'İlkokul öğretmeni',
	Eligibility: 'Uygunluk',
	Email: 'E-posta',
	EmailAlreadyExists: 'E-posta adresi zaten mevcut',
	EmailAndSms: 'E-posta ',
	EmailBody: 'E-posta metni',
	EmailContainsIgnoredDescription:
		'Aşağıdaki e-posta şu anda yok sayılan bir gönderici/gönderen e-postası içeriyor. Devam etmek istiyor musunuz?',
	EmailInviteToPortalBody: `Merhaba {contactName},
Lütfen güvenli müşteri portalınıza giriş yapmak ve bakımınızı kolayca yönetmek için bu bağlantıyı takip edin.

Saygılarımızla,

{providerName}`,
	EmailInviteToPortalSubject: `{providerName}'e hoş geldiniz`,
	EmailInvoice: 'Faturayı e-postayla gönder',
	EmailInvoiceOverdueBody: `Merhaba {contactName},
Faturanız {invoiceNumber} vadesi geçmiş durumda.
Lütfen aşağıdaki bağlantıyı kullanarak faturanızı çevrimiçi olarak ödeyiniz.

Herhangi bir sorunuz varsa lütfen bize bildirin.

Teşekkürler,
{providerName}`,
	EmailInvoicePaidBody: `Merhaba {contactName}
Faturanız {invoiceNumber} ödendi.
Faturanızın bir kopyasını görüntülemek ve indirmek için aşağıdaki bağlantıyı takip edin.

Herhangi bir sorunuz varsa lütfen bize bildirin.

Saygılarımızla,
{providerName}`,
	EmailInvoiceProcessingBody: `Merhaba {contactName}
Faturanız {invoiceNumber} hazır.
Faturanızı görüntülemek için aşağıdaki bağlantıyı takip edin.

Herhangi bir sorunuz varsa lütfen bize bildirin.

Teşekkürler,
{providerName}`,
	EmailInvoiceUnpaidBody: `Merhaba {contactName},
Faturanız {invoiceNumber} hazır, ödeme tarihi {dueDate}.
Faturanızı çevrimiçi görüntülemek ve ödemek için lütfen aşağıdaki bağlantıyı takip edin.

Herhangi bir sorunuz varsa lütfen bize bildirin.

Saygılarımızla,
{providerName}`,
	EmailInvoiceVoidBody: `Merhaba {contactName},
Faturanız {invoiceNumber} geçersiz.
Bu faturayı görüntülemek için aşağıdaki bağlantıyı izleyin.

Herhangi bir sorunuz varsa lütfen bize bildirin.

Teşekkürler,
{providerName}`,
	EmailNotFound: 'Email bulunamadı',
	EmailNotVerifiedErrorCodeSnackbar: 'İşlem gerçekleştirilemiyor. E-posta adresinizi doğrulamanız gerekiyor.',
	EmailNotVerifiedTitle: 'E-posta adresinizi doğrulayın',
	EmailSendClientIntakeBody: `Merhaba {contactName},
{providerName} sizden bazı bilgiler sağlamanızı ve önemli belgeleri gözden geçirmenizi rica ediyor. Lütfen başlamak için aşağıdaki bağlantıyı takip edin.

Saygılarımızla,

{providerName}`,
	EmailSendClientIntakeSubject: '{providerName}’e hoş geldiniz.',
	EmailSuperbillReceipt: 'Süper faturayı e-postayla gönder',
	EmailSuperbillReceiptBody: `Merhaba {contactName},
{providerName} size geri ödeme makbuzunuzun {date} tarihli bir kopyasını gönderdi.

Bunu doğrudan sigorta şirketinize indirip gönderebilirsiniz.`,
	EmailSuperbillReceiptFailure: 'Süper Fatura makbuzu gönderilemedi',
	EmailSuperbillReceiptSubject: '{providerName} geri ödeme makbuzunu gönderdi',
	EmailSuperbillReceiptSuccess: 'Superbill makbuzu başarıyla gönderildi',
	EmailVerificationDescription: 'Hesabınızı şimdi <span>doğruluyoruz</span>',
	EmailVerificationNotification: 'Doğrulama e-postası {email} adresine gönderildi.',
	EmailVerificationSuccess: 'E-posta adresiniz başarıyla {email} olarak değiştirildi.',
	Emails: 'E-postalar',
	EmergencyContact: 'Acil iletişim',
	EmployeesIdentificationNumber: 'Çalışan kimlik numarası',
	EmploymentStatus: 'çalışma durumu',
	EmptyAgendaViewDescription: 'Görüntülenecek etkinlik yok.<mark> Şimdi randevu oluştur</mark>',
	EmptyBin: 'Boş kutu',
	EmptyBinConfirmationDescription: `Boş çöp kutusu, Silinenler'deki tüm **{total} konuşmayı** silecektir. Bu işlem geri alınamaz.`,
	EmptyBinConfirmationTitle: 'Konuşmaları sonsuza kadar sil',
	EmptyTrash: 'Çöp kutusunu boşalt',
	Enable: 'Olanak vermek',
	EnableCustomServiceAvailability: 'Hizmet kullanılabilirliğini etkinleştir',
	EnableCustomServiceAvailabilityDescription: `Örneğin İlk randevular yalnızca her gün sabah 9'dan 10'a kadar alınabilir.`,
	EndCall: 'Son Arama',
	EndCallConfirmationForCreator: 'Aramayı başlatan siz olduğunuz için bunu herkes için sonlandıracaksınız.',
	EndCallConfirmationHasActiveAttendees:
		'Aramayı bitirmek üzeresiniz ancak müşteri(ler) zaten katılmış. Siz de katılmak ister misiniz?',
	EndCallForAll: 'Aramayı herkes için sonlandır',
	EndDate: 'Bitiş tarihi',
	EndDictation: 'Dikteyi sonlandır',
	EndOfLine: 'Artık randevu yok',
	EndSession: 'Oturum sonu',
	EndTranscription: 'Transkripsiyonu sonlandır',
	Ends: 'Bitişler',
	EndsOnDate: '{date} tarihinde sona eriyor',
	Enrol: 'Kayıt Ol',
	EnrollmentRejectedSubject: '{payerName} ile kayıt işleminiz reddedildi.',
	Enrolment: 'Giriş',
	Enrolments: 'Kayıtlar',
	EnrolmentsDescription: 'Ödemeciyle sağlayıcı kayıtlarını görüntüle ve yönet.',
	EnterAName: 'İsim girin...',
	EnterFieldLabel: 'Alan etiketini girin...',
	EnterPaymentDetailsDescription:
		'Kullanıcı eklerken veya kaldırırken abonelik maliyetiniz otomatik olarak ayarlanacaktır.',
	EnterSectionName: 'Bölüm adını girin...',
	EnterSubscriptionPaymentDetails: 'Ödeme ayrıntılarını girin',
	EnvironmentalScientist: 'Çevre bilimci',
	Epidemiologist: 'Epidemiyolog',
	Eraser: 'Silgi',
	Error: 'Hata',
	ErrorBoundaryAction: 'SAYFAYI yeniden yuklemek',
	ErrorBoundaryDescription: 'Lütfen sayfayı yenileyip tekrar deneyin.',
	ErrorBoundaryTitle: 'Hata! Bir şeyler yanlış gitti',
	ErrorCallNotFound: 'Çağrı bulunamıyor. Süresi dolmuş veya yaratıcısı sonlandırmış olabilir.',
	ErrorCannotAccessCallUninvitedCode: 'Üzgünüz, bu görüşmeye davet edilmediğiniz anlaşılıyor.',
	ErrorFileUploadCustomMaxFileCount: 'Bir seferde {count} dosyadan fazla yüklenemez',
	ErrorFileUploadCustomMaxFileSize: `Dosya boyutu {mb} MB'yi geçemez`,
	ErrorFileUploadInvalidFileType: 'Potansiyel virüsler ve zararlı yazılımlar içerebilecek geçersiz dosya türü',
	ErrorFileUploadMaxFileCount: `Aynı anda 150'den fazla dosya yüklenemiyor`,
	ErrorFileUploadMaxFileSize: `Dosya boyutu 100 MB'ı aşamaz`,
	ErrorFileUploadNoFileSelected: 'Lütfen yüklenecek dosyaları seçin',
	ErrorInvalidNationalProviderId: 'Sağlanan Ulusal Sağlayıcı Kimliği geçerli değil.',
	ErrorInvalidPayerId: 'Sağlanan Ödemeci Kimliği geçerli değil',
	ErrorInvalidTaxNumber: 'Sağlanan Vergi Numarası geçerli değil',
	ErrorInviteExistingProviderStaffCode: 'Bu kullanıcı zaten çalışma alanında.',
	ErrorInviteStaffExistingUser: 'Üzgünüz, eklediğiniz kullanıcının sistemimizde zaten mevcut olduğu görülüyor.',
	ErrorOnlySingleCallAllowed: 'Aynı anda yalnızca bir çağrı yapabilirsiniz.',
	ErrorPayerNotFound: 'Ödemeyi yapan bulunamadı',
	ErrorProfilePhotoMaxFileSize: 'Yükleme başarısız! Dosya boyutu sınırına ulaşıldı - 5 MB',
	ErrorRegisteredExistingUser: 'Üzgünüz, zaten kayıtlı olduğunuz görülüyor.',
	ErrorUserSignInIncorrectCredentials: 'Geçersiz e-posta veya şifre. Lütfen tekrar deneyin.',
	ErrorUserSigninGeneric: 'Üzgünüz, bir şeyler ters gitti.',
	ErrorUserSigninUserNotConfirmed:
		'Üzgünüz, oturum açmadan önce hesabınızı onaylamanız gerekiyor. Talimatlar için gelen kutunuzu kontrol edin.',
	Errors: 'Hatalar',
	EssentialPlanInclusionFive: 'Şablon içe aktarma',
	EssentialPlanInclusionFour: '5 GB depolama alanı',
	EssentialPlanInclusionHeader: 'Her şey Ücretsiz  ',
	EssentialPlanInclusionOne: 'Otomatik ve özel hatırlatıcılar',
	EssentialPlanInclusionSix: 'Öncelikli destek',
	EssentialPlanInclusionThree: 'Video sohbet',
	EssentialPlanInclusionTwo: '2 yönlü takvim senkronizasyonu',
	EssentialSubscriptionPlanSubtitle: 'Temel bilgilerle pratiğinizi basitleştirin',
	EssentialSubscriptionPlanTitle: 'Gerekli',
	Esthetician: 'Estetisyen',
	Estheticians: 'Estetisyenler',
	EstimatedArrivalDate: 'Varış Tahmini {numberOfDaysFromNow}',
	Ethnicity: 'Etnik köken',
	Europe: 'Avrupa',
	EventColor: 'Toplantı rengi',
	EventName: 'Etkinlik adı',
	EventType: 'Etkinlik türü',
	Every: 'Her',
	Every2Weeks: 'Her 2 haftada bir',
	EveryoneInWorkspace: 'Herkes çalışma alanında',
	ExercisePhysiologist: 'Egzersiz Fizyologu',
	Existing: 'Mevcut',
	ExistingClients: 'Mevcut müşteriler',
	ExistingFolders: 'Mevcut Klasörler',
	ExpiredPromotionCode: 'Promosyon kodunun süresi doldu',
	ExpiredReferralDescription: 'Yönlendirmenin süresi doldu',
	ExpiredVerificationLink: 'Süresi dolmuş doğrulama bağlantısı',
	ExpiredVerificationLinkDescription: `Üzgünüz ama tıkladığınız doğrulama bağlantısının süresi doldu. Bağlantıya tıklamak için 24 saatten uzun süre beklediyseniz veya e-posta adresinizi doğrulamak için bağlantıyı zaten kullandıysanız bu durum meydana gelebilir.

 Lütfen e-posta adresinizi doğrulamak için yeni bir doğrulama bağlantısı isteyin.`,
	ExpiryDateRequired: 'Son kullanma tarihi gerekli',
	ExploreFeature: 'İlk önce neyi keşfetmek istersiniz?',
	ExploreOptions: 'Keşfetmek için bir veya daha fazla seçeneği seçin...',
	Export: 'İhracat',
	ExportAppointments: 'Randevuları dışa aktar',
	ExportClaims: 'İhracat talepleri',
	ExportClaimsFilename: 'Talepler {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Dosyanız bir hatadan dolayı indirilemedi.',
	ExportClientsDownloadFailureSnackbarTitle: 'İndirme başarısız oldu',
	ExportClientsFailureSnackbarDescription:
		'Lütfen daha sonra tekrar deneyin veya yardım için destek ekibiyle iletişime geçin.',
	ExportClientsFailureSnackbarTitle: 'Dışa aktarma başarısız oldu',
	ExportClientsModalDescription: `Bu veri dışa aktarma işlemi, dışa aktarılan veri miktarına bağlı olarak birkaç dakika sürebilir. İndirmeye hazır olduğunda bağlantı içeren bir e-posta bildirimi alacaksınız.

 Müşteri verilerini dışa aktarmaya devam etmek istiyor musunuz?`,
	ExportClientsModalTitle: 'Müşteri verilerini dışa aktar',
	ExportCms1500: `CMS1500'ü dışa aktar`,
	ExportContactFailedNotificationSubject: 'Verilerinizin dışa aktarımı başarısız oldu',
	ExportFailed: 'Dışa aktarma başarısız oldu',
	ExportGuide: 'İhracat rehberi',
	ExportInvoiceFileName: 'İşlemler {fromDate}-{toDate}.csv',
	ExportPayments: 'Ödemeleri Dışa Aktar',
	ExportPaymentsFilename: 'Ödemeler {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Belgeniz indirilmeye hazır.',
	ExportPrintWaitMessage: 'Belgeniz hazırlanıyor. Lütfen bekleyin...',
	ExportTextOnly: 'Yalnızca metni dışa aktar',
	ExportTransactions: 'İhracat işlemleri',
	Exporting: 'Dışa aktarma',
	ExportingData: 'Verileri dışa aktarma',
	ExtendedFamilyMember: 'Genişletilmiş aile üyesi',
	External: 'Harici',
	ExternalEventInfoBanner: 'Bu randevu senkronize edilmiş bir takvimden geliyor ve bazı öğeler eksik olabilir.',
	ExtraLarge: 'Çok büyük',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Arızalı',
	FailedToJoinTheMeeting: 'Toplantıya katılım başarısız oldu.',
	FallbackPageDescription: `Bu sayfanın mevcut olmadığı görünüyor, en son değişiklikleri almak için {refreshButton} düğmesine basmanız gerekebilir.
Aksi takdirde, lütfen Carepatron destek ekibiyle iletişime geçin.`,
	FallbackPageDescriptionUpdateButton: 'yenileme',
	FallbackPageTitle: 'Ah...',
	FamilyPlanningService: 'Aile planlaması hizmeti',
	FashionDesigner: 'Moda tasarımcısı',
	FastTrackInvoicingAndBilling: 'Faturalandırmanızı ve faturalandırmanızı hızlı takip edin',
	Father: 'Baba',
	FatherInLaw: 'Kayınpeder',
	Favorite: 'Favori',
	FeatureBannerCalendarTile1ActionLabel: 'Çevrimiçi rezervasyon • 2 dakika',
	FeatureBannerCalendarTile1Description:
		'E-posta göndermeniz, kısa mesaj göndermeniz veya web sitenize uygunluk durumu eklemeniz yeterli',
	FeatureBannerCalendarTile1Title: 'Müşterilerinizin çevrimiçi rezervasyon yapmasını sağlayın',
	FeatureBannerCalendarTile2ActionLabel: 'Hatırlatıcıları otomatikleştirin • 2 dakika',
	FeatureBannerCalendarTile2Description: 'Otomatik hatırlatıcılarla müşteri katılımını artırın',
	FeatureBannerCalendarTile2Title: 'Rezervasyona gelmemeyi azaltın',
	FeatureBannerCalendarTile3Title: 'Planlama ve İş Akışı',
	FeatureBannerCalendarTitle: 'Planlamayı kolaylaştırın',
	FeatureBannerCallsTile1ActionLabel: 'Tele sağlık aramasını başlat',
	FeatureBannerCallsTile1Description: 'Yalnızca bir bağlantıyla istemci erişimi. Oturum açma, şifre veya güçlük yok',
	FeatureBannerCallsTile1Title: 'İstediğiniz yerden video görüşmesi başlatın',
	FeatureBannerCallsTile2ActionLabel: 'Uygulamaları bağlama • 4 dakika',
	FeatureBannerCallsTile2Description: 'Tercih edilen diğer tele sağlık sağlayıcılarına sorunsuz bir şekilde bağlanın',
	FeatureBannerCallsTile2Title: 'Tele sağlık uygulamalarınızı bağlayın',
	FeatureBannerCallsTile3Title: 'Aramalar',
	FeatureBannerCallsTitle: 'Müşterilerle bağlantı kurun — Her Yerde, Her Zaman',
	FeatureBannerClientsTile1ActionLabel: 'Şimdi içe aktarın • 2 dakika',
	FeatureBannerClientsTile1Description: 'Otomatik istemci içe aktarma aracımızla hızlı bir şekilde başlayın',
	FeatureBannerClientsTile1Title: 'Çok sayıda müşteriniz mi var?',
	FeatureBannerClientsTile2ActionLabel: 'Alımı özelleştirin • 2 dakika',
	FeatureBannerClientsTile2Description: 'Giriş evraklarını ortadan kaldırın ve müşteri deneyimlerini iyileştirin',
	FeatureBannerClientsTile2Title: 'Kağıtsız çalışın',
	FeatureBannerClientsTile3Title: 'Müşteri Portalı',
	FeatureBannerClientsTitle: 'Her şey müşterilerinizle başlar',
	FeatureBannerHeader: 'Topluluk adına, Topluluk için!',
	FeatureBannerInvoicesTile1ActionLabel: 'Ödemeleri otomatikleştirin • 2 dakika',
	FeatureBannerInvoicesTile1Description: 'Otomatik ödemelerle garip konuşmalardan kaçının',
	FeatureBannerInvoicesTile1Title: '2 kat daha hızlı ödeme alın',
	FeatureBannerInvoicesTile2ActionLabel: 'Nakit akışını takip etme • 2 dakika',
	FeatureBannerInvoicesTile2Description: 'Ödenmemiş faturaları azaltın ve gelirinizi takip edin',
	FeatureBannerInvoicesTile2Title: 'Gelirinizi sorunsuz bir şekilde takip edin',
	FeatureBannerInvoicesTile3Title: 'Faturalandırma ve Ödemeler',
	FeatureBannerInvoicesTitle: 'Dert edecek bir şey daha azaldı',
	FeatureBannerSubheader:
		'Ekibimiz ve topluluğumuz tarafından hazırlanan Carepatron şablonları. Yeni kaynakları deneyin veya kendinizinkini paylaşın!',
	FeatureBannerTeamTile1ActionLabel: 'Şimdi davet et',
	FeatureBannerTeamTile1Description: 'Ekip üyelerini hesabınıza davet edin ve işbirliğini kolaylaştırın',
	FeatureBannerTeamTile1Title: 'Ekibinizi bir araya getirin',
	FeatureBannerTeamTile2ActionLabel: 'Kullanılabilirliği ayarlayın • 2 dakika',
	FeatureBannerTeamTile2Description: 'Çifte rezervasyonu önlemek için ekibinizin müsaitlik durumunu yönetin',
	FeatureBannerTeamTile2Title: 'Müsaitlik durumunuzu ayarlayın',
	FeatureBannerTeamTile3ActionLabel: 'İzinleri ayarlayın • 2 dakika',
	FeatureBannerTeamTile3Description: 'Uyumluluk için hassas verilere ve araçlara erişimi kontrol edin',
	FeatureBannerTeamTile3Title: 'İzinleri ve erişimi özelleştirin',
	FeatureBannerTeamTitle: 'Büyük hiçbir şey tek başına başarılmaz',
	FeatureBannerTemplatesTile1ActionLabel: 'Kütüphaneyi keşfedin • 2 dakika',
	FeatureBannerTemplatesTile1Description:
		'Özelleştirilebilir kaynaklardan oluşan harika bir kitaplık arasından seçim yapın ',
	FeatureBannerTemplatesTile1Title: 'İş yükünüzü azaltın',
	FeatureBannerTemplatesTile2ActionLabel: 'Şimdi gönder • 2 dakika',
	FeatureBannerTemplatesTile2Description: 'Tamamlanmaları için müşterilere güzel şablonlar gönderin',
	FeatureBannerTemplatesTile2Title: 'Belgelemeyi eğlenceli hale getirin',
	FeatureBannerTemplatesTile3Title: 'Şablonlar',
	FeatureBannerTemplatesTitle: 'Kesinlikle her şey için şablonlar',
	FeatureLimitBannerDescription: `Şimdi yükseltin ve {featureName}'leri kesintisiz olarak oluşturmaya ve yönetmeye devam edin ve Carepatron'dan en iyi şekilde yararlanın!`,
	FeatureLimitBannerTitle: '{featureName} limitinize {percentage}% kaldınız.',
	FeatureRequiresUpgrade: 'Bu özellik, yükseltme gerektirir.',
	Fee: 'Ücret',
	Female: 'Dişi',
	FieldLabelTooltip: '{isHidden, select, true {Göster} other {Gizle}} alan etiketi',
	FieldName: 'Alan adı',
	FieldOptionsFirstPart: 'İlk kelime',
	FieldOptionsMiddlePart: 'Orta kelimeler',
	FieldOptionsSecondPart: 'Son söz',
	FieldOptionsWholeField: 'Bütün alan',
	FieldType: 'Alan türü',
	Fields: 'Alanlar',
	File: 'Dosya',
	FileDownloaded: '<strong>{fileName}</strong> indirildi',
	FileInvalidType: 'Dosya desteklenmiyor.',
	FileNotFound: 'Dosya bulunamadı',
	FileNotFoundDescription: 'Aramakta olduğunuz dosya mevcut değil veya silinmiş olabilir',
	FileTags: 'Dosya etiketi',
	FileTagsHelper: 'Etiketler tüm dosyalara uygulanacak',
	FileTooLarge: 'Dosya çok büyük.',
	FileTooSmall: 'Dosya çok küçük.',
	FileUploadComplete: 'Tamamlamak',
	FileUploadFailed: 'Arızalı',
	FileUploadInProgress: 'Yükleniyor',
	FileUploadedNotificationSubject: '{actorProfileName} bir dosya yükledi',
	Files: 'Dosyalar',
	FillOut: 'Doldur',
	Filter: 'Filtre',
	FilterBy: 'Tarafından filtre',
	FilterByAmount: 'Miktara göre filtrele',
	FilterByClient: 'Müşteriye göre filtrele',
	FilterByLocation: 'Konuma göre filtrele',
	FilterByService: 'Hizmete göre filtrele',
	FilterByStatus: 'Duruma göre filtrele',
	FilterByTags: 'Etiketlere göre filtrele',
	FilterByTeam: 'Takıma göre filtrele',
	Filters: 'Filtreler',
	FiltersAppliedToView: 'Filtreler görünüme uygulandı',
	FinalAppointment: 'Son Randevu',
	FinalizeImport: 'İthalatı Sonlandır',
	FinancialAnalyst: 'Finansal Analist',
	Finish: 'Sona ermek',
	Firefighter: 'İtfaiyeci',
	FirstName: 'İlk adı',
	FirstNameLastInitial: 'Ad, soyadının baş harfi',
	FirstPerson: '1. kişi',
	FolderName: 'Klasör adı',
	Folders: 'Klasörler',
	FontFamily: 'Font ailesi',
	ForClients: 'Müşteriler için',
	ForClientsDetails: 'Bakım veya sağlıkla ilgili hizmetler alıyorum',
	ForPractitioners: 'Uygulayıcılar için',
	ForPractitionersDetails: 'Uygulamanızı yönetin ve büyütün',
	ForgotPasswordConfirmAccessCode: 'Onay kodu',
	ForgotPasswordConfirmNewPassword: 'Yeni Şifre',
	ForgotPasswordConfirmPageDescription:
		'Lütfen e-posta adresinizi, yeni şifrenizi ve size az önce gönderdiğimiz onay kodunu girin.',
	ForgotPasswordConfirmPageTitle: 'Şifreyi yenile',
	ForgotPasswordPageButton: 'Sıfırlama Bağlantısı Gönder',
	ForgotPasswordPageDescription: 'E-postanızı girin, şifrenizi sıfırlamanız için size bir bağlantı göndereceğiz.',
	ForgotPasswordPageTitle: 'Unutulan Şifre',
	ForgotPasswordSuccessPageDescription: 'Sıfırlama bağlantınız için gelen kutunuzu kontrol edin.',
	ForgotPasswordSuccessPageTitle: 'Bağlantıyı sıfırlayın gönderildi!',
	Form: 'Form',
	FormAnswersSentToEmailNotification: 'Yanıtlarınızın bir kopyasını şu adrese gönderdik:',
	FormBlocks: 'Form blokları',
	FormFieldAddOption: 'Seçenek ekle',
	FormFieldAddOtherOption: '&quot;Diğer&quot;i ekle',
	FormFieldOptionPlaceholder: 'Seçenek {index}',
	FormStructures: 'Form yapıları',
	Format: 'Biçim',
	FormatLinkButtonColor: 'Düğme rengi',
	Forms: 'Formlar',
	FormsAndAgreementsValidationMessage:
		'Alım sürecine devam etmek için tüm formların ve anlaşmaların tamamlanması gerekir.',
	FormsCategoryDescription: 'Hasta bilgilerini toplamak ve düzenlemek için',
	Frankfurt: `Frankfurt'ta`,
	Free: 'Özgür',
	FreePlanInclusionFive: 'Otomatik faturalama ',
	FreePlanInclusionFour: 'Müşteri portalı',
	FreePlanInclusionHeader: 'Başlayın',
	FreePlanInclusionOne: 'Sınırsız müşteri',
	FreePlanInclusionSix: 'Canlı destek',
	FreePlanInclusionThree: '1 GB depolama alanı',
	FreePlanInclusionTwo: 'Tele sağlık',
	FreeSubscriptionPlanSubtitle: 'Herkese ücretsiz',
	FreeSubscriptionPlanTitle: 'Özgür',
	Friday: 'Cuma',
	From: 'İtibaren',
	FullName: 'Ad Soyad',
	FunctionalMedicineOrNaturopath: 'Fonksiyonel Tıp veya Naturopat',
	FuturePaymentsAuthoriseProvider: `Kaydedilen ödemeyi gelecekte kullanmak için {provider}'a izin ver`,
	FuturePaymentsSavePaymentMethod: 'Gelecekteki ödemeler için {paymentMethod} kaydet',
	GST: 'GST',
	Gender: 'Cinsiyet',
	GeneralAvailability: 'Genel kullanılabilirlik',
	GeneralAvailabilityDescription:
		'Düzenli olarak müsait olduğunuz zamanı ayarlayın. Müşteriler yalnızca uygun saatlerde hizmetleriniz için rezervasyon yapabilecektir.',
	GeneralAvailabilityDescription2:
		'Çevrimiçi rezervasyon uygunluğunuzu belirlemek için müsaitlik durumunuza ve belirli zamanlarda istediğiniz hizmet tekliflerine göre programlar oluşturun.',
	GeneralAvailabilityInfo: 'Müsait saatleriniz çevrimiçi rezervasyon uygunluğunuzu belirleyecektir',
	GeneralAvailabilityInfo2:
		'Grup etkinlikleri sunan hizmetler, müşterilerin çevrimiçi olarak rezerve edebileceği mevcut saatleri azaltmak için yeni bir program kullanmalıdır.',
	GeneralHoursPlural: '{count} {count, plural, one {saat} other {saat}}',
	GeneralPractitioner: 'Pratisyen',
	GeneralPractitioners: 'Pratisyen Hekimler',
	GeneralServiceAvailabilityInfo: 'Bu program, atanan ekip üyelerinin davranışlarını geçersiz kılacaktır',
	Generate: 'Oluştur',
	GenerateBillingItemsBannerContent: 'Tekrarlayan randevular için faturalama kalemleri otomatik olarak oluşturulmaz.',
	GenerateItems: 'Öğeleri oluştur',
	GenerateNote: 'Not oluştur',
	GenerateNoteConfirmationModalDescription:
		'Ne yapmak istersiniz? Yeni bir not oluşturmak, mevcut olana eklemek veya içeriğini değiştirmek?',
	GenerateNoteFor: 'Not oluştur',
	GeneratingContent: 'İçerik oluşturuluyor...',
	GeneratingNote: 'Notunuzu oluşturuyorum...',
	GeneratingTranscript: 'Transkript oluşturuluyor',
	GeneratingTranscriptDescription: 'İşlemin tamamlanması birkaç dakika sürebilir',
	GeneratingYourTranscript: 'Transkriptiniz oluşturuluyor',
	GenericErrorDescription: '{module} yüklenemedi. Lütfen daha sonra tekrar deneyin.',
	GenericErrorTitle: 'Beklenmedik bir hata oluştu',
	GenericFailureSnackbar: 'Üzgünüz, beklenmeyen bir şey oldu. Lütfen sayfayı yenileyip tekrar deneyin.',
	GenericSavedSuccessSnackbar: 'Başarı! değişiklikler kaydedildi',
	GeneticCounselor: 'Genetik Danışman',
	Gerontologist: 'Gerontolog',
	Get50PercentOff: '%50 indirim kazan!',
	GetHelp: 'Yardım alın',
	GetStarted: 'Başlamak',
	GettingStartedAppointmentTypes: 'Randevu türleri oluşturun',
	GettingStartedAppointmentTypesDescription:
		'Hizmetlerinizi, fiyatlarınızı ve faturalandırma kodlarınızı özelleştirerek planlamanızı ve faturalandırmanızı kolaylaştırın',
	GettingStartedAppointmentTypesTitle: 'Takvim ',
	GettingStartedClients: 'Müşterilerinizi ekleyin',
	GettingStartedClientsDescription:
		'Gelecekteki randevular, notlar ve ödemeler için müşterilerle çalışmaya hazır olun',
	GettingStartedClientsTitle: 'Her şey müşterilerle başlar',
	GettingStartedCreateClient: 'Müşteri oluştur',
	GettingStartedImportClients: 'İstemcileri içe aktar',
	GettingStartedInvoices: 'Profesyonel gibi faturalama',
	GettingStartedInvoicesDescription: `Profesyonel faturalar oluşturmak kolaydır.
 Logonuzu, konumunuzu ve ödeme koşullarınızı ekleyin`,
	GettingStartedInvoicesTitle: 'en iyi ayağını ileri koy',
	GettingStartedMobileApp: 'Mobil uygulamayı edinin',
	GettingStartedMobileAppDescription: `Hareket halindeyken kolay erişim için Carepatron'u iOS, Android veya masaüstü cihazınıza indirebilirsiniz.`,
	GettingStartedMobileAppTitle: 'Her yerden çalışın',
	GettingStartedNavItem: 'Başlarken',
	GettingStartedPageTitle: `Carepatron'a başlarken`,
	GettingStartedPayments: 'Çevrimiçi ödemeleri kabul edin',
	GettingStartedPaymentsDescription: `Müşterilerinizin çevrimiçi ödeme yapmasını sağlayarak daha hızlı ödeme alın.
 Tüm faturalarınızı ve ödemelerinizi tek bir yerde görün`,
	GettingStartedPaymentsTitle: 'Ödemeleri çocuk oyuncağı haline getirin',
	GettingStartedSaveBranding: 'Marka bilinci oluşturmayı kaydedin',
	GettingStartedSyncCalendars: 'Diğer takvimleri senkronize edin',
	GettingStartedSyncCalendarsDescription:
		'Carepatron takviminizde çakışmalar olup olmadığını kontrol eder, böylece randevular yalnızca siz müsait olduğunuzda planlanır',
	GettingStartedSyncCalendarsTitle: 'Her zaman güncel kalın',
	GettingStartedVideo: 'Bir tanıtım videosunu izleyin',
	GettingStartedVideoDescription:
		'Küçük ekipler ve müşterileri için ilk hepsi bir arada sağlık hizmetleri çalışma alanları',
	GettingStartedVideoTitle: `Carepatron'a hoş geldiniz`,
	GetttingStartedGetMobileDownload: 'Uygulamayı indirin',
	GetttingStartedGetMobileNoDownload: `Bu tarayıcıyla uyumlu değil. iPhone veya iPad kullanıyorsanız lütfen bu sayfayı Safari'de açın. Aksi halde Chrome'da açmayı deneyin.`,
	Glossary: 'Sözlük',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail, hesabınızdan günde yalnızca 500 mesaj gönderilmesine izin verir. Bazı mesajlar başarısız olabilir. Devam etmek istiyor musunuz?',
	GoToAppointment: 'Randevuya git',
	GoToApps: 'Uygulamalara git',
	GoToAvailability: 'Kullanılabilirliğe git',
	GoToClientList: 'Müşteri listesine git',
	GoToClientRecord: 'Müşteri kaydına git',
	GoToClientSettings: 'Şimdi istemci ayarlarına gidin',
	GoToInvoiceTemplates: 'Fatura şablonlarına git',
	GoToNotificationSettings: 'Bildirim ayarlarına git',
	GoToPaymentSettings: 'Ödeme ayarlarına git',
	Google: 'Google',
	GoogleCalendar: 'Google Takvim',
	GoogleColor: 'Google takvim rengi',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Etiket Yöneticisi Kapsayıcı Kimliği',
	GotIt: 'Anladım!',
	Goto: 'Git',
	Granddaughter: 'Kız torun',
	Grandfather: 'Büyük baba',
	Grandmother: 'Nene',
	Grandparent: 'Büyükbaba veya büyükanne',
	Grandson: 'Erkek torun',
	GrantPortalAccess: 'Portal erişimi ver',
	GraphicDesigner: 'Grafik Tasarımcı',
	Grid: 'Izgara',
	GridView: 'Izgara görünümü',
	Group: 'Grup',
	GroupBy: 'Gruplama ölçütü',
	GroupEvent: 'Grup etkinliği',
	GroupEventHelper: 'Hizmet için katılımcı limitleri belirleyin',
	GroupFilterLabel: 'Tüm {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Grup Kimliği',
	GroupInputFieldsFormPrimaryText: 'Grup giriş alanları',
	GroupInputFieldsFormSecondaryText: 'Özel alanlar seçin veya ekleyin',
	GuideTo: '{value} Kılavuzu',
	GuideToImproveVideoQuality: 'Video kalitesini artırma kılavuzu',
	GuideToManagingPayers: 'Ödeme Yöneticileri',
	GuideToSubscriptionsBilling: 'Abonelik faturalandırma kılavuzu',
	GuideToTroubleshooting: 'Sorun giderme kılavuzu',
	Guidelines: 'Yönergeler',
	GuidelinesCategoryDescription: 'Klinik karar vermeyi yönlendirmek için',
	HST: 'HST',
	HairStylist: 'Saç stilisti',
	HaveBeenWaiting: 'Uzun zamandır bekliyordun',
	HeHim: 'O/Onun',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Takvim',
	HeaderCalls: 'Aramalar',
	HeaderClientAppAccountSettings: 'Hesap ayarları',
	HeaderClientAppCalls: 'Aramalar',
	HeaderClientAppMyDocumentation: 'Belgeleme',
	HeaderClientAppMyRelationships: 'İlişkilerim',
	HeaderClients: 'Müşteriler',
	HeaderHelp: 'Yardım',
	HeaderMoreOptions: 'Daha fazla seçenek',
	HeaderStaff: 'Kadro',
	HealthCoach: 'Sağlık koçu',
	HealthCoaches: 'Sağlık Koçları',
	HealthEducator: 'Sağlık eğitimcisi',
	HealthInformationTechnician: 'Sağlık Bilgi Teknisyeni',
	HealthPolicyExpert: 'Sağlık Politikası Uzmanı',
	HealthServicesAdministrator: 'Sağlık Hizmetleri Yöneticisi',
	HelpArticles: 'Yardım makaleleri',
	HiddenColumns: 'Gizli sütunlar',
	HiddenFields: 'Gizli Alanlar',
	HiddenSections: 'Gizli bölümler',
	HiddenSectionsAndFields: 'Gizli bölümler/alanlar',
	HideColumn: 'Sütunu gizle',
	HideColumnButton: '{value} sütununu gizle düğmesi',
	HideDetails: 'Detayları gizle',
	HideField: 'Alanı gizle',
	HideFullAddress: 'Saklamak',
	HideMenu: 'Menüyü gizle',
	HideMergeSummarySidebar: 'Birleştirme özetini gizle',
	HideSection: 'Bölümü gizle',
	HideYourView: 'Görünümünüzü gizleyin',
	Highlight: 'Rengi vurgula',
	Highlighter: 'Vurgulayıcı',
	History: 'Geçmiş',
	HistoryItemFooter:
		'{actors, select, undefined {{date} saat {time}} other { {actors} tarafından • {date} saat {time}}}',
	HistorySidePanelEmptyState: 'Hiç geçmiş kaydı bulunamadı',
	HistoryTitle: 'Aktivite Günlüğü',
	HolisticHealthPractitioner: 'Bütünsel Sağlık Uygulayıcısı',
	HomeCaregiver: 'Evde Bakıcı',
	HomeHealthAide: 'Evde Sağlık Yardımcısı',
	HomelessShelter: 'Evsizler barınağı',
	HourAbbreviation: '{count} {count, plural, one {saat} other {saat}}',
	Hourly: 'Saatlik',
	HoursPlural: '{age, plural, one {# saat} other {# saat}}',
	HowCanWeImprove: 'Bunu nasıl geliştirebiliriz?',
	HowCanWeImproveResponse: 'Bu yanıtı nasıl iyileştirebiliriz?',
	HowDidWeDo: 'Nasıl yaptık?',
	HowDoesReferralWork: 'Yönlendirme programı kılavuzu',
	HowToUseAiSummarise: 'AI Özetleme nasıl kullanılır?',
	HumanResourcesManager: 'İnsan Kaynakları Yöneticisi',
	Husband: 'Koca',
	Hypnotherapist: 'Hipnoterapist',
	IVA: 'KDV',
	IgnoreNotification: 'Bildirimi yoksay',
	IgnoreOnce: 'Bir kez yoksay',
	IgnoreSender: 'Göndereni yoksay',
	IgnoreSenderDescription: `Gönderenin gelecekteki mesajlarını yoksaymak için bu göndereni yoksayabilirsiniz. Bu, gönderenin gelecekteki mesajlarını yoksayacak ve bu mesajları 'Görülmeyenler' klasörüne taşıyacaktır.`,
	IgnoreSenders: 'Gönderenleri yoksay',
	IgnoreSendersSuccess: 'E-posta adresi yok sayıldı <mark>{addresses}</mark>',
	Ignored: 'Yoksayıldı',
	Image: 'Resim',
	Import: 'İçe aktarmak',
	ImportActivity: 'İçe aktarma etkinliği',
	ImportClientSuccessSnackbarDescription: 'Dosyanız başarıyla içe aktarıldı',
	ImportClientSuccessSnackbarTitle: 'İçe aktarma başarılı!',
	ImportClients: 'Müşterileri içe aktar',
	ImportClientsFailureSnackbarDescription: 'Bir hata nedeniyle dosyanız başarıyla içe aktarılamadı.',
	ImportClientsFailureSnackbarTitle: 'İçe aktarma başarısız oldu!',
	ImportClientsGuide: 'Müşteri İçe Aktarma Rehberi',
	ImportClientsInProgressSnackbarDescription: 'Bu işlemin tamamlanması yalnızca bir dakika kadar sürecektir.',
	ImportClientsInProgressSnackbarTitle: '{fileName} içeri aktarılıyor',
	ImportClientsModalDescription:
		'Verilerinizin nereden geleceğini seçin; cihazınızdaki bir dosya mı, üçüncü taraf bir hizmet mi yoksa başka bir yazılım platformu mu?',
	ImportClientsModalFileUploadHelperText: '{fileTypes} dosyalarını destekler. Boyut limiti {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Müşteri verilerini içe aktarma kılavuzu',
	ImportClientsModalStep1Label: 'Veri kaynağını seçin',
	ImportClientsModalStep2Label: 'Dosya yükleme',
	ImportClientsModalStep3Label: 'Alanları inceleyin',
	ImportClientsModalTitle: 'Müşteri verilerinizi içe aktarma',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {müşteri} other {müşteriler}} içe aktarmaya hazır',
	ImportContactFailedNotificationSubject: 'Verilerinizin içe aktarımı başarısız oldu',
	ImportDataSourceSelectorLabel: 'Veri kaynağını şuradan içe aktar:',
	ImportDataSourceSelectorPlaceholder: 'İçe aktarma veri kaynağını arayın veya seçin',
	ImportExportButton: 'İthalat ihracat',
	ImportFailed: 'İçe aktarma başarısız oldu',
	ImportFromAnotherPlatformTileDescription: 'Müşteri dosyalarınızın bir ihracatını indirin ve buraya yükleyin.',
	ImportFromAnotherPlatformTileLabel: 'Başka bir platformdan içe aktar',
	ImportGuide: 'İthalat kılavuzu',
	ImportInProgress: 'İçe aktarma işlemi devam ediyor',
	ImportProcessing: 'İçeri aktarma işleniyor...',
	ImportSpreadsheetDescription: `.CSV, .XLS veya .XLSX gibi tablo verileri içeren bir e-tablo dosyası yükleyerek mevcut müşteri listenizi Carepatron'a aktarabilirsiniz.`,
	ImportSpreadsheetTitle: 'E-tablo dosyanızı içe aktarın',
	ImportTemplates: 'Şablonları içe aktar',
	Importing: 'İthalat',
	ImportingCalendarProductEvents: '{product} etkinliklerini içe aktarıyor',
	ImportingData: 'Verileri içe aktarma',
	ImportingSpreadsheetDescription: 'Bu işlemin tamamlanması yalnızca bir dakika sürecektir',
	ImportingSpreadsheetTitle: 'E-tablonuzu içe aktarma',
	ImportsInProgress: 'İthalat devam ediyor',
	InPersonMeeting: 'Şahsen toplantı',
	InProgress: 'Devam etmekte',
	InTransit: 'Transit olarak',
	InTransitTooltip: `Transit bakiyesi, Stripe'tan banka hesabınıza yapılan tüm ödenmiş fatura ödemelerini içerir. Bu fonların ödenmesi genellikle 3-5 gün sürer.`,
	Inactive: 'Etkin değil',
	InboundOrOutboundCalls: 'Gelen veya giden aramalar',
	Inbox: 'Gelen kutusu',
	InboxAccessRestricted: 'Geçiş kısıtlı. İzinler için lütfen gelen kutusu sahibiyle iletişime geçin.',
	InboxAccountAlreadyConnected: `Bağlanmaya çalıştığınız kanal zaten Carepatron'a bağlı`,
	InboxAddAttachments: 'Ek ekle',
	InboxAreYouSureDeleteMessage: 'Bu mesajı silmek istediğinize emin misiniz?',
	InboxBulkCloseSuccess: '{count, plural, one {Başarıyla # görüşme kapatıldı} other {Başarıyla # görüşme kapatıldı}}',
	InboxBulkComposeModalTitle: 'Toplu mesaj oluştur',
	InboxBulkDeleteSuccess: '{count, plural, one {# sohbet başarıyla silindi} other {# sohbet başarıyla silindi}}',
	InboxBulkReadSuccess:
		'{count, plural, one {# konuşma başarıyla okundu olarak işaretlendi} other {# konuşma başarıyla okundu olarak işaretlendi}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Başarıyla # görüşme yeniden açıldı} other {Başarıyla # görüşme yeniden açıldı}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Başarıyla # konuşma okunmamış olarak işaretlendi} other {Başarıyla # konuşma okunmamış olarak işaretlendi}}',
	InboxChatCreateGroup: 'Grup Oluştur',
	InboxChatDeleteGroupModalDescription:
		'Bu grubu silmek istediğinizden emin misiniz? Tüm mesajlar ve ekler silinecektir.',
	InboxChatDeleteGroupModalTitle: 'Grubu Sil',
	InboxChatDiscardDraft: 'Taslağı at',
	InboxChatDragDropText: 'Dosyaları yüklemek için buraya bırakın',
	InboxChatGroupConversation: 'Grup sohbeti',
	InboxChatGroupCreateModalDescription:
		'Ekibiniz, müşterileriniz veya topluluğunuzla mesajlaşmak ve işbirliği yapmak için yeni bir grup başlatın.',
	InboxChatGroupCreateModalTitle: 'Grup oluştur',
	InboxChatGroupMembers: 'Grup Üyeleri',
	InboxChatGroupModalGroupNameFieldLabel: 'Grup adı',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Örn. müşteri desteği, yönetici',
	InboxChatGroupModalGroupNameFieldRequired: 'Bu alan zorunludur',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'En az bir üye gerekli',
	InboxChatGroupModalMembersFieldLabel: 'Grup Üyelerini Seç',
	InboxChatGroupModalMembersFieldPlaceholder: 'Üyeleri Seç',
	InboxChatGroupUpdateModalTitle: 'Grubu Yönet',
	InboxChatLeaveGroup: 'Gruptan Ayrıl',
	InboxChatLeaveGroupModalDescription:
		'Bu gruptan ayrılmak istediğinizden emin misiniz? Artık mesaj veya güncelleme almayacaksınız.',
	InboxChatLeaveGroupModalTitle: 'Gruptan Ayrıl',
	InboxChatLeftGroupMessage: 'Sol grup mesajı',
	InboxChatManageGroup: 'Grubu Yönet',
	InboxChatSearchParticipants: 'Alıcıları Seçin',
	InboxCloseConversationSuccess: 'Konuşma başarıyla kapatıldı',
	InboxCompose: 'Oluştur',
	InboxComposeBulk: 'Toplu mesaj',
	InboxComposeCarepatronChat: 'Haberci',
	InboxComposeChat: 'Sohbet Oluştur',
	InboxComposeDisabledNoConnection: 'Mesaj göndermek için bir e-posta hesabı bağlayın.',
	InboxComposeDisabledNoPermissionTooltip: 'Bu gelen kutusundan mesaj göndermek için izniniz yok.',
	InboxComposeEmail: 'E-posta oluştur',
	InboxComposeMessageFrom: 'İtibaren',
	InboxComposeMessageRecipientBcc: 'Gizli',
	InboxComposeMessageRecipientCc: 'Bilgi',
	InboxComposeMessageRecipientTo: 'İle',
	InboxComposeMessageSubject: 'Ders:',
	InboxConnectAccountButton: 'E-postanızı bağlayın',
	InboxConnectedDescription: 'Gelen kutunuzda iletişim yok',
	InboxConnectedHeading: 'İletişim kurmaya başladığınız anda konuşmalarınız burada görünecek',
	InboxConnectedHeadingClientView: 'Müşteri iletişimlerinizi kolaylaştırın',
	InboxCreateFirstInboxButton: 'İlk gelen kutunuzu oluşturun',
	InboxCreationSuccess: 'Gelen kutusu başarıyla oluşturuldu',
	InboxDeleteAttachment: 'Eki sil',
	InboxDeleteConversationSuccess: 'Görüşme başarıyla silindi',
	InboxDeleteMessage: 'Mesaj silinsin mi?',
	InboxDirectMessage: 'Doğrudan mesaj',
	InboxEditDraft: 'Taslağı düzenle',
	InboxEmailComposeReplyEmail: 'Yanıt oluştur',
	InboxEmailDraft: 'Taslak',
	InboxEmailNotFound: 'Email bulunamadı',
	InboxEmailSubjectFieldInformation: 'Konu satırını değiştirmek yeni bir mesaj dizili e-posta oluşturacaktır.',
	InboxEmptyArchiveDescription: 'Arşivlenmiş görüşme bulunamadı',
	InboxEmptyBinDescription: 'Silinen görüşme bulunamadı',
	InboxEmptyBinHeading: 'Her şey açık, burada görülecek bir şey yok',
	InboxEmptyBinSuccess: 'Konuşmalar başarıyla silindi',
	InboxEmptyCongratsHeading: 'Güzel iş! Bir sonraki konuşmaya kadar otur ve rahatla',
	InboxEmptyDraftDescription: 'Taslak görüşme bulunamadı',
	InboxEmptyDraftHeading: 'Her şey açık, burada görülecek bir şey yok',
	InboxEmptyOtherDescription: 'Diğer görüşme bulunamadı',
	InboxEmptyScheduledHeading: 'Her şey yolunda, gönderilmek üzere planlanmış bir görüşme yok',
	InboxEmptySentDescription: 'Gönderilmiş görüşme bulunamadı',
	InboxForward: 'İleri',
	InboxGroupClientsLabel: 'Tüm müşteriler',
	InboxGroupClientsOverviewLabel: 'Müşteriler',
	InboxGroupClientsSelectedItemPrefix: 'Müşteri',
	InboxGroupStaffsLabel: 'Tüm ekip',
	InboxGroupStaffsOverviewLabel: 'Ekip',
	InboxGroupStaffsSelectedItemPrefix: 'Ekip',
	InboxGroupStatusLabel: 'Tüm Durumlar',
	InboxGroupStatusOverviewLabel: 'Bir duruma gönder',
	InboxGroupStatusSelectedItemPrefix: 'Durum',
	InboxGroupTagsLabel: 'Tüm etiketler',
	InboxGroupTagsOverviewLabel: 'Bir etikete gönder',
	InboxGroupTagsSelectedItemPrefix: 'Etiket',
	InboxHideQuotedText: 'Alıntı yapılan metni gizle',
	InboxIgnoreConversationSuccess: 'Konuşma başarıyla göz ardı edildi',
	InboxMessageAllLabelRecipientsCount: 'Tüm {label} Alıcıları ({count})',
	InboxMessageBodyPlaceholder: 'Mesajınızı ekleyin',
	InboxMessageDeleted: 'Mesaj silindi',
	InboxMessageMarkedAsRead: 'Mesaj okundu olarak işaretlendi',
	InboxMessageMarkedAsUnread: 'Mesaj okunmamış olarak işaretlendi',
	InboxMessageSentViaChat: '**Sohbet yoluyla gönderildi**  • {time} tarafından {name}',
	InboxMessageShowMoreRecipients: '+{count} daha fazla',
	InboxMessageWasDeleted: 'Bu mesaj silindi.',
	InboxNoConnectionDescription:
		'E-posta hesabınızı bağlayın veya birden fazla e-posta içeren gelen kutuları oluşturun',
	InboxNoConnectionHeading: 'Müşteri iletişimlerinizi entegre edin',
	InboxNoDirectMessage: 'Son mesaj yok',
	InboxRecentConversations: 'Son',
	InboxReopenConversationSuccess: 'Konuşma başarıyla yeniden açıldı',
	InboxReply: 'Cevap vermek',
	InboxReplyAll: 'Hepsini cevapla',
	InboxRestoreConversationSuccess: 'Görüşme başarıyla geri yüklendi',
	InboxScheduleSendCancelSendSuccess: 'Planlanan gönderim iptal edildi ve mesaj taslağa geri döndürüldü',
	InboxScheduleSendMessageSuccessDescription: '{date} için planlanan gönder',
	InboxScheduleSendMessageSuccessTitle: 'Göndermeyi planlama',
	InboxSearchForConversations: '"{query}" için ara',
	InboxSendMessageSuccess: 'Konuşma başarıyla gönderildi',
	InboxSettings: 'Gelen kutusu ayarları',
	InboxSettingsAppsDesc:
		'Bu paylaşılan gelen kutusu için bağlı uygulamaları yönetin: gerektiği şekilde bağlantı ekleyin veya kaldırın.',
	InboxSettingsAppsNewConnectedApp: 'Yeni bağlı uygulama',
	InboxSettingsAppsTitle: 'Bağlı uygulamalar',
	InboxSettingsDeleteAccountFailed: 'Gelen kutusu hesabı silinemedi',
	InboxSettingsDeleteAccountSuccess: 'Gelen kutusu hesabı başarıyla silindi',
	InboxSettingsDeleteAccountWarning:
		'{email} adresini kaldırmak, onu {inboxName} gelen kutusundan ayıracak ve mesajların senkronize olmasını durduracaktır.',
	InboxSettingsDeleteInboxFailed: 'Gelen kutusu silinemedi',
	InboxSettingsDeleteInboxSuccess: 'Gelen kutusu başarıyla silindi',
	InboxSettingsDeleteInboxWarning:
		'{inboxName} silinirse, bağlı tüm kanallar bağlantısı kesilecek ve bu gelen kutusu ile ilişkili tüm mesajlar silinecektir.		Bu işlem kalıcıdır ve geri alınamaz.',
	InboxSettingsDetailsDesc:
		'Ekibinizin müşteri mesajlarını verimli bir şekilde yönetmesi için iletişim gelen kutusu.',
	InboxSettingsDetailsTitle: 'Gelen kutusu ayrıntıları',
	InboxSettingsEmailSignatureLabel: 'E-posta imzası varsayılanı',
	InboxSettingsReplyFormatDesc:
		'Varsayılan yanıt adresinizi ve e-posta imzanızı, e-postayı kimin gönderdiğine bakılmaksızın tutarlı bir şekilde görüntülenecek şekilde ayarlayın.',
	InboxSettingsReplyFormatTitle: 'Yanıt biçimi',
	InboxSettingsSendFromLabel: 'Varsayılan yanıtı şuradan ayarlayın: ',
	InboxSettingsStaffDesc: 'Sorunsuz işbirliği için ekip üyelerinin bu paylaşılan gelen kutusuna erişimini yönetin.',
	InboxSettingsStaffTitle: 'Ekip üyelerini atayın',
	InboxSettingsUpdateInboxDetailsFailed: 'Gelen kutusu ayrıntıları güncellenemedi',
	InboxSettingsUpdateInboxDetailsSuccess: 'Gelen kutusu ayrıntıları başarıyla güncellendi',
	InboxSettingsUpdateInboxStaffsFailed: 'Gelen kutusu ekip üyeleri güncellenemedi',
	InboxSettingsUpdateInboxStaffsSuccess: 'Gelen kutusu ekip üyeleri başarıyla güncellendi',
	InboxSettingsUpdateReplyFormatFailed: 'Yanıt biçimi güncellenemedi',
	InboxSettingsUpdateReplyFormatSuccess: 'Yanıt biçimi başarıyla güncellendi',
	InboxShowQuotedText: 'Alıntı yapılan metni göster',
	InboxStaffRoleAdminDescription: 'Gelen kutularını görüntüleyin, yanıtlayın ve yönetin',
	InboxStaffRoleResponderDescription: 'Görüntüle ve yanıtla',
	InboxStaffRoleViewerDescription: 'Sadece Görüntüle',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Düzenlemeye devam et',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Evet, toplu gönderime geç',
	InboxSuggestMoveToBulkComposeMessageContent:
		'{count} kişiden fazla alıcı seçtiniz. Toplu e-posta olarak göndermek istiyor musunuz?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Uyarı',
	InboxSwitchToOtherInbox: 'Başka bir gelen kutusuna geç',
	InboxUndoSendMessageSuccess: 'Gönderim geri alındı',
	IncludeLineItems: 'Satır öğelerini dahil et',
	IncludeSalesTax: 'Vergiye tabi',
	IncludesAiSmartPrompt: 'AI akıllı istemleri içerir',
	Incomplete: 'Tamamlanmamış',
	IncreaseIndent: 'Girintiyi artır',
	IndianHealthServiceFreeStandingFacility: 'Bağımsız Hint Sağlık Hizmetleri tesisi',
	IndianHealthServiceProviderFacility: 'Hint Sağlık Hizmeti sağlayıcısı tabanlı tesis',
	Information: 'Bilgi',
	InitialAssessment: 'İlk Değerlendirme',
	InitialSignupPageClientFamilyTitle: 'Müşteri veya Aile Üyesi',
	InitialSignupPageProviderTitle: 'Sağlık ',
	InitialTreatment: 'İlk tedavi',
	Initials: 'Baş harfler',
	InlineEmbed: 'Satır içi gömme',
	InputPhraseToConfirm: 'Onaylamak için, {confirmationPhrase} yazın.',
	Insert: 'Sokmak',
	InsertTable: 'Tablo ekle',
	InstallCarepatronOnYourIphone1: `Carepatron'u iOS'unuza yükleyin: dokunun`,
	InstallCarepatronOnYourIphone2: 've ardından Ana Ekrana Ekle',
	InsufficientCalendarScopesSnackbar: `Senkronizasyon başarısız oldu - lütfen Carepatron'a takvim izinleri verin`,
	InsufficientInboxScopesSnackbar: `Senkronizasyon başarısız oldu - lütfen Carepatron'a e-posta izinleri verin`,
	InsufficientScopeErrorCodeSnackbar: `Senkronizasyon başarısız oldu - lütfen Carepatron'a yönelik tüm izinlere izin verin`,
	Insurance: 'Sigorta',
	InsuranceAmount: 'Sigorta Tutarı',
	InsuranceClaim: 'Sigorta talebi',
	InsuranceClaimAiChatPlaceholder: 'Sigorta talebi hakkında bilgi alın...',
	InsuranceClaimAiClaimNumber: 'İddia {number}',
	InsuranceClaimAiSubtitle: 'Sigorta Faturası • Talep Doğrulama',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber}, {payerNumber} {payerName} kuruluşuna gönderildi, reddedildi.',
	InsuranceClaimErrorDescription:
		'Talepte, ödeme yapan veya takas merkezi tarafından bildirilen hatalar bulunmaktadır. Lütfen aşağıdaki hata mesajlarını inceleyin ve talebi yeniden gönderin.',
	InsuranceClaimErrorGuideLink: 'Sigorta Talepleri Kılavuzu',
	InsuranceClaimErrorTitle: 'Talepte Hata',
	InsuranceClaimNotFound: 'Sigorta talebi bulunamadı',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {{paymentAmount} tutarında kısmi ödeme} other {{paymentAmount} tutarında ödeme}} {claimNumber} numaralı talep için {payerNumber} {payerName} tarafından kaydedildi',
	InsuranceClaimRejectedSubject: `Claim {claimNumber}, {payerNumber} {payerName}'a gönderildi, reddedildi.`,
	InsuranceClaims: 'Sigorta talepleri',
	InsuranceInformation: 'Sigorta bilgileri',
	InsurancePaid: 'Ödenen Sigorta',
	InsurancePayer: 'Sigorta ödeyicisi',
	InsurancePayers: 'Sigorta mükellefleri',
	InsurancePayersDescription: 'Hesabınıza eklenen ödeme yapanları görüntüleyin ve kayıt işlemlerini yönetin.',
	InsurancePayment: 'Sigorta ödemesi',
	InsurancePoliciesDetailsSubtitle: 'Talepleri desteklemek için müşteri sigorta bilgilerini ekleyin.',
	InsurancePoliciesDetailsTitle: 'Politika detayları',
	InsurancePoliciesListSubtitle: 'Talepleri desteklemek için müşteri sigorta bilgilerini ekleyin.',
	InsurancePoliciesListTitle: 'Sigorta poliçeleri',
	InsuranceSelfPay: 'Kendi Ödemesi',
	InsuranceType: 'Sigorta türü',
	InsuranceUnpaid: 'Sigorta ödenmedi',
	Intake: 'Giriş',
	IntakeExpiredErrorCodeSnackbar:
		'Bu alımın süresi doldu. Başka bir alımı yeniden göndermek için lütfen sağlayıcınızla iletişime geçin.',
	IntakeNotFoundErrorSnackbar:
		'Bu alım bulunamadı. Başka bir alımı yeniden göndermek için lütfen sağlayıcınızla iletişime geçin.',
	IntakeProcessLearnMoreInstructions: 'Giriş formlarınızı ayarlama kılavuzu',
	IntakeTemplateSelectorPlaceholder: 'Müşterinize doldurması için göndereceğiniz formları ve sözleşmeleri seçin',
	Integration: 'Entegrasyon',
	IntenseBlur: 'Arka planınızı yoğun bir şekilde bulanıklaştırın',
	InteriorDesigner: 'İç Mimar',
	InternetBanking: 'banka transferi',
	Interval: 'Aralık',
	IntervalDays: 'Aralık (Gün)',
	IntervalHours: 'Aralık (Saat)',
	Invalid: 'Geçersiz',
	InvalidDate: 'Geçersiz tarih',
	InvalidDateFormat: 'Tarih, {format} biçiminde olmalıdır.',
	InvalidDisplayName: 'Görüntülenen ad {value} içeremez.',
	InvalidEmailFormat: 'Geçersiz e-posta formatı',
	InvalidFileType: 'Geçersiz dosya türü',
	InvalidGTMContainerId: 'Geçersiz GTM kapsayıcı kimliği biçimi',
	InvalidPaymentMethodCode: 'Seçilen ödeme yöntemi geçerli değil. Lütfen diğerini seçin.',
	InvalidPromotionCode: 'Promosyon Kodu geçersiz',
	InvalidReferralDescription: `Carepatron'u zaten kullanıyorum`,
	InvalidStatementDescriptor: `Açıklama açıklayıcısı 5 ila 22 karakter uzunluğunda olmalı ve yalnızca harf, sayı ve boşluk içermeli ve <, >, , ', ", * içermemelidir`,
	InvalidToken: 'Geçersiz jeton',
	InvalidTotpSetupVerificationCode: 'Geçersiz doğrulama kodu.',
	InvalidURLErrorText: 'Bu geçerli bir URL olmalıdır',
	InvalidZoomTokenErrorCodeSnackbar:
		'Zoom jetonunun süresi doldu. Lütfen Zoom uygulamanızı yeniden bağlayın ve tekrar deneyin.',
	Invite: 'Davet etmek',
	InviteRelationships: 'İlişkileri davet et',
	InviteToPortal: 'Portala davet et',
	InviteToPortalModalDescription: `Carepatron'a kaydolması için müşterinize bir davet e-postası gönderilecektir.`,
	InviteToPortalModalTitle: `{name}'i Carepatron Portal'a Davet Et`,
	InviteUserDescription: ' ',
	InviteUserTitle: 'Yeni kullanıcı davet et',
	Invited: 'Davet edildi',
	Invoice: 'Fatura',
	InvoiceColorPickerDescription: 'Faturada kullanılacak renk teması',
	InvoiceColorTheme: 'Fatura renk teması',
	InvoiceContactDeleted: 'Fatura iletişim bilgisi silindi ve bu fatura güncellenemiyor.',
	InvoiceDate: 'Yayınlanma tarihi',
	InvoiceDetails: 'fatura detayları',
	InvoiceFieldsPlaceholder: 'Alanları arayın...',
	InvoiceFrom: `Fatura {number} {fromProvider}'dan`,
	InvoiceInvalidCredit: 'Geçersiz kredi tutarı, kredi tutarı fatura toplamını aşamaz',
	InvoiceNotFoundDescription:
		'Lütfen sağlayıcınızla iletişime geçin ve onlardan daha fazla bilgi almasını veya faturayı yeniden göndermesini isteyin.',
	InvoiceNotFoundTitle: 'Fatura bulunamadı',
	InvoiceNumber: 'Fatura #',
	InvoiceNumberFormat: 'Fatura #{number}',
	InvoiceNumberMustEndWithDigit: 'Fatura numarası bir rakamla (0-9) bitmelidir',
	InvoicePageHeader: 'Faturalar',
	InvoicePaidNotificationSubject: 'Fatura {invoiceNumber} ödendi',
	InvoiceReminder: 'Fatura hatırlatıcıları',
	InvoiceReminderSentence: 'Fatura vade tarihine {deliveryType} hatırlatıcıyı {interval} {unit} {beforeAfter} gönder',
	InvoiceReminderSettings: 'Fatura hatırlatma ayarları',
	InvoiceReminderSettingsInfo: 'Hatırlatmalar yalnızca Carepatron üzerinden gönderilen faturalar için geçerlidir',
	InvoiceReminders: 'Fatura hatırlatıcıları',
	InvoiceRemindersInfo:
		'Fatura son tarihleri için otomatik hatırlatıcılar ayarlayın. Hatırlatmalar yalnızca Carepatron aracılığıyla gönderilen faturalar için geçerlidir',
	InvoiceSettings: 'Fatura ayarları',
	InvoiceStatus: 'Fatura durumu',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, ABD',
	InvoiceTemplateDescriptionPlaceholder:
		'Alternatif ödemeler için notlar, banka havalesi ayrıntıları veya şartlar ve koşullar ekleyin',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Serbest meslek',
	InvoiceTemplateEthnicityPlaceholder: 'Kafkas',
	InvoiceTemplateNotFoundDescription: 'Lütfen sağlayıcınızla iletişime geçin ve daha fazla bilgi isteyin.',
	InvoiceTemplateNotFoundTitle: 'Fatura şablonu bulunamadı',
	InvoiceTemplates: 'Fatura şablonları',
	InvoiceTemplatesDescription:
		'Kullanıcı dostu şablonlarımızla fatura şablonlarınızı markanızı yansıtacak, yasal gereklilikleri karşılayacak ve müşteri tercihlerine uyacak şekilde uyarlayın.',
	InvoiceTheme: 'Fatura teması',
	InvoiceTotal: 'Fatura toplamı',
	InvoiceUninvoicedAmounts: 'Faturalandırılmamış tutarları faturalandır',
	InvoiceUpdateVersionMessage: `Bu faturanın düzenlenmesi en son sürümü gerektirir. Lütfen Carepatron'u yeniden yükleyin ve tekrar deneyin.`,
	Invoices: '{count, plural, one {Fatura} other {Faturalar}}',
	InvoicesEmptyStateDescription: 'Hiçbir fatura bulunamadı',
	InvoicingAndPayment: 'Faturalama ',
	Ireland: 'İrlanda',
	IsA: 'bir',
	IsBetween: 'arasında',
	IsEqualTo: 'eşittir',
	IsGreaterThan: 'daha büyüktür',
	IsGreaterThanOrEqualTo: 'büyük veya eşittir',
	IsLessThan: 'daha az',
	IsLessThanOrEqualTo: 'daha az veya eşittir',
	IssueCredit: 'Kredi sorunu',
	IssueCreditAdjustment: 'Kredi ayarlaması sorunu',
	IssueDate: 'Düzenleme tarihi',
	Italic: 'İtalik',
	Items: 'Öğeler',
	ItemsAndAdjustments: 'Öğeler ve düzenlemeler',
	ItemsRemaining: '+{count} öğe kaldı',
	JobTitle: 'İş unvanı',
	Join: 'Katılmak',
	JoinCall: 'Görüşmeye katıl',
	JoinNow: 'Şimdi Katıl',
	JoinProduct: `{product}'a katıl`,
	JoinVideoCall: 'Görüntülü görüşmeye katılın',
	JoinWebinar: 'Webinara katıl',
	JoinWithVideoCall: '{product} ile katılın',
	Journalist: 'Gazeteci',
	JustMe: 'Benim',
	JustYou: 'Sadece sen',
	Justify: 'Savunmak',
	KeepSeparate: 'Ayrı tutun',
	KeepSeparateSuccessMessage: '{clientNames} için ayrı kayıtları başarıyla tuttunuz.',
	KeepWaiting: 'Beklemeye devam et',
	Label: 'Etiket',
	LabelOptional: 'Etiket (İsteğe bağlı)',
	LactationConsulting: 'Emzirme danışmanlığı',
	Language: 'Dil',
	Large: 'Büyük',
	LastDxCode: 'Son DX kodu',
	LastLoggedIn: 'Son girişiniz {date} tarihinde {time} saatinde gerçekleşti.',
	LastMenstrualPeriod: 'Son adet tarihi',
	LastMonth: 'Geçen ay',
	LastNDays: 'Son {number} gün',
	LastName: 'Soy isim',
	LastNameFirstInitial: 'Soyadı, ilk harf',
	LastWeek: 'Geçen hafta',
	LastXRay: 'Son röntgen',
	LatestVisitOrConsultation: 'Son ziyaret veya danışma',
	Lawyer: 'Avukat',
	LearnMore: 'Daha fazla bilgi edin',
	LearnMoreTipsToGettingStarted: 'Başlamanız için daha fazla ipucu öğrenin',
	LearnToSetupInbox: 'Gelen kutusu hesabını nasıl ayarlayacağınızı öğrenin',
	Leave: 'Ayrılmak',
	LeaveCall: 'Aramayı bırak',
	LeftAlign: 'Sola hizala',
	LegacyBillingItemsNotAvailable:
		'Bu randevu için ayrı ayrı fatura kalemleri henüz mevcut değil. Yine de normal şekilde fatura edebilirsiniz.',
	LegacyBillingItemsNotAvailableTitle: 'Eski faturalama',
	LegalAndConsent: 'Yasal ve rıza',
	LegalConsentFormPrimaryText: 'Yasal onay',
	LegalConsentFormSecondaryText: 'Kabul etme veya reddetme seçenekleri',
	LegalGuardian: 'Yasal koruyucu',
	Letter: 'Mektup',
	LettersCategoryDescription: 'Klinik ve idari yazışmalar oluşturmak için',
	Librarian: 'Kütüphaneci',
	LicenseNumber: 'Lisans numarası',
	LifeCoach: 'Yaşam Koçu',
	LifeCoaches: 'Yaşam Koçları',
	Limited: 'Sınırlı',
	LineSpacing: 'Satır ve paragraf aralığı',
	LinearScaleFormPrimaryText: 'Doğrusal ölçek',
	LinearScaleFormSecondaryText: 'Ölçek seçenekleri 1-10',
	Lineitems: 'Satır öğeleri',
	Link: 'Bağlantı',
	LinkClientFormSearchClientLabel: 'Bir müşteri arayın',
	LinkClientModalTitle: 'Mevcut istemciye bağlantı',
	LinkClientSuccessDescription:
		'<strong>{newName}’</strong>in iletişim bilgileri <strong>{existingName}’</strong>in kaydına eklendi.',
	LinkClientSuccessTitle: 'Mevcut kişiye başarılı bir şekilde bağlanıldı',
	LinkForCallCopied: 'Bağlantı kopyalandı!',
	LinkToAnExistingClient: 'Mevcut bir istemciye bağlantı',
	LinkToClient: 'Müşteriye bağlantı',
	ListAndTracker: 'Liste/Takipçi',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} bu görüşmede}
			other {{attendees} bu görüşmede}
		}`,
	ListStyles: 'Stilleri listele',
	ListsAndTrackersCategoryDescription: 'İşleri düzenlemek ve takip etmek için',
	LivingArrangements: 'Yaşam düzenlemeleri',
	LoadMore: 'Daha fazla yükle',
	Loading: 'Yükleniyor...',
	LocalizationPanelDescription: 'Dilinize ve saat diliminize ilişkin ayarı yönetin',
	LocalizationPanelTitle: 'Dil ve saat dilimi',
	Location: 'Konum',
	LocationDescription:
		'Randevuları ve video görüşmelerini planlamayı kolaylaştırmak için belirli adresler, oda adları ve sanal alan türleriyle fiziksel ve sanal konumlar ayarlayın.',
	LocationNumber: 'Konum numarası',
	LocationOfService: 'Hizmetin yeri',
	LocationOfServiceRecommendedActionInfo:
		'Bu hizmete belirli bir konum eklemek, kullanılabilirliğinizi etkileyebilir.',
	LocationRemote: 'Uzaktan',
	LocationType: 'Konum türü',
	Locations: 'Konumlar',
	Lock: 'Kilit',
	Locked: 'Kilitli',
	LockedNote: 'Kilitli not',
	LogInToSaveOrAuthoriseCard: 'Kartı kaydetmek veya yetkilendirmek için oturum açın',
	LogInToSaveOrAuthorisePayment: 'Ödemeyi kaydetmek veya yetkilendirmek için oturum açın',
	Login: 'Giriş yapmak',
	LoginButton: 'Kayıt olmak',
	LoginEmail: 'E-posta',
	LoginForgotPasswordLink: 'Parolanızı mı unuttunuz',
	LoginPassword: 'Şifre',
	Logo: 'Logo',
	LogoutAreYouSure: 'Bu cihazdaki oturumunuzu kapatın.',
	LogoutButton: 'oturumu Kapat',
	London: 'Londra',
	LongTextAnswer: 'Uzun metin yanıtı',
	LongTextFormPrimaryText: 'Uzun metin',
	LongTextFormSecondaryText: 'Paragraf stili seçenekleri',
	Male: 'Erkek',
	Manage: 'Üstesinden gelmek',
	ManageAllClientTags: 'Tüm Müşteri Etiketlerini Yönet',
	ManageAllNoteTags: 'Tüm Not Etiketlerini Yönet',
	ManageAllTemplateTags: 'Tüm Şablon Etiketlerini Yönet',
	ManageConnections: 'Bağlantıları yönet',
	ManageConnectionsGmailDescription: `Diğer ekip üyeleri senkronize edilen Gmail'inizi göremeyecektir.`,
	ManageConnectionsGoogleCalendarDescription:
		'Diğer ekip üyeleri senkronize edilen takvimlerinizi göremez. Müşteri randevuları yalnızca Carepatron içinden güncellenebilir veya silinebilir.',
	ManageConnectionsInboxSyncHelperText:
		'Senkronizasyon Gelen Kutusu ayarlarını yönetmek için lütfen Gelen Kutusu sayfasına gidin.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Diğer ekip üyeleri senkronize edilmiş takvimlerinizi göremez. Müşteri randevuları yalnızca Carepatron içinden güncellenebilir veya silinebilir.',
	ManageConnectionsOutlookDescription: `Diğer ekip üyeleri senkronize edilmiş Microsoft Outlook'unuzu göremez.`,
	ManageInboxAccountButton: 'Yeni gelen kutusu',
	ManageInboxAccountEdit: 'Gelen Kutusunu Yönet',
	ManageInboxAccountPanelTitle: 'Gelen kutuları',
	ManageInboxAssignTeamPlaceholder: 'Gelen kutusu erişimi için ekip üyelerini seçin',
	ManageInboxBasicInfoColor: 'Renk',
	ManageInboxBasicInfoDescription: 'Tanım',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Ekibiniz bu gelen kutusunu ne için kullanacak?',
	ManageInboxBasicInfoName: 'Gelen kutusu adı',
	ManageInboxBasicInfoNamePlaceholder: 'Örneğin müşteri desteği, yönetici',
	ManageInboxConnectAppAlreadyConnectedError: `Bağlanmaya çalıştığınız kanal zaten Carepatron'a bağlı`,
	ManageInboxConnectAppConnect: 'Bağlamak',
	ManageInboxConnectAppConnectedInfo: 'Bir hesaba bağlanıldı',
	ManageInboxConnectAppContinue: 'Devam etmek',
	ManageInboxConnectAppEmail: 'E-posta',
	ManageInboxConnectAppSignInWith: 'Şununla giriş yap',
	ManageInboxConnectAppSubtitle:
		'Tüm iletişimlerinizi tek bir merkezi yerden sorunsuz bir şekilde göndermek, almak ve takip etmek için uygulamalarınızı bağlayın.',
	ManageInboxNewInboxTitle: 'Yeni Gelen Kutusu',
	ManagePlan: 'Planı Yönet',
	ManageProfile: 'Profili yönet',
	ManageReferralsModalDescription:
		'Sağlık hizmetleri platformumuz hakkındaki bilgileri yaymamıza ve ödüller kazanmamıza yardımcı olun.',
	ManageReferralsModalTitle: 'Bir arkadaşınıza önerin, ödüller kazanın!',
	ManageStaffRelationshipsAddButton: 'İlişkileri yönet',
	ManageStaffRelationshipsEmptyStateText: 'Hiçbir ilişki eklenmedi',
	ManageStaffRelationshipsModalDescription:
		'Müşterilerin seçilmesi yeni ilişkiler ekleyecektir, seçimin kaldırılması ise mevcut ilişkilerin kaldırılmasına neden olacaktır.',
	ManageStaffRelationshipsModalTitle: 'İlişkileri yönet',
	ManageStatuses: 'Durumları yönet',
	ManageStatusesActiveStatusHelperText: 'En az bir aktif durum gerekli',
	ManageStatusesDescription: 'Durum etiketlerinizi özelleştirin ve iş akışınıza uygun renkleri seçin.',
	ManageStatusesSuccessSnackbar: 'Durumlar başarıyla güncellendi',
	ManageTags: 'Etiketleri yönet',
	ManageTaskAttendeeStatus: 'Randevu Durumlarını Yönet',
	ManageTaskAttendeeStatusDescription: 'Randevu durumlarınızı iş akışınıza uygun hale getirin.',
	ManageTaskAttendeeStatusHelperText: 'En az bir durum gerekli',
	ManageTaskAttendeeStatusSubtitle: 'Özel Durumlar',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manuel',
	ManualAppointment: 'El manuel randevusu',
	ManualPayment: 'Manuel ödeme',
	ManuallyTypeLocation: 'Konumu manuel olarak yazın',
	MapColumns: 'Harita sütunları',
	MappingRequired: 'Eşleştirme gerekli',
	MarkAllAsRead: 'Tümünü okundu olarak işaretle',
	MarkAsCompleted: 'Tamamlandı olarak işaretle',
	MarkAsManualSubmission: 'Gönderildi olarak işaretle',
	MarkAsPaid: 'Ödendi olarak işaretle',
	MarkAsRead: 'Okundu olarak işaretle',
	MarkAsUnpaid: 'Ödenmemiş olarak işaretle',
	MarkAsUnread: 'okunmamış olarak işaretle',
	MarkAsVoid: 'Geçersiz olarak işaretle',
	Marker: 'İşaretleyici',
	MarketingManager: 'Pazarlama Müdürü',
	MassageTherapist: 'Masaj terapisti',
	MassageTherapists: 'Masaj Terapistleri',
	MassageTherapy: 'Masaj terapisi',
	MaxBookingTimeDescription1: 'Müşteriler en fazla planlama yapabilir',
	MaxBookingTimeDescription2: 'geleceğe',
	MaxBookingTimeLabel: '{timePeriod} öncesinde',
	MaxCapacity: 'maksimum kapasite',
	Maximize: 'Büyüt',
	MaximumAttendeeLimit: 'Maksimum sınır',
	MaximumBookingTime: 'Maksimum rezervasyon süresi',
	MaximumBookingTimeError: 'Maksimum rezervasyon süresi {valueUnit}’ü geçemez.',
	MaximumMinimizedPanelsReachedDescription:
		'Bir seferde en fazla {count} yan paneli minimize edebilirsiniz. Devam etmeniz, en erken minimize edilen paneli kapatacaktır. Devam etmek istiyor musunuz?',
	MaximumMinimizedPanelsReachedTitle: 'Çok fazla paneliniz açık.',
	MechanicalEngineer: 'Makine Mühendisi',
	MediaGallery: 'Medya Galerisi',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid sağlayıcı numarası',
	MedicalAssistant: 'Tıp asistanı',
	MedicalCoder: 'Tıbbi Kodlayıcı',
	MedicalDoctor: 'Tıbbi doktor',
	MedicalIllustrator: 'Tıbbi İllüstratör',
	MedicalInterpreter: 'Tıbbi Tercüman',
	MedicalTechnologist: 'Tıbbi Teknolog',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare sağlayıcı numarası',
	Medicine: 'İlaç',
	Medium: 'Orta',
	Meeting: 'Toplantı',
	MeetingEnd: 'Toplantıyı Sonlandır',
	MeetingEnded: 'Toplantı sona erdi',
	MeetingHost: 'Toplantı sahibi',
	MeetingLowerHand: 'Elinizi indirin',
	MeetingOpenChat: 'Açık sohbet',
	MeetingPersonRaisedHand: '{name} elini kaldırdı',
	MeetingRaiseHand: 'El kaldırmak',
	MeetingReady: 'Toplantı hazır',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {dk} other {dk}} {status}',
	Meetings: 'Toplantılar',
	MemberId: 'Üye Kimliği',
	MentalHealth: 'Akıl sağlığı',
	MentalHealthPractitioners: 'Ruh Sağlığı Uygulayıcıları',
	MentalHealthProfessional: 'Ruh Sağlığı Uzmanı',
	Merge: 'Birleştir',
	MergeClientRecords: 'İstemci kayıtlarını birleştir',
	MergeClientRecordsDescription:
		'Müşteri kayıtlarını birleştirmek, aşağıdakiler de dahil olmak üzere tüm verilerini birleştirecektir:',
	MergeClientRecordsDescription2: 'Birleştirmeye devam etmek istiyor musunuz? Bu işlem geri alınamaz.',
	MergeClientRecordsItem1: 'Notlar ve belgeler',
	MergeClientRecordsItem2: 'Randevular',
	MergeClientRecordsItem3: 'Fatura',
	MergeClientRecordsItem4: 'Konuşmalar',
	MergeClientsSuccess: 'Müşteri kaydını başarıyla birleştir',
	MergeLimitExceeded: 'Bir seferde en fazla 4 istemci birleştirebilirsiniz.',
	Message: 'İleti',
	MessageAttachments: '{total} ek {ek} dosya',
	Method: 'Yöntem',
	MfaAvailabilityDisclaimer:
		'MFA yalnızca e-posta ve parola oturum açma işlemleri için kullanılabilir. MFA ayarlarınızda değişiklik yapmak için e-postanızı ve parolanızı kullanarak oturum açın.',
	MfaDeviceLostPanelDescription: 'Alternatif olarak, e-posta yoluyla bir kod alarak kimliğinizi doğrulayabilirsiniz.',
	MfaDeviceLostPanelTitle: 'MFA cihazınızı mı kaybettiniz?',
	MfaDidntReceiveEmailCode: 'Kod almadınız mı? Destek ekibiyle iletişime geçin',
	MfaEmailOtpSendFailureSnackbar: `E-posta OTP'si gönderilemedi.`,
	MfaEmailOtpSentSnackbar: '{maskedEmail} adresine bir kod gönderildi.',
	MfaEmailOtpVerificationFailedSnackbar: `E-posta OTP'si doğrulanamadı.`,
	MfaHasBeenSetUpText: `MFA'yı kurdunuz`,
	MfaPanelDescription:
		'Ek bir koruma katmanı için Çok Faktörlü Kimlik Doğrulamayı (MFA) etkinleştirerek hesabınızı güvenceye alın. Yetkisiz erişimi önlemek için kimliğinizi ikincil bir yöntemle doğrulayın.',
	MfaPanelNotAuthorizedError: 'Kullanıcı adınızla giriş yapmış olmanız gerekir ',
	MfaPanelRecommendationDescription:
		'Kimliğinizi doğrulamak için alternatif bir yöntem kullanarak yakın zamanda oturum açtınız. Hesabınızı güvende tutmak için yeni bir MFA cihazı kurmayı düşünün.',
	MfaPanelRecommendationTitle: '**Önerilen:** MFA cihazınızı güncelleyin',
	MfaPanelTitle: 'Çok Faktörlü Kimlik Doğrulama (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'MFA ayarlarınızı güncelleyebilmeniz için öncelikle e-postanızı doğrulamanız gerekmektedir.',
	MfaRecommendationBannerDescription:
		'Kimliğinizi doğrulamak için alternatif bir yöntem kullanarak yakın zamanda oturum açtınız. Hesabınızı güvende tutmak için yeni bir MFA cihazı kurmayı düşünün.',
	MfaRecommendationBannerPrimaryAction: `MFA'yı kurun`,
	MfaRecommendationBannerTitle: 'Tavsiye edilen',
	MfaRemovedSnackbarTitle: 'MFA kaldırıldı.',
	MfaSendEmailCode: 'Kod gönder',
	MfaVerifyIdentityLostDeviceButton: 'MFA cihazıma erişimimi kaybettim',
	MfaVerifyYourIdentityPanelDescription: 'Doğrulama uygulamanızda kodu kontrol edin ve aşağıya girin.',
	MfaVerifyYourIdentityPanelTitle: 'Kimliğinizi doğrulayın',
	MicCamWarningMessage:
		'Tarayıcı adres çubuğundaki engellenen simgelere tıklayarak kamera ve mikrofonun engelini kaldırın.',
	MicCamWarningTitle: 'Kamera ve mikrofon engellendi',
	MicOff: 'Mikrofon kapalı',
	MicOn: 'Mikrofon açık',
	MicSource: 'Mikrofon kaynağı',
	MicWarningMessage: 'Mikrofonunuzla ilgili bir sorun algılandı',
	Microphone: 'Mikrofon',
	MicrophonePermissionBlocked: 'Mikrofon erişimi engellendi',
	MicrophonePermissionBlockedDescription: 'Kaydı başlatmak için mikrofon izinlerinizi güncelleyin.',
	MicrophonePermissionError: 'Devam etmek için lütfen tarayıcı ayarlarınıza mikrofon izni verin',
	MicrophonePermissionPrompt: 'Lütfen mikrofon erişiminin devam etmesine izin verin',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlook takvim rengi',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Ekipleri',
	MiddleEast: 'Orta Doğu',
	MiddleName: 'İkinci ad',
	MiddleNames: 'İkinci ad',
	Midwife: 'Ebe',
	Midwives: 'Ebeler',
	Milan: 'Milano',
	MinBookingTimeDescription1: 'Müşteriler belirli bir süre içinde planlama yapamaz',
	MinBookingTimeDescription2: 'randevunun başlangıç saatinden',
	MinBookingTimeLabel: 'Randevudan {timePeriod} önce',
	MinCancellationTimeEditModeDescription: 'Bir müşterinin ceza olmadan kaç saat iptal edebileceğini ayarlayın',
	MinCancellationTimeUnset: 'Minimum iptal süresi belirlenmedi',
	MinCancellationTimeViewModeDescription: 'Cezasız iptal süresi',
	MinMaxBookingTimeUnset: 'Zaman ayarlanmadı',
	Minimize: 'küçültmek',
	MinimizeConfirmationDescription:
		'Aktif, küçültülmüş bir paneliniz var. Devam ederseniz, kapanacak ve kaydedilmemiş verilerinizi kaybedebilirsiniz.',
	MinimizeConfirmationTitle: 'Kapatılmış paneli kapat?',
	MinimumBookingTime: 'Minimum rezervasyon süresi',
	MinimumCancellationTime: 'Minimum iptal süresi',
	MinimumPaymentError: 'Çevrimiçi ödemeler için {minimumAmount} tutarında minimum ücret gereklidir.',
	MinuteAbbreviated: 'dakika',
	MinuteAbbreviation: '{count} {count, plural, one {dakika} other {dakika}}',
	Minutely: 'Dakikada',
	MinutesPlural: '{age, plural, one {# dakika} other {# dakika}}',
	MiscellaneousInformation: 'Çeşitli bilgiler',
	MissingFeatures: 'Eksik özellikler',
	MissingPaymentMethod: 'Daha fazla personel eklemek için lütfen aboneliğinize bir ödeme yöntemi ekleyin.',
	MobileNumber: 'Cep numarası',
	MobileNumberOptional: 'Cep telefonu numarası (isteğe bağlı)',
	Modern: 'Modern',
	Modifiers: 'Belirteçler',
	ModifiersPlaceholder: 'Belirteçler',
	Monday: 'Pazartesi',
	Month: 'Ay',
	Monthly: 'Aylık',
	MonthlyCost: 'Aylık Maliyet',
	MonthlyOn: '{date} tarihinde aylık',
	MonthsPlural: '{age, plural, one {# ay} other {# ay}}',
	More: 'Daha',
	MoreActions: 'Daha fazla hareket',
	MoreSettings: 'Daha fazla ayar',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'En çok kullanılan',
	MostDownloaded: 'En çok indirilenler',
	MostPopular: 'En popüler',
	Mother: 'Anne',
	MotherInLaw: 'Kayınvalide',
	MoveDown: 'Aşağı inmek',
	MoveInboxConfirmationDescription:
		'Bu uygulama bağlantısını yeniden atamak, onu <strong>{currentInboxName}</strong> gelen kutusundan kaldıracaktır.',
	MoveTemplateToFolder: '`{templateTitle}`ı taşı **',
	MoveTemplateToFolderSuccess: '{templateTitle} {folderTitle} klasörüne taşındı.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Başarıyla varsayılan alım klasörüne taşındı',
	MoveTemplateToNewFolder: 'Bu öğeyi taşımak için yeni bir klasör oluşturun.',
	MoveToChosenFolder: 'Bu öğeyi taşımak için bir klasör seçin. Gerekirse yeni bir klasör oluşturabilirsiniz.',
	MoveToFolder: 'Klasöre taşı',
	MoveToInbox: 'Gelen Kutusuna Taşı',
	MoveToNewFolder: 'Yeni klasöre taşı',
	MoveToSelectedFolder:
		'Taşındıktan sonra, öğe seçilen klasör altında düzenlenecek ve mevcut konumunda görünmeyecektir.',
	MoveUp: 'Yukarı taşı',
	MultiSpeciality: 'Çoklu uzmanlık',
	MultipleChoiceFormPrimaryText: 'Çoktan seçmeli',
	MultipleChoiceFormSecondaryText: 'Birden fazla seçenek seçin',
	MultipleChoiceGridFormPrimaryText: 'Çoktan seçmeli ızgara',
	MultipleChoiceGridFormSecondaryText: 'Bir matristen seçenekleri seçme',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Müzik Terapisti',
	MustContainOneLetterError: 'En az bir harf içermelidir',
	MustEndWithANumber: 'Bir sayıyla bitmeli',
	MustHaveAtLeastXItems: 'En az {count, plural, one {# öğe} other {# öğe}} olmalı',
	MuteAudio: 'Sesi kapat',
	MuteEveryone: 'Herkesi sessize al',
	MyAvailability: 'Mevcutluğum',
	MyGallery: 'Benim Galeri',
	MyPortal: 'Portalım',
	MyRelationships: 'İlişkilerim',
	MyTemplates: 'Takım Şablonları',
	MyofunctionalTherapist: 'Miyofonksiyonel Terapist',
	NCalifornia: 'Kuzey Kaliforniya',
	NPI: 'NPI',
	NVirginia: 'Kuzey Virginia',
	Name: 'İsim',
	NameIsRequired: 'İsim gerekli',
	NameMustNotBeAWebsite: 'Ad bir web sitesi olmamalıdır',
	NameMustNotBeAnEmail: 'Ad bir e-posta olmamalıdır',
	NameMustNotContainAtSign: 'Ad @ işaretini içermemelidir',
	NameMustNotContainHTMLTags: 'Ad HTML etiketleri içermemelidir',
	NameMustNotContainSpecialCharacters: 'Ad özel karakterler içermemelidir',
	NameOnCard: 'Karttaki isim',
	NationalProviderId: 'Ulusal sağlayıcı tanımlayıcı (NPI)',
	NaturopathicDoctor: 'Naturopatik Doktor',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Abonelik Ayarları',
	NavigateToWorkspaceSettings: 'Çalışma alanı ayarları',
	NavigateToYourTeam: 'Takımı Yönet',
	NavigationDrawerBilling: 'Fatura',
	NavigationDrawerBillingInfo: 'Fatura bilgileri, faturalar ve Stripe',
	NavigationDrawerCommunication: 'İletişim',
	NavigationDrawerCommunicationInfo: 'Bildirimler ve şablonlar',
	NavigationDrawerInsurance: 'Sigorta',
	NavigationDrawerInsuranceInfo: 'Sigorta mükellefleri ve talepleri',
	NavigationDrawerInvoices: 'Faturalama',
	NavigationDrawerPersonal: 'Benim profilim',
	NavigationDrawerPersonalInfo: 'Senin kişisel detayların',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Ayarlar',
	NavigationDrawerScheduling: 'Planlama',
	NavigationDrawerSchedulingInfo: 'Hizmet ayrıntıları ve rezervasyonlar',
	NavigationDrawerSettings: 'Ayarlar',
	NavigationDrawerTemplates: 'Şablonlar',
	NavigationDrawerTemplatesV2: 'Şablonlar V2',
	NavigationDrawerTrash: 'Çöp',
	NavigationDrawerTrashInfo: 'Silinen öğeleri geri yükle',
	NavigationDrawerWorkspace: 'Çalışma Alanı Ayarları',
	NavigationDrawerWorkspaceInfo: 'Abonelik ve çalışma alanı bilgileri',
	NegativeBalanceNotSupported: 'Negatif hesap bakiyeleri desteklenmiyor',
	Nephew: 'Yeğen',
	NetworkQualityFair: 'Orta bağlantı',
	NetworkQualityGood: 'İyi bağlantı',
	NetworkQualityPoor: 'Zayıf bağlantı',
	Neurologist: 'Nörolog',
	Never: 'Asla',
	New: 'Yeni',
	NewAppointment: 'Yeni randevu',
	NewClaim: 'Yeni iddia',
	NewClient: 'Yeni müşteri',
	NewClientNextStepsModalAddAnotherClient: 'Başka bir müşteri ekle',
	NewClientNextStepsModalBookAppointment: 'Randevu al',
	NewClientNextStepsModalBookAppointmentDescription: 'Yaklaşan bir randevuyu ayırtın veya bir görev oluşturun.',
	NewClientNextStepsModalCompleteBasicInformation: 'Tam müşteri kaydı',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Müşteri bilgilerini ekleyin ve sonraki adımları yakalayın.',
	NewClientNextStepsModalCreateInvoice: 'Fatura oluştur',
	NewClientNextStepsModalCreateInvoiceDescription: 'Müşteri ödeme bilgilerini ekleyin veya fatura oluşturun.',
	NewClientNextStepsModalCreateNote: 'Not oluşturun veya belge yükleyin',
	NewClientNextStepsModalCreateNoteDescription: 'Müşteri notlarını ve dokümantasyonunu yakalayın.',
	NewClientNextStepsModalDescription:
		'Bir istemci kaydı oluşturduğunuza göre yapmanız gereken bazı işlemler şunlardır.',
	NewClientNextStepsModalSendIntake: 'Alımı gönder',
	NewClientNextStepsModalSendIntakeDescription:
		'Müşteri bilgilerini toplayın ve doldurulup imzalanması için ek formlar gönderin.',
	NewClientNextStepsModalSendMessage: 'Mesaj gönder',
	NewClientNextStepsModalSendMessageDescription: 'Müşterinize bir mesaj yazın ve gönderin.',
	NewClientNextStepsModalTitle: 'Sonraki adımlar',
	NewClientSuccess: 'Yeni istemci başarıyla oluşturuldu',
	NewClients: 'Yeni müşteriler',
	NewConnectedApp: 'Yeni bağlı uygulama',
	NewContact: 'Yeni bağlantı',
	NewContactNextStepsModalAddRelationship: 'İlişki ekle',
	NewContactNextStepsModalAddRelationshipDescription:
		'Bu iletişim bilgilerini ilgili müşterilere veya gruplara bağlayın.',
	NewContactNextStepsModalBookAppointment: 'Randevu al',
	NewContactNextStepsModalBookAppointmentDescription: 'Yaklaşan bir randevu ayırtın veya bir görev oluşturun.',
	NewContactNextStepsModalCompleteProfile: 'Profili Tamamla',
	NewContactNextStepsModalCompleteProfileDescription: 'İletişim bilgilerini ekleyin ve sonraki adımları kaydedin.',
	NewContactNextStepsModalCreateNote: 'Not oluştur veya belge yükle',
	NewContactNextStepsModalCreateNoteDescription: 'Capture client notes and documentation.',
	NewContactNextStepsModalDescription: 'İşte bir iletişim oluşturduktan sonra yapılacak bazı eylemler.',
	NewContactNextStepsModalInviteToPortal: 'Portala davet et',
	NewContactNextStepsModalInviteToPortalDescription: 'Portala erişim davetiyesi gönder.',
	NewContactNextStepsModalTitle: 'Sonraki adımlar',
	NewContactSuccess: 'Yeni kişi başarıyla oluşturuldu',
	NewDateOverrideButton: 'Yeni tarihi geçersiz kılma',
	NewDiagnosis: 'Tanı ekle',
	NewField: 'Yeni alan',
	NewFolder: 'Yeni klasör',
	NewInvoice: 'Yeni fatura',
	NewLocation: 'Yeni konum',
	NewLocationFailure: 'Yeni konum oluşturulamadı',
	NewLocationSuccess: 'Yeni konum başarıyla oluşturuldu',
	NewManualPayer: 'Yeni manuel ödeme yapan kişi',
	NewNote: 'Yeni not',
	NewNoteCreated: 'Yeni not başarıyla oluşturuldu',
	NewPassword: 'Yeni Şifre',
	NewPayer: 'Yeni ödeyici',
	NewPaymentMethod: 'Yeni ödeme yöntemi',
	NewPolicy: 'Yeni politika',
	NewRelationship: 'Yeni ilişki',
	NewReminder: 'Yeni hatırlatıcı',
	NewSchedule: 'Yeni program',
	NewSection: 'Yeni Kısım',
	NewSectionOld: 'Yeni bölüm [ESKİ]',
	NewSectionWithGrid: 'Izgara ile yeni bölüm',
	NewService: 'Yeni hizmet',
	NewServiceFailure: 'Yeni hizmet oluşturulamadı',
	NewServiceSuccess: 'Yeni hizmet başarıyla oluşturuldu',
	NewStatus: 'Yeni statü',
	NewTask: 'Yeni görev',
	NewTaxRate: 'Yeni vergi oranı',
	NewTeamMemberNextStepsModalAssignClients: 'Müşterileri ata',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Ekibinizdeki üyelere belirli müşteriler atayın.',
	NewTeamMemberNextStepsModalAssignServices: 'Hizmetleri Ata',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Atanan hizmetleri yönetsinler ve gerekirse fiyatlandırmayı ayarlasınlar.',
	NewTeamMemberNextStepsModalBookAppointment: 'Randevu Al',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Yaklaşan bir randevu ayarla veya görev oluştur.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Profil tamamla',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Ekip üyenizin profilini tamamlamak için ayrıntılar ekleyin.',
	NewTeamMemberNextStepsModalDescription: 'İşte bir ekip üyesi oluşturduktan sonra yapabileceğiniz bazı eylemler.',
	NewTeamMemberNextStepsModalEditPermissions: 'Düzenleme izinleri',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Doğru izinlere sahip olmalarını sağlamak için erişim seviyelerini ayarlayın.',
	NewTeamMemberNextStepsModalSetAvailability: 'Kullanılabilirliği ayarla',
	NewTeamMemberNextStepsModalSetAvailabilityDescription:
		'Çizelgeler oluşturmak için kullanılabilirliklerini yapılandırın.',
	NewTeamMemberNextStepsModalTitle: 'Sonraki adımlar',
	NewTemplateFolderDescription: 'Belgelerinizi düzenlemek için yeni bir klasör oluşturun.',
	NewUIUpdateBannerButton: 'Uygulamayı yeniden yükle',
	NewUIUpdateBannerTitle: 'Yeni bir güncelleme hazır!',
	NewZealand: 'Yeni Zelanda',
	Newest: 'En yeni',
	NewestUnreplied: 'En yeni yanıtlanmamış',
	Next: 'Sonraki',
	NextInvoiceIssueDate: 'Son Fatura Kesim Tarihi',
	NextNDays: 'Sonraki {number} gün',
	Niece: 'Yeğen',
	No: 'HAYIR',
	NoAccessGiven: 'Erişim verilmedi',
	NoActionConfigured: 'Hiçbir eylem yapılandırılmadı',
	NoActivePolicies: 'Aktif politika yok',
	NoActiveReferrals: 'Aktif yönlendirmeniz yok',
	NoAppointmentsFound: 'Randevu bulunamadı',
	NoAppointmentsHeading: 'Müşteri randevularını ve etkinliklerini yönetin',
	NoArchivedPolicies: 'Arşivlenmiş politika yok',
	NoAvailableTimes: 'Uygun zaman bulunamadı.',
	NoBillingItemsFound: 'Fatura kalemleri bulunamadı.',
	NoCalendarsSynced: 'Hiçbir takvim senkronize edilmedi',
	NoClaimsFound: 'Bulunan talep yok',
	NoClaimsHeading: 'Geri ödeme için talep göndermeyi kolaylaştırın',
	NoClientsHeading: 'Müşteri kayıtlarınızı bir araya getirin',
	NoCompletedReferrals: 'Tam yönlendirmeniz yok',
	NoConnectionsHeading: 'Müşteri iletişiminizi kolaylaştırın',
	NoContactsGivenAccess: 'Bu nota hiçbir müşteriye veya kişiye erişim izni verilmedi',
	NoContactsHeading: 'Uygulamanızı destekleyen kişilerle bağlantıda kalın',
	NoCopayOrCoinsurance: 'Ortak ödeme veya ek sigorta yok',
	NoCustomServiceSchedule: 'Özel bir program ayarlanmadı; müsaitlik, ekip üyesinin müsaitliğine bağlıdır',
	NoDescription: 'Açıklama yok',
	NoDocumentationHeading: 'Notları güvenli bir şekilde oluşturun ve saklayın',
	NoDuplicateRecordsHeading: 'Müşteri kayıtlarınız kopyalardan arındırılmıştır',
	NoEffect: 'Etkisi yok',
	NoEnrolmentProfilesFound: 'Kayıt profili bulunamadı',
	NoGlossaryItems: 'Hiçbir sözlük öğesi yok',
	NoInvitedReferrals: 'Davet edilmiş yönlendirmeniz yok',
	NoInvoicesFound: 'Fatura bulunamadı',
	NoInvoicesHeading: 'Faturalandırma ve ödemelerinizi otomatikleştirin',
	NoLimit: 'Limit yok',
	NoLocationsFound: 'Hiçbir konum bulunamadı',
	NoLocationsWillBeAdded: 'Hiçbir konum eklenmeyecek.',
	NoNoteFound: 'Not bulunamadı',
	NoPaymentMethods: 'Kayıtlı ödeme yönteminiz yok, ödeme yaparken ekleyebilirsiniz.',
	NoPermissionError: 'Izniniz yok',
	NoPermissions: 'Bu sayfayı görüntüleme yetkiniz yok',
	NoPolicy: 'İptal politikası eklenmedi',
	NoRecordsHeading: 'Müşteri kayıtlarınızı kişiselleştirin',
	NoRecordsToDisplay: 'Gösterilecek {resource} yok',
	NoRelationshipsHeading: 'Müşterinizi destekleyen kişilerle bağlantıda kalın',
	NoRemindersFound: 'Hiçbir hatırlatıcı bulunamadı',
	NoResultsFound: 'Sonuç bulunamadı',
	NoResultsFoundDescription: 'Aramanıza uygun hiçbir öğe bulamıyoruz',
	NoServicesAdded: 'Eklenen hizmet yok',
	NoServicesApplied: 'Uygulanan hizmet yok',
	NoServicesWillBeAdded: 'Hiçbir hizmet eklenmeyecek.',
	NoTemplate: 'Kaydedilmiş alıştırma şablonunuz yok',
	NoTemplatesHeading: 'Kendi şablonlarınızı oluşturun',
	NoTemplatesInFolder: 'Bu klasörde şablon yok',
	NoTitle: 'Başlık yok',
	NoTrashItemsHeading: 'Silinen öğe bulunamadı',
	NoTriggerConfigured: 'Tetikleyici yapılandırılmadı',
	NoUnclaimedItemsFound: 'Talep edilmeyen ürün bulunamadı.',
	NonAiTemplates: 'Yapay zeka olmayan şablonlar',
	None: 'Hiçbiri',
	NotAvailable: 'Mevcut değil',
	NotCovered: 'Kapsanmamış',
	NotFoundSnackbar: 'Kaynak bulunamadı.',
	NotRequiredField: 'Norunlu değil',
	Note: 'Not',
	NoteDuplicateSuccess: 'Başarıyla kopyalanan not',
	NoteEditModeViewSwitcherDescription: 'Not oluştur ve düzenle',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} {noteTitle} formunu gönderdi',
	NoteLockSuccess: '{title} kilitlendi',
	NoteModalAttachmentButton: 'Ek ekle',
	NoteModalPhotoButton: 'Fotoğraf ekle/yakala',
	NoteModalTrascribeButton: 'Canlı sesi metne dönüştürün',
	NoteResponderModeViewSwitcherDescription: 'Formları gönderin ve yanıtları inceleyin',
	NoteResponderModeViewSwitcherTooltipTitle: 'Müşterileriniz adına formlara yanıt verin ve gönderin',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Müşteri olarak formları doldurun ve gönderin',
	NoteUnlockSuccess: '{title} kilitlendi',
	NoteViewModeViewSwitcherDescription: 'Yalnızca görüntüleme erişimi',
	Notes: 'Notlar',
	NotesAndForms: 'Notlar ve Formlar',
	NotesCategoryDescription: 'Müşteri etkileşimlerini belgelemek için',
	NothingToSeeHere: 'Burada görülecek bir şey yok',
	Notification: 'Bildiri',
	NotificationIgnoredMessage: 'Tüm {notificationType} bildirimleri yoksayılacak.',
	NotificationRestoredMessage: 'Tüm {notificationType} bildirimleri geri yüklendi',
	NotificationSettingBillingDescription: 'Müşteri ödeme güncellemeleri ve hatırlatmaları için bildirim alın.',
	NotificationSettingBillingTitle: 'Faturalandırma ve ödeme',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} yalnızca} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Müşteri ödeme güncellemeleri ve hatırlatmaları için bildirim alın.',
	NotificationSettingClientDocumentationTitle: 'Müşteri ve belge yönetimi',
	NotificationSettingCommunicationsDescription:
		'Bağlı kanallarınızdan gelen kutunuz ve güncellemeler için bildirimler alın',
	NotificationSettingCommunicationsTitle: 'İletişimler',
	NotificationSettingEmail: 'E-posta',
	NotificationSettingInApp: 'Uygulama içi',
	NotificationSettingPanelDescription: 'Aktiviteler ve öneriler için almak istediğiniz bildirimleri seçin.',
	NotificationSettingPanelTitle: 'Bildirim tercihleri',
	NotificationSettingSchedulingDescription:
		'Bir ekip üyesi veya müşteri randevusunu ayırdığında, yeniden planladığında veya iptal ettiğinde bildirim alın.',
	NotificationSettingSchedulingTitle: 'Zamanlama',
	NotificationSettingUpdateSuccess: 'Bildirim ayarları başarıyla güncellendi',
	NotificationSettingWhereYouReceiveNotifications: 'Bu bildirimleri nerede almak istiyorsunuz',
	NotificationSettingWorkspaceDescription:
		'Sistem değişiklikleri, sorunlar, veri aktarımları ve abonelik hatırlatmaları hakkında bildirim alın.',
	NotificationSettingWorkspaceTitle: 'Çalışma alanı',
	NotificationTemplateUpdateFailed: 'Bildirim şablonu güncellenemedi',
	NotificationTemplateUpdateSuccess: 'Bildirim şablonu başarıyla güncellendi',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Katılımcılara iptal bildirimi e-postası göndermek ister misiniz?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'İptal gönder',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Katılımcılara bir onay bildirimi e-postası göndermek ister misiniz?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Onay gönder',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Katılımcılara iptal e-postası göndermek ister misiniz?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {yapmıyor} other {yapmıyor}} e-posta adresi olmadığı için otomatik bildirimler ve hatırlatmalar almayacak.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {bir} other {bir}} e-posta adresi bulunmadığı için otomatik bildirimler ve hatırlatıcılar almayacak.',
	NotifyAttendeesOfTaskModalTitle: 'Katılımcılara bildirim e-postası göndermek ister misiniz?',
	NotifyAttendeesOfTaskSnackbar: 'Bildirim gönderiliyor',
	NuclearMedicineTechnologist: 'Nükleer Tıp Teknolojisti',
	NumberOfClaims: '{number, plural, one {# Talep} other {# Talepler}}',
	NumberOfClients: '{number, plural, one {# Müşteri} other {# Müşteriler}}',
	NumberOfContacts: '{number, plural, one {# İletişim} other {# İletişimler}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {giriş} other {girişler}} bulundu',
	NumberOfErrors: '{count, plural, one {# hata} other {# hatalar}}',
	NumberOfInvoices: '{number, plural, one {# Fatura} other {# Faturalar}}',
	NumberOfLineitemsToCredit:
		'Kredi vermek için <mark>{count} {count, plural, one {satır öğesi} other {satır öğeleri}}</mark>ünüz var.',
	NumberOfPayments: '{number, plural, one {# Ödeme} other {# Ödemeler}}',
	NumberOfRelationships: '{number, plural, one {# İlişki} other {# İlişkiler}}',
	NumberOfResources: '{number, plural, one {# Kaynak} other {# Kaynaklar}}',
	NumberOfTeamMembers: '{number, plural, one {# Ekip üyesi} other {# Ekip üyeleri}}',
	NumberOfTrashItems: '{number, plural, one {# öğe} other {# öğeler}}',
	NumberOfUninvoicedAmounts:
		'<mark>{count} faturalanmamış {count, plural, one {tutar} other {tutarlar}}</mark> fatura edilmeyi bekliyor',
	NumberedList: 'Numaralı liste',
	Nurse: 'Hemşire',
	NurseAnesthetist: 'Hemşire Anestezi Uzmanı',
	NurseAssistant: 'Hemşire asistanı',
	NurseEducator: 'Hemşire Eğitimcisi',
	NurseMidwife: 'Hemşire Ebe',
	NursePractitioner: 'Pratisyen hemşire',
	Nurses: 'Hemşireler',
	Nursing: 'Hemşirelik',
	Nutritionist: 'Diyetisyen',
	Nutritionists: 'Beslenme uzmanları',
	ObstetricianOrGynecologist: 'Kadın Doğum Uzmanı/Jinekolog',
	Occupation: 'Meslek',
	OccupationalTherapist: 'Mesleki terapist',
	OccupationalTherapists: 'Mesleki Terapistler',
	OccupationalTherapy: 'İş terapisi',
	Occurrences: 'Olaylar',
	Of: 'ile ilgili',
	Ohio: 'Ohio',
	OldPassword: 'Eski Şifre',
	OlderMessages: '{count} eski mesaj',
	Oldest: 'En eski',
	OldestUnreplied: 'En eski yanıtlanmayan',
	On: 'Açık',
	OnboardingBusinessAgreement: 'Benim ve işletme adına, {businessAssociateAgreement} ile  **uyuşuyorum**.',
	OnboardingLoadingOccupationalTherapist: `<mark>Mesleki terapistler</mark> Carepatron'daki müşterilerimizin dörtte birini oluşturuyoruz`,
	OnboardingLoadingProfession: `Carepatron'u kullanan ve gelişen bir sürü <mark>{meslek}</mark> var.`,
	OnboardingLoadingPsychologist: `<mark>Psikologlar</mark> Carepatron'daki müşterilerimizin yarısından fazlasını oluşturuyoruz`,
	OnboardingLoadingSubtitleFive: 'Misyonumuz yapmaktır<mark> erişilebilir sağlık yazılımı</mark> Herkes için.',
	OnboardingLoadingSubtitleFour: `<mark>Basitleştirilmiş sağlık yazılımı</mark> dünya çapında 10.000'den fazla insan için.`,
	OnboardingLoadingSubtitleThree: `Kaydetmek<mark> haftada 1 gün</mark> Carepatron'un yardımıyla yönetim görevlerinde.`,
	OnboardingLoadingSubtitleTwo: `Kaydetmek<mark> 2 saat</mark> Carepatron'un yardımıyla günlük olarak yönetim görevlerinde bulunuyorum.`,
	OnboardingReviewLocationOne: 'Holland Park Ruh Sağlığı Merkezi',
	OnboardingReviewLocationThree: 'Uygulama Hemşiresi, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Yaşam Evi Kliniği',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E.',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne: `&quot;Carepatron'un kullanımı son derece sezgisel. Muayenehanemizi o kadar iyi yürütmemize yardımcı oluyor ki artık bir yönetici ekibine bile ihtiyacımız yok&quot;`,
	OnboardingReviewThree:
		'&quot;Hem özellikler hem de maliyet açısından kullandığım en iyi uygulama çözümü. İşimi büyütmek için ihtiyacım olan her şeye sahip&quot;',
	OnboardingReviewTwo:
		'&quot;Carepatron uygulamasını da seviyorum. Müşterilerimi takip etmeme ve hareket halindeyken çalışmama yardımcı oluyor.&quot;',
	OnboardingTitle: `hadi gidelim<mark> Bilmek
 daha iyisin</mark>`,
	Oncologist: 'Onkolog',
	Online: 'Çevrimiçi',
	OnlineBookingColorTheme: 'Çevrimiçi rezervasyon renk teması',
	OnlineBookings: 'Çevrimiçi rezervasyonlar',
	OnlineBookingsHelper:
		'Çevrimiçi rezervasyonların ne zaman ve hangi tür müşteriler tarafından yapılabileceğini seçin',
	OnlinePayment: 'Online ödeme',
	OnlinePaymentSettingCustomInfo:
		'Bu hizmete ilişkin çevrimiçi ödeme ayarları genel rezervasyon ayarlarından farklıdır.',
	OnlinePaymentSettings: 'Çevrimiçi ödeme ayarları',
	OnlinePaymentSettingsInfo:
		'Ödemeleri güvence altına almak ve kolaylaştırmak için çevrimiçi rezervasyon sırasında hizmetler için ödemeleri toplayın',
	OnlinePaymentSettingsPaymentsDisabled:
		'Ödemeler devre dışı bırakıldığı için çevrimiçi rezervasyon sırasında toplanamaz. Ödemeleri etkinleştirmek için ödeme ayarlarınızı kontrol edin.',
	OnlinePaymentSettingsStripeNote:
		'{action} ile online rezervasyon ödemeleri almak ve ödeme sürecinizi kolaylaştırmak',
	OnlinePaymentsNotSupportedForCurrency: '{currency} içinde çevrimiçi ödemeler desteklenmiyor.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Üzgünüz, çevrimiçi ödemeler bu para biriminde desteklenmiyor',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: 'Üzgünüz, ülkenizde çevrimiçi ödemeler henüz desteklenmiyor',
	OnlineScheduling: 'Çevrimiçi Planlama',
	OnlyVisibleToYou: 'Yalnızca Size görünür',
	OnlyYou: 'Sadece sen',
	OnsetDate: 'Başlangıç tarihi',
	OnsetOfCurrentSymptomsOrIllness: 'Mevcut semptomların veya hastalığın başlangıcı',
	Open: 'Açık',
	OpenFile: 'Açık dosya',
	OpenSettings: 'Ayarları aç',
	Ophthalmologist: 'Göz doktoru',
	OptimiseTelehealthCalls: 'Telesağlık çağrılarınızı optimize edin',
	OptimizeServiceTimes: 'Servis sürelerini optimize edin',
	Options: 'Seçenekler',
	Optometrist: 'göz doktoru',
	Or: 'veya',
	OrAttachSingleFile: 'dosya ekle',
	OrDragAndDrop: 'veya sürükle bırak',
	OrderBy: 'Tarafından sipariş',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Kuruluş veya birey',
	OrganizationPlanInclusion1: 'Gelişmiş izinler',
	OrganizationPlanInclusion2: 'Ücretsiz istemci verilerini içe aktarma desteği',
	OrganizationPlanInclusion3: 'Özel başarı yöneticisi',
	OrganizationPlanInclusionHeader: `Professional'daki her şey artı...`,
	Orthodontist: 'Ortodontist',
	Orthotist: 'Ortotist',
	Other: 'Diğer',
	OtherAdjustments: 'Diğer ayarlamalar',
	OtherAdjustmentsTableEmptyState: 'Hiçbir ayarlama bulunamadı',
	OtherEvents: 'Diğer olaylar',
	OtherId: 'Diğer Kimlik',
	OtherIdQualifier: 'Diğer kimlik niteleyicisi',
	OtherPaymentMethod: 'Diğer ödeme yöntemi',
	OtherPlanMessage:
		'Uygulamanızın ihtiyaçlarını kontrol altında tutun. Mevcut planınızı inceleyin, kullanımı izleyin ve ekibiniz büyüdükçe daha fazla özelliğin kilidini açmak için yükseltme seçeneklerini keşfedin.',
	OtherPolicy: 'Diğer sigortalar',
	OtherProducts: 'Başka hangi ürünleri veya araçları kullanıyorsunuz?',
	OtherServices: 'Diğer hizmetler',
	OtherTemplates: 'Diğer şablonlar',
	Others: 'Diğerleri',
	OthersPeople: `{n, plural, 		one {1 başka kişi}
		other {# diğer kişiler}
	}`,
	OurResearchTeamReachOut: `Carepatron'un ihtiyaçlarınıza göre nasıl daha iyi olabileceği konusunda daha fazla bilgi edinmek için araştırma ekibimiz size ulaşabilir mi?`,
	OutOfOffice: 'Ofis dışında',
	OutOfOfficeColor: 'Ofis dışında renk',
	OutOfOfficeHelper: 'Seçilen ekip üyelerinden bazıları ofis dışında',
	OutsideLabCharges: 'Dış laboratuvar ücretleri',
	OutsideOfWorkingHours: 'Çalışma saatleri dışında',
	OutsideWorkingHoursHelper: 'Seçilen ekip üyelerinden bazıları çalışma saatleri dışında',
	Overallocated: 'Aşırı tahsis edilmiş',
	OverallocatedPaymentDescription: `Bu ödeme faturalandırılabilir kalemlere fazla tahsis edildi.
 Ödenmemiş kalemlere tahsisat ekleyin veya kredi veya geri ödeme yapın.`,
	OverallocatedPaymentTitle: 'Fazla tahsis edilmiş ödeme',
	OverdueTerm: 'Gecikme süresi (gün)',
	OverinvoicedAmount: 'Fazla faturalanan miktar',
	Overpaid: 'Fazla ödenmiş',
	OverpaidAmount: 'Fazla ödenen tutar',
	Overtime: 'mesai',
	Owner: 'Mal sahibi',
	POS: 'POS',
	POSCode: 'POS kodu',
	POSPlaceholder: 'Satış noktası',
	PageBlockerDescription: 'Kaydedilmemiş değişiklikler kaybolacaktır. Yine de ayrılmak istiyor musunuz?',
	PageBlockerTitle: 'Değişiklikleri at?',
	PageFormat: 'Sayfa biçimi',
	PageNotFound: 'sayfa bulunamadı',
	PageNotFoundDescription: 'Artık bu sayfaya erişiminiz yok veya sayfa bulunamıyor',
	PageUnauthorised: 'Yetkisiz erişim',
	PageUnauthorisedDescription: 'Bu sayfaya erişim izniniz yok',
	Paid: 'Paralı',
	PaidAmount: 'Ödenen miktar',
	PaidAmountMinimumValueError: `Ödenen tutar 0'dan büyük olmalıdır`,
	PaidAmountRequiredError: 'Ödenen tutar gerekli',
	PaidItems: 'Ücretli ürünler',
	PaidMultiple: 'Paralı',
	PaidOut: 'Ödendi',
	ParagraphStyles: 'Paragraf stilleri',
	Parent: 'Ebeveyn',
	Paris: 'Paris',
	PartialRefundAmount: 'Kısmen iade edildi ({amount} kalan)',
	PartiallyFull: 'Kısmen dolu',
	PartiallyPaid: 'Kısmen ödenmiş',
	PartiallyRefunded: 'Kısmen iade edildi',
	Partner: 'Partner',
	Password: 'Şifre',
	Past: 'Geçmiş',
	PastDateOverridesEmpty: 'Etkinlik sona erdiğinde tarih geçersiz kılmalarınız burada görünecek',
	Pathologist: 'Patolog',
	Patient: 'Hasta',
	Pause: 'Duraklama',
	Paused: 'Duraklatıldı',
	Pay: 'Ödemek',
	PayMonthly: 'Aylık ödeme yapın',
	PayNow: 'Şimdi öde',
	PayValue: '{showPrice, select, true {{price}} other {şimdi}}',
	PayWithOtherCard: 'Başka kartla öde',
	PayYearly: 'Yıllık öde',
	PayYearlyPercentOff: 'Yıllık ödeme yapın <mark>{percent}% indirim</mark>',
	Payer: 'Ödeyen',
	PayerClaimId: 'Ödemeci talebi kimliği',
	PayerCoverage: 'Kapsam',
	PayerDetails: 'Ödeyen ayrıntıları',
	PayerDetailsDescription: 'Hesabınıza eklenen ödeme yapan kişinin ayrıntılarını görüntüleyin ve kaydı yönetin.',
	PayerID: 'Ödeme yapan kimliği',
	PayerId: 'Ödeme yapan kimliği',
	PayerName: 'Ödemeyi yapanın adı',
	PayerPhoneNumber: 'Ödeyen telefon numarası',
	Payers: 'Ödeme yapanlar',
	Payment: 'Ödeme',
	PaymentAccountUpdated: 'Hesabın güncellendi!',
	PaymentAccountUpgraded: 'Hesabınız yükseltildi!',
	PaymentAmount: 'Ödeme tutarı',
	PaymentDate: 'Ödeme tarihi',
	PaymentDetails: 'Ödeme bilgileri',
	PaymentForUsersPerMonth: '{billedUsers, plural, one {# kullanıcı} other {# kullanıcı}} aylık ödeme',
	PaymentInfoFormPrimaryText: 'Ödeme bilgileri',
	PaymentInfoFormSecondaryText: 'Ödeme ayrıntılarını toplayın',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Bu fatura zaten ödendi.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Bu fatura zaten işleniyor.',
	PaymentIntentAmountMismatchSnackbar:
		'Faturanın toplam tutarı değiştirildi. Lütfen ödeme yapmadan önce değişiklikleri inceleyin.',
	PaymentIntentSyncTimeoutSnackbar:
		'Ödemeniz başarılı oldu ancak zaman aşımı oluştu. Lütfen sayfayı yenileyin ve ödemeniz gösterilmezse lütfen destek ekibiyle iletişime geçin.',
	PaymentMethod: 'Ödeme yöntemi',
	PaymentMethodDescription:
		'Abonelik faturalandırma sürecinizi kolaylaştırmak için pratik ödeme yönteminizi ekleyin ve yönetin.',
	PaymentMethodLabelBank: 'banka hesabı',
	PaymentMethodLabelCard: 'kart',
	PaymentMethodLabelFallback: 'ödeme yöntemi',
	PaymentMethodRequired: 'Abonelikleri değiştirmeden önce lütfen bir ödeme yöntemi ekleyin',
	PaymentMethods: 'Ödeme metodları',
	PaymentProcessing: 'Odeme yapiliyor!',
	PaymentProcessingFee: 'Ödeme, {amount} işlem ücreti içerir.',
	PaymentReports: 'Ödeme raporları (ERA)',
	PaymentSettings: 'Ödeme ayarları',
	PaymentSuccessful: 'Ödeme başarılı!',
	PaymentType: 'Ödeme türü',
	Payments: 'Ödemeler',
	PaymentsAccountDisabledNotificationSubject: `{paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} üzerinden çevrimiçi ödemeler devre dışı bırakıldı.
Lütfen ödemeleri etkinleştirmek için ödeme ayarlarınızı kontrol edin.`,
	PaymentsEmptyStateDescription: 'Hiçbir ödeme bulunamadı.',
	PaymentsUnallocated: 'Tahsis edilmemiş ödemeler',
	PayoutDate: 'Ödeme tarihi',
	PayoutsDisabled: 'Ödemeler devre dışı bırakıldı',
	PayoutsEnabled: 'Ödemeler etkinleştirildi',
	PayoutsStatus: 'Ödeme durumu',
	Pediatrician: 'Çocuk Doktoru',
	Pen: 'Dolma kalem',
	Pending: 'Askıda olması',
	People: '{rosterSize } kişi',
	PeopleCount: 'Kişi ({count})',
	PerMonth: '/ Ay',
	PerUser: 'Kullanıcı tarafından',
	Permission: 'İzin',
	PermissionRequired: 'İzin gerekli',
	Permissions: 'İzinler',
	PermissionsClientAndContactDocumentation: 'Müşteri ',
	PermissionsClientAndContactProfiles: 'Müşteri ',
	PermissionsEditAccess: 'Erişimi düzenle',
	PermissionsInvoicesAndPayments: 'Faturalar ',
	PermissionsScheduling: 'Planlama',
	PermissionsUnassignClients: 'İstemcilerin atamasını kaldır',
	PermissionsUnassignClientsConfirmation: 'Bu istemcilerin atamasını kaldırmak istediğinizden emin misiniz?',
	PermissionsValuesAssigned: 'Yalnızca atanmış',
	PermissionsValuesEverything: 'Her şey',
	PermissionsValuesNone: 'Hiçbiri',
	PermissionsValuesOwnCalendar: 'Kendi takvimim',
	PermissionsViewAccess: 'Erişimi görüntüle',
	PermissionsWorkspaceSettings: 'Çalışma alanı ayarları',
	Person: '{rosterSize} kişi',
	PersonalDetails: 'Kişisel detaylar',
	PersonalHealthcareHistoryStoreDescription:
		'Kişisel sağlık geçmişinizi tek bir yerde yanıtlayın ve güvenli bir şekilde saklayın',
	PersonalTrainer: 'Kişisel antrenör',
	PersonalTraining: 'Kişisel Gelişim',
	PersonalizeWorkspace: 'Çalışma alanınızı kişiselleştirin',
	PersonalizingYourWorkspace: 'Çalışma alanınızı kişiselleştirme',
	Pharmacist: 'Eczacı',
	Pharmacy: 'Eczane',
	PhoneCall: 'Telefon görüşmesi',
	PhoneNumber: 'Telefon numarası',
	PhoneNumberOptional: 'telefon numarası (isteğe bağlı)',
	PhotoBy: 'fotoğrafı çeken',
	PhysicalAddress: 'Fiziksel adres',
	PhysicalTherapist: 'Fizyoterapist',
	PhysicalTherapists: 'Fiziksel terapistler',
	PhysicalTherapy: 'Fizik Tedavi',
	Physician: 'Doktor',
	PhysicianAssistant: 'Hekim Asistanı',
	Physicians: 'Doktorlar',
	Physiotherapist: 'Fizyoterapist',
	PlaceOfService: 'Hizmet yeri',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Rapor',
	PlanId: 'Plan Kimliği',
	PlansAndReportsCategoryDescription: 'Tedavi planlaması ve sonuçların özetlenmesi için',
	PleaseRefreshThisPageToTryAgain: 'Lütfen tekrar denemek için bu sayfayı yenileyin.',
	PleaseWait: 'Lütfen bekleyin...',
	PleaseWaitForHostToJoin: 'Toplantı Sahibinin katılması bekleniyor...',
	PleaseWaitForHostToStart: 'Lütfen Toplantı Sahibinin bu toplantıyı başlatmasını bekleyin.',
	PlusAdd: '+ Ekle',
	PlusOthers: '+{count} diğerleri',
	PlusPlanInclusionFive: 'Paylaşılan gelen kutuları',
	PlusPlanInclusionFour: 'Grup görüntülü görüşmeleri',
	PlusPlanInclusionHeader: `Essential'daki her şey  `,
	PlusPlanInclusionOne: 'Sınırsız AI',
	PlusPlanInclusionSix: 'Özel markalama',
	PlusPlanInclusionThree: 'Grup planlaması',
	PlusPlanInclusionTwo: 'Sınırsız depolama ',
	PlusSubscriptionPlanSubtitle: 'Uygulamaların optimize edilmesi ve büyümesi için',
	PlusSubscriptionPlanTitle: 'Artı',
	PoliceOfficer: 'Polis memuru',
	PolicyDates: 'Poliçe tarihleri',
	PolicyHolder: 'Poliçe sahibi',
	PolicyHoldersAddress: 'Poliçe Sahibi Adresi',
	PolicyMemberId: 'Poliçe Üyesi Kimliği',
	PolicyStatus: 'Politika durumu',
	Popular: 'Popüler',
	PortalAccess: 'Portal erişimi',
	PortalNoAppointmentsHeading: 'Gelecek ve geçmiş tüm randevuları takip edin',
	PortalNoDocumentationHeading: 'Belgelerinizi güvenli bir şekilde oluşturun ve saklayın',
	PortalNoRelationshipsHeading: 'Yolculuğunuzu destekleyen kişileri bir araya getirin',
	PosCodeErrorMessage: 'POS kodu gereklidir',
	PosoNumber: 'PO/SO numarası',
	PossibleClientDuplicate: 'Olası istemci kopyası',
	PotentialClientDuplicateTitle: 'Olası yinelenen müşteri kaydı',
	PotentialClientDuplicateWarning:
		'Bu müşteri bilgileri müşteri listenizde zaten mevcut olabilir. Lütfen gerekirse mevcut kaydı doğrulayın ve güncelleyin veya yeni müşteri oluşturmaya devam edin.',
	PoweredBy: 'Tarafından desteklenmektedir',
	Practice: 'Pratik',
	PracticeDetails: 'Alıştırma ayrıntıları',
	PracticeInfoHeader: 'İş Bilgileri',
	PracticeInfoPlaceholder: `Uygulama adı,
 Ulusal sağlayıcı tanımlayıcı,
 İşveren kimlik numarası`,
	PracticeLocation: 'Görünüşe göre antrenmanın devam ediyor',
	PracticeSettingsAvailabilityTab: 'Kullanılabilirlik',
	PracticeSettingsBillingTab: 'Faturalandırma ayarları',
	PracticeSettingsClientSettingsTab: 'İstemci ayarları',
	PracticeSettingsGeneralTab: 'Genel',
	PracticeSettingsOnlineBookingTab: 'Çevrimiçi rezervasyon',
	PracticeSettingsServicesTab: 'Hizmetler',
	PracticeSettingsTaxRatesTab: 'Vergi oranları',
	PracticeTemplate: 'Alıştırma Şablonu',
	Practitioner: 'Uygulayıcı',
	PreferredLanguage: 'Tercih edilen dil',
	PreferredName: 'Tercih edilen isim',
	Prescription: 'Reçeteli',
	PreventionSpecialist: 'Önleme Uzmanı',
	Preview: 'Ön izleme',
	PreviewAndSend: 'Önizleyin ve gönderin',
	PreviewUnavailable: 'Bu dosya türü için önizleme kullanılamıyor',
	PreviousNotes: 'Önceki notlar',
	Price: 'Fiyat',
	PriceError: `Fiyat 0'dan büyük olmalıdır`,
	PricePerClient: 'Müşteri başına fiyat',
	PricePerUser: 'Kullanıcı başına',
	PricePerUserBilledAnnually: 'Yıllık olarak faturalandırılan kullanıcı başına',
	PricePerUserPerPeriod: '{price} kullanıcı başına / {isMonthly, select, true {ay} other {yıl}}',
	PricingGuide: 'Fiyatlandırma planlarına ilişkin kılavuz',
	PricingPlanPerMonth: '/ ay',
	PricingPlanPerYear: '/ yıl',
	Primary: 'Öncelik',
	PrimaryInsurance: 'Birincil sigorta',
	PrimaryPolicy: 'Birincil sigorta',
	PrimaryTimezone: 'Birincil saat dilimi',
	Print: 'Yazdır',
	PrintToCms1500: `CMS1500'e yazdır`,
	PrivatePracticeConsultant: 'Özel Muayenehane Danışmanı',
	Proceed: 'Devam Et',
	ProcessAtTimeOfBookingDesc:
		'Müşterilerin çevrimiçi rezervasyon yapmak için hizmet bedelinin tamamını ödemesi gerekir',
	ProcessAtTimeOfBookingLabel: 'Ödemeleri rezervasyon sırasında işleme koyun',
	Processing: 'İşleme',
	ProcessingFee: 'İşlem ücreti',
	ProcessingFeeToolTip: `Carepatron, müşterilerinizden işlem ücretlerini tahsil etmenize olanak tanır.
 Bazı yargı bölgelerinde müşterilerinizden işlem ücreti talep etmek yasaktır. Geçerli yasalara uymak sizin sorumluluğunuzdadır.`,
	ProcessingRequest: 'İstek işleniyor...',
	Product: 'Ürün',
	Profession: 'Meslek',
	ProfessionExample: 'Terapist, Beslenme Uzmanı, Diş Hekimi',
	ProfessionPlaceholder: 'Mesleğinizi yazmaya başlayın veya listeden seçim yapın',
	ProfessionalPlanInclusion1: 'Sınırsız depolama',
	ProfessionalPlanInclusion2: 'Sınırsız görev',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '7/24 müşteri desteği',
	ProfessionalPlanInclusion5: 'SMS hatırlatıcıları',
	ProfessionalPlanInclusionHeader: `Starter'daki her şeye ek olarak...`,
	Professions: 'Meslekler',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: '5 MB dosya boyutu sınırı',
	ProfilePopoverSubTitle: '<strong>{email}</strong> olarak giriş yaptınız.',
	ProfilePopoverTitle: 'Çalışma alanlarınız',
	PromoCode: 'Promosyon kodu',
	PromotionCodeApplied: '{promo} uygulandı',
	ProposeNewDateTime: 'Yeni bir tarih/saat önerin',
	Prosthetist: 'Protezci',
	Provider: 'Sağlayıcı',
	ProviderBillingPlanExpansionManageButton: 'Planı yönet',
	ProviderCommercialNumber: 'Sağlayıcı ticari numarası',
	ProviderDetails: 'Sağlayıcı ayrıntıları',
	ProviderDetailsAddress: 'Adres',
	ProviderDetailsName: 'İsim',
	ProviderDetailsPhoneNumber: 'Telefon numarası',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Üzgünüz, bu sağlayıcının zaten mevcut bir faturalandırma hesabı var',
	ProviderInfoPlaceholder: `Personel ismi,
 E-posta adresi,
 Telefon numarası,
 Ulusal sağlayıcı tanımlayıcı,
 Lisans numarası`,
	ProviderIsChargedProcessingFee: 'İşlem ücretini ödeyeceksiniz',
	ProviderPaymentFormBackButton: 'Geri',
	ProviderPaymentFormBillingAddressCity: 'Şehir',
	ProviderPaymentFormBillingAddressCountry: 'Ülke',
	ProviderPaymentFormBillingAddressLine1: 'Satır 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Posta Kodu',
	ProviderPaymentFormBillingEmail: 'E-posta',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Kredi kartı detayları',
	ProviderPaymentFormCardExpiry: 'son kullanma tarihi',
	ProviderPaymentFormCardHolderAddressTitle: 'Adres',
	ProviderPaymentFormCardHolderName: 'Kart Sahibinin Adı',
	ProviderPaymentFormCardHolderTitle: 'Kart Sahibi Detayları',
	ProviderPaymentFormCardNumber: 'Kart numarası',
	ProviderPaymentFormPlanTitle: 'Seçilen plan',
	ProviderPaymentFormPlanTotalTitle: 'Toplam ({currency}):',
	ProviderPaymentFormSaveButton: 'Aboneliği kaydet',
	ProviderPaymentFreePlanDescription:
		'Ücretsiz planı seçmek, her personelin sağlayıcınızdaki müşterilerine erişimini kaldıracaktır. Ancak erişiminiz kalacak ve platformu kullanmaya devam edebileceksiniz.',
	ProviderPaymentStepName: 'Gözden geçirmek ',
	ProviderPaymentSuccessSnackbar: 'Harika! Yeni planınız başarıyla kaydedildi.',
	ProviderPaymentTitle: 'Gözden geçirmek ',
	ProviderPlanNetworkIdentificationNumber: 'Sağlayıcı planı ağ tanımlama numarası',
	ProviderRemindersSettingsBannerAction: 'İş Akışı Yönetimine Git',
	ProviderRemindersSettingsBannerDescription:
		'Yeni **İş Akışı Yönetimi** sekmesinde, **Ayarlar** altında tüm hatırlatıcıları bulun. Bu güncelleme, üretkenliğinizi artırmak için güçlü yeni özellikler, geliştirilmiş şablonlar ve daha akıllı otomasyon araçları getiriyor. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Hatırlatıcı deneyiminiz daha iyiye gidiyor',
	ProviderTaxonomy: 'Sağlayıcı sınıflandırması',
	ProviderUPINNumber: 'Sağlayıcı UPIN numarası',
	ProviderUsedStoragePercentage: '{providerName} depolama alanı % {usedStoragePercentage} dolu!',
	PsychiatricNursePractitioner: 'Psikiyatri Hemşiresi Uygulayıcısı',
	Psychiatrist: 'Psikiyatrist',
	Psychiatrists: 'Psikiyatristler',
	Psychiatry: 'Psikiyatri',
	Psychoanalyst: 'Psikanalist',
	Psychologist: 'Psikolog',
	Psychologists: 'Psikologlar',
	Psychology: 'Psikoloji',
	Psychometrician: 'Psikometrist',
	PsychosocialRehabilitationSpecialist: 'Psikososyal Rehabilitasyon Uzmanı',
	Psychotheraphy: 'Psikoterapi',
	Psychotherapists: 'Psikoterapistler',
	Psychotherapy: 'Psikoterapi',
	PublicCallDialogTitle: 'ile görüntülü görüşme ',
	PublicCallDialogTitlePlaceholder: 'Carepatron tarafından desteklenen video görüşmesi',
	PublicFormBackToForm: 'Başka bir yanıt gönder',
	PublicFormConfirmSubmissionHeader: 'Gönderimi Onayla',
	PublicFormNotFoundDescription: `Aradığınız form silinmiş veya bağlantı yanlış olabilir. Lütfen URL'yi kontrol edin ve tekrar deneyin.`,
	PublicFormNotFoundTitle: 'Form bulunamadı',
	PublicFormSubmissionError: 'Gönderim başarısız oldu. Lütfen tekrar deneyin.',
	PublicFormSubmissionSuccess: 'Form başarıyla gönderildi',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} {noteTitle} kamu formunu gönderdi.',
	PublicFormSubmittedSubtitle: 'Gönderiniz alındı.',
	PublicFormSubmittedTitle: 'Teşekkür ederim!',
	PublicFormVerifyClientEmailDialogSubtitle: 'E-postanıza bir onay kodu gönderdik.',
	PublicFormsInvalidConfirmationCode: 'Geçersiz onay kodu',
	PublicHealthInspector: 'Halk Sağlığı Müfettişi',
	PublicTemplates: 'Genel şablonlar',
	Publish: 'Yayınla',
	PublishTemplate: 'Şablonu yayınla',
	PublishTemplateFeatureBannerSubheader: 'Topluluğa fayda sağlamak için tasarlanmış şablonlar',
	PublishTemplateHeader: '{title} Yayınla',
	PublishTemplateToCommunity: 'Toplulukta şablonu yayınla',
	PublishToCommunity: 'Topluluğa yayınla',
	PublishToCommunitySuccessMessage: 'Topluluğa başarıyla yayınlandı',
	Published: 'Yayınlanan',
	PublishedBy: '{name} tarafından yayınlandı',
	PublishedNotesAreNotAutosaved: 'Yayınlanan notlar otomatik olarak kaydedilmeyecek',
	PublishedOnCarepatronCommunity: 'Carepatron topluluğunda yayınlandı',
	Purchase: 'Satın al',
	PushToCalendar: 'Takvime aktar',
	Question: 'Soru',
	QuestionOrTitle: 'Soru veya başlık',
	QuickActions: 'Hızlı eylemler',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Yaban Mersini',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Yosun',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Kabak',
	RadiationTherapist: 'Radyasyon Terapisti',
	Radiologist: 'Radyolog',
	Read: 'Okumak',
	ReadOnly: 'Salt okunabilir',
	ReadOnlyAppointment: 'Sadece okuma randevusu',
	ReadOnlyEventBanner: 'Bu randevu salt okunur bir takvimden senkronize edildi ve düzenlenemez.',
	ReaderMaxDepthHasBeenExceededCode: 'Not çok iç içe geçmiş. Bazı öğelerin girintisini kaldırmayı deneyin.',
	ReadyForMapping: 'Hazırda haritalama',
	RealEstateAgent: 'Emlakçı',
	RearrangeClientFields: 'İstemci ayarlarında istemci alanlarını yeniden düzenleme',
	Reason: 'Sebep',
	ReasonForChange: 'değişmek için sebep',
	RecentAppointments: 'Son randevular',
	RecentServices: 'Son Hizmetler',
	RecentTemplates: 'Son şablonlar',
	RecentlyUsed: 'Son zamanlarda kullanılmış',
	Recommended: 'Önerilen',
	RecommendedTemplates: 'Önerilen şablonlar',
	Recording: 'Kayıt',
	RecordingEnded: 'Kayıt sona erdi',
	RecordingInProgress: 'Kayıt devam ediyor',
	RecordingMicrophoneAccessErrorMessage:
		'Lütfen tarayıcınızda mikrofon erişimine izin verin ve kaydı başlatmak için yenileyin.',
	RecurrenceCount: ', {count, plural, one {bir kere} other {# kere}}',
	RecurrenceDaily: '{count, plural, one {Günlük} other {Günler}}',
	RecurrenceEndAfter: 'Sonra',
	RecurrenceEndNever: 'Asla',
	RecurrenceEndOn: 'Üzerinde',
	RecurrenceEvery: 'Her {description}',
	RecurrenceMonthly: '{count, plural, one {Aylık} other {Aylar}}',
	RecurrenceOn: '{description} üzerinde',
	RecurrenceOnAllDays: 'tüm günlerde',
	RecurrenceUntil: '{description}e kadar',
	RecurrenceWeekly: '{count, plural, one {Haftalık} other {Haftalar}}',
	RecurrenceYearly: '{count, plural, one {Yıllık} other {Yıllar}}',
	Recurring: 'Tekrarlayan',
	RecurringAppointment: 'Tekrarlayan randevu',
	RecurringAppointmentsLimitedBannerText:
		'Tüm yinelenen randevular gösterilmez. Dönem için tüm yinelenen randevuları görmek için tarih aralığını daraltın.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# etkinlik} other {# etkinlikler}}</b> aşağıdaki tarihlerde oluşturulacak',
	Redo: 'Yinele',
	ReferFriends: 'Arkadaşlarınızı yönlendirin',
	Reference: 'Referans',
	ReferralCreditedNotificationSubject: 'Referans krediniz {currency} {amount} olarak uygulandı.',
	ReferralEmailDefaultBody: `{name} sayesinde, Carepatron'a ÜCRETSİZ 3 aylık yükseltme gönderildi. Yeni bir çalışma şekli için oluşturulmuş 3 milyondan fazla sağlık uzmanından oluşan topluluğumuza katılın!
Saygılarımızla,
Carepatron Ekibi`,
	ReferralEmailDefaultSubject: `Carepatron'a katılmaya davet edildiniz`,
	ReferralHasNotSignedUpDescription: 'Arkadaşınız henüz kaydolmadı',
	ReferralHasSignedUpDescription: 'Arkadaşınız kaydoldu.',
	ReferralInformation: 'Yönlendirme bilgileri',
	ReferralJoinedNotificationSubject: `{actorProfileName} Carepatron'a katıldı`,
	ReferralListErrorDescription: 'Tavsiye listesi yüklenemedi.',
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {ay} other {ay}}</b> aktif',
	ReferralRewardBanner: 'Kaydolun ve yönlendirme ödülünüzü alın!',
	Referrals: 'Tavsiyeler',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} ay {percentOff, select, 100 {ücretsiz ödeme} other {{percentOff}% indirim}} {type, select, SubscriptionUpgrade {yükseltme} other {}}',
	ReferredUserBenefitTitle: 'Alırlar!',
	Referrer: 'Yönlendiren',
	ReferringProvider: 'Yönlendiren sağlayıcı',
	ReferringUserBenefitSubtitle: '<mark>3 arkadaş</mark> aktif hale geldiğinde USD${creditAmount} kredi.',
	ReferringUserBenefitTitle: 'Anladın!',
	RefreshPage: 'Sayfayı Yenile',
	Refund: 'İade',
	RefundAcknowledgement: '{clientName} için Carepatron dışında geri ödeme yaptım.',
	RefundAcknowledgementValidationMessage: 'Lütfen bu tutarı iade ettiğinizi onaylayın',
	RefundAmount: 'İade tutarı',
	RefundContent:
		'İadelerin müşterinizin hesabında görünmesi 7-10 gün sürer. Ödeme ücretleri iade edilmez, ancak iadeler için ekstra ücret yoktur. İadeler iptal edilemez ve bazılarının işleme alınmadan önce incelenmesi gerekebilir.',
	RefundCouldNotBeProcessed: 'Geri ödeme işlenemedi',
	RefundError:
		'Bu geri ödeme şu anda otomatik olarak işlenemiyor. Lütfen bu ödemenin geri ödenmesini talep etmek için Carepatron desteğiyle iletişime geçin.',
	RefundExceedTotalValidationError: 'Tutar toplam ödenen tutarı aşmamalıdır',
	RefundFailed: 'Geri ödeme başarısız oldu',
	RefundFailedTooltip:
		'Bu ödemenin iadesi daha önce başarısız oldu ve tekrar denenemez. Lütfen destek ile iletişime geçin.',
	RefundNonStripePaymentContent:
		'Bu ödeme Carepatron dışındaki bir yöntem kullanılarak yapıldı (örneğin nakit, internet bankacılığı). Carepatron içinde geri ödeme yapılması müşteriye herhangi bir para iadesi sağlamaz.',
	RefundReasonDescription: 'Müşterilerinizin işlemlerini incelerken bir iade nedeni eklemek yardımcı olabilir',
	Refunded: 'İade edildi',
	Refunds: 'Geri ödemeler',
	RefundsTableEmptyState: 'Hiçbir geri ödeme bulunamadı',
	Regenerate: 'Yeniden Oluştur',
	RegisterButton: 'Kayıt olmak',
	RegisterEmail: 'E-posta',
	RegisterFirstName: 'İlk adı',
	RegisterLastName: 'Soy isim',
	RegisterPassword: 'Şifre',
	RegisteredNurse: 'Kayıtlı hemşire',
	RehabilitationCounselor: 'Rehabilitasyon Danışmanı',
	RejectAppointmentFormTitle: 'Başaramıyor musun? Lütfen nedenini bize bildirin ve yeni bir zaman önerin.',
	Rejected: 'Reddedilmiş',
	Relationship: 'İlişki',
	RelationshipDetails: 'İlişki detayları',
	RelationshipEmptyStateTitle: 'Müşterinizi destekleyen kişilerle bağlantıda kalın',
	RelationshipPageAccessTypeColumnName: 'Profil erişimi',
	RelationshipSavedSuccessSnackbar: 'İlişki başarıyla kaydedildi!',
	RelationshipSelectorFamilyAdmin: 'Aile',
	RelationshipSelectorFamilyMember: 'Aile üyesi',
	RelationshipSelectorProviderAdmin: 'Sağlayıcı yöneticisi',
	RelationshipSelectorProviderStaff: 'Sağlayıcı personeli',
	RelationshipSelectorSupportNetworkPrimary: 'Arkadaş',
	RelationshipSelectorSupportNetworkSecondary: 'Destek ağı',
	RelationshipStatus: 'ilişki durumu',
	RelationshipType: 'İlişki türü',
	RelationshipTypeClientOwner: 'Müşteri',
	RelationshipTypeFamilyAdmin: 'İlişkiler',
	RelationshipTypeFamilyMember: 'Aile',
	RelationshipTypeFriendOrSupport: 'Arkadaş veya destek ağı',
	RelationshipTypeProviderAdmin: 'Sağlayıcı yöneticisi',
	RelationshipTypeProviderStaff: 'Kadro',
	RelationshipTypeSelectorPlaceholder: 'İlişki türlerini ara',
	Relationships: 'İlişkiler',
	Remaining: 'geriye kalan',
	RemainingTime: 'Kalan {time}',
	Reminder: 'Hatırlatma',
	ReminderColor: 'Hatırlatma rengi',
	ReminderDetails: 'Hatırlatma detayları',
	ReminderEditDisclaimer: 'Değişiklikler yalnızca yeni randevulara yansıyacak',
	ReminderSettings: 'Randevu hatırlatıcı ayarları',
	Reminders: 'Hatırlatıcılar',
	Remove: 'Kaldırmak',
	RemoveAccess: 'Erişimi kaldır',
	RemoveAllGuidesBtn: 'Tüm kılavuzları kaldır',
	RemoveAllGuidesPopoverBody:
		'İlk katılım kılavuzlarıyla işiniz bittiğinde her paneldeki kılavuzları kaldır düğmesini kullanmanız yeterlidir.',
	RemoveAllGuidesPopoverTitle: 'Artık işe alım kılavuzlarınıza ihtiyacınız yok mu?',
	RemoveAsDefault: 'Varsayılan olarak kaldır',
	RemoveAsIntake: 'Alım olarak kaldır',
	RemoveCol: 'Sütunu kaldır',
	RemoveColor: 'Rengi kaldır',
	RemoveField: 'Alanı kaldır',
	RemoveFromCall: 'Çağrıdan kaldır',
	RemoveFromCallDescription: '<mark>{attendeeName}</mark>  videolu görüşmeden kaldırmak istediğinizden emin misiniz?',
	RemoveFromCollection: 'Koleksiyondan çıkar',
	RemoveFromCommunity: 'Topluluktan kaldır',
	RemoveFromFolder: 'Klasörden kaldır',
	RemoveFromFolderConfirmationDescription:
		'Bu şablonu bu klasörden kaldırmak istediğinizden emin misiniz? Bu işlem geri alınamaz ancak daha sonra taşımayı tercih edebilirsiniz.',
	RemoveFromIntakeDefault: 'Giriş varsayılanından kaldır',
	RemoveGuides: 'Kılavuzları kaldır',
	RemoveMfaConfirmationDescription:
		'Çok Faktörlü Kimlik Doğrulamayı (MFA) kaldırmak hesabınızın güvenliğini azaltacaktır. Devam etmek istiyor musunuz?',
	RemoveMfaConfirmationTitle: 'MFA kaldırılsın mı?',
	RemovePaymentMethodDescription: `Bu işlem, bu ödeme yöntemine tüm erişimi ve gelecekte kullanımını kaldıracaktır.
 Bu işlem geri alınamaz.`,
	RemoveRow: 'Satırı kaldır',
	RemoveTable: 'Tabloyu kaldır',
	RemoveTemplateAsDefaultIntakeSuccess: 'Başarıyla {templateTitle} varsayılan alım şablonu olarak kaldırıldı.',
	RemoveTemplateFromCommunity: 'Şablonu topluluktan kaldır',
	RemoveTemplateFromFolder: '{templateTitle} başarıyla {folderTitle} dan kaldırıldı',
	Rename: 'Yeniden isimlendirmek',
	RenderingProvider: 'İşleme sağlayıcısı',
	Reopen: 'Yeniden aç',
	ReorderServiceGroupFailure: 'Koleksiyon yeniden sıralanamadı',
	ReorderServiceGroupSuccess: 'Koleksiyon başarıyla yeniden sıralandı',
	ReorderServicesFailure: 'Hizmetler yeniden sıralanamadı',
	ReorderServicesSuccess: 'Hizmetler başarıyla yeniden sıralandı',
	ReorderYourServiceList: 'Hizmet listenizi yeniden sıralayın',
	ReorderYourServiceListDescription:
		'Hizmetlerinizi ve koleksiyonlarınızı düzenleme şekliniz, tüm müşterilerinizin görmesi için çevrimiçi rezervasyon sayfanıza yansıtılacaktır!',
	RepeatEvery: 'Her',
	RepeatOn: 'Tekrarla',
	Repeating: 'Tekrarlanıyor',
	Repeats: 'Tekrarlar',
	RepeatsEvery: 'Her **<span style="font-weight: 700;">**<span style="font-size: 13.3333px;">**</span>**</span>',
	Rephrase: 'Yeniden ifade et',
	Replace: 'Yer değiştirmek',
	ReplaceBackground: 'Arka planı değiştir',
	ReplacementOfPriorClaim: 'Önceki talebin değiştirilmesi',
	Report: 'Rapor',
	Reprocess: 'Yeniden işleme',
	RepublishTemplateToCommunity: 'Topluluk için şablonu yeniden yayınla',
	RequestANewVerificationLink: 'Yeni bir doğrulama bağlantısı isteyin',
	RequestCoverageReport: 'Kapsam raporu talep edin',
	RequestingDevicePermissions: 'Cihaz izinleri isteniyor...',
	RequirePaymentMethodDesc: 'Müşteriler çevrimiçi rezervasyon yapabilmek için kredi kartı bilgilerini girmelidir',
	RequirePaymentMethodLabel: 'Kredi kartı bilgilerini iste',
	Required: 'gerekli',
	RequiredField: 'Gerekli',
	RequiredUrl: 'URL gerekli.',
	Reschedule: 'Yeniden planlamak',
	RescheduleBookingLinkModalDescription:
		'Müşteriniz bu bağlantıyı kullanarak randevu tarih ve saatini değiştirebilir.',
	RescheduleBookingLinkModalTitle: 'Rezervasyonu yeniden planla bağlantısı',
	RescheduleLink: 'Bağlantıyı yeniden planla',
	Resend: 'Yeniden gönder',
	ResendConfirmationCode: 'Onay kodunu tekrar gönder',
	ResendConfirmationCodeDescription:
		'Lütfen e-posta adresinizi girin, size e-postayla başka bir onay kodu göndereceğiz',
	ResendConfirmationCodeSuccess: 'Onay kodu yeniden gönderildi, lütfen gelen kutunuzu kontrol edin',
	ResendNewEmailVerificationSuccess: 'Yeni doğrulama bağlantısı {email} adresine gönderildi.',
	ResendVerificationEmail: 'Doğrulama e-postasını tekrar gönder',
	Reset: 'Sıfırla',
	Resources: 'Kaynaklar',
	RespiratoryTherapist: 'Solunum terapisti',
	RespondToHistoricAppointmentError:
		'Bu tarihi bir randevudur; bir sorunuz varsa lütfen uygulayıcınızla iletişime geçin.',
	Responder: 'Yanıtlayıcı',
	RestorableItemModalDescription:
		'{context} öğesini silmek istediğinizden emin misiniz?{canRestore, select, true { Daha sonra geri yükleyebilirsiniz. } other {}}',
	RestorableItemModalTitle: 'Delete {type}',
	Restore: 'Geri yükle',
	RestoreAll: 'Tümünü geri yükle',
	Restricted: 'Sınırlı',
	ResubmissionCodeReferenceNumber: 'Yeniden gönderme kodu ve referans numarası',
	Resubmit: 'Tekrar gönder',
	Resume: 'Sürdürmek',
	Retry: 'Tekrar dene',
	RetryingConnectionAttempt: 'Yeniden bağlantı deneniyor... (Deneme {retryCount} / {maxRetries})',
	ReturnToForm: 'Forma dön',
	RevertClaimStatus: 'Talep durumunu geri al',
	RevertClaimStatusDescriptionBody:
		'Bu talep bağlı ödemeler içerir ve durumun değiştirilmesi ödeme takibi veya işlenmesini etkileyebilir, bu da elle uzlaştırma gerektirebilir.',
	RevertClaimStatusDescriptionTitle: '{status} durumuna geri dönmek istediğinizden emin misiniz?',
	RevertClaimStatusError: 'Talep durumunu geri alma başarısız oldu',
	RevertToDraft: 'Taslağa geri dön',
	Review: 'Gözden geçirmek',
	ReviewsFirstQuote: 'Her zaman, her yerde randevular',
	ReviewsSecondJobTitle: 'Yaşam Evi Kliniği',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Carepatron uygulamasını da seviyorum. Müşterilerimi takip etmeme ve hareket halindeyken çalışmama yardımcı oluyor.',
	ReviewsThirdJobTitle: 'Manila Körfezi Kliniği',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'Gezinme kolaylığı ve güzel kullanıcı arayüzü her gün yüzüme bir gülümseme getiriyor.',
	RightAlign: 'Sağa hizala',
	Role: 'Rol',
	Roster: 'Katılımcılar',
	RunInBackground: 'Arka planda çalıştır',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: `Carepatron'dan ayrılıyorsunuz`,
	SafetyRedirectSubtext: 'Bu bağlantıya güveniyorsanız devam etmek için onu seçin',
	SalesRepresentative: 'Satış Temsilcis',
	SalesTax: 'Satış vergisi',
	SalesTaxHelp: 'Oluşturulan faturalara satış vergisi dahildir',
	SalesTaxIncluded: 'Evet',
	SalesTaxNotIncluded: 'HAYIR',
	SaoPaulo: 'São Paulo',
	Saturday: 'Cumartesi',
	Save: 'Kaydetmek',
	SaveAndClose: 'Kaydetmek ',
	SaveAndExit: 'Kaydetmek ',
	SaveAndLock: 'Kaydet ve kilitle',
	SaveAsDraft: 'Taslak olarak kaydet',
	SaveCardForFuturePayments: 'Gelecekteki ödemeler için kartı kaydedin',
	SaveChanges: 'Değişiklikleri Kaydet',
	SaveCollection: 'Koleksiyonu Kaydet',
	SaveField: 'Alanı kaydet',
	SavePaymentMethod: 'Ödeme yöntemini kaydet',
	SavePaymentMethodDescription: 'İlk randevunuza kadar sizden ücret alınmayacaktır.',
	SavePaymentMethodSetupError: 'Beklenmeyen bir hata oluştu ve şu anda ödemeleri yapılandıramadık.',
	SavePaymentMethodSetupInvoiceLater: 'İlk faturanızı öderken ödemeler ayarlanabilir ve kaydedilebilir.',
	SaveSection: 'Bölümü kaydet',
	SaveService: 'Yeni hizmet oluştur',
	SaveTemplate: 'Şablonu kaydet',
	Saved: 'Kaydedildi',
	SavedCards: 'Kayıtlı kartlar',
	SavedPaymentMethods: 'Kaydedildi',
	Saving: 'Kaydediliyor...',
	ScheduleAppointmentsAndOnlineServices: 'Randevuları ve çevrimiçi hizmetleri planlayın',
	ScheduleName: 'Program adı',
	ScheduleNew: 'Yeni planla',
	ScheduleSend: 'Randevuyu gönder',
	ScheduleSendAlertInfo: 'Programlanmış konuşmalar, planlanmış zamanda gönderilecektir.',
	ScheduleSendByName: '**Gönderi zamanlaması** • {time} tarafından {displayName}',
	ScheduleSetupCall: 'Kurulum görüşmesi planlayın',
	Scheduled: 'Programlanmış',
	SchedulingSend: 'Gönderimi planla',
	School: 'Okul',
	ScrollToTop: 'Yukarıya doğru kaydır',
	Search: 'Aramak',
	SearchAndConvertToLanguage: 'Arama ve dile dönüştür',
	SearchBasicBlocks: 'Temel blokları arayın',
	SearchByName: 'Ada göre ara',
	SearchClaims: 'Talepleri Ara',
	SearchClientFields: 'Müşteri alanlarında arama',
	SearchClients: 'Müşteri adı, müşteri kimliği veya telefon numarasına göre arama yapın',
	SearchCommandNotFound: '"{searchTerm}" için sonuç bulunamadı.',
	SearchContacts: 'Müşteri veya iletişim',
	SearchContactsPlaceholder: 'Kişileri ara',
	SearchConversations: 'Konuşmaları arayın',
	SearchInputPlaceholder: 'Tüm kaynakları arayın',
	SearchInvoiceNumber: 'Fatura numarasını ara',
	SearchInvoices: 'Faturaları arayın',
	SearchMultipleContacts: 'Müşteriler veya kişiler',
	SearchMultipleContactsOptional: 'Müşteriler veya kişiler (isteğe bağlı)',
	SearchOrCreateATag: 'Etiket arayın veya oluşturun',
	SearchPayments: 'Arama ödemeleri',
	SearchPrepopulatedData: 'Önceden doldurulmuş veri alanlarında arama yapın',
	SearchRelationships: 'İlişkileri ara',
	SearchRemindersAndWorkflows: 'Arama hatırlatıcıları ve iş akışları',
	SearchServices: 'Arama hizmetleri',
	SearchTags: 'Etiketlerde ara',
	SearchTeamMembers: 'Ekip üyelerini arayın',
	SearchTemplatePlaceholder: '{templateCount}+ kaynağı ara',
	SearchTimezone: 'Saat dilimini ara...',
	SearchTrashItems: 'Öğeleri ara',
	SearchUnsplashPlaceholder: `Unsplash'tan ücretsiz yüksek çözünürlüklü fotoğraflar arayın`,
	Secondary: 'İkincil',
	SecondaryInsurance: 'İkincil sigorta',
	SecondaryPolicy: 'İkincil sigorta',
	SecondaryTimezone: 'İkincil saat dilimi',
	Secondly: 'İkinci olarak',
	Section: 'Bölüm',
	SectionCannotBeEmpty: 'Bir bölümün en az bir satırı olmalıdır',
	SectionFormSecondaryText: 'Bölüm başlığı ve açıklaması',
	SectionName: 'Bölüm adı',
	Sections: 'Bölümler',
	SeeLess: 'Daha azını göster',
	SeeLessUpcomingAppointments: 'Yaklaşan randevuları daha az görün',
	SeeMore: 'Daha fazla gör',
	SeeMoreUpcomingAppointments: 'Yaklaşan diğer randevuları görün',
	SeeTemplateLibrary: 'Şablon kitaplığına bakın',
	Seen: 'Görülen',
	SeenByName: '<strong>Görüldü</strong> • {time} tarafından {displayName}',
	SelectAll: 'Hepsini seç',
	SelectAssignees: 'Atananları seçin',
	SelectAttendees: 'Katılımcıları seçin',
	SelectCollection: 'Koleksiyon Seç',
	SelectCorrespondingAttributes: 'İlgili nitelikleri seçin',
	SelectPayers: 'Ödeme yapanları seçin',
	SelectProfile: 'Profil seç',
	SelectServices: 'Hizmetleri seçin',
	SelectTags: 'Etiketleri Seçin',
	SelectTeamOrCommunity: `Ekip veya Topluluk'u seçin`,
	SelectTemplate: 'Şablon Seçin',
	SelectType: 'Tür seçin',
	Selected: 'Seçildi',
	SelfPay: 'Kendi kendine ödeme',
	Send: 'Göndermek',
	SendAndClose: 'Göndermek ',
	SendAndStopIgnore: 'Gönder ve yoksaymayı durdur',
	SendEmail: 'Eposta gönder',
	SendIntake: 'Giriş gönder',
	SendIntakeAndForms: 'Giriş Gönder ',
	SendMeACopy: 'bana bir kopyasını gönder',
	SendNotificationEmailWarning:
		'Bazı katılımcıların e-posta adresi yok ve otomatik bildirimler ve hatırlatıcılar almayacaklar.',
	SendNotificationLabel: 'E-posta ile bilgilendirilecek katılımcıları seçin',
	SendOnlinePayment: 'Çevrimiçi ödeme gönder',
	SendOnlinePaymentTooltipTitleAdmin: 'Lütfen tercih ettiğiniz ödeme ayarlarını ekleyin',
	SendOnlinePaymentTooltipTitleStaff: 'Lütfen sağlayıcının sahibinden çevrimiçi ödemeleri ayarlamasını isteyin.',
	SendPaymentLink: 'Ödeme bağlantısını gönder',
	SendReaction: 'Bir tepki gönder',
	SendScheduledForDate: '{date} için programlanmış gönderim',
	SendVerificationEmail: 'doğrulama e-postası gönder',
	SendingFailed: 'Gönderme başarısız',
	Sent: 'Gönderilmiş',
	SentByName: '<strong>Gönderildi</strong> • {time} tarafından {displayName}',
	Seoul: 'Seul',
	SeparateDuplicateClientsDescription:
		'Seçilen istemci kayıtları, birleştirmeyi seçmediğiniz sürece diğerlerinden ayrı kalacaktır.',
	Service: 'Hizmet',
	'Service/s': 'Hizmetler',
	ServiceAdjustment: 'Hizmet ayarlaması',
	ServiceAllowNewClientsIndicator: 'Yeni müşterilere izin ver',
	ServiceAlreadyExistsInCollection: 'Hizmet koleksiyonda zaten mevcut',
	ServiceBookableOnlineIndicator: 'Çevrimiçi rezervasyon yapılabilir',
	ServiceCode: 'Kod',
	ServiceCodeErrorMessage: 'Servis kodu gerekli',
	ServiceCodeSelectorPlaceholder: 'Hizmet kodu ekleyin',
	ServiceColour: 'Servis rengi',
	ServiceCoverageDescription: 'Uygun hizmetleri seçin ve bu sigorta poliçesi için katılım payını ödeyin.',
	ServiceCoverageGoToServices: 'Hizmetlere git',
	ServiceCoverageNoServicesDescription:
		'Varsayılan poliçe eş ödemesini geçersiz kılmak için hizmet eş ödeme tutarlarını özelleştirin. Hizmetlerin poliçeye karşı talep edilmesini önlemek için kapsamı devre dışı bırakın.',
	ServiceCoverageNoServicesLabel: 'Hiçbir hizmet bulunamadı.',
	ServiceCoverageTitle: 'Hizmet kapsamı',
	ServiceDate: 'Hizmet tarihi',
	ServiceDetails: 'Hizmet ayrıntıları',
	ServiceDuration: 'Süre',
	ServiceEmptyState: 'Henüz hizmet yok',
	ServiceErrorMessage: 'Servis gerekli',
	ServiceFacility: 'Servis tesisi',
	ServiceName: 'Hizmet adı',
	ServiceRate: 'Oran',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Süper Fatura {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} için {serviceReceiptNumber, select, undefined {kullanıcı} other {{clientName}}} ek bilgi gerektiriyor',
	ServiceSalesTax: 'Satış vergisi',
	ServiceType: 'Hizmet',
	ServiceWorkerForceUIUpdateDialogDescription: `Yenilemek ve en yeni Carepatron güncellemelerini almak için yeniden yükle'ye basın.`,
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Tekrar yükle',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Daha eski bir sürüm kullanıyorsunuz',
	ServiceWorkerForceUIUpdateDialogTitle: 'Tekrar hoşgeldiniz!',
	Services: 'Hizmetler',
	ServicesAndAvailability: 'Hizmetler ',
	ServicesAndDiagnosisCodesHeader: 'Hizmetleri ve teşhis kodlarını ekleyin',
	ServicesCount: '{count,plural,=0{Hizmetler}one{Hizmet}other{Hizmetler}}',
	ServicesPlaceholder: 'Hizmetler',
	ServicesProvidedBy: 'Tarafından sağlanan hizmet/hizmetler',
	SetAPhysicalAddress: 'Fiziksel bir adres ayarlayın',
	SetAVirtualLocation: 'Sanal konum ayarlayın',
	SetAsDefault: 'Varsayılan olarak ayarla',
	SetAsIntake: 'Alım olarak ayarla',
	SetAsIntakeDefault: 'Giriş varsayılanı olarak ayarla',
	SetAvailability: 'Kullanılabilirliği ayarla',
	SetTemplateAsDefaultIntakeSuccess: 'Başarıyla {templateTitle} varsayılan alım şablonu olarak ayarlandı',
	SetUpMfaButton: `MFA'yı kurun`,
	SetYourLocation: '<mark>Konumunuzu</mark> belirleyin',
	SetYourLocationDescription: 'İş adresim yok <span>(sadece çevrimiçi ve mobil hizmetler)</span>',
	SettingUpPayers: 'Ödeme Kaynaklarını Kurma',
	Settings: 'Ayarlar',
	SettingsNewUserPasswordDescription:
		'Kaydolduktan sonra size hesabınızı onaylamak için kullanabileceğiniz bir onay kodu göndereceğiz',
	SettingsNewUserPasswordTitle: `Carepatron'a kaydolun`,
	SettingsTabAutomation: 'Otomasyon',
	SettingsTabBillingDetails: 'Fatura Detayları',
	SettingsTabConnectedApps: 'Bağlı uygulamalar',
	SettingsTabCustomFields: 'Özel Alanlar',
	SettingsTabDetails: 'Detaylar',
	SettingsTabInvoices: 'Faturalar',
	SettingsTabLocations: 'Konumlar',
	SettingsTabNotifications: 'Bildirimler',
	SettingsTabOnlineBooking: 'Çevrimiçi Rezervasyon',
	SettingsTabPayers: 'Ödeme yapanlar',
	SettingsTabReminders: 'Hatırlatıcılar',
	SettingsTabServices: 'Hizmetler',
	SettingsTabServicesAndAvailability: 'Hizmetler ve kullanılabilirlik',
	SettingsTabSubscriptions: 'Abonelikler',
	SettingsTabWorkflowAutomations: 'Otomasyonlar',
	SettingsTabWorkflowReminders: 'Temel hatırlatmalar',
	SettingsTabWorkflowTemplates: 'Şablonlar',
	Setup: 'Kurulum',
	SetupGuide: 'Kurulum kılavuzu',
	SetupGuideAddServicesActionLabel: 'Başlangıç',
	SetupGuideAddServicesSubtitle: '4 adım • 2 dk',
	SetupGuideAddServicesTitle: 'Hizmetlerinizi ekleyin',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Başla',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 adım • 3 dk',
	SetupGuideEnableOnlinePaymentsTitle: 'Çevrimiçi ödemeleri etkinleştir',
	SetupGuideImportClientsActionLabel: 'Başla',
	SetupGuideImportClientsSubtitle: '4 adım • 3 dakika',
	SetupGuideImportClientsTitle: 'Müşterilerinizi İçe Aktarın',
	SetupGuideImportTemplatesActionLabel: 'Başla',
	SetupGuideImportTemplatesSubtitle: '2 adım • 1 dk',
	SetupGuideImportTemplatesTitle: 'Şablonlarınızı İçe Aktarın',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Başla',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 adım • 2 dk',
	SetupGuidePersonalizeWorkspaceTitle: 'Çalışma alanınızı kişiselleştirin',
	SetupGuideSetLocationActionLabel: 'Başla',
	SetupGuideSetLocationSubtitle: '4 adım • 1 dk',
	SetupGuideSetLocationTitle: 'Konumunu ayarlayın',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Ekibi Davet Et',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'Ekibinizi zahmetsizce iletişim kurmaya ve görevleri yönetmeye davet edin.',
	SetupGuideSuggestedAddTeamMembersTag: 'Kurulum',
	SetupGuideSuggestedAddTeamMembersTitle: 'Ekip Üyeleri Ekle',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Kişiselleştir',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Benzersiz logonuz ve marka renklerinizle profesyonel görünün.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Markayı Özelleştir',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'İndir',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Herhangi bir cihazda, istediğiniz zaman ve yerde çalışma alanınıza erişin.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Kurulum',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Uygulamayı İndir',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Kullanılabilirliği Ayarla',
	SetupGuideSuggestedEditAvailabilitySubtitle:
		'Kısa süreli rezervasyonları önlemek için müsaitlik durumunuzu ayarlayın.',
	SetupGuideSuggestedEditAvailabilityTag: 'Planlama',
	SetupGuideSuggestedEditAvailabilityTitle: 'Kullanılabilirliği Düzenle',
	SetupGuideSuggestedImportClientsActionLabel: 'İçe Aktarma',
	SetupGuideSuggestedImportClientsSubtitle: 'Mevcut müşteri verilerinizi tek bir tıklamayla anında yükleyin.',
	SetupGuideSuggestedImportClientsTag: 'Kurulum',
	SetupGuideSuggestedImportClientsTitle: 'İçeri aktar müşteriler',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Hatırlatıcıları Düzenle',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Otomatik randevu hatırlatıcıları ile randevu kayıplarını azaltın.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Kişiselleştirilmiş Hatırlatıcılar',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Arayı başlat',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'AI Destekli video araçlarımızı kullanarak görüşme düzenleyin ve müşterilerle bağlantı kurun.',
	SetupGuideSuggestedStartVideoCallTag: 'Tele-sağlık',
	SetupGuideSuggestedStartVideoCallTitle: 'Video görüşmesini başlat',
	SetupGuideSuggestedTryActionsTitle: 'Denemek İçin Şeyler 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Yapay zeka yardımını dene',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'İş sorularınızın anında cevaplarını alın.',
	SetupGuideSuggestedUseAIAssistantTag: 'Yeni',
	SetupGuideSuggestedUseAIAssistantTitle: 'Yapay zeka asistanını kullan',
	SetupGuideSyncCalendarActionLabel: 'Başla',
	SetupGuideSyncCalendarSubtitle: '1 adım • 1 dakikadan az',
	SetupGuideSyncCalendarTitle: 'Takviminizi Senkronize Edin',
	SetupGuideVerifyEmailLabel: 'Doğrula',
	SetupGuideVerifyEmailSubtitle: '2 adım • 2 dk',
	SetupOnlineStripePayments: 'Çevrimiçi ödemeler için Stripe kullanın',
	SetupPayments: 'Ödemeleri kurun',
	Sex: 'Cinsiyet',
	SexSelectorPlaceholder: 'Erkek / Kadın / Söylemek istemiyorum',
	Share: 'Paylaşmak',
	ShareBookingLink: 'Rezervasyon bağlantısını paylaşın',
	ShareNoteDefaultMessage: `Merhaba{name} "{documentName}" adlı dosyayı sizinle paylaştı.

Teşekkürler,
{practiceName}`,
	ShareNoteMessage: `Merhaba
{name} "{documentName}" {isResponder, select, true {üzerinde bazı sorularla sizinle paylaştı.} other {sizinle paylaştı.}}

Teşekkürler,
{practiceName}`,
	ShareNoteTitle: '‘{noteTitle}’yi Paylaş',
	ShareNotesWithClients: 'Müşterilerle veya kişilerle paylaşın',
	ShareScreen: 'Ekran paylaşımı',
	ShareScreenNotSupported: 'Cihazınız/tarayıcınız ekran paylaşma özelliğini desteklemiyor',
	ShareScreenWithId: 'Ekran {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Başkalarının bu şablonu görüntülemesine ve formu göndermesine izin ver.',
	ShareTemplateAsPublicFormModalTitle: '‘{title}’ için paylaşım bağlantısı',
	ShareTemplateAsPublicFormSaved: 'Kamu formu yapılandırması başarıyla güncellendi',
	ShareTemplateAsPublicFormSectionCustomization: 'Kişiselleştirme',
	ShareTemplateAsPublicFormShowPoweredBy: 'Formumda "Powered by Carepatron" yazısını göster',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Formumda “Powered by Carepatron” göster/gizle',
	ShareTemplateAsPublicFormTrigger: 'Paylaş',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Çalışma alanı markasını kullanın',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Çalışma alanı markasını göster/gizle',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Mevcut ve mevcut olmayan müşteriler için kod gönderir',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'İmzalar her zaman e-postanın doğrulanmasını gerektirir',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Sadece mevcut müşteriler için kod gönderir',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Asla kod göndermez.',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `'Asla' seçeneğini seçmek, mevcut bir müşterinin e-posta adresini kullanmaları durumunda doğrulanmamış kullanıcıların müşteri verilerini geçersiz kılmasına izin verebilir.`,
	ShareWithCommunity: 'Toplulukla Paylaş',
	ShareYourReferralLink: 'Yönlendirme bağlantınızı paylaşın',
	ShareYourScreen: 'Ekranınızı paylaşın',
	SheHer: 'O/Onun',
	ShortTextAnswer: 'Kısa metin yanıtı',
	ShortTextFormPrimaryText: 'Kısa metin',
	ShortTextFormSecondaryText: '300 karakterden az cevap',
	Show: 'Göstermek',
	ShowColumn: 'Sütunu göster',
	ShowColumnButton: '{value} sütununu göster düğmesi',
	ShowColumns: 'Sütunları göster',
	ShowColumnsMenu: 'Sütun menüsünü göster',
	ShowDateDurationDescription: 'Örneğin. 29 yaşında',
	ShowDateDurationLabel: 'Tarih süresini göster',
	ShowDetails: 'Detayları göster',
	ShowField: 'Alanı göster',
	ShowFullAddress: 'Adresi göster',
	ShowHideFields: 'Alanları Göster / Gizle',
	ShowIcons: 'Simgeleri göster',
	ShowLess: 'Daha az göster',
	ShowMeetingTimers: 'Toplantı zamanlayıcılarını göster',
	ShowMenu: 'Menüyü göster',
	ShowMergeSummarySidebar: 'Birleştirme özetini göster',
	ShowMore: 'Daha fazla göster',
	ShowOnTranscript: 'Transkript üzerinde göster',
	ShowReactions: 'Tepkileri göster',
	ShowSection: 'Bölümü göster',
	ShowServiceCode: 'Servis kodunu göster',
	ShowServiceDescription: 'Hizmet rezervasyonlarında açıklamayı göster',
	ShowServiceDescriptionDesc: 'Müşteriler rezervasyon sırasında hizmet açıklamalarını görüntüleyebilir',
	ShowServiceGroups: 'Koleksiyonları göster',
	ShowServiceGroupsDesc: 'Müşterilere rezervasyon sırasında koleksiyona göre gruplandırılmış hizmetler gösterilecek',
	ShowSpeakers: 'Konuşmacıları göster',
	ShowTax: 'Vergiyi göster',
	ShowTimestamp: 'Zaman damgasını göster',
	ShowUnits: 'Birimleri göster',
	ShowWeekends: 'Hafta sonlarını göster',
	ShowYourView: 'Görünümünüzü gösterin',
	SignInWithApple: `Apple'la oturum açın`,
	SignInWithGoogle: 'Google ile giriş yap',
	SignInWithMicrosoft: `Microsoft'ta oturum açın`,
	SignUpTitleReferralDefault: '<mark>Üye olmak</mark> ve yönlendirme ödülünüzü alın',
	SignUpTitleReferralUpgrade:
		'{durationInMonths} aylık <mark>{percentOff, select, 100 {ücretsiz} other {{percentOff}% indirimli}} yükseltme</mark> başlatın',
	SignatureCaptureError: 'İmza yakalanamıyor. Lütfen tekrar deneyin.',
	SignatureFormPrimaryText: 'İmza',
	SignatureFormSecondaryText: 'Dijital imza alın',
	SignatureInfoTooltip: 'Bu görsel temsil geçerli bir elektronik imza değildir.',
	SignaturePlaceholder: 'İmzanızı buraya atın',
	SignedBy: 'Tarafından imzalandı',
	Signup: 'Üye olmak',
	SignupAgreements: '{termsOfUse} ve {privacyStatement} şartlarını hesabım için kabul ediyorum.',
	SignupBAA: 'İş Ortaklığı Anlaşması',
	SignupBusinessAgreements:
		'Benim ve işletmem adına, hesabım için {businessAssociateAgreement}, {termsOfUse} ve {privacyStatement} ile  uyuştuğumu kabul ediyorum.',
	SignupInvitationForYou: `Carepatron'u kullanmaya davet edildiniz.`,
	SignupPageProviderWarning:
		'Yöneticiniz zaten bir hesap oluşturduysa, sizi bu sağlayıcıya davet etmesini istemeniz gerekir. Bu kayıt formunu kullanmayın. Daha fazla bilgi için bakınız',
	SignupPageProviderWarningLink: 'bu bağlantı.',
	SignupPrivacy: 'Gizlilik Politikası',
	SignupProfession: 'Sizi en iyi anlatan meslek hangisi?',
	SignupSubtitle: `Carepatron'un muayenehane yönetimi yazılımı, yalnız uygulayıcılar ve ekipler için tasarlanmıştır. Aşırı ücret ödemeyi bırakın ve devrimin parçası olun.`,
	SignupSuccessDescription:
		'Katılımınızı başlatmak için e-posta adresinizi doğrulayın. Hemen almazsanız, lütfen spam klasörünüzü kontrol edin.',
	SignupSuccessTitle: 'Lütfen emailinizi kontrol edin',
	SignupTermsOfUse: 'Kullanım Şartları',
	SignupTitleClient: '<mark>Sağlığınızı yönetin</mark> tek bir yerden',
	SignupTitleLast: 've yaptığınız tüm işler! - Bedava',
	SignupTitleOne: '<mark>Size güç veriyoruz</mark> , ',
	SignupTitleThree: '<mark>Müşterilerinize güç vermek</mark> , ',
	SignupTitleTwo: '<mark>Ekibinize güç vermek</mark> , ',
	Simple: 'Basit',
	SimplifyBillToDetails: 'Faturayı ayrıntılara kadar basitleştirin',
	SimplifyBillToHelperText: 'İstemciyle eşleştiğinde yalnızca ilk satır kullanılır',
	Singapore: 'Singapur',
	Single: 'Bekar',
	SingleChoiceFormPrimaryText: 'Tek seçim',
	SingleChoiceFormSecondaryText: 'Yalnızca bir seçeneği seçin',
	Sister: 'Kız kardeş',
	SisterInLaw: 'Baldız',
	Skip: 'Atlamak',
	SkipLogin: 'Giriş yapmayı geç',
	SlightBlur: 'Arka planınızı hafifçe bulanıklaştırın',
	Small: 'Küçük',
	SmartChips: 'Akıllı çipler',
	SmartDataChips: 'Akıllı veri çipleri',
	SmartReply: 'Akıllı yanıt',
	SmartSuggestNewClient: '**Akıllı Öneri** {name} adında yeni bir müşteri oluşturun',
	SmartSuggestedFieldDescription: 'Bu alan akıllı bir öneridir',
	SocialSecurityNumber: 'Sosyal güvenlik numarası',
	SocialWork: 'Sosyal çalışma',
	SocialWorker: 'Sosyal Görevli',
	SoftwareDeveloper: 'Yazılım geliştirici',
	Solo: 'Solo',
	Someone: 'Birisi',
	Son: 'Oğul',
	SortBy: 'Göre sırala',
	SouthAmerica: 'Güney Amerika',
	Speaker: 'Konuşmacı',
	SpeakerSource: 'Hoparlör kaynağı',
	Speakers: 'Hoparlörler',
	SpecifyPaymentMethod: 'Ödeme yöntemini belirtin',
	SpeechLanguagePathology: 'Konuşma dili patolojisi',
	SpeechTherapist: 'Konuşma terapisti',
	SpeechTherapists: 'Konuşma Terapistleri',
	SpeechTherapy: 'Konuşma terapisi',
	SportsMedicinePhysician: 'Spor Hekimliği Hekimi',
	Spouse: 'Eş',
	SpreadsheetColumnExample: 'Örneğin ',
	SpreadsheetColumns: 'E-tablo Sütunları',
	SpreadsheetUploaded: 'E-tablo Yüklendi',
	SpreadsheetUploading: 'Yükleniyor...',
	Staff: 'Kadro',
	StaffAccessDescriptionAdmin: 'Yöneticiler platformdaki her şeyi yönetebilir.',
	StaffAccessDescriptionStaff: `Personel üyeleri oluşturdukları veya paylaştıkları müşterileri, notları ve belgeleri yönetebilir
 onlarla randevu alın, faturaları yönetin.`,
	StaffContactAssignedSubject:
		'{actorProfileName}, {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} ve {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { ve 1 diğer müşteri} other { ve # diğer müşteriyi}} size atadı',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} {inboxName} kutusunu sizinle paylaştı.',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} {inboxName} gelen kutusu erişiminizi kaldırdı.',
	StaffMembers: 'Personel üyeleri',
	StaffMembersNumber: '{billedUsers, plural, one {# ekip üyesi} other {# ekip üyeleri}}',
	StaffSavedSuccessSnackbar: 'Ekip üyelerinin bilgileri başarıyla kaydedildi!',
	StaffSelectorAdminRole: 'Yönetici',
	StaffSelectorStaffRole: 'Personel',
	StandardAppointment: 'Standart Randevu',
	StandardColor: 'Görev rengi',
	StartAndEndTime: 'Başlangıç ve bitiş zamanı',
	StartCall: 'Aramayı başlat',
	StartDate: 'Başlangıç tarihi',
	StartDictating: 'Dikte etmeye başla',
	StartImport: 'İçe aktarmaya başla',
	StartRecordErrorTitle: 'Kaydınız başlatılırken bir hata oluştu',
	StartRecording: 'Kaydı başlat',
	StartTimeIncrements: 'Başlangıç zamanı artışları',
	StartTimeIncrementsView: '{startTimeIncrements} dk. aralıklarla',
	StartTranscribing: 'Transkripti başlat',
	StartTranscribingNotes:
		'Lütfen not oluşturmak istediğiniz müşterileri seçin. Daha sonra kaydı başlatmak için "Transkripsiyonu Başlat" düğmesine tıklayın',
	StartTranscription: 'Transkript başlat',
	StartVideoCall: 'Video görüşmesi başlat',
	StartWeekOn: 'Haftaya başla',
	StartedBy: 'Başlatan ',
	Starter: 'Başlangıç',
	State: 'Durum',
	StateIndustrialAccidentProviderNumber: 'Devlet endüstriyel kaza sağlayıcı numarası',
	StateLicenseNumber: 'Devlet lisans numarası',
	Statement: 'İfade',
	StatementDescriptor: 'Açıklama tanımlayıcı',
	StatementDescriptorToolTip:
		'Ekstre tanımlayıcısı müşterilerinizin banka veya kredi kartı ekstrelerinde gösterilir. 5 ile 22 karakter arasında olmalı ve işletme adınızı yansıtmalıdır.',
	StatementNumber: 'Bildirim #',
	Status: 'Durum',
	StatusFieldPlaceholder: 'Durum etiketi girin',
	StepFather: 'Üvey baba',
	StepMother: 'Üvey anne',
	Stockholm: 'Stokholm',
	StopIgnoreSendersDescription: `Gönderenin gelecekteki mesajlarını yoksaymayı durdurmak için bu göndereni yoksaymayı durdurabilirsiniz. Bu, gönderenin gelecekteki mesajlarını yoksaymayı durduracak ve bu mesajları 'Gelen Kutusu' klasörüne taşıyacaktır.`,
	StopIgnoring: 'Yoksaymayı durdur',
	StopIgnoringSenders: 'Gönderenleri yoksaymayı durdur',
	StopIgnoringSendersSuccess: '<mark>{addresses}</mark> e-posta adresini görmezden gelmeyi bıraktım',
	StopSharing: 'Paylaşmayı durdur',
	StopSharingLabel: 'carepatron.com ekranınızı paylaşıyor.',
	Storage: 'Depolamak',
	StorageAlmostFullDescription: '🚀 Hesabınızın sorunsuz bir şekilde çalışmaya devam etmesi için hemen yükseltin.',
	StorageAlmostFullTitle: `Çalışma alanı depolama limitinizin %{percentage}%'ini kullandınız!`,
	StorageFullDescription: 'Planınızı yükselterek daha fazla depolama alanına sahip olun.',
	StorageFullTitle: '	Depolama alanınız dolu.',
	Street: 'Sokak',
	StripeAccountNotCompleteErrorCode:
		'Çevrimiçi ödemeler {hasProviderName, select, true { {providerName} için ayarlanmış değil} other {bu sağlayıcı için etkin değil}}.',
	StripeAccountRejectedError: 'Stripe hesabı reddedildi. Lütfen destek ekibiyle iletişime geçin.',
	StripeBalance: 'Şerit Dengesi',
	StripeChargesInfoToolTip: 'Borç tahsil etmenizi sağlar ',
	StripeFeesDescription: `Carepatron size hızlı bir şekilde ödeme almak ve ödeme bilgilerinizi güvende tutmak için Stripe'ı kullanıyor. Kullanılabilir ödeme yöntemleri bölgeye göre değişir; tüm önemli ödeme yöntemleri `,
	StripeFeesDescriptionItem1: 'İşlem ücretleri, başarılı her işlem için uygulanır, {link} yapabilirsiniz.',
	StripeFeesDescriptionItem2: 'Ödemeler günlük olarak yapılır ancak 4 güne kadar bekletilir.',
	StripeFeesLinkToRatesText: 'fiyatlarımızı burada görüntüleyin',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Üzgünüz, çevrimiçi ödeme kullanan faturalar için en az {minimumAmount} gereklidir.',
	StripePaymentsDisabled: 'Çevrimiçi ödemeler devre dışı bırakıldı. Lütfen ödeme ayarlarınızı kontrol edin.',
	StripePaymentsUnavailable: 'Ödemeler kullanılamıyor',
	StripePaymentsUnavailableDescription: 'Ödemeler yüklenirken bir hata oluştu. Lütfen daha sonra tekrar deneyiniz.',
	StripePayoutsInfoToolTip: 'Banka hesabınıza ödeme yapmanızı sağlar',
	StyleYourWorkspace: '<mark>Stil</mark> çalışma alanınızı',
	StyleYourWorkspaceDescription1:
		'Web sitenizden marka varlıklarını getirdik. Bunları düzenlemekten çekinmeyin veya Carepatron çalışma alanınıza devam edin',
	StyleYourWorkspaceDescription2:
		'Marka varlıklarınızı, sorunsuz bir müşteri deneyimi için faturaları ve çevrimiçi rezervasyonları özelleştirmek için kullanın.',
	SubAdvanced: 'Gelişmiş',
	SubEssential: 'Önemli',
	SubOrganization: 'Organizasyon',
	SubPlus: 'Artı',
	SubProfessional: 'Profesyonel',
	Subject: 'Ders',
	Submit: 'Göndermek',
	SubmitElectronically: 'Elektronik olarak gönder',
	SubmitFeedback: 'Geri bildirim gönder',
	SubmitFormValidationError:
		'Lütfen gerekli tüm alanların doğru şekilde doldurulduğundan emin olun ve tekrar göndermeyi deneyin.',
	Submitted: 'Gönderildi',
	SubmittedDate: 'Gönderim tarihi',
	SubscribePerMonth: 'Abone Ol {price} {isMonthly, select, true {aylık} other {yıllık}}',
	SubscriptionDiscountDescription:
		'{percentOff}% indirim {months, select, null { } other { {months, plural, one { # ay için} other { # ay için}}}}',
	SubscriptionFreeTrialDescription: '{endDate} tarihine kadar ücretsiz',
	SubscriptionPaymentFailedNotificationSubject:
		'Aboneliğiniz için ödemeyi tamamlayamadık. Lütfen ödeme bilgilerinizi kontrol edin',
	SubscriptionPlanDetailsHeader: 'Kullanıcı başına/aylık, yıllık olarak faturalandırılır',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} aylık faturalandırılır (USD)',
	SubscriptionPlans: 'Abonelik Planları',
	SubscriptionPlansDescription:
		'Planınızı yükselterek ek avantajların kilidini açın ve uygulamanızın sorunsuz çalışmasını sağlayın.',
	SubscriptionPlansDescriptionNoPermission:
		'Şu anda yükseltme erişiminiz yok gibi görünüyor — lütfen yardım için yöneticinize başvurun.',
	SubscriptionSettings: 'Abonelik ayarları',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> depolama kullanıldı',
	SubscriptionSettingsStorageUsed: '{kullanılan} / {sınır} kullanıldı',
	SubscriptionSettingsUnlimitedStorage: 'Sınırsız depolama alanı mevcut',
	SubscriptionSummary: 'Abonelik özeti',
	SubscriptionUnavailableOverStorageLimit: 'Mevcut kullanımınız, bu planın depolama sınırını aşıyor.',
	SubscriptionUnpaidBannerButton: 'Aboneliklere git',
	SubscriptionUnpaidBannerDescription: 'Lütfen ödeme bilgilerinizin doğruluğunu kontrol edip tekrar deneyin',
	SubscriptionUnpaidBannerTitle: 'Aboneliğiniz için ödemeyi tamamlayamadık.',
	Subscriptions: 'Abonelikler',
	SubscriptionsAndPayments: 'Abonelikler ',
	Subtotal: 'ara toplam',
	SuburbOrProvince: 'Banliyö/İl',
	SuburbOrState: 'Banliyö/Eyalet',
	SuccessSavedNoteChanges: 'Not değişiklikleri başarıyla kaydedildi',
	SuccessShareDocument: 'Doküman başarıyla paylaşıldı',
	SuccessShareNote: 'Not başarıyla paylaşıldı',
	SuccessfullyCreatedValue: 'Başarıyla {value} oluşturuldu.',
	SuccessfullyDeletedTranscriptionPart: 'Transkript bölümü başarıyla silindi',
	SuccessfullyDeletedValue: 'Başarıyla {value} silindi',
	SuccessfullySubmitted: 'Başarıyla gönderildi ',
	SuccessfullyUpdatedClientSettings: 'İstemci Ayarları başarıyla güncellendi',
	SuccessfullyUpdatedTranscriptionPart: 'Transkript bölümü başarıyla güncellendi',
	SuccessfullyUpdatedValue: 'Başarıyla {value} güncellendi.',
	SuggestedAIPoweredTemplates: 'Yapay Zeka Destekli Şablon Önerileri',
	SuggestedAITemplates: 'Önerilen AI Şablonları',
	SuggestedActions: 'Önerilen eylemler',
	SuggestedLocations: 'Önerilen Konumlar',
	Suggestions: 'Öneriler',
	Summarise: 'Yapay zeka özeti',
	SummarisingContent: '{title} Özeti',
	Sunday: 'Pazar',
	Superbill: 'Süper fatura',
	SuperbillAndInsuranceBilling: 'Süper fatura ',
	SuperbillAutomationMonthly: 'Aktif • Ayın son günü',
	SuperbillAutomationNoEmail:
		'Otomatik faturalandırma belgelerini başarıyla göndermek için bu müşteriye bir e-posta adresi ekleyin',
	SuperbillAutomationNotActive: 'Aktif değil',
	SuperbillAutomationUpdateFailure: 'Superbill otomasyon ayarları güncellenemedi',
	SuperbillAutomationUpdateSuccess: 'Superbill otomasyon ayarları başarıyla güncellendi',
	SuperbillClientHelperText: 'Bu bilgiler müşteri ayrıntılarından önceden doldurulmuştur',
	SuperbillNotFoundDescription:
		'Lütfen sağlayıcınızla iletişime geçin ve daha fazla bilgi almasını veya süper faturayı yeniden göndermesini isteyin.',
	SuperbillNotFoundTitle: 'Süper fatura bulunamadı',
	SuperbillNumber: 'Süperfatura #{number}',
	SuperbillNumberAlreadyExists: 'Superbill makbuz numarası zaten mevcut',
	SuperbillPracticeHelperText: 'Bu bilgiler uygulama faturalandırma ayarlarından önceden doldurulmuştur',
	SuperbillProviderHelperText: 'Bu bilgiler personel ayrıntılarından önceden doldurulmuştur',
	SuperbillReceipts: 'Süper fatura makbuzları',
	SuperbillsEmptyStateDescription: 'Hiçbir süper fatura bulunamadı.',
	Surgeon: 'Cerrah',
	Surgeons: 'Cerrahlar',
	SurgicalTechnologist: 'Cerrahi Teknoloji Uzmanı',
	SwitchFromAnotherPlatform: 'Başka bir platformdan geçiyorum',
	SwitchToMyPortal: `Portalım'a geç`,
	SwitchToMyPortalTooltip: `Kendi kişisel portalınıza erişin,
 keşfetmenizi sağlar
 müşterinin portal deneyimi.`,
	SwitchWorkspace: 'Çalışma alanını değiştir',
	SwitchingToADifferentPlatform: 'Farklı bir platforma geçiş',
	Sydney: 'Sidney',
	SyncCalendar: 'Takvimi senkronize et',
	SyncCalendarModalDescription:
		'Diğer ekip üyeleri senkronize edilen takvimlerinizi göremez. Müşteri randevuları yalnızca Carepatron içinden güncellenebilir veya silinebilir.',
	SyncCalendarModalDisplayCalendar: `Takvimimi Carepatron'da görüntüle`,
	SyncCalendarModalSyncToCarepatron: 'Takvimimi Carepatron ile senkronize et',
	SyncCalendarModalSyncWithCalendar: 'Carepatron randevularını takvimimle senkronize et',
	SyncCarepatronAppointmentsWithMyCalendar: 'Carepatron randevularını takvime senkronize et',
	SyncGoogleCalendar: 'Google takvimini senkronize et',
	SyncInbox: 'Gelen kutusunu Carepatron ile senkronize et',
	SyncMyCalendarToCarepatron: 'Takvimi **Carepatron** ile eşitle',
	SyncOutlookCalendar: 'Outlook takvimini senkronize et',
	SyncedFromExternalCalendar: 'Harici takvimden senkronize edildi',
	SyncingCalendarName: '{calendarName} takvimini senkronize ediyor',
	SyncingFailed: 'Senkronizasyon başarısız oldu',
	SystemGenerated: 'Sistem tarafından oluşturuldu',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Masa',
	TableRowLabel: '{value} için tablo satırı',
	TagSelectorNoOptionsText: 'Yeni etiket eklemek için &quot;yeni oluştur&quot;u tıklayın',
	Tags: 'Etiketler',
	TagsInputPlaceholder: 'Etiketleri arayın veya oluşturun',
	Task: 'Görev',
	TaskAttendeeStatusUpdatedSuccess: 'Randevu durumları başarıyla güncellendi.',
	Tasks: 'Görevler',
	Tax: 'Vergi',
	TaxAmount: 'Vergi Tutarı',
	TaxID: 'Vergi Kimlik Numarası',
	TaxIdType: 'Vergi Kimlik Numarası Türü',
	TaxName: 'Vergi adı',
	TaxNumber: 'Vergi numarası',
	TaxNumberType: 'Vergi Numarası Türü',
	TaxNumberTypeInvalid: '{type} geçersiz',
	TaxPercentageOfAmount: `{taxName} ({percentage}% {amount}'nın)`,
	TaxRate: 'Vergi oranı',
	TaxRatesDescription: 'Fatura satır öğelerinize uygulanacak vergi oranlarını yönetin.',
	Taxable: 'Vergiye tabi',
	TaxonomyCode: 'Taksonomi kodu',
	TeacherAssistant: 'Öğretmen yardımcısı',
	Team: 'Takım',
	TeamMember: 'Takım üyesi',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {zaten bu saatte rezerve edilmiş} other {zaten bu saatte rezerve edilmişler}}.{br}Çift rezervasyon yapmamak için yeni bir zaman seçin.',
	TeamMembers: 'Takım üyeleri',
	TeamMembersColour: 'Ekip üyelerinin rengi',
	TeamMembersDetails: 'Ekip üyelerinin ayrıntıları',
	TeamSize: 'Ekibinizde kaç kişi var?',
	TeamTemplates: 'Ekip şablonları',
	TeamTemplatesSectionDescription: 'Sen ve ekibin tarafından oluşturuldu',
	TelehealthAndVideoCalls: 'Telesağlık ',
	TelehealthProvidedOtherThanInPatientCare:
		'Yatarak tedavi dışındaki hizmetler için tele sağlık hizmeti sağlanmaktadır',
	TelehealthVideoCall: 'Telesağlık görüntülü görüşmesi',
	Template: 'Şablon',
	TemplateDescription: 'Şablon açıklaması',
	TemplateDetails: 'Şablon detayları',
	TemplateEditModeViewSwitcherDescription: 'Şablon oluştur ve düzenle',
	TemplateGallery: 'Topluluk Şablonları',
	TemplateImportCompletedNotificationSubject: 'Şablon içeri aktarımı tamamlandı! {templateTitle} kullanıma hazır.',
	TemplateImportFailedNotificationSubject: '{fileName} dosyası içe aktarılamadı.',
	TemplateName: 'Şablon adı',
	TemplateNotFound: 'Şablon bulunamadı.',
	TemplatePreviewErrorMessage: 'Şablon önizlemesi yüklenirken bir hata oluştu',
	TemplateResponderModeViewSwitcherDescription: 'Formları önizleyin ve onlarla etkileşim kurun',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Formlarınızın yanıtlayanlar tarafından doldurulduğunda nasıl göründüğünü kontrol edin',
	TemplateSaved: 'Değişiklikler kaydedildi',
	TemplateTitle: 'Şablon Başlığı',
	TemplateType: 'Şablon Türü',
	Templates: 'Şablonlar',
	TemplatesCategoriesFilter: 'Kategoriye göre filtrele',
	TemplatesPublicTemplatesFilter: ' Topluluğa/Ekibe göre filtrele',
	Text: 'Metin',
	TextAlign: 'Metin hizalama',
	TextColor: 'Metin rengi',
	ThankYouForYourFeedback: 'Geri bildiriminiz için teşekkür ederiz!',
	ThanksForLettingKnow: 'Bizi bilgilendirdiğiniz için teşekkür ederiz.',
	ThePaymentMethod: 'Ödeme yöntemi',
	ThemThey: 'Onlar/Onlar',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Daha fazla tema',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Koyu',
	ThemeColorLightMode: 'Işık',
	ThemeColorModePickerTitle: 'Renk Modu',
	ThemeColorSystemMode: 'Sistem',
	ThemeCpColorPickerTitle: 'Carepatron temaları',
	ThemePanelDescription: 'Açık ve koyu mod arasında seçim yapın ve tema tercihlerinizi özelleştirin',
	ThemePanelTitle: 'Görünüm',
	Then: 'Daha sonra',
	Therapist: 'Terapist',
	Therapists: 'Terapistler',
	Therapy: 'Terapi',
	Thick: 'Kalın',
	Thin: 'İnce',
	ThirdPerson: '3. kişi',
	ThisAndFollowingAppointments: 'Bu ve sonraki randevular',
	ThisAndFollowingMeetings: 'Bu ve sonraki toplantılar',
	ThisAndFollowingReminders: 'Bu ve sonraki hatırlatıcılar',
	ThisAndFollowingTasks: 'Bu ve aşağıdaki görevler',
	ThisAppointment: 'Bu randevu',
	ThisMeeting: 'Bu toplantı',
	ThisMonth: 'Bu ay',
	ThisPerson: 'Bu kişi',
	ThisReminder: 'Bu hatırlatma',
	ThisTask: 'Bu görev',
	ThisWeek: 'Bu hafta',
	ThreeDay: '3 gün',
	Thursday: 'Perşembe',
	Time: 'Zaman',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {dk} other {dk}}',
	TimeAgoSeconds: '{number}ler',
	TimeFormat: 'Zaman biçimi',
	TimeIncrement: 'Zaman artışı',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Zaman aralığı boyutu',
	Timestamp: 'Zaman damgası',
	Timezone: 'Saat dilimi',
	TimezoneDisplay: 'Zaman dilimi gösterimi',
	TimezoneDisplayDescription: 'Saat dilimi görüntüleme ayarlarınızı yönetin.',
	Title: 'Başlık',
	To: 'İle',
	ToYourWorkspace: 'çalışma alanınıza',
	Today: 'Bugün',
	TodayInHoursPlural: 'Bugün {count} {count, plural, one {saat} other {saat}}',
	TodayInMinsAbbreviated: 'Bugün {count} {count, plural, one {dakika} other {dakika}}',
	ToggleHeaderCell: 'Başlık hücresini değiştir',
	ToggleHeaderCol: 'Başlık sütununu değiştir',
	ToggleHeaderRow: 'Başlık satırını değiştir',
	Tokyo: 'Tokyo',
	Tomorrow: 'Yarın',
	TomorrowAfternoon: 'Yarın öğleden sonra',
	TomorrowMorning: 'Yarın sabah',
	TooExpensive: 'Çok pahalı',
	TooHardToSetUp: 'Kurulumu çok zor',
	TooManyFiles: `1'den fazla dosya algılandı.`,
	ToolsExample: 'Basit uygulama, Microsoft, Calendly, Asana, Doxy.me...',
	Total: 'Toplam',
	TotalAccountCredit: 'Toplam hesap kredisi',
	TotalAdjustments: 'Toplam Ayarlamalar',
	TotalAmountToCreditInCurrency: 'Kredite edilecek toplam tutar ({currency})',
	TotalBilled: 'Toplam Fatura Edilen',
	TotalConversations: '{total} {total, plural, =0 {konuşma} one {konuşma} other {konuşmalar}}',
	TotalOverdue: 'Toplam Gecikme',
	TotalOverdueTooltip:
		'Toplam Vadesi geçmiş bakiye, tarih aralığına bakılmaksızın iptal edilmemiş veya işlenmemiş tüm ödenmemiş faturaları içerir.',
	TotalPaid: 'Toplam Ödenen',
	TotalPaidTooltip: 'Toplam Ödenen bakiye, belirtilen tarih aralığında ödenen faturalardaki tüm tutarları içerir.',
	TotalUnpaid: 'Toplam Ödenmemiş',
	TotalUnpaidTooltip:
		'Toplam Ödenmemiş Bakiye, belirtilen tarih aralığında vadesi gelen tüm ödenmemiş, ödenmemiş ve gönderilmiş fatura tutarlarını içerir.',
	TotalWorkflows: '{count} {count, plural, one {iş akışı} other {iş akışları}}',
	TotpSetUpManualEntryInstruction: 'Alternatif olarak, aşağıdaki kodu manuel olarak uygulamaya girebilirsiniz:',
	TotpSetUpModalDescription:
		'Çok Faktörlü Kimlik Doğrulamayı ayarlamak için kimlik doğrulama uygulamanızla QR kodunu tarayın.',
	TotpSetUpModalTitle: 'MFA aygıtını ayarlayın',
	TotpSetUpSuccess: 'Tamamdır! MFA etkinleştirildi.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Kimlik doğrulama uygulamanız tarafından oluşturulan kodu girin',
	Transcribe: 'Uyarlamak',
	TranscribeLanguageSelector: 'Giriş dilini seçin',
	TranscribeLiveAudio: 'Canlı sesi metne dönüştürün',
	Transcribing: 'Ses yazıya aktarılıyor...',
	TranscribingIn: 'Transkript etmek',
	Transcript: 'Deşifre metni',
	TranscriptRecordingCompleteInfo: 'Kaydınız tamamlandığında transkriptinizi burada göreceksiniz.',
	TranscriptSuccessSnackbar: 'Transkript başarıyla işlendi.',
	Transcription: 'Transkripsiyon',
	TranscriptionEmpty: 'Transkript mevcut değil',
	TranscriptionEmptyHelperMessage: 'Bu transkripsiyon hiçbir şey almadı. Yeniden başlatın ve tekrar deneyin.',
	TranscriptionFailedNotice: 'Bu transkripsiyon başarıyla işlenemedi',
	TranscriptionIdleMessage:
		'Sesinizi duyamıyoruz. Daha fazla zamana ihtiyacınız varsa lütfen {timeValue} saniye içinde yanıt verin, aksi takdirde oturum sona erecektir.',
	TranscriptionInProcess: 'Transkript devam ediyor...',
	TranscriptionIncompleteNotice: 'Bu transkripsiyonun bazı bölümleri başarıyla işlenemedi',
	TranscriptionOvertimeWarning: '{scribeType} oturumu <strong>{timeValue} {unit}</strong> içinde sona eriyor',
	TranscriptionPartDeleteMessage: 'Bu transkripsiyon bölümünü silmek istediğinizden emin misiniz?',
	TranscriptionText: 'Metne ses',
	TranscriptsPending: 'Oturum bittikten sonra transkriptiniz burada mevcut olacaktır.',
	Transfer: 'Aktar',
	TransferAndDelete: 'Aktar ve sil',
	TransferOwnership: 'Sahipliği aktar',
	TransferOwnershipConfirmationModalDescription:
		'Bu işlem yalnızca sahipliği size geri devretmeleri durumunda geri alınabilir.',
	TransferOwnershipDescription: 'Bu çalışma alanının sahipliğini başka bir ekip üyesine aktarın.',
	TransferOwnershipSuccessSnackbar: 'Sahiplik başarıyla aktarıldı!',
	TransferOwnershipToMember: 'Bu çalışma alanını {staff} ile paylaşmak istediğinizden emin misiniz?',
	TransferStatusAlert:
		'{numberOfStatuses, plural, one {bu durumu} other {bu durumları}} silmek, {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} müşteri durumu.</strong>} other {<strong>{numberOfAffectedRecords} müşteri durumu.</strong>}} etkileyecektir.',
	TransferStatusDescription:
		'Silme işlemine devam etmeden önce bu istemciler için başka bir durum seçin. Bu işlem geri alınamaz.',
	TransferStatusLabel: 'Yeni duruma geçiş',
	TransferStatusPlaceholder: 'Mevcut bir durumu seçin',
	TransferStatusTitle: 'Silmeden önceki aktarım durumu',
	TransferTaskAttendeeStatusAlert: `Bu durumu kaldırmak <strong>{number} gelecek randevu {number, plural, one {durumu} other {durumları}}</strong>'u etkileyecektir.`,
	TransferTaskAttendeeStatusDescription:
		'Silme işlemine devam etmeden önce bu müşteriler için başka bir durum seçin. Bu işlem geri alınamaz.',
	TransferTaskAttendeeStatusSubtitle: 'Randevu durumu',
	TransferTaskAttendeeStatusTitle: 'Silmeden önce aktarım durumu',
	Trash: 'Çöp',
	TrashDeleteItemsModalConfirm: 'Onaylamak için {confirmationText} yazın',
	TrashDeleteItemsModalDescription:
		'Aşağıdaki {count, plural, one {öğe} other {öğeler}} kalıcı olarak silinecek ve geri yüklenemeyecek.',
	TrashDeleteItemsModalTitle: '{count, plural, one {Öğeyi} other {Öğeleri}} kalıcı olarak sil',
	TrashDeletedAllItems: 'Tüm öğeler silindi',
	TrashDeletedItems: 'Silinen {count, plural, one {öğe} other {öğeler}}',
	TrashDeletedItemsFailure: 'Çöpten öğeleri silmekte başarısız oldu',
	TrashLocationAppointmentType: 'Takvim',
	TrashLocationBillingAndPaymentsType: 'Fatura ve Ödemeler',
	TrashLocationContactType: 'Müşteriler',
	TrashLocationNoteType: 'Notlar ve Belgeler',
	TrashRestoreItemsModalDescription: 'Aşağıdaki {count, plural, one {item} other {items}} geri yüklenecektir.',
	TrashRestoreItemsModalTitle: '{count, plural, one {öğeyi} other {öğeleri}} geri yükle',
	TrashRestoredAllItems: 'Tüm öğeler geri yüklendi',
	TrashRestoredItems: '{count} öğeyi geri yükledim',
	TrashRestoredItemsFailure: 'Çöpten öğeleri geri yüklemekte başarısız oldu',
	TrashSuccessfullyDeletedItem: 'Başarıyla silindi {type}',
	Trigger: 'Tetiklemek',
	Troubleshoot: 'Sorun giderme',
	TryAgain: 'Tekrar deneyin',
	Tuesday: 'Salı',
	TwoToTen: '2 - 10',
	Type: 'Tip',
	TypeHere: 'Buraya yaz...',
	TypeToConfirm: 'Onaylamak için {keyword} yazın',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Başlık 1',
	TypographyHeading2: 'Başlık 2',
	TypographyHeading3: 'Başlık 3',
	TypographyHeading4: 'Başlık 4',
	TypographyHeading5: 'Başlık 5',
	TypographyP: 'P',
	TypographyParagraph: 'Paragraf',
	UnableToCompleteAction: 'İşlem tamamlanamadı.',
	UnableToPrintDocument: 'Belge yazdırılamıyor. Lütfen daha sonra tekrar deneyiniz.',
	Unallocated: 'Tahsis edilmemiş',
	UnallocatedPaymentDescription: `Bu ödeme henüz faturalandırılabilir kalemlere tam olarak tahsis edilmedi.
 Ödenmemiş kalemlere tahsisat ekleyin veya kredi veya geri ödeme yapın.`,
	UnallocatedPaymentTitle: 'Tahsis edilmemiş ödeme',
	UnallocatedPayments: 'Tahsis edilmemiş ödemeler',
	Unarchive: 'Arşivden çıkar',
	Unassigned: 'Atanmamış',
	UnauthorisedInvoiceSnackbar: 'Bu müşteri faturalarını yönetme erişiminiz yok.',
	UnauthorisedSnackbar: 'Bunu yapmaya izniniz yok.',
	Unavailable: 'Kullanım dışı',
	Uncategorized: 'Kategorize edilmemiş',
	Unclaimed: 'Talep edilmemiş',
	UnclaimedAmount: 'İddia Edilmeyen Miktar',
	UnclaimedItems: 'Talep edilmeyen ürünler',
	UnclaimedItemsMustBeInCurrency: 'Sadece aşağıdaki para birimlerindeki öğeler desteklenir: {currencies}',
	Uncle: 'Amca',
	Unconfirmed: 'Onaylanmamış',
	Underline: 'Altını çizmek',
	Undo: 'Geri alma',
	Unfavorite: 'Beğenme',
	Uninvoiced: 'faturasız',
	UninvoicedAmount: 'Fatura edilmemiş tutar',
	UninvoicedAmounts:
		'{count, plural, =0 {Fatura edilmemiş tutar yok} one {Fatura edilmemiş tutar} other {Fatura edilmemiş tutarlar}}',
	Unit: 'Birim',
	UnitedKingdom: 'Birleşik Krallık',
	UnitedStates: 'Amerika Birleşik Devletleri',
	UnitedStatesEast: 'Amerika Birleşik Devletleri - Doğu',
	UnitedStatesWest: 'Amerika Birleşik Devletleri - Batı',
	Units: 'Birimler',
	UnitsIsRequired: 'Birim gerekli',
	UnitsMustBeGreaterThanZero: `Birimler 0'dan büyük olmalıdır`,
	UnitsPlaceholder: '1',
	Unknown: 'Bilinmeyen',
	Unlimited: 'Sınırsız',
	Unlock: 'Kilidini aç',
	UnlockNoteHelper: 'Yeni bir değişiklik yapmadan önce editörlerin notun kilidini açması gerekir.',
	UnmuteAudio: 'Sesi aç',
	UnmuteEveryone: 'Herkesin sesini aç',
	Unpaid: 'ödenmemiş',
	UnpaidInvoices: 'Ödenmemiş faturalar',
	UnpaidItems: 'Ödenmemiş kalemler',
	UnpaidMultiple: 'ödenmemiş',
	Unpublish: 'Yayından Kaldır',
	UnpublishTemplateConfirmationModalPrompt:
		'<span>{title}</span> öğesini kaldırmak, bu kaynağı Carepatron topluluğundan kaldıracaktır. Bu işlem geri alınamaz.',
	UnpublishToCommunitySuccessMessage: 'Topluluktan ‛{title}’ başarıyla kaldırıldı.',
	Unread: 'Okunmamış',
	Unrecognised: 'Tanınmıyor',
	UnrecognisedDescription:
		'Bu ödeme yöntemi mevcut uygulama sürümünüz tarafından tanınmıyor. Bu ödeme yöntemini görüntülemek ve düzenlemek için lütfen en son sürümü edinmek üzere tarayıcınızı yenileyin.',
	UnsavedChanges: 'Kaydedilmemiş değişiklikler',
	UnsavedChangesPromptContent: 'Kapatmadan önce değişikliklerinizi kaydetmek istiyor musunuz?',
	UnsavedChangesPromptTitle: 'kaydedilmemiş değişiklikleriniz mevcut',
	UnsavedNoteChangesWarning: 'Yaptığınız değişiklikler kaydedilmeyebilir',
	UnsavedTemplateChangesWarning: 'Yaptığınız değişiklikler kaydedilmeyebilir',
	UnselectAll: 'Hepsinin seçimini kaldır',
	Until: `<br>	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<br>
	<`,
	UntitledConversation: 'Başlıksız Konuşma',
	UntitledFolder: 'Başlıksız klasör',
	UntitledNote: 'Başlıksız not',
	UntitledSchedule: 'Başlıksız program',
	UntitledSection: 'Başlıksız bölüm',
	UntitledTemplate: 'Başlıksız şablon',
	Unverified: 'Doğrulanmamış',
	Upcoming: 'Yaklaşan',
	UpcomingAppointments: 'Yaklaşan randevular',
	UpcomingDateOverridesEmpty: 'Hiçbir tarih geçersiz kılma bulunamadı',
	UpdateAvailabilityScheduleFailure: 'Kullanılabilirlik planı güncellenemedi',
	UpdateAvailabilityScheduleSuccess: 'Kullanılabilirlik planı başarıyla güncellendi',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Yeni fiyatlandırmanın katılımcıların faturalarına ve taleplerine uygulanmasını ister misiniz?',
	UpdateLink: 'Güncelleme Bağlantısı',
	UpdatePrimaryEmailWarningDescription:
		'Müşterinizin e-posta adresini değiştirmeniz, mevcut randevularına ve notlarına erişimlerini kaybetmelerine neden olacaktır.',
	UpdatePrimaryEmailWarningTitle: 'Müşteri e-posta değişikliği',
	UpdateSettings: 'Ayarları güncelle',
	UpdateStatus: 'Güncelleme durumu',
	UpdateSuperbillReceiptFailure: 'Süper Fatura makbuzu güncellenemedi',
	UpdateSuperbillReceiptSuccess: 'Superbill makbuzu başarıyla güncellendi',
	UpdateTaskBillingDetails: 'Fatura ayrıntılarını güncelle',
	UpdateTaskBillingDetailsDescription:
		'Randevu fiyatlandırması değişti. Yeni fiyatlandırmanın katılımcının fatura kalemlerine, faturalarına ve taleplerine uygulanmasını ister misiniz? Devam etmek istediğiniz güncellemeleri seçin.',
	UpdateTemplateFolderSuccessMessage: 'Klasör başarıyla güncellendi',
	UpdateUnpaidInvoices: 'Ödenmemiş faturaları güncelle',
	UpdateUserInfoSuccessSnackbar: 'Kullanıcı bilgileri başarıyla güncellendi!',
	UpdateUserSettingsSuccessSnackbar: 'Kullanıcı ayarları başarıyla güncellendi!',
	Upgrade: 'Güncelleme',
	UpgradeForSMSReminder: `Sınırsız SMS hatırlatıcısı için <b>Profesyonel'e</b> yükseltin`,
	UpgradeNow: 'Hemen Yükselt',
	UpgradePlan: 'Yükseltme planı',
	UpgradeSubscriptionAlertDescription:
		'Depolama alanınız azalıyor. Uygulamanızın sorunsuz bir şekilde çalışmasını sağlamak için planınızı yükselterek ek depolama alanı açın!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Depolama alanınız azalıyor. Uygulamanızda <span>Yönetici erişimi</span> olan birinden ek depolama alanı açmak ve uygulamanızın sorunsuz çalışmasını sağlamak için planınızı yükseltmesini isteyin!',
	UpgradeSubscriptionAlertTitle: 'Aboneliğinizi yükseltmenin zamanı geldi',
	UpgradeYourPlan: 'Planınızı yükseltin',
	UploadAudio: 'Sesi Yükle',
	UploadFile: 'Dosya yükleme',
	UploadFileDescription: 'Hangi yazılım platformundan geçiş yapıyorsunuz?',
	UploadFileMaxSizeError: 'Dosya çok büyük. Maksimum dosya boyutu {fileSizeLimit}.',
	UploadFileSizeLimit: 'Boyut sınırı {size}MB',
	UploadFileTileDescription: 'Müşterilerinizi yüklemek için CSV, XLS, XLSX veya ZIP dosyalarını kullanın.',
	UploadFileTileLabel: 'Dosya Yükle',
	UploadFiles: 'Dosyaları yükle',
	UploadIndividually: 'Dosyaları tek tek yükleyin',
	UploadLogo: 'Logoyu yükle',
	UploadPhoto: 'Fotoğraf yükle',
	UploadToCarepatron: `Carepatron'a yükle`,
	UploadYourLogo: 'Logonuzu yükleyin',
	UploadYourTemplates: 'Şablonlarınızı yükleyin, biz sizin için dönüştürelim.',
	Uploading: 'Yükleniyor',
	UploadingAudio: 'Ses dosyanız yükleniyor...',
	UploadingFiles: 'Dosya yükleme',
	UrlLink: 'URL Bağlantısı',
	UsageCount: '{count} kez kullanıldı',
	UsageLimitValue: '{used}  {limit}  kullanılmış',
	UsageValue: '{kullanılmış} kullanılmış',
	Use: 'Kullanmak',
	UseAiToAutomateYourWorkflow: 'İş akışınızı otomatikleştirmek için yapay zekayı kullanın!',
	UseAsDefault: 'Varsayılan olarak kullan',
	UseCustom: 'Özel kullan',
	UseDefault: 'Varsayılanı Kullan',
	UseDefaultFilters: 'Varsayılan filtreleri kullan',
	UseTemplate: 'Şablon kullan',
	UseThisCard: 'Bu kartı kullan',
	UseValue: '"{value}" kullanın',
	UseWorkspaceDefault: 'Çalışma alanı varsayılanını kullan',
	UserIsTyping: '{name} yazıyor...',
	Username: 'Kullanıcı adı',
	Users: 'Kullanıcılar',
	VAT: 'KDV',
	ValidUrl: 'URL bağlantısı geçerli bir URL olmalıdır.',
	Validate: 'Doğrula',
	Validated: 'Doğrulandı',
	Validating: 'Doğrulama',
	ValidatingContent: 'İçerik doğrulanıyor...',
	ValidatingTranscripts: 'Transkriptler doğrulanıyor...',
	ValidationConfirmPasswordRequired: 'Şifreyi Onayla gerekli',
	ValidationDateMax: '{max}’dan önce olmalı',
	ValidationDateMin: `{min}'den sonra olmalı`,
	ValidationDateRange: 'Başlangıç ve bitiş tarihi gerekli',
	ValidationEndDateMustBeAfterStartDate: 'Bitiş tarihi, başlangıç tarihinden sonra olmalıdır.',
	ValidationMixedDefault: 'Bu geçersiz',
	ValidationMixedRequired: 'bu gereklidir',
	ValidationNumberInteger: 'Tam sayı olmalı',
	ValidationNumberMax: '{max} veya daha az olmalı',
	ValidationNumberMin: '{min} veya daha fazla olmalı',
	ValidationPasswordNotMatching: 'Şifreler eşleşmiyor',
	ValidationPrimaryAddressIsRequired: 'Varsayılan olarak ayarlandığında adres gereklidir',
	ValidationPrimaryPhoneNumberIsRequired: 'Varsayılan olarak ayarlandığında telefon numarası gereklidir',
	ValidationServiceMustBeNotBeFuture: 'Servis, günümüzde veya gelecekte olmamalıdır.',
	ValidationStringEmail: 'Geçerli bir e-posta olmalı',
	ValidationStringMax: '{max} karakter veya daha az olmalı',
	ValidationStringMin: '{min} veya daha fazla karakter olmalı.',
	ValidationStringPhoneNumber: 'Geçerli bir telefon numarası olmalı',
	ValueMinutes: '{value} dakika',
	VerbosityConcise: 'Özlü',
	VerbosityDetailed: 'Ayrıntılı',
	VerbosityStandard: 'Standart',
	VerbositySuperDetailed: 'Süper detaylı',
	VerificationCode: 'Doğrulama kodu',
	VerificationEmailDescription: 'Lütfen e-posta adresinizi ve size yeni gönderdiğimiz doğrulama kodunu girin.',
	VerificationEmailSubtitle: 'Spam klasörünü kontrol edin - e-posta gelmediyse',
	VerificationEmailTitle: 'E-postayı doğrula',
	VerificationOption: 'E-posta Doğrulaması',
	Verified: 'Doğrulandı',
	Verify: 'Doğrula',
	VerifyAndSubmit: 'Doğrula ve gönder',
	VerifyEmail: 'E-postayı doğrula',
	VerifyEmailAccessCode: 'Onay kodu',
	VerifyEmailAddress: 'E-posta Adresini doğrulayın',
	VerifyEmailButton: 'Doğrulayın ve oturumu kapatın',
	VerifyEmailSentSnackbar: 'Doğrulama e-postası gönderildi. Gelen kutunu kontrol et.',
	VerifyEmailSubTitle: 'E-posta ulaşmadıysa Spam klasörünü kontrol edin',
	VerifyEmailSuccessLogOutSnackbar: 'Başarı! Değişiklikleri uygulamak için lütfen çıkış yapın.',
	VerifyEmailSuccessSnackbar:
		'Başarı! E-posta Doğrulandı. Doğrulanmış bir hesap olarak devam etmek için lütfen giriş yapın.',
	VerifyEmailTitle: 'Eposta adresinizi doğrulayın',
	VerifyNow: 'Şimdi doğrula',
	Veterinarian: 'Veteriner hekim',
	VideoCall: 'Görüntülü arama',
	VideoCallAudioInputFailed: 'Ses giriş aygıtı çalışmıyor',
	VideoCallAudioInputFailedMessage: 'Ayarları açın ve mikrofon kaynağının doğru şekilde ayarlandığını kontrol edin',
	VideoCallChatBanner:
		'Mesajlar bu görüşmedeki herkes tarafından görülebilir ve görüşme sona erdiğinde silinecektir.',
	VideoCallChatSendBtn: 'Bir mesaj göndermek',
	VideoCallChatTitle: 'Sohbet',
	VideoCallDisconnectedMessage: 'Bağlantı kesildi',
	VideoCallOptionInfo: 'Zoom bağlanmadıysa Carepatron randevularınız için görüntülü aramaları yönetecek',
	VideoCallTilePaused: 'Bu video, ağınızdaki sorunlar nedeniyle duraklatıldı',
	VideoCallTranscriptionFormDescription: 'Bu ayarları istediğiniz zaman değiştirebilirsiniz',
	VideoCallTranscriptionFormHeading: `AI Scribe'ınızı özelleştirin`,
	VideoCallTranscriptionFormLanguageField: 'Oluşturulan çıktı dili',
	VideoCallTranscriptionFormNoteTemplateField: 'Varsayılan not şablonunu ayarla',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'AI içeren şablon bulunamadı',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Bir şablon seçin',
	VideoCallTranscriptionPronounField: 'Zamiriniz',
	VideoCallTranscriptionRecordingNote:
		'Oturumun sonunda, oluşturulmuş bir <strong>{noteTemplate} notu</strong> ve transkript alacaksınız.',
	VideoCallTranscriptionReferClientField: 'Müşteriye şu şekilde atıfta bulunun:',
	VideoCallTranscriptionReferPractitionerField: 'Uygulayıcıya şu şekilde atıfta bulunun:',
	VideoCallTranscriptionTitle: 'AI Yazıcısı',
	VideoCallTranscriptionVerbosityField: 'Sözcük zenginliği',
	VideoCallTranscriptionWritingPerspectiveField: 'Yazım perspektifi',
	VideoCalls: 'Video görüşmeleri',
	VideoConferencing: 'Video konferans',
	VideoOff: 'Video kapalı',
	VideoOn: 'Video kapalı',
	VideoQual360: 'Düşük kalite (360p)',
	VideoQual540: 'Orta kalite (540p)',
	VideoQual720: 'Yüksek kalite (720p)',
	View: 'Görüş',
	ViewAll: 'Tümünü görüntüle',
	ViewAppointment: 'Randevuyu Görüntüle',
	ViewBy: 'Şuna göre görüntüle:',
	ViewClaim: 'İddiayı Görüntüle',
	ViewCollection: 'Koleksiyonu görüntüle',
	ViewDetails: 'Ayrıntıları görüntüle',
	ViewEnrollment: 'Kaydı Görüntüle',
	ViewPayment: 'Ödemeyi Görüntüle',
	ViewRecord: 'Kaydı görüntüle',
	ViewRemittanceAdvice: `Havale Özeti'ni Görüntüle`,
	ViewRemittanceAdviceHeader: 'Havale İrsaliyesi Talebi',
	ViewRemittanceAdviceSubheader: 'Talep {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Ayarları Görüntüle',
	ViewStripeDashboard: 'Stripe kontrol panelini görüntüle',
	ViewTemplate: 'Şablonu Görüntüle',
	ViewTemplates: 'Şablonları görüntüle',
	ViewableBy: 'Görüntüleyen:',
	ViewableByHelper:
		'Siz ve Ekip, yayınladığınız notlara her zaman erişebilirsiniz. Bu notu müşterinizle ve/veya ilişkileriyle paylaşmayı seçebilirsiniz.',
	Viewer: 'Görüntüleyici',
	VirtualLocation: 'Sanal konum',
	VisibleTo: 'Görünür',
	VisitOurHelpCentre: 'Yardım Merkezimizi ziyaret edin',
	VisualEffects: 'Görsel efektler',
	VoiceFocus: 'Ses odağı',
	VoiceFocusLabel: 'Mikrofonunuzdan konuşma dışındaki sesleri filtreler',
	Void: 'Geçersiz',
	VoidCancelPriorClaim: 'İptal/Önceki talebi geçersiz kıl',
	WaitingforMins: '{count} dk. Bekleniyor',
	Warning: 'Uyarı',
	WatchAVideo: 'bir video izle',
	WatchDemoVideo: 'Demo videosunu izle',
	WebConference: 'Web konferansı',
	WebConferenceOrVirtualLocation: 'Web konferansı / sanal konum',
	WebDeveloper: 'Web Geliştiricisi',
	WebsiteOptional: 'Web sitesi<span>(İsteğe bağlı)</span>',
	WebsiteUrl: `Web Sitesi URL'si`,
	Wednesday: 'Çarşamba',
	Week: 'Hafta',
	WeekPlural: '{count, plural, one {hafta} other {haftalar}}',
	Weekly: 'Haftalık',
	WeeksPlural: '{age, plural, one {# hafta} other {# hafta}}',
	WelcomeBack: 'tekrar hoşgeldiniz',
	WelcomeBackName: 'Merhaba {name}, hoş geldin',
	WelcomeName: 'Hoş geldiniz {name}',
	WelcomeToCarepatron: `Carepatron'a hoş geldiniz`,
	WhatCanIHelpWith: 'Ne konuda yardımcı olabilirim?',
	WhatDidYouLikeResponse: 'Bu yanıtta neyi beğendin?',
	WhatIsCarepatron: 'Carepatron nedir?',
	WhatMadeYouCancel: `Planını iptal etmene ne sebep oldu?
 Tüm başvuruyu kontrol et.`,
	WhatServicesDoYouOffer: 'Hangi <mark>hizmetleri</mark> sunuyorsunuz?',
	WhatServicesDoYouOfferDescription: 'Daha sonra hizmetleri düzenleyebilir veya daha fazla hizmet ekleyebilirsiniz.',
	WhatsYourAvailability: '<mark>Müsaitliğiniz</mark> nasıl?',
	WhatsYourAvailabilityDescription: 'Daha sonra daha fazla program ekleyebilirsiniz.',
	WhatsYourBusinessName: '<mark>İşletmenizin adı</mark> nedir?',
	WhatsYourTeamSize: 'Ekibinizin <mark>boyutu nedir?</mark>',
	WhatsYourTeamSizeDescription: 'Bu, çalışma alanınızı doğru şekilde kurmamıza yardımcı olacaktır.',
	WhenThisHappens: 'Bu durum gerçekleştiğinde:',
	WhichBestDescribesYou: 'Sizi en iyi <mark>hangisi tanımlar?</mark>',
	WhichPlatforms: 'Hangi platformlar?',
	Wife: 'Eş',
	WorkflowDescription: 'İş akışı açıklaması',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Şablonlar, daha sorunsuz işlemler için iş akışlarına bağlanabilir. Bağlı iş akışlarını görüntülemek, bunları kolayca izlemek ve güncellemek için.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Ortak tetikleyicilere göre SMS + e-postalarınızı bağlayın',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'İş Akışı Otomasyonları',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Otomatik iş akışları',
	WorkflowTemplateConfigKey_Body: 'Gövde',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Markayı Göster',
	WorkflowTemplateConfigKey_Content: 'İçerik',
	WorkflowTemplateConfigKey_Footer: 'Alt Bilgi',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Alt Bilgi Çubuğunu Göster',
	WorkflowTemplateConfigKey_Header: 'Başlık',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Başlığı Göster',
	WorkflowTemplateConfigKey_SecurityFooter: 'Güvenlik altbilgisi',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Güvenlik altbilgisini göster',
	WorkflowTemplateConfigKey_Subject: 'Konu',
	WorkflowTemplateConfigKey_Title: 'Başlık',
	WorkflowTemplateDeleteConfirmationMessage: 'Bu şablonu silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
	WorkflowTemplateDeleteConfirmationTitle: 'Bildirim şablonunu sil',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Emin misiniz? Bu yalnızca {locale} sürümünü kaldıracaktır - diğer diller etkilenmeyecektir. Bu işlem geri alınamaz.',
	WorkflowTemplateDeleteLocalisationDialogTitle: '‘{locale}’ şablonunu sil',
	WorkflowTemplateDeletedSuccess: 'Bildirim şablonu başarıyla silindi.',
	WorkflowTemplateEditorDetailsTab: 'Şablon ayrıntıları',
	WorkflowTemplateEditorEmailContent: 'E-posta içeriği',
	WorkflowTemplateEditorEmailContentTab: 'E-posta içeriği',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert: 'Önizlemeler, müşterilerinizin ne göreceğini göstermek için örnek veri kullanır.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Emin misiniz? Bu işlem sürümü sistemin varsayılan şablonuna geri döndürecektir. Bu işlem geri alınamaz.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Şablonu Sıfırla',
	WorkflowTemplateSendTestEmail: 'Test e-postası gönder',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Kendinize bir test e-postası göndererek e-posta kurulumunuzu deneyin.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Alıcı e-postası',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Test Gönder',
	WorkflowTemplateSendTestEmailDialogTitle: 'Test e-postası gönder',
	WorkflowTemplateSendTestEmailSuccess: 'Başarı! <mark>{templateName}</mark> test e-postanız gönderildi.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Şablonlarınızı yönetin ve müşterilerinizle etkili bir şekilde iletişim kurmak için birden fazla dilde versiyon ekleyin.',
	WorkflowTemplateTemplateEditor: 'Şablon düzenleyici',
	WorkflowTemplateTranslateLocaleError: 'İçerik çevrilirken bir hata oluştu.',
	WorkflowTemplateTranslateLocaleSuccess: 'Başarıyla içeriği **{locale}** diline çevirdi.',
	WorkflowsAndReminders: 'İş akışları ',
	WorkflowsManagement: 'İş Akışları Yönetimi',
	WorksheetAndHandout: 'Çalışma Sayfası/El İmzalı Doküman',
	WorksheetsAndHandoutsDescription: 'Müşteri etkileşimi ve eğitimi için',
	Workspace: 'Çalışma alanı',
	WorkspaceBranding: 'Çalışma alanı markalaması',
	WorkspaceBrandingDescription: `Çalışma alanınızı, tarzınızı yansıtan uyumlu bir stille zahmetsizce markalayın
 profesyonellik ve kişilik. Güzel bir tatil için faturaları çevrimiçi rezervasyona göre özelleştirin
 müşteri deneyimi.`,
	WorkspaceName: 'Çalışma alanı adı',
	Workspaces: 'Çalışma alanları',
	WriteOff: 'Hurdaya çıkarmak',
	WriteOffModalDescription:
		'<mark>{count} {count, plural, one {satır öğesi} other {satır öğeleri}}</mark> silinmeyi bekliyor',
	WriteOffModalTitle: 'Yazma düzeltmesi',
	WriteOffReasonHelperText: 'Bu dahili bir nottur ve müşteriniz tarafından görülemeyecektir.',
	WriteOffReasonPlaceholder: 'Faturalandırılabilir işlemleri incelerken bir silme nedeni eklemek yardımcı olabilir',
	WriteOffTotal: 'Toplam Amortisman ({currencyCode})',
	Writer: 'yazar',
	Yearly: 'Yıllık',
	YearsPlural: '{age, plural, one {# yıl} other {# yıl}}',
	Yes: 'Evet',
	YesArchive: 'Evet, arşiv',
	YesDelete: 'Evet sil',
	YesDeleteOverride: 'Evet, geçersiz kılmayı sil',
	YesDeleteSection: 'Evet sil',
	YesDisconnect: 'Evet, bağlantıyı kes',
	YesEnd: 'Evet, son',
	YesEndTranscription: 'Evet, transkripsiyonu sonlandır',
	YesImFineWithThat: 'Evet, bunda sorun yok',
	YesLeave: 'Evet, bırak',
	YesMinimize: 'Evet, küçült',
	YesOrNoAnswerTypeDescription: 'Yanıt türünü yapılandırın',
	YesOrNoFormPrimaryText: 'Evet | HAYIR',
	YesOrNoFormSecondaryText: 'Evet veya hayır seçeneklerini seçin',
	YesProceed: 'Evet, devam edin.',
	YesRemove: 'Evet, kaldır',
	YesRestore: 'Evet, geri yükle',
	YesStopIgnoring: 'Evet, yoksaymayı durdur',
	YesTransfer: 'Evet, transfer',
	Yesterday: 'Dün',
	YogaInstructor: 'Yoga eğitmeni',
	You: 'Sen',
	YouArePresenting: 'Sen sunum yapıyorsun',
	YouCanChooseMultiple: 'Birden fazla seçebilirsiniz',
	YouCanSelectMultiple: 'Birden fazla seçebilirsiniz',
	YouHaveOngoingTranscription: 'Devam eden bir transkripsiyonunuz var',
	YourAnswer: 'Cevabınız',
	YourDisplayName: 'Görünen adınız',
	YourSpreadsheetColumns: 'E-tablo sütunlarınız',
	YourTeam: 'Takımın',
	ZipCode: 'Posta kodu',
	Zoom: 'Yakınlaştır',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Bu ekip üyesi için Zoom görüşmesi ekleyemezsiniz. <a>Daha fazla bilgi için lütfen destek belgelerine bakın.</a>',
};

export default items;
