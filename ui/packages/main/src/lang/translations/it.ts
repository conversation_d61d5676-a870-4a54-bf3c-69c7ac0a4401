import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'Codice Fiscale',
	AIPrompts: `L'intelligenza artificiale suggerisce`,
	ATeamMemberIsRequired: 'È necessario un membro del team',
	AboutClient: 'Informazioni sul cliente',
	AcceptAppointment: 'Grazie per aver confermato il tuo appuntamento',
	AcceptTermsAndConditionsRequired: 'Accetta i termini ',
	Accepted: 'Accettato',
	AccessGiven: 'Accesso concesso',
	AccessPermissions: 'Autorizzazioni di accesso',
	AccessType: 'Tipo di accesso',
	Accident: 'Incidente',
	Account: 'Account',
	AccountCredit: 'Credito sul conto',
	Accountant: 'Contabile',
	Action: 'Azione',
	Actions: 'Azioni',
	Active: 'Attivo',
	ActiveTags: 'Tag attivi',
	ActiveUsers: 'Utenti attivi',
	Activity: 'Attività',
	Actor: 'Attore',
	Acupuncture: 'Agopuntura',
	Acupuncturist: 'Agopunto<PERSON>',
	Acupuncturists: 'Agopuntori',
	AcuteManifestationOfAChronicCondition: 'Manifestazione acuta di una condizione cronica',
	Add: 'Aggiungere',
	AddADescription: 'Aggiungi una descrizione',
	AddALocation: 'Aggiungi posizione',
	AddASecondTimezone: 'Aggiungi un secondo fuso orario',
	AddAddress: 'Aggiungi indirizzo',
	AddAnother: '  Aggiungine un altro',
	AddAnotherAccount: 'Aggiungi un altro account',
	AddAnotherContact: 'Aggiungi un altro contatto',
	AddAnotherOption: `Aggiungi un'altra opzione`,
	AddAnotherTeamMember: 'Aggiungi un altro membro del team',
	AddAvailablePayers: '+ Aggiungi i pagatori disponibili',
	AddAvailablePayersDescription: `Cerca i pagatori da aggiungere all'elenco dei pagatori del tuo spazio di lavoro. Dopo averli aggiunti, puoi gestire le iscrizioni o modificare i dettagli del pagatore secondo necessità.`,
	AddCaption: 'Aggiungi didascalia',
	AddClaim: 'Aggiungi reclamo',
	AddClientFilesModalDescription: `Per limitare l'accesso, seleziona le opzioni nelle caselle di controllo "Visualizzabile da"`,
	AddClientFilesModalTitle: 'Carica i file per {name}',
	AddClientNoteButton: 'Aggiungi nota',
	AddClientNoteModalDescription:
		'Aggiungi contenuto alla tua nota. Utilizza la sezione "Visualizzabile da" per selezionare uno o più gruppi che possono vedere questa nota specifica.',
	AddClientNoteModalTitle: 'Aggiungi nota',
	AddClientOwnerRelationshipModalDescription: `Invitando il cliente, quest'ultimo potrà gestire le informazioni del proprio profilo e l'accesso come utente a tali informazioni.`,
	AddClientOwnerRelationshipModalTitle: 'Invita il cliente',
	AddCode: 'Aggiungi codice',
	AddColAfter: 'Aggiungi colonna dopo',
	AddColBefore: 'Aggiungi colonna prima',
	AddCollection: 'Aggiungi raccolta',
	AddColor: 'Aggiungi colore',
	AddColumn: 'Aggiungi colonna',
	AddContactRelationship: 'Aggiungi relazione di contatto',
	AddContacts: 'Aggiungi contatti',
	AddCustomField: 'Aggiungi campo personalizzato',
	AddDate: 'Aggiungi data',
	AddDescription: 'Aggiungi descrizione',
	AddDetail: 'Aggiungi dettaglio',
	AddDisplayName: 'Aggiungi nome visualizzato',
	AddDxCode: 'Aggiungi codice di diagnosi',
	AddEmail: 'Aggiungi email',
	AddFamilyClientRelationshipModalDescription: `Invitare un familiare gli consentirà di vedere le storie di assistenza e le informazioni del profilo del cliente. Se sono invitati come amministratori, avranno accesso per aggiornare le informazioni del profilo del cliente e gestire l'accesso utente.`,
	AddFamilyClientRelationshipModalTitle: 'Invita un membro della famiglia',
	AddField: 'Aggiungi campo',
	AddFormField: 'Aggiungi campo modulo',
	AddImages: 'Aggiungi immagini',
	AddInsurance: 'Aggiungere assicurazione',
	AddInvoice: 'Aggiungi fattura',
	AddLabel: 'Aggiungi etichetta',
	AddLanguage: 'Aggiungi lingua',
	AddLocation: 'Aggiungi posizione',
	AddManually: 'Aggiungi manualmente',
	AddMessage: 'Aggiungi messaggio',
	AddNewAction: 'Aggiungi nuova azione',
	AddNewSection: 'Aggiungi nuova sezione',
	AddNote: 'Aggiungi nota',
	AddOnlineBookingDetails: 'Aggiungi i dettagli della prenotazione online',
	AddPOS: 'Aggiungi POS',
	AddPaidInvoices: 'Aggiungi fatture pagate',
	AddPayer: 'Aggiungi pagatore',
	AddPayment: 'Aggiungi pagamento',
	AddPaymentAdjustment: 'Aggiungi aggiustamento pagamento',
	AddPaymentAdjustmentDisabledDescription: 'Le assegnazioni dei pagamenti non subiranno modifiche.',
	AddPaymentAdjustmentEnabledDescription: `L'importo disponibile per l'assegnazione sarà ridotto.`,
	AddPhoneNumber: 'Aggiungi numero di telefono',
	AddPhysicalOrVirtualLocations: 'Aggiungi posizioni fisiche o virtuali',
	AddQuestion: 'Aggiungi domanda',
	AddQuestionOrTitle: 'Aggiungi una domanda o un titolo',
	AddRelationship: 'Aggiungi relazione',
	AddRelationshipModalTitle: 'Connetti il contatto esistente',
	AddRelationshipModalTitleNewClient: 'Connetti nuovo contatto',
	AddRow: 'Aggiungi riga',
	AddRowAbove: 'Aggiungi riga sopra',
	AddRowBelow: 'Aggiungi riga sotto',
	AddService: 'Aggiungi servizio',
	AddServiceLocation: 'Aggiungi la posizione del servizio',
	AddServiceToCollections: 'Aggiungi il servizio alle raccolte',
	AddServiceToOneOrMoreCollections: 'Aggiungi il servizio a una o più raccolte',
	AddServices: 'Aggiungi servizi',
	AddSignature: 'Aggiungi firma',
	AddSignaturePlaceholder: 'Digita ulteriori dettagli da includere con la tua firma',
	AddSmartDataChips: 'Aggiungere chip di dati intelligenti',
	AddStaffClientRelationshipsModalDescription:
		'La selezione del personale consentirà loro di creare e visualizzare le storie di assistenza per questo cliente. Saranno anche in grado di visualizzare le informazioni del cliente.',
	AddStaffClientRelationshipsModalTitle: 'Aggiungere relazioni tra il personale',
	AddTag: 'Aggiungi un tag',
	AddTags: 'Aggiungi tag',
	AddTemplate: 'Aggiungi modello',
	AddTimezone: 'Aggiungi fuso orario',
	AddToClaim: 'Aggiungi al reclamo',
	AddToCollection: 'Aggiungi alla raccolta',
	AddToExisting: 'Aggiungi a esistente',
	AddToStarred: 'Aggiungi a preferiti',
	AddUnclaimedItems: 'Aggiungi articoli non reclamati',
	AddUnrelatedContactWarning:
		'Hai aggiunto qualcuno che non è un contatto di {contact}. Assicurati che il contenuto sia pertinente prima di procedere con la condivisione.',
	AddValue: 'Aggiungi "{value}"',
	AddVideoCall: 'Aggiungi videochiamata',
	AddVideoOrVoiceCall: 'Aggiungi video o chiamata vocale',
	AddictionCounselor: 'Consulente per le dipendenze',
	AddingManualPayerDisclaimer: `L'aggiunta manuale di un pagatore all'elenco dei fornitori non determina l'impostazione di una connessione per la presentazione elettronica dei reclami con quel pagatore, ma può essere utilizzata per creare reclami manualmente.`,
	AddingTeamMembersIncreaseCostAlert: `L'aggiunta di nuovi membri al team aumenterà il tuo abbonamento mensile.`,
	Additional: 'Aggiuntivo',
	AdditionalBillingProfiles: 'Profili di fatturazione aggiuntivi',
	AdditionalBillingProfilesSectionDescription:
		'Sostituisci le informazioni di fatturazione predefinite utilizzate per specifici membri del team, pagatori o modelli di fattura.',
	AdditionalFeedback: 'Feedback aggiuntivo',
	AddnNewWorkspace: 'Nuovo spazio di lavoro',
	AddnNewWorkspaceSuccessSnackbar: `L'area di lavoro è stata creata!`,
	Address: 'Indirizzo',
	AddressNumberStreet: 'Indirizzo (n., via)',
	Adjustment: 'Regolazione',
	AdjustmentType: 'Tipo di regolazione',
	Admin: 'Amministratore',
	Admins: 'Amministratori',
	AdminsOnly: 'Solo amministratori',
	AdvancedPlanInclusionFive: 'Account Manager',
	AdvancedPlanInclusionFour: 'Analisi di Google',
	AdvancedPlanInclusionHeader: 'Tutto in Plus  ',
	AdvancedPlanInclusionOne: 'Ruoli ',
	AdvancedPlanInclusionSix: `Supporto per l'importazione dei dati`,
	AdvancedPlanInclusionThree: 'Etichettatura bianca',
	AdvancedPlanInclusionTwo: 'Conservazione dei dati eliminati per 90 giorni',
	AdvancedPlanMessage: `Rimani in controllo delle esigenze del tuo studio. Rivedi il tuo piano attuale e monitora l'utilizzo.`,
	AdvancedSettings: 'Impostazioni avanzate',
	AdvancedSubscriptionPlanSubtitle: 'Espandi la tua pratica con tutte le funzionalità',
	AdvancedSubscriptionPlanTitle: 'Avanzato',
	AdvertisingManager: 'Responsabile della pubblicità',
	AerospaceEngineer: 'Ingegnere aerospaziale',
	AgeYearsOld: '{age} anni',
	Agenda: 'Ordine del giorno',
	AgendaView: `Vista dell'agenda`,
	AiAskSupportedFileTypes: 'Tipi di file supportati: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Un assistente a portata di mano',
	AiCopilotDisclaimer: `L'IA Copilot può commettere errori. Controlla le informazioni importanti.`,
	AiCreateNewConversation: 'Crea nuova conversazione',
	AiEnhanceYourProductivity: 'Migliora la tua produttività',
	AiPoweredTemplates: `Modelli basati sull'intelligenza artificiale`,
	AiScribeNoDeviceFoundErrorMessage:
		'Sembra che il tuo browser non supporti questa funzionalità oppure che non siano disponibili dispositivi compatibili.',
	AiScribeUploadFormat: 'Tipi di file supportati: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'solo 1 file alla volta',
	AiShowConversationHistory: 'Mostra la cronologia della conversazione',
	AiSmartPromptNodePlaceholderText:
		'Digita il tuo prompt personalizzato qui per aiutare a generare risultati di intelligenza artificiale accurati e personalizzati.',
	AiSmartPromptPrimaryText: 'Ai suggerimento intelligente',
	AiSmartPromptSecondaryText: 'Inserisci prompt intelligenti personalizzati AI',
	AiSmartReminders: 'Promemoria intelligenti AI',
	AiTemplateBannerTitle: `Semplifica il tuo lavoro con modelli basati sull'intelligenza artificiale`,
	AiTemplates: 'Modelli di intelligenza artificiale',
	AiTokens: 'Token AI',
	AiWorkBetterWithAi: `Lavora meglio con l'IA`,
	All: 'Tutto',
	AllAppointments: 'Tutti gli appuntamenti',
	AllCategories: 'Tutte le categorie',
	AllClients: 'Tutti i clienti',
	AllContactPolicySelectorLabel: 'Tutti i contatti di <mark>{client}</mark>',
	AllContacts: 'Tutti i contatti',
	AllContactsOf: 'Tutti i contatti di ‘{nome}’',
	AllDay: 'Tutto il giorno',
	AllInboxes: 'Tutte le caselle di posta',
	AllIndustries: 'Tutti i settori',
	AllLocations: 'Tutte le località',
	AllMeetings: 'Tutti gli incontri',
	AllNotificationsRestoredMessage: 'Tutte le notifiche ripristinate',
	AllProfessions: 'Tutte le professioni',
	AllReminders: 'Tutti i promemoria',
	AllServices: 'Tutti i servizi',
	AllStatuses: 'Tutti gli stati',
	AllTags: 'Tutti i tag',
	AllTasks: 'Tutti i compiti',
	AllTeamMembers: 'Tutti i membri del team',
	AllTypes: 'Tutti i tipi',
	Allocated: 'Assegnato',
	AllocatedItems: 'Articoli assegnati',
	AllocationTableEmptyState: 'Nessuna allocazione di pagamento trovata',
	AllocationTotalWarningMessage: `L'importo stanziato supera l'importo totale del pagamento.
 Si prega di esaminare le voci di seguito.`,
	AllowClientsToCancelAnytime: 'Consenti ai clienti di annullare in qualsiasi momento',
	AllowNewClient: 'Consentire nuovi clienti',
	AllowNewClientHelper: 'I nuovi clienti possono prenotare questo servizio',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: `Permetti almeno {hours} ore prima dell'appuntamento`,
	AllowToUseSavedCard: 'Consenti a {provider} di utilizzare la carta salvata in futuro',
	AllowVideoCalls: 'Consenti videochiamate',
	AlreadyAdded: 'Già aggiunto',
	AlreadyHasAccess: 'Ha accesso',
	AlreadyHasAccount: 'Hai già un account?',
	Always: 'Sempre',
	AlwaysIgnore: 'Ignora sempre',
	Amount: 'Quantità',
	AmountDue: 'Importo dovuto',
	AmountOfReferralRequests: '{amount, plural, one {# richiesta di referral} other {# richieste di referral}}',
	AmountPaid: 'Importo pagato',
	AnalyzingAudio: 'Analisi audio in corso...',
	AnalyzingInputContent: 'Analisi del contenuto di input...',
	AnalyzingRequest: 'Analisi della richiesta...',
	AnalyzingTemplateContent: 'Analisi del contenuto del modello...',
	And: 'E',
	Annually: 'Annualmente',
	Anonymous: 'Anonimo',
	AnswerExceeded: 'La risposta deve contenere meno di 300 caratteri.',
	AnyStatus: 'Qualsiasi stato',
	AppNotifications: 'Notifiche',
	AppNotificationsClearanceHeading: 'Ottimo lavoro! Hai cancellato tutte le attività',
	AppNotificationsEmptyHeading: `La tua attività nell'area di lavoro apparirà qui a breve`,
	AppNotificationsEmptySubtext: 'Per ora non ci sono azioni da intraprendere',
	AppNotificationsIgnoredCount: '{total} ignored',
	AppNotificationsUnread: '{total} non letti',
	Append: 'Aggiungere',
	Apply: 'Fare domanda a',
	ApplyAccountCredit: `Applicare credito sull'account`,
	ApplyDiscount: 'Applica sconto',
	ApplyVisualEffects: 'Applicare effetti visivi',
	ApplyVisualEffectsNotSupported: 'Applica effetti visivi non supportati',
	Appointment: 'Appuntamento',
	AppointmentAssignedNotificationSubject: '{actorProfileName} ti ha assegnato {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} ha annullato {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} ha confermato {appointmentName}',
	AppointmentDetails: `Dettagli dell'appuntamento`,
	AppointmentLocation: `Luogo dell'appuntamento`,
	AppointmentLocationDescription:
		'Gestisci le tue posizioni predefinite virtuali e fisiche. Quando viene programmato un appuntamento, queste posizioni saranno applicate automaticamente.',
	AppointmentNotFound: 'Appuntamento non trovato',
	AppointmentReminder: 'Promemoria appuntamento',
	AppointmentReminders: 'Promemoria appuntamenti',
	AppointmentRemindersInfo:
		'Imposta promemoria automatici per gli appuntamenti dei clienti per evitare mancate presentazioni e cancellazioni',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} ha riprogrammato {appointmentName}',
	AppointmentSaved: 'Appuntamento salvato',
	AppointmentStatus: `Stato dell'appuntamento`,
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}ora {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}ore {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Appuntamento annullato',
	Appointments: 'Appuntamenti',
	Archive: 'Archivio',
	ArchiveClients: 'Archivia i clienti',
	Archived: 'Archiviato',
	AreYouAClient: 'Sei un cliente?',
	AreYouStillThere: 'Sei ancora lì?',
	AreYouSure: 'Sei sicuro?',
	Arrangements: 'Disposizioni',
	ArtTherapist: 'Arteterapeuta',
	Articles: 'Articoli',
	Artist: 'Artista',
	AskAI: `Chiedi all'IA`,
	AskAiAddFormField: 'Aggiungere un campo modulo',
	AskAiChangeFormality: 'Cambia formalità',
	AskAiChangeToneToBeMoreProfessional: 'Cambia tono per essere più professionale',
	AskAiExplainThis: 'ChiediAiSpiegaQuesto',
	AskAiExplainWhatThisDocumentIsAbout: 'Spiega di cosa tratta questo documento',
	AskAiExplainWhatThisImageIsAbout: 'Spiega di cosa tratta questa immagine',
	AskAiFixSpellingAndGrammar: `Correggi l'ortografia e la grammatica`,
	AskAiGenerateACaptionForThisImage: 'Genera una didascalia per questa immagine',
	AskAiGenerateFromThisPage: 'Genera da questa pagina',
	AskAiGetStarted: 'Iniziare',
	AskAiGiveItAFriendlyTone: 'Dagli un tono amichevole',
	AskAiGreeting: 'Ciao {firstName}! Come posso aiutarti oggi?',
	AskAiHowCanIHelpWithYourContent: 'Come posso aiutarti con i tuoi contenuti?',
	AskAiInsert: 'Inserire',
	AskAiMakeItMoreCasual: 'Rendilo più informale',
	AskAiMakeThisTextMoreConcise: 'Rendi questo testo più conciso',
	AskAiMoreProfessional: 'Più professionale',
	AskAiOpenPreviousNote: 'Apri nota precedente',
	AskAiPondering: 'Riflettendo',
	AskAiReplace: 'Sostituire',
	AskAiReviewOrEditSelection: 'Rivedi o modifica la selezione',
	AskAiRuminating: 'Ruminando',
	AskAiSeeMore: 'Vedi altro',
	AskAiSimplifyLanguage: 'Semplificare il linguaggio',
	AskAiSomethingWentWrong:
		'Qualcosa è andato storto. Se il problema persiste, contattaci tramite il nostro centro assistenza.',
	AskAiStartWithATemplate: 'Inizia con un modello',
	AskAiSuccessfullyCopiedResponse: 'Risposta AI copiata correttamente',
	AskAiSuccessfullyInsertedResponse: 'Risposta AI inserita correttamente',
	AskAiSuccessfullyReplacedResponse: 'Risposta AI sostituita con successo',
	AskAiSuggested: 'Suggerito',
	AskAiSummariseTextIntoBulletPoints: 'Riassumere il testo in punti elenco',
	AskAiSummarizeNote: 'Riassumere nota',
	AskAiThinking: 'Pensiero',
	AskAiToday: 'Oggi {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Cosa vuoi fare con questo modulo?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Scrivi una nota professionale utilizzando il modello',
	AskAskAiAnything: `Chiedi qualsiasi cosa all'IA`,
	AskWriteSearchAnything: `Chiedi, scrivi '@' o cerca qualsiasi cosa...`,
	Asking: 'Chiedere',
	Assessment: 'Valutazione',
	Assessments: 'Valutazioni',
	AssessmentsCategoryDescription: 'Per la registrazione delle valutazioni dei clienti',
	AssignClients: 'Assegnare i clienti',
	AssignNewClients: 'Assegnare i clienti',
	AssignServices: 'Assegnare servizi',
	AssignTeam: 'Assegna squadra',
	AssignTeamMember: 'Assegna membro del team',
	Assigned: 'Assegnato',
	AssignedClients: 'Clienti assegnati',
	AssignedServices: 'Servizi assegnati',
	AssignedServicesDescription:
		'Visualizza e gestisci i servizi che ti sono stati assegnati, modificando i prezzi in base alle tue tariffe personalizzate. ',
	AssignedTeam: 'Squadra assegnata',
	AthleticTrainer: 'Preparatore atletico',
	AttachFiles: 'Allega file',
	AttachLogo: 'Allegare',
	Attachment: 'Allegato',
	AttachmentBlockedFileType: 'Bloccato per motivi di sicurezza!',
	AttachmentTooLargeFileSize: 'File troppo grande',
	AttachmentUploadItemComplete: 'Completare',
	AttachmentUploadItemError: 'Caricamento non riuscito',
	AttachmentUploadItemLoading: 'Caricamento',
	AttemptingToReconnect: 'Tentativo di riconnessione...',
	Attended: 'Ha partecipato',
	AttendeeBeingMutedTooltip: `L'host ti ha disattivato l'audio. Usa "alza la mano" per richiedere di riattivare l'audio`,
	AttendeeWithId: 'Partecipante {attendeeId}',
	Attendees: 'Partecipanti',
	AttendeesCount: '{count} partecipanti',
	Attending: 'Partecipare',
	Audiologist: 'Audiologo',
	Aunt: 'Zia',
	Australia: 'Australia',
	AuthenticationCode: 'Codice di autenticazione',
	AuthoriseProvider: 'Autorizza {provider}',
	AuthorisedProviders: 'Fornitori autorizzati',
	AutoDeclineAllFutureOption: 'Solo nuovi eventi o appuntamenti',
	AutoDeclineAllOption: 'Eventi e appuntamenti nuovi ed esistenti',
	AutoDeclinePrimaryText: 'Rifiuta automaticamente gli eventi',
	AutoDeclineSecondaryText: `Gli eventi durante il tuo periodo di assenza dall'ufficio saranno automaticamente rifiutati.`,
	AutogenerateBillings: 'Genera automaticamente i documenti di fatturazione',
	AutogenerateBillingsDescription: `I documenti di fatturazione automatizzati saranno generati l'ultimo giorno del mese. Le fatture e le ricevute superbill possono essere create manualmente in qualsiasi momento.`,
	AutomateWorkflows: 'Automatizzare i flussi di lavoro',
	AutomaticallySendSuperbill: 'Invia automaticamente le ricevute Superbill',
	AutomaticallySendSuperbillHelperText:
		'Una superfattura è una ricevuta dettagliata dei servizi forniti a un cliente per il rimborso assicurativo',
	Automation: 'Automazione',
	AutomationActionSendEmailLabel: 'Invia email',
	AutomationActionSendSMSLabel: 'Invia SMS',
	AutomationAndReminders: 'Automazione ',
	AutomationDeletedSuccessMessage: 'Automazione eliminata con successo',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Evento temporale',
	AutomationParams_timeUnit: 'Unità',
	AutomationParams_timeValue: 'Numero',
	AutomationPublishSuccessMessage: 'Automazione pubblicata con successo',
	AutomationPublishWarningTooltip: `Si prega di ricontrollare la configurazione dell'automazione e assicurarsi che sia stata configurata correttamente`,
	AutomationTriggerEventCancelledDescription: 'Si attiva quando un evento viene annullato o eliminato',
	AutomationTriggerEventCancelledLabel: 'Evento annullato',
	AutomationTriggerEventCreatedDescription: 'Si attiva quando viene creato un evento',
	AutomationTriggerEventCreatedLabel: 'Nuovo evento',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Si attiva quando un evento viene creato o aggiornato (tranne quando viene annullato)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Evento nuovo o aggiornato',
	AutomationTriggerEventEndedDescription: 'Si attiva quando termina un evento',
	AutomationTriggerEventEndedLabel: 'Evento terminato',
	AutomationTriggerEventStartsDescription: `Si attiva quando trascorre un periodo di tempo specificato prima dell'inizio di un evento`,
	AutomationTriggerEventStartsLabel: `L'evento inizia`,
	Automations: 'Automazioni',
	Availability: 'Disponibilità',
	AvailabilityDisableSchedule: 'Disabilita la pianificazione',
	AvailabilityDisabled: 'Disabilitato',
	AvailabilityEnableSchedule: 'Abilita la pianificazione',
	AvailabilityEnabled: 'Abilitato',
	AvailabilityNoActiveBanner:
		'Hai disattivato tutti i tuoi programmi. I clienti non possono prenotarti online e tutti gli appuntamenti futuri devono essere confermati manualmente.',
	AvailabilityNoActiveConfirmationDescription:
		'Disabilitando questa disponibilità non avrai più orari attivi. I clienti non potranno prenotarti online e tutte le prenotazioni effettuate dai professionisti saranno al di fuori del tuo orario di lavoro.',
	AvailabilityNoActiveConfirmationProceed: 'Sì, procedi',
	AvailabilityNoActiveConfirmationTitle: 'Nessun programma attivo',
	AvailabilityToggle: 'Programmazione abilitata',
	AvailabilityUnsetDate: 'Nessuna data fissata',
	AvailableLocations: 'Posizioni disponibili',
	AvailablePayers: 'Pagatori disponibili',
	AvailablePayersEmptyState: 'Nessun pagatore selezionato',
	AvailableTimes: 'Orari disponibili',
	Back: 'Indietro',
	BackHome: 'Tornato a casa',
	BackToAppointment: `Torna all'appuntamento`,
	BackToLogin: 'Torna al login',
	BackToMapColumns: 'Torna alle colonne della mappa',
	BackToTemplates: 'Torna ai modelli',
	BackToUploadFile: 'Torna al Caricamento file',
	Banker: 'Banchiere',
	BasicBlocks: 'Blocchi base',
	BeforeAppointment: `Invia promemoria {deliveryType} {interval} {unit} prima dell'appuntamento`,
	BehavioralAnalyst: 'Analista comportamentale',
	BehavioralHealthTherapy: 'Terapia comportamentale della salute',
	Beta: 'Beta',
	BillTo: 'disegno di legge per',
	BillableItems: 'Articoli fatturabili',
	BillableItemsEmptyState: 'Non sono stati trovati articoli fatturabili',
	Biller: 'Emittente della fattura',
	Billing: 'Fatturazione',
	BillingAddress: 'Indirizzo di fatturazione',
	BillingAndReceiptsUnauthorisedMessage: `Per accedere a queste informazioni è necessario l'accesso alla visualizzazione delle fatture e dei pagamenti.`,
	BillingBillablesTab: 'Fatturabili',
	BillingClaimsTab: 'Affermazioni',
	BillingDetails: 'Dettagli di fatturazione',
	BillingDocuments: 'Documenti di fatturazione',
	BillingDocumentsClaimsTab: 'Affermazioni',
	BillingDocumentsEmptyState: 'Nessun {tabType} trovato',
	BillingDocumentsInvoicesTab: 'Fatture',
	BillingDocumentsSuperbillsTab: 'Superfatture',
	BillingInformation: 'Informazioni di fatturazione',
	BillingInvoicesTab: 'Fatture',
	BillingItems: 'Articoli di fatturazione',
	BillingPaymentsTab: 'Pagamenti',
	BillingPeriod: 'Periodo di fatturazione',
	BillingProfile: 'Profilo di fatturazione',
	BillingProfileOverridesDescription: `Limitare l'utilizzo di questo profilo di fatturazione a specifici membri del team`,
	BillingProfileOverridesHeader: `Limita l'accesso`,
	BillingProfileProviderType: 'Tipo di fornitore',
	BillingProfileTypeIndividual: 'Praticante',
	BillingProfileTypeIndividualSubLabel: 'Tipo 1 NPI',
	BillingProfileTypeOrganisation: 'Organizzazione',
	BillingProfileTypeOrganisationSubLabel: 'Tipo 2 NPI',
	BillingProfiles: 'Profili di fatturazione',
	BillingProfilesEditHeader: 'Modifica il profilo di fatturazione di {name}',
	BillingProfilesNewHeader: 'Nuovo profilo di fatturazione',
	BillingProfilesSectionDescription:
		'Gestisci le informazioni di fatturazione per i professionisti e gli assicuratori impostando profili di fatturazione che possono essere applicati alle fatture e ai pagamenti assicurativi.',
	BillingSearchPlaceholder: 'Cerca elementi',
	BillingSettings: 'Impostazioni di fatturazione',
	BillingSuperbillsTab: 'Superfatture',
	BiomedicalEngineer: 'Ingegnere biomedico',
	BlankInvoice: 'Fattura in bianco',
	BlueShieldProviderNumber: 'Numero del fornitore Blue Shield',
	Body: 'Corpo',
	Bold: 'Grassetto',
	BookAgain: 'Prenota di nuovo',
	BookAppointment: 'Prenota un appuntamento',
	BookableOnline: 'Prenotabile online',
	BookableOnlineHelper: 'I clienti possono prenotare questo servizio online',
	BookedOnline: 'Prenotazione online',
	Booking: 'Prenotazione',
	BookingAnalyticsIntegrationPanelDescription: `Imposta Google Tag Manager per tracciare le azioni e le conversioni chiave nel tuo flusso di prenotazione online. Raccogli dati preziosi sulle interazioni degli utenti per migliorare gli sforzi di marketing e ottimizzare l'esperienza di prenotazione.`,
	BookingAnalyticsIntegrationPanelTitle: 'Integrazione analitica',
	BookingAndCancellationPolicies: 'Prenotazione ',
	BookingButtonEmbed: 'Pulsante',
	BookingButtonEmbedDescription: 'Aggiunge un pulsante di prenotazione online al tuo sito web',
	BookingDirectTextLink: 'Collegamento diretto al testo',
	BookingDirectTextLinkDescription: 'Apre la pagina di prenotazione online',
	BookingFormatLink: 'Collegamento formato',
	BookingFormatLinkButtonTitle: 'Titolo del pulsante',
	BookingInlineEmbed: 'Incorporamento in linea',
	BookingInlineEmbedDescription: 'Carica la pagina di prenotazione online direttamente sul tuo sito web',
	BookingLink: 'Link di prenotazione',
	BookingLinkModalCopyText: 'Copia',
	BookingLinkModalDescription:
		'Consenti ai clienti con questo link di prenotare qualsiasi membro del team o servizio',
	BookingLinkModalHelpText: 'Scopri come impostare le prenotazioni online',
	BookingLinkModalTitle: 'Condividi il tuo link di prenotazione',
	BookingPolicies: 'Politiche di prenotazione',
	BookingPoliciesDescription: 'Imposta quando i clienti possono effettuare prenotazioni online',
	BookingTimeUnitDays: 'giorni',
	BookingTimeUnitHours: 'ore',
	BookingTimeUnitMinutes: 'minuti',
	BookingTimeUnitMonths: 'mesi',
	BookingTimeUnitWeeks: 'settimane',
	BottomNavBilling: 'Fatturazione',
	BottomNavGettingStarted: 'Casa',
	BottomNavMore: 'Di più',
	BottomNavNotes: 'Appunti',
	Brands: 'Marchi',
	Brother: 'Fratello',
	BrotherInLaw: 'Cognato',
	BrowseOrDragFileHere: '<link>Sfoglia</link> o trascina il file qui',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} minuti dopo',
	BufferAndLabel: 'E',
	BufferAppointmentLabel: 'un appuntamento',
	BufferBeforeTime: '{time} minuti prima',
	BufferTime: 'Tempo di buffer',
	BufferTimeViewLabel: '{bufferBefore} minuti prima e {bufferAfter} minuti dopo gli appuntamenti',
	BulkArchiveClientsDescription: 'Vuoi davvero archiviare questi client? Puoi riattivarli più tardi.',
	BulkArchiveSuccess: 'Clienti archiviati con successo',
	BulkArchiveUndone: 'Archiviazione in blocco annullata',
	BulkPermanentDeleteDescription:
		'Questo eliminerà **{count} conversazioni**. Questa azione non può essere annullata.',
	BulkPermanentDeleteTitle: 'Elimina le conversazioni per sempre',
	BulkUnarchiveSuccess: 'Client de-archiviati con successo',
	BulletedList: 'Elenco puntato',
	BusinessAddress: 'Indirizzo commerciale',
	BusinessAddressOptional: 'Indirizzo aziendale <span>(facoltativo)</span>',
	BusinessName: 'Nome commerciale',
	Button: 'Bottone',
	By: 'Di',
	CHAMPUSIdentificationNumber: 'Numero identificativo CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'È richiesto il CVC',
	Calendar: 'Calendario',
	CalendarAppSyncFormDescription: 'Sincronizza gli eventi Carepatron con',
	CalendarAppSyncPanelTitle: 'Sincronizzazione delle app connesse',
	CalendarDescription: 'Gestisci i tuoi appuntamenti o imposta attività e promemoria personali',
	CalendarDetails: 'Dettagli del calendario',
	CalendarDetailsDescription: 'Gestisci le impostazioni di visualizzazione del calendario e degli appuntamenti.',
	CalendarScheduleNew: 'Pianifica nuovo',
	CalendarSettings: 'Impostazioni del calendario',
	Call: 'Chiamata',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> si è unito/a alla videochiamata',
	CallChangeLayoutTextContent: 'La selezione viene salvata per le riunioni future',
	CallIdlePrompt: 'Preferisci aspettare per partecipare o riprovare più tardi?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Barra laterale',
	CallLayoutOptionSpotlight: 'Riflettore',
	CallLayoutOptionTiled: 'Piastrellato',
	CallNoAttendees: 'Nessun partecipante alla riunione.',
	CallSessionExpiredError: 'Sessione scaduta. La chiamata è stata terminata. Prova a partecipare di nuovo.',
	CallWithPractitioner: 'Chiamata con {practitioner}',
	CallsListCreateButton: 'Nuova chiamata',
	CallsListEmptyState: 'Nessuna chiamata attiva',
	CallsListItemEndCall: 'Termina chiamata',
	CamWarningMessage: 'È stato rilevato un problema con la fotocamera',
	Camera: 'Telecamera',
	CameraAndMicIssueModalDescription: `Abilita l'accesso di Carepatron alla tua telecamera e al tuo microfono.
 Per maggiori informazioni <a>segui questa guida</a>`,
	CameraAndMicIssueModalTitle: 'La telecamera e il microfono sono bloccati',
	CameraQuality: 'Qualità della fotocamera',
	CameraSource: 'Fonte della fotocamera',
	CanModifyReadOnlyEvent: 'Non puoi modificare questo evento',
	Canada: 'Canada',
	Cancel: 'Cancellare',
	CancelClientImportDescription: 'Sei sicuro di voler annullare questa importazione?',
	CancelClientImportPrimaryAction: `Sì, annulla l'importazione`,
	CancelClientImportSecondaryAction: 'Continua a modificare',
	CancelClientImportTitle: `Annulla l'importazione dei clienti`,
	CancelImportButton: 'Annulla importazione',
	CancelPlan: 'Annulla piano',
	CancelPlanConfirmation: `Annullando il piano, tutti i saldi in sospeso relativi a questo mese verranno automaticamente addebitati sul tuo conto.
 Se desideri declassare gli utenti fatturati, puoi semplicemente rimuovere i membri del team e Carepatron aggiornerà automaticamente il prezzo dell'abbonamento.`,
	CancelSend: 'Annulla invio',
	CancelSubscription: 'Annulla abbonamento',
	Canceled: 'Cancellato',
	CancellationPolicy: 'Politica di cancellazione',
	Cancelled: 'Annullato',
	CannotContainSpecialCharactersError: 'Impossibile contenere {specialCharacters}',
	CannotDeleteInvoice: 'Le fatture pagate tramite pagamenti online non possono essere eliminate',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> non può essere spostato fuori dal gruppo <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Il servizio non può essere spostato al di fuori delle raccolte',
	CapeTown: 'Città del Capo',
	Caption: 'Didascalia',
	CaptureNameFieldLabel: 'Il nome con cui vorresti che gli altri si riferissero a te',
	CapturePaymentMethod: 'Cattura metodo di pagamento',
	CapturingAudio: 'Cattura audio',
	CapturingSignature: 'Cattura della firma...',
	CardInformation: 'Informazioni sulla carta',
	CardNumberRequired: 'Il numero della carta è obbligatorio',
	CardiacRehabilitationSpecialist: 'Specialista in riabilitazione cardiaca',
	Cardiologist: 'Cardiologo',
	CareAiNoConversations: 'Nessuna conversazione ancora',
	CareAiNoConversationsDescription: 'Inizia una conversazione con {aiName} per iniziare',
	CareAssistant: 'Assistente sanitario',
	CareManager: `Responsabile dell'assistenza`,
	Caregiver: 'Caregiver',
	CaregiverCreateModalDescription:
		'Aggiungere personale come amministratori consentirà loro di creare e gestire storie di assistenza. Offre inoltre loro pieno accesso per creare e gestire clienti.',
	CaregiverCreateModalTitle: 'Nuovo membro del team',
	CaregiverListCantAddStaffInfoTitle:
		'Hai raggiunto il numero massimo di personale per il tuo abbonamento. Aggiorna il tuo piano per aggiungere più membri del personale.',
	CaregiverListCreateButton: 'Nuovo membro del team',
	CaregiverListEmptyState: 'Nessun assistente aggiunto',
	CaregiversListItemRemoveStaff: 'Rimuovi personale',
	CarepatronApp: 'Applicazione Carepatron',
	CarepatronCommunity: 'Comunità',
	CarepatronFieldAddress: 'Indirizzo',
	CarepatronFieldAssignedStaff: 'Personale assegnato',
	CarepatronFieldBirthDate: 'Data di nascita',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: 'Stato occupazionale',
	CarepatronFieldEthnicity: 'Etnia',
	CarepatronFieldFirstName: 'Nome di battesimo',
	CarepatronFieldGender: 'Genere',
	CarepatronFieldIdentificationNumber: 'Numero di identificazione',
	CarepatronFieldIsArchived: 'Stato',
	CarepatronFieldLabel: 'Etichetta',
	CarepatronFieldLastName: 'Cognome',
	CarepatronFieldLivingArrangements: 'Disposizione abitativa',
	CarepatronFieldMiddleNames: 'Secondo nome',
	CarepatronFieldOccupation: 'Occupazione',
	CarepatronFieldPhoneNumber: 'Numero di telefono',
	CarepatronFieldRelationshipStatus: 'Stato della relazione',
	CarepatronFieldStatus: 'Stato',
	CarepatronFieldStatusHelperText: 'Massimo 10 stati.',
	CarepatronFieldTags: 'Etichette',
	CarepatronFields: 'Campi Carepatron',
	Cash: 'Contanti',
	Category: 'Categoria',
	CategoryInputPlaceholder: 'Scegli una categoria di modello',
	CenterAlign: 'Allinea al centro',
	Central: 'Centrale',
	ChangeLayout: 'Cambia layout',
	ChangeLogo: 'Modifica',
	ChangePassword: 'Cambiare la password',
	ChangePasswordFailureSnackbar:
		'Spiacenti, la tua password non è stata modificata. Controlla che la tua vecchia password sia corretta.',
	ChangePasswordHelperInfo: 'Lunghezza minima di {minLength}',
	ChangePasswordSuccessfulSnackbar: `Password modificata con successo! La prossima volta che effettui l'accesso, assicurati di utilizzare quella password.`,
	ChangeSubscription: `Modifica l'abbonamento`,
	ChangesNotAllowed: 'Non è possibile apportare modifiche a questo campo',
	ChargesDisabled: 'Spese disabilitate',
	ChargesEnabled: 'Addebiti abilitati',
	ChargesStatus: 'Stato delle spese',
	ChartAndDiagram: 'Grafico/Diagramma',
	ChartsAndDiagramsCategoryDescription: 'Per illustrare i dati e i progressi dei clienti',
	ChatEditMessage: 'Modifica messaggio',
	ChatReplyTo: 'Rispondi a {name}',
	ChatTypeMessageTo: 'Messaggio {name}',
	Check: 'Controllo',
	CheckList: 'Lista di controllo',
	Chef: 'Cuoco',
	Chiropractic: 'Chiropratica',
	Chiropractor: 'Chiropratico',
	Chiropractors: 'Chiropratici',
	ChooseACollection: 'Scegli una collezione',
	ChooseAContact: 'Scegli un contatto',
	ChooseAccountTypeHeader: 'Quale ti descrive meglio?',
	ChooseAction: 'Scegli azione',
	ChooseAnAccount: 'Scegli un account',
	ChooseAnOption: `Scegli un'opzione`,
	ChooseBillingProfile: 'Scegli il profilo di fatturazione',
	ChooseClaim: 'Scegli il reclamo',
	ChooseCollection: 'Scegli la collezione',
	ChooseColor: 'Scegli il colore',
	ChooseCustomDate: 'Scegli una data personalizzata',
	ChooseDateAndTime: 'Scegli data e ora',
	ChooseDxCodes: 'Scegli i codici di diagnosi',
	ChooseEventType: 'Scegli il tipo di evento',
	ChooseFileButton: 'Scegli un file',
	ChooseFolder: 'Scegli cartella',
	ChooseInbox: 'Scegli la posta in arrivo',
	ChooseMethod: 'Scegli il metodo',
	ChooseNewOwner: 'Scegli il nuovo proprietario',
	ChooseOrganization: 'Scegli organizzazione',
	ChoosePassword: 'Scegli la password',
	ChoosePayer: 'Scegli il pagatore',
	ChoosePaymentMethod: 'Scegli un metodo di pagamento',
	ChoosePhysicalOrRemoteLocations: 'Inserisci o scegli la posizione',
	ChoosePlan: 'Scegli {plan}',
	ChooseProfessional: 'Scegli Professionale',
	ChooseServices: 'Scegli i servizi',
	ChooseSource: 'Scegli origine',
	ChooseSourceDescription: `Scegli da dove stai importando i clienti – che si tratti di un file o di un'altra piattaforma software.`,
	ChooseTags: 'Scegli i tag',
	ChooseTaxName: 'Scegli il nome della tassa',
	ChooseTeamMembers: 'Scegli i membri del team',
	ChooseTheme: 'Scegli tema',
	ChooseTrigger: 'Scegli il trigger',
	ChooseYourProvider: 'Scegli il tuo fornitore',
	CircularProgressWithLabel: '{value}%',
	City: 'Città',
	CivilEngineer: 'Ingegnere civile',
	Claim: 'Reclamo',
	ClaimAddReferringProvider: 'Aggiungi medico curante',
	ClaimAddRenderingProvider: 'Aggiungi fornitore di rendering',
	ClaimAmount: 'Importo del reclamo',
	ClaimAmountPaidHelpContent: `L'importo pagato è il pagamento ricevuto dal paziente o da altri pagatori. Inserisci l'importo totale pagato dal paziente e/o da altri pagatori solo per i servizi coperti.`,
	ClaimAmountPaidHelpSubtitle: 'Campo 29',
	ClaimAmountPaidHelpTitle: 'Importo pagato',
	ClaimBillingProfileTypeIndividual: 'Individuale',
	ClaimBillingProfileTypeOrganisation: 'Organizzazione',
	ClaimChooseRenderingProviderOrTeamMember: 'Scegli il fornitore di rendering o un membro del team',
	ClaimClientInsurancePolicies: 'Polizze assicurative per i clienti',
	ClaimCreatedAction: '<mark>Richiesta {claimNumber}</mark> creata',
	ClaimDeniedAction: '<mark>Richiesta {claimNumber}</mark> è stata rifiutata da <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Cerca i codici di diagnosi ICD 10',
	ClaimDiagnosisSelectorHelpContent: `La “Diagnosi o lesione” è il segno, il sintomo, il reclamo o la condizione del paziente in relazione ai servizi indicati nella richiesta di risarcimento.
 È possibile selezionare fino a 12 codici di diagnosi ICD 10.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Campo 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnosi o lesione',
	ClaimDiagnosticCodesEmptyError: 'È richiesto almeno un codice di diagnosi',
	ClaimDoIncludeReferrerInformation: 'Includere le informazioni del referrer su CMS1500',
	ClaimERAReceivedAction: 'Ricevuta di rimessa elettronica da <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Bonifico bancario elettronico ricevuto	<mark>Pagamento {paymentReference}</mark> per <b>{paymentAmount}</b> da <b>{payerNumber} {payerName}</b> è stato registrato',
	ClaimExportedAction: '<mark>Richiesta {claimNumber}</mark> è stata esportata come <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nome del cliente o del contatto',
	ClaimFieldClientAddress: 'Indirizzo del cliente',
	ClaimFieldClientAddressDescription: `Inserisci l'indirizzo del cliente. La prima riga è per l'indirizzo stradale. Non usare punteggiatura (virgole o punti) o simboli nell'indirizzo. Se segnali un indirizzo estero, contatta il pagatore per istruzioni specifiche sulla segnalazione.`,
	ClaimFieldClientAddressSubtitle: 'Campo 5',
	ClaimFieldClientDateOfBirth: 'Data di nascita del cliente',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Campo 3',
	ClaimFieldClientDateOfBirthDescription: `Inserisci la data di nascita di 8 cifre del cliente (MM/GG/AAAA). La data di nascita del cliente è un'informazione che identificherà il cliente e distingue le persone con nomi simili.`,
	ClaimFieldClientDescription:
		'Il "nome del cliente" è il nome della persona che ha ricevuto il trattamento o le forniture.',
	ClaimFieldClientSexDescription: `Il "sesso" è un'informazione che identifica il cliente e distingue le persone con nomi simili.`,
	ClaimFieldClientSubtitle: 'Campo 2',
	ClaimFiling: 'Presentazione della richiesta',
	ClaimHistorySubtitle: 'Assicurazione • Richiesta {number}',
	ClaimIncidentAutoAccident: 'Incidente stradale?',
	ClaimIncidentConditionRelatedTo: 'La condizione del cliente è correlata a',
	ClaimIncidentConditionRelatedToHelpContent: `Questa informazione indica se la malattia o l'infortunio del cliente è correlato all'occupazione, all'incidente automobilistico o ad altri incidenti. L'occupazione (attuale o precedente) indicherebbe che la condizione è correlata al lavoro o al luogo di lavoro del cliente. L'incidente automobilistico indicherebbe che la condizione è il risultato di un incidente automobilistico. L'altro incidente indicherebbe che la condizione è il risultato di qualsiasi altro tipo di incidente.`,
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Campi 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'La condizione del cliente è correlata a',
	ClaimIncidentCurrentIllness: 'Malattia, infortunio o gravidanza in corso',
	ClaimIncidentCurrentIllnessHelpContent: `La data della malattia, dell'infortunio o della gravidanza attuale identifica la prima data di insorgenza della malattia, la data effettiva dell'infortunio o l'LMP per la gravidanza.`,
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Campo 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Date di malattia, infortunio o gravidanza attuale (LMP)',
	ClaimIncidentDate: 'Data',
	ClaimIncidentDateFrom: 'Data da',
	ClaimIncidentDateTo: 'Data a',
	ClaimIncidentEmploymentRelated: 'Occupazione',
	ClaimIncidentEmploymentRelatedDesc: '(Attuale o precedente)',
	ClaimIncidentHospitalizationDatesLabel: 'Date di ricovero relative alle prestazioni in corso',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Le date di ricovero ospedaliero relative ai servizi in corso si riferiscono al soggiorno del cliente e indicano le date di ricovero e di dimissione associate ai servizi indicati nella richiesta di rimborso.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Campo 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Date di ricovero relative alle prestazioni in corso',
	ClaimIncidentInformation: `Informazioni sull'incidente`,
	ClaimIncidentOtherAccident: 'Altro incidente?',
	ClaimIncidentOtherAssociatedDate: 'Altra data associata',
	ClaimIncidentOtherAssociatedDateHelpContent: `L'altra data identifica ulteriori informazioni sulla data relativa alle condizioni o al trattamento del cliente.`,
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Campo 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Altra data',
	ClaimIncidentQualifier: 'Qualificatore',
	ClaimIncidentQualifierPlaceholder: 'Scegli il qualificatore',
	ClaimIncidentUnableToWorkDatesLabel: `Il cliente non era in grado di lavorare nell'occupazione attuale`,
	ClaimIncidentUnableToWorkDatesLabelHelpContent: `Le date in cui il cliente non è stato in grado di lavorare nell'occupazione attuale sono il periodo di tempo in cui il cliente non è stato o non è stato in grado di lavorare`,
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Campo 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: `Date in cui il cliente non è stato in grado di lavorare nell'occupazione attuale`,
	ClaimIncludeReferrerInformation: 'Includere le informazioni del referrer su CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Il tipo di copertura assicurativa sanitaria applicabile a questa richiesta. Altro indica l'assicurazione sanitaria, inclusi HMO, assicurazione commerciale, incidente automobilistico, responsabilità civile o indennità per infortuni sul lavoro.
 Queste informazioni indirizzano la richiesta al programma corretto e possono stabilire la responsabilità primaria.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Campo 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Tipo di copertura',
	ClaimInsuranceGroupIdHelpContent: `Inserire il numero di polizza o di gruppo dell'assicurato così come appare sulla tessera sanitaria identificativa dell'assicurato.

 Il "Numero di polizza, gruppo o FECA dell'assicurato" è l'identificativo alfanumerico per la copertura sanitaria, auto o altro piano assicurativo. Il numero FECA è l'identificativo alfanumerico di 9 caratteri assegnato a un paziente che dichiara una condizione correlata al lavoro.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Campo 11',
	ClaimInsuranceGroupIdHelpTitle: `Numero di polizza, gruppo o FECA dell'assicurato`,
	ClaimInsuranceMemberIdHelpContent: `Inserire il numero identificativo dell'assicurato riportato sulla tessera assicurativa del pagatore a cui si invia la richiesta di risarcimento.
 Se al paziente è stato assegnato un numero identificativo univoco dal pagatore, immettere tale numero in questo campo.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Campo 1a',
	ClaimInsuranceMemberIdHelpTitle: 'ID membro assicurato',
	ClaimInsurancePayer: 'Pagatore assicurativo',
	ClaimManualPaymentAction: '<mark>Pagamento {paymentReference}</mark> per <b>{paymentAmount}</b> registrato',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Ulteriori informazioni sul reclamo',
	ClaimMiscAdditionalClaimInformationHelpContent: `Si prega di fare riferimento alle istruzioni attuali del pagatore pubblico o privato in merito all'utilizzo di questo campo. Riportare il qualificatore appropriato, quando disponibile, per le informazioni inserite.Non inserire uno spazio, un trattino o altro separatore tra il qualificatore e le informazioni.`,
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Campo 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Informazioni aggiuntive sulla richiesta',
	ClaimMiscClaimCodes: 'Codici di richiesta',
	ClaimMiscOriginalReferenceNumber: 'Numero di riferimento originale',
	ClaimMiscPatientsAccountNumber: 'Numero di conto del paziente',
	ClaimMiscPatientsAccountNumberHelpContent: `Il numero di conto del paziente è l'identificativo assegnato dal fornitore.`,
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Campo 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Numero di conto del paziente',
	ClaimMiscPriorAuthorizationNumber: 'Numero di autorizzazione preventiva',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Il numero di autorizzazione precedente è il numero assegnato al pagatore che autorizza il/i servizio/i.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Campo 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Numero di autorizzazione preventiva',
	ClaimMiscResubmissionCode: 'Codice di reinvio',
	ClaimMiscResubmissionCodeHelpContent:
		'Per nuova presentazione si intende il codice e il numero di riferimento originale assegnati dal pagatore o dal destinatario di destinazione per indicare un reclamo o un incontro precedentemente presentato.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Campo 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Nuova presentazione e/o numero di riferimento originale',
	ClaimNumber: 'Numero del reclamo',
	ClaimNumberFormat: 'Sinistro #{number}',
	ClaimOrderingProvider: 'Fornitore di ordinazione',
	ClaimOtherId: `Altro documento d'identità`,
	ClaimOtherIdPlaceholder: `Scegli un'opzione`,
	ClaimOtherIdQualifier: 'Altro qualificatore ID',
	ClaimOtherIdQualifierPlaceholder: 'Scegli il qualificatore ID',
	ClaimPlaceOfService: 'Luogo di servizio',
	ClaimPlaceOfServicePlaceholder: 'Aggiungi POS',
	ClaimPolicyHolderRelationship: 'Rapporto del titolare della polizza',
	ClaimPolicyInformation: 'Informazioni sulla politica',
	ClaimPolicyTelephone: 'Telefono (includere il prefisso)',
	ClaimReceivedAction: '<mark>Richiesta {claimNumber}</mark> ricevuta da <b>{name}</b>',
	ClaimReferringProvider: 'Fornitore di riferimento',
	ClaimReferringProviderEmpty: 'Nessun provider di riferimento aggiunto',
	ClaimReferringProviderHelpContent:
		'Il nome inserito è quello del fornitore di riferimento, del fornitore ordinante o del fornitore supervisore che ha indirizzato, ordinato o supervisionato il/i servizio/i o la/le fornitura/i nel reclamo. Il qualificatore indica il ruolo del fornitore segnalato.',
	ClaimReferringProviderHelpSubtitle: 'Campo 17',
	ClaimReferringProviderHelpTitle: 'Nome del fornitore o della fonte di riferimento',
	ClaimReferringProviderQualifier: 'Qualificatore',
	ClaimReferringProviderQualifierPlaceholder: 'Scegli il qualificatore',
	ClaimRejectedAction: '<mark>Richiesta {claimNumber}</mark> è stata rifiutata da <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Numero di identificazione',
	ClaimRenderingProviderOrTeamMember: 'Fornitore di rendering o membro del team',
	ClaimRestoredAction: '<mark>Richiesta {claimNumber}</mark> è stata ripristinata',
	ClaimServiceFacility: 'Struttura di servizio',
	ClaimServiceFacilityLocationHelpContent: `Il nome e l'indirizzo della struttura in cui sono stati forniti i servizi identificano il sito in cui sono stati forniti i servizi.`,
	ClaimServiceFacilityLocationHelpLabel: '32, 32a e 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Campo 32, 32a e 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Struttura di servizio',
	ClaimServiceFacilityPlaceholder: 'Scegli la struttura o la posizione del servizio',
	ClaimServiceLabChargesHelpContent: `Compilare questo campo quando si richiede un rimborso per servizi acquistati e forniti da un'entità diversa dal fornitore della fatturazione.
 Ogni servizio acquistato deve essere segnalato in una richiesta di rimborso separata, poiché nel modulo CMS1500 è possibile inserire un solo addebito.`,
	ClaimServiceLabChargesHelpSubtitle: 'Campo 20',
	ClaimServiceLabChargesHelpTitle: 'Spese di laboratorio esterne',
	ClaimServiceLineServiceHelpContent:
		'"Procedure, servizi o forniture" identificano i servizi e le procedure mediche fornite al paziente.',
	ClaimServiceLineServiceHelpSubtitle: 'Campo 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedure, servizi o forniture',
	ClaimServiceLinesEmptyError: 'Almeno una linea di servizio è obbligatoria',
	ClaimServiceSupplementaryInfoHelpContent: `Aggiungere una descrizione narrativa aggiuntiva dei servizi forniti utilizzando i qualificatori applicabili.
 Non inserire spazi, trattini o altri separatori tra il qualificatore e le informazioni.

 Per istruzioni complete sull'aggiunta di informazioni supplementari, consultare le istruzioni del modulo di richiesta CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Campo 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Informazioni supplementari',
	ClaimSettingsBillingMethodTitle: 'Metodo di fatturazione del cliente',
	ClaimSettingsClientSignatureDescription: `Acconsento al rilascio di informazioni mediche o di altro tipo necessarie per l'elaborazione delle richieste di risarcimento assicurativo.`,
	ClaimSettingsClientSignatureTitle: 'Firma del cliente in archivio',
	ClaimSettingsConsentLabel: 'Consenso necessario per elaborare le richieste di risarcimento assicurativo:',
	ClaimSettingsDescription: `Scegli il metodo di fatturazione del cliente per garantire un'elaborazione fluida del pagamento:`,
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} ha una polizza assicurativa attiva. Per abilitare la fatturazione assicurativa, aggiorna il metodo di fatturazione del cliente a Assicurazione.',
	ClaimSettingsInsuranceDescription: `Costi rimborsati dall'assicurazione`,
	ClaimSettingsInsuranceTitle: 'Assicurazione',
	ClaimSettingsNoPoliciesAlertDescription:
		'Aggiungere una polizza assicurativa per abilitare le richieste di risarcimento.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Acconsento a ricevere i pagamenti assicurativi per i servizi forniti.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Firma del titolare della polizza in archivio',
	ClaimSettingsSelfPayDescription: 'Il cliente pagherà gli appuntamenti',
	ClaimSettingsSelfPayTitle: 'Pagamento autonomo',
	ClaimSettingsTitle: 'Impostazioni di richiesta',
	ClaimSexSelectorPlaceholder: 'Maschio / Femmina',
	ClaimStatusChangedAction: '<mark>Richiesta {claimNumber}</mark> stato aggiornato',
	ClaimSubmittedAction:
		'<mark>Richiesta {claimNumber}</mark> inviata a <b>{payerClearingHouse}</b> per <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Richiesta n. #{claimNumber}',
	ClaimSupervisingProvider: 'Fornitore supervisore',
	ClaimSupplementaryInfo: 'Informazioni supplementari',
	ClaimSupplementaryInfoPlaceholder: 'Aggiungi informazioni supplementari',
	ClaimTrashedAction: '<mark>Richiesta {claimNumber}</mark> è stata eliminata',
	ClaimValidationFailure: 'Validazione del reclamo non riuscita',
	ClaimsEmptyStateDescription: 'Non sono stati trovati reclami.',
	ClainInsuranceTelephone: 'Assicurazione Telefono (includere prefisso)',
	Classic: 'Classico',
	Clear: 'Chiaro',
	ClearAll: 'Cancella tutto',
	ClearSearchFilter: 'Chiaro',
	ClearingHouse: 'Sgombero',
	ClearingHouseClaimId: 'ID Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Richiesta di riferimento per la pulizia',
	ClearingHouseUnavailableError: 'La clearing house è attualmente non disponibile. Si prega di riprovare più tardi.',
	ClickToUpload: 'Clicca per caricare',
	Client: 'Cliente',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} ha aggiunto {noteTitle, select, undefined { una nota } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Scegli i clienti e le loro relazioni',
	ClientAndRelationshipSelectorTitle: 'Tutti i clienti e le loro relazioni',
	ClientAndRelationshipSelectorTitle1: `Tutte le relazioni di '{name}'`,
	ClientAppCallsPageNoOptionsText: `Se stai aspettando una videochiamata, questa apparirà qui a breve. Se riscontri problemi, contatta la persona che l'ha avviata.`,
	ClientAppSubHeaderMyDocumentation: 'La mia documentazione',
	ClientAppointment: 'Appuntamento con il cliente',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} ha prenotato {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Non sono stati trovati appuntamenti',
	ClientAppointmentsEmptyStateTitle:
		'Tieni traccia degli appuntamenti futuri e storici dei tuoi clienti e della loro presenza',
	ClientArchivedSuccessfulSnackbar: 'Archivio riuscito <b>{name}</b>',
	ClientBalance: 'Saldo del cliente',
	ClientBilling: 'Fatturazione',
	ClientBillingAddPaymentMethodDescription:
		'Aggiungi e gestisci i metodi di pagamento dei tuoi clienti per semplificare il processo di fatturazione.',
	ClientBillingAndPaymentDueDate: 'Scadenza',
	ClientBillingAndPaymentHistory: 'Cronologia di fatturazione e pagamento',
	ClientBillingAndPaymentInvoices: 'Fatture',
	ClientBillingAndPaymentIssueDate: 'Data di emissione',
	ClientBillingAndPaymentPrice: 'Prezzo',
	ClientBillingAndPaymentReceipt: 'Ricevuta',
	ClientBillingAndPaymentServices: 'Servizi',
	ClientBillingAndPaymentStatus: 'Stato',
	ClientBulkStaffAssignedSuccessSnackbar: 'Team {count, plural, one {member} other {members}} assegnato!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Squadra con {count, plural, one {membro} other {membri}} non assegnati!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tag aggiunti!',
	ClientDuplicatesDeviewDescription:
		'Unisci più record cliente in uno per unificare tutti i dati: note, documenti, appuntamenti, fatture e conversazioni.',
	ClientDuplicatesPageMergeHeader: 'Seleziona i dati che desideri conservare',
	ClientDuplicatesReviewHeader: `Confronta i potenziali record duplicati per l'unione`,
	ClientEmailChangeWarningDescription: `L'aggiornamento dell'email del cliente rimuoverà il suo accesso a qualsiasi documentazione condivisa e consentirà l'accesso all'utente con la nuova email`,
	ClientFieldDateDescription: 'Formato data',
	ClientFieldDateLabel: 'Data',
	ClientFieldDateRangeDescription: 'Un intervallo di date',
	ClientFieldDateRangeLabel: 'Intervallo di date',
	ClientFieldDateShowDateDescription: 'ad esempio 29 anni',
	ClientFieldDateShowDateRangeDescription: 'ad esempio 2 settimane',
	ClientFieldEmailDescription: 'Indirizzo e-mail',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Etichetta del campo',
	ClientFieldLinearScaleDescription: 'Opzioni scala 1-10',
	ClientFieldLinearScaleLabel: 'Scala lineare',
	ClientFieldLocationDescription: 'Indirizzo fisico o postale',
	ClientFieldLocationLabel: 'Posizione',
	ClientFieldLongTextDescription: 'Area di testo lunga',
	ClientFieldLongTextLabel: 'Paragrafo',
	ClientFieldMultipleChoiceDropdownDescription: `Scegli più opzioni dall'elenco`,
	ClientFieldMultipleChoiceDropdownLabel: 'Menù a discesa a scelta multipla',
	ClientFieldPhoneNumberDescription: 'Numero di telefono',
	ClientFieldPhoneNumberLabel: 'Telefono',
	ClientFieldPlaceholder: 'Scegli un tipo di campo client',
	ClientFieldSingleChoiceDropdownDescription: `Scegli solo un'opzione dall'elenco`,
	ClientFieldSingleChoiceDropdownLabel: 'Menù a discesa a scelta singola',
	ClientFieldTextDescription: 'Campo di immissione testo',
	ClientFieldTextLabel: 'Testo',
	ClientFieldYesOrNoDescription: 'Scegli tra le opzioni sì o no',
	ClientFieldYesOrNoLabel: 'Sì | No',
	ClientFileFormAccessLevelDescription:
		'Tu e il Team avete sempre accesso ai file che caricate. Potete scegliere di condividere questo file con il cliente e/o con le sue relazioni',
	ClientFileSavedSuccessSnackbar: 'File salvato!',
	ClientFilesPageEmptyStateText: 'Nessun file caricato',
	ClientFilesPageUploadFileButton: 'Carica file',
	ClientHeaderBilling: 'Fatturazione',
	ClientHeaderBillingAndReceipts: 'Fatturazione ',
	ClientHeaderDocumentation: 'Documentazione',
	ClientHeaderDocuments: 'Documenti',
	ClientHeaderFile: 'Documento',
	ClientHeaderHistory: 'Storia medica',
	ClientHeaderInbox: 'Posta in arrivo',
	ClientHeaderNote: 'Nota',
	ClientHeaderOverview: 'Panoramica',
	ClientHeaderProfile: 'Personale',
	ClientHeaderRelationship: 'Relazione',
	ClientHeaderRelationships: 'Relazioni',
	ClientId: 'ID cliente',
	ClientImportProcessingDescription: 'File ancora in elaborazione. Ti avviseremo quando sarà completata.',
	ClientImportReadyForMappingDescription:
		'Abbiamo terminato di pre-elaborare il tuo file. Vuoi mappare le colonne per completare questo import?',
	ClientImportReadyForMappingNotificationSubject: `L'importazione preliminare del cliente è completa. Il file è ora pronto per la mappatura.`,
	ClientInAppMessaging: 'Messaggistica in-app per il cliente',
	ClientInfoAddField: 'Aggiungi un altro campo',
	ClientInfoAddRow: 'Aggiungi riga',
	ClientInfoAlertMessage:
		'Tutte le informazioni inserite in questa sezione verranno inserite nella scheda del cliente.',
	ClientInfoFormPrimaryText: 'Informazioni per il cliente',
	ClientInfoFormSecondaryText: 'Raccogliere i dettagli di contatto',
	ClientInfoPlaceholder: `Nome del cliente, indirizzo e-mail, numero di telefono
 Indirizzo fisico,
 Data di nascita`,
	ClientInformation: 'Informazioni per il cliente',
	ClientInsuranceTabLabel: 'Assicurazione',
	ClientIntakeFormsNotSupported: `I modelli di modulo non sono attualmente supportati tramite l'assunzione dei clienti.
 Creali e condividili come note per i clienti.`,
	ClientIntakeModalDescription: `Un'e-mail di ammissione verrà inviata al tuo cliente chiedendogli di completare il suo profilo, caricare i documenti medici o di referral pertinenti. Gli verrà concesso l'accesso al Portale Clienti.`,
	ClientIntakeModalTitle: `Invia l'assunzione a {name}`,
	ClientIntakeSkipPasswordSuccessSnackbar: 'Riuscito! La tua assunzione è stata salvata.',
	ClientIntakeSuccessSnackbar: `Riuscito! La tua assunzione è stata salvata e ti è stata inviata un'e-mail di conferma.`,
	ClientIsChargedProcessingFee: 'I tuoi clienti pagheranno la commissione di elaborazione',
	ClientListCreateButton: 'Nuovo cliente',
	ClientListEmptyState: 'Nessun cliente aggiunto',
	ClientListPageItemArchive: 'Rimuovi cliente',
	ClientListPageItemRemoveAccess: 'Rimuovi il mio accesso',
	ClientLocalizationPanelDescription: 'La lingua e il fuso orario preferiti dal cliente.',
	ClientLocalizationPanelTitle: 'Lingua e fuso orario',
	ClientManagementAndEHR: 'Gestione del cliente ',
	ClientMergeResultSummaryBanner: `La fusione dei record consolida tutti i dati dei clienti, inclusi note, documenti, appuntamenti, fatture e conversazioni. Verificare l'accuratezza prima di continuare.`,
	ClientMergeResultSummaryTitle: 'Sintesi dei risultati della fusione',
	ClientModalTitle: 'Nuovo cliente',
	ClientMustHaveEmaillAccessErrorText: 'Clienti/Contatti senza email',
	ClientMustHavePortalAccessErrorText: 'I clienti/contatti dovranno registrarsi',
	ClientMustHaveZoomAppConnectedErrorText: 'Collega Zoom tramite Impostazioni > App connesse',
	ClientNameFormat: 'Formato del nome del cliente',
	ClientNotFormAccessLevel: 'Visibile da:',
	ClientNotFormAccessLevelDescription:
		'Tu e il Team avete sempre accesso alle note che pubblichi. Puoi scegliere di condividere questa nota con il cliente e/o con le sue relazioni',
	ClientNotRegistered: 'Non registrato',
	ClientNoteFormAddFileButton: 'Allega file',
	ClientNoteFormChooseAClient: 'Seleziona un cliente/contatto per continuare',
	ClientNoteFormContent: 'Contenuto',
	ClientNoteItemDeleteConfirmationModalDescription: 'Una volta eliminata, la nota non potrà più essere recuperata.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Nota pubblicata e bloccata.',
	ClientNotePublishedSuccessSnackbar: 'Nota pubblicata!',
	ClientNotes: 'Note del cliente',
	ClientNotesEmptyStateText: 'Per aggiungere note, vai al profilo di un cliente e clicca sulla scheda Note.',
	ClientOnboardingChoosePasswordTitle1: 'Quasi finito!',
	ClientOnboardingChoosePasswordTitle2: 'Scegli una password',
	ClientOnboardingCompleteIntake: 'Assunzione completa',
	ClientOnboardingConfirmationScreenText:
		'Hai fornito tutte le informazioni necessarie a {providerName}.	Conferma il tuo indirizzo email per iniziare il tuo onboarding. Se non lo ricevi subito, controlla la tua cartella spam.',
	ClientOnboardingConfirmationScreenTitle: 'Ottimo! Controlla la tua posta in arrivo.',
	ClientOnboardingDashboardButton: 'Vai alla Dashboard',
	ClientOnboardingHealthRecordsDesc1: 'Vuoi condividere lettere di referenza o documenti con {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Aggiungi descrizione (facoltativo)',
	ClientOnboardingHealthRecordsTitle: 'Documentazione',
	ClientOnboardingPasswordRequirements: 'Requisiti',
	ClientOnboardingPasswordRequirementsConditions1: 'Sono richiesti almeno 9 caratteri',
	ClientOnboardingProviderIntroSignupButton: 'Iscriviti per me stesso',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Iscriviti per un membro della famiglia',
	ClientOnboardingProviderIntroTitle: '{name} ti ha invitato ad unirti alla loro piattaforma Carepatron',
	ClientOnboardingRegistrationInstructions: 'Inserisci i tuoi dati personali qui sotto.',
	ClientOnboardingRegistrationTitle: 'Per prima cosa avremo bisogno di alcuni dati personali',
	ClientOnboardingStepFormsAndAgreements: 'Moduli e Accordi',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Per favore, compila i seguenti moduli per il processo di ammissione di {providerName}.',
	ClientOnboardingStepHealthDetails: 'Dettagli sulla salute',
	ClientOnboardingStepPassword: 'Password',
	ClientOnboardingStepYourDetails: 'I tuoi dettagli',
	ClientPaymentMethodDescription:
		'Salva un metodo di pagamento sul tuo profilo per rendere la prenotazione e la fatturazione del tuo prossimo appuntamento più rapide e sicure.',
	ClientPortal: 'Portale clienti',
	ClientPortalDashboardEmptyDescription:
		'Qui verranno visualizzati lo storico dei tuoi appuntamenti e delle tue presenze.',
	ClientPortalDashboardEmptyTitle:
		'Tieni traccia di tutti gli appuntamenti imminenti, richiesti e passati insieme alla tua presenza',
	ClientPreferredNotificationPanelDescription:
		'Gestisci il metodo preferito dal tuo cliente per ricevere aggiornamenti e notifiche tramite:',
	ClientPreferredNotificationPanelTitle: 'Metodo di notifica preferito',
	ClientProcessingFee: 'Il pagamento include ({currencyCode}) {amount} di commissioni di elaborazione',
	ClientProfileAddress: 'Indirizzo',
	ClientProfileDOB: 'Data di nascita',
	ClientProfileEmailHelperText: `L'aggiunta di un'e-mail garantisce l'accesso al portale`,
	ClientProfileEmailHelperTextMoreInfo: `Concedere al cliente l'accesso al portale consente ai membri del team di condividere note, file e altra documentazione`,
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Numero di identificazione',
	ClientRelationshipsAddClientOwnerButton: 'Invita il cliente',
	ClientRelationshipsAddFamilyButton: 'Invita un membro della famiglia',
	ClientRelationshipsAddStaffButton: `Aggiungere l'accesso del personale`,
	ClientRelationshipsEmptyStateText: 'Nessuna relazione aggiunta',
	ClientRemovedSuccessSnackbar: 'Client rimosso con successo.',
	ClientResponsibility: 'Responsabilità del cliente',
	ClientSavedSuccessSnackbar: 'Il client è stato salvato correttamente.',
	ClientTableClientName: 'Nome del cliente',
	ClientTablePhone: 'Telefono',
	ClientTableStatus: 'Stato',
	ClientUnarchivedSuccessfulSnackbar: 'Riuscito a dearchiviare <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} ha modificato {noteTitle, select, undefined { una nota } other {{noteTitle}}}',
	ClientView: 'Vista del cliente',
	Clients: 'Clienti',
	ClientsTable: 'Tabella dei clienti',
	ClinicalFormat: 'Formato clinico',
	ClinicalPsychologist: 'Psicologo clinico',
	Close: 'Vicino',
	CloseImportClientsModal: `Vuoi davvero annullare l'importazione dei client?`,
	CloseReactions: 'Reazioni ravvicinate',
	Closed: 'Chiuso',
	Coaching: 'Allenamento',
	Code: 'Codice',
	CodeErrorMessage: 'Il codice è obbligatorio',
	CodePlaceholder: 'Codice',
	Coinsurance: 'Coassicurazione',
	Collection: 'Collezione',
	CollectionName: 'Nome della raccolta',
	Collections: 'Collezioni',
	ColorAppointmentsBy: 'Appuntamenti colore di',
	ColorTheme: 'Tema colore',
	ColourCalendarBy: 'Calendario da colorare di',
	ComingSoon: 'Prossimamente',
	Community: 'Comunità',
	CommunityHealthLead: 'Responsabile della salute della comunità',
	CommunityHealthWorker: 'Operatore sanitario della comunità',
	CommunityTemplatesSectionDescription: 'Creato dalla comunità Carepatron',
	CommunityTemplatesSectionTitle: 'Comunità',
	CommunityUser: 'Utente della comunità',
	Complete: 'Completare',
	CompleteAndLock: 'Completa e blocca',
	CompleteSetup: 'Configurazione completa',
	CompleteSetupSuccessDescription: 'Hai completato alcuni passaggi chiave per padroneggiare Carepatron.',
	CompleteSetupSuccessDescription2: 'Sblocca più modi per semplificare la tua pratica e supportare i tuoi clienti.',
	CompleteSetupSuccessTitle: 'Successo! Stai andando alla grande!',
	CompleteStripeSetup: 'Configurazione completa di Stripe',
	Completed: 'Completato',
	ComposeSms: 'Componi SMS',
	ComputerSystemsAnalyst: 'Analista di sistemi informatici',
	Confirm: 'Confermare',
	ConfirmDeleteAccountDescription:
		'Stai per eliminare il tuo account. Questa azione non può essere annullata. Se desideri procedere, conferma qui sotto.',
	ConfirmDeleteActionDescription:
		'Sei sicuro di voler eliminare questa azione? Questa operazione non può essere annullata',
	ConfirmDeleteAutomationDescription:
		'Vuoi davvero eliminare questa automazione? Questa azione non può essere annullata.',
	ConfirmDeleteScheduleDescription:
		'Eliminare il programma <strong>{scheduleName}</strong> lo rimuoverà dai tuoi programmi e potrebbe modificare il tuo servizio online disponibile. Questa azione non può essere annullata.',
	ConfirmDraftResponseContinue: 'Continua con la risposta',
	ConfirmDraftResponseDescription:
		'Se chiudi questa pagina la tua risposta rimarrà come bozza. Puoi tornare indietro e continuare in qualsiasi momento.',
	ConfirmDraftResponseSubmitResponse: 'Invia risposta',
	ConfirmDraftResponseTitle: 'La tua risposta non è stata inviata',
	ConfirmIfUserIsClientDescription: `Il modulo di iscrizione che hai compilato è per i fornitori (ovvero team/organizzazioni sanitarie).
 Se si tratta di un errore, puoi scegliere "Continua come cliente" e ti aiuteremo a configurare il tuo portale clienti`,
	ConfirmIfUserIsClientNoButton: 'Registrati come Fornitore',
	ConfirmIfUserIsClientTitle: 'Sembra che tu sia un cliente',
	ConfirmIfUserIsClientYesButton: 'Continua come cliente',
	ConfirmKeepSeparate: 'Confermare tenere separato',
	ConfirmMerge: 'Conferma la fusione',
	ConfirmPassword: 'Conferma password',
	ConfirmRevertClaim: 'Sì, ripristina stato',
	ConfirmSignupAccessCode: 'Codice di conferma',
	ConfirmSignupButtom: 'Confermare',
	ConfirmSignupDescription: 'Inserisci il tuo indirizzo email e il codice di conferma che ti abbiamo appena inviato.',
	ConfirmSignupSubTitle: `Controlla la cartella Spam - se l'email non è arrivata`,
	ConfirmSignupSuccessSnackbar: `Ottimo, abbiamo confermato il tuo account! Ora puoi effettuare l'accesso utilizzando la tua email e password`,
	ConfirmSignupTitle: 'Conferma account',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: `Conferma l'abbonamento {price} {isMonthly, select, true {al mese} other {all'anno}}`,
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Una volta eliminati i clienti, non sarà più possibile accedere alle loro informazioni.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Elimina {count, plural, one {# cliente} other {# clientes}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Una volta eliminati i contatti, non potrai più accedere alle loro informazioni.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Elimina {count, plural, one {# contatto} other {# contatti}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId: `Questa è un'azione permanente. Una volta eliminati i membri del team, non potrai più accedere alle loro informazioni.`,
	ConfirmationModalBulkDeleteMembersTitleId:
		'Elimina {count, plural, one {# membro del team} other {# membri del team}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Chiudendo questa nota verranno interrotte tutte le trascrizioni in corso. Vuoi procedere?',
	ConfirmationModalDeleteClientField: `Questa è un'azione permanente. Una volta eliminato il campo, non sarà più accessibile ai tuoi client rimanenti.`,
	ConfirmationModalDeleteSectionMessage:
		'Una volta eliminate, tutte le domande in questa sezione saranno rimosse. Questa azione non può essere annullata.',
	ConfirmationModalDeleteService: `Questa è un'azione permanente. Una volta eliminato il servizio, non sarà più accessibile nel tuo spazio di lavoro.`,
	ConfirmationModalDeleteServiceGroup: `L'eliminazione di una raccolta rimuoverà tutti i servizi dal gruppo e tornerà all'elenco dei servizi. Questa azione non può essere annullata.`,
	ConfirmationModalDeleteTranscript: 'Sei sicuro di voler eliminare la trascrizione?',
	ConfirmationModalDescriptionDeleteClient:
		'Una volta eliminato il cliente, non sarà più possibile accedere alle sue informazioni.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient: `Una volta rimosso l'accesso, non sarà più possibile visualizzare le informazioni del cliente.`,
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Il loro profilo non verrà eliminato, ma solo rimosso come relazione con questo cliente.',
	ConfirmationModalDescriptionRemoveStaff: 'Vuoi davvero rimuovere questa persona dal provider?',
	ConfirmationModalEndSession: 'Sei sicuro di voler terminare la sessione?',
	ConfirmationModalTitle: 'Sei sicuro?',
	Confirmed: 'Confermato',
	ConflictTimezoneWarningMessage: 'Possono verificarsi conflitti a causa di più fusi orari',
	Connect: 'Collegare',
	ConnectExistingClientOrContact: 'Crea nuovo cliente/contatto',
	ConnectInboxGoogleDescription: 'Aggiungi un account Gmail o un elenco di gruppi Google',
	ConnectInboxMicrosoftDescription: 'Aggiungi un account Outlook, Office365 o Exchange',
	ConnectInboxModalDescription:
		'Collega le tue app per inviare, ricevere e monitorare senza problemi tutte le tue comunicazioni in un unico posto centralizzato.',
	ConnectInboxModalExistingDescription:
		'Utilizza una connessione esistente dalle impostazioni delle app connesse per semplificare il processo di configurazione.',
	ConnectInboxModalExistingTitle: 'App connessa esistente in Carepatron',
	ConnectInboxModalTitle: 'Connetti posta in arrivo',
	ConnectToStripe: 'Connettiti a Stripe',
	ConnectZoom: 'Connetti Zoom',
	ConnectZoomModalDescription: 'Consenti a Carepatron di gestire le videochiamate per i tuoi appuntamenti.',
	ConnectedAppDisconnectedNotificationSubject: `Abbiamo perso la connessione all'account {account}. Si prega di riconnettere.`,
	ConnectedAppSyncDescription:
		'Gestisci le app connesse per creare eventi nei calendari di terze parti direttamente da Carepatron.',
	ConnectedApps: 'App connesse',
	ConnectedAppsGMailDescription: 'Aggiungi account Gmail o elenco di gruppi Google',
	ConnectedAppsGoogleCalendarDescription: 'Aggiungi calendari account o elenchi di gruppi Google',
	ConnectedAppsGoogleDescription: 'Aggiungi il tuo account Gmail e sincronizza i calendari di Google',
	ConnectedAppsMicrosoftDescription: 'Aggiungi un account Outlook, Office365 o Exchange',
	ConnectedCalendars: 'Calendari connessi',
	ConsentDocumentation: 'Moduli e accordi',
	ConsentDocumentationPublicTemplateError:
		'Per motivi di sicurezza, puoi scegliere solo modelli del tuo team (non pubblici).',
	ConstructionWorker: 'Operaio edile',
	Consultant: 'Consulente',
	Contact: 'Contatto',
	ContactAccessTypeHelperText: 'Consente agli amministratori della famiglia di aggiornare le informazioni',
	ContactAccessTypeHelperTextMoreInfo: 'Questo ti permetterà di condividere note/documenti su {clientFirstName}',
	ContactAddressLabelBilling: 'Fatturazione',
	ContactAddressLabelHome: 'Casa',
	ContactAddressLabelOthers: 'Altri',
	ContactAddressLabelWork: 'Lavoro',
	ContactChangeConfirmation:
		'Il cambiamento del contatto fattura rimuoverà tutte le voci di riga correlate a <mark>{contactName}</mark>',
	ContactDetails: 'Dettagli di contatto',
	ContactEmailLabelOthers: 'Altri',
	ContactEmailLabelPersonal: 'Personale',
	ContactEmailLabelSchool: 'Scuola',
	ContactEmailLabelWork: 'Lavoro',
	ContactInformation: 'Informazioni sui contatti',
	ContactInformationText: 'Informazioni sui contatti',
	ContactListCreateButton: 'Nuovo contatto',
	ContactName: 'Nome del contatto',
	ContactPhoneLabelHome: 'Casa',
	ContactPhoneLabelMobile: 'Mobile',
	ContactPhoneLabelSchool: 'Scuola',
	ContactPhoneLabelWork: 'Lavoro',
	ContactRelationship: 'Relazione di contatto',
	ContactRelationshipFormAccessType: `Concedi l'accesso alle informazioni condivise`,
	ContactRelationshipGrantAccessInfo: 'Questo ti consentirà di condividere le note ',
	ContactSupport: 'Contatta il supporto',
	Contacts: 'Contatti',
	ContainerIdNotSet: 'ID contenitore non impostato',
	Contemporary: 'Contemporaneo',
	Continue: 'Continuare',
	ContinueDictating: 'Continua a dettare',
	ContinueEditing: 'Continua a modificare',
	ContinueImport: `Continua l'importazione`,
	ContinueTranscription: 'Continua la trascrizione',
	ContinueWithApple: 'Continua con Apple',
	ContinueWithGoogle: 'Continua con Google',
	Conversation: 'Conversazione',
	Copay: 'Co-pagamento',
	CopayOrCoinsurance: 'Co-pagamento o coassicurazione',
	Copayment: 'Co-pagamento',
	CopiedToClipboard: 'Copiato negli appunti',
	Copy: 'Copia',
	CopyAddressSuccessSnackbar: 'Indirizzo copiato negli appunti',
	CopyCode: 'Copia codice',
	CopyCodeToClipboardSuccess: 'Codice copiato negli appunti',
	CopyEmailAddressSuccessSnackbar: 'Indirizzo email copiato negli appunti',
	CopyLink: 'Copia il collegamento',
	CopyLinkForCall: 'Copia questo link per condividere questa chiamata:',
	CopyLinkSuccessSnackbar: 'Collegamento copiato negli appunti',
	CopyMeetingLink: 'Copia il link della riunione',
	CopyPaymentLink: 'Copia il link di pagamento',
	CopyPhoneNumberSuccessSnackbar: 'Numero di telefono copiato negli appunti',
	CopyTemplateLink: 'Copia il collegamento al modello',
	CopyTemplateLinkSuccess: 'Collegamento copiato negli appunti',
	CopyToClipboardError: 'Impossibile copiare negli appunti. Riprova.',
	CopyToTeamTemplates: 'Copia nei modelli del team',
	CopyToWorkspace: `Copia nell'area di lavoro`,
	Cosmetologist: 'Cosmetologo',
	Cost: 'Costo',
	CostErrorMessage: 'Il costo è obbligatorio',
	Counseling: 'Consulenza',
	Counselor: 'Consigliere',
	Counselors: 'Consulenti',
	CountInvoicesAdded: '{count, plural, one {# Fattura aggiunta} other {# Fatture aggiunte}}',
	CountNotesAdded: '{count, plural, one {# Nota aggiunta} other {# Note aggiunte}}',
	CountSelected: '{count} selezionati',
	CountTimes: '{count} volte',
	Country: 'Paese',
	Cousin: 'Cugino',
	CoverageType: 'Tipo di copertura',
	Covered: 'Coperto',
	Create: 'Creare',
	CreateANewClient: 'Crea un nuovo cliente',
	CreateAccount: 'Creare un account',
	CreateAndSignNotes: 'Crea e firma la nota con i clienti',
	CreateAvailabilityScheduleFailure: 'Impossibile creare un nuovo programma di disponibilità',
	CreateAvailabilityScheduleSuccess: 'Creazione corretta del nuovo programma di disponibilità',
	CreateBillingItems: 'Crea voci di fatturazione',
	CreateCallFormButton: 'Avvia chiamata',
	CreateCallFormInviteOnly: 'Solo su invito',
	CreateCallFormInviteOnlyMoreInfo:
		'Possono partecipare solo le persone invitate a questa chiamata. Per condividere questa chiamata con altri, deseleziona semplicemente questa casella e copia/incolla il link nella pagina successiva',
	CreateCallFormRecipients: 'Destinatari',
	CreateCallFormRegion: 'Regione ospitante',
	CreateCallModalAddClientContactSelectorLabel: 'Contatti del cliente',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Cerca per nome cliente',
	CreateCallModalAddStaffSelectorLabel: 'Membri del team (facoltativo)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Cerca per nome del personale',
	CreateCallModalDescription:
		'Avvia una chiamata e invita membri dello staff e/o contatti. In alternativa, puoi deselezionare la casella "Privato" per rendere questa chiamata condivisibile con chiunque abbia Carepatron',
	CreateCallModalTitle: 'Avvia una chiamata',
	CreateCallModalTitleLabel: 'Titolo (facoltativo)',
	CreateCallNoPersonIdToolTip: 'Solo i contatti/clienti con accesso al portale possono partecipare alle chiamate',
	CreateClaim: 'Crea reclamo',
	CreateClaimCompletedMessage: 'La tua richiesta è stata creata.',
	CreateClientModalTitle: 'Nuovo cliente',
	CreateContactModalTitle: 'Nuovo contatto',
	CreateContactRelationshipButton: 'Aggiungi relazione',
	CreateContactSelectorDefaultOption: '  Crea contatto',
	CreateContactWithRelationshipFormAccessType: `Concedi l'accesso alle informazioni condivise `,
	CreateDocumentDnDPrompt: 'Trascina e rilascia per caricare i file',
	CreateDocumentSizeLimit: 'Limite di dimensione per file {size}MB. {total} file totali.',
	CreateFreeAccount: 'Crea un account gratuito',
	CreateInvoice: 'Crea fattura',
	CreateLink: 'Crea collegamento',
	CreateNew: 'Crea nuovo',
	CreateNewAppointment: 'Crea un nuovo appuntamento',
	CreateNewClaim: 'Crea un nuovo reclamo',
	CreateNewClaimForAClient: 'Crea una nuova richiesta per un cliente.',
	CreateNewClient: 'Crea nuovo cliente',
	CreateNewConnection: 'Nuova connessione',
	CreateNewContact: 'Crea nuovo contatto',
	CreateNewField: 'Crea nuovo campo',
	CreateNewLocation: 'Nuova posizione',
	CreateNewService: 'Crea nuovo servizio',
	CreateNewServiceGroupFailure: 'Impossibile creare una nuova raccolta',
	CreateNewServiceGroupMenu: 'Nuova collezione',
	CreateNewServiceGroupSuccess: 'Nuova raccolta creata con successo',
	CreateNewServiceMenu: 'Nuovo servizio',
	CreateNewTeamMember: 'Crea un nuovo membro del team',
	CreateNewTemplate: 'Nuovo modello',
	CreateNote: 'Crea nota',
	CreateSuperbillReceipt: 'Nuovo superprogetto',
	CreateSuperbillReceiptSuccess: 'Ricevuta Superbill creata con successo',
	CreateTemplateFolderSuccessMessage: 'Creato con successo {folderTitle}',
	Created: 'Creato',
	CreatedAt: 'Creato {timestamp}',
	Credit: 'Credito',
	CreditAdded: 'Credito applicato',
	CreditAdjustment: 'Adeguamento del credito',
	CreditAdjustmentReasonHelperText: 'Questa è una nota interna e non sarà visibile al tuo cliente.',
	CreditAdjustmentReasonPlaceholder: `L'aggiunta di un motivo di rettifica può essere utile durante la revisione delle transazioni fatturabili`,
	CreditAmount: '{amount} NC',
	CreditBalance: 'Saldo del credito',
	CreditCard: 'Carta di credito',
	CreditCardExpire: 'Scade {exp_month}/{exp_year}',
	CreditCardNumber: 'Numero di carta di credito',
	CreditDebitCard: 'Carta',
	CreditIssued: 'Credito emesso',
	CreditsUsed: 'Credito utilizzato',
	Crop: 'Raccolto',
	Currency: 'Valuta',
	CurrentCredit: 'Credito attuale',
	CurrentEventTime: `Ora dell'evento attuale`,
	CurrentPlan: 'Piano attuale',
	Custom: 'Costume',
	CustomRange: 'Gamma personalizzata',
	CustomRate: 'Tariffa personalizzata',
	CustomRecurrence: 'Ricorrenza personalizzata',
	CustomServiceAvailability: 'Disponibilità del servizio',
	CustomerBalance: 'Saldo del cliente',
	CustomerName: 'Nome del cliente',
	CustomerNameIsRequired: 'Il nome del cliente è obbligatorio',
	CustomerServiceRepresentative: 'Rappresentante del servizio clienti',
	CustomiseAppointments: 'Personalizza gli appuntamenti',
	CustomiseBookingLink: 'Personalizza le opzioni di prenotazione',
	CustomiseBookingLinkServicesInfo: 'I clienti possono scegliere solo i servizi prenotabili',
	CustomiseBookingLinkServicesLabel: 'Servizi',
	CustomiseClientRecordsAndWorkspace: `Personalizza i record dei tuoi clienti e l'area di lavoro`,
	CustomiseClientSettings: 'Personalizza le impostazioni del client',
	Customize: 'Personalizzare',
	CustomizeAppearance: `Personalizza l'aspetto`,
	CustomizeAppearanceDesc: `Personalizza l'aspetto del tuo sistema di prenotazione online in base al tuo marchio e ottimizza il modo in cui i tuoi servizi vengono presentati ai clienti.`,
	CustomizeClientFields: 'Personalizza i campi del cliente',
	CustomizeInvoiceTemplate: 'Personalizza il modello di fattura',
	CustomizeInvoiceTemplateDescription: 'Crea senza sforzo fatture professionali che rispecchiano il tuo marchio.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'È richiesto DX',
	Daily: 'Quotidiano',
	DanceTherapist: 'Danzaterapeuta',
	DangerZone: 'Zona pericolosa',
	Dashboard: 'Pannello di controllo',
	Date: 'Data',
	DateAndTime: 'Data ',
	DateDue: 'Data di scadenza',
	DateErrorMessage: 'La data è obbligatoria',
	DateFormPrimaryText: 'Data',
	DateFormSecondaryText: 'Scegli da un selettore di date',
	DateIssued: 'Data di emissione',
	DateOfPayment: 'Data del pagamento',
	DateOfService: 'Data del servizio',
	DateOverride: 'Sostituzione data',
	DateOverrideColor: 'Colore di sostituzione della data',
	DateOverrideInfo:
		'La sostituzione delle date consente ai professionisti di modificare manualmente la propria disponibilità per date specifiche, sostituendo le pianificazioni regolari.',
	DateOverrideInfoBanner:
		'In queste fasce orarie è possibile prenotare solo i servizi specificati per questa sostituzione di data; non sono consentite altre prenotazioni online.',
	DateOverrides: 'Sostituzioni di data',
	DatePickerFormPrimaryText: 'Data',
	DatePickerFormSecondaryText: 'Scegli una data',
	DateRange: 'Intervallo di date',
	DateRangeFormPrimaryText: 'Intervallo di date',
	DateRangeFormSecondaryText: 'Scegli un intervallo di date',
	DateReceived: 'Data di ricezione',
	DateSpecificHours: 'Orari specifici della data',
	DateSpecificHoursDescription:
		'Aggiungi le date in cui la tua disponibilità cambia rispetto agli orari programmati o per offrire un servizio in una data specifica.',
	DateUploaded: 'Caricato {date, date, medium}',
	Dates: 'Date',
	Daughter: 'Figlia',
	Day: 'Giorno',
	DayPlural: '{count, plural, one {giorno} other {giorni}}',
	Days: 'Giorni',
	DaysPlural: '{età, plural, one {# giorno} other {# giorni}}',
	DeFacto: 'Di fatto',
	Deactivated: 'Disattivato',
	Debit: 'Addebito',
	DecreaseIndent: 'Diminuisci rientro',
	Deductibles: 'Franchigie',
	Default: 'Predefinito',
	DefaultBillingProfile: 'Profilo di fatturazione predefinito',
	DefaultDescription: 'Descrizione predefinita',
	DefaultEndOfLine: 'Non ci sono più articoli',
	DefaultInPerson: 'Appuntamenti con i clienti',
	DefaultInvoiceTitle: 'Titolo predefinito',
	DefaultNotificationSubject: 'Hai ricevuto una nuova notifica per {notificationType}',
	DefaultPaymentMethod: 'Metodo di pagamento predefinito',
	DefaultService: 'Servizio predefinito',
	DefaultValue: 'Predefinito',
	DefaultVideo: 'Email di appuntamento video con il cliente',
	DefinedTemplateType: '{invoiceTemplate} modello',
	Delete: 'Eliminare',
	DeleteAccountButton: 'Elimina account',
	DeleteAccountDescription: 'Elimina il tuo account dalla piattaforma',
	DeleteAccountPanelInfoAlert:
		'Devi eliminare i tuoi workspace prima di eliminare il tuo profilo. Per procedere, passa a un workspace e seleziona Impostazioni > Impostazioni Workspace.',
	DeleteAccountTitle: 'Elimina account',
	DeleteAppointment: 'Elimina appuntamento',
	DeleteAppointmentDescription: 'Vuoi davvero eliminare questo appuntamento? Puoi ripristinarlo più tardi.',
	DeleteAvailabilityScheduleFailure: 'Impossibile eliminare la pianificazione della disponibilità',
	DeleteAvailabilityScheduleSuccess: 'Eliminazione corretta della pianificazione della disponibilità',
	DeleteBillable: 'Elimina fatturabile',
	DeleteBillableConfirmationMessage:
		'Vuoi davvero eliminare questo fatturabile? Questa azione non può essere annullata.',
	DeleteBillingProfileConfirmationMessage: 'Questa operazione rimuoverà definitivamente il profilo di fatturazione.',
	DeleteCardConfirmation: `Questa è un'azione permanente. Una volta eliminata la carta, non potrai più accedervi.`,
	DeleteCategory: 'Elimina categoria (non è permanente a meno che le modifiche non vengano salvate)',
	DeleteClientEventConfirmationDescription: 'Verrà rimosso definitivamente.',
	DeleteClients: 'Eliminare i clienti',
	DeleteCollection: 'Elimina raccolta',
	DeleteColumn: 'Elimina colonna',
	DeleteConversationConfirmationDescription:
		'Elimina questa conversazione per sempre. Questa azione non può essere annullata.',
	DeleteConversationConfirmationTitle: 'Elimina la conversazione per sempre',
	DeleteExternalEventDescription: 'Sei sicuro di voler cancellare questo appuntamento?',
	DeleteFileConfirmationModalPrompt: 'Una volta eliminato, il file non potrà più essere recuperato.',
	DeleteFolder: 'Elimina cartella',
	DeleteFolderConfirmationMessage: `Sei sicuro di voler eliminare questa cartella {name}? Tutti gli elementi all'interno di questa cartella saranno eliminati. Puoi ripristinare questa cartella in un secondo momento.`,
	DeleteForever: 'Elimina per sempre',
	DeleteInsurancePayerConfirmationMessage: `La rimozione di {payer} lo cancellerà dall'elenco dei tuoi pagatori di assicurazione. Questa azione è permanente e non può essere ripristinata.`,
	DeleteInsurancePayerFailure: `Impossibile eliminare il pagatore dell'assicurazione`,
	DeleteInsurancePolicyConfirmationMessage: 'Questa operazione eliminerà definitivamente la polizza assicurativa.',
	DeleteInvoiceConfirmationDescription:
		'Questa azione non può essere annullata. Eliminerà definitivamente la fattura e tutti i pagamenti ad essa associati.',
	DeleteLocationConfirmation: `L'eliminazione di una posizione è un'azione permanente. Una volta eliminata, non avrai più accesso ad essa. Questa azione non può essere annullata.`,
	DeletePayer: 'Elimina pagatore',
	DeletePracticeWorkspace: `Eliminare l'area di lavoro pratica`,
	DeletePracticeWorkspaceDescription: 'Elimina definitivamente questo spazio di lavoro di pratica',
	DeletePracticeWorkspaceFailedSnackbar: `Impossibile eliminare l'area di lavoro`,
	DeletePracticeWorkspaceModalCancelButton: 'Sì, annulla il mio abbonamento',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription: `Prima di procedere all'eliminazione del tuo spazio di lavoro, devi prima annullare l'abbonamento.`,
	DeletePracticeWorkspaceModalConfirmButton: `Sì, elimina definitivamente l'area di lavoro`,
	DeletePracticeWorkspaceModalDescription: `{name} spazio di lavoro verrà eliminato in modo permanente e tutti i membri del team perderanno l'accesso. Scarica tutti i dati o i messaggi importanti di cui potresti aver bisogno prima che l'eliminazione avvenga. Questa azione non può essere annullata.`,
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Questa decisione è stata presa a causa di:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Motivo',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Per favore, spiegaci perché desideri eliminare il tuo account.',
	DeletePracticeWorkspaceModalTitle: 'Sei sicuro?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: `L'accesso a tutti i membri del team è stato rimosso`,
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} è stato eliminato correttamente',
	DeletePublicTemplateContent: 'Questa operazione eliminerà solo il modello pubblico e non quello del tuo team.',
	DeleteRecurringAppointmentModalTitle: 'Elimina appuntamento ricorrente',
	DeleteRecurringEventModalTitle: 'Elimina riunione ricorrente',
	DeleteRecurringReminderModalTitle: 'Elimina promemoria ricorrente',
	DeleteRecurringTaskModalTitle: 'Elimina attività ripetuta',
	DeleteReminderConfirmation: `Questa è un'azione permanente. Una volta eliminato il promemoria, non potrai più accedervi. Influirà solo sui nuovi appuntamenti`,
	DeleteSection: 'Elimina sezione',
	DeleteSectionInfo:
		'La eliminación de la sección <strong>{section}</strong> ocultará todos los campos existentes dentro de ella. Esta acción no se puede deshacer.',
	DeleteSectionWarning:
		'Campi fondamentali non possono essere eliminati e saranno spostati nella sezione esistente **{sezione}**.',
	DeleteServiceFailure: 'Impossibile eliminare il servizio',
	DeleteServiceSuccess: 'Servizio eliminato con successo',
	DeleteStaffScheduleOverrideDescription:
		'Il suppression de cette date de remplacement sur {value} la supprimera de vos horaires et pourra modifier votre service en ligne disponible. Cette action est irréversible.',
	DeleteSuperbillConfirmationDescription:
		'Questa azione non può essere annullata. Eliminerà definitivamente la ricevuta Superbill.',
	DeleteSuperbillFailure: 'Impossibile eliminare la ricevuta Superbill',
	DeleteSuperbillSuccess: 'Ricevuta Superbill eliminata con successo',
	DeleteTaxRateConfirmationDescription: 'Sei sicuro di voler eliminare questa aliquota fiscale?',
	DeleteTemplateContent: 'Questa azione non può essere annullata',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} eliminato correttamente',
	DeleteTemplateSuccessMessage: '{templateTitle} eliminato correttamente',
	DeleteTemplateTitle: 'Sei sicuro di voler eliminare questo modello?',
	DeleteTranscript: 'Elimina trascrizione',
	DeleteWorkspace: 'Elimina area di lavoro',
	Deleted: 'Eliminato',
	DeletedBy: 'Eliminato da',
	DeletedContact: 'Contatto eliminato',
	DeletedOn: 'Eliminato il',
	DeletedStatusLabel: 'Stato eliminato',
	DeletedUserTooltip: 'Questo client è stato eliminato',
	DeliveryMethod: 'Metodo di consegna',
	Demo: 'Demo',
	Denied: 'Negato',
	Dental: 'Dentale',
	DentalAssistant: 'Assistente dentale',
	DentalHygienist: 'Igienista dentale',
	Dentist: 'Dentista',
	Dentists: 'Dentisti',
	Description: 'Descrizione',
	DescriptionMustNotExceed: 'Descrizione non deve superare {max} caratteri',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { con {staffName}}}',
	Details: 'Dettagli',
	Devices: 'Dispositivi',
	Diagnosis: 'Diagnosi',
	DiagnosisAndBillingItems: 'Diagnosi ',
	DiagnosisCode: 'Codice di diagnosi',
	DiagnosisCodeErrorMessage: 'È richiesto un codice di diagnosi',
	DiagnosisCodeSelectorPlaceholder: 'Cerca e aggiungi dai codici diagnostici ICD-10',
	DiagnosisCodeSelectorTooltip:
		'I codici di diagnosi vengono utilizzati per automatizzare le ricevute delle superfatture per il rimborso assicurativo',
	DiagnosticCodes: 'Codici diagnostici',
	Dictate: 'Dettare',
	DictatingIn: 'Dettare in',
	Dictation: 'Dettatura',
	DidNotAttend: 'Non ha partecipato',
	DidNotComplete: 'Non completato',
	DidNotProviderEnoughValue: 'Non ha fornito abbastanza valore',
	DidntProvideEnoughValue: 'Non ha fornito abbastanza valore',
	DieteticsOrNutrition: 'Dietetica o nutrizione',
	Dietician: 'Dietista',
	Dieticians: 'Dietologi',
	Dietitian: 'Dietista',
	DigitalSign: 'Firma qui:',
	DigitalSignHelp: '(Clicca/premi verso il basso per disegnare)',
	DirectDebit: 'Addebito diretto',
	DirectTextLink: 'Link diretto al testo',
	Disable: 'Disabilitare',
	DisabledEmailInfo: 'Non possiamo aggiornare il tuo indirizzo email poiché il tuo account non è gestito da noi',
	Discard: 'Scartare',
	DiscardChanges: 'Ignora le modifiche',
	DiscardDrafts: 'Scarta le bozze',
	Disconnect: 'Disconnettersi',
	DisconnectAppConfirmation: 'Vuoi disconnettere questa app?',
	DisconnectAppConfirmationDescription: 'Sei sicuro di voler scollegare questa app?',
	DisconnectAppConfirmationTitle: 'Disconnetti app',
	Discount: 'Sconto',
	DisplayCalendar: 'Visualizza in Carepatron',
	DisplayName: 'Nome da visualizzare',
	DisplayedToClients: 'Visualizzato ai clienti',
	DiversionalTherapist: 'Terapeuta diversivo',
	DoItLater: 'Fallo più tardi',
	DoNotImport: 'Non importare',
	DoNotSend: 'Non inviare',
	DoThisLater: 'Fallo più tardi',
	DoYouWantToEndSession: 'Vuoi continuare o terminare la sessione adesso?',
	Doctor: 'Medico',
	Doctors: 'Medici',
	DoesNotRepeat: 'Non si ripete',
	DoesntWorkWellWithExistingTools: 'Non funziona bene con i nostri strumenti o flussi di lavoro esistenti',
	DogWalker: 'Passeggiatore di cani',
	Done: 'Fatto',
	DontAllowClientsToCancel: 'Non consentire ai clienti di annullare',
	DontHaveAccount: 'Non hai un account?',
	DontSend: 'Non inviare',
	Double: 'Raddoppiare',
	DowngradeTo: 'Degradare a {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Spiacenti, non puoi effettuare il downgrade del tuo piano perché hai troppi membri del team. Rimuovi alcuni dal tuo provider e riprova.',
	Download: 'Scaricamento',
	DownloadAsPdf: 'Scarica come PDF',
	DownloadERA: 'Scarica ERA',
	DownloadPDF: 'Scarica PDF',
	DownloadTemplateFileName: 'Carepatron Switching Template.csv',
	DownloadTemplateTileDescription:
		'Utilizza il nostro modello di foglio di calcolo per organizzare e caricare i tuoi clienti.',
	DownloadTemplateTileLabel: 'Scarica il modello',
	Downloads: '{number, plural, one {<span>#</span> Scarica} other {<span>#</span> Scaricamenti}}',
	DoxyMe: 'Doxy.io',
	Draft: 'Bozza',
	DraftResponses: 'Bozza di risposta',
	DraftSaved: 'Modifiche salvate',
	DragAndDrop: 'trascinare e rilasciare',
	DragDropText: 'Trascina e rilascia i documenti sanitari',
	DragToMove: 'Trascina per spostare',
	DragToMoveOrActivate: 'Trascina per spostare o attivare',
	DramaTherapist: 'Drammaterapeuta',
	DropdownFormFieldPlaceHolder: `Scegli le opzioni dall'elenco`,
	DropdownFormPrimaryText: 'Cadere in picchiata',
	DropdownFormSecondaryText: 'Scegli da un elenco di opzioni',
	DropdownTextFieldError: `Il testo dell'opzione a discesa non può essere vuoto`,
	DropdownTextFieldPlaceholder: `Aggiungi un'opzione a discesa`,
	Due: 'Scadenza',
	DueDate: 'Scadenza',
	Duplicate: 'Duplicato',
	DuplicateAvailabilityScheduleFailure: 'Impossibile duplicare la pianificazione della disponibilità',
	DuplicateAvailabilityScheduleSuccess: 'Programma di {name} duplicato correttamente',
	DuplicateClientBannerAction: 'Revisione',
	DuplicateClientBannerDescription: `L'unione dei record dei clienti duplicati li consolida in uno solo, mantenendo tutte le informazioni univoche sui clienti.`,
	DuplicateClientBannerTitle: '{count} Duplicati trovati',
	DuplicateColumn: 'Colonna duplicata',
	DuplicateContactFieldSettingErrorSnackbar: 'Non è possibile avere nomi di sezione duplicati',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Non è possibile avere nomi di campo duplicati',
	DuplicateEmailError: 'Email duplicata',
	DuplicateHeadingName: 'Sezione {name} già esiste',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Esiste già una fattura con lo stesso "numero di fattura".',
	DuplicateRecords: 'Record duplicati',
	DuplicateRecordsMinimumError: 'Devono essere selezionati almeno 2 record',
	DuplicateRecordsRequired: 'Seleziona almeno 1 record da separare',
	DuplicateServiceFailure: 'Impossibile duplicare <strong>{titolo}</strong>',
	DuplicateServiceSuccess: 'Riuscito a duplicare <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Cartella duplicata correttamente',
	DuplicateTemplateSuccess: 'Modello duplicato con successo',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'Codice DX',
	DxCodeSelectPlaceholder: 'Cerca e aggiungi dai codici ICD-10',
	EIN: 'EIN',
	EMG: 'Elettromiografia',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Nessuno',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>contiene modifiche</i>} other {}}',
	EarnReferralCredit: 'Guadagna ${creditAmount}',
	Economist: 'Economista',
	Edit: 'Modificare',
	EditArrangements: 'Modifica accordi',
	EditBillTo: 'Modifica fattura in',
	EditClient: 'Modifica cliente',
	EditClientFileModalDescription: `Modifica l'accesso a questo file selezionando le opzioni nelle caselle di controllo "Visualizzabile da"`,
	EditClientFileModalTitle: 'Modifica file',
	EditClientNoteModalDescription:
		'Modifica il contenuto della nota. Utilizza la sezione "Visualizzabile da" per modificare chi può vedere la nota.',
	EditClientNoteModalTitle: 'Modifica nota',
	EditConnectedAppButton: 'Modificare',
	EditConnections: 'Modifica connessioni{account, select, null { } undefined { } other { per {account}}}',
	EditContactDetails: 'Modifica i dettagli del contatto',
	EditContactFormIsClientLabel: 'Converti in client',
	EditContactIsClientCheckboxWarning: 'La conversione di un contatto in un cliente non può essere annullata',
	EditContactIsClientWanringModal:
		'La conversione di questo contatto in un Cliente non può essere annullata. Tuttavia, tutte le relazioni rimarranno e ora avrai accesso alle loro note, file e altra documentazione.',
	EditContactRelationship: 'Modifica relazione contatto',
	EditDetails: 'Modifica i dettagli',
	EditFileModalTitle: 'Modifica il file per {name}',
	EditFolder: 'Modifica cartella',
	EditFolderDescription: 'Rinomina la cartella come...',
	EditInvoice: 'Modifica fattura',
	EditInvoiceDetails: 'Modifica i dettagli della fattura',
	EditLink: 'Modifica collegamento',
	EditLocation: 'Modifica posizione',
	EditLocationFailure: 'Impossibile aggiornare la posizione',
	EditLocationSucess: 'Posizione aggiornata con successo',
	EditPaymentDetails: 'Modifica i dettagli del pagamento',
	EditPaymentMethod: 'Modifica metodo di pagamento',
	EditPersonalDetails: 'Modifica i dati personali',
	EditPractitioner: 'Modifica il professionista',
	EditProvider: 'Modifica fornitore',
	EditProviderDetails: 'Modifica i dettagli del fornitore',
	EditRecurrence: 'Modifica ricorrenza',
	EditRecurringAppointmentModalTitle: 'Modifica appuntamento ricorrente',
	EditRecurringEventModalTitle: 'Modifica riunione ricorrente',
	EditRecurringReminderModalTitle: 'Modifica promemoria ricorrente',
	EditRecurringTaskModalTitle: 'Modifica attività ricorrente',
	EditRelationshipModalTitle: 'Modifica relazione',
	EditService: 'Modifica servizio',
	EditServiceFailure: 'Impossibile aggiornare il nuovo servizio',
	EditServiceGroup: 'Modifica raccolta',
	EditServiceGroupFailure: 'Impossibile aggiornare la raccolta',
	EditServiceGroupSuccess: 'Raccolta aggiornata con successo',
	EditServiceSuccess: 'Nuovo servizio aggiornato con successo',
	EditStaffDetails: 'Modifica i dettagli del personale',
	EditStaffDetailsCantUpdatedEmailTooltip: `Impossibile aggiornare l'indirizzo email. Crea un nuovo membro del team con un nuovo indirizzo email.`,
	EditSubscriptionBilledQuantity: 'Quantità fatturata',
	EditSubscriptionBilledQuantityValue: '{billedUsers} membri del team',
	EditSubscriptionLimitedTimeOffer: 'Offerta a tempo limitato! Sconto del 50% per 6 mesi.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Il costo della tua sottoscrizione verrà regolato quando aggiungerai o rimuoverai membri del team.',
	EditSubscriptionUpgradeContent:
		'Il tuo account verrà aggiornato immediatamente al nuovo piano e al nuovo periodo di fatturazione. Eventuali modifiche di prezzo saranno automaticamente addebitate al tuo metodo di pagamento salvato o accreditate sul tuo account.',
	EditSubscriptionUpgradePlanTitle: 'Aggiorna piano di abbonamento',
	EditSuperbillReceipt: 'Modifica superfattura',
	EditTags: 'Modifica i tag',
	EditTemplate: 'Modifica modello',
	EditTemplateFolderSuccessMessage: 'Cartella modello modificata con successo',
	EditValue: 'Modifica {value}',
	Edited: 'Modificato',
	Editor: 'Redattore',
	EditorAlertDescription: `È stato rilevato un formato non supportato. Ricarica l'app o contatta il nostro team di supporto.`,
	EditorAlertTitle: 'Stiamo riscontrando problemi nella visualizzazione di questo contenuto',
	EditorPlaceholder:
		'Inizia a scrivere, scegli un modello o aggiungi blocchi di base per catturare le risposte dei tuoi clienti.',
	EditorTemplatePlaceholder: 'Inizia a scrivere o aggiungi componenti per creare un modello',
	EditorTemplateWithSlashCommandPlaceholder:
		'Inizia a scrivere o aggiungi blocchi di base per catturare le risposte dei clienti. Utilizza i comandi barra (/) per azioni rapide.',
	EditorWithSlashCommandPlaceholder:
		'Inizia a scrivere, scegli un modello o aggiungi blocchi di base per catturare le risposte del cliente. Usa i comandi barra ( / ) per azioni rapide.',
	EffectiveStartEndDate: 'Data di inizio - fine effettiva',
	ElectricalEngineer: 'Ingegnere elettrico',
	Electronic: 'Elettronico',
	ElectronicSignature: 'Firma elettronica',
	ElementarySchoolTeacher: 'Insegnante di scuola elementare',
	Eligibility: 'Ammissibilità',
	Email: 'E-mail',
	EmailAlreadyExists: `L'indirizzo email esiste già`,
	EmailAndSms: 'E-mail ',
	EmailBody: `Corpo dell'email`,
	EmailContainsIgnoredDescription: `La seguente email contiene un'email del mittente/dei mittenti che è attualmente ignorata. Vuoi continuare?`,
	EmailInviteToPortalBody: `Ciao {contactName},
Per favore, segui questo link per accedere al tuo portale client sicuro e gestire facilmente le tue cure.

Cordiali saluti,

{providerName}`,
	EmailInviteToPortalSubject: 'Benvenuto in {providerName}',
	EmailInvoice: 'Fattura via e-mail',
	EmailInvoiceOverdueBody: `Ciao {contactName}
La tua fattura {invoiceNumber} è scaduta.
Ti preghiamo di pagare la tua fattura online utilizzando il link qui sotto.

Se hai domande, ti preghiamo di farcelo sapere.

Grazie,
{providerName}`,
	EmailInvoicePaidBody: `Ciao {contactName}
La tua fattura {invoiceNumber} è stata pagata.
Per visualizzare e scaricare una copia della tua fattura segui il link qui sotto.

Se hai domande, faccelo sapere.

Grazie,
{providerName}`,
	EmailInvoiceProcessingBody: `Ciao {contactName}
La tua fattura {invoiceNumber} è pronta.
Segui il link sottostante per visualizzare la tua fattura.

Se hai domande, faccelo sapere.

Grazie,
{providerName}`,
	EmailInvoiceUnpaidBody: `Ciao {contactName}
La tua fattura {invoiceNumber} è pronta e deve essere pagata entro {dueDate}.
Per visualizzare e pagare la tua fattura online, segui il link qui sotto.

Se hai domande, faccelo sapere.

Grazie,
{providerName}`,
	EmailInvoiceVoidBody: `Ciao {contactName}
La tua fattura {invoiceNumber} è nulla.
Per visualizzare questa fattura, segui il link qui sotto.

Se hai domande, faccelo sapere.

Grazie,
{providerName}`,
	EmailNotFound: 'Email non trovata',
	EmailNotVerifiedErrorCodeSnackbar: `Impossibile eseguire l'azione. Devi verificare il tuo indirizzo email.`,
	EmailNotVerifiedTitle: 'La tua email non è verificata. Alcune funzionalità saranno limitate.',
	EmailSendClientIntakeBody: `Ciao {contactName},
{providerName} vorrebbe che tu fornisca alcune informazioni e riveda documenti importanti. Per iniziare, segui il link qui sotto.

Cordiali saluti,

{providerName}`,
	EmailSendClientIntakeSubject: 'Benvenuto su {providerName}',
	EmailSuperbillReceipt: 'Email superbill',
	EmailSuperbillReceiptBody: `Ciao {contactName},
{providerName} ti ha inviato una copia della tua ricevuta di rimborso {date}.

Puoi scaricarla e inviarla direttamente alla tua compagnia assicurativa.`,
	EmailSuperbillReceiptFailure: 'Impossibile inviare la ricevuta Superbill',
	EmailSuperbillReceiptSubject: '{providerName} ha inviato una dichiarazione di ricezione del rimborso',
	EmailSuperbillReceiptSuccess: 'Ricevuta Superbill inviata con successo',
	EmailVerificationDescription: 'Stiamo <span>verificando</span> il tuo account ora',
	EmailVerificationNotification: 'Una email de verificación ha sido enviada a {email}',
	EmailVerificationSuccess: 'Il tuo indirizzo email è stato modificato correttamente in {email}',
	Emails: 'E-mail',
	EmergencyContact: 'Contatto di emergenza',
	EmployeesIdentificationNumber: 'Numero identificativo dei dipendenti',
	EmploymentStatus: 'Stato occupazionale',
	EmptyAgendaViewDescription: 'Nessun evento da visualizzare.<mark> Crea un appuntamento adesso</mark>',
	EmptyBin: 'Bidone vuoto',
	EmptyBinConfirmationDescription:
		'Vuoto cestino eliminerà tutte le <strong>{total} conversazioni</strong> in Eliminati. Questa azione non può essere annullata.',
	EmptyBinConfirmationTitle: 'Elimina le conversazioni per sempre',
	EmptyTrash: 'Svuota cestino',
	Enable: 'Abilitare',
	EnableCustomServiceAvailability: 'Abilita la disponibilità del servizio',
	EnableCustomServiceAvailabilityDescription:
		'Ad esempio, gli appuntamenti iniziali possono essere prenotati solo tutti i giorni dalle 9 alle 10.',
	EndCall: 'Termina chiamata',
	EndCallConfirmationForCreator: `Tu porrai fine a tutto questo per tutti perché sei tu l'iniziatore della chiamata.`,
	EndCallConfirmationHasActiveAttendees:
		'Stai per terminare la chiamata ma il/i cliente/i si è/sono già unito/i. Vuoi unirti anche tu?',
	EndCallForAll: 'Termina la chiamata per tutti',
	EndDate: 'Data di fine',
	EndDictation: 'Termina dettatura',
	EndOfLine: 'Non ci sono più appuntamenti',
	EndSession: 'Termina sessione',
	EndTranscription: 'Fine trascrizione',
	Ends: 'Termina',
	EndsOnDate: 'Termina il {date}',
	Enrol: 'Iscriviti',
	EnrollmentRejectedSubject: 'La tua iscrizione con {payerName} è stata rifiutata',
	Enrolment: 'Assunzione',
	Enrolments: 'Iscrizioni',
	EnrolmentsDescription: 'Visualizza e gestisci le iscrizioni dei fornitori con la compagnia di assicurazione.',
	EnterAName: 'Inserisci un nome...',
	EnterFieldLabel: `Inserisci l'etichetta del campo...`,
	EnterPaymentDetailsDescription: `Il costo dell'abbonamento verrà modificato automaticamente quando aggiungi o rimuovi utenti.`,
	EnterSectionName: 'Inserisci il nome della sezione...',
	EnterSubscriptionPaymentDetails: 'Inserisci i dettagli del pagamento',
	EnvironmentalScientist: 'Scienziato ambientale',
	Epidemiologist: 'Epidemiologo',
	Eraser: 'Gomma per cancellare',
	Error: 'Errore',
	ErrorBoundaryAction: 'Ricarica la pagina',
	ErrorBoundaryDescription: 'Aggiorna la pagina e riprova.',
	ErrorBoundaryTitle: 'Ops! Qualcosa è andato storto',
	ErrorCallNotFound: `Impossibile trovare la chiamata. Potrebbe essere scaduta o il creatore l'ha terminata.`,
	ErrorCannotAccessCallUninvitedCode: 'Spiacenti, sembra che tu non sia stato invitato a questa chiamata.',
	ErrorFileUploadCustomMaxFileCount: 'Impossibile caricare più di {count} file contemporaneamente',
	ErrorFileUploadCustomMaxFileSize: 'Dimensione del file non può superare {mb} MB',
	ErrorFileUploadInvalidFileType:
		'Tipo di file non valido che potrebbe contenere potenziali virus e software dannosi',
	ErrorFileUploadMaxFileCount: 'Non è possibile caricare più di 150 file contemporaneamente',
	ErrorFileUploadMaxFileSize: 'La dimensione del file non può superare i 100 MB',
	ErrorFileUploadNoFileSelected: 'Seleziona i file da caricare',
	ErrorInvalidNationalProviderId: 'Il Codice Fiscale del Fornitore Nazionale fornito non è valido',
	ErrorInvalidPayerId: `L'ID del pagatore fornito non è valido`,
	ErrorInvalidTaxNumber: 'Il numero di partita IVA fornito non è valido',
	ErrorInviteExistingProviderStaffCode: `Questo utente è già presente nell'area di lavoro.`,
	ErrorInviteStaffExistingUser: `Spiacenti, sembra che l'utente che hai aggiunto esista già nel nostro sistema.`,
	ErrorOnlySingleCallAllowed:
		'Puoi avere solo una chiamata alla volta. Termina la chiamata corrente per iniziarne una nuova.',
	ErrorPayerNotFound: 'Pagatore non trovato',
	ErrorProfilePhotoMaxFileSize: 'Caricamento non riuscito! Limite dimensione file raggiunto - 5 MB',
	ErrorRegisteredExistingUser: 'Spiacenti, sembra che tu sia già registrato.',
	ErrorUserSignInIncorrectCredentials: 'Email o password non valide. Riprova.',
	ErrorUserSigninGeneric: 'Spiacenti, qualcosa è andato storto.',
	ErrorUserSigninUserNotConfirmed: `Spiacenti, devi confermare il tuo account prima di effettuare l'accesso. Controlla la tua posta in arrivo per le istruzioni.`,
	Errors: 'Errori',
	EssentialPlanInclusionFive: 'Importazione del modello',
	EssentialPlanInclusionFour: '5 GB di spazio di archiviazione',
	EssentialPlanInclusionHeader: 'Tutto in Free  ',
	EssentialPlanInclusionOne: 'Promemoria automatici e personalizzati',
	EssentialPlanInclusionSix: 'Supporto prioritario',
	EssentialPlanInclusionThree: 'Video chat',
	EssentialPlanInclusionTwo: 'Sincronizzazione bidirezionale del calendario',
	EssentialSubscriptionPlanSubtitle: `Semplifica la tua pratica con l'essenziale`,
	EssentialSubscriptionPlanTitle: 'Essenziale',
	Esthetician: 'Estetista',
	Estheticians: 'Estetiste',
	EstimatedArrivalDate: 'Arrivo previsto {numberOfDaysFromNow}',
	Ethnicity: 'Etnia',
	Europe: 'Europa',
	EventColor: `Colore dell'incontro`,
	EventName: `Nome dell'evento`,
	EventType: 'Tipo di evento',
	Every: 'Ogni',
	Every2Weeks: 'Ogni 2 settimane',
	EveryoneInWorkspace: `Tutti nell'area di lavoro`,
	ExercisePhysiologist: `Fisiologo dell'esercizio fisico`,
	Existing: 'Esistente',
	ExistingClients: 'Clienti esistenti',
	ExistingFolders: 'Cartelle esistenti',
	ExpiredPromotionCode: 'Il codice promozionale è scaduto',
	ExpiredReferralDescription: 'Il referral è scaduto',
	ExpiredVerificationLink: 'Link di verifica scaduto',
	ExpiredVerificationLinkDescription: `Siamo spiacenti, ma il link di verifica su cui hai cliccato è scaduto. Ciò può accadere se hai atteso più di 24 ore per cliccare sul link o se hai già utilizzato il link per verificare il tuo indirizzo email.

 Richiedi un nuovo link di verifica per verificare il tuo indirizzo email.`,
	ExpiryDateRequired: 'La data di scadenza è obbligatoria',
	ExploreFeature: 'Cosa vorresti esplorare per primo?',
	ExploreOptions: 'Scegli una o più opzioni da esplorare...',
	Export: 'Esportare',
	ExportAppointments: 'Esportazione appuntamenti',
	ExportClaims: 'Esporta reclami',
	ExportClaimsFilename: 'Richieste {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Non è stato possibile scaricare il file a causa di un errore.',
	ExportClientsDownloadFailureSnackbarTitle: 'Download non riuscito',
	ExportClientsFailureSnackbarDescription:
		'Non è stato possibile esportare correttamente il file a causa di un errore.',
	ExportClientsFailureSnackbarTitle: 'Esportazione non riuscita',
	ExportClientsModalDescription: `Questo processo di esportazione dei dati potrebbe richiedere alcuni minuti, a seconda della quantità di dati esportati. Riceverai una notifica via email con un link quando sarà pronto per il download.

 Desideri procedere con l'esportazione dei dati del cliente?`,
	ExportClientsModalTitle: 'Esportare i dati del cliente',
	ExportCms1500: 'Esportazione CMS1500',
	ExportContactFailedNotificationSubject: `L'esportazione dei dati non è riuscita`,
	ExportFailed: 'Esportazione non riuscita',
	ExportGuide: `Guida all'esportazione`,
	ExportInvoiceFileName: 'Transazioni {fromDate}-{toDate}.csv',
	ExportPayments: 'Esporta pagamenti',
	ExportPaymentsFilename: 'Pagamenti {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Il tuo documento è pronto per essere scaricato.',
	ExportPrintWaitMessage: 'Preparazione del documento. Attendi...',
	ExportTextOnly: 'Esporta solo testo',
	ExportTransactions: 'Transazioni di esportazione',
	Exporting: 'Esportazione',
	ExportingData: 'Esportazione dei dati',
	ExtendedFamilyMember: 'Membro della famiglia allargata',
	External: 'Esterno',
	ExternalEventInfoBanner:
		'Questo appuntamento proviene da un calendario sincronizzato e potrebbe mancare di alcuni elementi.',
	ExtraLarge: 'Molto grande',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Fallito',
	FailedToJoinTheMeeting: 'Impossibile partecipare alla riunione.',
	FallbackPageDescription: `Sembra che questa pagina non esista, potrebbe essere necessario {refreshButton} questa pagina per ottenere le modifiche più recenti.
In caso contrario, contatta l'assistenza Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'rinfrescare',
	FallbackPageTitle: 'Oops...',
	FamilyPlanningService: 'Servizio di pianificazione familiare',
	FashionDesigner: 'Stilista di moda',
	FastTrackInvoicingAndBilling: 'Accelera la fatturazione e la fatturazione',
	Father: 'Padre',
	FatherInLaw: 'Suocero',
	Favorite: 'Preferito',
	FeatureBannerCalendarTile1ActionLabel: 'Prenotazione online • 2 minuti',
	FeatureBannerCalendarTile1Description: `Invia semplicemente un'e-mail, un SMS o aggiungi la disponibilità al tuo sito web`,
	FeatureBannerCalendarTile1Title: 'Consenti ai tuoi clienti di prenotare online',
	FeatureBannerCalendarTile2ActionLabel: 'Promemoria automatici • 2 minuti',
	FeatureBannerCalendarTile2Description: 'Aumenta la presenza dei clienti con promemoria automatici',
	FeatureBannerCalendarTile2Title: 'Ridurre le mancate presentazioni',
	FeatureBannerCalendarTile3Title: 'Pianificazione e flusso di lavoro',
	FeatureBannerCalendarTitle: 'Semplifica la pianificazione',
	FeatureBannerCallsTile1ActionLabel: 'Avvia la chiamata di telemedicina',
	FeatureBannerCallsTile1Description: 'Accesso client con un semplice link. Nessun login, password o seccatura',
	FeatureBannerCallsTile1Title: 'Avvia una videochiamata da qualsiasi luogo',
	FeatureBannerCallsTile2ActionLabel: 'Connetti app • 4 minuti',
	FeatureBannerCallsTile2Description: 'Connetti senza problemi altri fornitori di telemedicina preferiti',
	FeatureBannerCallsTile2Title: 'Collega le tue app di telemedicina',
	FeatureBannerCallsTile3Title: 'Chiamate',
	FeatureBannerCallsTitle: 'Connettiti con i clienti: ovunque e in qualsiasi momento',
	FeatureBannerClientsTile1ActionLabel: 'Importa ora • 2 minuti',
	FeatureBannerClientsTile1Description:
		'Inizia subito con il nostro strumento di importazione automatica dei clienti',
	FeatureBannerClientsTile1Title: 'Hai molti clienti?',
	FeatureBannerClientsTile2ActionLabel: `Personalizza l'assunzione • 2 minuti`,
	FeatureBannerClientsTile2Description: `Eliminare la documentazione di assunzione e migliorare l'esperienza dei clienti`,
	FeatureBannerClientsTile2Title: 'Elimina la carta',
	FeatureBannerClientsTile3Title: 'Portale clienti',
	FeatureBannerClientsTitle: 'Tutto inizia dai tuoi clienti',
	FeatureBannerHeader: 'Dalla Comunità, per la Comunità!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatizza i pagamenti • 2 minuti',
	FeatureBannerInvoicesTile1Description: 'Evita conversazioni imbarazzanti con i pagamenti automatici',
	FeatureBannerInvoicesTile1Title: 'Ricevi i pagamenti 2 volte più velocemente',
	FeatureBannerInvoicesTile2ActionLabel: 'Traccia il flusso di cassa • 2 minuti',
	FeatureBannerInvoicesTile2Description: 'Riduci le fatture non pagate e tieni sotto controllo il tuo reddito',
	FeatureBannerInvoicesTile2Title: 'Tieni traccia dei tuoi guadagni, senza problemi',
	FeatureBannerInvoicesTile3Title: 'Fatturazione e pagamenti',
	FeatureBannerInvoicesTitle: 'Una cosa in meno di cui preoccuparsi',
	FeatureBannerSubheader:
		'Modelli Carepatron realizzati dal nostro team e dalla nostra community. Prova nuove risorse o condividi le tue!',
	FeatureBannerTeamTile1ActionLabel: 'Invita ora',
	FeatureBannerTeamTile1Description: 'Invita i membri del team al tuo account e semplifica la collaborazione',
	FeatureBannerTeamTile1Title: 'Riunisci la tua squadra',
	FeatureBannerTeamTile2ActionLabel: 'Imposta disponibilità • 2 minuti',
	FeatureBannerTeamTile2Description: 'Gestisci la disponibilità dei tuoi team per evitare doppie prenotazioni',
	FeatureBannerTeamTile2Title: 'Imposta la tua disponibilità',
	FeatureBannerTeamTile3ActionLabel: 'Imposta permessi • 2 minuti',
	FeatureBannerTeamTile3Description: `Controllare l'accesso ai dati sensibili e agli strumenti per la conformità`,
	FeatureBannerTeamTile3Title: 'Personalizzare permessi e accessi',
	FeatureBannerTeamTitle: 'Niente di grande si realizza da soli',
	FeatureBannerTemplatesTile1ActionLabel: 'Esplora la libreria • 2 minuti',
	FeatureBannerTemplatesTile1Description: 'Scegli tra una straordinaria libreria di risorse personalizzabili ',
	FeatureBannerTemplatesTile1Title: 'Riduci il tuo carico di lavoro',
	FeatureBannerTemplatesTile2ActionLabel: 'Invia ora • 2 minuti',
	FeatureBannerTemplatesTile2Description: 'Invia bellissimi modelli ai clienti per il completamento',
	FeatureBannerTemplatesTile2Title: 'Rendi divertente la documentazione',
	FeatureBannerTemplatesTile3Title: 'Modelli',
	FeatureBannerTemplatesTitle: 'Modelli per qualsiasi cosa',
	FeatureLimitBannerDescription: `Fai l'upgrade ora per continuare a creare e gestire {featureName} senza interruzioni e ottenere il massimo da Carepatron!`,
	FeatureLimitBannerTitle: 'Sei {percentage}% del limite per il tuo {featureName}',
	FeatureRequiresUpgrade: 'Questa funzionalità richiede un aggiornamento',
	Fee: 'Tassa',
	Female: 'Femmina',
	FieldLabelTooltip: '{isHidden, select, true {Mostra} other {Nascondi}} etichetta campo',
	FieldName: 'Nome del campo',
	FieldOptionsFirstPart: 'Prima parola',
	FieldOptionsMiddlePart: 'Parole di mezzo',
	FieldOptionsSecondPart: 'Ultima parola',
	FieldOptionsWholeField: 'Campo intero',
	FieldType: 'Tipo di campo',
	Fields: 'Campi',
	File: 'File',
	FileDownloaded: '<strong>{fileName}</strong> scaricato',
	FileInvalidType: 'File non supportato.',
	FileNotFound: 'File non trovato',
	FileNotFoundDescription: 'Il file che stai cercando non è disponibile o è stato eliminato',
	FileTags: 'Tag dei file',
	FileTagsHelper: 'I tag verranno applicati a tutti i file',
	FileTooLarge: 'File troppo grande.',
	FileTooSmall: 'File troppo piccolo.',
	FileUploadComplete: 'Completare',
	FileUploadFailed: 'Fallito',
	FileUploadInProgress: 'Caricamento',
	FileUploadedNotificationSubject: '{actorProfileName} ha subido un archivo',
	Files: 'File',
	FillOut: 'Compilare',
	Filter: 'Filtro',
	FilterBy: 'Filtra per',
	FilterByAmount: 'Filtra per importo',
	FilterByClient: 'Filtra per cliente',
	FilterByLocation: 'Filtra per posizione',
	FilterByService: 'Filtra per servizio',
	FilterByStatus: 'Filtra per stato',
	FilterByTags: 'Filtra per tag',
	FilterByTeam: 'Filtra per squadra',
	Filters: 'Filtri',
	FiltersAppliedToView: 'Filtri applicati alla vista',
	FinalAppointment: 'Appuntamento finale',
	FinalizeImport: `Finalizza l'importazione`,
	FinancialAnalyst: 'Analista finanziario',
	Finish: 'Fine',
	Firefighter: 'Pompiere',
	FirstName: 'Nome di battesimo',
	FirstNameLastInitial: 'Nome, iniziale del cognome',
	FirstPerson: '1a persona',
	FolderName: 'Nome cartella',
	Folders: 'Cartelle',
	FontFamily: 'Famiglia di caratteri',
	ForClients: 'Per i clienti',
	ForClientsDetails: 'Ricevo cure o servizi sanitari',
	ForPractitioners: 'Per i professionisti',
	ForPractitionersDetails: 'Gestisci e fai crescere la tua attività',
	ForgotPasswordConfirmAccessCode: 'Codice di conferma',
	ForgotPasswordConfirmNewPassword: 'Nuova password',
	ForgotPasswordConfirmPageDescription:
		'Inserisci il tuo indirizzo email, una nuova password e il codice di conferma che ti abbiamo appena inviato.',
	ForgotPasswordConfirmPageTitle: 'Reimposta password',
	ForgotPasswordPageButton: 'Invia link di reimpostazione',
	ForgotPasswordPageDescription:
		'Inserisci il tuo indirizzo email e ti invieremo un link per reimpostare la password.',
	ForgotPasswordPageTitle: 'Password dimenticata',
	ForgotPasswordSuccessPageDescription: 'Controlla la tua posta in arrivo per trovare il link per il ripristino.',
	ForgotPasswordSuccessPageTitle: 'Link di reimpostazione inviato!',
	Form: 'Modulo',
	FormAnswersSentToEmailNotification: 'Abbiamo inviato una copia delle tue risposte a',
	FormBlocks: 'Blocchi di forma',
	FormFieldAddOption: 'Aggiungi opzione',
	FormFieldAddOtherOption: 'Aggiungi "altro"',
	FormFieldOptionPlaceholder: 'Opzione {index}',
	FormStructures: 'Strutture di forma',
	Format: 'Formato',
	FormatLinkButtonColor: 'Colore del pulsante',
	Forms: 'Forme',
	FormsAndAgreementsValidationMessage:
		'Per proseguire con la procedura di ammissione è necessario compilare tutti i moduli e gli accordi.',
	FormsCategoryDescription: `Per la raccolta e l'organizzazione dei dettagli dei pazienti`,
	Frankfurt: 'Francoforte',
	Free: 'Gratuito',
	FreePlanInclusionFive: 'Fatturazione automatica ',
	FreePlanInclusionFour: 'Portale clienti',
	FreePlanInclusionHeader: 'Inizia con',
	FreePlanInclusionOne: 'Clienti illimitati',
	FreePlanInclusionSix: 'Supporto in tempo reale',
	FreePlanInclusionThree: '1 GB di spazio di archiviazione',
	FreePlanInclusionTwo: 'Telemedicina',
	FreeSubscriptionPlanSubtitle: 'Gratuito per tutti',
	FreeSubscriptionPlanTitle: 'Gratuito',
	Friday: 'Venerdì',
	From: 'Da',
	FullName: 'Nome e cognome',
	FunctionalMedicineOrNaturopath: 'Medicina funzionale o naturopata',
	FuturePaymentsAuthoriseProvider: 'Consenti a {provider} di utilizzare il pagamento salvato in futuro',
	FuturePaymentsSavePaymentMethod: 'Salva {paymentMethod} per i pagamenti futuri',
	GST: 'IVA',
	Gender: 'Genere',
	GeneralAvailability: 'Disponibilità generale',
	GeneralAvailabilityDescription:
		'Imposta quando sei regolarmente disponibile. I clienti potranno prenotare i tuoi servizi solo durante gli orari disponibili.',
	GeneralAvailabilityDescription2:
		'Crea programmi in base alla tua disponibilità e ai servizi desiderati in orari specifici per determinare la disponibilità delle tue prenotazioni online.',
	GeneralAvailabilityInfo: 'Le tue ore disponibili determineranno la disponibilità della tua prenotazione online',
	GeneralAvailabilityInfo2:
		'I servizi che offrono eventi di gruppo dovrebbero utilizzare un nuovo programma per ridurre le ore disponibili per le prenotazioni online da parte dei clienti.',
	GeneralHoursPlural: '{count} {count, plural, one {ora} other {ore}}',
	GeneralPractitioner: 'Medico di medicina generale',
	GeneralPractitioners: 'Medici di medicina generale',
	GeneralServiceAvailabilityInfo:
		'Questa pianificazione sovrascriverà il comportamento dei membri del team assegnati',
	Generate: 'Generare',
	GenerateBillingItemsBannerContent:
		'Per gli appuntamenti ricorrenti le voci di fatturazione non vengono create automaticamente.',
	GenerateItems: 'Generare elementi',
	GenerateNote: 'Genera nota',
	GenerateNoteConfirmationModalDescription:
		'Cosa vorresti fare? Creare una nuova nota generata, aggiungerne una esistente o sostituirne il contenuto?',
	GenerateNoteFor: 'Genera nota per',
	GeneratingContent: 'Generazione di contenuti...',
	GeneratingNote: 'Generando tu nota...',
	GeneratingTranscript: 'Generazione della trascrizione',
	GeneratingTranscriptDescription: `L'elaborazione potrebbe richiedere alcuni minuti`,
	GeneratingYourTranscript: 'Generazione della trascrizione',
	GenericErrorDescription: '{module} non è stato possibile caricarlo. Riprova più tardi.',
	GenericErrorTitle: 'Si è verificato un errore imprevisto',
	GenericFailureSnackbar: 'Spiacenti, si è verificato un imprevisto. Aggiorna la pagina e riprova.',
	GenericSavedSuccessSnackbar: 'Successo! Modifiche salvate',
	GeneticCounselor: 'Consulente Genetico',
	Gerontologist: 'Gerontologo',
	Get50PercentOff: 'Ottieni il 50% di sconto!',
	GetHelp: 'Ottieni aiuto',
	GetStarted: 'Iniziare',
	GettingStartedAppointmentTypes: 'Crea tipi di appuntamento',
	GettingStartedAppointmentTypesDescription:
		'Semplifica la pianificazione e la fatturazione personalizzando i tuoi servizi, prezzi e codici di fatturazione',
	GettingStartedAppointmentTypesTitle: 'Programma ',
	GettingStartedClients: 'Aggiungi i tuoi clienti',
	GettingStartedClientsDescription: 'Inizia a collaborare con i clienti per appuntamenti, note e pagamenti futuri',
	GettingStartedClientsTitle: 'Tutto inizia con i clienti',
	GettingStartedCreateClient: 'Crea cliente',
	GettingStartedImportClients: 'Importare i clienti',
	GettingStartedInvoices: 'Fattura come un professionista',
	GettingStartedInvoicesDescription: `Creare fatture professionali è semplice.
 Aggiungi il tuo logo, la tua posizione e le condizioni di pagamento`,
	GettingStartedInvoicesTitle: 'Dai il meglio di te',
	GettingStartedMobileApp: `Ottieni l'app mobile`,
	GettingStartedMobileAppDescription:
		'Puoi scaricare Carepatron sul tuo dispositivo iOS, Android o desktop per un facile accesso in movimento',
	GettingStartedMobileAppTitle: 'Lavora da qualsiasi luogo',
	GettingStartedNavItem: 'Iniziare',
	GettingStartedPageTitle: 'Introduzione a Carepatron',
	GettingStartedPayments: 'Accetta pagamenti online',
	GettingStartedPaymentsDescription: `Ricevi i pagamenti più velocemente consentendo ai tuoi clienti di pagare online.
 Visualizza tutte le tue fatture e i tuoi pagamenti in un unico posto`,
	GettingStartedPaymentsTitle: 'Rendi i pagamenti un gioco da ragazzi',
	GettingStartedSaveBranding: 'Salva il marchio',
	GettingStartedSyncCalendars: 'Sincronizza altri calendari',
	GettingStartedSyncCalendarsDescription:
		'Carepatron controlla il tuo calendario per eventuali conflitti, quindi gli appuntamenti vengono programmati solo quando sei disponibile',
	GettingStartedSyncCalendarsTitle: 'Rimani sempre aggiornato',
	GettingStartedVideo: 'Guarda un video introduttivo',
	GettingStartedVideoDescription: 'I primi spazi di lavoro sanitari all-in-one per piccoli team e i loro clienti',
	GettingStartedVideoTitle: 'Benvenuti a Carepatron',
	GetttingStartedGetMobileDownload: `Scarica l'app`,
	GetttingStartedGetMobileNoDownload:
		'Non compatibile con questo browser. Se stai usando iPhone o iPad, apri questa pagina in Safari. Altrimenti, prova ad aprirla in Chrome.',
	Glossary: 'Glossario',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail consente di inviare solo 500 messaggi al giorno dal tuo account. Alcuni messaggi potrebbero non funzionare. Vuoi continuare?',
	GoToAppointment: `Vai all'appuntamento`,
	GoToApps: 'Vai alle app',
	GoToAvailability: 'Vai alla disponibilità',
	GoToClientList: `Vai all'elenco dei clienti`,
	GoToClientRecord: 'Vai alla scheda cliente',
	GoToClientSettings: 'Vai alle impostazioni del client ora',
	GoToInvoiceTemplates: 'Vai ai modelli di fattura',
	GoToNotificationSettings: 'Vai alle impostazioni di notifica',
	GoToPaymentSettings: 'Vai alle impostazioni di pagamento',
	Google: 'Google',
	GoogleCalendar: 'Calendario di Google',
	GoogleColor: 'Colore del calendario di Google',
	GoogleMeet: 'Incontro di Google',
	GoogleTagManagerContainerId: 'ID contenitore di Google Tag Manager',
	GotIt: 'Fatto!',
	Goto: 'Vai a',
	Granddaughter: 'Nipotina',
	Grandfather: 'Nonno',
	Grandmother: 'Nonna',
	Grandparent: 'Nonno',
	Grandson: 'Nipote',
	GrantPortalAccess: `Concedi l'accesso al portale`,
	GraphicDesigner: 'Grafico Progettista',
	Grid: 'Griglia',
	GridView: 'Visualizzazione griglia',
	Group: 'Gruppo',
	GroupBy: 'Raggruppa per',
	GroupEvent: 'Evento di gruppo',
	GroupEventHelper: 'Imposta un limite di partecipanti al servizio',
	GroupFilterLabel: 'Tutti {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'ID gruppo',
	GroupInputFieldsFormPrimaryText: 'Campi di input di gruppo',
	GroupInputFieldsFormSecondaryText: 'Scegli o aggiungi campi personalizzati',
	GuideTo: 'Guida a {value}',
	GuideToImproveVideoQuality: 'Guida per migliorare la qualità video',
	GuideToManagingPayers: 'Gestione dei pagatori',
	GuideToSubscriptionsBilling: 'Guida alla fatturazione degli abbonamenti',
	GuideToTroubleshooting: 'Guida alla risoluzione dei problemi',
	Guidelines: 'Linee guida',
	GuidelinesCategoryDescription: 'Per guidare il processo decisionale clinico',
	HST: 'IVA',
	HairStylist: 'Parrucchiere',
	HaveBeenWaiting: 'Hai aspettato a lungo',
	HeHim: 'Lui/Lui',
	HeaderAccountSettings: 'Profilo',
	HeaderCalendar: 'Calendario',
	HeaderCalls: 'Chiamate',
	HeaderClientAppAccountSettings: `Impostazioni dell'account`,
	HeaderClientAppCalls: 'Chiamate',
	HeaderClientAppMyDocumentation: 'Documentazione',
	HeaderClientAppMyRelationships: 'Le mie relazioni',
	HeaderClients: 'Clienti',
	HeaderHelp: 'Aiuto',
	HeaderMoreOptions: 'Altre opzioni',
	HeaderStaff: 'Personale',
	HealthCoach: 'Coach della salute',
	HealthCoaches: 'Coach della salute',
	HealthEducator: 'Educatore sanitario',
	HealthInformationTechnician: 'Tecnico delle informazioni sanitarie',
	HealthPolicyExpert: 'Esperto di politica sanitaria',
	HealthServicesAdministrator: 'Amministratore dei servizi sanitari',
	HelpArticles: 'Articoli di aiuto',
	HiddenColumns: 'Colonne nascoste',
	HiddenFields: 'Campi nascosti',
	HiddenSections: 'Sezioni nascoste',
	HiddenSectionsAndFields: 'Sezioni/campi nascosti',
	HideColumn: 'Nascondi colonna',
	HideColumnButton: 'Nascondi colonna {value} pulsante',
	HideDetails: 'Nascondi i dettagli',
	HideField: 'Nascondi campo',
	HideFullAddress: 'Nascondere',
	HideMenu: 'Nascondi il menu',
	HideMergeSummarySidebar: 'Nascondi riepilogo fusione',
	HideSection: 'Nascondi sezione',
	HideYourView: 'Nascondi la tua vista',
	Highlight: 'Evidenzia colore',
	Highlighter: 'Evidenziatore',
	History: 'Storia',
	HistoryItemFooter: '{actors, select, undefined {{date} alle {time}} other {Di {actors} • {date} alle {time}}}',
	HistorySidePanelEmptyState: 'Nessun record storico trovato',
	HistoryTitle: 'Registro delle attività',
	HolisticHealthPractitioner: 'Operatore sanitario olistico',
	HomeCaregiver: 'Assistente domiciliare',
	HomeHealthAide: 'Assistente sanitario domiciliare',
	HomelessShelter: 'Rifugio per senzatetto',
	HourAbbreviation: '{count} {count, plural, one {ora} other {ore}}',
	Hourly: 'Orario',
	HoursPlural: '{età, plural, one {# ora} other {# ore}}',
	HowCanWeImprove: 'Come possiamo migliorare questo?',
	HowCanWeImproveResponse: 'Come possiamo migliorare questa risposta?',
	HowDidWeDo: 'Come siamo andati?',
	HowDoesReferralWork: 'Guida al programma di referral',
	HowToUseAiSummarise: 'Come utilizzare AI Summarize',
	HumanResourcesManager: 'Responsabile delle risorse umane',
	Husband: 'Marito',
	Hypnotherapist: 'Ipnoterapeuta',
	IVA: 'IVA',
	IgnoreNotification: 'Ignora notifica',
	IgnoreOnce: 'Ignora una volta',
	IgnoreSender: 'Ignora mittente',
	IgnoreSenderDescription:
		'Le conversazioni future da questo mittente verranno automaticamente spostate in "Altro". Vuoi davvero ignorare questi mittenti?',
	IgnoreSenders: 'Ignora mittenti',
	IgnoreSendersSuccess: 'Ignorata indirizz di posta elettronica <mark>{indirizzi}</mark>',
	Ignored: 'Ignorato',
	Image: 'Immagine',
	Import: 'Importare',
	ImportActivity: 'Importa attività',
	ImportClientSuccessSnackbarDescription: 'Il tuo file è stato importato con successo',
	ImportClientSuccessSnackbarTitle: 'Importazione riuscita!',
	ImportClients: 'Importare i clienti',
	ImportClientsFailureSnackbarDescription: 'Il file non è stato importato correttamente a causa di un errore.',
	ImportClientsFailureSnackbarTitle: 'Importazione non riuscita!',
	ImportClientsGuide: `Guida all'importazione dei clienti`,
	ImportClientsInProgressSnackbarDescription:
		'Il completamento di questa operazione dovrebbe richiedere al massimo un minuto.',
	ImportClientsInProgressSnackbarTitle: 'Importare {fileName}',
	ImportClientsModalDescription: `Scegli da dove provengono i tuoi dati: un file sul tuo dispositivo, un servizio di terze parti o un'altra piattaforma software.`,
	ImportClientsModalFileUploadHelperText: 'Supporta {fileTypes}. Limite di dimensione {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: `Guida all'importazione dei dati dei clienti`,
	ImportClientsModalStep1Label: 'Scegli la fonte dei dati',
	ImportClientsModalStep2Label: 'Carica file',
	ImportClientsModalStep3Label: 'Campi di revisione',
	ImportClientsModalTitle: 'Importazione dei dati del cliente',
	ImportClientsPreviewClientsReadyForImport: `{count} {count, plural, one {cliente} other {clienti}} pronti per l'importazione`,
	ImportContactFailedNotificationSubject: `L'importazione dei dati non è riuscita`,
	ImportDataSourceSelectorLabel: 'Importa origine dati da',
	ImportDataSourceSelectorPlaceholder: `Cerca o scegli l'origine dati di importazione`,
	ImportExportButton: 'Importazione/Esportazione',
	ImportFailed: 'Importazione non riuscita',
	ImportFromAnotherPlatformTileDescription: `Scarica un'esportazione dei tuoi file client e caricali qui.`,
	ImportFromAnotherPlatformTileLabel: `Importa da un'altra piattaforma`,
	ImportGuide: `Guida all'importazione`,
	ImportInProgress: 'Importazione in corso',
	ImportProcessing: 'Importazione in corso...',
	ImportSpreadsheetDescription: `Puoi importare l'elenco dei tuoi clienti esistenti in Carepatron caricando un file di foglio di calcolo con dati tabulari, come .CSV, .XLS o .XLSX`,
	ImportSpreadsheetTitle: 'Importa il tuo file di foglio di calcolo',
	ImportTemplates: 'Importa modelli',
	Importing: 'Importazione',
	ImportingCalendarProductEvents: 'Importare eventi di {product}',
	ImportingData: 'Importazione dei dati',
	ImportingSpreadsheetDescription: 'Il completamento di questa operazione dovrebbe richiedere solo un minuto',
	ImportingSpreadsheetTitle: 'Importazione del foglio di calcolo',
	ImportsInProgress: 'Importazioni in corso',
	InPersonMeeting: 'Incontro di persona',
	InProgress: 'In corso',
	InTransit: 'In transito',
	InTransitTooltip:
		'Il saldo In Transit include tutti i pagamenti delle fatture pagate da Stripe al tuo conto bancario. Questi fondi solitamente impiegano 3-5 giorni per essere liquidati.',
	Inactive: 'Inattivo',
	InboundOrOutboundCalls: 'Chiamate in entrata o in uscita',
	Inbox: 'Posta in arrivo',
	InboxAccessRestricted: 'Accesso limitato. Contattare il proprietario della posta in arrivo per i permessi.',
	InboxAccountAlreadyConnected: 'Il canale a cui hai tentato di connetterti è già connesso a Carepatron',
	InboxAddAttachments: 'Aggiungi allegati',
	InboxAreYouSureDeleteMessage: 'Sei sicuro di voler eliminare questo messaggio?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Conversación cerrada correctamente #} other {Conversaciones cerradas correctamente #}}',
	InboxBulkComposeModalTitle: 'Componi messaggio in blocco',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Conversazione eliminata correttamente #} other {Conversazioni eliminate correttamente #}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Conversación marcada como leída con éxito} other {Conversaciones marcadas como leídas con éxito}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Conversazione riaperta correttamente #} other {Conversazioni riaperte correttamente #}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {# conversazione contrassegnata come non letta} other {# conversazioni contrassegnate come non lette}}',
	InboxChatCreateGroup: 'Crea gruppo',
	InboxChatDeleteGroupModalDescription:
		'Sei sicuro di voler eliminare questo gruppo? Tutti i messaggi e gli allegati saranno eliminati.',
	InboxChatDeleteGroupModalTitle: 'Elimina gruppo',
	InboxChatDiscardDraft: 'Scarta bozza',
	InboxChatDragDropText: 'Trascina i file qui per caricarli',
	InboxChatGroupConversation: 'Conversazione di gruppo',
	InboxChatGroupCreateModalDescription:
		'Crea un nuovo gruppo per messaggiare e collaborare con il tuo team, i tuoi clienti o la tua community.',
	InboxChatGroupCreateModalTitle: 'Crea gruppo',
	InboxChatGroupMembers: 'Membri del gruppo',
	InboxChatGroupModalGroupNameFieldLabel: 'Nome del gruppo',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'E.g. assistenza clienti, amministratore',
	InboxChatGroupModalGroupNameFieldRequired: 'Questo campo è obbligatorio',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Almeno un membro richiesto',
	InboxChatGroupModalMembersFieldLabel: 'Scegli i membri del gruppo',
	InboxChatGroupModalMembersFieldPlaceholder: 'Scegli membri',
	InboxChatGroupUpdateModalTitle: 'Gestisci gruppo',
	InboxChatLeaveGroup: 'Esci dal gruppo',
	InboxChatLeaveGroupModalDescription:
		'Sei sicuro di voler lasciare questo gruppo? Non riceverai più messaggi o aggiornamenti.',
	InboxChatLeaveGroupModalTitle: 'Lascia il gruppo',
	InboxChatLeftGroupMessage: 'Messaggio di gruppo a sinistra',
	InboxChatManageGroup: 'Gestisci gruppo',
	InboxChatSearchParticipants: 'Scegli i destinatari',
	InboxCloseConversationSuccess: 'Conversazione chiusa con successo',
	InboxCompose: 'Comporre',
	InboxComposeBulk: 'Messaggio in blocco',
	InboxComposeCarepatronChat: 'Messaggero',
	InboxComposeChat: 'Componi chat',
	InboxComposeDisabledNoConnection: 'Collega un account di posta elettronica per inviare messaggi',
	InboxComposeDisabledNoPermissionTooltip: `Non hai l'autorizzazione per inviare messaggi da questa posta in arrivo`,
	InboxComposeEmail: 'Componi email',
	InboxComposeMessageFrom: 'Da',
	InboxComposeMessageRecipientBcc: 'Ccn',
	InboxComposeMessageRecipientCc: 'Per conoscenza',
	InboxComposeMessageRecipientTo: 'A',
	InboxComposeMessageSubject: 'Soggetto:',
	InboxConnectAccountButton: 'Collega la tua email',
	InboxConnectedDescription: 'La tua casella di posta non ha comunicazioni',
	InboxConnectedHeading: 'Le tue conversazioni appariranno qui non appena inizierai a scambiare comunicazioni',
	InboxConnectedHeadingClientView: 'Semplifica le comunicazioni con i tuoi clienti',
	InboxCreateFirstInboxButton: 'Crea la tua prima casella di posta',
	InboxCreationSuccess: 'Posta in arrivo creata con successo',
	InboxDeleteAttachment: 'Elimina allegato',
	InboxDeleteConversationSuccess: 'Conversazione eliminata con successo',
	InboxDeleteMessage: 'Eliminare il messaggio?',
	InboxDirectMessage: 'Messaggio diretto',
	InboxEditDraft: 'Modifica bozza',
	InboxEmailComposeReplyEmail: 'Scrivi una risposta',
	InboxEmailDraft: 'Bozza',
	InboxEmailNotFound: 'Email non trovata',
	InboxEmailSubjectFieldInformation: `Modificando l'oggetto verrà creata una nuova email con thread.`,
	InboxEmptyArchiveDescription: 'Non è stata trovata alcuna conversazione archiviata',
	InboxEmptyBinDescription: 'Non è stata trovata alcuna conversazione eliminata',
	InboxEmptyBinHeading: 'Tutto a posto, niente da vedere qui',
	InboxEmptyBinSuccess: 'Conversazioni eliminate con successo',
	InboxEmptyCongratsHeading: 'Bel lavoro! Siediti e rilassati fino alla prossima conversazione',
	InboxEmptyDraftDescription: 'Non è stata trovata alcuna bozza di conversazione',
	InboxEmptyDraftHeading: 'Tutto a posto, niente da vedere qui',
	InboxEmptyOtherDescription: 'Non sono state trovate altre conversazioni',
	InboxEmptyScheduledHeading: `Tutto a posto, nessuna conversazione programmata per l'invio`,
	InboxEmptySentDescription: 'Nessuna conversazione inviata è stata trovata',
	InboxForward: 'Inoltrare',
	InboxGroupClientsLabel: 'Tutti i clienti',
	InboxGroupClientsOverviewLabel: 'Clienti',
	InboxGroupClientsSelectedItemPrefix: 'Cliente',
	InboxGroupStaffsLabel: 'Tutta la squadra',
	InboxGroupStaffsOverviewLabel: 'Squadra',
	InboxGroupStaffsSelectedItemPrefix: 'Squadra',
	InboxGroupStatusLabel: 'Tutti gli stati',
	InboxGroupStatusOverviewLabel: 'Invia a uno stato',
	InboxGroupStatusSelectedItemPrefix: 'Stato',
	InboxGroupTagsLabel: 'Tutti i tag',
	InboxGroupTagsOverviewLabel: 'Invia a un tag',
	InboxGroupTagsSelectedItemPrefix: 'Etichetta',
	InboxHideQuotedText: 'Nascondi il testo citato',
	InboxIgnoreConversationSuccess: 'Conversazione ignorata con successo',
	InboxMessageAllLabelRecipientsCount: 'Tutti i destinatari {label} ({count})',
	InboxMessageBodyPlaceholder: 'Aggiungi il tuo messaggio',
	InboxMessageDeleted: 'Messaggio cancellato',
	InboxMessageMarkedAsRead: 'Messaggio contrassegnato come letto',
	InboxMessageMarkedAsUnread: 'Messaggio contrassegnato come non letto',
	InboxMessageSentViaChat: '<strong>Inviato tramite chat</strong>  • {time} da {name}',
	InboxMessageShowMoreRecipients: '+{count} altro',
	InboxMessageWasDeleted: 'Questo messaggio è stato eliminato',
	InboxNoConnectionDescription: 'Collega il tuo account di posta elettronica o crea caselle di posta con più e-mail',
	InboxNoConnectionHeading: 'Integra le comunicazioni con i tuoi clienti',
	InboxNoDirectMessage: 'Nessun messaggio recente',
	InboxRecentConversations: 'Recenti',
	InboxReopenConversationSuccess: 'Conversazione riaperta con successo',
	InboxReply: 'Rispondere',
	InboxReplyAll: 'Rispondi a tutti',
	InboxRestoreConversationSuccess: 'Conversazione ripristinata con successo',
	InboxScheduleSendCancelSendSuccess: 'Invio programmato annullato e messaggio ripristinato come bozza',
	InboxScheduleSendMessageSuccessDescription: 'Inviato in programma per {date}',
	InboxScheduleSendMessageSuccessTitle: 'Pianificazione invio',
	InboxSearchForConversations: 'Cerca "{query}"',
	InboxSendMessageSuccess: 'Conversazione inviata con successo',
	InboxSettings: 'Impostazioni della posta in arrivo',
	InboxSettingsAppsDesc:
		'Gestisci le app connesse per questa posta in arrivo condivisa: aggiungi o rimuovi connessioni in base alle tue esigenze.',
	InboxSettingsAppsNewConnectedApp: 'Nuova app connessa',
	InboxSettingsAppsTitle: 'App connesse',
	InboxSettingsDeleteAccountFailed: `Impossibile eliminare l'account della posta in arrivo`,
	InboxSettingsDeleteAccountSuccess: 'Account di posta in arrivo eliminato con successo',
	InboxSettingsDeleteAccountWarning: `L'eliminazione di {email} lo disconnetterà dalla casella di posta {inboxName} e impedirà la sincronizzazione dei messaggi.`,
	InboxSettingsDeleteInboxFailed: 'Impossibile eliminare la posta in arrivo',
	InboxSettingsDeleteInboxSuccess: 'Posta in arrivo eliminata con successo',
	InboxSettingsDeleteInboxWarning:
		'Eliminare {inboxName} disconnetterà tutti i canali collegati ed eliminerà tutti i messaggi associati a questa casella di posta. 		Questa azione è permanente e non può essere annullata.',
	InboxSettingsDetailsDesc:
		'Posta in arrivo per le comunicazioni che consente al tuo team di gestire in modo efficiente i messaggi dei clienti.',
	InboxSettingsDetailsTitle: 'Dettagli della posta in arrivo',
	InboxSettingsEmailSignatureLabel: 'Firma e-mail predefinita',
	InboxSettingsReplyFormatDesc: `Imposta l'indirizzo di risposta predefinito e la firma e-mail in modo che vengano visualizzati sempre, indipendentemente da chi invia l'e-mail.`,
	InboxSettingsReplyFormatTitle: 'Formato di risposta',
	InboxSettingsSendFromLabel: 'Imposta una risposta predefinita da ',
	InboxSettingsStaffDesc: `Gestisci l'accesso dei membri del team a questa casella di posta condivisa per una collaborazione fluida.`,
	InboxSettingsStaffTitle: 'Assegna i membri del team',
	InboxSettingsUpdateInboxDetailsFailed: 'Impossibile aggiornare i dettagli della posta in arrivo',
	InboxSettingsUpdateInboxDetailsSuccess: 'Dettagli della posta in arrivo aggiornati con successo',
	InboxSettingsUpdateInboxStaffsFailed: 'Impossibile aggiornare i membri del team della posta in arrivo',
	InboxSettingsUpdateInboxStaffsSuccess: 'Membri del team della posta in arrivo aggiornati con successo',
	InboxSettingsUpdateReplyFormatFailed: 'Impossibile aggiornare il formato di risposta',
	InboxSettingsUpdateReplyFormatSuccess: 'Formato di risposta aggiornato con successo',
	InboxShowQuotedText: 'Mostra il testo citato',
	InboxStaffRoleAdminDescription: 'Visualizza, rispondi e gestisci le caselle di posta',
	InboxStaffRoleResponderDescription: 'Visualizza e rispondi',
	InboxStaffRoleViewerDescription: 'Visualizza solo',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Continua a modificare',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: `Sì, passa all'invio in blocco`,
	InboxSuggestMoveToBulkComposeMessageContent:
		'Hai scelto più di {count} destinatari. Vuoi inviarlo come email di massa?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Avvertimento',
	InboxSwitchToOtherInbox: `Passa a un'altra casella di posta`,
	InboxUndoSendMessageSuccess: 'Invio annullato',
	IncludeLineItems: 'Includi voci di riga',
	IncludeSalesTax: 'Imponibile',
	IncludesAiSmartPrompt: 'Include richieste intelligenti AI',
	Incomplete: 'Incompleto',
	IncreaseIndent: 'Aumenta rientro',
	IndianHealthServiceFreeStandingFacility: 'Struttura indipendente del Servizio Sanitario Indiano',
	IndianHealthServiceProviderFacility: 'Struttura basata sul fornitore del servizio sanitario indiano',
	Information: 'Informazioni',
	InitialAssessment: 'Valutazione iniziale',
	InitialSignupPageClientFamilyTitle: 'Cliente o familiare',
	InitialSignupPageProviderTitle: 'Salute ',
	InitialTreatment: 'Trattamento iniziale',
	Initials: 'Iniziali',
	InlineEmbed: 'Embed inline',
	InputPhraseToConfirm: 'Per confermare, digita {confirmationPhrase}.',
	Insert: 'Inserire',
	InsertTable: 'Inserisci tabella',
	InstallCarepatronOnYourIphone1: 'Installa Carepatron sul tuo iOS: tocca',
	InstallCarepatronOnYourIphone2: 'e quindi Aggiungi alla schermata Home',
	InsufficientCalendarScopesSnackbar:
		'Sincronizzazione non riuscita: consentire le autorizzazioni del calendario a Carepatron',
	InsufficientInboxScopesSnackbar: 'Sincronizzazione non riuscita: consentire le autorizzazioni e-mail a Carepatron',
	InsufficientScopeErrorCodeSnackbar:
		'Sincronizzazione non riuscita: consentire tutte le autorizzazioni a Carepatron',
	Insurance: 'Assicurazione',
	InsuranceAmount: `Importo dell'assicurazione`,
	InsuranceClaim: 'Richiesta di risarcimento assicurativo',
	InsuranceClaimAiChatPlaceholder: 'Chiedi informazioni sulla richiesta di indennizzo...',
	InsuranceClaimAiClaimNumber: 'Richiesta {number}',
	InsuranceClaimAiSubtitle: 'Fatture assicurative • Validazione delle richieste di risarcimento',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber} inviata a {payerNumber} {payerName} è stata rifiutata',
	InsuranceClaimErrorDescription:
		'La richiesta contiene errori segnalati dal pagatore o dalla clearing house. Si prega di rivedere i seguenti messaggi di errore e di reinviare la richiesta.',
	InsuranceClaimErrorGuideLink: 'Guida alle richieste di risarcimento',
	InsuranceClaimErrorTitle: `Errori nell'invio delle richieste`,
	InsuranceClaimNotFound: 'Richiesta di risarcimento assicurativo non trovata',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {È stato registrato un pagamento parziale di {paymentAmount}} other {È stato registrato un pagamento di {paymentAmount}}} per la richiesta di risarcimento {claimNumber} da {payerNumber} {payerName}',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber} inviato a {payerNumber} {payerName} è stato rifiutato',
	InsuranceClaims: 'Dichiarazioni di assicurazione',
	InsuranceInformation: 'Informazioni assicurative',
	InsurancePaid: 'Assicurazione pagata',
	InsurancePayer: 'Pagatore assicurativo',
	InsurancePayers: 'Pagatori assicurativi',
	InsurancePayersDescription: `Visualizza i pagatori che sono stati aggiunti al tuo account e gestisci l'iscrizione.`,
	InsurancePayment: 'Pagamento assicurativo',
	InsurancePoliciesDetailsSubtitle:
		'Aggiungere le informazioni assicurative del cliente per supportare le richieste di risarcimento.',
	InsurancePoliciesDetailsTitle: 'Dettagli delle politiche',
	InsurancePoliciesListSubtitle:
		'Aggiungere le informazioni assicurative del cliente per supportare le richieste di risarcimento.',
	InsurancePoliciesListTitle: 'Polizze assicurative',
	InsuranceSelfPay: 'Pagamento proprio',
	InsuranceType: 'Tipo di assicurazione',
	InsuranceUnpaid: 'Assicurazione non pagata',
	Intake: 'Assunzione',
	IntakeExpiredErrorCodeSnackbar: `Questa assunzione è scaduta. Contatta il tuo provider per inviare di nuovo un'altra assunzione.`,
	IntakeNotFoundErrorSnackbar: `Questa assunzione non è stata trovata. Contatta il tuo provider per inviare di nuovo un'altra assunzione.`,
	IntakeProcessLearnMoreInstructions: 'Guida per impostare i moduli di assunzione',
	IntakeTemplateSelectorPlaceholder: 'Scegli i moduli e gli accordi da inviare al tuo cliente per la compilazione',
	Integration: 'Integrazione',
	IntenseBlur: 'Sfoca intensamente lo sfondo',
	InteriorDesigner: `Architetto d'interni`,
	InternetBanking: 'Bonifico bancario',
	Interval: 'Intervallo',
	IntervalDays: 'Intervallo (giorni)',
	IntervalHours: 'Intervallo (ore)',
	Invalid: 'Non valido',
	InvalidDate: 'Data non valida',
	InvalidDateFormat: 'La data deve essere nel formato {format}',
	InvalidDisplayName: 'Nome visual não pode conter {value}',
	InvalidEmailFormat: 'Formato email non valido',
	InvalidFileType: 'Tipo di file non valido',
	InvalidGTMContainerId: 'Formato ID contenitore GTM non valido',
	InvalidPaymentMethodCode: 'Il metodo di pagamento selezionato non è valido. Scegline un altro.',
	InvalidPromotionCode: 'Il codice promozionale non è valido',
	InvalidReferralDescription: 'Utilizzo già Carepatron',
	InvalidStatementDescriptor: `Il descrittore di istruzione deve essere lungo tra 5 e 22 caratteri e contenere solo lettere, numeri, spazi e non deve includere <, >, \\, ', ", *`,
	InvalidToken: 'Token non valido',
	InvalidTotpSetupVerificationCode: 'Codice di verifica non valido.',
	InvalidURLErrorText: 'Questo deve essere un URL valido',
	InvalidZoomTokenErrorCodeSnackbar: 'Il token Zoom è scaduto. Riconnetti la tua app Zoom e riprova.',
	Invite: 'Invitare',
	InviteRelationships: 'Invita relazioni',
	InviteToPortal: 'Invita al portale',
	InviteToPortalModalDescription: `Verrà inviata un'e-mail di invito al tuo cliente per registrarsi a Carepatron.`,
	InviteToPortalModalTitle: 'Invita {name} al portal de Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Invita nuovo utente',
	Invited: 'Invitato',
	Invoice: 'Fattura',
	InvoiceColorPickerDescription: 'Tema colore da utilizzare nella fattura',
	InvoiceColorTheme: 'Tema colore fattura',
	InvoiceContactDeleted: 'Il contatto della fattura è stato eliminato e questa fattura non può essere aggiornata.',
	InvoiceDate: 'Data di emissione',
	InvoiceDetails: 'Dettagli della fattura',
	InvoiceFieldsPlaceholder: 'Cerca campi...',
	InvoiceFrom: 'Fattura {number} da {fromProvider}',
	InvoiceInvalidCredit: `Importo del credito non valido, l'importo del credito non può superare il totale della fattura`,
	InvoiceNotFoundDescription:
		'Contatta il tuo fornitore e chiedi maggiori informazioni o di inviare nuovamente la fattura.',
	InvoiceNotFoundTitle: 'Fattura non trovata',
	InvoiceNumber: 'Fattura #',
	InvoiceNumberFormat: 'Fattura #{number}',
	InvoiceNumberMustEndWithDigit: 'Il numero della fattura deve terminare con una cifra (0-9)',
	InvoicePageHeader: 'Fatture',
	InvoicePaidNotificationSubject: 'Fattura {invoiceNumber} pagata',
	InvoiceReminder: 'Promemoria fatture',
	InvoiceReminderSentence:
		'Invia promemoria {deliveryType} {interval} {unit} {beforeAfter} la data di scadenza della fattura',
	InvoiceReminderSettings: 'Impostazioni promemoria fattura',
	InvoiceReminderSettingsInfo: 'I promemoria si applicano solo alle fatture inviate su Carepatron',
	InvoiceReminders: 'Promemoria fatture',
	InvoiceRemindersInfo:
		'Imposta promemoria automatici per le date di scadenza delle fatture. I promemoria si applicano solo alle fatture inviate tramite Carepatron',
	InvoiceSettings: 'Impostazioni della fattura',
	InvoiceStatus: 'Stato della fattura',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, Stati Uniti',
	InvoiceTemplateDescriptionPlaceholder:
		'Aggiungi note, dettagli del bonifico bancario o termini e condizioni per pagamenti alternativi',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Lavoratore autonomo',
	InvoiceTemplateEthnicityPlaceholder: 'caucasico',
	InvoiceTemplateNotFoundDescription: 'Contatta il tuo fornitore e chiedi maggiori informazioni.',
	InvoiceTemplateNotFoundTitle: 'Modello di fattura non trovato',
	InvoiceTemplates: 'Modelli di fattura',
	InvoiceTemplatesDescription:
		'Personalizza i modelli di fattura in modo che riflettano il tuo marchio, soddisfino i requisiti normativi e soddisfino le preferenze dei clienti con i nostri modelli intuitivi.',
	InvoiceTheme: 'Tema della fattura',
	InvoiceTotal: 'Totale fattura',
	InvoiceUninvoicedAmounts: 'Fatturare importi non fatturati',
	InvoiceUpdateVersionMessage:
		'Per modificare questa fattura è necessaria la versione più recente. Ricarica Carepatron e riprova.',
	Invoices: '{count, plural, one {Fattura} other {Fatture}}',
	InvoicesEmptyStateDescription: 'Non sono state trovate fatture',
	InvoicingAndPayment: 'Fatturazione ',
	Ireland: 'Irlanda',
	IsA: 'è un',
	IsBetween: 'è tra',
	IsEqualTo: 'è uguale a',
	IsGreaterThan: 'è maggiore di',
	IsGreaterThanOrEqualTo: 'è maggiore o uguale a',
	IsLessThan: 'è inferiore a',
	IsLessThanOrEqualTo: 'è minore o uguale a',
	IssueCredit: 'Emettere credito',
	IssueCreditAdjustment: 'Emettere adeguamento del credito',
	IssueDate: 'Data di emissione',
	Italic: 'Corsivo',
	Items: 'Elementi',
	ItemsAndAdjustments: 'Elementi e modifiche',
	ItemsRemaining: '+{count} elementi rimanenti',
	JobTitle: 'Titolo di lavoro',
	Join: 'Giuntura',
	JoinCall: 'Partecipa alla chiamata',
	JoinNow: 'Iscriviti ora',
	JoinProduct: 'Unisciti a {product}',
	JoinVideoCall: 'Partecipa alla videochiamata',
	JoinWebinar: 'Partecipa al webinar',
	JoinWithVideoCall: 'Unisciti a {product}',
	Journalist: 'Giornalista',
	JustMe: 'Solo io',
	JustYou: 'Solo tu',
	Justify: 'Giustificare',
	KeepSeparate: 'Tenere separato',
	KeepSeparateSuccessMessage: 'Hai mantenuto correttamente registrazioni separate per {clientNames}',
	KeepWaiting: 'Continua ad aspettare',
	Label: 'Etichetta',
	LabelOptional: 'Etichetta (facoltativo)',
	LactationConsulting: `Consulenza per l'allattamento`,
	Language: 'Lingua',
	Large: 'Grande',
	LastDxCode: 'Ultimo codice DX',
	LastLoggedIn: 'Ultima accesso {date} alle {time}',
	LastMenstrualPeriod: 'Ultimo periodo mestruale',
	LastMonth: 'Il mese scorso',
	LastNDays: 'Ultimi {number} giorni',
	LastName: 'Cognome',
	LastNameFirstInitial: 'Cognome, iniziale del nome',
	LastWeek: 'La settimana scorsa',
	LastXRay: 'Ultima radiografia',
	LatestVisitOrConsultation: 'Ultima visita o consulenza',
	Lawyer: 'Avvocato',
	LearnMore: 'Saperne di più',
	LearnMoreTipsToGettingStarted: 'Scopri altri suggerimenti per iniziare',
	LearnToSetupInbox: `Guida per configurare l'account di posta in arrivo`,
	Leave: 'Partire',
	LeaveCall: 'Lascia la chiamata',
	LeftAlign: 'Allinea a sinistra',
	LegacyBillingItemsNotAvailable:
		'Fatture individuali per questo appuntamento non sono ancora disponibili. Puoi comunque fatturare normalmente.',
	LegacyBillingItemsNotAvailableTitle: 'Fatturazione legacy',
	LegalAndConsent: 'Legale e consenso',
	LegalConsentFormPrimaryText: 'Consenso legale',
	LegalConsentFormSecondaryText: 'Accetta o rifiuta le opzioni',
	LegalGuardian: 'Tutore legale',
	Letter: 'Lettera',
	LettersCategoryDescription: 'Per la creazione di corrispondenza clinica e amministrativa',
	Librarian: 'Bibliotecario',
	LicenseNumber: 'Numero di licenza',
	LifeCoach: 'Coach di vita',
	LifeCoaches: 'Coach di vita',
	Limited: 'Limitato',
	LineSpacing: 'Spaziatura tra righe e paragrafi',
	LinearScaleFormPrimaryText: 'Scala lineare',
	LinearScaleFormSecondaryText: 'Opzioni scala 1-10',
	Lineitems: 'Voci di riga',
	Link: 'Collegamento',
	LinkClientFormSearchClientLabel: 'Cerca un cliente',
	LinkClientModalTitle: 'Collegamento al cliente esistente',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> informazioni di contatto sono state aggiunte al record di <strong>{existingName}’s</strong>.',
	LinkClientSuccessTitle: 'Collegato con successo al contatto esistente',
	LinkForCallCopied: 'Link copiato!',
	LinkToAnExistingClient: 'Collegamento a un cliente esistente',
	LinkToClient: 'Collegamento al cliente',
	ListAndTracker: 'Elenco/Tracker',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} è in questa chiamata}
			other {{attendees} sono in questa chiamata}
		}`,
	ListStyles: 'Stili di elenco',
	ListsAndTrackersCategoryDescription: `Per l'organizzazione e il monitoraggio del lavoro`,
	LivingArrangements: 'Condizioni di vita',
	LoadMore: 'Carica altro',
	Loading: 'Caricamento...',
	LocalizationPanelDescription: 'Gestisci le impostazioni per la lingua e il fuso orario',
	LocalizationPanelTitle: 'Lingua e fuso orario',
	Location: 'Posizione',
	LocationDescription:
		'Imposta sedi fisiche e virtuali con indirizzi specifici, nomi di stanze e tipologie di spazi virtuali per semplificare la pianificazione di appuntamenti e videochiamate.',
	LocationNumber: 'Numero di posizione',
	LocationOfService: 'Posizione del servizio',
	LocationOfServiceRecommendedActionInfo:
		'Aggiungere una posizione specifica a questo servizio può influire sulla tua disponibilità.',
	LocationRemote: 'Remoto',
	LocationType: 'Tipo di posizione',
	Locations: 'Posizioni',
	Lock: 'Serratura',
	Locked: 'Bloccato',
	LockedNote: 'Nota bloccata',
	LogInToSaveOrAuthoriseCard: 'Accedi per salvare o autorizzare la carta',
	LogInToSaveOrAuthorisePayment: 'Accedi per salvare o autorizzare il pagamento',
	Login: 'Login',
	LoginButton: 'Registrazione',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Ha dimenticato la password',
	LoginPassword: 'Password',
	Logo: 'Logo',
	LogoutAreYouSure: 'Esci da questo dispositivo.',
	LogoutButton: 'disconnessione',
	London: 'Londra',
	LongTextAnswer: 'Risposta testo lungo',
	LongTextFormPrimaryText: 'Testo lungo',
	LongTextFormSecondaryText: 'Opzioni di stile paragrafo',
	Male: 'Maschio',
	Manage: 'Maneggio',
	ManageAllClientTags: 'Gestisci tutti i tag client',
	ManageAllNoteTags: 'Gestisci tutti i tag delle note',
	ManageAllTemplateTags: 'Gestisci tutti i tag del modello',
	ManageConnections: 'Gestisci connessioni',
	ManageConnectionsGmailDescription:
		'Gli altri membri del team non potranno vedere la tua posta Gmail sincronizzata.',
	ManageConnectionsGoogleCalendarDescription:
		'Gli altri membri del team non saranno in grado di vedere i tuoi calendari sincronizzati. Gli appuntamenti dei clienti possono essere aggiornati o eliminati solo da Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Per gestire le impostazioni di Sincronizzazione posta in arrivo, vai alla pagina Posta in arrivo.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Gli altri membri del team non saranno in grado di vedere i tuoi calendari sincronizzati. Gli appuntamenti dei clienti possono essere aggiornati o eliminati solo da Carepatron.',
	ManageConnectionsOutlookDescription:
		'Gli altri membri del team non potranno vedere il tuo Microsoft Outlook sincronizzato.',
	ManageInboxAccountButton: 'Nuova posta in arrivo',
	ManageInboxAccountEdit: 'Gestisci Posta in arrivo',
	ManageInboxAccountPanelTitle: 'Posta in arrivo',
	ManageInboxAssignTeamPlaceholder: `Scegli i membri del team per l'accesso alla posta in arrivo`,
	ManageInboxBasicInfoColor: 'Colore',
	ManageInboxBasicInfoDescription: 'Descrizione',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Per cosa utilizzerai questa casella di posta tu o il tuo team?',
	ManageInboxBasicInfoName: 'Nome della posta in arrivo',
	ManageInboxBasicInfoNamePlaceholder: 'Ad esempio assistenza clienti, amministrazione',
	ManageInboxConnectAppAlreadyConnectedError:
		'Il canale a cui hai tentato di connetterti è già connesso a Carepatron',
	ManageInboxConnectAppConnect: 'Collegare',
	ManageInboxConnectAppConnectedInfo: 'Connesso a un account',
	ManageInboxConnectAppContinue: 'Continuare',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Accedi con',
	ManageInboxConnectAppSubtitle:
		'Collega le tue app per inviare, ricevere e monitorare senza problemi tutte le tue comunicazioni in un unico posto centralizzato.',
	ManageInboxNewInboxTitle: 'Nuova posta in arrivo',
	ManagePlan: 'Gestisci piano',
	ManageProfile: 'Gestisci profilo',
	ManageReferralsModalDescription:
		'Aiutaci a diffondere la conoscenza della nostra piattaforma sanitaria e guadagna dei premi.',
	ManageReferralsModalTitle: 'Invita un amico e riceverai dei premi!',
	ManageStaffRelationshipsAddButton: 'Gestire le relazioni',
	ManageStaffRelationshipsEmptyStateText: 'Nessuna relazione aggiunta',
	ManageStaffRelationshipsModalDescription:
		'Selezionando i clienti verranno aggiunte nuove relazioni, mentre deselezionandoli verranno rimosse quelle esistenti.',
	ManageStaffRelationshipsModalTitle: 'Gestire le relazioni',
	ManageStatuses: 'Gestisci gli stati',
	ManageStatusesActiveStatusHelperText: 'È richiesto almeno uno stato attivo',
	ManageStatusesDescription:
		'Personalizza le etichette di stato e scegli i colori più adatti al tuo flusso di lavoro.',
	ManageStatusesSuccessSnackbar: 'Stati aggiornati con successo',
	ManageTags: 'Gestisci i tag',
	ManageTaskAttendeeStatus: 'Gestisci gli stati degli appuntamenti',
	ManageTaskAttendeeStatusDescription:
		'Personalizza i tuoi stati di appuntamento in modo che si allineino al tuo flusso di lavoro.',
	ManageTaskAttendeeStatusHelperText: 'È richiesto almeno uno stato',
	ManageTaskAttendeeStatusSubtitle: 'Stati personalizzati',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manuale',
	ManualAppointment: 'Appuntamento manuale',
	ManualPayment: 'Pagamento manuale',
	ManuallyTypeLocation: 'Digitare manualmente la posizione',
	MapColumns: 'Colonne della mappa',
	MappingRequired: 'Mapping richiesto',
	MarkAllAsRead: 'Segna tutto come letto',
	MarkAsCompleted: 'Segna come completato',
	MarkAsManualSubmission: 'Segna come inviato',
	MarkAsPaid: 'Contrassegna come pagato',
	MarkAsRead: 'Segna come letto',
	MarkAsUnpaid: 'Contrassegna come non pagato',
	MarkAsUnread: 'Contrassegna come non letto',
	MarkAsVoid: 'Contrassegna come nullo',
	Marker: 'Marcatore',
	MarketingManager: 'Responsabile marketing',
	MassageTherapist: 'Massaggiatore Terapeutico',
	MassageTherapists: 'Massaggiatori Terapeutici',
	MassageTherapy: 'Terapia di massaggio',
	MaxBookingTimeDescription1: 'I clienti possono programmare fino a',
	MaxBookingTimeDescription2: 'nel futuro',
	MaxBookingTimeLabel: '{timePeriod} in anticipo',
	MaxCapacity: 'Capacità massima',
	Maximize: 'Massimizzare',
	MaximumAttendeeLimit: 'Limite massimo',
	MaximumBookingTime: 'Tempo massimo di prenotazione',
	MaximumBookingTimeError: 'Tempo massimo di prenotazione non deve superare {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Puoi ridurre al minimo fino a {count} pannelli laterali contemporaneamente. Procedendo, il pannello minimizzato più vecchio verrà chiuso. Vuoi continuare?',
	MaximumMinimizedPanelsReachedTitle: 'Hai troppi pannelli aperti.',
	MechanicalEngineer: 'Ingegnere meccanico',
	MediaGallery: 'Galleria multimediale',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Numero del fornitore Medicaid',
	MedicalAssistant: 'Assistente medico',
	MedicalCoder: 'Codificatore medico',
	MedicalDoctor: 'Dottore in medicina',
	MedicalIllustrator: 'Illustratore medico',
	MedicalInterpreter: 'Interprete medico',
	MedicalTechnologist: 'Tecnologo medico',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Numero del fornitore Medicare',
	Medicine: 'Medicinale',
	Medium: 'Medio',
	Meeting: 'Incontro',
	MeetingEnd: 'Fine riunione',
	MeetingEnded: 'Riunione terminata',
	MeetingHost: 'Host della riunione',
	MeetingLowerHand: 'Mano inferiore',
	MeetingOpenChat: 'Apri chat',
	MeetingPersonRaisedHand: '{name} alzò la mano',
	MeetingRaiseHand: 'Alza la mano',
	MeetingReady: 'Pronto per la riunione',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Riunioni',
	MemberId: 'ID membro',
	MentalHealth: 'Salute mentale',
	MentalHealthPractitioners: 'Professionisti della salute mentale',
	MentalHealthProfessional: 'Professionista della salute mentale',
	Merge: 'Unisci',
	MergeClientRecords: 'Unisci i record dei clienti',
	MergeClientRecordsDescription: 'Unire i record dei clienti combinerà tutti i loro dati, inclusi:',
	MergeClientRecordsDescription2: 'Vuoi continuare con la fusione? Questa azione non può essere annullata',
	MergeClientRecordsItem1: 'Note e documenti',
	MergeClientRecordsItem2: 'Appuntamenti',
	MergeClientRecordsItem3: 'Fatture',
	MergeClientRecordsItem4: 'Conversaciones',
	MergeClientsSuccess: 'Fusione del record del cliente riuscita',
	MergeLimitExceeded: 'Puoi unire al massimo 4 clienti alla volta.',
	Message: 'Messaggio',
	MessageAttachments: '{total} allegati',
	Method: 'Metodo',
	MfaAvailabilityDisclaimer:
		'MFA è disponibile solo per gli accessi tramite e-mail e password. Per apportare modifiche alle impostazioni MFA, accedi utilizzando e-mail e password.',
	MfaDeviceLostPanelDescription: 'In alternativa, puoi verificare la tua identità ricevendo un codice via email.',
	MfaDeviceLostPanelTitle: 'Hai perso il tuo dispositivo MFA?',
	MfaDidntReceiveEmailCode: `Non hai ricevuto un codice? Contatta l'assistenza`,
	MfaEmailOtpSendFailureSnackbar: `Impossibile inviare l'OTP via email.`,
	MfaEmailOtpSentSnackbar: 'Un codice è stato inviato a {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: `Impossibile verificare l'OTP dell'email.`,
	MfaHasBeenSetUpText: 'Hai impostato MFA',
	MfaPanelDescription: `Proteggi il tuo account abilitando l'autenticazione a più fattori (MFA) per un ulteriore livello di protezione. Verifica la tua identità tramite un metodo secondario per impedire l'accesso non autorizzato.`,
	MfaPanelNotAuthorizedError: `Devi aver effettuato l'accesso con il nome utente `,
	MfaPanelRecommendationDescription: `Hai effettuato di recente l'accesso utilizzando un metodo alternativo per verificare la tua identità. Per proteggere il tuo account, prendi in considerazione la possibilità di configurare un nuovo dispositivo MFA.`,
	MfaPanelRecommendationTitle: '**Consigliata:** Aggiorna il tuo dispositivo MFA',
	MfaPanelTitle: 'Autenticazione a più fattori (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'Per poter aggiornare le impostazioni MFA, dovrai verificare la tua email.',
	MfaRecommendationBannerDescription: `Hai effettuato di recente l'accesso utilizzando un metodo alternativo per verificare la tua identità. Per proteggere il tuo account, prendi in considerazione la possibilità di configurare un nuovo dispositivo MFA.`,
	MfaRecommendationBannerPrimaryAction: 'Imposta MFA',
	MfaRecommendationBannerTitle: 'Raccomandato',
	MfaRemovedSnackbarTitle: `L'MFA è stato rimosso.`,
	MfaSendEmailCode: 'Invia codice',
	MfaVerifyIdentityLostDeviceButton: `Ho perso l'accesso al mio dispositivo MFA`,
	MfaVerifyYourIdentityPanelDescription:
		'Controlla la tua app di autenticazione per trovare il codice e inseriscilo qui sotto.',
	MfaVerifyYourIdentityPanelTitle: 'Verifica la tua identità',
	MicCamWarningMessage:
		'Sblocca la fotocamera e il microfono cliccando sulle icone bloccate nella barra degli indirizzi del browser.',
	MicCamWarningTitle: 'La telecamera e il microfono sono bloccati',
	MicOff: 'Il microfono è spento',
	MicOn: 'Il microfono è acceso',
	MicSource: 'Sorgente del microfono',
	MicWarningMessage: 'È stato rilevato un problema con il microfono',
	Microphone: 'Microfono',
	MicrophonePermissionBlocked: 'Accesso al microfono bloccato',
	MicrophonePermissionBlockedDescription:
		'Aggiorna le autorizzazioni del tuo microfono per iniziare la registrazione.',
	MicrophonePermissionError: `Concedi l'autorizzazione al microfono nelle impostazioni del browser per continuare`,
	MicrophonePermissionPrompt: `Si prega di consentire l'accesso al microfono per continuare`,
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Colore del calendario di Outlook',
	MicrosoftOutlook: 'Prospettiva di Microsoft',
	MicrosoftTeams: 'Team Microsoft',
	MiddleEast: 'Medio Oriente',
	MiddleName: 'Secondo nome',
	MiddleNames: 'Secondo nome',
	Midwife: 'Ostetrica',
	Midwives: 'ostetriche',
	Milan: 'Milano',
	MinBookingTimeDescription1: 'I clienti non possono pianificare entro',
	MinBookingTimeDescription2: `dell'orario di inizio di un appuntamento`,
	MinBookingTimeLabel: `{timePeriod} prima dell'appuntamento`,
	MinCancellationTimeEditModeDescription: 'Imposta quante ore un cliente può annullare senza penalità',
	MinCancellationTimeUnset: 'Nessun tempo minimo di cancellazione impostato',
	MinCancellationTimeViewModeDescription: 'Periodo di cancellazione senza penalità',
	MinMaxBookingTimeUnset: 'Nessun orario impostato',
	Minimize: 'Minimizzare',
	MinimizeConfirmationDescription:
		'Hai un pannello attivo ridotto. Se continui, si chiuderà e potresti perdere i dati non salvati.',
	MinimizeConfirmationTitle: 'Chiudi il pannello ridotto?',
	MinimumBookingTime: 'Tempo minimo di prenotazione',
	MinimumCancellationTime: 'Tempo minimo di cancellazione',
	MinimumPaymentError: 'Per i pagamenti online è richiesto un importo minimo di {minimumAmount}.',
	MinuteAbbreviated: 'minuti',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minuto per minuto',
	MinutesPlural: '{age, plural, one {# minuto} other {# minuti}}',
	MiscellaneousInformation: 'Informazioni varie',
	MissingFeatures: 'Caratteristiche mancanti',
	MissingPaymentMethod: 'Aggiungi un metodo di pagamento al tuo abbonamento per aggiungere altri membri dello staff.',
	MobileNumber: 'Numero di cellulare',
	MobileNumberOptional: 'Numero di cellulare (facoltativo)',
	Modern: 'Moderno',
	Modifiers: 'Modificatori',
	ModifiersPlaceholder: 'Modificatori',
	Monday: 'Lunedi',
	Month: 'Mese',
	Monthly: 'Mensile',
	MonthlyCost: 'Costo mensile',
	MonthlyOn: 'Mesečno na {date}',
	MonthsPlural: '{age, plural, one {# mese} other {# mesi}}',
	More: 'Di più',
	MoreActions: 'Altre azioni',
	MoreSettings: 'Altre impostazioni',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Più comunemente usato',
	MostDownloaded: 'I più scaricati',
	MostPopular: 'I più popolari',
	Mother: 'Madre',
	MotherInLaw: 'Suocera',
	MoveDown: 'Spostarsi verso il basso',
	MoveInboxConfirmationDescription: `Riassegnare questa connessione dell'app la rimuoverà dalla casella di posta <strong>{currentInboxName}</strong>.`,
	MoveTemplateToFolder: 'Sposta `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} spostato in {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Spostamento riuscito nella cartella di intake predefinita',
	MoveTemplateToNewFolder: 'Crea una nuova cartella per spostare questo elemento.',
	MoveToChosenFolder:
		'Scegli una cartella in cui spostare questo elemento. Puoi creare una nuova cartella se necessario.',
	MoveToFolder: 'Sposta nella cartella',
	MoveToInbox: 'Sposta nella Posta in arrivo',
	MoveToNewFolder: 'Sposta nella nuova cartella',
	MoveToSelectedFolder: `Una volta spostato, l'elemento sarà organizzato nella cartella selezionata e non apparirà più nella sua posizione attuale.`,
	MoveUp: `Spostarsi verso l'alto`,
	MultiSpeciality: 'Multi-specialità',
	MultipleChoiceFormPrimaryText: 'Scelta multipla',
	MultipleChoiceFormSecondaryText: 'Scegli più opzioni',
	MultipleChoiceGridFormPrimaryText: 'Griglia a scelta multipla',
	MultipleChoiceGridFormSecondaryText: 'Scegli le opzioni da una matrice',
	Mumbai: 'Mumbay',
	MusicTherapist: 'Musicoterapista',
	MustContainOneLetterError: 'Deve contenere almeno una lettera',
	MustEndWithANumber: 'Deve terminare con un numero',
	MustHaveAtLeastXItems: 'Dovrebbe avere almeno {count, plural, one {# elemento} other {# elementi}}',
	MuteAudio: 'Disattiva audio',
	MuteEveryone: `Disattiva l'audio per tutti`,
	MyAvailability: 'La mia disponibilità',
	MyGallery: 'La mia galleria',
	MyPortal: 'Il mio portale',
	MyRelationships: 'Le mie relazioni',
	MyTemplates: 'Modelli di squadra',
	MyofunctionalTherapist: 'Terapista miofunzionale',
	NCalifornia: 'California del Nord',
	NPI: 'NPI',
	NVirginia: 'Virginia del Nord',
	Name: 'Nome',
	NameIsRequired: 'Il nome è obbligatorio',
	NameMustNotBeAWebsite: 'Il nome non deve essere un sito web',
	NameMustNotBeAnEmail: 'Il nome non deve essere un indirizzo email',
	NameMustNotContainAtSign: 'Il nome non deve contenere il simbolo @',
	NameMustNotContainHTMLTags: 'Il nome non deve contenere tag HTML',
	NameMustNotContainSpecialCharacters: 'Il nome non deve contenere caratteri speciali',
	NameOnCard: 'Nome sulla carta',
	NationalProviderId: 'Identificatore nazionale del fornitore (NPI)',
	NaturopathicDoctor: 'Dottore naturopata',
	NavigateToPersonalSettings: 'Profilo',
	NavigateToSubscriptionSettings: `Impostazioni dell'abbonamento`,
	NavigateToWorkspaceSettings: `Impostazioni dell'area di lavoro`,
	NavigateToYourTeam: 'Gestisci team',
	NavigationDrawerBilling: 'Fatturazione',
	NavigationDrawerBillingInfo: 'Informazioni di fatturazione, fatture e Stripe',
	NavigationDrawerCommunication: 'Comunicazione',
	NavigationDrawerCommunicationInfo: 'Notifiche e modelli',
	NavigationDrawerInsurance: 'Assicurazione',
	NavigationDrawerInsuranceInfo: 'Pagatori assicurativi e reclami',
	NavigationDrawerInvoices: 'Fatturazione',
	NavigationDrawerPersonal: 'Il mio profilo',
	NavigationDrawerPersonalInfo: 'I tuoi dati personali',
	NavigationDrawerProfile: 'Profilo',
	NavigationDrawerProviderSettings: 'Impostazioni',
	NavigationDrawerScheduling: 'Pianificazione',
	NavigationDrawerSchedulingInfo: 'Dettagli dei servizi e prenotazioni',
	NavigationDrawerSettings: 'Impostazioni',
	NavigationDrawerTemplates: 'Modelli',
	NavigationDrawerTemplatesV2: 'Modelli V2',
	NavigationDrawerTrash: 'Spazzatura',
	NavigationDrawerTrashInfo: 'Ripristina gli elementi eliminati',
	NavigationDrawerWorkspace: `Impostazioni dell'area di lavoro`,
	NavigationDrawerWorkspaceInfo: 'Informazioni su abbonamento e spazio di lavoro',
	NegativeBalanceNotSupported: 'I saldi negativi dei conti non sono supportati',
	Nephew: 'Nipote',
	NetworkQualityFair: 'Connessione equa',
	NetworkQualityGood: 'Buona connessione',
	NetworkQualityPoor: 'Connessione scadente',
	Neurologist: 'Neurologo',
	Never: 'Mai',
	New: 'Nuovo',
	NewAppointment: 'Nuovo appuntamento',
	NewClaim: 'Nuova richiesta',
	NewClient: 'Nuovo cliente',
	NewClientNextStepsModalAddAnotherClient: 'Aggiungi un altro cliente',
	NewClientNextStepsModalBookAppointment: 'Prenota un appuntamento',
	NewClientNextStepsModalBookAppointmentDescription: `Prenota un appuntamento imminente o crea un'attività.`,
	NewClientNextStepsModalCompleteBasicInformation: 'Scheda completa del cliente',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Aggiungi le informazioni del cliente e registra i passaggi successivi.',
	NewClientNextStepsModalCreateInvoice: 'Crea fattura',
	NewClientNextStepsModalCreateInvoiceDescription:
		'Aggiungi le informazioni di pagamento del cliente o crea una fattura.',
	NewClientNextStepsModalCreateNote: 'Crea nota o carica documento',
	NewClientNextStepsModalCreateNoteDescription: 'Raccogliere note e documentazione del cliente.',
	NewClientNextStepsModalDescription: 'Ecco alcune azioni da intraprendere ora che hai creato un record cliente.',
	NewClientNextStepsModalSendIntake: 'Invia assunzione',
	NewClientNextStepsModalSendIntakeDescription:
		'Raccogliere le informazioni del cliente e inviare moduli aggiuntivi da compilare e firmare.',
	NewClientNextStepsModalSendMessage: 'Invia messaggio',
	NewClientNextStepsModalSendMessageDescription: 'Scrivi e invia un messaggio al tuo cliente.',
	NewClientNextStepsModalTitle: 'Prossimi passi',
	NewClientSuccess: 'Nuovo client creato con successo',
	NewClients: 'Nuovi clienti',
	NewConnectedApp: 'Nuova app connessa',
	NewContact: 'Nuovo contatto',
	NewContactNextStepsModalAddRelationship: 'Aggiungi relazione',
	NewContactNextStepsModalAddRelationshipDescription: 'Collegare questo contatto a clienti o gruppi correlati.',
	NewContactNextStepsModalBookAppointment: 'Prenota appuntamento',
	NewContactNextStepsModalBookAppointmentDescription: `Prenota un appuntamento imminente o crea un'attività.`,
	NewContactNextStepsModalCompleteProfile: 'Profilo completo',
	NewContactNextStepsModalCompleteProfileDescription:
		'Aggiungi le informazioni di contatto e registra i passaggi successivi.',
	NewContactNextStepsModalCreateNote: 'Crea nota o carga documento',
	NewContactNextStepsModalCreateNoteDescription: 'Cattura le note e la documentazione dei clienti.',
	NewContactNextStepsModalDescription: 'Ecco alcune azioni da intraprendere ora che hai creato un contatto.',
	NewContactNextStepsModalInviteToPortal: 'Invito al portale',
	NewContactNextStepsModalInviteToPortalDescription: 'Invia un invito per accedere al portale.',
	NewContactNextStepsModalTitle: 'Prossimi passi',
	NewContactSuccess: 'Nuovo contatto creato con successo',
	NewDateOverrideButton: 'Nuova sostituzione data',
	NewDiagnosis: 'Aggiungi diagnosi',
	NewField: 'Nuovo campo',
	NewFolder: 'Nuova cartella',
	NewInvoice: 'Nuova fattura',
	NewLocation: 'Nuova posizione',
	NewLocationFailure: 'Impossibile creare una nuova posizione',
	NewLocationSuccess: 'Nuova posizione creata con successo',
	NewManualPayer: 'Nuovo pagatore manuale',
	NewNote: 'Nuova nota',
	NewNoteCreated: 'Nuova nota creata con successo',
	NewPassword: 'Nuova password',
	NewPayer: 'Nuovo pagatore',
	NewPaymentMethod: 'Nuovo metodo di pagamento',
	NewPolicy: 'Nuova politica',
	NewRelationship: 'Nuova relazione',
	NewReminder: 'Nuovo promemoria',
	NewSchedule: 'Nuovo programma',
	NewSection: 'Nuova sezione',
	NewSectionOld: 'Nuova sezione [VECCHIA]',
	NewSectionWithGrid: 'Nuova sezione con griglia',
	NewService: 'Nuovo servizio',
	NewServiceFailure: 'Impossibile creare il nuovo servizio',
	NewServiceSuccess: 'Nuovo servizio creato con successo',
	NewStatus: 'Nuovo stato',
	NewTask: 'Nuovo compito',
	NewTaxRate: 'Nuova aliquota fiscale',
	NewTeamMemberNextStepsModalAssignClients: 'Assegna clienti',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Assegna clienti specifici al tuo membro del team.',
	NewTeamMemberNextStepsModalAssignServices: 'Assegna servizi',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Gestisci i servizi loro assegnati e regola i prezzi secondo necessità.',
	NewTeamMemberNextStepsModalBookAppointment: 'Prenota appuntamento',
	NewTeamMemberNextStepsModalBookAppointmentDescription: `Prenota un appuntamento imminente o crea un'attività.`,
	NewTeamMemberNextStepsModalCompleteProfile: 'Profilo completo',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Aggiungi dettagli sul tuo membro del team per completare il suo profilo.',
	NewTeamMemberNextStepsModalDescription:
		'Ecco alcune azioni da intraprendere ora che hai creato un membro del team.',
	NewTeamMemberNextStepsModalEditPermissions: 'Permessi di modifica',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Regola i loro livelli di accesso per assicurarsi che abbiano le giuste autorizzazioni.',
	NewTeamMemberNextStepsModalSetAvailability: 'Imposta la disponibilità',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Configura la loro disponibilità per creare programmi.',
	NewTeamMemberNextStepsModalTitle: 'Prossimi passi',
	NewTemplateFolderDescription: 'Crea una nuova cartella per organizzare la tua documentazione.',
	NewUIUpdateBannerButton: `Ricarica l'app`,
	NewUIUpdateBannerTitle: `C'è un nuovo aggiornamento pronto!`,
	NewZealand: 'Nuova Zelanda',
	Newest: 'Più recente',
	NewestUnreplied: 'Più recenti senza risposta',
	Next: 'Prossimo',
	NextInvoiceIssueDate: 'Prossima data di emissione fattura',
	NextNDays: 'Prossimi {number} giorni',
	Niece: 'Nipote',
	No: 'NO',
	NoAccessGiven: 'Nessun accesso concesso',
	NoActionConfigured: 'Nessuna azione configurata',
	NoActivePolicies: 'Nessuna politica attiva',
	NoActiveReferrals: 'Non hai referral attivi',
	NoAppointmentsFound: 'Non sono stati trovati appuntamenti',
	NoAppointmentsHeading: 'Gestire gli appuntamenti e le attività dei clienti',
	NoArchivedPolicies: 'Nessuna politica archiviata',
	NoAvailableTimes: 'Nessun orario disponibile trovato.',
	NoBillingItemsFound: 'Nessun elemento di fatturazione trovato',
	NoCalendarsSynced: 'Nessun calendario sincronizzato',
	NoClaimsFound: 'Nessuna richiesta trovata',
	NoClaimsHeading: `Semplifica l'invio delle richieste di rimborso`,
	NoClientsHeading: 'Riunisci i dati dei tuoi clienti',
	NoCompletedReferrals: 'Non hai referral completi',
	NoConnectionsHeading: 'Semplifica le comunicazioni con i tuoi clienti',
	NoContactsGivenAccess: 'Nessun cliente o contatto ha avuto accesso a questa nota',
	NoContactsHeading: 'Rimani in contatto con coloro che supportano la tua pratica',
	NoCopayOrCoinsurance: 'Nessun co-pagamento o co-assicurazione',
	NoCustomServiceSchedule:
		'Nessun programma personalizzato impostato: la disponibilità dipende dalla disponibilità del membro del team',
	NoDescription: 'Nessuna descrizione',
	NoDocumentationHeading: 'Crea e archivia note in modo sicuro',
	NoDuplicateRecordsHeading: 'Il tuo record cliente è privo di duplicati',
	NoEffect: 'Nessun effetto',
	NoEnrolmentProfilesFound: 'Nessun profilo di iscrizione trovato',
	NoGlossaryItems: 'Nessuna voce del glossario',
	NoInvitedReferrals: 'Non hai nessun referral invitato',
	NoInvoicesFound: 'Nessuna fattura trovata',
	NoInvoicesHeading: 'Automatizza la fatturazione e i pagamenti',
	NoLimit: 'Nessun limite',
	NoLocationsFound: 'Non sono state trovate posizioni',
	NoLocationsWillBeAdded: 'Nessuna località verrà aggiunta.',
	NoNoteFound: 'Nessuna nota trovata',
	NoPaymentMethods: 'Non hai salvato alcun metodo di pagamento, puoi aggiungerne uno quando effettui un pagamento.',
	NoPermissionError: 'Non hai il permesso',
	NoPermissions: 'Non hai il permesso di visualizzare questa pagina',
	NoPolicy: 'Nessuna politica di cancellazione aggiunta',
	NoRecordsHeading: 'Personalizza i dati dei tuoi clienti',
	NoRecordsToDisplay: 'Nessun {resource} da visualizzare',
	NoRelationshipsHeading: 'Rimani in contatto con coloro che supportano il tuo cliente',
	NoRemindersFound: 'Nessun promemoria trovato',
	NoResultsFound: 'Nessun risultato trovato',
	NoResultsFoundDescription: 'Non riusciamo a trovare alcun elemento che corrisponda alla tua ricerca',
	NoServicesAdded: 'Nessun servizio aggiunto',
	NoServicesApplied: 'Nessun servizio applicato',
	NoServicesWillBeAdded: 'Non verranno aggiunti servizi.',
	NoTemplate: 'Non hai salvato alcun modello di pratica',
	NoTemplatesHeading: 'Crea i tuoi modelli',
	NoTemplatesInFolder: 'Nessun modello in questa cartella',
	NoTitle: 'Nessun titolo',
	NoTrashItemsHeading: 'Nessun elemento eliminato trovato',
	NoTriggerConfigured: 'Nessun trigger configurato',
	NoUnclaimedItemsFound: 'Nessun oggetto non reclamato trovato.',
	NonAiTemplates: 'Modelli non AI',
	None: 'Nessuno',
	NotAvailable: 'Non disponibile',
	NotCovered: 'Non coperto',
	NotFoundSnackbar: 'Risorsa non trovata.',
	NotRequiredField: 'Non richiesto',
	Note: 'Nota',
	NoteDuplicateSuccess: 'Nota duplicata con successo',
	NoteEditModeViewSwitcherDescription: 'Crea e modifica nota',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} ha presentato il modulo {noteTitle}',
	NoteLockSuccess: '{title} è stato bloccato',
	NoteModalAttachmentButton: 'Aggiungi allegati',
	NoteModalPhotoButton: 'Aggiungi/Cattura foto',
	NoteModalTrascribeButton: `Trascrivi l'audio in diretta`,
	NoteResponderModeViewSwitcherDescription: 'Inviare moduli e rivedere le risposte',
	NoteResponderModeViewSwitcherTooltipTitle: 'Rispondi e invia moduli per conto dei tuoi clienti',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Compila e invia i moduli come cliente',
	NoteUnlockSuccess: '{title} è stato sbloccato',
	NoteViewModeViewSwitcherDescription: 'Accesso di sola visualizzazione',
	Notes: 'Appunti',
	NotesAndForms: 'Note e Moduli',
	NotesCategoryDescription: 'Per documentare le interazioni con i clienti',
	NothingToSeeHere: 'Niente da vedere qui',
	Notification: 'Notifica',
	NotificationIgnoredMessage: 'Tutte le notifiche {notificationType} saranno ignorate',
	NotificationRestoredMessage: 'Tutte le notifiche {notificationType} ripristinate',
	NotificationSettingBillingDescription:
		'Ricevi notifiche sugli aggiornamenti di pagamento dei clienti e sui promemoria.',
	NotificationSettingBillingTitle: 'Fatturazione e pagamento',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} solo} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Ricevi notifiche sugli aggiornamenti di pagamento dei clienti e sui promemoria.',
	NotificationSettingClientDocumentationTitle: 'Cliente e documentazione',
	NotificationSettingCommunicationsDescription:
		'Ricevi notifiche per la posta in arrivo e aggiornamenti dai tuoi canali connessi',
	NotificationSettingCommunicationsTitle: 'Comunicazioni',
	NotificationSettingEmail: 'Email',
	NotificationSettingInApp: `Nell'app`,
	NotificationSettingPanelDescription: 'Scegli le notifiche che desideri ricevere per attività e raccomandazioni.',
	NotificationSettingPanelTitle: 'Preferenze di notifica',
	NotificationSettingSchedulingDescription:
		'Ricevi notifiche quando un membro del team o un cliente prenota, riprogramma o annulla un appuntamento.',
	NotificationSettingSchedulingTitle: 'Pianificazione',
	NotificationSettingUpdateSuccess: 'Impostazioni di notifica aggiornate con successo',
	NotificationSettingWhereYouReceiveNotifications: 'Dove vuoi ricevere queste notifiche',
	NotificationSettingWorkspaceDescription:
		'Ricevi notifiche su modifiche del sistema, problemi, trasferimenti di dati e promemoria di abbonamento.',
	NotificationSettingWorkspaceTitle: 'Spazio di lavoro',
	NotificationTemplateUpdateFailed: 'Impossibile aggiornare il modello di notifica',
	NotificationTemplateUpdateSuccess: 'Modello di notifica aggiornato con successo',
	NotifyAttendeesOfTaskCancellationModalDescription: `Desideri inviare un'e-mail di notifica di annullamento ai partecipanti?`,
	NotifyAttendeesOfTaskCancellationModalTitle: 'Invia annullamento',
	NotifyAttendeesOfTaskConfirmationModalDescription: `Desideri inviare un'e-mail di notifica di conferma ai partecipanti?`,
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Invia conferma',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Desideri inviare email di cancellazione ai partecipanti?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {fa} other {fanno}} non hanno un indirizzo email, quindi non riceveranno notifiche e promemoria automatici.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {ha} other {hanno}} un indirizzo email e quindi non riceveranno notifiche e promemoria automatici.',
	NotifyAttendeesOfTaskModalTitle: `Desideri inviare un'e-mail di notifica ai partecipanti?`,
	NotifyAttendeesOfTaskSnackbar: 'Invio notifica',
	NuclearMedicineTechnologist: 'Tecnologo di medicina nucleare',
	NumberOfClaims: '{number, plural, one {# Richiesta} other {# Richieste}}',
	NumberOfClients: '{number, plural, one {# Cliente} other {# Clientes}}',
	NumberOfContacts: '{number, plural, one {# Contatto} other {# Contatti}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entry} other {entries}} trovati',
	NumberOfErrors: '{count, plural, one {# errore} other {# errori}}',
	NumberOfInvoices: '{number, plural, one {# Fattura} other {# Fatture}}',
	NumberOfLineitemsToCredit:
		'Hai <mark>{count} {count, plural, one {elemento di linea} other {elementi di linea}}</mark> per emettere un credito.',
	NumberOfPayments: '{number, plural, one {# Pagamento} other {# Pagamenti}}',
	NumberOfRelationships: '{number, plural, one {# Relazione} other {# Relazioni}}',
	NumberOfResources: '{number, plural, one {# Risorsa} other {# Risorse}}',
	NumberOfTeamMembers: '{number, plural, one {# Membro del team} other {# Membri del team}}',
	NumberOfTrashItems: '{number, plural, one {# articolo} other {# articoli}}',
	NumberOfUninvoicedAmounts:
		'Hai <mark>{count} fattura non emessa {count, plural, one {importo} other {importi}}</mark> da fatturare',
	NumberedList: 'Elenco numerato',
	Nurse: 'Infermiera',
	NurseAnesthetist: 'Infermiere anestesista',
	NurseAssistant: 'Assistente infermieristico',
	NurseEducator: 'Infermiere educatore',
	NurseMidwife: 'Ostetrica infermiera',
	NursePractitioner: 'Infermiere specializzato',
	Nurses: 'Infermieri',
	Nursing: 'Infermieristica',
	Nutritionist: 'Nutrizionista',
	Nutritionists: 'Nutrizionisti',
	ObstetricianOrGynecologist: 'Ostetrico/ginecologo',
	Occupation: 'Occupazione',
	OccupationalTherapist: 'Terapista occupazionale',
	OccupationalTherapists: 'Terapisti occupazionali',
	OccupationalTherapy: 'Terapia occupazionale',
	Occurrences: 'Eventi',
	Of: 'Di',
	Ohio: 'Ohio',
	OldPassword: 'Vecchia password',
	OlderMessages: '{count} messaggi più vecchi',
	Oldest: 'Il più vecchio',
	OldestUnreplied: 'Più vecchio senza risposta',
	On: 'SU',
	OnboardingBusinessAgreement: `A nome mio e dell'azienda, accetto il {businessAssociateAgreement}`,
	OnboardingLoadingOccupationalTherapist:
		'<mark>Terapisti occupazionali</mark> costituiscono un quarto dei nostri clienti su Carepatron',
	OnboardingLoadingProfession: 'Abbiamo un sacco di <mark>{profession}</mark> che usano e prosperano su Carepatron.',
	OnboardingLoadingPsychologist:
		'<mark>Psicologi</mark> costituiscono oltre la metà dei nostri clienti su Carepatron',
	OnboardingLoadingSubtitleFive: 'La nostra missione è fare<mark> software sanitario accessibile</mark> a tutti.',
	OnboardingLoadingSubtitleFour:
		'<mark>Software sanitario semplificato</mark> per oltre 10.000 persone in tutto il mondo.',
	OnboardingLoadingSubtitleThree: `Salva<mark> 1 giorno alla settimana</mark> sulle attività amministrative con l'ausilio di Carepatron.`,
	OnboardingLoadingSubtitleTwo: `Salva<mark> 2 ore</mark> quotidianamente su attività amministrative con l'ausilio di Carepatron.`,
	OnboardingReviewLocationOne: 'Centro di salute mentale di Holland Park',
	OnboardingReviewLocationThree: 'Infermiere di pratica, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Clinica della casa della vita',
	OnboardingReviewNameOne: 'Annulla P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Chiara W.',
	OnboardingReviewOne:
		'"Carepatron è super intuitivo da usare. Ci aiuta a gestire il nostro studio così bene che non abbiamo nemmeno più bisogno di un team di amministratori"',
	OnboardingReviewThree:
		'"È la soluzione migliore che abbia mai utilizzato, sia in termini di funzionalità che di costi. Ha tutto ciò di cui ho bisogno per far crescere la mia attività"',
	OnboardingReviewTwo: `"Adoro anche l'app Carepatron. Mi aiuta a tenere traccia dei miei clienti e a lavorare mentre sono in movimento."`,
	OnboardingTitle: `Andiamo a<mark> Sapere
 tu faresti meglio</mark>`,
	Oncologist: 'Oncologo',
	Online: 'In linea',
	OnlineBookingColorTheme: 'Tema colore prenotazione online',
	OnlineBookings: 'Prenotazioni online',
	OnlineBookingsHelper: 'Scegli quando possono essere effettuate le prenotazioni online e da quale tipo di clienti',
	OnlinePayment: 'Pagamento online',
	OnlinePaymentSettingCustomInfo:
		'Le impostazioni di pagamento online per questo servizio differiscono dalle impostazioni di prenotazione globali.',
	OnlinePaymentSettings: 'Impostazioni di pagamento online',
	OnlinePaymentSettingsInfo:
		'Raccogli i pagamenti per i servizi al momento della prenotazione online per proteggere e semplificare i pagamenti',
	OnlinePaymentSettingsPaymentsDisabled:
		'I pagamenti sono disabilitati e quindi non possono essere raccolti durante la prenotazione online. Controlla le impostazioni di pagamento per abilitare i pagamenti.',
	OnlinePaymentSettingsStripeNote:
		'{azione} per ricevere pagamenti di prenotazione online e semplificare il processo di pagamento',
	OnlinePaymentsNotSupportedForCurrency: 'Pagamenti online non sono supportati in {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Spiacenti, i pagamenti online non sono supportati in questa valuta',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Spiacenti, i pagamenti online non sono ancora supportati nel tuo Paese',
	OnlineScheduling: 'Pianificazione online',
	OnlyVisibleToYou: 'Visibile solo a te',
	OnlyYou: 'Solo tu',
	OnsetDate: 'Data di inizio',
	OnsetOfCurrentSymptomsOrIllness: 'Insorgenza di sintomi o malattie attuali',
	Open: 'Aprire',
	OpenFile: 'Apri file',
	OpenSettings: 'Apri impostazioni',
	Ophthalmologist: 'Oculista',
	OptimiseTelehealthCalls: 'Ottimizza le tue chiamate di Telemedicina',
	OptimizeServiceTimes: 'Ottimizzare i tempi di servizio',
	Options: 'Opzioni',
	Optometrist: 'Optometrista',
	Or: 'O',
	OrAttachSingleFile: 'allegare un file',
	OrDragAndDrop: 'o trascina e rilascia',
	OrderBy: 'Ordina per',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organizzazione o individuo',
	OrganizationPlanInclusion1: 'Autorizzazioni avanzate',
	OrganizationPlanInclusion2: `Supporto gratuito per l'importazione dei dati dei clienti`,
	OrganizationPlanInclusion3: 'Responsabile del successo dedicato',
	OrganizationPlanInclusionHeader: 'Tutto ciò che è presente in Professional, più...',
	Orthodontist: 'Ortodontista',
	Orthotist: 'Ortottista',
	Other: 'Altro',
	OtherAdjustments: 'Altri aggiustamenti',
	OtherAdjustmentsTableEmptyState: 'Nessuna modifica trovata',
	OtherEvents: 'Altri eventi',
	OtherId: `Altro documento d'identità`,
	OtherIdQualifier: 'Altro qualificatore ID',
	OtherPaymentMethod: 'Altro metodo di pagamento',
	OtherPlanMessage: `Rimani al controllo delle esigenze del tuo studio. Rivedi il tuo piano attuale, monitora l'utilizzo ed esplora le opzioni di aggiornamento per sbloccare più funzionalità man mano che il tuo team cresce.`,
	OtherPolicy: 'Altre assicurazioni',
	OtherProducts: 'Quali altri prodotti o strumenti utilizzi?',
	OtherServices: 'Altri servizi',
	OtherTemplates: 'Altri modelli',
	Others: 'Altri',
	OthersPeople: `{n, plural, 		one {1 altra persona}
		other {# altre persone}
	}`,
	OurResearchTeamReachOut:
		'Il nostro team di ricerca può contattarti per saperne di più su come Carepatron avrebbe potuto soddisfare meglio le tue esigenze?',
	OutOfOffice: 'Fuori ufficio',
	OutOfOfficeColor: 'Colore fuori ufficio',
	OutOfOfficeHelper: 'Alcuni membri del team scelti sono fuori ufficio',
	OutsideLabCharges: 'Spese di laboratorio esterne',
	OutsideOfWorkingHours: 'Fuori orario di lavoro',
	OutsideWorkingHoursHelper: 'Alcuni membri del team scelti sono fuori orario di lavoro',
	Overallocated: 'Sovrassegnato',
	OverallocatedPaymentDescription: `Questo pagamento è stato sovrastimato per voci fatturabili.
 Aggiungere un'assegnazione agli articoli non pagati oppure emettere un accredito o un rimborso.`,
	OverallocatedPaymentTitle: 'Pagamento sovra-assegnato',
	OverdueTerm: 'Termine di ritardo (giorni)',
	OverinvoicedAmount: 'Importo fatturato in eccesso',
	Overpaid: 'Pagato troppo',
	OverpaidAmount: 'Importo pagato in eccesso',
	Overtime: 'col tempo',
	Owner: 'Proprietario',
	POS: 'Punto vendita',
	POSCode: 'Codice POS',
	POSPlaceholder: 'Punto vendita',
	PageBlockerDescription: 'Non salvate modifiche andranno perse. Vuoi comunque uscire?',
	PageBlockerTitle: 'Elimina cambios?',
	PageFormat: 'Formato pagina',
	PageNotFound: 'Pagina non trovata',
	PageNotFoundDescription: 'Non hai più accesso a questa pagina o non può essere trovata',
	PageUnauthorised: 'Accesso non autorizzato',
	PageUnauthorisedDescription: 'Non hai il permesso di accedere a questa pagina',
	Paid: 'Pagato',
	PaidAmount: 'Importo pagato',
	PaidAmountMinimumValueError: `L'importo pagato deve essere maggiore di 0`,
	PaidAmountRequiredError: `È richiesto l'importo pagato`,
	PaidItems: 'Articoli a pagamento',
	PaidMultiple: 'Pagato',
	PaidOut: 'Pagato',
	ParagraphStyles: 'Stili di paragrafo',
	Parent: 'Genitore',
	Paris: 'Parigi',
	PartialRefundAmount: 'Parzialmente rimborsato ({amount} rimanente)',
	PartiallyFull: 'Parzialmente pieno',
	PartiallyPaid: 'Parzialmente pagato',
	PartiallyRefunded: 'Parzialmente rimborsato',
	Partner: 'Partner',
	Password: 'Password',
	Past: 'Passato',
	PastDateOverridesEmpty: `Le sostituzioni delle date appariranno qui non appena l'evento sarà trascorso`,
	Pathologist: 'Patologo',
	Patient: 'Paziente',
	Pause: 'Pausa',
	Paused: 'In pausa',
	Pay: 'Paga',
	PayMonthly: 'Paga mensilmente',
	PayNow: 'Paga ora',
	PayValue: 'Paga {showPrice, select, true {{price}} other {ora}}',
	PayWithOtherCard: `Paga con un'altra carta`,
	PayYearly: 'Paga annualmente',
	PayYearlyPercentOff: 'Paga annualmente <mark>{percent}% di sconto</mark>',
	Payer: 'Pagatore',
	PayerClaimId: 'ID della richiesta del pagatore',
	PayerCoverage: 'Copertura',
	PayerDetails: 'Dettagli del pagatore',
	PayerDetailsDescription: 'Visualizza i dettagli dei pagatori aggiunti al tuo account e gestisci le iscrizioni.',
	PayerID: 'ID del pagatore',
	PayerId: 'ID del pagatore',
	PayerName: 'Nome del pagatore',
	PayerPhoneNumber: 'Numero di telefono del pagatore',
	Payers: 'Pagatori',
	Payment: 'Pagamento',
	PaymentAccountUpdated: 'Il tuo account è stato aggiornato!',
	PaymentAccountUpgraded: 'Il tuo account è stato aggiornato!',
	PaymentAmount: 'Importo del pagamento',
	PaymentDate: 'Data di pagamento',
	PaymentDetails: 'Dettagli di pagamento',
	PaymentForUsersPerMonth: 'Pagamento per {billedUsers, plural, one {# utente} other {# utenti}} al mese',
	PaymentInfoFormPrimaryText: 'Informazioni sul pagamento',
	PaymentInfoFormSecondaryText: 'Raccogliere i dettagli del pagamento',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Questa fattura è già stata pagata.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Questa fattura è già in elaborazione.',
	PaymentIntentAmountMismatchSnackbar: `L'importo totale della fattura è stato modificato. Si prega di rivedere le modifiche prima di pagare.`,
	PaymentIntentSyncTimeoutSnackbar: `Il tuo pagamento è andato a buon fine, ma si è verificato un timeout. Aggiorna la pagina e se il tuo pagamento non viene visualizzato, contatta l'assistenza.`,
	PaymentMethod: 'Metodo di pagamento',
	PaymentMethodDescription: `Aggiungi e gestisci il metodo di pagamento del tuo studio per semplificare il processo di fatturazione dell'abbonamento.`,
	PaymentMethodLabelBank: 'conto bancario',
	PaymentMethodLabelCard: 'carta',
	PaymentMethodLabelFallback: 'metodo di pagamento',
	PaymentMethodRequired: 'Si prega di aggiungere un metodo di pagamento prima di modificare gli abbonamenti',
	PaymentMethods: 'Metodi di pagamento',
	PaymentProcessing: 'Elaborazione dei pagamenti!',
	PaymentProcessingFee: 'Pagamento include {amount} commissione di elaborazione',
	PaymentReports: 'Report di pagamento (ERA)',
	PaymentSettings: 'Impostazioni di pagamento',
	PaymentSuccessful: 'Pagamento effettuato con successo!',
	PaymentType: 'Tipo di pagamento',
	Payments: 'Pagamenti',
	PaymentsAccountDisabledNotificationSubject: `I pagamenti online tramite {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} sono stati disabilitati.
Controlla le tue impostazioni di pagamento per abilitare i pagamenti.`,
	PaymentsEmptyStateDescription: 'Non sono stati trovati pagamenti.',
	PaymentsUnallocated: 'Pagamenti non assegnati',
	PayoutDate: 'Data di pagamento',
	PayoutsDisabled: 'Pagamenti disabilitati',
	PayoutsEnabled: 'Pagamenti abilitati',
	PayoutsStatus: 'Stato del pagamento',
	Pediatrician: 'Pediatra',
	Pen: 'Penna',
	Pending: 'In attesa di',
	People: '{rosterSize } persone',
	PeopleCount: 'Persone ({count})',
	PerMonth: '/ Mese',
	PerUser: 'Per utente',
	Permission: 'Permesso',
	PermissionRequired: 'Permesso richiesto',
	Permissions: 'Permessi',
	PermissionsClientAndContactDocumentation: 'Cliente ',
	PermissionsClientAndContactProfiles: 'Cliente ',
	PermissionsEditAccess: 'Modifica accesso',
	PermissionsInvoicesAndPayments: 'Fatture ',
	PermissionsScheduling: 'Pianificazione',
	PermissionsUnassignClients: 'Annulla assegnazione clienti',
	PermissionsUnassignClientsConfirmation: `Vuoi davvero annullare l'assegnazione di questi client?`,
	PermissionsValuesAssigned: 'Assegnato solo',
	PermissionsValuesEverything: 'Qualunque cosa',
	PermissionsValuesNone: 'Nessuno',
	PermissionsValuesOwnCalendar: 'Calendario proprio',
	PermissionsViewAccess: `Visualizza l'accesso`,
	PermissionsWorkspaceSettings: `Impostazioni dell'area di lavoro`,
	Person: '{rosterSize} persona',
	PersonalDetails: 'Dati personali',
	PersonalHealthcareHistoryStoreDescription:
		'Rispondi e conserva in modo sicuro la tua cronologia sanitaria personale in un unico posto',
	PersonalTrainer: 'Allenatore personale',
	PersonalTraining: 'Allenamento personale',
	PersonalizeWorkspace: 'Personalizza il tuo spazio di lavoro',
	PersonalizingYourWorkspace: 'Personalizzazione del tuo spazio di lavoro',
	Pharmacist: 'Farmacista',
	Pharmacy: 'Farmacia',
	PhoneCall: 'Telefonata',
	PhoneNumber: 'Numero di telefono',
	PhoneNumberOptional: 'Numero di telefono (facoltativo)',
	PhotoBy: 'Foto di',
	PhysicalAddress: 'Indirizzo fisico',
	PhysicalTherapist: 'Fisioterapista',
	PhysicalTherapists: 'Fisioterapisti',
	PhysicalTherapy: 'Terapia fisica',
	Physician: 'Medico',
	PhysicianAssistant: 'Assistente medico',
	Physicians: 'Medici',
	Physiotherapist: 'Fisioterapista',
	PlaceOfService: 'Luogo di servizio',
	Plan: 'Piano',
	PlanAndReport: 'Piano/Relazione',
	PlanId: 'ID del piano',
	PlansAndReportsCategoryDescription: 'Per la pianificazione del trattamento e la sintesi dei risultati',
	PleaseRefreshThisPageToTryAgain: 'Per favore, aggiorna questa pagina per riprovare.',
	PleaseWait: 'Attendere prego...',
	PleaseWaitForHostToJoin: `In attesa che l'host si unisca...`,
	PleaseWaitForHostToStart: `Attendi che l'host avvii la riunione.`,
	PlusAdd: '+ Aggiungi',
	PlusOthers: '+{count} altri',
	PlusPlanInclusionFive: 'Caselle di posta condivise',
	PlusPlanInclusionFour: 'Videochiamate di gruppo',
	PlusPlanInclusionHeader: 'Tutto in Essential  ',
	PlusPlanInclusionOne: 'AI illimitata',
	PlusPlanInclusionSix: 'Marchio personalizzato',
	PlusPlanInclusionThree: 'Pianificazione di gruppo',
	PlusPlanInclusionTwo: 'Archiviazione illimitata ',
	PlusSubscriptionPlanSubtitle: 'Per le pratiche da ottimizzare e far crescere',
	PlusSubscriptionPlanTitle: 'Più',
	PoliceOfficer: 'Agente di polizia',
	PolicyDates: 'Date della politica',
	PolicyHolder: 'Titolare della polizza',
	PolicyHoldersAddress: 'Indirizzo del contraente',
	PolicyMemberId: 'ID del Membro della Policy',
	PolicyStatus: 'Stato della politica',
	Popular: 'Popolare',
	PortalAccess: 'Accesso al portale',
	PortalNoAppointmentsHeading: 'Tieni traccia di tutti gli appuntamenti imminenti e passati',
	PortalNoDocumentationHeading: 'Crea e archivia in modo sicuro i tuoi documenti',
	PortalNoRelationshipsHeading: 'Riunisci coloro che sostengono il tuo viaggio',
	PosCodeErrorMessage: 'Codice POS richiesto',
	PosoNumber: 'Numero PO/SO',
	PossibleClientDuplicate: 'Possibile duplicato del client',
	PotentialClientDuplicateTitle: 'Potenziale record cliente duplicato',
	PotentialClientDuplicateWarning:
		'Queste informazioni sul cliente potrebbero già esistere nel tuo elenco clienti. Verifica e aggiorna il record esistente se necessario o continua a creare un nuovo cliente.',
	PoweredBy: 'Offerto da',
	Practice: 'Pratica',
	PracticeDetails: 'Dettagli della pratica',
	PracticeInfoHeader: 'Informazioni commerciali',
	PracticeInfoPlaceholder: `Nome della pratica,
 Identificativo del fornitore nazionale,
 Numero di identificazione del datore di lavoro`,
	PracticeLocation: 'Sembra che il tuo studio sia in',
	PracticeSettingsAvailabilityTab: 'Disponibilità',
	PracticeSettingsBillingTab: 'Impostazioni di fatturazione',
	PracticeSettingsClientSettingsTab: 'Impostazioni del cliente',
	PracticeSettingsGeneralTab: 'Generale',
	PracticeSettingsOnlineBookingTab: 'Prenotazione online',
	PracticeSettingsServicesTab: 'Servizi',
	PracticeSettingsTaxRatesTab: 'Aliquote fiscali',
	PracticeTemplate: 'Modello di pratica',
	Practitioner: 'Praticante',
	PreferredLanguage: 'Lingua preferita',
	PreferredName: 'Nome preferito',
	Prescription: 'Prescrizione',
	PreventionSpecialist: 'Specialista della prevenzione',
	Preview: 'Anteprima',
	PreviewAndSend: 'Anteprima e invio',
	PreviewUnavailable: 'Anteprima non disponibile per questo tipo di file',
	PreviousNotes: 'Note precedenti',
	Price: 'Prezzo',
	PriceError: 'Il prezzo deve essere maggiore di 0',
	PricePerClient: 'Prezzo per cliente',
	PricePerUser: 'Per utente',
	PricePerUserBilledAnnually: 'Per utente fatturato annualmente',
	PricePerUserPerPeriod: '{price} per utente / {isMonthly, select, true {mese} other {anno}}',
	PricingGuide: 'Guida ai piani tariffari',
	PricingPlanPerMonth: '/ mese',
	PricingPlanPerYear: '/ anno',
	Primary: 'Primario',
	PrimaryInsurance: 'Assicurazione primaria',
	PrimaryPolicy: 'Assicurazione primaria',
	PrimaryTimezone: 'Fuso orario principale',
	Print: 'Stampa',
	PrintToCms1500: 'Stampa su CMS1500',
	PrivatePracticeConsultant: 'Consulente di studio privato',
	Proceed: 'Procedere',
	ProcessAtTimeOfBookingDesc: 'I clienti devono pagare il prezzo intero del servizio per prenotare online',
	ProcessAtTimeOfBookingLabel: 'Elaborare i pagamenti al momento della prenotazione',
	Processing: 'Elaborazione',
	ProcessingFee: 'Commissione di elaborazione',
	ProcessingFeeToolTip: `Carepatron ti consente di addebitare le commissioni di elaborazione ai tuoi clienti.
 In alcune giurisdizioni è vietato addebitare commissioni di elaborazione ai tuoi clienti. È tua responsabilità rispettare le leggi applicabili.`,
	ProcessingRequest: 'Elaborazione della richiesta...',
	Product: 'Prodotto',
	Profession: 'Professione',
	ProfessionExample: 'Terapista, Nutrizionista, Dentista',
	ProfessionPlaceholder: `Inizia a digitare la tua professione o scegli dall'elenco`,
	ProfessionalPlanInclusion1: 'Archiviazione illimitata',
	ProfessionalPlanInclusion2: 'Attività illimitate',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Assistenza clienti 24 ore su 24, 7 giorni su 7',
	ProfessionalPlanInclusion5: 'Promemoria SMS',
	ProfessionalPlanInclusionHeader: 'Tutto ciò che è incluso in Starter, più...',
	Professions: 'Professioni',
	Profile: 'Profilo',
	ProfilePhotoFileSizeLimit: 'Limite dimensione file 5 MB',
	ProfilePopoverSubTitle: 'Sei connesso come <strong>{email}</strong>',
	ProfilePopoverTitle: 'I tuoi spazi di lavoro',
	PromoCode: 'Codice promozionale',
	PromotionCodeApplied: '{promo} applicato',
	ProposeNewDateTime: 'Proponi una nuova data/ora',
	Prosthetist: 'Protesista',
	Provider: 'Fornitore',
	ProviderBillingPlanExpansionManageButton: 'Gestisci il piano',
	ProviderCommercialNumber: 'Numero commerciale del fornitore',
	ProviderDetails: 'Dettagli del fornitore',
	ProviderDetailsAddress: 'Indirizzo',
	ProviderDetailsName: 'Nome',
	ProviderDetailsPhoneNumber: 'Numero di telefono',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Spiacenti, questo provider ha già un account di fatturazione esistente',
	ProviderInfoPlaceholder: `Nome del personale,
 Indirizzo e-mail,
 Numero di telefono,
 Identificativo del fornitore nazionale,
 Numero di licenza`,
	ProviderIsChargedProcessingFee: 'Pagherai la commissione di elaborazione',
	ProviderPaymentFormBackButton: 'Indietro',
	ProviderPaymentFormBillingAddressCity: 'Città',
	ProviderPaymentFormBillingAddressCountry: 'Paese',
	ProviderPaymentFormBillingAddressLine1: 'Linea 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Codice Postale',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'Codice CVC',
	ProviderPaymentFormCardDetailsTitle: 'Dettagli della carta di credito',
	ProviderPaymentFormCardExpiry: 'Scadenza',
	ProviderPaymentFormCardHolderAddressTitle: 'Indirizzo',
	ProviderPaymentFormCardHolderName: 'Nome del titolare',
	ProviderPaymentFormCardHolderTitle: 'Dettagli del titolare della carta',
	ProviderPaymentFormCardNumber: 'Numero della carta',
	ProviderPaymentFormPlanTitle: 'Piano scelto',
	ProviderPaymentFormPlanTotalTitle: 'Totale ({currency}):',
	ProviderPaymentFormSaveButton: `Salva l'abbonamento`,
	ProviderPaymentFreePlanDescription:
		'Scegliendo il piano gratuito, ogni membro dello staff non avrà più accesso ai propri clienti nel tuo provider. Tuttavia, il tuo accesso rimarrà e potrai comunque utilizzare la piattaforma.',
	ProviderPaymentStepName: 'Revisione ',
	ProviderPaymentSuccessSnackbar: 'Ottimo! Il tuo nuovo piano è stato salvato correttamente.',
	ProviderPaymentTitle: 'Revisione ',
	ProviderPlanNetworkIdentificationNumber: 'Numero identificativo di rete del piano del fornitore',
	ProviderRemindersSettingsBannerAction: 'Vai alla gestione del flusso di lavoro',
	ProviderRemindersSettingsBannerDescription:
		'Trova tutti i promemoria nella nuova scheda **Gestione flusso di lavoro** in **Impostazioni**. Questo aggiornamento offre nuove funzionalità potenti, modelli migliorati e strumenti di automazione più intelligenti per aumentare la tua produttività. 🚀',
	ProviderRemindersSettingsBannerTitle: 'La tua esperienza di promemoria sta migliorando',
	ProviderTaxonomy: 'Tassonomia del fornitore',
	ProviderUPINNumber: 'Numero UPIN del fornitore',
	ProviderUsedStoragePercentage: '{providerName} storage è pieno al {usedStoragePercentage}%!',
	PsychiatricNursePractitioner: 'Infermiere psichiatrico specializzato',
	Psychiatrist: 'Psichiatra',
	Psychiatrists: 'Psichiatri',
	Psychiatry: 'Psichiatria',
	Psychoanalyst: 'Psicoanalista',
	Psychologist: 'Psicologo',
	Psychologists: 'Psicologi',
	Psychology: 'Psicologia',
	Psychometrician: 'Psicometrico',
	PsychosocialRehabilitationSpecialist: 'Specialista in riabilitazione psicosociale',
	Psychotheraphy: 'Psicoterapia',
	Psychotherapists: 'Psicoterapeuti',
	Psychotherapy: 'Psicoterapia',
	PublicCallDialogTitle: 'Videochiamata con ',
	PublicCallDialogTitlePlaceholder: 'Videochiamata fornita da Carepatron',
	PublicFormBackToForm: `Invia un'altra risposta`,
	PublicFormConfirmSubmissionHeader: 'Conferma invio',
	PublicFormNotFoundDescription: `Il modulo che stai cercando potrebbe essere stato eliminato o il collegamento potrebbe essere errato. Controlla l'URL e riprova.`,
	PublicFormNotFoundTitle: 'Modulo non trovato',
	PublicFormSubmissionError: 'Invio fallito. Riprova.',
	PublicFormSubmissionSuccess: 'Modulo inviato con successo',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} ha inviato il modulo pubblico {noteTitle}',
	PublicFormSubmittedSubtitle: 'La tua richiesta è stata ricevuta.',
	PublicFormSubmittedTitle: 'Grazie!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Ti abbiamo inviato un codice di conferma alla tua email',
	PublicFormsInvalidConfirmationCode: 'Codice di conferma non valido',
	PublicHealthInspector: 'Ispettore della sanità pubblica',
	PublicTemplates: 'Modelli pubblici',
	Publish: 'Pubblicare',
	PublishTemplate: 'Pubblica modello',
	PublishTemplateFeatureBannerSubheader: 'Modelli progettati per il beneficio della comunità',
	PublishTemplateHeader: 'Pubblica {title}',
	PublishTemplateToCommunity: 'Pubblica modello alla community',
	PublishToCommunity: 'Pubblica sulla comunità',
	PublishToCommunitySuccessMessage: 'Pubblicato con successo nella comunità',
	Published: 'Pubblicato',
	PublishedBy: 'Pubblicato da {name}',
	PublishedNotesAreNotAutosaved: 'Le note pubblicate non verranno salvate automaticamente',
	PublishedOnCarepatronCommunity: 'Pubblicato sulla comunità Carepatron',
	Purchase: 'Acquistare',
	PushToCalendar: 'Spingere al calendario',
	Question: 'Domanda',
	QuestionOrTitle: 'Domanda o titolo',
	QuickActions: 'Azioni rapide',
	QuickThemeSwitcherColorBasil: 'Basilico',
	QuickThemeSwitcherColorBlueberry: 'Mirtillo',
	QuickThemeSwitcherColorFushcia: 'Fucsia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Muschio',
	QuickThemeSwitcherColorRose: 'Rosa',
	QuickThemeSwitcherColorSquash: 'Zucca',
	RadiationTherapist: 'Terapista delle radiazioni',
	Radiologist: 'Radiologo',
	Read: 'Leggere',
	ReadOnly: 'Solo lettura',
	ReadOnlyAppointment: 'Appuntamento di sola lettura',
	ReadOnlyEventBanner:
		'Questo appuntamento è sincronizzato da un calendario di sola lettura e non può essere modificato.',
	ReaderMaxDepthHasBeenExceededCode: 'La nota è troppo annidata. Prova a rimuovere il rientro di alcuni elementi.',
	ReadyForMapping: 'Pronto per la mappatura',
	RealEstateAgent: 'Agente immobiliare',
	RearrangeClientFields: 'Riorganizza i campi del cliente nelle impostazioni del cliente',
	Reason: 'Motivo',
	ReasonForChange: 'Motivo del cambiamento',
	RecentAppointments: 'Nomine recenti',
	RecentServices: 'Servizi recenti',
	RecentTemplates: 'Modelli recenti',
	RecentlyUsed: 'Usato di recente',
	Recommended: 'Raccomandato',
	RecommendedTemplates: 'Modelli consigliati',
	Recording: 'Registrazione',
	RecordingEnded: 'Registrazione terminata',
	RecordingInProgress: 'Registrazione in corso',
	RecordingMicrophoneAccessErrorMessage: `Consenti l'accesso al microfono nel tuo browser e aggiorna la pagina per avviare la registrazione.`,
	RecurrenceCount: ', {count, plural, one {una volta} other {# volte}}',
	RecurrenceDaily: '{count, plural, one {Giornaliero} other {Giorni}}',
	RecurrenceEndAfter: 'Dopo',
	RecurrenceEndNever: 'Mai',
	RecurrenceEndOn: 'Su',
	RecurrenceEvery: 'Ogni {description}',
	RecurrenceMonthly: '{count, plural, one {Mese} other {Mesi}}',
	RecurrenceOn: 'su {description}',
	RecurrenceOnAllDays: 'in tutti i giorni',
	RecurrenceUntil: 'fino a {description}',
	RecurrenceWeekly: '{count, plural, one {Settimanale} other {Settimane}}',
	RecurrenceYearly: '{count, plural, one {Annuale} other {Anni}}',
	Recurring: 'Ricorrente',
	RecurringAppointment: 'Appuntamento ricorrente',
	RecurringAppointmentsLimitedBannerText: `Non tutti gli appuntamenti ricorrenti sono mostrati. Riduci l'intervallo di date per vedere tutti gli appuntamenti ricorrenti per il periodo.`,
	RecurringEventListDescription:
		'<b>{count, plural, one {# evento} other {# eventi}}</b> saranno creati alle seguenti date',
	Redo: 'Rifare',
	ReferFriends: 'Consiglia gli amici',
	Reference: 'Riferimento',
	ReferralCreditedNotificationSubject: 'Il tuo credito di referral di {currency} {amount} è stato applicato',
	ReferralEmailDefaultBody: `Grazie a {nome}, ti è stato inviato un aggiornamento GRATUITO di 3 mesi a Carepatron. Unisciti alla nostra community di oltre 3 milioni di professionisti sanitari creata per un nuovo modo di lavorare!
Grazie,
Il team di Carepatron`,
	ReferralEmailDefaultSubject: 'Sei stato invitato a unirti a Carepatron',
	ReferralHasNotSignedUpDescription: 'Il tuo amico non si è ancora iscritto',
	ReferralHasSignedUpDescription: 'Il tuo amico si è iscritto.',
	ReferralInformation: 'Informazioni di riferimento',
	ReferralJoinedNotificationSubject: '{actorProfileName} si è unito a Carepatron',
	ReferralListErrorDescription: `Non è stato possibile caricare l'elenco dei referral.`,
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {mese} other {mesi}}</b> attive',
	ReferralRewardBanner: 'Registrati e richiedi il tuo premio di referral!',
	Referrals: 'Riferimenti',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} mese {percentOff, select, 100 {gratuito pagato} other {{percentOff}% di sconto}} {type, select, SubscriptionUpgrade {aggiornamento} other {}}',
	ReferredUserBenefitTitle: 'Loro capiscono!',
	Referrer: 'Referente',
	ReferringProvider: 'Fornitore di riferimento',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} credito quando <mark>3 amici</mark> si attivano.',
	ReferringUserBenefitTitle: 'Hai capito!',
	RefreshPage: 'Aggiorna Pagina',
	Refund: 'Rimborso',
	RefundAcknowledgement: 'Ho restituito {clientName} al di fuori di Carepatron',
	RefundAcknowledgementValidationMessage: 'Si prega di confermare di aver rimborsato questo importo',
	RefundAmount: 'Importo del rimborso',
	RefundContent: `I rimborsi impiegano 7-10 giorni per apparire sul conto del tuo cliente. Le commissioni di pagamento non saranno rimborsate, ma non ci sono costi aggiuntivi per i rimborsi. I rimborsi non possono essere annullati e alcuni potrebbero richiedere una revisione prima dell'elaborazione.`,
	RefundCouldNotBeProcessed: 'Il rimborso non è stato elaborato',
	RefundError:
		'Al momento, questo rimborso non può essere elaborato automaticamente. Contatta il supporto Carepatron per richiedere il rimborso di questo pagamento.',
	RefundExceedTotalValidationError: `L'importo non deve superare il totale pagato`,
	RefundFailed: 'Rimborso non riuscito',
	RefundFailedTooltip: `Il rimborso di questo pagamento è fallito in precedenza e non può essere ritentato. Contatta l'assistenza.`,
	RefundNonStripePaymentContent: `Questo pagamento è stato effettuato tramite un metodo esterno a Carepatron (ad esempio, contanti, internet banking). L'emissione di un rimborso all'interno di Carepatron non restituirà alcun fondo al cliente.`,
	RefundReasonDescription:
		'Aggiungere un motivo di rimborso può essere utile quando si esaminano le transazioni dei clienti',
	Refunded: 'Rimborsato',
	Refunds: 'Rimborsi',
	RefundsTableEmptyState: 'Nessun rimborso trovato',
	Regenerate: 'Rigenera',
	RegisterButton: 'Registro',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Nome di battesimo',
	RegisterLastName: 'Cognome',
	RegisterPassword: 'Password',
	RegisteredNurse: 'Infermiere abilitato',
	RehabilitationCounselor: 'Consulente di riabilitazione',
	RejectAppointmentFormTitle: 'Non puoi venire? Facci sapere perché e proponi un nuovo orario.',
	Rejected: 'Respinto',
	Relationship: 'Relazione',
	RelationshipDetails: 'Dettagli della relazione',
	RelationshipEmptyStateTitle: 'Rimani in contatto con coloro che supportano il tuo cliente',
	RelationshipPageAccessTypeColumnName: 'Accesso al profilo',
	RelationshipSavedSuccessSnackbar: 'Relazione salvata con successo!',
	RelationshipSelectorFamilyAdmin: 'Famiglia',
	RelationshipSelectorFamilyMember: 'Membro della famiglia',
	RelationshipSelectorProviderAdmin: 'Amministratore del fornitore',
	RelationshipSelectorProviderStaff: 'Personale del fornitore',
	RelationshipSelectorSupportNetworkPrimary: 'Amico',
	RelationshipSelectorSupportNetworkSecondary: 'Rete di supporto',
	RelationshipStatus: 'Stato della relazione',
	RelationshipType: 'Tipo di relazione',
	RelationshipTypeClientOwner: 'Cliente',
	RelationshipTypeFamilyAdmin: 'Relazioni',
	RelationshipTypeFamilyMember: 'Famiglia',
	RelationshipTypeFriendOrSupport: 'Amicizia o rete di supporto',
	RelationshipTypeProviderAdmin: 'Amministratore del fornitore',
	RelationshipTypeProviderStaff: 'Personale',
	RelationshipTypeSelectorPlaceholder: 'Cerca tipi di relazione',
	Relationships: 'Relazioni',
	Remaining: 'rimanente',
	RemainingTime: '{tempo} rimanenti',
	Reminder: 'Promemoria',
	ReminderColor: 'Colore promemoria',
	ReminderDetails: 'Dettagli del promemoria',
	ReminderEditDisclaimer: 'Le modifiche si rifletteranno solo sulle nuove nomine',
	ReminderSettings: 'Impostazioni promemoria appuntamento',
	Reminders: 'Promemoria',
	Remove: 'Rimuovere',
	RemoveAccess: 'Rimuovi accesso',
	RemoveAllGuidesBtn: 'Rimuovi tutte le guide',
	RemoveAllGuidesPopoverBody:
		'Una volta completate le guide di onboarding, basta utilizzare il pulsante Rimuovi guide presente in ciascun pannello.',
	RemoveAllGuidesPopoverTitle: 'Non hai più bisogno delle guide di onboarding?',
	RemoveAsDefault: 'Rimuovi come predefinito',
	RemoveAsIntake: 'Rimuovere come assunzione',
	RemoveCol: 'Rimuovi colonna',
	RemoveColor: 'Rimuovi colore',
	RemoveField: 'Rimuovi campo',
	RemoveFromCall: 'Rimuovi dalla chiamata',
	RemoveFromCallDescription: 'Sei sicuro di voler rimuovere <mark>{attendeeName}</mark> da questa videochiamata?',
	RemoveFromCollection: 'Rimuovi dalla raccolta',
	RemoveFromCommunity: 'Rimuovi dalla comunità',
	RemoveFromFolder: 'Rimuovi dalla cartella',
	RemoveFromFolderConfirmationDescription:
		'Sei sicuro di voler rimuovere questo modello da questa cartella? Questa azione non può essere annullata, ma puoi scegliere di spostarlo di nuovo in seguito.',
	RemoveFromIntakeDefault: `Rimuovi dall'assunzione predefinita`,
	RemoveGuides: 'Rimuovi guide',
	RemoveMfaConfirmationDescription: `La rimozione dell'autenticazione a più fattori (MFA) ridurrà la sicurezza del tuo account. Vuoi procedere?`,
	RemoveMfaConfirmationTitle: 'Vuoi rimuovere MFA?',
	RemovePaymentMethodDescription: `Questo eliminerà ogni accesso e utilizzo futuro di questo metodo di pagamento.
 Questa azione non può essere annullata.`,
	RemoveRow: 'Rimuovi riga',
	RemoveTable: 'Rimuovi tabella',
	RemoveTemplateAsDefaultIntakeSuccess:
		'Rimosso correttamente {templateTitle} come modello di assunzione predefinito',
	RemoveTemplateFromCommunity: 'Rimuovi modello dalla community',
	RemoveTemplateFromFolder: '{templateTitle} rimosso correttamente da {folderTitle}',
	Rename: 'Rinominare',
	RenderingProvider: 'Fornitore di rendering',
	Reopen: 'Riaprire',
	ReorderServiceGroupFailure: 'Impossibile riordinare la raccolta',
	ReorderServiceGroupSuccess: 'Raccolta riordinata con successo',
	ReorderServicesFailure: 'Impossibile riordinare i servizi',
	ReorderServicesSuccess: 'Servizi riordinati con successo',
	ReorderYourServiceList: `Riordina l'elenco dei tuoi servizi`,
	ReorderYourServiceListDescription:
		'Il modo in cui organizzi i tuoi servizi e le tue collezioni si rifletterà sulla tua pagina di prenotazione online, visibile a tutti i tuoi clienti!',
	RepeatEvery: 'Ripeti ogni',
	RepeatOn: 'Ripeti su',
	Repeating: 'Ripetendo',
	Repeats: 'Ripetizioni',
	RepeatsEvery: 'Si ripete ogni',
	Rephrase: 'Riformulare',
	Replace: 'Sostituire',
	ReplaceBackground: 'Sostituisci sfondo',
	ReplacementOfPriorClaim: 'Sostituzione del reclamo precedente',
	Report: 'Rapporto',
	Reprocess: 'Rielaborare',
	RepublishTemplateToCommunity: 'Ripubblica modello alla community',
	RequestANewVerificationLink: 'Richiedi un nuovo link di verifica',
	RequestCoverageReport: 'Richiedi il report di copertura',
	RequestingDevicePermissions: 'Richiesta autorizzazioni dispositivo...',
	RequirePaymentMethodDesc: 'I clienti devono inserire i dati della propria carta di credito per prenotare online',
	RequirePaymentMethodLabel: 'Richiedi i dettagli della carta di credito',
	Required: 'necessario',
	RequiredField: 'Necessario',
	RequiredUrl: `L'URL è obbligatorio.`,
	Reschedule: 'Riprogrammare',
	RescheduleBookingLinkModalDescription: `Il tuo cliente può modificare la data e l'ora del suo appuntamento utilizzando questo link.`,
	RescheduleBookingLinkModalTitle: 'Link per riprogrammare la prenotazione',
	RescheduleLink: 'Riprogramma collegamento',
	Resend: 'Invia di nuovo',
	ResendConfirmationCode: 'Invia nuovamente il codice di conferma',
	ResendConfirmationCodeDescription: 'Inserisci il tuo indirizzo email e ti invieremo un altro codice di conferma',
	ResendConfirmationCodeSuccess: 'Il codice di conferma è stato reinviato, controlla la tua posta in arrivo',
	ResendNewEmailVerificationSuccess: 'Nuova email di verifica inviata a {email}',
	ResendVerificationEmail: `Invia nuovamente l'email di verifica`,
	Reset: 'Reset',
	Resources: 'Risorse',
	RespiratoryTherapist: 'Terapista respiratorio',
	RespondToHistoricAppointmentError:
		'Si tratta di un appuntamento storico, per qualsiasi domanda contattate il vostro medico.',
	Responder: 'Risponditore',
	RestorableItemModalDescription:
		'Sei sicuro di voler eliminare {context}?{canRestore, select, true { Puoi ripristinarlo in seguito.} other {}}',
	RestorableItemModalTitle: 'Elimina {tipo}',
	Restore: 'Ripristinare',
	RestoreAll: 'Ripristina tutto',
	Restricted: 'Limitato',
	ResubmissionCodeReferenceNumber: 'Codice di reinvio e numero di riferimento',
	Resubmit: 'Reinvia',
	Resume: 'Riprendere',
	Retry: 'Riprova',
	RetryingConnectionAttempt: 'Riconnessione in corso... (Tentativo {retryCount} di {maxRetries})',
	ReturnToForm: 'Ritorna al modulo',
	RevertClaimStatus: 'Ripristina stato del reclamo',
	RevertClaimStatusDescriptionBody: `Questa richiesta ha pagamenti collegati, e cambiare lo stato potrebbe influenzare il tracciamento o l'elaborazione dei pagamenti, il che potrebbe richiedere una riconciliazione manuale.`,
	RevertClaimStatusDescriptionTitle: 'Sei sicuro di voler tornare a {status}?',
	RevertClaimStatusError: 'Impossibile ripristinare lo stato della richiesta',
	RevertToDraft: 'Ripristina bozza',
	Review: 'Revisione',
	ReviewsFirstQuote: 'Appuntamenti ovunque, in qualsiasi momento',
	ReviewsSecondJobTitle: 'Clinica Lifehouse',
	ReviewsSecondName: 'Chiara W.',
	ReviewsSecondQuote: `Adoro anche l'app Carepatron. Mi aiuta a tenere traccia dei miei clienti e a lavorare mentre sono in giro.`,
	ReviewsThirdJobTitle: 'Clinica della baia di Manila',
	ReviewsThirdName: 'di Jackie H.',
	ReviewsThirdQuote:
		'La semplicità di navigazione e la splendida interfaccia utente mi strappano un sorriso ogni giorno.',
	RightAlign: 'Allinea a destra',
	Role: 'Ruolo',
	Roster: 'Partecipanti',
	RunInBackground: 'Esegui in background',
	SMS: 'sms',
	SMSAndEmailReminder: 'sms ',
	SSN: 'Numero di previdenza sociale',
	SafetyRedirectHeading: 'Stai lasciando Carepatron',
	SafetyRedirectSubtext: 'Se ti fidi di questo collegamento, selezionalo per continuare',
	SalesRepresentative: 'Rappresentante di vendita',
	SalesTax: 'Imposta sulle vendite',
	SalesTaxHelp: `Include l'imposta sulle vendite sulle fatture generate`,
	SalesTaxIncluded: 'SÌ',
	SalesTaxNotIncluded: 'NO',
	SaoPaulo: 'San Paolo',
	Saturday: 'Sabato',
	Save: 'Salva',
	SaveAndClose: 'Salva ',
	SaveAndExit: 'Salva ',
	SaveAndLock: 'Salva e blocca',
	SaveAsDraft: 'Salva come bozza',
	SaveCardForFuturePayments: 'Salva la carta per i pagamenti futuri',
	SaveChanges: 'Salvare le modifiche',
	SaveCollection: 'Salva la raccolta',
	SaveField: 'Salva campo',
	SavePaymentMethod: 'Salva il metodo di pagamento',
	SavePaymentMethodDescription: 'Non ti verrà addebitato alcun costo fino al tuo primo appuntamento.',
	SavePaymentMethodSetupError:
		'Si è verificato un errore imprevisto e al momento non siamo riusciti a configurare i pagamenti.',
	SavePaymentMethodSetupInvoiceLater:
		'I pagamenti possono essere impostati e salvati al momento del pagamento della prima fattura.',
	SaveSection: 'Salva la sezione',
	SaveService: 'Crea nuovo servizio',
	SaveTemplate: 'Salva modello',
	Saved: 'Salvato',
	SavedCards: 'Carte salvate',
	SavedPaymentMethods: 'Salvato',
	Saving: 'Risparmio...',
	ScheduleAppointmentsAndOnlineServices: 'Prenota appuntamenti e servizi online',
	ScheduleName: 'Nome del programma',
	ScheduleNew: 'Pianifica nuovo',
	ScheduleSend: 'Pianifica invio',
	ScheduleSendAlertInfo: `Le conversazioni programmate verranno inviate all'orario previsto.`,
	ScheduleSendByName: '**Pianificazione invio** • {time} di {displayName}',
	ScheduleSetupCall: 'Organizza una chiamata di configurazione',
	Scheduled: 'Programmato',
	SchedulingSend: 'Pianificazione invio',
	School: 'Scuola',
	ScrollToTop: `Scorri verso l'alto`,
	Search: 'Ricerca',
	SearchAndConvertToLanguage: 'Cerca e converti in lingua',
	SearchBasicBlocks: 'Cerca blocchi base',
	SearchByName: 'Cerca per nome',
	SearchClaims: 'Ricerca reclami',
	SearchClientFields: 'Cerca campi client',
	SearchClients: 'Cerca per nome cliente, ID cliente o numero di telefono',
	SearchCommandNotFound: 'Nessun risultato trovato per "{searchTerm}"',
	SearchContacts: 'Cliente o contatto',
	SearchContactsPlaceholder: 'Cerca contatti',
	SearchConversations: 'Cerca conversazioni',
	SearchInputPlaceholder: 'Cerca tutte le risorse',
	SearchInvoiceNumber: 'Cerca numero fattura',
	SearchInvoices: 'Cerca fatture',
	SearchMultipleContacts: 'Clienti o contatti',
	SearchMultipleContactsOptional: 'Clienti o contatti (facoltativo)',
	SearchOrCreateATag: 'Cerca o crea un tag',
	SearchPayments: 'Cerca pagamenti',
	SearchPrepopulatedData: 'Cerca campi dati precompilati',
	SearchRelationships: 'Cerca relazioni',
	SearchRemindersAndWorkflows: 'Cerca promemoria e flussi di lavoro',
	SearchServices: 'Servizi di ricerca',
	SearchTags: 'Cerca tag',
	SearchTeamMembers: 'Cerca i membri del team',
	SearchTemplatePlaceholder: 'Cerca {templateCount}+ risorse',
	SearchTimezone: 'Cerca fuso orario...',
	SearchTrashItems: 'Cerca elementi',
	SearchUnsplashPlaceholder: 'Cerca foto ad alta risoluzione gratuite da Unsplash',
	Secondary: 'Secondario',
	SecondaryInsurance: 'Assicurazione secondaria',
	SecondaryPolicy: 'Assicurazione secondaria',
	SecondaryTimezone: 'Fuso orario secondario',
	Secondly: 'In secondo luogo',
	Section: 'Sezione',
	SectionCannotBeEmpty: 'Una sezione deve avere almeno una riga',
	SectionFormSecondaryText: 'Titolo e descrizione della sezione',
	SectionName: 'Nome della sezione',
	Sections: 'Sezioni',
	SeeLess: 'Vedi meno',
	SeeLessUpcomingAppointments: 'Visualizza meno appuntamenti imminenti',
	SeeMore: 'Vedi altro',
	SeeMoreUpcomingAppointments: 'Vedi altri prossimi appuntamenti',
	SeeTemplateLibrary: 'Vedi la libreria dei modelli',
	Seen: 'Visto',
	SeenByName: '**Visto** • {time} da {displayName}',
	SelectAll: 'Seleziona tutto',
	SelectAssignees: 'Seleziona gli assegnatari',
	SelectAttendees: 'Seleziona i partecipanti',
	SelectCollection: 'Seleziona la raccolta',
	SelectCorrespondingAttributes: 'Seleziona gli attributi corrispondenti',
	SelectPayers: 'Seleziona i pagatori',
	SelectProfile: 'Seleziona profilo',
	SelectServices: 'Seleziona i servizi',
	SelectTags: 'Seleziona i tag',
	SelectTeamOrCommunity: 'Seleziona Team o Community',
	SelectTemplate: 'Seleziona modello',
	SelectType: 'Seleziona il tipo',
	Selected: 'Selezionato',
	SelfPay: 'Pagamento autonomo',
	Send: 'Inviare',
	SendAndClose: 'Inviare ',
	SendAndStopIgnore: 'Invia e smetti di ignorare',
	SendEmail: 'Invia email',
	SendIntake: 'Invia assunzione',
	SendIntakeAndForms: 'Invia assunzione ',
	SendMeACopy: 'Inviami una copia',
	SendNotificationEmailWarning:
		'Alcuni partecipanti non hanno un indirizzo email e non riceveranno notifiche e promemoria automatici.',
	SendNotificationLabel: `Scegli i partecipanti da notificare con un'email`,
	SendOnlinePayment: 'Invia pagamento online',
	SendOnlinePaymentTooltipTitleAdmin: 'Aggiungi le tue impostazioni di pagamento preferite',
	SendOnlinePaymentTooltipTitleStaff: 'Chiedi al proprietario del fornitore di impostare i pagamenti online.',
	SendPaymentLink: 'Invia il link di pagamento',
	SendReaction: 'Invia una reazione',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Invia email di verifica',
	SendingFailed: 'Invio non riuscito',
	Sent: 'Inviato',
	SentByName: '**Inviato** • {time} da {displayName}',
	Seoul: 'Seul',
	SeparateDuplicateClientsDescription:
		'I record dei clienti scelti rimarranno separati dagli altri a meno che non si scelga di unirli',
	Service: 'Servizio',
	'Service/s': 'Servizio/i',
	ServiceAdjustment: 'Regolazione del servizio',
	ServiceAllowNewClientsIndicator: 'Consenti nuovi clienti',
	ServiceAlreadyExistsInCollection: 'Il servizio esiste già nella raccolta',
	ServiceBookableOnlineIndicator: 'Prenotabile online',
	ServiceCode: 'Codice',
	ServiceCodeErrorMessage: 'È richiesto un codice di servizio',
	ServiceCodeSelectorPlaceholder: 'Aggiungi un codice di servizio',
	ServiceColour: 'Colore del servizio',
	ServiceCoverageDescription: 'Scegli i servizi ammissibili e il co-pagamento per questa polizza assicurativa.',
	ServiceCoverageGoToServices: 'Vai ai servizi',
	ServiceCoverageNoServicesDescription:
		'Personalizza gli importi del co-pagamento del servizio per sostituire il co-pagamento predefinito della polizza. Disattiva la copertura per impedire che i servizi vengano richiesti in base alla polizza.',
	ServiceCoverageNoServicesLabel: 'Non sono stati trovati servizi.',
	ServiceCoverageTitle: 'Copertura del servizio',
	ServiceDate: 'Data del servizio',
	ServiceDetails: 'Dettagli del servizio',
	ServiceDuration: 'Durata',
	ServiceEmptyState: 'Non ci sono ancora servizi',
	ServiceErrorMessage: 'Il servizio è richiesto',
	ServiceFacility: 'Struttura di servizio',
	ServiceName: 'Nome del servizio',
	ServiceRate: 'Valutare',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} per {serviceReceiptNumber, select, undefined {user} other {{clientName}}} richiede informazioni aggiuntive',
	ServiceSalesTax: 'Imposta sulle vendite',
	ServiceType: 'Servizio',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Premi "Ricarica" per aggiornare e ricevere gli ultimi aggiornamenti di Carepatron.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Ricaricare',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Stai utilizzando una versione precedente',
	ServiceWorkerForceUIUpdateDialogTitle: 'Bentornato!',
	Services: 'Servizi',
	ServicesAndAvailability: 'Servizi ',
	ServicesAndDiagnosisCodesHeader: 'Aggiungere servizi e codici di diagnosi',
	ServicesCount: '{count,plural,=0{Servizi}one{Servizio}other{Servizi}}',
	ServicesPlaceholder: 'Servizi',
	ServicesProvidedBy: 'Servizio/i fornito/i da',
	SetAPhysicalAddress: 'Imposta un indirizzo fisico',
	SetAVirtualLocation: 'Imposta una posizione virtuale',
	SetAsDefault: 'Imposta come predefinito',
	SetAsIntake: 'Imposta come assunzione',
	SetAsIntakeDefault: 'Imposta come assunzione predefinita',
	SetAvailability: 'Imposta disponibilità',
	SetTemplateAsDefaultIntakeSuccess: 'Impostata correttamente {templateTitle} come modello di intake predefinito',
	SetUpMfaButton: 'Imposta MFA',
	SetYourLocation: 'Imposta il tuo<mark> posizione</mark>',
	SetYourLocationDescription: 'Non ho un indirizzo aziendale <span>(solo servizi online e mobili)</span>',
	SettingUpPayers: 'Impostazione dei pagatori',
	Settings: 'Impostazioni',
	SettingsNewUserPasswordDescription:
		'Una volta effettuata la registrazione, ti invieremo un codice di conferma che potrai utilizzare per confermare il tuo account',
	SettingsNewUserPasswordTitle: 'Iscriviti a Carepatron',
	SettingsTabAutomation: 'Automazione',
	SettingsTabBillingDetails: 'Dettagli di fatturazione',
	SettingsTabConnectedApps: 'App connesse',
	SettingsTabCustomFields: 'Campi personalizzati',
	SettingsTabDetails: 'Dettagli',
	SettingsTabInvoices: 'Fatture',
	SettingsTabLocations: 'Posizioni',
	SettingsTabNotifications: 'Notifiche',
	SettingsTabOnlineBooking: 'Prenotazione online',
	SettingsTabPayers: 'Pagatori',
	SettingsTabReminders: 'Promemoria',
	SettingsTabServices: 'Servizi',
	SettingsTabServicesAndAvailability: 'Servizi e disponibilità',
	SettingsTabSubscriptions: 'Abbonamenti',
	SettingsTabWorkflowAutomations: 'Automazioni',
	SettingsTabWorkflowReminders: 'Promemoria di base',
	SettingsTabWorkflowTemplates: 'Modelli',
	Setup: 'Impostare',
	SetupGuide: 'Guida alla configurazione',
	SetupGuideAddServicesActionLabel: 'Inizio',
	SetupGuideAddServicesSubtitle: '4 passaggi • 2 min',
	SetupGuideAddServicesTitle: 'Aggiungi i tuoi servizi',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Inizio',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 passaggi • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Abilita i pagamenti online',
	SetupGuideImportClientsActionLabel: 'Inizio',
	SetupGuideImportClientsSubtitle: '4 passaggi • 3 min',
	SetupGuideImportClientsTitle: 'Importa i tuoi clienti',
	SetupGuideImportTemplatesActionLabel: 'Inizio',
	SetupGuideImportTemplatesSubtitle: '2 passaggi • 1 min',
	SetupGuideImportTemplatesTitle: 'Importa i tuoi modelli',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Inizio',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 passaggi • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personalizza il tuo spazio di lavoro',
	SetupGuideSetLocationActionLabel: 'Inizio',
	SetupGuideSetLocationSubtitle: '4 passaggi • 1 min',
	SetupGuideSetLocationTitle: 'Imposta la tua posizione',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Invita team',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Invita il tuo team a comunicare e gestire i compiti senza sforzo.',
	SetupGuideSuggestedAddTeamMembersTag: 'Imposta',
	SetupGuideSuggestedAddTeamMembersTitle: 'Aggiungi membri del team',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Personalizza',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Sentiti professionale con il tuo logo unico e i colori del brand.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Personalizza il marchio',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Scarica',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Accedi al tuo spazio di lavoro ovunque, in qualsiasi momento e su qualsiasi dispositivo.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Impostazione',
	SetupGuideSuggestedDownloadMobileAppTitle: `Scarica l'app`,
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Imposta la disponibilità',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Previeni le doppie prenotazioni impostando la tua disponibilità.',
	SetupGuideSuggestedEditAvailabilityTag: 'Pianificazione',
	SetupGuideSuggestedEditAvailabilityTitle: 'Modifica disponibilità',
	SetupGuideSuggestedImportClientsActionLabel: 'Importa',
	SetupGuideSuggestedImportClientsSubtitle:
		'Carica istantaneamente i dati dei tuoi clienti esistenti con un solo clic.',
	SetupGuideSuggestedImportClientsTag: 'Impostazione',
	SetupGuideSuggestedImportClientsTitle: 'Importa clienti',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Modifica promemoria',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Riduci le mancate presentazioni con promemoria di appuntamenti automatici.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Personalizza i promemoria',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Inizia chiamata',
	SetupGuideSuggestedStartVideoCallSubtitle: `Ospita una chiamata e connettiti con i clienti utilizzando i nostri strumenti video basati sull'intelligenza artificiale.`,
	SetupGuideSuggestedStartVideoCallTag: 'Telemedicina',
	SetupGuideSuggestedStartVideoCallTitle: 'Inizia videochiamata',
	SetupGuideSuggestedTryActionsTitle: 'Cose da provare 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: `Prova l'assistenza AI`,
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Ottieni risposte immediate a tutte le tue domande di lavoro.',
	SetupGuideSuggestedUseAIAssistantTag: 'Nuovo',
	SetupGuideSuggestedUseAIAssistantTitle: `Utilizza l'assistente AI`,
	SetupGuideSyncCalendarActionLabel: 'Inizio',
	SetupGuideSyncCalendarSubtitle: '1 passaggio • meno di 1 minuto',
	SetupGuideSyncCalendarTitle: 'Sincronizza il tuo calendario',
	SetupGuideVerifyEmailLabel: 'Verifica',
	SetupGuideVerifyEmailSubtitle: '2 passaggi • 2 min',
	SetupOnlineStripePayments: 'Utilizza Stripe per i pagamenti online',
	SetupPayments: 'Imposta i pagamenti',
	Sex: 'Sesso',
	SexSelectorPlaceholder: 'Maschio / Femmina / Preferisco non dirlo',
	Share: 'Condividere',
	ShareBookingLink: 'Condividi il link di prenotazione',
	ShareNoteDefaultMessage: `Ciao{name} ha condiviso "{documentName}" con te.

Grazie,
{practiceName}`,
	ShareNoteMessage: `Ciao
{name} ha condiviso "{documentName}" {isResponder, select, true {con alcune domande da compilare.} other {con te.}}

Grazie,
{practiceName}`,
	ShareNoteTitle: 'Condividi ‘{noteTitle}’',
	ShareNotesWithClients: 'Condividi con clienti o contatti',
	ShareScreen: 'Condividi schermo',
	ShareScreenNotSupported: 'Il tuo dispositivo/browser non supporta la funzione di condivisione dello schermo',
	ShareScreenWithId: 'Schermo {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Consenti ad altri di visualizzare questo modello e di inviarlo come modulo.',
	ShareTemplateAsPublicFormModalTitle: 'Condividi link per ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Configurazione del modulo pubblico aggiornata correttamente',
	ShareTemplateAsPublicFormSectionCustomization: 'Personalizzazione',
	ShareTemplateAsPublicFormShowPoweredBy: 'Mostra "Powered by Carepatron" sul mio modulo',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Mostra/nascondi “Powered by Carepatron” sul mio modulo',
	ShareTemplateAsPublicFormTrigger: 'Condividi',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Utilizza il branding dello spazio di lavoro',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Mostra/nascondi la marca dello spazio di lavoro',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: 'Invia codice per clienti esistenti e non esistenti',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: `Le firme richiedono sempre la verifica dell'email`,
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Invia codice solo per i clienti esistenti',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Non invia mai codice',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Selezionando "Mai" potrebbe consentire a utenti non verificati di sovrascrivere i dati del cliente se utilizzano l'indirizzo email di un cliente esistente.`,
	ShareWithCommunity: 'Condividi con la comunità',
	ShareYourReferralLink: 'Condividi il tuo link di riferimento',
	ShareYourScreen: 'Condividi il tuo schermo',
	SheHer: 'Lei/La sua',
	ShortTextAnswer: 'Risposta breve in forma di testo',
	ShortTextFormPrimaryText: 'Testo breve',
	ShortTextFormSecondaryText: 'Risposta di meno di 300 caratteri',
	Show: 'Spettacolo',
	ShowColumn: 'Mostra colonna',
	ShowColumnButton: 'Mostra il pulsante colonna {value}',
	ShowColumns: 'Mostra colonne',
	ShowColumnsMenu: 'Mostra menu colonne',
	ShowDateDurationDescription: 'ad esempio 29 anni',
	ShowDateDurationLabel: 'Mostra durata data',
	ShowDetails: 'Mostra dettagli',
	ShowField: 'Mostra campo',
	ShowFullAddress: 'Mostra indirizzo',
	ShowHideFields: 'Mostra / Nascondi campi',
	ShowIcons: 'Mostra icone',
	ShowLess: 'Mostra meno',
	ShowMeetingTimers: 'Mostra i timer delle riunioni',
	ShowMenu: 'Mostra il menu',
	ShowMergeSummarySidebar: 'Mostra riepilogo della fusione',
	ShowMore: 'Mostra altro',
	ShowOnTranscript: 'Mostra nella trascrizione',
	ShowReactions: 'Mostra reazioni',
	ShowSection: 'Mostra sezione',
	ShowServiceCode: 'Mostra codice servizio',
	ShowServiceDescription: 'Mostra la descrizione sulle prenotazioni dei servizi',
	ShowServiceDescriptionDesc:
		'I clienti possono visualizzare le descrizioni dei servizi al momento della prenotazione',
	ShowServiceGroups: 'Mostra collezioni',
	ShowServiceGroupsDesc:
		'Ai clienti verranno mostrati i servizi raggruppati per collezione al momento della prenotazione',
	ShowSpeakers: 'Mostra relatori',
	ShowTax: 'Mostra imposta',
	ShowTimestamp: 'Mostra timestamp',
	ShowUnits: 'Mostra unità',
	ShowWeekends: 'Mostra i fine settimana',
	ShowYourView: 'Mostra la tua opinione',
	SignInWithApple: 'Accedi con Apple',
	SignInWithGoogle: 'Accedi con Google',
	SignInWithMicrosoft: 'Accedi con Microsoft',
	SignUpTitleReferralDefault: '<mark>Iscrizione</mark> e richiedi il tuo premio di referral',
	SignUpTitleReferralUpgrade:
		'Inizia il tuo {durationInMonths} mese <mark>{percentOff, select, 100 {gratuito} other {{percentOff}% di sconto}} aggiornamento</mark>',
	SignatureCaptureError: 'Impossibile catturare la firma. Riprova.',
	SignatureFormPrimaryText: 'Firma',
	SignatureFormSecondaryText: 'Ottieni una firma digitale',
	SignatureInfoTooltip: 'Questa rappresentazione visiva non costituisce una firma elettronica valida.',
	SignaturePlaceholder: 'Disegna qui la tua firma',
	SignedBy: 'Firmato da',
	Signup: 'Iscrizione',
	SignupAgreements: `Accetto i {termsOfUse} e l'{privacyStatement} per il mio account.`,
	SignupBAA: 'Contratto di associazione commerciale',
	SignupBusinessAgreements: `A nome mio e dell'azienda, accetto il {businessAssociateAgreement}, i {termsOfUse} e il {privacyStatement} per il mio account.`,
	SignupInvitationForYou: 'Sei stato invitato a utilizzare Carepatron.',
	SignupPageProviderWarning:
		'Se il tuo amministratore ha già creato un account, devi chiedergli di invitarti in quel provider. Non usare questo modulo di iscrizione. Per maggiori informazioni, vedi',
	SignupPageProviderWarningLink: 'questo collegamento.',
	SignupPrivacy: 'politica sulla riservatezza',
	SignupProfession: 'Quale professione ti descrive meglio?',
	SignupSubtitle:
		'Il software di gestione della pratica di Carepatron è fatto per professionisti singoli e team. Smetti di pagare tariffe eccessive e diventa parte della rivoluzione.',
	SignupSuccessDescription: `Conferma il tuo indirizzo email per iniziare l'onboarding. Se non lo ricevi subito, controlla la cartella spam.`,
	SignupSuccessTitle: 'Per favore controlla la tua email',
	SignupTermsOfUse: 'Termini di utilizzo',
	SignupTitleClient: '<mark>Gestisci la tua salute</mark> da un posto',
	SignupTitleLast: 'e tutto il lavoro che fai! — È gratis',
	SignupTitleOne: '<mark>Ti alimenta</mark> , ',
	SignupTitleThree: '<mark>Potenziare i tuoi clienti</mark> , ',
	SignupTitleTwo: '<mark>Potenziare il tuo team</mark> , ',
	Simple: 'Semplice',
	SimplifyBillToDetails: 'Semplifica la fattura nei dettagli',
	SimplifyBillToHelperText: 'Solo la prima riga viene utilizzata quando corrisponde al client',
	Singapore: 'Singapore',
	Single: 'Separare',
	SingleChoiceFormPrimaryText: 'Scelta singola',
	SingleChoiceFormSecondaryText: `Scegli solo un'opzione`,
	Sister: 'Sorella',
	SisterInLaw: 'Cognata',
	Skip: 'Saltare',
	SkipLogin: `Salta l'accesso`,
	SlightBlur: 'Sfoca leggermente lo sfondo',
	Small: 'Piccolo',
	SmartChips: 'Chip intelligenti',
	SmartDataChips: 'Chip di dati intelligenti',
	SmartReply: 'Risposta rapida',
	SmartSuggestNewClient: '**Smart Suggest** crea {name} come nuovo cliente',
	SmartSuggestedFieldDescription: 'Questo campo è un suggerimento intelligente',
	SocialSecurityNumber: 'Numero di Social Security',
	SocialWork: 'Lavoro sociale',
	SocialWorker: 'Assistente sociale',
	SoftwareDeveloper: 'Sviluppatore di software',
	Solo: 'Solo',
	Someone: 'Qualcuno',
	Son: 'Figlio',
	SortBy: 'Ordina per',
	SouthAmerica: 'Sud America',
	Speaker: 'Oratore',
	SpeakerSource: `Sorgente dell'altoparlante`,
	Speakers: 'Altoparlanti',
	SpecifyPaymentMethod: 'Specificare il metodo di pagamento',
	SpeechLanguagePathology: 'Patologia del linguaggio e della parola',
	SpeechTherapist: 'Logopedista',
	SpeechTherapists: 'Logopedisti',
	SpeechTherapy: 'Logopedia',
	SportsMedicinePhysician: 'Medico specialista in medicina sportiva',
	Spouse: 'Coniuge',
	SpreadsheetColumnExample: 'per esempio ',
	SpreadsheetColumns: 'Colonne del foglio di calcolo',
	SpreadsheetUploaded: 'Foglio di calcolo caricato',
	SpreadsheetUploading: 'Caricamento in corso...',
	Staff: 'Personale',
	StaffAccessDescriptionAdmin: 'Gli amministratori possono gestire tutto sulla piattaforma.',
	StaffAccessDescriptionStaff: `I membri dello staff possono gestire i clienti, le note e la documentazione che hanno creato o che è stata condivisa
 con loro, pianificare appuntamenti, gestire fatture.`,
	StaffContactAssignedSubject:
		'{actorProfileName} ha assegnato {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} e {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { e 1 altro cliente} other { e # altri clienti}} a te',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} ha condiviso la casella di posta {inboxName} con te',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} ha rimosso il tuo accesso alla casella di posta {inboxName}',
	StaffMembers: 'Membri dello staff',
	StaffMembersNumber: '{billedUsers, plural, one {# membro del team} other {# membri del team}}',
	StaffSavedSuccessSnackbar: 'Informazioni sui membri del team salvate correttamente!',
	StaffSelectorAdminRole: 'Amministratore',
	StaffSelectorStaffRole: 'Membro dello staff',
	StandardAppointment: 'Appuntamento standard',
	StandardColor: `Colore dell'attività`,
	StartAndEndTime: 'Ora di inizio e di fine',
	StartCall: 'Avvia chiamata',
	StartDate: 'Data di inizio',
	StartDictating: 'Inizia a dettare',
	StartImport: `Inizia l'importazione`,
	StartRecordErrorTitle: `Si è verificato un errore durante l'avvio della registrazione`,
	StartRecording: 'Inizia la registrazione',
	StartTimeIncrements: 'Incrementi di tempo di inizio',
	StartTimeIncrementsView: '{startTimeIncrements} min intervalli',
	StartTranscribing: 'Inizia la trascrizione',
	StartTranscribingNotes:
		'Seleziona i clienti per i quali vuoi generare la nota. Quindi clicca sul pulsante "Avvia trascrizione" per iniziare la registrazione',
	StartTranscription: 'Inizia trascrizione',
	StartVideoCall: 'Avvia la videochiamata',
	StartWeekOn: 'Inizia la settimana il',
	StartedBy: 'Iniziato da ',
	Starter: 'Antipasto',
	State: 'Stato',
	StateIndustrialAccidentProviderNumber: 'Numero del fornitore di incidenti industriali statali',
	StateLicenseNumber: 'Numero di licenza statale',
	Statement: 'Dichiarazione',
	StatementDescriptor: 'Descrittore della dichiarazione',
	StatementDescriptorToolTip: `Il descrittore dell'estratto conto è mostrato sugli estratti conto bancari o delle carte di credito dei tuoi clienti. Deve essere compreso tra 5 e 22 caratteri e riflettere il nome della tua attività.`,
	StatementNumber: 'Dichiarazione #',
	Status: 'Stato',
	StatusFieldPlaceholder: `Inserisci un'etichetta di stato`,
	StepFather: 'Patrigno',
	StepMother: 'Matrigna',
	Stockholm: 'Stoccolma',
	StopIgnoreSendersDescription: `Smettendo di ignorare questi mittenti, le conversazioni future verranno inviate a 'Posta in arrivo'. Vuoi davvero smettere di ignorare questi mittenti?`,
	StopIgnoring: 'Smettila di ignorare',
	StopIgnoringSenders: 'Smettila di ignorare i mittenti',
	StopIgnoringSendersSuccess: `Ha smesso di ignorare l'indirizzo email <mark>{addresses}</mark>`,
	StopSharing: 'Interrompere la condivisione',
	StopSharingLabel: 'carepatron.com sta condividendo il tuo schermo.',
	Storage: 'Magazzinaggio',
	StorageAlmostFullDescription: `🚀 Esegui subito l'upgrade per far sì che il tuo account continui a funzionare senza problemi.`,
	StorageAlmostFullTitle: 'Hai utilizzato il {percentage}% del limite di archiviazione dello spazio di lavoro!',
	StorageFullDescription: 'Ottieni più spazio di archiviazione aggiornando il tuo piano.',
	StorageFullTitle: '	Lo spazio di archiviazione è pieno.',
	Street: 'Strada',
	StripeAccountNotCompleteErrorCode:
		'I pagamenti online non sono {hasProviderName, select, true {configurati per {providerName}} other {abilitati per questo provider}}.',
	StripeAccountRejectedError: `L'account Stripe è stato rifiutato. Contatta l'assistenza.`,
	StripeBalance: 'Bilanciamento Stripe',
	StripeChargesInfoToolTip: 'Consente di addebitare il debito ',
	StripeFeesDescription:
		'Carepatron utilizza Stripe per farti pagare rapidamente e proteggere le tue informazioni di pagamento. I metodi di pagamento disponibili variano in base alla regione, tutti i principali debito ',
	StripeFeesDescriptionItem1:
		'Elaborazione delle commissioni viene applicata a ogni transazione riuscita, è possibile {link}.',
	StripeFeesDescriptionItem2:
		'I pagamenti avvengono quotidianamente, ma vengono trattenuti per un massimo di 4 giorni.',
	StripeFeesLinkToRatesText: 'visualizza le nostre tariffe qui',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Mi dispiace, è richiesto un importo minimo di {minimumAmount} per le fatture che utilizzano i pagamenti online',
	StripePaymentsDisabled: 'Pagamenti online disabilitati. Si prega di controllare le impostazioni di pagamento.',
	StripePaymentsUnavailable: 'Pagamenti non disponibili',
	StripePaymentsUnavailableDescription:
		'Si è verificato un errore durante il caricamento dei pagamenti. Riprova più tardi.',
	StripePayoutsInfoToolTip: 'Ti consente di ricevere il pagamento sul tuo conto bancario',
	StyleYourWorkspace: '<mark>Stile</mark> il tuo spazio di lavoro',
	StyleYourWorkspaceDescription1:
		'Abbiamo recuperato le risorse del marchio dal tuo sito web. Sentiti libero di modificarle o continua al tuo spazio di lavoro Carepatron.',
	StyleYourWorkspaceDescription2: `Utilizza i tuoi asset di marca per personalizzare le fatture e le prenotazioni online per un'esperienza cliente senza soluzione di continuità.`,
	SubAdvanced: 'Avanzato',
	SubEssential: 'Essenziale',
	SubOrganization: 'Organizzazione',
	SubPlus: 'Più',
	SubProfessional: 'Professionale',
	Subject: 'Soggetto',
	Submit: 'Invia',
	SubmitElectronically: 'Invia elettronicamente',
	SubmitFeedback: 'Invia feedback',
	SubmitFormValidationError:
		'Assicurati che tutti i campi obbligatori siano compilati correttamente e prova a inviare nuovamente.',
	Submitted: 'Inviato',
	SubmittedDate: 'Data di invio',
	SubscribePerMonth: `Iscriviti {price} {isMonthly, select, true {al mese} other {all'anno}}`,
	SubscriptionDiscountDescription:
		'{percentOff}% sconto {months, select, null { } other { {months, plural, one {per # mese} other {per # mesi}}}}',
	SubscriptionFreeTrialDescription: 'Gratuito fino al {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Non siamo riusciti a completare il pagamento per il tuo abbonamento. Controlla i dettagli del pagamento',
	SubscriptionPlanDetailsHeader: 'Per utente/mensile fatturato annualmente',
	SubscriptionPlanDetailsSubheader: '{prezzoMensile} fatturato mensile (USD)',
	SubscriptionPlans: 'Piani di abbonamento',
	SubscriptionPlansDescription:
		'Aggiorna il tuo piano per sbloccare vantaggi aggiuntivi e mantenere la tua pratica in corso senza problemi.',
	SubscriptionPlansDescriptionNoPermission: `Sembra che tu non abbia accesso all'aggiornamento in questo momento — contatta il tuo amministratore per assistenza.`,
	SubscriptionSettings: 'Impostazioni di abbonamento',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> di spazio di archiviazione utilizzato',
	SubscriptionSettingsStorageUsed: '{usati} di {limite} usati',
	SubscriptionSettingsUnlimitedStorage: 'Spazio di archiviazione illimitato disponibile',
	SubscriptionSummary: 'Riepilogo abbonamento',
	SubscriptionUnavailableOverStorageLimit:
		'Il tuo utilizzo attuale supera il limite di archiviazione di questo piano.',
	SubscriptionUnpaidBannerButton: 'Vai agli abbonamenti',
	SubscriptionUnpaidBannerDescription: 'Controlla che i tuoi dati di pagamento siano corretti e riprova',
	SubscriptionUnpaidBannerTitle: 'Non siamo riusciti a completare il pagamento del tuo abbonamento.',
	Subscriptions: 'Abbonamenti',
	SubscriptionsAndPayments: 'Abbonamenti ',
	Subtotal: 'Subtotale',
	SuburbOrProvince: 'Sobborgo/Provincia',
	SuburbOrState: 'Sobborgo/Stato',
	SuccessSavedNoteChanges: 'Modifiche alle note salvate correttamente',
	SuccessShareDocument: 'Documento condiviso con successo',
	SuccessShareNote: 'Nota condivisa con successo',
	SuccessfullyCreatedValue: 'Creato correttamente {value}',
	SuccessfullyDeletedTranscriptionPart: 'Eliminata con successo la parte della trascrizione',
	SuccessfullyDeletedValue: 'Eliminato correttamente {value}',
	SuccessfullySubmitted: 'Inviato con successo ',
	SuccessfullyUpdatedClientSettings: 'Impostazioni client aggiornate correttamente',
	SuccessfullyUpdatedTranscriptionPart: 'Parte della trascrizione aggiornata con successo',
	SuccessfullyUpdatedValue: 'Aggiornamento di {value} completato con successo',
	SuggestedAIPoweredTemplates: `Modelli suggeriti basati sull'intelligenza artificiale`,
	SuggestedAITemplates: 'Modelli AI suggeriti',
	SuggestedActions: 'Azioni suggerite',
	SuggestedLocations: 'Luoghi suggeriti',
	Suggestions: 'Suggerimenti',
	Summarise: 'Riassunto AI',
	SummarisingContent: 'Riassumendo {title}',
	Sunday: 'Domenica',
	Superbill: 'Superfattura',
	SuperbillAndInsuranceBilling: 'Superfattura ',
	SuperbillAutomationMonthly: 'Attivo • Ultimo giorno del mese',
	SuperbillAutomationNoEmail:
		'Per inviare correttamente i documenti di fatturazione automatica, aggiungi un indirizzo email per questo cliente',
	SuperbillAutomationNotActive: 'Non attivo',
	SuperbillAutomationUpdateFailure: 'Impossibile aggiornare le impostazioni di automazione Superbill',
	SuperbillAutomationUpdateSuccess: 'Impostazioni di automazione Superbill aggiornate con successo',
	SuperbillClientHelperText: 'Queste informazioni sono precompilate dai dettagli del cliente',
	SuperbillNotFoundDescription:
		'Contatta il tuo fornitore e chiedi maggiori informazioni o di inviarti nuovamente la superbolletta.',
	SuperbillNotFoundTitle: 'Superbill non trovato',
	SuperbillNumber: 'Superbill #{number}',
	SuperbillNumberAlreadyExists: 'Il numero di ricevuta Superbill esiste già',
	SuperbillPracticeHelperText:
		'Queste informazioni sono precompilate dalle impostazioni di fatturazione dello studio',
	SuperbillProviderHelperText: 'Queste informazioni sono precompilate dai dettagli del personale',
	SuperbillReceipts: 'Ricevute Superbill',
	SuperbillsEmptyStateDescription: 'Non sono state trovate superfatture.',
	Surgeon: 'Chirurgo',
	Surgeons: 'Chirurghi',
	SurgicalTechnologist: 'Tecnologo chirurgico',
	SwitchFromAnotherPlatform: `Sto passando da un'altra piattaforma`,
	SwitchToMyPortal: 'Passa al mio portale',
	SwitchToMyPortalTooltip: `Accedi al tuo portale personale,
 consentendoti di esplorare il tuo
 esperienza del portale del cliente.`,
	SwitchWorkspace: 'Cambia spazio di lavoro',
	SwitchingToADifferentPlatform: 'Passaggio a una piattaforma diversa',
	Sydney: 'Sidney',
	SyncCalendar: 'Sincronizza calendario',
	SyncCalendarModalDescription:
		'Gli altri membri del team non saranno in grado di vedere i tuoi calendari sincronizzati. Gli appuntamenti dei clienti possono essere aggiornati o eliminati solo da Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Visualizza il mio calendario in Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Sincronizza il mio calendario con Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Sincronizza gli appuntamenti Carepatron con il mio calendario',
	SyncCarepatronAppointmentsWithMyCalendar: 'Sincronizza gli appuntamenti di Carepatron con il mio calendario',
	SyncGoogleCalendar: 'Sincronizza il calendario di Google',
	SyncInbox: 'Sincronizza la posta in arrivo con Carepatron',
	SyncMyCalendarToCarepatron: 'Sincronizza il mio calendario con Carepatron',
	SyncOutlookCalendar: 'Sincronizza il calendario di Outlook',
	SyncedFromExternalCalendar: 'Sincronizzato dal calendario esterno',
	SyncingCalendarName: 'Sincronizzando il calendario {calendarName}',
	SyncingFailed: 'Sincronizzazione non riuscita',
	SystemGenerated: 'Generato dal sistema',
	TFN: 'Codice Fiscale',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tavolo',
	TableRowLabel: 'Riga di tabella per {value}',
	TagSelectorNoOptionsText: 'Fai clic su "crea nuovo" per aggiungere un nuovo tag',
	Tags: 'Etichette',
	TagsInputPlaceholder: 'Cerca o crea tag',
	Task: 'Compito',
	TaskAttendeeStatusUpdatedSuccess: 'Stati degli appuntamenti aggiornati correttamente',
	Tasks: 'Compiti',
	Tax: 'Tassare',
	TaxAmount: 'Importo Imposta',
	TaxID: 'Codice Fiscale',
	TaxIdType: 'Tipo di codice fiscale',
	TaxName: 'Nome fiscale',
	TaxNumber: 'Numero di codice fiscale',
	TaxNumberType: 'Tipo di numero fiscale',
	TaxNumberTypeInvalid: '{type} non è valido',
	TaxPercentageOfAmount: '{taxName} ({percentage}% di {amount})',
	TaxRate: 'Aliquota fiscale',
	TaxRatesDescription: 'Gestisci le aliquote fiscali che verranno applicate alle voci della tua fattura.',
	Taxable: 'Imponibile',
	TaxonomyCode: 'Codice tassonomia',
	TeacherAssistant: 'Assistente insegnante',
	Team: 'Squadra',
	TeamMember: 'Membro del team',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {è} other {sono}} già prenotati in questo momento.{br}Scegli un nuovo orario per evitare una doppia prenotazione.',
	TeamMembers: 'Membri del team',
	TeamMembersColour: 'Colore dei membri del team',
	TeamMembersDetails: 'Dettagli dei membri del team',
	TeamSize: 'Quante persone ci sono nel tuo team?',
	TeamTemplates: 'Modelli di squadra',
	TeamTemplatesSectionDescription: 'Creato da te e dal tuo team',
	TelehealthAndVideoCalls: 'Telemedicina ',
	TelehealthProvidedOtherThanInPatientCare: 'Telemedicina fornita per cure diverse da quelle ospedaliere',
	TelehealthVideoCall: 'Videochiamata di telemedicina',
	Template: 'Modello',
	TemplateDescription: 'Descrizione del modello',
	TemplateDetails: 'Dettagli del modello',
	TemplateEditModeViewSwitcherDescription: 'Crea e modifica modello',
	TemplateGallery: 'Modelli della comunità',
	TemplateImportCompletedNotificationSubject: `Importazione del modello completata! {templateTitle} è pronto per l'uso.`,
	TemplateImportFailedNotificationSubject: 'Impossibile importare il file {fileName}.',
	TemplateName: 'Nome del modello',
	TemplateNotFound: 'Impossibile trovare il modello.',
	TemplatePreviewErrorMessage: `Si è verificato un errore durante il caricamento dell'anteprima del modello`,
	TemplateResponderModeViewSwitcherDescription: 'Visualizza in anteprima e interagisci con i moduli',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Controlla come appaiono i tuoi moduli quando vengono compilati dai rispondenti',
	TemplateSaved: 'Modifiche salvate',
	TemplateTitle: 'Titolo del modello',
	TemplateType: 'Tipo di modello',
	Templates: 'Modelli',
	TemplatesCategoriesFilter: 'Filtra per categoria',
	TemplatesPublicTemplatesFilter: ' Filtra per comunità/team',
	Text: 'Testo',
	TextAlign: 'Allineamento del testo',
	TextColor: 'Colore del testo',
	ThankYouForYourFeedback: 'Grazie per il tuo feedback!',
	ThanksForLettingKnow: 'Grazie per avercelo fatto sapere.',
	ThePaymentMethod: 'Il metodo di pagamento',
	ThemThey: 'loro/loro',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Più temi',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Scuro',
	ThemeColorLightMode: 'Luce',
	ThemeColorModePickerTitle: 'Modalità colore',
	ThemeColorSystemMode: 'Sistema',
	ThemeCpColorPickerTitle: 'Carepatron themes',
	ThemePanelDescription: 'Scegli tra la modalità chiara e scura e personalizza le tue preferenze sul tema',
	ThemePanelTitle: 'Aspetto',
	Then: 'Poi',
	Therapist: 'Terapista',
	Therapists: 'Terapeuti',
	Therapy: 'Terapia',
	Thick: 'Spesso',
	Thin: 'Magro',
	ThirdPerson: '3a persona',
	ThisAndFollowingAppointments: 'Questo e i seguenti appuntamenti',
	ThisAndFollowingMeetings: 'Questo e i seguenti incontri',
	ThisAndFollowingReminders: 'Questo e i seguenti promemoria',
	ThisAndFollowingTasks: 'Questo e i seguenti compiti',
	ThisAppointment: 'Questo appuntamento',
	ThisMeeting: 'Questo incontro',
	ThisMonth: 'Questo mese',
	ThisPerson: 'Questa persona',
	ThisReminder: 'Questo promemoria',
	ThisTask: 'Questo compito',
	ThisWeek: 'Questa settimana',
	ThreeDay: '3 giorni',
	Thursday: 'Giovedì',
	Time: 'Tempo',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{numero}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{numero}s',
	TimeFormat: `Formato dell'ora`,
	TimeIncrement: 'Incremento di tempo',
	TimeRangeFormula: '{ore}:{minuti}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Dimensione della fascia oraria',
	Timestamp: 'Marca temporale',
	Timezone: 'Fuso orario',
	TimezoneDisplay: 'Visualizzazione del fuso orario',
	TimezoneDisplayDescription: 'Gestisci le impostazioni di visualizzazione del fuso orario.',
	Title: 'Titolo',
	To: 'A',
	ToYourWorkspace: 'al tuo spazio di lavoro',
	Today: 'Oggi',
	TodayInHoursPlural: 'Oggi in {count} {count, plural, one {ora} other {ore}}',
	TodayInMinsAbbreviated: 'Oggi in {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Attiva/disattiva cella intestazione',
	ToggleHeaderCol: `Attiva/disattiva la colonna dell'intestazione`,
	ToggleHeaderRow: `Attiva/disattiva la riga dell'intestazione`,
	Tokyo: 'Tokio',
	Tomorrow: 'Domani',
	TomorrowAfternoon: 'Domani pomeriggio',
	TomorrowMorning: 'Domani mattina',
	TooExpensive: 'Troppo costoso',
	TooHardToSetUp: 'Troppo difficile da configurare',
	TooManyFiles: 'Rilevato più di 1 file.',
	ToolsExample: 'Pratica semplice, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Totale',
	TotalAccountCredit: 'Credito totale del conto',
	TotalAdjustments: 'Ajuste totali',
	TotalAmountToCreditInCurrency: 'Importo totale da accreditare ({currency})',
	TotalBilled: 'Totale fatturato',
	TotalConversations: '{total} {total, plural, =0 {conversazione} one {conversazione} other {conversazioni}}',
	TotalOverdue: 'Totale in ritardo',
	TotalOverdueTooltip: `Il saldo totale in ritardo include tutte le fatture non pagate, indipendentemente dall'intervallo di date, che non sono né annullate né elaborate.`,
	TotalPaid: 'Totale pagato',
	TotalPaidTooltip: `Il saldo totale pagato include tutti gli importi delle fatture pagate nell'intervallo di date specificato.`,
	TotalUnpaid: 'Totale non pagato',
	TotalUnpaidTooltip: `Il saldo totale non pagato include tutti gli importi in sospeso derivanti da fatture in elaborazione, non pagate e inviate, in scadenza nell'intervallo di date specificato.`,
	TotalWorkflows: '{count} {count, plural, one {flusso di lavoro} other {flussi di lavoro}}',
	TotpSetUpManualEntryInstruction: `In alternativa, puoi inserire manualmente il codice sottostante nell'app:`,
	TotpSetUpModalDescription: `Scansiona il codice QR con la tua app di autenticazione per impostare l'autenticazione a più fattori.`,
	TotpSetUpModalTitle: 'Imposta dispositivo MFA',
	TotpSetUpSuccess: 'Fatto! MFA è stato abilitato.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Inserisci il codice generato dalla tua app di autenticazione',
	Transcribe: 'Trascrivere',
	TranscribeLanguageSelector: 'Seleziona la lingua di input',
	TranscribeLiveAudio: `Trascrivi l'audio in diretta`,
	Transcribing: 'Trascrizione audio...',
	TranscribingIn: 'Trascrivere in',
	Transcript: 'Trascrizione',
	TranscriptRecordingCompleteInfo: 'Una volta completata la registrazione, potrai visualizzare la trascrizione qui.',
	TranscriptSuccessSnackbar: 'Trascrizione elaborata con successo.',
	Transcription: 'Trascrizione',
	TranscriptionEmpty: 'Nessuna trascrizione disponibile',
	TranscriptionEmptyHelperMessage: 'Questa trascrizione non ha rilevato nulla. Riavviala e riprova.',
	TranscriptionFailedNotice: 'Questa trascrizione non è stata elaborata correttamente',
	TranscriptionIdleMessage:
		'Non sentiamo alcun audio. Se hai bisogno di più tempo, ti preghiamo di rispondere entro {timeValue} secondi, altrimenti la sessione terminerà.',
	TranscriptionInProcess: 'Trascrizione in corso...',
	TranscriptionIncompleteNotice: 'Alcune parti di questa trascrizione non sono state elaborate correttamente',
	TranscriptionOvertimeWarning: '{scribeType} sessione termina in <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Sei sicuro di voler eliminare questa parte della trascrizione?',
	TranscriptionText: 'Da voce a testo',
	TranscriptsPending: 'La trascrizione sarà disponibile qui al termine della sessione.',
	Transfer: 'Trasferire',
	TransferAndDelete: 'Trasferisci ed elimina',
	TransferOwnership: 'Trasferire la proprietà',
	TransferOwnershipConfirmationModalDescription:
		'Questa azione può essere annullata solo se la proprietà viene trasferita nuovamente a te.',
	TransferOwnershipDescription: 'Trasferisci la proprietà di questo spazio di lavoro a un altro membro del team.',
	TransferOwnershipSuccessSnackbar: 'Trasferimento della proprietà effettuato con successo!',
	TransferOwnershipToMember: 'Sei sicuro di voler trasferire questo spazio di lavoro a {staff}?',
	TransferStatusAlert:
		'Rimuovere {numberOfStatuses, plural, one {questo stato} other {questi stati}} avrà un impatto su {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} stato client.</strong>} other {<strong>{numberOfAffectedRecords} stati client.</strong>}}',
	TransferStatusDescription: `Scegli un altro stato per questi client prima di procedere con l'eliminazione. Questa azione non può essere annullata.`,
	TransferStatusLabel: 'Trasferimento al nuovo stato',
	TransferStatusPlaceholder: 'Scegli uno stato esistente',
	TransferStatusTitle: `Stato del trasferimento prima dell'eliminazione`,
	TransferTaskAttendeeStatusAlert:
		'Rimuovere questo stato influenzerà <strong>{number} appuntamento futuro {number, plural, one {stato} other {stati}}.</strong>',
	TransferTaskAttendeeStatusDescription: `Scegli un altro stato per questi clienti prima di procedere con l'eliminazione. Questa azione non può essere annullata.`,
	TransferTaskAttendeeStatusSubtitle: 'Stato appuntamento',
	TransferTaskAttendeeStatusTitle: `Stato del trasferimento prima dell'eliminazione`,
	Trash: 'Spazzatura',
	TrashDeleteItemsModalConfirm: 'Per confermare, digita {confirmationText}',
	TrashDeleteItemsModalDescription:
		'I seguenti {count, plural, one {elemento} other {elementi}} saranno eliminati in modo permanente e non potranno essere ripristinati.',
	TrashDeleteItemsModalTitle: 'Elimina {count, plural, one {elemento} other {elementos}} per sempre',
	TrashDeletedAllItems: 'Eliminati tutti gli elementi',
	TrashDeletedItems: 'Cancellati {count, plural, one {elemento} other {elementi}}',
	TrashDeletedItemsFailure: 'Impossibile eliminare gli elementi dal cestino',
	TrashLocationAppointmentType: 'Calendario',
	TrashLocationBillingAndPaymentsType: 'Fatturazione & pagamenti',
	TrashLocationContactType: 'Clienti',
	TrashLocationNoteType: 'Appunti ',
	TrashRestoreItemsModalDescription:
		'I seguenti {count, plural, one {elemento} other {elementi}} saranno ripristinati.',
	TrashRestoreItemsModalTitle: 'Ripristina {count, plural, one {elemento} other {elementi}}',
	TrashRestoredAllItems: 'Ripristinati tutti gli elementi',
	TrashRestoredItems: 'Ripristinato {count, plural, one {elemento} other {elementi}}',
	TrashRestoredItemsFailure: 'Impossibile ripristinare gli elementi dal cestino',
	TrashSuccessfullyDeletedItem: 'Riuscito a cancellare {type}',
	Trigger: 'Grilletto',
	Troubleshoot: 'Risoluzione dei problemi',
	TryAgain: 'Riprova',
	Tuesday: 'Martedì',
	TwoToTen: '2 - 10',
	Type: 'Tipo',
	TypeHere: 'Digita qui...',
	TypeToConfirm: 'Per confermare, digita {keyword}',
	TypographyH1: 'H1',
	TypographyH2: `L'H2`,
	TypographyH3: `L'altezza 3`,
	TypographyH4: 'L4a',
	TypographyH5: `L'altezza 5`,
	TypographyHeading1: 'Titolo 1',
	TypographyHeading2: 'Titolo 2',
	TypographyHeading3: 'Titolo 3',
	TypographyHeading4: 'Titolo 4',
	TypographyHeading5: 'Titolo 5',
	TypographyP: 'P',
	TypographyParagraph: 'Paragrafo',
	UnableToCompleteAction: `Impossibile completare l'azione.`,
	UnableToPrintDocument: 'Impossibile stampare il documento. Riprova più tardi.',
	Unallocated: 'Non assegnato',
	UnallocatedPaymentDescription: `Questo pagamento non è stato completamente assegnato alle voci fatturabili.
 Aggiungere un'assegnazione agli articoli non pagati oppure emettere un accredito o un rimborso.`,
	UnallocatedPaymentTitle: 'Pagamento non assegnato',
	UnallocatedPayments: 'Pagamenti non assegnati',
	Unarchive: 'Disarchiviare',
	Unassigned: 'Non assegnato',
	UnauthorisedInvoiceSnackbar: 'Non hai accesso alla gestione delle fatture per questo cliente.',
	UnauthorisedSnackbar: `Non hai l'autorizzazione per farlo.`,
	Unavailable: 'Non disponibile',
	Uncategorized: 'Non categorizzato',
	Unclaimed: 'Non reclamato',
	UnclaimedAmount: 'Importo non riscosso',
	UnclaimedItems: 'Oggetti non reclamati',
	UnclaimedItemsMustBeInCurrency: 'Solo gli articoli nelle seguenti valute sono supportati: {currencies}',
	Uncle: 'Zio',
	Unconfirmed: 'Non confermato',
	Underline: 'Sottolineare',
	Undo: 'Disfare',
	Unfavorite: 'Rimuovi dai preferiti',
	Uninvoiced: 'Non fatturato',
	UninvoicedAmount: 'Importo non fatturato',
	UninvoicedAmounts:
		'{count, plural, =0 {Nessun importo non fatturato} one {Importo non fatturato} other {Importi non fatturati}}',
	Unit: 'Unità',
	UnitedKingdom: 'Regno Unito',
	UnitedStates: 'Stati Uniti',
	UnitedStatesEast: 'Stati Uniti - Est',
	UnitedStatesWest: 'Stati Uniti - Ovest',
	Units: 'Unità',
	UnitsIsRequired: 'Le unità sono obbligatorie',
	UnitsMustBeGreaterThanZero: 'Le unità devono essere maggiori di 0',
	UnitsPlaceholder: '1',
	Unknown: 'Sconosciuto',
	Unlimited: 'Illimitato',
	Unlock: 'Sbloccare',
	UnlockNoteHelper: 'Prima di apportare nuove modifiche, gli editori devono sbloccare la nota.',
	UnmuteAudio: 'Disattiva audio',
	UnmuteEveryone: `Disattiva l'audio per tutti`,
	Unpaid: 'Non pagato',
	UnpaidInvoices: 'Fatture non pagate',
	UnpaidItems: 'Articoli non pagati',
	UnpaidMultiple: 'Non pagato',
	Unpublish: 'Dispubblica',
	UnpublishTemplateConfirmationModalPrompt:
		'<span>{title}</span>’i kaldırmak, bu kaynağı Carepatron topluluğundan kaldıracaktır. Bu işlem geri alınamaz.',
	UnpublishToCommunitySuccessMessage: 'Rimosso con successo ‛{title}’ dalla community',
	Unread: 'Non letto',
	Unrecognised: 'Non riconosciuto',
	UnrecognisedDescription: `Questo metodo di pagamento non è riconosciuto dalla tua attuale versione dell'applicazione. Aggiorna il tuo browser per ottenere la versione più recente per visualizzare e modificare questo metodo di pagamento.`,
	UnsavedChanges: 'Modifiche non salvate',
	UnsavedChangesPromptContent: 'Vuoi salvare le modifiche prima di chiudere?',
	UnsavedChangesPromptTitle: 'Hai modifiche non salvate',
	UnsavedNoteChangesWarning: 'Le modifiche apportate potrebbero non essere salvate',
	UnsavedTemplateChangesWarning: 'Le modifiche apportate potrebbero non essere salvate',
	UnselectAll: 'Deseleziona tutto',
	Until: 'Fino a',
	UntitledConversation: 'Conversazione senza titolo',
	UntitledFolder: 'Cartella senza titolo',
	UntitledNote: 'Nota senza titolo',
	UntitledSchedule: 'Programma senza titolo',
	UntitledSection: 'Sezione senza titolo',
	UntitledTemplate: 'Modello senza titolo',
	Unverified: 'Non verificato',
	Upcoming: 'Prossimamente',
	UpcomingAppointments: 'Prossimi appuntamenti',
	UpcomingDateOverridesEmpty: 'Non sono state trovate sostituzioni di data',
	UpdateAvailabilityScheduleFailure: 'Impossibile aggiornare la pianificazione della disponibilità',
	UpdateAvailabilityScheduleSuccess: 'Aggiornamento del programma di disponibilità riuscito',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Desideri che i nuovi prezzi vengano applicati alle fatture e ai reclami dei partecipanti?',
	UpdateLink: 'Aggiorna link',
	UpdatePrimaryEmailWarningDescription: `Cambiare l'indirizzo email del tuo cliente comporterà la perdita del loro accesso agli appuntamenti e alle note esistenti.`,
	UpdatePrimaryEmailWarningTitle: 'Cambio di email del cliente',
	UpdateSettings: 'Aggiorna impostazioni',
	UpdateStatus: 'Aggiorna stato',
	UpdateSuperbillReceiptFailure: 'Impossibile aggiornare la ricevuta Superbill',
	UpdateSuperbillReceiptSuccess: 'Ricevuta Superbill aggiornata con successo',
	UpdateTaskBillingDetails: 'Aggiorna i dettagli di fatturazione',
	UpdateTaskBillingDetailsDescription: `Il prezzo dell'appuntamento è cambiato. Vuoi che il nuovo prezzo venga applicato alle voci di fatturazione, alle fatture e ai reclami dei partecipanti? Seleziona gli aggiornamenti con cui vuoi procedere.`,
	UpdateTemplateFolderSuccessMessage: 'Cartella aggiornata correttamente',
	UpdateUnpaidInvoices: 'Aggiorna le fatture non pagate',
	UpdateUserInfoSuccessSnackbar: 'Informazioni utente aggiornate con successo!',
	UpdateUserSettingsSuccessSnackbar: 'Impostazioni utente aggiornate con successo!',
	Upgrade: 'Aggiornamento',
	UpgradeForSMSReminder: 'Passa a <b>Professional</b> per promemoria SMS illimitati',
	UpgradeNow: 'Aggiorna ora',
	UpgradePlan: 'Piano di aggiornamento',
	UpgradeSubscriptionAlertDescription:
		'Ti stai esaurendo lo spazio di archiviazione. Aggiorna il tuo piano per sbloccare spazio di archiviazione aggiuntivo e mantenere la tua pratica in funzione senza problemi!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Ti stai esaurendo lo spazio di archiviazione. Chiedi a qualcuno nel tuo studio con <span>accesso da amministratore</span> di aggiornare il tuo piano per sbloccare spazio di archiviazione aggiuntivo e mantenere il tuo studio in funzione senza problemi!',
	UpgradeSubscriptionAlertTitle: 'È il momento di aggiornare il tuo abbonamento',
	UpgradeYourPlan: 'Aggiorna il tuo piano',
	UploadAudio: 'Carica audio',
	UploadFile: 'Carica file',
	UploadFileDescription: 'Da quale piattaforma software stai passando?',
	UploadFileMaxSizeError: 'File è troppo grande. Dimensione massima del file è {fileSizeLimit}.',
	UploadFileSizeLimit: 'Limite di dimensione {size}MB',
	UploadFileTileDescription: 'Utilizza file CSV, XLS, XLSX o ZIP per caricare i tuoi clienti.',
	UploadFileTileLabel: 'Carica un file',
	UploadFiles: 'Carica file',
	UploadIndividually: 'Carica i file singolarmente',
	UploadLogo: 'Carica il logo',
	UploadPhoto: 'Carica foto',
	UploadToCarepatron: 'Carica su Carepatron',
	UploadYourLogo: 'Carica il tuo logo',
	UploadYourTemplates: 'Carica i tuoi modelli e noi li convertiremo per te.',
	Uploading: 'Caricamento in corso',
	UploadingAudio: `Caricamento dell'audio in corso...`,
	UploadingFiles: 'Caricamento dei file',
	UrlLink: 'Collegamento URL',
	UsageCount: 'Usato {count} volte',
	UsageLimitValue: '{utilizzati} di {limite} utilizzati',
	UsageValue: '{usato} usato',
	Use: 'Utilizzo',
	UseAiToAutomateYourWorkflow: `Utilizza l'intelligenza artificiale per automatizzare il tuo flusso di lavoro!`,
	UseAsDefault: 'Usa come predefinito',
	UseCustom: 'Usa personalizzato',
	UseDefault: 'Usa predefinito',
	UseDefaultFilters: 'Utilizza filtri predefiniti',
	UseTemplate: 'Usa il modello',
	UseThisCard: 'Usa questa carta',
	UseValue: 'Usa "{value}"',
	UseWorkspaceDefault: `Usa l'area di lavoro predefinita`,
	UserIsTyping: '{name} sta scrivendo...',
	Username: 'Nome utente',
	Users: 'Utenti',
	VAT: 'I.V.A.',
	ValidUrl: 'Il collegamento URL deve essere un URL valido.',
	Validate: 'Convalidare',
	Validated: 'Validato',
	Validating: 'Convalidare',
	ValidatingContent: 'Convalida del contenuto...',
	ValidatingTranscripts: 'Convalida delle trascrizioni...',
	ValidationConfirmPasswordRequired: 'Conferma password obbligatoria',
	ValidationDateMax: 'Deve essere prima di {max}',
	ValidationDateMin: 'Dovrebbe essere dopo {min}',
	ValidationDateRange: 'Sono obbligatorie le date di inizio e fine',
	ValidationEndDateMustBeAfterStartDate: 'La data di fine deve essere successiva alla data di inizio',
	ValidationMixedDefault: 'Questo non è valido',
	ValidationMixedRequired: 'Questo è obbligatorio',
	ValidationNumberInteger: 'Deve essere un numero intero',
	ValidationNumberMax: 'Deve essere {max} o meno',
	ValidationNumberMin: 'Deve essere {min} o più',
	ValidationPasswordNotMatching: 'Le password non corrispondono',
	ValidationPrimaryAddressIsRequired: `L'indirizzo è obbligatorio se impostato come predefinito`,
	ValidationPrimaryPhoneNumberIsRequired: 'Il numero di telefono è obbligatorio se impostato come predefinito',
	ValidationServiceMustBeNotBeFuture: 'Il servizio non deve essere di oggi o del futuro',
	ValidationStringEmail: 'Deve essere un indirizzo email valido',
	ValidationStringMax: 'Deve avere {max} caratteri o meno',
	ValidationStringMin: 'Deve avere {min} o più caratteri',
	ValidationStringPhoneNumber: 'Deve essere un numero di telefono valido',
	ValueMinutes: '{value} minuti',
	VerbosityConcise: 'Conciso',
	VerbosityDetailed: 'Dettagliato',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Super dettagliato',
	VerificationCode: 'Codice di verifica',
	VerificationEmailDescription:
		'Inserisci il tuo indirizzo email e il codice di verifica che ti abbiamo appena inviato.',
	VerificationEmailSubtitle: `Controlla la cartella Spam - se l'email non è arrivata`,
	VerificationEmailTitle: 'Verifica email',
	VerificationOption: `Verifica dell'email`,
	Verified: 'Verificato',
	Verify: 'Verificare',
	VerifyAndSubmit: 'Verifica e invia',
	VerifyEmail: 'Verifica email',
	VerifyEmailAccessCode: 'Codice di conferma',
	VerifyEmailAddress: `Verifica l'indirizzo email`,
	VerifyEmailButton: 'Verifica e disconnetti',
	VerifyEmailSentSnackbar: 'Email di verifica inviata. Controlla la tua posta in arrivo.',
	VerifyEmailSubTitle: `Controlla la cartella Spam se l'email non è arrivata`,
	VerifyEmailSuccessLogOutSnackbar: 'Riuscito! Esci per applicare le modifiche.',
	VerifyEmailSuccessSnackbar: 'Successo! Email verificata. Accedi per continuare come account verificato.',
	VerifyEmailTitle: 'Verifica la tua email',
	VerifyNow: 'Verifica ora',
	Veterinarian: 'Veterinario',
	VideoCall: 'Videochiamata',
	VideoCallAudioInputFailed: 'Dispositivo di input audio non funzionante',
	VideoCallAudioInputFailedMessage:
		'Apri le impostazioni e controlla se hai impostato correttamente la sorgente del microfono',
	VideoCallChatBanner:
		'I messaggi potranno essere visualizzati da tutti i partecipanti alla chiamata e verranno eliminati al termine della stessa.',
	VideoCallChatSendBtn: 'Invia un messaggio',
	VideoCallChatTitle: 'Chiacchierata',
	VideoCallDisconnectedMessage: 'Hai perso la connessione di rete. Tentativo di riconnettersi',
	VideoCallOptionInfo: 'Carepatron gestirà le videochiamate per i tuoi appuntamenti se Zoom non è stato connesso',
	VideoCallTilePaused: 'Questo video è in pausa a causa di problemi con la tua rete',
	VideoCallTranscriptionFormDescription: 'Puoi modificare queste impostazioni in qualsiasi momento',
	VideoCallTranscriptionFormHeading: 'Personalizza il tuo AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'La lingua di output generata',
	VideoCallTranscriptionFormNoteTemplateField: 'Imposta modello di nota predefinito',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Nessun modello con AI trovato',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Scegli un modello',
	VideoCallTranscriptionPronounField: 'I tuoi pronomi',
	VideoCallTranscriptionRecordingNote:
		'Alla fine della sessione, riceverai una <strong>{noteTemplate} nota</strong> e una trascrizione generata.',
	VideoCallTranscriptionReferClientField: 'Riferirsi al Cliente come',
	VideoCallTranscriptionReferPractitionerField: 'Fare riferimento al professionista come',
	VideoCallTranscriptionTitle: 'Scriba AI',
	VideoCallTranscriptionVerbosityField: 'Verbosità',
	VideoCallTranscriptionWritingPerspectiveField: 'Prospettiva di scrittura',
	VideoCalls: 'Videochiamate',
	VideoConferencing: 'Videoconferenza',
	VideoOff: 'Il video è spento',
	VideoOn: 'Il video è spento',
	VideoQual360: 'Bassa qualità (360p)',
	VideoQual540: 'Qualità media (540p)',
	VideoQual720: 'Alta qualità (720p)',
	View: 'Visualizzazione',
	ViewAll: 'Visualizza tutto',
	ViewAppointment: 'Visualizza appuntamento',
	ViewBy: 'Visualizza per',
	ViewClaim: 'Visualizza richiesta di risarcimento',
	ViewCollection: 'Visualizza la collezione',
	ViewDetails: 'Visualizza i dettagli',
	ViewEnrollment: 'Visualizza iscrizione',
	ViewPayment: 'Visualizza pagamento',
	ViewRecord: 'Visualizza record',
	ViewRemittanceAdvice: `Visualizza l'avviso di pagamento`,
	ViewRemittanceAdviceHeader: '<p>Conferma di pagamento</p>',
	ViewRemittanceAdviceSubheader: 'Richiesta {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Visualizza impostazioni',
	ViewStripeDashboard: 'Visualizza la dashboard di Stripe',
	ViewTemplate: 'Visualizza modello',
	ViewTemplates: 'Visualizza i modelli',
	ViewableBy: 'Visibile da',
	ViewableByHelper:
		'Tu e il Team avete sempre accesso alle note che pubblichi. Puoi scegliere di condividere questa nota con il cliente e/o con le sue relazioni',
	Viewer: 'Spettatore',
	VirtualLocation: 'Posizione virtuale',
	VisibleTo: 'Visibile a',
	VisitOurHelpCentre: 'Visita il nostro centro assistenza',
	VisualEffects: 'Effetti visivi',
	VoiceFocus: 'Messa a fuoco vocale',
	VoiceFocusLabel: 'Filtra i suoni dal microfono che non sono vocali',
	Void: 'Vuoto',
	VoidCancelPriorClaim: `Void/Cancel prior claim		
Annulla/Cancella richiesta precedente`,
	WaitingforMins: 'In attesa di {count} minuti',
	Warning: 'Avvertimento',
	WatchAVideo: 'guarda un video',
	WatchDemoVideo: 'Guarda il video dimostrativo',
	WebConference: 'Conferenza Web',
	WebConferenceOrVirtualLocation: 'Web conference / sede virtuale',
	WebDeveloper: 'Sviluppatore Web',
	WebsiteOptional: 'Sito web <span>(facoltativo)</span>',
	WebsiteUrl: 'URL del sito web',
	Wednesday: 'Mercoledì',
	Week: 'Settimana',
	WeekPlural: '{count, plural, one {settimana} other {settimane}}',
	Weekly: 'Settimanale',
	WeeksPlural: '{età, plural, one {# settimana} other {# settimane}}',
	WelcomeBack: 'Bentornato',
	WelcomeBackName: 'Bentornato, {name}',
	WelcomeName: 'Benvenuto {name}',
	WelcomeToCarepatron: 'Benvenuti a Carepatron',
	WhatCanIHelpWith: 'Come posso aiutarti?',
	WhatDidYouLikeResponse: 'Cosa ti è piaciuto di questa risposta?',
	WhatIsCarepatron: `Che cos'è Carepatron?`,
	WhatMadeYouCancel: `Cosa ti ha spinto ad annullare il tuo piano?
 Seleziona tutte le risposte che ritieni appropriate.`,
	WhatServicesDoYouOffer: 'Che cosa<mark> servizi</mark> offrite?',
	WhatServicesDoYouOfferDescription: 'Potrai modificare o aggiungere altri servizi in seguito.',
	WhatsYourAvailability: 'Qual è la tua <mark>disponibilità?</mark>',
	WhatsYourAvailabilityDescription: 'Puoi aggiungere altri programmi in seguito.',
	WhatsYourBusinessName: `Qual è il tuo<mark> nome dell'azienda?</mark>`,
	WhatsYourTeamSize: 'Qual è il tuo<mark> dimensione del team?</mark>',
	WhatsYourTeamSizeDescription: 'Questo ci aiuterà a configurare correttamente il tuo spazio di lavoro.',
	WhenThisHappens: 'Quando ciò accade:',
	WhichBestDescribesYou: 'Quale migliore<mark> ti descrive?</mark>',
	WhichPlatforms: 'Quali piattaforme?',
	Wife: 'Moglie',
	WorkflowDescription: 'Descrizione del flusso di lavoro',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'I modelli possono collegarsi a flussi di lavoro per processi più fluidi. Visualizza i flussi di lavoro collegati per tracciarli e aggiornarli facilmente.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'Connetti i tuoi SMS + email in base a trigger comuni',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Automazioni del flusso di lavoro',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: `<br><br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>`,
	WorkflowTemplateConfigKey_Body: 'Corpo',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Mostra il marchio',
	WorkflowTemplateConfigKey_Content: 'Contenuto',
	WorkflowTemplateConfigKey_Footer: 'Piede di pagina',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Mostra piè di pagina',
	WorkflowTemplateConfigKey_Header: 'Intestazione',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Mostra intestazione',
	WorkflowTemplateConfigKey_SecurityFooter: 'Piè di pagina sulla sicurezza',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Mostra piè di pagina di sicurezza',
	WorkflowTemplateConfigKey_Subject: 'Oggetto',
	WorkflowTemplateConfigKey_Title: 'Titolo',
	WorkflowTemplateDeleteConfirmationMessage:
		'Sei sicuro di voler eliminare questo modello? Questa azione non può essere annullata.',
	WorkflowTemplateDeleteConfirmationTitle: 'Elimina modello notifica',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Sei sicuro? Questo rimuoverà solo la versione {locale} - le altre lingue non saranno interessate. Questa azione non può essere annullata.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Elimina il modello ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Modello di notifica eliminato correttamente',
	WorkflowTemplateEditorDetailsTab: 'Dettagli del modello',
	WorkflowTemplateEditorEmailContent: 'Contenuto email',
	WorkflowTemplateEditorEmailContentTab: 'Contenuto della email',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert:
		'Le anteprime utilizzano dati di esempio per mostrare ai tuoi clienti cosa vedranno.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Sei sicuro? Questo ripristinerà la versione al modello predefinito del sistema. Questa azione non può essere annullata.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Ripristina modello',
	WorkflowTemplateSendTestEmail: 'Invia email di prova',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Prova la configurazione della tua email inviando una email di prova a te stesso.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Email del destinatario',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Invia test',
	WorkflowTemplateSendTestEmailDialogTitle: `Invia un'email di prova`,
	WorkflowTemplateSendTestEmailSuccess: 'Successo! La tua <mark>{templateName}</mark> email di test è stata inviata.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Gestisci i tuoi modelli e aggiungi versioni in più lingue per comunicare in modo efficace con i clienti.',
	WorkflowTemplateTemplateEditor: 'Editor del modello',
	WorkflowTemplateTranslateLocaleError: 'Qualcosa è andato storto durante la traduzione del contenuto',
	WorkflowTemplateTranslateLocaleSuccess: 'Contenuto tradotto correttamente in **{locale}**',
	WorkflowsAndReminders: 'Flussi di lavoro ',
	WorkflowsManagement: 'Gestione dei flussi di lavoro',
	WorksheetAndHandout: 'Foglio di lavoro/Dispensa',
	WorksheetsAndHandoutsDescription: `Per l'impegno e l'istruzione dei clienti`,
	Workspace: 'Spazio di lavoro',
	WorkspaceBranding: `Branding dell'area di lavoro`,
	WorkspaceBrandingDescription: `Personalizza senza sforzo il tuo spazio di lavoro con uno stile coerente che rifletta il tuo
 professionalità e personalità. Personalizza le fatture per la prenotazione online per una bella
 esperienza del cliente.`,
	WorkspaceName: `Nome dell'area di lavoro`,
	Workspaces: 'Spazi di lavoro',
	WriteOff: 'Cancellare',
	WriteOffModalDescription: 'Hai <mark>{count} {count, plural, one {riga} other {righe}}</mark> da stornare',
	WriteOffModalTitle: 'Adeguamento della svalutazione',
	WriteOffReasonHelperText: 'Questa è una nota interna e non sarà visibile al tuo cliente.',
	WriteOffReasonPlaceholder: `L'aggiunta di un motivo di cancellazione può essere utile quando si esaminano le transazioni fatturabili`,
	WriteOffTotal: 'Storno totale ({currencyCode})',
	Writer: 'Scrittore',
	Yearly: 'Annuale',
	YearsPlural: '{age, plural, one {# anno} other {# anni}}',
	Yes: 'SÌ',
	YesArchive: 'Sì, archivia',
	YesDelete: 'Sì, elimina',
	YesDeleteOverride: `Sì, elimina l'override`,
	YesDeleteSection: 'Sì, elimina',
	YesDisconnect: 'Sì, disconnetti',
	YesEnd: 'Sì, fine',
	YesEndTranscription: 'Sì, termina la trascrizione',
	YesImFineWithThat: 'Sì, mi sta bene così',
	YesLeave: 'Sì, esci',
	YesMinimize: 'Sì, minimizza',
	YesOrNoAnswerTypeDescription: 'Configura il tipo di risposta',
	YesOrNoFormPrimaryText: 'Sì | No',
	YesOrNoFormSecondaryText: 'Scegli le opzioni sì o no',
	YesProceed: 'Sì, procedi',
	YesRemove: 'Sì, rimuovi',
	YesRestore: 'Sì, ripristina',
	YesStopIgnoring: 'Sì, smettila di ignorare',
	YesTransfer: 'Sì, trasferisci',
	Yesterday: 'Ieri',
	YogaInstructor: 'Istruttore di Yoga',
	You: 'Voi',
	YouArePresenting: 'Stai presentando',
	YouCanChooseMultiple: 'Puoi scegliere più',
	YouCanSelectMultiple: 'Puoi selezionarne più di uno',
	YouHaveOngoingTranscription: 'Hai una trascrizione in corso',
	YourAnswer: 'La tua risposta',
	YourDisplayName: 'Il tuo nome visualizzato',
	YourSpreadsheetColumns: 'Le colonne del tuo foglio di calcolo',
	YourTeam: 'La tua squadra',
	ZipCode: 'Cap',
	Zoom: 'Ingrandire',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Non puoi aggiungere una chiamata Zoom per questo membro del team. <a>Per maggiori informazioni, consulta i documenti di supporto.</a>',
};

export default items;
