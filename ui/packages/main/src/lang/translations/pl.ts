import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'Monity AI',
	ATeamMemberIsRequired: 'Potr<PERSON>bny jest członek zespołu',
	AboutClient: '<PERSON>',
	AcceptAppointment: 'D<PERSON><PERSON>ku<PERSON>my za potwierdzenie rezerwacji',
	AcceptTermsAndConditionsRequired: 'Akceptuję warunki ',
	Accepted: 'Zaakceptowane',
	AccessGiven: 'Dostęp udzielony',
	AccessPermissions: 'Uprawnienia dostępu',
	AccessType: 'Typ dostępu',
	Accident: 'Wy<PERSON><PERSON>',
	Account: 'Konto',
	AccountCredit: 'Kredyt na koncie',
	Accountant: 'Księgowy',
	Action: 'Działanie',
	Actions: 'Akcje',
	Active: 'Aktywny',
	ActiveTags: 'Aktywne tagi',
	ActiveUsers: 'Aktywni użytkownicy',
	Activity: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
	Actor: 'Aktor',
	Acupuncture: 'Akupunk<PERSON>',
	Acupuncturist: 'Specjalista od akupunktury',
	Acupuncturists: 'Akupunkturzyści',
	AcuteManifestationOfAChronicCondition: 'Ostre objawy przewlekłego stanu chorobowego',
	Add: 'Dodać',
	AddADescription: 'Dodaj opis',
	AddALocation: 'Dodaj lokalizację',
	AddASecondTimezone: 'Dodaj drugą strefę czasową',
	AddAddress: 'Dodaj adres',
	AddAnother: '  Dodaj kolejny',
	AddAnotherAccount: 'Dodaj kolejne konto',
	AddAnotherContact: 'Dodaj kolejny kontakt',
	AddAnotherOption: 'Dodaj inną opcję',
	AddAnotherTeamMember: 'Dodaj innego członka zespołu',
	AddAvailablePayers: '+ Dodaj dostępnych płatników',
	AddAvailablePayersDescription:
		'Wyszukaj płatników, aby dodać ich do listy płatników w Twojej przestrzeni roboczej. Po dodaniu możesz zarządzać zapisami lub dostosowywać dane płatnika w razie potrzeby.',
	AddCaption: 'Dodaj podpis',
	AddClaim: 'Dodaj roszczenie',
	AddClientFilesModalDescription: 'Aby ograniczyć dostęp, zaznacz opcje w polach wyboru „Dostępne dla”',
	AddClientFilesModalTitle: 'Wgraj pliki dla {name}',
	AddClientNoteButton: 'Dodaj notatkę',
	AddClientNoteModalDescription:
		'Dodaj treść do swojej notatki. Użyj sekcji „Widoczne dla”, aby wybrać jedną lub więcej grup, które mogą zobaczyć tę konkretną notatkę.',
	AddClientNoteModalTitle: 'Dodaj notatkę',
	AddClientOwnerRelationshipModalDescription:
		'Zaproszenie klienta umożliwi mu zarządzanie własnymi informacjami profilowymi i zarządzanie dostępem użytkownika do informacji profilowych.',
	AddClientOwnerRelationshipModalTitle: 'Zaproś klienta',
	AddCode: 'Dodaj kod',
	AddColAfter: 'Dodaj kolumnę po',
	AddColBefore: 'Dodaj kolumnę przed',
	AddCollection: 'Dodaj kolekcję',
	AddColor: 'Dodaj kolor',
	AddColumn: 'Dodaj kolumnę',
	AddContactRelationship: 'Dodaj relację kontaktową',
	AddContacts: 'Dodaj kontakty',
	AddCustomField: 'Dodaj pole niestandardowe',
	AddDate: 'Dodaj datę',
	AddDescription: 'Dodaj opis',
	AddDetail: 'Dodaj szczegół',
	AddDisplayName: 'Dodaj nazwę wyświetlaną',
	AddDxCode: 'Dodaj kod diagnostyczny',
	AddEmail: 'Dodaj e-mail',
	AddFamilyClientRelationshipModalDescription:
		'Zaproszenie członka rodziny pozwoli mu zobaczyć historie opieki i informacje o profilu klienta. Jeśli zostanie zaproszony jako administrator, będzie miał dostęp do aktualizacji informacji o profilu klienta i zarządzania dostępem użytkownika.',
	AddFamilyClientRelationshipModalTitle: 'Zaproś członka rodziny',
	AddField: 'Dodaj pole',
	AddFormField: 'Dodaj pole formularza',
	AddImages: 'Dodaj obrazy',
	AddInsurance: 'Dodaj ubezpieczenie',
	AddInvoice: 'Dodaj fakturę',
	AddLabel: 'Dodaj etykietę',
	AddLanguage: 'Dodaj język',
	AddLocation: 'Dodaj lokalizację',
	AddManually: 'Dodaj ręcznie',
	AddMessage: 'Dodaj wiadomość',
	AddNewAction: 'Dodaj nową akcję',
	AddNewSection: 'Dodaj nową sekcję',
	AddNote: 'Dodaj notatkę',
	AddOnlineBookingDetails: 'Dodaj szczegóły rezerwacji online',
	AddPOS: 'Dodaj POS',
	AddPaidInvoices: 'Dodaj zapłacone faktury',
	AddPayer: 'Dodaj płatnika',
	AddPayment: 'Dodaj płatność',
	AddPaymentAdjustment: 'Dodaj korektę płatności',
	AddPaymentAdjustmentDisabledDescription: 'Podział płatności nie ulegnie zmianie.',
	AddPaymentAdjustmentEnabledDescription: 'Kwota dostępna do rozdysponowania zostanie zmniejszona.',
	AddPhoneNumber: 'Dodaj numer telefonu',
	AddPhysicalOrVirtualLocations: 'Dodaj lokalizacje fizyczne lub wirtualne',
	AddQuestion: 'Dodaj pytanie',
	AddQuestionOrTitle: 'Dodaj pytanie lub tytuł',
	AddRelationship: 'Dodaj relację',
	AddRelationshipModalTitle: 'Połącz istniejący kontakt',
	AddRelationshipModalTitleNewClient: 'Połącz nowy kontakt',
	AddRow: 'Dodaj wiersz',
	AddRowAbove: 'Dodaj wiersz powyżej',
	AddRowBelow: 'Dodaj wiersz poniżej',
	AddService: 'Dodaj usługę',
	AddServiceLocation: 'Dodaj lokalizację usługi',
	AddServiceToCollections: 'Dodaj usługę do kolekcji',
	AddServiceToOneOrMoreCollections: 'Dodaj usługę do jednej lub więcej kolekcji',
	AddServices: 'Dodaj usługi',
	AddSignature: 'Dodaj podpis',
	AddSignaturePlaceholder: 'Wpisz dodatkowe szczegóły, które chcesz dołączyć do swojego podpisu',
	AddSmartDataChips: 'Dodaj inteligentne układy danych',
	AddStaffClientRelationshipsModalDescription:
		'Wybór personelu umożliwi im tworzenie i przeglądanie historii opieki nad tym klientem. Będą oni również mogli przeglądać informacje o kliencie.',
	AddStaffClientRelationshipsModalTitle: 'Dodaj relacje między pracownikami',
	AddTag: 'Dodaj tag',
	AddTags: 'Dodaj tagi',
	AddTemplate: 'Dodaj szablon',
	AddTimezone: 'Dodaj strefę czasową',
	AddToClaim: 'Dodaj do roszczenia',
	AddToCollection: 'Dodaj do kolekcji',
	AddToExisting: 'Dodaj do istniejącego',
	AddToStarred: 'Dodaj do oznaczonych gwiazdką',
	AddUnclaimedItems: 'Dodaj nieodebrane przedmioty',
	AddUnrelatedContactWarning:
		'Dodałeś kogoś, kto nie jest kontaktem {contact}. Upewnij się, że treść jest odpowiednia, zanim przejdziesz do udostępniania.',
	AddValue: 'Dodaj "{value}"',
	AddVideoCall: 'Dodaj połączenie wideo',
	AddVideoOrVoiceCall: 'Dodaj połączenie wideo lub głosowe',
	AddictionCounselor: 'Doradca ds. uzależnień',
	AddingManualPayerDisclaimer:
		'Ręczne dodanie płatnika do listy dostawców nie powoduje utworzenia elektronicznego połączenia umożliwiającego składanie roszczeń z tym płatnikiem, ale umożliwia ręczne tworzenie roszczeń.',
	AddingTeamMembersIncreaseCostAlert:
		'Dodanie nowych członków zespołu spowoduje zwiększenie miesięcznej opłaty za subskrypcję.',
	Additional: 'Dodatkowy',
	AdditionalBillingProfiles: 'Dodatkowe profile rozliczeniowe',
	AdditionalBillingProfilesSectionDescription:
		'Zastąp domyślne dane rozliczeniowe używane dla określonych członków zespołu, płatników lub szablonów faktur.',
	AdditionalFeedback: 'Dodatkowe informacje zwrotne',
	AddnNewWorkspace: 'Nowe miejsce pracy',
	AddnNewWorkspaceSuccessSnackbar: 'Obszar roboczy został utworzony!',
	Address: 'Adres',
	AddressNumberStreet: 'Adres (nr, ulica)',
	Adjustment: 'Dopasowanie',
	AdjustmentType: 'Typ regulacji',
	Admin: 'Administrator',
	Admins: 'Administratorzy',
	AdminsOnly: 'Tylko dla administratorów',
	AdvancedPlanInclusionFive: 'Menedżer ds. kont',
	AdvancedPlanInclusionFour: 'Analiza Google',
	AdvancedPlanInclusionHeader: 'Wszystko w Plus  ',
	AdvancedPlanInclusionOne: 'Role ',
	AdvancedPlanInclusionSix: 'Wsparcie importu danych',
	AdvancedPlanInclusionThree: 'Białe etykietowanie',
	AdvancedPlanInclusionTwo: '90 dni przechowywania usuniętych danych',
	AdvancedPlanMessage:
		'Zachowaj kontrolę nad potrzebami swojej praktyki. Przejrzyj swój obecny plan i monitoruj jego wykorzystanie.',
	AdvancedSettings: 'Ustawienia zaawansowane',
	AdvancedSubscriptionPlanSubtitle: 'Rozszerz swoją praktykę o wszystkie funkcje',
	AdvancedSubscriptionPlanTitle: 'Zaawansowany',
	AdvertisingManager: 'Menedżer ds. Reklamy',
	AerospaceEngineer: 'Inżynier lotnictwa i kosmonautyki',
	AgeYearsOld: '{age} lat',
	Agenda: 'Porządek obrad',
	AgendaView: 'Widok agendy',
	AiAskSupportedFileTypes: 'Obsługiwane typy plików: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Asystent na wyciągnięcie ręki',
	AiCopilotDisclaimer: 'AI Copilot może popełniać błędy. Sprawdź ważne informacje.',
	AiCreateNewConversation: 'Utwórz nową rozmowę',
	AiEnhanceYourProductivity: 'Zwiększ swoją produktywność',
	AiPoweredTemplates: 'Szablony oparte na sztucznej inteligencji',
	AiScribeNoDeviceFoundErrorMessage:
		'Wygląda na to, że Twoja przeglądarka nie obsługuje tej funkcji lub nie są dostępne żadne zgodne urządzenia.',
	AiScribeUploadFormat: 'Obsługiwane typy plików: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'tylko 1 plik na raz',
	AiShowConversationHistory: 'Pokaż historię konwersacji',
	AiSmartPromptNodePlaceholderText:
		'Wpisz swoje niestandardowe polecenie tutaj, aby pomóc w generowaniu dokładnych i spersonalizowanych wyników AI.',
	AiSmartPromptPrimaryText: 'Inteligentny monit AI',
	AiSmartPromptSecondaryText: 'Wstaw niestandardową inteligentną podpowiedź AI',
	AiSmartReminders: 'Inteligentne przypomnienia AI',
	AiTemplateBannerTitle: 'Uprość swoją pracę dzięki szablonom opartym na sztucznej inteligencji',
	AiTemplates: 'Szablony AI',
	AiTokens: 'Tokeny AI',
	AiWorkBetterWithAi: 'Pracuj lepiej z AI',
	All: 'Wszystko',
	AllAppointments: 'Wszystkie spotkania',
	AllCategories: 'Wszystkie kategorie',
	AllClients: 'Wszyscy klienci',
	AllContactPolicySelectorLabel: 'Wszyscy kontakty <mark>{client}</mark>',
	AllContacts: 'Wszystkie kontakty',
	AllContactsOf: 'Wszyscy kontakty ‘{name}’',
	AllDay: 'Cały dzień',
	AllInboxes: 'Wszystkie skrzynki odbiorcze',
	AllIndustries: 'Wszystkie branże',
	AllLocations: 'Wszystkie lokalizacje',
	AllMeetings: 'Wszystkie spotkania',
	AllNotificationsRestoredMessage: 'Wszystkie powiadomienia przywrócone',
	AllProfessions: 'Wszystkie zawody',
	AllReminders: 'Wszystkie przypomnienia',
	AllServices: 'Wszystkie usługi',
	AllStatuses: 'Wszystkie statusy',
	AllTags: 'Wszystkie tagi',
	AllTasks: 'Wszystkie zadania',
	AllTeamMembers: 'Wszyscy członkowie zespołu',
	AllTypes: 'Wszystkie typy',
	Allocated: 'Asygnowany',
	AllocatedItems: 'Przydzielone przedmioty',
	AllocationTableEmptyState: 'Nie znaleziono żadnych alokacji płatności',
	AllocationTotalWarningMessage: `Przydzielona kwota przekracza całkowitą kwotę płatności.
 Proszę przejrzeć pozycje poniżej.`,
	AllowClientsToCancelAnytime: 'Pozwól klientom anulować w dowolnym momencie',
	AllowNewClient: 'Zezwalaj na nowych klientów',
	AllowNewClientHelper: 'Nowi klienci mogą zarezerwować tę usługę',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Zarezerwuj co najmniej {hours} godzin przed umówioną wizytą',
	AllowToUseSavedCard: 'Zezwalaj {provider} na używanie zapisanej karty w przyszłości',
	AllowVideoCalls: 'Zezwalaj na połączenia wideo',
	AlreadyAdded: 'Już dodano',
	AlreadyHasAccess: 'Ma dostęp',
	AlreadyHasAccount: 'Masz już konto?',
	Always: 'Zawsze',
	AlwaysIgnore: 'Zawsze ignoruj',
	Amount: 'Kwota',
	AmountDue: 'Kwota należna',
	AmountOfReferralRequests: '{amount, plural, one {# prośba o skierowanie} other {# prośby o skierowanie}}',
	AmountPaid: 'Kwota zapłacona',
	AnalyzingAudio: 'Analizowanie dźwięku...',
	AnalyzingInputContent: 'Analizowanie treści wejściowej...',
	AnalyzingRequest: 'Analizowanie żądania...',
	AnalyzingTemplateContent: 'Analizowanie zawartości szablonu...',
	And: 'I',
	Annually: 'Rocznie',
	Anonymous: 'Anonimowy',
	AnswerExceeded: 'Twoja odpowiedź musi zawierać mniej niż 300 znaków.',
	AnyStatus: 'Jakikolwiek status',
	AppNotifications: 'Powiadomienia',
	AppNotificationsClearanceHeading: 'Dobra robota! Wyczyściłeś całą aktywność',
	AppNotificationsEmptyHeading: 'Twoja aktywność w obszarze roboczym wkrótce pojawi się tutaj',
	AppNotificationsEmptySubtext: 'Na razie nie ma żadnych działań do podjęcia',
	AppNotificationsIgnoredCount: '{total} zignorowanych',
	AppNotificationsUnread: '{total} nieprzeczytanych',
	Append: 'Dodać',
	Apply: 'Stosować',
	ApplyAccountCredit: 'Zastosuj kredyt na koncie',
	ApplyDiscount: 'Zastosuj zniżkę',
	ApplyVisualEffects: 'Zastosuj efekty wizualne',
	ApplyVisualEffectsNotSupported: 'Zastosuj efekty wizualne nieobsługiwane',
	Appointment: 'Spotkanie',
	AppointmentAssignedNotificationSubject: '{actorProfileName} przypisał(a) Ci {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} odwołał/a {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} potwierdził(a) {appointmentName}',
	AppointmentDetails: 'Szczegóły spotkania',
	AppointmentLocation: 'Miejsce spotkania',
	AppointmentLocationDescription:
		'Zarządzaj domyślnymi lokalizacjami wirtualnymi i fizycznymi. Po zaplanowaniu wizyty, te lokalizacje zostaną automatycznie zastosowane.',
	AppointmentNotFound: 'Nie znaleziono terminu',
	AppointmentReminder: 'Przypomnienie o spotkaniu',
	AppointmentReminders: 'Przypomnienia o spotkaniach',
	AppointmentRemindersInfo:
		'Ustaw automatyczne przypomnienia o wizytach klientów, aby uniknąć nieobecności i anulowania wizyt',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} przełożył(a) {appointmentName}',
	AppointmentSaved: 'Spotkanie zapisane',
	AppointmentStatus: 'Status spotkania',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}godz {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}godz {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Spotkanie odwołane',
	Appointments: 'Spotkania',
	Archive: 'Archiwum',
	ArchiveClients: 'Archiwizuj klientów',
	Archived: 'Zarchiwizowano',
	AreYouAClient: 'Czy jesteś klientem?',
	AreYouStillThere: 'Jesteś tam jeszcze?',
	AreYouSure: 'Jesteś pewien?',
	Arrangements: 'Ustalenia',
	ArtTherapist: 'Terapeuta przez sztukę',
	Articles: 'Artykuły',
	Artist: 'Artysta',
	AskAI: 'Zapytaj AI',
	AskAiAddFormField: 'Dodaj pole formularza',
	AskAiChangeFormality: 'Zmień formalność',
	AskAiChangeToneToBeMoreProfessional: 'Zmień ton na bardziej profesjonalny',
	AskAiExplainThis: 'AskAiWyjaśnijTo',
	AskAiExplainWhatThisDocumentIsAbout: 'Wyjaśnij, czego dotyczy ten dokument',
	AskAiExplainWhatThisImageIsAbout: 'Wyjaśnij, co przedstawia ten obraz',
	AskAiFixSpellingAndGrammar: 'Popraw pisownię i gramatykę',
	AskAiGenerateACaptionForThisImage: 'Wygeneruj podpis do tego obrazu',
	AskAiGenerateFromThisPage: 'Wygeneruj z tej strony',
	AskAiGetStarted: 'Zacznij',
	AskAiGiveItAFriendlyTone: 'Nadaj mu przyjazny ton',
	AskAiGreeting: 'Cześć {firstName}! W czym mogę Ci dzisiaj pomóc?',
	AskAiHowCanIHelpWithYourContent: 'W jaki sposób mogę pomóc w tworzeniu treści?',
	AskAiInsert: 'Wstawić',
	AskAiMakeItMoreCasual: 'Uczyń to bardziej swobodnym',
	AskAiMakeThisTextMoreConcise: 'Uczyń ten tekst bardziej zwięzłym',
	AskAiMoreProfessional: 'Bardziej profesjonalny',
	AskAiOpenPreviousNote: 'Otwórz poprzednią notatkę',
	AskAiPondering: 'Rozważanie',
	AskAiReplace: 'Zastępować',
	AskAiReviewOrEditSelection: 'Przejrzyj lub edytuj wybór',
	AskAiRuminating: 'Rozmyślanie',
	AskAiSeeMore: 'Zobacz więcej',
	AskAiSimplifyLanguage: 'Uprość język',
	AskAiSomethingWentWrong:
		'Coś poszło nie tak. Jeśli problem będzie się powtarzał, skontaktuj się z nami za pośrednictwem naszego centrum pomocy.',
	AskAiStartWithATemplate: 'Zacznij od szablonu',
	AskAiSuccessfullyCopiedResponse: 'Pomyślnie skopiowano odpowiedź AI',
	AskAiSuccessfullyInsertedResponse: 'Pomyślnie wstawiono odpowiedź AI',
	AskAiSuccessfullyReplacedResponse: 'Pomyślnie zastąpiono odpowiedź AI',
	AskAiSuggested: 'Sugerowane',
	AskAiSummariseTextIntoBulletPoints: 'Podsumuj tekst w punktach wypunktowanych',
	AskAiSummarizeNote: 'Podsumuj notatkę',
	AskAiThinking: 'Myślący',
	AskAiToday: 'Dzisiaj {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Co chcesz zrobić za pomocą tego formularza?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Napisz profesjonalną notatkę, korzystając z szablonu',
	AskAskAiAnything: 'Zapytaj AI o cokolwiek',
	AskWriteSearchAnything: `Zapytaj, napisz '@' lub wyszukaj cokolwiek...`,
	Asking: 'Pytanie',
	Assessment: 'Ocena',
	Assessments: 'Oceny',
	AssessmentsCategoryDescription: 'Do rejestrowania ocen klientów',
	AssignClients: 'Przypisz klientów',
	AssignNewClients: 'Przypisz klientów',
	AssignServices: 'Przypisz usługi',
	AssignTeam: 'Przypisz zespół',
	AssignTeamMember: 'Przypisz członka zespołu',
	Assigned: 'Przydzielony',
	AssignedClients: 'Przypisani klienci',
	AssignedServices: 'Przypisane usługi',
	AssignedServicesDescription:
		'Przeglądaj i zarządzaj przypisanymi Ci usługami, dostosowując ceny do swoich indywidualnych stawek. ',
	AssignedTeam: 'Przydzielony zespół',
	AthleticTrainer: 'Trener sportowy',
	AttachFiles: 'Dołącz pliki',
	AttachLogo: 'Przytwierdzać',
	Attachment: 'Załącznik',
	AttachmentBlockedFileType: 'Zablokowano ze względów bezpieczeństwa!',
	AttachmentTooLargeFileSize: 'Plik jest za duży',
	AttachmentUploadItemComplete: 'Kompletny',
	AttachmentUploadItemError: 'Nie udało się przesłać',
	AttachmentUploadItemLoading: 'Załadunek',
	AttemptingToReconnect: 'Próba ponownego połączenia...',
	Attended: 'Uczęszczany',
	AttendeeBeingMutedTooltip: `Gospodarz wyciszył Cię. Użyj 'podnieś rękę', aby poprosić o wyłączenie wyciszenia`,
	AttendeeWithId: 'Uczestnik {attendeeId}',
	Attendees: 'Uczestnicy',
	AttendeesCount: '{count} uczestników',
	Attending: 'Uczestniczenie',
	Audiologist: 'Audiolog',
	Aunt: 'Ciotka',
	Australia: 'Australia',
	AuthenticationCode: 'Kod uwierzytelniający',
	AuthoriseProvider: 'Autoryzuj {provider}',
	AuthorisedProviders: 'Autoryzowani dostawcy',
	AutoDeclineAllFutureOption: 'Tylko nowe wydarzenia lub spotkania',
	AutoDeclineAllOption: 'Nowe i istniejące wydarzenia lub terminy',
	AutoDeclinePrimaryText: 'Automatycznie odrzucaj wydarzenia',
	AutoDeclineSecondaryText: 'Wydarzenia w okresie Twojej nieobecności zostaną automatycznie odrzucone.',
	AutogenerateBillings: 'Automatyczne generowanie dokumentów rozliczeniowych',
	AutogenerateBillingsDescription:
		'Zautomatyzowane dokumenty rozliczeniowe będą generowane ostatniego dnia miesiąca. Faktury i paragony za superrachunki można tworzyć ręcznie w dowolnym momencie.',
	AutomateWorkflows: 'Automatyzacja przepływów pracy',
	AutomaticallySendSuperbill: 'Automatyczne wysyłanie pokwitowań Super Bill',
	AutomaticallySendSuperbillHelperText:
		'Superrachunek to szczegółowy rachunek za usługi świadczone klientowi w celu uzyskania zwrotu kosztów od ubezpieczyciela.',
	Automation: 'Automatyzacja',
	AutomationActionSendEmailLabel: 'Wyślij e-mail',
	AutomationActionSendSMSLabel: 'Wyślij SMS',
	AutomationAndReminders: 'Automatyzacja ',
	AutomationDeletedSuccessMessage: 'Pomyślnie usunięto automatyzację',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Wydarzenie czasowe',
	AutomationParams_timeUnit: 'Jednostka',
	AutomationParams_timeValue: 'Numer',
	AutomationPublishSuccessMessage: 'Automatyzacja opublikowana pomyślnie',
	AutomationPublishWarningTooltip:
		'Sprawdź ponownie konfigurację automatyzacji i upewnij się, że została skonfigurowana prawidłowo',
	AutomationTriggerEventCancelledDescription: 'Wyzwalane, gdy zdarzenie zostanie anulowane lub usunięte',
	AutomationTriggerEventCancelledLabel: 'Wydarzenie odwołane',
	AutomationTriggerEventCreatedDescription: 'Wyzwalane po utworzeniu zdarzenia',
	AutomationTriggerEventCreatedLabel: 'Nowe wydarzenie',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Wyzwalane, gdy zdarzenie jest tworzone lub aktualizowane (z wyjątkiem sytuacji, gdy jest anulowane)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Nowe lub zaktualizowane wydarzenie',
	AutomationTriggerEventEndedDescription: 'Wyzwala się, gdy zdarzenie się zakończy',
	AutomationTriggerEventEndedLabel: 'Wydarzenie zakończone',
	AutomationTriggerEventStartsDescription: 'Wyzwala się, gdy określony czas przed rozpoczęciem zdarzenia',
	AutomationTriggerEventStartsLabel: 'Wydarzenie się rozpoczyna',
	Automations: 'Automatyzacje',
	Availability: 'Dostępność',
	AvailabilityDisableSchedule: 'Wyłącz harmonogram',
	AvailabilityDisabled: 'Wyłączony',
	AvailabilityEnableSchedule: 'Włącz harmonogram',
	AvailabilityEnabled: 'Włączony',
	AvailabilityNoActiveBanner:
		'Wyłączyłeś wszystkie swoje harmonogramy. Klienci nie mogą Cię rezerwować online, a wszystkie przyszłe spotkania muszą być potwierdzane ręcznie.',
	AvailabilityNoActiveConfirmationDescription:
		'Wyłączenie tej dostępności spowoduje brak aktywnych harmonogramów. Klienci nie będą mogli rezerwować wizyt online, a wszelkie rezerwacje dokonane przez praktyków będą poza godzinami pracy.',
	AvailabilityNoActiveConfirmationProceed: 'Tak, kontynuuj',
	AvailabilityNoActiveConfirmationTitle: 'Brak aktywnych harmonogramów',
	AvailabilityToggle: 'Włączony harmonogram',
	AvailabilityUnsetDate: 'Brak ustalonej daty',
	AvailableLocations: 'Dostępne lokalizacje',
	AvailablePayers: 'Dostępni płatnicy',
	AvailablePayersEmptyState: 'Brak wybranych płatników',
	AvailableTimes: 'Dostępne godziny',
	Back: 'Z powrotem',
	BackHome: 'Powrót do domu',
	BackToAppointment: 'Powrót do terminu',
	BackToLogin: 'Powrót do logowania',
	BackToMapColumns: 'Wróć do kolumn mapy',
	BackToTemplates: 'Wróć do Szablonów',
	BackToUploadFile: 'Powrót do Wgrywania pliku',
	Banker: 'Bankier',
	BasicBlocks: 'Podstawowe bloki',
	BeforeAppointment: 'Wyślij przypomnienie {deliveryType} {interval} {unit} przed wizytą',
	BehavioralAnalyst: 'Analityk behawioralny',
	BehavioralHealthTherapy: 'Terapia behawioralna',
	Beta: 'Beta',
	BillTo: 'Rachunek do',
	BillableItems: 'Pozycje rozliczeniowe',
	BillableItemsEmptyState: 'Nie znaleziono żadnych pozycji podlegających rozliczeniu',
	Biller: 'Fakturujący',
	Billing: 'Rozliczanie',
	BillingAddress: 'Adres rozliczeniowy',
	BillingAndReceiptsUnauthorisedMessage:
		'Aby uzyskać dostęp do tych informacji, wymagany jest dostęp do faktur i płatności.',
	BillingBillablesTab: 'Rozliczenia',
	BillingClaimsTab: 'Roszczenia',
	BillingDetails: 'Szczegóły rozliczeń',
	BillingDocuments: 'Dokumenty rozliczeniowe',
	BillingDocumentsClaimsTab: 'Roszczenia',
	BillingDocumentsEmptyState: 'Nie znaleziono żadnych {tabType}',
	BillingDocumentsInvoicesTab: 'Faktury',
	BillingDocumentsSuperbillsTab: 'Superrachunki',
	BillingInformation: 'Informacje o rozliczeniach',
	BillingInvoicesTab: 'Faktury',
	BillingItems: 'Pozycje rozliczeniowe',
	BillingPaymentsTab: 'Płatności',
	BillingPeriod: 'Okres rozliczeniowy',
	BillingProfile: 'Profil rozliczeniowy',
	BillingProfileOverridesDescription:
		'Ogranicz dostęp do tego profilu rozliczeniowego dla określonych członków zespołu.',
	BillingProfileOverridesHeader: 'Ogranicz dostęp',
	BillingProfileProviderType: 'Typ dostawcy',
	BillingProfileTypeIndividual: 'Praktykujący',
	BillingProfileTypeIndividualSubLabel: 'Typ 1 NPI',
	BillingProfileTypeOrganisation: 'Organizacja',
	BillingProfileTypeOrganisationSubLabel: 'Typ 2 NPI',
	BillingProfiles: 'Profile rozliczeniowe',
	BillingProfilesEditHeader: 'Edytuj profil rozliczeniowy {name}',
	BillingProfilesNewHeader: 'Nowy profil rozliczeniowy',
	BillingProfilesSectionDescription:
		'Zarządzaj informacjami dotyczącymi rozliczeń z lekarzami i płatnikami ubezpieczeń, tworząc profile rozliczeniowe, które można stosować do faktur i wypłat od ubezpieczycieli.',
	BillingSearchPlaceholder: 'Wyszukaj elementy',
	BillingSettings: 'Ustawienia rozliczeń',
	BillingSuperbillsTab: 'Superrachunki',
	BiomedicalEngineer: 'Inżynier biomedyczny',
	BlankInvoice: 'Pusta faktura',
	BlueShieldProviderNumber: 'Numer dostawcy Blue Shield',
	Body: 'Ciało',
	Bold: 'Pogrubiony',
	BookAgain: 'Zarezerwuj ponownie',
	BookAppointment: 'Zarezerwuj wizytę',
	BookableOnline: 'Możliwość rezerwacji online',
	BookableOnlineHelper: 'Klienci mogą zarezerwować tę usługę online',
	BookedOnline: 'Zarezerwowane online',
	Booking: 'Rezerwacja',
	BookingAnalyticsIntegrationPanelDescription:
		'Skonfiguruj Menedżera tagów Google, aby śledzić kluczowe działania i konwersje w przepływie rezerwacji online. Zbieraj cenne dane na temat interakcji użytkowników, aby ulepszyć działania marketingowe i zoptymalizować doświadczenie rezerwacji.',
	BookingAnalyticsIntegrationPanelTitle: 'Integracja analityki',
	BookingAndCancellationPolicies: 'Rezerwacja ',
	BookingButtonEmbed: 'Przycisk',
	BookingButtonEmbedDescription: 'Dodaje przycisk rezerwacji online do Twojej witryny',
	BookingDirectTextLink: 'Bezpośredni link tekstowy',
	BookingDirectTextLinkDescription: 'Otwiera stronę rezerwacji online',
	BookingFormatLink: 'Format łącza',
	BookingFormatLinkButtonTitle: 'Tytuł przycisku',
	BookingInlineEmbed: 'Osadzanie w tekście',
	BookingInlineEmbedDescription: 'Ładuje stronę rezerwacji online bezpośrednio na Twojej stronie internetowej',
	BookingLink: 'Link do rezerwacji',
	BookingLinkModalCopyText: 'Kopia',
	BookingLinkModalDescription:
		'Zezwól klientom korzystającym z tego łącza na rezerwację dowolnego członka zespołu lub usług',
	BookingLinkModalHelpText: 'Dowiedz się, jak skonfigurować rezerwacje online',
	BookingLinkModalTitle: 'Udostępnij link do swojej rezerwacji',
	BookingPolicies: 'Zasady rezerwacji',
	BookingPoliciesDescription: 'Ustaw, kiedy klienci mogą dokonywać rezerwacji online',
	BookingTimeUnitDays: 'dni',
	BookingTimeUnitHours: 'godziny',
	BookingTimeUnitMinutes: 'protokół',
	BookingTimeUnitMonths: 'miesiące',
	BookingTimeUnitWeeks: 'tygodnie',
	BottomNavBilling: 'Rozliczanie',
	BottomNavGettingStarted: 'Dom',
	BottomNavMore: 'Więcej',
	BottomNavNotes: 'Notatki',
	Brands: 'Marki',
	Brother: 'Brat',
	BrotherInLaw: 'Szwagier',
	BrowseOrDragFileHere: '<link>Przeglądaj</link> lub przeciągnij plik tutaj',
	BrowseOrDragFileHereDescription: 'PNG, JPG (maks. {limit})',
	BufferAfterTime: '{time} min po',
	BufferAndLabel: 'I',
	BufferAppointmentLabel: 'spotkanie',
	BufferBeforeTime: '{time} min przed',
	BufferTime: 'Czas buforowy',
	BufferTimeViewLabel: '{bufferBefore} min przed i {bufferAfter} min po wizytach',
	BulkArchiveClientsDescription:
		'Czy na pewno chcesz zarchiwizować tych klientów? Możesz ich ponownie aktywować później.',
	BulkArchiveSuccess: 'Pomyślnie zarchiwizowano klientów',
	BulkArchiveUndone: 'Archiwum zbiorcze zostało cofnięte',
	BulkPermanentDeleteDescription: 'To spowoduje usunięcie **{count} rozmów**. Tej akcji nie można cofnąć.',
	BulkPermanentDeleteTitle: 'Usuń konwersacje na zawsze',
	BulkUnarchiveSuccess: 'Pomyślnie odzyskano archiwa klientów',
	BulletedList: 'Lista wypunktowana',
	BusinessAddress: 'Adres firmy',
	BusinessAddressOptional: 'Adres firmy <span>(opcjonalnie)</span>',
	BusinessName: 'Nazwa firmy',
	Button: 'Przycisk',
	By: 'Przez',
	CHAMPUSIdentificationNumber: 'Numer identyfikacyjny CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'Kod CVC jest wymagany',
	Calendar: 'Kalendarz',
	CalendarAppSyncFormDescription: 'Synchronizuj wydarzenia Carepatron z',
	CalendarAppSyncPanelTitle: 'Synchronizacja połączonych aplikacji',
	CalendarDescription: 'Zarządzaj swoimi spotkaniami lub ustawiaj osobiste zadania i przypomnienia',
	CalendarDetails: 'Szczegóły kalendarza',
	CalendarDetailsDescription: 'Zarządzaj kalendarzem i ustawieniami wyświetlania spotkań.',
	CalendarScheduleNew: 'Zaplanuj nowy',
	CalendarSettings: 'Ustawienia kalendarza',
	Call: 'Dzwonić',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> dołączył(a) do rozmowy wideo',
	CallChangeLayoutTextContent: 'Wybór zostanie zapisany na potrzeby przyszłych spotkań',
	CallIdlePrompt: 'Czy wolisz czekać na dołączenie, czy spróbować ponownie później?',
	CallLayoutOptionAuto: 'Automatyczny',
	CallLayoutOptionSidebar: 'Pasek boczny',
	CallLayoutOptionSpotlight: 'Reflektor',
	CallLayoutOptionTiled: 'Taflowy',
	CallNoAttendees: 'Brak uczestników spotkania.',
	CallSessionExpiredError: 'Sesja wygasła. Połączenie zostało zakończone. Spróbuj dołączyć ponownie.',
	CallWithPractitioner: 'Połączenie z {practitioner}',
	CallsListCreateButton: 'Nowe połączenie',
	CallsListEmptyState: 'Brak aktywnych połączeń',
	CallsListItemEndCall: 'Zakończ połączenie',
	CamWarningMessage: 'Wykryto problem z aparatem',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Proszę zezwolić Carepatronowi na dostęp do kamery i mikrofonu.
 Aby uzyskać więcej informacji, <a>zapoznaj się z tym przewodnikiem</a>`,
	CameraAndMicIssueModalTitle: 'Kamera i mikrofon są zablokowane',
	CameraQuality: 'Jakość kamery',
	CameraSource: 'Źródło kamery',
	CanModifyReadOnlyEvent: 'Nie możesz modyfikować tego wydarzenia',
	Canada: 'Kanada',
	Cancel: 'Anulować',
	CancelClientImportDescription: 'Czy na pewno chcesz anulować ten import?',
	CancelClientImportPrimaryAction: 'Tak, anuluj import',
	CancelClientImportSecondaryAction: 'Kontynuuj edycję',
	CancelClientImportTitle: 'Anuluj importowanie klientów',
	CancelImportButton: 'Anuluj importowanie',
	CancelPlan: 'Anuluj plan',
	CancelPlanConfirmation: `Anulowanie planu spowoduje automatyczne obciążenie Twojego konta wszelkimi zaległymi saldami za ten miesiąc.
 Jeśli chcesz obniżyć poziom rozliczanych użytkowników, możesz po prostu usunąć członków zespołu, a Carepatron automatycznie zaktualizuje cenę subskrypcji.`,
	CancelSend: 'Anuluj wysyłanie',
	CancelSubscription: 'Anuluj subskrypcję',
	Canceled: 'Anulowano',
	CancellationPolicy: 'Zasady anulowania',
	Cancelled: 'Odwołany',
	CannotContainSpecialCharactersError: 'Nie można zawierać {specialCharacters}',
	CannotDeleteInvoice: 'Faktur opłaconych za pomocą płatności online nie można usunąć',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> nie może zostać przeniesiony poza grupę <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Usługi nie można przenieść poza zbiory',
	CapeTown: 'Kapsztad',
	Caption: 'Podpis',
	CaptureNameFieldLabel: 'Nazwa, którą chcesz, aby inni Cię nazywali',
	CapturePaymentMethod: 'Zbierz metodę płatności',
	CapturingAudio: 'Przechwytywanie dźwięku',
	CapturingSignature: 'Przechwytywanie podpisu...',
	CardInformation: 'Informacje o karcie',
	CardNumberRequired: 'Numer karty jest wymagany',
	CardiacRehabilitationSpecialist: 'Specjalista rehabilitacji kardiologicznej',
	Cardiologist: 'Kardiolog',
	CareAiNoConversations: 'Brak rozmów',
	CareAiNoConversationsDescription: 'Rozpocznij rozmowę z {aiName}, aby rozpocząć',
	CareAssistant: 'Asystent Opiekuna',
	CareManager: 'Menedżer ds. opieki',
	Caregiver: 'Opiekun',
	CaregiverCreateModalDescription:
		'Dodanie pracowników jako administratorów umożliwi im tworzenie i zarządzanie historiami opieki. Daje im to również pełny dostęp do tworzenia i zarządzania klientami.',
	CaregiverCreateModalTitle: 'Nowy członek zespołu',
	CaregiverListCantAddStaffInfoTitle:
		'Osiągnąłeś maksymalną liczbę pracowników dla swojej subskrypcji. Uaktualnij swój plan, aby dodać więcej pracowników.',
	CaregiverListCreateButton: 'Nowy członek zespołu',
	CaregiverListEmptyState: 'Nie dodano opiekunów',
	CaregiversListItemRemoveStaff: 'Usuń personel',
	CarepatronApp: 'Aplikacja Carepatron',
	CarepatronCommunity: 'Wspólnota',
	CarepatronFieldAddress: 'Adres',
	CarepatronFieldAssignedStaff: 'Przydzielony personel',
	CarepatronFieldBirthDate: 'Data urodzenia',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: 'Status zatrudnienia',
	CarepatronFieldEthnicity: 'Pochodzenie etniczne',
	CarepatronFieldFirstName: 'Imię',
	CarepatronFieldGender: 'Płeć',
	CarepatronFieldIdentificationNumber: 'Numer identyfikacyjny',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Etykieta',
	CarepatronFieldLastName: 'Nazwisko',
	CarepatronFieldLivingArrangements: 'Warunki mieszkaniowe',
	CarepatronFieldMiddleNames: 'Drugie imię',
	CarepatronFieldOccupation: 'Zawód',
	CarepatronFieldPhoneNumber: 'Numer telefonu',
	CarepatronFieldRelationshipStatus: 'Status związku',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: 'Maksymalnie 10 statusów.',
	CarepatronFieldTags: 'Tagi',
	CarepatronFields: 'Pola opiekunów',
	Cash: 'Gotówka',
	Category: 'Kategoria',
	CategoryInputPlaceholder: 'Wybierz kategorię szablonów',
	CenterAlign: 'Wyśrodkuj',
	Central: 'Centralny',
	ChangeLayout: 'Zmień układ',
	ChangeLogo: 'Zmiana',
	ChangePassword: 'Zmień hasło',
	ChangePasswordFailureSnackbar:
		'Przepraszamy, Twoje hasło nie zostało zmienione. Sprawdź, czy Twoje stare hasło jest poprawne.',
	ChangePasswordHelperInfo: 'Minimalna długość {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Pomyślnie zmieniono hasło! Następnym razem, gdy się zalogujesz, upewnij się, że używasz tego hasła.',
	ChangeSubscription: 'Zmień subskrypcję',
	ChangesNotAllowed: 'W tym polu nie można wprowadzać zmian',
	ChargesDisabled: 'Opłaty wyłączone',
	ChargesEnabled: 'Opłaty włączone',
	ChargesStatus: 'Status opłat',
	ChartAndDiagram: 'Wykres/Diagram',
	ChartsAndDiagramsCategoryDescription: 'Do ilustrowania danych klienta i postępów',
	ChatEditMessage: 'Edytuj wiadomość',
	ChatReplyTo: 'Odpowiedz do {name}',
	ChatTypeMessageTo: 'Wiadomość {name}',
	Check: 'Sprawdzać',
	CheckList: 'Lista kontrolna',
	Chef: 'Szef kuchni',
	Chiropractic: 'Chiropraktyka',
	Chiropractor: 'Kręgarz',
	Chiropractors: 'Chiropraktycy',
	ChooseACollection: 'Wybierz kolekcję',
	ChooseAContact: 'Wybierz kontakt',
	ChooseAccountTypeHeader: 'Które z nich najlepiej Cię opisuje?',
	ChooseAction: 'Wybierz akcję',
	ChooseAnAccount: 'Wybierz konto',
	ChooseAnOption: 'Wybierz opcję',
	ChooseBillingProfile: 'Wybierz profil rozliczeniowy',
	ChooseClaim: 'Wybierz roszczenie',
	ChooseCollection: 'Wybierz kolekcję',
	ChooseColor: 'Wybierz kolor',
	ChooseCustomDate: 'Wybierz niestandardową datę',
	ChooseDateAndTime: 'Wybierz datę i godzinę',
	ChooseDxCodes: 'Wybierz kody diagnostyczne',
	ChooseEventType: 'Wybierz typ wydarzenia',
	ChooseFileButton: 'Wybierz plik',
	ChooseFolder: 'Wybierz folder',
	ChooseInbox: 'Wybierz skrzynkę odbiorczą',
	ChooseMethod: 'Wybierz metodę',
	ChooseNewOwner: 'Wybierz nowego właściciela',
	ChooseOrganization: 'Wybierz organizację',
	ChoosePassword: 'Wybierz hasło',
	ChoosePayer: 'Wybierz płatnika',
	ChoosePaymentMethod: 'Wybierz metodę płatności',
	ChoosePhysicalOrRemoteLocations: 'Wprowadź lub wybierz lokalizację',
	ChoosePlan: 'Wybierz {plan}',
	ChooseProfessional: 'Wybierz Profesjonalistę',
	ChooseServices: 'Wybierz usługi',
	ChooseSource: 'Wybierz źródło',
	ChooseSourceDescription:
		'Wybierz, skąd importujesz klientów – czy to z pliku, czy z innej platformy oprogramowania.',
	ChooseTags: 'Wybierz tagi',
	ChooseTaxName: 'Wybierz nazwę podatku',
	ChooseTeamMembers: 'Wybierz członków zespołu',
	ChooseTheme: 'Wybierz motyw',
	ChooseTrigger: 'Wybierz wyzwalacz',
	ChooseYourProvider: 'Wybierz swojego dostawcę',
	CircularProgressWithLabel: '{value}%',
	City: 'Miasto',
	CivilEngineer: 'Inżynier Budowlany',
	Claim: 'Prawo',
	ClaimAddReferringProvider: 'Dodaj lekarza kierującego',
	ClaimAddRenderingProvider: 'Dodaj dostawcę renderowania',
	ClaimAmount: 'Kwota roszczenia',
	ClaimAmountPaidHelpContent:
		'Kwota zapłacona to płatność otrzymana od pacjenta lub innych płatników. Wprowadź całkowitą kwotę, którą pacjent i/lub inni płatnicy zapłacili wyłącznie za objęte ubezpieczeniem usługi.',
	ClaimAmountPaidHelpSubtitle: 'Pole 29',
	ClaimAmountPaidHelpTitle: 'Kwota zapłacona',
	ClaimBillingProfileTypeIndividual: 'Indywidualny',
	ClaimBillingProfileTypeOrganisation: 'Organizacja',
	ClaimChooseRenderingProviderOrTeamMember: 'Wybierz dostawcę renderowania lub członka zespołu',
	ClaimClientInsurancePolicies: 'Polisy ubezpieczeniowe klientów',
	ClaimCreatedAction: '<mark>Zgłoszenie {claimNumber}</mark> utworzone',
	ClaimDeniedAction: '<mark>Wniosek {claimNumber}</mark> został odrzucony przez <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Wyszukaj kody diagnostyczne ICD 10',
	ClaimDiagnosisSelectorHelpContent: `„Diagnoza lub uraz” oznacza oznakę, objaw, skargę lub stan pacjenta odnoszący się do usługi(-ek) objętej(-ych) roszczeniem.
 Można wybrać maksymalnie 12 kodów diagnostycznych ICD 10.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Pole 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnoza lub uraz',
	ClaimDiagnosticCodesEmptyError: 'Wymagany jest co najmniej jeden kod diagnozy',
	ClaimDoIncludeReferrerInformation: 'Dołącz informacje o polecającym w CMS1500',
	ClaimERAReceivedAction: 'Elektroniczny przelew otrzymany od <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Otrzymano przelew elektroniczny	<mark>Płatność {paymentReference}</mark> na kwotę <b>{paymentAmount}</b> od <b>{payerNumber} {payerName}</b> została zarejestrowana',
	ClaimExportedAction: '<mark>Zgłoszenie {claimNumber}</mark> zostało wyeksportowane jako <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nazwa klienta lub osoby kontaktowej',
	ClaimFieldClientAddress: 'Adres klienta',
	ClaimFieldClientAddressDescription:
		'Wprowadź adres klienta. Pierwszy wiersz jest adresem ulicy. Nie używaj znaków interpunkcyjnych (przecinków lub kropek) ani żadnych symboli w adresie. Jeśli zgłaszasz adres zagraniczny, skontaktuj się z płatnikiem, aby uzyskać szczegółowe instrukcje dotyczące zgłaszania.',
	ClaimFieldClientAddressSubtitle: 'Pole 5',
	ClaimFieldClientDateOfBirth: 'Data urodzenia klienta',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Pole 3',
	ClaimFieldClientDateOfBirthDescription:
		'Wprowadź 8-cyfrową datę urodzenia klienta (MM/DD/RRRR). Data urodzenia klienta to informacja, która identyfikuje klienta i odróżnia osoby o podobnych nazwiskach.',
	ClaimFieldClientDescription:
		'Nazwisko klienta oznacza imię i nazwisko osoby, która poddała się zabiegowi lub otrzymała materiały.',
	ClaimFieldClientSexDescription:
		'„Płeć” jest informacją pozwalającą zidentyfikować klienta i odróżnić osoby o podobnych imionach.',
	ClaimFieldClientSubtitle: 'Pole 2',
	ClaimFiling: 'Zgłoszenie roszczenia',
	ClaimHistorySubtitle: 'Ubezpieczenie • Reklamacja {number}',
	ClaimIncidentAutoAccident: 'Wypadek samochodowy?',
	ClaimIncidentConditionRelatedTo: 'Czy stan klienta jest związany z',
	ClaimIncidentConditionRelatedToHelpContent:
		'Informacje te wskazują, czy choroba lub uraz klienta są związane z zatrudnieniem, wypadkiem samochodowym lub innym wypadkiem. Zatrudnienie (obecne lub poprzednie) wskazywałoby, że stan jest związany z pracą lub miejscem pracy klienta. Wypadek samochodowy wskazywałby, że stan jest wynikiem wypadku samochodowego. Inny wypadek wskazywałby, że stan jest wynikiem innego rodzaju wypadku.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Pola 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Czy stan klienta jest związany z',
	ClaimIncidentCurrentIllness: 'Obecna choroba, uraz lub ciąża',
	ClaimIncidentCurrentIllnessHelpContent:
		'Data obecnej choroby, urazu lub ciąży identyfikuje pierwszą datę wystąpienia choroby, rzeczywistą datę urazu lub LMP w przypadku ciąży.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Pole 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Daty bieżącej choroby, urazu lub ciąży (LMP)',
	ClaimIncidentDate: 'Data',
	ClaimIncidentDateFrom: 'Data od',
	ClaimIncidentDateTo: 'Data do',
	ClaimIncidentEmploymentRelated: 'Zatrudnienie',
	ClaimIncidentEmploymentRelatedDesc: '(Obecne lub poprzednie)',
	ClaimIncidentHospitalizationDatesLabel: 'Daty hospitalizacji związane z bieżącymi usługami',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Daty hospitalizacji związane z bieżącymi usługami odnoszą się do pobytu klienta i wskazują daty przyjęcia i wypisu związane z usługami ujętymi w roszczeniu.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Pole 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Daty hospitalizacji związane z bieżącymi usługami',
	ClaimIncidentInformation: 'Informacje o zdarzeniu',
	ClaimIncidentOtherAccident: 'Inny wypadek?',
	ClaimIncidentOtherAssociatedDate: 'Inna powiązana data',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Druga data określa dodatkowe informacje dotyczące stanu zdrowia klienta lub jego leczenia.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Pole 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Inna data',
	ClaimIncidentQualifier: 'Kwalifikator',
	ClaimIncidentQualifierPlaceholder: 'Wybierz kwalifikator',
	ClaimIncidentUnableToWorkDatesLabel: 'Klient nie był w stanie pracować na obecnym stanowisku',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Daty, w których klient nie mógł pracować w obecnym zawodzie, to okres, w którym klient jest lub nie był w stanie pracować.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Pole 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Daty, w których klient nie mógł pracować na obecnym stanowisku',
	ClaimIncludeReferrerInformation: 'Dołącz informacje o polecającym w CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Rodzaj ubezpieczenia zdrowotnego mający zastosowanie do tego roszczenia. Inne oznacza ubezpieczenie zdrowotne, w tym HMO, ubezpieczenie komercyjne, ubezpieczenie od wypadków samochodowych, odpowiedzialność cywilną lub odszkodowanie pracownicze.
 Informacje te kierują roszczenie do właściwego programu i mogą ustalać podstawową odpowiedzialność.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Pole 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Rodzaj pokrycia',
	ClaimInsuranceGroupIdHelpContent: `Wprowadź numer polisy lub grupy ubezpieczonego widniejący na karcie identyfikacyjnej ubezpieczenia zdrowotnego ubezpieczonego.

 „Polisa, grupa lub numer FECA ubezpieczonego” to alfanumeryczny identyfikator ubezpieczenia zdrowotnego, samochodowego lub innego. Numer FECA to 9-znakowy alfanumeryczny identyfikator przypisany pacjentowi zgłaszającemu stan związany z pracą.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Pole 11',
	ClaimInsuranceGroupIdHelpTitle: 'Numer polisy, grupy lub FECA ubezpieczonego',
	ClaimInsuranceMemberIdHelpContent: `Wprowadź numer identyfikacyjny ubezpieczonego, który znajduje się na jego dowodzie osobistym, płatnika, do którego składany jest wniosek.
 Jeśli pacjent ma nadany przez płatnika unikalny Numer Identyfikacji Członka, należy wpisać ten numer w tym polu.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Pole 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Numer identyfikacyjny członka ubezpieczonego',
	ClaimInsurancePayer: 'Płatnik ubezpieczenia',
	ClaimManualPaymentAction: '<mark>Płatność {paymentReference}</mark> za <b>{paymentAmount}</b> zarejestrowana',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Dodatkowe informacje dotyczące roszczenia',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Proszę zapoznać się z aktualnymi instrukcjami publicznego lub prywatnego płatnika dotyczącymi wykorzystania tego pola. Podaj odpowiedni kwalifikator, jeśli jest dostępny, dla wprowadzanych informacji. Nie wprowadzaj spacji, łącznika ani innych separatorów między kwalifikatorem a informacją.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Pole 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Dodatkowe informacje o roszczeniu',
	ClaimMiscClaimCodes: 'Kody roszczeń',
	ClaimMiscOriginalReferenceNumber: 'Oryginalny numer referencyjny',
	ClaimMiscPatientsAccountNumber: 'Numer konta pacjenta',
	ClaimMiscPatientsAccountNumberHelpContent: 'Numer konta pacjenta to identyfikator przypisany przez usługodawcę.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Pole 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Numer konta pacjenta',
	ClaimMiscPriorAuthorizationNumber: 'Numer autoryzacji wstępnej',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Numer autoryzacji wstępnej jest numerem nadanym przez płatnika, autoryzującym usługę/usługi.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Pole 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Numer autoryzacji wstępnej',
	ClaimMiscResubmissionCode: 'Kod ponownego przesłania',
	ClaimMiscResubmissionCodeHelpContent:
		'Ponowne przesłanie oznacza kod i oryginalny numer referencyjny nadany przez płatnika lub odbiorcę docelowego w celu wskazania wcześniej przesłanego roszczenia lub zdarzenia.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Pole 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Ponowne przesłanie i/lub oryginalny numer referencyjny',
	ClaimNumber: 'Numer zgłoszenia',
	ClaimNumberFormat: 'Roszczenie nr {number}',
	ClaimOrderingProvider: 'Dostawca zamówień',
	ClaimOtherId: 'Inne ID',
	ClaimOtherIdPlaceholder: 'Wybierz opcję',
	ClaimOtherIdQualifier: 'Inny kwalifikator ID',
	ClaimOtherIdQualifierPlaceholder: 'Wybierz kwalifikator ID',
	ClaimPlaceOfService: 'Miejsce świadczenia usług',
	ClaimPlaceOfServicePlaceholder: 'Dodaj POS',
	ClaimPolicyHolderRelationship: 'Relacja posiadacza polisy',
	ClaimPolicyInformation: 'Informacje o polityce',
	ClaimPolicyTelephone: 'Telefon (wraz z numerem kierunkowym)',
	ClaimReceivedAction: '<mark>Zgłoszenie {claimNumber}</mark> otrzymane przez <b>{name}</b>',
	ClaimReferringProvider: 'Dostawca polecający',
	ClaimReferringProviderEmpty: 'Brak dodanych lekarzy kierujących',
	ClaimReferringProviderHelpContent:
		'Wprowadzona nazwa to dostawca polecający, dostawca zamawiający lub dostawca nadzorujący, który skierował, zamówił lub nadzorował usługę(i) lub dostawę(y) w roszczeniu. Kwalifikator wskazuje rolę dostawcy, którego dotyczy raport.',
	ClaimReferringProviderHelpSubtitle: 'Pole 17',
	ClaimReferringProviderHelpTitle: 'Nazwa dostawcy odsyłającego lub źródła',
	ClaimReferringProviderQualifier: 'Kwalifikator',
	ClaimReferringProviderQualifierPlaceholder: 'Wybierz kwalifikator',
	ClaimRejectedAction: '<mark>Zgłoszenie {claimNumber}</mark> zostało odrzucone przez <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Numer identyfikacyjny',
	ClaimRenderingProviderOrTeamMember: 'Dostawca usług renderowania lub członek zespołu',
	ClaimRestoredAction: '<mark>Zgłoszenie {claimNumber}</mark> zostało przywrócone',
	ClaimServiceFacility: 'Obiekt usługowy',
	ClaimServiceFacilityLocationHelpContent:
		'Nazwa i adres placówki, w której świadczono usługi, identyfikują miejsce, w którym świadczono usługi.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a i 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Pole 32, 32a i 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Obiekt usługowy',
	ClaimServiceFacilityPlaceholder: 'Wybierz placówkę lub lokalizację serwisu',
	ClaimServiceLabChargesHelpContent: `Wypełnij to pole, jeśli ubiegasz się o zwrot kosztów zakupionych usług świadczonych przez inny podmiot niż dostawca usług rozliczeniowych.
 Każda zakupiona usługa musi zostać uwzględniona w oddzielnym roszczeniu, ponieważ na formularzu CMS1500 można wpisać tylko jedną opłatę.`,
	ClaimServiceLabChargesHelpSubtitle: 'Pole 20',
	ClaimServiceLabChargesHelpTitle: 'Opłaty za korzystanie z laboratorium zewnętrznego',
	ClaimServiceLineServiceHelpContent:
		'„Procedury, usługi lub dostawy” oznaczają usługi i procedury medyczne świadczone pacjentowi.',
	ClaimServiceLineServiceHelpSubtitle: 'Pole 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedury, usługi lub dostawy',
	ClaimServiceLinesEmptyError: 'Wymagana jest co najmniej jedna linia usług',
	ClaimServiceSupplementaryInfoHelpContent: `Dodaj dodatkowy opis narracyjny świadczonych usług, używając odpowiednich kwalifikatorów.
 Nie należy wprowadzać spacji, myślnika ani innego separatora między kwalifikatorem a informacją.

 Pełne instrukcje dotyczące dodawania informacji uzupełniających można znaleźć w instrukcji dotyczącej formularza roszczenia CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Pole 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Informacje uzupełniające',
	ClaimSettingsBillingMethodTitle: 'Metoda rozliczeń klienta',
	ClaimSettingsClientSignatureDescription:
		'Wyrażam zgodę na udostępnienie informacji medycznych i innych informacji niezbędnych do rozpatrzenia roszczeń ubezpieczeniowych.',
	ClaimSettingsClientSignatureTitle: 'Podpis klienta w aktach',
	ClaimSettingsConsentLabel: 'Zgoda wymagana do rozpatrywania roszczeń ubezpieczeniowych:',
	ClaimSettingsDescription: 'Wybierz metodę rozliczeń klienta, aby zapewnić sobie sprawne przetwarzanie płatności:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} posiada aktywną polisę ubezpieczeniową. Aby włączyć rozliczenie ubezpieczeniowe, zaktualizuj metodę płatności klienta na Ubezpieczenie.',
	ClaimSettingsInsuranceDescription: 'Koszty zwracane przez ubezpieczenie',
	ClaimSettingsInsuranceTitle: 'Ubezpieczenie',
	ClaimSettingsNoPoliciesAlertDescription:
		'Dodaj polisę ubezpieczeniową, aby umożliwić składanie roszczeń ubezpieczeniowych.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Wyrażam zgodę na otrzymywanie płatności z ubezpieczenia za świadczone usługi.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Podpis posiadacza polisy w aktach',
	ClaimSettingsSelfPayDescription: 'Klient zapłaci za wizyty',
	ClaimSettingsSelfPayTitle: 'Samozapłać',
	ClaimSettingsTitle: 'Ustawienia roszczeń',
	ClaimSexSelectorPlaceholder: 'Mężczyzna / Kobieta',
	ClaimStatusChangedAction: '<mark>Zgłoszenie {claimNumber}</mark> zaktualizowano status',
	ClaimSubmittedAction:
		'<mark>Zgłoszenie {claimNumber}</mark> przesłane do <b>{payerClearingHouse}</b> dla <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Roszczenie #{claimNumber}',
	ClaimSupervisingProvider: 'Dostawca nadzorujący',
	ClaimSupplementaryInfo: 'Informacje uzupełniające',
	ClaimSupplementaryInfoPlaceholder: 'Dodaj informacje uzupełniające',
	ClaimTrashedAction: '<mark>Zgłoszenie {claimNumber}</mark> zostało usunięte',
	ClaimValidationFailure: 'Nie udało się zweryfikować roszczenia',
	ClaimsEmptyStateDescription: 'Nie znaleziono żadnych roszczeń.',
	ClainInsuranceTelephone: 'Telefon ubezpieczeniowy (wraz z numerem kierunkowym)',
	Classic: 'Klasyczny',
	Clear: 'Jasne',
	ClearAll: 'Wyczyść wszystko',
	ClearSearchFilter: 'Jasne',
	ClearingHouse: 'Sprzątanie domu',
	ClearingHouseClaimId: 'Id Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Numer referencyjny',
	ClearingHouseUnavailableError: 'Serwis rozliczeniowy jest obecnie niedostępny. Spróbuj ponownie później.',
	ClickToUpload: 'Kliknij, aby przesłać',
	Client: 'Klient',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} dodał {noteTitle, select, undefined { notatkę } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Wybierz klientów i ich relacje',
	ClientAndRelationshipSelectorTitle: 'Wszyscy klienci i ich relacje',
	ClientAndRelationshipSelectorTitle1: 'Wszystkie relacje ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Jeśli czekasz na rozmowę wideo, pojawi się ona wkrótce tutaj. Jeśli masz jakieś problemy, skontaktuj się z osobą, która ją zainicjowała.',
	ClientAppSubHeaderMyDocumentation: 'Moja dokumentacja',
	ClientAppointment: 'Spotkanie z klientem',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} zarezerwował(a) {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Nie znaleziono żadnych terminów',
	ClientAppointmentsEmptyStateTitle: 'Śledź nadchodzące i historyczne wizyty swoich klientów oraz ich obecność',
	ClientArchivedSuccessfulSnackbar: 'Pomyślnie zarchiwizowano <b>{name}</b>',
	ClientBalance: 'Saldo klienta',
	ClientBilling: 'Rozliczanie',
	ClientBillingAddPaymentMethodDescription:
		'Dodawaj i zarządzaj metodami płatności swojego klienta, aby usprawnić proces fakturowania i wystawiania rachunków.',
	ClientBillingAndPaymentDueDate: 'Termin wykonania',
	ClientBillingAndPaymentHistory: 'Historia rozliczeń i płatności',
	ClientBillingAndPaymentInvoices: 'Faktury',
	ClientBillingAndPaymentIssueDate: 'Data wydania',
	ClientBillingAndPaymentPrice: 'Cena',
	ClientBillingAndPaymentReceipt: 'Paragon',
	ClientBillingAndPaymentServices: 'Usługi',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar:
		'Zespół <span data-plural-form="one">członek</span><span data-plural-form="other">członków</span> przypisany!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Zespół {count, plural, one {członek} other {członków}} nieprzypisany!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tagi dodane!',
	ClientDuplicatesDeviewDescription:
		'Połącz wiele rekordów klientów w jeden, aby ujednolicić wszystkie dane — notatki, dokumenty, spotkania, faktury i rozmowy.',
	ClientDuplicatesPageMergeHeader: 'Wybierz dane, które chcesz zachować',
	ClientDuplicatesReviewHeader: 'Porównaj potencjalnie zduplikowane rekordy do scalenia',
	ClientEmailChangeWarningDescription:
		'Aktualizacja adresu e-mail klienta spowoduje usunięcie jego dostępu do wszelkiej udostępnionej dokumentacji i przyzna dostęp użytkownikowi z nowym adresem e-mail',
	ClientFieldDateDescription: 'Formatuj datę',
	ClientFieldDateLabel: 'Data',
	ClientFieldDateRangeDescription: 'Zakres dat',
	ClientFieldDateRangeLabel: 'Zakres dat',
	ClientFieldDateShowDateDescription: 'np. 29 lat',
	ClientFieldDateShowDateRangeDescription: 'np. 2 tygodnie',
	ClientFieldEmailDescription: 'Adres e-mail',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Etykieta pola',
	ClientFieldLinearScaleDescription: 'Opcje skali 1-10',
	ClientFieldLinearScaleLabel: 'Skala liniowa',
	ClientFieldLocationDescription: 'Adres fizyczny lub pocztowy',
	ClientFieldLocationLabel: 'Lokalizacja',
	ClientFieldLongTextDescription: 'Obszar długiego tekstu',
	ClientFieldLongTextLabel: 'Ustęp',
	ClientFieldMultipleChoiceDropdownDescription: 'Wybierz wiele opcji z listy',
	ClientFieldMultipleChoiceDropdownLabel: 'Lista rozwijana z wieloma opcjami wyboru',
	ClientFieldPhoneNumberDescription: 'Numer telefonu',
	ClientFieldPhoneNumberLabel: 'Telefon',
	ClientFieldPlaceholder: 'Wybierz typ pola klienta',
	ClientFieldSingleChoiceDropdownDescription: 'Wybierz tylko jedną opcję z listy',
	ClientFieldSingleChoiceDropdownLabel: 'Pojedyncza lista rozwijana wyboru',
	ClientFieldTextDescription: 'Pole wprowadzania tekstu',
	ClientFieldTextLabel: 'Tekst',
	ClientFieldYesOrNoDescription: 'Wybierz opcję tak lub nie',
	ClientFieldYesOrNoLabel: 'Tak | Nie',
	ClientFileFormAccessLevelDescription:
		'Ty i Zespół macie zawsze dostęp do plików, które przesyłacie. Możesz wybrać, czy chcesz udostępnić ten plik klientowi i/lub jego relacjom',
	ClientFileSavedSuccessSnackbar: 'Plik zapisany!',
	ClientFilesPageEmptyStateText: 'Nie przesłano żadnych plików',
	ClientFilesPageUploadFileButton: 'Prześlij pliki',
	ClientHeaderBilling: 'Rozliczanie',
	ClientHeaderBillingAndReceipts: 'Rozliczanie ',
	ClientHeaderDocumentation: 'Dokumentacja',
	ClientHeaderDocuments: 'Dokumenty',
	ClientHeaderFile: 'Dokument',
	ClientHeaderHistory: 'Historia medyczna',
	ClientHeaderInbox: 'Skrzynka odbiorcza',
	ClientHeaderNote: 'Notatka',
	ClientHeaderOverview: 'Przegląd',
	ClientHeaderProfile: 'Osobisty',
	ClientHeaderRelationship: 'Relacja',
	ClientHeaderRelationships: 'Relacje',
	ClientId: 'Identyfikator klienta',
	ClientImportProcessingDescription: 'Plik jest wciąż przetwarzany. Powiadomimy Cię, gdy to się zakończy.',
	ClientImportReadyForMappingDescription:
		'Skończyliśmy wstępne przetwarzanie Twojego pliku. Czy chcesz zmapować kolumny, aby zakończyć ten import?',
	ClientImportReadyForMappingNotificationSubject:
		'Import wstępnego przetwarzania klienta został zakończony. Plik jest teraz gotowy do mapowania.',
	ClientInAppMessaging: 'Wiadomości w aplikacji klienta',
	ClientInfoAddField: 'Dodaj kolejne pole',
	ClientInfoAddRow: 'Dodaj wiersz',
	ClientInfoAlertMessage: 'Wszelkie informacje wprowadzone w tej sekcji zostaną dodane do rekordu klienta.',
	ClientInfoFormPrimaryText: 'Informacje o kliencie',
	ClientInfoFormSecondaryText: 'Zbierz dane kontaktowe',
	ClientInfoPlaceholder: `Nazwa klienta, adres e-mail, numer telefonu
 Adres fizyczny,
 Data urodzenia`,
	ClientInformation: 'Informacje o kliencie',
	ClientInsuranceTabLabel: 'Ubezpieczenie',
	ClientIntakeFormsNotSupported: `Szablony formularzy nie są obecnie obsługiwane za pośrednictwem aplikacji klienckich.
 Zamiast tego twórz je i udostępniaj klientom jako notatki.`,
	ClientIntakeModalDescription:
		'E-mail z prośbą o uzupełnienie profilu, przesłanie odpowiednich dokumentów medycznych lub skierowań zostanie wysłany do Twojego klienta. Otrzyma on dostęp do Portalu Klienta.',
	ClientIntakeModalTitle: 'Wyślij zgłoszenie do {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Sukces! Twój pobór został zapisany.',
	ClientIntakeSuccessSnackbar: 'Sukces! Twój pobór został zapisany i wysłano e-mail z potwierdzeniem.',
	ClientIsChargedProcessingFee: 'Twoi klienci zapłacą opłatę za przetwarzanie',
	ClientListCreateButton: 'Nowy klient',
	ClientListEmptyState: 'Nie dodano żadnych klientów',
	ClientListPageItemArchive: 'Usuń klienta',
	ClientListPageItemRemoveAccess: 'Usuń mój dostęp',
	ClientLocalizationPanelDescription: 'Preferowany język i strefa czasowa klienta.',
	ClientLocalizationPanelTitle: 'Język i strefa czasowa',
	ClientManagementAndEHR: 'Zarządzanie klientami ',
	ClientMergeResultSummaryBanner:
		'Scalanie rekordów konsoliduje wszystkie dane klienta, w tym notatki, dokumenty, spotkania, faktury i rozmowy. Zweryfikuj dokładność przed kontynuowaniem.',
	ClientMergeResultSummaryTitle: 'Podsumowanie wyników scalania',
	ClientModalTitle: 'Nowy klient',
	ClientMustHaveEmaillAccessErrorText: 'Klienci/Kontakty bez adresów e-mail',
	ClientMustHavePortalAccessErrorText: 'Klienci/kontakty będą musieli się zarejestrować',
	ClientMustHaveZoomAppConnectedErrorText: 'Połącz Zoom poprzez Ustawienia > Połączone aplikacje',
	ClientNameFormat: 'Format nazwy klienta',
	ClientNotFormAccessLevel: 'Dostępne dla:',
	ClientNotFormAccessLevelDescription:
		'Ty i Zespół macie zawsze dostęp do notatek, które publikujecie. Możesz wybrać, czy chcesz udostępnić tę notatkę klientowi i/lub jego relacjom',
	ClientNotRegistered: 'Niezarejestrowany',
	ClientNoteFormAddFileButton: 'Dołącz pliki',
	ClientNoteFormChooseAClient: 'Wybierz klienta/kontakt, aby kontynuować',
	ClientNoteFormContent: 'Treść',
	ClientNoteItemDeleteConfirmationModalDescription: 'Po usunięciu notatki nie będzie można jej odzyskać.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Notatka opublikowana i zablokowana.',
	ClientNotePublishedSuccessSnackbar: 'Uwaga opublikowana!',
	ClientNotes: 'Notatki klienta',
	ClientNotesEmptyStateText: 'Aby dodać notatki, przejdź do profilu klienta i kliknij kartę Notatki.',
	ClientOnboardingChoosePasswordTitle1: 'Prawie gotowe!',
	ClientOnboardingChoosePasswordTitle2: 'Wybierz hasło',
	ClientOnboardingCompleteIntake: 'Całkowite spożycie',
	ClientOnboardingConfirmationScreenText:
		'Dostarczyłeś wszystkie informacje wymagane przez {providerName}. Potwierdź swój adres e-mail, aby rozpocząć onboarding. Jeśli nie otrzymasz go natychmiast, sprawdź folder ze spamem.',
	ClientOnboardingConfirmationScreenTitle: 'Świetnie! Sprawdź skrzynkę odbiorczą.',
	ClientOnboardingDashboardButton: 'Przejdź do pulpitu nawigacyjnego',
	ClientOnboardingHealthRecordsDesc1:
		'Czy chcesz udostępnić jakieś listy polecające, dokumenty firmie {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Dodaj opis (opcjonalnie)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentacja',
	ClientOnboardingPasswordRequirements: 'Wymagania',
	ClientOnboardingPasswordRequirementsConditions1: 'Wymagane jest minimum 9 znaków',
	ClientOnboardingProviderIntroSignupButton: 'Zarejestruj się dla siebie',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Zarejestruj się jako członek rodziny',
	ClientOnboardingProviderIntroTitle: '{name} zaprosił(a) Cię do dołączenia do platformy Carepatron',
	ClientOnboardingRegistrationInstructions: 'Wprowadź poniżej swoje dane osobowe.',
	ClientOnboardingRegistrationTitle: 'Najpierw będziemy potrzebować kilku danych osobowych',
	ClientOnboardingStepFormsAndAgreements: 'Formularze i umowy',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Proszę wypełnić poniższe formularze w celu procesu przyjęcia do {providerName}',
	ClientOnboardingStepHealthDetails: 'Szczegóły dotyczące zdrowia',
	ClientOnboardingStepPassword: 'Hasło',
	ClientOnboardingStepYourDetails: 'Twoje dane',
	ClientPaymentMethodDescription:
		'Zapisz metodę płatności w swoim profilu, aby umawianie wizyt i wystawianie faktur przebiegało szybciej i bezpieczniej.',
	ClientPortal: 'Portal Klienta',
	ClientPortalDashboardEmptyDescription: 'Tutaj będzie wyświetlana historia Twoich wizyt i obecności.',
	ClientPortalDashboardEmptyTitle: 'Śledź wszystkie nadchodzące, żądane i przeszłe spotkania, a także swoją obecność',
	ClientPreferredNotificationPanelDescription:
		'Zarządzaj preferowaną przez klienta metodą otrzymywania aktualizacji i powiadomień za pośrednictwem:',
	ClientPreferredNotificationPanelTitle: 'Preferowana metoda powiadamiania',
	ClientProcessingFee: 'Płatność obejmuje opłatę za przetwarzanie w wysokości ({currencyCode}) {amount}',
	ClientProfileAddress: 'Adres',
	ClientProfileDOB: 'Data urodzenia',
	ClientProfileEmailHelperText: 'Dodanie adresu e-mail umożliwiającego dostęp do portalu',
	ClientProfileEmailHelperTextMoreInfo:
		'Udzielenie klientowi dostępu do portalu umożliwia członkom zespołu udostępnianie notatek, plików i innej dokumentacji',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Numer identyfikacyjny',
	ClientRelationshipsAddClientOwnerButton: 'Zaproś klienta',
	ClientRelationshipsAddFamilyButton: 'Zaproś członka rodziny',
	ClientRelationshipsAddStaffButton: 'Dodaj dostęp dla personelu',
	ClientRelationshipsEmptyStateText: 'Nie dodano żadnych relacji',
	ClientRemovedSuccessSnackbar: 'Klient został pomyślnie usunięty.',
	ClientResponsibility: 'Odpowiedzialność klienta',
	ClientSavedSuccessSnackbar: 'Klient został pomyślnie zapisany.',
	ClientTableClientName: 'Nazwa klienta',
	ClientTablePhone: 'Telefon',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Pomyślnie przywrócono <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} edytował {noteTitle, select, undefined { notatkę } other {{noteTitle}}}',
	ClientView: 'Widok klienta',
	Clients: 'Klienci',
	ClientsTable: 'Tabela klientów',
	ClinicalFormat: 'Format kliniczny',
	ClinicalPsychologist: 'Psycholog kliniczny',
	Close: 'Zamknąć',
	CloseImportClientsModal: 'Czy na pewno chcesz anulować importowanie klientów?',
	CloseReactions: 'Reakcje bliskie',
	Closed: 'Zamknięte',
	Coaching: 'Trening',
	Code: 'Kod',
	CodeErrorMessage: 'Kod jest wymagany',
	CodePlaceholder: 'Kod',
	Coinsurance: 'Współubezpieczenie',
	Collection: 'Kolekcja',
	CollectionName: 'Nazwa kolekcji',
	Collections: 'Kolekcje',
	ColorAppointmentsBy: 'Spotkania kolorystyczne według',
	ColorTheme: 'Motyw kolorystyczny',
	ColourCalendarBy: 'Kalendarz kolorów według',
	ComingSoon: 'Już wkrótce',
	Community: 'Wspólnota',
	CommunityHealthLead: 'Lider zdrowia społeczności',
	CommunityHealthWorker: 'Pracownik służby zdrowia społecznego',
	CommunityTemplatesSectionDescription: 'Utworzone przez społeczność Carepatron',
	CommunityTemplatesSectionTitle: 'Wspólnota',
	CommunityUser: 'Użytkownik społeczności',
	Complete: 'Kompletny',
	CompleteAndLock: 'Zakończ i zablokuj',
	CompleteSetup: 'Pełna konfiguracja',
	CompleteSetupSuccessDescription: 'Zakończyłeś(aś) kilka kluczowych kroków w kierunku opanowania Carepatron.',
	CompleteSetupSuccessDescription2:
		'Odkryj więcej sposobów na usprawnienie swojej praktyki i wsparcie dla swoich klientów.',
	CompleteSetupSuccessTitle: 'Sukces! Świetnie Ci idzie!',
	CompleteStripeSetup: 'Pełna konfiguracja Stripe',
	Completed: 'Zakończone',
	ComposeSms: 'Napisz SMS-a',
	ComputerSystemsAnalyst: 'Analityk systemów komputerowych',
	Confirm: 'Potwierdzać',
	ConfirmDeleteAccountDescription:
		'Zamierzasz usunąć swoje konto. Tej czynności nie można cofnąć. Jeśli chcesz kontynuować, potwierdź poniżej.',
	ConfirmDeleteActionDescription: 'Czy na pewno chcesz usunąć tę akcję? Tego nie można cofnąć',
	ConfirmDeleteAutomationDescription: 'Czy na pewno chcesz usunąć tę automatyzację? Tej czynności nie można cofnąć.',
	ConfirmDeleteScheduleDescription:
		'Usuwanie harmonogramu <strong>{scheduleName}</strong> spowoduje jego usunięcie z Twoich harmonogramów i może zmienić dostępność Twojej usługi online. Tej czynności nie można cofnąć.',
	ConfirmDraftResponseContinue: 'Kontynuuj odpowiedź',
	ConfirmDraftResponseDescription:
		'Jeśli zamkniesz tę stronę, Twoja odpowiedź pozostanie jako wersja robocza. Możesz wrócić i kontynuować w dowolnym momencie.',
	ConfirmDraftResponseSubmitResponse: 'Prześlij odpowiedź',
	ConfirmDraftResponseTitle: 'Twoja odpowiedź nie została wysłana',
	ConfirmIfUserIsClientDescription: `Wypełniony przez Ciebie formularz rejestracyjny jest przeznaczony dla Dostawców (tj. zespołów/organizacji zajmujących się ochroną zdrowia).
 Jeśli to pomyłka, możesz wybrać opcję „Kontynuuj jako klient”, a my skonfigurujemy Twój portal klienta`,
	ConfirmIfUserIsClientNoButton: 'Zarejestruj się jako Dostawca',
	ConfirmIfUserIsClientTitle: 'Wygląda na to, że jesteś klientem',
	ConfirmIfUserIsClientYesButton: 'Kontynuuj jako klient',
	ConfirmKeepSeparate: 'Potwierdź, że należy zachować oddzielnie',
	ConfirmMerge: 'Potwierdź scalenie',
	ConfirmPassword: 'Potwierdź hasło',
	ConfirmRevertClaim: 'Tak, przywróć status',
	ConfirmSignupAccessCode: 'Kod potwierdzający',
	ConfirmSignupButtom: 'Potwierdzać',
	ConfirmSignupDescription:
		'Proszę wpisać swój adres e-mail i kod potwierdzający, który właśnie do Ciebie wysłaliśmy.',
	ConfirmSignupSubTitle: 'Sprawdź folder ze spamem – jeśli wiadomość e-mail nie dotarła',
	ConfirmSignupSuccessSnackbar:
		'Świetnie, potwierdziliśmy Twoje konto! Teraz możesz się zalogować używając swojego adresu e-mail i hasła',
	ConfirmSignupTitle: 'Potwierdź konto',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: 'Potwierdź subskrypcję {price} {isMonthly, select, true {miesięcznie} other {rocznie}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Po usunięciu klientów nie będziesz już mieć dostępu do ich danych.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Usuń {count, plural, one {# klienta} other {# klientów}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Po usunięciu kontaktów nie będziesz już mieć dostępu do ich informacji.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Usuń {count, plural, one {# kontakt} other {# kontakty}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Jest to działanie trwałe. Po usunięciu członków zespołu nie będziesz już mieć dostępu do ich informacji.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'Usuń {count, plural, one {# członka zespołu} other {# członków zespołu}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Zamknięcie tej notatki zakończy wszelkie trwające transkrypcje. Czy na pewno chcesz kontynuować?',
	ConfirmationModalDeleteClientField:
		'Jest to stała akcja. Po usunięciu pola nie będzie ono już dostępne dla pozostałych klientów.',
	ConfirmationModalDeleteSectionMessage:
		'Po usunięciu wszystkie pytania w tej sekcji zostaną usunięte. Tej czynności nie można cofnąć.',
	ConfirmationModalDeleteService:
		'Jest to stała akcja. Po usunięciu usługi nie będzie ona już dostępna w Twojej przestrzeni roboczej.',
	ConfirmationModalDeleteServiceGroup:
		'Usunięcie kolekcji spowoduje usunięcie wszystkich usług z grupy i powrót do listy usług. Tej czynności nie można cofnąć.',
	ConfirmationModalDeleteTranscript: 'Czy na pewno chcesz usunąć transkrypt?',
	ConfirmationModalDescriptionDeleteClient: 'Po usunięciu klienta nie będziesz już mieć dostępu do jego danych.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Po usunięciu dostępu nie będziesz już mógł przeglądać informacji o kliencie.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Ich profil nie zostanie usunięty, zostanie jedynie usunięty jako relacja tego klienta.',
	ConfirmationModalDescriptionRemoveStaff: 'Czy na pewno chcesz usunąć tę osobę z usługodawcy?',
	ConfirmationModalEndSession: 'Czy na pewno chcesz zakończyć sesję?',
	ConfirmationModalTitle: 'Jesteś pewien?',
	Confirmed: 'Potwierdzony',
	ConflictTimezoneWarningMessage: 'Mogą wystąpić konflikty ze względu na różne strefy czasowe',
	Connect: 'Łączyć',
	ConnectExistingClientOrContact: 'Utwórz nowego klienta/kontakt',
	ConnectInboxGoogleDescription: 'Dodaj konto Gmail lub listę grup Google',
	ConnectInboxMicrosoftDescription: 'Dodaj konto Outlook, Office365 lub Exchange',
	ConnectInboxModalDescription:
		'Połącz swoje aplikacje, aby bezproblemowo wysyłać, odbierać i śledzić wszystkie komunikaty w jednym scentralizowanym miejscu.',
	ConnectInboxModalExistingDescription:
		'Aby usprawnić proces konfiguracji, użyj istniejącego połączenia z ustawień połączonych aplikacji.',
	ConnectInboxModalExistingTitle: 'Istniejąca połączona aplikacja w Carepatron',
	ConnectInboxModalTitle: 'Połącz skrzynkę odbiorczą',
	ConnectToStripe: 'Połącz się ze Stripe',
	ConnectZoom: 'Połącz Zoom',
	ConnectZoomModalDescription: 'Pozwól Carepatronowi zarządzać połączeniami wideo podczas Twoich wizyt.',
	ConnectedAppDisconnectedNotificationSubject:
		'Utraciliśmy połączenie z kontem {account}. Prosimy o ponowne połączenie.',
	ConnectedAppSyncDescription:
		'Zarządzaj połączonymi aplikacjami, aby tworzyć wydarzenia w kalendarzach innych firm bezpośrednio z Carepatron.',
	ConnectedApps: 'Połączone aplikacje',
	ConnectedAppsGMailDescription: 'Dodaj konta Gmail lub listę grup Google',
	ConnectedAppsGoogleCalendarDescription: 'Dodaj konta kalendarzy lub listę grup Google',
	ConnectedAppsGoogleDescription: 'Dodaj swoje konto Gmail i zsynchronizuj kalendarze Google',
	ConnectedAppsMicrosoftDescription: 'Dodaj konto Outlook, Office365 lub Exchange',
	ConnectedCalendars: 'Połączone kalendarze',
	ConsentDocumentation: 'Formularze i umowy',
	ConsentDocumentationPublicTemplateError:
		'Ze względów bezpieczeństwa możesz wybierać szablony tylko od swojego zespołu (niepubliczne).',
	ConstructionWorker: 'Pracownik budowlany',
	Consultant: 'Konsultant',
	Contact: 'Kontakt',
	ContactAccessTypeHelperText: 'Umożliwia administratorom rodziny aktualizowanie informacji',
	ContactAccessTypeHelperTextMoreInfo: 'To pozwoli Ci udostępniać notatki/dokumenty dotyczące {clientFirstName}',
	ContactAddressLabelBilling: 'Rozliczanie',
	ContactAddressLabelHome: 'Dom',
	ContactAddressLabelOthers: 'Inni',
	ContactAddressLabelWork: 'Praca',
	ContactChangeConfirmation:
		'Zmiana danych kontaktowych na fakturze spowoduje usunięcie wszystkich pozycji wiersza związanych z <mark>{contactName}</mark>',
	ContactDetails: 'Dane kontaktowe',
	ContactEmailLabelOthers: 'Inni',
	ContactEmailLabelPersonal: 'Osobisty',
	ContactEmailLabelSchool: 'Szkoła',
	ContactEmailLabelWork: 'Praca',
	ContactInformation: 'Informacje kontaktowe',
	ContactInformationText: 'Informacje kontaktowe',
	ContactListCreateButton: 'Nowy kontakt',
	ContactName: 'Nazwa kontaktu',
	ContactPhoneLabelHome: 'Dom',
	ContactPhoneLabelMobile: 'Przenośny',
	ContactPhoneLabelSchool: 'Szkoła',
	ContactPhoneLabelWork: 'Praca',
	ContactRelationship: 'Relacja kontaktowa',
	ContactRelationshipFormAccessType: 'Udzielaj dostępu do udostępnianych informacji',
	ContactRelationshipGrantAccessInfo: 'Dzięki temu będziesz mógł udostępniać notatki ',
	ContactSupport: 'Skontaktuj się z pomocą techniczną',
	Contacts: 'Łączność',
	ContainerIdNotSet: 'Identyfikator kontenera nie został ustawiony',
	Contemporary: 'Współczesny',
	Continue: 'Kontynuować',
	ContinueDictating: 'Kontynuuj dyktowanie',
	ContinueEditing: 'Kontynuuj edycję',
	ContinueImport: 'Kontynuuj import',
	ContinueTranscription: 'Kontynuuj transkrypcję',
	ContinueWithApple: 'Kontynuuj z Apple',
	ContinueWithGoogle: 'Kontynuuj z Google',
	Conversation: 'Rozmowa',
	Copay: 'Współpłać',
	CopayOrCoinsurance: 'Współpłacenie lub współubezpieczenie',
	Copayment: 'Współpłacenie',
	CopiedToClipboard: 'Skopiowano do schowka',
	Copy: 'Kopia',
	CopyAddressSuccessSnackbar: 'Skopiowano adres do schowka',
	CopyCode: 'Skopiuj kod',
	CopyCodeToClipboardSuccess: 'Kod skopiowany do schowka',
	CopyEmailAddressSuccessSnackbar: 'Skopiowano adres e-mail do schowka',
	CopyLink: 'Kopiuj link',
	CopyLinkForCall: 'Skopiuj ten link, aby udostępnić to połączenie:',
	CopyLinkSuccessSnackbar: 'Skopiowano link do schowka',
	CopyMeetingLink: 'Kopiuj link do spotkania',
	CopyPaymentLink: 'Kopiuj link do płatności',
	CopyPhoneNumberSuccessSnackbar: 'Skopiowano numer telefonu do schowka',
	CopyTemplateLink: 'Kopiuj link do szablonu',
	CopyTemplateLinkSuccess: 'Skopiowano link do schowka',
	CopyToClipboardError: 'Nie można skopiować do schowka. Spróbuj ponownie.',
	CopyToTeamTemplates: 'Kopiuj do szablonów zespołu',
	CopyToWorkspace: 'Kopiuj do obszaru roboczego',
	Cosmetologist: 'Kosmetolog',
	Cost: 'Koszt',
	CostErrorMessage: 'Koszt jest wymagany',
	Counseling: 'Poradnictwo',
	Counselor: 'Doradca',
	Counselors: 'Doradcy',
	CountInvoicesAdded: '{count, plural, one {# Dodano fakturę} other {# Dodano faktury}}',
	CountNotesAdded: '{count, plural, one {# Uwaga dodana} other {# Uwagi dodane}}',
	CountSelected: '{count} wybranych',
	CountTimes: '{count} razy',
	Country: 'Kraj',
	Cousin: 'Kuzyn',
	CoverageType: 'Rodzaj pokrycia',
	Covered: 'Pokryty',
	Create: 'Tworzyć',
	CreateANewClient: 'Utwórz nowego klienta',
	CreateAccount: 'Utwórz konto',
	CreateAndSignNotes: 'Utwórz i podpisz notatkę z klientami',
	CreateAvailabilityScheduleFailure: 'Nie udało się utworzyć nowego harmonogramu dostępności',
	CreateAvailabilityScheduleSuccess: 'Pomyślnie utworzono nowy harmonogram dostępności',
	CreateBillingItems: 'Utwórz pozycje rozliczeniowe',
	CreateCallFormButton: 'Rozpocznij połączenie',
	CreateCallFormInviteOnly: 'Tylko na zaproszenie',
	CreateCallFormInviteOnlyMoreInfo:
		'Tylko osoby zaproszone do tego połączenia mogą dołączyć. Aby udostępnić to połączenie innym, po prostu odznacz to i skopiuj/wklej link na następnej stronie',
	CreateCallFormRecipients: 'Odbiorcy',
	CreateCallFormRegion: 'Region hostingu',
	CreateCallModalAddClientContactSelectorLabel: 'Kontakty z klientami',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Wyszukaj według nazwy klienta',
	CreateCallModalAddStaffSelectorLabel: 'Członkowie zespołu (opcjonalnie)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Szukaj według nazwiska pracownika',
	CreateCallModalDescription:
		'Rozpocznij połączenie i zaproś członków personelu i/lub kontakty. Możesz również odznaczyć pole „Prywatne”, aby udostępnić to połączenie każdemu z Carepatron',
	CreateCallModalTitle: 'Rozpocznij połączenie',
	CreateCallModalTitleLabel: 'Tytuł (opcjonalnie)',
	CreateCallNoPersonIdToolTip: 'Do połączeń mogą dołączać tylko kontakty/klienci z dostępem do portalu',
	CreateClaim: 'Utwórz roszczenie',
	CreateClaimCompletedMessage: 'Twoje roszczenie zostało utworzone.',
	CreateClientModalTitle: 'Nowy klient',
	CreateContactModalTitle: 'Nowy kontakt',
	CreateContactRelationshipButton: 'Dodaj relację',
	CreateContactSelectorDefaultOption: '  Utwórz kontakt',
	CreateContactWithRelationshipFormAccessType: 'Udziel dostępu do udostępnianych informacji ',
	CreateDocumentDnDPrompt: 'Przeciągnij i upuść, aby przesłać pliki',
	CreateDocumentSizeLimit: 'Limit rozmiaru pliku {size}MB. {total} plików łącznie.',
	CreateFreeAccount: 'Utwórz bezpłatne konto',
	CreateInvoice: 'Utwórz fakturę',
	CreateLink: 'Utwórz link',
	CreateNew: 'Utwórz nowy',
	CreateNewAppointment: 'Utwórz nowe spotkanie',
	CreateNewClaim: 'Utwórz nowe roszczenie',
	CreateNewClaimForAClient: 'Utwórz nowe roszczenie dla klienta.',
	CreateNewClient: 'Utwórz nowego klienta',
	CreateNewConnection: 'Nowe połączenie',
	CreateNewContact: 'Utwórz nowy kontakt',
	CreateNewField: 'Utwórz nowe pole',
	CreateNewLocation: 'Nowa lokalizacja',
	CreateNewService: 'Utwórz nową usługę',
	CreateNewServiceGroupFailure: 'Nie udało się utworzyć nowej kolekcji',
	CreateNewServiceGroupMenu: 'Nowa kolekcja',
	CreateNewServiceGroupSuccess: 'Pomyślnie utworzono nową kolekcję',
	CreateNewServiceMenu: 'Nowa usługa',
	CreateNewTeamMember: 'Utwórz nowego członka zespołu',
	CreateNewTemplate: 'Nowy szablon',
	CreateNote: 'Utwórz notatkę',
	CreateSuperbillReceipt: 'Nowy superrachunek',
	CreateSuperbillReceiptSuccess: 'Pomyślnie utworzono paragon Superbill',
	CreateTemplateFolderSuccessMessage: 'Pomyślnie utworzono {folderTitle}',
	Created: 'Stworzony',
	CreatedAt: 'Utworzony {timestamp}',
	Credit: 'Kredyt',
	CreditAdded: 'Zastosowano kredyt',
	CreditAdjustment: 'Korekta kredytowa',
	CreditAdjustmentReasonHelperText: 'Jest to notatka wewnętrzna i Twój klient nie będzie jej widział.',
	CreditAdjustmentReasonPlaceholder:
		'Dodanie powodu korekty może być pomocne podczas przeglądania transakcji podlegających rozliczeniu',
	CreditAmount: '{amount} KK',
	CreditBalance: 'Saldo kredytowe',
	CreditCard: 'Karta kredytowa',
	CreditCardExpire: 'Wygasa {exp_month}/{exp_year}',
	CreditCardNumber: 'Numer karty kredytowej',
	CreditDebitCard: 'Karta',
	CreditIssued: 'Wydany kredyt',
	CreditsUsed: 'Użyte kredyty',
	Crop: 'Przyciąć',
	Currency: 'Waluta',
	CurrentCredit: 'Aktualny kredyt',
	CurrentEventTime: 'Aktualny czas wydarzenia',
	CurrentPlan: 'Aktualny plan',
	Custom: 'Zwyczaj',
	CustomRange: 'Zakres niestandardowy',
	CustomRate: 'Stawka niestandardowa',
	CustomRecurrence: 'Niestandardowa częstotliwość',
	CustomServiceAvailability: 'Dostępność usługi',
	CustomerBalance: 'Saldo klienta',
	CustomerName: 'Nazwa klienta',
	CustomerNameIsRequired: 'Nazwa klienta jest wymagana',
	CustomerServiceRepresentative: 'Przedstawiciel obsługi klienta',
	CustomiseAppointments: 'Dostosuj spotkania',
	CustomiseBookingLink: 'Dostosuj opcje rezerwacji',
	CustomiseBookingLinkServicesInfo: 'Klienci mogą wybierać tylko usługi, które można zarezerwować',
	CustomiseBookingLinkServicesLabel: 'Usługi',
	CustomiseClientRecordsAndWorkspace: 'Dostosuj swoje rekordy klientów i przestrzeń roboczą',
	CustomiseClientSettings: 'Dostosuj ustawienia klienta',
	Customize: 'Dostosuj',
	CustomizeAppearance: 'Dostosuj wygląd',
	CustomizeAppearanceDesc:
		'Dostosuj wygląd swojego systemu rezerwacji online do swojej marki i zoptymalizuj sposób, w jaki Twoje usługi są prezentowane klientom.',
	CustomizeClientFields: 'Dostosuj pola klienta',
	CustomizeInvoiceTemplate: 'Dostosuj szablon faktury',
	CustomizeInvoiceTemplateDescription: 'Bez trudu twórz profesjonalne faktury odzwierciedlające Twoją markę.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX jest wymagane',
	Daily: 'Codziennie',
	DanceTherapist: 'Terapeuta Tańcem',
	DangerZone: 'Strefa niebezpieczna',
	Dashboard: 'Panel',
	Date: 'Data',
	DateAndTime: 'Data ',
	DateDue: 'Termin płatności',
	DateErrorMessage: 'Data jest wymagana',
	DateFormPrimaryText: 'Data',
	DateFormSecondaryText: 'Wybierz z selektora dat',
	DateIssued: 'Data wydania',
	DateOfPayment: 'Data płatności',
	DateOfService: 'Data usługi',
	DateOverride: 'Nadpisanie daty',
	DateOverrideColor: 'Kolor zastępowania daty',
	DateOverrideInfo:
		'Funkcja nadpisywania dat umożliwia specjalistom ręczne dostosowywanie swojej dostępności na konkretne daty poprzez nadpisywanie regularnych harmonogramów.',
	DateOverrideInfoBanner:
		'W tych przedziałach czasowych można rezerwować tylko określone usługi objęte tą zmianą daty; nie dopuszcza się innych rezerwacji online.',
	DateOverrides: 'Nadpisywanie dat',
	DatePickerFormPrimaryText: 'Data',
	DatePickerFormSecondaryText: 'Wybierz datę',
	DateRange: 'Zakres dat',
	DateRangeFormPrimaryText: 'Zakres dat',
	DateRangeFormSecondaryText: 'Wybierz zakres dat',
	DateReceived: 'Data otrzymania',
	DateSpecificHours: 'Konkretne godziny daty',
	DateSpecificHoursDescription:
		'Dodaj daty, w których Twoja dostępność zmienia się w stosunku do zaplanowanych godzin lub gdy chcesz zaoferować usługę w określonym dniu.',
	DateUploaded: 'Wgrany {date, date, medium}',
	Dates: 'Daty',
	Daughter: 'Córka',
	Day: 'Dzień',
	DayPlural: '{count, plural, one {dzień} other {dni}}',
	Days: 'Dni',
	DaysPlural: '{age, plural, one {# dzień} other {# dni}}',
	DeFacto: 'De facto',
	Deactivated: 'Deaktywowany',
	Debit: 'Obciążyć',
	DecreaseIndent: 'Zmniejsz wcięcie',
	Deductibles: 'Odliczenia',
	Default: 'Domyślny',
	DefaultBillingProfile: 'Domyślny profil rozliczeniowy',
	DefaultDescription: 'Domyślny opis',
	DefaultEndOfLine: 'Brak więcej elementów',
	DefaultInPerson: 'Spotkania z klientami',
	DefaultInvoiceTitle: 'Tytuł domyślny',
	DefaultNotificationSubject: 'Otrzymałeś nowe powiadomienie dla {notificationType}',
	DefaultPaymentMethod: 'Domyślna metoda płatności',
	DefaultService: 'Usługa domyślna',
	DefaultValue: 'Domyślny',
	DefaultVideo: 'E-mail z wideorozmową z klientem',
	DefinedTemplateType: '{invoiceTemplate} szablon',
	Delete: 'Usuwać',
	DeleteAccountButton: 'Usuń konto',
	DeleteAccountDescription: 'Usuń swoje konto z platformy',
	DeleteAccountPanelInfoAlert:
		'Musisz usunąć swoje obszary robocze przed usunięciem swojego profilu. Aby kontynuować, przełącz się na obszar roboczy i wybierz Ustawienia > Ustawienia obszaru roboczego.',
	DeleteAccountTitle: 'Usuń konto',
	DeleteAppointment: 'Usuń spotkanie',
	DeleteAppointmentDescription: 'Czy na pewno chcesz usunąć to spotkanie? Możesz je przywrócić później.',
	DeleteAvailabilityScheduleFailure: 'Nie udało się usunąć harmonogramu dostępności',
	DeleteAvailabilityScheduleSuccess: 'Pomyślnie usunięto harmonogram dostępności',
	DeleteBillable: 'Usuń płatność',
	DeleteBillableConfirmationMessage: 'Czy na pewno chcesz usunąć ten rachunek? Tego działania nie można cofnąć.',
	DeleteBillingProfileConfirmationMessage: 'Spowoduje to trwałe usunięcie profilu rozliczeniowego.',
	DeleteCardConfirmation: 'Jest to stała czynność. Po usunięciu karty nie będziesz już mieć do niej dostępu.',
	DeleteCategory: 'Usuń kategorię (nie jest to trwałe, chyba że zmiany zostaną zapisane)',
	DeleteClientEventConfirmationDescription: 'Ta informacja zostanie trwale usunięta.',
	DeleteClients: 'Usuń klientów',
	DeleteCollection: 'Usuń kolekcję',
	DeleteColumn: 'Usuń kolumnę',
	DeleteConversationConfirmationDescription: 'Usuń tę konwersację na zawsze. Tej czynności nie można cofnąć.',
	DeleteConversationConfirmationTitle: 'Usuń konwersację na zawsze',
	DeleteExternalEventDescription: 'Czy na pewno chcesz usunąć to spotkanie?',
	DeleteFileConfirmationModalPrompt: 'Po usunięciu pliku nie będzie można go odzyskać.',
	DeleteFolder: 'Usuń folder',
	DeleteFolderConfirmationMessage:
		'Czy na pewno chcesz usunąć ten folder {name}? Wszystkie elementy w tym folderze zostaną również usunięte. Możesz go przywrócić później.',
	DeleteForever: 'Usuń na zawsze',
	DeleteInsurancePayerConfirmationMessage:
		'Usunięcie {payer} usunie go z Twojej listy płatników ubezpieczeń. Ta czynność jest trwała i nie może być cofnięta.',
	DeleteInsurancePayerFailure: 'Nie udało się usunąć płatnika ubezpieczenia',
	DeleteInsurancePolicyConfirmationMessage: 'Spowoduje to trwałe usunięcie polisy ubezpieczeniowej.',
	DeleteInvoiceConfirmationDescription:
		'Tej czynności nie można cofnąć. Faktura i wszystkie płatności z nią powiązane zostaną trwale usunięte.',
	DeleteLocationConfirmation:
		'Usunięcie lokalizacji jest działaniem trwałym. Po jej usunięciu nie będziesz już mieć do niej dostępu. Tego działania nie można cofnąć.',
	DeletePayer: 'Usuń płatnika',
	DeletePracticeWorkspace: 'Usuń obszar roboczy ćwiczeń',
	DeletePracticeWorkspaceDescription: 'Trwale usuń tę przestrzeń roboczą',
	DeletePracticeWorkspaceFailedSnackbar: 'Nie udało się usunąć obszaru roboczego',
	DeletePracticeWorkspaceModalCancelButton: 'Tak, anuluj moją subskrypcję',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Zanim usuniesz swoją przestrzeń roboczą, musisz najpierw anulować subskrypcję.',
	DeletePracticeWorkspaceModalConfirmButton: 'Tak, trwale usuń obszar roboczy',
	DeletePracticeWorkspaceModalDescription:
		'Przestrzeń robocza {name} zostanie trwale usunięta, a wszyscy członkowie zespołu stracą do niej dostęp. Pobierz wszelkie ważne dane lub wiadomości, których możesz potrzebować przed usunięciem. Tej czynności nie można cofnąć.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Decyzja ta została podjęta ze względu na:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Powód',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Proszę nam powiedzieć, dlaczego chcesz usunąć swoje konto.',
	DeletePracticeWorkspaceModalTitle: 'Jesteś pewien?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Dostęp wszystkich członków zespołu został usunięty',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} został(a) pomyślnie usunięty(a)',
	DeletePublicTemplateContent: 'Spowoduje to usunięcie tylko szablonu publicznego, a nie szablonu Twojego zespołu.',
	DeleteRecurringAppointmentModalTitle: 'Usuń powtarzające się spotkanie',
	DeleteRecurringEventModalTitle: 'Usuń powtarzające się spotkanie',
	DeleteRecurringReminderModalTitle: 'Usuń powtarzające się przypomnienie',
	DeleteRecurringTaskModalTitle: 'Usuń powtarzające się zadanie',
	DeleteReminderConfirmation:
		'Jest to stała akcja. Po usunięciu przypomnienia nie będziesz już mieć do niego dostępu. Będzie to dotyczyć tylko nowych spotkań',
	DeleteSection: 'Usuń sekcję',
	DeleteSectionInfo:
		'Usuwanie sekcji <strong>{section}</strong> spowoduje ukrycie wszystkich istniejących w niej pól. Tej czynności nie można cofnąć.',
	DeleteSectionWarning:
		'Pola podstawowe nie mogą być usunięte i zostaną przeniesione do istniejącej sekcji <strong>{section}</strong>.',
	DeleteServiceFailure: 'Nie udało się usunąć usługi',
	DeleteServiceSuccess: 'Usługa została pomyślnie usunięta',
	DeleteStaffScheduleOverrideDescription:
		'Usunięcie tego zastąpienia daty dla {value} spowoduje usunięcie go z harmonogramów i może zmienić dostępność Twojej usługi online. Tej akcji nie można cofnąć.',
	DeleteSuperbillConfirmationDescription:
		'Tej czynności nie można cofnąć. Spowoduje to trwałe usunięcie paragonu Superbill.',
	DeleteSuperbillFailure: 'Nie udało się usunąć paragonu Superbill',
	DeleteSuperbillSuccess: 'Pomyślnie usunięto paragon Superbill',
	DeleteTaxRateConfirmationDescription: 'Czy na pewno chcesz usunąć tę stawkę podatku?',
	DeleteTemplateContent: 'Tej czynności nie można cofnąć',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} pomyślnie usunięty',
	DeleteTemplateSuccessMessage: '{templateTitle} pomyślnie usunięty',
	DeleteTemplateTitle: 'Czy na pewno chcesz usunąć ten szablon?',
	DeleteTranscript: 'Usuń transkrypt',
	DeleteWorkspace: 'Usuń obszar roboczy',
	Deleted: 'Usunięto',
	DeletedBy: 'Usunięte przez',
	DeletedContact: 'Usunięty kontakt',
	DeletedOn: 'Usunięto dnia',
	DeletedStatusLabel: 'Status usunięty',
	DeletedUserTooltip: 'Ten klient został usunięty',
	DeliveryMethod: 'Metoda dostawy',
	Demo: 'Demo',
	Denied: 'Zaprzeczony',
	Dental: 'Dentystyczny',
	DentalAssistant: 'Asystent stomatologiczny',
	DentalHygienist: 'Higienistka stomatologiczna',
	Dentist: 'Dentysta',
	Dentists: 'Dentyści',
	Description: 'Opis',
	DescriptionMustNotExceed: 'Opis nie może przekroczyć {max} znaków',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { z {staffName}}}',
	Details: 'Bliższe dane',
	Devices: 'Urządzenia',
	Diagnosis: 'Diagnoza',
	DiagnosisAndBillingItems: 'Diagnoza ',
	DiagnosisCode: 'Kod diagnostyczny',
	DiagnosisCodeErrorMessage: 'Wymagany jest kod diagnostyczny',
	DiagnosisCodeSelectorPlaceholder: 'Wyszukaj i dodaj z kodów diagnostycznych ICD-10',
	DiagnosisCodeSelectorTooltip:
		'Kody diagnostyczne służą do automatyzacji pokwitowań superrachunków w celu uzyskania zwrotu kosztów ubezpieczenia',
	DiagnosticCodes: 'Kody diagnostyczne',
	Dictate: 'Dyktować',
	DictatingIn: 'Dyktowanie w',
	Dictation: 'Dyktando',
	DidNotAttend: 'Nie uczestniczyłem',
	DidNotComplete: 'Nieukończone',
	DidNotProviderEnoughValue: 'Nie zapewnił wystarczającej wartości',
	DidntProvideEnoughValue: 'Nie zapewnił wystarczającej wartości',
	DieteticsOrNutrition: 'Dietetyka lub żywienie',
	Dietician: 'Dietetyk',
	Dieticians: 'Dietetycy',
	Dietitian: 'Dietetyk',
	DigitalSign: 'Podpisz tutaj:',
	DigitalSignHelp: '(Kliknij/naciśnij, aby rysować)',
	DirectDebit: 'Polecenie zapłaty',
	DirectTextLink: 'Bezpośredni link do tekstu',
	Disable: 'Wyłączyć',
	DisabledEmailInfo:
		'Nie możemy zaktualizować Twojego adresu e-mail, ponieważ Twoje konto nie jest przez nas zarządzane',
	Discard: 'Wyrzucać',
	DiscardChanges: 'Odrzuć zmiany',
	DiscardDrafts: 'Odrzuć wersje robocze',
	Disconnect: 'Odłączyć',
	DisconnectAppConfirmation: 'Czy chcesz rozłączyć tę aplikację?',
	DisconnectAppConfirmationDescription: 'Czy na pewno chcesz rozłączyć tę aplikację?',
	DisconnectAppConfirmationTitle: 'Rozłącz aplikację',
	Discount: 'Rabat',
	DisplayCalendar: 'Wyświetl w Carepatron',
	DisplayName: 'Nazwa wyświetlana',
	DisplayedToClients: 'Wyświetlane klientom',
	DiversionalTherapist: 'Terapeuta dywersyjny',
	DoItLater: 'Zrób to później',
	DoNotImport: 'Nie importuj',
	DoNotSend: 'Nie wysyłaj',
	DoThisLater: 'Zrób to później',
	DoYouWantToEndSession: 'Czy chcesz kontynuować, czy zakończyć sesję?',
	Doctor: 'Lekarz',
	Doctors: 'Lekarze',
	DoesNotRepeat: 'Nie powtarza się',
	DoesntWorkWellWithExistingTools: 'Nie współpracuje dobrze z naszymi obecnymi narzędziami lub przepływami pracy',
	DogWalker: 'Wyprowadzacz psów',
	Done: 'Gotowe',
	DontAllowClientsToCancel: 'Nie zezwalaj klientom na anulowanie',
	DontHaveAccount: 'Nie masz konta?',
	DontSend: 'Nie wysyłaj',
	Double: 'Podwójnie',
	DowngradeTo: 'Zmień na plan {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Przepraszamy, nie możesz obniżyć swojego planu, ponieważ masz zbyt wielu członków zespołu. Usuń niektórych z usługodawcy i spróbuj ponownie.',
	Download: 'Pobierać',
	DownloadAsPdf: 'Pobierz jako PDF',
	DownloadERA: 'Pobierz ERA',
	DownloadPDF: 'Pobierz PDF',
	DownloadTemplateFileName: 'Carepatron Szablon Przełączania.csv',
	DownloadTemplateTileDescription:
		'Skorzystaj z naszego szablonu arkusza kalkulacyjnego, aby zorganizować i przesłać swoich klientów.',
	DownloadTemplateTileLabel: 'Pobierz szablon',
	Downloads: '{number, plural, one {<span>#</span> Pobranie} other {<span>#</span> Pobrania}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Projekt',
	DraftResponses: 'Projekt odpowiedzi',
	DraftSaved: 'Zapisane zmiany',
	DragAndDrop: 'przeciągnij i upuść',
	DragDropText: 'Przeciągnij i upuść dokumenty dotyczące zdrowia',
	DragToMove: 'Przeciągnij, aby przesunąć',
	DragToMoveOrActivate: 'Przeciągnij, aby przesunąć lub aktywować',
	DramaTherapist: 'Terapeuta Dramatyczny',
	DropdownFormFieldPlaceHolder: 'Wybierz opcje z listy',
	DropdownFormPrimaryText: 'Lista rozwijana',
	DropdownFormSecondaryText: 'Wybierz z listy opcji',
	DropdownTextFieldError: 'Tekst opcji rozwijanej nie może być pusty',
	DropdownTextFieldPlaceholder: 'Dodaj opcję rozwijaną',
	Due: 'Termin',
	DueDate: 'Termin wykonania',
	Duplicate: 'Duplikat',
	DuplicateAvailabilityScheduleFailure: 'Nie udało się zduplikować harmonogramu dostępności',
	DuplicateAvailabilityScheduleSuccess: 'Pomyślnie zduplikowano harmonogram {name}',
	DuplicateClientBannerAction: 'Recenzja',
	DuplicateClientBannerDescription:
		'Scalenie zduplikowanych rekordów klientów powoduje ich konsolidację w jeden, przy zachowaniu wszystkich unikalnych informacji o kliencie.',
	DuplicateClientBannerTitle: 'Znaleziono {count} duplikatów',
	DuplicateColumn: 'Duplikuj kolumnę',
	DuplicateContactFieldSettingErrorSnackbar: 'Nie można mieć zduplikowanych nazw sekcji',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Nie można mieć zduplikowanych nazw pól',
	DuplicateEmailError: 'Duplikat wiadomości e-mail',
	DuplicateHeadingName: 'Sekcja {name} już istnieje',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Faktura o tym samym „numerze faktury” już istnieje.',
	DuplicateRecords: 'Duplikaty rekordów',
	DuplicateRecordsMinimumError: 'Należy wybrać co najmniej 2 rekordy',
	DuplicateRecordsRequired: 'Wybierz co najmniej 1 rekord do oddzielenia',
	DuplicateServiceFailure: 'Nie udało się zduplikować <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Pomyślnie zduplikowano **{title}**',
	DuplicateTemplateFolderSuccessMessage: 'Pomyślnie zduplikowano folder',
	DuplicateTemplateSuccess: 'Pomyślnie zduplikowano szablon',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'Kod DX',
	DxCodeSelectPlaceholder: 'Wyszukaj i dodaj z kodów ICD-10',
	EIN: 'Numer identyfikacyjny',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Nic',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>zawiera korekty</i>} other {}}',
	EarnReferralCredit: 'Zarabiaj ${creditAmount}',
	Economist: 'Ekonomista',
	Edit: 'Redagować',
	EditArrangements: 'Edytuj aranżacje',
	EditBillTo: 'Edytuj rachunek do',
	EditClient: 'Edytuj klienta',
	EditClientFileModalDescription: 'Edytuj dostęp do tego pliku, wybierając opcje w polach wyboru „Dostępne dla”',
	EditClientFileModalTitle: 'Edytuj plik',
	EditClientNoteModalDescription:
		'Edytuj zawartość notatki. Użyj sekcji „Widoczne dla”, aby zmienić, kto może zobaczyć notatkę.',
	EditClientNoteModalTitle: 'Edytuj notatkę',
	EditConnectedAppButton: 'Redagować',
	EditConnections: 'Edytuj połączenia{account, select, null { } undefined { } other { dla {account}}}',
	EditContactDetails: 'Edytuj dane kontaktowe',
	EditContactFormIsClientLabel: 'Konwertuj na klienta',
	EditContactIsClientCheckboxWarning: 'Konwersji kontaktu na klienta nie można cofnąć',
	EditContactIsClientWanringModal:
		'Konwersji tego kontaktu na Klienta nie można cofnąć. Jednak wszystkie relacje pozostaną, a Ty będziesz mieć teraz dostęp do ich notatek, plików i innej dokumentacji.',
	EditContactRelationship: 'Edytuj relację kontaktową',
	EditDetails: 'Edytuj szczegóły',
	EditFileModalTitle: 'Edytuj plik dla {name}',
	EditFolder: 'Edytuj folder',
	EditFolderDescription: 'Zmień nazwę folderu na...',
	EditInvoice: 'Edytuj fakturę',
	EditInvoiceDetails: 'Edytuj szczegóły faktury',
	EditLink: 'Edytuj link',
	EditLocation: 'Edytuj lokalizację',
	EditLocationFailure: 'Nie udało się zaktualizować lokalizacji',
	EditLocationSucess: 'Pomyślnie zaktualizowano lokalizację',
	EditPaymentDetails: 'Edytuj szczegóły płatności',
	EditPaymentMethod: 'Edytuj metodę płatności',
	EditPersonalDetails: 'Edytuj dane osobowe',
	EditPractitioner: 'Edytuj Praktyka',
	EditProvider: 'Edytuj dostawcę',
	EditProviderDetails: 'Edytuj szczegóły dostawcy',
	EditRecurrence: 'Edytuj powtarzanie',
	EditRecurringAppointmentModalTitle: 'Edytuj powtarzające się spotkanie',
	EditRecurringEventModalTitle: 'Edytuj powtarzające się spotkanie',
	EditRecurringReminderModalTitle: 'Edytuj powtarzające się przypomnienie',
	EditRecurringTaskModalTitle: 'Edytuj powtarzające się zadanie',
	EditRelationshipModalTitle: 'Edytuj relację',
	EditService: 'Edytuj usługę',
	EditServiceFailure: 'Nie udało się zaktualizować nowej usługi',
	EditServiceGroup: 'Edytuj kolekcję',
	EditServiceGroupFailure: 'Nie udało się zaktualizować kolekcji',
	EditServiceGroupSuccess: 'Pomyślnie zaktualizowano kolekcję',
	EditServiceSuccess: 'Pomyślnie zaktualizowano nową usługę',
	EditStaffDetails: 'Edytuj szczegóły personelu',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Nie można zaktualizować adresu e-mail. Utwórz nowego członka zespołu z nowym adresem e-mail.',
	EditSubscriptionBilledQuantity: 'Fakturowana ilość',
	EditSubscriptionBilledQuantityValue: '{billedUsers} członków zespołu',
	EditSubscriptionLimitedTimeOffer: 'Ograniczona oferta czasowa! 50% zniżki przez 6 miesięcy.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Koszt subskrypcji zostanie dostosowany podczas dodawania lub usuwania członków zespołu.',
	EditSubscriptionUpgradeContent:
		'Twoje konto zostanie natychmiast zaktualizowane do nowego planu i okresu rozliczeniowego. Wszelkie zmiany cen zostaną automatycznie obciążone Twoją zapisaną metodą płatności lub zaksięgowane na Twoim koncie.',
	EditSubscriptionUpgradePlanTitle: 'Uaktualnij plan subskrypcji',
	EditSuperbillReceipt: 'Edytuj superrachunek',
	EditTags: 'Edytuj tagi',
	EditTemplate: 'Edytuj szablon',
	EditTemplateFolderSuccessMessage: 'Folder szablonów został pomyślnie zaktualizowany',
	EditValue: 'Edytuj {value}',
	Edited: 'Edytowano',
	Editor: 'Redaktor',
	EditorAlertDescription:
		'Wykryto nieobsługiwany format. Ponownie załaduj aplikację lub skontaktuj się z naszym zespołem wsparcia.',
	EditorAlertTitle: 'Mamy problem z wyświetleniem tej zawartości',
	EditorPlaceholder:
		'Zacznij pisać, wybierz szablon lub dodaj podstawowe bloki, aby zebrać odpowiedzi od swoich klientów.',
	EditorTemplatePlaceholder: 'Zacznij pisać lub dodaj komponenty, aby utworzyć szablon',
	EditorTemplateWithSlashCommandPlaceholder:
		'Zacznij pisać lub dodaj podstawowe bloki, aby rejestrować odpowiedzi klientów. Użyj poleceń ze skosem (/) w celu szybkich działań.',
	EditorWithSlashCommandPlaceholder:
		'Zacznij pisać, wybierz szablon lub dodaj podstawowe bloki, aby przechwycić odpowiedzi klientów. Użyj poleceń ukośnika ( / ) do szybkich działań.',
	EffectiveStartEndDate: 'Obowiązująca data rozpoczęcia i zakończenia',
	ElectricalEngineer: 'Inżynier elektryk',
	Electronic: 'Elektroniczny',
	ElectronicSignature: 'Podpis elektroniczny',
	ElementarySchoolTeacher: 'Nauczyciel szkoły podstawowej',
	Eligibility: 'Kwalifikowalność',
	Email: 'E-mail',
	EmailAlreadyExists: 'Adres e-mail już istnieje',
	EmailAndSms: 'E-mail ',
	EmailBody: 'Treść wiadomości e-mail',
	EmailContainsIgnoredDescription:
		'Poniższy e-mail zawiera adres e-mail nadawcy/ów, który jest obecnie ignorowany. Czy chcesz kontynuować?',
	EmailInviteToPortalBody: `Cześć {contactName},
Proszę, kliknij w poniższy link, aby zalogować się do bezpiecznego portalu klienta i łatwo zarządzać swoją opieką.

Z poważaniem,

{providerName}`,
	EmailInviteToPortalSubject: 'Witamy w {providerName}',
	EmailInvoice: 'Faktura e-mail',
	EmailInvoiceOverdueBody: `Cześć {contactName}
Twoja faktura {invoiceNumber} jest przeterminowana.
Proszę uiścić należność za fakturę online, korzystając z poniższego linku.

Jeśli masz jakieś pytania, daj nam znać.

Z poważaniem,
{providerName}`,
	EmailInvoicePaidBody: `Cześć {contactName}
Twoja faktura {invoiceNumber} została opłacona.
Aby wyświetlić i pobrać kopię faktury, kliknij w poniższy link.

Jeśli masz jakieś pytania, daj nam znać.

Dzięki,
{providerName}`,
	EmailInvoiceProcessingBody: `Cześć {contactName}
Twoja faktura {invoiceNumber} jest gotowa.
Kliknij w poniższy link, aby wyświetlić fakturę.

Jeśli masz jakieś pytania, daj nam znać.

Dzięki,
{providerName}`,
	EmailInvoiceUnpaidBody: `Cześć {contactName}
Twoja faktura {invoiceNumber} jest gotowa do zapłaty do {dueDate}.
Aby wyświetlić i opłacić fakturę online, skorzystaj z poniższego linku.

W razie pytań, skontaktuj się z nami.

Z poważaniem,
{providerName}`,
	EmailInvoiceVoidBody: `Cześć {contactName}
Twoja faktura {invoiceNumber} została anulowana.
Aby wyświetlić fakturę, kliknij poniższy link.

W przypadku jakichkolwiek pytań prosimy o kontakt.

Z poważaniem,
{providerName}`,
	EmailNotFound: 'Nie znaleziono adresu e-mail',
	EmailNotVerifiedErrorCodeSnackbar: 'Nie można wykonać akcji. Musisz zweryfikować swój adres e-mail.',
	EmailNotVerifiedTitle: 'Twój adres e-mail nie jest zweryfikowany. Niektóre funkcje będą ograniczone.',
	EmailSendClientIntakeBody: `Cześć {contactName},
{providerName} chciałby/chciałaby, abyś/aś podał/podała kilka informacji i zapoznał/zapoznała się z ważnymi dokumentami. Aby rozpocząć, przejdź do poniższego linku.

Z poważaniem,

{providerName}`,
	EmailSendClientIntakeSubject: 'Witamy w {providerName}',
	EmailSuperbillReceipt: 'Wyślij superrachunek e-mailem',
	EmailSuperbillReceiptBody: `Cześć {contactName},
{providerName} wysłał Ci kopię potwierdzenia zwrotu kosztów {date}.

Możesz pobrać i przesłać je bezpośrednio do swojego ubezpieczyciela.`,
	EmailSuperbillReceiptFailure: 'Nie udało się wysłać rachunku Superbill',
	EmailSuperbillReceiptSubject: '{providerName} wysłał(a) oświadczenie o otrzymaniu zwrotu kosztów',
	EmailSuperbillReceiptSuccess: 'Pomyślnie wysłano potwierdzenie Superbill',
	EmailVerificationDescription: '<span>Weryfikujemy</span> teraz Twoje konto',
	EmailVerificationNotification: 'Wysłano wiadomość e-mail weryfikacyjną na adres {email}',
	EmailVerificationSuccess: 'Twój adres e-mail został pomyślnie zmieniony na {email}',
	Emails: 'E-maile',
	EmergencyContact: 'Kontakt alarmowy',
	EmployeesIdentificationNumber: 'Numer identyfikacyjny pracownika',
	EmploymentStatus: 'Status zatrudnienia',
	EmptyAgendaViewDescription: 'Brak wydarzeń do wyświetlenia.<mark> Utwórz spotkanie teraz</mark>',
	EmptyBin: 'Opróżnij kosz',
	EmptyBinConfirmationDescription:
		'Pusty kosz usunie wszystkie **{total} rozmowy** w folderze Usunięte. Tego działania nie można cofnąć.',
	EmptyBinConfirmationTitle: 'Usuń konwersacje na zawsze',
	EmptyTrash: 'Opróżnij kosz',
	Enable: 'Włączać',
	EnableCustomServiceAvailability: 'Włącz dostępność usługi',
	EnableCustomServiceAvailabilityDescription:
		'Np. pierwsze wizyty można rezerwować tylko codziennie w godzinach 9-10 rano',
	EndCall: 'Zakończ połączenie',
	EndCallConfirmationForCreator: 'Zakończysz to dla wszystkich, ponieważ jesteś inicjatorem połączenia.',
	EndCallConfirmationHasActiveAttendees:
		'Zamierzasz zakończyć połączenie, ale klient(zy) już dołączyli. Czy chcesz również dołączyć?',
	EndCallForAll: 'Zakończenie rozmowy dla wszystkich',
	EndDate: 'Data zakończenia',
	EndDictation: 'Zakończ dyktando',
	EndOfLine: 'Nie ma więcej spotkań',
	EndSession: 'Zakończ sesję',
	EndTranscription: 'Zakończ transkrypcję',
	Ends: 'Koniec',
	EndsOnDate: 'Kończy się {date}',
	Enrol: 'Zapisz się',
	EnrollmentRejectedSubject: 'Twoja rejestracja w {payerName} została odrzucona',
	Enrolment: 'Wlot',
	Enrolments: 'Zapisy',
	EnrolmentsDescription: 'Zobacz i zarządzaj rejestracjami dostawców u płatnika.',
	EnterAName: 'Wprowadź nazwę...',
	EnterFieldLabel: 'Wprowadź etykietę pola...',
	EnterPaymentDetailsDescription:
		'Koszt subskrypcji zostanie automatycznie dostosowany podczas dodawania i usuwania użytkowników.',
	EnterSectionName: 'Wprowadź nazwę sekcji...',
	EnterSubscriptionPaymentDetails: 'Wprowadź dane płatności',
	EnvironmentalScientist: 'Naukowiec zajmujący się ochroną środowiska',
	Epidemiologist: 'Epidemiolog',
	Eraser: 'Gumka do ścierania',
	Error: 'Błąd',
	ErrorBoundaryAction: 'Odśwież stronę',
	ErrorBoundaryDescription: 'Odśwież stronę i spróbuj ponownie.',
	ErrorBoundaryTitle: 'Ups! Coś poszło nie tak',
	ErrorCallNotFound: 'Nie można znaleźć połączenia. Mogło wygasnąć lub twórca je zakończył.',
	ErrorCannotAccessCallUninvitedCode: 'Przepraszamy, wygląda na to, że nie zostałeś zaproszony na tę rozmowę.',
	ErrorFileUploadCustomMaxFileCount: 'Nie można przesłać więcej niż {count} plików jednocześnie',
	ErrorFileUploadCustomMaxFileSize: 'Rozmiar pliku nie może przekroczyć {mb} MB',
	ErrorFileUploadInvalidFileType:
		'Nieprawidłowy typ pliku, który może zawierać potencjalne wirusy i szkodliwe oprogramowanie',
	ErrorFileUploadMaxFileCount: 'Nie można przesłać więcej niż 150 plików na raz',
	ErrorFileUploadMaxFileSize: 'Rozmiar pliku nie może przekraczać 100 MB',
	ErrorFileUploadNoFileSelected: 'Proszę wybrać pliki do przesłania',
	ErrorInvalidNationalProviderId: 'Podany identyfikator krajowego dostawcy jest nieprawidłowy',
	ErrorInvalidPayerId: 'Podany identyfikator płatnika jest nieprawidłowy',
	ErrorInvalidTaxNumber: 'Podany numer NIP jest nieprawidłowy',
	ErrorInviteExistingProviderStaffCode: 'Ten użytkownik jest już w obszarze roboczym.',
	ErrorInviteStaffExistingUser:
		'Przepraszamy, wygląda na to, że dodany przez Ciebie użytkownik już istnieje w naszym systemie.',
	ErrorOnlySingleCallAllowed:
		'Możesz mieć tylko jedno połączenie na raz. Zakończ bieżące połączenie, aby rozpocząć nowe.',
	ErrorPayerNotFound: 'Płatnik nie został znaleziony',
	ErrorProfilePhotoMaxFileSize: 'Nie udało się przesłać! Osiągnięto limit rozmiaru pliku - 5MB',
	ErrorRegisteredExistingUser: 'Przepraszamy, wygląda na to, że jesteś już zarejestrowany.',
	ErrorUserSignInIncorrectCredentials: 'Nieprawidłowy adres e-mail lub hasło. Spróbuj ponownie.',
	ErrorUserSigninGeneric: 'Przepraszamy, coś poszło nie tak.',
	ErrorUserSigninUserNotConfirmed:
		'Przepraszamy, musisz potwierdzić swoje konto przed zalogowaniem. Sprawdź skrzynkę odbiorczą, aby uzyskać instrukcje.',
	Errors: 'Błędy',
	EssentialPlanInclusionFive: 'Importowanie szablonu',
	EssentialPlanInclusionFour: '5 GB pamięci masowej',
	EssentialPlanInclusionHeader: 'Wszystko za darmo  ',
	EssentialPlanInclusionOne: 'Automatyczne i niestandardowe przypomnienia',
	EssentialPlanInclusionSix: 'Wsparcie priorytetowe',
	EssentialPlanInclusionThree: 'Wideorozmowa',
	EssentialPlanInclusionTwo: 'Dwukierunkowa synchronizacja kalendarza',
	EssentialSubscriptionPlanSubtitle: 'Uprość swoją praktykę dzięki podstawowym rozwiązaniom',
	EssentialSubscriptionPlanTitle: 'Niezbędny',
	Esthetician: 'Estetyk',
	Estheticians: 'Kosmetyczki',
	EstimatedArrivalDate: 'Szac. przyjazd {numberOfDaysFromNow}',
	Ethnicity: 'Pochodzenie etniczne',
	Europe: 'Europa',
	EventColor: 'Spotkanie kolorów',
	EventName: 'Nazwa wydarzenia',
	EventType: 'Typ wydarzenia',
	Every: 'Każdy',
	Every2Weeks: 'Co 2 tygodnie',
	EveryoneInWorkspace: 'Wszyscy w miejscu pracy',
	ExercisePhysiologist: 'Fizjolog wysiłku fizycznego',
	Existing: 'Istniejący',
	ExistingClients: 'Istniejący klienci',
	ExistingFolders: 'Istniejące foldery',
	ExpiredPromotionCode: 'Kod promocyjny wygasł',
	ExpiredReferralDescription: 'Polecenie wygasło',
	ExpiredVerificationLink: 'Wygasł link weryfikacyjny',
	ExpiredVerificationLinkDescription: `Przepraszamy, ale link weryfikacyjny, na który kliknąłeś, wygasł. Może się tak zdarzyć, jeśli czekałeś dłużej niż 24 godziny na kliknięcie linku lub jeśli użyłeś już linku do weryfikacji swojego adresu e-mail.

 Poproś o nowy link weryfikacyjny, aby zweryfikować swój adres e-mail.`,
	ExpiryDateRequired: 'Data ważności jest wymagana',
	ExploreFeature: 'Co chciałbyś zbadać w pierwszej kolejności?',
	ExploreOptions: 'Wybierz jedną lub więcej opcji, aby je zbadać...',
	Export: 'Eksport',
	ExportAppointments: 'Eksportuj spotkania',
	ExportClaims: 'Eksportuj roszczenia',
	ExportClaimsFilename: 'Roszczenia {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Nie udało się pobrać pliku z powodu błędu.',
	ExportClientsDownloadFailureSnackbarTitle: 'Pobieranie nie powiodło się',
	ExportClientsFailureSnackbarDescription: 'Nie udało się wyeksportować pliku z powodu błędu.',
	ExportClientsFailureSnackbarTitle: 'Eksport nie powiódł się',
	ExportClientsModalDescription: `Ten proces eksportu danych może potrwać kilka minut w zależności od ilości eksportowanych danych. Otrzymasz powiadomienie e-mail z linkiem, gdy dane będą gotowe do pobrania.

 Czy chcesz kontynuować eksportowanie danych klienta?`,
	ExportClientsModalTitle: 'Eksportuj dane klienta',
	ExportCms1500: 'Eksportuj CMS1500',
	ExportContactFailedNotificationSubject: 'Eksport danych nie powiódł się',
	ExportFailed: 'Eksport nie powiódł się',
	ExportGuide: 'Przewodnik eksportowy',
	ExportInvoiceFileName: 'Transakcje {fromDate}-{toDate}.csv',
	ExportPayments: 'Eksport płatności',
	ExportPaymentsFilename: 'Płatności {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Twój dokument jest gotowy do pobrania.',
	ExportPrintWaitMessage: 'Przygotowuję dokument. Proszę czekać...',
	ExportTextOnly: 'Eksportuj tylko tekst',
	ExportTransactions: 'Transakcje eksportowe',
	Exporting: 'Eksportowanie',
	ExportingData: 'Eksportowanie danych',
	ExtendedFamilyMember: 'Dalszy członek rodziny',
	External: 'Zewnętrzny',
	ExternalEventInfoBanner:
		'To spotkanie pochodzi z zsynchronizowanego kalendarza i może zawierać brakujące elementy.',
	ExtraLarge: 'Bardzo duży',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Przegrany',
	FailedToJoinTheMeeting: 'Nie udało się dołączyć do spotkania.',
	FallbackPageDescription: `Wygląda na to, że ta strona nie istnieje. Możesz spróbować {refreshButton} odświeżyć tę stronę, aby uzyskać najnowsze zmiany.
W przeciwnym razie skontaktuj się z pomocą techniczną Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'odświeżać',
	FallbackPageTitle: 'Ups...',
	FamilyPlanningService: 'Usługa planowania rodziny',
	FashionDesigner: 'Projektant mody',
	FastTrackInvoicingAndBilling: 'Przyspiesz fakturowanie i wystawianie rachunków',
	Father: 'Ojciec',
	FatherInLaw: 'Teść',
	Favorite: 'Ulubione',
	FeatureBannerCalendarTile1ActionLabel: 'Rezerwacja online • 2 min',
	FeatureBannerCalendarTile1Description:
		'Wystarczy wysłać e-mail, SMS-a lub dodać dostępność na swojej stronie internetowej',
	FeatureBannerCalendarTile1Title: 'Umożliw swoim klientom dokonywanie rezerwacji online',
	FeatureBannerCalendarTile2ActionLabel: 'Zautomatyzuj przypomnienia • 2 min',
	FeatureBannerCalendarTile2Description: 'Zwiększ frekwencję klientów dzięki automatycznym przypomnieniom',
	FeatureBannerCalendarTile2Title: 'Zmniejsz liczbę nieobecności',
	FeatureBannerCalendarTile3Title: 'Harmonogram i przepływ pracy',
	FeatureBannerCalendarTitle: 'Ułatw planowanie',
	FeatureBannerCallsTile1ActionLabel: 'Rozpocznij rozmowę telemedyczną',
	FeatureBannerCallsTile1Description: 'Dostęp klienta tylko za pomocą linku. Bez loginów, haseł i kłopotów',
	FeatureBannerCallsTile1Title: 'Rozpocznij rozmowę wideo z dowolnego miejsca',
	FeatureBannerCallsTile2ActionLabel: 'Połącz aplikacje • 4 minuty',
	FeatureBannerCallsTile2Description:
		'Bezproblemowe łączenie się z innymi preferowanymi dostawcami usług telemedycznych',
	FeatureBannerCallsTile2Title: 'Połącz swoje aplikacje telemedyczne',
	FeatureBannerCallsTile3Title: 'Połączenia',
	FeatureBannerCallsTitle: 'Nawiąż kontakt z klientami — gdziekolwiek i kiedykolwiek',
	FeatureBannerClientsTile1ActionLabel: 'Importuj teraz • 2 minuty',
	FeatureBannerClientsTile1Description:
		'Rozpocznij szybko pracę z naszym zautomatyzowanym narzędziem do importowania klientów',
	FeatureBannerClientsTile1Title: 'Masz wielu klientów?',
	FeatureBannerClientsTile2ActionLabel: 'Dostosuj spożycie • 2 min',
	FeatureBannerClientsTile2Description: 'Usuń dokumenty dotyczące przyjęć i popraw jakość obsługi klienta',
	FeatureBannerClientsTile2Title: 'Zrezygnuj z papieru',
	FeatureBannerClientsTile3Title: 'Portal Klienta',
	FeatureBannerClientsTitle: 'Wszystko zaczyna się od Twoich klientów',
	FeatureBannerHeader: 'Przez Społeczność, dla Społeczności!',
	FeatureBannerInvoicesTile1ActionLabel: 'Zautomatyzuj płatności • 2 min',
	FeatureBannerInvoicesTile1Description: 'Unikaj niezręcznych rozmów dzięki automatycznym płatnościom',
	FeatureBannerInvoicesTile1Title: 'Otrzymaj zapłatę 2x szybciej',
	FeatureBannerInvoicesTile2ActionLabel: 'Śledź przepływy pieniężne • 2 minuty',
	FeatureBannerInvoicesTile2Description: 'Zmniejsz liczbę niezapłaconych faktur i kontroluj swoje dochody',
	FeatureBannerInvoicesTile2Title: 'Śledź swoje dochody bezboleśnie',
	FeatureBannerInvoicesTile3Title: 'Rozliczenia i płatności',
	FeatureBannerInvoicesTitle: 'Jedna rzecz mniej, o którą trzeba się martwić',
	FeatureBannerSubheader:
		'Szablony Carepatron stworzone przez nasz zespół i społeczność. Wypróbuj nowe zasoby lub podziel się swoimi!',
	FeatureBannerTeamTile1ActionLabel: 'Zaproś teraz',
	FeatureBannerTeamTile1Description: 'Zaproś członków zespołu do swojego konta i ułatw sobie współpracę',
	FeatureBannerTeamTile1Title: 'Zbierz swój zespół',
	FeatureBannerTeamTile2ActionLabel: 'Ustaw dostępność • 2 min',
	FeatureBannerTeamTile2Description: 'Zarządzaj dostępnością swoich zespołów, aby uniknąć podwójnych rezerwacji',
	FeatureBannerTeamTile2Title: 'Ustaw swoją dostępność',
	FeatureBannerTeamTile3ActionLabel: 'Ustaw uprawnienia • 2 min',
	FeatureBannerTeamTile3Description:
		'Kontroluj dostęp do poufnych danych i narzędzi zapewniających zgodność z przepisami',
	FeatureBannerTeamTile3Title: 'Dostosuj uprawnienia i dostęp',
	FeatureBannerTeamTitle: 'Niczego wielkiego nie osiąga się w pojedynkę',
	FeatureBannerTemplatesTile1ActionLabel: 'Przeglądaj bibliotekę • 2 min',
	FeatureBannerTemplatesTile1Description: 'Wybierz z niesamowitej biblioteki zasobów, które można dostosować ',
	FeatureBannerTemplatesTile1Title: 'Zmniejsz ilość pracy',
	FeatureBannerTemplatesTile2ActionLabel: 'Wyślij teraz • 2 minuty',
	FeatureBannerTemplatesTile2Description: 'Wyślij klientom piękne szablony do ukończenia',
	FeatureBannerTemplatesTile2Title: 'Uczyń dokumentację przyjemną',
	FeatureBannerTemplatesTile3Title: 'Szablony',
	FeatureBannerTemplatesTitle: 'Szablony na absolutnie wszystko',
	FeatureLimitBannerDescription:
		'Uaktualnij teraz, aby kontynuować tworzenie i zarządzanie {featureName} bez przerw i w pełni wykorzystać możliwości!',
	FeatureLimitBannerTitle: 'Jesteś {percentage}% w drodze do limitu {featureName}',
	FeatureRequiresUpgrade: 'Ta funkcja wymaga uaktualnienia',
	Fee: 'Opłata',
	Female: 'Kobieta',
	FieldLabelTooltip: '{isHidden, select, true {Pokaż} other {Ukryj}} etykieta pola',
	FieldName: 'Nazwa pola',
	FieldOptionsFirstPart: 'Pierwsze słowo',
	FieldOptionsMiddlePart: 'Środkowe słowa',
	FieldOptionsSecondPart: 'Ostatnie słowo',
	FieldOptionsWholeField: 'Całe pole',
	FieldType: 'Typ pola',
	Fields: 'Pola',
	File: 'Plik',
	FileDownloaded: '<strong>{fileName}</strong> pobrano',
	FileInvalidType: 'Plik nie jest obsługiwany.',
	FileNotFound: 'Nie znaleziono pliku',
	FileNotFoundDescription: 'Plik, którego szukasz jest niedostępny lub został usunięty',
	FileTags: 'Tagi plików',
	FileTagsHelper: 'Tagi zostaną zastosowane do wszystkich plików',
	FileTooLarge: 'Plik jest za duży.',
	FileTooSmall: 'Plik jest za mały.',
	FileUploadComplete: 'Kompletny',
	FileUploadFailed: 'Przegrany',
	FileUploadInProgress: 'Załadunek',
	FileUploadedNotificationSubject: '{actorProfileName} dodał plik',
	Files: 'Akta',
	FillOut: 'Nadąć',
	Filter: 'Filtr',
	FilterBy: 'Filtruj według',
	FilterByAmount: 'Filtruj według kwoty',
	FilterByClient: 'Filtruj według klienta',
	FilterByLocation: 'Filtruj według lokalizacji',
	FilterByService: 'Filtruj według usługi',
	FilterByStatus: 'Filtruj według statusu',
	FilterByTags: 'Filtruj według tagów',
	FilterByTeam: 'Filtruj według zespołu',
	Filters: 'Filtry',
	FiltersAppliedToView: 'Filtry zastosowane do widoku',
	FinalAppointment: 'Ostateczne spotkanie',
	FinalizeImport: 'Zakończ import',
	FinancialAnalyst: 'Analityk finansowy',
	Finish: 'Skończyć',
	Firefighter: 'Strażak',
	FirstName: 'Imię',
	FirstNameLastInitial: 'Imię, pierwsza litera nazwiska',
	FirstPerson: '1 osoba',
	FolderName: 'Nazwa folderu',
	Folders: 'Lornetka składana',
	FontFamily: 'Rodzina czcionek',
	ForClients: 'Dla klientów',
	ForClientsDetails: 'Otrzymuję opiekę lub usługi związane ze zdrowiem',
	ForPractitioners: 'Dla praktyków',
	ForPractitionersDetails: 'Zarządzaj i rozwijaj swoją praktykę',
	ForgotPasswordConfirmAccessCode: 'Kod potwierdzający',
	ForgotPasswordConfirmNewPassword: 'Nowe hasło',
	ForgotPasswordConfirmPageDescription:
		'Proszę wpisać swój adres e-mail, nowe hasło i kod potwierdzający, który właśnie do Ciebie wysłaliśmy.',
	ForgotPasswordConfirmPageTitle: 'Zresetuj hasło',
	ForgotPasswordPageButton: 'Wyślij link resetujący',
	ForgotPasswordPageDescription: 'Podaj swój adres e-mail, a my wyślemy Ci link umożliwiający zresetowanie hasła.',
	ForgotPasswordPageTitle: 'Zapomniane hasło',
	ForgotPasswordSuccessPageDescription: 'Sprawdź swoją skrzynkę odbiorczą, czy znajdziesz tam link do resetowania.',
	ForgotPasswordSuccessPageTitle: 'Link resetujący został wysłany!',
	Form: 'Formularz',
	FormAnswersSentToEmailNotification: 'Wysłaliśmy kopię Twoich odpowiedzi na adres',
	FormBlocks: 'Bloki formularzy',
	FormFieldAddOption: 'Dodaj opcję',
	FormFieldAddOtherOption: 'Dodaj „inne”',
	FormFieldOptionPlaceholder: 'Opcja {index}',
	FormStructures: 'Struktury formularzy',
	Format: 'Format',
	FormatLinkButtonColor: 'Kolor przycisku',
	Forms: 'Formularze',
	FormsAndAgreementsValidationMessage:
		'Aby kontynuować proces przyjęcia, należy wypełnić wszystkie formularze i umowy.',
	FormsCategoryDescription: 'Do gromadzenia i organizowania danych pacjentów',
	Frankfurt: 'Frankfurt',
	Free: 'Bezpłatny',
	FreePlanInclusionFive: 'Automatyczne fakturowanie ',
	FreePlanInclusionFour: 'Portal klienta',
	FreePlanInclusionHeader: 'Zacznij od',
	FreePlanInclusionOne: 'Nieograniczona liczba klientów',
	FreePlanInclusionSix: 'Wsparcie na żywo',
	FreePlanInclusionThree: '1 GB pamięci masowej',
	FreePlanInclusionTwo: 'Telezdrowie',
	FreeSubscriptionPlanSubtitle: 'Za darmo dla wszystkich',
	FreeSubscriptionPlanTitle: 'Bezpłatny',
	Friday: 'Piątek',
	From: 'Z',
	FullName: 'Pełne imię i nazwisko',
	FunctionalMedicineOrNaturopath: 'Medycyna funkcjonalna lub naturopata',
	FuturePaymentsAuthoriseProvider: 'Zezwól {provider} na wykorzystanie zapisanego sposobu płatności w przyszłości',
	FuturePaymentsSavePaymentMethod: 'Zapisz {paymentMethod} na potrzeby przyszłych płatności',
	GST: 'Podatek od towarów i usług (GST)',
	Gender: 'Płeć',
	GeneralAvailability: 'Dostępność ogólna',
	GeneralAvailabilityDescription:
		'Ustaw, kiedy jesteś regularnie dostępny. Klienci będą mogli rezerwować Twoje usługi tylko w dostępnych godzinach.',
	GeneralAvailabilityDescription2:
		'Utwórz harmonogramy na podstawie swojej dostępności i pożądanych ofert usług w określonych godzinach, aby określić możliwość rezerwacji online.',
	GeneralAvailabilityInfo: 'Dostępne godziny określą Twoją dostępność do rezerwacji online',
	GeneralAvailabilityInfo2:
		'Usługi oferujące wydarzenia grupowe powinny korzystać z nowego harmonogramu, aby skrócić godziny, w których klienci mogą dokonywać rezerwacji online.',
	GeneralHoursPlural: '{count} {count, plural, one {godzina} few {godziny} many {godzin}}',
	GeneralPractitioner: 'Internista',
	GeneralPractitioners: 'Lekarze rodzinni',
	GeneralServiceAvailabilityInfo:
		'Ten harmonogram będzie miał pierwszeństwo przed zachowaniem przypisanych członków zespołu',
	Generate: 'Spowodować',
	GenerateBillingItemsBannerContent:
		'Pozycje rozliczeniowe nie są tworzone automatycznie w przypadku powtarzających się wizyt.',
	GenerateItems: 'Generuj elementy',
	GenerateNote: 'Wygeneruj notatkę',
	GenerateNoteConfirmationModalDescription:
		'Co chcesz zrobić? Utworzyć nową wygenerowaną notatkę, dodać do istniejącej lub zastąpić jej zawartość?',
	GenerateNoteFor: 'Wygeneruj notatkę dla',
	GeneratingContent: 'Generowanie treści...',
	GeneratingNote: 'Generowanie notatki...',
	GeneratingTranscript: 'Generowanie transkryptu',
	GeneratingTranscriptDescription: 'Przetworzenie może potrwać kilka minut',
	GeneratingYourTranscript: 'Generowanie transkryptu',
	GenericErrorDescription: '{module} nie mógł zostać załadowany. Spróbuj ponownie później.',
	GenericErrorTitle: 'Wystąpił nieoczekiwany błąd',
	GenericFailureSnackbar: 'Przepraszamy, wydarzyło się coś nieoczekiwanego. Odśwież stronę i spróbuj ponownie.',
	GenericSavedSuccessSnackbar: 'Sukces! Zmiany zapisane',
	GeneticCounselor: 'Doradca genetyczny',
	Gerontologist: 'Gerontolog',
	Get50PercentOff: 'Odbierz 50% zniżki!',
	GetHelp: 'Uzyskaj pomoc',
	GetStarted: 'Zacznij',
	GettingStartedAppointmentTypes: 'Utwórz typy spotkań',
	GettingStartedAppointmentTypesDescription:
		'Usprawnij planowanie i rozliczanie, dostosowując usługi, ceny i kody rozliczeniowe',
	GettingStartedAppointmentTypesTitle: 'Harmonogram ',
	GettingStartedClients: 'Dodaj swoich klientów',
	GettingStartedClientsDescription:
		'Przygotuj się do współpracy z klientami w celu umówienia przyszłych spotkań, sporządzenia notatek i dokonania płatności',
	GettingStartedClientsTitle: 'Wszystko zaczyna się od klientów',
	GettingStartedCreateClient: 'Utwórz klienta',
	GettingStartedImportClients: 'Importuj klientów',
	GettingStartedInvoices: 'Fakturuj jak profesjonalista',
	GettingStartedInvoicesDescription: `Tworzenie profesjonalnych faktur jest proste.
 Dodaj swoje logo, lokalizację i warunki płatności`,
	GettingStartedInvoicesTitle: 'Pokaż się z jak najlepszej strony',
	GettingStartedMobileApp: 'Pobierz aplikację mobilną',
	GettingStartedMobileAppDescription:
		'Możesz pobrać Carepatron na urządzenie z systemem iOS, Android lub komputer stacjonarny, aby mieć do niego łatwy dostęp w podróży',
	GettingStartedMobileAppTitle: 'Pracuj z dowolnego miejsca',
	GettingStartedNavItem: 'Pierwsze kroki',
	GettingStartedPageTitle: 'Pierwsze kroki w Carepatron',
	GettingStartedPayments: 'Akceptuj płatności online',
	GettingStartedPaymentsDescription: `Umożliw swoim klientom płacenie online, aby otrzymywać płatności szybciej.
 Zobacz wszystkie swoje faktury i płatności w jednym miejscu`,
	GettingStartedPaymentsTitle: 'Ułatw sobie płatności',
	GettingStartedSaveBranding: 'Zapisz branding',
	GettingStartedSyncCalendars: 'Synchronizuj inne kalendarze',
	GettingStartedSyncCalendarsDescription:
		'Carepatron sprawdza Twój kalendarz pod kątem konfliktów, dzięki czemu wizyty są planowane tylko wtedy, gdy masz czas',
	GettingStartedSyncCalendarsTitle: 'Bądź zawsze na bieżąco',
	GettingStartedVideo: 'Obejrzyj film wprowadzający',
	GettingStartedVideoDescription:
		'Pierwsze kompleksowe przestrzenie robocze w opiece zdrowotnej dla małych zespołów i ich klientów',
	GettingStartedVideoTitle: 'Witamy w Carepatron',
	GetttingStartedGetMobileDownload: 'Pobierz aplikację',
	GetttingStartedGetMobileNoDownload: `Niekompatybilny z tą przeglądarką. Jeśli używasz iPhone'a lub iPada, otwórz tę stronę w Safari. W przeciwnym razie spróbuj otworzyć ją w Chrome.`,
	Glossary: 'Słownik',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail pozwala na wysłanie tylko 500 wiadomości z Twojego konta w ciągu dnia. Niektóre wiadomości mogą nie zostać wysłane. Czy chcesz kontynuować?',
	GoToAppointment: 'Umów się na wizytę',
	GoToApps: 'Przejdź do aplikacji',
	GoToAvailability: 'Przejdź do dostępności',
	GoToClientList: 'Przejdź do listy klientów',
	GoToClientRecord: 'Przejdź do rekordu klienta',
	GoToClientSettings: 'Przejdź teraz do ustawień klienta',
	GoToInvoiceTemplates: 'Przejdź do szablonów faktur',
	GoToNotificationSettings: 'Przejdź do ustawień powiadomień',
	GoToPaymentSettings: 'Przejdź do ustawień płatności',
	Google: 'Google',
	GoogleCalendar: 'Kalendarz Google',
	GoogleColor: 'Kolor kalendarza Google',
	GoogleMeet: 'Spotkanie Google',
	GoogleTagManagerContainerId: 'Identyfikator kontenera Menedżera tagów Google',
	GotIt: 'Rozumiem!',
	Goto: 'Idź do',
	Granddaughter: 'Wnuczka',
	Grandfather: 'Dziadek',
	Grandmother: 'Babcia',
	Grandparent: 'Dziadkowie',
	Grandson: 'Wnuk',
	GrantPortalAccess: 'Udziel dostępu do portalu',
	GraphicDesigner: 'Projektant graficzny',
	Grid: 'Siatka',
	GridView: 'Widok siatki',
	Group: 'Grupa',
	GroupBy: 'Grupuj według',
	GroupEvent: 'Wydarzenie grupowe',
	GroupEventHelper: 'Ustaw limity uczestników nabożeństwa',
	GroupFilterLabel: 'Wszyscy {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Identyfikator grupy',
	GroupInputFieldsFormPrimaryText: 'Pola wprowadzania grupowego',
	GroupInputFieldsFormSecondaryText: 'Wybierz lub dodaj pola niestandardowe',
	GuideTo: 'Przewodnik po {value}',
	GuideToImproveVideoQuality: 'Przewodnik po poprawie jakości wideo',
	GuideToManagingPayers: 'Zarządzanie płatnikami',
	GuideToSubscriptionsBilling: 'Przewodnik po rozliczeniach subskrypcji',
	GuideToTroubleshooting: 'Przewodnik po rozwiązywaniu problemów',
	Guidelines: 'Wytyczne',
	GuidelinesCategoryDescription: 'Do wspomagania podejmowania decyzji klinicznych',
	HST: 'HST',
	HairStylist: 'Stylista fryzur',
	HaveBeenWaiting: 'Długo czekałeś',
	HeHim: 'On/Jego',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Kalendarz',
	HeaderCalls: 'Połączenia',
	HeaderClientAppAccountSettings: 'Ustawienia konta',
	HeaderClientAppCalls: 'Połączenia',
	HeaderClientAppMyDocumentation: 'Dokumentacja',
	HeaderClientAppMyRelationships: 'Moje związki',
	HeaderClients: 'Klienci',
	HeaderHelp: 'Pomoc',
	HeaderMoreOptions: 'Więcej opcji',
	HeaderStaff: 'Personel',
	HealthCoach: 'Trener zdrowia',
	HealthCoaches: 'Trenerzy zdrowia',
	HealthEducator: 'Edukator ds. zdrowia',
	HealthInformationTechnician: 'Technik Informacji Zdrowotnej',
	HealthPolicyExpert: 'Ekspert ds. Polityki Zdrowotnej',
	HealthServicesAdministrator: 'Administrator Służby Zdrowia',
	HelpArticles: 'Artykuły pomocy',
	HiddenColumns: 'Ukryte kolumny',
	HiddenFields: 'Ukryte pola',
	HiddenSections: 'Ukryte sekcje',
	HiddenSectionsAndFields: 'Ukryte sekcje/pola',
	HideColumn: 'Ukryj kolumnę',
	HideColumnButton: 'Ukryj kolumnę {value} przycisk',
	HideDetails: 'Ukryj szczegóły',
	HideField: 'Ukryj pole',
	HideFullAddress: 'Ukrywać',
	HideMenu: 'Ukryj menu',
	HideMergeSummarySidebar: 'Ukryj podsumowanie scalenia',
	HideSection: 'Ukryj sekcję',
	HideYourView: 'Ukryj swój widok',
	Highlight: 'Podświetl kolor',
	Highlighter: 'Zakreślacz',
	History: 'Historia',
	HistoryItemFooter: '{actors, select, undefined {{date} o {time}} other {Przez {actors} • {date} o {time}}}',
	HistorySidePanelEmptyState: 'Nie znaleziono żadnych wpisów w historii',
	HistoryTitle: 'Dziennik aktywności',
	HolisticHealthPractitioner: 'Praktyk holistycznego zdrowia',
	HomeCaregiver: 'Opiekun domowy',
	HomeHealthAide: 'Pomoc domowa',
	HomelessShelter: 'Schronisko dla bezdomnych',
	HourAbbreviation: '{count} {count, plural, one {godzina} other {godzin}}',
	Hourly: 'Godzinowe',
	HoursPlural: '{age, plural, one {# godzina} other {# godzin}}',
	HowCanWeImprove: 'Jak możemy to poprawić?',
	HowCanWeImproveResponse: 'Jak możemy ulepszyć tę odpowiedź?',
	HowDidWeDo: 'Jak sobie poradziliśmy?',
	HowDoesReferralWork: 'Przewodnik po programie poleceń',
	HowToUseAiSummarise: 'Jak korzystać z funkcji Podsumowanie AI',
	HumanResourcesManager: 'Menedżer ds. zasobów ludzkich',
	Husband: 'Mąż',
	Hypnotherapist: 'Hipnoterapeuta',
	IVA: 'VAT',
	IgnoreNotification: 'Ignoruj powiadomienie',
	IgnoreOnce: 'Zignoruj raz',
	IgnoreSender: 'Ignoruj nadawcę',
	IgnoreSenderDescription:
		'Przyszłe konwersacje od tego nadawcy zostaną automatycznie przeniesione do „Inne”. Czy na pewno chcesz zignorować tych nadawców?',
	IgnoreSenders: 'Ignoruj nadawców',
	IgnoreSendersSuccess: 'Zignorowany adres e-mail <mark>{addresses}</mark>',
	Ignored: 'Zignorowano',
	Image: 'Obraz',
	Import: 'Import',
	ImportActivity: 'Importuj aktywność',
	ImportClientSuccessSnackbarDescription: 'Twój plik został pomyślnie zaimportowany',
	ImportClientSuccessSnackbarTitle: 'Import zakończony sukcesem!',
	ImportClients: 'Importuj klientów',
	ImportClientsFailureSnackbarDescription: 'Nie udało się zaimportować pliku z powodu błędu.',
	ImportClientsFailureSnackbarTitle: 'Import nie powiódł się!',
	ImportClientsGuide: 'Przewodnik po importowania klientów',
	ImportClientsInProgressSnackbarDescription: 'Ukończenie tej operacji powinno zająć około minuty.',
	ImportClientsInProgressSnackbarTitle: 'Importowanie {fileName}',
	ImportClientsModalDescription:
		'Wybierz źródło swoich danych – czy będzie to plik na Twoim urządzeniu, usługa zewnętrzna czy inna platforma programowa.',
	ImportClientsModalFileUploadHelperText: 'Wspiera {fileTypes}. Limit rozmiaru {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Przewodnik po imporcie danych klienta',
	ImportClientsModalStep1Label: 'Wybierz źródło danych',
	ImportClientsModalStep2Label: 'Prześlij plik',
	ImportClientsModalStep3Label: 'Przejrzyj pola',
	ImportClientsModalTitle: 'Importowanie danych klienta',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {klient} other {klientów}} gotowych do importu',
	ImportContactFailedNotificationSubject: 'Importowanie danych nie powiodło się',
	ImportDataSourceSelectorLabel: 'Importuj źródło danych z',
	ImportDataSourceSelectorPlaceholder: 'Wyszukaj lub wybierz źródło importu danych',
	ImportExportButton: 'Import/Eksport',
	ImportFailed: 'Import nie powiódł się',
	ImportFromAnotherPlatformTileDescription: 'Pobierz eksport swoich plików klienckich i prześlij je tutaj.',
	ImportFromAnotherPlatformTileLabel: 'Import z innej platformy',
	ImportGuide: 'Przewodnik importu',
	ImportInProgress: 'Importowanie w toku',
	ImportProcessing: 'Importowanie przetwarzania...',
	ImportSpreadsheetDescription:
		'Możesz zaimportować istniejącą listę klientów do Carepatron, przesyłając plik arkusza kalkulacyjnego z danymi tabelarycznymi, np. w formacie .CSV, .XLS lub .XLSX',
	ImportSpreadsheetTitle: 'Zaimportuj plik arkusza kalkulacyjnego',
	ImportTemplates: 'Importuj szablony',
	Importing: 'Importowanie',
	ImportingCalendarProductEvents: 'Importowanie zdarzeń <product>',
	ImportingData: 'Importowanie danych',
	ImportingSpreadsheetDescription: 'Wykonanie tej czynności powinno zająć maksymalnie minutę',
	ImportingSpreadsheetTitle: 'Importowanie arkusza kalkulacyjnego',
	ImportsInProgress: 'Importy w toku',
	InPersonMeeting: 'Spotkanie osobiste',
	InProgress: 'W toku',
	InTransit: 'W tranzycie',
	InTransitTooltip:
		'Saldo w tranzycie obejmuje wszystkie opłacone faktury wypłacone przez Stripe na Twoje konto bankowe. Rozliczenie tych środków trwa zazwyczaj 3-5 dni.',
	Inactive: 'Nieaktywny',
	InboundOrOutboundCalls: 'Połączenia przychodzące i wychodzące',
	Inbox: 'Skrzynka odbiorcza',
	InboxAccessRestricted:
		'Dostęp ograniczony. Skontaktuj się z właścicielem skrzynki odbiorczej, aby uzyskać uprawnienia.',
	InboxAccountAlreadyConnected: 'Kanał, z którym próbowałeś się połączyć, jest już połączony z Carepatron',
	InboxAddAttachments: 'Dodaj załączniki',
	InboxAreYouSureDeleteMessage: 'Czy na pewno chcesz usunąć tę wiadomość?',
	InboxBulkCloseSuccess: '{count, plural, one {Pomyślnie zamknięto # rozmowę} other {Pomyślnie zamknięto # rozmów}}',
	InboxBulkComposeModalTitle: 'Napisz wiadomość masową',
	InboxBulkDeleteSuccess: '{count, plural, one {Usunięto # rozmowę} other {Usunięto # rozmów}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Poprawnie oznaczono # rozmowę jako przeczytaną} other {Poprawnie oznaczono # rozmów jako przeczytane}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Pomyślnie ponownie otwarto # rozmowę} other {Pomyślnie ponownie otwarto # rozmów}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Pomyślnie oznaczono # rozmowę jako nieprzeczytaną} other {Pomyślnie oznaczono # rozmów jako nieprzeczytane}}',
	InboxChatCreateGroup: 'Utwórz grupę',
	InboxChatDeleteGroupModalDescription:
		'Czy na pewno chcesz usunąć tę grupę? Wszystkie wiadomości i załączniki zostaną usunięte.',
	InboxChatDeleteGroupModalTitle: 'Usuń grupę',
	InboxChatDiscardDraft: 'Odrzuć szkic',
	InboxChatDragDropText: 'Upuść pliki tutaj, aby je przesłać',
	InboxChatGroupConversation: 'Rozmowa grupowa',
	InboxChatGroupCreateModalDescription:
		'Utwórz nową grupę, aby komunikować się i współpracować z zespołem, klientami lub społecznością.',
	InboxChatGroupCreateModalTitle: 'Utwórz grupę',
	InboxChatGroupMembers: 'Członkowie grupy',
	InboxChatGroupModalGroupNameFieldLabel: 'Nazwa grupy',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Np. obsługa klienta, administrator',
	InboxChatGroupModalGroupNameFieldRequired: 'To pole jest wymagane',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimalnie jeden członek wymagany',
	InboxChatGroupModalMembersFieldLabel: 'Wybierz członków grupy',
	InboxChatGroupModalMembersFieldPlaceholder: 'Wybierz członków',
	InboxChatGroupUpdateModalTitle: 'Zarządzaj grupą',
	InboxChatLeaveGroup: 'Opuść grupę',
	InboxChatLeaveGroupModalDescription:
		'Czy na pewno chcesz opuścić tę grupę? Nie będziesz już otrzymywać wiadomości ani aktualizacji.',
	InboxChatLeaveGroupModalTitle: 'Opuść grupę',
	InboxChatLeftGroupMessage: 'Lewa wiadomość grupowa',
	InboxChatManageGroup: 'Zarządzaj grupą',
	InboxChatSearchParticipants: 'Wybierz uczestników',
	InboxCloseConversationSuccess: 'Pomyślnie zamknięto konwersację',
	InboxCompose: 'Komponować',
	InboxComposeBulk: 'Wiadomość zbiorcza',
	InboxComposeCarepatronChat: 'Posłaniec',
	InboxComposeChat: 'Skomponuj czat',
	InboxComposeDisabledNoConnection: 'Połącz konto e-mail, aby wysyłać wiadomości',
	InboxComposeDisabledNoPermissionTooltip: 'Nie masz uprawnień do wysyłania wiadomości z tej skrzynki odbiorczej',
	InboxComposeEmail: 'Napisz e-mail',
	InboxComposeMessageFrom: 'Z',
	InboxComposeMessageRecipientBcc: 'UDW',
	InboxComposeMessageRecipientCc: 'DW',
	InboxComposeMessageRecipientTo: 'Do',
	InboxComposeMessageSubject: 'Temat:',
	InboxConnectAccountButton: 'Połącz swój e-mail',
	InboxConnectedDescription: 'W Twojej skrzynce odbiorczej nie ma żadnych komunikatów',
	InboxConnectedHeading: 'Twoje konwersacje będą się tutaj pojawiać, gdy tylko zaczniesz wymieniać się komunikatami',
	InboxConnectedHeadingClientView: 'Usprawnij komunikację z klientami',
	InboxCreateFirstInboxButton: 'Utwórz swoją pierwszą skrzynkę odbiorczą',
	InboxCreationSuccess: 'Skrzynka odbiorcza została pomyślnie utworzona',
	InboxDeleteAttachment: 'Usuń załącznik',
	InboxDeleteConversationSuccess: 'Pomyślnie usunięto konwersację',
	InboxDeleteMessage: 'Usunąć wiadomość?',
	InboxDirectMessage: 'Wiadomość prywatna',
	InboxEditDraft: 'Edytuj projekt',
	InboxEmailComposeReplyEmail: 'Napisz odpowiedź',
	InboxEmailDraft: 'Projekt',
	InboxEmailNotFound: 'Nie znaleziono adresu e-mail',
	InboxEmailSubjectFieldInformation:
		'Zmiana tematu wiadomości e-mail spowoduje utworzenie nowego wątku wiadomości e-mail.',
	InboxEmptyArchiveDescription: 'Nie znaleziono żadnej zarchiwizowanej konwersacji',
	InboxEmptyBinDescription: 'Nie znaleziono żadnej usuniętej konwersacji',
	InboxEmptyBinHeading: 'Wszystko jasne, nic tu nie ma do zobaczenia',
	InboxEmptyBinSuccess: 'Pomyślnie usunięto konwersacje',
	InboxEmptyCongratsHeading: 'Dobra robota! Usiądź wygodnie i zrelaksuj się do następnej rozmowy',
	InboxEmptyDraftDescription: 'Nie znaleziono żadnego projektu konwersacji',
	InboxEmptyDraftHeading: 'Wszystko jasne, nic tu nie ma do zobaczenia',
	InboxEmptyOtherDescription: 'Nie znaleziono żadnej innej konwersacji',
	InboxEmptyScheduledHeading: 'Wszystko jasne, nie zaplanowano żadnych rozmów do wysłania',
	InboxEmptySentDescription: 'Nie znaleziono żadnej wysłanej konwersacji',
	InboxForward: 'Do przodu',
	InboxGroupClientsLabel: 'Wszyscy klienci',
	InboxGroupClientsOverviewLabel: 'Klienci',
	InboxGroupClientsSelectedItemPrefix: 'Klient',
	InboxGroupStaffsLabel: 'Cała drużyna',
	InboxGroupStaffsOverviewLabel: 'Zespół',
	InboxGroupStaffsSelectedItemPrefix: 'Zespół',
	InboxGroupStatusLabel: 'Wszystkie statusy',
	InboxGroupStatusOverviewLabel: 'Wyślij do statusu',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Wszystkie tagi',
	InboxGroupTagsOverviewLabel: 'Wyślij do tagu',
	InboxGroupTagsSelectedItemPrefix: 'Etykietka',
	InboxHideQuotedText: 'Ukryj zacytowany tekst',
	InboxIgnoreConversationSuccess: 'Pomyślnie zignorowano konwersację',
	InboxMessageAllLabelRecipientsCount: 'Wszyscy odbiorcy {label} ({count})',
	InboxMessageBodyPlaceholder: 'Dodaj swoją wiadomość',
	InboxMessageDeleted: 'Wiadomość usunięta',
	InboxMessageMarkedAsRead: 'Wiadomość oznaczona jako przeczytana',
	InboxMessageMarkedAsUnread: 'Wiadomość oznaczona jako nieprzeczytana',
	InboxMessageSentViaChat: '**Wysłane przez czat**  • {time} przez {name}',
	InboxMessageShowMoreRecipients: '+{count} więcej',
	InboxMessageWasDeleted: 'Ta wiadomość została usunięta',
	InboxNoConnectionDescription: 'Połącz swoje konto e-mail lub utwórz skrzynki odbiorcze z wieloma adresami e-mail',
	InboxNoConnectionHeading: 'Zintegruj komunikację z klientem',
	InboxNoDirectMessage: 'Brak nowych wiadomości',
	InboxRecentConversations: 'Ostatnio',
	InboxReopenConversationSuccess: 'Pomyślnie otwarto ponownie rozmowę',
	InboxReply: 'Odpowiedź',
	InboxReplyAll: 'Odpowiedz wszystkim',
	InboxRestoreConversationSuccess: 'Pomyślnie przywrócono konwersację',
	InboxScheduleSendCancelSendSuccess:
		'Zaplanowana wysyłka została anulowana, a wiadomość przywrócona do wersji roboczej',
	InboxScheduleSendMessageSuccessDescription: 'Wysłano w planie na {date}',
	InboxScheduleSendMessageSuccessTitle: 'Planowanie wysyłania',
	InboxSearchForConversations: 'Szukaj "{query}"',
	InboxSendMessageSuccess: 'Pomyślnie wysłano konwersację',
	InboxSettings: 'Ustawienia skrzynki odbiorczej',
	InboxSettingsAppsDesc:
		'Zarządzaj połączonymi aplikacjami dla tej współdzielonej skrzynki odbiorczej: dodawaj lub usuwaj połączenia według potrzeb.',
	InboxSettingsAppsNewConnectedApp: 'Nowa połączona aplikacja',
	InboxSettingsAppsTitle: 'Połączone aplikacje',
	InboxSettingsDeleteAccountFailed: 'Nie udało się usunąć konta skrzynki odbiorczej',
	InboxSettingsDeleteAccountSuccess: 'Pomyślnie usunięto konto skrzynki odbiorczej',
	InboxSettingsDeleteAccountWarning:
		'Usunięcie {email} spowoduje rozłączenie go od skrzynki odbiorczej {inboxName} i zatrzyma synchronizację wiadomości.',
	InboxSettingsDeleteInboxFailed: 'Nie udało się usunąć skrzynki odbiorczej',
	InboxSettingsDeleteInboxSuccess: 'Pomyślnie usunięto skrzynkę odbiorczą',
	InboxSettingsDeleteInboxWarning:
		'Usuwanie {inboxName} spowoduje rozłączenie wszystkich połączonych kanałów i usunięcie wszystkich wiadomości skojarzonych z tą skrzynką odbiorczą. 		Ta akcja jest stała i nie można jej odwrócić.',
	InboxSettingsDetailsDesc:
		'Skrzynka komunikacyjna dla Twojego zespołu umożliwiająca efektywne zarządzanie wiadomościami od klientów.',
	InboxSettingsDetailsTitle: 'Szczegóły skrzynki odbiorczej',
	InboxSettingsEmailSignatureLabel: 'Domyślny podpis e-mail',
	InboxSettingsReplyFormatDesc:
		'Skonfiguruj domyślny adres zwrotny i podpis e-mail, aby były wyświetlane spójnie, niezależnie od tego, kto wysyła wiadomość.',
	InboxSettingsReplyFormatTitle: 'Format odpowiedzi',
	InboxSettingsSendFromLabel: 'Ustaw domyślną odpowiedź od ',
	InboxSettingsStaffDesc:
		'Zarządzaj dostępem członków zespołu do tej wspólnej skrzynki odbiorczej, aby zapewnić bezproblemową współpracę.',
	InboxSettingsStaffTitle: 'Przypisz członków zespołu',
	InboxSettingsUpdateInboxDetailsFailed: 'Nie udało się zaktualizować szczegółów skrzynki odbiorczej',
	InboxSettingsUpdateInboxDetailsSuccess: 'Pomyślnie zaktualizowano szczegóły skrzynki odbiorczej',
	InboxSettingsUpdateInboxStaffsFailed: 'Nie udało się zaktualizować członków zespołu skrzynki odbiorczej',
	InboxSettingsUpdateInboxStaffsSuccess: 'Pomyślnie zaktualizowano członków zespołu skrzynki odbiorczej',
	InboxSettingsUpdateReplyFormatFailed: 'Nie udało się zaktualizować formatu odpowiedzi',
	InboxSettingsUpdateReplyFormatSuccess: 'Pomyślnie zaktualizowano format odpowiedzi',
	InboxShowQuotedText: 'Pokaż cytowany tekst',
	InboxStaffRoleAdminDescription: 'Wyświetlaj, odpowiadaj i zarządzaj skrzynkami odbiorczymi',
	InboxStaffRoleResponderDescription: 'Wyświetl i odpowiedz',
	InboxStaffRoleViewerDescription: 'Tylko do przeglądania',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Kontynuuj edycję',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Tak, przełącz na wysyłkę zbiorczą',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Wybrałeś więcej niż {count} odbiorców. Czy chcesz wysłać to jako wiadomość masową?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Ostrzeżenie',
	InboxSwitchToOtherInbox: 'Przełącz się na inną skrzynkę odbiorczą',
	InboxUndoSendMessageSuccess: 'Wysyłanie niewykonane',
	IncludeLineItems: 'Uwzględnij pozycje zamówienia',
	IncludeSalesTax: 'Podlegający opodatkowaniu',
	IncludesAiSmartPrompt: 'Zawiera inteligentne podpowiedzi AI',
	Incomplete: 'Niekompletny',
	IncreaseIndent: 'Zwiększ wcięcie',
	IndianHealthServiceFreeStandingFacility: 'Placówka wolnostojąca Indian Health Service',
	IndianHealthServiceProviderFacility: 'Placówka świadcząca usługi w zakresie opieki zdrowotnej dla Indian',
	Information: 'Informacja',
	InitialAssessment: 'Ocena wstępna',
	InitialSignupPageClientFamilyTitle: 'Klient lub członek rodziny',
	InitialSignupPageProviderTitle: 'Zdrowie ',
	InitialTreatment: 'Leczenie początkowe',
	Initials: 'Inicjały',
	InlineEmbed: 'Wbudowany w tekst',
	InputPhraseToConfirm: 'Aby potwierdzić, wpisz {confirmationPhrase}.',
	Insert: 'Wstawić',
	InsertTable: 'Wstaw tabelę',
	InstallCarepatronOnYourIphone1: 'Zainstaluj Carepatron na swoim iOS: stuknij',
	InstallCarepatronOnYourIphone2: 'a następnie Dodaj do ekranu głównego',
	InsufficientCalendarScopesSnackbar:
		'Synchronizacja nie powiodła się – zezwól Carepatronowi na dostęp do kalendarza',
	InsufficientInboxScopesSnackbar:
		'Synchronizacja nie powiodła się — zezwól Carepatronowi na dostęp do poczty e-mail',
	InsufficientScopeErrorCodeSnackbar:
		'Synchronizacja nie powiodła się — proszę przyznać Carepatronowi wszystkie uprawnienia',
	Insurance: 'Ubezpieczenie',
	InsuranceAmount: 'Kwota ubezpieczenia',
	InsuranceClaim: 'Roszczenie ubezpieczeniowe',
	InsuranceClaimAiChatPlaceholder: 'Zapytaj o roszczenie ubezpieczeniowe...',
	InsuranceClaimAiClaimNumber: 'Roszczenie {number}',
	InsuranceClaimAiSubtitle: 'Fakturowanie ubezpieczeń • Walidacja roszczeń',
	InsuranceClaimDeniedSubject: 'Roszczenie {claimNumber} przesłane do {payerNumber} {payerName} zostało odrzucone',
	InsuranceClaimErrorDescription:
		'Rozwidnienie zawiera błędy zgłoszone przez płatnika lub izbę rozliczeniową.  Proszę przejrzeć poniższe komunikaty o błędach i ponownie przesłać rozwidnienie.',
	InsuranceClaimErrorGuideLink: 'Przewodnik po roszczeniach ubezpieczeniowych',
	InsuranceClaimErrorTitle: 'Błędy w zgłoszeniu roszczenia',
	InsuranceClaimNotFound: 'Roszczenie ubezpieczeniowe nie zostało znalezione',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Zarejestrowano częściową płatność w wysokości {paymentAmount}} other {Zarejestrowano płatność w wysokości {paymentAmount}}} dla roszczenia {claimNumber} przez {payerNumber} {payerName}.',
	InsuranceClaimRejectedSubject: 'Roszczenie {claimNumber} przesłane do {payerNumber} {payerName} zostało odrzucone',
	InsuranceClaims: 'Roszczenia ubezpieczeniowe',
	InsuranceInformation: 'Informacje o ubezpieczeniu',
	InsurancePaid: 'Ubezpieczenie opłacone',
	InsurancePayer: 'Płatnik ubezpieczenia',
	InsurancePayers: 'Płatnicy ubezpieczeń',
	InsurancePayersDescription: 'Zobacz płatników dodanych do Twojego konta i zarządzaj rejestracją.',
	InsurancePayment: 'Płatność za ubezpieczenie',
	InsurancePoliciesDetailsSubtitle: 'Dodaj informacje o ubezpieczeniu klienta, aby wesprzeć składanie roszczeń.',
	InsurancePoliciesDetailsTitle: 'Szczegóły zasad',
	InsurancePoliciesListSubtitle: 'Dodaj informacje o ubezpieczeniu klienta, aby wesprzeć składanie roszczeń.',
	InsurancePoliciesListTitle: 'Polisy ubezpieczeniowe',
	InsuranceSelfPay: 'Płatność własna',
	InsuranceType: 'Rodzaj ubezpieczenia',
	InsuranceUnpaid: 'Ubezpieczenie niezapłacone',
	Intake: 'Wlot',
	IntakeExpiredErrorCodeSnackbar:
		'Ten pobór wygasł. Skontaktuj się ze swoim dostawcą, aby ponownie wysłać kolejny pobór.',
	IntakeNotFoundErrorSnackbar:
		'Nie można znaleźć tego przyjęcia. Skontaktuj się ze swoim dostawcą, aby ponownie wysłać inne przyjęcie.',
	IntakeProcessLearnMoreInstructions: 'Przewodnik po konfiguracji formularzy przyjęć',
	IntakeTemplateSelectorPlaceholder: 'Wybierz formularze i umowy, które chcesz wysłać klientowi do wypełnienia',
	Integration: 'Integracja',
	IntenseBlur: 'Intensywnie rozmyj tło',
	InteriorDesigner: 'Projektant wnętrz',
	InternetBanking: 'Przelew bankowy',
	Interval: 'Interwał',
	IntervalDays: 'Interwał (dni)',
	IntervalHours: 'Interwał (godziny)',
	Invalid: 'Nieważny',
	InvalidDate: 'Nieprawidłowa data',
	InvalidDateFormat: 'Data musi być w formacie {format}',
	InvalidDisplayName: 'Nazwa wyświetlana nie może zawierać {value}',
	InvalidEmailFormat: 'Nieprawidłowy format adresu e-mail',
	InvalidFileType: 'Nieprawidłowy typ pliku',
	InvalidGTMContainerId: 'Nieprawidłowy format identyfikatora kontenera GTM',
	InvalidPaymentMethodCode: 'Wybrana metoda płatności jest nieprawidłowa. Wybierz inną.',
	InvalidPromotionCode: 'Kod promocyjny jest nieprawidłowy',
	InvalidReferralDescription: 'Już korzystam z Carepatron',
	InvalidStatementDescriptor: `Opis polecenia musi mieć długość od 5 do 22 znaków i zawierać wyłącznie litery, cyfry i spacje. Nie może zawierać znaków <, >, \\, ', ", *`,
	InvalidToken: 'Nieprawidłowy token',
	InvalidTotpSetupVerificationCode: 'Nieprawidłowy kod weryfikacyjny.',
	InvalidURLErrorText: 'Musi to być prawidłowy adres URL',
	InvalidZoomTokenErrorCodeSnackbar: 'Token Zoom wygasł. Podłącz ponownie aplikację Zoom i spróbuj ponownie.',
	Invite: 'Zapraszać',
	InviteRelationships: 'Zaproś relacje',
	InviteToPortal: 'Zaproś do portalu',
	InviteToPortalModalDescription:
		'Do Twojego klienta zostanie wysłany e-mail z zaproszeniem do rejestracji w Carepatron.',
	InviteToPortalModalTitle: 'Zaproszenie {name} do Portalu Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Zaproś nowego użytkownika',
	Invited: 'Zaproszony',
	Invoice: 'Faktura',
	InvoiceColorPickerDescription: 'Motyw kolorystyczny, który ma być użyty na fakturze',
	InvoiceColorTheme: 'Motyw kolorystyczny faktury',
	InvoiceContactDeleted: 'Kontakt do faktury został usunięty i faktura ta nie może zostać zaktualizowana.',
	InvoiceDate: 'Data wydania',
	InvoiceDetails: 'Szczegóły faktury',
	InvoiceFieldsPlaceholder: 'Wyszukaj pola...',
	InvoiceFrom: 'Faktura {number} od {fromProvider}',
	InvoiceInvalidCredit: 'Nieprawidłowa kwota kredytu, kwota kredytu nie może przekroczyć całkowitej kwoty faktury',
	InvoiceNotFoundDescription:
		'Skontaktuj się ze swoim dostawcą i poproś o dodatkowe informacje lub o ponowne wysłanie faktury.',
	InvoiceNotFoundTitle: 'Faktura nie została znaleziona',
	InvoiceNumber: 'Faktura #',
	InvoiceNumberFormat: 'Faktura nr {number}',
	InvoiceNumberMustEndWithDigit: 'Numer faktury musi kończyć się cyfrą (0-9)',
	InvoicePageHeader: 'Faktury',
	InvoicePaidNotificationSubject: 'Faktura {invoiceNumber} opłacona',
	InvoiceReminder: 'Przypomnienia o fakturach',
	InvoiceReminderSentence:
		'Wyślij przypomnienie o {deliveryType} {interval} {unit} {beforeAfter} terminu płatności faktury',
	InvoiceReminderSettings: 'Ustawienia przypomnienia o fakturze',
	InvoiceReminderSettingsInfo: 'Przypomnienia dotyczą wyłącznie faktur wysyłanych na Carepatron',
	InvoiceReminders: 'Przypomnienia o fakturach',
	InvoiceRemindersInfo:
		'Ustaw automatyczne przypomnienia o terminach płatności faktur. Przypomnienia dotyczą tylko faktur wysyłanych za pośrednictwem Carepatron',
	InvoiceSettings: 'Ustawienia faktury',
	InvoiceStatus: 'Status faktury',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, USA',
	InvoiceTemplateDescriptionPlaceholder:
		'Dodaj notatki, szczegóły przelewu bankowego lub warunki i postanowienia dotyczące alternatywnych metod płatności',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Samozatrudniony',
	InvoiceTemplateEthnicityPlaceholder: 'Kaukaski',
	InvoiceTemplateNotFoundDescription: 'Skontaktuj się ze swoim dostawcą i poproś go o więcej informacji.',
	InvoiceTemplateNotFoundTitle: 'Nie znaleziono szablonu faktury',
	InvoiceTemplates: 'Szablony faktur',
	InvoiceTemplatesDescription:
		'Dostosuj szablony faktur tak, aby odzwierciedlały Twoją markę, spełniały wymogi regulacyjne i odpowiadały preferencjom klientów, korzystając z naszych przyjaznych dla użytkownika szablonów.',
	InvoiceTheme: 'Motyw faktury',
	InvoiceTotal: 'Kwota faktury',
	InvoiceUninvoicedAmounts: 'Faktura niezafakturowanych kwot',
	InvoiceUpdateVersionMessage:
		'Edycja tej faktury wymaga najnowszej wersji. Proszę ponownie załadować Carepatron i spróbować ponownie.',
	Invoices: '{count, plural, one {Faktura} other {Faktury}}',
	InvoicesEmptyStateDescription: 'Nie znaleziono żadnych faktur',
	InvoicingAndPayment: 'Fakturowanie ',
	Ireland: 'Irlandia',
	IsA: 'jest',
	IsBetween: 'jest pomiędzy',
	IsEqualTo: 'jest równy',
	IsGreaterThan: 'jest większy niż',
	IsGreaterThanOrEqualTo: 'jest większe lub równe',
	IsLessThan: 'jest mniejsze niż',
	IsLessThanOrEqualTo: 'jest mniejsze lub równe',
	IssueCredit: 'Wydanie kredytu',
	IssueCreditAdjustment: 'Korekta kredytu emisyjnego',
	IssueDate: 'Data wydania',
	Italic: 'italski',
	Items: 'Rzeczy',
	ItemsAndAdjustments: 'Pozycje i korekty',
	ItemsRemaining: '+{count} pozostałych elementów',
	JobTitle: 'Stanowisko',
	Join: 'Dołączyć',
	JoinCall: 'Dołącz do rozmowy',
	JoinNow: 'Dołącz teraz',
	JoinProduct: 'Dołącz do {product}',
	JoinVideoCall: 'Dołącz do rozmowy wideo',
	JoinWebinar: 'Dołącz do webinaru',
	JoinWithVideoCall: 'Dołącz do {product}',
	Journalist: 'Dziennikarz',
	JustMe: 'Tylko ja',
	JustYou: 'Tylko ty',
	Justify: 'Uzasadniać',
	KeepSeparate: 'Trzymać oddzielnie',
	KeepSeparateSuccessMessage: 'Pomyślnie utworzono oddzielne rekordy dla {clientNames}',
	KeepWaiting: 'Czekaj dalej',
	Label: 'Etykieta',
	LabelOptional: 'Etykieta (opcjonalnie)',
	LactationConsulting: 'Poradnictwo laktacyjne',
	Language: 'Język',
	Large: 'Duży',
	LastDxCode: 'Ostatni kod DX',
	LastLoggedIn: 'Ostatnio zalogowano {date} o {time}',
	LastMenstrualPeriod: 'Ostatnia miesiączka',
	LastMonth: 'Ostatni miesiąc',
	LastNDays: 'Ostatnie {number} dni',
	LastName: 'Nazwisko',
	LastNameFirstInitial: 'Nazwisko, pierwsza litera imienia',
	LastWeek: 'W zeszłym tygodniu',
	LastXRay: 'Ostatnie zdjęcie rentgenowskie',
	LatestVisitOrConsultation: 'Ostatnia wizyta lub konsultacja',
	Lawyer: 'Prawnik',
	LearnMore: 'Dowiedz się więcej',
	LearnMoreTipsToGettingStarted: 'Dowiedz się więcej o tym, jak zacząć',
	LearnToSetupInbox: 'Przewodnik po konfiguracji konta skrzynki odbiorczej',
	Leave: 'Wyjechać',
	LeaveCall: 'Opuść połączenie',
	LeftAlign: 'Wyrównanie do lewej',
	LegacyBillingItemsNotAvailable:
		'Pozycje rozliczeniowe nie są jeszcze dostępne dla tej wizyty. Nadal możesz ją normalnie fakturować.',
	LegacyBillingItemsNotAvailableTitle: 'Fakturowanie dziedziczone',
	LegalAndConsent: 'Prawo i zgoda',
	LegalConsentFormPrimaryText: 'Zgoda prawna',
	LegalConsentFormSecondaryText: 'Akceptuj lub odrzucaj opcje',
	LegalGuardian: 'Opiekun prawny',
	Letter: 'List',
	LettersCategoryDescription: 'Do tworzenia korespondencji klinicznej i administracyjnej',
	Librarian: 'Bibliotekarz',
	LicenseNumber: 'Numer licencji',
	LifeCoach: 'Trener Życia',
	LifeCoaches: 'Trenerzy Życia',
	Limited: 'Ograniczony',
	LineSpacing: 'Odstępy między wierszami i akapitami',
	LinearScaleFormPrimaryText: 'Skala liniowa',
	LinearScaleFormSecondaryText: 'Opcje skali 1-10',
	Lineitems: 'Pozycje zamówienia',
	Link: 'Połączyć',
	LinkClientFormSearchClientLabel: 'Wyszukaj klienta',
	LinkClientModalTitle: 'Link do istniejącego klienta',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> dane kontaktowe zostały dodane do rekordu <strong>{existingName}’s</strong>.',
	LinkClientSuccessTitle: 'Pomyślnie połączono z istniejącym kontaktem',
	LinkForCallCopied: 'Link skopiowany!',
	LinkToAnExistingClient: 'Link do istniejącego klienta',
	LinkToClient: 'Link do klienta',
	ListAndTracker: 'Lista/Śledzenie',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} jest na tej rozmowie}
			other {{attendees} są na tej rozmowie}
		}`,
	ListStyles: 'Lista stylów',
	ListsAndTrackersCategoryDescription: 'Do organizowania i śledzenia pracy',
	LivingArrangements: 'Warunki mieszkaniowe',
	LoadMore: 'Załaduj więcej',
	Loading: 'Załadunek...',
	LocalizationPanelDescription: 'Zarządzaj ustawieniami języka i strefy czasowej',
	LocalizationPanelTitle: 'Język i strefa czasowa',
	Location: 'Lokalizacja',
	LocationDescription:
		'Skonfiguruj lokalizacje fizyczne i wirtualne, podając konkretne adresy, nazwy pomieszczeń i typy przestrzeni wirtualnych, aby ułatwić umawianie spotkań i rozmów wideo.',
	LocationNumber: 'Numer lokalizacji',
	LocationOfService: 'Miejsce świadczenia usług',
	LocationOfServiceRecommendedActionInfo:
		'Dodanie konkretnej lokalizacji do tej usługi może wpłynąć na Twoją dostępność.',
	LocationRemote: 'Zdalny',
	LocationType: 'Typ lokalizacji',
	Locations: 'Lokalizacje',
	Lock: 'Zamek',
	Locked: 'Zamknięty',
	LockedNote: 'Zablokowana notatka',
	LogInToSaveOrAuthoriseCard: 'Zaloguj się, aby zapisać lub autoryzować kartę',
	LogInToSaveOrAuthorisePayment: 'Zaloguj się, aby zapisać lub autoryzować płatność',
	Login: 'Zaloguj się',
	LoginButton: 'Zalogować się',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Zapomniałem hasła',
	LoginPassword: 'Hasło',
	Logo: 'Logo',
	LogoutAreYouSure: 'Wyloguj się z tego urządzenia.',
	LogoutButton: 'Wyloguj się',
	London: 'Londyn',
	LongTextAnswer: 'Długa odpowiedź tekstowa',
	LongTextFormPrimaryText: 'Długi tekst',
	LongTextFormSecondaryText: 'Opcje stylu akapitu',
	Male: 'Mężczyzna',
	Manage: 'Zarządzać',
	ManageAllClientTags: 'Zarządzaj wszystkimi tagami klientów',
	ManageAllNoteTags: 'Zarządzaj wszystkimi tagami notatek',
	ManageAllTemplateTags: 'Zarządzaj wszystkimi tagami szablonów',
	ManageConnections: 'Zarządzaj połączeniami',
	ManageConnectionsGmailDescription:
		'Pozostali członkowie zespołu nie będą mogli zobaczyć zsynchronizowanej poczty Gmail.',
	ManageConnectionsGoogleCalendarDescription:
		'Inni członkowie zespołu nie będą mogli zobaczyć Twoich zsynchronizowanych kalendarzy. Wizyty klientów można aktualizować lub usuwać tylko z Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Aby zarządzać ustawieniami synchronizacji skrzynki odbiorczej, przejdź do strony Skrzynka odbiorcza.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Inni członkowie zespołu nie będą mogli zobaczyć Twoich zsynchronizowanych kalendarzy. Wizyty klientów można aktualizować lub usuwać tylko z Carepatron.',
	ManageConnectionsOutlookDescription:
		'Pozostali członkowie zespołu nie będą mogli zobaczyć zsynchronizowanego programu Microsoft Outlook.',
	ManageInboxAccountButton: 'Nowa skrzynka odbiorcza',
	ManageInboxAccountEdit: 'Zarządzaj skrzynką odbiorczą',
	ManageInboxAccountPanelTitle: 'Skrzynki odbiorcze',
	ManageInboxAssignTeamPlaceholder: 'Wybierz członków zespołu, którzy będą mieli dostęp do skrzynki odbiorczej',
	ManageInboxBasicInfoColor: 'Kolor',
	ManageInboxBasicInfoDescription: 'Opis',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Do czego Ty lub Twój zespół będziecie używać tej skrzynki odbiorczej?',
	ManageInboxBasicInfoName: 'Nazwa skrzynki odbiorczej',
	ManageInboxBasicInfoNamePlaceholder: 'Np. obsługa klienta, administracja',
	ManageInboxConnectAppAlreadyConnectedError:
		'Kanał, z którym próbowałeś się połączyć, jest już połączony z Carepatron',
	ManageInboxConnectAppConnect: 'Łączyć',
	ManageInboxConnectAppConnectedInfo: 'Połączono z kontem',
	ManageInboxConnectAppContinue: 'Kontynuować',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Zaloguj się za pomocą',
	ManageInboxConnectAppSubtitle:
		'Połącz swoje aplikacje, aby bezproblemowo wysyłać, odbierać i śledzić wszystkie komunikaty w jednym scentralizowanym miejscu.',
	ManageInboxNewInboxTitle: 'Nowa skrzynka odbiorcza',
	ManagePlan: 'Zarządzaj planem',
	ManageProfile: 'Zarządzaj profilem',
	ManageReferralsModalDescription:
		'Pomóż nam rozpowszechnić informacje o naszej platformie opieki zdrowotnej i zdobywaj nagrody.',
	ManageReferralsModalTitle: 'Poleć znajomego i zdobądź nagrody!',
	ManageStaffRelationshipsAddButton: 'Zarządzaj relacjami',
	ManageStaffRelationshipsEmptyStateText: 'Nie dodano żadnych relacji',
	ManageStaffRelationshipsModalDescription:
		'Wybranie klientów spowoduje dodanie nowych relacji, natomiast odznaczenie klientów spowoduje usunięcie istniejących relacji.',
	ManageStaffRelationshipsModalTitle: 'Zarządzaj relacjami',
	ManageStatuses: 'Zarządzaj statusami',
	ManageStatusesActiveStatusHelperText: 'Wymagany jest co najmniej jeden aktywny status',
	ManageStatusesDescription: 'Dostosuj etykiety statusu i wybierz kolory odpowiadające Twojemu przepływowi pracy.',
	ManageStatusesSuccessSnackbar: 'Pomyślnie zaktualizowano statusy',
	ManageTags: 'Zarządzaj tagami',
	ManageTaskAttendeeStatus: 'Zarządzaj statusami wizyt',
	ManageTaskAttendeeStatusDescription: 'Dostosuj statusy swoich wizyt, aby dopasować je do swojego przepływu pracy.',
	ManageTaskAttendeeStatusHelperText: 'Wymagany jest co najmniej jeden status',
	ManageTaskAttendeeStatusSubtitle: 'Statusy niestandardowe',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Podręcznik',
	ManualAppointment: 'Rezerwacja manualna',
	ManualPayment: 'Płatność ręczna',
	ManuallyTypeLocation: 'Wpisz lokalizację ręcznie',
	MapColumns: 'Kolumny mapy',
	MappingRequired: 'Wymagane mapowanie',
	MarkAllAsRead: 'Oznacz wszystkie jako przeczytane',
	MarkAsCompleted: 'Oznacz jako ukończone',
	MarkAsManualSubmission: 'Oznacz jako przesłane',
	MarkAsPaid: 'Oznacz jako opłacone',
	MarkAsRead: 'Oznacz jako przeczytane',
	MarkAsUnpaid: 'Oznacz jako niezapłacone',
	MarkAsUnread: 'Oznacz jako nieprzeczytane',
	MarkAsVoid: 'Oznacz jako nieważne',
	Marker: 'Znacznik',
	MarketingManager: 'Kierownik ds. marketingu',
	MassageTherapist: 'Masażysta',
	MassageTherapists: 'Masażyści',
	MassageTherapy: 'Terapia masażem',
	MaxBookingTimeDescription1: 'Klienci mogą zaplanować do',
	MaxBookingTimeDescription2: 'w przyszłość',
	MaxBookingTimeLabel: '{timePeriod} z góry',
	MaxCapacity: 'Maksymalna pojemność',
	Maximize: 'Wyolbrzymiać',
	MaximumAttendeeLimit: 'Maksymalny limit',
	MaximumBookingTime: 'Maksymalny czas rezerwacji',
	MaximumBookingTimeError: 'Maksymalny czas rezerwacji nie może przekroczyć {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Możesz zminimalizować do {count} paneli bocznych jednocześnie. Kontynuowanie spowoduje zamknięcie najwcześniej zminimalizowanego panelu. Czy chcesz kontynuować?',
	MaximumMinimizedPanelsReachedTitle: 'Masz zbyt wiele otwartych paneli.',
	MechanicalEngineer: 'Inżynier Mechanik',
	MediaGallery: 'Galeria multimediów',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Numer dostawcy Medicaid',
	MedicalAssistant: 'Asystent medyczny',
	MedicalCoder: 'Koder medyczny',
	MedicalDoctor: 'Lekarz medycyny',
	MedicalIllustrator: 'Ilustrator medyczny',
	MedicalInterpreter: 'Tłumacz medyczny',
	MedicalTechnologist: 'Technolog medyczny',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Numer dostawcy Medicare',
	Medicine: 'Medycyna',
	Medium: 'Średni',
	Meeting: 'Spotkanie',
	MeetingEnd: 'Spotkanie końcowe',
	MeetingEnded: 'Spotkanie zakończone',
	MeetingHost: 'Gospodarz spotkania',
	MeetingLowerHand: 'Dolna ręka',
	MeetingOpenChat: 'Otwórz czat',
	MeetingPersonRaisedHand: '{name} podniósł/a rękę',
	MeetingRaiseHand: 'Podnieś rękę',
	MeetingReady: 'Spotkanie gotowe',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Spotkania',
	MemberId: 'Identyfikator członka',
	MentalHealth: 'Zdrowie psychiczne',
	MentalHealthPractitioners: 'Praktycy zdrowia psychicznego',
	MentalHealthProfessional: 'Specjalista ds. zdrowia psychicznego',
	Merge: 'Łączyć',
	MergeClientRecords: 'Scal wszystkie dane klientów',
	MergeClientRecordsDescription: 'Scalanie rekordów klienta połączy wszystkie ich dane, w tym:',
	MergeClientRecordsDescription2: 'Czy chcesz kontynuować scalanie? Ta operacja jest nieodwracalna',
	MergeClientRecordsItem1: 'Notatki i dokumenty',
	MergeClientRecordsItem2: 'Terminy',
	MergeClientRecordsItem3: 'Faktury',
	MergeClientRecordsItem4: 'Rozmowy',
	MergeClientsSuccess: 'Pomyślnie scalono rekord klienta',
	MergeLimitExceeded: 'Możesz połączyć maksymalnie 4 klientów jednocześnie.',
	Message: 'Wiadomość',
	MessageAttachments: '{total} załączników',
	Method: 'Metoda',
	MfaAvailabilityDisclaimer:
		'MFA jest dostępne tylko dla logowania za pomocą adresu e-mail i hasła. Aby wprowadzić zmiany w ustawieniach MFA, zaloguj się za pomocą adresu e-mail i hasła.',
	MfaDeviceLostPanelDescription: 'Alternatywnie możesz potwierdzić swoją tożsamość, otrzymując kod na adres e-mail.',
	MfaDeviceLostPanelTitle: 'Zgubiłeś urządzenie MFA?',
	MfaDidntReceiveEmailCode: 'Nie otrzymałeś kodu? Skontaktuj się z pomocą techniczną',
	MfaEmailOtpSendFailureSnackbar: 'Nie udało się wysłać jednorazowego hasła e-mail.',
	MfaEmailOtpSentSnackbar: 'Kod został wysłany na {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Nie udało się zweryfikować adresu e-mail OTP.',
	MfaHasBeenSetUpText: 'Skonfigurowałeś uwierzytelnianie wieloskładnikowe',
	MfaPanelDescription:
		'Zabezpiecz swoje konto, włączając uwierzytelnianie wieloskładnikowe (MFA) dla dodatkowej warstwy ochrony. Zweryfikuj swoją tożsamość za pomocą metody drugorzędnej, aby zapobiec nieautoryzowanemu dostępowi.',
	MfaPanelNotAuthorizedError: 'Musisz być zalogowany za pomocą nazwy użytkownika ',
	MfaPanelRecommendationDescription:
		'Niedawno zalogowałeś się, używając alternatywnej metody weryfikacji swojej tożsamości. Aby zachować bezpieczeństwo swojego konta, rozważ skonfigurowanie nowego urządzenia MFA.',
	MfaPanelRecommendationTitle: '**Zalecane:** Zaktualizuj swoje urządzenie MFA',
	MfaPanelTitle: 'Uwierzytelnianie wieloskładnikowe (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'Przed zaktualizowaniem ustawień MFA musisz zweryfikować swój adres e-mail.',
	MfaRecommendationBannerDescription:
		'Niedawno zalogowałeś się, używając alternatywnej metody weryfikacji swojej tożsamości. Aby zachować bezpieczeństwo swojego konta, rozważ skonfigurowanie nowego urządzenia MFA.',
	MfaRecommendationBannerPrimaryAction: 'Konfigurowanie uwierzytelniania wieloskładnikowego',
	MfaRecommendationBannerTitle: 'Zalecony',
	MfaRemovedSnackbarTitle: 'MFA zostało usunięte.',
	MfaSendEmailCode: 'Wyślij kod',
	MfaVerifyIdentityLostDeviceButton: 'Straciłem dostęp do mojego urządzenia MFA',
	MfaVerifyYourIdentityPanelDescription: 'Sprawdź kod w swojej aplikacji uwierzytelniającej i wprowadź go poniżej.',
	MfaVerifyYourIdentityPanelTitle: 'Zweryfikuj swoją tożsamość',
	MicCamWarningMessage: 'Odblokuj kamerę i mikrofon klikając zablokowane ikony na pasku adresu przeglądarki.',
	MicCamWarningTitle: 'Kamera i mikrofon są zablokowane',
	MicOff: 'Mikrofon jest wyłączony',
	MicOn: 'Mikrofon jest włączony',
	MicSource: 'Źródło mikrofonu',
	MicWarningMessage: 'Wykryto problem z mikrofonem',
	Microphone: 'Mikrofon',
	MicrophonePermissionBlocked: 'Dostęp do mikrofonu zablokowany',
	MicrophonePermissionBlockedDescription: 'Zaktualizuj uprawnienia mikrofonu, aby rozpocząć nagrywanie.',
	MicrophonePermissionError: 'Aby kontynuować, proszę udzielić pozwolenia na mikrofon w ustawieniach przeglądarki',
	MicrophonePermissionPrompt: 'Aby kontynuować, zezwól na dostęp do mikrofonu',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Kolor kalendarza programu Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Zespoły Microsoft',
	MiddleEast: 'Środkowy Wschód',
	MiddleName: 'Drugie imię',
	MiddleNames: 'Drugie imię',
	Midwife: 'Położna',
	Midwives: 'Położne',
	Milan: 'Mediolan',
	MinBookingTimeDescription1: 'Klienci nie mogą planować w ciągu',
	MinBookingTimeDescription2: 'o godzinie rozpoczęcia spotkania',
	MinBookingTimeLabel: '{timePeriod} przed wizytą',
	MinCancellationTimeEditModeDescription: 'Ustaw liczbę godzin, przez które klient może anulować zamówienie bez kary',
	MinCancellationTimeUnset: 'Brak minimalnego czasu anulowania',
	MinCancellationTimeViewModeDescription: 'Okres anulowania bez kary',
	MinMaxBookingTimeUnset: 'Nie podano czasu',
	Minimize: 'Zminimalizować',
	MinimizeConfirmationDescription:
		'Masz aktywny zminimalizowany panel. Jeśli kontynuujesz, zostanie on zamknięty i możesz stracić niezapisane dane.',
	MinimizeConfirmationTitle: 'Zamknij zminimalizowany panel?',
	MinimumBookingTime: 'Minimalny czas rezerwacji',
	MinimumCancellationTime: 'Minimalny czas anulowania',
	MinimumPaymentError: 'Minimalna opłata w wysokości {minimumAmount} jest wymagana dla płatności online',
	MinuteAbbreviated: 'minuty',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minutowo',
	MinutesPlural: '{age, plural, one {# minutę} other {# minut}}',
	MiscellaneousInformation: 'Informacje różne',
	MissingFeatures: 'Brakujące funkcje',
	MissingPaymentMethod: 'Aby dodać więcej pracowników, dodaj metodę płatności do swojej subskrypcji.',
	MobileNumber: 'Numer telefonu komórkowego',
	MobileNumberOptional: 'Numer telefonu komórkowego (opcjonalnie)',
	Modern: 'Nowoczesny',
	Modifiers: 'Modyfikatory',
	ModifiersPlaceholder: 'Modyfikatory',
	Monday: 'Poniedziałek',
	Month: 'Miesiąc',
	Monthly: 'Miesięczny',
	MonthlyCost: 'Koszt miesięczny',
	MonthlyOn: 'Miesięcznie w dniu {date}',
	MonthsPlural: '{age, plural, one {# miesiąc} other {# miesięcy}}',
	More: 'Więcej',
	MoreActions: 'Więcej akcji',
	MoreSettings: 'Więcej ustawień',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Najczęściej używane',
	MostDownloaded: 'Najczęściej pobierane',
	MostPopular: 'Najpopularniejsze',
	Mother: 'Matka',
	MotherInLaw: 'Teściowa',
	MoveDown: 'Opuszczać',
	MoveInboxConfirmationDescription:
		'Przeniesienie tego połączenia aplikacji usunie je ze skrzynki odbiorczej **{currentInboxName}**.',
	MoveTemplateToFolder: 'Przenieś `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} przeniesiono do {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Pomyślnie przeniesiono do domyślnego folderu przyjęcia',
	MoveTemplateToNewFolder: 'Utwórz nowy folder, aby przenieść ten element.',
	MoveToChosenFolder:
		'Wybierz folder, do którego chcesz przenieść ten element. W razie potrzeby możesz utworzyć nowy folder.',
	MoveToFolder: 'Przenieś do folderu',
	MoveToInbox: 'Przenieś do skrzynki odbiorczej',
	MoveToNewFolder: 'Przenieś do nowego folderu',
	MoveToSelectedFolder:
		'Po przeniesieniu element zostanie zorganizowany w wybranym folderze i nie będzie już widoczny w obecnej lokalizacji.',
	MoveUp: 'Podnieść',
	MultiSpeciality: 'Wielospecjalistyczny',
	MultipleChoiceFormPrimaryText: 'Wielokrotny wybór',
	MultipleChoiceFormSecondaryText: 'Wybierz wiele opcji',
	MultipleChoiceGridFormPrimaryText: 'Siatka wielokrotnego wyboru',
	MultipleChoiceGridFormSecondaryText: 'Wybierz opcje z macierzy',
	Mumbai: 'Bombaj',
	MusicTherapist: 'Muzykoterapeuta',
	MustContainOneLetterError: 'Musi zawierać co najmniej jedną literę',
	MustEndWithANumber: 'Musi kończyć się liczbą',
	MustHaveAtLeastXItems: 'Musi mieć co najmniej {count, plural, one {# element} other {# elementy}}',
	MuteAudio: 'Wycisz dźwięk',
	MuteEveryone: 'Wycisz wszystkich',
	MyAvailability: 'Moja dostępność',
	MyGallery: 'Moja galeria',
	MyPortal: 'Mój Portal',
	MyRelationships: 'Moje związki',
	MyTemplates: 'Szablony zespołów',
	MyofunctionalTherapist: 'Terapeuta miofunkcjonalny',
	NCalifornia: 'Północna Kalifornia',
	NPI: 'NPI',
	NVirginia: 'Północna Wirginia',
	Name: 'Nazwa',
	NameIsRequired: 'Imię jest wymagane',
	NameMustNotBeAWebsite: 'Nazwa nie może być nazwą strony internetowej',
	NameMustNotBeAnEmail: 'Nazwa nie może być adresem e-mail',
	NameMustNotContainAtSign: 'Nazwa nie może zawierać znaku @',
	NameMustNotContainHTMLTags: 'Nazwa nie może zawierać znaczników HTML',
	NameMustNotContainSpecialCharacters: 'Nazwa nie może zawierać znaków specjalnych',
	NameOnCard: 'Imię na karcie',
	NationalProviderId: 'Krajowy identyfikator dostawcy (NPI)',
	NaturopathicDoctor: 'Lekarz Naturopata',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Ustawienia subskrypcji',
	NavigateToWorkspaceSettings: 'Ustawienia przestrzeni roboczej',
	NavigateToYourTeam: 'Zarządzaj zespołem',
	NavigationDrawerBilling: 'Fakturowanie',
	NavigationDrawerBillingInfo: 'Informacje o rozliczeniach, faktury i Stripe',
	NavigationDrawerCommunication: 'Komunikacja',
	NavigationDrawerCommunicationInfo: 'Powiadomienia i szablony',
	NavigationDrawerInsurance: 'Ubezpieczenie',
	NavigationDrawerInsuranceInfo: 'Płatnicy i roszczenia ubezpieczeniowe',
	NavigationDrawerInvoices: 'Rozliczanie',
	NavigationDrawerPersonal: 'Mój profil',
	NavigationDrawerPersonalInfo: 'Twoje dane osobowe',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Ustawienia',
	NavigationDrawerScheduling: 'Harmonogramowanie',
	NavigationDrawerSchedulingInfo: 'Szczegóły usług i rezerwacje',
	NavigationDrawerSettings: 'Ustawienia',
	NavigationDrawerTemplates: 'Szablony',
	NavigationDrawerTemplatesV2: 'Szablony V2',
	NavigationDrawerTrash: 'Śmieci',
	NavigationDrawerTrashInfo: 'Przywróć usunięte elementy',
	NavigationDrawerWorkspace: 'Ustawienia obszaru roboczego',
	NavigationDrawerWorkspaceInfo: 'Informacje o subskrypcji i przestrzeni roboczej',
	NegativeBalanceNotSupported: 'Ujemne salda kont nie są obsługiwane',
	Nephew: 'Siostrzeniec',
	NetworkQualityFair: 'Sprawiedliwe połączenie',
	NetworkQualityGood: 'Dobre połączenie',
	NetworkQualityPoor: 'Słabe połączenie',
	Neurologist: 'Neurolog',
	Never: 'Nigdy',
	New: 'Nowy',
	NewAppointment: 'Nowe spotkanie',
	NewClaim: 'Nowe roszczenie',
	NewClient: 'Nowy klient',
	NewClientNextStepsModalAddAnotherClient: 'Dodaj kolejnego klienta',
	NewClientNextStepsModalBookAppointment: 'Zarezerwuj wizytę',
	NewClientNextStepsModalBookAppointmentDescription: 'Zarezerwuj nadchodzące spotkanie lub utwórz zadanie.',
	NewClientNextStepsModalCompleteBasicInformation: 'Kompletny rejestr klienta',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'Dodaj informacje o kliencie i zapisz kolejne kroki.',
	NewClientNextStepsModalCreateInvoice: 'Utwórz fakturę',
	NewClientNextStepsModalCreateInvoiceDescription: 'Dodaj informacje o płatnościach klienta lub utwórz fakturę.',
	NewClientNextStepsModalCreateNote: 'Utwórz notatkę lub prześlij dokument',
	NewClientNextStepsModalCreateNoteDescription: 'Rejestruj notatki i dokumentację klientów.',
	NewClientNextStepsModalDescription: 'Oto kilka czynności, które należy wykonać po utworzeniu rekordu klienta.',
	NewClientNextStepsModalSendIntake: 'Wyślij przyjęcie',
	NewClientNextStepsModalSendIntakeDescription:
		'Zbierz informacje o kliencie i wyślij dodatkowe formularze do wypełnienia i podpisania.',
	NewClientNextStepsModalSendMessage: 'Wyślij wiadomość',
	NewClientNextStepsModalSendMessageDescription: 'Napisz i wyślij wiadomość do swojego klienta.',
	NewClientNextStepsModalTitle: 'Następne kroki',
	NewClientSuccess: 'Pomyślnie utworzono nowego klienta',
	NewClients: 'Nowi klienci',
	NewConnectedApp: 'Nowa połączona aplikacja',
	NewContact: 'Nowy kontakt',
	NewContactNextStepsModalAddRelationship: 'Dodaj relację',
	NewContactNextStepsModalAddRelationshipDescription: 'Połącz ten kontakt z powiązanymi klientami lub grupami.',
	NewContactNextStepsModalBookAppointment: 'Umów wizytę',
	NewContactNextStepsModalBookAppointmentDescription: 'Umów spotkanie lub utwórz zadanie.',
	NewContactNextStepsModalCompleteProfile: 'Pełny profil',
	NewContactNextStepsModalCompleteProfileDescription: 'Dodaj dane kontaktowe i zapisz kolejne kroki.',
	NewContactNextStepsModalCreateNote: 'Utwórz notatkę lub załaduj dokument',
	NewContactNextStepsModalCreateNoteDescription: 'Rejestruj notatki i dokumentację klienta.',
	NewContactNextStepsModalDescription: 'Oto kilka czynności, które możesz wykonać po utworzeniu kontaktu.',
	NewContactNextStepsModalInviteToPortal: 'Zaproszenie do portalu',
	NewContactNextStepsModalInviteToPortalDescription: 'Wyślij zaproszenie do dostępu do portalu.',
	NewContactNextStepsModalTitle: 'Następne kroki',
	NewContactSuccess: 'Pomyślnie utworzono nowy kontakt',
	NewDateOverrideButton: 'Nowe zastąpienie daty',
	NewDiagnosis: 'Dodaj diagnozę',
	NewField: 'Nowe pole',
	NewFolder: 'Nowy folder',
	NewInvoice: 'Nowa faktura',
	NewLocation: 'Nowa lokalizacja',
	NewLocationFailure: 'Nie udało się utworzyć nowej lokalizacji',
	NewLocationSuccess: 'Pomyślnie utworzono nową lokalizację',
	NewManualPayer: 'Nowy płatnik ręczny',
	NewNote: 'Nowa notatka',
	NewNoteCreated: 'Pomyślnie utworzono nową notatkę',
	NewPassword: 'Nowe hasło',
	NewPayer: 'Nowy płatnik',
	NewPaymentMethod: 'Nowa metoda płatności',
	NewPolicy: 'Nowa polityka',
	NewRelationship: 'Nowy związek',
	NewReminder: 'Nowe przypomnienie',
	NewSchedule: 'Nowy harmonogram',
	NewSection: 'Nowa sekcja',
	NewSectionOld: 'Nowa sekcja [STARA]',
	NewSectionWithGrid: 'Nowa sekcja z siatką',
	NewService: 'Nowa usługa',
	NewServiceFailure: 'Nie udało się utworzyć nowej usługi',
	NewServiceSuccess: 'Pomyślnie utworzono nową usługę',
	NewStatus: 'Nowy status',
	NewTask: 'Nowe zadanie',
	NewTaxRate: 'Nowa stawka podatkowa',
	NewTeamMemberNextStepsModalAssignClients: 'Przypisz klientów',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Przypisz określonych klientów do członka Twojego zespołu.',
	NewTeamMemberNextStepsModalAssignServices: 'Przypisz usługi',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Zarządzaj przypisanymi usługami i dostosuj ceny w razie potrzeby.',
	NewTeamMemberNextStepsModalBookAppointment: 'Umów wizytę',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Zarezerwuj nadchodzące spotkanie lub utwórz zadanie.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Pełny profil',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Dodaj szczegóły dotyczące członka Twojego zespołu, aby uzupełnić jego profil.',
	NewTeamMemberNextStepsModalDescription:
		'Oto kilka czynności, które możesz teraz wykonać, po stworzeniu członka zespołu.',
	NewTeamMemberNextStepsModalEditPermissions: 'Uprawnienia do edycji',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Dostosuj ich poziomy dostępu, aby zapewnić im odpowiednie uprawnienia.',
	NewTeamMemberNextStepsModalSetAvailability: 'Ustaw dostępność',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Konfiguruj ich dostępność, aby tworzyć harmonogramy.',
	NewTeamMemberNextStepsModalTitle: 'Następne kroki',
	NewTemplateFolderDescription: 'Utwórz nowy folder, aby uporządkować swoją dokumentację.',
	NewUIUpdateBannerButton: 'Przeładuj aplikację',
	NewUIUpdateBannerTitle: 'Nowa aktualizacja jest już gotowa!',
	NewZealand: 'Nowa Zelandia',
	Newest: 'Najnowsze',
	NewestUnreplied: 'Najnowsze bez odpowiedzi',
	Next: 'Następny',
	NextInvoiceIssueDate: 'Data wystawienia kolejnej faktury',
	NextNDays: 'Następne {number} dni',
	Niece: 'Siostrzenica',
	No: 'NIE',
	NoAccessGiven: 'Brak dostępu',
	NoActionConfigured: 'Brak skonfigurowanej akcji',
	NoActivePolicies: 'Brak aktywnych zasad',
	NoActiveReferrals: 'Nie masz aktywnych poleceń',
	NoAppointmentsFound: 'Nie znaleziono żadnych terminów',
	NoAppointmentsHeading: 'Zarządzaj spotkaniami i aktywnościami klientów',
	NoArchivedPolicies: 'Brak zarchiwizowanych zasad',
	NoAvailableTimes: 'Nie znaleziono dostępnych terminów.',
	NoBillingItemsFound: 'Nie znaleziono żadnych pozycji rozliczeniowych',
	NoCalendarsSynced: 'Brak synchronizowanych kalendarzy',
	NoClaimsFound: 'Brak zgłoszeń',
	NoClaimsHeading: 'Usprawnione składanie wniosków o zwrot kosztów',
	NoClientsHeading: 'Zbierz razem swoje dane klientów',
	NoCompletedReferrals: 'Nie masz żadnych kompletnych poleceń',
	NoConnectionsHeading: 'Usprawnij komunikację z klientami',
	NoContactsGivenAccess: 'Żaden klient ani kontakt nie otrzymał dostępu do tej notatki',
	NoContactsHeading: 'Pozostań w kontakcie z osobami, które wspierają Twoją praktykę',
	NoCopayOrCoinsurance: 'Brak dopłat i współubezpieczenia',
	NoCustomServiceSchedule:
		'Brak ustalonego niestandardowego harmonogramu — dostępność zależy od dostępności członka zespołu',
	NoDescription: 'Brak opisu',
	NoDocumentationHeading: 'Bezpieczne tworzenie i przechowywanie notatek',
	NoDuplicateRecordsHeading: 'Twój rekord klienta jest wolny od duplikatów',
	NoEffect: 'Brak efektu',
	NoEnrolmentProfilesFound: 'Nie znaleziono profili rejestracyjnych',
	NoGlossaryItems: 'Brak elementów słownika',
	NoInvitedReferrals: 'Nie masz żadnych zaproszonych poleceń',
	NoInvoicesFound: 'Brak znalezionych faktur',
	NoInvoicesHeading: 'Zautomatyzuj swoje fakturowanie i płatności',
	NoLimit: 'Bez limitu',
	NoLocationsFound: 'Nie znaleziono żadnych lokalizacji',
	NoLocationsWillBeAdded: 'Nie zostaną dodane żadne lokalizacje.',
	NoNoteFound: 'Nie znaleziono notatki',
	NoPaymentMethods: 'Nie masz zapisanych metod płatności, możesz je dodać podczas dokonywania płatności.',
	NoPermissionError: 'Nie masz uprawnień',
	NoPermissions: 'Nie masz uprawnień do przeglądania tej strony',
	NoPolicy: 'Nie dodano żadnej polityki anulowania',
	NoRecordsHeading: 'Personalizuj swoje dane klientów',
	NoRecordsToDisplay: 'Brak {resource} do wyświetlenia',
	NoRelationshipsHeading: 'Pozostań w kontakcie z osobami wspierającymi Twojego klienta',
	NoRemindersFound: 'Nie znaleziono przypomnień',
	NoResultsFound: 'Nie znaleziono wyników',
	NoResultsFoundDescription: 'Nie możemy znaleźć żadnych pozycji odpowiadających Twojemu wyszukiwaniu',
	NoServicesAdded: 'Brak dodanych usług',
	NoServicesApplied: 'Nie zastosowano żadnych usług',
	NoServicesWillBeAdded: 'Żadne usługi nie zostaną dodane.',
	NoTemplate: 'Nie masz zapisanych szablonów ćwiczeń',
	NoTemplatesHeading: 'Utwórz własne szablony',
	NoTemplatesInFolder: 'Brak szablonów w tym folderze',
	NoTitle: 'Brak tytułu',
	NoTrashItemsHeading: 'Nie znaleziono usuniętego elementu',
	NoTriggerConfigured: 'Nie skonfigurowano żadnego wyzwalacza',
	NoUnclaimedItemsFound: 'Nie znaleziono nieodebranych przedmiotów.',
	NonAiTemplates: 'Szablony niebędące AI',
	None: 'Nic',
	NotAvailable: 'Niedostępne',
	NotCovered: 'Nieobjęte',
	NotFoundSnackbar: 'Nie znaleziono zasobu.',
	NotRequiredField: 'Nie wymagane',
	Note: 'Notatka',
	NoteDuplicateSuccess: 'Notatka została pomyślnie zduplikowana',
	NoteEditModeViewSwitcherDescription: 'Utwórz i edytuj notatkę',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} przesłał formularz {noteTitle}',
	NoteLockSuccess: '{title} zostało zablokowane',
	NoteModalAttachmentButton: 'Dodaj załączniki',
	NoteModalPhotoButton: 'Dodaj/zrób zdjęcia',
	NoteModalTrascribeButton: 'Transkrybuj dźwięk na żywo',
	NoteResponderModeViewSwitcherDescription: 'Wysyłaj formularze i przeglądaj odpowiedzi',
	NoteResponderModeViewSwitcherTooltipTitle: 'Odpowiadaj i przesyłaj formularze w imieniu swoich klientów',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Wypełniaj i przesyłaj formularze jako klient',
	NoteUnlockSuccess: '{title} zostało odblokowane',
	NoteViewModeViewSwitcherDescription: 'Dostęp tylko do przeglądania',
	Notes: 'Notatki',
	NotesAndForms: 'Notatki i formularze',
	NotesCategoryDescription: 'Do dokumentowania interakcji z klientem',
	NothingToSeeHere: 'Nic tu nie ma do zobaczenia',
	Notification: 'Powiadomienie',
	NotificationIgnoredMessage: 'Wszystkie powiadomienia {notificationType} będą ignorowane',
	NotificationRestoredMessage: 'Wszystkie powiadomienia typu {notificationType} zostały przywrócone',
	NotificationSettingBillingDescription:
		'Otrzymuj powiadomienia o aktualizacjach i przypomnieniach dotyczących płatności klientów.',
	NotificationSettingBillingTitle: 'Rozliczenia i płatności',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} tylko} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Otrzymuj powiadomienia o aktualizacjach i przypomnieniach dotyczących płatności klientów.',
	NotificationSettingClientDocumentationTitle: 'Klient i dokumentacja',
	NotificationSettingCommunicationsDescription:
		'Otrzymuj powiadomienia do skrzynki odbiorczej i aktualizacje ze swoich połączonych kanałów',
	NotificationSettingCommunicationsTitle: 'Komunikacja',
	NotificationSettingEmail: 'E-mail',
	NotificationSettingInApp: 'W aplikacji',
	NotificationSettingPanelDescription:
		'Wybierz powiadomienia, które chcesz otrzymywać dotyczące aktywności i rekomendacji.',
	NotificationSettingPanelTitle: 'Preferencje powiadomień',
	NotificationSettingSchedulingDescription:
		'Otrzymuj powiadomienia, gdy członek zespołu lub klient zarezerwuje, zmieni termin lub odwoła wizytę.',
	NotificationSettingSchedulingTitle: 'Harmonogramowanie',
	NotificationSettingUpdateSuccess: 'Ustawienia powiadomień zaktualizowane pomyślnie',
	NotificationSettingWhereYouReceiveNotifications: 'Gdzie chcesz otrzymywać te powiadomienia',
	NotificationSettingWorkspaceDescription:
		'Otrzymuj powiadomienia o zmianach systemowych, problemach, transferach danych i przypomnieniach o subskrypcji.',
	NotificationSettingWorkspaceTitle: 'Przestrzeń robocza',
	NotificationTemplateUpdateFailed: 'Nie udało się zaktualizować szablonu powiadomienia',
	NotificationTemplateUpdateSuccess: 'Pomyślnie zaktualizowano szablon powiadomienia',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Czy chcesz wysłać uczestnikom e-mail z powiadomieniem o anulowaniu?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Wyślij anulowanie',
	NotifyAttendeesOfTaskConfirmationModalDescription: 'Czy chcesz wysłać uczestnikom e-mail z potwierdzeniem?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Wyślij potwierdzenie',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Czy chcesz wysłać uczestnikom e-maile z informacją o anulowaniu?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {ma} other {mają}} adres e-mail, więc nie będą otrzymywać automatycznych powiadomień i przypomnień.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {nie ma} other {nie mają}} adresu e-mail, więc nie otrzymają automatycznych powiadomień i przypomnień.',
	NotifyAttendeesOfTaskModalTitle: 'Czy chcesz wysłać uczestnikom powiadomienie e-mailem?',
	NotifyAttendeesOfTaskSnackbar: 'Wysyłanie powiadomienia',
	NuclearMedicineTechnologist: 'Technolog medycyny nuklearnej',
	NumberOfClaims: '{number, plural, one {# Zgłoszenie} other {# Zgłoszenia}}',
	NumberOfClients: '{number, plural, one {# Klient} other {# Klienci}}',
	NumberOfContacts: '{number, plural, one {# Kontakt} other {# Kontakty}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entry} other {entries}} znaleziono',
	NumberOfErrors: '{count, plural, one {# błąd} other {# błędy}}',
	NumberOfInvoices: '{number, plural, one {# Faktura} other {# Faktury}}',
	NumberOfLineitemsToCredit:
		'Masz <mark>{count} {count, plural, one {pozycję} other {pozycje}}</mark> do wystawienia kredytu.',
	NumberOfPayments: '{number, plural, one {# Płatność} other {# Płatności}}',
	NumberOfRelationships: '{number, plural, one {# Związek} other {# Związki}}',
	NumberOfResources: '{number, plural, one {# Zasób} other {# Zasoby}}',
	NumberOfTeamMembers: '{number, plural, one {# Członek zespołu} other {# Członkowie zespołu}}',
	NumberOfTrashItems: '{number, plural, one {# element} other {# elementy}}',
	NumberOfUninvoicedAmounts:
		'Masz <mark>{count} niefakturowanych {count, plural, one {kwota} other {kwoty}}</mark> do fakturowania',
	NumberedList: 'Lista numerowana',
	Nurse: 'Pielęgniarka',
	NurseAnesthetist: 'Pielęgniarka Anestezjolog',
	NurseAssistant: 'Asystent pielęgniarki',
	NurseEducator: 'Pielęgniarka Edukator',
	NurseMidwife: 'Pielęgniarka Położna',
	NursePractitioner: 'Pielęgniarka Praktykująca',
	Nurses: 'Pielęgniarki',
	Nursing: 'Pielęgniarstwo',
	Nutritionist: 'Specjalista od żywienia',
	Nutritionists: 'Dietetycy',
	ObstetricianOrGynecologist: 'Położnik/Ginekolog',
	Occupation: 'Zawód',
	OccupationalTherapist: 'Terapeuta zajęciowy',
	OccupationalTherapists: 'Terapeuci zajęciowi',
	OccupationalTherapy: 'Terapia zajęciowa',
	Occurrences: 'Wystąpienia',
	Of: 'z',
	Ohio: 'Ohio',
	OldPassword: 'Stare hasło',
	OlderMessages: '{count} starsze wiadomości',
	Oldest: 'Najstarszy',
	OldestUnreplied: 'Najstarsze bez odpowiedzi',
	On: 'NA',
	OnboardingBusinessAgreement: 'W imieniu swoim i firmy wyrażam zgodę na {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Terapeuci zajęciowi</mark> stanowią jedną czwartą naszych klientów na Carepatron',
	OnboardingLoadingProfession:
		'Mamy mnóstwo <mark>{profession}</mark> korzystających z Carepatron i rozwijających się dzięki niemu.',
	OnboardingLoadingPsychologist: '<mark>Psychologowie</mark> stanowią ponad połowę naszych klientów na Carepatron',
	OnboardingLoadingSubtitleFive:
		'Naszą misją jest tworzenie<mark> oprogramowanie dla służby zdrowia dostępne</mark> dla wszystkich.',
	OnboardingLoadingSubtitleFour:
		'<mark>Uproszczone oprogramowanie zdrowotne</mark> dla ponad 10 000 osób na całym świecie.',
	OnboardingLoadingSubtitleThree:
		'Ratować<mark> 1 dzień w tygodniu</mark> w zakresie zadań administracyjnych przy pomocy Carepatrona.',
	OnboardingLoadingSubtitleTwo:
		'Ratować<mark> 2 godziny</mark> codziennie nad zadaniami administracyjnymi przy pomocy Carepatrona.',
	OnboardingReviewLocationOne: 'Centrum Zdrowia Psychicznego Holland Park',
	OnboardingReviewLocationThree: 'Pielęgniarka praktyczna, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Klinika Domu Życia',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alicja E.',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'„Carepatron jest superintuicyjny w obsłudze. Pomaga nam prowadzić naszą praktykę tak dobrze, że nie potrzebujemy już nawet zespołu administratorów”',
	OnboardingReviewThree:
		'„To najlepsze rozwiązanie praktyczne, z którego korzystałem zarówno pod względem funkcji, jak i kosztów. Ma wszystko, czego potrzebuję, aby rozwijać swój biznes”',
	OnboardingReviewTwo: '„Uwielbiam też aplikację carepatron. Pomaga mi śledzić moich klientów i pracę w podróży”.',
	OnboardingTitle: `Przejdźmy do<mark> wiedzieć
 lepiej, żebyś</mark>`,
	Oncologist: 'Onkolog',
	Online: 'W sieci',
	OnlineBookingColorTheme: 'Motyw kolorystyczny rezerwacji online',
	OnlineBookings: 'Rezerwacje online',
	OnlineBookingsHelper: 'Wybierz, kiedy można dokonywać rezerwacji online i dla jakiego typu klientów',
	OnlinePayment: 'Płatność online',
	OnlinePaymentSettingCustomInfo:
		'Ustawienia płatności online dla tej usługi różnią się od globalnych ustawień rezerwacji.',
	OnlinePaymentSettings: 'Ustawienia płatności online',
	OnlinePaymentSettingsInfo:
		'Zbieraj płatności za usługi w momencie dokonywania rezerwacji online, aby zabezpieczyć i usprawnić płatności',
	OnlinePaymentSettingsPaymentsDisabled:
		'Płatności są wyłączone, więc nie można ich pobierać podczas rezerwacji online. Sprawdź ustawienia płatności, aby włączyć płatności.',
	OnlinePaymentSettingsStripeNote:
		'{action} do otrzymywania płatności za rezerwacje online i usprawnienia procesu płatności',
	OnlinePaymentsNotSupportedForCurrency: 'Płatności online nie są obsługiwane w {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Niestety, płatności online nie są obsługiwane w tej walucie',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Przepraszamy, płatności online nie są jeszcze obsługiwane w Twoim kraju',
	OnlineScheduling: 'Harmonogramowanie online',
	OnlyVisibleToYou: 'Widoczne tylko dla Ciebie',
	OnlyYou: 'Tylko ty',
	OnsetDate: 'Data rozpoczęcia',
	OnsetOfCurrentSymptomsOrIllness: 'Wystąpienie obecnych objawów lub choroby',
	Open: 'Otwarte',
	OpenFile: 'Otwórz plik',
	OpenSettings: 'Otwórz ustawienia',
	Ophthalmologist: 'Okulista',
	OptimiseTelehealthCalls: 'Zoptymalizuj swoje połączenia telemedyczne',
	OptimizeServiceTimes: 'Zoptymalizuj czasy obsługi',
	Options: 'Opcje',
	Optometrist: 'Optometrysta',
	Or: 'Lub',
	OrAttachSingleFile: 'załącz plik',
	OrDragAndDrop: 'lub przeciągnij i upuść',
	OrderBy: 'Sortuj według',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organizacja lub osoba fizyczna',
	OrganizationPlanInclusion1: 'Zaawansowane uprawnienia',
	OrganizationPlanInclusion2: 'Bezpłatne wsparcie importu danych klientów',
	OrganizationPlanInclusion3: 'Dedykowany menedżer sukcesu',
	OrganizationPlanInclusionHeader: 'Wszystko, co w wersji Professional, plus...',
	Orthodontist: 'Ortodonta',
	Orthotist: 'Ortopeda',
	Other: 'Inny',
	OtherAdjustments: 'Inne dostosowania',
	OtherAdjustmentsTableEmptyState: 'Nie znaleziono żadnych zmian',
	OtherEvents: 'Inne wydarzenia',
	OtherId: 'Inne ID',
	OtherIdQualifier: 'Inny kwalifikator ID',
	OtherPaymentMethod: 'Inna metoda płatności',
	OtherPlanMessage:
		'Zachowaj kontrolę nad potrzebami swojej praktyki. Przejrzyj swój obecny plan, monitoruj wykorzystanie i poznaj opcje aktualizacji, aby odblokować więcej funkcji w miarę rozwoju zespołu.',
	OtherPolicy: 'Inne ubezpieczenia',
	OtherProducts: 'Jakich innych produktów i narzędzi używasz?',
	OtherServices: 'Inne usługi',
	OtherTemplates: 'Inne szablony',
	Others: 'Inni',
	OthersPeople: `{n, plural, 		one {1 inna osoba}
		other {# innych osób}
	}`,
	OurResearchTeamReachOut:
		'Czy nasz zespół badawczy może się z Tobą skontaktować, aby dowiedzieć się więcej o tym, w jaki sposób Carepatron mógłby lepiej spełnić Twoje potrzeby?',
	OutOfOffice: 'Poza biurem',
	OutOfOfficeColor: 'Kolor poza biurem',
	OutOfOfficeHelper: 'Niektórzy wybrani członkowie zespołu są poza biurem',
	OutsideLabCharges: 'Opłaty za korzystanie z laboratorium zewnętrznego',
	OutsideOfWorkingHours: 'Poza godzinami pracy',
	OutsideWorkingHoursHelper: 'Niektórzy wybrani członkowie zespołu pracują poza godzinami pracy',
	Overallocated: 'Nadmiernie przydzielony',
	OverallocatedPaymentDescription: `Ta płatność została nadmierna w stosunku do pozycji rozliczanych.
 Dodaj alokację do niezapłaconych pozycji lub wystaw notę kredytową lub zwróć pieniądze.`,
	OverallocatedPaymentTitle: 'Nadmiernie przydzielona płatność',
	OverdueTerm: 'Termin przeterminowania (dni)',
	OverinvoicedAmount: 'Zawyżona kwota faktury',
	Overpaid: 'Przepłacony',
	OverpaidAmount: 'Kwota nadpłacona',
	Overtime: 'nadgodziny',
	Owner: 'Właściciel',
	POS: 'POZ',
	POSCode: 'Kod POS',
	POSPlaceholder: 'POZ',
	PageBlockerDescription: 'Niezapisane zmiany zostaną utracone. Nadal chcesz opuścić stronę?',
	PageBlockerTitle: 'Odrzucić zmiany?',
	PageFormat: 'Format strony',
	PageNotFound: 'Strona nie znaleziona',
	PageNotFoundDescription: 'Nie masz już dostępu do tej strony lub nie można jej znaleźć',
	PageUnauthorised: 'Nieautoryzowany dostęp',
	PageUnauthorisedDescription: 'Nie masz uprawnień do dostępu do tej strony',
	Paid: 'Płatny',
	PaidAmount: 'Zapłacona kwota',
	PaidAmountMinimumValueError: 'Kwota zapłacona musi być większa niż 0',
	PaidAmountRequiredError: 'Wymagana jest zapłacona kwota',
	PaidItems: 'Przedmioty płatne',
	PaidMultiple: 'Płatny',
	PaidOut: 'Wypłacono',
	ParagraphStyles: 'Style akapitów',
	Parent: 'Roślina mateczna',
	Paris: 'Paryż',
	PartialRefundAmount: 'Częściowo zwrócono ({amount} pozostało)',
	PartiallyFull: 'Częściowo pełny',
	PartiallyPaid: 'Częściowo opłacone',
	PartiallyRefunded: 'Częściowo zwrócono',
	Partner: 'Partner',
	Password: 'Hasło',
	Past: 'Przeszłość',
	PastDateOverridesEmpty: 'Twoje nadpisania dat pojawią się tutaj, gdy tylko wydarzenie minie',
	Pathologist: 'Patolog',
	Patient: 'Pacjent',
	Pause: 'Pauza',
	Paused: 'Wstrzymano',
	Pay: 'Płacić',
	PayMonthly: 'Płać miesięcznie',
	PayNow: 'Zapłać teraz',
	PayValue: 'Płać {showPrice, select, true {{price}} other {teraz}}',
	PayWithOtherCard: 'Zapłać inną kartą',
	PayYearly: 'Płać rocznie',
	PayYearlyPercentOff: 'Płać rocznie <mark>{percent}% taniej</mark>',
	Payer: 'Płatnik',
	PayerClaimId: 'Numer identyfikacyjny płatnika',
	PayerCoverage: 'Zasięg',
	PayerDetails: 'Dane płatnika',
	PayerDetailsDescription: 'Przeglądaj dane płatników dodane do Twojego konta i zarządzaj zapisami.',
	PayerID: 'ID płatnika',
	PayerId: 'ID płatnika',
	PayerName: 'Nazwa płatnika',
	PayerPhoneNumber: 'Numer telefonu płatnika',
	Payers: 'Płatnicy',
	Payment: 'Zapłata',
	PaymentAccountUpdated: 'Twoje konto zostało zaktualizowane!',
	PaymentAccountUpgraded: 'Twoje konto zostało uaktualnione!',
	PaymentAmount: 'Kwota płatności',
	PaymentDate: 'Data płatności',
	PaymentDetails: 'Dane płatności',
	PaymentForUsersPerMonth:
		'Płatność za {billedUsers, plural, one {# użytkownika} other {# użytkowników}} miesięcznie',
	PaymentInfoFormPrimaryText: 'Informacje o płatnościach',
	PaymentInfoFormSecondaryText: 'Zbierz szczegóły płatności',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Ta faktura została już zapłacona.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Ta faktura jest już przetwarzana.',
	PaymentIntentAmountMismatchSnackbar:
		'Całkowita kwota faktury została zmodyfikowana. Przed dokonaniem płatności zapoznaj się ze zmianami.',
	PaymentIntentSyncTimeoutSnackbar:
		'Twoja płatność powiodła się, ale wystąpiło przekroczenie limitu czasu. Odśwież stronę i jeśli Twoja płatność nie jest wyświetlana, skontaktuj się z pomocą techniczną.',
	PaymentMethod: 'Metoda płatności',
	PaymentMethodDescription:
		'Dodaj i zarządzaj metodą płatności w swojej praktyce, aby usprawnić proces rozliczania abonamentu.',
	PaymentMethodLabelBank: 'rachunek bankowy',
	PaymentMethodLabelCard: 'karta',
	PaymentMethodLabelFallback: 'metoda płatności',
	PaymentMethodRequired: 'Przed zmianą subskrypcji dodaj metodę płatności',
	PaymentMethods: 'Metody płatności',
	PaymentProcessing: 'Przetwarzanie płatności!',
	PaymentProcessingFee: 'Płatność zawiera opłatę za przetwarzanie w wysokości {amount}',
	PaymentReports: 'Raporty płatności (ERA)',
	PaymentSettings: 'Ustawienia płatności',
	PaymentSuccessful: 'Płatność powiodła się!',
	PaymentType: 'Rodzaj płatności',
	Payments: 'Płatności',
	PaymentsAccountDisabledNotificationSubject: `Płatności online za pośrednictwem {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} zostały wyłączone.
Sprawdź ustawienia płatności, aby włączyć płatności.`,
	PaymentsEmptyStateDescription: 'Nie znaleziono żadnych płatności.',
	PaymentsUnallocated: 'Nieprzydzielone płatności',
	PayoutDate: 'Data wypłaty',
	PayoutsDisabled: 'Wypłaty wyłączone',
	PayoutsEnabled: 'Wypłaty włączone',
	PayoutsStatus: 'Status wypłaty',
	Pediatrician: 'Pediatra',
	Pen: 'Pióro',
	Pending: 'Aż do',
	People: '{rosterSize } osób',
	PeopleCount: 'Osoby ({count})',
	PerMonth: '/ Miesiąc',
	PerUser: 'Na życzenie użytkownika',
	Permission: 'Pozwolenie',
	PermissionRequired: 'Wymagane pozwolenie',
	Permissions: 'Uprawnienia',
	PermissionsClientAndContactDocumentation: 'Klient ',
	PermissionsClientAndContactProfiles: 'Klient ',
	PermissionsEditAccess: 'Dostęp do edycji',
	PermissionsInvoicesAndPayments: 'Faktury ',
	PermissionsScheduling: 'Harmonogramowanie',
	PermissionsUnassignClients: 'Anuluj przypisanie klientów',
	PermissionsUnassignClientsConfirmation: 'Czy na pewno chcesz usunąć przypisanie tych klientów?',
	PermissionsValuesAssigned: 'Tylko przypisane',
	PermissionsValuesEverything: 'Wszystko',
	PermissionsValuesNone: 'Nic',
	PermissionsValuesOwnCalendar: 'Własny kalendarz',
	PermissionsViewAccess: 'Wyświetl dostęp',
	PermissionsWorkspaceSettings: 'Ustawienia obszaru roboczego',
	Person: '{rosterSize} osoba',
	PersonalDetails: 'Dane osobowe',
	PersonalHealthcareHistoryStoreDescription:
		'Odpowiadaj i bezpiecznie przechowuj swoją historię opieki zdrowotnej w jednym miejscu',
	PersonalTrainer: 'Trener personalny',
	PersonalTraining: 'Trening personalny',
	PersonalizeWorkspace: 'Spersonalizuj swoje miejsce pracy',
	PersonalizingYourWorkspace: 'Personalizacja miejsca pracy',
	Pharmacist: 'Farmaceuta',
	Pharmacy: 'Apteka',
	PhoneCall: 'Rozmowa telefoniczna',
	PhoneNumber: 'Numer telefonu',
	PhoneNumberOptional: 'Numer telefonu (opcjonalnie)',
	PhotoBy: 'Zdjęcie autorstwa',
	PhysicalAddress: 'Adres fizyczny',
	PhysicalTherapist: 'Fizjoterapeuta',
	PhysicalTherapists: 'Fizjoterapeuci',
	PhysicalTherapy: 'Fizjoterapia',
	Physician: 'Lekarz',
	PhysicianAssistant: 'Asystent lekarza',
	Physicians: 'Lekarze',
	Physiotherapist: 'Fizjoterapeuta',
	PlaceOfService: 'Miejsce świadczenia usług',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Raport',
	PlanId: 'Identyfikator planu',
	PlansAndReportsCategoryDescription: 'Do planowania leczenia i podsumowywania wyników',
	PleaseRefreshThisPageToTryAgain: 'Proszę odświeżyć tę stronę, aby spróbować ponownie.',
	PleaseWait: 'Proszę czekać...',
	PleaseWaitForHostToJoin: 'Oczekiwanie na dołączenie Gospodarza...',
	PleaseWaitForHostToStart: 'Proszę czekać, aż Gospodarz rozpocznie spotkanie.',
	PlusAdd: '+ Dodaj',
	PlusOthers: '+{count} innych',
	PlusPlanInclusionFive: 'Wspólne skrzynki odbiorcze',
	PlusPlanInclusionFour: 'Grupowe połączenia wideo',
	PlusPlanInclusionHeader: 'Wszystko w Essential  ',
	PlusPlanInclusionOne: 'Nieograniczona sztuczna inteligencja',
	PlusPlanInclusionSix: 'Niestandardowe brandingi',
	PlusPlanInclusionThree: 'Planowanie grupowe',
	PlusPlanInclusionTwo: 'Nieograniczone miejsce do przechowywania ',
	PlusSubscriptionPlanSubtitle: 'Aby praktyki mogły być optymalizowane i rozwijane',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Policjant',
	PolicyDates: 'Daty polityki',
	PolicyHolder: 'Posiadacz polisy',
	PolicyHoldersAddress: 'Adres ubezpieczonego',
	PolicyMemberId: 'Numer Identyfikacyjny Uczestnika Polisy',
	PolicyStatus: 'Status polityki',
	Popular: 'Popularny',
	PortalAccess: 'Dostęp do portalu',
	PortalNoAppointmentsHeading: 'Śledź wszystkie nadchodzące i minione spotkania',
	PortalNoDocumentationHeading: 'Bezpiecznie twórz i przechowuj swoje dokumenty',
	PortalNoRelationshipsHeading: 'Zbierz tych, którzy wspierają Twoją podróż',
	PosCodeErrorMessage: 'Kod POS jest wymagany',
	PosoNumber: 'Numer zamówienia/zlecenia',
	PossibleClientDuplicate: 'Możliwy duplikat klienta',
	PotentialClientDuplicateTitle: 'Potencjalny duplikat rekordu klienta',
	PotentialClientDuplicateWarning:
		'Te informacje o kliencie mogą już znajdować się na liście klientów. W razie potrzeby zweryfikuj i zaktualizuj istniejący rekord lub kontynuuj tworzenie nowego klienta.',
	PoweredBy: 'Zasilane przez',
	Practice: 'Praktyka',
	PracticeDetails: 'Szczegóły dotyczące praktyki',
	PracticeInfoHeader: 'Informacje biznesowe',
	PracticeInfoPlaceholder: `Nazwa praktyki,
 Krajowy identyfikator dostawcy,
 Numer identyfikacyjny pracodawcy`,
	PracticeLocation: 'Wygląda na to, że Twoja praktyka jest w',
	PracticeSettingsAvailabilityTab: 'Dostępność',
	PracticeSettingsBillingTab: 'Ustawienia rozliczeń',
	PracticeSettingsClientSettingsTab: 'Ustawienia klienta',
	PracticeSettingsGeneralTab: 'Ogólny',
	PracticeSettingsOnlineBookingTab: 'Rezerwacja online',
	PracticeSettingsServicesTab: 'Usługi',
	PracticeSettingsTaxRatesTab: 'Stawki podatkowe',
	PracticeTemplate: 'Szablon ćwiczeń',
	Practitioner: 'Praktykujący',
	PreferredLanguage: 'Preferowany język',
	PreferredName: 'Preferowana nazwa',
	Prescription: 'Recepta',
	PreventionSpecialist: 'Specjalista ds. zapobiegania',
	Preview: 'Zapowiedź',
	PreviewAndSend: 'Podgląd i wysłanie',
	PreviewUnavailable: 'Podgląd niedostępny dla tego typu pliku',
	PreviousNotes: 'Poprzednie notatki',
	Price: 'Cena',
	PriceError: 'Cena musi być większa niż 0',
	PricePerClient: 'Cena za klienta',
	PricePerUser: 'Na użytkownika',
	PricePerUserBilledAnnually: 'Za użytkownika, rozliczane rocznie',
	PricePerUserPerPeriod: '{price} za użytkownika / {isMonthly, select, true {miesiąc} other {rok}}',
	PricingGuide: 'Przewodnik po planach cenowych',
	PricingPlanPerMonth: '/ miesiąc',
	PricingPlanPerYear: '/ rok',
	Primary: 'Podstawowy',
	PrimaryInsurance: 'Ubezpieczenie podstawowe',
	PrimaryPolicy: 'Ubezpieczenie podstawowe',
	PrimaryTimezone: 'Główna strefa czasowa',
	Print: 'Wydrukować',
	PrintToCms1500: 'Drukuj do CMS1500',
	PrivatePracticeConsultant: 'Konsultant ds. praktyki prywatnej',
	Proceed: 'Przejdź',
	ProcessAtTimeOfBookingDesc: 'Klienci muszą zapłacić pełną cenę usługi, aby dokonać rezerwacji online',
	ProcessAtTimeOfBookingLabel: 'Przetwarzaj płatności w momencie rezerwacji',
	Processing: 'Przetwarzanie',
	ProcessingFee: 'Opłata za przetwarzanie',
	ProcessingFeeToolTip: `Carepatron umożliwia obciążanie klientów opłatami za przetwarzanie.
 W niektórych jurysdykcjach pobieranie opłat za przetwarzanie od klientów jest zabronione. Twoim obowiązkiem jest przestrzeganie obowiązujących przepisów.`,
	ProcessingRequest: 'Przetwarzanie żądania...',
	Product: 'Produkt',
	Profession: 'Zawód',
	ProfessionExample: 'Terapeuta, Dietetyk, Dentysta',
	ProfessionPlaceholder: 'Zacznij wpisywać swój zawód lub wybierz z listy',
	ProfessionalPlanInclusion1: 'Nieograniczone miejsce do przechowywania',
	ProfessionalPlanInclusion2: 'Nieograniczone zadania',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Obsługa klienta 24/7',
	ProfessionalPlanInclusion5: 'Przypomnienia SMS',
	ProfessionalPlanInclusionHeader: 'Wszystko, co w wersji Starter, plus...',
	Professions: 'Zawody',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: 'Limit rozmiaru pliku 5 MB',
	ProfilePopoverSubTitle: 'Jesteś zalogowany jako <strong>{email}</strong>',
	ProfilePopoverTitle: 'Twoje miejsca pracy',
	PromoCode: 'Kod promocyjny',
	PromotionCodeApplied: '{promo} zastosowano',
	ProposeNewDateTime: 'Zaproponuj nową datę/godzinę',
	Prosthetist: 'Protetyk',
	Provider: 'Dostawca',
	ProviderBillingPlanExpansionManageButton: 'Zarządzaj planem',
	ProviderCommercialNumber: 'Numer komercyjny dostawcy',
	ProviderDetails: 'Szczegóły dostawcy',
	ProviderDetailsAddress: 'Adres',
	ProviderDetailsName: 'Nazwa',
	ProviderDetailsPhoneNumber: 'Numer telefonu',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Przepraszamy, ten dostawca ma już istniejące konto rozliczeniowe',
	ProviderInfoPlaceholder: `Imię i nazwisko pracownika,
 Adres e-mail,
 Numer telefonu,
 Krajowy identyfikator dostawcy,
 Numer licencji`,
	ProviderIsChargedProcessingFee: 'Zapłacisz opłatę za przetwarzanie',
	ProviderPaymentFormBackButton: 'Z powrotem',
	ProviderPaymentFormBillingAddressCity: 'Miasto',
	ProviderPaymentFormBillingAddressCountry: 'Kraj',
	ProviderPaymentFormBillingAddressLine1: 'Wiersz 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Kod pocztowy',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Dane karty kredytowej',
	ProviderPaymentFormCardExpiry: 'Wygaśnięcie',
	ProviderPaymentFormCardHolderAddressTitle: 'Adres',
	ProviderPaymentFormCardHolderName: 'Imię i nazwisko posiadacza karty',
	ProviderPaymentFormCardHolderTitle: 'Dane posiadacza karty',
	ProviderPaymentFormCardNumber: 'Numer karty',
	ProviderPaymentFormPlanTitle: 'Wybrany plan',
	ProviderPaymentFormPlanTotalTitle: 'Razem ({currency}):',
	ProviderPaymentFormSaveButton: 'Zapisz subskrypcję',
	ProviderPaymentFreePlanDescription:
		'Wybór bezpłatnego planu spowoduje usunięcie dostępu każdego członka personelu do klientów u Twojego dostawcy. Jednak Twój dostęp pozostanie i nadal będziesz mógł korzystać z platformy.',
	ProviderPaymentStepName: 'Recenzja ',
	ProviderPaymentSuccessSnackbar: 'Świetnie! Twój nowy plan został pomyślnie zapisany.',
	ProviderPaymentTitle: 'Recenzja ',
	ProviderPlanNetworkIdentificationNumber: 'Numer identyfikacyjny sieci planu dostawcy',
	ProviderRemindersSettingsBannerAction: 'Przejdź do Zarządzania Przepływem Pracy',
	ProviderRemindersSettingsBannerDescription:
		'Znajdź wszystkie przypomnienia w nowej zakładce **Zarządzanie przepływami pracy** w **Ustawieniach**. Ta aktualizacja wprowadza nowe funkcje, ulepszone szablony i inteligentniejsze narzędzia automatyzacji, aby zwiększyć Twoją produktywność. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Twoje doświadczenie z przypomnieniami staje się lepsze',
	ProviderTaxonomy: 'Taksonomia dostawców',
	ProviderUPINNumber: 'Numer UPIN dostawcy',
	ProviderUsedStoragePercentage: '{providerName} magazyn jest w {usedStoragePercentage}% zapełniony!',
	PsychiatricNursePractitioner: 'Pielęgniarka psychiatryczna',
	Psychiatrist: 'Psychiatra',
	Psychiatrists: 'Psychiatrzy',
	Psychiatry: 'Psychiatria',
	Psychoanalyst: 'Psychoanalityk',
	Psychologist: 'Psycholog',
	Psychologists: 'Psychologowie',
	Psychology: 'Psychologia',
	Psychometrician: 'Psychometryk',
	PsychosocialRehabilitationSpecialist: 'Specjalista ds. rehabilitacji psychospołecznej',
	Psychotheraphy: 'Psychoterapia',
	Psychotherapists: 'Psychoterapeuci',
	Psychotherapy: 'Psychoterapia',
	PublicCallDialogTitle: 'Rozmowa wideo z ',
	PublicCallDialogTitlePlaceholder: 'Rozmowa wideo obsługiwana przez Carepatron',
	PublicFormBackToForm: 'Prześlij inną odpowiedź',
	PublicFormConfirmSubmissionHeader: 'Potwierdź przesłanie',
	PublicFormNotFoundDescription:
		'Formularz, którego szukasz, mógł zostać usunięty lub link może być nieprawidłowy. Sprawdź adres URL i spróbuj ponownie.',
	PublicFormNotFoundTitle: 'Formularz nie został znaleziony',
	PublicFormSubmissionError: 'Przesłanie nie powiodło się. Spróbuj ponownie.',
	PublicFormSubmissionSuccess: 'Formularz został pomyślnie wysłany',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} przesłał/a formularz publiczny {noteTitle}',
	PublicFormSubmittedSubtitle: 'Twoje zgłoszenie zostało otrzymane.',
	PublicFormSubmittedTitle: 'Dziękuję!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Wysłaliśmy kod potwierdzający na Twój adres email',
	PublicFormsInvalidConfirmationCode: 'Nieprawidłowy kod potwierdzenia',
	PublicHealthInspector: 'Inspektor Zdrowia Publicznego',
	PublicTemplates: 'Szablony publiczne',
	Publish: 'Publikować',
	PublishTemplate: 'Opublikuj szablon',
	PublishTemplateFeatureBannerSubheader: 'Szablony zaprojektowane z myślą o korzyściach dla społeczności',
	PublishTemplateHeader: 'Opublikuj {title}',
	PublishTemplateToCommunity: 'Opublikuj szablon w społeczności',
	PublishToCommunity: 'Publikuj w społeczności',
	PublishToCommunitySuccessMessage: 'Pomyślnie opublikowano w społeczności',
	Published: 'Opublikowany',
	PublishedBy: 'Opublikowane przez {name}',
	PublishedNotesAreNotAutosaved: 'Opublikowane notatki nie zostaną automatycznie zapisane',
	PublishedOnCarepatronCommunity: 'Opublikowano w społeczności Carepatron',
	Purchase: 'Zakup',
	PushToCalendar: 'Przenieś do kalendarza',
	Question: 'Pytanie',
	QuestionOrTitle: 'Pytanie lub tytuł',
	QuickActions: 'Szybkie akcje',
	QuickThemeSwitcherColorBasil: 'Bazylia',
	QuickThemeSwitcherColorBlueberry: 'Borówka',
	QuickThemeSwitcherColorFushcia: 'Fuksja',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Mech',
	QuickThemeSwitcherColorRose: 'Róża',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Radioterapia',
	Radiologist: 'Radiolog',
	Read: 'Czytać',
	ReadOnly: 'Tylko do odczytu',
	ReadOnlyAppointment: 'Wizyta tylko do odczytu',
	ReadOnlyEventBanner: 'To spotkanie jest zsynchronizowane z kalendarza tylko do odczytu i nie można go edytować.',
	ReaderMaxDepthHasBeenExceededCode: 'Notatka jest zbyt zagnieżdżona. Spróbuj usunąć wcięcie niektórych elementów.',
	ReadyForMapping: 'Gotowy do mapowania',
	RealEstateAgent: 'Agent nieruchomości',
	RearrangeClientFields: 'Zmień kolejność pól klienta w ustawieniach klienta',
	Reason: 'Powód',
	ReasonForChange: 'Powód zmiany',
	RecentAppointments: 'Ostatnie nominacje',
	RecentServices: 'Usługi ostatnio dodane',
	RecentTemplates: 'Ostatnie szablony',
	RecentlyUsed: 'Ostatnio używane',
	Recommended: 'Polecane',
	RecommendedTemplates: 'Polecane szablony',
	Recording: 'Nagranie',
	RecordingEnded: 'Nagrywanie zakończone',
	RecordingInProgress: 'Nagrywanie w toku',
	RecordingMicrophoneAccessErrorMessage:
		'Aby rozpocząć nagrywanie, zezwól na dostęp do mikrofonu w przeglądarce i odśwież stronę.',
	RecurrenceCount: ', {count, plural, one {raz} other {# razy}}',
	RecurrenceDaily: '{count, plural, one {Codziennie} other {Dni}}',
	RecurrenceEndAfter: 'Po',
	RecurrenceEndNever: 'Nigdy',
	RecurrenceEndOn: 'Na',
	RecurrenceEvery: 'Każde {description}',
	RecurrenceMonthly: '{count, plural, one {Miesięcznie} other {Miesięcy}}',
	RecurrenceOn: 'w {description}',
	RecurrenceOnAllDays: 'we wszystkich dniach',
	RecurrenceUntil: 'dopóki {description}',
	RecurrenceWeekly: '{count, plural, one {Tygodniowo} other {Tygodni}}',
	RecurrenceYearly: '{count, plural, one {Rocznie} other {Lata}}',
	Recurring: 'Powtarzający się',
	RecurringAppointment: 'Powtarzające się spotkanie',
	RecurringAppointmentsLimitedBannerText:
		'Nie wszystkie powtarzające się spotkania są wyświetlane. Zmniejsz zakres dat, aby zobaczyć wszystkie powtarzające się spotkania w danym okresie.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# wydarzenie} other {# wydarzeń}}</b> zostanie utworzone w następujących datach',
	Redo: 'Przerobić',
	ReferFriends: 'Poleć znajomym',
	Reference: 'Odniesienie',
	ReferralCreditedNotificationSubject: 'Twój kredyt polecający w wysokości {currency} {amount} został zastosowany',
	ReferralEmailDefaultBody: `Dzięki {name}, otrzymasz bezpłatny 3-miesięczny upgrade do Carepatron. Dołącz do naszej społeczności ponad 3 milionów pracowników służby zdrowia stworzonej z myślą o nowym sposobie pracy!
Dzięki,
Zespół Carepatron`,
	ReferralEmailDefaultSubject: 'Zostałeś zaproszony do dołączenia do Carepatron',
	ReferralHasNotSignedUpDescription: 'Twój znajomy jeszcze się nie zapisał',
	ReferralHasSignedUpDescription: 'Twój znajomy się zapisał.',
	ReferralInformation: 'Informacje o skierowaniu',
	ReferralJoinedNotificationSubject: '{actorProfileName} dołączył(a) do Carepatron',
	ReferralListErrorDescription: 'Nie udało się załadować listy poleceń.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {miesiąc} other {miesięcy}}</b> aktywny',
	ReferralRewardBanner: 'Zarejestruj się i odbierz nagrodę za polecenie!',
	Referrals: 'Polecenia',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} miesiąc {percentOff, select, 100 {bezpłatny płatny} other {{percentOff}% zniżki}} {type, select, SubscriptionUpgrade {aktualizacji} other {}}',
	ReferredUserBenefitTitle: 'Oni dostają!',
	Referrer: 'Polecający',
	ReferringProvider: 'Dostawca polecający',
	ReferringUserBenefitSubtitle: 'Kredyt w wysokości USD${creditAmount} po aktywacji przez <mark>3 znajomych</mark>.',
	ReferringUserBenefitTitle: 'Dostajesz!',
	RefreshPage: 'Odśwież stronę',
	Refund: 'Refundacja',
	RefundAcknowledgement: 'Zwróciłem {clientName} pieniądze poza systemem Carepatron.',
	RefundAcknowledgementValidationMessage: 'Potwierdź, że dokonałeś zwrotu tej kwoty',
	RefundAmount: 'Kwota zwrotu',
	RefundContent:
		'Zwroty pojawiają się na koncie klienta w ciągu 7-10 dni. Opłaty za płatność nie podlegają zwrotowi, ale nie ma żadnych dodatkowych opłat za zwroty. Zwrotów nie można anulować, a niektóre mogą wymagać przeglądu przed przetworzeniem.',
	RefundCouldNotBeProcessed: 'Zwrotu nie można było przetworzyć',
	RefundError:
		'Ten zwrot nie może być w tej chwili automatycznie przetworzony. Skontaktuj się z pomocą techniczną Carepatron, aby poprosić o zwrot tej płatności.',
	RefundExceedTotalValidationError: 'Kwota nie może przekroczyć całkowitej kwoty zapłaconej',
	RefundFailed: 'Zwrot nie powiódł się',
	RefundFailedTooltip:
		'Zwrot tej płatności nie powiódł się i nie można go ponowić. Skontaktuj się z pomocą techniczną.',
	RefundNonStripePaymentContent:
		'Ta płatność została dokonana przy użyciu metody spoza Carepatron (np. gotówka, bankowość internetowa). Wydanie zwrotu w Carepatron nie spowoduje zwrotu środków klientowi.',
	RefundReasonDescription: 'Dodanie powodu zwrotu może być pomocne podczas przeglądania transakcji klientów',
	Refunded: 'Zwrócono',
	Refunds: 'Zwroty',
	RefundsTableEmptyState: 'Nie znaleziono zwrotów',
	Regenerate: 'Odtwórz',
	RegisterButton: 'Rejestr',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Imię',
	RegisterLastName: 'Nazwisko',
	RegisterPassword: 'Hasło',
	RegisteredNurse: 'Pielęgniarka dyplomowana',
	RehabilitationCounselor: 'Doradca ds. rehabilitacji',
	RejectAppointmentFormTitle: 'Nie możesz przyjść? Proszę daj nam znać dlaczego i zaproponuj nowy termin.',
	Rejected: 'Odrzucony',
	Relationship: 'Relacja',
	RelationshipDetails: 'Szczegóły związku',
	RelationshipEmptyStateTitle: 'Pozostań w kontakcie z osobami wspierającymi Twojego klienta',
	RelationshipPageAccessTypeColumnName: 'Dostęp do profilu',
	RelationshipSavedSuccessSnackbar: 'Związek został pomyślnie zapisany!',
	RelationshipSelectorFamilyAdmin: 'Rodzina',
	RelationshipSelectorFamilyMember: 'Członek rodziny',
	RelationshipSelectorProviderAdmin: 'Administrator dostawcy',
	RelationshipSelectorProviderStaff: 'Personel dostawcy',
	RelationshipSelectorSupportNetworkPrimary: 'Przyjaciel',
	RelationshipSelectorSupportNetworkSecondary: 'Sieć wsparcia',
	RelationshipStatus: 'Status związku',
	RelationshipType: 'Typ relacji',
	RelationshipTypeClientOwner: 'Klient',
	RelationshipTypeFamilyAdmin: 'Relacje',
	RelationshipTypeFamilyMember: 'Rodzina',
	RelationshipTypeFriendOrSupport: 'Sieć przyjaciół lub wsparcia',
	RelationshipTypeProviderAdmin: 'Administrator dostawcy',
	RelationshipTypeProviderStaff: 'Personel',
	RelationshipTypeSelectorPlaceholder: 'Wyszukaj typy relacji',
	Relationships: 'Relacje',
	Remaining: 'pozostały',
	RemainingTime: '{time} pozostało',
	Reminder: 'Przypomnienie',
	ReminderColor: 'Kolor przypomnienia',
	ReminderDetails: 'Szczegóły przypomnienia',
	ReminderEditDisclaimer: 'Zmiany zostaną uwzględnione wyłącznie w przypadku nowych nominacji',
	ReminderSettings: 'Ustawienia przypomnień o spotkaniach',
	Reminders: 'Przypomnienia',
	Remove: 'Usunąć',
	RemoveAccess: 'Usuń dostęp',
	RemoveAllGuidesBtn: 'Usuń wszystkie prowadnice',
	RemoveAllGuidesPopoverBody:
		'Po zakończeniu korzystania z przewodników wprowadzających wystarczy użyć przycisku Usuń przewodniki na każdym panelu.',
	RemoveAllGuidesPopoverTitle: 'Nie potrzebujesz już przewodników wprowadzających?',
	RemoveAsDefault: 'Usuń jako domyślne',
	RemoveAsIntake: 'Usuń jako wlot',
	RemoveCol: 'Usuń kolumnę',
	RemoveColor: 'Usuń kolor',
	RemoveField: 'Usuń pole',
	RemoveFromCall: 'Usuń z rozmowy',
	RemoveFromCallDescription: 'Czy na pewno chcesz usunąć <mark>{attendeeName}</mark> z tej wideorozmowy?',
	RemoveFromCollection: 'Usuń z kolekcji',
	RemoveFromCommunity: 'Usuń ze społeczności',
	RemoveFromFolder: 'Usuń z folderu',
	RemoveFromFolderConfirmationDescription:
		'Czy na pewno chcesz usunąć ten szablon z tego folderu? Tej akcji nie można cofnąć, ale możesz przenieść go z powrotem później.',
	RemoveFromIntakeDefault: 'Usuń z domyślnego pobierania',
	RemoveGuides: 'Usuń prowadnice',
	RemoveMfaConfirmationDescription:
		'Usunięcie uwierzytelniania wieloskładnikowego (MFA) obniży bezpieczeństwo Twojego konta. Czy chcesz kontynuować?',
	RemoveMfaConfirmationTitle: 'Usunąć MFA?',
	RemovePaymentMethodDescription: `Spowoduje to utratę dostępu i uniemożliwienie dalszego korzystania z tej metody płatności.
 Tej czynności nie można cofnąć.`,
	RemoveRow: 'Usuń wiersz',
	RemoveTable: 'Usuń tabelę',
	RemoveTemplateAsDefaultIntakeSuccess: 'Pomyślnie usunięto {templateTitle} jako domyślny szablon przyjmowania',
	RemoveTemplateFromCommunity: 'Usuń szablon ze społeczności',
	RemoveTemplateFromFolder: '{templateTitle} pomyślnie usunięty z {folderTitle}',
	Rename: 'Przemianować',
	RenderingProvider: 'Dostawca renderowania',
	Reopen: 'Otworzyć na nowo',
	ReorderServiceGroupFailure: 'Nie udało się ponownie zamówić kolekcji',
	ReorderServiceGroupSuccess: 'Pomyślnie uporządkowano kolekcję',
	ReorderServicesFailure: 'Nie udało się ponownie zamówić usług',
	ReorderServicesSuccess: 'Usługi zostały pomyślnie zamówione ponownie',
	ReorderYourServiceList: 'Zmień kolejność listy usług',
	ReorderYourServiceListDescription:
		'Sposób organizacji usług i kolekcji będzie widoczny na Twojej stronie rezerwacji online i widoczny dla wszystkich Twoich klientów!',
	RepeatEvery: 'Powtarzaj co',
	RepeatOn: 'Powtórz na',
	Repeating: 'Powtórzenie',
	Repeats: 'Powtarza się',
	RepeatsEvery: 'Powtarza się co',
	Rephrase: 'Przeformułuj',
	Replace: 'Zastępować',
	ReplaceBackground: 'Zamień tło',
	ReplacementOfPriorClaim: 'Wymiana wcześniejszego roszczenia',
	Report: 'Raport',
	Reprocess: 'Przetworzyć ponownie',
	RepublishTemplateToCommunity: 'Opublikuj szablon w społeczności',
	RequestANewVerificationLink: 'Poproś o nowy link weryfikacyjny',
	RequestCoverageReport: 'Poproś o raport dotyczący zasięgu',
	RequestingDevicePermissions: 'Prośba o uprawnienia urządzenia...',
	RequirePaymentMethodDesc: 'Aby dokonać rezerwacji online, klienci muszą podać dane swojej karty kredytowej',
	RequirePaymentMethodLabel: 'Wymagaj danych karty kredytowej',
	Required: 'wymagany',
	RequiredField: 'Wymagany',
	RequiredUrl: 'Adres URL jest wymagany.',
	Reschedule: 'Przełożyć',
	RescheduleBookingLinkModalDescription: 'Twój klient może zmienić datę i godzinę wizyty korzystając z tego łącza.',
	RescheduleBookingLinkModalTitle: 'Link do zmiany terminu rezerwacji',
	RescheduleLink: 'Link do zmiany terminu',
	Resend: 'Wyślij ponownie',
	ResendConfirmationCode: 'Wyślij ponownie kod potwierdzający',
	ResendConfirmationCodeDescription: 'Proszę wpisać swój adres e-mail, a my wyślemy Ci kolejny kod potwierdzający',
	ResendConfirmationCodeSuccess: 'Kod potwierdzający został wysłany ponownie, sprawdź skrzynkę odbiorczą',
	ResendNewEmailVerificationSuccess: 'Nowy link weryfikacyjny został wysłany na adres {email}',
	ResendVerificationEmail: 'Wyślij ponownie e-mail weryfikacyjny',
	Reset: 'Nastawić',
	Resources: 'Zasoby',
	RespiratoryTherapist: 'Terapeuta oddechowy',
	RespondToHistoricAppointmentError:
		'Jest to wizyta historyczna. Jeśli masz pytania, skontaktuj się ze swoim lekarzem.',
	Responder: 'Osoba odpowiadająca',
	RestorableItemModalDescription:
		'Czy na pewno chcesz usunąć {context}?{canRestore, select, true { Możesz go przywrócić później.} other {}}',
	RestorableItemModalTitle: 'Usuń {type}',
	Restore: 'Przywrócić',
	RestoreAll: 'Przywróć wszystko',
	Restricted: 'Ograniczony',
	ResubmissionCodeReferenceNumber: 'Kod ponownego zgłoszenia i numer referencyjny',
	Resubmit: 'Prześlij ponownie',
	Resume: 'Wznawiać',
	Retry: 'Spróbować ponownie',
	RetryingConnectionAttempt: 'Ponawianie połączenia... (Próba {retryCount} z {maxRetries})',
	ReturnToForm: 'Powrót do formularza',
	RevertClaimStatus: 'Cofnij status roszczenia',
	RevertClaimStatusDescriptionBody:
		'To roszczenie ma powiązane płatności, a zmiana statusu może wpłynąć na śledzenie lub przetwarzanie płatności, co może wymagać ręcznego uzgodnienia.',
	RevertClaimStatusDescriptionTitle: 'Czy na pewno chcesz wrócić do {status}?',
	RevertClaimStatusError: 'Nie udało się cofnąć statusu roszczenia',
	RevertToDraft: 'Wróć do wersji roboczej',
	Review: 'Recenzja',
	ReviewsFirstQuote: 'Wizyty w dowolnym miejscu i czasie',
	ReviewsSecondJobTitle: 'Klinika Lifehouse',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote: 'Uwielbiam również aplikację carepatron. Pomaga mi śledzić moich klientów i pracę w podróży.',
	ReviewsThirdJobTitle: 'Klinika w zatoce Manila',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'Łatwa nawigacja i piękny interfejs użytkownika wywołują uśmiech na mojej twarzy każdego dnia.',
	RightAlign: 'Wyrównanie do prawej',
	Role: 'Rola',
	Roster: 'Uczestnicy',
	RunInBackground: 'Uruchom w tle',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'Numer ubezpieczenia społecznego',
	SafetyRedirectHeading: 'Opuszczasz Carepatron',
	SafetyRedirectSubtext: 'Jeśli ufasz temu linkowi, wybierz go, aby kontynuować',
	SalesRepresentative: 'Przedstawiciel handlowy',
	SalesTax: 'Podatek od sprzedaży',
	SalesTaxHelp: 'Zawiera podatek od sprzedaży na wygenerowanych fakturach',
	SalesTaxIncluded: 'Tak',
	SalesTaxNotIncluded: 'NIE',
	SaoPaulo: 'San Paulo',
	Saturday: 'Sobota',
	Save: 'Ratować',
	SaveAndClose: 'Ratować ',
	SaveAndExit: 'Ratować ',
	SaveAndLock: 'Zapisz i zablokuj',
	SaveAsDraft: 'Zapisz jako wersję roboczą',
	SaveCardForFuturePayments: 'Zapisz kartę na przyszłe płatności',
	SaveChanges: 'Zapisz zmiany',
	SaveCollection: 'Zapisz kolekcję',
	SaveField: 'Zapisz pole',
	SavePaymentMethod: 'Zapisz metodę płatności',
	SavePaymentMethodDescription: 'Opłata nie zostanie pobrana aż do pierwszej wizyty.',
	SavePaymentMethodSetupError: 'Wystąpił nieoczekiwany błąd i w tej chwili nie możemy skonfigurować płatności.',
	SavePaymentMethodSetupInvoiceLater: 'Płatności można skonfigurować i zapisać podczas opłacania pierwszej faktury.',
	SaveSection: 'Zapisz sekcję',
	SaveService: 'Utwórz nową usługę',
	SaveTemplate: 'Zapisz szablon',
	Saved: 'Zapisane',
	SavedCards: 'Zapisane karty',
	SavedPaymentMethods: 'Zapisane',
	Saving: 'Oszczędność...',
	ScheduleAppointmentsAndOnlineServices: 'Zaplanuj spotkania i usługi online',
	ScheduleName: 'Nazwa harmonogramu',
	ScheduleNew: 'Zaplanuj nowy',
	ScheduleSend: 'Zaplanuj wysłanie',
	ScheduleSendAlertInfo: 'Zaplanowane konwersacje zostaną wysłane w zaplanowanym czasie.',
	ScheduleSendByName: '**Planowanie wysyłki** • {time} przez {displayName}',
	ScheduleSetupCall: 'Zaplanuj rozmowę wprowadzającą',
	Scheduled: 'Zaplanowany',
	SchedulingSend: 'Planowanie wysyłania',
	School: 'Szkoła',
	ScrollToTop: 'Przewiń do góry',
	Search: 'Szukaj',
	SearchAndConvertToLanguage: 'Szukaj i konwertuj na język',
	SearchBasicBlocks: 'Wyszukaj podstawowe bloki',
	SearchByName: 'Szukaj według nazwy',
	SearchClaims: 'Szukaj roszczeń',
	SearchClientFields: 'Wyszukaj pola klienta',
	SearchClients: 'Wyszukaj według nazwy klienta, identyfikatora klienta lub numeru telefonu',
	SearchCommandNotFound: 'Brak wyników dla "{searchTerm}"',
	SearchContacts: 'Klient lub kontakt',
	SearchContactsPlaceholder: 'Wyszukaj kontakty',
	SearchConversations: 'Wyszukaj konwersacje',
	SearchInputPlaceholder: 'Przeszukaj wszystkie zasoby',
	SearchInvoiceNumber: 'Wyszukaj numer faktury',
	SearchInvoices: 'Wyszukaj faktury',
	SearchMultipleContacts: 'Klienci lub kontakty',
	SearchMultipleContactsOptional: 'Klienci lub kontakty (opcjonalnie)',
	SearchOrCreateATag: 'Wyszukaj lub utwórz tag',
	SearchPayments: 'Wyszukaj płatności',
	SearchPrepopulatedData: 'Przeszukaj wstępnie wypełnione pola danych',
	SearchRelationships: 'Wyszukaj relacje',
	SearchRemindersAndWorkflows: 'Wyszukaj przypomnienia i przepływy pracy',
	SearchServices: 'Wyszukiwanie usług',
	SearchTags: 'Szukaj tagów',
	SearchTeamMembers: 'Szukaj członków zespołu',
	SearchTemplatePlaceholder: 'Szukaj {templateCount}+ zasobów',
	SearchTimezone: 'Wyszukaj strefę czasową...',
	SearchTrashItems: 'Wyszukaj elementy',
	SearchUnsplashPlaceholder: 'Wyszukaj bezpłatne zdjęcia w wysokiej rozdzielczości na Unsplash',
	Secondary: 'Wtórny',
	SecondaryInsurance: 'Ubezpieczenie dodatkowe',
	SecondaryPolicy: 'Ubezpieczenie dodatkowe',
	SecondaryTimezone: 'Druga strefa czasowa',
	Secondly: 'Po drugie',
	Section: 'Sekcja',
	SectionCannotBeEmpty: 'Sekcja musi mieć co najmniej jeden wiersz',
	SectionFormSecondaryText: 'Tytuł i opis sekcji',
	SectionName: 'Nazwa sekcji',
	Sections: 'Sekcje',
	SeeLess: 'Zobacz mniej',
	SeeLessUpcomingAppointments: 'Zobacz mniej nadchodzących spotkań',
	SeeMore: 'Zobacz więcej',
	SeeMoreUpcomingAppointments: 'Zobacz więcej nadchodzących spotkań',
	SeeTemplateLibrary: 'Zobacz bibliotekę szablonów',
	Seen: 'Widziany',
	SeenByName: '<strong>Widziane</strong> • {time} przez {displayName}',
	SelectAll: 'Zaznacz wszystko',
	SelectAssignees: 'Wybierz osoby przypisane',
	SelectAttendees: 'Wybierz uczestników',
	SelectCollection: 'Wybierz kolekcję',
	SelectCorrespondingAttributes: 'Wybierz odpowiednie atrybuty',
	SelectPayers: 'Wybierz płatników',
	SelectProfile: 'Wybierz profil',
	SelectServices: 'Wybierz usługi',
	SelectTags: 'Wybierz Tagi',
	SelectTeamOrCommunity: 'Wybierz drużynę lub społeczność',
	SelectTemplate: 'Wybierz szablon',
	SelectType: 'Wybierz typ',
	Selected: 'Wybrany',
	SelfPay: 'Samozapłać',
	Send: 'Wysłać',
	SendAndClose: 'Wysłać ',
	SendAndStopIgnore: 'Wyślij i przestań ignorować',
	SendEmail: 'Wyślij e-mail',
	SendIntake: 'Wyślij przyjęcie',
	SendIntakeAndForms: 'Wyślij przyjęcie ',
	SendMeACopy: 'Wyślij mi kopię',
	SendNotificationEmailWarning:
		'Niektórzy uczestnicy nie mają adresu e-mail i nie otrzymają automatycznych powiadomień i przypomnień.',
	SendNotificationLabel: 'Wybierz uczestników, którzy mają otrzymać powiadomienie e-mailem',
	SendOnlinePayment: 'Wyślij płatność online',
	SendOnlinePaymentTooltipTitleAdmin: 'Proszę dodać preferowane ustawienia wypłaty',
	SendOnlinePaymentTooltipTitleStaff: 'Poproś właściciela dostawcy o skonfigurowanie płatności online.',
	SendPaymentLink: 'Wyślij link do płatności',
	SendReaction: 'Wyślij reakcję',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Wyślij e-mail weryfikacyjny',
	SendingFailed: 'Wysyłanie nie powiodło się',
	Sent: 'Wysłano',
	SentByName: '**Wysłano** • {time} przez {displayName}',
	Seoul: 'Seul',
	SeparateDuplicateClientsDescription:
		'Wybrane rekordy klientów pozostaną oddzielne od pozostałych, chyba że zdecydujesz się je połączyć',
	Service: 'Praca',
	'Service/s': 'Usługa/y',
	ServiceAdjustment: 'Dostosowanie usługi',
	ServiceAllowNewClientsIndicator: 'Zezwalaj nowym klientom',
	ServiceAlreadyExistsInCollection: 'Usługa już istnieje w kolekcji',
	ServiceBookableOnlineIndicator: 'Możliwość rezerwacji online',
	ServiceCode: 'Kod',
	ServiceCodeErrorMessage: 'Wymagany jest kod serwisowy',
	ServiceCodeSelectorPlaceholder: 'Dodaj kod usługi',
	ServiceColour: 'Kolor usługi',
	ServiceCoverageDescription: 'Wybierz kwalifikujące się usługi i zapłać współpłatę za tę polisę ubezpieczeniową.',
	ServiceCoverageGoToServices: 'Przejdź do usług',
	ServiceCoverageNoServicesDescription:
		'Dostosuj kwoty współpłacenia za usługę, aby zastąpić domyślną współpłacenie polisy. Wyłącz ochronę, aby zapobiec roszczeniu usług w ramach polisy.',
	ServiceCoverageNoServicesLabel: 'Nie znaleziono żadnych usług.',
	ServiceCoverageTitle: 'Zakres usług',
	ServiceDate: 'Data usługi',
	ServiceDetails: 'Szczegóły usługi',
	ServiceDuration: 'Czas trwania',
	ServiceEmptyState: 'Nie ma jeszcze żadnych usług',
	ServiceErrorMessage: 'Wymagana jest usługa',
	ServiceFacility: 'Obiekt usługowy',
	ServiceName: 'Nazwa usługi',
	ServiceRate: 'Wskaźnik',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} dla {serviceReceiptNumber, select, undefined {użytkownika} other {{clientName}}} wymaga dodatkowych informacji',
	ServiceSalesTax: 'Podatek od sprzedaży',
	ServiceType: 'Praca',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Naciśnij przycisk odświeżenia, aby odświeżyć i otrzymać najnowsze aktualizacje Carepatron.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Przeładować',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Używasz starszej wersji',
	ServiceWorkerForceUIUpdateDialogTitle: 'Witamy ponownie!',
	Services: 'Usługi',
	ServicesAndAvailability: 'Usługi ',
	ServicesAndDiagnosisCodesHeader: 'Dodaj usługi i kody diagnostyczne',
	ServicesCount: '{count,plural,=0{Usługi}one{Usługa}other{Usługi}}',
	ServicesPlaceholder: 'Usługi',
	ServicesProvidedBy: 'Usługa/y świadczone przez',
	SetAPhysicalAddress: 'Ustaw adres fizyczny',
	SetAVirtualLocation: 'Ustaw wirtualną lokalizację',
	SetAsDefault: 'Ustaw jako domyślne',
	SetAsIntake: 'Ustaw jako wlot',
	SetAsIntakeDefault: 'Ustaw jako domyślne pobieranie',
	SetAvailability: 'Ustaw dostępność',
	SetTemplateAsDefaultIntakeSuccess: 'Pomyślnie ustawiono {templateTitle} jako domyślny szablon odbioru',
	SetUpMfaButton: 'Konfigurowanie uwierzytelniania wieloskładnikowego',
	SetYourLocation: 'Ustaw swój<mark> lokalizacja</mark>',
	SetYourLocationDescription: 'Nie mam adresu firmowego <span>(tylko usługi online i mobilne)</span>',
	SettingUpPayers: 'Konfiguracja płatników',
	Settings: 'Ustawienia',
	SettingsNewUserPasswordDescription:
		'Po zarejestrowaniu się wyślemy Ci kod potwierdzający, którego możesz użyć do potwierdzenia swojego konta',
	SettingsNewUserPasswordTitle: 'Zarejestruj się w Carepatron',
	SettingsTabAutomation: 'Automatyzacja',
	SettingsTabBillingDetails: 'Szczegóły rozliczeń',
	SettingsTabConnectedApps: 'Połączone aplikacje',
	SettingsTabCustomFields: 'Pola niestandardowe',
	SettingsTabDetails: 'Bliższe dane',
	SettingsTabInvoices: 'Faktury',
	SettingsTabLocations: 'Lokalizacje',
	SettingsTabNotifications: 'Powiadomienia',
	SettingsTabOnlineBooking: 'Rezerwacja online',
	SettingsTabPayers: 'Płatnicy',
	SettingsTabReminders: 'Przypomnienia',
	SettingsTabServices: 'Usługi',
	SettingsTabServicesAndAvailability: 'Usługi i dostępność',
	SettingsTabSubscriptions: 'Subskrypcje',
	SettingsTabWorkflowAutomations: 'Automatyzacje',
	SettingsTabWorkflowReminders: 'Podstawowe przypomnienia',
	SettingsTabWorkflowTemplates: 'Szablony',
	Setup: 'Organizować coś',
	SetupGuide: '<h1>Instrukcja konfiguracji</h1>',
	SetupGuideAddServicesActionLabel: 'Start',
	SetupGuideAddServicesSubtitle: '4 kroki • 2 min',
	SetupGuideAddServicesTitle: 'Dodaj swoje usługi',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Początek',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 kroki • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Włącz płatności online',
	SetupGuideImportClientsActionLabel: 'Początek',
	SetupGuideImportClientsSubtitle: '4 kroki • 3 min',
	SetupGuideImportClientsTitle: 'Importuj swoich klientów',
	SetupGuideImportTemplatesActionLabel: 'Rozpocznij',
	SetupGuideImportTemplatesSubtitle: '2 kroki • 1 min',
	SetupGuideImportTemplatesTitle: 'Zaimportuj swoje szablony',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Początek',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 kroki • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Spersonalizuj swoje miejsce pracy',
	SetupGuideSetLocationActionLabel: 'Start',
	SetupGuideSetLocationSubtitle: '4 kroki • 1 min',
	SetupGuideSetLocationTitle: 'Ustaw swoją lokalizację',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Zaproszenie zespołu',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Zaproś swój zespół do łatwej komunikacji i zarządzania zadaniami.',
	SetupGuideSuggestedAddTeamMembersTag: 'Ustawienia',
	SetupGuideSuggestedAddTeamMembersTitle: 'Dodaj członków zespołu',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Dostosuj',
	SetupGuideSuggestedCustomizeBrandSubtitle:
		'Prezentuj się profesjonalnie dzięki swojemu unikatowemu logo i kolorystyce marki.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Dostosuj markę',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Pobierz',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Dostęp do swojej przestrzeni roboczej w dowolnym miejscu i czasie, na dowolnym urządzeniu.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Konfiguracja',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Pobierz aplikację',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Ustaw dostępność',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Zapobiegaj podwójnym rezerwacjom, ustawiając swoją dostępność.',
	SetupGuideSuggestedEditAvailabilityTag: 'Planowanie',
	SetupGuideSuggestedEditAvailabilityTitle: 'Edytuj dostępność',
	SetupGuideSuggestedImportClientsActionLabel: 'Import',
	SetupGuideSuggestedImportClientsSubtitle: 'Przenieś istniejące dane klienta w jedno kliknięcie.',
	SetupGuideSuggestedImportClientsTag: 'Konfiguracja',
	SetupGuideSuggestedImportClientsTitle: 'Importuj klientów',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Przypomnienia o edycji',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Zmniejsz liczbę nieobecności dzięki automatycznym przypomnieniom o wizycie.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Spersonalizuj przypomnienia',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Rozpocznij połączenie',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Zorganizuj połączenie i skontaktuj się z klientami za pomocą naszych narzędzi wideo opartych na sztucznej inteligencji.',
	SetupGuideSuggestedStartVideoCallTag: 'Telemedycyna',
	SetupGuideSuggestedStartVideoCallTitle: 'Rozpocznij wideorozmowę',
	SetupGuideSuggestedTryActionsTitle: 'Rzeczy do wypróbowania 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Spróbuj asystenta AI',
	SetupGuideSuggestedUseAIAssistantSubtitle:
		'Uzyskaj natychmiastowe odpowiedzi na wszystkie pytania dotyczące pracy.',
	SetupGuideSuggestedUseAIAssistantTag: 'Nowy',
	SetupGuideSuggestedUseAIAssistantTitle: 'Użyj asystenta AI',
	SetupGuideSyncCalendarActionLabel: 'Początek',
	SetupGuideSyncCalendarSubtitle: '1 krok • mniej niż 1 min',
	SetupGuideSyncCalendarTitle: 'Zsynchronizuj swój kalendarz',
	SetupGuideVerifyEmailLabel: 'Zweryfikuj',
	SetupGuideVerifyEmailSubtitle: '2 kroki • 2 min',
	SetupOnlineStripePayments: 'Użyj Stripe do płatności online',
	SetupPayments: 'Ustaw płatności',
	Sex: 'Seks',
	SexSelectorPlaceholder: 'Mężczyzna / Kobieta / Wolę nie mówić',
	Share: 'Udział',
	ShareBookingLink: 'Udostępnij link do rezerwacji',
	ShareNoteDefaultMessage: `Cześć{name} udostępnił(a) "{documentName}" dla Ciebie.

Dzięki,
{practiceName}`,
	ShareNoteMessage: `Cześć
{name} udostępnił "{documentName}" {isResponder, select, true {z kilkoma pytaniami do wypełnienia.} other {z Tobą.}}

Dzięki,
{practiceName}`,
	ShareNoteTitle: 'Udostępnij ‘{noteTitle}’',
	ShareNotesWithClients: 'Udostępnij klientom lub kontaktom',
	ShareScreen: 'Udostępnij ekran',
	ShareScreenNotSupported: 'Twoje urządzenie/przeglądarka nie obsługuje funkcji udostępniania ekranu',
	ShareScreenWithId: 'Ekran {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Zezwalaj innym na wyświetlanie tego szablonu i przesyłanie go jako formularz.',
	ShareTemplateAsPublicFormModalTitle: 'Udostępnij link do ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Konfiguracja formularza publicznego została pomyślnie zaktualizowana',
	ShareTemplateAsPublicFormSectionCustomization: 'Dostosowanie',
	ShareTemplateAsPublicFormShowPoweredBy: 'Pokaż "Powered by Carepatron" na moim formularzu',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Pokaż/ukryj „Powered by Carepatron” na moim formularzu',
	ShareTemplateAsPublicFormTrigger: 'Udostępnij',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Użyj brandingu przestrzeni roboczej',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Pokaż/ukryj branding przestrzeni roboczej',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Wysyła kod dla istniejących i nieistniejących klientów',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Podpisy zawsze wymagają weryfikacji adresu e-mail',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Wysyła kod tylko dla istniejących klientów',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Nigdy nie wysyła kodu',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'Wybranie opcji "Nigdy" może umożliwić nieautoryzowanym użytkownikom nadpisanie danych klienta, jeśli użyją adresu e-mail istniejącego klienta.',
	ShareWithCommunity: 'Podziel się ze społecznością',
	ShareYourReferralLink: 'Udostępnij swój link polecający',
	ShareYourScreen: 'Udostępnij swój ekran',
	SheHer: 'Ona/Jej',
	ShortTextAnswer: 'Krótka odpowiedź tekstowa',
	ShortTextFormPrimaryText: 'Krótki tekst',
	ShortTextFormSecondaryText: 'Odpowiedź krótsza niż 300 znaków',
	Show: 'Pokazywać',
	ShowColumn: 'Pokaż kolumnę',
	ShowColumnButton: 'Pokaż kolumnę {value} przycisk',
	ShowColumns: 'Pokaż kolumny',
	ShowColumnsMenu: 'Pokaż menu kolumn',
	ShowDateDurationDescription: 'np. 29 lat',
	ShowDateDurationLabel: 'Pokaż datę trwania',
	ShowDetails: 'Pokaż szczegóły',
	ShowField: 'Pokaż pole',
	ShowFullAddress: 'Pokaż adres',
	ShowHideFields: 'Pokaż/Ukryj pola',
	ShowIcons: 'Pokaż ikony',
	ShowLess: 'Pokaż mniej',
	ShowMeetingTimers: 'Pokaż timery spotkań',
	ShowMenu: 'Pokaż menu',
	ShowMergeSummarySidebar: 'Pokaż podsumowanie scalania',
	ShowMore: 'Pokaż więcej',
	ShowOnTranscript: 'Pokaż na transkrypcji',
	ShowReactions: 'Pokaż reakcje',
	ShowSection: 'Pokaż sekcję',
	ShowServiceCode: 'Pokaż kod serwisowy',
	ShowServiceDescription: 'Pokaż opis w rezerwacjach usług',
	ShowServiceDescriptionDesc: 'Klienci mogą przeglądać opisy usług podczas dokonywania rezerwacji',
	ShowServiceGroups: 'Pokaż kolekcje',
	ShowServiceGroupsDesc: 'Podczas dokonywania rezerwacji klientom zostaną pokazane usługi pogrupowane według odbioru',
	ShowSpeakers: 'Pokaż mówców',
	ShowTax: 'Pokaż podatek',
	ShowTimestamp: 'Pokaż znacznik czasu',
	ShowUnits: 'Pokaż jednostki',
	ShowWeekends: 'Pokaż weekendy',
	ShowYourView: 'Pokaż swój widok',
	SignInWithApple: 'Zaloguj się za pomocą Apple',
	SignInWithGoogle: 'Zaloguj się za pomocą Google',
	SignInWithMicrosoft: 'Zaloguj się za pomocą Microsoft',
	SignUpTitleReferralDefault: '<mark>Zapisać się</mark> i odbierz swoją nagrodę za polecenie',
	SignUpTitleReferralUpgrade:
		'Rozpocznij swój {durationInMonths}-miesięczny <mark>{percentOff, select, 100 {bezpłatny} other {{percentOff}% zniżki}} upgrade</mark>',
	SignatureCaptureError: 'Nie można przechwycić podpisu. Spróbuj ponownie.',
	SignatureFormPrimaryText: 'Podpis',
	SignatureFormSecondaryText: 'Uzyskaj podpis cyfrowy',
	SignatureInfoTooltip: 'Niniejsza reprezentacja wizualna nie jest ważnym podpisem elektronicznym.',
	SignaturePlaceholder: 'Narysuj tutaj swój podpis',
	SignedBy: 'Podpisane przez',
	Signup: 'Zapisać się',
	SignupAgreements: 'Zgadzam się z {termsOfUse} i {privacyStatement} dla mojego konta.',
	SignupBAA: 'Umowa o współpracy biznesowej',
	SignupBusinessAgreements:
		'W imieniu swoim i firmy, wyrażam zgodę na {businessAssociateAgreement}, {termsOfUse} i {privacyStatement} dla mojego konta.',
	SignupInvitationForYou: 'Zostałeś zaproszony do korzystania z Carepatron.',
	SignupPageProviderWarning:
		'Jeśli Twój administrator już utworzył konto, musisz poprosić go o zaproszenie Cię do tego dostawcy. Nie używaj tego formularza rejestracyjnego. Aby uzyskać więcej informacji, zobacz',
	SignupPageProviderWarningLink: 'ten link.',
	SignupPrivacy: 'Polityka prywatności',
	SignupProfession: 'Jaki zawód najlepiej Cię opisuje?',
	SignupSubtitle:
		'Oprogramowanie do zarządzania praktyką Carepatron jest przeznaczone dla praktyków indywidualnych i zespołów. Przestań płacić wygórowane opłaty i bądź częścią rewolucji.',
	SignupSuccessDescription:
		'Potwierdź swój adres e-mail, aby rozpocząć onboarding. Jeśli nie otrzymasz go od razu, sprawdź folder spam.',
	SignupSuccessTitle: 'Proszę sprawdzić swoją pocztę e-mail',
	SignupTermsOfUse: 'Warunki korzystania',
	SignupTitleClient: '<mark>Zarządzaj swoim zdrowiem</mark> z jednego miejsca',
	SignupTitleLast: 'i cała praca, którą wykonujesz! — To nic nie kosztuje',
	SignupTitleOne: '<mark>Dajemy Ci moc</mark> , ',
	SignupTitleThree: '<mark>Zasilaj swoich klientów</mark> , ',
	SignupTitleTwo: '<mark>Wzmacnianie Twojego zespołu</mark> , ',
	Simple: 'Prosty',
	SimplifyBillToDetails: 'Uprość fakturę do szczegółów',
	SimplifyBillToHelperText: 'Tylko pierwsza linia jest używana, gdy pasuje do klienta',
	Singapore: 'Singapur',
	Single: 'Pojedynczy',
	SingleChoiceFormPrimaryText: 'Pojedynczy wybór',
	SingleChoiceFormSecondaryText: 'Wybierz tylko jedną opcję',
	Sister: 'Siostra',
	SisterInLaw: 'Bratowa',
	Skip: 'Pominąć',
	SkipLogin: 'Pomiń logowanie',
	SlightBlur: 'Nieznacznie rozmyj tło',
	Small: 'Mały',
	SmartChips: 'Inteligentne chipy',
	SmartDataChips: 'Inteligentne układy scalone danych',
	SmartReply: 'Szybka odpowiedź',
	SmartSuggestNewClient: '**Smart Suggest** utwórz {name} jako nowego klienta',
	SmartSuggestedFieldDescription: 'To pole jest inteligentną sugestią',
	SocialSecurityNumber: 'Numer ubezpieczenia społecznego',
	SocialWork: 'Praca socjalna',
	SocialWorker: 'Pracownik socjalny',
	SoftwareDeveloper: 'Programista oprogramowania',
	Solo: 'Solo',
	Someone: 'Ktoś',
	Son: 'Syn',
	SortBy: 'Sortuj według',
	SouthAmerica: 'Ameryka Południowa',
	Speaker: 'Głośnik',
	SpeakerSource: 'Źródło głośnika',
	Speakers: 'Głośniki',
	SpecifyPaymentMethod: 'Określ metodę płatności',
	SpeechLanguagePathology: 'Patologia mowy i języka',
	SpeechTherapist: 'Logopeda',
	SpeechTherapists: 'Terapeuci mowy',
	SpeechTherapy: 'Terapia logopedyczna',
	SportsMedicinePhysician: 'Lekarz medycyny sportowej',
	Spouse: 'Współmałżonek',
	SpreadsheetColumnExample: 'np ',
	SpreadsheetColumns: 'Kolumny arkusza kalkulacyjnego',
	SpreadsheetUploaded: 'Arkusz kalkulacyjny przesłany',
	SpreadsheetUploading: 'Przesyłanie...',
	Staff: 'Personel',
	StaffAccessDescriptionAdmin: 'Administratorzy mogą zarządzać wszystkim na platformie.',
	StaffAccessDescriptionStaff: `Pracownicy mogą zarządzać klientami, notatkami i dokumentacją, którą utworzyli lub udostępnili
 umawiaj z nimi spotkania, zarządzaj fakturami.`,
	StaffContactAssignedSubject:
		'{actorProfileName} przypisał(a) {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} i {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { i 1 innego klienta} other { i # innych klientów}} do Ciebie',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} udostępnił(a) Ci skrzynkę odbiorczą {inboxName}.',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} usunął/a dostęp do skrzynki odbiorczej {inboxName}',
	StaffMembers: 'Członkowie personelu',
	StaffMembersNumber: '{billedUsers, plural, one {# członek zespołu} other {# członkowie zespołu}}',
	StaffSavedSuccessSnackbar: 'Informacje o członkach zespołu zostały pomyślnie zapisane!',
	StaffSelectorAdminRole: 'Administrator',
	StaffSelectorStaffRole: 'Członek personelu',
	StandardAppointment: 'Standardowe spotkanie',
	StandardColor: 'Kolor zadania',
	StartAndEndTime: 'Godzina rozpoczęcia i zakończenia',
	StartCall: 'Rozpocznij połączenie',
	StartDate: 'Data rozpoczęcia',
	StartDictating: 'Zacznij dyktować',
	StartImport: 'Rozpocznij import',
	StartRecordErrorTitle: 'Wystąpił błąd podczas uruchamiania nagrywania',
	StartRecording: 'Rozpocznij nagrywanie',
	StartTimeIncrements: 'Przyrosty czasu rozpoczęcia',
	StartTimeIncrementsView: '{startTimeIncrements} min interwały',
	StartTranscribing: 'Rozpocznij transkrypcję',
	StartTranscribingNotes:
		'Wybierz klientów, dla których chcesz wygenerować notatkę. Następnie kliknij przycisk „Rozpocznij transkrypcję”, aby rozpocząć nagrywanie',
	StartTranscription: 'Rozpocznij transkrypcję',
	StartVideoCall: 'Rozpocznij połączenie wideo',
	StartWeekOn: 'Rozpocznij tydzień',
	StartedBy: 'Rozpoczęty przez ',
	Starter: 'Rozrusznik',
	State: 'Państwo',
	StateIndustrialAccidentProviderNumber: 'Numer dostawcy usług w zakresie wypadków przemysłowych w stanie',
	StateLicenseNumber: 'Numer licencji państwowej',
	Statement: 'Oświadczenie',
	StatementDescriptor: 'Opis oświadczenia',
	StatementDescriptorToolTip:
		'Opis oświadczenia jest wyświetlany na wyciągach bankowych lub z kart kredytowych klientów. Musi mieć od 5 do 22 znaków i odzwierciedlać nazwę Twojej firmy.',
	StatementNumber: 'Oświadczenie #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Wprowadź etykietę statusu',
	StepFather: 'Ojczym',
	StepMother: 'Macocha',
	Stockholm: 'Sztokholm',
	StopIgnoreSendersDescription:
		'Jeśli przestaniesz ignorować tych nadawców, przyszłe konwersacje będą wysyłane do „Skrzynki odbiorczej”. Czy na pewno chcesz przestać ignorować tych nadawców?',
	StopIgnoring: 'Przestań ignorować',
	StopIgnoringSenders: 'Przestań ignorować nadawców',
	StopIgnoringSendersSuccess: 'Przestałem ignorować adres e-mail <mark>{addresses}</mark>',
	StopSharing: 'Zatrzymaj udostępnianie',
	StopSharingLabel: 'carepatron.com udostępnia Twój ekran.',
	Storage: 'Składowanie',
	StorageAlmostFullDescription: '🚀 Zaktualizuj już teraz, aby Twoje konto działało bezproblemowo.',
	StorageAlmostFullTitle: 'Zużyłeś {percentage}% limitu pamięci Twojej przestrzeni roboczej!',
	StorageFullDescription: 'Uzyskaj więcej miejsca, rozszerzając swój plan.',
	StorageFullTitle: '	Pamięć masowa jest pełna.',
	Street: 'Ulica',
	StripeAccountNotCompleteErrorCode:
		'Płatności online nie są {hasProviderName, select, true {skonfigurowane dla {providerName}} other {włączone dla tego dostawcy}}.',
	StripeAccountRejectedError: 'Konto Stripe zostało odrzucone. Skontaktuj się z pomocą techniczną.',
	StripeBalance: 'Równowaga pasków',
	StripeChargesInfoToolTip: 'Umożliwia naliczanie opłat za pomocą karty debetowej ',
	StripeFeesDescription:
		'Carepatron używa Stripe, aby szybko wypłacać Ci pieniądze i chronić Twoje dane płatnicze. Dostępne metody płatności różnią się w zależności od regionu, wszystkie główne metody debetowe ',
	StripeFeesDescriptionItem1:
		'Opłaty za przetwarzanie są naliczane za każdą transakcję, która zakończyła się powodzeniem. Możesz {link}.',
	StripeFeesDescriptionItem2: 'Wypłaty odbywają się codziennie, ale są wstrzymywane przez okres do 4 dni.',
	StripeFeesLinkToRatesText: 'zobacz nasze stawki tutaj',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Przepraszamy, dla faktur korzystających z płatności online wymagana jest minimalna kwota {minimumAmount}',
	StripePaymentsDisabled: 'Płatności online są wyłączone. Sprawdź ustawienia płatności.',
	StripePaymentsUnavailable: 'Płatności niedostępne',
	StripePaymentsUnavailableDescription: 'Wystąpił błąd podczas ładowania płatności. Spróbuj ponownie później.',
	StripePayoutsInfoToolTip: 'Umożliwia wypłatę środków na Twoje konto bankowe',
	StyleYourWorkspace: '<mark>Stylizuj</mark> swoje miejsce pracy',
	StyleYourWorkspaceDescription1:
		'Pobraliśmy zasoby marki z Twojej strony internetowej. Możesz je edytować lub kontynuować do swojego Carepatron workspace',
	StyleYourWorkspaceDescription2:
		'Użyj swoich zasobów marki, aby spersonalizować faktury i rezerwacje online, tworząc spójne wrażenia dla klienta.',
	SubAdvanced: 'Zaawansowane',
	SubEssential: 'Essential',
	SubOrganization: 'Organizacja',
	SubPlus: 'Plus',
	SubProfessional: 'Profesjonalny',
	Subject: 'Temat',
	Submit: 'Składać',
	SubmitElectronically: 'Prześlij elektronicznie',
	SubmitFeedback: 'Złóż opinię',
	SubmitFormValidationError:
		'Upewnij się, że wszystkie wymagane pola są poprawnie wypełnione i spróbuj ponownie wysłać formularz.',
	Submitted: 'Złożony',
	SubmittedDate: 'Data złożenia',
	SubscribePerMonth: 'Zapisz się {price} {isMonthly, select, true {miesięcznie} other {rocznie}}',
	SubscriptionDiscountDescription:
		'{percentOff}% zniżki {months, select, null { } other { {months, plural, one {na # miesiąc} other {na # miesięcy}}}}',
	SubscriptionFreeTrialDescription: 'Darmowe do {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Nie udało nam się dokonać płatności za Twoją subskrypcję. Sprawdź szczegóły płatności',
	SubscriptionPlanDetailsHeader: 'Za użytkownika/rozliczane co miesiąc, rocznie',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} rozliczane miesięcznie (USD)',
	SubscriptionPlans: 'Plany subskrypcji',
	SubscriptionPlansDescription:
		'Uaktualnij swój plan, aby odblokować dodatkowe korzyści i zapewnić płynne działanie swojego gabinetu.',
	SubscriptionPlansDescriptionNoPermission:
		'Wygląda na to, że nie masz dostępu do aktualizacji w tej chwili — skontaktuj się ze swoim administratorem, aby uzyskać pomoc.',
	SubscriptionSettings: 'Ustawienia subskrypcji',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> użytego miejsca na dysku',
	SubscriptionSettingsStorageUsed: '{użyte} z {limit} użytych',
	SubscriptionSettingsUnlimitedStorage: 'Dostępna nieograniczona ilość miejsca do przechowywania',
	SubscriptionSummary: 'Podsumowanie subskrypcji',
	SubscriptionUnavailableOverStorageLimit:
		'Twoje obecne wykorzystanie przekracza limit przechowywania dla tego planu.',
	SubscriptionUnpaidBannerButton: 'Przejdź do subskrypcji',
	SubscriptionUnpaidBannerDescription: 'Sprawdź, czy dane płatności są poprawne i spróbuj ponownie',
	SubscriptionUnpaidBannerTitle: 'Nie udało nam się zrealizować płatności za Twoją subskrypcję.',
	Subscriptions: 'Subskrypcje',
	SubscriptionsAndPayments: 'Subskrypcje ',
	Subtotal: 'Podsumowanie',
	SuburbOrProvince: 'Przedmieście/Prowincja',
	SuburbOrState: 'Przedmieście/stan',
	SuccessSavedNoteChanges: 'Pomyślnie zapisano zmiany w notatce',
	SuccessShareDocument: 'Dokument udostępniony pomyślnie',
	SuccessShareNote: 'Notatka udostępniona pomyślnie',
	SuccessfullyCreatedValue: 'Pomyślnie utworzono {value}',
	SuccessfullyDeletedTranscriptionPart: 'Pomyślnie usunięto część transkrypcji',
	SuccessfullyDeletedValue: 'Pomyślnie usunięto {value}',
	SuccessfullySubmitted: 'Pomyślnie wysłano ',
	SuccessfullyUpdatedClientSettings: 'Pomyślnie zaktualizowano ustawienia klienta',
	SuccessfullyUpdatedTranscriptionPart: 'Pomyślnie zaktualizowano część transkrypcji',
	SuccessfullyUpdatedValue: 'Pomyślnie zaktualizowano {value}',
	SuggestedAIPoweredTemplates: 'Sugerowane Szablony Wspomagane Sztuczną Inteligencją',
	SuggestedAITemplates: 'Sugerowane szablony AI',
	SuggestedActions: 'Sugerowane działania',
	SuggestedLocations: 'Sugerowane lokalizacje',
	Suggestions: 'Sugestie',
	Summarise: 'Podsumowanie AI',
	SummarisingContent: 'Podsumowując {title}',
	Sunday: 'Niedziela',
	Superbill: 'Superprojekt',
	SuperbillAndInsuranceBilling: 'Superprojekt ',
	SuperbillAutomationMonthly: 'Aktywny • Ostatni dzień miesiąca',
	SuperbillAutomationNoEmail:
		'Aby pomyślnie wysłać automatyczne dokumenty rozliczeniowe, dodaj adres e-mail tego klienta',
	SuperbillAutomationNotActive: 'Nieaktywny',
	SuperbillAutomationUpdateFailure: 'Nie udało się zaktualizować ustawień automatyzacji Superbill',
	SuperbillAutomationUpdateSuccess: 'Pomyślnie zaktualizowano ustawienia automatyzacji Superbill',
	SuperbillClientHelperText: 'Informacje te są wstępnie uzupełniane na podstawie danych klienta',
	SuperbillNotFoundDescription:
		'Skontaktuj się ze swoim dostawcą i poproś o więcej informacji lub o ponowne wysłanie rachunku.',
	SuperbillNotFoundTitle: 'Nie znaleziono Superbill',
	SuperbillNumber: 'Superfaktura #{number}',
	SuperbillNumberAlreadyExists: 'Numer rachunku Superbill już istnieje',
	SuperbillPracticeHelperText: 'Informacje te są wstępnie uzupełniane na podstawie ustawień rozliczeniowych gabinetu',
	SuperbillProviderHelperText: 'Informacje te są wstępnie uzupełniane na podstawie danych osobowych personelu',
	SuperbillReceipts: 'Paragony Superbill',
	SuperbillsEmptyStateDescription: 'Nie znaleziono żadnych superrachunków.',
	Surgeon: 'Chirurg',
	Surgeons: 'Chirurdzy',
	SurgicalTechnologist: 'Technik chirurgiczny',
	SwitchFromAnotherPlatform: 'Przechodzę z innej platformy',
	SwitchToMyPortal: 'Przejdź do Mój portal',
	SwitchToMyPortalTooltip: `Uzyskaj dostęp do własnego portalu osobistego,
 umożliwiając Ci eksplorację
 doświadczenia klienta w korzystaniu z portalu.`,
	SwitchWorkspace: 'Zmień obszar roboczy',
	SwitchingToADifferentPlatform: 'Przejście na inną platformę',
	Sydney: 'Sydnej',
	SyncCalendar: 'Synchronizuj kalendarz',
	SyncCalendarModalDescription:
		'Inni członkowie zespołu nie będą mogli zobaczyć Twoich zsynchronizowanych kalendarzy. Wizyty klientów można aktualizować lub usuwać tylko z Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Wyświetl mój kalendarz w Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Synchronizuj mój kalendarz z Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Synchronizuj spotkania Carepatron z moim kalendarzem',
	SyncCarepatronAppointmentsWithMyCalendar: 'Synchonizuj terminy Carepatron z moim kalendarzem',
	SyncGoogleCalendar: 'Synchronizuj kalendarz Google',
	SyncInbox: 'Synchronizuj skrzynkę odbiorczą z Carepatron',
	SyncMyCalendarToCarepatron: 'Zsynchronizuj mój kalendarz z Carepatron',
	SyncOutlookCalendar: 'Synchronizuj kalendarz programu Outlook',
	SyncedFromExternalCalendar: 'Zsynchronizowano z zewnętrznego kalendarza',
	SyncingCalendarName: 'Synchronizowanie kalendarza {calendarName}',
	SyncingFailed: 'Synchronizacja nie powiodła się',
	SystemGenerated: 'Wygenerowany przez system',
	TFN: 'NIP',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tabela',
	TableRowLabel: 'Rząd tabeli dla {value}',
	TagSelectorNoOptionsText: 'Kliknij „utwórz nowy”, aby dodać nowy tag',
	Tags: 'Tagi',
	TagsInputPlaceholder: 'Wyszukaj lub utwórz tagi',
	Task: 'Zadanie',
	TaskAttendeeStatusUpdatedSuccess: 'Pomyślnie zaktualizowano statusy wizyt',
	Tasks: 'Zadania',
	Tax: 'Podatek',
	TaxAmount: 'Kwota podatku',
	TaxID: 'Numer NIP',
	TaxIdType: 'Typ identyfikatora podatkowego',
	TaxName: 'Nazwa podatkowa',
	TaxNumber: 'Numer podatkowy',
	TaxNumberType: 'Rodzaj numeru podatkowego',
	TaxNumberTypeInvalid: '{type} jest nieprawidłowy',
	TaxPercentageOfAmount: '{taxName} ({percentage}% od {amount})',
	TaxRate: 'Stawka podatku',
	TaxRatesDescription: 'Zarządzaj stawkami podatkowymi, które zostaną zastosowane do pozycji faktury.',
	Taxable: 'Podlegający opodatkowaniu',
	TaxonomyCode: 'Kod taksonomii',
	TeacherAssistant: 'Asystent nauczyciela',
	Team: 'Zespół',
	TeamMember: 'Członek zespołu',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {jest} other {są}} już zarezerwowani na ten czas.{br}Wybierz nowy termin, aby uniknąć podwójnej rezerwacji.',
	TeamMembers: 'Członkowie zespołu',
	TeamMembersColour: 'Kolor członków zespołu',
	TeamMembersDetails: 'Szczegóły dotyczące członków zespołu',
	TeamSize: 'Ile osób liczy Twój zespół?',
	TeamTemplates: 'Szablony zespołów',
	TeamTemplatesSectionDescription: 'Utworzone przez Ciebie i Twój zespół',
	TelehealthAndVideoCalls: 'Telezdrowie ',
	TelehealthProvidedOtherThanInPatientCare: 'Telemedycyna świadczona w celach innych niż opieka stacjonarna',
	TelehealthVideoCall: 'Rozmowa wideo na temat telezdrowia',
	Template: 'Szablon',
	TemplateDescription: 'Opis szablonu',
	TemplateDetails: 'Szczegóły szablonu',
	TemplateEditModeViewSwitcherDescription: 'Utwórz i edytuj szablon',
	TemplateGallery: 'Szablony społeczności',
	TemplateImportCompletedNotificationSubject: 'Import szablonu zakończony! {templateTitle} jest gotowy do użycia.',
	TemplateImportFailedNotificationSubject: 'Nie udało się zaimportować pliku {fileName}.',
	TemplateName: 'Nazwa szablonu',
	TemplateNotFound: 'Nie znaleziono szablonu.',
	TemplatePreviewErrorMessage: 'Wystąpił błąd podczas ładowania podglądu szablonu',
	TemplateResponderModeViewSwitcherDescription: 'Podgląd formularzy i interakcja z nimi',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Sprawdź, jak wyglądają Twoje formularze, gdy wypełniają je respondenci',
	TemplateSaved: 'Zapisane zmiany',
	TemplateTitle: 'Tytuł szablonu',
	TemplateType: 'Typ szablonu',
	Templates: 'Szablony',
	TemplatesCategoriesFilter: 'Filtruj według kategorii',
	TemplatesPublicTemplatesFilter: ' Filtruj według społeczności/zespołu',
	Text: 'Tekst',
	TextAlign: 'Wyrównanie tekstu',
	TextColor: 'Kolor tekstu',
	ThankYouForYourFeedback: 'Dziękujemy za Twoją opinię!',
	ThanksForLettingKnow: 'Dziękujemy za informację.',
	ThePaymentMethod: 'Metoda płatności',
	ThemThey: 'Oni/oni',
	Theme: 'Temat',
	ThemeAllColorsPickerTitle: 'Więcej motywów',
	ThemeColor: 'Temat',
	ThemeColorDarkMode: 'Ciemny',
	ThemeColorLightMode: 'Światło',
	ThemeColorModePickerTitle: 'Tryb koloru',
	ThemeColorSystemMode: 'System',
	ThemeCpColorPickerTitle: 'Carepatron themes',
	ThemePanelDescription: 'Wybierz między trybem jasnym i ciemnym, a także dostosuj preferencje motywu',
	ThemePanelTitle: 'Wygląd',
	Then: 'Następnie',
	Therapist: 'Terapeuta',
	Therapists: 'Terapeuci',
	Therapy: 'Terapia',
	Thick: 'Gruby',
	Thin: 'Cienki',
	ThirdPerson: '3 osoba',
	ThisAndFollowingAppointments: 'To i kolejne nominacje',
	ThisAndFollowingMeetings: 'To i kolejne spotkania',
	ThisAndFollowingReminders: 'To i kolejne przypomnienia',
	ThisAndFollowingTasks: 'To i kolejne zadania',
	ThisAppointment: 'To spotkanie',
	ThisMeeting: 'To spotkanie',
	ThisMonth: 'W tym miesiącu',
	ThisPerson: 'Ta osoba',
	ThisReminder: 'To przypomnienie',
	ThisTask: 'To zadanie',
	ThisWeek: 'W tym tygodniu',
	ThreeDay: '3 dni',
	Thursday: 'Czwartek',
	Time: 'Czas',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {min}}',
	TimeAgoSeconds: '{number}ów',
	TimeFormat: 'Format czasu',
	TimeIncrement: 'Przyrost czasu',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Rozmiar przedziału czasowego',
	Timestamp: 'Znak czasu',
	Timezone: 'Strefa czasowa',
	TimezoneDisplay: 'Wyświetlanie strefy czasowej',
	TimezoneDisplayDescription: 'Zarządzaj ustawieniami wyświetlania strefy czasowej.',
	Title: 'Tytuł',
	To: 'Do',
	ToYourWorkspace: 'do Twojego miejsca pracy',
	Today: 'Dzisiaj',
	TodayInHoursPlural: 'Dzisiaj w ciągu {count} {count, plural, one {godziny} other {godzin}}',
	TodayInMinsAbbreviated: 'Dzisiaj w {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Przełącz komórkę nagłówka',
	ToggleHeaderCol: 'Przełącz kolumnę nagłówka',
	ToggleHeaderRow: 'Przełącz wiersz nagłówka',
	Tokyo: 'Tokio',
	Tomorrow: 'Jutro',
	TomorrowAfternoon: 'Jutro po południu',
	TomorrowMorning: 'Jutro rano',
	TooExpensive: 'Za drogie',
	TooHardToSetUp: 'Zbyt trudne do skonfigurowania',
	TooManyFiles: 'Wykryto więcej niż 1 plik.',
	ToolsExample: 'Prosta praktyka: Microsoft, Calendly, Asana, Doxy.me...',
	Total: 'Całkowity',
	TotalAccountCredit: 'Całkowity kredyt na koncie',
	TotalAdjustments: 'Całkowite korekty',
	TotalAmountToCreditInCurrency: 'Całkowita kwota do zaksięgowania ({currency})',
	TotalBilled: 'Całkowita kwota do zapłaty',
	TotalConversations: '{total} {total, plural, =0 {rozmowa} one {rozmowa} other {rozmowy}}',
	TotalOverdue: 'Całkowite przeterminowanie',
	TotalOverdueTooltip:
		'Całkowite saldo przeterminowanych płatności obejmuje wszystkie niezapłacone faktury, niezależnie od zakresu dat, które nie zostały anulowane ani przetworzone.',
	TotalPaid: 'Całkowita kwota zapłacona',
	TotalPaidTooltip:
		'Całkowite saldo płatności obejmuje wszystkie kwoty z faktur, które zostały zapłacone w określonym przedziale dat.',
	TotalUnpaid: 'Całkowita kwota niezapłacona',
	TotalUnpaidTooltip:
		'Całkowite niezapłacone saldo obejmuje wszystkie zaległe kwoty z tytułu przetwarzania, niezapłacone i wysłane faktury płatne w określonym przedziale dat.',
	TotalWorkflows: '{count} {count, plural, one {przepływ pracy} other {przepływy pracy}}',
	TotpSetUpManualEntryInstruction: 'Alternatywnie możesz ręcznie wprowadzić poniższy kod do aplikacji:',
	TotpSetUpModalDescription:
		'Zeskanuj kod QR za pomocą aplikacji uwierzytelniającej, aby skonfigurować uwierzytelnianie wieloskładnikowe.',
	TotpSetUpModalTitle: 'Skonfiguruj urządzenie MFA',
	TotpSetUpSuccess: 'Wszystko gotowe! Uwierzytelnianie wieloskładnikowe zostało włączone.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Wprowadź kod wygenerowany przez aplikację uwierzytelniającą',
	Transcribe: 'Rozpisać',
	TranscribeLanguageSelector: 'Wybierz język wprowadzania',
	TranscribeLiveAudio: 'Transkrybuj dźwięk na żywo',
	Transcribing: 'Transkrypcja dźwięku...',
	TranscribingIn: 'Transkrypcja w',
	Transcript: 'Transkrypcja',
	TranscriptRecordingCompleteInfo: 'Po zakończeniu nagrania zobaczysz tutaj swój zapis transkryptu.',
	TranscriptSuccessSnackbar: 'Transkrypcja została pomyślnie przetworzona.',
	Transcription: 'Transkrypcja',
	TranscriptionEmpty: 'Brak dostępnej transkrypcji',
	TranscriptionEmptyHelperMessage: 'Ta transkrypcja nic nie wykryła. Uruchom ją ponownie i spróbuj jeszcze raz.',
	TranscriptionFailedNotice: 'Ta transkrypcja nie została pomyślnie przetworzona',
	TranscriptionIdleMessage:
		'Nie słyszymy żadnego dźwięku. Jeśli potrzebujesz więcej czasu, odpowiedz w ciągu {timeValue} sekund, w przeciwnym razie sesja zostanie zakończona.',
	TranscriptionInProcess: 'Transkrypcja w toku…',
	TranscriptionIncompleteNotice: 'Niektóre części tej transkrypcji nie zostały pomyślnie przetworzone',
	TranscriptionOvertimeWarning: '{scribeType} sesja kończy się za <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Czy na pewno chcesz usunąć tę część transkrypcji?',
	TranscriptionText: 'Głos na tekst',
	TranscriptsPending: 'Twój zapis będzie dostępny tutaj po zakończeniu sesji.',
	Transfer: 'Przenosić',
	TransferAndDelete: 'Przenieś i usuń',
	TransferOwnership: 'Przeniesienie własności',
	TransferOwnershipConfirmationModalDescription:
		'Tę czynność można cofnąć tylko w przypadku przeniesienia własności z powrotem na Ciebie.',
	TransferOwnershipDescription: 'Przekaż własność tego obszaru roboczego innemu członkowi zespołu.',
	TransferOwnershipSuccessSnackbar: 'Przeniesienie własności powiodło się!',
	TransferOwnershipToMember: 'Czy na pewno chcesz przenieść tę przestrzeń roboczą do {staff}?',
	TransferStatusAlert:
		'Usuwanie {numberOfStatuses, plural, one {tego statusu} other {tych statusów}} wpłynie na {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} status klienta.</strong>} other {<strong>{numberOfAffectedRecords} statusów klientów.</strong>}}',
	TransferStatusDescription:
		'Wybierz inny status dla tych klientów przed kontynuowaniem usuwania. Tej czynności nie można cofnąć.',
	TransferStatusLabel: 'Przenieś do nowego statusu',
	TransferStatusPlaceholder: 'Wybierz istniejący status',
	TransferStatusTitle: 'Status transferu przed usunięciem',
	TransferTaskAttendeeStatusAlert:
		'Usunięcie tego statusu wpłynie na <strong>{number} przyszłych umówień {number, plural, one {statusu} other {statusów}}.</strong>',
	TransferTaskAttendeeStatusDescription:
		'Wybierz inny status dla tych klientów, zanim przejdziesz do usunięcia. Ta akcja jest nieodwracalna.',
	TransferTaskAttendeeStatusSubtitle: 'Status wizyty',
	TransferTaskAttendeeStatusTitle: 'Status transferu przed usunięciem',
	Trash: 'Śmieci',
	TrashDeleteItemsModalConfirm: 'Aby potwierdzić, wpisz {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Następujące {count, plural, one {element} other {elementy}} zostaną trwale usunięte i nie można ich odzyskać.',
	TrashDeleteItemsModalTitle: 'Usuń {count, plural, one {element} other {elementy}} na zawsze',
	TrashDeletedAllItems: 'Usunięto wszystkie elementy',
	TrashDeletedItems: 'Usunięto {count, plural, one {element} other {elementy}}',
	TrashDeletedItemsFailure: 'Nie udało się usunąć elementów z kosza',
	TrashLocationAppointmentType: 'Kalendarz',
	TrashLocationBillingAndPaymentsType: 'Fakturowanie i płatności',
	TrashLocationContactType: 'Klienci',
	TrashLocationNoteType: 'Notatki ',
	TrashRestoreItemsModalDescription:
		'Następujące {count, plural, one {element} other {elementy}} zostaną przywrócone.',
	TrashRestoreItemsModalTitle: 'Przywróć {count, plural, one {element} other {elementy}}',
	TrashRestoredAllItems: 'Przywrócono wszystkie elementy',
	TrashRestoredItems: 'Przywrócono {count, plural, one {element} other {elementy}}',
	TrashRestoredItemsFailure: 'Nie udało się przywrócić elementów z kosza',
	TrashSuccessfullyDeletedItem: 'Pomyślnie usunięto {type}',
	Trigger: 'Spust',
	Troubleshoot: 'Rozwiązywanie problemów',
	TryAgain: 'Spróbuj ponownie',
	Tuesday: 'Wtorek',
	TwoToTen: '2 - 10',
	Type: 'Typ',
	TypeHere: 'Wpisz tutaj...',
	TypeToConfirm: 'Aby potwierdzić, wpisz {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Nagłówek 1',
	TypographyHeading2: 'Nagłówek 2',
	TypographyHeading3: 'Nagłówek 3',
	TypographyHeading4: 'Nagłówek 4',
	TypographyHeading5: 'Nagłówek 5',
	TypographyP: 'P',
	TypographyParagraph: 'Ustęp',
	UnableToCompleteAction: 'Nie można wykonać akcji.',
	UnableToPrintDocument: 'Nie można wydrukować dokumentu. Spróbuj ponownie później.',
	Unallocated: 'Nieprzydzielony',
	UnallocatedPaymentDescription: `Ta płatność nie została w całości przypisana do pozycji rozliczanych.
 Dodaj alokację do niezapłaconych pozycji lub wystaw notę kredytową lub zwróć pieniądze.`,
	UnallocatedPaymentTitle: 'Nieprzydzielona płatność',
	UnallocatedPayments: 'Nieprzydzielone płatności',
	Unarchive: 'Wycofaj z archiwum',
	Unassigned: 'Nieprzypisane',
	UnauthorisedInvoiceSnackbar: 'Nie masz dostępu do zarządzania fakturami tego klienta.',
	UnauthorisedSnackbar: 'Nie masz uprawnień do wykonania tej czynności.',
	Unavailable: 'Nie płynny',
	Uncategorized: 'Bez kategorii',
	Unclaimed: 'Nieodebrane',
	UnclaimedAmount: 'Niewymagana kwota',
	UnclaimedItems: 'Nieodebrane przedmioty',
	UnclaimedItemsMustBeInCurrency: 'Obsługiwane są tylko przedmioty w następujących walutach: {currencies}',
	Uncle: 'Wujek',
	Unconfirmed: 'Niepotwierdzone',
	Underline: 'Podkreślać',
	Undo: 'Anulować',
	Unfavorite: 'Usuń z ulubionych',
	Uninvoiced: 'Niefakturowane',
	UninvoicedAmount: 'Niewystawiona kwota',
	UninvoicedAmounts:
		'{count, plural, =0 {Brak niefakturowanych kwot} one {Niefakturowana kwota} other {Niefakturowane kwoty}}',
	Unit: 'Jednostka',
	UnitedKingdom: 'Zjednoczone Królestwo',
	UnitedStates: 'Stany Zjednoczone',
	UnitedStatesEast: 'Stany Zjednoczone - Wschód',
	UnitedStatesWest: 'Stany Zjednoczone - Zachód',
	Units: 'Jednostki',
	UnitsIsRequired: 'Jednostki są wymagane',
	UnitsMustBeGreaterThanZero: 'Jednostki muszą być większe niż 0',
	UnitsPlaceholder: '1',
	Unknown: 'Nieznany',
	Unlimited: 'Nieograniczony',
	Unlock: 'Odblokować',
	UnlockNoteHelper: 'Przed wprowadzeniem jakichkolwiek zmian redaktorzy są zobowiązani odblokować notatkę.',
	UnmuteAudio: 'Wyłącz wyciszenie dźwięku',
	UnmuteEveryone: 'Wyłącz wyciszenie dla wszystkich',
	Unpaid: 'Nie zapłacony',
	UnpaidInvoices: 'Niezapłacone faktury',
	UnpaidItems: 'Nieopłacone przedmioty',
	UnpaidMultiple: 'Nie zapłacony',
	Unpublish: 'Usuń publikację',
	UnpublishTemplateConfirmationModalPrompt:
		'Usuwanie <span>{title}</span> spowoduje usunięcie tego zasobu ze społeczności Carepatron. Tego działania nie można cofnąć.',
	UnpublishToCommunitySuccessMessage: 'Pomyślnie usunięto ‛{title}’ ze społeczności',
	Unread: 'Niewykształcony',
	Unrecognised: 'Nierozpoznany',
	UnrecognisedDescription:
		'Ta metoda płatności nie jest rozpoznawana przez Twoją obecną wersję aplikacji. Odśwież przeglądarkę, aby uzyskać najnowszą wersję do przeglądania i edytowania tej metody płatności.',
	UnsavedChanges: 'Niezapisane zmiany',
	UnsavedChangesPromptContent: 'Czy chcesz zapisać zmiany przed zamknięciem?',
	UnsavedChangesPromptTitle: 'Masz niezapisane zmiany',
	UnsavedNoteChangesWarning: 'Wprowadzone zmiany mogą nie zostać zapisane',
	UnsavedTemplateChangesWarning: 'Wprowadzone zmiany mogą nie zostać zapisane',
	UnselectAll: 'Odznacz wszystko',
	Until: 'Aż',
	UntitledConversation: '<h1>Nieo tytułowanej konwersacji</h1>',
	UntitledFolder: 'Folder bez tytułu',
	UntitledNote: 'Notatka bez tytułu',
	UntitledSchedule: 'Harmonogram bez tytułu',
	UntitledSection: 'Sekcja bez tytułu',
	UntitledTemplate: 'Szablon bez tytułu',
	Unverified: 'Niesprawdzony',
	Upcoming: 'Nadchodzące',
	UpcomingAppointments: 'Nadchodzące spotkania',
	UpcomingDateOverridesEmpty: 'Nie znaleziono żadnych zastąpień daty',
	UpdateAvailabilityScheduleFailure: 'Nie udało się zaktualizować harmonogramu dostępności',
	UpdateAvailabilityScheduleSuccess: 'Pomyślnie zaktualizowano harmonogram dostępności',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Czy chcesz, aby nowe ceny zostały zastosowane do faktur i roszczeń uczestników?',
	UpdateLink: 'Zaktualizuj link',
	UpdatePrimaryEmailWarningDescription:
		'Zmiana adresu e-mail klienta spowoduje utratę dostępu do istniejących wizyt i notatek.',
	UpdatePrimaryEmailWarningTitle: 'Zmiana adresu e-mail klienta',
	UpdateSettings: 'Aktualizuj ustawienia',
	UpdateStatus: 'Aktualizuj status',
	UpdateSuperbillReceiptFailure: 'Nie udało się zaktualizować paragonu Superbill',
	UpdateSuperbillReceiptSuccess: 'Pomyślnie zaktualizowano paragon Superbill',
	UpdateTaskBillingDetails: 'Aktualizuj dane rozliczeniowe',
	UpdateTaskBillingDetailsDescription:
		'Cennik wizyt uległ zmianie. Czy chcesz, aby nowy cennik został zastosowany do pozycji rozliczeniowych, faktur i roszczeń uczestników? Wybierz aktualizacje, które chcesz kontynuować.',
	UpdateTemplateFolderSuccessMessage: 'Pomyślnie zaktualizowano folder',
	UpdateUnpaidInvoices: 'Aktualizuj niezapłacone faktury',
	UpdateUserInfoSuccessSnackbar: 'Informacje o użytkowniku zostały pomyślnie zaktualizowane!',
	UpdateUserSettingsSuccessSnackbar: 'Ustawienia użytkownika zostały pomyślnie zaktualizowane!',
	Upgrade: 'Aktualizacja',
	UpgradeForSMSReminder: 'Zaktualizuj do <b>wersji Professional,</b> aby otrzymywać nieograniczone przypomnienia SMS',
	UpgradeNow: 'Zaktualizuj teraz',
	UpgradePlan: 'Plan uaktualnienia',
	UpgradeSubscriptionAlertDescription:
		'Masz mało miejsca w pamięci. Zwiększ swój plan, aby odblokować dodatkową pamięć i zapewnić płynne działanie swojej praktyki!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Masz mało miejsca do przechowywania. Poproś kogoś z Twojej praktyki o <span>Dostęp administratora</span>, aby zaktualizować plan i odblokować dodatkowe miejsce do przechowywania, aby Twoja praktyka działała płynnie!',
	UpgradeSubscriptionAlertTitle: 'Czas na uaktualnienie subskrypcji',
	UpgradeYourPlan: 'Uaktualnij swój plan',
	UploadAudio: 'Prześlij dźwięk',
	UploadFile: 'Prześlij plik',
	UploadFileDescription: 'Z jakiej platformy oprogramowania przechodzisz?',
	UploadFileMaxSizeError: 'Plik jest zbyt duży. Maksymalny rozmiar pliku to {fileSizeLimit}.',
	UploadFileSizeLimit: 'Limit rozmiaru {size}MB',
	UploadFileTileDescription: 'Użyj plików CSV, XLS, XLSX lub ZIP, aby przesłać swoich klientów.',
	UploadFileTileLabel: 'Załaduj plik',
	UploadFiles: 'Prześlij pliki',
	UploadIndividually: 'Prześlij pliki indywidualnie',
	UploadLogo: 'Prześlij logo',
	UploadPhoto: 'Prześlij zdjęcie',
	UploadToCarepatron: 'Prześlij do Carepatron',
	UploadYourLogo: 'Prześlij swoje logo',
	UploadYourTemplates: 'Wgraj swoje szablony, a my je dla Ciebie przekonwertujemy',
	Uploading: 'Przesyłanie',
	UploadingAudio: 'Przesyłanie pliku audio...',
	UploadingFiles: 'Przesyłanie plików',
	UrlLink: 'Link URL',
	UsageCount: 'Użyto {count} razy',
	UsageLimitValue: '{used} z {limit} użytych',
	UsageValue: '{used} używane',
	Use: 'Używać',
	UseAiToAutomateYourWorkflow: 'Użyj sztucznej inteligencji do automatyzacji swojego przepływu pracy!',
	UseAsDefault: 'Użyj jako domyślnego',
	UseCustom: 'Użyj niestandardowego',
	UseDefault: 'Użyj domyślnego',
	UseDefaultFilters: 'Użyj domyślnych filtrów',
	UseTemplate: 'Użyj szablonu',
	UseThisCard: 'Użyj tej karty',
	UseValue: 'Użyj "{value}"',
	UseWorkspaceDefault: 'Użyj domyślnego obszaru roboczego',
	UserIsTyping: '{name} pisze...',
	Username: 'Nazwa użytkownika',
	Users: 'Użytkownicy',
	VAT: 'KADŹ',
	ValidUrl: 'Link URL musi być prawidłowym adresem URL.',
	Validate: 'Uprawomocnić',
	Validated: 'Zweryfikowany',
	Validating: 'Walidacja',
	ValidatingContent: 'Sprawdzanie treści...',
	ValidatingTranscripts: 'Sprawdzanie transkryptów...',
	ValidationConfirmPasswordRequired: 'Potwierdź hasło jest wymagane',
	ValidationDateMax: 'Musi być przed {max}',
	ValidationDateMin: 'Musi być po {min}',
	ValidationDateRange: 'Data rozpoczęcia i zakończenia jest wymagana',
	ValidationEndDateMustBeAfterStartDate: 'Data zakończenia musi być po dacie rozpoczęcia',
	ValidationMixedDefault: 'To jest nieprawidłowe',
	ValidationMixedRequired: 'To jest wymagane',
	ValidationNumberInteger: 'Musi być liczbą całkowitą',
	ValidationNumberMax: 'Musi być {max} lub mniej',
	ValidationNumberMin: 'Musi być {min} lub więcej',
	ValidationPasswordNotMatching: 'Hasła nie pasują',
	ValidationPrimaryAddressIsRequired: 'Adres jest wymagany, jeśli jest ustawiony jako domyślny',
	ValidationPrimaryPhoneNumberIsRequired: 'Numer telefonu jest wymagany, jeśli ustawiono go jako domyślny',
	ValidationServiceMustBeNotBeFuture: 'Usługa nie może być dzisiejszego dnia lub w przyszłości',
	ValidationStringEmail: 'Musi to być prawidłowy adres e-mail',
	ValidationStringMax: 'Musi mieć {max} lub mniej znaków',
	ValidationStringMin: 'Musi zawierać {min} lub więcej znaków',
	ValidationStringPhoneNumber: 'Musi to być prawidłowy numer telefonu',
	ValueMinutes: '{value} minut',
	VerbosityConcise: 'Zwięzły',
	VerbosityDetailed: 'Szczegółowy',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Bardzo szczegółowy',
	VerificationCode: 'Kod weryfikacyjny',
	VerificationEmailDescription:
		'Proszę wpisać swój adres e-mail i kod weryfikacyjny, który właśnie do Ciebie wysłaliśmy.',
	VerificationEmailSubtitle: 'Sprawdź folder ze spamem – jeśli wiadomość e-mail nie dotarła',
	VerificationEmailTitle: 'Zweryfikuj adres e-mail',
	VerificationOption: 'Weryfikacja adresu e-mail',
	Verified: 'Zweryfikowano',
	Verify: 'Zweryfikować',
	VerifyAndSubmit: 'Zweryfikuj i wyślij',
	VerifyEmail: 'Weryfikacja adresu e-mail',
	VerifyEmailAccessCode: 'Kod potwierdzający',
	VerifyEmailAddress: 'Zweryfikuj adres e-mail',
	VerifyEmailButton: 'Zweryfikuj i wyloguj się',
	VerifyEmailSentSnackbar: 'E-mail weryfikacyjny został wysłany. Sprawdź skrzynkę odbiorczą.',
	VerifyEmailSubTitle: 'Sprawdź folder ze spamem, jeśli wiadomość e-mail nie dotarła',
	VerifyEmailSuccessLogOutSnackbar: 'Sukces! Wyloguj się, aby zastosować zmiany.',
	VerifyEmailSuccessSnackbar: 'Sukces! E-mail zweryfikowany. Zaloguj się, aby kontynuować jako zweryfikowane konto.',
	VerifyEmailTitle: 'Zweryfikuj swój adres e-mail',
	VerifyNow: 'Zweryfikuj teraz',
	Veterinarian: 'Lekarz weterynarii',
	VideoCall: 'Rozmowa wideo',
	VideoCallAudioInputFailed: 'Urządzenie wejścia audio nie działa',
	VideoCallAudioInputFailedMessage: 'Otwórz ustawienia i sprawdź, czy źródło mikrofonu jest poprawnie ustawione',
	VideoCallChatBanner:
		'Wiadomości będą widoczne dla wszystkich uczestników połączenia i zostaną usunięte po zakończeniu połączenia.',
	VideoCallChatSendBtn: 'Wyślij wiadomość',
	VideoCallChatTitle: 'Pogawędzić',
	VideoCallDisconnectedMessage: 'Straciłeś połączenie sieciowe. Próba ponownego połączenia',
	VideoCallOptionInfo:
		'Carepatron będzie zarządzać połączeniami wideo podczas Twoich wizyt, jeśli Zoom nie został połączony',
	VideoCallTilePaused: 'Ten film został wstrzymany z powodu problemów z siecią',
	VideoCallTranscriptionFormDescription: 'Możesz zmienić te ustawienia w dowolnym momencie',
	VideoCallTranscriptionFormHeading: `Dostosuj swojego Scribe'a AI`,
	VideoCallTranscriptionFormLanguageField: 'Wygenerowany język wyjściowy',
	VideoCallTranscriptionFormNoteTemplateField: 'Ustaw domyślny szablon notatki',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Nie znaleziono szablonów z AI',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Wybierz szablon',
	VideoCallTranscriptionPronounField: 'Twoje zaimki',
	VideoCallTranscriptionRecordingNote:
		'Na zakończenie sesji otrzymasz wygenerowaną **{noteTemplate} notatkę** i transkrypcję.',
	VideoCallTranscriptionReferClientField: 'Odnieś się do Klienta jako',
	VideoCallTranscriptionReferPractitionerField: 'Odnieś się do Praktyka jako',
	VideoCallTranscriptionTitle: 'Pisarz AI',
	VideoCallTranscriptionVerbosityField: 'Gadatliwość',
	VideoCallTranscriptionWritingPerspectiveField: 'Perspektywa pisania',
	VideoCalls: 'Rozmowy wideo',
	VideoConferencing: 'Wideokonferencje',
	VideoOff: 'Wideo jest wyłączone',
	VideoOn: 'Wideo jest wyłączone',
	VideoQual360: 'Niska jakość (360p)',
	VideoQual540: 'Średnia jakość (540p)',
	VideoQual720: 'Wysoka jakość (720p)',
	View: 'Pogląd',
	ViewAll: 'Zobacz wszystko',
	ViewAppointment: 'Zobacz wizytę',
	ViewBy: 'Widok według',
	ViewClaim: 'Wyświetl roszczenie',
	ViewCollection: 'Zobacz kolekcję',
	ViewDetails: 'Zobacz szczegóły',
	ViewEnrollment: 'Zobacz zapisy',
	ViewPayment: 'Zobacz płatność',
	ViewRecord: 'Wyświetl rekord',
	ViewRemittanceAdvice: 'Zobacz przelew',
	ViewRemittanceAdviceHeader: 'Potwierdzenie przekazania roszczenia',
	ViewRemittanceAdviceSubheader: 'Roszczenie {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Zobacz ustawienia',
	ViewStripeDashboard: 'Wyświetl panel Stripe',
	ViewTemplate: 'Pokaż szablon',
	ViewTemplates: 'Wyświetl szablony',
	ViewableBy: 'Można oglądać przez',
	ViewableByHelper:
		'Ty i Zespół macie zawsze dostęp do notatek, które publikujecie. Możesz wybrać, czy chcesz udostępnić tę notatkę klientowi i/lub jego relacjom',
	Viewer: 'Dozorca',
	VirtualLocation: 'Wirtualna lokalizacja',
	VisibleTo: 'Widoczny dla',
	VisitOurHelpCentre: 'Odwiedź nasze centrum pomocy',
	VisualEffects: 'Efekty wizualne',
	VoiceFocus: 'Skupienie na głosie',
	VoiceFocusLabel: 'Filtruje dźwięk z mikrofonu, który nie jest mową',
	Void: 'Próżnia',
	VoidCancelPriorClaim: 'Unieważnij/Anuluj poprzednie roszczenie',
	WaitingforMins: 'Czekam od {count} min.',
	Warning: 'Ostrzeżenie',
	WatchAVideo: 'Oglądaj wideo',
	WatchDemoVideo: 'Obejrzyj film demonstracyjny',
	WebConference: 'Konferencja internetowa',
	WebConferenceOrVirtualLocation: 'Konferencja internetowa / lokalizacja wirtualna',
	WebDeveloper: 'Programista stron internetowych',
	WebsiteOptional: 'Strona internetowa <span>(opcjonalnie)</span>',
	WebsiteUrl: 'Adres URL witryny',
	Wednesday: 'Środa',
	Week: 'Tydzień',
	WeekPlural: '{count, plural, one {tydzień} other {tygodnie}}',
	Weekly: 'Tygodnik',
	WeeksPlural: '{age, plural, one {# tydzień} other {# tygodnie}}',
	WelcomeBack: 'Witamy ponownie',
	WelcomeBackName: 'Witaj ponownie, {name}',
	WelcomeName: 'Witaj {name}',
	WelcomeToCarepatron: 'Witamy w Carepatron',
	WhatCanIHelpWith: 'W czym mogę pomóc?',
	WhatDidYouLikeResponse: 'Co Ci się podobało w tej odpowiedzi?',
	WhatIsCarepatron: 'Czym jest Carepatron?',
	WhatMadeYouCancel: `Co skłoniło Cię do anulowania planu?
 Zaznacz wszystkie, które mają zastosowanie.`,
	WhatServicesDoYouOffer: 'Co<mark> Usługi</mark> oferujesz?',
	WhatServicesDoYouOfferDescription: 'Możesz edytować lub dodać więcej usług później.',
	WhatsYourAvailability: 'Jaka jest Twoja <mark>dostępność?</mark>',
	WhatsYourAvailabilityDescription: 'Możesz dodać więcej harmonogramów później.',
	WhatsYourBusinessName: 'Co jest twoje?<mark> nazwa firmy?</mark>',
	WhatsYourTeamSize: 'Co jest twoje?<mark> wielkość zespołu?</mark>',
	WhatsYourTeamSizeDescription: 'Pomoże nam to prawidłowo skonfigurować Twoją przestrzeń roboczą.',
	WhenThisHappens: 'Kiedy to się dzieje:',
	WhichBestDescribesYou: 'Który najlepszy<mark> opisuje Ciebie?</mark>',
	WhichPlatforms: 'Które platformy?',
	Wife: 'Żona',
	WorkflowDescription: 'Opis przepływu pracy',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Szablony mogą łączyć się z przepływami pracy w celu usprawnienia procesów. Wyświetl połączone przepływy pracy, aby łatwo je śledzić i aktualizować.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Połącz swoje SMS-y + e-maile na podstawie wspólnych wyzwalaczy',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Automatyzacja przepływów pracy',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Zautomatyzowane przepływy pracy',
	WorkflowTemplateConfigKey_Body: 'Treść',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Pokaż branding',
	WorkflowTemplateConfigKey_Content: 'Treść',
	WorkflowTemplateConfigKey_Footer: 'Stopka',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Pokaż stopkę',
	WorkflowTemplateConfigKey_Header: 'Nagłówek',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Pokaż nagłówek',
	WorkflowTemplateConfigKey_SecurityFooter: 'Stopka bezpieczeństwa',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Pokaż stopkę bezpieczeństwa',
	WorkflowTemplateConfigKey_Subject: 'Temat',
	WorkflowTemplateConfigKey_Title: 'Tytuł',
	WorkflowTemplateDeleteConfirmationMessage:
		'Czy na pewno chcesz usunąć ten szablon? Tej czynności nie można cofnąć.',
	WorkflowTemplateDeleteConfirmationTitle: 'Usuń szablon powiadomienia',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Czy na pewno? To usunie tylko wersję {locale} — inne języki nie zostaną dotknięte. Tej czynności nie można cofnąć.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Usuń szablon ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Szablon powiadomienia został usunięty pomyślnie',
	WorkflowTemplateEditorDetailsTab: 'Szczegóły szablonu',
	WorkflowTemplateEditorEmailContent: 'Treść wiadomości e-mail',
	WorkflowTemplateEditorEmailContentTab: 'Treść wiadomości e-mail',
	WorkflowTemplateEditorThemeTab: 'Temat',
	WorkflowTemplatePreviewerAlert: 'Podgląd wykorzystuje dane przykładowe, aby pokazać, co zobaczą Twoi klienci.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Czy na pewno? To spowoduje przywrócenie wersji do domyślnego szablonu systemu. Tej czynności nie można cofnąć.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Resetuj szablon',
	WorkflowTemplateSendTestEmail: 'Wyślij testowy email',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Spróbuj skonfigurować swoją pocztę elektroniczną, wysyłając do siebie wiadomość testową.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Adres e-mail odbiorcy',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Wyślij test',
	WorkflowTemplateSendTestEmailDialogTitle: 'Wyślij testowego maila',
	WorkflowTemplateSendTestEmailSuccess: 'Sukces! Twój <mark>{templateName}</mark> testowy e-mail został wysłany.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Zarządzaj swoimi szablonami i dodawaj wersje w wielu językach, aby skutecznie komunikować się z klientami.',
	WorkflowTemplateTemplateEditor: 'Edytor szablonu',
	WorkflowTemplateTranslateLocaleError: 'Coś poszło nie tak podczas tłumaczenia treści',
	WorkflowTemplateTranslateLocaleSuccess: 'Pomyślnie przetłumaczono treść na **{locale}**',
	WorkflowsAndReminders: 'Przepływy pracy ',
	WorkflowsManagement: 'Zarządzanie przepływami pracy',
	WorksheetAndHandout: 'Arkusz/Materiały do druku',
	WorksheetsAndHandoutsDescription: 'W celu angażowania i edukowania klientów',
	Workspace: 'Miejsce pracy',
	WorkspaceBranding: 'Branding przestrzeni roboczej',
	WorkspaceBrandingDescription: `Bez wysiłku zbuduj markę swojego miejsca pracy, nadając mu spójny styl, który odzwierciedla Twoją firmę.
 profesjonalizm i osobowość. Dostosuj faktury do rezerwacji online, aby uzyskać piękny
 doświadczenie klienta.`,
	WorkspaceName: 'Nazwa obszaru roboczego',
	Workspaces: 'Przestrzenie robocze',
	WriteOff: 'Odpisanie',
	WriteOffModalDescription:
		'Masz <mark>{count} {count, plural, one {pozycję do skreślenia} other {pozycji do skreślenia}}</mark> do skreślenia',
	WriteOffModalTitle: 'Korekta odpisu',
	WriteOffReasonHelperText: 'Jest to notatka wewnętrzna i Twój klient nie będzie jej widział.',
	WriteOffReasonPlaceholder:
		'Dodanie przyczyny odpisu może być pomocne podczas przeglądania transakcji podlegających rozliczeniu',
	WriteOffTotal: 'Całkowita amortyzacja ({currencyCode})',
	Writer: 'Pisarz',
	Yearly: 'Roczny',
	YearsPlural: '{age, plural, one {# rok} other {# lata}}',
	Yes: 'Tak',
	YesArchive: 'Tak, archiwum',
	YesDelete: 'Tak, usuń',
	YesDeleteOverride: 'Tak, usuń nadpisanie',
	YesDeleteSection: 'Tak, usuń',
	YesDisconnect: 'Tak, rozłącz',
	YesEnd: 'Tak, koniec',
	YesEndTranscription: 'Tak, zakończ transkrypcję',
	YesImFineWithThat: 'Tak, to mi odpowiada',
	YesLeave: 'Tak, wyjdź',
	YesMinimize: 'Tak, minimalizuj',
	YesOrNoAnswerTypeDescription: 'Skonfiguruj typ odpowiedzi',
	YesOrNoFormPrimaryText: 'Tak | Nie',
	YesOrNoFormSecondaryText: 'Wybierz opcję tak lub nie',
	YesProceed: 'Tak, kontynuuj',
	YesRemove: 'Tak, usuń',
	YesRestore: 'Tak, przywróć',
	YesStopIgnoring: 'Tak, przestań ignorować',
	YesTransfer: 'Tak, prześlij',
	Yesterday: 'Wczoraj',
	YogaInstructor: 'Instruktor Jogi',
	You: 'Ty',
	YouArePresenting: 'Prezentujesz',
	YouCanChooseMultiple: 'Możesz wybrać wiele',
	YouCanSelectMultiple: 'Możesz wybrać wiele',
	YouHaveOngoingTranscription: 'Masz trwającą transkrypcję',
	YourAnswer: 'Twoja odpowiedź',
	YourDisplayName: 'Twoja nazwa wyświetlana',
	YourSpreadsheetColumns: 'Kolumny arkusza kalkulacyjnego',
	YourTeam: 'Twój zespół',
	ZipCode: 'Kod pocztowy',
	Zoom: 'Brzęczenie',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Nie możesz dodać połączenia Zoom dla tego członka zespołu. <a>Więcej informacji znajdziesz w dokumentacji pomocy technicznej.</a>',
};

export default items;
