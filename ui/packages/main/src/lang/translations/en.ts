import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'AI prompts',
	ATeamMemberIsRequired: 'A team member is required',
	AboutClient: 'About Client',
	AcceptAppointment: 'Thank you for confirming your appointment',
	AcceptTermsAndConditionsRequired: 'Accept Terms & Conditions is required',
	Accepted: 'Accepted',
	AccessGiven: 'Access given',
	AccessPermissions: 'Access permissions',
	AccessType: 'Access type',
	Accident: 'Accident',
	Account: 'Account',
	AccountCredit: 'Account credit',
	Accountant: 'Accountant',
	Action: 'Action',
	Actions: 'Actions',
	Active: 'Active',
	ActiveTags: 'Active tags',
	ActiveUsers: 'Active users',
	Activity: 'Activity',
	Actor: 'Actor',
	Acupuncture: 'Acupuncture',
	Acupuncturist: 'Acupuncturist',
	Acupuncturists: 'Acupuncturists',
	AcuteManifestationOfAChronicCondition: 'Acute manifestation of a chronic condition',
	Add: 'Add',
	AddADescription: 'Add a description',
	AddALocation: 'Add location',
	AddASecondTimezone: 'Add a second timezone',
	AddAddress: 'Add address',
	AddAnother: '+ Add another',
	AddAnotherAccount: 'Add another account',
	AddAnotherContact: 'Add another contact',
	AddAnotherOption: 'Add another option',
	AddAnotherTeamMember: 'Add another team member',
	AddAvailablePayers: '+ Add available payers',
	AddAvailablePayersDescription:
		'Search payers to add to your workspace payers list. After adding them, you can manage enrollments or adjust the payer details as needed.',
	AddCaption: 'Add caption',
	AddClaim: 'Add claim',
	AddClientFilesModalDescription: 'To restrict access choose the options in the "Viewable by" checkboxes',
	AddClientFilesModalTitle: 'Upload files for {name}',
	AddClientNoteButton: 'Add note',
	AddClientNoteModalDescription:
		'Add content to your note. Use the "Viewable by" section to select one or more groups that can see this specific note.',
	AddClientNoteModalTitle: 'Add note',
	AddClientOwnerRelationshipModalDescription:
		'Inviting the client will allow them to manage their own profile information and manage user access to their profile information.',
	AddClientOwnerRelationshipModalTitle: 'Invite the client',
	AddCode: 'Add code',
	AddColAfter: 'Add column after',
	AddColBefore: 'Add column before',
	AddCollection: 'Add Collection',
	AddColor: 'Add color',
	AddColumn: 'Add column',
	AddContactRelationship: 'Add contact relationship',
	AddContacts: 'Add contacts',
	AddCustomField: 'Add custom field',
	AddDate: 'Add date',
	AddDescription: 'Add description',
	AddDetail: 'Add detail',
	AddDisplayName: 'Add display name',
	AddDxCode: 'Add diagnosis code',
	AddEmail: 'Add email',
	AddFamilyClientRelationshipModalDescription: `Inviting a family member will allow them to see care stories and the client's profile information. If they are invited as administrators, they will have access to update the client's profile information and manage user access.`,
	AddFamilyClientRelationshipModalTitle: 'Invite family member',
	AddField: 'Add field',
	AddFormField: 'Add form field',
	AddImages: 'Add images',
	AddInsurance: 'Add insurance',
	AddInvoice: 'Add invoice',
	AddLabel: 'Add label',
	AddLanguage: 'Add language',
	AddLocation: 'Add location',
	AddManually: 'Add manually',
	AddMessage: 'Add message',
	AddNewAction: 'Add new action',
	AddNewSection: 'Add new section',
	AddNote: 'Add note',
	AddOnlineBookingDetails: 'Add online booking details',
	AddPOS: 'Add POS',
	AddPaidInvoices: 'Add paid invoices',
	AddPayer: 'Add payer',
	AddPayment: 'Add payment',
	AddPaymentAdjustment: 'Add payment adjustment',
	AddPaymentAdjustmentDisabledDescription: 'Payment allocations will not be changed.',
	AddPaymentAdjustmentEnabledDescription: 'Amount available to allocate will be reduced.',
	AddPhoneNumber: 'Add phone number',
	AddPhysicalOrVirtualLocations: 'Add physical or virtual locations',
	AddQuestion: 'Add question',
	AddQuestionOrTitle: 'Add a question or title',
	AddRelationship: 'Add relationship',
	AddRelationshipModalTitle: 'Connect existing contact',
	AddRelationshipModalTitleNewClient: 'Connect new contact',
	AddRow: 'Add row',
	AddRowAbove: 'Add row above',
	AddRowBelow: 'Add row below',
	AddService: 'Add service',
	AddServiceLocation: 'Add service location',
	AddServiceToCollections: 'Add service to collections',
	AddServiceToOneOrMoreCollections: 'Add service to one or more collections',
	AddServices: 'Add services',
	AddSignature: 'Add signature',
	AddSignaturePlaceholder: 'Type additional details to include with your signature',
	AddSmartDataChips: 'Add smart data chips',
	AddStaffClientRelationshipsModalDescription:
		'Selecting staff will allow them to create and view care stories for this client. They will also be able to view client information.',
	AddStaffClientRelationshipsModalTitle: 'Add staff relationships',
	AddTag: 'Add a tag',
	AddTags: 'Add tags',
	AddTemplate: 'Add template',
	AddTimezone: 'Add timezone',
	AddToClaim: 'Add to claim',
	AddToCollection: 'Add to collection',
	AddToExisting: 'Add to existing',
	AddToStarred: 'Add to starred',
	AddUnclaimedItems: 'Add unclaimed items',
	AddUnrelatedContactWarning: `You have added someone who isn't a contact of {contact}. Ensure the content is relevant before proceeding with sharing.`,
	AddValue: 'Add "{value}"',
	AddVideoCall: 'Add video call',
	AddVideoOrVoiceCall: 'Add video or voice call',
	AddictionCounselor: 'Addiction Counselor',
	AddingManualPayerDisclaimer: `Adding a payer manually to your provider list doesn't set up an electronic claims filing connection with that payer, but can be used to create claims manually.`,
	AddingTeamMembersIncreaseCostAlert: 'Adding new team members will increase your monthly subscription.',
	Additional: 'Additional',
	AdditionalBillingProfiles: 'Additional billing profiles',
	AdditionalBillingProfilesSectionDescription:
		'Override the default billing information used for specific team members, payers or invoice templates.',
	AdditionalFeedback: 'Additional feedback',
	AddnNewWorkspace: 'New workspace',
	AddnNewWorkspaceSuccessSnackbar: 'Workspace has been created!',
	Address: 'Address',
	AddressNumberStreet: 'Address (No, street)',
	Adjustment: 'Adjustment',
	AdjustmentType: 'Adjustment type',
	Admin: 'Admin',
	Admins: 'Admins',
	AdminsOnly: 'Admins only',
	AdvancedPlanInclusionFive: 'Account manager',
	AdvancedPlanInclusionFour: 'Google analytics',
	AdvancedPlanInclusionHeader: 'Everything in Plus +',
	AdvancedPlanInclusionOne: 'Roles & permissions',
	AdvancedPlanInclusionSix: 'Data import support',
	AdvancedPlanInclusionThree: 'White labeling',
	AdvancedPlanInclusionTwo: '90 days deleted data retention',
	AdvancedPlanMessage: 'Stay in control of your practice’s needs. Review your current plan and monitor usage.',
	AdvancedSettings: 'Advanced settings',
	AdvancedSubscriptionPlanSubtitle: 'Expand your practice with all features',
	AdvancedSubscriptionPlanTitle: 'Advanced',
	AdvertisingManager: 'Advertising Manager',
	AerospaceEngineer: 'Aerospace Engineer',
	AgeYearsOld: '{age} years old',
	Agenda: 'Agenda',
	AgendaView: 'Agenda view',
	AiAskSupportedFileTypes: 'Supported file types: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'An assistant at your fingertips',
	AiCopilotDisclaimer: 'AI Copilot can make mistakes. Check important info.',
	AiCreateNewConversation: 'Create new conversation',
	AiEnhanceYourProductivity: 'Enhance your productivity',
	AiPoweredTemplates: 'AI-powered templates',
	AiScribeNoDeviceFoundErrorMessage:
		'It looks like your browser does not support this feature, or no compatible devices are available.',
	AiScribeUploadFormat: 'Supported file types: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'only 1 file at a time',
	AiShowConversationHistory: 'Show conversation history',
	AiSmartPromptNodePlaceholderText:
		'Type your custom prompt here to help generate accurate and personalised AI results.',
	AiSmartPromptPrimaryText: 'AI smart prompt',
	AiSmartPromptSecondaryText: 'Insert custom AI smart prompt',
	AiSmartReminders: 'AI smart reminders',
	AiTemplateBannerTitle: 'Simplify your work with AI-powered templates',
	AiTemplates: 'AI templates',
	AiTokens: 'AI Tokens',
	AiWorkBetterWithAi: 'Work better with AI',
	All: 'All',
	AllAppointments: 'All appointments',
	AllCategories: 'All categories',
	AllClients: 'All clients',
	AllContactPolicySelectorLabel: 'All contacts of <mark>{client}</mark>',
	AllContacts: 'All contacts',
	AllContactsOf: 'All contacts of ‘{name}’ ',
	AllDay: 'All day',
	AllInboxes: 'All Inboxes',
	AllIndustries: 'All industries',
	AllLocations: 'All locations',
	AllMeetings: 'All meetings',
	AllNotificationsRestoredMessage: 'All notifications restored',
	AllProfessions: 'All professions',
	AllReminders: 'All reminders',
	AllServices: 'All services',
	AllStatuses: 'All statuses',
	AllTags: 'All tags',
	AllTasks: 'All tasks',
	AllTeamMembers: 'All team members',
	AllTypes: 'All types',
	Allocated: 'Allocated',
	AllocatedItems: 'Allocated items',
	AllocationTableEmptyState: 'No payment allocations found',
	AllocationTotalWarningMessage: `The allocated amount exceeds the total payment amount.
Please review the line items below.`,
	AllowClientsToCancelAnytime: 'Allow clients to cancel at any time',
	AllowNewClient: 'Allow for new clients',
	AllowNewClientHelper: 'New clients can book this service',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Allow at least {hours} hours before appointment',
	AllowToUseSavedCard: 'Allow {provider} to use the saved card in the future',
	AllowVideoCalls: 'Allow video calls',
	AlreadyAdded: 'Already added',
	AlreadyHasAccess: 'Has access',
	AlreadyHasAccount: 'Already have an account?',
	Always: 'Always',
	AlwaysIgnore: 'Always ignore',
	Amount: 'Amount',
	AmountDue: 'Amount due',
	AmountOfReferralRequests: '{amount, plural, one {# referral request} other {# referral requests}}',
	AmountPaid: 'Amount paid',
	AnalyzingAudio: 'Analyzing audio...',
	AnalyzingInputContent: 'Analyzing input content...',
	AnalyzingRequest: 'Analyzing request...',
	AnalyzingTemplateContent: 'Analyzing template content...',
	And: 'and',
	Annually: 'Annually',
	Anonymous: 'Anonymous',
	AnswerExceeded: 'Your answer must be less than 300 characters.',
	AnyStatus: 'Any status',
	AppNotifications: 'Notifications',
	AppNotificationsClearanceHeading: 'Nice work! You’ve cleared all activity',
	AppNotificationsEmptyHeading: 'Your workspace activity will appear here shortly',
	AppNotificationsEmptySubtext: 'There are no actions to be taken for now',
	AppNotificationsIgnoredCount: '{total} ignored',
	AppNotificationsUnread: '{total} unread',
	Append: 'Append',
	Apply: 'Apply',
	ApplyAccountCredit: 'Apply account credit',
	ApplyDiscount: 'Apply discount',
	ApplyVisualEffects: 'Apply visual effects',
	ApplyVisualEffectsNotSupported: 'Apply visual effects not supported',
	Appointment: 'Appointment',
	AppointmentAssignedNotificationSubject: '{actorProfileName} has assigned you {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} has cancelled {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} has confirmed {appointmentName}',
	AppointmentDetails: 'Appointment details',
	AppointmentLocation: 'Appointment location',
	AppointmentLocationDescription:
		'Manage your default virtual and physical locations. When an appointment is scheduled these locations will be automatically applied.',
	AppointmentNotFound: 'Appointment not found',
	AppointmentReminder: 'Appointment reminder',
	AppointmentReminders: 'Appointment reminders',
	AppointmentRemindersInfo: 'Set automated reminders for client appointments to avoid no-shows and cancellations',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} has rescheduled {appointmentName}',
	AppointmentSaved: 'Appointment saved',
	AppointmentStatus: 'Appointment status',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0mins} other {{minutes}mins}} } one {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} other {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} }',
	AppointmentUndone: 'Appointment undone',
	Appointments: 'Appointments',
	Archive: 'Archive',
	ArchiveClients: 'Archive clients',
	Archived: 'Archived',
	AreYouAClient: 'Are you a client?',
	AreYouStillThere: 'Are you still there?',
	AreYouSure: 'Are you sure?',
	Arrangements: 'Arrangements',
	ArtTherapist: 'Art Therapist',
	Articles: 'Articles',
	Artist: 'Artist',
	AskAI: 'Ask AI',
	AskAiAddFormField: 'Add a form field',
	AskAiChangeFormality: 'Change formality',
	AskAiChangeToneToBeMoreProfessional: 'Change tone to be more professional',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Explain what this document is about',
	AskAiExplainWhatThisImageIsAbout: 'Explain what this image is about',
	AskAiFixSpellingAndGrammar: 'Fix spelling and grammar',
	AskAiGenerateACaptionForThisImage: 'Generate a caption for this image',
	AskAiGenerateFromThisPage: 'Generate from this page',
	AskAiGetStarted: 'Get started',
	AskAiGiveItAFriendlyTone: 'Give it a friendly tone',
	AskAiGreeting: 'Hi {firstName}! How can I assist you today?',
	AskAiHowCanIHelpWithYourContent: 'How can I help with your content?',
	AskAiInsert: 'Insert',
	AskAiMakeItMoreCasual: 'Make it more casual',
	AskAiMakeThisTextMoreConcise: 'Make this text more concise',
	AskAiMoreProfessional: 'More professional',
	AskAiOpenPreviousNote: 'Open a previous note',
	AskAiPondering: 'Pondering',
	AskAiReplace: 'Replace',
	AskAiReviewOrEditSelection: 'Review or edit selection',
	AskAiRuminating: 'Ruminating',
	AskAiSeeMore: 'See more',
	AskAiSimplifyLanguage: 'Simplify language',
	AskAiSomethingWentWrong: 'Something went wrong. If this issue persists, please contact us through our help center.',
	AskAiStartWithATemplate: 'Start with a template',
	AskAiSuccessfullyCopiedResponse: 'Successfully copied',
	AskAiSuccessfullyInsertedResponse: 'Successfully inserted',
	AskAiSuccessfullyReplacedResponse: 'Successfully replaced',
	AskAiSuggested: 'Suggested',
	AskAiSummariseTextIntoBulletPoints: 'Summarise text into bullet points',
	AskAiSummarizeNote: 'Summarize note',
	AskAiThinking: 'Thinking',
	AskAiToday: 'Today {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'What do you want to do with this form?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Write a professional note using the template',
	AskAskAiAnything: 'Ask AI anything',
	AskWriteSearchAnything: `Ask, write '@' or search for anything...`,
	Asking: 'Asking',
	Assessment: 'Assessment',
	Assessments: 'Assessments',
	AssessmentsCategoryDescription: 'For recording client evaluations',
	AssignClients: 'Assign clients',
	AssignNewClients: 'Assign clients',
	AssignServices: 'Assign services',
	AssignTeam: 'Assign team',
	AssignTeamMember: 'Assign team member',
	Assigned: 'Assigned',
	AssignedClients: 'Assigned clients',
	AssignedServices: 'Assigned services',
	AssignedServicesDescription:
		'View and manage your assigned services, adjusting the prices to reflect your custom rates. ',
	AssignedTeam: 'Assigned team',
	AthleticTrainer: 'Athletic Trainer',
	AttachFiles: 'Attach files',
	AttachLogo: 'Attach',
	Attachment: 'Attachment',
	AttachmentBlockedFileType: 'Blocked for security reasons!',
	AttachmentTooLargeFileSize: 'File too large',
	AttachmentUploadItemComplete: 'Complete',
	AttachmentUploadItemError: 'Upload failed',
	AttachmentUploadItemLoading: 'Loading',
	AttemptingToReconnect: 'Attempting to reconnect...',
	Attended: 'Attended',
	AttendeeBeingMutedTooltip: `Host has muted you. Use 'raise hand' to request unmute`,
	AttendeeWithId: 'Attendee {attendeeId}',
	Attendees: 'Attendees',
	AttendeesCount: '{count} attendees',
	Attending: 'Attending',
	Audiologist: 'Audiologist',
	Aunt: 'Aunt',
	Australia: 'Australia',
	AuthenticationCode: 'Authentication Code',
	AuthoriseProvider: 'Authorise {provider}',
	AuthorisedProviders: 'Authorised providers',
	AutoDeclineAllFutureOption: 'Only new events or appointments',
	AutoDeclineAllOption: 'New and existing events or appointments',
	AutoDeclinePrimaryText: 'Automatically decline events',
	AutoDeclineSecondaryText: 'Events during your out-of-office period will be auto-declined',
	AutogenerateBillings: 'Autogenerate billing documents',
	AutogenerateBillingsDescription:
		'Automated billing documents will be generated on the last day of the month. Invoices and superbill receipts can be created manually anytime.',
	AutomateWorkflows: 'Automate Workflows',
	AutomaticallySendSuperbill: 'Automatically send superbill receipts',
	AutomaticallySendSuperbillHelperText:
		'A superbill is a detailed receipt of the services provided to a client for insurance reimbursement',
	Automation: 'Automation',
	AutomationActionSendEmailLabel: 'Send Email',
	AutomationActionSendSMSLabel: 'Send SMS',
	AutomationAndReminders: 'Automation & Reminders',
	AutomationDeletedSuccessMessage: 'Successfully deleted automation',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Time event',
	AutomationParams_timeUnit: 'Unit',
	AutomationParams_timeValue: 'Number',
	AutomationPublishSuccessMessage: 'Automation published successfully',
	AutomationPublishWarningTooltip: 'Please recheck the automation config and ensure it got configured properly',
	AutomationTriggerEventCancelledDescription: 'Triggers when an event is cancelled or deleted',
	AutomationTriggerEventCancelledLabel: 'Event cancelled',
	AutomationTriggerEventCreatedDescription: 'Triggers when an event is created',
	AutomationTriggerEventCreatedLabel: 'New event',
	AutomationTriggerEventCreatedOrUpdatedDescription: `Triggers when an event is created or updated (except when it's cancelled)`,
	AutomationTriggerEventCreatedOrUpdatedLabel: 'New or updated event',
	AutomationTriggerEventEndedDescription: 'Triggers when an event ends',
	AutomationTriggerEventEndedLabel: 'Event ended',
	AutomationTriggerEventStartsDescription: 'Triggers when a specified amount of time before an event starts',
	AutomationTriggerEventStartsLabel: 'Event starts',
	Automations: 'Automations',
	Availability: 'Availability',
	AvailabilityDisableSchedule: 'Disable schedule',
	AvailabilityDisabled: 'Disabled',
	AvailabilityEnableSchedule: 'Enable schedule',
	AvailabilityEnabled: 'Enabled',
	AvailabilityNoActiveBanner:
		'You have turned off all your schedules. Clients cannot book you online, and all future appointments need to be confirmed manually.',
	AvailabilityNoActiveConfirmationDescription:
		'Disabling this availability will result in having no active schedules. Clients will not be able to book you online, and any bookings made by practitioners will fall outside of your working hours.',
	AvailabilityNoActiveConfirmationProceed: 'Yes, proceed',
	AvailabilityNoActiveConfirmationTitle: 'No active schedules',
	AvailabilityToggle: 'Enabled schedule',
	AvailabilityUnsetDate: 'No date set',
	AvailableLocations: 'Available locations',
	AvailablePayers: 'Available payers',
	AvailablePayersEmptyState: 'No payers selected',
	AvailableTimes: 'Available times',
	Back: 'Back',
	BackHome: 'Back home',
	BackToAppointment: 'Back to appointment',
	BackToLogin: 'Back to login',
	BackToMapColumns: 'Back to Map columns',
	BackToTemplates: 'Back to Templates',
	BackToUploadFile: 'Back to Upload file',
	Banker: 'Banker',
	BasicBlocks: 'Basic blocks',
	BeforeAppointment: 'Send {deliveryType} reminder {interval} {unit} before appointment',
	BehavioralAnalyst: 'Behavioral Analyst',
	BehavioralHealthTherapy: 'Behavioral health therapy',
	Beta: 'Beta',
	BillTo: 'Bill to',
	BillableItems: 'Billable items',
	BillableItemsEmptyState: 'No billable items have been found',
	Biller: 'Biller',
	Billing: 'Billing',
	BillingAddress: 'Billing address',
	BillingAndReceiptsUnauthorisedMessage: 'Invoices and payments view access is required to access this information.',
	BillingBillablesTab: 'Billables',
	BillingClaimsTab: 'Claims',
	BillingDetails: 'Billing details',
	BillingDocuments: 'Billing documents',
	BillingDocumentsClaimsTab: 'Claims',
	BillingDocumentsEmptyState: 'No {tabType} have been found',
	BillingDocumentsInvoicesTab: 'Invoices',
	BillingDocumentsSuperbillsTab: 'Superbills',
	BillingInformation: 'Billing information',
	BillingInvoicesTab: 'Invoices',
	BillingItems: 'Billing items',
	BillingPaymentsTab: 'Payments',
	BillingPeriod: 'Billing period',
	BillingProfile: 'Billing profile',
	BillingProfileOverridesDescription: 'Limit the usage of this billing profile to particular team members',
	BillingProfileOverridesHeader: 'Limit access',
	BillingProfileProviderType: 'Provider type',
	BillingProfileTypeIndividual: 'Practitioner',
	BillingProfileTypeIndividualSubLabel: 'Type 1 NPI',
	BillingProfileTypeOrganisation: 'Organisation',
	BillingProfileTypeOrganisationSubLabel: 'Type 2 NPI',
	BillingProfiles: 'Billing profiles',
	BillingProfilesEditHeader: 'Edit {name} billing profile',
	BillingProfilesNewHeader: 'New billing profile',
	BillingProfilesSectionDescription:
		'Manage your billing information for practitioners and insurance payers by setting up billing profiles that can be applied to invoices and insurance payouts.',
	BillingSearchPlaceholder: 'Search items',
	BillingSettings: 'Billing settings',
	BillingSuperbillsTab: 'Superbills',
	BiomedicalEngineer: 'Biomedical Engineer',
	BlankInvoice: 'Blank invoice',
	BlueShieldProviderNumber: 'Blue Shield provider number',
	Body: 'Body',
	Bold: 'Bold',
	BookAgain: 'Book again',
	BookAppointment: 'Book appointment',
	BookableOnline: 'Bookable online',
	BookableOnlineHelper: 'Clients can book this service online',
	BookedOnline: 'Booked online',
	Booking: 'Booking',
	BookingAnalyticsIntegrationPanelDescription:
		'Set up Google Tag Manager to track key actions and conversions in your online booking flow. Gather valuable data on user interactions to improve marketing efforts and optimize the booking experience.',
	BookingAnalyticsIntegrationPanelTitle: 'Analytics integration',
	BookingAndCancellationPolicies: 'Booking & cancellation policies',
	BookingButtonEmbed: 'Button',
	BookingButtonEmbedDescription: 'Adds an online booking button to your website',
	BookingDirectTextLink: 'Direct text link',
	BookingDirectTextLinkDescription: 'Opens the online booking page',
	BookingFormatLink: 'Format link',
	BookingFormatLinkButtonTitle: 'Button title',
	BookingInlineEmbed: 'Inline embed',
	BookingInlineEmbedDescription: 'Loads the online booking page directly in your website',
	BookingLink: 'Booking link',
	BookingLinkModalCopyText: 'Copy',
	BookingLinkModalDescription: 'Allow clients with this link to book any team member or services',
	BookingLinkModalHelpText: 'Learn how to set up online bookings',
	BookingLinkModalTitle: 'Share your booking link',
	BookingPolicies: 'Booking policies',
	BookingPoliciesDescription: 'Set when online bookings can be made by clients',
	BookingTimeUnitDays: 'days',
	BookingTimeUnitHours: 'hours',
	BookingTimeUnitMinutes: 'minutes',
	BookingTimeUnitMonths: 'months',
	BookingTimeUnitWeeks: 'weeks',
	BottomNavBilling: 'Billing',
	BottomNavGettingStarted: 'Home',
	BottomNavMore: 'More',
	BottomNavNotes: 'Notes',
	Brands: 'Brands',
	Brother: 'Brother',
	BrotherInLaw: 'Brother-in-law',
	BrowseOrDragFileHere: '<link>Browse</link> or drag file here',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} mins after',
	BufferAndLabel: 'and',
	BufferAppointmentLabel: 'an appointment',
	BufferBeforeTime: '{time} mins before',
	BufferTime: 'Buffer time',
	BufferTimeViewLabel: '{bufferBefore} mins before and {bufferAfter} mins after appointments',
	BulkArchiveClientsDescription: 'Are you sure you want to archive these clients? You can reactivate them later.',
	BulkArchiveSuccess: 'Successfully archived clients',
	BulkArchiveUndone: 'Bulk archive undone',
	BulkPermanentDeleteDescription:
		'This will delete <strong>{count} conversations</strong>. This action can’t be undone.',
	BulkPermanentDeleteTitle: 'Delete conversations forever',
	BulkUnarchiveSuccess: 'Sucessfully unarchived clients',
	BulletedList: 'Bulleted list',
	BusinessAddress: 'Business address',
	BusinessAddressOptional: 'Business address<span>(Optional)</span>',
	BusinessName: 'Business name',
	Button: 'Button',
	By: 'By',
	CHAMPUSIdentificationNumber: 'CHAMPUS identification number',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC is required',
	Calendar: 'Calendar',
	CalendarAppSyncFormDescription: 'Sync Carepatron events to',
	CalendarAppSyncPanelTitle: 'Connected app sync',
	CalendarDescription: 'Manage your appointments or set personal tasks and reminders',
	CalendarDetails: 'Calendar details',
	CalendarDetailsDescription: 'Manage your calendar and appointment display settings.',
	CalendarScheduleNew: 'Schedule new',
	CalendarSettings: 'Calendar settings',
	Call: 'Call',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> has joined the video call',
	CallChangeLayoutTextContent: 'Selection is saved for future meetings',
	CallIdlePrompt: 'Would you prefer to keep waiting to join, or would you like to try again later?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Sidebar',
	CallLayoutOptionSpotlight: 'Spotlight',
	CallLayoutOptionTiled: 'Tiled',
	CallNoAttendees: 'No atteendees in the meeting.',
	CallSessionExpiredError: 'Session expired. Call has been ended. Please try to join again.',
	CallWithPractitioner: 'Call with {practitioner}',
	CallsListCreateButton: 'New call',
	CallsListEmptyState: 'No active calls',
	CallsListItemEndCall: 'End call',
	CamWarningMessage: 'An issue has been detected with your camera',
	Camera: 'Camera',
	CameraAndMicIssueModalDescription: `Please enable Carepatron access to your camera and microphone.
		For more information, <a>follow this guide</a>`,
	CameraAndMicIssueModalTitle: 'Camera and microphone are blocked',
	CameraQuality: 'Camera quality',
	CameraSource: 'Camera source',
	CanModifyReadOnlyEvent: `You can't modify this event`,
	Canada: 'Canada',
	Cancel: 'Cancel',
	CancelClientImportDescription: 'Are you sure you want to cancel this import?',
	CancelClientImportPrimaryAction: 'Yes, cancel import',
	CancelClientImportSecondaryAction: 'Keep editing',
	CancelClientImportTitle: 'Cancel importing clients',
	CancelImportButton: 'Cancel import',
	CancelPlan: 'Cancel plan',
	CancelPlanConfirmation: `Cancelling the plan will automatically charge your account with any outstanding balances you have for this month. 
	If you want to downgrade your billed users, you can simply remove team members and Carepatron will automatically update your subscription price.`,
	CancelSend: 'Cancel send',
	CancelSubscription: 'Cancel subscription',
	Canceled: 'Canceled',
	CancellationPolicy: 'Cancellation policy',
	Cancelled: 'Canceled',
	CannotContainSpecialCharactersError: 'Cannot contain {specialCharacters}',
	CannotDeleteInvoice: 'Invoices paid through online payments cannot be deleted',
	CannotMoveCarepatronStatusOutsideGroup: `<b>{status}</b> can't be moved out of the <b>{group}</b> group`,
	CannotMoveServiceOutsideCollections: 'Service cannot be moved outside collections',
	CapeTown: 'Cape Town',
	Caption: 'Caption',
	CaptureNameFieldLabel: 'The name you would like others to refer to you',
	CapturePaymentMethod: 'Capture payment method',
	CapturingAudio: 'Capturing audio',
	CapturingSignature: 'Capturing signature...',
	CardInformation: 'Card information',
	CardNumberRequired: 'Card number is required',
	CardiacRehabilitationSpecialist: 'Cardiac Rehabilitation Specialist',
	Cardiologist: 'Cardiologist',
	CareAiNoConversations: 'No conversations yet',
	CareAiNoConversationsDescription: 'Start a conversation with {aiName} to get started',
	CareAssistant: 'Care Assistant',
	CareManager: 'Care Manager',
	Caregiver: 'Caregiver',
	CaregiverCreateModalDescription:
		'Adding staff as administrators will allow them create and manage care stories. It also gives them full access to create and manage clients.',
	CaregiverCreateModalTitle: 'New team member',
	CaregiverListCantAddStaffInfoTitle:
		'You have reached the maximum number of staff for your subscription. Please upgrade your plan to add more staff members.',
	CaregiverListCreateButton: 'New team member',
	CaregiverListEmptyState: 'No caregivers added',
	CaregiversListItemRemoveStaff: 'Remove staff',
	CarepatronApp: 'Carepatron app',
	CarepatronCommunity: 'Community',
	CarepatronFieldAddress: 'Address',
	CarepatronFieldAssignedStaff: 'Assigned staff',
	CarepatronFieldBirthDate: 'Birth date',
	CarepatronFieldEmail: 'Email',
	CarepatronFieldEmploymentStatus: 'Employment status',
	CarepatronFieldEthnicity: 'Ethnicity',
	CarepatronFieldFirstName: 'First name',
	CarepatronFieldGender: 'Gender',
	CarepatronFieldIdentificationNumber: 'Identification number',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Label',
	CarepatronFieldLastName: 'Last name',
	CarepatronFieldLivingArrangements: 'Living arrangements',
	CarepatronFieldMiddleNames: 'Middle name',
	CarepatronFieldOccupation: 'Occupation',
	CarepatronFieldPhoneNumber: 'Phone number',
	CarepatronFieldRelationshipStatus: 'Relationship status',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 statuses max.',
	CarepatronFieldTags: 'Tags',
	CarepatronFields: 'Carepatron fields',
	Cash: 'Cash',
	Category: 'Category',
	CategoryInputPlaceholder: 'Choose a template category',
	CenterAlign: 'Center align',
	Central: 'Central',
	ChangeLayout: 'Change layout',
	ChangeLogo: 'Change',
	ChangePassword: 'Change password',
	ChangePasswordFailureSnackbar: 'Sorry, your password was not changed. Check that your old password is correct.',
	ChangePasswordHelperInfo: 'Minimum length of {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Successfully changed password! Next time you log in, make sure to use that password.',
	ChangeSubscription: 'Change subscription',
	ChangesNotAllowed: 'Changes can’t be made to this field',
	ChargesDisabled: 'Charges disabled',
	ChargesEnabled: 'Charges enabled',
	ChargesStatus: 'Charges status',
	ChartAndDiagram: 'Chart/Diagram',
	ChartsAndDiagramsCategoryDescription: 'For illustrating client data and progress',
	ChatEditMessage: 'Edit message',
	ChatReplyTo: 'Reply to {name}',
	ChatTypeMessageTo: 'Message {name}',
	Check: 'Check',
	CheckList: 'Checklist',
	Chef: 'Chef',
	Chiropractic: 'Chiropractic',
	Chiropractor: 'Chiropractor',
	Chiropractors: 'Chiropractors',
	ChooseACollection: 'Choose a collection',
	ChooseAContact: 'Choose a contact',
	ChooseAccountTypeHeader: 'Which describes you best?',
	ChooseAction: 'Choose action',
	ChooseAnAccount: 'Choose an account',
	ChooseAnOption: 'Choose an option',
	ChooseBillingProfile: 'Choose billing profile',
	ChooseClaim: 'Choose claim',
	ChooseCollection: 'Choose collection',
	ChooseColor: 'Choose color',
	ChooseCustomDate: 'Choose custom date',
	ChooseDateAndTime: 'Choose date and time',
	ChooseDxCodes: 'Choose diagnosis codes',
	ChooseEventType: 'Choose event type',
	ChooseFileButton: 'Choose a file',
	ChooseFolder: 'Choose folder',
	ChooseInbox: 'Choose inbox',
	ChooseMethod: 'Choose method',
	ChooseNewOwner: 'Choose new owner',
	ChooseOrganization: 'Choose Organization',
	ChoosePassword: 'Choose password',
	ChoosePayer: 'Choose payer',
	ChoosePaymentMethod: 'Choose a payment method',
	ChoosePhysicalOrRemoteLocations: 'Enter or choose location',
	ChoosePlan: 'Choose {plan}',
	ChooseProfessional: 'Choose Professional',
	ChooseServices: 'Choose services',
	ChooseSource: 'Choose source',
	ChooseSourceDescription: `Choose where you are importing clients from – whether it's a file or another software platform.`,
	ChooseTags: 'Choose tags',
	ChooseTaxName: 'Choose tax name',
	ChooseTeamMembers: 'Choose team members',
	ChooseTheme: 'Choose theme',
	ChooseTrigger: 'Choose trigger',
	ChooseYourProvider: 'Choose your provider',
	CircularProgressWithLabel: '{value}%',
	City: 'City',
	CivilEngineer: 'Civil Engineer',
	Claim: 'Claim',
	ClaimAddReferringProvider: 'Add referring provider',
	ClaimAddRenderingProvider: 'Add rendering provider',
	ClaimAmount: 'Claim amount',
	ClaimAmountPaidHelpContent:
		'The amount paid is the payment received from the patient or other payers. Enter the total amount the patient and/or other payers paid on the covered services only.',
	ClaimAmountPaidHelpSubtitle: 'Field 29',
	ClaimAmountPaidHelpTitle: 'Amount paid',
	ClaimBillingProfileTypeIndividual: 'Individual',
	ClaimBillingProfileTypeOrganisation: 'Organisation',
	ClaimChooseRenderingProviderOrTeamMember: 'Choose rendering provider or team member',
	ClaimClientInsurancePolicies: 'Client insurance policies ',
	ClaimCreatedAction: '<mark>Claim {claimNumber}</mark> created',
	ClaimDeniedAction: '<mark>Claim {claimNumber}</mark> was denied by <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Search ICD 10 diagnosis codes',
	ClaimDiagnosisSelectorHelpContent: `The “Diagnosis or injury” is the sign, symptom, complaint, or condition of the patient relating to the service(s) on the claim.
Up to 12 ICD 10 diagnosis codes can be selected.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Field 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnosis or injury',
	ClaimDiagnosticCodesEmptyError: 'At least one diagnosis code is required',
	ClaimDoIncludeReferrerInformation: 'Do include referrer information on CMS1500',
	ClaimERAReceivedAction: 'Electronic remittance received from <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction: `Electronic remittance received
	<mark>Payment {paymentReference}</mark> for <b>{paymentAmount}</b> by <b>{payerNumber} {payerName}</b> was recorded`,
	ClaimExportedAction: '<mark>Claim {claimNumber}</mark> was exported as <b>{attachmentType}</b>',
	ClaimFieldClient: 'Client or contact name',
	ClaimFieldClientAddress: 'Client address',
	ClaimFieldClientAddressDescription: `Enter the client's address. The first line is for the street address. Do not use punctuation (commas or periods) or any symbols in the address. If reporting a foreign address, contact the payer for specific reporting instructions.`,
	ClaimFieldClientAddressSubtitle: 'Field 5',
	ClaimFieldClientDateOfBirth: `Client's Date of birth`,
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Field 3',
	ClaimFieldClientDateOfBirthDescription: `Enter the clients 8-digit birth date (MM/DD/YYYY). The client's birth date is information that will identify the client and it distinguishes persons with similar names.`,
	ClaimFieldClientDescription: `The client's name' is the name of the person who received the treatment or supplies.`,
	ClaimFieldClientSexDescription: `The 'sex' is information that will identify the client and it distinguishes persons with similar names.`,
	ClaimFieldClientSubtitle: 'Field 2',
	ClaimFiling: 'Claim filing',
	ClaimHistorySubtitle: 'Insurance • Claim {number}',
	ClaimIncidentAutoAccident: 'Auto accident?',
	ClaimIncidentConditionRelatedTo: 'Is client’s condition related to',
	ClaimIncidentConditionRelatedToHelpContent:
		'This information indicates whether the client’s illness or injury is related to the employment, auto accident, or other accident. Employment (current or previous) would indicate that the condition is related to the client’s job or workplace. Auto accident would indicate that the conditions is the result of an automobile accident. Other accident would indicate that the condition is the result of any other type of accident.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Fields 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Is client’s condition related to',
	ClaimIncidentCurrentIllness: 'Current illness, injury or pregnancy',
	ClaimIncidentCurrentIllnessHelpContent:
		'The date of current illness, injury, or pregnancy identifies the first date of onset of illness, the actual date of injury, or the LMP for pregnancy.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Field 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Dates of current illness, injury, or pregnancy (LMP)',
	ClaimIncidentDate: 'Date',
	ClaimIncidentDateFrom: 'Date from',
	ClaimIncidentDateTo: 'Date to',
	ClaimIncidentEmploymentRelated: 'Employment',
	ClaimIncidentEmploymentRelatedDesc: '(Current or previous)',
	ClaimIncidentHospitalizationDatesLabel: 'Hospitalisation dates related to current services',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'The hospitalisation dates related to current services refers to an client stay and indicated the admission and discharge dates associated with the service(s) on the claim.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Field 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Hospitalisation dates related to current services',
	ClaimIncidentInformation: 'Incident information',
	ClaimIncidentOtherAccident: 'Other accident?',
	ClaimIncidentOtherAssociatedDate: 'Other associated date',
	ClaimIncidentOtherAssociatedDateHelpContent: `The other date identifies additional date information about the client's condition or treatment.`,
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Field 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Other date',
	ClaimIncidentQualifier: 'Qualifier',
	ClaimIncidentQualifierPlaceholder: 'Choose qualifier',
	ClaimIncidentUnableToWorkDatesLabel: 'Client was unable to work in current occupation',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Dates client was unable to work in current occupation is the time span the client is or was unable to work',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Field 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Dates client was unable to work in current occupation',
	ClaimIncludeReferrerInformation: 'Include referrer information on CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `The type of health insurance coverage applicable to this claim. Other indicates health insurance including HMOs, commercial insurance, automobile accident, liability, or workers' compensation.
This information directs the claim to the correct program and may establish primary liability.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Field 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Coverage type',
	ClaimInsuranceGroupIdHelpContent: `Enter the insured's policy or group number as it appears on the insured's health care identification card.

The “Insured's Policy, Group, or FECA Number” is the alphanumeric identifier for the health, auto, or other insurance plan coverage. The FECA number is the 9-character alphanumeric identifier assigned to a patient claiming work-related condition.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Field 11',
	ClaimInsuranceGroupIdHelpTitle: 'Insured’s Policy, Group, or FECA Number',
	ClaimInsuranceMemberIdHelpContent: `Enter the insured’s ID number as shown on insured’s ID card for the payer to which the claim is being submitted.
If the patient has a unique Member Identification Number assigned by the payer, then enter that number in this field.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Field 1a',
	ClaimInsuranceMemberIdHelpTitle: `Insured's Member ID`,
	ClaimInsurancePayer: 'Insurance payer',
	ClaimManualPaymentAction: '<mark>Payment {paymentReference}</mark> for <b>{paymentAmount}</b> recorded',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Additional claim information',
	ClaimMiscAdditionalClaimInformationHelpContent: `Please refer to the current instructions from the public or private payer regarding the use of this field. Report the appropriate qualifier, when available, for the information being entered.
Do not enter a space, hyphen, or other separator between the qualifier and the information.`,
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Field 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Additional claim information',
	ClaimMiscClaimCodes: 'Claim codes',
	ClaimMiscOriginalReferenceNumber: 'Original reference number',
	ClaimMiscPatientsAccountNumber: `Patient's account number`,
	ClaimMiscPatientsAccountNumberHelpContent:
		'The patient’s account number is the identifier assigned by the provider.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Field 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Patient’s account number',
	ClaimMiscPriorAuthorizationNumber: 'Prior authorization number',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'The prior authourisation number is the payer assigned number authorising the service/s.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Field 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Prior authorisation number',
	ClaimMiscResubmissionCode: 'Resubmission code',
	ClaimMiscResubmissionCodeHelpContent:
		'Resubmission means the code and original reference number assigned by the destination payer or receiver to indicate a previously submitted claim or encounter.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Field 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Resubmission and / or Original Reference Number',
	ClaimNumber: 'Claim number',
	ClaimNumberFormat: 'Claim #{number}',
	ClaimOrderingProvider: 'Ordering provider',
	ClaimOtherId: 'Other ID',
	ClaimOtherIdPlaceholder: 'Choose an option',
	ClaimOtherIdQualifier: 'Other ID qualifier',
	ClaimOtherIdQualifierPlaceholder: 'Choose ID qualifier',
	ClaimPlaceOfService: 'Place of service',
	ClaimPlaceOfServicePlaceholder: 'Add POS',
	ClaimPolicyHolderRelationship: 'Policy holder relationship',
	ClaimPolicyInformation: 'Policy information',
	ClaimPolicyTelephone: 'Telephone (include area code)',
	ClaimReceivedAction: '<mark>Claim {claimNumber}</mark> received by <b>{name}</b>',
	ClaimReferringProvider: 'Referring provider',
	ClaimReferringProviderEmpty: 'No referring provider/s added',
	ClaimReferringProviderHelpContent:
		'The name entered is the referring provider, ordering provider, or supervising provider who referred, ordered, or supervised the service(s) or supply(ies) on the claim. The qualifier indicates the role of the provider being reporte.',
	ClaimReferringProviderHelpSubtitle: 'Field 17',
	ClaimReferringProviderHelpTitle: 'Name of referring provider or source',
	ClaimReferringProviderQualifier: 'Qualifier',
	ClaimReferringProviderQualifierPlaceholder: 'Choose qualifier',
	ClaimRejectedAction: '<mark>Claim {claimNumber}</mark> was rejected by <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'ID number',
	ClaimRenderingProviderOrTeamMember: 'Rendering provider or team member',
	ClaimRestoredAction: '<mark>Claim {claimNumber}</mark> was restored',
	ClaimServiceFacility: 'Service facility',
	ClaimServiceFacilityLocationHelpContent:
		'The name and address of the facility where services were rendered, identifies the site where service(s) were provided.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a and 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Field 32, 32a and 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Service facility',
	ClaimServiceFacilityPlaceholder: 'Choose service facility or location',
	ClaimServiceLabChargesHelpContent: `Complete this field when claiming for purchased services provided by an entity other than the billing provider.
Each purchased service must be reported on a separate claim as only one charge can be entered on the CMS1500 form.`,
	ClaimServiceLabChargesHelpSubtitle: 'Field 20',
	ClaimServiceLabChargesHelpTitle: 'Outside lab charges',
	ClaimServiceLineServiceHelpContent:
		'"Procedures, Services or Supplies" identify the medical services and procedures provided to the patient.',
	ClaimServiceLineServiceHelpSubtitle: 'Field 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedures, Services or Supplies',
	ClaimServiceLinesEmptyError: 'At least one service line is required',
	ClaimServiceSupplementaryInfoHelpContent: `Add additional narrative description of services provided using applicable qualifiers.
Do not enter a space, hyphen, or other separator between the qualifier and information.

For complete instructions on adding supplementary information review the CMS 1500 claim form instructions.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Field 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Supplementary info',
	ClaimSettingsBillingMethodTitle: 'Client billing method',
	ClaimSettingsClientSignatureDescription:
		'I have consent to release medical or other information necessary to process insurance claims.',
	ClaimSettingsClientSignatureTitle: 'Client signature on file',
	ClaimSettingsConsentLabel: 'Consent required to process insurance claims:',
	ClaimSettingsDescription: 'Choose the client billing method to ensure smooth payment processing:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} has an active insurance policy. To enable insurance billing, update the <mark>client billing method</mark> to <mark>Insurance</mark>.',
	ClaimSettingsInsuranceDescription: 'Costs reimbursed by insurance',
	ClaimSettingsInsuranceTitle: 'Insurance',
	ClaimSettingsNoPoliciesAlertDescription: 'Add an insurance policy to enable insurance claims.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'I have consent to receive insurance payments for services provided.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Policy holder signature on file',
	ClaimSettingsSelfPayDescription: 'Client will pay for appointments',
	ClaimSettingsSelfPayTitle: 'Self-pay',
	ClaimSettingsTitle: 'Claim settings',
	ClaimSexSelectorPlaceholder: 'Male / Female',
	ClaimStatusChangedAction: '<mark>Claim {claimNumber}</mark> status updated',
	ClaimSubmittedAction:
		'<mark>Claim {claimNumber}</mark> submitted to <b>{payerClearingHouse}</b> for <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Claim #{claimNumber}',
	ClaimSupervisingProvider: 'Supervising provider',
	ClaimSupplementaryInfo: 'Supplementary info',
	ClaimSupplementaryInfoPlaceholder: 'Add supplementary info',
	ClaimTrashedAction: '<mark>Claim {claimNumber}</mark> was deleted',
	ClaimValidationFailure: 'Failed to validate claim',
	ClaimsEmptyStateDescription: 'No claims have been found.',
	ClainInsuranceTelephone: 'Insurance Telephone (include area code)',
	Classic: 'Classic',
	Clear: 'Clear',
	ClearAll: 'Clear all',
	ClearSearchFilter: 'Clear',
	ClearingHouse: 'Clearing house',
	ClearingHouseClaimId: 'Claim.MD Id',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Clearing house reference',
	ClearingHouseUnavailableError: 'The clearing house is currently unavailable. Please try again later.',
	ClickToUpload: 'Click to upload',
	Client: 'Client',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} added {noteTitle, select, undefined { a note } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Choose clients and their relationships',
	ClientAndRelationshipSelectorTitle: 'All clients and their relationships',
	ClientAndRelationshipSelectorTitle1: 'All relationship of ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'If you are waiting for a video call, it will appear here shortly. If you are having any issues please contact the person who initiated it.',
	ClientAppSubHeaderMyDocumentation: 'My documentation',
	ClientAppointment: 'Client Appointment',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} has booked {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'No appointments have been found',
	ClientAppointmentsEmptyStateTitle: `Keep track of your clients' upcoming and historic appointments and their attendance`,
	ClientArchivedSuccessfulSnackbar: 'Successfully archived <b>{name}</b>',
	ClientBalance: 'Client balance',
	ClientBilling: 'Billing',
	ClientBillingAddPaymentMethodDescription:
		'Add and manage your client’s payment methods to streamline their invoicing and billing process.',
	ClientBillingAndPaymentDueDate: 'Due date',
	ClientBillingAndPaymentHistory: 'Billing and payment history',
	ClientBillingAndPaymentInvoices: 'Invoices',
	ClientBillingAndPaymentIssueDate: 'Issue date',
	ClientBillingAndPaymentPrice: 'Price',
	ClientBillingAndPaymentReceipt: 'Receipt',
	ClientBillingAndPaymentServices: 'Services',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Team {count, plural, one {member} other {members}} assigned!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Team {count, plural, one {member} other {members}} unassigned!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tags added!',
	ClientDuplicatesDeviewDescription:
		'Merge multiple client records into one to unify all data—notes, documents, appointments, invoices, and conversations.',
	ClientDuplicatesPageMergeHeader: `Choose the data you'd like to keep`,
	ClientDuplicatesReviewHeader: 'Compare potential duplicate records for merge',
	ClientEmailChangeWarningDescription: `Updating the client's email will remove their access to any shared documentation, and will grant access to the user with the new email`,
	ClientFieldDateDescription: 'Format date',
	ClientFieldDateLabel: 'Date',
	ClientFieldDateRangeDescription: 'A range of dates',
	ClientFieldDateRangeLabel: 'Date range',
	ClientFieldDateShowDateDescription: 'e.g. 29 years',
	ClientFieldDateShowDateRangeDescription: 'e.g. 2 weeks',
	ClientFieldEmailDescription: 'Email address',
	ClientFieldEmailLabel: 'Email',
	ClientFieldLabel: 'Field label',
	ClientFieldLinearScaleDescription: 'Scale options 1-10',
	ClientFieldLinearScaleLabel: 'Linear scale',
	ClientFieldLocationDescription: 'Physical or postal address',
	ClientFieldLocationLabel: 'Location',
	ClientFieldLongTextDescription: 'Long text area',
	ClientFieldLongTextLabel: 'Paragraph',
	ClientFieldMultipleChoiceDropdownDescription: 'Choose multiple options from list',
	ClientFieldMultipleChoiceDropdownLabel: 'Multiple choice dropdown',
	ClientFieldPhoneNumberDescription: 'Phone number',
	ClientFieldPhoneNumberLabel: 'Phone',
	ClientFieldPlaceholder: 'Choose a client field type',
	ClientFieldSingleChoiceDropdownDescription: 'Choose only one option from list',
	ClientFieldSingleChoiceDropdownLabel: 'Single choice dropdown',
	ClientFieldTextDescription: 'Text input field',
	ClientFieldTextLabel: 'Text',
	ClientFieldYesOrNoDescription: 'Choose from yes or no options',
	ClientFieldYesOrNoLabel: 'Yes | No',
	ClientFileFormAccessLevelDescription:
		'You and the Team always have access to files you upload. You can choose to share this file with the client and/or their relationships',
	ClientFileSavedSuccessSnackbar: 'File saved!',
	ClientFilesPageEmptyStateText: 'No files uploaded',
	ClientFilesPageUploadFileButton: 'Upload files',
	ClientHeaderBilling: 'Billing',
	ClientHeaderBillingAndReceipts: 'Billing & receipts',
	ClientHeaderDocumentation: 'Documentation',
	ClientHeaderDocuments: 'Documents',
	ClientHeaderFile: 'Document',
	ClientHeaderHistory: 'Medical history',
	ClientHeaderInbox: 'Inbox',
	ClientHeaderNote: 'Note',
	ClientHeaderOverview: 'Overview',
	ClientHeaderProfile: 'Personal',
	ClientHeaderRelationship: 'Relationship',
	ClientHeaderRelationships: 'Relationships',
	ClientId: 'Client ID',
	ClientImportProcessingDescription: 'File still processing. We will notify you when this is done.',
	ClientImportReadyForMappingDescription:
		'We have finished pre-processing your file. Would you like to map columns to complete this import?',
	ClientImportReadyForMappingNotificationSubject:
		'Client import pre-processing is complete. The file is now ready for mapping.',
	ClientInAppMessaging: 'Client In-app messaging',
	ClientInfoAddField: 'Add another field',
	ClientInfoAddRow: 'Add row',
	ClientInfoAlertMessage: 'Any information filled out in this section will populate the client record.',
	ClientInfoFormPrimaryText: 'Client information',
	ClientInfoFormSecondaryText: 'Gather contact details',
	ClientInfoPlaceholder: `Client name, Email address, Phone number
Physical address,
Date of birth`,
	ClientInformation: 'Client information',
	ClientInsuranceTabLabel: 'Insurance',
	ClientIntakeFormsNotSupported: `Form templates are not currently supported through client intakes.
Create and share them as client notes instead.`,
	ClientIntakeModalDescription:
		'An intake email will be sent to your client asking them to complete their profile, upload relevant medical or referral documents. They will be given Client Portal access.',
	ClientIntakeModalTitle: 'Send intake to {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Success! Your intake has been saved.',
	ClientIntakeSuccessSnackbar: 'Success! Your intake has been saved and a confirmation email sent.',
	ClientIsChargedProcessingFee: 'Your customers will pay the processing fee',
	ClientListCreateButton: 'New client',
	ClientListEmptyState: 'No clients added',
	ClientListPageItemArchive: 'Remove client',
	ClientListPageItemRemoveAccess: 'Remove my access',
	ClientLocalizationPanelDescription: `The client's preferred language and timezone.`,
	ClientLocalizationPanelTitle: 'Language and timezone',
	ClientManagementAndEHR: 'Client Management & EHR',
	ClientMergeResultSummaryBanner:
		'Merging records consolidates all client data, including notes, documents, appointments, invoices, and conversations. Verify accuracy before continuing.',
	ClientMergeResultSummaryTitle: 'Merge result summary',
	ClientModalTitle: 'New client',
	ClientMustHaveEmaillAccessErrorText: 'Clients/Contacts with no emails',
	ClientMustHavePortalAccessErrorText: 'Clients/Contacts will be required to sign up',
	ClientMustHaveZoomAppConnectedErrorText: 'Connect Zoom via Settings > Connected Apps',
	ClientNameFormat: 'Client name format',
	ClientNotFormAccessLevel: 'Viewable by:',
	ClientNotFormAccessLevelDescription:
		'You and the Team always have access to notes you publish. You can choose to share this note with the client and/or their relationships',
	ClientNotRegistered: 'Not registered',
	ClientNoteFormAddFileButton: 'Attach files',
	ClientNoteFormChooseAClient: 'Choose a client/contact to continue',
	ClientNoteFormContent: 'Content',
	ClientNoteItemDeleteConfirmationModalDescription: `Once deleted you can't retrieve this note again.`,
	ClientNotePublishedAndLockSuccessSnackbar: 'Note published and locked.',
	ClientNotePublishedSuccessSnackbar: 'Note published!',
	ClientNotes: 'Client notes',
	ClientNotesEmptyStateText: `To add notes, go to a client's profile and click the Notes tab.`,
	ClientOnboardingChoosePasswordTitle1: 'Almost Done!',
	ClientOnboardingChoosePasswordTitle2: 'Choose a password',
	ClientOnboardingCompleteIntake: 'Complete intake',
	ClientOnboardingConfirmationScreenText: `You've provided all the information {providerName} requires.
	Confirm your email address to start your onboarding. If you don't receive it right away, please check your spam folder.`,
	ClientOnboardingConfirmationScreenTitle: 'Great! Check your inbox.',
	ClientOnboardingDashboardButton: 'Go to Dashboard',
	ClientOnboardingHealthRecordsDesc1: 'Do you want to share any referral letters, documents with {providerName}? ',
	ClientOnboardingHealthRecordsDescription: 'Add Description (optional)',
	ClientOnboardingHealthRecordsTitle: 'Documentation',
	ClientOnboardingPasswordRequirements: 'Requirements',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimum 9 characters required',
	ClientOnboardingProviderIntroSignupButton: 'Sign up for myself',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Sign up for a family member',
	ClientOnboardingProviderIntroTitle: '{name} has invited you to join their Carepatron platform',
	ClientOnboardingRegistrationInstructions: 'Enter your personal details below.',
	ClientOnboardingRegistrationTitle: `First we'll  need some personal details`,
	ClientOnboardingStepFormsAndAgreements: 'Forms and Agreements',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Please complete the following forms for {providerName} intake process',
	ClientOnboardingStepHealthDetails: 'Health Details',
	ClientOnboardingStepPassword: 'Password',
	ClientOnboardingStepYourDetails: 'Your Details',
	ClientPaymentMethodDescription:
		'Save a payment method against your profile to make your next appointment booking and invoicing faster and more secure.',
	ClientPortal: 'Client Portal',
	ClientPortalDashboardEmptyDescription: 'Your appointment history and attendance will show up here.',
	ClientPortalDashboardEmptyTitle:
		'Keep track of all upcoming, requested and past appointments along with your attendance',
	ClientPreferredNotificationPanelDescription:
		'Manage your client’s preferred method to receive updates and notifications via:',
	ClientPreferredNotificationPanelTitle: 'Preferred notification method',
	ClientProcessingFee: 'Payment includes ({currencyCode}) {amount} processing fee',
	ClientProfileAddress: 'Address',
	ClientProfileDOB: 'Date of birth',
	ClientProfileEmailHelperText: 'Adding an email grants portal access',
	ClientProfileEmailHelperTextMoreInfo:
		'Granting the client access to the portal allows team members to share notes, files and other documentation',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Identification number',
	ClientRelationshipsAddClientOwnerButton: 'Invite the client',
	ClientRelationshipsAddFamilyButton: 'Invite family member',
	ClientRelationshipsAddStaffButton: 'Add staff access',
	ClientRelationshipsEmptyStateText: 'No relationships added',
	ClientRemovedSuccessSnackbar: 'Client successfully removed.',
	ClientResponsibility: 'Client responsibility',
	ClientSavedSuccessSnackbar: 'Client successfully saved.',
	ClientTableClientName: 'Client name',
	ClientTablePhone: 'Phone',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Successfully unarchived <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} edited {noteTitle, select, undefined { a note } other {{noteTitle}}}',
	ClientView: 'Client view',
	Clients: 'Clients',
	ClientsTable: 'Clients Table',
	ClinicalFormat: 'Clinical format',
	ClinicalPsychologist: 'Clinical Psychologist',
	Close: 'Close',
	CloseImportClientsModal: 'Are you sure you want to cancel importing clients?',
	CloseReactions: 'Close reactions',
	Closed: 'Closed',
	Coaching: 'Coaching',
	Code: 'Code',
	CodeErrorMessage: 'Code is required',
	CodePlaceholder: 'Code',
	Coinsurance: 'Co-insurance',
	Collection: 'Collection',
	CollectionName: 'Collection Name',
	Collections: 'Collections',
	ColorAppointmentsBy: 'Color appointments by',
	ColorTheme: 'Color theme',
	ColourCalendarBy: 'Color calendar by',
	ComingSoon: 'Coming soon',
	Community: 'Community',
	CommunityHealthLead: 'Community Health Lead',
	CommunityHealthWorker: 'Community Health Worker',
	CommunityTemplatesSectionDescription: 'Created by the Carepatron community',
	CommunityTemplatesSectionTitle: 'Community',
	CommunityUser: 'Community User',
	Complete: 'Complete',
	CompleteAndLock: 'Complete and lock',
	CompleteSetup: 'Complete Setup',
	CompleteSetupSuccessDescription: 'You’ve completed some key steps towards mastering Carepatron.',
	CompleteSetupSuccessDescription2: 'Unlock more ways to help streamline your practice and support your clients.',
	CompleteSetupSuccessTitle: 'Success! You’re doing amazing!',
	CompleteStripeSetup: 'Complete Stripe setup',
	Completed: 'Completed',
	ComposeSms: 'Compose SMS',
	ComputerSystemsAnalyst: 'Computer Systems Analyst',
	Confirm: 'Confirm',
	ConfirmDeleteAccountDescription:
		'You are about to delete your account. This action cannot be undone. If you wish to proceed, please confirm below.',
	ConfirmDeleteActionDescription: 'Are you sure you want to delete this action? This cannot be undone',
	ConfirmDeleteAutomationDescription:
		'Are you sure you want to delete this automation? This action cannot be undone.',
	ConfirmDeleteScheduleDescription:
		'Deleting <strong>{scheduleName}</strong> schedule will remove it from your schedules and it may change your online service available. This action can’t be undone.',
	ConfirmDraftResponseContinue: 'Continue with response',
	ConfirmDraftResponseDescription:
		'If you close this page your response will remain as a draft. You can comeback and continue at any time.',
	ConfirmDraftResponseSubmitResponse: 'Submit response',
	ConfirmDraftResponseTitle: `Your response hasn't been submitted`,
	ConfirmIfUserIsClientDescription: `The sign up form you filled out is for Providers (i.e. health teams/organisation).
If this is a mistake you can choose "Continue as client" and we'll get you setup for your client portal`,
	ConfirmIfUserIsClientNoButton: 'Sign up as Provider',
	ConfirmIfUserIsClientTitle: `It looks like you're a client`,
	ConfirmIfUserIsClientYesButton: 'Continue as client',
	ConfirmKeepSeparate: 'Confirm keep separate',
	ConfirmMerge: 'Confirm merge',
	ConfirmPassword: 'Confirm password',
	ConfirmRevertClaim: 'Yes, revert status',
	ConfirmSignupAccessCode: 'Confirmation code',
	ConfirmSignupButtom: 'Confirm',
	ConfirmSignupDescription: `Please enter your email address and the confirmation code we've just sent you.`,
	ConfirmSignupSubTitle: `Check Spam folder - if the email hasn't arrived`,
	ConfirmSignupSuccessSnackbar: `Great, we've confirmed your account! Now you can login using your email and password`,
	ConfirmSignupTitle: 'Confirm account',
	ConfirmSignupUsername: 'Email',
	ConfirmSubscriptionUpdate: 'Confirm subscription {price} {isMonthly, select, true {per month} other {per year}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Once the clients are deleted, you will no longer be able to access their information.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Delete {count, plural, one {# client} other {# clients}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Once the contacts are deleted, you will no longer be able to access their information.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Delete {count, plural, one {# contact} other {# contacts}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'This is a permanent action. Once the team members are deleted, you will no longer be able to access their information.',
	ConfirmationModalBulkDeleteMembersTitleId: 'Delete {count, plural, one {# team member} other {# team members}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Closing this note will end any ongoing transcriptions. Are you sure you want to proceed?',
	ConfirmationModalDeleteClientField:
		'This is a permanent action. Once the field is deleted it will no longer be accessible on your remaining clients.',
	ConfirmationModalDeleteSectionMessage:
		'Once deleted, all questions in this section will be removed. This action can not be undone.',
	ConfirmationModalDeleteService:
		'This is a permanent action. Once the service is deleted it will no longer be accessible on your workspace.',
	ConfirmationModalDeleteServiceGroup: `Deleting a collection will remove all services from the group and will return to your service list. This action can't be undone.`,
	ConfirmationModalDeleteTranscript: 'Are you sure you want to delete transcript?',
	ConfirmationModalDescriptionDeleteClient:
		'Once the client is deleted, you will no longer be able to access the client information.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Once you remove your access, you will no longer be able to view the client information.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Their profile will not be deleted, just removed as a relationship of this client.',
	ConfirmationModalDescriptionRemoveStaff: 'Are you sure you want to remove this person from the provider?',
	ConfirmationModalEndSession: 'Are you sure you want to end session?',
	ConfirmationModalTitle: 'Are you sure?',
	Confirmed: 'Confirmed',
	ConflictTimezoneWarningMessage: 'Conflicts may occur due to multiple timezones',
	Connect: 'Connect',
	ConnectExistingClientOrContact: 'Create new client/contact',
	ConnectInboxGoogleDescription: 'Add a Gmail account or Google group list',
	ConnectInboxMicrosoftDescription: 'Add a Outlook, Office365 or Exchange account',
	ConnectInboxModalDescription:
		'Connect your apps to seamlessly send, receive, and track all your communications in one centralized place.',
	ConnectInboxModalExistingDescription:
		'Use an existing connection from your connected apps settings to streamline the configuration process.',
	ConnectInboxModalExistingTitle: 'Existing connected app in Carepatron',
	ConnectInboxModalTitle: 'Connect inbox',
	ConnectToStripe: 'Connect to Stripe',
	ConnectZoom: 'Connect Zoom',
	ConnectZoomModalDescription: 'Allow Carepatron to manage video calls for your appointments.',
	ConnectedAppDisconnectedNotificationSubject: 'We have lost connection to {account} account. Please reconnect',
	ConnectedAppSyncDescription:
		'Manage connected apps to create events in 3rd-party calendars directly from Carepatron.',
	ConnectedApps: 'Connected apps',
	ConnectedAppsGMailDescription: 'Add Gmail accounts or Google group list',
	ConnectedAppsGoogleCalendarDescription: 'Add calendars accounts or Google group list',
	ConnectedAppsGoogleDescription: 'Add your Gmail account and sync Google calendars',
	ConnectedAppsMicrosoftDescription: 'Add a Outlook, Office365 or Exchange account',
	ConnectedCalendars: 'Connected Calendars',
	ConsentDocumentation: 'Forms and agreements',
	ConsentDocumentationPublicTemplateError:
		'For security reasons, you can only choose templates from your team (non-public).',
	ConstructionWorker: 'Construction Worker',
	Consultant: 'Consultant',
	Contact: 'Contact',
	ContactAccessTypeHelperText: 'Allows family admins to update info',
	ContactAccessTypeHelperTextMoreInfo: 'This will allow you to share notes/documents about {clientFirstName}',
	ContactAddressLabelBilling: 'Billing',
	ContactAddressLabelHome: 'Home',
	ContactAddressLabelOthers: 'Others',
	ContactAddressLabelWork: 'Work',
	ContactChangeConfirmation:
		'Changing the invoice contact will remove all line items related to <mark>{contactName}</mark>',
	ContactDetails: 'Contact details',
	ContactEmailLabelOthers: 'Others',
	ContactEmailLabelPersonal: 'Personal',
	ContactEmailLabelSchool: 'School',
	ContactEmailLabelWork: 'Work',
	ContactInformation: 'Contact Information',
	ContactInformationText: 'Contact information',
	ContactListCreateButton: 'New contact',
	ContactName: 'Contact name',
	ContactPhoneLabelHome: 'Home',
	ContactPhoneLabelMobile: 'Mobile',
	ContactPhoneLabelSchool: 'School',
	ContactPhoneLabelWork: 'Work',
	ContactRelationship: 'Contact relationship',
	ContactRelationshipFormAccessType: 'Grant access to shared information',
	ContactRelationshipGrantAccessInfo: 'This will allow you to share notes & documents',
	ContactSupport: 'Contact support',
	Contacts: 'Contacts',
	ContainerIdNotSet: 'Container ID not set',
	Contemporary: 'Contemporary',
	Continue: 'Continue',
	ContinueDictating: 'Continue dictating',
	ContinueEditing: 'Continue editing',
	ContinueImport: 'Continue import',
	ContinueTranscription: 'Continue transcription',
	ContinueWithApple: 'Continue with Apple',
	ContinueWithGoogle: 'Continue with Google',
	Conversation: 'Conversation',
	Copay: 'Co-pay',
	CopayOrCoinsurance: 'Co-pay or Co-insurance',
	Copayment: 'Co-payment',
	CopiedToClipboard: 'Copied to clipboard',
	Copy: 'Copy',
	CopyAddressSuccessSnackbar: 'Copied address to clipboard',
	CopyCode: 'Copy code',
	CopyCodeToClipboardSuccess: 'Code copied to clipboard',
	CopyEmailAddressSuccessSnackbar: 'Copied email address to clipboard',
	CopyLink: 'Copy link',
	CopyLinkForCall: 'Copy this link to share this call:',
	CopyLinkSuccessSnackbar: 'Copied link to clipboard',
	CopyMeetingLink: 'Copy meeting link',
	CopyPaymentLink: 'Copy payment link',
	CopyPhoneNumberSuccessSnackbar: 'Copied phone number to clipboard',
	CopyTemplateLink: 'Copy link to template',
	CopyTemplateLinkSuccess: 'Copied link to clipboard',
	CopyToClipboardError: 'Could not copy to clipboard. Please try again.',
	CopyToTeamTemplates: 'Copy to Team templates',
	CopyToWorkspace: 'Copy to workspace',
	Cosmetologist: 'Cosmetologist',
	Cost: 'Cost',
	CostErrorMessage: 'Cost is required',
	Counseling: 'Counseling',
	Counselor: 'Counselor',
	Counselors: 'Counselors',
	CountInvoicesAdded: '{count, plural, one {# Invoice added} other {# Invoices added}}',
	CountNotesAdded: '{count, plural, one {# Note added} other {# Notes added}}',
	CountSelected: '{count} selected',
	CountTimes: '{count} times',
	Country: 'Country',
	Cousin: 'Cousin',
	CoverageType: 'Coverage type',
	Covered: 'Covered',
	Create: 'Create',
	CreateANewClient: 'Create a new client',
	CreateAccount: 'Create account',
	CreateAndSignNotes: 'Create and sign note with clients',
	CreateAvailabilityScheduleFailure: 'Failed to create new availability schedule',
	CreateAvailabilityScheduleSuccess: 'Successfully created new availability schedule',
	CreateBillingItems: 'Create billing items',
	CreateCallFormButton: 'Start call',
	CreateCallFormInviteOnly: 'Invite only',
	CreateCallFormInviteOnlyMoreInfo:
		'Only people invited to this call can join. To share this call with others, simply uncheck this and copy/paste the link on the next page',
	CreateCallFormRecipients: 'Recipients',
	CreateCallFormRegion: 'Hosting region',
	CreateCallModalAddClientContactSelectorLabel: 'Client contacts',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Search by client name',
	CreateCallModalAddStaffSelectorLabel: 'Team members (optional)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Search by staff name',
	CreateCallModalDescription:
		'Start a call and invite staff members and/or contacts. Alternatively, you can uncheck the "Private" box to make this call shareable to anyone with Carepatron',
	CreateCallModalTitle: 'Start a call',
	CreateCallModalTitleLabel: 'Title (optional)',
	CreateCallNoPersonIdToolTip: 'Only contacts/clients with portal access can join calls',
	CreateClaim: 'Create claim',
	CreateClaimCompletedMessage: 'Your claim has been created.',
	CreateClientModalTitle: 'New client',
	CreateContactModalTitle: 'New contact',
	CreateContactRelationshipButton: 'Add relationship',
	CreateContactSelectorDefaultOption: '+ Create contact',
	CreateContactWithRelationshipFormAccessType: 'Grant access to shared info ',
	CreateDocumentDnDPrompt: 'Drag and drop to upload files',
	CreateDocumentSizeLimit: 'Size limit per file {size}MB. {total} files total.',
	CreateFreeAccount: 'Create free account',
	CreateInvoice: 'Create invoice',
	CreateLink: 'Create link',
	CreateNew: 'Create new',
	CreateNewAppointment: 'Create new appointment',
	CreateNewClaim: 'Create a new claim',
	CreateNewClaimForAClient: 'Create new claim for a client.',
	CreateNewClient: 'Create new client',
	CreateNewConnection: 'New connection',
	CreateNewContact: 'Create new contact',
	CreateNewField: 'Create new field',
	CreateNewLocation: 'New location',
	CreateNewService: 'Create new service',
	CreateNewServiceGroupFailure: 'Failed to create new collection',
	CreateNewServiceGroupMenu: 'New collection',
	CreateNewServiceGroupSuccess: 'Successfully created new collection',
	CreateNewServiceMenu: 'New service',
	CreateNewTeamMember: 'Create new team member',
	CreateNewTemplate: 'New template',
	CreateNote: 'Create note',
	CreateSuperbillReceipt: 'New superbill',
	CreateSuperbillReceiptSuccess: 'Successfully created Superbill receipt',
	CreateTemplateFolderSuccessMessage: 'Successfully created {folderTitle}',
	Created: 'Created',
	CreatedAt: 'Created {timestamp}',
	Credit: 'Credit',
	CreditAdded: 'Credit applied',
	CreditAdjustment: 'Credit adjustment',
	CreditAdjustmentReasonHelperText: 'This is an internal note and will not be visible to your client.',
	CreditAdjustmentReasonPlaceholder: 'Adding an adjustment reason can help when reviewing billable transactions',
	CreditAmount: '{amount} CR',
	CreditBalance: 'Credit balance',
	CreditCard: 'Credit card',
	CreditCardExpire: 'Expires {exp_month}/{exp_year}',
	CreditCardNumber: 'Credit card number',
	CreditDebitCard: 'Card',
	CreditIssued: 'Credit issued',
	CreditsUsed: 'Credits used',
	Crop: 'Crop',
	Currency: 'Currency',
	CurrentCredit: 'Current credit',
	CurrentEventTime: 'Current event time',
	CurrentPlan: 'Current plan',
	Custom: 'Custom',
	CustomRange: 'Custom range',
	CustomRate: 'Custom rate',
	CustomRecurrence: 'Custom recurrence',
	CustomServiceAvailability: 'Service availability',
	CustomerBalance: 'Customer balance',
	CustomerName: 'Customer name',
	CustomerNameIsRequired: 'Customer name is required',
	CustomerServiceRepresentative: 'Customer Service Representative',
	CustomiseAppointments: 'Customize appointments',
	CustomiseBookingLink: 'Customize booking options',
	CustomiseBookingLinkServicesInfo: 'Clients can only choose bookable services',
	CustomiseBookingLinkServicesLabel: 'Services',
	CustomiseClientRecordsAndWorkspace: 'Customize your client records and workspace',
	CustomiseClientSettings: 'Customize client settings',
	Customize: 'Customize',
	CustomizeAppearance: 'Customize appearance',
	CustomizeAppearanceDesc:
		'Customize your online booking appearance to match your brand and optmise how your services are displayed to clients.',
	CustomizeClientFields: 'Customize client fields',
	CustomizeInvoiceTemplate: 'Customize invoice template',
	CustomizeInvoiceTemplateDescription: 'Effortlessly create professional invoices that reflects your brand.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX is required',
	Daily: 'Daily',
	DanceTherapist: 'Dance Therapist',
	DangerZone: 'Danger zone',
	Dashboard: 'Dashboard',
	Date: 'Date',
	DateAndTime: 'Date & time',
	DateDue: 'Date due',
	DateErrorMessage: 'Date is required',
	DateFormPrimaryText: 'Date',
	DateFormSecondaryText: 'Choose from a datepicker',
	DateIssued: 'Date issued',
	DateOfPayment: 'Date of payment',
	DateOfService: 'Date of service',
	DateOverride: 'Date override',
	DateOverrideColor: 'Date override color',
	DateOverrideInfo:
		'Date overrides allows practitioners to manually adjust their availability for specific dates by overriding regular schedules.',
	DateOverrideInfoBanner:
		'Only specified services for this date override can be booked in these timeslots; no other online bookings allowed.',
	DateOverrides: 'Date overrides',
	DatePickerFormPrimaryText: 'Date',
	DatePickerFormSecondaryText: 'Choose a date',
	DateRange: 'Date range',
	DateRangeFormPrimaryText: 'Date range',
	DateRangeFormSecondaryText: 'Choose a date range',
	DateReceived: 'Date received',
	DateSpecificHours: 'Date specific hours',
	DateSpecificHoursDescription:
		'Add dates when your availability changes from your scheduled hours or to offer a service on a specific date.',
	DateUploaded: 'Uploaded {date, date, medium}',
	Dates: 'Dates',
	Daughter: 'Daughter',
	Day: 'Day',
	DayPlural: '{count, plural, one {day} other {days}}',
	Days: 'Days',
	DaysPlural: '{age, plural, one {# day} other {# days}}',
	DeFacto: 'De facto',
	Deactivated: 'Deactivated',
	Debit: 'Debit',
	DecreaseIndent: 'Decrease indent',
	Deductibles: 'Deductibles',
	Default: 'Default',
	DefaultBillingProfile: 'Default billing profile',
	DefaultDescription: 'Default description',
	DefaultEndOfLine: 'No more items',
	DefaultInPerson: 'Client appointments',
	DefaultInvoiceTitle: 'Default title',
	DefaultNotificationSubject: `You've received a new notification for {notificationType}`,
	DefaultPaymentMethod: 'Default payment method',
	DefaultService: 'Default service',
	DefaultValue: 'Default',
	DefaultVideo: 'Client video appointment email',
	DefinedTemplateType: '{invoiceTemplate} template',
	Delete: 'Delete',
	DeleteAccountButton: 'Delete account',
	DeleteAccountDescription: 'Delete your account from the platform',
	DeleteAccountPanelInfoAlert:
		'You must delete your workspaces prior to deleting your profile. To proceed, switch to a workspace and select Settings > Workspace Settings.',
	DeleteAccountTitle: 'Delete account',
	DeleteAppointment: 'Delete appointment',
	DeleteAppointmentDescription: 'Are you sure you want to delete this appointment? You can restore it later.',
	DeleteAvailabilityScheduleFailure: 'Failed to delete availability schedule',
	DeleteAvailabilityScheduleSuccess: 'Successfully deleted availability schedule',
	DeleteBillable: 'Delete billable',
	DeleteBillableConfirmationMessage: 'Are you sure you want to delete this billable? This action cannot be undone.',
	DeleteBillingProfileConfirmationMessage: 'This will permanently remove the billing profile.',
	DeleteCardConfirmation:
		'This is a permanent action. Once the card is deleted, you will no longer be able to access it.',
	DeleteCategory: 'Delete category (this is not permanent unless changes are saved)',
	DeleteClientEventConfirmationDescription: 'This will be permanently removed.',
	DeleteClients: 'Delete clients',
	DeleteCollection: 'Delete Collection',
	DeleteColumn: 'Delete column',
	DeleteConversationConfirmationDescription: 'Delete this conversation forever. This action can’t be undone.',
	DeleteConversationConfirmationTitle: 'Delete conversation forever',
	DeleteExternalEventDescription: 'Are you sure you want to delete this appointment?',
	DeleteFileConfirmationModalPrompt: `Once deleted you can't retrieve this file again.`,
	DeleteFolder: 'Delete folder',
	DeleteFolderConfirmationMessage:
		'Are you sure you want to delete this folder {name}? All items inside this folder will also be deleted. You may restore this later. ',
	DeleteForever: 'Delete forever',
	DeleteInsurancePayerConfirmationMessage:
		'Removing {payer} will delete it from your list of insurance payers. This action is permanent and cannot be restored.',
	DeleteInsurancePayerFailure: 'Failed to delete insurance payer',
	DeleteInsurancePolicyConfirmationMessage: 'This will permanently remove the insurance policy.',
	DeleteInvoiceConfirmationDescription:
		'This action cannot be undone. It will permanently delete the invoice and all payments associated with it.',
	DeleteLocationConfirmation:
		'Deleting a location is a permanent action. Once you delete it, you will no longer have access to it. This action can not be undone.',
	DeletePayer: 'Delete Payer',
	DeletePracticeWorkspace: 'Delete practice workspace',
	DeletePracticeWorkspaceDescription: 'Permanently delete this practice workspace',
	DeletePracticeWorkspaceFailedSnackbar: 'Failed to delete workspace',
	DeletePracticeWorkspaceModalCancelButton: 'Yes, cancel my subscription',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Before you proceed with the deletion of your workspace, you must cancel your subscription first.',
	DeletePracticeWorkspaceModalConfirmButton: 'Yes, permanently delete workspace',
	DeletePracticeWorkspaceModalDescription:
		'{name} workspace will be permanently deleted and all team members will lose access. Download any important data or messages that you may need before the deletion takes place. This action can not be undone.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'This decision was made due to:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Reason',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Please tell us why you’d like to delete your account.',
	DeletePracticeWorkspaceModalTitle: 'Are you sure?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'All team members access has been removed',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} has been successfully deleted',
	DeletePublicTemplateContent: `This will only delete the public template and not your team's template.`,
	DeleteRecurringAppointmentModalTitle: 'Delete repeating appointment',
	DeleteRecurringEventModalTitle: 'Delete repeating meeting',
	DeleteRecurringReminderModalTitle: 'Delete repeating reminder',
	DeleteRecurringTaskModalTitle: 'Delete repeating task',
	DeleteReminderConfirmation:
		'This is a permanent action. Once the reminder is deleted, you will no longer be able to access it. Will only affect new appointments',
	DeleteSection: 'Delete section',
	DeleteSectionInfo: `Deleting the section <strong>{section}</strong> will hide all existing fields within it. This action can't be undone.`,
	DeleteSectionWarning:
		'Core fields can’t be deleted and will be moved to the existing section <strong>{section}</strong>.',
	DeleteServiceFailure: 'Failed to delete service',
	DeleteServiceSuccess: 'Successfully deleted service',
	DeleteStaffScheduleOverrideDescription:
		'Deleting this date override on {value} will remove it from your schedules and it may change your online service available. This action can’t be undone.',
	DeleteSuperbillConfirmationDescription:
		'This action cannot be undone. It will permanently delete the Superbill receipt.',
	DeleteSuperbillFailure: 'Failed to delete Superbill receipt',
	DeleteSuperbillSuccess: 'Successfully deleted Superbill receipt',
	DeleteTaxRateConfirmationDescription: 'Are you sure you want to delete this tax rate?',
	DeleteTemplateContent: 'This action cannot be undone',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} successfully deleted',
	DeleteTemplateSuccessMessage: '{templateTitle} successfully deleted ',
	DeleteTemplateTitle: 'Are you sure you want to delete this template?',
	DeleteTranscript: 'Delete transcript',
	DeleteWorkspace: 'Delete workspace',
	Deleted: 'Deleted',
	DeletedBy: 'Deleted by',
	DeletedContact: 'Deleted contact',
	DeletedOn: 'Deleted on',
	DeletedStatusLabel: 'Deleted status',
	DeletedUserTooltip: 'This client has been deleted',
	DeliveryMethod: 'Delivery Method',
	Demo: 'Demo',
	Denied: 'Denied',
	Dental: 'Dental',
	DentalAssistant: 'Dental Assistant',
	DentalHygienist: 'Dental Hygienist',
	Dentist: 'Dentist',
	Dentists: 'Dentists',
	Description: 'Description',
	DescriptionMustNotExceed: 'Description must not exceed {max} characters',
	DetailDurationWithStaff: '{duration} mins{staffName, select, null {} other { with {staffName}}}',
	Details: 'Details',
	Devices: 'Devices',
	Diagnosis: 'Diagnosis',
	DiagnosisAndBillingItems: 'Diagnosis & billing items',
	DiagnosisCode: 'Diagnosis code',
	DiagnosisCodeErrorMessage: 'A diagnosis code is required',
	DiagnosisCodeSelectorPlaceholder: 'Search and add from ICD-10 diagnostic codes',
	DiagnosisCodeSelectorTooltip:
		'Diagnosis codes are used to automate superbills receipts for insurance reimbursement',
	DiagnosticCodes: 'Diagnostic codes',
	Dictate: 'Dictate',
	DictatingIn: 'Dictating in',
	Dictation: 'Dictation',
	DidNotAttend: 'Did not attend',
	DidNotComplete: 'Did not complete',
	DidNotProviderEnoughValue: 'Didn’t provide enough value',
	DidntProvideEnoughValue: `Didn't provide enough value`,
	DieteticsOrNutrition: 'Dietetics or nutrition',
	Dietician: 'Dietician',
	Dieticians: 'Dieticians',
	Dietitian: 'Dietitian',
	DigitalSign: 'Sign here:',
	DigitalSignHelp: '(Click/press down to draw)',
	DirectDebit: 'Direct debit',
	DirectTextLink: 'Direct text link',
	Disable: 'Disable',
	DisabledEmailInfo: 'We cannot update your email address as your account is not managed by us',
	Discard: 'Discard',
	DiscardChanges: 'Discard changes',
	DiscardDrafts: 'Discard drafts',
	Disconnect: 'Disconnect',
	DisconnectAppConfirmation: 'Do you want to disconnect this app?',
	DisconnectAppConfirmationDescription: 'Are you sure you want to disconnect this app?',
	DisconnectAppConfirmationTitle: 'Disconnect app',
	Discount: 'Discount',
	DisplayCalendar: 'Display in Carepatron',
	DisplayName: 'Display name',
	DisplayedToClients: 'Displayed to clients',
	DiversionalTherapist: 'Diversional Therapist',
	DoItLater: 'Do it later',
	DoNotImport: 'Do not import',
	DoNotSend: 'Do not send',
	DoThisLater: 'Do this later',
	DoYouWantToEndSession: 'Do you want to continue, or end your session now?',
	Doctor: 'Doctor',
	Doctors: 'Doctors',
	DoesNotRepeat: `Doesn't repeat`,
	DoesntWorkWellWithExistingTools: `Doesn't work well with our existing tools or workflows`,
	DogWalker: 'Dog Walker',
	Done: 'Done',
	DontAllowClientsToCancel: `Don't allow clients to cancel`,
	DontHaveAccount: `Don't have an account?`,
	DontSend: `Don't send`,
	Double: 'Double',
	DowngradeTo: 'Downgrade to {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Sorry, you cannot downgrade your plan because you have too many team members. Please remove some from your provider and try again.',
	Download: 'Download',
	DownloadAsPdf: 'Download as PDF',
	DownloadERA: 'Download ERA',
	DownloadPDF: 'Download PDF',
	DownloadTemplateFileName: 'Carepatron Switching Template.csv',
	DownloadTemplateTileDescription: 'Use our spreadsheet template to organize and upload your clients.',
	DownloadTemplateTileLabel: 'Download template',
	Downloads: '{number, plural, one {<span>#</span> Download} other {<span>#</span> Downloads}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Draft',
	DraftResponses: 'Draft response',
	DraftSaved: 'Saved changes',
	DragAndDrop: 'drag and drop',
	DragDropText: 'Drag and drop health documents',
	DragToMove: 'Drag to move',
	DragToMoveOrActivate: 'Drag to move or activate',
	DramaTherapist: 'Drama Therapist',
	DropdownFormFieldPlaceHolder: 'Choose options from the list',
	DropdownFormPrimaryText: 'Dropdown',
	DropdownFormSecondaryText: 'Choose from a list of options',
	DropdownTextFieldError: 'Dropdown option text cannot be empty',
	DropdownTextFieldPlaceholder: 'Add a dropdown option',
	Due: 'Due',
	DueDate: 'Due date',
	Duplicate: 'Duplicate',
	DuplicateAvailabilityScheduleFailure: 'Failed to duplicate availability schedule',
	DuplicateAvailabilityScheduleSuccess: 'Successfully duplicated {name} schedule',
	DuplicateClientBannerAction: 'Review',
	DuplicateClientBannerDescription:
		'Merging duplicate client records consolidates them into one, keeping all unique client information.',
	DuplicateClientBannerTitle: '{count} Duplicates found',
	DuplicateColumn: 'Duplicate column',
	DuplicateContactFieldSettingErrorSnackbar: 'Cannot have duplicate section names',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Cannot have duplicate field names',
	DuplicateEmailError: 'Duplicate email',
	DuplicateHeadingName: 'Section {name} already exists',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'An invoice with the same "invoice number" already exists.',
	DuplicateRecords: 'Duplicate records',
	DuplicateRecordsMinimumError: 'Minimum of 2 records must be selected',
	DuplicateRecordsRequired: 'Select at least 1 record to separate',
	DuplicateServiceFailure: 'Failed to duplicate <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Successfully duplicated <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Successfully duplicated folder',
	DuplicateTemplateSuccess: 'Successfully duplicated template',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'DX code',
	DxCodeSelectPlaceholder: 'Search and add from ICD-10 codes',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'None',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>contains adjustments</i>} other {}}',
	EarnReferralCredit: 'Earn ${creditAmount}',
	Economist: 'Economist',
	Edit: 'Edit',
	EditArrangements: 'Edit arrangements',
	EditBillTo: 'Edit Bill to',
	EditClient: 'Edit Client',
	EditClientFileModalDescription:
		'Edit the access to this file by choosing the options in the "Viewable by" checkboxes',
	EditClientFileModalTitle: 'Edit file',
	EditClientNoteModalDescription:
		'Edit content in the note. Use the "Viewable by" section to change who can see the note.',
	EditClientNoteModalTitle: 'Edit note',
	EditConnectedAppButton: 'Edit',
	EditConnections: 'Edit connections{account, select, null { } undefined { } other { for {account}}}',
	EditContactDetails: 'Edit contact details',
	EditContactFormIsClientLabel: 'Convert to client',
	EditContactIsClientCheckboxWarning: 'Converting a contact into a client cannot be undone',
	EditContactIsClientWanringModal:
		'Converting this contact into a Client cannot be undone. However, all of the relationships will still remain and you will now have access to their notes, files, and other documentation.',
	EditContactRelationship: 'Edit contact relationship',
	EditDetails: 'Edit details',
	EditFileModalTitle: 'Edit file for {name}',
	EditFolder: 'Edit folder',
	EditFolderDescription: 'Rename the folder as...',
	EditInvoice: 'Edit invoice',
	EditInvoiceDetails: 'Edit Invoice details',
	EditLink: 'Edit Link',
	EditLocation: 'Edit location',
	EditLocationFailure: 'Failed to update location',
	EditLocationSucess: 'Successfully updated location',
	EditPaymentDetails: 'Edit payment details',
	EditPaymentMethod: 'Edit payment method',
	EditPersonalDetails: 'Edit personal details',
	EditPractitioner: 'Edit Practitioner',
	EditProvider: 'Edit Provider',
	EditProviderDetails: 'Edit provider details',
	EditRecurrence: 'Edit recurrence',
	EditRecurringAppointmentModalTitle: 'Edit repeating appointment',
	EditRecurringEventModalTitle: 'Edit repeating meeting',
	EditRecurringReminderModalTitle: 'Edit repeating reminder',
	EditRecurringTaskModalTitle: 'Edit repeating task',
	EditRelationshipModalTitle: 'Edit relationship',
	EditService: 'Edit service',
	EditServiceFailure: 'Failed to update new service',
	EditServiceGroup: 'Edit collection',
	EditServiceGroupFailure: 'Failed to update collection',
	EditServiceGroupSuccess: 'Successfully updated collection',
	EditServiceSuccess: 'Successfully updated new service',
	EditStaffDetails: 'Edit staff details',
	EditStaffDetailsCantUpdatedEmailTooltip: `Can't update email address. Please create a new team member with a new email address.`,
	EditSubscriptionBilledQuantity: 'Billed quantity',
	EditSubscriptionBilledQuantityValue: '{billedUsers} team members',
	EditSubscriptionLimitedTimeOffer: 'Limited time offer! 50% off for 6 months.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Your subscription cost will be adjusted when adding or removing team members.',
	EditSubscriptionUpgradeContent:
		'Your account will be immediately updated to the new plan and billing period. Any pricing changes will be automatically charged to your saved payment method or credited to your account.',
	EditSubscriptionUpgradePlanTitle: 'Upgrade subscription plan',
	EditSuperbillReceipt: 'Edit superbill',
	EditTags: 'Edit tags',
	EditTemplate: 'Edit Template',
	EditTemplateFolderSuccessMessage: 'Successfully updated folder',
	EditValue: 'Edit {value}',
	Edited: 'Edited',
	Editor: 'Editor',
	EditorAlertDescription: 'An unsupported format has been detected. Reload the app or contact our support team.',
	EditorAlertTitle: `We're having trouble displaying this content`,
	EditorPlaceholder: 'Start writing, choose a template or add basic blocks to capture answers from your clients.',
	EditorTemplatePlaceholder: 'Start writing or add components to build a template',
	EditorTemplateWithSlashCommandPlaceholder:
		'Start writing or add basic blocks to capture client responses. Use slash commands (/) for quick actions.',
	EditorWithSlashCommandPlaceholder:
		'Start writing, choose a template, or add basic blocks to capture client responses. Use slash commands ( / ) for quick actions.',
	EffectiveStartEndDate: 'Effective start - end date',
	ElectricalEngineer: 'Electrical Engineer',
	Electronic: 'Electronic',
	ElectronicSignature: 'Electronic signature',
	ElementarySchoolTeacher: 'Elementary School Teacher',
	Eligibility: 'Eligibility',
	Email: 'Email',
	EmailAlreadyExists: 'Email address already exists',
	EmailAndSms: 'Email & SMS',
	EmailBody: 'Email body',
	EmailContainsIgnoredDescription:
		'The following email contains a sender/s email that is currently ignored. Do you want to continue?',
	EmailInviteToPortalBody: `Hi {contactName},

Please follow this link to sign in to your secure client portal and easily manage your care.

Kind Regards,

{providerName}`,
	EmailInviteToPortalSubject: 'Welcome to {providerName}',
	EmailInvoice: 'Email invoice',
	EmailInvoiceOverdueBody: `Hi {contactName}

Your invoice {invoiceNumber} is overdue.
Please pay your invoice online using the link below.

If you have any questions, please let us know.

Thanks,
{providerName}`,
	EmailInvoicePaidBody: `Hi {contactName}

Your invoice {invoiceNumber} is paid.
To view and download a copy of your invoice follow the link below.

If you have any questions, please let us know.

Thanks,
{providerName}`,
	EmailInvoiceProcessingBody: `Hi {contactName}

Your invoice {invoiceNumber} is ready.
Follow the link below to view your invoice.

If you have any questions, please let us know.

Thanks,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hi {contactName}

Your invoice {invoiceNumber} is ready, to be paid by {dueDate}.
To view and pay your invoice online follow the link below.

If you have any questions, please let us know.

Thanks,
{providerName}`,
	EmailInvoiceVoidBody: `Hi {contactName}

Your invoice {invoiceNumber} is void.
To view this invoice follow the link below.

If you have any questions, please let us know.

Thanks,
{providerName}`,
	EmailNotFound: 'Email not found',
	EmailNotVerifiedErrorCodeSnackbar: 'Unable to perform action. You need to verify your email address.',
	EmailNotVerifiedTitle: 'Your email is not verified. Some features will be limited.',
	EmailSendClientIntakeBody: `Hi {contactName},

{providerName} would like you to provide some information and review important documents. Please follow the link below to get started.

Kind Regards,

{providerName}`,
	EmailSendClientIntakeSubject: 'Welcome to {providerName}',
	EmailSuperbillReceipt: 'Email superbill',
	EmailSuperbillReceiptBody: `Hi {contactName},

{providerName} has sent you a copy of your statement of reimbursement receipt {date}.

You can download and submit this directly to your insurance company.`,
	EmailSuperbillReceiptFailure: 'Failed to send Superbill receipt',
	EmailSuperbillReceiptSubject: '{providerName} has sent a statement of reimbursement receipt',
	EmailSuperbillReceiptSuccess: 'Successfully sent Superbill receipt',
	EmailVerificationDescription: 'We are <span>verifying</span> your account now',
	EmailVerificationNotification: 'A verification email has been sent to {email}',
	EmailVerificationSuccess: 'Your email address has been successfully changed to {email}',
	Emails: 'Emails',
	EmergencyContact: 'Emergency contact',
	EmployeesIdentificationNumber: 'Employees identification number',
	EmploymentStatus: 'Employment Status',
	EmptyAgendaViewDescription: 'No events to display. <mark>Create an appointment now</mark>',
	EmptyBin: 'Empty bin',
	EmptyBinConfirmationDescription:
		'Empty bin will delete all <strong>{total} conversations</strong> in Deleted. This action can’t be undone.',
	EmptyBinConfirmationTitle: 'Delete conversations forever',
	EmptyTrash: 'Empty trash',
	Enable: 'Enable',
	EnableCustomServiceAvailability: 'Enable service availability',
	EnableCustomServiceAvailabilityDescription: 'E.g. Initial appointments can only be booked everyday from 9-10am',
	EndCall: 'End call',
	EndCallConfirmationForCreator: 'You will end this for everyone because you are the initiator of the call.',
	EndCallConfirmationHasActiveAttendees:
		'You about to end call but client(s) already joined. Do you also want to join?',
	EndCallForAll: 'End call for everyone',
	EndDate: 'End date',
	EndDictation: 'End dictation',
	EndOfLine: 'No more appointments',
	EndSession: 'End session',
	EndTranscription: 'End transcription',
	Ends: 'Ends',
	EndsOnDate: 'Ends on {date}',
	Enrol: 'Enroll',
	EnrollmentRejectedSubject: 'Your enrollment with {payerName} has been rejected',
	Enrolment: 'Intake',
	Enrolments: 'Enrollments',
	EnrolmentsDescription: 'View and manage provider enrollments with the payer.',
	EnterAName: 'Enter a name...',
	EnterFieldLabel: 'Enter field label...',
	EnterPaymentDetailsDescription:
		'Your subscription cost will automatically be adjusted when adding or removing users.',
	EnterSectionName: 'Enter section name...',
	EnterSubscriptionPaymentDetails: 'Enter payment details',
	EnvironmentalScientist: 'Environmental Scientist',
	Epidemiologist: 'Epidemiologist',
	Eraser: 'Eraser',
	Error: 'Error',
	ErrorBoundaryAction: 'Reload page',
	ErrorBoundaryDescription: 'Please refresh the page and try again.',
	ErrorBoundaryTitle: 'Whoops! Something went wrong',
	ErrorCallNotFound: 'The call cannot be found. It may have expired or the creator has ended it.',
	ErrorCannotAccessCallUninvitedCode: 'Sorry, it seems you have not been invited to this call.',
	ErrorFileUploadCustomMaxFileCount: 'Cannot upload more than {count} files at once',
	ErrorFileUploadCustomMaxFileSize: 'File size cannot exceed {mb} MB',
	ErrorFileUploadInvalidFileType: 'Invalid file type that could contain potential viruses and harmful software',
	ErrorFileUploadMaxFileCount: 'Cannot upload more than 150 files at once',
	ErrorFileUploadMaxFileSize: 'File size cannot exceed 100 MB',
	ErrorFileUploadNoFileSelected: 'Please select files to upload',
	ErrorInvalidNationalProviderId: 'The National Provider Id provided is not valid',
	ErrorInvalidPayerId: 'The Payer Id provided is not valid',
	ErrorInvalidTaxNumber: 'The Tax number provided is not valid',
	ErrorInviteExistingProviderStaffCode: 'This user is already in the workspace.',
	ErrorInviteStaffExistingUser: 'Sorry, it seems the user you added already exists in our system.',
	ErrorOnlySingleCallAllowed: 'You can only have one call at a time. Please end the current call to start a new one.',
	ErrorPayerNotFound: 'Payer not found',
	ErrorProfilePhotoMaxFileSize: 'Upload failed! File size limit reached - 5MB',
	ErrorRegisteredExistingUser: 'It looks like you’re already registered.',
	ErrorUserSignInIncorrectCredentials: 'Invalid email or password. Please try again.',
	ErrorUserSigninGeneric: 'An unexpected error occurred. Please try again.',
	ErrorUserSigninUserNotConfirmed:
		'You must verify your account before signing in. Please check your inbox for instructions.',
	Errors: 'Errors',
	EssentialPlanInclusionFive: 'Template import',
	EssentialPlanInclusionFour: '5 GB of storage',
	EssentialPlanInclusionHeader: 'Everything in Free +',
	EssentialPlanInclusionOne: 'Automatic and custom reminders',
	EssentialPlanInclusionSix: 'Priority support',
	EssentialPlanInclusionThree: 'Video chat',
	EssentialPlanInclusionTwo: '2 way calendar sync',
	EssentialSubscriptionPlanSubtitle: 'Simplify your practice with the essentials',
	EssentialSubscriptionPlanTitle: 'Essential',
	Esthetician: 'Esthetician',
	Estheticians: 'Estheticians',
	EstimatedArrivalDate: 'Est. arrival {numberOfDaysFromNow}',
	Ethnicity: 'Ethnicity',
	Europe: 'Europe',
	EventColor: 'Meeting color',
	EventName: 'Event name',
	EventType: 'Event type',
	Every: 'Every',
	Every2Weeks: 'Every 2 weeks',
	EveryoneInWorkspace: 'Everyone in the workspace',
	ExercisePhysiologist: 'Exercise Physiologist',
	Existing: 'Existing',
	ExistingClients: 'Existing clients',
	ExistingFolders: 'Existing folders',
	ExpiredPromotionCode: 'Promotion code has expired',
	ExpiredReferralDescription: 'Referral has expired',
	ExpiredVerificationLink: 'Expired verification link',
	ExpiredVerificationLinkDescription: `We're sorry, but the verification link you clicked on has expired. This can happen if you waited longer than 24 hours to click on the link or if you have already used the link to verify your email address.

Please request a new verification link to verify your email address.`,
	ExpiryDateRequired: 'Expiry date is required',
	ExploreFeature: 'What would you like to explore first?',
	ExploreOptions: 'Choose one or more options to explore...',
	Export: 'Export',
	ExportAppointments: 'Export appointments',
	ExportClaims: 'Export claims',
	ExportClaimsFilename: 'Claims {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: `Your file couldn't be downloaded due to an error.`,
	ExportClientsDownloadFailureSnackbarTitle: 'Download failed',
	ExportClientsFailureSnackbarDescription: `Your file couldn't be exported successfully due to an error.`,
	ExportClientsFailureSnackbarTitle: 'Export failed',
	ExportClientsModalDescription: `This data export process may take a few minutes depending on the amount of data being exported. You'll receive an email notification with a link once it's ready for download.

Do you wish to proceed with exporting the client data?`,
	ExportClientsModalTitle: 'Export client data',
	ExportCms1500: 'Export CMS1500',
	ExportContactFailedNotificationSubject: 'Your data export has failed',
	ExportFailed: 'Export failed',
	ExportGuide: 'Export guide',
	ExportInvoiceFileName: 'Transactions {fromDate}-{toDate}.csv',
	ExportPayments: 'Export payments',
	ExportPaymentsFilename: 'Payments {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Your document is ready to download.',
	ExportPrintWaitMessage: 'Preparing your document. Please wait...',
	ExportTextOnly: 'Export text only',
	ExportTransactions: 'Export transactions',
	Exporting: 'Exporting',
	ExportingData: 'Exporting data',
	ExtendedFamilyMember: 'Extended family member',
	External: 'External',
	ExternalEventInfoBanner: 'This appointment is from a synced calendar and may be missing items.',
	ExtraLarge: 'Extra large',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Failed',
	FailedToJoinTheMeeting: 'Failed to join the meeting.',
	FallbackPageDescription: `Looks like this page doesn't exist, you may need to {refreshButton} this page to get the latest changes.

Otherwise, please contact Carepatron support.`,
	FallbackPageDescriptionUpdateButton: 'refresh',
	FallbackPageTitle: 'Oops...',
	FamilyPlanningService: 'Family planning service',
	FashionDesigner: 'Fashion Designer',
	FastTrackInvoicingAndBilling: 'Fast track your invoicing and billing',
	Father: 'Father',
	FatherInLaw: 'Father-in-law',
	Favorite: 'Favorite',
	FeatureBannerCalendarTile1ActionLabel: 'Online booking • 2 mins',
	FeatureBannerCalendarTile1Description: 'Simply email, text, or add availability to your website',
	FeatureBannerCalendarTile1Title: 'Enable your clients to book online',
	FeatureBannerCalendarTile2ActionLabel: 'Automate reminders • 2 mins',
	FeatureBannerCalendarTile2Description: 'Increase client attendance with automated reminders',
	FeatureBannerCalendarTile2Title: 'Reduce no-shows',
	FeatureBannerCalendarTile3Title: 'Scheduling and Workflow',
	FeatureBannerCalendarTitle: 'Make scheduling easy',
	FeatureBannerCallsTile1ActionLabel: 'Start telehealth call',
	FeatureBannerCallsTile1Description: 'Client access with just a link. No logins, passwords or hassle',
	FeatureBannerCallsTile1Title: 'Start a video call from anywhere',
	FeatureBannerCallsTile2ActionLabel: 'Connect apps • 4 mins',
	FeatureBannerCallsTile2Description: 'Seamlessly connect other preferred telehealth providers',
	FeatureBannerCallsTile2Title: 'Connect your telehealth apps',
	FeatureBannerCallsTile3Title: 'Calls',
	FeatureBannerCallsTitle: 'Connect with clients — Anywhere, Anytime',
	FeatureBannerClientsTile1ActionLabel: 'Import now • 2 mins',
	FeatureBannerClientsTile1Description: 'Get started quickly with our automated client import tool',
	FeatureBannerClientsTile1Title: 'Have lots of clients?',
	FeatureBannerClientsTile2ActionLabel: 'Customize intake • 2 mins',
	FeatureBannerClientsTile2Description: 'Remove intake paperwork and improve client experiences',
	FeatureBannerClientsTile2Title: 'Go paperless',
	FeatureBannerClientsTile3Title: 'Client Portal',
	FeatureBannerClientsTitle: 'It all starts with your clients',
	FeatureBannerHeader: 'By the Community, for the Community!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automate payments • 2 mins',
	FeatureBannerInvoicesTile1Description: 'Avoid awkward conversations with automated payments',
	FeatureBannerInvoicesTile1Title: 'Get paid 2x faster',
	FeatureBannerInvoicesTile2ActionLabel: 'Track cashflow • 2 mins',
	FeatureBannerInvoicesTile2Description: 'Reduce unpaid invoices and keep tabs on your income',
	FeatureBannerInvoicesTile2Title: 'Track your income, painlessly',
	FeatureBannerInvoicesTile3Title: 'Billing and Payments',
	FeatureBannerInvoicesTitle: 'One less thing to worry about',
	FeatureBannerSubheader: 'Carepatron templates made by our team and community. Try new resources or share your own!',
	FeatureBannerTeamTile1ActionLabel: 'Invite now',
	FeatureBannerTeamTile1Description: 'Invite team members to your account and make collaboration easy',
	FeatureBannerTeamTile1Title: 'Bring your team together',
	FeatureBannerTeamTile2ActionLabel: 'Set availability • 2 mins',
	FeatureBannerTeamTile2Description: 'Manage your teams availability to avoid double-bookings',
	FeatureBannerTeamTile2Title: 'Set your availability',
	FeatureBannerTeamTile3ActionLabel: 'Set permissions • 2 mins',
	FeatureBannerTeamTile3Description: 'Control access to sensitive data and tools for compliance',
	FeatureBannerTeamTile3Title: 'Customise permissions and access',
	FeatureBannerTeamTitle: 'Nothing great is achieved alone',
	FeatureBannerTemplatesTile1ActionLabel: 'Explore library • 2 mins',
	FeatureBannerTemplatesTile1Description: 'Choose from an amazing library of customizable resources ',
	FeatureBannerTemplatesTile1Title: 'Reduce your workload',
	FeatureBannerTemplatesTile2ActionLabel: 'Send now • 2 mins',
	FeatureBannerTemplatesTile2Description: 'Send beautiful templates to clients for completion',
	FeatureBannerTemplatesTile2Title: 'Make documentation fun',
	FeatureBannerTemplatesTile3Title: 'Templates',
	FeatureBannerTemplatesTitle: 'Templates for absolutely anything',
	FeatureLimitBannerDescription:
		'Upgrade now to keep creating and managing {featureName} without interruption and get the most out of Carepatron!',
	FeatureLimitBannerTitle: 'You’re {percentage}% of the way to your {featureName} limit',
	FeatureRequiresUpgrade: 'This feature requires an upgrade',
	Fee: 'Fee',
	Female: 'Female',
	FieldLabelTooltip: '{isHidden, select, true {Show} other {Hide}} field label',
	FieldName: 'Field name',
	FieldOptionsFirstPart: 'First word',
	FieldOptionsMiddlePart: 'Middle words',
	FieldOptionsSecondPart: 'Last word',
	FieldOptionsWholeField: 'Whole field',
	FieldType: 'Field type',
	Fields: 'Fields',
	File: 'File',
	FileDownloaded: '<strong>{fileName}</strong> downloaded',
	FileInvalidType: 'File not supported.',
	FileNotFound: 'File not found',
	FileNotFoundDescription: 'The file you are looking for is not available or has been deleted',
	FileTags: 'File tags',
	FileTagsHelper: 'Tags will be applied to all files',
	FileTooLarge: 'File too large.',
	FileTooSmall: 'File too small.',
	FileUploadComplete: 'Complete',
	FileUploadFailed: 'Failed',
	FileUploadInProgress: 'Loading',
	FileUploadedNotificationSubject: '{actorProfileName} has uploaded a file',
	Files: 'Files',
	FillOut: 'Fill out',
	Filter: 'Filter',
	FilterBy: 'Filter by',
	FilterByAmount: 'Filter by amount',
	FilterByClient: 'Filter by client',
	FilterByLocation: 'Filter by location',
	FilterByService: 'Filter by service',
	FilterByStatus: 'Filter by status',
	FilterByTags: 'Filter by tags',
	FilterByTeam: 'Filter by team',
	Filters: 'Filters',
	FiltersAppliedToView: 'Filters applied to view',
	FinalAppointment: 'Final Appointment',
	FinalizeImport: 'Finalize import',
	FinancialAnalyst: 'Financial Analyst',
	Finish: 'Finish',
	Firefighter: 'Firefighter',
	FirstName: 'First name',
	FirstNameLastInitial: 'First name, last initial',
	FirstPerson: '1st person',
	FolderName: 'Folder name',
	Folders: 'Folders',
	FontFamily: 'Font family',
	ForClients: 'For clients',
	ForClientsDetails: 'I receive care or health related services',
	ForPractitioners: 'For practitioners',
	ForPractitionersDetails: 'Manage and grow your practice',
	ForgotPasswordConfirmAccessCode: 'Confirmation code',
	ForgotPasswordConfirmNewPassword: 'New password',
	ForgotPasswordConfirmPageDescription: `Please enter your email address, a new password, and the confirmation code we've just sent you.`,
	ForgotPasswordConfirmPageTitle: 'Reset password',
	ForgotPasswordPageButton: 'Send reset link',
	ForgotPasswordPageDescription: `Enter your email and we'll send you a link to reset your password.`,
	ForgotPasswordPageTitle: 'Forgotten password',
	ForgotPasswordSuccessPageDescription: 'Check your inbox for your reset link.',
	ForgotPasswordSuccessPageTitle: 'Reset link sent!',
	Form: 'Form',
	FormAnswersSentToEmailNotification: 'We have sent a copy of your answers to',
	FormBlocks: 'Form blocks',
	FormFieldAddOption: 'Add option',
	FormFieldAddOtherOption: 'Add "other"',
	FormFieldOptionPlaceholder: 'Option {index}',
	FormStructures: 'Form structures',
	Format: 'Format',
	FormatLinkButtonColor: 'Button color',
	Forms: 'Forms',
	FormsAndAgreementsValidationMessage:
		'All forms and agreements must be completed to continue with the intake process.',
	FormsCategoryDescription: 'For collecting and organizing patient details',
	Frankfurt: 'Frankfurt',
	Free: 'Free',
	FreePlanInclusionFive: 'Automated billing & online payments',
	FreePlanInclusionFour: 'Client portal',
	FreePlanInclusionHeader: 'Get started with',
	FreePlanInclusionOne: 'Unlimited clients',
	FreePlanInclusionSix: 'Live support',
	FreePlanInclusionThree: '1 GB of storage',
	FreePlanInclusionTwo: 'Telehealth',
	FreeSubscriptionPlanSubtitle: 'Free for everyone',
	FreeSubscriptionPlanTitle: 'Free',
	Friday: 'Friday',
	From: 'From',
	FullName: 'Full name',
	FunctionalMedicineOrNaturopath: 'Functional Medicine or Naturopath',
	FuturePaymentsAuthoriseProvider: 'Allow {provider} to use the saved payment in the future',
	FuturePaymentsSavePaymentMethod: 'Save {paymentMethod} for future payments',
	GST: 'GST',
	Gender: 'Gender',
	GeneralAvailability: 'General availability',
	GeneralAvailabilityDescription: `Set when you're regularly available. Clients will only be able to book your services during available hours.`,
	GeneralAvailabilityDescription2:
		'Create schedules based on your availability and desired service offerings at specific times to determine your online booking availability.',
	GeneralAvailabilityInfo: 'Your available hours will determine your online booking availability',
	GeneralAvailabilityInfo2:
		'Services offering group events should use a new schedule to reduce the available hours it can be booked by clients online.',
	GeneralHoursPlural: '{count} {count, plural, one {hour} other {hours}}',
	GeneralPractitioner: 'General Practitioner',
	GeneralPractitioners: 'General Practitioners',
	GeneralServiceAvailabilityInfo: 'This schedule will override the behavior for assigned team members',
	Generate: 'Generate',
	GenerateBillingItemsBannerContent: 'Billing items are not automatically created for recurring appointments.',
	GenerateItems: 'Generate items',
	GenerateNote: 'Generate note',
	GenerateNoteConfirmationModalDescription:
		'What would you like to do? Create a new generated note, add to the existing one, or replace its content?',
	GenerateNoteFor: 'Generate note for',
	GeneratingContent: 'Generating content...',
	GeneratingNote: 'Generating your note...',
	GeneratingTranscript: 'Generating transcript',
	GeneratingTranscriptDescription: 'This may take a few minutes to process',
	GeneratingYourTranscript: 'Generating your transcript',
	GenericErrorDescription: '{module} could not be loaded. Please try again later.',
	GenericErrorTitle: 'Unexpected error occured',
	GenericFailureSnackbar: 'Sorry, something unexpected occurred. Please refresh the page and try again.',
	GenericSavedSuccessSnackbar: 'Success! Changes saved',
	GeneticCounselor: 'GeneticCounselor',
	Gerontologist: 'Gerontologist',
	Get50PercentOff: 'Get 50% off!',
	GetHelp: 'Get help',
	GetStarted: 'Get started',
	GettingStartedAppointmentTypes: 'Create appointment types',
	GettingStartedAppointmentTypesDescription:
		'Streamline your scheduling and billing by customizing your services, prices, and billing codes',
	GettingStartedAppointmentTypesTitle: 'Schedule & bill fast',
	GettingStartedClients: 'Add your clients',
	GettingStartedClientsDescription:
		'Get yourself up and running with clients for future appointments, notes, and payments',
	GettingStartedClientsTitle: 'It all starts with clients',
	GettingStartedCreateClient: 'Create client',
	GettingStartedImportClients: 'Import clients',
	GettingStartedInvoices: 'Invoice like a pro',
	GettingStartedInvoicesDescription: `It's simple to create professional invoices.
		Add your logo, location, and payment terms`,
	GettingStartedInvoicesTitle: 'Put your best foot forward',
	GettingStartedMobileApp: 'Get the mobile app',
	GettingStartedMobileAppDescription:
		'You can download Carepatron on your iOS, Android, or desktop device for easy access on the go',
	GettingStartedMobileAppTitle: 'Work from anywhere',
	GettingStartedNavItem: 'Getting started',
	GettingStartedPageTitle: 'Getting started on Carepatron',
	GettingStartedPayments: 'Accept online payments',
	GettingStartedPaymentsDescription: `Get paid faster by enabling your clients to pay online.
		See all your invoices and payments in one place`,
	GettingStartedPaymentsTitle: 'Make payments a breeze',
	GettingStartedSaveBranding: 'Save branding',
	GettingStartedSyncCalendars: 'Sync other calendars',
	GettingStartedSyncCalendarsDescription: `Carepatron checks your calendar for conflicts, so appointments are only scheduled when you're available`,
	GettingStartedSyncCalendarsTitle: 'Always stay up to date',
	GettingStartedVideo: 'Watch an introduction video',
	GettingStartedVideoDescription: 'The first all-in-one healthcare workspaces for small teams and their clients',
	GettingStartedVideoTitle: 'Welcome to Carepatron',
	GetttingStartedGetMobileDownload: 'Download the app',
	GetttingStartedGetMobileNoDownload:
		'Not compatible with this browser. If you are using iPhone or iPad, please open this page in Safari. Otherwise, try opening it in Chrome.',
	Glossary: 'Glossary',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail only allows 500 messages to be sent from your account in a day. Some of the messages may fail. Do you want to continue?',
	GoToAppointment: 'Go to appointment',
	GoToApps: 'Go to apps',
	GoToAvailability: 'Go to availability',
	GoToClientList: 'Go to client list',
	GoToClientRecord: 'Go to client record',
	GoToClientSettings: 'Go to client settings now',
	GoToInvoiceTemplates: 'Go to invoice templates',
	GoToNotificationSettings: 'Go to notification settings',
	GoToPaymentSettings: 'Go to payment settings',
	Google: 'Google',
	GoogleCalendar: 'Google Calendar',
	GoogleColor: 'Google calendar color',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Tag Manager Container ID',
	GotIt: 'Got it!',
	Goto: 'Go to',
	Granddaughter: 'Granddaughter',
	Grandfather: 'Grandfather',
	Grandmother: 'Grandmother',
	Grandparent: 'Grandparent',
	Grandson: 'Grandson',
	GrantPortalAccess: 'Grant portal access',
	GraphicDesigner: 'Graphic Designer',
	Grid: 'Grid',
	GridView: 'Grid view',
	Group: 'Group',
	GroupBy: 'Group by',
	GroupEvent: 'Group event',
	GroupEventHelper: 'Set an attendee limits for the service',
	GroupFilterLabel: 'All {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Group ID',
	GroupInputFieldsFormPrimaryText: 'Group input fields',
	GroupInputFieldsFormSecondaryText: 'Choose or add custom fields',
	GuideTo: 'Guide to {value}',
	GuideToImproveVideoQuality: 'Guide to improve video quality',
	GuideToManagingPayers: 'Managing payers',
	GuideToSubscriptionsBilling: 'Guide to subscriptions billing',
	GuideToTroubleshooting: 'Guide to troubleshooting',
	Guidelines: 'Guidelines',
	GuidelinesCategoryDescription: 'For guiding clinical decision-making',
	HST: 'HST',
	HairStylist: 'Hair Stylist',
	HaveBeenWaiting: 'You’ve been waiting for a long time',
	HeHim: 'He/Him',
	HeaderAccountSettings: 'Profile',
	HeaderCalendar: 'Calendar',
	HeaderCalls: 'Calls',
	HeaderClientAppAccountSettings: 'Account Settings',
	HeaderClientAppCalls: 'Calls',
	HeaderClientAppMyDocumentation: 'Documentation',
	HeaderClientAppMyRelationships: 'My relationships',
	HeaderClients: 'Clients',
	HeaderHelp: 'Help',
	HeaderMoreOptions: 'More options',
	HeaderStaff: 'Staff',
	HealthCoach: 'Health Coach',
	HealthCoaches: 'Health Coaches',
	HealthEducator: 'Health Educator',
	HealthInformationTechnician: 'Health Information Technician',
	HealthPolicyExpert: 'Health Policy Expert',
	HealthServicesAdministrator: 'Health Services Administrator',
	HelpArticles: 'Help articles',
	HiddenColumns: 'Hidden columns',
	HiddenFields: 'Hidden Fields',
	HiddenSections: 'Hidden sections',
	HiddenSectionsAndFields: 'Hidden sections/fields',
	HideColumn: 'Hide column',
	HideColumnButton: 'Hide column {value} button',
	HideDetails: 'Hide details',
	HideField: 'Hide field',
	HideFullAddress: 'Hide',
	HideMenu: 'Hide menu',
	HideMergeSummarySidebar: 'Hide merge summary',
	HideSection: 'Hide section',
	HideYourView: 'Hide your view',
	Highlight: 'Highlight color',
	Highlighter: 'Highlighter',
	History: 'History',
	HistoryItemFooter: '{actors, select, undefined {{date} at {time}} other {By {actors} • {date} at {time}}}',
	HistorySidePanelEmptyState: 'No history records found',
	HistoryTitle: 'Activity log',
	HolisticHealthPractitioner: 'Holistic Health Practitioner',
	HomeCaregiver: 'Home Caregiver',
	HomeHealthAide: 'Home Health Aide',
	HomelessShelter: 'Homeless shelter',
	HourAbbreviation: '{count} {count, plural, one {hr} other {hrs}}',
	Hourly: 'Hourly',
	HoursPlural: '{age, plural, one {# hour} other {# hours}}',
	HowCanWeImprove: 'How can we improve this?',
	HowCanWeImproveResponse: 'How can we improve this response?',
	HowDidWeDo: 'How did we do?',
	HowDoesReferralWork: 'Guide to the referral program',
	HowToUseAiSummarise: 'How to use AI Summarize',
	HumanResourcesManager: 'Human Resources Manager',
	Husband: 'Husband',
	Hypnotherapist: 'Hypnotherapist',
	IVA: 'IVA',
	IgnoreNotification: 'Ignore notification',
	IgnoreOnce: 'Ignore once',
	IgnoreSender: 'Ignore sender',
	IgnoreSenderDescription: `Future conversations from this sender will be automatically moved to 'Other'. Are you sure you want to ignore these senders?`,
	IgnoreSenders: 'Ignore senders',
	IgnoreSendersSuccess: 'Ignored email address <mark>{addresses}</mark>',
	Ignored: 'Ignored',
	Image: 'Image',
	Import: 'Import',
	ImportActivity: 'Import activity',
	ImportClientSuccessSnackbarDescription: 'Your file has been imported successfully',
	ImportClientSuccessSnackbarTitle: 'Import successful!',
	ImportClients: 'Import clients',
	ImportClientsFailureSnackbarDescription: `Your file couldn't be imported successfully due to an error.`,
	ImportClientsFailureSnackbarTitle: 'Import unsuccessful!',
	ImportClientsGuide: 'Guide to importing clients',
	ImportClientsInProgressSnackbarDescription: 'This should only take up to a minute to complete.',
	ImportClientsInProgressSnackbarTitle: 'Importing {fileName}',
	ImportClientsModalDescription: `Choose where your data is coming from – whether it's a file on your device, a third-party service, or another software platform.`,
	ImportClientsModalFileUploadHelperText: 'Supports {fileTypes}. Size limit {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Guide to import client data',
	ImportClientsModalStep1Label: 'Choose data source',
	ImportClientsModalStep2Label: 'Upload file',
	ImportClientsModalStep3Label: 'Review fields',
	ImportClientsModalTitle: 'Importing your client data',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {client} other {clients}} ready for import',
	ImportContactFailedNotificationSubject: 'Your data import has failed',
	ImportDataSourceSelectorLabel: 'Import data source from',
	ImportDataSourceSelectorPlaceholder: 'Search or choose import data source',
	ImportExportButton: 'Import/Export',
	ImportFailed: 'Import failed',
	ImportFromAnotherPlatformTileDescription: 'Download an export of your client files and upload them here.',
	ImportFromAnotherPlatformTileLabel: 'Import from another platform',
	ImportGuide: 'Import guide',
	ImportInProgress: 'Import in progress',
	ImportProcessing: 'Import processing...',
	ImportSpreadsheetDescription:
		'You can import your existing client list into Carepatron by uploading a spreadsheet file with tabular data, such as .CSV, .XLS, or .XLSX',
	ImportSpreadsheetTitle: 'Import your spreadsheet file',
	ImportTemplates: 'Import templates',
	Importing: 'Importing',
	ImportingCalendarProductEvents: 'Importing {product} events',
	ImportingData: 'Importing data',
	ImportingSpreadsheetDescription: 'This should only take up to a minute to complete',
	ImportingSpreadsheetTitle: 'Importing your spreadsheet',
	ImportsInProgress: 'Imports in progress',
	InPersonMeeting: 'In-person meeting',
	InProgress: 'In progress',
	InTransit: 'In Transit',
	InTransitTooltip:
		'In Transit balance includes all paid invoice payouts from Stripe to your bank account. These funds typically take 3-5 days to settle.',
	Inactive: 'Inactive',
	InboundOrOutboundCalls: 'Inbound or outbound calls',
	Inbox: 'Inbox',
	InboxAccessRestricted: 'Access restricted. Please contact the inbox owner for permissions.',
	InboxAccountAlreadyConnected: 'The channel you attempted to connect is already connect to Carepatron',
	InboxAddAttachments: 'Add attachments',
	InboxAreYouSureDeleteMessage: 'Are you sure you want to delete this message?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Successfully closed # conversation} other {Successfully closed # conversations}}',
	InboxBulkComposeModalTitle: 'Compose bulk message',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Successfully deleted # conversation} other {Successfully deleted # conversations}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Successfully marked # conversation as read} other {Successfully marked # conversations as read}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Successfully reopened # conversation} other {Successfully reopened # conversations}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Successfully marked # conversation as unread} other {Successfully marked # conversations as unread}}',
	InboxChatCreateGroup: 'Create group',
	InboxChatDeleteGroupModalDescription:
		'Are you sure you want to delete this group? All messages and attachments will be deleted.',
	InboxChatDeleteGroupModalTitle: 'Delete group',
	InboxChatDiscardDraft: 'Discard draft',
	InboxChatDragDropText: 'Drop files here to upload',
	InboxChatGroupConversation: 'Group conversation',
	InboxChatGroupCreateModalDescription:
		'Start a new group to essage and collaborate with your team, clients, or community.',
	InboxChatGroupCreateModalTitle: 'Create group',
	InboxChatGroupMembers: 'Group members',
	InboxChatGroupModalGroupNameFieldLabel: 'Group name',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'E.g customer support, admin',
	InboxChatGroupModalGroupNameFieldRequired: 'This field is required',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimum one member required',
	InboxChatGroupModalMembersFieldLabel: 'Choose group members',
	InboxChatGroupModalMembersFieldPlaceholder: 'Choose members',
	InboxChatGroupUpdateModalTitle: 'Manage group',
	InboxChatLeaveGroup: 'Leave group',
	InboxChatLeaveGroupModalDescription:
		'Are you sure you want to leave this group? You will no longer receive messages or updates. ',
	InboxChatLeaveGroupModalTitle: 'Leave group',
	InboxChatLeftGroupMessage: 'You have left the group chat',
	InboxChatManageGroup: 'Manage group',
	InboxChatSearchParticipants: 'Choose recipients',
	InboxCloseConversationSuccess: 'Successfully closed conversation',
	InboxCompose: 'Compose',
	InboxComposeBulk: 'Bulk message',
	InboxComposeCarepatronChat: 'Messenger',
	InboxComposeChat: 'Compose chat',
	InboxComposeDisabledNoConnection: 'Connect an email account to send messages',
	InboxComposeDisabledNoPermissionTooltip: 'You do not have permission to send messages from this inbox',
	InboxComposeEmail: 'Compose email',
	InboxComposeMessageFrom: 'From',
	InboxComposeMessageRecipientBcc: 'Bcc',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'To',
	InboxComposeMessageSubject: 'Subject:',
	InboxConnectAccountButton: 'Connect your email',
	InboxConnectedDescription: 'Your inbox has no communications',
	InboxConnectedHeading: 'Your conversations will appear here as soon as you start exchanging communications',
	InboxConnectedHeadingClientView: 'Streamline your client communications',
	InboxCreateFirstInboxButton: 'Create your first inbox',
	InboxCreationSuccess: 'Successfully created inbox',
	InboxDeleteAttachment: 'Delete attachment',
	InboxDeleteConversationSuccess: 'Successfully deleted conversation',
	InboxDeleteMessage: 'Delete message?',
	InboxDirectMessage: 'Direct message',
	InboxEditDraft: 'Edit draft',
	InboxEmailComposeReplyEmail: 'Compose reply',
	InboxEmailDraft: 'Draft',
	InboxEmailNotFound: 'Email not found',
	InboxEmailSubjectFieldInformation: 'Changing the subject line will create a new threaded email.',
	InboxEmptyArchiveDescription: 'No archived conversation have been found',
	InboxEmptyBinDescription: 'No deleted conversation have been found',
	InboxEmptyBinHeading: 'All clear, nothing to see here',
	InboxEmptyBinSuccess: 'Successfully deleted conversations',
	InboxEmptyCongratsHeading: 'Nice work! Sit back and relax until the next conversation',
	InboxEmptyDraftDescription: 'No draft conversation have been found',
	InboxEmptyDraftHeading: 'All clear, nothing to see here',
	InboxEmptyOtherDescription: 'No other conversation have been found',
	InboxEmptyScheduledHeading: 'All clear, no conversations scheduled for send',
	InboxEmptySentDescription: 'No sent conversation have been found',
	InboxForward: 'Forward',
	InboxGroupClientsLabel: 'All clients',
	InboxGroupClientsOverviewLabel: 'Clients',
	InboxGroupClientsSelectedItemPrefix: 'Client',
	InboxGroupStaffsLabel: 'All team',
	InboxGroupStaffsOverviewLabel: 'Team',
	InboxGroupStaffsSelectedItemPrefix: 'Team',
	InboxGroupStatusLabel: 'All Status',
	InboxGroupStatusOverviewLabel: 'Send to a status',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'All tags',
	InboxGroupTagsOverviewLabel: 'Send to a tag',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Hide quoted text',
	InboxIgnoreConversationSuccess: 'Successfully ignored conversation',
	InboxMessageAllLabelRecipientsCount: 'All {label} Recipients ({count})',
	InboxMessageBodyPlaceholder: 'Add your message',
	InboxMessageDeleted: 'Message deleted',
	InboxMessageMarkedAsRead: 'Message marked as read',
	InboxMessageMarkedAsUnread: 'Message marked as unread',
	InboxMessageSentViaChat: '<strong>Sent via chat</strong>  • {time} by {name}',
	InboxMessageShowMoreRecipients: '+{count} more',
	InboxMessageWasDeleted: 'This message was deleted',
	InboxNoConnectionDescription: 'Connect your email account or create inboxes with multiple emails',
	InboxNoConnectionHeading: 'Integrate your client communications',
	InboxNoDirectMessage: 'No recent messages',
	InboxRecentConversations: 'Recent',
	InboxReopenConversationSuccess: 'Successfully reopened conversation',
	InboxReply: 'Reply',
	InboxReplyAll: 'Reply all',
	InboxRestoreConversationSuccess: 'Successfully restored conversation',
	InboxScheduleSendCancelSendSuccess: 'Scheduled send canceled and message reverted to draft',
	InboxScheduleSendMessageSuccessDescription: 'Send scheduled for {date}',
	InboxScheduleSendMessageSuccessTitle: 'Scheduling send',
	InboxSearchForConversations: 'Search for "{query}"',
	InboxSendMessageSuccess: 'Successfully sent conversation',
	InboxSettings: 'Inbox settings',
	InboxSettingsAppsDesc: 'Manage connected apps for this shared inbox: add or remove connections as needed.',
	InboxSettingsAppsNewConnectedApp: 'New connected app',
	InboxSettingsAppsTitle: 'Connected apps',
	InboxSettingsDeleteAccountFailed: 'Failed to delete inbox account',
	InboxSettingsDeleteAccountSuccess: 'Successfully deleted inbox account',
	InboxSettingsDeleteAccountWarning:
		'Removing {email} will disconnect it from the inbox {inboxName} and will stop messages from syncing.',
	InboxSettingsDeleteInboxFailed: 'Failed to delete inbox',
	InboxSettingsDeleteInboxSuccess: 'Successfully deleted inbox',
	InboxSettingsDeleteInboxWarning: `Deleting {inboxName} will disconnect all connected channels and delete all messages associated with this inbox. 
		This action is permanent and cannot be undone.`,
	InboxSettingsDetailsDesc: 'Communication inbox for your team to manage client messages efficiently.',
	InboxSettingsDetailsTitle: 'Inbox details',
	InboxSettingsEmailSignatureLabel: 'Email signature default',
	InboxSettingsReplyFormatDesc:
		'Set up your default reply-to address and email signature to be consistently displayed, regardless of who is sending the email.',
	InboxSettingsReplyFormatTitle: 'Reply format',
	InboxSettingsSendFromLabel: 'Set a default reply from ',
	InboxSettingsStaffDesc: 'Manage team member access to this shared inbox for seamless collaboration.',
	InboxSettingsStaffTitle: 'Assign team members',
	InboxSettingsUpdateInboxDetailsFailed: 'Failed to update inbox details',
	InboxSettingsUpdateInboxDetailsSuccess: 'Succesfully updated inbox details',
	InboxSettingsUpdateInboxStaffsFailed: 'Failed to update inbox team members',
	InboxSettingsUpdateInboxStaffsSuccess: 'Succesfully updated inbox team members',
	InboxSettingsUpdateReplyFormatFailed: 'Failed to update reply format',
	InboxSettingsUpdateReplyFormatSuccess: 'Succesfully updated reply format',
	InboxShowQuotedText: 'Show quoted text',
	InboxStaffRoleAdminDescription: 'View, reply and manage inboxes',
	InboxStaffRoleResponderDescription: 'View and reply',
	InboxStaffRoleViewerDescription: 'View only',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Continue editing',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Yes, switch to bulk send',
	InboxSuggestMoveToBulkComposeMessageContent:
		'You have chosen more than {count} recipients. Do you want to send it as bulk email?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Warning',
	InboxSwitchToOtherInbox: 'Switch to another inbox',
	InboxUndoSendMessageSuccess: 'Sending undone',
	IncludeLineItems: 'Include line items',
	IncludeSalesTax: 'Taxable',
	IncludesAiSmartPrompt: 'Includes AI smart prompts',
	Incomplete: 'Incomplete',
	IncreaseIndent: 'Increase indent',
	IndianHealthServiceFreeStandingFacility: 'Indian Health Service free-standing facility',
	IndianHealthServiceProviderFacility: 'Indian Health Service provider-based facility',
	Information: 'Information',
	InitialAssessment: 'Initial Assessment',
	InitialSignupPageClientFamilyTitle: 'Client or Family Member',
	InitialSignupPageProviderTitle: 'Health & Social Care Provider',
	InitialTreatment: 'Initial treatment',
	Initials: 'Initials',
	InlineEmbed: 'Inline embed',
	InputPhraseToConfirm: 'To confirm, type {confirmationPhrase}.',
	Insert: 'Insert',
	InsertTable: 'Insert table',
	InstallCarepatronOnYourIphone1: 'Install Carepatron on your iOS: tap',
	InstallCarepatronOnYourIphone2: 'and then Add to Home Screen',
	InsufficientCalendarScopesSnackbar: 'Sync failed - please allow calendar permissions to Carepatron',
	InsufficientInboxScopesSnackbar: 'Sync failed - please allow email permissions to Carepatron',
	InsufficientScopeErrorCodeSnackbar: 'Sync failed - please allow all permissions to Carepatron',
	Insurance: 'Insurance',
	InsuranceAmount: 'Ins. Amount',
	InsuranceClaim: 'Insurance claim',
	InsuranceClaimAiChatPlaceholder: 'Ask about the insurance claim...',
	InsuranceClaimAiClaimNumber: 'Claim {number}',
	InsuranceClaimAiSubtitle: 'Insurance billing • Claim validation',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber} submitted to {payerNumber} {payerName} was denied',
	InsuranceClaimErrorDescription:
		'The claim contains errors reported from the payer or clearing house. Please review the following error messages and resubmit the claim.',
	InsuranceClaimErrorGuideLink: 'Guide to insurance claims',
	InsuranceClaimErrorTitle: 'Claim submission errors',
	InsuranceClaimNotFound: 'Insurance claim not found',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {A partial payment of {paymentAmount}} other {A {paymentAmount} payment}} for claim {claimNumber} by {payerNumber} {payerName} was recorded',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber} submitted to {payerNumber} {payerName} was rejected',
	InsuranceClaims: 'Insurance claims',
	InsuranceInformation: 'Insurance information',
	InsurancePaid: 'Ins. Paid',
	InsurancePayer: 'Insurance payer',
	InsurancePayers: 'Insurance payers',
	InsurancePayersDescription: 'View the payers that have been added to your account and manage enrollment.',
	InsurancePayment: 'Insurance payment',
	InsurancePoliciesDetailsSubtitle: 'Add client insurance information to support claims.',
	InsurancePoliciesDetailsTitle: 'Policy details',
	InsurancePoliciesListSubtitle: 'Add client insurance information to support claims.',
	InsurancePoliciesListTitle: 'Insurance policies',
	InsuranceSelfPay: 'Self Pay',
	InsuranceType: 'Insurance type',
	InsuranceUnpaid: 'Insurance unpaid',
	Intake: 'Intake',
	IntakeExpiredErrorCodeSnackbar: 'This intake has expired. Please contact your provider to resend another intake.',
	IntakeNotFoundErrorSnackbar:
		'This intake could not be found. Please contact your provider to resend another intake.',
	IntakeProcessLearnMoreInstructions: 'Guide to set up your intake forms',
	IntakeTemplateSelectorPlaceholder: 'Choose forms and agreements to send to your client to complete',
	Integration: 'Integration',
	IntenseBlur: 'Intensely blur your background',
	InteriorDesigner: 'Interior Designer',
	InternetBanking: 'Bank transfer',
	Interval: 'Interval',
	IntervalDays: 'Interval (Days)',
	IntervalHours: 'Interval (Hours)',
	Invalid: 'Invalid',
	InvalidDate: 'Invalid date',
	InvalidDateFormat: 'Date must be in {format} format',
	InvalidDisplayName: 'Display name cannot contain {value}',
	InvalidEmailFormat: 'Invalid email format',
	InvalidFileType: 'Invalid file type',
	InvalidGTMContainerId: 'Invalid GTM container ID format',
	InvalidPaymentMethodCode: 'The selected payment method is not valid. Please choose another.',
	InvalidPromotionCode: 'Promotion code is invalid',
	InvalidReferralDescription: 'Already using Carepatron',
	InvalidStatementDescriptor: `Statement descriptor must be between 5 and 22 characters long and contain only letters, numbers, spaces, and must not include <, >, \\, ', ", *`,
	InvalidToken: 'Invalid token',
	InvalidTotpSetupVerificationCode: 'Invalid verification code.',
	InvalidURLErrorText: 'This must be a valid URL',
	InvalidZoomTokenErrorCodeSnackbar: 'The Zoom token has expired. Please reconnect your Zoom app and try again.',
	Invite: 'Invite',
	InviteRelationships: 'Invite relationships',
	InviteToPortal: 'Invite to portal',
	InviteToPortalModalDescription: 'An invite email will be sent to your client to sign up to Carepatron.',
	InviteToPortalModalTitle: 'Invite {name} to Carepatron Portal',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Invite new user',
	Invited: 'Invited',
	Invoice: 'Invoice',
	InvoiceColorPickerDescription: 'Color theme to be used in the invoice',
	InvoiceColorTheme: 'Invoice color theme',
	InvoiceContactDeleted: 'The invoice contact has been deleted and this invoice cannot be updated.',
	InvoiceDate: 'Date issued',
	InvoiceDetails: 'Invoice details',
	InvoiceFieldsPlaceholder: 'Search for fields...',
	InvoiceFrom: 'Invoice {number} from {fromProvider}',
	InvoiceInvalidCredit: 'Invalid credit amount, credit amount cannot exceed the invoice total',
	InvoiceNotFoundDescription:
		'Please contact your provider and ask them for more information or to resend the invoice.',
	InvoiceNotFoundTitle: 'Invoice not found',
	InvoiceNumber: 'Invoice #',
	InvoiceNumberFormat: 'Invoice #{number}',
	InvoiceNumberMustEndWithDigit: 'Invoice number must end with a digit (0-9)',
	InvoicePageHeader: 'Invoices',
	InvoicePaidNotificationSubject: 'Invoice {invoiceNumber} paid',
	InvoiceReminder: 'Invoice reminders',
	InvoiceReminderSentence: 'Send {deliveryType} reminder {interval} {unit} {beforeAfter} invoice due date',
	InvoiceReminderSettings: 'Invoice reminder settings',
	InvoiceReminderSettingsInfo: 'Reminders only apply to invoices sent on Carepatron',
	InvoiceReminders: 'Invoice reminders',
	InvoiceRemindersInfo:
		'Set automated reminders for invoice due dates. Reminders only apply to invoices sent through Carepatron',
	InvoiceSettings: 'Invoice settings',
	InvoiceStatus: 'Invoice status',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, USA',
	InvoiceTemplateDescriptionPlaceholder:
		'Add notes, bank transfer details or terms and conditions for alternative payments',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Self-Employed',
	InvoiceTemplateEthnicityPlaceholder: 'Caucasian',
	InvoiceTemplateNotFoundDescription: 'Please contact your provider and ask them for more information.',
	InvoiceTemplateNotFoundTitle: 'Invoice template not found',
	InvoiceTemplates: 'Invoice templates',
	InvoiceTemplatesDescription:
		'Tailor your invoice templates to reflect your brand, meet regulatory requirements, and cater to client preferences with our user-friendly templates.',
	InvoiceTheme: 'Invoice theme',
	InvoiceTotal: 'Invoice total',
	InvoiceUninvoicedAmounts: 'Invoice uninvoiced amounts',
	InvoiceUpdateVersionMessage:
		'Editing this invoice requires the latest version. Please reload Carepatron and try again.',
	Invoices: '{count, plural, one {Invoice} other {Invoices}}',
	InvoicesEmptyStateDescription: 'No invoices have been found',
	InvoicingAndPayment: 'Invoicing & Payment',
	Ireland: 'Ireland',
	IsA: 'is a',
	IsBetween: 'is between',
	IsEqualTo: 'is equal to',
	IsGreaterThan: 'is greater than',
	IsGreaterThanOrEqualTo: 'is greater than or equal to',
	IsLessThan: 'is less than',
	IsLessThanOrEqualTo: 'is less than or equal to',
	IssueCredit: 'Issue credit',
	IssueCreditAdjustment: 'Issue credit adjustment',
	IssueDate: 'Issue date',
	Italic: 'Italic',
	Items: 'Items',
	ItemsAndAdjustments: 'Items and adjustments',
	ItemsRemaining: '+{count} items remaining',
	JobTitle: 'Job title',
	Join: 'Join',
	JoinCall: 'Join call',
	JoinNow: 'Join now',
	JoinProduct: 'Join {product}',
	JoinVideoCall: 'Join video call',
	JoinWebinar: 'Join webinar',
	JoinWithVideoCall: 'Join with {product}',
	Journalist: 'Journalist',
	JustMe: 'Just me',
	JustYou: 'Just you',
	Justify: 'Justify',
	KeepSeparate: 'Keep separate',
	KeepSeparateSuccessMessage: 'You have successfully kept separate records for {clientNames}',
	KeepWaiting: 'Keep waiting',
	Label: 'Label',
	LabelOptional: 'Label (Optional)',
	LactationConsulting: 'Lactation consulting',
	Language: 'Language',
	Large: 'Large',
	LastDxCode: 'Last DX code',
	LastLoggedIn: 'Last logged in {date} at {time}',
	LastMenstrualPeriod: 'Last menstrual period',
	LastMonth: 'Last month',
	LastNDays: 'Last {number} days',
	LastName: 'Last name',
	LastNameFirstInitial: 'Last name, first initial',
	LastWeek: 'Last week',
	LastXRay: 'Last X-ray',
	LatestVisitOrConsultation: 'Latest visit or consultation',
	Lawyer: 'Lawyer',
	LearnMore: 'Learn more',
	LearnMoreTipsToGettingStarted: 'Learn more tips to getting started',
	LearnToSetupInbox: 'Guide to set up inbox account',
	Leave: 'Leave',
	LeaveCall: 'Leave call',
	LeftAlign: 'Left align',
	LegacyBillingItemsNotAvailable:
		'Individual billing items are not yet available for this appointment. You can still invoice it normally.',
	LegacyBillingItemsNotAvailableTitle: 'Legacy billing',
	LegalAndConsent: 'Legal and consent',
	LegalConsentFormPrimaryText: 'Legal consent',
	LegalConsentFormSecondaryText: 'Accept or decline options',
	LegalGuardian: 'Legal guardian',
	Letter: 'Letter',
	LettersCategoryDescription: 'For creating clinical and admin correspondence',
	Librarian: 'Librarian',
	LicenseNumber: 'License number',
	LifeCoach: 'Life Coach',
	LifeCoaches: 'Life Coaches',
	Limited: 'Limited',
	LineSpacing: 'Line and paragraph spacing',
	LinearScaleFormPrimaryText: 'Linear scale',
	LinearScaleFormSecondaryText: 'Scale options 1-10',
	Lineitems: 'Line items',
	Link: 'Link',
	LinkClientFormSearchClientLabel: 'Search for a client',
	LinkClientModalTitle: 'Link to existing client',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> contact information is added to <strong>{existingName}’s</strong> record.',
	LinkClientSuccessTitle: 'Successful linked to existing contact',
	LinkForCallCopied: 'Link copied!',
	LinkToAnExistingClient: 'Link to an existing client',
	LinkToClient: 'Link to client',
	ListAndTracker: 'List/Tracker',
	ListPeopleInThisMeeting: `
		{n, plural, 
			one {{attendees} is in this call}
			other {{attendees} are in this call}
		}
	`,
	ListStyles: 'List styles',
	ListsAndTrackersCategoryDescription: 'For organizing and tracking work',
	LivingArrangements: 'Living Arrangements',
	LoadMore: 'Load More',
	Loading: 'Loading...',
	LocalizationPanelDescription: 'Manage settings for your language and timezone',
	LocalizationPanelTitle: 'Language and timezone',
	Location: 'Location',
	LocationDescription:
		'Set up physical and virtual locations with specific addresses, room names, and types of virtual spaces to make scheduling appointments and video calls easier.',
	LocationNumber: 'Location number',
	LocationOfService: 'Location of service',
	LocationOfServiceRecommendedActionInfo: 'Adding a specific location to this service may affect your availability.',
	LocationRemote: 'Remote',
	LocationType: 'Location type',
	Locations: 'Locations',
	Lock: 'Lock',
	Locked: 'Locked',
	LockedNote: 'Locked note',
	LogInToSaveOrAuthoriseCard: 'Log in to save or authorise the card',
	LogInToSaveOrAuthorisePayment: 'Log in to save or authorise the payment',
	Login: 'Log in',
	LoginButton: 'Sign in',
	LoginEmail: 'Email',
	LoginForgotPasswordLink: 'Forgot password',
	LoginPassword: 'Password',
	Logo: 'Logo',
	LogoutAreYouSure: 'Sign out of this device.',
	LogoutButton: 'Sign out',
	London: 'London',
	LongTextAnswer: 'Long text answer',
	LongTextFormPrimaryText: 'Long text',
	LongTextFormSecondaryText: 'Paragraph style options',
	Male: 'Male',
	Manage: 'Manage',
	ManageAllClientTags: 'Manage All Client Tags',
	ManageAllNoteTags: 'Manage All Note Tags',
	ManageAllTemplateTags: 'Manage All Template Tags',
	ManageConnections: 'Manage connections',
	ManageConnectionsGmailDescription: 'Other team members won’t be able to see your synced Gmail.',
	ManageConnectionsGoogleCalendarDescription:
		'Other team members won’t be able to see your synced calendars. Clients appointments can only be updated or deleted from within Carepatron.',
	ManageConnectionsInboxSyncHelperText: 'Please go to Inbox page in order to manage Sync Inbox settings.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Other team members won’t be able to see your synced calendars. Clients appointments can only be updated or deleted from within Carepatron.',
	ManageConnectionsOutlookDescription: 'Other team members won’t be able to see your synced Microsoft Outlook.',
	ManageInboxAccountButton: 'New inbox',
	ManageInboxAccountEdit: 'Manage inbox',
	ManageInboxAccountPanelTitle: 'Inboxes',
	ManageInboxAssignTeamPlaceholder: 'Choose team members for inbox access',
	ManageInboxBasicInfoColor: 'Color',
	ManageInboxBasicInfoDescription: 'Description',
	ManageInboxBasicInfoDescriptionPlaceholder: 'What will you or your team use this inbox for?',
	ManageInboxBasicInfoName: 'Inbox name',
	ManageInboxBasicInfoNamePlaceholder: 'E.g customer support, admin',
	ManageInboxConnectAppAlreadyConnectedError: 'The channel you attempted to connect is already connect to Carepatron',
	ManageInboxConnectAppConnect: 'Connect',
	ManageInboxConnectAppConnectedInfo: 'Connected to an account',
	ManageInboxConnectAppContinue: 'Continue',
	ManageInboxConnectAppEmail: 'Email',
	ManageInboxConnectAppSignInWith: 'Sign in with',
	ManageInboxConnectAppSubtitle:
		'Connect your apps to seamlessly send, receive, and track all your communications in one centralized place.',
	ManageInboxNewInboxTitle: 'New inbox',
	ManagePlan: 'Manage plan',
	ManageProfile: 'Manage profile',
	ManageReferralsModalDescription: 'Help us spread the word about our healthcare platform and earn rewards.',
	ManageReferralsModalTitle: 'Refer a friend, earn rewards!',
	ManageStaffRelationshipsAddButton: 'Manage relationships',
	ManageStaffRelationshipsEmptyStateText: 'No relationships added',
	ManageStaffRelationshipsModalDescription:
		'Selecting clients will add new relationships, while unselecting them will remove existing relationships.',
	ManageStaffRelationshipsModalTitle: 'Manage relationships',
	ManageStatuses: 'Manage statuses',
	ManageStatusesActiveStatusHelperText: 'At least one active status is required',
	ManageStatusesDescription: 'Customize your status labels and choose colors to align with your workflow.',
	ManageStatusesSuccessSnackbar: 'Successfully updated statuses',
	ManageTags: 'Manage tags',
	ManageTaskAttendeeStatus: 'Manage appointment statuses',
	ManageTaskAttendeeStatusDescription: 'Customise your appointment statuses to align with your workflow.',
	ManageTaskAttendeeStatusHelperText: 'At least one status is required',
	ManageTaskAttendeeStatusSubtitle: 'Custom statuses',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manual',
	ManualAppointment: 'Manual appointment',
	ManualPayment: 'Manual payment',
	ManuallyTypeLocation: 'Manually type location',
	MapColumns: 'Map columns',
	MappingRequired: 'Mapping required',
	MarkAllAsRead: 'Mark all as read',
	MarkAsCompleted: 'Mark as completed',
	MarkAsManualSubmission: 'Mark as submitted',
	MarkAsPaid: 'Mark as paid',
	MarkAsRead: 'Mark as read',
	MarkAsUnpaid: 'Mark as unpaid',
	MarkAsUnread: 'Mark as unread',
	MarkAsVoid: 'Mark as void',
	Marker: 'Marker',
	MarketingManager: 'Marketing Manager',
	MassageTherapist: 'Massage Therapist',
	MassageTherapists: 'Massage Therapists',
	MassageTherapy: 'Massage therapy',
	MaxBookingTimeDescription1: 'Clients can schedule up to',
	MaxBookingTimeDescription2: 'into the future',
	MaxBookingTimeLabel: '{timePeriod} in advance',
	MaxCapacity: 'Max capacity',
	Maximize: 'Maximize',
	MaximumAttendeeLimit: 'Maximum limit',
	MaximumBookingTime: 'Maximum booking time',
	MaximumBookingTimeError: 'Maximum booking time must not exceed {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'You can minimize up to {count} side panels at a time. Proceeding will close the earliest minimized panel. Do you wish to continue?',
	MaximumMinimizedPanelsReachedTitle: 'You have too many panels open.',
	MechanicalEngineer: 'Mechanical Engineer',
	MediaGallery: 'Media gallery',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid provider number',
	MedicalAssistant: 'Medical Assistant',
	MedicalCoder: 'Medical Coder',
	MedicalDoctor: 'Medical Doctor',
	MedicalIllustrator: 'Medical Illustrator',
	MedicalInterpreter: 'Medical Interpreter',
	MedicalTechnologist: 'Medical Technologist',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare provider number',
	Medicine: 'Medicine',
	Medium: 'Medium',
	Meeting: 'Meeting',
	MeetingEnd: 'End Meeting',
	MeetingEnded: 'Meeting ended',
	MeetingHost: 'Meeting host',
	MeetingLowerHand: 'Lower hand',
	MeetingOpenChat: 'Open Chat',
	MeetingPersonRaisedHand: '{name} raised their hand',
	MeetingRaiseHand: 'Raise hand',
	MeetingReady: 'Meeting ready',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Meetings',
	MemberId: 'Member ID',
	MentalHealth: 'Mental Health',
	MentalHealthPractitioners: 'Mental Health Practitioners',
	MentalHealthProfessional: 'Mental Health Professional',
	Merge: 'Merge',
	MergeClientRecords: 'Merge client records',
	MergeClientRecordsDescription: 'Merging client records will combine all their data, including:',
	MergeClientRecordsDescription2: 'Do you wish to continue with the merge? This action cannot be undone',
	MergeClientRecordsItem1: 'Notes and documents',
	MergeClientRecordsItem2: 'Appointments',
	MergeClientRecordsItem3: 'Invoices',
	MergeClientRecordsItem4: 'Conversations',
	MergeClientsSuccess: 'Successfully merge client record',
	MergeLimitExceeded: 'You can only merge up to 4 clients at a time.',
	Message: 'Message',
	MessageAttachments: '{total} attachments',
	Method: 'Method',
	MfaAvailabilityDisclaimer:
		'MFA is only available for email and password logins. To make changes to your MFA settings, log in using your email and password.',
	MfaDeviceLostPanelDescription: 'Alternatively, you can verify your identity by receiving a code via email.',
	MfaDeviceLostPanelTitle: 'Lost your MFA device?',
	MfaDidntReceiveEmailCode: `Didn't receive a code? Contact support`,
	MfaEmailOtpSendFailureSnackbar: 'Failed to send email OTP.',
	MfaEmailOtpSentSnackbar: 'A code has been sent to {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Failed to verify email OTP.',
	MfaHasBeenSetUpText: `You've set up MFA`,
	MfaPanelDescription:
		'Secure your account by enabling Multi-Factor Authentication (MFA) for an extra layer of protection. Verify your identity through a secondary method to prevent unauthorized access.',
	MfaPanelNotAuthorizedError: 'You must be signed in with username & password to set up MFA.',
	MfaPanelRecommendationDescription:
		'You recently signed in using an alternative method to verify your identity. To keep your account secure, consider setting up a new MFA device.',
	MfaPanelRecommendationTitle: '<strong>Recommended:</strong> Update your MFA device',
	MfaPanelTitle: 'Multi-Factor Authentication (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'You’ll need to verify your email before you can update your MFA settings.',
	MfaRecommendationBannerDescription:
		'You recently signed in using an alternative method to verify your identity. To keep your account secure, consider setting up a new MFA device.',
	MfaRecommendationBannerPrimaryAction: 'Set up MFA',
	MfaRecommendationBannerTitle: 'Recommended',
	MfaRemovedSnackbarTitle: 'MFA has been removed.',
	MfaSendEmailCode: 'Send code',
	MfaVerifyIdentityLostDeviceButton: 'I lost access to my MFA device',
	MfaVerifyYourIdentityPanelDescription: 'Check your authenticator app for the code and enter it below.',
	MfaVerifyYourIdentityPanelTitle: 'Verify your identity',
	MicCamWarningMessage: 'Unblock camera and microphone by clicking blocked icons in browser address bar.',
	MicCamWarningTitle: 'Camera and microphone are blocked',
	MicOff: 'Microphone is off',
	MicOn: 'Microphone is on',
	MicSource: 'Microphone source',
	MicWarningMessage: 'An issue has been detected with your microphone',
	Microphone: 'Microphone',
	MicrophonePermissionBlocked: 'Microphone access blocked',
	MicrophonePermissionBlockedDescription: 'Update your microphone permissions to start recording.',
	MicrophonePermissionError: 'Please grant microphone permission in browser your settings to continue',
	MicrophonePermissionPrompt: 'Please allow microphone access to continue',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlook calendar color',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Teams',
	MiddleEast: 'Middle East',
	MiddleName: 'Middle name',
	MiddleNames: 'Middle name',
	Midwife: 'Midwife',
	Midwives: 'Midwives',
	Milan: 'Milan',
	MinBookingTimeDescription1: `Clients can't schedule within`,
	MinBookingTimeDescription2: `of an appointment's start time`,
	MinBookingTimeLabel: '{timePeriod} before appointment',
	MinCancellationTimeEditModeDescription: 'Set how many hours a client can cancel without penalty',
	MinCancellationTimeUnset: 'No minimum cancellation time set',
	MinCancellationTimeViewModeDescription: 'Cancellation period without penalty',
	MinMaxBookingTimeUnset: 'No time set',
	Minimize: 'Minimize',
	MinimizeConfirmationDescription:
		'You have an active minimized panel. If you continue, it will close, and you may lose unsaved data.',
	MinimizeConfirmationTitle: 'Close minimized panel?',
	MinimumBookingTime: 'Minimum booking time',
	MinimumCancellationTime: 'Minimum cancellation time',
	MinimumPaymentError: 'A minimum charge of {minimumAmount} is required for online payments',
	MinuteAbbreviated: 'mins',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minutely',
	MinutesPlural: '{age, plural, one {# minute} other {# minutes}}',
	MiscellaneousInformation: 'Miscellaneous information',
	MissingFeatures: 'Missing features',
	MissingPaymentMethod: 'Please add a payment method to your subscription to add more staff members.',
	MobileNumber: 'Mobile number',
	MobileNumberOptional: 'Mobile Number (optional)',
	Modern: 'Modern',
	Modifiers: 'Modifiers',
	ModifiersPlaceholder: 'Modifiers',
	Monday: 'Monday',
	Month: 'Month',
	Monthly: 'Monthly',
	MonthlyCost: 'Monthly Cost',
	MonthlyOn: 'Monthly on {date}',
	MonthsPlural: '{age, plural, one {# month} other {# months}}',
	More: 'More',
	MoreActions: 'More actions',
	MoreSettings: 'More settings',
	MoreThanTen: '10+',
	MostCommonlyUsed: 'Most commonly used',
	MostDownloaded: 'Most downloaded',
	MostPopular: 'Most popular',
	Mother: 'Mother',
	MotherInLaw: 'Mother-in-law',
	MoveDown: 'Move down',
	MoveInboxConfirmationDescription:
		'Reassigning this app connection will remove it from the <strong>{currentInboxName}</strong> inbox.',
	MoveTemplateToFolder: 'Move `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} moved to {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Successfully moved to default intake folder',
	MoveTemplateToNewFolder: 'Create a new folder to move this item to.',
	MoveToChosenFolder: 'Choose a folder to move this item to. You can create a new folder if needed.',
	MoveToFolder: 'Move to folder',
	MoveToInbox: 'Move to Inbox',
	MoveToNewFolder: 'Move to new folder',
	MoveToSelectedFolder:
		'Once moved, the item will be organized under the selected folder and will no longer appear in its current location.',
	MoveUp: 'Move up',
	MultiSpeciality: 'Multi-speciality',
	MultipleChoiceFormPrimaryText: 'Multiple choice',
	MultipleChoiceFormSecondaryText: 'Choose multiple options',
	MultipleChoiceGridFormPrimaryText: 'Multiple choice grid',
	MultipleChoiceGridFormSecondaryText: 'Choose options from a matrix',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Music Therapist',
	MustContainOneLetterError: 'Must contain at least one letter',
	MustEndWithANumber: 'Must end with a number',
	MustHaveAtLeastXItems: 'Must have at least {count, plural, one {# item} other {# items}}',
	MuteAudio: 'Mute audio',
	MuteEveryone: 'Mute everyone',
	MyAvailability: 'My availability',
	MyGallery: 'My gallery',
	MyPortal: 'My Portal',
	MyRelationships: 'My relationships',
	MyTemplates: 'Team Templates',
	MyofunctionalTherapist: 'Myofunctional Therapist',
	NCalifornia: 'North California',
	NPI: 'NPI',
	NVirginia: 'North Virginia',
	Name: 'Name',
	NameIsRequired: 'Name is required',
	NameMustNotBeAWebsite: 'Name must not be a website',
	NameMustNotBeAnEmail: 'Name must not be an email',
	NameMustNotContainAtSign: 'Name must not contain @ sign',
	NameMustNotContainHTMLTags: 'Name must not contain HTML tags',
	NameMustNotContainSpecialCharacters: 'Name must not contain special characters',
	NameOnCard: 'Name on card',
	NationalProviderId: 'National provider identifier (NPI)',
	NaturopathicDoctor: 'Naturopathic Doctor',
	NavigateToPersonalSettings: 'Profile',
	NavigateToSubscriptionSettings: 'Subscription settings',
	NavigateToWorkspaceSettings: 'Workspace settings',
	NavigateToYourTeam: 'Manage team',
	NavigationDrawerBilling: 'Billing',
	NavigationDrawerBillingInfo: 'Billing info, invoices, and Stripe',
	NavigationDrawerCommunication: 'Communication',
	NavigationDrawerCommunicationInfo: 'Notifications and templates',
	NavigationDrawerInsurance: 'Insurance',
	NavigationDrawerInsuranceInfo: 'Insurance payers and claims',
	NavigationDrawerInvoices: 'Billing',
	NavigationDrawerPersonal: 'My Profile',
	NavigationDrawerPersonalInfo: 'Your personal details',
	NavigationDrawerProfile: 'Profile',
	NavigationDrawerProviderSettings: 'Settings',
	NavigationDrawerScheduling: 'Scheduling',
	NavigationDrawerSchedulingInfo: 'Services details and bookings',
	NavigationDrawerSettings: 'Settings',
	NavigationDrawerTemplates: 'Templates',
	NavigationDrawerTemplatesV2: 'Templates V2',
	NavigationDrawerTrash: 'Trash',
	NavigationDrawerTrashInfo: 'Restore deleted items',
	NavigationDrawerWorkspace: 'Workspace Settings',
	NavigationDrawerWorkspaceInfo: 'Subscription and workspace info',
	NegativeBalanceNotSupported: 'Negative account balances are not supported',
	Nephew: 'Nephew',
	NetworkQualityFair: 'Fair connection',
	NetworkQualityGood: 'Good connection',
	NetworkQualityPoor: 'Poor connection',
	Neurologist: 'Neurologist',
	Never: 'Never',
	New: 'New',
	NewAppointment: 'New appointment',
	NewClaim: 'New claim',
	NewClient: 'New client',
	NewClientNextStepsModalAddAnotherClient: 'Add another client',
	NewClientNextStepsModalBookAppointment: 'Book appointment',
	NewClientNextStepsModalBookAppointmentDescription: 'Book an upcoming appointment or create a task.',
	NewClientNextStepsModalCompleteBasicInformation: 'Complete client record',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'Add client information and capture next steps.',
	NewClientNextStepsModalCreateInvoice: 'Create invoice',
	NewClientNextStepsModalCreateInvoiceDescription: 'Add client payment information or create an invoice.',
	NewClientNextStepsModalCreateNote: 'Create note or upload document',
	NewClientNextStepsModalCreateNoteDescription: 'Capture client notes and documentation.',
	NewClientNextStepsModalDescription: 'Here are some actions to take now you have created a client record.',
	NewClientNextStepsModalSendIntake: 'Send intake',
	NewClientNextStepsModalSendIntakeDescription:
		'Collect client information and send additional forms for completion and signing.',
	NewClientNextStepsModalSendMessage: 'Send message',
	NewClientNextStepsModalSendMessageDescription: 'Compose and send a message to your client.',
	NewClientNextStepsModalTitle: 'Next steps',
	NewClientSuccess: 'Successfully created new client',
	NewClients: 'New clients',
	NewConnectedApp: 'New connected app',
	NewContact: 'New contact',
	NewContactNextStepsModalAddRelationship: 'Add relationship',
	NewContactNextStepsModalAddRelationshipDescription: 'Link this contact to related clients or groups.',
	NewContactNextStepsModalBookAppointment: 'Book appointment',
	NewContactNextStepsModalBookAppointmentDescription: 'Book an upcoming appointment or create a task.',
	NewContactNextStepsModalCompleteProfile: 'Complete profile',
	NewContactNextStepsModalCompleteProfileDescription: 'Add contact information and capture next steps.',
	NewContactNextStepsModalCreateNote: 'Create note or upload document',
	NewContactNextStepsModalCreateNoteDescription: 'Capture client notes and documentation.',
	NewContactNextStepsModalDescription: 'Here are some actions to take now you have created a contact.',
	NewContactNextStepsModalInviteToPortal: 'Invite to portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Send an invitation to access the portal.',
	NewContactNextStepsModalTitle: 'Next steps',
	NewContactSuccess: 'Successfully created new contact',
	NewDateOverrideButton: 'New date override',
	NewDiagnosis: 'Add diagnosis',
	NewField: 'New field',
	NewFolder: 'New folder',
	NewInvoice: 'New invoice',
	NewLocation: 'New location',
	NewLocationFailure: 'Failed to create new location',
	NewLocationSuccess: 'Successfully created new location',
	NewManualPayer: 'New manual payer',
	NewNote: 'New note',
	NewNoteCreated: 'Successfully created new note',
	NewPassword: 'New password',
	NewPayer: 'New payer',
	NewPaymentMethod: 'New payment method',
	NewPolicy: 'New policy',
	NewRelationship: 'New relationship',
	NewReminder: 'New reminder',
	NewSchedule: 'New schedule',
	NewSection: 'New section',
	NewSectionOld: 'New section [OLD]',
	NewSectionWithGrid: 'New section with grid',
	NewService: 'New service',
	NewServiceFailure: 'Failed to create new service',
	NewServiceSuccess: 'Successfully created new service',
	NewStatus: 'New status',
	NewTask: 'New task',
	NewTaxRate: 'New tax rate',
	NewTeamMemberNextStepsModalAssignClients: 'Assign clients',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Assign specific clients to your team member.',
	NewTeamMemberNextStepsModalAssignServices: 'Assign services',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Manage their assigned services and adjust pricing as needed.',
	NewTeamMemberNextStepsModalBookAppointment: 'Book appointment',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Book an upcoming appointment or create a task.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Complete profile',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Add details about your team member to complete their profile.',
	NewTeamMemberNextStepsModalDescription: 'Here are some actions to take now you have created a team member.',
	NewTeamMemberNextStepsModalEditPermissions: 'Edit permissions',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Adjust their access levels to ensure they have the right permissions.',
	NewTeamMemberNextStepsModalSetAvailability: 'Set availability',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Configure their availability to create schedules.',
	NewTeamMemberNextStepsModalTitle: 'Next steps',
	NewTemplateFolderDescription: 'Create a new folder to organize your documentation.',
	NewUIUpdateBannerButton: 'Reload app',
	NewUIUpdateBannerTitle: `There's a new update ready!`,
	NewZealand: 'New Zealand',
	Newest: 'Newest',
	NewestUnreplied: 'Newest unreplied',
	Next: 'Next',
	NextInvoiceIssueDate: 'Next invoice issue date',
	NextNDays: 'Next {number} days',
	Niece: 'Niece',
	No: 'No',
	NoAccessGiven: 'No access given',
	NoActionConfigured: 'No action configured',
	NoActivePolicies: 'No active policies',
	NoActiveReferrals: 'You have no active referrals',
	NoAppointmentsFound: 'No appointments have been found',
	NoAppointmentsHeading: 'Manage client appointments and activity',
	NoArchivedPolicies: 'No archived policies',
	NoAvailableTimes: 'No available times found.',
	NoBillingItemsFound: 'No billing items found',
	NoCalendarsSynced: 'No calendars synced',
	NoClaimsFound: 'No claims found',
	NoClaimsHeading: 'Streamline submitting claims for reimbursement',
	NoClientsHeading: 'Bring together your client records',
	NoCompletedReferrals: 'You have no complete referrals',
	NoConnectionsHeading: 'Streamline your client communications',
	NoContactsGivenAccess: 'No clients or contacts have been given access to this note',
	NoContactsHeading: 'Stay connected with those who support your practice',
	NoCopayOrCoinsurance: 'No co-pay or co-insurance',
	NoCustomServiceSchedule: 'No custom schedule set — the availability is dependent on the team member’s availability',
	NoDescription: 'No description',
	NoDocumentationHeading: 'Securely create and store notes',
	NoDuplicateRecordsHeading: 'Your client record is free of duplicates',
	NoEffect: 'No effect',
	NoEnrolmentProfilesFound: 'No enrollment profiles found',
	NoGlossaryItems: 'No glossary items',
	NoInvitedReferrals: 'You have no invited referrals',
	NoInvoicesFound: 'No invoices found',
	NoInvoicesHeading: 'Automate your billing and payments',
	NoLimit: 'No limit',
	NoLocationsFound: 'No locations have been found',
	NoLocationsWillBeAdded: 'No locations will be added.',
	NoNoteFound: 'No note found',
	NoPaymentMethods: 'You have no saved payment methods, you can add one when making a payment.',
	NoPermissionError: 'You do not have permission',
	NoPermissions: 'You do not have permission to view this page',
	NoPolicy: 'No cancellation policy added',
	NoRecordsHeading: 'Personalize your client records',
	NoRecordsToDisplay: 'No {resource} to display',
	NoRelationshipsHeading: 'Stay connected with those who support your client',
	NoRemindersFound: 'No reminders found',
	NoResultsFound: 'No results found',
	NoResultsFoundDescription: 'We can’t find any items matching your search',
	NoServicesAdded: 'No services added',
	NoServicesApplied: 'No services applied',
	NoServicesWillBeAdded: 'No services will be added.',
	NoTemplate: 'You have no practice templates saved',
	NoTemplatesHeading: 'Create your own templates',
	NoTemplatesInFolder: 'No templates in this folder',
	NoTitle: 'No title',
	NoTrashItemsHeading: 'No deleted item found',
	NoTriggerConfigured: 'No trigger configured',
	NoUnclaimedItemsFound: 'No unclaimed items found.',
	NonAiTemplates: 'Non-AI templates',
	None: 'None',
	NotAvailable: 'Not available',
	NotCovered: 'Not covered',
	NotFoundSnackbar: 'Resource not found.',
	NotRequiredField: 'Not required',
	Note: 'Note',
	NoteDuplicateSuccess: 'Successfully duplicated note',
	NoteEditModeViewSwitcherDescription: 'Create and edit note',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} submitted {noteTitle} form',
	NoteLockSuccess: '{title} has been locked',
	NoteModalAttachmentButton: 'Add attachments',
	NoteModalPhotoButton: 'Add/Capture photos',
	NoteModalTrascribeButton: 'Transcribe live audio',
	NoteResponderModeViewSwitcherDescription: 'Send forms and review responses',
	NoteResponderModeViewSwitcherTooltipTitle: 'Respond and submit forms on behalf of your clients',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Fill out and submit forms as a client',
	NoteUnlockSuccess: '{title} has been unlocked',
	NoteViewModeViewSwitcherDescription: 'View-only access',
	Notes: 'Notes',
	NotesAndForms: 'Notes and Forms',
	NotesCategoryDescription: 'For documenting client interactions',
	NothingToSeeHere: 'Nothing to see here',
	Notification: 'Notification',
	NotificationIgnoredMessage: 'All {notificationType} notifications will be ignored',
	NotificationRestoredMessage: 'All {notificationType} notifications restored',
	NotificationSettingBillingDescription: 'Receive notifications for client payment updates and reminders.',
	NotificationSettingBillingTitle: 'Billing and payment',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} only} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Receive notifications for client payment updates and reminders.',
	NotificationSettingClientDocumentationTitle: 'Client and documentation',
	NotificationSettingCommunicationsDescription:
		'Receive notifications for inbox and updates from your connected channels',
	NotificationSettingCommunicationsTitle: 'Communications',
	NotificationSettingEmail: 'Email',
	NotificationSettingInApp: 'In-app',
	NotificationSettingPanelDescription:
		'Choose the notifications you`d like to receive for activities and recommendations.',
	NotificationSettingPanelTitle: 'Notification preferences',
	NotificationSettingSchedulingDescription:
		'Receive notifications when a team member or client books, reschedules, or cancels their appointment.',
	NotificationSettingSchedulingTitle: 'Scheduling',
	NotificationSettingUpdateSuccess: 'Notification settings updated successfully',
	NotificationSettingWhereYouReceiveNotifications: 'Where you want to receive these notifications',
	NotificationSettingWorkspaceDescription:
		'Receive notifications for system changes, issues, data transfers and subscription reminders',
	NotificationSettingWorkspaceTitle: 'Workspace',
	NotificationTemplateUpdateFailed: 'Failed to update notification template',
	NotificationTemplateUpdateSuccess: 'Successfully updated notification template',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Would you like to send a cancellation notification email to attendees?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Send cancellation',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Would you like to send a confirmation notification email to attendees?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Send confirmation',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Would you like to send cancellation emails to attendees?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {does} other {do}} not have an email address so will not receive automated notifications and reminders.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {does} other {do}} not have an email address so will not receive automated notifications and reminders.',
	NotifyAttendeesOfTaskModalTitle: 'Would you like to send a notification email to attendees?',
	NotifyAttendeesOfTaskSnackbar: 'Sending notification',
	NuclearMedicineTechnologist: 'Nuclear Medicine Technologist',
	NumberOfClaims: '{number, plural, one {# Claim} other {# Claims}}',
	NumberOfClients: '{number, plural, one {# Client} other {# Clients}}',
	NumberOfContacts: '{number, plural, one {# Contact} other {# Contacts}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entry} other {entries}} found',
	NumberOfErrors: '{count, plural, one {# error} other {# errors}}',
	NumberOfInvoices: '{number, plural, one {# Invoice} other {# Invoices}}',
	NumberOfLineitemsToCredit:
		'You have <mark>{count} {count, plural, one {line item} other {line items}}</mark> to issue a credit for.',
	NumberOfPayments: '{number, plural, one {# Payment} other {# Payments}}',
	NumberOfRelationships: '{number, plural, one {# Relationship} other {# Relationships}}',
	NumberOfResources: '{number, plural, one {# Resource} other {# Resources}}',
	NumberOfTeamMembers: '{number, plural, one {# Team member} other {# Team members}}',
	NumberOfTrashItems: '{number, plural, one {# item} other {# items}}',
	NumberOfUninvoicedAmounts:
		'You have <mark>{count} uninvoiced {count, plural, one {amount} other {amounts}}</mark> to be invoiced',
	NumberedList: 'Numbered list',
	Nurse: 'Nurse',
	NurseAnesthetist: 'Nurse Anesthetist',
	NurseAssistant: 'Nurse Assistant',
	NurseEducator: 'Nurse Educator',
	NurseMidwife: 'Nurse Midwife',
	NursePractitioner: 'Nurse Practitioner',
	Nurses: 'Nurses',
	Nursing: 'Nursing',
	Nutritionist: 'Nutritionist',
	Nutritionists: 'Nutritionists',
	ObstetricianOrGynecologist: 'Obstetrician/Gynecologist',
	Occupation: 'Occupation',
	OccupationalTherapist: 'Occupational Therapist',
	OccupationalTherapists: 'Occupational Therapists',
	OccupationalTherapy: 'Occupational therapy',
	Occurrences: 'Occurrences',
	Of: 'of',
	Ohio: 'Ohio',
	OldPassword: 'Old password',
	OlderMessages: '{count} older messages',
	Oldest: 'Oldest',
	OldestUnreplied: 'Oldest unreplied',
	On: 'on',
	OnboardingBusinessAgreement: 'On behalf of myself and the business, I agree to the {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Occupational therapists</mark> make up a quarter of our customers on Carepatron',
	OnboardingLoadingProfession: 'We’ve got loads of <mark>{profession}</mark> using and thriving on Carepatron.',
	OnboardingLoadingPsychologist: '<mark>Psychologists</mark> make up over half of our customers on Carepatron',
	OnboardingLoadingSubtitleFive: 'Our mission is to make <mark>healthcare software accessible</mark> to everyone.',
	OnboardingLoadingSubtitleFour: '<mark>Simplified health software</mark> for more than 10,000 people worldwide.',
	OnboardingLoadingSubtitleThree:
		'Save <mark>1 day per week</mark> on administration tasks with the help of Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Save <mark>2 hours</mark> daily on administration tasks with the help of Carepatron.',
	OnboardingReviewLocationOne: 'Holland Park Mental Health Centre',
	OnboardingReviewLocationThree: 'Practice Nurse, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Life House Clinic',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron is super intuitive to use. It helps us run our practice so well that we don’t even need a team of administrators anymore"',
	OnboardingReviewThree:
		'"It’s the best practice solution I have used both in terms of features and cost. It has everything I need to grow my business"',
	OnboardingReviewTwo:
		'"I also love the carepatron app. Helps me keep track of my clients and work while on the go."',
	OnboardingTitle: `Let’s get to <mark>know
you better</mark>`,
	Oncologist: 'Oncologist',
	Online: 'Online',
	OnlineBookingColorTheme: 'Online booking color theme',
	OnlineBookings: 'Online bookings',
	OnlineBookingsHelper: 'Choose when online bookings can be made and by which type of clients',
	OnlinePayment: 'Online payment',
	OnlinePaymentSettingCustomInfo: 'Online payment settings for this service differ from the global booking settings.',
	OnlinePaymentSettings: 'Online payment settings',
	OnlinePaymentSettingsInfo:
		'Collect payments for services at the time of online booking to secure and streamline payments',
	OnlinePaymentSettingsPaymentsDisabled:
		'Payments are disabled so can not be collected during online booking. Please check your payment settings to enable payments.',
	OnlinePaymentSettingsStripeNote: '{action} to receive online booking payments and streamline your payment process',
	OnlinePaymentsNotSupportedForCurrency: 'Online payments are not supported in {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'Sorry, online payments are not supported in this currency',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Sorry, online payments are not yet supported in your country',
	OnlineScheduling: 'Online Scheduling',
	OnlyVisibleToYou: 'Only visible to You',
	OnlyYou: 'Only you',
	OnsetDate: 'Onset date',
	OnsetOfCurrentSymptomsOrIllness: 'Onset of current symptoms or illness',
	Open: 'Open',
	OpenFile: 'Open file',
	OpenSettings: 'Open settings',
	Ophthalmologist: 'Ophthalmologist',
	OptimiseTelehealthCalls: 'Optimize your Telehealth calls',
	OptimizeServiceTimes: 'Optimize service times',
	Options: 'Options',
	Optometrist: 'Optometrist',
	Or: 'or',
	OrAttachSingleFile: 'attach a file',
	OrDragAndDrop: 'or drag and drop',
	OrderBy: 'Order By',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisation or individual',
	OrganizationPlanInclusion1: 'Advanced permissions',
	OrganizationPlanInclusion2: 'Free client data import support',
	OrganizationPlanInclusion3: 'Dedicated success manager',
	OrganizationPlanInclusionHeader: 'Everything in Professional, plus...',
	Orthodontist: 'Orthodontist',
	Orthotist: 'Orthotist',
	Other: 'Other',
	OtherAdjustments: 'Other adjustments',
	OtherAdjustmentsTableEmptyState: 'No adjustments found',
	OtherEvents: 'Other events',
	OtherId: 'Other ID',
	OtherIdQualifier: 'Other ID qualifier',
	OtherPaymentMethod: 'Other payment method',
	OtherPlanMessage:
		'Stay in control of your practice’s needs. Review your current plan, monitor usage, and explore upgrade options to unlock more features as your team grows.',
	OtherPolicy: 'Other insurance',
	OtherProducts: 'What other products or tools do you use?',
	OtherServices: 'Other services',
	OtherTemplates: 'Other templates',
	Others: 'Others',
	OthersPeople: `
	{n, plural, 
		one {1 other person}
		other {# others people}
	}`,
	OurResearchTeamReachOut:
		'Can our research team reach out to learn more about how Carepatron could have been better for your needs?',
	OutOfOffice: 'Out of office',
	OutOfOfficeColor: 'Out of office color',
	OutOfOfficeHelper: 'Some team members chosen are out of office',
	OutsideLabCharges: 'Outside lab charges',
	OutsideOfWorkingHours: 'Outside of working hours',
	OutsideWorkingHoursHelper: 'Some team members chosen are outside of working hours',
	Overallocated: 'Overallocated',
	OverallocatedPaymentDescription: `This payment has been over allocated to billable items.
Add an allocation to unpaid items, or issue a credit or refund.`,
	OverallocatedPaymentTitle: 'Over allocated payment',
	OverdueTerm: 'Overdue term (days)',
	OverinvoicedAmount: 'Overinvoiced amount',
	Overpaid: 'Overpaid',
	OverpaidAmount: 'Overpaid amount',
	Overtime: 'overtime',
	Owner: 'Owner',
	POS: 'POS',
	POSCode: 'POS code',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Unsaved changes will be lost. Still want to leave?',
	PageBlockerTitle: 'Discard changes?',
	PageFormat: 'Page format',
	PageNotFound: 'Page not found',
	PageNotFoundDescription: `You no longer have access to this page or it can't be found`,
	PageUnauthorised: 'Unauthorised access',
	PageUnauthorisedDescription: 'You do not have permission to access this page',
	Paid: 'Paid',
	PaidAmount: 'Paid amount',
	PaidAmountMinimumValueError: 'Paid amount must be greater than 0',
	PaidAmountRequiredError: 'Paid amount is required',
	PaidItems: 'Paid items',
	PaidMultiple: 'Paid',
	PaidOut: 'Paid out',
	ParagraphStyles: 'Paragraph styles',
	Parent: 'Parent',
	Paris: 'Paris',
	PartialRefundAmount: 'Partially refunded ({amount} remaining)',
	PartiallyFull: 'Partially full',
	PartiallyPaid: 'Partially paid',
	PartiallyRefunded: 'Partially refunded',
	Partner: 'Partner',
	Password: 'Password',
	Past: 'Past',
	PastDateOverridesEmpty: 'Your date overrides will appear here as soon as the event has passed',
	Pathologist: 'Pathologist',
	Patient: 'Patient',
	Pause: 'Pause',
	Paused: 'Paused',
	Pay: 'Pay',
	PayMonthly: 'Pay monthly',
	PayNow: 'Pay now',
	PayValue: 'Pay {showPrice, select, true {{price}} other {now}}',
	PayWithOtherCard: 'Pay with other card',
	PayYearly: 'Pay yearly',
	PayYearlyPercentOff: 'Pay yearly <mark>{percent}% off</mark>',
	Payer: 'Payer',
	PayerClaimId: 'Payer claim id',
	PayerCoverage: 'Coverage',
	PayerDetails: 'Payer details',
	PayerDetailsDescription: 'View the payers details that have been added to your account and manage enrolment.',
	PayerID: 'Payer ID',
	PayerId: 'Payer ID',
	PayerName: 'Payer name',
	PayerPhoneNumber: 'Payer phone number',
	Payers: 'Payers',
	Payment: 'Payment',
	PaymentAccountUpdated: 'Your account has been updated!',
	PaymentAccountUpgraded: 'Your account has been upgraded!',
	PaymentAmount: 'Payment amount',
	PaymentDate: 'Payment date',
	PaymentDetails: 'Payment details',
	PaymentForUsersPerMonth: 'Payment for {billedUsers, plural, one {# user} other {# users}} a month',
	PaymentInfoFormPrimaryText: 'Payment information',
	PaymentInfoFormSecondaryText: 'Gather payment details',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'This invoice has already been paid.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'This invoice is already processing.',
	PaymentIntentAmountMismatchSnackbar:
		'The total amount of the invoice has been modified. Please review the changes before paying.',
	PaymentIntentSyncTimeoutSnackbar:
		'Your payment was successful but a timeout occurred. Please refresh the page and if your payment is not shown please contact support.',
	PaymentMethod: 'Payment method',
	PaymentMethodDescription:
		'Add and manage your practice payment method to streamline your subscription billing process.',
	PaymentMethodLabelBank: 'bank account',
	PaymentMethodLabelCard: 'card',
	PaymentMethodLabelFallback: 'payment method',
	PaymentMethodRequired: 'Please add a payment method before changing subscriptions',
	PaymentMethods: 'Payment methods',
	PaymentProcessing: 'Payment processing!',
	PaymentProcessingFee: 'Payment includes {amount} processing fee',
	PaymentReports: 'Payment reports (ERA)',
	PaymentSettings: 'Payment settings',
	PaymentSuccessful: 'Payment successful!',
	PaymentType: 'Payment type',
	Payments: 'Payments',
	PaymentsAccountDisabledNotificationSubject: `Online payments through {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} have been disabled.
Please check your payment settings to enable payments.`,
	PaymentsEmptyStateDescription: 'No payments have been found.',
	PaymentsUnallocated: 'Unallocated payments',
	PayoutDate: 'Payout date',
	PayoutsDisabled: 'Payouts disabled',
	PayoutsEnabled: 'Payouts enabled',
	PayoutsStatus: 'Payout status',
	Pediatrician: 'Pediatrician',
	Pen: 'Pen',
	Pending: 'Pending',
	People: '{rosterSize } people',
	PeopleCount: 'People ({count})',
	PerMonth: '/ Month',
	PerUser: 'Per user',
	Permission: 'Permission',
	PermissionRequired: 'Permission required',
	Permissions: 'Permissions',
	PermissionsClientAndContactDocumentation: 'Client & Contact documentation',
	PermissionsClientAndContactProfiles: 'Client & Contact profiles',
	PermissionsEditAccess: 'Edit access',
	PermissionsInvoicesAndPayments: 'Invoices & Payments',
	PermissionsScheduling: 'Scheduling',
	PermissionsUnassignClients: 'Unassign clients',
	PermissionsUnassignClientsConfirmation: 'Are you sure you want to unassign these clients?',
	PermissionsValuesAssigned: 'Assigned only',
	PermissionsValuesEverything: 'Everything',
	PermissionsValuesNone: 'None',
	PermissionsValuesOwnCalendar: 'Own calendar',
	PermissionsViewAccess: 'View access',
	PermissionsWorkspaceSettings: 'Workspace settings',
	Person: '{rosterSize} person',
	PersonalDetails: 'Personal details',
	PersonalHealthcareHistoryStoreDescription:
		'Answer and securely store your personal healthcare history in one place',
	PersonalTrainer: 'Personal Trainer',
	PersonalTraining: 'Personal Training',
	PersonalizeWorkspace: 'Personalize your workspace',
	PersonalizingYourWorkspace: 'Personalizing your workspace',
	Pharmacist: 'Pharmacist',
	Pharmacy: 'Pharmacy',
	PhoneCall: 'Phone call',
	PhoneNumber: 'Phone number',
	PhoneNumberOptional: 'Phone number (optional)',
	PhotoBy: 'Photo by',
	PhysicalAddress: 'Physical address',
	PhysicalTherapist: 'Physical Therapist',
	PhysicalTherapists: 'Physical Therapists',
	PhysicalTherapy: 'Physical therapy',
	Physician: 'Physician',
	PhysicianAssistant: 'Physician Assistant',
	Physicians: 'Physicians',
	Physiotherapist: 'Physiotherapist',
	PlaceOfService: 'Place of service',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Report',
	PlanId: 'Plan ID',
	PlansAndReportsCategoryDescription: 'For treatment planning and summarizing outcomes',
	PleaseRefreshThisPageToTryAgain: 'Please refresh this page to try again.',
	PleaseWait: 'Please wait...',
	PleaseWaitForHostToJoin: 'Waiting for Host to join...',
	PleaseWaitForHostToStart: 'Please wait for the Host to start this meeting.',
	PlusAdd: '+ Add',
	PlusOthers: '+{count} others',
	PlusPlanInclusionFive: 'Shared inboxes',
	PlusPlanInclusionFour: 'Group video calls',
	PlusPlanInclusionHeader: 'Everything in Essential +',
	PlusPlanInclusionOne: 'Unlimited AI',
	PlusPlanInclusionSix: 'Custom branding',
	PlusPlanInclusionThree: 'Group scheduling',
	PlusPlanInclusionTwo: 'Unlimited storage & tasks',
	PlusSubscriptionPlanSubtitle: 'For practices to optimize and grow',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Police Officer',
	PolicyDates: 'Policy dates',
	PolicyHolder: 'Policy holder',
	PolicyHoldersAddress: 'Policy holder address',
	PolicyMemberId: 'Policy Member Id',
	PolicyStatus: 'Policy status',
	Popular: 'Popular',
	PortalAccess: 'Portal access',
	PortalNoAppointmentsHeading: 'Keep track of all upcoming and past appointments',
	PortalNoDocumentationHeading: 'Securely create and store your documents',
	PortalNoRelationshipsHeading: 'Bring together those who support your journey',
	PosCodeErrorMessage: 'POS code is required',
	PosoNumber: 'PO/SO number',
	PossibleClientDuplicate: 'Possible client duplicate',
	PotentialClientDuplicateTitle: 'Potential duplicate client record',
	PotentialClientDuplicateWarning:
		'This client information may already exist in your client list. Please verify and update the existing record if necessary or continue to create new client.',
	PoweredBy: 'Powered by',
	Practice: 'Practice',
	PracticeDetails: 'Practice details',
	PracticeInfoHeader: 'Business information',
	PracticeInfoPlaceholder: `Practice name,
National provider identifier,
Employer identification number`,
	PracticeLocation: 'Looks like your practice is in',
	PracticeSettingsAvailabilityTab: 'Availability',
	PracticeSettingsBillingTab: 'Billing settings',
	PracticeSettingsClientSettingsTab: 'Client settings',
	PracticeSettingsGeneralTab: 'General',
	PracticeSettingsOnlineBookingTab: 'Online booking',
	PracticeSettingsServicesTab: 'Services',
	PracticeSettingsTaxRatesTab: 'Tax rates',
	PracticeTemplate: 'Practice Template',
	Practitioner: 'Practitioner',
	PreferredLanguage: 'Preferred language',
	PreferredName: 'Preferred name',
	Prescription: 'Prescription',
	PreventionSpecialist: 'Prevention Specialist',
	Preview: 'Preview',
	PreviewAndSend: 'Preview and send',
	PreviewUnavailable: 'Preview unavailable for this file type',
	PreviousNotes: 'Previous notes',
	Price: 'Price',
	PriceError: 'Price must be greater than 0',
	PricePerClient: 'Price per client',
	PricePerUser: 'Per user',
	PricePerUserBilledAnnually: 'Per user billed annually',
	PricePerUserPerPeriod: '{price} per user / {isMonthly, select, true {month} other {year}}',
	PricingGuide: 'Guide to pricing plans',
	PricingPlanPerMonth: '/ month',
	PricingPlanPerYear: '/ year',
	Primary: 'Primary',
	PrimaryInsurance: 'Primary insurance',
	PrimaryPolicy: 'Primary insurance',
	PrimaryTimezone: 'Primary timezone',
	Print: 'Print',
	PrintToCms1500: 'Print to CMS1500',
	PrivatePracticeConsultant: 'Private Practice Consultant',
	Proceed: 'Proceed',
	ProcessAtTimeOfBookingDesc: 'Clients must pay the full service price to book online',
	ProcessAtTimeOfBookingLabel: 'Process payments at time of booking',
	Processing: 'Processing',
	ProcessingFee: 'Processing fee',
	ProcessingFeeToolTip: `Carepatron allows you to oncharge the processing fees to your customers. 
 In some jurisdictions it is prohibited to charge processing fees to your customers. It is your responsibility to comply with applicable laws.`,
	ProcessingRequest: 'Processing request...',
	Product: 'Product',
	Profession: 'Profession',
	ProfessionExample: 'Therapist, Nutritionist, Dentist',
	ProfessionPlaceholder: 'Start typing your profession or choose from the list',
	ProfessionalPlanInclusion1: 'Unlimited storage',
	ProfessionalPlanInclusion2: 'Unlimited tasks',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 customer support',
	ProfessionalPlanInclusion5: 'SMS reminders',
	ProfessionalPlanInclusionHeader: 'Everything in Starter, plus...',
	Professions: 'Professions',
	Profile: 'Profile',
	ProfilePhotoFileSizeLimit: '5MB file size limit',
	ProfilePopoverSubTitle: `You're signed in as <strong>{email}</strong>`,
	ProfilePopoverTitle: 'Your workspaces',
	PromoCode: 'Promo code',
	PromotionCodeApplied: '{promo} applied',
	ProposeNewDateTime: 'Propose a new date/time',
	Prosthetist: 'Prosthetist',
	Provider: 'Provider',
	ProviderBillingPlanExpansionManageButton: 'Manage plan',
	ProviderCommercialNumber: 'Provider commercial number',
	ProviderDetails: 'Provider details',
	ProviderDetailsAddress: 'Address',
	ProviderDetailsName: 'Name',
	ProviderDetailsPhoneNumber: 'Phone number',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'Sorry, this provider already has an existing billing account',
	ProviderInfoPlaceholder: `Staff name, 
Email address,
Phone number,
National provider identifier,
License number`,
	ProviderIsChargedProcessingFee: 'You will pay the processing fee',
	ProviderPaymentFormBackButton: 'Back',
	ProviderPaymentFormBillingAddressCity: 'City',
	ProviderPaymentFormBillingAddressCountry: 'Country',
	ProviderPaymentFormBillingAddressLine1: 'Line1',
	ProviderPaymentFormBillingAddressPostalCode: 'Postal code',
	ProviderPaymentFormBillingEmail: 'Email',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Credit card details',
	ProviderPaymentFormCardExpiry: 'Expiry',
	ProviderPaymentFormCardHolderAddressTitle: 'Address',
	ProviderPaymentFormCardHolderName: 'Card holder name',
	ProviderPaymentFormCardHolderTitle: 'Card holder details',
	ProviderPaymentFormCardNumber: 'Card number',
	ProviderPaymentFormPlanTitle: 'Chosen plan',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}): ',
	ProviderPaymentFormSaveButton: 'Save subscription',
	ProviderPaymentFreePlanDescription: `Choosing the free plan  will remove each staff members access to their clients in your provider. However, your access will remain and you'll still be able to use the platform.`,
	ProviderPaymentStepName: 'Review & pay',
	ProviderPaymentSuccessSnackbar: 'Great! Your new plan was successfully saved.',
	ProviderPaymentTitle: 'Review & pay',
	ProviderPlanNetworkIdentificationNumber: 'Provider plan network identification number',
	ProviderRemindersSettingsBannerAction: 'Go to Workflow Management',
	ProviderRemindersSettingsBannerDescription:
		'Find all reminders under the new <b>Workflow Management</b> tab in <b>Settings</b>. This update brings powerful new features, improved templating, and smarter automation tools to boost your productivity. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Your reminder experience is getting better',
	ProviderTaxonomy: 'Provider taxonomy',
	ProviderUPINNumber: 'Provider UPIN number',
	ProviderUsedStoragePercentage: '{providerName} storage is {usedStoragePercentage}% full!',
	PsychiatricNursePractitioner: 'Psychiatric Nurse Practitioner',
	Psychiatrist: 'Psychiatrist',
	Psychiatrists: 'Psychiatrists',
	Psychiatry: 'Psychiatry',
	Psychoanalyst: 'Psychoanalyst',
	Psychologist: 'Psychologist',
	Psychologists: 'Psychologists',
	Psychology: 'Psychology',
	Psychometrician: 'Psychometrician',
	PsychosocialRehabilitationSpecialist: 'Psychosocial Rehabilitation Specialist',
	Psychotheraphy: 'Psychotheraphy',
	Psychotherapists: 'Psychotherapists',
	Psychotherapy: 'Psychotherapy',
	PublicCallDialogTitle: 'Video call with ',
	PublicCallDialogTitlePlaceholder: 'Video call powered by Carepatron',
	PublicFormBackToForm: 'Submit another response',
	PublicFormConfirmSubmissionHeader: 'Confirm Submission',
	PublicFormNotFoundDescription:
		'The form you are looking for may have been deleted or the link may be incorrect. Please check the URL and try again.',
	PublicFormNotFoundTitle: 'Form not found',
	PublicFormSubmissionError: 'Submission failed. Please try again.',
	PublicFormSubmissionSuccess: 'Form submitted successfully',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} submitted {noteTitle} public form',
	PublicFormSubmittedSubtitle: 'Your submission has been received.',
	PublicFormSubmittedTitle: 'Thank you!',
	PublicFormVerifyClientEmailDialogSubtitle: `We've sent a confirmation code to your email`,
	PublicFormsInvalidConfirmationCode: 'Invalid confirmation code',
	PublicHealthInspector: 'Public Health Inspector',
	PublicTemplates: 'Public templates',
	Publish: 'Publish',
	PublishTemplate: 'Publish template',
	PublishTemplateFeatureBannerSubheader: 'Templates designed to benefit the community',
	PublishTemplateHeader: 'Publish {title}',
	PublishTemplateToCommunity: 'Publish template to community',
	PublishToCommunity: 'Publish to community',
	PublishToCommunitySuccessMessage: 'Successfully published to the community',
	Published: 'Published',
	PublishedBy: 'Published by {name}',
	PublishedNotesAreNotAutosaved: `Published notes won't autosave`,
	PublishedOnCarepatronCommunity: 'Published on Carepatron community',
	Purchase: 'Purchase',
	PushToCalendar: 'Push to calendar',
	Question: 'Question',
	QuestionOrTitle: 'Question or title',
	QuickActions: 'Quick actions',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Blueberry',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Moss',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Radiation Therapist',
	Radiologist: 'Radiologist',
	Read: 'Read',
	ReadOnly: 'Read-only',
	ReadOnlyAppointment: 'Read only appointment',
	ReadOnlyEventBanner: 'This appointment is synced from a read-only calendar and can’t be edited.',
	ReaderMaxDepthHasBeenExceededCode: 'Note is too nested. Try to unindent some items.',
	ReadyForMapping: 'Ready for mapping',
	RealEstateAgent: 'Real Estate Agent',
	RearrangeClientFields: 'Rearrange client fields in client settings',
	Reason: 'Reason',
	ReasonForChange: 'Reason for change',
	RecentAppointments: 'Recent appointments',
	RecentServices: 'Recent services',
	RecentTemplates: 'Recent templates',
	RecentlyUsed: 'Recently used',
	Recommended: 'Recommended',
	RecommendedTemplates: 'Recommended templates',
	Recording: 'Recording',
	RecordingEnded: 'Recording ended',
	RecordingInProgress: 'Recording in progress',
	RecordingMicrophoneAccessErrorMessage:
		'Please allow microphone access in your browser and refresh to start recording.',
	RecurrenceCount: ', {count, plural, one {once} other {# times}}',
	RecurrenceDaily: '{count, plural, one {Daily} other {Days}}',
	RecurrenceEndAfter: 'After',
	RecurrenceEndNever: 'Never',
	RecurrenceEndOn: 'On',
	RecurrenceEvery: 'Every {description}',
	RecurrenceMonthly: '{count, plural, one {Monthly} other {Months}}',
	RecurrenceOn: 'on {description}',
	RecurrenceOnAllDays: 'on all days',
	RecurrenceUntil: 'until {description}',
	RecurrenceWeekly: '{count, plural, one {Weekly} other {Weeks}}',
	RecurrenceYearly: '{count, plural, one {Yearly} other {Years}}',
	Recurring: 'Recurring',
	RecurringAppointment: 'Recurring appointment',
	RecurringAppointmentsLimitedBannerText:
		'Not all recurring appointments are shown. Reduce the date range to see all recurring appointments for the period.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# event} other {# events}}</b> will be created at the following dates',
	Redo: 'Redo',
	ReferFriends: 'Refer friends',
	Reference: 'Reference',
	ReferralCreditedNotificationSubject: 'Your referral credit of {currency} {amount} has been applied',
	ReferralEmailDefaultBody: `Thanks to {name}, you've been sent a FREE 3 month upgrade to Carepatron. Join our community of over 3 million healthcare practitioners built for a new way of working!

Thanks,
The Carepatron Team`,
	ReferralEmailDefaultSubject: `You've been invited to join Carepatron`,
	ReferralHasNotSignedUpDescription: 'Your friend hasn’t signed up yet',
	ReferralHasSignedUpDescription: 'Your friend has signed up.',
	ReferralInformation: 'Referral information',
	ReferralJoinedNotificationSubject: '{actorProfileName} has joined Carepatron',
	ReferralListErrorDescription: 'Referrals list could not be loaded.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {month} other {months}}</b> active',
	ReferralRewardBanner: 'Sign up and claim your referral reward!',
	Referrals: 'Referrals',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} month {percentOff, select, 100 {free paid} other {{percentOff}% off}} {type, select, SubscriptionUpgrade {upgrade} other {}}',
	ReferredUserBenefitTitle: 'They get!',
	Referrer: 'Referrer',
	ReferringProvider: 'Referring provider',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} credit when <mark>3 friends</mark> activate.',
	ReferringUserBenefitTitle: 'You get!',
	RefreshPage: 'Refresh Page',
	Refund: 'Refund',
	RefundAcknowledgement: 'I have refunded {clientName} outside of Carepatron',
	RefundAcknowledgementValidationMessage: 'Please confirm you have refunded this amount',
	RefundAmount: 'Refund amount',
	RefundContent: `Refunds take 7-10 days to appear in your client's account. Payment fees won’t be refunded, but there are no extra charges for refunds. Refunds can't be canceled, and some may need review before processing.`,
	RefundCouldNotBeProcessed: 'Refund could not be processed',
	RefundError:
		'This refund cannot be automatically processed at the moment. Please contact Carepatron support to request refunding this payment.',
	RefundExceedTotalValidationError: 'Amount must not exceed total paid',
	RefundFailed: 'Refund failed',
	RefundFailedTooltip: 'Refunding this payment previously failed and cannot be retried. Please contact support.',
	RefundNonStripePaymentContent:
		'This payment was made using a method outside of Carepatron (e.g., cash, internet banking). Issuing a refund within Carepatron will not return any funds to the client.',
	RefundReasonDescription: 'Adding a refund reason can help when reviewing your clients transactions',
	Refunded: 'Refunded',
	Refunds: 'Refunds',
	RefundsTableEmptyState: 'No refunds found',
	Regenerate: 'Regenerate',
	RegisterButton: 'Register',
	RegisterEmail: 'Email',
	RegisterFirstName: 'First name',
	RegisterLastName: 'Last name',
	RegisterPassword: 'Password',
	RegisteredNurse: 'Registered Nurse',
	RehabilitationCounselor: 'Rehabilitation Counselor',
	RejectAppointmentFormTitle: `Can't make it? Please let us know why and propose a new time.`,
	Rejected: 'Rejected',
	Relationship: 'Relationship',
	RelationshipDetails: 'Relationship details',
	RelationshipEmptyStateTitle: 'Stay connected with those who support your client',
	RelationshipPageAccessTypeColumnName: 'Profile access',
	RelationshipSavedSuccessSnackbar: 'Relationship saved successfully!',
	RelationshipSelectorFamilyAdmin: 'Family',
	RelationshipSelectorFamilyMember: 'Family member',
	RelationshipSelectorProviderAdmin: 'Provider administrator',
	RelationshipSelectorProviderStaff: 'Provider staff',
	RelationshipSelectorSupportNetworkPrimary: 'Friend',
	RelationshipSelectorSupportNetworkSecondary: 'Support network',
	RelationshipStatus: 'Relationship Status',
	RelationshipType: 'Relationship type',
	RelationshipTypeClientOwner: 'Client',
	RelationshipTypeFamilyAdmin: 'Relationships',
	RelationshipTypeFamilyMember: 'Family',
	RelationshipTypeFriendOrSupport: 'Friend or support network',
	RelationshipTypeProviderAdmin: 'Provider admin',
	RelationshipTypeProviderStaff: 'Staff',
	RelationshipTypeSelectorPlaceholder: 'Search relationship types',
	Relationships: 'Relationships',
	Remaining: 'remaining',
	RemainingTime: '{time} remaining',
	Reminder: 'Reminder',
	ReminderColor: 'Reminder color',
	ReminderDetails: 'Reminder details',
	ReminderEditDisclaimer: 'Changes will only be reflected in new appointments',
	ReminderSettings: 'Appointment reminder settings',
	Reminders: 'Reminders',
	Remove: 'Remove',
	RemoveAccess: 'Remove access',
	RemoveAllGuidesBtn: 'Remove all guides',
	RemoveAllGuidesPopoverBody:
		'When you are finished with the onboarding guides simply use the remove guides button on each panel.',
	RemoveAllGuidesPopoverTitle: 'No longer need your onboarding guides?',
	RemoveAsDefault: 'Remove as default',
	RemoveAsIntake: 'Remove as intake',
	RemoveCol: 'Remove column',
	RemoveColor: 'Remove color',
	RemoveField: 'Remove field',
	RemoveFromCall: 'Remove from call',
	RemoveFromCallDescription: 'Are you sure you want to remove <mark>{attendeeName}</mark> from this video call?',
	RemoveFromCollection: 'Remove from collection',
	RemoveFromCommunity: 'Remove from community',
	RemoveFromFolder: 'Remove from folder',
	RemoveFromFolderConfirmationDescription:
		'Are you sure you want to remove this template from this folder? This action cannot be undone, but you may opt to move it back later.',
	RemoveFromIntakeDefault: 'Remove from intake default',
	RemoveGuides: 'Remove guides',
	RemoveMfaConfirmationDescription:
		'Removing Multi-Factor Authentication (MFA) will reduce the security of your account. Do you want to proceed?',
	RemoveMfaConfirmationTitle: 'Remove MFA?',
	RemovePaymentMethodDescription: `This will remove all access and future use of this payment method.
This action can’t be undone.`,
	RemoveRow: 'Remove row',
	RemoveTable: 'Remove table',
	RemoveTemplateAsDefaultIntakeSuccess: 'Successfully removed {templateTitle} as default intake template',
	RemoveTemplateFromCommunity: 'Remove template from community',
	RemoveTemplateFromFolder: '{templateTitle} successfully removed from {folderTitle}',
	Rename: 'Rename',
	RenderingProvider: 'Rendering provider',
	Reopen: 'Reopen',
	ReorderServiceGroupFailure: 'Failed to reorder collection',
	ReorderServiceGroupSuccess: 'Successfully reordered collection',
	ReorderServicesFailure: 'Failed to reorder services',
	ReorderServicesSuccess: 'Successfully reordered services',
	ReorderYourServiceList: 'Reorder your service list',
	ReorderYourServiceListDescription:
		'The way you organize your services and collections will be reflected on your online booking page for all your clients to see!',
	RepeatEvery: 'Repeat every',
	RepeatOn: 'Repeat on',
	Repeating: 'Repeating',
	Repeats: 'Repeats',
	RepeatsEvery: 'Repeats every',
	Rephrase: 'Rephrase',
	Replace: 'Replace',
	ReplaceBackground: 'Replace background',
	ReplacementOfPriorClaim: 'Replacement of prior claim',
	Report: 'Report',
	Reprocess: 'Reprocess',
	RepublishTemplateToCommunity: 'Republish template to community',
	RequestANewVerificationLink: 'Request a new verification link',
	RequestCoverageReport: 'Request coverage report',
	RequestingDevicePermissions: 'Requesting for device permissions...',
	RequirePaymentMethodDesc: 'Clients must enter their credit card details to book online',
	RequirePaymentMethodLabel: 'Require credit card details',
	Required: 'required',
	RequiredField: 'Required',
	RequiredUrl: 'URL is required.',
	Reschedule: 'Reschedule',
	RescheduleBookingLinkModalDescription: 'Your client can change their appointment date and time using this link.',
	RescheduleBookingLinkModalTitle: 'Reschedule booking link',
	RescheduleLink: 'Reschedule link',
	Resend: 'Resend',
	ResendConfirmationCode: 'Resend confirmation code',
	ResendConfirmationCodeDescription: `Please enter your email address and we'll email you another confirmation code`,
	ResendConfirmationCodeSuccess: 'Confirmation code has been resent, please check your inbox',
	ResendNewEmailVerificationSuccess: 'New verification link has been sent to {email}',
	ResendVerificationEmail: 'Resend verification email',
	Reset: 'Reset',
	Resources: 'Resources',
	RespiratoryTherapist: 'Respiratory Therapist',
	RespondToHistoricAppointmentError:
		'This is a historic appointment, please contact your practitioner if you have a question.',
	Responder: 'Responder',
	RestorableItemModalDescription:
		'Are you sure you want to delete {context}?{canRestore, select, true { You can restore it later.} other {}}',
	RestorableItemModalTitle: 'Delete {type}',
	Restore: 'Restore',
	RestoreAll: 'Restore all',
	Restricted: 'Restricted',
	ResubmissionCodeReferenceNumber: 'Resubmission code and reference number',
	Resubmit: 'Resubmit',
	Resume: 'Resume',
	Retry: 'Retry',
	RetryingConnectionAttempt: 'Retrying connection... (Attempt {retryCount} of {maxRetries})',
	ReturnToForm: 'Return to form',
	RevertClaimStatus: 'Revert claim status',
	RevertClaimStatusDescriptionBody:
		'This claim has linked payments, and changing the status may affect payment tracking or processing, which could require manual reconciliation.',
	RevertClaimStatusDescriptionTitle: 'Are you sure you want to revert to {status}?',
	RevertClaimStatusError: 'Failed to revert claim status',
	RevertToDraft: 'Revert to draft',
	Review: 'Review',
	ReviewsFirstQuote: 'Appointments anywhere, at any time',
	ReviewsSecondJobTitle: 'Lifehouse Clinic',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote: 'I also love the carepatron app. Helps me keep track of my clients and work while on the go.',
	ReviewsThirdJobTitle: 'Manila Bay Clinic',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'The ease of navigation and the beautiful user interface brings a smile to my face everyday.',
	RightAlign: 'Right align',
	Role: 'Role',
	Roster: 'Attendees',
	RunInBackground: 'Run in background',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS & Email Reminder',
	SSN: 'SSN',
	SafetyRedirectHeading: `You're leaving Carepatron`,
	SafetyRedirectSubtext: 'If you trust this link, select it to continue',
	SalesRepresentative: 'Sales Representative',
	SalesTax: 'Sales Tax',
	SalesTaxHelp: 'Includes sales tax on generated invoices',
	SalesTaxIncluded: 'Yes',
	SalesTaxNotIncluded: 'No',
	SaoPaulo: 'São Paulo',
	Saturday: 'Saturday',
	Save: 'Save',
	SaveAndClose: 'Save & close',
	SaveAndExit: 'Save & exit',
	SaveAndLock: 'Save and lock',
	SaveAsDraft: 'Save as draft',
	SaveCardForFuturePayments: 'Save card for future payments',
	SaveChanges: 'Save changes',
	SaveCollection: 'Save Collection',
	SaveField: 'Save field',
	SavePaymentMethod: 'Save payment method',
	SavePaymentMethodDescription: `You won't be charged until your first appointment.`,
	SavePaymentMethodSetupError: 'An unexpected error occurred and we were unable to configure payments at this time.',
	SavePaymentMethodSetupInvoiceLater: 'Payments can be set up and saved when paying your first invoice.',
	SaveSection: 'Save section',
	SaveService: 'Create new service',
	SaveTemplate: 'Save template',
	Saved: 'Saved',
	SavedCards: 'Saved cards',
	SavedPaymentMethods: 'Saved',
	Saving: 'Saving...',
	ScheduleAppointmentsAndOnlineServices: 'Schedule appointments and online services',
	ScheduleName: 'Schedule name',
	ScheduleNew: 'Schedule new',
	ScheduleSend: 'Schedule send',
	ScheduleSendAlertInfo: 'Conversations in scheduled will be sent at their scheduled time.',
	ScheduleSendByName: '<strong>Schedule send</strong> • {time} by {displayName}',
	ScheduleSetupCall: 'Schedule setup call',
	Scheduled: 'Scheduled',
	SchedulingSend: 'Scheduling send',
	School: 'School',
	ScrollToTop: 'Scroll to top',
	Search: 'Search',
	SearchAndConvertToLanguage: 'Search and convert to language',
	SearchBasicBlocks: 'Search basic blocks',
	SearchByName: 'Search by name',
	SearchClaims: 'Search claims',
	SearchClientFields: 'Search client fields',
	SearchClients: 'Search by client name, client ID or phone number',
	SearchCommandNotFound: 'No results for "{searchTerm}" have been found',
	SearchContacts: 'Client or contact',
	SearchContactsPlaceholder: 'Search contacts',
	SearchConversations: 'Search conversations',
	SearchInputPlaceholder: 'Search all resources',
	SearchInvoiceNumber: 'Search invoice number',
	SearchInvoices: 'Search invoices',
	SearchMultipleContacts: 'Clients or contacts',
	SearchMultipleContactsOptional: 'Clients or contacts (optional)',
	SearchOrCreateATag: 'Search or create a tag',
	SearchPayments: 'Search payments',
	SearchPrepopulatedData: 'Search pre populated data fields',
	SearchRelationships: 'Search relationships',
	SearchRemindersAndWorkflows: 'Search reminders and workflows',
	SearchServices: 'Search services',
	SearchTags: 'Search tags',
	SearchTeamMembers: 'Search team members',
	SearchTemplatePlaceholder: 'Search {templateCount}+ resources',
	SearchTimezone: 'Search timezone...',
	SearchTrashItems: 'Search items',
	SearchUnsplashPlaceholder: 'Search free high-resolution photos from Unsplash',
	Secondary: 'Secondary',
	SecondaryInsurance: 'Secondary insurance',
	SecondaryPolicy: 'Secondary insurance',
	SecondaryTimezone: 'Secondary timezone',
	Secondly: 'Secondly',
	Section: 'Section',
	SectionCannotBeEmpty: 'A section must have at least one row',
	SectionFormSecondaryText: 'Section title and description',
	SectionName: 'Section name',
	Sections: 'Sections',
	SeeLess: 'See less',
	SeeLessUpcomingAppointments: 'See less upcoming appointments',
	SeeMore: 'See more',
	SeeMoreUpcomingAppointments: 'See more upcoming appointments',
	SeeTemplateLibrary: 'See template library',
	Seen: 'Seen',
	SeenByName: '<strong>Seen</strong> • {time} by {displayName}',
	SelectAll: 'Select all',
	SelectAssignees: 'Select assignees',
	SelectAttendees: 'Select attendees',
	SelectCollection: 'Select Collection',
	SelectCorrespondingAttributes: 'Select corresponding attributes',
	SelectPayers: 'Select payers',
	SelectProfile: 'Select profile',
	SelectServices: 'Select services',
	SelectTags: 'Select Tags',
	SelectTeamOrCommunity: 'Select Team or Community',
	SelectTemplate: 'Select Template',
	SelectType: 'Select type',
	Selected: 'Selected',
	SelfPay: 'Self-pay',
	Send: 'Send',
	SendAndClose: 'Send & close',
	SendAndStopIgnore: 'Send and stop ignorning',
	SendEmail: 'Send email',
	SendIntake: 'Send intake',
	SendIntakeAndForms: 'Send Intake & Forms',
	SendMeACopy: 'Send me a copy',
	SendNotificationEmailWarning: `Some attendees doesn't have an email address and will not receive automated notifications and reminders.`,
	SendNotificationLabel: 'Choose attendees to notify with an email',
	SendOnlinePayment: 'Send online payment',
	SendOnlinePaymentTooltipTitleAdmin: 'Please add your preferred payout settings',
	SendOnlinePaymentTooltipTitleStaff: 'Please ask the owner of the provider to set up online payments.',
	SendPaymentLink: 'Send payment link',
	SendReaction: 'Send a reaction',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Send verification email',
	SendingFailed: 'Sending failed',
	Sent: 'Sent',
	SentByName: '<strong>Sent</strong> • {time} by {displayName}',
	Seoul: 'Seoul',
	SeparateDuplicateClientsDescription:
		'The chosen client records will remain separate from the rest unless you choose to merge them',
	Service: 'Service',
	'Service/s': 'Service/s',
	ServiceAdjustment: 'Service adjustment',
	ServiceAllowNewClientsIndicator: 'Allow new clients',
	ServiceAlreadyExistsInCollection: 'Service already exists in collection',
	ServiceBookableOnlineIndicator: 'Bookable online',
	ServiceCode: 'Code',
	ServiceCodeErrorMessage: 'A service code is required',
	ServiceCodeSelectorPlaceholder: 'Add a service code',
	ServiceColour: 'Service color',
	ServiceCoverageDescription:
		'Customise the service co-pay amounts to override the default policy co-pay. Disable coverage to prevent services being claimed against the policy.',
	ServiceCoverageGoToServices: 'Go to services',
	ServiceCoverageNoServicesDescription:
		'Customise the service co-pay amounts to override the default policy co-pay. Disable coverage to prevent services being claimed against the policy.',
	ServiceCoverageNoServicesLabel: 'No services have been found. ',
	ServiceCoverageTitle: 'Service coverage',
	ServiceDate: 'Date of service',
	ServiceDetails: 'Service details',
	ServiceDuration: 'Duration',
	ServiceEmptyState: 'There are no services yet',
	ServiceErrorMessage: 'Service is required',
	ServiceFacility: 'Service facility',
	ServiceName: 'Service name',
	ServiceRate: 'Rate',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} for {serviceReceiptNumber, select, undefined {user} other {{clientName}}} requires additional information',
	ServiceSalesTax: 'Sales tax',
	ServiceType: 'Service',
	ServiceWorkerForceUIUpdateDialogDescription: 'Hit reload to refresh and get the newest Carepatron updates.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Reload',
	ServiceWorkerForceUIUpdateDialogSubTitle: `You're using an older version`,
	ServiceWorkerForceUIUpdateDialogTitle: 'Welcome back!',
	Services: 'Services',
	ServicesAndAvailability: 'Services & availability',
	ServicesAndDiagnosisCodesHeader: 'Add services and diagnosis codes',
	ServicesCount: '{count,plural,=0{Services}one{Service}other{Services}}',
	ServicesPlaceholder: 'Services',
	ServicesProvidedBy: 'Service/s provided by',
	SetAPhysicalAddress: 'Set a physical address',
	SetAVirtualLocation: 'Set a virtual location',
	SetAsDefault: 'Set as default',
	SetAsIntake: 'Set as intake',
	SetAsIntakeDefault: 'Set as intake default',
	SetAvailability: 'Set availability',
	SetTemplateAsDefaultIntakeSuccess: 'Successfully set {templateTitle} as default intake template',
	SetUpMfaButton: 'Set up MFA',
	SetYourLocation: 'Set your <mark>location</mark>',
	SetYourLocationDescription: 'I don’t have a business address <span>(online and mobile services only)</span>',
	SettingUpPayers: 'Setting up payers',
	Settings: 'Settings',
	SettingsNewUserPasswordDescription: `Once you have signed up, we'll send you a confirmation code which you can use to confirm your account`,
	SettingsNewUserPasswordTitle: 'Sign up to Carepatron',
	SettingsTabAutomation: 'Automation',
	SettingsTabBillingDetails: 'Billing details',
	SettingsTabConnectedApps: 'Connected apps',
	SettingsTabCustomFields: 'Custom fields',
	SettingsTabDetails: 'Details',
	SettingsTabInvoices: 'Invoices',
	SettingsTabLocations: 'Locations',
	SettingsTabNotifications: 'Notifications',
	SettingsTabOnlineBooking: 'Online Booking',
	SettingsTabPayers: 'Payers',
	SettingsTabReminders: 'Reminders',
	SettingsTabServices: 'Services',
	SettingsTabServicesAndAvailability: 'Services and availability',
	SettingsTabSubscriptions: 'Subscriptions',
	SettingsTabWorkflowAutomations: 'Automations',
	SettingsTabWorkflowReminders: 'Basic reminders',
	SettingsTabWorkflowTemplates: 'Templates',
	Setup: 'Set up',
	SetupGuide: 'Set up guide',
	SetupGuideAddServicesActionLabel: 'Start',
	SetupGuideAddServicesSubtitle: '4 steps • 2 min',
	SetupGuideAddServicesTitle: 'Add your services',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Start',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 steps • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Enable online payments',
	SetupGuideImportClientsActionLabel: 'Start',
	SetupGuideImportClientsSubtitle: '4 steps • 3 min',
	SetupGuideImportClientsTitle: 'Import your clients',
	SetupGuideImportTemplatesActionLabel: 'Start',
	SetupGuideImportTemplatesSubtitle: '2 steps • 1 min',
	SetupGuideImportTemplatesTitle: 'Import your templates',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Start',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 steps • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personalize your workspace',
	SetupGuideSetLocationActionLabel: 'Start',
	SetupGuideSetLocationSubtitle: '4 steps • 1 min',
	SetupGuideSetLocationTitle: 'Set your location',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Invite team',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Invite your team to communicate and manage tasks effortlessly.',
	SetupGuideSuggestedAddTeamMembersTag: 'Set up',
	SetupGuideSuggestedAddTeamMembersTitle: 'Add team members',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Customize',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Feel professional with your unique logo and brand colors.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Customize brand',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Download',
	SetupGuideSuggestedDownloadMobileAppSubtitle: 'Access your workspace anywhere, anytime on any device.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Set up',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Download the app',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Set availability',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Prevent double bookings by setting your availability.',
	SetupGuideSuggestedEditAvailabilityTag: 'Scheduling',
	SetupGuideSuggestedEditAvailabilityTitle: 'Edit availability',
	SetupGuideSuggestedImportClientsActionLabel: 'Import',
	SetupGuideSuggestedImportClientsSubtitle: 'Instantly upload your existing client data in just one click.',
	SetupGuideSuggestedImportClientsTag: 'Set up',
	SetupGuideSuggestedImportClientsTitle: 'Import clients',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Edit reminders',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'Reduce no-shows with automatic appointment reminders.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Personalize reminders',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Start call',
	SetupGuideSuggestedStartVideoCallSubtitle: 'Host a call and connect with clients using our AI-Powered video tools.',
	SetupGuideSuggestedStartVideoCallTag: 'Telehealth',
	SetupGuideSuggestedStartVideoCallTitle: 'Start video call',
	SetupGuideSuggestedTryActionsTitle: 'Things to try 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Try AI assist',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Get instant answers to all your work questions.',
	SetupGuideSuggestedUseAIAssistantTag: 'New',
	SetupGuideSuggestedUseAIAssistantTitle: 'Use AI assistant',
	SetupGuideSyncCalendarActionLabel: 'Start',
	SetupGuideSyncCalendarSubtitle: '1 step • less than 1 min',
	SetupGuideSyncCalendarTitle: 'Sync your calendar',
	SetupGuideVerifyEmailLabel: 'Verify',
	SetupGuideVerifyEmailSubtitle: '2 steps • 2 min',
	SetupOnlineStripePayments: 'Use Stripe for online payments',
	SetupPayments: 'Set up payments',
	Sex: 'Sex',
	SexSelectorPlaceholder: 'Male / Female / Prefer not to say',
	Share: 'Share',
	ShareBookingLink: 'Share booking link',
	ShareNoteDefaultMessage: `Hi
{name} has shared "{documentName}" with you.

Thanks,
{practiceName}`,
	ShareNoteMessage: `Hi

{name} has shared "{documentName}" {isResponder, select, true {with some questions for you to fill out.} other {with you.}}

Thanks,
{practiceName}`,
	ShareNoteTitle: 'Share ‘{noteTitle}’',
	ShareNotesWithClients: 'Share with clients or contacts',
	ShareScreen: 'Share screen',
	ShareScreenNotSupported: 'Your device/browser does not support the share screen feature',
	ShareScreenWithId: 'Screen {screenId}',
	ShareTemplateAsPublicFormModalDescription: 'Allow others to view this template and submit it as a form.',
	ShareTemplateAsPublicFormModalTitle: 'Share link for ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Public form configuration successfully updated',
	ShareTemplateAsPublicFormSectionCustomization: 'Customization',
	ShareTemplateAsPublicFormShowPoweredBy: 'Show "Powered by Carepatron" on my form',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Show/hide “Powered by Carepatron” on my form',
	ShareTemplateAsPublicFormTrigger: 'Share',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Use workspace branding',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Show/hide workspace branding',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: 'Sends code for existing and non-existing clients',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Signatures always require the email to be verified',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Only sends code for existing clients',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Never sends code',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Selecting 'Never' may allow unverified users to overwrite client data if they use an existing client's email address.`,
	ShareWithCommunity: 'Share With Community',
	ShareYourReferralLink: 'Share your referral link',
	ShareYourScreen: 'Share your screen',
	SheHer: 'She/Her',
	ShortTextAnswer: 'Short text answer',
	ShortTextFormPrimaryText: 'Short text',
	ShortTextFormSecondaryText: 'Less than 300 character answer',
	Show: 'Show',
	ShowColumn: 'Show column',
	ShowColumnButton: 'Show column {value} button',
	ShowColumns: 'Show columns',
	ShowColumnsMenu: 'Show columns menu',
	ShowDateDurationDescription: 'eg. 29 years old',
	ShowDateDurationLabel: 'Show date duration',
	ShowDetails: 'Show details',
	ShowField: 'Show field',
	ShowFullAddress: 'Show address',
	ShowHideFields: 'Show / Hide fields',
	ShowIcons: 'Show icons',
	ShowLess: 'Show less',
	ShowMeetingTimers: 'Show meeting timers',
	ShowMenu: 'Show menu',
	ShowMergeSummarySidebar: 'Show merge summary',
	ShowMore: 'Show more',
	ShowOnTranscript: 'Show on transcript',
	ShowReactions: 'Show reactions',
	ShowSection: 'Show section',
	ShowServiceCode: 'Show service code',
	ShowServiceDescription: 'Show description on service bookings',
	ShowServiceDescriptionDesc: 'Clients can view services descriptions when booking',
	ShowServiceGroups: 'Show collections',
	ShowServiceGroupsDesc: 'Clients will be shown services grouped by collection when booking',
	ShowSpeakers: 'Show speakers',
	ShowTax: 'Show tax',
	ShowTimestamp: 'Show timestamp',
	ShowUnits: 'Show units',
	ShowWeekends: 'Show weekends',
	ShowYourView: 'Show your view',
	SignInWithApple: 'Sign in with Apple',
	SignInWithGoogle: 'Sign in with Google',
	SignInWithMicrosoft: 'Sign in with Microsoft',
	SignUpTitleReferralDefault: '<mark>Sign up</mark> and claim your referral reward',
	SignUpTitleReferralUpgrade:
		'Start your {durationInMonths} month <mark>{percentOff, select, 100 {free} other {{percentOff}% off}} upgrade</mark>',
	SignatureCaptureError: 'Unable to capture signature. Please try again.',
	SignatureFormPrimaryText: 'Signature',
	SignatureFormSecondaryText: 'Get a digital signature',
	SignatureInfoTooltip: 'This visual representation is not a valid electronic signature.',
	SignaturePlaceholder: 'Draw your signature here',
	SignedBy: 'Signed by',
	Signup: 'Sign up',
	SignupAgreements: 'I agree to the {termsOfUse} and the {privacyStatement} for my account.',
	SignupBAA: 'Business Associate Agreement',
	SignupBusinessAgreements:
		'On behalf of myself and the business, I agree to the {businessAssociateAgreement}, the {termsOfUse}, and the {privacyStatement} for my account.',
	SignupInvitationForYou: 'You have been invited to use Carepatron.',
	SignupPageProviderWarning:
		'If your administrator has already created an account, you need to ask them to invite you into that provider. Do not use this sign up form. For more information see',
	SignupPageProviderWarningLink: 'this link.',
	SignupPrivacy: 'Privacy Policy',
	SignupProfession: 'What profession best describes you?',
	SignupSubtitle: `Carepatron's practice management software is made for solo practitioners and teams. Stop paying excessive fees and be part of the revolution.`,
	SignupSuccessDescription: `Confirm your email address to start your onboarding. If you don't receive it right away, please check your spam folder.`,
	SignupSuccessTitle: 'Please check your email',
	SignupTermsOfUse: 'Terms of Use',
	SignupTitleClient: '<mark>Manage your health</mark> from one place',
	SignupTitleLast: 'and all the work you do! — It’s free',
	SignupTitleOne: '<mark>Powering you</mark>, ',
	SignupTitleThree: '<mark>Powering your clients</mark>, ',
	SignupTitleTwo: '<mark>Powering your team</mark>, ',
	Simple: 'Simple',
	SimplifyBillToDetails: 'Simplify bill to details',
	SimplifyBillToHelperText: 'Only the first line is used when it matches the client',
	Singapore: 'Singapore',
	Single: 'Single',
	SingleChoiceFormPrimaryText: 'Single choice',
	SingleChoiceFormSecondaryText: 'Choose only one option',
	Sister: 'Sister',
	SisterInLaw: 'Sister-in-law',
	Skip: 'Skip',
	SkipLogin: 'Skip login',
	SlightBlur: 'Slightly blur your background',
	Small: 'Small',
	SmartChips: 'Smartchips',
	SmartDataChips: 'Smart data chips',
	SmartReply: 'Smart reply',
	SmartSuggestNewClient: '<strong>Smart Suggest</strong> create {name} as a new client',
	SmartSuggestedFieldDescription: 'This field is a smart suggestion',
	SocialSecurityNumber: 'Social security number',
	SocialWork: 'Social work',
	SocialWorker: 'Social Worker',
	SoftwareDeveloper: 'Software Developer',
	Solo: 'Solo',
	Someone: 'Someone',
	Son: 'Son',
	SortBy: 'Sort by',
	SouthAmerica: 'South America',
	Speaker: 'Speaker',
	SpeakerSource: 'Speaker source',
	Speakers: 'Speakers',
	SpecifyPaymentMethod: 'Specify payment method',
	SpeechLanguagePathology: 'Speech-Language pathology',
	SpeechTherapist: 'Speech Therapist',
	SpeechTherapists: 'Speech Therapists',
	SpeechTherapy: 'Speech Therapy',
	SportsMedicinePhysician: 'Sports Medicine Physician',
	Spouse: 'Spouse',
	SpreadsheetColumnExample: 'e.g. ',
	SpreadsheetColumns: 'Spreadsheet columns',
	SpreadsheetUploaded: 'Spreadsheet Uploaded',
	SpreadsheetUploading: 'Uploading...',
	Staff: 'Staff',
	StaffAccessDescriptionAdmin: 'Admins can manage everything on the platform.',
	StaffAccessDescriptionStaff: `Staff members can manage clients, notes and documentation they've created or has been shared
						with them, schedule appointments, manage invoices.`,
	StaffContactAssignedSubject:
		'{actorProfileName} has assigned {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} and {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { and 1 other client} other { and # other clients}} to you',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} has shared the {inboxName} inbox with you',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} has removed your access to the {inboxName} inbox',
	StaffMembers: 'Staff Members',
	StaffMembersNumber: '{billedUsers, plural, one {# team member} other {# team members}}',
	StaffSavedSuccessSnackbar: 'Team members info successfully saved!',
	StaffSelectorAdminRole: 'Administrator',
	StaffSelectorStaffRole: 'Staff member',
	StandardAppointment: 'Standard Appointment',
	StandardColor: 'Task color',
	StartAndEndTime: 'Start and end time',
	StartCall: 'Start call',
	StartDate: 'Start date',
	StartDictating: 'Start dictating',
	StartImport: 'Start import',
	StartRecordErrorTitle: 'There was an error starting your recording',
	StartRecording: 'Start recording',
	StartTimeIncrements: 'Start time increments',
	StartTimeIncrementsView: '{startTimeIncrements} min intervals',
	StartTranscribing: 'Start transcribing',
	StartTranscribingNotes:
		'Choose the clients you want to create a note for, then click ‘Start transcribing’ to begin recording.',
	StartTranscription: 'Start transcription',
	StartVideoCall: 'Start video call',
	StartWeekOn: 'Start week on',
	StartedBy: 'Started by ',
	Starter: 'Starter',
	State: 'State',
	StateIndustrialAccidentProviderNumber: 'State industrial accident provider number',
	StateLicenseNumber: 'State license number',
	Statement: 'Statement',
	StatementDescriptor: 'Statement descriptor',
	StatementDescriptorToolTip:
		'Statement descriptor is shown on your clients bank or credit card statements. It must be between 5 and 22 characters and reflect your business name.',
	StatementNumber: 'Statement #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Enter a status label',
	StepFather: 'Step-father',
	StepMother: 'Step-mother',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription: `By ceasing to ignore these senders future conversations will be sent to 'Inbox'. Are you sure you want to stop ignoring these senders?`,
	StopIgnoring: 'Stop ignoring',
	StopIgnoringSenders: 'Stop ignoring senders',
	StopIgnoringSendersSuccess: 'Stopped ignoring email address <mark>{addresses}</mark>',
	StopSharing: 'Stop sharing',
	StopSharingLabel: 'carepatron.com is sharing your screen.',
	Storage: 'Storage',
	StorageAlmostFullDescription: '🚀 Upgrade now to keep your account running smoothly.',
	StorageAlmostFullTitle: `You've used {percentage}% of your workspace storage limit!`,
	StorageFullDescription: 'Get more storage by upgrading your plan.',
	StorageFullTitle: 'Your storage is full.',
	Street: 'Street',
	StripeAccountNotCompleteErrorCode:
		'Online payments are not {hasProviderName, select, true {set up for {providerName}} other {enabled for this provider}}.',
	StripeAccountRejectedError: 'Stripe account has been rejected. Please contact support.',
	StripeBalance: 'Stripe Balance',
	StripeChargesInfoToolTip: 'Allows you to charge debit & credit cards',
	StripeFeesDescription:
		'Carepatron uses Stripe to get you paid quickly and keep your payment information secure. Available payment methods vary by region, all major debit & credit cards are accepted.',
	StripeFeesDescriptionItem1: 'Processing fees are applied to each successful transaction, you can {link}.',
	StripeFeesDescriptionItem2: 'Payouts occur daily but are held for up to 4 days.',
	StripeFeesLinkToRatesText: 'view our rates here',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar: `Sorry, there's a minimum of {minimumAmount} required for invoices that use online payments`,
	StripePaymentsDisabled: 'Online payments disabled. Please check your payment settings.',
	StripePaymentsUnavailable: 'Payments unavailable',
	StripePaymentsUnavailableDescription: 'An error occurred while loading payments. Please try again later.',
	StripePayoutsInfoToolTip: 'Allows you to get paid out into your bank account',
	StyleYourWorkspace: '<mark>Style</mark> your workspace',
	StyleYourWorkspaceDescription1:
		'We fetched the brand assets from your website. Feel free to edit them or continue to your Carepatron workspace',
	StyleYourWorkspaceDescription2:
		'Use your brand assets to customize invoices and online bookings for a seamless customer experience',
	SubAdvanced: 'Advanced',
	SubEssential: 'Essential',
	SubOrganization: 'Organization',
	SubPlus: 'Plus',
	SubProfessional: 'Professional',
	Subject: 'Subject',
	Submit: 'Submit',
	SubmitElectronically: 'Submit electronically',
	SubmitFeedback: 'Submit feedback',
	SubmitFormValidationError: 'Please ensure all required fields are filled in correctly and try submitting again.',
	Submitted: 'Submitted',
	SubmittedDate: 'Submitted date',
	SubscribePerMonth: 'Subscribe {price} {isMonthly, select, true {per month} other {per year}}',
	SubscriptionDiscountDescription:
		'{percentOff}% off {months, select, null { } other { {months, plural, one {for # month} other {for # months}}}}',
	SubscriptionFreeTrialDescription: 'Free until {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'We were unable to complete payment for your subscription. Please check your payment details',
	SubscriptionPlanDetailsHeader: 'Per user/monthly billed annually',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} billed monthly (USD)',
	SubscriptionPlans: 'Subscription plans',
	SubscriptionPlansDescription:
		'Upgrade your plan to unlock additional benefits and keep your practice running smoothly.',
	SubscriptionPlansDescriptionNoPermission: `Looks like you don't have access to upgrade right now — please contact your administrator for help.`,
	SubscriptionSettings: 'Subscription settings',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> of storage used',
	SubscriptionSettingsStorageUsed: '{used} of {limit} used',
	SubscriptionSettingsUnlimitedStorage: 'Unlimited storage available',
	SubscriptionSummary: 'Subscription summary',
	SubscriptionUnavailableOverStorageLimit: 'Your current usage exceeds this plans storage limit.',
	SubscriptionUnpaidBannerButton: 'Go to subscriptions',
	SubscriptionUnpaidBannerDescription: 'Please check that your payment details are correct and try again',
	SubscriptionUnpaidBannerTitle: 'We were unable to complete payment for your subscription.',
	Subscriptions: 'Subscriptions',
	SubscriptionsAndPayments: 'Subscriptions & payments',
	Subtotal: 'Subtotal',
	SuburbOrProvince: 'Suburb/Province',
	SuburbOrState: 'Suburb/State',
	SuccessSavedNoteChanges: 'Successfully saved note changes',
	SuccessShareDocument: 'Successfully shared document',
	SuccessShareNote: 'Successfully shared note',
	SuccessfullyCreatedValue: 'Successfully created {value}',
	SuccessfullyDeletedTranscriptionPart: 'Successfully deleted transcription part',
	SuccessfullyDeletedValue: 'Successfully deleted {value}',
	SuccessfullySubmitted: 'Successfully submitted ',
	SuccessfullyUpdatedClientSettings: 'Successfully updated Client Settings',
	SuccessfullyUpdatedTranscriptionPart: 'Successfully updated transcription part',
	SuccessfullyUpdatedValue: 'Successfully updated {value}',
	SuggestedAIPoweredTemplates: 'Suggested AI-Powered templates',
	SuggestedAITemplates: 'Suggested AI templates',
	SuggestedActions: 'Suggested actions',
	SuggestedLocations: 'Suggested locations',
	Suggestions: 'Suggestions',
	Summarise: 'AI summarize',
	SummarisingContent: 'Summarizing {title}',
	Sunday: 'Sunday',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superbill & Insurance Billing',
	SuperbillAutomationMonthly: 'Active • Last day of the month',
	SuperbillAutomationNoEmail:
		'To send automated billing documents successfully, add an email address for this client',
	SuperbillAutomationNotActive: 'Not active',
	SuperbillAutomationUpdateFailure: 'Failed to update Superbill automation settings',
	SuperbillAutomationUpdateSuccess: 'Successfully updated Superbill automation settings',
	SuperbillClientHelperText: 'This information is prepopulated from the client details',
	SuperbillNotFoundDescription:
		'Please contact your provider and ask them for more information or to resend the superbill.',
	SuperbillNotFoundTitle: 'Superbill not found',
	SuperbillNumber: 'Superbill #{number}',
	SuperbillNumberAlreadyExists: 'Superbill receipt number already exists',
	SuperbillPracticeHelperText: 'This information is prepopulated from the practice billing settings',
	SuperbillProviderHelperText: 'This information is prepopulated from the staff details',
	SuperbillReceipts: 'Superbill receipts',
	SuperbillsEmptyStateDescription: 'No superbills have been found.',
	Surgeon: 'Surgeon',
	Surgeons: 'Surgeons',
	SurgicalTechnologist: 'Surgical Technologist',
	SwitchFromAnotherPlatform: 'I am switching from another platform',
	SwitchToMyPortal: 'Switch to My portal',
	SwitchToMyPortalTooltip: `Access your own personal portal,
 enabling you to explore your
 client's portal experience.`,
	SwitchWorkspace: 'Switch workspace',
	SwitchingToADifferentPlatform: 'Switching to a different platform',
	Sydney: 'Sydney',
	SyncCalendar: 'Sync calendar',
	SyncCalendarModalDescription: `Other team members won't be able to see your synced calendars. Client appointments can only be updated or deleted from within Carepatron.`,
	SyncCalendarModalDisplayCalendar: 'Display my calendar in Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Sync my calendar to Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Sync Carepatron appointments with my calendar',
	SyncCarepatronAppointmentsWithMyCalendar: 'Sync Carepatron appointments with my calendar',
	SyncGoogleCalendar: 'Sync Google calendar',
	SyncInbox: 'Sync inbox with Carepatron',
	SyncMyCalendarToCarepatron: 'Sync my calendar to Carepatron',
	SyncOutlookCalendar: 'Sync Outlook calendar',
	SyncedFromExternalCalendar: 'Synced from external calendar',
	SyncingCalendarName: 'Syncing {calendarName} calendar',
	SyncingFailed: 'Syncing failed',
	SystemGenerated: 'System generated',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Table',
	TableRowLabel: 'Table row for {value}',
	TagSelectorNoOptionsText: 'Click "create new" to add new tag',
	Tags: 'Tags',
	TagsInputPlaceholder: 'Search or create tags',
	Task: 'Task',
	TaskAttendeeStatusUpdatedSuccess: 'Successfully updated appointment statuses',
	Tasks: 'Tasks',
	Tax: 'Tax',
	TaxAmount: 'Tax Amount',
	TaxID: 'Tax ID',
	TaxIdType: 'Tax ID type',
	TaxName: 'Tax name',
	TaxNumber: 'Tax number',
	TaxNumberType: 'Tax number type',
	TaxNumberTypeInvalid: '{type} is invalid',
	TaxPercentageOfAmount: '{taxName} ({percentage}% of {amount})',
	TaxRate: 'Tax rate',
	TaxRatesDescription: 'Manage the tax rates that will be applied to your invoice line items.',
	Taxable: 'Taxable',
	TaxonomyCode: 'Taxonomy code',
	TeacherAssistant: 'Teacher Assistant',
	Team: 'Team',
	TeamMember: 'Team member',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {is} other {are}} already booked at this time.{br}Choose a new time to avoid double booking.',
	TeamMembers: 'Team members',
	TeamMembersColour: 'Team members color',
	TeamMembersDetails: 'Team members details',
	TeamSize: 'How many people are in your team?',
	TeamTemplates: 'Team templates',
	TeamTemplatesSectionDescription: 'Created by you and your team',
	TelehealthAndVideoCalls: 'Telehealth & Video Calls',
	TelehealthProvidedOtherThanInPatientCare: 'Telehealth provided for other than in-patient care',
	TelehealthVideoCall: 'Telehealth video call',
	Template: 'Template',
	TemplateDescription: 'Template description',
	TemplateDetails: 'Template details',
	TemplateEditModeViewSwitcherDescription: 'Create and edit template',
	TemplateGallery: 'Community Templates',
	TemplateImportCompletedNotificationSubject: 'Template import completed! {templateTitle} is ready for use.',
	TemplateImportFailedNotificationSubject: 'Failed to import file {fileName}.',
	TemplateName: 'Template name',
	TemplateNotFound: 'Template could not be found.',
	TemplatePreviewErrorMessage: 'An error occured while loading the template preview',
	TemplateResponderModeViewSwitcherDescription: 'Preview and interact with forms',
	TemplateResponderModeViewSwitcherTooltipTitle: 'Check how your forms appear when filled out by responders',
	TemplateSaved: 'Saved changes',
	TemplateTitle: 'Template Title',
	TemplateType: 'Template Type',
	Templates: 'Templates',
	TemplatesCategoriesFilter: 'Filter by category',
	TemplatesPublicTemplatesFilter: ' Filter by Community/Team',
	Text: 'Text',
	TextAlign: 'Text alignment',
	TextColor: 'Text color',
	ThankYouForYourFeedback: 'Thank you for your feedback!',
	ThanksForLettingKnow: 'Thank you for letting us know.',
	ThePaymentMethod: 'The payment method',
	ThemThey: 'Them/They',
	Theme: 'Theme',
	ThemeAllColorsPickerTitle: 'More themes',
	ThemeColor: 'Theme',
	ThemeColorDarkMode: 'Dark',
	ThemeColorLightMode: 'Light',
	ThemeColorModePickerTitle: 'Colour mode',
	ThemeColorSystemMode: 'System',
	ThemeCpColorPickerTitle: 'Carepatron themes',
	ThemePanelDescription: 'Choose between light and dark mode, and customize your theme preferences',
	ThemePanelTitle: 'Appearance',
	Then: 'Then',
	Therapist: 'Therapist',
	Therapists: 'Therapists',
	Therapy: 'Therapy',
	Thick: 'Thick',
	Thin: 'Thin',
	ThirdPerson: '3rd person',
	ThisAndFollowingAppointments: 'This and following appointments',
	ThisAndFollowingMeetings: 'This and following meetings',
	ThisAndFollowingReminders: 'This and following reminders',
	ThisAndFollowingTasks: 'This and following tasks',
	ThisAppointment: 'This appointment',
	ThisMeeting: 'This meeting',
	ThisMonth: 'This month',
	ThisPerson: 'This person',
	ThisReminder: 'This reminder',
	ThisTask: 'This task',
	ThisWeek: 'This week',
	ThreeDay: '3 Day',
	Thursday: 'Thursday',
	Time: 'Time',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Time format',
	TimeIncrement: 'Time increment',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Timeslot size',
	Timestamp: 'Timestamp',
	Timezone: 'Timezone',
	TimezoneDisplay: 'Timezone display',
	TimezoneDisplayDescription: 'Manage your timezone display settings.',
	Title: 'Title',
	To: 'To',
	ToYourWorkspace: 'to your workspace',
	Today: 'Today',
	TodayInHoursPlural: 'Today in {count} {count, plural, one {hour} other {hours}}',
	TodayInMinsAbbreviated: 'Today in {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Toggle header cell',
	ToggleHeaderCol: 'Toggle header column',
	ToggleHeaderRow: 'Toggle header row',
	Tokyo: 'Tokyo',
	Tomorrow: 'Tomorrow',
	TomorrowAfternoon: 'Tomorrow afternoon',
	TomorrowMorning: 'Tomorrow morning',
	TooExpensive: 'Too expensive',
	TooHardToSetUp: 'Too hard to set up',
	TooManyFiles: 'More than 1 file detected.',
	ToolsExample: 'Simple practice, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Total',
	TotalAccountCredit: 'Total account credit',
	TotalAdjustments: 'Total adjustments',
	TotalAmountToCreditInCurrency: 'Total amount to credit ({currency})',
	TotalBilled: 'Total billed',
	TotalConversations: '{total} {total, plural, =0 {conversation} one {conversation} other {conversations}}',
	TotalOverdue: 'Total Overdue',
	TotalOverdueTooltip:
		'Total Overdue balance includes all unpaid invoices, ``reg``ardless of the date range, that are neither voided nor processed.',
	TotalPaid: 'Total Paid',
	TotalPaidTooltip:
		'Total Paid balance includes all amounts from invoices that have been paid within the specified date range.',
	TotalUnpaid: 'Total Unpaid',
	TotalUnpaidTooltip:
		'Total Unpaid balance includes all outstanding amounts from processing, unpaid, and sent invoices due within the specified date range.',
	TotalWorkflows: '{count} {count, plural, one {workflow} other {workflows}}',
	TotpSetUpManualEntryInstruction: 'Alternatively, you can manually enter the code below into the app:',
	TotpSetUpModalDescription: 'Scan the QR code with your authenticator app to set up Multi-Factor Authentication.',
	TotpSetUpModalTitle: 'Set up MFA device',
	TotpSetUpSuccess: `You're all set! MFA has been enabled.`,
	TotpSetupEnterAuthenticatorCodeInstruction: 'Enter the code generated by your authenticator app',
	Transcribe: 'Transcribe',
	TranscribeLanguageSelector: 'Select input language',
	TranscribeLiveAudio: 'Transcribe live audio',
	Transcribing: 'Transcribing audio...',
	TranscribingIn: 'Transcribing in',
	Transcript: 'Transcript',
	TranscriptRecordingCompleteInfo: 'You will see your transcript here once the recording has been completed.',
	TranscriptSuccessSnackbar: 'Successfully processed transcript.',
	Transcription: 'Transcription',
	TranscriptionEmpty: 'No transcription available',
	TranscriptionEmptyHelperMessage: 'This transcription didn’t pick up anything. Restart it and try again.',
	TranscriptionFailedNotice: 'This transcription was not processed successfully',
	TranscriptionIdleMessage:
		'We’re not hearing any audio. If you need more time, please respond within {timeValue} seconds, or the session will end.',
	TranscriptionInProcess: 'Transcription in process...',
	TranscriptionIncompleteNotice: 'Some parts of this transcription were not processed successfully',
	TranscriptionOvertimeWarning: '{scribeType} session ends in <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Are you sure you want to delete this transcription part?',
	TranscriptionText: 'Voice to text',
	TranscriptsPending: 'Your transcript will be available here after the session ends.',
	Transfer: 'Transfer',
	TransferAndDelete: 'Transfer and delete',
	TransferOwnership: 'Transfer ownership',
	TransferOwnershipConfirmationModalDescription:
		'This action can only be undone if they transfer ownership back to you.',
	TransferOwnershipDescription: 'Transfer the ownership of this workspace to another team member.',
	TransferOwnershipSuccessSnackbar: 'Transferred ownership successfully!',
	TransferOwnershipToMember: 'Are you sure you want to transfer this workspace to {staff}?',
	TransferStatusAlert:
		'Removing {numberOfStatuses, plural, one {this status} other {these statuses}} will impact {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} client status.</strong>} other {<strong>{numberOfAffectedRecords} client statuses.</strong>}}',
	TransferStatusDescription:
		'Choose another status for these clients before proceeding with the deletion. This action can’t be undone.',
	TransferStatusLabel: 'Transfer to new status',
	TransferStatusPlaceholder: 'Choose an existing status',
	TransferStatusTitle: 'Transfer status before deletion',
	TransferTaskAttendeeStatusAlert:
		'Removing this status will impact <strong>{number} future appointment {number, plural, one {status} other {statuses}}.</strong>',
	TransferTaskAttendeeStatusDescription:
		'Choose another status for these clients before proceeding with the deletion. This action can’t be undone.',
	TransferTaskAttendeeStatusSubtitle: 'Appointment status',
	TransferTaskAttendeeStatusTitle: 'Transfer status before deletion',
	Trash: 'Trash',
	TrashDeleteItemsModalConfirm: 'To confirm, type {confirmationText}',
	TrashDeleteItemsModalDescription:
		'The following {count, plural, one {item} other {items}} will be permanently deleted and cannot be restored.',
	TrashDeleteItemsModalTitle: 'Delete {count, plural, one {item} other {items}} forever',
	TrashDeletedAllItems: 'Deleted all items',
	TrashDeletedItems: 'Deleted {count, plural, one {item} other {items}}',
	TrashDeletedItemsFailure: 'Failed to delete items from trash',
	TrashLocationAppointmentType: 'Calendar',
	TrashLocationBillingAndPaymentsType: 'Billing & payments',
	TrashLocationContactType: 'Clients',
	TrashLocationNoteType: 'Notes & Documents',
	TrashRestoreItemsModalDescription: 'The following {count, plural, one {item} other {items}} will be restored.',
	TrashRestoreItemsModalTitle: 'Restore {count, plural, one {item} other {items}}',
	TrashRestoredAllItems: 'Restored all items',
	TrashRestoredItems: 'Restored {count, plural, one {item} other {items}}',
	TrashRestoredItemsFailure: 'Failed to restore items from trash',
	TrashSuccessfullyDeletedItem: 'Successfully deleted {type}',
	Trigger: 'Trigger',
	Troubleshoot: 'Troubleshoot',
	TryAgain: 'Try again',
	Tuesday: 'Tuesday',
	TwoToTen: '2 - 10',
	Type: 'Type',
	TypeHere: 'Type here...',
	TypeToConfirm: 'To confirm, type {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Heading 1',
	TypographyHeading2: 'Heading 2',
	TypographyHeading3: 'Heading 3',
	TypographyHeading4: 'Heading 4',
	TypographyHeading5: 'Heading 5',
	TypographyP: 'P',
	TypographyParagraph: 'Paragraph',
	UnableToCompleteAction: 'Unable to complete action.',
	UnableToPrintDocument: 'Unable to print document. Please try again later.',
	Unallocated: 'Unallocated',
	UnallocatedPaymentDescription: `This payment has not been fully allocated to billable items.
Add an allocation to unpaid items, or issue a credit or refund.`,
	UnallocatedPaymentTitle: 'Unallocated payment',
	UnallocatedPayments: 'Unallocated payments',
	Unarchive: 'Unarchive',
	Unassigned: 'Unassigned',
	UnauthorisedInvoiceSnackbar: `You don't have access to manage invoices for this client.`,
	UnauthorisedSnackbar: 'You do not have permission to do this.',
	Unavailable: 'Unavailable',
	Uncategorized: 'Uncategorized',
	Unclaimed: 'Unclaimed',
	UnclaimedAmount: 'Unclaimed amount',
	UnclaimedItems: 'Unclaimed items',
	UnclaimedItemsMustBeInCurrency: 'Only items in the following currencies are supported: {currencies}',
	Uncle: 'Uncle',
	Unconfirmed: 'Unconfirmed',
	Underline: 'Underline',
	Undo: 'Undo',
	Unfavorite: 'Unfavorite',
	Uninvoiced: 'Uninvoiced',
	UninvoicedAmount: 'Uninvoiced amount',
	UninvoicedAmounts: '{count, plural, =0 {No uninvoiced amounts} one {Uninvoiced amount} other {Uninvoiced amounts}}',
	Unit: 'Unit',
	UnitedKingdom: 'United Kingdom',
	UnitedStates: 'United States',
	UnitedStatesEast: 'United States - East',
	UnitedStatesWest: 'United States - West',
	Units: 'Units',
	UnitsIsRequired: 'Units is required',
	UnitsMustBeGreaterThanZero: 'Units must be greater than 0',
	UnitsPlaceholder: '1',
	Unknown: 'Unknown',
	Unlimited: 'Unlimited',
	Unlock: 'Unlock',
	UnlockNoteHelper: 'Before making any new changes, editors are required to unlock the note.',
	UnmuteAudio: 'Unmute audio',
	UnmuteEveryone: 'Unmute everyone',
	Unpaid: 'Unpaid',
	UnpaidInvoices: 'Unpaid invoices',
	UnpaidItems: 'Unpaid items',
	UnpaidMultiple: 'Unpaid',
	Unpublish: 'Unpublish',
	UnpublishTemplateConfirmationModalPrompt:
		'Removing <span>{title}</span> will remove this resource from the Carepatron community. This action can’t be undone.',
	UnpublishToCommunitySuccessMessage: 'Successfully removed ‛{title}’ from the community',
	Unread: 'Unread',
	Unrecognised: 'Unrecognized',
	UnrecognisedDescription:
		'This payment method is not recognised by your current application version. Please refresh your browser to get the latest version to view and edit this payment method.',
	UnsavedChanges: 'Unsaved changes',
	UnsavedChangesPromptContent: 'Do you want to save your changes before closing?',
	UnsavedChangesPromptTitle: 'You have unsaved changes',
	UnsavedNoteChangesWarning: 'Changes you made may not be saved',
	UnsavedTemplateChangesWarning: 'Changes you made may not be saved',
	UnselectAll: 'Unselect all',
	Until: 'Until',
	UntitledConversation: 'Untitled conversation',
	UntitledFolder: 'Untitled folder',
	UntitledNote: 'Untitled note',
	UntitledSchedule: 'Untitled schedule',
	UntitledSection: 'Untitled section',
	UntitledTemplate: 'Untitled template',
	Unverified: 'Unverified',
	Upcoming: 'Upcoming',
	UpcomingAppointments: 'Upcoming appointments',
	UpcomingDateOverridesEmpty: 'No date overrides have been found',
	UpdateAvailabilityScheduleFailure: 'Failed to update availability schedule',
	UpdateAvailabilityScheduleSuccess: 'Successfully updated availability schedule',
	UpdateInvoicesOrClaimsAgainstBillable: `Do you want the new pricing to be applied to attendee's invoices and claims?`,
	UpdateLink: 'Update link',
	UpdatePrimaryEmailWarningDescription: `Changing your client's email address will result to losing their access to their existing appointments and notes.`,
	UpdatePrimaryEmailWarningTitle: 'Client email change',
	UpdateSettings: 'Update settings',
	UpdateStatus: 'Update status',
	UpdateSuperbillReceiptFailure: 'Failed to update Superbill receipt',
	UpdateSuperbillReceiptSuccess: 'Successfully updated Superbill receipt',
	UpdateTaskBillingDetails: 'Update billing details',
	UpdateTaskBillingDetailsDescription: `The appointment pricing has changed. Do you want the new pricing to be applied to attendee's billing items, invoices and claims? Choose the updates you want to proceed with.`,
	UpdateTemplateFolderSuccessMessage: 'Successfully updated folder',
	UpdateUnpaidInvoices: 'Update unpaid invoices',
	UpdateUserInfoSuccessSnackbar: 'Successfully updated user information!',
	UpdateUserSettingsSuccessSnackbar: 'Successfully updated user settings!',
	Upgrade: 'Upgrade',
	UpgradeForSMSReminder: 'Upgrade to <b>Professional</b> for unlimited SMS reminders',
	UpgradeNow: 'Upgrade Now',
	UpgradePlan: 'Upgrade plan',
	UpgradeSubscriptionAlertDescription: `You're running low on storage. Upgrade your plan to unlock additional storage and keep your practice running smoothly!`,
	UpgradeSubscriptionAlertDescriptionNoPermission: `You're running low on storage. Ask someone in your practice with <span>Administrator access</span> about upgrading your plan to unlock additional storage and keep your practice running smoothly!`,
	UpgradeSubscriptionAlertTitle: 'It’s time to upgrade your subscription',
	UpgradeYourPlan: 'Upgrade your plan',
	UploadAudio: 'Upload audio',
	UploadFile: 'Upload file',
	UploadFileDescription: 'Which software platform are you switching from?',
	UploadFileMaxSizeError: 'File is too large. Maximum file size is {fileSizeLimit}.',
	UploadFileSizeLimit: 'Size limit {size}MB',
	UploadFileTileDescription: 'Use CSV, XLS, XLSX, or ZIP files to upload your clients.',
	UploadFileTileLabel: 'Upload a file',
	UploadFiles: 'Upload files',
	UploadIndividually: 'Upload files individually',
	UploadLogo: 'Upload logo',
	UploadPhoto: 'Upload photo',
	UploadToCarepatron: 'Upload to Carepatron',
	UploadYourLogo: 'Upload your logo',
	UploadYourTemplates: `Upload your templates and we'll convert them for you`,
	Uploading: 'Uploading',
	UploadingAudio: 'Uploading your audio...',
	UploadingFiles: 'Uploading files',
	UrlLink: 'URL Link',
	UsageCount: 'Used {count} times',
	UsageLimitValue: '{used} of {limit} used',
	UsageValue: '{used} used',
	Use: 'Use',
	UseAiToAutomateYourWorkflow: 'Use AI to automate your workflow!',
	UseAsDefault: 'Use as default',
	UseCustom: 'Use custom',
	UseDefault: 'Use default',
	UseDefaultFilters: 'Use default filters',
	UseTemplate: 'Use template',
	UseThisCard: 'Use this card',
	UseValue: 'Use "{value}"',
	UseWorkspaceDefault: 'Use workspace default',
	UserIsTyping: '{name} is typing...',
	Username: 'Username',
	Users: 'Users',
	VAT: 'VAT',
	ValidUrl: 'URL link must be a valid URL.',
	Validate: 'Validate',
	Validated: 'Validated',
	Validating: 'Validating',
	ValidatingContent: 'Validating content...',
	ValidatingTranscripts: 'Validating transcripts...',
	ValidationConfirmPasswordRequired: 'Confirm Password is required',
	ValidationDateMax: 'Must be before {max}',
	ValidationDateMin: 'Must be after {min}',
	ValidationDateRange: 'Start and end date are required',
	ValidationEndDateMustBeAfterStartDate: 'End date must be after start date',
	ValidationMixedDefault: 'This is invalid',
	ValidationMixedRequired: 'This is required',
	ValidationNumberInteger: 'Must be a whole number',
	ValidationNumberMax: 'Must be {max} or less',
	ValidationNumberMin: 'Must be {min} or more',
	ValidationPasswordNotMatching: `Passwords don't match`,
	ValidationPrimaryAddressIsRequired: 'Address is required when set as default',
	ValidationPrimaryPhoneNumberIsRequired: 'Phone number is required when set as default',
	ValidationServiceMustBeNotBeFuture: 'Service must not be current day or in the future',
	ValidationStringEmail: 'Must be a valid email',
	ValidationStringMax: 'Must be {max} or fewer characters',
	ValidationStringMin: 'Must be {min} or more characters',
	ValidationStringPhoneNumber: 'Must be a valid phone number',
	ValueMinutes: '{value} minutes',
	VerbosityConcise: 'Concise',
	VerbosityDetailed: 'Detailed',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Super detailed',
	VerificationCode: 'Verification code',
	VerificationEmailDescription: `Please enter your email address and the verification code we've just sent you.`,
	VerificationEmailSubtitle: `Check Spam folder - if the email hasn't arrived`,
	VerificationEmailTitle: 'Verify email',
	VerificationOption: 'Verification of email',
	Verified: 'Verified',
	Verify: 'Verify',
	VerifyAndSubmit: 'Verify & submit',
	VerifyEmail: 'Verify email',
	VerifyEmailAccessCode: 'Confirmation code',
	VerifyEmailAddress: 'Verify email address',
	VerifyEmailButton: 'Verify and logout',
	VerifyEmailSentSnackbar: 'Verification email sent. Check your inbox.',
	VerifyEmailSubTitle: `Check Spam folder if the email hasn't arrived`,
	VerifyEmailSuccessLogOutSnackbar: 'Success! Please log out to apply the changes.',
	VerifyEmailSuccessSnackbar: 'Success! Email verified. Please log in to continue as a verified account.',
	VerifyEmailTitle: 'Verify your email',
	VerifyNow: 'Verify now',
	Veterinarian: 'Veterinarian',
	VideoCall: 'Video call',
	VideoCallAudioInputFailed: 'Audio input device not working',
	VideoCallAudioInputFailedMessage: 'Open settings and check if you have the microphone source properly set',
	VideoCallChatBanner: 'Messages can be seen by everyone on this call and will be deleted when the call ends.',
	VideoCallChatSendBtn: 'Send a message',
	VideoCallChatTitle: 'Chat',
	VideoCallDisconnectedMessage: 'You lost your network connection. Trying to reconnect',
	VideoCallOptionInfo: 'Carepatron will manage video calls for your appointments if Zoom has not been connected',
	VideoCallTilePaused: 'This video is paused due to problems with your network',
	VideoCallTranscriptionFormDescription: 'You can adjust these settings at anytime',
	VideoCallTranscriptionFormHeading: 'Customize your AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'The generated output language',
	VideoCallTranscriptionFormNoteTemplateField: 'Set default note template',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'No templates with AI found',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Choose a template',
	VideoCallTranscriptionPronounField: 'Your pronouns',
	VideoCallTranscriptionRecordingNote:
		'At the end of the session, you’ll receive a generated <strong>{noteTemplate} note</strong> and transcript.',
	VideoCallTranscriptionReferClientField: 'Refer to Client as',
	VideoCallTranscriptionReferPractitionerField: 'Refer to Practitioner as',
	VideoCallTranscriptionTitle: 'AI Scribe',
	VideoCallTranscriptionVerbosityField: 'Verbosity',
	VideoCallTranscriptionWritingPerspectiveField: 'Writing perspective',
	VideoCalls: 'Video calls',
	VideoConferencing: 'Video conferencing',
	VideoOff: 'Video is off',
	VideoOn: 'Video is off',
	VideoQual360: 'Low quality (360p)',
	VideoQual540: 'Medium quality (540p)',
	VideoQual720: 'High quality (720p)',
	View: 'View',
	ViewAll: 'View all',
	ViewAppointment: 'View appointment',
	ViewBy: 'View by',
	ViewClaim: 'View claim',
	ViewCollection: 'View collection',
	ViewDetails: 'View details',
	ViewEnrollment: 'View enrollment',
	ViewPayment: 'View payment',
	ViewRecord: 'View record',
	ViewRemittanceAdvice: 'View remittance advice',
	ViewRemittanceAdviceHeader: 'Claim remittance advice',
	ViewRemittanceAdviceSubheader: 'Claim {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'View settings',
	ViewStripeDashboard: 'View Stripe dashboard',
	ViewTemplate: 'View template',
	ViewTemplates: 'View templates',
	ViewableBy: 'Viewable by',
	ViewableByHelper:
		'You and the Team always have access to notes you publish. You can choose to share this note with the client and/or their relationships',
	Viewer: 'Viewer',
	VirtualLocation: 'Virtual location',
	VisibleTo: 'Visible To',
	VisitOurHelpCentre: 'Visit our help centre',
	VisualEffects: 'Visual effects',
	VoiceFocus: 'Voice focus',
	VoiceFocusLabel: `Filters out sound from your mic that isn't speech`,
	Void: 'Void',
	VoidCancelPriorClaim: 'Void/Cancel prior claim',
	WaitingforMins: 'Waiting for {count} mins',
	Warning: 'Warning',
	WatchAVideo: 'watch a video',
	WatchDemoVideo: 'Watch demo video',
	WebConference: 'Web conference',
	WebConferenceOrVirtualLocation: 'Web conference / virtual location',
	WebDeveloper: 'Web Developer',
	WebsiteOptional: 'Website<span>(Optional)</span>',
	WebsiteUrl: 'Website URL',
	Wednesday: 'Wednesday',
	Week: 'Week',
	WeekPlural: '{count, plural, one {week} other {weeks}}',
	Weekly: 'Weekly',
	WeeksPlural: '{age, plural, one {# week} other {# weeks}}',
	WelcomeBack: 'Welcome back',
	WelcomeBackName: 'Welcome back, {name}',
	WelcomeName: 'Welcome {name}',
	WelcomeToCarepatron: 'Welcome to Carepatron',
	WhatCanIHelpWith: 'What can I help with?',
	WhatDidYouLikeResponse: 'What did you like about this response?',
	WhatIsCarepatron: 'What is Carepatron?',
	WhatMadeYouCancel: `What made you cancel your plan?
Check all that apply.`,
	WhatServicesDoYouOffer: 'What <mark>services</mark> do you offer?',
	WhatServicesDoYouOfferDescription: 'You can edit or add more services later.',
	WhatsYourAvailability: `What's your <mark>availability?</mark>`,
	WhatsYourAvailabilityDescription: 'You can add more schedules later.',
	WhatsYourBusinessName: `What's your <mark>business name?</mark>`,
	WhatsYourTeamSize: `What's your <mark>team size?</mark>`,
	WhatsYourTeamSizeDescription: 'This will help us set up your workspace correctly.',
	WhenThisHappens: 'When this happens:',
	WhichBestDescribesYou: 'Which best <mark>describes you?</mark>',
	WhichPlatforms: 'Which platforms?',
	Wife: 'Wife',
	WorkflowDescription: 'Workflow description',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Templates can link to workflows for smoother processes. View linked workflows to track and update them easily.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'Connect your SMS + emails based on common triggers',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Workflow automations',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Automated workflows',
	WorkflowTemplateConfigKey_Body: 'Body',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Show branding',
	WorkflowTemplateConfigKey_Content: 'Content',
	WorkflowTemplateConfigKey_Footer: 'Footer',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Show footer',
	WorkflowTemplateConfigKey_Header: 'Header',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Show header',
	WorkflowTemplateConfigKey_SecurityFooter: 'Security footer',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Show security footer',
	WorkflowTemplateConfigKey_Subject: 'Subject',
	WorkflowTemplateConfigKey_Title: 'Title',
	WorkflowTemplateDeleteConfirmationMessage:
		'Are you sure you want to delete this template? This action cannot be undone.',
	WorkflowTemplateDeleteConfirmationTitle: 'Delete notification template',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Are you sure? This will remove the {locale} version only—other languages won’t be affected. This action cannot be undone.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Delete ‘{locale}’ template',
	WorkflowTemplateDeletedSuccess: 'Notification template deleted successfully',
	WorkflowTemplateEditorDetailsTab: 'Template details',
	WorkflowTemplateEditorEmailContent: 'Email content',
	WorkflowTemplateEditorEmailContentTab: 'Email content',
	WorkflowTemplateEditorThemeTab: 'Theme',
	WorkflowTemplatePreviewerAlert: 'Previews use sample data to show what your clients will see.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Are you sure? This will reset the version back to the default template of the system. This action cannot be undone.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Reset template',
	WorkflowTemplateSendTestEmail: 'Send test email',
	WorkflowTemplateSendTestEmailDialogDescription: 'Try out your email setup by sending a test email to yourself.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Recipient email',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Send test',
	WorkflowTemplateSendTestEmailDialogTitle: 'Send a test email',
	WorkflowTemplateSendTestEmailSuccess: 'Success! Your <mark>{templateName}</mark> test email has been sent.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Manage your templates and add multiple language versions to communicate effectively with clients.',
	WorkflowTemplateTemplateEditor: 'Template editor',
	WorkflowTemplateTranslateLocaleError: 'Something went wrong while translating content',
	WorkflowTemplateTranslateLocaleSuccess: 'Successfully translated the content to <strong>{locale}</strong>',
	WorkflowsAndReminders: 'Workflows & Reminders',
	WorkflowsManagement: 'Workflows Management',
	WorksheetAndHandout: 'Worksheet/Handout',
	WorksheetsAndHandoutsDescription: 'For client engagement and education',
	Workspace: 'Workspace',
	WorkspaceBranding: 'Workspace branding',
	WorkspaceBrandingDescription: `Effortlessly brand your workspace with a cohesive style that reflects your
	professionalism and personality. Customize invoices to online booking for a beautiful
	customer experience.`,
	WorkspaceName: 'Workspace name',
	Workspaces: 'Workspaces',
	WriteOff: 'Write-off',
	WriteOffModalDescription:
		'You have <mark>{count} {count, plural, one {line item} other {line items}}</mark> to be written-off',
	WriteOffModalTitle: 'Write-off adjustment',
	WriteOffReasonHelperText: 'This is an internal note and will not be visible to your client.',
	WriteOffReasonPlaceholder: 'Adding a write-off reason can help when reviewing billable transactions',
	WriteOffTotal: 'Total write-off ({currencyCode})',
	Writer: 'Writer',
	Yearly: 'Yearly',
	YearsPlural: '{age, plural, one {# year} other {# years}}',
	Yes: 'Yes',
	YesArchive: 'Yes, archive',
	YesDelete: 'Yes, delete',
	YesDeleteOverride: 'Yes, delete override',
	YesDeleteSection: 'Yes, delete',
	YesDisconnect: 'Yes, disconnect',
	YesEnd: 'Yes, end',
	YesEndTranscription: 'Yes, end transcription',
	YesImFineWithThat: `Yes, I'm fine with that`,
	YesLeave: 'Yes, leave',
	YesMinimize: 'Yes, minimize',
	YesOrNoAnswerTypeDescription: 'Configure answer type',
	YesOrNoFormPrimaryText: 'Yes | No',
	YesOrNoFormSecondaryText: 'Choose yes or no options',
	YesProceed: 'Yes, proceed',
	YesRemove: 'Yes, remove',
	YesRestore: 'Yes, restore',
	YesStopIgnoring: 'Yes, stop ignoring',
	YesTransfer: 'Yes, transfer',
	Yesterday: 'Yesterday',
	YogaInstructor: 'Yoga Instructor',
	You: 'You',
	YouArePresenting: 'You are presenting',
	YouCanChooseMultiple: 'You can choose multiple',
	YouCanSelectMultiple: 'You can select multiple',
	YouHaveOngoingTranscription: 'You have an ongoing transcription',
	YourAnswer: 'Your answer',
	YourDisplayName: 'Your display name',
	YourSpreadsheetColumns: 'Your spreadsheet columns',
	YourTeam: 'Your team',
	ZipCode: 'Zip code',
	Zoom: 'Zoom',
	ZoomUserNotInAccountErrorCodeSnackbar: `You can't add a Zoom call for this team member. Please refer to the <a>support docs for more info.</a>`,
};

export default items;
