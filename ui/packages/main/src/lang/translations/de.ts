import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'KI-Eingabeaufforderungen',
	ATeamMemberIsRequired: 'Ein Teammitglied wird benötigt',
	AboutClient: '<PERSON>ber den Kunden',
	AcceptAppointment: 'Vielen Dank für die Bestätigung Ihres Termins',
	AcceptTermsAndConditionsRequired: 'Die Bedingungen akzeptieren ',
	Accepted: 'Akzeptiert',
	AccessGiven: 'Zugang gewährt',
	AccessPermissions: 'Zugriffsberechtigungen',
	AccessType: 'Zugangsart',
	Accident: 'Unfall',
	Account: 'Konto',
	AccountCredit: 'Kontoguthaben',
	Accountant: 'Buchhalter',
	Action: 'Aktion',
	Actions: 'Aktionen',
	Active: 'Aktiv',
	ActiveTags: 'Aktive Tags',
	ActiveUsers: 'Aktive Benutzer',
	Activity: 'Aktivität',
	Actor: '<PERSON><PERSON><PERSON><PERSON>',
	Acupuncture: 'Akupunktur',
	Acupuncturist: 'Akupunkteur',
	Acupuncturists: 'Akupunkteure',
	AcuteManifestationOfAChronicCondition: 'Akute Manifestation einer chronischen Erkrankung',
	Add: 'Hinzufügen',
	AddADescription: 'Hinzufügen einer Beschreibung',
	AddALocation: 'Ort hinzufügen',
	AddASecondTimezone: 'Eine zweite Zeitzone hinzufügen',
	AddAddress: 'Adresse hinzufügen',
	AddAnother: '  Neue hinzufügen',
	AddAnotherAccount: 'Ein weiteres Konto hinzufügen',
	AddAnotherContact: 'Einen weiteren Kontakt hinzufügen',
	AddAnotherOption: 'Weitere Option hinzufügen',
	AddAnotherTeamMember: 'Einen weiteren Teammitglied hinzufügen',
	AddAvailablePayers: '+ Verfügbare Kostenträger hinzufügen',
	AddAvailablePayersDescription:
		'Suche Zahler, die du deiner Arbeitsbereich-Zahlerliste hinzufügen möchtest. Nachdem du sie hinzugefügt hast, kannst du Anmeldungen verwalten oder die Zahlerdetails bei Bedarf anpassen.',
	AddCaption: 'Bildunterschrift hinzufügen',
	AddClaim: 'Anspruch hinzufügen',
	AddClientFilesModalDescription:
		'Um den Zugriff einzuschränken, wählen Sie die Optionen in den Kontrollkästchen &quot;Sichtbar für&quot;',
	AddClientFilesModalTitle: 'Dateien für {name} hochladen',
	AddClientNoteButton: 'Notiz hinzufügen',
	AddClientNoteModalDescription:
		'Fügen Sie Ihrer Notiz Inhalt hinzu. Verwenden Sie den Abschnitt „Anzeigbar für“, um eine oder mehrere Gruppen auszuwählen, die diese bestimmte Notiz sehen können.',
	AddClientNoteModalTitle: 'Notiz hinzufügen',
	AddClientOwnerRelationshipModalDescription:
		'Durch die Einladung kann der Kunde seine eigenen Profilinformationen verwalten und den Benutzerzugriff auf seine Profilinformationen steuern.',
	AddClientOwnerRelationshipModalTitle: 'Laden Sie den Kunden ein',
	AddCode: 'Code hinzufügen',
	AddColAfter: 'Spalte hinzufügen nach',
	AddColBefore: 'Spalte hinzufügen vor',
	AddCollection: 'Sammlung hinzufügen',
	AddColor: 'Farbe hinzufügen',
	AddColumn: 'Spalte hinzufügen',
	AddContactRelationship: 'Kontaktbeziehung hinzufügen',
	AddContacts: 'Kontakte hinzufügen',
	AddCustomField: 'Benutzerdefiniertes Feld hinzufügen',
	AddDate: 'Datum hinzufügen',
	AddDescription: 'Beschreibung hinzufügen',
	AddDetail: 'Details hinzufügen',
	AddDisplayName: 'Anzeigenamen hinzufügen',
	AddDxCode: 'Diagnosecode hinzufügen',
	AddEmail: 'E-Mail hinzufügen',
	AddFamilyClientRelationshipModalDescription:
		'Wenn Sie ein Familienmitglied einladen, kann es Pflegeberichte und die Profilinformationen des Klienten sehen. Wenn sie als Administratoren eingeladen werden, können sie die Profilinformationen des Klienten aktualisieren und den Benutzerzugriff verwalten.',
	AddFamilyClientRelationshipModalTitle: 'Familienmitglied einladen',
	AddField: 'Feld hinzufügen',
	AddFormField: 'Formularfeld hinzufügen',
	AddImages: 'Füge Bilder hinzu',
	AddInsurance: 'Versicherung hinzufügen',
	AddInvoice: 'Rechnung hinzufügen',
	AddLabel: 'Etikett hinzufügen',
	AddLanguage: 'Sprache hinzufügen',
	AddLocation: 'Ort hinzufügen',
	AddManually: 'Manuell hinzufügen',
	AddMessage: 'Nachricht hinzufügen',
	AddNewAction: 'Neue Aktion hinzufügen',
	AddNewSection: 'Neuen Abschnitt hinzufügen',
	AddNote: 'Notiz hinzufügen',
	AddOnlineBookingDetails: 'Details zur Online-Buchung hinzufügen',
	AddPOS: 'POS hinzufügen',
	AddPaidInvoices: 'Bezahlte Rechnungen hinzufügen',
	AddPayer: 'Zahler hinzufügen',
	AddPayment: 'Zahlung hinzufügen',
	AddPaymentAdjustment: 'Zahlungsanpassung hinzufügen',
	AddPaymentAdjustmentDisabledDescription: 'Die Zahlungszuordnungen werden nicht verändert.',
	AddPaymentAdjustmentEnabledDescription: 'Der zuzuteilende Betrag wird reduziert.',
	AddPhoneNumber: 'Telefonnummer hinzufügen',
	AddPhysicalOrVirtualLocations: 'Fügen Sie physische oder virtuelle Standorte hinzu',
	AddQuestion: 'Frage hinzufügen',
	AddQuestionOrTitle: 'Eine Frage oder einen Titel hinzufügen',
	AddRelationship: 'Beziehung hinzufügen',
	AddRelationshipModalTitle: 'Vorhandenen Kontakt verknüpfen',
	AddRelationshipModalTitleNewClient: 'Neuen Kontakt verbinden',
	AddRow: 'Zeile hinzufügen',
	AddRowAbove: 'Zeile darüber hinzufügen',
	AddRowBelow: 'Zeile unten hinzufügen',
	AddService: 'Dienst hinzufügen',
	AddServiceLocation: 'Servicestandort hinzufügen',
	AddServiceToCollections: 'Dienst zu Sammlungen hinzufügen',
	AddServiceToOneOrMoreCollections: 'Dienst zu einer oder mehreren Sammlungen hinzufügen',
	AddServices: 'Dienste hinzufügen',
	AddSignature: 'Signatur hinzufügen',
	AddSignaturePlaceholder: 'Geben Sie zusätzliche Details ein, die in Ihre Signatur aufgenommen werden sollen',
	AddSmartDataChips: 'Smart Data Chips hinzufügen',
	AddStaffClientRelationshipsModalDescription:
		'Durch die Auswahl von Mitarbeitern können diese Pflegeberichte für diesen Kunden erstellen und anzeigen. Sie können auch Kundeninformationen anzeigen.',
	AddStaffClientRelationshipsModalTitle: 'Mitarbeiterbeziehungen hinzufügen',
	AddTag: 'Füge einen Tag hinzu',
	AddTags: 'Tags hinzufügen',
	AddTemplate: 'Vorlage hinzufügen',
	AddTimezone: 'Zeitzone hinzufügen',
	AddToClaim: 'Zum Anspruch hinzufügen',
	AddToCollection: 'Zur Sammlung hinzufügen',
	AddToExisting: 'Zu vorhandenem hinzufügen',
	AddToStarred: 'Zu den Favoriten hinzufügen',
	AddUnclaimedItems: 'Nicht abgeholte Artikel hinzufügen',
	AddUnrelatedContactWarning:
		'Sie haben jemanden hinzugefügt, der kein Kontakt von {contact} ist. Stellen Sie sicher, dass der Inhalt relevant ist, bevor Sie mit dem Teilen fortfahren.',
	AddValue: 'Füge "{value}" hinzu',
	AddVideoCall: 'Videoanruf hinzufügen',
	AddVideoOrVoiceCall: 'Video- oder Sprachanruf hinzufügen',
	AddictionCounselor: 'Suchtberater',
	AddingManualPayerDisclaimer:
		'Wenn Sie einen Zahler manuell zu Ihrer Anbieterliste hinzufügen, wird nicht automatisch eine Verbindung zur elektronischen Einreichung von Ansprüchen mit diesem Zahler eingerichtet. Wenn Sie elektronisch einreichen müssen, müssen Sie dies separat einrichten.',
	AddingTeamMembersIncreaseCostAlert:
		'Durch das Hinzufügen neuer Teammitglieder erhöht sich Ihr monatliches Abonnement. ',
	Additional: 'Zusätzlich',
	AdditionalBillingProfiles: 'Zusätzliche Abrechnungsprofile',
	AdditionalBillingProfilesSectionDescription:
		'Überschreiben Sie die standardmäßigen Rechnungsinformationen, die für bestimmte Teammitglieder, Zahler oder Rechnungsvorlagen verwendet werden.',
	AdditionalFeedback: 'Zusätzliches Feedback',
	AddnNewWorkspace: 'Neuer Arbeitsbereich',
	AddnNewWorkspaceSuccessSnackbar: 'Arbeitsbereich wurde erstellt!',
	Address: 'Adresse',
	AddressNumberStreet: 'Adresse (Nr., Straße)',
	Adjustment: 'Anpassung',
	AdjustmentType: 'Anpassungstyp',
	Admin: 'Administrator',
	Admins: 'Admins',
	AdminsOnly: 'Nur Administratoren',
	AdvancedPlanInclusionFive: 'Account-Manager',
	AdvancedPlanInclusionFour: 'Google Analytics',
	AdvancedPlanInclusionHeader: 'Alles in Plus  ',
	AdvancedPlanInclusionOne: 'Rollen ',
	AdvancedPlanInclusionSix: 'Unterstützung beim Datenimport',
	AdvancedPlanInclusionThree: 'Weiße Beschriftung',
	AdvancedPlanInclusionTwo: '90 Tage Aufbewahrung gelöschter Daten',
	AdvancedPlanMessage:
		'Behalten Sie die Kontrolle über die Bedürfnisse Ihrer Praxis. Überprüfen Sie Ihren aktuellen Plan und überwachen Sie die Nutzung.',
	AdvancedSettings: 'Erweiterte Einstellungen',
	AdvancedSubscriptionPlanSubtitle: 'Erweitern Sie Ihre Praxis mit allen Funktionen',
	AdvancedSubscriptionPlanTitle: 'Fortschrittlich',
	AdvertisingManager: 'Werbemanager',
	AerospaceEngineer: 'Ingenieur für Luft-und Raumfahrt',
	AgeYearsOld: '{age} Jahre alt',
	Agenda: 'Agenda',
	AgendaView: 'Agenda-Ansicht',
	AiAskSupportedFileTypes: 'Unterstützte Dateitypen: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Ein Assistent an Ihren Fingerspitzen',
	AiCopilotDisclaimer: 'AI Copilot kann Fehler machen. Überprüfen Sie wichtige Informationen.',
	AiCreateNewConversation: 'Neue Unterhaltung erstellen',
	AiEnhanceYourProductivity: 'Steigern Sie Ihre Produktivität',
	AiPoweredTemplates: 'KI-gestützte Vorlagen',
	AiScribeNoDeviceFoundErrorMessage:
		'Es sieht so aus, als ob Ihr Browser diese Funktion nicht unterstützt oder keine kompatiblen Geräte verfügbar sind.',
	AiScribeUploadFormat: 'Unterstützte Dateitypen: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'nur 1 Datei gleichzeitig',
	AiShowConversationHistory: 'Konversationsverlauf anzeigen',
	AiSmartPromptNodePlaceholderText:
		'Geben Sie hier Ihre benutzerdefinierte Eingabeaufforderung ein, um präzise und personalisierte KI-Ergebnisse zu generieren.',
	AiSmartPromptPrimaryText: 'AI-intelligenter Prompt',
	AiSmartPromptSecondaryText: 'Füge einen benutzerdefinierten AI-Smart-Prompt ein',
	AiSmartReminders: 'Intelligente Erinnerungen mit KI',
	AiTemplateBannerTitle: 'Vereinfachen Sie Ihre Arbeit mit KI-gestützten Vorlagen',
	AiTemplates: 'KI-Vorlagen',
	AiTokens: 'KI-Token',
	AiWorkBetterWithAi: '<h1>Besser arbeiten mit KI</h1>',
	All: 'Alle',
	AllAppointments: 'Alle Termine',
	AllCategories: 'Alle Kategorien',
	AllClients: 'Alle Kunden',
	AllContactPolicySelectorLabel: 'Alle Kontakte von <mark>{client}</mark>',
	AllContacts: 'Alle Kontakte',
	AllContactsOf: 'Alle Kontakte von ‘{name}’',
	AllDay: 'Den ganzen Tag',
	AllInboxes: 'Alle Posteingänge',
	AllIndustries: 'Alle Branchen',
	AllLocations: 'alle Orte',
	AllMeetings: 'Alle Tagungen',
	AllNotificationsRestoredMessage: 'Alle Benachrichtigungen wiederhergestellt',
	AllProfessions: 'Alle Berufe',
	AllReminders: 'Alle Erinnerungen',
	AllServices: 'Alle Dienstleistungen',
	AllStatuses: 'Alle Status',
	AllTags: 'Alle Tags',
	AllTasks: 'Alle Aufgaben',
	AllTeamMembers: 'Alle Teammitglieder',
	AllTypes: 'Alle Arten',
	Allocated: 'Zugewiesen',
	AllocatedItems: 'Zugewiesene Artikel',
	AllocationTableEmptyState: 'Keine Zahlungszuordnungen gefunden',
	AllocationTotalWarningMessage: `Der zugewiesene Betrag übersteigt den Gesamtzahlungsbetrag.
 Bitte überprüfen Sie die nachstehenden Positionen.`,
	AllowClientsToCancelAnytime: 'Ermöglichen Sie Kunden, jederzeit zu kündigen',
	AllowNewClient: 'Neue Kunden zulassen',
	AllowNewClientHelper: 'Neukunden können diesen Service buchen',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Erlauben Sie mindestens {hours} Stunden vor dem Termin',
	AllowToUseSavedCard: 'Erlaube {provider}, die gespeicherte Karte in Zukunft zu verwenden',
	AllowVideoCalls: 'Videoanrufe zulassen',
	AlreadyAdded: 'Bereits hinzugefügt',
	AlreadyHasAccess: 'Hat Zugriff',
	AlreadyHasAccount: 'Sie haben bereits ein Konto?',
	Always: 'Immer',
	AlwaysIgnore: 'Immer ignorieren',
	Amount: 'Menge',
	AmountDue: 'Fälliger Betrag',
	AmountOfReferralRequests: '{amount, plural, one {# Überweisungswunsch} other {# Überweisungswünsche}}',
	AmountPaid: 'Gezahlter Betrag',
	AnalyzingAudio: 'Audio wird analysiert...',
	AnalyzingInputContent: 'Eingabeinhalt wird analysiert ...',
	AnalyzingRequest: 'Anfrage wird analysiert...',
	AnalyzingTemplateContent: 'Vorlageninhalt wird analysiert …',
	And: 'Und',
	Annually: 'Jährlich',
	Anonymous: 'Anonym',
	AnswerExceeded: 'Ihre Antwort muss weniger als 300 Zeichen umfassen.',
	AnyStatus: 'Jeder Status',
	AppNotifications: 'Benachrichtigungen',
	AppNotificationsClearanceHeading: 'Gute Arbeit! Sie haben alle Aktivitäten gelöscht',
	AppNotificationsEmptyHeading: 'Ihre Arbeitsbereichsaktivität wird in Kürze hier angezeigt',
	AppNotificationsEmptySubtext: 'Es sind derzeit keine Maßnahmen erforderlich',
	AppNotificationsIgnoredCount: '{total} ignoriert',
	AppNotificationsUnread: '{total} ungelesen',
	Append: 'Anhängen',
	Apply: 'Anwenden',
	ApplyAccountCredit: 'Kontoguthaben beantragen',
	ApplyDiscount: 'Rabatt anwenden',
	ApplyVisualEffects: 'Visuelle Effekte anwenden',
	ApplyVisualEffectsNotSupported: 'Das Anwenden visueller Effekte wird nicht unterstützt',
	Appointment: 'Termin',
	AppointmentAssignedNotificationSubject: '{actorProfileName} hat Ihnen {appointmentName} zugewiesen.',
	AppointmentCancelledNotificationSubject: '{actorProfileName} hat {appointmentName} abgesagt.',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} hat {appointmentName} bestätigt.',
	AppointmentDetails: 'Details zum Termin',
	AppointmentLocation: 'Terminort',
	AppointmentLocationDescription:
		'Verwalten Sie Ihre Standard-Standorte, sowohl virtuell als auch physisch. Wenn ein Termin geplant wird, werden diese Standorte automatisch angewendet.',
	AppointmentNotFound: 'Termin nicht gefunden',
	AppointmentReminder: 'Terminerinnerung',
	AppointmentReminders: 'Terminerinnerungen',
	AppointmentRemindersInfo:
		'Richten Sie automatische Erinnerungen für Kundentermine ein, um Nichterscheinen und Absagen zu vermeiden',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} hat {appointmentName} verschoben.',
	AppointmentSaved: 'Termin gespeichert',
	AppointmentStatus: 'Terminstatus',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0Min} other {{minutes}Min}} } one {{hours}Std {minutes, plural, =0 {} other {{minutes}Min}}} other {{hours}Std {minutes, plural, =0 {} other {{minutes}Min}}} }',
	AppointmentUndone: 'Termin abgesagt',
	Appointments: 'Termine',
	Archive: 'Archiv',
	ArchiveClients: 'Archiv-Clients',
	Archived: 'Archiviert',
	AreYouAClient: 'Sind Sie Kunde?',
	AreYouStillThere: 'Bist du noch da?',
	AreYouSure: 'Bist du sicher?',
	Arrangements: 'Arrangements',
	ArtTherapist: 'Kunsttherapeut',
	Articles: 'Artikel',
	Artist: 'Künstler',
	AskAI: 'Fragen Sie die KI',
	AskAiAddFormField: 'Hinzufügen eines Formularfelds',
	AskAiChangeFormality: 'Formalität ändern',
	AskAiChangeToneToBeMoreProfessional: 'Ändern Sie den Ton, um professioneller zu wirken',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Erklären Sie, worum es in diesem Dokument geht',
	AskAiExplainWhatThisImageIsAbout: 'Erklären Sie, worum es in diesem Bild geht',
	AskAiFixSpellingAndGrammar: 'Rechtschreibung und Grammatik korrigieren',
	AskAiGenerateACaptionForThisImage: 'Erstellen Sie eine Beschriftung für dieses Bild',
	AskAiGenerateFromThisPage: 'Von dieser Seite aus generieren',
	AskAiGetStarted: 'Erste Schritte',
	AskAiGiveItAFriendlyTone: 'Geben Sie ihm einen freundlichen Ton',
	AskAiGreeting: 'Hallo {firstName}! Wie kann ich dir heute helfen?',
	AskAiHowCanIHelpWithYourContent: 'Wie kann ich bei Ihren Inhalten helfen?',
	AskAiInsert: 'Einfügen',
	AskAiMakeItMoreCasual: 'Machen Sie es lockerer',
	AskAiMakeThisTextMoreConcise: 'Machen Sie diesen Text prägnanter',
	AskAiMoreProfessional: 'Professioneller',
	AskAiOpenPreviousNote: 'Vorherige Notiz öffnen',
	AskAiPondering: 'Nachdenken',
	AskAiReplace: 'Ersetzen',
	AskAiReviewOrEditSelection: 'Auswahl überprüfen oder bearbeiten',
	AskAiRuminating: 'Grübeln',
	AskAiSeeMore: 'Mehr anzeigen',
	AskAiSimplifyLanguage: 'Sprache vereinfachen',
	AskAiSomethingWentWrong:
		'Etwas ist schief gelaufen. Wenn dieses Problem weiterhin besteht, kontaktieren Sie uns bitte über unser Hilfezentrum.',
	AskAiStartWithATemplate: 'Beginnen Sie mit einer Vorlage',
	AskAiSuccessfullyCopiedResponse: 'KI-Antwort erfolgreich kopiert',
	AskAiSuccessfullyInsertedResponse: 'Erfolgreich eingefügte KI-Antwort',
	AskAiSuccessfullyReplacedResponse: 'Erfolgreich ersetzte KI-Antwort',
	AskAiSuggested: 'Empfohlen',
	AskAiSummariseTextIntoBulletPoints: 'Text in Stichpunkten zusammenfassen',
	AskAiSummarizeNote: 'Notiz zusammenfassen',
	AskAiThinking: 'Denken',
	AskAiToday: 'Heute {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Was möchten Sie mit diesem Formular tun?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Schreiben Sie eine professionelle Notiz mithilfe der Vorlage',
	AskAskAiAnything: 'Stellen Sie der KI alle möglichen Fragen',
	AskWriteSearchAnything: 'Fragen Sie, schreiben Sie „@“ oder suchen Sie nach etwas …',
	Asking: 'Fragen',
	Assessment: 'Bewertung',
	Assessments: 'Beurteilungen',
	AssessmentsCategoryDescription: 'Für die Aufzeichnung von Klientenbewertungen',
	AssignClients: 'Kunden zuweisen',
	AssignNewClients: 'Kunden zuweisen',
	AssignServices: 'Dienste zuweisen',
	AssignTeam: 'Team zuweisen',
	AssignTeamMember: 'Teammitglied zuweisen',
	Assigned: 'Zugewiesen',
	AssignedClients: 'Zugewiesene Kunden',
	AssignedServices: 'Zugewiesene Leistungen',
	AssignedServicesDescription:
		'Zeigen Sie die Ihnen zugewiesenen Dienste an, verwalten Sie sie und passen Sie die Preise Ihren individuellen Tarifen an. ',
	AssignedTeam: 'Zugewiesenes Team',
	AthleticTrainer: 'Sporttrainer',
	AttachFiles: 'Dateien anhängen',
	AttachLogo: 'Anfügen',
	Attachment: 'Anhang',
	AttachmentBlockedFileType: 'Aus Sicherheitsgründen gesperrt!',
	AttachmentTooLargeFileSize: 'Datei zu groß',
	AttachmentUploadItemComplete: 'Vollständig',
	AttachmentUploadItemError: 'Upload fehlgeschlagen',
	AttachmentUploadItemLoading: 'Wird geladen',
	AttemptingToReconnect: 'Versuche, die Verbindung wiederherzustellen...',
	Attended: 'Besucht',
	AttendeeBeingMutedTooltip:
		'Der Gastgeber hat Sie stummgeschaltet. Verwenden Sie „Hand heben“, um die Stummschaltung aufzuheben.',
	AttendeeWithId: 'Teilnehmer {attendeeId}',
	Attendees: 'Teilnehmer',
	AttendeesCount: '{count} Teilnehmer',
	Attending: 'Teilnahme',
	Audiologist: 'Audiologe',
	Aunt: 'Tante',
	Australia: 'Australien',
	AuthenticationCode: 'Authentifizierungscode',
	AuthoriseProvider: 'Autorisiere {provider}',
	AuthorisedProviders: 'Autorisierte Anbieter',
	AutoDeclineAllFutureOption: 'Nur neue Ereignisse oder Termine',
	AutoDeclineAllOption: 'Neue und bestehende Events oder Termine',
	AutoDeclinePrimaryText: 'Automatisch Ereignisse ablehnen',
	AutoDeclineSecondaryText: 'Ereignisse während Ihrer Abwesenheit werden automatisch abgelehnt.',
	AutogenerateBillings: 'Rechnungsdokumente automatisch generieren',
	AutogenerateBillingsDescription:
		'Automatische Abrechnungsdokumente werden am letzten Tag des Monats erstellt. Rechnungen und Superbill-Belege können jederzeit manuell erstellt werden.',
	AutomateWorkflows: 'Automatisieren Sie Workflows',
	AutomaticallySendSuperbill: 'Superbill-Belege automatisch versenden',
	AutomaticallySendSuperbillHelperText:
		'Eine Superbill ist eine detaillierte Quittung über die an einen Kunden erbrachten Leistungen zur Erstattung durch die Versicherung.',
	Automation: 'Automatisierung',
	AutomationActionSendEmailLabel: 'E-Mail senden',
	AutomationActionSendSMSLabel: 'SMS senden',
	AutomationAndReminders: 'Automatisierung ',
	AutomationDeletedSuccessMessage: 'Automatisierung erfolgreich gelöscht',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Zeitereignis',
	AutomationParams_timeUnit: 'Einheit',
	AutomationParams_timeValue: 'Nummer',
	AutomationPublishSuccessMessage: 'Automatisierung erfolgreich veröffentlicht',
	AutomationPublishWarningTooltip:
		'Bitte überprüfen Sie die Automatisierungskonfiguration erneut und stellen Sie sicher, dass sie richtig konfiguriert wurde',
	AutomationTriggerEventCancelledDescription: 'Wird ausgelöst, wenn ein Ereignis abgesagt oder gelöscht wird',
	AutomationTriggerEventCancelledLabel: 'Veranstaltung abgesagt',
	AutomationTriggerEventCreatedDescription: 'Wird ausgelöst, wenn ein Ereignis erstellt wird',
	AutomationTriggerEventCreatedLabel: 'Neues Event',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Wird ausgelöst, wenn ein Ereignis erstellt oder aktualisiert wird (außer wenn es abgesagt wird)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Neues oder aktualisiertes Event',
	AutomationTriggerEventEndedDescription: 'Wird ausgelöst, wenn ein Ereignis endet',
	AutomationTriggerEventEndedLabel: 'Veranstaltung beendet',
	AutomationTriggerEventStartsDescription:
		'Wird ausgelöst, wenn eine bestimmte Zeitspanne vor Beginn eines Ereignisses',
	AutomationTriggerEventStartsLabel: 'Veranstaltung beginnt',
	Automations: 'Automatisierungen',
	Availability: 'Verfügbarkeit',
	AvailabilityDisableSchedule: 'Zeitplan deaktivieren',
	AvailabilityDisabled: 'Deaktiviert',
	AvailabilityEnableSchedule: 'Zeitplan aktivieren',
	AvailabilityEnabled: 'Ermöglicht',
	AvailabilityNoActiveBanner:
		'Sie haben alle Ihre Terminpläne deaktiviert. Kunden können Sie nicht online buchen und alle zukünftigen Termine müssen manuell bestätigt werden.',
	AvailabilityNoActiveConfirmationDescription:
		'Wenn Sie diese Verfügbarkeit deaktivieren, haben Sie keine aktiven Terminpläne mehr. Kunden können Sie nicht online buchen und alle Buchungen von Ärzten fallen außerhalb Ihrer Arbeitszeiten.',
	AvailabilityNoActiveConfirmationProceed: 'Ja, weiter',
	AvailabilityNoActiveConfirmationTitle: 'Keine aktiven Zeitpläne',
	AvailabilityToggle: 'Zeitplan aktiviert',
	AvailabilityUnsetDate: 'Kein Datum festgelegt',
	AvailableLocations: 'Verfügbare Standorte',
	AvailablePayers: 'Verfügbare Zahler',
	AvailablePayersEmptyState: 'Keine Zahler ausgewählt',
	AvailableTimes: 'Verfügbare Zeiten',
	Back: 'Zurück',
	BackHome: 'Zurück zuhause',
	BackToAppointment: 'Zurück zum Termin',
	BackToLogin: 'Zurück zum Login',
	BackToMapColumns: 'Zurück zu Kartenspalten',
	BackToTemplates: 'Zurück zu Vorlagen',
	BackToUploadFile: 'Zurück zum Datei-Upload',
	Banker: 'Banker',
	BasicBlocks: 'Grundblöcke',
	BeforeAppointment: 'Senden Sie {deliveryType} Erinnerung {interval} {unit} vor Termin',
	BehavioralAnalyst: 'Verhaltensanalytiker',
	BehavioralHealthTherapy: 'Verhaltenstherapie',
	Beta: 'Beta',
	BillTo: 'Gesetzesentwurf für',
	BillableItems: 'Abrechnungsfähige Artikel',
	BillableItemsEmptyState: 'Es wurden keine abrechenbaren Positionen gefunden',
	Biller: 'Biller',
	Billing: 'Abrechnung',
	BillingAddress: 'Rechnungsadresse',
	BillingAndReceiptsUnauthorisedMessage:
		'Um auf diese Informationen zuzugreifen, ist der Zugriff auf die Ansicht „Rechnungen und Zahlungen“ erforderlich.',
	BillingBillablesTab: 'Abrechenbare',
	BillingClaimsTab: 'Ansprüche',
	BillingDetails: 'Rechnungsdetails',
	BillingDocuments: 'Abrechnungsdokumente',
	BillingDocumentsClaimsTab: 'Ansprüche',
	BillingDocumentsEmptyState: 'Keine {tabType} gefunden',
	BillingDocumentsInvoicesTab: 'Rechnungen',
	BillingDocumentsSuperbillsTab: 'Superrechnungen',
	BillingInformation: 'Abrechnungsdaten',
	BillingInvoicesTab: 'Rechnungen',
	BillingItems: 'Rechnungspositionen',
	BillingPaymentsTab: 'Zahlungen',
	BillingPeriod: 'Abrechnungszeitraum',
	BillingProfile: 'Abrechnungsprofil',
	BillingProfileOverridesDescription:
		'Begrenzen Sie die Verwendung dieses Abrechnungsprofils auf bestimmte Teammitglieder.',
	BillingProfileOverridesHeader: 'Zugriff beschränken',
	BillingProfileProviderType: 'Anbietertyp',
	BillingProfileTypeIndividual: 'Individuell',
	BillingProfileTypeIndividualSubLabel: 'Typ 1 NPI',
	BillingProfileTypeOrganisation: 'Organisation',
	BillingProfileTypeOrganisationSubLabel: 'Typ 2 NPI',
	BillingProfiles: 'Abrechnungsprofile',
	BillingProfilesEditHeader: 'Rechnungsdetails für {name} bearbeiten',
	BillingProfilesNewHeader: 'Neues Abrechnungsprofil',
	BillingProfilesSectionDescription:
		'Verwalten Sie Ihre Abrechnungsinformationen für Ärzte und Versicherungszahler, indem Sie Abrechnungsprofile einrichten, die auf Rechnungen und Versicherungsauszahlungen angewendet werden können.',
	BillingSearchPlaceholder: 'Artikel suchen',
	BillingSettings: 'Abrechnungseinstellungen',
	BillingSuperbillsTab: 'Superrechnungen',
	BiomedicalEngineer: 'Biomedizintechniker',
	BlankInvoice: 'Blankorechnung',
	BlueShieldProviderNumber: 'Blue Shield-Anbieternummer',
	Body: 'Körper',
	Bold: 'Deutlich',
	BookAgain: 'Nochmals buchen',
	BookAppointment: 'Einen Termin verabreden',
	BookableOnline: 'Online buchbar',
	BookableOnlineHelper: 'Kunden können diesen Service online buchen',
	BookedOnline: 'Online gebucht',
	Booking: 'Buchung',
	BookingAnalyticsIntegrationPanelDescription:
		'Richten Sie Google Tag Manager ein, um wichtige Aktionen und Konvertierungen in Ihrem Online-Buchungsablauf zu verfolgen. Sammeln Sie wertvolle Daten zu Benutzerinteraktionen, um Ihre Marketingbemühungen zu verbessern und das Buchungserlebnis zu optimieren.',
	BookingAnalyticsIntegrationPanelTitle: 'Analytics-Integration',
	BookingAndCancellationPolicies: 'Buchung ',
	BookingButtonEmbed: 'Taste',
	BookingButtonEmbedDescription: 'Fügt Ihrer Website eine Online-Buchungsschaltfläche hinzu',
	BookingDirectTextLink: 'Direkter Textlink',
	BookingDirectTextLinkDescription: 'Öffnet die Online-Buchungsseite',
	BookingFormatLink: 'Link formatieren',
	BookingFormatLinkButtonTitle: 'Schaltflächentitel',
	BookingInlineEmbed: 'Inline-Einbettung',
	BookingInlineEmbedDescription: 'Lädt die Online-Buchungsseite direkt in Ihre Website',
	BookingLink: 'Buchungslink',
	BookingLinkModalCopyText: 'Kopieren',
	BookingLinkModalDescription:
		'Ermöglichen Sie Kunden über diesen Link, beliebige Teammitglieder oder Dienste zu buchen',
	BookingLinkModalHelpText: 'Erfahren Sie, wie Sie Online-Buchungen einrichten',
	BookingLinkModalTitle: 'Teilen Sie Ihren Buchungslink',
	BookingPolicies: 'Buchungsrichtlinien',
	BookingPoliciesDescription: 'Legen Sie fest, wann Online-Buchungen durch Kunden möglich sind',
	BookingTimeUnitDays: 'Tage',
	BookingTimeUnitHours: 'Std.',
	BookingTimeUnitMinutes: 'Protokoll',
	BookingTimeUnitMonths: 'Monate',
	BookingTimeUnitWeeks: 'Wochen',
	BottomNavBilling: 'Abrechnung',
	BottomNavGettingStarted: 'Heim',
	BottomNavMore: 'Mehr',
	BottomNavNotes: 'Anmerkungen',
	Brands: 'Marken',
	Brother: 'Bruder',
	BrotherInLaw: 'Schwager',
	BrowseOrDragFileHere: '<link>Durchsuchen</link> oder Datei hierher ziehen',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} Minuten nach',
	BufferAndLabel: 'Und',
	BufferAppointmentLabel: 'einen Termin',
	BufferBeforeTime: '{time} Min. vorher',
	BufferTime: 'Pufferzeit',
	BufferTimeViewLabel: '{bufferBefore} Minuten vor und {bufferAfter} Minuten nach Terminen',
	BulkArchiveClientsDescription:
		'Möchten Sie diese Kunden wirklich archivieren? Sie können sie später erneut aktivieren.',
	BulkArchiveSuccess: 'Kunden erfolgreich archiviert',
	BulkArchiveUndone: 'Massenarchivierung rückgängig gemacht',
	BulkPermanentDeleteDescription:
		'Dies löscht **{count} Konversationen**. Diese Aktion kann nicht rückgängig gemacht werden.',
	BulkPermanentDeleteTitle: 'Konversationen dauerhaft löschen',
	BulkUnarchiveSuccess: 'Kunden erfolgreich entarchiviert',
	BulletedList: 'Aufzählungsliste',
	BusinessAddress: 'Geschäftsadresse',
	BusinessAddressOptional: 'Geschäftsadresse<span>(Optional)</span>',
	BusinessName: 'Firmenname',
	Button: 'Button',
	By: 'Von',
	CHAMPUSIdentificationNumber: 'CHAMPUS-Identifikationsnummer',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC ist erforderlich',
	Calendar: 'Kalender',
	CalendarAppSyncFormDescription: 'Synchronisieren Sie Carepatron-Ereignisse mit',
	CalendarAppSyncPanelTitle: 'Synchronisierung verbundener Apps',
	CalendarDescription: 'Verwalten Sie Ihre Termine oder legen Sie persönliche Aufgaben und Erinnerungen fest',
	CalendarDetails: 'Kalenderdetails',
	CalendarDetailsDescription: 'Verwalten Sie Ihre Kalender- und Terminanzeigeeinstellungen.',
	CalendarScheduleNew: 'Termin neu planen',
	CalendarSettings: 'Kalendereinstellungen',
	Call: 'Anruf',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> hat am Videoanruf teilgenommen',
	CallChangeLayoutTextContent: 'Auswahl wird für zukünftige Meetings gespeichert',
	CallIdlePrompt: 'Möchten Sie mit der Anmeldung lieber noch warten oder es später noch einmal versuchen?',
	CallLayoutOptionAuto: 'Automatisch',
	CallLayoutOptionSidebar: 'Seitenleiste',
	CallLayoutOptionSpotlight: 'Spotlight',
	CallLayoutOptionTiled: 'Gekachelt',
	CallNoAttendees: 'Keine Teilnehmer an der Besprechung.',
	CallSessionExpiredError: 'Sitzung abgelaufen. Anruf wurde beendet. Bitte versuchen Sie erneut beizutreten.',
	CallWithPractitioner: 'Anruf mit {practitioner}',
	CallsListCreateButton: 'Neuer Anruf',
	CallsListEmptyState: 'Keine aktiven Anrufe',
	CallsListItemEndCall: 'Anruf beenden',
	CamWarningMessage: 'Bei Ihrer Kamera wurde ein Problem festgestellt',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Bitte ermöglichen Sie Carepatron den Zugriff auf Ihre Kamera und Ihr Mikrofon.
 Weitere Informationen <a>finden Sie in dieser Anleitung</a>`,
	CameraAndMicIssueModalTitle: 'Kamera und Mikrofon sind blockiert',
	CameraQuality: 'Kameraqualität',
	CameraSource: 'Kameraquelle',
	CanModifyReadOnlyEvent: 'Du kannst dieses Ereignis nicht ändern.',
	Canada: 'Kanada',
	Cancel: 'Stornieren',
	CancelClientImportDescription: 'Sind Sie sicher, dass Sie diesen Import abbrechen möchten?',
	CancelClientImportPrimaryAction: 'Ja, Import abbrechen',
	CancelClientImportSecondaryAction: 'Bearbeitung fortsetzen',
	CancelClientImportTitle: 'Abbrechen des Client-Imports',
	CancelImportButton: 'Import abbrechen',
	CancelPlan: 'Plan stornieren',
	CancelPlanConfirmation: `Wenn Sie den Plan kündigen, wird Ihr Konto automatisch mit allen ausstehenden Beträgen für diesen Monat belastet.
 Wenn Sie die Anzahl der abgerechneten Benutzer herabstufen möchten, können Sie einfach Teammitglieder entfernen und Carepatron aktualisiert Ihren Abonnementpreis automatisch.`,
	CancelSend: 'Sendung abbrechen',
	CancelSubscription: 'Abonnement beenden',
	Canceled: 'Abgebrochen',
	CancellationPolicy: 'Stornierungsbedingungen',
	Cancelled: 'Abgesagt',
	CannotContainSpecialCharactersError: 'Darf keine {specialCharacters} enthalten',
	CannotDeleteInvoice: 'Über Online-Zahlungen bezahlte Rechnungen können nicht gelöscht werden',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> kann nicht aus der <b>{group}</b> Gruppe entfernt werden',
	CannotMoveServiceOutsideCollections: 'Dienst kann nicht außerhalb von Sammlungen verschoben werden',
	CapeTown: 'Kapstadt',
	Caption: 'Untertitel',
	CaptureNameFieldLabel: 'Der Name, mit dem Sie von anderen angesprochen werden möchten',
	CapturePaymentMethod: 'Zahlungsmethode erfassen',
	CapturingAudio: 'Audio aufnehmen',
	CapturingSignature: 'Unterschrift wird erfasst...',
	CardInformation: 'Karteninformationen',
	CardNumberRequired: 'Kartennummer ist erforderlich',
	CardiacRehabilitationSpecialist: 'Spezialist für kardiologische Rehabilitation',
	Cardiologist: 'Kardiologe',
	CareAiNoConversations: 'Noch keine Gespräche',
	CareAiNoConversationsDescription: 'Starte ein Gespräch mit {aiName}, um loszulegen',
	CareAssistant: 'Pflegekraft',
	CareManager: 'Pflegemanager',
	Caregiver: 'Pflegekraft',
	CaregiverCreateModalDescription:
		'Wenn Sie Mitarbeiter als Administratoren hinzufügen, können diese Pflegeberichte erstellen und verwalten. Außerdem erhalten sie vollen Zugriff zum Erstellen und Verwalten von Kunden.',
	CaregiverCreateModalTitle: 'Neues Teammitglied',
	CaregiverListCantAddStaffInfoTitle:
		'Sie haben die maximale Mitarbeiterzahl für Ihr Abonnement erreicht. Bitte aktualisieren Sie Ihren Plan, um weitere Mitarbeiter hinzuzufügen.',
	CaregiverListCreateButton: 'Neues Teammitglied',
	CaregiverListEmptyState: 'Keine Betreuer hinzugefügt',
	CaregiversListItemRemoveStaff: 'Mitarbeiter entfernen',
	CarepatronApp: 'Carepatron App',
	CarepatronCommunity: 'Gemeinschaft',
	CarepatronFieldAddress: 'Adresse',
	CarepatronFieldAssignedStaff: 'Zugewiesenes Personal',
	CarepatronFieldBirthDate: 'Geburtsdatum',
	CarepatronFieldEmail: 'Email',
	CarepatronFieldEmploymentStatus: 'Arbeitsverhältnis',
	CarepatronFieldEthnicity: 'Ethnizität',
	CarepatronFieldFirstName: 'Vorname',
	CarepatronFieldGender: 'Geschlecht',
	CarepatronFieldIdentificationNumber: 'Identifikationsnummer',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Etikett',
	CarepatronFieldLastName: 'Familienname, Nachname',
	CarepatronFieldLivingArrangements: 'Wohnsituation',
	CarepatronFieldMiddleNames: 'Zweiter Vorname',
	CarepatronFieldOccupation: 'Beruf',
	CarepatronFieldPhoneNumber: 'Telefonnummer',
	CarepatronFieldRelationshipStatus: 'Beziehungsstatus',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: 'Maximal 10 Status.',
	CarepatronFieldTags: 'Stichworte',
	CarepatronFields: 'Carepatron-Felder',
	Cash: 'Kasse',
	Category: 'Kategorie',
	CategoryInputPlaceholder: 'Wählen Sie eine Vorlagenkategorie',
	CenterAlign: 'Zentriert ausrichten',
	Central: 'Zentral',
	ChangeLayout: 'Layout ändern',
	ChangeLogo: 'Ändern',
	ChangePassword: 'Kennwort ändern',
	ChangePasswordFailureSnackbar:
		'Leider wurde Ihr Passwort nicht geändert. Überprüfen Sie, ob Ihr altes Passwort korrekt ist.',
	ChangePasswordHelperInfo: 'Mindestens {minLength} Zeichen lang',
	ChangePasswordSuccessfulSnackbar:
		'Passwort erfolgreich geändert! Achten Sie bei der nächsten Anmeldung darauf, dieses Passwort zu verwenden.',
	ChangeSubscription: 'Abonnement ändern',
	ChangesNotAllowed: 'In diesem Feld können keine Änderungen vorgenommen werden',
	ChargesDisabled: 'Gebühren deaktiviert',
	ChargesEnabled: 'Gebühren aktiviert',
	ChargesStatus: 'Gebührenstatus',
	ChartAndDiagram: 'Diagramm/Chart',
	ChartsAndDiagramsCategoryDescription: 'Zur Veranschaulichung von Kundendaten und Fortschritt',
	ChatEditMessage: 'Nachricht bearbeiten',
	ChatReplyTo: 'Antwort an {name}',
	ChatTypeMessageTo: 'Nachricht {name}',
	Check: 'Überprüfen',
	CheckList: 'Checklist',
	Chef: 'Koch',
	Chiropractic: 'Chiropraktik',
	Chiropractor: 'Chiropraktiker',
	Chiropractors: 'Chiropraktiker',
	ChooseACollection: 'Wähle eine Sammlung',
	ChooseAContact: 'Wählen Sie einen Kontakt',
	ChooseAccountTypeHeader: 'Was beschreibt Sie am besten?',
	ChooseAction: 'Aktion auswählen',
	ChooseAnAccount: 'Wähle einen Account',
	ChooseAnOption: 'Wählen Sie eine Option',
	ChooseBillingProfile: 'Abrechnungsprofil auswählen',
	ChooseClaim: 'Anspruch auswählen',
	ChooseCollection: 'Kollektion auswählen',
	ChooseColor: 'Wähle eine Farbe',
	ChooseCustomDate: 'Wählen Sie ein benutzerdefiniertes Datum',
	ChooseDateAndTime: 'Datum und Uhrzeit auswählen',
	ChooseDxCodes: 'Diagnosecodes auswählen',
	ChooseEventType: 'Wählen Sie den Veranstaltungstyp',
	ChooseFileButton: 'Wähle eine Datei',
	ChooseFolder: 'Ordner auswählen',
	ChooseInbox: 'Posteingang auswählen',
	ChooseMethod: 'Methode wählen',
	ChooseNewOwner: 'Neuen Besitzer wählen',
	ChooseOrganization: 'Organisation auswählen',
	ChoosePassword: 'Wählen sie ein Passwort',
	ChoosePayer: 'Zahler auswählen',
	ChoosePaymentMethod: 'Wählen Sie eine Bezahlungsart',
	ChoosePhysicalOrRemoteLocations: 'Ort eingeben oder auswählen',
	ChoosePlan: 'Wähle {plan}',
	ChooseProfessional: 'Wählen Sie Professional',
	ChooseServices: 'Wählen Sie Dienstleistungen',
	ChooseSource: 'Quelle auswählen',
	ChooseSourceDescription:
		'Wählen Sie aus, woher Sie Kunden importieren – ob aus einer Datei oder einer anderen Softwareplattform.',
	ChooseTags: 'Tags auswählen',
	ChooseTaxName: 'Steuernamen wählen',
	ChooseTeamMembers: 'Teammitglieder auswählen',
	ChooseTheme: 'Thema auswählen',
	ChooseTrigger: 'Auslöser auswählen',
	ChooseYourProvider: 'Wählen Sie Ihren Anbieter',
	CircularProgressWithLabel: '{value}%',
	City: 'Stadt',
	CivilEngineer: 'Bauingenieur',
	Claim: 'Beanspruchen',
	ClaimAddReferringProvider: 'Referierenden Anbieter hinzufügen',
	ClaimAddRenderingProvider: 'Rendering-Anbieter hinzufügen',
	ClaimAmount: 'Anspruchsbetrag',
	ClaimAmountPaidHelpContent:
		'Der gezahlte Betrag ist die vom Patienten oder anderen Kostenträgern erhaltene Zahlung. Geben Sie nur den Gesamtbetrag ein, den der Patient und/oder andere Kostenträger für die abgedeckten Leistungen gezahlt haben.',
	ClaimAmountPaidHelpSubtitle: 'Feld 29',
	ClaimAmountPaidHelpTitle: 'Gezahlter Betrag',
	ClaimBillingProfileTypeIndividual: 'Person',
	ClaimBillingProfileTypeOrganisation: 'Organisation',
	ClaimChooseRenderingProviderOrTeamMember: 'Rendering-Anbieter oder Teammitglied auswählen',
	ClaimClientInsurancePolicies: 'Kundenversicherungspolicen',
	ClaimCreatedAction: '<mark>Anspruch {claimNumber}</mark> erstellt',
	ClaimDeniedAction: '<mark>Anspruch {claimNumber}</mark> wurde von <b>{payerNumber} {payerName}</b> abgelehnt.',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Suche nach ICD 10 Diagnosecodes',
	ClaimDiagnosisSelectorHelpContent: `Die „Diagnose oder Verletzung“ ist das Anzeichen, Symptom, die Beschwerde oder der Zustand des Patienten in Bezug auf die Leistung(en) im Anspruch.
 Es können bis zu 12 ICD 10 Diagnosecodes ausgewählt werden.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Feld 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnose oder Verletzung',
	ClaimDiagnosticCodesEmptyError: 'Mindestens ein Diagnosecode ist erforderlich',
	ClaimDoIncludeReferrerInformation: 'Geben Sie Referrer-Informationen in CMS1500 an',
	ClaimERAReceivedAction: 'Elektronische Überweisung erhalten von <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Elektronische Überweisung erhalten	<mark>Zahlung {paymentReference}</mark> für <b>{paymentAmount}</b> von <b>{payerNumber} {payerName}</b> wurde verbucht',
	ClaimExportedAction: '<mark>Anspruch {claimNumber}</mark> wurde als <b>{attachmentType}</b> exportiert',
	ClaimFieldClient: 'Name des Kunden oder Ansprechpartners',
	ClaimFieldClientAddress: 'Kundenadresse',
	ClaimFieldClientAddressDescription:
		'Geben Sie die Adresse des Kunden ein. Die erste Zeile ist für die Straßenadresse. Verwenden Sie in der Adresse keine Satzzeichen (Kommas oder Punkte) oder Symbole. Wenn Sie eine ausländische Adresse angeben, wenden Sie sich an den Zahler, um spezifische Anweisungen zur Angabe zu erhalten.',
	ClaimFieldClientAddressSubtitle: 'Feld 5',
	ClaimFieldClientDateOfBirth: 'Geburtsdatum des Kunden',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Feld 3',
	ClaimFieldClientDateOfBirthDescription:
		'Geben Sie das 8-stellige Geburtsdatum des Kunden ein (TT/MM/JJJJ). Das Geburtsdatum des Kunden ist eine Information, die den Kunden identifiziert und Personen mit ähnlichen Namen unterscheidet.',
	ClaimFieldClientDescription:
		'Der Name des Kunden ist der Name der Person, die die Behandlung oder Lieferung erhalten hat.',
	ClaimFieldClientSexDescription:
		'Das „Geschlecht“ ist eine Information zur Identifizierung des Kunden und unterscheidet Personen mit ähnlichem Namen.',
	ClaimFieldClientSubtitle: 'Feld 2',
	ClaimFiling: 'Schadensmeldung',
	ClaimHistorySubtitle: 'Versicherung • Schadensfall {number}',
	ClaimIncidentAutoAccident: 'Autounfall?',
	ClaimIncidentConditionRelatedTo: 'Hängt der Zustand des Klienten mit',
	ClaimIncidentConditionRelatedToHelpContent:
		'Diese Informationen geben an, ob die Krankheit oder Verletzung des Klienten mit der Beschäftigung, dem Autounfall oder einem anderen Unfall in Zusammenhang steht. Beschäftigung (aktuell oder früher) würde darauf hinweisen, dass der Zustand mit der Arbeit oder dem Arbeitsplatz des Klienten in Zusammenhang steht. Autounfall würde darauf hinweisen, dass der Zustand das Ergebnis eines Autounfalls ist. Anderer Unfall würde darauf hinweisen, dass der Zustand das Ergebnis einer anderen Art von Unfall ist.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Felder 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Hängt der Zustand des Klienten mit',
	ClaimIncidentCurrentIllness: 'Aktuelle Krankheit, Verletzung oder Schwangerschaft',
	ClaimIncidentCurrentIllnessHelpContent:
		'Das Datum der aktuellen Erkrankung, Verletzung oder Schwangerschaft identifiziert das Datum des ersten Krankheitsausbruchs, das tatsächliche Datum der Verletzung oder die letzte Monatsblutung bei einer Schwangerschaft.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Feld 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Daten der aktuellen Krankheit, Verletzung oder Schwangerschaft (LMP)',
	ClaimIncidentDate: 'Datum',
	ClaimIncidentDateFrom: 'Stammen aus',
	ClaimIncidentDateTo: 'Datum bis',
	ClaimIncidentEmploymentRelated: 'Anstellung',
	ClaimIncidentEmploymentRelatedDesc: '(Aktuell oder vorhergehend)',
	ClaimIncidentHospitalizationDatesLabel: 'Krankenhausaufenthaltsdaten im Zusammenhang mit aktuellen Leistungen',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Die Krankenhausaufenthaltsdaten im Zusammenhang mit aktuellen Leistungen beziehen sich auf den Aufenthalt eines Klienten und geben die Aufnahme- und Entlassungsdaten im Zusammenhang mit der/den Leistung(en) auf der Rechnung an.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Feld 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle:
		'Krankenhausaufenthaltsdaten im Zusammenhang mit aktuellen Leistungen',
	ClaimIncidentInformation: 'Informationen zum Vorfall',
	ClaimIncidentOtherAccident: 'Anderer Unfall?',
	ClaimIncidentOtherAssociatedDate: 'Anderes zugehöriges Datum',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Das andere Datum gibt zusätzliche Datumsinformationen zum Zustand oder zur Behandlung des Klienten an.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Feld 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Anderes Datum',
	ClaimIncidentQualifier: 'Qualifikation',
	ClaimIncidentQualifierPlaceholder: 'Qualifizierer auswählen',
	ClaimIncidentUnableToWorkDatesLabel: 'Der Klient konnte seinem derzeitigen Beruf nicht mehr nachgehen',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'„Daten, in denen der Klient nicht in der Lage war, in seinem aktuellen Beruf zu arbeiten“ ist der Zeitraum, in dem der Klient nicht arbeiten konnte oder konnte.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Feld 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle:
		'Daten, an denen der Kunde seinen derzeitigen Beruf nicht ausüben konnte',
	ClaimIncludeReferrerInformation: 'Referrer-Informationen in CMS1500 einbinden',
	ClaimInsuranceCoverageTypeHelpContent: `Die Art des Krankenversicherungsschutzes, der für diesen Anspruch gilt. „Sonstiges“ bedeutet Krankenversicherung einschließlich HMOs, gewerbliche Versicherung, Autounfallversicherung, Haftpflichtversicherung oder Arbeiterunfallversicherung.
 Diese Informationen leiten den Anspruch an das richtige Programm weiter und können die primäre Haftung begründen.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Feld 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Abdeckungsart',
	ClaimInsuranceGroupIdHelpContent: `Geben Sie die Versicherungs- oder Gruppennummer des Versicherten so ein, wie sie auf der Krankenversicherungskarte des Versicherten steht.

 Die „Police-, Gruppen- oder FECA-Nummer des Versicherten“ ist die alphanumerische Kennung für den Kranken-, Auto- oder sonstigen Versicherungsschutz. Die FECA-Nummer ist die 9-stellige alphanumerische Kennung, die einem Patienten zugewiesen wird, der eine arbeitsbedingte Erkrankung geltend macht.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Feld 11',
	ClaimInsuranceGroupIdHelpTitle: 'Versicherungspolice, Gruppen- oder FECA-Nummer des Versicherten',
	ClaimInsuranceMemberIdHelpContent: `Geben Sie die Versicherungsnehmer-Identifikationsnummer ein, die auf dem Versicherungsausweis des Versicherungsnehmers für den Zahler steht, bei dem der Anspruch geltend gemacht wird.
 Wenn dem Patienten vom Kostenträger eine eindeutige Mitgliedsidentifikationsnummer zugewiesen wurde, geben Sie diese Nummer in dieses Feld ein.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Feld 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Mitgliedsnummer des Versicherten',
	ClaimInsurancePayer: 'Versicherungszahler',
	ClaimManualPaymentAction: '<mark>Zahlung {paymentReference}</mark> für <b>{paymentAmount}</b> verzeichnet',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Zusätzliche Anspruchsinformationen',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Bitte beziehen Sie sich auf die aktuellen Anweisungen des öffentlichen oder privaten Zahlers bezüglich der Verwendung dieses Feldes. Melden Sie den entsprechenden Qualifizierer, falls verfügbar, für die eingegebenen Informationen.Geben Sie kein Leerzeichen, Bindestrich oder eine andere Trennzeichen zwischen dem Qualifizierer und den Informationen ein.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Feld 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Zusätzliche Informationen zum Anspruch',
	ClaimMiscClaimCodes: 'Anspruchscodes',
	ClaimMiscOriginalReferenceNumber: 'Ursprüngliche Referenznummer',
	ClaimMiscPatientsAccountNumber: 'Kontonummer des Patienten',
	ClaimMiscPatientsAccountNumberHelpContent:
		'Die Kontonummer des Patienten ist die vom Leistungserbringer vergebene Kennung.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Feld 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Kontonummer des Patienten',
	ClaimMiscPriorAuthorizationNumber: 'Vorabgenehmigungsnummer',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Bei der Vorautorisierungsnummer handelt es sich um die dem Zahler zugewiesene Nummer zur Autorisierung der Dienstleistung(en).',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Feld 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Vorabgenehmigungsnummer',
	ClaimMiscResubmissionCode: 'Wiedervorlagecode',
	ClaimMiscResubmissionCodeHelpContent:
		'Unter erneuter Einreichung versteht man den Code und die ursprüngliche Referenznummer, die vom Zielzahler oder -empfänger zugewiesen wurden, um einen zuvor eingereichten Anspruch oder Vorfall anzuzeigen.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Feld 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Wiedervorlage und / oder ursprüngliche Referenznummer',
	ClaimNumber: 'Anspruchsnummer',
	ClaimNumberFormat: 'Forderung Nr. {number}',
	ClaimOrderingProvider: 'Bestellender Anbieter',
	ClaimOtherId: 'Andere ID',
	ClaimOtherIdPlaceholder: 'Wählen Sie eine Option',
	ClaimOtherIdQualifier: 'Anderer ID-Qualifizierer',
	ClaimOtherIdQualifierPlaceholder: 'ID-Qualifizierer auswählen',
	ClaimPlaceOfService: 'Leistungsort',
	ClaimPlaceOfServicePlaceholder: 'POS hinzufügen',
	ClaimPolicyHolderRelationship: 'Versicherungsnehmerverhältnis',
	ClaimPolicyInformation: 'Richtlinien-Informationen',
	ClaimPolicyTelephone: 'Telefon (mit Vorwahl)',
	ClaimReceivedAction: '<mark>Anspruch {claimNumber}</mark> von <b>{name}</b> empfangen',
	ClaimReferringProvider: 'Verweisender Anbieter',
	ClaimReferringProviderEmpty: 'Kein verweisender Anbieter/keine verweisenden Anbieter hinzugefügt',
	ClaimReferringProviderHelpContent:
		'Der eingegebene Name ist der des überweisenden, bestellenden oder überwachenden Leistungserbringers, der die Leistung(en) oder Lieferung(en) im Anspruch überwiesen, bestellt oder überwacht hat. Der Qualifizierer gibt die Rolle des gemeldeten Leistungserbringers an.',
	ClaimReferringProviderHelpSubtitle: 'Feld 17',
	ClaimReferringProviderHelpTitle: 'Name des verweisenden Anbieters bzw. der Quelle',
	ClaimReferringProviderQualifier: 'Qualifikation',
	ClaimReferringProviderQualifierPlaceholder: 'Qualifizierer auswählen',
	ClaimRejectedAction: '<mark>Anspruch {claimNumber}</mark> wurde von <b>{name}</b> abgelehnt',
	ClaimRenderingProviderIdNumber: 'ID-Nummer',
	ClaimRenderingProviderOrTeamMember: 'Rendering-Anbieter oder Teammitglied',
	ClaimRestoredAction: '<mark>Anspruch {claimNumber}</mark> wurde wiederhergestellt',
	ClaimServiceFacility: 'Serviceeinrichtung',
	ClaimServiceFacilityLocationHelpContent:
		'Der Name und die Adresse der Einrichtung, in der die Leistungen erbracht wurden, identifizieren den Ort, an dem die Leistungen erbracht wurden.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a und 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Feld 32, 32a und 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Serviceeinrichtung',
	ClaimServiceFacilityPlaceholder: 'Serviceeinrichtung oder Standort wählen',
	ClaimServiceLabChargesHelpContent: `Füllen Sie dieses Feld aus, wenn Sie Ansprüche für erworbene Dienste geltend machen, die von einer anderen Stelle als dem Abrechnungsanbieter bereitgestellt wurden.
 Jede erworbene Dienstleistung muss in einer separaten Forderung gemeldet werden, da im Formular CMS1500 nur eine Gebühr eingetragen werden kann.`,
	ClaimServiceLabChargesHelpSubtitle: 'Feld 20',
	ClaimServiceLabChargesHelpTitle: 'Kosten für externe Labore',
	ClaimServiceLineServiceHelpContent:
		'„Verfahren, Dienstleistungen oder Lieferungen“ bezeichnen die medizinischen Dienstleistungen und Verfahren, die dem Patienten zur Verfügung gestellt werden.',
	ClaimServiceLineServiceHelpSubtitle: 'Feld 24d',
	ClaimServiceLineServiceHelpTitle: 'Verfahren, Dienstleistungen oder Lieferungen',
	ClaimServiceLinesEmptyError: 'Mindestens eine Service-Leitung ist erforderlich',
	ClaimServiceSupplementaryInfoHelpContent: `Fügen Sie eine zusätzliche narrative Beschreibung der bereitgestellten Dienste unter Verwendung der entsprechenden Qualifikationen hinzu.
 Geben Sie zwischen Qualifizierer und Informationen kein Leerzeichen, keinen Bindestrich oder ein anderes Trennzeichen ein.

 Vollständige Anweisungen zum Hinzufügen zusätzlicher Informationen finden Sie in den Anweisungen zum CMS 1500-Anspruchsformular.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Feld 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Ergänzende Informationen',
	ClaimSettingsBillingMethodTitle: 'Abrechnungsmethode für Kunden',
	ClaimSettingsClientSignatureDescription:
		'Ich bin mit der Weitergabe medizinischer und sonstiger Informationen einverstanden, die für die Bearbeitung von Versicherungsansprüchen erforderlich sind.',
	ClaimSettingsClientSignatureTitle: 'Kundensignatur in der Datei',
	ClaimSettingsConsentLabel: 'Einwilligung zur Bearbeitung von Versicherungsansprüchen erforderlich:',
	ClaimSettingsDescription:
		'Wählen Sie die Rechnungsstellungsmethode für den Kunden, um eine reibungslose Zahlungsabwicklung zu gewährleisten:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} hat eine aktive Versicherung. Um die Versicherung abzurechnen, aktualisiere die Kundenrechnungsmethode auf Versicherung.',
	ClaimSettingsInsuranceDescription: 'Von der Versicherung erstattete Kosten',
	ClaimSettingsInsuranceTitle: 'Versicherung',
	ClaimSettingsNoPoliciesAlertDescription:
		'Fügen Sie eine Versicherungspolice hinzu, um Versicherungsansprüche zu ermöglichen.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Ich bin damit einverstanden, für erbrachte Leistungen Versicherungsleistungen zu erhalten.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Unterschrift des Versicherungsnehmers in den Akten',
	ClaimSettingsSelfPayDescription: 'Der Kunde zahlt für die Termine',
	ClaimSettingsSelfPayTitle: 'Selbstzahler',
	ClaimSettingsTitle: 'Anspruchseinstellungen',
	ClaimSexSelectorPlaceholder: 'Männlich weiblich',
	ClaimStatusChangedAction: '<mark>Anspruch {claimNumber}</mark> Status aktualisiert',
	ClaimSubmittedAction:
		'<mark>Anspruch {claimNumber}</mark> an <b>{payerClearingHouse}</b> für <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Anspruch #{claimNumber}',
	ClaimSupervisingProvider: 'Betreuender Anbieter',
	ClaimSupplementaryInfo: 'Ergänzende Informationen',
	ClaimSupplementaryInfoPlaceholder: 'Ergänzende Informationen hinzufügen',
	ClaimTrashedAction: '<mark>Anspruch {claimNumber}</mark> wurde gelöscht',
	ClaimValidationFailure: 'Gültigkeit der Forderung konnte nicht überprüft werden',
	ClaimsEmptyStateDescription: 'Es wurden keine Ansprüche gefunden.',
	ClainInsuranceTelephone: 'Versicherungstelefon (mit Vorwahl)',
	Classic: 'Klassisch',
	Clear: 'Klar',
	ClearAll: 'Alles löschen',
	ClearSearchFilter: 'Klar',
	ClearingHouse: 'Hausreinigung',
	ClearingHouseClaimId: 'Claim.MD-ID',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Clearing-Haus-Referenz',
	ClearingHouseUnavailableError:
		'Die Clearing House ist derzeit nicht verfügbar. Bitte versuchen Sie es später erneut.',
	ClickToUpload: 'Zum Hochladen klicken',
	Client: 'Klient',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} hat {noteTitle, select, undefined { eine Notiz } other {{noteTitle}}} hinzugefügt',
	ClientAndRelationshipSelectorPlaceholder: 'Wählen Sie Kunden und ihre Beziehungen aus',
	ClientAndRelationshipSelectorTitle: 'Alle Kunden und ihre Beziehungen',
	ClientAndRelationshipSelectorTitle1: 'Alle Beziehungen von ‚{name}‘',
	ClientAppCallsPageNoOptionsText:
		'Wenn Sie auf einen Videoanruf warten, wird dieser in Kürze hier angezeigt. Bei Problemen wenden Sie sich bitte an die Person, die den Anruf initiiert hat.',
	ClientAppSubHeaderMyDocumentation: 'Meine Dokumentation',
	ClientAppointment: 'Kundentermin',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} hat {appointmentName} gebucht',
	ClientAppointmentsEmptyStateDescription: 'Es wurden keine Termine gefunden',
	ClientAppointmentsEmptyStateTitle:
		'Behalten Sie den Überblick über die anstehenden und vergangenen Termine Ihrer Kunden und deren Teilnahme',
	ClientArchivedSuccessfulSnackbar: 'Erfolgreich archiviert <b>{name}</b>',
	ClientBalance: 'Kundenguthaben',
	ClientBilling: 'Abrechnung',
	ClientBillingAddPaymentMethodDescription:
		'Fügen Sie die Zahlungsmethoden Ihrer Kunden hinzu und verwalten Sie sie, um deren Rechnungsstellungs- und Abrechnungsprozess zu optimieren.',
	ClientBillingAndPaymentDueDate: 'Fälligkeitsdatum',
	ClientBillingAndPaymentHistory: 'Rechnungs- und Zahlungsverlauf',
	ClientBillingAndPaymentInvoices: 'Rechnungen',
	ClientBillingAndPaymentIssueDate: 'Ausgabedatum',
	ClientBillingAndPaymentPrice: 'Preis',
	ClientBillingAndPaymentReceipt: 'Quittung',
	ClientBillingAndPaymentServices: 'Dienstleistungen',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Team {count, plural, one {Mitglied} other {Mitglieder}} zugewiesen!',
	ClientBulkStaffUnassignedSuccessSnackbar:
		'Team {count, plural, one {Mitglied} other {Mitglieder}} nicht zugewiesen!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tags hinzugefügt!',
	ClientDuplicatesDeviewDescription:
		'Führen Sie mehrere Kundendatensätze zu einem zusammen, um alle Daten – Notizen, Dokumente, Termine, Rechnungen und Gespräche – zu vereinheitlichen.',
	ClientDuplicatesPageMergeHeader: 'Wählen Sie die Daten aus, die Sie behalten möchten',
	ClientDuplicatesReviewHeader: 'Vergleichen potenzieller doppelter Datensätze zum Zusammenführen',
	ClientEmailChangeWarningDescription:
		'Durch die Aktualisierung der E-Mail-Adresse des Kunden wird dessen Zugriff auf alle freigegebenen Dokumente entfernt und der Benutzer mit der neuen E-Mail erhält Zugriff',
	ClientFieldDateDescription: 'Datum formatieren',
	ClientFieldDateLabel: 'Datum',
	ClientFieldDateRangeDescription: 'Eine Reihe von Terminen',
	ClientFieldDateRangeLabel: 'Datumsbereich',
	ClientFieldDateShowDateDescription: 'zB 29 Jahre',
	ClientFieldDateShowDateRangeDescription: 'zB 2 Wochen',
	ClientFieldEmailDescription: 'E-Mail-Adresse',
	ClientFieldEmailLabel: 'Email',
	ClientFieldLabel: 'Feldbezeichnung',
	ClientFieldLinearScaleDescription: 'Skalenoptionen 1-10',
	ClientFieldLinearScaleLabel: 'Lineare Skalierung',
	ClientFieldLocationDescription: 'Physische oder Postadresse',
	ClientFieldLocationLabel: 'Standort',
	ClientFieldLongTextDescription: 'Langer Textbereich',
	ClientFieldLongTextLabel: 'Absatz',
	ClientFieldMultipleChoiceDropdownDescription: 'Wählen Sie mehrere Optionen aus der Liste',
	ClientFieldMultipleChoiceDropdownLabel: 'Dropdown-Liste mit Mehrfachauswahl',
	ClientFieldPhoneNumberDescription: 'Telefonnummer',
	ClientFieldPhoneNumberLabel: 'Telefon',
	ClientFieldPlaceholder: 'Wählen Sie einen Clientfeldtyp',
	ClientFieldSingleChoiceDropdownDescription: 'Wählen Sie nur eine Option aus der Liste',
	ClientFieldSingleChoiceDropdownLabel: 'Dropdown-Liste mit Einzelauswahl',
	ClientFieldTextDescription: 'Texteingabefeld',
	ClientFieldTextLabel: 'Text',
	ClientFieldYesOrNoDescription: 'Wählen Sie zwischen den Optionen „Ja“ oder „Nein“',
	ClientFieldYesOrNoLabel: 'Ja | Nein',
	ClientFileFormAccessLevelDescription:
		'Sie und das Team haben immer Zugriff auf die Dateien, die Sie hochladen. Sie können diese Datei mit dem Kunden und/oder seinen Beziehungen teilen.',
	ClientFileSavedSuccessSnackbar: 'Datei gespeichert!',
	ClientFilesPageEmptyStateText: 'Keine Dateien hochgeladen',
	ClientFilesPageUploadFileButton: 'Daten hochladen',
	ClientHeaderBilling: 'Abrechnung',
	ClientHeaderBillingAndReceipts: 'Abrechnung ',
	ClientHeaderDocumentation: 'Dokumentation',
	ClientHeaderDocuments: 'Unterlagen',
	ClientHeaderFile: 'Dokumentieren',
	ClientHeaderHistory: 'Krankengeschichte',
	ClientHeaderInbox: 'Posteingang',
	ClientHeaderNote: 'Notiz',
	ClientHeaderOverview: 'Überblick',
	ClientHeaderProfile: 'persönlich',
	ClientHeaderRelationship: 'Beziehung',
	ClientHeaderRelationships: 'Beziehungen',
	ClientId: 'Kunden-ID',
	ClientImportProcessingDescription:
		'Datei wird noch verarbeitet. Wir benachrichtigen Sie, wenn dies abgeschlossen ist.',
	ClientImportReadyForMappingDescription:
		'Wir haben die Vorverarbeitung Ihrer Datei abgeschlossen. Möchten Sie Spalten zuordnen, um diesen Import abzuschließen?',
	ClientImportReadyForMappingNotificationSubject:
		'Client Import Pre-Processing ist abgeschlossen. Die Datei ist jetzt bereit für die Zuordnung.',
	ClientInAppMessaging: 'In-App-Messaging für den Client',
	ClientInfoAddField: 'Ein weiteres Feld hinzufügen',
	ClientInfoAddRow: 'Zeile hinzufügen',
	ClientInfoAlertMessage:
		'Alle in diesem Abschnitt eingetragenen Informationen werden in den Kundendatensatz eingetragen.',
	ClientInfoFormPrimaryText: 'Kundeninformation',
	ClientInfoFormSecondaryText: 'Kontaktdaten erfassen',
	ClientInfoPlaceholder: `Kundenname, E-Mail-Adresse, Telefonnummer
 Physikalische Adresse,
 Geburtsdatum`,
	ClientInformation: 'Kundeninformation',
	ClientInsuranceTabLabel: 'Versicherung',
	ClientIntakeFormsNotSupported: `Formularvorlagen werden derzeit durch Client-Intakes nicht unterstützt.
 Erstellen und teilen Sie sie stattdessen als Kundennotizen.`,
	ClientIntakeModalDescription:
		'Ihr Kunde erhält eine Aufnahme-E-Mail mit der Aufforderung, sein Profil zu vervollständigen und relevante medizinische oder Überweisungsdokumente hochzuladen. Er erhält Zugriff auf das Kundenportal.',
	ClientIntakeModalTitle: 'Sende Intake an {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Erfolg! Ihre Aufnahme wurde gespeichert.',
	ClientIntakeSuccessSnackbar: 'Erfolg! Ihre Aufnahme wurde gespeichert und eine Bestätigungs-E-Mail gesendet.',
	ClientIsChargedProcessingFee: 'Ihre Kunden bezahlen die Bearbeitungsgebühr',
	ClientListCreateButton: 'Neuer Kunde',
	ClientListEmptyState: 'Keine Kunden hinzugefügt',
	ClientListPageItemArchive: 'Client entfernen',
	ClientListPageItemRemoveAccess: 'Meinen Zugriff entfernen',
	ClientLocalizationPanelDescription: 'Die bevorzugte Sprache und Zeitzone des Kunden.',
	ClientLocalizationPanelTitle: 'Sprache und Zeitzone',
	ClientManagementAndEHR: 'Client-Management ',
	ClientMergeResultSummaryBanner:
		'Das Zusammenführen von Datensätzen konsolidiert alle Kundendaten, einschließlich Notizen, Dokumente, Termine, Rechnungen und Konversationen. Überprüfen Sie die Richtigkeit, bevor Sie fortfahren.',
	ClientMergeResultSummaryTitle: 'Zusammenfassung des Merge-Ergebnisses',
	ClientModalTitle: 'Neuer Kunde',
	ClientMustHaveEmaillAccessErrorText: 'Kunden/Kontakte ohne E-Mail',
	ClientMustHavePortalAccessErrorText: 'Kunden/Kontakte müssen sich anmelden',
	ClientMustHaveZoomAppConnectedErrorText: 'Verbinden Sie Zoom über Einstellungen &gt; Verbundene Apps',
	ClientNameFormat: 'Client-Namensformat',
	ClientNotFormAccessLevel: 'Sichtbar für:',
	ClientNotFormAccessLevelDescription:
		'Sie und das Team haben immer Zugriff auf die von Ihnen veröffentlichten Notizen. Sie können diese Notiz mit dem Kunden und/oder seinen Beziehungen teilen.',
	ClientNotRegistered: 'Nicht registriert',
	ClientNoteFormAddFileButton: 'Dateien anhängen',
	ClientNoteFormChooseAClient: 'Wählen Sie einen Kunden/Kontakt aus, um fortzufahren',
	ClientNoteFormContent: 'Inhalt',
	ClientNoteItemDeleteConfirmationModalDescription: 'Nach dem Löschen können Sie diese Notiz nicht mehr abrufen.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Notiz veröffentlicht und gesperrt.',
	ClientNotePublishedSuccessSnackbar: 'Hinweis veröffentlicht!',
	ClientNotes: 'Kundennotizen',
	ClientNotesEmptyStateText:
		'Um Notizen hinzuzufügen, gehen Sie zum Profil eines Kunden und klicken Sie auf die Registerkarte „Notizen“.',
	ClientOnboardingChoosePasswordTitle1: 'Fast fertig!',
	ClientOnboardingChoosePasswordTitle2: 'Wählen Sie ein Passwort',
	ClientOnboardingCompleteIntake: 'Vollständige Aufnahme',
	ClientOnboardingConfirmationScreenText:
		'Sie haben alle Informationen bereitgestellt, die {providerName} benötigt.	Bestätigen Sie Ihre E-Mail-Adresse, um mit Ihrer Onboarding-Phase zu beginnen. Sollten Sie die E-Mail nicht sofort erhalten, überprüfen Sie bitte Ihren Spam-Ordner.',
	ClientOnboardingConfirmationScreenTitle: 'Großartig! Überprüfen Sie Ihren Posteingang.',
	ClientOnboardingDashboardButton: 'Zum Dashboard',
	ClientOnboardingHealthRecordsDesc1: 'Möchten Sie Empfehlungsbriefe, Dokumente mit {providerName} teilen?',
	ClientOnboardingHealthRecordsDescription: 'Beschreibung hinzufügen (optional)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentation',
	ClientOnboardingPasswordRequirements: 'Anforderungen',
	ClientOnboardingPasswordRequirementsConditions1: 'Mindestens 9 Zeichen erforderlich',
	ClientOnboardingProviderIntroSignupButton: 'Melde dich für mich an',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Für ein Familienmitglied anmelden',
	ClientOnboardingProviderIntroTitle: '{name} hat Sie eingeladen, ihrer Carepatron-Plattform beizutreten',
	ClientOnboardingRegistrationInstructions: 'Geben Sie unten Ihre persönlichen Daten ein.',
	ClientOnboardingRegistrationTitle: 'Zunächst benötigen wir einige persönliche Daten',
	ClientOnboardingStepFormsAndAgreements: 'Formulare und Vereinbarungen',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Bitte füllen Sie die folgenden Formulare für den {providerName} Aufnahmeprozess aus.',
	ClientOnboardingStepHealthDetails: 'Gesundheitsdetails',
	ClientOnboardingStepPassword: 'Passwort',
	ClientOnboardingStepYourDetails: 'Deine Details',
	ClientPaymentMethodDescription:
		'Speichern Sie eine Zahlungsmethode in Ihrem Profil, um Ihre nächste Terminbuchung und Rechnungsstellung schneller und sicherer zu machen.',
	ClientPortal: 'Kundenportal',
	ClientPortalDashboardEmptyDescription: 'Ihr Terminverlauf und Ihre Teilnahme werden hier angezeigt.',
	ClientPortalDashboardEmptyTitle:
		'Behalten Sie den Überblick über alle anstehenden, angefragten und vergangenen Termine sowie Ihre Anwesenheit',
	ClientPreferredNotificationPanelDescription:
		'Verwalten Sie die bevorzugte Methode Ihres Kunden zum Empfangen von Updates und Benachrichtigungen über:',
	ClientPreferredNotificationPanelTitle: 'Bevorzugte Benachrichtigungsmethode',
	ClientProcessingFee: 'Die Zahlung beinhaltet eine Bearbeitungsgebühr von ({currencyCode}) {amount}',
	ClientProfileAddress: 'Adresse',
	ClientProfileDOB: 'Geburtsdatum',
	ClientProfileEmailHelperText: 'Durch das Hinzufügen einer E-Mail wird der Portalzugriff gewährt',
	ClientProfileEmailHelperTextMoreInfo:
		'Wenn Sie dem Kunden Zugriff auf das Portal gewähren, können Teammitglieder Notizen, Dateien und andere Dokumente teilen',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Identifikationsnummer',
	ClientRelationshipsAddClientOwnerButton: 'Laden Sie den Kunden ein',
	ClientRelationshipsAddFamilyButton: 'Familienmitglied einladen',
	ClientRelationshipsAddStaffButton: 'Mitarbeiterzugriff hinzufügen',
	ClientRelationshipsEmptyStateText: 'Keine Beziehungen hinzugefügt',
	ClientRemovedSuccessSnackbar: 'Client erfolgreich entfernt.',
	ClientResponsibility: 'Kundenverantwortung',
	ClientSavedSuccessSnackbar: 'Client erfolgreich gespeichert.',
	ClientTableClientName: 'Kundenname',
	ClientTablePhone: 'Telefon',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Erfolgreich aus dem Archiv entfernt <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} hat {noteTitle, select, undefined { eine Notiz } other {{noteTitle}}} bearbeitet',
	ClientView: 'Clientansicht',
	Clients: 'Kunden',
	ClientsTable: 'Kundentabelle',
	ClinicalFormat: 'Klinisches Format',
	ClinicalPsychologist: 'Klinischer Psychologe',
	Close: 'Schließen',
	CloseImportClientsModal: 'Möchten Sie den Clientimport wirklich abbrechen?',
	CloseReactions: 'Enge Reaktionen',
	Closed: 'Geschlossen',
	Coaching: 'Coaching',
	Code: 'Code',
	CodeErrorMessage: 'Code ist erforderlich',
	CodePlaceholder: 'Code',
	Coinsurance: 'Mitversicherung',
	Collection: 'Sammlung',
	CollectionName: 'Sammlungsname',
	Collections: 'Sammlungen',
	ColorAppointmentsBy: 'Farbtermine von',
	ColorTheme: 'Farbthema',
	ColourCalendarBy: 'Farbkalender von',
	ComingSoon: 'Demnächst',
	Community: 'Gemeinschaft',
	CommunityHealthLead: 'Leiter der Gesundheitsfürsorge in der Gemeinde',
	CommunityHealthWorker: 'Gemeindegesundheitsarbeiter',
	CommunityTemplatesSectionDescription: 'Erstellt von der Carepatron-Community',
	CommunityTemplatesSectionTitle: 'Gemeinschaft',
	CommunityUser: 'Community-Benutzer',
	Complete: 'Vollständig',
	CompleteAndLock: 'Abschließen und sperren',
	CompleteSetup: 'Vollständige Einrichtung',
	CompleteSetupSuccessDescription:
		'Sie haben einige wichtige Schritte in Richtung der Beherrschung von Carepatron abgeschlossen.',
	CompleteSetupSuccessDescription2:
		'Entdecken Sie mehr Möglichkeiten, um Ihre Praxis zu optimieren und Ihre Kunden zu unterstützen.',
	CompleteSetupSuccessTitle: 'Erfolg! Du machst das toll!',
	CompleteStripeSetup: 'Schließen Sie die Stripe-Einrichtung ab',
	Completed: 'Abgeschlossen',
	ComposeSms: 'SMS verfassen',
	ComputerSystemsAnalyst: 'Computersystemanalytiker',
	Confirm: 'Bestätigen',
	ConfirmDeleteAccountDescription:
		'Sie sind dabei, Ihr Konto zu löschen. Diese Aktion kann nicht rückgängig gemacht werden. Wenn Sie fortfahren möchten, bestätigen Sie bitte unten.',
	ConfirmDeleteActionDescription:
		'Möchten Sie diese Aktion wirklich löschen? Dies kann nicht rückgängig gemacht werden.',
	ConfirmDeleteAutomationDescription:
		'Möchten Sie diese Automatisierung wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.',
	ConfirmDeleteScheduleDescription:
		'Das Löschen des <strong>{scheduleName}</strong>-Plans entfernt ihn aus Ihren Plänen und kann Ihre verfügbaren Online-Dienste ändern. Diese Aktion kann nicht rückgängig gemacht werden.',
	ConfirmDraftResponseContinue: 'Weiter mit der Antwort',
	ConfirmDraftResponseDescription:
		'Wenn Sie diese Seite schließen, bleibt Ihre Antwort als Entwurf erhalten. Sie können jederzeit zurückkommen und fortfahren.',
	ConfirmDraftResponseSubmitResponse: 'Antwort abschicken',
	ConfirmDraftResponseTitle: 'Ihre Antwort wurde nicht übermittelt',
	ConfirmIfUserIsClientDescription: `Das von Ihnen ausgefüllte Anmeldeformular ist für Anbieter (d. h. Gesundheitsteams/-organisationen).
 Wenn dies ein Fehler ist, können Sie „Als Kunde fortfahren“ wählen und wir richten Ihr Kundenportal für Sie ein.`,
	ConfirmIfUserIsClientNoButton: 'Als Anbieter anmelden',
	ConfirmIfUserIsClientTitle: 'Es sieht so aus, als wären Sie ein Kunde',
	ConfirmIfUserIsClientYesButton: 'Als Kunde fortfahren',
	ConfirmKeepSeparate: 'Getrennt halten bestätigen',
	ConfirmMerge: 'Bestätigen Sie die Zusammenführung',
	ConfirmPassword: 'Bestätige das Passwort',
	ConfirmRevertClaim: 'Ja, Status zurücksetzen',
	ConfirmSignupAccessCode: 'Bestätigungscode',
	ConfirmSignupButtom: 'Bestätigen',
	ConfirmSignupDescription:
		'Bitte geben Sie Ihre E-Mail-Adresse und den Bestätigungscode ein, den wir Ihnen gerade gesendet haben.',
	ConfirmSignupSubTitle: 'Überprüfen Sie den Spam-Ordner - wenn die E-Mail nicht angekommen ist',
	ConfirmSignupSuccessSnackbar:
		'Super, wir haben Ihr Konto bestätigt! Jetzt können Sie sich mit Ihrer E-Mail-Adresse und Ihrem Passwort anmelden',
	ConfirmSignupTitle: 'Konto bestätigen',
	ConfirmSignupUsername: 'Email',
	ConfirmSubscriptionUpdate: 'Abonnement bestätigen {price} {isMonthly, select, true {pro Monat} other {pro Jahr}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Sobald die Kunden gelöscht sind, können Sie nicht mehr auf ihre Informationen zugreifen.',
	ConfirmationModalBulkDeleteClientsTitleId: '{count, plural, one {# Kunden} other {# Kunden}} löschen?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Sobald die Kontakte gelöscht sind, können Sie nicht mehr auf ihre Informationen zugreifen.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Löschen {count, plural, one {# Kontakt} other {# Kontakte}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Dies ist eine dauerhafte Aktion. Sobald die Teammitglieder gelöscht sind, können Sie nicht mehr auf ihre Informationen zugreifen.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'Löschen {count, plural, one {# Teammitglied} other {# Teammitglieder}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Durch Schließen dieser Notiz werden alle laufenden Transkriptionen beendet. Möchten Sie wirklich fortfahren?',
	ConfirmationModalDeleteClientField:
		'Dies ist eine dauerhafte Aktion. Sobald das Feld gelöscht ist, ist es auf Ihren verbleibenden Clients nicht mehr zugänglich.',
	ConfirmationModalDeleteSectionMessage:
		'Nach dem Löschen werden alle Fragen in diesem Abschnitt entfernt. Diese Aktion kann nicht rückgängig gemacht werden.',
	ConfirmationModalDeleteService:
		'Dies ist eine dauerhafte Aktion. Sobald der Dienst gelöscht ist, ist er in Ihrem Arbeitsbereich nicht mehr zugänglich.',
	ConfirmationModalDeleteServiceGroup:
		'Durch das Löschen einer Sammlung werden alle Dienste aus der Gruppe entfernt und Sie kehren zu Ihrer Dienstliste zurück. Diese Aktion kann nicht rückgängig gemacht werden.',
	ConfirmationModalDeleteTranscript: 'Möchten Sie das Transkript wirklich löschen?',
	ConfirmationModalDescriptionDeleteClient:
		'Sobald der Kunde gelöscht ist, können Sie nicht mehr auf die Kundeninformationen zugreifen.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Sobald Sie Ihren Zugriff entfernen, können Sie die Kundeninformationen nicht mehr anzeigen.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Ihr Profil wird nicht gelöscht, sondern nur als Beziehung zu diesem Kunden entfernt.',
	ConfirmationModalDescriptionRemoveStaff: 'Möchten Sie diese Person wirklich vom Anbieter entfernen?',
	ConfirmationModalEndSession: 'Möchten Sie die Sitzung wirklich beenden?',
	ConfirmationModalTitle: 'Bist du sicher?',
	Confirmed: 'Bestätigt',
	ConflictTimezoneWarningMessage: 'Aufgrund mehrerer Zeitzonen können Konflikte auftreten',
	Connect: 'Verbinden',
	ConnectExistingClientOrContact: 'Neuen Kunden/Kontakt anlegen',
	ConnectInboxGoogleDescription: 'Fügen Sie ein Gmail-Konto oder eine Google-Gruppenliste hinzu',
	ConnectInboxMicrosoftDescription: 'Fügen Sie ein Outlook-, Office365- oder Exchange-Konto hinzu',
	ConnectInboxModalDescription:
		'Verbinden Sie Ihre Apps, um alle Ihre Nachrichten nahtlos an einem zentralen Ort zu senden, zu empfangen und zu verfolgen.',
	ConnectInboxModalExistingDescription:
		'Verwenden Sie eine vorhandene Verbindung aus den Einstellungen Ihrer verbundenen Apps, um den Konfigurationsprozess zu optimieren.',
	ConnectInboxModalExistingTitle: 'Vorhandene verbundene App in Carepatron',
	ConnectInboxModalTitle: 'Posteingang verbinden',
	ConnectToStripe: 'Mit Stripe verbinden',
	ConnectZoom: 'Zoom verbinden',
	ConnectZoomModalDescription: 'Erlauben Sie Carepatron, Videoanrufe für Ihre Termine zu verwalten.',
	ConnectedAppDisconnectedNotificationSubject:
		'Wir haben die Verbindung zum {account}-Konto verloren. Bitte verbinde dich erneut.',
	ConnectedAppSyncDescription:
		'Verwalten Sie verbundene Apps, um Ereignisse in Kalendern von Drittanbietern direkt von Carepatron aus zu erstellen.',
	ConnectedApps: 'Verbundene Apps',
	ConnectedAppsGMailDescription: 'Gmail-Konten oder Google-Gruppenlisten hinzufügen',
	ConnectedAppsGoogleCalendarDescription: 'Kalenderkonten oder Google-Gruppenliste hinzufügen',
	ConnectedAppsGoogleDescription: 'Fügen Sie Ihr Gmail-Konto hinzu und synchronisieren Sie Google-Kalender',
	ConnectedAppsMicrosoftDescription: 'Fügen Sie ein Outlook-, Office365- oder Exchange-Konto hinzu',
	ConnectedCalendars: 'Vernetzte Kalender',
	ConsentDocumentation: 'Formulare und Vereinbarungen',
	ConsentDocumentationPublicTemplateError:
		'Aus Sicherheitsgründen können Sie nur Vorlagen aus Ihrem Team (nicht öffentlich) auswählen.',
	ConstructionWorker: 'Bauarbeiter',
	Consultant: 'Berater',
	Contact: 'Kontakt',
	ContactAccessTypeHelperText: 'Ermöglicht Familienadministratoren, Informationen zu aktualisieren',
	ContactAccessTypeHelperTextMoreInfo:
		'Dies ermöglicht es Ihnen, Notizen/Dokumente über {clientFirstName} zu teilen.',
	ContactAddressLabelBilling: 'Abrechnung',
	ContactAddressLabelHome: 'Heim',
	ContactAddressLabelOthers: 'Sonstiges',
	ContactAddressLabelWork: 'Arbeiten',
	ContactChangeConfirmation:
		'Das Ändern des Rechnungskontakts entfernt alle Positionen, die mit <mark>{contactName}</mark> zusammenhängen',
	ContactDetails: 'Kontaktdetails',
	ContactEmailLabelOthers: 'Sonstiges',
	ContactEmailLabelPersonal: 'Persönlich',
	ContactEmailLabelSchool: 'Schule',
	ContactEmailLabelWork: 'Arbeiten',
	ContactInformation: 'Kontaktinformationen',
	ContactInformationText: 'Kontaktinformationen',
	ContactListCreateButton: 'Neuer Kontakt',
	ContactName: 'Kontaktname',
	ContactPhoneLabelHome: 'Heim',
	ContactPhoneLabelMobile: 'Mobile',
	ContactPhoneLabelSchool: 'Schule',
	ContactPhoneLabelWork: 'Arbeiten',
	ContactRelationship: 'Kontaktbeziehung',
	ContactRelationshipFormAccessType: 'Zugriff auf geteilte Informationen gewähren',
	ContactRelationshipGrantAccessInfo: 'Dies ermöglicht es Ihnen, Notizen und Dokumente zu teilen',
	ContactSupport: 'Support kontaktieren',
	Contacts: 'Kontakte',
	ContainerIdNotSet: 'Container-ID nicht festgelegt',
	Contemporary: 'Zeitgenössisch',
	Continue: 'Weitermachen',
	ContinueDictating: 'Weiter diktieren',
	ContinueEditing: 'Weiter bearbeiten',
	ContinueImport: 'Import fortsetzen',
	ContinueTranscription: 'Transkription fortsetzen',
	ContinueWithApple: 'Weiter mit Apple',
	ContinueWithGoogle: 'Weiter mit Google',
	Conversation: 'Gespräch',
	Copay: 'Zuzahlung',
	CopayOrCoinsurance: 'Zuzahlung oder Kostenbeteiligung',
	Copayment: 'Kostenbeteiligung',
	CopiedToClipboard: 'In die Zwischenablage kopiert',
	Copy: 'Kopieren',
	CopyAddressSuccessSnackbar: 'Kopierte Adresse in die Zwischenablage',
	CopyCode: 'Kopieren Sie den Code',
	CopyCodeToClipboardSuccess: 'Code in die Zwischenablage kopiert',
	CopyEmailAddressSuccessSnackbar: 'E-Mail-Adresse in die Zwischenablage kopiert',
	CopyLink: 'Link kopieren',
	CopyLinkForCall: 'Kopieren Sie diesen Link, um diesen Anruf zu teilen:',
	CopyLinkSuccessSnackbar: 'Link in die Zwischenablage kopiert',
	CopyMeetingLink: 'Meeting-Link kopieren',
	CopyPaymentLink: 'Zahlungslink kopieren',
	CopyPhoneNumberSuccessSnackbar: 'Telefonnummer in die Zwischenablage kopiert',
	CopyTemplateLink: 'Link zur Vorlage kopieren',
	CopyTemplateLinkSuccess: 'Link in die Zwischenablage kopiert',
	CopyToClipboardError: 'Konnte nicht in die Zwischenablage kopiert werden. Bitte versuchen Sie es erneut.',
	CopyToTeamTemplates: 'In Team-Vorlagen kopieren',
	CopyToWorkspace: 'In den Arbeitsbereich kopieren',
	Cosmetologist: 'Kosmetikerin',
	Cost: 'Kosten',
	CostErrorMessage: 'Kosten sind erforderlich',
	Counseling: 'Beratung',
	Counselor: 'Berater',
	Counselors: 'Berater',
	CountInvoicesAdded: '{count, plural, one {# Rechnung hinzugefügt} other {# Rechnungen hinzugefügt}}',
	CountNotesAdded: '{count, plural, one {# Notiz hinzugefügt} other {# Notizen hinzugefügt}}',
	CountSelected: '{count} ausgewählt',
	CountTimes: '{count} Mal',
	Country: 'Land',
	Cousin: 'Cousin',
	CoverageType: 'Abdeckungsart',
	Covered: 'Bedeckt',
	Create: 'Erstellen',
	CreateANewClient: 'Neuen Kunden anlegen',
	CreateAccount: 'Benutzerkonto erstellen',
	CreateAndSignNotes: 'Notizen mit Kunden erstellen und unterzeichnen',
	CreateAvailabilityScheduleFailure: 'Neuer Verfügbarkeitsplan konnte nicht erstellt werden',
	CreateAvailabilityScheduleSuccess: 'Neuer Verfügbarkeitsplan erfolgreich erstellt',
	CreateBillingItems: 'Rechnungspositionen erstellen',
	CreateCallFormButton: 'Anruf starten',
	CreateCallFormInviteOnly: 'Nur geladene Gäste',
	CreateCallFormInviteOnlyMoreInfo:
		'Nur Personen, die zu diesem Anruf eingeladen wurden, können teilnehmen. Um diesen Anruf mit anderen zu teilen, deaktivieren Sie einfach dieses Kontrollkästchen und kopieren/fügen Sie den Link auf der nächsten Seite ein',
	CreateCallFormRecipients: 'Empfänger',
	CreateCallFormRegion: 'Gastgeberregion',
	CreateCallModalAddClientContactSelectorLabel: 'Kundenkontakte',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Suche nach Kundennamen',
	CreateCallModalAddStaffSelectorLabel: 'Teammitglieder (optional)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Suche nach Mitarbeiternamen',
	CreateCallModalDescription:
		'Starten Sie einen Anruf und laden Sie Mitarbeiter und/oder Kontakte ein. Alternativ können Sie das Kontrollkästchen „Privat“ deaktivieren, um diesen Anruf für alle mit Carepatron freigeben zu können.',
	CreateCallModalTitle: 'Einen Anruf starten',
	CreateCallModalTitleLabel: 'Titel (optional)',
	CreateCallNoPersonIdToolTip: 'Nur Kontakte/Kunden mit Portalzugriff können an Anrufen teilnehmen',
	CreateClaim: 'Schaden melden',
	CreateClaimCompletedMessage: 'Ihr Anspruch wurde erstellt.',
	CreateClientModalTitle: 'Neuer Kunde',
	CreateContactModalTitle: 'Neuer Kontakt',
	CreateContactRelationshipButton: 'Beziehung hinzufügen',
	CreateContactSelectorDefaultOption: '  Kontakt erstellen',
	CreateContactWithRelationshipFormAccessType: 'Zugriff auf freigegebene Informationen gewähren ',
	CreateDocumentDnDPrompt: 'Ziehen und Ablegen zum Hochladen von Dateien',
	CreateDocumentSizeLimit: 'Dateigrößenbegrenzung pro Datei {size}MB. Insgesamt {total} Dateien.',
	CreateFreeAccount: 'Kostenlosen Account erstellen',
	CreateInvoice: 'Rechnung erstellen',
	CreateLink: 'Verknüpfung erstellen',
	CreateNew: 'Erstelle neu',
	CreateNewAppointment: 'Neuen Termin anlegen',
	CreateNewClaim: 'Neuen Anspruch erstellen',
	CreateNewClaimForAClient: 'Neuen Anspruch für einen Kunden erstellen.',
	CreateNewClient: 'Neuen Kunden anlegen',
	CreateNewConnection: 'Neue Verbindung',
	CreateNewContact: 'Neuen Kontakt anlegen',
	CreateNewField: 'Neues Feld erstellen',
	CreateNewLocation: 'Neuen Ort',
	CreateNewService: 'Neuen Dienst anlegen',
	CreateNewServiceGroupFailure: 'Neue Sammlung konnte nicht erstellt werden',
	CreateNewServiceGroupMenu: 'Neue Kollektion',
	CreateNewServiceGroupSuccess: 'Neue Kollektion erfolgreich erstellt',
	CreateNewServiceMenu: 'Neuer Service',
	CreateNewTeamMember: 'Neues Teammitglied anlegen',
	CreateNewTemplate: 'Neue Vorlage',
	CreateNote: 'Notiz erstellen',
	CreateSuperbillReceipt: 'Neuer Supergesetzentwurf',
	CreateSuperbillReceiptSuccess: 'Superbill-Quittung erfolgreich erstellt',
	CreateTemplateFolderSuccessMessage: 'Erfolgreich erstellt {folderTitle}',
	Created: 'Erstellt',
	CreatedAt: 'Erstellt {timestamp}',
	Credit: 'Guthaben',
	CreditAdded: 'Kredit angewendet',
	CreditAdjustment: 'Kreditanpassung',
	CreditAdjustmentReasonHelperText: 'Dies ist eine interne Notiz und für Ihren Kunden nicht sichtbar.',
	CreditAdjustmentReasonPlaceholder:
		'Das Hinzufügen eines Anpassungsgrunds kann bei der Überprüfung abrechenbarer Transaktionen hilfreich sein',
	CreditAmount: '{amount} GS',
	CreditBalance: 'Guthaben',
	CreditCard: 'Kreditkarte',
	CreditCardExpire: 'Läuft ab {exp_month}/{exp_year}',
	CreditCardNumber: 'Kreditkartennummer',
	CreditDebitCard: 'Karte',
	CreditIssued: 'Kredit ausgestellt',
	CreditsUsed: 'Verwendete Credits',
	Crop: 'Ernte',
	Currency: 'Währung',
	CurrentCredit: 'Aktueller Kredit',
	CurrentEventTime: 'Aktuelle Ereigniszeit',
	CurrentPlan: 'Aktueller Plan',
	Custom: 'Brauch',
	CustomRange: 'Benutzerdefinierten Bereich',
	CustomRate: 'Benutzerdefinierter Tarif',
	CustomRecurrence: 'Individuelle Wiederholung',
	CustomServiceAvailability: 'Serviceverfügbarkeit',
	CustomerBalance: 'Kundenguthaben',
	CustomerName: 'Kundenname',
	CustomerNameIsRequired: 'Kundenname ist erforderlich',
	CustomerServiceRepresentative: 'Kundendienstvertretung',
	CustomiseAppointments: 'Termine anpassen',
	CustomiseBookingLink: 'Buchungsoptionen anpassen',
	CustomiseBookingLinkServicesInfo: 'Kunden können nur buchbare Leistungen wählen',
	CustomiseBookingLinkServicesLabel: 'Dienstleistungen',
	CustomiseClientRecordsAndWorkspace: 'Passen Sie Ihre Kundendatensätze und Ihren Arbeitsbereich an',
	CustomiseClientSettings: 'Anpassen der Clienteinstellungen',
	Customize: 'Anpassen',
	CustomizeAppearance: 'Anpassen des Erscheinungsbilds',
	CustomizeAppearanceDesc:
		'Passen Sie das Erscheinungsbild Ihrer Online-Buchung an Ihre Marke an und optimieren Sie die Anzeige Ihrer Dienste für Kunden.',
	CustomizeClientFields: 'Kundenfelder anpassen',
	CustomizeInvoiceTemplate: 'Rechnungsvorlage anpassen',
	CustomizeInvoiceTemplateDescription:
		'Erstellen Sie mühelos professionelle Rechnungen, die Ihre Marke widerspiegeln.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX ist erforderlich',
	Daily: 'Täglich',
	DanceTherapist: 'Tanztherapeutin',
	DangerZone: 'Gefahrenzone',
	Dashboard: 'Armaturenbrett',
	Date: 'Datum',
	DateAndTime: 'Datum ',
	DateDue: 'Fälligkeitsdatum',
	DateErrorMessage: 'Datum ist erforderlich',
	DateFormPrimaryText: 'Datum',
	DateFormSecondaryText: 'Wählen Sie aus einem Datumswähler',
	DateIssued: 'Ausgabedatum',
	DateOfPayment: 'Zahlungsdatum',
	DateOfService: 'Datum der Zustellung',
	DateOverride: 'Datumsüberschreibung',
	DateOverrideColor: 'Datumsüberschreibungsfarbe',
	DateOverrideInfo:
		'Durch Datumsüberschreibungen können Ärzte ihre Verfügbarkeit für bestimmte Daten manuell anpassen, indem sie reguläre Zeitpläne überschreiben.',
	DateOverrideInfoBanner:
		'In diesen Zeitfenstern können nur die für diese Datumsüberschreibung angegebenen Dienste gebucht werden; andere Online-Buchungen sind nicht zulässig.',
	DateOverrides: 'Datumsüberschreibungen',
	DatePickerFormPrimaryText: 'Datum',
	DatePickerFormSecondaryText: 'Wählen Sie ein Datum',
	DateRange: 'Datumsbereich',
	DateRangeFormPrimaryText: 'Datumsbereich',
	DateRangeFormSecondaryText: 'Wählen Sie einen Datumsbereich',
	DateReceived: 'Empfangsdatum',
	DateSpecificHours: 'Datumsspezifische Stunden',
	DateSpecificHoursDescription:
		'Fügen Sie Daten hinzu, wenn sich Ihre Verfügbarkeit von den geplanten Zeiten unterscheidet oder um einen Service an einem bestimmten Datum anzubieten.',
	DateUploaded: 'Hochgeladen {date, date, medium}',
	Dates: 'Termine',
	Daughter: 'Tochter',
	Day: 'Tag',
	DayPlural: '{count, plural, one {Tag} other {Tage}}',
	Days: 'Tage',
	DaysPlural: '{age, plural, one {# Tag} other {# Tage}}',
	DeFacto: 'De facto',
	Deactivated: 'Deaktiviert',
	Debit: 'Lastschrift',
	DecreaseIndent: 'Einzug verkleinern',
	Deductibles: 'Selbstbeteiligungen',
	Default: 'Standard',
	DefaultBillingProfile: 'Standardabrechnungsprofil',
	DefaultDescription: 'Standardbeschreibung',
	DefaultEndOfLine: 'Keine weiteren Artikel',
	DefaultInPerson: 'Kundentermine',
	DefaultInvoiceTitle: 'Standardtitel',
	DefaultNotificationSubject: 'Sie haben eine neue Benachrichtigung für {notificationType} erhalten.',
	DefaultPaymentMethod: 'Standardzahlungsmethode',
	DefaultService: 'Standarddienst',
	DefaultValue: 'Standard',
	DefaultVideo: 'E-Mail zum Videotermin mit dem Kunden',
	DefinedTemplateType: '{invoiceTemplate} Vorlage',
	Delete: 'Löschen',
	DeleteAccountButton: 'Konto löschen',
	DeleteAccountDescription: 'Löschen Sie Ihr Konto von der Plattform',
	DeleteAccountPanelInfoAlert:
		'Sie müssen Ihre Arbeitsbereiche löschen, bevor Sie Ihr Profil löschen. Um fortzufahren, wechseln Sie zu einem Arbeitsbereich und wählen Sie „Einstellungen“ > „Arbeitsbereichseinstellungen“.',
	DeleteAccountTitle: 'Konto löschen',
	DeleteAppointment: 'Termin löschen',
	DeleteAppointmentDescription: 'Möchten Sie diesen Termin wirklich löschen? Sie können ihn später wiederherstellen.',
	DeleteAvailabilityScheduleFailure: 'Der Verfügbarkeitsplan konnte nicht gelöscht werden.',
	DeleteAvailabilityScheduleSuccess: 'Verfügbarkeitsplan erfolgreich gelöscht',
	DeleteBillable: 'Abrechnungspflichtiges löschen',
	DeleteBillableConfirmationMessage:
		'Möchten Sie diese Rechnung wirklich löschen? Diese Aktion kann nicht rückgängig gemacht werden.',
	DeleteBillingProfileConfirmationMessage: 'Dies wird das Abrechnungsprofil dauerhaft entfernen.',
	DeleteCardConfirmation:
		'Dies ist eine dauerhafte Aktion. Sobald die Karte gelöscht ist, können Sie nicht mehr darauf zugreifen.',
	DeleteCategory: 'Kategorie löschen (dies ist nicht dauerhaft, sofern die Änderungen nicht gespeichert werden)',
	DeleteClientEventConfirmationDescription: 'Dies wird dauerhaft entfernt.',
	DeleteClients: 'Löschen von Clients',
	DeleteCollection: 'Sammlung löschen',
	DeleteColumn: 'Spalte löschen',
	DeleteConversationConfirmationDescription:
		'Diese Konversation endgültig löschen. Diese Aktion kann nicht rückgängig gemacht werden.',
	DeleteConversationConfirmationTitle: 'Konversation endgültig löschen',
	DeleteExternalEventDescription: 'Sind Sie sicher, dass Sie diesen Termin löschen möchten?',
	DeleteFileConfirmationModalPrompt: 'Nach dem Löschen können Sie diese Datei nicht wieder abrufen.',
	DeleteFolder: 'Ordner löschen',
	DeleteFolderConfirmationMessage:
		'Sind Sie sicher, dass Sie diesen Ordner {name} löschen möchten? Alle Elemente in diesem Ordner werden ebenfalls gelöscht. Sie können dies später wiederherstellen.',
	DeleteForever: 'Unwiederuflich löschen',
	DeleteInsurancePayerConfirmationMessage:
		'Durch das Entfernen von {payer} wird es aus Ihrer Liste der Versicherungszahler gelöscht. Diese Aktion ist dauerhaft und kann nicht wiederhergestellt werden.',
	DeleteInsurancePayerFailure: 'Löschen des Versicherungszahlers fehlgeschlagen',
	DeleteInsurancePolicyConfirmationMessage: 'Dadurch wird die Versicherungspolice dauerhaft gelöscht.',
	DeleteInvoiceConfirmationDescription:
		'Diese Aktion kann nicht rückgängig gemacht werden. Sie löscht die Rechnung und alle damit verbundenen Zahlungen unwiderruflich.',
	DeleteLocationConfirmation:
		'Das Löschen eines Standorts ist eine dauerhafte Aktion. Sobald Sie ihn löschen, haben Sie keinen Zugriff mehr darauf. Diese Aktion kann nicht rückgängig gemacht werden.',
	DeletePayer: 'Zahler löschen',
	DeletePracticeWorkspace: 'Übungsarbeitsbereich löschen',
	DeletePracticeWorkspaceDescription: 'Diesen Übungsarbeitsbereich dauerhaft löschen',
	DeletePracticeWorkspaceFailedSnackbar: 'Löschen des Arbeitsbereichs fehlgeschlagen',
	DeletePracticeWorkspaceModalCancelButton: 'Ja, mein Abonnement kündigen',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Bevor Sie mit der Löschung Ihres Arbeitsbereichs fortfahren, müssen Sie zuerst Ihr Abonnement kündigen.',
	DeletePracticeWorkspaceModalConfirmButton: 'Ja, Arbeitsbereich endgültig löschen',
	DeletePracticeWorkspaceModalDescription:
		'Der Arbeitsbereich von {name} wird dauerhaft gelöscht und alle Teammitglieder verlieren den Zugriff. Laden Sie alle wichtigen Daten oder Nachrichten herunter, die Sie benötigen, bevor die Löschung erfolgt. Diese Aktion kann nicht rückgängig gemacht werden.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Diese Entscheidung wurde aufgrund folgender Gründe getroffen:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Grund',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Bitte teilen Sie uns mit, warum Sie Ihr Konto löschen möchten.',
	DeletePracticeWorkspaceModalTitle: 'Bist du sicher?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Der Zugriff aller Teammitglieder wurde entfernt',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} wurde erfolgreich gelöscht',
	DeletePublicTemplateContent: 'Dadurch wird nur die öffentliche Vorlage gelöscht, nicht die Vorlage Ihres Teams.',
	DeleteRecurringAppointmentModalTitle: 'Wiederkehrenden Termin löschen',
	DeleteRecurringEventModalTitle: 'Wiederholte Besprechung löschen',
	DeleteRecurringReminderModalTitle: 'Wiederholte Erinnerung löschen',
	DeleteRecurringTaskModalTitle: 'Wiederkehrende Aufgabe löschen',
	DeleteReminderConfirmation:
		'Dies ist eine permanente Aktion. Sobald die Erinnerung gelöscht ist, können Sie nicht mehr darauf zugreifen. Wirkt sich nur auf neue Termine aus',
	DeleteSection: 'Abschnitt löschen',
	DeleteSectionInfo:
		'Das Löschen des Abschnitts <strong>{section}</strong> blendet alle darin enthaltenen Felder aus. Diese Aktion kann nicht rückgängig gemacht werden.',
	DeleteSectionWarning:
		'Kernfelder können nicht gelöscht werden und werden in den bestehenden Abschnitt <strong>{section}</strong> verschoben.',
	DeleteServiceFailure: 'Dienst konnte nicht gelöscht werden',
	DeleteServiceSuccess: 'Dienst erfolgreich gelöscht',
	DeleteStaffScheduleOverrideDescription:
		'Das Löschen dieser Datumsüberschreibung auf {value} entfernt sie aus Ihren Zeitplänen und kann Ihre online verfügbaren Dienste ändern. Diese Aktion kann nicht rückgängig gemacht werden.',
	DeleteSuperbillConfirmationDescription:
		'Diese Aktion kann nicht rückgängig gemacht werden. Die Superbill-Quittung wird dadurch dauerhaft gelöscht.',
	DeleteSuperbillFailure: 'Superbill-Beleg konnte nicht gelöscht werden',
	DeleteSuperbillSuccess: 'Superbill-Quittung erfolgreich gelöscht',
	DeleteTaxRateConfirmationDescription: 'Möchten Sie diesen Steuersatz wirklich löschen?',
	DeleteTemplateContent: 'Diese Aktion kann nicht rückgängig gemacht werden',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} erfolgreich gelöscht',
	DeleteTemplateSuccessMessage: '{templateTitle} erfolgreich gelöscht',
	DeleteTemplateTitle: 'Sind Sie sicher, dass Sie diese Vorlage löschen möchten?',
	DeleteTranscript: 'Transkript löschen',
	DeleteWorkspace: 'Arbeitsbereich löschen',
	Deleted: 'Gelöscht',
	DeletedBy: 'Gelöscht von',
	DeletedContact: 'Gelöschter Kontakt',
	DeletedOn: 'Gelöscht am',
	DeletedStatusLabel: 'Gelöschter Status',
	DeletedUserTooltip: 'Dieser Kunde wurde gelöscht',
	DeliveryMethod: 'Versandart',
	Demo: 'Demo',
	Denied: 'Bestritten',
	Dental: 'Zahnmedizin',
	DentalAssistant: 'Zahnarzthelferin',
	DentalHygienist: 'Zahnhygieniker',
	Dentist: 'Zahnarzt',
	Dentists: 'Zahnärzte',
	Description: 'Beschreibung',
	DescriptionMustNotExceed: 'Beschreibung darf {max} Zeichen nicht überschreiten',
	DetailDurationWithStaff: '{duration} Min{staffName, select, null {} other { mit {staffName}}}',
	Details: 'Einzelheiten',
	Devices: 'Geräte',
	Diagnosis: 'Diagnose',
	DiagnosisAndBillingItems: 'Diagnose ',
	DiagnosisCode: 'Diagnosecode',
	DiagnosisCodeErrorMessage: 'Ein Diagnosecode ist erforderlich',
	DiagnosisCodeSelectorPlaceholder: 'Suchen und Hinzufügen von ICD-10-Diagnosecodes',
	DiagnosisCodeSelectorTooltip:
		'Diagnosecodes werden verwendet, um Superbill-Belege für die Versicherungserstattung zu automatisieren',
	DiagnosticCodes: 'Diagnosecodes',
	Dictate: 'Diktieren',
	DictatingIn: 'Diktieren in',
	Dictation: 'Diktat',
	DidNotAttend: 'Hat nicht teilgenommen',
	DidNotComplete: 'Nicht abgeschlossen',
	DidNotProviderEnoughValue: 'Hat nicht genug Wert geboten',
	DidntProvideEnoughValue: 'Hat nicht genug Wert geboten',
	DieteticsOrNutrition: 'Diätetik oder Ernährung',
	Dietician: 'Ernährungsberaterin',
	Dieticians: 'Ernährungsberater',
	Dietitian: 'Ernährungsberaterin',
	DigitalSign: 'Hier unterschreiben:',
	DigitalSignHelp: '(Zum Zeichnen klicken/nach unten drücken)',
	DirectDebit: 'Lastschrift',
	DirectTextLink: 'Direkter Textlink',
	Disable: 'Deaktivieren',
	DisabledEmailInfo: 'Wir können Ihre E-Mail-Adresse nicht aktualisieren, da Ihr Konto nicht von uns verwaltet wird',
	Discard: 'Verwerfen',
	DiscardChanges: 'Änderungen verwerfen',
	DiscardDrafts: 'Entwürfe verwerfen',
	Disconnect: 'Trennen',
	DisconnectAppConfirmation: 'Möchten Sie die Verbindung zu dieser App trennen?',
	DisconnectAppConfirmationDescription: 'Bist du sicher, dass du diese App trennen möchtest?',
	DisconnectAppConfirmationTitle: 'App trennen',
	Discount: 'Rabatt',
	DisplayCalendar: 'Anzeige im Carepatron',
	DisplayName: 'Anzeigename',
	DisplayedToClients: 'Den Kunden angezeigt',
	DiversionalTherapist: 'Freizeittherapeut',
	DoItLater: 'Später machen',
	DoNotImport: 'Nicht importieren',
	DoNotSend: 'Nicht senden',
	DoThisLater: 'Mach das später',
	DoYouWantToEndSession: 'Möchten Sie fortfahren oder Ihre Sitzung jetzt beenden?',
	Doctor: 'Arzt',
	Doctors: 'Ärzte',
	DoesNotRepeat: 'Wiederholt sich nicht',
	DoesntWorkWellWithExistingTools: 'Funktioniert nicht gut mit unseren vorhandenen Tools oder Workflows',
	DogWalker: 'Hundeführer',
	Done: 'Erledigt',
	DontAllowClientsToCancel: 'Erlauben Sie Kunden nicht, zu stornieren',
	DontHaveAccount: 'Sie haben noch kein Konto?',
	DontSend: 'Nicht senden',
	Double: 'Doppelt',
	DowngradeTo: 'Downgrade auf {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Leider können Sie Ihren Plan nicht herabstufen, da Sie zu viele Teammitglieder haben. Bitte entfernen Sie einige von Ihrem Anbieter und versuchen Sie es erneut.',
	Download: 'Herunterladen',
	DownloadAsPdf: 'Download als PDF',
	DownloadERA: 'ERA herunterladen',
	DownloadPDF: 'PDF Herunterladen',
	DownloadTemplateFileName: 'Carepatron Switching Template.csv',
	DownloadTemplateTileDescription:
		'Verwenden Sie unsere Tabellenvorlage, um Ihre Kunden zu organisieren und hochzuladen.',
	DownloadTemplateTileLabel: 'Vorlage herunterladen',
	Downloads: '{number, plural, one {<span>#</span> Download} other {<span>#</span> Downloads}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Entwurf',
	DraftResponses: 'Antwortentwurf',
	DraftSaved: 'Gespeicherte Änderungen',
	DragAndDrop: 'Ziehen und Ablegen',
	DragDropText: 'Ziehen und Ablegen von Gesundheitsdokumenten',
	DragToMove: 'Ziehen zum bewegen',
	DragToMoveOrActivate: 'Zum Verschieben oder Aktivieren ziehen',
	DramaTherapist: 'Dramatherapeut',
	DropdownFormFieldPlaceHolder: 'Wählen Sie Optionen aus der Liste',
	DropdownFormPrimaryText: 'Runterfallen',
	DropdownFormSecondaryText: 'Wählen Sie aus einer Liste von Optionen',
	DropdownTextFieldError: 'Der Text der Dropdown-Option darf nicht leer sein',
	DropdownTextFieldPlaceholder: 'Hinzufügen einer Dropdown-Option',
	Due: 'Fälligkeitsdatum',
	DueDate: 'Fälligkeitsdatum',
	Duplicate: 'Duplikat',
	DuplicateAvailabilityScheduleFailure: 'Der Verfügbarkeitsplan konnte nicht dupliziert werden',
	DuplicateAvailabilityScheduleSuccess: 'Erfolgreich duplizierter Zeitplan von {name}',
	DuplicateClientBannerAction: 'Rezension',
	DuplicateClientBannerDescription:
		'Durch das Zusammenführen doppelter Kundendatensätze werden diese zu einem einzigen Datensatz konsolidiert, wobei alle eindeutigen Kundeninformationen erhalten bleiben.',
	DuplicateClientBannerTitle: '{count} Duplikate gefunden',
	DuplicateColumn: 'Spalte duplizieren',
	DuplicateContactFieldSettingErrorSnackbar: 'Es dürfen keine doppelten Abschnittsnamen vorhanden sein',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Es dürfen keine doppelten Feldnamen vorhanden sein',
	DuplicateEmailError: 'E-Mail duplizieren',
	DuplicateHeadingName: 'Abschnitt {name} existiert bereits',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Es existiert bereits eine Rechnung mit der gleichen „Rechnungsnummer“.',
	DuplicateRecords: 'Doppelte Datensätze',
	DuplicateRecordsMinimumError: 'Es müssen mindestens 2 Datensätze ausgewählt werden',
	DuplicateRecordsRequired: 'Wählen Sie mindestens 1 Datensatz zum Trennen aus',
	DuplicateServiceFailure: 'Fehler beim Duplizieren von <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Erfolgreich dupliziert <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Ordner erfolgreich dupliziert',
	DuplicateTemplateSuccess: 'Vorlage erfolgreich dupliziert',
	DurationInMinutes: '{duration} Min',
	Dx: 'DX',
	DxCode: 'DX-Code',
	DxCodeSelectPlaceholder: 'Suchen und Hinzufügen von ICD-10-Codes',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Keiner',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>enthält Anpassungen</i>} other {}}',
	EarnReferralCredit: 'Verdiene ${creditAmount}',
	Economist: 'Ökonom',
	Edit: 'Bearbeiten',
	EditArrangements: 'Arrangements bearbeiten',
	EditBillTo: 'Rechnung bearbeiten an',
	EditClient: 'Client bearbeiten',
	EditClientFileModalDescription:
		'Bearbeiten Sie den Zugriff auf diese Datei, indem Sie die Optionen in den Kontrollkästchen &quot;Anzeigbar für&quot; auswählen.',
	EditClientFileModalTitle: 'Datei bearbeiten',
	EditClientNoteModalDescription:
		'Bearbeiten Sie den Inhalt der Notiz. Verwenden Sie den Abschnitt „Anzeigbar für“, um zu ändern, wer die Notiz sehen kann.',
	EditClientNoteModalTitle: 'Notiz bearbeiten',
	EditConnectedAppButton: 'Bearbeiten',
	EditConnections: 'Verbindungen bearbeiten{account, select, null { } undefined { } other { für {account}}}',
	EditContactDetails: 'Kontaktdaten bearbeiten',
	EditContactFormIsClientLabel: 'In Client konvertieren',
	EditContactIsClientCheckboxWarning:
		'Die Umwandlung eines Kontakts in einen Kunden kann nicht rückgängig gemacht werden',
	EditContactIsClientWanringModal:
		'Die Umwandlung dieses Kontakts in einen Kunden kann nicht rückgängig gemacht werden. Alle Beziehungen bleiben jedoch bestehen und Sie haben nun Zugriff auf die Notizen, Dateien und andere Dokumentation des Kontakts.',
	EditContactRelationship: 'Kontaktbeziehung bearbeiten',
	EditDetails: 'Details bearbeiten',
	EditFileModalTitle: 'Datei für {name} bearbeiten',
	EditFolder: 'Ordner bearbeiten',
	EditFolderDescription: 'Benennen Sie den Ordner um in ...',
	EditInvoice: 'Rechnung bearbeiten',
	EditInvoiceDetails: 'Rechnungsdetails bearbeiten',
	EditLink: 'Link bearbeiten',
	EditLocation: 'Standort bearbeiten',
	EditLocationFailure: 'Standort konnte nicht aktualisiert werden',
	EditLocationSucess: 'Standort erfolgreich aktualisiert',
	EditPaymentDetails: 'Zahlungsdetails bearbeiten',
	EditPaymentMethod: 'Zahlungsmethode bearbeiten',
	EditPersonalDetails: 'Persönliche Daten bearbeiten',
	EditPractitioner: 'Praktiker bearbeiten',
	EditProvider: 'Anbieter bearbeiten',
	EditProviderDetails: 'Anbieterdetails bearbeiten',
	EditRecurrence: 'Wiederholung bearbeiten',
	EditRecurringAppointmentModalTitle: 'Wiederkehrenden Termin bearbeiten',
	EditRecurringEventModalTitle: 'Wiederholtes Meeting bearbeiten',
	EditRecurringReminderModalTitle: 'Wiederholte Erinnerung bearbeiten',
	EditRecurringTaskModalTitle: 'Wiederkehrende Aufgabe bearbeiten',
	EditRelationshipModalTitle: 'Beziehung bearbeiten',
	EditService: 'Service bearbeiten',
	EditServiceFailure: 'Der neue Dienst konnte nicht aktualisiert werden.',
	EditServiceGroup: 'Sammlung bearbeiten',
	EditServiceGroupFailure: 'Aktualisierung der Sammlung fehlgeschlagen',
	EditServiceGroupSuccess: 'Sammlung erfolgreich aktualisiert',
	EditServiceSuccess: 'Neuer Dienst erfolgreich aktualisiert',
	EditStaffDetails: 'Mitarbeiterdetails bearbeiten',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'E-Mail-Adresse kann nicht aktualisiert werden. Bitte erstellen Sie ein neues Teammitglied mit einer neuen E-Mail-Adresse.',
	EditSubscriptionBilledQuantity: 'Rechnungsmenge',
	EditSubscriptionBilledQuantityValue: '{billedUsers} Teammitglieder',
	EditSubscriptionLimitedTimeOffer: 'Nur für kurze Zeit! 50 % Rabatt für 6 Monate.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Ihre Abonnementkosten werden angepasst, wenn Sie Teammitglieder hinzufügen oder entfernen.',
	EditSubscriptionUpgradeContent:
		'Ihr Konto wird sofort auf den neuen Plan und die neue Abrechnungsperiode aktualisiert. Alle Preisänderungen werden automatisch mit Ihrer gespeicherten Zahlungsmethode belastet oder Ihrem Konto gutgeschrieben.',
	EditSubscriptionUpgradePlanTitle: 'Abonnementsplan aktualisieren',
	EditSuperbillReceipt: 'Superbill bearbeiten',
	EditTags: 'Stichworte bearbeiten',
	EditTemplate: 'Vorlage bearbeiten',
	EditTemplateFolderSuccessMessage: 'Ordner erfolgreich bearbeitet',
	EditValue: 'Bearbeite {value}',
	Edited: 'Herausgegeben',
	Editor: 'Editor',
	EditorAlertDescription:
		'Es wurde ein nicht unterstütztes Format erkannt. Laden Sie die App neu oder wenden Sie sich an unser Supportteam.',
	EditorAlertTitle: 'Wir haben Probleme, diesen Inhalt anzuzeigen',
	EditorPlaceholder:
		'Beginnen Sie mit dem Schreiben, wählen Sie eine Vorlage oder fügen Sie grundlegende Blöcke hinzu, um die Antworten Ihrer Kunden zu erfassen.',
	EditorTemplatePlaceholder:
		'Beginnen Sie mit dem Schreiben oder fügen Sie Komponenten hinzu, um eine Vorlage zu erstellen',
	EditorTemplateWithSlashCommandPlaceholder:
		'Fangen Sie an zu schreiben oder fügen Sie grundlegende Blöcke hinzu, um Antworten von Kunden zu erfassen. Verwenden Sie Slash-Befehle (/) für schnelle Aktionen.',
	EditorWithSlashCommandPlaceholder:
		'Beginnen Sie mit dem Schreiben, wählen Sie eine Vorlage aus oder fügen Sie grundlegende Blöcke hinzu, um Clientantworten zu erfassen. Verwenden Sie Schrägstrichbefehle ( / ) für schnelle Aktionen.',
	EffectiveStartEndDate: 'Gültigkeitsbeginn - Gültigkeitsende',
	ElectricalEngineer: 'Elektroingenieur',
	Electronic: 'Elektronisch',
	ElectronicSignature: 'Elektronische Unterschrift',
	ElementarySchoolTeacher: 'Grundschullehrer',
	Eligibility: 'Berechtigung',
	Email: 'Email',
	EmailAlreadyExists: 'Diese E-Mail Adresse ist bereits vergeben',
	EmailAndSms: 'E-Mail ',
	EmailBody: 'Nachrichtentext',
	EmailContainsIgnoredDescription:
		'Die folgende E-Mail enthält {count, plural, one {ein Absender} others {Absender}} E-Mail, die derzeit ignoriert wird. Möchten Sie fortfahren?',
	EmailInviteToPortalBody: `Hallo {contactName},
Bitte folgen Sie diesem Link, um sich in Ihr sicheres Kundenportal einzuloggen und Ihre Versorgung einfach zu verwalten.

Mit freundlichen Grüßen,

{providerName}`,
	EmailInviteToPortalSubject: 'Willkommen bei {providerName}',
	EmailInvoice: 'E-Mail-Rechnung',
	EmailInvoiceOverdueBody: `Hallo {contactName}
Deine Rechnung {invoiceNumber} ist überfällig.
Bitte bezahle deine Rechnung online über den folgenden Link.

Wenn du Fragen hast, lass es uns bitte wissen.

Vielen Dank,
{providerName}`,
	EmailInvoicePaidBody: `Hallo {contactName}
Ihre Rechnung {invoiceNumber} ist bezahlt.
Um eine Kopie Ihrer Rechnung anzuzeigen und herunterzuladen, folgen Sie dem unten stehenden Link.

Wenn Sie Fragen haben, lassen Sie es uns bitte wissen.

Vielen Dank,
{providerName}`,
	EmailInvoiceProcessingBody: `Hallo {contactName},
Deine Rechnung {invoiceNumber} ist fertig.
Folge dem untenstehenden Link, um deine Rechnung einzusehen.

Wenn du Fragen hast, lass es uns bitte wissen.

Vielen Dank,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hallo {contactName}
Ihre Rechnung {invoiceNumber} ist fertig und muss bis zum {dueDate} bezahlt werden.
Um Ihre Rechnung online einzusehen und zu bezahlen, folgen Sie bitte dem untenstehenden Link.

Wenn Sie Fragen haben, lassen Sie es uns bitte wissen.

Vielen Dank,
{providerName}`,
	EmailInvoiceVoidBody: `Hallo {contactName},
Ihre Rechnung {invoiceNumber} ist ungültig.
Um diese Rechnung anzuzeigen, folgen Sie dem Link unten.

Wenn Sie Fragen haben, lassen Sie es uns bitte wissen.

Vielen Dank,
{providerName}`,
	EmailNotFound: 'Email wurde nicht gefunden',
	EmailNotVerifiedErrorCodeSnackbar:
		'Aktion kann nicht ausgeführt werden. Sie müssen Ihre E-Mail-Adresse bestätigen.',
	EmailNotVerifiedTitle: 'E-Mail nicht verifiziert',
	EmailSendClientIntakeBody: `Hallo {contactName},
{providerName} möchte Sie bitten, einige Informationen zu liefern und wichtige Dokumente zu überprüfen. Bitte folgen Sie dem untenstehenden Link, um zu beginnen.

Mit freundlichen Grüßen,

{providerName}`,
	EmailSendClientIntakeSubject: 'Willkommen bei {providerName}',
	EmailSuperbillReceipt: 'E-Mail-Superrechnung',
	EmailSuperbillReceiptBody: `Hallo {contactName},
{providerName} hat dir eine Kopie deines Erstattungsbelegs {date} geschickt.

Du kannst diesen direkt herunterladen und bei deiner Versicherung einreichen.`,
	EmailSuperbillReceiptFailure: 'Superbill-Quittung konnte nicht gesendet werden',
	EmailSuperbillReceiptSubject: '{providerName} hat eine Erklärung über den Erhalt der Erstattung geschickt',
	EmailSuperbillReceiptSuccess: 'Superbill-Quittung erfolgreich gesendet',
	EmailVerificationDescription: 'Wir <span>verifizieren</span> jetzt Ihr Konto',
	EmailVerificationNotification: 'Eine Bestätigungs-E-Mail wurde an {email} gesendet.',
	EmailVerificationSuccess: 'Ihre E-Mail-Adresse wurde erfolgreich zu {email} geändert.',
	Emails: 'E-Mails',
	EmergencyContact: 'Notfallkontakt',
	EmployeesIdentificationNumber: 'Mitarbeiteridentifikationsnummer',
	EmploymentStatus: 'Arbeitsverhältnis',
	EmptyAgendaViewDescription: 'Keine Ereignisse zum Anzeigen.<mark> Jetzt Termin vereinbaren</mark>',
	EmptyBin: 'Behälter leeren',
	EmptyBinConfirmationDescription:
		'Leeren Sie den Papierkorb, um alle **{total} Unterhaltungen** in Gelöscht zu löschen. Diese Aktion kann nicht rückgängig gemacht werden.',
	EmptyBinConfirmationTitle: 'Konversationen dauerhaft löschen',
	EmptyTrash: 'Papierkorb leeren',
	Enable: 'Aktivieren',
	EnableCustomServiceAvailability: 'Aktivieren der Dienstverfügbarkeit',
	EnableCustomServiceAvailabilityDescription: 'Ersttermine können z. B. nur täglich von 9-10 Uhr gebucht werden',
	EndCall: 'Anruf beenden',
	EndCallConfirmationForCreator: 'Sie beenden dies für alle, da Sie der Initiator des Anrufs sind.',
	EndCallConfirmationHasActiveAttendees:
		'Sie sind gerade dabei, das Gespräch zu beenden, aber der/die Kunde(n) sind bereits beigetreten. Möchten Sie auch beitreten?',
	EndCallForAll: 'Anruf für alle beenden',
	EndDate: 'Endtermin',
	EndDictation: 'Diktat beenden',
	EndOfLine: 'Keine Termine mehr',
	EndSession: 'Sitzung beenden',
	EndTranscription: 'Transkription beenden',
	Ends: 'Ende',
	EndsOnDate: 'Endet am {date}',
	Enrol: 'Anmelden',
	EnrollmentRejectedSubject: 'Ihre Anmeldung bei {payerName} wurde abgelehnt.',
	Enrolment: 'Aufnahme',
	Enrolments: 'Anmeldungen',
	EnrolmentsDescription: 'Anbieter-Anmeldungen mit dem Kostenträger einsehen und verwalten.',
	EnterAName: 'Geben Sie einen Namen ein...',
	EnterFieldLabel: 'Geben Sie die Feldbezeichnung ein ...',
	EnterPaymentDetailsDescription:
		'Ihre Abonnementkosten werden beim Hinzufügen oder Entfernen von Benutzern automatisch angepasst.',
	EnterSectionName: 'Geben Sie den Abschnittsnamen ein ...',
	EnterSubscriptionPaymentDetails: 'Zahlungsdetails eingeben',
	EnvironmentalScientist: 'Umweltwissenschaftler',
	Epidemiologist: 'Epidemiologe',
	Eraser: 'Radiergummi',
	Error: 'Fehler',
	ErrorBoundaryAction: 'Seite neuladen',
	ErrorBoundaryDescription: 'Bitte aktualisieren Sie die Seite und versuchen Sie es erneut.',
	ErrorBoundaryTitle: 'Hoppla! Da ist etwas schiefgelaufen',
	ErrorCallNotFound:
		'Der Anruf kann nicht gefunden werden. Er ist möglicherweise abgelaufen oder der Ersteller hat ihn beendet.',
	ErrorCannotAccessCallUninvitedCode: 'Es tut uns leid, anscheinend wurden Sie nicht zu diesem Anruf eingeladen.',
	ErrorFileUploadCustomMaxFileCount: 'Kann nicht mehr als {count} Dateien gleichzeitig hochladen',
	ErrorFileUploadCustomMaxFileSize: 'Datei darf {mb} MB nicht überschreiten',
	ErrorFileUploadInvalidFileType:
		'Ungültiger Dateityp, der potenzielle Viren und schädliche Software enthalten könnte',
	ErrorFileUploadMaxFileCount: 'Es können nicht mehr als 150 Dateien gleichzeitig hochgeladen werden.',
	ErrorFileUploadMaxFileSize: 'Die Dateigröße darf 100 MB nicht überschreiten.',
	ErrorFileUploadNoFileSelected: 'Bitte wählen Sie die hochzuladenden Dateien aus',
	ErrorInvalidNationalProviderId: 'Die angegebene nationale Anbieter-ID ist ungültig.',
	ErrorInvalidPayerId: 'Die angegebene Payer-ID ist ungültig.',
	ErrorInvalidTaxNumber: 'Die angegebene Steuernummer ist ungültig.',
	ErrorInviteExistingProviderStaffCode: 'Dieser Benutzer ist bereits im Workspace vorhanden.',
	ErrorInviteStaffExistingUser:
		'Es tut uns leid, aber der von Ihnen hinzugefügte Benutzer scheint bereits in unserem System zu existieren.',
	ErrorOnlySingleCallAllowed: 'Sie können nur einen Anruf gleichzeitig führen',
	ErrorPayerNotFound: 'Zahler nicht gefunden',
	ErrorProfilePhotoMaxFileSize: 'Hochladen fehlgeschlagen! Dateigrößenlimit erreicht – 5 MB',
	ErrorRegisteredExistingUser: 'Entschuldigung, es scheint, dass Sie bereits registriert sind.',
	ErrorUserSignInIncorrectCredentials: 'Ungültige E-Mail oder ungültiges Passwort. Bitte versuchen Sie es erneut.',
	ErrorUserSigninGeneric: 'Entschuldigung, es ist etwas schiefgelaufen.',
	ErrorUserSigninUserNotConfirmed:
		'Entschuldigung, Sie müssen Ihr Konto bestätigen, bevor Sie sich anmelden können. Anweisungen finden Sie in Ihrem Posteingang.',
	Errors: 'Fehler',
	EssentialPlanInclusionFive: 'Vorlagenimport',
	EssentialPlanInclusionFour: '5 GB Speicherplatz',
	EssentialPlanInclusionHeader: 'Alles kostenlos  ',
	EssentialPlanInclusionOne: 'Automatische und benutzerdefinierte Erinnerungen',
	EssentialPlanInclusionSix: 'Vorrangiger Support',
	EssentialPlanInclusionThree: 'Video-Chat',
	EssentialPlanInclusionTwo: 'Zweiwege-Kalendersynchronisierung',
	EssentialSubscriptionPlanSubtitle: 'Vereinfachen Sie Ihre Praxis mit dem Wesentlichen',
	EssentialSubscriptionPlanTitle: 'Essentiell',
	Esthetician: 'Kosmetikerin',
	Estheticians: 'Kosmetikerinnen',
	EstimatedArrivalDate: 'Angekommen am {numberOfDaysFromNow}',
	Ethnicity: 'Ethnizität',
	Europe: 'Europa',
	EventColor: 'Besprechungsfarbe',
	EventName: 'Veranstaltungsname',
	EventType: 'Veranstaltungstyp',
	Every: 'Jeder',
	Every2Weeks: 'Alle 2 Wochen',
	EveryoneInWorkspace: 'Alle im Arbeitsbereich',
	ExercisePhysiologist: 'Sportphysiologe',
	Existing: 'Bestehend',
	ExistingClients: 'Bestehende Kunden',
	ExistingFolders: 'Bestehende Ordner',
	ExpiredPromotionCode: 'Der Aktionscode ist abgelaufen',
	ExpiredReferralDescription: 'Empfehlung ist abgelaufen',
	ExpiredVerificationLink: 'Abgelaufener Bestätigungslink',
	ExpiredVerificationLinkDescription: `Es tut uns leid, aber der Bestätigungslink, auf den Sie geklickt haben, ist abgelaufen. Dies kann passieren, wenn Sie länger als 24 Stunden gewartet haben, bevor Sie auf den Link geklickt haben, oder wenn Sie den Link bereits zur Bestätigung Ihrer E-Mail-Adresse verwendet haben.

 Bitte fordern Sie einen neuen Bestätigungslink an, um Ihre E-Mail-Adresse zu bestätigen.`,
	ExpiryDateRequired: 'Ablaufdatum ist erforderlich',
	ExploreFeature: 'Was möchten Sie zuerst erkunden?',
	ExploreOptions: 'Wählen Sie eine oder mehrere Optionen zum Erkunden aus ...',
	Export: 'Export',
	ExportAppointments: 'Termine exportieren',
	ExportClaims: 'Export-Ansprüche',
	ExportClaimsFilename: 'Ansprüche {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription:
		'Ihre Datei konnte aufgrund eines Fehlers nicht heruntergeladen werden.',
	ExportClientsDownloadFailureSnackbarTitle: 'Download fehlgeschlagen',
	ExportClientsFailureSnackbarDescription:
		'Bitte versuchen Sie es später erneut oder wenden Sie sich an den Support, um Hilfe zu erhalten.',
	ExportClientsFailureSnackbarTitle: 'Export fehlgeschlagen',
	ExportClientsModalDescription: `Dieser Datenexportvorgang kann je nach der Menge der zu exportierenden Daten einige Minuten dauern. Sie erhalten eine E-Mail-Benachrichtigung mit einem Link, sobald die Daten zum Download bereitstehen.

 Möchten Sie mit dem Export der Kundendaten fortfahren?`,
	ExportClientsModalTitle: 'Kundendaten exportieren',
	ExportCms1500: 'CMS1500 exportieren',
	ExportContactFailedNotificationSubject: 'Ihr Datenexport ist fehlgeschlagen',
	ExportFailed: 'Export fehlgeschlagen',
	ExportGuide: 'Leitfaden exportieren',
	ExportInvoiceFileName: 'Transaktionen {fromDate}-{toDate}.csv',
	ExportPayments: 'Exportzahlungen',
	ExportPaymentsFilename: 'Zahlungen {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Ihr Dokument steht zum Download bereit.',
	ExportPrintWaitMessage: 'Ihr Dokument wird vorbereitet. Bitte warten …',
	ExportTextOnly: 'Nur Text exportieren',
	ExportTransactions: 'Exporttransaktionen',
	Exporting: 'Exportieren',
	ExportingData: 'Daten exportieren',
	ExtendedFamilyMember: 'Erweitertes Familienmitglied',
	External: 'Extern',
	ExternalEventInfoBanner:
		'Dieser Termin stammt aus einem synchronisierten Kalender und kann möglicherweise Elemente fehlen.',
	ExtraLarge: 'Extragroß',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Fehlgeschlagen',
	FailedToJoinTheMeeting: 'Der Beitritt zum Meeting ist fehlgeschlagen.',
	FallbackPageDescription: `Sieht so aus, als ob diese Seite nicht existiert. Möglicherweise müssen Sie diese Seite {refreshButton} aktualisieren, um die neuesten Änderungen zu erhalten.
Andernfalls wenden Sie sich bitte an den Carepatron-Support.`,
	FallbackPageDescriptionUpdateButton: 'Aktualisierung',
	FallbackPageTitle: 'Hoppla...',
	FamilyPlanningService: 'Familienplanungsservice',
	FashionDesigner: 'Modedesigner',
	FastTrackInvoicingAndBilling: 'Beschleunigen Sie Ihre Rechnungsstellung',
	Father: 'Vater',
	FatherInLaw: 'Schwiegervater',
	Favorite: 'Favorit',
	FeatureBannerCalendarTile1ActionLabel: 'Online-Buchung • 2 Min.',
	FeatureBannerCalendarTile1Description:
		'Senden Sie einfach eine E-Mail, eine SMS oder fügen Sie die Verfügbarkeit auf Ihrer Website hinzu',
	FeatureBannerCalendarTile1Title: 'Ermöglichen Sie Ihren Kunden die Online-Buchung',
	FeatureBannerCalendarTile2ActionLabel: 'Erinnerungen automatisieren • 2 Min.',
	FeatureBannerCalendarTile2Description: 'Steigern Sie die Kundenpräsenz mit automatischen Erinnerungen',
	FeatureBannerCalendarTile2Title: 'Reduzieren Sie No-Shows',
	FeatureBannerCalendarTile3Title: 'Planung und Workflow',
	FeatureBannerCalendarTitle: 'Machen Sie die Planung einfach',
	FeatureBannerCallsTile1ActionLabel: 'Telemedizin-Anruf starten',
	FeatureBannerCallsTile1Description: 'Client-Zugriff mit nur einem Link. Keine Logins, Passwörter oder Ärger',
	FeatureBannerCallsTile1Title: 'Starten Sie von überall aus einen Videoanruf',
	FeatureBannerCallsTile2ActionLabel: 'Apps verbinden • 4 Min.',
	FeatureBannerCallsTile2Description: 'Nahtlose Anbindung anderer bevorzugter Telemedizin-Anbieter',
	FeatureBannerCallsTile2Title: 'Verbinden Sie Ihre Telemedizin-Apps',
	FeatureBannerCallsTile3Title: 'Anrufe',
	FeatureBannerCallsTitle: 'Verbinden Sie sich mit Kunden – überall und jederzeit',
	FeatureBannerClientsTile1ActionLabel: 'Jetzt importieren • 2 Min.',
	FeatureBannerClientsTile1Description: 'Legen Sie schnell los mit unserem automatisierten Client-Importtool',
	FeatureBannerClientsTile1Title: 'Haben Sie viele Kunden?',
	FeatureBannerClientsTile2ActionLabel: 'Aufnahme anpassen • 2 Min.',
	FeatureBannerClientsTile2Description: 'Beseitigen Sie den Papierkram und verbessern Sie das Kundenerlebnis',
	FeatureBannerClientsTile2Title: 'Papierlos werden',
	FeatureBannerClientsTile3Title: 'Kundenportal',
	FeatureBannerClientsTitle: 'Alles beginnt mit Ihren Kunden',
	FeatureBannerHeader: 'Von der Community, für die Community!',
	FeatureBannerInvoicesTile1ActionLabel: 'Zahlungen automatisieren • 2 Min.',
	FeatureBannerInvoicesTile1Description: 'Vermeiden Sie unangenehme Gespräche mit automatisierten Zahlungen',
	FeatureBannerInvoicesTile1Title: 'Erhalten Sie Ihr Geld doppelt so schnell',
	FeatureBannerInvoicesTile2ActionLabel: 'Cashflow verfolgen • 2 Min.',
	FeatureBannerInvoicesTile2Description:
		'Reduzieren Sie unbezahlte Rechnungen und behalten Sie Ihre Einnahmen im Auge',
	FeatureBannerInvoicesTile2Title: 'Verfolgen Sie Ihr Einkommen mühelos',
	FeatureBannerInvoicesTile3Title: 'Abrechnung und Zahlungen',
	FeatureBannerInvoicesTitle: 'Eine Sorge weniger',
	FeatureBannerSubheader:
		'Von unserem Team und unserer Community erstellte Carepatron-Vorlagen. Probieren Sie neue Ressourcen aus oder teilen Sie Ihre eigenen!',
	FeatureBannerTeamTile1ActionLabel: 'Jetzt einladen',
	FeatureBannerTeamTile1Description:
		'Laden Sie Teammitglieder zu Ihrem Konto ein und vereinfachen Sie die Zusammenarbeit',
	FeatureBannerTeamTile1Title: 'Bringen Sie Ihr Team zusammen',
	FeatureBannerTeamTile2ActionLabel: 'Verfügbarkeit festlegen • 2 Min.',
	FeatureBannerTeamTile2Description: 'Verwalten Sie die Verfügbarkeit Ihres Teams, um Doppelbuchungen zu vermeiden',
	FeatureBannerTeamTile2Title: 'Legen Sie Ihre Verfügbarkeit fest',
	FeatureBannerTeamTile3ActionLabel: 'Berechtigungen festlegen • 2 Min.',
	FeatureBannerTeamTile3Description:
		'Kontrollieren Sie den Zugriff auf vertrauliche Daten und Tools zur Einhaltung von Vorschriften',
	FeatureBannerTeamTile3Title: 'Berechtigungen und Zugriff anpassen',
	FeatureBannerTeamTitle: 'Nichts Großes erreicht man allein',
	FeatureBannerTemplatesTile1ActionLabel: 'Bibliothek erkunden • 2 Min.',
	FeatureBannerTemplatesTile1Description: 'Wählen Sie aus einer erstaunlichen Bibliothek anpassbarer Ressourcen ',
	FeatureBannerTemplatesTile1Title: 'Reduzieren Sie Ihren Arbeitsaufwand',
	FeatureBannerTemplatesTile2ActionLabel: 'Jetzt senden • 2 Min.',
	FeatureBannerTemplatesTile2Description: 'Schicken Sie Ihren Kunden schöne Vorlagen zur Vervollständigung',
	FeatureBannerTemplatesTile2Title: 'So macht Dokumentation Spaß',
	FeatureBannerTemplatesTile3Title: 'Vorlagen',
	FeatureBannerTemplatesTitle: 'Vorlagen für absolut alles',
	FeatureLimitBannerDescription:
		'Jetzt upgraden, um {featureName} ohne Unterbrechung zu erstellen und zu verwalten und das Beste aus Carepatron herauszuholen!',
	FeatureLimitBannerTitle: 'Sie sind {percentage}% auf dem Weg zu Ihrem {featureName} Limit',
	FeatureRequiresUpgrade: 'Diese Funktion erfordert ein Upgrade.',
	Fee: 'Gebühr',
	Female: 'Weiblich',
	FieldLabelTooltip: '{isHidden, select, true {Zeigen} other {Verstecken}} Feldbezeichnung',
	FieldName: 'Feldname',
	FieldOptionsFirstPart: 'Erstes Wort',
	FieldOptionsMiddlePart: 'Wörter aus der Mitte',
	FieldOptionsSecondPart: 'Letztes Wort',
	FieldOptionsWholeField: 'Ganzes Feld',
	FieldType: 'Feldtyp',
	Fields: 'Felder',
	File: 'Datei',
	FileDownloaded: '<strong>{fileName}</strong> heruntergeladen',
	FileInvalidType: 'Datei wird nicht unterstützt.',
	FileNotFound: 'Datei nicht gefunden',
	FileNotFoundDescription: 'Die Datei, die Sie suchen, wurde möglicherweise verschoben oder gelöscht',
	FileTags: 'Datei-Tags',
	FileTagsHelper: 'Tags werden auf alle Dateien angewendet',
	FileTooLarge: 'Datei zu groß.',
	FileTooSmall: 'Datei zu klein.',
	FileUploadComplete: 'Vollständig',
	FileUploadFailed: 'Fehlgeschlagen',
	FileUploadInProgress: 'Wird geladen',
	FileUploadedNotificationSubject: '{actorProfileName} hat eine Datei hochgeladen',
	Files: 'Dateien',
	FillOut: 'Ausfüllen',
	Filter: 'Filter',
	FilterBy: 'Filtern nach',
	FilterByAmount: 'Nach Betrag filtern',
	FilterByClient: 'Filtern nach Kunde',
	FilterByLocation: 'Nach Standort filtern',
	FilterByService: 'Nach Service filtern',
	FilterByStatus: 'Nach Status filtern',
	FilterByTags: 'Filtern nach Tags',
	FilterByTeam: 'Nach Team filtern',
	Filters: 'Filter',
	FiltersAppliedToView: 'Filter angewendet, um die Ansicht anzuzeigen',
	FinalAppointment: 'Abschlusstermin',
	FinalizeImport: 'Import abschließen',
	FinancialAnalyst: 'Finanzanalyst',
	Finish: 'Beenden',
	Firefighter: 'Feuerwehrmann',
	FirstName: 'Vorname',
	FirstNameLastInitial: 'Vorname, Anfangsbuchstabe des Nachnamens',
	FirstPerson: '1. Person',
	FolderName: 'Ordnername',
	Folders: 'Ordner',
	FontFamily: 'Schriftfamilie',
	ForClients: 'Für Kunden',
	ForClientsDetails: 'Ich erhalte Pflege- oder Gesundheitsdienstleistungen',
	ForPractitioners: 'Für Praktiker',
	ForPractitionersDetails: 'Verwalten und erweitern Sie Ihre Praxis',
	ForgotPasswordConfirmAccessCode: 'Bestätigungscode',
	ForgotPasswordConfirmNewPassword: 'Neues Kennwort',
	ForgotPasswordConfirmPageDescription:
		'Bitte geben Sie Ihre E-Mail-Adresse, ein neues Passwort und den Bestätigungscode ein, den wir Ihnen gerade gesendet haben.',
	ForgotPasswordConfirmPageTitle: 'Passwort zurücksetzen',
	ForgotPasswordPageButton: 'Link zum Zurücksetzen senden',
	ForgotPasswordPageDescription:
		'Geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen einen Link zum Zurücksetzen Ihres Passworts.',
	ForgotPasswordPageTitle: 'Passwort vergessen',
	ForgotPasswordSuccessPageDescription: 'Suchen Sie in Ihrem Posteingang nach Ihrem Link zum Zurücksetzen.',
	ForgotPasswordSuccessPageTitle: 'Link zum Zurücksetzen gesendet!',
	Form: 'Form',
	FormAnswersSentToEmailNotification: 'Wir haben eine Kopie Ihrer Antworten an',
	FormBlocks: 'Formularblöcke',
	FormFieldAddOption: 'Option hinzufügen',
	FormFieldAddOtherOption: '„Andere“ hinzufügen',
	FormFieldOptionPlaceholder: 'Option {index}',
	FormStructures: 'Formularstrukturen',
	Format: 'Format',
	FormatLinkButtonColor: 'Button-Farbe',
	Forms: 'Formen',
	FormsAndAgreementsValidationMessage:
		'Um mit dem Aufnahmeprozess fortzufahren, müssen alle Formulare und Vereinbarungen ausgefüllt werden.',
	FormsCategoryDescription: 'Zum Sammeln und Organisieren von Patientendaten',
	Frankfurt: 'Frankfurt',
	Free: 'Frei',
	FreePlanInclusionFive: 'Automatisierte Abrechnung ',
	FreePlanInclusionFour: 'Kundenportal',
	FreePlanInclusionHeader: 'Erste Schritte mit',
	FreePlanInclusionOne: 'Unbegrenzte Anzahl an Kunden',
	FreePlanInclusionSix: 'Live-Unterstützung',
	FreePlanInclusionThree: '1 GB Speicherplatz',
	FreePlanInclusionTwo: 'Telemedizin',
	FreeSubscriptionPlanSubtitle: 'Kostenlos für alle',
	FreeSubscriptionPlanTitle: 'Frei',
	Friday: 'Freitag',
	From: 'Aus',
	FullName: 'Vollständiger Name',
	FunctionalMedicineOrNaturopath: 'Funktionelle Medizin oder Naturheilkunde',
	FuturePaymentsAuthoriseProvider: 'Erlaube {provider}, die gespeicherte Zahlungsmethode in Zukunft zu verwenden',
	FuturePaymentsSavePaymentMethod: 'Speichern Sie {paymentMethod} für zukünftige Zahlungen',
	GST: 'Mehrwertsteuer',
	Gender: 'Geschlecht',
	GeneralAvailability: 'Allgemeine Verfügbarkeit',
	GeneralAvailabilityDescription:
		'Legen Sie fest, wann Sie regelmäßig verfügbar sind. Kunden können Ihre Dienste nur während der verfügbaren Stunden buchen.',
	GeneralAvailabilityDescription2:
		'Erstellen Sie Zeitpläne auf Grundlage Ihrer Verfügbarkeit und der gewünschten Serviceangebote zu bestimmten Zeiten, um Ihre Online-Buchungsverfügbarkeit zu ermitteln.',
	GeneralAvailabilityInfo: 'Ihre verfügbaren Stunden bestimmen die Verfügbarkeit Ihrer Online-Buchung',
	GeneralAvailabilityInfo2:
		'Dienste, die Gruppenveranstaltungen anbieten, sollten einen neuen Zeitplan verwenden, um die verfügbaren Stunden zu reduzieren, in denen Kunden diese online buchen können.',
	GeneralHoursPlural: '{count} {count, plural, one {Stunde} other {Stunden}}',
	GeneralPractitioner: 'Arzt für Allgemeinmedizin',
	GeneralPractitioners: 'Hausärzte',
	GeneralServiceAvailabilityInfo: 'Dieser Zeitplan überschreibt das Verhalten für zugewiesene Teammitglieder',
	Generate: 'Erzeugen',
	GenerateBillingItemsBannerContent:
		'Bei wiederkehrenden Terminen werden keine Abrechnungspositionen automatisch erstellt.',
	GenerateItems: 'Artikel generieren',
	GenerateNote: 'Notiz generieren',
	GenerateNoteConfirmationModalDescription:
		'Was möchten Sie tun? Eine neue Notiz erstellen, eine vorhandene ergänzen oder ihren Inhalt ersetzen?',
	GenerateNoteFor: 'Notiz generieren für',
	GeneratingContent: 'Inhalte werden generiert...',
	GeneratingNote: 'Ihre Notiz wird generiert...',
	GeneratingTranscript: 'Transkript erstellen',
	GeneratingTranscriptDescription: 'Die Verarbeitung kann einige Minuten dauern',
	GeneratingYourTranscript: 'Erstellen Ihres Transkripts',
	GenericErrorDescription: '{module} konnte nicht geladen werden. Bitte versuchen Sie es später erneut.',
	GenericErrorTitle: 'Unerwarteter Fehler aufgetreten',
	GenericFailureSnackbar:
		'Es tut uns leid, es ist etwas Unerwartetes passiert. Bitte aktualisieren Sie die Seite und versuchen Sie es erneut.',
	GenericSavedSuccessSnackbar: 'Erfolg! Änderungen gespeichert',
	GeneticCounselor: 'GenetischerBerater',
	Gerontologist: 'Gerontologe',
	Get50PercentOff: 'Holen Sie sich 50 % Rabatt!',
	GetHelp: 'Hier erhalten Sie Hilfe',
	GetStarted: 'Loslegen',
	GettingStartedAppointmentTypes: 'Terminarten anlegen',
	GettingStartedAppointmentTypesDescription:
		'Optimieren Sie Ihre Planung und Abrechnung, indem Sie Ihre Dienste, Preise und Abrechnungscodes anpassen',
	GettingStartedAppointmentTypesTitle: 'Zeitplan ',
	GettingStartedClients: 'Fügen Sie Ihre Kunden hinzu',
	GettingStartedClientsDescription: 'Machen Sie sich mit Kunden für zukünftige Termine, Notizen und Zahlungen bereit',
	GettingStartedClientsTitle: 'Alles beginnt mit den Kunden',
	GettingStartedCreateClient: 'Client erstellen',
	GettingStartedImportClients: 'Clients importieren',
	GettingStartedInvoices: 'Rechnungen erstellen wie ein Profi',
	GettingStartedInvoicesDescription: `So erstellen Sie ganz einfach professionelle Rechnungen.
 Fügen Sie Ihr Logo, Ihren Standort und Ihre Zahlungsbedingungen hinzu`,
	GettingStartedInvoicesTitle: 'Zeigen Sie sich von Ihrer besten Seite',
	GettingStartedMobileApp: 'Holen Sie sich die mobile App',
	GettingStartedMobileAppDescription:
		'Sie können Carepatron auf Ihr iOS-, Android- oder Desktop-Gerät herunterladen, um unterwegs einfach darauf zugreifen zu können',
	GettingStartedMobileAppTitle: 'Arbeiten Sie von überall',
	GettingStartedNavItem: 'Erste Schritte',
	GettingStartedPageTitle: 'Erste Schritte mit Carepatron',
	GettingStartedPayments: 'Akzeptieren Sie Online-Zahlungen',
	GettingStartedPaymentsDescription: `Erhalten Sie Ihr Geld schneller, indem Sie Ihren Kunden die Online-Zahlung ermöglichen.
 Alle Rechnungen und Zahlungen an einem Ort anzeigen`,
	GettingStartedPaymentsTitle: 'Machen Sie Zahlungen zum Kinderspiel',
	GettingStartedSaveBranding: 'Branding speichern',
	GettingStartedSyncCalendars: 'Andere Kalender synchronisieren',
	GettingStartedSyncCalendarsDescription:
		'Carepatron prüft Ihren Kalender auf Konflikte, sodass Termine nur dann geplant werden, wenn Sie verfügbar sind',
	GettingStartedSyncCalendarsTitle: 'Immer auf dem Laufenden bleiben',
	GettingStartedVideo: 'Sehen Sie sich ein Einführungsvideo an',
	GettingStartedVideoDescription:
		'Die ersten All-in-One-Arbeitsplätze im Gesundheitswesen für kleine Teams und ihre Kunden',
	GettingStartedVideoTitle: 'Willkommen bei Carepatron',
	GetttingStartedGetMobileDownload: 'Laden Sie die App herunter',
	GetttingStartedGetMobileNoDownload:
		'Nicht mit diesem Browser kompatibel. Wenn Sie ein iPhone oder iPad verwenden, öffnen Sie diese Seite bitte in Safari. Andernfalls versuchen Sie, sie in Chrome zu öffnen.',
	Glossary: 'Glossar',
	Gmail: 'Google Mail',
	GmailSendMessagesLimitWarning:
		'Gmail erlaubt nur 500 Nachrichten pro Tag von Ihrem Konto zu senden. Einige Nachrichten könnten fehlschlagen. Möchten Sie fortfahren?',
	GoToAppointment: 'Zum Termin',
	GoToApps: 'Gehe zu Apps',
	GoToAvailability: 'Zur Verfügbarkeit',
	GoToClientList: 'Zur Kundenliste',
	GoToClientRecord: 'Zum Kundendatensatz',
	GoToClientSettings: 'Jetzt zu den Clienteinstellungen',
	GoToInvoiceTemplates: 'Zu den Rechnungsvorlagen',
	GoToNotificationSettings: 'Gehen Sie zu den Benachrichtigungseinstellungen',
	GoToPaymentSettings: 'Gehen Sie zu den Zahlungseinstellungen',
	Google: 'Google',
	GoogleCalendar: 'Google Kalender',
	GoogleColor: 'Google Kalenderfarbe',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'Google Tag Manager Container-ID',
	GotIt: 'Habe es!',
	Goto: 'Gehe zu',
	Granddaughter: 'Enkelin',
	Grandfather: 'Großvater',
	Grandmother: 'Oma',
	Grandparent: 'Großelternteil',
	Grandson: 'Enkel',
	GrantPortalAccess: 'Portalzugriff gewähren',
	GraphicDesigner: 'Grafikdesigner',
	Grid: 'Netz',
	GridView: 'Rasteransicht',
	Group: 'Gruppe',
	GroupBy: 'Gruppiere nach',
	GroupEvent: 'Gruppenveranstaltung',
	GroupEventHelper: 'Legen Sie eine Teilnehmerbegrenzung für den Dienst fest',
	GroupFilterLabel: 'Alle {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Gruppen-ID',
	GroupInputFieldsFormPrimaryText: 'Eingabefelder gruppieren',
	GroupInputFieldsFormSecondaryText: 'Auswählen oder Hinzufügen benutzerdefinierter Felder',
	GuideTo: 'Leitfaden zu {value}',
	GuideToImproveVideoQuality: 'Anleitung zur Verbesserung der Videoqualität',
	GuideToManagingPayers: 'Zahler verwalten',
	GuideToSubscriptionsBilling: 'Leitfaden zur Abonnementabrechnung',
	GuideToTroubleshooting: 'Anleitung zur Fehlerbehebung',
	Guidelines: 'Richtlinien',
	GuidelinesCategoryDescription: 'Zur Unterstützung klinischer Entscheidungsfindung',
	HST: 'HST',
	HairStylist: 'Friseur',
	HaveBeenWaiting: 'Du hast lange gewartet',
	HeHim: 'Er/Ihm',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Kalender',
	HeaderCalls: 'Anrufe',
	HeaderClientAppAccountSettings: 'Account Einstellungen',
	HeaderClientAppCalls: 'Anrufe',
	HeaderClientAppMyDocumentation: 'Dokumentation',
	HeaderClientAppMyRelationships: 'Meine Beziehungen',
	HeaderClients: 'Kunden',
	HeaderHelp: 'Helfen',
	HeaderMoreOptions: 'Mehr Optionen',
	HeaderStaff: 'Personal',
	HealthCoach: 'Gesundheitstrainer',
	HealthCoaches: 'Gesundheitscoaches',
	HealthEducator: 'Gesundheitserzieher',
	HealthInformationTechnician: 'Gesundheitsinformationstechniker',
	HealthPolicyExpert: 'Experte für Gesundheitspolitik',
	HealthServicesAdministrator: 'Administrator für Gesundheitsdienste',
	HelpArticles: 'Hilfe-Artikel',
	HiddenColumns: 'Ausgeblendete Spalten',
	HiddenFields: 'Ausgeblendete Felder',
	HiddenSections: 'Versteckte Abschnitte',
	HiddenSectionsAndFields: 'Ausgeblendete Abschnitte/Felder',
	HideColumn: 'Spalte ausblenden',
	HideColumnButton: 'Spalte {value} ausblenden',
	HideDetails: 'Details ausblenden',
	HideField: 'Feld ausblenden',
	HideFullAddress: 'Verstecken',
	HideMenu: 'Menü ausblenden',
	HideMergeSummarySidebar: 'Zusammenführungs-Zusammenfassung ausblenden',
	HideSection: 'Abschnitt ausblenden',
	HideYourView: 'Verbergen Sie Ihre Ansicht',
	Highlight: 'Hervorhebungsfarbe',
	Highlighter: 'Textmarker',
	History: 'Geschichte',
	HistoryItemFooter: '{actors, select, undefined {{date} um {time}} other {Von {actors} • {date} um {time}}}',
	HistorySidePanelEmptyState: 'Keine Verlaufsdatensätze gefunden',
	HistoryTitle: 'Aktivitätsprotokoll',
	HolisticHealthPractitioner: 'Heilpraktikerin',
	HomeCaregiver: 'Pflegekraft zu Hause',
	HomeHealthAide: 'Pflegehelfer/in',
	HomelessShelter: 'Obdachlosenheim',
	HourAbbreviation: '{count} {count, plural, one {Stunde} other {Stunden}}',
	Hourly: 'Stündlich',
	HoursPlural: '{age, plural, one {# Stunde} other {# Stunden}}',
	HowCanWeImprove: 'Wie können wir das verbessern?',
	HowCanWeImproveResponse: 'Wie können wir diese Antwort verbessern?',
	HowDidWeDo: 'Wie haben wir abgeschnitten?',
	HowDoesReferralWork: 'Leitfaden zum Empfehlungsprogramm',
	HowToUseAiSummarise: 'So verwenden Sie AI Summarize',
	HumanResourcesManager: 'Personalleiter',
	Husband: 'Ehemann',
	Hypnotherapist: 'Hypnotherapeut',
	IVA: 'Umsatzsteuer',
	IgnoreNotification: 'Benachrichtigung ignorieren',
	IgnoreOnce: 'Einmal ignorieren',
	IgnoreSender: 'Absender ignorieren',
	IgnoreSenderDescription:
		'Wenn Sie einen Absender ignorieren, wird die E-Mail in den Papierkorb verschoben und alle zukünftigen E-Mails von diesem Absender werden automatisch ignoriert.',
	IgnoreSenders: 'Absender ignorieren',
	IgnoreSendersSuccess: 'Erfolgreich ignorierte E-Mail-Adresse <mark>{addresses}</mark>',
	Ignored: 'Ignoriert',
	Image: 'Bild',
	Import: 'Importieren',
	ImportActivity: 'Importaktivität',
	ImportClientSuccessSnackbarDescription: 'Ihre Datei wurde erfolgreich importiert',
	ImportClientSuccessSnackbarTitle: 'Import erfolgreich!',
	ImportClients: 'Kunden importieren',
	ImportClientsFailureSnackbarDescription:
		'Ihre Datei konnte aufgrund eines Fehlers nicht erfolgreich importiert werden.',
	ImportClientsFailureSnackbarTitle: 'Import fehlgeschlagen!',
	ImportClientsGuide: '<h1>Leitfaden zum Importieren von Kunden</h1>',
	ImportClientsInProgressSnackbarDescription: 'Dies sollte nur etwa eine Minute dauern.',
	ImportClientsInProgressSnackbarTitle: 'Importieren von {fileName}',
	ImportClientsModalDescription:
		'Wählen Sie, woher Ihre Daten kommen – ob es sich um eine Datei auf Ihrem Gerät, einen Drittanbieterdienst oder eine andere Softwareplattform handelt.',
	ImportClientsModalFileUploadHelperText: 'Unterstützt {fileTypes}. Größenbeschränkung {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Anleitung zum Importieren von Kundendaten',
	ImportClientsModalStep1Label: 'Datenquelle auswählen',
	ImportClientsModalStep2Label: 'Datei hochladen',
	ImportClientsModalStep3Label: 'Überprüfen der Felder',
	ImportClientsModalTitle: 'Importieren Ihrer Kundendaten',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {Klient} other {Klienten}} bereit zum Importieren',
	ImportContactFailedNotificationSubject: 'Ihr Datenimport ist fehlgeschlagen',
	ImportDataSourceSelectorLabel: 'Datenquelle importieren aus',
	ImportDataSourceSelectorPlaceholder: 'Suchen oder Importdatenquelle auswählen',
	ImportExportButton: 'Import Export',
	ImportFailed: 'Import fehlgeschlagen',
	ImportFromAnotherPlatformTileDescription:
		'Laden Sie einen Export Ihrer Client-Dateien herunter und laden Sie sie hier hoch.',
	ImportFromAnotherPlatformTileLabel: 'Import aus einer anderen Plattform',
	ImportGuide: 'Anleitung zum Importieren',
	ImportInProgress: 'Import wird ausgeführt',
	ImportProcessing: 'Importverarbeitung...',
	ImportSpreadsheetDescription:
		'Sie können Ihre vorhandene Kundenliste in Carepatron importieren, indem Sie eine Tabellenkalkulationsdatei mit tabellarischen Daten hochladen, z. B. .CSV, .XLS oder .XLSX',
	ImportSpreadsheetTitle: 'Importieren Sie Ihre Tabellenkalkulationsdatei',
	ImportTemplates: 'Importvorlagen',
	Importing: 'Importieren',
	ImportingCalendarProductEvents: 'Importiere {product} Ereignisse',
	ImportingData: 'Daten importieren',
	ImportingSpreadsheetDescription: 'Dies sollte nur etwa eine Minute dauern.',
	ImportingSpreadsheetTitle: 'Importieren Ihrer Tabelle',
	ImportsInProgress: 'Importe in Bearbeitung',
	InPersonMeeting: 'Persönliches Treffen',
	InProgress: 'Im Gange',
	InTransit: 'Unterwegs',
	InTransitTooltip:
		'Der Saldo „Unterwegs“ umfasst alle bezahlten Rechnungsauszahlungen von Stripe auf Ihr Bankkonto. Die Abrechnung dieser Beträge dauert in der Regel 3–5 Tage.',
	Inactive: 'Inaktiv',
	InboundOrOutboundCalls: 'Eingehende oder ausgehende Anrufe',
	Inbox: 'Posteingang',
	InboxAccessRestricted:
		'Zugriff eingeschränkt. Bitte wenden Sie sich für Berechtigungen an den Besitzer des Posteingangs.',
	InboxAccountAlreadyConnected: 'Der Kanal, den Sie verbinden wollten, ist bereits mit Carepatron verbunden',
	InboxAddAttachments: 'Anhänge hinzufügen',
	InboxAreYouSureDeleteMessage: 'Möchten Sie diese Nachricht wirklich löschen?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Erfolgreich # Unterhaltung geschlossen} other {Erfolgreich # Unterhaltungen geschlossen}}',
	InboxBulkComposeModalTitle: 'Massennachricht verfassen',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Erfolgreich # Konversation gelöscht} other {Erfolgreich # Konversationen gelöscht}}',
	InboxBulkReadSuccess:
		'{count, plural, one {# Unterhaltung erfolgreich als gelesen markiert} other {# Unterhaltungen erfolgreich als gelesen markiert}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Erfolgreich wiedereröffnet # Unterhaltung} other {Erfolgreich wiedereröffnet # Unterhaltungen}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Erfolgreich # Unterhaltung als ungelesen markiert} other {Erfolgreich # Unterhaltungen als ungelesen markiert}}',
	InboxChatCreateGroup: 'Gruppe erstellen',
	InboxChatDeleteGroupModalDescription:
		'Sind Sie sicher, dass Sie diese Gruppe löschen möchten? Alle Nachrichten und Anhänge werden gelöscht.',
	InboxChatDeleteGroupModalTitle: 'Gruppe löschen',
	InboxChatDiscardDraft: 'Entwurf verwerfen',
	InboxChatDragDropText: 'Dateien zum Hochladen hier ablegen',
	InboxChatGroupConversation: 'Gruppenkonversation',
	InboxChatGroupCreateModalDescription:
		'Erstelle eine neue Gruppe, um mit deinem Team, deinen Kunden oder deiner Community Nachrichten auszutauschen und zusammenzuarbeiten.',
	InboxChatGroupCreateModalTitle: 'Gruppe erstellen',
	InboxChatGroupMembers: 'Gruppenmitglieder',
	InboxChatGroupModalGroupNameFieldLabel: 'Gruppenname',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Z. B. Kundensupport, Administrator',
	InboxChatGroupModalGroupNameFieldRequired: 'Dieses Feld ist erforderlich',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Mindestens ein Mitglied erforderlich',
	InboxChatGroupModalMembersFieldLabel: 'Gruppenmitglieder auswählen',
	InboxChatGroupModalMembersFieldPlaceholder: 'Mitglieder auswählen',
	InboxChatGroupUpdateModalTitle: 'Gruppe verwalten',
	InboxChatLeaveGroup: 'Gruppe verlassen',
	InboxChatLeaveGroupModalDescription:
		'Sind Sie sicher, dass Sie diese Gruppe verlassen möchten? Sie werden keine Nachrichten oder Aktualisierungen mehr erhalten.',
	InboxChatLeaveGroupModalTitle: 'Gruppe verlassen',
	InboxChatLeftGroupMessage: 'Linksgruppennachricht',
	InboxChatManageGroup: 'Gruppe verwalten',
	InboxChatSearchParticipants: 'Empfänger auswählen',
	InboxCloseConversationSuccess: 'Konversation erfolgreich beendet',
	InboxCompose: 'Komponieren',
	InboxComposeBulk: 'Massennachricht',
	InboxComposeCarepatronChat: 'Bote',
	InboxComposeChat: 'Chat verfassen',
	InboxComposeDisabledNoConnection: 'Verbinden Sie ein E-Mail-Konto, um Nachrichten zu senden.',
	InboxComposeDisabledNoPermissionTooltip:
		'Sie haben keine Berechtigung, Nachrichten aus diesem Posteingang zu senden.',
	InboxComposeEmail: 'E-Mail verfassen',
	InboxComposeMessageFrom: 'Aus',
	InboxComposeMessageRecipientBcc: 'Bcc',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'Zu',
	InboxComposeMessageSubject: 'Thema:',
	InboxConnectAccountButton: 'Verknüpfen Sie Ihre E-Mail',
	InboxConnectedDescription: 'Ihr Posteingang enthält keine Nachrichten',
	InboxConnectedHeading:
		'Ihre Unterhaltungen werden hier angezeigt, sobald Sie mit dem Austausch von Nachrichten beginnen',
	InboxConnectedHeadingClientView: 'Optimieren Sie Ihre Kundenkommunikation',
	InboxCreateFirstInboxButton: 'Erstellen Sie Ihren ersten Posteingang',
	InboxCreationSuccess: 'Posteingang erfolgreich erstellt',
	InboxDeleteAttachment: 'Anhang löschen',
	InboxDeleteConversationSuccess: 'Konversation erfolgreich gelöscht',
	InboxDeleteMessage: 'Nachricht löschen?',
	InboxDirectMessage: 'Direktnachricht',
	InboxEditDraft: 'Entwurf bearbeiten',
	InboxEmailComposeReplyEmail: 'Antwort verfassen',
	InboxEmailDraft: 'Entwurf',
	InboxEmailNotFound: 'Email wurde nicht gefunden',
	InboxEmailSubjectFieldInformation: 'Durch Ändern der Betreffzeile wird eine neue Thread-E-Mail erstellt.',
	InboxEmptyArchiveDescription: 'Es wurden keine archivierten Konversationen gefunden',
	InboxEmptyBinDescription: 'Es wurden keine gelöschten Konversationen gefunden',
	InboxEmptyBinHeading: 'Alles klar, hier gibt es nichts zu sehen',
	InboxEmptyBinSuccess: 'Erfolgreich gelöschte Konversationen',
	InboxEmptyCongratsHeading: 'Gute Arbeit! Lehn dich zurück und entspanne dich bis zum nächsten Gespräch',
	InboxEmptyDraftDescription: 'Es wurden keine Gesprächsentwürfe gefunden',
	InboxEmptyDraftHeading: 'Alles klar, hier gibt es nichts zu sehen',
	InboxEmptyOtherDescription: 'Es wurden keine anderen Konversationen gefunden',
	InboxEmptyScheduledHeading: 'Alles klar, keine Gespräche zum Senden geplant',
	InboxEmptySentDescription: 'Es wurden keine gesendeten Konversationen gefunden',
	InboxForward: 'Nach vorne',
	InboxGroupClientsLabel: 'Alle Kunden',
	InboxGroupClientsOverviewLabel: 'Kunden',
	InboxGroupClientsSelectedItemPrefix: 'Kunde',
	InboxGroupStaffsLabel: 'Ganzes Team',
	InboxGroupStaffsOverviewLabel: 'Team',
	InboxGroupStaffsSelectedItemPrefix: 'Team',
	InboxGroupStatusLabel: 'Alle Status',
	InboxGroupStatusOverviewLabel: 'An einen Status senden',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Alle Tags',
	InboxGroupTagsOverviewLabel: 'An ein Tag senden',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Zitierten Text ausblenden',
	InboxIgnoreConversationSuccess: 'Konversation erfolgreich ignoriert',
	InboxMessageAllLabelRecipientsCount: 'Alle {label} Empfänger ({count})',
	InboxMessageBodyPlaceholder: 'Fügen Sie Ihre Nachricht hinzu',
	InboxMessageDeleted: 'Nachricht gelöscht',
	InboxMessageMarkedAsRead: 'Nachricht als gelesen markiert',
	InboxMessageMarkedAsUnread: 'Nachricht als ungelesen markiert',
	InboxMessageSentViaChat: '**Über Chat gesendet**  • {time} von {name}',
	InboxMessageShowMoreRecipients: '+{count} mehr',
	InboxMessageWasDeleted: 'Diese Nachricht wurde gelöscht.',
	InboxNoConnectionDescription: 'Verbinde dein E-Mail-Konto oder erstelle Postfächer mit mehreren E-Mails',
	InboxNoConnectionHeading: 'Integrieren Sie Ihre Kundenkommunikation',
	InboxNoDirectMessage: 'Keine neuen Nachrichten',
	InboxRecentConversations: 'Aktuell',
	InboxReopenConversationSuccess: 'Konversation erfolgreich wiedereröffnet',
	InboxReply: 'Antwort',
	InboxReplyAll: 'Allen antworten',
	InboxRestoreConversationSuccess: 'Konversation erfolgreich wiederhergestellt',
	InboxScheduleSendCancelSendSuccess: 'Geplanter Versand abgebrochen und Nachricht in den Entwurf zurückversetzt',
	InboxScheduleSendMessageSuccessDescription: 'Gesendet für {date}',
	InboxScheduleSendMessageSuccessTitle: 'Senden planen',
	InboxSearchForConversations: 'Suche nach "{query}"',
	InboxSendMessageSuccess: 'Konversation erfolgreich gesendet',
	InboxSettings: 'Posteingangseinstellungen',
	InboxSettingsAppsDesc:
		'Verwalten Sie verbundene Apps für diesen freigegebenen Posteingang: Fügen Sie nach Bedarf Verbindungen hinzu oder entfernen Sie sie.',
	InboxSettingsAppsNewConnectedApp: 'Neue verbundene App',
	InboxSettingsAppsTitle: 'Verbundene Apps',
	InboxSettingsDeleteAccountFailed: 'Das Löschen des Posteingangskontos ist fehlgeschlagen.',
	InboxSettingsDeleteAccountSuccess: 'Posteingangskonto erfolgreich gelöscht',
	InboxSettingsDeleteAccountWarning:
		'Das Entfernen von {email} trennt es vom Posteingang {inboxName} und stoppt die Synchronisierung von Nachrichten.',
	InboxSettingsDeleteInboxFailed: 'Posteingang konnte nicht gelöscht werden',
	InboxSettingsDeleteInboxSuccess: 'Posteingang erfolgreich gelöscht',
	InboxSettingsDeleteInboxWarning:
		'Das Löschen von {inboxName} trennt alle verbundenen Kanäle und löscht alle mit diesem Posteingang verbundenen Nachrichten.		Diese Aktion ist dauerhaft und kann nicht rückgängig gemacht werden.',
	InboxSettingsDetailsDesc: 'Kommunikations-Posteingang für Ihr Team, um Kundennachrichten effizient zu verwalten.',
	InboxSettingsDetailsTitle: 'Posteingangsdetails',
	InboxSettingsEmailSignatureLabel: 'Standardmäßige E-Mail-Signatur',
	InboxSettingsReplyFormatDesc:
		'Richten Sie Ihre Standardantwortadresse und E-Mail-Signatur so ein, dass sie unabhängig vom Absender der E-Mail stets angezeigt werden.',
	InboxSettingsReplyFormatTitle: 'Antwortformat',
	InboxSettingsSendFromLabel: 'Legen Sie eine Standardantwort fest von ',
	InboxSettingsStaffDesc:
		'Verwalten Sie den Zugriff der Teammitglieder auf diesen freigegebenen Posteingang für eine reibungslose Zusammenarbeit.',
	InboxSettingsStaffTitle: 'Teammitglieder zuweisen',
	InboxSettingsUpdateInboxDetailsFailed: 'Posteingangsdetails konnten nicht aktualisiert werden',
	InboxSettingsUpdateInboxDetailsSuccess: 'Posteingangsdetails erfolgreich aktualisiert',
	InboxSettingsUpdateInboxStaffsFailed: 'Posteingang der Teammitglieder konnte nicht aktualisiert werden',
	InboxSettingsUpdateInboxStaffsSuccess: 'Posteingang der Teammitglieder erfolgreich aktualisiert',
	InboxSettingsUpdateReplyFormatFailed: 'Das Antwortformat konnte nicht aktualisiert werden.',
	InboxSettingsUpdateReplyFormatSuccess: 'Antwortformat erfolgreich aktualisiert',
	InboxShowQuotedText: 'Zitierten Text anzeigen',
	InboxStaffRoleAdminDescription: 'Posteingänge anzeigen, beantworten und verwalten',
	InboxStaffRoleResponderDescription: 'Anzeigen und Antworten',
	InboxStaffRoleViewerDescription: 'Nur anschauen',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Weiter bearbeiten',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Ja, auf Massenversand umschalten',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Sie haben mehr als {count} Empfänger ausgewählt. Möchten Sie es als Massen-E-Mail senden?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Warnung',
	InboxSwitchToOtherInbox: 'Zu einem anderen Posteingang wechseln',
	InboxUndoSendMessageSuccess: 'Senden rückgängig gemacht',
	IncludeLineItems: 'Positionen einschließen',
	IncludeSalesTax: 'Steuerpflichtig',
	IncludesAiSmartPrompt: 'Enthält intelligente KI-Eingabeaufforderungen',
	Incomplete: 'Unvollständig',
	IncreaseIndent: 'Einzug vergrößern',
	IndianHealthServiceFreeStandingFacility: 'Freistehende Einrichtung des Indian Health Service',
	IndianHealthServiceProviderFacility: 'Einrichtung mit Sitz in Indian Health Service',
	Information: 'Information',
	InitialAssessment: 'Erstbewertung',
	InitialSignupPageClientFamilyTitle: 'Kunde oder Familienmitglied',
	InitialSignupPageProviderTitle: 'Gesundheit ',
	InitialTreatment: 'Erstbehandlung',
	Initials: 'Initialen',
	InlineEmbed: 'Inline-Einbettung',
	InputPhraseToConfirm: 'Um zu bestätigen, geben Sie {confirmationPhrase} ein.',
	Insert: 'Einfügen',
	InsertTable: 'Tabelle einfügen',
	InstallCarepatronOnYourIphone1: 'Installieren Sie Carepatron auf Ihrem iOS: Tippen Sie auf',
	InstallCarepatronOnYourIphone2: 'und dann Zum Home-Bildschirm hinzufügen',
	InsufficientCalendarScopesSnackbar:
		'Synchronisierung fehlgeschlagen – bitte erteilen Sie Carepatron Kalenderberechtigungen',
	InsufficientInboxScopesSnackbar:
		'Synchronisierung fehlgeschlagen – bitte erlauben Sie Carepatron E-Mail-Berechtigungen',
	InsufficientScopeErrorCodeSnackbar:
		'Synchronisierung fehlgeschlagen - bitte erteilen Sie Carepatron alle Berechtigungen',
	Insurance: 'Versicherung',
	InsuranceAmount: 'Versicherungsbetrag',
	InsuranceClaim: 'Versicherungsanspruch',
	InsuranceClaimAiChatPlaceholder: 'Fragen Sie nach der Versicherungsschadensmeldung...',
	InsuranceClaimAiClaimNumber: 'Anspruch {number}',
	InsuranceClaimAiSubtitle: 'Versicherungsabrechnung • Anspruchsprüfung',
	InsuranceClaimDeniedSubject:
		'Anspruch {claimNumber}, der bei {payerNumber} {payerName} eingereicht wurde, wurde abgelehnt.',
	InsuranceClaimErrorDescription:
		'Der Anspruch enthält Fehler, die vom Zahler oder der Clearing-Stelle gemeldet wurden. Bitte überprüfen Sie die folgenden Fehlermeldungen und reichen Sie den Anspruch erneut ein.',
	InsuranceClaimErrorGuideLink: 'Leitfaden für Versicherungsansprüche',
	InsuranceClaimErrorTitle: 'Fehler bei der Einreichung von Ansprüchen',
	InsuranceClaimNotFound: 'Versicherungsanspruch nicht gefunden',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Eine Teilzahlung von {paymentAmount}} other {Eine {paymentAmount} Zahlung}} für Forderung {claimNumber} von {payerNumber} {payerName} wurde erfasst',
	InsuranceClaimRejectedSubject:
		'Anspruch {claimNumber}, der an {payerNumber} {payerName} gesendet wurde, wurde abgelehnt',
	InsuranceClaims: 'Versicherungsansprüche',
	InsuranceInformation: 'Versicherungsinformationen',
	InsurancePaid: 'Versicherungszahlung',
	InsurancePayer: 'Versicherungszahler',
	InsurancePayers: 'Versicherungszahler',
	InsurancePayersDescription:
		'Sehen Sie die Zahler, die Ihrem Konto hinzugefügt wurden, und verwalten Sie die Registrierung.',
	InsurancePayment: 'Versicherungszahlung',
	InsurancePoliciesDetailsSubtitle: 'Fügen Sie Kundenversicherungsinformationen hinzu, um Ansprüche zu untermauern.',
	InsurancePoliciesDetailsTitle: 'Richtliniendetails',
	InsurancePoliciesListSubtitle: 'Fügen Sie Kundenversicherungsinformationen hinzu, um Ansprüche zu untermauern.',
	InsurancePoliciesListTitle: 'Versicherungspolicen',
	InsuranceSelfPay: 'Selbstzahler',
	InsuranceType: 'Versicherungsart',
	InsuranceUnpaid: 'Versicherung unbezahlt',
	Intake: 'Aufnahme',
	IntakeExpiredErrorCodeSnackbar:
		'Diese Aufnahme ist abgelaufen. Bitte wenden Sie sich an Ihren Anbieter, um eine weitere Aufnahme zu senden.',
	IntakeNotFoundErrorSnackbar:
		'Diese Aufnahme konnte nicht gefunden werden. Bitte wenden Sie sich an Ihren Anbieter, um eine weitere Aufnahme zu senden.',
	IntakeProcessLearnMoreInstructions: 'Anleitung zum Einrichten Ihrer Aufnahmeformulare',
	IntakeTemplateSelectorPlaceholder:
		'Wählen Sie Formulare und Vereinbarungen aus, die Sie zum Ausfüllen an Ihren Kunden senden möchten',
	Integration: 'Integration',
	IntenseBlur: 'Verwischen Sie Ihren Hintergrund intensiv',
	InteriorDesigner: 'Innenarchitekt',
	InternetBanking: 'Banküberweisung',
	Interval: 'Intervall',
	IntervalDays: 'Intervall (Tage)',
	IntervalHours: 'Intervall (Stunden)',
	Invalid: 'Ungültig',
	InvalidDate: 'Ungültiges Datum',
	InvalidDateFormat: 'Das Datum muss im {format}-Format sein.',
	InvalidDisplayName: 'Anzeige-Name darf nicht {value} enthalten',
	InvalidEmailFormat: 'ungültiges Email-Format',
	InvalidFileType: 'Ungültiger Dateityp',
	InvalidGTMContainerId: 'Ungültiges GTM-Container-ID-Format',
	InvalidPaymentMethodCode: 'Die gewählte Zahlungsart ist ungültig. Bitte wählen Sie eine andere.',
	InvalidPromotionCode: 'Aktionscode ist ungültig',
	InvalidReferralDescription: 'Sie verwenden Carepatron bereits',
	InvalidStatementDescriptor: `Der Anweisungsdeskriptor muss zwischen 5 und 22 Zeichen lang sein und darf nur Buchstaben, Zahlen und Leerzeichen enthalten. Er darf keine  <, >, \\, ', ", * enthalten.`,
	InvalidToken: 'Ungültiges Token',
	InvalidTotpSetupVerificationCode: 'Ungültiger Bestätigungscode.',
	InvalidURLErrorText: 'Dies muss eine gültige URL sein',
	InvalidZoomTokenErrorCodeSnackbar:
		'Der Zoom-Token ist abgelaufen. Bitte verbinden Sie Ihre Zoom-App erneut und versuchen Sie es erneut.',
	Invite: 'Einladen',
	InviteRelationships: 'Beziehungen einladen',
	InviteToPortal: 'Zum Portal einladen',
	InviteToPortalModalDescription: 'Ihrem Kunden wird eine Einladungs-E-Mail zur Anmeldung bei Carepatron gesendet.',
	InviteToPortalModalTitle: 'Lade {name} zum Carepatron Portal ein',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Neuen Benutzer einladen',
	Invited: 'Eingeladen',
	Invoice: 'Rechnung',
	InvoiceColorPickerDescription: 'In der Rechnung zu verwendendes Farbthema',
	InvoiceColorTheme: 'Farbdesign für Rechnungen',
	InvoiceContactDeleted: 'Der Rechnungskontakt wurde gelöscht und diese Rechnung kann nicht aktualisiert werden.',
	InvoiceDate: 'Ausgabedatum',
	InvoiceDetails: 'Rechnungs-Details',
	InvoiceFieldsPlaceholder: 'Suche nach Feldern...',
	InvoiceFrom: 'Rechnung {number} von {fromProvider}',
	InvoiceInvalidCredit: 'Ungültiger Guthabenbetrag, Guthabenbetrag darf den Rechnungsbetrag nicht überschreiten',
	InvoiceNotFoundDescription:
		'Bitte wenden Sie sich an Ihren Anbieter und bitten Sie ihn um weitere Informationen oder um eine erneute Zusendung der Rechnung.',
	InvoiceNotFoundTitle: 'Rechnung nicht gefunden',
	InvoiceNumber: 'Rechnung #',
	InvoiceNumberFormat: 'Rechnung #{number}',
	InvoiceNumberMustEndWithDigit: 'Die Rechnungsnummer muss mit einer Ziffer (0-9) enden',
	InvoicePageHeader: 'Rechnungen',
	InvoicePaidNotificationSubject: 'Rechnung {invoiceNumber} bezahlt',
	InvoiceReminder: 'Rechnungserinnerungen',
	InvoiceReminderSentence:
		'Sende {deliveryType} Erinnerung {interval} {unit} {beforeAfter} Rechnungsfälligkeitsdatum',
	InvoiceReminderSettings: 'Einstellungen für Rechnungserinnerungen',
	InvoiceReminderSettingsInfo: 'Mahnungen gelten nur für Rechnungen, die über Carepatron verschickt werden',
	InvoiceReminders: 'Rechnungserinnerungen',
	InvoiceRemindersInfo:
		'Legen Sie automatische Erinnerungen für Fälligkeitstermine von Rechnungen fest. Erinnerungen gelten nur für Rechnungen, die über Carepatron gesendet werden.',
	InvoiceSettings: 'Rechnungseinstellungen',
	InvoiceStatus: 'Rechnungsstatus',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, USA',
	InvoiceTemplateDescriptionPlaceholder:
		'Fügen Sie Notizen, Banküberweisungsdetails oder Geschäftsbedingungen für alternative Zahlungsarten hinzu',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Selbstständig',
	InvoiceTemplateEthnicityPlaceholder: 'kaukasisch',
	InvoiceTemplateNotFoundDescription:
		'Bitte wenden Sie sich an Ihren Anbieter und bitten Sie ihn um weitere Informationen.',
	InvoiceTemplateNotFoundTitle: 'Rechnungsvorlage nicht gefunden',
	InvoiceTemplates: 'Rechnungsvorlagen',
	InvoiceTemplatesDescription:
		'Passen Sie Ihre Rechnungsvorlagen an, damit sie Ihre Marke widerspiegeln, gesetzliche Anforderungen erfüllen und auf die Präferenzen Ihrer Kunden eingehen – mit unseren benutzerfreundlichen Vorlagen.',
	InvoiceTheme: 'Rechnungsthema',
	InvoiceTotal: 'Rechnungssumme',
	InvoiceUninvoicedAmounts: 'Nicht in Rechnung gestellte Beträge in Rechnung stellen',
	InvoiceUpdateVersionMessage:
		'Zum Bearbeiten dieser Rechnung ist die neueste Version erforderlich. Bitte laden Sie Carepatron neu und versuchen Sie es erneut.',
	Invoices: '{count, plural, one {Rechnung} other {Rechnungen}}',
	InvoicesEmptyStateDescription: 'Es wurden keine Rechnungen gefunden',
	InvoicingAndPayment: 'Fakturierung ',
	Ireland: 'Irland',
	IsA: 'ist ein',
	IsBetween: 'liegt zwischen',
	IsEqualTo: 'ist gleich',
	IsGreaterThan: 'ist größer als',
	IsGreaterThanOrEqualTo: 'ist größer oder gleich',
	IsLessThan: 'ist kleiner als',
	IsLessThanOrEqualTo: 'ist kleiner oder gleich',
	IssueCredit: 'Kredit ausgeben',
	IssueCreditAdjustment: 'Ausgabe einer Kreditkorrektur',
	IssueDate: 'Ausgabedatum',
	Italic: 'Kursiv',
	Items: 'Artikel',
	ItemsAndAdjustments: 'Artikel und Anpassungen',
	ItemsRemaining: '+{count} Artikel verbleiben',
	JobTitle: 'Berufsbezeichnung',
	Join: 'Verbinden',
	JoinCall: 'An Telefongespräch teilnehmen',
	JoinNow: 'Jetzt beitreten',
	JoinProduct: 'Tritt {product} bei',
	JoinVideoCall: 'Am Videoanruf teilnehmen',
	JoinWebinar: 'Webinar beitreten',
	JoinWithVideoCall: 'Tritt {product} bei',
	Journalist: 'Journalist',
	JustMe: 'Nur ich',
	JustYou: 'Nur du',
	Justify: 'Rechtfertigen',
	KeepSeparate: 'Getrennt halten',
	KeepSeparateSuccessMessage: 'Sie haben erfolgreich getrennte Aufzeichnungen für {clientNames} geführt',
	KeepWaiting: 'Warte noch',
	Label: 'Etikett',
	LabelOptional: 'Etikett (optional)',
	LactationConsulting: 'Stillberatung',
	Language: 'Sprache',
	Large: 'Groß',
	LastDxCode: 'Letzter DX-Code',
	LastLoggedIn: 'Letzter Login am {date} um {time}',
	LastMenstrualPeriod: 'Letzte Menstruation',
	LastMonth: 'Im vergangenen Monat',
	LastNDays: 'Letzte {number} Tage',
	LastName: 'Familienname, Nachname',
	LastNameFirstInitial: 'Nachname, Anfangsbuchstabe des Vornamens',
	LastWeek: 'Letzte Woche',
	LastXRay: 'Letztes Röntgenbild',
	LatestVisitOrConsultation: 'Letzter Besuch oder Beratung',
	Lawyer: 'Rechtsanwalt',
	LearnMore: 'Erfahren Sie mehr',
	LearnMoreTipsToGettingStarted: 'Erfahren Sie mehr Tipps für den Einstieg',
	LearnToSetupInbox: 'Erfahren Sie, wie Sie ein Posteingangskonto einrichten',
	Leave: 'Verlassen',
	LeaveCall: 'Anruf hinterlassen',
	LeftAlign: 'Linksbündig',
	LegacyBillingItemsNotAvailable:
		'Einzelne Rechnungsposten sind für diesen Termin noch nicht verfügbar. Sie können ihn trotzdem normal in Rechnung stellen.',
	LegacyBillingItemsNotAvailableTitle: 'Legacy-Abrechnung',
	LegalAndConsent: 'Rechtliches und Einwilligung',
	LegalConsentFormPrimaryText: 'Rechtliche Einwilligung',
	LegalConsentFormSecondaryText: 'Optionen zum Akzeptieren oder Ablehnen',
	LegalGuardian: 'Erziehungsberechtigter',
	Letter: 'Brief',
	LettersCategoryDescription: 'Für die Erstellung von klinischer und administrativer Korrespondenz',
	Librarian: 'Bibliothekar',
	LicenseNumber: 'Amtliches Kennzeichen',
	LifeCoach: 'Life-Coach',
	LifeCoaches: 'Lebensberater',
	Limited: 'Eingeschränkt',
	LineSpacing: 'Zeilen- und Absatzabstand',
	LinearScaleFormPrimaryText: 'Lineare Skalierung',
	LinearScaleFormSecondaryText: 'Skalenoptionen 1-10',
	Lineitems: 'Einzelposten',
	Link: 'Verknüpfung',
	LinkClientFormSearchClientLabel: 'Suche nach einem Kunden',
	LinkClientModalTitle: 'Link zum bestehenden Client',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> Kontaktinformationen werden zu <strong>{existingName}’s</strong> Datensatz hinzugefügt.',
	LinkClientSuccessTitle: 'Erfolgreich mit bestehendem Kontakt verknüpft',
	LinkForCallCopied: 'Link kopiert!',
	LinkToAnExistingClient: 'Link zu einem bestehenden Client',
	LinkToClient: 'Link zum Kunden',
	ListAndTracker: 'Liste/Tracker',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} ist in diesem Anruf}
			other {{attendees} sind in diesem Anruf}
		}`,
	ListStyles: 'Listenstile',
	ListsAndTrackersCategoryDescription: 'Zur Organisation und Nachverfolgung von Arbeit',
	LivingArrangements: 'Wohnverhältnisse',
	LoadMore: 'Mehr laden',
	Loading: 'Wird geladen...',
	LocalizationPanelDescription: 'Verwalten Sie die Einstellungen für Ihre Sprache und Zeitzone',
	LocalizationPanelTitle: 'Sprache und Zeitzone',
	Location: 'Standort',
	LocationDescription:
		'Richten Sie physische und virtuelle Standorte mit spezifischen Adressen, Raumnamen und Arten von virtuellen Räumen ein, um die Planung von Terminen und Videoanrufen zu vereinfachen.',
	LocationNumber: 'Standortnummer',
	LocationOfService: 'Standort der Leistung',
	LocationOfServiceRecommendedActionInfo:
		'Das Hinzufügen eines bestimmten Standorts zu diesem Service kann sich auf Ihre Verfügbarkeit auswirken.',
	LocationRemote: 'Fern',
	LocationType: 'Standorttyp',
	Locations: 'Standorte',
	Lock: 'Sperren',
	Locked: 'Gesperrt',
	LockedNote: 'Gesperrte Notiz',
	LogInToSaveOrAuthoriseCard: 'Melden Sie sich an, um die Karte zu speichern oder zu autorisieren',
	LogInToSaveOrAuthorisePayment: 'Melden Sie sich an, um die Zahlung zu speichern oder zu autorisieren',
	Login: 'Anmeldung',
	LoginButton: 'anmelden',
	LoginEmail: 'Email',
	LoginForgotPasswordLink: 'Passwort vergessen',
	LoginPassword: 'Passwort',
	Logo: 'Logo',
	LogoutAreYouSure: 'Melden Sie sich von diesem Gerät ab.',
	LogoutButton: 'Abmelden',
	London: 'London',
	LongTextAnswer: 'Langtext-Antwort',
	LongTextFormPrimaryText: 'Langtext',
	LongTextFormSecondaryText: 'Absatzformatoptionen',
	Male: 'Männlich',
	Manage: 'Verwalten',
	ManageAllClientTags: 'Alle Client-Tags verwalten',
	ManageAllNoteTags: 'Alle Notiz-Tags verwalten',
	ManageAllTemplateTags: 'Alle Vorlagen-Tags verwalten',
	ManageConnections: 'Verwalten von Verbindungen',
	ManageConnectionsGmailDescription: 'Andere Teammitglieder können Ihr synchronisiertes Gmail nicht sehen.',
	ManageConnectionsGoogleCalendarDescription:
		'Andere Teammitglieder können Ihre synchronisierten Kalender nicht sehen. Kundentermine können nur innerhalb von Carepatron aktualisiert oder gelöscht werden.',
	ManageConnectionsInboxSyncHelperText:
		'Um die Einstellungen für die Posteingangssynchronisierung zu verwalten, gehen Sie bitte zur Posteingangsseite.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Andere Teammitglieder können Ihre synchronisierten Kalender nicht sehen. Kundentermine können nur innerhalb von Carepatron aktualisiert oder gelöscht werden.',
	ManageConnectionsOutlookDescription:
		'Andere Teammitglieder können Ihr synchronisiertes Microsoft Outlook nicht sehen.',
	ManageInboxAccountButton: 'Neuer Posteingang',
	ManageInboxAccountEdit: 'Posteingang verwalten',
	ManageInboxAccountPanelTitle: 'Posteingänge',
	ManageInboxAssignTeamPlaceholder: 'Wählen Sie Teammitglieder für den Posteingangszugriff aus',
	ManageInboxBasicInfoColor: 'Farbe',
	ManageInboxBasicInfoDescription: 'Beschreibung',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Wofür werden Sie oder Ihr Team diesen Posteingang verwenden?',
	ManageInboxBasicInfoName: 'Posteingangsname',
	ManageInboxBasicInfoNamePlaceholder: 'ZB Kundensupport, Admin',
	ManageInboxConnectAppAlreadyConnectedError:
		'Der Kanal, den Sie verbinden wollten, ist bereits mit Carepatron verbunden.',
	ManageInboxConnectAppConnect: 'Verbinden',
	ManageInboxConnectAppConnectedInfo: 'Mit einem Konto verbunden',
	ManageInboxConnectAppContinue: 'Weitermachen',
	ManageInboxConnectAppEmail: 'Email',
	ManageInboxConnectAppSignInWith: 'Anmelden mit',
	ManageInboxConnectAppSubtitle:
		'Verbinden Sie Ihre Apps, um alle Ihre Nachrichten nahtlos an einem zentralen Ort zu senden, zu empfangen und zu verfolgen.',
	ManageInboxNewInboxTitle: 'Neuer Posteingang',
	ManagePlan: 'Plan verwalten',
	ManageProfile: 'Profil verwalten',
	ManageReferralsModalDescription:
		'Helfen Sie uns, unsere Gesundheitsplattform bekannt zu machen und verdienen Sie Prämien.',
	ManageReferralsModalTitle: 'Empfehlen Sie einen Freund und verdienen Sie Prämien!',
	ManageStaffRelationshipsAddButton: 'Verwalten von Beziehungen',
	ManageStaffRelationshipsEmptyStateText: 'Keine Beziehungen hinzugefügt',
	ManageStaffRelationshipsModalDescription:
		'Durch die Auswahl von Clients werden neue Beziehungen hinzugefügt. Durch die Aufhebung der Auswahl werden vorhandene Beziehungen entfernt.',
	ManageStaffRelationshipsModalTitle: 'Verwalten von Beziehungen',
	ManageStatuses: 'Status verwalten',
	ManageStatusesActiveStatusHelperText: 'Mindestens ein aktiver Status ist erforderlich',
	ManageStatusesDescription:
		'Passen Sie Ihre Statusbeschriftungen an und wählen Sie Farben aus, die zu Ihrem Arbeitsablauf passen.',
	ManageStatusesSuccessSnackbar: 'Status erfolgreich aktualisiert',
	ManageTags: 'Tags verwalten',
	ManageTaskAttendeeStatus: 'Termine verwalten',
	ManageTaskAttendeeStatusDescription: 'Passen Sie Ihre Terminstatus an, um sie an Ihren Workflow anzupassen.',
	ManageTaskAttendeeStatusHelperText: 'Mindestens ein Status ist erforderlich',
	ManageTaskAttendeeStatusSubtitle: 'Benutzerdefinierte Status',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Handbuch',
	ManualAppointment: 'Manuelle Terminvereinbarung',
	ManualPayment: 'Manuelle Zahlung',
	ManuallyTypeLocation: 'Standort manuell eingeben',
	MapColumns: 'Spaltenkarte',
	MappingRequired: 'Mapping erforderlich',
	MarkAllAsRead: 'Alles als gelesen markieren',
	MarkAsCompleted: 'Als abgeschlossen markieren',
	MarkAsManualSubmission: 'Als abgegeben markieren',
	MarkAsPaid: 'Als bezahlt markieren',
	MarkAsRead: 'als gelesen markieren',
	MarkAsUnpaid: 'Als unbezahlt markieren',
	MarkAsUnread: 'als ungelesen markieren',
	MarkAsVoid: 'Als ungültig markieren',
	Marker: 'Marker',
	MarketingManager: 'Marketing Manager',
	MassageTherapist: 'Masseur',
	MassageTherapists: 'Massagetherapeuten',
	MassageTherapy: 'Nachrichtentherapie',
	MaxBookingTimeDescription1: 'Kunden können bis zu',
	MaxBookingTimeDescription2: 'in die Zukunft',
	MaxBookingTimeLabel: '{timePeriod} im Voraus',
	MaxCapacity: 'maximale Kapazität',
	Maximize: 'Maximieren',
	MaximumAttendeeLimit: 'Maximalgrenze',
	MaximumBookingTime: 'Maximale Buchungszeit',
	MaximumBookingTimeError: 'Maximale Buchungszeit darf {valueUnit} nicht überschreiten.',
	MaximumMinimizedPanelsReachedDescription:
		'Sie können maximal {count} Seitenleisten gleichzeitig minimieren. Durch Fortfahren wird die am frühesten minimierte Leiste geschlossen. Möchten Sie fortfahren?',
	MaximumMinimizedPanelsReachedTitle: 'Sie haben zu viele Panels geöffnet.',
	MechanicalEngineer: 'Maschinenbauingenieur',
	MediaGallery: 'Medien Gallerie',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Medicaid-Anbieternummer',
	MedicalAssistant: 'Medizinischer Assistent',
	MedicalCoder: 'Medizinischer Kodierer',
	MedicalDoctor: 'Arzt',
	MedicalIllustrator: 'Medizinischer Illustrator',
	MedicalInterpreter: 'Medizinischer Dolmetscher',
	MedicalTechnologist: 'Medizintechniker',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Medicare-Anbieternummer',
	Medicine: 'Medizin',
	Medium: 'Medium',
	Meeting: 'Treffen',
	MeetingEnd: 'Besprechung beenden',
	MeetingEnded: 'Sitzung beendet',
	MeetingHost: 'Gastgeber des Meetings',
	MeetingLowerHand: 'Untere Hand',
	MeetingOpenChat: 'Chat öffnen',
	MeetingPersonRaisedHand: '{name} hob ihren Arm.',
	MeetingRaiseHand: 'Hand heben',
	MeetingReady: 'Bereit für Meetings',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Treffen',
	MemberId: 'Mitglieds-ID',
	MentalHealth: 'Psychische Gesundheit',
	MentalHealthPractitioners: 'Psychiatrische Fachkräfte',
	MentalHealthProfessional: 'Fachkraft für psychische Gesundheit',
	Merge: 'Verschmelzen',
	MergeClientRecords: 'Klientenaufzeichnungen zusammenführen',
	MergeClientRecordsDescription:
		'Das Zusammenführen von Kundenaufzeichnungen kombiniert alle ihre Daten, einschließlich:',
	MergeClientRecordsDescription2:
		'Möchten Sie mit der Zusammenführung fortfahren? Diese Aktion kann nicht rückgängig gemacht werden',
	MergeClientRecordsItem1: 'Notizen und Dokumente',
	MergeClientRecordsItem2: 'Termine',
	MergeClientRecordsItem3: 'Rechnungen',
	MergeClientRecordsItem4: 'Gespräche',
	MergeClientsSuccess: 'Erfolgreich Kundenakte zusammengeführt',
	MergeLimitExceeded: 'Sie können maximal 4 Clients gleichzeitig zusammenführen.',
	Message: 'Nachricht',
	MessageAttachments: '{total} Anhänge',
	Method: 'Verfahren',
	MfaAvailabilityDisclaimer:
		'MFA ist nur für E-Mail- und Passwort-Logins verfügbar. Um Änderungen an Ihren MFA-Einstellungen vorzunehmen, melden Sie sich mit Ihrer E-Mail-Adresse und Ihrem Passwort an.',
	MfaDeviceLostPanelDescription:
		'Alternativ können Sie Ihre Identität auch durch den Erhalt eines Codes per E-Mail verifizieren.',
	MfaDeviceLostPanelTitle: 'Haben Sie Ihr MFA-Gerät verloren?',
	MfaDidntReceiveEmailCode: 'Keinen Code erhalten? Support kontaktieren',
	MfaEmailOtpSendFailureSnackbar: 'Das Senden des OTP-E-Mail-Codes ist fehlgeschlagen.',
	MfaEmailOtpSentSnackbar: 'Ein Code wurde an {maskedEmail} gesendet.',
	MfaEmailOtpVerificationFailedSnackbar: 'Die Bestätigung des E-Mail-OTP ist fehlgeschlagen.',
	MfaHasBeenSetUpText: 'Sie haben MFA eingerichtet',
	MfaPanelDescription:
		'Sichern Sie Ihr Konto, indem Sie die Multi-Faktor-Authentifizierung (MFA) für eine zusätzliche Schutzebene aktivieren. Bestätigen Sie Ihre Identität über eine sekundäre Methode, um unbefugten Zugriff zu verhindern.',
	MfaPanelNotAuthorizedError: 'Sie müssen mit dem Benutzernamen angemeldet sein ',
	MfaPanelRecommendationDescription:
		'Sie haben sich vor Kurzem mit einer alternativen Methode zur Bestätigung Ihrer Identität angemeldet. Um die Sicherheit Ihres Kontos zu gewährleisten, sollten Sie ein neues MFA-Gerät einrichten.',
	MfaPanelRecommendationTitle: '**Empfohlen:** Aktualisieren Sie Ihr MFA-Gerät',
	MfaPanelTitle: 'Multi-Faktor-Authentifizierung (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'Sie müssen Ihre E-Mail bestätigen, bevor Sie Ihre MFA-Einstellungen aktualisieren können.',
	MfaRecommendationBannerDescription:
		'Sie haben sich vor Kurzem mit einer alternativen Methode zur Bestätigung Ihrer Identität angemeldet. Um die Sicherheit Ihres Kontos zu gewährleisten, sollten Sie ein neues MFA-Gerät einrichten.',
	MfaRecommendationBannerPrimaryAction: 'Einrichten von MFA',
	MfaRecommendationBannerTitle: 'Empfohlen',
	MfaRemovedSnackbarTitle: 'MFA wurde entfernt.',
	MfaSendEmailCode: 'Code senden',
	MfaVerifyIdentityLostDeviceButton: 'Ich habe den Zugriff auf mein MFA-Gerät verloren',
	MfaVerifyYourIdentityPanelDescription:
		'Suchen Sie in Ihrer Authentifizierungs-App nach dem Code und geben Sie ihn unten ein.',
	MfaVerifyYourIdentityPanelTitle: 'Bestätigen Sie Ihre Identität',
	MicCamWarningMessage:
		'Entsperren Sie Kamera und Mikrofon, indem Sie in der Adressleiste des Browsers auf blockierte Symbole klicken.',
	MicCamWarningTitle: 'Kamera und Mikrofon sind blockiert',
	MicOff: 'Mikrofon ist ausgeschaltet',
	MicOn: 'Mikrofon ist eingeschaltet',
	MicSource: 'Mikrofonquelle',
	MicWarningMessage: 'Bei Ihrem Mikrofon wurde ein Problem festgestellt',
	Microphone: 'Mikrofon',
	MicrophonePermissionBlocked: 'Mikrofonzugriff blockiert',
	MicrophonePermissionBlockedDescription:
		'Aktualisiere deine Mikrofonberechtigungen, um mit der Aufnahme zu beginnen.',
	MicrophonePermissionError:
		'Bitte erteilen Sie in Ihren Browsereinstellungen die Mikrofonberechtigung, um fortzufahren',
	MicrophonePermissionPrompt: 'Bitte erlauben Sie den Mikrofonzugriff, um fortzufahren',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Outlook-Kalenderfarbe',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Teams',
	MiddleEast: 'Naher Osten',
	MiddleName: 'Zweiter Vorname',
	MiddleNames: 'Zweiter Vorname',
	Midwife: 'Hebamme',
	Midwives: 'Hebammen',
	Milan: 'Mailand',
	MinBookingTimeDescription1: 'Kunden können nicht planen innerhalb',
	MinBookingTimeDescription2: 'der Startzeit eines Termins',
	MinBookingTimeLabel: '{timePeriod} vor Termin',
	MinCancellationTimeEditModeDescription: 'Legen Sie fest, wie viele Stunden ein Kunde ohne Strafe stornieren kann',
	MinCancellationTimeUnset: 'Keine Mindeststornierungsfrist festgelegt',
	MinCancellationTimeViewModeDescription: 'Stornierungsfrist ohne Strafe',
	MinMaxBookingTimeUnset: 'Keine Zeit eingestellt',
	Minimize: 'Minimieren',
	MinimizeConfirmationDescription:
		'Sie haben ein aktives minimiertes Panel. Wenn Sie fortfahren, wird es geschlossen und Sie könnten ungespeicherte Daten verlieren.',
	MinimizeConfirmationTitle: 'Minimiertes Panel schließen?',
	MinimumBookingTime: 'Mindestbuchungszeit',
	MinimumCancellationTime: 'Mindeststornierungszeit',
	MinimumPaymentError: 'Eine Mindestgebühr von {minimumAmount} ist für Online-Zahlungen erforderlich',
	MinuteAbbreviated: 'Min.',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minütlich',
	MinutesPlural: '{age, plural, one {# Minute} other {# Minuten}}',
	MiscellaneousInformation: 'Sonstige Informationen',
	MissingFeatures: 'Fehlende Funktionen',
	MissingPaymentMethod:
		'Bitte fügen Sie Ihrem Abonnement eine Zahlungsmethode hinzu, um weitere Mitarbeiter hinzuzufügen.',
	MobileNumber: 'Handynummer',
	MobileNumberOptional: 'Mobilnummer (optional)',
	Modern: 'Modern',
	Modifiers: 'Modifikatoren',
	ModifiersPlaceholder: 'Modifikatoren',
	Monday: 'Montag',
	Month: 'Monat',
	Monthly: 'Monatlich',
	MonthlyCost: 'Monatliche Kosten',
	MonthlyOn: 'Monatlich am {date}',
	MonthsPlural: '{age, plural, one {# Monat} other {# Monate}}',
	More: 'Mehr',
	MoreActions: 'Mehr Aktionen',
	MoreSettings: 'Mehr Einstellungen',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Meist genutzt',
	MostDownloaded: 'Am meisten heruntergeladen',
	MostPopular: 'Am beliebtesten',
	Mother: 'Mutter',
	MotherInLaw: 'Schwiegermutter',
	MoveDown: 'Sich abwärts bewegen',
	MoveInboxConfirmationDescription:
		'Wenn diese App-Verbindung neu zugewiesen wird, wird sie aus dem <strong>{currentInboxName}</strong>-Posteingang entfernt.',
	MoveTemplateToFolder: 'Verschiebe `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} wurde nach {folderTitle} verschoben.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Erfolgreich in den Standard-Aufnahmeordner verschoben',
	MoveTemplateToNewFolder: 'Erstelle einen neuen Ordner, um diesen Eintrag zu verschieben.',
	MoveToChosenFolder:
		'Wählen Sie einen Ordner, um diesen Eintrag zu verschieben. Sie können bei Bedarf einen neuen Ordner erstellen.',
	MoveToFolder: 'In den Ordner verschieben',
	MoveToInbox: 'In den Posteingang verschieben',
	MoveToNewFolder: 'In einen neuen Ordner verschieben',
	MoveToSelectedFolder:
		'Nach dem Verschieben wird der Artikel unter dem ausgewählten Ordner organisiert und erscheint nicht mehr an seinem aktuellen Ort.',
	MoveUp: 'Nach oben',
	MultiSpeciality: 'Mehrere Spezialisierungen',
	MultipleChoiceFormPrimaryText: 'Mehrfachauswahl',
	MultipleChoiceFormSecondaryText: 'Wählen Sie mehrere Optionen',
	MultipleChoiceGridFormPrimaryText: 'Multiple-Choice-Raster',
	MultipleChoiceGridFormSecondaryText: 'Auswählen von Optionen aus einer Matrix',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Musiktherapeut',
	MustContainOneLetterError: 'Muss mindestens einen Buchstaben enthalten',
	MustEndWithANumber: 'Muss mit einer Zahl enden',
	MustHaveAtLeastXItems: 'Muss mindestens {count, plural, one {# Element} other {# Elemente}} haben',
	MuteAudio: 'Audio stummschalten',
	MuteEveryone: 'Alle stummschalten',
	MyAvailability: 'Meine Verfügbarkeit',
	MyGallery: 'Meine Gallerie',
	MyPortal: 'Mein Portal',
	MyRelationships: 'Meine Beziehungen',
	MyTemplates: 'Team-Vorlagen',
	MyofunctionalTherapist: 'Myofunktionelle Therapeutin',
	NCalifornia: 'Nordkalifornien',
	NPI: 'NPI',
	NVirginia: 'Nord-Virginia',
	Name: 'Name',
	NameIsRequired: 'Name ist erforderlich',
	NameMustNotBeAWebsite: 'Der Name darf keine Website sein',
	NameMustNotBeAnEmail: 'Der Name darf keine E-Mail-Adresse sein',
	NameMustNotContainAtSign: 'Der Name darf kein @-Zeichen enthalten',
	NameMustNotContainHTMLTags: 'Der Name darf keine HTML-Tags enthalten',
	NameMustNotContainSpecialCharacters: 'Der Name darf keine Sonderzeichen enthalten',
	NameOnCard: 'Name auf der Karte',
	NationalProviderId: 'Nationale Anbieterkennung (NPI)',
	NaturopathicDoctor: 'Naturheilkundlicher Arzt',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Abonnementeinstellungen',
	NavigateToWorkspaceSettings: 'Workspace-Einstellungen',
	NavigateToYourTeam: 'Manage team',
	NavigationDrawerBilling: 'Rechnungsstellung',
	NavigationDrawerBillingInfo: 'Rechnungsinformationen, Rechnungen und Stripe',
	NavigationDrawerCommunication: 'Kommunikation',
	NavigationDrawerCommunicationInfo: 'Benachrichtigungen und Vorlagen',
	NavigationDrawerInsurance: 'Versicherung',
	NavigationDrawerInsuranceInfo: 'Versicherungszahler und Ansprüche',
	NavigationDrawerInvoices: 'Abrechnung',
	NavigationDrawerPersonal: 'Mein Profil',
	NavigationDrawerPersonalInfo: 'Deine Persönlichen Details',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Einstellungen',
	NavigationDrawerScheduling: 'Terminplanung',
	NavigationDrawerSchedulingInfo: 'Leistungsdetails und Buchungen',
	NavigationDrawerSettings: 'Einstellungen',
	NavigationDrawerTemplates: 'Vorlagen',
	NavigationDrawerTemplatesV2: 'Vorlagen V2',
	NavigationDrawerTrash: 'Papierkorb',
	NavigationDrawerTrashInfo: 'Wiederherstellen gelöschter Elemente',
	NavigationDrawerWorkspace: 'Arbeitsbereichseinstellungen',
	NavigationDrawerWorkspaceInfo: 'Abonnement- und Arbeitsbereichsinformationen',
	NegativeBalanceNotSupported: 'Negative Kontostände werden nicht unterstützt',
	Nephew: 'Neffe',
	NetworkQualityFair: 'Mittlere Verbindung',
	NetworkQualityGood: 'Gute Verbindung',
	NetworkQualityPoor: 'Schlechte Verbindung',
	Neurologist: 'Neurologe',
	Never: 'Niemals',
	New: 'Neu',
	NewAppointment: 'Neuer Termin',
	NewClaim: 'Neuer Anspruch',
	NewClient: 'Neuer Kunde',
	NewClientNextStepsModalAddAnotherClient: 'Einen weiteren Client hinzufügen',
	NewClientNextStepsModalBookAppointment: 'Termin buchen',
	NewClientNextStepsModalBookAppointmentDescription:
		'Buchen Sie einen bevorstehenden Termin oder erstellen Sie eine Aufgabe.',
	NewClientNextStepsModalCompleteBasicInformation: 'Vollständiger Kundendatensatz',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Fügen Sie Kundeninformationen hinzu und erfassen Sie die nächsten Schritte.',
	NewClientNextStepsModalCreateInvoice: 'Rechnung erstellen',
	NewClientNextStepsModalCreateInvoiceDescription:
		'Fügen Sie Zahlungsinformationen des Kunden hinzu oder erstellen Sie eine Rechnung.',
	NewClientNextStepsModalCreateNote: 'Notiz erstellen oder Dokument hochladen',
	NewClientNextStepsModalCreateNoteDescription: 'Erfassen Sie Kundennotizen und Dokumentation.',
	NewClientNextStepsModalDescription:
		'Nachdem Sie einen Kundendatensatz erstellt haben, sind hier einige Aktionen aufgeführt, die Sie ausführen müssen.',
	NewClientNextStepsModalSendIntake: 'Aufnahme senden',
	NewClientNextStepsModalSendIntakeDescription:
		'Erfassen Sie Kundeninformationen und senden Sie zusätzliche Formulare zum Ausfüllen und Unterschreiben.',
	NewClientNextStepsModalSendMessage: 'Nachricht senden',
	NewClientNextStepsModalSendMessageDescription: 'Verfassen und senden Sie eine Nachricht an Ihren Kunden.',
	NewClientNextStepsModalTitle: 'Nächste Schritte',
	NewClientSuccess: 'Neuer Kunde erfolgreich angelegt',
	NewClients: 'Neue Kunden',
	NewConnectedApp: 'Neue verbundene App',
	NewContact: 'Neuer Kontakt',
	NewContactNextStepsModalAddRelationship: 'Beziehung hinzufügen',
	NewContactNextStepsModalAddRelationshipDescription:
		'Verknüpfen Sie diesen Kontakt mit zugehörigen Kunden oder Gruppen.',
	NewContactNextStepsModalBookAppointment: 'Termin buchen',
	NewContactNextStepsModalBookAppointmentDescription:
		'Buchen Sie einen kommenden Termin oder erstellen Sie eine Aufgabe.',
	NewContactNextStepsModalCompleteProfile: 'Komplettes Profil',
	NewContactNextStepsModalCompleteProfileDescription:
		'Kontaktinformationen hinzufügen und nächste Schritte festhalten.',
	NewContactNextStepsModalCreateNote: 'Notiz erstellen oder Dokument hochladen',
	NewContactNextStepsModalCreateNoteDescription: 'Notizen und Dokumentation von Kunden erfassen.',
	NewContactNextStepsModalDescription:
		'Hier sind einige Aktionen, die Sie jetzt ausführen können, nachdem Sie einen Kontakt erstellt haben.',
	NewContactNextStepsModalInviteToPortal: 'Einladung zum Portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Senden Sie eine Einladung zum Zugriff auf das Portal.',
	NewContactNextStepsModalTitle: 'Nächste Schritte',
	NewContactSuccess: 'Neuer Kontakt erfolgreich erstellt',
	NewDateOverrideButton: 'Neue Datumsüberschreibung',
	NewDiagnosis: 'Diagnose hinzufügen',
	NewField: 'Neues Feld',
	NewFolder: 'Neuer Ordner',
	NewInvoice: 'Neue Rechnung',
	NewLocation: 'Neuen Ort',
	NewLocationFailure: 'Neuer Standort konnte nicht erstellt werden',
	NewLocationSuccess: 'Neuer Standort erfolgreich erstellt',
	NewManualPayer: 'Neuer manueller Zahler',
	NewNote: 'Neue Notiz',
	NewNoteCreated: 'Neue Notiz erfolgreich erstellt',
	NewPassword: 'Neues Kennwort',
	NewPayer: 'Neuer Zahler',
	NewPaymentMethod: 'Neue Zahlungsmethode',
	NewPolicy: 'Neue Richtlinie',
	NewRelationship: 'Neue Beziehung',
	NewReminder: 'Neue Erinnerung',
	NewSchedule: 'Neue Terminplanung',
	NewSection: 'Neuer Abschnitt',
	NewSectionOld: 'Neuer Abschnitt [Alt]',
	NewSectionWithGrid: 'Neuer Abschnitt mit Raster',
	NewService: 'Neuer Service',
	NewServiceFailure: 'Neuer Dienst konnte nicht erstellt werden',
	NewServiceSuccess: 'Neuer Dienst erfolgreich erstellt',
	NewStatus: 'Neuer Status',
	NewTask: 'Neue Aufgabe',
	NewTaxRate: 'Neuer Steuersatz',
	NewTeamMemberNextStepsModalAssignClients: 'Kunden zuweisen',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Weisen Sie bestimmte Kunden Ihrem Teammitglied zu.',
	NewTeamMemberNextStepsModalAssignServices: 'Dienste zuordnen',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Verwalten Sie ihre zugewiesenen Dienstleistungen und passen Sie die Preise bei Bedarf an.',
	NewTeamMemberNextStepsModalBookAppointment: 'Termin buchen',
	NewTeamMemberNextStepsModalBookAppointmentDescription:
		'Vereinbaren Sie einen zukünftigen Termin oder erstellen Sie eine Aufgabe.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Komplettes Profil',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Fügen Sie Details über Ihr Teammitglied hinzu, um sein Profil zu vervollständigen.',
	NewTeamMemberNextStepsModalDescription:
		'Hier sind einige Aktionen, die Sie jetzt ausführen können, nachdem Sie ein Teammitglied erstellt haben.',
	NewTeamMemberNextStepsModalEditPermissions: 'Bearbeitungsberechtigungen',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Passen Sie ihre Zugriffsebenen an, um sicherzustellen, dass sie über die richtigen Berechtigungen verfügen.',
	NewTeamMemberNextStepsModalSetAvailability: 'Verfügbarkeit festlegen',
	NewTeamMemberNextStepsModalSetAvailabilityDescription:
		'Konfigurieren Sie ihre Verfügbarkeit, um Zeitpläne zu erstellen.',
	NewTeamMemberNextStepsModalTitle: 'Nächste Schritte',
	NewTemplateFolderDescription: 'Erstellen Sie einen neuen Ordner, um Ihre Dokumentation zu organisieren.',
	NewUIUpdateBannerButton: 'App neu laden',
	NewUIUpdateBannerTitle: 'Es steht ein neues Update bereit!',
	NewZealand: 'Neuseeland',
	Newest: 'Neueste',
	NewestUnreplied: 'Neueste unbeantwortete',
	Next: 'Nächste',
	NextInvoiceIssueDate: 'Nächstes Rechnungsdatum',
	NextNDays: 'Nächste {number} Tage',
	Niece: 'Nichte',
	No: 'NEIN',
	NoAccessGiven: 'Kein Zugriff gewährt',
	NoActionConfigured: 'Keine Aktion konfiguriert',
	NoActivePolicies: 'Keine aktiven Richtlinien',
	NoActiveReferrals: 'Sie haben keine aktiven Empfehlungen',
	NoAppointmentsFound: 'Es wurden keine Termine gefunden',
	NoAppointmentsHeading: 'Verwalten von Kundenterminen und Aktivitäten',
	NoArchivedPolicies: 'Keine archivierten Richtlinien',
	NoAvailableTimes: 'Keine verfügbaren Zeiten gefunden.',
	NoBillingItemsFound: 'Keine Abrechnungselemente gefunden',
	NoCalendarsSynced: 'Keine Kalender synchronisiert',
	NoClaimsFound: 'Keine Ansprüche gefunden',
	NoClaimsHeading: 'Vereinfachen Sie die Einreichung von Erstattungsansprüchen',
	NoClientsHeading: 'Fassen Sie die Aufzeichnungen Ihrer Kunden zusammen',
	NoCompletedReferrals: 'Sie haben keine vollständigen Empfehlungen',
	NoConnectionsHeading: 'Optimieren Sie Ihre Kundenkommunikation',
	NoContactsGivenAccess: 'Kein Kunde oder Kontakt hat Zugriff auf diese Notiz erhalten',
	NoContactsHeading: 'Bleiben Sie in Verbindung mit denen, die Ihre Praxis unterstützen',
	NoCopayOrCoinsurance: 'Keine Zuzahlung oder Selbstbeteiligung',
	NoCustomServiceSchedule:
		'Kein benutzerdefinierter Zeitplan festgelegt – die Verfügbarkeit hängt von der Verfügbarkeit des Teammitglieds ab',
	NoDescription: 'Keine Beschreibung',
	NoDocumentationHeading: 'Erstellen und speichern Sie Notizen sicher',
	NoDuplicateRecordsHeading: 'Ihr Kundendatensatz ist frei von Duplikaten',
	NoEffect: 'Kein Effekt',
	NoEnrolmentProfilesFound: 'Keine Anmeldeprofile gefunden',
	NoGlossaryItems: 'Keine Glossareinträge',
	NoInvitedReferrals: 'Sie haben keine eingeladenen Empfehlungen',
	NoInvoicesFound: 'Keine Rechnungen gefunden',
	NoInvoicesHeading: 'Automatisieren Sie Ihre Rechnungen und Zahlungen',
	NoLimit: 'Keine Begrenzung',
	NoLocationsFound: 'Es wurden keine Standorte gefunden',
	NoLocationsWillBeAdded: 'Es werden keine Standorte hinzugefügt.',
	NoNoteFound: 'Keine Notiz gefunden',
	NoPaymentMethods: 'Sie haben keine Zahlungsmethoden gespeichert. Sie können beim Bezahlvorgang eine hinzufügen.',
	NoPermissionError: 'Sie haben keine Berechtigung',
	NoPermissions: 'Sie haben keine Berechtigung, diese Seite anzuzeigen',
	NoPolicy: 'Keine Stornierungsbedingungen hinzugefügt',
	NoRecordsHeading: 'Personalisieren Sie die Aufzeichnungen Ihrer Kunden',
	NoRecordsToDisplay: 'Keine {resource} zum Anzeigen',
	NoRelationshipsHeading: 'Bleiben Sie in Verbindung mit denen, die Ihren Kunden unterstützen',
	NoRemindersFound: 'Keine Erinnerungen gefunden',
	NoResultsFound: 'Keine Ergebnisse gefunden',
	NoResultsFoundDescription: 'Wir können keine Elemente finden, die Ihrer Suche entsprechen',
	NoServicesAdded: 'Keine Dienste hinzugefügt',
	NoServicesApplied: 'Keine Dienste angewendet',
	NoServicesWillBeAdded: 'Es werden keine Dienste hinzugefügt.',
	NoTemplate: 'Sie haben keine Übungsvorlagen gespeichert',
	NoTemplatesHeading: 'Erstellen Sie Ihre eigenen Vorlagen',
	NoTemplatesInFolder: 'Keine Vorlagen in diesem Ordner',
	NoTitle: 'Kein Titel',
	NoTrashItemsHeading: 'Kein gelöschtes Element gefunden',
	NoTriggerConfigured: 'Kein Trigger konfiguriert',
	NoUnclaimedItemsFound: 'Keine nicht abgeholten Artikel gefunden.',
	NonAiTemplates: 'Nicht-KI-Vorlagen',
	None: 'Keiner',
	NotAvailable: 'Nicht verfügbar',
	NotCovered: 'Nicht abgedeckt',
	NotFoundSnackbar: 'Ressource nicht gefunden.',
	NotRequiredField: 'Nicht erforderliches',
	Note: 'Notiz',
	NoteDuplicateSuccess: 'Notiz erfolgreich dupliziert',
	NoteEditModeViewSwitcherDescription: 'Notiz erstellen und bearbeiten',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} hat das Formular {noteTitle} eingereicht',
	NoteLockSuccess: '{title} wurde gesperrt',
	NoteModalAttachmentButton: 'Anhänge hinzufügen',
	NoteModalPhotoButton: 'Fotos hinzufügen/aufnehmen',
	NoteModalTrascribeButton: 'Live-Audio transkribieren',
	NoteResponderModeViewSwitcherDescription: 'Senden Sie Formulare und überprüfen Sie die Antworten',
	NoteResponderModeViewSwitcherTooltipTitle: 'Beantworten und übermitteln Sie Formulare im Namen Ihrer Kunden',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Als Kunde Formulare ausfüllen und übermitteln',
	NoteUnlockSuccess: '{title} wurde freigeschaltet',
	NoteViewModeViewSwitcherDescription: 'Nur Lesezugriff',
	Notes: 'Anmerkungen',
	NotesAndForms: 'Hinweise und Formulare',
	NotesCategoryDescription: 'Zur Dokumentation von Kundeninteraktionen',
	NothingToSeeHere: 'Es gibt hier nichts zu sehen',
	Notification: 'Benachrichtigung',
	NotificationIgnoredMessage: 'Alle {notificationType} Benachrichtigungen werden ignoriert',
	NotificationRestoredMessage: 'Alle {notificationType} Benachrichtigungen wiederhergestellt',
	NotificationSettingBillingDescription:
		'Erhalten Sie Benachrichtigungen über Zahlungsaktualisierungen und Erinnerungen von Kunden.',
	NotificationSettingBillingTitle: 'Abrechnung und Zahlung',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} nur} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Erhalten Sie Benachrichtigungen über Zahlungsaktualisierungen und Erinnerungen von Kunden.',
	NotificationSettingClientDocumentationTitle: 'Kunde und Dokumentation',
	NotificationSettingCommunicationsDescription:
		'Erhalten Sie Benachrichtigungen für den Posteingang und Updates von Ihren verbundenen Kanälen',
	NotificationSettingCommunicationsTitle: 'Kommunikation',
	NotificationSettingEmail: 'E-Mail',
	NotificationSettingInApp: 'In-App',
	NotificationSettingPanelDescription:
		'Wählen Sie die Benachrichtigungen, die Sie für Aktivitäten und Empfehlungen erhalten möchten.',
	NotificationSettingPanelTitle: 'Benachrichtigungseinstellungen',
	NotificationSettingSchedulingDescription:
		'Erhalten Sie Benachrichtigungen, wenn ein Teammitglied oder ein Kunde seinen Termin bucht, verschiebt oder storniert.',
	NotificationSettingSchedulingTitle: 'Planung',
	NotificationSettingUpdateSuccess: 'Benachrichtigungseinstellungen erfolgreich aktualisiert',
	NotificationSettingWhereYouReceiveNotifications: 'Wo Sie diese Benachrichtigungen erhalten möchten',
	NotificationSettingWorkspaceDescription:
		'Erhalten Sie Benachrichtigungen über Systemänderungen, Probleme, Datenübertragungen und Abonnement-Erinnerungen.',
	NotificationSettingWorkspaceTitle: 'Arbeitsbereich',
	NotificationTemplateUpdateFailed: 'Die Aktualisierung der Benachrichtigungsvorlage ist fehlgeschlagen.',
	NotificationTemplateUpdateSuccess: 'Benachrichtigungsvorlage erfolgreich aktualisiert',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Möchten Sie den Teilnehmern eine E-Mail mit der Stornierungsbenachrichtigung senden?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Stornierung senden',
	NotifyAttendeesOfTaskConfirmationModalDescription: 'Möchten Sie den Teilnehmern eine Bestätigungs-E-Mail senden?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Bestätigung senden',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Möchten Sie den Teilnehmern Stornierungs-E-Mails senden?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {hat} other {haben}} keine E-Mail-Adresse und erhalten daher keine automatischen Benachrichtigungen und Erinnerungen.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {hat} other {haben}} keine E-Mail-Adresse und erhalten daher keine automatischen Benachrichtigungen und Erinnerungen.',
	NotifyAttendeesOfTaskModalTitle: 'Möchten Sie den Teilnehmern eine Benachrichtigungs-E-Mail senden?',
	NotifyAttendeesOfTaskSnackbar: 'Benachrichtigung senden',
	NuclearMedicineTechnologist: 'Nuklearmedizintechniker',
	NumberOfClaims: '{number, plural, one {# Anspruch} other {# Ansprüche}}',
	NumberOfClients: '{number, plural, one {# Kunde} other {# Kunden}}',
	NumberOfContacts: '{number, plural, one {# Kontakt} other {# Kontakte}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {Eintrag} other {Einträge}} gefunden',
	NumberOfErrors: '{count, plural, one {# Fehler} other {# Fehler}}',
	NumberOfInvoices: '{number, plural, one {# Rechnung} other {# Rechnungen}}',
	NumberOfLineitemsToCredit:
		'Sie haben <mark>{count} {count, plural, one {Zeilenelement} other {Zeilenelemente}}</mark> für die Ausstellung einer Gutschrift.',
	NumberOfPayments: '{number, plural, one {# Zahlung} other {# Zahlungen}}',
	NumberOfRelationships: '{number, plural, one {# Beziehung} other {# Beziehungen}}',
	NumberOfResources: '{number, plural, one {# Ressource} other {# Ressourcen}}',
	NumberOfTeamMembers: '{number, plural, one {# Teammitglied} other {# Teammitglieder}}',
	NumberOfTrashItems: '{number, plural, one {# Element} other {# Elemente}}',
	NumberOfUninvoicedAmounts:
		'Sie haben <mark>{count} nicht in Rechnung gestellte {count, plural, one {Betrag} other {Beträge}}</mark>, die in Rechnung gestellt werden sollen',
	NumberedList: 'Nummerierte Liste',
	Nurse: 'Krankenschwester',
	NurseAnesthetist: 'Krankenschwester Anästhesie',
	NurseAssistant: 'Assistierende Krankenschwester',
	NurseEducator: 'Krankenschwester-Ausbilderin',
	NurseMidwife: 'Krankenschwester Hebamme',
	NursePractitioner: 'Krankenschwester',
	Nurses: 'Krankenschwestern',
	Nursing: 'Pflege',
	Nutritionist: 'Ernährungsberaterin',
	Nutritionists: 'Ernährungswissenschaftler',
	ObstetricianOrGynecologist: 'Frauenarzt/Geburtshelfer',
	Occupation: 'Beruf',
	OccupationalTherapist: 'Ergotherapeut',
	OccupationalTherapists: 'Ergotherapeuten',
	OccupationalTherapy: 'Beschäftigungstherapie',
	Occurrences: 'Vorkommen',
	Of: 'von',
	Ohio: 'Ohio',
	OldPassword: 'Altes Kennwort',
	OlderMessages: '{count} ältere Nachrichten',
	Oldest: 'Älteste',
	OldestUnreplied: 'Älteste unbeantwortete',
	On: 'An',
	OnboardingBusinessAgreement:
		'Im Namen von mir selbst und dem Unternehmen stimme ich dem {businessAssociateAgreement} zu.',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Ergotherapeuten</mark> machen ein Viertel unserer Kunden auf Carepatron aus',
	OnboardingLoadingProfession:
		'Wir haben eine Menge <mark>{profession}</mark>, die Carepatron nutzen und erfolgreich sind.',
	OnboardingLoadingPsychologist: '<mark>Psychologen</mark> machen über die Hälfte unserer Kunden auf Carepatron aus',
	OnboardingLoadingSubtitleFive:
		'Unsere Mission ist es,<mark> zugängliche Software für das Gesundheitswesen</mark> an alle.',
	OnboardingLoadingSubtitleFour:
		'<mark>Vereinfachte Gesundheitssoftware</mark> für mehr als 10.000 Menschen weltweit.',
	OnboardingLoadingSubtitleThree:
		'Speichern<mark> 1 Tag pro Woche</mark> an Verwaltungsaufgaben mit Hilfe von Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Speichern<mark> 2 Stunden</mark> täglich an Verwaltungsaufgaben mit Hilfe von Carepatron.',
	OnboardingReviewLocationOne: 'Holland Park Psychiatrisches Zentrum',
	OnboardingReviewLocationThree: 'Praxisschwester, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Life House Klinik',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'„Carepatron ist super intuitiv zu bedienen. Es hilft uns, unsere Praxis so gut zu führen, dass wir nicht einmal mehr ein Team von Administratoren brauchen.“',
	OnboardingReviewThree:
		'„Es ist die beste Lösung, die ich je verwendet habe, sowohl was die Funktionen als auch die Kosten angeht. Sie bietet alles, was ich für das Wachstum meines Unternehmens brauche.“',
	OnboardingReviewTwo:
		'„Ich liebe auch die Carepatron-App. Sie hilft mir, den Überblick über meine Kunden und meine Arbeit zu behalten, während ich unterwegs bin.“',
	OnboardingTitle: `Kommen wir zu<mark> wissen
 Du solltest besser</mark>`,
	Oncologist: 'Onkologe',
	Online: 'Online',
	OnlineBookingColorTheme: 'Farbthema für Online-Buchungen',
	OnlineBookings: 'Online-Buchungen',
	OnlineBookingsHelper: 'Wählen Sie, wann Online-Buchungen möglich sind und von welcher Art von Kunden',
	OnlinePayment: 'Onlinebezahlung',
	OnlinePaymentSettingCustomInfo:
		'Die Online-Zahlungseinstellungen für diesen Dienst unterscheiden sich von den globalen Buchungseinstellungen.',
	OnlinePaymentSettings: 'Online-Zahlungseinstellungen',
	OnlinePaymentSettingsInfo:
		'Erfassen Sie Zahlungen für Dienstleistungen zum Zeitpunkt der Online-Buchung, um Zahlungen abzusichern und zu rationalisieren',
	OnlinePaymentSettingsPaymentsDisabled:
		'Zahlungen sind deaktiviert, daher können sie während der Online-Buchung nicht eingezogen werden. Bitte überprüfen Sie Ihre Zahlungseinstellungen, um Zahlungen zu aktivieren.',
	OnlinePaymentSettingsStripeNote:
		'{action} um Online-Buchungszahlungen zu erhalten und Ihren Zahlungsprozess zu optimieren',
	OnlinePaymentsNotSupportedForCurrency: 'Online-Zahlungen werden in {currency} nicht unterstützt.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Online-Zahlungen werden in dieser Währung leider nicht unterstützt.',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Leider werden Online-Zahlungen in Ihrem Land noch nicht unterstützt.',
	OnlineScheduling: 'Online-Terminvereinbarung',
	OnlyVisibleToYou: 'Nur für Sie sichtbar',
	OnlyYou: 'Nur du',
	OnsetDate: 'Beginndatum',
	OnsetOfCurrentSymptomsOrIllness: 'Beginn aktueller Symptome oder Krankheit',
	Open: 'Offen',
	OpenFile: 'Datei öffnen',
	OpenSettings: 'Einstellungen öffnen',
	Ophthalmologist: 'Augenarzt',
	OptimiseTelehealthCalls: 'Optimieren Sie Ihre Telemedizin-Anrufe',
	OptimizeServiceTimes: 'Servicezeiten optimieren',
	Options: 'Optionen',
	Optometrist: 'Optiker',
	Or: 'oder',
	OrAttachSingleFile: 'eine Datei anhängen',
	OrDragAndDrop: 'oder ziehen Sie per Drag & Drop',
	OrderBy: 'Sortieren nach',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisation oder Einzelperson',
	OrganizationPlanInclusion1: 'Erweiterte Berechtigungen',
	OrganizationPlanInclusion2: 'Kostenlose Unterstützung beim Importieren von Kundendaten',
	OrganizationPlanInclusion3: 'Engagierter Erfolgsmanager',
	OrganizationPlanInclusionHeader: 'Alles in Professional, plus ...',
	Orthodontist: 'Kieferorthopäde',
	Orthotist: 'Orthopädietechniker',
	Other: 'Andere',
	OtherAdjustments: 'Weitere Anpassungen',
	OtherAdjustmentsTableEmptyState: 'Keine Anpassungen gefunden',
	OtherEvents: 'Andere Ereignisse',
	OtherId: 'Andere ID',
	OtherIdQualifier: 'Anderer ID-Qualifizierer',
	OtherPaymentMethod: 'Andere Bezahlmethode',
	OtherPlanMessage:
		'Behalten Sie die Kontrolle über die Bedürfnisse Ihrer Praxis. Überprüfen Sie Ihren aktuellen Plan, überwachen Sie die Nutzung und erkunden Sie Upgrade-Optionen, um weitere Funktionen freizuschalten, wenn Ihr Team wächst.',
	OtherPolicy: 'Andere Versicherungen',
	OtherProducts: 'Welche anderen Produkte oder Werkzeuge verwenden Sie?',
	OtherServices: 'Sonstige Dienstleistungen',
	OtherTemplates: 'Andere Vorlagen',
	Others: 'Andere',
	OthersPeople: `{n, plural, 		one {1 andere Person}
		other {# andere Personen}
	}`,
	OurResearchTeamReachOut:
		'Kann unser Forschungsteam mehr darüber erfahren, wie Carepatron Ihren Anforderungen besser hätte gerecht werden können?',
	OutOfOffice: 'Ausserhaus',
	OutOfOfficeColor: 'Farbe für Abwesenheitsnotizen',
	OutOfOfficeHelper: 'Einige ausgewählte Teammitglieder sind nicht im Büro',
	OutsideLabCharges: 'Kosten für externe Labore',
	OutsideOfWorkingHours: 'Ausserhalb der Arbeitszeit',
	OutsideWorkingHoursHelper: 'Einige ausgewählte Teammitglieder arbeiten außerhalb der Arbeitszeiten',
	Overallocated: 'Überzugeordnet',
	OverallocatedPaymentDescription: `Diese Zahlung wurde für abrechenbare Artikel überbewertet.
 Fügen Sie unbezahlten Artikeln eine Zuordnung hinzu oder stellen Sie eine Gutschrift bzw. Rückerstattung aus.`,
	OverallocatedPaymentTitle: 'Über zugewiesene Zahlung',
	OverdueTerm: 'Überfälligkeit (Tage)',
	OverinvoicedAmount: 'Zu viel in Rechnung gestellter Betrag',
	Overpaid: 'Überbezahlt',
	OverpaidAmount: 'Zu viel gezahlter Betrag',
	Overtime: 'im Laufe der Zeit',
	Owner: 'Eigentümer',
	POS: 'Kassensystem',
	POSCode: 'Kassencode',
	POSPlaceholder: 'Kassensystem',
	PageBlockerDescription: 'Ungespeicherte Änderungen gehen verloren. Möchten Sie trotzdem verlassen?',
	PageBlockerTitle: 'Änderungen verwerfen?',
	PageFormat: 'Seitenformat',
	PageNotFound: 'Seite nicht gefunden',
	PageNotFoundDescription: 'Sie haben keinen Zugriff mehr auf diese Seite oder sie kann nicht gefunden werden',
	PageUnauthorised: 'Unbefugter Zugriff',
	PageUnauthorisedDescription: 'Sie haben keine Berechtigung, auf diese Seite zuzugreifen',
	Paid: 'Bezahlt',
	PaidAmount: 'Bezahlter Betrag',
	PaidAmountMinimumValueError: 'Der gezahlte Betrag muss größer als 0 sein',
	PaidAmountRequiredError: 'Bezahlter Betrag ist erforderlich',
	PaidItems: 'Bezahlte Artikel',
	PaidMultiple: 'Bezahlt',
	PaidOut: 'Ausbezahlt',
	ParagraphStyles: 'Absatzformate',
	Parent: 'Elternteil',
	Paris: 'Paris',
	PartialRefundAmount: 'Teilweise erstattet ({amount} verbleibend)',
	PartiallyFull: 'Teilweise voll',
	PartiallyPaid: 'Teilweise bezahlt',
	PartiallyRefunded: 'Teilweise erstattet',
	Partner: 'Partner',
	Password: 'Passwort',
	Past: 'Vergangenheit',
	PastDateOverridesEmpty: 'Ihre Terminüberschreibungen werden hier angezeigt, sobald die Veranstaltung vorüber ist',
	Pathologist: 'Pathologe',
	Patient: 'Geduldig',
	Pause: 'Pause',
	Paused: 'Angehalten',
	Pay: 'Zahlen',
	PayMonthly: 'Monatlich zahlen',
	PayNow: 'Zahlen Sie jetzt',
	PayValue: 'Zahle {showPrice, select, true {{price}} other {now}}',
	PayWithOtherCard: 'Mit anderer Karte bezahlen',
	PayYearly: 'Jährlich zahlen',
	PayYearlyPercentOff: 'Zahle jährlich <mark>{percent}% Rabatt</mark>',
	Payer: 'Zahler',
	PayerClaimId: 'Zahlungsempfänger-Anspruch-ID',
	PayerCoverage: 'Abdeckung',
	PayerDetails: 'Angaben zum Zahler',
	PayerDetailsDescription:
		'Zeigen Sie die Zahlerdetails an, die Ihrem Konto hinzugefügt wurden, und verwalten Sie die Registrierung.',
	PayerID: 'Zahler-ID',
	PayerId: 'Zahler-ID',
	PayerName: 'Name des Zahlers',
	PayerPhoneNumber: 'Telefonnummer des Zahlers',
	Payers: 'Zahler',
	Payment: 'Zahlung',
	PaymentAccountUpdated: 'Dein Konto wurde aktualisiert!',
	PaymentAccountUpgraded: 'Ihr Konto wurde aktualisiert!',
	PaymentAmount: 'Zahlungsbetrag',
	PaymentDate: 'Zahlungsdatum',
	PaymentDetails: 'Zahlungsdetails',
	PaymentForUsersPerMonth: 'Zahlung für {billedUsers, plural, one {# Benutzer} other {# Benutzer}} im Monat',
	PaymentInfoFormPrimaryText: 'Zahlungsinformationen',
	PaymentInfoFormSecondaryText: 'Erfassen Sie Zahlungsdetails',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Diese Rechnung wurde bereits bezahlt.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Diese Rechnung wird bereits bearbeitet.',
	PaymentIntentAmountMismatchSnackbar:
		'Der Gesamtbetrag der Rechnung wurde geändert. Bitte überprüfen Sie die Änderungen vor der Zahlung.',
	PaymentIntentSyncTimeoutSnackbar:
		'Ihre Zahlung war erfolgreich, es ist jedoch eine Zeitüberschreitung aufgetreten. Bitte aktualisieren Sie die Seite. Wenn Ihre Zahlung nicht angezeigt wird, wenden Sie sich bitte an den Support.',
	PaymentMethod: 'Bezahlverfahren',
	PaymentMethodDescription:
		'Fügen Sie Ihre Praxiszahlungsmethode hinzu und verwalten Sie sie, um Ihren Abonnementabrechnungsprozess zu optimieren.',
	PaymentMethodLabelBank: 'Bankkonto',
	PaymentMethodLabelCard: 'Karte',
	PaymentMethodLabelFallback: 'Bezahlverfahren',
	PaymentMethodRequired: 'Bitte fügen Sie eine Zahlungsmethode hinzu, bevor Sie Abonnements ändern',
	PaymentMethods: 'Zahlungsarten',
	PaymentProcessing: 'Zahlungsabwicklung!',
	PaymentProcessingFee: 'Die Zahlung beinhaltet eine Bearbeitungsgebühr von {amount}',
	PaymentReports: 'Zahlungsberichte (ERA)',
	PaymentSettings: 'Zahlungseinstellungen',
	PaymentSuccessful: 'Bezahlung erfolgreich!',
	PaymentType: 'Zahlungsart',
	Payments: 'Zahlungen',
	PaymentsAccountDisabledNotificationSubject: `Online-Zahlungen über {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} wurden deaktiviert.
Bitte überprüfe deine Zahlungseinstellungen, um Zahlungen zu aktivieren.`,
	PaymentsEmptyStateDescription: 'Es wurden keine Zahlungen gefunden.',
	PaymentsUnallocated: 'Nicht zugewiesene Zahlungen',
	PayoutDate: 'Auszahlungsdatum',
	PayoutsDisabled: 'Auszahlungen deaktiviert',
	PayoutsEnabled: 'Auszahlungen aktiviert',
	PayoutsStatus: 'Auszahlungsstatus',
	Pediatrician: 'Kinderarzt',
	Pen: 'Stift',
	Pending: 'Ausstehend',
	People: '{rosterSize } Personen',
	PeopleCount: 'Personen ({count})',
	PerMonth: '/ Monat',
	PerUser: 'Pro Benutzer',
	Permission: 'Erlaubnis',
	PermissionRequired: 'Erlaubnis erforderlich',
	Permissions: 'Berechtigungen',
	PermissionsClientAndContactDocumentation: 'Klient ',
	PermissionsClientAndContactProfiles: 'Klient ',
	PermissionsEditAccess: 'Bearbeitungszugriff',
	PermissionsInvoicesAndPayments: 'Rechnungen ',
	PermissionsScheduling: 'Terminplanung',
	PermissionsUnassignClients: 'Aufheben der Clientzuweisung',
	PermissionsUnassignClientsConfirmation: 'Möchten Sie die Zuweisung dieser Clients wirklich aufheben?',
	PermissionsValuesAssigned: 'Nur zugewiesen',
	PermissionsValuesEverything: 'Alles',
	PermissionsValuesNone: 'Keiner',
	PermissionsValuesOwnCalendar: 'Eigener Kalender',
	PermissionsViewAccess: 'Zugriff anzeigen',
	PermissionsWorkspaceSettings: 'Arbeitsbereichseinstellungen',
	Person: '{rosterSize} Person',
	PersonalDetails: 'Persönliche Daten',
	PersonalHealthcareHistoryStoreDescription:
		'Beantworten Sie Ihre persönliche Gesundheitsgeschichte und speichern Sie sie sicher an einem Ort',
	PersonalTrainer: 'Personal Trainer',
	PersonalTraining: 'Personal Training',
	PersonalizeWorkspace: 'Personalisiere deinen Arbeitsbereich',
	PersonalizingYourWorkspace: 'Personalisierung Ihres Arbeitsbereichs',
	Pharmacist: 'Apotheker',
	Pharmacy: 'Apotheke',
	PhoneCall: 'Anruf',
	PhoneNumber: 'Telefonnummer',
	PhoneNumberOptional: 'Telefonnummer (freiwillig)',
	PhotoBy: 'Foto von',
	PhysicalAddress: 'Physikalische Adresse',
	PhysicalTherapist: 'Physiotherapeut',
	PhysicalTherapists: 'Physiotherapeuten',
	PhysicalTherapy: 'Physiotherapie',
	Physician: 'Arzt',
	PhysicianAssistant: 'Arzthelferin',
	Physicians: 'Ärzte',
	Physiotherapist: 'Physiotherapeut',
	PlaceOfService: 'Leistungsort',
	Plan: 'Planen',
	PlanAndReport: 'Plan/Bericht',
	PlanId: 'Plan-ID',
	PlansAndReportsCategoryDescription: 'Für die Behandlungsplanung und Zusammenfassung der Ergebnisse',
	PleaseRefreshThisPageToTryAgain: 'Bitte aktualisieren Sie diese Seite, um es erneut zu versuchen.',
	PleaseWait: 'Bitte warten...',
	PleaseWaitForHostToJoin: 'Warte auf den Beitritt des Hosts …',
	PleaseWaitForHostToStart: 'Bitte warten Sie, bis der Gastgeber dieses Meeting startet.',
	PlusAdd: '+ Hinzufügen',
	PlusOthers: '+{count} andere',
	PlusPlanInclusionFive: 'Gemeinsam genutzte Posteingänge',
	PlusPlanInclusionFour: 'Gruppen-Videoanrufe',
	PlusPlanInclusionHeader: 'Alles in Essential  ',
	PlusPlanInclusionOne: 'Unbegrenzte KI',
	PlusPlanInclusionSix: 'Benutzerdefiniertes Branding',
	PlusPlanInclusionThree: 'Gruppenplanung',
	PlusPlanInclusionTwo: 'Unbegrenzter Speicherplatz ',
	PlusSubscriptionPlanSubtitle: 'Für die Optimierung und das Wachstum Ihrer Praxis',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Polizist',
	PolicyDates: 'Versicherungsdaten',
	PolicyHolder: 'Versicherungsnehmer',
	PolicyHoldersAddress: 'Versicherungsnehmeradresse',
	PolicyMemberId: 'Policenummer',
	PolicyStatus: 'Richtlinienstatus',
	Popular: 'Beliebt',
	PortalAccess: 'Portalzugang',
	PortalNoAppointmentsHeading: 'Behalten Sie alle bevorstehenden und vergangenen Termine im Blick',
	PortalNoDocumentationHeading: 'Erstellen und speichern Sie Ihre Dokumente sicher',
	PortalNoRelationshipsHeading: 'Bringen Sie diejenigen zusammen, die Ihre Reise unterstützen',
	PosCodeErrorMessage: 'POS-Code erforderlich',
	PosoNumber: 'PO/SO-Nummer',
	PossibleClientDuplicate: 'Mögliches Mandanten-Duplikat',
	PotentialClientDuplicateTitle: 'Mögliche doppelte Kundendatensätze',
	PotentialClientDuplicateWarning:
		'Diese Kundeninformationen sind möglicherweise bereits in Ihrer Kundenliste vorhanden. Bitte überprüfen und aktualisieren Sie den vorhandenen Datensatz bei Bedarf oder fahren Sie mit der Erstellung eines neuen Kunden fort.',
	PoweredBy: 'Angetrieben von',
	Practice: 'Üben',
	PracticeDetails: 'Praxisdetails',
	PracticeInfoHeader: 'Geschäftsinformationen',
	PracticeInfoPlaceholder: `Praxisname,
 Nationale Anbieterkennung,
 Mitarbeiter Identifikationsnummer`,
	PracticeLocation: 'Sieht so aus, als ob Ihre Praxis in',
	PracticeSettingsAvailabilityTab: 'Verfügbarkeit',
	PracticeSettingsBillingTab: 'Abrechnungseinstellungen',
	PracticeSettingsClientSettingsTab: 'Clienteinstellungen',
	PracticeSettingsGeneralTab: 'Allgemein',
	PracticeSettingsOnlineBookingTab: 'Online-Buchung',
	PracticeSettingsServicesTab: 'Dienstleistungen',
	PracticeSettingsTaxRatesTab: 'Steuersätze',
	PracticeTemplate: 'Übungsvorlage',
	Practitioner: 'Praktiker',
	PreferredLanguage: 'Bevorzugte Sprache',
	PreferredName: 'Bevorzugter Name',
	Prescription: 'Rezept',
	PreventionSpecialist: 'Präventionsspezialist',
	Preview: 'Vorschau',
	PreviewAndSend: 'Vorschau und Senden',
	PreviewUnavailable: 'Für diesen Dateityp ist keine Vorschau verfügbar',
	PreviousNotes: 'Vorherige Hinweise',
	Price: 'Preis',
	PriceError: 'Der Preis muss größer als 0 sein',
	PricePerClient: 'Preis pro Kunde',
	PricePerUser: 'Pro Benutzer',
	PricePerUserBilledAnnually: 'Pro Benutzer, jährliche Abrechnung',
	PricePerUserPerPeriod: '{price} pro Benutzer / {isMonthly, select, true {Monat} other {Jahr}}',
	PricingGuide: 'Leitfaden zu Preisplänen',
	PricingPlanPerMonth: '/ Monat',
	PricingPlanPerYear: '/ Jahr',
	Primary: 'Primär',
	PrimaryInsurance: 'Erstversicherung',
	PrimaryPolicy: 'Erstversicherung',
	PrimaryTimezone: 'Primäre Zeitzone',
	Print: 'Drucken',
	PrintToCms1500: 'Drucken auf CMS1500',
	PrivatePracticeConsultant: 'Berater in privater Praxis',
	Proceed: 'Weiter',
	ProcessAtTimeOfBookingDesc: 'Kunden müssen den vollen Servicepreis bezahlen, um online zu buchen',
	ProcessAtTimeOfBookingLabel: 'Verarbeiten Sie Zahlungen zum Zeitpunkt der Buchung',
	Processing: 'wird bearbeitet',
	ProcessingFee: 'Bearbeitungsgebühr',
	ProcessingFeeToolTip: `Carepatron ermöglicht es Ihnen, die Bearbeitungsgebühren an Ihre Kunden weiterzubelasten.
 In einigen Rechtsgebieten ist es verboten, Ihren Kunden Bearbeitungsgebühren in Rechnung zu stellen. Es liegt in Ihrer Verantwortung, die geltenden Gesetze einzuhalten.`,
	ProcessingRequest: 'Anfrage wird verarbeitet...',
	Product: 'Produkt',
	Profession: 'Beruf',
	ProfessionExample: 'Therapeut, Ernährungsberater, Zahnarzt',
	ProfessionPlaceholder: 'Geben Sie Ihren Beruf ein oder wählen Sie aus der Liste',
	ProfessionalPlanInclusion1: 'Unbegrenzter Speicherplatz',
	ProfessionalPlanInclusion2: 'Unbegrenzte Aufgaben',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 Kundensupport',
	ProfessionalPlanInclusion5: 'SMS-Erinnerungen',
	ProfessionalPlanInclusionHeader: 'Alles in Starter, plus ...',
	Professions: 'Berufe',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: 'Dateigrößenbeschränkung: 5 MB',
	ProfilePopoverSubTitle: 'Sie sind angemeldet als <strong>{email}</strong>',
	ProfilePopoverTitle: 'Ihre Arbeitsbereiche',
	PromoCode: 'Aktionscode',
	PromotionCodeApplied: '{promo} angewendet',
	ProposeNewDateTime: 'Neues Datum/neue Uhrzeit vorschlagen',
	Prosthetist: 'Orthopädietechniker',
	Provider: 'Anbieter',
	ProviderBillingPlanExpansionManageButton: 'Plan verwalten',
	ProviderCommercialNumber: 'Firmenrufnummer des Anbieters',
	ProviderDetails: 'Anbieterdetails',
	ProviderDetailsAddress: 'Adresse',
	ProviderDetailsName: 'Name',
	ProviderDetailsPhoneNumber: 'Telefonnummer',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Entschuldigung, dieser Anbieter hat bereits ein bestehendes Abrechnungskonto',
	ProviderInfoPlaceholder: `Personal Name,
 E-Mail-Adresse,
 Telefonnummer,
 Nationale Anbieterkennung,
 Amtliches Kennzeichen`,
	ProviderIsChargedProcessingFee: 'Sie bezahlen die Bearbeitungsgebühr',
	ProviderPaymentFormBackButton: 'Zurück',
	ProviderPaymentFormBillingAddressCity: 'Stadt',
	ProviderPaymentFormBillingAddressCountry: 'Land',
	ProviderPaymentFormBillingAddressLine1: 'Linie 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Postleitzahl',
	ProviderPaymentFormBillingEmail: 'Email',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Kreditkartendetails',
	ProviderPaymentFormCardExpiry: 'Ablauf',
	ProviderPaymentFormCardHolderAddressTitle: 'Adresse',
	ProviderPaymentFormCardHolderName: 'Name des Karteninhabers',
	ProviderPaymentFormCardHolderTitle: 'Daten des Karteninhabers',
	ProviderPaymentFormCardNumber: 'Kartennummer',
	ProviderPaymentFormPlanTitle: 'Ausgewählter Plan',
	ProviderPaymentFormPlanTotalTitle: 'Gesamt ({currency}):',
	ProviderPaymentFormSaveButton: 'Abonnement speichern',
	ProviderPaymentFreePlanDescription:
		'Wenn Sie sich für den kostenlosen Plan entscheiden, wird jedem Mitarbeiter der Zugriff auf seine Kunden bei Ihrem Anbieter entzogen. Ihr Zugriff bleibt jedoch bestehen und Sie können die Plattform weiterhin nutzen.',
	ProviderPaymentStepName: 'Rezension ',
	ProviderPaymentSuccessSnackbar: 'Großartig! Ihr neuer Plan wurde erfolgreich gespeichert.',
	ProviderPaymentTitle: 'Rezension ',
	ProviderPlanNetworkIdentificationNumber: 'Netzidentifikationsnummer des Anbieterplans',
	ProviderRemindersSettingsBannerAction: 'Gehe zu Workflow-Management',
	ProviderRemindersSettingsBannerDescription:
		'Finden Sie alle Erinnerungen unter der neuen **Workflow Management**-Registerkarte in **Einstellungen**. Dieses Update bringt leistungsstarke neue Funktionen, verbesserte Vorlagen und intelligentere Automatisierungstools, um Ihre Produktivität zu steigern. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Deine Erinnerungsfunktion wird besser.',
	ProviderTaxonomy: 'Anbietertaxonomie',
	ProviderUPINNumber: 'Anbieter-UPIN-Nummer',
	ProviderUsedStoragePercentage: '{providerName} Speicher ist {usedStoragePercentage}% voll!',
	PsychiatricNursePractitioner: 'Psychiatrische Krankenschwester',
	Psychiatrist: 'Psychiater',
	Psychiatrists: 'Psychiater',
	Psychiatry: 'Psychiatrie',
	Psychoanalyst: 'Psychoanalytiker',
	Psychologist: 'Psychologe',
	Psychologists: 'Psychologen',
	Psychology: 'Psychologie',
	Psychometrician: 'Psychometriker',
	PsychosocialRehabilitationSpecialist: 'Fachkraft für psychosoziale Rehabilitation',
	Psychotheraphy: 'Psychotherapie',
	Psychotherapists: 'Psychotherapeuten',
	Psychotherapy: 'Psychotherapie',
	PublicCallDialogTitle: 'Videoanruf mit ',
	PublicCallDialogTitlePlaceholder: 'Videoanruf unterstützt von Carepatron',
	PublicFormBackToForm: 'Senden Sie eine weitere Antwort',
	PublicFormConfirmSubmissionHeader: 'Übermittlung bestätigen',
	PublicFormNotFoundDescription:
		'Das Formular, das Sie suchen, wurde möglicherweise gelöscht oder der Link ist falsch. Bitte überprüfen Sie die URL und versuchen Sie es erneut.',
	PublicFormNotFoundTitle: 'Formular nicht gefunden',
	PublicFormSubmissionError: 'Eingabe fehlgeschlagen. Bitte versuche es erneut.',
	PublicFormSubmissionSuccess: 'Formular erfolgreich abgeschickt',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} hat {noteTitle} öffentliches Formular eingereicht',
	PublicFormSubmittedSubtitle: 'Ihre Einreichung wurde empfangen.',
	PublicFormSubmittedTitle: 'Danke schön!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Wir haben einen Bestätigungscode an Ihre E-Mail-Adresse gesendet.',
	PublicFormsInvalidConfirmationCode: 'Ungültiger Bestätigungscode',
	PublicHealthInspector: 'Inspektor für öffentliche Gesundheit',
	PublicTemplates: 'Öffentliche Vorlagen',
	Publish: 'Veröffentlichen',
	PublishTemplate: 'Vorlage veröffentlichen',
	PublishTemplateFeatureBannerSubheader: 'Vorlagen zum Nutzen der Community',
	PublishTemplateHeader: 'Veröffentliche {title}',
	PublishTemplateToCommunity: 'Template für die Community veröffentlichen',
	PublishToCommunity: 'In der Community veröffentlichen',
	PublishToCommunitySuccessMessage: 'Erfolgreich in der Community veröffentlicht',
	Published: 'Veröffentlicht',
	PublishedBy: 'Veröffentlicht von {name}',
	PublishedNotesAreNotAutosaved: 'Veröffentlichte Notizen werden nicht automatisch gespeichert',
	PublishedOnCarepatronCommunity: 'Veröffentlicht in der Carepatron-Community',
	Purchase: 'Kaufen',
	PushToCalendar: 'In Kalender übertragen',
	Question: 'Frage',
	QuestionOrTitle: 'Frage oder Titel',
	QuickActions: 'Schnelle Aktionen',
	QuickThemeSwitcherColorBasil: 'Basilikum',
	QuickThemeSwitcherColorBlueberry: 'Blaubeere',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Moss',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Strahlentherapeut',
	Radiologist: 'Radiologe',
	Read: 'Lesen',
	ReadOnly: 'Nur lesbar',
	ReadOnlyAppointment: 'Nur-Lese-Termin',
	ReadOnlyEventBanner:
		'Dieser Termin ist mit einem schreibgeschützten Kalender synchronisiert und kann nicht bearbeitet werden.',
	ReaderMaxDepthHasBeenExceededCode:
		'Die Notiz ist zu verschachtelt. Versuchen Sie, die Einrückung einiger Elemente aufzuheben.',
	ReadyForMapping: 'Bereit zum Mapping',
	RealEstateAgent: 'Immobilienmakler',
	RearrangeClientFields: 'Neuanordnen von Clientfeldern in den Clienteinstellungen',
	Reason: 'Grund',
	ReasonForChange: 'Änderungsgrund',
	RecentAppointments: 'Aktuelle Ernennungen',
	RecentServices: 'Aktuelle Dienste',
	RecentTemplates: 'Aktuelle Vorlagen',
	RecentlyUsed: 'Kürzlich benutzt',
	Recommended: 'Empfohlen',
	RecommendedTemplates: 'Empfohlene Vorlagen',
	Recording: 'Aufnahme',
	RecordingEnded: 'Aufzeichnung beendet',
	RecordingInProgress: 'Aufnahme läuft',
	RecordingMicrophoneAccessErrorMessage:
		'Bitte erlauben Sie den Mikrofonzugriff in Ihrem Browser und aktualisieren Sie, um die Aufnahme zu starten.',
	RecurrenceCount: ', {count, plural, one {einmal} other {# Mal}}',
	RecurrenceDaily: '{count, plural, one {Täglich} other {Tage}}',
	RecurrenceEndAfter: 'Nachdem',
	RecurrenceEndNever: 'Nie',
	RecurrenceEndOn: 'Auf',
	RecurrenceEvery: 'Jeder {description}',
	RecurrenceMonthly: '{count, plural, one {Monatlich} other {Monate}}',
	RecurrenceOn: 'auf {description}',
	RecurrenceOnAllDays: 'an allen Tagen',
	RecurrenceUntil: 'bis {description}',
	RecurrenceWeekly: '{count, plural, one {Wöchentlich} other {Wochen}}',
	RecurrenceYearly: '{count, plural, one {Jährlich} other {Jahre}}',
	Recurring: 'Wiederkehrend',
	RecurringAppointment: 'Wiederkehrender Termin',
	RecurringAppointmentsLimitedBannerText:
		'Es werden nicht alle wiederkehrenden Termine angezeigt. Reduzieren Sie den Datumsbereich, um alle wiederkehrenden Termine für den Zeitraum anzuzeigen.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# Ereignis} other {# Veranstaltungen}}</b> werden zu folgenden Terminen erstellt',
	Redo: 'Wiederholen',
	ReferFriends: 'Freunde werben',
	Reference: 'Referenz',
	ReferralCreditedNotificationSubject: 'Ihr Empfehlungsguthaben von {currency} {amount} wurde angewendet',
	ReferralEmailDefaultBody: `Danke an {name}, du hast ein KOSTENLOSES 3-Monats-Upgrade für Carepatron erhalten. Werde Teil unserer Community von über 3 Millionen medizinischen Fachkräften, die für eine neue Arbeitsweise geschaffen wurde!
Mit freundlichen Grüßen,
Das Carepatron-Team`,
	ReferralEmailDefaultSubject: 'Sie wurden eingeladen, Carepatron beizutreten',
	ReferralHasNotSignedUpDescription: 'Dein Freund hat sich noch nicht angemeldet',
	ReferralHasSignedUpDescription: 'Dein Freund hat sich angemeldet.',
	ReferralInformation: 'Empfehlungsinformationen',
	ReferralJoinedNotificationSubject: '{actorProfileName} ist Carepatron beigetreten',
	ReferralListErrorDescription: 'Die Empfehlungsliste konnte nicht geladen werden.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {Monat} other {Monate}}</b> aktiv',
	ReferralRewardBanner: 'Melden Sie sich an und fordern Sie Ihre Empfehlungsprämie an!',
	Referrals: 'Empfehlungen',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} Monat {percentOff, select, 100 {kostenlos bezahlt} other {{percentOff}% Rabatt}} {type, select, SubscriptionUpgrade {Upgrade} other {}}',
	ReferredUserBenefitTitle: 'Sie bekommen!',
	Referrer: 'Referrer',
	ReferringProvider: 'Verweisender Anbieter',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} Guthaben, wenn <mark>3 Freunde</mark> aktiviert werden.',
	ReferringUserBenefitTitle: 'Du erhältst!',
	RefreshPage: 'Seite neu laden',
	Refund: 'Rückerstattung',
	RefundAcknowledgement: 'Ich habe {clientName} außerhalb von Carepatron erstattet.',
	RefundAcknowledgementValidationMessage: 'Bitte bestätigen Sie, dass Sie diesen Betrag zurückerstattet haben',
	RefundAmount: 'Rückerstattungsbetrag',
	RefundContent:
		'Rückerstattungen erscheinen erst nach 7-10 Tagen auf dem Konto Ihres Kunden. Zahlungsgebühren werden nicht erstattet, es fallen jedoch keine zusätzlichen Gebühren für Rückerstattungen an. Rückerstattungen können nicht storniert werden und einige müssen vor der Bearbeitung möglicherweise überprüft werden.',
	RefundCouldNotBeProcessed: 'Rückerstattung konnte nicht bearbeitet werden',
	RefundError:
		'Diese Rückerstattung kann derzeit nicht automatisch bearbeitet werden. Bitte wenden Sie sich an den Carepatron-Support, um die Rückerstattung dieser Zahlung anzufordern.',
	RefundExceedTotalValidationError: 'Der Betrag darf den Gesamtbetrag nicht überschreiten.',
	RefundFailed: 'Rückerstattung fehlgeschlagen',
	RefundFailedTooltip:
		'Die Rückerstattung dieser Zahlung ist zuvor fehlgeschlagen und kann nicht erneut versucht werden. Bitte wenden Sie sich an den Support.',
	RefundNonStripePaymentContent:
		'Diese Zahlung wurde mit einer Methode außerhalb von Carepatron getätigt (z. B. Bargeld, Online-Banking). Bei einer Rückerstattung innerhalb von Carepatron wird dem Kunden kein Geld zurückerstattet.',
	RefundReasonDescription:
		'Das Hinzufügen eines Rückerstattungsgrundes kann bei der Überprüfung der Transaktionen Ihrer Kunden hilfreich sein',
	Refunded: 'Rückerstattung',
	Refunds: 'Rückerstattungen',
	RefundsTableEmptyState: 'Keine Rückerstattungen gefunden',
	Regenerate: 'Neu generieren',
	RegisterButton: 'Registrieren',
	RegisterEmail: 'Email',
	RegisterFirstName: 'Vorname',
	RegisterLastName: 'Familienname, Nachname',
	RegisterPassword: 'Passwort',
	RegisteredNurse: 'Staatlich geprüfte Krankenschwester',
	RehabilitationCounselor: 'Rehabilitationsberater',
	RejectAppointmentFormTitle:
		'Sie können nicht kommen? Dann teilen Sie uns bitte den Grund mit und schlagen Sie einen neuen Termin vor.',
	Rejected: 'Abgelehnt',
	Relationship: 'Beziehung',
	RelationshipDetails: 'Beziehungsdetails',
	RelationshipEmptyStateTitle: 'Bleiben Sie mit den Menschen in Kontakt, die Ihren Kunden unterstützen',
	RelationshipPageAccessTypeColumnName: 'Profilzugriff',
	RelationshipSavedSuccessSnackbar: 'Beziehung erfolgreich gespeichert!',
	RelationshipSelectorFamilyAdmin: 'Familie',
	RelationshipSelectorFamilyMember: 'Familienmitglied',
	RelationshipSelectorProviderAdmin: 'Anbieteradministrator',
	RelationshipSelectorProviderStaff: 'Mitarbeiter des Anbieters',
	RelationshipSelectorSupportNetworkPrimary: 'Freund',
	RelationshipSelectorSupportNetworkSecondary: 'Unterstützungsnetzwerk',
	RelationshipStatus: 'Beziehungsstatus',
	RelationshipType: 'Beziehungstyp',
	RelationshipTypeClientOwner: 'Klient',
	RelationshipTypeFamilyAdmin: 'Beziehungen',
	RelationshipTypeFamilyMember: 'Familie',
	RelationshipTypeFriendOrSupport: 'Freunde oder Unterstützungsnetzwerk',
	RelationshipTypeProviderAdmin: 'Anbieteradministrator',
	RelationshipTypeProviderStaff: 'Personal',
	RelationshipTypeSelectorPlaceholder: 'Beziehungsarten suchen',
	Relationships: 'Beziehungen',
	Remaining: 'übrig',
	RemainingTime: '{time} verbleibend',
	Reminder: 'Erinnerung',
	ReminderColor: 'Erinnerungsfarbe',
	ReminderDetails: 'Erinnerungsdetails',
	ReminderEditDisclaimer: 'Änderungen werden erst bei Neubesetzungen berücksichtigt',
	ReminderSettings: 'Einstellungen für Terminerinnerungen',
	Reminders: 'Erinnerungen',
	Remove: 'Entfernen',
	RemoveAccess: 'Zugriff entfernen',
	RemoveAllGuidesBtn: 'Alle Hilfslinien entfernen',
	RemoveAllGuidesPopoverBody:
		'Wenn Sie mit den Onboarding-Anleitungen fertig sind, verwenden Sie einfach die Schaltfläche „Anleitungen entfernen“ in jedem Bereich.',
	RemoveAllGuidesPopoverTitle: 'Benötigen Sie Ihre Onboarding-Leitfäden nicht mehr?',
	RemoveAsDefault: 'Als Standard entfernen',
	RemoveAsIntake: 'Als Aufnahme entfernen',
	RemoveCol: 'Spalte entfernen',
	RemoveColor: 'Farbe entfernen',
	RemoveField: 'Feld entfernen',
	RemoveFromCall: 'Aus Anruf entfernen',
	RemoveFromCallDescription:
		'Sind Sie sicher, dass Sie <mark>{attendeeName}</mark> aus diesem Videoanruf entfernen möchten?',
	RemoveFromCollection: 'Aus der Sammlung entfernen',
	RemoveFromCommunity: 'Aus der Community entfernen',
	RemoveFromFolder: 'Aus Ordner entfernen',
	RemoveFromFolderConfirmationDescription:
		'Sind Sie sicher, dass Sie diese Vorlage aus diesem Ordner entfernen möchten? Diese Aktion kann nicht rückgängig gemacht werden, Sie können sie aber später wieder verschieben.',
	RemoveFromIntakeDefault: 'Aus Aufnahmestandard entfernen',
	RemoveGuides: 'Hilfslinien entfernen',
	RemoveMfaConfirmationDescription:
		'Durch das Entfernen der Multi-Faktor-Authentifizierung (MFA) wird die Sicherheit Ihres Kontos verringert. Möchten Sie fortfahren?',
	RemoveMfaConfirmationTitle: 'MFA entfernen?',
	RemovePaymentMethodDescription: `Dadurch wird der Zugriff auf diese Zahlungsmethode und ihre zukünftige Verwendung aufgehoben.
 Diese Aktion kann nicht rückgängig gemacht werden.`,
	RemoveRow: 'Zeile entfernen',
	RemoveTable: 'Tabelle entfernen',
	RemoveTemplateAsDefaultIntakeSuccess: 'Erfolgreich entfernt {templateTitle} als Standard-Intake-Vorlage',
	RemoveTemplateFromCommunity: 'Vorlage aus Community entfernen',
	RemoveTemplateFromFolder: '{templateTitle} erfolgreich aus {folderTitle} entfernt',
	Rename: 'Umbenennen',
	RenderingProvider: 'Rendering-Anbieter',
	Reopen: 'Wieder öffnen',
	ReorderServiceGroupFailure: 'Neubestellung der Sammlung fehlgeschlagen',
	ReorderServiceGroupSuccess: 'Sammlung erfolgreich nachbestellt',
	ReorderServicesFailure: 'Dienste konnten nicht nachbestellt werden',
	ReorderServicesSuccess: 'Dienste erfolgreich nachbestellt',
	ReorderYourServiceList: 'Ordnen Sie Ihre Serviceliste neu an',
	ReorderYourServiceListDescription:
		'Die Art und Weise, wie Sie Ihre Dienste und Sammlungen organisieren, wird auf Ihrer Online-Buchungsseite für alle Ihre Kunden sichtbar angezeigt!',
	RepeatEvery: 'Wiederholen Sie jeden',
	RepeatOn: 'Wiederholen auf',
	Repeating: 'Wiederholen',
	Repeats: 'Wiederholungen',
	RepeatsEvery: 'Wiederholt sich alle',
	Rephrase: 'Umformulieren',
	Replace: 'Ersetzen',
	ReplaceBackground: 'Hintergrund ersetzen',
	ReplacementOfPriorClaim: 'Ersatz vorheriger Forderung',
	Report: 'Bericht',
	Reprocess: 'Erneut verarbeiten',
	RepublishTemplateToCommunity: 'Republikationsvorlage für die Community',
	RequestANewVerificationLink: 'Neuen Bestätigungslink anfordern',
	RequestCoverageReport: 'Abdeckungsbericht anfordern',
	RequestingDevicePermissions: 'Geräteberechtigungen anfordern ...',
	RequirePaymentMethodDesc: 'Kunden müssen ihre Kreditkartendaten eingeben, um online zu buchen',
	RequirePaymentMethodLabel: 'Kreditkartendaten anfordern',
	Required: 'erforderlich',
	RequiredField: 'Erforderlich',
	RequiredUrl: 'URL ist erforderlich.',
	Reschedule: 'Neu planen',
	RescheduleBookingLinkModalDescription: 'Über diesen Link kann Ihr Kunde Datum und Uhrzeit seines Termins ändern.',
	RescheduleBookingLinkModalTitle: 'Link zur Umbuchung',
	RescheduleLink: 'Link zum Neuterminieren',
	Resend: 'Erneut senden',
	ResendConfirmationCode: 'Bestätigungscode erneut senden',
	ResendConfirmationCodeDescription:
		'Bitte geben Sie Ihre E-Mail-Adresse ein und wir senden Ihnen einen weiteren Bestätigungscode per E-Mail',
	ResendConfirmationCodeSuccess:
		'Der Bestätigungscode wurde erneut gesendet. Bitte überprüfen Sie Ihren Posteingang.',
	ResendNewEmailVerificationSuccess: 'Ein neuer Verifizierungslink wurde an {email} gesendet.',
	ResendVerificationEmail: 'Bestätigungsmail erneut senden',
	Reset: 'Zurücksetzen',
	Resources: 'Ressourcen',
	RespiratoryTherapist: 'Atemtherapeuten',
	RespondToHistoricAppointmentError:
		'Dies ist ein historischer Termin. Bitte wenden Sie sich bei Fragen an Ihren Arzt.',
	Responder: 'Antwortender',
	RestorableItemModalDescription:
		'Sind Sie sicher, dass Sie {context} löschen möchten?{canRestore, select, true { Sie können es später wiederherstellen.} other {}}',
	RestorableItemModalTitle: 'Löschen {type}',
	Restore: 'Wiederherstellen',
	RestoreAll: 'Alles wiederherstellen',
	Restricted: 'Eingeschränkt',
	ResubmissionCodeReferenceNumber: 'Erneuter Einreichungscode und Referenznummer',
	Resubmit: 'Erneut einreichen',
	Resume: 'Wieder aufnehmen',
	Retry: 'Erneut versuchen',
	RetryingConnectionAttempt: 'Wiederverbindungsversuch... (Versuch {retryCount} von {maxRetries})',
	ReturnToForm: 'Zurück zur Form',
	RevertClaimStatus: 'Status der Reklamation zurücksetzen',
	RevertClaimStatusDescriptionBody:
		'Dieser Anspruch hat verknüpfte Zahlungen, und das Ändern des Status kann die Verfolgung oder Verarbeitung von Zahlungen beeinträchtigen, was eine manuelle Abstimmung erfordern könnte.',
	RevertClaimStatusDescriptionTitle: 'Sind Sie sicher, dass Sie zu {status} zurückkehren möchten?',
	RevertClaimStatusError: 'Status der Forderung konnte nicht zurückgesetzt werden',
	RevertToDraft: 'Zum Entwurf zurückkehren',
	Review: 'Rezension',
	ReviewsFirstQuote: 'Termine überall und jederzeit',
	ReviewsSecondJobTitle: 'Lifehouse-Klinik',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Ich liebe auch die Carepatron-App. Sie hilft mir, den Überblick über meine Kunden und meine Arbeit zu behalten, auch wenn ich unterwegs bin.',
	ReviewsThirdJobTitle: 'Manila Bay Klinik',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote:
		'Die einfache Navigation und die schöne Benutzeroberfläche zaubern mir jeden Tag ein Lächeln ins Gesicht.',
	RightAlign: 'Rechtsbündig',
	Role: 'Rolle',
	Roster: 'Teilnehmer',
	RunInBackground: 'Im Hintergrund ausführen',
	SMS: 'Direct Mail',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Sie verlassen Carepatron',
	SafetyRedirectSubtext: 'Wenn Sie diesem Link vertrauen, wählen Sie ihn aus, um fortzufahren',
	SalesRepresentative: 'Außendienstmitarbeiter',
	SalesTax: 'Mehrwertsteuer',
	SalesTaxHelp: 'Enthält die Umsatzsteuer auf den erstellten Rechnungen',
	SalesTaxIncluded: 'Ja',
	SalesTaxNotIncluded: 'NEIN',
	SaoPaulo: 'São Paulo',
	Saturday: 'Samstag',
	Save: 'Speichern',
	SaveAndClose: 'Speichern ',
	SaveAndExit: 'Speichern ',
	SaveAndLock: 'Speichern und sperren',
	SaveAsDraft: 'Als Entwurf speichern',
	SaveCardForFuturePayments: 'Karte für zukünftige Zahlungen speichern',
	SaveChanges: 'Änderungen speichern',
	SaveCollection: 'Sammlung speichern',
	SaveField: 'Feld speichern',
	SavePaymentMethod: 'Zahlungsart speichern',
	SavePaymentMethodDescription: 'Erst bei Ihrem ersten Termin entstehen Ihnen Kosten.',
	SavePaymentMethodSetupError:
		'Ein unerwarteter Fehler ist aufgetreten und wir konnten derzeit keine Zahlungen konfigurieren.',
	SavePaymentMethodSetupInvoiceLater:
		'Zahlungen können beim Bezahlen Ihrer ersten Rechnung eingerichtet und gespeichert werden.',
	SaveSection: 'Abschnitt „Speichern“',
	SaveService: 'Neuen Dienst anlegen',
	SaveTemplate: 'Vorlage speichern',
	Saved: 'Gerettet',
	SavedCards: 'Gespeicherte Karten',
	SavedPaymentMethods: 'Gerettet',
	Saving: 'Speichern...',
	ScheduleAppointmentsAndOnlineServices: 'Termine und Online-Dienste planen',
	ScheduleName: 'Zeitplanname',
	ScheduleNew: 'Termin neu planen',
	ScheduleSend: 'Termin senden',
	ScheduleSendAlertInfo: 'Nachrichten in geplant werden, um ihre geplante Zeit zu senden.',
	ScheduleSendByName: '**Sendetermin** • {time} von {displayName}',
	ScheduleSetupCall: 'Planen Sie einen Einrichtungsanruf',
	Scheduled: 'Geplant',
	SchedulingSend: 'Senden planen',
	School: 'Schule',
	ScrollToTop: 'Nach oben scrollen',
	Search: 'Suchen',
	SearchAndConvertToLanguage: 'Suche und Konvertiere in Sprache',
	SearchBasicBlocks: 'Suche nach Basisblöcken',
	SearchByName: 'Suche mit Name',
	SearchClaims: 'Suchvorgänge',
	SearchClientFields: 'Kundenfelder durchsuchen',
	SearchClients: 'Suche nach Kundenname, Kunden-ID oder Telefonnummer',
	SearchCommandNotFound: 'Keine Ergebnisse für "{searchTerm}" gefunden.',
	SearchContacts: 'Kunde oder Kontakt',
	SearchContactsPlaceholder: 'Kontakte durchsuchen',
	SearchConversations: 'Konversationen durchsuchen',
	SearchInputPlaceholder: 'Alle Ressourcen durchsuchen',
	SearchInvoiceNumber: 'Rechnungsnummer suchen',
	SearchInvoices: 'Rechnungen suchen',
	SearchMultipleContacts: 'Kunden oder Kontakte',
	SearchMultipleContactsOptional: 'Kunden oder Kontakte (optional)',
	SearchOrCreateATag: 'Suchen oder erstellen Sie ein Tag',
	SearchPayments: 'Suche nach Zahlungen',
	SearchPrepopulatedData: 'Suche in vorab ausgefüllten Datenfeldern',
	SearchRelationships: 'Suchbeziehungen',
	SearchRemindersAndWorkflows: 'Sucherinnerungen und Workflows',
	SearchServices: 'Suchdienste',
	SearchTags: 'Such-Tags',
	SearchTeamMembers: 'Teammitglieder suchen',
	SearchTemplatePlaceholder: 'Suche {templateCount}+ Ressourcen',
	SearchTimezone: 'Zeitzone suchen...',
	SearchTrashItems: 'Suche nach elementen',
	SearchUnsplashPlaceholder: 'Suchen Sie nach kostenlosen hochauflösenden Fotos von Unsplash',
	Secondary: 'Sekundär',
	SecondaryInsurance: 'Zweitversicherung',
	SecondaryPolicy: 'Zweitversicherung',
	SecondaryTimezone: 'Sekundäre Zeitzone',
	Secondly: 'Zweitens',
	Section: 'Abschnitt',
	SectionCannotBeEmpty: 'Ein Abschnitt muss mindestens eine Zeile haben',
	SectionFormSecondaryText: 'Abschnittstitel und -beschreibung',
	SectionName: 'Abteilungsname',
	Sections: 'Abschnitte',
	SeeLess: 'Weniger anzeigen',
	SeeLessUpcomingAppointments: 'Weniger bevorstehende Termine anzeigen',
	SeeMore: 'Mehr sehen',
	SeeMoreUpcomingAppointments: 'Weitere bevorstehende Termine anzeigen',
	SeeTemplateLibrary: 'Siehe Vorlagenbibliothek',
	Seen: 'Gesehen',
	SeenByName: '<strong>Gesehen</strong> • {time} von {displayName}',
	SelectAll: 'Wählen Sie Alle',
	SelectAssignees: 'Auswählen der Beauftragten',
	SelectAttendees: 'Teilnehmer auswählen',
	SelectCollection: 'Kollektion auswählen',
	SelectCorrespondingAttributes: 'Entsprechende Attribute auswählen',
	SelectPayers: 'Zahler auswählen',
	SelectProfile: 'Wähle Profil',
	SelectServices: 'Dienste auswählen',
	SelectTags: 'Tags auswählen',
	SelectTeamOrCommunity: 'Team oder Community auswählen',
	SelectTemplate: 'Vorlage auswählen',
	SelectType: 'Art auswählen',
	Selected: 'Ausgewählt',
	SelfPay: 'Selbstzahler',
	Send: 'Schicken',
	SendAndClose: 'Schicken ',
	SendAndStopIgnore: 'Senden und Ignorieren beenden',
	SendEmail: 'E-Mail senden',
	SendIntake: 'Aufnahme senden',
	SendIntakeAndForms: 'Aufnahme senden ',
	SendMeACopy: 'Sende mir eine Kopie',
	SendNotificationEmailWarning:
		'Einige Teilnehmer haben keine E-Mail-Adresse und erhalten keine automatischen Benachrichtigungen und Erinnerungen.',
	SendNotificationLabel: 'Wählen Sie die Teilnehmer aus, die per E-Mail benachrichtigt werden sollen.',
	SendOnlinePayment: 'Online-Zahlung senden',
	SendOnlinePaymentTooltipTitleAdmin: 'Bitte fügen Sie Ihre bevorzugten Auszahlungseinstellungen hinzu',
	SendOnlinePaymentTooltipTitleStaff:
		'Um die Online-Zahlung einzurichten, wenden Sie sich bitte an den Inhaber des Anbieters.',
	SendPaymentLink: 'Zahlungslink senden',
	SendReaction: 'Senden Sie eine Reaktion',
	SendScheduledForDate: 'Senden geplant für {date}',
	SendVerificationEmail: 'Senden Sie E-Mail-Verifizierung',
	SendingFailed: 'Senden fehlgeschlagen',
	Sent: 'Gesendet',
	SentByName: '**Gesendet** • {time} von {displayName}',
	Seoul: 'Seoul',
	SeparateDuplicateClientsDescription:
		'Die ausgewählten Kundendatensätze bleiben vom Rest getrennt, sofern Sie sich nicht für die Zusammenführung entscheiden.',
	Service: 'Service',
	'Service/s': 'Dienstleistungen',
	ServiceAdjustment: 'Serviceanpassung',
	ServiceAllowNewClientsIndicator: 'Neue Clients zulassen',
	ServiceAlreadyExistsInCollection: 'Der Dienst ist bereits in der Sammlung vorhanden.',
	ServiceBookableOnlineIndicator: 'Online buchbar',
	ServiceCode: 'Code',
	ServiceCodeErrorMessage: 'Ein Servicecode ist erforderlich',
	ServiceCodeSelectorPlaceholder: 'Einen Servicecode hinzufügen',
	ServiceColour: 'Servicefarbe',
	ServiceCoverageDescription:
		'Wählen Sie die erstattungsfähigen Leistungen und den Selbstbehalt für diese Versicherungspolice aus.',
	ServiceCoverageGoToServices: 'Zu den Leistungen',
	ServiceCoverageNoServicesDescription:
		'Passen Sie die Selbstbeteiligungsbeträge für Dienstleistungen an, um die standardmäßige Selbstbeteiligung der Police zu überschreiben. Deaktivieren Sie die Deckung, um zu verhindern, dass Dienstleistungen über die Police in Anspruch genommen werden.',
	ServiceCoverageNoServicesLabel: 'Es wurden keine Dienste gefunden.',
	ServiceCoverageTitle: 'Leistungsumfang',
	ServiceDate: 'Datum der Zustellung',
	ServiceDetails: 'Leistungsdetails',
	ServiceDuration: 'Dauer',
	ServiceEmptyState: 'Es sind noch keine Dienste vorhanden',
	ServiceErrorMessage: 'Service ist erforderlich',
	ServiceFacility: 'Serviceeinrichtung',
	ServiceName: 'Dienstname',
	ServiceRate: 'Rate',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} für {serviceReceiptNumber, select, undefined {user} other {{clientName}}} benötigt zusätzliche Informationen',
	ServiceSalesTax: 'Mehrwertsteuer',
	ServiceType: 'Service',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Klicken Sie auf „Neu laden“, um zu aktualisieren und die neuesten Carepatron-Updates zu erhalten.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Neu laden',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Sie verwenden eine ältere Version',
	ServiceWorkerForceUIUpdateDialogTitle: 'Willkommen zurück!',
	Services: 'Dienstleistungen',
	ServicesAndAvailability: 'Dienstleistungen ',
	ServicesAndDiagnosisCodesHeader: 'Dienste und Diagnosecodes hinzufügen',
	ServicesCount: '{count,plural,=0{Dienstleistungen}one{Dienstleistung}other{Dienstleistungen}}',
	ServicesPlaceholder: 'Dienstleistungen',
	ServicesProvidedBy: 'Leistung/en erbracht durch',
	SetAPhysicalAddress: 'Eine physische Adresse festlegen',
	SetAVirtualLocation: 'Einen virtuellen Standort festlegen',
	SetAsDefault: 'Als Standard einstellen',
	SetAsIntake: 'Als Aufnahme festlegen',
	SetAsIntakeDefault: 'Als Aufnahmestandard festlegen',
	SetAvailability: 'Verfügbarkeit festlegen',
	SetTemplateAsDefaultIntakeSuccess: 'Erfolgreich {templateTitle} als Standard-Aufnahmevorlage festgelegt',
	SetUpMfaButton: 'Einrichten von MFA',
	SetYourLocation: 'Legen Sie Ihren <mark>Standort</mark> fest',
	SetYourLocationDescription: 'Ich habe keine Geschäftsadresse <span>(nur Online- und Mobil-Dienste)</span>',
	SettingUpPayers: 'Zahler einrichten',
	Settings: 'Einstellungen',
	SettingsNewUserPasswordDescription:
		'Sobald Sie sich angemeldet haben, senden wir Ihnen einen Bestätigungscode, mit dem Sie Ihr Konto bestätigen können',
	SettingsNewUserPasswordTitle: 'Melden Sie sich bei Carepatron an',
	SettingsTabAutomation: 'Automatisierung',
	SettingsTabBillingDetails: 'Rechnungsdetails',
	SettingsTabConnectedApps: 'Verbundene Apps',
	SettingsTabCustomFields: 'Benutzerdefinierte Felder',
	SettingsTabDetails: 'Einzelheiten',
	SettingsTabInvoices: 'Rechnungen',
	SettingsTabLocations: 'Standorte',
	SettingsTabNotifications: 'Benachrichtigungen',
	SettingsTabOnlineBooking: 'Online-Buchung',
	SettingsTabPayers: 'Zahler',
	SettingsTabReminders: 'Erinnerung',
	SettingsTabServices: 'Dienstleistungen',
	SettingsTabServicesAndAvailability: 'Dienste und Verfügbarkeit',
	SettingsTabSubscriptions: 'Abonnements',
	SettingsTabWorkflowAutomations: 'Automatisierungen',
	SettingsTabWorkflowReminders: 'Grundlegende Erinnerungen',
	SettingsTabWorkflowTemplates: 'Vorlagen',
	Setup: 'Einrichten',
	SetupGuide: 'Einrichtungsanleitung',
	SetupGuideAddServicesActionLabel: 'Start',
	SetupGuideAddServicesSubtitle: '4 Schritte • 2 Min',
	SetupGuideAddServicesTitle: 'Fügen Sie Ihre Dienste hinzu',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Start',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 Schritte • 3 Minuten',
	SetupGuideEnableOnlinePaymentsTitle: 'Online-Zahlungen aktivieren',
	SetupGuideImportClientsActionLabel: 'Start',
	SetupGuideImportClientsSubtitle: '4 Schritte • 3 Min',
	SetupGuideImportClientsTitle: 'Importiere deine Kunden',
	SetupGuideImportTemplatesActionLabel: 'Start',
	SetupGuideImportTemplatesSubtitle: '2 Schritte • 1 Minute',
	SetupGuideImportTemplatesTitle: 'Importiere deine Vorlagen',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Start',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 Schritte • 2 Min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personalisiere deinen Arbeitsbereich',
	SetupGuideSetLocationActionLabel: 'Start',
	SetupGuideSetLocationSubtitle: '4 Schritte • 1 Min',
	SetupGuideSetLocationTitle: 'Setzen Sie Ihren Standort',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Team einladen',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'Laden Sie Ihr Team ein, mühelos zu kommunizieren und Aufgaben zu verwalten.',
	SetupGuideSuggestedAddTeamMembersTag: 'Einrichtung',
	SetupGuideSuggestedAddTeamMembersTitle: 'Teammitglieder hinzufügen',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Anpassen',
	SetupGuideSuggestedCustomizeBrandSubtitle:
		'Fühlen Sie sich professionell mit Ihrem einzigartigen Logo und Ihren Markenfarben.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Marke anpassen',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Herunterladen',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Greifen Sie jederzeit und von jedem Gerät aus auf Ihren Arbeitsbereich zu.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Einrichtung',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Laden Sie die App herunter',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Verfügbarkeit festlegen',
	SetupGuideSuggestedEditAvailabilitySubtitle:
		'Verhindern Sie Doppelbuchungen, indem Sie Ihre Verfügbarkeit festlegen.',
	SetupGuideSuggestedEditAvailabilityTag: '<h1>Terminplanung</h1>',
	SetupGuideSuggestedEditAvailabilityTitle: 'Verfügbarkeit bearbeiten',
	SetupGuideSuggestedImportClientsActionLabel: 'Import',
	SetupGuideSuggestedImportClientsSubtitle: 'Laden Sie Ihre bestehenden Kundendaten mit nur einem Klick sofort hoch.',
	SetupGuideSuggestedImportClientsTag: 'Einrichtung',
	SetupGuideSuggestedImportClientsTitle: 'Kunden importieren',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Erinnerungen bearbeiten',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Reduzieren Sie Terminausfälle mit automatischen Terminerinnerungen.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Individuelle Erinnerungen',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Anruf starten',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Führen Sie einen Anruf durch und verbinden Sie sich mit Kunden mithilfe unserer KI-gestützten Video-Tools.',
	SetupGuideSuggestedStartVideoCallTag: 'Telemedizin',
	SetupGuideSuggestedStartVideoCallTitle: 'Videoanruf starten',
	SetupGuideSuggestedTryActionsTitle: 'Dinge zum Ausprobieren 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Versuchen Sie AI-Assistent',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Erhalten Sie sofortige Antworten auf all Ihre Arbeitsfragen.',
	SetupGuideSuggestedUseAIAssistantTag: 'Neu',
	SetupGuideSuggestedUseAIAssistantTitle: 'KI-Assistent verwenden',
	SetupGuideSyncCalendarActionLabel: 'Start',
	SetupGuideSyncCalendarSubtitle: '1 Schritt • weniger als 1 Min',
	SetupGuideSyncCalendarTitle: 'Synchronisieren Sie Ihren Kalender',
	SetupGuideVerifyEmailLabel: 'Überprüfen',
	SetupGuideVerifyEmailSubtitle: '2 Schritte • 2 Min',
	SetupOnlineStripePayments: 'Verwenden Sie Stripe für Online-Zahlungen',
	SetupPayments: 'Zahlungen einrichten',
	Sex: 'Geschlecht',
	SexSelectorPlaceholder: 'Männlich / Weiblich / Bevorzuge keine Angabe',
	Share: 'Aktie',
	ShareBookingLink: 'Buchungslink teilen',
	ShareNoteDefaultMessage: `Hallo{name} hat "{documentName}" mit dir geteilt.

Vielen Dank,
{practiceName}`,
	ShareNoteMessage: `Hallo
{name} hat "{documentName}" {isResponder, select, true {mit einigen Fragen zum Ausfüllen geteilt.} other {mit Ihnen geteilt.}}

Vielen Dank,
{practiceName}`,
	ShareNoteTitle: 'Teile „{noteTitle}“',
	ShareNotesWithClients: 'Mit Kunden oder Kontakten teilen',
	ShareScreen: 'Bildschirm freigeben',
	ShareScreenNotSupported: 'Ihr Gerät/Browser unterstützt die Funktion „Bildschirm teilen“ nicht',
	ShareScreenWithId: 'Bildschirm {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Erlaube anderen, diese Vorlage anzuzeigen und als Formular einzureichen.',
	ShareTemplateAsPublicFormModalTitle: 'Link teilen für ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Öffentliche Formular Konfiguration erfolgreich aktualisiert',
	ShareTemplateAsPublicFormSectionCustomization: 'Anpassung',
	ShareTemplateAsPublicFormShowPoweredBy: 'Zeige "Powered by Carepatron" auf meinem Formular',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage:
		'"Powered by Carepatron" auf meinem Formular ein-/ausblenden',
	ShareTemplateAsPublicFormTrigger: 'Teilen',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Workspace-Branding verwenden',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Arbeitsbereich-Branding anzeigen/ausblenden',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Sendet Code für bestehende und nicht bestehende Kunden',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Signaturen erfordern immer die Verifizierung der E-Mail-Adresse.',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Sendet nur Code für bestehende Kunden',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Sendet niemals Code',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning:
		'Die Auswahl von "Nie" kann es nicht verifizierten Benutzern ermöglichen, Kundendaten zu überschreiben, wenn sie die E-Mail-Adresse eines bestehenden Kunden verwenden.',
	ShareWithCommunity: 'Mit der Community teilen',
	ShareYourReferralLink: 'Teilen Sie Ihren Empfehlungslink',
	ShareYourScreen: 'Geben Sie Ihren Bildschirm frei',
	SheHer: 'Sie/Ihr',
	ShortTextAnswer: 'Kurze Textantwort',
	ShortTextFormPrimaryText: 'Kurzer Text',
	ShortTextFormSecondaryText: 'Antwort mit weniger als 300 Zeichen',
	Show: 'Zeigen',
	ShowColumn: 'Spalte anzeigen',
	ShowColumnButton: 'Spalte {value} Schaltfläche anzeigen',
	ShowColumns: 'Spalten anzeigen',
	ShowColumnsMenu: 'Menü „Spalten anzeigen“',
	ShowDateDurationDescription: 'zB. 29 Jahre alt',
	ShowDateDurationLabel: 'Datumsdauer anzeigen',
	ShowDetails: 'Zeige Details',
	ShowField: 'Feld anzeigen',
	ShowFullAddress: 'Adresse anzeigen',
	ShowHideFields: 'Felder ein-/ausblenden',
	ShowIcons: 'Symbole anzeigen',
	ShowLess: 'Zeige weniger',
	ShowMeetingTimers: 'Besprechungstimer anzeigen',
	ShowMenu: 'Zeige das Menü',
	ShowMergeSummarySidebar: 'Zeige Zusammenfassungsübersicht',
	ShowMore: 'Zeig mehr',
	ShowOnTranscript: 'Im Transkript anzeigen',
	ShowReactions: 'Reaktionen anzeigen',
	ShowSection: 'Abschnitt anzeigen',
	ShowServiceCode: 'Servicecode anzeigen',
	ShowServiceDescription: 'Beschreibung bei Servicebuchungen anzeigen',
	ShowServiceDescriptionDesc: 'Kunden können bei der Buchung Leistungsbeschreibungen einsehen',
	ShowServiceGroups: 'Kollektionen anzeigen',
	ShowServiceGroupsDesc: 'Kunden werden bei der Buchung die nach Kollektion gruppierten Dienstleistungen angezeigt',
	ShowSpeakers: 'Referenten anzeigen',
	ShowTax: 'Steuer anzeigen',
	ShowTimestamp: 'Zeitstempel anzeigen',
	ShowUnits: 'Einheiten anzeigen',
	ShowWeekends: 'Wochenenden anzeigen',
	ShowYourView: 'Zeigen Sie Ihre Meinung',
	SignInWithApple: 'Mit Apple anmelden',
	SignInWithGoogle: 'Anmeldung mit Google',
	SignInWithMicrosoft: 'Mit Microsoft anmelden',
	SignUpTitleReferralDefault: '<mark>Melden Sie sich an</mark> und fordern Sie Ihre Empfehlungsprämie an',
	SignUpTitleReferralUpgrade:
		'Starten Sie Ihr {durationInMonths} monatiges <mark>{percentOff, select, 100 {kostenlos} other {{percentOff}% Rabatt}} Upgrade</mark>',
	SignatureCaptureError: 'Die Signatur konnte nicht erfasst werden. Bitte versuchen Sie es erneut.',
	SignatureFormPrimaryText: 'Unterschrift',
	SignatureFormSecondaryText: 'Holen Sie sich eine digitale Signatur',
	SignatureInfoTooltip: 'Diese visuelle Darstellung ist keine gültige elektronische Signatur.',
	SignaturePlaceholder: 'Zeichnen Sie hier Ihre Unterschrift',
	SignedBy: 'Unterzeichnet von',
	Signup: 'Melden Sie sich an',
	SignupAgreements: 'Ich stimme den {termsOfUse} und der {privacyStatement} für mein Konto zu.',
	SignupBAA: 'Geschäftspartnervereinbarung',
	SignupBusinessAgreements:
		'Im Namen von mir und dem Unternehmen stimme ich der {businessAssociateAgreement}, den {termsOfUse} und der {privacyStatement} für mein Konto zu.',
	SignupInvitationForYou: 'Sie wurden eingeladen, Carepatron zu verwenden.',
	SignupPageProviderWarning:
		'Wenn Ihr Administrator bereits ein Konto erstellt hat, müssen Sie ihn bitten, Sie zu diesem Anbieter einzuladen. Verwenden Sie dieses Anmeldeformular nicht. Weitere Informationen finden Sie unter',
	SignupPageProviderWarningLink: 'dieser Link.',
	SignupPrivacy: 'Datenschutzrichtlinie',
	SignupProfession: 'Welcher Beruf beschreibt Sie am besten?',
	SignupSubtitle:
		'Die Praxisverwaltungssoftware von Carepatron ist für Einzelpraktiker und Teams konzipiert. Zahlen Sie keine überhöhten Gebühren mehr und werden Sie Teil der Revolution.',
	SignupSuccessDescription:
		'Bestätigen Sie Ihre E-Mail-Adresse, um mit dem Onboarding zu beginnen. Wenn Sie die E-Mail nicht sofort erhalten, überprüfen Sie bitte Ihren Spam-Ordner.',
	SignupSuccessTitle: 'Bitte überprüfen Sie Ihre E-Mail',
	SignupTermsOfUse: 'Nutzungsbedingungen',
	SignupTitleClient: '<mark>Verwalten Sie Ihre Gesundheit</mark> von einem Ort',
	SignupTitleLast: 'und all die Arbeit, die Sie tun! — Es ist kostenlos',
	SignupTitleOne: '<mark>Wir geben Ihnen die nötige Kraft</mark> , ',
	SignupTitleThree: '<mark>Unterstützung für Ihre Kunden</mark> , ',
	SignupTitleTwo: '<mark>Stärke dein Team</mark> , ',
	Simple: 'Einfach',
	SimplifyBillToDetails: 'Vereinfachen Sie die Rechnung auf Details',
	SimplifyBillToHelperText: 'Es wird nur die erste Zeile verwendet, wenn sie mit dem Client übereinstimmt.',
	Singapore: 'Singapur',
	Single: 'Einzel',
	SingleChoiceFormPrimaryText: 'Einzelauswahl',
	SingleChoiceFormSecondaryText: 'Wählen Sie nur eine Option',
	Sister: 'Schwester',
	SisterInLaw: 'Schwägerin',
	Skip: 'Überspringen',
	SkipLogin: 'Login überspringen',
	SlightBlur: 'Verwischen Sie den Hintergrund leicht',
	Small: 'Klein',
	SmartChips: 'Smartchips',
	SmartDataChips: 'Smarte Datenchips',
	SmartReply: 'Schnellantwort',
	SmartSuggestNewClient: '<strong>Smart Suggest</strong> {name} als neuen Kunden erstellen',
	SmartSuggestedFieldDescription: 'Dieses Feld ist eine intelligente Vorschlag',
	SocialSecurityNumber: 'Sozialversicherungsnummer',
	SocialWork: 'Sozialarbeit',
	SocialWorker: 'Sozialarbeiter',
	SoftwareDeveloper: 'Softwareentwickler',
	Solo: 'Solo',
	Someone: 'Jemand',
	Son: 'Sohn',
	SortBy: 'Sortiere nach',
	SouthAmerica: 'Südamerika',
	Speaker: 'Lautsprecher',
	SpeakerSource: 'Lautsprecherquelle',
	Speakers: 'Lautsprecher',
	SpecifyPaymentMethod: 'Zahlungsmethode angeben',
	SpeechLanguagePathology: 'Sprachpathologie',
	SpeechTherapist: 'Sprachtherapeut',
	SpeechTherapists: 'Logopäden',
	SpeechTherapy: 'Sprachtherapie',
	SportsMedicinePhysician: 'Sportmediziner',
	Spouse: 'Ehepartner',
	SpreadsheetColumnExample: 'z.B ',
	SpreadsheetColumns: 'Spalten der Tabellenkalkulation',
	SpreadsheetUploaded: 'Hochgeladene Tabelle',
	SpreadsheetUploading: 'Hochladen...',
	Staff: 'Personal',
	StaffAccessDescriptionAdmin: 'Administratoren können alles auf der Plattform verwalten.',
	StaffAccessDescriptionStaff: `Mitarbeiter können Kunden, Notizen und Dokumentationen verwalten, die sie erstellt oder freigegeben haben
 mit ihnen, vereinbaren Sie Termine, verwalten Sie Rechnungen.`,
	StaffContactAssignedSubject:
		'{actorProfileName} hat {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} und {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { und 1 weiteren Klienten} other { und # weitere Klienten}} zugewiesen',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} hat den Posteingang {inboxName} mit Ihnen geteilt',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} hat Ihnen den Zugriff auf den Posteingang {inboxName} entzogen.',
	StaffMembers: 'Mitarbeiter',
	StaffMembersNumber: '{billedUsers, plural, one {# Teammitglied} other {# Teammitglieder}}',
	StaffSavedSuccessSnackbar: 'Informationen zu Teammitgliedern erfolgreich gespeichert!',
	StaffSelectorAdminRole: 'Administrator',
	StaffSelectorStaffRole: 'Mitarbeiter',
	StandardAppointment: 'Standardtermin',
	StandardColor: 'Aufgabenfarbe',
	StartAndEndTime: 'Start- und Endzeit',
	StartCall: 'Anruf starten',
	StartDate: 'Startdatum',
	StartDictating: 'Beginnen Sie zu diktieren',
	StartImport: 'Import starten',
	StartRecordErrorTitle: 'Beim Starten Ihrer Aufzeichnung ist ein Fehler aufgetreten',
	StartRecording: 'Aufnahme starten',
	StartTimeIncrements: 'Startzeitinkremente',
	StartTimeIncrementsView: '{startTimeIncrements} min Intervalle',
	StartTranscribing: 'Beginnen Sie mit der Transkription',
	StartTranscribingNotes:
		'Beginnen Sie mit der Transkription, um Ihre Sitzung aufzuzeichnen und automatisch in Text umzuwand',
	StartTranscription: 'Transkript starten',
	StartVideoCall: 'Videoanruf starten',
	StartWeekOn: 'Start der Woche am',
	StartedBy: 'Gestartet von ',
	Starter: 'Anlasser',
	State: 'Zustand',
	StateIndustrialAccidentProviderNumber: 'Staatliche Betriebsunfall-Anbieternummer',
	StateLicenseNumber: 'Staatliche Lizenznummer',
	Statement: 'Stellungnahme',
	StatementDescriptor: 'Anweisungsdeskriptor',
	StatementDescriptorToolTip:
		'Der Kontoauszugsbezeichner wird auf den Bank- oder Kreditkartenabrechnungen Ihrer Kunden angezeigt. Er muss zwischen 5 und 22 Zeichen lang sein und den Namen Ihres Unternehmens wiedergeben.',
	StatementNumber: 'Anweisung #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Geben Sie eine Statusbezeichnung ein',
	StepFather: 'Stiefvater',
	StepMother: 'Stiefmutter',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription:
		'Wenn Sie das Ignorieren von Absendern beenden, werden alle zukünftigen E-Mails von diesem Absender wieder in Ihren Posteingang verschoben.',
	StopIgnoring: 'Ignorieren beenden',
	StopIgnoringSenders: 'Absender ignorieren beenden',
	StopIgnoringSendersSuccess: 'Erfolgreich ignorierte E-Mail-Adresse <mark>{addresses}</mark>',
	StopSharing: 'Hör auf zu teilen',
	StopSharingLabel: 'carepatron.com gibt Ihren Bildschirm frei.',
	Storage: 'Lagerung',
	StorageAlmostFullDescription: '🚀 Führen Sie jetzt ein Upgrade durch, damit Ihr Konto weiterhin reibungslos läuft.',
	StorageAlmostFullTitle: 'Du hast {percentage}% deines Workspace-Speicherlimits verbraucht!',
	StorageFullDescription: 'Erhalten Sie mehr Speicherplatz, indem Sie Ihren Plan aktualisieren.',
	StorageFullTitle: '	Ihr Speicher ist voll.',
	Street: 'Straße',
	StripeAccountNotCompleteErrorCode:
		'Online-Zahlungen sind nicht {hasProviderName, select, true {für {providerName} eingerichtet} other {für diesen Anbieter aktiviert}}.',
	StripeAccountRejectedError: 'Stripe-Konto wurde abgelehnt. Bitte kontaktieren Sie den Support.',
	StripeBalance: 'Streifenguthaben',
	StripeChargesInfoToolTip: 'Ermöglicht das Aufladen per Debitkarte ',
	StripeFeesDescription:
		'Carepatron verwendet Stripe, um Ihnen schnell Geld zu zahlen und Ihre Zahlungsinformationen sicher zu halten. Die verfügbaren Zahlungsmethoden variieren je nach Region, alle gängigen Debitkarten ',
	StripeFeesDescriptionItem1:
		'Bearbeitungsgebühren werden auf jede erfolgreiche Transaktion erhoben. Sie können {link}.',
	StripeFeesDescriptionItem2: 'Auszahlungen erfolgen täglich, werden jedoch bis zu 4 Tage lang zurückgehalten.',
	StripeFeesLinkToRatesText: 'Sehen Sie sich hier unsere Preise an',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Entschuldigung, es ist ein Mindestbetrag von {minimumAmount} für Rechnungen erforderlich, die Online-Zahlungen verwenden.',
	StripePaymentsDisabled: 'Online-Zahlungen deaktiviert. Bitte überprüfen Sie Ihre Zahlungseinstellungen.',
	StripePaymentsUnavailable: 'Zahlungen nicht verfügbar',
	StripePaymentsUnavailableDescription:
		'Beim Laden der Zahlungen ist ein Fehler aufgetreten. Bitte versuchen Sie es später noch einmal.',
	StripePayoutsInfoToolTip: 'Ermöglicht Ihnen die Auszahlung auf Ihr Bankkonto',
	StyleYourWorkspace: '<mark>Gestalten</mark> Sie Ihren Arbeitsbereich',
	StyleYourWorkspaceDescription1:
		'Wir haben die Markenressourcen von Ihrer Website abgerufen. Sie können diese gerne bearbeiten oder in Ihren Carepatron-Arbeitsbereich wechseln.',
	StyleYourWorkspaceDescription2:
		'Nutzen Sie Ihre Markenressourcen, um Rechnungen und Online-Buchungen zu personalisieren und ein nahtloses Kundenerlebnis zu schaffen',
	SubAdvanced: 'Erweitert',
	SubEssential: 'Wesentlich',
	SubOrganization: 'Organisation',
	SubPlus: 'Plus',
	SubProfessional: 'Fachmann',
	Subject: 'Thema',
	Submit: 'Einreichen',
	SubmitElectronically: 'Elektronisch einreichen',
	SubmitFeedback: 'Feedback senden',
	SubmitFormValidationError:
		'Bitte stellen Sie sicher, dass alle erforderlichen Felder korrekt ausgefüllt sind, und versuchen Sie es erneut.',
	Submitted: 'Gesendet',
	SubmittedDate: 'Eingereichtes Datum',
	SubscribePerMonth: 'Abonnieren {price} {isMonthly, select, true {pro Monat} other {pro Jahr}}',
	SubscriptionDiscountDescription:
		'{percentOff}% Rabatt {months, select, null { } other { {months, plural, one {für # Monat} other {für # Monate}}}}',
	SubscriptionFreeTrialDescription: 'Kostenlos bis {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Wir konnten die Zahlung für Ihr Abonnement nicht abschließen. Bitte überprüfen Sie Ihre Zahlungsdetails',
	SubscriptionPlanDetailsHeader: 'Pro Benutzer/monatlich, jährliche Abrechnung',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} monatlich abgerechnet (USD)',
	SubscriptionPlans: 'Abonnements',
	SubscriptionPlansDescription:
		'Erweitern Sie Ihren Plan, um zusätzliche Vorteile freizuschalten und Ihre Praxis reibungslos am Laufen zu halten.',
	SubscriptionPlansDescriptionNoPermission:
		'Es scheint, dass du derzeit keinen Zugriff auf ein Upgrade hast – wende dich bitte an deinen Administrator, um Hilfe zu erhalten.',
	SubscriptionSettings: 'Abonnementeinstellungen',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> des Speichers genutzt',
	SubscriptionSettingsStorageUsed: '{verwendet} von {limit} verwendet',
	SubscriptionSettingsUnlimitedStorage: 'Unbegrenzter Speicherplatz verfügbar',
	SubscriptionSummary: 'Abonnementübersicht',
	SubscriptionUnavailableOverStorageLimit: 'Deine aktuelle Nutzung überschreitet die Speichergrenze dieses Plans.',
	SubscriptionUnpaidBannerButton: 'Zu den Abonnements',
	SubscriptionUnpaidBannerDescription:
		'Bitte überprüfen Sie, ob Ihre Zahlungsdetails korrekt sind, und versuchen Sie es erneut',
	SubscriptionUnpaidBannerTitle: 'Wir konnten die Zahlung für Ihr Abonnement nicht abschließen.',
	Subscriptions: 'Abonnements',
	SubscriptionsAndPayments: 'Abonnements ',
	Subtotal: 'Zwischensumme',
	SuburbOrProvince: 'Vorort/Provinz',
	SuburbOrState: 'Vorort/Staat',
	SuccessSavedNoteChanges: 'Notizänderungen erfolgreich gespeichert',
	SuccessShareDocument: 'Dokument erfolgreich freigegeben',
	SuccessShareNote: 'Notiz erfolgreich geteilt',
	SuccessfullyCreatedValue: 'Erfolgreich erstellt {value}',
	SuccessfullyDeletedTranscriptionPart: 'Transkriptionsteil erfolgreich gelöscht',
	SuccessfullyDeletedValue: 'Erfolgreich gelöscht {value}',
	SuccessfullySubmitted: 'Erfolgreich eingereicht ',
	SuccessfullyUpdatedClientSettings: 'Client-Einstellungen erfolgreich aktualisiert',
	SuccessfullyUpdatedTranscriptionPart: 'Transkriptionsteil erfolgreich aktualisiert',
	SuccessfullyUpdatedValue: 'Erfolgreich aktualisiert {value}',
	SuggestedAIPoweredTemplates: 'Vorgeschlagene KI-gestützte Vorlagen',
	SuggestedAITemplates: 'Vorgeschlagene KI-Vorlagen',
	SuggestedActions: 'Vorgeschlagene Handlungen',
	SuggestedLocations: 'Vorgeschlagene Orte',
	Suggestions: 'Vorschläge',
	Summarise: 'KI zusammenfassen',
	SummarisingContent: 'Zusammenfassung von {title}',
	Sunday: 'Sonntag',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superbill ',
	SuperbillAutomationMonthly: 'Aktiv • Letzter Tag des Monats',
	SuperbillAutomationNoEmail:
		'Um automatisierte Rechnungsdokumente erfolgreich zu versenden, fügen Sie eine E-Mail-Adresse für diesen Kunden hinzu',
	SuperbillAutomationNotActive: 'Nicht aktiv',
	SuperbillAutomationUpdateFailure: 'Die Superbill-Automatisierungseinstellungen konnten nicht aktualisiert werden.',
	SuperbillAutomationUpdateSuccess: 'Superbill-Automatisierungseinstellungen erfolgreich aktualisiert',
	SuperbillClientHelperText: 'Diese Informationen werden aus den Kundendaten vorab ausgefüllt.',
	SuperbillNotFoundDescription:
		'Bitte wenden Sie sich an Ihren Anbieter und bitten Sie ihn um weitere Informationen oder um eine erneute Zusendung der Superbill.',
	SuperbillNotFoundTitle: 'Superbill nicht gefunden',
	SuperbillNumber: 'Superrechnung #{number}',
	SuperbillNumberAlreadyExists: 'Superbill-Quittungsnummer existiert bereits',
	SuperbillPracticeHelperText:
		'Diese Informationen werden aus den Abrechnungseinstellungen der Praxis vorab ausgefüllt.',
	SuperbillProviderHelperText: 'Diese Informationen werden aus den Personaldaten vorab ausgefüllt.',
	SuperbillReceipts: 'Superbill-Quittungen',
	SuperbillsEmptyStateDescription: 'Es wurden keine Superbills gefunden.',
	Surgeon: 'Der Chirurg',
	Surgeons: 'Chirurgen',
	SurgicalTechnologist: 'Operationstechnischer Assistent',
	SwitchFromAnotherPlatform: 'Ich wechsle von einer anderen Plattform',
	SwitchToMyPortal: 'Zu „Mein Portal“ wechseln',
	SwitchToMyPortalTooltip: `Greifen Sie auf Ihr persönliches Portal zu,
 So können Sie Ihre
 Portalerfahrung des Kunden.`,
	SwitchWorkspace: 'Arbeitsbereich wechseln',
	SwitchingToADifferentPlatform: 'Wechsel zu einer anderen Plattform',
	Sydney: 'Sydney',
	SyncCalendar: 'Kalender synchronisieren',
	SyncCalendarModalDescription:
		'Andere Teammitglieder können Ihre synchronisierten Kalender nicht sehen. Kundentermine können nur innerhalb von Carepatron aktualisiert oder gelöscht werden.',
	SyncCalendarModalDisplayCalendar: 'Meinen Kalender in Carepatron anzeigen',
	SyncCalendarModalSyncToCarepatron: 'Meinen Kalender mit Carepatron synchronisieren',
	SyncCalendarModalSyncWithCalendar: 'Carepatron-Termine mit meinem Kalender synchronisieren',
	SyncCarepatronAppointmentsWithMyCalendar: 'Carepatron-Termine mit meinem Kalender synchronisieren',
	SyncGoogleCalendar: 'Google Kalender synchronisieren',
	SyncInbox: 'Posteingang mit Carepatron synchronisieren',
	SyncMyCalendarToCarepatron: 'Synchrone meinen Kalender mit Carepatron',
	SyncOutlookCalendar: 'Outlook-Kalender synchronisieren',
	SyncedFromExternalCalendar: 'Aus externem Kalender synchronisiert',
	SyncingCalendarName: 'Synchronisiere {calendarName} Kalender',
	SyncingFailed: 'Synchronisierung fehlgeschlagen',
	SystemGenerated: 'Systemgeneriert',
	TFN: 'Steuernummer',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tisch',
	TableRowLabel: 'Tabellenzeile für {value}',
	TagSelectorNoOptionsText: 'Klicken Sie auf „Neu erstellen“, um ein neues Tag hinzuzufügen',
	Tags: 'Stichworte',
	TagsInputPlaceholder: 'Suchen oder erstellen Sie Tags',
	Task: 'Aufgabe',
	TaskAttendeeStatusUpdatedSuccess: 'Erfolgreich aktualisierte Terminstatus',
	Tasks: 'Aufgaben',
	Tax: 'Steuer',
	TaxAmount: 'Steuerbetrag',
	TaxID: 'Steuernummer',
	TaxIdType: 'Steuer-ID-Typ',
	TaxName: 'Steuerbezeichnung',
	TaxNumber: 'Steuernummer',
	TaxNumberType: 'Steuernummertyp',
	TaxNumberTypeInvalid: '{type} ist ungültig',
	TaxPercentageOfAmount: '{taxName} ({percentage}% von {amount})',
	TaxRate: 'Steuersatz',
	TaxRatesDescription: 'Verwalten Sie die Steuersätze, die auf Ihre Rechnungspositionen angewendet werden.',
	Taxable: 'Steuerpflichtig',
	TaxonomyCode: 'Taxonomiecode',
	TeacherAssistant: 'Lehrer-Assistent',
	Team: 'Team',
	TeamMember: 'Teammitglied',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {ist} other {sind}} bereits zu dieser Zeit gebucht.{br}Wählen Sie einen neuen Termin, um eine Doppelbuchung zu vermeiden.',
	TeamMembers: 'Teammitglieder',
	TeamMembersColour: 'Farbe der Teammitglieder',
	TeamMembersDetails: 'Details zu den Teammitgliedern',
	TeamSize: 'Wie viele Personen sind in Ihrem Team?',
	TeamTemplates: 'Team-Vorlagen',
	TeamTemplatesSectionDescription: 'Erstellt von Ihnen und Ihrem Team',
	TelehealthAndVideoCalls: 'Telemedizin ',
	TelehealthProvidedOtherThanInPatientCare: 'Telemedizin für andere als stationäre Behandlungen',
	TelehealthVideoCall: 'Telemedizinischer Videoanruf',
	Template: 'Vorlage',
	TemplateDescription: 'Vorlagenbeschreibung',
	TemplateDetails: 'Vorlagendetails',
	TemplateEditModeViewSwitcherDescription: 'Vorlage erstellen und bearbeiten',
	TemplateGallery: 'Community-Vorlagen',
	TemplateImportCompletedNotificationSubject: 'Vorlage importiert! {templateTitle} ist einsatzbereit.',
	TemplateImportFailedNotificationSubject: 'Datei {fileName} konnte nicht importiert werden.',
	TemplateName: 'Vorlagenname',
	TemplateNotFound: 'Vorlage konnte nicht gefunden werden.',
	TemplatePreviewErrorMessage: 'Beim Laden der Vorlagenvorschau ist ein Fehler aufgetreten',
	TemplateResponderModeViewSwitcherDescription: 'Vorschau und Interaktion mit Formularen',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Prüfen Sie, wie Ihre Formulare aussehen, wenn sie von den Antwortenden ausgefüllt werden',
	TemplateSaved: 'Gespeicherte Änderungen',
	TemplateTitle: 'Vorlagentitel',
	TemplateType: 'Vorlagentyp',
	Templates: 'Vorlagen',
	TemplatesCategoriesFilter: 'Nach Kategorie filtern',
	TemplatesPublicTemplatesFilter: ' Filtern nach Community/Team',
	Text: 'Text',
	TextAlign: 'Textausrichtung',
	TextColor: 'Textfarbe',
	ThankYouForYourFeedback: 'Vielen Dank für Ihr Feedback!',
	ThanksForLettingKnow: 'Danke, dass du es uns hast wissen lassen.',
	ThePaymentMethod: 'Die Zahlungsmethode',
	ThemThey: 'Sie/Ihnen',
	Theme: 'Thema',
	ThemeAllColorsPickerTitle: 'Mehr Designs',
	ThemeColor: 'Thema',
	ThemeColorDarkMode: 'Dunkel',
	ThemeColorLightMode: 'Licht',
	ThemeColorModePickerTitle: 'Farbmodus',
	ThemeColorSystemMode: 'System',
	ThemeCpColorPickerTitle: 'Carepatron-Designs',
	ThemePanelDescription: 'Wählen Sie zwischen hellem und dunklem Modus und passen Sie Ihre Designpräferenzen an',
	ThemePanelTitle: 'Erscheinungsbild',
	Then: 'Dann',
	Therapist: 'Therapeut',
	Therapists: 'Therapeuten',
	Therapy: 'Therapie',
	Thick: 'Dick',
	Thin: 'Dünn',
	ThirdPerson: '3. Person',
	ThisAndFollowingAppointments: 'Dieser und folgende Termine',
	ThisAndFollowingMeetings: 'Dieses und folgende Treffen',
	ThisAndFollowingReminders: 'Diese und folgende Erinnerungen',
	ThisAndFollowingTasks: 'Diese und folgende Aufgaben',
	ThisAppointment: 'Diese Ernennung',
	ThisMeeting: 'Dieses Treffen',
	ThisMonth: 'Diesen Monat',
	ThisPerson: 'Diese Person',
	ThisReminder: 'Diese Erinnerung',
	ThisTask: 'Diese Aufgabe',
	ThisWeek: 'Diese Woche',
	ThreeDay: '3 Tage',
	Thursday: 'Donnerstag',
	Time: 'Zeit',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Zeitformat',
	TimeIncrement: 'Zeitinkrement',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {Uhr morgens} other {Uhr nachmittags}}',
	TimeslotSize: 'Zeitfenstergröße',
	Timestamp: 'Zeitstempel',
	Timezone: 'Zeitzone',
	TimezoneDisplay: 'Zeitzonenanzeige',
	TimezoneDisplayDescription: 'Verwalten Sie Ihre Zeitzonenanzeigeeinstellungen.',
	Title: 'Titel',
	To: 'Zu',
	ToYourWorkspace: 'zu Ihrem Arbeitsplatz',
	Today: 'Heute',
	TodayInHoursPlural: 'Heute in {count} {count, plural, one {Stunde} other {Stunden}}',
	TodayInMinsAbbreviated: 'Heute in {count} {count, plural, one {Min} other {Minuten}}',
	ToggleHeaderCell: 'Kopfzeilenzelle umschalten',
	ToggleHeaderCol: 'Kopfspalte umschalten',
	ToggleHeaderRow: 'Kopfzeile umschalten',
	Tokyo: 'Tokio',
	Tomorrow: 'Morgen',
	TomorrowAfternoon: 'Morgen nachmittag',
	TomorrowMorning: 'Morgen früh',
	TooExpensive: 'Zu teuer',
	TooHardToSetUp: 'Zu schwer einzurichten',
	TooManyFiles: 'Mehr als 1 Datei erkannt.',
	ToolsExample: 'Einfache Praxis, Microsoft, Calendly, Asana, Doxy.me …',
	Total: 'Gesamt',
	TotalAccountCredit: 'Gesamtguthaben auf dem Konto',
	TotalAdjustments: 'Gesamtanpassungen',
	TotalAmountToCreditInCurrency: 'Gesamtbetrag zur Gutschrift ({currency})',
	TotalBilled: 'Gesamt berechnet',
	TotalConversations: '{total} {total, plural, =0 {Konversation} one {Konversation} other {Konversationen}}',
	TotalOverdue: 'Gesamtüberfällig',
	TotalOverdueTooltip:
		'Der Gesamtsaldo überfälliger Rechnungen umfasst alle unbezahlten Rechnungen, unabhängig vom Datumsbereich, die weder storniert noch bearbeitet wurden.',
	TotalPaid: 'Ganz bezahlt',
	TotalPaidTooltip:
		'Der Gesamtsaldo umfasst alle Beträge aus Rechnungen, die innerhalb des angegebenen Datumsbereichs bezahlt wurden.',
	TotalUnpaid: 'Unbezahlter Gesamtbetrag',
	TotalUnpaidTooltip:
		'Der Gesamtsaldo unbezahlter Rechnungen umfasst alle ausstehenden Beträge aus der Bearbeitung sowie unbezahlte und gesendete Rechnungen, die innerhalb des angegebenen Datumsbereichs fällig sind.',
	TotalWorkflows: '{count} {count, plural, one {Workflow} other {Workflows}}',
	TotpSetUpManualEntryInstruction: 'Alternativ können Sie den folgenden Code manuell in die App eingeben:',
	TotpSetUpModalDescription:
		'Scannen Sie den QR-Code mit Ihrer Authentifizierungs-App, um die Multi-Faktor-Authentifizierung einzurichten.',
	TotpSetUpModalTitle: 'Einrichten des MFA-Geräts',
	TotpSetUpSuccess: 'Sie sind fertig! MFA wurde aktiviert.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Geben Sie den von Ihrer Authentifizierungs-App generierten Code ein',
	Transcribe: 'Transkribieren',
	TranscribeLanguageSelector: 'Eingabesprache auswählen',
	TranscribeLiveAudio: 'Live-Audio transkribieren',
	Transcribing: 'Audio transkribieren...',
	TranscribingIn: 'Transkribieren in',
	Transcript: 'Transkript',
	TranscriptRecordingCompleteInfo: 'Nach Abschluss der Aufzeichnung wird Ihnen hier Ihr Transkript angezeigt.',
	TranscriptSuccessSnackbar: 'Transkript erfolgreich verarbeitet.',
	Transcription: 'Transkription',
	TranscriptionEmpty: 'Keine Transkription verfügbar',
	TranscriptionEmptyHelperMessage:
		'Diese Transkription hat nichts erkannt. Starten Sie sie neu und versuchen Sie es noch einmal.',
	TranscriptionFailedNotice: 'Diese Transkription wurde nicht erfolgreich verarbeitet',
	TranscriptionIdleMessage:
		'Wir hören kein Audio. Wenn Sie mehr Zeit benötigen, antworten Sie bitte innerhalb von {timeValue} Sekunden, andernfalls wird die Sitzung beendet.',
	TranscriptionInProcess: 'Transkript wird erstellt...',
	TranscriptionIncompleteNotice: 'Einige Teile dieser Transkription wurden nicht erfolgreich verarbeitet',
	TranscriptionOvertimeWarning: '{scribeType} Sitzung endet in <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Möchten Sie diesen Transkriptionsteil wirklich löschen?',
	TranscriptionText: 'Sprache zu Text',
	TranscriptsPending: 'Ihr Transkript wird nach Ende der Sitzung hier verfügbar sein.',
	Transfer: 'Überweisen',
	TransferAndDelete: 'Übertragen und Löschen',
	TransferOwnership: 'Eigentum übertragen',
	TransferOwnershipConfirmationModalDescription:
		'Diese Aktion kann nur rückgängig gemacht werden, wenn das Eigentum an Sie zurückübertragen wird.',
	TransferOwnershipDescription: 'Übertragen Sie den Besitz dieses Arbeitsbereichs an ein anderes Teammitglied.',
	TransferOwnershipSuccessSnackbar: 'Eigentum erfolgreich übertragen!',
	TransferOwnershipToMember: 'Sind Sie sicher, dass Sie diesen Arbeitsbereich an {staff} übertragen möchten?',
	TransferStatusAlert:
		'Das Entfernen von {numberOfStatuses, plural, one {diesem Status} other {diesen Statussen}} wirkt sich auf {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} Kundenstatus.</strong>} other {<strong>{numberOfAffectedRecords} Kundenstatusse.</strong>}} aus.',
	TransferStatusDescription:
		'Wählen Sie einen anderen Status für diese Kunden, bevor Sie mit dem Löschen fortfahren. Diese Aktion kann nicht rückgängig gemacht werden.',
	TransferStatusLabel: 'Überführung in den neuen Status',
	TransferStatusPlaceholder: 'Wählen Sie einen vorhandenen Status',
	TransferStatusTitle: 'Übertragungsstatus vor der Löschung',
	TransferTaskAttendeeStatusAlert:
		'Das Entfernen dieses Status wirkt sich auf <strong>{number} zukünftige Termin{number, plural, one {Status} other {Statussen}}</strong> aus.',
	TransferTaskAttendeeStatusDescription:
		'Wählen Sie einen anderen Status für diese Kunden aus, bevor Sie mit der Löschung fortfahren. Diese Aktion kann nicht rückgängig gemacht werden.',
	TransferTaskAttendeeStatusSubtitle: 'Terminstatus',
	TransferTaskAttendeeStatusTitle: 'Übertragungsstatus vor Löschung',
	Trash: 'Papierkorb',
	TrashDeleteItemsModalConfirm: 'Um zu bestätigen, geben Sie {confirmationText} ein',
	TrashDeleteItemsModalDescription:
		'Die folgenden {count, plural, one {Artikel} other {Artikel}} werden dauerhaft gelöscht und können nicht wiederhergestellt werden.',
	TrashDeleteItemsModalTitle: 'Löschen Sie {count, plural, one {Artikel} other {Artikel}} für immer',
	TrashDeletedAllItems: 'Alle Artikel gelöscht',
	TrashDeletedItems: 'Gelöscht {count, plural, one {Artikel} other {Artikel}}',
	TrashDeletedItemsFailure: 'Fehler beim Löschen von Elementen aus dem Papierkorb',
	TrashLocationAppointmentType: 'Kalender',
	TrashLocationBillingAndPaymentsType: 'Rechnungsstellung & Zahlungen',
	TrashLocationContactType: 'Kunden',
	TrashLocationNoteType: 'Notizen & Dokumente',
	TrashRestoreItemsModalDescription:
		'Die folgenden {count, plural, one {item} other {items}} werden wiederhergestellt.',
	TrashRestoreItemsModalTitle: 'Wiederherstellen {count, plural, one {Element} other {Elemente}}',
	TrashRestoredAllItems: 'Alle Artikel wiederhergestellt',
	TrashRestoredItems: 'Wiederhergestellt {count, plural, one {Artikel} other {Artikel}}',
	TrashRestoredItemsFailure: 'Wiederherstellen von Elementen aus dem Papierkorb fehlgeschlagen',
	TrashSuccessfullyDeletedItem: 'Erfolgreich gelöscht {type}',
	Trigger: 'Auslösen',
	Troubleshoot: 'Fehlerbehebung',
	TryAgain: 'Versuchen Sie es erneut',
	Tuesday: 'Dienstag',
	TwoToTen: '2 - 10',
	Type: 'Typ',
	TypeHere: 'Hier eingeben...',
	TypeToConfirm: 'Um zu bestätigen, geben Sie {keyword} ein',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Überschrift 1',
	TypographyHeading2: 'Überschrift 2',
	TypographyHeading3: 'Überschrift 3',
	TypographyHeading4: 'Überschrift 4',
	TypographyHeading5: 'Überschrift 5',
	TypographyP: 'P',
	TypographyParagraph: 'Absatz',
	UnableToCompleteAction: 'Aktion kann nicht abgeschlossen werden.',
	UnableToPrintDocument: 'Dokument kann nicht gedruckt werden. Bitte versuchen Sie es später erneut.',
	Unallocated: 'Nicht zugeordnet',
	UnallocatedPaymentDescription: `Diese Zahlung wurde den abrechenbaren Positionen nicht vollständig zugeordnet.
 Fügen Sie unbezahlten Artikeln eine Zuordnung hinzu oder stellen Sie eine Gutschrift bzw. Rückerstattung aus.`,
	UnallocatedPaymentTitle: 'Nicht zugewiesene Zahlung',
	UnallocatedPayments: 'Nicht zugewiesene Zahlungen',
	Unarchive: 'Archivierung aufheben',
	Unassigned: 'Nicht zugewiesen',
	UnauthorisedInvoiceSnackbar: 'Sie haben keinen Zugriff auf die Rechnungen für diesen Kunden.',
	UnauthorisedSnackbar: 'Du hast keine Erlaubnis das zu tun.',
	Unavailable: 'Nicht verfügbar',
	Uncategorized: 'Unkategorisiert',
	Unclaimed: 'Nicht beansprucht',
	UnclaimedAmount: 'Nicht abgeholter Betrag',
	UnclaimedItems: 'Nicht abgeholte Gegenstände',
	UnclaimedItemsMustBeInCurrency: 'Nur Artikel in folgenden Währungen werden unterstützt: {currencies}',
	Uncle: 'Onkel',
	Unconfirmed: 'Unbestätigt',
	Underline: 'Unterstreichen',
	Undo: 'Rückgängig machen',
	Unfavorite: 'Entfernen aus Favoriten',
	Uninvoiced: 'Nicht in Rechnung gestellt',
	UninvoicedAmount: 'Nicht in Rechnung gestellte Beträge',
	UninvoicedAmounts:
		'{count, plural, =0 {Keine unbelegten Beträge} one {Unbelegter Betrag} other {Unbelegte Beträge}}',
	Unit: 'Einheit',
	UnitedKingdom: 'Großbritannien',
	UnitedStates: 'Vereinigte Staaten',
	UnitedStatesEast: 'Vereinigte Staaten - Ost',
	UnitedStatesWest: 'Vereinigte Staaten - Westen',
	Units: 'Einheiten',
	UnitsIsRequired: 'Einheiten sind erforderlich',
	UnitsMustBeGreaterThanZero: 'Einheiten müssen größer als 0 sein',
	UnitsPlaceholder: '1',
	Unknown: 'Unbekannt',
	Unlimited: 'Unbegrenzt',
	Unlock: 'Freischalten',
	UnlockNoteHelper: 'Vor der Durchführung neuer Änderungen müssen Redakteure die Notiz entsperren.',
	UnmuteAudio: 'Ton einschalten',
	UnmuteEveryone: 'Stummschaltung für alle aufheben',
	Unpaid: 'Unbezahlt',
	UnpaidInvoices: 'Unbezahlte Rechnungen',
	UnpaidItems: 'Unbezahlte Artikel',
	UnpaidMultiple: 'Unbezahlt',
	Unpublish: 'Nicht veröffentlichen',
	UnpublishTemplateConfirmationModalPrompt:
		'Das Entfernen von <span>{title}</span> entfernt diese Ressource aus der Carepatron-Community. Diese Aktion kann nicht rückgängig gemacht werden.',
	UnpublishToCommunitySuccessMessage: 'Erfolgreich entfernt ‛{title}’ aus der Community',
	Unread: 'Ungelesen',
	Unrecognised: 'Unerkannt',
	UnrecognisedDescription:
		'Diese Zahlungsmethode wird von Ihrer aktuellen Anwendungsversion nicht erkannt. Bitte aktualisieren Sie Ihren Browser, um die neueste Version zum Anzeigen und Bearbeiten dieser Zahlungsmethode zu erhalten.',
	UnsavedChanges: 'Nicht gespeicherte Änderungen',
	UnsavedChangesPromptContent: 'Möchten Sie Ihre Änderungen vor dem Schließen speichern?',
	UnsavedChangesPromptTitle: 'Du hast nicht gespeicherte Änderungen',
	UnsavedNoteChangesWarning: 'Von Ihnen vorgenommene Änderungen werden möglicherweise nicht gespeichert',
	UnsavedTemplateChangesWarning: 'Von Ihnen vorgenommene Änderungen werden möglicherweise nicht gespeichert',
	UnselectAll: 'Alles abwählen',
	Until: 'Bis',
	UntitledConversation: 'Unbenannte Unterhaltung',
	UntitledFolder: 'Unbenannter Ordner',
	UntitledNote: 'Unbenannte Notiz',
	UntitledSchedule: 'Unbenannter Zeitplan',
	UntitledSection: 'Unbenannter Abschnitt',
	UntitledTemplate: 'Unbenannte Vorlage',
	Unverified: 'Nicht verifiziert',
	Upcoming: 'Bevorstehende',
	UpcomingAppointments: 'Nächste Termine',
	UpcomingDateOverridesEmpty: 'Es wurden keine Datumsüberschreibungen gefunden',
	UpdateAvailabilityScheduleFailure: 'Der Verfügbarkeitsplan konnte nicht aktualisiert werden.',
	UpdateAvailabilityScheduleSuccess: 'Verfügbarkeitsplan erfolgreich aktualisiert',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Möchten Sie, dass die neuen Preise auf die Rechnungen und Forderungen der Teilnehmer angewendet werden?',
	UpdateLink: 'Update-Link',
	UpdatePrimaryEmailWarningDescription:
		'Änderung der E-Mail-Adresse Ihres Kunden führt zum Verlust des Zugriffs auf bestehende Termine und Notizen.',
	UpdatePrimaryEmailWarningTitle: 'E-Mail-Änderung des Kunden',
	UpdateSettings: 'Update-Einstellungen',
	UpdateStatus: 'Update Status',
	UpdateSuperbillReceiptFailure: 'Superbill-Beleg konnte nicht aktualisiert werden',
	UpdateSuperbillReceiptSuccess: 'Superbill-Beleg erfolgreich aktualisiert',
	UpdateTaskBillingDetails: 'Rechnungsdetails aktualisieren',
	UpdateTaskBillingDetailsDescription:
		'Die Terminpreise haben sich geändert. Möchten Sie, dass die neuen Preise auf die Rechnungspositionen, Rechnungen und Forderungen der Teilnehmer angewendet werden? Wählen Sie die Aktualisierungen aus, mit denen Sie fortfahren möchten.',
	UpdateTemplateFolderSuccessMessage: 'Ordner erfolgreich aktualisiert',
	UpdateUnpaidInvoices: 'Unbezahlte Rechnungen aktualisieren',
	UpdateUserInfoSuccessSnackbar: 'Benutzerinformationen erfolgreich aktualisiert!',
	UpdateUserSettingsSuccessSnackbar: 'Benutzereinstellungen erfolgreich aktualisiert!',
	Upgrade: 'Aktualisierung',
	UpgradeForSMSReminder: 'Upgrade auf <b>Professional</b> für unbegrenzte SMS-Erinnerungen',
	UpgradeNow: 'Jetzt upgraden',
	UpgradePlan: 'Upgrade-Plan',
	UpgradeSubscriptionAlertDescription:
		'Du hast nicht mehr viel Speicherplatz. Rüste dein Abonnement auf, um zusätzlichen Speicherplatz freizuschalten und deinen Praxisbetrieb reibungslos zu gewährleisten!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Ihr Speicherplatz geht zur Neige. Fragen Sie jemanden in Ihrer Praxis mit <span>Administratorzugriff</span> nach einem Upgrade Ihres Plans, um zusätzlichen Speicherplatz freizuschalten und den reibungslosen Betrieb Ihrer Praxis zu gewährleisten!',
	UpgradeSubscriptionAlertTitle: 'Es ist Zeit, Ihr Abonnement zu aktualisieren',
	UpgradeYourPlan: 'Upgraden Sie Ihren Plan',
	UploadAudio: 'Audio hochladen',
	UploadFile: 'Datei hochladen',
	UploadFileDescription: 'Von welcher Softwareplattform wechseln Sie?',
	UploadFileMaxSizeError: 'Datei ist zu groß. Maximale Dateigröße ist {fileSizeLimit}.',
	UploadFileSizeLimit: 'Größenbeschränkung {size}MB',
	UploadFileTileDescription: 'Verwenden Sie CSV-, XLS-, XLSX- oder ZIP-Dateien, um Ihre Kunden hochzuladen.',
	UploadFileTileLabel: 'Datei hochladen',
	UploadFiles: 'Daten hochladen',
	UploadIndividually: 'Dateien einzeln hochladen',
	UploadLogo: 'Logo hochladen',
	UploadPhoto: 'Foto hochladen',
	UploadToCarepatron: 'Zu Carepatron hochladen',
	UploadYourLogo: 'Laden Sie Ihr Logo hoch',
	UploadYourTemplates: 'Laden Sie Ihre Vorlagen hoch und wir konvertieren sie für Sie',
	Uploading: 'Hochladen',
	UploadingAudio: 'Ihr Audio wird hochgeladen …',
	UploadingFiles: 'Hochladen von Dateien',
	UrlLink: 'URL-Link',
	UsageCount: '{count} Mal verwendet',
	UsageLimitValue: '{verwendet} von {limit} verwendet',
	UsageValue: '{verwendet} verwendet',
	Use: 'Verwenden',
	UseAiToAutomateYourWorkflow: 'Nutzen Sie KI, um Ihren Arbeitsablauf zu automatisieren!',
	UseAsDefault: 'Als Standard verwenden',
	UseCustom: 'Benutzerdefinierte verwenden',
	UseDefault: 'Verwende den Standard',
	UseDefaultFilters: 'Standardfilter verwenden',
	UseTemplate: 'Benutze Template',
	UseThisCard: 'Verwenden Sie diese Karte',
	UseValue: 'Verwenden Sie "{value}"',
	UseWorkspaceDefault: 'Arbeitsbereichsstandard verwenden',
	UserIsTyping: '{name} tippt...',
	Username: 'Nutzername',
	Users: 'Benutzer',
	VAT: 'Umsatzsteuer',
	ValidUrl: 'Der URL-Link muss eine gültige URL sein.',
	Validate: 'Bestätigen',
	Validated: 'Validiert',
	Validating: 'Validierung',
	ValidatingContent: 'Inhalt wird validiert...',
	ValidatingTranscripts: 'Transkripte werden validiert …',
	ValidationConfirmPasswordRequired: 'Bestätigung des Passworts ist erforderlich',
	ValidationDateMax: 'Muss vor {max} sein',
	ValidationDateMin: 'Muss nach {min} sein',
	ValidationDateRange: 'Start- und Enddatum sind erforderlich',
	ValidationEndDateMustBeAfterStartDate: 'Enddatum muss nach dem Startdatum liegen',
	ValidationMixedDefault: 'Das ist ungültig',
	ValidationMixedRequired: 'Dies ist erforderlich',
	ValidationNumberInteger: 'Muss eine ganze Zahl sein',
	ValidationNumberMax: 'Muss {max} oder weniger sein',
	ValidationNumberMin: 'Muss mindestens {min} sein',
	ValidationPasswordNotMatching: 'Passwörter stimmen nicht überein',
	ValidationPrimaryAddressIsRequired: 'Wenn die Adresse als Standard festgelegt ist, ist sie erforderlich.',
	ValidationPrimaryPhoneNumberIsRequired: 'Wenn die Telefonnummer als Standard festgelegt ist, ist sie erforderlich.',
	ValidationServiceMustBeNotBeFuture: 'Der Service darf nicht im heutigen Tag oder in der Zukunft liegen.',
	ValidationStringEmail: 'Es muss sich um eine gültige E-Mail-Adresse handeln',
	ValidationStringMax: 'Muss {max} Zeichen oder weniger haben',
	ValidationStringMin: 'Muss mindestens {min} Zeichen lang sein',
	ValidationStringPhoneNumber: 'Muss eine gültige Telefonnummer sein',
	ValueMinutes: '{value} Minuten',
	VerbosityConcise: 'Prägnant',
	VerbosityDetailed: 'Detaillierte',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Super detailliert',
	VerificationCode: 'Verifizierungscode',
	VerificationEmailDescription:
		'Bitte geben Sie Ihre E-Mail-Adresse und den Verifizierungscode ein, den wir Ihnen gerade gesendet haben.',
	VerificationEmailSubtitle: 'Überprüfen Sie den Spam-Ordner - falls die E-Mail nicht angekommen ist',
	VerificationEmailTitle: 'E-Mail verifizieren',
	VerificationOption: 'E-Mail-Verifizierung',
	Verified: 'Verifiziert',
	Verify: 'Verifizieren',
	VerifyAndSubmit: 'Überprüfen & absenden',
	VerifyEmail: 'E-Mail verifizieren',
	VerifyEmailAccessCode: 'Bestätigungscode',
	VerifyEmailAddress: 'Email Adresse bestätigen',
	VerifyEmailButton: 'Überprüfen und abmelden',
	VerifyEmailSentSnackbar: 'Bestätigungs-E-Mail gesendet. Überprüfen Sie Ihren Posteingang.',
	VerifyEmailSubTitle: 'Überprüfen Sie den Spam-Ordner, wenn die E-Mail nicht angekommen ist',
	VerifyEmailSuccessLogOutSnackbar: 'Erfolg! Bitte melden Sie sich ab, um die Änderungen zu übernehmen.',
	VerifyEmailSuccessSnackbar:
		'Erfolgreich! E-Mail verifiziert. Bitte melden Sie sich an, um mit einem verifizierten Konto fortzufahren.',
	VerifyEmailTitle: 'Bestätigen Sie Ihre E-Mail',
	VerifyNow: 'Jetzt verifizieren',
	Veterinarian: 'Tierarzt',
	VideoCall: 'Videoanruf',
	VideoCallAudioInputFailed: 'Audio-Eingabegerät funktioniert nicht',
	VideoCallAudioInputFailedMessage:
		'Öffnen Sie die Einstellungen und überprüfen Sie, ob die Mikrofonquelle richtig eingestellt ist',
	VideoCallChatBanner:
		'Nachrichten sind für alle Teilnehmer dieses Anrufs sichtbar und werden nach Ende des Anrufs gelöscht.',
	VideoCallChatSendBtn: 'Eine Nachricht schicken',
	VideoCallChatTitle: 'Plaudern',
	VideoCallDisconnectedMessage:
		'Sie haben Ihre Netzwerkverbindung verloren. Ich versuche, die Verbindung wiederherzustellen.',
	VideoCallOptionInfo: 'Carepatron verwaltet Videoanrufe für Ihre Termine, wenn Zoom nicht verbunden ist',
	VideoCallTilePaused: 'Dieses Video wurde aufgrund von Problemen mit Ihrem Netzwerk angehalten.',
	VideoCallTranscriptionFormDescription: 'Sie können diese Einstellungen jederzeit anpassen',
	VideoCallTranscriptionFormHeading: 'Passen Sie Ihren AI Scribe an',
	VideoCallTranscriptionFormLanguageField: 'Die generierte Ausgabesprache',
	VideoCallTranscriptionFormNoteTemplateField: 'Standard-Notizvorlage festlegen',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Keine Vorlagen mit KI gefunden',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Auswählen einer Vorlage',
	VideoCallTranscriptionPronounField: 'Dein Pronomen',
	VideoCallTranscriptionRecordingNote:
		'Am Ende der Sitzung erhalten Sie eine generierte <strong>{noteTemplate}-Notiz</strong> und ein Transkript.',
	VideoCallTranscriptionReferClientField: 'Beziehen Sie sich auf den Kunden als',
	VideoCallTranscriptionReferPractitionerField: 'Beziehen Sie sich auf den Praktiker als',
	VideoCallTranscriptionTitle: 'KI-Schreiber',
	VideoCallTranscriptionVerbosityField: 'Ausführlichkeit',
	VideoCallTranscriptionWritingPerspectiveField: 'Schreibperspektive',
	VideoCalls: 'Videoanrufe',
	VideoConferencing: 'Videokonferenzen',
	VideoOff: 'Video ist ausgeschaltet',
	VideoOn: 'Video ist ausgeschaltet',
	VideoQual360: 'Niedrige Qualität (360p)',
	VideoQual540: 'Mittlere Qualität (540p)',
	VideoQual720: 'Hohe Qualität (720p)',
	View: 'Sicht',
	ViewAll: 'Alle ansehen',
	ViewAppointment: 'Termin einsehen',
	ViewBy: 'Gesehen von',
	ViewClaim: 'Anzeige des Antrags',
	ViewCollection: 'Kollektion anzeigen',
	ViewDetails: 'Details anzeigen',
	ViewEnrollment: 'Anmeldung anzeigen',
	ViewPayment: 'Zahlung ansehen',
	ViewRecord: 'Datensatz anzeigen',
	ViewRemittanceAdvice: 'Überweisungsschein anzeigen',
	ViewRemittanceAdviceHeader: 'Gutschrift-Empfangsbestätigung',
	ViewRemittanceAdviceSubheader: 'Anspruch {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Einstellungen anzeigen',
	ViewStripeDashboard: 'Stripe-Dashboard anzeigen',
	ViewTemplate: 'Vorlage anzeigen',
	ViewTemplates: 'Vorlagen anzeigen',
	ViewableBy: 'Sichtbar für',
	ViewableByHelper:
		'Sie und das Team haben immer Zugriff auf die von Ihnen veröffentlichten Notizen. Sie können diese Notiz mit dem Kunden und/oder seinen Beziehungen teilen.',
	Viewer: 'Zuschauer',
	VirtualLocation: 'Virtueller Standort',
	VisibleTo: 'Sichtbar für',
	VisitOurHelpCentre: 'Besuchen Sie unser Hilfecenter',
	VisualEffects: 'Visuelle Effekte',
	VoiceFocus: 'Sprachfokus',
	VoiceFocusLabel: 'Filtert Geräusche aus Ihrem Mikrofon heraus, die keine Sprache sind',
	Void: 'Leere',
	VoidCancelPriorClaim: 'Void/Storniere vorherige Forderung',
	WaitingforMins: 'Warten für {count} Min.',
	Warning: 'Warnung',
	WatchAVideo: 'sich ein Video ansehen',
	WatchDemoVideo: 'Demo-Video ansehen',
	WebConference: 'Webkonferenz',
	WebConferenceOrVirtualLocation: 'Webkonferenz / virtueller Standort',
	WebDeveloper: 'Web-Entwickler',
	WebsiteOptional: 'Webseite<span>(Optional)</span>',
	WebsiteUrl: 'Webadresse',
	Wednesday: 'Mittwoch',
	Week: 'Woche',
	WeekPlural: '{count, plural, one {Woche} other {Wochen}}',
	Weekly: 'Wöchentlich',
	WeeksPlural: '{age, plural, one {# Woche} other {# Wochen}}',
	WelcomeBack: 'Willkommen zurück',
	WelcomeBackName: 'Willkommen zurück, {name}',
	WelcomeName: 'Willkommen {name}',
	WelcomeToCarepatron: 'Willkommen bei Carepatron',
	WhatCanIHelpWith: 'Womit kann ich helfen?',
	WhatDidYouLikeResponse: 'Was hat Ihnen an dieser Antwort gefallen?',
	WhatIsCarepatron: 'Was ist Carepatron?',
	WhatMadeYouCancel: `Warum haben Sie Ihren Plan abgesagt?
 Zutreffendes bitte ankreuzen.`,
	WhatServicesDoYouOffer: 'Welche <mark>Dienstleistungen</mark> bieten Sie an?',
	WhatServicesDoYouOfferDescription: 'Sie können später weitere Dienstleistungen bearbeiten oder hinzufügen.',
	WhatsYourAvailability: 'Wie sieht deine <mark>Verfügbarkeit</mark> aus?',
	WhatsYourAvailabilityDescription: 'Sie können später weitere Zeitpläne hinzufügen.',
	WhatsYourBusinessName: 'Wie lautet der <mark>Name Ihres Unternehmens</mark>?',
	WhatsYourTeamSize: 'Wie groß ist Ihr <mark>Team?</mark>',
	WhatsYourTeamSizeDescription: 'Dies hilft uns, Ihren Arbeitsplatz korrekt einzurichten.',
	WhenThisHappens: 'Wenn dies passiert:',
	WhichBestDescribesYou: 'Was <mark>beschreibt Sie</mark> am besten?',
	WhichPlatforms: 'Welche Plattformen?',
	Wife: 'Gattin',
	WorkflowDescription: 'Workflow-Beschreibung',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Vorlagen können mit Workflows verlinkt werden, um Prozesse zu optimieren. Zeigen Sie verlinkte Workflows an, um sie einfach zu verfolgen und zu aktualisieren.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Verbinden Sie Ihre SMS + E-Mails anhand gemeinsamer Auslöser',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Workflow-Automatisierung',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Automatisierte Workflows',
	WorkflowTemplateConfigKey_Body: 'Körper',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Branding anzeigen',
	WorkflowTemplateConfigKey_Content: 'Inhalt',
	WorkflowTemplateConfigKey_Footer: 'Fußzeile',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Fußzeile anzeigen',
	WorkflowTemplateConfigKey_Header: 'Überschrift',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Kopfzeile anzeigen',
	WorkflowTemplateConfigKey_SecurityFooter: 'Sicherheitsfußzeile',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Sicherheitsfußzeile anzeigen',
	WorkflowTemplateConfigKey_Subject: 'Betreff',
	WorkflowTemplateConfigKey_Title: 'Titel',
	WorkflowTemplateDeleteConfirmationMessage:
		'Sind Sie sicher, dass Sie diese Vorlage löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.',
	WorkflowTemplateDeleteConfirmationTitle: 'Benachrichtigungsvorlage löschen',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Sind Sie sicher? Dies wird nur die {locale}-Version entfernen – andere Sprachen werden nicht beeinflusst. Diese Aktion kann nicht rückgängig gemacht werden.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Löschen Sie die Vorlage „{locale}“',
	WorkflowTemplateDeletedSuccess: 'Benachrichtigungsvorlage erfolgreich gelöscht',
	WorkflowTemplateEditorDetailsTab: 'Vorlagendetails',
	WorkflowTemplateEditorEmailContent: 'E-Mail-Inhalt',
	WorkflowTemplateEditorEmailContentTab: 'E-Mail-Inhalt',
	WorkflowTemplateEditorThemeTab: 'Thema',
	WorkflowTemplatePreviewerAlert: 'Vorschauen verwenden Beispieldaten, um zu zeigen, was Ihre Kunden sehen werden.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Bist du sicher? Dies setzt die Version auf die Standardvorlage des Systems zurück. Diese Aktion kann nicht rückgängig gemacht werden.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Vorlage zurücksetzen',
	WorkflowTemplateSendTestEmail: 'Test-E-Mail senden',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Testen Sie Ihre E-Mail-Einrichtung, indem Sie eine Test-E-Mail an sich selbst senden.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Empfänger-E-Mail',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Test senden',
	WorkflowTemplateSendTestEmailDialogTitle: 'Sende eine Test-E-Mail',
	WorkflowTemplateSendTestEmailSuccess: 'Erfolg! Ihre <mark>{templateName}</mark> Test-E-Mail wurde gesendet.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Verwalten Sie Ihre Vorlagen und fügen Sie mehrere Sprachversionen hinzu, um effektiv mit Kunden zu kommunizieren.',
	WorkflowTemplateTemplateEditor: 'Vorlageneditor',
	WorkflowTemplateTranslateLocaleError: 'Beim Übersetzen von Inhalten ist ein Fehler aufgetreten.',
	WorkflowTemplateTranslateLocaleSuccess: 'Erfolgreich den Inhalt in **{locale}** übersetzt',
	WorkflowsAndReminders: 'Workflows ',
	WorkflowsManagement: 'Workflow-Management',
	WorksheetAndHandout: 'Arbeitsblatt/Handout',
	WorksheetsAndHandoutsDescription: 'Für Kundenbindung und -bildung',
	Workspace: 'Arbeitsplatz',
	WorkspaceBranding: 'Arbeitsbereich-Branding',
	WorkspaceBrandingDescription: `Verleihen Sie Ihrem Arbeitsplatz mühelos einen einheitlichen Stil, der Ihre
 Professionalität und Persönlichkeit. Passen Sie Rechnungen an Online-Buchungen an, um ein schönes
 Kundenerfahrung.`,
	WorkspaceName: 'Name des Arbeitsbereichs',
	Workspaces: 'Arbeitsbereiche',
	WriteOff: 'Abschreibung',
	WriteOffModalDescription:
		'Sie haben <mark>{count} {count, plural, one {Zeilenelement} other {Zeilenelemente}}</mark> zu streichen',
	WriteOffModalTitle: 'Abschreibungsanpassung',
	WriteOffReasonHelperText: 'Dies ist eine interne Notiz und ist für Ihren Kunden nicht sichtbar.',
	WriteOffReasonPlaceholder:
		'Das Hinzufügen eines Abschreibungsgrundes kann bei der Überprüfung abrechenbarer Transaktionen hilfreich sein',
	WriteOffTotal: 'Totaler Totalschaden ({currencyCode})',
	Writer: 'Schriftsteller',
	Yearly: 'Jährlich',
	YearsPlural: '{age, plural, one {# Jahr} other {# Jahre}}',
	Yes: 'Ja',
	YesArchive: 'Ja, Archiv',
	YesDelete: 'Ja, löschen',
	YesDeleteOverride: 'Ja, Überschreibung löschen',
	YesDeleteSection: 'Ja, löschen',
	YesDisconnect: 'Ja, trennen',
	YesEnd: 'Ja, Ende',
	YesEndTranscription: 'Ja, Transkription beenden',
	YesImFineWithThat: 'Ja, das ist für mich in Ordnung',
	YesLeave: 'Ja, geh.',
	YesMinimize: 'Ja, minimieren',
	YesOrNoAnswerTypeDescription: 'Konfigurieren des Antworttyps',
	YesOrNoFormPrimaryText: 'Ja | Nein',
	YesOrNoFormSecondaryText: 'Wählen Sie die Optionen „Ja“ oder „Nein“',
	YesProceed: 'Ja, weitermachen',
	YesRemove: 'Ja, entfernen',
	YesRestore: 'Ja, wiederherstellen',
	YesStopIgnoring: 'Ja, Ignorieren beenden',
	YesTransfer: 'Ja, Überweisung',
	Yesterday: 'Gestern',
	YogaInstructor: 'Yogalehrerin',
	You: 'Du',
	YouArePresenting: 'Sie präsentieren',
	YouCanChooseMultiple: 'Sie können mehrere auswählen',
	YouCanSelectMultiple: 'Sie können mehrere auswählen',
	YouHaveOngoingTranscription: 'Sie haben eine laufende Transkription',
	YourAnswer: 'Deine Antwort',
	YourDisplayName: 'Ihr Anzeigename',
	YourSpreadsheetColumns: 'Ihre Tabellenspalten',
	YourTeam: 'Dein Team',
	ZipCode: 'PLZ',
	Zoom: 'Zoomen',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Sie können für dieses Teammitglied keinen Zoom-Anruf hinzufügen. <a>Weitere Informationen finden Sie in den Support-Dokumenten.</a>',
};

export default items;
