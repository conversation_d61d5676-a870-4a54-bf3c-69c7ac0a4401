import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'Numéro de TVA',
	AIPrompts: `Invites de l'IA`,
	ATeamMemberIsRequired: `Un membre d'équipe est requis`,
	AboutClient: 'À propos du client',
	AcceptAppointment: `Merci d'avoir confirmé votre rendez-vous`,
	AcceptTermsAndConditionsRequired: 'Accepter les termes ',
	Accepted: 'Accepté',
	AccessGiven: 'Accès donné',
	AccessPermissions: `Autorisations d'accès`,
	AccessType: `Type d'accès`,
	Accident: 'Accident',
	Account: 'Compte',
	AccountCredit: 'Crédit de compte',
	Accountant: 'Comptable',
	Action: 'Action',
	Actions: 'Actions',
	Active: 'Actif',
	ActiveTags: 'Balises actives',
	ActiveUsers: 'Utilisateurs actifs',
	Activity: 'Activité',
	Actor: 'Acteur',
	Acupuncture: 'Acupuncture',
	Acupuncturist: 'Acupuncteur',
	Acupuncturists: 'Acupuncteurs',
	AcuteManifestationOfAChronicCondition: 'Manifestation aiguë d’une maladie chronique',
	Add: 'Ajouter',
	AddADescription: 'ajouter une description',
	AddALocation: 'Ajouter un emplacement',
	AddASecondTimezone: 'Ajouter un deuxième fuseau horaire',
	AddAddress: 'Ajouter une adresse',
	AddAnother: '  Ajouter un autre',
	AddAnotherAccount: 'Ajouter un autre compte',
	AddAnotherContact: 'Ajouter un autre contact',
	AddAnotherOption: 'Ajouter une autre option',
	AddAnotherTeamMember: `Ajouter un autre membre de l'équipe`,
	AddAvailablePayers: '+ Ajouter les payeurs disponibles',
	AddAvailablePayersDescription: `Recherchez des payeurs à ajouter à votre liste de payeurs de l'espace de travail. Après les avoir ajoutés, vous pouvez gérer les inscriptions ou ajuster les détails du payeur selon vos besoins.`,
	AddCaption: 'Ajouter une légende',
	AddClaim: 'Ajouter une réclamation',
	AddClientFilesModalDescription: `Pour restreindre l'accès, choisissez les options dans les cases à cocher « Visible par »`,
	AddClientFilesModalTitle: 'Télécharger des fichiers pour {name}',
	AddClientNoteButton: 'Ajouter une note',
	AddClientNoteModalDescription:
		'Ajoutez du contenu à votre note. Utilisez la section « Visible par » pour sélectionner un ou plusieurs groupes pouvant voir cette note spécifique.',
	AddClientNoteModalTitle: 'Ajouter une note',
	AddClientOwnerRelationshipModalDescription: `Inviter le client lui permettra de gérer ses propres informations de profil et de gérer l'accès des utilisateurs à ses informations de profil.`,
	AddClientOwnerRelationshipModalTitle: 'Inviter le client',
	AddCode: 'Ajouter du code',
	AddColAfter: 'Ajouter une colonne après',
	AddColBefore: 'Ajouter une colonne avant',
	AddCollection: 'Ajouter une collection',
	AddColor: 'Ajouter de la couleur',
	AddColumn: 'Ajouter une colonne',
	AddContactRelationship: 'Ajouter une relation de contact',
	AddContacts: 'Ajouter des contacts',
	AddCustomField: 'Ajouter un champ personnalisé',
	AddDate: 'Ajouter une date',
	AddDescription: 'Ajouter une description',
	AddDetail: 'Ajouter des détails',
	AddDisplayName: `Ajouter un nom d'affichage`,
	AddDxCode: 'Ajouter un code de diagnostic',
	AddEmail: 'Ajouter un e-mail',
	AddFamilyClientRelationshipModalDescription: `Inviter un membre de la famille lui permettra de voir les histoires de soins et les informations de profil du client. S'ils sont invités en tant qu'administrateurs, ils auront accès pour mettre à jour les informations de profil du client et gérer l'accès des utilisateurs.`,
	AddFamilyClientRelationshipModalTitle: 'Inviter un membre de la famille',
	AddField: 'Ajouter le champ',
	AddFormField: 'Ajouter un champ de formulaire',
	AddImages: 'Ajouter des images',
	AddInsurance: 'Ajouter une assurance',
	AddInvoice: 'Ajouter une facture',
	AddLabel: 'Ajouter une étiquette',
	AddLanguage: 'Ajouter une langue',
	AddLocation: 'Ajouter un emplacement',
	AddManually: 'Ajouter manuellement',
	AddMessage: 'Ajouter un message',
	AddNewAction: 'Ajouter une nouvelle action',
	AddNewSection: 'Ajouter une nouvelle rubrique',
	AddNote: 'Ajouter une note',
	AddOnlineBookingDetails: 'Ajouter les détails de la réservation en ligne',
	AddPOS: 'Ajouter un point de vente',
	AddPaidInvoices: 'Ajouter des factures payées',
	AddPayer: 'Ajouter un payeur',
	AddPayment: 'Ajouter un paiement',
	AddPaymentAdjustment: 'Ajouter un ajustement de paiement',
	AddPaymentAdjustmentDisabledDescription: 'Les affectations de paiement ne seront pas modifiées.',
	AddPaymentAdjustmentEnabledDescription: 'Le montant disponible pour l’attribution sera réduit.',
	AddPhoneNumber: 'Ajouter un numéro de téléphone',
	AddPhysicalOrVirtualLocations: 'Ajouter des emplacements physiques ou virtuels',
	AddQuestion: 'Ajouter une question',
	AddQuestionOrTitle: 'Ajouter une question ou un titre',
	AddRelationship: 'Ajouter une relation',
	AddRelationshipModalTitle: 'Connecter un contact existant',
	AddRelationshipModalTitleNewClient: 'Connecter un nouveau contact',
	AddRow: 'Ajouter une rangée',
	AddRowAbove: 'Ajouter une ligne ci-dessus',
	AddRowBelow: 'Ajouter une ligne ci-dessous',
	AddService: 'Ajouter un service',
	AddServiceLocation: 'Ajouter un emplacement de service',
	AddServiceToCollections: 'Ajouter un service aux collections',
	AddServiceToOneOrMoreCollections: 'Ajouter un service à une ou plusieurs collections',
	AddServices: 'Ajouter des services',
	AddSignature: 'Ajouter une signature',
	AddSignaturePlaceholder: 'Tapez les détails supplémentaires à inclure avec votre signature',
	AddSmartDataChips: 'Ajoutez des puces de données intelligentes',
	AddStaffClientRelationshipsModalDescription:
		'La sélection du personnel leur permettra de créer et de visualiser des histoires de soins pour ce client. Ils pourront également consulter les informations sur les clients.',
	AddStaffClientRelationshipsModalTitle: 'Ajouter des relations avec le personnel',
	AddTag: 'Ajouter une balise',
	AddTags: 'Ajouter des balises',
	AddTemplate: 'Ajouter un modèle',
	AddTimezone: 'Ajouter un fuseau horaire',
	AddToClaim: 'Ajouter à la réclamation',
	AddToCollection: 'Ajouter à la collection',
	AddToExisting: `Ajouter à l'existant`,
	AddToStarred: 'Ajouter aux favoris',
	AddUnclaimedItems: 'Ajouter des articles non réclamés',
	AddUnrelatedContactWarning: `Vous avez ajouté une personne qui n'est pas un contact de {contact}. Assurez-vous que le contenu est pertinent avant de poursuivre le partage.`,
	AddValue: 'Ajouter "{value}"',
	AddVideoCall: 'Ajouter un appel vidéo',
	AddVideoOrVoiceCall: 'Ajouter un appel vidéo ou vocal',
	AddictionCounselor: 'Conseiller en toxicomanie',
	AddingManualPayerDisclaimer: `L'ajout manuel d'un payeur à votre liste de fournisseurs n'établit pas automatiquement une connexion de dépôt électronique de réclamations avec ce payeur. Si vous devez déposer par voie électronique, vous devrez le configurer séparément.`,
	AddingTeamMembersIncreaseCostAlert: `L'ajout de nouveaux membres à l'équipe augmentera votre abonnement mensuel.`,
	Additional: 'Supplémentaire',
	AdditionalBillingProfiles: 'Profils de facturation supplémentaires',
	AdditionalBillingProfilesSectionDescription: `Remplacez les informations de facturation par défaut utilisées pour des membres d'équipe, des payeurs ou des modèles de facture spécifiques.`,
	AdditionalFeedback: 'Commentaires supplémentaires',
	AddnNewWorkspace: 'Nouvel espace de travail',
	AddnNewWorkspaceSuccessSnackbar: `L'espace de travail a été créé !`,
	Address: 'Adresse',
	AddressNumberStreet: 'Adresse (N°, rue)',
	Adjustment: 'Ajustement',
	AdjustmentType: 'Type de réglage',
	Admin: 'Administrateur',
	Admins: 'Administrateurs',
	AdminsOnly: 'Administrateurs uniquement',
	AdvancedPlanInclusionFive: 'Gestionnaire de compte',
	AdvancedPlanInclusionFour: 'Google Analytics',
	AdvancedPlanInclusionHeader: 'Tout en Plus  ',
	AdvancedPlanInclusionOne: 'Rôles ',
	AdvancedPlanInclusionSix: `Prise en charge de l'importation de données`,
	AdvancedPlanInclusionThree: 'Marque blanche',
	AdvancedPlanInclusionTwo: 'Conservation des données supprimées pendant 90 jours',
	AdvancedPlanMessage:
		'Restez maître des besoins de votre cabinet. Examinez votre plan actuel et surveillez l’utilisation.',
	AdvancedSettings: 'Réglages avancés',
	AdvancedSubscriptionPlanSubtitle: 'Développez votre pratique avec toutes les fonctionnalités',
	AdvancedSubscriptionPlanTitle: 'Avancé',
	AdvertisingManager: 'Directeur de la publicité',
	AerospaceEngineer: 'Ingénieur aérospatial',
	AgeYearsOld: '{age} ans',
	Agenda: 'Ordre du jour',
	AgendaView: 'Vue Agenda',
	AiAskSupportedFileTypes: 'Types de fichiers pris en charge : JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Un assistant à portée de main',
	AiCopilotDisclaimer: 'AI Copilot peut faire des erreurs. Vérifiez les informations importantes.',
	AiCreateNewConversation: 'Créer une nouvelle conversation',
	AiEnhanceYourProductivity: 'Améliorez votre productivité',
	AiPoweredTemplates: `Modèles alimentés par l'IA`,
	AiScribeNoDeviceFoundErrorMessage: `Il semble que votre navigateur ne prenne pas en charge cette fonctionnalité ou qu'aucun appareil compatible ne soit disponible.`,
	AiScribeUploadFormat: 'Types de fichiers pris en charge : MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'un seul fichier à la fois',
	AiShowConversationHistory: `Afficher l'historique de la conversation`,
	AiSmartPromptNodePlaceholderText: `Tapez votre invite personnalisée ici pour aider à générer des résultats d'IA précis et personnalisés.`,
	AiSmartPromptPrimaryText: 'Invite intelligent',
	AiSmartPromptSecondaryText: `Insérer une invite intelligente personnalisée d'IA`,
	AiSmartReminders: `Rappels intelligents de l'IA`,
	AiTemplateBannerTitle: `Simplifiez votre travail avec des modèles optimisés par l'IA`,
	AiTemplates: `Modèles d'IA`,
	AiTokens: `Jetons d'IA`,
	AiWorkBetterWithAi: `Travailler mieux avec l'IA`,
	All: 'Tous',
	AllAppointments: 'Tous les rendez-vous',
	AllCategories: 'Toutes catégories',
	AllClients: 'Tous les clients',
	AllContactPolicySelectorLabel: 'Tous les contacts de <mark>{client}</mark>',
	AllContacts: 'Tous les contacts',
	AllContactsOf: 'Tous les contacts de ‘{name}’',
	AllDay: 'Toute la journée',
	AllInboxes: 'Toutes les boîtes de réception',
	AllIndustries: 'Tous les secteurs',
	AllLocations: 'Tous les emplacements',
	AllMeetings: 'Toutes les réunions',
	AllNotificationsRestoredMessage: 'Toutes les notifications restaurées',
	AllProfessions: 'Tous les métiers',
	AllReminders: 'Tous les rappels',
	AllServices: 'Toutes les prestations',
	AllStatuses: 'Tous les statuts',
	AllTags: 'Toutes les balises',
	AllTasks: 'Toutes les tâches',
	AllTeamMembers: `Tous les membres de l'équipe`,
	AllTypes: 'Tous les types',
	Allocated: 'Attribué',
	AllocatedItems: 'Articles attribués',
	AllocationTableEmptyState: 'Aucune allocation de paiement trouvée',
	AllocationTotalWarningMessage: `Le montant alloué dépasse le montant total du paiement.
 Veuillez consulter les éléments ci-dessous.`,
	AllowClientsToCancelAnytime: `Permettre aux clients d'annuler à tout moment`,
	AllowNewClient: 'Autoriser les nouveaux clients',
	AllowNewClientHelper: 'Les nouveaux clients peuvent réserver ce service',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Autoriser au moins {hours} heures avant le rendez-vous',
	AllowToUseSavedCard: `Autoriser {provider} à utiliser la carte enregistrée à l'avenir`,
	AllowVideoCalls: 'Autoriser les appels vidéo',
	AlreadyAdded: 'Déjà ajouté',
	AlreadyHasAccess: 'A accès',
	AlreadyHasAccount: 'Vous avez déjà un compte?',
	Always: 'Toujours',
	AlwaysIgnore: 'Ignorer toujours',
	Amount: 'Montant',
	AmountDue: 'Montant dû',
	AmountOfReferralRequests:
		'{amount, plural, one {# demande de recommandation} other {# demandes de recommandation}}',
	AmountPaid: 'Montant payé',
	AnalyzingAudio: 'Analyse audio...',
	AnalyzingInputContent: 'Analyse du contenu des entrées...',
	AnalyzingRequest: 'Analyse de la demande...',
	AnalyzingTemplateContent: 'Analyse du contenu du modèle...',
	And: 'et',
	Annually: 'Annuellement',
	Anonymous: 'Anonyme',
	AnswerExceeded: 'Votre réponse doit comporter moins de 300 caractères.',
	AnyStatus: `N'importe quel statut`,
	AppNotifications: 'Notifications',
	AppNotificationsClearanceHeading: 'Bon travail ! Vous avez terminé toutes les activités',
	AppNotificationsEmptyHeading: `Votre activité sur l'espace de travail apparaîtra ici sous peu`,
	AppNotificationsEmptySubtext: `Il n'y a aucune mesure à prendre pour l'instant`,
	AppNotificationsIgnoredCount: '{total} ignorés',
	AppNotificationsUnread: '{total} non lus',
	Append: 'Ajouter',
	Apply: 'Appliquer',
	ApplyAccountCredit: 'Appliquer le crédit du compte',
	ApplyDiscount: 'Appliquer la réduction',
	ApplyVisualEffects: 'Appliquer des effets visuels',
	ApplyVisualEffectsNotSupported: 'Appliquer des effets visuels non pris en charge',
	Appointment: 'Rendez-vous',
	AppointmentAssignedNotificationSubject: '{actorProfileName} vous a attribué {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} a annulé {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} a confirmé {appointmentName}',
	AppointmentDetails: 'Détails du rendez-vous',
	AppointmentLocation: 'Lieu du rendez-vous',
	AppointmentLocationDescription:
		'Gérez vos emplacements virtuels et physiques par défaut. Lorsque vous planifiez un rendez-vous, ces emplacements seront automatiquement appliqués.',
	AppointmentNotFound: 'Rendez-vous introuvable',
	AppointmentReminder: 'Rappel de rendez-vous',
	AppointmentReminders: 'Rappels de rendez-vous',
	AppointmentRemindersInfo: `Définissez des rappels automatisés pour les rendez-vous des clients afin d'éviter les non-présentations et les annulations`,
	AppointmentRescheduledNotificationSubject: '{actorProfileName} a reporté {appointmentName}',
	AppointmentSaved: 'Rendez-vous enregistré',
	AppointmentStatus: 'Statut du rendez-vous',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0min} other {{minutes}min}} } one {{hours}h {minutes, plural, =0 {} other {{minutes}min}}} other {{hours}h {minutes, plural, =0 {} other {{minutes}min}}} }',
	AppointmentUndone: 'Rendez-vous annulé',
	Appointments: 'Rendez-vous',
	Archive: 'Archive',
	ArchiveClients: `Clients d'archives`,
	Archived: 'Archivé',
	AreYouAClient: 'Êtes-vous client?',
	AreYouStillThere: 'Vous êtes toujours là ?',
	AreYouSure: 'Es-tu sûr?',
	Arrangements: 'Dispositions',
	ArtTherapist: 'Art-thérapeute',
	Articles: 'Articles',
	Artist: 'Artiste',
	AskAI: `Demandez à l'IA`,
	AskAiAddFormField: 'Ajouter un champ de formulaire',
	AskAiChangeFormality: 'Change formality',
	AskAiChangeToneToBeMoreProfessional: 'Changer de ton pour être plus professionnel',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Expliquez de quoi parle ce document',
	AskAiExplainWhatThisImageIsAbout: 'Expliquez de quoi parle cette image',
	AskAiFixSpellingAndGrammar: `Corriger l'orthographe et la grammaire`,
	AskAiGenerateACaptionForThisImage: 'Générer une légende pour cette image',
	AskAiGenerateFromThisPage: 'Generate from this page',
	AskAiGetStarted: 'Get started',
	AskAiGiveItAFriendlyTone: 'Give it a friendly tone',
	AskAiGreeting: `Bonjour {firstName} ! Comment puis-je vous aider aujourd'hui ?`,
	AskAiHowCanIHelpWithYourContent: 'Comment puis-je vous aider avec votre contenu ?',
	AskAiInsert: 'Insérer',
	AskAiMakeItMoreCasual: 'Make it more casual',
	AskAiMakeThisTextMoreConcise: 'Rendre ce texte plus concis',
	AskAiMoreProfessional: 'More professional',
	AskAiOpenPreviousNote: 'Ouvrir la note précédente',
	AskAiPondering: 'Réflexion',
	AskAiReplace: 'Remplacer',
	AskAiReviewOrEditSelection: 'Review or edit selection',
	AskAiRuminating: 'Ruminer',
	AskAiSeeMore: 'Voir plus',
	AskAiSimplifyLanguage: 'Simplify language',
	AskAiSomethingWentWrong: `Quelque chose s'est mal passé. Si le problème persiste, veuillez nous contacter via notre centre d'aide.`,
	AskAiStartWithATemplate: 'Commencer avec un modèle',
	AskAiSuccessfullyCopiedResponse: `Réponse de l'IA copiée avec succès`,
	AskAiSuccessfullyInsertedResponse: `Réponse de l'IA insérée avec succès`,
	AskAiSuccessfullyReplacedResponse: `Réponse de l'IA remplacée avec succès`,
	AskAiSuggested: 'Suggéré',
	AskAiSummariseTextIntoBulletPoints: 'Résumer le texte en puces',
	AskAiSummarizeNote: 'Summarize note',
	AskAiThinking: 'Pensée',
	AskAiToday: `Aujourd'hui {time}`,
	AskAiWhatDoYouWantToDoWithThisForm: 'Que voulez-vous faire avec ce formulaire ?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Rédigez une note professionnelle en utilisant le modèle',
	AskAskAiAnything: `Demandez n'importe quoi à l'IA`,
	AskWriteSearchAnything: `Demandez, écrivez « @ » ou recherchez n'importe quoi...`,
	Asking: 'Demander',
	Assessment: 'Évaluation',
	Assessments: 'Évaluations',
	AssessmentsCategoryDescription: `Pour l'enregistrement des évaluations des clients`,
	AssignClients: 'Attribuer des clients',
	AssignNewClients: 'Attribuer des clients',
	AssignServices: 'Attribuer des services',
	AssignTeam: 'Attribuer une équipe',
	AssignTeamMember: 'Ajouter un nouveau membre',
	Assigned: 'Attribué',
	AssignedClients: 'Clients assignés',
	AssignedServices: 'Services assignés',
	AssignedServicesDescription:
		'Affichez et gérez les services qui vous sont attribués, en ajustant les prix pour refléter vos tarifs personnalisés. ',
	AssignedTeam: 'Équipe assignée',
	AthleticTrainer: `Entraîneur d'athlétisme`,
	AttachFiles: 'Joindre des fichiers',
	AttachLogo: 'Attacher',
	Attachment: 'Pièce jointe',
	AttachmentBlockedFileType: 'Bloqué pour des raisons de sécurité !',
	AttachmentTooLargeFileSize: 'Fichier trop large',
	AttachmentUploadItemComplete: 'Complet',
	AttachmentUploadItemError: 'Échec du téléchargement',
	AttachmentUploadItemLoading: 'Chargement',
	AttemptingToReconnect: 'Tentative de reconnexion...',
	Attended: 'Participé',
	AttendeeBeingMutedTooltip: `L'hôte vous a mis en sourdine. Utilisez « lever la main » pour demander la réactivation du son`,
	AttendeeWithId: 'Participant {attendeeId}',
	Attendees: 'Participants',
	AttendeesCount: '{count} participants',
	Attending: 'Participer',
	Audiologist: 'Audiologiste',
	Aunt: 'Tante',
	Australia: 'Australie',
	AuthenticationCode: `Code d'authentification`,
	AuthoriseProvider: 'Autoriser {provider}',
	AuthorisedProviders: 'Fournisseurs agréés',
	AutoDeclineAllFutureOption: 'Seulement les nouveaux événements ou rendez-vous',
	AutoDeclineAllOption: 'Événements et rendez-vous nouveaux et existants',
	AutoDeclinePrimaryText: 'Refuser automatiquement les événements',
	AutoDeclineSecondaryText: `Les événements pendant votre période d'absence seront automatiquement refusés.`,
	AutogenerateBillings: 'Générer automatiquement des documents de facturation',
	AutogenerateBillingsDescription:
		'Les documents de facturation automatisés seront générés le dernier jour du mois. Les factures et les reçus Superbill peuvent être créés manuellement à tout moment.',
	AutomateWorkflows: 'Automatisez les flux de travail',
	AutomaticallySendSuperbill: 'Envoyez automatiquement des reçus Superbill',
	AutomaticallySendSuperbillHelperText: `Une superbe facture est un reçu détaillé des services fournis à un client pour le remboursement de l'assurance`,
	Automation: 'Automation',
	AutomationActionSendEmailLabel: 'Envoyer un e-mail',
	AutomationActionSendSMSLabel: 'Envoyer un SMS',
	AutomationAndReminders: 'Automation ',
	AutomationDeletedSuccessMessage: 'Automatisation supprimée avec succès',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Événement temporel',
	AutomationParams_timeUnit: 'Unité',
	AutomationParams_timeValue: 'Nombre',
	AutomationPublishSuccessMessage: 'Automatisation publiée avec succès',
	AutomationPublishWarningTooltip: `Veuillez revérifier la configuration de l'automatisation et vous assurer qu'elle a été correctement configurée`,
	AutomationTriggerEventCancelledDescription: `Déclencheurs lorsqu'un événement est annulé ou supprimé`,
	AutomationTriggerEventCancelledLabel: 'Événement annulé',
	AutomationTriggerEventCreatedDescription: `Déclencheurs lorsqu'un événement est créé`,
	AutomationTriggerEventCreatedLabel: 'Nouvel événement',
	AutomationTriggerEventCreatedOrUpdatedDescription: `Se déclenche lorsqu'un événement est créé ou mis à jour (sauf lorsqu'il est annulé)`,
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Événement nouveau ou mis à jour',
	AutomationTriggerEventEndedDescription: `Déclencheurs lorsqu'un événement se termine`,
	AutomationTriggerEventEndedLabel: 'Événement terminé',
	AutomationTriggerEventStartsDescription: `Se déclenche lorsqu'un laps de temps spécifié avant le début d'un événement`,
	AutomationTriggerEventStartsLabel: `L'événement commence`,
	Automations: 'Automatismes',
	Availability: 'Disponibilité',
	AvailabilityDisableSchedule: 'Désactiver le calendrier',
	AvailabilityDisabled: 'Désactivé',
	AvailabilityEnableSchedule: 'Activer le planning',
	AvailabilityEnabled: 'Activé',
	AvailabilityNoActiveBanner:
		'Vous avez désactivé tous vos plannings. Les clients ne peuvent pas vous réserver en ligne et tous les futurs rendez-vous doivent être confirmés manuellement.',
	AvailabilityNoActiveConfirmationDescription:
		'La désactivation de cette disponibilité entraînera l’absence de plannings actifs. Les clients ne pourront pas vous réserver en ligne et toute réservation effectuée par les praticiens se fera en dehors de vos heures de travail.',
	AvailabilityNoActiveConfirmationProceed: 'Oui, continuez',
	AvailabilityNoActiveConfirmationTitle: 'Aucun horaire actif',
	AvailabilityToggle: 'Calendrier activé',
	AvailabilityUnsetDate: 'Aucune date fixée',
	AvailableLocations: 'Emplacements disponibles',
	AvailablePayers: 'Payeurs disponibles',
	AvailablePayersEmptyState: 'Aucun payeur sélectionné',
	AvailableTimes: 'Heures disponibles',
	Back: 'Dos',
	BackHome: 'Retour à la maison',
	BackToAppointment: 'Retour au rendez-vous',
	BackToLogin: 'Retour à la connexion',
	BackToMapColumns: 'Retour aux colonnes de la carte',
	BackToTemplates: 'Retour aux Modèles',
	BackToUploadFile: 'Retour à Télécharger le fichier',
	Banker: 'Banquier',
	BasicBlocks: 'Blocs de base',
	BeforeAppointment: 'Envoyer un rappel {deliveryType} {interval} {unit} avant le rendez-vous',
	BehavioralAnalyst: 'Analyste comportemental',
	BehavioralHealthTherapy: 'Thérapie de santé comportementale',
	Beta: 'Bêta',
	BillTo: 'Facturer à',
	BillableItems: 'Éléments facturables',
	BillableItemsEmptyState: `Aucun élément facturable n'a été trouvé`,
	Biller: 'Facturateur',
	Billing: 'Facturation',
	BillingAddress: 'Adresse de facturation',
	BillingAndReceiptsUnauthorisedMessage:
		'Un accès à la vue des factures et des paiements est requis pour accéder à ces informations.',
	BillingBillablesTab: 'Facturables',
	BillingClaimsTab: 'Réclamations',
	BillingDetails: 'Détails de facturation',
	BillingDocuments: 'Documents de facturation',
	BillingDocumentsClaimsTab: 'Réclamations',
	BillingDocumentsEmptyState: 'Aucun {tabType} trouvé',
	BillingDocumentsInvoicesTab: 'Factures',
	BillingDocumentsSuperbillsTab: 'Superfactures',
	BillingInformation: 'Informations de facturation',
	BillingInvoicesTab: 'Factures',
	BillingItems: 'Éléments de facturation',
	BillingPaymentsTab: 'Paiements',
	BillingPeriod: 'Période de facturation',
	BillingProfile: 'Profil de facturation',
	BillingProfileOverridesDescription: `Limiter l'utilisation de ce profil de facturation à des membres d'équipe spécifiques`,
	BillingProfileOverridesHeader: `Limiter l'accès`,
	BillingProfileProviderType: 'Type de fournisseur',
	BillingProfileTypeIndividual: 'Individuel',
	BillingProfileTypeIndividualSubLabel: 'Type 1 NPI',
	BillingProfileTypeOrganisation: 'Organisation',
	BillingProfileTypeOrganisationSubLabel: 'Type 2 NPI',
	BillingProfiles: 'Profils de facturation',
	BillingProfilesEditHeader: 'Modifier le profil de facturation de {name}',
	BillingProfilesNewHeader: 'Nouveau profil de facturation',
	BillingProfilesSectionDescription:
		'Gérez vos informations de facturation pour les praticiens et les payeurs d’assurance en configurant des profils de facturation qui peuvent être appliqués aux factures et aux paiements d’assurance.',
	BillingSearchPlaceholder: 'Rechercher des articles',
	BillingSettings: 'Paramètres de facturation',
	BillingSuperbillsTab: 'Superbills',
	BiomedicalEngineer: 'Ingénieur biomédical',
	BlankInvoice: 'Facture vierge',
	BlueShieldProviderNumber: 'Numéro de fournisseur Blue Shield',
	Body: 'Corps',
	Bold: 'Audacieux',
	BookAgain: 'Réservez à nouveau',
	BookAppointment: 'Prendre rendez-vous',
	BookableOnline: 'Réservable en ligne',
	BookableOnlineHelper: 'Les clients peuvent réserver ce service en ligne',
	BookedOnline: 'Réservé en ligne',
	Booking: 'Réservation',
	BookingAnalyticsIntegrationPanelDescription: `Configurez Google Tag Manager pour suivre les actions et conversions clés de votre processus de réservation en ligne. Collectez des données précieuses sur les interactions des utilisateurs pour améliorer vos efforts marketing et optimiser l'expérience de réservation.`,
	BookingAnalyticsIntegrationPanelTitle: `Intégration d'analyses`,
	BookingAndCancellationPolicies: 'Réservation ',
	BookingButtonEmbed: 'Bouton',
	BookingButtonEmbedDescription: 'Ajoute un bouton de réservation en ligne à votre site Web',
	BookingDirectTextLink: 'Lien texte direct',
	BookingDirectTextLinkDescription: 'Ouvre la page de réservation en ligne',
	BookingFormatLink: 'Lien de mise en forme',
	BookingFormatLinkButtonTitle: 'Titre du bouton',
	BookingInlineEmbed: 'Intégration en ligne',
	BookingInlineEmbedDescription: 'Charge la page de réservation en ligne directement sur votre site Web',
	BookingLink: 'Lien de réservation',
	BookingLinkModalCopyText: 'Copie',
	BookingLinkModalDescription: `Autoriser les clients disposant de ce lien à réserver n'importe quel membre de l'équipe ou service`,
	BookingLinkModalHelpText: 'Apprenez à configurer les réservations en ligne',
	BookingLinkModalTitle: 'Partagez votre lien de réservation',
	BookingPolicies: 'Politiques de réservation',
	BookingPoliciesDescription: 'Définir quand les réservations en ligne peuvent être effectuées par les clients',
	BookingTimeUnitDays: 'jours',
	BookingTimeUnitHours: 'heures',
	BookingTimeUnitMinutes: 'minutes',
	BookingTimeUnitMonths: 'mois',
	BookingTimeUnitWeeks: 'semaines',
	BottomNavBilling: 'Facturation',
	BottomNavGettingStarted: 'Maison',
	BottomNavMore: 'Plus',
	BottomNavNotes: 'Remarques',
	Brands: 'Marques',
	Brother: 'Frère',
	BrotherInLaw: 'Beau-frère',
	BrowseOrDragFileHere: '<link>Parcourir</link> ou déposer un fichier ici',
	BrowseOrDragFileHereDescription: 'PNG, JPG (max. {limit})',
	BufferAfterTime: '{time} min après',
	BufferAndLabel: 'et',
	BufferAppointmentLabel: 'un rendez-vous',
	BufferBeforeTime: '{time} min avant',
	BufferTime: 'Temps tampon',
	BufferTimeViewLabel: '{bufferBefore} mins avant et {bufferAfter} mins après les rendez-vous',
	BulkArchiveClientsDescription:
		'Etes-vous sûr de vouloir archiver ces clients ? Vous pourrez les réactiver plus tard.',
	BulkArchiveSuccess: 'Clients archivés avec succès',
	BulkArchiveUndone: 'Archivage en masse annulé',
	BulkPermanentDeleteDescription: 'Ceci supprimera **{count} conversations**. Cette action est irréversible.',
	BulkPermanentDeleteTitle: 'Supprimer les conversations pour toujours',
	BulkUnarchiveSuccess: 'Clients désarchivés avec succès',
	BulletedList: 'Liste à puces',
	BusinessAddress: `Adresse de l'entreprise`,
	BusinessAddressOptional: `Adresse de l'entreprise<span>(Optionnel)</span>`,
	BusinessName: `Nom de l'entreprise`,
	Button: 'Bouton',
	By: 'Par',
	CHAMPUSIdentificationNumber: `Numéro d'identification CHAMPUS`,
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'Le CVC est requis',
	Calendar: 'Calendrier',
	CalendarAppSyncFormDescription: 'Synchronisez les événements Carepatron avec',
	CalendarAppSyncPanelTitle: 'Synchronisation des applications connectées',
	CalendarDescription: 'Gérez vos rendez-vous ou définissez des tâches personnelles et des rappels',
	CalendarDetails: 'Détails du calendrier',
	CalendarDetailsDescription: `Gérez vos paramètres d'affichage de calendrier et de rendez-vous.`,
	CalendarScheduleNew: 'Programmer un nouveau',
	CalendarSettings: 'Paramètres du calendrier',
	Call: 'Appel',
	CallAttendeeJoinAttemptedNotificationSubject: `<strong>{attendeeName}</strong> a rejoint l'appel vidéo`,
	CallChangeLayoutTextContent: 'La sélection est enregistrée pour les réunions futures',
	CallIdlePrompt: 'Préféreriez-vous attendre pour vous inscrire ou réessayer plus tard ?',
	CallLayoutOptionAuto: 'Auto',
	CallLayoutOptionSidebar: 'Barre latérale',
	CallLayoutOptionSpotlight: 'Pleins feux',
	CallLayoutOptionTiled: 'Mosaïque',
	CallNoAttendees: 'Aucun participant à la réunion.',
	CallSessionExpiredError: `La session a expiré. L'appel est terminé. Veuillez réessayer de vous inscrire.`,
	CallWithPractitioner: 'Appel avec {practitioner}',
	CallsListCreateButton: 'Nouvel appel',
	CallsListEmptyState: 'Aucun appel actif',
	CallsListItemEndCall: `Fin d'appel`,
	CamWarningMessage: 'Un problème a été détecté avec votre caméra',
	Camera: 'Caméra',
	CameraAndMicIssueModalDescription: `Veuillez autoriser Carepatron à accéder à votre caméra et à votre microphone.
 Pour plus d'informations, <a>suivez ce guide</a>`,
	CameraAndMicIssueModalTitle: 'La caméra et le microphone sont bloqués',
	CameraQuality: 'Qualité de la caméra',
	CameraSource: 'Source de la caméra',
	CanModifyReadOnlyEvent: 'Vous ne pouvez pas modifier cet événement',
	Canada: 'Canada',
	Cancel: 'Annuler',
	CancelClientImportDescription: 'Êtes-vous sûr de vouloir annuler cette importation ?',
	CancelClientImportPrimaryAction: `Oui, annuler l'importation`,
	CancelClientImportSecondaryAction: `Continue d'éditer`,
	CancelClientImportTitle: `Annuler l'importation des clients`,
	CancelImportButton: `Annuler l'importation`,
	CancelPlan: 'Annuler le forfait',
	CancelPlanConfirmation: `L'annulation du forfait débitera automatiquement votre compte de tous les soldes impayés que vous avez pour ce mois.
 Si vous souhaitez rétrograder vos utilisateurs facturés, vous pouvez simplement supprimer des membres de l'équipe et Carepatron mettra automatiquement à jour le prix de votre abonnement.`,
	CancelSend: `Annuler l'envoi`,
	CancelSubscription: `Annuler l'abonnement`,
	Canceled: 'Annulé',
	CancellationPolicy: `Politique d'annulation`,
	Cancelled: 'Annulé',
	CannotContainSpecialCharactersError: 'Ne peut pas contenir {specialCharacters}',
	CannotDeleteInvoice: 'Les factures payées via les paiements en ligne ne peuvent pas être supprimées',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> ne peut pas être déplacé hors du groupe <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Le service ne peut pas être déplacé en dehors des collections',
	CapeTown: 'Le Cap',
	Caption: 'Légende',
	CaptureNameFieldLabel: 'Le nom que vous aimeriez que les autres vous désignent',
	CapturePaymentMethod: 'Capturer le mode de paiement',
	CapturingAudio: 'Capture audio',
	CapturingSignature: 'Capturer la signature...',
	CardInformation: 'Informations sur la carte',
	CardNumberRequired: 'Le numéro de carte est requis',
	CardiacRehabilitationSpecialist: 'Spécialiste en réadaptation cardiaque',
	Cardiologist: 'Cardiologue',
	CareAiNoConversations: 'Aucune conversation pour le moment',
	CareAiNoConversationsDescription: 'Commencez une conversation avec {aiName} pour commencer',
	CareAssistant: 'Aide-soignant',
	CareManager: 'Responsable des soins',
	Caregiver: 'Soignant',
	CaregiverCreateModalDescription: `L'ajout de personnel en tant qu'administrateurs leur permettra de créer et de gérer des histoires de soins. Cela leur donne également un accès complet pour créer et gérer des clients.`,
	CaregiverCreateModalTitle: `Nouveau membre de l'équipe`,
	CaregiverListCantAddStaffInfoTitle: `Vous avez atteint le nombre maximum d'employés pour votre abonnement. Veuillez mettre à niveau votre plan pour ajouter plus de membres du personnel.`,
	CaregiverListCreateButton: `Nouveau membre de l'équipe`,
	CaregiverListEmptyState: 'Aucun soignant ajouté',
	CaregiversListItemRemoveStaff: 'Supprimer le personnel',
	CarepatronApp: 'Application Carepatron',
	CarepatronCommunity: 'Communauté',
	CarepatronFieldAddress: 'Adresse',
	CarepatronFieldAssignedStaff: 'Personnel affecté',
	CarepatronFieldBirthDate: 'Date de naissance',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: `Statut d'emploi`,
	CarepatronFieldEthnicity: 'Origine ethnique',
	CarepatronFieldFirstName: 'Prénom',
	CarepatronFieldGender: 'Genre',
	CarepatronFieldIdentificationNumber: `Numéro d'identification`,
	CarepatronFieldIsArchived: 'Statut',
	CarepatronFieldLabel: 'Étiquette',
	CarepatronFieldLastName: 'Nom de famille',
	CarepatronFieldLivingArrangements: 'Modes de vie',
	CarepatronFieldMiddleNames: 'Deuxième prénom',
	CarepatronFieldOccupation: 'Profession',
	CarepatronFieldPhoneNumber: 'Numéro de téléphone',
	CarepatronFieldRelationshipStatus: 'Statut de la relation',
	CarepatronFieldStatus: 'Statut',
	CarepatronFieldStatusHelperText: '10 statuts maximum.',
	CarepatronFieldTags: 'Mots clés',
	CarepatronFields: 'Champs Carepatron',
	Cash: 'Espèces',
	Category: 'Catégorie',
	CategoryInputPlaceholder: 'Choisissez une catégorie de modèle',
	CenterAlign: 'Aligner au centre',
	Central: 'Central',
	ChangeLayout: 'Changer la disposition',
	ChangeLogo: 'Changement',
	ChangePassword: 'Changer le mot de passe',
	ChangePasswordFailureSnackbar: `Désolé, votre mot de passe n'a pas été modifié. Vérifiez que votre ancien mot de passe est correct.`,
	ChangePasswordHelperInfo: 'Longueur minimale de {minLength}',
	ChangePasswordSuccessfulSnackbar: `Mot de passe modifié avec succès ! La prochaine fois que vous vous connecterez, assurez-vous d'utiliser ce mot de passe.`,
	ChangeSubscription: `Changer d'abonnement`,
	ChangesNotAllowed: 'Aucune modification ne peut être apportée à ce champ',
	ChargesDisabled: 'Frais désactivés',
	ChargesEnabled: 'Frais activés',
	ChargesStatus: 'État des frais',
	ChartAndDiagram: 'Graphique/Diagramme',
	ChartsAndDiagramsCategoryDescription: 'Pour illustrer les données et les progrès des clients',
	ChatEditMessage: 'Modifier le message',
	ChatReplyTo: 'Répondre à {name}',
	ChatTypeMessageTo: 'Message {name}',
	Check: 'Vérifier',
	CheckList: 'Liste de contrôle',
	Chef: 'Chef',
	Chiropractic: 'Chiropratique',
	Chiropractor: 'Chiropracteur',
	Chiropractors: 'Chiropracteurs',
	ChooseACollection: 'Choisissez une collection',
	ChooseAContact: 'Choisissez un contact',
	ChooseAccountTypeHeader: 'Qu’est-ce qui vous décrit le mieux ?',
	ChooseAction: `Choisissez l'action`,
	ChooseAnAccount: 'Choisissez un compte',
	ChooseAnOption: 'Choisissez une option',
	ChooseBillingProfile: 'Choisissez le profil de facturation',
	ChooseClaim: 'Choisissez une réclamation',
	ChooseCollection: 'Choisir une collection',
	ChooseColor: 'Choisir la couleur',
	ChooseCustomDate: 'Choisissez une date personnalisée',
	ChooseDateAndTime: 'Choisir une date et un temps',
	ChooseDxCodes: 'Choisissez les codes de diagnostic',
	ChooseEventType: `Choisissez le type d'événement`,
	ChooseFileButton: 'Choisissez un fichier',
	ChooseFolder: 'Choisir un dossier',
	ChooseInbox: 'Choisissez la boîte de réception',
	ChooseMethod: 'Choisissez la méthode',
	ChooseNewOwner: 'Choisir un nouveau propriétaire',
	ChooseOrganization: 'Choisir une organisation',
	ChoosePassword: 'Choisissez un mot de passe',
	ChoosePayer: 'Choisissez le payeur',
	ChoosePaymentMethod: 'Choisissez une méthode de paiement',
	ChoosePhysicalOrRemoteLocations: 'Entrez ou choisissez un lieu',
	ChoosePlan: 'Choisissez {plan}',
	ChooseProfessional: 'Choisissez Professionnel',
	ChooseServices: 'Choisissez des prestations',
	ChooseSource: 'Choisissez la source',
	ChooseSourceDescription: `Choisissez d'où vous importez vos clients – qu'il s'agisse d'un fichier ou d'une autre plateforme logicielle.`,
	ChooseTags: 'Choisissez des balises',
	ChooseTaxName: 'Choisissez le nom de la taxe',
	ChooseTeamMembers: `Choisir des membres d'équipe`,
	ChooseTheme: 'Choisissez un thème',
	ChooseTrigger: 'Choisissez le déclencheur',
	ChooseYourProvider: 'Choisissez votre fournisseur',
	CircularProgressWithLabel: '{value}%',
	City: 'Ville',
	CivilEngineer: 'Ingénieur civil',
	Claim: 'Réclamer',
	ClaimAddReferringProvider: 'Ajouter un fournisseur référent',
	ClaimAddRenderingProvider: 'Ajouter un fournisseur de rendu',
	ClaimAmount: 'Montant de la demande',
	ClaimAmountPaidHelpContent: `Le montant payé correspond au paiement reçu du patient ou d'autres payeurs. Saisissez le montant total payé par le patient et/ou les autres payeurs pour les services couverts uniquement.`,
	ClaimAmountPaidHelpSubtitle: 'Champ 29',
	ClaimAmountPaidHelpTitle: 'Montant payé',
	ClaimBillingProfileTypeIndividual: 'Individuel',
	ClaimBillingProfileTypeOrganisation: 'Organisation',
	ClaimChooseRenderingProviderOrTeamMember: `Choisissez un fournisseur de rendu ou un membre de l'équipe`,
	ClaimClientInsurancePolicies: 'Polices d’assurance client',
	ClaimCreatedAction: '<mark>Réclamation {claimNumber}</mark> créée',
	ClaimDeniedAction: '<mark>Demande {claimNumber}</mark> a été refusée par <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Rechercher les codes de diagnostic de la CIM 10',
	ClaimDiagnosisSelectorHelpContent: `Le « diagnostic ou la blessure » est le signe, le symptôme, la plainte ou l’état du patient relatif au(x) service(s) faisant l’objet de la réclamation.
 Jusqu'à 12 codes de diagnostic ICD 10 peuvent être sélectionnés.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Champ 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnostic ou blessure',
	ClaimDiagnosticCodesEmptyError: 'Au moins un code de diagnostic est requis',
	ClaimDoIncludeReferrerInformation: 'Inclure les informations de référence sur CMS1500',
	ClaimERAReceivedAction: 'Reçu de remise électronique de <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Virement électronique reçu	<mark>Paiement {paymentReference}</mark> pour <b>{paymentAmount}</b> par <b>{payerNumber} {payerName}</b> a été enregistré',
	ClaimExportedAction: '<mark>Demande {claimNumber}</mark> a été exportée en tant que <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nom du client ou du contact',
	ClaimFieldClientAddress: 'Adresse du client',
	ClaimFieldClientAddressDescription: `Entrez l'adresse du client. La première ligne est réservée à l'adresse postale. N'utilisez pas de ponctuation (virgules ou points) ni de symboles dans l'adresse. Si vous déclarez une adresse à l'étranger, contactez le payeur pour obtenir des instructions de déclaration spécifiques.`,
	ClaimFieldClientAddressSubtitle: 'Champ 5',
	ClaimFieldClientDateOfBirth: 'Date de naissance du client',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Champ 3',
	ClaimFieldClientDateOfBirthDescription: `Saisissez la date de naissance à 8 chiffres du client (JJ/MM/AAAA). La date de naissance du client est une information qui permet d'identifier le client et de distinguer les personnes portant des noms similaires.`,
	ClaimFieldClientDescription:
		'Le « nom du client » est le nom de la personne qui a reçu le traitement ou les fournitures.',
	ClaimFieldClientSexDescription:
		'Le « sexe » est une information qui permet d’identifier le client et de distinguer les personnes portant des noms similaires.',
	ClaimFieldClientSubtitle: 'Champ 2',
	ClaimFiling: 'Dépôt de réclamation',
	ClaimHistorySubtitle: 'Assurance • Réclamation {number}',
	ClaimIncidentAutoAccident: 'Accident de voiture ?',
	ClaimIncidentConditionRelatedTo: `L'état du client est-il lié à`,
	ClaimIncidentConditionRelatedToHelpContent: `Ces informations indiquent si la maladie ou la blessure du client est liée à l'emploi, à un accident de voiture ou à un autre accident. L'emploi (actuel ou antérieur) indiquerait que la condition est liée au travail ou au lieu de travail du client. Accident de voiture indiquerait que la condition est le résultat d'un accident de voiture. Autre accident indiquerait que la condition est le résultat de tout autre type d'accident.`,
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Terrains 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: `L'état du client est-il lié à`,
	ClaimIncidentCurrentIllness: 'Maladie, blessure ou grossesse actuelle',
	ClaimIncidentCurrentIllnessHelpContent:
		'La date de la maladie, de la blessure ou de la grossesse actuelle identifie la première date d’apparition de la maladie, la date réelle de la blessure ou la date des dernières règles pour la grossesse.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Champ 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Dates de la maladie, de la blessure ou de la grossesse en cours (LMP)',
	ClaimIncidentDate: 'Date',
	ClaimIncidentDateFrom: 'Dater de',
	ClaimIncidentDateTo: 'Date à',
	ClaimIncidentEmploymentRelated: 'Emploi',
	ClaimIncidentEmploymentRelatedDesc: '(Actuel ou précédent)',
	ClaimIncidentHospitalizationDatesLabel: `Dates d'hospitalisation liées aux services actuels`,
	ClaimIncidentHospitalizationDatesLabelHelpContent: `Les dates d'hospitalisation liées aux services actuels font référence au séjour d'un client et indiquent les dates d'admission et de sortie associées au(x) service(s) de la réclamation.`,
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Champ 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: `Dates d'hospitalisation liées aux services actuels`,
	ClaimIncidentInformation: `Informations sur l'incident`,
	ClaimIncidentOtherAccident: 'Autre accident ?',
	ClaimIncidentOtherAssociatedDate: 'Autre date associée',
	ClaimIncidentOtherAssociatedDateHelpContent: `L'autre date identifie des informations de date supplémentaires sur l'état ou le traitement du client.`,
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Champ 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Autre date',
	ClaimIncidentQualifier: 'Qualificatif',
	ClaimIncidentQualifierPlaceholder: 'Choisissez le qualificatif',
	ClaimIncidentUnableToWorkDatesLabel: 'Le client n’était pas en mesure de travailler dans son emploi actuel',
	ClaimIncidentUnableToWorkDatesLabelHelpContent: `Les dates pendant lesquelles le client n'a pas pu travailler dans son emploi actuel correspondent à la période pendant laquelle le client est ou était incapable de travailler.`,
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Champ 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: `Dates auxquelles le client n'a pas pu travailler dans son emploi actuel`,
	ClaimIncludeReferrerInformation: 'Inclure les informations de référence sur CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Le type de couverture d'assurance maladie applicable à cette réclamation. Autre indique une assurance maladie, y compris les HMO, l'assurance commerciale, l'assurance accident automobile, la responsabilité civile ou l'indemnisation des accidents du travail.
 Ces informations orientent la réclamation vers le programme approprié et peuvent établir la responsabilité principale.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Champ 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Type de couverture',
	ClaimInsuranceGroupIdHelpContent: `Saisissez le numéro de police ou de groupe de l’assuré tel qu’il apparaît sur sa carte d’identité médicale.

 Le « numéro de police, de groupe ou FECA de l'assuré » est l'identifiant alphanumérique de la couverture d'assurance maladie, automobile ou autre. Le numéro FECA est l'identifiant alphanumérique à 9 caractères attribué à un patient qui déclare avoir une maladie liée au travail.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Champ 11',
	ClaimInsuranceGroupIdHelpTitle: `Numéro de police, de groupe ou FECA de l'assuré`,
	ClaimInsuranceMemberIdHelpContent: `Saisissez le numéro d'identification de l'assuré tel qu'il figure sur la carte d'identité de l'assuré auprès du payeur auquel la réclamation est soumise.
 Si le patient dispose d’un numéro d’identification de membre unique attribué par le payeur, saisissez ce numéro dans ce champ.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Champ 1a',
	ClaimInsuranceMemberIdHelpTitle: `Numéro de membre de l'assuré`,
	ClaimInsurancePayer: `Payeur d'assurance`,
	ClaimManualPaymentAction: '<mark>Paiement {paymentReference}</mark> pour <b>{paymentAmount}</b> enregistré',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Informations complémentaires sur la réclamation',
	ClaimMiscAdditionalClaimInformationHelpContent: `Veuillez consulter les instructions actuelles du payeur public ou privé concernant l'utilisation de ce champ. Indiquez le qualificateur approprié, lorsqu'il est disponible, pour l'information saisie.Ne saisissez pas d'espace, de tiret ou d'autre séparateur entre le qualificateur et l'information.`,
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Champ 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Informations complémentaires sur la réclamation',
	ClaimMiscClaimCodes: 'Codes de réclamation',
	ClaimMiscOriginalReferenceNumber: `Numéro de référence d'origine`,
	ClaimMiscPatientsAccountNumber: 'Numéro de compte du patient',
	ClaimMiscPatientsAccountNumberHelpContent: `Le numéro de compte du patient est l'identifiant attribué par le prestataire.`,
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Champ 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Numéro de compte du patient',
	ClaimMiscPriorAuthorizationNumber: `Numéro d'autorisation préalable`,
	ClaimMiscPriorAuthorizationNumberHelpContent: `Le numéro d'autorisation préalable est le numéro attribué au payeur autorisant le/les service(s).`,
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Champ 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: `Numéro d'autorisation préalable`,
	ClaimMiscResubmissionCode: 'Code de resoumission',
	ClaimMiscResubmissionCodeHelpContent:
		'La nouvelle soumission désigne le code et le numéro de référence original attribués par le payeur ou le destinataire de destination pour indiquer une réclamation ou une rencontre précédemment soumise.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Champ 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Nouvelle soumission et/ou numéro de référence original',
	ClaimNumber: 'Numéro de réclamation',
	ClaimNumberFormat: 'Réclamation #{number}',
	ClaimOrderingProvider: 'Fournisseur de commandes',
	ClaimOtherId: `Autre pièce d'identité`,
	ClaimOtherIdPlaceholder: 'Choisissez une option',
	ClaimOtherIdQualifier: `Autre qualificatif d'identification`,
	ClaimOtherIdQualifierPlaceholder: `Choisissez le qualificatif d'ID`,
	ClaimPlaceOfService: 'Lieu de service',
	ClaimPlaceOfServicePlaceholder: 'Ajouter un point de vente',
	ClaimPolicyHolderRelationship: `Relation avec l'assuré`,
	ClaimPolicyInformation: 'Informations sur la politique',
	ClaimPolicyTelephone: `Téléphone (inclure l'indicatif régional)`,
	ClaimReceivedAction: '<mark>Réclamation {claimNumber}</mark> reçue par <b>{name}</b>',
	ClaimReferringProvider: 'Fournisseur référent',
	ClaimReferringProviderEmpty: 'Aucun fournisseur référent/s ajouté',
	ClaimReferringProviderHelpContent: `Le nom saisi est celui du fournisseur référent, du fournisseur donneur d'ordre ou du fournisseur superviseur qui a référé, commandé ou supervisé le(s) service(s) ou la(les) fourniture(s) faisant l'objet de la réclamation. Le qualificatif indique le rôle du fournisseur faisant l'objet du signalement.`,
	ClaimReferringProviderHelpSubtitle: 'Champ 17',
	ClaimReferringProviderHelpTitle: 'Nom du fournisseur ou de la source référente',
	ClaimReferringProviderQualifier: 'Qualificatif',
	ClaimReferringProviderQualifierPlaceholder: 'Choisissez le qualificatif',
	ClaimRejectedAction: '<mark>Demande {claimNumber}</mark> a été refusée par <b>{name}</b>',
	ClaimRenderingProviderIdNumber: `Numéro d'identification`,
	ClaimRenderingProviderOrTeamMember: `Fournisseur de rendu ou membre de l'équipe`,
	ClaimRestoredAction: '<mark>Réclamation {claimNumber}</mark> a été restaurée',
	ClaimServiceFacility: 'Centre de service',
	ClaimServiceFacilityLocationHelpContent:
		'Le nom et l’adresse de l’établissement où les services ont été rendus identifient le site où les services ont été fournis.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a et 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Terrain 32, 32a et 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Centre de service',
	ClaimServiceFacilityPlaceholder: 'Choisissez un centre de service ou un emplacement',
	ClaimServiceLabChargesHelpContent: `Remplissez ce champ lorsque vous réclamez des services achetés fournis par une entité autre que le fournisseur de facturation.
 Chaque service acheté doit être déclaré sur une réclamation distincte, car un seul frais peut être inscrit sur le formulaire CMS1500.`,
	ClaimServiceLabChargesHelpSubtitle: 'Champ 20',
	ClaimServiceLabChargesHelpTitle: 'Frais de laboratoire externe',
	ClaimServiceLineServiceHelpContent:
		'Les « procédures, services ou fournitures » identifient les services et procédures médicaux fournis au patient.',
	ClaimServiceLineServiceHelpSubtitle: 'Champ 24d',
	ClaimServiceLineServiceHelpTitle: 'Procédures, services ou fournitures',
	ClaimServiceLinesEmptyError: 'Au moins une ligne de service est requise',
	ClaimServiceSupplementaryInfoHelpContent: `Ajoutez une description narrative supplémentaire des services fournis en utilisant les qualificatifs applicables.
 N'entrez pas d'espace, de trait d'union ou d'autre séparateur entre le qualificatif et l'information.

 Pour obtenir des instructions complètes sur l'ajout d'informations supplémentaires, consultez les instructions du formulaire de réclamation CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Champ 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Informations complémentaires',
	ClaimSettingsBillingMethodTitle: 'Méthode de facturation du client',
	ClaimSettingsClientSignatureDescription: `J'ai le consentement de divulguer les renseignements médicaux ou autres renseignements nécessaires au traitement des demandes d'assurance.`,
	ClaimSettingsClientSignatureTitle: 'Signature du client au dossier',
	ClaimSettingsConsentLabel: 'Consentement requis pour traiter les réclamations d’assurance :',
	ClaimSettingsDescription:
		'Choisissez le mode de facturation du client pour garantir un traitement fluide des paiements :',
	ClaimSettingsHasActivePoliciesAlertDescription: `{name} a une police d'assurance active. Pour activer la facturation de l'assurance, mettez à jour le mode de facturation du client à Assurance.`,
	ClaimSettingsInsuranceDescription: `Frais remboursés par l'assurance`,
	ClaimSettingsInsuranceTitle: 'Assurance',
	ClaimSettingsNoPoliciesAlertDescription:
		'Ajoutez une police d’assurance pour activer les réclamations d’assurance.',
	ClaimSettingsPolicyHolderSignatureDescription: `J'ai le consentement de recevoir des paiements d'assurance pour les services fournis.`,
	ClaimSettingsPolicyHolderSignatureTitle: `Signature du titulaire de la police d'assurance au dossier`,
	ClaimSettingsSelfPayDescription: 'Le client paiera pour les rendez-vous',
	ClaimSettingsSelfPayTitle: 'Auto-paiement',
	ClaimSettingsTitle: 'Paramètres de réclamation',
	ClaimSexSelectorPlaceholder: 'Homme / Femme',
	ClaimStatusChangedAction: '<mark>Réclamation {claimNumber}</mark> mise à jour',
	ClaimSubmittedAction:
		'<mark>Réclamation {claimNumber}</mark> soumise à <b>{payerClearingHouse}</b> pour <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Réclamation n° #{claimNumber}',
	ClaimSupervisingProvider: 'Fournisseur de supervision',
	ClaimSupplementaryInfo: 'Informations complémentaires',
	ClaimSupplementaryInfoPlaceholder: 'Ajouter des informations supplémentaires',
	ClaimTrashedAction: '<mark>Réclamation {claimNumber}</mark> a été supprimée',
	ClaimValidationFailure: 'Échec de la validation de la réclamation',
	ClaimsEmptyStateDescription: `Aucune réclamation n'a été trouvée.`,
	ClainInsuranceTelephone: `Numéro de téléphone de l'assurance (inclure l'indicatif régional)`,
	Classic: 'Classique',
	Clear: 'Clair',
	ClearAll: 'Effacer tout',
	ClearSearchFilter: 'Supprimer',
	ClearingHouse: 'Nettoyage de la maison',
	ClearingHouseClaimId: 'Identifiant Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Effacement de la référence de la maison',
	ClearingHouseUnavailableError: `La chambre de compensation n'est actuellement pas disponible. Veuillez réessayer plus tard.`,
	ClickToUpload: 'Cliquez pour télécharger',
	Client: 'Client',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} a ajouté {noteTitle, select, undefined { une note } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Choisissez vos clients et leurs relations',
	ClientAndRelationshipSelectorTitle: 'Tous les clients et leurs relations',
	ClientAndRelationshipSelectorTitle1: 'Toutes les relations de ‘{name}’',
	ClientAppCallsPageNoOptionsText: `Si vous attendez un appel vidéo, il apparaîtra ici sous peu. Si vous rencontrez des problèmes, veuillez contacter la personne qui l'a initié.`,
	ClientAppSubHeaderMyDocumentation: 'Ma documentation',
	ClientAppointment: 'Rendez-vous client',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} a réservé {appointmentName}',
	ClientAppointmentsEmptyStateDescription: `Aucun rendez-vous n'a été trouvé`,
	ClientAppointmentsEmptyStateTitle:
		'Gardez une trace des rendez-vous à venir et historiques de vos clients ainsi que de leur présence',
	ClientArchivedSuccessfulSnackbar: 'Archivage de <b>{name}</b> réussi',
	ClientBalance: 'Solde client',
	ClientBilling: 'Facturation',
	ClientBillingAddPaymentMethodDescription:
		'Ajoutez et gérez les modes de paiement de vos clients pour rationaliser leur processus de facturation.',
	ClientBillingAndPaymentDueDate: `Date d'échéance`,
	ClientBillingAndPaymentHistory: 'Historique de facturation et de paiement',
	ClientBillingAndPaymentInvoices: 'Factures',
	ClientBillingAndPaymentIssueDate: `Date d'émission`,
	ClientBillingAndPaymentPrice: 'Prix',
	ClientBillingAndPaymentReceipt: 'Reçu',
	ClientBillingAndPaymentServices: 'Prestations de service',
	ClientBillingAndPaymentStatus: 'Statut',
	ClientBulkStaffAssignedSuccessSnackbar: 'Équipe {count, plural, one {membre} other {membres}} affectée !',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Équipe {count, plural, one {membre} other {membres}} non affectée !',
	ClientBulkTagsAddedSuccessSnackbar: 'Balises ajoutées !',
	ClientDuplicatesDeviewDescription:
		'Fusionnez plusieurs dossiers clients en un seul pour unifier toutes les données : notes, documents, rendez-vous, factures et conversations.',
	ClientDuplicatesPageMergeHeader: 'Choisissez les données que vous souhaitez conserver',
	ClientDuplicatesReviewHeader: 'Comparer les enregistrements en double potentiels pour la fusion',
	ClientEmailChangeWarningDescription: `La mise à jour de l'e-mail du client supprimera son accès à toute documentation partagée et accordera l'accès à l'utilisateur avec le nouvel e-mail.`,
	ClientFieldDateDescription: 'Formater la date',
	ClientFieldDateLabel: 'Date',
	ClientFieldDateRangeDescription: 'Une plage de dates',
	ClientFieldDateRangeLabel: 'Plage de dates',
	ClientFieldDateShowDateDescription: 'par exemple 29 ans',
	ClientFieldDateShowDateRangeDescription: 'par exemple 2 semaines',
	ClientFieldEmailDescription: 'Adresse e-mail',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Libellé du champ',
	ClientFieldLinearScaleDescription: `Options d'échelle 1 à 10`,
	ClientFieldLinearScaleLabel: 'Échelle linéaire',
	ClientFieldLocationDescription: 'Adresse physique ou postale',
	ClientFieldLocationLabel: 'Emplacement',
	ClientFieldLongTextDescription: 'Zone de texte longue',
	ClientFieldLongTextLabel: 'Paragraphe',
	ClientFieldMultipleChoiceDropdownDescription: 'Choisissez plusieurs options dans la liste',
	ClientFieldMultipleChoiceDropdownLabel: 'Liste déroulante à choix multiples',
	ClientFieldPhoneNumberDescription: 'Numéro de téléphone',
	ClientFieldPhoneNumberLabel: 'Téléphone',
	ClientFieldPlaceholder: 'Choisissez un type de champ client',
	ClientFieldSingleChoiceDropdownDescription: 'Choisissez une seule option dans la liste',
	ClientFieldSingleChoiceDropdownLabel: 'Liste déroulante à choix unique',
	ClientFieldTextDescription: 'Champ de saisie de texte',
	ClientFieldTextLabel: 'Texte',
	ClientFieldYesOrNoDescription: 'Choisissez parmi les options oui ou non',
	ClientFieldYesOrNoLabel: 'Oui | Non',
	ClientFileFormAccessLevelDescription: `Vous et l'équipe avez toujours accès aux fichiers que vous téléchargez. Vous pouvez choisir de partager ce fichier avec le client et/ou ses relations`,
	ClientFileSavedSuccessSnackbar: 'Fichier enregistré !',
	ClientFilesPageEmptyStateText: 'Aucun fichier téléchargé',
	ClientFilesPageUploadFileButton: 'Télécharger des fichiers',
	ClientHeaderBilling: 'Facturation',
	ClientHeaderBillingAndReceipts: 'Facturation ',
	ClientHeaderDocumentation: 'Documentation',
	ClientHeaderDocuments: 'Documents',
	ClientHeaderFile: 'Document',
	ClientHeaderHistory: 'Antécédents médicaux',
	ClientHeaderInbox: 'Boîte de réception',
	ClientHeaderNote: 'Note',
	ClientHeaderOverview: 'Aperçu',
	ClientHeaderProfile: 'Personnel',
	ClientHeaderRelationship: 'Relation',
	ClientHeaderRelationships: 'Des relations',
	ClientId: 'ID client',
	ClientImportProcessingDescription:
		'Fichier toujours en cours de traitement. Nous vous informerons lorsque ce sera terminé.',
	ClientImportReadyForMappingDescription:
		'Nous avons terminé le prétraitement de votre fichier. Souhaitez-vous mapper les colonnes pour finaliser cette importation ?',
	ClientImportReadyForMappingNotificationSubject: `L'importation du client en prétraitement est terminée. Le fichier est maintenant prêt pour le mappage.`,
	ClientInAppMessaging: `Messagerie client dans l'application`,
	ClientInfoAddField: 'Ajouter un autre champ',
	ClientInfoAddRow: 'Ajouter une rangée',
	ClientInfoAlertMessage: 'Toute information renseignée dans cette section remplira le dossier client.',
	ClientInfoFormPrimaryText: 'Informations clients',
	ClientInfoFormSecondaryText: 'Recueillir les coordonnées',
	ClientInfoPlaceholder: `Nom du client, adresse e-mail, numéro de téléphone
 Adresse physique,
 Date de naissance`,
	ClientInformation: 'Informations client',
	ClientInsuranceTabLabel: 'Assurance',
	ClientIntakeFormsNotSupported: `Les modèles de formulaire ne sont actuellement pas pris en charge par le biais des admissions de clients.
 Créez-les et partagez-les plutôt sous forme de notes client.`,
	ClientIntakeModalDescription: `Un e-mail d'admission sera envoyé à votre client lui demandant de compléter son profil, de télécharger les documents médicaux ou de référence pertinents. Ils auront accès au portail client.`,
	ClientIntakeModalTitle: `Envoyer l'admission à {name}`,
	ClientIntakeSkipPasswordSuccessSnackbar: 'Succès! Votre consommation a été enregistrée.',
	ClientIntakeSuccessSnackbar: 'Succès! Votre apport a été enregistré et un email de confirmation envoyé.',
	ClientIsChargedProcessingFee: 'Vos clients paieront les frais de traitement',
	ClientListCreateButton: 'Nouvelle cliente',
	ClientListEmptyState: 'Aucun client ajouté',
	ClientListPageItemArchive: 'Supprimer le client',
	ClientListPageItemRemoveAccess: 'Supprimer mon accès',
	ClientLocalizationPanelDescription: 'La langue et le fuseau horaire préférés du client.',
	ClientLocalizationPanelTitle: 'Langue et fuseau horaire',
	ClientManagementAndEHR: 'La gestion des clients ',
	ClientMergeResultSummaryBanner: `La fusion des dossiers consolide toutes les données des clients, y compris les notes, les documents, les rendez-vous, les factures et les conversations. Vérifiez l'exactitude avant de continuer.`,
	ClientMergeResultSummaryTitle: 'Résumé de la fusion',
	ClientModalTitle: 'Nouvelle cliente',
	ClientMustHaveEmaillAccessErrorText: 'Clients/Contacts sans e-mail',
	ClientMustHavePortalAccessErrorText: 'Les clients/contacts devront s’inscrire',
	ClientMustHaveZoomAppConnectedErrorText: 'Connectez Zoom via Paramètres&gt; Applications connectées',
	ClientNameFormat: 'Format du nom du client',
	ClientNotFormAccessLevel: 'Visible par :',
	ClientNotFormAccessLevelDescription: `Vous et l'équipe avez toujours accès aux notes que vous publiez. Vous pouvez choisir de partager cette note avec le client et/ou ses relations`,
	ClientNotRegistered: 'Non enregistré',
	ClientNoteFormAddFileButton: 'Joindre des fichiers',
	ClientNoteFormChooseAClient: 'Choisissez un client/contact pour continuer',
	ClientNoteFormContent: 'Contenu',
	ClientNoteItemDeleteConfirmationModalDescription: 'Une fois supprimée, vous ne pourrez plus récupérer cette note.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Note publiée et verrouillée.',
	ClientNotePublishedSuccessSnackbar: 'Remarque publiée !',
	ClientNotes: 'Notes clients',
	ClientNotesEmptyStateText: `Pour ajouter des notes, accédez au profil d'un client et cliquez sur l'onglet Notes.`,
	ClientOnboardingChoosePasswordTitle1: 'Presque fini!',
	ClientOnboardingChoosePasswordTitle2: 'Choisissez un mot de passe',
	ClientOnboardingCompleteIntake: 'Admission complète',
	ClientOnboardingConfirmationScreenText:
		'Vous avez fourni toutes les informations requises par {providerName}.	Confirmez votre adresse électronique pour commencer votre intégration. Si vous ne la recevez pas immédiatement, veuillez vérifier votre dossier de courrier indésirable.',
	ClientOnboardingConfirmationScreenTitle: 'Super! Vérifiez votre boîte de réception.',
	ClientOnboardingDashboardButton: 'Aller au tableau de bord',
	ClientOnboardingHealthRecordsDesc1:
		'Souhaitez-vous partager des lettres de recommandation, des documents avec {providerName} ?',
	ClientOnboardingHealthRecordsDescription: 'Ajouter une description (facultatif)',
	ClientOnboardingHealthRecordsTitle: 'Documentation',
	ClientOnboardingPasswordRequirements: 'Exigences',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimum 9 caractères requis',
	ClientOnboardingProviderIntroSignupButton: `S'inscrire pour moi`,
	ClientOnboardingProviderIntroSignupFamilyButton: 'Inscrivez-vous pour un membre de la famille',
	ClientOnboardingProviderIntroTitle: '{name} vous a invité à rejoindre sa plateforme Carepatron',
	ClientOnboardingRegistrationInstructions: 'Entrez vos informations personnelles ci-dessous.',
	ClientOnboardingRegistrationTitle: `Nous aurons d'abord besoin de quelques informations personnelles`,
	ClientOnboardingStepFormsAndAgreements: 'Formulaires et accords',
	ClientOnboardingStepFormsAndAgreementsDesc1: `Veuillez compléter les formulaires suivants pour le processus d'admission de {providerName}`,
	ClientOnboardingStepHealthDetails: 'Détails de santé',
	ClientOnboardingStepPassword: 'Mot de passe',
	ClientOnboardingStepYourDetails: 'Vos détails',
	ClientPaymentMethodDescription:
		'Enregistrez un mode de paiement dans votre profil pour rendre votre prochaine prise de rendez-vous et votre facturation plus rapides et plus sécurisées.',
	ClientPortal: 'Portail client',
	ClientPortalDashboardEmptyDescription: 'Votre historique de rendez-vous et votre présence s’afficheront ici.',
	ClientPortalDashboardEmptyTitle:
		'Gardez une trace de tous les rendez-vous à venir, demandés et passés ainsi que de votre présence',
	ClientPreferredNotificationPanelDescription:
		'Gérez la méthode préférée de votre client pour recevoir des mises à jour et des notifications via :',
	ClientPreferredNotificationPanelTitle: 'Méthode de notification préférée',
	ClientProcessingFee: 'Le paiement comprend des frais de traitement de ({currencyCode}) {amount}',
	ClientProfileAddress: 'Adresse',
	ClientProfileDOB: 'Date de naissance',
	ClientProfileEmailHelperText: `L'ajout d'un e-mail accorde l'accès au portail`,
	ClientProfileEmailHelperTextMoreInfo: `Accorder au client l'accès au portail permet aux membres de l'équipe de partager des notes, des fichiers et d'autres documents`,
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: `Numéro d'identification`,
	ClientRelationshipsAddClientOwnerButton: 'Inviter le client',
	ClientRelationshipsAddFamilyButton: 'Inviter un membre de la famille',
	ClientRelationshipsAddStaffButton: 'Ajouter un accès au personnel',
	ClientRelationshipsEmptyStateText: 'Aucune relation ajoutée',
	ClientRemovedSuccessSnackbar: 'Client supprimé avec succès.',
	ClientResponsibility: 'Responsabilité du client',
	ClientSavedSuccessSnackbar: 'Client enregistré avec succès.',
	ClientTableClientName: 'Nom du client',
	ClientTablePhone: 'Téléphone',
	ClientTableStatus: 'Statut',
	ClientUnarchivedSuccessfulSnackbar: 'Archivage de **{name}** annulé avec succès',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} a modifié {noteTitle, select, undefined { une note } other {{noteTitle}}}',
	ClientView: 'Vue client',
	Clients: 'Clientèle',
	ClientsTable: 'Tableau des clients',
	ClinicalFormat: 'Format clinique',
	ClinicalPsychologist: 'Psychologue clinicien',
	Close: 'Fermer',
	CloseImportClientsModal: `Êtes-vous sûr de vouloir annuler l'importation de clients ?`,
	CloseReactions: 'Réactions proches',
	Closed: 'Fermé',
	Coaching: 'encadrement',
	Code: 'Code',
	CodeErrorMessage: 'Le code est requis',
	CodePlaceholder: 'Code',
	Coinsurance: 'Coassurance',
	Collection: 'Collection',
	CollectionName: 'Nom de la collection',
	Collections: 'Collections',
	ColorAppointmentsBy: 'Rendez-vous des couleurs par',
	ColorTheme: 'Thème de couleur',
	ColourCalendarBy: 'Calendrier couleur par',
	ComingSoon: 'À venir',
	Community: 'Communauté',
	CommunityHealthLead: 'Responsable de la santé communautaire',
	CommunityHealthWorker: 'Agent de santé communautaire',
	CommunityTemplatesSectionDescription: 'Créé par la communauté Carepatron',
	CommunityTemplatesSectionTitle: 'Communauté',
	CommunityUser: 'Utilisateur communautaire',
	Complete: 'Complet',
	CompleteAndLock: 'Compléter et verrouiller',
	CompleteSetup: 'Installation complète',
	CompleteSetupSuccessDescription: 'Vous avez terminé certaines étapes clés pour maîtriser Carepatron.',
	CompleteSetupSuccessDescription2:
		'Débloquez plus de façons de rationaliser votre pratique et de soutenir vos clients.',
	CompleteSetupSuccessTitle: 'Succès ! Vous êtes incroyable !',
	CompleteStripeSetup: 'Configuration complète de Stripe',
	Completed: 'Terminé',
	ComposeSms: 'Rédiger un SMS',
	ComputerSystemsAnalyst: 'Analyste des systèmes informatiques',
	Confirm: 'Confirmer',
	ConfirmDeleteAccountDescription:
		'Vous êtes sur le point de supprimer votre compte. Cette action ne peut pas être annulée. Si vous souhaitez continuer, veuillez confirmer ci-dessous.',
	ConfirmDeleteActionDescription:
		'Etes-vous sûr de vouloir supprimer cette action ? Cette action ne peut pas être annulée',
	ConfirmDeleteAutomationDescription:
		'Etes-vous sûr de vouloir supprimer cette automatisation ? Cette action ne peut pas être annulée.',
	ConfirmDeleteScheduleDescription:
		'Supprimer le calendrier **{scheduleName}** le supprimera de vos calendriers et peut modifier votre service en ligne disponible. Cette action ne peut pas être annulée.',
	ConfirmDraftResponseContinue: 'Continuer avec la réponse',
	ConfirmDraftResponseDescription:
		'Si vous fermez cette page, votre réponse restera sous forme de brouillon. Vous pouvez revenir et continuer à tout moment.',
	ConfirmDraftResponseSubmitResponse: 'Soumettre la réponse',
	ConfirmDraftResponseTitle: `Votre réponse n'a pas été soumise`,
	ConfirmIfUserIsClientDescription: `Le formulaire d'inscription que vous avez rempli est destiné aux prestataires (c'est-à-dire les équipes/organisations de santé).
 S'il s'agit d'une erreur, vous pouvez choisir &quot;Continuer en tant que client&quot; et nous vous aiderons à configurer votre portail client.`,
	ConfirmIfUserIsClientNoButton: 'Inscrivez-vous en tant que fournisseur',
	ConfirmIfUserIsClientTitle: 'On dirait que tu es un client',
	ConfirmIfUserIsClientYesButton: 'Continuer en tant que client',
	ConfirmKeepSeparate: 'Confirmer garder séparé',
	ConfirmMerge: 'Confirmer la fusion',
	ConfirmPassword: 'Confirmez le mot de passe',
	ConfirmRevertClaim: 'Oui, rétablir le statut',
	ConfirmSignupAccessCode: 'Code de confirmation',
	ConfirmSignupButtom: 'Confirmer',
	ConfirmSignupDescription:
		'Veuillez saisir votre adresse e-mail et le code de confirmation que nous venons de vous envoyer.',
	ConfirmSignupSubTitle: `Vérifiez le dossier Spam - si l'e-mail n'est pas arrivé`,
	ConfirmSignupSuccessSnackbar:
		'Super, nous avons confirmé votre compte ! Vous pouvez maintenant vous connecter en utilisant votre email et votre mot de passe',
	ConfirmSignupTitle: 'Confirmer le compte',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: `Confirmer l'abonnement {price} {isMonthly, select, true {par mois} other {par an}}`,
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Une fois les clients supprimés, vous ne pourrez plus accéder à leurs informations.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Supprimer {count, plural, one {# client} other {# clients}} ?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Une fois les contacts supprimés, vous ne pourrez plus accéder à leurs informations.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Supprimer {count, plural, one {# contact} other {# contacts}} ?',
	ConfirmationModalBulkDeleteMembersDescriptionId: `Il s'agit d'une action permanente. Une fois les membres de l’équipe supprimés, vous ne pourrez plus accéder à leurs informations.`,
	ConfirmationModalBulkDeleteMembersTitleId: `Supprimer {count, plural, one {# membre de l'équipe} other {# membres de l'équipe}} ?`,
	ConfirmationModalCloseOnGoingTranscription:
		'La fermeture de cette note mettra fin à toutes les transcriptions en cours. Êtes-vous sûr de vouloir continuer ?',
	ConfirmationModalDeleteClientField: `Il s'agit d'une action permanente. Une fois le champ supprimé, il ne sera plus accessible sur vos clients restants.`,
	ConfirmationModalDeleteSectionMessage:
		'Une fois supprimées, toutes les questions de cette section seront supprimées. Cette action ne peut pas être annulée.',
	ConfirmationModalDeleteService: `Il s'agit d'une action permanente. Une fois le service supprimé, il ne sera plus accessible sur votre espace de travail.`,
	ConfirmationModalDeleteServiceGroup: `La suppression d'une collection supprimera tous les services du groupe et reviendra à votre liste de services. Cette action ne peut pas être annulée.`,
	ConfirmationModalDeleteTranscript: 'Êtes-vous sûr de vouloir supprimer la transcription ?',
	ConfirmationModalDescriptionDeleteClient:
		'Une fois le client supprimé, vous ne pourrez plus accéder aux informations du client.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Une fois votre accès supprimé, vous ne pourrez plus consulter les informations client.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Leur profil ne sera pas supprimé, mais simplement supprimé en tant que relation de ce client.',
	ConfirmationModalDescriptionRemoveStaff: 'Êtes-vous sûr de vouloir supprimer cette personne du fournisseur ?',
	ConfirmationModalEndSession: 'Etes-vous sûr de vouloir terminer la session ?',
	ConfirmationModalTitle: 'Es-tu sûr?',
	Confirmed: 'Confirmé',
	ConflictTimezoneWarningMessage: 'Des conflits peuvent survenir en raison de plusieurs fuseaux horaires',
	Connect: 'Connecter',
	ConnectExistingClientOrContact: 'Créer un nouveau client/contact',
	ConnectInboxGoogleDescription: 'Ajouter un compte Gmail ou une liste de groupes Google',
	ConnectInboxMicrosoftDescription: 'Ajouter un compte Outlook, Office365 ou Exchange',
	ConnectInboxModalDescription:
		'Connectez vos applications pour envoyer, recevoir et suivre en toute transparence toutes vos communications en un seul endroit centralisé.',
	ConnectInboxModalExistingDescription:
		'Utilisez une connexion existante à partir des paramètres de vos applications connectées pour rationaliser le processus de configuration.',
	ConnectInboxModalExistingTitle: 'Application connectée existante dans Carepatron',
	ConnectInboxModalTitle: 'Connecter la boîte de réception',
	ConnectToStripe: 'Connectez-vous à Stripe',
	ConnectZoom: 'Connecter Zoom',
	ConnectZoomModalDescription: 'Autorisez Carepatron à gérer les appels vidéo pour vos rendez-vous.',
	ConnectedAppDisconnectedNotificationSubject:
		'Nous avons perdu la connexion au compte {account}. Veuillez vous reconnecter.',
	ConnectedAppSyncDescription:
		'Gérez les applications connectées pour créer des événements dans des calendriers tiers directement depuis Carepatron.',
	ConnectedApps: 'Applications connectées',
	ConnectedAppsGMailDescription: 'Ajouter des comptes Gmail ou une liste de groupes Google',
	ConnectedAppsGoogleCalendarDescription: 'Ajouter des comptes de calendriers ou une liste de groupes Google',
	ConnectedAppsGoogleDescription: 'Ajoutez votre compte Gmail et synchronisez les calendriers Google',
	ConnectedAppsMicrosoftDescription: 'Ajouter un compte Outlook, Office365 ou Exchange',
	ConnectedCalendars: 'Calendriers connectés',
	ConsentDocumentation: 'Formulaires et accords',
	ConsentDocumentationPublicTemplateError:
		'Pour des raisons de sécurité, vous ne pouvez choisir que des modèles de votre équipe (non publics).',
	ConstructionWorker: 'Ouvrier du batiment',
	Consultant: 'Consultant',
	Contact: 'Contact',
	ContactAccessTypeHelperText: 'Permet aux administrateurs de la famille de mettre à jour les informations',
	ContactAccessTypeHelperTextMoreInfo:
		'Ceci vous permettra de partager des notes/documents concernant {clientFirstName}',
	ContactAddressLabelBilling: 'Facturation',
	ContactAddressLabelHome: 'Maison',
	ContactAddressLabelOthers: 'Autres',
	ContactAddressLabelWork: 'Travail',
	ContactChangeConfirmation:
		'Changer le contact de la facture supprimera tous les éléments liés à <mark>{contactName}</mark>',
	ContactDetails: 'Détails du contact',
	ContactEmailLabelOthers: 'Autres',
	ContactEmailLabelPersonal: 'Personnel',
	ContactEmailLabelSchool: 'École',
	ContactEmailLabelWork: 'Travail',
	ContactInformation: 'Coordonnées',
	ContactInformationText: 'Coordonnées',
	ContactListCreateButton: 'Nouveau contact',
	ContactName: 'Nom du contact',
	ContactPhoneLabelHome: 'Maison',
	ContactPhoneLabelMobile: 'Mobile',
	ContactPhoneLabelSchool: 'École',
	ContactPhoneLabelWork: 'Travail',
	ContactRelationship: 'Relation de contact',
	ContactRelationshipFormAccessType: `Accorder l'accès aux informations partagées`,
	ContactRelationshipGrantAccessInfo: 'Cela vous permettra de partager des notes et des documents',
	ContactSupport: 'Contacter le support',
	Contacts: 'Contacts',
	ContainerIdNotSet: 'ID de conteneur non défini',
	Contemporary: 'Contemporain',
	Continue: 'Continuer',
	ContinueDictating: 'Continuer à dicter',
	ContinueEditing: 'Continuer la modification',
	ContinueImport: `Continuer l'importation`,
	ContinueTranscription: 'Continuer la transcription',
	ContinueWithApple: 'Continuer avec Apple',
	ContinueWithGoogle: 'Continuer avec Google',
	Conversation: 'Conversation',
	Copay: 'Co-paiement',
	CopayOrCoinsurance: 'Co-paiement ou coassurance',
	Copayment: 'Co-paiement',
	CopiedToClipboard: 'Copié dans le presse-papier',
	Copy: 'Copie',
	CopyAddressSuccessSnackbar: 'Adresse copiée dans le presse-papiers',
	CopyCode: 'Copier le code',
	CopyCodeToClipboardSuccess: 'Code copié dans le presse-papiers',
	CopyEmailAddressSuccessSnackbar: 'Adresse électronique copiée dans le presse-papiers',
	CopyLink: 'Copier le lien',
	CopyLinkForCall: 'Copiez ce lien pour partager cet appel :',
	CopyLinkSuccessSnackbar: 'Lien copié vers le presse-papiers',
	CopyMeetingLink: 'Copier le lien de la réunion',
	CopyPaymentLink: 'Copier le lien de paiement',
	CopyPhoneNumberSuccessSnackbar: 'Numéro de téléphone copié dans le presse-papiers',
	CopyTemplateLink: 'Copier le lien vers le modèle',
	CopyTemplateLinkSuccess: 'Lien copié vers le presse-papiers',
	CopyToClipboardError: 'Impossible de copier dans le presse-papiers. Veuillez réessayer.',
	CopyToTeamTemplates: `Copier dans les modèles d'équipe`,
	CopyToWorkspace: `Copier dans l'espace de travail`,
	Cosmetologist: 'Cosmétologue',
	Cost: 'Coût',
	CostErrorMessage: 'Le coût est requis',
	Counseling: 'Conseils',
	Counselor: 'Conseiller',
	Counselors: 'Conseillers',
	CountInvoicesAdded: '{count, plural, one {# Facture ajoutée} other {# Factures ajoutées}}',
	CountNotesAdded: '{count, plural, one {# Note ajouté} other {# Notes ajoutés}}',
	CountSelected: '{count} sélectionné(e)s',
	CountTimes: '{count} fois',
	Country: 'Pays',
	Cousin: 'Cousin',
	CoverageType: 'Type de couverture',
	Covered: 'Couvert',
	Create: 'Créer',
	CreateANewClient: 'Créer un nouveau client',
	CreateAccount: 'Créer un compte',
	CreateAndSignNotes: 'Créer et signer une note avec les clients',
	CreateAvailabilityScheduleFailure: `Échec de la création d'un nouveau calendrier de disponibilité`,
	CreateAvailabilityScheduleSuccess: 'Nouveau planning de disponibilité créé avec succès',
	CreateBillingItems: 'Créer des éléments de facturation',
	CreateCallFormButton: `Démarrer l'appel`,
	CreateCallFormInviteOnly: 'Inviter seulement',
	CreateCallFormInviteOnlyMoreInfo: `Seules les personnes invitées à cet appel peuvent y participer. Pour partager cet appel avec d'autres, décochez simplement cette case et copiez/collez le lien sur la page suivante`,
	CreateCallFormRecipients: 'Destinataires',
	CreateCallFormRegion: `Région d'accueil`,
	CreateCallModalAddClientContactSelectorLabel: 'Contacts clients',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Rechercher par nom de client',
	CreateCallModalAddStaffSelectorLabel: `Membres de l'équipe (facultatif)`,
	CreateCallModalAddStaffSelectorPlaceholder: 'Recherche par nom de personnel',
	CreateCallModalDescription:
		'Démarrez un appel et invitez des membres du personnel et/ou des contacts. Vous pouvez également décocher la case &quot;Privé&quot; pour que cet appel puisse être partagé avec toute personne disposant de Carepatron.',
	CreateCallModalTitle: 'Démarrer un appel',
	CreateCallModalTitleLabel: 'Titre (facultatif)',
	CreateCallNoPersonIdToolTip: 'Seuls les contacts/clients ayant accès au portail peuvent rejoindre les appels',
	CreateClaim: 'Créer une réclamation',
	CreateClaimCompletedMessage: 'Votre réclamation a été créée.',
	CreateClientModalTitle: 'Nouvelle cliente',
	CreateContactModalTitle: 'Nouveau contact',
	CreateContactRelationshipButton: 'Ajouter une relation',
	CreateContactSelectorDefaultOption: '  Créer un contact',
	CreateContactWithRelationshipFormAccessType: `Accorder l'accès aux informations partagées `,
	CreateDocumentDnDPrompt: 'Glisser-déposer pour télécharger des fichiers',
	CreateDocumentSizeLimit: 'Limite de taille par fichier {size} Mo. {total} fichiers au total.',
	CreateFreeAccount: 'Créer un compte gratuitement',
	CreateInvoice: 'Créer une facture',
	CreateLink: 'Créer un lien',
	CreateNew: 'Créer un nouveau',
	CreateNewAppointment: 'Créer un nouveau rendez-vous',
	CreateNewClaim: 'Créer une nouvelle réclamation',
	CreateNewClaimForAClient: 'Créer une nouvelle réclamation pour un client.',
	CreateNewClient: 'Créer un nouveau client',
	CreateNewConnection: 'Nouvelle connexion',
	CreateNewContact: 'Créer un nouveau contact',
	CreateNewField: 'Créer un nouveau champ',
	CreateNewLocation: 'Nouvel emplacement',
	CreateNewService: 'Créer un nouveau service',
	CreateNewServiceGroupFailure: `Échec de la création d'une nouvelle collection`,
	CreateNewServiceGroupMenu: 'Nouvelle collection',
	CreateNewServiceGroupSuccess: 'Nouvelle collection créée avec succès',
	CreateNewServiceMenu: 'Nouveau service',
	CreateNewTeamMember: `Créer un nouveau membre de l'équipe`,
	CreateNewTemplate: 'Nouveau modèle',
	CreateNote: 'Créer une note',
	CreateSuperbillReceipt: 'Nouvelle superbe facture',
	CreateSuperbillReceiptSuccess: 'Reçu Superbill créé avec succès',
	CreateTemplateFolderSuccessMessage: 'Création de {folderTitle} réussie',
	Created: 'Créé',
	CreatedAt: 'Créé {timestamp}',
	Credit: 'Crédit',
	CreditAdded: 'Crédit appliqué',
	CreditAdjustment: 'Ajustement du crédit',
	CreditAdjustmentReasonHelperText: `Il s'agit d'une note interne et ne sera pas visible par votre client.`,
	CreditAdjustmentReasonPlaceholder: `L'ajout d'un motif d'ajustement peut s'avérer utile lors de l'examen des transactions facturables.`,
	CreditAmount: '{amount} AV',
	CreditBalance: 'Solde créditeur',
	CreditCard: 'Carte de crédit',
	CreditCardExpire: 'Expire {exp_month}/{exp_year}',
	CreditCardNumber: 'Numéro de Carte de Crédit',
	CreditDebitCard: 'Carte',
	CreditIssued: 'Crédit émis',
	CreditsUsed: 'Crédits utilisés',
	Crop: 'Recadrer',
	Currency: 'Devise',
	CurrentCredit: 'Crédit actuel',
	CurrentEventTime: `Heure de l'événement actuel`,
	CurrentPlan: 'Plan actuel',
	Custom: 'Coutume',
	CustomRange: 'Gamme personnalisée',
	CustomRate: 'Tarif personnalisé',
	CustomRecurrence: 'Récurrence personnalisée',
	CustomServiceAvailability: 'Disponibilité du service',
	CustomerBalance: 'Solde client',
	CustomerName: 'Nom du client',
	CustomerNameIsRequired: 'Le nom du client est requis',
	CustomerServiceRepresentative: 'Représentant du service à la clientèle',
	CustomiseAppointments: 'Personnaliser les rendez-vous',
	CustomiseBookingLink: 'Personnaliser les options de réservation',
	CustomiseBookingLinkServicesInfo: 'Les clients ne peuvent choisir que des services réservables',
	CustomiseBookingLinkServicesLabel: 'Prestations de service',
	CustomiseClientRecordsAndWorkspace: 'Personnalisez vos dossiers clients et votre espace de travail',
	CustomiseClientSettings: 'Personnaliser les paramètres du client',
	Customize: 'Personnaliser',
	CustomizeAppearance: `Personnaliser l'apparence`,
	CustomizeAppearanceDesc:
		'Personnalisez votre apparence de réservation en ligne en fonction de votre marque et optimisez la façon dont vos services sont présentés aux clients.',
	CustomizeClientFields: 'Personnaliser champs de clients',
	CustomizeInvoiceTemplate: 'Personnaliser le modèle de facture',
	CustomizeInvoiceTemplateDescription: 'Créez sans effort des factures professionnelles qui reflètent votre marque.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX est requis',
	Daily: 'Tous les jours',
	DanceTherapist: 'Danse-thérapeute',
	DangerZone: 'Zone dangereuse',
	Dashboard: 'Tableau de bord',
	Date: 'Date',
	DateAndTime: 'Date ',
	DateDue: `Date d'échéance`,
	DateErrorMessage: 'La date est requise',
	DateFormPrimaryText: 'Date',
	DateFormSecondaryText: 'Choisissez parmi un sélecteur de date',
	DateIssued: `Date d'émission`,
	DateOfPayment: 'Date de règlement',
	DateOfService: 'Date de service',
	DateOverride: 'Remplacement de la date',
	DateOverrideColor: 'Couleur de remplacement de la date',
	DateOverrideInfo: `Les remplacements de dates permettent aux praticiens d'ajuster manuellement leur disponibilité pour des dates spécifiques en remplaçant les horaires réguliers.`,
	DateOverrideInfoBanner: `Seuls les services spécifiés pour ce remplacement de date peuvent être réservés dans ces plages horaires ; aucune autre réservation en ligne n'est autorisée.`,
	DateOverrides: 'Remplacements de date',
	DatePickerFormPrimaryText: 'Date',
	DatePickerFormSecondaryText: 'Choisissez une date',
	DateRange: 'Plage de dates',
	DateRangeFormPrimaryText: 'Plage de dates',
	DateRangeFormSecondaryText: 'Choisissez une plage de dates',
	DateReceived: 'Date de réception',
	DateSpecificHours: 'Heures spécifiques à une date',
	DateSpecificHoursDescription:
		'Ajoutez des dates lorsque votre disponibilité change par rapport à vos heures programmées ou pour proposer un service à une date précise.',
	DateUploaded: 'Téléchargé {date, date, medium}',
	Dates: 'Rendez-vous',
	Daughter: 'Fille',
	Day: 'Jour',
	DayPlural: '{count, plural, one {jour} other {jours}}',
	Days: 'Jours',
	DaysPlural: '{age, plural, one {# jour} other {# jours}}',
	DeFacto: 'De facto',
	Deactivated: 'Désactivé',
	Debit: 'Débit',
	DecreaseIndent: 'Diminuer le retrait',
	Deductibles: 'Franchises',
	Default: 'Défaut',
	DefaultBillingProfile: 'Profil de facturation par défaut',
	DefaultDescription: 'Description par défaut',
	DefaultEndOfLine: `Plus d'articles`,
	DefaultInPerson: 'Rendez-vous clients',
	DefaultInvoiceTitle: 'Titre par défaut',
	DefaultNotificationSubject: 'Vous avez reçu une nouvelle notification pour {notificationType}',
	DefaultPaymentMethod: 'Méthode de paiement par défaut',
	DefaultService: 'Service par défaut',
	DefaultValue: 'Défaut',
	DefaultVideo: 'E-mail de rendez-vous vidéo client',
	DefinedTemplateType: '{invoiceTemplate} modèle',
	Delete: 'Supprimer',
	DeleteAccountButton: 'Supprimer le compte',
	DeleteAccountDescription: 'Supprimez votre compte de la plateforme',
	DeleteAccountPanelInfoAlert: `Vous devez supprimer vos espaces de travail avant de supprimer votre profil. Pour continuer, accédez à un espace de travail et sélectionnez Paramètres > Paramètres de l'espace de travail.`,
	DeleteAccountTitle: 'Supprimer le compte',
	DeleteAppointment: 'Supprimer le rendez-vous',
	DeleteAppointmentDescription:
		'Etes-vous sûr de vouloir supprimer ce rendez-vous ? Vous pourrez le restaurer plus tard.',
	DeleteAvailabilityScheduleFailure: 'Échec de la suppression du calendrier de disponibilité',
	DeleteAvailabilityScheduleSuccess: 'Calendrier de disponibilité supprimé avec succès',
	DeleteBillable: 'Supprimer facturable',
	DeleteBillableConfirmationMessage:
		'Etes-vous sûr de vouloir supprimer cette facture ? Cette action ne peut pas être annulée.',
	DeleteBillingProfileConfirmationMessage: 'Cela supprimera définitivement le profil de facturation.',
	DeleteCardConfirmation: `Il s'agit d'une action permanente. Une fois la carte supprimée, vous ne pourrez plus y accéder.`,
	DeleteCategory: `Supprimer la catégorie (ceci n'est pas permanent sauf si les modifications sont enregistrées)`,
	DeleteClientEventConfirmationDescription: 'Celui-ci sera définitivement supprimé.',
	DeleteClients: 'Supprimer des clients',
	DeleteCollection: 'Supprimer la collection',
	DeleteColumn: 'Supprimer la colonne',
	DeleteConversationConfirmationDescription:
		'Supprimez définitivement cette conversation. Cette action ne peut pas être annulée.',
	DeleteConversationConfirmationTitle: 'Supprimer définitivement la conversation',
	DeleteExternalEventDescription: 'Êtes-vous sûr de vouloir supprimer ce rendez-vous ?',
	DeleteFileConfirmationModalPrompt: 'Une fois supprimé, vous ne pourrez plus récupérer ce fichier.',
	DeleteFolder: 'Supprimer le dossier',
	DeleteFolderConfirmationMessage: `Êtes-vous sûr de vouloir supprimer ce dossier {name} ? Tous les éléments à l'intérieur de ce dossier seront également supprimés. Vous pourrez le restaurer plus tard.`,
	DeleteForever: 'Supprimer pour toujours',
	DeleteInsurancePayerConfirmationMessage: `La suppression de {payer} le supprimera de votre liste de payeurs d'assurance. Cette action est permanente et ne peut pas être restaurée.`,
	DeleteInsurancePayerFailure: `Impossible de supprimer le payeur d'assurance`,
	DeleteInsurancePolicyConfirmationMessage: 'Cela supprimera définitivement la police d’assurance.',
	DeleteInvoiceConfirmationDescription:
		'Cette action ne peut pas être annulée. Cela supprimera définitivement la facture et tous les paiements qui y sont associés.',
	DeleteLocationConfirmation: `La suppression d'un emplacement est une action permanente. Une fois supprimé, vous n’y aurez plus accès. Cette action ne peut pas être annulée.`,
	DeletePayer: 'Supprimer le payeur',
	DeletePracticeWorkspace: `Supprimer l'espace de travail de ce cabinet`,
	DeletePracticeWorkspaceDescription: `Supprimer définitivement l'espace de travail de ce cabinet`,
	DeletePracticeWorkspaceFailedSnackbar: `Échec de la suppression de l'espace de travail`,
	DeletePracticeWorkspaceModalCancelButton: 'Oui, annuler mon abonnement',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription: `Avant de procéder à la suppression de votre espace de travail, vous devez d'abord annuler votre abonnement.`,
	DeletePracticeWorkspaceModalConfirmButton: `Oui, supprimer définitivement l'espace de travail`,
	DeletePracticeWorkspaceModalDescription: `L'espace de travail de {name} sera supprimé de façon permanente et tous les membres de l'équipe perdront l'accès. Téléchargez toutes les données ou les messages importants dont vous pourriez avoir besoin avant la suppression. Cette action est irréversible.`,
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Cette décision a été prise en raison de :',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Raison',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Veuillez nous indiquer pourquoi vous souhaitez supprimer votre compte.',
	DeletePracticeWorkspaceModalTitle: 'Es-tu sûr?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'L’accès de tous les membres de l’équipe a été supprimé',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} a été supprimé avec succès.',
	DeletePublicTemplateContent: 'Cela supprimera uniquement le modèle public et non le modèle de votre équipe.',
	DeleteRecurringAppointmentModalTitle: 'Supprimer un rendez-vous répétitif',
	DeleteRecurringEventModalTitle: 'Supprimer la réunion répétitive',
	DeleteRecurringReminderModalTitle: 'Supprimer le rappel répétitif',
	DeleteRecurringTaskModalTitle: 'Supprimer la tâche répétitive',
	DeleteReminderConfirmation: `Il s'agit d'une action permanente. Une fois le rappel supprimé, vous ne pourrez plus y accéder. N'affectera que les nouveaux rendez-vous`,
	DeleteSection: 'Supprimer la rubrique',
	DeleteSectionInfo: `Supprimer la section **{section}** cachera tous les champs existants qu'elle contient. Cette action est irréversible.`,
	DeleteSectionWarning:
		'Les champs principaux ne peuvent pas être supprimés et seront déplacés vers la section existante **{section}**.',
	DeleteServiceFailure: 'Échec de la suppression du service',
	DeleteServiceSuccess: 'Service supprimé avec succès',
	DeleteStaffScheduleOverrideDescription:
		'Supprimer cette substitution de date sur {value} la supprimera de vos horaires et peut modifier la disponibilité de votre service en ligne. Cette action est irréversible.',
	DeleteSuperbillConfirmationDescription:
		'Cette action ne peut pas être annulée. Cela supprimera définitivement le reçu Superbill.',
	DeleteSuperbillFailure: 'Échec de la suppression du reçu Superbill',
	DeleteSuperbillSuccess: 'Reçu Superbill supprimé avec succès',
	DeleteTaxRateConfirmationDescription: 'Êtes-vous sûr de vouloir supprimer ce taux de taxe ?',
	DeleteTemplateContent: 'Cette action ne peut pas être annulée',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} supprimé avec succès',
	DeleteTemplateSuccessMessage: '{templateTitle} supprimé avec succès',
	DeleteTemplateTitle: 'Êtes-vous sûr de vouloir supprimer ce modèle ?',
	DeleteTranscript: 'Supprimer la transcription',
	DeleteWorkspace: `Supprimer l'espace de travail`,
	Deleted: 'Supprimé',
	DeletedBy: 'Supprimé par',
	DeletedContact: 'Contact supprimé',
	DeletedOn: 'Supprimé le',
	DeletedStatusLabel: 'Statut supprimé',
	DeletedUserTooltip: 'Ce client a été supprimé',
	DeliveryMethod: 'méthode de livraison',
	Demo: 'Démo',
	Denied: 'Refusé',
	Dental: 'Dentaire',
	DentalAssistant: 'Assistante dentaire',
	DentalHygienist: 'Hygiéniste dentaire',
	Dentist: 'Dentiste',
	Dentists: 'Dentistes',
	Description: 'Description',
	DescriptionMustNotExceed: 'Description ne doit pas dépasser {max} caractères',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { avec {staffName}}}',
	Details: 'Détails',
	Devices: 'Dispositifs',
	Diagnosis: 'Diagnostic',
	DiagnosisAndBillingItems: 'Diagnostic ',
	DiagnosisCode: 'Code de diagnostic',
	DiagnosisCodeErrorMessage: 'Un code de diagnostic est requis',
	DiagnosisCodeSelectorPlaceholder: 'Rechercher et ajouter des codes de diagnostic CIM-10',
	DiagnosisCodeSelectorTooltip: `Les codes de diagnostic sont utilisés pour automatiser les reçus de superbes factures pour le remboursement de l'assurance.`,
	DiagnosticCodes: 'Codes de diagnostic',
	Dictate: 'Dicter',
	DictatingIn: 'Dicter en',
	Dictation: 'Dictée',
	DidNotAttend: `Ne s'est pas présenté`,
	DidNotComplete: 'Non terminé',
	DidNotProviderEnoughValue: `N'a pas fourni suffisamment de valeur`,
	DidntProvideEnoughValue: `N'a pas fourni suffisamment de valeur`,
	DieteticsOrNutrition: 'Diététique ou nutrition',
	Dietician: 'Diététicien',
	Dieticians: 'Diététistes',
	Dietitian: 'Diététicien',
	DigitalSign: 'Signez ici:',
	DigitalSignHelp: '(Cliquez/appuyez pour dessiner)',
	DirectDebit: 'Prélèvement automatique',
	DirectTextLink: 'Lien texte direct',
	Disable: 'Désactiver',
	DisabledEmailInfo: `Nous ne pouvons pas mettre à jour votre adresse e-mail car votre compte n'est pas géré par nous`,
	Discard: 'Jeter',
	DiscardChanges: 'Annuler les modifications',
	DiscardDrafts: 'Jeter les brouillons',
	Disconnect: 'Déconnecter',
	DisconnectAppConfirmation: 'Voulez-vous déconnecter cette application ?',
	DisconnectAppConfirmationDescription: 'Êtes-vous sûr de vouloir déconnecter cette application ?',
	DisconnectAppConfirmationTitle: `Déconnecter l'application`,
	Discount: 'Rabais',
	DisplayCalendar: 'Afficher dans Carepatron',
	DisplayName: 'Afficher un nom',
	DisplayedToClients: 'Affiché aux clients',
	DiversionalTherapist: 'Thérapeute diversionnel',
	DoItLater: 'Faites-le plus tard',
	DoNotImport: 'Ne pas importer',
	DoNotSend: 'Ne pas envoyer',
	DoThisLater: 'Faites ceci plus tard',
	DoYouWantToEndSession: 'Voulez-vous continuer ou terminer votre session maintenant ?',
	Doctor: 'Médecin',
	Doctors: 'Médecins',
	DoesNotRepeat: 'Ne se répète pas',
	DoesntWorkWellWithExistingTools: 'Ne fonctionne pas bien avec nos outils ou flux de travail existants',
	DogWalker: 'Promeneur de chien',
	Done: 'Fait',
	DontAllowClientsToCancel: `Ne pas permettre aux clients d'annuler`,
	DontHaveAccount: `Vous n'avez pas de compte ?`,
	DontSend: 'Ne pas envoyer',
	Double: 'Double',
	DowngradeTo: 'Passer à {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Désolé, vous ne pouvez pas rétrograder votre forfait car votre équipe est trop nombreuse. Veuillez en supprimer certains de votre fournisseur et réessayer.',
	Download: 'Télécharger',
	DownloadAsPdf: 'Télécharger en PDF',
	DownloadERA: 'Télécharger ERA',
	DownloadPDF: 'Télécharger le PDF',
	DownloadTemplateFileName: 'Modèle de commutation Carepatron.csv',
	DownloadTemplateTileDescription:
		'Utilisez notre modèle de feuille de calcul pour organiser et télécharger vos clients.',
	DownloadTemplateTileLabel: 'Télécharger le modèle',
	Downloads: '{number, plural, one {<span>#</span> Téléchargement} other {<span>#</span> Téléchargements}}',
	DoxyMe: 'Doxy.moi',
	Draft: 'Brouillon',
	DraftResponses: 'Projet de réponse',
	DraftSaved: 'Modifications enregistrées',
	DragAndDrop: 'glisser-déposer',
	DragDropText: 'Glissez-déposez les documents de santé',
	DragToMove: 'Glisser pour déplacer',
	DragToMoveOrActivate: 'Faites glisser pour déplacer ou activer',
	DramaTherapist: 'Dramathérapeute',
	DropdownFormFieldPlaceHolder: 'Choisissez des options dans la liste',
	DropdownFormPrimaryText: 'Dérouler',
	DropdownFormSecondaryText: `Choisissez parmi une liste d'options`,
	DropdownTextFieldError: `Le texte de l'option déroulante ne peut pas être vide`,
	DropdownTextFieldPlaceholder: 'Ajouter une option déroulante',
	Due: `Date d'échéance`,
	DueDate: `Date d'échéance`,
	Duplicate: 'Dupliquer',
	DuplicateAvailabilityScheduleFailure: 'Échec de la duplication du calendrier de disponibilité',
	DuplicateAvailabilityScheduleSuccess: 'Horaires de {name} dupliqués avec succès',
	DuplicateClientBannerAction: 'DupliquerClientBannerAction',
	DuplicateClientBannerDescription:
		'La fusion des enregistrements clients en double les consolide en un seul, conservant toutes les informations client uniques.',
	DuplicateClientBannerTitle: '{count} Duplicatas trouvés',
	DuplicateColumn: 'Colonne en double',
	DuplicateContactFieldSettingErrorSnackbar: `Impossible d'avoir des noms de section en double`,
	DuplicateContactFieldSettingFieldErrorSnackbar: `Impossible d'avoir des noms de champs en double`,
	DuplicateEmailError: 'Dupliquer un e-mail',
	DuplicateHeadingName: 'Section {name} existe déjà',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Une facture avec le même « numéro de facture » existe déjà.',
	DuplicateRecords: 'Enregistrements en double',
	DuplicateRecordsMinimumError: 'Au moins 2 enregistrements doivent être sélectionnés',
	DuplicateRecordsRequired: 'Sélectionnez au moins 1 enregistrement pour séparer',
	DuplicateServiceFailure: 'Échec de la duplication de <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Duplication réussie de <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Dossier dupliqué avec succès',
	DuplicateTemplateSuccess: 'Modèle dupliqué avec succès',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'Code DX',
	DxCodeSelectPlaceholder: 'Rechercher et ajouter des codes CIM-10',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Aucun',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>contient des ajustements</i>} other {}}',
	EarnReferralCredit: 'Gagner ${creditAmount}',
	Economist: 'Économiste',
	Edit: 'Modifier',
	EditArrangements: 'Modifier les arrangements',
	EditBillTo: 'Modifier la facture en',
	EditClient: 'Modifier le client',
	EditClientFileModalDescription: `Modifiez l'accès à ce fichier en choisissant les options dans les cases &quot;Visible par&quot;`,
	EditClientFileModalTitle: 'Modifier le fichier',
	EditClientNoteModalDescription:
		'Modifiez le contenu de la note. Utilisez la section « Visible par » pour modifier qui peut voir la note.',
	EditClientNoteModalTitle: 'Note éditée',
	EditConnectedAppButton: 'Modifier',
	EditConnections: 'Modifier les connexions{account, select, null { } undefined { } other { pour {account}}}',
	EditContactDetails: 'Modifier les coordonnées',
	EditContactFormIsClientLabel: 'Convertir en client',
	EditContactIsClientCheckboxWarning: `La conversion d'un contact en client est irréversible`,
	EditContactIsClientWanringModal:
		'La conversion de ce contact en client est irréversible. Cependant, toutes les relations demeureront et vous aurez désormais accès à leurs notes, fichiers et autres documents.',
	EditContactRelationship: 'Modifier une relation de contact',
	EditDetails: 'Modifier les détails',
	EditFileModalTitle: 'Modifier le fichier pour {name}',
	EditFolder: 'Modifier le dossier',
	EditFolderDescription: 'Renommer le dossier en...',
	EditInvoice: 'Modifier la facture',
	EditInvoiceDetails: 'Modifier les détails de la facture',
	EditLink: 'Modifier le lien',
	EditLocation: `Modifier l'emplacement`,
	EditLocationFailure: `Échec de la mise à jour de l'emplacement`,
	EditLocationSucess: 'Emplacement mis à jour avec succès',
	EditPaymentDetails: 'Modifier les détails de paiement',
	EditPaymentMethod: 'Modifier le mode de paiement',
	EditPersonalDetails: 'Modifier les informations personnelles',
	EditPractitioner: 'Modifier le praticien',
	EditProvider: 'Modifier le fournisseur',
	EditProviderDetails: 'Modifier les détails du fournisseur',
	EditRecurrence: 'Modifier la récurrence',
	EditRecurringAppointmentModalTitle: 'Modifier un rendez-vous récurrent',
	EditRecurringEventModalTitle: 'Modifier une réunion répétitive',
	EditRecurringReminderModalTitle: 'Modifier le rappel répétitif',
	EditRecurringTaskModalTitle: 'Modifier une tâche répétitive',
	EditRelationshipModalTitle: 'Modifier la relation',
	EditService: 'Modifier le service',
	EditServiceFailure: 'Échec de la mise à jour du nouveau service',
	EditServiceGroup: 'Modifier la collection',
	EditServiceGroupFailure: 'Échec de la mise à jour de la collection',
	EditServiceGroupSuccess: 'Collection mise à jour avec succès',
	EditServiceSuccess: 'Nouveau service mis à jour avec succès',
	EditStaffDetails: 'Modifier les détails du personnel',
	EditStaffDetailsCantUpdatedEmailTooltip: `Impossible de mettre à jour l'adresse e-mail. Veuillez créer un nouveau membre de l'équipe avec une nouvelle adresse e-mail.`,
	EditSubscriptionBilledQuantity: 'Quantité facturée',
	EditSubscriptionBilledQuantityValue: `membres de l'équipe {billedUsers}`,
	EditSubscriptionLimitedTimeOffer: 'Offre à durée limitée ! 50 % de réduction pendant 6 mois.',
	EditSubscriptionUpgradeAdjustTeamBanner: `Le coût de votre abonnement sera ajusté lors de l'ajout ou de la suppression de membres de l'équipe.`,
	EditSubscriptionUpgradeContent:
		'Votre compte sera immédiatement mis à jour avec le nouveau forfait et la nouvelle période de facturation. Tout changement de prix sera automatiquement facturé à votre méthode de paiement enregistrée ou crédité sur votre compte.',
	EditSubscriptionUpgradePlanTitle: `Mettre à niveau le plan d'abonnement`,
	EditSuperbillReceipt: 'Modifier la superbe facture',
	EditTags: `Étiquettes d'édition`,
	EditTemplate: 'Modifier le modèle',
	EditTemplateFolderSuccessMessage: 'Dossier de modèle modifié avec succès',
	EditValue: 'Modifier {value}',
	Edited: 'Édité',
	Editor: 'Éditeur',
	EditorAlertDescription: `Un format non pris en charge a été détecté. Rechargez l'application ou contactez notre équipe d'assistance.`,
	EditorAlertTitle: 'Nous rencontrons des difficultés pour afficher ce contenu',
	EditorPlaceholder:
		'Commencez à rédiger, choisissez un modèle ou ajoutez des blocs de base pour capturer les réponses de vos clients.',
	EditorTemplatePlaceholder: 'Commencez à écrire ou ajoutez des composants pour créer un modèle',
	EditorTemplateWithSlashCommandPlaceholder:
		'Commencez à écrire ou ajoutez des blocs de base pour capturer les réponses des clients. Utilisez les commandes slash (/) pour des actions rapides.',
	EditorWithSlashCommandPlaceholder:
		'Commencez à rédiger, choisissez un modèle ou ajoutez des blocs de base pour capturer les réponses des clients. Utilisez les commandes slash ( / ) pour des actions rapides.',
	EffectiveStartEndDate: 'Date de début et de fin effective',
	ElectricalEngineer: 'Ingénieur éléctricien',
	Electronic: 'Électronique',
	ElectronicSignature: 'Signature électronique',
	ElementarySchoolTeacher: `Professeur d'école primaire`,
	Eligibility: 'Éligibilité',
	Email: 'E-mail',
	EmailAlreadyExists: `l'adresse mail existe déjà`,
	EmailAndSms: 'E-mail ',
	EmailBody: `Corps de l'e-mail`,
	EmailContainsIgnoredDescription: `L'e-mail suivant contient {count, plural, one {un expéditeur} other {expéditrices}} e-mails actuellement ignorés. Voulez-vous continuer ?`,
	EmailInviteToPortalBody: `Bonjour {contactName},
Veuillez suivre ce lien pour vous connecter à votre portail client sécurisé et gérer facilement vos soins.

Cordialement,

{providerName}`,
	EmailInviteToPortalSubject: 'Bienvenue à {providerName}',
	EmailInvoice: 'Facture par courrier électronique',
	EmailInvoiceOverdueBody: `Bonjour {contactName}
Votre facture {invoiceNumber} est en retard.
Veuillez payer votre facture en ligne en utilisant le lien ci-dessous.

Si vous avez des questions, n'hésitez pas à nous contacter.

Merci,
{providerName}`,
	EmailInvoicePaidBody: `Bonjour {contactName}
Votre facture {invoiceNumber} est payée.
Pour consulter et télécharger une copie de votre facture, suivez le lien ci-dessous.

Si vous avez des questions, n'hésitez pas à nous contacter.

Merci,
{providerName}`,
	EmailInvoiceProcessingBody: `Bonjour {contactName}
Votre facture {invoiceNumber} est prête.
Suivez le lien ci-dessous pour consulter votre facture.

Si vous avez des questions, n'hésitez pas à nous contacter.

Merci,
{providerName}`,
	EmailInvoiceUnpaidBody: `Bonjour {contactName}
Votre facture {invoiceNumber} est prête, à payer au plus tard le {dueDate}.
Pour consulter et payer votre facture en ligne, suivez le lien ci-dessous.

Si vous avez des questions, n'hésitez pas à nous contacter.

Merci,
{providerName}`,
	EmailInvoiceVoidBody: `Bonjour {contactName}
Votre facture {invoiceNumber} est annulée.
Pour consulter cette facture, suivez le lien ci-dessous.

Si vous avez des questions, n'hésitez pas à nous contacter.

Merci,
{providerName}`,
	EmailNotFound: 'Email non trouvé',
	EmailNotVerifiedErrorCodeSnackbar: `Impossible d'effectuer l'action. Vous devez vérifier votre adresse e-mail.`,
	EmailNotVerifiedTitle: 'Vérifiez votre adresse e-mail',
	EmailSendClientIntakeBody: `Bonjour {contactName},
{providerName} vous demande de fournir des informations et de consulter des documents importants. Veuillez suivre le lien ci-dessous pour commencer.

Cordialement,

{providerName}`,
	EmailSendClientIntakeSubject: 'Bienvenue à {providerName}',
	EmailSuperbillReceipt: 'Envoyer une superbe facture par e-mail',
	EmailSuperbillReceiptBody: `Bonjour {contactName},
{providerName} vous a envoyé une copie de votre reçu de remboursement {date}.

Vous pouvez télécharger et soumettre ce document directement à votre compagnie d'assurance.`,
	EmailSuperbillReceiptFailure: `Échec de l'envoi du reçu Superbill`,
	EmailSuperbillReceiptSubject: '{providerName} a envoyé un relevé de remboursement reçu',
	EmailSuperbillReceiptSuccess: 'Reçu Superbill envoyé avec succès',
	EmailVerificationDescription: 'Nous <span>vérifions</span> votre compte maintenant',
	EmailVerificationNotification: 'Un e-mail de vérification a été envoyé à {email}',
	EmailVerificationSuccess: 'Votre adresse e-mail a été modifiée avec succès en {email}',
	Emails: 'E-mails',
	EmergencyContact: `Personne à contacter en cas d'urgence`,
	EmployeesIdentificationNumber: `Numéro d'identification des employés`,
	EmploymentStatus: `Statut d'emploi`,
	EmptyAgendaViewDescription: 'Aucun événement à afficher.<mark> Créer un rendez-vous maintenant</mark>',
	EmptyBin: 'Bac vide',
	EmptyBinConfirmationDescription:
		'Vider la corbeille supprimera toutes les **{total} conversations** dans Supprimés. Cette action est irréversible.',
	EmptyBinConfirmationTitle: 'Supprimer définitivement les conversations',
	EmptyTrash: 'Vider la corbeille',
	Enable: 'Activer',
	EnableCustomServiceAvailability: 'Activer la disponibilité du service',
	EnableCustomServiceAvailabilityDescription:
		'Par exemple, les premiers rendez-vous ne peuvent être réservés que tous les jours de 9h à 10h.',
	EndCall: `Fin d'appel`,
	EndCallConfirmationForCreator: `Vous y mettrez fin pour tout le monde car vous êtes l'initiateur de l'appel.`,
	EndCallConfirmationHasActiveAttendees: `Vous êtes sur le point de mettre fin à l'appel mais un ou plusieurs clients ont déjà rejoint l'appel. Voulez-vous également nous rejoindre ?`,
	EndCallForAll: `Mettre fin à l'appel pour tout le monde`,
	EndDate: 'Date de fin',
	EndDictation: 'Fin de la dictée',
	EndOfLine: 'Plus de rendez-vous',
	EndSession: 'Fin de la session',
	EndTranscription: 'Fin de la transcription',
	Ends: 'Fin',
	EndsOnDate: 'Se termine le {date}',
	Enrol: `S'inscrire`,
	EnrollmentRejectedSubject: 'Votre inscription avec {payerName} a été refusée',
	Enrolment: 'Admission',
	Enrolments: 'Inscriptions',
	EnrolmentsDescription: 'Afficher et gérer les inscriptions des fournisseurs auprès du payeur.',
	EnterAName: 'Entrez un nom...',
	EnterFieldLabel: 'Entrez le libellé du champ...',
	EnterPaymentDetailsDescription:
		'Le coût de votre abonnement sera automatiquement ajusté lors de l’ajout ou de la suppression d’utilisateurs.',
	EnterSectionName: 'Entrez le nom de la section...',
	EnterSubscriptionPaymentDetails: 'Entrez les détails du paiement',
	EnvironmentalScientist: `Spécialiste de l'environnement`,
	Epidemiologist: 'Épidémiologiste',
	Eraser: 'Gomme',
	Error: 'Erreur',
	ErrorBoundaryAction: 'Rafraîchir la page',
	ErrorBoundaryDescription: 'Veuillez actualiser la page et réessayer.',
	ErrorBoundaryTitle: `Oups ! Quelque chose s'est mal passé`,
	ErrorCallNotFound: `L'appel est introuvable. Il a peut-être expiré ou le créateur y a mis fin.`,
	ErrorCannotAccessCallUninvitedCode: `Désolé, il semble que vous n'ayez pas été invité à cet appel.`,
	ErrorFileUploadCustomMaxFileCount: 'Impossible de télécharger plus de {count} fichiers à la fois',
	ErrorFileUploadCustomMaxFileSize: 'La taille du fichier ne peut pas dépasser {mb} Mo',
	ErrorFileUploadInvalidFileType:
		'Type de fichier non valide pouvant contenir des virus potentiels et des logiciels nuisibles',
	ErrorFileUploadMaxFileCount: 'Impossible de télécharger plus de 150 fichiers à la fois',
	ErrorFileUploadMaxFileSize: 'La taille du fichier ne peut pas dépasser 100 Mo',
	ErrorFileUploadNoFileSelected: 'Veuillez sélectionner les fichiers à télécharger',
	ErrorInvalidNationalProviderId: `Le numéro d'identification du fournisseur national fourni n'est pas valide.`,
	ErrorInvalidPayerId: `L'identifiant du payeur fourni n'est pas valide`,
	ErrorInvalidTaxNumber: `Le numéro de taxe fourni n'est pas valide`,
	ErrorInviteExistingProviderStaffCode: `Cet utilisateur est déjà dans l'espace de travail.`,
	ErrorInviteStaffExistingUser: `Désolé, il semble que l'utilisateur que vous avez ajouté existe déjà dans notre système.`,
	ErrorOnlySingleCallAllowed: `Vous ne pouvez avoir qu'un seul appel actif à la fois.`,
	ErrorPayerNotFound: 'Payeur introuvable',
	ErrorProfilePhotoMaxFileSize: 'Échec du téléchargement ! Limite de taille de fichier atteinte : 5 Mo',
	ErrorRegisteredExistingUser: 'Désolé, il semble que vous soyez déjà inscrit.',
	ErrorUserSignInIncorrectCredentials: 'Email ou mot de passe invalide. Veuillez réessayer.',
	ErrorUserSigninGeneric: `Désolé, quelque chose s'est mal passé.`,
	ErrorUserSigninUserNotConfirmed:
		'Désolé, vous devez confirmer votre compte avant de vous connecter. Vérifiez votre boîte de réception pour obtenir des instructions.',
	Errors: 'Erreurs',
	EssentialPlanInclusionFive: 'Importation de modèle',
	EssentialPlanInclusionFour: '5 Go de stockage',
	EssentialPlanInclusionHeader: 'Tout en Gratuit  ',
	EssentialPlanInclusionOne: 'Rappels automatiques et personnalisés',
	EssentialPlanInclusionSix: 'Assistance prioritaire',
	EssentialPlanInclusionThree: 'Conversation vidéo',
	EssentialPlanInclusionTwo: 'Synchronisation bidirectionnelle du calendrier',
	EssentialSubscriptionPlanSubtitle: `Simplifiez votre pratique avec l'essentiel`,
	EssentialSubscriptionPlanTitle: 'Essentiel',
	Esthetician: 'Esthéticienne',
	Estheticians: 'Esthéticiennes',
	EstimatedArrivalDate: 'Arrivée prévue dans {numberOfDaysFromNow} jours',
	Ethnicity: 'Origine ethnique',
	Europe: `L'Europe `,
	EventColor: 'Couleur de la réunion',
	EventName: `Nom de l'événement`,
	EventType: `Type d'événement`,
	Every: 'Chaque',
	Every2Weeks: 'Toutes les 2 semaines',
	EveryoneInWorkspace: `Tout le monde dans l'espace de travail`,
	ExercisePhysiologist: `Physiologiste de l'exercice`,
	Existing: 'Existant',
	ExistingClients: 'Clients existants',
	ExistingFolders: 'Dossiers existants',
	ExpiredPromotionCode: 'Le code promotionnel a expiré',
	ExpiredReferralDescription: 'Le parrainage a expiré',
	ExpiredVerificationLink: 'Lien de vérification expiré',
	ExpiredVerificationLinkDescription: `Nous sommes désolés, mais le lien de vérification sur lequel vous avez cliqué a expiré. Cela peut se produire si vous avez attendu plus de 24 heures pour cliquer sur le lien ou si vous avez déjà utilisé le lien pour vérifier votre adresse e-mail.

 Veuillez demander un nouveau lien de vérification pour vérifier votre adresse e-mail.`,
	ExpiryDateRequired: `La date d'expiration est requise`,
	ExploreFeature: 'Qu’aimeriez-vous explorer en premier ?',
	ExploreOptions: 'Choisissez une ou plusieurs options à explorer...',
	Export: 'Exporter',
	ExportAppointments: 'Exporter les rendez-vous',
	ExportClaims: 'Exporter les réclamations',
	ExportClaimsFilename: 'Réclamations {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Votre fichier n’a pas pu être téléchargé en raison d’une erreur.',
	ExportClientsDownloadFailureSnackbarTitle: 'Échec du téléchargement',
	ExportClientsFailureSnackbarDescription: `Veuillez réessayer plus tard ou contacter l'assistance pour obtenir de l'aide.`,
	ExportClientsFailureSnackbarTitle: `Échec de l'exportation`,
	ExportClientsModalDescription: `Ce processus d'exportation de données peut prendre quelques minutes en fonction de la quantité de données exportées. Vous recevrez une notification par e-mail avec un lien une fois qu'il sera prêt à être téléchargé.

 Souhaitez-vous procéder à l’export des données clients ?`,
	ExportClientsModalTitle: 'Exporter les données clients',
	ExportCms1500: 'Exporter CMS1500',
	ExportContactFailedNotificationSubject: 'Votre exportation de données a échoué',
	ExportFailed: `Échec de l'exportation`,
	ExportGuide: `Guide d'exportation`,
	ExportInvoiceFileName: 'Transactions {fromDate}-{toDate}.csv',
	ExportPayments: 'Exporter les paiements',
	ExportPaymentsFilename: 'Paiements {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Votre document est prêt à être téléchargé.',
	ExportPrintWaitMessage: 'Préparation de votre document. Veuillez patienter...',
	ExportTextOnly: 'Exporter uniquement le texte',
	ExportTransactions: `Opérations d'exportation`,
	Exporting: 'Exportation',
	ExportingData: 'Exporter des données',
	ExtendedFamilyMember: 'Membre de la famille élargie',
	External: 'Externe',
	ExternalEventInfoBanner: `Ce rendez-vous provient d'un calendrier synchronisé et peut manquer d'éléments.`,
	ExtraLarge: 'Très grand',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Échoué',
	FailedToJoinTheMeeting: 'Impossible de rejoindre la réunion.',
	FallbackPageDescription: `Il semble que cette page n'existe pas. Vous devrez peut-être {refreshButton} cette page pour obtenir les dernières modifications.
Sinon, veuillez contacter le support Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'rafraîchir',
	FallbackPageTitle: 'Oops...',
	FamilyPlanningService: 'Service de planification familiale',
	FashionDesigner: 'Styliste modéliste',
	FastTrackInvoicingAndBilling: 'Accélérez votre facturation et votre facturation',
	Father: 'Père',
	FatherInLaw: 'Beau-père',
	Favorite: 'Favoris',
	FeatureBannerCalendarTile1ActionLabel: 'Réservation en ligne • 2 min',
	FeatureBannerCalendarTile1Description:
		'Envoyez simplement un e-mail, envoyez un SMS ou ajoutez des disponibilités à votre site Web',
	FeatureBannerCalendarTile1Title: 'Permettez à vos clients de réserver en ligne',
	FeatureBannerCalendarTile2ActionLabel: 'Automatiser les rappels • 2 minutes',
	FeatureBannerCalendarTile2Description: 'Augmentez la fréquentation des clients avec des rappels automatisés',
	FeatureBannerCalendarTile2Title: 'Réduisez les absences',
	FeatureBannerCalendarTile3Title: 'Planification et flux de travail',
	FeatureBannerCalendarTitle: 'Facilitez la planification',
	FeatureBannerCallsTile1ActionLabel: 'Démarrer un appel de télésanté',
	FeatureBannerCallsTile1Description:
		'Accès client avec juste un lien. Pas de connexions, de mots de passe ou de tracas',
	FeatureBannerCallsTile1Title: `Démarrez un appel vidéo de n'importe où`,
	FeatureBannerCallsTile2ActionLabel: 'Connecter des applications • 4 minutes',
	FeatureBannerCallsTile2Description: `Connectez en toute transparence d'autres fournisseurs de télésanté préférés`,
	FeatureBannerCallsTile2Title: 'Connectez vos applications de télésanté',
	FeatureBannerCallsTile3Title: 'Appels',
	FeatureBannerCallsTitle: `Connectez-vous avec vos clients — n'importe où, n'importe quand`,
	FeatureBannerClientsTile1ActionLabel: 'Importer maintenant • 2 minutes',
	FeatureBannerClientsTile1Description: 'Démarrez rapidement avec notre outil automatisé d’importation de clients',
	FeatureBannerClientsTile1Title: 'Vous avez beaucoup de clients ?',
	FeatureBannerClientsTile2ActionLabel: `Personnaliser l'admission • 2 minutes`,
	FeatureBannerClientsTile2Description: 'Supprimez les formalités d’admission et améliorez l’expérience client',
	FeatureBannerClientsTile2Title: 'Passez au sans papier',
	FeatureBannerClientsTile3Title: 'Portail client',
	FeatureBannerClientsTitle: 'Tout commence avec vos clients',
	FeatureBannerHeader: 'Par la Communauté, pour la Communauté !',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatiser les paiements • 2 minutes',
	FeatureBannerInvoicesTile1Description: 'Évitez les conversations gênantes grâce aux paiements automatisés',
	FeatureBannerInvoicesTile1Title: 'Soyez payé 2 fois plus vite',
	FeatureBannerInvoicesTile2ActionLabel: 'Suivre les flux de trésorerie • 2 minutes',
	FeatureBannerInvoicesTile2Description: 'Réduisez les factures impayées et gardez un œil sur vos revenus',
	FeatureBannerInvoicesTile2Title: 'Suivez vos revenus, sans douleur',
	FeatureBannerInvoicesTile3Title: 'Facturation et paiements',
	FeatureBannerInvoicesTitle: `Une chose de moins à s'inquiéter`,
	FeatureBannerSubheader:
		'Modèles Carepatron créés par notre équipe et notre communauté. Essayez de nouvelles ressources ou partagez les vôtres !',
	FeatureBannerTeamTile1ActionLabel: 'Inviter maintenant',
	FeatureBannerTeamTile1Description:
		'Invitez les membres de votre équipe sur votre compte et facilitez la collaboration',
	FeatureBannerTeamTile1Title: 'Rassemblez votre équipe',
	FeatureBannerTeamTile2ActionLabel: 'Définir la disponibilité • 2 minutes',
	FeatureBannerTeamTile2Description: 'Gérez la disponibilité de vos équipes pour éviter les doubles réservations',
	FeatureBannerTeamTile2Title: 'Définissez votre disponibilité',
	FeatureBannerTeamTile3ActionLabel: 'Définir les autorisations • 2 minutes',
	FeatureBannerTeamTile3Description: 'Contrôler l’accès aux données sensibles et aux outils de conformité',
	FeatureBannerTeamTile3Title: `Personnaliser les autorisations et l'accès`,
	FeatureBannerTeamTitle: `Rien de grand ne s'accomplit seul`,
	FeatureBannerTemplatesTile1ActionLabel: 'Explorer la bibliothèque • 2 minutes',
	FeatureBannerTemplatesTile1Description:
		'Choisissez parmi une incroyable bibliothèque de ressources personnalisables ',
	FeatureBannerTemplatesTile1Title: 'Réduisez votre charge de travail',
	FeatureBannerTemplatesTile2ActionLabel: 'Envoyer maintenant • 2 minutes',
	FeatureBannerTemplatesTile2Description: `Envoyez de superbes modèles aux clients pour qu'ils les complètent`,
	FeatureBannerTemplatesTile2Title: 'Rendre la documentation amusante',
	FeatureBannerTemplatesTile3Title: 'Modèles',
	FeatureBannerTemplatesTitle: 'Des modèles pour absolument tout',
	FeatureLimitBannerDescription:
		'Passez à la version supérieure dès maintenant pour continuer à créer et gérer {featureName} sans interruption et profiter au maximum de Carepatron !',
	FeatureLimitBannerTitle: 'Vous êtes {percentage}% vers votre limite {featureName}',
	FeatureRequiresUpgrade: 'Cette fonctionnalité nécessite une mise à niveau',
	Fee: 'Frais',
	Female: 'Femelle',
	FieldLabelTooltip: '{isHidden, select, true {Afficher} other {Masquer}} champ de libellé',
	FieldName: 'Nom de domaine',
	FieldOptionsFirstPart: 'Premier mot',
	FieldOptionsMiddlePart: 'Mots du milieu',
	FieldOptionsSecondPart: 'Dernier mot',
	FieldOptionsWholeField: 'Champ entier',
	FieldType: 'Type de champ',
	Fields: 'Des champs',
	File: 'Déposer',
	FileDownloaded: '<strong>{fileName}</strong> téléchargé',
	FileInvalidType: 'Fichier non pris en charge.',
	FileNotFound: 'Fichier non trouvé',
	FileNotFoundDescription: `Vous n'avez plus accès à ce fichier ou il est introuvable`,
	FileTags: 'Balises de fichier',
	FileTagsHelper: 'Les balises seront appliquées à tous les fichiers',
	FileTooLarge: 'Fichier trop volumineux.',
	FileTooSmall: 'Fichier trop petit.',
	FileUploadComplete: 'Complet',
	FileUploadFailed: 'Échoué',
	FileUploadInProgress: 'Chargement',
	FileUploadedNotificationSubject: '{actorProfileName} a téléchargé un fichier',
	Files: 'Des dossiers',
	FillOut: 'Remplir',
	Filter: 'Filtre',
	FilterBy: 'Filtrer par',
	FilterByAmount: 'Filtrer par montant',
	FilterByClient: 'Filtrer par client',
	FilterByLocation: 'Filtrer par emplacement',
	FilterByService: 'Filtrer par prestation',
	FilterByStatus: 'Filtrer par statut',
	FilterByTags: 'Filtrer par balises',
	FilterByTeam: 'Filtrer par équipe',
	Filters: 'Filtres',
	FiltersAppliedToView: 'Filtres appliqués à la vue',
	FinalAppointment: 'Dernier rendez-vous',
	FinalizeImport: `Finaliser l'importation`,
	FinancialAnalyst: 'Analyste financier',
	Finish: 'Finition',
	Firefighter: 'Sapeur pompier',
	FirstName: 'Prénom',
	FirstNameLastInitial: 'Prénom, initiale du nom de famille',
	FirstPerson: '1ère personne',
	FolderName: 'Nom du dossier',
	Folders: 'Dossiers',
	FontFamily: 'Famille de polices',
	ForClients: 'Pour les clients',
	ForClientsDetails: 'Je reçois des soins ou des services liés à la santé',
	ForPractitioners: 'Pour les praticiens',
	ForPractitionersDetails: 'Gérez et développez votre cabinet',
	ForgotPasswordConfirmAccessCode: 'Code de confirmation',
	ForgotPasswordConfirmNewPassword: 'Nouveau mot de passe',
	ForgotPasswordConfirmPageDescription:
		'Veuillez saisir votre adresse e-mail, un nouveau mot de passe et le code de confirmation que nous venons de vous envoyer.',
	ForgotPasswordConfirmPageTitle: 'Réinitialiser le mot de passe',
	ForgotPasswordPageButton: 'Envoyer le lien de réinitialisation',
	ForgotPasswordPageDescription:
		'Entrez votre email et nous vous enverrons un lien pour réinitialiser votre mot de passe.',
	ForgotPasswordPageTitle: 'Mot de passe oublié',
	ForgotPasswordSuccessPageDescription: 'Vérifiez votre boîte de réception pour votre lien de réinitialisation.',
	ForgotPasswordSuccessPageTitle: 'Lien de réinitialisation envoyé !',
	Form: 'Formulaire',
	FormAnswersSentToEmailNotification: 'Nous avons envoyé une copie de vos réponses à',
	FormBlocks: 'Blocs de formulaire',
	FormFieldAddOption: 'Ajouter une option',
	FormFieldAddOtherOption: 'Ajouter &quot;autre&quot;',
	FormFieldOptionPlaceholder: 'Option {index}',
	FormStructures: 'Structures de formulaire',
	Format: 'Format',
	FormatLinkButtonColor: 'Couleur du bouton',
	Forms: 'Formes',
	FormsAndAgreementsValidationMessage:
		'Tous les formulaires et accords doivent être remplis pour poursuivre le processus d’admission.',
	FormsCategoryDescription: `Pour la collecte et l'organisation des détails des patients`,
	Frankfurt: 'Francfort',
	Free: 'Gratuit',
	FreePlanInclusionFive: 'Facturation automatisée ',
	FreePlanInclusionFour: 'Portail client',
	FreePlanInclusionHeader: 'Commencer avec',
	FreePlanInclusionOne: 'Clients illimités',
	FreePlanInclusionSix: 'Assistance en direct',
	FreePlanInclusionThree: '1 Go de stockage',
	FreePlanInclusionTwo: 'Télésanté',
	FreeSubscriptionPlanSubtitle: 'Gratuit pour tous',
	FreeSubscriptionPlanTitle: 'Gratuit',
	Friday: 'Vendredi',
	From: 'Depuis',
	FullName: 'Nom complet',
	FunctionalMedicineOrNaturopath: 'Médecine Fonctionnelle ou Naturopathe',
	FuturePaymentsAuthoriseProvider: `Autoriser {provider} à utiliser le paiement enregistré à l'avenir`,
	FuturePaymentsSavePaymentMethod: 'Enregistrer {paymentMethod} pour les paiements futurs',
	GST: 'TPS',
	Gender: 'Genre',
	GeneralAvailability: 'Disponibilité générale',
	GeneralAvailabilityDescription:
		'Définissez quand vous êtes régulièrement disponible. Les clients ne pourront réserver vos services que pendant les heures disponibles.',
	GeneralAvailabilityDescription2:
		'Créez des horaires en fonction de votre disponibilité et des offres de services souhaitées à des heures spécifiques pour déterminer la disponibilité de votre réservation en ligne.',
	GeneralAvailabilityInfo: 'Vos heures disponibles détermineront votre disponibilité de réservation en ligne',
	GeneralAvailabilityInfo2:
		'Les services proposant des événements de groupe devraient utiliser un nouvel horaire afin de réduire les heures disponibles pouvant être réservées par les clients en ligne.',
	GeneralHoursPlural: '{count} {count, plural, one {heure} other {heures}}',
	GeneralPractitioner: 'Médecin généraliste',
	GeneralPractitioners: 'Les médecins généralistes',
	GeneralServiceAvailabilityInfo: `Ce calendrier remplacera le comportement des membres de l'équipe affectés`,
	Generate: 'Générer',
	GenerateBillingItemsBannerContent:
		'Les éléments de facturation ne sont pas créés automatiquement pour les rendez-vous récurrents.',
	GenerateItems: 'Générer des éléments',
	GenerateNote: 'Générer une note',
	GenerateNoteConfirmationModalDescription:
		'Que souhaitez-vous faire ? Créer une nouvelle note générée, ajouter à une note existante ou remplacer son contenu ?',
	GenerateNoteFor: 'Générer une note pour',
	GeneratingContent: 'Générer du contenu...',
	GeneratingNote: 'Génération de votre note...',
	GeneratingTranscript: 'Génération de transcription',
	GeneratingTranscriptDescription: 'Le traitement peut prendre quelques minutes.',
	GeneratingYourTranscript: 'Générer votre transcription',
	GenericErrorDescription: `{module} n'a pas pu être chargé. Veuillez réessayer plus tard.`,
	GenericErrorTitle: 'Erreur inattendue',
	GenericFailureSnackbar: `Désolé, quelque chose d'inattendu s'est produit. Veuillez actualiser la page et réessayer.`,
	GenericSavedSuccessSnackbar: 'Succès! Changements sauvegardés',
	GeneticCounselor: 'Conseiller en génétique',
	Gerontologist: 'Gérontologue',
	Get50PercentOff: 'Obtenez 50% de réduction !',
	GetHelp: `Obtenir de l'aide`,
	GetStarted: 'Commencer',
	GettingStartedAppointmentTypes: 'Créer des types de rendez-vous',
	GettingStartedAppointmentTypesDescription:
		'Rationalisez votre planification et votre facturation en personnalisant vos services, vos prix et vos codes de facturation',
	GettingStartedAppointmentTypesTitle: 'Calendrier ',
	GettingStartedClients: 'Ajoutez vos clients',
	GettingStartedClientsDescription:
		'Soyez opérationnel avec les clients pour les futurs rendez-vous, notes et paiements',
	GettingStartedClientsTitle: 'Tout commence avec les clients',
	GettingStartedCreateClient: 'Créer un client',
	GettingStartedImportClients: 'Importer des clients',
	GettingStartedInvoices: 'Facturez comme un pro',
	GettingStartedInvoicesDescription: `C'est simple de créer des factures professionnelles.
 Ajoutez votre logo, votre emplacement et vos conditions de paiement`,
	GettingStartedInvoicesTitle: 'Faites de votre mieux',
	GettingStartedMobileApp: `Obtenez l'application mobile`,
	GettingStartedMobileAppDescription:
		'Vous pouvez télécharger Carepatron sur votre appareil iOS, Android ou de bureau pour un accès facile lors de vos déplacements.',
	GettingStartedMobileAppTitle: `Travaillez de n'importe où`,
	GettingStartedNavItem: 'Commencer',
	GettingStartedPageTitle: 'Premiers pas sur Carepatron',
	GettingStartedPayments: 'Acceptez les paiements en ligne',
	GettingStartedPaymentsDescription: `Soyez payé plus rapidement en permettant à vos clients de payer en ligne.
 Visualisez toutes vos factures et paiements en un seul endroit`,
	GettingStartedPaymentsTitle: 'Facilitez les paiements',
	GettingStartedSaveBranding: 'Enregistrer la marque',
	GettingStartedSyncCalendars: `Synchroniser d'autres calendriers`,
	GettingStartedSyncCalendarsDescription:
		'Carepatron vérifie votre calendrier pour déceler les conflits, de sorte que les rendez-vous ne sont programmés que lorsque vous êtes disponible',
	GettingStartedSyncCalendarsTitle: 'Restez toujours à jour',
	GettingStartedVideo: `Regarder une vidéo d'introduction`,
	GettingStartedVideoDescription:
		'Les premiers espaces de travail de santé tout-en-un pour les petites équipes et leurs clients',
	GettingStartedVideoTitle: 'Bienvenue chez Carepatron',
	GetttingStartedGetMobileDownload: `Téléchargez l'application`,
	GetttingStartedGetMobileNoDownload: `Non compatible avec ce navigateur. Si vous utilisez un iPhone ou un iPad, veuillez ouvrir cette page dans Safari. Sinon, essayez de l'ouvrir dans Chrome.`,
	Glossary: 'Glossaire',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning: `Gmail ne permet d'envoyer que 500 messages par jour depuis votre compte. Certains messages peuvent échouer. Voulez-vous continuer ?`,
	GoToAppointment: 'Aller au rendez-vous',
	GoToApps: 'Accéder aux applications',
	GoToAvailability: 'Aller à la disponibilité',
	GoToClientList: 'Accéder à la liste des clients',
	GoToClientRecord: 'Aller à la fiche client',
	GoToClientSettings: 'Accédez maintenant aux paramètres du client',
	GoToInvoiceTemplates: 'Accédez aux modèles de facture',
	GoToNotificationSettings: 'Accéder aux paramètres de notification',
	GoToPaymentSettings: 'Accédez aux paramètres de paiement',
	Google: 'Google',
	GoogleCalendar: 'Google Agenda',
	GoogleColor: 'Couleur du calendrier Google',
	GoogleMeet: 'Google Rencontre',
	GoogleTagManagerContainerId: 'ID du conteneur Google Tag Manager',
	GotIt: `J'ai compris!`,
	Goto: 'Aller à',
	Granddaughter: 'Petite fille',
	Grandfather: 'Grand-père',
	Grandmother: 'Grand-mère',
	Grandparent: 'Grand-parent',
	Grandson: 'Petit fils',
	GrantPortalAccess: `Accorder l'accès au portail`,
	GraphicDesigner: 'Designer graphique',
	Grid: 'Grille',
	GridView: 'Vue grille',
	Group: 'Groupe',
	GroupBy: 'Grouper selon',
	GroupEvent: 'Événement de groupe',
	GroupEventHelper: 'Définir une limite de participants pour le service',
	GroupFilterLabel: 'Tous les {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'ID de groupe',
	GroupInputFieldsFormPrimaryText: 'Champs de saisie de groupe',
	GroupInputFieldsFormSecondaryText: 'Choisir ou ajouter des champs personnalisés',
	GuideTo: 'Guide à {value}',
	GuideToImproveVideoQuality: 'Guide pour améliorer la qualité vidéo',
	GuideToManagingPayers: 'Gestion des payeurs',
	GuideToSubscriptionsBilling: 'Guide de facturation des abonnements',
	GuideToTroubleshooting: 'Guide de dépannage',
	Guidelines: 'Directives',
	GuidelinesCategoryDescription: 'Pour guider la prise de décision clinique',
	HST: 'TVA',
	HairStylist: 'Coiffeur',
	HaveBeenWaiting: 'Tu attends depuis longtemps',
	HeHim: 'Il/Lui',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Calendrier',
	HeaderCalls: 'Appels',
	HeaderClientAppAccountSettings: 'Paramètres du compte',
	HeaderClientAppCalls: 'Appels',
	HeaderClientAppMyDocumentation: 'Documentation',
	HeaderClientAppMyRelationships: 'Mes relations',
	HeaderClients: 'Clientèle',
	HeaderHelp: 'Aide',
	HeaderMoreOptions: `Plus d'options`,
	HeaderStaff: 'Personnel',
	HealthCoach: 'Coach santé',
	HealthCoaches: 'Coachs de santé',
	HealthEducator: 'Éducateur de santé',
	HealthInformationTechnician: 'Technicien en information sur la santé',
	HealthPolicyExpert: 'Expert en politiques de santé',
	HealthServicesAdministrator: 'Administrateur des services de santé',
	HelpArticles: `Articles d'aide`,
	HiddenColumns: 'Colonnes masquées',
	HiddenFields: 'Champs cachés',
	HiddenSections: 'Sections masquées',
	HiddenSectionsAndFields: 'Sections/champs cachés',
	HideColumn: 'Masquer la colonne',
	HideColumnButton: 'Masquer la colonne {value}',
	HideDetails: 'Cacher les détails',
	HideField: 'Masquer le champ',
	HideFullAddress: 'Cacher',
	HideMenu: 'Masquer le menu',
	HideMergeSummarySidebar: 'Masquer le résumé de la fusion',
	HideSection: 'Section cachée',
	HideYourView: 'Cachez votre vue',
	Highlight: 'Couleur de surbrillance',
	Highlighter: 'Surligneur',
	History: 'Histoire',
	HistoryItemFooter: '{actors, select, undefined {{date} à {time}} other {Par {actors} • {date} à {time}}}',
	HistorySidePanelEmptyState: 'Aucun historique trouvé',
	HistoryTitle: `Journal d'activité`,
	HolisticHealthPractitioner: 'Praticien en santé holistique',
	HomeCaregiver: 'Aide-soignant à domicile',
	HomeHealthAide: 'Aide à domicile',
	HomelessShelter: 'Refuge pour sans-abri',
	HourAbbreviation: '{count} {count, plural, one {h} other {h}}',
	Hourly: 'Horaire',
	HoursPlural: '{age, plural, one {# heure} other {# heures}}',
	HowCanWeImprove: 'Comment pouvons-nous améliorer cela ?',
	HowCanWeImproveResponse: 'Comment pouvons-nous améliorer cette réponse ?',
	HowDidWeDo: 'Comment avons-nous fait ?',
	HowDoesReferralWork: 'Guide du programme de parrainage',
	HowToUseAiSummarise: 'Comment utiliser AI Summarize',
	HumanResourcesManager: 'Directeur des Ressources Humaines',
	Husband: 'Mari',
	Hypnotherapist: 'Hypnothérapeute',
	IVA: 'TVA',
	IgnoreNotification: 'Ignorer la notification',
	IgnoreOnce: 'Ignorer une fois',
	IgnoreSender: 'Ignorer l’expéditeur',
	IgnoreSenderDescription:
		'Ignorer les expéditeurs pour ne plus recevoir de notifications de messages de leur part. Vous pouvez toujours consulter les messages dans votre boîte de réception.',
	IgnoreSenders: 'Ignorer les expéditeurs',
	IgnoreSendersSuccess: 'Adresse e-mail ignorée <mark>{addresses}</mark>',
	Ignored: 'Ignoré',
	Image: 'Image',
	Import: 'Importer',
	ImportActivity: `Importer l'activité`,
	ImportClientSuccessSnackbarDescription: 'Votre fichier a été importé avec succès',
	ImportClientSuccessSnackbarTitle: 'Importation réussie !',
	ImportClients: 'Importer des clients',
	ImportClientsFailureSnackbarDescription: `Votre fichier n'a pas pu être importé correctement en raison d'une erreur.`,
	ImportClientsFailureSnackbarTitle: `Échec de l'importation !`,
	ImportClientsGuide: `Guide d'importation des clients`,
	ImportClientsInProgressSnackbarDescription: 'Cela ne devrait prendre qu’une minute.',
	ImportClientsInProgressSnackbarTitle: 'Importation de {fileName}',
	ImportClientsModalDescription: `Choisissez d'où proviennent vos données, qu'il s'agisse d'un fichier sur votre appareil, d'un service tiers ou d'une autre plate-forme logicielle.`,
	ImportClientsModalFileUploadHelperText: 'Prend en charge {fileTypes}. Limite de taille {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Guide pour importer des données clients',
	ImportClientsModalStep1Label: 'Choisir la source de données',
	ImportClientsModalStep2Label: 'Téléverser un fichier',
	ImportClientsModalStep3Label: 'Champs de révision',
	ImportClientsModalTitle: 'Importer vos données clients',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {client} other {clients}} prêt(e)s à importer',
	ImportContactFailedNotificationSubject: `L'importation de vos données a échoué`,
	ImportDataSourceSelectorLabel: 'Importer une source de données depuis',
	ImportDataSourceSelectorPlaceholder: `Rechercher ou choisir une source de données d'importation`,
	ImportExportButton: 'Importer / Exporter',
	ImportFailed: `Échec de l'importation`,
	ImportFromAnotherPlatformTileDescription:
		'Téléchargez une exportation de vos fichiers client et téléchargez-les ici.',
	ImportFromAnotherPlatformTileLabel: 'Importer depuis une autre plateforme',
	ImportGuide: `Guide d'importation`,
	ImportInProgress: 'Importation en cours',
	ImportProcessing: 'Importation en cours...',
	ImportSpreadsheetDescription:
		'Vous pouvez importer votre liste de clients existante dans Carepatron en téléchargeant une feuille de calcul avec des données tabulaires, telles que .CSV, .XLS ou .XLSX.',
	ImportSpreadsheetTitle: 'Importez votre fichier de feuille de calcul',
	ImportTemplates: 'Importer des modèles',
	Importing: 'Importation',
	ImportingCalendarProductEvents: 'Importation des événements {product}',
	ImportingData: 'Importation de données',
	ImportingSpreadsheetDescription: `Cela ne devrait prendre qu'une minute.`,
	ImportingSpreadsheetTitle: 'Importer votre feuille de calcul',
	ImportsInProgress: 'Importations en cours',
	InPersonMeeting: 'Réunion en personne',
	InProgress: 'En cours',
	InTransit: 'En transit',
	InTransitTooltip:
		'Le solde en transit comprend tous les paiements de factures payées de Stripe vers votre compte bancaire. Ces fonds prennent généralement 3 à 5 jours pour être réglés.',
	Inactive: 'Inactif',
	InboundOrOutboundCalls: 'Appels entrants ou sortants',
	Inbox: 'Boîte de réception',
	InboxAccessRestricted:
		'Accès Restreint. Veuillez contacter le propriétaire de la boîte de réception pour obtenir les autorisations.',
	InboxAccountAlreadyConnected: 'La chaîne que vous avez tenté de connecter est déjà connectée à Carepatron',
	InboxAddAttachments: 'Ajouter des pièces jointes',
	InboxAreYouSureDeleteMessage: 'Etes-vous sûr de vouloir supprimer ce message ?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Conversation fermée avec succès #} other {Conversations fermées avec succès #}}',
	InboxBulkComposeModalTitle: 'Composer message groupé',
	InboxBulkDeleteSuccess:
		'{count, plural, one {# conversation supprimée avec succès} other {# conversations supprimées avec succès}}',
	InboxBulkReadSuccess:
		'{count, plural, one {# conversation marquée comme lue avec succès} other {# conversations marquées comme lues avec succès}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Conversation # rouverte avec succès} other {Conversations # rouvertes avec succès}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Marqué # conversation comme non lue avec succès} other {Marqué # conversations comme non lues avec succès}}',
	InboxChatCreateGroup: 'Créer un groupe',
	InboxChatDeleteGroupModalDescription:
		'Êtes-vous sûr de vouloir supprimer ce groupe ? Tous les messages et pièces jointes seront supprimés.',
	InboxChatDeleteGroupModalTitle: 'Supprimer le groupe',
	InboxChatDiscardDraft: 'Supprimer le brouillon',
	InboxChatDragDropText: 'Déposez les fichiers ici pour les télécharger',
	InboxChatGroupConversation: 'Conversation de groupe',
	InboxChatGroupCreateModalDescription:
		'Créez un nouveau groupe pour échanger des messages et collaborer avec votre équipe, vos clients ou votre communauté.',
	InboxChatGroupCreateModalTitle: 'Créer un groupe',
	InboxChatGroupMembers: 'Membres du groupe',
	InboxChatGroupModalGroupNameFieldLabel: 'Nom du groupe',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Par exemple, assistance clientèle, administrateur',
	InboxChatGroupModalGroupNameFieldRequired: 'Ce champ est obligatoire',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimum un membre requis',
	InboxChatGroupModalMembersFieldLabel: 'Choisissez les membres du groupe',
	InboxChatGroupModalMembersFieldPlaceholder: `Choisissez les membres de l'équipe à ajouter à ce groupe`,
	InboxChatGroupUpdateModalTitle: 'Gérer le groupe',
	InboxChatLeaveGroup: 'Quitter le groupe',
	InboxChatLeaveGroupModalDescription:
		'Êtes-vous sûr de vouloir quitter ce groupe&nbsp;? Vous ne recevrez plus de messages ni de mises à jour.',
	InboxChatLeaveGroupModalTitle: 'Quitter le groupe',
	InboxChatLeftGroupMessage: 'Message de groupe à gauche',
	InboxChatManageGroup: 'Gérer le groupe',
	InboxChatSearchParticipants: 'Choisissez les destinataires',
	InboxCloseConversationSuccess: 'Conversation terminée avec succès',
	InboxCompose: 'Composer',
	InboxComposeBulk: 'Message en masse',
	InboxComposeCarepatronChat: 'Messager',
	InboxComposeChat: 'Composer un chat',
	InboxComposeDisabledNoConnection: 'Connectez un compte e-mail pour envoyer des messages.',
	InboxComposeDisabledNoPermissionTooltip: `Vous n'avez pas la permission d'envoyer des messages depuis cette boîte de réception.`,
	InboxComposeEmail: 'Écrire un email',
	InboxComposeMessageFrom: 'Depuis',
	InboxComposeMessageRecipientBcc: 'Cci',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'À',
	InboxComposeMessageSubject: 'Sujet:',
	InboxConnectAccountButton: 'Connectez votre email',
	InboxConnectedDescription: `Votre boîte de réception n'a aucune communication`,
	InboxConnectedHeading: 'Vos conversations apparaîtront ici dès que vous commencerez à échanger des communications',
	InboxConnectedHeadingClientView: 'Rationalisez vos communications avec vos clients',
	InboxCreateFirstInboxButton: 'Créez votre première boîte de réception',
	InboxCreationSuccess: 'Boîte de réception créée avec succès',
	InboxDeleteAttachment: 'Supprimer la pièce jointe',
	InboxDeleteConversationSuccess: 'Conversation supprimée avec succès',
	InboxDeleteMessage: 'Supprimer le message ?',
	InboxDirectMessage: 'Message direct',
	InboxEditDraft: 'Modifier le brouillon',
	InboxEmailComposeReplyEmail: 'Rédiger la réponse',
	InboxEmailDraft: 'Brouillon',
	InboxEmailNotFound: 'Email non trouvé',
	InboxEmailSubjectFieldInformation: `Changer la ligne d'objet créera un nouveau fil de discussion.`,
	InboxEmptyArchiveDescription: `Aucune conversation archivée n'a été trouvée`,
	InboxEmptyBinDescription: `Aucune conversation supprimée n'a été trouvée`,
	InboxEmptyBinHeading: 'Tout est clair, rien à voir ici',
	InboxEmptyBinSuccess: 'Conversations supprimées avec succès',
	InboxEmptyCongratsHeading: `Bon travail ! Asseyez-vous et détendez-vous jusqu'à la prochaine conversation`,
	InboxEmptyDraftDescription: `Aucun brouillon de conversation n'a été trouvé`,
	InboxEmptyDraftHeading: 'Tout est clair, rien à voir ici',
	InboxEmptyOtherDescription: `Aucune conversation n'a été trouvée`,
	InboxEmptyScheduledHeading: `Tout est clair, aucune conversation n'est prévue pour l'envoi`,
	InboxEmptySentDescription: `Aucune conversation envoyée n'a été trouvée`,
	InboxForward: 'Avant',
	InboxGroupClientsLabel: 'Tous les clients',
	InboxGroupClientsOverviewLabel: 'Clients',
	InboxGroupClientsSelectedItemPrefix: 'Client',
	InboxGroupStaffsLabel: `Toute l'équipe`,
	InboxGroupStaffsOverviewLabel: 'Équipe',
	InboxGroupStaffsSelectedItemPrefix: 'Équipe',
	InboxGroupStatusLabel: 'Tous les statuts',
	InboxGroupStatusOverviewLabel: 'Envoyer à un statut',
	InboxGroupStatusSelectedItemPrefix: 'Statut',
	InboxGroupTagsLabel: 'Tous les tags',
	InboxGroupTagsOverviewLabel: 'Envoyer à un tag',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Cacher le texte cité',
	InboxIgnoreConversationSuccess: 'Conversation ignorée avec succès',
	InboxMessageAllLabelRecipientsCount: 'Tous les destinataires {label} ({count})',
	InboxMessageBodyPlaceholder: 'Ajoutez votre message',
	InboxMessageDeleted: 'Message supprimé',
	InboxMessageMarkedAsRead: 'Message marqué comme lu',
	InboxMessageMarkedAsUnread: 'Message marqué comme non lu',
	InboxMessageSentViaChat: '<strong>Envoyé via le chat</strong>  • {time} par {name}',
	InboxMessageShowMoreRecipients: '+{count} de plus',
	InboxMessageWasDeleted: 'Ce message a été supprimé',
	InboxNoConnectionDescription:
		'Connectez votre compte de messagerie ou créez des boîtes de réception avec plusieurs e-mails',
	InboxNoConnectionHeading: 'Intégrez vos communications clients',
	InboxNoDirectMessage: 'Pas de messages récents',
	InboxRecentConversations: 'Récent',
	InboxReopenConversationSuccess: 'Conversation rouverte avec succès',
	InboxReply: 'Répondre',
	InboxReplyAll: 'Répondre à tous',
	InboxRestoreConversationSuccess: 'Conversation restaurée avec succès',
	InboxScheduleSendCancelSendSuccess: `Envoi programmé annulé et message rétabli à l'état de brouillon`,
	InboxScheduleSendMessageSuccessDescription: 'Envoyé à {date}',
	InboxScheduleSendMessageSuccessTitle: `Planification de l'envoi`,
	InboxSearchForConversations: 'Rechercher « {query} »',
	InboxSendMessageSuccess: 'Conversation envoyée avec succès',
	InboxSettings: 'Paramètres de la boîte de réception',
	InboxSettingsAppsDesc:
		'Gérez les applications connectées pour cette boîte de réception partagée : ajoutez ou supprimez des connexions selon vos besoins.',
	InboxSettingsAppsNewConnectedApp: 'Nouvelle application connectée',
	InboxSettingsAppsTitle: 'Applications connectées',
	InboxSettingsDeleteAccountFailed: 'Échec de la suppression du compte de boîte de réception',
	InboxSettingsDeleteAccountSuccess: 'Compte de boîte de réception supprimé avec succès',
	InboxSettingsDeleteAccountWarning:
		'Supprimer {email} le déconnectera de la boîte de réception {inboxName} et empêchera la synchronisation des messages.',
	InboxSettingsDeleteInboxFailed: 'Échec de la suppression de la boîte de réception',
	InboxSettingsDeleteInboxSuccess: 'Boîte de réception supprimée avec succès',
	InboxSettingsDeleteInboxWarning:
		'Supprimer {inboxName} déconnectera tous les canaux connectés et supprimera tous les messages associés à cette boîte de réception. 		Cette action est permanente et ne peut pas être annulée.',
	InboxSettingsDetailsDesc:
		'Boîte de réception de communication pour votre équipe afin de gérer efficacement les messages des clients.',
	InboxSettingsDetailsTitle: 'Détails de la boîte de réception',
	InboxSettingsEmailSignatureLabel: 'Signature email par défaut',
	InboxSettingsReplyFormatDesc: `Configurez votre adresse de réponse par défaut et votre signature électronique pour qu'elles soient affichées de manière cohérente, quelle que soit la personne qui envoie l'e-mail.`,
	InboxSettingsReplyFormatTitle: 'Format de réponse',
	InboxSettingsSendFromLabel: 'Définir une réponse par défaut de ',
	InboxSettingsStaffDesc:
		'Gérez l’accès des membres de l’équipe à cette boîte de réception partagée pour une collaboration transparente.',
	InboxSettingsStaffTitle: `Affecter les membres de l'équipe`,
	InboxSettingsUpdateInboxDetailsFailed: 'Échec de la mise à jour des détails de la boîte de réception',
	InboxSettingsUpdateInboxDetailsSuccess: 'Détails de la boîte de réception mis à jour avec succès',
	InboxSettingsUpdateInboxStaffsFailed: `Échec de la mise à jour des membres de l'équipe de la boîte de réception`,
	InboxSettingsUpdateInboxStaffsSuccess: `Membres de l'équipe de la boîte de réception mis à jour avec succès`,
	InboxSettingsUpdateReplyFormatFailed: 'Échec de la mise à jour du format de réponse',
	InboxSettingsUpdateReplyFormatSuccess: 'Format de réponse mis à jour avec succès',
	InboxShowQuotedText: 'Afficher le texte cité',
	InboxStaffRoleAdminDescription: 'Afficher, répondre et gérer les boîtes de réception',
	InboxStaffRoleResponderDescription: 'Afficher et répondre',
	InboxStaffRoleViewerDescription: 'Visualisation uniquement',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Continuer à éditer',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: `Oui, passer à l'envoi en masse`,
	InboxSuggestMoveToBulkComposeMessageContent: `Vous avez choisi plus de {count} destinataires. Voulez-vous l'envoyer en tant qu'e-mail groupé?`,
	InboxSuggestMoveToBulkComposeMessageTitle: 'Avertissement',
	InboxSwitchToOtherInbox: 'Passer à une autre boîte de réception',
	InboxUndoSendMessageSuccess: 'Envoi annulé',
	IncludeLineItems: 'Inclure les éléments de campagne',
	IncludeSalesTax: 'Imposable',
	IncludesAiSmartPrompt: `Inclut des invites intelligentes d'IA`,
	Incomplete: 'Incomplet',
	IncreaseIndent: 'Augmenter le retrait',
	IndianHealthServiceFreeStandingFacility: 'Établissement indépendant du Service de santé indien',
	IndianHealthServiceProviderFacility: 'Établissement basé sur un prestataire de services de santé indien',
	Information: 'Information',
	InitialAssessment: 'Évaluation initiale',
	InitialSignupPageClientFamilyTitle: 'Client ou membre de la famille',
	InitialSignupPageProviderTitle: 'Santé ',
	InitialTreatment: 'Traitement initial',
	Initials: 'Initiales',
	InlineEmbed: 'Intégration en ligne',
	InputPhraseToConfirm: 'Pour confirmer, saisissez {confirmationPhrase}.',
	Insert: 'Insérer',
	InsertTable: 'Insérer un tableau',
	InstallCarepatronOnYourIphone1: 'Installez Carepatron sur votre iOS : appuyez sur',
	InstallCarepatronOnYourIphone2: `puis Ajouter à l'écran d'accueil`,
	InsufficientCalendarScopesSnackbar:
		'Échec de la synchronisation - veuillez autoriser les autorisations de calendrier sur Carepatron',
	InsufficientInboxScopesSnackbar:
		'Échec de la synchronisation - veuillez autoriser les autorisations de courrier électronique à Carepatron',
	InsufficientScopeErrorCodeSnackbar:
		'Échec de la synchronisation - veuillez autoriser toutes les autorisations à Carepatron',
	Insurance: 'Assurance',
	InsuranceAmount: `Montant de l'assurance`,
	InsuranceClaim: `Réclamation d'assurance`,
	InsuranceClaimAiChatPlaceholder: 'Demandez au sujet de la réclamation d’assurance…',
	InsuranceClaimAiClaimNumber: 'Réclamation {number}',
	InsuranceClaimAiSubtitle: `Facturation d'assurance • Validation des réclamations`,
	InsuranceClaimDeniedSubject: 'La demande {claimNumber} soumise à {payerNumber} {payerName} a été refusée',
	InsuranceClaimErrorDescription: `La réclamation contient des erreurs signalées par le payeur ou la chambre de compensation. Veuillez examiner les messages d'erreur suivants et renvoyer la réclamation.`,
	InsuranceClaimErrorGuideLink: `Guide des réclamations d'assurance`,
	InsuranceClaimErrorTitle: 'Erreurs de soumission de réclamation',
	InsuranceClaimNotFound: `Réclamation d'assurance non trouvée`,
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Un paiement partiel de {paymentAmount}} other {Un paiement de {paymentAmount}}} pour la réclamation {claimNumber} par {payerNumber} {payerName} a été enregistré',
	InsuranceClaimRejectedSubject: 'La demande {claimNumber} soumise à {payerNumber} {payerName} a été rejetée',
	InsuranceClaims: `Demandes d'indemnisation`,
	InsuranceInformation: `Informations sur l'assurance`,
	InsurancePaid: 'Assurance payée',
	InsurancePayer: `Payeur d'assurance`,
	InsurancePayers: `Payeurs d'assurance`,
	InsurancePayersDescription: `Afficher les payeurs qui ont été ajoutés à votre compte et gérer l'inscription.`,
	InsurancePayment: `Paiement d'assurance`,
	InsurancePoliciesDetailsSubtitle: 'Ajoutez les informations d’assurance client pour appuyer les réclamations.',
	InsurancePoliciesDetailsTitle: 'Détails des politiques',
	InsurancePoliciesListSubtitle: 'Ajoutez les informations d’assurance client pour appuyer les réclamations.',
	InsurancePoliciesListTitle: `Polices d'assurance`,
	InsuranceSelfPay: 'Paiement personnel',
	InsuranceType: `Type d'assurance`,
	InsuranceUnpaid: 'Assurance impayée',
	Intake: 'Admission',
	IntakeExpiredErrorCodeSnackbar:
		'Cette prise est expirée. Veuillez contacter votre fournisseur pour renvoyer une autre prise.',
	IntakeNotFoundErrorSnackbar: `Cet apport n'a pas pu être trouvé. Veuillez contacter votre fournisseur pour renvoyer une autre prise.`,
	IntakeProcessLearnMoreInstructions: `Guide pour configurer vos formulaires d'admission`,
	IntakeTemplateSelectorPlaceholder: `Choisissez les formulaires et les accords à envoyer à votre client pour qu'il les remplisse`,
	Integration: `L'intégration`,
	IntenseBlur: 'Brouillez intensément votre arrière-plan',
	InteriorDesigner: `Décorateur d'intérieur`,
	InternetBanking: 'virement',
	Interval: 'Intervalle',
	IntervalDays: 'Intervalle (jours)',
	IntervalHours: 'Intervalle (heures)',
	Invalid: 'Invalide',
	InvalidDate: 'Date invalide',
	InvalidDateFormat: 'La date doit être au format {format}',
	InvalidDisplayName: `Nom d'affichage ne peut pas contenir {value}`,
	InvalidEmailFormat: `Format d'email invalide`,
	InvalidFileType: 'Type de fichier non valide',
	InvalidGTMContainerId: `Format d'ID de conteneur GTM non valide`,
	InvalidPaymentMethodCode: `Le mode de paiement sélectionné n'est pas valide. Merci d'en choisir un autre.`,
	InvalidPromotionCode: 'Le code de promotion est invalide',
	InvalidReferralDescription: 'Vous utilisez déjà Carepatron',
	InvalidStatementDescriptor: `Le descripteur de l'instruction doit comporter entre 5 et 22 caractères et contenir uniquement des lettres, des chiffres et des espaces et ne doit pas inclure <, >, \\, ', ", *`,
	InvalidToken: 'Jeton invalide',
	InvalidTotpSetupVerificationCode: 'Code de vérification non valide.',
	InvalidURLErrorText: `Il doit s'agir d'une URL valide`,
	InvalidZoomTokenErrorCodeSnackbar:
		'Le jeton Zoom a expiré. Veuillez reconnecter votre application Zoom et réessayer.',
	Invite: 'Inviter',
	InviteRelationships: 'Inviter des relations',
	InviteToPortal: 'Inviter sur le portail',
	InviteToPortalModalDescription: `Un e-mail d'invitation sera envoyé à votre client pour s'inscrire à Carepatron.`,
	InviteToPortalModalTitle: 'Inviter {name} au portail Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Inviter un nouvel utilisateur',
	Invited: 'Invité',
	Invoice: 'Facture',
	InvoiceColorPickerDescription: 'Thème de couleur à utiliser dans la facture',
	InvoiceColorTheme: 'Thème de couleur de facture',
	InvoiceContactDeleted: 'Le contact de facture a été supprimé et cette facture ne peut pas être mise à jour.',
	InvoiceDate: `Date d'émission`,
	InvoiceDetails: 'Détails de la facture',
	InvoiceFieldsPlaceholder: 'Rechercher des champs...',
	InvoiceFrom: 'Facture {number} de {fromProvider}',
	InvoiceInvalidCredit:
		'Montant de crédit non valide, le montant du crédit ne peut pas dépasser le total de la facture',
	InvoiceNotFoundDescription: `Veuillez contacter votre fournisseur et lui demander plus d'informations ou pour renvoyer la facture.`,
	InvoiceNotFoundTitle: 'Facture introuvable',
	InvoiceNumber: 'Facture #',
	InvoiceNumberFormat: 'Facture #{number}',
	InvoiceNumberMustEndWithDigit: 'Le numéro de facture doit se terminer par un chiffre (0-9)',
	InvoicePageHeader: 'Factures',
	InvoicePaidNotificationSubject: 'Facture {invoiceNumber} payée',
	InvoiceReminder: 'Rappels de facture',
	InvoiceReminderSentence: `Envoyer un rappel {deliveryType} {interval} {unit} {beforeAfter} la date d'échéance de la facture`,
	InvoiceReminderSettings: 'Paramètres de rappel de facture',
	InvoiceReminderSettingsInfo: `Les rappels s'appliquent uniquement aux factures envoyées sur Carepatron`,
	InvoiceReminders: 'Rappels de facture',
	InvoiceRemindersInfo: `Définissez des rappels automatiques pour les dates d’échéance des factures. Les rappels s'appliquent uniquement aux factures envoyées via Carepatron`,
	InvoiceSettings: 'Paramètres de facture',
	InvoiceStatus: 'Statut de la facture',
	InvoiceTemplateAddressPlaceholder: `123 Main St, N'importe quelle ville, États-Unis`,
	InvoiceTemplateDescriptionPlaceholder:
		'Ajoutez des notes, des détails de virement bancaire ou des conditions générales pour des paiements alternatifs',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Travailleur indépendant',
	InvoiceTemplateEthnicityPlaceholder: 'caucasien',
	InvoiceTemplateNotFoundDescription: `Veuillez contacter votre fournisseur et lui demander plus d'informations.`,
	InvoiceTemplateNotFoundTitle: 'Modèle de facture introuvable',
	InvoiceTemplates: 'Modèles de facture',
	InvoiceTemplatesDescription:
		'Adaptez vos modèles de facture pour refléter votre marque, répondre aux exigences réglementaires et répondre aux préférences des clients grâce à nos modèles conviviaux.',
	InvoiceTheme: 'Thème de facture',
	InvoiceTotal: 'Total de la facture',
	InvoiceUninvoicedAmounts: 'Facturer les montants non facturés',
	InvoiceUpdateVersionMessage:
		'La modification de cette facture nécessite la dernière version. Veuillez recharger Carepatron et réessayer.',
	Invoices: '{count, plural, one {Facture} other {Factures}}',
	InvoicesEmptyStateDescription: `Aucune facture n'a été trouvée`,
	InvoicingAndPayment: 'Facturation ',
	Ireland: 'Irlande',
	IsA: 'est un',
	IsBetween: 'est entre',
	IsEqualTo: 'est égal à',
	IsGreaterThan: 'est supérieur à',
	IsGreaterThanOrEqualTo: 'est supérieur ou égal à',
	IsLessThan: 'est inférieur à',
	IsLessThanOrEqualTo: 'est inférieur ou égal à',
	IssueCredit: 'Émettre un crédit',
	IssueCreditAdjustment: `Régularisation du crédit d'émission`,
	IssueDate: `Date d'émission`,
	Italic: 'Italique',
	Items: 'Articles',
	ItemsAndAdjustments: 'Articles et ajustements',
	ItemsRemaining: '+{count} éléments restants',
	JobTitle: `Titre d'emploi`,
	Join: 'Rejoindre',
	JoinCall: `Rejoindre l'appel`,
	JoinNow: 'Adhérer maintenant',
	JoinProduct: 'Rejoindre {product}',
	JoinVideoCall: 'Rejoindre un appel vidéo',
	JoinWebinar: 'Participer au webinaire',
	JoinWithVideoCall: 'Rejoignez {product}',
	Journalist: 'Journaliste',
	JustMe: 'Juste moi',
	JustYou: 'Juste toi',
	Justify: 'Justifier',
	KeepSeparate: 'Garder séparé',
	KeepSeparateSuccessMessage: 'Vous avez correctement tenu des dossiers distincts pour {clientNames}',
	KeepWaiting: 'Faire attendre',
	Label: 'Étiquette',
	LabelOptional: 'Étiquette (facultatif)',
	LactationConsulting: 'Consultation en lactation',
	Language: 'Langue',
	Large: 'Grand',
	LastDxCode: 'Dernier code DX',
	LastLoggedIn: 'Dernière connexion le {date} à {time}',
	LastMenstrualPeriod: 'Dernières règles',
	LastMonth: 'Le mois dernier',
	LastNDays: 'Derniers {number} jours',
	LastName: 'Nom de famille',
	LastNameFirstInitial: 'Nom de famille, initiale du prénom',
	LastWeek: 'La semaine dernière',
	LastXRay: 'Dernière radiographie',
	LatestVisitOrConsultation: 'Dernière visite ou consultation',
	Lawyer: 'Avocat',
	LearnMore: 'Apprendre encore plus',
	LearnMoreTipsToGettingStarted: 'Découvrez plus de conseils pour démarrer',
	LearnToSetupInbox: 'Découvrez comment configurer un compte de boîte de réception',
	Leave: 'Partir',
	LeaveCall: `Quitter l'appel`,
	LeftAlign: 'Aligner à gauche',
	LegacyBillingItemsNotAvailable:
		'Les éléments de facturation individuels ne sont pas encore disponibles pour ce rendez-vous. Vous pouvez néanmoins le facturer normalement.',
	LegacyBillingItemsNotAvailableTitle: 'Facturation héritée',
	LegalAndConsent: 'Mentions légales et consentement',
	LegalConsentFormPrimaryText: 'Consentement légal',
	LegalConsentFormSecondaryText: 'Accepter ou refuser les options',
	LegalGuardian: 'Tuteur légal',
	Letter: 'Lettre',
	LettersCategoryDescription: 'Pour créer des correspondances cliniques et administratives',
	Librarian: 'Bibliothécaire',
	LicenseNumber: 'Numéro de licence',
	LifeCoach: 'Coach de vie',
	LifeCoaches: 'Coachs de vie',
	Limited: 'Limitée',
	LineSpacing: 'Espacement des lignes et des paragraphes',
	LinearScaleFormPrimaryText: 'Échelle linéaire',
	LinearScaleFormSecondaryText: `Options d'échelle 1 à 10`,
	Lineitems: 'Éléments de ligne',
	Link: 'Lien',
	LinkClientFormSearchClientLabel: 'Rechercher un client',
	LinkClientModalTitle: 'Lien vers un client existant',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> coordonnées sont ajoutées au dossier de <strong>{existingName}’s</strong>.',
	LinkClientSuccessTitle: 'Liaison réussie avec un contact existant',
	LinkForCallCopied: 'Lien copié !',
	LinkToAnExistingClient: 'Lien vers un client existant',
	LinkToClient: 'Lien vers le client',
	ListAndTracker: 'Liste/Suivi',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} est dans cet appel}
			other {{attendees} sont dans cet appel}
		}`,
	ListStyles: 'Styles de liste',
	ListsAndTrackersCategoryDescription: 'Pour organiser et suivre le travail',
	LivingArrangements: 'Modes de vie',
	LoadMore: 'Charger plus',
	Loading: 'Chargement...',
	LocalizationPanelDescription: 'Gérer les paramètres de votre langue et de votre fuseau horaire',
	LocalizationPanelTitle: 'Langue et fuseau horaire',
	Location: 'Emplacement',
	LocationDescription: `Configurez des emplacements physiques et virtuels avec des adresses spécifiques, des noms de salles et des types d'espaces virtuels pour faciliter la planification des rendez-vous et des appels vidéo.`,
	LocationNumber: `Numéro d'emplacement`,
	LocationOfService: 'Lieu de prestation',
	LocationOfServiceRecommendedActionInfo:
		'Ajouter un emplacement spécifique à ce service peut affecter votre disponibilité.',
	LocationRemote: 'Distant',
	LocationType: 'Type de lieu',
	Locations: 'Emplacements',
	Lock: 'Verrouillage',
	Locked: 'Fermé à clé',
	LockedNote: 'Note verrouillée',
	LogInToSaveOrAuthoriseCard: 'Connectez-vous pour enregistrer ou autoriser la carte',
	LogInToSaveOrAuthorisePayment: 'Connectez-vous pour enregistrer ou autoriser le paiement',
	Login: 'Se connecter',
	LoginButton: 'Se connecter',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Mot de passe oublié',
	LoginPassword: 'Mot de passe',
	Logo: 'Logo',
	LogoutAreYouSure: 'Déconnectez-vous de cet appareil.',
	LogoutButton: 'se déconnecter',
	London: 'Londres',
	LongTextAnswer: 'Réponse textuelle longue',
	LongTextFormPrimaryText: 'Texte long',
	LongTextFormSecondaryText: 'Options de style de paragraphe',
	Male: 'Mâle',
	Manage: 'Gérer',
	ManageAllClientTags: 'Gérer toutes les balises client',
	ManageAllNoteTags: 'Gérer toutes les balises de note',
	ManageAllTemplateTags: 'Gérer toutes les balises de modèle',
	ManageConnections: 'Gérer les connexions',
	ManageConnectionsGmailDescription: `Les autres membres de l'équipe ne pourront pas voir votre Gmail synchronisé.`,
	ManageConnectionsGoogleCalendarDescription: `Les autres membres de l'équipe ne pourront pas voir vos calendriers synchronisés. Les rendez-vous des clients ne peuvent être mis à jour ou supprimés qu'à partir de Carepatron.`,
	ManageConnectionsInboxSyncHelperText:
		'Veuillez accéder à la page Boîte de réception afin de gérer les paramètres de synchronisation de la boîte de réception.',
	ManageConnectionsMicrosoftCalendarDescription: `Les autres membres de l'équipe ne pourront pas voir vos calendriers synchronisés. Les rendez-vous des clients ne peuvent être mis à jour ou supprimés que depuis Carepatron.`,
	ManageConnectionsOutlookDescription:
		'Les autres membres de l’équipe ne pourront pas voir votre Microsoft Outlook synchronisé.',
	ManageInboxAccountButton: 'Nouvelle boîte de réception',
	ManageInboxAccountEdit: 'Gérer la boîte de réception',
	ManageInboxAccountPanelTitle: 'Boîtes de réception',
	ManageInboxAssignTeamPlaceholder: `Choisir les membres de l'équipe pour accéder à la boîte de réception`,
	ManageInboxBasicInfoColor: 'Couleur',
	ManageInboxBasicInfoDescription: 'Description',
	ManageInboxBasicInfoDescriptionPlaceholder: 'À quoi allez-vous ou votre équipe utiliser cette boîte de réception?',
	ManageInboxBasicInfoName: 'Nom de la boîte de réception',
	ManageInboxBasicInfoNamePlaceholder: 'Par exemple, support client, administrateur',
	ManageInboxConnectAppAlreadyConnectedError:
		'La chaîne que vous avez tenté de connecter est déjà connectée à Carepatron',
	ManageInboxConnectAppConnect: 'Connecter',
	ManageInboxConnectAppConnectedInfo: 'Connecté à un compte',
	ManageInboxConnectAppContinue: 'Continuer',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Se connecter avec',
	ManageInboxConnectAppSubtitle:
		'Connectez vos applications pour envoyer, recevoir et suivre en toute transparence toutes vos communications en un seul endroit centralisé.',
	ManageInboxNewInboxTitle: 'Nouvelle boîte de réception',
	ManagePlan: 'Gérer le plan',
	ManageProfile: 'Gérer le profil',
	ManageReferralsModalDescription:
		'Aidez-nous à faire connaître notre plateforme de soins de santé et gagnez des récompenses.',
	ManageReferralsModalTitle: 'Parrainez un ami, gagnez des récompenses !',
	ManageStaffRelationshipsAddButton: 'Gérer les relations',
	ManageStaffRelationshipsEmptyStateText: 'Aucune relation ajoutée',
	ManageStaffRelationshipsModalDescription:
		'La sélection de clients ajoutera de nouvelles relations, tandis que leur désélection supprimera les relations existantes.',
	ManageStaffRelationshipsModalTitle: 'Gérer les relations',
	ManageStatuses: 'Gérer les statuts',
	ManageStatusesActiveStatusHelperText: 'Au moins un statut actif est requis',
	ManageStatusesDescription:
		'Personnalisez vos étiquettes de statut et choisissez les couleurs en fonction de votre flux de travail.',
	ManageStatusesSuccessSnackbar: 'Statuts mis à jour avec succès',
	ManageTags: 'Gérer les balises',
	ManageTaskAttendeeStatus: 'Gérer les statuts des rendez-vous',
	ManageTaskAttendeeStatusDescription:
		'Personnalisez vos statuts de rendez-vous pour les aligner sur votre flux de travail.',
	ManageTaskAttendeeStatusHelperText: 'Au moins un statut est requis',
	ManageTaskAttendeeStatusSubtitle: 'Statuts personnalisés',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manuel',
	ManualAppointment: 'Rendez-vous manuel',
	ManualPayment: 'Paiement manuel',
	ManuallyTypeLocation: `Saisir manuellement l'emplacement`,
	MapColumns: 'Colonnes de la carte',
	MappingRequired: 'Mapping requis',
	MarkAllAsRead: 'tout marquer comme lu',
	MarkAsCompleted: 'Marquer comme terminé',
	MarkAsManualSubmission: 'Marquer comme soumis',
	MarkAsPaid: 'Marquer comme payé',
	MarkAsRead: 'Marquer comme lu',
	MarkAsUnpaid: 'Marquer comme impayé',
	MarkAsUnread: 'marquer comme non lu',
	MarkAsVoid: 'Marquer comme nul',
	Marker: 'Marqueur',
	MarketingManager: 'Responsable marketing',
	MassageTherapist: 'Massothérapeute',
	MassageTherapists: 'Massothérapeutes',
	MassageTherapy: 'Massage thérapeutique',
	MaxBookingTimeDescription1: `Les clients peuvent planifier jusqu'à`,
	MaxBookingTimeDescription2: 'dans le futur',
	MaxBookingTimeLabel: `{timePeriod} à l'avance`,
	MaxCapacity: 'capacité maximale',
	Maximize: 'Maximiser',
	MaximumAttendeeLimit: 'Limite maximale',
	MaximumBookingTime: 'Durée maximale de réservation',
	MaximumBookingTimeError: 'Durée maximale de réservation ne doit pas dépasser {valueUnit}',
	MaximumMinimizedPanelsReachedDescription: `Vous pouvez minimiser jusqu'à {count} panneaux latéraux à la fois. La poursuite de l'opération fermera le panneau minimisé le plus ancien. Voulez-vous continuer ?`,
	MaximumMinimizedPanelsReachedTitle: 'Vous avez trop de panneaux ouverts.',
	MechanicalEngineer: 'Ingénieur mécanique',
	MediaGallery: 'Galerie des médias',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Numéro de fournisseur Medicaid',
	MedicalAssistant: 'Assistant médical',
	MedicalCoder: 'Codeur médical',
	MedicalDoctor: 'Médecin',
	MedicalIllustrator: 'Illustrateur médical',
	MedicalInterpreter: 'Interprète médical',
	MedicalTechnologist: 'Technologue médical',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Numéro de fournisseur Medicare',
	Medicine: 'Médecine',
	Medium: 'Moyen',
	Meeting: 'Réunion',
	MeetingEnd: 'Fin de la réunion',
	MeetingEnded: 'Réunion terminée',
	MeetingHost: 'Hôte de la réunion',
	MeetingLowerHand: 'Baisser la main',
	MeetingOpenChat: 'Ouvrir le chat',
	MeetingPersonRaisedHand: '{name} a levé la main',
	MeetingRaiseHand: 'Lever la main',
	MeetingReady: 'Prêt pour la réunion',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Réunions',
	MemberId: 'Numéro de membre',
	MentalHealth: 'Santé mentale',
	MentalHealthPractitioners: 'Praticiens de la santé mentale',
	MentalHealthProfessional: 'Professionnel de la santé mentale',
	Merge: 'Fusionner',
	MergeClientRecords: 'Fusionner les dossiers clients',
	MergeClientRecordsDescription: 'La fusion des dossiers clients combinera toutes leurs données, y compris :',
	MergeClientRecordsDescription2: 'Souhaitez-vous continuer la fusion ? Cette action est irréversible.',
	MergeClientRecordsItem1: 'Notes et documents',
	MergeClientRecordsItem2: 'Rendez-vous',
	MergeClientRecordsItem3: 'Factures',
	MergeClientRecordsItem4: 'Conversations',
	MergeClientsSuccess: 'Fusion des dossiers clients réussie',
	MergeLimitExceeded: 'Vous ne pouvez fusionner que 4 clients à la fois.',
	Message: 'Message',
	MessageAttachments: '{total} pièces jointes',
	Method: 'Méthode',
	MfaAvailabilityDisclaimer: `L'authentification multifacteur est uniquement disponible pour les connexions par e-mail et mot de passe. Pour modifier vos paramètres d'authentification multifacteur, connectez-vous à l'aide de votre e-mail et de votre mot de passe.`,
	MfaDeviceLostPanelDescription:
		'Alternativement, vous pouvez vérifier votre identité en recevant un code par e-mail.',
	MfaDeviceLostPanelTitle: 'Vous avez perdu votre appareil MFA ?',
	MfaDidntReceiveEmailCode: `Vous n'avez pas reçu de code ? Contactez le support`,
	MfaEmailOtpSendFailureSnackbar: `Échec de l'envoi de l'OTP par e-mail.`,
	MfaEmailOtpSentSnackbar: 'Un code a été envoyé à {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: `Échec de la vérification de l'OTP de l'e-mail.`,
	MfaHasBeenSetUpText: 'Vous avez configuré MFA',
	MfaPanelDescription: `Sécurisez votre compte en activant l'authentification multifacteur (MFA) pour une couche de protection supplémentaire. Vérifiez votre identité via une méthode secondaire pour empêcher tout accès non autorisé.`,
	MfaPanelNotAuthorizedError: `Vous devez être connecté avec un nom d'utilisateur `,
	MfaPanelRecommendationDescription: `Vous vous êtes récemment connecté à l'aide d'une autre méthode pour vérifier votre identité. Pour sécuriser votre compte, pensez à configurer un nouveau périphérique MFA.`,
	MfaPanelRecommendationTitle: `**Recommandé :** Mettez à jour votre appareil d'authentification à deux facteurs`,
	MfaPanelTitle: 'Authentification multifacteur (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'Vous devrez vérifier votre e-mail avant de pouvoir mettre à jour vos paramètres MFA.',
	MfaRecommendationBannerDescription: `Vous vous êtes récemment connecté à l'aide d'une autre méthode pour vérifier votre identité. Pour sécuriser votre compte, pensez à configurer un nouveau périphérique MFA.`,
	MfaRecommendationBannerPrimaryAction: 'Configurer MFA',
	MfaRecommendationBannerTitle: 'Recommandé',
	MfaRemovedSnackbarTitle: `L'AMF a été supprimé.`,
	MfaSendEmailCode: 'Envoyer le code',
	MfaVerifyIdentityLostDeviceButton: `J'ai perdu l'accès à mon appareil MFA`,
	MfaVerifyYourIdentityPanelDescription: `Vérifiez votre application d'authentification pour le code et saisissez-le ci-dessous.`,
	MfaVerifyYourIdentityPanelTitle: 'Vérifiez votre identité',
	MicCamWarningMessage: `Débloquez la caméra et le microphone en cliquant sur les icônes bloquées dans la barre d'adresse du navigateur.`,
	MicCamWarningTitle: 'La caméra et le microphone sont bloqués',
	MicOff: 'Le micro est éteint',
	MicOn: 'Le micro est allumé',
	MicSource: 'Source du microphone',
	MicWarningMessage: 'Un problème a été détecté avec votre microphone',
	Microphone: 'Microphone',
	MicrophonePermissionBlocked: 'Accès au microphone bloqué',
	MicrophonePermissionBlockedDescription: `Mettez à jour les autorisations de votre microphone pour commencer l'enregistrement.`,
	MicrophonePermissionError: `Veuillez accorder l'autorisation du microphone dans vos paramètres du navigateur pour continuer`,
	MicrophonePermissionPrompt: `Veuillez autoriser l'accès au microphone pour continuer`,
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Couleur du calendrier Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Équipes Microsoft',
	MiddleEast: 'Moyen-Orient',
	MiddleName: 'Deuxième prénom',
	MiddleNames: 'Deuxième prénom',
	Midwife: 'Sage-femme',
	Midwives: 'Sages-femmes',
	Milan: 'Milan',
	MinBookingTimeDescription1: 'Les clients ne peuvent pas planifier dans',
	MinBookingTimeDescription2: `de l'heure de début d'un rendez-vous`,
	MinBookingTimeLabel: '{timePeriod} avant rendez-vous',
	MinCancellationTimeEditModeDescription: `Définissez le nombre d'heures qu'un client peut annuler sans pénalité`,
	MinCancellationTimeUnset: `Aucun délai minimum d'annulation fixé`,
	MinCancellationTimeViewModeDescription: `Délai d'annulation sans pénalité`,
	MinMaxBookingTimeUnset: 'Aucune heure définie',
	Minimize: 'Minimiser',
	MinimizeConfirmationDescription:
		'Vous avez un panneau actif minimisé. Si vous continuez, il se fermera et vous risquez de perdre des données non sauvegardées.',
	MinimizeConfirmationTitle: 'Fermer le panneau réduit ?',
	MinimumBookingTime: 'Durée minimale de réservation',
	MinimumCancellationTime: `Délai minimum d'annulation`,
	MinimumPaymentError: 'Des frais minimums de {minimumAmount} sont requis pour les paiements en ligne.',
	MinuteAbbreviated: 'minutes',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minute par minute',
	MinutesPlural: '{age, plural, one {# minute} other {# minutes}}',
	MiscellaneousInformation: 'Informations diverses',
	MissingFeatures: 'Fonctionnalités manquantes',
	MissingPaymentMethod:
		'Veuillez ajouter un mode de paiement à votre abonnement pour ajouter plus de membres du personnel.',
	MobileNumber: 'Numéro de portable',
	MobileNumberOptional: 'Numéro de portable (facultatif)',
	Modern: 'Moderne',
	Modifiers: 'Modificateurs',
	ModifiersPlaceholder: 'Modificateurs',
	Monday: 'Lundi',
	Month: 'Mois',
	Monthly: 'Mensuel',
	MonthlyCost: 'Coût mensuel',
	MonthlyOn: 'Mensuel le {date}',
	MonthsPlural: '{age, plural, one {# mois} other {# mois}}',
	More: 'Plus',
	MoreActions: `Plus d'actions`,
	MoreSettings: 'Plus de réglages',
	MoreThanTen: 'dix ',
	MostCommonlyUsed: 'Le plus couramment utilisé',
	MostDownloaded: 'Le plus téléchargé',
	MostPopular: 'Le plus populaire',
	Mother: 'Mère',
	MotherInLaw: 'Belle-mère',
	MoveDown: 'Descendre',
	MoveInboxConfirmationDescription: `Reassigner cette connexion d'application la supprimera de la boîte de réception **{currentInboxName}**.`,
	MoveTemplateToFolder: 'Déplacer `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} déplacé vers {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: `Déplacé avec succès vers le dossier d'admission par défaut`,
	MoveTemplateToNewFolder: 'Créer un nouveau dossier pour déplacer cet élément.',
	MoveToChosenFolder:
		'Choisissez un dossier pour déplacer cet élément. Vous pouvez créer un nouveau dossier si nécessaire.',
	MoveToFolder: 'Aller au dossier',
	MoveToInbox: 'Déplacer vers la boîte de réception',
	MoveToNewFolder: 'Déplacer vers un nouveau dossier',
	MoveToSelectedFolder: `Une fois déplacé, l'élément sera organisé sous le dossier sélectionné et ne figurera plus à son emplacement actuel.`,
	MoveUp: 'Déplacer vers le haut',
	MultiSpeciality: 'Multi-spécialité',
	MultipleChoiceFormPrimaryText: 'Choix multiple',
	MultipleChoiceFormSecondaryText: 'Choisissez plusieurs options',
	MultipleChoiceGridFormPrimaryText: 'Grille à choix multiples',
	MultipleChoiceGridFormSecondaryText: 'Choisissez des options dans une matrice',
	Mumbai: 'Bombay',
	MusicTherapist: 'Musique thérapeute',
	MustContainOneLetterError: 'Doit contenir au moins une lettre',
	MustEndWithANumber: 'Doit se terminer par un chiffre',
	MustHaveAtLeastXItems: 'Doit avoir au moins {count, plural, one {# élément} other {# éléments}}',
	MuteAudio: 'Couper le son',
	MuteEveryone: 'Couper le son de tout le monde',
	MyAvailability: 'Ma disponibilité',
	MyGallery: 'Ma galerie',
	MyPortal: 'Mon portail',
	MyRelationships: 'Mes relations',
	MyTemplates: `Modèles d'équipe`,
	MyofunctionalTherapist: 'Thérapeute myofonctionnel',
	NCalifornia: 'Californie du Nord',
	NPI: 'IPN',
	NVirginia: 'Virginie du Nord',
	Name: 'Nom',
	NameIsRequired: 'Le nom est requis',
	NameMustNotBeAWebsite: 'Le nom ne doit pas être un site Web',
	NameMustNotBeAnEmail: 'Le nom ne doit pas être un email',
	NameMustNotContainAtSign: 'Le nom ne doit pas contenir le signe @',
	NameMustNotContainHTMLTags: 'Le nom ne doit pas contenir de balises HTML',
	NameMustNotContainSpecialCharacters: 'Le nom ne doit pas contenir de caractères spéciaux',
	NameOnCard: 'Nom sur la carte',
	NationalProviderId: 'Identifiant national du fournisseur (NPI)',
	NaturopathicDoctor: 'Docteur Naturopathe',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: `Paramètres d'abonnement`,
	NavigateToWorkspaceSettings: `Paramètres de l'espace de travail`,
	NavigateToYourTeam: `Gérer l'équipe`,
	NavigationDrawerBilling: 'Facturation',
	NavigationDrawerBillingInfo: 'Informations de facturation, factures et Stripe',
	NavigationDrawerCommunication: 'Communication',
	NavigationDrawerCommunicationInfo: 'Notifications et modèles',
	NavigationDrawerInsurance: 'Assurance',
	NavigationDrawerInsuranceInfo: 'Payeurs d’assurance et réclamations',
	NavigationDrawerInvoices: 'Facturation',
	NavigationDrawerPersonal: 'Mon profil',
	NavigationDrawerPersonalInfo: 'Vos informations personnelles',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Paramètres',
	NavigationDrawerScheduling: 'Planification',
	NavigationDrawerSchedulingInfo: 'Détails des prestations et réservations',
	NavigationDrawerSettings: 'Paramètres',
	NavigationDrawerTemplates: 'Modèles',
	NavigationDrawerTemplatesV2: 'Modèles V2',
	NavigationDrawerTrash: 'Corbeille',
	NavigationDrawerTrashInfo: 'Restaurer les éléments supprimés',
	NavigationDrawerWorkspace: `Paramètres de l'espace de travail`,
	NavigationDrawerWorkspaceInfo: `Informations sur l'abonnement et l'espace de travail`,
	NegativeBalanceNotSupported: 'Les soldes de compte négatifs ne sont pas pris en charge',
	Nephew: 'Neveu',
	NetworkQualityFair: 'Connexion moyenne',
	NetworkQualityGood: 'Bonne connexion',
	NetworkQualityPoor: 'Connexion faible',
	Neurologist: 'Neurologue',
	Never: 'Jamais',
	New: 'Nouveau',
	NewAppointment: 'Nouveau rendez-vous',
	NewClaim: 'Nouvelle réclamation',
	NewClient: 'Nouveau client',
	NewClientNextStepsModalAddAnotherClient: 'Ajouter un autre client',
	NewClientNextStepsModalBookAppointment: 'Prendre rendez-vous',
	NewClientNextStepsModalBookAppointmentDescription: 'Réservez un rendez-vous à venir ou créez une tâche.',
	NewClientNextStepsModalCompleteBasicInformation: 'Dossier client complet',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Ajoutez les informations client et capturez les étapes suivantes.',
	NewClientNextStepsModalCreateInvoice: 'Créer une facture',
	NewClientNextStepsModalCreateInvoiceDescription:
		'Ajoutez les informations de paiement du client ou créez une facture.',
	NewClientNextStepsModalCreateNote: 'Créer une note ou télécharger un document',
	NewClientNextStepsModalCreateNoteDescription: 'Capturez les notes et la documentation du client.',
	NewClientNextStepsModalDescription:
		'Voici quelques actions à entreprendre maintenant que vous avez créé un dossier client.',
	NewClientNextStepsModalSendIntake: `Envoyer l'admission`,
	NewClientNextStepsModalSendIntakeDescription:
		'Collectez les informations sur les clients et envoyez des formulaires supplémentaires à remplir et à signer.',
	NewClientNextStepsModalSendMessage: 'Envoyer un message',
	NewClientNextStepsModalSendMessageDescription: 'Rédigez et envoyez un message à votre client.',
	NewClientNextStepsModalTitle: 'Prochaines étapes',
	NewClientSuccess: 'Nouveau client créé avec succès',
	NewClients: 'Nouveaux clients',
	NewConnectedApp: 'Nouvelle application connectée',
	NewContact: 'Nouveau contact',
	NewContactNextStepsModalAddRelationship: 'Ajouter une relation',
	NewContactNextStepsModalAddRelationshipDescription: 'Lier ce contact à des clients ou groupes associés.',
	NewContactNextStepsModalBookAppointment: 'Prendre rendez-vous',
	NewContactNextStepsModalBookAppointmentDescription: 'Prendre rendez-vous ou créer une tâche.',
	NewContactNextStepsModalCompleteProfile: 'Profil complet',
	NewContactNextStepsModalCompleteProfileDescription: 'Ajouter les coordonnées et capturer les prochaines étapes.',
	NewContactNextStepsModalCreateNote: 'Créer une note ou télécharger un document',
	NewContactNextStepsModalCreateNoteDescription: 'Capturer les notes et la documentation des clients.',
	NewContactNextStepsModalDescription: 'Voici quelques actions à prendre maintenant que vous avez créé un contact.',
	NewContactNextStepsModalInviteToPortal: 'Invitation au portail',
	NewContactNextStepsModalInviteToPortalDescription: 'Envoyer une invitation pour accéder au portail.',
	NewContactNextStepsModalTitle: 'Étapes suivantes',
	NewContactSuccess: 'Nouveau contact créé avec succès',
	NewDateOverrideButton: `Remplacement d'une nouvelle date`,
	NewDiagnosis: 'Ajouter un diagnostic',
	NewField: 'Nouveau champ',
	NewFolder: 'Nouveau dossier',
	NewInvoice: 'Nouvelle facture',
	NewLocation: 'Nouvel emplacement',
	NewLocationFailure: `Échec de la création d'un nouvel emplacement`,
	NewLocationSuccess: 'Nouvel emplacement créé avec succès',
	NewManualPayer: 'Nouveau payeur manuel',
	NewNote: 'Nouvelle note',
	NewNoteCreated: 'Nouvelle note créée avec succès',
	NewPassword: 'Nouveau mot de passe',
	NewPayer: 'Nouveau payeur',
	NewPaymentMethod: 'Nouveau mode de paiement',
	NewPolicy: 'Nouvelle politique',
	NewRelationship: 'Nouvelle relation',
	NewReminder: 'Nouveau rappel',
	NewSchedule: 'Nouveau emploi du temps',
	NewSection: 'Nouvelle rubrique',
	NewSectionOld: 'Nouvelle rubrique [ancienne]',
	NewSectionWithGrid: 'Nouvelle rubrique avec grille',
	NewService: 'Nouveau service',
	NewServiceFailure: `Échec de la création d'un nouveau service`,
	NewServiceSuccess: 'Nouveau service créé avec succès',
	NewStatus: 'Nouveau statut',
	NewTask: 'Nouvelle tâche',
	NewTaxRate: `Nouveau taux d'imposition`,
	NewTeamMemberNextStepsModalAssignClients: 'Attribuer des clients',
	NewTeamMemberNextStepsModalAssignClientsDescription: `Affectez des clients spécifiques à votre membre d'équipe.`,
	NewTeamMemberNextStepsModalAssignServices: 'Attribuer des services',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Gérer leurs services assignés et ajuster les prix au besoin.',
	NewTeamMemberNextStepsModalBookAppointment: 'Prendre rendez-vous',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Prenez rendez-vous ou créez une tâche.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Profil complet',
	NewTeamMemberNextStepsModalCompleteProfileDescription: `Ajoutez des détails sur votre membre d'équipe pour compléter son profil.`,
	NewTeamMemberNextStepsModalDescription: `Voici quelques actions à prendre maintenant que vous avez créé un membre d'équipe.`,
	NewTeamMemberNextStepsModalEditPermissions: `Permissions d'édition`,
	NewTeamMemberNextStepsModalEditPermissionsDescription: `Ajustez leurs niveaux d'accès pour vous assurer qu'ils disposent des autorisations adéquates.`,
	NewTeamMemberNextStepsModalSetAvailability: 'Définir la disponibilité',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Configurer leur disponibilité pour créer des horaires.',
	NewTeamMemberNextStepsModalTitle: 'Prochaines étapes',
	NewTemplateFolderDescription: 'Créez un nouveau dossier pour organiser votre documentation.',
	NewUIUpdateBannerButton: `Recharger l'application`,
	NewUIUpdateBannerTitle: 'Une nouvelle mise à jour est prête !',
	NewZealand: 'Nouvelle-Zélande',
	Newest: 'Le plus récent',
	NewestUnreplied: 'Le plus récent sans réponse',
	Next: 'Suivant',
	NextInvoiceIssueDate: `Prochaine date d'émission de la facture`,
	NextNDays: 'Prochains {number} jours',
	Niece: 'Nièce',
	No: 'Non',
	NoAccessGiven: 'Aucun accès donné',
	NoActionConfigured: 'Aucune action configurée',
	NoActivePolicies: 'Aucune politique active',
	NoActiveReferrals: `Vous n'avez aucune référence active`,
	NoAppointmentsFound: `Aucun rendez-vous n'a été trouvé`,
	NoAppointmentsHeading: `Gérez les rendez-vous et l'activité des clients`,
	NoArchivedPolicies: 'Aucune politique archivée',
	NoAvailableTimes: 'Aucun créneau disponible trouvé.',
	NoBillingItemsFound: 'Aucun article de facturation trouvé',
	NoCalendarsSynced: 'Aucun calendrier synchronisé',
	NoClaimsFound: 'Aucune réclamation trouvée',
	NoClaimsHeading: 'Simplifier la soumission des demandes de remboursement',
	NoClientsHeading: 'Rassemblez les dossiers de vos clients',
	NoCompletedReferrals: `Vous n'avez pas de références complètes`,
	NoConnectionsHeading: 'Rationalisez vos communications avec les clients',
	NoContactsGivenAccess: `Aucun client ou contact n'a eu accès à cette note`,
	NoContactsHeading: 'Restez connecté avec ceux qui soutiennent votre pratique',
	NoCopayOrCoinsurance: 'Pas de quote-part ni de coassurance',
	NoCustomServiceSchedule: `Aucun horaire personnalisé défini : la disponibilité dépend de la disponibilité du membre de l'équipe`,
	NoDescription: 'Aucune description',
	NoDocumentationHeading: 'Créez et stockez des notes en toute sécurité',
	NoDuplicateRecordsHeading: 'Votre dossier client est exempt de doublons',
	NoEffect: 'Aucun effet',
	NoEnrolmentProfilesFound: `Aucun profil d'inscription trouvé`,
	NoGlossaryItems: 'Aucun élément de glossaire',
	NoInvitedReferrals: `Vous n'avez aucune référence invitée`,
	NoInvoicesFound: 'Aucune facture trouvée',
	NoInvoicesHeading: 'Automatisez votre facturation et vos paiements',
	NoLimit: 'Sans limites',
	NoLocationsFound: `Aucun emplacement n'a été trouvé`,
	NoLocationsWillBeAdded: 'Aucun emplacement ne sera ajouté.',
	NoNoteFound: 'Aucune note trouvée',
	NoPaymentMethods: `Vous n'avez aucun moyen de paiement enregistré, vous pouvez en ajouter un lors d'un paiement.`,
	NoPermissionError: `Tu n'as pas la permission`,
	NoPermissions: `Vous n'avez pas la permission de voir cette page`,
	NoPolicy: `Aucune politique d'annulation ajoutée`,
	NoRecordsHeading: 'Personnalisez les dossiers de vos clients',
	NoRecordsToDisplay: 'Pas de {resource} à afficher',
	NoRelationshipsHeading: 'Restez connecté avec ceux qui soutiennent votre client',
	NoRemindersFound: 'Aucun rappel trouvé',
	NoResultsFound: 'Aucun résultat trouvé',
	NoResultsFoundDescription: 'Nous ne pouvons trouver aucun élément correspondant à votre recherche',
	NoServicesAdded: 'Aucun service ajouté',
	NoServicesApplied: 'Aucun service appliqué',
	NoServicesWillBeAdded: 'Aucun service ne sera ajouté.',
	NoTemplate: `Vous n'avez enregistré aucun modèle de pratique`,
	NoTemplatesHeading: 'Créez vos propres modèles',
	NoTemplatesInFolder: 'Pas de modèles dans ce dossier',
	NoTitle: 'Pas de titre',
	NoTrashItemsHeading: 'Aucun élément supprimé trouvé',
	NoTriggerConfigured: 'Aucun déclencheur configuré',
	NoUnclaimedItemsFound: `Aucun objet non réclamé n'a été trouvé.`,
	NonAiTemplates: 'Modèles non-IA',
	None: 'Aucun',
	NotAvailable: 'Indisponible',
	NotCovered: 'Non couvert',
	NotFoundSnackbar: 'Ressource introuvable.',
	NotRequiredField: 'Non requis',
	Note: 'Note',
	NoteDuplicateSuccess: 'Note dupliquée avec succès',
	NoteEditModeViewSwitcherDescription: 'Créer et modifier une note',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} a soumis le formulaire {noteTitle}',
	NoteLockSuccess: '{title} a été verrouillé',
	NoteModalAttachmentButton: 'Ajouter des pièces jointes',
	NoteModalPhotoButton: 'Ajouter/Capturer des photos',
	NoteModalTrascribeButton: `Transcrire l'audio en direct`,
	NoteResponderModeViewSwitcherDescription: 'Envoyer des formulaires et examiner les réponses',
	NoteResponderModeViewSwitcherTooltipTitle: 'Répondez et soumettez des formulaires au nom de vos clients',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Remplir et soumettre des formulaires en tant que client',
	NoteUnlockSuccess: '{title} a été déverrouillé',
	NoteViewModeViewSwitcherDescription: 'Accès en lecture seule',
	Notes: 'Remarques',
	NotesAndForms: 'Notes et formulaires',
	NotesCategoryDescription: 'Pour documenter les interactions avec les clients',
	NothingToSeeHere: 'Rien à voir ici',
	Notification: 'Notification',
	NotificationIgnoredMessage: 'Toutes les notifications {notificationType} seront ignorées',
	NotificationRestoredMessage: 'Toutes les notifications {notificationType} restaurées',
	NotificationSettingBillingDescription:
		'Recevez des notifications concernant les mises à jour de paiement des clients et les rappels.',
	NotificationSettingBillingTitle: 'Facturation et paiement',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} seulement} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Recevez des notifications concernant les mises à jour de paiement des clients et les rappels.',
	NotificationSettingClientDocumentationTitle: 'Client et documentation',
	NotificationSettingCommunicationsDescription:
		'Recevez des notifications pour la boîte de réception et les mises à jour de vos canaux connectés',
	NotificationSettingCommunicationsTitle: 'Communications',
	NotificationSettingEmail: 'Email',
	NotificationSettingInApp: `Dans l'application`,
	NotificationSettingPanelDescription:
		'Choisissez les notifications que vous souhaitez recevoir pour les activités et les recommandations.',
	NotificationSettingPanelTitle: 'Préférences de notification',
	NotificationSettingSchedulingDescription: `Recevez des notifications lorsqu'un membre de l'équipe ou un client réserve, reprogramme ou annule son rendez-vous.`,
	NotificationSettingSchedulingTitle: 'Planification',
	NotificationSettingUpdateSuccess: 'Paramètres de notification mis à jour avec succès',
	NotificationSettingWhereYouReceiveNotifications: 'Où souhaitez-vous recevoir ces notifications',
	NotificationSettingWorkspaceDescription: `Recevez des notifications concernant les changements de système, les problèmes, les transferts de données et les rappels d'abonnement.`,
	NotificationSettingWorkspaceTitle: 'Espace de travail',
	NotificationTemplateUpdateFailed: 'Échec de la mise à jour du modèle de notification',
	NotificationTemplateUpdateSuccess: 'Modèle de notification mis à jour avec succès',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Souhaitez-vous envoyer un e-mail de notification d’annulation aux participants ?',
	NotifyAttendeesOfTaskCancellationModalTitle: `Envoyer l'annulation`,
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Souhaitez-vous envoyer un e-mail de notification de confirmation aux participants ?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Envoyer une confirmation',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Souhaitez-vous envoyer des e-mails d’annulation aux participants ?',
	NotifyAttendeesOfTaskMissingEmails: `{names} {count, plural, one {ne} other {ne}} possèdent pas d'adresse e-mail et ne recevront donc pas de notifications et de rappels automatiques.`,
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {n’a} other {n’ont}} pas d’adresse électronique et ne recevront donc pas de notifications et de rappels automatiques.',
	NotifyAttendeesOfTaskModalTitle: 'Souhaitez-vous envoyer un e-mail de notification aux participants ?',
	NotifyAttendeesOfTaskSnackbar: `Envoi d'une notification`,
	NuclearMedicineTechnologist: 'Technologue en médecine nucléaire',
	NumberOfClaims: '{number, plural, one {# Réclamation} other {# Réclamations}}',
	NumberOfClients: '{number, plural, one {# Client} other {# Clients}}',
	NumberOfContacts: '{number, plural, one {# Contact} other {# Contacts}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entry} other {entries}} trouvés',
	NumberOfErrors: '{count, plural, one {# erreur} other {# erreurs}}',
	NumberOfInvoices: '{number, plural, one {# Facture} other {# Factures}}',
	NumberOfLineitemsToCredit: 'Vous avez <mark>{count} {count, plural, one {ligne} other {lignes}}</mark> à créditer.',
	NumberOfPayments: '{number, plural, one {# Paiement} other {# Paiements}}',
	NumberOfRelationships: '{number, plural, one {# Relation} other {# Relations}}',
	NumberOfResources: '{number, plural, one {# Ressource} other {# Ressources}}',
	NumberOfTeamMembers: '{number, plural, one {# Membre de l’équipe} other {# Membres de l’équipe}}',
	NumberOfTrashItems: '{number, plural, one {# élément} other {# éléments}}',
	NumberOfUninvoicedAmounts:
		'Vous avez <mark>{count} {count, plural, one {montant} other {montants}} non facturé{count, plural, one {} other {s}}</mark> à facturer',
	NumberedList: 'Liste numérotée',
	Nurse: 'Infirmière',
	NurseAnesthetist: 'Infirmière anesthésiste',
	NurseAssistant: 'Infirmière assistante',
	NurseEducator: 'Infirmière éducatrice',
	NurseMidwife: 'Infirmière sage-femme',
	NursePractitioner: 'Infirmière praticienne',
	Nurses: 'Infirmières',
	Nursing: 'Allaitement',
	Nutritionist: 'Nutritionniste',
	Nutritionists: 'Nutritionnistes',
	ObstetricianOrGynecologist: 'Gynécologue obstétricien',
	Occupation: 'Profession',
	OccupationalTherapist: 'Ergothérapeute',
	OccupationalTherapists: 'Ergothérapeutes',
	OccupationalTherapy: 'Ergothérapie',
	Occurrences: 'Occurrences',
	Of: 'de',
	Ohio: 'Ohio',
	OldPassword: 'Ancien mot de passe',
	OlderMessages: '{count} messages plus anciens',
	Oldest: 'Le plus ancien',
	OldestUnreplied: 'Le plus ancien sans réponse',
	On: 'sur',
	OnboardingBusinessAgreement: `Au nom de moi-même et de l'entreprise, j'accepte le {businessAssociateAgreement}`,
	OnboardingLoadingOccupationalTherapist:
		'<mark>Ergothérapeutes</mark> représentent un quart de nos clients sur Carepatron',
	OnboardingLoadingProfession:
		'Nous avons des tonnes de <mark>{profession}</mark> qui utilisent et prospèrent sur Carepatron.',
	OnboardingLoadingPsychologist:
		'<mark>Psychologues</mark> représentent plus de la moitié de nos clients sur Carepatron',
	OnboardingLoadingSubtitleFive:
		'Notre mission est de faire<mark> logiciel de santé accessible</mark> à tout le monde.',
	OnboardingLoadingSubtitleFour:
		'<mark>Logiciel de santé simplifié</mark> pour plus de 10 000 personnes dans le monde.',
	OnboardingLoadingSubtitleThree: `Sauvegarder<mark> 1 jour par semaine</mark> sur des tâches administratives avec l'aide de Carepatron.`,
	OnboardingLoadingSubtitleTwo: `Sauvegarder<mark> 2 heures</mark> quotidiennement sur des tâches administratives avec l'aide de Carepatron.`,
	OnboardingReviewLocationOne: 'Centre de santé mentale Holland Park',
	OnboardingReviewLocationThree: 'Infirmière praticienne, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Clinique de la Maison de la Vie',
	OnboardingReviewNameOne: 'Anul P.',
	OnboardingReviewNameThree: 'Alice E.',
	OnboardingReviewNameTwo: 'Clara W.',
	OnboardingReviewOne: `&quot;Carepatron est super intuitif à utiliser. Il nous aide si bien à gérer notre cabinet que nous n'avons même plus besoin d'une équipe d'administrateurs&quot;`,
	OnboardingReviewThree: `&quot;C'est la solution la plus performante que j'ai utilisée, tant en termes de fonctionnalités que de coût. Elle contient tout ce dont j'ai besoin pour développer mon entreprise.&quot;`,
	OnboardingReviewTwo: `&quot;J'aime aussi l'application Carepatron. Elle m'aide à suivre mes clients et à travailler lors de mes déplacements.&quot;`,
	OnboardingTitle: `Venons-en à<mark> savoir
 tu ferais mieux</mark>`,
	Oncologist: 'Oncologiste',
	Online: 'En ligne',
	OnlineBookingColorTheme: 'Thème de couleur de réservation en ligne',
	OnlineBookings: 'Réservations en ligne',
	OnlineBookingsHelper:
		'Choisissez quand les réservations en ligne peuvent être effectuées et par quel type de clients',
	OnlinePayment: 'Paiement en ligne',
	OnlinePaymentSettingCustomInfo:
		'Les paramètres de paiement en ligne pour ce service diffèrent des paramètres de réservation globaux.',
	OnlinePaymentSettings: 'Paramètres de paiement en ligne',
	OnlinePaymentSettingsInfo:
		'Collectez les paiements des services au moment de la réservation en ligne pour sécuriser et rationaliser les paiements',
	OnlinePaymentSettingsPaymentsDisabled:
		'Les paiements sont désactivés et ne peuvent donc pas être collectés lors de la réservation en ligne. Veuillez vérifier vos paramètres de paiement pour activer les paiements.',
	OnlinePaymentSettingsStripeNote:
		'{action} pour recevoir les paiements de réservation en ligne et rationaliser votre processus de paiement',
	OnlinePaymentsNotSupportedForCurrency: 'Les paiements en ligne ne sont pas pris en charge dans {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Désolé, les paiements en ligne ne sont pas pris en charge dans cette devise',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Désolé, les paiements en ligne ne sont pas encore pris en charge dans votre pays',
	OnlineScheduling: 'Planification en ligne',
	OnlyVisibleToYou: 'Uniquement visible pour vous',
	OnlyYou: 'Seulement vous',
	OnsetDate: 'Date de début',
	OnsetOfCurrentSymptomsOrIllness: 'Apparition des symptômes ou de la maladie actuels',
	Open: 'Ouvrir',
	OpenFile: 'Fichier ouvert',
	OpenSettings: 'Ouvrir les paramètres',
	Ophthalmologist: 'Ophtalmologiste',
	OptimiseTelehealthCalls: 'Optimisez vos appels de télésanté',
	OptimizeServiceTimes: 'Optimiser les temps de service',
	Options: 'Possibilités',
	Optometrist: 'Optométriste',
	Or: 'ou',
	OrAttachSingleFile: 'joindre un fichier',
	OrDragAndDrop: 'ou faites glisser-déposer',
	OrderBy: 'Commandé par',
	Oregon: 'Oregon',
	OrganisationOrIndividual: 'Organisation ou individu',
	OrganizationPlanInclusion1: 'Autorisations avancées',
	OrganizationPlanInclusion2: `Prise en charge gratuite de l'importation de données client`,
	OrganizationPlanInclusion3: 'Gestionnaire de réussite dédié',
	OrganizationPlanInclusionHeader: 'Tout en Professionnel, plus...',
	Orthodontist: 'Orthodontiste',
	Orthotist: 'Orthésiste',
	Other: 'Autre',
	OtherAdjustments: 'Autres ajustements',
	OtherAdjustmentsTableEmptyState: 'Aucun ajustement trouvé',
	OtherEvents: 'Autres événements',
	OtherId: `Autre pièce d'identité`,
	OtherIdQualifier: `Autre qualificatif d'identification`,
	OtherPaymentMethod: 'Autre méthode de paiement',
	OtherPlanMessage: `Restez maître des besoins de votre cabinet. Passez en revue votre plan actuel, surveillez l'utilisation et explorez les options de mise à niveau pour débloquer plus de fonctionnalités au fur et à mesure que votre équipe grandit.`,
	OtherPolicy: 'Autres assurances',
	OtherProducts: 'Quels autres produits ou outils utilisez-vous ?',
	OtherServices: 'Autres services',
	OtherTemplates: 'Autres modèles',
	Others: 'Autres',
	OthersPeople: `{n, plural, 		one {1 autre personne}
		other {# autres personnes}
	}`,
	OurResearchTeamReachOut:
		'Notre équipe de recherche peut-elle vous contacter pour en savoir plus sur la manière dont Carepatron aurait pu mieux répondre à vos besoins ?',
	OutOfOffice: 'Absent du bureau',
	OutOfOfficeColor: `Couleur d'absence du bureau`,
	OutOfOfficeHelper: `Certains membres de l'équipe choisis sont absents`,
	OutsideLabCharges: 'Frais de laboratoire externe',
	OutsideOfWorkingHours: 'En dehors des heures de travail',
	OutsideWorkingHoursHelper: `Certains membres de l'équipe choisis sont en dehors des heures de travail`,
	Overallocated: 'Surutilisé',
	OverallocatedPaymentDescription: `Ce paiement a été suraffecté à des éléments facturables.
 Ajoutez une allocation aux éléments non payés ou émettez un crédit ou un remboursement.`,
	OverallocatedPaymentTitle: 'Paiement sur-affecté',
	OverdueTerm: 'Délai de retard (jours)',
	OverinvoicedAmount: 'Montant surfacturé',
	Overpaid: 'Surpayé',
	OverpaidAmount: 'Montant payé en trop',
	Overtime: 'au fil du temps',
	Owner: 'Propriétaire',
	POS: 'PDV',
	POSCode: 'Code de point de vente',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Les modifications non enregistrées seront perdues. Voulez-vous quand même quitter ?',
	PageBlockerTitle: 'Jeter les modifications ?',
	PageFormat: 'Format de page',
	PageNotFound: 'Page non trouvée',
	PageNotFoundDescription: `Vous n'avez plus accès à cette page ou elle est introuvable`,
	PageUnauthorised: 'Accès non autorisé',
	PageUnauthorisedDescription: `Vous n'avez pas la permission d'accéder à cette page`,
	Paid: 'Payé',
	PaidAmount: 'Montant payé',
	PaidAmountMinimumValueError: 'Le montant payé doit être supérieur à 0',
	PaidAmountRequiredError: 'Le montant payé est requis',
	PaidItems: 'Éléments payés',
	PaidMultiple: 'Payé',
	PaidOut: 'Payé',
	ParagraphStyles: 'Styles de paragraphe',
	Parent: 'Mère',
	Paris: 'Paris',
	PartialRefundAmount: 'Remboursé partiellement ({amount} restant)',
	PartiallyFull: 'Partiellement plein',
	PartiallyPaid: 'Partiellement payé',
	PartiallyRefunded: 'Partiellement remboursé',
	Partner: 'Partenaire',
	Password: 'Mot de passe',
	Past: 'Passé',
	PastDateOverridesEmpty: `Vos remplacements de date apparaîtront ici dès que l'événement sera passé`,
	Pathologist: 'Pathologiste',
	Patient: 'Patient',
	Pause: 'Pause',
	Paused: 'En pause',
	Pay: 'Payer',
	PayMonthly: 'Payez mensuellement',
	PayNow: 'Payez maintenant',
	PayValue: 'Payer {showPrice, select, true {{price}} other {maintenant}}',
	PayWithOtherCard: 'Payer avec une autre carte',
	PayYearly: 'Payez annuellement',
	PayYearlyPercentOff: 'Payez annuellement <mark>{percent}% de réduction</mark>',
	Payer: 'Payeur',
	PayerClaimId: `Payer l'ID de la réclamation`,
	PayerCoverage: 'Couverture',
	PayerDetails: 'Détails du payeur',
	PayerDetailsDescription: `Consultez les détails des payeurs qui ont été ajoutés à votre compte et gérez l'inscription.`,
	PayerID: 'Identifiant du payeur',
	PayerId: 'Identifiant du payeur',
	PayerName: 'Nom du payeur',
	PayerPhoneNumber: 'Numéro de téléphone du payeur',
	Payers: 'Payeurs',
	Payment: 'Paiement',
	PaymentAccountUpdated: 'Votre compte a été mis à jour!',
	PaymentAccountUpgraded: 'Votre compte a été mis à jour !',
	PaymentAmount: 'Montant du paiement',
	PaymentDate: 'Date de paiement',
	PaymentDetails: 'Détails du paiement',
	PaymentForUsersPerMonth: 'Paiement pour {billedUsers, plural, one {# utilisateur} other {# utilisateurs}} par mois',
	PaymentInfoFormPrimaryText: 'Informations de paiement',
	PaymentInfoFormSecondaryText: 'Recueillir les détails du paiement',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Cette facture a déjà été payée.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Cette facture est déjà en cours de traitement.',
	PaymentIntentAmountMismatchSnackbar:
		'Le montant total de la facture a été modifié. Veuillez examiner les modifications avant de payer.',
	PaymentIntentSyncTimeoutSnackbar: `Votre paiement a réussi mais un délai d'attente s'est produit. Veuillez actualiser la page et si votre paiement n'apparaît pas, veuillez contacter le support.`,
	PaymentMethod: 'Mode de paiement',
	PaymentMethodDescription: `Ajoutez et gérez le mode de paiement de votre cabinet pour rationaliser votre processus de facturation d'abonnement.`,
	PaymentMethodLabelBank: 'compte bancaire',
	PaymentMethodLabelCard: 'carte',
	PaymentMethodLabelFallback: 'mode de paiement',
	PaymentMethodRequired: 'Veuillez ajouter un mode de paiement avant de modifier les abonnements',
	PaymentMethods: 'Méthodes de payement',
	PaymentProcessing: 'Traitement des paiements !',
	PaymentProcessingFee: 'Le paiement inclut des frais de traitement de {amount}',
	PaymentReports: 'Rapports de paiement (ERA)',
	PaymentSettings: 'Paramètres de paiement',
	PaymentSuccessful: 'Paiement réussi !',
	PaymentType: 'Type de paiement',
	Payments: 'Paiements',
	PaymentsAccountDisabledNotificationSubject: `Les paiements en ligne via {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} ont été désactivés.
Veuillez vérifier vos paramètres de paiement pour activer les paiements.`,
	PaymentsEmptyStateDescription: `Aucun paiement n'a été trouvé.`,
	PaymentsUnallocated: 'Paiements non affectés',
	PayoutDate: 'Date de paiement',
	PayoutsDisabled: 'Paiements désactivés',
	PayoutsEnabled: 'Paiements activés',
	PayoutsStatus: 'Statut de paiement',
	Pediatrician: 'Pédiatre',
	Pen: 'Stylo',
	Pending: 'En attente',
	People: '{rosterSize } personnes',
	PeopleCount: 'Personnes ({count})',
	PerMonth: '/ Mois',
	PerUser: 'Par utilisateur',
	Permission: 'Autorisation',
	PermissionRequired: 'Autorisation requise',
	Permissions: 'Autorisations',
	PermissionsClientAndContactDocumentation: 'Client ',
	PermissionsClientAndContactProfiles: 'Client ',
	PermissionsEditAccess: `Modifier l'accès`,
	PermissionsInvoicesAndPayments: 'Factures ',
	PermissionsScheduling: 'Planification',
	PermissionsUnassignClients: `Annuler l'attribution de clients`,
	PermissionsUnassignClientsConfirmation: `Êtes-vous sûr de vouloir annuler l'attribution de ces clients ?`,
	PermissionsValuesAssigned: 'Attribué uniquement',
	PermissionsValuesEverything: 'Tout',
	PermissionsValuesNone: 'Aucun',
	PermissionsValuesOwnCalendar: 'Propre calendrier',
	PermissionsViewAccess: `Afficher l'accès`,
	PermissionsWorkspaceSettings: `Paramètres de l'espace de travail`,
	Person: '{rosterSize} personne',
	PersonalDetails: 'Détails personnels',
	PersonalHealthcareHistoryStoreDescription:
		'Répondez et stockez en toute sécurité vos antécédents médicaux personnels en un seul endroit',
	PersonalTrainer: 'Entraîneur personnel',
	PersonalTraining: 'Formation personnelle',
	PersonalizeWorkspace: 'Personnalisez votre espace de travail',
	PersonalizingYourWorkspace: 'Personnalisation de votre espace de travail',
	Pharmacist: 'Pharmacien',
	Pharmacy: 'Pharmacie',
	PhoneCall: 'Appel téléphonique',
	PhoneNumber: 'Numéro de téléphone',
	PhoneNumberOptional: 'Numéro de téléphone (facultatif)',
	PhotoBy: 'photo par',
	PhysicalAddress: 'Adresse physique',
	PhysicalTherapist: 'Physiothérapeute',
	PhysicalTherapists: 'Physiothérapeutes',
	PhysicalTherapy: 'Thérapie physique',
	Physician: 'Médecin',
	PhysicianAssistant: 'Médecin assistant',
	Physicians: 'Médecins',
	Physiotherapist: 'Physiothérapeute',
	PlaceOfService: 'Lieu de prestation',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Rapport',
	PlanId: 'ID du plan',
	PlansAndReportsCategoryDescription: 'Pour la planification du traitement et la synthèse des résultats',
	PleaseRefreshThisPageToTryAgain: 'Veuillez rafraîchir cette page pour réessayer.',
	PleaseWait: `S'il vous plaît, attendez...`,
	PleaseWaitForHostToJoin: `En attente de l'adhésion de l'hôte...`,
	PleaseWaitForHostToStart: `Veuillez attendre que l'hôte démarre cette réunion.`,
	PlusAdd: '+ Ajouter',
	PlusOthers: '+{count} autres',
	PlusPlanInclusionFive: 'Boîtes de réception partagées',
	PlusPlanInclusionFour: 'Appels vidéo de groupe',
	PlusPlanInclusionHeader: `Tout dans l'essentiel  `,
	PlusPlanInclusionOne: 'IA illimitée',
	PlusPlanInclusionSix: 'Image de marque personnalisée',
	PlusPlanInclusionThree: 'Planification de groupe',
	PlusPlanInclusionTwo: 'Stockage illimité ',
	PlusSubscriptionPlanSubtitle: `Pour que les pratiques s'optimisent et se développent`,
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Officier de police',
	PolicyDates: 'Dates de la politique',
	PolicyHolder: `Titulaire de la police d'assurance`,
	PolicyHoldersAddress: 'Adresse du bénéficiaire',
	PolicyMemberId: `Numéro d'identification du membre de la police`,
	PolicyStatus: 'Statut de la politique',
	Popular: 'Populaire',
	PortalAccess: 'Accès au portail',
	PortalNoAppointmentsHeading: 'Suivez tous les rendez-vous futurs et passés',
	PortalNoDocumentationHeading: 'Créez et stockez vos documents en toute sécurité',
	PortalNoRelationshipsHeading: 'Rassemblez ceux qui soutiennent votre parcours',
	PosCodeErrorMessage: 'Code POS requis',
	PosoNumber: 'Numéro de bon de commande/SO',
	PossibleClientDuplicate: 'Possible doublon de client',
	PotentialClientDuplicateTitle: 'Dossier client potentiel en double',
	PotentialClientDuplicateWarning: `Ces informations client existent peut-être déjà dans votre liste de clients. Veuillez vérifier et mettre à jour l'enregistrement existant si nécessaire ou continuer à créer un nouveau client.`,
	PoweredBy: 'Alimenté par',
	Practice: 'Pratique',
	PracticeDetails: 'Détails de la pratique',
	PracticeInfoHeader: 'Informations commerciales',
	PracticeInfoPlaceholder: `Nom de pratique,
 Identifiant national du fournisseur,
 Numéro d’identification de l’employeur`,
	PracticeLocation: 'On dirait que votre cabinet est en',
	PracticeSettingsAvailabilityTab: 'Disponibilité',
	PracticeSettingsBillingTab: 'Paramètres de facturation',
	PracticeSettingsClientSettingsTab: 'Paramètres clients',
	PracticeSettingsGeneralTab: 'Général',
	PracticeSettingsOnlineBookingTab: 'Reservation en ligne',
	PracticeSettingsServicesTab: 'Prestations de service',
	PracticeSettingsTaxRatesTab: `Les taux d'imposition`,
	PracticeTemplate: 'Modèle de pratique',
	Practitioner: 'Praticien',
	PreferredLanguage: 'Langue préférée',
	PreferredName: 'Nom préféré',
	Prescription: 'Ordonnance',
	PreventionSpecialist: 'Spécialiste de la prévention',
	Preview: 'Aperçu',
	PreviewAndSend: 'Prévisualiser et envoyer',
	PreviewUnavailable: 'Aperçu indisponible pour ce type de fichier',
	PreviousNotes: 'Notes précédentes',
	Price: 'Prix',
	PriceError: 'Le prix doit être supérieur à 0',
	PricePerClient: 'Prix par client',
	PricePerUser: 'Par utilisateur',
	PricePerUserBilledAnnually: 'Par utilisateur facturé annuellement',
	PricePerUserPerPeriod: '{price} par utilisateur / {isMonthly, select, true {mois} other {année}}',
	PricingGuide: 'Guide des plans tarifaires',
	PricingPlanPerMonth: '/ mois',
	PricingPlanPerYear: '/ année',
	Primary: 'Primaire',
	PrimaryInsurance: 'Assurance primaire',
	PrimaryPolicy: 'Assurance primaire',
	PrimaryTimezone: 'Fuseau horaire principal',
	Print: 'Imprimer',
	PrintToCms1500: 'Imprimer sur CMS1500',
	PrivatePracticeConsultant: 'Consultant en pratique privée',
	Proceed: 'Procéder',
	ProcessAtTimeOfBookingDesc: 'Les clients doivent payer le prix complet du service pour réserver en ligne',
	ProcessAtTimeOfBookingLabel: 'Traiter les paiements au moment de la réservation',
	Processing: 'Traitement',
	ProcessingFee: 'Frais de traitement',
	ProcessingFeeToolTip: `Carepatron vous permet de facturer les frais de traitement à vos clients.
 Dans certaines juridictions, il est interdit de facturer des frais de traitement à vos clients. Il est de votre responsabilité de respecter les lois applicables.`,
	ProcessingRequest: 'Traitement de la demande...',
	Product: 'Produit',
	Profession: 'Profession',
	ProfessionExample: 'Thérapeute, nutritionniste, dentiste',
	ProfessionPlaceholder: 'Commencez à saisir votre profession ou choisissez dans la liste',
	ProfessionalPlanInclusion1: 'Stockage illimité',
	ProfessionalPlanInclusion2: 'Tâches illimitées',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Assistance client 24h/24 et 7j/7',
	ProfessionalPlanInclusion5: 'Rappels SMS',
	ProfessionalPlanInclusionHeader: 'Tout dans Starter, plus...',
	Professions: 'Métiers',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: 'La taille doit être inférieure à 5 Mo',
	ProfilePopoverSubTitle: 'Vous êtes connecté en tant que <strong>{email}</strong>',
	ProfilePopoverTitle: 'Vos espaces de travail',
	PromoCode: 'Code promo',
	PromotionCodeApplied: '{promo} appliqué',
	ProposeNewDateTime: 'Proposer une nouvelle date/heure',
	Prosthetist: 'Prothésiste',
	Provider: 'Fournisseur',
	ProviderBillingPlanExpansionManageButton: 'Gérer le forfait',
	ProviderCommercialNumber: 'Numéro commercial du fournisseur',
	ProviderDetails: 'Détails du fournisseur',
	ProviderDetailsAddress: 'Adresse',
	ProviderDetailsName: 'Nom',
	ProviderDetailsPhoneNumber: 'Numéro de téléphone',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Désolé, ce fournisseur possède déjà un compte de facturation existant',
	ProviderInfoPlaceholder: `Nom du personnel,
 Adresse e-mail,
 Numéro de téléphone,
 Identifiant national du fournisseur,
 Numéro de licence`,
	ProviderIsChargedProcessingFee: 'Vous paierez les frais de traitement',
	ProviderPaymentFormBackButton: 'Dos',
	ProviderPaymentFormBillingAddressCity: 'Ville',
	ProviderPaymentFormBillingAddressCountry: 'Pays',
	ProviderPaymentFormBillingAddressLine1: 'Ligne 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Code Postal',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Détails de la carte de crédit',
	ProviderPaymentFormCardExpiry: 'Expiration',
	ProviderPaymentFormCardHolderAddressTitle: 'Adresse',
	ProviderPaymentFormCardHolderName: 'Nom du titulaire',
	ProviderPaymentFormCardHolderTitle: 'Détails du titulaire de la carte',
	ProviderPaymentFormCardNumber: 'Numéro de carte',
	ProviderPaymentFormPlanTitle: 'Plan choisi',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}):',
	ProviderPaymentFormSaveButton: `Enregistrer l'abonnement`,
	ProviderPaymentFreePlanDescription: `Le choix du forfait gratuit supprimera l'accès de chaque membre du personnel à ses clients chez votre fournisseur. Cependant, votre accès restera et vous pourrez toujours utiliser la plateforme.`,
	ProviderPaymentStepName: 'Revoir ',
	ProviderPaymentSuccessSnackbar: 'Super! Votre nouveau forfait a été enregistré avec succès.',
	ProviderPaymentTitle: 'Revoir ',
	ProviderPlanNetworkIdentificationNumber: `Numéro d'identification du réseau du plan du fournisseur`,
	ProviderRemindersSettingsBannerAction: 'Aller à la gestion des workflows',
	ProviderRemindersSettingsBannerDescription: `Retrouvez tous les rappels sous le nouvel onglet **Gestion des workflows** dans **Paramètres**. Cette mise à jour apporte de nouvelles fonctionnalités puissantes, une amélioration des modèles et des outils d'automatisation plus intelligents pour booster votre productivité. 🚀`,
	ProviderRemindersSettingsBannerTitle: `Votre expérience de rappel s'améliore`,
	ProviderTaxonomy: 'Taxonomie des fournisseurs',
	ProviderUPINNumber: 'Numéro UPIN du fournisseur',
	ProviderUsedStoragePercentage: 'Le stockage de {providerName} est plein à {usedStoragePercentage} % !',
	PsychiatricNursePractitioner: 'Infirmière praticienne en psychiatrie',
	Psychiatrist: 'Psychiatre',
	Psychiatrists: 'Psychiatres',
	Psychiatry: 'Psychiatrie',
	Psychoanalyst: 'Psychanalyste',
	Psychologist: 'Psychologue',
	Psychologists: 'Psychologues',
	Psychology: 'Psychologie',
	Psychometrician: 'Psychométricien',
	PsychosocialRehabilitationSpecialist: 'Spécialiste en réadaptation psychosociale',
	Psychotheraphy: 'Psychothérapie',
	Psychotherapists: 'Psychothérapeutes',
	Psychotherapy: 'Psychothérapie',
	PublicCallDialogTitle: 'Appel vidéo avec ',
	PublicCallDialogTitlePlaceholder: 'Appel vidéo alimenté par Carepatron',
	PublicFormBackToForm: 'Soumettre une autre réponse',
	PublicFormConfirmSubmissionHeader: 'Confirmer la soumission',
	PublicFormNotFoundDescription: `Le formulaire que vous recherchez a peut-être été supprimé ou le lien peut être incorrect. Veuillez vérifier l'URL et réessayer.`,
	PublicFormNotFoundTitle: 'Formulaire introuvable',
	PublicFormSubmissionError: 'Soumission échouée. Veuillez réessayer.',
	PublicFormSubmissionSuccess: 'Formulaire envoyé avec succès',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} a soumis le formulaire public {noteTitle}',
	PublicFormSubmittedSubtitle: 'Votre soumission a été reçue.',
	PublicFormSubmittedTitle: 'Merci!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Nous avons envoyé un code de confirmation à votre adresse e-mail.',
	PublicFormsInvalidConfirmationCode: 'Code de confirmation invalide',
	PublicHealthInspector: 'Inspecteur de la santé publique',
	PublicTemplates: 'Modèles publics',
	Publish: 'Publier',
	PublishTemplate: 'Publier le modèle',
	PublishTemplateFeatureBannerSubheader: 'Modèles conçus pour bénéficier à la communauté',
	PublishTemplateHeader: 'Publier {title}',
	PublishTemplateToCommunity: 'Publier le modèle à la communauté',
	PublishToCommunity: 'Publier sur la communauté',
	PublishToCommunitySuccessMessage: 'Publié avec succès dans la communauté',
	Published: 'Publié',
	PublishedBy: 'Publié par {name}',
	PublishedNotesAreNotAutosaved: 'Les notes publiées ne seront pas enregistrées automatiquement',
	PublishedOnCarepatronCommunity: 'Publié sur la communauté Carepatron',
	Purchase: 'Achat',
	PushToCalendar: 'Pousser vers le calendrier',
	Question: 'Question',
	QuestionOrTitle: 'Question ou titre',
	QuickActions: 'Actions rapides',
	QuickThemeSwitcherColorBasil: 'Basilic',
	QuickThemeSwitcherColorBlueberry: 'Myrtille',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Mousse',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Courge',
	RadiationTherapist: 'Radiothérapeute',
	Radiologist: 'Radiologue',
	Read: 'Lire',
	ReadOnly: 'En lecture seule',
	ReadOnlyAppointment: 'Rendez-vous en lecture seule',
	ReadOnlyEventBanner: `Ce rendez-vous est synchronisé à partir d'un calendrier en lecture seule et ne peut pas être modifié.`,
	ReaderMaxDepthHasBeenExceededCode: `La note est trop imbriquée. Essayez de supprimer l'indentation de certains éléments.`,
	ReadyForMapping: 'Prêt pour la cartographie',
	RealEstateAgent: 'Agent immobilier',
	RearrangeClientFields: 'Réorganiser les champs client dans les paramètres client',
	Reason: 'Raison',
	ReasonForChange: 'Raison du changement',
	RecentAppointments: 'Nominations récentes',
	RecentServices: 'Services récents',
	RecentTemplates: 'Modèles récents',
	RecentlyUsed: 'Utilisé récemment',
	Recommended: 'Recommandé',
	RecommendedTemplates: 'Modèles recommandés',
	Recording: 'Enregistrement',
	RecordingEnded: 'Enregistrement terminé',
	RecordingInProgress: 'Enregistrement en cours',
	RecordingMicrophoneAccessErrorMessage: `Veuillez autoriser l'accès au microphone dans votre navigateur et actualiser pour démarrer l'enregistrement.`,
	RecurrenceCount: ', {count, plural, one {une fois} other {# fois}}',
	RecurrenceDaily: '{count, plural, one {Quotidien} other {Jours}}',
	RecurrenceEndAfter: 'Après',
	RecurrenceEndNever: 'Jamais',
	RecurrenceEndOn: 'Sur',
	RecurrenceEvery: 'Chaque {description}',
	RecurrenceMonthly: '{count, plural, one {Mensuel} other {Mois}}',
	RecurrenceOn: 'sur {description}',
	RecurrenceOnAllDays: 'tous les jours',
	RecurrenceUntil: `jusqu'à {description}`,
	RecurrenceWeekly: '{count, plural, one {Hebdomadaire} other {Semaines}}',
	RecurrenceYearly: '{count, plural, one {Annuel} other {Années}}',
	Recurring: 'Récurrent',
	RecurringAppointment: 'Rendez-vous récurrent',
	RecurringAppointmentsLimitedBannerText:
		'Tous les rendez-vous récurrents ne sont pas affichés. Réduisez la plage de dates pour voir tous les rendez-vous récurrents de la période.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# événement} other {# événements}}</b> seront créés aux dates suivantes',
	Redo: 'Refaire',
	ReferFriends: 'Parrainer des amis',
	Reference: 'Référence',
	ReferralCreditedNotificationSubject: 'Votre crédit de parrainage de {currency} {amount} a été appliqué',
	ReferralEmailDefaultBody: `Grâce à {name}, vous avez reçu une mise à niveau GRATUITE de 3 mois pour Carepatron. Rejoignez notre communauté de plus de 3 millions de professionnels de la santé conçue pour une nouvelle façon de travailler !
Merci,
L’équipe Carepatron`,
	ReferralEmailDefaultSubject: 'Vous avez été invité à rejoindre Carepatron',
	ReferralHasNotSignedUpDescription: `Votre ami n'est pas encore inscrit`,
	ReferralHasSignedUpDescription: `Votre ami s'est inscrit.`,
	ReferralInformation: 'Informations de référence',
	ReferralJoinedNotificationSubject: '{actorProfileName} a rejoint Carepatron',
	ReferralListErrorDescription: `La liste de références n'a pas pu être chargée.`,
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {mois} other {mois}}</b> actif',
	ReferralRewardBanner: 'Inscrivez-vous et réclamez votre récompense de parrainage !',
	Referrals: 'Références',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} mois {percentOff, select, 100 {gratuit payé} other {{percentOff}% de réduction}} {type, select, SubscriptionUpgrade {mise à niveau} other {}}',
	ReferredUserBenefitTitle: 'Ils obtiennent!',
	Referrer: 'Référent',
	ReferringProvider: 'Fournisseur référent',
	ReferringUserBenefitSubtitle: `Crédit de USD\${creditAmount} lorsque <mark>3 amis</mark> s'activent.`,
	ReferringUserBenefitTitle: 'Vous obtenez!',
	RefreshPage: 'Actualiser la page',
	Refund: 'Remboursement',
	RefundAcknowledgement: `J'ai remboursé {clientName} en dehors de Carepatron.`,
	RefundAcknowledgementValidationMessage: 'Veuillez confirmer que vous avez remboursé ce montant',
	RefundAmount: 'Montant du remboursement',
	RefundContent: `Les remboursements prennent 7 à 10 jours pour apparaître sur le compte de votre client. Les frais de paiement ne seront pas remboursés, mais aucun frais supplémentaire ne sera facturé pour les remboursements. Les remboursements ne peuvent pas être annulés et certains peuvent nécessiter un examen avant d'être traités.`,
	RefundCouldNotBeProcessed: `Le remboursement n'a pas pu être traité`,
	RefundError:
		'Ce remboursement ne peut pas être traité automatiquement pour le moment. Veuillez contacter le support Carepatron pour demander le remboursement de ce paiement.',
	RefundExceedTotalValidationError: 'Le montant ne doit pas dépasser le total payé',
	RefundFailed: 'Le remboursement a échoué',
	RefundFailedTooltip:
		'Le remboursement de ce paiement a échoué et ne peut pas être réessayé. Veuillez contacter le support.',
	RefundNonStripePaymentContent: `Ce paiement a été effectué par un moyen de paiement extérieur à Carepatron (par exemple, en espèces, par banque en ligne). L'émission d'un remboursement au sein de Carepatron ne restituera aucun fonds au client.`,
	RefundReasonDescription: `L'ajout d'un motif de remboursement peut vous aider lors de l'examen des transactions de vos clients`,
	Refunded: 'Remboursé',
	Refunds: 'Remboursements',
	RefundsTableEmptyState: 'Aucun remboursement trouvé',
	Regenerate: 'Regénérer',
	RegisterButton: 'Registre',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Prénom',
	RegisterLastName: 'Nom de famille',
	RegisterPassword: 'Mot de passe',
	RegisteredNurse: 'Infirmière autorisée',
	RehabilitationCounselor: 'Conseiller en réadaptation',
	RejectAppointmentFormTitle:
		'Vous ne pouvez pas venir ? Merci de nous faire savoir pourquoi et de proposer une nouvelle heure.',
	Rejected: 'Rejeté',
	Relationship: 'Relation',
	RelationshipDetails: 'Détails de la relation',
	RelationshipEmptyStateTitle: 'Restez en contact avec ceux qui soutiennent votre client',
	RelationshipPageAccessTypeColumnName: 'Accès au profil',
	RelationshipSavedSuccessSnackbar: 'Relation enregistrée avec succès !',
	RelationshipSelectorFamilyAdmin: 'Famille',
	RelationshipSelectorFamilyMember: 'Membre de la famille',
	RelationshipSelectorProviderAdmin: 'Administrateur du fournisseur',
	RelationshipSelectorProviderStaff: 'Personnel du prestataire',
	RelationshipSelectorSupportNetworkPrimary: 'Ami',
	RelationshipSelectorSupportNetworkSecondary: 'Réseau de soutien',
	RelationshipStatus: 'Statut de la relation',
	RelationshipType: 'Type de relation',
	RelationshipTypeClientOwner: 'Client',
	RelationshipTypeFamilyAdmin: 'Des relations',
	RelationshipTypeFamilyMember: 'Famille',
	RelationshipTypeFriendOrSupport: 'Ami ou réseau de soutien',
	RelationshipTypeProviderAdmin: 'Administrateur du fournisseur',
	RelationshipTypeProviderStaff: 'Personnel',
	RelationshipTypeSelectorPlaceholder: 'Rechercher des types de relations',
	Relationships: 'Des relations',
	Remaining: 'restant',
	RemainingTime: '{time} restant',
	Reminder: 'Rappel',
	ReminderColor: 'Couleur de rappel',
	ReminderDetails: 'Détails du rappel',
	ReminderEditDisclaimer: 'Les changements ne seront reflétés que dans les nouvelles nominations',
	ReminderSettings: 'Paramètres de rappel de rendez-vous',
	Reminders: 'Rappels',
	Remove: 'Retirer',
	RemoveAccess: `Supprimer l'accès`,
	RemoveAllGuidesBtn: 'Supprimer tous les guides',
	RemoveAllGuidesPopoverBody: `Lorsque vous avez terminé avec les guides d'intégration, utilisez simplement le bouton de suppression des guides sur chaque panneau.`,
	RemoveAllGuidesPopoverTitle: `Vous n'avez plus besoin de vos guides d'intégration ?`,
	RemoveAsDefault: 'Supprimer par défaut',
	RemoveAsIntake: 'Retirer comme apport',
	RemoveCol: 'Supprimer la colonne',
	RemoveColor: 'Supprimer la couleur',
	RemoveField: 'Supprimer le champ',
	RemoveFromCall: `Supprimer de l'appel`,
	RemoveFromCallDescription: 'Êtes-vous sûr de vouloir supprimer <mark>{attendeeName}</mark> de cet appel vidéo ?',
	RemoveFromCollection: 'Retirer de la collection',
	RemoveFromCommunity: 'Supprimer de la communauté',
	RemoveFromFolder: 'Supprimer du dossier',
	RemoveFromFolderConfirmationDescription:
		'Êtes-vous sûr de vouloir supprimer ce modèle de ce dossier ? Cette action est irréversible, mais vous pouvez choisir de le déplacer de nouveau plus tard.',
	RemoveFromIntakeDefault: `Supprimer de la valeur par défaut de l'admission`,
	RemoveGuides: 'Supprimer les guides',
	RemoveMfaConfirmationDescription: `La suppression de l'authentification multifacteur (MFA) réduira la sécurité de votre compte. Voulez-vous continuer ?`,
	RemoveMfaConfirmationTitle: `Supprimer l'authentification multifacteur ?`,
	RemovePaymentMethodDescription: `Cela supprimera tout accès et toute utilisation future de ce mode de paiement.
 Cette action ne peut pas être annulée.`,
	RemoveRow: 'Supprimer la ligne',
	RemoveTable: 'Supprimer le tableau',
	RemoveTemplateAsDefaultIntakeSuccess: `Le modèle d'accueil par défaut {templateTitle} a été supprimé avec succès.`,
	RemoveTemplateFromCommunity: 'Supprimer le modèle de la communauté',
	RemoveTemplateFromFolder: '{templateTitle} supprimé avec succès de {folderTitle}',
	Rename: 'Renommer',
	RenderingProvider: 'Fournisseur de rendu',
	Reopen: 'Rouvrir',
	ReorderServiceGroupFailure: 'Échec de la réorganisation de la collection',
	ReorderServiceGroupSuccess: 'Collection réorganisée avec succès',
	ReorderServicesFailure: 'Échec de la réorganisation des services',
	ReorderServicesSuccess: 'Services réorganisés avec succès',
	ReorderYourServiceList: 'Réorganisez votre liste de services',
	ReorderYourServiceListDescription:
		'La façon dont vous organisez vos services et collections sera reflétée sur votre page de réservation en ligne, à la vue de tous vos clients !',
	RepeatEvery: 'Répéter chaque',
	RepeatOn: 'Répéter sur',
	Repeating: 'Répéter',
	Repeats: 'Répétitions',
	RepeatsEvery: 'Se répète toutes les',
	Rephrase: 'Reformuler',
	Replace: 'Remplacer',
	ReplaceBackground: `Remplacer l'arrière-plan`,
	ReplacementOfPriorClaim: 'Remplacement de la demande antérieure',
	Report: 'Rapport',
	Reprocess: 'Retraiter',
	RepublishTemplateToCommunity: 'Republier le modèle à la communauté',
	RequestANewVerificationLink: 'Demander un nouveau lien de vérification',
	RequestCoverageReport: 'Demander un rapport de couverture',
	RequestingDevicePermissions: `Demande d'autorisations pour l'appareil...`,
	RequirePaymentMethodDesc: 'Les clients doivent saisir les détails de leur carte de crédit pour réserver en ligne',
	RequirePaymentMethodLabel: 'Exiger les détails de la carte de crédit',
	Required: 'requis',
	RequiredField: 'Requis',
	RequiredUrl: `L'URL est requise.`,
	Reschedule: 'Reprogrammer',
	RescheduleBookingLinkModalDescription: `Votre client peut modifier la date et l'heure de son rendez-vous en utilisant ce lien.`,
	RescheduleBookingLinkModalTitle: 'Lien pour reprogrammer la réservation',
	RescheduleLink: 'Lien de reprogrammation',
	Resend: 'Renvoyer',
	ResendConfirmationCode: 'Renvoyer le code de confirmation',
	ResendConfirmationCodeDescription:
		'Veuillez entrer votre adresse e-mail et nous vous enverrons un autre code de confirmation',
	ResendConfirmationCodeSuccess: 'Le code de confirmation a été renvoyé, veuillez vérifier votre boîte de réception',
	ResendNewEmailVerificationSuccess: 'Nouveau lien de vérification envoyé à {email}',
	ResendVerificationEmail: `Renvoyer l'e-mail de vérification`,
	Reset: 'Réinitialiser',
	Resources: 'Ressources',
	RespiratoryTherapist: 'Thérapeute de la respiration',
	RespondToHistoricAppointmentError: `Il s'agit d'un rendez-vous historique, merci de contacter votre praticien si vous avez une question.`,
	Responder: 'Répondeur',
	RestorableItemModalDescription:
		'Êtes-vous sûr de vouloir supprimer {context} ?{canRestore, select, true { Vous pouvez le restaurer plus tard.} other {}}',
	RestorableItemModalTitle: 'Supprimer {type}',
	Restore: 'Restaurer',
	RestoreAll: 'Restaurer tout',
	Restricted: 'Limité',
	ResubmissionCodeReferenceNumber: 'Code de nouvelle soumission et numéro de référence',
	Resubmit: 'Soumettre à nouveau',
	Resume: 'CV',
	Retry: 'Réessayer',
	RetryingConnectionAttempt: 'Nouvelle tentative de connexion... (Tentative {retryCount} sur {maxRetries})',
	ReturnToForm: 'Retour au formulaire',
	RevertClaimStatus: 'Remettre le statut de la réclamation',
	RevertClaimStatusDescriptionBody:
		'Cette demande comporte des paiements liés, et la modification du statut peut affecter le suivi ou le traitement des paiements, ce qui pourrait nécessiter une réconciliation manuelle.',
	RevertClaimStatusDescriptionTitle: 'Êtes-vous sûr de vouloir revenir à {status} ?',
	RevertClaimStatusError: `Échec de la restauration de l'état de la réclamation`,
	RevertToDraft: 'Revenir au brouillon',
	Review: 'Revoir',
	ReviewsFirstQuote: 'Rendez-vous partout et à tout moment',
	ReviewsSecondJobTitle: 'Clinique Lifehouse',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote: `J'adore aussi l'application Carepatron. M'aide à suivre mes clients et à travailler lors de mes déplacements.`,
	ReviewsThirdJobTitle: 'Clinique de la baie de Manille',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'La facilité de navigation et la belle interface utilisateur me font sourire au quotidien.',
	RightAlign: 'Aligner à droite',
	Role: 'Rôle',
	Roster: 'Participants',
	RunInBackground: 'Exécuter en arrière-plan',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Vous quittez Carepatron',
	SafetyRedirectSubtext: 'Si vous faites confiance à ce lien, sélectionnez-le pour continuer',
	SalesRepresentative: 'Représentant des ventes',
	SalesTax: 'Taxe de vente',
	SalesTaxHelp: 'Inclut la taxe de vente sur les factures générées',
	SalesTaxIncluded: 'Oui',
	SalesTaxNotIncluded: 'Non',
	SaoPaulo: 'São Paulo',
	Saturday: 'Samedi',
	Save: 'Sauvegarder',
	SaveAndClose: 'Sauvegarder ',
	SaveAndExit: 'Sauvegarder ',
	SaveAndLock: 'Enregistrer et verrouiller',
	SaveAsDraft: 'Enregistrer comme brouillon',
	SaveCardForFuturePayments: 'Enregistrez la carte pour les paiements futurs',
	SaveChanges: 'Sauvegarder les modifications',
	SaveCollection: 'Enregistrer la collection',
	SaveField: 'Enregistrer le champ',
	SavePaymentMethod: 'Enregistrer le mode de paiement',
	SavePaymentMethodDescription: 'Vous ne serez pas facturé avant votre premier rendez-vous.',
	SavePaymentMethodSetupError: `Une erreur inattendue s'est produite et nous n'avons pas pu configurer les paiements pour le moment.`,
	SavePaymentMethodSetupInvoiceLater:
		'Les paiements peuvent être configurés et enregistrés lors du paiement de votre première facture.',
	SaveSection: 'Enregistrer la section',
	SaveService: 'Créer un nouveau service',
	SaveTemplate: 'Enregistrer le modèle',
	Saved: 'Enregistré',
	SavedCards: 'Cartes enregistrées',
	SavedPaymentMethods: 'Enregistré',
	Saving: 'Économie...',
	ScheduleAppointmentsAndOnlineServices: 'Planifier des rendez-vous et des services en ligne',
	ScheduleName: 'Nom du emploi du temps',
	ScheduleNew: 'Programmer un nouveau',
	ScheduleSend: 'Envoyer programmé',
	ScheduleSendAlertInfo: 'Les conversations programmées seront envoyées à leur temps programmé.',
	ScheduleSendByName: '<strong>Envoi planifié</strong> • {time} par {displayName}',
	ScheduleSetupCall: `Planifier l'appel de configuration`,
	Scheduled: 'Programmé',
	SchedulingSend: `Planifier l'envoi`,
	School: 'École',
	ScrollToTop: 'Faire défiler vers le haut',
	Search: 'Recherche',
	SearchAndConvertToLanguage: 'Rechercher et convertir en langue',
	SearchBasicBlocks: 'Rechercher des blocs de base',
	SearchByName: 'Rechercher par nom',
	SearchClaims: 'Rechercher les réclamations',
	SearchClientFields: 'Recherche dans les champs du client',
	SearchClients: 'Rechercher par nom de client, identifiant client ou numéro de téléphone',
	SearchCommandNotFound: `Aucun résultat pour "{searchTerm}" n'a été trouvé.`,
	SearchContacts: 'Client ou contact',
	SearchContactsPlaceholder: 'Rechercher des contacts',
	SearchConversations: 'Rechercher des conversations',
	SearchInputPlaceholder: 'Rechercher toutes les ressources',
	SearchInvoiceNumber: 'Rechercher un numéro de facture',
	SearchInvoices: 'Rechercher des factures',
	SearchMultipleContacts: 'Clients ou contacts',
	SearchMultipleContactsOptional: 'Clients ou contacts (facultatif)',
	SearchOrCreateATag: 'Rechercher ou créer une balise',
	SearchPayments: 'Rechercher des paiements',
	SearchPrepopulatedData: 'Rechercher des champs de données pré-remplis',
	SearchRelationships: 'Rechercher des relations',
	SearchRemindersAndWorkflows: 'Rechercher des rappels et des flux de travail',
	SearchServices: 'Services de recherche',
	SearchTags: 'Rechercher des balises',
	SearchTeamMembers: `Rechercher des membres de l'équipe`,
	SearchTemplatePlaceholder: 'Rechercher {templateCount}+ ressources',
	SearchTimezone: 'Rechercher le fuseau horaire...',
	SearchTrashItems: 'Rechercher des éléments',
	SearchUnsplashPlaceholder: 'Recherchez des photos haute résolution gratuites sur Unsplash',
	Secondary: 'Secondaire',
	SecondaryInsurance: 'Assurance secondaire',
	SecondaryPolicy: 'Assurance secondaire',
	SecondaryTimezone: 'Fuseau horaire secondaire',
	Secondly: 'Deuxièmement',
	Section: 'Section',
	SectionCannotBeEmpty: 'Une section doit avoir au moins une ligne',
	SectionFormSecondaryText: 'Titre et description de la section',
	SectionName: 'Nom de la section',
	Sections: 'Sections',
	SeeLess: 'Voir moins',
	SeeLessUpcomingAppointments: 'Voir moins de rendez-vous à venir',
	SeeMore: 'Voir plus',
	SeeMoreUpcomingAppointments: 'Voir plus de rendez-vous à venir',
	SeeTemplateLibrary: 'Voir la bibliothèque de modèles',
	Seen: 'Vu',
	SeenByName: '**Vu** • {time} par {displayName}',
	SelectAll: 'Tout sélectionner',
	SelectAssignees: 'Sélectionner les destinataires',
	SelectAttendees: 'Sélectionner les participants',
	SelectCollection: 'Sélectionnez une collection',
	SelectCorrespondingAttributes: 'Sélectionnez les attributs correspondants',
	SelectPayers: 'Sélectionner les payeurs',
	SelectProfile: 'Choisissez un profil',
	SelectServices: 'Sélectionnez les prestations',
	SelectTags: 'Sélectionnez les balises',
	SelectTeamOrCommunity: 'Sélectionnez une équipe ou une communauté',
	SelectTemplate: 'Sélectionnez un modèle',
	SelectType: 'Sélectionner le genre',
	Selected: 'Choisi',
	SelfPay: 'Auto-paiement',
	Send: 'Envoyer',
	SendAndClose: 'Envoyer ',
	SendAndStopIgnore: 'Envoyer et arrêter d’ignorer',
	SendEmail: 'Envoyer un e-mail',
	SendIntake: `Envoyer l'admission`,
	SendIntakeAndForms: `Envoyer l'admission `,
	SendMeACopy: 'Envoie-moi une copie',
	SendNotificationEmailWarning: `Certains participants n'ont pas d'adresse électronique et ne recevront pas de notifications et de rappels automatisés.`,
	SendNotificationLabel: 'Choisissez les participants à notifier par e-mail',
	SendOnlinePayment: 'Envoyer le paiement en ligne',
	SendOnlinePaymentTooltipTitleAdmin: 'Veuillez ajouter vos paramètres de paiement préférés',
	SendOnlinePaymentTooltipTitleStaff:
		'Veuillez demander au propriétaire du fournisseur de configurer les paiements en ligne.',
	SendPaymentLink: 'Envoyer le lien de paiement',
	SendReaction: 'Envoyer une réaction',
	SendScheduledForDate: 'Envoyer programmé pour le {date}',
	SendVerificationEmail: 'Envoyer email de vérification',
	SendingFailed: `Échec de l'envoi`,
	Sent: 'Envoyé',
	SentByName: '**Envoyé** • {time} par {displayName}',
	Seoul: 'Séoul',
	SeparateDuplicateClientsDescription:
		'Les dossiers clients choisis resteront séparés du reste, sauf si vous choisissez de les fusionner',
	Service: 'Service',
	'Service/s': 'Prestations de service',
	ServiceAdjustment: 'Ajustement du service',
	ServiceAllowNewClientsIndicator: 'Autoriser les nouveaux clients',
	ServiceAlreadyExistsInCollection: 'Le service existe déjà dans la collection',
	ServiceBookableOnlineIndicator: 'Réservable en ligne',
	ServiceCode: 'Code',
	ServiceCodeErrorMessage: 'Un code de service est requis',
	ServiceCodeSelectorPlaceholder: 'Ajouter un code de service',
	ServiceColour: 'Couleur de service',
	ServiceCoverageDescription:
		'Choisissez les services admissibles et payez une quote-part pour cette police d’assurance.',
	ServiceCoverageGoToServices: 'Accéder aux services',
	ServiceCoverageNoServicesDescription:
		'Personnalisez les montants de la quote-part des services pour remplacer la quote-part par défaut de la police. Désactivez la couverture pour empêcher que les services ne soient réclamés dans le cadre de la police.',
	ServiceCoverageNoServicesLabel: `Aucun service n'a été trouvé.`,
	ServiceCoverageTitle: 'Couverture du service',
	ServiceDate: 'Date de service',
	ServiceDetails: 'Détails des services',
	ServiceDuration: 'Durée',
	ServiceEmptyState: `Il n'y a pas encore de services`,
	ServiceErrorMessage: 'Le service est requis',
	ServiceFacility: 'Centre de service',
	ServiceName: 'Nom du service',
	ServiceRate: 'Taux',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} pour {serviceReceiptNumber, select, undefined {user} other {{clientName}}} nécessite des informations supplémentaires',
	ServiceSalesTax: 'Taxe de vente',
	ServiceType: 'Service',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Appuyez sur recharger pour actualiser et obtenir les dernières mises à jour de Carepatron.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Recharger',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Vous utilisez une ancienne version',
	ServiceWorkerForceUIUpdateDialogTitle: 'Content de te revoir!',
	Services: 'Prestations de service',
	ServicesAndAvailability: 'Prestations de service ',
	ServicesAndDiagnosisCodesHeader: 'Ajouter des services et des codes de diagnostic',
	ServicesCount: '{count,plural,=0{Services}one{Service}other{Services}}',
	ServicesPlaceholder: 'Services',
	ServicesProvidedBy: 'Service(s) fourni(s) par',
	SetAPhysicalAddress: 'Définir une adresse physique',
	SetAVirtualLocation: 'Définir un emplacement virtuel',
	SetAsDefault: 'Définir par défaut',
	SetAsIntake: 'Définir comme admission',
	SetAsIntakeDefault: 'Définir comme entrée par défaut',
	SetAvailability: 'Définir la disponibilité',
	SetTemplateAsDefaultIntakeSuccess: `Modèle d'accueil par défaut défini avec succès sur {templateTitle}`,
	SetUpMfaButton: 'Configurer MFA',
	SetYourLocation: 'Définissez votre <mark>localisation</mark>',
	SetYourLocationDescription: `Je n'ai pas d'adresse commerciale <span>(services en ligne et mobiles uniquement)</span>`,
	SettingUpPayers: 'Configuration des payeurs',
	Settings: 'Paramètres',
	SettingsNewUserPasswordDescription:
		'Une fois inscrit, nous vous enverrons un code de confirmation que vous pourrez utiliser pour confirmer votre compte.',
	SettingsNewUserPasswordTitle: `S'inscrire à Carepatron`,
	SettingsTabAutomation: 'Automatisation',
	SettingsTabBillingDetails: 'Détails de la facturation',
	SettingsTabConnectedApps: 'Applications connectées',
	SettingsTabCustomFields: 'Champs personnalisés',
	SettingsTabDetails: 'Détails',
	SettingsTabInvoices: 'Factures',
	SettingsTabLocations: 'Emplacements',
	SettingsTabNotifications: 'Notifications',
	SettingsTabOnlineBooking: 'Reservation en ligne',
	SettingsTabPayers: 'Payeurs',
	SettingsTabReminders: 'Rappels',
	SettingsTabServices: 'Prestations de service',
	SettingsTabServicesAndAvailability: 'Services et disponibilité',
	SettingsTabSubscriptions: 'Abonnements',
	SettingsTabWorkflowAutomations: 'Automatismes',
	SettingsTabWorkflowReminders: 'Rappels de base',
	SettingsTabWorkflowTemplates: 'Modèles',
	Setup: 'Configurer',
	SetupGuide: `Guide d'installation`,
	SetupGuideAddServicesActionLabel: 'Commencer',
	SetupGuideAddServicesSubtitle: '4 étapes • 2 min',
	SetupGuideAddServicesTitle: 'Ajouter vos services',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Début',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 étapes • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Activer les paiements en ligne',
	SetupGuideImportClientsActionLabel: 'Début',
	SetupGuideImportClientsSubtitle: '4 étapes • 3 min',
	SetupGuideImportClientsTitle: 'Importer vos clients',
	SetupGuideImportTemplatesActionLabel: 'Début',
	SetupGuideImportTemplatesSubtitle: '2 étapes • 1 min',
	SetupGuideImportTemplatesTitle: 'Importez vos modèles',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Début',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 étapes • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personnalisez votre espace de travail',
	SetupGuideSetLocationActionLabel: 'Commencer',
	SetupGuideSetLocationSubtitle: '4 étapes • 1 min',
	SetupGuideSetLocationTitle: 'Définir votre emplacement',
	SetupGuideSuggestedAddTeamMembersActionLabel: `Inviter l'équipe`,
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Invitez votre équipe à communiquer et à gérer les tâches sans effort.',
	SetupGuideSuggestedAddTeamMembersTag: 'Configuration',
	SetupGuideSuggestedAddTeamMembersTitle: `Ajouter des membres de l'équipe`,
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Personnaliser',
	SetupGuideSuggestedCustomizeBrandSubtitle:
		'Affichez votre professionnalisme avec votre logo unique et vos couleurs de marque.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Personnaliser la marque',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Télécharger',
	SetupGuideSuggestedDownloadMobileAppSubtitle: `Accédez à votre espace de travail n'importe où, n'importe quand et sur n'importe quel appareil.`,
	SetupGuideSuggestedDownloadMobileAppTag: 'Configuration',
	SetupGuideSuggestedDownloadMobileAppTitle: `Téléchargez l'application`,
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Définir la disponibilité',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Évitez les doubles réservations en définissant votre disponibilité.',
	SetupGuideSuggestedEditAvailabilityTag: 'Planification',
	SetupGuideSuggestedEditAvailabilityTitle: 'Modifier la disponibilité',
	SetupGuideSuggestedImportClientsActionLabel: 'Importer',
	SetupGuideSuggestedImportClientsSubtitle:
		'Téléchargez instantanément vos données client existantes en un seul clic.',
	SetupGuideSuggestedImportClientsTag: 'Configuration',
	SetupGuideSuggestedImportClientsTitle: 'Importer des clients',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Modifier les rappels',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Réduisez les rendez-vous manqués avec des rappels de rendez-vous automatiques.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Personnaliser les rappels',
	SetupGuideSuggestedStartVideoCallActionLabel: `Début de l'appel`,
	SetupGuideSuggestedStartVideoCallSubtitle: `Organisez un appel et connectez-vous avec vos clients à l'aide de nos outils vidéo basés sur l'IA.`,
	SetupGuideSuggestedStartVideoCallTag: 'Télé santé',
	SetupGuideSuggestedStartVideoCallTitle: `Démarrer l'appel vidéo`,
	SetupGuideSuggestedTryActionsTitle: 'Choses à essayer 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: `Essayer l'assistance IA`,
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Obtenez des réponses instantanées à toutes vos questions de travail.',
	SetupGuideSuggestedUseAIAssistantTag: 'Nouveau',
	SetupGuideSuggestedUseAIAssistantTitle: 'Utiliser un assistant IA',
	SetupGuideSyncCalendarActionLabel: 'Début',
	SetupGuideSyncCalendarSubtitle: '1 étape • moins de 1 min',
	SetupGuideSyncCalendarTitle: 'Synchroniser votre calendrier',
	SetupGuideVerifyEmailLabel: 'Vérifier',
	SetupGuideVerifyEmailSubtitle: '2 étapes • 2 min',
	SetupOnlineStripePayments: 'Utilisez Stripe pour les paiements en ligne',
	SetupPayments: 'Configurer les paiements',
	Sex: 'Sexe',
	SexSelectorPlaceholder: 'Homme / Femme / Préfère ne pas dire',
	Share: 'Partager',
	ShareBookingLink: 'Partager le lien de réservation',
	ShareNoteDefaultMessage: `Bonjour{name} a partagé "{documentName}" avec vous.

Merci,
{practiceName}`,
	ShareNoteMessage: `Bonjour
{name} a partagé "{documentName}" {isResponder, select, true {avec quelques questions à remplir.} other {avec vous.}}

Merci,
{practiceName}`,
	ShareNoteTitle: 'Partager « {noteTitle} »',
	ShareNotesWithClients: 'Partager avec des clients ou des contacts',
	ShareScreen: `Partager l'écran`,
	ShareScreenNotSupported: `Votre appareil/navigateur ne prend pas en charge la fonctionnalité de partage d'écran`,
	ShareScreenWithId: 'Écran {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Autoriser les autres à voir ce modèle et à le soumettre sous forme de formulaire.',
	ShareTemplateAsPublicFormModalTitle: 'Partager le lien pour ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Configuration du formulaire public mise à jour avec succès',
	ShareTemplateAsPublicFormSectionCustomization: 'Personnalisation',
	ShareTemplateAsPublicFormShowPoweredBy: 'Afficher "Powered by Carepatron" sur mon formulaire',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage:
		'Afficher/masquer « Alimenté par Carepatron » sur mon formulaire',
	ShareTemplateAsPublicFormTrigger: 'Partager',
	ShareTemplateAsPublicFormUseWorkspaceBranding: `Utiliser l'identité visuelle de l'espace de travail`,
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: `Afficher/masquer la marque de l'espace de travail`,
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Envoie du code pour les clients existants et non existants',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: `Les signatures exigent toujours que l'adresse courriel soit vérifiée`,
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Ne envoie du code que pour les clients existants',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: `N'envoie jamais de code`,
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Sélectionner "Jamais" peut permettre aux utilisateurs non vérifiés de remplacer les données client s'ils utilisent l'adresse e-mail d'un client existant.`,
	ShareWithCommunity: 'Partager avec la communauté',
	ShareYourReferralLink: 'Partagez votre lien de parrainage',
	ShareYourScreen: 'Partagez votre écran',
	SheHer: 'Elle/Elle',
	ShortTextAnswer: 'Réponse textuelle courte',
	ShortTextFormPrimaryText: 'Texte court',
	ShortTextFormSecondaryText: 'Réponse de moins de 300 caractères',
	Show: 'Montrer',
	ShowColumn: 'Afficher la colonne',
	ShowColumnButton: 'Afficher la colonne {value} bouton',
	ShowColumns: 'Afficher les colonnes',
	ShowColumnsMenu: 'Afficher le menu des colonnes',
	ShowDateDurationDescription: 'par exemple. 29 ans',
	ShowDateDurationLabel: 'Afficher la durée de la date',
	ShowDetails: 'Afficher les détails',
	ShowField: 'Afficher le champ',
	ShowFullAddress: `Afficher l'adresse`,
	ShowHideFields: 'Afficher/Masquer les champs',
	ShowIcons: 'Afficher les icônes',
	ShowLess: 'Montrer moins',
	ShowMeetingTimers: 'Afficher les minuteurs de réunion',
	ShowMenu: 'Afficher le menu',
	ShowMergeSummarySidebar: 'Afficher le résumé de la fusion',
	ShowMore: 'Montre plus',
	ShowOnTranscript: 'Afficher sur la transcription',
	ShowReactions: 'Afficher les réactions',
	ShowSection: 'Afficher la section',
	ShowServiceCode: 'Afficher le code de service',
	ShowServiceDescription: 'Afficher la description sur les réservations de services',
	ShowServiceDescriptionDesc: 'Les clients peuvent consulter les descriptions des services lors de la réservation',
	ShowServiceGroups: 'Afficher les collections',
	ShowServiceGroupsDesc: 'Les clients verront les services regroupés par collection lors de la réservation',
	ShowSpeakers: 'Afficher les intervenants',
	ShowTax: 'Afficher la taxe',
	ShowTimestamp: `Afficher l'horodatage`,
	ShowUnits: 'Afficher les unités',
	ShowWeekends: 'Afficher les week-ends',
	ShowYourView: 'Montrez votre point de vue',
	SignInWithApple: 'Connectez-vous avec Apple',
	SignInWithGoogle: 'Connectez-vous avec Google',
	SignInWithMicrosoft: 'Connectez-vous avec Microsoft',
	SignUpTitleReferralDefault: `<mark>S'inscrire</mark> et réclamez votre récompense de parrainage`,
	SignUpTitleReferralUpgrade: `Commencez votre période d'essai de {durationInMonths} mois <mark>{percentOff, select, 100 {gratuite} other {{percentOff}% de réduction}} mise à niveau</mark>`,
	SignatureCaptureError: 'Impossible de capturer la signature. Veuillez réessayer.',
	SignatureFormPrimaryText: 'Signature',
	SignatureFormSecondaryText: 'Obtenez une signature numérique',
	SignatureInfoTooltip: 'Cette représentation visuelle ne constitue pas une signature électronique valide.',
	SignaturePlaceholder: 'Dessinez votre signature ici',
	SignedBy: 'Signé par',
	Signup: `S'inscrire`,
	SignupAgreements: `J'accepte les {termsOfUse} et la {privacyStatement} pour mon compte.`,
	SignupBAA: 'Accord de partenariat commercial',
	SignupBusinessAgreements: `Au nom de moi-même et de l'entreprise, j'accepte le {businessAssociateAgreement}, les {termsOfUse} et la {privacyStatement} pour mon compte.`,
	SignupInvitationForYou: 'Vous avez été invité à utiliser Carepatron.',
	SignupPageProviderWarning: `Si votre administrateur a déjà créé un compte, vous devez lui demander de vous inviter chez ce fournisseur. N'utilisez pas ce formulaire d'inscription. Pour plus d'informations, voir`,
	SignupPageProviderWarningLink: 'ce lien.',
	SignupPrivacy: 'politique de confidentialité',
	SignupProfession: 'Quel métier vous décrit le mieux ?',
	SignupSubtitle:
		'Le logiciel de gestion de cabinet de Carepatron est destiné aux praticiens solos et aux équipes. Arrêtez de payer des frais excessifs et faites partie de la révolution.',
	SignupSuccessDescription:
		'Confirmez votre adresse e-mail pour commencer votre intégration. Si vous ne le recevez pas immédiatement, veuillez vérifier votre dossier spam.',
	SignupSuccessTitle: 'Merci de consulter vos emails',
	SignupTermsOfUse: `Conditions d'utilisation`,
	SignupTitleClient: `<mark>Gérez votre santé</mark> d'un seul endroit`,
	SignupTitleLast: `et tout le travail que vous faites ! - C'est gratuit`,
	SignupTitleOne: '<mark>Vous alimenter</mark> , ',
	SignupTitleThree: '<mark>Alimenter vos clients</mark> , ',
	SignupTitleTwo: '<mark>Alimenter votre équipe</mark> , ',
	Simple: 'Simple',
	SimplifyBillToDetails: 'Simplifiez la facturation aux détails',
	SimplifyBillToHelperText: `Seule la première ligne est utilisée lorsqu'elle correspond au client`,
	Singapore: 'Singapour',
	Single: 'Célibataire',
	SingleChoiceFormPrimaryText: 'Choix unique',
	SingleChoiceFormSecondaryText: 'Choisissez une seule option',
	Sister: 'Sœur',
	SisterInLaw: 'Belle-sœur',
	Skip: 'Sauter',
	SkipLogin: `Passer l'identification`,
	SlightBlur: 'Brouillez légèrement votre arrière-plan',
	Small: 'Petit',
	SmartChips: 'Puces intelligentes',
	SmartDataChips: 'Puces de données intelligentes',
	SmartReply: 'Réponse intelligente',
	SmartSuggestNewClient: '**Smart Suggest** créer {name} comme nouveau client',
	SmartSuggestedFieldDescription: 'Ce champ est une suggestion intelligente',
	SocialSecurityNumber: 'Numéro de sécurité sociale',
	SocialWork: 'Travail social',
	SocialWorker: 'Travailleur social',
	SoftwareDeveloper: 'Développeur de logiciels',
	Solo: 'Solo',
	Someone: `Quelqu'un`,
	Son: 'Fils',
	SortBy: 'Trier par',
	SouthAmerica: 'Amérique du Sud',
	Speaker: 'Conférencier',
	SpeakerSource: 'Source du haut-parleur',
	Speakers: 'Haut-parleurs',
	SpecifyPaymentMethod: 'Préciser le mode de paiement',
	SpeechLanguagePathology: 'Orthophonie',
	SpeechTherapist: 'Orthophoniste',
	SpeechTherapists: 'Orthophonistes',
	SpeechTherapy: 'Orthophonie',
	SportsMedicinePhysician: 'Médecin en médecine du sport',
	Spouse: 'Conjoint',
	SpreadsheetColumnExample: 'par exemple ',
	SpreadsheetColumns: 'Colonnes de feuille de calcul',
	SpreadsheetUploaded: 'Feuille de calcul téléchargée',
	SpreadsheetUploading: 'Téléchargement...',
	Staff: 'Personnel',
	StaffAccessDescriptionAdmin: 'Les administrateurs peuvent tout gérer sur la plateforme.',
	StaffAccessDescriptionStaff: `Les membres du personnel peuvent gérer les clients, les notes et la documentation qu'ils ont créée ou partagée.
 avec eux, planifier les rendez-vous, gérer les factures.`,
	StaffContactAssignedSubject:
		'{actorProfileName} a assigné {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} et {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { et 1 autre client} other { et # autres clients}}',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} a partagé la boîte de réception {inboxName} avec vous',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} a supprimé votre accès à la boîte de réception {inboxName}',
	StaffMembers: 'Les membres du personnel',
	StaffMembersNumber: `{billedUsers, plural, one {# membre de l'équipe} other {# membres de l'équipe}}`,
	StaffSavedSuccessSnackbar: `Les informations sur les membres de l'équipe ont été enregistrées avec succès !`,
	StaffSelectorAdminRole: 'Administrateur',
	StaffSelectorStaffRole: 'Membre du staff',
	StandardAppointment: 'Rendez-vous standard',
	StandardColor: 'Couleur de la tâche',
	StartAndEndTime: 'Heure de début et de fin',
	StartCall: `Démarrer l'appel`,
	StartDate: 'Date de début',
	StartDictating: 'Commencer à dicter',
	StartImport: `Commencer l'importation`,
	StartRecordErrorTitle: `Une erreur s'est produite lors du démarrage de votre enregistrement`,
	StartRecording: `Commencer l'enregistrement`,
	StartTimeIncrements: `Incréments d'heure de début`,
	StartTimeIncrementsView: '{startTimeIncrements} intervalles de min',
	StartTranscribing: 'Commencer à transcrire',
	StartTranscribingNotes: 'Commencez à transcrire pour générer des notes automatiques à la fin de la session.',
	StartTranscription: 'Commencer la transcription',
	StartVideoCall: 'Démarrer un appel vidéo',
	StartWeekOn: 'Commencer la semaine le',
	StartedBy: 'Commencé par ',
	Starter: 'Starter',
	State: 'État',
	StateIndustrialAccidentProviderNumber: `Numéro de fournisseur d'assurance accident du travail de l'État`,
	StateLicenseNumber: `Numéro de permis d'État`,
	Statement: 'Déclaration',
	StatementDescriptor: `Descripteur d'instruction`,
	StatementDescriptorToolTip:
		'Le descripteur du relevé est affiché sur les relevés bancaires ou de carte de crédit de vos clients. Il doit comporter entre 5 et 22 caractères et refléter le nom de votre entreprise.',
	StatementNumber: 'Déclaration #',
	Status: 'Statut',
	StatusFieldPlaceholder: `Saisissez un libellé d'état`,
	StepFather: 'Beau-père',
	StepMother: 'Belle-mère',
	Stockholm: 'Stockholm',
	StopIgnoreSendersDescription:
		'Arrêtez d’ignorer les expéditeurs pour recevoir à nouveau des notifications de messages de leur part.',
	StopIgnoring: 'Arrêter d’ignorer',
	StopIgnoringSenders: 'Arrêter d’ignorer les expéditeurs',
	StopIgnoringSendersSuccess: `J'ai arrêté d'ignorer l'adresse e-mail <mark>{addresses}</mark>`,
	StopSharing: 'Arrêter de partager',
	StopSharingLabel: 'carepatron.com partage votre écran.',
	Storage: 'Stockage',
	StorageAlmostFullDescription:
		'🚀 Effectuez une mise à niveau maintenant pour que votre compte continue de fonctionner correctement.',
	StorageAlmostFullTitle: `Vous avez utilisé {percentage}% de votre limite de stockage de l'espace de travail !`,
	StorageFullDescription: 'Obtenez plus de stockage en mettant à niveau votre forfait.',
	StorageFullTitle: '	Votre espace de stockage est plein.',
	Street: 'Rue',
	StripeAccountNotCompleteErrorCode:
		'Les paiements en ligne ne sont pas {hasProviderName, select, true {configurés pour {providerName}} other {activés pour ce fournisseur}}.',
	StripeAccountRejectedError: 'Le compte Stripe a été rejeté. Veuillez contacter le support.',
	StripeBalance: 'Équilibre des rayures',
	StripeChargesInfoToolTip: 'Vous permet de facturer le débit ',
	StripeFeesDescription:
		'Carepatron utilise Stripe pour vous faire payer rapidement et sécuriser vos informations de paiement. Les méthodes de paiement disponibles varient selon la région, toutes les principales cartes de débit ',
	StripeFeesDescriptionItem1:
		'Des frais de traitement sont appliqués à chaque transaction réussie, vous pouvez {link}.',
	StripeFeesDescriptionItem2: `Les paiements sont effectués quotidiennement mais sont conservés jusqu'à 4 jours.`,
	StripeFeesLinkToRatesText: 'consultez nos tarifs ici',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Désolé, il faut un minimum de {minimumAmount} pour les factures qui utilisent les paiements en ligne.',
	StripePaymentsDisabled: 'Paiements en ligne désactivés. Veuillez vérifier vos paramètres de paiement.',
	StripePaymentsUnavailable: 'Paiements indisponibles',
	StripePaymentsUnavailableDescription: `Une erreur s'est produite lors du chargement des paiements. Veuillez réessayer plus tard.`,
	StripePayoutsInfoToolTip: `Vous permet d'être payé sur votre compte bancaire`,
	StyleYourWorkspace: '<mark>Stylisez</mark> votre espace de travail',
	StyleYourWorkspaceDescription1: `Nous avons récupéré les ressources de votre marque sur votre site Web. N'hésitez pas à les modifier ou à poursuivre vers votre espace de travail Carepatron.`,
	StyleYourWorkspaceDescription2:
		'Utilisez vos actifs de marque pour personnaliser les factures et les réservations en ligne afin de garantir une expérience client transparente.',
	SubAdvanced: 'Avancé',
	SubEssential: 'Essentiel',
	SubOrganization: 'Organisation',
	SubPlus: 'Plus',
	SubProfessional: 'Professionnel',
	Subject: 'Sujet',
	Submit: 'Soumettre',
	SubmitElectronically: 'Soumettez électroniquement',
	SubmitFeedback: 'Soumettre un commentaire',
	SubmitFormValidationError:
		'Veuillez vous assurer que tous les champs obligatoires sont correctement remplis et réessayez de soumettre.',
	Submitted: 'Soumis',
	SubmittedDate: 'Date de soumission',
	SubscribePerMonth: `S'abonner {price} {isMonthly, select, true {par mois} other {par an}}`,
	SubscriptionDiscountDescription:
		'{percentOff}% de réduction {months, select, null { } other { {months, plural, one {pendant # mois} other {pendant # mois}}}}',
	SubscriptionFreeTrialDescription: `Gratuit jusqu'au {endDate}`,
	SubscriptionPaymentFailedNotificationSubject: `Nous n'avons pas pu finaliser le paiement de votre abonnement. Veuillez vérifier vos informations de paiement`,
	SubscriptionPlanDetailsHeader: 'Par utilisateur/mensuel facturé annuellement',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} facturé mensuellement (USD)',
	SubscriptionPlans: `Plans d'abonnement`,
	SubscriptionPlansDescription:
		'Surclassez votre forfait pour débloquer des avantages supplémentaires et assurer le bon fonctionnement de votre cabinet.',
	SubscriptionPlansDescriptionNoPermission: `Il semble que vous n'ayez pas accès à la mise à niveau pour le moment — veuillez contacter votre administrateur pour obtenir de l'aide.`,
	SubscriptionSettings: `Paramètres d'abonnement`,
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> de stockage utilisé',
	SubscriptionSettingsStorageUsed: '{used} sur {limit} utilisé',
	SubscriptionSettingsUnlimitedStorage: 'Stockage illimité disponible',
	SubscriptionSummary: `Résumé de l'abonnement`,
	SubscriptionUnavailableOverStorageLimit: 'Votre utilisation actuelle dépasse la limite de stockage de ce forfait.',
	SubscriptionUnpaidBannerButton: 'Aller aux abonnements',
	SubscriptionUnpaidBannerDescription:
		'Veuillez vérifier que vos informations de paiement sont correctes et réessayer',
	SubscriptionUnpaidBannerTitle: `Nous n'avons pas pu finaliser le paiement de votre abonnement.`,
	Subscriptions: 'Abonnements',
	SubscriptionsAndPayments: 'Abonnements ',
	Subtotal: 'Total',
	SuburbOrProvince: 'Banlieue/Province',
	SuburbOrState: 'Banlieue/État',
	SuccessSavedNoteChanges: 'Les modifications de notes ont été enregistrées avec succès',
	SuccessShareDocument: 'Document partagé avec succès',
	SuccessShareNote: 'Note partagée avec succès',
	SuccessfullyCreatedValue: 'Création réussie de {value}',
	SuccessfullyDeletedTranscriptionPart: 'Partie de transcription supprimée avec succès',
	SuccessfullyDeletedValue: 'Supprimé avec succès {value}',
	SuccessfullySubmitted: 'Soumis avec succès ',
	SuccessfullyUpdatedClientSettings: 'Paramètres client mis à jour avec succès',
	SuccessfullyUpdatedTranscriptionPart: 'Partie de transcription mise à jour avec succès',
	SuccessfullyUpdatedValue: 'Mis à jour avec succès {value}',
	SuggestedAIPoweredTemplates: `Modèles suggérés basés sur l'IA`,
	SuggestedAITemplates: `Modèles d'IA suggérés`,
	SuggestedActions: 'Actions suggérées',
	SuggestedLocations: 'Lieux suggérés',
	Suggestions: 'Suggestions',
	Summarise: `Résumé de l'IA`,
	SummarisingContent: 'Résumé de {title}',
	Sunday: 'Dimanche',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Super facture ',
	SuperbillAutomationMonthly: 'Actif • Dernier jour du mois',
	SuperbillAutomationNoEmail:
		'Pour envoyer avec succès les documents de facturation automatisés, ajoutez une adresse e-mail pour ce client',
	SuperbillAutomationNotActive: 'Pas actif',
	SuperbillAutomationUpdateFailure: `Échec de la mise à jour des paramètres d'automatisation de Superbill`,
	SuperbillAutomationUpdateSuccess: `Paramètres d'automatisation Superbill mis à jour avec succès`,
	SuperbillClientHelperText: 'Ces informations sont préremplies à partir des détails du client',
	SuperbillNotFoundDescription: `Veuillez contacter votre fournisseur et lui demander plus d'informations ou pour renvoyer la superbe facture.`,
	SuperbillNotFoundTitle: 'Superbe facture introuvable',
	SuperbillNumber: 'Superfacture #{number}',
	SuperbillNumberAlreadyExists: 'Le numéro de reçu Superbill existe déjà',
	SuperbillPracticeHelperText: 'Ces informations sont préremplies à partir des paramètres de facturation du cabinet.',
	SuperbillProviderHelperText: 'Ces informations sont préremplies à partir des détails du personnel',
	SuperbillReceipts: 'Reçus Superbill',
	SuperbillsEmptyStateDescription: `Aucun superbill n'a été trouvé.`,
	Surgeon: 'Chirurgien',
	Surgeons: 'Chirurgiens',
	SurgicalTechnologist: 'Technologue chirurgical',
	SwitchFromAnotherPlatform: 'Je change de plateforme',
	SwitchToMyPortal: 'Passer à Mon portail',
	SwitchToMyPortalTooltip: `Accédez à votre propre portail personnel,
 vous permettant d'explorer votre
 l'expérience du portail du client.`,
	SwitchWorkspace: `Changer d'espace de travail`,
	SwitchingToADifferentPlatform: 'Passer à une autre plateforme',
	Sydney: 'Sidney',
	SyncCalendar: 'Synchroniser le calendrier',
	SyncCalendarModalDescription: `Les autres membres de l'équipe ne pourront pas voir vos calendriers synchronisés. Les rendez-vous clients ne peuvent être mis à jour ou supprimés qu’à partir de Carepatron.`,
	SyncCalendarModalDisplayCalendar: 'Afficher mon calendrier dans Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Synchroniser mon calendrier avec Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Synchroniser les rendez-vous Carepatron avec mon calendrier',
	SyncCarepatronAppointmentsWithMyCalendar: 'Synchroniser les rendez-vous Carepatron avec mon calendrier',
	SyncGoogleCalendar: 'Synchroniser le calendrier Google',
	SyncInbox: 'Synchroniser la boîte de réception avec Carepatron',
	SyncMyCalendarToCarepatron: 'Synchroniser mon calendrier avec Carepatron',
	SyncOutlookCalendar: 'Synchroniser le calendrier Outlook',
	SyncedFromExternalCalendar: 'Synchronisé à partir du calendrier externe',
	SyncingCalendarName: 'Synchronisation du calendrier {calendarName}',
	SyncingFailed: 'Échec de la synchronisation',
	SystemGenerated: 'Système généré',
	TFN: 'NIF',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Tableau',
	TableRowLabel: 'Ligne de tableau pour {value}',
	TagSelectorNoOptionsText: 'Cliquez sur &quot;créer un nouveau&quot; pour ajouter une nouvelle balise',
	Tags: 'Mots clés',
	TagsInputPlaceholder: 'Rechercher ou créer des balises',
	Task: 'Tâche',
	TaskAttendeeStatusUpdatedSuccess: 'Statut des rendez-vous mis à jour avec succès',
	Tasks: 'Tâches',
	Tax: 'Impôt',
	TaxAmount: `Montant de l'impôt`,
	TaxID: `Numéro d'identification fiscale`,
	TaxIdType: `Type d'identifiant fiscal`,
	TaxName: 'Nom de la taxe',
	TaxNumber: `Numéro d'identification fiscale`,
	TaxNumberType: 'Type de numéro de taxe',
	TaxNumberTypeInvalid: '{type} est invalide',
	TaxPercentageOfAmount: '{taxName} ({percentage}% de {amount})',
	TaxRate: `Taux d'imposition`,
	TaxRatesDescription: 'Gérez les taux de taxe qui seront appliqués aux éléments de ligne de votre facture.',
	Taxable: 'Imposable',
	TaxonomyCode: 'Code de taxonomie',
	TeacherAssistant: 'Assistante pédagogique',
	Team: 'Équipe',
	TeamMember: `Membre de l'équipe`,
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {est} other {sont}} déjà réservé(e)s à ce moment-là.{br}Choisissez un nouvel horaire pour éviter une double réservation.',
	TeamMembers: `Membres de l'équipe`,
	TeamMembersColour: `Couleur des membres de l'équipe`,
	TeamMembersDetails: `Détails des membres de l'équipe`,
	TeamSize: 'Combien de personnes compte votre équipe ?',
	TeamTemplates: `Modèles d'équipe`,
	TeamTemplatesSectionDescription: 'Créé par vous et votre équipe',
	TelehealthAndVideoCalls: 'Télésanté ',
	TelehealthProvidedOtherThanInPatientCare:
		'Télésanté fournie pour des raisons autres que les soins aux patients hospitalisés',
	TelehealthVideoCall: 'Appel vidéo de télésanté',
	Template: 'Modèle',
	TemplateDescription: 'Description du modèle',
	TemplateDetails: 'Détails du modèle',
	TemplateEditModeViewSwitcherDescription: 'Créer et modifier un modèle',
	TemplateGallery: 'Modèles de communauté',
	TemplateImportCompletedNotificationSubject: 'Import du modèle terminé ! {templateTitle} est prêt à l’emploi.',
	TemplateImportFailedNotificationSubject: `Échec de l'importation du fichier {fileName}.`,
	TemplateName: 'Nom du modèle',
	TemplateNotFound: 'Le modèle est introuvable.',
	TemplatePreviewErrorMessage: `Une erreur s'est produite lors du chargement de l'aperçu du modèle`,
	TemplateResponderModeViewSwitcherDescription: 'Prévisualiser et interagir avec les formulaires',
	TemplateResponderModeViewSwitcherTooltipTitle: `Vérifiez comment vos formulaires apparaissent lorsqu'ils sont remplis par les intervenants`,
	TemplateSaved: 'Modifications enregistrées',
	TemplateTitle: 'Titre du modèle',
	TemplateType: 'Type de modèle',
	Templates: 'Modèles',
	TemplatesCategoriesFilter: 'Filtrer par catégorie',
	TemplatesPublicTemplatesFilter: ' Filtrer par communauté/équipe',
	Text: 'Texte',
	TextAlign: 'Alignement du texte',
	TextColor: 'Couleur du texte',
	ThankYouForYourFeedback: 'Merci pour votre retour!',
	ThanksForLettingKnow: 'Merci de nous en avoir informé.',
	ThePaymentMethod: 'Le mode de paiement',
	ThemThey: 'Ils/Elles',
	Theme: 'Thème',
	ThemeAllColorsPickerTitle: 'Plus de thèmes',
	ThemeColor: 'Thème',
	ThemeColorDarkMode: 'Sombre',
	ThemeColorLightMode: 'Lumière',
	ThemeColorModePickerTitle: 'Mode de couleur',
	ThemeColorSystemMode: 'Système',
	ThemeCpColorPickerTitle: 'Thèmes Carepatron',
	ThemePanelDescription:
		'Choisissez entre le mode clair et le mode sombre, et personnalisez vos préférences de thème.',
	ThemePanelTitle: 'Apparence',
	Then: 'Alors',
	Therapist: 'Thérapeute',
	Therapists: 'Thérapeutes',
	Therapy: 'Thérapie',
	Thick: 'Épais',
	Thin: 'Mince',
	ThirdPerson: '3ème personne',
	ThisAndFollowingAppointments: 'Ce rendez-vous et les suivants',
	ThisAndFollowingMeetings: 'Cette réunion et les suivantes',
	ThisAndFollowingReminders: 'Ceci et les rappels suivants',
	ThisAndFollowingTasks: 'Cette tâche et les suivantes',
	ThisAppointment: 'Ce rendez-vous',
	ThisMeeting: 'Cette réunion',
	ThisMonth: 'Ce mois-ci',
	ThisPerson: 'Cette personne',
	ThisReminder: 'Ce rappel',
	ThisTask: 'Cette tâche',
	ThisWeek: 'Cette semaine',
	ThreeDay: '3 jours',
	Thursday: 'Jeudi',
	Time: 'Temps',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Format de l’heure',
	TimeIncrement: 'Incrément de temps',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Taille du créneau horaire',
	Timestamp: 'Horodatage',
	Timezone: 'Fuseau horaire',
	TimezoneDisplay: 'Affichage du fuseau horaire',
	TimezoneDisplayDescription: 'Gérez les paramètres d’affichage de votre fuseau horaire.',
	Title: 'Titre',
	To: 'À',
	ToYourWorkspace: 'à votre espace de travail',
	Today: `Aujourd'hui`,
	TodayInHoursPlural: `Aujourd'hui en {count} {count, plural, one {heure} other {heures}}`,
	TodayInMinsAbbreviated: 'Aujourd’hui en {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: `Basculer la cellule d'en-tête`,
	ToggleHeaderCol: `Basculer la colonne d'en-tête`,
	ToggleHeaderRow: `Basculer la ligne d'en-tête`,
	Tokyo: 'Tokyo',
	Tomorrow: 'Demain',
	TomorrowAfternoon: 'Demain après-midi',
	TomorrowMorning: 'Demain matin',
	TooExpensive: 'Trop cher',
	TooHardToSetUp: 'Trop dur à mettre en place',
	TooManyFiles: `Plus d'un fichier détecté.`,
	ToolsExample: 'Pratique simple, Microsoft, Calendly, Asana, Doxy.me...',
	Total: 'Total',
	TotalAccountCredit: 'Crédit total du compte',
	TotalAdjustments: 'Ajustements totaux',
	TotalAmountToCreditInCurrency: 'Montant total à créditer ({currency})',
	TotalBilled: 'Total facturé',
	TotalConversations: '{total} {total, plural, =0 {conversation} one {conversation} other {conversations}}',
	TotalOverdue: 'Total en retard',
	TotalOverdueTooltip:
		'Le solde total en retard comprend toutes les factures impayées, quelle que soit la plage de dates, qui ne sont ni annulées ni traitées.',
	TotalPaid: 'Total payé',
	TotalPaidTooltip:
		'Le solde total payé comprend tous les montants des factures qui ont été payées dans la plage de dates spécifiée.',
	TotalUnpaid: 'Total impayé',
	TotalUnpaidTooltip:
		'Le solde total impayé comprend tous les montants impayés provenant du traitement, des factures impayées et envoyées dues dans la plage de dates spécifiée.',
	TotalWorkflows: '{count} {count, plural, one {flux de travail} other {flux de travail}}',
	TotpSetUpManualEntryInstruction: `Alternativement, vous pouvez saisir manuellement le code ci-dessous dans l'application :`,
	TotpSetUpModalDescription: `Scannez le code QR avec votre application d'authentification pour configurer l'authentification multifacteur.`,
	TotpSetUpModalTitle: 'Configurer le périphérique MFA',
	TotpSetUpSuccess: `Vous êtes prêt ! L'authentification multifacteur a été activée.`,
	TotpSetupEnterAuthenticatorCodeInstruction: `Entrez le code généré par votre application d'authentification`,
	Transcribe: 'Transcrire',
	TranscribeLanguageSelector: 'Sélectionnez la langue de saisie',
	TranscribeLiveAudio: `Transcrire l'audio en direct`,
	Transcribing: 'Transcription audio...',
	TranscribingIn: 'Transcrire en',
	Transcript: 'Transcription',
	TranscriptRecordingCompleteInfo: `Vous verrez votre transcription ici une fois l'enregistrement terminé.`,
	TranscriptSuccessSnackbar: 'Transcription traitée avec succès.',
	Transcription: 'Transcription',
	TranscriptionEmpty: 'Aucune transcription disponible',
	TranscriptionEmptyHelperMessage: `Cette transcription n'a rien détecté. Redémarrez-la et réessayez.`,
	TranscriptionFailedNotice: `Cette transcription n'a pas été traitée avec succès`,
	TranscriptionIdleMessage:
		'Nous n’entendons aucun son. Si vous avez besoin de plus de temps, veuillez répondre dans les {timeValue} secondes, sinon la session prendra fin.',
	TranscriptionInProcess: 'Transcription en cours...',
	TranscriptionIncompleteNotice: `Certaines parties de cette transcription n'ont pas été traitées avec succès`,
	TranscriptionOvertimeWarning: '{scribeType} session se termine dans <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Êtes-vous sûr de vouloir supprimer cette partie de transcription ?',
	TranscriptionText: 'Voix en texte',
	TranscriptsPending: 'Votre relevé de notes sera disponible ici après la fin de la session.',
	Transfer: 'Transfert',
	TransferAndDelete: 'Transférer et supprimer',
	TransferOwnership: 'Transfert de propriété',
	TransferOwnershipConfirmationModalDescription: `Cette action ne peut être annulée que s'ils vous transfèrent la propriété.`,
	TransferOwnershipDescription: 'Transférez la propriété de cet espace de travail à un autre membre de l’équipe.',
	TransferOwnershipSuccessSnackbar: 'Transfert de propriété réussi !',
	TransferOwnershipToMember: 'Êtes-vous sûr de vouloir transférer cet espace de travail vers {staff} ?',
	TransferStatusAlert:
		'Supprimer {numberOfStatuses, plural, one {ce statut} other {ces statuts}} affectera {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} statut client.</strong>} other {<strong>{numberOfAffectedRecords} statuts clients.</strong>}}',
	TransferStatusDescription:
		'Choisissez un autre statut pour ces clients avant de procéder à la suppression. Cette action ne peut pas être annulée.',
	TransferStatusLabel: 'Transfert vers un nouveau statut',
	TransferStatusPlaceholder: 'Choisir un statut existant',
	TransferStatusTitle: 'Statut du transfert avant suppression',
	TransferTaskAttendeeStatusAlert:
		'Supprimer ce statut aura un impact sur <strong>{number} rendez-vous futur{number, plural, one {statut} other {statuts}}. </strong>',
	TransferTaskAttendeeStatusDescription:
		'Choisissez un autre statut pour ces clients avant de procéder à la suppression. Cette action est irréversible.',
	TransferTaskAttendeeStatusSubtitle: 'Statut du rendez-vous',
	TransferTaskAttendeeStatusTitle: 'Statut du transfert avant suppression',
	Trash: 'Corbeille',
	TrashDeleteItemsModalConfirm: 'Pour confirmer, tapez {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Les {count, plural, one {élément} other {éléments}} suivants seront supprimés de façon permanente et ne pourront pas être restaurés.',
	TrashDeleteItemsModalTitle: 'Supprimer {count, plural, one {élément} other {éléments}} pour toujours',
	TrashDeletedAllItems: 'Supprimé tous les éléments',
	TrashDeletedItems: 'Supprimé {count, plural, one {élément} other {éléments}}',
	TrashDeletedItemsFailure: 'Échec de la suppression des éléments de la corbeille',
	TrashLocationAppointmentType: 'Rendez-vous',
	TrashLocationBillingAndPaymentsType: 'Facturation et paiements',
	TrashLocationContactType: 'Patients',
	TrashLocationNoteType: 'Notes et documents',
	TrashRestoreItemsModalDescription: 'Les {count, plural, one {item} other {items}} suivants seront restaurés.',
	TrashRestoreItemsModalTitle: 'Restaurer {count, plural, one {élément} other {éléments}}',
	TrashRestoredAllItems: 'Restauré tous les éléments',
	TrashRestoredItems: 'Restauré {count, plural, one {élément} other {éléments}}',
	TrashRestoredItemsFailure: 'Impossible de restaurer les éléments de la corbeille',
	TrashSuccessfullyDeletedItem: 'Supprimé avec succès {type}',
	Trigger: 'Déclenchement',
	Troubleshoot: 'Dépanner',
	TryAgain: 'Essayer à nouveau',
	Tuesday: 'Mardi',
	TwoToTen: '2 - 10',
	Type: 'Taper',
	TypeHere: 'Écrivez ici...',
	TypeToConfirm: 'Pour confirmer, saisissez {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Titre 1',
	TypographyHeading2: 'Titre 2',
	TypographyHeading3: 'Titre 3',
	TypographyHeading4: 'Titre 4',
	TypographyHeading5: 'Titre 5',
	TypographyP: 'P.',
	TypographyParagraph: 'Paragraphe',
	UnableToCompleteAction: `Impossible de terminer l'action.`,
	UnableToPrintDocument: `Impossible d'imprimer le document. Veuillez réessayer plus tard.`,
	Unallocated: 'Non alloué',
	UnallocatedPaymentDescription: `Ce paiement n’a pas été entièrement affecté aux éléments facturables.
 Ajoutez une allocation aux éléments non payés ou émettez un crédit ou un remboursement.`,
	UnallocatedPaymentTitle: 'Paiement non affecté',
	UnallocatedPayments: 'Paiements non alloués',
	Unarchive: 'Désarchiver',
	Unassigned: 'Non attribué',
	UnauthorisedInvoiceSnackbar: `Vous n'avez pas accès à la gestion des factures pour ce client.`,
	UnauthorisedSnackbar: `Vous n'avez pas la permission de faire cela.`,
	Unavailable: 'Indisponible',
	Uncategorized: 'Non classé',
	Unclaimed: 'Non réclamé',
	UnclaimedAmount: 'Montant non réclamé',
	UnclaimedItems: 'Articles non réclamés',
	UnclaimedItemsMustBeInCurrency: 'Seuls les articles dans les devises suivantes sont pris en charge : {currencies}',
	Uncle: 'Oncle',
	Unconfirmed: 'Non confirmé',
	Underline: 'Souligner',
	Undo: 'annuler',
	Unfavorite: 'Supprimer des favoris',
	Uninvoiced: 'Non facturé',
	UninvoicedAmount: 'Montant non facturé',
	UninvoicedAmounts:
		'{count, plural, =0 {Aucun montant non facturé} one {Montant non facturé} other {Montants non facturés}}',
	Unit: 'Unité',
	UnitedKingdom: 'Royaume-Uni',
	UnitedStates: 'États-Unis',
	UnitedStatesEast: 'États-Unis - Est',
	UnitedStatesWest: 'États-Unis - Ouest',
	Units: 'Unités',
	UnitsIsRequired: 'Les unités sont obligatoires',
	UnitsMustBeGreaterThanZero: 'Les unités doivent être supérieures à 0',
	UnitsPlaceholder: '1',
	Unknown: 'Inconnu',
	Unlimited: 'Illimité',
	Unlock: 'Ouvrir',
	UnlockNoteHelper: `Avant d'apporter de nouvelles modifications, les éditeurs doivent déverrouiller la note.`,
	UnmuteAudio: 'Activer le son',
	UnmuteEveryone: 'Rétablir le son de tout le monde',
	Unpaid: 'Non payé',
	UnpaidInvoices: 'Factures impayées',
	UnpaidItems: 'Éléments non payés',
	UnpaidMultiple: 'Non payé',
	Unpublish: 'Désactiver la publication',
	UnpublishTemplateConfirmationModalPrompt:
		'Supprimer <span>{title}</span> supprimera cette ressource de la communauté Carepatron. Cette action est irréversible.',
	UnpublishToCommunitySuccessMessage: 'Supprimé avec succès « {title} » de la communauté',
	Unread: 'Non lu',
	Unrecognised: 'Méconnu',
	UnrecognisedDescription: `Ce mode de paiement n'est pas reconnu par la version actuelle de votre application. Veuillez actualiser votre navigateur pour obtenir la dernière version afin d'afficher et de modifier ce mode de paiement.`,
	UnsavedChanges: 'Modifications non enregistrées',
	UnsavedChangesPromptContent: 'Voulez-vous enregistrer vos modifications avant de fermer ?',
	UnsavedChangesPromptTitle: 'Vous avez des changements non enregistrés',
	UnsavedNoteChangesWarning: 'Les modifications que vous avez apportées peuvent ne pas être enregistrées',
	UnsavedTemplateChangesWarning: 'Les modifications que vous avez apportées peuvent ne pas être enregistrées',
	UnselectAll: 'Désélectionner tout',
	Until: `Jusqu'à`,
	UntitledConversation: 'Conversation sans titre',
	UntitledFolder: 'Dossier sans titre',
	UntitledNote: 'Note sans titre',
	UntitledSchedule: 'Calendrier sans titre',
	UntitledSection: 'Section sans titre',
	UntitledTemplate: 'Modèle sans titre',
	Unverified: 'Non vérifié',
	Upcoming: 'A venir',
	UpcomingAppointments: 'Rendez-vous à venir',
	UpcomingDateOverridesEmpty: 'Il n’y a pas de remplacement de date',
	UpdateAvailabilityScheduleFailure: 'Échec de la mise à jour du calendrier de disponibilité',
	UpdateAvailabilityScheduleSuccess: 'Calendrier de disponibilité mis à jour avec succès',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Souhaitez-vous que la nouvelle tarification soit appliquée aux factures et réclamations des participants ?',
	UpdateLink: 'Mettre à jour le lien',
	UpdatePrimaryEmailWarningDescription: `Changer l'adresse courriel de votre client entraînera la perte de son accès à ses rendez-vous et notes existants.`,
	UpdatePrimaryEmailWarningTitle: `Changement d'adresse courriel du client`,
	UpdateSettings: 'Mettre à jour les paramètres',
	UpdateStatus: 'État de mise à jour',
	UpdateSuperbillReceiptFailure: 'Échec de la mise à jour du reçu Superbill',
	UpdateSuperbillReceiptSuccess: 'Reçu Superbill mis à jour avec succès',
	UpdateTaskBillingDetails: 'Mettre à jour les détails de facturation',
	UpdateTaskBillingDetailsDescription:
		'La tarification des rendez-vous a changé. Souhaitez-vous que la nouvelle tarification soit appliquée aux éléments de facturation, aux factures et aux réclamations des participants ? Choisissez les mises à jour que vous souhaitez effectuer.',
	UpdateTemplateFolderSuccessMessage: 'Dossier mis à jour avec succès',
	UpdateUnpaidInvoices: 'Mettre à jour les factures impayées',
	UpdateUserInfoSuccessSnackbar: 'Informations utilisateur mises à jour avec succès !',
	UpdateUserSettingsSuccessSnackbar: 'Paramètres utilisateur mis à jour avec succès !',
	Upgrade: 'Mise à niveau',
	UpgradeForSMSReminder: 'Passez à <b>Professional</b> pour des rappels SMS illimités',
	UpgradeNow: 'Mettre à jour maintenant',
	UpgradePlan: 'Plan de mise à niveau',
	UpgradeSubscriptionAlertDescription:
		'Vous manquez de stockage. Mettez à niveau votre plan pour débloquer du stockage supplémentaire et maintenir le bon fonctionnement de votre cabinet !',
	UpgradeSubscriptionAlertDescriptionNoPermission: `Vous manquez d'espace de stockage. Demandez à un membre de votre cabinet disposant d'un <span>accès administrateur</span> de mettre à niveau votre forfait pour débloquer de l'espace de stockage supplémentaire et assurer le bon fonctionnement de votre cabinet !`,
	UpgradeSubscriptionAlertTitle: 'Il est temps de mettre à niveau votre abonnement',
	UpgradeYourPlan: 'Mettre à niveau votre abonnement',
	UploadAudio: `Télécharger l'audio`,
	UploadFile: 'Téléverser un fichier',
	UploadFileDescription: 'De quelle plateforme logicielle changez-vous ?',
	UploadFileMaxSizeError: 'Fichier trop volumineux. Taille de fichier maximale : {fileSizeLimit}.',
	UploadFileSizeLimit: 'Limite de taille {size}Mo',
	UploadFileTileDescription: 'Utilisez des fichiers CSV, XLS, XLSX ou ZIP pour télécharger vos clients.',
	UploadFileTileLabel: 'Télécharger un fichier',
	UploadFiles: 'Télécharger des fichiers',
	UploadIndividually: 'Télécharger des fichiers individuellement',
	UploadLogo: 'Télécharger le logo',
	UploadPhoto: 'Importer votre photo',
	UploadToCarepatron: 'Télécharger sur Carepatron',
	UploadYourLogo: 'Téléchargez votre logo',
	UploadYourTemplates: 'Téléchargez vos modèles et nous les convertirons pour vous.',
	Uploading: 'Téléchargement',
	UploadingAudio: 'Téléchargement de votre audio...',
	UploadingFiles: 'Téléchargement de fichiers',
	UrlLink: 'Lien URL',
	UsageCount: 'Utilisé {count} fois',
	UsageLimitValue: '{used} sur {limit} utilisés',
	UsageValue: '{used} utilisé',
	Use: 'Utiliser',
	UseAiToAutomateYourWorkflow: 'Utilisez l’IA pour automatiser votre flux de travail !',
	UseAsDefault: 'Utiliser par défaut',
	UseCustom: 'Utiliser personnalisé',
	UseDefault: `L'utilisation par défaut`,
	UseDefaultFilters: 'Utiliser les filtres par défaut',
	UseTemplate: 'Utilise le modèle',
	UseThisCard: 'Utilisez cette carte',
	UseValue: 'Utiliser "{value}"',
	UseWorkspaceDefault: `Utiliser la valeur par défaut de l'espace de travail`,
	UserIsTyping: '{name} est en train de taper...',
	Username: `Nom d'utilisateur`,
	Users: 'Utilisateurs',
	VAT: 'T.V.A.',
	ValidUrl: 'Le lien URL doit être une URL valide.',
	Validate: 'Valider',
	Validated: 'Validé',
	Validating: 'Validation',
	ValidatingContent: 'Validation du contenu...',
	ValidatingTranscripts: 'Validation des relevés de notes...',
	ValidationConfirmPasswordRequired: 'Confirmer que le mot de passe est requis',
	ValidationDateMax: 'Doit être avant {max}',
	ValidationDateMin: 'Doit être après {min}',
	ValidationDateRange: 'Les dates de début et de fin sont obligatoires',
	ValidationEndDateMustBeAfterStartDate: 'Date de fin doit être après la date de début',
	ValidationMixedDefault: `Ceci n'est pas valide`,
	ValidationMixedRequired: 'Ceci est nécessaire',
	ValidationNumberInteger: 'Doit être un nombre entier',
	ValidationNumberMax: 'Doit être inférieur ou égal à {max}',
	ValidationNumberMin: 'Doit être {min} ou plus',
	ValidationPasswordNotMatching: 'Les mots de passe ne correspondent pas',
	ValidationPrimaryAddressIsRequired: `L'adresse est requise lorsqu'elle est définie par défaut`,
	ValidationPrimaryPhoneNumberIsRequired: `Le numéro de téléphone est requis lorsqu'il est défini par défaut`,
	ValidationServiceMustBeNotBeFuture: 'Le service ne doit pas être le jour même ou dans le futur',
	ValidationStringEmail: 'Doit être un e-mail valide',
	ValidationStringMax: 'Doit comporter {max} caractères ou moins',
	ValidationStringMin: 'Doit avoir {min} caractères ou plus',
	ValidationStringPhoneNumber: 'Doit être un numéro de téléphone valide',
	ValueMinutes: '{value} minutes',
	VerbosityConcise: 'Concis',
	VerbosityDetailed: 'Détaillé',
	VerbosityStandard: 'Standard',
	VerbositySuperDetailed: 'Super détaillé',
	VerificationCode: 'Code de vérification',
	VerificationEmailDescription:
		'Veuillez entrer votre adresse email et le code de vérification que nous venons de vous envoyer.',
	VerificationEmailSubtitle: `Vérifiez le dossier Spam - si l'email n'est pas arrivé`,
	VerificationEmailTitle: `Vérifier l'email`,
	VerificationOption: `Vérification de l'adresse courriel`,
	Verified: 'Vérifié',
	Verify: 'Vérifier',
	VerifyAndSubmit: 'Vérifier et soumettre',
	VerifyEmail: `Vérifier l'adresse électronique`,
	VerifyEmailAccessCode: 'Code de confirmation',
	VerifyEmailAddress: `Vérifier l'adresse e-mail`,
	VerifyEmailButton: 'Vérifier et se déconnecter',
	VerifyEmailSentSnackbar: `L'email de vérification a été envoyé. Vérifiez votre boîte de réception.`,
	VerifyEmailSubTitle: `Vérifiez le dossier Spam si l'e-mail n'est pas arrivé`,
	VerifyEmailSuccessLogOutSnackbar: 'Succès! Veuillez vous déconnecter pour appliquer les modifications.',
	VerifyEmailSuccessSnackbar:
		'Succès! Email verifié. Veuillez vous connecter pour continuer en tant que compte vérifié.',
	VerifyEmailTitle: 'Vérifiez votre e-mail',
	VerifyNow: 'Vérifiez maintenant',
	Veterinarian: 'Vétérinaire',
	VideoCall: 'Appel vidéo',
	VideoCallAudioInputFailed: `Périphérique d'entrée audio ne fonctionne pas`,
	VideoCallAudioInputFailedMessage:
		'Ouvrez les paramètres et vérifiez si vous avez correctement défini la source du microphone.',
	VideoCallChatBanner: `Les messages peuvent être vus par tous les participants à cet appel et seront supprimés à la fin de l'appel.`,
	VideoCallChatSendBtn: 'Envoyer un message',
	VideoCallChatTitle: 'Chat',
	VideoCallDisconnectedMessage: 'Vous avez perdu votre connexion réseau. Tentative de reconnexion',
	VideoCallOptionInfo: `Carepatron gérera les appels vidéo pour vos rendez-vous si Zoom n'est pas connecté`,
	VideoCallTilePaused: 'Cette vidéo est en pause en raison de problèmes avec votre réseau',
	VideoCallTranscriptionFormDescription: 'Vous pouvez ajuster ces paramètres à tout moment',
	VideoCallTranscriptionFormHeading: 'Personnalisez votre AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'La langue de sortie générée',
	VideoCallTranscriptionFormNoteTemplateField: 'Définir le modèle de note par défaut',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Aucun modèle avec IA trouvé',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Choisissez un modèle',
	VideoCallTranscriptionPronounField: 'Votre pronom',
	VideoCallTranscriptionRecordingNote:
		'À la fin de la session, vous recevrez une note générée <strong>{noteTemplate}</strong> et une transcription.',
	VideoCallTranscriptionReferClientField: 'Se référer au client comme',
	VideoCallTranscriptionReferPractitionerField: 'Se référer au praticien comme',
	VideoCallTranscriptionTitle: 'AI Scribe',
	VideoCallTranscriptionVerbosityField: 'Verbosité',
	VideoCallTranscriptionWritingPerspectiveField: `Perspective d'écriture`,
	VideoCalls: 'Appels vidéo',
	VideoConferencing: 'Visioconférence',
	VideoOff: 'La vidéo est éteinte',
	VideoOn: 'La vidéo est éteinte',
	VideoQual360: 'Faible qualité (360p)',
	VideoQual540: 'Qualité moyenne (540p)',
	VideoQual720: 'Haute qualité (720p)',
	View: 'Voir',
	ViewAll: 'Tout voir',
	ViewAppointment: 'Afficher le rendez-vous',
	ViewBy: 'Vu par',
	ViewClaim: 'Voir la réclamation',
	ViewCollection: 'Voir la collection',
	ViewDetails: 'Voir les détails',
	ViewEnrollment: `Voir l'inscription`,
	ViewPayment: 'Voir le paiement',
	ViewRecord: `Afficher l'enregistrement`,
	ViewRemittanceAdvice: `Voir l'avis de paiement`,
	ViewRemittanceAdviceHeader: 'Avis de versement de réclamation',
	ViewRemittanceAdviceSubheader: 'Réclamation {claimNumber} • Virement bancaire n° {remittanceReference}',
	ViewSettings: 'Afficher les paramètres',
	ViewStripeDashboard: 'Afficher le tableau de bord Stripe',
	ViewTemplate: 'Afficher le modèle',
	ViewTemplates: 'Voir les modèles',
	ViewableBy: 'Visible par',
	ViewableByHelper: `Vous et l'équipe avez toujours accès aux notes que vous publiez. Vous pouvez choisir de partager cette note avec le client et/ou ses relations`,
	Viewer: 'Téléspectateur',
	VirtualLocation: 'Emplacement virtuel',
	VisibleTo: 'Visible à',
	VisitOurHelpCentre: `Visitez notre centre d'aide`,
	VisualEffects: 'Effets visuels',
	VoiceFocus: 'Concentration vocale',
	VoiceFocusLabel: `Filtre le son de votre micro qui n'est pas de la parole`,
	Void: 'Vide',
	VoidCancelPriorClaim: 'Annuler/Remplacer une réclamation antérieure',
	WaitingforMins: 'En attente pendant {count} minutes',
	Warning: 'Avertissement',
	WatchAVideo: 'regarder une vidéo',
	WatchDemoVideo: 'Regarder la vidéo de démonstration',
	WebConference: 'Conférence Web',
	WebConferenceOrVirtualLocation: 'Conférence Web / emplacement virtuel',
	WebDeveloper: 'Développeur web',
	WebsiteOptional: 'Site web<span>(Optionnel)</span>',
	WebsiteUrl: 'URL de site web',
	Wednesday: 'Mercredi',
	Week: 'Semaine',
	WeekPlural: '{count, plural, one {semaine} other {semaines}}',
	Weekly: 'Hebdomadaire',
	WeeksPlural: '{age, plural, one {# semaine} other {# semaines}}',
	WelcomeBack: 'Content de te revoir',
	WelcomeBackName: 'Bienvenue, {name}',
	WelcomeName: 'Bienvenue {name}',
	WelcomeToCarepatron: 'Bienvenue chez Carepatron',
	WhatCanIHelpWith: 'En quoi puis-je aider ?',
	WhatDidYouLikeResponse: `Qu'avez-vous aimé dans cette réponse ?`,
	WhatIsCarepatron: 'Qu’est-ce que Carepatron ?',
	WhatMadeYouCancel: `Qu'est-ce qui vous a poussé à annuler votre forfait ?
 Cochez toutes les cases.`,
	WhatServicesDoYouOffer: 'Quels <mark>services</mark> proposez-vous ?',
	WhatServicesDoYouOfferDescription: `Vous pouvez modifier ou ajouter d'autres services plus tard.`,
	WhatsYourAvailability: 'Quelle est votre <mark>disponibilité</mark> ?',
	WhatsYourAvailabilityDescription: `Vous pouvez ajouter d'autres horaires plus tard.`,
	WhatsYourBusinessName: 'Quel est le <mark>nom de votre entreprise</mark> ?',
	WhatsYourTeamSize: 'Quelle est la <mark>taille de votre équipe</mark> ?',
	WhatsYourTeamSizeDescription: 'Cela nous aidera à configurer correctement votre espace de travail.',
	WhenThisHappens: 'Quand cela se produit :',
	WhichBestDescribesYou: 'Quelle <mark>description</mark> vous correspond le mieux ?',
	WhichPlatforms: 'Quelles plateformes ?',
	Wife: 'Épouse',
	WorkflowDescription: 'Description du flux de travail',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Les modèles peuvent être liés à des flux de travail pour des processus plus fluides. Affichez les flux de travail liés pour les suivre et les mettre à jour facilement.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Connectez vos SMS + emails en fonction de déclencheurs communs',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Automations de flux de travail',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Flux de travail automatisés',
	WorkflowTemplateConfigKey_Body: 'Corps',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Afficher la marque',
	WorkflowTemplateConfigKey_Content: 'Contenu',
	WorkflowTemplateConfigKey_Footer: 'Pied de page',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Afficher le pied de page',
	WorkflowTemplateConfigKey_Header: 'En-tête',
	WorkflowTemplateConfigKey_Header_IsVisible: `Afficher l'en-tête`,
	WorkflowTemplateConfigKey_SecurityFooter: 'Pied de page de sécurité',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Afficher le pied de page de sécurité',
	WorkflowTemplateConfigKey_Subject: 'Objet',
	WorkflowTemplateConfigKey_Title: 'Titre',
	WorkflowTemplateDeleteConfirmationMessage:
		'Êtes-vous sûr de vouloir supprimer ce modèle ? Cette action est irréversible.',
	WorkflowTemplateDeleteConfirmationTitle: 'Supprimer le modèle de notification',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Êtes-vous sûr ? Cela ne supprimera que la version {locale} —les autres langues ne seront pas affectées. Cette action est irréversible.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Supprimer le modèle ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Modèle de notification supprimé avec succès',
	WorkflowTemplateEditorDetailsTab: 'Détails du modèle',
	WorkflowTemplateEditorEmailContent: 'Contenu du courriel',
	WorkflowTemplateEditorEmailContentTab: `Contenu de l'email`,
	WorkflowTemplateEditorThemeTab: 'Thème',
	WorkflowTemplatePreviewerAlert: `Les aperçus utilisent des données d'échantillon pour montrer ce que vos clients verront.`,
	WorkflowTemplateResetEmailContentDialogDescription:
		'Êtes-vous sûr ? Cela réinitialisera la version à la version par défaut du système. Cette action est irréversible.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Réinitialiser le modèle',
	WorkflowTemplateSendTestEmail: 'Envoyer un email de test',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Testez la configuration de votre messagerie en vous envoyant un courriel de test.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Courriel du destinataire',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Envoyer un test',
	WorkflowTemplateSendTestEmailDialogTitle: 'Envoyer un e-mail de test',
	WorkflowTemplateSendTestEmailSuccess: 'Succès ! Votre <mark>{templateName}</mark> e-mail de test a été envoyé.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Gérez vos modèles et ajoutez des versions multilingues pour communiquer efficacement avec les clients.',
	WorkflowTemplateTemplateEditor: 'Éditeur de modèle',
	WorkflowTemplateTranslateLocaleError: `Quelque chose s'est mal passé lors de la traduction du contenu`,
	WorkflowTemplateTranslateLocaleSuccess: 'Traduit avec succès le contenu en **{locale}**',
	WorkflowsAndReminders: 'Flux de travail ',
	WorkflowsManagement: 'Gestion des flux de travail',
	WorksheetAndHandout: 'Feuille de travail/Document distribué',
	WorksheetsAndHandoutsDescription: `Pour l'engagement et l'éducation des clients`,
	Workspace: 'Espace de travail',
	WorkspaceBranding: 'Image de marque de l’espace de travail',
	WorkspaceBrandingDescription: `Modifiez l'image de marque de votre espace de travail avec un style qui correspond à votre professionnalisme et à votre personnalité. Personnalisez des factures à votre lien de réservation pour une belle expérience client.`,
	WorkspaceName: `Nom de l'espace de travail`,
	Workspaces: 'Espaces de travail',
	WriteOff: 'Radier',
	WriteOffModalDescription: 'Vous avez <mark>{count} {count, plural, one {ligne} other {lignes}}</mark> à amortir',
	WriteOffModalTitle: 'Ajustement de radiation',
	WriteOffReasonHelperText: `Il s'agit d'une note interne et ne sera pas visible par votre client.`,
	WriteOffReasonPlaceholder: `L'ajout d'un motif de radiation peut être utile lors de l'examen des transactions facturables`,
	WriteOffTotal: 'Écriture totale ({currencyCode})',
	Writer: 'Écrivain',
	Yearly: 'Annuel',
	YearsPlural: '{age, plural, one {# année} other {# années}}',
	Yes: 'Oui',
	YesArchive: 'Oui, archiver',
	YesDelete: 'Oui, supprimer',
	YesDeleteOverride: 'Oui, supprimer le remplacement',
	YesDeleteSection: 'Oui, supprimer',
	YesDisconnect: 'Oui, déconnecter',
	YesEnd: 'Oui, fin',
	YesEndTranscription: 'Oui, fin de la transcription',
	YesImFineWithThat: 'Oui, ça me va',
	YesLeave: 'Oui, partir',
	YesMinimize: 'Oui, réduire',
	YesOrNoAnswerTypeDescription: 'Configurer le type de réponse',
	YesOrNoFormPrimaryText: 'Oui | Non',
	YesOrNoFormSecondaryText: 'Choisissez les options oui ou non',
	YesProceed: 'Oui, continuez',
	YesRemove: 'Oui, supprimer',
	YesRestore: 'Oui, restaurer',
	YesStopIgnoring: 'Oui, arrêter d’ignorer',
	YesTransfer: 'Oui, transférer',
	Yesterday: 'Hier',
	YogaInstructor: 'Instructeur de yoga',
	You: 'Toi',
	YouArePresenting: 'Vous présentez',
	YouCanChooseMultiple: 'Vous pouvez en choisir plusieurs',
	YouCanSelectMultiple: 'Vous pouvez sélectionner plusieurs',
	YouHaveOngoingTranscription: 'Vous avez une transcription en cours',
	YourAnswer: 'Votre Réponse',
	YourDisplayName: `Votre nom d'affichage`,
	YourSpreadsheetColumns: 'Les colonnes de votre feuille de calcul',
	YourTeam: 'Ton équipe',
	ZipCode: 'Code postal',
	Zoom: 'Zoom',
	ZoomUserNotInAccountErrorCodeSnackbar: `Vous ne pouvez pas ajouter un appel Zoom pour ce membre de l'équipe. Veuillez vous référer aux <a>documents d'assistance pour plus d'informations.</a>`,
};

export default items;
