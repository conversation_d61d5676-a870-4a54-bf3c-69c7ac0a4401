import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: '사업자등록번호',
	AIPrompts: 'AI 프롬프트',
	ATeamMemberIsRequired: '팀원이 필요합니다.',
	AboutClient: '고객 정보',
	AcceptAppointment: '예약확인해주셔서 감사합니다',
	AcceptTermsAndConditionsRequired: '약관 동의 ',
	Accepted: '승인됨',
	AccessGiven: '접근이 허용됨',
	AccessPermissions: '접근 권한',
	AccessType: '접근 유형',
	Accident: '사고',
	Account: '계정',
	AccountCredit: '계정 크레딧',
	Accountant: '회계사',
	Action: '행동',
	Actions: '행위',
	Active: '활동적인',
	ActiveTags: '활성 태그',
	ActiveUsers: '활성 사용자',
	Activity: '활동',
	Actor: '배우',
	Acupuncture: '침 요법',
	Acupuncturist: '침술사',
	Acupuncturists: '침술사',
	AcuteManifestationOfAChronicCondition: '만성 질환의 급성 증상',
	Add: '추가하다',
	AddADescription: '설명을 추가하세요',
	AddALocation: '위치 추가',
	AddASecondTimezone: '두 번째 시간대 추가',
	AddAddress: '주소 추가',
	AddAnother: '  다른 것을 추가하세요',
	AddAnotherAccount: '다른 계정 추가',
	AddAnotherContact: '연락처 추가',
	AddAnotherOption: '다른 옵션을 추가하세요',
	AddAnotherTeamMember: '팀원 추가',
	AddAvailablePayers: '+ 이용 가능한 지불 수단 추가',
	AddAvailablePayersDescription:
		'작업 공간 지불자 목록에 추가할 지불자를 검색하세요. 추가 후에는 필요에 따라 등록을 관리하거나 지불자 세부 정보를 조정할 수 있습니다.',
	AddCaption: '캡션 추가',
	AddClaim: '클레임 추가',
	AddClientFilesModalDescription: '액세스를 제한하려면 "보기 가능" 체크박스에서 옵션을 선택하세요.',
	AddClientFilesModalTitle: '{name}에 대한 파일 업로드',
	AddClientNoteButton: '메모 추가',
	AddClientNoteModalDescription:
		'노트에 콘텐츠를 추가합니다. "Viewable by" 섹션을 사용하여 이 특정 노트를 볼 수 있는 하나 이상의 그룹을 선택합니다.',
	AddClientNoteModalTitle: '메모 추가',
	AddClientOwnerRelationshipModalDescription:
		'고객을 초대하면 고객은 자신의 프로필 정보를 관리하고, 프로필 정보에 대한 사용자 액세스를 관리할 수 있습니다.',
	AddClientOwnerRelationshipModalTitle: '고객을 초대합니다',
	AddCode: '코드 추가',
	AddColAfter: '뒤에 열 추가',
	AddColBefore: '열 추가하기 전에',
	AddCollection: '컬렉션 추가',
	AddColor: '색상을 추가하세요',
	AddColumn: '열 추가',
	AddContactRelationship: '연락처 관계 추가',
	AddContacts: '연락처 추가',
	AddCustomField: '사용자 정의 필드 추가',
	AddDate: '날짜 추가',
	AddDescription: '설명 추가',
	AddDetail: '세부 정보 추가',
	AddDisplayName: '표시 이름 추가',
	AddDxCode: '진단코드 추가',
	AddEmail: '이메일 추가',
	AddFamilyClientRelationshipModalDescription:
		'가족을 초대하면 케어 스토리와 클라이언트의 프로필 정보를 볼 수 있습니다. 관리자로 초대된 경우 클라이언트의 프로필 정보를 업데이트하고 사용자 액세스를 관리할 수 있습니다.',
	AddFamilyClientRelationshipModalTitle: '가족을 초대하세요',
	AddField: '필드 추가',
	AddFormField: '양식 필드 추가',
	AddImages: '이미지 추가',
	AddInsurance: '보험 추가',
	AddInvoice: '송장 추가',
	AddLabel: '라벨 추가',
	AddLanguage: '언어 추가',
	AddLocation: '위치 추가',
	AddManually: '수동으로 추가',
	AddMessage: '메시지 추가',
	AddNewAction: '새로운 작업 추가',
	AddNewSection: '새로운 섹션 추가',
	AddNote: '메모 추가',
	AddOnlineBookingDetails: '온라인 예약 세부 정보 추가',
	AddPOS: 'POS 추가',
	AddPaidInvoices: '지불된 송장 추가',
	AddPayer: '결제자 추가',
	AddPayment: '결제 추가',
	AddPaymentAdjustment: '결제 조정 추가',
	AddPaymentAdjustmentDisabledDescription: '지급 할당은 변경되지 않습니다.',
	AddPaymentAdjustmentEnabledDescription: '할당 가능한 금액이 줄어들 것입니다.',
	AddPhoneNumber: '전화번호 추가',
	AddPhysicalOrVirtualLocations: '실제 또는 가상 위치 추가',
	AddQuestion: '질문 추가',
	AddQuestionOrTitle: '질문이나 제목을 추가하세요',
	AddRelationship: '관계 추가',
	AddRelationshipModalTitle: '기존 연락처 연결',
	AddRelationshipModalTitleNewClient: '새로운 연락처 연결',
	AddRow: '행 추가',
	AddRowAbove: '위에 행 추가',
	AddRowBelow: '아래에 행 추가',
	AddService: '서비스 추가',
	AddServiceLocation: '서비스 위치 추가',
	AddServiceToCollections: '컬렉션에 서비스 추가',
	AddServiceToOneOrMoreCollections: '하나 이상의 컬렉션에 서비스 추가',
	AddServices: '서비스 추가',
	AddSignature: '서명 추가',
	AddSignaturePlaceholder: '서명에 포함할 추가 세부 정보를 입력하세요.',
	AddSmartDataChips: '스마트 데이터 칩 추가',
	AddStaffClientRelationshipsModalDescription:
		'직원을 선택하면 이 클라이언트에 대한 케어 스토리를 만들고 볼 수 있습니다. 또한 클라이언트 정보를 볼 수도 있습니다.',
	AddStaffClientRelationshipsModalTitle: '직원 관계 추가',
	AddTag: '태그 추가',
	AddTags: '태그 추가',
	AddTemplate: '템플릿 추가',
	AddTimezone: '시간대 추가',
	AddToClaim: '청구에 추가',
	AddToCollection: '컬렉션에 추가',
	AddToExisting: '기존에 추가',
	AddToStarred: '별표에 추가',
	AddUnclaimedItems: '청구되지 않은 항목 추가',
	AddUnrelatedContactWarning:
		'{contact}의 연락처가 아닌 사람을 추가했습니다. 공유를 진행하기 전에 콘텐츠가 적절한지 확인하십시오.',
	AddValue: '"{value}"를 추가합니다.',
	AddVideoCall: '영상통화 추가',
	AddVideoOrVoiceCall: '영상통화 또는 음성통화 추가',
	AddictionCounselor: '중독 상담사',
	AddingManualPayerDisclaimer:
		'공급자 목록에 수동으로 지불자를 추가해도 해당 지불자와 전자 청구 제출 연결이 설정되지는 않지만, 수동으로 청구를 생성하는 데 사용할 수 있습니다.',
	AddingTeamMembersIncreaseCostAlert: '새로운 팀원을 추가하면 월 구독료가 늘어납니다.',
	Additional: '추가의',
	AdditionalBillingProfiles: '추가 청구 프로필',
	AdditionalBillingProfilesSectionDescription:
		'특정 팀원, 지불인 또는 송장 템플릿에 사용되는 기본 청구 정보를 재정의합니다.',
	AdditionalFeedback: '추가 피드백',
	AddnNewWorkspace: '새로운 작업 공간',
	AddnNewWorkspaceSuccessSnackbar: '작업 공간이 생성되었습니다!',
	Address: '주소',
	AddressNumberStreet: '주소 (번호, 거리)',
	Adjustment: '조정',
	AdjustmentType: '조정 유형',
	Admin: '관리자',
	Admins: '관리자',
	AdminsOnly: '관리자만',
	AdvancedPlanInclusionFive: '계정 관리자',
	AdvancedPlanInclusionFour: '구글 애널리틱스',
	AdvancedPlanInclusionHeader: '플러스의 모든 것  ',
	AdvancedPlanInclusionOne: '역할 ',
	AdvancedPlanInclusionSix: '데이터 가져오기 지원',
	AdvancedPlanInclusionThree: '화이트 라벨링',
	AdvancedPlanInclusionTwo: '삭제된 데이터 보존 기간은 90일입니다.',
	AdvancedPlanMessage: '진료소의 요구 사항을 관리하십시오. 현재 계획을 검토하고 사용량을 모니터링하십시오.',
	AdvancedSettings: '고급 설정',
	AdvancedSubscriptionPlanSubtitle: '모든 기능으로 연습을 확장하세요',
	AdvancedSubscriptionPlanTitle: '고급의',
	AdvertisingManager: '광고 관리자',
	AerospaceEngineer: '항공우주 엔지니어',
	AgeYearsOld: '{age}살',
	Agenda: '의제',
	AgendaView: '일정 보기',
	AiAskSupportedFileTypes: '지원되는 파일 유형: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: '손끝에서 바로 사용 가능한 도우미',
	AiCopilotDisclaimer: 'AI 조종사는 실수를 할 수 있습니다. 중요한 정보를 확인하세요.',
	AiCreateNewConversation: '새로운 대화 만들기',
	AiEnhanceYourProductivity: '생산성 향상',
	AiPoweredTemplates: 'AI 기반 템플릿',
	AiScribeNoDeviceFoundErrorMessage: '귀하의 브라우저가 이 기능을 지원하지 않거나, 호환되는 기기가 없는 것 같습니다.',
	AiScribeUploadFormat: '지원되는 파일 유형: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: '한 번에 1개의 파일만',
	AiShowConversationHistory: '대화 기록 보기',
	AiSmartPromptNodePlaceholderText:
		'정확하고 개인화된 AI 결과를 생성하는 데 도움이 되는 사용자 지정 프롬프트를 입력하세요.',
	AiSmartPromptPrimaryText: 'AI 스마트 프롬프트',
	AiSmartPromptSecondaryText: '사용자 지정 AI 스마트 프롬프트 삽입',
	AiSmartReminders: 'AI 스마트 리마인더',
	AiTemplateBannerTitle: 'AI 기반 템플릿으로 작업을 간소화하세요',
	AiTemplates: 'AI 템플릿',
	AiTokens: 'AI 토큰',
	AiWorkBetterWithAi: 'AI와 함께 더 나은 작업을 하세요',
	All: '모두',
	AllAppointments: '모든 약속',
	AllCategories: '모든 카테고리',
	AllClients: '모든 클라이언트',
	AllContactPolicySelectorLabel: '<mark>{client}</mark>의 모든 연락처',
	AllContacts: '모든 연락처',
	AllContactsOf: `'{name}'의 모든 연락처`,
	AllDay: '하루 종일',
	AllInboxes: '모든 받은 편지함',
	AllIndustries: '모든 산업',
	AllLocations: '모든 위치',
	AllMeetings: '모든 회의',
	AllNotificationsRestoredMessage: '모든 알림이 복구되었습니다',
	AllProfessions: '모든 직업',
	AllReminders: '모든 알림',
	AllServices: '모든 서비스',
	AllStatuses: '모든 상태',
	AllTags: '모든 태그',
	AllTasks: '모든 작업',
	AllTeamMembers: '모든 팀원들',
	AllTypes: '모든 유형',
	Allocated: '할당됨',
	AllocatedItems: '할당된 항목',
	AllocationTableEmptyState: '지불 할당이 발견되지 않았습니다',
	AllocationTotalWarningMessage: `할당된 금액이 총 지불금액을 초과합니다.
 아래 항목을 검토하세요.`,
	AllowClientsToCancelAnytime: '언제든지 클라이언트가 취소할 수 있도록 허용',
	AllowNewClient: '새로운 클라이언트 허용',
	AllowNewClientHelper: '신규 고객은 이 서비스를 예약할 수 있습니다.',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: '최소 {hours}시간 전에 예약하십시오.',
	AllowToUseSavedCard: '저장된 카드를 {provider}에서 향후 사용하도록 허용',
	AllowVideoCalls: '영상통화 허용',
	AlreadyAdded: '이미 추가됨',
	AlreadyHasAccess: '접근 가능',
	AlreadyHasAccount: '이미 계정이 있으신가요?',
	Always: '항상',
	AlwaysIgnore: '항상 무시하다',
	Amount: '양',
	AmountDue: '지불해야 할 금액',
	AmountOfReferralRequests: '{amount, plural, one {# 추천 요청} other {# 추천 요청}}',
	AmountPaid: '지불금액',
	AnalyzingAudio: '오디오 분석 중...',
	AnalyzingInputContent: '입력 콘텐츠 분석 중...',
	AnalyzingRequest: '요청을 분석하는 중...',
	AnalyzingTemplateContent: '템플릿 콘텐츠를 분석하는 중...',
	And: '그리고',
	Annually: '매년',
	Anonymous: '익명의',
	AnswerExceeded: '답변은 300자 이내여야 합니다.',
	AnyStatus: '모든 상태',
	AppNotifications: '알림',
	AppNotificationsClearanceHeading: '잘하셨어요! 모든 활동을 지웠어요',
	AppNotificationsEmptyHeading: '귀하의 작업 공간 활동이 곧 여기에 표시됩니다.',
	AppNotificationsEmptySubtext: '지금 당장은 취할 조치가 없습니다.',
	AppNotificationsIgnoredCount: '{total} 무시됨',
	AppNotificationsUnread: '{total} 읽지 않음',
	Append: '추가',
	Apply: '적용하다',
	ApplyAccountCredit: '계정 크레딧 적용',
	ApplyDiscount: '할인 적용',
	ApplyVisualEffects: '시각 효과 적용',
	ApplyVisualEffectsNotSupported: '시각 효과 적용이 지원되지 않습니다.',
	Appointment: '약속',
	AppointmentAssignedNotificationSubject: '{actorProfileName} 님이 {appointmentName}을(를) 할당했습니다.',
	AppointmentCancelledNotificationSubject: '{actorProfileName}님이 {appointmentName}을 취소하셨습니다.',
	AppointmentConfirmedNotificationSubject: '{actorProfileName}님이 {appointmentName}를 확인했습니다.',
	AppointmentDetails: '약속 세부 사항',
	AppointmentLocation: '예약 위치',
	AppointmentLocationDescription:
		'기본 가상 및 물리적 위치를 관리하세요. 약속이 예약되면 이러한 위치가 자동으로 적용됩니다.',
	AppointmentNotFound: '약속을 찾을 수 없습니다',
	AppointmentReminder: '약속 알림',
	AppointmentReminders: '약속 알림',
	AppointmentRemindersInfo: '고객 약속에 대한 자동 알림을 설정하여 불참 및 취소를 방지하세요.',
	AppointmentRescheduledNotificationSubject: '{actorProfileName}님이 {appointmentName}을 재예약했습니다.',
	AppointmentSaved: '약속이 저장되었습니다',
	AppointmentStatus: '약속 상태',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0분} other {{minutes}분}} } one {{hours}시간 {minutes, plural, =0 {} other {{minutes}분}}} other {{hours}시간 {minutes, plural, =0 {} other {{minutes}분}}} }',
	AppointmentUndone: '약속 취소됨',
	Appointments: '설비',
	Archive: '보관소',
	ArchiveClients: '아카이브 클라이언트',
	Archived: '보관됨',
	AreYouAClient: '당신은 고객이신가요?',
	AreYouStillThere: '아직 거기 계신가요?',
	AreYouSure: '정말이에요?',
	Arrangements: '준비',
	ArtTherapist: '아트 테라피스트',
	Articles: '기사',
	Artist: '아티스트',
	AskAI: 'AI에게 물어보세요',
	AskAiAddFormField: '양식 필드 추가',
	AskAiChangeFormality: '형식을 바꾸다',
	AskAiChangeToneToBeMoreProfessional: '보다 전문적으로 보이려면 톤을 변경하세요',
	AskAiExplainThis: 'AskAi설명하기',
	AskAiExplainWhatThisDocumentIsAbout: '이 문서의 내용을 설명하세요',
	AskAiExplainWhatThisImageIsAbout: '이 이미지가 무엇에 대한 것인지 설명하세요',
	AskAiFixSpellingAndGrammar: '철자와 문법을 수정하세요',
	AskAiGenerateACaptionForThisImage: '이 이미지에 대한 캡션을 생성하세요',
	AskAiGenerateFromThisPage: '이 페이지에서 생성',
	AskAiGetStarted: '시작하세요',
	AskAiGiveItAFriendlyTone: '친근한 어조로 말해보세요',
	AskAiGreeting: '안녕하세요 {firstName}님! 오늘 어떻게 도와드릴까요?',
	AskAiHowCanIHelpWithYourContent: '귀하의 콘텐츠에 관해 어떻게 도움을 드릴 수 있나요?',
	AskAiInsert: '끼워 넣다',
	AskAiMakeItMoreCasual: '좀 더 캐주얼하게 만들어보세요',
	AskAiMakeThisTextMoreConcise: '이 텍스트를 더 간결하게 만들어보세요',
	AskAiMoreProfessional: '더욱 전문적으로',
	AskAiOpenPreviousNote: '이전 노트 열기',
	AskAiPondering: '숙고하다',
	AskAiReplace: '바꾸다',
	AskAiReviewOrEditSelection: '선택 항목 검토 또는 편집',
	AskAiRuminating: '반추하다',
	AskAiSeeMore: '더 보기',
	AskAiSimplifyLanguage: '언어를 단순화하다',
	AskAiSomethingWentWrong: '문제가 발생했습니다. 문제가 지속될 경우 도움말 센터를 통해 문의해 주세요.',
	AskAiStartWithATemplate: '템플릿으로 시작하세요',
	AskAiSuccessfullyCopiedResponse: 'AI 응답을 성공적으로 복사했습니다.',
	AskAiSuccessfullyInsertedResponse: 'AI 응답을 성공적으로 삽입했습니다.',
	AskAiSuccessfullyReplacedResponse: 'AI 대응을 성공적으로 대체했습니다',
	AskAiSuggested: '제안된',
	AskAiSummariseTextIntoBulletPoints: '텍스트를 요점으로 요약합니다',
	AskAiSummarizeNote: '요약 노트',
	AskAiThinking: '생각',
	AskAiToday: '오늘 {time}',
	AskAiWhatDoYouWantToDoWithThisForm: '이 양식으로 무엇을 하시겠습니까?',
	AskAiWriteProfessionalNoteUsingTemplate: '템플릿을 사용하여 전문적인 메모를 작성하세요',
	AskAskAiAnything: 'AI에게 무엇이든 물어보세요',
	AskWriteSearchAnything: '질문하거나, @를 쓰거나, 무엇이든 검색해 보세요...',
	Asking: '질문',
	Assessment: '평가',
	Assessments: '평가',
	AssessmentsCategoryDescription: '고객 평가 기록을 위한',
	AssignClients: '클라이언트 할당',
	AssignNewClients: '클라이언트 할당',
	AssignServices: '서비스 할당',
	AssignTeam: '팀 배정',
	AssignTeamMember: '팀원 배정하기',
	Assigned: '할당된',
	AssignedClients: '할당된 클라이언트',
	AssignedServices: '할당된 서비스',
	AssignedServicesDescription: '귀하에게 할당된 서비스를 보고 관리하고, 맞춤 가격을 반영하여 가격을 조정하세요. ',
	AssignedTeam: '할당된 팀',
	AthleticTrainer: '운동 트레이너',
	AttachFiles: '파일 첨부',
	AttachLogo: '붙이다',
	Attachment: '부착',
	AttachmentBlockedFileType: '보안상의 이유로 차단되었습니다!',
	AttachmentTooLargeFileSize: '파일이 너무 큽니다',
	AttachmentUploadItemComplete: '완벽한',
	AttachmentUploadItemError: '업로드 실패',
	AttachmentUploadItemLoading: '로딩중',
	AttemptingToReconnect: '재연결을 시도하는 중...',
	Attended: '참석했다',
	AttendeeBeingMutedTooltip: `호스트가 당신을 음소거했습니다. 음소거 해제를 요청하려면 '손들기'를 사용하세요.`,
	AttendeeWithId: '참석자 {attendeeId}',
	Attendees: '참석자',
	AttendeesCount: '{count}명 참석자',
	Attending: '참석하다',
	Audiologist: '청각학자',
	Aunt: '이모',
	Australia: '호주',
	AuthenticationCode: '인증 코드',
	AuthoriseProvider: '{provider} 승인',
	AuthorisedProviders: '공인 공급자',
	AutoDeclineAllFutureOption: '새로운 이벤트 또는 약속만',
	AutoDeclineAllOption: '새로운 이벤트 및 기존 약속',
	AutoDeclinePrimaryText: '자동으로 이벤트 거부',
	AutoDeclineSecondaryText: '휴가 기간 동안의 이벤트는 자동으로 거절됩니다.',
	AutogenerateBillings: '청구 문서 자동 생성',
	AutogenerateBillingsDescription:
		'자동 청구 문서는 매월 마지막 날에 생성됩니다. 송장과 슈퍼빌 영수증은 언제든지 수동으로 생성할 수 있습니다.',
	AutomateWorkflows: '워크플로 자동화',
	AutomaticallySendSuperbill: '자동으로 슈퍼빌 영수증을 보냅니다',
	AutomaticallySendSuperbillHelperText:
		'슈퍼빌은 보험금 상환을 위해 고객에게 제공된 서비스에 대한 자세한 영수증입니다.',
	Automation: '오토메이션',
	AutomationActionSendEmailLabel: '이메일 보내기',
	AutomationActionSendSMSLabel: 'SMS 보내기',
	AutomationAndReminders: '오토메이션 ',
	AutomationDeletedSuccessMessage: '자동화를 성공적으로 삭제했습니다',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: '시간 이벤트',
	AutomationParams_timeUnit: '단위',
	AutomationParams_timeValue: '숫자',
	AutomationPublishSuccessMessage: '자동화가 성공적으로 게시되었습니다',
	AutomationPublishWarningTooltip: '자동화 구성을 다시 확인하고 제대로 구성되었는지 확인하세요.',
	AutomationTriggerEventCancelledDescription: '이벤트가 취소되거나 삭제될 때 트리거됩니다.',
	AutomationTriggerEventCancelledLabel: '이벤트 취소됨',
	AutomationTriggerEventCreatedDescription: '이벤트가 생성될 때 트리거됩니다.',
	AutomationTriggerEventCreatedLabel: '새로운 이벤트',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'이벤트가 생성되거나 업데이트될 때 트리거됩니다(취소되는 경우 제외)',
	AutomationTriggerEventCreatedOrUpdatedLabel: '새로운 이벤트 또는 업데이트된 이벤트',
	AutomationTriggerEventEndedDescription: '이벤트가 종료되면 트리거됩니다.',
	AutomationTriggerEventEndedLabel: '이벤트 종료',
	AutomationTriggerEventStartsDescription: '이벤트가 시작되기 전에 지정된 시간이 지나면 트리거됩니다.',
	AutomationTriggerEventStartsLabel: '이벤트 시작',
	Automations: '자동화',
	Availability: '유효성',
	AvailabilityDisableSchedule: '일정 비활성화',
	AvailabilityDisabled: '장애가 있는',
	AvailabilityEnableSchedule: '일정 활성화',
	AvailabilityEnabled: '활성화됨',
	AvailabilityNoActiveBanner:
		'모든 일정을 꺼두었습니다. 클라이언트가 온라인으로 예약할 수 없으며, 모든 향후 예약은 수동으로 확인해야 합니다.',
	AvailabilityNoActiveConfirmationDescription:
		'이 가용성을 비활성화하면 활성 일정이 없게 됩니다. 클라이언트는 온라인으로 예약할 수 없으며, 실무자가 한 모든 예약은 근무 시간 외에 이루어집니다.',
	AvailabilityNoActiveConfirmationProceed: '네, 진행합니다',
	AvailabilityNoActiveConfirmationTitle: '활성화된 일정이 없습니다',
	AvailabilityToggle: '활성화된 일정',
	AvailabilityUnsetDate: '날짜 미정',
	AvailableLocations: '이용 가능한 위치',
	AvailablePayers: '사용 가능한 지불자',
	AvailablePayersEmptyState: '지불자가 선택되지 않았습니다.',
	AvailableTimes: `<br><br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>
<br>`,
	Back: '뒤쪽에',
	BackHome: '집으로 돌아가다',
	BackToAppointment: '예약으로 돌아가기',
	BackToLogin: '로그인으로 돌아가기',
	BackToMapColumns: '지도 열로 돌아가기',
	BackToTemplates: '템플릿으로 돌아가기',
	BackToUploadFile: '파일 업로드로 돌아가기',
	Banker: '은행가',
	BasicBlocks: '기본 블록',
	BeforeAppointment: '약속 {interval} {unit} 전에 {deliveryType} 알림 보내기',
	BehavioralAnalyst: '행동 분석가',
	BehavioralHealthTherapy: '행동 건강 치료',
	Beta: '베타',
	BillTo: '청구서',
	BillableItems: '청구 가능한 항목',
	BillableItemsEmptyState: '청구 가능한 항목이 없습니다.',
	Biller: '청구자',
	Billing: '청구',
	BillingAddress: '청구서 주소',
	BillingAndReceiptsUnauthorisedMessage: '이 정보에 액세스하려면 송장 및 지불 보기 액세스 권한이 필요합니다.',
	BillingBillablesTab: '청구 가능 금액',
	BillingClaimsTab: '청구',
	BillingDetails: '결제 세부 정보',
	BillingDocuments: '청구 문서',
	BillingDocumentsClaimsTab: '청구',
	BillingDocumentsEmptyState: '{tabType}이 발견되지 않았습니다.',
	BillingDocumentsInvoicesTab: '송장',
	BillingDocumentsSuperbillsTab: '슈퍼빌',
	BillingInformation: '결제 정보',
	BillingInvoicesTab: '송장',
	BillingItems: '청구 항목',
	BillingPaymentsTab: '결제',
	BillingPeriod: '결제 기간',
	BillingProfile: '청구 프로필',
	BillingProfileOverridesDescription: '이 청구 프로필의 사용을 특정 팀원으로 제한합니다.',
	BillingProfileOverridesHeader: '접근 제한',
	BillingProfileProviderType: '제공자 유형',
	BillingProfileTypeIndividual: '실무자',
	BillingProfileTypeIndividualSubLabel: '1형 NPI',
	BillingProfileTypeOrganisation: '조직',
	BillingProfileTypeOrganisationSubLabel: '2형 NPI',
	BillingProfiles: '청구 프로필',
	BillingProfilesEditHeader: '{name} 청구 프로필 편집',
	BillingProfilesNewHeader: '새로운 청구 프로필',
	BillingProfilesSectionDescription:
		'송장과 보험금 지급에 적용할 수 있는 청구 프로필을 설정하여 의료인과 보험 지불자의 청구 정보를 관리하세요.',
	BillingSearchPlaceholder: '항목 검색',
	BillingSettings: '결제 설정',
	BillingSuperbillsTab: '슈퍼빌',
	BiomedicalEngineer: '생물의학 엔지니어',
	BlankInvoice: '빈 송장',
	BlueShieldProviderNumber: 'Blue Shield 제공자 번호',
	Body: '몸',
	Bold: '용감한',
	BookAgain: '다시 예약하기',
	BookAppointment: '예약하기',
	BookableOnline: '온라인으로 예약 가능',
	BookableOnlineHelper: '고객은 이 서비스를 온라인으로 예약할 수 있습니다.',
	BookedOnline: '온라인으로 예약됨',
	Booking: '예약',
	BookingAnalyticsIntegrationPanelDescription:
		'Google 태그 관리자를 설정하여 온라인 예약 흐름에서 주요 작업과 전환을 추적하세요. 사용자 상호작용에 대한 귀중한 데이터를 수집하여 마케팅 노력을 개선하고 예약 경험을 최적화하세요.',
	BookingAnalyticsIntegrationPanelTitle: '분석 통합',
	BookingAndCancellationPolicies: '예약 ',
	BookingButtonEmbed: '단추',
	BookingButtonEmbedDescription: '귀하의 웹사이트에 온라인 예약 버튼을 추가합니다',
	BookingDirectTextLink: '직접 텍스트 링크',
	BookingDirectTextLinkDescription: '온라인 예약 페이지를 엽니다',
	BookingFormatLink: '형식 링크',
	BookingFormatLinkButtonTitle: '버튼 제목',
	BookingInlineEmbed: '인라인 임베드',
	BookingInlineEmbedDescription: '귀하의 웹사이트에 온라인 예약 페이지를 직접 로드합니다.',
	BookingLink: '예약 링크',
	BookingLinkModalCopyText: '복사',
	BookingLinkModalDescription: '이 링크가 있는 클라이언트가 팀원이나 서비스를 예약할 수 있도록 허용합니다.',
	BookingLinkModalHelpText: '온라인 예약을 설정하는 방법을 알아보세요',
	BookingLinkModalTitle: '예약 링크를 공유하세요',
	BookingPolicies: '예약 정책',
	BookingPoliciesDescription: '클라이언트가 온라인 예약을 할 수 있는 시간을 설정합니다.',
	BookingTimeUnitDays: '날',
	BookingTimeUnitHours: '시간',
	BookingTimeUnitMinutes: '분',
	BookingTimeUnitMonths: '개월',
	BookingTimeUnitWeeks: '주',
	BottomNavBilling: '청구',
	BottomNavGettingStarted: '집',
	BottomNavMore: '더',
	BottomNavNotes: '노트',
	Brands: '브랜드',
	Brother: '형제',
	BrotherInLaw: '처남',
	BrowseOrDragFileHere: '<link>찾아보기</link> 또는 파일을 여기에 끌어놓으세요',
	BrowseOrDragFileHereDescription: 'PNG, JPG (최대 {limit})',
	BufferAfterTime: '{time} 분 후',
	BufferAndLabel: '그리고',
	BufferAppointmentLabel: '약속',
	BufferBeforeTime: '{time} 분 전',
	BufferTime: '버퍼 시간',
	BufferTimeViewLabel: '약속 {bufferBefore} 분 전과 {bufferAfter} 분 후',
	BulkArchiveClientsDescription: '이 클라이언트를 보관하시겠습니까? 나중에 다시 활성화할 수 있습니다.',
	BulkArchiveSuccess: '성공적으로 클라이언트를 보관했습니다',
	BulkArchiveUndone: '대량 보관 취소',
	BulkPermanentDeleteDescription: '이는 **{count}개의 대화**를 삭제합니다. 이 작업은 되돌릴 수 없습니다.',
	BulkPermanentDeleteTitle: '대화를 영구히 삭제',
	BulkUnarchiveSuccess: '클라이언트를 성공적으로 보관 해제했습니다.',
	BulletedList: '요점 목록',
	BusinessAddress: '사업장 주소',
	BusinessAddressOptional: '사업장 주소 <span>(선택사항)</span>',
	BusinessName: '사업체 이름',
	Button: '버튼',
	By: '에 의해',
	CHAMPUSIdentificationNumber: 'CHAMPUS 식별번호',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC가 필요합니다',
	Calendar: '달력',
	CalendarAppSyncFormDescription: 'Carepatron 이벤트를 동기화합니다.',
	CalendarAppSyncPanelTitle: '연결된 앱 동기화',
	CalendarDescription: '약속을 관리하거나 개인 작업 및 알림을 설정하세요',
	CalendarDetails: '달력 세부 정보',
	CalendarDetailsDescription: '일정과 약속 표시 설정을 관리합니다.',
	CalendarScheduleNew: '새로운 일정',
	CalendarSettings: '캘린더 설정',
	Call: '부르다',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong>님이 화상 통화에 참여했습니다',
	CallChangeLayoutTextContent: '선택 사항은 향후 회의를 위해 저장됩니다.',
	CallIdlePrompt: '참여를 기다리시겠습니까, 아니면 나중에 다시 시도하시겠습니까?',
	CallLayoutOptionAuto: '자동차',
	CallLayoutOptionSidebar: '사이드바',
	CallLayoutOptionSpotlight: '스포트라이트',
	CallLayoutOptionTiled: '타일',
	CallNoAttendees: '회의에 참석자가 없습니다.',
	CallSessionExpiredError: '세션이 만료되었습니다. 통화가 종료되었습니다. 다시 참여해 보세요.',
	CallWithPractitioner: '{practitioner}와 통화',
	CallsListCreateButton: '새로운 호출',
	CallsListEmptyState: '활성 통화 없음',
	CallsListItemEndCall: '통화 종료',
	CamWarningMessage: '카메라에서 문제가 감지되었습니다.',
	Camera: '카메라',
	CameraAndMicIssueModalDescription: `Carepatron이 카메라와 마이크에 액세스할 수 있도록 설정해 주세요.
 자세한 내용은 <a>이 가이드를 따르세요.</a>`,
	CameraAndMicIssueModalTitle: '카메라와 마이크가 차단되었습니다',
	CameraQuality: '카메라 품질',
	CameraSource: '카메라 소스',
	CanModifyReadOnlyEvent: '이 이벤트는 수정할 수 없습니다.',
	Canada: '캐나다',
	Cancel: '취소',
	CancelClientImportDescription: '정말로 이 가져오기를 취소하시겠습니까?',
	CancelClientImportPrimaryAction: '예, 가져오기 취소',
	CancelClientImportSecondaryAction: '계속 편집',
	CancelClientImportTitle: '클라이언트 가져오기 취소',
	CancelImportButton: '가져오기 취소',
	CancelPlan: '계획 취소',
	CancelPlanConfirmation: `플랜을 취소하면 이번 달의 미지불 잔액이 자동으로 귀하의 계좌에 청구됩니다.
 청구되는 사용자를 다운그레이드하려면 간단히 팀 구성원을 제거하면 Carepatron에서 자동으로 구독 가격을 업데이트합니다.`,
	CancelSend: '보내기 취소',
	CancelSubscription: '구독 취소',
	Canceled: '취소됨',
	CancellationPolicy: '취소 정책',
	Cancelled: '취소',
	CannotContainSpecialCharactersError: '{specialCharacters}를 포함할 수 없습니다.',
	CannotDeleteInvoice: '온라인 결제로 결제된 송장은 삭제할 수 없습니다.',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b>은(는) <b>{group}</b> 그룹에서 이동할 수 없습니다.',
	CannotMoveServiceOutsideCollections: '서비스는 컬렉션 외부로 이동할 수 없습니다.',
	CapeTown: '케이프타운',
	Caption: '표제',
	CaptureNameFieldLabel: '다른 사람들이 당신을 지칭하기를 원하는 이름',
	CapturePaymentMethod: '결제 방법 캡처',
	CapturingAudio: '오디오 캡처',
	CapturingSignature: '서명을 캡처하는 중...',
	CardInformation: '카드 정보',
	CardNumberRequired: '카드번호가 필요합니다',
	CardiacRehabilitationSpecialist: '심장 재활 전문의',
	Cardiologist: '심장내과 의사',
	CareAiNoConversations: '아직 대화가 없습니다.',
	CareAiNoConversationsDescription: '{aiName}와 대화를 시작하여 시작하세요.',
	CareAssistant: '케어 어시스턴트',
	CareManager: '케어 매니저',
	Caregiver: '보호자',
	CaregiverCreateModalDescription:
		'직원을 관리자로 추가하면 케어 스토리를 만들고 관리할 수 있습니다. 또한 클라이언트를 만들고 관리할 수 있는 전체 액세스 권한도 부여됩니다.',
	CaregiverCreateModalTitle: '새로운 팀원',
	CaregiverListCantAddStaffInfoTitle:
		'구독에 대한 최대 직원 수에 도달했습니다. 직원을 더 추가하려면 플랜을 업그레이드하세요.',
	CaregiverListCreateButton: '새로운 팀원',
	CaregiverListEmptyState: '보호자가 추가되지 않았습니다',
	CaregiversListItemRemoveStaff: '직원 제거',
	CarepatronApp: '케어패트론 앱',
	CarepatronCommunity: '지역 사회',
	CarepatronFieldAddress: '주소',
	CarepatronFieldAssignedStaff: '배정된 직원',
	CarepatronFieldBirthDate: '생년월일',
	CarepatronFieldEmail: '이메일',
	CarepatronFieldEmploymentStatus: '고용 상태',
	CarepatronFieldEthnicity: '민족성',
	CarepatronFieldFirstName: '이름',
	CarepatronFieldGender: '성별',
	CarepatronFieldIdentificationNumber: '식별번호',
	CarepatronFieldIsArchived: '상태',
	CarepatronFieldLabel: '상표',
	CarepatronFieldLastName: '성',
	CarepatronFieldLivingArrangements: '생활 환경',
	CarepatronFieldMiddleNames: '중간 이름',
	CarepatronFieldOccupation: '직업',
	CarepatronFieldPhoneNumber: '전화 번호',
	CarepatronFieldRelationshipStatus: '관계 상태',
	CarepatronFieldStatus: '상태',
	CarepatronFieldStatusHelperText: '최대 10개 상태',
	CarepatronFieldTags: '태그',
	CarepatronFields: '케어패트론 필드',
	Cash: '현금',
	Category: '범주',
	CategoryInputPlaceholder: '템플릿 카테고리를 선택하세요',
	CenterAlign: '가운데 정렬',
	Central: '본부',
	ChangeLayout: '레이아웃 변경',
	ChangeLogo: '변화',
	ChangePassword: '비밀번호 변경',
	ChangePasswordFailureSnackbar: '죄송합니다. 비밀번호가 변경되지 않았습니다. 이전 비밀번호가 올바른지 확인하세요.',
	ChangePasswordHelperInfo: '최소 길이 {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'비밀번호가 성공적으로 변경되었습니다! 다음에 로그인할 때는 반드시 그 비밀번호를 사용하세요.',
	ChangeSubscription: '구독 변경',
	ChangesNotAllowed: '이 필드는 변경할 수 없습니다.',
	ChargesDisabled: '요금 비활성화',
	ChargesEnabled: '요금 활성화됨',
	ChargesStatus: '요금 상태',
	ChartAndDiagram: '차트/다이어그램',
	ChartsAndDiagramsCategoryDescription: '고객 데이터와 진행 상황을 설명하기 위해',
	ChatEditMessage: '메시지 수정',
	ChatReplyTo: '{name} 님께 답장',
	ChatTypeMessageTo: '메시지 {name}',
	Check: '확인하다',
	CheckList: '체크리스트',
	Chef: '요리사',
	Chiropractic: '카이로프랙틱',
	Chiropractor: '지압 사',
	Chiropractors: '카이로프랙터',
	ChooseACollection: '컬렉션 선택하기',
	ChooseAContact: '연락처를 선택하세요',
	ChooseAccountTypeHeader: '어떤 설명이 당신을 가장 잘 설명합니까?',
	ChooseAction: '작업을 선택하세요',
	ChooseAnAccount: '계정을 선택하세요',
	ChooseAnOption: '옵션을 선택하세요',
	ChooseBillingProfile: '결제 프로필 선택',
	ChooseClaim: '클레임을 선택하세요',
	ChooseCollection: '컬렉션 선택',
	ChooseColor: '색상을 선택하세요',
	ChooseCustomDate: '사용자 지정 날짜를 선택하세요',
	ChooseDateAndTime: '날짜와 시간을 선택하세요',
	ChooseDxCodes: '진단코드를 선택하세요',
	ChooseEventType: '이벤트 유형을 선택하세요',
	ChooseFileButton: '파일을 선택하세요',
	ChooseFolder: '폴더 선택',
	ChooseInbox: '받은 편지함 선택',
	ChooseMethod: '방법을 선택하세요',
	ChooseNewOwner: '새로운 소유자를 선택하세요',
	ChooseOrganization: '조직 선택',
	ChoosePassword: '비밀번호를 선택하세요',
	ChoosePayer: '지불자를 선택하세요',
	ChoosePaymentMethod: '결제방법을 선택하세요',
	ChoosePhysicalOrRemoteLocations: '위치 입력 또는 선택',
	ChoosePlan: '{plan}을 선택하세요',
	ChooseProfessional: '전문가를 선택하세요',
	ChooseServices: '서비스를 선택하세요',
	ChooseSource: '출처 선택',
	ChooseSourceDescription: '클라이언트를 가져올 위치를 선택하세요. 파일인지 다른 소프트웨어 플랫폼인지 선택하세요.',
	ChooseTags: '태그를 선택하세요',
	ChooseTaxName: '세금 이름을 선택하세요',
	ChooseTeamMembers: '팀원을 선택하세요',
	ChooseTheme: '테마 선택',
	ChooseTrigger: '트리거를 선택하세요',
	ChooseYourProvider: '귀하의 공급자를 선택하세요',
	CircularProgressWithLabel: '{value}%',
	City: '도시',
	CivilEngineer: '토목 기술자',
	Claim: '주장하다',
	ClaimAddReferringProvider: '추천 의료기관 추가',
	ClaimAddRenderingProvider: '렌더링 공급자 추가',
	ClaimAmount: '청구 금액',
	ClaimAmountPaidHelpContent:
		'지불 금액은 환자 또는 다른 지불자로부터 받은 지불입니다. 환자 및/또는 다른 지불자가 해당 서비스에 대해서만 지불한 총 금액을 입력하세요.',
	ClaimAmountPaidHelpSubtitle: '필드 29',
	ClaimAmountPaidHelpTitle: '지불금액',
	ClaimBillingProfileTypeIndividual: '개인',
	ClaimBillingProfileTypeOrganisation: '조직',
	ClaimChooseRenderingProviderOrTeamMember: '렌더링 제공자 또는 팀원을 선택하세요',
	ClaimClientInsurancePolicies: '고객 보험 정책',
	ClaimCreatedAction: '<mark>청구 {claimNumber}</mark> 생성됨',
	ClaimDeniedAction: '<mark>청구 {claimNumber}</mark>는 <b>{payerNumber} {payerName}</b>에 의해 거부되었습니다.',
	ClaimDiagnosisCodeSelectorPlaceholder: 'ICD 10 진단 코드 검색',
	ClaimDiagnosisSelectorHelpContent: `"진단 또는 부상"은 청구에 포함된 서비스와 관련된 환자의 징후, 증상, 불만 또는 상태입니다.
 최대 12개의 ICD 10 진단 코드를 선택할 수 있습니다.`,
	ClaimDiagnosisSelectorHelpSubtitle: '필드 21',
	ClaimDiagnosisSelectorHelpTitle: '진단 또는 부상',
	ClaimDiagnosticCodesEmptyError: '진단 코드가 하나 이상 필요합니다',
	ClaimDoIncludeReferrerInformation: 'CMS1500에 추천인 정보를 포함하세요',
	ClaimERAReceivedAction: '전자 송금 수령: <b>{payerNumber} {payerName}</b>으로부터',
	ClaimElectronicPaymentAction:
		'전자 송금 수령	<mark>결제 {paymentReference}</mark> <b>{paymentAmount}</b>에 대한 <b>{payerNumber} {payerName}</b>님의 결제가 기록되었습니다',
	ClaimExportedAction: '<mark>청구 {claimNumber}</mark>는 <b>{attachmentType}</b>로 내보내졌습니다.',
	ClaimFieldClient: '고객 또는 연락처 이름',
	ClaimFieldClientAddress: '고객 주소',
	ClaimFieldClientAddressDescription:
		'고객의 주소를 입력하세요. 첫 번째 줄은 거리 주소입니다. 주소에 구두점(쉼표나 마침표)이나 기호를 사용하지 마세요. 외국 주소를 보고하는 경우, 구체적인 보고 지침은 지불자에게 문의하세요.',
	ClaimFieldClientAddressSubtitle: '필드 5',
	ClaimFieldClientDateOfBirth: '고객의 생년월일',
	ClaimFieldClientDateOfBirthAndSexSubtitle: '필드 3',
	ClaimFieldClientDateOfBirthDescription:
		'고객의 8자리 생년월일(MM/DD/YYYY)을 입력합니다. 고객의 생년월일은 고객을 식별하는 정보이며 유사한 이름을 가진 사람을 구별합니다.',
	ClaimFieldClientDescription: `'고객 이름'은 치료나 의료용품을 받은 사람의 이름입니다.`,
	ClaimFieldClientSexDescription: `'성별'은 고객을 식별하는 정보이며 이름이 비슷한 사람을 구별합니다.`,
	ClaimFieldClientSubtitle: '필드 2',
	ClaimFiling: '청구 제출',
	ClaimHistorySubtitle: '보험 • 청구 {number}',
	ClaimIncidentAutoAccident: '자동차 사고?',
	ClaimIncidentConditionRelatedTo: '고객의 상태는 다음과 관련이 있습니까?',
	ClaimIncidentConditionRelatedToHelpContent:
		'이 정보는 고객의 질병이나 부상이 고용, 자동차 사고 또는 기타 사고와 관련이 있는지 여부를 나타냅니다. 고용(현재 또는 이전)은 해당 상태가 고객의 직업 또는 직장과 관련이 있음을 나타냅니다. 자동차 사고는 해당 상태가 자동차 사고의 결과임을 나타냅니다. 기타 사고는 해당 상태가 다른 유형의 사고의 결과임을 나타냅니다.',
	ClaimIncidentConditionRelatedToHelpSubtitle: '필드 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: '고객의 상태는 다음과 관련이 있습니까?',
	ClaimIncidentCurrentIllness: '현재 질병, 부상 또는 임신',
	ClaimIncidentCurrentIllnessHelpContent:
		'현재 질병, 부상 또는 임신 날짜는 질병이 처음 시작된 날짜, 실제 부상 날짜 또는 임신의 경우 LMP를 나타냅니다.',
	ClaimIncidentCurrentIllnessHelpSubtitle: '필드 14',
	ClaimIncidentCurrentIllnessHelpTitle: '현재 질병, 부상 또는 임신 날짜(LMP)',
	ClaimIncidentDate: '날짜',
	ClaimIncidentDateFrom: '날짜부터',
	ClaimIncidentDateTo: '날짜까지',
	ClaimIncidentEmploymentRelated: '고용',
	ClaimIncidentEmploymentRelatedDesc: '(현재 또는 이전)',
	ClaimIncidentHospitalizationDatesLabel: '현재 서비스와 관련된 입원 날짜',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'현재 서비스와 관련된 입원 날짜는 고객의 입원 날짜를 말하며, 청구서에 해당 서비스와 관련된 입원 및 퇴원 날짜를 표시합니다.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: '필드 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: '현재 서비스와 관련된 입원 날짜',
	ClaimIncidentInformation: '사고 정보',
	ClaimIncidentOtherAccident: '다른 사고?',
	ClaimIncidentOtherAssociatedDate: '기타 관련 날짜',
	ClaimIncidentOtherAssociatedDateHelpContent: '다른 날짜는 고객의 상태나 치료에 대한 추가 날짜 정보를 나타냅니다.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: '필드 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: '다른 날짜',
	ClaimIncidentQualifier: '예선전',
	ClaimIncidentQualifierPlaceholder: '자격 조건을 선택하세요',
	ClaimIncidentUnableToWorkDatesLabel: '고객은 현재 직업에서 일할 수 없었습니다.',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'날짜 클라이언트가 현재 직업에서 일할 수 없었던 기간은 클라이언트가 일할 수 없거나 일할 수 없었던 기간입니다.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: '필드 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: '날짜 클라이언트는 현재 직업에서 일할 수 없었습니다.',
	ClaimIncludeReferrerInformation: 'CMS1500에 대한 추천인 정보 포함',
	ClaimInsuranceCoverageTypeHelpContent: `이 청구에 적용되는 건강 보험 적용 유형입니다. 기타는 HMO, 상업 보험, 자동차 사고, 책임 또는 근로자 보상을 포함한 건강 보험을 나타냅니다.
 이 정보는 청구를 해당 프로그램에 대해 진행하는 데 도움이 되며 주요 책임을 확립하는 데 도움이 될 수 있습니다.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: '필드 1',
	ClaimInsuranceCoverageTypeHelpTitle: '보장 유형',
	ClaimInsuranceGroupIdHelpContent: `보험에 가입한 사람의 건강 관리 신분증에 표시된 대로 보험에 가입한 사람의 보험 번호나 그룹 번호를 입력하세요.

 "보험가입자의 정책, 그룹 또는 FECA 번호"는 건강, 자동차 또는 기타 보험 플랜 적용 범위에 대한 영숫자 식별자입니다. FECA 번호는 업무 관련 상태를 청구하는 환자에게 할당된 9자리 영숫자 식별자입니다.`,
	ClaimInsuranceGroupIdHelpSubtitle: '필드 11',
	ClaimInsuranceGroupIdHelpTitle: '보험가입자의 정책, 그룹 또는 FECA 번호',
	ClaimInsuranceMemberIdHelpContent: `청구를 제출하는 지불자의 보험자 신분증에 표시된 보험자 신분증 번호를 입력하세요.
 환자가 지불자로부터 고유 회원 식별 번호를 할당받은 경우, 이 필드에 해당 번호를 입력하세요.`,
	ClaimInsuranceMemberIdHelpSubtitle: '필드 1a',
	ClaimInsuranceMemberIdHelpTitle: '피보험자 회원ID',
	ClaimInsurancePayer: '보험금 지불자',
	ClaimManualPaymentAction: '<mark>결제 {paymentReference}</mark> for <b>{paymentAmount}</b> 기록됨',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: '추가 청구 정보',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'이 필드 사용에 대한 공공 또는 민간 지불자의 현재 지침을 참조하십시오. 입력되는 정보에 대해 적절한 한정어가 있으면 보고하십시오.한정어와 정보 사이에 공백, 하이픈 또는 기타 구분 기호를 입력하지 마십시오.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: '필드 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: '추가 청구 정보',
	ClaimMiscClaimCodes: '클레임 코드',
	ClaimMiscOriginalReferenceNumber: '원래 참조 번호',
	ClaimMiscPatientsAccountNumber: '환자의 계좌번호',
	ClaimMiscPatientsAccountNumberHelpContent: '환자의 계좌번호는 의료 서비스 제공자가 지정한 식별자입니다.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: '필드 26',
	ClaimMiscPatientsAccountNumberHelpTitle: '환자의 계좌번호',
	ClaimMiscPriorAuthorizationNumber: '사전 승인 번호',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'사전 승인 번호는 서비스를 승인하기 위해 지불자에게 할당된 번호입니다.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: '필드 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: '사전 승인 번호',
	ClaimMiscResubmissionCode: '재제출 코드',
	ClaimMiscResubmissionCodeHelpContent:
		'재제출이란 목적지 지불인 또는 수취인이 이전에 제출한 청구나 접촉을 나타내기 위해 지정한 코드와 원래 참조 번호를 의미합니다.',
	ClaimMiscResubmissionCodeHelpSubtitle: '필드 22',
	ClaimMiscResubmissionCodeHelpTitle: '재제출 및/또는 원래 참조 번호',
	ClaimNumber: '청구 번호',
	ClaimNumberFormat: '클레임 #{number}',
	ClaimOrderingProvider: '주문 제공자',
	ClaimOtherId: '기타 ID',
	ClaimOtherIdPlaceholder: '옵션을 선택하세요',
	ClaimOtherIdQualifier: '기타 ID 한정자',
	ClaimOtherIdQualifierPlaceholder: 'ID 자격 조건을 선택하세요',
	ClaimPlaceOfService: '서비스 장소',
	ClaimPlaceOfServicePlaceholder: 'POS 추가',
	ClaimPolicyHolderRelationship: '보험계약자 관계',
	ClaimPolicyInformation: '정책 정보',
	ClaimPolicyTelephone: '전화번호 (지역번호 포함)',
	ClaimReceivedAction: '<mark>청구 {claimNumber}</mark> 을 <b>{name}</b>이(가) 받았습니다.',
	ClaimReferringProvider: '추천 제공자',
	ClaimReferringProviderEmpty: '추천 의료기관이 없습니다.',
	ClaimReferringProviderHelpContent:
		'입력된 이름은 청구에 대한 서비스 또는 공급품을 추천, 주문 또는 감독한 추천 제공자, 주문 제공자 또는 감독 제공자입니다. 한정자는 보고되는 제공자의 역할을 나타냅니다.',
	ClaimReferringProviderHelpSubtitle: '필드 17',
	ClaimReferringProviderHelpTitle: '추천 제공자 또는 소스의 이름',
	ClaimReferringProviderQualifier: '예선전',
	ClaimReferringProviderQualifierPlaceholder: '자격 조건을 선택하세요',
	ClaimRejectedAction: '<mark>청구 {claimNumber}</mark>은 <b>{name}</b>에 의해 거부되었습니다.',
	ClaimRenderingProviderIdNumber: '신분증 번호',
	ClaimRenderingProviderOrTeamMember: '렌더링 제공자 또는 팀원',
	ClaimRestoredAction: '<mark>청구 {claimNumber}</mark>가 복원되었습니다',
	ClaimServiceFacility: '서비스 시설',
	ClaimServiceFacilityLocationHelpContent:
		'서비스가 제공된 시설의 이름과 주소는 서비스가 제공된 사이트를 식별합니다.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a 및 32b',
	ClaimServiceFacilityLocationHelpSubtitle: '필드 32, 32a 및 32b',
	ClaimServiceFacilityLocationHelpTitle: '서비스 시설',
	ClaimServiceFacilityPlaceholder: '서비스 시설 또는 위치를 선택하세요',
	ClaimServiceLabChargesHelpContent: `청구 제공자가 아닌 다른 기관에서 구매한 서비스에 대한 비용을 청구하는 경우 이 필드를 작성하세요.
 CMS1500 양식에는 하나의 요금만 입력할 수 있으므로 구매한 각 서비스는 별도의 청구서로 보고해야 합니다.`,
	ClaimServiceLabChargesHelpSubtitle: '필드 20',
	ClaimServiceLabChargesHelpTitle: '외부 실험실 비용',
	ClaimServiceLineServiceHelpContent:
		'"시술, 서비스 또는 의료용품"은 환자에게 제공되는 의료 서비스와 시술을 식별합니다.',
	ClaimServiceLineServiceHelpSubtitle: '필드 24d',
	ClaimServiceLineServiceHelpTitle: '절차, 서비스 또는 공급품',
	ClaimServiceLinesEmptyError: '서비스 라인이 하나 이상 필요합니다.',
	ClaimServiceSupplementaryInfoHelpContent: `해당 한정사를 사용해 제공되는 서비스에 대한 추가적인 서술적 설명을 추가합니다.
 한정사와 정보 사이에 공백, 하이픈 또는 기타 구분 기호를 넣지 마십시오.

 보충 정보를 추가하는 방법에 대한 자세한 지침은 CMS 1500 청구 양식 지침을 검토하세요.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: '필드 24',
	ClaimServiceSupplementaryInfoHelpTitle: '추가 정보',
	ClaimSettingsBillingMethodTitle: '클라이언트 청구 방법',
	ClaimSettingsClientSignatureDescription:
		'보험 청구를 처리하는 데 필요한 의료 정보 또는 기타 정보를 공개하는 데 동의합니다.',
	ClaimSettingsClientSignatureTitle: '파일에 있는 클라이언트 서명',
	ClaimSettingsConsentLabel: '보험 청구 처리에 필요한 동의:',
	ClaimSettingsDescription: '원활한 결제 처리를 위해 클라이언트 청구 방법을 선택하세요.',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name}는 활성화된 보험 정책이 있습니다. 보험 청구를 활성화하려면 클라이언트 청구 방식을 보험으로 업데이트하세요.',
	ClaimSettingsInsuranceDescription: '보험에서 보상하는 비용',
	ClaimSettingsInsuranceTitle: '보험',
	ClaimSettingsNoPoliciesAlertDescription: '보험 청구를 가능하게 하려면 보험 정책을 추가하세요.',
	ClaimSettingsPolicyHolderSignatureDescription: '제공된 서비스에 대한 보험금을 받는 데 동의합니다.',
	ClaimSettingsPolicyHolderSignatureTitle: '파일에 있는 정책 보유자 서명',
	ClaimSettingsSelfPayDescription: '고객이 약속에 대한 비용을 지불합니다.',
	ClaimSettingsSelfPayTitle: '자기부담',
	ClaimSettingsTitle: '클레임 설정',
	ClaimSexSelectorPlaceholder: '남자 / 여자',
	ClaimStatusChangedAction: '<mark>청구 {claimNumber}</mark> 상태 업데이트됨',
	ClaimSubmittedAction:
		'<mark>청구 {claimNumber}</mark> 제출됨 <b>{payerClearingHouse}</b> 에게 <b>{payerNumber} {payerName}</b>을 위해',
	ClaimSubtitle: '청구 #{claimNumber}',
	ClaimSupervisingProvider: '감독 제공자',
	ClaimSupplementaryInfo: '추가 정보',
	ClaimSupplementaryInfoPlaceholder: '보충 정보 추가',
	ClaimTrashedAction: '<mark>청구 {claimNumber}</mark>가 삭제되었습니다',
	ClaimValidationFailure: '클레임 유효성 검사 실패',
	ClaimsEmptyStateDescription: '청구사항이 발견되지 않았습니다.',
	ClainInsuranceTelephone: '보험 전화번호 (지역번호 포함)',
	Classic: '권위 있는',
	Clear: '분명한',
	ClearAll: '모두 지우기',
	ClearSearchFilter: '분명한',
	ClearingHouse: '하우스 정리',
	ClearingHouseClaimId: 'Claim.MD ID',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: '주택 정리 참조',
	ClearingHouseUnavailableError: '클리어링 하우스는 현재 이용할 수 없습니다. 나중에 다시 시도해 주세요.',
	ClickToUpload: '업로드하려면 클릭하세요',
	Client: '고객',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName}는 {noteTitle, select, undefined { 메모 } other {{noteTitle}}}를 추가했습니다.',
	ClientAndRelationshipSelectorPlaceholder: '고객과 고객 관계를 선택하세요',
	ClientAndRelationshipSelectorTitle: '모든 고객과 그들의 관계',
	ClientAndRelationshipSelectorTitle1: '‘{name}’의 모든 관계',
	ClientAppCallsPageNoOptionsText:
		'화상 통화를 기다리고 있다면 곧 여기에 나타납니다. 문제가 있으면 통화를 시작한 사람에게 문의하세요.',
	ClientAppSubHeaderMyDocumentation: '내 문서',
	ClientAppointment: '고객 약속',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName}님이 {appointmentName}을 예약했습니다.',
	ClientAppointmentsEmptyStateDescription: '약속이 발견되지 않았습니다',
	ClientAppointmentsEmptyStateTitle: '고객의 예정된 약속과 이전 약속 및 참석을 추적하세요.',
	ClientArchivedSuccessfulSnackbar: '성공적으로 보관되었습니다 <b>{name}</b>',
	ClientBalance: '클라이언트 잔액',
	ClientBilling: '청구',
	ClientBillingAddPaymentMethodDescription:
		'고객의 결제 방법을 추가하고 관리하여 송장 및 청구 프로세스를 간소화하세요.',
	ClientBillingAndPaymentDueDate: '마감일',
	ClientBillingAndPaymentHistory: '청구 및 지불 내역',
	ClientBillingAndPaymentInvoices: '송장',
	ClientBillingAndPaymentIssueDate: '발행일',
	ClientBillingAndPaymentPrice: '가격',
	ClientBillingAndPaymentReceipt: '영수증',
	ClientBillingAndPaymentServices: '서비스',
	ClientBillingAndPaymentStatus: '상태',
	ClientBulkStaffAssignedSuccessSnackbar: '팀 {count, plural, one {멤버} other {멤버}} 배정되었습니다!',
	ClientBulkStaffUnassignedSuccessSnackbar: '팀 {count, plural, one {멤버} other {멤버}} 할당되지 않았습니다!',
	ClientBulkTagsAddedSuccessSnackbar: '태그가 추가되었습니다!',
	ClientDuplicatesDeviewDescription:
		'여러 고객 기록을 하나로 병합하여 메모, 문서, 약속, 송장, 대화 등 모든 데이터를 통합합니다.',
	ClientDuplicatesPageMergeHeader: '보관하고 싶은 데이터를 선택하세요',
	ClientDuplicatesReviewHeader: '병합을 위해 잠재적 중복 레코드를 비교합니다.',
	ClientEmailChangeWarningDescription:
		'클라이언트의 이메일을 업데이트하면 공유 문서에 대한 액세스 권한이 제거되고 새 이메일을 사용하는 사용자에게 액세스 권한이 부여됩니다.',
	ClientFieldDateDescription: '날짜 형식 지정',
	ClientFieldDateLabel: '날짜',
	ClientFieldDateRangeDescription: '다양한 날짜',
	ClientFieldDateRangeLabel: '날짜 범위',
	ClientFieldDateShowDateDescription: '예를 들어 29년',
	ClientFieldDateShowDateRangeDescription: '예를 들어 2주',
	ClientFieldEmailDescription: '이메일 주소',
	ClientFieldEmailLabel: '이메일',
	ClientFieldLabel: '필드 레이블',
	ClientFieldLinearScaleDescription: '스케일 옵션 1-10',
	ClientFieldLinearScaleLabel: '선형 스케일',
	ClientFieldLocationDescription: '실제 주소 또는 우편 주소',
	ClientFieldLocationLabel: '위치',
	ClientFieldLongTextDescription: '긴 텍스트 영역',
	ClientFieldLongTextLabel: '절',
	ClientFieldMultipleChoiceDropdownDescription: '목록에서 여러 옵션을 선택하세요',
	ClientFieldMultipleChoiceDropdownLabel: '다중 선택 드롭다운',
	ClientFieldPhoneNumberDescription: '전화 번호',
	ClientFieldPhoneNumberLabel: '핸드폰',
	ClientFieldPlaceholder: '클라이언트 필드 유형을 선택하세요',
	ClientFieldSingleChoiceDropdownDescription: '목록에서 하나의 옵션만 선택하세요',
	ClientFieldSingleChoiceDropdownLabel: '단일 선택 드롭다운',
	ClientFieldTextDescription: '텍스트 입력 필드',
	ClientFieldTextLabel: '텍스트',
	ClientFieldYesOrNoDescription: '예 또는 아니오 옵션 중에서 선택하세요',
	ClientFieldYesOrNoLabel: '예 | 아니오',
	ClientFileFormAccessLevelDescription:
		'귀하와 팀은 항상 귀하가 업로드한 파일에 액세스할 수 있습니다. 이 파일을 클라이언트 및/또는 그들의 관계와 공유하도록 선택할 수 있습니다.',
	ClientFileSavedSuccessSnackbar: '파일이 저장되었습니다!',
	ClientFilesPageEmptyStateText: '업로드된 파일이 없습니다',
	ClientFilesPageUploadFileButton: '파일 업로드',
	ClientHeaderBilling: '청구',
	ClientHeaderBillingAndReceipts: '청구 ',
	ClientHeaderDocumentation: '선적 서류 비치',
	ClientHeaderDocuments: '서류',
	ClientHeaderFile: '문서',
	ClientHeaderHistory: '병력',
	ClientHeaderInbox: '받은 편지함',
	ClientHeaderNote: '메모',
	ClientHeaderOverview: '개요',
	ClientHeaderProfile: '개인의',
	ClientHeaderRelationship: '관계',
	ClientHeaderRelationships: '관계',
	ClientId: '클라이언트 ID',
	ClientImportProcessingDescription: '파일이 아직 처리 중입니다. 완료되면 알려드리겠습니다.',
	ClientImportReadyForMappingDescription:
		'파일 전처리가 완료되었습니다. 이 가져오기를 완료하기 위해 열을 매핑하시겠습니까?',
	ClientImportReadyForMappingNotificationSubject:
		'Client import pre-processing is complete. 파일 매핑 준비가 완료되었습니다.',
	ClientInAppMessaging: '클라이언트 인앱 메시징',
	ClientInfoAddField: '다른 필드 추가',
	ClientInfoAddRow: '행 추가',
	ClientInfoAlertMessage: '이 섹션에 입력한 모든 정보는 클라이언트 레코드에 저장됩니다.',
	ClientInfoFormPrimaryText: '고객 정보',
	ClientInfoFormSecondaryText: '연락처 정보를 수집하세요',
	ClientInfoPlaceholder: `고객 이름, 이메일 주소, 전화번호
 실제 주소,
 생일`,
	ClientInformation: '고객 정보',
	ClientInsuranceTabLabel: '보험',
	ClientIntakeFormsNotSupported: `현재 클라이언트 접수를 통해 양식 템플릿이 지원되지 않습니다.
 대신 클라이언트 메모로 작성하여 공유하세요.`,
	ClientIntakeModalDescription:
		'고객에게 프로필을 작성하고 관련 의료 또는 추천 문서를 업로드하도록 요청하는 접수 이메일이 전송됩니다. 클라이언트 포털 액세스 권한이 부여됩니다.',
	ClientIntakeModalTitle: '{name}에게 섭취량을 보내세요.',
	ClientIntakeSkipPasswordSuccessSnackbar: '성공! 섭취가 저장되었습니다.',
	ClientIntakeSuccessSnackbar: '성공! 귀하의 섭취가 저장되었고 확인 이메일이 전송되었습니다.',
	ClientIsChargedProcessingFee: '귀하의 고객이 처리 수수료를 지불합니다.',
	ClientListCreateButton: '새로운 고객',
	ClientListEmptyState: '클라이언트가 추가되지 않았습니다',
	ClientListPageItemArchive: '클라이언트 제거',
	ClientListPageItemRemoveAccess: '내 액세스 제거',
	ClientLocalizationPanelDescription: '고객이 선호하는 언어와 시간대.',
	ClientLocalizationPanelTitle: '언어 및 시간대',
	ClientManagementAndEHR: '고객 관리 ',
	ClientMergeResultSummaryBanner:
		'레코드 병합은 노트, 문서, 약속, 청구서 및 대화를 포함한 모든 클라이언트 데이터를 통합합니다. 계속하기 전에 정확성을 확인하십시오.',
	ClientMergeResultSummaryTitle: '병합 결과 요약',
	ClientModalTitle: '새로운 고객',
	ClientMustHaveEmaillAccessErrorText: '이메일이 없는 고객/연락처',
	ClientMustHavePortalAccessErrorText: '고객/연락처는 가입이 필요합니다.',
	ClientMustHaveZoomAppConnectedErrorText: '설정 > 연결된 앱을 통해 Zoom 연결',
	ClientNameFormat: '클라이언트 이름 형식',
	ClientNotFormAccessLevel: '다음에서 볼 수 있습니다:',
	ClientNotFormAccessLevelDescription:
		'귀하와 팀은 귀하가 게시한 노트에 항상 액세스할 수 있습니다. 이 노트를 클라이언트 및/또는 그들의 관계와 공유하도록 선택할 수 있습니다.',
	ClientNotRegistered: '등록되지 않음',
	ClientNoteFormAddFileButton: '파일 첨부',
	ClientNoteFormChooseAClient: '계속하려면 클라이언트/연락처를 선택하세요',
	ClientNoteFormContent: '콘텐츠',
	ClientNoteItemDeleteConfirmationModalDescription: '삭제한 메모는 다시 검색할 수 없습니다.',
	ClientNotePublishedAndLockSuccessSnackbar: '메모가 게시되고 잠겼습니다.',
	ClientNotePublishedSuccessSnackbar: '공지가 게시되었습니다!',
	ClientNotes: '고객 노트',
	ClientNotesEmptyStateText: '메모를 추가하려면 고객 프로필로 가서 메모 탭을 클릭하세요.',
	ClientOnboardingChoosePasswordTitle1: '거의 완료!',
	ClientOnboardingChoosePasswordTitle2: '비밀번호를 선택하세요',
	ClientOnboardingCompleteIntake: '완전한 섭취',
	ClientOnboardingConfirmationScreenText:
		'{providerName}에서 요구하는 모든 정보를 제공했습니다.	온보딩을 시작하려면 이메일 주소를 확인하세요. 즉시 이메일을 받지 못하면 스팸 폴더를 확인하세요.',
	ClientOnboardingConfirmationScreenTitle: '좋아요! 받은 편지함을 확인하세요.',
	ClientOnboardingDashboardButton: '대시보드로 이동',
	ClientOnboardingHealthRecordsDesc1: '{providerName}에게 추천서나 서류를 공유하시겠습니까?',
	ClientOnboardingHealthRecordsDescription: '설명 추가(선택 사항)',
	ClientOnboardingHealthRecordsTitle: '선적 서류 비치',
	ClientOnboardingPasswordRequirements: '요구 사항',
	ClientOnboardingPasswordRequirementsConditions1: '최소 9자 이상 입력해주세요',
	ClientOnboardingProviderIntroSignupButton: '나 자신을 위해 가입하기',
	ClientOnboardingProviderIntroSignupFamilyButton: '가족 구성원으로 등록하세요',
	ClientOnboardingProviderIntroTitle: '{name} 님이 Carepatron 플랫폼에 참여하도록 초대했습니다.',
	ClientOnboardingRegistrationInstructions: '아래에 개인 정보를 입력하세요.',
	ClientOnboardingRegistrationTitle: '먼저 개인 정보가 필요합니다.',
	ClientOnboardingStepFormsAndAgreements: '양식 및 계약',
	ClientOnboardingStepFormsAndAgreementsDesc1: '{providerName} 입학 절차를 위해 다음 양식을 작성해 주십시오.',
	ClientOnboardingStepHealthDetails: '건강 정보',
	ClientOnboardingStepPassword: '비밀번호',
	ClientOnboardingStepYourDetails: '귀하의 세부 정보',
	ClientPaymentMethodDescription:
		'다음 약속 예약 및 청구서 발행을 보다 빠르고 안전하게 하기 위해 프로필에 결제 방법을 저장하세요.',
	ClientPortal: '클라이언트 포털',
	ClientPortalDashboardEmptyDescription: '귀하의 약속 내역과 출석 내역이 여기에 표시됩니다.',
	ClientPortalDashboardEmptyTitle: '귀하의 참석과 함께 예정된 모든 약속, 요청된 약속 및 과거 약속을 추적하세요.',
	ClientPreferredNotificationPanelDescription:
		'다음을 통해 고객이 업데이트 및 알림을 받는 데 선호하는 방법을 관리하세요.',
	ClientPreferredNotificationPanelTitle: '선호하는 알림 방법',
	ClientProcessingFee: '결제에는 ({currencyCode}) {amount} 처리 수수료가 포함됩니다.',
	ClientProfileAddress: '주소',
	ClientProfileDOB: '생일',
	ClientProfileEmailHelperText: '이메일을 추가하면 포털 액세스 권한이 부여됩니다.',
	ClientProfileEmailHelperTextMoreInfo:
		'클라이언트에게 포털 액세스 권한을 부여하면 팀 구성원이 메모, 파일 및 기타 문서를 공유할 수 있습니다.',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: '식별번호',
	ClientRelationshipsAddClientOwnerButton: '고객을 초대합니다',
	ClientRelationshipsAddFamilyButton: '가족을 초대하세요',
	ClientRelationshipsAddStaffButton: '직원 액세스 추가',
	ClientRelationshipsEmptyStateText: '관계가 추가되지 않았습니다',
	ClientRemovedSuccessSnackbar: '클라이언트가 성공적으로 제거되었습니다.',
	ClientResponsibility: '고객 책임',
	ClientSavedSuccessSnackbar: '클라이언트가 성공적으로 저장되었습니다.',
	ClientTableClientName: '고객 이름',
	ClientTablePhone: '핸드폰',
	ClientTableStatus: '상태',
	ClientUnarchivedSuccessfulSnackbar: '성공적으로 <b>{name}</b>을 복구했습니다.',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName}는 {noteTitle, select, undefined { 메모 } other {{noteTitle}}}를 수정했습니다.',
	ClientView: '클라이언트 뷰',
	Clients: '고객',
	ClientsTable: '클라이언트 테이블',
	ClinicalFormat: '임상 형식',
	ClinicalPsychologist: '임상 심리학자',
	Close: '닫다',
	CloseImportClientsModal: '클라이언트 가져오기를 취소하시겠습니까?',
	CloseReactions: '가까운 반응',
	Closed: '닫은',
	Coaching: '코칭',
	Code: '암호',
	CodeErrorMessage: '코드가 필요합니다',
	CodePlaceholder: '암호',
	Coinsurance: '공동보험',
	Collection: '수집',
	CollectionName: '컬렉션 이름',
	Collections: '컬렉션',
	ColorAppointmentsBy: '색상 예약',
	ColorTheme: '색상 테마',
	ColourCalendarBy: '색상별 달력',
	ComingSoon: '곧 출시 예정',
	Community: '지역 사회',
	CommunityHealthLead: '커뮤니티 건강 책임자',
	CommunityHealthWorker: '지역사회 보건 종사자',
	CommunityTemplatesSectionDescription: 'Carepatron 커뮤니티에서 제작했습니다.',
	CommunityTemplatesSectionTitle: '지역 사회',
	CommunityUser: '커뮤니티 사용자',
	Complete: '완벽한',
	CompleteAndLock: '완료하고 잠금',
	CompleteSetup: '완전한 설정',
	CompleteSetupSuccessDescription: 'Carepatron 마스터링을 위한 핵심 단계를 완료했습니다.',
	CompleteSetupSuccessDescription2: '귀하의 진료를 간소화하고 클라이언트를 지원할 수 있는 더 많은 방법을 찾아보세요.',
	CompleteSetupSuccessTitle: '성공했어요! 정말 대단해요!',
	CompleteStripeSetup: 'Stripe 설정 완료',
	Completed: '완료',
	ComposeSms: 'SMS 작성',
	ComputerSystemsAnalyst: '컴퓨터 시스템 분석가',
	Confirm: '확인하다',
	ConfirmDeleteAccountDescription:
		'계정을 삭제하려고 합니다. 이 작업은 취소할 수 없습니다. 계속하려면 아래에서 확인하세요.',
	ConfirmDeleteActionDescription: '이 작업을 삭제하시겠습니까? 취소할 수 없습니다.',
	ConfirmDeleteAutomationDescription: '이 자동화를 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.',
	ConfirmDeleteScheduleDescription:
		'<strong>{scheduleName}</strong> 일정을 삭제하면 일정에서 삭제되고 온라인 서비스 이용 가능 여부가 변경될 수 있습니다. 이 작업은 취소할 수 없습니다.',
	ConfirmDraftResponseContinue: '응답을 계속하세요',
	ConfirmDraftResponseDescription:
		'이 페이지를 닫으면 응답은 초안으로 유지됩니다. 언제든지 돌아와서 계속할 수 있습니다.',
	ConfirmDraftResponseSubmitResponse: '응답 제출',
	ConfirmDraftResponseTitle: '귀하의 응답이 제출되지 않았습니다.',
	ConfirmIfUserIsClientDescription: `귀하께서 작성하신 가입 양식은 공급자(예: 건강팀/조직)를 위한 것입니다.
 이것이 실수라면 "클라이언트로 계속"을 선택하면 클라이언트 포털을 설정해 드립니다.`,
	ConfirmIfUserIsClientNoButton: '공급자로 등록',
	ConfirmIfUserIsClientTitle: '당신은 고객인 것 같습니다.',
	ConfirmIfUserIsClientYesButton: '클라이언트로 계속',
	ConfirmKeepSeparate: '확인 분리해두세요',
	ConfirmMerge: '병합 확인',
	ConfirmPassword: '비밀번호 확인',
	ConfirmRevertClaim: '네, 상태를 되돌립니다.',
	ConfirmSignupAccessCode: '확인코드',
	ConfirmSignupButtom: '확인하다',
	ConfirmSignupDescription: '귀하의 이메일 주소와 방금 보낸 확인 코드를 입력해 주세요.',
	ConfirmSignupSubTitle: '스팸 폴더를 확인하세요 - 이메일이 도착하지 않은 경우',
	ConfirmSignupSuccessSnackbar:
		'좋습니다. 계정이 확인되었습니다! 이제 이메일과 비밀번호를 사용하여 로그인할 수 있습니다.',
	ConfirmSignupTitle: '계정 확인',
	ConfirmSignupUsername: '이메일',
	ConfirmSubscriptionUpdate: '구독 확인 {price} {isMonthly, select, true {월별} other {연간}}',
	ConfirmationModalBulkDeleteClientsDescriptionId: '클라이언트가 삭제되면 더 이상 해당 정보에 접근할 수 없습니다.',
	ConfirmationModalBulkDeleteClientsTitleId: '{count, plural, one {# 고객} other {# 고객}}을 삭제하시겠습니까?',
	ConfirmationModalBulkDeleteContactsDescriptionId: '연락처가 삭제되면 더 이상 해당 정보에 접근할 수 없습니다.',
	ConfirmationModalBulkDeleteContactsTitleId:
		'{count, plural, one {# 연락처} other {# 연락처들}}을 삭제하시겠습니까?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'이것은 영구적인 작업입니다. 팀원이 삭제되면 더 이상 해당 정보에 액세스할 수 없습니다.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'{count, plural, one {# 팀 구성원} other {# 팀 구성원들}}을 삭제하시겠습니까?',
	ConfirmationModalCloseOnGoingTranscription:
		'이 메모를 닫으면 진행 중인 모든 필사 작업이 종료됩니다. 계속하시겠습니까?',
	ConfirmationModalDeleteClientField:
		'이것은 영구적인 작업입니다. 필드가 삭제되면 나머지 클라이언트에서 더 이상 액세스할 수 없습니다.',
	ConfirmationModalDeleteSectionMessage: '삭제되면 이 섹션의 모든 질문이 제거됩니다. 이 작업은 취소할 수 없습니다.',
	ConfirmationModalDeleteService:
		'이는 영구적인 작업입니다. 서비스가 삭제되면 더 이상 작업 공간에서 액세스할 수 없습니다.',
	ConfirmationModalDeleteServiceGroup:
		'컬렉션을 삭제하면 그룹에서 모든 서비스가 제거되고 서비스 목록으로 돌아갑니다. 이 작업은 취소할 수 없습니다.',
	ConfirmationModalDeleteTranscript: '정말로 대본을 삭제하시겠습니까?',
	ConfirmationModalDescriptionDeleteClient: '클라이언트가 삭제되면 더 이상 클라이언트 정보에 접근할 수 없습니다.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'액세스 권한을 제거하면 더 이상 클라이언트 정보를 볼 수 없습니다.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'해당 프로필 자체는 삭제되지 않으며, 해당 고객과의 관계만 삭제됩니다.',
	ConfirmationModalDescriptionRemoveStaff: '이 사람을 공급자에서 제거하시겠습니까?',
	ConfirmationModalEndSession: '세션을 종료하시겠습니까?',
	ConfirmationModalTitle: '정말이에요?',
	Confirmed: '확인됨',
	ConflictTimezoneWarningMessage: '여러 시간대로 인해 충돌이 발생할 수 있습니다.',
	Connect: '연결하다',
	ConnectExistingClientOrContact: '새로운 고객/연락처 생성',
	ConnectInboxGoogleDescription: 'Gmail 계정 또는 Google 그룹 목록 추가',
	ConnectInboxMicrosoftDescription: 'Outlook, Office365 또는 Exchange 계정 추가',
	ConnectInboxModalDescription: '모든 커뮤니케이션을 한곳에서 원활하게 보내고 받고 추적할 수 있도록 앱을 연결하세요.',
	ConnectInboxModalExistingDescription: '연결된 앱 설정에서 기존 연결을 사용하여 구성 프로세스를 간소화합니다.',
	ConnectInboxModalExistingTitle: 'Carepatron에 연결된 기존 앱',
	ConnectInboxModalTitle: '받은 편지함에 연결',
	ConnectToStripe: 'Stripe에 연결',
	ConnectZoom: '줌 연결',
	ConnectZoomModalDescription: 'Carepatron이 귀하의 약속에 대한 화상 통화를 관리하도록 허용하세요.',
	ConnectedAppDisconnectedNotificationSubject: '{account} 계정과의 연결이 끊어졌습니다. 다시 연결해 주세요.',
	ConnectedAppSyncDescription: 'Carepatron에서 직접 타사 캘린더에 이벤트를 생성하여 연결된 앱을 관리하세요.',
	ConnectedApps: '연결된 앱',
	ConnectedAppsGMailDescription: 'Gmail 계정 또는 Google 그룹 목록 추가',
	ConnectedAppsGoogleCalendarDescription: '캘린더 계정 또는 Google 그룹 목록 추가',
	ConnectedAppsGoogleDescription: 'Gmail 계정을 추가하고 Google 캘린더를 동기화하세요',
	ConnectedAppsMicrosoftDescription: 'Outlook, Office365 또는 Exchange 계정 추가',
	ConnectedCalendars: '연결된 캘린더',
	ConsentDocumentation: '양식 및 계약',
	ConsentDocumentationPublicTemplateError: '보안상의 이유로 팀(비공개)에서만 템플릿을 선택할 수 있습니다.',
	ConstructionWorker: '건설 노동자',
	Consultant: '컨설턴트',
	Contact: '연락하다',
	ContactAccessTypeHelperText: '가족 관리자가 정보를 업데이트할 수 있도록 허용합니다.',
	ContactAccessTypeHelperTextMoreInfo: '이를 통해 {clientFirstName}에 대한 메모/문서를 공유할 수 있습니다.',
	ContactAddressLabelBilling: '청구',
	ContactAddressLabelHome: '집',
	ContactAddressLabelOthers: '기타',
	ContactAddressLabelWork: '일하다',
	ContactChangeConfirmation: '인보이스 연락처를 변경하면 <mark>{contactName}</mark>과 관련된 모든 품목이 삭제됩니다.',
	ContactDetails: '연락처 정보',
	ContactEmailLabelOthers: '기타',
	ContactEmailLabelPersonal: '개인의',
	ContactEmailLabelSchool: '학교',
	ContactEmailLabelWork: '일하다',
	ContactInformation: '연락처 정보',
	ContactInformationText: '연락처 정보',
	ContactListCreateButton: '새로운 연락처',
	ContactName: '연락처 이름',
	ContactPhoneLabelHome: '집',
	ContactPhoneLabelMobile: '이동하는',
	ContactPhoneLabelSchool: '학교',
	ContactPhoneLabelWork: '일하다',
	ContactRelationship: '접촉 관계',
	ContactRelationshipFormAccessType: '공유 정보에 대한 액세스 권한 부여',
	ContactRelationshipGrantAccessInfo: '이렇게 하면 메모를 공유할 수 있습니다. ',
	ContactSupport: '지원팀에 문의하세요',
	Contacts: '콘택트 렌즈',
	ContainerIdNotSet: '컨테이너 ID가 설정되지 않았습니다',
	Contemporary: '현대의',
	Continue: '계속하다',
	ContinueDictating: '계속해서 받아쓰기',
	ContinueEditing: '편집 계속하기',
	ContinueImport: '계속 가져오기',
	ContinueTranscription: '전사를 계속하세요',
	ContinueWithApple: 'Apple로 계속하기',
	ContinueWithGoogle: 'Google로 계속하기',
	Conversation: '대화',
	Copay: '공제금',
	CopayOrCoinsurance: '공제금 또는 공동보험',
	Copayment: '공제금',
	CopiedToClipboard: '클립보드에 복사됨',
	Copy: '복사',
	CopyAddressSuccessSnackbar: '클립보드에 주소 복사',
	CopyCode: '복사 코드',
	CopyCodeToClipboardSuccess: '클립보드에 코드 복사됨',
	CopyEmailAddressSuccessSnackbar: '클립보드에 이메일 주소를 복사했습니다.',
	CopyLink: '링크 복사',
	CopyLinkForCall: '이 통화를 공유하려면 이 링크를 복사하세요:',
	CopyLinkSuccessSnackbar: '클립보드에 링크 복사',
	CopyMeetingLink: '회의 링크 복사',
	CopyPaymentLink: '결제 링크 복사',
	CopyPhoneNumberSuccessSnackbar: '클립보드에 전화번호가 복사되었습니다.',
	CopyTemplateLink: '템플릿에 링크 복사',
	CopyTemplateLinkSuccess: '클립보드에 링크 복사',
	CopyToClipboardError: '클립보드에 복사할 수 없습니다. 다시 시도해 주세요.',
	CopyToTeamTemplates: '팀 템플릿에 복사',
	CopyToWorkspace: '작업공간에 복사',
	Cosmetologist: '미용사',
	Cost: '비용',
	CostErrorMessage: '비용이 필요합니다',
	Counseling: '상담',
	Counselor: '참사관',
	Counselors: '상담사',
	CountInvoicesAdded: '{count, plural, one {# 청구서가 추가되었습니다} other {# 청구서가 추가되었습니다}}',
	CountNotesAdded: '{count, plural, one {# 메모 추가됨} other {# 메모 추가됨}}',
	CountSelected: '{count} 선택됨',
	CountTimes: '{count} 번',
	Country: '국가',
	Cousin: '사촌',
	CoverageType: '보장 유형',
	Covered: '덮여있다',
	Create: '만들다',
	CreateANewClient: '새로운 클라이언트를 만드세요',
	CreateAccount: '계정 생성',
	CreateAndSignNotes: '고객과 함께 메모를 작성하고 서명하세요',
	CreateAvailabilityScheduleFailure: '새로운 가용성 일정을 생성하지 못했습니다.',
	CreateAvailabilityScheduleSuccess: '새로운 가용성 일정이 성공적으로 생성되었습니다.',
	CreateBillingItems: '청구 항목 생성',
	CreateCallFormButton: '통화 시작',
	CreateCallFormInviteOnly: '초대만 가능',
	CreateCallFormInviteOnlyMoreInfo:
		'이 통화에 초대된 사람만 참여할 수 있습니다. 이 통화를 다른 사람과 공유하려면 이 체크를 해제하고 다음 페이지에 있는 링크를 복사/붙여넣기만 하면 됩니다.',
	CreateCallFormRecipients: '수신자',
	CreateCallFormRegion: '호스팅 지역',
	CreateCallModalAddClientContactSelectorLabel: '고객 연락처',
	CreateCallModalAddClientContactSelectorPlaceholder: '클라이언트 이름으로 검색',
	CreateCallModalAddStaffSelectorLabel: '팀 구성원 (선택 사항)',
	CreateCallModalAddStaffSelectorPlaceholder: '직원 이름으로 검색',
	CreateCallModalDescription:
		'전화를 시작하고 직원 및/또는 연락처를 초대합니다. 또는 "비공개" 상자의 선택을 취소하여 이 전화를 Carepatron을 사용하는 모든 사람과 공유할 수 있습니다.',
	CreateCallModalTitle: '통화 시작',
	CreateCallModalTitleLabel: '제목 (선택사항)',
	CreateCallNoPersonIdToolTip: '포털 액세스 권한이 있는 연락처/클라이언트만 통화에 참여할 수 있습니다.',
	CreateClaim: '청구서 작성',
	CreateClaimCompletedMessage: '귀하의 청구가 생성되었습니다.',
	CreateClientModalTitle: '새로운 고객',
	CreateContactModalTitle: '새로운 연락처',
	CreateContactRelationshipButton: '관계 추가',
	CreateContactSelectorDefaultOption: '  연락처 만들기',
	CreateContactWithRelationshipFormAccessType: '공유 정보에 대한 액세스 권한 부여 ',
	CreateDocumentDnDPrompt: '드래그 앤 드롭으로 파일 업로드',
	CreateDocumentSizeLimit: '파일당 크기 제한 {size}MB. 총 {total}개 파일.',
	CreateFreeAccount: '무료 계정 생성',
	CreateInvoice: '송장 생성',
	CreateLink: '링크 생성',
	CreateNew: '새로 만들기',
	CreateNewAppointment: '새로운 약속을 만드세요',
	CreateNewClaim: '새로운 클레임 생성',
	CreateNewClaimForAClient: '클라이언트를 위한 새로운 청구서를 만듭니다.',
	CreateNewClient: '새로운 클라이언트 생성',
	CreateNewConnection: '새로운 연결',
	CreateNewContact: '새로운 연락처 만들기',
	CreateNewField: '새로운 필드 만들기',
	CreateNewLocation: '새로운 위치',
	CreateNewService: '새로운 서비스 만들기',
	CreateNewServiceGroupFailure: '새로운 컬렉션을 생성하지 못했습니다.',
	CreateNewServiceGroupMenu: '새로운 컬렉션',
	CreateNewServiceGroupSuccess: '새로운 컬렉션을 성공적으로 생성했습니다.',
	CreateNewServiceMenu: '새로운 서비스',
	CreateNewTeamMember: '새로운 팀원 만들기',
	CreateNewTemplate: '새로운 템플릿',
	CreateNote: '노트 만들기',
	CreateSuperbillReceipt: '새로운 슈퍼빌',
	CreateSuperbillReceiptSuccess: 'Superbill 영수증이 성공적으로 생성되었습니다.',
	CreateTemplateFolderSuccessMessage: '{folderTitle} 생성 완료',
	Created: '생성됨',
	CreatedAt: '생성됨 {timestamp}',
	Credit: '신용 거래',
	CreditAdded: '신용 적용됨',
	CreditAdjustment: '신용 조정',
	CreditAdjustmentReasonHelperText: '이는 내부 메모이므로 고객에게는 표시되지 않습니다.',
	CreditAdjustmentReasonPlaceholder: '청구 가능한 거래를 검토할 때 조정 이유를 추가하면 도움이 될 수 있습니다.',
	CreditAmount: '{amount} 크레',
	CreditBalance: '신용잔액',
	CreditCard: '신용 카드',
	CreditCardExpire: '만료 {exp_month}/{exp_year}',
	CreditCardNumber: '신용카드 번호',
	CreditDebitCard: '카드',
	CreditIssued: '신용 발급',
	CreditsUsed: '사용된 크레딧',
	Crop: '수확고',
	Currency: '통화',
	CurrentCredit: '현재 신용',
	CurrentEventTime: '현재 이벤트 시간',
	CurrentPlan: '현재 계획',
	Custom: '관습',
	CustomRange: '사용자 정의 범위',
	CustomRate: '사용자 정의 요금',
	CustomRecurrence: '사용자 지정 반복',
	CustomServiceAvailability: '서비스 가용성',
	CustomerBalance: '고객 잔액',
	CustomerName: '고객 이름',
	CustomerNameIsRequired: '고객 이름이 필요합니다',
	CustomerServiceRepresentative: '고객 서비스 담당자',
	CustomiseAppointments: '약속을 맞춤 설정하세요',
	CustomiseBookingLink: '예약 옵션 사용자 정의',
	CustomiseBookingLinkServicesInfo: '고객은 예약 가능한 서비스만 선택할 수 있습니다.',
	CustomiseBookingLinkServicesLabel: '서비스',
	CustomiseClientRecordsAndWorkspace: '클라이언트 기록 및 작업 공간 사용자 지정',
	CustomiseClientSettings: '클라이언트 설정 사용자 정의',
	Customize: '사용자 정의',
	CustomizeAppearance: '모양 사용자 정의',
	CustomizeAppearanceDesc:
		'브랜드와 일치하도록 온라인 예약 화면을 맞춤 설정하고 서비스가 고객에게 표시되는 방식을 최적화하세요.',
	CustomizeClientFields: '클라이언트 필드 사용자 정의',
	CustomizeInvoiceTemplate: '송장 템플릿 사용자 정의',
	CustomizeInvoiceTemplateDescription: '귀사의 브랜드를 반영하는 전문적인 송장을 손쉽게 만들어 보세요.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX가 필요합니다',
	Daily: '일일',
	DanceTherapist: '댄스 테라피스트',
	DangerZone: '위험 구역',
	Dashboard: '계기반',
	Date: '날짜',
	DateAndTime: '날짜 ',
	DateDue: '마감일',
	DateErrorMessage: '날짜가 필요합니다',
	DateFormPrimaryText: '날짜',
	DateFormSecondaryText: '날짜 선택기에서 선택하세요',
	DateIssued: '발행일',
	DateOfPayment: '지불일자',
	DateOfService: '서비스 날짜',
	DateOverride: '날짜 재정의',
	DateOverrideColor: '날짜 재정의 색상',
	DateOverrideInfo:
		'날짜 재정의를 통해 의료진은 정기적인 일정을 재정의하여 특정 날짜에 대한 진료 가능 시간을 수동으로 조정할 수 있습니다.',
	DateOverrideInfoBanner:
		'이 날짜에 지정된 서비스만 이 시간대에 예약 가능합니다. 다른 온라인 예약은 허용되지 않습니다.',
	DateOverrides: '날짜 재정의',
	DatePickerFormPrimaryText: '날짜',
	DatePickerFormSecondaryText: '날짜를 선택하세요',
	DateRange: '날짜 범위',
	DateRangeFormPrimaryText: '날짜 범위',
	DateRangeFormSecondaryText: '날짜 범위를 선택하세요',
	DateReceived: '받은 날짜',
	DateSpecificHours: '날짜별 시간',
	DateSpecificHoursDescription:
		'예약된 영업시간에서 예약 가능 시간이 변경될 경우 날짜를 추가하거나, 특정 날짜에 서비스를 제공하려면 날짜를 추가하세요.',
	DateUploaded: '업로드됨 {date, date, medium}',
	Dates: '날짜',
	Daughter: '딸',
	Day: '낮',
	DayPlural: '{count, plural, one {일} other {일}}',
	Days: '날',
	DaysPlural: '{age, plural, one {#일} other {#일}}',
	DeFacto: '사실상',
	Deactivated: '비활성화됨',
	Debit: '직불',
	DecreaseIndent: '들여쓰기 감소',
	Deductibles: '공제금',
	Default: '기본',
	DefaultBillingProfile: '기본 청구 프로필',
	DefaultDescription: '기본 설명',
	DefaultEndOfLine: '더 이상 항목이 없습니다',
	DefaultInPerson: '고객 약속',
	DefaultInvoiceTitle: '기본 제목',
	DefaultNotificationSubject: '{notificationType}에 대한 새 알림을 받았습니다.',
	DefaultPaymentMethod: '기본 결제 방법',
	DefaultService: '기본 서비스',
	DefaultValue: '기본',
	DefaultVideo: '클라이언트 비디오 약속 이메일',
	DefinedTemplateType: '{invoiceTemplate} 템플릿',
	Delete: '삭제',
	DeleteAccountButton: '계정 삭제',
	DeleteAccountDescription: '플랫폼에서 계정을 삭제하세요',
	DeleteAccountPanelInfoAlert:
		'프로필을 삭제하기 전에 작업 공간을 삭제해야 합니다. 진행하려면 작업 공간으로 전환하고 설정 > 작업 공간 설정을 선택하세요.',
	DeleteAccountTitle: '계정 삭제',
	DeleteAppointment: '약속 삭제',
	DeleteAppointmentDescription: '이 약속을 삭제하시겠습니까? 나중에 복원할 수 있습니다.',
	DeleteAvailabilityScheduleFailure: '이용 가능 일정 삭제에 실패했습니다.',
	DeleteAvailabilityScheduleSuccess: '가용성 일정을 성공적으로 삭제했습니다.',
	DeleteBillable: '청구 가능한 항목 삭제',
	DeleteBillableConfirmationMessage: '이 청구 항목을 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.',
	DeleteBillingProfileConfirmationMessage: '이렇게 하면 청구 프로필이 영구적으로 제거됩니다.',
	DeleteCardConfirmation: '이것은 영구적인 작업입니다. 카드가 삭제되면 더 이상 액세스할 수 없습니다.',
	DeleteCategory: '카테고리 삭제(변경 사항을 저장하지 않는 한 영구적으로 삭제되지 않습니다)',
	DeleteClientEventConfirmationDescription: '이는 영구적으로 제거됩니다.',
	DeleteClients: '클라이언트 삭제',
	DeleteCollection: '컬렉션 삭제',
	DeleteColumn: '열 삭제',
	DeleteConversationConfirmationDescription: '이 대화를 영구히 삭제합니다. 이 작업은 취소할 수 없습니다.',
	DeleteConversationConfirmationTitle: '대화를 영구히 삭제',
	DeleteExternalEventDescription: '이 약속을 삭제하시겠습니까?',
	DeleteFileConfirmationModalPrompt: '삭제하면 이 파일을 다시 검색할 수 없습니다.',
	DeleteFolder: '폴더 삭제',
	DeleteFolderConfirmationMessage:
		'{name} 폴더를 삭제하시겠습니까? 이 폴더 안의 모든 항목도 삭제됩니다. 나중에 복원할 수 있습니다.',
	DeleteForever: '영구 삭제',
	DeleteInsurancePayerConfirmationMessage:
		'{payer}를 제거하면 보험 지불자 목록에서 삭제됩니다. 이 작업은 영구적이며 되돌릴 수 없습니다.',
	DeleteInsurancePayerFailure: '보험납부자 삭제에 실패했습니다.',
	DeleteInsurancePolicyConfirmationMessage: '이렇게 하면 보험 혜택이 영구적으로 삭제됩니다.',
	DeleteInvoiceConfirmationDescription:
		'이 작업은 취소할 수 없습니다. 송장과 관련된 모든 지불이 영구적으로 삭제됩니다.',
	DeleteLocationConfirmation:
		'위치 삭제는 영구적인 작업입니다. 삭제하면 더 이상 액세스할 수 없습니다. 이 작업은 취소할 수 없습니다.',
	DeletePayer: '지불자 삭제',
	DeletePracticeWorkspace: '연습 작업 공간 삭제',
	DeletePracticeWorkspaceDescription: '이 연습 작업 공간을 영구적으로 삭제합니다.',
	DeletePracticeWorkspaceFailedSnackbar: '작업공간 삭제에 실패했습니다',
	DeletePracticeWorkspaceModalCancelButton: '네, 구독을 취소합니다',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'작업 공간 삭제를 진행하기 전에 먼저 구독을 취소해야 합니다.',
	DeletePracticeWorkspaceModalConfirmButton: '예, 작업 공간을 영구적으로 삭제합니다.',
	DeletePracticeWorkspaceModalDescription:
		'{name} 작업 공간이 영구적으로 삭제되며 모든 팀 구성원은 액세스 권한을 잃게 됩니다. 삭제가 진행되기 전에 필요한 중요한 데이터 또는 메시지를 다운로드하세요. 이 작업은 취소할 수 없습니다.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: '이러한 결정은 다음과 같은 이유로 내려졌습니다.',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: '이유',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: '계정을 삭제하고 싶은 이유를 알려주세요.',
	DeletePracticeWorkspaceModalTitle: '정말이에요?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: '모든 팀원의 액세스가 제거되었습니다.',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName}이(가) 성공적으로 삭제되었습니다.',
	DeletePublicTemplateContent: '이렇게 하면 공개 템플릿만 삭제되고 팀 템플릿은 삭제되지 않습니다.',
	DeleteRecurringAppointmentModalTitle: '반복되는 약속 삭제',
	DeleteRecurringEventModalTitle: '반복되는 회의 삭제',
	DeleteRecurringReminderModalTitle: '반복되는 알림 삭제',
	DeleteRecurringTaskModalTitle: '반복되는 작업 삭제',
	DeleteReminderConfirmation:
		'이것은 영구적인 작업입니다. 알림이 삭제되면 더 이상 액세스할 수 없습니다. 새로운 약속에만 영향을 미칩니다.',
	DeleteSection: '섹션 삭제',
	DeleteSectionInfo:
		'섹션 <strong>{section}</strong>을 삭제하면 해당 섹션에 있는 모든 필드가 숨겨집니다. 이 작업은 되돌릴 수 없습니다.',
	DeleteSectionWarning: '핵심 필드는 삭제할 수 없으며 기존 섹션 <strong>{section}</strong>으로 이동됩니다.',
	DeleteServiceFailure: '서비스 삭제에 실패했습니다',
	DeleteServiceSuccess: '서비스를 성공적으로 삭제했습니다',
	DeleteStaffScheduleOverrideDescription:
		'{value}에 대한 날짜 재정의를 삭제하면 일정에서 삭제되고 온라인 서비스 이용 가능 여부가 변경될 수 있습니다. 이 작업은 취소할 수 없습니다.',
	DeleteSuperbillConfirmationDescription: '이 작업은 취소할 수 없습니다. Superbill 영수증이 영구적으로 삭제됩니다.',
	DeleteSuperbillFailure: 'Superbill 영수증 삭제에 실패했습니다.',
	DeleteSuperbillSuccess: 'Superbill 영수증을 성공적으로 삭제했습니다.',
	DeleteTaxRateConfirmationDescription: '이 세율을 삭제하시겠습니까?',
	DeleteTemplateContent: '이 작업은 취소할 수 없습니다.',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} 삭제 완료',
	DeleteTemplateSuccessMessage: '{templateTitle} 삭제 완료',
	DeleteTemplateTitle: '이 템플릿을 삭제하시겠습니까?',
	DeleteTranscript: '대본 삭제',
	DeleteWorkspace: '작업공간 삭제',
	Deleted: '삭제됨',
	DeletedBy: '삭제됨',
	DeletedContact: '삭제된 연락처',
	DeletedOn: '삭제됨',
	DeletedStatusLabel: '삭제된 상태',
	DeletedUserTooltip: '이 클라이언트는 삭제되었습니다',
	DeliveryMethod: '배송 방법',
	Demo: '데모',
	Denied: '거부됨',
	Dental: '이의',
	DentalAssistant: '치과 보조원',
	DentalHygienist: '치과위생사',
	Dentist: '치과 의사',
	Dentists: '치과 의사',
	Description: '설명',
	DescriptionMustNotExceed: '설명은 {max}자를 초과할 수 없습니다.',
	DetailDurationWithStaff: '{duration}분{staffName, select, null {} other { {staffName}와 함께}}',
	Details: '세부',
	Devices: '장치',
	Diagnosis: '진단',
	DiagnosisAndBillingItems: '진단 ',
	DiagnosisCode: '진단코드',
	DiagnosisCodeErrorMessage: '진단코드가 필요합니다',
	DiagnosisCodeSelectorPlaceholder: 'ICD-10 진단 코드에서 검색 및 추가',
	DiagnosisCodeSelectorTooltip: '진단 코드는 보험금 환불을 위한 슈퍼빌 영수증을 자동화하는 데 사용됩니다.',
	DiagnosticCodes: '진단 코드',
	Dictate: '명령',
	DictatingIn: '받아쓰기',
	Dictation: '받아쓰기',
	DidNotAttend: '참석하지 않았다',
	DidNotComplete: '완료하지 못했습니다.',
	DidNotProviderEnoughValue: '충분한 가치를 제공하지 못했습니다',
	DidntProvideEnoughValue: '충분한 가치를 제공하지 못했습니다',
	DieteticsOrNutrition: '영양학 또는 영양학',
	Dietician: '영양사',
	Dieticians: '영양사',
	Dietitian: '영양사',
	DigitalSign: '여기에 서명하세요:',
	DigitalSignHelp: '(클릭/아래로 눌러서 그리기)',
	DirectDebit: '직불 인출',
	DirectTextLink: '직접 텍스트 링크',
	Disable: '장애를 입히다',
	DisabledEmailInfo: '귀하의 계정은 당사에서 관리하지 않으므로 귀하의 이메일 주소를 업데이트할 수 없습니다.',
	Discard: '버리다',
	DiscardChanges: '변경 사항 취소',
	DiscardDrafts: '초안 삭제',
	Disconnect: '연결 해제',
	DisconnectAppConfirmation: '이 앱의 연결을 해제하시겠습니까?',
	DisconnectAppConfirmationDescription: '이 앱 연결을 끊으시겠습니까?',
	DisconnectAppConfirmationTitle: '앱 연결 해제',
	Discount: '할인',
	DisplayCalendar: 'Carepatron에 표시',
	DisplayName: '표시 이름',
	DisplayedToClients: '클라이언트에게 표시됨',
	DiversionalTherapist: '전환 치료사',
	DoItLater: '나중에 하세요',
	DoNotImport: '수입하지 마십시오',
	DoNotSend: '보내지 마세요',
	DoThisLater: '나중에 하세요',
	DoYouWantToEndSession: '계속하시겠습니까? 아니면 지금 세션을 종료하시겠습니까?',
	Doctor: '의사',
	Doctors: '의사',
	DoesNotRepeat: '반복하지 않습니다',
	DoesntWorkWellWithExistingTools: '기존 도구나 워크플로와 잘 맞지 않습니다.',
	DogWalker: '반려견 산책시키기',
	Done: '완료',
	DontAllowClientsToCancel: '클라이언트가 취소하는 것을 허용하지 마세요',
	DontHaveAccount: '계정이 없으신가요?',
	DontSend: '보내지 마세요',
	Double: '더블',
	DowngradeTo: '{plan}으로 다운그레이드',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'죄송합니다. 팀원이 너무 많아서 플랜을 다운그레이드할 수 없습니다. 제공자에서 일부를 제거한 후 다시 시도하세요.',
	Download: '다운로드',
	DownloadAsPdf: 'PDF로 다운로드',
	DownloadERA: 'ERA 다운로드',
	DownloadPDF: 'PDF 다운로드',
	DownloadTemplateFileName: 'Carepatron 전환 템플릿.csv',
	DownloadTemplateTileDescription: '클라이언트를 구성하고 업로드하려면 스프레드시트 템플릿을 사용하세요.',
	DownloadTemplateTileLabel: '템플릿 다운로드',
	Downloads: '{number, plural, one {<span>#</span> 다운로드} other {<span>#</span> 다운로드}}',
	DoxyMe: '독시.미',
	Draft: '초안',
	DraftResponses: '초안 응답',
	DraftSaved: '저장된 변경 사항',
	DragAndDrop: '드래그 앤 드롭',
	DragDropText: '드래그 앤 드롭 건강 문서',
	DragToMove: '드래그하여 이동',
	DragToMoveOrActivate: '드래그하여 이동하거나 활성화하세요',
	DramaTherapist: '드라마 테라피스트',
	DropdownFormFieldPlaceHolder: '목록에서 옵션을 선택하세요',
	DropdownFormPrimaryText: '드롭다운',
	DropdownFormSecondaryText: '옵션 목록에서 선택하세요',
	DropdownTextFieldError: '드롭다운 옵션 텍스트는 비워둘 수 없습니다.',
	DropdownTextFieldPlaceholder: '드롭다운 옵션 추가',
	Due: '마감일',
	DueDate: '마감일',
	Duplicate: '복제하다',
	DuplicateAvailabilityScheduleFailure: '가용성 일정을 복제하지 못했습니다.',
	DuplicateAvailabilityScheduleSuccess: '{name} 일정 복제 성공',
	DuplicateClientBannerAction: '검토',
	DuplicateClientBannerDescription:
		'중복된 고객 기록을 병합하면 모든 고유한 고객 정보가 유지되면서 하나의 기록으로 통합됩니다.',
	DuplicateClientBannerTitle: '{count} 개의 중복 항목이 발견되었습니다.',
	DuplicateColumn: '중복된 열',
	DuplicateContactFieldSettingErrorSnackbar: '중복된 섹션 이름을 가질 수 없습니다.',
	DuplicateContactFieldSettingFieldErrorSnackbar: '중복된 필드 이름을 가질 수 없습니다.',
	DuplicateEmailError: '중복된 이메일',
	DuplicateHeadingName: '섹션 {name}은 이미 존재합니다.',
	DuplicateInvoiceNumberErrorCodeSnackbar: '동일한 "송장 번호"를 가진 송장이 이미 존재합니다.',
	DuplicateRecords: '중복된 기록',
	DuplicateRecordsMinimumError: '최소 2개의 레코드를 선택해야 합니다.',
	DuplicateRecordsRequired: '최소 1개의 레코드를 선택하여 분리하세요.',
	DuplicateServiceFailure: '<strong>{title}</strong> 복제 실패',
	DuplicateServiceSuccess: '성공적으로 <strong>{title}</strong> 복제되었습니다.',
	DuplicateTemplateFolderSuccessMessage: '폴더 복제 성공',
	DuplicateTemplateSuccess: '템플릿을 성공적으로 복제했습니다',
	DurationInMinutes: '{duration}분',
	Dx: '디엑스',
	DxCode: 'DX 코드',
	DxCodeSelectPlaceholder: 'ICD-10 코드에서 검색 및 추가',
	EIN: '개인식별번호(EIN)',
	EMG: '근전도',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: '없음',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>조정 사항이 포함되어 있습니다</i>} other {}}',
	EarnReferralCredit: '${creditAmount} 적립하기',
	Economist: '경제학자',
	Edit: '편집하다',
	EditArrangements: '편곡 편집',
	EditBillTo: '청구서 편집',
	EditClient: '클라이언트 편집',
	EditClientFileModalDescription: '"보기 가능" 체크박스에서 옵션을 선택하여 이 파일에 대한 액세스를 편집합니다.',
	EditClientFileModalTitle: '파일 편집',
	EditClientNoteModalDescription:
		'노트의 내용을 편집합니다. "Viewable by" 섹션을 사용하여 노트를 볼 수 있는 사람을 변경합니다.',
	EditClientNoteModalTitle: '메모 편집',
	EditConnectedAppButton: '편집하다',
	EditConnections: '연결 편집{account, select, null { } undefined { } other { {account}에 대한}}',
	EditContactDetails: '연락처 정보 편집',
	EditContactFormIsClientLabel: '클라이언트로 변환',
	EditContactIsClientCheckboxWarning: '연락처를 클라이언트로 변환한 후에는 실행 취소할 수 없습니다.',
	EditContactIsClientWanringModal:
		'이 연락처를 클라이언트로 전환하는 것은 취소할 수 없습니다. 그러나 모든 관계는 그대로 유지되며 이제 해당 연락처의 메모, 파일 및 기타 문서에 액세스할 수 있습니다.',
	EditContactRelationship: '연락처 관계 수정',
	EditDetails: '세부 정보 편집',
	EditFileModalTitle: '{name} 파일 수정',
	EditFolder: '폴더 편집',
	EditFolderDescription: '폴더 이름을 다음으로 변경하세요.',
	EditInvoice: '송장 수정',
	EditInvoiceDetails: '송장 세부 정보 편집',
	EditLink: '링크 편집',
	EditLocation: '위치 수정',
	EditLocationFailure: '위치 업데이트에 실패했습니다',
	EditLocationSucess: '위치를 성공적으로 업데이트했습니다',
	EditPaymentDetails: '결제 세부 정보 편집',
	EditPaymentMethod: '결제 방법 수정',
	EditPersonalDetails: '개인 정보 수정',
	EditPractitioner: '실무자 편집',
	EditProvider: '제공자 편집',
	EditProviderDetails: '공급자 세부 정보 편집',
	EditRecurrence: '반복 수정',
	EditRecurringAppointmentModalTitle: '반복되는 약속 편집',
	EditRecurringEventModalTitle: '반복되는 회의 편집',
	EditRecurringReminderModalTitle: '반복되는 알림 편집',
	EditRecurringTaskModalTitle: '반복되는 작업 편집',
	EditRelationshipModalTitle: '관계 수정',
	EditService: '서비스 편집',
	EditServiceFailure: '새로운 서비스를 업데이트하지 못했습니다.',
	EditServiceGroup: '컬렉션 편집',
	EditServiceGroupFailure: '컬렉션 업데이트에 실패했습니다',
	EditServiceGroupSuccess: '컬렉션이 성공적으로 업데이트되었습니다.',
	EditServiceSuccess: '새로운 서비스를 성공적으로 업데이트했습니다',
	EditStaffDetails: '직원 세부 정보 편집',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'이메일 주소를 업데이트할 수 없습니다. 새 이메일 주소로 새 팀원을 만들어 주세요.',
	EditSubscriptionBilledQuantity: '청구 수량',
	EditSubscriptionBilledQuantityValue: '{billedUsers} 팀 구성원',
	EditSubscriptionLimitedTimeOffer: '제한된 시간 동안 제공! 6개월 동안 50% 할인.',
	EditSubscriptionUpgradeAdjustTeamBanner: '팀 구성원을 추가하거나 삭제하면 구독 비용이 조정됩니다.',
	EditSubscriptionUpgradeContent:
		'계정이 새 요금제와 청구 기간으로 즉시 업데이트됩니다. 가격 변경 사항은 저장된 결제 수단으로 자동 청구되거나 계정에 크레딧됩니다.',
	EditSubscriptionUpgradePlanTitle: '구독 플랜 업그레이드',
	EditSuperbillReceipt: '슈퍼빌 편집',
	EditTags: '태그 편집',
	EditTemplate: '템플릿 편집',
	EditTemplateFolderSuccessMessage: '템플릿 폴더가 성공적으로 수정되었습니다',
	EditValue: '{value} 편집',
	Edited: '편집됨',
	Editor: '편집자',
	EditorAlertDescription: '지원되지 않는 형식이 감지되었습니다. 앱을 다시 로드하거나 지원팀에 문의하세요.',
	EditorAlertTitle: '이 콘텐츠를 표시하는 데 문제가 있습니다.',
	EditorPlaceholder: '글쓰기를 시작하고, 템플릿을 선택하거나 기본 블록을 추가하여 고객의 답변을 수집하세요.',
	EditorTemplatePlaceholder: '템플릿을 빌드하기 위해 작성을 시작하거나 구성 요소를 추가하세요',
	EditorTemplateWithSlashCommandPlaceholder:
		'클라이언트의 응답을 기록하기 위해 글쓰기를 시작하거나 기본 블록을 추가하세요. 빠른 동작을 위해 슬래시 명령(/)을 사용하세요.',
	EditorWithSlashCommandPlaceholder:
		'쓰기 시작, 템플릿 선택 또는 기본 블록을 추가하여 클라이언트 응답을 수집합니다. 빠른 작업을 위해 슬래시 명령( / )을 사용합니다.',
	EffectiveStartEndDate: '유효 시작일 - 종료일',
	ElectricalEngineer: '전기 기술자',
	Electronic: '전자',
	ElectronicSignature: '전자서명',
	ElementarySchoolTeacher: '초등학교 교사',
	Eligibility: '자격 요건',
	Email: '이메일',
	EmailAlreadyExists: '이메일 주소가 이미 존재합니다',
	EmailAndSms: '이메일 ',
	EmailBody: '이메일 본문',
	EmailContainsIgnoredDescription:
		'다음 이메일에는 현재 무시되는 발신자/발신자 이메일이 포함되어 있습니다. 계속하시겠습니까?',
	EmailInviteToPortalBody: `안녕하세요 {contactName}님,
다음 링크를 클릭하여 안전한 고객 포털에 로그인하고 손쉽게 치료를 관리하세요.

감사합니다,

{providerName}`,
	EmailInviteToPortalSubject: '{providerName}에 오신 것을 환영합니다',
	EmailInvoice: '이메일 송장',
	EmailInvoiceOverdueBody: `안녕하세요 {contactName}님
{invoiceNumber}번 청구서가 연체되었습니다.
아래 링크를 사용하여 온라인으로 청구서를 결제해 주세요.

문의 사항이 있으시면 알려주세요.

감사합니다,
{providerName}`,
	EmailInvoicePaidBody: `안녕하세요 {contactName}님
청구서 {invoiceNumber}가 지불되었습니다.
청구서 사본을 보시려면 아래 링크를 클릭하세요.

궁금한 사항이 있으시면 알려주세요.

감사합니다,
{providerName}`,
	EmailInvoiceProcessingBody: `안녕하세요 {contactName}님
{invoiceNumber}번 청구서가 준비되었습니다.
아래 링크를 통해 청구서를 확인하세요.

궁금한 사항이 있으시면 알려주세요.

감사합니다,
{providerName}`,
	EmailInvoiceUnpaidBody: `안녕하세요 {contactName}님
{invoiceNumber}번 청구서가 발행되었습니다. 지불 마감일은 {dueDate}입니다.
아래 링크를 통해 청구서를 확인하고 온라인으로 결제하실 수 있습니다.

문의 사항이 있으시면 알려주세요.

감사합니다,
{providerName}`,
	EmailInvoiceVoidBody: `안녕하세요 {contactName}님
{invoiceNumber}번 청구서는 무효입니다.
아래 링크를 클릭하여 청구서를 확인하세요.

문의 사항이 있으시면 알려주세요.

감사합니다,
{providerName}`,
	EmailNotFound: '이메일을 찾을 수 없습니다',
	EmailNotVerifiedErrorCodeSnackbar: '작업을 수행할 수 없습니다. 이메일 주소를 확인해야 합니다.',
	EmailNotVerifiedTitle: '귀하의 이메일은 검증되지 않았습니다. 일부 기능이 제한됩니다.',
	EmailSendClientIntakeBody: `안녕하세요 {contactName}님,
{providerName}에서 정보를 제공해주시고 중요한 문서를 검토해주시기 바랍니다. 시작하려면 아래 링크를 클릭해주세요.

감사합니다.

{providerName}`,
	EmailSendClientIntakeSubject: '{providerName}에 오신 것을 환영합니다.',
	EmailSuperbillReceipt: '이메일 슈퍼빌',
	EmailSuperbillReceiptBody: `안녕하세요 {contactName}님,
{providerName}에서 {date}에 귀하의 환급 영수증 사본을 보내드렸습니다.

다운로드하여 보험 회사에 직접 제출하실 수 있습니다.`,
	EmailSuperbillReceiptFailure: 'Superbill 영수증을 보내지 못했습니다.',
	EmailSuperbillReceiptSubject: '{providerName}은(는) 환불 영수증을 발송했습니다.',
	EmailSuperbillReceiptSuccess: 'Superbill 영수증을 성공적으로 보냈습니다',
	EmailVerificationDescription: '지금 귀하의 계정을 <span>확인</span> 하고 있습니다.',
	EmailVerificationNotification: '{email} 로 인증 이메일이 발송되었습니다.',
	EmailVerificationSuccess: '이메일 주소가 {email}로 성공적으로 변경되었습니다.',
	Emails: '이메일',
	EmergencyContact: '비상연락처',
	EmployeesIdentificationNumber: '직원식별번호',
	EmploymentStatus: '고용 상태',
	EmptyAgendaViewDescription: '표시할 이벤트가 없습니다.<mark> 지금 약속을 만드세요</mark>',
	EmptyBin: '빈 통',
	EmptyBinConfirmationDescription:
		'비운 휴지통은 삭제된 항목에서 모든 **{total}개의 대화**를 삭제합니다. 이 작업은 되돌릴 수 없습니다.',
	EmptyBinConfirmationTitle: '대화를 영구히 삭제',
	EmptyTrash: '휴지통 비우기',
	Enable: '할 수 있게 하다',
	EnableCustomServiceAvailability: '서비스 가용성 활성화',
	EnableCustomServiceAvailabilityDescription: '예를 들어 초기 예약은 매일 오전 9시부터 10시까지만 예약 가능합니다.',
	EndCall: '통화 종료',
	EndCallConfirmationForCreator: '당신은 통화를 시작한 사람이기 때문에 모든 사람을 위해 통화를 종료해야 합니다.',
	EndCallConfirmationHasActiveAttendees:
		'통화를 종료하려고 하는데 클라이언트가 이미 참여했습니다. 당신도 참여하시겠습니까?',
	EndCallForAll: '모든 사람을 위한 통화 종료',
	EndDate: '종료일',
	EndDictation: '받아쓰기 종료',
	EndOfLine: '더 이상 예약이 없습니다',
	EndSession: '세션 종료',
	EndTranscription: '전사 종료',
	Ends: '종료',
	EndsOnDate: '{date}에 종료됩니다.',
	Enrol: '등록',
	EnrollmentRejectedSubject: '{payerName}에 대한 등록이 거부되었습니다.',
	Enrolment: '섭취',
	Enrolments: '등록',
	EnrolmentsDescription: '지불자와 함께 제공자 등록을 보고 관리합니다.',
	EnterAName: '이름을 입력하세요...',
	EnterFieldLabel: '필드 라벨을 입력하세요...',
	EnterPaymentDetailsDescription: '사용자를 추가하거나 제거하면 구독 비용이 자동으로 조정됩니다.',
	EnterSectionName: '섹션 이름을 입력하세요...',
	EnterSubscriptionPaymentDetails: '결제 세부 정보를 입력하세요',
	EnvironmentalScientist: '환경 과학자',
	Epidemiologist: '역학자',
	Eraser: '지우개',
	Error: '오류',
	ErrorBoundaryAction: '페이지 다시 로드',
	ErrorBoundaryDescription: '페이지를 새로 고치고 다시 시도하세요.',
	ErrorBoundaryTitle: '앗! 뭔가 잘못됐어요',
	ErrorCallNotFound: '호출을 찾을 수 없습니다. 만료되었거나 생성자가 종료했을 수 있습니다.',
	ErrorCannotAccessCallUninvitedCode: '죄송합니다. 이 통화에 초대받지 못한 것 같습니다.',
	ErrorFileUploadCustomMaxFileCount: '한 번에 {count}개 이상의 파일을 업로드할 수 없습니다.',
	ErrorFileUploadCustomMaxFileSize: '파일 크기는 {mb} MB를 초과할 수 없습니다.',
	ErrorFileUploadInvalidFileType: '잠재적인 바이러스 및 유해한 소프트웨어를 포함할 수 있는 잘못된 파일 유형입니다.',
	ErrorFileUploadMaxFileCount: '한 번에 150개 이상의 파일을 업로드할 수 없습니다.',
	ErrorFileUploadMaxFileSize: '파일 크기는 100MB를 초과할 수 없습니다.',
	ErrorFileUploadNoFileSelected: '업로드할 파일을 선택해주세요',
	ErrorInvalidNationalProviderId: '제공된 국가 공급자 ID가 유효하지 않습니다.',
	ErrorInvalidPayerId: '제공된 지불자 ID가 유효하지 않습니다.',
	ErrorInvalidTaxNumber: '제공된 세금 번호가 유효하지 않습니다.',
	ErrorInviteExistingProviderStaffCode: '이 사용자는 이미 워크스페이스에 있습니다.',
	ErrorInviteStaffExistingUser: '죄송합니다. 추가하신 사용자는 이미 저희 시스템에 존재하는 것 같습니다.',
	ErrorOnlySingleCallAllowed: '한 번에 한 통화만 가능합니다. 새 통화를 시작하려면 현재 통화를 종료하세요.',
	ErrorPayerNotFound: '지불자를 찾을 수 없습니다',
	ErrorProfilePhotoMaxFileSize: '업로드 실패! 파일 크기 제한에 도달했습니다 - 5MB',
	ErrorRegisteredExistingUser: '죄송합니다. 이미 등록된 것 같습니다.',
	ErrorUserSignInIncorrectCredentials: '잘못된 이메일 또는 비밀번호입니다. 다시 시도해 주세요.',
	ErrorUserSigninGeneric: '죄송합니다. 오류가 발생했습니다.',
	ErrorUserSigninUserNotConfirmed:
		'죄송합니다. 로그인하기 전에 계정을 확인해야 합니다. 지침은 받은 편지함에서 확인하세요.',
	Errors: '오류',
	EssentialPlanInclusionFive: '템플릿 가져오기',
	EssentialPlanInclusionFour: '5GB의 저장공간',
	EssentialPlanInclusionHeader: '모든 것이 무료입니다  ',
	EssentialPlanInclusionOne: '자동 및 맞춤 알림',
	EssentialPlanInclusionSix: '우선 지원',
	EssentialPlanInclusionThree: '화상 채팅',
	EssentialPlanInclusionTwo: '2방향 캘린더 동기화',
	EssentialSubscriptionPlanSubtitle: '필수 요소로 연습을 간소화하세요',
	EssentialSubscriptionPlanTitle: '필수적인',
	Esthetician: '미용사',
	Estheticians: '미용사',
	EstimatedArrivalDate: '예상 도착 {numberOfDaysFromNow}',
	Ethnicity: '민족성',
	Europe: '유럽',
	EventColor: '미팅 컬러',
	EventName: '이벤트 이름',
	EventType: '이벤트 유형',
	Every: '모든',
	Every2Weeks: '2주마다',
	EveryoneInWorkspace: '작업 공간에 있는 모든 사람',
	ExercisePhysiologist: '운동생리학자',
	Existing: '기존',
	ExistingClients: '기존 고객',
	ExistingFolders: '기존 폴더',
	ExpiredPromotionCode: '프로모션 코드가 만료되었습니다',
	ExpiredReferralDescription: '추천이 만료되었습니다',
	ExpiredVerificationLink: '만료된 검증 링크',
	ExpiredVerificationLinkDescription: `죄송합니다만, 클릭하신 확인 링크가 만료되었습니다. 이는 링크를 클릭하기 위해 24시간 이상 기다렸거나 이미 링크를 사용하여 이메일 주소를 확인했을 경우 발생할 수 있습니다.

 이메일 주소를 인증하려면 새로운 인증 링크를 요청하세요.`,
	ExpiryDateRequired: '유효기간이 필요합니다',
	ExploreFeature: '무엇을 먼저 알아보고 싶으신가요?',
	ExploreOptions: '하나 이상의 옵션을 선택하여 살펴보세요.',
	Export: '내보내다',
	ExportAppointments: '수출 약속',
	ExportClaims: '내보내기 청구',
	ExportClaimsFilename: '청구 {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: '오류로 인해 파일을 다운로드할 수 없습니다.',
	ExportClientsDownloadFailureSnackbarTitle: '다운로드 실패',
	ExportClientsFailureSnackbarDescription: '오류로 인해 파일을 성공적으로 내보내지 못했습니다.',
	ExportClientsFailureSnackbarTitle: '내보내기 실패',
	ExportClientsModalDescription: `이 데이터 내보내기 프로세스는 내보내는 데이터 양에 따라 몇 분이 걸릴 수 있습니다. 다운로드할 준비가 되면 링크가 포함된 이메일 알림을 받게 됩니다.

 클라이언트 데이터를 내보내시겠습니까?`,
	ExportClientsModalTitle: '클라이언트 데이터 내보내기',
	ExportCms1500: 'CMS1500 수출',
	ExportContactFailedNotificationSubject: '데이터 내보내기가 실패했습니다.',
	ExportFailed: '내보내기 실패',
	ExportGuide: '수출 가이드',
	ExportInvoiceFileName: '거래내역 {fromDate}-{toDate}.csv',
	ExportPayments: '지불 내역 내보내기',
	ExportPaymentsFilename: '결제 {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: '문서를 다운로드할 준비가 되었습니다.',
	ExportPrintWaitMessage: '문서를 준비 중입니다. 잠시만 기다려 주세요...',
	ExportTextOnly: '텍스트만 내보내기',
	ExportTransactions: '수출 거래',
	Exporting: '수출 중',
	ExportingData: '데이터 내보내기',
	ExtendedFamilyMember: '확대된 가족 구성원',
	External: '외부',
	ExternalEventInfoBanner: '이 약속은 동기화된 일정에서 가져온 것이며 항목이 누락되었을 수 있습니다.',
	ExtraLarge: '특대',
	FECABlackLung: 'FECA Black Lung',
	Failed: '실패한',
	FailedToJoinTheMeeting: '회의에 참여하지 못했습니다.',
	FallbackPageDescription: `이 페이지는 존재하지 않는 것 같습니다. {refreshButton}을 클릭하여 최신 변경 사항을 확인하세요.
그렇지 않으면 Carepatron 지원팀에 문의하세요.`,
	FallbackPageDescriptionUpdateButton: '새로 고치다',
	FallbackPageTitle: '어머...',
	FamilyPlanningService: '가족 계획 서비스',
	FashionDesigner: '패션 디자이너',
	FastTrackInvoicingAndBilling: '송장 및 청구서를 빠르게 추적하세요',
	Father: '아버지',
	FatherInLaw: '시아버지',
	Favorite: '즐겨찾기',
	FeatureBannerCalendarTile1ActionLabel: '온라인 예약 • 2분',
	FeatureBannerCalendarTile1Description: '간단히 이메일, 문자 메시지를 보내거나 웹사이트에 가용성을 추가하세요.',
	FeatureBannerCalendarTile1Title: '고객이 온라인으로 예약할 수 있도록 하세요',
	FeatureBannerCalendarTile2ActionLabel: '알림 자동화 • 2분',
	FeatureBannerCalendarTile2Description: '자동화된 알림으로 고객 참석률 증가',
	FeatureBannerCalendarTile2Title: '불참자 감소',
	FeatureBannerCalendarTile3Title: '스케줄링 및 워크플로',
	FeatureBannerCalendarTitle: '일정을 쉽게 잡으세요',
	FeatureBannerCallsTile1ActionLabel: '원격진료 전화 시작',
	FeatureBannerCallsTile1Description:
		'링크만 있으면 클라이언트에 접속할 수 있습니다. 로그인, 비밀번호 또는 번거로움 없음',
	FeatureBannerCallsTile1Title: '어디에서나 화상 통화를 시작하세요',
	FeatureBannerCallsTile2ActionLabel: '앱 연결 • 4분',
	FeatureBannerCallsTile2Description: '다른 선호하는 텔레헬스 제공자와 원활하게 연결',
	FeatureBannerCallsTile2Title: '텔레헬스 앱을 연결하세요',
	FeatureBannerCallsTile3Title: '전화',
	FeatureBannerCallsTitle: '언제 어디서나 고객과 연결하세요',
	FeatureBannerClientsTile1ActionLabel: '지금 가져오기 • 2분',
	FeatureBannerClientsTile1Description: '자동화된 클라이언트 가져오기 도구로 빠르게 시작하세요',
	FeatureBannerClientsTile1Title: '고객이 많으신가요?',
	FeatureBannerClientsTile2ActionLabel: '섭취량 맞춤 설정 • 2분',
	FeatureBannerClientsTile2Description: '접수 서류를 제거하고 고객 경험을 개선하세요',
	FeatureBannerClientsTile2Title: '종이 없이 생활하세요',
	FeatureBannerClientsTile3Title: '클라이언트 포털',
	FeatureBannerClientsTitle: '모든 것은 고객으로부터 시작됩니다',
	FeatureBannerHeader: '지역사회에 의한, 지역사회를 위한!',
	FeatureBannerInvoicesTile1ActionLabel: '결제 자동화 • 2분',
	FeatureBannerInvoicesTile1Description: '자동 결제로 어색한 대화를 피하세요',
	FeatureBannerInvoicesTile1Title: '2배 더 빨리 급여를 받으세요',
	FeatureBannerInvoicesTile2ActionLabel: '현금 흐름 추적 • 2분',
	FeatureBannerInvoicesTile2Description: '미납 청구서를 줄이고 수입을 파악하세요',
	FeatureBannerInvoicesTile2Title: '고통없이 수입을 추적하세요',
	FeatureBannerInvoicesTile3Title: '청구 및 지불',
	FeatureBannerInvoicesTitle: '걱정할 일이 하나 줄었습니다',
	FeatureBannerSubheader:
		'우리 팀과 커뮤니티가 만든 Carepatron 템플릿. 새로운 리소스를 시도하거나 자신의 리소스를 공유하세요!',
	FeatureBannerTeamTile1ActionLabel: '지금 초대하세요',
	FeatureBannerTeamTile1Description: '팀원을 귀하의 계정에 초대하여 협업을 쉽게 만드세요',
	FeatureBannerTeamTile1Title: '팀을 하나로 모으세요',
	FeatureBannerTeamTile2ActionLabel: '가용성 설정 • 2분',
	FeatureBannerTeamTile2Description: '중복 예약을 방지하기 위해 팀의 가용성을 관리하세요',
	FeatureBannerTeamTile2Title: '귀하의 가용성을 설정하세요',
	FeatureBannerTeamTile3ActionLabel: '권한 설정 • 2분',
	FeatureBannerTeamTile3Description: '규정 준수를 위한 중요 데이터 및 도구에 대한 액세스 제어',
	FeatureBannerTeamTile3Title: '권한 및 액세스 사용자 지정',
	FeatureBannerTeamTitle: '혼자서는 위대한 일을 이룰 수 없습니다.',
	FeatureBannerTemplatesTile1ActionLabel: '도서관 탐색 • 2분',
	FeatureBannerTemplatesTile1Description: '사용자 정의 가능한 리소스의 놀라운 라이브러리에서 선택하세요 ',
	FeatureBannerTemplatesTile1Title: '업무량을 줄이세요',
	FeatureBannerTemplatesTile2ActionLabel: '지금 보내기 • 2분',
	FeatureBannerTemplatesTile2Description: '완성을 위해 아름다운 템플릿을 고객에게 보내세요',
	FeatureBannerTemplatesTile2Title: '문서화를 재미있게 만들어보세요',
	FeatureBannerTemplatesTile3Title: '템플릿',
	FeatureBannerTemplatesTitle: '무엇이든 템플릿',
	FeatureLimitBannerDescription:
		'지금 업그레이드하여 {featureName}을 계속 만들고 관리하고 Carepatron을 최대한 활용하세요!',
	FeatureLimitBannerTitle: '{featureName} 한도까지 {percentage}% 남았습니다.',
	FeatureRequiresUpgrade: '이 기능은 업그레이드가 필요합니다.',
	Fee: '요금',
	Female: '여성',
	FieldLabelTooltip: '{isHidden, select, true {보이기} other {숨기기}} 필드 레이블',
	FieldName: '필드 이름',
	FieldOptionsFirstPart: '첫 번째 단어',
	FieldOptionsMiddlePart: '중간 단어',
	FieldOptionsSecondPart: '마지막 단어',
	FieldOptionsWholeField: '전체 필드',
	FieldType: '필드 유형',
	Fields: '전지',
	File: '파일',
	FileDownloaded: '<strong>{fileName}</strong> 다운로드됨',
	FileInvalidType: '지원되지 않는 파일입니다.',
	FileNotFound: '파일을 찾을 수 없습니다',
	FileNotFoundDescription: '찾으시는 파일을 사용할 수 없거나 삭제되었습니다.',
	FileTags: '파일 태그',
	FileTagsHelper: '태그는 모든 파일에 적용됩니다',
	FileTooLarge: '파일이 너무 큽니다.',
	FileTooSmall: '파일이 너무 작습니다.',
	FileUploadComplete: '완벽한',
	FileUploadFailed: '실패한',
	FileUploadInProgress: '로딩중',
	FileUploadedNotificationSubject: '{actorProfileName}가 파일을 업로드했습니다.',
	Files: '파일',
	FillOut: '작성하다',
	Filter: '필터',
	FilterBy: '필터링 기준',
	FilterByAmount: '금액으로 필터링',
	FilterByClient: '클라이언트별 필터링',
	FilterByLocation: '위치별 필터링',
	FilterByService: '서비스로 필터링',
	FilterByStatus: '상태별 필터링',
	FilterByTags: '태그로 필터링',
	FilterByTeam: '팀별 필터링',
	Filters: '필터',
	FiltersAppliedToView: '적용된 필터 보기',
	FinalAppointment: '최종 약속',
	FinalizeImport: '최종 가져오기',
	FinancialAnalyst: '재무 분석가',
	Finish: '마치다',
	Firefighter: '소방관',
	FirstName: '이름',
	FirstNameLastInitial: '이름, 성의 첫 글자',
	FirstPerson: '1인칭',
	FolderName: '폴더 이름',
	Folders: '폴더',
	FontFamily: '글꼴 패밀리',
	ForClients: '고객을 위해',
	ForClientsDetails: '나는 치료나 건강 관련 서비스를 받습니다',
	ForPractitioners: '실무자를 위한',
	ForPractitionersDetails: '귀하의 진료를 관리하고 성장시키세요',
	ForgotPasswordConfirmAccessCode: '확인코드',
	ForgotPasswordConfirmNewPassword: '새로운 비밀번호',
	ForgotPasswordConfirmPageDescription: '이메일 주소, 새로운 비밀번호, 방금 보낸 확인 코드를 입력하세요.',
	ForgotPasswordConfirmPageTitle: '비밀번호 재설정',
	ForgotPasswordPageButton: '재설정 링크 보내기',
	ForgotPasswordPageDescription: '이메일을 입력하시면 비밀번호 재설정 링크를 보내드립니다.',
	ForgotPasswordPageTitle: '비밀번호를 잊어버리셨습니까?',
	ForgotPasswordSuccessPageDescription: '받은 편지함에서 재설정 링크를 확인하세요.',
	ForgotPasswordSuccessPageTitle: '재설정 링크가 전송되었습니다!',
	Form: '양식',
	FormAnswersSentToEmailNotification: '귀하의 답변 사본을 다음 주소로 보냈습니다.',
	FormBlocks: '블록을 형성하다',
	FormFieldAddOption: '옵션 추가',
	FormFieldAddOtherOption: '"기타"를 추가하세요',
	FormFieldOptionPlaceholder: '옵션 {index}',
	FormStructures: '구조 형성',
	Format: '포맷',
	FormatLinkButtonColor: '버튼 색상',
	Forms: '양식',
	FormsAndAgreementsValidationMessage: '섭취 과정을 계속 진행하려면 모든 양식과 계약서를 작성해야 합니다.',
	FormsCategoryDescription: '환자 정보 수집 및 정리',
	Frankfurt: '프랑크푸르트',
	Free: '무료',
	FreePlanInclusionFive: '자동 청구 ',
	FreePlanInclusionFour: '클라이언트 포털',
	FreePlanInclusionHeader: '시작하세요',
	FreePlanInclusionOne: '무제한 클라이언트',
	FreePlanInclusionSix: '라이브 지원',
	FreePlanInclusionThree: '1GB의 저장공간',
	FreePlanInclusionTwo: '원격진료',
	FreeSubscriptionPlanSubtitle: '누구나 무료',
	FreeSubscriptionPlanTitle: '무료',
	Friday: '금요일',
	From: '에서',
	FullName: '전체 이름',
	FunctionalMedicineOrNaturopath: '기능성 의학 또는 자연 요법',
	FuturePaymentsAuthoriseProvider: '저장된 결제 정보를 향후 {provider}에서 사용하도록 허용',
	FuturePaymentsSavePaymentMethod: '미래 결제를 위해 {paymentMethod} 저장',
	GST: '상품세금(GST)',
	Gender: '성별',
	GeneralAvailability: '일반 가용성',
	GeneralAvailabilityDescription:
		'정기적으로 이용 가능한 시간을 설정하세요. 고객은 이용 가능한 시간에만 서비스를 예약할 수 있습니다.',
	GeneralAvailabilityDescription2:
		'특정 시간대에 귀하의 이용 가능 여부와 원하는 서비스 제공 사항을 기반으로 일정을 생성하여 온라인 예약 가능 여부를 확인하세요.',
	GeneralAvailabilityInfo: '귀하의 이용 가능 시간은 귀하의 온라인 예약 가능 여부를 결정합니다.',
	GeneralAvailabilityInfo2:
		'그룹 이벤트를 제공하는 서비스에서는 새로운 일정을 사용하여 고객이 온라인으로 예약할 수 있는 시간을 줄여야 합니다.',
	GeneralHoursPlural: '{count} {count, plural, one {시간} other {시간}}',
	GeneralPractitioner: '일반의',
	GeneralPractitioners: '일반의',
	GeneralServiceAvailabilityInfo: '이 일정은 지정된 팀 구성원의 동작을 재정의합니다.',
	Generate: '생성하다',
	GenerateBillingItemsBannerContent: '반복되는 약속에 대한 청구 항목은 자동으로 생성되지 않습니다.',
	GenerateItems: '아이템 생성',
	GenerateNote: '노트 생성',
	GenerateNoteConfirmationModalDescription:
		'무엇을 하시겠습니까? 새로 생성된 노트를 만들거나, 기존 노트에 추가하거나, 해당 내용을 대체하시겠습니까?',
	GenerateNoteFor: '메모 생성',
	GeneratingContent: '콘텐츠 생성 중...',
	GeneratingNote: '메모를 생성하는 중입니다...',
	GeneratingTranscript: '전사본 생성 중',
	GeneratingTranscriptDescription: '처리하는 데 몇 분이 걸릴 수 있습니다.',
	GeneratingYourTranscript: '필사본 생성',
	GenericErrorDescription: '{module}을(를) 불러올 수 없습니다. 나중에 다시 시도해 주세요.',
	GenericErrorTitle: '예상치 못한 오류가 발생했습니다',
	GenericFailureSnackbar: '죄송합니다. 예상치 못한 일이 발생했습니다. 페이지를 새로 고침하고 다시 시도하세요.',
	GenericSavedSuccessSnackbar: '성공! 변경 사항이 저장되었습니다.',
	GeneticCounselor: '유전상담사',
	Gerontologist: '노년학자',
	Get50PercentOff: '50% 할인!',
	GetHelp: '도움을 받으세요',
	GetStarted: '시작하세요',
	GettingStartedAppointmentTypes: '약속 유형 만들기',
	GettingStartedAppointmentTypesDescription:
		'서비스, 가격 및 청구 코드를 사용자 지정하여 일정 및 청구를 간소화하세요.',
	GettingStartedAppointmentTypesTitle: '일정 ',
	GettingStartedClients: '클라이언트를 추가하세요',
	GettingStartedClientsDescription: '향후 약속, 메모 및 지불을 위해 고객과 협력하여 작업을 시작하세요.',
	GettingStartedClientsTitle: '모든 것은 고객으로부터 시작됩니다',
	GettingStartedCreateClient: '클라이언트 생성',
	GettingStartedImportClients: '클라이언트 가져오기',
	GettingStartedInvoices: '프로처럼 송장 작성하기',
	GettingStartedInvoicesDescription: `전문적인 송장을 만드는 것은 간단합니다.
 로고, 위치, 지불 조건을 추가하세요`,
	GettingStartedInvoicesTitle: '최선을 다해 보세요',
	GettingStartedMobileApp: '모바일 앱을 받으세요',
	GettingStartedMobileAppDescription:
		'이동 중에도 쉽게 액세스할 수 있도록 iOS, Android 또는 데스크톱 기기에 Carepatron을 다운로드할 수 있습니다.',
	GettingStartedMobileAppTitle: '어디에서나 작업하세요',
	GettingStartedNavItem: '시작하기',
	GettingStartedPageTitle: 'Carepatron 시작하기',
	GettingStartedPayments: '온라인 결제 수락',
	GettingStartedPaymentsDescription: `고객이 온라인으로 결제할 수 있도록 하여 더 빨리 대금을 받으세요.
 모든 송장과 지불을 한곳에서 확인하세요`,
	GettingStartedPaymentsTitle: '결제를 더욱 간편하게 하세요',
	GettingStartedSaveBranding: '브랜딩 저장',
	GettingStartedSyncCalendars: '다른 캘린더 동기화',
	GettingStartedSyncCalendarsDescription:
		'Carepatron은 일정 충돌 여부를 확인하므로 귀하가 가능한 경우에만 약속을 예약합니다.',
	GettingStartedSyncCalendarsTitle: '항상 최신 정보를 얻으세요',
	GettingStartedVideo: '소개 영상을 시청하세요',
	GettingStartedVideoDescription: '소규모 팀과 고객을 위한 최초의 올인원 의료 작업 공간',
	GettingStartedVideoTitle: 'Carepatron에 오신 것을 환영합니다',
	GetttingStartedGetMobileDownload: '앱을 다운로드하세요',
	GetttingStartedGetMobileNoDownload:
		'이 브라우저와 호환되지 않습니다. iPhone 또는 iPad를 사용하는 경우 Safari에서 이 페이지를 열어주세요. 그렇지 않은 경우 Chrome에서 열어보세요.',
	Glossary: '용어집',
	Gmail: '지메일',
	GmailSendMessagesLimitWarning:
		'Gmail은 하루에 귀하의 계정에서 500개의 메시지만 보낼 수 있습니다. 일부 메시지는 실패할 수 있습니다. 계속하시겠습니까?',
	GoToAppointment: '약속장소로 가다',
	GoToApps: '앱으로 이동',
	GoToAvailability: '이용 가능 여부로 이동',
	GoToClientList: '클라이언트 목록으로 이동',
	GoToClientRecord: '클라이언트 기록으로 이동',
	GoToClientSettings: '지금 클라이언트 설정으로 이동하세요',
	GoToInvoiceTemplates: '송장 템플릿으로 이동',
	GoToNotificationSettings: '알림 설정으로 이동',
	GoToPaymentSettings: '결제 설정으로 이동',
	Google: 'Google',
	GoogleCalendar: '구글 캘린더',
	GoogleColor: '구글 캘린더 색상',
	GoogleMeet: '구글 미트',
	GoogleTagManagerContainerId: 'Google 태그 관리자 컨테이너 ID',
	GotIt: '알았어요!',
	Goto: '이동하다',
	Granddaughter: '손녀',
	Grandfather: '할아버지',
	Grandmother: '할머니',
	Grandparent: '조부모',
	Grandson: '손자',
	GrantPortalAccess: '포털 액세스 권한 부여',
	GraphicDesigner: '그래픽 디자이너',
	Grid: '그리드',
	GridView: '그리드 보기',
	Group: '그룹',
	GroupBy: '그룹별로',
	GroupEvent: '그룹 이벤트',
	GroupEventHelper: '서비스에 참석자 제한을 설정합니다.',
	GroupFilterLabel: '모든 {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: '그룹 ID',
	GroupInputFieldsFormPrimaryText: '그룹 입력 필드',
	GroupInputFieldsFormSecondaryText: '사용자 정의 필드를 선택하거나 추가하세요',
	GuideTo: '{value} 가이드',
	GuideToImproveVideoQuality: '비디오 화질을 개선하기 위한 가이드',
	GuideToManagingPayers: '지불자 관리',
	GuideToSubscriptionsBilling: '구독 청구 가이드',
	GuideToTroubleshooting: '문제 해결 가이드',
	Guidelines: '지침',
	GuidelinesCategoryDescription: '임상 의사 결정을 위한 지침',
	HST: 'HST',
	HairStylist: '헤어 스타일리스트',
	HaveBeenWaiting: '당신은 오랫동안 기다리고 있었어요',
	HeHim: '그는/그를',
	HeaderAccountSettings: '윤곽',
	HeaderCalendar: '달력',
	HeaderCalls: '전화',
	HeaderClientAppAccountSettings: '계정 설정',
	HeaderClientAppCalls: '전화',
	HeaderClientAppMyDocumentation: '선적 서류 비치',
	HeaderClientAppMyRelationships: '내 관계',
	HeaderClients: '고객',
	HeaderHelp: '돕다',
	HeaderMoreOptions: '더 많은 옵션',
	HeaderStaff: '직원',
	HealthCoach: '건강 코치',
	HealthCoaches: '건강 코치',
	HealthEducator: '건강 교육자',
	HealthInformationTechnician: '건강 정보 기술자',
	HealthPolicyExpert: '건강 정책 전문가',
	HealthServicesAdministrator: '건강 서비스 관리자',
	HelpArticles: '도움말 문서',
	HiddenColumns: '숨겨진 열',
	HiddenFields: '숨겨진 필드',
	HiddenSections: '숨겨진 섹션',
	HiddenSectionsAndFields: '숨겨진 섹션/필드',
	HideColumn: '열 숨기기',
	HideColumnButton: '{value} 열 숨기기 버튼',
	HideDetails: '세부 정보 숨기기',
	HideField: '필드 숨기기',
	HideFullAddress: '숨다',
	HideMenu: '메뉴 숨기기',
	HideMergeSummarySidebar: '병합 요약 숨기기',
	HideSection: '섹션 숨기기',
	HideYourView: '보기 숨기기',
	Highlight: '하이라이트 색상',
	Highlighter: '하이라이터',
	History: '역사',
	HistoryItemFooter: '{actors, select, undefined {{date} at {time}} other {By {actors} • {date} at {time}}}',
	HistorySidePanelEmptyState: '기록된 내역이 없습니다.',
	HistoryTitle: '활동 로그',
	HolisticHealthPractitioner: '전인 건강 전문가',
	HomeCaregiver: '홈 케어기버',
	HomeHealthAide: '홈 헬스 에이드',
	HomelessShelter: '노숙자 보호소',
	HourAbbreviation: '{count} {count, plural, one {시간} other {시간}}',
	Hourly: '시간당',
	HoursPlural: '{age, plural, one {# 시간} other {# 시간}}',
	HowCanWeImprove: '이걸 어떻게 개선할 수 있을까요?',
	HowCanWeImproveResponse: '이 응답을 어떻게 개선할 수 있을까요?',
	HowDidWeDo: '어떻게 생각하세요?',
	HowDoesReferralWork: '추천 프로그램 가이드',
	HowToUseAiSummarise: 'AI Summarize를 사용하는 방법',
	HumanResourcesManager: '인사 관리자',
	Husband: '남편',
	Hypnotherapist: '최면 치료사',
	IVA: '부가가치세',
	IgnoreNotification: '알림 무시',
	IgnoreOnce: '한번 무시하세요',
	IgnoreSender: '발신자 무시',
	IgnoreSenderDescription: `이 발신자의 향후 대화는 자동으로 '기타'로 이동됩니다. 이 발신자를 무시하시겠습니까?`,
	IgnoreSenders: '발신자 무시',
	IgnoreSendersSuccess: '무시된 이메일 주소 <mark>{addresses}</mark>',
	Ignored: '무시됨',
	Image: '영상',
	Import: '수입',
	ImportActivity: 'Import activity가져오기 활동',
	ImportClientSuccessSnackbarDescription: '귀하의 파일이 성공적으로 가져왔습니다.',
	ImportClientSuccessSnackbarTitle: '가져오기가 성공했습니다!',
	ImportClients: '클라이언트 가져오기',
	ImportClientsFailureSnackbarDescription: '오류로 인해 파일을 성공적으로 가져오지 못했습니다.',
	ImportClientsFailureSnackbarTitle: '가져오기에 실패했습니다!',
	ImportClientsGuide: '클라이언트 가져오기 가이드',
	ImportClientsInProgressSnackbarDescription: '완료하는 데 최대 1분 정도 걸립니다.',
	ImportClientsInProgressSnackbarTitle: '{fileName} 가져오기',
	ImportClientsModalDescription:
		'데이터가 어디에서 왔는지 선택하세요. 기기에 있는 파일이든, 타사 서비스든, 다른 소프트웨어 플랫폼이든 말이죠.',
	ImportClientsModalFileUploadHelperText: '지원되는 파일 형식: {fileTypes}. 크기 제한: {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: '클라이언트 데이터 가져오기 가이드',
	ImportClientsModalStep1Label: '데이터 소스 선택',
	ImportClientsModalStep2Label: '파일 업로드',
	ImportClientsModalStep3Label: '검토 필드',
	ImportClientsModalTitle: '클라이언트 데이터 가져오기',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {고객} other {고객}} 가져오기 준비 완료',
	ImportContactFailedNotificationSubject: '데이터 가져오기에 실패했습니다.',
	ImportDataSourceSelectorLabel: '데이터 소스 가져오기',
	ImportDataSourceSelectorPlaceholder: '데이터 소스 가져오기 검색 또는 선택',
	ImportExportButton: '수입/수출',
	ImportFailed: '가져오기 실패',
	ImportFromAnotherPlatformTileDescription: '고객 파일을 내보낸 다음 여기에 업로드하세요.',
	ImportFromAnotherPlatformTileLabel: '다른 플랫폼에서 가져오기',
	ImportGuide: '수입 가이드',
	ImportInProgress: '가져오기 진행 중',
	ImportProcessing: 'Import 처리 중...',
	ImportSpreadsheetDescription:
		'.CSV, .XLS 또는 .XLSX와 같은 표 형식 데이터가 있는 스프레드시트 파일을 업로드하여 기존 클라이언트 목록을 Carepatron으로 가져올 수 있습니다.',
	ImportSpreadsheetTitle: '스프레드시트 파일을 가져오세요',
	ImportTemplates: '템플릿 가져오기',
	Importing: '수입',
	ImportingCalendarProductEvents: '{product} 이벤트 가져오기',
	ImportingData: '데이터 가져오기',
	ImportingSpreadsheetDescription: '이 작업을 완료하는 데 최대 1분 정도 걸립니다.',
	ImportingSpreadsheetTitle: '스프레드시트 가져오기',
	ImportsInProgress: '진행 중인 가져오기',
	InPersonMeeting: '대면 회의',
	InProgress: '진행중',
	InTransit: '이동 중',
	InTransitTooltip:
		'In Transit 잔액에는 Stripe에서 귀하의 은행 계좌로 지불된 모든 송장 지급이 포함됩니다. 이러한 자금은 일반적으로 정산하는 데 3-5일이 걸립니다.',
	Inactive: '비활성',
	InboundOrOutboundCalls: '인바운드 또는 아웃바운드 통화',
	Inbox: '받은 편지함',
	InboxAccessRestricted: '접근이 제한되었습니다. 권한은 받은 편지함 소유자에게 문의하세요.',
	InboxAccountAlreadyConnected: '연결하려고 시도한 채널은 이미 Carepatron에 연결되어 있습니다.',
	InboxAddAttachments: '첨부파일 추가',
	InboxAreYouSureDeleteMessage: '이 메시지를 삭제하시겠습니까?',
	InboxBulkCloseSuccess:
		'{count, plural, one {대화 #개를 성공적으로 마감했습니다} other {대화 #개를 성공적으로 마감했습니다}}',
	InboxBulkComposeModalTitle: '대량 메시지 작성',
	InboxBulkDeleteSuccess: '{count, plural, one {대화 #개 삭제 완료} other {대화 #개 삭제 완료}}',
	InboxBulkReadSuccess:
		'{count, plural, one {대화 #개를 읽은 것으로 표시했습니다} other {대화 #개를 읽은 것으로 표시했습니다}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {대화 #개를 성공적으로 다시 열었습니다} other {대화 #개를 성공적으로 다시 열었습니다}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {대화 #개를 읽지 않음으로 표시했습니다} other {대화 #개를 읽지 않음으로 표시했습니다}}',
	InboxChatCreateGroup: '그룹 만들기',
	InboxChatDeleteGroupModalDescription: '이 그룹을 삭제하시겠습니까? 모든 메시지와 첨부 파일이 삭제됩니다.',
	InboxChatDeleteGroupModalTitle: '그룹 삭제',
	InboxChatDiscardDraft: '초안 삭제',
	InboxChatDragDropText: '여기에 파일을 드롭하여 업로드하세요.',
	InboxChatGroupConversation: '그룹 대화',
	InboxChatGroupCreateModalDescription: '팀, 고객 또는 커뮤니티와 메시지 및 협업을 위한 새 그룹을 시작하세요.',
	InboxChatGroupCreateModalTitle: '그룹 만들기',
	InboxChatGroupMembers: '그룹 멤버',
	InboxChatGroupModalGroupNameFieldLabel: '그룹 이름',
	InboxChatGroupModalGroupNameFieldPlaceholder: '예: 고객 지원, 관리자',
	InboxChatGroupModalGroupNameFieldRequired: '이 필드는 필수입니다',
	InboxChatGroupModalMembersFieldErrorMinimumOne: '최소 한 명의 멤버가 필요합니다.',
	InboxChatGroupModalMembersFieldLabel: '그룹 멤버를 선택하세요',
	InboxChatGroupModalMembersFieldPlaceholder: '멤버 선택',
	InboxChatGroupUpdateModalTitle: '그룹 관리',
	InboxChatLeaveGroup: '그룹 나가기',
	InboxChatLeaveGroupModalDescription:
		'이 그룹을 정말로 나가시겠습니까? 더 이상 메시지나 업데이트를 받지 못하게 됩니다.',
	InboxChatLeaveGroupModalTitle: '그룹 나가기',
	InboxChatLeftGroupMessage: '왼쪽 그룹 메시지',
	InboxChatManageGroup: '그룹 관리',
	InboxChatSearchParticipants: '수신자 선택',
	InboxCloseConversationSuccess: '대화를 성공적으로 종료했습니다.',
	InboxCompose: '구성하다',
	InboxComposeBulk: '대량 메시지',
	InboxComposeCarepatronChat: '전령',
	InboxComposeChat: '챗 작성',
	InboxComposeDisabledNoConnection: '이메일 계정을 연결하여 메시지를 보내세요',
	InboxComposeDisabledNoPermissionTooltip: '이 받은 편지함에서 메시지를 보낼 수 있는 권한이 없습니다.',
	InboxComposeEmail: '이메일 작성',
	InboxComposeMessageFrom: '에서',
	InboxComposeMessageRecipientBcc: '숨은 참조',
	InboxComposeMessageRecipientCc: '참조',
	InboxComposeMessageRecipientTo: '에게',
	InboxComposeMessageSubject: '주제:',
	InboxConnectAccountButton: '이메일을 연결하세요',
	InboxConnectedDescription: '귀하의 받은 편지함에 통신 내용이 없습니다.',
	InboxConnectedHeading: '통신을 시작하자마자 대화 내용이 여기에 표시됩니다.',
	InboxConnectedHeadingClientView: '고객 커뮤니케이션을 간소화하세요',
	InboxCreateFirstInboxButton: '첫 번째 받은 편지함을 만들어보세요',
	InboxCreationSuccess: '받은 편지함이 성공적으로 생성되었습니다.',
	InboxDeleteAttachment: '첨부 파일 삭제',
	InboxDeleteConversationSuccess: '대화가 성공적으로 삭제되었습니다',
	InboxDeleteMessage: '메시지를 삭제하시겠습니까?',
	InboxDirectMessage: '다이렉트 메시지',
	InboxEditDraft: '초안 편집',
	InboxEmailComposeReplyEmail: '답글 작성',
	InboxEmailDraft: '초안',
	InboxEmailNotFound: '이메일을 찾을 수 없습니다',
	InboxEmailSubjectFieldInformation: '제목을 변경하면 새로운 스레드 이메일이 생성됩니다.',
	InboxEmptyArchiveDescription: '보관된 대화가 발견되지 않았습니다.',
	InboxEmptyBinDescription: '삭제된 대화가 발견되지 않았습니다.',
	InboxEmptyBinHeading: '모두 정상입니다. 여기에는 볼 것이 없습니다.',
	InboxEmptyBinSuccess: '대화를 성공적으로 삭제했습니다',
	InboxEmptyCongratsHeading: '잘했어요! 다음 대화까지 앉아서 편히 쉬세요',
	InboxEmptyDraftDescription: '초안 대화가 발견되지 않았습니다.',
	InboxEmptyDraftHeading: '모두 정상입니다. 여기에는 볼 것이 없습니다.',
	InboxEmptyOtherDescription: '다른 대화는 발견되지 않았습니다.',
	InboxEmptyScheduledHeading: '모두 정상, 전송 예정된 대화 없음',
	InboxEmptySentDescription: '보낸 대화가 발견되지 않았습니다.',
	InboxForward: '앞으로',
	InboxGroupClientsLabel: '모든 클라이언트',
	InboxGroupClientsOverviewLabel: '고객',
	InboxGroupClientsSelectedItemPrefix: '고객',
	InboxGroupStaffsLabel: '모든 팀',
	InboxGroupStaffsOverviewLabel: '팀',
	InboxGroupStaffsSelectedItemPrefix: '팀',
	InboxGroupStatusLabel: '모든 상태',
	InboxGroupStatusOverviewLabel: '상태로 보내기',
	InboxGroupStatusSelectedItemPrefix: '상태',
	InboxGroupTagsLabel: '모든 태그',
	InboxGroupTagsOverviewLabel: '태그로 보내기',
	InboxGroupTagsSelectedItemPrefix: '꼬리표',
	InboxHideQuotedText: '인용된 텍스트 숨기기',
	InboxIgnoreConversationSuccess: '대화를 성공적으로 무시했습니다.',
	InboxMessageAllLabelRecipientsCount: '모든 {label} 수신자({count})',
	InboxMessageBodyPlaceholder: '메시지를 추가하세요',
	InboxMessageDeleted: '메시지가 삭제되었습니다',
	InboxMessageMarkedAsRead: '메시지가 읽음으로 표시됨',
	InboxMessageMarkedAsUnread: '메시지가 읽지 않은 것으로 표시됨',
	InboxMessageSentViaChat: '<strong>채팅으로 보냄</strong>  • {time} {name}님',
	InboxMessageShowMoreRecipients: '+{count} 개 더',
	InboxMessageWasDeleted: '이 메시지는 삭제되었습니다.',
	InboxNoConnectionDescription: '이메일 계정을 연결하거나 여러 이메일이 있는 받은 편지함을 만드세요',
	InboxNoConnectionHeading: '고객 커뮤니케이션을 통합하세요',
	InboxNoDirectMessage: '최근 메시지 없음',
	InboxRecentConversations: '최근',
	InboxReopenConversationSuccess: '대화가 성공적으로 다시 열렸습니다.',
	InboxReply: '회신하다',
	InboxReplyAll: '모두에게 답장하기',
	InboxRestoreConversationSuccess: '대화가 성공적으로 복구되었습니다.',
	InboxScheduleSendCancelSendSuccess: '예약된 보내기가 취소되고 메시지가 초안으로 돌아갔습니다.',
	InboxScheduleSendMessageSuccessDescription: '{date}에 예약된 발송',
	InboxScheduleSendMessageSuccessTitle: '일정 보내기',
	InboxSearchForConversations: '"{query}"를 검색하세요.',
	InboxSendMessageSuccess: '대화를 성공적으로 보냈습니다',
	InboxSettings: '받은 편지함 설정',
	InboxSettingsAppsDesc: '이 공유 받은 편지함에 연결된 앱을 관리합니다. 필요에 따라 연결을 추가하거나 제거합니다.',
	InboxSettingsAppsNewConnectedApp: '새로운 연결 앱',
	InboxSettingsAppsTitle: '연결된 앱',
	InboxSettingsDeleteAccountFailed: '받은 편지함 계정을 삭제하지 못했습니다.',
	InboxSettingsDeleteAccountSuccess: '받은 편지함 계정이 성공적으로 삭제되었습니다.',
	InboxSettingsDeleteAccountWarning:
		'{email}을 제거하면 {inboxName} 받은 편지함에서 연결이 해제되고 메시지 동기화가 중지됩니다.',
	InboxSettingsDeleteInboxFailed: '받은 편지함 삭제에 실패했습니다',
	InboxSettingsDeleteInboxSuccess: '받은 편지함을 성공적으로 삭제했습니다',
	InboxSettingsDeleteInboxWarning:
		'{inboxName}을 삭제하면 연결된 모든 채널이 연결 해제되고 이 인박스와 관련된 모든 메시지가 삭제됩니다.		이 작업은 영구적이며 취소할 수 없습니다.',
	InboxSettingsDetailsDesc: '고객 메시지를 효율적으로 관리할 수 있는 팀의 커뮤니케이션 받은 편지함입니다.',
	InboxSettingsDetailsTitle: '받은 편지함 세부 정보',
	InboxSettingsEmailSignatureLabel: '이메일 서명 기본값',
	InboxSettingsReplyFormatDesc:
		'이메일을 보낸 사람에 관계없이 기본 회신 주소와 이메일 서명이 일관되게 표시되도록 설정하세요.',
	InboxSettingsReplyFormatTitle: '답변 형식',
	InboxSettingsSendFromLabel: '기본 회신을 설정하세요 ',
	InboxSettingsStaffDesc: '원활한 협업을 위해 이 공유 받은 편지함에 대한 팀원의 액세스를 관리하세요.',
	InboxSettingsStaffTitle: '팀원 배정하기',
	InboxSettingsUpdateInboxDetailsFailed: '받은 편지함 세부 정보를 업데이트하지 못했습니다.',
	InboxSettingsUpdateInboxDetailsSuccess: '받은 편지함 세부 정보가 성공적으로 업데이트되었습니다.',
	InboxSettingsUpdateInboxStaffsFailed: '받은 편지함 팀원을 업데이트하는 데 실패했습니다.',
	InboxSettingsUpdateInboxStaffsSuccess: '성공적으로 받은 편지함 팀원들에게 업데이트되었습니다.',
	InboxSettingsUpdateReplyFormatFailed: '답변 형식을 업데이트하지 못했습니다.',
	InboxSettingsUpdateReplyFormatSuccess: '답변 형식이 성공적으로 업데이트되었습니다.',
	InboxShowQuotedText: '인용된 텍스트 표시',
	InboxStaffRoleAdminDescription: '받은 편지함 보기, 답장 및 관리',
	InboxStaffRoleResponderDescription: '보기 및 답변',
	InboxStaffRoleViewerDescription: '보기만',
	InboxSuggestMoveToBulkComposeMessageActionCancel: '편집 계속하기',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: '네, 대량 전송으로 전환합니다.',
	InboxSuggestMoveToBulkComposeMessageContent:
		'{count}명 이상의 수신자를 선택했습니다. 대량 이메일로 보내시겠습니까?',
	InboxSuggestMoveToBulkComposeMessageTitle: '경고',
	InboxSwitchToOtherInbox: '다른 받은 편지함으로 전환',
	InboxUndoSendMessageSuccess: '취소된 것을 보냅니다',
	IncludeLineItems: '라인 항목 포함',
	IncludeSalesTax: '과세 대상',
	IncludesAiSmartPrompt: 'AI 스마트 프롬프트 포함',
	Incomplete: '불완전한',
	IncreaseIndent: '들여쓰기 늘리기',
	IndianHealthServiceFreeStandingFacility: '인디언 건강 서비스 독립형 시설',
	IndianHealthServiceProviderFacility: '인디언 건강 서비스 제공자 기반 시설',
	Information: '정보',
	InitialAssessment: '초기 평가',
	InitialSignupPageClientFamilyTitle: '고객 또는 가족 구성원',
	InitialSignupPageProviderTitle: '건강 ',
	InitialTreatment: '초기 치료',
	Initials: '머리 글자',
	InlineEmbed: '인라인 임베드',
	InputPhraseToConfirm: '확인하려면 {confirmationPhrase}를 입력하십시오.',
	Insert: '끼워 넣다',
	InsertTable: '테이블 삽입',
	InstallCarepatronOnYourIphone1: 'iOS에 Carepatron 설치: 탭하세요.',
	InstallCarepatronOnYourIphone2: '그런 다음 홈 화면에 추가',
	InsufficientCalendarScopesSnackbar: '동기화에 실패했습니다. Carepatron에 캘린더 권한을 허용해 주세요.',
	InsufficientInboxScopesSnackbar: '동기화에 실패했습니다. Carepatron에 이메일 권한을 허용해 주세요.',
	InsufficientScopeErrorCodeSnackbar: '동기화에 실패했습니다. Carepatron에 대한 모든 권한을 허용해 주세요.',
	Insurance: '보험',
	InsuranceAmount: '보험 금액',
	InsuranceClaim: '보험 청구',
	InsuranceClaimAiChatPlaceholder: '보험 청구에 대해 문의하십시오...',
	InsuranceClaimAiClaimNumber: '청구 {number}',
	InsuranceClaimAiSubtitle: '보험 청구 • 청구 검증',
	InsuranceClaimDeniedSubject: 'Claim {claimNumber}이(가) {payerNumber} {payerName}에 제출되었으나 거부되었습니다.',
	InsuranceClaimErrorDescription:
		'청구서에 지불자 또는 청구소에서 보고된 오류가 포함되어 있습니다. 다음 오류 메시지를 검토하고 청구서를 다시 제출하십시오.',
	InsuranceClaimErrorGuideLink: '보험 청구 가이드',
	InsuranceClaimErrorTitle: '청구 제출 오류',
	InsuranceClaimNotFound: '보험 청구를 찾을 수 없습니다',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {{paymentAmount}의 부분 결제가 기록되었습니다} other {{paymentAmount} 결제가 기록되었습니다}} 청구 {claimNumber}에 대해 {payerNumber} {payerName}에 의해',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber}이(가) {payerNumber} {payerName}에 제출되었으나 거부되었습니다.',
	InsuranceClaims: '보험 청구',
	InsuranceInformation: '보험 정보',
	InsurancePaid: '보험 지급',
	InsurancePayer: '보험금 지불자',
	InsurancePayers: '보험 지불자',
	InsurancePayersDescription: '계정에 추가된 지불자를 보고 등록을 관리하세요.',
	InsurancePayment: '보험금 지급',
	InsurancePoliciesDetailsSubtitle: '청구를 뒷받침하기 위해 고객 보험 정보를 추가합니다.',
	InsurancePoliciesDetailsTitle: '정책 세부 정보',
	InsurancePoliciesListSubtitle: '청구를 뒷받침하기 위해 고객 보험 정보를 추가합니다.',
	InsurancePoliciesListTitle: '보험 정책',
	InsuranceSelfPay: '자비 부담',
	InsuranceType: '보험 종류',
	InsuranceUnpaid: '보험 미지급',
	Intake: '섭취',
	IntakeExpiredErrorCodeSnackbar: '이 섭취는 만료되었습니다. 다른 섭취를 다시 보내려면 공급자에게 문의하세요.',
	IntakeNotFoundErrorSnackbar: '이 섭취량을 찾을 수 없습니다. 다른 섭취량을 다시 보내려면 공급자에게 문의하세요.',
	IntakeProcessLearnMoreInstructions: '섭취 양식 설정 가이드',
	IntakeTemplateSelectorPlaceholder: '고객에게 보낼 양식과 계약을 선택하여 완료하세요.',
	Integration: '완성',
	IntenseBlur: '배경을 강하게 흐리게 처리합니다',
	InteriorDesigner: '인테리어 디자이너',
	InternetBanking: '은행 송금',
	Interval: '간격',
	IntervalDays: '간격(일)',
	IntervalHours: '간격(시간)',
	Invalid: '유효하지 않은',
	InvalidDate: '잘못된 날짜',
	InvalidDateFormat: '날짜는 {format} 형식이어야 합니다.',
	InvalidDisplayName: '표시 이름에 {value}를 포함할 수 없습니다.',
	InvalidEmailFormat: '잘못된 이메일 형식입니다',
	InvalidFileType: '잘못된 파일 유형입니다',
	InvalidGTMContainerId: '잘못된 GTM 컨테이너 ID 형식입니다.',
	InvalidPaymentMethodCode: '선택한 결제 수단이 유효하지 않습니다. 다른 결제 수단을 선택하세요.',
	InvalidPromotionCode: '프로모션 코드가 잘못되었습니다.',
	InvalidReferralDescription: '이미 Carepatron을 사용하고 있습니다',
	InvalidStatementDescriptor: `문장 설명자는 5~22자 길이여야 하며 문자, 숫자, 공백만 포함해야 하며 <, >, \\, ', ", *를 포함할 수 없습니다.`,
	InvalidToken: '잘못된 토큰',
	InvalidTotpSetupVerificationCode: '잘못된 인증코드입니다.',
	InvalidURLErrorText: '이는 유효한 URL이어야 합니다.',
	InvalidZoomTokenErrorCodeSnackbar: 'Zoom 토큰이 만료되었습니다. Zoom 앱을 다시 연결하고 다시 시도하세요.',
	Invite: '초대하다',
	InviteRelationships: '관계를 초대하다',
	InviteToPortal: '포털에 초대',
	InviteToPortalModalDescription: '귀하의 고객에게 Carepatron에 가입할 수 있도록 초대 이메일이 전송됩니다.',
	InviteToPortalModalTitle: '{name}님을 Carepatron 포털에 초대합니다.',
	InviteUserDescription: ' ',
	InviteUserTitle: '새로운 사용자 초대',
	Invited: '초대됨',
	Invoice: '송장',
	InvoiceColorPickerDescription: '송장에 사용할 색상 테마',
	InvoiceColorTheme: '송장 색상 테마',
	InvoiceContactDeleted: '송장 연락처가 삭제되어 이 송장을 업데이트할 수 없습니다.',
	InvoiceDate: '발행일',
	InvoiceDetails: '송장 세부 정보',
	InvoiceFieldsPlaceholder: '필드 검색...',
	InvoiceFrom: 'Invoice {number} from {fromProvider}',
	InvoiceInvalidCredit: '잘못된 신용 금액입니다. 신용 금액은 송장 총액을 초과할 수 없습니다.',
	InvoiceNotFoundDescription: '공급업체에 연락하여 자세한 정보를 요청하거나 송장을 다시 보내달라고 요청하세요.',
	InvoiceNotFoundTitle: '송장을 찾을 수 없습니다',
	InvoiceNumber: '송장 {해시태그}',
	InvoiceNumberFormat: '송장 #{number}',
	InvoiceNumberMustEndWithDigit: '송장 번호는 숫자(0-9)로 끝나야 합니다.',
	InvoicePageHeader: '송장',
	InvoicePaidNotificationSubject: '송장 {invoiceNumber} 결제 완료',
	InvoiceReminder: '송장 알림',
	InvoiceReminderSentence: '{deliveryType} 알림을 청구서 만기일 {beforeAfter} {interval} {unit} 전에 보내세요.',
	InvoiceReminderSettings: '송장 알림 설정',
	InvoiceReminderSettingsInfo: '알림은 Carepatron에서 보낸 송장에만 적용됩니다.',
	InvoiceReminders: '송장 알림',
	InvoiceRemindersInfo:
		'송장 납부 기한에 대한 자동 알림을 설정합니다. 알림은 Carepatron을 통해 전송된 송장에만 적용됩니다.',
	InvoiceSettings: '송장 설정',
	InvoiceStatus: '송장 상태',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, USA',
	InvoiceTemplateDescriptionPlaceholder: '대체 지불에 대한 메모, 은행 송금 세부 정보 또는 약관을 추가합니다.',
	InvoiceTemplateEmploymentStatusPlaceholder: '자영업자',
	InvoiceTemplateEthnicityPlaceholder: '코카서스 사람',
	InvoiceTemplateNotFoundDescription: '자세한 내용은 공급업체에 문의하세요.',
	InvoiceTemplateNotFoundTitle: '송장 템플릿을 찾을 수 없습니다',
	InvoiceTemplates: '송장 템플릿',
	InvoiceTemplatesDescription:
		'사용자 친화적인 템플릿을 사용하여 브랜드를 반영하고, 규제 요구 사항을 충족하고, 고객 선호도에 맞게 송장 템플릿을 맞춤 설정하세요.',
	InvoiceTheme: '송장 테마',
	InvoiceTotal: '<h1>인보이스 합계</h1>',
	InvoiceUninvoicedAmounts: '청구되지 않은 금액 청구',
	InvoiceUpdateVersionMessage:
		'이 송장을 편집하려면 최신 버전이 필요합니다. Carepatron을 다시 로드하고 다시 시도하세요.',
	Invoices: '{count, plural, one {송장} other {송장들}}',
	InvoicesEmptyStateDescription: '송장이 발견되지 않았습니다',
	InvoicingAndPayment: '청구서 발송 ',
	Ireland: '아일랜드',
	IsA: '이다',
	IsBetween: '사이에 있습니다',
	IsEqualTo: '와 같다',
	IsGreaterThan: '보다 크다',
	IsGreaterThanOrEqualTo: '보다 크거나 같다',
	IsLessThan: '보다 작습니다',
	IsLessThanOrEqualTo: '보다 작거나 같다',
	IssueCredit: '신용 발급',
	IssueCreditAdjustment: '신용 조정 문제',
	IssueDate: '발행일',
	Italic: '이탤릭체',
	Items: '아이템',
	ItemsAndAdjustments: '항목 및 조정',
	ItemsRemaining: '+{count} 개 남았습니다.',
	JobTitle: '직함',
	Join: '가입하다',
	JoinCall: '통화에 참여하세요',
	JoinNow: '지금 가입하세요',
	JoinProduct: '{product}에 참여하세요',
	JoinVideoCall: '영상통화에 참여하세요',
	JoinWebinar: '웹 세미나 참여',
	JoinWithVideoCall: '{product}에 참여하세요.',
	Journalist: '기자',
	JustMe: '나뿐이야',
	JustYou: '당신뿐',
	Justify: '신이 옳다고 하다',
	KeepSeparate: '따로 보관하세요',
	KeepSeparateSuccessMessage: '{clientNames}에 대한 별도 기록을 성공적으로 유지했습니다.',
	KeepWaiting: '계속 기다리세요',
	Label: '상표',
	LabelOptional: '라벨(선택사항)',
	LactationConsulting: '수유 컨설팅',
	Language: '언어',
	Large: '크기가 큰',
	LastDxCode: '마지막 DX 코드',
	LastLoggedIn: '마지막 로그인 {date} {time}',
	LastMenstrualPeriod: '마지막 월경 기간',
	LastMonth: '전달',
	LastNDays: '지난 {number}일',
	LastName: '성',
	LastNameFirstInitial: '성, 이름의 첫 글자',
	LastWeek: '지난주',
	LastXRay: '마지막 엑스레이',
	LatestVisitOrConsultation: '최근 방문 또는 상담',
	Lawyer: '변호사',
	LearnMore: '더 알아보기',
	LearnMoreTipsToGettingStarted: '시작하는 데 도움이 되는 더 많은 팁을 알아보세요',
	LearnToSetupInbox: '받은 편지함 계정 설정 가이드',
	Leave: '떠나다',
	LeaveCall: '전화 남기기',
	LeftAlign: '왼쪽 정렬',
	LegacyBillingItemsNotAvailable:
		'개별 청구 항목은 아직 이 약속에 대해 사용할 수 없습니다. 일반적으로 청구할 수 있습니다.',
	LegacyBillingItemsNotAvailableTitle: '레거시 결제',
	LegalAndConsent: '법률 및 동의',
	LegalConsentFormPrimaryText: '법적 동의',
	LegalConsentFormSecondaryText: '옵션 수락 또는 거부',
	LegalGuardian: '법적 보호자',
	Letter: '편지',
	LettersCategoryDescription: '임상 및 관리 서신 작성을 위해',
	Librarian: '사서',
	LicenseNumber: '라이센스 번호',
	LifeCoach: '라이프 코치',
	LifeCoaches: '라이프 코치',
	Limited: '제한됨',
	LineSpacing: '줄 간격 및 문단 간격',
	LinearScaleFormPrimaryText: '선형 스케일',
	LinearScaleFormSecondaryText: '스케일 옵션 1-10',
	Lineitems: '라인 항목',
	Link: '링크',
	LinkClientFormSearchClientLabel: '고객 검색',
	LinkClientModalTitle: '기존 클라이언트에 대한 링크',
	LinkClientSuccessDescription: '**{newName}**의 연락처 정보가 **{existingName}**의 기록에 추가되었습니다.',
	LinkClientSuccessTitle: '기존 연락처에 성공적으로 연결되었습니다',
	LinkForCallCopied: '링크가 복사되었습니다!',
	LinkToAnExistingClient: '기존 클라이언트에 대한 링크',
	LinkToClient: '클라이언트에 대한 링크',
	ListAndTracker: '목록/추적기',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees}는 이 통화에 있습니다}
			other {{attendees}는 이 통화에 있습니다}
		}`,
	ListStyles: '목록 스타일',
	ListsAndTrackersCategoryDescription: '업무를 구성하고 추적하기 위해',
	LivingArrangements: '생활 환경',
	LoadMore: '더 불러오기',
	Loading: '로딩중...',
	LocalizationPanelDescription: '언어 및 시간대에 대한 설정을 관리하세요',
	LocalizationPanelTitle: '언어 및 시간대',
	Location: '위치',
	LocationDescription:
		'구체적인 주소, 객실 이름, 가상 공간 유형을 지정하여 실제 및 가상 위치를 설정하면 약속 일정과 화상 통화를 보다 쉽게 잡을 수 있습니다.',
	LocationNumber: '위치 번호',
	LocationOfService: '서비스 위치',
	LocationOfServiceRecommendedActionInfo: '이 서비스에 특정 위치를 추가하면 이용 가능 여부에 영향을 줄 수 있습니다.',
	LocationRemote: '원격',
	LocationType: '위치 유형',
	Locations: '위치',
	Lock: '잠그다',
	Locked: '잠김',
	LockedNote: '잠긴 노트',
	LogInToSaveOrAuthoriseCard: '카드를 저장하거나 승인하려면 로그인하세요',
	LogInToSaveOrAuthorisePayment: '결제를 저장하거나 승인하려면 로그인하세요',
	Login: '로그인',
	LoginButton: '로그인',
	LoginEmail: '이메일',
	LoginForgotPasswordLink: '비밀번호를 잊으셨나요?',
	LoginPassword: '비밀번호',
	Logo: '심벌 마크',
	LogoutAreYouSure: '이 장치에서 로그아웃하세요.',
	LogoutButton: '로그아웃',
	London: '런던',
	LongTextAnswer: '긴 텍스트 답변',
	LongTextFormPrimaryText: '긴 글',
	LongTextFormSecondaryText: '문단 스타일 옵션',
	Male: '남성',
	Manage: '관리하다',
	ManageAllClientTags: '모든 클라이언트 태그 관리',
	ManageAllNoteTags: '모든 노트 태그 관리',
	ManageAllTemplateTags: '모든 템플릿 태그 관리',
	ManageConnections: '연결 관리',
	ManageConnectionsGmailDescription: '다른 팀원은 동기화된 Gmail을 볼 수 없습니다.',
	ManageConnectionsGoogleCalendarDescription:
		'다른 팀원은 동기화된 캘린더를 볼 수 없습니다. 클라이언트 약속은 Carepatron 내에서만 업데이트하거나 삭제할 수 있습니다.',
	ManageConnectionsInboxSyncHelperText: '받은 편지함 동기화 설정을 관리하려면 받은 편지함 페이지로 이동하세요.',
	ManageConnectionsMicrosoftCalendarDescription:
		'다른 팀원은 동기화된 캘린더를 볼 수 없습니다. 고객 약속은 Carepatron 내에서만 업데이트하거나 삭제할 수 있습니다.',
	ManageConnectionsOutlookDescription: '다른 팀원은 동기화된 Microsoft Outlook을 볼 수 없습니다.',
	ManageInboxAccountButton: '새로운 받은 편지함',
	ManageInboxAccountEdit: '받은 편지함 관리',
	ManageInboxAccountPanelTitle: '받은 편지함',
	ManageInboxAssignTeamPlaceholder: '받은 편지함에 액세스할 팀원을 선택하세요',
	ManageInboxBasicInfoColor: '색상',
	ManageInboxBasicInfoDescription: '설명',
	ManageInboxBasicInfoDescriptionPlaceholder: '당신이나 당신의 팀은 이 받은편지함을 어떤 용도로 사용할 예정인가요?',
	ManageInboxBasicInfoName: '받은 편지함 이름',
	ManageInboxBasicInfoNamePlaceholder: '예: 고객 지원, 관리자',
	ManageInboxConnectAppAlreadyConnectedError: '연결하려고 시도한 채널은 이미 Carepatron에 연결되어 있습니다.',
	ManageInboxConnectAppConnect: '연결하다',
	ManageInboxConnectAppConnectedInfo: '계정에 연결됨',
	ManageInboxConnectAppContinue: '계속하다',
	ManageInboxConnectAppEmail: '이메일',
	ManageInboxConnectAppSignInWith: '로 로그인',
	ManageInboxConnectAppSubtitle:
		'모든 커뮤니케이션을 한곳에서 원활하게 보내고 받고 추적할 수 있도록 앱을 연결하세요.',
	ManageInboxNewInboxTitle: '새로운 받은 편지함',
	ManagePlan: '계획 관리',
	ManageProfile: '프로필 관리',
	ManageReferralsModalDescription: '당사의 헬스케어 플랫폼을 널리 알리고 보상을 받으세요.',
	ManageReferralsModalTitle: '친구를 추천하고 보상을 받으세요!',
	ManageStaffRelationshipsAddButton: '관계 관리',
	ManageStaffRelationshipsEmptyStateText: '관계가 추가되지 않았습니다',
	ManageStaffRelationshipsModalDescription:
		'클라이언트를 선택하면 새로운 관계가 추가되고, 클라이언트를 선택 취소하면 기존 관계가 제거됩니다.',
	ManageStaffRelationshipsModalTitle: '관계 관리',
	ManageStatuses: '상태 관리',
	ManageStatusesActiveStatusHelperText: '최소한 하나의 활성 상태가 필요합니다.',
	ManageStatusesDescription: '상태 라벨을 사용자 지정하고 작업 흐름에 맞는 색상을 선택하세요.',
	ManageStatusesSuccessSnackbar: '상태가 성공적으로 업데이트되었습니다.',
	ManageTags: '태그 관리',
	ManageTaskAttendeeStatus: '약속 상태 관리',
	ManageTaskAttendeeStatusDescription: '<p>예약 상태를 사용자 지정하여 워크플로우에 맞추세요.</p>',
	ManageTaskAttendeeStatusHelperText: '최소한 하나의 상태가 필요합니다.',
	ManageTaskAttendeeStatusSubtitle: '사용자 지정 상태',
	ManagedClaimMd: 'Claim.MD',
	Manual: '수동',
	ManualAppointment: '수동 예약',
	ManualPayment: '수동 결제',
	ManuallyTypeLocation: '수동으로 위치를 입력하세요',
	MapColumns: '지도 열',
	MappingRequired: '매핑 필요',
	MarkAllAsRead: '모두 읽음으로 표시',
	MarkAsCompleted: '완료로 표시',
	MarkAsManualSubmission: '제출 완료',
	MarkAsPaid: '지불됨으로 표시',
	MarkAsRead: '읽음으로 표시',
	MarkAsUnpaid: '미납으로 표시',
	MarkAsUnread: '읽지 않음으로 표시',
	MarkAsVoid: '무효로 표시',
	Marker: '채점자',
	MarketingManager: '마케팅 매니저',
	MassageTherapist: '마사지 치료사',
	MassageTherapists: '마사지 치료사',
	MassageTherapy: '마사지 치료',
	MaxBookingTimeDescription1: '클라이언트는 최대 일정을 정할 수 있습니다.',
	MaxBookingTimeDescription2: '미래로',
	MaxBookingTimeLabel: '{timePeriod} 전에',
	MaxCapacity: '최대 용량',
	Maximize: '최대화하다',
	MaximumAttendeeLimit: '최대 한도',
	MaximumBookingTime: '최대 예약 시간',
	MaximumBookingTimeError: '최대 예약 시간은 {valueUnit}을 초과할 수 없습니다.',
	MaximumMinimizedPanelsReachedDescription:
		'한 번에 최대 {count}개의 사이드 패널을 최소화할 수 있습니다. 계속하면 가장 먼저 최소화된 패널이 닫힙니다. 계속하시겠습니까?',
	MaximumMinimizedPanelsReachedTitle: '너무 많은 패널이 열려 있습니다.',
	MechanicalEngineer: '기계 엔지니어',
	MediaGallery: '미디어 갤러리',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: '메디케이드 제공자 번호',
	MedicalAssistant: '의료 보조원',
	MedicalCoder: '의료 코더',
	MedicalDoctor: '의사',
	MedicalIllustrator: '의료 일러스트레이터',
	MedicalInterpreter: '의료 통역사',
	MedicalTechnologist: '의료 기술자',
	Medicare: 'Medicare',
	MedicareProviderNumber: '메디케어 제공자 번호',
	Medicine: '약',
	Medium: '중간',
	Meeting: '회의',
	MeetingEnd: '회의 종료',
	MeetingEnded: '회의가 종료되었습니다',
	MeetingHost: '회의 호스트',
	MeetingLowerHand: '하강하다',
	MeetingOpenChat: '오픈채팅',
	MeetingPersonRaisedHand: '{name}는 손을 들었습니다.',
	MeetingRaiseHand: '손을 들어보세요',
	MeetingReady: '회의 준비 완료',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {분} other {분}} {status}',
	Meetings: '회의',
	MemberId: '회원ID',
	MentalHealth: '정신 건강',
	MentalHealthPractitioners: '정신 건강 전문가',
	MentalHealthProfessional: '정신 건강 전문가',
	Merge: '병합',
	MergeClientRecords: '클라이언트 레코드 병합',
	MergeClientRecordsDescription: '클라이언트 레코드를 병합하면 다음을 포함한 모든 데이터가 결합됩니다.',
	MergeClientRecordsDescription2: '병합을 계속하시겠습니까? 이 작업은 취소할 수 없습니다.',
	MergeClientRecordsItem1: '참고 자료 및 문서',
	MergeClientRecordsItem2: '예약',
	MergeClientRecordsItem3: '송장',
	MergeClientRecordsItem4: '대화',
	MergeClientsSuccess: '클라이언트 기록 병합 성공',
	MergeLimitExceeded: '한 번에 최대 4명의 고객만 병합할 수 있습니다.',
	Message: '메시지',
	MessageAttachments: '{total} 개의 첨부 파일',
	Method: '방법',
	MfaAvailabilityDisclaimer:
		'MFA는 이메일과 비밀번호 로그인에만 사용할 수 있습니다. MFA 설정을 변경하려면 이메일과 비밀번호를 사용하여 로그인하세요.',
	MfaDeviceLostPanelDescription: '혹은 이메일로 코드를 받아 신원을 확인할 수도 있습니다.',
	MfaDeviceLostPanelTitle: 'MFA 기기를 분실하셨나요?',
	MfaDidntReceiveEmailCode: '코드를 받지 못하셨나요? 지원팀에 문의하세요',
	MfaEmailOtpSendFailureSnackbar: '이메일 OTP를 전송하는 데 실패했습니다.',
	MfaEmailOtpSentSnackbar: '{maskedEmail}로 코드가 발송되었습니다.',
	MfaEmailOtpVerificationFailedSnackbar: '이메일 OTP를 확인하지 못했습니다.',
	MfaHasBeenSetUpText: 'MFA를 설정했습니다',
	MfaPanelDescription:
		'다중 인증(MFA)을 활성화하여 계정을 보호하여 추가 보호 계층을 구축하세요. 보조적인 방법을 통해 신원을 확인하여 무단 접근을 방지하세요.',
	MfaPanelNotAuthorizedError: '사용자 이름으로 로그인해야 합니다. ',
	MfaPanelRecommendationDescription:
		'최근에 신원을 확인하기 위해 대체 방법을 사용하여 로그인했습니다. 계정을 안전하게 유지하려면 새 MFA 장치를 설정하는 것을 고려하세요.',
	MfaPanelRecommendationTitle: '<strong>권장 사항:</strong> MFA 장치 업데이트',
	MfaPanelTitle: '다중 인증 요소(MFA)',
	MfaPanelVerifyEmailFirstAlert: 'MFA 설정을 업데이트하려면 먼저 이메일을 확인해야 합니다.',
	MfaRecommendationBannerDescription:
		'최근에 신원을 확인하기 위해 대체 방법을 사용하여 로그인했습니다. 계정을 안전하게 유지하려면 새 MFA 장치를 설정하는 것을 고려하세요.',
	MfaRecommendationBannerPrimaryAction: 'MFA 설정',
	MfaRecommendationBannerTitle: '추천',
	MfaRemovedSnackbarTitle: 'MFA가 제거되었습니다.',
	MfaSendEmailCode: '코드 보내기',
	MfaVerifyIdentityLostDeviceButton: 'MFA 장치에 대한 액세스 권한을 잃었습니다.',
	MfaVerifyYourIdentityPanelDescription: '인증 앱에서 코드를 확인하고 아래에 입력하세요.',
	MfaVerifyYourIdentityPanelTitle: '본인 확인',
	MicCamWarningMessage: '브라우저 주소창에서 차단된 아이콘을 클릭하여 카메라와 마이크의 차단을 해제하세요.',
	MicCamWarningTitle: '카메라와 마이크가 차단되었습니다',
	MicOff: '마이크가 꺼져 있습니다',
	MicOn: '마이크가 켜져 있습니다',
	MicSource: '마이크 소스',
	MicWarningMessage: '마이크에서 문제가 감지되었습니다.',
	Microphone: '마이크로폰',
	MicrophonePermissionBlocked: '마이크 액세스 차단',
	MicrophonePermissionBlockedDescription: '녹음을 시작하려면 마이크 권한을 업데이트하세요.',
	MicrophonePermissionError: '계속하려면 브라우저 설정에서 마이크 권한을 부여하세요.',
	MicrophonePermissionPrompt: '계속하려면 마이크 액세스를 허용하세요.',
	Microsoft: '마이크로소프트',
	MicrosoftCalendar: '마이크로소프트',
	MicrosoftColor: 'Outlook 일정 색상',
	MicrosoftOutlook: '마이크로소프트 아웃룩',
	MicrosoftTeams: '마이크로소프트 팀',
	MiddleEast: '중동',
	MiddleName: '중간 이름',
	MiddleNames: '중간 이름',
	Midwife: '산파',
	Midwives: '조산사',
	Milan: '밀라노',
	MinBookingTimeDescription1: '클라이언트는 일정을 정할 수 없습니다.',
	MinBookingTimeDescription2: '약속 시작 시간',
	MinBookingTimeLabel: '{timePeriod} 약속 전',
	MinCancellationTimeEditModeDescription: '고객이 패널티 없이 취소할 수 있는 시간을 설정합니다.',
	MinCancellationTimeUnset: '최소 취소 시간 설정 없음',
	MinCancellationTimeViewModeDescription: '패널티 없는 취소 기간',
	MinMaxBookingTimeUnset: '시간 설정 없음',
	Minimize: '최소화하다',
	MinimizeConfirmationDescription:
		'활성화된 축소된 패널이 있습니다. 계속하면 닫히고 저장되지 않은 데이터가 손실될 수 있습니다.',
	MinimizeConfirmationTitle: '축소된 패널을 닫겠습니까?',
	MinimumBookingTime: '최소 예약 시간',
	MinimumCancellationTime: '최소 취소 시간',
	MinimumPaymentError: '온라인 결제는 {minimumAmount} 이상부터 가능합니다.',
	MinuteAbbreviated: '분',
	MinuteAbbreviation: '{count} {count, plural, one {분} other {분}}',
	Minutely: '분 단위로',
	MinutesPlural: '{age, plural, one {# 분} other {# 분}}',
	MiscellaneousInformation: '기타 정보',
	MissingFeatures: '누락된 기능',
	MissingPaymentMethod: '더 많은 직원을 추가하려면 구독에 결제 방법을 추가하세요.',
	MobileNumber: '휴대폰 번호',
	MobileNumberOptional: '휴대폰 번호 (선택사항)',
	Modern: '현대의',
	Modifiers: '수정자',
	ModifiersPlaceholder: '수정자',
	Monday: '월요일',
	Month: '월',
	Monthly: '월간 간행물',
	MonthlyCost: '월별 비용',
	MonthlyOn: '매월 {date}일에',
	MonthsPlural: '{age, plural, one {# 개월} other {# 개월}}',
	More: '더',
	MoreActions: '더 많은 작업',
	MoreSettings: '더 많은 설정',
	MoreThanTen: '10 ',
	MostCommonlyUsed: '가장 일반적으로 사용되는',
	MostDownloaded: '가장 많이 다운로드됨',
	MostPopular: '가장 인기 있는',
	Mother: '어머니',
	MotherInLaw: '시어머니',
	MoveDown: '아래로 이동',
	MoveInboxConfirmationDescription: '이 앱 연결을 다시 할당하면 **{currentInboxName}** 받은 편지함에서 삭제됩니다.',
	MoveTemplateToFolder: '`{templateTitle}`을 이동합니다.',
	MoveTemplateToFolderSuccess: '{templateTitle}이(가) {folderTitle}로 이동되었습니다.',
	MoveTemplateToIntakeFolderSuccessMessage: '성공적으로 기본 접수 폴더로 이동되었습니다.',
	MoveTemplateToNewFolder: '이 항목을 이동할 새 폴더를 만듭니다.',
	MoveToChosenFolder: '이 항목을 옮길 폴더를 선택하십시오. 필요한 경우 새 폴더를 만들 수 있습니다.',
	MoveToFolder: '폴더로 이동',
	MoveToInbox: '받은 편지함으로 이동',
	MoveToNewFolder: '새로운 폴더로 이동',
	MoveToSelectedFolder: '이동되면 해당 폴더 아래로 정리되며 현재 위치에서 더 이상 보이지 않습니다.',
	MoveUp: '위로 이동',
	MultiSpeciality: '다양한 전문 분야',
	MultipleChoiceFormPrimaryText: '다중 선택',
	MultipleChoiceFormSecondaryText: '다양한 옵션을 선택하세요',
	MultipleChoiceGridFormPrimaryText: '다중 선택 그리드',
	MultipleChoiceGridFormSecondaryText: '매트릭스에서 옵션을 선택하세요',
	Mumbai: '뭄바이',
	MusicTherapist: '음악 치료사',
	MustContainOneLetterError: '최소한 한 글자 이상 포함되어야 합니다.',
	MustEndWithANumber: '숫자로 끝나야 합니다',
	MustHaveAtLeastXItems: '최소 {count, plural, one {# 항목} other {# 항목}}이 있어야 합니다.',
	MuteAudio: '오디오 음소거',
	MuteEveryone: '모든 사람 음소거',
	MyAvailability: '내 일정',
	MyGallery: '내 갤러리',
	MyPortal: '내 포털',
	MyRelationships: '내 관계',
	MyTemplates: '팀 템플릿',
	MyofunctionalTherapist: '근육기능 치료사',
	NCalifornia: '북부 캘리포니아',
	NPI: '국립과학수사연구원',
	NVirginia: '버지니아 북부',
	Name: '이름',
	NameIsRequired: '이름이 필요합니다',
	NameMustNotBeAWebsite: '이름은 웹사이트가 될 수 없습니다.',
	NameMustNotBeAnEmail: '이름은 이메일이 될 수 없습니다.',
	NameMustNotContainAtSign: '이름에는 @ 기호가 포함될 수 없습니다.',
	NameMustNotContainHTMLTags: '이름에는 HTML 태그가 포함될 수 없습니다.',
	NameMustNotContainSpecialCharacters: '이름에는 특수문자가 포함될 수 없습니다.',
	NameOnCard: '카드에 적힌 이름',
	NationalProviderId: '국가 공급자 식별자(NPI)',
	NaturopathicDoctor: '자연요법 의사',
	NavigateToPersonalSettings: '프로필',
	NavigateToSubscriptionSettings: '구독 설정',
	NavigateToWorkspaceSettings: '워크스페이스 설정',
	NavigateToYourTeam: '팀 관리',
	NavigationDrawerBilling: '청구',
	NavigationDrawerBillingInfo: '결제 정보, 송장 및 Stripe',
	NavigationDrawerCommunication: '의사소통',
	NavigationDrawerCommunicationInfo: '알림 및 템플릿',
	NavigationDrawerInsurance: '보험',
	NavigationDrawerInsuranceInfo: '보험금 지급자와 청구',
	NavigationDrawerInvoices: '청구',
	NavigationDrawerPersonal: '내 프로필',
	NavigationDrawerPersonalInfo: '귀하의 개인 정보',
	NavigationDrawerProfile: '윤곽',
	NavigationDrawerProviderSettings: '설정',
	NavigationDrawerScheduling: '스케줄링',
	NavigationDrawerSchedulingInfo: '서비스 세부 사항 및 예약',
	NavigationDrawerSettings: '설정',
	NavigationDrawerTemplates: '템플릿',
	NavigationDrawerTemplatesV2: '템플릿 V2',
	NavigationDrawerTrash: '쓰레기',
	NavigationDrawerTrashInfo: '삭제된 항목 복원',
	NavigationDrawerWorkspace: '작업 공간 설정',
	NavigationDrawerWorkspaceInfo: '구독 및 작업 공간 정보',
	NegativeBalanceNotSupported: '마이너스 계좌 잔액은 지원되지 않습니다.',
	Nephew: '조카',
	NetworkQualityFair: '공정한 연결',
	NetworkQualityGood: '연결성이 좋습니다',
	NetworkQualityPoor: '연결 불량',
	Neurologist: '신경과 의사',
	Never: '절대',
	New: '새로운',
	NewAppointment: '신임',
	NewClaim: '새로운 청구',
	NewClient: '새로운 고객',
	NewClientNextStepsModalAddAnotherClient: '다른 클라이언트 추가',
	NewClientNextStepsModalBookAppointment: '예약하기',
	NewClientNextStepsModalBookAppointmentDescription: '다가오는 약속을 예약하거나 작업을 생성하세요.',
	NewClientNextStepsModalCompleteBasicInformation: '완전한 고객 기록',
	NewClientNextStepsModalCompleteBasicInformationDescription: '클라이언트 정보를 추가하고 다음 단계를 파악합니다.',
	NewClientNextStepsModalCreateInvoice: '송장 생성',
	NewClientNextStepsModalCreateInvoiceDescription: '고객 결제 정보를 추가하거나 송장을 만드세요.',
	NewClientNextStepsModalCreateNote: '메모 작성 또는 문서 업로드',
	NewClientNextStepsModalCreateNoteDescription: '고객의 메모와 문서를 수집합니다.',
	NewClientNextStepsModalDescription: '이제 클라이언트 레코드를 생성했다면 다음과 같은 작업을 수행해야 합니다.',
	NewClientNextStepsModalSendIntake: '섭취량 보내기',
	NewClientNextStepsModalSendIntakeDescription: '고객 정보를 수집하고 작성 및 서명을 위해 추가 양식을 보냅니다.',
	NewClientNextStepsModalSendMessage: '메시지 보내기',
	NewClientNextStepsModalSendMessageDescription: '메시지를 작성하여 고객에게 보냅니다.',
	NewClientNextStepsModalTitle: '다음 단계',
	NewClientSuccess: '새로운 클라이언트가 성공적으로 생성되었습니다',
	NewClients: '새로운 고객',
	NewConnectedApp: '새로운 연결 앱',
	NewContact: '새로운 연락처',
	NewContactNextStepsModalAddRelationship: '관계 추가',
	NewContactNextStepsModalAddRelationshipDescription: '관련 고객 또는 그룹에 이 연락처를 연결합니다.',
	NewContactNextStepsModalBookAppointment: '예약하기',
	NewContactNextStepsModalBookAppointmentDescription: '다가오는 약속을 예약하거나 작업을 만드세요.',
	NewContactNextStepsModalCompleteProfile: '프로필 완성',
	NewContactNextStepsModalCompleteProfileDescription: '연락처 정보를 추가하고 다음 단계를 기록합니다.',
	NewContactNextStepsModalCreateNote: '메모 작성 또는 문서 업로드',
	NewContactNextStepsModalCreateNoteDescription: '고객 메모 및 문서를 캡처합니다.',
	NewContactNextStepsModalDescription: '연락처를 만드셨으니 이제 다음 작업을 수행하십시오.',
	NewContactNextStepsModalInviteToPortal: '포털 초대',
	NewContactNextStepsModalInviteToPortalDescription: '포털에 액세스할 수 있는 초대를 보내세요.',
	NewContactNextStepsModalTitle: '다음 단계',
	NewContactSuccess: '새로운 연락처가 성공적으로 생성되었습니다.',
	NewDateOverrideButton: '새로운 날짜 재정의',
	NewDiagnosis: '진단 추가',
	NewField: '새로운 분야',
	NewFolder: '새 폴더',
	NewInvoice: '새로운 송장',
	NewLocation: '새로운 위치',
	NewLocationFailure: '새로운 위치를 생성하지 못했습니다.',
	NewLocationSuccess: '새로운 위치를 성공적으로 생성했습니다',
	NewManualPayer: '새로운 수동 지불자',
	NewNote: '새로운 노트',
	NewNoteCreated: '새로운 메모가 성공적으로 생성되었습니다.',
	NewPassword: '새로운 비밀번호',
	NewPayer: '새로운 지불자',
	NewPaymentMethod: '새로운 결제 방법',
	NewPolicy: '새로운 정책',
	NewRelationship: '새로운 관계',
	NewReminder: '새로운 알림',
	NewSchedule: '새로운 일정',
	NewSection: '새로운 섹션',
	NewSectionOld: '새로운 섹션 [이전]',
	NewSectionWithGrid: '그리드가 있는 새로운 섹션',
	NewService: '새로운 서비스',
	NewServiceFailure: '새로운 서비스를 생성하지 못했습니다.',
	NewServiceSuccess: '새로운 서비스가 성공적으로 생성되었습니다',
	NewStatus: '새로운 상태',
	NewTask: '새로운 작업',
	NewTaxRate: '새로운 세율',
	NewTeamMemberNextStepsModalAssignClients: '클라이언트 배정',
	NewTeamMemberNextStepsModalAssignClientsDescription: '팀 구성원에게 특정 고객을 할당합니다.',
	NewTeamMemberNextStepsModalAssignServices: '서비스 할당',
	NewTeamMemberNextStepsModalAssignServicesDescription: '담당 서비스를 관리하고 필요에 따라 가격을 조정합니다.',
	NewTeamMemberNextStepsModalBookAppointment: '예약하기',
	NewTeamMemberNextStepsModalBookAppointmentDescription: '다가오는 약속을 예약하거나 작업을 만드세요.',
	NewTeamMemberNextStepsModalCompleteProfile: '프로필 완성',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'팀 구성원에 대한 세부 정보를 추가하여 프로필을 완성하십시오.',
	NewTeamMemberNextStepsModalDescription: '팀 멤버를 만드셨으니 이제 다음과 같은 작업을 수행하십시오.',
	NewTeamMemberNextStepsModalEditPermissions: '수정 권한',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'권한이 적절히 부여되었는지 확인하기 위해 액세스 수준을 조정합니다.',
	NewTeamMemberNextStepsModalSetAvailability: '사용 가능 시간 설정',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: '일정을 만들 수 있도록 이용 가능 시간을 설정하세요.',
	NewTeamMemberNextStepsModalTitle: '다음 단계',
	NewTemplateFolderDescription: '문서를 정리하기 위해 새 폴더를 만드세요.',
	NewUIUpdateBannerButton: '앱 다시 로드',
	NewUIUpdateBannerTitle: '새로운 업데이트가 준비되었습니다!',
	NewZealand: '뉴질랜드',
	Newest: '최신',
	NewestUnreplied: '최신 미답변',
	Next: '다음',
	NextInvoiceIssueDate: '다음 청구서 발행일',
	NextNDays: '다음 {number} 일',
	Niece: '조카',
	No: '아니요',
	NoAccessGiven: '접근 권한이 없습니다',
	NoActionConfigured: '동작이 구성되지 않았습니다',
	NoActivePolicies: '활성화된 정책이 없습니다',
	NoActiveReferrals: '활성 추천이 없습니다.',
	NoAppointmentsFound: '약속이 발견되지 않았습니다',
	NoAppointmentsHeading: '클라이언트 약속 및 활동 관리',
	NoArchivedPolicies: '보관된 정책이 없습니다',
	NoAvailableTimes: '시간이 없습니다.',
	NoBillingItemsFound: '결제 항목을 찾을 수 없습니다.',
	NoCalendarsSynced: '동기화된 일정이 없습니다',
	NoClaimsFound: '결과가 없습니다.',
	NoClaimsHeading: '환불 청구 제출을 간소화합니다',
	NoClientsHeading: '고객 기록을 모아보세요',
	NoCompletedReferrals: '완료된 추천이 없습니다.',
	NoConnectionsHeading: '고객 커뮤니케이션을 간소화하세요',
	NoContactsGivenAccess: '이 메모에 대한 액세스 권한이 클라이언트나 연락처에 부여되지 않았습니다.',
	NoContactsHeading: '귀하의 진료를 지원하는 사람들과 계속 연락하세요',
	NoCopayOrCoinsurance: '공제금이나 공동보험료 없음',
	NoCustomServiceSchedule: '사용자 정의 일정 설정 없음 - 가용성은 팀원의 가용성에 따라 달라집니다.',
	NoDescription: '설명 없음',
	NoDocumentationHeading: '안전하게 메모를 생성하고 저장하세요',
	NoDuplicateRecordsHeading: '귀하의 클라이언트 레코드에는 중복이 없습니다.',
	NoEffect: '효과 없음',
	NoEnrolmentProfilesFound: '등록된 프로필이 없습니다.',
	NoGlossaryItems: '글로세리 항목 없음',
	NoInvitedReferrals: '초대된 추천인이 없습니다.',
	NoInvoicesFound: '찾을 수 있는 청구서가 없습니다.',
	NoInvoicesHeading: '청구 및 지불을 자동화하세요',
	NoLimit: '제한 없음',
	NoLocationsFound: '위치를 찾을 수 없습니다',
	NoLocationsWillBeAdded: '위치가 추가되지 않습니다.',
	NoNoteFound: '메모를 찾을 수 없습니다.',
	NoPaymentMethods: '저장된 결제 방법이 없습니다. 결제 시 결제 방법을 추가할 수 있습니다.',
	NoPermissionError: '권한이 없습니다',
	NoPermissions: '이 페이지를 볼 수 있는 권한이 없습니다.',
	NoPolicy: '취소 정책이 추가되지 않았습니다',
	NoRecordsHeading: '고객 기록 개인화',
	NoRecordsToDisplay: '표시할 {resource}가 없습니다.',
	NoRelationshipsHeading: '고객을 지원하는 사람들과 계속 연락하세요',
	NoRemindersFound: '알림을 찾을 수 없습니다.',
	NoResultsFound: '검색 결과가 없습니다',
	NoResultsFoundDescription: '검색과 일치하는 항목을 찾을 수 없습니다.',
	NoServicesAdded: '서비스 추가 없음',
	NoServicesApplied: '서비스가 적용되지 않았습니다',
	NoServicesWillBeAdded: '서비스가 추가되지 않습니다.',
	NoTemplate: '저장된 연습 템플릿이 없습니다.',
	NoTemplatesHeading: '나만의 템플릿을 만들어보세요',
	NoTemplatesInFolder: '이 폴더에는 템플릿이 없습니다.',
	NoTitle: '제목 없음',
	NoTrashItemsHeading: '삭제된 항목을 찾을 수 없습니다.',
	NoTriggerConfigured: '트리거가 구성되지 않았습니다',
	NoUnclaimedItemsFound: '청구되지 않은 품목이 발견되지 않았습니다.',
	NonAiTemplates: 'AI가 아닌 템플릿',
	None: '없음',
	NotAvailable: '사용할 수 없음',
	NotCovered: '포함되지 않음',
	NotFoundSnackbar: '리소스를 찾을 수 없습니다.',
	NotRequiredField: '필요하지 않음',
	Note: '메모',
	NoteDuplicateSuccess: '성공적으로 메모를 복제했습니다.',
	NoteEditModeViewSwitcherDescription: '메모 생성 및 편집',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} 님이 {noteTitle} 양식을 제출했습니다.',
	NoteLockSuccess: '{title}은(는) 잠겼습니다.',
	NoteModalAttachmentButton: '첨부파일 추가',
	NoteModalPhotoButton: '사진 추가/캡처',
	NoteModalTrascribeButton: '라이브 오디오를 필사하다',
	NoteResponderModeViewSwitcherDescription: '양식 보내기 및 응답 검토',
	NoteResponderModeViewSwitcherTooltipTitle: '고객을 대신하여 양식에 응답하고 제출하세요',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: '고객으로서 양식을 작성하고 제출하세요',
	NoteUnlockSuccess: '{title}이(가) 잠금 해제되었습니다.',
	NoteViewModeViewSwitcherDescription: '보기 전용 액세스',
	Notes: '노트',
	NotesAndForms: '노트와 양식',
	NotesCategoryDescription: '고객 상호 작용 기록을 위해',
	NothingToSeeHere: '여기에는 볼 것이 없습니다',
	Notification: '공고',
	NotificationIgnoredMessage: '모든 {notificationType} 알림이 무시됩니다.',
	NotificationRestoredMessage: '모든 {notificationType} 알림이 복원되었습니다.',
	NotificationSettingBillingDescription: '고객 결제 업데이트 및 알림에 대한 알림을 받습니다.',
	NotificationSettingBillingTitle: '청구 및 결제',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} 만} other{{channels}} }',
	NotificationSettingClientDocumentationDescription: '고객 결제 업데이트 및 알림에 대한 알림을 받습니다.',
	NotificationSettingClientDocumentationTitle: '클라이언트 및 문서',
	NotificationSettingCommunicationsDescription: '연결된 채널의 받은 편지함과 업데이트에 대한 알림을 받으세요.',
	NotificationSettingCommunicationsTitle: '연락',
	NotificationSettingEmail: '이메일',
	NotificationSettingInApp: '앱 내',
	NotificationSettingPanelDescription: '활동 및 추천에 대해 받고 싶은 알림을 선택하세요.',
	NotificationSettingPanelTitle: '알림 환경 설정',
	NotificationSettingSchedulingDescription: '팀원이나 고객이 약속을 예약, 변경 또는 취소할 때 알림을 받으세요.',
	NotificationSettingSchedulingTitle: '일정 관리',
	NotificationSettingUpdateSuccess: '알림 설정이 성공적으로 업데이트되었습니다',
	NotificationSettingWhereYouReceiveNotifications: '이 알림을 받고 싶은 위치',
	NotificationSettingWorkspaceDescription: '시스템 변경 사항, 문제, 데이터 전송 및 구독 알림에 대한 알림을 받습니다.',
	NotificationSettingWorkspaceTitle: '작업 공간',
	NotificationTemplateUpdateFailed: '알림 템플릿을 업데이트하지 못했습니다.',
	NotificationTemplateUpdateSuccess: '알림 템플릿이 성공적으로 업데이트되었습니다.',
	NotifyAttendeesOfTaskCancellationModalDescription: '참석자에게 취소 알림 이메일을 보내시겠습니까?',
	NotifyAttendeesOfTaskCancellationModalTitle: '취소 보내기',
	NotifyAttendeesOfTaskConfirmationModalDescription: '참석자에게 확인 알림 이메일을 보내시겠습니까?',
	NotifyAttendeesOfTaskConfirmationModalTitle: '확인 보내기',
	NotifyAttendeesOfTaskDeletedModalTitle: '참석자에게 취소 이메일을 보내시겠습니까?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {는} other {는}} 이메일 주소가 없으므로 자동 알림 및 미리 알림을 받지 않습니다.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {는} other {은}} 이메일 주소가 없으므로 자동 알림 및 미리 알림을 받지 않습니다.',
	NotifyAttendeesOfTaskModalTitle: '참석자에게 알림 이메일을 보내시겠습니까?',
	NotifyAttendeesOfTaskSnackbar: '알림 전송 중',
	NuclearMedicineTechnologist: '핵의학 기술자',
	NumberOfClaims: '{number, plural, one {# 청구} other {# 청구}}',
	NumberOfClients: '{number, plural, one {# 고객} other {# 고객들}}',
	NumberOfContacts: '{number, plural, one {# 연락처} other {# 연락처들}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {항목} other {항목}} 발견됨',
	NumberOfErrors: '{count, plural, one {# 오류} other {# 오류들}}',
	NumberOfInvoices: '{number, plural, one {# 청구서} other {# 청구서들}}',
	NumberOfLineitemsToCredit:
		'<mark>{count} {count, plural, one {줄 항목} other {줄 항목들}}</mark>에 대해 크레딧을 발행할 수 있습니다.',
	NumberOfPayments: '{number, plural, one {# 결제} other {# 결제}}',
	NumberOfRelationships: '{number, plural, one {# 관계} other {# 관계들}}',
	NumberOfResources: '{number, plural, one {# 리소스} other {# 리소스들}}',
	NumberOfTeamMembers: '{number, plural, one {# 팀원} other {# 팀원들}}',
	NumberOfTrashItems: '{number, plural, one {# 개} other {# 개}}',
	NumberOfUninvoicedAmounts:
		'<mark>{count} 개의 청구되지 않은 {count, plural, one {금액} other {금액들}}가 청구될 예정입니다.</mark>',
	NumberedList: '번호가 매겨진 목록',
	Nurse: '간호사',
	NurseAnesthetist: '간호사 마취사',
	NurseAssistant: '간호 조수',
	NurseEducator: '간호 교육자',
	NurseMidwife: '간호사 조산사',
	NursePractitioner: '간호사',
	Nurses: '간호사',
	Nursing: '육아',
	Nutritionist: '영양사',
	Nutritionists: '영양사',
	ObstetricianOrGynecologist: '산부인과 의사',
	Occupation: '직업',
	OccupationalTherapist: '작업치료사',
	OccupationalTherapists: '작업 치료사',
	OccupationalTherapy: '작업 요법',
	Occurrences: '발생',
	Of: '~의',
	Ohio: '오하이오',
	OldPassword: '이전 비밀번호',
	OlderMessages: '{count}개의 이전 메시지',
	Oldest: '가장 오래된',
	OldestUnreplied: '가장 오래된 답변 없음',
	On: '~에',
	OnboardingBusinessAgreement: '저와 회사를 대표하여 {businessAssociateAgreement}에 동의합니다.',
	OnboardingLoadingOccupationalTherapist: '<mark>작업치료사</mark> Carepatron 고객의 4분의 1을 차지합니다',
	OnboardingLoadingProfession: 'Carepatron을 사용하고 번창하는 <mark>{profession}</mark>이 많이 있습니다.',
	OnboardingLoadingPsychologist: '<mark>심리학자</mark> Carepatron 고객의 절반 이상을 차지합니다',
	OnboardingLoadingSubtitleFive: '우리의 사명은<mark> 접근 가능한 의료 소프트웨어</mark> 모든 사람에게.',
	OnboardingLoadingSubtitleFour: '<mark>간소화된 건강 소프트웨어</mark> 전세계 10,000명 이상에게 제공됩니다.',
	OnboardingLoadingSubtitleThree: '구하다<mark> 주 1일</mark> Carepatron의 도움으로 관리 업무를 수행합니다.',
	OnboardingLoadingSubtitleTwo: '구하다<mark> 2시간</mark> Carepatron의 도움을 받아 매일 관리 업무를 수행합니다.',
	OnboardingReviewLocationOne: '홀랜드 파크 정신 건강 센터',
	OnboardingReviewLocationThree: '실무 간호사, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: '라이프하우스클리닉',
	OnboardingReviewNameOne: '아눌 P',
	OnboardingReviewNameThree: '앨리스 E',
	OnboardingReviewNameTwo: '클라라 W',
	OnboardingReviewOne:
		'"Carepatron은 사용하기 매우 직관적입니다. 덕분에 우리가 진료소를 잘 운영할 수 있어서 더 이상 관리자 팀이 필요 없습니다."',
	OnboardingReviewThree:
		'"기능과 비용 면에서 제가 사용해 본 가장 모범적인 솔루션입니다. 제 사업을 성장시키는 데 필요한 모든 것이 있습니다."',
	OnboardingReviewTwo:
		'"저는 또한 케어패트론 앱을 좋아합니다. 이동 중에도 고객을 추적하고 작업하는 데 도움이 됩니다."',
	OnboardingTitle: `이제 시작하자<mark> 알다 
너는 더 나은</mark>`,
	Oncologist: '종양학자',
	Online: '온라인',
	OnlineBookingColorTheme: '온라인 예약 색상 테마',
	OnlineBookings: '온라인 예약',
	OnlineBookingsHelper: '온라인 예약이 가능한 시기와 고객 유형을 선택하세요',
	OnlinePayment: '온라인 결제',
	OnlinePaymentSettingCustomInfo: '이 서비스의 온라인 결제 설정은 글로벌 예약 설정과 다릅니다.',
	OnlinePaymentSettings: '온라인 결제 설정',
	OnlinePaymentSettingsInfo: '온라인 예약 시 서비스에 대한 결제를 수집하여 결제를 안전하고 간소화합니다.',
	OnlinePaymentSettingsPaymentsDisabled:
		'온라인 예약 시 결제가 비활성화되어 결제를 수집할 수 없습니다. 결제 설정을 확인하여 결제를 활성화하십시오.',
	OnlinePaymentSettingsStripeNote: '{action}을 통해 온라인 예약 결제를 받고 결제 프로세스를 간소화하세요',
	OnlinePaymentsNotSupportedForCurrency: '{currency}에서는 온라인 결제가 지원되지 않습니다.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: '죄송합니다. 이 통화로는 온라인 결제가 지원되지 않습니다.',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'죄송합니다. 귀하의 국가에서는 아직 온라인 결제가 지원되지 않습니다.',
	OnlineScheduling: '온라인 스케줄링',
	OnlyVisibleToYou: '당신에게만 보입니다',
	OnlyYou: '오직 당신만',
	OnsetDate: '발병일',
	OnsetOfCurrentSymptomsOrIllness: '현재 증상이나 질병의 시작',
	Open: '열려 있는',
	OpenFile: '파일 열기',
	OpenSettings: '설정 열기',
	Ophthalmologist: '안과 의사',
	OptimiseTelehealthCalls: '원격 진료 통화를 최적화하세요',
	OptimizeServiceTimes: '서비스 시간 최적화',
	Options: '옵션',
	Optometrist: '안과의사',
	Or: '또는',
	OrAttachSingleFile: '파일을 첨부하다',
	OrDragAndDrop: '또는 드래그 앤 드롭',
	OrderBy: '주문 기준',
	Oregon: '오리건',
	OrganisationOrIndividual: '조직 또는 개인',
	OrganizationPlanInclusion1: '고급 권한',
	OrganizationPlanInclusion2: '무료 클라이언트 데이터 가져오기 지원',
	OrganizationPlanInclusion3: '전담 성공 관리자',
	OrganizationPlanInclusionHeader: '전문가 수준의 모든 기능과...',
	Orthodontist: '치열교정의사',
	Orthotist: '정형외과 의사',
	Other: '다른',
	OtherAdjustments: '기타 조정 사항',
	OtherAdjustmentsTableEmptyState: '조정 사항이 없습니다',
	OtherEvents: '기타 이벤트',
	OtherId: '기타 ID',
	OtherIdQualifier: '기타 ID 한정자',
	OtherPaymentMethod: '기타 결제수단',
	OtherPlanMessage:
		'귀하의 진료소 요구 사항을 관리하십시오. 현재 계획을 검토하고 사용량을 모니터링하며 팀이 성장함에 따라 더 많은 기능을 해제하기 위해 업그레이드 옵션을 살펴보십시오.',
	OtherPolicy: '기타 보험',
	OtherProducts: '다른 제품이나 도구를 어떤 걸 사용하시나요?',
	OtherServices: '기타 서비스',
	OtherTemplates: '다른 템플릿',
	Others: '기타',
	OthersPeople: `{n, plural, 		one {다른 사람 1명}
		other {#명의 다른 사람들}
	}`,
	OurResearchTeamReachOut:
		'저희 연구팀이 Carepatron이 귀하의 요구 사항에 어떻게 더 적합할 수 있는지 자세히 알아보고자 연락해 주실 수 있습니까?',
	OutOfOffice: '사무실 밖',
	OutOfOfficeColor: '사무실 밖 색상',
	OutOfOfficeHelper: '선택된 팀원 중 일부는 사무실에 없습니다.',
	OutsideLabCharges: '외부 실험실 비용',
	OutsideOfWorkingHours: '근무시간 외',
	OutsideWorkingHoursHelper: '일부 팀원은 근무 시간 외에 선발되었습니다.',
	Overallocated: '초과 할당',
	OverallocatedPaymentDescription: `이 지불은 청구 가능 항목에 초과 할당되었습니다.
 지불되지 않은 항목에 할당을 추가하거나, 크레딧 또는 환불을 발행합니다.`,
	OverallocatedPaymentTitle: '할당된 지불 초과',
	OverdueTerm: '연체기간(일)',
	OverinvoicedAmount: '초과 청구 금액',
	Overpaid: '과다 지불',
	OverpaidAmount: '과불금액',
	Overtime: '시간 외에',
	Owner: '소유자',
	POS: '포스',
	POSCode: 'POS 코드',
	POSPlaceholder: '포스',
	PageBlockerDescription: '저장되지 않은 변경 사항은 손실됩니다. 그래도 떠나시겠습니까?',
	PageBlockerTitle: '변경 사항을 삭제하시겠습니까?',
	PageFormat: '페이지 형식',
	PageNotFound: '페이지를 찾을 수 없습니다',
	PageNotFoundDescription: '더 이상 이 페이지에 액세스할 수 없거나 찾을 수 없습니다.',
	PageUnauthorised: '무단 접근',
	PageUnauthorisedDescription: '이 페이지에 접근할 수 있는 권한이 없습니다.',
	Paid: '유급의',
	PaidAmount: '지불 금액',
	PaidAmountMinimumValueError: '결제금액은 0보다 커야 합니다.',
	PaidAmountRequiredError: '결제금액이 필요합니다',
	PaidItems: '유료 아이템',
	PaidMultiple: '유급의',
	PaidOut: '지불됨',
	ParagraphStyles: '문단 스타일',
	Parent: '조상',
	Paris: '파리',
	PartialRefundAmount: '부분 환불됨 ({amount} 남음)',
	PartiallyFull: '부분적으로 가득 찼습니다',
	PartiallyPaid: '부분적으로 지불됨',
	PartiallyRefunded: '일부 환불됨',
	Partner: '파트너',
	Password: '비밀번호',
	Past: '과거',
	PastDateOverridesEmpty: '이벤트가 지나면 날짜 재정의가 여기에 표시됩니다.',
	Pathologist: '병리학자',
	Patient: '인내심 있는',
	Pause: '정지시키다',
	Paused: '일시 중지됨',
	Pay: '지불하다',
	PayMonthly: '월별 결제',
	PayNow: '지금 결제하세요',
	PayValue: '{showPrice, select, true {{price}} other {지금}}',
	PayWithOtherCard: '다른 카드로 결제하기',
	PayYearly: '연간 결제',
	PayYearlyPercentOff: '연간 결제 시 <mark>{percent}% 할인</mark>',
	Payer: '지불자',
	PayerClaimId: '지불자 청구 ID',
	PayerCoverage: '적용 범위',
	PayerDetails: '지불자 세부 정보',
	PayerDetailsDescription: '귀하의 계정에 추가된 지불자 세부 정보를 보고 등록을 관리하세요.',
	PayerID: '지불자 ID',
	PayerId: '지불자 ID',
	PayerName: '지불자 이름',
	PayerPhoneNumber: '납부자 전화번호',
	Payers: '지불자',
	Payment: '지불',
	PaymentAccountUpdated: '귀하의 계정이 업데이트되었습니다!',
	PaymentAccountUpgraded: '귀하의 계정이 업그레이드되었습니다!',
	PaymentAmount: '지불 금액',
	PaymentDate: '결제일',
	PaymentDetails: '결제 정보',
	PaymentForUsersPerMonth: '{billedUsers, plural, one {# 명의 사용자} other {# 명의 사용자}}에 대한 월별 결제',
	PaymentInfoFormPrimaryText: '결제 정보',
	PaymentInfoFormSecondaryText: '결제 세부 정보 수집',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: '이 송장은 이미 지불되었습니다.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: '이 송장은 이미 처리 중입니다.',
	PaymentIntentAmountMismatchSnackbar: '송장의 총 금액이 수정되었습니다. 지불하기 전에 변경 사항을 검토하세요.',
	PaymentIntentSyncTimeoutSnackbar:
		'결제가 성공했지만 시간 초과가 발생했습니다. 페이지를 새로 고치세요. 결제가 표시되지 않으면 지원팀에 문의하세요.',
	PaymentMethod: '결제방법',
	PaymentMethodDescription: '구독 청구 프로세스를 간소화하기 위해 진료소 결제 방법을 추가하고 관리하세요.',
	PaymentMethodLabelBank: '은행 계좌',
	PaymentMethodLabelCard: '카드',
	PaymentMethodLabelFallback: '결제방법',
	PaymentMethodRequired: '구독을 변경하기 전에 결제 방법을 추가해 주세요.',
	PaymentMethods: '결제 방법',
	PaymentProcessing: '결제 처리 중!',
	PaymentProcessingFee: '결제에는 {amount} 처리 수수료가 포함됩니다.',
	PaymentReports: '지불 보고서 (ERA)',
	PaymentSettings: '결제 설정',
	PaymentSuccessful: '결제가 성공했습니다!',
	PaymentType: '결제 유형',
	Payments: '결제',
	PaymentsAccountDisabledNotificationSubject: `{paymentProvider, select, undefined { Stripe } other {{paymentProvider}}}를 통한 온라인 결제가 비활성화되었습니다.
결제를 활성화하려면 결제 설정을 확인하세요.`,
	PaymentsEmptyStateDescription: '지불이 발견되지 않았습니다.',
	PaymentsUnallocated: '할당되지 않은 지불',
	PayoutDate: '지급일',
	PayoutsDisabled: '지급이 비활성화되었습니다',
	PayoutsEnabled: '지급이 활성화되었습니다',
	PayoutsStatus: '지급 상태',
	Pediatrician: '소아과 의사',
	Pen: '펜',
	Pending: '보류 중',
	People: '{rosterSize } 명',
	PeopleCount: '사람({count})',
	PerMonth: '/ 월',
	PerUser: '사용자에 의해',
	Permission: '허가',
	PermissionRequired: '허가가 필요합니다',
	Permissions: '권한',
	PermissionsClientAndContactDocumentation: '고객 ',
	PermissionsClientAndContactProfiles: '고객 ',
	PermissionsEditAccess: '편집 액세스',
	PermissionsInvoicesAndPayments: '송장 ',
	PermissionsScheduling: '스케줄링',
	PermissionsUnassignClients: '클라이언트 할당 해제',
	PermissionsUnassignClientsConfirmation: '이 클라이언트의 할당을 해제하시겠습니까?',
	PermissionsValuesAssigned: '할당만 됨',
	PermissionsValuesEverything: '모든 것',
	PermissionsValuesNone: '없음',
	PermissionsValuesOwnCalendar: '나만의 달력',
	PermissionsViewAccess: '접근 보기',
	PermissionsWorkspaceSettings: '작업 공간 설정',
	Person: '{rosterSize} 명',
	PersonalDetails: '개인 정보',
	PersonalHealthcareHistoryStoreDescription: '개인 건강 관리 내역을 한곳에서 안전하게 답변하고 저장하세요',
	PersonalTrainer: '개인 트레이너',
	PersonalTraining: '개인 트레이닝',
	PersonalizeWorkspace: '개인화된 작업 공간 만들기',
	PersonalizingYourWorkspace: '작업 공간 개인화',
	Pharmacist: '제약사',
	Pharmacy: '약국',
	PhoneCall: '전화통화',
	PhoneNumber: '전화 번호',
	PhoneNumberOptional: '전화번호 (선택사항)',
	PhotoBy: '사진 제공:',
	PhysicalAddress: '물리적 주소',
	PhysicalTherapist: '물리치료사',
	PhysicalTherapists: '물리 치료사',
	PhysicalTherapy: '물리 치료',
	Physician: '내과 의사',
	PhysicianAssistant: '의사 보조원',
	Physicians: '의사들',
	Physiotherapist: '물리치료사',
	PlaceOfService: '서비스 장소',
	Plan: '계획',
	PlanAndReport: '계획/보고',
	PlanId: '플랜 ID',
	PlansAndReportsCategoryDescription: '치료 계획 및 결과 요약을 위해',
	PleaseRefreshThisPageToTryAgain: '이 페이지를 새로고침하여 다시 시도하십시오.',
	PleaseWait: '기다리세요...',
	PleaseWaitForHostToJoin: '호스트의 참여를 기다리는 중...',
	PleaseWaitForHostToStart: '호스트가 회의를 시작할 때까지 기다려 주세요.',
	PlusAdd: '+ 추가',
	PlusOthers: '+{count} 명의 다른 사람들',
	PlusPlanInclusionFive: '공유 받은 편지함',
	PlusPlanInclusionFour: '그룹 화상 통화',
	PlusPlanInclusionHeader: '필수품의 모든 것  ',
	PlusPlanInclusionOne: '무제한 AI',
	PlusPlanInclusionSix: '맞춤형 브랜딩',
	PlusPlanInclusionThree: '그룹 스케줄링',
	PlusPlanInclusionTwo: '무제한 저장 ',
	PlusSubscriptionPlanSubtitle: '최적화하고 성장하기 위한 실습',
	PlusSubscriptionPlanTitle: '을 더한',
	PoliceOfficer: '경찰관',
	PolicyDates: '정책 날짜',
	PolicyHolder: '보험계약자',
	PolicyHoldersAddress: '보험 계약자 주소',
	PolicyMemberId: '정책 회원 ID',
	PolicyStatus: '정책 상태',
	Popular: '인기 있는',
	PortalAccess: '포털 접근',
	PortalNoAppointmentsHeading: '예정된 모든 약속과 지난 약속을 추적하세요',
	PortalNoDocumentationHeading: '문서를 안전하게 생성하고 저장하세요',
	PortalNoRelationshipsHeading: '당신의 여정을 지지하는 사람들을 모으세요',
	PosCodeErrorMessage: 'POS 코드가 필요합니다.',
	PosoNumber: 'PO/SO 번호',
	PossibleClientDuplicate: '가능한 클라이언트 중복',
	PotentialClientDuplicateTitle: '잠재적인 중복 클라이언트 레코드',
	PotentialClientDuplicateWarning:
		'이 고객 정보는 이미 고객 목록에 있을 수 있습니다. 필요한 경우 기존 기록을 확인하고 업데이트하거나 새 고객을 계속 생성하세요.',
	PoweredBy: '제공자:',
	Practice: '관행',
	PracticeDetails: '연습 세부 사항',
	PracticeInfoHeader: '사업 정보',
	PracticeInfoPlaceholder: `연습 이름,
 국가 공급자 식별자,
 고용주 식별 번호`,
	PracticeLocation: '귀하의 진료가 진행 중인 것 같습니다.',
	PracticeSettingsAvailabilityTab: '유효성',
	PracticeSettingsBillingTab: '결제 설정',
	PracticeSettingsClientSettingsTab: '클라이언트 설정',
	PracticeSettingsGeneralTab: '일반적인',
	PracticeSettingsOnlineBookingTab: '온라인 예약',
	PracticeSettingsServicesTab: '서비스',
	PracticeSettingsTaxRatesTab: '세율',
	PracticeTemplate: '연습 템플릿',
	Practitioner: '실무자',
	PreferredLanguage: '선호 언어',
	PreferredName: '선호하는 이름',
	Prescription: '처방',
	PreventionSpecialist: '예방 전문가',
	Preview: '시사',
	PreviewAndSend: '미리보기 및 보내기',
	PreviewUnavailable: '이 파일 형식에 대한 미리보기를 사용할 수 없습니다.',
	PreviousNotes: '이전 노트',
	Price: '가격',
	PriceError: '가격은 0보다 커야 합니다.',
	PricePerClient: '고객당 가격',
	PricePerUser: '사용자당',
	PricePerUserBilledAnnually: '사용자당 연간 청구',
	PricePerUserPerPeriod: '{price} 사용자당 / {isMonthly, select, true {월} other {년}}',
	PricingGuide: '가격 계획 안내',
	PricingPlanPerMonth: '/ 월',
	PricingPlanPerYear: '/ 년',
	Primary: '주요한',
	PrimaryInsurance: '1차 보험',
	PrimaryPolicy: '1차 보험',
	PrimaryTimezone: '기본 시간대',
	Print: '인쇄',
	PrintToCms1500: 'CMS1500으로 인쇄',
	PrivatePracticeConsultant: '개인 진료 컨설턴트',
	Proceed: '계속',
	ProcessAtTimeOfBookingDesc: '고객은 온라인으로 예약하기 위해 전체 서비스 가격을 지불해야 합니다.',
	ProcessAtTimeOfBookingLabel: '예약 시 결제를 처리합니다.',
	Processing: '처리 중',
	ProcessingFee: '처리 수수료',
	ProcessingFeeToolTip: `Carepatron을 사용하면 고객에게 처리 수수료를 부과할 수 있습니다.
 일부 관할권에서는 고객에게 처리 수수료를 청구하는 것이 금지되어 있습니다. 해당 법률을 준수하는 것은 귀하의 책임입니다.`,
	ProcessingRequest: '요청을 처리 중입니다...',
	Product: '제품',
	Profession: '직업',
	ProfessionExample: '치료사, 영양사, 치과 의사',
	ProfessionPlaceholder: '직업을 입력하거나 목록에서 선택하세요',
	ProfessionalPlanInclusion1: '무제한 저장',
	ProfessionalPlanInclusion2: '무제한 작업',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 고객 지원',
	ProfessionalPlanInclusion5: 'SMS 알림',
	ProfessionalPlanInclusionHeader: '스타터의 모든 것에 더하여...',
	Professions: '직업',
	Profile: '윤곽',
	ProfilePhotoFileSizeLimit: '파일 크기 제한 5MB',
	ProfilePopoverSubTitle: '<strong>{email}</strong>으로 로그인했습니다.',
	ProfilePopoverTitle: '귀하의 작업 공간',
	PromoCode: '프로모 코드',
	PromotionCodeApplied: '{promo} 적용됨',
	ProposeNewDateTime: '새로운 날짜/시간 제안하기',
	Prosthetist: '보철사',
	Provider: '공급자',
	ProviderBillingPlanExpansionManageButton: '계획 관리',
	ProviderCommercialNumber: '공급자 상업 번호',
	ProviderDetails: '제공자 세부 정보',
	ProviderDetailsAddress: '주소',
	ProviderDetailsName: '이름',
	ProviderDetailsPhoneNumber: '전화 번호',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'죄송합니다. 이 공급업체는 이미 기존 청구 계정을 보유하고 있습니다.',
	ProviderInfoPlaceholder: `직원 이름,
 이메일 주소,
 전화 번호,
 국가 공급자 식별자,
 라이센스 번호`,
	ProviderIsChargedProcessingFee: '당신은 처리 수수료를 지불할 것입니다',
	ProviderPaymentFormBackButton: '뒤쪽에',
	ProviderPaymentFormBillingAddressCity: '도시',
	ProviderPaymentFormBillingAddressCountry: '국가',
	ProviderPaymentFormBillingAddressLine1: '라인1',
	ProviderPaymentFormBillingAddressPostalCode: '우편번호',
	ProviderPaymentFormBillingEmail: '이메일',
	ProviderPaymentFormCardCvc: '씨비씨(CVC)',
	ProviderPaymentFormCardDetailsTitle: '신용 카드 정보',
	ProviderPaymentFormCardExpiry: '만료',
	ProviderPaymentFormCardHolderAddressTitle: '주소',
	ProviderPaymentFormCardHolderName: '카드 소유자 이름',
	ProviderPaymentFormCardHolderTitle: '카드 소지자 세부 정보',
	ProviderPaymentFormCardNumber: '카드번호',
	ProviderPaymentFormPlanTitle: '선택한 계획',
	ProviderPaymentFormPlanTotalTitle: '총 ({currency}):',
	ProviderPaymentFormSaveButton: '구독 저장',
	ProviderPaymentFreePlanDescription:
		'무료 플랜을 선택하면 각 직원이 귀하의 제공업체에서 클라이언트에 접근할 수 없게 됩니다. 그러나 귀하의 접근 권한은 유지되며 플랫폼을 계속 사용할 수 있습니다.',
	ProviderPaymentStepName: '검토 ',
	ProviderPaymentSuccessSnackbar: '좋습니다! 새 플랜이 성공적으로 저장되었습니다.',
	ProviderPaymentTitle: '검토 ',
	ProviderPlanNetworkIdentificationNumber: '공급자 계획 네트워크 식별 번호',
	ProviderRemindersSettingsBannerAction: '워크플로우 관리로 이동',
	ProviderRemindersSettingsBannerDescription:
		'새로운 **워크플로 관리** 탭에서 **설정** 아래 모든 미리 알림을 찾으세요. 이 업데이트는 강력한 새 기능, 개선된 템플릿 및 더욱 스마트한 자동화 도구를 제공하여 생산성을 높여줍니다. 🚀',
	ProviderRemindersSettingsBannerTitle: '리마인더 경험이 더 나아지고 있습니다.',
	ProviderTaxonomy: '공급자 분류',
	ProviderUPINNumber: '공급자 UPIN 번호',
	ProviderUsedStoragePercentage: '{providerName} 저장 공간이 {usedStoragePercentage}% 가득 찼습니다!',
	PsychiatricNursePractitioner: '정신과 간호사',
	Psychiatrist: '정신과 의사',
	Psychiatrists: '정신과 의사',
	Psychiatry: '정신과',
	Psychoanalyst: '정신분석학자',
	Psychologist: '심리학자',
	Psychologists: '심리학자',
	Psychology: '심리학',
	Psychometrician: '심리측정학자',
	PsychosocialRehabilitationSpecialist: '심리사회적 재활 전문가',
	Psychotheraphy: '심리치료',
	Psychotherapists: '심리치료사',
	Psychotherapy: '심리치료',
	PublicCallDialogTitle: '영상통화 ',
	PublicCallDialogTitlePlaceholder: 'Carepatron이 제공하는 화상 통화',
	PublicFormBackToForm: '다른 응답을 제출하세요',
	PublicFormConfirmSubmissionHeader: '제출 확인',
	PublicFormNotFoundDescription:
		'찾으시는 양식이 삭제되었거나 링크가 잘못되었을 수 있습니다. URL을 확인하시고 다시 시도해 주세요.',
	PublicFormNotFoundTitle: '폼을 찾을 수 없습니다.',
	PublicFormSubmissionError: '제출에 실패했습니다. 다시 시도해 주세요.',
	PublicFormSubmissionSuccess: '양식이 성공적으로 제출되었습니다',
	PublicFormSubmittedNotificationSubject: '{actorProfileName}는 {noteTitle} 공개 양식을 제출했습니다.',
	PublicFormSubmittedSubtitle: '귀하의 제출이 접수되었습니다.',
	PublicFormSubmittedTitle: '감사합니다!',
	PublicFormVerifyClientEmailDialogSubtitle: '이메일로 확인 코드를 보냈습니다.',
	PublicFormsInvalidConfirmationCode: '잘못된 확인 코드',
	PublicHealthInspector: '보건감독관',
	PublicTemplates: '공개 템플릿',
	Publish: '게시하다',
	PublishTemplate: '템플릿 게시',
	PublishTemplateFeatureBannerSubheader: '커뮤니티에 도움이 되도록 설계된 템플릿',
	PublishTemplateHeader: '{title} 게시',
	PublishTemplateToCommunity: '커뮤니티에 템플릿 게시',
	PublishToCommunity: '커뮤니티에 게시',
	PublishToCommunitySuccessMessage: '커뮤니티에 성공적으로 게시되었습니다',
	Published: '게시됨',
	PublishedBy: '{name}에 의해 게시됨',
	PublishedNotesAreNotAutosaved: '게시된 노트는 자동 저장되지 않습니다.',
	PublishedOnCarepatronCommunity: 'Carepatron 커뮤니티에 게시됨',
	Purchase: '구입',
	PushToCalendar: '캘린더에 푸시',
	Question: '질문',
	QuestionOrTitle: '질문이나 제목',
	QuickActions: '빠른 조치',
	QuickThemeSwitcherColorBasil: 'Basil바질',
	QuickThemeSwitcherColorBlueberry: '블루베리',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: '이끼',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: '스쿼시',
	RadiationTherapist: '방사선 치료사',
	Radiologist: '방사선 학자',
	Read: '읽다',
	ReadOnly: '읽기 전용',
	ReadOnlyAppointment: '읽기 전용 약속',
	ReadOnlyEventBanner: '이 약속은 읽기 전용 달력에서 동기화되었으며 편집할 수 없습니다.',
	ReaderMaxDepthHasBeenExceededCode: '노트가 너무 중첩되어 있습니다. 일부 항목의 들여쓰기를 풀어보세요.',
	ReadyForMapping: '매핑 준비 완료',
	RealEstateAgent: '부동산 중개인',
	RearrangeClientFields: '클라이언트 설정에서 클라이언트 필드 재정렬',
	Reason: '이유',
	ReasonForChange: '변경 이유',
	RecentAppointments: '최근 약속',
	RecentServices: '최근 서비스',
	RecentTemplates: '최근 템플릿',
	RecentlyUsed: '최근 사용',
	Recommended: '권장',
	RecommendedTemplates: '추천 템플릿',
	Recording: '녹음',
	RecordingEnded: '녹음이 종료되었습니다',
	RecordingInProgress: '녹음 진행 중',
	RecordingMicrophoneAccessErrorMessage: '브라우저에서 마이크 접근을 허용하고 새로 고침하여 녹음을 시작하세요.',
	RecurrenceCount: ', {count, plural, one {한 번} other {#번}}',
	RecurrenceDaily: '{count, plural, one {매일} other {일}}',
	RecurrenceEndAfter: '다음',
	RecurrenceEndNever: '절대로',
	RecurrenceEndOn: 'On',
	RecurrenceEvery: '모든 {description}',
	RecurrenceMonthly: '{count, plural, one {월별} other {개월}}',
	RecurrenceOn: '{description}에',
	RecurrenceOnAllDays: '모든 날에',
	RecurrenceUntil: '{description}까지',
	RecurrenceWeekly: '{count, plural, one {주간} other {주}}',
	RecurrenceYearly: '{count, plural, one {연간} other {년}}',
	Recurring: '반복되는',
	RecurringAppointment: '정기 예약',
	RecurringAppointmentsLimitedBannerText:
		'모든 반복 예약이 표시되는 것은 아닙니다. 해당 기간의 모든 반복 예약을 보려면 날짜 범위를 줄이세요.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# 이벤트} other {# 이벤트들}}</b>은 다음 날짜에 생성됩니다.',
	Redo: '다시 하다',
	ReferFriends: '친구 추천',
	Reference: '참조',
	ReferralCreditedNotificationSubject: '{currency} {amount}의 추천 크레딧이 적용되었습니다.',
	ReferralEmailDefaultBody: `{name}님 덕분에 Carepatron 3개월 무료 업그레이드가 제공되었습니다. 새로운 방식으로 일하는 300만 명 이상의 의료 전문가 커뮤니티에 참여하세요!
감사합니다.
Carepatron 팀`,
	ReferralEmailDefaultSubject: 'Carepatron에 가입하도록 초대되었습니다.',
	ReferralHasNotSignedUpDescription: '귀하의 친구가 아직 가입하지 않았습니다.',
	ReferralHasSignedUpDescription: '친구가 가입했습니다.',
	ReferralInformation: '추천 정보',
	ReferralJoinedNotificationSubject: '{actorProfileName}님이 Carepatron에 합류했습니다.',
	ReferralListErrorDescription: '추천 목록을 불러올 수 없습니다.',
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {개월} other {개월}}</b> 활성',
	ReferralRewardBanner: '가입하고 추천 보상을 받으세요!',
	Referrals: '추천',
	ReferredUserBenefitSubtitle:
		'{durationInMonths}개월 {percentOff, select, 100 {무료 결제} other {{percentOff}% 할인}} {type, select, SubscriptionUpgrade {업그레이드} other {}}',
	ReferredUserBenefitTitle: '그들은 이해한다!',
	Referrer: '추천인',
	ReferringProvider: '추천 제공자',
	ReferringUserBenefitSubtitle: '<mark>3명의 친구</mark>가 활성화되면 USD${creditAmount} 크레딧 제공.',
	ReferringUserBenefitTitle: '당신은 얻을!',
	RefreshPage: '페이지 새로고침',
	Refund: '환불하다',
	RefundAcknowledgement: '저는 Carepatron 외부에서 {clientName}에게 환불했습니다.',
	RefundAcknowledgementValidationMessage: '이 금액을 환불했는지 확인해 주세요.',
	RefundAmount: '환불금액',
	RefundContent:
		'환불은 고객 계정에 나타나기까지 7-10일이 걸립니다. 결제 수수료는 환불되지 않지만 환불에 대한 추가 비용은 없습니다. 환불은 취소할 수 없으며 일부는 처리하기 전에 검토가 필요할 수 있습니다.',
	RefundCouldNotBeProcessed: '환불을 처리할 수 없습니다.',
	RefundError:
		'이 환불은 현재 자동으로 처리될 수 없습니다. 이 결제에 대한 환불을 요청하려면 Carepatron 지원팀에 문의하세요.',
	RefundExceedTotalValidationError: '금액은 총 지불 금액을 초과할 수 없습니다.',
	RefundFailed: '환불 실패',
	RefundFailedTooltip: '이전에 이 결제에 대한 환불이 실패했으며 다시 시도할 수 없습니다. 지원팀에 문의하세요.',
	RefundNonStripePaymentContent:
		'이 결제는 Carepatron 외부의 방법(예: 현금, 인터넷 뱅킹)을 사용하여 이루어졌습니다. Carepatron 내에서 환불을 하면 고객에게 어떠한 금액도 반환되지 않습니다.',
	RefundReasonDescription: '환불 사유를 추가하면 클라이언트 거래를 검토할 때 도움이 될 수 있습니다.',
	Refunded: '환불됨',
	Refunds: '환불',
	RefundsTableEmptyState: '환불이 발견되지 않았습니다',
	Regenerate: '재생',
	RegisterButton: '등록하다',
	RegisterEmail: '이메일',
	RegisterFirstName: '이름',
	RegisterLastName: '성',
	RegisterPassword: '비밀번호',
	RegisteredNurse: '등록 간호사',
	RehabilitationCounselor: '재활 상담사',
	RejectAppointmentFormTitle: '참석할 수 없으신가요? 이유를 알려주시고 새로운 시간을 제안해 주세요.',
	Rejected: '거부됨',
	Relationship: '관계',
	RelationshipDetails: '관계 세부 정보',
	RelationshipEmptyStateTitle: '고객을 지원하는 사람들과 계속 연락하세요',
	RelationshipPageAccessTypeColumnName: '프로필 접근',
	RelationshipSavedSuccessSnackbar: '관계가 성공적으로 저장되었습니다!',
	RelationshipSelectorFamilyAdmin: '가족',
	RelationshipSelectorFamilyMember: '가족 구성원',
	RelationshipSelectorProviderAdmin: '공급자 관리자',
	RelationshipSelectorProviderStaff: '공급자 직원',
	RelationshipSelectorSupportNetworkPrimary: '친구',
	RelationshipSelectorSupportNetworkSecondary: '지원 네트워크',
	RelationshipStatus: '관계 상태',
	RelationshipType: '관계 유형',
	RelationshipTypeClientOwner: '고객',
	RelationshipTypeFamilyAdmin: '관계',
	RelationshipTypeFamilyMember: '가족',
	RelationshipTypeFriendOrSupport: '친구 또는 지원 네트워크',
	RelationshipTypeProviderAdmin: '공급자 관리자',
	RelationshipTypeProviderStaff: '직원',
	RelationshipTypeSelectorPlaceholder: '관계 유형 검색',
	Relationships: '관계',
	Remaining: '남은',
	RemainingTime: '남은 {time}',
	Reminder: '알림',
	ReminderColor: '알림색상',
	ReminderDetails: '미리알림 세부 정보',
	ReminderEditDisclaimer: '변경 사항은 새로운 약속에만 반영됩니다.',
	ReminderSettings: '약속 알림 설정',
	Reminders: '알림',
	Remove: '제거하다',
	RemoveAccess: '접근 권한 제거',
	RemoveAllGuidesBtn: '모든 가이드를 제거하세요',
	RemoveAllGuidesPopoverBody:
		'온보딩 가이드를 다 읽고 나면 각 패널에 있는 가이드 제거 버튼을 사용하기만 하면 됩니다.',
	RemoveAllGuidesPopoverTitle: '더 이상 온보딩 가이드가 필요하지 않으신가요?',
	RemoveAsDefault: '기본값으로 제거',
	RemoveAsIntake: '흡입구로 제거',
	RemoveCol: '열 제거',
	RemoveColor: '색상 제거',
	RemoveField: '필드 제거',
	RemoveFromCall: '통화에서 제거',
	RemoveFromCallDescription: '<mark>{attendeeName}</mark>을(를) 이 비디오 통화에서 제거하시겠습니까?',
	RemoveFromCollection: '컬렉션에서 제거',
	RemoveFromCommunity: '커뮤니티에서 제거',
	RemoveFromFolder: '폴더에서 삭제',
	RemoveFromFolderConfirmationDescription:
		'이 템플릿을 이 폴더에서 삭제하시겠습니까? 이 작업은 취소할 수 없습니다. 나중에 다시 이동할 수 있습니다.',
	RemoveFromIntakeDefault: '흡입 기본값에서 제거',
	RemoveGuides: '가이드 제거',
	RemoveMfaConfirmationDescription: '다중 요소 인증(MFA)을 제거하면 계정의 보안이 약화됩니다. 계속하시겠습니까?',
	RemoveMfaConfirmationTitle: 'MFA를 제거하시겠습니까?',
	RemovePaymentMethodDescription: `이렇게 하면 이 결제 방법에 대한 모든 접근 권한과 향후 사용이 제거됩니다.
 이 작업은 실행 취소할 수 없습니다.`,
	RemoveRow: '행 제거',
	RemoveTable: '테이블 제거',
	RemoveTemplateAsDefaultIntakeSuccess: '{templateTitle} 기본 입구 템플릿으로 삭제되었습니다.',
	RemoveTemplateFromCommunity: '커뮤니티에서 템플릿 제거',
	RemoveTemplateFromFolder: '{templateTitle}이(가) {folderTitle}에서 성공적으로 삭제되었습니다.',
	Rename: '이름 바꾸기',
	RenderingProvider: '렌더링 제공자',
	Reopen: '다시 열다',
	ReorderServiceGroupFailure: '컬렉션 재주문에 실패했습니다',
	ReorderServiceGroupSuccess: '컬렉션을 성공적으로 재정렬했습니다.',
	ReorderServicesFailure: '서비스 재주문 실패',
	ReorderServicesSuccess: '서비스를 성공적으로 재주문했습니다',
	ReorderYourServiceList: '서비스 목록을 재정렬하세요',
	ReorderYourServiceListDescription:
		'귀하가 서비스와 컬렉션을 구성하는 방식은 모든 고객이 볼 수 있는 온라인 예약 페이지에 반영됩니다!',
	RepeatEvery: '반복하다',
	RepeatOn: '반복하다',
	Repeating: '반복',
	Repeats: '반복',
	RepeatsEvery: '반복됨',
	Rephrase: '다시 표현하다',
	Replace: '바꾸다',
	ReplaceBackground: '배경 바꾸기',
	ReplacementOfPriorClaim: '이전 청구 대체',
	Report: '보고서',
	Reprocess: '다시 처리하다',
	RepublishTemplateToCommunity: '커뮤니티에 다시 게시할 템플릿',
	RequestANewVerificationLink: '새로운 확인 링크 요청',
	RequestCoverageReport: '커버리지 보고서 요청',
	RequestingDevicePermissions: '장치 권한을 요청합니다...',
	RequirePaymentMethodDesc: '고객은 온라인으로 예약하려면 신용 카드 정보를 입력해야 합니다.',
	RequirePaymentMethodLabel: '신용 카드 정보 요구',
	Required: '필수의',
	RequiredField: '필수의',
	RequiredUrl: 'URL이 필요합니다.',
	Reschedule: '일정 변경',
	RescheduleBookingLinkModalDescription: '귀하의 고객은 이 링크를 사용하여 약속 날짜와 시간을 변경할 수 있습니다.',
	RescheduleBookingLinkModalTitle: '예약 일정 변경 링크',
	RescheduleLink: '링크 재예약',
	Resend: '다시 보내기',
	ResendConfirmationCode: '확인코드 재전송',
	ResendConfirmationCodeDescription: '이메일 주소를 입력하시면 다른 확인 코드를 이메일로 보내드립니다.',
	ResendConfirmationCodeSuccess: '확인코드가 재전송되었습니다. 받은편지함을 확인해 주세요.',
	ResendNewEmailVerificationSuccess: '새로운 인증 링크가 {email}로 전송되었습니다.',
	ResendVerificationEmail: '확인 이메일을 다시 보내주세요',
	Reset: '다시 놓기',
	Resources: '자원',
	RespiratoryTherapist: '호흡 치료사',
	RespondToHistoricAppointmentError:
		'이는 역사적인 예약이므로, 궁금한 사항이 있으시면 담당 의사에게 문의해 주시기 바랍니다.',
	Responder: '응답자',
	RestorableItemModalDescription:
		'{context}를 삭제하시겠습니까?{canRestore, select, true { 나중에 복원할 수 있습니다.} other {}}',
	RestorableItemModalTitle: '{type} 삭제',
	Restore: '복원하다',
	RestoreAll: '모두 복원',
	Restricted: '제한된',
	ResubmissionCodeReferenceNumber: '재제출 코드 및 참조 번호',
	Resubmit: '다시 제출',
	Resume: '재개하다',
	Retry: '다시 해 보다',
	RetryingConnectionAttempt: '연결 재시도 중... (시도 {retryCount}/{maxRetries})',
	ReturnToForm: '폼으로 돌아가기',
	RevertClaimStatus: '청구 상태 되돌리기',
	RevertClaimStatusDescriptionBody:
		'이 청구에는 연결된 결제가 포함되어 있으며, 상태를 변경하면 결제 추적 또는 처리에 영향을 미칠 수 있으며, 수동 조정이 필요할 수 있습니다.',
	RevertClaimStatusDescriptionTitle: '{status}으로 되돌리시겠습니까?',
	RevertClaimStatusError: '청구 상태를 되돌리지 못했습니다.',
	RevertToDraft: '초안으로 되돌리기',
	Review: '검토',
	ReviewsFirstQuote: '언제 어디서나 예약 가능',
	ReviewsSecondJobTitle: '라이프하우스 클리닉',
	ReviewsSecondName: '클라라 W.',
	ReviewsSecondQuote: '저는 또한 케어패트론 앱을 좋아합니다. 이동 중에도 고객과 업무를 추적하는 데 도움이 됩니다.',
	ReviewsThirdJobTitle: '마닐라 베이 클리닉',
	ReviewsThirdName: '재키 H.',
	ReviewsThirdQuote: '편리한 탐색 기능과 아름다운 사용자 인터페이스 덕분에 매일매일 기분이 좋아집니다.',
	RightAlign: '오른쪽 정렬',
	Role: '역할',
	Roster: '참석자',
	RunInBackground: '백그라운드에서 실행',
	SMS: '문자 메시지',
	SMSAndEmailReminder: '문자 메시지 ',
	SSN: '사회보장번호',
	SafetyRedirectHeading: 'Carepatron을 떠나게 됩니다',
	SafetyRedirectSubtext: '이 링크를 신뢰한다면 계속하려면 선택하세요',
	SalesRepresentative: '영업 담당자',
	SalesTax: '매출세',
	SalesTaxHelp: '생성된 송장에 판매세가 포함됩니다',
	SalesTaxIncluded: '예',
	SalesTaxNotIncluded: '아니요',
	SaoPaulo: '상파울루',
	Saturday: '토요일',
	Save: '구하다',
	SaveAndClose: '구하다 ',
	SaveAndExit: '구하다 ',
	SaveAndLock: '저장 및 잠금',
	SaveAsDraft: '초안으로 저장',
	SaveCardForFuturePayments: '향후 지불을 위해 카드를 저장하세요',
	SaveChanges: '변경 사항 저장',
	SaveCollection: '컬렉션 저장',
	SaveField: '필드 저장',
	SavePaymentMethod: '결제방법 저장',
	SavePaymentMethodDescription: '첫 번째 예약일 이전에는 요금이 청구되지 않습니다.',
	SavePaymentMethodSetupError: '예상치 못한 오류가 발생하여 지금은 결제를 구성할 수 없습니다.',
	SavePaymentMethodSetupInvoiceLater: '첫 번째 청구서를 지불할 때 지불을 설정하고 저장할 수 있습니다.',
	SaveSection: '섹션 저장',
	SaveService: '새로운 서비스 만들기',
	SaveTemplate: '템플릿 저장',
	Saved: '저장됨',
	SavedCards: '저장된 카드',
	SavedPaymentMethods: '저장됨',
	Saving: '절약...',
	ScheduleAppointmentsAndOnlineServices: '약속 및 온라인 서비스 일정을 예약하세요',
	ScheduleName: '일정 이름',
	ScheduleNew: '새로운 일정',
	ScheduleSend: '일정 보내기',
	ScheduleSendAlertInfo: '예약된 대화는 예약된 시간에 전송됩니다.',
	ScheduleSendByName: '<strong>일정 보내기</strong> • {time} by {displayName}',
	ScheduleSetupCall: '일정 설정 통화 예약',
	Scheduled: '예정됨',
	SchedulingSend: '일정 보내기',
	School: '학교',
	ScrollToTop: '맨 위로 스크롤',
	Search: '찾다',
	SearchAndConvertToLanguage: '검색 및 언어 변환',
	SearchBasicBlocks: '기본 블록 검색',
	SearchByName: '이름으로 검색',
	SearchClaims: '청구 검색',
	SearchClientFields: '클라이언트 필드 검색',
	SearchClients: '고객 이름, 고객 ID 또는 전화번호로 검색',
	SearchCommandNotFound: '"{searchTerm}"에 대한 결과를 찾을 수 없습니다.',
	SearchContacts: '고객 또는 연락처',
	SearchContactsPlaceholder: '연락처 검색',
	SearchConversations: '대화 검색',
	SearchInputPlaceholder: '모든 리소스 검색',
	SearchInvoiceNumber: '송장번호 검색',
	SearchInvoices: '송장 검색',
	SearchMultipleContacts: '고객 또는 연락처',
	SearchMultipleContactsOptional: '클라이언트 또는 연락처(선택 사항)',
	SearchOrCreateATag: '태그 검색 또는 생성',
	SearchPayments: '결제 검색',
	SearchPrepopulatedData: '미리 채워진 데이터 필드 검색',
	SearchRelationships: '관계 검색',
	SearchRemindersAndWorkflows: '알림 및 워크플로 검색',
	SearchServices: '검색 서비스',
	SearchTags: '태그 검색',
	SearchTeamMembers: '팀원 검색',
	SearchTemplatePlaceholder: '{templateCount}+개의 리소스 검색',
	SearchTimezone: '시간대 검색...',
	SearchTrashItems: '항목 검색',
	SearchUnsplashPlaceholder: 'Unsplash에서 무료 고해상도 사진 검색',
	Secondary: '반성',
	SecondaryInsurance: '2차 보험',
	SecondaryPolicy: '2차 보험',
	SecondaryTimezone: '2차 시간대',
	Secondly: '둘째',
	Section: '부분',
	SectionCannotBeEmpty: '섹션에는 최소한 하나의 행이 있어야 합니다.',
	SectionFormSecondaryText: '섹션 제목 및 설명',
	SectionName: '섹션 이름',
	Sections: '섹션',
	SeeLess: '덜 보기',
	SeeLessUpcomingAppointments: '다가오는 약속을 덜 보기',
	SeeMore: '더 보기',
	SeeMoreUpcomingAppointments: '더 많은 예정된 약속 보기',
	SeeTemplateLibrary: '템플릿 라이브러리 보기',
	Seen: '보았다',
	SeenByName: '<strong>확인됨</strong> • {time} by {displayName}',
	SelectAll: '모두 선택',
	SelectAssignees: '담당자 선택',
	SelectAttendees: '참석자 선택',
	SelectCollection: '컬렉션 선택',
	SelectCorrespondingAttributes: '해당 속성을 선택하세요',
	SelectPayers: '지불자 선택',
	SelectProfile: '프로필 선택',
	SelectServices: '서비스 선택',
	SelectTags: '태그 선택',
	SelectTeamOrCommunity: '팀 또는 커뮤니티 선택',
	SelectTemplate: '템플릿 선택',
	SelectType: '유형을 선택하세요',
	Selected: '선택된',
	SelfPay: '자기부담',
	Send: '보내다',
	SendAndClose: '보내다 ',
	SendAndStopIgnore: '보내고 무시하는 것을 멈추세요',
	SendEmail: '이메일 보내기',
	SendIntake: '섭취량 보내기',
	SendIntakeAndForms: '섭취량 보내기 ',
	SendMeACopy: '나에게 사본을 보내주세요',
	SendNotificationEmailWarning: '일부 참석자는 이메일 주소가 없어 자동 알림 및 미리 알림을 받지 못합니다.',
	SendNotificationLabel: '이메일로 알릴 참석자를 선택하세요.',
	SendOnlinePayment: '온라인으로 결제하기',
	SendOnlinePaymentTooltipTitleAdmin: '원하는 지급 설정을 추가해 주세요.',
	SendOnlinePaymentTooltipTitleStaff: '온라인 결제를 설정하려면 공급업체 소유자에게 문의하세요.',
	SendPaymentLink: '결제 링크 보내기',
	SendReaction: '반응을 보내다',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: '확인메일을 보내세요',
	SendingFailed: '전송 실패',
	Sent: '전송된',
	SentByName: '**보냄** • {time} by {displayName}',
	Seoul: '서울',
	SeparateDuplicateClientsDescription:
		'병합을 선택하지 않는 한 선택한 클라이언트 레코드는 나머지 레코드와 별도로 유지됩니다.',
	Service: '서비스',
	'Service/s': '서비스',
	ServiceAdjustment: '서비스 조정',
	ServiceAllowNewClientsIndicator: '새로운 클라이언트 허용',
	ServiceAlreadyExistsInCollection: '서비스가 이미 컬렉션에 존재합니다.',
	ServiceBookableOnlineIndicator: '온라인으로 예약 가능',
	ServiceCode: '암호',
	ServiceCodeErrorMessage: '서비스 코드가 필요합니다',
	ServiceCodeSelectorPlaceholder: '서비스 코드 추가',
	ServiceColour: '서비스 색상',
	ServiceCoverageDescription: '해당 보험 상품에 적합한 서비스와 공제금을 선택하세요.',
	ServiceCoverageGoToServices: '서비스로 이동',
	ServiceCoverageNoServicesDescription:
		'기본 정책 공제금을 재정의하기 위해 서비스 공제금 금액을 사용자 정의합니다. 정책에 대해 서비스가 청구되는 것을 방지하기 위해 보장을 비활성화합니다.',
	ServiceCoverageNoServicesLabel: '서비스를 찾을 수 없습니다.',
	ServiceCoverageTitle: '서비스 범위',
	ServiceDate: '서비스 날짜',
	ServiceDetails: '서비스 세부 정보',
	ServiceDuration: '지속',
	ServiceEmptyState: '아직 서비스가 없습니다',
	ServiceErrorMessage: '서비스가 필요합니다',
	ServiceFacility: '서비스 시설',
	ServiceName: '서비스 이름',
	ServiceRate: '비율',
	ServiceReceiptRequiresReviewNotificationSubject:
		'슈퍼빌 {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} for {serviceReceiptNumber, select, undefined {사용자} other {{clientName}}} 에 추가 정보가 필요합니다.',
	ServiceSalesTax: '매출세',
	ServiceType: '서비스',
	ServiceWorkerForceUIUpdateDialogDescription:
		'새로 고침 버튼을 눌러 새로 고침하고 최신 Carepatron 업데이트를 받으세요.',
	ServiceWorkerForceUIUpdateDialogReloadButton: '다시 로드',
	ServiceWorkerForceUIUpdateDialogSubTitle: '이전 버전을 사용하고 있습니다',
	ServiceWorkerForceUIUpdateDialogTitle: '환영합니다!',
	Services: '서비스',
	ServicesAndAvailability: '서비스 ',
	ServicesAndDiagnosisCodesHeader: '서비스 및 진단 코드 추가',
	ServicesCount: '{count,plural,=0{서비스}one{서비스}other{서비스}}',
	ServicesPlaceholder: '서비스',
	ServicesProvidedBy: '서비스 제공자',
	SetAPhysicalAddress: '물리적 주소 설정',
	SetAVirtualLocation: '가상 위치 설정',
	SetAsDefault: '기본값으로 설정',
	SetAsIntake: '섭취량으로 설정',
	SetAsIntakeDefault: '흡입 기본값으로 설정',
	SetAvailability: '가용성 설정',
	SetTemplateAsDefaultIntakeSuccess: '성공적으로 {templateTitle}을 기본 입수 템플릿으로 설정했습니다.',
	SetUpMfaButton: 'MFA 설정',
	SetYourLocation: '설정하세요<mark> 위치</mark>',
	SetYourLocationDescription: '사업장 주소가 없습니다 <span>(온라인 및 모바일 서비스만 해당)</span>',
	SettingUpPayers: '지불자 설정',
	Settings: '설정',
	SettingsNewUserPasswordDescription: '가입이 완료되면 계정을 확인하는 데 사용할 수 있는 확인 코드가 전송됩니다.',
	SettingsNewUserPasswordTitle: 'Carepatron에 가입하세요',
	SettingsTabAutomation: '오토메이션',
	SettingsTabBillingDetails: '결제 세부 정보',
	SettingsTabConnectedApps: '연결된 앱',
	SettingsTabCustomFields: '사용자 정의 필드',
	SettingsTabDetails: '세부',
	SettingsTabInvoices: '송장',
	SettingsTabLocations: '위치',
	SettingsTabNotifications: '알림',
	SettingsTabOnlineBooking: '온라인 예약',
	SettingsTabPayers: '지불자',
	SettingsTabReminders: '알림',
	SettingsTabServices: '서비스',
	SettingsTabServicesAndAvailability: '서비스 및 가용성',
	SettingsTabSubscriptions: '구독',
	SettingsTabWorkflowAutomations: '자동화',
	SettingsTabWorkflowReminders: '기본 알림',
	SettingsTabWorkflowTemplates: '템플릿',
	Setup: '설정',
	SetupGuide: '설정 가이드',
	SetupGuideAddServicesActionLabel: '시작',
	SetupGuideAddServicesSubtitle: '4단계 • 2분',
	SetupGuideAddServicesTitle: '서비스 추가',
	SetupGuideEnableOnlinePaymentsActionLabel: '시작',
	SetupGuideEnableOnlinePaymentsSubtitle: '4단계 • 3분',
	SetupGuideEnableOnlinePaymentsTitle: '온라인 결제 활성화',
	SetupGuideImportClientsActionLabel: '시작',
	SetupGuideImportClientsSubtitle: '4단계 • 3분',
	SetupGuideImportClientsTitle: '고객 가져오기',
	SetupGuideImportTemplatesActionLabel: '시작',
	SetupGuideImportTemplatesSubtitle: '2 단계 • 1분',
	SetupGuideImportTemplatesTitle: '템플릿 가져오기',
	SetupGuidePersonalizeWorkspaceActionLabel: '시작',
	SetupGuidePersonalizeWorkspaceSubtitle: '3단계 • 2분',
	SetupGuidePersonalizeWorkspaceTitle: '개인화된 작업 공간 만들기',
	SetupGuideSetLocationActionLabel: '시작',
	SetupGuideSetLocationSubtitle: '4단계 • 1분',
	SetupGuideSetLocationTitle: '위치 설정',
	SetupGuideSuggestedAddTeamMembersActionLabel: '팀 초대',
	SetupGuideSuggestedAddTeamMembersSubtitle: '팀과 쉽게 소통하고 작업을 관리할 수 있도록 초대하세요.',
	SetupGuideSuggestedAddTeamMembersTag: '설정',
	SetupGuideSuggestedAddTeamMembersTitle: '팀 구성원 추가',
	SetupGuideSuggestedCustomizeBrandActionLabel: '맞춤 설정',
	SetupGuideSuggestedCustomizeBrandSubtitle: '고유한 로고와 브랜드 색상으로 전문적인 느낌을 연출하세요.',
	SetupGuideSuggestedCustomizeBrandTitle: '브랜드 맞춤 설정',
	SetupGuideSuggestedDownloadMobileAppActionLabel: '다운로드',
	SetupGuideSuggestedDownloadMobileAppSubtitle: '어디서든, 언제든지, 어떤 기기에서든 작업 공간에 액세스하세요.',
	SetupGuideSuggestedDownloadMobileAppTag: '설정',
	SetupGuideSuggestedDownloadMobileAppTitle: '앱을 다운로드하세요',
	SetupGuideSuggestedEditAvailabilityActionLabel: '<h1>사용 가능 시간 설정</h1>',
	SetupGuideSuggestedEditAvailabilitySubtitle: '사용 가능 시간을 설정하여 중복 예약을 방지하세요.',
	SetupGuideSuggestedEditAvailabilityTag: '일정 관리',
	SetupGuideSuggestedEditAvailabilityTitle: '<h1>편집 가능 여부</h1>',
	SetupGuideSuggestedImportClientsActionLabel: '임포트',
	SetupGuideSuggestedImportClientsSubtitle: '단 한 번의 클릭으로 기존 고객 데이터를 즉시 업로드하십시오.',
	SetupGuideSuggestedImportClientsTag: '설정',
	SetupGuideSuggestedImportClientsTitle: '클라이언트 가져오기',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: '리마인더 편집',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: '자동 약속 알림으로 노쇼를 줄이세요.',
	SetupGuideSuggestedPersonalizeRemindersTitle: '개인 맞춤형 알림',
	SetupGuideSuggestedStartVideoCallActionLabel: '통화 시작',
	SetupGuideSuggestedStartVideoCallSubtitle: 'AI 기반 비디오 도구를 사용하여 전화를 주최하고 고객과 연결하십시오.',
	SetupGuideSuggestedStartVideoCallTag: '원격의료',
	SetupGuideSuggestedStartVideoCallTitle: '비디오 통화 시작',
	SetupGuideSuggestedTryActionsTitle: '시도해 볼 것들 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'AI 도움 받기',
	SetupGuideSuggestedUseAIAssistantSubtitle: '업무 관련 질문에 대한 즉각적인 답변을 얻으세요.',
	SetupGuideSuggestedUseAIAssistantTag: '새로운',
	SetupGuideSuggestedUseAIAssistantTitle: 'AI 어시스턴트를 사용하세요',
	SetupGuideSyncCalendarActionLabel: '시작',
	SetupGuideSyncCalendarSubtitle: '1단계 • 1분 미만',
	SetupGuideSyncCalendarTitle: '캘린더 동기화',
	SetupGuideVerifyEmailLabel: '확인',
	SetupGuideVerifyEmailSubtitle: '2단계 • 2분',
	SetupOnlineStripePayments: '온라인 결제에는 Stripe를 사용하세요',
	SetupPayments: '결제 설정',
	Sex: '섹스',
	SexSelectorPlaceholder: '남자 / 여자 / 말하고 싶지 않음',
	Share: '공유하다',
	ShareBookingLink: '예약 링크 공유',
	ShareNoteDefaultMessage: `안녕하세요{name}님이 "{documentName}"을(를) 공유했습니다.

감사합니다,
{practiceName}`,
	ShareNoteMessage: `안녕하세요
{name} 님이 "{documentName}"을 {isResponder, select, true {작성해 달라고 몇 가지 질문과 함께 공유했습니다.} other {공유했습니다.}}

감사합니다,
{practiceName}`,
	ShareNoteTitle: '‘{noteTitle}’ 공유하기',
	ShareNotesWithClients: '고객 또는 연락처와 공유',
	ShareScreen: '화면 공유',
	ShareScreenNotSupported: '귀하의 기기/브라우저는 화면 공유 기능을 지원하지 않습니다.',
	ShareScreenWithId: '화면 {screenId}',
	ShareTemplateAsPublicFormModalDescription: '다른 사람이 이 템플릿을 보고 양식으로 제출할 수 있도록 허용합니다.',
	ShareTemplateAsPublicFormModalTitle: '‘{title}’ 공유 링크',
	ShareTemplateAsPublicFormSaved: '공개 양식 구성이 성공적으로 업데이트되었습니다.',
	ShareTemplateAsPublicFormSectionCustomization: '맞춤 설정',
	ShareTemplateAsPublicFormShowPoweredBy: '내 양식에 "Powered by Carepatron"을 표시',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: '내 양식에서 “Carepatron으로 구동됨” 표시/숨기기',
	ShareTemplateAsPublicFormTrigger: '공유',
	ShareTemplateAsPublicFormUseWorkspaceBranding: '워크스페이스 브랜딩 사용',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: '작업 공간 브랜딩 표시/숨기기',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: '기존 고객과 신규 고객에게 코드를 보냅니다.',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: '서명에는 항상 이메일 확인이 필요합니다.',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: '기존 고객에게만 코드를 보냅니다.',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: '코드를 보내지 않습니다.',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `'절대 안 함'을 선택하면 기존 클라이언트의 이메일 주소를 사용하는 경우 검증되지 않은 사용자가 클라이언트 데이터를 덮어쓸 수 있습니다.`,
	ShareWithCommunity: '커뮤니티와 공유',
	ShareYourReferralLink: '추천 링크를 공유하세요',
	ShareYourScreen: '화면 공유하기',
	SheHer: '그녀/그녀',
	ShortTextAnswer: '짧은 텍스트 답변',
	ShortTextFormPrimaryText: '짧은 글',
	ShortTextFormSecondaryText: '300자 이내 답변',
	Show: '보여주다',
	ShowColumn: '열 표시',
	ShowColumnButton: '{value} 열 보기 버튼',
	ShowColumns: '열 표시',
	ShowColumnsMenu: '열 메뉴 표시',
	ShowDateDurationDescription: '예를 들어 29세',
	ShowDateDurationLabel: '날짜 기간 표시',
	ShowDetails: '세부 정보 표시',
	ShowField: '필드 표시',
	ShowFullAddress: '주소 표시',
	ShowHideFields: '필드 표시/숨기기',
	ShowIcons: '아이콘 표시',
	ShowLess: '덜 보기',
	ShowMeetingTimers: '회의 타이머 표시',
	ShowMenu: '메뉴보기',
	ShowMergeSummarySidebar: '병합 요약 표시',
	ShowMore: '더 보기',
	ShowOnTranscript: '대본에 표시',
	ShowReactions: '반응 보기',
	ShowSection: '섹션 표시',
	ShowServiceCode: '서비스 코드 표시',
	ShowServiceDescription: '서비스 예약에 대한 설명 표시',
	ShowServiceDescriptionDesc: '고객은 예약 시 서비스 설명을 볼 수 있습니다.',
	ShowServiceGroups: '컬렉션 보기',
	ShowServiceGroupsDesc: '예약 시 고객에게는 컬렉션별로 그룹화된 서비스가 표시됩니다.',
	ShowSpeakers: '스피커 표시',
	ShowTax: '세금 표시',
	ShowTimestamp: '타임스탬프 표시',
	ShowUnits: '단위 표시',
	ShowWeekends: '주말을 보여주세요',
	ShowYourView: '당신의 견해를 보여주세요',
	SignInWithApple: 'Apple로 로그인',
	SignInWithGoogle: 'Google로 로그인',
	SignInWithMicrosoft: 'Microsoft로 로그인',
	SignUpTitleReferralDefault: '<mark>가입하기</mark> 추천 보상을 청구하세요',
	SignUpTitleReferralUpgrade:
		'{durationInMonths}개월 <mark>{percentOff, select, 100 {무료} other {{percentOff}% 할인}} 업그레이드</mark> 시작하기',
	SignatureCaptureError: '서명을 캡처할 수 없습니다. 다시 시도하세요.',
	SignatureFormPrimaryText: '서명',
	SignatureFormSecondaryText: '디지털 서명 받기',
	SignatureInfoTooltip: '이 시각적 표현은 유효한 전자 서명이 아닙니다.',
	SignaturePlaceholder: '여기에 서명을 그려보세요',
	SignedBy: '에 의해 서명',
	Signup: '가입하기',
	SignupAgreements: '저는 계정에 대한 {termsOfUse} 및 {privacyStatement}에 동의합니다.',
	SignupBAA: '사업 파트너 계약',
	SignupBusinessAgreements:
		'저와 회사를 대신하여 저는 제 계정에 대한 {businessAssociateAgreement}, {termsOfUse} 및 {privacyStatement}에 동의합니다.',
	SignupInvitationForYou: '귀하께서는 Carepatron을 사용하도록 초대를 받았습니다.',
	SignupPageProviderWarning:
		'관리자가 이미 계정을 만든 경우 해당 공급자에 초대해 달라고 요청해야 합니다. 이 가입 양식을 사용하지 마십시오. 자세한 내용은 다음을 참조하십시오.',
	SignupPageProviderWarningLink: '이 링크.',
	SignupPrivacy: '개인정보 보호정책',
	SignupProfession: '당신을 가장 잘 설명하는 직업은 무엇입니까?',
	SignupSubtitle:
		'Carepatron의 진료 관리 소프트웨어는 단독 진료자와 팀을 위해 만들어졌습니다. 과도한 수수료 지불을 중단하고 혁명에 참여하세요.',
	SignupSuccessDescription: '온보딩을 시작하려면 이메일 주소를 확인하세요. 바로 받지 못하면 스팸 폴더를 확인하세요.',
	SignupSuccessTitle: '이메일을 확인해 주세요',
	SignupTermsOfUse: '이용 약관',
	SignupTitleClient: '<mark>건강을 관리하세요</mark> 한곳에서',
	SignupTitleLast: '그리고 당신이 하는 모든 일! — 무료입니다',
	SignupTitleOne: '<mark>당신에게 힘을 실어줍니다</mark> , ',
	SignupTitleThree: '<mark>고객에게 힘을 실어주세요</mark> , ',
	SignupTitleTwo: '<mark>팀에 힘을 실어주세요</mark> , ',
	Simple: '단순한',
	SimplifyBillToDetails: '청구서를 세부 정보로 간소화',
	SimplifyBillToHelperText: '클라이언트와 일치하는 경우에만 첫 번째 줄이 사용됩니다.',
	Singapore: '싱가포르',
	Single: '하나의',
	SingleChoiceFormPrimaryText: '단일 선택',
	SingleChoiceFormSecondaryText: '하나의 옵션만 선택하세요',
	Sister: '자매',
	SisterInLaw: '시누이',
	Skip: '건너뛰다',
	SkipLogin: '로그인 건너뛰기',
	SlightBlur: '배경을 약간 흐리게 처리합니다',
	Small: '작은',
	SmartChips: '스마트칩',
	SmartDataChips: '스마트 데이터 칩',
	SmartReply: '스마트 답장',
	SmartSuggestNewClient: '<strong>스마트 제안</strong> {name}을 새 고객으로 생성합니다.',
	SmartSuggestedFieldDescription: '이 필드는 스마트 제안입니다.',
	SocialSecurityNumber: '사회보장번호',
	SocialWork: '사회사업',
	SocialWorker: '사회 사업가',
	SoftwareDeveloper: '소프트웨어 개발자',
	Solo: '독주',
	Someone: '누구',
	Son: '아들',
	SortBy: '정렬 기준',
	SouthAmerica: '남아메리카',
	Speaker: '스피커',
	SpeakerSource: '스피커 소스',
	Speakers: '스피커',
	SpecifyPaymentMethod: '결제방법을 지정해주세요',
	SpeechLanguagePathology: '언어병리학',
	SpeechTherapist: '언어 치료사',
	SpeechTherapists: '언어 치료사',
	SpeechTherapy: '언어 치료',
	SportsMedicinePhysician: '스포츠 의학 의사',
	Spouse: '배우자',
	SpreadsheetColumnExample: '예를 들어 ',
	SpreadsheetColumns: '스프레드시트 열',
	SpreadsheetUploaded: '스프레드시트 업로드됨',
	SpreadsheetUploading: '업로드 중...',
	Staff: '직원',
	StaffAccessDescriptionAdmin: '관리자는 플랫폼의 모든 것을 관리할 수 있습니다.',
	StaffAccessDescriptionStaff: `직원은 자신이 만들거나 공유한 클라이언트, 메모 및 문서를 관리할 수 있습니다.
 이들과 함께 약속 일정을 잡고, 송장을 관리하세요.`,
	StaffContactAssignedSubject:
		'{actorProfileName}이(가) {totalCount, plural, =1 {{contactName1}} =2 {{contactName1}과(와) {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 {과(와) 다른 1명} other {외 #명의 고객}}을(를) 배정했습니다',
	StaffInboxAssignedNotificationSubject: '{actorProfileName}님이 {inboxName} 인박스를 당신과 공유했습니다.',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName}님이 {inboxName} 받은 편지함에 대한 액세스 권한을 제거했습니다.',
	StaffMembers: '직원',
	StaffMembersNumber: '{billedUsers, plural, one {# 팀 구성원} other {# 팀 구성원들}}',
	StaffSavedSuccessSnackbar: '팀원 정보가 성공적으로 저장되었습니다!',
	StaffSelectorAdminRole: '관리자',
	StaffSelectorStaffRole: '직원',
	StandardAppointment: '표준 예약',
	StandardColor: '작업 색상',
	StartAndEndTime: '시작 및 종료 시간',
	StartCall: '통화 시작',
	StartDate: '시작 날짜',
	StartDictating: '받아쓰기를 시작하세요',
	StartImport: '시작 가져오기',
	StartRecordErrorTitle: '녹음을 시작하는 중에 오류가 발생했습니다.',
	StartRecording: '녹음 시작',
	StartTimeIncrements: '시작 시간 증가',
	StartTimeIncrementsView: '{startTimeIncrements} 분 간격',
	StartTranscribing: '필사 시작',
	StartTranscribingNotes:
		'노트를 생성하려는 고객을 선택하십시오. 그런 다음 "필사 시작" 버튼을 클릭하여 녹음을 시작합니다.',
	StartTranscription: '녹음 시작',
	StartVideoCall: '영상통화 시작',
	StartWeekOn: '주를 시작하다',
	StartedBy: '에 의해 시작됨 ',
	Starter: '기동기',
	State: '상태',
	StateIndustrialAccidentProviderNumber: '국가산업재해보호기관 번호',
	StateLicenseNumber: '주 라이센스 번호',
	Statement: '성명',
	StatementDescriptor: '진술서 서술자',
	StatementDescriptorToolTip:
		'명세서 설명자는 고객의 은행 또는 신용카드 명세서에 표시됩니다. 5~22자여야 하며 회사 이름을 반영해야 합니다.',
	StatementNumber: '진술 {해시태그}',
	Status: '상태',
	StatusFieldPlaceholder: '상태 라벨을 입력하세요',
	StepFather: '의붓아버지',
	StepMother: '계모',
	Stockholm: '스톡홀름',
	StopIgnoreSendersDescription: `이 발신자를 무시하지 않으면 향후 대화가 '받은 편지함'으로 전송됩니다. 이 발신자를 무시하지 않으시겠습니까?`,
	StopIgnoring: '더 이상 무시하지 마세요',
	StopIgnoringSenders: '발신자 무시 중단',
	StopIgnoringSendersSuccess: '이메일 주소 <mark>{addresses}</mark> 무시 중단',
	StopSharing: '공유 중지',
	StopSharingLabel: 'carepatron.com에서 화면을 공유하고 있습니다.',
	Storage: '저장',
	StorageAlmostFullDescription: '🚀 지금 업그레이드하여 계정을 원활하게 유지하세요.',
	StorageAlmostFullTitle: '저장 공간 사용량이 {percentage}%에 도달했습니다!',
	StorageFullDescription: '플랜을 업그레이드하여 더 많은 저장 공간을 확보하세요.',
	StorageFullTitle: '	저장 공간이 가득 찼습니다.',
	Street: '거리',
	StripeAccountNotCompleteErrorCode:
		'온라인 결제는 {hasProviderName, select, true { {providerName}에 설정되어 있지 않습니다} other {이 제공업체에서 사용할 수 없습니다}}.',
	StripeAccountRejectedError: 'Stripe 계정이 거부되었습니다. 지원팀에 문의하세요.',
	StripeBalance: '스트라이프 밸런스',
	StripeChargesInfoToolTip: '직불카드로 요금을 청구할 수 있습니다 ',
	StripeFeesDescription:
		'Carepatron은 Stripe를 사용하여 귀하에게 빠르게 지불을 받고 귀하의 지불 정보를 안전하게 보호합니다. 사용 가능한 지불 방법은 지역에 따라 다르며 모든 주요 직불 카드 ',
	StripeFeesDescriptionItem1: '처리 수수료는 성공적인 거래마다 부과되며, {link}를 통해 확인할 수 있습니다.',
	StripeFeesDescriptionItem2: '지급은 매일 이루어지지만 최대 4일 동안 보류됩니다.',
	StripeFeesLinkToRatesText: '여기에서 가격을 확인하세요',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'죄송합니다. 온라인 결제를 사용하는 송장에는 최소 {minimumAmount}가 필요합니다.',
	StripePaymentsDisabled: '온라인 결제가 비활성화되었습니다. 결제 설정을 확인해 주세요.',
	StripePaymentsUnavailable: '결제 불가',
	StripePaymentsUnavailableDescription: '결제를 로드하는 동안 오류가 발생했습니다. 나중에 다시 시도하세요.',
	StripePayoutsInfoToolTip: '귀하의 은행 계좌로 지급을 받을 수 있습니다',
	StyleYourWorkspace: '<mark>스타일</mark>  작업 공간',
	StyleYourWorkspaceDescription1:
		'웹사이트에서 브랜드 자산을 가져왔습니다. 편집하거나 Carepatron 작업 공간으로 계속 진행하십시오.',
	StyleYourWorkspaceDescription2:
		'브랜드 자산을 사용하여 고객 경험을 원활하게 하기 위해 송장과 온라인 예약을 맞춤 설정하세요.',
	SubAdvanced: '고급',
	SubEssential: '필수',
	SubOrganization: '조직',
	SubPlus: '더하기',
	SubProfessional: '전문적인',
	Subject: '주제',
	Submit: '제출하다',
	SubmitElectronically: '전자적으로 제출하십시오',
	SubmitFeedback: '피드백 제출',
	SubmitFormValidationError: '모든 필수 항목을 올바르게 입력했는지 확인하고 다시 제출해 보세요.',
	Submitted: '제출된',
	SubmittedDate: '제출 날짜',
	SubscribePerMonth: '구독 {price} {isMonthly, select, true {월} other {년}}',
	SubscriptionDiscountDescription:
		'{percentOff}% 할인 {months, select, null { } other { {months, plural, one { #개월 동안} other { #개월 동안}}}}',
	SubscriptionFreeTrialDescription: '{endDate}까지 무료',
	SubscriptionPaymentFailedNotificationSubject:
		'귀하의 구독에 대한 결제를 완료할 수 없습니다. 결제 세부 정보를 확인하세요.',
	SubscriptionPlanDetailsHeader: '사용자당/월별 연간 청구',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} 월별 청구 (USD)',
	SubscriptionPlans: '구독 플랜',
	SubscriptionPlansDescription: '계획을 업그레이드하여 추가 혜택을 받고 진료를 원활하게 유지하십시오.',
	SubscriptionPlansDescriptionNoPermission:
		'지금은 업그레이드 권한이 없는 것 같습니다. 도움이 필요하면 관리자에게 문의하세요.',
	SubscriptionSettings: '구독 설정',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong>의 저장 공간 사용 중',
	SubscriptionSettingsStorageUsed: '{used} 중 {limit} 사용됨',
	SubscriptionSettingsUnlimitedStorage: '무제한 저장 가능',
	SubscriptionSummary: '구독 요약',
	SubscriptionUnavailableOverStorageLimit: '현재 사용량이 이 플랜의 저장 용량 한도를 초과했습니다.',
	SubscriptionUnpaidBannerButton: '구독으로 이동',
	SubscriptionUnpaidBannerDescription: '결제 정보가 정확한지 확인하고 다시 시도해 주세요.',
	SubscriptionUnpaidBannerTitle: '귀하의 구독에 대한 결제를 완료할 수 없습니다.',
	Subscriptions: '구독',
	SubscriptionsAndPayments: '구독 ',
	Subtotal: '소계',
	SuburbOrProvince: '교외/도',
	SuburbOrState: '교외/주',
	SuccessSavedNoteChanges: '노트 변경 사항을 성공적으로 저장했습니다.',
	SuccessShareDocument: '문서 공유가 성공했습니다',
	SuccessShareNote: '성공적으로 메모 공유됨',
	SuccessfullyCreatedValue: '{value}가 성공적으로 생성되었습니다.',
	SuccessfullyDeletedTranscriptionPart: '전사 부분을 성공적으로 삭제했습니다.',
	SuccessfullyDeletedValue: '{value}가 성공적으로 삭제되었습니다.',
	SuccessfullySubmitted: '성공적으로 제출되었습니다 ',
	SuccessfullyUpdatedClientSettings: '클라이언트 설정을 성공적으로 업데이트했습니다.',
	SuccessfullyUpdatedTranscriptionPart: '전사 부분을 성공적으로 업데이트했습니다.',
	SuccessfullyUpdatedValue: '{value}가 성공적으로 업데이트되었습니다.',
	SuggestedAIPoweredTemplates: 'AI 기반 템플릿 제안',
	SuggestedAITemplates: '제안된 AI 템플릿',
	SuggestedActions: '제안된 작업',
	SuggestedLocations: '제안된 위치',
	Suggestions: '제안',
	Summarise: 'AI 요약',
	SummarisingContent: '{title} 요약',
	Sunday: '일요일',
	Superbill: '슈퍼빌',
	SuperbillAndInsuranceBilling: '슈퍼빌 ',
	SuperbillAutomationMonthly: '활성 • 매월 마지막 날',
	SuperbillAutomationNoEmail: '자동 청구 문서를 성공적으로 보내려면 이 클라이언트의 이메일 주소를 추가하세요.',
	SuperbillAutomationNotActive: '비활성',
	SuperbillAutomationUpdateFailure: 'Superbill 자동화 설정을 업데이트하지 못했습니다.',
	SuperbillAutomationUpdateSuccess: 'Superbill 자동화 설정이 성공적으로 업데이트되었습니다.',
	SuperbillClientHelperText: '이 정보는 클라이언트 세부 정보에서 미리 채워집니다.',
	SuperbillNotFoundDescription: '공급업체에 연락하여 자세한 정보를 요청하거나 슈퍼빌을 다시 보내달라고 요청하세요.',
	SuperbillNotFoundTitle: '슈퍼빌을 찾을 수 없습니다',
	SuperbillNumber: '슈퍼 청구서 #{number}',
	SuperbillNumberAlreadyExists: 'Superbill 영수증 번호가 이미 존재합니다.',
	SuperbillPracticeHelperText: '이 정보는 연습 청구 설정에서 미리 채워집니다.',
	SuperbillProviderHelperText: '이 정보는 직원 세부 정보에서 미리 채워집니다.',
	SuperbillReceipts: '슈퍼빌 영수증',
	SuperbillsEmptyStateDescription: '슈퍼빌이 발견되지 않았습니다.',
	Surgeon: '외과 의사',
	Surgeons: '외과의사',
	SurgicalTechnologist: '수술 기술자',
	SwitchFromAnotherPlatform: '저는 다른 플랫폼에서 전환합니다.',
	SwitchToMyPortal: '내 포털로 전환',
	SwitchToMyPortalTooltip: `개인 포털에 접속하세요.
 당신이 탐색할 수 있도록
 고객의 포털 경험.`,
	SwitchWorkspace: '작업공간 전환',
	SwitchingToADifferentPlatform: '다른 플랫폼으로 전환',
	Sydney: '시드니',
	SyncCalendar: '일정 동기화',
	SyncCalendarModalDescription:
		'다른 팀원은 동기화된 캘린더를 볼 수 없습니다. 고객 약속은 Carepatron 내에서만 업데이트하거나 삭제할 수 있습니다.',
	SyncCalendarModalDisplayCalendar: 'Carepatron에서 내 일정을 표시합니다',
	SyncCalendarModalSyncToCarepatron: '내 캘린더를 Carepatron과 동기화',
	SyncCalendarModalSyncWithCalendar: 'Carepatron 약속을 내 캘린더와 동기화',
	SyncCarepatronAppointmentsWithMyCalendar: 'Carepatron 약속을 제 달력과 동기화하세요.',
	SyncGoogleCalendar: 'Google 캘린더 동기화',
	SyncInbox: 'Carepatron과 받은 편지함 동기화',
	SyncMyCalendarToCarepatron: '내 달력을 Carepatron과 동기화합니다.',
	SyncOutlookCalendar: 'Outlook 일정 동기화',
	SyncedFromExternalCalendar: '외부 일정에서 동기화됨',
	SyncingCalendarName: '{calendarName} 캘린더 동기화 중',
	SyncingFailed: '동기화에 실패했습니다',
	SystemGenerated: '시스템 생성',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: '테이블',
	TableRowLabel: '{value}에 대한 표 행',
	TagSelectorNoOptionsText: '새로운 태그를 추가하려면 "새로 만들기"를 클릭하세요.',
	Tags: '태그',
	TagsInputPlaceholder: '태그 검색 또는 생성',
	Task: '일',
	TaskAttendeeStatusUpdatedSuccess: '약속 상태 업데이트 완료',
	Tasks: '작업',
	Tax: '세',
	TaxAmount: '세금 금액',
	TaxID: '세금 ID',
	TaxIdType: '세금 ID 유형',
	TaxName: '세금 이름',
	TaxNumber: '세금 번호',
	TaxNumberType: '세금 번호 유형',
	TaxNumberTypeInvalid: '{type}는 잘못되었습니다.',
	TaxPercentageOfAmount: '{taxName} ({percentage}% 의 {amount})',
	TaxRate: '세율',
	TaxRatesDescription: '송장 항목에 적용될 세율을 관리합니다.',
	Taxable: '과세 대상',
	TaxonomyCode: '택소노미 코드',
	TeacherAssistant: '교사 조수',
	Team: '팀',
	TeamMember: '팀원',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {는} other {은}} 이 시간에 이미 예약되어 있습니다.{br}중복 예약을 피하려면 다른 시간을 선택하세요.',
	TeamMembers: '팀원들',
	TeamMembersColour: '팀원 색상',
	TeamMembersDetails: '팀원 세부 정보',
	TeamSize: '당신의 팀은 몇 명입니까?',
	TeamTemplates: '팀 템플릿',
	TeamTemplatesSectionDescription: '당신과 팀이 만든',
	TelehealthAndVideoCalls: '원격진료 ',
	TelehealthProvidedOtherThanInPatientCare: '입원 치료 이외의 치료를 위한 원격 진료 제공',
	TelehealthVideoCall: '원격진료 화상통화',
	Template: '주형',
	TemplateDescription: '템플릿 설명',
	TemplateDetails: '템플릿 세부 정보',
	TemplateEditModeViewSwitcherDescription: '템플릿 생성 및 편집',
	TemplateGallery: '커뮤니티 템플릿',
	TemplateImportCompletedNotificationSubject: '템플릿 가져오기 완료! {templateTitle} 사용 가능합니다.',
	TemplateImportFailedNotificationSubject: '파일 {fileName} 가져오기 실패했습니다.',
	TemplateName: '템플릿 이름',
	TemplateNotFound: '템플릿을 찾을 수 없습니다.',
	TemplatePreviewErrorMessage: '템플릿 미리보기를 로드하는 동안 오류가 발생했습니다.',
	TemplateResponderModeViewSwitcherDescription: '양식 미리보기 및 상호 작용',
	TemplateResponderModeViewSwitcherTooltipTitle: '응답자가 양식을 작성했을 때 어떻게 표시되는지 확인하세요.',
	TemplateSaved: '저장된 변경 사항',
	TemplateTitle: '템플릿 제목',
	TemplateType: '템플릿 유형',
	Templates: '템플릿',
	TemplatesCategoriesFilter: '카테고리별 필터링',
	TemplatesPublicTemplatesFilter: ' 커뮤니티/팀별 필터링',
	Text: '텍스트',
	TextAlign: '텍스트 정렬',
	TextColor: '텍스트 색상',
	ThankYouForYourFeedback: '피드백 주셔서 감사합니다!',
	ThanksForLettingKnow: '알려주셔서 감사합니다.',
	ThePaymentMethod: '결제방법',
	ThemThey: '그들/그들',
	Theme: '주제',
	ThemeAllColorsPickerTitle: '더 많은 테마',
	ThemeColor: '주제',
	ThemeColorDarkMode: '어두운',
	ThemeColorLightMode: '빛',
	ThemeColorModePickerTitle: '색상 모드',
	ThemeColorSystemMode: '체계',
	ThemeCpColorPickerTitle: 'Carepatron 테마',
	ThemePanelDescription: '밝은 모드와 어두운 모드 중에서 선택하고 테마 환경 설정을 사용자 지정하세요.',
	ThemePanelTitle: '외관',
	Then: '그 다음에',
	Therapist: '치료사',
	Therapists: '치료사',
	Therapy: '요법',
	Thick: '두꺼운',
	Thin: '얇은',
	ThirdPerson: '3인칭',
	ThisAndFollowingAppointments: '이 약속과 다음 약속',
	ThisAndFollowingMeetings: '이번 회의와 다음 회의',
	ThisAndFollowingReminders: '이 알림과 다음 알림',
	ThisAndFollowingTasks: '이 작업과 다음 작업',
	ThisAppointment: '이 약속',
	ThisMeeting: '이 회의',
	ThisMonth: '이번 달',
	ThisPerson: '이 사람',
	ThisReminder: '이 알림',
	ThisTask: '이 작업',
	ThisWeek: '이번 주',
	ThreeDay: '3일',
	Thursday: '목요일',
	Time: '시간',
	TimeAgoDays: '{number}일',
	TimeAgoHours: '{number}시간',
	TimeAgoMinutes: '{number}{number, plural, one {분} other {분}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: '시간 형식',
	TimeIncrement: '시간 증가',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {오전} other {오후}}',
	TimeslotSize: '타임슬롯 크기',
	Timestamp: '타임스탬프',
	Timezone: '시간대',
	TimezoneDisplay: '시간대 표시',
	TimezoneDisplayDescription: '시간대 표시 설정을 관리하세요.',
	Title: '제목',
	To: '에게',
	ToYourWorkspace: '귀하의 작업 공간으로',
	Today: '오늘',
	TodayInHoursPlural: '오늘 {count} {count, plural, one {시간} other {시간}}',
	TodayInMinsAbbreviated: '오늘 {count} {count, plural, one {분} other {분}}',
	ToggleHeaderCell: '헤더 셀 전환',
	ToggleHeaderCol: '헤더 열 전환',
	ToggleHeaderRow: '헤더 행 전환',
	Tokyo: '도쿄',
	Tomorrow: '내일',
	TomorrowAfternoon: '내일 오후',
	TomorrowMorning: '내일 아침',
	TooExpensive: '너무 비싸다',
	TooHardToSetUp: '설치하기 너무 어려움',
	TooManyFiles: '1개 이상의 파일이 감지되었습니다.',
	ToolsExample: '간단한 연습, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: '총',
	TotalAccountCredit: '총 계정 신용',
	TotalAdjustments: '총 조정',
	TotalAmountToCreditInCurrency: '총 크레딧 금액 ({currency})',
	TotalBilled: '총 청구 금액',
	TotalConversations: '{total} {total, plural, =0 {대화} one {대화} other {대화}}',
	TotalOverdue: '총 연체금',
	TotalOverdueTooltip:
		'총 연체 잔액에는 날짜 범위에 관계없이 무효화되거나 처리되지 않은 모든 미지불 송장이 포함됩니다.',
	TotalPaid: '총 지불 금액',
	TotalPaidTooltip: '총 지불 잔액에는 지정된 날짜 범위 내에 지불된 송장의 모든 금액이 포함됩니다.',
	TotalUnpaid: '미지급 총액',
	TotalUnpaidTooltip:
		'미지불 잔액에는 지정된 날짜 범위 내에 처리 중인 송장, 미지불 송장, 발송된 송장의 모든 미지불 금액이 포함됩니다.',
	TotalWorkflows: '{count} {count, plural, one {워크플로우} other {워크플로우}}',
	TotpSetUpManualEntryInstruction: '또는 아래 코드를 앱에 직접 입력할 수 있습니다.',
	TotpSetUpModalDescription: '다중 인증 요소를 설정하려면 인증 앱으로 QR 코드를 스캔하세요.',
	TotpSetUpModalTitle: 'MFA 장치 설정',
	TotpSetUpSuccess: '모두 설정되었습니다! MFA가 활성화되었습니다.',
	TotpSetupEnterAuthenticatorCodeInstruction: '인증 앱에서 생성된 코드를 입력하세요.',
	Transcribe: '고쳐 쓰다',
	TranscribeLanguageSelector: '입력 언어를 선택하세요',
	TranscribeLiveAudio: '라이브 오디오를 필사하다',
	Transcribing: '오디오를 필사하는 중...',
	TranscribingIn: '필사 중',
	Transcript: '성적 증명서',
	TranscriptRecordingCompleteInfo: '녹음이 완료되면 여기에서 대본을 볼 수 있습니다.',
	TranscriptSuccessSnackbar: '성공적으로 사본을 처리했습니다.',
	Transcription: '전사',
	TranscriptionEmpty: '사용 가능한 전사본이 없습니다.',
	TranscriptionEmptyHelperMessage: '이 필사본은 아무것도 수집하지 못했습니다. 다시 시작하고 다시 시도하세요.',
	TranscriptionFailedNotice: '이 필사본은 성공적으로 처리되지 않았습니다.',
	TranscriptionIdleMessage:
		'오디오가 들리지 않습니다. 더 시간이 필요하시면 {timeValue}초 안에 응답해 주세요. 그렇지 않으면 세션이 종료됩니다.',
	TranscriptionInProcess: '전사 중...',
	TranscriptionIncompleteNotice: '이 전사본의 일부가 성공적으로 처리되지 않았습니다.',
	TranscriptionOvertimeWarning: '{scribeType} 세션은 **{timeValue} {unit}**에 종료됩니다.',
	TranscriptionPartDeleteMessage: '이 필사본 부분을 삭제하시겠습니까?',
	TranscriptionText: '음성을 텍스트로',
	TranscriptsPending: '세션이 종료된 후 여기에서 대본을 확인하실 수 있습니다.',
	Transfer: '옮기다',
	TransferAndDelete: '전송 및 삭제',
	TransferOwnership: '소유권 이전',
	TransferOwnershipConfirmationModalDescription:
		'해당 작업은 소유권을 귀하에게 다시 이전하는 경우에만 취소할 수 있습니다.',
	TransferOwnershipDescription: '이 작업 공간의 소유권을 다른 팀원에게 이전합니다.',
	TransferOwnershipSuccessSnackbar: '소유권이 성공적으로 이전되었습니다!',
	TransferOwnershipToMember: '{staff}로 이 작업 공간을 전송하시겠습니까?',
	TransferStatusAlert:
		'{numberOfStatuses, plural, one {이 상태} other {이러한 상태}}를 제거하면 {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} 클라이언트 상태.</strong>} other {<strong>{numberOfAffectedRecords} 클라이언트 상태.</strong>}}에 영향을 미칩니다.',
	TransferStatusDescription:
		'삭제를 진행하기 전에 이 클라이언트에 대해 다른 상태를 선택하세요. 이 작업은 취소할 수 없습니다.',
	TransferStatusLabel: '새로운 상태로 전환',
	TransferStatusPlaceholder: '기존 상태를 선택하세요',
	TransferStatusTitle: '삭제 전 전송 상태',
	TransferTaskAttendeeStatusAlert:
		'이 상태를 제거하면 <strong>{number}개의 미래 약속 {number, plural, one {상태} other {상태}}</strong>에 영향을 미칩니다.',
	TransferTaskAttendeeStatusDescription:
		'삭제를 진행하기 전에 이 고객에 대한 다른 상태를 선택하세요. 이 작업은 되돌릴 수 없습니다.',
	TransferTaskAttendeeStatusSubtitle: '예약 상태',
	TransferTaskAttendeeStatusTitle: '삭제 전 전송 상태',
	Trash: '쓰레기',
	TrashDeleteItemsModalConfirm: '확인하려면 {confirmationText} 입력하세요.',
	TrashDeleteItemsModalDescription:
		'다음 {count, plural, one {항목} other {항목}}은 영구적으로 삭제되며 복구할 수 없습니다.',
	TrashDeleteItemsModalTitle: '{count, plural, one {항목} other {항목}}을 영원히 삭제합니다.',
	TrashDeletedAllItems: '모든 항목을 삭제했습니다',
	TrashDeletedItems: '삭제된 {count, plural, one {항목} other {항목들}}',
	TrashDeletedItemsFailure: '휴지통에서 항목을 삭제하지 못했습니다.',
	TrashLocationAppointmentType: '달력',
	TrashLocationBillingAndPaymentsType: '청구 및 결제',
	TrashLocationContactType: '고객',
	TrashLocationNoteType: '노트 ',
	TrashRestoreItemsModalDescription: '다음 {count, plural, one {항목} other {항목}}이 복원됩니다.',
	TrashRestoreItemsModalTitle: '{count, plural, one {항목} other {항목}} 복원',
	TrashRestoredAllItems: '모든 아이템을 복구했습니다',
	TrashRestoredItems: '복원된 {count, plural, one {항목} other {항목들}}',
	TrashRestoredItemsFailure: '휴지통에서 항목을 복원하지 못했습니다.',
	TrashSuccessfullyDeletedItem: '{type} 삭제 완료',
	Trigger: '방아쇠',
	Troubleshoot: '문제 해결',
	TryAgain: '다시 시도하세요',
	Tuesday: '화요일',
	TwoToTen: '2 - 10',
	Type: '유형',
	TypeHere: '여기에 입력하세요...',
	TypeToConfirm: '확인하려면 {keyword}를 입력하세요.',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: '제목 1',
	TypographyHeading2: '제목 2',
	TypographyHeading3: '제목 3',
	TypographyHeading4: '제목 4',
	TypographyHeading5: '제목 5',
	TypographyP: '피',
	TypographyParagraph: '절',
	UnableToCompleteAction: '작업을 완료할 수 없습니다.',
	UnableToPrintDocument: '문서를 인쇄할 수 없습니다. 나중에 다시 시도하세요.',
	Unallocated: '할당되지 않음',
	UnallocatedPaymentDescription: `이 지불은 청구 항목에 완전히 할당되지 않았습니다.
 지불되지 않은 항목에 할당을 추가하거나, 크레딧 또는 환불을 발행합니다.`,
	UnallocatedPaymentTitle: '할당되지 않은 지불',
	UnallocatedPayments: '할당되지 않은 지불',
	Unarchive: '보관 취소',
	Unassigned: '할당되지 않음',
	UnauthorisedInvoiceSnackbar: '이 고객의 송장을 관리할 수 있는 권한이 없습니다.',
	UnauthorisedSnackbar: '이 작업을 수행할 권한이 없습니다.',
	Unavailable: '없는',
	Uncategorized: '분류되지 않음',
	Unclaimed: '청구되지 않음',
	UnclaimedAmount: '미수금',
	UnclaimedItems: '청구되지 않은 품목',
	UnclaimedItemsMustBeInCurrency: '다음 통화로만 지원됩니다: {currencies}',
	Uncle: '삼촌',
	Unconfirmed: '확인되지 않음',
	Underline: '밑줄',
	Undo: '끄르다',
	Unfavorite: '즐겨찾기 해제',
	Uninvoiced: '청구되지 않음',
	UninvoicedAmount: '미청구 금액',
	UninvoicedAmounts: '{count, plural, =0 {미청구 금액 없음} one {미청구 금액} other {미청구 금액}}',
	Unit: '단위',
	UnitedKingdom: '영국',
	UnitedStates: '미국',
	UnitedStatesEast: '미국 - 동부',
	UnitedStatesWest: '미국 - 서부',
	Units: '단위',
	UnitsIsRequired: '단위가 필요합니다',
	UnitsMustBeGreaterThanZero: '단위는 0보다 커야 합니다.',
	UnitsPlaceholder: '1',
	Unknown: '알 수 없음',
	Unlimited: '무제한',
	Unlock: '터놓다',
	UnlockNoteHelper: '새로운 변경을 하기 전에 편집자는 메모의 잠금을 해제해야 합니다.',
	UnmuteAudio: '오디오 음소거 해제',
	UnmuteEveryone: '모든 사람의 음소거 해제',
	Unpaid: '무급',
	UnpaidInvoices: '미납 송장',
	UnpaidItems: '미지불 품목',
	UnpaidMultiple: '무급',
	Unpublish: '게시 취소',
	UnpublishTemplateConfirmationModalPrompt:
		'<span>{title}</span>을 제거하면 Carepatron 커뮤니티에서 이 리소스가 삭제됩니다. 이 작업은 되돌릴 수 없습니다.',
	UnpublishToCommunitySuccessMessage: '커뮤니티에서 ‛{title}’이 성공적으로 삭제되었습니다.',
	Unread: '읽히지 않는',
	Unrecognised: '인식되지 않음',
	UnrecognisedDescription:
		'이 결제 방법은 현재 애플리케이션 버전에서 인식되지 않습니다. 이 결제 방법을 보고 편집하려면 브라우저를 새로 고침하여 최신 버전을 받으세요.',
	UnsavedChanges: '저장되지 않은 변경 사항',
	UnsavedChangesPromptContent: '닫기 전에 변경 사항을 저장하시겠습니까?',
	UnsavedChangesPromptTitle: '저장되지 않은 변경 사항이 있습니다',
	UnsavedNoteChangesWarning: '변경한 내용은 저장되지 않을 수 있습니다.',
	UnsavedTemplateChangesWarning: '변경한 내용은 저장되지 않을 수 있습니다.',
	UnselectAll: '모두 선택 취소',
	Until: '까지',
	UntitledConversation: '제목 없음 대화',
	UntitledFolder: '제목 없는 폴더',
	UntitledNote: '제목 없는 메모',
	UntitledSchedule: '제목 없는 일정',
	UntitledSection: '제목 없는 섹션',
	UntitledTemplate: '제목 없는 템플릿',
	Unverified: '검증되지 않음',
	Upcoming: '다가올',
	UpcomingAppointments: '예정된 약속',
	UpcomingDateOverridesEmpty: '날짜 재정의가 발견되지 않았습니다.',
	UpdateAvailabilityScheduleFailure: '이용 가능 일정을 업데이트하지 못했습니다.',
	UpdateAvailabilityScheduleSuccess: '가용성 일정이 성공적으로 업데이트되었습니다.',
	UpdateInvoicesOrClaimsAgainstBillable: '새로운 가격을 참석자의 송장과 청구에 적용하시겠습니까?',
	UpdateLink: '링크 업데이트',
	UpdatePrimaryEmailWarningDescription:
		'고객의 이메일 주소를 변경하면 기존 약속 및 노트에 대한 액세스 권한이 손실됩니다.',
	UpdatePrimaryEmailWarningTitle: '고객 이메일 변경',
	UpdateSettings: '설정 업데이트',
	UpdateStatus: '상태 업데이트',
	UpdateSuperbillReceiptFailure: 'Superbill 영수증 업데이트에 실패했습니다.',
	UpdateSuperbillReceiptSuccess: 'Superbill 영수증이 성공적으로 업데이트되었습니다.',
	UpdateTaskBillingDetails: '결제 세부 정보 업데이트',
	UpdateTaskBillingDetailsDescription:
		'약속 가격이 변경되었습니다. 참석자의 청구 항목, 송장 및 청구에 새 가격을 적용하시겠습니까? 진행하려는 업데이트를 선택하세요.',
	UpdateTemplateFolderSuccessMessage: '폴더가 성공적으로 업데이트되었습니다.',
	UpdateUnpaidInvoices: '미납 송장 업데이트',
	UpdateUserInfoSuccessSnackbar: '사용자 정보가 성공적으로 업데이트되었습니다!',
	UpdateUserSettingsSuccessSnackbar: '사용자 설정이 성공적으로 업데이트되었습니다!',
	Upgrade: '치받이',
	UpgradeForSMSReminder: '무제한 SMS 알림을 받으려면 <b>Professional</b> 로 업그레이드하세요.',
	UpgradeNow: '지금 업그레이드하세요',
	UpgradePlan: '업그레이드 계획',
	UpgradeSubscriptionAlertDescription:
		'저장 공간이 부족합니다. 추가 저장 공간을 확보하고 진료를 원활하게 운영하려면 요금제를 업그레이드하세요!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'저장 공간이 부족합니다. <span>관리자 권한</span>이 있는 연습 그룹의 다른 사람에게 문의하여 추가 저장 공간을 확보하고 연습을 원활하게 진행할 수 있도록 계획을 업그레이드하십시오!',
	UpgradeSubscriptionAlertTitle: '구독을 업그레이드할 시간입니다.',
	UpgradeYourPlan: '계획을 업그레이드하세요',
	UploadAudio: '오디오 업로드',
	UploadFile: '파일 업로드',
	UploadFileDescription: '어떤 소프트웨어 플랫폼에서 전환하시는 건가요?',
	UploadFileMaxSizeError: '파일이 너무 큽니다. 최대 파일 크기는 {fileSizeLimit}입니다.',
	UploadFileSizeLimit: '크기 제한 {size}MB',
	UploadFileTileDescription: 'CSV, XLS, XLSX 또는 ZIP 파일을 사용하여 고객을 업로드하십시오.',
	UploadFileTileLabel: '파일 업로드',
	UploadFiles: '파일 업로드',
	UploadIndividually: '개별적으로 파일 업로드',
	UploadLogo: '로고 업로드',
	UploadPhoto: '사진 업로드',
	UploadToCarepatron: 'Carepatron에 업로드',
	UploadYourLogo: '로고를 업로드하세요',
	UploadYourTemplates: '템플릿을 업로드하면 변환해 드립니다.',
	Uploading: '업로드 중',
	UploadingAudio: '오디오를 업로드하는 중...',
	UploadingFiles: '파일 업로드',
	UrlLink: 'URL 링크',
	UsageCount: '{count}번 사용됨',
	UsageLimitValue: '{사용됨} of {제한} 사용됨',
	UsageValue: '{used} 사용됨',
	Use: '사용',
	UseAiToAutomateYourWorkflow: 'AI를 활용해 업무 흐름을 자동화하세요!',
	UseAsDefault: '기본값으로 사용',
	UseCustom: '사용자 정의 사용',
	UseDefault: '기본값 사용',
	UseDefaultFilters: '기본 필터 사용',
	UseTemplate: '템플릿 사용',
	UseThisCard: '이 카드를 사용하세요',
	UseValue: '"{value}"를 사용하세요.',
	UseWorkspaceDefault: '작업 공간 기본값 사용',
	UserIsTyping: '{name} 님이 입력 중입니다...',
	Username: '사용자 이름',
	Users: '사용자',
	VAT: '큰 통',
	ValidUrl: 'URL 링크는 유효한 URL이어야 합니다.',
	Validate: '검증하다',
	Validated: '검증됨',
	Validating: '검증 중',
	ValidatingContent: '콘텐츠 검증 중...',
	ValidatingTranscripts: '필사본 검증 중...',
	ValidationConfirmPasswordRequired: '비밀번호 확인이 필요합니다',
	ValidationDateMax: '{max} 이전이어야 합니다.',
	ValidationDateMin: '{min} 이후여야 합니다.',
	ValidationDateRange: '시작일과 종료일은 필수입니다.',
	ValidationEndDateMustBeAfterStartDate: '종료 날짜는 시작 날짜 이후여야 합니다.',
	ValidationMixedDefault: '이것은 유효하지 않습니다',
	ValidationMixedRequired: '이것은 필수입니다',
	ValidationNumberInteger: '정수여야 합니다',
	ValidationNumberMax: '{max} 이하이어야 합니다.',
	ValidationNumberMin: '{min} 이상이어야 합니다.',
	ValidationPasswordNotMatching: '비밀번호가 일치하지 않습니다',
	ValidationPrimaryAddressIsRequired: '기본값으로 설정할 경우 주소가 필요합니다.',
	ValidationPrimaryPhoneNumberIsRequired: '기본값으로 설정할 경우 전화번호가 필요합니다.',
	ValidationServiceMustBeNotBeFuture: '서비스는 현재 날짜 또는 미래 날짜가 될 수 없습니다.',
	ValidationStringEmail: '유효한 이메일이어야 합니다.',
	ValidationStringMax: '{max}자 이하이어야 합니다.',
	ValidationStringMin: '{min}자 이상이어야 합니다.',
	ValidationStringPhoneNumber: '유효한 전화번호여야 합니다.',
	ValueMinutes: '{value} 분',
	VerbosityConcise: '간결한',
	VerbosityDetailed: '상세한',
	VerbosityStandard: '기준',
	VerbositySuperDetailed: '매우 자세하다',
	VerificationCode: '검증코드',
	VerificationEmailDescription: '귀하의 이메일 주소와 방금 전송된 인증 코드를 입력해 주세요.',
	VerificationEmailSubtitle: '스팸 폴더를 확인하세요 - 이메일이 도착하지 않은 경우',
	VerificationEmailTitle: '이메일 확인',
	VerificationOption: '이메일 확인',
	Verified: '검증됨',
	Verify: '확인하다',
	VerifyAndSubmit: '확인 및 제출',
	VerifyEmail: '이메일 확인',
	VerifyEmailAccessCode: '확인코드',
	VerifyEmailAddress: '이메일 주소 확인',
	VerifyEmailButton: '확인 및 로그아웃',
	VerifyEmailSentSnackbar: '확인 이메일이 전송되었습니다. 받은 편지함을 확인하세요.',
	VerifyEmailSubTitle: '이메일이 도착하지 않으면 스팸 폴더를 확인하세요.',
	VerifyEmailSuccessLogOutSnackbar: '성공! 변경 사항을 적용하려면 로그아웃하세요.',
	VerifyEmailSuccessSnackbar: '성공! 이메일이 확인되었습니다. 확인된 계정으로 계속하려면 로그인하세요.',
	VerifyEmailTitle: '이메일을 확인하세요',
	VerifyNow: '지금 확인하세요',
	Veterinarian: '수의사',
	VideoCall: '영상통화',
	VideoCallAudioInputFailed: '오디오 입력 장치가 작동하지 않습니다.',
	VideoCallAudioInputFailedMessage: '설정을 열고 마이크 소스가 제대로 설정되었는지 확인하세요.',
	VideoCallChatBanner: '메시지는 통화에 참여한 모든 사람이 볼 수 있으며 통화가 종료되면 삭제됩니다.',
	VideoCallChatSendBtn: '메시지를 보내다',
	VideoCallChatTitle: '채팅',
	VideoCallDisconnectedMessage: '네트워크 연결이 끊어졌습니다. 다시 연결하려고 합니다.',
	VideoCallOptionInfo: 'Zoom이 연결되지 않은 경우 Carepatron이 귀하의 약속에 대한 화상 통화를 관리합니다.',
	VideoCallTilePaused: '네트워크 문제로 인해 이 영상이 일시 중지되었습니다.',
	VideoCallTranscriptionFormDescription: '언제든지 이러한 설정을 조정할 수 있습니다.',
	VideoCallTranscriptionFormHeading: 'AI Scribe를 사용자 지정하세요',
	VideoCallTranscriptionFormLanguageField: '생성된 출력 언어',
	VideoCallTranscriptionFormNoteTemplateField: '기본 노트 템플릿 설정',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'AI가 포함된 템플릿을 찾을 수 없습니다.',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: '템플릿을 선택하세요',
	VideoCallTranscriptionPronounField: '당신의 대명사',
	VideoCallTranscriptionRecordingNote: '세션이 끝나면 생성된 **{noteTemplate} 노트**와  녹취록을 받게 됩니다.',
	VideoCallTranscriptionReferClientField: '클라이언트를 다음과 같이 참조합니다.',
	VideoCallTranscriptionReferPractitionerField: '실무자에게 참조',
	VideoCallTranscriptionTitle: 'AI 서기',
	VideoCallTranscriptionVerbosityField: '다변',
	VideoCallTranscriptionWritingPerspectiveField: '글쓰기 관점',
	VideoCalls: '비디오 통화',
	VideoConferencing: '비디오 회의',
	VideoOff: '비디오가 꺼져 있습니다',
	VideoOn: '비디오가 꺼져 있습니다',
	VideoQual360: '낮은 품질(360p)',
	VideoQual540: '중간 품질(540p)',
	VideoQual720: '고화질(720p)',
	View: '보다',
	ViewAll: '모두 보기',
	ViewAppointment: '예약 보기',
	ViewBy: '보기 기준',
	ViewClaim: '클레임 보기',
	ViewCollection: '컬렉션 보기',
	ViewDetails: '자세한 내용 보기',
	ViewEnrollment: '등록 보기',
	ViewPayment: '결제 보기',
	ViewRecord: '기록보기',
	ViewRemittanceAdvice: '송금 내역 보기',
	ViewRemittanceAdviceHeader: '송금 명세서',
	ViewRemittanceAdviceSubheader: '청구 {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: '설정 보기',
	ViewStripeDashboard: 'Stripe 대시보드 보기',
	ViewTemplate: '템플릿 보기',
	ViewTemplates: '템플릿 보기',
	ViewableBy: '볼 수 있는 사람',
	ViewableByHelper:
		'귀하와 팀은 귀하가 게시한 노트에 항상 액세스할 수 있습니다. 이 노트를 클라이언트 및/또는 그들의 관계와 공유하도록 선택할 수 있습니다.',
	Viewer: '뷰어',
	VirtualLocation: '가상 위치',
	VisibleTo: '볼 수 있는 대상',
	VisitOurHelpCentre: '도움말 센터 방문하기',
	VisualEffects: '시각 효과',
	VoiceFocus: '음성 초점',
	VoiceFocusLabel: '음성이 아닌 마이크 소리를 필터링합니다.',
	Void: '무효의',
	VoidCancelPriorClaim: 'Void/Cancel prior claim이전 청구 무효화/취소',
	WaitingforMins: '{count}분 기다리는 중',
	Warning: '경고',
	WatchAVideo: '비디오 시청',
	WatchDemoVideo: '데모 비디오 보기',
	WebConference: '웹 컨퍼런스',
	WebConferenceOrVirtualLocation: '웹 컨퍼런스 / 가상 위치',
	WebDeveloper: '웹 개발자',
	WebsiteOptional: '웹사이트 <span>(선택)</span>',
	WebsiteUrl: '웹사이트 URL',
	Wednesday: '수요일',
	Week: '주',
	WeekPlural: '{count, plural, one {주} other {주}}',
	Weekly: '주간',
	WeeksPlural: '{age, plural, one {#주} other {#주}}',
	WelcomeBack: '환영합니다',
	WelcomeBackName: '다시 오신 것을 환영합니다, {name}',
	WelcomeName: '환영합니다 {name}',
	WelcomeToCarepatron: 'Carepatron에 오신 것을 환영합니다',
	WhatCanIHelpWith: '무엇을 도와드릴까요?',
	WhatDidYouLikeResponse: '이 응답에 대해 어떤 부분이 좋았나요?',
	WhatIsCarepatron: '케어패트론이란?',
	WhatMadeYouCancel: `무슨 이유로 계획을 취소하게 되었나요?
 해당되는 모든 사항을 체크하세요.`,
	WhatServicesDoYouOffer: '무엇<mark> 서비스</mark> 제공하시나요?',
	WhatServicesDoYouOfferDescription: '나중에 서비스를 편집하거나 추가할 수 있습니다.',
	WhatsYourAvailability: '<mark>시간</mark>은 어떻게 되시나요?',
	WhatsYourAvailabilityDescription: '나중에 더 많은 일정을 추가할 수 있습니다.',
	WhatsYourBusinessName: '당신은 무엇입니까<mark> 상호?</mark>',
	WhatsYourTeamSize: '당신은 무엇입니까<mark> 팀 규모?</mark>',
	WhatsYourTeamSizeDescription: '이렇게 하면 작업 공간을 올바르게 설정하는 데 도움이 됩니다.',
	WhenThisHappens: '이런 일이 발생하면:',
	WhichBestDescribesYou: '어느 것이 가장 좋은가<mark> 당신을 묘사하는 것인가요?</mark>',
	WhichPlatforms: '어떤 플랫폼인가요?',
	Wife: '부인',
	WorkflowDescription: '워크플로 설명',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'템플릿은 더 원활한 프로세스를 위해 워크플로우에 연결될 수 있습니다. 연결된 워크플로우를 보면 쉽게 추적하고 업데이트할 수 있습니다.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: '공통 트리거를 기반으로 SMS + 이메일 연결',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: '워크플로 자동화',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: '자동화된 워크플로우',
	WorkflowTemplateConfigKey_Body: '본문',
	WorkflowTemplateConfigKey_Branding_IsVisible: '브랜딩 보여주기',
	WorkflowTemplateConfigKey_Content: '내용',
	WorkflowTemplateConfigKey_Footer: '푸터',
	WorkflowTemplateConfigKey_Footer_IsVisible: '풋터 표시',
	WorkflowTemplateConfigKey_Header: '헤더',
	WorkflowTemplateConfigKey_Header_IsVisible: '헤더 표시',
	WorkflowTemplateConfigKey_SecurityFooter: '보안 푸터',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: '보안 푸터 표시',
	WorkflowTemplateConfigKey_Subject: '제목',
	WorkflowTemplateConfigKey_Title: '제목',
	WorkflowTemplateDeleteConfirmationMessage: '이 템플릿을 삭제하시겠습니까? 이 작업은 취소할 수 없습니다.',
	WorkflowTemplateDeleteConfirmationTitle: '알림 템플릿 삭제',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'확실해요? 이 작업은 {locale} 버전만 삭제합니다. 다른 언어는 영향을 받지 않습니다. 이 작업은 취소할 수 없습니다.',
	WorkflowTemplateDeleteLocalisationDialogTitle: '‘{locale}’ 템플릿 삭제',
	WorkflowTemplateDeletedSuccess: '알림 템플릿이 성공적으로 삭제되었습니다.',
	WorkflowTemplateEditorDetailsTab: '템플릿 세부 정보',
	WorkflowTemplateEditorEmailContent: '이메일 내용',
	WorkflowTemplateEditorEmailContentTab: '이메일 내용',
	WorkflowTemplateEditorThemeTab: '테마',
	WorkflowTemplatePreviewerAlert: '미리보기는 고객이 보게 될 내용을 보여주기 위해 샘플 데이터를 사용합니다.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'확실해요? 이렇게 하면 버전이 시스템의 기본 템플릿으로 재설정됩니다. 이 작업은 취소할 수 없습니다.',
	WorkflowTemplateResetEmailContentDialogTitle: '템플릿 재설정',
	WorkflowTemplateSendTestEmail: '테스트 이메일 보내기',
	WorkflowTemplateSendTestEmailDialogDescription: '본인에게 테스트 이메일을 보내 이메일 설정을 확인해 보세요.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: '받는 사람 이메일',
	WorkflowTemplateSendTestEmailDialogSendButton: '테스트 보내기',
	WorkflowTemplateSendTestEmailDialogTitle: '테스트 이메일 보내기',
	WorkflowTemplateSendTestEmailSuccess: '성공! <mark>{templateName}</mark> 테스트 이메일이 발송되었습니다.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'템플릿을 관리하고 여러 언어 버전을 추가하여 클라이언트와 효과적으로 소통하십시오.',
	WorkflowTemplateTemplateEditor: '템플릿 편집기',
	WorkflowTemplateTranslateLocaleError: '컨텐츠 번역 중 오류가 발생했습니다.',
	WorkflowTemplateTranslateLocaleSuccess: '콘텐츠를 **{locale}**으로 성공적으로 번역했습니다.',
	WorkflowsAndReminders: '워크플로 ',
	WorkflowsManagement: '워크플로 관리',
	WorksheetAndHandout: '워크시트/핸드아웃',
	WorksheetsAndHandoutsDescription: '고객 참여 및 교육을 위해',
	Workspace: '작업 공간',
	WorkspaceBranding: '작업 공간 브랜딩',
	WorkspaceBrandingDescription: `귀하의 작업 공간을 통합된 스타일로 손쉽게 브랜드화하여 반영하세요.
 전문성과 개성. 아름다운 온라인 예약을 위한 송장 맞춤화
 고객 경험.`,
	WorkspaceName: '작업공간 이름',
	Workspaces: '작업 공간',
	WriteOff: '대손충당',
	WriteOffModalDescription:
		'<mark>{count} {count, plural, one {줄 항목} other {줄 항목들}}</mark>을 상각해야 합니다.',
	WriteOffModalTitle: '대손 조정',
	WriteOffReasonHelperText: '이는 내부 메모이므로 고객에게는 표시되지 않습니다.',
	WriteOffReasonPlaceholder: '청구 가능한 거래를 검토할 때 대손 이유를 추가하면 도움이 될 수 있습니다.',
	WriteOffTotal: '총 감면 ({currencyCode})',
	Writer: '작가',
	Yearly: '연간',
	YearsPlural: '{age, plural, one {#년} other {#년}}',
	Yes: '예',
	YesArchive: '네, 보관합니다',
	YesDelete: '네, 삭제합니다',
	YesDeleteOverride: '예, 오버라이드 삭제',
	YesDeleteSection: '네, 삭제합니다',
	YesDisconnect: '네, 연결 해제',
	YesEnd: '네, 끝',
	YesEndTranscription: '네, 전사를 끝냅니다',
	YesImFineWithThat: '네, 저는 그걸로 괜찮습니다',
	YesLeave: '네, 나가세요',
	YesMinimize: '네, 최소화',
	YesOrNoAnswerTypeDescription: '답변 유형 구성',
	YesOrNoFormPrimaryText: '예 | 아니오',
	YesOrNoFormSecondaryText: '예 또는 아니오 옵션을 선택하세요',
	YesProceed: '네, 진행하세요',
	YesRemove: '네, 제거합니다',
	YesRestore: '네, 복원합니다',
	YesStopIgnoring: '네, 무시하지 마세요',
	YesTransfer: '네, 전송합니다.',
	Yesterday: '어제',
	YogaInstructor: '요가 강사',
	You: '너',
	YouArePresenting: '당신은 발표하고 있습니다',
	YouCanChooseMultiple: '여러개를 선택할 수 있습니다',
	YouCanSelectMultiple: '여러개를 선택할 수 있습니다',
	YouHaveOngoingTranscription: '진행 중인 필사 작업이 있습니다.',
	YourAnswer: '당신의 답변',
	YourDisplayName: '표시 이름',
	YourSpreadsheetColumns: '스프레드시트 열',
	YourTeam: '당신의 팀',
	ZipCode: '우편 번호',
	Zoom: '줌',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'이 팀원에 대한 Zoom 통화를 추가할 수 없습니다. <a>자세한 내용은 지원 문서를 참조하세요.</a>',
};

export default items;
