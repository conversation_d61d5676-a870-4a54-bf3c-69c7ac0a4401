import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'Petunjuk AI',
	ATeamMemberIsRequired: '<PERSON><PERSON><PERSON><PERSON> anggota tim',
	AboutClient: '<PERSON>tan<PERSON>',
	AcceptAppointment: 'Terima kasih telah mengkonfirmasi janji temu <PERSON>',
	AcceptTermsAndConditionsRequired: 'Terima Persyaratan ',
	Accepted: '<PERSON><PERSON><PERSON>',
	AccessGiven: 'Akses diberikan',
	AccessPermissions: 'Izin akses',
	AccessType: 'Jenis akses',
	Accident: 'Ke<PERSON><PERSON><PERSON>',
	Account: 'Akun',
	AccountCredit: 'Kredit akun',
	Accountant: 'Akuntan',
	Action: 'Tindakan',
	Actions: 'Tindakan',
	Active: 'Aktif',
	ActiveTags: 'Tag aktif',
	ActiveUsers: 'Pengguna Aktif',
	Activity: 'Aktivitas',
	Actor: 'Aktor',
	Acupuncture: '<PERSON><PERSON>punk<PERSON>',
	Acupuncturist: '<PERSON><PERSON>puntur',
	Acupuncturists: 'Ah<PERSON> Akupuntur',
	AcuteManifestationOfAChronicCondition: 'Manifestasi akut dari kondisi kronis',
	Add: 'Menambahkan',
	AddADescription: 'Tambahkan deskripsi',
	AddALocation: 'Tambahkan lokasi',
	AddASecondTimezone: 'Tambahkan zona waktu kedua',
	AddAddress: 'Tambahkan alamat',
	AddAnother: '  Tambahkan lagi',
	AddAnotherAccount: 'Tambahkan akun lain',
	AddAnotherContact: 'Tambahkan kontak lain',
	AddAnotherOption: 'Tambahkan opsi lain',
	AddAnotherTeamMember: 'Tambahkan anggota tim lainnya',
	AddAvailablePayers: '+ Tambah pembayar yang tersedia',
	AddAvailablePayersDescription:
		'Cari pembayar untuk ditambahkan ke daftar pembayar ruang kerja Anda. Setelah menambahkannya, Anda dapat mengelola pendaftaran atau menyesuaikan detail pembayar sesuai kebutuhan.',
	AddCaption: 'Tambahkan keterangan',
	AddClaim: 'Tambahkan klaim',
	AddClientFilesModalDescription: 'Untuk membatasi akses, pilih opsi di kotak centang "Dapat dilihat oleh"',
	AddClientFilesModalTitle: 'Unggah file untuk {name}',
	AddClientNoteButton: 'Tambahkan catatan',
	AddClientNoteModalDescription:
		'Tambahkan konten ke catatan Anda. Gunakan bagian "Dapat dilihat oleh" untuk memilih satu atau beberapa grup yang dapat melihat catatan khusus ini.',
	AddClientNoteModalTitle: 'Tambahkan catatan',
	AddClientOwnerRelationshipModalDescription:
		'Mengundang klien akan memungkinkan mereka untuk mengelola informasi profil mereka sendiri dan mengelola akses pengguna ke informasi profil mereka.',
	AddClientOwnerRelationshipModalTitle: 'Undang klien',
	AddCode: 'Tambahkan kode',
	AddColAfter: 'Tambahkan kolom setelah',
	AddColBefore: 'Tambahkan kolom sebelumnya',
	AddCollection: 'Tambahkan Koleksi',
	AddColor: 'Tambahkan warna',
	AddColumn: 'Tambahkan kolom',
	AddContactRelationship: 'Tambahkan hubungan kontak',
	AddContacts: 'Tambahkan kontak',
	AddCustomField: 'Tambahkan bidang khusus',
	AddDate: 'Tambahkan tanggal',
	AddDescription: 'Tambahkan deskripsi',
	AddDetail: 'Tambahkan detail',
	AddDisplayName: 'Tambahkan nama tampilan',
	AddDxCode: 'Tambahkan kode diagnosis',
	AddEmail: 'Tambahkan email',
	AddFamilyClientRelationshipModalDescription:
		'Mengundang anggota keluarga akan memungkinkan mereka melihat kisah perawatan dan informasi profil klien. Jika mereka diundang sebagai administrator, mereka akan memiliki akses untuk memperbarui informasi profil klien dan mengelola akses pengguna.',
	AddFamilyClientRelationshipModalTitle: 'Undang anggota keluarga',
	AddField: 'Tambahkan bidang',
	AddFormField: 'Tambahkan bidang formulir',
	AddImages: 'Tambahkan gambar',
	AddInsurance: 'Tambahkan asuransi',
	AddInvoice: 'Tambahkan faktur',
	AddLabel: 'Tambahkan label',
	AddLanguage: 'Tambahkan bahasa',
	AddLocation: 'Tambahkan lokasi',
	AddManually: 'Tambahkan secara manual',
	AddMessage: 'Tambahkan pesan',
	AddNewAction: 'Tambahkan tindakan baru',
	AddNewSection: 'Tambahkan bagian baru',
	AddNote: 'Tambahkan catatan',
	AddOnlineBookingDetails: 'Tambahkan detail pemesanan online',
	AddPOS: 'Tambahkan POS',
	AddPaidInvoices: 'Tambahkan faktur berbayar',
	AddPayer: 'Tambahkan pembayar',
	AddPayment: 'Tambahkan pembayaran',
	AddPaymentAdjustment: 'Tambahkan penyesuaian pembayaran',
	AddPaymentAdjustmentDisabledDescription: 'Alokasi pembayaran tidak akan diubah.',
	AddPaymentAdjustmentEnabledDescription: 'Jumlah yang tersedia untuk dialokasikan akan dikurangi.',
	AddPhoneNumber: 'Tambahkan nomor telepon',
	AddPhysicalOrVirtualLocations: 'Tambahkan lokasi fisik atau virtual',
	AddQuestion: 'Tambahkan pertanyaan',
	AddQuestionOrTitle: 'Tambahkan pertanyaan atau judul',
	AddRelationship: 'Tambahkan hubungan',
	AddRelationshipModalTitle: 'Hubungkan kontak yang ada',
	AddRelationshipModalTitleNewClient: 'Hubungkan kontak baru',
	AddRow: 'Tambahkan baris',
	AddRowAbove: 'Tambahkan baris di atas',
	AddRowBelow: 'Tambahkan baris di bawah ini',
	AddService: 'Tambahkan layanan',
	AddServiceLocation: 'Tambahkan lokasi layanan',
	AddServiceToCollections: 'Tambahkan layanan ke koleksi',
	AddServiceToOneOrMoreCollections: 'Tambahkan layanan ke satu atau beberapa koleksi',
	AddServices: 'Tambahkan layanan',
	AddSignature: 'Tambahkan tanda tangan',
	AddSignaturePlaceholder: 'Ketik detail tambahan untuk disertakan dengan tanda tangan Anda',
	AddSmartDataChips: 'Tambahkan chip data pintar',
	AddStaffClientRelationshipsModalDescription:
		'Memilih staf akan memungkinkan mereka membuat dan melihat kisah perawatan untuk klien ini. Mereka juga akan dapat melihat informasi klien.',
	AddStaffClientRelationshipsModalTitle: 'Tambahkan hubungan staf',
	AddTag: 'Tambahkan tag',
	AddTags: 'Tambahkan tag',
	AddTemplate: 'Tambahkan templat',
	AddTimezone: 'Tambahkan zona waktu',
	AddToClaim: 'Tambahkan ke klaim',
	AddToCollection: 'Tambahkan ke koleksi',
	AddToExisting: 'Tambahkan ke yang sudah ada',
	AddToStarred: 'Tambahkan ke berbintang',
	AddUnclaimedItems: 'Tambahkan item yang belum diklaim',
	AddUnrelatedContactWarning:
		'Anda telah menambahkan seseorang yang bukan kontak dari {contact}. Pastikan kontennya relevan sebelum melanjutkan dengan berbagi.',
	AddValue: 'Tambahkan "{value}"',
	AddVideoCall: 'Tambahkan panggilan video',
	AddVideoOrVoiceCall: 'Tambahkan panggilan video atau suara',
	AddictionCounselor: 'Konselor Kecanduan',
	AddingManualPayerDisclaimer:
		'Menambahkan pembayar secara manual ke daftar penyedia Anda tidak membuat koneksi pengajuan klaim elektronik dengan pembayar tersebut, tetapi dapat digunakan untuk membuat klaim secara manual.',
	AddingTeamMembersIncreaseCostAlert: 'Menambahkan anggota tim baru akan meningkatkan langganan bulanan Anda.',
	Additional: 'Tambahan',
	AdditionalBillingProfiles: 'Profil penagihan tambahan',
	AdditionalBillingProfilesSectionDescription:
		'Mengganti informasi penagihan default yang digunakan untuk anggota tim, pembayar, atau templat faktur tertentu.',
	AdditionalFeedback: 'Umpan balik tambahan',
	AddnNewWorkspace: 'Ruang kerja baru',
	AddnNewWorkspaceSuccessSnackbar: 'Ruang kerja telah dibuat!',
	Address: 'Alamat',
	AddressNumberStreet: 'Alamat (No, jalan)',
	Adjustment: 'Penyesuaian',
	AdjustmentType: 'Jenis penyesuaian',
	Admin: 'Admin',
	Admins: 'Admin',
	AdminsOnly: 'Hanya untuk admin',
	AdvancedPlanInclusionFive: 'Manajer akun',
	AdvancedPlanInclusionFour: 'Analisis Google',
	AdvancedPlanInclusionHeader: 'Segala sesuatu di Plus  ',
	AdvancedPlanInclusionOne: 'Peran ',
	AdvancedPlanInclusionSix: 'Dukungan impor data',
	AdvancedPlanInclusionThree: 'Pelabelan putih',
	AdvancedPlanInclusionTwo: 'Penyimpanan data yang dihapus selama 90 hari',
	AdvancedPlanMessage:
		'Tetap kendalikan kebutuhan praktik Anda. Tinjau rencana Anda saat ini dan pantau penggunaannya.',
	AdvancedSettings: 'Pengaturan lanjutan',
	AdvancedSubscriptionPlanSubtitle: 'Perluas praktik Anda dengan semua fitur',
	AdvancedSubscriptionPlanTitle: 'Canggih',
	AdvertisingManager: 'Manajer Periklanan',
	AerospaceEngineer: 'Insinyur Dirgantara',
	AgeYearsOld: '{age} tahun',
	Agenda: 'Agenda',
	AgendaView: 'Tampilan agenda',
	AiAskSupportedFileTypes: 'Jenis file yang didukung: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Asisten di ujung jari Anda',
	AiCopilotDisclaimer: 'AI Copilot dapat melakukan kesalahan. Periksa info penting.',
	AiCreateNewConversation: 'Buat percakapan baru',
	AiEnhanceYourProductivity: 'Tingkatkan Produktivitas Anda',
	AiPoweredTemplates: 'Template bertenaga AI',
	AiScribeNoDeviceFoundErrorMessage:
		'Sepertinya browser Anda tidak mendukung fitur ini, atau tidak ada perangkat yang kompatibel tersedia.',
	AiScribeUploadFormat: 'Jenis file yang didukung: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'hanya 1 file pada satu waktu',
	AiShowConversationHistory: 'Tampilkan riwayat percakapan',
	AiSmartPromptNodePlaceholderText:
		'Ketik prompt khusus Anda di sini untuk membantu menghasilkan hasil AI yang akurat dan personal.',
	AiSmartPromptPrimaryText: 'Perintah cerdas AI',
	AiSmartPromptSecondaryText: 'Masukan prompt cerdas AI khusus',
	AiSmartReminders: 'Pengingat cerdas AI',
	AiTemplateBannerTitle: 'Sederhanakan pekerjaan Anda dengan templat bertenaga AI',
	AiTemplates: 'Templat AI',
	AiTokens: 'Token AI',
	AiWorkBetterWithAi: 'Bekerja lebih baik dengan AI',
	All: 'Semua',
	AllAppointments: 'Semua janji temu',
	AllCategories: 'Semua kategori',
	AllClients: 'Semua klien',
	AllContactPolicySelectorLabel: 'Semua kontak dari <mark>{client}</mark>',
	AllContacts: 'Semua kontak',
	AllContactsOf: 'Semua kontak dari ‘{name}’',
	AllDay: 'Sepanjang hari',
	AllInboxes: 'Semua Kotak Masuk',
	AllIndustries: 'Semua industri',
	AllLocations: 'Semua lokasi',
	AllMeetings: 'Semua pertemuan',
	AllNotificationsRestoredMessage: 'Semua notifikasi dipulihkan',
	AllProfessions: 'Semua profesi',
	AllReminders: 'Semua pengingat',
	AllServices: 'Semua layanan',
	AllStatuses: 'Semua status',
	AllTags: 'Semua tag',
	AllTasks: 'Semua tugas',
	AllTeamMembers: 'Semua anggota tim',
	AllTypes: 'Semua tipe',
	Allocated: 'Dialokasikan',
	AllocatedItems: 'Item yang dialokasikan',
	AllocationTableEmptyState: 'Tidak ditemukan alokasi pembayaran',
	AllocationTotalWarningMessage: `Jumlah yang dialokasikan melebihi jumlah total pembayaran.
 Harap tinjau baris item di bawah ini.`,
	AllowClientsToCancelAnytime: 'Izinkan klien untuk membatalkan kapan saja',
	AllowNewClient: 'Izinkan klien baru',
	AllowNewClientHelper: 'Klien baru dapat memesan layanan ini',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Izinkan setidaknya {hours} jam sebelum janji temu',
	AllowToUseSavedCard: 'Izinkan {provider} untuk menggunakan kartu yang disimpan di masa mendatang',
	AllowVideoCalls: 'Izinkan panggilan video',
	AlreadyAdded: 'Sudah ditambahkan',
	AlreadyHasAccess: 'Memiliki akses',
	AlreadyHasAccount: 'Sudah punya akun?',
	Always: 'Selalu',
	AlwaysIgnore: 'Selalu abaikan',
	Amount: 'Jumlah',
	AmountDue: 'Jumlah yang harus dibayar',
	AmountOfReferralRequests: '{amount, plural, one {# permintaan rujukan} other {# permintaan rujukan}}',
	AmountPaid: 'Jumlah yang dibayarkan',
	AnalyzingAudio: 'Menganalisis audio...',
	AnalyzingInputContent: 'Menganalisis konten masukan...',
	AnalyzingRequest: 'Menganalisis permintaan...',
	AnalyzingTemplateContent: 'Menganalisis konten templat...',
	And: 'Dan',
	Annually: 'Tahunan',
	Anonymous: 'Anonim',
	AnswerExceeded: 'Jawaban Anda harus kurang dari 300 karakter.',
	AnyStatus: 'Status apa pun',
	AppNotifications: 'Pemberitahuan',
	AppNotificationsClearanceHeading: 'Kerja bagus! Anda telah menghapus semua aktivitas',
	AppNotificationsEmptyHeading: 'Aktivitas ruang kerja Anda akan segera muncul di sini',
	AppNotificationsEmptySubtext: 'Tidak ada tindakan yang perlu diambil untuk saat ini',
	AppNotificationsIgnoredCount: '{total} diabaikan',
	AppNotificationsUnread: '{total} belum dibaca',
	Append: 'Menambahkan',
	Apply: 'Menerapkan',
	ApplyAccountCredit: 'Terapkan kredit akun',
	ApplyDiscount: 'Terapkan diskon',
	ApplyVisualEffects: 'Terapkan efek visual',
	ApplyVisualEffectsNotSupported: 'Terapkan efek visual yang tidak didukung',
	Appointment: 'Janji temu',
	AppointmentAssignedNotificationSubject: '{actorProfileName} telah menetapkan {appointmentName} untuk Anda.',
	AppointmentCancelledNotificationSubject: '{actorProfileName} telah membatalkan {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} telah mengonfirmasi {appointmentName}',
	AppointmentDetails: 'Detail janji temu',
	AppointmentLocation: 'Lokasi Janji Temu',
	AppointmentLocationDescription:
		'Kelola lokasi virtual dan fisik default Anda. Saat janji temu dijadwalkan, lokasi ini akan diterapkan secara otomatis.',
	AppointmentNotFound: 'Janji temu tidak ditemukan',
	AppointmentReminder: 'Pengingat janji temu',
	AppointmentReminders: 'Pengingat janji temu',
	AppointmentRemindersInfo:
		'Tetapkan pengingat otomatis untuk janji temu klien guna menghindari ketidakhadiran dan pembatalan',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} telah menjadwal ulang {appointmentName}',
	AppointmentSaved: 'Janji temu telah disimpan',
	AppointmentStatus: 'Status janji temu',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0menit} other {{minutes}menit}} } one {{hours}jam {minutes, plural, =0 {} other {{minutes}menit}}} other {{hours}jam {minutes, plural, =0 {} other {{minutes}menit}}} }',
	AppointmentUndone: 'Janji temu dibatalkan',
	Appointments: 'Janji temu',
	Archive: 'Arsip',
	ArchiveClients: 'Klien arsip',
	Archived: 'Diarsipkan',
	AreYouAClient: 'Apakah Anda seorang klien?',
	AreYouStillThere: 'Apakah kamu masih di sana?',
	AreYouSure: 'Apa kamu yakin?',
	Arrangements: 'Pengaturan',
	ArtTherapist: 'Terapis Seni',
	Articles: 'Artikel',
	Artist: 'Artis',
	AskAI: 'Tanya AI',
	AskAiAddFormField: 'Tambahkan bidang formulir',
	AskAiChangeFormality: 'Ubah formalitas',
	AskAiChangeToneToBeMoreProfessional: 'Ubah nada bicara menjadi lebih profesional',
	AskAiExplainThis: 'TanyaAiJelaskanIni',
	AskAiExplainWhatThisDocumentIsAbout: 'Jelaskan tentang dokumen ini',
	AskAiExplainWhatThisImageIsAbout: 'Jelaskan tentang apa gambar ini',
	AskAiFixSpellingAndGrammar: 'Memperbaiki ejaan dan tata bahasa',
	AskAiGenerateACaptionForThisImage: 'Buat judul untuk gambar ini',
	AskAiGenerateFromThisPage: 'Dihasilkan dari halaman ini',
	AskAiGetStarted: 'Memulai',
	AskAiGiveItAFriendlyTone: 'Berikan nada yang ramah',
	AskAiGreeting: 'Hai {firstName}! Bagaimana saya bisa membantu Anda hari ini?',
	AskAiHowCanIHelpWithYourContent: 'Bagaimana saya dapat membantu dengan konten Anda?',
	AskAiInsert: 'Menyisipkan',
	AskAiMakeItMoreCasual: 'Jadikan lebih kasual',
	AskAiMakeThisTextMoreConcise: 'Buat teks ini lebih ringkas',
	AskAiMoreProfessional: 'Lebih profesional',
	AskAiOpenPreviousNote: 'Buka catatan sebelumnya',
	AskAiPondering: 'Memikirkan',
	AskAiReplace: 'Mengganti',
	AskAiReviewOrEditSelection: 'Tinjau atau edit pilihan',
	AskAiRuminating: 'merenungkan',
	AskAiSeeMore: 'Lihat selengkapnya',
	AskAiSimplifyLanguage: 'Sederhanakan bahasa',
	AskAiSomethingWentWrong:
		'Terjadi kesalahan. Jika masalah ini berlanjut, silakan hubungi kami melalui pusat bantuan kami.',
	AskAiStartWithATemplate: 'Mulailah dengan template',
	AskAiSuccessfullyCopiedResponse: 'Respons AI berhasil disalin',
	AskAiSuccessfullyInsertedResponse: 'Respons AI berhasil dimasukkan',
	AskAiSuccessfullyReplacedResponse: 'Berhasil mengganti respons AI',
	AskAiSuggested: 'Disarankan',
	AskAiSummariseTextIntoBulletPoints: 'Merangkum teks menjadi poin-poin penting',
	AskAiSummarizeNote: 'Ringkasan catatan',
	AskAiThinking: 'Pemikiran',
	AskAiToday: 'Hari ini {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'Apa yang ingin Anda lakukan dengan formulir ini?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Tulis catatan profesional menggunakan template',
	AskAskAiAnything: 'Tanyakan apa saja pada AI',
	AskWriteSearchAnything: `Bertanya, tulis '@' atau cari apa saja...`,
	Asking: 'Meminta',
	Assessment: 'Penilaian',
	Assessments: 'Penilaian',
	AssessmentsCategoryDescription: 'Untuk merekam evaluasi klien',
	AssignClients: 'Tetapkan klien',
	AssignNewClients: 'Tetapkan klien',
	AssignServices: 'Tetapkan layanan',
	AssignTeam: 'Tetapkan tim',
	AssignTeamMember: 'Tetapkan anggota tim',
	Assigned: 'Ditugaskan',
	AssignedClients: 'Klien yang ditugaskan',
	AssignedServices: 'Layanan yang ditugaskan',
	AssignedServicesDescription:
		'Lihat dan kelola layanan yang ditugaskan kepada Anda, sesuaikan harga untuk mencerminkan tarif khusus Anda. ',
	AssignedTeam: 'Tim yang ditugaskan',
	AthleticTrainer: 'Pelatih Atletik',
	AttachFiles: 'Lampirkan file',
	AttachLogo: 'Menempel',
	Attachment: 'Lampiran',
	AttachmentBlockedFileType: 'Diblokir karena alasan keamanan!',
	AttachmentTooLargeFileSize: 'File terlalu besar',
	AttachmentUploadItemComplete: 'Menyelesaikan',
	AttachmentUploadItemError: 'Pengunggahan gagal',
	AttachmentUploadItemLoading: 'Memuat',
	AttemptingToReconnect: 'Mencoba menyambung kembali...',
	Attended: 'Telah hadir',
	AttendeeBeingMutedTooltip: `Host telah menonaktifkan suara Anda. Gunakan 'angkat tangan' untuk meminta agar suara Anda tidak dibisukan`,
	AttendeeWithId: 'Peserta {attendeeId}',
	Attendees: 'Peserta',
	AttendeesCount: '{count} peserta',
	Attending: 'Menghadiri',
	Audiologist: 'Ahli audiologi',
	Aunt: 'Tante',
	Australia: 'Australia',
	AuthenticationCode: 'Kode Autentikasi',
	AuthoriseProvider: 'Otorisasi {provider}',
	AuthorisedProviders: 'Penyedia resmi',
	AutoDeclineAllFutureOption: 'Hanya acara atau janji temu baru',
	AutoDeclineAllOption: 'Acara atau janji temu baru dan yang sudah ada',
	AutoDeclinePrimaryText: 'Otomatis Tolak Acara',
	AutoDeclineSecondaryText: 'Acara selama masa Anda tidak di kantor akan ditolak secara otomatis',
	AutogenerateBillings: 'Buat dokumen penagihan secara otomatis',
	AutogenerateBillingsDescription:
		'Dokumen penagihan otomatis akan dibuat pada hari terakhir setiap bulan. Faktur dan tanda terima superbill dapat dibuat secara manual kapan saja.',
	AutomateWorkflows: 'Mengotomatiskan Alur Kerja',
	AutomaticallySendSuperbill: 'Kirim tanda terima superbill secara otomatis',
	AutomaticallySendSuperbillHelperText:
		'Superbill adalah tanda terima terperinci atas layanan yang diberikan kepada klien untuk penggantian asuransi.',
	Automation: 'Otomatisasi',
	AutomationActionSendEmailLabel: 'Kirim Email',
	AutomationActionSendSMSLabel: 'Kirim SMS',
	AutomationAndReminders: 'Otomatisasi ',
	AutomationDeletedSuccessMessage: 'Berhasil menghapus otomatisasi',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Peristiwa waktu',
	AutomationParams_timeUnit: 'Satuan',
	AutomationParams_timeValue: 'Nomor',
	AutomationPublishSuccessMessage: 'Otomasi berhasil dipublikasikan',
	AutomationPublishWarningTooltip:
		'Harap periksa kembali konfigurasi otomatisasi dan pastikan sudah dikonfigurasi dengan benar',
	AutomationTriggerEventCancelledDescription: 'Dipicu saat suatu acara dibatalkan atau dihapus',
	AutomationTriggerEventCancelledLabel: 'Acara dibatalkan',
	AutomationTriggerEventCreatedDescription: 'Pemicu saat suatu peristiwa dibuat',
	AutomationTriggerEventCreatedLabel: 'Acara baru',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Dipicu saat suatu acara dibuat atau diperbarui (kecuali saat dibatalkan)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Acara baru atau yang diperbarui',
	AutomationTriggerEventEndedDescription: 'Dipicu saat suatu peristiwa berakhir',
	AutomationTriggerEventEndedLabel: 'Acara berakhir',
	AutomationTriggerEventStartsDescription: 'Dipicu ketika sejumlah waktu tertentu sebelum suatu peristiwa dimulai',
	AutomationTriggerEventStartsLabel: 'Acara dimulai',
	Automations: 'Otomatisasi',
	Availability: 'Tersedianya',
	AvailabilityDisableSchedule: 'Nonaktifkan jadwal',
	AvailabilityDisabled: 'Dengan disabilitas',
	AvailabilityEnableSchedule: 'Aktifkan jadwal',
	AvailabilityEnabled: 'Diaktifkan',
	AvailabilityNoActiveBanner:
		'Anda telah menonaktifkan semua jadwal Anda. Klien tidak dapat memesan Anda secara online, dan semua janji temu di masa mendatang perlu dikonfirmasi secara manual.',
	AvailabilityNoActiveConfirmationDescription:
		'Menonaktifkan ketersediaan ini akan mengakibatkan tidak ada jadwal aktif. Klien tidak akan dapat memesan Anda secara daring, dan pemesanan yang dilakukan oleh praktisi akan berada di luar jam kerja Anda.',
	AvailabilityNoActiveConfirmationProceed: 'Ya, lanjutkan',
	AvailabilityNoActiveConfirmationTitle: 'Tidak ada jadwal aktif',
	AvailabilityToggle: 'Jadwal diaktifkan',
	AvailabilityUnsetDate: 'Belum ada tanggal yang ditentukan',
	AvailableLocations: 'Lokasi yang tersedia',
	AvailablePayers: 'Pembayar yang tersedia',
	AvailablePayersEmptyState: 'Tidak ada pembayar yang dipilih',
	AvailableTimes: 'Waktu yang Tersedia',
	Back: 'Kembali',
	BackHome: 'Kembali ke rumah',
	BackToAppointment: 'Kembali ke janji temu',
	BackToLogin: 'Kembali ke masuk',
	BackToMapColumns: 'Kembali ke Kolom Peta',
	BackToTemplates: 'Kembali ke Template',
	BackToUploadFile: 'Kembali ke Unggah file',
	Banker: 'Bankir',
	BasicBlocks: 'Blok dasar',
	BeforeAppointment: 'Kirim pengingat {deliveryType} {interval} {unit} sebelum janji temu',
	BehavioralAnalyst: 'Analis Perilaku',
	BehavioralHealthTherapy: 'Terapi kesehatan perilaku',
	Beta: 'Bahasa Inggris',
	BillTo: 'Tagihan ke',
	BillableItems: 'Barang yang dapat ditagih',
	BillableItemsEmptyState: 'Tidak ada item yang dapat ditagih yang ditemukan',
	Biller: 'Penagih',
	Billing: 'Penagihan',
	BillingAddress: 'Alamat penagihan',
	BillingAndReceiptsUnauthorisedMessage:
		'Akses tampilan faktur dan pembayaran diperlukan untuk mengakses informasi ini.',
	BillingBillablesTab: 'Tagihan',
	BillingClaimsTab: 'Klaim',
	BillingDetails: 'Rincian penagihan',
	BillingDocuments: 'Dokumen penagihan',
	BillingDocumentsClaimsTab: 'Klaim',
	BillingDocumentsEmptyState: 'Tidak ada {tabType} yang ditemukan',
	BillingDocumentsInvoicesTab: 'Faktur',
	BillingDocumentsSuperbillsTab: 'Tagihan super',
	BillingInformation: 'Informasi penagihan',
	BillingInvoicesTab: 'Faktur',
	BillingItems: 'Item penagihan',
	BillingPaymentsTab: 'Pembayaran',
	BillingPeriod: 'Periode penagihan',
	BillingProfile: 'Profil penagihan',
	BillingProfileOverridesDescription: 'Batas penggunaan profil penagihan ini untuk anggota tim tertentu',
	BillingProfileOverridesHeader: 'Batas akses',
	BillingProfileProviderType: 'Jenis penyedia',
	BillingProfileTypeIndividual: 'Praktisi',
	BillingProfileTypeIndividualSubLabel: 'NPI Tipe 1',
	BillingProfileTypeOrganisation: 'Organisasi',
	BillingProfileTypeOrganisationSubLabel: 'NPI Tipe 2',
	BillingProfiles: 'Profil penagihan',
	BillingProfilesEditHeader: 'Edit {name} profil tagihan',
	BillingProfilesNewHeader: 'Profil penagihan baru',
	BillingProfilesSectionDescription:
		'Kelola informasi penagihan Anda untuk praktisi dan pembayar asuransi dengan menyiapkan profil penagihan yang dapat diterapkan pada faktur dan pembayaran asuransi.',
	BillingSearchPlaceholder: 'Pencarian item',
	BillingSettings: 'Pengaturan penagihan',
	BillingSuperbillsTab: 'Tagihan super',
	BiomedicalEngineer: 'Insinyur Biomedis',
	BlankInvoice: 'Faktur kosong',
	BlueShieldProviderNumber: 'Nomor penyedia Blue Shield',
	Body: 'Tubuh',
	Bold: 'Berani',
	BookAgain: 'Pesan lagi',
	BookAppointment: 'Pesan janji temu',
	BookableOnline: 'Dapat dipesan secara online',
	BookableOnlineHelper: 'Klien dapat memesan layanan ini secara online',
	BookedOnline: 'Dipesan secara daring',
	Booking: 'Pemesanan',
	BookingAnalyticsIntegrationPanelDescription:
		'Siapkan Google Tag Manager untuk melacak tindakan dan konversi utama dalam alur pemesanan online Anda. Kumpulkan data berharga tentang interaksi pengguna untuk meningkatkan upaya pemasaran dan mengoptimalkan pengalaman pemesanan.',
	BookingAnalyticsIntegrationPanelTitle: 'Integrasi analitik',
	BookingAndCancellationPolicies: 'Pemesanan ',
	BookingButtonEmbed: 'Tombol',
	BookingButtonEmbedDescription: 'Menambahkan tombol pemesanan online ke situs web Anda',
	BookingDirectTextLink: 'Tautan teks langsung',
	BookingDirectTextLinkDescription: 'Membuka halaman pemesanan online',
	BookingFormatLink: 'Format tautan',
	BookingFormatLinkButtonTitle: 'Judul tombol',
	BookingInlineEmbed: 'Sematan sebaris',
	BookingInlineEmbedDescription: 'Memuat halaman pemesanan online langsung di situs web Anda',
	BookingLink: 'Tautan pemesanan',
	BookingLinkModalCopyText: 'Menyalin',
	BookingLinkModalDescription: 'Izinkan klien dengan tautan ini untuk memesan anggota tim atau layanan apa pun',
	BookingLinkModalHelpText: 'Pelajari cara mengatur pemesanan online',
	BookingLinkModalTitle: 'Bagikan tautan pemesanan Anda',
	BookingPolicies: 'Kebijakan pemesanan',
	BookingPoliciesDescription: 'Tetapkan kapan pemesanan online dapat dilakukan oleh klien',
	BookingTimeUnitDays: 'hari',
	BookingTimeUnitHours: 'jam',
	BookingTimeUnitMinutes: 'menit',
	BookingTimeUnitMonths: 'bulan',
	BookingTimeUnitWeeks: 'minggu',
	BottomNavBilling: 'Penagihan',
	BottomNavGettingStarted: 'Rumah',
	BottomNavMore: 'Lagi',
	BottomNavNotes: 'Catatan',
	Brands: 'Merek',
	Brother: 'Saudara laki-laki',
	BrotherInLaw: 'Kakak ipar',
	BrowseOrDragFileHere: '<link>Jelajahi</link> atau seret file di sini',
	BrowseOrDragFileHereDescription: 'PNG, JPG (maks. {limit})',
	BufferAfterTime: '{time} menit setelah',
	BufferAndLabel: 'Dan',
	BufferAppointmentLabel: 'sebuah janji temu',
	BufferBeforeTime: '{time} menit sebelum',
	BufferTime: 'Waktu penyangga',
	BufferTimeViewLabel: '{bufferBefore} menit sebelum dan {bufferAfter} menit setelah janji temu',
	BulkArchiveClientsDescription:
		'Apakah Anda yakin ingin mengarsipkan klien ini? Anda dapat mengaktifkannya kembali nanti.',
	BulkArchiveSuccess: 'Klien berhasil diarsipkan',
	BulkArchiveUndone: 'Arsip massal dibatalkan',
	BulkPermanentDeleteDescription: 'Ini akan menghapus **{count} percakapan**. Tindakan ini tidak dapat dibatalkan.',
	BulkPermanentDeleteTitle: 'Hapus percakapan selamanya',
	BulkUnarchiveSuccess: 'Klien berhasil diarsipkan',
	BulletedList: 'Daftar poin',
	BusinessAddress: 'Alamat bisnis',
	BusinessAddressOptional: 'Alamat bisnis <span>(Opsional)</span>',
	BusinessName: 'Nama bisnis',
	Button: 'Tombol',
	By: 'Oleh',
	CHAMPUSIdentificationNumber: 'Nomor identifikasi CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC diperlukan',
	Calendar: 'Kalender',
	CalendarAppSyncFormDescription: 'Sinkronkan acara Carepatron ke',
	CalendarAppSyncPanelTitle: 'Sinkronisasi aplikasi yang terhubung',
	CalendarDescription: 'Kelola janji temu Anda atau atur tugas dan pengingat pribadi',
	CalendarDetails: 'Detail kalender',
	CalendarDetailsDescription: 'Kelola pengaturan tampilan kalender dan janji temu Anda.',
	CalendarScheduleNew: 'Jadwal baru',
	CalendarSettings: 'Pengaturan kalender',
	Call: 'Panggilan',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> telah bergabung ke panggilan video',
	CallChangeLayoutTextContent: 'Pilihan disimpan untuk pertemuan mendatang',
	CallIdlePrompt: 'Apakah Anda lebih suka tetap menunggu untuk bergabung, atau ingin mencoba lagi nanti?',
	CallLayoutOptionAuto: 'Mobil',
	CallLayoutOptionSidebar: 'Bilah Samping',
	CallLayoutOptionSpotlight: 'Menyoroti',
	CallLayoutOptionTiled: 'Ubin',
	CallNoAttendees: 'Tidak ada peserta dalam rapat.',
	CallSessionExpiredError: 'Sesi telah berakhir. Panggilan telah berakhir. Silakan coba bergabung lagi.',
	CallWithPractitioner: 'Telepon dengan {practitioner}',
	CallsListCreateButton: 'Panggilan baru',
	CallsListEmptyState: 'Tidak ada panggilan aktif',
	CallsListItemEndCall: 'Akhiri panggilan',
	CamWarningMessage: 'Masalah telah terdeteksi pada kamera Anda',
	Camera: 'Kamera',
	CameraAndMicIssueModalDescription: `Harap aktifkan akses Carepatron ke kamera dan mikrofon Anda.
 Untuk informasi lebih lanjut, <a>ikuti panduan ini</a>`,
	CameraAndMicIssueModalTitle: 'Kamera dan mikrofon diblokir',
	CameraQuality: 'Kualitas kamera',
	CameraSource: 'Sumber kamera',
	CanModifyReadOnlyEvent: 'Anda tidak dapat memodifikasi acara ini',
	Canada: 'Kanada',
	Cancel: 'Membatalkan',
	CancelClientImportDescription: 'Apakah Anda yakin ingin membatalkan impor ini?',
	CancelClientImportPrimaryAction: 'Ya, batalkan impor',
	CancelClientImportSecondaryAction: 'Tetap mengedit',
	CancelClientImportTitle: 'Batalkan impor klien',
	CancelImportButton: 'Batalkan impor',
	CancelPlan: 'Batalkan rencana',
	CancelPlanConfirmation: `Membatalkan paket akan secara otomatis membebankan akun Anda dengan saldo terutang yang Anda miliki untuk bulan ini.
 Jika Anda ingin menurunkan versi pengguna yang ditagih, Anda cukup menghapus anggota tim dan Carepatron akan secara otomatis memperbarui harga langganan Anda.`,
	CancelSend: 'Batal kirim',
	CancelSubscription: 'Batalkan langganan',
	Canceled: 'Dibatalkan',
	CancellationPolicy: 'Kebijakan pembatalan',
	Cancelled: 'Dibatalkan',
	CannotContainSpecialCharactersError: 'Tidak dapat berisi {specialCharacters}',
	CannotDeleteInvoice: 'Faktur yang dibayar melalui pembayaran online tidak dapat dihapus',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> tidak dapat dipindahkan dari grup <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'Layanan tidak dapat dipindahkan ke luar koleksi',
	CapeTown: 'Kota Tanjung',
	Caption: 'Keterangan',
	CaptureNameFieldLabel: 'Nama yang Anda ingin orang lain sebut sebagai Anda',
	CapturePaymentMethod: 'Tangkap metode pembayaran',
	CapturingAudio: 'Menangkap audio',
	CapturingSignature: 'Menangkap tanda tangan...',
	CardInformation: 'Informasi kartu',
	CardNumberRequired: 'Nomor kartu diperlukan',
	CardiacRehabilitationSpecialist: 'Spesialis Rehabilitasi Jantung',
	Cardiologist: 'Ahli jantung',
	CareAiNoConversations: 'Belum ada percakapan',
	CareAiNoConversationsDescription: 'Mulai percakapan dengan {aiName} untuk memulai',
	CareAssistant: 'Asisten Perawatan',
	CareManager: 'Manajer Perawatan',
	Caregiver: 'Pengasuh',
	CaregiverCreateModalDescription:
		'Menambahkan staf sebagai administrator akan memungkinkan mereka membuat dan mengelola cerita perawatan. Ini juga memberi mereka akses penuh untuk membuat dan mengelola klien.',
	CaregiverCreateModalTitle: 'Anggota tim baru',
	CaregiverListCantAddStaffInfoTitle:
		'Anda telah mencapai jumlah staf maksimum untuk langganan Anda. Harap tingkatkan paket Anda untuk menambah lebih banyak anggota staf.',
	CaregiverListCreateButton: 'Anggota tim baru',
	CaregiverListEmptyState: 'Tidak ada pengasuh yang ditambahkan',
	CaregiversListItemRemoveStaff: 'Hapus staf',
	CarepatronApp: 'Aplikasi Carepatron',
	CarepatronCommunity: 'Masyarakat',
	CarepatronFieldAddress: 'Alamat',
	CarepatronFieldAssignedStaff: 'Staf yang ditugaskan',
	CarepatronFieldBirthDate: 'Tanggal lahir',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: 'Status pekerjaan',
	CarepatronFieldEthnicity: 'Suku Bangsa',
	CarepatronFieldFirstName: 'Nama depan',
	CarepatronFieldGender: 'Jenis kelamin',
	CarepatronFieldIdentificationNumber: 'Nomor identifikasi',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Label',
	CarepatronFieldLastName: 'Nama belakang',
	CarepatronFieldLivingArrangements: 'Pengaturan tempat tinggal',
	CarepatronFieldMiddleNames: 'Nama tengah',
	CarepatronFieldOccupation: 'Pekerjaan',
	CarepatronFieldPhoneNumber: 'Nomor telepon',
	CarepatronFieldRelationshipStatus: 'Status hubungan',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 status maks.',
	CarepatronFieldTags: 'Tag:',
	CarepatronFields: 'Bidang Carepatron',
	Cash: 'Uang tunai',
	Category: 'Kategori',
	CategoryInputPlaceholder: 'Pilih kategori template',
	CenterAlign: 'Rata tengah',
	Central: 'Pusat',
	ChangeLayout: 'Ubah tata letak',
	ChangeLogo: 'Mengubah',
	ChangePassword: 'Ubah kata sandi',
	ChangePasswordFailureSnackbar:
		'Maaf, kata sandi Anda belum diubah. Periksa apakah kata sandi lama Anda sudah benar.',
	ChangePasswordHelperInfo: 'Panjang minimum {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Berhasil mengubah kata sandi! Saat Anda masuk lagi, pastikan untuk menggunakan kata sandi tersebut.',
	ChangeSubscription: 'Ubah Langganan',
	ChangesNotAllowed: 'Perubahan tidak dapat dilakukan pada bidang ini',
	ChargesDisabled: 'Biaya dinonaktifkan',
	ChargesEnabled: 'Biaya diaktifkan',
	ChargesStatus: 'Status biaya',
	ChartAndDiagram: 'Bagan/Diagram',
	ChartsAndDiagramsCategoryDescription: 'Untuk mengilustrasikan data klien dan kemajuan',
	ChatEditMessage: 'Edit pesan',
	ChatReplyTo: 'Balas kepada {name}',
	ChatTypeMessageTo: 'Pesan {name}',
	Check: 'Memeriksa',
	CheckList: 'Daftar Periksa',
	Chef: 'Koki',
	Chiropractic: 'Kiropraktik',
	Chiropractor: 'Dokter Kiropraktik',
	Chiropractors: 'Dokter Kiropraktik',
	ChooseACollection: 'Pilih koleksi',
	ChooseAContact: 'Pilih kontak',
	ChooseAccountTypeHeader: 'Mana yang paling menggambarkan Anda?',
	ChooseAction: 'Pilih tindakan',
	ChooseAnAccount: 'Pilih akun',
	ChooseAnOption: 'Pilih opsi',
	ChooseBillingProfile: 'Pilih profil penagihan',
	ChooseClaim: 'Pilih klaim',
	ChooseCollection: 'Pilih koleksi',
	ChooseColor: 'Pilih warna',
	ChooseCustomDate: 'Pilih tanggal khusus',
	ChooseDateAndTime: 'Pilih tanggal dan waktu',
	ChooseDxCodes: 'Pilih kode diagnosis',
	ChooseEventType: 'Pilih jenis acara',
	ChooseFileButton: 'Pilih file',
	ChooseFolder: 'Pilih folder',
	ChooseInbox: 'Pilih kotak masuk',
	ChooseMethod: 'Pilih metode',
	ChooseNewOwner: 'Pilih pemilik baru',
	ChooseOrganization: 'Pilih Organisasi',
	ChoosePassword: 'Pilih kata sandi',
	ChoosePayer: 'Pilih pembayar',
	ChoosePaymentMethod: 'Pilih metode pembayaran',
	ChoosePhysicalOrRemoteLocations: 'Masukan atau pilih lokasi',
	ChoosePlan: 'Pilih {plan}',
	ChooseProfessional: 'Pilih Profesional',
	ChooseServices: 'Pilih layanan',
	ChooseSource: 'Pilih sumber',
	ChooseSourceDescription:
		'Pilih dari mana Anda mengimpor klien – baik itu dari file atau platform perangkat lunak lainnya.',
	ChooseTags: 'Pilih tag',
	ChooseTaxName: 'Pilih nama pajak',
	ChooseTeamMembers: 'Pilih anggota tim',
	ChooseTheme: 'Pilih tema',
	ChooseTrigger: 'Pilih pemicu',
	ChooseYourProvider: 'Pilih penyedia Anda',
	CircularProgressWithLabel: '{value}%',
	City: 'Kota',
	CivilEngineer: 'Insinyur Sipil',
	Claim: 'Mengeklaim',
	ClaimAddReferringProvider: 'Tambahkan pengarah rujukan',
	ClaimAddRenderingProvider: 'Tambahkan penyedia rendering',
	ClaimAmount: 'Jumlah klaim',
	ClaimAmountPaidHelpContent:
		'Jumlah yang dibayarkan adalah pembayaran yang diterima dari pasien atau pembayar lainnya. Masukkan jumlah total yang dibayarkan pasien dan/atau pembayar lainnya hanya untuk layanan yang dicakup.',
	ClaimAmountPaidHelpSubtitle: 'Lapangan 29',
	ClaimAmountPaidHelpTitle: 'Jumlah yang dibayarkan',
	ClaimBillingProfileTypeIndividual: 'Individu',
	ClaimBillingProfileTypeOrganisation: 'Organisasi',
	ClaimChooseRenderingProviderOrTeamMember: 'Pilih penyedia rendering atau anggota tim',
	ClaimClientInsurancePolicies: 'Polis asuransi klien',
	ClaimCreatedAction: '<mark>Klaim {claimNumber}</mark> dibuat',
	ClaimDeniedAction: '<mark>Klaim {claimNumber}</mark> ditolak oleh <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Cari kode diagnosis ICD 10',
	ClaimDiagnosisSelectorHelpContent: `“Diagnosis atau cedera” adalah tanda, gejala, keluhan, atau kondisi pasien yang berhubungan dengan layanan pada klaim.
 Hingga 12 kode diagnosis ICD 10 dapat dipilih.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Lapangan 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnosis atau cedera',
	ClaimDiagnosticCodesEmptyError: 'Setidaknya satu kode diagnosis diperlukan',
	ClaimDoIncludeReferrerInformation: 'Sertakan informasi rujukan pada CMS1500',
	ClaimERAReceivedAction:
		'Electronic remittance received from <b>{payerNumber} {payerName}</b>Transfer elektronik diterima dari <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Penerimaan pengiriman uang elektronik	<mark>Pembayaran {paymentReference}</mark> untuk <b>{paymentAmount}</b> oleh <b>{payerNumber} {payerName}</b> telah dicatat',
	ClaimExportedAction: '<mark>Klaim {claimNumber}</mark> telah diekspor sebagai <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nama klien atau kontak',
	ClaimFieldClientAddress: 'Alamat klien',
	ClaimFieldClientAddressDescription:
		'Masukkan alamat klien. Baris pertama adalah untuk alamat jalan. Jangan gunakan tanda baca (koma atau titik) atau simbol apa pun di alamat tersebut. Jika melaporkan alamat asing, hubungi pembayar untuk mendapatkan petunjuk pelaporan khusus.',
	ClaimFieldClientAddressSubtitle: 'Lapangan 5',
	ClaimFieldClientDateOfBirth: 'Tanggal lahir klien',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Lapangan 3',
	ClaimFieldClientDateOfBirthDescription:
		'Masukkan tanggal lahir klien yang terdiri dari 8 digit (MM/DD/YYYY). Tanggal lahir klien adalah informasi yang akan mengidentifikasi klien dan membedakan orang-orang dengan nama yang mirip.',
	ClaimFieldClientDescription: `'Nama klien' adalah nama orang yang menerima perawatan atau perlengkapan.`,
	ClaimFieldClientSexDescription: `'Jenis kelamin' adalah informasi yang akan mengidentifikasi klien dan membedakan orang dengan nama yang mirip.`,
	ClaimFieldClientSubtitle: 'Lapangan 2',
	ClaimFiling: 'Klaim pengajuan',
	ClaimHistorySubtitle: 'Asuransi • Klaim {number}',
	ClaimIncidentAutoAccident: 'Kecelakaan mobil?',
	ClaimIncidentConditionRelatedTo: 'Apakah kondisi klien berhubungan dengan',
	ClaimIncidentConditionRelatedToHelpContent:
		'Informasi ini menunjukkan apakah penyakit atau cedera klien terkait dengan pekerjaan, kecelakaan mobil, atau kecelakaan lainnya. Pekerjaan (saat ini atau sebelumnya) akan menunjukkan bahwa kondisi tersebut terkait dengan pekerjaan atau tempat kerja klien. Kecelakaan mobil akan menunjukkan bahwa kondisi tersebut merupakan akibat dari kecelakaan mobil. Kecelakaan lainnya akan menunjukkan bahwa kondisi tersebut merupakan akibat dari jenis kecelakaan lainnya.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Lapangan 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'Apakah kondisi klien berhubungan dengan',
	ClaimIncidentCurrentIllness: 'Penyakit, cedera, atau kehamilan saat ini',
	ClaimIncidentCurrentIllnessHelpContent:
		'Tanggal penyakit, cedera, atau kehamilan saat ini mengidentifikasi tanggal pertama timbulnya penyakit, tanggal cedera sebenarnya, atau HPHT untuk kehamilan.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Lapangan 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Tanggal sakit, cedera, atau kehamilan saat ini (LMP)',
	ClaimIncidentDate: 'Tanggal',
	ClaimIncidentDateFrom: 'Tanggal dari',
	ClaimIncidentDateTo: 'Tanggal sampai',
	ClaimIncidentEmploymentRelated: 'Pekerjaan',
	ClaimIncidentEmploymentRelatedDesc: '(Saat ini atau sebelumnya)',
	ClaimIncidentHospitalizationDatesLabel: 'Tanggal rawat inap terkait dengan layanan saat ini',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Tanggal rawat inap yang terkait dengan layanan saat ini mengacu pada rawat inap klien dan menunjukkan tanggal masuk dan keluar yang terkait dengan layanan pada klaim.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Lapangan 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Tanggal rawat inap terkait dengan layanan saat ini',
	ClaimIncidentInformation: 'Informasi insiden',
	ClaimIncidentOtherAccident: 'Kecelakaan lainnya?',
	ClaimIncidentOtherAssociatedDate: 'Tanggal terkait lainnya',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'Tanggal lainnya mengidentifikasi informasi tanggal tambahan tentang kondisi atau perawatan klien.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Lapangan 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Tanggal lainnya',
	ClaimIncidentQualifier: 'Kualifikasi',
	ClaimIncidentQualifierPlaceholder: 'Pilih kualifikasi',
	ClaimIncidentUnableToWorkDatesLabel: 'Klien tidak dapat bekerja pada pekerjaan saat ini',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Tanggal klien tidak dapat bekerja pada pekerjaan saat ini adalah rentang waktu klien tidak dapat bekerja atau tidak dapat bekerja.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Lapangan 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Tanggal klien tidak dapat bekerja pada pekerjaan saat ini',
	ClaimIncludeReferrerInformation: 'Sertakan informasi rujukan pada CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `Jenis pertanggungan asuransi kesehatan yang berlaku untuk klaim ini. Lainnya menunjukkan asuransi kesehatan termasuk HMO, asuransi komersial, kecelakaan mobil, liabilitas, atau kompensasi pekerja.
 Informasi ini mengarahkan klaim ke program yang benar dan dapat menetapkan tanggung jawab utama.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Lapangan 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Jenis cakupan',
	ClaimInsuranceGroupIdHelpContent: `Masukkan nomor polis atau nomor grup tertanggung seperti yang tercantum pada kartu identifikasi perawatan kesehatan tertanggung.

 "Nomor Polis, Grup, atau FECA Tertanggung" adalah pengenal alfanumerik untuk cakupan asuransi kesehatan, asuransi mobil, atau asuransi lainnya. Nomor FECA adalah pengenal alfanumerik 9 karakter yang diberikan kepada pasien yang mengklaim kondisi terkait pekerjaan.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Lapangan 11',
	ClaimInsuranceGroupIdHelpTitle: 'Nomor Polis, Grup, atau FECA Tertanggung',
	ClaimInsuranceMemberIdHelpContent: `Masukkan nomor identitas tertanggung seperti yang tertera pada kartu identitas tertanggung untuk pembayar yang menjadi tujuan pengajuan klaim.
 Jika pasien memiliki Nomor Identifikasi Anggota unik yang ditetapkan oleh pembayar, masukkan nomor tersebut di kolom ini.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Lapangan 1a',
	ClaimInsuranceMemberIdHelpTitle: 'ID Anggota Tertanggung',
	ClaimInsurancePayer: 'Pembayar asuransi',
	ClaimManualPaymentAction: '<mark>Pembayaran {paymentReference}</mark> untuk <b>{paymentAmount}</b> tercatat',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Informasi klaim tambahan',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Harap merujuk pada instruksi terkini dari pembayar publik atau swasta mengenai penggunaan kolom ini. Laporkan kualifikasi yang sesuai, jika tersedia, untuk informasi yang dimasukkan.Jangan masukkan spasi, tanda hubung, atau pemisah lain di antara kualifikasi dan informasi.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Field 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Informasi klaim tambahan',
	ClaimMiscClaimCodes: 'Kode klaim',
	ClaimMiscOriginalReferenceNumber: 'Nomor referensi asli',
	ClaimMiscPatientsAccountNumber: 'Nomor akun pasien',
	ClaimMiscPatientsAccountNumberHelpContent:
		'Nomor akun pasien adalah pengenal yang diberikan oleh penyedia layanan.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Lapangan 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Nomor akun pasien',
	ClaimMiscPriorAuthorizationNumber: 'Nomor otorisasi sebelumnya',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'Nomor otorisasi sebelumnya adalah nomor yang ditetapkan pembayar yang mengotorisasi layanan.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Lapangan 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Nomor otorisasi sebelumnya',
	ClaimMiscResubmissionCode: 'Kode pengiriman ulang',
	ClaimMiscResubmissionCodeHelpContent:
		'Pengajuan ulang berarti kode dan nomor referensi asli yang ditetapkan oleh pembayar atau penerima tujuan untuk menunjukkan klaim atau perjumpaan yang telah diajukan sebelumnya.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Lapangan 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Pengiriman Ulang dan / atau Nomor Referensi Asli',
	ClaimNumber: 'Nomor Klaim',
	ClaimNumberFormat: 'Klaim #{number}',
	ClaimOrderingProvider: 'Penyedia pemesanan',
	ClaimOtherId: 'ID Lainnya',
	ClaimOtherIdPlaceholder: 'Pilih opsi',
	ClaimOtherIdQualifier: 'Kualifikasi ID lainnya',
	ClaimOtherIdQualifierPlaceholder: 'Pilih kualifikasi ID',
	ClaimPlaceOfService: 'Tempat pelayanan',
	ClaimPlaceOfServicePlaceholder: 'Tambahkan POS',
	ClaimPolicyHolderRelationship: 'Hubungan pemegang polis',
	ClaimPolicyInformation: 'Informasi kebijakan',
	ClaimPolicyTelephone: 'Telepon (sertakan kode area)',
	ClaimReceivedAction: '<mark>Klaim {claimNumber}</mark> diterima oleh <b>{name}</b>',
	ClaimReferringProvider: 'Penyedia rujukan',
	ClaimReferringProviderEmpty: 'Tidak ada penyedia rujukan yang ditambahkan',
	ClaimReferringProviderHelpContent:
		'Nama yang dimasukkan adalah penyedia rujukan, penyedia pemesanan, atau penyedia pengawas yang merujuk, memesan, atau mengawasi layanan atau pasokan pada klaim. Kualifikasi menunjukkan peran penyedia yang dilaporkan.',
	ClaimReferringProviderHelpSubtitle: 'Lapangan 17',
	ClaimReferringProviderHelpTitle: 'Nama penyedia atau sumber rujukan',
	ClaimReferringProviderQualifier: 'Kualifikasi',
	ClaimReferringProviderQualifierPlaceholder: 'Pilih kualifikasi',
	ClaimRejectedAction: '<mark>Klaim {claimNumber}</mark> ditolak oleh <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Nomor ID',
	ClaimRenderingProviderOrTeamMember: 'Penyedia rendering atau anggota tim',
	ClaimRestoredAction: '<mark>Klaim {claimNumber}</mark> telah dipulihkan',
	ClaimServiceFacility: 'Fasilitas layanan',
	ClaimServiceFacilityLocationHelpContent:
		'Nama dan alamat fasilitas tempat layanan diberikan, mengidentifikasi lokasi tempat layanan diberikan.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a dan 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Lapangan 32, 32a dan 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Fasilitas Pelayanan',
	ClaimServiceFacilityPlaceholder: 'Pilih fasilitas layanan atau lokasi',
	ClaimServiceLabChargesHelpContent: `Lengkapi kolom ini saat mengajukan klaim untuk layanan yang dibeli dan disediakan oleh entitas selain penyedia penagihan.
 Setiap layanan yang dibeli harus dilaporkan pada klaim terpisah karena hanya satu biaya yang dapat dimasukkan pada formulir CMS1500.`,
	ClaimServiceLabChargesHelpSubtitle: 'Lapangan 20',
	ClaimServiceLabChargesHelpTitle: 'Biaya lab luar',
	ClaimServiceLineServiceHelpContent:
		'“Prosedur, Layanan, atau Persediaan” mengidentifikasi layanan dan prosedur medis yang diberikan kepada pasien.',
	ClaimServiceLineServiceHelpSubtitle: 'Lapangan 24d',
	ClaimServiceLineServiceHelpTitle: 'Prosedur, Layanan atau Persediaan',
	ClaimServiceLinesEmptyError: 'Setidaknya satu layanan diperlukan',
	ClaimServiceSupplementaryInfoHelpContent: `Tambahkan deskripsi naratif tambahan tentang layanan yang diberikan menggunakan kualifikasi yang berlaku.
 Jangan masukkan spasi, tanda hubung, atau pemisah lainnya antara kualifikasi dan informasi.

 Untuk petunjuk lengkap tentang penambahan informasi tambahan, tinjau petunjuk formulir klaim CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Lapangan 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Info tambahan',
	ClaimSettingsBillingMethodTitle: 'Metode penagihan klien',
	ClaimSettingsClientSignatureDescription:
		'Saya memiliki izin untuk merilis informasi medis atau informasi lainnya yang diperlukan untuk memproses klaim asuransi.',
	ClaimSettingsClientSignatureTitle: 'Tanda tangan klien pada arsip',
	ClaimSettingsConsentLabel: 'Persetujuan yang diperlukan untuk memproses klaim asuransi:',
	ClaimSettingsDescription: 'Pilih metode penagihan klien untuk memastikan pemrosesan pembayaran lancar:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} memiliki polis asuransi aktif. Untuk mengaktifkan penagihan asuransi, perbarui metode penagihan Klien menjadi Asuransi.',
	ClaimSettingsInsuranceDescription: 'Biaya yang diganti oleh asuransi',
	ClaimSettingsInsuranceTitle: 'Asuransi',
	ClaimSettingsNoPoliciesAlertDescription: 'Tambahkan polis asuransi untuk mengaktifkan klaim asuransi.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Saya memiliki izin untuk menerima pembayaran asuransi atas layanan yang diberikan.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Tanda tangan pemegang polis pada arsip',
	ClaimSettingsSelfPayDescription: 'Klien akan membayar untuk janji temu',
	ClaimSettingsSelfPayTitle: 'Bayar sendiri',
	ClaimSettingsTitle: 'Klaim pengaturan',
	ClaimSexSelectorPlaceholder: 'Pria / Wanita',
	ClaimStatusChangedAction: '<mark>Klaim {claimNumber}</mark> status diperbarui',
	ClaimSubmittedAction:
		'<mark>Klaim {claimNumber}</mark> diajukan ke <b>{payerClearingHouse}</b> untuk <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Klaim #{claimNumber}',
	ClaimSupervisingProvider: 'Penyedia pengawasan',
	ClaimSupplementaryInfo: 'Info tambahan',
	ClaimSupplementaryInfoPlaceholder: 'Tambahkan info tambahan',
	ClaimTrashedAction: '<mark>Klaim {claimNumber}</mark> telah dihapus',
	ClaimValidationFailure: 'Gagal memvalidasi klaim',
	ClaimsEmptyStateDescription: 'Tidak ada Klaim yang ditemukan.',
	ClainInsuranceTelephone: 'Telepon Asuransi (sertakan kode area)',
	Classic: 'Klasik',
	Clear: 'Jernih',
	ClearAll: 'Bersihkan semua',
	ClearSearchFilter: 'Jernih',
	ClearingHouse: 'Rumah Pembersihan',
	ClearingHouseClaimId: 'ID Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Referensi rumah pembersihan',
	ClearingHouseUnavailableError: 'Clearing house saat ini tidak tersedia. Harap coba lagi nanti.',
	ClickToUpload: 'Klik untuk mengunggah',
	Client: 'Klien',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} menambahkan {noteTitle, select, undefined { catatan } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Pilih klien dan hubungan mereka',
	ClientAndRelationshipSelectorTitle: 'Semua klien dan hubungan mereka',
	ClientAndRelationshipSelectorTitle1: 'Semua hubungan dari ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Jika Anda sedang menunggu panggilan video, panggilan tersebut akan segera muncul di sini. Jika Anda mengalami masalah, silakan hubungi orang yang memulainya.',
	ClientAppSubHeaderMyDocumentation: 'Dokumentasi saya',
	ClientAppointment: 'Janji temu klien',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} telah memesan {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Tidak ada janji temu yang ditemukan',
	ClientAppointmentsEmptyStateTitle:
		'Pantau janji temu klien Anda yang akan datang dan yang sudah lewat serta kehadiran mereka',
	ClientArchivedSuccessfulSnackbar: 'Berhasil diarsipkan **{name}**',
	ClientBalance: 'Saldo klien',
	ClientBilling: 'Penagihan',
	ClientBillingAddPaymentMethodDescription:
		'Tambahkan dan kelola metode pembayaran klien Anda untuk menyederhanakan proses pembuatan faktur dan penagihan mereka.',
	ClientBillingAndPaymentDueDate: 'Tenggat waktu',
	ClientBillingAndPaymentHistory: 'Riwayat penagihan dan pembayaran',
	ClientBillingAndPaymentInvoices: 'Faktur',
	ClientBillingAndPaymentIssueDate: 'Tanggal terbit',
	ClientBillingAndPaymentPrice: 'Harga',
	ClientBillingAndPaymentReceipt: 'Kuitansi',
	ClientBillingAndPaymentServices: 'Layanan',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Tim {count, plural, one {anggota} other {anggota}} ditugaskan!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Tim {count, plural, one {anggota} other {anggota}} tidak ditugaskan!',
	ClientBulkTagsAddedSuccessSnackbar: 'Tag ditambahkan!',
	ClientDuplicatesDeviewDescription:
		'Gabungkan beberapa catatan klien menjadi satu untuk menyatukan semua data—catatan, dokumen, janji temu, faktur, dan percakapan.',
	ClientDuplicatesPageMergeHeader: 'Pilih data yang ingin Anda simpan',
	ClientDuplicatesReviewHeader: 'Bandingkan potensi rekaman duplikat untuk digabungkan',
	ClientEmailChangeWarningDescription:
		'Memperbarui email klien akan menghapus akses mereka ke dokumentasi bersama apa pun, dan akan memberikan akses kepada pengguna dengan email baru',
	ClientFieldDateDescription: 'Format tanggal',
	ClientFieldDateLabel: 'Tanggal',
	ClientFieldDateRangeDescription: 'Berbagai tanggal',
	ClientFieldDateRangeLabel: 'Rentang tanggal',
	ClientFieldDateShowDateDescription: 'misal 29 tahun',
	ClientFieldDateShowDateRangeDescription: 'misalnya 2 minggu',
	ClientFieldEmailDescription: 'Alamat email',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Label bidang',
	ClientFieldLinearScaleDescription: 'Pilihan skala 1-10',
	ClientFieldLinearScaleLabel: 'Skala linier',
	ClientFieldLocationDescription: 'Alamat fisik atau pos',
	ClientFieldLocationLabel: 'Lokasi',
	ClientFieldLongTextDescription: 'Area teks panjang',
	ClientFieldLongTextLabel: 'Ayat',
	ClientFieldMultipleChoiceDropdownDescription: 'Pilih beberapa opsi dari daftar',
	ClientFieldMultipleChoiceDropdownLabel: 'Dropdown pilihan ganda',
	ClientFieldPhoneNumberDescription: 'Nomor telepon',
	ClientFieldPhoneNumberLabel: 'Telepon',
	ClientFieldPlaceholder: 'Pilih jenis bidang klien',
	ClientFieldSingleChoiceDropdownDescription: 'Pilih hanya satu opsi dari daftar',
	ClientFieldSingleChoiceDropdownLabel: 'Dropdown pilihan tunggal',
	ClientFieldTextDescription: 'Bidang masukan teks',
	ClientFieldTextLabel: 'Teks',
	ClientFieldYesOrNoDescription: 'Pilih dari opsi ya atau tidak',
	ClientFieldYesOrNoLabel: 'Ya | Tidak',
	ClientFileFormAccessLevelDescription:
		'Anda dan Tim selalu memiliki akses ke berkas yang Anda unggah. Anda dapat memilih untuk membagikan berkas ini dengan klien dan/atau relasi mereka',
	ClientFileSavedSuccessSnackbar: 'Berkas berhasil disimpan!',
	ClientFilesPageEmptyStateText: 'Tidak ada file yang diunggah',
	ClientFilesPageUploadFileButton: 'Unggah file',
	ClientHeaderBilling: 'Penagihan',
	ClientHeaderBillingAndReceipts: 'Penagihan ',
	ClientHeaderDocumentation: 'Dokumentasi',
	ClientHeaderDocuments: 'Dokumen',
	ClientHeaderFile: 'Dokumen',
	ClientHeaderHistory: 'Riwayat kesehatan',
	ClientHeaderInbox: 'Kotak Masuk',
	ClientHeaderNote: 'Catatan',
	ClientHeaderOverview: 'Ringkasan',
	ClientHeaderProfile: 'Pribadi',
	ClientHeaderRelationship: 'Hubungan',
	ClientHeaderRelationships: 'Hubungan',
	ClientId: 'ID Klien',
	ClientImportProcessingDescription: 'File masih dalam proses. Kami akan memberitahu Anda ketika ini selesai.',
	ClientImportReadyForMappingDescription:
		'Kami telah menyelesaikan pra-pemrosesan file Anda. Apakah Anda ingin memetakan kolom untuk menyelesaikan impor ini?',
	ClientImportReadyForMappingNotificationSubject:
		'Impor klien pra-pemrosesan selesai. File sekarang siap untuk pemetaan.',
	ClientInAppMessaging: 'Pesan dalam aplikasi klien',
	ClientInfoAddField: 'Tambahkan bidang lain',
	ClientInfoAddRow: 'Tambahkan baris',
	ClientInfoAlertMessage: 'Segala informasi yang diisi pada bagian ini akan mengisi catatan klien.',
	ClientInfoFormPrimaryText: 'Informasi klien',
	ClientInfoFormSecondaryText: 'Kumpulkan detail kontak',
	ClientInfoPlaceholder: `Nama klien, Alamat email, Nomor telepon
 Alamat fisik,
 Tanggal lahir`,
	ClientInformation: 'Informasi klien',
	ClientInsuranceTabLabel: 'Asuransi',
	ClientIntakeFormsNotSupported: `Templat formulir saat ini tidak didukung melalui penerimaan klien.
 Buat dan bagikan sebagai catatan klien.`,
	ClientIntakeModalDescription:
		'Email penerimaan akan dikirim ke klien Anda dan meminta mereka untuk melengkapi profil, mengunggah dokumen medis atau rujukan yang relevan. Mereka akan diberikan akses ke Portal Klien.',
	ClientIntakeModalTitle: 'Kirim intake ke {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Berhasil! Data Anda telah disimpan.',
	ClientIntakeSuccessSnackbar: 'Berhasil! Data Anda telah disimpan dan email konfirmasi telah dikirim.',
	ClientIsChargedProcessingFee: 'Pelanggan Anda akan membayar biaya pemrosesan',
	ClientListCreateButton: 'Klien baru',
	ClientListEmptyState: 'Tidak ada klien yang ditambahkan',
	ClientListPageItemArchive: 'Hapus klien',
	ClientListPageItemRemoveAccess: 'Hapus akses saya',
	ClientLocalizationPanelDescription: 'Bahasa dan zona waktu yang disukai klien.',
	ClientLocalizationPanelTitle: 'Bahasa dan zona waktu',
	ClientManagementAndEHR: 'Manajemen Klien ',
	ClientMergeResultSummaryBanner:
		'Menggabungkan catatan menggabungkan semua data klien, termasuk catatan, dokumen, janji temu, faktur, dan percakapan. Verifikasi keakuratan sebelum melanjutkan.',
	ClientMergeResultSummaryTitle: 'Ringkasan hasil penggabungan',
	ClientModalTitle: 'Klien baru',
	ClientMustHaveEmaillAccessErrorText: 'Klien/Kontak tanpa email',
	ClientMustHavePortalAccessErrorText: 'Klien/Kontak akan diminta untuk mendaftar',
	ClientMustHaveZoomAppConnectedErrorText: 'Hubungkan Zoom melalui Pengaturan > Aplikasi yang Terhubung',
	ClientNameFormat: 'Format nama klien',
	ClientNotFormAccessLevel: 'Dapat dilihat oleh:',
	ClientNotFormAccessLevelDescription:
		'Anda dan Tim selalu memiliki akses ke catatan yang Anda publikasikan. Anda dapat memilih untuk membagikan catatan ini dengan klien dan/atau relasinya',
	ClientNotRegistered: 'Tidak terdaftar',
	ClientNoteFormAddFileButton: 'Lampirkan file',
	ClientNoteFormChooseAClient: 'Pilih klien/kontak untuk melanjutkan',
	ClientNoteFormContent: 'Isi',
	ClientNoteItemDeleteConfirmationModalDescription:
		'Setelah dihapus, Anda tidak dapat mengambil kembali catatan ini.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Catatan diterbitkan dan dikunci.',
	ClientNotePublishedSuccessSnackbar: 'Catatan diterbitkan!',
	ClientNotes: 'Catatan klien',
	ClientNotesEmptyStateText: 'Untuk menambahkan catatan, buka profil klien dan klik tab Catatan.',
	ClientOnboardingChoosePasswordTitle1: 'Hampir Selesai!',
	ClientOnboardingChoosePasswordTitle2: 'Pilih kata sandi',
	ClientOnboardingCompleteIntake: 'Asupan lengkap',
	ClientOnboardingConfirmationScreenText:
		'Anda telah memberikan semua informasi yang dibutuhkan oleh {providerName}.	Konfirmasi alamat email Anda untuk memulai onboarding Anda. Jika Anda tidak menerimanya segera, silakan periksa folder spam Anda.',
	ClientOnboardingConfirmationScreenTitle: 'Bagus! Periksa kotak masuk Anda.',
	ClientOnboardingDashboardButton: 'Buka Dasbor',
	ClientOnboardingHealthRecordsDesc1: 'Apakah Anda ingin berbagi surat referensi, dokumen dengan {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Tambahkan Deskripsi (opsional)',
	ClientOnboardingHealthRecordsTitle: 'Dokumentasi',
	ClientOnboardingPasswordRequirements: 'Persyaratan',
	ClientOnboardingPasswordRequirementsConditions1: 'Minimal 9 karakter diperlukan',
	ClientOnboardingProviderIntroSignupButton: 'Daftar untuk diriku sendiri',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Daftar untuk anggota keluarga',
	ClientOnboardingProviderIntroTitle:
		'{name} telah mengundang Anda untuk bergabung dengan platform Carepatron mereka',
	ClientOnboardingRegistrationInstructions: 'Masukkan rincian pribadi Anda di bawah ini.',
	ClientOnboardingRegistrationTitle: 'Pertama kita butuh beberapa detail pribadi',
	ClientOnboardingStepFormsAndAgreements: 'Formulir dan Perjanjian',
	ClientOnboardingStepFormsAndAgreementsDesc1: 'Harap lengkapi formulir berikut untuk proses intake {providerName}',
	ClientOnboardingStepHealthDetails: 'Rincian Kesehatan',
	ClientOnboardingStepPassword: 'Kata sandi',
	ClientOnboardingStepYourDetails: 'Rincian Anda',
	ClientPaymentMethodDescription:
		'Simpan metode pembayaran pada profil Anda agar pemesanan janji temu dan pembuatan faktur berikutnya menjadi lebih cepat dan aman.',
	ClientPortal: 'Portal Klien',
	ClientPortalDashboardEmptyDescription: 'Riwayat janji temu dan kehadiran Anda akan ditampilkan di sini.',
	ClientPortalDashboardEmptyTitle:
		'Pantau semua janji temu yang akan datang, yang diminta, dan yang telah lewat beserta kehadiran Anda',
	ClientPreferredNotificationPanelDescription:
		'Kelola metode pilihan klien Anda untuk menerima pembaruan dan pemberitahuan melalui:',
	ClientPreferredNotificationPanelTitle: 'Metode pemberitahuan yang disukai',
	ClientProcessingFee: 'Pembayaran termasuk biaya pemrosesan {amount} ({currencyCode})',
	ClientProfileAddress: 'Alamat',
	ClientProfileDOB: 'Tanggal lahir',
	ClientProfileEmailHelperText: 'Menambahkan email memberikan akses portal',
	ClientProfileEmailHelperTextMoreInfo:
		'Memberikan akses klien ke portal memungkinkan anggota tim untuk berbagi catatan, file, dan dokumentasi lainnya',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Nomor identifikasi',
	ClientRelationshipsAddClientOwnerButton: 'Undang klien',
	ClientRelationshipsAddFamilyButton: 'Undang anggota keluarga',
	ClientRelationshipsAddStaffButton: 'Tambahkan akses staf',
	ClientRelationshipsEmptyStateText: 'Tidak ada hubungan yang ditambahkan',
	ClientRemovedSuccessSnackbar: 'Klien berhasil dihapus.',
	ClientResponsibility: 'Tanggung Jawab Klien',
	ClientSavedSuccessSnackbar: 'Klien berhasil disimpan.',
	ClientTableClientName: 'Nama klien',
	ClientTablePhone: 'Telepon',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Berhasil memulihkan arsip <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} mengedit {noteTitle, select, undefined { catatan } other {{noteTitle}}}',
	ClientView: 'Tampilan klien',
	Clients: 'Klien',
	ClientsTable: 'Meja Klien',
	ClinicalFormat: 'Format klinis',
	ClinicalPsychologist: 'Psikolog Klinis',
	Close: 'Menutup',
	CloseImportClientsModal: 'Apakah Anda yakin ingin membatalkan impor klien?',
	CloseReactions: 'Reaksi yang tertutup',
	Closed: 'Tertutup',
	Coaching: 'Pelatihan',
	Code: 'Kode',
	CodeErrorMessage: 'Kode diperlukan',
	CodePlaceholder: 'Kode',
	Coinsurance: 'Asuransi bersama',
	Collection: 'Koleksi',
	CollectionName: 'Nama Koleksi',
	Collections: 'Koleksi',
	ColorAppointmentsBy: 'Penunjukan warna oleh',
	ColorTheme: 'Tema warna',
	ColourCalendarBy: 'Kalender warna oleh',
	ComingSoon: 'Segera hadir',
	Community: 'Masyarakat',
	CommunityHealthLead: 'Pimpinan Kesehatan Masyarakat',
	CommunityHealthWorker: 'Petugas Kesehatan Masyarakat',
	CommunityTemplatesSectionDescription: 'Dibuat oleh komunitas Carepatron',
	CommunityTemplatesSectionTitle: 'Masyarakat',
	CommunityUser: 'Pengguna Komunitas',
	Complete: 'Menyelesaikan',
	CompleteAndLock: 'Lengkap dan terkunci',
	CompleteSetup: 'Pengaturan Lengkap',
	CompleteSetupSuccessDescription: 'Anda telah menyelesaikan beberapa langkah kunci untuk menguasai Carepatron.',
	CompleteSetupSuccessDescription2:
		'Buka lebih banyak cara untuk membantu merampingkan praktik Anda dan mendukung klien Anda.',
	CompleteSetupSuccessTitle: 'Sukses! Kamu luar biasa!',
	CompleteStripeSetup: 'Pengaturan Stripe Lengkap',
	Completed: 'Selesai',
	ComposeSms: 'Tulis SMS',
	ComputerSystemsAnalyst: 'Analis Sistem Komputer',
	Confirm: 'Mengonfirmasi',
	ConfirmDeleteAccountDescription:
		'Anda akan menghapus akun Anda. Tindakan ini tidak dapat dibatalkan. Jika Anda ingin melanjutkan, mohon konfirmasi di bawah ini.',
	ConfirmDeleteActionDescription:
		'Apakah Anda yakin ingin menghapus tindakan ini? Tindakan ini tidak dapat dibatalkan',
	ConfirmDeleteAutomationDescription:
		'Apakah Anda yakin ingin menghapus otomatisasi ini? Tindakan ini tidak dapat dibatalkan.',
	ConfirmDeleteScheduleDescription:
		'Menghapus jadwal <strong>{scheduleName}</strong> akan menghapusnya dari jadwal Anda dan dapat mengubah layanan daring Anda yang tersedia. Tindakan ini tidak dapat dibatalkan.',
	ConfirmDraftResponseContinue: 'Lanjutkan dengan respons',
	ConfirmDraftResponseDescription:
		'Jika Anda menutup halaman ini, respons Anda akan tetap berupa draf. Anda dapat kembali dan melanjutkan kapan saja.',
	ConfirmDraftResponseSubmitResponse: 'Kirimkan tanggapan',
	ConfirmDraftResponseTitle: 'Respon Anda belum dikirim',
	ConfirmIfUserIsClientDescription: `Formulir pendaftaran yang Anda isi ditujukan untuk Penyedia (misalnya tim/organisasi kesehatan).
 Jika ini adalah kesalahan, Anda dapat memilih "Lanjutkan sebagai klien" dan kami akan menyiapkan portal klien Anda`,
	ConfirmIfUserIsClientNoButton: 'Daftar sebagai Penyedia',
	ConfirmIfUserIsClientTitle: 'Sepertinya Anda adalah klien',
	ConfirmIfUserIsClientYesButton: 'Lanjutkan sebagai klien',
	ConfirmKeepSeparate: 'Konfirmasikan untuk tetap terpisah',
	ConfirmMerge: 'Konfirmasi penggabungan',
	ConfirmPassword: 'Konfirmasi kata sandi',
	ConfirmRevertClaim: 'Ya, kembalikan status',
	ConfirmSignupAccessCode: 'Kode konfirmasi',
	ConfirmSignupButtom: 'Mengonfirmasi',
	ConfirmSignupDescription:
		'Silakan masukkan alamat email Anda dan kode konfirmasi yang baru saja kami kirimkan kepada Anda.',
	ConfirmSignupSubTitle: 'Periksa folder Spam - jika email belum sampai',
	ConfirmSignupSuccessSnackbar:
		'Bagus, kami telah mengonfirmasi akun Anda! Sekarang Anda dapat masuk menggunakan email dan kata sandi Anda',
	ConfirmSignupTitle: 'Konfirmasi akun',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: 'Konfirmasi langganan {price} {isMonthly, select, true {per bulan} other {per tahun}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Setelah klien dihapus, Anda tidak akan dapat lagi mengakses informasi mereka.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Hapus {count, plural, one {# klien} other {# klien}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Setelah kontak dihapus, Anda tidak akan dapat lagi mengakses informasinya.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Hapus {count, plural, one {# kontak} other {# kontak}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Ini adalah tindakan permanen. Setelah anggota tim dihapus, Anda tidak akan dapat lagi mengakses informasi mereka.',
	ConfirmationModalBulkDeleteMembersTitleId: 'Hapus {count, plural, one {# anggota tim} other {# anggota tim}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Menutup catatan ini akan mengakhiri semua transkripsi yang sedang berlangsung. Apakah Anda yakin ingin melanjutkan?',
	ConfirmationModalDeleteClientField:
		'Ini adalah tindakan permanen. Setelah kolom dihapus, kolom tersebut tidak akan dapat diakses lagi oleh klien Anda yang tersisa.',
	ConfirmationModalDeleteSectionMessage:
		'Setelah dihapus, semua pertanyaan di bagian ini akan dihapus. Tindakan ini tidak dapat dibatalkan.',
	ConfirmationModalDeleteService:
		'Ini adalah tindakan permanen. Setelah layanan dihapus, layanan tersebut tidak akan dapat diakses lagi di ruang kerja Anda.',
	ConfirmationModalDeleteServiceGroup:
		'Menghapus koleksi akan menghapus semua layanan dari grup dan akan kembali ke daftar layanan Anda. Tindakan ini tidak dapat dibatalkan.',
	ConfirmationModalDeleteTranscript: 'Apakah Anda yakin ingin menghapus transkrip?',
	ConfirmationModalDescriptionDeleteClient:
		'Setelah klien dihapus, Anda tidak akan dapat lagi mengakses informasi klien.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Setelah Anda menghapus akses, Anda tidak akan dapat lagi melihat informasi klien.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Profil mereka tidak akan dihapus, hanya dihapus sebagai hubungan klien ini.',
	ConfirmationModalDescriptionRemoveStaff: 'Apakah Anda yakin ingin menghapus orang ini dari penyedia?',
	ConfirmationModalEndSession: 'Apakah Anda yakin ingin mengakhiri sesi?',
	ConfirmationModalTitle: 'Apa kamu yakin?',
	Confirmed: 'Dikonfirmasi',
	ConflictTimezoneWarningMessage: 'Konflik mungkin terjadi karena beberapa zona waktu',
	Connect: 'Menghubungkan',
	ConnectExistingClientOrContact: 'Buat klien/kontak baru',
	ConnectInboxGoogleDescription: 'Tambahkan akun Gmail atau daftar grup Google',
	ConnectInboxMicrosoftDescription: 'Tambahkan akun Outlook, Office365 atau Exchange',
	ConnectInboxModalDescription:
		'Hubungkan aplikasi Anda untuk mengirim, menerima, dan melacak semua komunikasi Anda dengan mudah di satu tempat terpusat.',
	ConnectInboxModalExistingDescription:
		'Gunakan koneksi yang ada dari pengaturan aplikasi yang terhubung untuk menyederhanakan proses konfigurasi.',
	ConnectInboxModalExistingTitle: 'Aplikasi terhubung yang ada di Carepatron',
	ConnectInboxModalTitle: 'Hubungkan kotak masuk',
	ConnectToStripe: 'Hubungkan ke Stripe',
	ConnectZoom: 'Hubungkan Zoom',
	ConnectZoomModalDescription: 'Izinkan Carepatron mengelola panggilan video untuk janji temu Anda.',
	ConnectedAppDisconnectedNotificationSubject:
		'Kami telah kehilangan koneksi ke akun {account}. Harap sambungkan kembali',
	ConnectedAppSyncDescription:
		'Kelola aplikasi yang terhubung untuk membuat acara di kalender pihak ketiga langsung dari Carepatron.',
	ConnectedApps: 'Aplikasi yang terhubung',
	ConnectedAppsGMailDescription: 'Tambahkan akun Gmail atau daftar Google Group',
	ConnectedAppsGoogleCalendarDescription: 'Tambahkan akun kalender atau daftar grup Google',
	ConnectedAppsGoogleDescription: 'Tambahkan akun Gmail Anda dan sinkronkan kalender Google',
	ConnectedAppsMicrosoftDescription: 'Tambahkan akun Outlook, Office365 atau Exchange',
	ConnectedCalendars: 'Kalender Terhubung',
	ConsentDocumentation: 'Formulir dan perjanjian',
	ConsentDocumentationPublicTemplateError:
		'Untuk alasan keamanan, Anda hanya dapat memilih templat dari tim Anda (non-publik).',
	ConstructionWorker: 'Pekerja Konstruksi',
	Consultant: 'Konsultan',
	Contact: 'Kontak',
	ContactAccessTypeHelperText: 'Memungkinkan admin keluarga memperbarui info',
	ContactAccessTypeHelperTextMoreInfo:
		'Ini akan memungkinkan Anda untuk membagikan catatan/dokumen tentang {clientFirstName}',
	ContactAddressLabelBilling: 'Penagihan',
	ContactAddressLabelHome: 'Rumah',
	ContactAddressLabelOthers: 'Yang lain',
	ContactAddressLabelWork: 'Bekerja',
	ContactChangeConfirmation:
		'Mengubah kontak faktur akan menghapus semua item baris yang terkait dengan <mark>{contactName}</mark>',
	ContactDetails: 'Rincian kontak',
	ContactEmailLabelOthers: 'Yang lain',
	ContactEmailLabelPersonal: 'Pribadi',
	ContactEmailLabelSchool: 'Sekolah',
	ContactEmailLabelWork: 'Bekerja',
	ContactInformation: 'Informasi Kontak',
	ContactInformationText: 'Informasi kontak',
	ContactListCreateButton: 'Kontak baru',
	ContactName: 'Nama kontak',
	ContactPhoneLabelHome: 'Rumah',
	ContactPhoneLabelMobile: 'Seluler',
	ContactPhoneLabelSchool: 'Sekolah',
	ContactPhoneLabelWork: 'Bekerja',
	ContactRelationship: 'Hubungan kontak',
	ContactRelationshipFormAccessType: 'Berikan akses ke informasi bersama',
	ContactRelationshipGrantAccessInfo: 'Ini akan memungkinkan Anda untuk berbagi catatan ',
	ContactSupport: 'Hubungi dukungan',
	Contacts: 'Kontak',
	ContainerIdNotSet: 'ID Kontainer belum ditetapkan',
	Contemporary: 'Kontemporer',
	Continue: 'Melanjutkan',
	ContinueDictating: 'Terus mendikte',
	ContinueEditing: 'Lanjutkan mengedit',
	ContinueImport: 'Lanjutkan impor',
	ContinueTranscription: 'Lanjutkan transkripsi',
	ContinueWithApple: 'Lanjutkan dengan Apple',
	ContinueWithGoogle: 'Lanjutkan dengan Google',
	Conversation: 'Percakapan',
	Copay: 'Pembayaran bersama',
	CopayOrCoinsurance: 'Pembayaran Bersama atau Asuransi Bersama',
	Copayment: 'Pembayaran bersama',
	CopiedToClipboard: 'Disalin ke papan klip',
	Copy: 'Menyalin',
	CopyAddressSuccessSnackbar: 'Alamat disalin ke clipboard',
	CopyCode: 'Salin Kode',
	CopyCodeToClipboardSuccess: 'Kode disalin ke clipboard',
	CopyEmailAddressSuccessSnackbar: 'Alamat email disalin ke clipboard',
	CopyLink: 'Salin tautan',
	CopyLinkForCall: 'Salin tautan ini untuk membagikan panggilan ini:',
	CopyLinkSuccessSnackbar: 'Salin tautan ke papan klip',
	CopyMeetingLink: 'Salin tautan rapat',
	CopyPaymentLink: 'Salin tautan pembayaran',
	CopyPhoneNumberSuccessSnackbar: 'Nomor telepon telah disalin ke clipboard',
	CopyTemplateLink: 'Salin tautan ke templat',
	CopyTemplateLinkSuccess: 'Salin tautan ke papan klip',
	CopyToClipboardError: 'Tidak dapat menyalin ke clipboard. Silakan coba lagi.',
	CopyToTeamTemplates: 'Salin ke templat Tim',
	CopyToWorkspace: 'Salin ke ruang kerja',
	Cosmetologist: 'Ahli kecantikan',
	Cost: 'Biaya',
	CostErrorMessage: 'Biaya diperlukan',
	Counseling: 'Konseling',
	Counselor: 'Konselor',
	Counselors: 'Konselor',
	CountInvoicesAdded: '{count, plural, one {# Faktur ditambahkan} other {# Faktur ditambahkan}}',
	CountNotesAdded: '{count, plural, one {# Catatan ditambahkan} other {# Catatan ditambahkan}}',
	CountSelected: '{count} terpilih',
	CountTimes: '{count} kali',
	Country: 'Negara',
	Cousin: 'Sepupu',
	CoverageType: 'Jenis cakupan',
	Covered: 'Tercakup',
	Create: 'Membuat',
	CreateANewClient: 'Buat klien baru',
	CreateAccount: 'Buat akun',
	CreateAndSignNotes: 'Membuat dan menandatangani catatan dengan klien',
	CreateAvailabilityScheduleFailure: 'Gagal membuat jadwal ketersediaan baru',
	CreateAvailabilityScheduleSuccess: 'Berhasil membuat jadwal ketersediaan baru',
	CreateBillingItems: 'Buat item penagihan',
	CreateCallFormButton: 'Mulai panggilan',
	CreateCallFormInviteOnly: 'Hanya mengundang',
	CreateCallFormInviteOnlyMoreInfo:
		'Hanya orang yang diundang ke panggilan ini yang dapat bergabung. Untuk membagikan panggilan ini dengan orang lain, cukup hapus centang ini dan salin/tempel tautan di halaman berikutnya',
	CreateCallFormRecipients: 'Penerima',
	CreateCallFormRegion: 'Wilayah tuan rumah',
	CreateCallModalAddClientContactSelectorLabel: 'Kontak klien',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Cari berdasarkan nama klien',
	CreateCallModalAddStaffSelectorLabel: 'Anggota tim (opsional)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Cari berdasarkan nama staf',
	CreateCallModalDescription:
		'Mulai panggilan dan undang anggota staf dan/atau kontak. Atau, Anda dapat menghapus centang pada kotak "Pribadi" untuk membuat panggilan ini dapat dibagikan kepada siapa pun dengan Carepatron',
	CreateCallModalTitle: 'Mulai panggilan',
	CreateCallModalTitleLabel: 'Judul (opsional)',
	CreateCallNoPersonIdToolTip: 'Hanya kontak/klien dengan akses portal yang dapat bergabung dalam panggilan',
	CreateClaim: 'Buat klaim',
	CreateClaimCompletedMessage: 'Klaim Anda telah dibuat.',
	CreateClientModalTitle: 'Klien baru',
	CreateContactModalTitle: 'Kontak baru',
	CreateContactRelationshipButton: 'Tambahkan hubungan',
	CreateContactSelectorDefaultOption: '  Buat kontak',
	CreateContactWithRelationshipFormAccessType: 'Berikan akses ke info yang dibagikan ',
	CreateDocumentDnDPrompt: 'Seret dan lepas untuk mengunggah file',
	CreateDocumentSizeLimit: 'Batas ukuran per file {size}MB. Total {total} file.',
	CreateFreeAccount: 'Buat akun gratis',
	CreateInvoice: 'Buat faktur',
	CreateLink: 'Buat tautan',
	CreateNew: 'Buat baru',
	CreateNewAppointment: 'Buat janji temu baru',
	CreateNewClaim: 'Buat klaim baru',
	CreateNewClaimForAClient: 'Buat klaim baru untuk klien.',
	CreateNewClient: 'Buat klien baru',
	CreateNewConnection: 'Koneksi baru',
	CreateNewContact: 'Buat kontak baru',
	CreateNewField: 'Buat bidang baru',
	CreateNewLocation: 'Lokasi baru',
	CreateNewService: 'Buat layanan baru',
	CreateNewServiceGroupFailure: 'Gagal membuat koleksi baru',
	CreateNewServiceGroupMenu: 'Koleksi baru',
	CreateNewServiceGroupSuccess: 'Berhasil membuat koleksi baru',
	CreateNewServiceMenu: 'Layanan baru',
	CreateNewTeamMember: 'Buat anggota tim baru',
	CreateNewTemplate: 'Template baru',
	CreateNote: 'Buat catatan',
	CreateSuperbillReceipt: 'Superbill baru',
	CreateSuperbillReceiptSuccess: 'Berhasil membuat tanda terima Superbill',
	CreateTemplateFolderSuccessMessage: 'Berhasil membuat {folderTitle}',
	Created: 'Dibuat',
	CreatedAt: 'Dibuat {timestamp}',
	Credit: 'Kredit',
	CreditAdded: 'Kredit diterapkan',
	CreditAdjustment: 'Penyesuaian kredit',
	CreditAdjustmentReasonHelperText: 'Ini adalah catatan internal dan tidak akan terlihat oleh klien Anda.',
	CreditAdjustmentReasonPlaceholder:
		'Menambahkan alasan penyesuaian dapat membantu saat meninjau transaksi yang dapat ditagih',
	CreditAmount: '{amount} NC',
	CreditBalance: 'Saldo kredit',
	CreditCard: 'Kartu kredit',
	CreditCardExpire: 'Berlaku hingga {exp_month}/{exp_year}',
	CreditCardNumber: 'Nomor kartu kredit',
	CreditDebitCard: 'Kartu',
	CreditIssued: 'Kredit dikeluarkan',
	CreditsUsed: 'Kredit yang digunakan',
	Crop: 'Tanaman',
	Currency: 'Mata uang',
	CurrentCredit: 'Kredit saat ini',
	CurrentEventTime: 'Waktu kejadian saat ini',
	CurrentPlan: 'Rencana saat ini',
	Custom: 'Kebiasaan',
	CustomRange: 'Rentang khusus',
	CustomRate: 'Tarif khusus',
	CustomRecurrence: 'Pengulangan Kustom',
	CustomServiceAvailability: 'Ketersediaan layanan',
	CustomerBalance: 'Saldo pelanggan',
	CustomerName: 'Nama pelanggan',
	CustomerNameIsRequired: 'Nama pelanggan diperlukan',
	CustomerServiceRepresentative: 'Perwakilan Layanan Pelanggan',
	CustomiseAppointments: 'Sesuaikan janji temu',
	CustomiseBookingLink: 'Sesuaikan opsi pemesanan',
	CustomiseBookingLinkServicesInfo: 'Klien hanya dapat memilih layanan yang dapat dipesan',
	CustomiseBookingLinkServicesLabel: 'Layanan',
	CustomiseClientRecordsAndWorkspace: 'Sesuaikan catatan klien dan ruang kerja Anda',
	CustomiseClientSettings: 'Sesuaikan pengaturan klien',
	Customize: 'Sesuaikan',
	CustomizeAppearance: 'Sesuaikan penampilan',
	CustomizeAppearanceDesc:
		'Sesuaikan tampilan pemesanan online Anda agar sesuai dengan merek Anda dan optimalkan bagaimana layanan Anda ditampilkan kepada klien.',
	CustomizeClientFields: 'Sesuaikan bidang klien',
	CustomizeInvoiceTemplate: 'Sesuaikan template faktur',
	CustomizeInvoiceTemplateDescription: 'Buat faktur profesional yang mencerminkan merek Anda dengan mudah.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX diperlukan',
	Daily: 'Sehari-hari',
	DanceTherapist: 'Terapis Tari',
	DangerZone: 'Zona bahaya',
	Dashboard: 'Dasbor',
	Date: 'Tanggal',
	DateAndTime: 'Tanggal ',
	DateDue: 'Tanggal jatuh tempo',
	DateErrorMessage: 'Tanggal diperlukan',
	DateFormPrimaryText: 'Tanggal',
	DateFormSecondaryText: 'Pilih dari pemilih tanggal',
	DateIssued: 'Tanggal terbit',
	DateOfPayment: 'Tanggal pembayaran',
	DateOfService: 'Tanggal layanan',
	DateOverride: 'Penggantian tanggal',
	DateOverrideColor: 'Warna penggantian tanggal',
	DateOverrideInfo:
		'Penggantian tanggal memungkinkan praktisi menyesuaikan ketersediaan mereka secara manual untuk tanggal tertentu dengan mengesampingkan jadwal reguler.',
	DateOverrideInfoBanner:
		'Hanya layanan tertentu untuk penggantian tanggal ini yang dapat dipesan dalam slot waktu ini; pemesanan daring lainnya tidak diperbolehkan.',
	DateOverrides: 'Penggantian tanggal',
	DatePickerFormPrimaryText: 'Tanggal',
	DatePickerFormSecondaryText: 'Pilih tanggal',
	DateRange: 'Rentang tanggal',
	DateRangeFormPrimaryText: 'Rentang tanggal',
	DateRangeFormSecondaryText: 'Pilih rentang tanggal',
	DateReceived: 'Tanggal diterima',
	DateSpecificHours: 'Tanggal jam tertentu',
	DateSpecificHoursDescription:
		'Tambahkan tanggal saat ketersediaan Anda berubah dari jam yang dijadwalkan atau untuk menawarkan layanan pada tanggal tertentu.',
	DateUploaded: 'Diunggah {date, date, medium}',
	Dates: 'Tanggal',
	Daughter: 'Anak perempuan',
	Day: 'Hari',
	DayPlural: '{count, plural, one {hari} other {hari}}',
	Days: 'Hari',
	DaysPlural: '{age, plural, one {# hari} other {# hari}}',
	DeFacto: 'Secara de facto',
	Deactivated: 'Dinonaktifkan',
	Debit: 'Debet',
	DecreaseIndent: 'Kurangi indentasi',
	Deductibles: 'Pengurangan',
	Default: 'Bawaan',
	DefaultBillingProfile: 'Profil penagihan default',
	DefaultDescription: 'Deskripsi bawaan',
	DefaultEndOfLine: 'Tidak ada lagi item',
	DefaultInPerson: 'Janji temu klien',
	DefaultInvoiceTitle: 'Judul default',
	DefaultNotificationSubject: 'Anda telah menerima pemberitahuan baru untuk {notificationType}',
	DefaultPaymentMethod: 'Metode pembayaran default',
	DefaultService: 'Layanan standar',
	DefaultValue: 'Bawaan',
	DefaultVideo: 'Email janji temu video klien',
	DefinedTemplateType: '{invoiceTemplate} template',
	Delete: 'Menghapus',
	DeleteAccountButton: 'Hapus akun',
	DeleteAccountDescription: 'Hapus akun Anda dari platform',
	DeleteAccountPanelInfoAlert:
		'Anda harus menghapus ruang kerja sebelum menghapus profil Anda. Untuk melanjutkan, beralihlah ke ruang kerja dan pilih Setelan > Setelan Ruang Kerja.',
	DeleteAccountTitle: 'Hapus akun',
	DeleteAppointment: 'Hapus janji temu',
	DeleteAppointmentDescription: 'Apakah Anda yakin ingin menghapus janji temu ini? Anda dapat memulihkannya nanti.',
	DeleteAvailabilityScheduleFailure: 'Gagal menghapus jadwal ketersediaan',
	DeleteAvailabilityScheduleSuccess: 'Berhasil menghapus jadwal ketersediaan',
	DeleteBillable: 'Hapus yang dapat ditagih',
	DeleteBillableConfirmationMessage:
		'Apakah Anda yakin ingin menghapus tagihan ini? Tindakan ini tidak dapat dibatalkan.',
	DeleteBillingProfileConfirmationMessage: 'Ini akan menghapus profil penagihan secara permanen.',
	DeleteCardConfirmation:
		'Ini adalah tindakan permanen. Setelah kartu dihapus, Anda tidak akan dapat mengaksesnya lagi.',
	DeleteCategory: 'Hapus kategori (ini tidak permanen kecuali perubahan disimpan)',
	DeleteClientEventConfirmationDescription: 'Ini akan dihapus secara permanen.',
	DeleteClients: 'Hapus klien',
	DeleteCollection: 'Hapus Koleksi',
	DeleteColumn: 'Hapus kolom',
	DeleteConversationConfirmationDescription: 'Hapus percakapan ini selamanya. Tindakan ini tidak dapat dibatalkan.',
	DeleteConversationConfirmationTitle: 'Hapus percakapan selamanya',
	DeleteExternalEventDescription: 'Apakah Anda yakin ingin menghapus janji temu ini?',
	DeleteFileConfirmationModalPrompt: 'Setelah dihapus, Anda tidak dapat mengambil kembali berkas ini.',
	DeleteFolder: 'Hapus folder',
	DeleteFolderConfirmationMessage:
		'Apakah Anda yakin ingin menghapus folder ini {name}? Semua item di dalam folder ini juga akan dihapus. Anda dapat memulihkannya nanti.',
	DeleteForever: 'Hapus selamanya',
	DeleteInsurancePayerConfirmationMessage:
		'Menghapus {payer} akan menghapusnya dari daftar pembayar asuransi Anda. Tindakan ini permanen dan tidak dapat dipulihkan.',
	DeleteInsurancePayerFailure: 'Gagal menghapus pembayar asuransi',
	DeleteInsurancePolicyConfirmationMessage: 'Ini akan menghapus polis asuransi secara permanen.',
	DeleteInvoiceConfirmationDescription:
		'Tindakan ini tidak dapat dibatalkan. Tindakan ini akan menghapus faktur dan semua pembayaran yang terkait dengannya secara permanen.',
	DeleteLocationConfirmation:
		'Menghapus lokasi adalah tindakan permanen. Setelah Anda menghapusnya, Anda tidak akan dapat mengaksesnya lagi. Tindakan ini tidak dapat dibatalkan.',
	DeletePayer: 'Hapus Pembayar',
	DeletePracticeWorkspace: 'Hapus ruang kerja praktik',
	DeletePracticeWorkspaceDescription: 'Hapus ruang kerja praktik ini secara permanen',
	DeletePracticeWorkspaceFailedSnackbar: 'Gagal menghapus ruang kerja',
	DeletePracticeWorkspaceModalCancelButton: 'Ya, batalkan langganan saya',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Sebelum Anda melanjutkan penghapusan ruang kerja Anda, Anda harus membatalkan langganan Anda terlebih dahulu.',
	DeletePracticeWorkspaceModalConfirmButton: 'Ya, hapus ruang kerja secara permanen',
	DeletePracticeWorkspaceModalDescription:
		'Ruang kerja {name} akan dihapus secara permanen dan semua anggota tim akan kehilangan akses. Unduh semua data atau pesan penting yang mungkin Anda perlukan sebelum penghapusan dilakukan. Tindakan ini tidak dapat dibatalkan.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Keputusan ini dibuat karena:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Alasan',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'Harap beritahu kami mengapa Anda ingin menghapus akun Anda.',
	DeletePracticeWorkspaceModalTitle: 'Apa kamu yakin?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Akses semua anggota tim telah dihapus',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} telah berhasil dihapus',
	DeletePublicTemplateContent: 'Ini hanya akan menghapus templat publik dan bukan templat tim Anda.',
	DeleteRecurringAppointmentModalTitle: 'Hapus janji temu yang berulang',
	DeleteRecurringEventModalTitle: 'Hapus rapat yang berulang',
	DeleteRecurringReminderModalTitle: 'Hapus pengingat berulang',
	DeleteRecurringTaskModalTitle: 'Hapus tugas yang berulang',
	DeleteReminderConfirmation:
		'Ini adalah tindakan permanen. Setelah pengingat dihapus, Anda tidak akan dapat mengaksesnya lagi. Hanya akan memengaruhi janji temu baru',
	DeleteSection: 'Hapus bagian',
	DeleteSectionInfo:
		'Menghapus bagian <strong>{section}</strong> akan menyembunyikan semua bidang yang ada di dalamnya. Tindakan ini tidak dapat dibatalkan.',
	DeleteSectionWarning:
		'Bidang inti tidak dapat dihapus dan akan dipindahkan ke bagian yang ada <strong>{section}</strong>.',
	DeleteServiceFailure: 'Gagal menghapus layanan',
	DeleteServiceSuccess: 'Berhasil menghapus layanan',
	DeleteStaffScheduleOverrideDescription:
		'M menghapus penggantian tanggal ini pada {value} akan menghapusnya dari jadwal Anda dan mungkin mengubah layanan daring Anda yang tersedia. Tindakan ini tidak dapat dibatalkan.',
	DeleteSuperbillConfirmationDescription:
		'Tindakan ini tidak dapat dibatalkan. Tindakan ini akan menghapus tanda terima Superbill secara permanen.',
	DeleteSuperbillFailure: 'Gagal menghapus tanda terima Superbill',
	DeleteSuperbillSuccess: 'Berhasil menghapus tanda terima Superbill',
	DeleteTaxRateConfirmationDescription: 'Apakah Anda yakin ingin menghapus tarif pajak ini?',
	DeleteTemplateContent: 'Tindakan ini tidak dapat dibatalkan',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} berhasil dihapus',
	DeleteTemplateSuccessMessage: '{templateTitle} berhasil dihapus',
	DeleteTemplateTitle: 'Apakah Anda yakin ingin menghapus templat ini?',
	DeleteTranscript: 'Hapus transkrip',
	DeleteWorkspace: 'Hapus ruang kerja',
	Deleted: 'Dihapus',
	DeletedBy: 'Dihapus oleh',
	DeletedContact: 'Kontak dihapus',
	DeletedOn: 'Dihapus pada',
	DeletedStatusLabel: 'Status dihapus',
	DeletedUserTooltip: 'Klien ini telah dihapus',
	DeliveryMethod: 'Metode Pengiriman',
	Demo: 'Demo',
	Denied: 'Ditolak',
	Dental: 'Dental',
	DentalAssistant: 'Asisten Gigi',
	DentalHygienist: 'Ahli Kebersihan Gigi',
	Dentist: 'Dokter gigi',
	Dentists: 'Dokter gigi',
	Description: 'Keterangan',
	DescriptionMustNotExceed: 'Deskripsi tidak boleh melebihi {max} karakter',
	DetailDurationWithStaff: '{duration} menit{staffName, select, null {} other { bersama {staffName}}}',
	Details: 'Rincian',
	Devices: 'Perangkat',
	Diagnosis: 'Diagnosa',
	DiagnosisAndBillingItems: 'Diagnosa ',
	DiagnosisCode: 'Kode diagnosis',
	DiagnosisCodeErrorMessage: 'Kode diagnosis diperlukan',
	DiagnosisCodeSelectorPlaceholder: 'Cari dan tambahkan dari kode diagnostik ICD-10',
	DiagnosisCodeSelectorTooltip:
		'Kode diagnosis digunakan untuk mengotomatiskan penerimaan superbills untuk penggantian asuransi',
	DiagnosticCodes: 'Kode diagnostik',
	Dictate: 'Mendikte',
	DictatingIn: 'Mendikte dalam',
	Dictation: 'Dikte',
	DidNotAttend: 'Tidak hadir',
	DidNotComplete: 'Tidak selesai',
	DidNotProviderEnoughValue: 'Tidak memberikan nilai yang cukup',
	DidntProvideEnoughValue: 'Tidak memberikan nilai yang cukup',
	DieteticsOrNutrition: 'Dietetika atau nutrisi',
	Dietician: 'Ahli diet',
	Dieticians: 'Ahli gizi',
	Dietitian: 'Ahli diet',
	DigitalSign: 'Tanda tangani di sini:',
	DigitalSignHelp: '(Klik/tekan ke bawah untuk menggambar)',
	DirectDebit: 'Debit langsung',
	DirectTextLink: 'Tautan teks langsung',
	Disable: 'Cacat',
	DisabledEmailInfo: 'Kami tidak dapat memperbarui alamat email Anda karena akun Anda tidak dikelola oleh kami',
	Discard: 'Membuang',
	DiscardChanges: 'Buang perubahan',
	DiscardDrafts: 'Buang draf',
	Disconnect: 'Memutuskan',
	DisconnectAppConfirmation: 'Apakah Anda ingin memutuskan sambungan aplikasi ini?',
	DisconnectAppConfirmationDescription: 'Apakah Anda yakin ingin memutuskan sambungan aplikasi ini?',
	DisconnectAppConfirmationTitle: 'Putuskan aplikasi',
	Discount: 'Diskon',
	DisplayCalendar: 'Tampilan di Carepatron',
	DisplayName: 'Nama tampilan',
	DisplayedToClients: 'Ditampilkan kepada klien',
	DiversionalTherapist: 'Terapis Pengalihan',
	DoItLater: 'Lakukan nanti saja',
	DoNotImport: 'Jangan mengimpor',
	DoNotSend: 'Jangan kirim',
	DoThisLater: 'Lakukan ini nanti',
	DoYouWantToEndSession: 'Apakah Anda ingin melanjutkan atau mengakhiri sesi Anda sekarang?',
	Doctor: 'Dokter',
	Doctors: 'Dokter',
	DoesNotRepeat: 'Tidak mengulang',
	DoesntWorkWellWithExistingTools: 'Tidak berfungsi dengan baik dengan alat atau alur kerja kami yang ada',
	DogWalker: 'Pengasuh Anjing',
	Done: 'Selesai',
	DontAllowClientsToCancel: 'Jangan izinkan klien untuk membatalkan',
	DontHaveAccount: 'Belum punya akun?',
	DontSend: 'Jangan kirim',
	Double: 'Dobel',
	DowngradeTo: 'Turun kelas ke {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Maaf, Anda tidak dapat menurunkan paket karena jumlah anggota tim Anda terlalu banyak. Harap hapus beberapa dari penyedia layanan Anda dan coba lagi.',
	Download: 'Unduh',
	DownloadAsPdf: 'Unduh sebagai PDF',
	DownloadERA: 'Unduh ERA',
	DownloadPDF: 'Unduh PDF',
	DownloadTemplateFileName: 'Carepatron Switching Template.csv',
	DownloadTemplateTileDescription: 'Gunakan templat spreadsheet kami untuk mengatur dan mengunggah klien Anda.',
	DownloadTemplateTileLabel: 'Unduh Templat',
	Downloads: '{number, plural, one {<span>#</span> Unduh} other {<span>#</span> Unduhan}}',
	DoxyMe: 'Doxy.saya',
	Draft: 'Draf',
	DraftResponses: 'Draf tanggapan',
	DraftSaved: 'Perubahan yang disimpan',
	DragAndDrop: 'seret dan jatuhkan',
	DragDropText: 'Seret dan lepas dokumen kesehatan',
	DragToMove: 'Seret untuk memindahkan',
	DragToMoveOrActivate: 'Seret untuk memindahkan atau mengaktifkan',
	DramaTherapist: 'Terapis Drama',
	DropdownFormFieldPlaceHolder: 'Pilih opsi dari daftar',
	DropdownFormPrimaryText: 'tarik-turun',
	DropdownFormSecondaryText: 'Pilih dari daftar pilihan',
	DropdownTextFieldError: 'Teks opsi dropdown tidak boleh kosong',
	DropdownTextFieldPlaceholder: 'Tambahkan opsi dropdown',
	Due: 'Tenggat',
	DueDate: 'Tenggat waktu',
	Duplicate: 'Duplikat',
	DuplicateAvailabilityScheduleFailure: 'Gagal menduplikasi jadwal ketersediaan',
	DuplicateAvailabilityScheduleSuccess: 'Berhasil menduplikasi jadwal {name}',
	DuplicateClientBannerAction: 'Tinjauan',
	DuplicateClientBannerDescription:
		'Penggabungan catatan klien duplikat akan menggabungkannya menjadi satu, dengan tetap menyimpan semua informasi klien yang unik.',
	DuplicateClientBannerTitle: '{count} Duplikat ditemukan',
	DuplicateColumn: 'Kolom duplikat',
	DuplicateContactFieldSettingErrorSnackbar: 'Tidak dapat memiliki nama bagian duplikat',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Tidak dapat memiliki nama bidang duplikat',
	DuplicateEmailError: 'Email duplikat',
	DuplicateHeadingName: 'Bagian {name} sudah ada',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Faktur dengan "nomor faktur" yang sama sudah ada.',
	DuplicateRecords: 'Rekaman duplikat',
	DuplicateRecordsMinimumError: 'Minimal 2 catatan harus dipilih',
	DuplicateRecordsRequired: 'Pilih setidaknya 1 rekaman untuk dipisahkan',
	DuplicateServiceFailure: 'Gagal menduplikasi <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Berhasil menduplikasi <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Berhasil menduplikasi folder',
	DuplicateTemplateSuccess: 'Template berhasil diduplikasi',
	DurationInMinutes: '{duration}menit',
	Dx: 'Bahasa Indonesia: DX',
	DxCode: 'kode DX',
	DxCodeSelectPlaceholder: 'Cari dan tambahkan dari kode ICD-10',
	EIN: 'SEBUAH',
	EMG: 'Elektromagnetik',
	EPSDT: 'Bahasa Indonesia: EPSD',
	EPSDTPlaceholder: 'Tidak ada',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>mengandung penyesuaian</i>} other {}}',
	EarnReferralCredit: 'Dapatkan ${creditAmount}',
	Economist: 'Ekonom',
	Edit: 'Sunting',
	EditArrangements: 'Edit pengaturan',
	EditBillTo: 'Edit Tagihan ke',
	EditClient: 'Edit Klien',
	EditClientFileModalDescription: 'Edit akses ke file ini dengan memilih opsi di kotak centang "Dapat dilihat oleh"',
	EditClientFileModalTitle: 'Sunting berkas',
	EditClientNoteModalDescription:
		'Edit konten dalam catatan. Gunakan bagian "Dapat dilihat oleh" untuk mengubah siapa saja yang dapat melihat catatan tersebut.',
	EditClientNoteModalTitle: 'Edit catatan',
	EditConnectedAppButton: 'Sunting',
	EditConnections: 'Edit koneksi{account, select, null { } undefined { } other { untuk {account}}}',
	EditContactDetails: 'Edit detail kontak',
	EditContactFormIsClientLabel: 'Konversi ke klien',
	EditContactIsClientCheckboxWarning: 'Mengubah kontak menjadi klien tidak dapat dibatalkan',
	EditContactIsClientWanringModal:
		'Mengubah kontak ini menjadi Klien tidak dapat dibatalkan. Namun, semua hubungan akan tetap ada dan Anda sekarang akan memiliki akses ke catatan, berkas, dan dokumentasi lainnya.',
	EditContactRelationship: 'Edit hubungan kontak',
	EditDetails: 'Edit detailnya',
	EditFileModalTitle: 'Edit file untuk {name}',
	EditFolder: 'Mengedit folder',
	EditFolderDescription: 'Ubah nama folder menjadi...',
	EditInvoice: 'Edit faktur',
	EditInvoiceDetails: 'Edit rincian Faktur',
	EditLink: 'Edit Tautan',
	EditLocation: 'Edit lokasi',
	EditLocationFailure: 'Gagal memperbarui lokasi',
	EditLocationSucess: 'Berhasil memperbarui lokasi',
	EditPaymentDetails: 'Edit detail pembayaran',
	EditPaymentMethod: 'Edit metode pembayaran',
	EditPersonalDetails: 'Edit detail pribadi',
	EditPractitioner: 'Edit Praktisi',
	EditProvider: 'Edit Penyedia',
	EditProviderDetails: 'Edit detail penyedia',
	EditRecurrence: 'Edit Pengulangan',
	EditRecurringAppointmentModalTitle: 'Edit janji temu yang berulang',
	EditRecurringEventModalTitle: 'Edit rapat berulang',
	EditRecurringReminderModalTitle: 'Edit pengingat berulang',
	EditRecurringTaskModalTitle: 'Edit tugas yang berulang',
	EditRelationshipModalTitle: 'Edit hubungan',
	EditService: 'Edit layanan',
	EditServiceFailure: 'Gagal memperbarui layanan baru',
	EditServiceGroup: 'Edit koleksi',
	EditServiceGroupFailure: 'Gagal memperbarui koleksi',
	EditServiceGroupSuccess: 'Berhasil memperbarui koleksi',
	EditServiceSuccess: 'Berhasil memperbarui layanan baru',
	EditStaffDetails: 'Edit detail staf',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Tidak dapat memperbarui alamat email. Harap buat anggota tim baru dengan alamat email baru.',
	EditSubscriptionBilledQuantity: 'Kuantitas yang Ditagihkan',
	EditSubscriptionBilledQuantityValue: '{billedUsers} anggota tim',
	EditSubscriptionLimitedTimeOffer: 'Penawaran terbatas! Diskon 50% selama 6 bulan.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'Biaya langganan Anda akan disesuaikan ketika menambahkan atau menghapus anggota tim.',
	EditSubscriptionUpgradeContent:
		'Akun Anda akan segera diperbarui ke rencana dan periode penagihan baru. Setiap perubahan harga akan secara otomatis ditagihkan ke metode pembayaran yang disimpan atau dikreditkan ke akun Anda.',
	EditSubscriptionUpgradePlanTitle: 'Tingkatkan rencana berlangganan',
	EditSuperbillReceipt: 'Edit tagihan luar biasa',
	EditTags: 'Edit tag',
	EditTemplate: 'Edit Templat',
	EditTemplateFolderSuccessMessage: 'Folder template berhasil diedit',
	EditValue: 'Edit {value}',
	Edited: 'Diedit',
	Editor: 'Editor',
	EditorAlertDescription:
		'Format yang tidak didukung telah terdeteksi. Muat ulang aplikasi atau hubungi tim dukungan kami.',
	EditorAlertTitle: 'Kami mengalami masalah saat menampilkan konten ini',
	EditorPlaceholder:
		'Mulailah menulis, pilih templat atau tambahkan blok dasar untuk menangkap jawaban dari klien Anda.',
	EditorTemplatePlaceholder: 'Mulai menulis atau tambahkan komponen untuk membuat templat',
	EditorTemplateWithSlashCommandPlaceholder:
		'Mulai menulis atau tambahkan blok dasar untuk menangkap tanggapan klien. Gunakan perintah slash (/) untuk tindakan cepat.',
	EditorWithSlashCommandPlaceholder:
		'Mulailah menulis, pilih templat, atau tambahkan blok dasar untuk menangkap respons klien. Gunakan perintah garis miring ( / ) untuk tindakan cepat.',
	EffectiveStartEndDate: 'Tanggal mulai - akhir yang efektif',
	ElectricalEngineer: 'Insinyur Listrik',
	Electronic: 'Elektronik',
	ElectronicSignature: 'Tanda tangan elektronik',
	ElementarySchoolTeacher: 'Guru Sekolah Dasar',
	Eligibility: 'Kriteria Kelayakan',
	Email: 'E-mail',
	EmailAlreadyExists: 'Alamat email sudah ada',
	EmailAndSms: 'E-mail ',
	EmailBody: 'Isi email',
	EmailContainsIgnoredDescription:
		'Email berikut berisi email pengirim yang saat ini diabaikan. Apakah Anda ingin melanjutkan?',
	EmailInviteToPortalBody: `Hai {contactName},
Silakan ikuti tautan ini untuk masuk ke portal klien aman Anda dan dengan mudah mengelola perawatan Anda.

Hormat kami,

{providerName}`,
	EmailInviteToPortalSubject: 'Selamat datang di {providerName}',
	EmailInvoice: 'Faktur email',
	EmailInvoiceOverdueBody: `Hai {contactName}
Tagihan Anda {invoiceNumber} sudah jatuh tempo.
Silakan bayar tagihan Anda secara online menggunakan tautan di bawah ini.

Jika Anda memiliki pertanyaan, beri tahu kami.

Terima kasih,
{providerName}`,
	EmailInvoicePaidBody: `Hai {contactName}
Tagihan Anda {invoiceNumber} telah dibayar.
Untuk melihat dan mengunduh salinan tagihan Anda, ikuti tautan di bawah ini.

Jika Anda memiliki pertanyaan, silakan beri tahu kami.

Terima kasih,
{providerName}`,
	EmailInvoiceProcessingBody: `Hai {contactName}
Tagihan Anda {invoiceNumber} sudah siap.
Ikuti tautan di bawah ini untuk melihat tagihan Anda.

Jika Anda memiliki pertanyaan, beri tahu kami.

Terima kasih,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hai {contactName}
Tagihan Anda {invoiceNumber} telah siap, dan harus dibayarkan paling lambat {dueDate}.
Untuk melihat dan membayar tagihan Anda secara online, ikuti tautan di bawah ini.

Jika Anda memiliki pertanyaan, silakan beri tahu kami.

Terima kasih,
{providerName}`,
	EmailInvoiceVoidBody: `Hai {contactName}
Tagihan {invoiceNumber} Anda tidak berlaku.
Untuk melihat tagihan ini, ikuti tautan di bawah.

Jika Anda memiliki pertanyaan, beri tahu kami.

Terima kasih,
{providerName}`,
	EmailNotFound: 'Email tidak ditemukan',
	EmailNotVerifiedErrorCodeSnackbar: 'Tidak dapat melakukan tindakan. Anda perlu memverifikasi alamat email Anda.',
	EmailNotVerifiedTitle: 'Email Anda belum diverifikasi. Beberapa fitur akan dibatasi.',
	EmailSendClientIntakeBody: `Hai {contactName},
{providerName} ingin Anda untuk memberikan beberapa informasi dan meninjau dokumen penting. Silakan ikuti tautan di bawah ini untuk memulai.

Hormat kami,

{providerName}`,
	EmailSendClientIntakeSubject: 'Selamat datang di {providerName}',
	EmailSuperbillReceipt: 'Email tagihan luar biasa',
	EmailSuperbillReceiptBody: `Hai {contactName},
{providerName} telah mengirimkan Anda salinan pernyataan penerimaan pengembalian biaya Anda {date}.

Anda dapat mengunduh dan menyerahkannya langsung ke perusahaan asuransi Anda.`,
	EmailSuperbillReceiptFailure: 'Gagal mengirim tanda terima Superbill',
	EmailSuperbillReceiptSubject: '{providerName} telah mengirimkan pernyataan penerimaan penggantian',
	EmailSuperbillReceiptSuccess: 'Berhasil mengirim tanda terima Superbill',
	EmailVerificationDescription: 'Kami sedang <span>memverifikasi</span> akun Anda sekarang',
	EmailVerificationNotification: 'Email verifikasi telah dikirim ke {email}',
	EmailVerificationSuccess: 'Alamat email Anda telah berhasil diubah menjadi {email}',
	Emails: 'Surel',
	EmergencyContact: 'Kontak darurat',
	EmployeesIdentificationNumber: 'Nomor identifikasi karyawan',
	EmploymentStatus: 'Status Pekerjaan',
	EmptyAgendaViewDescription: 'Tidak ada acara untuk ditampilkan.<mark> Buat janji temu sekarang</mark>',
	EmptyBin: 'Tempat sampah kosong',
	EmptyBinConfirmationDescription:
		'Kosongkan tempat sampah akan menghapus semua **{total} percakapan** di Terhapus. Tindakan ini tidak dapat dibatalkan.',
	EmptyBinConfirmationTitle: 'Hapus percakapan selamanya',
	EmptyTrash: 'Sampah kosong',
	Enable: 'Memungkinkan',
	EnableCustomServiceAvailability: 'Aktifkan ketersediaan layanan',
	EnableCustomServiceAvailabilityDescription:
		'Misalnya janji temu awal hanya dapat dipesan setiap hari dari jam 9-10 pagi',
	EndCall: 'Akhiri panggilan',
	EndCallConfirmationForCreator:
		'Anda akan mengakhiri ini untuk semua orang karena Anda adalah pemrakarsa panggilan tersebut.',
	EndCallConfirmationHasActiveAttendees:
		'Anda akan mengakhiri panggilan tetapi klien sudah bergabung. Apakah Anda juga ingin bergabung?',
	EndCallForAll: 'Akhiri panggilan untuk semua orang',
	EndDate: 'Tanggal akhir',
	EndDictation: 'Akhiri dikte',
	EndOfLine: 'Tidak ada lagi janji temu',
	EndSession: 'Akhiri sesi',
	EndTranscription: 'Akhiri transkripsi',
	Ends: 'Berakhir',
	EndsOnDate: 'Berakhir pada {date}',
	Enrol: 'Daftar',
	EnrollmentRejectedSubject: 'Pendaftaran Anda dengan {payerName} telah ditolak',
	Enrolment: 'Asupan',
	Enrolments: 'Pendaftaran',
	EnrolmentsDescription: 'Lihat dan kelola pendaftaran penyedia dengan penjamin.',
	EnterAName: 'Masukkan nama...',
	EnterFieldLabel: 'Masukkan label bidang...',
	EnterPaymentDetailsDescription:
		'Biaya langganan Anda akan secara otomatis disesuaikan saat menambah atau menghapus pengguna.',
	EnterSectionName: 'Masukkan nama bagian...',
	EnterSubscriptionPaymentDetails: 'Masukkan rincian pembayaran',
	EnvironmentalScientist: 'Ilmuwan Lingkungan',
	Epidemiologist: 'Ahli epidemiologi',
	Eraser: 'Penghapus',
	Error: 'Kesalahan',
	ErrorBoundaryAction: 'Muat ulang halaman',
	ErrorBoundaryDescription: 'Harap segarkan halaman dan coba lagi.',
	ErrorBoundaryTitle: 'Ups! Ada yang salah',
	ErrorCallNotFound:
		'Panggilan tidak dapat ditemukan. Panggilan mungkin telah kedaluwarsa atau pembuatnya telah mengakhirinya.',
	ErrorCannotAccessCallUninvitedCode: 'Maaf, tampaknya Anda tidak diundang ke panggilan ini.',
	ErrorFileUploadCustomMaxFileCount: 'Tidak dapat mengunggah lebih dari {count} file sekaligus',
	ErrorFileUploadCustomMaxFileSize: 'Ukuran file tidak boleh melebihi {mb} MB',
	ErrorFileUploadInvalidFileType:
		'Jenis file tidak valid yang mungkin berisi virus potensial dan perangkat lunak berbahaya',
	ErrorFileUploadMaxFileCount: 'Tidak dapat mengunggah lebih dari 150 file sekaligus',
	ErrorFileUploadMaxFileSize: 'Ukuran file tidak boleh melebihi 100 MB',
	ErrorFileUploadNoFileSelected: 'Silakan pilih file yang akan diunggah',
	ErrorInvalidNationalProviderId: 'ID Penyedia Nasional yang diberikan tidak valid',
	ErrorInvalidPayerId: 'ID Pembayar yang diberikan tidak valid',
	ErrorInvalidTaxNumber: 'Nomor Pajak yang diberikan tidak valid',
	ErrorInviteExistingProviderStaffCode: 'Pengguna ini sudah ada di workspace.',
	ErrorInviteStaffExistingUser: 'Maaf, tampaknya pengguna yang Anda tambahkan sudah ada dalam sistem kami.',
	ErrorOnlySingleCallAllowed:
		'Anda hanya dapat melakukan satu panggilan dalam satu waktu. Harap akhiri panggilan saat ini untuk memulai panggilan baru.',
	ErrorPayerNotFound: 'Pembayar tidak ditemukan',
	ErrorProfilePhotoMaxFileSize: 'Pengunggahan gagal! Batas ukuran file tercapai - 5MB',
	ErrorRegisteredExistingUser: 'Maaf, tampaknya Anda sudah terdaftar.',
	ErrorUserSignInIncorrectCredentials: 'Email atau kata sandi tidak valid. Silakan coba lagi.',
	ErrorUserSigninGeneric: 'Maaf, terjadi kesalahan.',
	ErrorUserSigninUserNotConfirmed:
		'Maaf, Anda perlu mengonfirmasi akun Anda sebelum masuk. Periksa kotak masuk Anda untuk petunjuk.',
	Errors: 'Kesalahan',
	EssentialPlanInclusionFive: 'Impor templat',
	EssentialPlanInclusionFour: 'Penyimpanan 5 GB',
	EssentialPlanInclusionHeader: 'Segala Sesuatu Gratis  ',
	EssentialPlanInclusionOne: 'Pengingat Otomatis dan Kustom',
	EssentialPlanInclusionSix: 'Dukungan prioritas',
	EssentialPlanInclusionThree: 'Obrolan Video',
	EssentialPlanInclusionTwo: 'Sinkronisasi kalender 2 arah',
	EssentialSubscriptionPlanSubtitle: 'Sederhanakan praktik Anda dengan hal-hal penting',
	EssentialSubscriptionPlanTitle: 'Penting',
	Esthetician: 'Ahli kecantikan',
	Estheticians: 'Ahli kecantikan',
	EstimatedArrivalDate: 'Estimasi kedatangan {numberOfDaysFromNow}',
	Ethnicity: 'Suku Bangsa',
	Europe: 'Eropa',
	EventColor: 'Pertemuan warna',
	EventName: 'Nama acara',
	EventType: 'Jenis acara',
	Every: 'Setiap',
	Every2Weeks: 'Setiap 2 minggu',
	EveryoneInWorkspace: 'Semua orang di tempat kerja',
	ExercisePhysiologist: 'Ahli Fisiologi Olahraga',
	Existing: 'Ada',
	ExistingClients: 'Klien yang sudah ada',
	ExistingFolders: 'Folder yang sudah ada',
	ExpiredPromotionCode: 'Kode promosi telah kedaluwarsa',
	ExpiredReferralDescription: 'Rujukan telah kedaluwarsa',
	ExpiredVerificationLink: 'Tautan verifikasi kedaluwarsa',
	ExpiredVerificationLinkDescription: `Maaf, tautan verifikasi yang Anda klik telah kedaluwarsa. Hal ini dapat terjadi jika Anda menunggu lebih dari 24 jam untuk mengklik tautan tersebut atau jika Anda telah menggunakan tautan tersebut untuk memverifikasi alamat email Anda.

 Silakan minta tautan verifikasi baru untuk memverifikasi alamat email Anda.`,
	ExpiryDateRequired: 'Tanggal kedaluwarsa diperlukan',
	ExploreFeature: 'Apa yang ingin Anda jelajahi pertama kali?',
	ExploreOptions: 'Pilih satu atau lebih opsi untuk dijelajahi...',
	Export: 'Ekspor',
	ExportAppointments: 'Janji ekspor',
	ExportClaims: 'Ekspor klaim',
	ExportClaimsFilename: 'Klaim {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Berkas Anda tidak dapat diunduh karena terjadi kesalahan.',
	ExportClientsDownloadFailureSnackbarTitle: 'Unduhan gagal',
	ExportClientsFailureSnackbarDescription: 'Berkas Anda tidak dapat diekspor dengan sukses karena terjadi kesalahan.',
	ExportClientsFailureSnackbarTitle: 'Ekspor gagal',
	ExportClientsModalDescription: `Proses ekspor data ini dapat memakan waktu beberapa menit, tergantung pada jumlah data yang diekspor. Anda akan menerima pemberitahuan email dengan tautan setelah data siap diunduh.

 Apakah Anda ingin melanjutkan dengan mengekspor data klien?`,
	ExportClientsModalTitle: 'Ekspor data klien',
	ExportCms1500: 'Ekspor CMS1500',
	ExportContactFailedNotificationSubject: 'Ekspor data Anda telah gagal',
	ExportFailed: 'Ekspor gagal',
	ExportGuide: 'Panduan ekspor',
	ExportInvoiceFileName: 'Transaksi {fromDate}-{toDate}.csv',
	ExportPayments: 'Ekspor pembayaran',
	ExportPaymentsFilename: 'Pembayaran {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Dokumen Anda siap diunduh.',
	ExportPrintWaitMessage: 'Sedang mempersiapkan dokumen Anda. Harap tunggu...',
	ExportTextOnly: 'Ekspor teks saja',
	ExportTransactions: 'Transaksi ekspor',
	Exporting: 'Mengekspor',
	ExportingData: 'Mengekspor data',
	ExtendedFamilyMember: 'Anggota keluarga besar',
	External: 'Luar',
	ExternalEventInfoBanner: 'Pertemuan ini berasal dari kalender yang disinkronkan dan mungkin ada item yang hilang.',
	ExtraLarge: 'Ekstra besar',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Gagal',
	FailedToJoinTheMeeting: 'Gagal bergabung dalam rapat.',
	FallbackPageDescription: `Sepertinya halaman ini tidak ada, Anda mungkin perlu {refreshButton} halaman ini untuk mendapatkan perubahan terbaru.
Jika tidak, silakan hubungi dukungan Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'menyegarkan',
	FallbackPageTitle: 'Waduh...',
	FamilyPlanningService: 'Pelayanan keluarga berencana',
	FashionDesigner: 'Perancang Busana',
	FastTrackInvoicingAndBilling: 'Percepat penagihan dan faktur Anda',
	Father: 'Ayah',
	FatherInLaw: 'Ayah mertua',
	Favorite: 'Favorit',
	FeatureBannerCalendarTile1ActionLabel: 'Pemesanan online • 2 menit',
	FeatureBannerCalendarTile1Description: 'Cukup kirim email, teks, atau tambahkan ketersediaan ke situs web Anda',
	FeatureBannerCalendarTile1Title: 'Memungkinkan klien Anda untuk memesan secara online',
	FeatureBannerCalendarTile2ActionLabel: 'Otomatiskan pengingat • 2 menit',
	FeatureBannerCalendarTile2Description: 'Tingkatkan kehadiran klien dengan pengingat otomatis',
	FeatureBannerCalendarTile2Title: 'Mengurangi ketidakhadiran',
	FeatureBannerCalendarTile3Title: 'Penjadwalan dan Alur Kerja',
	FeatureBannerCalendarTitle: 'Memudahkan penjadwalan',
	FeatureBannerCallsTile1ActionLabel: 'Mulai panggilan telehealth',
	FeatureBannerCallsTile1Description: 'Akses klien hanya dengan satu tautan. Tanpa login, kata sandi, atau kerumitan',
	FeatureBannerCallsTile1Title: 'Mulai panggilan video dari mana saja',
	FeatureBannerCallsTile2ActionLabel: 'Hubungkan aplikasi • 4 menit',
	FeatureBannerCallsTile2Description: 'Hubungkan penyedia telehealth pilihan lainnya dengan lancar',
	FeatureBannerCallsTile2Title: 'Hubungkan aplikasi telehealth Anda',
	FeatureBannerCallsTile3Title: 'Panggilan',
	FeatureBannerCallsTitle: 'Terhubung dengan klien — Di mana saja, kapan saja',
	FeatureBannerClientsTile1ActionLabel: 'Impor sekarang • 2 menit',
	FeatureBannerClientsTile1Description: 'Mulailah dengan cepat dengan alat impor klien otomatis kami',
	FeatureBannerClientsTile1Title: 'Punya banyak klien?',
	FeatureBannerClientsTile2ActionLabel: 'Sesuaikan asupan • 2 menit',
	FeatureBannerClientsTile2Description: 'Hapus dokumen penerimaan dan tingkatkan pengalaman klien',
	FeatureBannerClientsTile2Title: 'Beralih ke nir-kertas',
	FeatureBannerClientsTile3Title: 'Portal Klien',
	FeatureBannerClientsTitle: 'Semuanya dimulai dengan klien Anda',
	FeatureBannerHeader: 'Oleh Masyarakat, untuk Masyarakat!',
	FeatureBannerInvoicesTile1ActionLabel: 'Otomatisasi pembayaran • 2 menit',
	FeatureBannerInvoicesTile1Description: 'Hindari percakapan canggung dengan pembayaran otomatis',
	FeatureBannerInvoicesTile1Title: 'Dapatkan bayaran 2x lebih cepat',
	FeatureBannerInvoicesTile2ActionLabel: 'Lacak arus kas • 2 menit',
	FeatureBannerInvoicesTile2Description: 'Kurangi faktur yang belum dibayar dan pantau pendapatan Anda',
	FeatureBannerInvoicesTile2Title: 'Lacak penghasilan Anda, tanpa kesulitan',
	FeatureBannerInvoicesTile3Title: 'Penagihan dan Pembayaran',
	FeatureBannerInvoicesTitle: 'Satu hal yang tidak perlu dikhawatirkan lagi',
	FeatureBannerSubheader:
		'Template Carepatron dibuat oleh tim dan komunitas kami. Cobalah sumber daya baru atau bagikan sumber daya Anda sendiri!',
	FeatureBannerTeamTile1ActionLabel: 'Undang sekarang',
	FeatureBannerTeamTile1Description: 'Undang anggota tim ke akun Anda dan buat kolaborasi menjadi mudah',
	FeatureBannerTeamTile1Title: 'Satukan tim Anda',
	FeatureBannerTeamTile2ActionLabel: 'Tetapkan ketersediaan • 2 menit',
	FeatureBannerTeamTile2Description: 'Kelola ketersediaan tim Anda untuk menghindari pemesanan ganda',
	FeatureBannerTeamTile2Title: 'Atur ketersediaan Anda',
	FeatureBannerTeamTile3ActionLabel: 'Tetapkan izin • 2 menit',
	FeatureBannerTeamTile3Description: 'Kontrol akses ke data sensitif dan alat untuk kepatuhan',
	FeatureBannerTeamTile3Title: 'Sesuaikan izin dan akses',
	FeatureBannerTeamTitle: 'Tidak ada hal hebat yang dapat dicapai sendirian',
	FeatureBannerTemplatesTile1ActionLabel: 'Jelajahi perpustakaan • 2 menit',
	FeatureBannerTemplatesTile1Description:
		'Pilih dari perpustakaan sumber daya yang dapat disesuaikan yang menakjubkan ',
	FeatureBannerTemplatesTile1Title: 'Kurangi beban kerja Anda',
	FeatureBannerTemplatesTile2ActionLabel: 'Kirim sekarang • 2 menit',
	FeatureBannerTemplatesTile2Description: 'Kirim template cantik ke klien untuk diselesaikan',
	FeatureBannerTemplatesTile2Title: 'Jadikan dokumentasi menyenangkan',
	FeatureBannerTemplatesTile3Title: 'Templat',
	FeatureBannerTemplatesTitle: 'Template untuk apa saja',
	FeatureLimitBannerDescription:
		'Upgrade sekarang untuk terus membuat dan mengelola {featureName} tanpa gangguan dan dapatkan hasil maksimal dari Carepatron!',
	FeatureLimitBannerTitle: 'Kamu sudah {percentage}% menuju batas {featureName} kamu',
	FeatureRequiresUpgrade: 'Fitur ini membutuhkan peningkatan',
	Fee: 'Biaya',
	Female: 'Perempuan',
	FieldLabelTooltip: '{isHidden, select, true {Tampilkan} other {Sembunyikan}} label bidang',
	FieldName: 'Nama bidang',
	FieldOptionsFirstPart: 'Kata pertama',
	FieldOptionsMiddlePart: 'Kata tengah',
	FieldOptionsSecondPart: 'Kata terakhir',
	FieldOptionsWholeField: 'Seluruh lapangan',
	FieldType: 'Jenis bidang',
	Fields: 'Lapangan',
	File: 'Mengajukan',
	FileDownloaded: '<strong>{fileName}</strong> telah diunduh',
	FileInvalidType: 'Berkas tidak didukung.',
	FileNotFound: 'Berkas tidak ditemukan',
	FileNotFoundDescription: 'File yang Anda cari tidak tersedia atau telah dihapus',
	FileTags: 'Tag berkas',
	FileTagsHelper: 'Tag akan diterapkan ke semua file',
	FileTooLarge: 'Berkas terlalu besar.',
	FileTooSmall: 'Berkas terlalu kecil.',
	FileUploadComplete: 'Menyelesaikan',
	FileUploadFailed: 'Gagal',
	FileUploadInProgress: 'Memuat',
	FileUploadedNotificationSubject: '{actorProfileName} telah mengunggah sebuah file',
	Files: 'Berkas',
	FillOut: 'Mengisi',
	Filter: 'Menyaring',
	FilterBy: 'Filter berdasarkan',
	FilterByAmount: 'Filter berdasarkan jumlah',
	FilterByClient: 'Filter berdasarkan klien',
	FilterByLocation: 'Filter berdasarkan lokasi',
	FilterByService: 'Filter berdasarkan layanan',
	FilterByStatus: 'Filter berdasarkan status',
	FilterByTags: 'Filter berdasarkan tag',
	FilterByTeam: 'Filter berdasarkan tim',
	Filters: 'Filternya',
	FiltersAppliedToView: 'Filter diterapkan ke tampilan',
	FinalAppointment: 'Penunjukan Terakhir',
	FinalizeImport: 'Finalisasi impor',
	FinancialAnalyst: 'Analis Keuangan',
	Finish: 'Menyelesaikan',
	Firefighter: 'Petugas Pemadam Kebakaran',
	FirstName: 'Nama depan',
	FirstNameLastInitial: 'Nama depan, inisial belakang',
	FirstPerson: 'Orang pertama',
	FolderName: 'Nama folder',
	Folders: 'folder',
	FontFamily: 'Keluarga font',
	ForClients: 'Untuk klien',
	ForClientsDetails: 'Saya menerima perawatan atau layanan terkait kesehatan',
	ForPractitioners: 'Untuk praktisi',
	ForPractitionersDetails: 'Kelola dan kembangkan praktik Anda',
	ForgotPasswordConfirmAccessCode: 'Kode konfirmasi',
	ForgotPasswordConfirmNewPassword: 'Kata sandi baru',
	ForgotPasswordConfirmPageDescription:
		'Silakan masukkan alamat email Anda, kata sandi baru, dan kode konfirmasi yang baru saja kami kirimkan kepada Anda.',
	ForgotPasswordConfirmPageTitle: 'Setel ulang kata sandi',
	ForgotPasswordPageButton: 'Kirim tautan pengaturan ulang',
	ForgotPasswordPageDescription:
		'Masukkan email Anda dan kami akan mengirimkan tautan untuk mengatur ulang kata sandi Anda.',
	ForgotPasswordPageTitle: 'Lupa kata sandi',
	ForgotPasswordSuccessPageDescription: 'Periksa kotak masuk Anda untuk tautan pengaturan ulang.',
	ForgotPasswordSuccessPageTitle: 'Tautan reset terkirim!',
	Form: 'Form',
	FormAnswersSentToEmailNotification: 'Kami telah mengirimkan salinan jawaban Anda ke',
	FormBlocks: 'Blok bentuk',
	FormFieldAddOption: 'Tambahkan opsi',
	FormFieldAddOtherOption: 'Tambahkan "lainnya"',
	FormFieldOptionPlaceholder: 'Opsi {index}',
	FormStructures: 'Struktur bentuk',
	Format: 'Format',
	FormatLinkButtonColor: 'Warna Tombol',
	Forms: 'Formulir',
	FormsAndAgreementsValidationMessage:
		'Semua formulir dan perjanjian harus dilengkapi untuk melanjutkan proses penerimaan.',
	FormsCategoryDescription: 'Untuk mengumpulkan dan mengorganisir detail pasien',
	Frankfurt: 'Frankfurt',
	Free: 'Bebas',
	FreePlanInclusionFive: 'Penagihan otomatis ',
	FreePlanInclusionFour: 'Portal klien',
	FreePlanInclusionHeader: 'Memulai dengan',
	FreePlanInclusionOne: 'Klien tak terbatas',
	FreePlanInclusionSix: 'Dukungan langsung',
	FreePlanInclusionThree: 'Penyimpanan 1 GB',
	FreePlanInclusionTwo: 'Telekesehatan',
	FreeSubscriptionPlanSubtitle: 'Gratis untuk semua orang',
	FreeSubscriptionPlanTitle: 'Bebas',
	Friday: 'Jumat',
	From: 'Dari',
	FullName: 'Nama lengkap',
	FunctionalMedicineOrNaturopath: 'Pengobatan Fungsional atau Naturopath',
	FuturePaymentsAuthoriseProvider: 'Izinkan {provider} untuk menggunakan pembayaran yang disimpan di masa mendatang',
	FuturePaymentsSavePaymentMethod: 'Simpan {paymentMethod} untuk pembayaran di masa mendatang',
	GST: 'PPN',
	Gender: 'Jenis kelamin',
	GeneralAvailability: 'Ketersediaan umum',
	GeneralAvailabilityDescription:
		'Atur kapan Anda tersedia secara rutin. Klien hanya dapat memesan layanan Anda selama jam-jam yang tersedia.',
	GeneralAvailabilityDescription2:
		'Buat jadwal berdasarkan ketersediaan dan penawaran layanan yang diinginkan pada waktu tertentu untuk menentukan ketersediaan pemesanan online Anda.',
	GeneralAvailabilityInfo: 'Jam operasional Anda akan menentukan ketersediaan pemesanan online Anda',
	GeneralAvailabilityInfo2:
		'Layanan yang menawarkan acara kelompok harus menggunakan jadwal baru untuk mengurangi jam yang tersedia untuk dipesan oleh klien secara daring.',
	GeneralHoursPlural: '{count} {count, plural, one {jam} other {jam}}',
	GeneralPractitioner: 'Dokter Umum',
	GeneralPractitioners: 'Dokter umum',
	GeneralServiceAvailabilityInfo: 'Jadwal ini akan menggantikan perilaku anggota tim yang ditugaskan',
	Generate: 'Menghasilkan',
	GenerateBillingItemsBannerContent: 'Item penagihan tidak secara otomatis dibuat untuk janji temu berulang.',
	GenerateItems: 'Hasilkan item',
	GenerateNote: 'Hasilkan catatan',
	GenerateNoteConfirmationModalDescription:
		'Apa yang ingin Anda lakukan? Membuat catatan baru, menambahkan catatan yang sudah ada, atau mengganti isinya?',
	GenerateNoteFor: 'Hasilkan catatan untuk',
	GeneratingContent: 'Menghasilkan konten...',
	GeneratingNote: 'Membuat catatan Anda...',
	GeneratingTranscript: 'Membuat transkrip',
	GeneratingTranscriptDescription: 'Proses ini mungkin memerlukan waktu beberapa menit',
	GeneratingYourTranscript: 'Membuat transkrip Anda',
	GenericErrorDescription: '{module} tidak dapat dimuat. Silakan coba lagi nanti.',
	GenericErrorTitle: 'Terjadi kesalahan tak terduga',
	GenericFailureSnackbar: 'Maaf, terjadi sesuatu yang tidak diharapkan. Harap segarkan halaman dan coba lagi.',
	GenericSavedSuccessSnackbar: 'Berhasil! Perubahan telah disimpan',
	GeneticCounselor: 'Konselor Genetika',
	Gerontologist: 'Dokter Spesialis Gerontologi',
	Get50PercentOff: 'Dapatkan diskon 50%!',
	GetHelp: 'Dapatkan bantuan',
	GetStarted: 'Memulai',
	GettingStartedAppointmentTypes: 'Buat jenis janji temu',
	GettingStartedAppointmentTypesDescription:
		'Sederhanakan penjadwalan dan penagihan Anda dengan menyesuaikan layanan, harga, dan kode penagihan Anda',
	GettingStartedAppointmentTypesTitle: 'Jadwal ',
	GettingStartedClients: 'Tambahkan klien Anda',
	GettingStartedClientsDescription:
		'Persiapkan diri Anda dan jalankan dengan klien untuk janji temu, catatan, dan pembayaran di masa mendatang',
	GettingStartedClientsTitle: 'Semuanya dimulai dengan klien',
	GettingStartedCreateClient: 'Buat klien',
	GettingStartedImportClients: 'Impor klien',
	GettingStartedInvoices: 'Buat faktur seperti seorang profesional',
	GettingStartedInvoicesDescription: `Mudah untuk membuat faktur profesional.
 Tambahkan logo, lokasi, dan ketentuan pembayaran Anda`,
	GettingStartedInvoicesTitle: 'Tunjukkan sisi terbaikmu',
	GettingStartedMobileApp: 'Dapatkan aplikasi selulernya',
	GettingStartedMobileAppDescription:
		'Anda dapat mengunduh Carepatron di perangkat iOS, Android, atau desktop Anda untuk akses mudah saat bepergian',
	GettingStartedMobileAppTitle: 'Bekerja dari mana saja',
	GettingStartedNavItem: 'Memulai',
	GettingStartedPageTitle: 'Memulai Carepatron',
	GettingStartedPayments: 'Terima pembayaran online',
	GettingStartedPaymentsDescription: `Dapatkan pembayaran lebih cepat dengan memungkinkan klien Anda membayar secara online.
 Lihat semua faktur dan pembayaran Anda di satu tempat`,
	GettingStartedPaymentsTitle: 'Jadikan pembayaran mudah',
	GettingStartedSaveBranding: 'Simpan merek',
	GettingStartedSyncCalendars: 'Sinkronkan kalender lainnya',
	GettingStartedSyncCalendarsDescription:
		'Carepatron memeriksa kalender Anda untuk mengetahui adanya konflik, sehingga janji temu hanya dijadwalkan saat Anda tersedia',
	GettingStartedSyncCalendarsTitle: 'Selalu mengikuti perkembangan terkini',
	GettingStartedVideo: 'Tonton video pengantar',
	GettingStartedVideoDescription:
		'Ruang kerja perawatan kesehatan all-in-one pertama untuk tim kecil dan klien mereka',
	GettingStartedVideoTitle: 'Selamat datang di Carepatron',
	GetttingStartedGetMobileDownload: 'Unduh aplikasinya',
	GetttingStartedGetMobileNoDownload:
		'Tidak kompatibel dengan browser ini. Jika Anda menggunakan iPhone atau iPad, silakan buka halaman ini di Safari. Jika tidak, coba buka di Chrome.',
	Glossary: 'Glosarium',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail hanya mengizinkan 500 pesan untuk dikirim dari akun Anda dalam sehari. Beberapa pesan mungkin gagal. Apakah Anda ingin melanjutkan?',
	GoToAppointment: 'Pergi ke janji temu',
	GoToApps: 'Buka aplikasi',
	GoToAvailability: 'Buka ketersediaan',
	GoToClientList: 'Buka daftar klien',
	GoToClientRecord: 'Buka catatan klien',
	GoToClientSettings: 'Buka pengaturan klien sekarang',
	GoToInvoiceTemplates: 'Buka template faktur',
	GoToNotificationSettings: 'Buka pengaturan notifikasi',
	GoToPaymentSettings: 'Buka pengaturan pembayaran',
	Google: 'Google',
	GoogleCalendar: 'Kalender Google',
	GoogleColor: 'Kalender Google berwarna',
	GoogleMeet: 'Google Bertemu',
	GoogleTagManagerContainerId: 'ID Kontainer Pengelola Tag Google',
	GotIt: 'Mengerti!',
	Goto: 'Pergi ke',
	Granddaughter: 'Cucu perempuan',
	Grandfather: 'Kakek',
	Grandmother: 'Nenek',
	Grandparent: 'Eyang',
	Grandson: 'Cucu',
	GrantPortalAccess: 'Berikan akses portal',
	GraphicDesigner: 'Desainer Grafis',
	Grid: 'Jaringan',
	GridView: 'Tampilan kisi',
	Group: 'Kelompok',
	GroupBy: 'Kelompokkan berdasarkan',
	GroupEvent: 'Acara kelompok',
	GroupEventHelper: 'Tetapkan batas peserta untuk layanan tersebut',
	GroupFilterLabel: 'Semua {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'ID Grup',
	GroupInputFieldsFormPrimaryText: 'Bidang masukan grup',
	GroupInputFieldsFormSecondaryText: 'Pilih atau tambahkan bidang khusus',
	GuideTo: 'Panduan untuk {value}',
	GuideToImproveVideoQuality: 'Panduan untuk meningkatkan kualitas video',
	GuideToManagingPayers: 'Mengelola pembayar',
	GuideToSubscriptionsBilling: 'Panduan penagihan langganan',
	GuideToTroubleshooting: 'Panduan untuk pemecahan masalah',
	Guidelines: 'Pedoman',
	GuidelinesCategoryDescription: 'Untuk memandu pengambilan keputusan klinis',
	HST: 'HST',
	HairStylist: 'Penata Rambut',
	HaveBeenWaiting: 'Kamu sudah menunggu lama',
	HeHim: 'Dia/Dia',
	HeaderAccountSettings: 'Profil',
	HeaderCalendar: 'Kalender',
	HeaderCalls: 'Panggilan',
	HeaderClientAppAccountSettings: 'Pengaturan Akun',
	HeaderClientAppCalls: 'Panggilan',
	HeaderClientAppMyDocumentation: 'Dokumentasi',
	HeaderClientAppMyRelationships: 'Hubungan saya',
	HeaderClients: 'Klien',
	HeaderHelp: 'Membantu',
	HeaderMoreOptions: 'Lebih banyak pilihan',
	HeaderStaff: 'Staf',
	HealthCoach: 'Pelatih Kesehatan',
	HealthCoaches: 'Pelatih Kesehatan',
	HealthEducator: 'Pendidik Kesehatan',
	HealthInformationTechnician: 'Teknisi Informasi Kesehatan',
	HealthPolicyExpert: 'Pakar Kebijakan Kesehatan',
	HealthServicesAdministrator: 'Administrator Layanan Kesehatan',
	HelpArticles: 'Artikel bantuan',
	HiddenColumns: 'Kolom tersembunyi',
	HiddenFields: 'Lapangan Tersembunyi',
	HiddenSections: 'Bagian tersembunyi',
	HiddenSectionsAndFields: 'Bagian/bidang tersembunyi',
	HideColumn: 'Sembunyikan kolom',
	HideColumnButton: 'Sembunyikan tombol kolom {value}',
	HideDetails: 'Sembunyikan detail',
	HideField: 'Sembunyikan bidang',
	HideFullAddress: 'Bersembunyi',
	HideMenu: 'Sembunyikan menu',
	HideMergeSummarySidebar: 'Sembunyikan ringkasan penggabungan',
	HideSection: 'Sembunyikan bagian',
	HideYourView: 'Sembunyikan tampilan Anda',
	Highlight: 'Sorot warna',
	Highlighter: 'stabilo',
	History: 'Sejarah',
	HistoryItemFooter: '{actors, select, undefined {{date} pada {time}} other {Oleh {actors} • {date} pada {time}}}',
	HistorySidePanelEmptyState: 'Tidak ditemukan riwayat',
	HistoryTitle: 'Log Aktivitas',
	HolisticHealthPractitioner: 'Praktisi Kesehatan Holistik',
	HomeCaregiver: 'Pengasuh Rumah',
	HomeHealthAide: 'Asisten Kesehatan Rumah',
	HomelessShelter: 'Tempat penampungan tunawisma',
	HourAbbreviation: '{count} {count, plural, one {jam} other {jam}}',
	Hourly: 'Per Jam',
	HoursPlural: '{age, plural, one {# jam} other {# jam}}',
	HowCanWeImprove: 'Bagaimana kita dapat meningkatkan ini?',
	HowCanWeImproveResponse: 'Bagaimana kita dapat meningkatkan tanggapan ini?',
	HowDidWeDo: 'Bagaimana menurut Anda?',
	HowDoesReferralWork: 'Panduan untuk program rujukan',
	HowToUseAiSummarise: 'Cara menggunakan AI Summarize',
	HumanResourcesManager: 'Manajer Sumber Daya Manusia',
	Husband: 'Suami',
	Hypnotherapist: 'Hipnoterapis',
	IVA: 'IVA',
	IgnoreNotification: 'Abaikan pemberitahuan',
	IgnoreOnce: 'Abaikan sekali',
	IgnoreSender: 'Abaikan pengirim',
	IgnoreSenderDescription: `Percakapan selanjutnya dari pengirim ini akan otomatis dipindahkan ke 'Lainnya'. Apakah Anda yakin ingin mengabaikan pengirim ini?`,
	IgnoreSenders: 'Abaikan pengirim',
	IgnoreSendersSuccess: 'Alamat email yang diabaikan <mark>{addresses}</mark>',
	Ignored: 'Diabaikan',
	Image: 'Gambar',
	Import: 'Impor',
	ImportActivity: 'Impor aktivitas',
	ImportClientSuccessSnackbarDescription: 'File Anda telah berhasil diimpor',
	ImportClientSuccessSnackbarTitle: 'Impor berhasil!',
	ImportClients: 'Impor klien',
	ImportClientsFailureSnackbarDescription: 'Berkas Anda tidak dapat diimpor dengan sukses karena terjadi kesalahan.',
	ImportClientsFailureSnackbarTitle: 'Impor gagal!',
	ImportClientsGuide: 'Panduan Mengimpor Klien',
	ImportClientsInProgressSnackbarDescription: 'Proses ini hanya memerlukan waktu satu menit untuk menyelesaikannya.',
	ImportClientsInProgressSnackbarTitle: 'Mengimpor {fileName}',
	ImportClientsModalDescription:
		'Pilih dari mana data Anda berasal – apakah itu file di perangkat Anda, layanan pihak ketiga, atau platform perangkat lunak lain.',
	ImportClientsModalFileUploadHelperText: 'Mendukung {fileTypes}. Batas ukuran {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Panduan untuk mengimpor data klien',
	ImportClientsModalStep1Label: 'Pilih sumber data',
	ImportClientsModalStep2Label: 'Unggah berkas',
	ImportClientsModalStep3Label: 'Tinjau bidang',
	ImportClientsModalTitle: 'Mengimpor data klien Anda',
	ImportClientsPreviewClientsReadyForImport: '{count} {count, plural, one {klien} other {klien}} siap untuk diimpor',
	ImportContactFailedNotificationSubject: 'Impor data Anda telah gagal',
	ImportDataSourceSelectorLabel: 'Impor sumber data dari',
	ImportDataSourceSelectorPlaceholder: 'Cari atau pilih sumber data impor',
	ImportExportButton: 'Impor/Ekspor',
	ImportFailed: 'Impor gagal',
	ImportFromAnotherPlatformTileDescription: 'Unduh ekspor file klien Anda dan unggah di sini.',
	ImportFromAnotherPlatformTileLabel: 'Impor dari platform lain',
	ImportGuide: 'Panduan impor',
	ImportInProgress: 'Impor sedang berlangsung',
	ImportProcessing: 'Impor sedang diproses...',
	ImportSpreadsheetDescription:
		'Anda dapat mengimpor daftar klien yang ada ke Carepatron dengan mengunggah file spreadsheet dengan data tabular, seperti .CSV, .XLS, atau .XLSX',
	ImportSpreadsheetTitle: 'Impor file spreadsheet Anda',
	ImportTemplates: 'Impor template',
	Importing: 'Pengimporan',
	ImportingCalendarProductEvents: 'Mengimpor peristiwa {product}',
	ImportingData: 'Mengimpor data',
	ImportingSpreadsheetDescription: 'Ini hanya akan memakan waktu satu menit untuk menyelesaikannya',
	ImportingSpreadsheetTitle: 'Mengimpor spreadsheet Anda',
	ImportsInProgress: 'Impor sedang berlangsung',
	InPersonMeeting: 'Pertemuan tatap muka',
	InProgress: 'Sedang berlangsung',
	InTransit: 'Dalam perjalanan',
	InTransitTooltip:
		'Saldo Dalam Perjalanan mencakup semua pembayaran faktur yang dibayarkan dari Stripe ke rekening bank Anda. Dana ini biasanya memerlukan waktu 3-5 hari untuk dicairkan.',
	Inactive: 'Tidak aktif',
	InboundOrOutboundCalls: 'Panggilan masuk atau keluar',
	Inbox: 'Kotak Masuk',
	InboxAccessRestricted: 'Akses dibatasi. Silakan hubungi pemilik kotak masuk untuk mendapatkan izin.',
	InboxAccountAlreadyConnected: 'Saluran yang Anda coba sambungkan sudah terhubung ke Carepatron',
	InboxAddAttachments: 'Tambahkan lampiran',
	InboxAreYouSureDeleteMessage: 'Apakah Anda yakin ingin menghapus pesan ini?',
	InboxBulkCloseSuccess: '{count, plural, one {Berhasil menutup # percakapan} other {Berhasil menutup # percakapan}}',
	InboxBulkComposeModalTitle: 'Tulis pesan massal',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Berhasil menghapus # percakapan} other {Berhasil menghapus # percakapan}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Berhasil menandai # percakapan sebagai terbaca} other {Berhasil menandai # percakapan sebagai terbaca}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Berhasil membuka kembali # percakapan} other {Berhasil membuka kembali # percakapan}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Berhasil menandai # percakapan sebagai belum dibaca} other {Berhasil menandai # percakapan sebagai belum dibaca}}',
	InboxChatCreateGroup: 'Buat grup',
	InboxChatDeleteGroupModalDescription:
		'Apakah Anda yakin ingin menghapus grup ini? Semua pesan dan lampiran akan dihapus.',
	InboxChatDeleteGroupModalTitle: 'Hapus grup',
	InboxChatDiscardDraft: 'Buang draf',
	InboxChatDragDropText: 'Letakkan berkas di sini untuk mengunggah',
	InboxChatGroupConversation: 'Percakapan grup',
	InboxChatGroupCreateModalDescription:
		'Mulai grup baru untuk Berkirim Pesan dan berkolaborasi dengan tim, klien, atau komunitas Anda.',
	InboxChatGroupCreateModalTitle: 'Buat grup',
	InboxChatGroupMembers: 'Anggota kelompok',
	InboxChatGroupModalGroupNameFieldLabel: 'Nama Grup',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'Misalnya dukungan pelanggan, admin',
	InboxChatGroupModalGroupNameFieldRequired: '<p>Kolom ini wajib diisi</p>',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Minimal satu anggota diperlukan',
	InboxChatGroupModalMembersFieldLabel: 'Pilih anggota grup',
	InboxChatGroupModalMembersFieldPlaceholder: 'Pilih anggota',
	InboxChatGroupUpdateModalTitle: 'Kelola grup',
	InboxChatLeaveGroup: 'Keluar grup',
	InboxChatLeaveGroupModalDescription:
		'Apakah Anda yakin ingin meninggalkan grup ini? Anda tidak akan lagi menerima pesan atau pembaruan.',
	InboxChatLeaveGroupModalTitle: 'Keluar grup',
	InboxChatLeftGroupMessage: 'Pesan grup kiri',
	InboxChatManageGroup: 'Kelola grup',
	InboxChatSearchParticipants: 'Pilih penerima',
	InboxCloseConversationSuccess: 'Berhasil menutup percakapan',
	InboxCompose: 'Menyusun',
	InboxComposeBulk: 'Pesan massal',
	InboxComposeCarepatronChat: 'Kurir',
	InboxComposeChat: 'Buat Chat',
	InboxComposeDisabledNoConnection: 'Hubungkan akun email untuk mengirim pesan',
	InboxComposeDisabledNoPermissionTooltip: 'Anda tidak memiliki izin untuk mengirim pesan dari kotak masuk ini',
	InboxComposeEmail: 'Tulis email',
	InboxComposeMessageFrom: 'Dari',
	InboxComposeMessageRecipientBcc: 'Bcc',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'Ke',
	InboxComposeMessageSubject: 'Subjek:',
	InboxConnectAccountButton: 'Hubungkan email Anda',
	InboxConnectedDescription: 'Kotak masuk Anda tidak memiliki komunikasi',
	InboxConnectedHeading: 'Percakapan Anda akan muncul di sini segera setelah Anda mulai bertukar komunikasi',
	InboxConnectedHeadingClientView: 'Sederhanakan komunikasi klien Anda',
	InboxCreateFirstInboxButton: 'Buat kotak masuk pertama Anda',
	InboxCreationSuccess: 'Kotak masuk berhasil dibuat',
	InboxDeleteAttachment: 'Hapus lampiran',
	InboxDeleteConversationSuccess: 'Berhasil menghapus percakapan',
	InboxDeleteMessage: 'Hapus pesan?',
	InboxDirectMessage: 'Pesan langsung',
	InboxEditDraft: 'Edit draf',
	InboxEmailComposeReplyEmail: 'Tulis balasan',
	InboxEmailDraft: 'Draf',
	InboxEmailNotFound: 'Email tidak ditemukan',
	InboxEmailSubjectFieldInformation: 'Mengubah baris subjek akan membuat email berulir baru.',
	InboxEmptyArchiveDescription: 'Tidak ditemukan percakapan yang diarsipkan',
	InboxEmptyBinDescription: 'Tidak ditemukan percakapan yang dihapus',
	InboxEmptyBinHeading: 'Semua aman, tidak ada yang perlu dilihat di sini',
	InboxEmptyBinSuccess: 'Percakapan berhasil dihapus',
	InboxEmptyCongratsHeading: 'Kerja bagus! Duduk santai dan rileks sampai percakapan berikutnya',
	InboxEmptyDraftDescription: 'Tidak ditemukan draf percakapan',
	InboxEmptyDraftHeading: 'Semua aman, tidak ada yang perlu dilihat di sini',
	InboxEmptyOtherDescription: 'Tidak ada percakapan lain yang ditemukan',
	InboxEmptyScheduledHeading: 'Semua jelas, tidak ada percakapan yang dijadwalkan untuk dikirim',
	InboxEmptySentDescription: 'Tidak ditemukan percakapan terkirim',
	InboxForward: 'Maju',
	InboxGroupClientsLabel: 'Semua klien',
	InboxGroupClientsOverviewLabel: 'Klien',
	InboxGroupClientsSelectedItemPrefix: 'Klien',
	InboxGroupStaffsLabel: 'Semua tim',
	InboxGroupStaffsOverviewLabel: 'Tim',
	InboxGroupStaffsSelectedItemPrefix: 'Tim',
	InboxGroupStatusLabel: 'Semua Status',
	InboxGroupStatusOverviewLabel: 'Kirim ke status',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Semua tag',
	InboxGroupTagsOverviewLabel: 'Kirim ke tag',
	InboxGroupTagsSelectedItemPrefix: 'Menandai',
	InboxHideQuotedText: 'Sembunyikan teks kutipan',
	InboxIgnoreConversationSuccess: 'Berhasil mengabaikan percakapan',
	InboxMessageAllLabelRecipientsCount: 'Semua Penerima {label} ({count})',
	InboxMessageBodyPlaceholder: 'Tambahkan pesan Anda',
	InboxMessageDeleted: 'Pesan dihapus',
	InboxMessageMarkedAsRead: 'Pesan ditandai sebagai telah dibaca',
	InboxMessageMarkedAsUnread: 'Pesan ditandai sebagai belum dibaca',
	InboxMessageSentViaChat: '**Dikirim melalui obrolan**  • {time} oleh {name}',
	InboxMessageShowMoreRecipients: '+{count} lagi',
	InboxMessageWasDeleted: 'Pesan ini telah dihapus',
	InboxNoConnectionDescription: 'Hubungkan akun email Anda atau buat kotak masuk dengan beberapa email',
	InboxNoConnectionHeading: 'Integrasikan komunikasi klien Anda',
	InboxNoDirectMessage: 'Tidak ada pesan baru',
	InboxRecentConversations: 'Terbaru',
	InboxReopenConversationSuccess: 'Berhasil membuka kembali percakapan',
	InboxReply: 'Membalas',
	InboxReplyAll: 'Balas semua',
	InboxRestoreConversationSuccess: 'Berhasil memulihkan percakapan',
	InboxScheduleSendCancelSendSuccess: 'Pengiriman terjadwal dibatalkan dan pesan dikembalikan ke draf',
	InboxScheduleSendMessageSuccessDescription: 'Kirim terjadwal untuk {date}',
	InboxScheduleSendMessageSuccessTitle: 'Penjadwalan pengiriman',
	InboxSearchForConversations: 'Cari "{query}"',
	InboxSendMessageSuccess: 'Berhasil mengirim percakapan',
	InboxSettings: 'Pengaturan kotak masuk',
	InboxSettingsAppsDesc:
		'Kelola aplikasi yang terhubung untuk kotak masuk bersama ini: tambahkan atau hapus koneksi sesuai kebutuhan.',
	InboxSettingsAppsNewConnectedApp: 'Aplikasi terhubung baru',
	InboxSettingsAppsTitle: 'Aplikasi yang terhubung',
	InboxSettingsDeleteAccountFailed: 'Gagal menghapus akun kotak masuk',
	InboxSettingsDeleteAccountSuccess: 'Berhasil menghapus akun kotak masuk',
	InboxSettingsDeleteAccountWarning:
		'Menghapus {email} akan memutuskan koneksi dari kotak masuk {inboxName} dan akan menghentikan sinkronisasi pesan.',
	InboxSettingsDeleteInboxFailed: 'Gagal menghapus kotak masuk',
	InboxSettingsDeleteInboxSuccess: 'Berhasil menghapus kotak masuk',
	InboxSettingsDeleteInboxWarning:
		'Menghapus {inboxName} akan memutuskan semua saluran yang terhubung dan menghapus semua pesan yang terkait dengan kotak masuk ini. 		Tindakan ini bersifat permanen dan tidak dapat dibatalkan.',
	InboxSettingsDetailsDesc: 'Kotak masuk komunikasi bagi tim Anda untuk mengelola pesan klien secara efisien.',
	InboxSettingsDetailsTitle: 'Detail kotak masuk',
	InboxSettingsEmailSignatureLabel: 'Tanda tangan email default',
	InboxSettingsReplyFormatDesc:
		'Siapkan alamat balasan dan tanda tangan email default Anda agar ditampilkan secara konsisten, terlepas dari siapa yang mengirim email tersebut.',
	InboxSettingsReplyFormatTitle: 'Format balasan',
	InboxSettingsSendFromLabel: 'Tetapkan balasan default dari ',
	InboxSettingsStaffDesc: 'Kelola akses anggota tim ke kotak masuk bersama ini untuk kolaborasi yang lancar.',
	InboxSettingsStaffTitle: 'Tetapkan anggota tim',
	InboxSettingsUpdateInboxDetailsFailed: 'Gagal memperbarui detail kotak masuk',
	InboxSettingsUpdateInboxDetailsSuccess: 'Berhasil memperbarui detail kotak masuk',
	InboxSettingsUpdateInboxStaffsFailed: 'Gagal memperbarui anggota tim kotak masuk',
	InboxSettingsUpdateInboxStaffsSuccess: 'Berhasil memperbarui anggota tim kotak masuk',
	InboxSettingsUpdateReplyFormatFailed: 'Gagal memperbarui format balasan',
	InboxSettingsUpdateReplyFormatSuccess: 'Berhasil memperbarui format balasan',
	InboxShowQuotedText: 'Tampilkan teks yang dikutip',
	InboxStaffRoleAdminDescription: 'Melihat, membalas, dan mengelola kotak masuk',
	InboxStaffRoleResponderDescription: 'Lihat dan balas',
	InboxStaffRoleViewerDescription: 'Lihat saja',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Lanjutkan mengedit',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Ya, beralih ke pengiriman massal',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Anda telah memilih lebih dari {count} penerima. Apakah Anda ingin mengirimkannya sebagai email massal?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Peringatan',
	InboxSwitchToOtherInbox: 'Beralih ke kotak masuk lain',
	InboxUndoSendMessageSuccess: 'Mengirim yang belum selesai',
	IncludeLineItems: 'Sertakan item baris',
	IncludeSalesTax: 'Kena Pajak',
	IncludesAiSmartPrompt: 'Termasuk perintah cerdas AI',
	Incomplete: 'Tidak lengkap',
	IncreaseIndent: 'Meningkatkan indentasi',
	IndianHealthServiceFreeStandingFacility: 'Fasilitas mandiri Layanan Kesehatan India',
	IndianHealthServiceProviderFacility: 'Fasilitas berbasis penyedia Layanan Kesehatan India',
	Information: 'Informasi',
	InitialAssessment: 'Penilaian Awal',
	InitialSignupPageClientFamilyTitle: 'Klien atau Anggota Keluarga',
	InitialSignupPageProviderTitle: 'Kesehatan ',
	InitialTreatment: 'Perawatan awal',
	Initials: 'Inisial',
	InlineEmbed: 'Penyematan sebaris',
	InputPhraseToConfirm: 'Untuk konfirmasi, ketik {confirmationPhrase}.',
	Insert: 'Menyisipkan',
	InsertTable: 'Sisipkan tabel',
	InstallCarepatronOnYourIphone1: 'Instal Carepatron di iOS Anda: ketuk',
	InstallCarepatronOnYourIphone2: 'lalu Tambahkan ke Layar Utama',
	InsufficientCalendarScopesSnackbar: 'Sinkronisasi gagal - mohon izinkan izin kalender ke Carepatron',
	InsufficientInboxScopesSnackbar: 'Sinkronisasi gagal - mohon izinkan izin email ke Carepatron',
	InsufficientScopeErrorCodeSnackbar: 'Sinkronisasi gagal - mohon izinkan semua izin ke Carepatron',
	Insurance: 'Asuransi',
	InsuranceAmount: 'Jumlah Asuransi',
	InsuranceClaim: 'Klaim asuransi',
	InsuranceClaimAiChatPlaceholder: 'Tanyakan tentang klaim asuransi...',
	InsuranceClaimAiClaimNumber: 'Klaim {number}',
	InsuranceClaimAiSubtitle: 'Tagihan Asuransi • Validasi Klaim',
	InsuranceClaimDeniedSubject: 'Klaim {claimNumber} yang diajukan ke {payerNumber} {payerName} ditolak',
	InsuranceClaimErrorDescription:
		'Klaim ini mengandung kesalahan yang dilaporkan dari pembayar atau clearing house. Harap tinjau pesan kesalahan berikut dan kirim kembali klaim.',
	InsuranceClaimErrorGuideLink: 'Panduan klaim asuransi',
	InsuranceClaimErrorTitle: 'Kesalahan pengajuan klaim',
	InsuranceClaimNotFound: 'Klaim asuransi tidak ditemukan',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Pembayaran sebagian sebesar {paymentAmount}} other {Pembayaran sebesar {paymentAmount}}} untuk klaim {claimNumber} oleh {payerNumber} {payerName} telah dicatat',
	InsuranceClaimRejectedSubject: 'Klaim {claimNumber} yang diajukan ke {payerNumber} {payerName} ditolak',
	InsuranceClaims: 'Klaim Asuransi',
	InsuranceInformation: 'Informasi asuransi',
	InsurancePaid: 'Asuransi Dibayar',
	InsurancePayer: 'Pembayar asuransi',
	InsurancePayers: 'Pembayar asuransi',
	InsurancePayersDescription: 'Lihat pembayar yang telah ditambahkan ke akun Anda dan kelola pendaftaran.',
	InsurancePayment: 'Pembayaran asuransi',
	InsurancePoliciesDetailsSubtitle: 'Tambahkan informasi asuransi klien untuk mendukung klaim.',
	InsurancePoliciesDetailsTitle: 'Rincian kebijakan',
	InsurancePoliciesListSubtitle: 'Tambahkan informasi asuransi klien untuk mendukung klaim.',
	InsurancePoliciesListTitle: 'Polis asuransi',
	InsuranceSelfPay: 'Bayar Sendiri',
	InsuranceType: 'Jenis asuransi',
	InsuranceUnpaid: 'Asuransi belum dibayar',
	Intake: 'Asupan',
	IntakeExpiredErrorCodeSnackbar:
		'Penerimaan ini telah kedaluwarsa. Silakan hubungi penyedia Anda untuk mengirim ulang penerimaan lainnya.',
	IntakeNotFoundErrorSnackbar:
		'Penerimaan ini tidak dapat ditemukan. Silakan hubungi penyedia Anda untuk mengirim ulang penerimaan lainnya.',
	IntakeProcessLearnMoreInstructions: 'Panduan untuk menyiapkan formulir penerimaan Anda',
	IntakeTemplateSelectorPlaceholder: 'Pilih formulir dan perjanjian untuk dikirim ke klien Anda untuk diselesaikan',
	Integration: 'Integrasi',
	IntenseBlur: 'Buramkan latar belakang Anda secara intens',
	InteriorDesigner: 'Desainer Interior',
	InternetBanking: 'Transfer Bank',
	Interval: 'Selang',
	IntervalDays: 'Interval (Hari)',
	IntervalHours: 'Interval (Jam)',
	Invalid: 'Tidak sah',
	InvalidDate: 'Tanggal tidak valid',
	InvalidDateFormat: 'Tanggal harus dalam format {format}',
	InvalidDisplayName: 'Nama tampilan tidak boleh berisi {value}',
	InvalidEmailFormat: 'Format email tidak valid',
	InvalidFileType: 'Jenis berkas tidak valid',
	InvalidGTMContainerId: 'Format ID kontainer GTM tidak valid',
	InvalidPaymentMethodCode: 'Metode pembayaran yang dipilih tidak valid. Silakan pilih yang lain.',
	InvalidPromotionCode: 'Kode promosi tidak valid',
	InvalidReferralDescription: 'Sudah menggunakan Carepatron',
	InvalidStatementDescriptor: `Deskripsi pernyataan harus terdiri dari 5 hingga 22 karakter dan hanya berisi huruf, angka, spasi, dan tidak boleh menyertakan <, >, \\, ', ", *`,
	InvalidToken: 'Token tidak valid',
	InvalidTotpSetupVerificationCode: 'Kode verifikasi tidak valid.',
	InvalidURLErrorText: 'Ini harus menjadi URL yang valid',
	InvalidZoomTokenErrorCodeSnackbar:
		'Token Zoom telah kedaluwarsa. Harap sambungkan kembali aplikasi Zoom Anda dan coba lagi.',
	Invite: 'Mengundang',
	InviteRelationships: 'Undang hubungan',
	InviteToPortal: 'Undang ke portal',
	InviteToPortalModalDescription: 'Email undangan akan dikirim ke klien Anda untuk mendaftar ke Carepatron.',
	InviteToPortalModalTitle: 'Undang {name} ke Portal Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Undang pengguna baru',
	Invited: 'Diundang',
	Invoice: 'Faktur',
	InvoiceColorPickerDescription: 'Tema warna yang akan digunakan dalam faktur',
	InvoiceColorTheme: 'Tema warna faktur',
	InvoiceContactDeleted: 'Kontak faktur telah dihapus dan faktur ini tidak dapat diperbarui.',
	InvoiceDate: 'Tanggal terbit',
	InvoiceDetails: 'Rincian faktur',
	InvoiceFieldsPlaceholder: 'Cari bidang...',
	InvoiceFrom: 'Invoice {number} dari {fromProvider}',
	InvoiceInvalidCredit: 'Jumlah kredit tidak valid, jumlah kredit tidak boleh melebihi total faktur',
	InvoiceNotFoundDescription:
		'Silakan hubungi penyedia Anda dan tanyakan informasi lebih lanjut atau untuk mengirim ulang faktur.',
	InvoiceNotFoundTitle: 'Faktur tidak ditemukan',
	InvoiceNumber: 'Faktur #',
	InvoiceNumberFormat: 'Faktur #{number}',
	InvoiceNumberMustEndWithDigit: 'Nomor faktur harus diakhiri dengan angka (0-9)',
	InvoicePageHeader: 'Faktur',
	InvoicePaidNotificationSubject: 'Invoice {invoiceNumber} dibayar',
	InvoiceReminder: 'Pengingat faktur',
	InvoiceReminderSentence:
		'Kirim pengingat {deliveryType} {interval} {unit} {beforeAfter} tanggal jatuh tempo faktur',
	InvoiceReminderSettings: 'Pengaturan pengingat faktur',
	InvoiceReminderSettingsInfo: 'Pengingat hanya berlaku untuk faktur yang dikirim melalui Carepatron',
	InvoiceReminders: 'Pengingat faktur',
	InvoiceRemindersInfo:
		'Tetapkan pengingat otomatis untuk tanggal jatuh tempo faktur. Pengingat hanya berlaku untuk faktur yang dikirim melalui Carepatron',
	InvoiceSettings: 'Pengaturan faktur',
	InvoiceStatus: 'Status faktur',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, Amerika Serikat',
	InvoiceTemplateDescriptionPlaceholder:
		'Tambahkan catatan, detail transfer bank, atau syarat dan ketentuan untuk pembayaran alternatif',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Wiraswasta',
	InvoiceTemplateEthnicityPlaceholder: 'Kaukasia',
	InvoiceTemplateNotFoundDescription: 'Silakan hubungi penyedia Anda dan tanyakan informasi lebih lanjut.',
	InvoiceTemplateNotFoundTitle: 'Template faktur tidak ditemukan',
	InvoiceTemplates: 'Template faktur',
	InvoiceTemplatesDescription:
		'Sesuaikan templat faktur Anda untuk mencerminkan merek Anda, memenuhi persyaratan peraturan, dan memenuhi preferensi klien dengan templat kami yang mudah digunakan.',
	InvoiceTheme: 'Tema faktur',
	InvoiceTotal: 'Total Invoice',
	InvoiceUninvoicedAmounts: 'Menagih jumlah yang belum ditagih',
	InvoiceUpdateVersionMessage:
		'Mengedit faktur ini memerlukan versi terbaru. Harap muat ulang Carepatron dan coba lagi.',
	Invoices: '{count, plural, one {Faktur} other {Faktur}}',
	InvoicesEmptyStateDescription: 'Tidak ada faktur yang ditemukan',
	InvoicingAndPayment: 'Penagihan ',
	Ireland: 'Irlandia',
	IsA: 'adalah',
	IsBetween: 'adalah di antara',
	IsEqualTo: 'sama dengan',
	IsGreaterThan: 'lebih besar dari',
	IsGreaterThanOrEqualTo: 'lebih besar dari atau sama dengan',
	IsLessThan: 'kurang dari',
	IsLessThanOrEqualTo: 'kurang dari atau sama dengan',
	IssueCredit: 'Masalah kredit',
	IssueCreditAdjustment: 'Masalah penyesuaian kredit',
	IssueDate: 'Tanggal terbit',
	Italic: 'Miring',
	Items: 'Barang',
	ItemsAndAdjustments: 'Item dan penyesuaian',
	ItemsRemaining: '+{count} item yang tersisa',
	JobTitle: 'Judul pekerjaan',
	Join: 'Bergabung',
	JoinCall: 'Bergabunglah dalam panggilan',
	JoinNow: 'Bergabung sekarang',
	JoinProduct: 'Bergabunglah dengan {product}',
	JoinVideoCall: 'Bergabunglah dengan panggilan video',
	JoinWebinar: 'Ikuti webinar',
	JoinWithVideoCall: 'Bergabung dengan {product}',
	Journalist: 'Wartawan',
	JustMe: 'Hanya aku',
	JustYou: 'Hanya kamu',
	Justify: 'Membenarkan',
	KeepSeparate: 'Tetap terpisah',
	KeepSeparateSuccessMessage: 'Anda telah berhasil menjaga catatan terpisah untuk {clientNames}',
	KeepWaiting: 'Tetap menunggu',
	Label: 'Label',
	LabelOptional: 'Label (Opsional)',
	LactationConsulting: 'Konsultasi laktasi',
	Language: 'Bahasa',
	Large: 'Besar',
	LastDxCode: 'Kode DX Terakhir',
	LastLoggedIn: 'Terakhir masuk {date} pukul {time}',
	LastMenstrualPeriod: 'Periode menstruasi terakhir',
	LastMonth: 'Bulan lalu',
	LastNDays: 'Terakhir {number} hari',
	LastName: 'Nama belakang',
	LastNameFirstInitial: 'Nama belakang, inisial pertama',
	LastWeek: 'Minggu lalu',
	LastXRay: 'Sinar-X terakhir',
	LatestVisitOrConsultation: 'Kunjungan atau konsultasi terakhir',
	Lawyer: 'Pengacara',
	LearnMore: 'Pelajari lebih lanjut',
	LearnMoreTipsToGettingStarted: 'Pelajari lebih lanjut kiat untuk memulai',
	LearnToSetupInbox: 'Panduan untuk mengatur akun kotak masuk',
	Leave: 'Meninggalkan',
	LeaveCall: 'Tinggalkan panggilan',
	LeftAlign: 'Rata kiri',
	LegacyBillingItemsNotAvailable:
		'Rincian tagihan individual belum tersedia untuk janji temu ini. Anda masih dapat menagihnya secara normal.',
	LegacyBillingItemsNotAvailableTitle: 'Tagihan warisan',
	LegalAndConsent: 'Hukum dan persetujuan',
	LegalConsentFormPrimaryText: 'Persetujuan hukum',
	LegalConsentFormSecondaryText: 'Terima atau tolak opsi',
	LegalGuardian: 'Wali sah',
	Letter: 'Surat',
	LettersCategoryDescription: 'Untuk membuat surat klinis dan administrasi',
	Librarian: 'Pustakawan',
	LicenseNumber: 'Nomor lisensi',
	LifeCoach: 'Pelatih Kehidupan',
	LifeCoaches: 'Pelatih Kehidupan',
	Limited: 'Terbatas',
	LineSpacing: 'Spasi baris dan paragraf',
	LinearScaleFormPrimaryText: 'Skala linier',
	LinearScaleFormSecondaryText: 'Pilihan skala 1-10',
	Lineitems: 'Item baris',
	Link: 'Link',
	LinkClientFormSearchClientLabel: 'Cari klien',
	LinkClientModalTitle: 'Tautan ke klien yang ada',
	LinkClientSuccessDescription: '**{newName}**’s informasi kontak ditambahkan ke catatan **{existingName}**.',
	LinkClientSuccessTitle: 'Berhasil ditautkan ke kontak yang ada',
	LinkForCallCopied: 'Tautan telah disalin!',
	LinkToAnExistingClient: 'Tautan ke klien yang sudah ada',
	LinkToClient: 'Tautan ke klien',
	ListAndTracker: 'Daftar/Pelacak',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} ada dalam panggilan ini}
			other {{attendees} berada dalam panggilan ini}
		}`,
	ListStyles: 'Daftar gaya',
	ListsAndTrackersCategoryDescription: 'Untuk mengatur dan melacak pekerjaan',
	LivingArrangements: 'Penataan Tempat Tinggal',
	LoadMore: 'Muat Lebih Banyak',
	Loading: 'Memuat...',
	LocalizationPanelDescription: 'Kelola pengaturan untuk bahasa dan zona waktu Anda',
	LocalizationPanelTitle: 'Bahasa dan zona waktu',
	Location: 'Lokasi',
	LocationDescription:
		'Siapkan lokasi fisik dan virtual dengan alamat tertentu, nama ruangan, dan jenis ruang virtual untuk memudahkan penjadwalan janji temu dan panggilan video.',
	LocationNumber: 'Nomor lokasi',
	LocationOfService: 'Lokasi layanan',
	LocationOfServiceRecommendedActionInfo:
		'Menambahkan lokasi tertentu ke layanan ini dapat memengaruhi ketersediaan Anda.',
	LocationRemote: 'Jarak Jauh',
	LocationType: 'Tipe lokasi',
	Locations: 'Lokasi',
	Lock: 'Kunci',
	Locked: 'Terkunci',
	LockedNote: 'Catatan terkunci',
	LogInToSaveOrAuthoriseCard: 'Masuk untuk menyimpan atau mengotorisasi kartu',
	LogInToSaveOrAuthorisePayment: 'Masuk untuk menyimpan atau mengotorisasi pembayaran',
	Login: 'Masuk',
	LoginButton: 'Masuk',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Lupa kata sandi',
	LoginPassword: 'Kata sandi',
	Logo: 'Logo',
	LogoutAreYouSure: 'Keluar dari perangkat ini.',
	LogoutButton: 'Keluar',
	London: 'London',
	LongTextAnswer: 'Jawaban teks panjang',
	LongTextFormPrimaryText: 'Teks panjang',
	LongTextFormSecondaryText: 'Opsi gaya paragraf',
	Male: 'Pria',
	Manage: 'Mengelola',
	ManageAllClientTags: 'Kelola Semua Tag Klien',
	ManageAllNoteTags: 'Kelola Semua Tag Catatan',
	ManageAllTemplateTags: 'Kelola Semua Tag Template',
	ManageConnections: 'Kelola koneksi',
	ManageConnectionsGmailDescription: 'Anggota tim lainnya tidak akan dapat melihat Gmail Anda yang disinkronkan.',
	ManageConnectionsGoogleCalendarDescription:
		'Anggota tim lain tidak akan dapat melihat kalender Anda yang disinkronkan. Janji temu klien hanya dapat diperbarui atau dihapus dari dalam Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Silakan buka halaman Kotak Masuk untuk mengelola pengaturan Sinkronisasi Kotak Masuk.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Anggota tim lain tidak akan dapat melihat kalender Anda yang disinkronkan. Janji temu klien hanya dapat diperbarui atau dihapus dari dalam Carepatron.',
	ManageConnectionsOutlookDescription:
		'Anggota tim lainnya tidak akan dapat melihat Microsoft Outlook Anda yang disinkronkan.',
	ManageInboxAccountButton: 'Kotak masuk baru',
	ManageInboxAccountEdit: 'Kelola Kotak Masuk',
	ManageInboxAccountPanelTitle: 'Kotak Masuk',
	ManageInboxAssignTeamPlaceholder: 'Pilih anggota tim untuk akses kotak masuk',
	ManageInboxBasicInfoColor: 'Warna',
	ManageInboxBasicInfoDescription: 'Keterangan',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Untuk apa Anda atau tim Anda akan menggunakan kotak masuk ini?',
	ManageInboxBasicInfoName: 'Nama kotak masuk',
	ManageInboxBasicInfoNamePlaceholder: 'Misalnya dukungan pelanggan, admin',
	ManageInboxConnectAppAlreadyConnectedError: 'Saluran yang Anda coba sambungkan sudah terhubung ke Carepatron',
	ManageInboxConnectAppConnect: 'Menghubungkan',
	ManageInboxConnectAppConnectedInfo: 'Terhubung ke akun',
	ManageInboxConnectAppContinue: 'Melanjutkan',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Masuk dengan',
	ManageInboxConnectAppSubtitle:
		'Hubungkan aplikasi Anda untuk mengirim, menerima, dan melacak semua komunikasi Anda dengan mudah di satu tempat terpusat.',
	ManageInboxNewInboxTitle: 'Kotak masuk baru',
	ManagePlan: 'Kelola Rencana',
	ManageProfile: 'Kelola profil',
	ManageReferralsModalDescription:
		'Bantu kami menyebarkan informasi tentang platform perawatan kesehatan kami dan dapatkan hadiah.',
	ManageReferralsModalTitle: 'Referensikan teman, dapatkan hadiah!',
	ManageStaffRelationshipsAddButton: 'Mengelola hubungan',
	ManageStaffRelationshipsEmptyStateText: 'Tidak ada hubungan yang ditambahkan',
	ManageStaffRelationshipsModalDescription:
		'Memilih klien akan menambah hubungan baru, sedangkan tidak memilih klien akan menghapus hubungan yang ada.',
	ManageStaffRelationshipsModalTitle: 'Mengelola hubungan',
	ManageStatuses: 'Kelola status',
	ManageStatusesActiveStatusHelperText: 'Setidaknya satu status aktif diperlukan',
	ManageStatusesDescription: 'Sesuaikan label status Anda dan pilih warna yang selaras dengan alur kerja Anda.',
	ManageStatusesSuccessSnackbar: 'Berhasil memperbarui status',
	ManageTags: 'Kelola tag',
	ManageTaskAttendeeStatus: 'Kelola status janji temu',
	ManageTaskAttendeeStatusDescription: 'Sesuaikan status janji temu Anda agar selaras dengan alur kerja Anda.',
	ManageTaskAttendeeStatusHelperText: 'Setidaknya satu status diperlukan',
	ManageTaskAttendeeStatusSubtitle: 'Status Kustom',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Buku petunjuk',
	ManualAppointment: 'Pertemuan manual',
	ManualPayment: 'Pembayaran manual',
	ManuallyTypeLocation: 'Ketik lokasi secara manual',
	MapColumns: 'Kolom Peta',
	MappingRequired: 'Pemetaan diperlukan',
	MarkAllAsRead: 'Tandai semua sebagai telah dibaca',
	MarkAsCompleted: 'Tandai sebagai selesai',
	MarkAsManualSubmission: 'Tandai sebagai terkirim',
	MarkAsPaid: 'Tandai sebagai sudah dibayar',
	MarkAsRead: 'Tandai sebagai sudah dibaca',
	MarkAsUnpaid: 'Tandai sebagai belum dibayar',
	MarkAsUnread: 'Tandai sebagai belum dibaca',
	MarkAsVoid: 'Tandai sebagai batal',
	Marker: 'Penanda',
	MarketingManager: 'Manajer Pemasaran',
	MassageTherapist: 'Terapis Pijat',
	MassageTherapists: 'Terapis Pijat',
	MassageTherapy: 'Terapi pijat',
	MaxBookingTimeDescription1: 'Klien dapat menjadwalkan hingga',
	MaxBookingTimeDescription2: 'ke masa depan',
	MaxBookingTimeLabel: '{timePeriod} sebelumnya',
	MaxCapacity: 'Kapasitas maksimal',
	Maximize: 'Memaksimalkan',
	MaximumAttendeeLimit: 'Batas maksimum',
	MaximumBookingTime: 'Waktu pemesanan maksimum',
	MaximumBookingTimeError: 'Waktu pemesanan maksimum tidak boleh melebihi {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Anda dapat meminimalkan hingga {count} panel samping sekaligus. Melanjutkan akan menutup panel yang diminimalkan pertama kali. Apakah Anda ingin melanjutkan?',
	MaximumMinimizedPanelsReachedTitle: 'Kamu memiliki terlalu banyak panel yang terbuka.',
	MechanicalEngineer: 'Insinyur Mekanik',
	MediaGallery: 'Galeri media',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Nomor penyedia Medicaid',
	MedicalAssistant: 'Asisten Medis',
	MedicalCoder: 'Koder Medis',
	MedicalDoctor: 'Dokter Medis',
	MedicalIllustrator: 'Ilustrator Medis',
	MedicalInterpreter: 'Juru Bahasa Medis',
	MedicalTechnologist: 'Teknolog Medis',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Nomor penyedia Medicare',
	Medicine: 'Obat',
	Medium: 'Sedang',
	Meeting: 'Pertemuan',
	MeetingEnd: 'Akhiri Pertemuan',
	MeetingEnded: 'Pertemuan berakhir',
	MeetingHost: 'Tuan rumah rapat',
	MeetingLowerHand: 'Tangan bawah',
	MeetingOpenChat: 'Obrolan Terbuka',
	MeetingPersonRaisedHand: '{name} mengangkat tangannya',
	MeetingRaiseHand: 'Angkat tangan',
	MeetingReady: 'Siap rapat',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {menit} other {menit}} {status}',
	Meetings: 'Pertemuan',
	MemberId: 'ID Anggota',
	MentalHealth: 'Kesehatan Mental',
	MentalHealthPractitioners: 'Praktisi Kesehatan Mental',
	MentalHealthProfessional: 'Profesional Kesehatan Mental',
	Merge: 'Menggabungkan',
	MergeClientRecords: 'Gabungkan catatan klien',
	MergeClientRecordsDescription: 'Penggabungan catatan klien akan menggabungkan semua data mereka, termasuk:',
	MergeClientRecordsDescription2:
		'Apakah Anda ingin melanjutkan penggabungan ini? Tindakan ini tidak dapat dibatalkan',
	MergeClientRecordsItem1: 'Catatan dan dokumen',
	MergeClientRecordsItem2: 'Janji Temu',
	MergeClientRecordsItem3: 'Faktur',
	MergeClientRecordsItem4: 'Percakapan',
	MergeClientsSuccess: 'Berhasil menggabungkan catatan klien',
	MergeLimitExceeded: 'Anda hanya dapat menggabungkan hingga 4 klien sekaligus.',
	Message: 'Pesan',
	MessageAttachments: '{total} lampiran',
	Method: 'Metode',
	MfaAvailabilityDisclaimer:
		'MFA hanya tersedia untuk login dengan email dan kata sandi. Untuk membuat perubahan pada pengaturan MFA, login menggunakan email dan kata sandi Anda.',
	MfaDeviceLostPanelDescription: 'Atau, Anda dapat memverifikasi identitas Anda dengan menerima kode melalui email.',
	MfaDeviceLostPanelTitle: 'Kehilangan perangkat MFA Anda?',
	MfaDidntReceiveEmailCode: 'Tidak menerima kode? Hubungi dukungan',
	MfaEmailOtpSendFailureSnackbar: 'Gagal mengirim email OTP.',
	MfaEmailOtpSentSnackbar: 'Kode telah dikirim ke {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Gagal memverifikasi OTP email.',
	MfaHasBeenSetUpText: 'Anda telah menyiapkan MFA',
	MfaPanelDescription:
		'Amankan akun Anda dengan mengaktifkan Multi-Factor Authentication (MFA) untuk lapisan perlindungan ekstra. Verifikasi identitas Anda melalui metode sekunder untuk mencegah akses yang tidak sah.',
	MfaPanelNotAuthorizedError: 'Anda harus masuk dengan nama pengguna ',
	MfaPanelRecommendationDescription:
		'Anda baru saja masuk menggunakan metode alternatif untuk memverifikasi identitas Anda. Untuk menjaga keamanan akun Anda, pertimbangkan untuk menyiapkan perangkat MFA baru.',
	MfaPanelRecommendationTitle: '**Disarankan:** Perbarui perangkat MFA Anda',
	MfaPanelTitle: 'Autentikasi Multi-Faktor (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'Anda perlu memverifikasi email Anda sebelum dapat memperbarui pengaturan MFA Anda.',
	MfaRecommendationBannerDescription:
		'Anda baru saja masuk menggunakan metode alternatif untuk memverifikasi identitas Anda. Untuk menjaga keamanan akun Anda, pertimbangkan untuk menyiapkan perangkat MFA baru.',
	MfaRecommendationBannerPrimaryAction: 'Siapkan MFA',
	MfaRecommendationBannerTitle: 'Direkomendasikan',
	MfaRemovedSnackbarTitle: 'MFA telah dihapus.',
	MfaSendEmailCode: 'Kirim kode',
	MfaVerifyIdentityLostDeviceButton: 'Saya kehilangan akses ke perangkat MFA saya',
	MfaVerifyYourIdentityPanelDescription: 'Periksa kode pada aplikasi autentikator Anda dan masukkan di bawah ini.',
	MfaVerifyYourIdentityPanelTitle: 'Verifikasi identitas Anda',
	MicCamWarningMessage: 'Buka blokir kamera dan mikrofon dengan mengklik ikon yang diblokir di bilah alamat browser.',
	MicCamWarningTitle: 'Kamera dan mikrofon diblokir',
	MicOff: 'Mikrofon mati',
	MicOn: 'Mikrofon aktif',
	MicSource: 'Sumber mikrofon',
	MicWarningMessage: 'Masalah telah terdeteksi pada mikrofon Anda',
	Microphone: 'Mikropon',
	MicrophonePermissionBlocked: 'Akses mikrofon diblokir',
	MicrophonePermissionBlockedDescription: 'Perbarui izin mikrofon Anda untuk memulai perekaman.',
	MicrophonePermissionError: 'Harap berikan izin mikrofon di pengaturan browser Anda untuk melanjutkan',
	MicrophonePermissionPrompt: 'Harap izinkan akses mikrofon untuk melanjutkan',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Warna kalender Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Tim Microsoft',
	MiddleEast: 'Timur Tengah',
	MiddleName: 'Nama tengah',
	MiddleNames: 'Nama tengah',
	Midwife: 'Bidan',
	Midwives: 'Bidan',
	Milan: 'Milan',
	MinBookingTimeDescription1: 'Klien tidak dapat menjadwalkan dalam',
	MinBookingTimeDescription2: 'dari waktu mulai janji temu',
	MinBookingTimeLabel: '{timePeriod} sebelum janji temu',
	MinCancellationTimeEditModeDescription: 'Tetapkan berapa jam klien dapat membatalkan tanpa penalti',
	MinCancellationTimeUnset: 'Tidak ada waktu pembatalan minimum yang ditetapkan',
	MinCancellationTimeViewModeDescription: 'Periode pembatalan tanpa penalti',
	MinMaxBookingTimeUnset: 'Tidak ada waktu yang ditentukan',
	Minimize: 'Memperkecil',
	MinimizeConfirmationDescription:
		'Anda memiliki panel yang sedang diminimalkan. Jika Anda melanjutkan, panel ini akan ditutup, dan Anda mungkin kehilangan data yang belum disimpan.',
	MinimizeConfirmationTitle: 'Tutup panel yang diminimalkan?',
	MinimumBookingTime: 'Waktu pemesanan minimum',
	MinimumCancellationTime: 'Waktu pembatalan minimum',
	MinimumPaymentError: 'Biaya minimum {minimumAmount} diperlukan untuk pembayaran online',
	MinuteAbbreviated: 'menit',
	MinuteAbbreviation: '{count} {count, plural, one {menit} other {menit}}',
	Minutely: 'Menit demi menit',
	MinutesPlural: '{age, plural, one {# menit} other {# menit}}',
	MiscellaneousInformation: 'Informasi lain-lain',
	MissingFeatures: 'Fitur yang hilang',
	MissingPaymentMethod:
		'Harap tambahkan metode pembayaran ke langganan Anda untuk menambahkan lebih banyak anggota staf.',
	MobileNumber: 'Nomor ponsel',
	MobileNumberOptional: 'Nomor Ponsel (opsional)',
	Modern: 'Modern',
	Modifiers: 'Pengubah',
	ModifiersPlaceholder: 'Pengubah',
	Monday: 'Senin',
	Month: 'Bulan',
	Monthly: 'Bulanan',
	MonthlyCost: 'Biaya Bulanan',
	MonthlyOn: 'Bulanan pada {date}',
	MonthsPlural: '{age, plural, one {# bulan} other {# bulan}}',
	More: 'Lagi',
	MoreActions: 'Lebih banyak tindakan',
	MoreSettings: 'Pengaturan lainnya',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Paling umum digunakan',
	MostDownloaded: 'Paling banyak diunduh',
	MostPopular: 'Paling populer',
	Mother: 'Ibu',
	MotherInLaw: 'Ibu mertua',
	MoveDown: 'Pindah ke bawah',
	MoveInboxConfirmationDescription:
		'Menetapkan ulang koneksi aplikasi ini akan menghapusnya dari kotak masuk <strong>{currentInboxName}</strong>.',
	MoveTemplateToFolder: 'Pindahkan `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} dipindahkan ke {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Berhasil dipindahkan ke folder intake default',
	MoveTemplateToNewFolder: 'Buat folder baru untuk memindahkan item ini.',
	MoveToChosenFolder: 'Pilih folder untuk memindahkan item ini. Anda dapat membuat folder baru jika diperlukan.',
	MoveToFolder: 'Pindah ke folder',
	MoveToInbox: 'Pindahkan ke Kotak Masuk',
	MoveToNewFolder: 'Pindahkan ke folder baru',
	MoveToSelectedFolder:
		'Setelah dipindahkan, item tersebut akan diatur di bawah folder yang dipilih dan tidak akan lagi muncul di lokasi saat ini.',
	MoveUp: 'Naik ke atas',
	MultiSpeciality: 'Multi-spesialisasi',
	MultipleChoiceFormPrimaryText: 'Pilihan ganda',
	MultipleChoiceFormSecondaryText: 'Pilih beberapa opsi',
	MultipleChoiceGridFormPrimaryText: 'Kotak pilihan ganda',
	MultipleChoiceGridFormSecondaryText: 'Pilih opsi dari matriks',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Terapis Musik',
	MustContainOneLetterError: 'Harus mengandung setidaknya satu huruf',
	MustEndWithANumber: 'Harus diakhiri dengan angka',
	MustHaveAtLeastXItems: 'Harus memiliki setidaknya {count, plural, one {# item} other {# items}}',
	MuteAudio: 'Matikan audio',
	MuteEveryone: 'Bisukan semua orang',
	MyAvailability: 'Ketersediaan saya',
	MyGallery: 'Galeri saya',
	MyPortal: 'Portal Saya',
	MyRelationships: 'Hubungan saya',
	MyTemplates: 'Templat Tim',
	MyofunctionalTherapist: 'Terapis Miofungsional',
	NCalifornia: 'California Utara',
	NPI: 'NPI',
	NVirginia: 'Virginia Utara',
	Name: 'Nama',
	NameIsRequired: 'Nama diperlukan',
	NameMustNotBeAWebsite: 'Nama tidak boleh berupa situs web',
	NameMustNotBeAnEmail: 'Nama tidak boleh berupa email',
	NameMustNotContainAtSign: 'Nama tidak boleh mengandung tanda @',
	NameMustNotContainHTMLTags: 'Nama tidak boleh mengandung tag HTML',
	NameMustNotContainSpecialCharacters: 'Nama tidak boleh mengandung karakter khusus',
	NameOnCard: 'Nama pada kartu',
	NationalProviderId: 'Pengidentifikasi penyedia nasional (NPI)',
	NaturopathicDoctor: 'Dokter Naturopati',
	NavigateToPersonalSettings: 'Profil',
	NavigateToSubscriptionSettings: 'Pengaturan Langganan',
	NavigateToWorkspaceSettings: 'Pengaturan Ruang Kerja',
	NavigateToYourTeam: 'Kelola tim',
	NavigationDrawerBilling: 'Tagihan',
	NavigationDrawerBillingInfo: 'Info penagihan, faktur, dan Stripe',
	NavigationDrawerCommunication: 'Komunikasi',
	NavigationDrawerCommunicationInfo: 'Notifikasi dan template',
	NavigationDrawerInsurance: 'Asuransi',
	NavigationDrawerInsuranceInfo: 'Pembayar dan klaim asuransi',
	NavigationDrawerInvoices: 'Penagihan',
	NavigationDrawerPersonal: 'Profil Saya',
	NavigationDrawerPersonalInfo: 'Detail pribadi Anda',
	NavigationDrawerProfile: 'Profil',
	NavigationDrawerProviderSettings: 'Pengaturan',
	NavigationDrawerScheduling: 'Penjadwalan',
	NavigationDrawerSchedulingInfo: 'Detail dan pemesanan layanan',
	NavigationDrawerSettings: 'Pengaturan',
	NavigationDrawerTemplates: 'Templat',
	NavigationDrawerTemplatesV2: 'Template V2',
	NavigationDrawerTrash: 'Sampah',
	NavigationDrawerTrashInfo: 'Mengembalikan item yang dihapus',
	NavigationDrawerWorkspace: 'Pengaturan Ruang Kerja',
	NavigationDrawerWorkspaceInfo: 'Info langganan dan ruang kerja',
	NegativeBalanceNotSupported: 'Saldo akun negatif tidak didukung',
	Nephew: 'Keponakan laki-laki',
	NetworkQualityFair: 'Koneksi yang adil',
	NetworkQualityGood: 'Koneksi bagus',
	NetworkQualityPoor: 'Koneksi buruk',
	Neurologist: 'Ahli saraf',
	Never: 'Tidak pernah',
	New: 'Baru',
	NewAppointment: 'Penunjukan baru',
	NewClaim: 'Klaim baru',
	NewClient: 'Klien baru',
	NewClientNextStepsModalAddAnotherClient: 'Tambahkan klien lain',
	NewClientNextStepsModalBookAppointment: 'Pesan janji temu',
	NewClientNextStepsModalBookAppointmentDescription: 'Pesan janji temu mendatang atau buat tugas.',
	NewClientNextStepsModalCompleteBasicInformation: 'Catatan klien lengkap',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Tambahkan informasi klien dan rekam langkah berikutnya.',
	NewClientNextStepsModalCreateInvoice: 'Buat faktur',
	NewClientNextStepsModalCreateInvoiceDescription: 'Tambahkan informasi pembayaran klien atau buat faktur.',
	NewClientNextStepsModalCreateNote: 'Buat catatan atau unggah dokumen',
	NewClientNextStepsModalCreateNoteDescription: 'Menangkap catatan dan dokumentasi klien.',
	NewClientNextStepsModalDescription:
		'Berikut adalah beberapa tindakan yang perlu dilakukan setelah Anda membuat rekaman klien.',
	NewClientNextStepsModalSendIntake: 'Kirim asupan',
	NewClientNextStepsModalSendIntakeDescription:
		'Kumpulkan informasi klien dan kirim formulir tambahan untuk dilengkapi dan ditandatangani.',
	NewClientNextStepsModalSendMessage: 'Kirim pesan',
	NewClientNextStepsModalSendMessageDescription: 'Tulis dan kirim pesan ke klien Anda.',
	NewClientNextStepsModalTitle: 'Langkah selanjutnya',
	NewClientSuccess: 'Berhasil membuat klien baru',
	NewClients: 'Klien baru',
	NewConnectedApp: 'Aplikasi terhubung baru',
	NewContact: 'Kontak baru',
	NewContactNextStepsModalAddRelationship: 'Tambahkan hubungan',
	NewContactNextStepsModalAddRelationshipDescription: 'Hubungkan kontak ini ke klien atau grup terkait.',
	NewContactNextStepsModalBookAppointment: 'Pesan janji temu',
	NewContactNextStepsModalBookAppointmentDescription: 'Pesan janji temu mendatang atau buat tugas.',
	NewContactNextStepsModalCompleteProfile: 'Profil lengkap',
	NewContactNextStepsModalCompleteProfileDescription: 'Tambahkan informasi kontak dan catat langkah selanjutnya.',
	NewContactNextStepsModalCreateNote: 'Buat catatan atau unggah dokumen',
	NewContactNextStepsModalCreateNoteDescription: 'Rekam catatan dan dokumentasi klien.',
	NewContactNextStepsModalDescription:
		'Berikut adalah beberapa tindakan yang dapat Anda ambil sekarang setelah Anda membuat kontak.',
	NewContactNextStepsModalInviteToPortal: 'Undangan ke Portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Kirim undangan untuk mengakses portal.',
	NewContactNextStepsModalTitle: 'Langkah selanjutnya',
	NewContactSuccess: 'Berhasil membuat kontak baru',
	NewDateOverrideButton: 'Penggantian tanggal baru',
	NewDiagnosis: 'Tambahkan diagnosis',
	NewField: 'Lapangan baru',
	NewFolder: 'Folder baru',
	NewInvoice: 'Faktur baru',
	NewLocation: 'Lokasi baru',
	NewLocationFailure: 'Gagal membuat lokasi baru',
	NewLocationSuccess: 'Berhasil membuat lokasi baru',
	NewManualPayer: 'Pembayar manual baru',
	NewNote: 'Catatan baru',
	NewNoteCreated: 'Berhasil membuat catatan baru',
	NewPassword: 'Kata sandi baru',
	NewPayer: 'Pembayar baru',
	NewPaymentMethod: 'Metode pembayaran baru',
	NewPolicy: 'Kebijakan baru',
	NewRelationship: 'Hubungan baru',
	NewReminder: 'Pengingat baru',
	NewSchedule: 'Jadwal baru',
	NewSection: 'Bagian baru',
	NewSectionOld: 'Bagian baru [LAMA]',
	NewSectionWithGrid: 'Bagian baru dengan grid',
	NewService: 'Layanan baru',
	NewServiceFailure: 'Gagal membuat layanan baru',
	NewServiceSuccess: 'Berhasil membuat layanan baru',
	NewStatus: 'Status baru',
	NewTask: 'Tugas baru',
	NewTaxRate: 'Tarif pajak baru',
	NewTeamMemberNextStepsModalAssignClients: 'Tetapkan Klien',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Tetapkan klien tertentu ke anggota tim Anda.',
	NewTeamMemberNextStepsModalAssignServices: 'Tentukan layanan',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Kelola layanan yang ditugaskan dan sesuaikan harga sesuai kebutuhan.',
	NewTeamMemberNextStepsModalBookAppointment: 'Booking janji temu',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Pesan janji temu mendatang atau buat tugas.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Profil lengkap',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Tambahkan detail tentang anggota tim Anda untuk melengkapi profil mereka.',
	NewTeamMemberNextStepsModalDescription:
		'Berikut adalah beberapa tindakan yang dapat Anda lakukan sekarang setelah Anda membuat anggota tim.',
	NewTeamMemberNextStepsModalEditPermissions: 'Izin Edit',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Sesuaikan tingkat akses mereka untuk memastikan mereka memiliki izin yang tepat.',
	NewTeamMemberNextStepsModalSetAvailability: 'Atur Ketersediaan',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Konfigurasikan ketersediaan mereka untuk membuat jadwal.',
	NewTeamMemberNextStepsModalTitle: 'Langkah selanjutnya',
	NewTemplateFolderDescription: 'Buat folder baru untuk mengatur dokumentasi Anda.',
	NewUIUpdateBannerButton: 'Muat ulang aplikasi',
	NewUIUpdateBannerTitle: 'Ada pembaruan baru yang siap!',
	NewZealand: 'Selandia Baru',
	Newest: 'Terbaru',
	NewestUnreplied: 'Terbaru belum dibalas',
	Next: 'Berikutnya',
	NextInvoiceIssueDate: 'Tanggal penerbitan faktur berikutnya',
	NextNDays: 'Berikutnya {number} hari',
	Niece: 'Keponakan perempuan',
	No: 'TIDAK',
	NoAccessGiven: 'Tidak ada akses yang diberikan',
	NoActionConfigured: 'Tidak ada tindakan yang dikonfigurasi',
	NoActivePolicies: 'Tidak ada kebijakan aktif',
	NoActiveReferrals: 'Anda tidak memiliki referensi aktif',
	NoAppointmentsFound: 'Tidak ada janji temu yang ditemukan',
	NoAppointmentsHeading: 'Mengelola janji temu dan aktivitas klien',
	NoArchivedPolicies: 'Tidak ada kebijakan yang diarsipkan',
	NoAvailableTimes: 'Tidak ada waktu yang tersedia.',
	NoBillingItemsFound: 'Tidak ditemukan item tagihan',
	NoCalendarsSynced: 'Tidak ada kalender yang disinkronkan',
	NoClaimsFound: 'Tidak ada klaim yang ditemukan',
	NoClaimsHeading: 'Sederhanakan pengiriman klaim untuk penggantian biaya',
	NoClientsHeading: 'Satukan catatan klien Anda',
	NoCompletedReferrals: 'Anda tidak memiliki rujukan lengkap',
	NoConnectionsHeading: 'Sederhanakan komunikasi klien Anda',
	NoContactsGivenAccess: 'Tidak ada klien atau kontak yang diberi akses ke catatan ini',
	NoContactsHeading: 'Tetap terhubung dengan mereka yang mendukung praktik Anda',
	NoCopayOrCoinsurance: 'Tidak ada pembayaran bersama atau asuransi bersama',
	NoCustomServiceSchedule:
		'Tidak ada jadwal khusus yang ditetapkan — ketersediaannya bergantung pada ketersediaan anggota tim',
	NoDescription: 'Tidak ada deskripsi',
	NoDocumentationHeading: 'Buat dan simpan catatan dengan aman',
	NoDuplicateRecordsHeading: 'Catatan klien Anda bebas dari duplikat',
	NoEffect: 'Tidak berpengaruh',
	NoEnrolmentProfilesFound: 'Tidak ditemukan profil pendaftaran',
	NoGlossaryItems: 'Tidak ada item glosarium',
	NoInvitedReferrals: 'Anda tidak memiliki referensi yang diundang',
	NoInvoicesFound: 'Tidak ditemukan faktur',
	NoInvoicesHeading: 'Otomatiskan penagihan dan pembayaran Anda',
	NoLimit: 'Tidak ada batas',
	NoLocationsFound: 'Tidak ada lokasi yang ditemukan',
	NoLocationsWillBeAdded: 'Tidak ada lokasi yang akan ditambahkan.',
	NoNoteFound: 'Tidak ada catatan yang ditemukan',
	NoPaymentMethods:
		'Anda tidak memiliki metode pembayaran yang tersimpan, Anda dapat menambahkannya saat melakukan pembayaran.',
	NoPermissionError: 'Anda tidak memiliki izin',
	NoPermissions: 'Anda tidak memiliki izin untuk melihat halaman ini',
	NoPolicy: 'Tidak ada kebijakan pembatalan yang ditambahkan',
	NoRecordsHeading: 'Personalisasi catatan klien Anda',
	NoRecordsToDisplay: 'Tidak ada {resource} untuk ditampilkan',
	NoRelationshipsHeading: 'Tetap terhubung dengan mereka yang mendukung klien Anda',
	NoRemindersFound: 'Tidak ada pengingat yang ditemukan',
	NoResultsFound: 'Tidak ada hasil ditemukan',
	NoResultsFoundDescription: 'Kami tidak dapat menemukan item yang sesuai dengan pencarian Anda',
	NoServicesAdded: 'Tidak ada layanan yang ditambahkan',
	NoServicesApplied: 'Tidak ada layanan yang diterapkan',
	NoServicesWillBeAdded: 'Tidak ada layanan yang akan ditambahkan.',
	NoTemplate: 'Anda tidak memiliki template latihan yang tersimpan',
	NoTemplatesHeading: 'Buat template Anda sendiri',
	NoTemplatesInFolder: 'Tidak ada templat di folder ini',
	NoTitle: 'Tidak ada judul',
	NoTrashItemsHeading: 'Tidak ditemukan item yang dihapus',
	NoTriggerConfigured: 'Tidak ada pemicu yang dikonfigurasi',
	NoUnclaimedItemsFound: 'Tidak ditemukan barang yang tidak diklaim.',
	NonAiTemplates: 'Template non-AI',
	None: 'Tidak ada',
	NotAvailable: 'Tidak tersedia',
	NotCovered: 'Tidak tercakup',
	NotFoundSnackbar: 'Sumber tidak ditemukan.',
	NotRequiredField: 'Tidak diperlukan',
	Note: 'Catatan',
	NoteDuplicateSuccess: 'Catatan berhasil digandakan',
	NoteEditModeViewSwitcherDescription: 'Buat dan edit catatan',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} mengirimkan formulir {noteTitle}',
	NoteLockSuccess: '{title} telah terkunci',
	NoteModalAttachmentButton: 'Tambahkan lampiran',
	NoteModalPhotoButton: 'Tambahkan/Ambil foto',
	NoteModalTrascribeButton: 'Transkripsikan audio langsung',
	NoteResponderModeViewSwitcherDescription: 'Kirim formulir dan tinjau respons',
	NoteResponderModeViewSwitcherTooltipTitle: 'Tanggapi dan kirimkan formulir atas nama klien Anda',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Isi dan kirim formulir sebagai klien',
	NoteUnlockSuccess: '{title} telah dibuka kunci',
	NoteViewModeViewSwitcherDescription: 'Akses hanya lihat',
	Notes: 'Catatan',
	NotesAndForms: 'Catatan dan Formulir',
	NotesCategoryDescription: 'Untuk mendokumentasikan interaksi klien',
	NothingToSeeHere: 'Tidak ada yang bisa dilihat di sini',
	Notification: 'Pemberitahuan',
	NotificationIgnoredMessage: 'Semua notifikasi {notificationType} akan diabaikan',
	NotificationRestoredMessage: 'Semua notifikasi {notificationType} dipulihkan',
	NotificationSettingBillingDescription: 'Terima notifikasi untuk pembaruan dan pengingat pembayaran klien.',
	NotificationSettingBillingTitle: 'Penagihan dan pembayaran',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} hanya} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Terima notifikasi untuk pembaruan dan pengingat pembayaran klien.',
	NotificationSettingClientDocumentationTitle: 'Klien dan dokumentasi',
	NotificationSettingCommunicationsDescription:
		'Terima pemberitahuan untuk kotak masuk dan pembaruan dari saluran terhubung Anda',
	NotificationSettingCommunicationsTitle: 'Komunikasi',
	NotificationSettingEmail: 'Email',
	NotificationSettingInApp: 'Di aplikasi',
	NotificationSettingPanelDescription: 'Pilih notifikasi yang ingin Anda terima untuk aktivitas dan rekomendasi.',
	NotificationSettingPanelTitle: 'Preferensi notifikasi',
	NotificationSettingSchedulingDescription:
		'Terima pemberitahuan saat anggota tim atau klien memesan, menjadwalkan ulang, atau membatalkan janji temu mereka.',
	NotificationSettingSchedulingTitle: 'Penjadwalan',
	NotificationSettingUpdateSuccess: 'Pengaturan notifikasi berhasil diperbarui',
	NotificationSettingWhereYouReceiveNotifications: 'Di mana Anda ingin menerima notifikasi ini',
	NotificationSettingWorkspaceDescription:
		'Terima notifikasi untuk perubahan sistem, masalah, transfer data, dan pengingat langganan.',
	NotificationSettingWorkspaceTitle: 'Ruang kerja',
	NotificationTemplateUpdateFailed: 'Gagal memperbarui templat notifikasi',
	NotificationTemplateUpdateSuccess: 'Template notifikasi berhasil diperbarui',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Apakah Anda ingin mengirim email pemberitahuan pembatalan kepada peserta?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Kirim pembatalan',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Apakah Anda ingin mengirim email pemberitahuan konfirmasi kepada peserta?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Kirim konfirmasi',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Apakah Anda ingin mengirim email pembatalan kepada peserta?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {tidak} other {tidak}} memiliki alamat email sehingga tidak akan menerima pemberitahuan dan pengingat otomatis.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {tidak} other {tidak}} memiliki alamat email sehingga tidak akan menerima pemberitahuan dan pengingat otomatis.',
	NotifyAttendeesOfTaskModalTitle: 'Apakah Anda ingin mengirim email pemberitahuan kepada peserta?',
	NotifyAttendeesOfTaskSnackbar: 'Mengirim pemberitahuan',
	NuclearMedicineTechnologist: 'Teknolog Kedokteran Nuklir',
	NumberOfClaims: '{number, plural, one {# Klaim} other {# Klaim}}',
	NumberOfClients: '{number, plural, one {# Klien} other {# Klien}}',
	NumberOfContacts: '{number, plural, one {# Kontak} other {# Kontak}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entri} other {entri}} ditemukan',
	NumberOfErrors: '{count, plural, one {# kesalahan} other {# kesalahan}}',
	NumberOfInvoices: '{number, plural, one {# Faktur} other {# Faktur}}',
	NumberOfLineitemsToCredit:
		'Anda memiliki <mark>{count} {count, plural, one {item baris} other {item baris}}</mark> untuk mengeluarkan kredit.',
	NumberOfPayments: '{number, plural, one {# Pembayaran} other {# Pembayaran}}',
	NumberOfRelationships: '{number, plural, one {# Hubungan} other {# Hubungan}}',
	NumberOfResources: '{number, plural, one {# Sumber Daya} other {# Sumber Daya}}',
	NumberOfTeamMembers: '{number, plural, one {# Anggota Tim} other {# Anggota Tim}}',
	NumberOfTrashItems: '{number, plural, one {# item} other {# item}}',
	NumberOfUninvoicedAmounts:
		'Anda memiliki <mark>{count} {count, plural, one {jumlah} other {jumlah-jumlah}} yang belum difaktur</mark> untuk difakturkan',
	NumberedList: 'Daftar bernomor',
	Nurse: 'Perawat',
	NurseAnesthetist: 'Perawat Anestesi',
	NurseAssistant: 'Asisten Perawat',
	NurseEducator: 'Pendidik Perawat',
	NurseMidwife: 'Perawat Bidan',
	NursePractitioner: 'Perawat Praktisi',
	Nurses: 'Perawat',
	Nursing: 'Perawatan',
	Nutritionist: 'Ahli ilmu gizi',
	Nutritionists: 'Ahli Gizi',
	ObstetricianOrGynecologist: 'Dokter Kandungan/Ginekologi',
	Occupation: 'Pekerjaan',
	OccupationalTherapist: 'Terapis Okupasi',
	OccupationalTherapists: 'Terapis Okupasi',
	OccupationalTherapy: 'Terapi okupasi',
	Occurrences: 'Kejadian',
	Of: 'dari',
	Ohio: 'Bahasa Indonesia: Ohio',
	OldPassword: 'Kata sandi lama',
	OlderMessages: '{count} pesan yang lebih lama',
	Oldest: 'Tertua',
	OldestUnreplied: 'Tertua yang belum dibalas',
	On: 'pada',
	OnboardingBusinessAgreement: 'Atas nama saya dan perusahaan, saya setuju dengan {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Terapis okupasi</mark> membentuk seperempat dari pelanggan kami di Carepatron',
	OnboardingLoadingProfession:
		'Kami memiliki banyak <mark>{profession}</mark> yang menggunakan dan berkembang di Carepatron.',
	OnboardingLoadingPsychologist: '<mark>Psikolog</mark> membentuk lebih dari setengah pelanggan kami di Carepatron',
	OnboardingLoadingSubtitleFive:
		'Misi kami adalah membuat<mark> perangkat lunak perawatan kesehatan yang dapat diakses</mark> untuk semua orang.',
	OnboardingLoadingSubtitleFour:
		'<mark>Perangkat lunak kesehatan yang disederhanakan</mark> untuk lebih dari 10.000 orang di seluruh dunia.',
	OnboardingLoadingSubtitleThree:
		'Menyimpan<mark> 1 hari per minggu</mark> pada tugas-tugas administrasi dengan bantuan Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Menyimpan<mark> 2 jam</mark> setiap hari pada tugas-tugas administrasi dengan bantuan Carepatron.',
	OnboardingReviewLocationOne: 'Pusat Kesehatan Mental Holland Park',
	OnboardingReviewLocationThree: 'Perawat Praktik, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Klinik Rumah Kehidupan',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron sangat intuitif untuk digunakan. Carepatron membantu kami menjalankan praktik dengan sangat baik sehingga kami bahkan tidak memerlukan tim administrator lagi"',
	OnboardingReviewThree:
		'"Ini adalah solusi praktik terbaik yang pernah saya gunakan, baik dari segi fitur maupun biaya. Solusi ini memiliki semua yang saya butuhkan untuk mengembangkan bisnis saya"',
	OnboardingReviewTwo:
		'"Saya juga menyukai aplikasi carepatron. Aplikasi ini membantu saya memantau klien dan pekerjaan saya saat bepergian."',
	OnboardingTitle: `Ayo kita mulai<mark> tahu
 kamu lebih baik</mark>`,
	Oncologist: 'Dokter Spesialis Onkologi',
	Online: 'On line',
	OnlineBookingColorTheme: 'Tema warna pemesanan online',
	OnlineBookings: 'Pemesanan online',
	OnlineBookingsHelper: 'Pilih kapan pemesanan online dapat dilakukan dan berdasarkan jenis klien mana',
	OnlinePayment: 'Pembayaran online',
	OnlinePaymentSettingCustomInfo:
		'Pengaturan pembayaran daring untuk layanan ini berbeda dari pengaturan pemesanan global.',
	OnlinePaymentSettings: 'Pengaturan pembayaran online',
	OnlinePaymentSettingsInfo:
		'Kumpulkan pembayaran untuk layanan pada saat pemesanan online untuk mengamankan dan menyederhanakan pembayaran',
	OnlinePaymentSettingsPaymentsDisabled:
		'Pembayaran dinonaktifkan sehingga tidak dapat dikumpulkan selama pemesanan online. Harap periksa pengaturan pembayaran Anda untuk mengaktifkan pembayaran.',
	OnlinePaymentSettingsStripeNote:
		'{action} untuk menerima pembayaran pemesanan daring dan memperlancar proses pembayaran Anda',
	OnlinePaymentsNotSupportedForCurrency: 'Pembayaran daring tidak didukung di {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Maaf, pembayaran online tidak didukung dalam mata uang ini',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar: 'Maaf, pembayaran online belum didukung di negara Anda',
	OnlineScheduling: 'Penjadwalan Online',
	OnlyVisibleToYou: 'Hanya terlihat oleh Anda',
	OnlyYou: 'Hanya kamu',
	OnsetDate: 'Tanggal mulai',
	OnsetOfCurrentSymptomsOrIllness: 'Timbulnya gejala atau penyakit saat ini',
	Open: 'Membuka',
	OpenFile: 'Buka berkas',
	OpenSettings: 'Buka pengaturan',
	Ophthalmologist: 'Dokter mata',
	OptimiseTelehealthCalls: 'Optimalkan panggilan Telehealth Anda',
	OptimizeServiceTimes: 'Optimalkan waktu layanan',
	Options: 'Pilihan',
	Optometrist: 'Ahli kacamata',
	Or: 'atau',
	OrAttachSingleFile: 'lampirkan file',
	OrDragAndDrop: 'atau seret dan lepas',
	OrderBy: 'Pesan Berdasarkan',
	Oregon: 'Negara Bagian Oregon',
	OrganisationOrIndividual: 'Organisasi atau individu',
	OrganizationPlanInclusion1: 'Izin lanjutan',
	OrganizationPlanInclusion2: 'Dukungan impor data klien gratis',
	OrganizationPlanInclusion3: 'Manajer sukses yang berdedikasi',
	OrganizationPlanInclusionHeader: 'Segala yang ada di Profesional, plus...',
	Orthodontist: 'Dokter Gigi Ortodontis',
	Orthotist: 'Dokter Spesialis Ortopedi',
	Other: 'Lainnya',
	OtherAdjustments: 'Penyesuaian lainnya',
	OtherAdjustmentsTableEmptyState: 'Tidak ada penyesuaian yang ditemukan',
	OtherEvents: 'Acara lainnya',
	OtherId: 'ID Lainnya',
	OtherIdQualifier: 'Kualifikasi ID lainnya',
	OtherPaymentMethod: 'Metode pembayaran lainnya',
	OtherPlanMessage:
		'Tetap kendalikan kebutuhan praktik Anda. Tinjau rencana Anda saat ini, pantau penggunaan, dan jelajahi opsi peningkatan untuk membuka lebih banyak fitur saat tim Anda berkembang.',
	OtherPolicy: 'Asuransi lainnya',
	OtherProducts: 'Produk atau alat apa lagi yang Anda gunakan?',
	OtherServices: 'Layanan lainnya',
	OtherTemplates: 'Template lainnya',
	Others: 'Yang lain',
	OthersPeople: `{n, plural, 		one {1 orang lain}
		other {# orang lainnya}
	}`,
	OurResearchTeamReachOut:
		'Dapatkah tim peneliti kami menghubungi Anda untuk mempelajari lebih lanjut tentang bagaimana Carepatron dapat lebih memenuhi kebutuhan Anda?',
	OutOfOffice: 'Diluar kantor',
	OutOfOfficeColor: 'Warna di luar kantor',
	OutOfOfficeHelper: 'Beberapa anggota tim yang dipilih sedang tidak berada di kantor',
	OutsideLabCharges: 'Biaya lab luar',
	OutsideOfWorkingHours: 'Diluar jam kerja',
	OutsideWorkingHoursHelper: 'Beberapa anggota tim yang dipilih berada di luar jam kerja',
	Overallocated: 'kelebihan alokasi',
	OverallocatedPaymentDescription: `Pembayaran ini telah dialokasikan berlebihan ke item yang dapat ditagih.
 Tambahkan alokasi ke item yang belum dibayar, atau keluarkan kredit atau pengembalian dana.`,
	OverallocatedPaymentTitle: 'Pembayaran yang dialokasikan berlebihan',
	OverdueTerm: 'Jangka waktu jatuh tempo (hari)',
	OverinvoicedAmount: 'Jumlah yang ditagih berlebihan',
	Overpaid: 'Dibayar terlalu mahal',
	OverpaidAmount: 'Jumlah kelebihan pembayaran',
	Overtime: 'lembur',
	Owner: 'Pemilik',
	POS: 'POS',
	POSCode: 'Kode POS',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Perubahan yang tidak tersimpan akan hilang. Masih ingin pergi?',
	PageBlockerTitle: 'Buang perubahan?',
	PageFormat: 'Format halaman',
	PageNotFound: 'Halaman tidak ditemukan',
	PageNotFoundDescription: 'Anda tidak lagi memiliki akses ke halaman ini atau halaman ini tidak dapat ditemukan',
	PageUnauthorised: 'Akses tidak sah',
	PageUnauthorisedDescription: 'Anda tidak memiliki izin untuk mengakses halaman ini',
	Paid: 'Dibayar',
	PaidAmount: 'Jumlah yang Dibayarkan',
	PaidAmountMinimumValueError: 'Jumlah yang dibayarkan harus lebih besar dari 0',
	PaidAmountRequiredError: 'Jumlah yang dibayarkan diperlukan',
	PaidItems: 'Item berbayar',
	PaidMultiple: 'Dibayar',
	PaidOut: 'Sudah dibayar',
	ParagraphStyles: 'Gaya paragraf',
	Parent: 'Induk',
	Paris: 'Paris',
	PartialRefundAmount: 'Refund sebagian ({amount} tersisa)',
	PartiallyFull: 'Sebagian penuh',
	PartiallyPaid: 'Dibayar sebagian',
	PartiallyRefunded: 'Sebagian sudah dikembalikan',
	Partner: 'Mitra',
	Password: 'Kata sandi',
	Past: 'Masa lalu',
	PastDateOverridesEmpty: 'Tanggal yang Anda ganti akan muncul di sini segera setelah acara berakhir',
	Pathologist: 'Ahli patologi',
	Patient: 'Sabar',
	Pause: 'Berhenti sebentar',
	Paused: 'Dijeda',
	Pay: 'Membayar',
	PayMonthly: 'Bayar bulanan',
	PayNow: 'Bayar sekarang',
	PayValue: 'Bayar {showPrice, select, true {{price}} other {now}}',
	PayWithOtherCard: 'Bayar dengan kartu lain',
	PayYearly: 'Bayar Tahunan',
	PayYearlyPercentOff: 'Bayar tahunan <mark>{percent}% off</mark>',
	Payer: 'Pembayar',
	PayerClaimId: 'ID klaim Pembayar',
	PayerCoverage: 'Cakupan',
	PayerDetails: 'Rincian pembayar',
	PayerDetailsDescription: 'Lihat rincian pembayar yang telah ditambahkan ke akun Anda dan kelola pendaftaran.',
	PayerID: 'ID Pembayar',
	PayerId: 'ID Pembayar',
	PayerName: 'Nama pembayar',
	PayerPhoneNumber: 'Nomor telepon pembayar',
	Payers: 'Pembayar',
	Payment: 'Pembayaran',
	PaymentAccountUpdated: 'Akun Anda telah diperbarui!',
	PaymentAccountUpgraded: 'Akun Anda telah ditingkatkan!',
	PaymentAmount: 'Jumlah pembayaran',
	PaymentDate: 'Tanggal pembayaran',
	PaymentDetails: 'Detail Pembayaran',
	PaymentForUsersPerMonth: 'Pembayaran untuk {billedUsers, plural, one {# pengguna} other {# pengguna}} sebulan',
	PaymentInfoFormPrimaryText: 'Informasi pembayaran',
	PaymentInfoFormSecondaryText: 'Kumpulkan rincian pembayaran',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Faktur ini telah dibayar.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Faktur ini sudah diproses.',
	PaymentIntentAmountMismatchSnackbar:
		'Jumlah total faktur telah diubah. Harap tinjau perubahan tersebut sebelum membayar.',
	PaymentIntentSyncTimeoutSnackbar:
		'Pembayaran Anda berhasil tetapi terjadi batas waktu. Harap segarkan halaman dan jika pembayaran Anda tidak ditampilkan, silakan hubungi bagian dukungan.',
	PaymentMethod: 'Metode pembayaran',
	PaymentMethodDescription:
		'Tambahkan dan kelola metode pembayaran praktik Anda untuk menyederhanakan proses penagihan langganan Anda.',
	PaymentMethodLabelBank: 'rekening bank',
	PaymentMethodLabelCard: 'kartu',
	PaymentMethodLabelFallback: 'metode pembayaran',
	PaymentMethodRequired: 'Harap tambahkan metode pembayaran sebelum mengubah langganan',
	PaymentMethods: 'Metode pembayaran',
	PaymentProcessing: 'Pemrosesan pembayaran!',
	PaymentProcessingFee: 'Pembayaran termasuk biaya pemrosesan {amount}',
	PaymentReports: 'Laporan pembayaran (ERA)',
	PaymentSettings: 'Pengaturan pembayaran',
	PaymentSuccessful: 'Pembayaran berhasil!',
	PaymentType: 'Jenis pembayaran',
	Payments: 'Pembayaran',
	PaymentsAccountDisabledNotificationSubject: `Pembayaran online melalui {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} telah dinonaktifkan.
Silakan periksa pengaturan pembayaran Anda untuk mengaktifkan pembayaran.`,
	PaymentsEmptyStateDescription: 'Tidak ada pembayaran yang ditemukan.',
	PaymentsUnallocated: 'Pembayaran yang tidak dialokasikan',
	PayoutDate: 'Tanggal pembayaran',
	PayoutsDisabled: 'Pembayaran dinonaktifkan',
	PayoutsEnabled: 'Pembayaran diaktifkan',
	PayoutsStatus: 'Status pembayaran',
	Pediatrician: 'Dokter Anak',
	Pen: 'Pena',
	Pending: 'Tertunda',
	People: '{rosterSize } orang',
	PeopleCount: 'Orang ({count})',
	PerMonth: '/ Bulan',
	PerUser: 'Per pengguna',
	Permission: 'Izin',
	PermissionRequired: 'Diperlukan izin',
	Permissions: 'Izin',
	PermissionsClientAndContactDocumentation: 'Klien ',
	PermissionsClientAndContactProfiles: 'Klien ',
	PermissionsEditAccess: 'Edit akses',
	PermissionsInvoicesAndPayments: 'Faktur ',
	PermissionsScheduling: 'Penjadwalan',
	PermissionsUnassignClients: 'Batalkan penetapan klien',
	PermissionsUnassignClientsConfirmation: 'Apakah Anda yakin ingin membatalkan penetapan klien ini?',
	PermissionsValuesAssigned: 'Hanya ditugaskan',
	PermissionsValuesEverything: 'Semuanya',
	PermissionsValuesNone: 'Tidak ada',
	PermissionsValuesOwnCalendar: 'Kalender sendiri',
	PermissionsViewAccess: 'Lihat akses',
	PermissionsWorkspaceSettings: 'Pengaturan ruang kerja',
	Person: '{rosterSize} orang',
	PersonalDetails: 'Detail pribadi',
	PersonalHealthcareHistoryStoreDescription:
		'Jawab dan simpan riwayat kesehatan pribadi Anda dengan aman di satu tempat',
	PersonalTrainer: 'Pelatih Pribadi',
	PersonalTraining: 'Pelatihan Pribadi',
	PersonalizeWorkspace: '<h1>Personalisasi ruang kerja Anda</h1>',
	PersonalizingYourWorkspace: 'Personalisasi ruang kerja Anda',
	Pharmacist: 'Apoteker',
	Pharmacy: 'Farmasi',
	PhoneCall: 'Panggilan telepon',
	PhoneNumber: 'Nomor telepon',
	PhoneNumberOptional: 'Nomor telepon (opsional)',
	PhotoBy: 'Foto oleh',
	PhysicalAddress: 'Alamat fisik',
	PhysicalTherapist: 'Terapis Fisik',
	PhysicalTherapists: 'Terapis Fisik',
	PhysicalTherapy: 'Terapi fisik',
	Physician: 'Dokter',
	PhysicianAssistant: 'Asisten Dokter',
	Physicians: 'Dokter',
	Physiotherapist: 'Fisioterapis',
	PlaceOfService: 'Tempat pelayanan',
	Plan: 'Rencana',
	PlanAndReport: 'Rencana/Laporan',
	PlanId: 'ID Rencana',
	PlansAndReportsCategoryDescription: 'Untuk perencanaan pengobatan dan merangkum hasil',
	PleaseRefreshThisPageToTryAgain: 'Silakan segarkan halaman ini untuk mencoba lagi.',
	PleaseWait: 'Harap tunggu...',
	PleaseWaitForHostToJoin: 'Menunggu Host untuk bergabung...',
	PleaseWaitForHostToStart: 'Harap menunggu Tuan Rumah untuk memulai rapat ini.',
	PlusAdd: '+ Tambah',
	PlusOthers: '+{count} lainnya',
	PlusPlanInclusionFive: 'Kotak masuk bersama',
	PlusPlanInclusionFour: 'Panggilan video grup',
	PlusPlanInclusionHeader: 'Segala sesuatu di Essential  ',
	PlusPlanInclusionOne: 'AI Tanpa Batas',
	PlusPlanInclusionSix: 'Merek khusus',
	PlusPlanInclusionThree: 'Penjadwalan kelompok',
	PlusPlanInclusionTwo: 'Penyimpanan tak terbatas ',
	PlusSubscriptionPlanSubtitle: 'Untuk praktik yang mengoptimalkan dan berkembang',
	PlusSubscriptionPlanTitle: 'Plus',
	PoliceOfficer: 'Petugas Polisi',
	PolicyDates: 'Tanggal kebijakan',
	PolicyHolder: 'Pemegang polis',
	PolicyHoldersAddress: 'Alamat pemegang polis',
	PolicyMemberId: 'ID Anggota Kebijakan',
	PolicyStatus: 'Status kebijakan',
	Popular: 'Populer',
	PortalAccess: 'Akses portal',
	PortalNoAppointmentsHeading: 'Pantau semua janji temu yang akan datang dan yang telah lewat',
	PortalNoDocumentationHeading: 'Buat dan simpan dokumen Anda dengan aman',
	PortalNoRelationshipsHeading: 'Satukan mereka yang mendukung perjalanan Anda',
	PosCodeErrorMessage: 'Kode POS diperlukan',
	PosoNumber: 'Nomor PO/SO',
	PossibleClientDuplicate: 'Kemungkinan klien duplikat',
	PotentialClientDuplicateTitle: 'Potensi duplikasi catatan klien',
	PotentialClientDuplicateWarning:
		'Informasi klien ini mungkin sudah ada dalam daftar klien Anda. Harap verifikasi dan perbarui catatan yang ada jika perlu atau lanjutkan untuk membuat klien baru.',
	PoweredBy: 'Didukung oleh',
	Practice: 'Praktik',
	PracticeDetails: 'Detail latihan',
	PracticeInfoHeader: 'Informasi bisnis',
	PracticeInfoPlaceholder: `Nama praktik,
 Pengidentifikasi penyedia nasional,
 Nomor identifikasi pemberi kerja`,
	PracticeLocation: 'Sepertinya latihan Anda ada di',
	PracticeSettingsAvailabilityTab: 'Tersedianya',
	PracticeSettingsBillingTab: 'Pengaturan penagihan',
	PracticeSettingsClientSettingsTab: 'Pengaturan klien',
	PracticeSettingsGeneralTab: 'Umum',
	PracticeSettingsOnlineBookingTab: 'Pemesanan online',
	PracticeSettingsServicesTab: 'Layanan',
	PracticeSettingsTaxRatesTab: 'Tarif pajak',
	PracticeTemplate: 'Template Praktik',
	Practitioner: 'Praktisi',
	PreferredLanguage: 'Bahasa pilihan',
	PreferredName: 'Nama yang disukai',
	Prescription: 'Resep',
	PreventionSpecialist: 'Spesialis Pencegahan',
	Preview: 'Pratinjau',
	PreviewAndSend: 'Pratinjau dan kirim',
	PreviewUnavailable: 'Pratinjau tidak tersedia untuk jenis file ini',
	PreviousNotes: 'Catatan sebelumnya',
	Price: 'Harga',
	PriceError: 'Harga harus lebih besar dari 0',
	PricePerClient: 'Harga per klien',
	PricePerUser: 'Per pengguna',
	PricePerUserBilledAnnually: 'Per pengguna ditagih setiap tahun',
	PricePerUserPerPeriod: '{price} per pengguna / {isMonthly, select, true {bulan} other {tahun}}',
	PricingGuide: 'Panduan untuk paket harga',
	PricingPlanPerMonth: '/ bulan',
	PricingPlanPerYear: '/ tahun',
	Primary: 'Utama',
	PrimaryInsurance: 'Asuransi primer',
	PrimaryPolicy: 'Asuransi primer',
	PrimaryTimezone: 'Zona waktu utama',
	Print: 'Mencetak',
	PrintToCms1500: 'Cetak ke CMS1500',
	PrivatePracticeConsultant: 'Konsultan Praktik Swasta',
	Proceed: 'Lanjutkan',
	ProcessAtTimeOfBookingDesc: 'Klien harus membayar harga layanan penuh untuk memesan secara online',
	ProcessAtTimeOfBookingLabel: 'Memproses pembayaran pada saat pemesanan',
	Processing: 'Pengolahan',
	ProcessingFee: 'Biaya pemrosesan',
	ProcessingFeeToolTip: `Carepatron memungkinkan Anda untuk membebankan biaya pemrosesan kepada pelanggan Anda.
 Di beberapa wilayah hukum, mengenakan biaya pemrosesan kepada pelanggan Anda adalah hal yang dilarang. Anda bertanggung jawab untuk mematuhi hukum yang berlaku.`,
	ProcessingRequest: 'Sedang memproses permintaan...',
	Product: 'Produk',
	Profession: 'Profesi',
	ProfessionExample: 'Terapis, Ahli Gizi, Dokter Gigi',
	ProfessionPlaceholder: 'Mulailah mengetik profesi Anda atau pilih dari daftar',
	ProfessionalPlanInclusion1: 'Penyimpanan tak terbatas',
	ProfessionalPlanInclusion2: 'Tugas tak terbatas',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Dukungan pelanggan 24/7',
	ProfessionalPlanInclusion5: 'pengingat SMS',
	ProfessionalPlanInclusionHeader: 'Semua yang ada di Starter, plus...',
	Professions: 'Profesi',
	Profile: 'Profil',
	ProfilePhotoFileSizeLimit: 'Batasan ukuran file 5MB',
	ProfilePopoverSubTitle: 'Anda masuk sebagai <strong>{email}</strong>',
	ProfilePopoverTitle: 'Ruang kerja Anda',
	PromoCode: 'Kode promo',
	PromotionCodeApplied: '{promo} diterapkan',
	ProposeNewDateTime: 'Usulkan tanggal/waktu baru',
	Prosthetist: 'Dokter Prostetis',
	Provider: 'Penyedia',
	ProviderBillingPlanExpansionManageButton: 'Kelola rencana',
	ProviderCommercialNumber: 'Nomor komersial penyedia',
	ProviderDetails: 'Rincian penyedia',
	ProviderDetailsAddress: 'Alamat',
	ProviderDetailsName: 'Nama',
	ProviderDetailsPhoneNumber: 'Nomor telepon',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'Maaf, penyedia ini sudah memiliki akun penagihan',
	ProviderInfoPlaceholder: `Nama staf,
 Alamat email,
 Nomor telepon,
 Pengidentifikasi penyedia nasional,
 Nomor lisensi`,
	ProviderIsChargedProcessingFee: 'Anda akan membayar biaya pemrosesan',
	ProviderPaymentFormBackButton: 'Kembali',
	ProviderPaymentFormBillingAddressCity: 'Kota',
	ProviderPaymentFormBillingAddressCountry: 'Negara',
	ProviderPaymentFormBillingAddressLine1: 'Baris 1',
	ProviderPaymentFormBillingAddressPostalCode: 'kode Pos',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Rincian kartu kredit',
	ProviderPaymentFormCardExpiry: 'Kedaluwarsa',
	ProviderPaymentFormCardHolderAddressTitle: 'Alamat',
	ProviderPaymentFormCardHolderName: 'Nama pemegang kartu',
	ProviderPaymentFormCardHolderTitle: 'Detail pemegang kartu',
	ProviderPaymentFormCardNumber: 'Nomor kartu',
	ProviderPaymentFormPlanTitle: 'Rencana yang dipilih',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}):',
	ProviderPaymentFormSaveButton: 'Simpan langganan',
	ProviderPaymentFreePlanDescription:
		'Memilih paket gratis akan menghilangkan akses setiap anggota staf ke klien mereka di penyedia layanan Anda. Namun, akses Anda akan tetap ada dan Anda masih dapat menggunakan platform tersebut.',
	ProviderPaymentStepName: 'Tinjauan ',
	ProviderPaymentSuccessSnackbar: 'Bagus! Paket baru Anda berhasil disimpan.',
	ProviderPaymentTitle: 'Tinjauan ',
	ProviderPlanNetworkIdentificationNumber: 'Nomor identifikasi jaringan rencana penyedia',
	ProviderRemindersSettingsBannerAction: 'Pergi ke Manajemen Alur Kerja',
	ProviderRemindersSettingsBannerDescription:
		'Temukan semua pengingat di bawah tab baru **Workflow Management** di **Settings**. Pembaruan ini menghadirkan fitur baru yang hebat, templating yang ditingkatkan, dan alat otomatisasi yang lebih pintar untuk meningkatkan produktivitas Anda. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Pengalaman pengingat Anda semakin baik',
	ProviderTaxonomy: 'Taksonomi penyedia',
	ProviderUPINNumber: 'Nomor UPIN penyedia',
	ProviderUsedStoragePercentage: '{providerName} penyimpanan {usedStoragePercentage}% penuh!',
	PsychiatricNursePractitioner: 'Perawat Praktisi Psikiatri',
	Psychiatrist: 'Psikiater',
	Psychiatrists: 'Psikiater',
	Psychiatry: 'Psikiatri',
	Psychoanalyst: 'Psikoanalis',
	Psychologist: 'Psikolog',
	Psychologists: 'Psikolog',
	Psychology: 'Psikologi',
	Psychometrician: 'Psikometrika',
	PsychosocialRehabilitationSpecialist: 'Spesialis Rehabilitasi Psikososial',
	Psychotheraphy: 'Psikoterapi',
	Psychotherapists: 'Psikoterapis',
	Psychotherapy: 'Psikoterapi',
	PublicCallDialogTitle: 'Panggilan video dengan ',
	PublicCallDialogTitlePlaceholder: 'Panggilan video didukung oleh Carepatron',
	PublicFormBackToForm: 'Kirimkan tanggapan lain',
	PublicFormConfirmSubmissionHeader: 'Konfirmasi Pengiriman',
	PublicFormNotFoundDescription:
		'Formulir yang Anda cari mungkin telah dihapus atau tautannya mungkin salah. Harap periksa URL dan coba lagi.',
	PublicFormNotFoundTitle: 'Form tidak ditemukan',
	PublicFormSubmissionError: 'Pengiriman gagal. Silakan coba lagi.',
	PublicFormSubmissionSuccess: 'Formulir berhasil dikirim',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} mengirimkan formulir publik {noteTitle}',
	PublicFormSubmittedSubtitle: 'Kiriman Anda telah diterima.',
	PublicFormSubmittedTitle: 'Terima kasih!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Kami telah mengirimkan kode konfirmasi ke email Anda',
	PublicFormsInvalidConfirmationCode: 'Kode konfirmasi tidak valid',
	PublicHealthInspector: 'Inspektur Kesehatan Masyarakat',
	PublicTemplates: 'Templat publik',
	Publish: 'Menerbitkan',
	PublishTemplate: 'Publikasikan templat',
	PublishTemplateFeatureBannerSubheader: 'Template yang dirancang untuk memberi manfaat bagi masyarakat',
	PublishTemplateHeader: 'Publikasikan {title}',
	PublishTemplateToCommunity: 'Publikasikan templat ke komunitas',
	PublishToCommunity: 'Publikasikan ke komunitas',
	PublishToCommunitySuccessMessage: 'Berhasil dipublikasikan ke komunitas',
	Published: 'Diterbitkan',
	PublishedBy: 'Diterbitkan oleh {name}',
	PublishedNotesAreNotAutosaved: 'Catatan yang dipublikasikan tidak akan disimpan secara otomatis',
	PublishedOnCarepatronCommunity: 'Diterbitkan di komunitas Carepatron',
	Purchase: 'Pembelian',
	PushToCalendar: 'Dorong ke kalender',
	Question: 'Pertanyaan',
	QuestionOrTitle: 'Pertanyaan atau judul',
	QuickActions: 'Tindakan cepat',
	QuickThemeSwitcherColorBasil: 'Basil',
	QuickThemeSwitcherColorBlueberry: 'Blueberry',
	QuickThemeSwitcherColorFushcia: 'Fushcia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Lumut',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Terapis Radiasi',
	Radiologist: 'Radiolog',
	Read: 'Membaca',
	ReadOnly: 'Hanya Baca',
	ReadOnlyAppointment: 'Janji Temu Hanya Baca',
	ReadOnlyEventBanner: 'Janji temu ini disinkronkan dari kalender baca saja dan tidak dapat diedit.',
	ReaderMaxDepthHasBeenExceededCode: 'Catatan terlalu bertumpuk. Coba hilangkan indentasi beberapa item.',
	ReadyForMapping: 'Siap untuk pemetaan',
	RealEstateAgent: 'Agen Properti',
	RearrangeClientFields: 'Atur ulang bidang klien dalam pengaturan klien',
	Reason: 'Alasan',
	ReasonForChange: 'Alasan untuk perubahan',
	RecentAppointments: 'Penunjukan terkini',
	RecentServices: 'Layanan Terbaru',
	RecentTemplates: 'Template terbaru',
	RecentlyUsed: 'Baru-baru ini digunakan',
	Recommended: 'Direkomendasikan',
	RecommendedTemplates: 'Template yang direkomendasikan',
	Recording: 'Rekaman',
	RecordingEnded: 'Rekaman berakhir',
	RecordingInProgress: 'Rekaman sedang berlangsung',
	RecordingMicrophoneAccessErrorMessage:
		'Harap izinkan akses mikrofon di peramban Anda dan segarkan untuk mulai merekam.',
	RecurrenceCount: ', {count, plural, one {sekali} other {# kali}}',
	RecurrenceDaily: '{count, plural, one {Harian} other {Hari}}',
	RecurrenceEndAfter: 'Setelah',
	RecurrenceEndNever: 'Tidak pernah',
	RecurrenceEndOn: 'Di',
	RecurrenceEvery: 'Setiap {description}',
	RecurrenceMonthly: '{count, plural, one {Bulanan} other {Bulan}}',
	RecurrenceOn: 'pada {description}',
	RecurrenceOnAllDays: 'pada semua hari',
	RecurrenceUntil: 'sampai {description}',
	RecurrenceWeekly: '{count, plural, one {Mingguan} other {Minggu}}',
	RecurrenceYearly: '{count, plural, one {Tahunan} other {Tahun}}',
	Recurring: 'Berulang',
	RecurringAppointment: 'Janji temu berulang',
	RecurringAppointmentsLimitedBannerText:
		'Tidak semua janji temu berulang ditampilkan. Kurangi rentang tanggal untuk melihat semua janji temu berulang untuk periode tersebut.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# peristiwa} other {# Peristiwa}}</b> akan dibuat pada tanggal-tanggal berikut',
	Redo: 'Mengulangi',
	ReferFriends: 'Rekomendasikan ke teman',
	Reference: 'Referensi',
	ReferralCreditedNotificationSubject: 'Kredit referral Anda sebesar {currency} {amount} telah diterapkan',
	ReferralEmailDefaultBody: `Terima kasih kepada {name}, Anda telah dikirim peningkatan GRATIS 3 bulan ke Carepatron. Bergabunglah dengan komunitas kami yang terdiri dari lebih dari 3 juta praktisi kesehatan yang dibangun untuk cara kerja baru!
Terima kasih,
Tim Carepatron`,
	ReferralEmailDefaultSubject: 'Anda telah diundang untuk bergabung dengan Carepatron',
	ReferralHasNotSignedUpDescription: 'Teman Anda belum mendaftar',
	ReferralHasSignedUpDescription: 'Teman Anda telah mendaftar.',
	ReferralInformation: 'Informasi rujukan',
	ReferralJoinedNotificationSubject: '{actorProfileName} telah bergabung dengan Carepatron',
	ReferralListErrorDescription: 'Daftar rujukan tidak dapat dimuat.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {bulan} other {bulan}}</b> aktif',
	ReferralRewardBanner: 'Daftar dan klaim hadiah referensi Anda!',
	Referrals: 'Rujukan',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} bulan {percentOff, select, 100 {gratis berbayar} other {{percentOff}% diskon}} {type, select, SubscriptionUpgrade {peningkatan} other {}}',
	ReferredUserBenefitTitle: 'Mereka mengerti!',
	Referrer: 'Pengarah',
	ReferringProvider: 'Penyedia rujukan',
	ReferringUserBenefitSubtitle: 'Kredit USD${creditAmount} ketika <mark>3 teman</mark> diaktifkan.',
	ReferringUserBenefitTitle: 'Kamu dapat!',
	RefreshPage: 'Refresh PageSegarkan Halaman',
	Refund: 'Pengembalian dana',
	RefundAcknowledgement: 'Saya telah mengembalikan dana {clientName} di luar Carepatron',
	RefundAcknowledgementValidationMessage: 'Harap konfirmasi bahwa Anda telah mengembalikan jumlah ini',
	RefundAmount: 'Jumlah pengembalian dana',
	RefundContent:
		'Pengembalian dana memerlukan waktu 7-10 hari untuk muncul di akun klien Anda. Biaya pembayaran tidak akan dikembalikan, tetapi tidak ada biaya tambahan untuk pengembalian dana. Pengembalian dana tidak dapat dibatalkan, dan beberapa mungkin perlu ditinjau sebelum diproses.',
	RefundCouldNotBeProcessed: 'Pengembalian dana tidak dapat diproses',
	RefundError:
		'Pengembalian dana ini tidak dapat diproses secara otomatis saat ini. Silakan hubungi dukungan Carepatron untuk meminta pengembalian dana pembayaran ini.',
	RefundExceedTotalValidationError: 'Jumlahnya tidak boleh melebihi total yang dibayarkan',
	RefundFailed: 'Pengembalian dana gagal',
	RefundFailedTooltip:
		'Pengembalian dana pembayaran ini sebelumnya gagal dan tidak dapat dicoba lagi. Silakan hubungi dukungan.',
	RefundNonStripePaymentContent:
		'Pembayaran ini dilakukan menggunakan metode di luar Carepatron (misalnya, tunai, internet banking). Mengeluarkan pengembalian dana melalui Carepatron tidak akan mengembalikan dana apa pun kepada klien.',
	RefundReasonDescription: 'Menambahkan alasan pengembalian dana dapat membantu saat meninjau transaksi klien Anda',
	Refunded: 'Dikembalikan dananya',
	Refunds: 'Pengembalian uang',
	RefundsTableEmptyState: 'Tidak ditemukan pengembalian dana',
	Regenerate: 'Buat Ulang',
	RegisterButton: 'Daftar',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Nama depan',
	RegisterLastName: 'Nama belakang',
	RegisterPassword: 'Kata sandi',
	RegisteredNurse: 'Mantri kesehatan',
	RehabilitationCounselor: 'Konselor Rehabilitasi',
	RejectAppointmentFormTitle: 'Tidak bisa hadir? Mohon beri tahu kami alasannya dan usulkan waktu baru.',
	Rejected: 'Ditolak',
	Relationship: 'Hubungan',
	RelationshipDetails: 'Detail hubungan',
	RelationshipEmptyStateTitle: 'Tetap terhubung dengan mereka yang mendukung klien Anda',
	RelationshipPageAccessTypeColumnName: 'Akses profil',
	RelationshipSavedSuccessSnackbar: 'Hubungan berhasil disimpan!',
	RelationshipSelectorFamilyAdmin: 'Keluarga',
	RelationshipSelectorFamilyMember: 'Anggota keluarga',
	RelationshipSelectorProviderAdmin: 'Administrator penyedia',
	RelationshipSelectorProviderStaff: 'Staf penyedia',
	RelationshipSelectorSupportNetworkPrimary: 'Teman',
	RelationshipSelectorSupportNetworkSecondary: 'Jaringan dukungan',
	RelationshipStatus: 'Status hubungan',
	RelationshipType: 'Tipe hubungan',
	RelationshipTypeClientOwner: 'Klien',
	RelationshipTypeFamilyAdmin: 'Hubungan',
	RelationshipTypeFamilyMember: 'Keluarga',
	RelationshipTypeFriendOrSupport: 'Teman atau jaringan dukungan',
	RelationshipTypeProviderAdmin: 'Admin penyedia',
	RelationshipTypeProviderStaff: 'Staf',
	RelationshipTypeSelectorPlaceholder: 'Cari jenis hubungan',
	Relationships: 'Hubungan',
	Remaining: 'tersisa',
	RemainingTime: '{time} tersisa',
	Reminder: 'Pengingat',
	ReminderColor: 'Pengingat warna',
	ReminderDetails: 'Detail pengingat',
	ReminderEditDisclaimer: 'Perubahan hanya akan terlihat pada penunjukan baru',
	ReminderSettings: 'Pengaturan pengingat janji temu',
	Reminders: 'Pengingat',
	Remove: 'Menghapus',
	RemoveAccess: 'Hapus akses',
	RemoveAllGuidesBtn: 'Hapus semua panduan',
	RemoveAllGuidesPopoverBody:
		'Setelah Anda selesai dengan panduan orientasi, cukup gunakan tombol hapus panduan di setiap panel.',
	RemoveAllGuidesPopoverTitle: 'Tidak lagi memerlukan panduan orientasi Anda?',
	RemoveAsDefault: 'Hapus sebagai default',
	RemoveAsIntake: 'Hapus sebagai asupan',
	RemoveCol: 'Hapus kolom',
	RemoveColor: 'Hapus warna',
	RemoveField: 'Hapus bidang',
	RemoveFromCall: 'Hapus dari panggilan',
	RemoveFromCallDescription:
		'Apakah Anda yakin ingin menghapus <mark>{attendeeName}</mark> dari panggilan video ini?',
	RemoveFromCollection: 'Hapus dari koleksi',
	RemoveFromCommunity: 'Hapus dari komunitas',
	RemoveFromFolder: 'Hapus dari folder',
	RemoveFromFolderConfirmationDescription:
		'Apakah Anda yakin ingin menghapus template ini dari folder ini? Tindakan ini tidak dapat dibatalkan, tetapi Anda dapat memilih untuk memindahkannya kembali nanti.',
	RemoveFromIntakeDefault: 'Hapus dari asupan default',
	RemoveGuides: 'Hapus panduan',
	RemoveMfaConfirmationDescription:
		'Menghapus Multi-Factor Authentication (MFA) akan mengurangi keamanan akun Anda. Apakah Anda ingin melanjutkan?',
	RemoveMfaConfirmationTitle: 'Hapus MFA?',
	RemovePaymentMethodDescription: `Ini akan menghapus semua akses dan penggunaan metode pembayaran ini di masa mendatang.
 Tindakan ini tidak dapat dibatalkan.`,
	RemoveRow: 'Hapus baris',
	RemoveTable: 'Hapus tabel',
	RemoveTemplateAsDefaultIntakeSuccess: 'Berhasil menghapus {templateTitle} sebagai template intake default',
	RemoveTemplateFromCommunity: 'Hapus templat dari komunitas',
	RemoveTemplateFromFolder: '{templateTitle} berhasil dihapus dari {folderTitle}',
	Rename: 'Ganti nama',
	RenderingProvider: 'Penyedia rendering',
	Reopen: 'Buka kembali',
	ReorderServiceGroupFailure: 'Gagal menyusun ulang koleksi',
	ReorderServiceGroupSuccess: 'Berhasil menyusun ulang koleksi',
	ReorderServicesFailure: 'Gagal memesan ulang layanan',
	ReorderServicesSuccess: 'Berhasil memesan ulang layanan',
	ReorderYourServiceList: 'Susun ulang daftar layanan Anda',
	ReorderYourServiceListDescription:
		'Cara Anda mengatur layanan dan koleksi akan tercermin pada halaman pemesanan online Anda agar semua klien Anda dapat melihatnya!',
	RepeatEvery: 'Ulangi setiap',
	RepeatOn: 'Ulangi pada',
	Repeating: 'Mengulang',
	Repeats: 'Mengulang',
	RepeatsEvery: 'Berulang setiap',
	Rephrase: 'Mengulang kembali',
	Replace: 'Mengganti',
	ReplaceBackground: 'Ganti latar belakang',
	ReplacementOfPriorClaim: 'Penggantian klaim sebelumnya',
	Report: 'Laporan',
	Reprocess: 'Proses ulang',
	RepublishTemplateToCommunity: 'Terbitkan kembali templat ke komunitas',
	RequestANewVerificationLink: 'Minta tautan verifikasi baru',
	RequestCoverageReport: 'Minta laporan cakupan',
	RequestingDevicePermissions: 'Meminta izin perangkat...',
	RequirePaymentMethodDesc: 'Klien harus memasukkan rincian kartu kredit mereka untuk memesan secara online',
	RequirePaymentMethodLabel: 'Memerlukan rincian kartu kredit',
	Required: 'diperlukan',
	RequiredField: 'Diperlukan',
	RequiredUrl: 'URL dibutuhkan.',
	Reschedule: 'Menjadwalkan ulang',
	RescheduleBookingLinkModalDescription:
		'Klien Anda dapat mengubah tanggal dan waktu janji temu menggunakan tautan ini.',
	RescheduleBookingLinkModalTitle: 'Tautan penjadwalan ulang pemesanan',
	RescheduleLink: 'Tautan penjadwalan ulang',
	Resend: 'Kirim ulang',
	ResendConfirmationCode: 'Kirim ulang kode konfirmasi',
	ResendConfirmationCodeDescription:
		'Silakan masukkan alamat email Anda dan kami akan mengirimkan kode konfirmasi lainnya melalui email',
	ResendConfirmationCodeSuccess: 'Kode konfirmasi telah dikirim ulang, silakan periksa kotak masuk Anda',
	ResendNewEmailVerificationSuccess: 'Tautan verifikasi baru telah dikirim ke {email}',
	ResendVerificationEmail: 'Kirim ulang email verifikasi',
	Reset: 'Mengatur ulang',
	Resources: 'Sumber Daya',
	RespiratoryTherapist: 'Terapis Pernapasan',
	RespondToHistoricAppointmentError:
		'Ini adalah pertemuan bersejarah, silakan hubungi praktisi Anda jika Anda memiliki pertanyaan.',
	Responder: 'Penanggap',
	RestorableItemModalDescription:
		'Apakah Anda yakin ingin menghapus {context}?{canRestore, select, true { Anda dapat memulihkannya nanti.} other {}}',
	RestorableItemModalTitle: 'Hapus {type}',
	Restore: 'Memulihkan',
	RestoreAll: 'Pulihkan semuanya',
	Restricted: 'Terbatas',
	ResubmissionCodeReferenceNumber: 'Kode pengajuan ulang dan nomor referensi',
	Resubmit: 'Kirim ulang',
	Resume: 'Melanjutkan',
	Retry: 'Mencoba kembali',
	RetryingConnectionAttempt: 'Mencoba kembali koneksi... (Percobaan {retryCount} dari {maxRetries})',
	ReturnToForm: 'Kembali ke bentuk semula',
	RevertClaimStatus: 'Kembalikan status klaim',
	RevertClaimStatusDescriptionBody:
		'Klaim ini telah ditautkan ke pembayaran, dan mengubah statusnya dapat memengaruhi pelacakan atau pemrosesan pembayaran, yang dapat memerlukan rekonsiliasi manual.',
	RevertClaimStatusDescriptionTitle: 'Apakah Anda yakin ingin kembali ke {status}?',
	RevertClaimStatusError: 'Gagal mengembalikan status klaim',
	RevertToDraft: 'Kembali ke draf',
	Review: 'Tinjauan',
	ReviewsFirstQuote: 'Janji temu di mana saja, kapan saja',
	ReviewsSecondJobTitle: 'Klinik Lifehouse',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Saya juga menyukai aplikasi carepatron. Aplikasi ini membantu saya memantau klien dan pekerjaan saya saat bepergian.',
	ReviewsThirdJobTitle: 'Klinik Teluk Manila',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'Kemudahan navigasi dan antarmuka pengguna yang indah membuat saya tersenyum setiap hari.',
	RightAlign: 'Rata kanan',
	Role: 'Peran',
	Roster: 'Peserta',
	RunInBackground: 'Jalankan di latar belakang',
	SMS: 'Pesan singkat',
	SMSAndEmailReminder: 'Pesan singkat ',
	SSN: 'Nomor Jaminan Sosial (SSN)',
	SafetyRedirectHeading: 'Anda meninggalkan Carepatron',
	SafetyRedirectSubtext: 'Jika Anda percaya tautan ini, pilih untuk melanjutkan',
	SalesRepresentative: 'Perwakilan Penjualan',
	SalesTax: 'Pajak penjualan',
	SalesTaxHelp: 'Termasuk pajak penjualan pada faktur yang dibuat',
	SalesTaxIncluded: 'Ya',
	SalesTaxNotIncluded: 'TIDAK',
	SaoPaulo: 'Sao Paulo, Brasil',
	Saturday: 'Sabtu',
	Save: 'Menyimpan',
	SaveAndClose: 'Menyimpan ',
	SaveAndExit: 'Menyimpan ',
	SaveAndLock: 'Simpan dan kunci',
	SaveAsDraft: 'Simpan sebagai draf',
	SaveCardForFuturePayments: 'Simpan kartu untuk pembayaran di masa mendatang',
	SaveChanges: 'Simpan perubahan',
	SaveCollection: 'Simpan Koleksi',
	SaveField: 'Simpan bidang',
	SavePaymentMethod: 'Simpan metode pembayaran',
	SavePaymentMethodDescription: 'Anda tidak akan dikenakan biaya sampai janji temu pertama Anda.',
	SavePaymentMethodSetupError:
		'Terjadi kesalahan tak terduga dan kami tidak dapat mengonfigurasi pembayaran saat ini.',
	SavePaymentMethodSetupInvoiceLater: 'Pembayaran dapat diatur dan disimpan saat membayar faktur pertama Anda.',
	SaveSection: 'Simpan bagian',
	SaveService: 'Buat layanan baru',
	SaveTemplate: 'Simpan template',
	Saved: 'Tersimpan',
	SavedCards: 'Kartu yang disimpan',
	SavedPaymentMethods: 'Tersimpan',
	Saving: 'Penghematan...',
	ScheduleAppointmentsAndOnlineServices: 'Jadwalkan janji temu dan layanan online',
	ScheduleName: 'Nama jadwal',
	ScheduleNew: 'Jadwal baru',
	ScheduleSend: 'Jadwalkan pengiriman',
	ScheduleSendAlertInfo: 'Percakapan yang terjadwal akan dikirim pada waktu yang dijadwalkan.',
	ScheduleSendByName: '**Jadwal kirim** • {time} oleh {displayName}',
	ScheduleSetupCall: 'Jadwalkan Panggilan Persiapan',
	Scheduled: 'Dijadwalkan',
	SchedulingSend: 'Penjadwalan pengiriman',
	School: 'Sekolah',
	ScrollToTop: 'Gulir ke atas',
	Search: 'Mencari',
	SearchAndConvertToLanguage: 'Cari dan konversi ke bahasa',
	SearchBasicBlocks: 'Cari blok dasar',
	SearchByName: 'Pencarian berdasarkan nama',
	SearchClaims: 'Cari klaim',
	SearchClientFields: 'Cari bidang klien',
	SearchClients: 'Cari berdasarkan nama klien, ID klien atau nomor telepon',
	SearchCommandNotFound: 'Tidak ada hasil yang ditemukan untuk "{searchTerm}"',
	SearchContacts: 'Klien atau kontak',
	SearchContactsPlaceholder: 'Pencarian kontak',
	SearchConversations: 'Cari percakapan',
	SearchInputPlaceholder: 'Cari semua sumber daya',
	SearchInvoiceNumber: 'Cari nomor faktur',
	SearchInvoices: 'Cari faktur',
	SearchMultipleContacts: 'Klien atau kontak',
	SearchMultipleContactsOptional: 'Klien atau kontak (opsional)',
	SearchOrCreateATag: 'Cari atau buat tag',
	SearchPayments: 'Pencarian pembayaran',
	SearchPrepopulatedData: 'Cari bidang data yang sudah diisi sebelumnya',
	SearchRelationships: 'Pencarian hubungan',
	SearchRemindersAndWorkflows: 'Pengingat pencarian dan alur kerja',
	SearchServices: 'Layanan Pencarian',
	SearchTags: 'Tag pencarian',
	SearchTeamMembers: 'Cari anggota tim',
	SearchTemplatePlaceholder: 'Cari {templateCount}+ sumber daya',
	SearchTimezone: 'Cari zona waktu...',
	SearchTrashItems: 'Pencarian item',
	SearchUnsplashPlaceholder: 'Cari foto resolusi tinggi gratis dari Unsplash',
	Secondary: 'Sekunder',
	SecondaryInsurance: 'Asuransi sekunder',
	SecondaryPolicy: 'Asuransi sekunder',
	SecondaryTimezone: 'Zona waktu sekunder',
	Secondly: 'Kedua',
	Section: 'Bagian',
	SectionCannotBeEmpty: 'Suatu bagian harus memiliki setidaknya satu baris',
	SectionFormSecondaryText: 'Judul dan deskripsi bagian',
	SectionName: 'Nama bagian',
	Sections: 'Bagian',
	SeeLess: 'Lihat lebih sedikit',
	SeeLessUpcomingAppointments: 'Lihat lebih sedikit janji temu yang akan datang',
	SeeMore: 'Lihat selengkapnya',
	SeeMoreUpcomingAppointments: 'Lihat lebih banyak janji temu yang akan datang',
	SeeTemplateLibrary: 'Lihat perpustakaan templat',
	Seen: 'Terlihat',
	SeenByName: '<strong>Terlihat</strong> • {time} oleh {displayName}',
	SelectAll: 'Pilih semua',
	SelectAssignees: 'Pilih penerima tugas',
	SelectAttendees: 'Pilih peserta',
	SelectCollection: 'Pilih Koleksi',
	SelectCorrespondingAttributes: 'Pilih atribut yang sesuai',
	SelectPayers: 'Pilih Pembayar',
	SelectProfile: 'Pilih profil',
	SelectServices: 'Pilih layanan',
	SelectTags: 'Pilih Tag',
	SelectTeamOrCommunity: 'Pilih Tim atau Komunitas',
	SelectTemplate: 'Pilih Template',
	SelectType: 'Pilih jenis',
	Selected: 'Terpilih',
	SelfPay: 'Bayar sendiri',
	Send: 'Mengirim',
	SendAndClose: 'Mengirim ',
	SendAndStopIgnore: 'Kirim dan berhenti mengabaikan',
	SendEmail: 'Kirim email',
	SendIntake: 'Kirim asupan',
	SendIntakeAndForms: 'Kirim Asupan ',
	SendMeACopy: 'Kirimkan saya salinannya',
	SendNotificationEmailWarning:
		'Beberapa peserta tidak memiliki alamat email dan tidak akan menerima pemberitahuan dan pengingat otomatis.',
	SendNotificationLabel: 'Pilih peserta yang akan diberitahu dengan email',
	SendOnlinePayment: 'Kirim pembayaran online',
	SendOnlinePaymentTooltipTitleAdmin: 'Harap tambahkan pengaturan pembayaran pilihan Anda',
	SendOnlinePaymentTooltipTitleStaff: 'Silakan minta pemilik penyedia untuk mengatur pembayaran daring.',
	SendPaymentLink: 'Kirim tautan pembayaran',
	SendReaction: 'Kirimkan reaksi',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'Kirim email verifikasi',
	SendingFailed: 'Pengiriman gagal',
	Sent: 'Terkirim',
	SentByName: '**Terkirim** • {time} oleh {displayName}',
	Seoul: 'Seoul',
	SeparateDuplicateClientsDescription:
		'Catatan klien yang dipilih akan tetap terpisah dari yang lain kecuali Anda memilih untuk menggabungkannya',
	Service: 'Melayani',
	'Service/s': 'Layanan/s',
	ServiceAdjustment: 'Penyesuaian layanan',
	ServiceAllowNewClientsIndicator: 'Izinkan klien baru',
	ServiceAlreadyExistsInCollection: 'Layanan sudah ada dalam koleksi',
	ServiceBookableOnlineIndicator: 'Dapat dipesan secara online',
	ServiceCode: 'Kode',
	ServiceCodeErrorMessage: 'Kode layanan diperlukan',
	ServiceCodeSelectorPlaceholder: 'Tambahkan kode layanan',
	ServiceColour: 'Warna layanan',
	ServiceCoverageDescription: 'Pilih layanan yang memenuhi syarat dan bayar bersama untuk polis asuransi ini.',
	ServiceCoverageGoToServices: 'Pergi ke layanan',
	ServiceCoverageNoServicesDescription:
		'Sesuaikan jumlah pembayaran bersama layanan untuk mengganti pembayaran bersama polis default. Nonaktifkan cakupan untuk mencegah layanan diklaim terhadap polis.',
	ServiceCoverageNoServicesLabel: 'Tidak ada layanan yang ditemukan.',
	ServiceCoverageTitle: 'Cakupan layanan',
	ServiceDate: 'Tanggal layanan',
	ServiceDetails: 'Rincian layanan',
	ServiceDuration: 'Lamanya',
	ServiceEmptyState: 'Belum ada layanan',
	ServiceErrorMessage: 'Layanan diperlukan',
	ServiceFacility: 'Fasilitas layanan',
	ServiceName: 'Nama layanan',
	ServiceRate: 'Kecepatan',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} untuk {serviceReceiptNumber, select, undefined {user} other {{clientName}}} membutuhkan informasi tambahan',
	ServiceSalesTax: 'Pajak penjualan',
	ServiceType: 'Melayani',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Tekan muat ulang untuk menyegarkan dan mendapatkan pembaruan Carepatron terbaru.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Muat ulang',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Anda menggunakan versi lama',
	ServiceWorkerForceUIUpdateDialogTitle: 'Selamat Datang kembali!',
	Services: 'Layanan',
	ServicesAndAvailability: 'Layanan ',
	ServicesAndDiagnosisCodesHeader: 'Tambahkan layanan dan kode diagnosis',
	ServicesCount: '{count,plural,=0{Layanan}one{Layanan}other{Layanan}}',
	ServicesPlaceholder: 'Layanan',
	ServicesProvidedBy: 'Layanan/layanan yang disediakan oleh',
	SetAPhysicalAddress: 'Tetapkan alamat fisik',
	SetAVirtualLocation: 'Tetapkan lokasi virtual',
	SetAsDefault: 'Ditetapkan sebagai default',
	SetAsIntake: 'Ditetapkan sebagai asupan',
	SetAsIntakeDefault: 'Ditetapkan sebagai default asupan',
	SetAvailability: 'Tetapkan ketersediaan',
	SetTemplateAsDefaultIntakeSuccess: 'Berhasil menetapkan {templateTitle} sebagai templat intake default',
	SetUpMfaButton: 'Siapkan MFA',
	SetYourLocation: 'Atur Anda<mark> lokasi</mark>',
	SetYourLocationDescription: 'Saya tidak memiliki alamat bisnis <span>(hanya layanan online dan seluler)</span>',
	SettingUpPayers: 'Menetapkan Pembayar',
	Settings: 'Pengaturan',
	SettingsNewUserPasswordDescription:
		'Setelah Anda mendaftar, kami akan mengirimkan kode konfirmasi yang dapat Anda gunakan untuk mengonfirmasi akun Anda',
	SettingsNewUserPasswordTitle: 'Daftar ke Carepatron',
	SettingsTabAutomation: 'Otomatisasi',
	SettingsTabBillingDetails: 'Rincian penagihan',
	SettingsTabConnectedApps: 'Aplikasi yang terhubung',
	SettingsTabCustomFields: 'Bidang khusus',
	SettingsTabDetails: 'Rincian',
	SettingsTabInvoices: 'Faktur',
	SettingsTabLocations: 'Lokasi',
	SettingsTabNotifications: 'Pemberitahuan',
	SettingsTabOnlineBooking: 'Pemesanan Online',
	SettingsTabPayers: 'Pembayar',
	SettingsTabReminders: 'Pengingat',
	SettingsTabServices: 'Layanan',
	SettingsTabServicesAndAvailability: 'Layanan dan ketersediaan',
	SettingsTabSubscriptions: 'Langganan',
	SettingsTabWorkflowAutomations: 'Otomatisasi',
	SettingsTabWorkflowReminders: 'Pengingat dasar',
	SettingsTabWorkflowTemplates: 'Templat',
	Setup: 'Mendirikan',
	SetupGuide: 'Panduan Pengaturan',
	SetupGuideAddServicesActionLabel: 'Mulai',
	SetupGuideAddServicesSubtitle: '4 langkah • 2 menit',
	SetupGuideAddServicesTitle: 'Tambahkan Layanan Anda',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Mulai',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 langkah • 3 menit',
	SetupGuideEnableOnlinePaymentsTitle: 'Aktifkan pembayaran online',
	SetupGuideImportClientsActionLabel: 'Mulai',
	SetupGuideImportClientsSubtitle: '4 langkah • 3 menit',
	SetupGuideImportClientsTitle: 'Impor klien Anda',
	SetupGuideImportTemplatesActionLabel: 'Mulai',
	SetupGuideImportTemplatesSubtitle: '2 langkah • 1 menit',
	SetupGuideImportTemplatesTitle: 'Impor templat Anda',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Mulai',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 langkah • 2 menit',
	SetupGuidePersonalizeWorkspaceTitle: 'Personalisasi ruang kerja Anda',
	SetupGuideSetLocationActionLabel: 'Mulai',
	SetupGuideSetLocationSubtitle: '4 langkah • 1 menit',
	SetupGuideSetLocationTitle: 'Atur lokasi Anda',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Undang tim',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Ajak tim Anda untuk berkomunikasi dan mengelola tugas dengan mudah.',
	SetupGuideSuggestedAddTeamMembersTag: 'Pengaturan',
	SetupGuideSuggestedAddTeamMembersTitle: 'Tambahkan anggota tim',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Sesuaikan',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Terlihat profesional dengan logo dan warna merek Anda yang unik.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Kustomisasi merek',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Unduh',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Akses ruang kerja Anda di mana saja, kapan saja, di perangkat apa pun.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Pengaturan',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Unduh aplikasinya',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Atur Ketersediaan',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Cegah pemesanan ganda dengan mengatur ketersediaan Anda.',
	SetupGuideSuggestedEditAvailabilityTag: 'Penjadwalan',
	SetupGuideSuggestedEditAvailabilityTitle: 'Edit ketersediaan',
	SetupGuideSuggestedImportClientsActionLabel: 'Impor',
	SetupGuideSuggestedImportClientsSubtitle: 'Unggah data klien Anda yang sudah ada dengan satu klik saja.',
	SetupGuideSuggestedImportClientsTag: 'Pengaturan',
	SetupGuideSuggestedImportClientsTitle: 'Impor Klien',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Edit pengingat',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Kurangi pasien yang tidak hadir dengan pengingat janji temu otomatis.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Pengingat yang dipersonalisasi',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Mulai panggilan',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Selenggarakan panggilan dan terhubung dengan klien menggunakan alat video yang didukung AI kami.',
	SetupGuideSuggestedStartVideoCallTag: 'Telehealth',
	SetupGuideSuggestedStartVideoCallTitle: 'Mulai panggilan video',
	SetupGuideSuggestedTryActionsTitle: 'Hal yang perlu dicoba 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Coba bantuan AI',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Dapatkan jawaban instan untuk semua pertanyaan kerja Anda.',
	SetupGuideSuggestedUseAIAssistantTag: 'Baru',
	SetupGuideSuggestedUseAIAssistantTitle: 'Gunakan asisten AI',
	SetupGuideSyncCalendarActionLabel: 'Mulai',
	SetupGuideSyncCalendarSubtitle: '1 langkah • kurang dari 1 menit',
	SetupGuideSyncCalendarTitle: 'Sinkronkan kalender Anda',
	SetupGuideVerifyEmailLabel: 'Verifikasi',
	SetupGuideVerifyEmailSubtitle: '2 langkah • 2 menit',
	SetupOnlineStripePayments: 'Gunakan Stripe untuk pembayaran online',
	SetupPayments: 'Atur Pembayaran',
	Sex: 'Seks',
	SexSelectorPlaceholder: 'Pria / Wanita / Lebih suka tidak menyebutkan',
	Share: 'Membagikan',
	ShareBookingLink: 'Bagikan tautan pemesanan',
	ShareNoteDefaultMessage: `Hai{name} telah membagikan "{documentName}" kepada Anda.

Terima kasih,
{practiceName}`,
	ShareNoteMessage: `Hai
{name} telah membagikan "{documentName}" {isResponder, select, true {dengan beberapa pertanyaan untuk Anda isi.} other {dengan Anda.}}

Terima kasih,
{practiceName}`,
	ShareNoteTitle: 'Bagikan ‘{noteTitle}’',
	ShareNotesWithClients: 'Bagikan dengan klien atau kontak',
	ShareScreen: 'Bagikan layar',
	ShareScreenNotSupported: 'Perangkat/browser Anda tidak mendukung fitur berbagi layar',
	ShareScreenWithId: 'Layar {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'Izinkan orang lain untuk melihat template ini dan mengirimkan sebagai formulir.',
	ShareTemplateAsPublicFormModalTitle: 'Bagikan tautan untuk ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Konfigurasi formulir publik berhasil diperbarui',
	ShareTemplateAsPublicFormSectionCustomization: 'Kustomisasi',
	ShareTemplateAsPublicFormShowPoweredBy: 'Tampilkan "Powered by Carepatron" di form saya',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage:
		'Tampilkan/sembunyikan “Powered by Carepatron” di formulir saya',
	ShareTemplateAsPublicFormTrigger: 'Bagikan',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Gunakan branding ruang kerja',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Tampilkan/sembunyikan branding ruang kerja',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Mengirim kode untuk klien yang sudah ada dan yang belum ada',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Tanda tangan selalu mengharuskan email untuk diverifikasi',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Hanya mengirimkan kode untuk klien yang sudah ada',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Tidak pernah mengirimkan kode',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Memilih 'Tidak Pernah' dapat memungkinkan pengguna yang tidak diverifikasi untuk menimpa data klien jika mereka menggunakan alamat email klien yang sudah ada.`,
	ShareWithCommunity: 'Berbagi dengan Komunitas',
	ShareYourReferralLink: 'Bagikan tautan rujukan Anda',
	ShareYourScreen: 'Bagikan layar Anda',
	SheHer: 'Dia/Dia',
	ShortTextAnswer: 'Jawaban teks pendek',
	ShortTextFormPrimaryText: 'Teks pendek',
	ShortTextFormSecondaryText: 'Jawaban kurang dari 300 karakter',
	Show: 'Menunjukkan',
	ShowColumn: 'Tampilkan kolom',
	ShowColumnButton: 'Tampilkan tombol kolom {value}',
	ShowColumns: 'Tampilkan kolom',
	ShowColumnsMenu: 'Tampilkan menu kolom',
	ShowDateDurationDescription: 'misal 29 tahun',
	ShowDateDurationLabel: 'Tampilkan durasi tanggal',
	ShowDetails: 'Tampilkan detail',
	ShowField: 'Tampilkan bidang',
	ShowFullAddress: 'Tampilkan alamat',
	ShowHideFields: 'Tampilkan / Sembunyikan bidang',
	ShowIcons: 'Tampilkan ikon',
	ShowLess: 'Tampilkan lebih sedikit',
	ShowMeetingTimers: 'Tampilkan pengatur waktu rapat',
	ShowMenu: 'Tampilkan menu',
	ShowMergeSummarySidebar: 'Tampilkan ringkasan penggabungan',
	ShowMore: 'Tampilkan lebih banyak',
	ShowOnTranscript: 'Tampilkan pada transkrip',
	ShowReactions: 'Tampilkan reaksi',
	ShowSection: 'Tampilkan bagian',
	ShowServiceCode: 'Tampilkan kode layanan',
	ShowServiceDescription: 'Tampilkan deskripsi pada pemesanan layanan',
	ShowServiceDescriptionDesc: 'Klien dapat melihat deskripsi layanan saat melakukan pemesanan',
	ShowServiceGroups: 'Tampilkan koleksi',
	ShowServiceGroupsDesc:
		'Klien akan diperlihatkan layanan yang dikelompokkan berdasarkan koleksi saat melakukan pemesanan',
	ShowSpeakers: 'Tampilkan pembicara',
	ShowTax: 'Tampilkan pajak',
	ShowTimestamp: 'Tampilkan stempel waktu',
	ShowUnits: 'Tampilkan unit',
	ShowWeekends: 'Tampilkan akhir pekan',
	ShowYourView: 'Tunjukkan pandangan Anda',
	SignInWithApple: 'Masuk dengan Apple',
	SignInWithGoogle: 'Masuk dengan Google',
	SignInWithMicrosoft: 'Masuk dengan Microsoft',
	SignUpTitleReferralDefault: '<mark>Mendaftar</mark> dan klaim hadiah referensi Anda',
	SignUpTitleReferralUpgrade:
		'Mulai {durationInMonths} bulan <mark>{percentOff, select, 100 {gratis} other {{percentOff}% diskon}} peningkatan</mark>',
	SignatureCaptureError: 'Tidak dapat mengambil tanda tangan. Silakan coba lagi.',
	SignatureFormPrimaryText: 'Tanda tangan',
	SignatureFormSecondaryText: 'Dapatkan tanda tangan digital',
	SignatureInfoTooltip: 'Representasi visual ini bukan tanda tangan elektronik yang sah.',
	SignaturePlaceholder: 'Tulis tanda tanganmu di sini',
	SignedBy: 'Ditandatangani oleh',
	Signup: 'Mendaftar',
	SignupAgreements: 'Saya setuju dengan {termsOfUse} dan {privacyStatement} untuk akun saya.',
	SignupBAA: 'Perjanjian Rekanan Bisnis',
	SignupBusinessAgreements:
		'Atas nama saya dan bisnis saya, saya setuju dengan {businessAssociateAgreement}, {termsOfUse}, dan {privacyStatement} untuk akun saya.',
	SignupInvitationForYou: 'Anda telah diundang untuk menggunakan Carepatron.',
	SignupPageProviderWarning:
		'Jika administrator Anda telah membuat akun, Anda perlu meminta mereka untuk mengundang Anda ke penyedia tersebut. Jangan gunakan formulir pendaftaran ini. Untuk informasi lebih lanjut, lihat',
	SignupPageProviderWarningLink: 'tautan ini.',
	SignupPrivacy: 'Kebijakan Privasi',
	SignupProfession: 'Profesi apa yang paling menggambarkan Anda?',
	SignupSubtitle:
		'Perangkat lunak manajemen praktik Carepatron dibuat untuk praktisi tunggal dan tim. Berhentilah membayar biaya berlebihan dan jadilah bagian dari revolusi ini.',
	SignupSuccessDescription:
		'Konfirmasikan alamat email Anda untuk memulai pendaftaran. Jika Anda tidak segera menerimanya, silakan periksa folder spam Anda.',
	SignupSuccessTitle: 'Silakan periksa email Anda',
	SignupTermsOfUse: 'Ketentuan Penggunaan',
	SignupTitleClient: '<mark>Kelola kesehatan Anda</mark> dari satu tempat',
	SignupTitleLast: 'dan semua pekerjaan yang Anda lakukan! — Gratis',
	SignupTitleOne: '<mark>Memberdayakan Anda</mark> Bahasa Indonesia: ',
	SignupTitleThree: '<mark>Memberdayakan klien Anda</mark> Bahasa Indonesia: ',
	SignupTitleTwo: '<mark>Memberdayakan tim Anda</mark> Bahasa Indonesia: ',
	Simple: 'Sederhana',
	SimplifyBillToDetails: 'Sederhanakan tagihan ke detail',
	SimplifyBillToHelperText: 'Hanya baris pertama yang digunakan saat cocok dengan klien',
	Singapore: 'Singapura',
	Single: 'Lajang',
	SingleChoiceFormPrimaryText: 'Pilihan tunggal',
	SingleChoiceFormSecondaryText: 'Pilih hanya satu opsi',
	Sister: 'Saudari',
	SisterInLaw: 'Kakak ipar',
	Skip: 'Melewati',
	SkipLogin: 'Lewati login',
	SlightBlur: 'Sedikit mengaburkan latar belakang Anda',
	Small: 'Kecil',
	SmartChips: 'Chip pintar',
	SmartDataChips: 'Chip data pintar',
	SmartReply: 'Balas Cepat',
	SmartSuggestNewClient: '<strong>Smart Suggest</strong> buat {name} sebagai klien baru',
	SmartSuggestedFieldDescription: 'Bidang ini adalah saran cerdas',
	SocialSecurityNumber: 'Nomor jaminan sosial',
	SocialWork: 'Pekerjaan sosial',
	SocialWorker: 'Pekerja Sosial',
	SoftwareDeveloper: 'Pengembang Perangkat Lunak',
	Solo: 'Solo',
	Someone: 'Seseorang',
	Son: 'Putra',
	SortBy: 'Urutkan berdasarkan',
	SouthAmerica: 'Amerika Selatan',
	Speaker: 'Pembicara',
	SpeakerSource: 'Sumber pembicara',
	Speakers: 'Pembicara',
	SpecifyPaymentMethod: 'Tentukan metode pembayaran',
	SpeechLanguagePathology: 'Patologi Bicara dan Bahasa',
	SpeechTherapist: 'Terapis Bicara',
	SpeechTherapists: 'Terapis Bicara',
	SpeechTherapy: 'Terapi Bicara',
	SportsMedicinePhysician: 'Dokter Kedokteran Olahraga',
	Spouse: 'Pasangan',
	SpreadsheetColumnExample: 'misalnya ',
	SpreadsheetColumns: 'Kolom Spreadsheet',
	SpreadsheetUploaded: 'Spreadsheet Diunggah',
	SpreadsheetUploading: 'Mengunggah...',
	Staff: 'Staf',
	StaffAccessDescriptionAdmin: 'Admin dapat mengelola segalanya di platform.',
	StaffAccessDescriptionStaff: `Anggota staf dapat mengelola klien, catatan, dan dokumentasi yang telah mereka buat atau telah dibagikan
 bersama mereka, menjadwalkan janji temu, mengelola faktur.`,
	StaffContactAssignedSubject:
		'{actorProfileName} telah menugaskan {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} dan {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { dan 1 klien lainnya} other { dan # klien lainnya}} kepada Anda',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} telah membagikan kotak masuk {inboxName} kepada Anda',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} telah menghapus akses Anda ke kotak masuk {inboxName}',
	StaffMembers: 'Anggota Staf',
	StaffMembersNumber: '{billedUsers, plural, one {# anggota tim} other {# anggota tim}}',
	StaffSavedSuccessSnackbar: 'Info anggota tim berhasil disimpan!',
	StaffSelectorAdminRole: 'Administrator',
	StaffSelectorStaffRole: 'Anggota staf',
	StandardAppointment: 'Janji Temu Standar',
	StandardColor: 'Tugas warna',
	StartAndEndTime: 'Waktu mulai dan berakhir',
	StartCall: 'Mulai panggilan',
	StartDate: 'Tanggal mulai',
	StartDictating: 'Mulai mendikte',
	StartImport: 'Mulai impor',
	StartRecordErrorTitle: 'Terjadi kesalahan saat memulai rekaman Anda',
	StartRecording: 'Mulai merekam',
	StartTimeIncrements: 'Peningkatan waktu mulai',
	StartTimeIncrementsView: '{startTimeIncrements} interval menit',
	StartTranscribing: 'Mulai Transkripsi',
	StartTranscribingNotes:
		'Silakan pilih klien yang ingin Anda buatkan catatannya. Lalu klik tombol "Mulai Transkripsi" untuk mulai merekam.',
	StartTranscription: 'Mulai transkripsi',
	StartVideoCall: 'Mulai panggilan video',
	StartWeekOn: 'Mulai minggu di',
	StartedBy: 'Dimulai oleh ',
	Starter: 'Starter',
	State: 'Negara',
	StateIndustrialAccidentProviderNumber: 'Nomor penyedia kecelakaan industri negara bagian',
	StateLicenseNumber: 'Nomor lisensi negara',
	Statement: 'Penyataan',
	StatementDescriptor: 'Deskripsi pernyataan',
	StatementDescriptorToolTip:
		'Deskripsi laporan ditampilkan pada laporan bank atau kartu kredit klien Anda. Deskripsi laporan harus terdiri dari 5 hingga 22 karakter dan mencerminkan nama bisnis Anda.',
	StatementNumber: 'Pernyataan #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Masukkan label status',
	StepFather: 'Ayah tiri',
	StepMother: 'Ibu tiri',
	Stockholm: 'Kota Stockholm',
	StopIgnoreSendersDescription: `Dengan berhenti mengabaikan pengirim ini, percakapan selanjutnya akan dikirim ke 'Kotak Masuk'. Apakah Anda yakin ingin berhenti mengabaikan pengirim ini?`,
	StopIgnoring: 'Berhenti mengabaikan',
	StopIgnoringSenders: 'Berhenti mengabaikan pengirim',
	StopIgnoringSendersSuccess: 'Berhenti mengabaikan alamat email <mark>{addresses}</mark>',
	StopSharing: 'Berhenti berbagi',
	StopSharingLabel: 'carepatron.com sedang membagikan layar Anda.',
	Storage: 'Penyimpanan',
	StorageAlmostFullDescription: '🚀 Tingkatkan sekarang agar akun Anda berjalan lancar.',
	StorageAlmostFullTitle: 'Kamu telah menggunakan {percentage}% dari batas penyimpanan ruang kerja kamu!',
	StorageFullDescription: 'Dapatkan penyimpanan lebih banyak dengan meningkatkan paket Anda.',
	StorageFullTitle: '	Penyimpanan Anda penuh.',
	Street: 'Jalan',
	StripeAccountNotCompleteErrorCode:
		'Pembayaran online tidak {hasProviderName, select, true {disiapkan untuk {providerName}} other {diaktifkan untuk penyedia ini}}.',
	StripeAccountRejectedError: 'Akun Stripe telah ditolak. Silakan hubungi bagian dukungan.',
	StripeBalance: 'Keseimbangan Garis',
	StripeChargesInfoToolTip: 'Memungkinkan Anda untuk mengisi debit ',
	StripeFeesDescription:
		'Carepatron menggunakan Stripe untuk membuat Anda dibayar dengan cepat dan menjaga informasi pembayaran Anda tetap aman. Metode pembayaran yang tersedia bervariasi menurut wilayah, semua metode debit utama ',
	StripeFeesDescriptionItem1: 'Biaya pemrosesan diterapkan untuk setiap transaksi yang berhasil, Anda dapat {link}.',
	StripeFeesDescriptionItem2: 'Pembayaran terjadi setiap hari tetapi ditahan hingga 4 hari.',
	StripeFeesLinkToRatesText: 'lihat tarif kami di sini',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Maaf, minimal {minimumAmount} diperlukan untuk faktur yang menggunakan pembayaran online',
	StripePaymentsDisabled: 'Pembayaran online dinonaktifkan. Harap periksa pengaturan pembayaran Anda.',
	StripePaymentsUnavailable: 'Pembayaran tidak tersedia',
	StripePaymentsUnavailableDescription: 'Terjadi kesalahan saat memuat pembayaran. Coba lagi nanti.',
	StripePayoutsInfoToolTip: 'Memungkinkan Anda menerima pembayaran ke rekening bank Anda',
	StyleYourWorkspace: '<mark>Gaya</mark> ruang kerja Anda',
	StyleYourWorkspaceDescription1:
		'Kami telah mengambil aset merek dari situs web Anda. Jangan ragu untuk mengeditnya atau lanjutkan ke ruang kerja Carepatron Anda',
	StyleYourWorkspaceDescription2:
		'Gunakan aset merek Anda untuk menyesuaikan faktur dan pemesanan online untuk pengalaman pelanggan yang mulus',
	SubAdvanced: 'Lanjutan',
	SubEssential: 'Penting',
	SubOrganization: 'Organisasi',
	SubPlus: 'Plus',
	SubProfessional: 'Profesional',
	Subject: 'Subjek',
	Submit: 'Kirim',
	SubmitElectronically: 'Kirim secara elektronik',
	SubmitFeedback: 'Kirim Umpan Balik',
	SubmitFormValidationError: 'Pastikan semua kolom yang diperlukan telah diisi dengan benar, lalu coba kirim lagi.',
	Submitted: 'Dikirimkan',
	SubmittedDate: 'Tanggal pengajuan',
	SubscribePerMonth: 'Berlangganan {price} {isMonthly, select, true {per bulan} other {per tahun}}',
	SubscriptionDiscountDescription:
		'{percentOff}% off {months, select, null { } other { {months, plural, one {selama # bulan} other {selama # bulan}}}}',
	SubscriptionFreeTrialDescription: 'Gratis hingga {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Kami tidak dapat menyelesaikan pembayaran langganan Anda. Silakan periksa detail pembayaran Anda',
	SubscriptionPlanDetailsHeader: 'Per pengguna/bulanan ditagih setiap tahun',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} ditagih bulanan (USD)',
	SubscriptionPlans: 'Paket Berlangganan',
	SubscriptionPlansDescription:
		'Tingkatkan paket Anda untuk membuka manfaat tambahan dan menjaga praktik Anda berjalan lancar.',
	SubscriptionPlansDescriptionNoPermission:
		'Sepertinya Anda tidak memiliki akses untuk peningkatan saat ini - silakan hubungi administrator Anda untuk mendapatkan bantuan.',
	SubscriptionSettings: 'Pengaturan langganan',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> dari penyimpanan terpakai',
	SubscriptionSettingsStorageUsed: '{used} dari {limit} yang digunakan',
	SubscriptionSettingsUnlimitedStorage: 'Penyimpanan tak terbatas tersedia',
	SubscriptionSummary: 'Ringkasan langganan',
	SubscriptionUnavailableOverStorageLimit: 'Penggunaan Anda saat ini melebihi batas penyimpanan paket ini.',
	SubscriptionUnpaidBannerButton: 'Buka langganan',
	SubscriptionUnpaidBannerDescription: 'Harap periksa apakah detail pembayaran Anda sudah benar dan coba lagi',
	SubscriptionUnpaidBannerTitle: 'Kami tidak dapat menyelesaikan pembayaran langganan Anda.',
	Subscriptions: 'Langganan',
	SubscriptionsAndPayments: 'Langganan ',
	Subtotal: 'Jumlah keseluruhan',
	SuburbOrProvince: 'Daerah Pinggiran Kota/Provinsi',
	SuburbOrState: 'Daerah Pinggiran Kota/Negara Bagian',
	SuccessSavedNoteChanges: 'Berhasil menyimpan perubahan catatan',
	SuccessShareDocument: 'Dokumen berhasil dibagikan',
	SuccessShareNote: 'Catatan berhasil dibagikan',
	SuccessfullyCreatedValue: 'Berhasil dibuat {value}',
	SuccessfullyDeletedTranscriptionPart: 'Bagian transkripsi berhasil dihapus',
	SuccessfullyDeletedValue: 'Berhasil menghapus {value}',
	SuccessfullySubmitted: 'Berhasil dikirimkan ',
	SuccessfullyUpdatedClientSettings: 'Berhasil memperbarui Pengaturan Klien',
	SuccessfullyUpdatedTranscriptionPart: 'Berhasil memperbarui bagian transkripsi',
	SuccessfullyUpdatedValue: 'Berhasil diperbarui {value}',
	SuggestedAIPoweredTemplates: 'Templat Bertenaga AI yang Disarankan',
	SuggestedAITemplates: 'Template AI yang Disarankan',
	SuggestedActions: 'Tindakan yang disarankan',
	SuggestedLocations: 'Lokasi yang Disarankan',
	Suggestions: 'Saran',
	Summarise: 'AI merangkum',
	SummarisingContent: 'Merangkum {title}',
	Sunday: 'Minggu',
	Superbill: 'Tagihan super',
	SuperbillAndInsuranceBilling: 'Tagihan super ',
	SuperbillAutomationMonthly: 'Aktif • Hari terakhir bulan ini',
	SuperbillAutomationNoEmail:
		'Untuk mengirim dokumen penagihan otomatis dengan sukses, tambahkan alamat email untuk klien ini',
	SuperbillAutomationNotActive: 'Tidak aktif',
	SuperbillAutomationUpdateFailure: 'Gagal memperbarui pengaturan otomatisasi Superbill',
	SuperbillAutomationUpdateSuccess: 'Berhasil memperbarui pengaturan otomatisasi Superbill',
	SuperbillClientHelperText: 'Informasi ini diisi sebelumnya dari rincian klien',
	SuperbillNotFoundDescription:
		'Silakan hubungi penyedia Anda dan tanyakan kepada mereka untuk informasi lebih lanjut atau untuk mengirim ulang superbill.',
	SuperbillNotFoundTitle: 'Superbill tidak ditemukan',
	SuperbillNumber: 'Tagihan Super #{number}',
	SuperbillNumberAlreadyExists: 'Nomor penerimaan Superbill sudah ada',
	SuperbillPracticeHelperText: 'Informasi ini diisi sebelumnya dari pengaturan penagihan praktik',
	SuperbillProviderHelperText: 'Informasi ini diisi sebelumnya dari rincian staf',
	SuperbillReceipts: 'Kwitansi Superbill',
	SuperbillsEmptyStateDescription: 'Tidak ada superbill yang ditemukan.',
	Surgeon: 'Ahli bedah',
	Surgeons: 'Dokter bedah',
	SurgicalTechnologist: 'Ahli Teknologi Bedah',
	SwitchFromAnotherPlatform: 'Saya beralih dari platform lain',
	SwitchToMyPortal: 'Beralih ke Portal Saya',
	SwitchToMyPortalTooltip: `Akses portal pribadi Anda,
 memungkinkan Anda menjelajahi
 pengalaman portal klien.`,
	SwitchWorkspace: 'Beralih ruang kerja',
	SwitchingToADifferentPlatform: 'Beralih ke platform lain',
	Sydney: 'Kota Sydney',
	SyncCalendar: 'Sinkronkan kalender',
	SyncCalendarModalDescription:
		'Anggota tim lain tidak akan dapat melihat kalender Anda yang disinkronkan. Janji temu klien hanya dapat diperbarui atau dihapus dari dalam Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Tampilkan kalender saya di Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Sinkronkan kalender saya ke Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Sinkronkan janji temu Carepatron dengan kalender saya',
	SyncCarepatronAppointmentsWithMyCalendar: 'Sinkronkan janji temu Carepatron dengan kalender saya',
	SyncGoogleCalendar: 'Sinkronkan kalender Google',
	SyncInbox: 'Sinkronkan kotak masuk dengan Carepatron',
	SyncMyCalendarToCarepatron: 'Sinkronkan kalender saya ke Carepatron',
	SyncOutlookCalendar: 'Sinkronkan kalender Outlook',
	SyncedFromExternalCalendar: 'Disinkronkan dari kalender eksternal',
	SyncingCalendarName: 'Menyinkronkan kalender {calendarName}',
	SyncingFailed: 'Sinkronisasi gagal',
	SystemGenerated: 'Dibuat oleh sistem',
	TFN: 'NPWP',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Meja',
	TableRowLabel: 'Baris tabel untuk {value}',
	TagSelectorNoOptionsText: 'Klik "buat baru" untuk menambahkan tag baru',
	Tags: 'Tag:',
	TagsInputPlaceholder: 'Cari atau buat tag',
	Task: 'Tugas',
	TaskAttendeeStatusUpdatedSuccess: 'Status janji temu berhasil diperbarui',
	Tasks: 'Tugas',
	Tax: 'Pajak',
	TaxAmount: 'Jumlah Pajak',
	TaxID: 'Nomor Pokok Wajib Pajak',
	TaxIdType: 'Jenis NPWP',
	TaxName: 'Nama pajak',
	TaxNumber: 'Nomor pajak',
	TaxNumberType: 'Tipe Nomor Pajak',
	TaxNumberTypeInvalid: '{type} tidak valid',
	TaxPercentageOfAmount: '{taxName} ({percentage}% dari {amount})',
	TaxRate: 'Tarif pajak',
	TaxRatesDescription: 'Kelola tarif pajak yang akan diterapkan pada baris item faktur Anda.',
	Taxable: 'Kena Pajak',
	TaxonomyCode: 'Kode taksonomi',
	TeacherAssistant: 'Asisten Guru',
	Team: 'Tim',
	TeamMember: 'Anggota tim',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {sudah} other {sudah}} dipesan pada waktu ini.{br}Pilih waktu baru untuk menghindari pemesanan ganda.',
	TeamMembers: 'Anggota tim',
	TeamMembersColour: 'Anggota tim mewarnai',
	TeamMembersDetails: 'Detail anggota tim',
	TeamSize: 'Berapa banyak orang dalam tim Anda?',
	TeamTemplates: 'Templat tim',
	TeamTemplatesSectionDescription: 'Dibuat oleh Anda dan tim Anda',
	TelehealthAndVideoCalls: 'Telekesehatan ',
	TelehealthProvidedOtherThanInPatientCare: 'Telehealth disediakan untuk perawatan selain rawat inap',
	TelehealthVideoCall: 'Panggilan video telehealth',
	Template: 'Templat',
	TemplateDescription: 'Deskripsi templat',
	TemplateDetails: 'Detail templat',
	TemplateEditModeViewSwitcherDescription: 'Buat dan edit template',
	TemplateGallery: 'Template Komunitas',
	TemplateImportCompletedNotificationSubject: 'Template impor selesai! {templateTitle} siap digunakan.',
	TemplateImportFailedNotificationSubject: 'Gagal mengimpor berkas {fileName}.',
	TemplateName: 'Nama templat',
	TemplateNotFound: 'Templat tidak dapat ditemukan.',
	TemplatePreviewErrorMessage: 'Terjadi kesalahan saat memuat pratinjau templat',
	TemplateResponderModeViewSwitcherDescription: 'Pratinjau dan berinteraksi dengan formulir',
	TemplateResponderModeViewSwitcherTooltipTitle: 'Periksa bagaimana formulir Anda muncul saat diisi oleh responden',
	TemplateSaved: 'Perubahan yang disimpan',
	TemplateTitle: 'Judul Template',
	TemplateType: 'Tipe Template',
	Templates: 'Templat',
	TemplatesCategoriesFilter: 'Filter berdasarkan kategori',
	TemplatesPublicTemplatesFilter: ' Filter berdasarkan Komunitas/Tim',
	Text: 'Teks',
	TextAlign: 'Penyelarasan teks',
	TextColor: 'Warna teks',
	ThankYouForYourFeedback: 'Terima kasih atas masukan Anda!',
	ThanksForLettingKnow: 'Terima kasih telah memberi tahu kami.',
	ThePaymentMethod: 'Metode pembayaran',
	ThemThey: 'Mereka/Mereka',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Lebih banyak tema',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Gelap',
	ThemeColorLightMode: 'Cahaya',
	ThemeColorModePickerTitle: 'Mode Warna',
	ThemeColorSystemMode: 'Sistem',
	ThemeCpColorPickerTitle: 'Tema Carepatron',
	ThemePanelDescription: 'Pilih antara mode terang dan gelap, dan sesuaikan preferensi tema Anda',
	ThemePanelTitle: 'Penampilan',
	Then: 'Kemudian',
	Therapist: 'Dokter',
	Therapists: 'Terapis',
	Therapy: 'Terapi',
	Thick: 'Tebal',
	Thin: 'Tipis',
	ThirdPerson: 'Orang ke 3',
	ThisAndFollowingAppointments: 'Penunjukan ini dan selanjutnya',
	ThisAndFollowingMeetings: 'Pertemuan ini dan pertemuan berikutnya',
	ThisAndFollowingReminders: 'Pengingat ini dan berikut ini',
	ThisAndFollowingTasks: 'Tugas ini dan tugas berikutnya',
	ThisAppointment: 'Penunjukan ini',
	ThisMeeting: 'Pertemuan ini',
	ThisMonth: 'Bulan ini',
	ThisPerson: 'Orang ini',
	ThisReminder: 'Pengingat ini',
	ThisTask: 'Tugas ini',
	ThisWeek: 'Minggu ini',
	ThreeDay: '3 Hari',
	Thursday: 'Kamis',
	Time: 'Waktu',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {menit} other {menit}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Format Waktu',
	TimeIncrement: 'Peningkatan waktu',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {pagi} other {sore}}',
	TimeslotSize: 'Ukuran slot waktu',
	Timestamp: 'Stempel waktu',
	Timezone: 'Zona waktu',
	TimezoneDisplay: 'Tampilan zona waktu',
	TimezoneDisplayDescription: 'Kelola pengaturan tampilan zona waktu Anda.',
	Title: 'Judul',
	To: 'Ke',
	ToYourWorkspace: 'ke ruang kerja Anda',
	Today: 'Hari ini',
	TodayInHoursPlural: 'Hari ini dalam {count} {count, plural, one {jam} other {jam}}',
	TodayInMinsAbbreviated: 'Hari ini dalam {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Alihkan sel tajuk',
	ToggleHeaderCol: 'Alihkan kolom tajuk',
	ToggleHeaderRow: 'Alihkan baris tajuk',
	Tokyo: 'Tokyo',
	Tomorrow: 'Besok',
	TomorrowAfternoon: 'Besok sore',
	TomorrowMorning: 'Besok pagi',
	TooExpensive: 'Terlalu mahal',
	TooHardToSetUp: 'Terlalu sulit untuk diatur',
	TooManyFiles: 'Lebih dari 1 berkas terdeteksi.',
	ToolsExample: 'Praktik sederhana, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Total',
	TotalAccountCredit: 'Total kredit akun',
	TotalAdjustments: 'Total penyesuaian',
	TotalAmountToCreditInCurrency: 'Jumlah total kredit ({currency})',
	TotalBilled: 'Total yang ditagihkan',
	TotalConversations: '{total} {total, plural, =0 {percakapan} one {percakapan} other {percakapan}}',
	TotalOverdue: 'Total Terlambat',
	TotalOverdueTooltip:
		'Total Saldo Tertunggak mencakup semua faktur yang belum dibayar, berapa pun rentang tanggalnya, yang tidak dibatalkan maupun diproses.',
	TotalPaid: 'Total Dibayar',
	TotalPaidTooltip:
		'Total Saldo Terbayar mencakup semua jumlah dari faktur yang telah dibayar dalam rentang tanggal yang ditentukan.',
	TotalUnpaid: 'Total yang Belum Dibayar',
	TotalUnpaidTooltip:
		'Total Saldo yang Belum Dibayar mencakup semua jumlah terutang dari pemrosesan, faktur yang belum dibayar, dan faktur yang telah dikirim yang jatuh tempo dalam rentang tanggal yang ditentukan.',
	TotalWorkflows: '{count} {count, plural, one {alur kerja} other {alur kerja}}',
	TotpSetUpManualEntryInstruction: 'Atau, Anda dapat memasukkan kode di bawah ini secara manual ke dalam aplikasi:',
	TotpSetUpModalDescription:
		'Pindai kode QR dengan aplikasi autentikator Anda untuk menyiapkan Autentikasi Multi-Faktor.',
	TotpSetUpModalTitle: 'Siapkan perangkat MFA',
	TotpSetUpSuccess: 'Anda sudah siap! MFA telah diaktifkan.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Masukkan kode yang dihasilkan oleh aplikasi autentikator Anda',
	Transcribe: 'Menuliskan',
	TranscribeLanguageSelector: 'Pilih bahasa masukan',
	TranscribeLiveAudio: 'Transkripsikan audio langsung',
	Transcribing: 'Menyalin audio...',
	TranscribingIn: 'Transkripsi dalam',
	Transcript: 'Salinan',
	TranscriptRecordingCompleteInfo: 'Anda akan melihat transkrip Anda di sini setelah rekaman selesai.',
	TranscriptSuccessSnackbar: 'Transkrip berhasil diproses.',
	Transcription: 'Transkripsi',
	TranscriptionEmpty: 'Tidak ada transkripsi yang tersedia',
	TranscriptionEmptyHelperMessage: 'Transkripsi ini tidak menangkap apa pun. Mulai ulang dan coba lagi.',
	TranscriptionFailedNotice: 'Transkripsi ini tidak berhasil diproses',
	TranscriptionIdleMessage:
		'Kami tidak mendengar audio apa pun. Jika Anda membutuhkan lebih banyak waktu, harap tanggapi dalam {timeValue} detik, atau sesi akan berakhir.',
	TranscriptionInProcess: 'Transkripsi sedang berlangsung...',
	TranscriptionIncompleteNotice: 'Beberapa bagian transkripsi ini tidak berhasil diproses',
	TranscriptionOvertimeWarning: '{scribeType} sesi berakhir dalam <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Apakah Anda yakin ingin menghapus bagian transkripsi ini?',
	TranscriptionText: 'Suara ke teks',
	TranscriptsPending: 'Transkrip Anda akan tersedia di sini setelah sesi berakhir.',
	Transfer: 'Transfer',
	TransferAndDelete: 'Transfer dan hapus',
	TransferOwnership: 'Mentransfer kepemilikan',
	TransferOwnershipConfirmationModalDescription:
		'Tindakan ini hanya dapat dibatalkan jika mereka mentransfer kepemilikan kembali kepada Anda.',
	TransferOwnershipDescription: 'Transfer kepemilikan ruang kerja ini ke anggota tim lain.',
	TransferOwnershipSuccessSnackbar: 'Berhasil mentransfer kepemilikan!',
	TransferOwnershipToMember: 'Apakah Anda yakin ingin mentransfer ruang kerja ini ke {staff}?',
	TransferStatusAlert:
		'Menghapus {numberOfStatuses, plural, one {status ini} other {status-status ini}} akan berdampak pada {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} status klien.</strong>} other {<strong>{numberOfAffectedRecords} status klien.</strong>}}',
	TransferStatusDescription:
		'Pilih status lain untuk klien ini sebelum melanjutkan penghapusan. Tindakan ini tidak dapat dibatalkan.',
	TransferStatusLabel: 'Transfer ke status baru',
	TransferStatusPlaceholder: 'Pilih status yang ada',
	TransferStatusTitle: 'Status transfer sebelum penghapusan',
	TransferTaskAttendeeStatusAlert:
		'Menghapus status ini akan berdampak pada <strong>{number} janji temu mendatang {number, plural, one {status} other {status}}.</strong>',
	TransferTaskAttendeeStatusDescription:
		'Pilih status lain untuk klien ini sebelum melanjutkan dengan penghapusan. Tindakan ini tidak dapat dibatalkan.',
	TransferTaskAttendeeStatusSubtitle: 'Status Pertemuan',
	TransferTaskAttendeeStatusTitle: 'Status Transfer Sebelum Penghapusan',
	Trash: 'Sampah',
	TrashDeleteItemsModalConfirm: 'Untuk konfirmasi, ketik {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Berikut {count, plural, one {item} other {items}} akan dihapus secara permanen dan tidak dapat dipulihkan.',
	TrashDeleteItemsModalTitle: 'Hapus {count, plural, one {item} other {items}} selamanya',
	TrashDeletedAllItems: 'Menghapus semua item',
	TrashDeletedItems: 'Dihapus {count, plural, one {item} other {items}}',
	TrashDeletedItemsFailure: 'Gagal menghapus item dari sampah',
	TrashLocationAppointmentType: 'Kalender',
	TrashLocationBillingAndPaymentsType: 'Tagihan & Pembayaran',
	TrashLocationContactType: 'Klien',
	TrashLocationNoteType: 'Catatan ',
	TrashRestoreItemsModalDescription: 'Berikut {count, plural, one {item} other {items}} akan dipulihkan.',
	TrashRestoreItemsModalTitle: 'Pulihkan {count, plural, one {item} other {items}}',
	TrashRestoredAllItems: 'Mengembalikan semua item',
	TrashRestoredItems: 'Diperbarui {count, plural, one {item} other {items}}',
	TrashRestoredItemsFailure: 'Gagal memulihkan item dari sampah',
	TrashSuccessfullyDeletedItem: 'Berhasil menghapus {type}',
	Trigger: 'Pemicu',
	Troubleshoot: 'Pemecahan Masalah',
	TryAgain: 'Coba lagi',
	Tuesday: 'Selasa',
	TwoToTen: '2 - 10',
	Type: 'Jenis',
	TypeHere: 'Ketik di sini...',
	TypeToConfirm: 'Untuk konfirmasi, ketik {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Judul 1',
	TypographyHeading2: 'Judul 2',
	TypographyHeading3: 'Judul 3',
	TypographyHeading4: 'Judul 4',
	TypographyHeading5: 'Judul 5',
	TypographyP: 'P',
	TypographyParagraph: 'Ayat',
	UnableToCompleteAction: 'Tidak dapat menyelesaikan tindakan.',
	UnableToPrintDocument: 'Tidak dapat mencetak dokumen. Silakan coba lagi nanti.',
	Unallocated: 'Tidak dialokasikan',
	UnallocatedPaymentDescription: `Pembayaran ini belum sepenuhnya dialokasikan ke item yang dapat ditagih.
 Tambahkan alokasi ke item yang belum dibayar, atau keluarkan kredit atau pengembalian dana.`,
	UnallocatedPaymentTitle: 'Pembayaran yang tidak dialokasikan',
	UnallocatedPayments: 'Pembayaran yang tidak dialokasikan',
	Unarchive: 'Batalkan pengarsipan',
	Unassigned: 'Tidak Ditugaskan',
	UnauthorisedInvoiceSnackbar: 'Anda tidak memiliki akses untuk mengelola faktur untuk klien ini.',
	UnauthorisedSnackbar: 'Anda tidak memiliki izin untuk melakukan ini.',
	Unavailable: 'Tidak tersedia',
	Uncategorized: 'Tidak Berkategori',
	Unclaimed: 'Tidak diklaim',
	UnclaimedAmount: 'Jumlah yang Tidak Dituntut',
	UnclaimedItems: 'Barang yang belum diklaim',
	UnclaimedItemsMustBeInCurrency: 'Hanya item dalam mata uang berikut yang didukung: {currencies}',
	Uncle: 'Paman',
	Unconfirmed: 'Belum dikonfirmasi',
	Underline: 'Menggarisbawahi',
	Undo: 'Membuka',
	Unfavorite: 'Tidak Disukai',
	Uninvoiced: 'Tidak ditagih',
	UninvoicedAmount: 'Jumlah yang Belum Difakturkan',
	UninvoicedAmounts:
		'{count, plural, =0 {Tidak ada jumlah yang belum difakturkan} one {Jumlah yang belum difakturkan} other {Jumlah yang belum difakturkan}}',
	Unit: 'Satuan',
	UnitedKingdom: 'Inggris Raya',
	UnitedStates: 'Amerika Serikat',
	UnitedStatesEast: 'Amerika Serikat - Timur',
	UnitedStatesWest: 'Amerika Serikat - Barat',
	Units: 'Satuan',
	UnitsIsRequired: 'Unit diperlukan',
	UnitsMustBeGreaterThanZero: 'Unit harus lebih besar dari 0',
	UnitsPlaceholder: '1',
	Unknown: 'Tidak diketahui',
	Unlimited: 'Tidak Terbatas',
	Unlock: 'Membuka kunci',
	UnlockNoteHelper: 'Sebelum membuat perubahan baru, editor diharuskan membuka kunci catatan tersebut.',
	UnmuteAudio: 'Nonaktifkan audio',
	UnmuteEveryone: 'Nonaktifkan semua orang',
	Unpaid: 'Belum dibayar',
	UnpaidInvoices: 'Faktur yang belum dibayar',
	UnpaidItems: 'Barang yang belum dibayar',
	UnpaidMultiple: 'Belum dibayar',
	Unpublish: 'Tidak Terbitkan',
	UnpublishTemplateConfirmationModalPrompt:
		'Menghapus <span>{title}</span> akan menghapus sumber daya ini dari komunitas Carepatron. Tindakan ini tidak dapat dibatalkan.',
	UnpublishToCommunitySuccessMessage: 'Berhasil menghapus ‛{title}’ dari komunitas',
	Unread: 'Belum dibaca',
	Unrecognised: 'Tidak dikenal',
	UnrecognisedDescription:
		'Metode pembayaran ini tidak dikenali oleh versi aplikasi Anda saat ini. Harap segarkan peramban Anda untuk mendapatkan versi terbaru guna melihat dan mengedit metode pembayaran ini.',
	UnsavedChanges: 'Perubahan yang belum disimpan',
	UnsavedChangesPromptContent: 'Apakah Anda ingin menyimpan perubahan sebelum menutup?',
	UnsavedChangesPromptTitle: 'Anda memiliki perubahan yang belum disimpan',
	UnsavedNoteChangesWarning: 'Perubahan yang Anda buat mungkin tidak disimpan',
	UnsavedTemplateChangesWarning: 'Perubahan yang Anda buat mungkin tidak disimpan',
	UnselectAll: 'Batalkan pilihan semua',
	Until: 'Sampai',
	UntitledConversation: 'Percakapan Tanpa Judul',
	UntitledFolder: 'Folder tanpa judul',
	UntitledNote: 'Catatan tanpa judul',
	UntitledSchedule: 'Jadwal tanpa judul',
	UntitledSection: 'Bagian tanpa judul',
	UntitledTemplate: 'Template tanpa judul',
	Unverified: 'Belum diverifikasi',
	Upcoming: 'Mendatang',
	UpcomingAppointments: 'Janji temu yang akan datang',
	UpcomingDateOverridesEmpty: 'Tidak ditemukan penggantian tanggal',
	UpdateAvailabilityScheduleFailure: 'Gagal memperbarui jadwal ketersediaan',
	UpdateAvailabilityScheduleSuccess: 'Berhasil memperbarui jadwal ketersediaan',
	UpdateInvoicesOrClaimsAgainstBillable: 'Apakah Anda ingin harga baru diterapkan pada faktur dan klaim peserta?',
	UpdateLink: 'Perbarui tautan',
	UpdatePrimaryEmailWarningDescription:
		'Mengubah alamat email klien Anda akan mengakibatkan hilangnya akses mereka ke janji temu dan catatan yang ada.',
	UpdatePrimaryEmailWarningTitle: 'Perubahan email klien',
	UpdateSettings: 'Perbarui pengaturan',
	UpdateStatus: 'Perbarui status',
	UpdateSuperbillReceiptFailure: 'Gagal memperbarui tanda terima Superbill',
	UpdateSuperbillReceiptSuccess: 'Berhasil memperbarui tanda terima Superbill',
	UpdateTaskBillingDetails: 'Perbarui detail penagihan',
	UpdateTaskBillingDetailsDescription:
		'Harga janji temu telah berubah. Apakah Anda ingin harga baru diterapkan pada item penagihan, faktur, dan klaim peserta? Pilih pembaruan yang ingin Anda lanjutkan.',
	UpdateTemplateFolderSuccessMessage: 'Berhasil memperbarui folder',
	UpdateUnpaidInvoices: 'Perbarui faktur yang belum dibayar',
	UpdateUserInfoSuccessSnackbar: 'Informasi pengguna berhasil diperbarui!',
	UpdateUserSettingsSuccessSnackbar: 'Berhasil memperbarui pengaturan pengguna!',
	Upgrade: 'Meningkatkan',
	UpgradeForSMSReminder: 'Tingkatkan ke <b>Profesional</b> untuk pengingat SMS tanpa batas',
	UpgradeNow: 'Tingkatkan Sekarang',
	UpgradePlan: 'Rencana peningkatan',
	UpgradeSubscriptionAlertDescription:
		'Anda kehabisan penyimpanan. Tingkatkan paket Anda untuk membuka penyimpanan tambahan dan agar praktik Anda tetap berjalan lancar!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Anda kehabisan ruang penyimpanan. Mintalah seseorang di praktik Anda dengan <span>Akses Administrator</span> tentang peningkatan paket Anda untuk membuka penyimpanan tambahan dan menjaga praktik Anda berjalan lancar!',
	UpgradeSubscriptionAlertTitle: 'Saatnya untuk meningkatkan langganan Anda',
	UpgradeYourPlan: 'Tingkatkan paket Anda',
	UploadAudio: 'Unggah Audio',
	UploadFile: 'Unggah berkas',
	UploadFileDescription: 'Platform perangkat lunak mana yang Anda gunakan saat ini?',
	UploadFileMaxSizeError: 'File terlalu besar. Ukuran file maksimum adalah {fileSizeLimit}.',
	UploadFileSizeLimit: 'Batas ukuran {size}MB',
	UploadFileTileDescription: 'Gunakan file CSV, XLS, XLSX, atau ZIP untuk mengunggah klien Anda.',
	UploadFileTileLabel: 'Unggah Berkas',
	UploadFiles: 'Unggah file',
	UploadIndividually: 'Unggah file satu per satu',
	UploadLogo: 'Unggah logo',
	UploadPhoto: 'Unggah foto',
	UploadToCarepatron: 'Unggah ke Carepatron',
	UploadYourLogo: 'Unggah logo Anda',
	UploadYourTemplates: 'Unggah template Anda dan kami akan mengonversinya untuk Anda',
	Uploading: 'Mengunggah',
	UploadingAudio: 'Mengunggah audio Anda...',
	UploadingFiles: 'Mengunggah file',
	UrlLink: 'Tautan URL',
	UsageCount: 'Digunakan {count} kali',
	UsageLimitValue: '{used} dari {limit} terpakai',
	UsageValue: '{used} sudah digunakan',
	Use: 'Menggunakan',
	UseAiToAutomateYourWorkflow: 'Gunakan AI untuk mengotomatiskan alur kerja Anda!',
	UseAsDefault: 'Gunakan sebagai default',
	UseCustom: 'Gunakan kustom',
	UseDefault: 'Gunakan default',
	UseDefaultFilters: 'Gunakan filter default',
	UseTemplate: 'Gunakan template',
	UseThisCard: 'Gunakan kartu ini',
	UseValue: 'Gunakan "{value}"',
	UseWorkspaceDefault: 'Gunakan ruang kerja default',
	UserIsTyping: '{name} sedang mengetik...',
	Username: 'Nama belakang',
	Users: 'Pengguna',
	VAT: 'TONG',
	ValidUrl: 'Tautan URL harus berupa URL yang valid.',
	Validate: 'Mengesahkan',
	Validated: 'Terverifikasi',
	Validating: 'Memvalidasi',
	ValidatingContent: 'Memvalidasi konten...',
	ValidatingTranscripts: 'Memvalidasi transkrip...',
	ValidationConfirmPasswordRequired: 'Konfirmasi Kata Sandi diperlukan',
	ValidationDateMax: 'Harus sebelum {max}',
	ValidationDateMin: 'Harus setelah {min}',
	ValidationDateRange: 'Tanggal mulai dan berakhir diperlukan',
	ValidationEndDateMustBeAfterStartDate: 'Tanggal akhir harus setelah tanggal mulai',
	ValidationMixedDefault: 'Ini tidak valid',
	ValidationMixedRequired: 'Ini diperlukan',
	ValidationNumberInteger: 'Harus berupa bilangan bulat',
	ValidationNumberMax: 'Harus {max} atau kurang',
	ValidationNumberMin: 'Harus {min} atau lebih',
	ValidationPasswordNotMatching: 'Kata sandi tidak cocok',
	ValidationPrimaryAddressIsRequired: 'Alamat diperlukan saat ditetapkan sebagai default',
	ValidationPrimaryPhoneNumberIsRequired: 'Nomor telepon diperlukan saat ditetapkan sebagai default',
	ValidationServiceMustBeNotBeFuture: 'Layanan tidak boleh di hari ini atau di masa depan',
	ValidationStringEmail: 'Harus berupa email yang valid',
	ValidationStringMax: 'Harus {max} karakter atau kurang',
	ValidationStringMin: 'Harus {min} karakter atau lebih',
	ValidationStringPhoneNumber: 'Harus berupa nomor telepon yang valid',
	ValueMinutes: '{value} menit',
	VerbosityConcise: 'Ringkas',
	VerbosityDetailed: 'Terperinci',
	VerbosityStandard: 'Standar',
	VerbositySuperDetailed: 'Sangat detail',
	VerificationCode: 'Kode verifikasi',
	VerificationEmailDescription:
		'Silakan masukkan alamat email Anda dan kode verifikasi yang baru saja kami kirimkan kepada Anda.',
	VerificationEmailSubtitle: 'Periksa folder Spam - jika email belum sampai',
	VerificationEmailTitle: 'Verifikasi email',
	VerificationOption: 'Verifikasi email',
	Verified: 'Terverifikasi',
	Verify: 'Memeriksa',
	VerifyAndSubmit: 'Verifikasi & kirim',
	VerifyEmail: 'Verifikasi email',
	VerifyEmailAccessCode: 'Kode konfirmasi',
	VerifyEmailAddress: 'Verifikasi alamat email',
	VerifyEmailButton: 'Verifikasi dan keluar',
	VerifyEmailSentSnackbar: 'Email verifikasi telah terkirim. Periksa kotak masuk Anda.',
	VerifyEmailSubTitle: 'Periksa folder Spam jika email belum sampai',
	VerifyEmailSuccessLogOutSnackbar: 'Berhasil! Harap keluar untuk menerapkan perubahan.',
	VerifyEmailSuccessSnackbar:
		'Berhasil! Email telah diverifikasi. Silakan masuk untuk melanjutkan sebagai akun terverifikasi.',
	VerifyEmailTitle: 'Verifikasi email Anda',
	VerifyNow: 'Verifikasi sekarang',
	Veterinarian: 'Dokter hewan',
	VideoCall: 'Panggilan video',
	VideoCallAudioInputFailed: 'Perangkat input audio tidak berfungsi',
	VideoCallAudioInputFailedMessage:
		'Buka pengaturan dan periksa apakah sumber mikrofon Anda sudah diatur dengan benar',
	VideoCallChatBanner:
		'Pesan dapat dilihat oleh semua orang dalam panggilan ini dan akan dihapus saat panggilan berakhir.',
	VideoCallChatSendBtn: 'Kirim pesan',
	VideoCallChatTitle: 'Mengobrol',
	VideoCallDisconnectedMessage: 'Anda kehilangan koneksi jaringan. Mencoba menyambungkan kembali',
	VideoCallOptionInfo: 'Carepatron akan mengelola panggilan video untuk janji temu Anda jika Zoom belum terhubung',
	VideoCallTilePaused: 'Video ini dijeda karena ada masalah dengan jaringan Anda',
	VideoCallTranscriptionFormDescription: 'Anda dapat menyesuaikan pengaturan ini kapan saja',
	VideoCallTranscriptionFormHeading: 'Sesuaikan AI Scribe Anda',
	VideoCallTranscriptionFormLanguageField: 'Bahasa keluaran yang dihasilkan',
	VideoCallTranscriptionFormNoteTemplateField: 'Tetapkan templat catatan default',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Tidak ditemukan template dengan AI',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Pilih template',
	VideoCallTranscriptionPronounField: 'Kata ganti Anda',
	VideoCallTranscriptionRecordingNote:
		'Di akhir sesi, Anda akan menerima <strong>{noteTemplate} catatan</strong> dan transkrip yang dihasilkan.',
	VideoCallTranscriptionReferClientField: 'Merujuk ke Klien sebagai',
	VideoCallTranscriptionReferPractitionerField: 'Lihat Praktisi sebagai',
	VideoCallTranscriptionTitle: 'Juru Tulis AI',
	VideoCallTranscriptionVerbosityField: 'Banyak kata',
	VideoCallTranscriptionWritingPerspectiveField: 'Perspektif penulisan',
	VideoCalls: 'Panggilan video',
	VideoConferencing: 'Konferensi video',
	VideoOff: 'Videonya mati',
	VideoOn: 'Videonya mati',
	VideoQual360: 'Kualitas rendah (360p)',
	VideoQual540: 'Kualitas sedang (540p)',
	VideoQual720: 'Kualitas tinggi (720p)',
	View: 'Melihat',
	ViewAll: 'Lihat semua',
	ViewAppointment: 'Lihat janji temu',
	ViewBy: 'Lihat berdasarkan',
	ViewClaim: 'Lihat klaim',
	ViewCollection: 'Lihat koleksi',
	ViewDetails: 'Lihat detailnya',
	ViewEnrollment: 'Lihat pendaftaran',
	ViewPayment: 'Lihat pembayaran',
	ViewRecord: 'Lihat rekaman',
	ViewRemittanceAdvice: 'Lihat bukti pengiriman uang',
	ViewRemittanceAdviceHeader: 'Klaim bukti transfer',
	ViewRemittanceAdviceSubheader: 'Klaim {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Lihat pengaturan',
	ViewStripeDashboard: 'Lihat dasbor Stripe',
	ViewTemplate: 'Lihat templat',
	ViewTemplates: 'Lihat templat',
	ViewableBy: 'Dapat dilihat oleh',
	ViewableByHelper:
		'Anda dan Tim selalu memiliki akses ke catatan yang Anda publikasikan. Anda dapat memilih untuk membagikan catatan ini dengan klien dan/atau relasinya',
	Viewer: 'Penonton',
	VirtualLocation: 'Lokasi virtual',
	VisibleTo: 'Terlihat Oleh',
	VisitOurHelpCentre: 'Kunjungi pusat bantuan kami',
	VisualEffects: 'Efek visual',
	VoiceFocus: 'Fokus suara',
	VoiceFocusLabel: 'Menyaring suara dari mikrofon Anda yang bukan ucapan',
	Void: 'Ruang kosong',
	VoidCancelPriorClaim: 'Void/Batalkan klaim sebelumnya',
	WaitingforMins: 'Menunggu selama {count} menit',
	Warning: 'Peringatan',
	WatchAVideo: 'Tonton video',
	WatchDemoVideo: 'Tonton video demo',
	WebConference: 'Konferensi web',
	WebConferenceOrVirtualLocation: 'Konferensi web / lokasi virtual',
	WebDeveloper: 'Pengembang Web',
	WebsiteOptional: 'Situs web <span>(Opsional)</span>',
	WebsiteUrl: 'URL situs web',
	Wednesday: 'Rabu',
	Week: 'Pekan',
	WeekPlural: '{count, plural, one {minggu} other {minggu}}',
	Weekly: 'Mingguan',
	WeeksPlural: '{age, plural, one {# minggu} other {# minggu}}',
	WelcomeBack: 'Selamat Datang kembali',
	WelcomeBackName: 'Selamat datang kembali, {name}',
	WelcomeName: 'Selamat datang {name}',
	WelcomeToCarepatron: 'Selamat datang di Carepatron',
	WhatCanIHelpWith: 'Apa yang bisa saya bantu?',
	WhatDidYouLikeResponse: 'Apa yang Anda sukai dari respons ini?',
	WhatIsCarepatron: 'Apa itu Carepatron?',
	WhatMadeYouCancel: `Apa yang membuat Anda membatalkan rencana Anda?
 Centang semua yang berlaku.`,
	WhatServicesDoYouOffer: 'Apa<mark> layanan</mark> apakah anda menawarkan?',
	WhatServicesDoYouOfferDescription: 'Anda dapat mengedit atau menambahkan lebih banyak layanan nanti.',
	WhatsYourAvailability: 'Ketersediaan <mark>Anda</mark> bagaimana?',
	WhatsYourAvailabilityDescription: 'Anda dapat menambahkan lebih banyak jadwal nanti.',
	WhatsYourBusinessName: 'Apa kabarmu?<mark> nama bisnis?</mark>',
	WhatsYourTeamSize: 'Apa kabarmu?<mark> ukuran tim?</mark>',
	WhatsYourTeamSizeDescription: 'Ini akan membantu kami menyiapkan ruang kerja Anda dengan benar.',
	WhenThisHappens: 'Ketika hal ini terjadi:',
	WhichBestDescribesYou: 'Yang mana yang terbaik<mark> menggambarkan Anda?</mark>',
	WhichPlatforms: 'Platform apa saja?',
	Wife: 'Istri',
	WorkflowDescription: 'Deskripsi alur kerja',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Template dapat terhubung ke alur kerja untuk proses yang lebih lancar. Lihat alur kerja yang terhubung untuk melacak dan memperbaruinya dengan mudah.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'Hubungkan SMS + email Anda berdasarkan pemicu umum',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Otomasi Alur Kerja',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Alur kerja otomatis',
	WorkflowTemplateConfigKey_Body: 'Badan',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Tampilkan branding',
	WorkflowTemplateConfigKey_Content: 'Konten',
	WorkflowTemplateConfigKey_Footer: 'Footer',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Tampilkan footer',
	WorkflowTemplateConfigKey_Header: 'Header',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Tampilkan header',
	WorkflowTemplateConfigKey_SecurityFooter: 'Kaki halaman Keamanan',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Tampilkan footer keamanan',
	WorkflowTemplateConfigKey_Subject: 'Perihal',
	WorkflowTemplateConfigKey_Title: 'Judul',
	WorkflowTemplateDeleteConfirmationMessage:
		'Apakah Anda yakin ingin menghapus templat ini? Tindakan ini tidak dapat dibatalkan.',
	WorkflowTemplateDeleteConfirmationTitle: 'Hapus template notifikasi',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Apakah Anda yakin? Ini hanya akan menghapus versi {locale}—bahasa lain tidak akan terpengaruh. Tindakan ini tidak dapat dibatalkan.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Hapus template ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Templat notifikasi berhasil dihapus',
	WorkflowTemplateEditorDetailsTab: 'Rincian Templat',
	WorkflowTemplateEditorEmailContent: 'Konten Email',
	WorkflowTemplateEditorEmailContentTab: 'Konten email',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert:
		'Pratinjau menggunakan data sampel untuk menunjukkan apa yang akan dilihat klien Anda.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Apakah Anda yakin? Ini akan mengatur ulang versi kembali ke templat default sistem. Tindakan ini tidak dapat dibatalkan.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Reset template',
	WorkflowTemplateSendTestEmail: 'Kirim email uji',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Coba pengaturan email Anda dengan mengirimkan email uji ke diri Anda sendiri.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Email Penerima',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Kirim tes',
	WorkflowTemplateSendTestEmailDialogTitle: 'Kirim email uji',
	WorkflowTemplateSendTestEmailSuccess: 'Sukses! Email uji <mark>{templateName}</mark> Anda telah terkirim.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Kelola templat Anda dan tambahkan beberapa versi bahasa untuk berkomunikasi secara efektif dengan klien.',
	WorkflowTemplateTemplateEditor: 'Editor Templat',
	WorkflowTemplateTranslateLocaleError: 'Terjadi kesalahan saat menerjemahkan konten',
	WorkflowTemplateTranslateLocaleSuccess: 'Berhasil menerjemahkan konten ke **{locale}**',
	WorkflowsAndReminders: 'Alur kerja ',
	WorkflowsManagement: 'Manajemen Alur Kerja',
	WorksheetAndHandout: 'Lembar Kerja/Bagikan',
	WorksheetsAndHandoutsDescription: 'Untuk keterlibatan dan edukasi klien',
	Workspace: 'Ruang kerja',
	WorkspaceBranding: 'Pencitraan merek ruang kerja',
	WorkspaceBrandingDescription: `Berikan merek pada ruang kerja Anda dengan mudah dengan gaya kohesif yang mencerminkan
 profesionalisme dan kepribadian. Sesuaikan faktur dengan pemesanan online untuk tampilan yang indah
 pengalaman pelanggan.`,
	WorkspaceName: 'Nama ruang kerja',
	Workspaces: 'Ruang kerja',
	WriteOff: 'Penghapusan',
	WriteOffModalDescription:
		'Anda memiliki <mark>{count} {count, plural, one {item baris} other {item baris}}</mark> yang harus dihapuskan',
	WriteOffModalTitle: 'Penyesuaian penghapusan',
	WriteOffReasonHelperText: 'Ini adalah catatan internal dan tidak akan terlihat oleh klien Anda.',
	WriteOffReasonPlaceholder:
		'Menambahkan alasan penghapusan dapat membantu saat meninjau transaksi yang dapat ditagih',
	WriteOffTotal: 'Total pembatalan ({currencyCode})',
	Writer: 'Penulis',
	Yearly: 'Tahunan',
	YearsPlural: '{age, plural, one {# tahun} other {# tahun}}',
	Yes: 'Ya',
	YesArchive: 'Ya, arsipkan',
	YesDelete: 'Ya, hapus',
	YesDeleteOverride: 'Ya, hapus penggantian',
	YesDeleteSection: 'Ya, hapus',
	YesDisconnect: 'Ya, putuskan koneksi',
	YesEnd: 'Ya, selesai',
	YesEndTranscription: 'Ya, akhiri transkripsi',
	YesImFineWithThat: 'Ya, aku baik-baik saja dengan itu',
	YesLeave: 'Ya, pergi',
	YesMinimize: 'Ya, minimalkan',
	YesOrNoAnswerTypeDescription: 'Konfigurasikan jenis jawaban',
	YesOrNoFormPrimaryText: 'Ya | Tidak',
	YesOrNoFormSecondaryText: 'Pilih opsi ya atau tidak',
	YesProceed: 'Ya, lanjutkan',
	YesRemove: 'Ya, hapus',
	YesRestore: 'Ya, pulihkan',
	YesStopIgnoring: 'Ya, berhentilah mengabaikannya',
	YesTransfer: 'Ya, transfer',
	Yesterday: 'Kemarin',
	YogaInstructor: 'Instruktur Yoga',
	You: 'Anda',
	YouArePresenting: 'Anda sedang melakukan presentasi',
	YouCanChooseMultiple: 'Anda dapat memilih beberapa',
	YouCanSelectMultiple: 'Anda dapat memilih beberapa',
	YouHaveOngoingTranscription: 'Anda memiliki transkripsi yang sedang berlangsung',
	YourAnswer: 'Jawaban Anda',
	YourDisplayName: 'Nama tampilan Anda',
	YourSpreadsheetColumns: 'Kolom spreadsheet Anda',
	YourTeam: 'Tim Anda',
	ZipCode: 'Kode pos',
	Zoom: 'Perbesar',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Anda tidak dapat menambahkan panggilan Zoom untuk anggota tim ini. Silakan lihat <a>dokumen dukungan untuk informasi lebih lanjut.</a>',
};

export default items;
