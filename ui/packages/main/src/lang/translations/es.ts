import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'ABN',
	AIPrompts: 'Avisos de IA',
	ATeamMemberIsRequired: 'Se necesita un miembro del equipo',
	AboutClient: 'Acerca del cliente',
	AcceptAppointment: 'Grac<PERSON> por confirmar su cita',
	AcceptTermsAndConditionsRequired: 'Se requiere aceptar los Términos y Condiciones',
	Accepted: 'Aceptado',
	AccessGiven: 'Acceso concedido',
	AccessPermissions: 'Permisos de acceso',
	AccessType: 'Tipo de acceso',
	Accident: 'Accidente',
	Account: 'Cuenta',
	AccountCredit: 'Crédito de cuenta',
	Accountant: 'Contable',
	Action: 'Acción',
	Actions: 'Comportamiento',
	Active: 'Activo',
	ActiveTags: 'Etiquetas activas',
	ActiveUsers: 'Usuarios activos',
	Activity: 'Actividad',
	Actor: 'Actor',
	Acupuncture: 'Acupuntura',
	Acupuncturist: 'Acupuntor',
	Acupuncturists: 'Acupuncturists',
	AcuteManifestationOfAChronicCondition: 'Manifestación aguda de una enfermedad crónica',
	Add: 'Agregar',
	AddADescription: 'Agregar una descripción',
	AddALocation: 'Agregar una ubicación',
	AddASecondTimezone: 'Agregar una segunda zona horaria',
	AddAddress: 'Agregar dirección',
	AddAnother: '+ Agregar otro',
	AddAnotherAccount: 'Add another account',
	AddAnotherContact: 'Añadir otro contacto',
	AddAnotherOption: 'Añadir otra opción',
	AddAnotherTeamMember: 'Agregar otro miembro del equipo',
	AddAvailablePayers: '+ Añadir pagadores disponibles',
	AddAvailablePayersDescription:
		'Busca pagadores para agregar a la lista de pagadores de tu espacio de trabajo. Después de agregarlos, puedes administrar las inscripciones o ajustar los detalles del pagador según sea necesario.',
	AddCaption: 'Añadir un subtitulo',
	AddClaim: 'Agregar reclamo',
	AddClientFilesModalDescription: 'Para restringir el acceso, elija las opciones en las casillas "Visto por"',
	AddClientFilesModalTitle: 'Cargar archivos para {name}',
	AddClientNoteButton: 'Agregar nota',
	AddClientNoteModalDescription:
		'Agregue contenido a su nota. Use la sección "Visible por" para seleccionar uno o más grupos que puedan ver esta nota específica.',
	AddClientNoteModalTitle: 'Agregar nota',
	AddClientOwnerRelationshipModalDescription:
		'Invitar al cliente les permitirá gestionar su propia información de perfil y el acceso de usuario a su información de perfil.',
	AddClientOwnerRelationshipModalTitle: 'Invitar al cliente',
	AddCode: 'Agregar código',
	AddColAfter: 'Añadir columna después',
	AddColBefore: 'Agregar columna antes',
	AddCollection: 'Agregar colección',
	AddColor: 'Añadir color',
	AddColumn: 'Agregar columna',
	AddContactRelationship: 'Agregar relación de contacto',
	AddContacts: 'Agregar contactos',
	AddCustomField: 'Agregar campo personalizado',
	AddDate: 'Agregar fecha',
	AddDescription: 'Agregar descripción',
	AddDetail: 'Agregar detalle',
	AddDisplayName: 'Agregar nombre para mostrar',
	AddDxCode: 'Agregar código de diagnóstico',
	AddEmail: 'Agregar correo electrónico',
	AddFamilyClientRelationshipModalDescription:
		'Invitar a un miembro de la familia les permitirá ver historias de cuidado e información de perfil del cliente. Si se les invita como administradores, tendrán acceso para actualizar la información de perfil del cliente y gestionar el acceso de usuario.',
	AddFamilyClientRelationshipModalTitle: 'Invitar a un miembro de la familia',
	AddField: 'Agregar campo',
	AddFormField: 'Agregar campo de formulario',
	AddImages: 'Agregar imágenes',
	AddInsurance: 'Añadir seguro',
	AddInvoice: 'Agregar factura',
	AddLabel: 'Añadir etiqueta',
	AddLanguage: 'Agregar idioma',
	AddLocation: 'Agregar direccion',
	AddManually: 'Añadir manualmente',
	AddMessage: 'Agregar mensaje',
	AddNewAction: 'Añadir nueva acción',
	AddNewSection: 'Agregar nueva sección',
	AddNote: 'Agregar nota',
	AddOnlineBookingDetails: 'Agregar detalles de reserva en línea',
	AddPOS: 'Añadir POS',
	AddPaidInvoices: 'Agregar facturas pagadas',
	AddPayer: 'Agregar pagador',
	AddPayment: 'Agregar pago',
	AddPaymentAdjustment: 'Agregar ajuste de pago',
	AddPaymentAdjustmentDisabledDescription: 'Las asignaciones de pagos no se modificarán.',
	AddPaymentAdjustmentEnabledDescription: 'Se reducirá la cantidad disponible para asignar.',
	AddPhoneNumber: 'Agregar número de teléfono',
	AddPhysicalOrVirtualLocations: 'Agregar ubicaciones físicas o virtuales',
	AddQuestion: 'Agregar pregunta',
	AddQuestionOrTitle: 'Agregar una pregunta o un título',
	AddRelationship: 'Agregar relación',
	AddRelationshipModalTitle: 'Conectar contacto existente',
	AddRelationshipModalTitleNewClient: 'Conectar nuevo contacto',
	AddRow: 'Agregar fila',
	AddRowAbove: 'Agregar fila arriba',
	AddRowBelow: 'Agregar fila a continuación',
	AddService: 'Agregar servicio',
	AddServiceLocation: 'Agregar ubicación del servicio',
	AddServiceToCollections: 'Añadir servicio a las colecciones',
	AddServiceToOneOrMoreCollections: 'Añadir servicio a una o varias colecciones',
	AddServices: 'Agregar servicios',
	AddSignature: 'Agregar firma',
	AddSignaturePlaceholder: 'Escriba detalles adicionales para incluirlos con su firma',
	AddSmartDataChips: 'Agregar chips de datos inteligentes',
	AddStaffClientRelationshipsModalDescription:
		'Al seleccionar el personal, se les permitirá crear y ver historias de atención para este cliente. También podrán ver la información del cliente.',
	AddStaffClientRelationshipsModalTitle: 'Agregar relaciones del personal',
	AddTag: 'Agregar etiqueta',
	AddTags: 'Agregar etiquetas',
	AddTemplate: 'Añadir plantilla',
	AddTimezone: 'Agregar zona horaria',
	AddToClaim: 'Añadir a la reclamación',
	AddToCollection: 'Añadir a la colección',
	AddToExisting: 'Añadir a existente',
	AddToStarred: 'Añadir a favoritos',
	AddUnclaimedItems: 'Agregar artículos no reclamados',
	AddUnrelatedContactWarning:
		'Has añadido a alguien que no es contacto de {contact}. Asegúrese de que el contenido sea relevante antes de proceder a compartirlo.',
	AddValue: 'Añade "{value}"',
	AddVideoCall: 'Añadir videollamada',
	AddVideoOrVoiceCall: 'Añadir llamada de vídeo o voz',
	AddictionCounselor: 'Asesor en adicciones',
	AddingManualPayerDisclaimer:
		'Agregar un pagador manualmente a su lista de proveedores no establece una conexión para el envío electrónico de reclamaciones con ese pagador, pero se puede usar para crear reclamaciones manualmente.',
	AddingTeamMembersIncreaseCostAlert: 'Agregar nuevos miembros al equipo aumentará su suscripción mensual. ',
	Additional: 'Adicional',
	AdditionalBillingProfiles: 'Perfiles de facturación adicionales',
	AdditionalBillingProfilesSectionDescription:
		'Anular la información de facturación predeterminada utilizada para miembros del equipo, pagadores o plantillas de facturas específicos.',
	AdditionalFeedback: 'Comentarios adicionales',
	AddnNewWorkspace: 'Nuevo espacio de trabajo',
	AddnNewWorkspaceSuccessSnackbar: '¡Se ha creado el espacio de trabajo!',
	Address: 'Vínculo',
	AddressNumberStreet: 'Dirección (Nº, calle)',
	Adjustment: 'Ajuste',
	AdjustmentType: 'Tipo de ajuste',
	Admin: 'Administrador',
	Admins: 'Administradores',
	AdminsOnly: 'Solo administradores',
	AdvancedPlanInclusionFive: 'Gerente de cuentas',
	AdvancedPlanInclusionFour: 'Análisis de Google',
	AdvancedPlanInclusionHeader: 'Todo en Plus  ',
	AdvancedPlanInclusionOne: 'Roles ',
	AdvancedPlanInclusionSix: 'Soporte de importación de datos',
	AdvancedPlanInclusionThree: 'Marcado blanco',
	AdvancedPlanInclusionTwo: 'Retención de datos eliminados durante 90 días',
	AdvancedPlanMessage:
		'Mantenga el control de las necesidades de su práctica. Revise su plan actual y supervise el uso.',
	AdvancedSettings: 'Ajustes avanzados',
	AdvancedSubscriptionPlanSubtitle: 'Amplíe su práctica con todas las funciones',
	AdvancedSubscriptionPlanTitle: 'Avanzado',
	AdvertisingManager: 'Director de publicidad',
	AerospaceEngineer: 'Ingeniero aeroespacial',
	AgeYearsOld: '{age} años',
	Agenda: 'Orden del día',
	AgendaView: 'Agenda',
	AiAskSupportedFileTypes: 'Tipos de archivos admitidos: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Un asistente a tu alcance',
	AiCopilotDisclaimer: 'AI Copilot puede cometer errores. Consulta la información importante.',
	AiCreateNewConversation: 'Crear nueva conversación',
	AiEnhanceYourProductivity: 'Mejora tu productividad',
	AiPoweredTemplates: 'Plantillas impulsadas por IA',
	AiScribeNoDeviceFoundErrorMessage:
		'Parece que su navegador no admite esta función o no hay dispositivos compatibles disponibles.',
	AiScribeUploadFormat: 'Tipos de archivos admitidos: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'solo 1 archivo a la vez',
	AiShowConversationHistory: 'Mostrar historial de conversación',
	AiSmartPromptNodePlaceholderText:
		'Escribe tu indicación personalizada aquí para ayudar a generar resultados de IA precisos y personalizados.',
	AiSmartPromptPrimaryText: 'Ai solicitud inteligente',
	AiSmartPromptSecondaryText: 'Inserte una indicación inteligente de IA personalizada',
	AiSmartReminders: 'Recordatorios inteligentes con IA',
	AiTemplateBannerTitle: 'Simplifique su trabajo con plantillas impulsadas por IA',
	AiTemplates: 'Plantillas de IA',
	AiTokens: 'Tokens de IA',
	AiWorkBetterWithAi: 'Trabaja mejor con IA',
	All: 'Todos',
	AllAppointments: 'Todas las citas',
	AllCategories: 'Todas las categorías',
	AllClients: 'Todos los clientes',
	AllContactPolicySelectorLabel: 'Todos los contactos de <mark>{client}</mark>',
	AllContacts: 'Todos los contactos',
	AllContactsOf: 'Todos los contactos de ‘{name}’ ',
	AllDay: 'Todo el día',
	AllInboxes: 'Todas las bandejas de entrada',
	AllIndustries: 'Todas las industrias',
	AllLocations: 'Todas las ubicaciones',
	AllMeetings: 'Todas las reuniones',
	AllNotificationsRestoredMessage: 'Todas las notificaciones restauradas',
	AllProfessions: 'Todas las profesiones',
	AllReminders: 'Todos los recordatorios',
	AllServices: 'Todos los servicios',
	AllStatuses: 'Todos los estados',
	AllTags: 'Todos los tags',
	AllTasks: 'Todas las tareas',
	AllTeamMembers: 'Todos los miembros del equipo',
	AllTypes: 'Todos los tipos',
	Allocated: 'Asignado',
	AllocatedItems: 'Elementos asignados',
	AllocationTableEmptyState: 'No se encontraron asignaciones de pago',
	AllocationTotalWarningMessage: `El monto asignado excede el monto total del pago.
 Por favor revise las líneas de ítems a continuación.`,
	AllowClientsToCancelAnytime: 'Permitir que los clientes cancelen en cualquier momento',
	AllowNewClient: 'Permitir nuevos clientes',
	AllowNewClientHelper: 'Los nuevos clientes pueden reservar este servicio',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Permitir al menos {hours} horas antes de la cita',
	AllowToUseSavedCard: 'Permitir que {provider} use la tarjeta guardada en el futuro',
	AllowVideoCalls: 'Permitir videollamadas',
	AlreadyAdded: 'Ya añadido',
	AlreadyHasAccess: 'Tiene acceso',
	AlreadyHasAccount: '¿Ya tienes una cuenta?',
	Always: 'Siempre',
	AlwaysIgnore: 'Siempre ignorar',
	Amount: 'Cantidad',
	AmountDue: 'Importe adeudado',
	AmountOfReferralRequests: '{amount, plural, one {# solicitud de referencia} other {# solicitudes de referencia}}',
	AmountPaid: 'Importe pagado',
	AnalyzingAudio: 'Analizando audio...',
	AnalyzingInputContent: 'Analizando el contenido de entrada...',
	AnalyzingRequest: 'Analizando solicitud...',
	AnalyzingTemplateContent: 'Analizando el contenido de la plantilla...',
	And: 'y',
	Annually: 'Anualmente',
	Anonymous: 'Anónimo',
	AnswerExceeded: 'La respuesta debe tener menos de 300 caracteres.',
	AnyStatus: 'Cualquier estado',
	AppNotifications: 'Notificaciones',
	AppNotificationsClearanceHeading: '¡Buen trabajo! Has eliminado toda la actividad.',
	AppNotificationsEmptyHeading: 'La actividad de tu espacio de trabajo aparecerá aquí en breve',
	AppNotificationsEmptySubtext: 'No hay acciones a tomar por ahora',
	AppNotificationsIgnoredCount: '{total} ignored',
	AppNotificationsUnread: '{total} no leído',
	Append: 'Añadir',
	Apply: 'Aplicar',
	ApplyAccountCredit: 'Aplicar crédito de cuenta',
	ApplyDiscount: 'Aplicar descuento',
	ApplyVisualEffects: 'Aplicar efectos visuales',
	ApplyVisualEffectsNotSupported: 'Aplicar efectos visuales no es compatible',
	Appointment: 'Cita',
	AppointmentAssignedNotificationSubject: '{actorProfileName} te ha asignado {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} ha cancelado {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} ha confirmado {appointmentName}',
	AppointmentDetails: 'Detalles de la cita',
	AppointmentLocation: 'Ubicación de la cita',
	AppointmentLocationDescription:
		'Administre sus ubicaciones virtuales y físicas predeterminadas. Cuando se programe una cita, estas ubicaciones se aplicarán automáticamente.',
	AppointmentNotFound: 'Cita no encontrada',
	AppointmentReminder: 'Recordatorio de cita',
	AppointmentReminders: 'Recordatorios de citas',
	AppointmentRemindersInfo:
		'Establezca recordatorios automáticos para las citas de los clientes para evitar ausencias y cancelaciones',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} ha reprogramado {appointmentName}',
	AppointmentSaved: 'Cita guardada',
	AppointmentStatus: 'Estado del nombramiento',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0mins} other {{minutes}mins}} } one {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} other {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} }',
	AppointmentUndone: 'Cita deshecha',
	Appointments: 'Citas',
	Archive: 'Archivar',
	ArchiveClients: 'Clientes de archivo',
	Archived: 'Archivado',
	AreYouAClient: '¿Es usted cliente?',
	AreYouStillThere: '¿Sigues ahí?',
	AreYouSure: '¿Estás seguro/a?',
	Arrangements: 'Arreglos',
	ArtTherapist: 'Terapeuta artístico',
	Articles: 'Artículos',
	Artist: 'Artista',
	AskAI: 'Preguntar AI',
	AskAiAddFormField: 'Agregar un campo de formulario',
	AskAiChangeFormality: 'Cambiar formalidad',
	AskAiChangeToneToBeMoreProfessional: 'Cambia el tono para ser más profesional',
	AskAiExplainThis: 'Pregúntale a AiExplícalo',
	AskAiExplainWhatThisDocumentIsAbout: 'Explique de qué trata este documento',
	AskAiExplainWhatThisImageIsAbout: 'Explica de qué se trata esta imagen',
	AskAiFixSpellingAndGrammar: 'Corregir ortografía y gramática',
	AskAiGenerateACaptionForThisImage: 'Generar un título para esta imagen',
	AskAiGenerateFromThisPage: 'Generar desde esta página',
	AskAiGetStarted: 'Empezar',
	AskAiGiveItAFriendlyTone: 'Dale un tono amigable',
	AskAiGreeting: '¡Hola {firstName}! ¿Cómo puedo ayudarte hoy?',
	AskAiHowCanIHelpWithYourContent: '¿Cómo puedo ayudarte con tu contenido?',
	AskAiInsert: 'Insertar',
	AskAiMakeItMoreCasual: 'Hazlo más informal',
	AskAiMakeThisTextMoreConcise: 'Haz este texto más conciso',
	AskAiMoreProfessional: 'Más profesional',
	AskAiOpenPreviousNote: 'Abrir nota anterior',
	AskAiPondering: 'Reflexionando',
	AskAiReplace: 'Reemplazar',
	AskAiReviewOrEditSelection: 'Revisar o editar la selección',
	AskAiRuminating: 'Rumiando',
	AskAiSeeMore: 'Ver más',
	AskAiSimplifyLanguage: 'Simplificar el lenguaje',
	AskAiSomethingWentWrong:
		'Algo salió mal. Si este problema persiste, por favor contáctanos a través de nuestro centro de ayuda.',
	AskAiStartWithATemplate: 'Empezar con una plantilla',
	AskAiSuccessfullyCopiedResponse: 'Respuesta de IA copiada con éxito',
	AskAiSuccessfullyInsertedResponse: 'Respuesta de IA insertada con éxito',
	AskAiSuccessfullyReplacedResponse: 'Respuesta de IA reemplazada con éxito',
	AskAiSuggested: 'Sugerido',
	AskAiSummariseTextIntoBulletPoints: 'Resumir el texto en viñetas',
	AskAiSummarizeNote: 'Nota resumida',
	AskAiThinking: 'Pensamiento',
	AskAiToday: 'Hoy {time}',
	AskAiWhatDoYouWantToDoWithThisForm: '¿Qué desea hacer con este formulario?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Escribe una nota profesional usando la plantilla',
	AskAskAiAnything: 'Pregúntale cualquier cosa a la IA',
	AskWriteSearchAnything: `Pregunta, escribe '@' o busca cualquier cosa...`,
	Asking: 'Preguntando',
	Assessment: 'Evaluación',
	Assessments: 'Evaluaciones',
	AssessmentsCategoryDescription: 'Para registrar las evaluaciones de los clientes',
	AssignClients: 'Asignar clientes',
	AssignNewClients: 'Asignar clientes',
	AssignServices: 'Asignar servicios',
	AssignTeam: 'Asignar equipo',
	AssignTeamMember: 'Asignar miembro del equipo',
	Assigned: 'Asignado',
	AssignedClients: 'Clientes asignados',
	AssignedServices: 'Servicios asignados',
	AssignedServicesDescription:
		'Ver y administrar tus servicios asignados, ajustando los precios para reflejar tus tarifas personalizadas.',
	AssignedTeam: 'Equipo asignado',
	AthleticTrainer: 'Entrenador de atletismo',
	AttachFiles: 'Adjuntar archivos',
	AttachLogo: 'Adjuntar',
	Attachment: 'Adjunto',
	AttachmentBlockedFileType: 'Bloqueado por motivos de seguridad!',
	AttachmentTooLargeFileSize: 'Fichero demasiado grande',
	AttachmentUploadItemComplete: 'Completo',
	AttachmentUploadItemError: 'Error al cargar',
	AttachmentUploadItemLoading: 'Cargando',
	AttemptingToReconnect: 'Intentando reconectar...',
	Attended: 'Asistió',
	AttendeeBeingMutedTooltip:
		'El anfitrión te ha silenciado. Usa "levantar la mano" para solicitar que se active el sonido.',
	AttendeeWithId: 'Asistente {attendeeId}',
	Attendees: 'Asistentes',
	AttendeesCount: '{count} asistentes',
	Attending: 'Asistiendo',
	Audiologist: 'Audiólogo',
	Aunt: 'Tía',
	Australia: 'Australia',
	AuthenticationCode: 'Código de autenticación',
	AuthoriseProvider: 'Autorizar {proveedor}',
	AuthorisedProviders: 'Proveedores autorizados',
	AutoDeclineAllFutureOption: 'Solo eventos o citas nuevas',
	AutoDeclineAllOption: 'Eventos y citas nuevos y existentes',
	AutoDeclinePrimaryText: 'Rechazar eventos automáticamente',
	AutoDeclineSecondaryText: 'Los eventos durante tu periodo de ausencia se rechazarán automáticamente',
	AutogenerateBillings: 'Generar documentos de facturación automáticamente',
	AutogenerateBillingsDescription:
		'Los documentos de facturación automatizados se generarán el último día del mes. Las facturas y los recibos de Superbill se pueden crear manualmente en cualquier momento.',
	AutomateWorkflows: 'Automatizar flujos de trabajo',
	AutomaticallySendSuperbill: 'Enviar recibos de Superbill automáticamente',
	AutomaticallySendSuperbillHelperText:
		'Un recibo de Superbill es un recibo detallado de los servicios proporcionados a un cliente para el reembolso del seguro',
	Automation: 'Automatización',
	AutomationActionSendEmailLabel: 'Enviar correo electrónico',
	AutomationActionSendSMSLabel: 'Enviar SMS',
	AutomationAndReminders: 'Automatización ',
	AutomationDeletedSuccessMessage: 'Automatización eliminada con éxito',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Evento temporal',
	AutomationParams_timeUnit: 'Unidad',
	AutomationParams_timeValue: 'Número',
	AutomationPublishSuccessMessage: 'Automatización publicada con éxito',
	AutomationPublishWarningTooltip:
		'Vuelva a verificar la configuración de automatización y asegúrese de que se haya configurado correctamente.',
	AutomationTriggerEventCancelledDescription: 'Se activa cuando se cancela o elimina un evento',
	AutomationTriggerEventCancelledLabel: 'Evento cancelado',
	AutomationTriggerEventCreatedDescription: 'Se activa cuando se crea un evento',
	AutomationTriggerEventCreatedLabel: 'Nuevo evento',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Se activa cuando se crea o actualiza un evento (excepto cuando se cancela)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Evento nuevo o actualizado',
	AutomationTriggerEventEndedDescription: 'Se activa cuando finaliza un evento',
	AutomationTriggerEventEndedLabel: 'Evento finalizado',
	AutomationTriggerEventStartsDescription:
		'Se activa cuando transcurre un período de tiempo específico antes de que comience un evento.',
	AutomationTriggerEventStartsLabel: 'Comienza el evento',
	Automations: 'Automatizaciones',
	Availability: 'Disponibilidad',
	AvailabilityDisableSchedule: 'Deshabilitar horario',
	AvailabilityDisabled: 'Deshabilitado',
	AvailabilityEnableSchedule: 'Habilitar horario',
	AvailabilityEnabled: 'Habilitado',
	AvailabilityNoActiveBanner:
		'Has desactivado todos tus horarios. Los clientes no pueden reservarte en línea, y todas las citas futuras deben ser confirmadas manualmente.',
	AvailabilityNoActiveConfirmationDescription:
		'Deshabilitar esta disponibilidad resultará en no tener horarios activos. Los clientes no podrán reservar citas en línea, y cualquier reserva realizada por los practicantes estará fuera de tu horario laboral.',
	AvailabilityNoActiveConfirmationProceed: 'Sí, proceder',
	AvailabilityNoActiveConfirmationTitle: 'No hay horarios activos',
	AvailabilityToggle: 'Alternar disponibilidad',
	AvailabilityUnsetDate: 'Sin fecha establecida',
	AvailableLocations: 'Ubicaciones disponibles',
	AvailablePayers: 'Pagadores disponibles',
	AvailablePayersEmptyState: 'No se han seleccionado pagadores',
	AvailableTimes: 'Tiempos disponibles',
	Back: 'Atrás',
	BackHome: 'Volver a casa',
	BackToAppointment: 'Volver a la cita',
	BackToLogin: 'Volver a iniciar sesión',
	BackToMapColumns: 'Volver a las columnas del Mapa',
	BackToTemplates: 'Volver a Plantillas',
	BackToUploadFile: 'Volver a Cargar archivo',
	Banker: 'Banquero',
	BasicBlocks: 'Bloques Básicos',
	BeforeAppointment: 'Enviar recordatorio de {deliveryType} {interval} {unit} antes de la cita',
	BehavioralAnalyst: 'Analista del comportamiento',
	BehavioralHealthTherapy: 'Terapia conductual',
	Beta: 'Beta',
	BillTo: 'Facturar a',
	BillableItems: 'Artículos facturables',
	BillableItemsEmptyState: 'Es wurden keine abrechenbaren Positionen gefunden',
	Biller: 'Facturación',
	Billing: 'Facturación',
	BillingAddress: 'Dirección de facturación',
	BillingAndReceiptsUnauthorisedMessage:
		'Se requiere acceso para ver facturas y pagos para acceder a esta información.',
	BillingBillablesTab: 'Facturables',
	BillingClaimsTab: 'Reclamos',
	BillingDetails: 'Datos de facturación',
	BillingDocuments: 'Documentos de facturación',
	BillingDocumentsClaimsTab: 'Reclamaciones',
	BillingDocumentsEmptyState: 'No se han encontrado {tabType}',
	BillingDocumentsInvoicesTab: 'Facturas',
	BillingDocumentsSuperbillsTab: 'Superfacturas',
	BillingInformation: 'Información de facturación',
	BillingInvoicesTab: 'Facturas',
	BillingItems: 'Artículos de facturación',
	BillingPaymentsTab: 'Pagos',
	BillingPeriod: 'Período de facturación',
	BillingProfile: 'Perfil de facturación',
	BillingProfileOverridesDescription: 'Limite el uso de este perfil de facturación a miembros específicos del equipo',
	BillingProfileOverridesHeader: 'Limitar acceso',
	BillingProfileProviderType: 'Tipo de proveedor',
	BillingProfileTypeIndividual: 'Facultativo',
	BillingProfileTypeIndividualSubLabel: 'Tipo 1 NPI',
	BillingProfileTypeOrganisation: 'Organización',
	BillingProfileTypeOrganisationSubLabel: 'Tipo 2 NPI',
	BillingProfiles: 'Perfiles de facturación',
	BillingProfilesEditHeader: 'Editar el perfil de facturación de {name}',
	BillingProfilesNewHeader: 'Nuevo perfil de facturación',
	BillingProfilesSectionDescription:
		'Administre su información de facturación para profesionales y aseguradoras configurando perfiles de facturación que se puedan aplicar a facturas y pagos de seguros.',
	BillingSearchPlaceholder: 'Buscar artículos',
	BillingSettings: 'Configuración de facturación',
	BillingSuperbillsTab: 'Superfacturas',
	BiomedicalEngineer: 'Ingeniero biomédico',
	BlankInvoice: 'Factura en blanco',
	BlueShieldProviderNumber: 'Número de proveedor de Blue Shield',
	Body: 'Mensaje',
	Bold: 'Negrita',
	BookAgain: 'Reservar de nuevo',
	BookAppointment: 'Reservar cita',
	BookableOnline: 'Reservable en línea',
	BookableOnlineHelper: 'Los clientes pueden reservar este servicio en línea',
	BookedOnline: 'Reservado en línea',
	Booking: 'Reservas',
	BookingAnalyticsIntegrationPanelDescription:
		'Configura Google Tag Manager para realizar un seguimiento de las acciones y conversiones clave en tu flujo de reservas online. Recopila datos valiosos sobre las interacciones de los usuarios para mejorar las iniciativas de marketing y optimizar la experiencia de reserva.',
	BookingAnalyticsIntegrationPanelTitle: 'Integración de análisis',
	BookingAndCancellationPolicies: 'Políticas de reserva y cancelación',
	BookingButtonEmbed: 'Botón',
	BookingButtonEmbedDescription: 'Agrega un botón de reserva en línea a tu sitio web',
	BookingDirectTextLink: 'Enlace de texto directo',
	BookingDirectTextLinkDescription: 'Abre la página de reserva en línea',
	BookingFormatLink: 'Formato de enlace',
	BookingFormatLinkButtonTitle: 'Título del botón',
	BookingInlineEmbed: 'Incrustación en línea',
	BookingInlineEmbedDescription: 'Carga la página de reserva en línea directamente en tu sitio web',
	BookingLink: 'Enlace de reserva',
	BookingLinkModalCopyText: 'Copiar',
	BookingLinkModalDescription:
		'Permite a los clientes con este enlace reservar cualquier miembro del equipo o servicios',
	BookingLinkModalHelpText: 'Aprende cómo configurar las reservas en línea',
	BookingLinkModalTitle: 'Comparte tu enlace de reserva',
	BookingPolicies: 'Políticas de reservas',
	BookingPoliciesDescription: 'Establezca cuándo los clientes pueden hacer reservas en línea',
	BookingTimeUnitDays: 'días',
	BookingTimeUnitHours: 'horas',
	BookingTimeUnitMinutes: 'minutos',
	BookingTimeUnitMonths: 'meses',
	BookingTimeUnitWeeks: 'semanas',
	BottomNavBilling: 'Facturación',
	BottomNavGettingStarted: 'Inicio',
	BottomNavMore: 'Más',
	BottomNavNotes: 'Notas',
	Brands: 'Marcas',
	Brother: 'Hermano',
	BrotherInLaw: 'Cuñado',
	BrowseOrDragFileHere: '<link>Explorar</link> o arrastrar archivo aquí',
	BrowseOrDragFileHereDescription: 'PNG, JPG (máx. {limit})',
	BufferAfterTime: '{time} minutos después',
	BufferAndLabel: 'y',
	BufferAppointmentLabel: 'una cita',
	BufferBeforeTime: '{time} minutos antes',
	BufferTime: 'Tiempo de amortiguación',
	BufferTimeViewLabel: '{bufferBefore} minutos antes y {bufferAfter} minutos después de las citas',
	BulkArchiveClientsDescription: '¿Está seguro de que desea archivar estos clientes? Puede reactivarlos más tarde.',
	BulkArchiveSuccess: 'Clientes archivados con éxito',
	BulkArchiveUndone: 'Archivado masivo deshecho',
	BulkPermanentDeleteDescription: 'Esto eliminará **{count} conversaciones**. Esta acción no se puede deshacer.',
	BulkPermanentDeleteTitle: 'Eliminar conversaciones para siempre',
	BulkUnarchiveSuccess: 'Clientes desarchivados con éxito',
	BulletedList: 'Lista con viñetas',
	BusinessAddress: 'Dirección del negocio',
	BusinessAddressOptional: 'Dirección del negocio<span>(Opcional)</span>',
	BusinessName: 'Nombre del negocio',
	Button: 'Botón',
	By: 'Por',
	CHAMPUSIdentificationNumber: 'Número de identificación CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'Se requiere el CVC',
	Calendar: 'Calendario',
	CalendarAppSyncFormDescription: 'Sincronizar eventos de Carepatron con',
	CalendarAppSyncPanelTitle: 'Sincronización de aplicaciones conectadas',
	CalendarDescription: 'Administre sus citas o establezca tareas y recordatorios personales',
	CalendarDetails: 'Detalles del calendario',
	CalendarDetailsDescription: 'Administre su calendario y la configuración de visualización de citas.',
	CalendarScheduleNew: 'Programar nueva',
	CalendarSettings: 'Configuración del calendario',
	Call: 'Llamada',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> se ha unido a la videollamada',
	CallChangeLayoutTextContent: 'La selección se guarda para futuras reuniones',
	CallIdlePrompt: '¿Sigues ahí? ¿Quieres continuar la llamada?',
	CallLayoutOptionAuto: 'Automático',
	CallLayoutOptionSidebar: 'Barra lateral',
	CallLayoutOptionSpotlight: 'Destacado',
	CallLayoutOptionTiled: 'Mosaico',
	CallNoAttendees: 'No hay asistentes en la reunión.',
	CallSessionExpiredError: 'La sesión de llamada ha expirado',
	CallWithPractitioner: 'Llamada con {practitioner}',
	CallsListCreateButton: 'Nueva convocatoria',
	CallsListEmptyState: 'No hay llamadas activas',
	CallsListItemEndCall: 'Finalizar llamada',
	CamWarningMessage: 'Se ha detectado un problema con tu cámara',
	Camera: 'Cámara',
	CameraAndMicIssueModalDescription:
		'Por favor, habilite el acceso de Carepatron a su cámara y micrófono. Para obtener más información, <a>siga esta guía</a>',
	CameraAndMicIssueModalTitle: 'La cámara y el micrófono están bloqueados',
	CameraQuality: 'Calidad de la cámara',
	CameraSource: 'Fuente de la cámara',
	CanModifyReadOnlyEvent: 'No puedes modificar este evento',
	Canada: 'Canadá',
	Cancel: 'Cancelar',
	CancelClientImportDescription: '¿Está seguro de que quiere cancelar esta importación?',
	CancelClientImportPrimaryAction: 'Sí, cancelar importación',
	CancelClientImportSecondaryAction: 'Sigue editando',
	CancelClientImportTitle: 'Cancelar la importación de clientes',
	CancelImportButton: 'Cancelar importación',
	CancelPlan: 'Cancelar plan',
	CancelPlanConfirmation: `Cancelar el plan automáticamente cargará su cuenta con cualquier saldo pendiente que tenga para este mes. 
Si desea reducir el número de usuarios facturados, simplemente puede eliminar a los miembros del equipo y Carepatron actualizará automáticamente el precio de su suscripción.`,
	CancelSend: 'Cancelar envío',
	CancelSubscription: 'Cancelar suscripción',
	Canceled: 'Cancelado',
	CancellationPolicy: 'Política de cancelación',
	Cancelled: 'Cancelada',
	CannotContainSpecialCharactersError: 'No puede contener {specialCharacters}',
	CannotDeleteInvoice: 'Las facturas pagadas mediante pagos en línea no se pueden eliminar',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> no se puede mover fuera del grupo <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'El servicio no puede trasladarse fuera de las colecciones',
	CapeTown: 'Ciudad del Cabo',
	Caption: 'Pie de foto',
	CaptureNameFieldLabel: 'El nombre con el que te gustaría que otros te llamen',
	CapturePaymentMethod: 'Capturar método de pago',
	CapturingAudio: 'Captura de audio',
	CapturingSignature: 'Capturando firma...',
	CardInformation: 'Información de la tarjeta',
	CardNumberRequired: 'Se requiere el número de tarjeta',
	CardiacRehabilitationSpecialist: 'Especialista en rehabilitación cardiaca',
	Cardiologist: 'Cardiólogo',
	CareAiNoConversations: 'Aún no hay conversaciones',
	CareAiNoConversationsDescription: 'Empieza una conversación con {aiName} para empezar',
	CareAssistant: 'Asistente de cuidados',
	CareManager: 'Gerente de cuidados',
	Caregiver: 'Cuidador',
	CaregiverCreateModalDescription:
		'Agregar personal como administradores les permitirá crear y administrar historias de atención. También les da acceso completo para crear y administrar clientes.',
	CaregiverCreateModalTitle: 'Nuevo miembro del equipo',
	CaregiverListCantAddStaffInfoTitle:
		'Ha alcanzado el número máximo de personal para su suscripción. Actualice su plan para agregar más miembros del personal.',
	CaregiverListCreateButton: 'Nuevo miembro del equipo',
	CaregiverListEmptyState: 'No se han agregado cuidadores',
	CaregiversListItemRemoveStaff: 'Eliminar personal',
	CarepatronApp: 'Aplicación Carepatron',
	CarepatronCommunity: 'Comunidad',
	CarepatronFieldAddress: 'Dirección',
	CarepatronFieldAssignedStaff: 'Personal asignado',
	CarepatronFieldBirthDate: 'Fecha de nacimiento',
	CarepatronFieldEmail: 'Correo electrónico',
	CarepatronFieldEmploymentStatus: 'Estado de empleo',
	CarepatronFieldEthnicity: 'Etnia',
	CarepatronFieldFirstName: 'Nombre',
	CarepatronFieldGender: 'Género',
	CarepatronFieldIdentificationNumber: 'Número de identificación',
	CarepatronFieldIsArchived: 'Estado',
	CarepatronFieldLabel: 'Etiqueta',
	CarepatronFieldLastName: 'Apellido',
	CarepatronFieldLivingArrangements: 'Arreglos de vida',
	CarepatronFieldMiddleNames: 'Segundo nombre',
	CarepatronFieldOccupation: 'Ocupación',
	CarepatronFieldPhoneNumber: 'Número de teléfono',
	CarepatronFieldRelationshipStatus: 'Estado de relación',
	CarepatronFieldStatus: 'Estado',
	CarepatronFieldStatusHelperText: '10 estados como máximo.',
	CarepatronFieldTags: 'Etiquetas',
	CarepatronFields: 'Campos de Carepatron',
	Cash: 'Efectivo',
	Category: 'Categoría',
	CategoryInputPlaceholder: 'Elige una categoría de plantilla',
	CenterAlign: 'Alinear al centro',
	Central: 'Central',
	ChangeLayout: 'Cambiar diseño',
	ChangeLogo: 'Cambiar',
	ChangePassword: 'Cambiar contraseña',
	ChangePasswordFailureSnackbar:
		'Lo siento, tu contraseña no se cambió. Comprueba que tu contraseña antigua sea correcta.',
	ChangePasswordHelperInfo: 'Longitud mínima de {minLength} caracteres',
	ChangePasswordSuccessfulSnackbar:
		'¡Contraseña cambiada con éxito! La próxima vez que inicie sesión, asegúrese de utilizar esa contraseña.',
	ChangeSubscription: 'Cambiar suscripción',
	ChangesNotAllowed: 'No se pueden realizar cambios en este campo',
	ChargesDisabled: 'Cargos deshabilitados',
	ChargesEnabled: 'Cargos habilitados',
	ChargesStatus: 'Estado de cargos',
	ChartAndDiagram: 'Gráfico/Diagrama',
	ChartsAndDiagramsCategoryDescription: 'Para ilustrar los datos y el progreso del cliente',
	ChatEditMessage: 'Editar mensaje',
	ChatReplyTo: 'Responder a {name}',
	ChatTypeMessageTo: 'Mensaje {name}',
	Check: 'Verificar',
	CheckList: 'Lista de verificación',
	Chef: 'Chef',
	Chiropractic: 'Quiropráctica',
	Chiropractor: 'Quiropráctico',
	Chiropractors: 'Quiroprácticos',
	ChooseACollection: 'Elige una colección',
	ChooseAContact: 'Elige un contacto',
	ChooseAccountTypeHeader: '¿Cuál le describe mejor?',
	ChooseAction: 'Elija la acción',
	ChooseAnAccount: 'Elige una cuenta',
	ChooseAnOption: 'Elige una opción',
	ChooseBillingProfile: 'Elija el perfil de facturación',
	ChooseClaim: 'Elija una reclamación',
	ChooseCollection: 'Seleccionar colección',
	ChooseColor: 'Elegir color',
	ChooseCustomDate: 'Elegir fecha personalizada',
	ChooseDateAndTime: 'Elegir fecha y hora',
	ChooseDxCodes: 'Seleccionar códigos de diagnóstico',
	ChooseEventType: 'Elija el tipo de evento',
	ChooseFileButton: 'Elegir un archivo',
	ChooseFolder: 'Elegir carpeta',
	ChooseInbox: 'Seleccione la bandeja de entrada',
	ChooseMethod: 'Elija el método',
	ChooseNewOwner: 'Elegir nuevo propietario',
	ChooseOrganization: 'Elija Organización',
	ChoosePassword: 'Elegir contraseña',
	ChoosePayer: 'Elija el pagador',
	ChoosePaymentMethod: 'Elige un método de pago',
	ChoosePhysicalOrRemoteLocations: 'Ingrese o elija ubicación',
	ChoosePlan: 'Elige {plan}',
	ChooseProfessional: 'Elige Profesional',
	ChooseServices: 'Elegir servicios',
	ChooseSource: 'Elegir origen',
	ChooseSourceDescription:
		'Elige dónde estás importando clientes, ya sea desde un archivo u otra plataforma de software.',
	ChooseTags: 'Seleccionar etiquetas',
	ChooseTaxName: 'Elija el nombre del impuesto',
	ChooseTeamMembers: 'Elija miembros del equipo',
	ChooseTheme: 'Elige tema',
	ChooseTrigger: 'Elija el disparador',
	ChooseYourProvider: 'Elige tu proveedor',
	CircularProgressWithLabel: '{value}%',
	City: 'Ciudad',
	CivilEngineer: 'Ingeniero civil',
	Claim: 'Afirmar',
	ClaimAddReferringProvider: 'Añadir proveedor de referencia',
	ClaimAddRenderingProvider: 'Agregar proveedor de renderizado',
	ClaimAmount: 'Monto de la reclamación',
	ClaimAmountPaidHelpContent:
		'El monto pagado es el pago recibido del paciente u otros pagadores. Ingrese el monto total que el paciente y/u otros pagadores pagaron solo por los servicios cubiertos.',
	ClaimAmountPaidHelpSubtitle: 'Campo 29',
	ClaimAmountPaidHelpTitle: 'Importe pagado',
	ClaimBillingProfileTypeIndividual: 'Individual',
	ClaimBillingProfileTypeOrganisation: 'Organización',
	ClaimChooseRenderingProviderOrTeamMember: 'Elija un proveedor de renderizado o un miembro del equipo',
	ClaimClientInsurancePolicies: 'Pólizas de seguro para clientes',
	ClaimCreatedAction: '<mark>Reclamación {claimNumber}</mark> creada',
	ClaimDeniedAction: '<mark>Reclamo {claimNumber}</mark> fue denegado por <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Búsqueda de códigos de diagnóstico de la CIE-10',
	ClaimDiagnosisSelectorHelpContent: `El “Diagnóstico o lesión” es el signo, síntoma, queja o condición del paciente relacionado con el servicio o servicios del reclamo.
 Se pueden seleccionar hasta 12 códigos de diagnóstico ICD 10.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Campo 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnóstico o lesión',
	ClaimDiagnosticCodesEmptyError: 'Se requiere al menos un código de diagnóstico',
	ClaimDoIncludeReferrerInformation: 'Incluya información del referente en CMS1500',
	ClaimERAReceivedAction: 'Remesa electrónica recibida de <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Remesa electrónica recibida	<mark>Pago {paymentReference}</mark> por <b>{paymentAmount}</b> de <b>{payerNumber} {payerName}</b> se registró',
	ClaimExportedAction: '<mark>Reclamación {claimNumber}</mark> se exportó como <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nombre del cliente o contacto',
	ClaimFieldClientAddress: 'Dirección del cliente',
	ClaimFieldClientAddressDescription:
		'Ingrese la dirección del cliente. La primera línea es para la dirección postal. No utilice signos de puntuación (comas o puntos) ni ningún otro símbolo en la dirección. Si informa una dirección extranjera, comuníquese con el pagador para obtener instrucciones específicas al respecto.',
	ClaimFieldClientAddressSubtitle: 'Campo 5',
	ClaimFieldClientDateOfBirth: 'Fecha de nacimiento del cliente',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Campo 3',
	ClaimFieldClientDateOfBirthDescription:
		'Ingrese la fecha de nacimiento de 8 dígitos del cliente (MM/DD/AAAA). La fecha de nacimiento del cliente es un dato que lo identificará y distinguirá a las personas con nombres similares.',
	ClaimFieldClientDescription: `El 'nombre del cliente' es el nombre de la persona que recibió el tratamiento o los suministros.`,
	ClaimFieldClientSexDescription:
		'El ‘sexo’ es una información que identificará al cliente y distinguirá a las personas con nombres similares.',
	ClaimFieldClientSubtitle: 'Campo 2',
	ClaimFiling: 'Presentación de reclamos',
	ClaimHistorySubtitle: 'Seguro • Reclamo {number}',
	ClaimIncidentAutoAccident: '¿Accidente automovilístico?',
	ClaimIncidentConditionRelatedTo: '¿La condición del cliente está relacionada con?',
	ClaimIncidentConditionRelatedToHelpContent:
		'Esta información indica si la enfermedad o lesión del cliente está relacionada con el empleo, un accidente automovilístico u otro accidente. El empleo (actual o anterior) indicaría que la afección está relacionada con el trabajo o lugar de trabajo del cliente. El accidente automovilístico indicaría que la afección es el resultado de un accidente automovilístico. Otro accidente indicaría que la afección es el resultado de cualquier otro tipo de accidente.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Campos 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: '¿La condición del cliente está relacionada con?',
	ClaimIncidentCurrentIllness: 'Enfermedad, lesión o embarazo actual',
	ClaimIncidentCurrentIllnessHelpContent:
		'La fecha de la enfermedad, lesión o embarazo actual identifica la primera fecha de aparición de la enfermedad, la fecha real de la lesión o la FUM del embarazo.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Campo 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Fechas de enfermedad, lesión o embarazo actual (FUM)',
	ClaimIncidentDate: 'Fecha',
	ClaimIncidentDateFrom: 'Fecha desde',
	ClaimIncidentDateTo: 'Fecha hasta',
	ClaimIncidentEmploymentRelated: 'Empleo',
	ClaimIncidentEmploymentRelatedDesc: '(Actual o anterior)',
	ClaimIncidentHospitalizationDatesLabel: 'Fechas de hospitalización relacionadas con los servicios actuales',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'Las fechas de hospitalización relacionadas con los servicios actuales se refieren a la estadía de un cliente e indican las fechas de admisión y alta asociadas con el(los) servicio(s) en el reclamo.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Campo 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle:
		'Fechas de hospitalización relacionadas con los servicios actuales',
	ClaimIncidentInformation: 'Información del incidente',
	ClaimIncidentOtherAccident: '¿Otro accidente?',
	ClaimIncidentOtherAssociatedDate: 'Otra fecha asociada',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'La otra fecha identifica información de fecha adicional sobre la condición o el tratamiento del cliente.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Campo 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Otra fecha',
	ClaimIncidentQualifier: 'Índice',
	ClaimIncidentQualifierPlaceholder: 'Elija calificador',
	ClaimIncidentUnableToWorkDatesLabel: 'El cliente no pudo trabajar en su ocupación actual',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'Las fechas en las que el cliente no pudo trabajar en su ocupación actual es el período de tiempo en el que el cliente no pudo o no pudo trabajar.',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Campo 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle:
		'Fechas en las que el cliente no pudo trabajar en su ocupación actual',
	ClaimIncludeReferrerInformation: 'Incluir información del referente en CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `El tipo de cobertura de seguro médico aplicable a esta reclamación. Otro indica seguro médico, incluidas las HMO, seguro comercial, seguro de accidentes automovilísticos, seguro de responsabilidad civil o compensación laboral.
 Esta información dirige el reclamo al programa correcto y puede establecer la responsabilidad primaria.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Campo 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Tipo de cobertura',
	ClaimInsuranceGroupIdHelpContent: `Ingrese el número de póliza o grupo del asegurado tal como aparece en la tarjeta de identificación de atención médica del asegurado.

 El “Número de póliza, grupo o FECA del asegurado” es el identificador alfanumérico de la cobertura del plan de seguro médico, de automóvil u otro. El número FECA es el identificador alfanumérico de 9 caracteres que se le asigna a un paciente que reclama una condición relacionada con el trabajo.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Campo 11',
	ClaimInsuranceGroupIdHelpTitle: 'Número de póliza, grupo o FECA del asegurado',
	ClaimInsuranceMemberIdHelpContent: `Ingrese el número de identificación del asegurado tal como aparece en la tarjeta de identificación del asegurado para el pagador al que se envía el reclamo.
 Si el paciente tiene un Número de Identificación de Miembro único asignado por el pagador, ingrese ese número en este campo.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Campo 1a',
	ClaimInsuranceMemberIdHelpTitle: 'Número de identificación del asegurado',
	ClaimInsurancePayer: 'Pagador de seguros',
	ClaimManualPaymentAction: '<mark>Pago {paymentReference}</mark> por <b>{paymentAmount}</b> registrado',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Información adicional sobre reclamaciones',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Por favor, consulte las instrucciones actuales del pagador público o privado con respecto al uso de este campo. Reporte el calificador apropiado, cuando esté disponible, para la información que se está ingresando.No ingrese un espacio, guion u otro separador entre el calificador y la información.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Campo 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Información adicional de la reclamación',
	ClaimMiscClaimCodes: 'Códigos de reclamación',
	ClaimMiscOriginalReferenceNumber: 'Número de referencia original',
	ClaimMiscPatientsAccountNumber: 'Número de cuenta del paciente',
	ClaimMiscPatientsAccountNumberHelpContent:
		'El número de cuenta del paciente es el identificador asignado por el proveedor.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Campo 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Número de cuenta del paciente',
	ClaimMiscPriorAuthorizationNumber: 'Número de autorización previa',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'El número de autorización previa es el número asignado al pagador que autoriza el/los servicio/s.',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Campo 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Número de autorización previa',
	ClaimMiscResubmissionCode: 'Código de reenvío',
	ClaimMiscResubmissionCodeHelpContent:
		'Reenvío significa el código y número de referencia original asignado por el pagador o receptor de destino para indicar un reclamo o encuentro presentado previamente.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Campo 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Reenvío y/o Número de Referencia Original',
	ClaimNumber: 'Número de reclamación',
	ClaimNumberFormat: 'Reclamación #{number}',
	ClaimOrderingProvider: 'Proveedor de pedidos',
	ClaimOtherId: 'Otra identificación',
	ClaimOtherIdPlaceholder: 'Elige una opción',
	ClaimOtherIdQualifier: 'Otro calificador de identificación',
	ClaimOtherIdQualifierPlaceholder: 'Elija el calificador de ID',
	ClaimPlaceOfService: 'Lugar de servicio',
	ClaimPlaceOfServicePlaceholder: 'Añadir POS',
	ClaimPolicyHolderRelationship: 'Relación con el tomador de la póliza',
	ClaimPolicyInformation: 'Información de la política',
	ClaimPolicyTelephone: 'Teléfono (incluir código de área)',
	ClaimReceivedAction: '<mark>Reclamo {claimNumber}</mark> recibido por <b>{name}</b>',
	ClaimReferringProvider: 'Proveedor referente',
	ClaimReferringProviderEmpty: 'No se ha añadido ningún proveedor de referencia.',
	ClaimReferringProviderHelpContent:
		'El nombre ingresado es el del proveedor que remitió, ordenó o supervisó el servicio o los suministros que se mencionan en la reclamación. El calificador indica el rol del proveedor que se informa.',
	ClaimReferringProviderHelpSubtitle: 'Campo 17',
	ClaimReferringProviderHelpTitle: 'Nombre del proveedor o fuente de referencia',
	ClaimReferringProviderQualifier: 'Índice',
	ClaimReferringProviderQualifierPlaceholder: 'Elija calificador',
	ClaimRejectedAction: '<mark>Reclamo {claimNumber}</mark> fue rechazado por <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Número de identificación',
	ClaimRenderingProviderOrTeamMember: 'Proveedor de servicios de renderización o miembro del equipo',
	ClaimRestoredAction: '<mark>Reclamación {claimNumber}</mark> fue restaurada',
	ClaimServiceFacility: 'Instalación de servicio',
	ClaimServiceFacilityLocationHelpContent:
		'El nombre y la dirección de la instalación donde se prestaron los servicios identifican el sitio donde se proporcionaron los servicios.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a y 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Campo 32, 32a y 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Instalación de servicio',
	ClaimServiceFacilityPlaceholder: 'Elija el centro de servicio o la ubicación',
	ClaimServiceLabChargesHelpContent: `Complete este campo cuando reclame servicios adquiridos y prestados por una entidad distinta al proveedor de facturación.
 Cada servicio adquirido debe informarse en un reclamo separado, ya que solo se puede ingresar un cargo en el formulario CMS1500.`,
	ClaimServiceLabChargesHelpSubtitle: 'Campo 20',
	ClaimServiceLabChargesHelpTitle: 'Cargos de laboratorio externos',
	ClaimServiceLineServiceHelpContent:
		'“Procedimientos, Servicios o Suministros” identifica los servicios y procedimientos médicos proporcionados al paciente.',
	ClaimServiceLineServiceHelpSubtitle: 'Campo 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedimientos, Servicios o Suministros',
	ClaimServiceLinesEmptyError: 'Se requiere al menos una línea de servicio',
	ClaimServiceSupplementaryInfoHelpContent: `Agregue una descripción narrativa adicional de los servicios prestados utilizando los calificadores aplicables.
 No ingrese un espacio, guion u otro separador entre el calificador y la información.

 Para obtener instrucciones completas sobre cómo agregar información complementaria, revise las instrucciones del formulario de reclamo CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Campo 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Información complementaria',
	ClaimSettingsBillingMethodTitle: 'Método de facturación al cliente',
	ClaimSettingsClientSignatureDescription:
		'Tengo consentimiento para divulgar información médica o de otro tipo necesaria para procesar reclamos de seguros.',
	ClaimSettingsClientSignatureTitle: 'Firma del cliente en archivo',
	ClaimSettingsConsentLabel: 'Se requiere consentimiento para procesar reclamaciones de seguros:',
	ClaimSettingsDescription:
		'Elija el método de facturación del cliente para garantizar un procesamiento de pago sin problemas:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} tiene una póliza de seguro activa. Para habilitar la facturación del seguro, actualice el método de facturación del cliente a Seguro.',
	ClaimSettingsInsuranceDescription: 'Costes reembolsados por el seguro',
	ClaimSettingsInsuranceTitle: 'Seguro',
	ClaimSettingsNoPoliciesAlertDescription: 'Agregue una póliza de seguro para permitir reclamos de seguros.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Tengo el consentimiento para recibir pagos de seguro por los servicios prestados.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Firma del titular de la póliza en archivo',
	ClaimSettingsSelfPayDescription: 'El cliente pagará por las citas.',
	ClaimSettingsSelfPayTitle: 'Pago por cuenta propia',
	ClaimSettingsTitle: 'Configuración de reclamo',
	ClaimSexSelectorPlaceholder: 'Masculino / Femenino',
	ClaimStatusChangedAction: '<mark>Reclamo {claimNumber}</mark> estado actualizado',
	ClaimSubmittedAction:
		'<mark>Reclamación {claimNumber}</mark> enviada a <b>{payerClearingHouse}</b> para <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Reclamación #{claimNumber}',
	ClaimSupervisingProvider: 'Proveedor supervisor',
	ClaimSupplementaryInfo: 'Información complementaria',
	ClaimSupplementaryInfoPlaceholder: 'Añadir información complementaria',
	ClaimTrashedAction: '<mark>Reclamación {claimNumber}</mark> fue eliminada',
	ClaimValidationFailure: 'Fallo en la validación de la reclamación',
	ClaimsEmptyStateDescription: 'No se han encontrado reclamaciones.',
	ClainInsuranceTelephone: 'Teléfono del seguro (incluya el código de área)',
	Classic: 'Clásico',
	Clear: 'Clara',
	ClearAll: 'Borrar todo',
	ClearSearchFilter: 'Claro',
	ClearingHouse: 'Limpieza de la casa',
	ClearingHouseClaimId: 'ID de Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Liquidación de referencia de la casa',
	ClearingHouseUnavailableError:
		'La cámara de compensación no está disponible actualmente. Por favor, inténtelo de nuevo más tarde.',
	ClickToUpload: 'Haga clic para cargar',
	Client: 'Cliente',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} agregó {noteTitle, select, undefined { una nota } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Elija clientes y sus relaciones',
	ClientAndRelationshipSelectorTitle: 'Todos los clientes y sus relaciones',
	ClientAndRelationshipSelectorTitle1: 'Todas las relaciones de ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Si estás esperando una videollamada, aparecerá aquí en breve. Si tienes algún problema, por favor contacta a la persona que la inició.',
	ClientAppSubHeaderMyDocumentation: 'Mi documentación',
	ClientAppointment: 'Cita con el cliente',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} ha reservado {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'No se han encontrado citas',
	ClientAppointmentsEmptyStateTitle:
		'Realice un seguimiento de las citas futuras e históricas de sus clientes y su asistencia',
	ClientArchivedSuccessfulSnackbar: 'Archivado correctamente <b>{name}</b>',
	ClientBalance: 'Saldo del cliente',
	ClientBilling: 'Facturación',
	ClientBillingAddPaymentMethodDescription:
		'Agregue y administre los métodos de pago de sus clientes para agilizar su proceso de facturación y facturación.',
	ClientBillingAndPaymentDueDate: 'Fecha de vencimiento',
	ClientBillingAndPaymentHistory: 'Historial de facturación y pagos',
	ClientBillingAndPaymentInvoices: 'Facturas',
	ClientBillingAndPaymentIssueDate: 'Fecha de emisión',
	ClientBillingAndPaymentPrice: 'Precio',
	ClientBillingAndPaymentReceipt: 'Recibo',
	ClientBillingAndPaymentServices: 'Servicios',
	ClientBillingAndPaymentStatus: 'Estado',
	ClientBulkStaffAssignedSuccessSnackbar:
		'{count, plural, one {Miembro} other {Miembors}} del equipo {count, plural, one {asignado} other {asignados}}',
	ClientBulkStaffUnassignedSuccessSnackbar:
		'{count, plural, one {Miembro} other {Miembors}} del equipo no {count, plural, one {asignado} other {asignados}}',
	ClientBulkTagsAddedSuccessSnackbar: '¡Etiquetas añadidas!',
	ClientDuplicatesDeviewDescription:
		'Fusiona varios registros de clientes en uno para unificar todos los datos: notas, documentos, citas, facturas y conversaciones.',
	ClientDuplicatesPageMergeHeader: 'Elige los datos que deseas conservar',
	ClientDuplicatesReviewHeader: 'Comparar posibles registros duplicados para fusionarlos',
	ClientEmailChangeWarningDescription:
		'Actualizar el correo electrónico del cliente eliminará su acceso a cualquier documentación compartida y otorgará acceso al usuario con el nuevo correo electrónico',
	ClientFieldDateDescription: 'Formato de fecha',
	ClientFieldDateLabel: 'Fecha',
	ClientFieldDateRangeDescription: 'Un rango de fechas',
	ClientFieldDateRangeLabel: 'Rango de fechas',
	ClientFieldDateShowDateDescription: 'por ejemplo, 29 años',
	ClientFieldDateShowDateRangeDescription: 'Por ejemplo, 2 semanas',
	ClientFieldEmailDescription: 'Dirección de correo electrónico',
	ClientFieldEmailLabel: 'Correo electrónico',
	ClientFieldLabel: 'Etiqueta de campo',
	ClientFieldLinearScaleDescription: 'Opciones de escala 1-10',
	ClientFieldLinearScaleLabel: 'Escala lineal',
	ClientFieldLocationDescription: 'Dirección física o postal',
	ClientFieldLocationLabel: 'Ubicación',
	ClientFieldLongTextDescription: 'Área de texto largo',
	ClientFieldLongTextLabel: 'Párrafo',
	ClientFieldMultipleChoiceDropdownDescription: 'Elige varias opciones de la lista',
	ClientFieldMultipleChoiceDropdownLabel: 'Desplegable de opción múltiple',
	ClientFieldPhoneNumberDescription: 'Número de teléfono',
	ClientFieldPhoneNumberLabel: 'Teléfono',
	ClientFieldPlaceholder: 'Elija un tipo de campo de cliente',
	ClientFieldSingleChoiceDropdownDescription: 'Elige sólo una opción de la lista',
	ClientFieldSingleChoiceDropdownLabel: 'Desplegable de elección única',
	ClientFieldTextDescription: 'Campo de entrada de texto',
	ClientFieldTextLabel: 'Texto',
	ClientFieldYesOrNoDescription: 'Elija entre las opciones sí o no',
	ClientFieldYesOrNoLabel: 'Sí | No',
	ClientFileFormAccessLevelDescription:
		'Tú y el equipo siempre tienen acceso a los archivos que cargas. Puedes elegir compartir este archivo con el cliente y/o sus relaciones.',
	ClientFileSavedSuccessSnackbar: '¡Archivo guardado!',
	ClientFilesPageEmptyStateText: 'No se han cargado archivos',
	ClientFilesPageUploadFileButton: 'Cargar archivos',
	ClientHeaderBilling: 'Facturación',
	ClientHeaderBillingAndReceipts: 'Facturación y recibos',
	ClientHeaderDocumentation: 'Documentación',
	ClientHeaderDocuments: 'Documentos',
	ClientHeaderFile: 'Documento',
	ClientHeaderHistory: 'Historial médico',
	ClientHeaderInbox: 'Bandeja de entrada',
	ClientHeaderNote: 'Nota',
	ClientHeaderOverview: 'Resumen',
	ClientHeaderProfile: 'Personal',
	ClientHeaderRelationship: 'Relación',
	ClientHeaderRelationships: 'Relaciones',
	ClientId: 'Identificación del cliente',
	ClientImportProcessingDescription: 'Archivo aún en procesamiento. Le notificaremos cuando esto esté listo.',
	ClientImportReadyForMappingDescription:
		'Hemos terminado de pre-procesar su archivo. ¿Le gustaría mapear columnas para completar esta importación?',
	ClientImportReadyForMappingNotificationSubject:
		'La pre-tramitación de la importación del cliente está completa. El archivo ahora está listo para la asignación.',
	ClientInAppMessaging: 'Mensajería en la aplicación para clientes',
	ClientInfoAddField: 'Agregar otro campo',
	ClientInfoAddRow: 'Agregar fila',
	ClientInfoAlertMessage: 'Toda la información rellenada en esta sección rellenará el registro del cliente.',
	ClientInfoFormPrimaryText: 'Información del cliente',
	ClientInfoFormSecondaryText: 'Recopile detalles de contacto',
	ClientInfoPlaceholder: `Nombre del cliente, Dirección de correo electrónico, Número de teléfono 
Dirección física, 
Fecha de nacimiento`,
	ClientInformation: 'Información del cliente',
	ClientInsuranceTabLabel: 'Seguro',
	ClientIntakeFormsNotSupported: `Actualmente, las plantillas de formulario no se admiten a través de las admisiones de clientes.
En su lugar, créalos y compártelos como notas del cliente.`,
	ClientIntakeModalDescription:
		'Se enviará un correo electrónico de admisión a su cliente pidiéndole que complete su perfil y cargue documentos médicos o de referencia relevantes. Se les dará acceso al Portal del Cliente.',
	ClientIntakeModalTitle: 'Enviar admisión a {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: '¡Éxito! Se ha guardado su admisión.',
	ClientIntakeSuccessSnackbar:
		'¡Éxito! Su admisión ha sido guardada y se ha enviado un correo electrónico de confirmación.',
	ClientIsChargedProcessingFee: 'Los clientes pagarán la tarifa de procesamiento',
	ClientListCreateButton: 'Nuevo cliente',
	ClientListEmptyState: 'No se han agregado clientes',
	ClientListPageItemArchive: 'Eliminar cliente',
	ClientListPageItemRemoveAccess: 'Eliminar mi acceso',
	ClientLocalizationPanelDescription: 'El idioma y la zona horaria preferidos del cliente.',
	ClientLocalizationPanelTitle: 'Idioma y zona horaria',
	ClientManagementAndEHR: 'Gestión de clientes y EHR',
	ClientMergeResultSummaryBanner:
		'Fusionar registros consolida todos los datos del cliente, incluidas las notas, los documentos, las citas, las facturas y las conversaciones. Verifique la precisión antes de continuar.',
	ClientMergeResultSummaryTitle: 'Resumen de resultados de la fusión',
	ClientModalTitle: 'Nuevo cliente',
	ClientMustHaveEmaillAccessErrorText: 'Clientes/Contactos sin correo electrónico',
	ClientMustHavePortalAccessErrorText: 'Clientes/Contactos deberán registrarse',
	ClientMustHaveZoomAppConnectedErrorText: 'Conecta Zoom a través de Configuración > Aplicaciones conectadas',
	ClientNameFormat: 'Formato del nombre del cliente',
	ClientNotFormAccessLevel: 'Visible por:',
	ClientNotFormAccessLevelDescription:
		'Usted y el equipo siempre tienen acceso a las notas que publica. Puede optar por compartir esta nota con el cliente y / o sus relaciones',
	ClientNotRegistered: 'No registrado',
	ClientNoteFormAddFileButton: 'Adjuntar archivos',
	ClientNoteFormChooseAClient: 'Elige un cliente/contacto para continuar',
	ClientNoteFormContent: 'Contenido',
	ClientNoteItemDeleteConfirmationModalDescription: 'Una vez eliminada, no podrá recuperar esta nota.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Nota publicada y bloqueada.',
	ClientNotePublishedSuccessSnackbar: '¡Nota publicada!',
	ClientNotes: 'Notas del cliente',
	ClientNotesEmptyStateText: 'Para agregar notas, vaya al perfil de un cliente y haga clic en la pestaña Notas.',
	ClientOnboardingChoosePasswordTitle1: '¡Casi listo!',
	ClientOnboardingChoosePasswordTitle2: 'Elige una contraseña',
	ClientOnboardingCompleteIntake: 'Completar admisión',
	ClientOnboardingConfirmationScreenText: `Ha proporcionado toda la información que requiere {providerName}.
Confirme su dirección de correo electrónico para comenzar su incorporación. Si no lo recibe de inmediato, verifique su carpeta de correo no deseado.`,
	ClientOnboardingConfirmationScreenTitle: '¡Genial! Revisa tu bandeja de entrada.',
	ClientOnboardingDashboardButton: 'Ir al panel de control',
	ClientOnboardingHealthRecordsDesc1: '¿Quieres compartir alguna carta de referencia o documento con {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Agregar descripción (opcional)',
	ClientOnboardingHealthRecordsTitle: 'Documentación',
	ClientOnboardingPasswordRequirements: 'Requisitos',
	ClientOnboardingPasswordRequirementsConditions1: 'Mínimo 9 caracteres',
	ClientOnboardingProviderIntroSignupButton: 'Registrarme para mí',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Registrarme para un familiar',
	ClientOnboardingProviderIntroTitle: '{name} te ha invitado a unirte a su plataforma Carepatron',
	ClientOnboardingRegistrationInstructions: 'Ingrese sus datos personales a continuación.',
	ClientOnboardingRegistrationTitle: 'Primero necesitamos algunos detalles personales',
	ClientOnboardingStepFormsAndAgreements: 'Formularios y acuerdos',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Complete los siguientes formularios para el proceso de admisión de {providerName}',
	ClientOnboardingStepHealthDetails: 'Detalles de salud',
	ClientOnboardingStepPassword: 'Contraseña',
	ClientOnboardingStepYourDetails: 'Tus detalles',
	ClientPaymentMethodDescription:
		'Guarda un método de pago en tu perfil para hacer más rápido y seguro tu próximo agendamiento de citas y facturación.',
	ClientPortal: 'Portal de clientes',
	ClientPortalDashboardEmptyDescription: 'Tu historial de citas y asistencia aparecerá aquí.',
	ClientPortalDashboardEmptyTitle:
		'Mantén un registro de todas las citas próximas, solicitadas y pasadas, junto con tu asistencia',
	ClientPreferredNotificationPanelDescription:
		'Administra el método preferido de tus clientes para recibir actualizaciones y notificaciones a través de:',
	ClientPreferredNotificationPanelTitle: 'Método de notificación preferido',
	ClientProcessingFee: 'El pago incluye ({currencyCode}) {amount} de gastos de procesamiento',
	ClientProfileAddress: 'Dirección',
	ClientProfileDOB: 'Fecha de nacimiento',
	ClientProfileEmailHelperText: 'Agregar un correo electrónico otorga acceso al portal',
	ClientProfileEmailHelperTextMoreInfo:
		'Conceder acceso al portal al cliente permite a los miembros del equipo compartir notas, archivos y otra documentación',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Número de identificación',
	ClientRelationshipsAddClientOwnerButton: 'Invitar al cliente',
	ClientRelationshipsAddFamilyButton: 'Invitar a un miembro de la familia',
	ClientRelationshipsAddStaffButton: 'Agregar acceso del personal',
	ClientRelationshipsEmptyStateText: 'No se han agregado relaciones',
	ClientRemovedSuccessSnackbar: 'Cliente eliminado correctamente.',
	ClientResponsibility: 'Responsabilidad del cliente',
	ClientSavedSuccessSnackbar: 'Cliente guardado correctamente.',
	ClientTableClientName: 'Nombre del cliente',
	ClientTablePhone: 'Teléfono',
	ClientTableStatus: 'Estado',
	ClientUnarchivedSuccessfulSnackbar: 'Se ha desarchivado correctamente <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} editó {noteTitle, select, undefined { una nota } other {{noteTitle}}}',
	ClientView: 'Vista de cliente',
	Clients: 'Clientes',
	ClientsTable: 'Tabla de clientes',
	ClinicalFormat: 'Formato clínico',
	ClinicalPsychologist: 'Psicólogo clínico',
	Close: 'Cerrar',
	CloseImportClientsModal: '¿Está seguro de que desea cancelar la importación de clientes?',
	CloseReactions: 'Reacciones cercanas',
	Closed: 'Cerrado',
	Coaching: 'Coaching',
	Code: 'Código',
	CodeErrorMessage: 'Se requiere un código',
	CodePlaceholder: 'Código',
	Coinsurance: 'Coaseguro',
	Collection: 'Colección',
	CollectionName: 'Nombre de la colección',
	Collections: 'Colecciones',
	ColorAppointmentsBy: 'Citas en color de',
	ColorTheme: 'Tema de color',
	ColourCalendarBy: 'Color del calendario por',
	ComingSoon: 'Próximamente',
	Community: 'Comunidad',
	CommunityHealthLead: 'Líder de salud comunitaria',
	CommunityHealthWorker: 'Trabajador sanitario comunitario',
	CommunityTemplatesSectionDescription: 'Creado por la comunidad',
	CommunityTemplatesSectionTitle: 'Comunidad',
	CommunityUser: 'Usuario de la comunidad',
	Complete: 'Completa',
	CompleteAndLock: 'Completar y bloquear',
	CompleteSetup: 'Configuración completa',
	CompleteSetupSuccessDescription: 'Has completado algunos pasos clave para dominar Carepatron.',
	CompleteSetupSuccessDescription2:
		'Desbloquea más formas de ayudar a optimizar tu práctica y apoyar a tus clientes.',
	CompleteSetupSuccessTitle: '¡Éxito! ¡Lo estás haciendo increíble!',
	CompleteStripeSetup: 'Completar la configuración de Stripe',
	Completed: 'Completado',
	ComposeSms: 'Redactar SMS',
	ComputerSystemsAnalyst: 'Analista de sistemas informáticos',
	Confirm: 'Confirmar',
	ConfirmDeleteAccountDescription:
		'Estás a punto de eliminar tu cuenta. Esta acción no se puede deshacer. Si deseas continuar, confirma a continuación.',
	ConfirmDeleteActionDescription: '¿Está seguro de que desea eliminar esta acción? No se puede deshacer.',
	ConfirmDeleteAutomationDescription:
		'¿Está seguro de que desea eliminar esta automatización? Esta acción no se puede deshacer.',
	ConfirmDeleteScheduleDescription:
		'Eliminar el horario <strong>{scheduleName}</strong> eliminará este de tus horarios y podría afectar la disponibilidad de tus servicios en línea. Esta acción no se puede deshacer.',
	ConfirmDraftResponseContinue: 'Continúa con la respuesta',
	ConfirmDraftResponseDescription:
		'Si cierras esta página, tu respuesta permanecerá como un borrador. Puedes volver y continuar en cualquier momento.',
	ConfirmDraftResponseSubmitResponse: 'Enviar respuesta',
	ConfirmDraftResponseTitle: 'Tu respuesta no ha sido enviada',
	ConfirmIfUserIsClientDescription:
		'El formulario de registro que completó es para proveedores (es decir, equipos/organizaciones de salud). Si esto es un error, puede elegir "Continuar como cliente" y lo configuraremos para su portal de cliente',
	ConfirmIfUserIsClientNoButton: 'Registrarse como proveedor',
	ConfirmIfUserIsClientTitle: 'Parece que eres un cliente',
	ConfirmIfUserIsClientYesButton: 'Continuar como cliente',
	ConfirmKeepSeparate: 'Confirmar mantener separado',
	ConfirmMerge: 'Confirmar fusión',
	ConfirmPassword: 'Confirmar contraseña',
	ConfirmRevertClaim: 'Sí, revertir estado',
	ConfirmSignupAccessCode: 'Código de confirmación',
	ConfirmSignupButtom: 'Confirmar',
	ConfirmSignupDescription:
		'Ingrese su dirección de correo electrónico y el código de confirmación que acabamos de enviarle.',
	ConfirmSignupSubTitle: 'Verifique en la carpeta de correo no deseado si el correo electrónico no ha llegado',
	ConfirmSignupSuccessSnackbar:
		'¡Genial, hemos confirmado tu cuenta! Ahora puedes iniciar sesión usando tu correo electrónico y contraseña',
	ConfirmSignupTitle: 'Confirmar cuenta',
	ConfirmSignupUsername: 'Correo electrónico',
	ConfirmSubscriptionUpdate: 'Confirmar suscripción {price} {isMonthly, select, true {al mes} other {al año}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Una vez que se eliminen los clientes, ya no podrás acceder a su información.',
	ConfirmationModalBulkDeleteClientsTitleId: '¿Eliminar {count, plural, one {# cliente} other {# clientes}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Una vez que los contactos sean eliminados, no podrás acceder a su información.',
	ConfirmationModalBulkDeleteContactsTitleId: '¿Eliminar {count, plural, one {# contacto} other {# contactos}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Esta es una acción permanente. Una vez que se eliminen los miembros del equipo, ya no podrás acceder a su información.',
	ConfirmationModalBulkDeleteMembersTitleId: '¿Eliminar {count, plural, one {# team member} other {# team member}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Al cerrar esta nota se terminarán todas las transcripciones en curso. ¿Está seguro de que desea continuar?',
	ConfirmationModalDeleteClientField:
		'Esta es una acción permanente. Una vez que se elimine el campo, ya no será accesible en tus clientes restantes.',
	ConfirmationModalDeleteSectionMessage:
		'Una vez eliminadas, se eliminarán todas las preguntas de esta sección. Esta acción no se puede deshacer.',
	ConfirmationModalDeleteService:
		'Esta es una acción permanente. Una vez que se elimine el servicio, ya no será accesible en tu espacio de trabajo.',
	ConfirmationModalDeleteServiceGroup:
		'Al eliminar una colección se eliminarán todos los servicios del grupo y volverás a tu lista de servicios. Esta acción no se puede deshacer.',
	ConfirmationModalDeleteTranscript: '¿Está seguro de que desea eliminar la transcripción?',
	ConfirmationModalDescriptionDeleteClient:
		'Una vez que se elimine el cliente, ya no podrás acceder a la información del cliente.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Una vez que elimines tu acceso, ya no podrás ver la información del cliente.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'Su perfil no se eliminará, solo se eliminará como una relación de este cliente.',
	ConfirmationModalDescriptionRemoveStaff: '¿Estás seguro de que deseas eliminar a esta persona del proveedor?',
	ConfirmationModalEndSession: '¿Estás seguro que deseas finalizar la sesión?',
	ConfirmationModalTitle: '¿Estás seguro?',
	Confirmed: 'Confirmada',
	ConflictTimezoneWarningMessage: 'Pueden ocurrir conflictos debido a múltiples zonas horarias',
	Connect: 'Conectar',
	ConnectExistingClientOrContact: 'Crear nuevo cliente/contacto',
	ConnectInboxGoogleDescription: 'Agregar una cuenta de Gmail o una lista de grupos de Google',
	ConnectInboxMicrosoftDescription: 'Agregue una cuenta de Outlook, Office365 o Exchange',
	ConnectInboxModalDescription:
		'Vincula y dirige un canal para ver y realizar un seguimiento de la comunicación, todo en un solo lugar.',
	ConnectInboxModalExistingDescription:
		'Utilice una conexión existente desde la configuración de sus aplicaciones conectadas para simplificar el proceso de configuración.',
	ConnectInboxModalExistingTitle: 'App conectada existente en Carepatron',
	ConnectInboxModalTitle: 'Conectar bandeja de entrada',
	ConnectToStripe: 'Conectar con Stripe',
	ConnectZoom: 'Conectar con Zoom',
	ConnectZoomModalDescription: 'Permettre à Carepatron de gérer les appels vidéo pour vos rendez-vous.',
	ConnectedAppDisconnectedNotificationSubject: 'Hemos perdido la conexión a la cuenta {account}. Vuelva a conectar.',
	ConnectedAppSyncDescription:
		'Administre aplicaciones conectadas para crear eventos en calendarios de terceros directamente desde Carepatron.',
	ConnectedApps: 'Aplicaciones conectadas',
	ConnectedAppsGMailDescription: 'Agregar cuentas de Gmail o lista de grupos de Google',
	ConnectedAppsGoogleCalendarDescription: 'Añadir cuentas de calendarios o lista de grupos de Google',
	ConnectedAppsGoogleDescription: 'Agrega tu cuenta de Google y sincroniza tu calendario',
	ConnectedAppsMicrosoftDescription: 'Agrega una cuenta de Outlook, Office365 o Exchange',
	ConnectedCalendars: 'Calendarios conectados',
	ConsentDocumentation: 'Formularios y acuerdos',
	ConsentDocumentationPublicTemplateError:
		'Por razones de seguridad, solo puedes elegir plantillas de tu equipo (no públicas).',
	ConstructionWorker: 'Trabajador de la construcción',
	Consultant: 'Consultor',
	Contact: 'Contacto',
	ContactAccessTypeHelperText: 'Permite a los administradores de la familia actualizar información',
	ContactAccessTypeHelperTextMoreInfo: 'Esto le permitirá compartir notas/documentos sobre {clientFirstName}',
	ContactAddressLabelBilling: 'Facturación',
	ContactAddressLabelHome: 'Hogar',
	ContactAddressLabelOthers: 'Otros',
	ContactAddressLabelWork: 'Trabajar',
	ContactChangeConfirmation:
		'Cambiar el contacto de la factura eliminará todos los elementos relacionados con <mark>{contactName}</mark>',
	ContactDetails: 'Detalles de contacto',
	ContactEmailLabelOthers: 'Otros',
	ContactEmailLabelPersonal: 'Personal',
	ContactEmailLabelSchool: 'Escuela',
	ContactEmailLabelWork: 'Trabajar',
	ContactInformation: 'Información de contacto',
	ContactInformationText: 'Información del contacto',
	ContactListCreateButton: 'Nuevo contacto',
	ContactName: 'Nombre del contacto',
	ContactPhoneLabelHome: 'Hogar',
	ContactPhoneLabelMobile: 'Móvil',
	ContactPhoneLabelSchool: 'Escuela',
	ContactPhoneLabelWork: 'Trabajar',
	ContactRelationship: 'Relación de contacto',
	ContactRelationshipFormAccessType: 'Conceder acceso a la información compartida',
	ContactRelationshipGrantAccessInfo: 'Esto te permitirá compartir notas y documentos',
	ContactSupport: 'Contactar con soporte técnico',
	Contacts: 'Contactos',
	ContainerIdNotSet: 'ID de contenedor no establecido',
	Contemporary: 'Contemporáneo',
	Continue: 'Continuar',
	ContinueDictating: 'Continuar dictando',
	ContinueEditing: 'Seguir editando',
	ContinueImport: 'Continuar importando',
	ContinueTranscription: 'Continuar la transcripción',
	ContinueWithApple: 'Continuar con Apple',
	ContinueWithGoogle: 'Continuar con Google',
	Conversation: 'Conversación',
	Copay: 'Copago',
	CopayOrCoinsurance: 'Copago o coaseguro',
	Copayment: 'Copago',
	CopiedToClipboard: 'Copiado al portapapeles',
	Copy: 'Copiar',
	CopyAddressSuccessSnackbar: 'Dirección copiada al portapapeles',
	CopyCode: 'Copiar código',
	CopyCodeToClipboardSuccess: 'Código copiado al portapapeles',
	CopyEmailAddressSuccessSnackbar: 'Dirección de correo electrónico copiada al portapapeles',
	CopyLink: 'Copiar enlace',
	CopyLinkForCall: 'Copiar este enlace para compartir esta llamada:',
	CopyLinkSuccessSnackbar: 'Enlace copiado al portapapeles',
	CopyMeetingLink: 'Copiar enlace de la reunión',
	CopyPaymentLink: 'Copiar enlace de pago',
	CopyPhoneNumberSuccessSnackbar: 'Número de teléfono copiado al portapapeles',
	CopyTemplateLink: 'Copiar enlace de la plantilla',
	CopyTemplateLinkSuccess: 'Enlace copiado al portapapeles',
	CopyToClipboardError: 'No se pudo copiar al portapapeles. Por favor, inténtalo de nuevo.',
	CopyToTeamTemplates: 'Copiar a plantillas de equipo',
	CopyToWorkspace: 'Copiar al espacio de trabajo',
	Cosmetologist: 'Cosmetóloga',
	Cost: 'Costo',
	CostErrorMessage: 'Se requiere el costo',
	Counseling: 'Asesoramiento',
	Counselor: 'Consejero',
	Counselors: 'Consejeros',
	CountInvoicesAdded: '{count, plural, one {# Factura añadido} other {# Facturas añadido}}',
	CountNotesAdded: '{count, plural, one {# Nota añadido} other {# Notas añadido}}',
	CountSelected: '{count} seleccionados',
	CountTimes: '{count} veces',
	Country: 'País',
	Cousin: 'Primo/Prima',
	CoverageType: 'Tipo de cobertura',
	Covered: 'Cubierto',
	Create: 'Crear',
	CreateANewClient: 'Crear un nuevo cliente',
	CreateAccount: 'Crear cuenta',
	CreateAndSignNotes: 'Crear y firmar notas con los clientes',
	CreateAvailabilityScheduleFailure: 'No se pudo crear el horario de disponibilidad',
	CreateAvailabilityScheduleSuccess: 'Horario de disponibilidad creado exitosamente',
	CreateBillingItems: 'Crear elementos de facturación',
	CreateCallFormButton: 'Iniciar llamada',
	CreateCallFormInviteOnly: 'Solo por invitación',
	CreateCallFormInviteOnlyMoreInfo:
		'Sólo las personas invitadas a esta llamada pueden unirse. Para compartir esta llamada con otros, simplemente desmarque esto y copie/pegue el enlace en la siguiente página',
	CreateCallFormRecipients: 'Destinatarios',
	CreateCallFormRegion: 'Región de alojamiento',
	CreateCallModalAddClientContactSelectorLabel: 'Contactos del cliente',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Buscar por nombre de cliente',
	CreateCallModalAddStaffSelectorLabel: 'Miembros del equipo (opcional)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Buscar por nombre de personal',
	CreateCallModalDescription:
		'Inicie una llamada e invite a miembros del personal y/o contactos. Alternativamente, puede desmarcar la casilla "Privado" para compartir esta llamada con cualquier persona con Carepatron',
	CreateCallModalTitle: 'Iniciar una llamada',
	CreateCallModalTitleLabel: 'Título (opcional)',
	CreateCallNoPersonIdToolTip: 'Sólo los contactos/clientes con acceso al portal pueden unirse a las llamadas',
	CreateClaim: 'Crear reclamación',
	CreateClaimCompletedMessage: 'Su reclamo ha sido creado.',
	CreateClientModalTitle: 'Nuevo cliente',
	CreateContactModalTitle: 'Nuevo contacto',
	CreateContactRelationshipButton: 'Agregar relación',
	CreateContactSelectorDefaultOption: '+ Crear contacto',
	CreateContactWithRelationshipFormAccessType: 'Conceder acceso a información compartida',
	CreateDocumentDnDPrompt: 'Arrastra y suelta para subir archivos',
	CreateDocumentSizeLimit: 'Límite de tamaño por archivo {size} MB. Total de {total} archivos.',
	CreateFreeAccount: 'Crear una cuenta gratis',
	CreateInvoice: 'Crear factura',
	CreateLink: 'Crear enlace',
	CreateNew: 'Crear nuevo',
	CreateNewAppointment: 'Crear nueva cita',
	CreateNewClaim: 'Crear un nuevo reclamo',
	CreateNewClaimForAClient: 'Crear nueva reclamación para un cliente.',
	CreateNewClient: 'Crear nuevo cliente',
	CreateNewConnection: 'Nueva conexión',
	CreateNewContact: 'Crear nuevo contacto',
	CreateNewField: 'Crear nuevo campo',
	CreateNewLocation: 'Nueva ubicación',
	CreateNewService: 'Crear nuevo servicio',
	CreateNewServiceGroupFailure: 'No se pudo crear la nueva colección',
	CreateNewServiceGroupMenu: 'Nueva colección',
	CreateNewServiceGroupSuccess: 'Se ha creado la nueva colección con éxito',
	CreateNewServiceMenu: 'Nuevo servicio',
	CreateNewTeamMember: 'Crear nuevo miembro del equipo',
	CreateNewTemplate: 'Nueva plantilla',
	CreateNote: 'Crear nota',
	CreateSuperbillReceipt: 'Nuevo superbill',
	CreateSuperbillReceiptSuccess: 'Recibo de Superbill creado correctamente',
	CreateTemplateFolderSuccessMessage: 'Creado correctamente {folderTitle}',
	Created: 'Creado',
	CreatedAt: 'Creado {timestamp}',
	Credit: 'Crédito',
	CreditAdded: 'Crédito aplicado',
	CreditAdjustment: 'Ajuste de crédito',
	CreditAdjustmentReasonHelperText: 'Esta es una nota interna y no será visible para su cliente.',
	CreditAdjustmentReasonPlaceholder:
		'Agregar un motivo de ajuste puede ayudar al revisar las transacciones facturables',
	CreditAmount: '{amount} NC',
	CreditBalance: 'saldo acreedor',
	CreditCard: 'Tarjeta de crédito',
	CreditCardExpire: 'Expira el {exp_month}/{exp_year}',
	CreditCardNumber: 'Número de tarjeta de crédito',
	CreditDebitCard: 'Tarjeta',
	CreditIssued: 'Crédito emitido',
	CreditsUsed: 'Crédits utilisés',
	Crop: 'Recortar',
	Currency: 'Moneda',
	CurrentCredit: 'Crédito actual',
	CurrentEventTime: 'Hora actual del evento',
	CurrentPlan: 'Plan actual',
	Custom: 'Personalizado',
	CustomRange: 'Rango personalizado',
	CustomRate: 'Tarifa personalizada',
	CustomRecurrence: 'Recurrencia personalizada',
	CustomServiceAvailability: 'Servicio disponible',
	CustomerBalance: 'Saldo del cliente',
	CustomerName: 'Nombre del cliente',
	CustomerNameIsRequired: 'El nombre del cliente es obligatorio',
	CustomerServiceRepresentative: 'Representante de atención al cliente',
	CustomiseAppointments: 'Personalizar citas',
	CustomiseBookingLink: 'Personalizar opciones de reserva',
	CustomiseBookingLinkServicesInfo: 'Los clientes solo pueden reservar servicios disponibles',
	CustomiseBookingLinkServicesLabel: 'Servicios',
	CustomiseClientRecordsAndWorkspace: 'Personalice los registros y el espacio de trabajo de sus clientes',
	CustomiseClientSettings: 'Personalizar configuraciones del cliente',
	Customize: 'Personalizar',
	CustomizeAppearance: 'Personalizar apariencia',
	CustomizeAppearanceDesc:
		'Personaliza la apariencia de tu reserva en línea para que coincida con tu marca y optimiza cómo se muestran tus servicios a los clientes.',
	CustomizeClientFields: 'Personalizar los campos del cliente',
	CustomizeInvoiceTemplate: 'Personalizar plantilla de factura',
	CustomizeInvoiceTemplateDescription: 'Cree sin esfuerzo facturas profesionales que reflejen su marca.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'Se requiere DX',
	Daily: 'Diario',
	DanceTherapist: 'Terapeuta de danza',
	DangerZone: 'Zona peligrosa',
	Dashboard: 'Tablero',
	Date: 'Fecha',
	DateAndTime: 'Fecha y hora',
	DateDue: 'Fecha de vencimiento',
	DateErrorMessage: 'Se requiere la fecha',
	DateFormPrimaryText: 'Fecha',
	DateFormSecondaryText: 'Seleccione desde un selector de fechas',
	DateIssued: 'Fecha de emisión',
	DateOfPayment: 'Fecha de pago',
	DateOfService: 'Fecha del servicio',
	DateOverride: 'Anulación de fecha',
	DateOverrideColor: 'Color de anulación de fecha',
	DateOverrideInfo:
		'La anulación de fechas permite a los profesionales ajustar manualmente su disponibilidad para fechas concretas anulando los horarios habituales.',
	DateOverrideInfoBanner:
		'En estas franjas horarias sólo se pueden reservar los servicios especificados para esta anulación de fecha; no se permiten otras reservas en línea.',
	DateOverrides: 'Anulación de fecha',
	DatePickerFormPrimaryText: 'Fecha',
	DatePickerFormSecondaryText: 'Elige una fecha',
	DateRange: 'Intervalo de fechas',
	DateRangeFormPrimaryText: 'Rango de fechas',
	DateRangeFormSecondaryText: 'Elige un rango de fechas',
	DateReceived: 'Fecha de recepción',
	DateSpecificHours: 'Disponibilidad ajustada',
	DateSpecificHoursDescription: 'Indica a qué horas estás disponible en fechas concretas.',
	DateUploaded: 'Subido el {date, date, medium}',
	Dates: 'Fechas',
	Daughter: 'Hija',
	Day: 'Día',
	DayPlural: '{count, plural, one {día} other {días}}',
	Days: 'Días',
	DaysPlural: '{age, plural, one {# día} other {# días}}',
	DeFacto: 'De facto',
	Deactivated: 'Desactivado',
	Debit: 'Débito',
	DecreaseIndent: 'Disminuir la sangría',
	Deductibles: 'Deducibles',
	Default: 'Predeterminado',
	DefaultBillingProfile: 'Perfil de facturación predeterminado',
	DefaultDescription: 'Descripción predeterminada',
	DefaultEndOfLine: 'No hay más elementos',
	DefaultInPerson: 'Citas con el cliente',
	DefaultInvoiceTitle: 'Título predeterminado',
	DefaultNotificationSubject: 'Has recibido una nueva notificación para {notificationType}',
	DefaultPaymentMethod: 'Método de pago predeterminado',
	DefaultService: 'Servicio predeterminado',
	DefaultValue: 'Por defecto',
	DefaultVideo: 'Correo electrónico de cita en video con el cliente',
	DefinedTemplateType: 'Plantilla de {invoiceTemplate}',
	Delete: 'Eliminar',
	DeleteAccountButton: 'Eliminar cuenta',
	DeleteAccountDescription: 'Eliminar tu cuenta de la plataforma',
	DeleteAccountPanelInfoAlert:
		'Debes eliminar tus espacios de trabajo antes de eliminar tu perfil. Para continuar, cambia a un espacio de trabajo y selecciona Configuración > Configuración del espacio de trabajo.',
	DeleteAccountTitle: 'Eliminar cuenta',
	DeleteAppointment: 'Eliminar cita',
	DeleteAppointmentDescription: '¿Está seguro de que desea eliminar esta cita? Puede restaurarla más tarde.',
	DeleteAvailabilityScheduleFailure: 'No se pudo eliminar el horario de disponibilidad',
	DeleteAvailabilityScheduleSuccess: 'Horario de disponibilidad eliminado exitosamente',
	DeleteBillable: 'Eliminar facturable',
	DeleteBillableConfirmationMessage:
		'¿Está seguro de que desea eliminar este documento facturable? Esta acción no se puede deshacer.',
	DeleteBillingProfileConfirmationMessage: 'Esto eliminará permanentemente el perfil de facturación.',
	DeleteCardConfirmation: 'Esta es una acción permanente. Una vez eliminada la tarjeta, ya no podrás acceder a ella.',
	DeleteCategory: 'Eliminar categoría (esto no es permanente a menos que se guarden los cambios)',
	DeleteClientEventConfirmationDescription: 'Esto se eliminará permanentemente.',
	DeleteClients: 'Eliminar clientes',
	DeleteCollection: 'Eliminar colección',
	DeleteColumn: 'Eliminar columna',
	DeleteConversationConfirmationDescription:
		'Elimina esta conversación para siempre. Esta acción no se puede deshacer.',
	DeleteConversationConfirmationTitle: 'Eliminar conversación para siempre',
	DeleteExternalEventDescription: '¿Estás seguro de que quieres eliminar esta cita?',
	DeleteFileConfirmationModalPrompt: 'Una vez eliminado, no puede recuperar este archivo de nuevo.',
	DeleteFolder: 'Eliminar carpeta',
	DeleteFolderConfirmationMessage:
		'¿Estás seguro de que quieres eliminar esta carpeta {name}? Todos los elementos dentro de esta carpeta también se eliminarán. Puedes restaurarlo más tarde.',
	DeleteForever: 'Eliminar siempre',
	DeleteInsurancePayerConfirmationMessage:
		'Si elimina {payer}, se eliminará de su lista de pagadores de seguros. Esta acción es permanente y no se puede restaurar.',
	DeleteInsurancePayerFailure: 'No se pudo eliminar el pagador del seguro',
	DeleteInsurancePolicyConfirmationMessage: 'Esto eliminará permanentemente la póliza de seguro.',
	DeleteInvoiceConfirmationDescription:
		'Esta acción no se puede deshacer. Eliminará permanentemente la factura y todos los pagos asociados con ella.',
	DeleteLocationConfirmation:
		'Eliminar una ubicación es una acción permanente. Una vez que la elimines, ya no tendrás acceso a ella. Esta acción no se puede deshacer.',
	DeletePayer: 'Eliminar pagador',
	DeletePracticeWorkspace: 'Eliminar espacio de trabajo de práctica',
	DeletePracticeWorkspaceDescription: 'Eliminar permanentemente este espacio de trabajo de práctica',
	DeletePracticeWorkspaceFailedSnackbar: 'No se pudo eliminar el espacio de trabajo',
	DeletePracticeWorkspaceModalCancelButton: 'Sí, cancelar mi suscripción',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Antes de proceder con la eliminación de su espacio de trabajo, debe cancelar su suscripción primero.',
	DeletePracticeWorkspaceModalConfirmButton: 'Sí, eliminar espacio de trabajo de forma permanente',
	DeletePracticeWorkspaceModalDescription:
		'El espacio de trabajo {name} se eliminará de forma permanente y todos los miembros del equipo perderán el acceso. Descargue cualquier dato o mensaje importante que pueda necesitar antes de que se realice la eliminación. Esta acción no se puede deshacer.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Esta decisión se tomó debido a:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Razón',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Por favor, díganos por qué desea eliminar su cuenta.',
	DeletePracticeWorkspaceModalTitle: '¿Está seguro?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'Se ha eliminado el acceso de todos los miembros del equipo',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} ha sido eliminado correctamente',
	DeletePublicTemplateContent: 'Esto solo eliminará la plantilla pública y no la plantilla de tu equipo.',
	DeleteRecurringAppointmentModalTitle: 'Eliminar cita repetida',
	DeleteRecurringEventModalTitle: 'Eliminar reunión repetida',
	DeleteRecurringReminderModalTitle: 'Eliminar recordatorio repetido',
	DeleteRecurringTaskModalTitle: 'Eliminar tarea repetida',
	DeleteReminderConfirmation:
		'Esta es una acción permanente. Una vez que se elimine el recordatorio, ya no podrá acceder a él. Solo afectará a las nuevas citas',
	DeleteSection: 'Eliminar sección',
	DeleteSectionInfo:
		'Al eliminar la sección <strong>{section}</strong> se ocultarán todos los campos existentes en ella. Esta acción no se puede deshacer.',
	DeleteSectionWarning:
		'Los campos principales no pueden ser eliminados y serán trasladados a la sección existente <strong>{section}</strong>.',
	DeleteServiceFailure: 'Error al eliminar el servicio',
	DeleteServiceSuccess: 'Servicio eliminado con éxito',
	DeleteStaffScheduleOverrideDescription:
		'Eliminar esta anulación de fecha el {value} la eliminará de sus horarios y puede cambiar su servicio en línea disponible. Esta acción no se puede deshacer.',
	DeleteSuperbillConfirmationDescription:
		'Esta acción no se puede deshacer. Eliminará permanentemente el Recibo de Superbill.',
	DeleteSuperbillFailure: 'No se pudo eliminar el Recibo de Superbill',
	DeleteSuperbillSuccess: 'Recibo de Superbill eliminado exitosamente',
	DeleteTaxRateConfirmationDescription: '¿Está seguro de que desea eliminar esta tasa impositiva?',
	DeleteTemplateContent: 'Esta acción no se puede deshacer',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} eliminado correctamente',
	DeleteTemplateSuccessMessage: '{templateTitle} eliminado correctamente',
	DeleteTemplateTitle: '¿Estás seguro de que quieres eliminar esta plantilla?',
	DeleteTranscript: 'Eliminar transcripción',
	DeleteWorkspace: 'Eliminar espacio de trabajo',
	Deleted: 'Eliminada',
	DeletedBy: 'Eliminado por',
	DeletedContact: 'Contacto eliminado',
	DeletedOn: 'Eliminado el',
	DeletedStatusLabel: 'Estado eliminado',
	DeletedUserTooltip: 'Este cliente ha sido eliminado',
	DeliveryMethod: 'Método de entrega',
	Demo: 'Demo',
	Denied: 'Denegado',
	Dental: 'Odontología',
	DentalAssistant: 'Asistente dental',
	DentalHygienist: 'Higienista dental',
	Dentist: 'Dentista',
	Dentists: 'Dentistas',
	Description: 'Descripción',
	DescriptionMustNotExceed: 'La descripción no debe exceder {max} caracteres',
	DetailDurationWithStaff: '{duration} mins{staffName, select, null {} other { con {staffName}}}',
	Details: 'Detalles',
	Devices: 'Dispositivos',
	Diagnosis: 'Diagnóstico',
	DiagnosisAndBillingItems: 'Diagnóstico ',
	DiagnosisCode: 'Código de diagnóstico',
	DiagnosisCodeErrorMessage: 'Se requiere un código de diagnóstico',
	DiagnosisCodeSelectorPlaceholder: 'Buscar y agregar códigos de diagnóstico ICD-10',
	DiagnosisCodeSelectorTooltip:
		'Los códigos de diagnóstico se utilizan para automatizar los recibos de Superbill para el reembolso del seguro',
	DiagnosticCodes: 'Códigos de diagnóstico',
	Dictate: 'Dictar',
	DictatingIn: 'Dictando en',
	Dictation: 'Dictado',
	DidNotAttend: 'No asistió',
	DidNotComplete: 'No se completó',
	DidNotProviderEnoughValue: 'No proporcionó suficiente valor',
	DidntProvideEnoughValue: 'No proporcionó suficiente valor',
	DieteticsOrNutrition: 'Dietética o nutrición',
	Dietician: 'Dietista',
	Dieticians: 'Dietistas',
	Dietitian: 'Dietista',
	DigitalSign: 'Firma aquí:',
	DigitalSignHelp: '(Haz clic/presiona para dibujar)',
	DirectDebit: 'Transferencia bancaria',
	DirectTextLink: 'Enlace directo al texto',
	Disable: 'Desactivar',
	DisabledEmailInfo:
		'No podemos actualizar su dirección de correo electrónico ya que su cuenta no es administrada por nosotros',
	Discard: 'Descarte',
	DiscardChanges: 'Descartar cambios',
	DiscardDrafts: 'Descartar borradores',
	Disconnect: 'Disconnect',
	DisconnectAppConfirmation: '¿Desea desconectar esta aplicación?',
	DisconnectAppConfirmationDescription: '¿Estás seguro de que quieres desconectar esta aplicación?',
	DisconnectAppConfirmationTitle: 'Desconectar aplicación',
	Discount: 'Descuento',
	DisplayCalendar: 'Mostrar en Carepatron',
	DisplayName: 'Nombre para mostrar',
	DisplayedToClients: 'Mostrado a los clientes',
	DiversionalTherapist: 'Terapeuta de recreación',
	DoItLater: 'Hacerlo más tarde',
	DoNotImport: 'No importar',
	DoNotSend: 'No enviar',
	DoThisLater: 'Haz esto más tarde',
	DoYouWantToEndSession: '¿Quieres continuar o finalizar tu sesión ahora?',
	Doctor: 'Doctor',
	Doctors: 'Doctores',
	DoesNotRepeat: 'No se repite',
	DoesntWorkWellWithExistingTools: 'No funciona bien con nuestras herramientas o flujos de trabajo existentes.',
	DogWalker: 'Paseador de perros',
	Done: 'Hecho',
	DontAllowClientsToCancel: 'No permitir que los clientes cancelen',
	DontHaveAccount: '¿No tienes una cuenta?',
	DontSend: 'No envíes',
	Double: 'Doble',
	DowngradeTo: 'Bajar a {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Lo siento, no puedes reducir tu plan porque tienes demasiados miembros en el equipo. Por favor, elimina algunos de tu proveedor y vuelve a intentarlo',
	Download: 'Descargar',
	DownloadAsPdf: 'Descargar en formato PDF',
	DownloadERA: 'Descargar ERA',
	DownloadPDF: 'Descargar PDF',
	DownloadTemplateFileName: 'Carepatron Plantilla de Cambio.csv',
	DownloadTemplateTileDescription: 'Usa nuestra plantilla de hoja de cálculo para organizar y cargar tus clientes.',
	DownloadTemplateTileLabel: 'Descargar plantilla',
	Downloads: '{number, plural, one {# Descargar} other {# Descargas}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Borrador',
	DraftResponses: 'Proyecto de respuesta',
	DraftSaved: 'Cambios guardados',
	DragAndDrop: 'arrastrar y soltar',
	DragDropText: 'Arrastra y suelta documentos de salud',
	DragToMove: 'Arrastre para mover',
	DragToMoveOrActivate: 'Arrastra para mover o activar',
	DramaTherapist: 'Dramaterapeuta',
	DropdownFormFieldPlaceHolder: 'Elija opciones de la lista',
	DropdownFormPrimaryText: 'Lista desplegable',
	DropdownFormSecondaryText: 'Elija de una lista de opciones',
	DropdownTextFieldError: 'El texto de la opción desplegable no puede estar vacío',
	DropdownTextFieldPlaceholder: 'Agregar una opción desplegable',
	Due: 'Fecha de vencimiento',
	DueDate: 'Fecha de vencimiento',
	Duplicate: 'Duplicado',
	DuplicateAvailabilityScheduleFailure: 'No se pudo duplicar el horario de disponibilidad',
	DuplicateAvailabilityScheduleSuccess: 'Horario de disponibilidad duplicado {name} exitosamente',
	DuplicateClientBannerAction: 'Revisar',
	DuplicateClientBannerDescription:
		'La fusión de registros de clientes duplicados los consolida en uno solo, conservando toda la información única del cliente.',
	DuplicateClientBannerTitle: '{count} Duplicados encontrados',
	DuplicateColumn: 'Duplicar columna',
	DuplicateContactFieldSettingErrorSnackbar: 'No se pueden tener nombres de sección duplicados.',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'No se pueden tener nombres de campo duplicados.',
	DuplicateEmailError: 'Correo electrónico duplicado',
	DuplicateHeadingName: 'La sección {name} ya existe',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Ya existe una factura con el mismo número de factura.',
	DuplicateRecords: 'Registros duplicados',
	DuplicateRecordsMinimumError: 'Se deben seleccionar un mínimo de 2 registros',
	DuplicateRecordsRequired: 'Seleccione al menos 1 registro para separar',
	DuplicateServiceFailure: 'Error al duplicar <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Duplicado con éxito <strong>{title}</strong>',
	DuplicateTemplateFolderSuccessMessage: 'Carpeta duplicada correctamente',
	DuplicateTemplateSuccess: 'Plantilla duplicada con éxito',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'Código de diagnóstico',
	DxCodeSelectPlaceholder: 'Buscar y agregar códigos de la CIE-10',
	EIN: 'EIN',
	EMG: 'Electromiografía',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Ninguno',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>contiene ajustes</i>} other {}}',
	EarnReferralCredit: 'Gana ${creditAmount}',
	Economist: 'Economist',
	Edit: 'Editar',
	EditArrangements: 'Editar arreglos',
	EditBillTo: 'Editar Facturar a',
	EditClient: 'Editar Cliente',
	EditClientFileModalDescription: 'Edite el acceso a este archivo eligiendo las opciones en las casillas "Visto por"',
	EditClientFileModalTitle: 'Editar archivo',
	EditClientNoteModalDescription:
		'Edite el contenido en la nota. Use la sección "Visible por" para cambiar quién puede ver la nota.',
	EditClientNoteModalTitle: 'Editar nota',
	EditConnectedAppButton: 'Editar',
	EditConnections: 'Editar conexiones{account, select, null { } undefined { } other { para {account}}}',
	EditContactDetails: 'Editar detalles de contacto',
	EditContactFormIsClientLabel: 'Convertir en cliente',
	EditContactIsClientCheckboxWarning: 'La conversión de un contacto en un cliente no se puede deshacer',
	EditContactIsClientWanringModal:
		'La conversión de este contacto en un cliente no se puede deshacer. Sin embargo, todas las relaciones seguirán existiendo y ahora tendrás acceso a sus notas, archivos y otra documentación.',
	EditContactRelationship: 'Editar relación de contacto',
	EditDetails: 'Editar detalles',
	EditFileModalTitle: 'Editar archivo para {name}',
	EditFolder: 'Editar carpeta',
	EditFolderDescription: 'Renombrar la carpeta como...',
	EditInvoice: 'Editar factura',
	EditInvoiceDetails: 'Editar detalles de la factura',
	EditLink: 'Editar Enlace',
	EditLocation: 'Editar ubicación',
	EditLocationFailure: 'No se pudo actualizar la ubicación',
	EditLocationSucess: 'Ubicación actualizada con éxito',
	EditPaymentDetails: 'Editar detalles de pago',
	EditPaymentMethod: 'Editar método de pago',
	EditPersonalDetails: 'Editar detalles personales',
	EditPractitioner: 'Editar Practicante',
	EditProvider: 'Editar Proveedor',
	EditProviderDetails: 'Editar detalles del proveedor',
	EditRecurrence: 'Editar recurrencia',
	EditRecurringAppointmentModalTitle: 'Editar cita repetida',
	EditRecurringEventModalTitle: 'Editar reunión repetida',
	EditRecurringReminderModalTitle: 'Editar recordatorio repetido',
	EditRecurringTaskModalTitle: 'Editar tarea repetida',
	EditRelationshipModalTitle: 'Editar relación',
	EditService: 'Editar servicio',
	EditServiceFailure: 'No se pudo actualizar el servicio',
	EditServiceGroup: 'Editar colección',
	EditServiceGroupFailure: 'No se pudo actualizar la colección',
	EditServiceGroupSuccess: 'Se ha actualizado la colección con éxito',
	EditServiceSuccess: 'Se ha actualizado el servicio con éxito',
	EditStaffDetails: 'Editar detalles del personal',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'No se puede actualizar la dirección de correo electrónico. Cree un nuevo miembro del equipo con una nueva dirección de correo electrónico.',
	EditSubscriptionBilledQuantity: 'Cantidad facturada',
	EditSubscriptionBilledQuantityValue: '{billedUsers} miembros del equipo',
	EditSubscriptionLimitedTimeOffer: '¡Oferta por tiempo limitado! 50% de descuento por 6 meses.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'El costo de su suscripción se ajustará al agregar o eliminar miembros del equipo.',
	EditSubscriptionUpgradeContent:
		'Su cuenta se actualizará inmediatamente al nuevo plan y período de facturación. Cualquier cambio de precio se cargará automáticamente a su método de pago guardado o se acreditará a su cuenta.',
	EditSubscriptionUpgradePlanTitle: 'Actualizar plan de suscripción',
	EditSuperbillReceipt: 'Editar superbill',
	EditTags: 'Editar etiquetas',
	EditTemplate: 'Editar plantilla',
	EditTemplateFolderSuccessMessage: 'Carpeta actualizada exitosamente',
	EditValue: 'Editar {value}',
	Edited: 'Editado',
	Editor: 'Editor',
	EditorAlertDescription:
		'Se ha detectado un formato no compatible. Vuelva a cargar la aplicación o comuníquese con nuestro equipo de soporte.',
	EditorAlertTitle: 'Estamos teniendo problemas para mostrar este contenido.',
	EditorPlaceholder:
		'Comienza a escribir, elige una plantilla o agrega bloques básicos para capturar las respuestas de tus clientes',
	EditorTemplatePlaceholder: 'Comience a escribir o agregue componentes para crear una plantilla',
	EditorTemplateWithSlashCommandPlaceholder:
		'Empieza a escribir o agrega bloques básicos para capturar las respuestas de los clientes. Usa comandos de barra (/) para acciones rápidas.',
	EditorWithSlashCommandPlaceholder:
		'Comience a escribir, elija una plantilla o agregue bloques básicos para capturar las respuestas de los clientes. Use comandos de barra diagonal (/) para acciones rápidas.',
	EffectiveStartEndDate: 'Fecha efectiva de inicio y finalización',
	ElectricalEngineer: 'Ingeniero eléctrico',
	Electronic: 'Electrónico',
	ElectronicSignature: 'Firma electronica',
	ElementarySchoolTeacher: 'Profesor de primaria',
	Eligibility: 'Elegibilidad',
	Email: 'Correo electrónico',
	EmailAlreadyExists: 'La dirección de correo electrónico ya existe',
	EmailAndSms: 'Correo electrónico ',
	EmailBody: 'Cuerpo del correo electrónico',
	EmailContainsIgnoredDescription:
		'El siguiente correo electrónico contiene {count, plural, one {un remitente} other {remitentes}} correo electrónico que actualmente se ignora. ¿Quieres continuar?',
	EmailInviteToPortalBody: `Hola {contactName},

Siga este enlace para iniciar sesión en su portal seguro para clientes y administrar fácilmente su atención.

Saludos cordiales,

{providerName}`,
	EmailInviteToPortalSubject: 'Bienvenido a {providerName}',
	EmailInvoice: 'Enviar factura por correo electrónico',
	EmailInvoiceOverdueBody: `Hola {contactName}

Tu factura {invoiceNumber} está vencida.
Por favor, paga tu factura en línea usando el enlace a continuación.

Si tienes alguna pregunta, por favor avísanos.

Gracias,
{providerName}`,
	EmailInvoicePaidBody: `Hola {contactName}

Tu factura {invoiceNumber} está pagada.
Para ver y descargar una copia de tu factura, sigue el enlace a continuación.

Si tienes alguna pregunta, por favor avísanos.

Gracias,
{providerName}`,
	EmailInvoiceProcessingBody: `Hola {contactName}

Tu factura {invoiceNumber} está lista.
Sigue el enlace a continuación para ver tu factura.

Si tienes alguna pregunta, por favor avísanos.

Gracias,
{providerName}`,
	EmailInvoiceUnpaidBody: `Hola {contactName}

Tu factura {invoiceNumber} está lista para ser pagada antes del {dueDate}.
Para ver y pagar tu factura en línea, sigue el enlace a continuación.

Si tienes alguna pregunta, por favor avísanos.

Gracias,
{providerName}`,
	EmailInvoiceVoidBody: `Hola {contactName}

Tu factura {invoiceNumber} está anulada.
Para ver esta factura, sigue el enlace a continuación.

Si tienes alguna pregunta, por favor avísanos.

Gracias,
{providerName}`,
	EmailNotFound: 'Correo electrónico no encontrado',
	EmailNotVerifiedErrorCodeSnackbar:
		'No se puede realizar la acción. Necesitas verificar tu dirección de correo electrónico.',
	EmailNotVerifiedTitle: 'Verifica tu dirección de correo electrónico',
	EmailSendClientIntakeBody: `Hola {contactName},

{providerName} desea que proporcione información y revise documentos importantes. Siga el siguiente enlace para comenzar.

Saludos cordiales,

{providerName}`,
	EmailSendClientIntakeSubject: 'Bienvenido a {providerName}',
	EmailSuperbillReceipt: 'Enviar Superbill por correo electrónico',
	EmailSuperbillReceiptBody: `Hola {contactName},

{providerName} te ha enviado una copia de tu estado de cuenta o recibo de reembolso con fecha {date}.

Puedes descargarlo y presentarlo directamente a tu compañía de seguros`,
	EmailSuperbillReceiptFailure: 'No se pudo enviar el Recibo de Superbill',
	EmailSuperbillReceiptSubject: '{providerName} ha enviado un estado de cuenta o recibo de reembolso.',
	EmailSuperbillReceiptSuccess: 'Recibo de Superbill enviado exitosamente',
	EmailVerificationDescription: 'Estamos <span>verificando</span> su cuenta ahora',
	EmailVerificationNotification: 'Se ha enviado un correo electrónico de verificación a {email}',
	EmailVerificationSuccess: 'Su dirección de correo electrónico se ha cambiado correctamente a {email}',
	Emails: 'Correos electrónicos',
	EmergencyContact: 'Contacto de emergencia',
	EmployeesIdentificationNumber: 'Número de identificación del empleado',
	EmploymentStatus: 'Estado laboral',
	EmptyAgendaViewDescription: 'No hay eventos que mostrar. <mark>Crear una cita ahora</mark>',
	EmptyBin: 'Vaciar papelera',
	EmptyBinConfirmationDescription:
		'La papelera vacía eliminará todas las <strong>{total} conversaciones</strong> en Eliminadas. Esta acción no se puede deshacer.',
	EmptyBinConfirmationTitle: 'Eliminar conversaciones para siempre',
	EmptyTrash: 'Vaciar papelera',
	Enable: 'Permitir',
	EnableCustomServiceAvailability: 'Habilitar la disponibilidad del servicio',
	EnableCustomServiceAvailabilityDescription:
		'Por ejemplo, las citas iniciales solo se pueden reservar todos los días de 9 a 10 de la mañana."',
	EndCall: 'Finalizar llamada',
	EndCallConfirmationForCreator: 'Va a finalizar esto para todos porque es el iniciador de la llamada.',
	EndCallConfirmationHasActiveAttendees:
		'Has estado esperando durante mucho tiempo. El cliente ya se unió a la llamada. ¿Tú también quieres unirte?',
	EndCallForAll: 'Finalizar llamada para todos',
	EndDate: 'Fecha final',
	EndDictation: 'Fin del dictado',
	EndOfLine: 'No hay más citas',
	EndSession: 'Finalizar sesión',
	EndTranscription: 'Finalizar la transcripción',
	Ends: 'Termina',
	EndsOnDate: 'Termina el {date}',
	Enrol: 'Inscribirse',
	EnrollmentRejectedSubject: 'Tu inscripción con {payerName} ha sido rechazada',
	Enrolment: 'Inscripción',
	Enrolments: 'Inscripciones',
	EnrolmentsDescription: 'Ver y administrar las inscripciones de proveedores con la pagadora.',
	EnterAName: 'Ingresa un nombre...',
	EnterFieldLabel: 'Ingrese la etiqueta del campo...',
	EnterPaymentDetailsDescription:
		'El costo de su suscripción se ajustará automáticamente al agregar o eliminar usuarios.',
	EnterSectionName: 'Ingrese el nombre de la sección...',
	EnterSubscriptionPaymentDetails: 'Ingresar detalles de pago',
	EnvironmentalScientist: 'Environmental Scientist',
	Epidemiologist: 'Epidemiólogo',
	Eraser: 'Borrador',
	Error: 'Error',
	ErrorBoundaryAction: 'Recargar página',
	ErrorBoundaryDescription: 'Actualice la página y vuelva a intentarlo.',
	ErrorBoundaryTitle: '¡Vaya! Algo salió mal',
	ErrorCallNotFound: 'No se puede encontrar la llamada. Puede haber expirado o el creador la ha finalizado.',
	ErrorCannotAccessCallUninvitedCode: 'Lo siento, parece que no has sido invitado a esta llamada.',
	ErrorFileUploadCustomMaxFileCount: 'No se pueden cargar más de {count} archivos a la vez',
	ErrorFileUploadCustomMaxFileSize: 'El tamaño del archivo no puede superar los {mb} MB',
	ErrorFileUploadInvalidFileType:
		'Tipo de archivo inválido que podría contener posibles virus y software malintencionado',
	ErrorFileUploadMaxFileCount: 'No se pueden cargar más de 150 archivos a la vez',
	ErrorFileUploadMaxFileSize: 'El tamaño del archivo no puede superar los 100 MB',
	ErrorFileUploadNoFileSelected: 'Seleccione los archivos para cargar',
	ErrorInvalidNationalProviderId: 'El Id de Proveedor Nacional proporcionado no es válido',
	ErrorInvalidPayerId: 'El Id de Pagador proporcionado no es válido',
	ErrorInvalidTaxNumber: 'El Número de Identificación Fiscal proporcionado no es válido',
	ErrorInviteExistingProviderStaffCode: 'Este usuario ya está en el espacio de trabajo.',
	ErrorInviteStaffExistingUser: 'Lo siento, parece que el usuario que agregaste ya existe en nuestro sistema.',
	ErrorOnlySingleCallAllowed:
		'Solo se permite una llamada a la vez. Por favor, finalice la llamada actual antes de iniciar una nueva.',
	ErrorPayerNotFound: 'Pagador no encontrado',
	ErrorProfilePhotoMaxFileSize: '¡Error al cargar! Se ha alcanzado el límite de tamaño de archivo - 5MB',
	ErrorRegisteredExistingUser: 'Lo siento, parece que ya estás registrado.',
	ErrorUserSignInIncorrectCredentials: 'Correo electrónico o contraseña no válidos. Inténtalo de nuevo.',
	ErrorUserSigninGeneric: 'Lo siento, algo salió mal.',
	ErrorUserSigninUserNotConfirmed:
		'Lo siento, debes confirmar tu cuenta antes de iniciar sesión. Revisa tu bandeja de entrada para obtener instrucciones.',
	Errors: 'Errores',
	EssentialPlanInclusionFive: 'Importación de plantillas',
	EssentialPlanInclusionFour: '5 GB de almacenamiento',
	EssentialPlanInclusionHeader: 'Todo en Gratis  ',
	EssentialPlanInclusionOne: 'Recordatorios automáticos y personalizados',
	EssentialPlanInclusionSix: 'Soporte prioritario',
	EssentialPlanInclusionThree: 'Video chat',
	EssentialPlanInclusionTwo: 'Sincronización de calendario bidireccional',
	EssentialSubscriptionPlanSubtitle: 'Simplifique su práctica con lo esencial',
	EssentialSubscriptionPlanTitle: 'Básico',
	Esthetician: 'Esteticista',
	Estheticians: 'Esteticistas',
	EstimatedArrivalDate: 'Est. llegada {numberOfDaysFromNow}',
	Ethnicity: 'Etnicidad',
	Europe: 'Europa',
	EventColor: 'Color de la reunión',
	EventName: 'Nombre del evento',
	EventType: 'Tipo de evento',
	Every: 'Cada',
	Every2Weeks: 'Cada 2 semanas',
	EveryoneInWorkspace: 'Todos en el espacio de trabajo',
	ExercisePhysiologist: 'Fisiólogo del ejercicio',
	Existing: 'Existente',
	ExistingClients: 'Clientes existentes',
	ExistingFolders: 'Carpetas existentes',
	ExpiredPromotionCode: 'El código de promoción ha caducado',
	ExpiredReferralDescription: 'La referencia ha caducado',
	ExpiredVerificationLink: 'Enlace de verificación caducado',
	ExpiredVerificationLinkDescription: `Lo sentimos, pero el enlace de verificación en el que hizo clic ha caducado. Esto puede suceder si esperó más de 24 horas para hacer clic en el enlace o si ya ha utilizado el enlace para verificar su dirección de correo electrónico.

Por favor, solicite un nuevo enlace de verificación para verificar su dirección de correo electrónico.`,
	ExpiryDateRequired: 'Se requiere la fecha de vencimiento',
	ExploreFeature: '¿Qué le gustaría explorar primero?',
	ExploreOptions: 'Elija una o más opciones para explorar...',
	Export: 'Exportar',
	ExportAppointments: 'Exportar citas',
	ExportClaims: 'Exportar reclamaciones',
	ExportClaimsFilename: 'Reclamaciones {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription:
		'Ihre Datei konnte aufgrund eines Fehlers nicht heruntergeladen werden.',
	ExportClientsDownloadFailureSnackbarTitle: 'Download fehlgeschlagen',
	ExportClientsFailureSnackbarDescription: 'Por favor, inténtalo de nuevo más tarde.',
	ExportClientsFailureSnackbarTitle: 'Error al exportar datos del cliente',
	ExportClientsModalDescription: `Este proceso de exportación de datos puede tardar unos minutos dependiendo de la cantidad de datos que se exporten. Recibirás una notificación por correo electrónico con un enlace una vez que esté listo para su descarga.

Deseas proceder con la exportación de los datos del cliente?`,
	ExportClientsModalTitle: 'Exportar datos del cliente',
	ExportCms1500: 'Exportación CMS1500',
	ExportContactFailedNotificationSubject: 'Su exportación de datos ha fallado',
	ExportFailed: 'Error al exportar',
	ExportGuide: 'Guía de exportación',
	ExportInvoiceFileName: 'Transacciones {fromDate}-{toDate}.csv',
	ExportPayments: 'Exportar pagos',
	ExportPaymentsFilename: 'Pagos {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Su documento está listo para descargarse.',
	ExportPrintWaitMessage: 'Preparando su documento. Por favor espere...',
	ExportTextOnly: 'Exportar solo texto',
	ExportTransactions: 'Exportar transacciones',
	Exporting: 'Exportando',
	ExportingData: 'Exportando datos',
	ExtendedFamilyMember: 'Miembro de la familia extendida',
	External: 'Externo',
	ExternalEventInfoBanner: 'Esta cita es de un calendario sincronizado y puede que falten elementos.',
	ExtraLarge: 'Extra grande',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Fallido',
	FailedToJoinTheMeeting: 'No se pudo unir a la reunión.',
	FallbackPageDescription: `Parece que esta página no existe, es posible que necesites {refreshButton} esta página para obtener los últimos cambios.

De lo contrario, comunícate con el soporte de Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'actualizar',
	FallbackPageTitle: '¡Ups...!',
	FamilyPlanningService: 'Servicio de planificación familiar',
	FashionDesigner: 'Diseñador de moda',
	FastTrackInvoicingAndBilling: 'Acelerar la facturación y el pago',
	Father: 'Padre',
	FatherInLaw: 'Suegro',
	Favorite: 'Favorito',
	FeatureBannerCalendarTile1ActionLabel: 'Reserva en línea • 2 minutos',
	FeatureBannerCalendarTile1Description:
		'Simplemente envíe un correo electrónico, texto o agregue disponibilidad a su sitio web',
	FeatureBannerCalendarTile1Title: 'Permitir que sus clientes reserven en línea',
	FeatureBannerCalendarTile2ActionLabel: 'Automatizar recordatorios • 2 minutos',
	FeatureBannerCalendarTile2Description: 'Aumente la asistencia de los clientes con recordatorios automatizados',
	FeatureBannerCalendarTile2Title: 'Reduzca las ausencias',
	FeatureBannerCalendarTile3Title: 'Programación y flujo de trabajo',
	FeatureBannerCalendarTitle: 'Simplifique la programación',
	FeatureBannerCallsTile1ActionLabel: 'Comience la llamada de telesalud',
	FeatureBannerCallsTile1Description:
		'Los clientes acceden con solo un enlace. Sin inicios de sesión, contraseñas o molestias',
	FeatureBannerCallsTile1Title: 'Comience una videollamada desde cualquier lugar',
	FeatureBannerCallsTile2ActionLabel: 'Conectar aplicaciones • 4 minutos',
	FeatureBannerCallsTile2Description: 'Conecte sin problemas a otros proveedores de telesalud preferidos',
	FeatureBannerCallsTile2Title: 'Conecte sus aplicaciones de telesalud',
	FeatureBannerCallsTile3Title: 'Llamadas',
	FeatureBannerCallsTitle: 'Conéctese con los clientes: en cualquier lugar, en cualquier momento',
	FeatureBannerClientsTile1ActionLabel: 'Importar ahora • 2 minutos',
	FeatureBannerClientsTile1Description:
		'Comience rápidamente con nuestra herramienta automatizada de importación de clientes',
	FeatureBannerClientsTile1Title: '¿Tienes muchos clientes?',
	FeatureBannerClientsTile2ActionLabel: 'Personalizar la admisión • 2 minutos',
	FeatureBannerClientsTile2Description: 'Eliminar el papeleo de admisión y mejorar las experiencias de los clientes',
	FeatureBannerClientsTile2Title: 'Vaya sin papel',
	FeatureBannerClientsTile3Title: 'Portal del cliente',
	FeatureBannerClientsTitle: 'Todo comienza con tus clientes',
	FeatureBannerHeader: '¡Por la comunidad, para la comunidad!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatizar pagos • 2 minutos',
	FeatureBannerInvoicesTile1Description: 'Evite conversaciones incómodas con pagos automatizados',
	FeatureBannerInvoicesTile1Title: 'Reciba el pago 2 veces más rápido',
	FeatureBannerInvoicesTile2ActionLabel: 'Seguimiento de flujo de caja • 2 minutos',
	FeatureBannerInvoicesTile2Description: 'Reduzca las facturas impagadas y controle sus ingresos',
	FeatureBannerInvoicesTile2Title: 'Realice un seguimiento de sus ingresos sin dolor',
	FeatureBannerInvoicesTile3Title: 'Facturación y Pagos',
	FeatureBannerInvoicesTitle: 'Una cosa menos de la que preocuparse',
	FeatureBannerSubheader:
		'Plantillas de Carepatron creadas por nuestro equipo y la comunidad. ¡Prueba nuevos recursos o comparte los tuyos!',
	FeatureBannerTeamTile1ActionLabel: 'Invitar ahora',
	FeatureBannerTeamTile1Description: 'Invita a miembros del equipo a tu cuenta y facilita la colaboración',
	FeatureBannerTeamTile1Title: 'Reúna a su equipo',
	FeatureBannerTeamTile2ActionLabel: 'Establecer disponibilidad • 2 minutos',
	FeatureBannerTeamTile2Description: 'Administra la disponibilidad de tus equipos para evitar reservas dobles',
	FeatureBannerTeamTile2Title: 'Establezca su disponibilidad',
	FeatureBannerTeamTile3ActionLabel: 'Establecer permisos • 2 minutos',
	FeatureBannerTeamTile3Description:
		'Controle el acceso a los datos confidenciales y las herramientas para el cumplimiento',
	FeatureBannerTeamTile3Title: 'Personalice los permisos y el acceso',
	FeatureBannerTeamTitle: 'Nada grande se logra solo',
	FeatureBannerTemplatesTile1ActionLabel: 'Explorar la biblioteca • 2 minutos',
	FeatureBannerTemplatesTile1Description: 'Elija entre una increíble biblioteca de recursos personalizables',
	FeatureBannerTemplatesTile1Title: 'Reduzca su carga de trabajo',
	FeatureBannerTemplatesTile2ActionLabel: 'Enviar ahora • 2 minutos',
	FeatureBannerTemplatesTile2Description: 'Envía plantillas hermosas a los clientes para que las completen',
	FeatureBannerTemplatesTile2Title: 'Haz que la documentación sea divertida',
	FeatureBannerTemplatesTile3Title: 'Plantillas',
	FeatureBannerTemplatesTitle: 'Plantillas para absolutamente cualquier cosa',
	FeatureLimitBannerDescription:
		'Actualiza ahora para seguir creando y gestionando {featureName} sin interrupciones y saca el máximo provecho de Carepatron!',
	FeatureLimitBannerTitle: 'Estás {percentage}% de camino a tu límite de {featureName}',
	FeatureRequiresUpgrade: 'Esta función requiere una actualización',
	Fee: 'Tarifa',
	Female: 'Femenino',
	FieldLabelTooltip: '{isHidden, select, true {Mostrar} other {Ocultar}} etiqueta del campo',
	FieldName: 'Nombre del campo',
	FieldOptionsFirstPart: 'Primera palabra',
	FieldOptionsMiddlePart: 'Palabras intermedias',
	FieldOptionsSecondPart: 'Última palabra',
	FieldOptionsWholeField: 'Campo completo',
	FieldType: 'Tipo de campo',
	Fields: 'Campos',
	File: 'Archivo',
	FileDownloaded: '<strong>{fileName}</strong> descargado',
	FileInvalidType: 'Archivo no compatible.',
	FileNotFound: 'Archivo no encontrado',
	FileNotFoundDescription: 'El archivo que estás buscando no se puede encontrar',
	FileTags: 'Etiquetas de archivo',
	FileTagsHelper: 'Las etiquetas se aplicarán a todos los archivos',
	FileTooLarge: 'Archivo demasiado grande.',
	FileTooSmall: 'Archivo demasiado pequeño.',
	FileUploadComplete: 'Completo',
	FileUploadFailed: 'Fallido',
	FileUploadInProgress: 'Cargando',
	FileUploadedNotificationSubject: '{actorProfileName} ha subido un archivo',
	Files: 'Archivos',
	FillOut: 'Llenar',
	Filter: 'Filtro',
	FilterBy: 'Filtrar por',
	FilterByAmount: 'Filtrar por cantidad',
	FilterByClient: 'Filtrar por cliente',
	FilterByLocation: 'Filtrar por ubicación',
	FilterByService: 'Filtrar por servicio',
	FilterByStatus: 'Filtrar por estado',
	FilterByTags: 'Filtrar por etiquetas',
	FilterByTeam: 'Filtrar por equipo',
	Filters: 'Filtros',
	FiltersAppliedToView: 'Filtros aplicados a la vista',
	FinalAppointment: 'Cita final',
	FinalizeImport: 'Finalizar importación',
	FinancialAnalyst: 'Analista financiero',
	Finish: 'Finalizar',
	Firefighter: 'Bombero',
	FirstName: 'Nombre de pila',
	FirstNameLastInitial: 'Nombre, inicial del apellido',
	FirstPerson: '1st person',
	FolderName: 'Nombre de la carpeta',
	Folders: 'Carpetas',
	FontFamily: 'Familia de fuentes',
	ForClients: 'Para clientes',
	ForClientsDetails: 'Recibo asistencia o servicios relacionados con la salud',
	ForPractitioners: 'Para practicantes',
	ForPractitionersDetails: 'Administra y haz crecer tu práctica',
	ForgotPasswordConfirmAccessCode: 'Código de confirmación',
	ForgotPasswordConfirmNewPassword: 'Nueva contraseña',
	ForgotPasswordConfirmPageDescription:
		'Por favor ingresa tu dirección de correo electrónico, una nueva contraseña y el código de confirmación que acabamos de enviarte.',
	ForgotPasswordConfirmPageTitle: 'Restablecer contraseña',
	ForgotPasswordPageButton: 'Enviar enlace de restablecimiento',
	ForgotPasswordPageDescription:
		'Ingresa tu correo electrónico y te enviaremos un enlace para restablecer tu contraseña.',
	ForgotPasswordPageTitle: 'Contraseña olvidada',
	ForgotPasswordSuccessPageDescription: 'Revisa tu bandeja de entrada para encontrar el enlace de restablecimiento.',
	ForgotPasswordSuccessPageTitle: '¡Enlace de restablecimiento enviado!',
	Form: 'Formulario',
	FormAnswersSentToEmailNotification: 'Hemos enviado una copia de sus respuestas a',
	FormBlocks: 'Bloques de formulario',
	FormFieldAddOption: 'Agregar opción',
	FormFieldAddOtherOption: 'Agregar "otra"',
	FormFieldOptionPlaceholder: 'Opción {index}',
	FormStructures: 'Estructuras de formulario',
	Format: 'Formato',
	FormatLinkButtonColor: 'Color del botón',
	Forms: 'Formularios',
	FormsAndAgreementsValidationMessage:
		'Todos los formularios y acuerdos deben completarse para continuar con el proceso de admisión.',
	FormsCategoryDescription: 'Para recopilar y organizar los detalles del paciente',
	Frankfurt: 'Fráncfort',
	Free: 'Gratis',
	FreePlanInclusionFive: 'Facturación automatizada ',
	FreePlanInclusionFour: 'Portal del cliente',
	FreePlanInclusionHeader: 'Empezar con',
	FreePlanInclusionOne: 'Clientes ilimitados',
	FreePlanInclusionSix: 'Soporte en vivo',
	FreePlanInclusionThree: '1 GB de almacenamiento',
	FreePlanInclusionTwo: 'Telesalud',
	FreeSubscriptionPlanSubtitle: 'Gratis para todos',
	FreeSubscriptionPlanTitle: 'Gratis',
	Friday: 'Viernes',
	From: 'Desde',
	FullName: 'Nombre completo',
	FunctionalMedicineOrNaturopath: 'Medicina funcional o naturópata',
	FuturePaymentsAuthoriseProvider: 'Permitir que {provider} use pagos futuros',
	FuturePaymentsSavePaymentMethod: 'Guardar {paymentMethod} para pagos futuros',
	GST: 'IVA',
	Gender: 'Género',
	GeneralAvailability: 'Disponibilidad general',
	GeneralAvailabilityDescription:
		'Establezca cuando esté regularmente disponible. Los clientes solo podrán reservar sus servicios durante las horas disponibles.',
	GeneralAvailabilityDescription2:
		'Cree horarios basados ​​en su disponibilidad y las ofertas de servicios deseadas en momentos específicos para determinar la disponibilidad de su reserva en línea.',
	GeneralAvailabilityInfo: 'Sus horas disponibles determinarán su disponibilidad de reserva en línea',
	GeneralAvailabilityInfo2:
		'Los servicios que ofrecen eventos para grupos deben utilizar un nuevo horario para reducir las horas disponibles que pueden reservar los clientes en línea.',
	GeneralHoursPlural: '{count} {count, plural, one {hora} other {horas}}',
	GeneralPractitioner: 'Médico general',
	GeneralPractitioners: 'Médicos generales',
	GeneralServiceAvailabilityInfo: 'Este horario anulará el comportamiento para los miembros del equipo asignados',
	Generate: 'Generar',
	GenerateBillingItemsBannerContent:
		'Los elementos de facturación no se crean automáticamente para citas recurrentes.',
	GenerateItems: 'Generar elementos',
	GenerateNote: 'Generar nota',
	GenerateNoteConfirmationModalDescription:
		'¿Qué le gustaría hacer? ¿Crear una nueva nota generada, agregarla a la existente o reemplazar su contenido?',
	GenerateNoteFor: 'Generar nota para',
	GeneratingContent: 'Generando contenido...',
	GeneratingNote: 'Generando tu nota...',
	GeneratingTranscript: 'Generando transcripción',
	GeneratingTranscriptDescription: 'Esto puede tardar unos minutos en procesarse.',
	GeneratingYourTranscript: 'Generando tu transcripción',
	GenericErrorDescription: 'No se pudo cargar {module}. Por favor, inténtelo de nuevo más tarde.',
	GenericErrorTitle: 'Ocurrió un error inesperado',
	GenericFailureSnackbar: 'Lo siento, ocurrió algo inesperado. Por favor, actualice la página e inténtelo de nuevo.',
	GenericSavedSuccessSnackbar: '¡Éxito! Cambios guardados',
	GeneticCounselor: 'Asesor genético',
	Gerontologist: 'Gerontólogo',
	Get50PercentOff: '¡Obtén 50% de descuento!',
	GetHelp: 'Obtener ayuda',
	GetStarted: 'Empezar',
	GettingStartedAppointmentTypes: 'Crea tipos de citas',
	GettingStartedAppointmentTypesDescription:
		'Simplifica tu programación y facturación personalizando tus servicios, precios y códigos de facturación',
	GettingStartedAppointmentTypesTitle: 'Programa y factura rápidamente',
	GettingStartedClients: 'Añade a tus clientes',
	GettingStartedClientsDescription: 'Ponte en marcha con los clientes para futuras citas, notas y pagos',
	GettingStartedClientsTitle: 'Todo comienza con los clientes',
	GettingStartedCreateClient: 'Crear cliente',
	GettingStartedImportClients: 'Importar clientes',
	GettingStartedInvoices: 'Factura como un profesional',
	GettingStartedInvoicesDescription:
		'Es fácil crear facturas profesionales. Agrega tu logotipo, ubicación y términos de pago',
	GettingStartedInvoicesTitle: 'Da lo mejor de ti',
	GettingStartedMobileApp: 'Obtén la aplicación móvil',
	GettingStartedMobileAppDescription:
		'Puedes descargar Carepatron en tu dispositivo iOS, Android o de escritorio para un fácil acceso sobre la marcha',
	GettingStartedMobileAppTitle: 'Trabaja desde cualquier lugar',
	GettingStartedNavItem: 'Empezando',
	GettingStartedPageTitle: 'Empezando con Carepatron',
	GettingStartedPayments: 'Acepta pagos en línea',
	GettingStartedPaymentsDescription: `Cobra más rápido habilitando a tus clientes para pagar en línea.
	Mira todas tus facturas y pagos en un solo lugar`,
	GettingStartedPaymentsTitle: 'Facilita los pagos',
	GettingStartedSaveBranding: 'Guardar marca',
	GettingStartedSyncCalendars: 'Sincroniza otros calendarios',
	GettingStartedSyncCalendarsDescription:
		'Carepatron verifica tu calendario en busca de conflictos, por lo que las citas solo se programan cuando estás disponible',
	GettingStartedSyncCalendarsTitle: 'Mantente siempre actualizado',
	GettingStartedVideo: 'Mira un video de introducción',
	GettingStartedVideoDescription:
		'El primer espacio de trabajo de atención médica todo en uno para pequeños equipos y sus clientes',
	GettingStartedVideoTitle: 'Bienvenido a Carepatron',
	GetttingStartedGetMobileDownload: 'Descarga la aplicación',
	GetttingStartedGetMobileNoDownload:
		'No compatible con este navegador. Si estás usando un iPhone o iPad, abre esta página en Safari. De lo contrario, intenta abrirla en Chrome.',
	Glossary: 'Glosario',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'Gmail solo permite enviar 500 mensajes desde tu cuenta en un día. Algunos mensajes pueden fallar. ¿Deseas continuar?',
	GoToAppointment: 'Ir a la cita',
	GoToApps: 'Ir a aplicaciones',
	GoToAvailability: 'Ir a disponibilidad',
	GoToClientList: 'Ir a la lista de clientes',
	GoToClientRecord: 'Ir al registro del cliente',
	GoToClientSettings: 'Ir a configuración de cliente ahora',
	GoToInvoiceTemplates: 'Ir a las plantillas de facturas',
	GoToNotificationSettings: 'Ir a la configuración de notificaciones',
	GoToPaymentSettings: 'Ir a la configuración de pago',
	Google: 'Google',
	GoogleCalendar: 'Calendario de Google',
	GoogleColor: 'Color del calendario de Google',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'ID de contenedor de Google Tag Manager',
	GotIt: '¡Entendido!',
	Goto: 'Ir a',
	Granddaughter: 'Nieta',
	Grandfather: 'Abuelo',
	Grandmother: 'Abuela',
	Grandparent: 'Abuelo/Abuela',
	Grandson: 'Nieto',
	GrantPortalAccess: 'Permitir acceso al portal',
	GraphicDesigner: 'Diseñador gráfico',
	Grid: 'Red',
	GridView: 'Vista en cuadrícula',
	Group: 'Grupo',
	GroupBy: 'Grupo por',
	GroupEvent: 'Evento de grupo',
	GroupEventHelper: 'Establecer un límite de asistentes para el servicio',
	GroupFilterLabel: 'Todo {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'Identificación del grupo',
	GroupInputFieldsFormPrimaryText: 'Campos de entrada de grupo',
	GroupInputFieldsFormSecondaryText: 'Elija o agregue campos personalizados',
	GuideTo: 'Guía de {value}',
	GuideToImproveVideoQuality: 'Guía para mejorar la calidad del vídeo',
	GuideToManagingPayers: 'Gestionar pagadores',
	GuideToSubscriptionsBilling: 'Guía de facturación de suscripciones',
	GuideToTroubleshooting: 'Guía para la resolución de problemas',
	Guidelines: 'Pautas',
	GuidelinesCategoryDescription: 'Para guiar la toma de decisiones clínicas',
	HST: 'HST',
	HairStylist: 'Estilista',
	HaveBeenWaiting: 'Has estado esperando',
	HeHim: 'Él/Él',
	HeaderAccountSettings: 'Configuración de perfil',
	HeaderCalendar: 'Calendario',
	HeaderCalls: 'Llamadas',
	HeaderClientAppAccountSettings: 'Configuración de la cuenta',
	HeaderClientAppCalls: 'Llamadas',
	HeaderClientAppMyDocumentation: 'Documentación',
	HeaderClientAppMyRelationships: 'Mis relaciones',
	HeaderClients: 'Clientes',
	HeaderHelp: 'Ayuda',
	HeaderMoreOptions: 'Más opciones',
	HeaderStaff: 'Personal',
	HealthCoach: 'Entrenador de salud',
	HealthCoaches: 'Entrenadores de salud',
	HealthEducator: 'Educador sanitario',
	HealthInformationTechnician: 'Técnico de información sanitaria',
	HealthPolicyExpert: 'Experto en política sanitaria',
	HealthServicesAdministrator: 'Administrador de servicios sanitarios',
	HelpArticles: 'Artículos de ayuda',
	HiddenColumns: 'Columnas ocultas',
	HiddenFields: 'Campos Ocultos',
	HiddenSections: 'Secciones ocultas',
	HiddenSectionsAndFields: 'Secciones/campos ocultos',
	HideColumn: 'Ocultar columna',
	HideColumnButton: 'Ocultar columna {value} botón',
	HideDetails: 'Ocultar detalles',
	HideField: 'Ocultar campo',
	HideFullAddress: 'Ocultar',
	HideMenu: 'Ocultar el menú',
	HideMergeSummarySidebar: 'Ocultar resumen de fusión',
	HideSection: 'Ocultar sección',
	HideYourView: 'Ocultar tu vista',
	Highlight: 'Color de resaltado',
	Highlighter: 'Resaltador',
	History: 'Historia',
	HistoryItemFooter: '{actors, select, undefined {{date} a las {time}} other {Por {actors} • {date} a las {time}}}',
	HistorySidePanelEmptyState: 'No se encontraron registros de historial',
	HistoryTitle: 'Registro de actividad',
	HolisticHealthPractitioner: 'Profesional de la salud holística',
	HomeCaregiver: 'Cuidador en el hogar',
	HomeHealthAide: 'Auxiliar sanitario a domicilio',
	HomelessShelter: 'Refugio para personas sin hogar',
	HourAbbreviation: '{count} {count, plural, one {hr} other {hrs}}',
	Hourly: 'Por hora',
	HoursPlural: '{age, plural, one {# hora} other {# horas}}',
	HowCanWeImprove: '¿Cómo podemos mejorar esto?',
	HowCanWeImproveResponse: '¿Cómo podemos mejorar esta respuesta?',
	HowDidWeDo: '¿Cómo lo hicimos?',
	HowDoesReferralWork: 'Guía del programa de referidos.',
	HowToUseAiSummarise: 'Cómo utilizar el resumen de IA',
	HumanResourcesManager: 'Gestor de recursos humanos',
	Husband: 'Esposo',
	Hypnotherapist: 'Hipnoterapeuta',
	IVA: 'IVA',
	IgnoreNotification: 'Ignorar notificación',
	IgnoreOnce: 'Ignorar una vez',
	IgnoreSender: 'Ignorar remitente',
	IgnoreSenderDescription: `Las conversaciones futuras de este remitente se moverán automáticamente a 'Otros'. ¿Está seguro de que desea ignorar a estos remitentes?`,
	IgnoreSenders: 'Ignorar remitentes',
	IgnoreSendersSuccess: 'Dirección de correo electrónico ignorada <mark>{addresses}</mark>',
	Ignored: 'Ignorado',
	Image: 'Imagen',
	Import: 'Importar',
	ImportActivity: 'Importar actividad',
	ImportClientSuccessSnackbarDescription: 'Su archivo se ha importado correctamente',
	ImportClientSuccessSnackbarTitle: 'Importación correcta',
	ImportClients: 'Importar clientes',
	ImportClientsFailureSnackbarDescription: 'Su archivo no se ha podido importar correctamente debido a un error',
	ImportClientsFailureSnackbarTitle: 'Importación fallida',
	ImportClientsGuide: 'Guía para importar clientes',
	ImportClientsInProgressSnackbarDescription: 'Esto sólo debería tardar un minuto en completarse',
	ImportClientsInProgressSnackbarTitle: 'Importando <strong>{fileName}</strong>',
	ImportClientsModalDescription:
		'Elige de dónde proceden tus datos, ya sea un archivo de tu dispositivo, un servicio de terceros u otra plataforma de software.',
	ImportClientsModalFileUploadHelperText: 'Admite {fileTypes}. Límite de tamaño {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Guía para importar datos del cliente',
	ImportClientsModalStep1Label: 'Elegir fuente de datos',
	ImportClientsModalStep2Label: 'Subir archivo',
	ImportClientsModalStep3Label: 'Revisar campos',
	ImportClientsModalTitle: 'Importando los datos de sus clientes',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {cliente} other {clientes}} listo para importar',
	ImportContactFailedNotificationSubject: 'Su importación de datos ha fallado',
	ImportDataSourceSelectorLabel: 'Importar fuente de datos de',
	ImportDataSourceSelectorPlaceholder: 'Buscar o elegir fuente de datos de importación',
	ImportExportButton: 'Importar/Exportar',
	ImportFailed: 'Importación fallida',
	ImportFromAnotherPlatformTileDescription: 'Descarga una exportación de tus archivos de cliente y súbelos aquí.',
	ImportFromAnotherPlatformTileLabel: 'Importar desde otra plataforma',
	ImportGuide: 'Guía de importación',
	ImportInProgress: 'Importación en curso',
	ImportProcessing: 'Importando procesamiento...',
	ImportSpreadsheetDescription:
		'Puede importar su lista de clientes existente a Carepatron subiendo un archivo de hoja de cálculo con datos tabulares, como .CSV, .XLS o .XLSX',
	ImportSpreadsheetTitle: 'Importe su archivo de hoja de cálculo',
	ImportTemplates: 'Importar plantillas',
	Importing: 'Importando',
	ImportingCalendarProductEvents: 'Importando eventos de {product}',
	ImportingData: 'Importando datos',
	ImportingSpreadsheetDescription: 'Esto debería tardar hasta un minuto en completarse',
	ImportingSpreadsheetTitle: 'Importando su hoja de cálculo',
	ImportsInProgress: 'Importaciones en curso',
	InPersonMeeting: 'Reunión en persona',
	InProgress: 'En curso',
	InTransit: 'En tránsito',
	InTransitTooltip:
		'El saldo en tránsito incluye todos los pagos de facturas pagadas de Stripe a su cuenta bancaria. Estos fondos suelen tardar de 3 a 5 días en liquidarse.',
	Inactive: 'Inactivo',
	InboundOrOutboundCalls: 'Llamadas entrantes o salientes',
	Inbox: 'Bandeja de entrada',
	InboxAccessRestricted: 'Acceso restringido. Por favor, contacte al propietario del buzón para obtener permisos.',
	InboxAccountAlreadyConnected: 'El canal que intentó conectarse ya está conectado a Carepatron',
	InboxAddAttachments: 'Adjuntar archivos',
	InboxAreYouSureDeleteMessage: '¿Estás seguro que deseas eliminar este mensaje?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Se cerró correctamente # conversación} other {Se cerraron correctamente # conversaciones}}',
	InboxBulkComposeModalTitle: 'Componer mensaje masivo',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Se eliminó # conversación correctamente} other {Se eliminaron # conversaciones correctamente}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Marcada # conversación como leída correctamente} other {Marcadas # conversaciones como leídas correctamente}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Se reabrió correctamente # conversación} other {Se reabrieron correctamente # conversaciones}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Marcó correctamente # conversación como no leída} other {Marcó correctamente # conversaciones como no leídas}}',
	InboxChatCreateGroup: 'Crear grupo',
	InboxChatDeleteGroupModalDescription:
		'¿Está seguro de que desea eliminar este grupo? Todos los mensajes y archivos adjuntos se eliminarán.',
	InboxChatDeleteGroupModalTitle: 'Eliminar grupo',
	InboxChatDiscardDraft: 'Descartar borrador',
	InboxChatDragDropText: 'Arrastra los archivos aquí para subirlos',
	InboxChatGroupConversation: 'Conversación grupal',
	InboxChatGroupCreateModalDescription:
		'Comienza un nuevo grupo para mensajear y colaborar con tu equipo, clientes o comunidad.',
	InboxChatGroupCreateModalTitle: 'Crear grupo',
	InboxChatGroupMembers: 'Miembros del grupo',
	InboxChatGroupModalGroupNameFieldLabel: 'Nombre del grupo',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'E.g. atención al cliente, administrador',
	InboxChatGroupModalGroupNameFieldRequired: 'Este campo es obligatorio',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Se requiere al menos un miembro',
	InboxChatGroupModalMembersFieldLabel: 'Elija miembros del grupo',
	InboxChatGroupModalMembersFieldPlaceholder: 'Elegir miembros',
	InboxChatGroupUpdateModalTitle: 'Administrar grupo',
	InboxChatLeaveGroup: 'Abandonar grupo',
	InboxChatLeaveGroupModalDescription:
		'¿Está seguro de que quiere salir de este grupo? Ya no recibirá mensajes ni actualizaciones.',
	InboxChatLeaveGroupModalTitle: 'Abandonar grupo',
	InboxChatLeftGroupMessage: 'Mensaje de grupo izquierdo',
	InboxChatManageGroup: 'Gestionar grupo',
	InboxChatSearchParticipants: 'Elegir destinatarios',
	InboxCloseConversationSuccess: 'Conversación cerrada con éxito',
	InboxCompose: 'Componer',
	InboxComposeBulk: 'Mensaje masivo',
	InboxComposeCarepatronChat: 'Mensajero',
	InboxComposeChat: 'Redactar chat',
	InboxComposeDisabledNoConnection: 'Conecta una cuenta de correo electrónico para enviar mensajes.',
	InboxComposeDisabledNoPermissionTooltip: 'No tienes permiso para enviar mensajes desde este buzón.',
	InboxComposeEmail: 'Escribir correo',
	InboxComposeMessageFrom: 'De',
	InboxComposeMessageRecipientBcc: 'Cco',
	InboxComposeMessageRecipientCc: 'Cc',
	InboxComposeMessageRecipientTo: 'Para',
	InboxComposeMessageSubject: 'Asunto:',
	InboxConnectAccountButton: 'Conecta tu correo electrónico',
	InboxConnectedDescription: 'Tu bandeja de entrada no tiene comunicaciones.',
	InboxConnectedHeading:
		'Tus conversaciones aparecerán aquí tan pronto como comiences a intercambiar comunicaciones.',
	InboxConnectedHeadingClientView: 'Optimice las comunicaciones con sus clientes',
	InboxCreateFirstInboxButton: 'Crea tu primera bandeja de entrada',
	InboxCreationSuccess: 'Buzón creado con éxito',
	InboxDeleteAttachment: 'Eliminar archivo adjunto',
	InboxDeleteConversationSuccess: 'Conversación eliminada exitosamente',
	InboxDeleteMessage: '¿Borrar mensaje?',
	InboxDirectMessage: 'Mensaje directo',
	InboxEditDraft: 'Editar borrador',
	InboxEmailComposeReplyEmail: 'Redactar respuesta',
	InboxEmailDraft: 'Borrador',
	InboxEmailNotFound: 'Correo no encontrado',
	InboxEmailSubjectFieldInformation: 'Cambiar el asunto creará un nuevo correo electrónico enhebrado.',
	InboxEmptyArchiveDescription: 'No se ha encontrado ninguna conversación archivada',
	InboxEmptyBinDescription: 'No se ha encontrado ninguna conversación eliminada',
	InboxEmptyBinHeading: 'Todo despejado, no hay nada que ver aquí',
	InboxEmptyBinSuccess: 'Conversaciones eliminadas con éxito',
	InboxEmptyCongratsHeading: 'Buen trabajo! Siéntate y relájate hasta la próxima conversación',
	InboxEmptyDraftDescription: 'No se ha encontrado ningún borrador de conversación',
	InboxEmptyDraftHeading: 'Todo despejado, no hay nada que ver aquí',
	InboxEmptyOtherDescription: 'No se ha encontrado ninguna conversación',
	InboxEmptyScheduledHeading: 'Todo despejado, no hay conversaciones programadas para enviar',
	InboxEmptySentDescription: 'No se ha encontrado ninguna conversación enviada',
	InboxForward: 'Reenviar',
	InboxGroupClientsLabel: 'Todos los clientes',
	InboxGroupClientsOverviewLabel: 'Clientes',
	InboxGroupClientsSelectedItemPrefix: 'Cliente',
	InboxGroupStaffsLabel: 'Todo el equipo',
	InboxGroupStaffsOverviewLabel: 'Equipo',
	InboxGroupStaffsSelectedItemPrefix: 'Equipo',
	InboxGroupStatusLabel: 'Todos los estados',
	InboxGroupStatusOverviewLabel: 'Enviar a un estado',
	InboxGroupStatusSelectedItemPrefix: 'Estado',
	InboxGroupTagsLabel: 'Todas las etiquetas',
	InboxGroupTagsOverviewLabel: 'Enviar a una etiqueta',
	InboxGroupTagsSelectedItemPrefix: 'Etiqueta',
	InboxHideQuotedText: 'Ocultar texto citado',
	InboxIgnoreConversationSuccess: 'Conversación ignorada con éxito',
	InboxMessageAllLabelRecipientsCount: 'Todos los destinatarios de {label} ({count})',
	InboxMessageBodyPlaceholder: 'Añade tu mensaje',
	InboxMessageDeleted: 'Mensaje eliminado',
	InboxMessageMarkedAsRead: 'Mensaje marcado como leído',
	InboxMessageMarkedAsUnread: 'Mensaje marcado como no leído',
	InboxMessageSentViaChat: '<strong>Enviado por chat</strong>  • {time} por {name}',
	InboxMessageShowMoreRecipients: '+{count} más',
	InboxMessageWasDeleted: 'Este mensaje fue eliminado',
	InboxNoConnectionDescription:
		'Conecte su cuenta de correo electrónico o cree bandejas de entrada con múltiples correos electrónicos',
	InboxNoConnectionHeading: 'Integre las comunicaciones de sus clientes',
	InboxNoDirectMessage: 'No hay mensajes recientes',
	InboxRecentConversations: 'Recientes',
	InboxReopenConversationSuccess: 'Conversación reabierta con éxito',
	InboxReply: 'Responder',
	InboxReplyAll: 'Responder a todos',
	InboxRestoreConversationSuccess: 'Conversación restaurada correctamente',
	InboxScheduleSendCancelSendSuccess: 'El envío programado se canceló y el mensaje volvió a ser borrador',
	InboxScheduleSendMessageSuccessDescription: 'Enviar programado para {date}',
	InboxScheduleSendMessageSuccessTitle: 'Programación de envío',
	InboxSearchForConversations: 'Buscar "{query}"',
	InboxSendMessageSuccess: 'Conversación enviada con éxito',
	InboxSettings: 'Configuración de la bandeja de entrada',
	InboxSettingsAppsDesc:
		'Gestiona las aplicaciones conectadas a este buzón compartido: añade o elimina conexiones según sea necesario.',
	InboxSettingsAppsNewConnectedApp: 'Nueva aplicación conectada',
	InboxSettingsAppsTitle: 'Aplicaciones conectadas',
	InboxSettingsDeleteAccountFailed: 'No se pudo eliminar la cuenta de la bandeja de entrada',
	InboxSettingsDeleteAccountSuccess: 'Cuenta de bandeja de entrada eliminada con éxito',
	InboxSettingsDeleteAccountWarning:
		'Eliminar {email} desconectará el buzón {inboxName} y dejará de sincronizar los mensajes.',
	InboxSettingsDeleteInboxFailed: 'No se pudo eliminar la bandeja de entrada',
	InboxSettingsDeleteInboxSuccess: 'Bandeja de entrada eliminada con éxito',
	InboxSettingsDeleteInboxWarning:
		'Eliminar {inboxName} desconectará todos los canales conectados y eliminará todos los mensajes asociados con esta bandeja de entrada. Esta acción es permanente y no se puede deshacer.',
	InboxSettingsDetailsDesc:
		'Buzón de comunicación para que tu equipo gestione los mensajes de los clientes de manera eficiente.',
	InboxSettingsDetailsTitle: 'Detalles del buzón',
	InboxSettingsEmailSignatureLabel: 'Firma de correo electrónico predeterminada',
	InboxSettingsReplyFormatDesc:
		'Configura tu dirección de respuesta predeterminada y la firma de correo electrónico para que se muestren de manera consistente, independientemente de quién envíe el correo.',
	InboxSettingsReplyFormatTitle: 'Formato de respuesta',
	InboxSettingsSendFromLabel: 'Establecer un remitente predeterminado',
	InboxSettingsStaffDesc:
		'Gestiona el acceso de los miembros del equipo a este buzón compartido para una colaboración sin problemas.',
	InboxSettingsStaffTitle: 'Asignar miembros del equipo',
	InboxSettingsUpdateInboxDetailsFailed: 'No se pudo actualizar los detalles de la bandeja de entrada',
	InboxSettingsUpdateInboxDetailsSuccess: 'Detalles de la bandeja de entrada actualizados correctamente',
	InboxSettingsUpdateInboxStaffsFailed: 'No se ha podido actualizar la bandeja de entrada de los miembros del equipo',
	InboxSettingsUpdateInboxStaffsSuccess: 'Actualizar con éxito la bandeja de entrada de los miembros del equipo',
	InboxSettingsUpdateReplyFormatFailed: 'Error al actualizar el formato de respuesta',
	InboxSettingsUpdateReplyFormatSuccess: 'Formato de respuesta actualizado con éxito',
	InboxShowQuotedText: 'Mostrar texto citado',
	InboxStaffRoleAdminDescription: 'Ver, responder y gestionar las bandejas de entrada',
	InboxStaffRoleResponderDescription: 'Ver y responder',
	InboxStaffRoleViewerDescription: 'Ver sólo',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Seguir editando',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Sí, cambiar a envío masivo',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Has elegido más de {count} destinatarios. ¿Quieres enviarlo como correo electrónico masivo?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Advertencia',
	InboxSwitchToOtherInbox: 'Cambiar a otra bandeja de entrada',
	InboxUndoSendMessageSuccess: 'Envío deshecho',
	IncludeLineItems: 'Incluir artículos de línea',
	IncludeSalesTax: 'Gravable',
	IncludesAiSmartPrompt: 'Incluye indicaciones inteligentes de IA',
	Incomplete: 'Incompleto',
	IncreaseIndent: 'Aumentar la sangría',
	IndianHealthServiceFreeStandingFacility: 'Instalación independiente del Servicio de Salud Indígena',
	IndianHealthServiceProviderFacility: 'Instalación basada en un proveedor de servicios de salud para indios',
	Information: 'Información',
	InitialAssessment: 'Evaluación inicial',
	InitialSignupPageClientFamilyTitle: 'Cliente o miembro de la familia',
	InitialSignupPageProviderTitle: 'Proveedor de cuidado de la salud y social',
	InitialTreatment: 'Tratamiento inicial',
	Initials: 'Iniciales',
	InlineEmbed: 'Incrustación en línea',
	InputPhraseToConfirm: 'Para confirmar, escriba {confirmationPhrase}.',
	Insert: 'Insertar',
	InsertTable: 'Insertar tabla',
	InstallCarepatronOnYourIphone1: 'Instala Carepatron en tu iOS: toca',
	InstallCarepatronOnYourIphone2: 'y luego Agregar a la pantalla de inicio',
	InsufficientCalendarScopesSnackbar:
		'La sincronización falló - por favor permite todos los permisos para Carepatron.',
	InsufficientInboxScopesSnackbar: 'La sincronización falló - por favor permite todos los permisos para Carepatron.',
	InsufficientScopeErrorCodeSnackbar:
		'La sincronización falló - por favor permite todos los permisos para Carepatron.',
	Insurance: 'Seguros',
	InsuranceAmount: 'Monto del seguro',
	InsuranceClaim: 'Reclamo de seguro',
	InsuranceClaimAiChatPlaceholder: 'Pregunta sobre el reclamo de seguro...',
	InsuranceClaimAiClaimNumber: 'Reclamación {number}',
	InsuranceClaimAiSubtitle: 'Facturación de seguros • Validación de reclamos',
	InsuranceClaimDeniedSubject: 'Reclamación {claimNumber} enviada a {payerNumber} {payerName} fue denegada',
	InsuranceClaimErrorDescription:
		'La reclamación contiene errores reportados por el pagador o la cámara de compensación. Por favor revise los siguientes mensajes de error y vuelva a enviar la reclamación.',
	InsuranceClaimErrorGuideLink: 'Guía de reclamaciones de seguros',
	InsuranceClaimErrorTitle: 'Errores en el envío de reclamos',
	InsuranceClaimNotFound: 'Reclamo de seguro no encontrado',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Se registró un pago parcial de {paymentAmount}} other {Se registró un pago de {paymentAmount}}} para la reclamación {claimNumber} por {payerNumber} {payerName}',
	InsuranceClaimRejectedSubject: 'Reclamo {claimNumber} enviado a {payerNumber} {payerName} fue rechazado',
	InsuranceClaims: 'Reclamaciones de seguro',
	InsuranceInformation: 'Información de seguros',
	InsurancePaid: 'Seguro pagado',
	InsurancePayer: 'Pagador de seguros',
	InsurancePayers: 'Pagadores de seguros',
	InsurancePayersDescription: 'Ver los pagadores que se han agregado a su cuenta y administrar la inscripción.',
	InsurancePayment: 'Pago de seguro',
	InsurancePoliciesDetailsSubtitle: 'Agregue información del seguro del cliente para respaldar los reclamos.',
	InsurancePoliciesDetailsTitle: 'Detalles de las políticas',
	InsurancePoliciesListSubtitle: 'Agregue información del seguro del cliente para respaldar los reclamos.',
	InsurancePoliciesListTitle: 'Pólizas de seguro',
	InsuranceSelfPay: 'Pago propio',
	InsuranceType: 'Tipo de seguro',
	InsuranceUnpaid: 'Seguro no pagado',
	Intake: 'Admisión',
	IntakeExpiredErrorCodeSnackbar:
		'Este registro de admisión ha expirado. Por favor, contacta a tu proveedor para enviar otro registro.',
	IntakeNotFoundErrorSnackbar:
		'No se pudo encontrar este registro de admisión. Por favor, contacta a tu proveedor para enviar otro registro.',
	IntakeProcessLearnMoreInstructions: 'Guía para configurar sus formularios de admisión',
	IntakeTemplateSelectorPlaceholder: 'Elija formularios y acuerdos para enviar a su cliente para que los complete',
	Integration: 'Integración',
	IntenseBlur: 'Desenfoca intensamente tu fondo',
	InteriorDesigner: 'Diseñador de interiores',
	InternetBanking: 'Transferencia bancaria',
	Interval: 'Intervalo',
	IntervalDays: 'Intervalo (Días)',
	IntervalHours: 'Intervalo (Horas)',
	Invalid: 'Inválido',
	InvalidDate: 'Fecha inválida',
	InvalidDateFormat: 'La fecha debe estar en formato {format}',
	InvalidDisplayName: 'El nombre para mostrar no puede contener {value}',
	InvalidEmailFormat: 'Formato de correo electrónico inválido',
	InvalidFileType: 'Tipo de archivo no válido',
	InvalidGTMContainerId: 'Formato de ID de contenedor GTM no válido',
	InvalidPaymentMethodCode: 'El método de pago seleccionado no es válido. Por favor, elija otro.',
	InvalidPromotionCode: 'El código de promoción es invalido',
	InvalidReferralDescription: 'Ya estoy usando Carepatron',
	InvalidStatementDescriptor: `El descriptor de la declaración debe tener entre 5 y 22 caracteres de longitud y contener solo letras, números, espacios, y no debe incluir <, >, \\, ', ", *`,
	InvalidToken: 'Token inválido',
	InvalidTotpSetupVerificationCode: 'Código de verificación inválido.',
	InvalidURLErrorText: 'Esto debe ser una URL válida',
	InvalidZoomTokenErrorCodeSnackbar:
		'El token de Zoom ha expirado. Por favor, reconecta tu aplicación de Zoom e intenta de nuevo.',
	Invite: 'Invitar',
	InviteRelationships: 'Invitar relaciones',
	InviteToPortal: 'Invitar al portal',
	InviteToPortalModalDescription:
		'Se enviará un correo electrónico de invitación a su cliente para que se registre en Carepatron.',
	InviteToPortalModalTitle: 'Invitar a {name} al Portal de Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Invitar nuevo usuario',
	Invited: 'Invitado',
	Invoice: 'Factura',
	InvoiceColorPickerDescription: 'Tema de color que se utilizará en la factura',
	InvoiceColorTheme: 'Tema de color de factura',
	InvoiceContactDeleted: 'El contacto de la factura se ha eliminado y esta factura no se puede actualizar.',
	InvoiceDate: 'Fecha de emisión',
	InvoiceDetails: 'Detalles de la factura',
	InvoiceFieldsPlaceholder: 'Buscar campos...',
	InvoiceFrom: 'Factura {number} de {fromProvider}',
	InvoiceInvalidCredit: 'Monto de crédito no válido, el monto de crédito no puede exceder el total de la factura',
	InvoiceNotFoundDescription:
		'Por favor, póngase en contacto con su proveedor y pídale más información o que reenvíe la factura.',
	InvoiceNotFoundTitle: 'Factura no encontrada',
	InvoiceNumber: 'Número de factura',
	InvoiceNumberFormat: 'Factura #{number}',
	InvoiceNumberMustEndWithDigit: 'El número de factura debe terminar con un dígito (0-9)',
	InvoicePageHeader: 'Facturas',
	InvoicePaidNotificationSubject: 'Factura {invoiceNumber} pagada',
	InvoiceReminder: 'Recordatorios de factura',
	InvoiceReminderSentence:
		'Enviar recordatorio de {deliveryType} {interval} {unit} {beforeAfter} fecha de vencimiento de la factura',
	InvoiceReminderSettings: 'Configuración de recordatorios de facturas',
	InvoiceReminderSettingsInfo: 'Los recordatorios solo se aplican a las facturas enviadas en Carepatron',
	InvoiceReminders: 'Recordatorios de facturas',
	InvoiceRemindersInfo:
		'Establezca recordatorios automáticos para los pagos de facturas. Los recordatorios solo se aplican a las facturas enviadas en Carepatron',
	InvoiceSettings: 'Configuración de facturación',
	InvoiceStatus: 'Estado de la factura',
	InvoiceTemplateAddressPlaceholder: '123 Calle Principal, Ciudad, España',
	InvoiceTemplateDescriptionPlaceholder:
		'Agregue notas, detalles de transferencias bancarias o términos y condiciones para pagos alternativos',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Autónomo',
	InvoiceTemplateEthnicityPlaceholder: 'Caucásico',
	InvoiceTemplateNotFoundDescription: 'Por favor, póngase en contacto con su proveedor y pídale más información.',
	InvoiceTemplateNotFoundTitle: 'Plantilla de factura no encontrada',
	InvoiceTemplates: 'Plantillas de facturas',
	InvoiceTemplatesDescription:
		'Adapte sus plantillas de facturas para reflejar su marca, cumplir con los requisitos reglamentarios y satisfacer las preferencias del cliente con nuestras plantillas fáciles de usar.',
	InvoiceTheme: 'Tema de factura',
	InvoiceTotal: 'Total de la factura',
	InvoiceUninvoicedAmounts: 'Facturar importes no facturados',
	InvoiceUpdateVersionMessage:
		'La edición de esta factura requiere la última versión. Por favor, recarga Carepatron e inténtalo de nuevo.',
	Invoices: '{count, plural, one {Factura} other {Facturas}}',
	InvoicesEmptyStateDescription: 'No se han encontrado facturas',
	InvoicingAndPayment: 'Facturación y pagos',
	Ireland: 'Irlanda',
	IsA: 'es un(a)',
	IsBetween: 'está entre',
	IsEqualTo: 'es igual a',
	IsGreaterThan: 'es mayor que',
	IsGreaterThanOrEqualTo: 'es mayor o igual a',
	IsLessThan: 'es menor que',
	IsLessThanOrEqualTo: 'es menor o igual a',
	IssueCredit: 'Emitir crédito',
	IssueCreditAdjustment: 'Ajuste del crédito de emisión',
	IssueDate: 'Fecha de emisión',
	Italic: 'Cursiva',
	Items: 'Elementos',
	ItemsAndAdjustments: 'Artículos y ajustes',
	ItemsRemaining: 'Faltan {count} elementos',
	JobTitle: 'Título del trabajo',
	Join: 'Unirse',
	JoinCall: 'Unirse a la llamada',
	JoinNow: 'Unirse ahora',
	JoinProduct: 'Unirse {product}',
	JoinVideoCall: 'Unirse a la llamada de video',
	JoinWebinar: 'Únete al webinar',
	JoinWithVideoCall: 'Unirse con {product}',
	Journalist: 'Periodista',
	JustMe: 'Solo yo',
	JustYou: 'Solo tú',
	Justify: 'Justificar',
	KeepSeparate: 'Mantener separado',
	KeepSeparateSuccessMessage: 'Has mantenido registros separados para {clientNames} correctamente.',
	KeepWaiting: 'Seguir esperando',
	Label: 'Etiqueta',
	LabelOptional: 'Etiqueta (opcional)',
	LactationConsulting: 'Asesoramiento en lactancia',
	Language: 'Idioma',
	Large: 'Grande',
	LastDxCode: 'Último código DX',
	LastLoggedIn: 'Último inicio de sesión {date} a las {time}',
	LastMenstrualPeriod: 'Último periodo menstrual',
	LastMonth: 'El mes pasado',
	LastNDays: 'Últimos {number} días',
	LastName: 'Apellido',
	LastNameFirstInitial: 'Apellido, inicial del nombre',
	LastWeek: 'La semana pasada',
	LastXRay: 'Última radiografía',
	LatestVisitOrConsultation: 'Última visita o consulta',
	Lawyer: 'Abogado',
	LearnMore: 'Aprende más',
	LearnMoreTipsToGettingStarted: 'Aprende más consejos para empezar',
	LearnToSetupInbox: 'Aprende a configurar tu bandeja de entrada',
	Leave: 'Dejar',
	LeaveCall: 'Salir de la llamada',
	LeftAlign: 'Alinear a la izquierda',
	LegacyBillingItemsNotAvailable:
		'Los artículos de facturación individuales aún no están disponibles para esta cita. Puedes seguir facturándola normalmente.',
	LegacyBillingItemsNotAvailableTitle: 'Facturación heredada',
	LegalAndConsent: 'Legal y consentimiento',
	LegalConsentFormPrimaryText: 'Consentimiento legal',
	LegalConsentFormSecondaryText: 'Acepte o decline opciones',
	LegalGuardian: 'Tutor legal',
	Letter: 'Carta',
	LettersCategoryDescription: 'Para crear correspondencia clínica y administrativa',
	Librarian: 'Bibliotecario',
	LicenseNumber: 'Número de licencia',
	LifeCoach: 'Entrenador de vida',
	LifeCoaches: 'Entrenadores de vida',
	Limited: 'Limitado',
	LineSpacing: 'Espaciado de línea y párrafo',
	LinearScaleFormPrimaryText: 'Escala lineal',
	LinearScaleFormSecondaryText: 'Opciones de escala de 1 a 10',
	Lineitems: 'Elementos de línea',
	Link: 'Enlace',
	LinkClientFormSearchClientLabel: 'Busqueda de un cliente',
	LinkClientModalTitle: 'Enlace a cliente existente',
	LinkClientSuccessDescription:
		'<strong>{newName}’s</strong> información de contacto se agrega al registro de <strong>{existingName}’s</strong>.',
	LinkClientSuccessTitle: 'Vinculación exitosa a contacto existente',
	LinkForCallCopied: '¡Enlace copiado!',
	LinkToAnExistingClient: 'Enlace a un cliente existente',
	LinkToClient: 'Enlace al cliente',
	ListAndTracker: 'Lista/Seguimiento',
	ListPeopleInThisMeeting: `
	{n, plural, 
		one {{attendees} está en esta llamada}
		other {{attendees} están en esta llamada}
	}
	`,
	ListStyles: 'Estilos de lista',
	ListsAndTrackersCategoryDescription: 'Para organizar y hacer seguimiento del trabajo',
	LivingArrangements: 'Arreglos de vida',
	LoadMore: 'Cargar más',
	Loading: 'Cargando...',
	LocalizationPanelDescription: 'Administrar la configuración de su idioma y zona horaria',
	LocalizationPanelTitle: 'Idioma y zona horaria',
	Location: 'Ubicación',
	LocationDescription:
		'Configure ubicaciones físicas y virtuales con direcciones específicas, nombres de salas y tipos de espacios virtuales para facilitar la programación de citas y videollamadas.',
	LocationNumber: 'Número de ubicación',
	LocationOfService: 'Ubicación del servicio',
	LocationOfServiceRecommendedActionInfo: 'Agregue una ubicación de servicio para programar citas en línea',
	LocationRemote: 'Remoto',
	LocationType: 'Tipo de ubicación',
	Locations: 'Ubicaciones',
	Lock: 'Bloquear',
	Locked: 'Bloqueado',
	LockedNote: 'Nota bloqueada',
	LogInToSaveOrAuthoriseCard: 'Iniciar sesión para guardar o autorizar la tarjeta',
	LogInToSaveOrAuthorisePayment: 'Iniciar sesión para guardar o autorizar el pago',
	Login: 'Iniciar sesión',
	LoginButton: 'Iniciar sesión',
	LoginEmail: 'Correo electrónico',
	LoginForgotPasswordLink: '¿Olvidaste tu contraseña?',
	LoginPassword: 'Contraseña',
	Logo: 'Logotipo',
	LogoutAreYouSure: 'Cerrar sesión en este dispositivo.',
	LogoutButton: 'Cerrar sesión',
	London: 'Londres',
	LongTextAnswer: 'Respuesta de texto largo',
	LongTextFormPrimaryText: 'Texto largo',
	LongTextFormSecondaryText: 'Opciones de estilo de párrafo',
	Male: 'Masculino',
	Manage: 'Gestionar',
	ManageAllClientTags: 'Administrar todas las etiquetas de cliente',
	ManageAllNoteTags: 'Administrar todas las etiquetas de nota',
	ManageAllTemplateTags: 'Administrar todas las etiquetas de plantilla',
	ManageConnections: 'Administrar conexiones',
	ManageConnectionsGmailDescription: 'Otros miembros del equipo no podrán ver tu Gmail sincronizado.',
	ManageConnectionsGoogleCalendarDescription:
		'Otros miembros del equipo no podrán ver sus calendarios sincronizados. Las citas de los clientes solo se pueden actualizar o eliminar desde Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Vaya a la página Bandeja de entrada para administrar la configuración de Sincronizar bandeja de entrada.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Los demás miembros del equipo no podrán ver los calendarios sincronizados. Las citas de los clientes solo se pueden actualizar o eliminar desde Carepatron.',
	ManageConnectionsOutlookDescription:
		'Los demás miembros del equipo no podrán ver su Microsoft Outlook sincronizado.',
	ManageInboxAccountButton: 'Nuevo buzón',
	ManageInboxAccountEdit: 'Nuevo Buzón',
	ManageInboxAccountPanelTitle: 'Buzones',
	ManageInboxAssignTeamPlaceholder: 'Elegir a los miembros del equipo para acceder a la bandeja de entrada',
	ManageInboxBasicInfoColor: 'Elegir color',
	ManageInboxBasicInfoDescription: 'Descripción',
	ManageInboxBasicInfoDescriptionPlaceholder: '¿Para qué usarán usted o su equipo esta bandeja de entrada?',
	ManageInboxBasicInfoName: 'Nombre',
	ManageInboxBasicInfoNamePlaceholder: 'Por ejemplo, atención al cliente, administración',
	ManageInboxConnectAppAlreadyConnectedError: 'El canal que intentaste conectar ya está conectado a Carepatron',
	ManageInboxConnectAppConnect: 'Conectar',
	ManageInboxConnectAppConnectedInfo: 'Conectado a una cuenta',
	ManageInboxConnectAppContinue: 'Continuar',
	ManageInboxConnectAppEmail: 'Correo electrónico',
	ManageInboxConnectAppSignInWith: 'Iniciar sesión con',
	ManageInboxConnectAppSubtitle:
		'Conecte sus aplicaciones para enviar, recibir y realizar el seguimiento de todas sus comunicaciones en un único lugar centralizado.',
	ManageInboxNewInboxTitle: 'Nuevo Buzón',
	ManagePlan: 'Administrar plan',
	ManageProfile: 'Administrar perfil',
	ManageReferralsModalDescription:
		'Ayúdenos a correr la voz sobre nuestra plataforma de atención médica y gane recompensas.',
	ManageReferralsModalTitle: '¡Recomiende a un amigo y gane recompensas!',
	ManageStaffRelationshipsAddButton: 'Administrar relaciones',
	ManageStaffRelationshipsEmptyStateText: 'No se han agregado relaciones',
	ManageStaffRelationshipsModalDescription:
		'Al seleccionar clientes se agregarán nuevas relaciones, mientras que al desmarcarlos se eliminarán las relaciones existentes.',
	ManageStaffRelationshipsModalTitle: 'Administrar relaciones',
	ManageStatuses: 'Administrar estados',
	ManageStatusesActiveStatusHelperText: 'Se requiere al menos un estado activo',
	ManageStatusesDescription:
		'Personalice sus etiquetas de estado y elija colores para alinearlos con su flujo de trabajo.',
	ManageStatusesSuccessSnackbar: 'Estados actualizados correctamente',
	ManageTags: 'Administrar etiquetas',
	ManageTaskAttendeeStatus: 'Gestionar estados de citas',
	ManageTaskAttendeeStatusDescription:
		'Personaliza los estados de tus citas para que se alineen con tu flujo de trabajo.',
	ManageTaskAttendeeStatusHelperText: 'Se requiere al menos un estado',
	ManageTaskAttendeeStatusSubtitle: 'Estados personalizados',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manual',
	ManualAppointment: 'Cita manual',
	ManualPayment: 'Pago manual',
	ManuallyTypeLocation: 'Escribir ubicación manualmente',
	MapColumns: 'Columnas del mapa',
	MappingRequired: 'Mapeo requerido',
	MarkAllAsRead: 'Marcar todo como leido',
	MarkAsCompleted: 'Marcar como completado',
	MarkAsManualSubmission: 'Marcar como enviado',
	MarkAsPaid: 'Marcar como pagado',
	MarkAsRead: 'Marcar como leído',
	MarkAsUnpaid: 'Marcar como no pagado',
	MarkAsUnread: 'Marcar como no leído',
	MarkAsVoid: 'Marcar como anulado',
	Marker: 'Rotulador',
	MarketingManager: 'Director de marketing',
	MassageTherapist: 'Masajista',
	MassageTherapists: 'Masajistas terapéuticos',
	MassageTherapy: 'Masoterapia',
	MaxBookingTimeDescription1: 'Los clientes pueden programar hasta',
	MaxBookingTimeDescription2: 'en el futuro',
	MaxBookingTimeLabel: '{timePeriod} de antelación',
	MaxCapacity: 'Capacidad máxima',
	Maximize: 'Maximizar',
	MaximumAttendeeLimit: 'Límite máximo',
	MaximumBookingTime: 'Tiempo máximo de reserva',
	MaximumBookingTimeError: 'El tiempo máximo de reserva no debe exceder {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Puedes minimizar hasta {count} paneles laterales a la vez. Continuar cerrará el panel minimizado más antiguo. ¿Deseas continuar?',
	MaximumMinimizedPanelsReachedTitle: 'Tienes demasiados paneles abiertos.',
	MechanicalEngineer: 'Ingeniero mecánico',
	MediaGallery: 'Galería de medios',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Número de proveedor de Medicaid',
	MedicalAssistant: 'Asistente médico',
	MedicalCoder: 'Codificador médico',
	MedicalDoctor: 'Médico',
	MedicalIllustrator: 'Ilustrador médico',
	MedicalInterpreter: 'Intérprete médico',
	MedicalTechnologist: 'Tecnólogo médico',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Número de proveedor de Medicare',
	Medicine: 'Medicina',
	Medium: 'Medio',
	Meeting: 'Reunión',
	MeetingEnd: 'Terminar reunión',
	MeetingEnded: 'Reunión terminada',
	MeetingHost: 'Anfitrión de la reunión',
	MeetingLowerHand: 'Bajar la mano',
	MeetingOpenChat: 'Chat abierto',
	MeetingPersonRaisedHand: '{name} levantó la mano',
	MeetingRaiseHand: 'Levantar la mano',
	MeetingReady: 'Reunión lista',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {minuto} other {minutos}} {status}',
	Meetings: 'Reuniones',
	MemberId: 'Identificación de miembro',
	MentalHealth: 'Salud Mental',
	MentalHealthPractitioners: 'Profesionales de salud mental',
	MentalHealthProfessional: 'Profesional de la salud mental',
	Merge: 'Unir',
	MergeClientRecords: 'Fusionar registros de clientes',
	MergeClientRecordsDescription: 'Fusionar los registros de los clientes combinará todos sus datos, incluyendo:',
	MergeClientRecordsDescription2: '¿Desea continuar con la fusión? Esta acción no se puede deshacer',
	MergeClientRecordsItem1: 'Notas y documentos',
	MergeClientRecordsItem2: 'Citas',
	MergeClientRecordsItem3: 'Facturas',
	MergeClientRecordsItem4: 'Conversaciones',
	MergeClientsSuccess: 'Fusión exitosa de registro de cliente',
	MergeLimitExceeded: 'Solo puedes fusionar hasta 4 clientes a la vez.',
	Message: 'Mensaje',
	MessageAttachments: '{total} adjunto',
	Method: 'Método',
	MfaAvailabilityDisclaimer:
		'La autenticación multifactor solo está disponible para inicios de sesión con correo electrónico y contraseña. Para realizar cambios en la configuración de autenticación multifactor, inicie sesión con su correo electrónico y contraseña.',
	MfaDeviceLostPanelDescription:
		'Alternativamente, puede verificar su identidad recibiendo un código por correo electrónico.',
	MfaDeviceLostPanelTitle: '¿Perdió su dispositivo MFA?',
	MfaDidntReceiveEmailCode: '¿No has recibido un código? Contacta con el servicio de asistencia',
	MfaEmailOtpSendFailureSnackbar: 'No se pudo enviar el OTP del correo electrónico.',
	MfaEmailOtpSentSnackbar: 'Se ha enviado un código a {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'No se pudo verificar el OTP del correo electrónico.',
	MfaHasBeenSetUpText: 'Has configurado MFA',
	MfaPanelDescription:
		'Proteja su cuenta activando la autenticación multifactor (MFA) para obtener una capa adicional de protección. Verifique su identidad a través de un método secundario para evitar el acceso no autorizado.',
	MfaPanelNotAuthorizedError: 'Debes iniciar sesión con tu nombre de usuario ',
	MfaPanelRecommendationDescription:
		'Recientemente iniciaste sesión con un método alternativo para verificar tu identidad. Para mantener tu cuenta segura, considera configurar un nuevo dispositivo MFA.',
	MfaPanelRecommendationTitle: '<strong>Recomendado:</strong> Actualiza tu dispositivo de MFA',
	MfaPanelTitle: 'Autenticación multifactor (MFA)',
	MfaPanelVerifyEmailFirstAlert:
		'Necesitará verificar su correo electrónico antes de poder actualizar su configuración de MFA.',
	MfaRecommendationBannerDescription:
		'Recientemente iniciaste sesión con un método alternativo para verificar tu identidad. Para mantener tu cuenta segura, considera configurar un nuevo dispositivo MFA.',
	MfaRecommendationBannerPrimaryAction: 'Configurar MFA',
	MfaRecommendationBannerTitle: 'Recomendado',
	MfaRemovedSnackbarTitle: 'Se ha eliminado MFA.',
	MfaSendEmailCode: 'Enviar código',
	MfaVerifyIdentityLostDeviceButton: 'Perdí el acceso a mi dispositivo MFA',
	MfaVerifyYourIdentityPanelDescription:
		'Busque el código en su aplicación de autenticación e ingréselo a continuación.',
	MfaVerifyYourIdentityPanelTitle: 'Verificar su identidad',
	MicCamWarningMessage:
		'Desbloquee la cámara y el micrófono haciendo clic en los iconos bloqueados en la barra de direcciones del navegador.',
	MicCamWarningTitle: 'Cámara y micrófono bloqueados',
	MicOff: 'Micrófono está apagado',
	MicOn: 'Micrófono está encendido',
	MicSource: 'Fuente del micrófono',
	MicWarningMessage: 'Se ha detectado un problema con tu micrófono',
	Microphone: 'Micrófono',
	MicrophonePermissionBlocked: 'Acceso al micrófono bloqueado',
	MicrophonePermissionBlockedDescription: 'Actualiza los permisos de tu micrófono para comenzar a grabar.',
	MicrophonePermissionError:
		'Por favor, concede permiso para el micrófono en la configuración del navegador para continuar',
	MicrophonePermissionPrompt: 'Por favor, permite el acceso al micrófono para continuar',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Color del calendario de Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Microsoft Teams',
	MiddleEast: 'Medio Oriente',
	MiddleName: 'Segundo nombre',
	MiddleNames: 'Segundo nombre',
	Midwife: 'Partera',
	Midwives: 'Parteras',
	Milan: 'Milán',
	MinBookingTimeDescription1: 'Los clientes no pueden programar dentro de los',
	MinBookingTimeDescription2: 'del inicio de la cita',
	MinBookingTimeLabel: '{timePeriod} antes de la cita',
	MinCancellationTimeEditModeDescription: 'Establecer cuántas horas un cliente puede cancelar sin penalización',
	MinCancellationTimeUnset: 'No se ha establecido un tiempo mínimo de cancelación',
	MinCancellationTimeViewModeDescription: 'Período de cancelación sin penalización',
	MinMaxBookingTimeUnset: 'Sin tiempo establecido',
	Minimize: 'Minimizar',
	MinimizeConfirmationDescription:
		'Tienes un panel minimizado activo. Si continúas, se cerrará y podrías perder datos no guardados.',
	MinimizeConfirmationTitle: '¿Cerrar panel minimizado?',
	MinimumBookingTime: 'Tiempo mínimo de reserva',
	MinimumCancellationTime: 'Tiempo mínimo de cancelación',
	MinimumPaymentError: 'Un cargo mínimo de {minimumAmount} es requerido para los pagos en línea.',
	MinuteAbbreviated: 'mins',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minuto a minuto',
	MinutesPlural: '{age, plural, one {# minuto} other {# minutos}}',
	MiscellaneousInformation: 'Información miscelánea',
	MissingFeatures: 'Funciones faltantes',
	MissingPaymentMethod: 'Por favor, añade un método de pago a tu suscripción para agregar más miembros al equipo.',
	MobileNumber: 'Número de teléfono móvil',
	MobileNumberOptional: 'Número de teléfono móvil (opcional)',
	Modern: 'Moderno',
	Modifiers: 'Modificadores',
	ModifiersPlaceholder: 'Modificadores',
	Monday: 'Lunes',
	Month: 'Mes',
	Monthly: 'Mensual',
	MonthlyCost: 'Costo mensual',
	MonthlyOn: 'Mensual el {date}',
	MonthsPlural: '{age, plural, one {# mes} other {# meses}}',
	More: 'Más',
	MoreActions: 'Más acciones',
	MoreSettings: 'Más ajustes',
	MoreThanTen: '10+',
	MostCommonlyUsed: 'Más comúnmente utilizado',
	MostDownloaded: 'Más descargados',
	MostPopular: 'Más popular',
	Mother: 'Madre',
	MotherInLaw: 'Suegra',
	MoveDown: 'Mover hacia abajo',
	MoveInboxConfirmationDescription:
		'Reasignar esta conexión de la aplicación la eliminará de la bandeja de entrada <strong>{currentInboxName}</strong>.',
	MoveTemplateToFolder: 'Mover `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} se trasladó a {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Movido correctamente a la carpeta de admisión predeterminada',
	MoveTemplateToNewFolder: 'Crea una nueva carpeta para mover este elemento.',
	MoveToChosenFolder: 'Elige una carpeta para mover este elemento. Puedes crear una nueva carpeta si es necesario.',
	MoveToFolder: 'Mover a la carpeta',
	MoveToInbox: 'Mover a Bandeja',
	MoveToNewFolder: 'Mover a la carpeta nueva',
	MoveToSelectedFolder:
		'Una vez movido, el elemento se organizará dentro de la carpeta seleccionada y ya no aparecerá en su ubicación actual.',
	MoveUp: 'Ascender',
	MultiSpeciality: 'Multiespecialidad',
	MultipleChoiceFormPrimaryText: 'Opción múltiple',
	MultipleChoiceFormSecondaryText: 'Elija múltiples opciones',
	MultipleChoiceGridFormPrimaryText: 'Cuadrícula de opciones múltiples',
	MultipleChoiceGridFormSecondaryText: 'Elija opciones de una matriz',
	Mumbai: 'Mumbai',
	MusicTherapist: 'Musicoterapeuta',
	MustContainOneLetterError: 'ebe contener al menos una letra',
	MustEndWithANumber: 'Debe terminar con un número',
	MustHaveAtLeastXItems: 'Debe tener al menos {count, plural, one {# artículo} other {# artículos}}',
	MuteAudio: 'Silenciar audio',
	MuteEveryone: 'Silenciar a todos',
	MyAvailability: 'Mi disponibilidad',
	MyGallery: 'Mi galería',
	MyPortal: 'Mi portal',
	MyRelationships: 'Mis relaciones',
	MyTemplates: 'Plantillas de equipo',
	MyofunctionalTherapist: 'Terapeuta miofuncional',
	NCalifornia: 'California del Norte',
	NPI: 'NPI',
	NVirginia: 'Virginia del Norte',
	Name: 'Nombre',
	NameIsRequired: 'El nombre es obligatorio',
	NameMustNotBeAWebsite: 'El nombre no debe ser un sitio web',
	NameMustNotBeAnEmail: 'El nombre no debe ser un correo electrónico',
	NameMustNotContainAtSign: 'El nombre no debe contener el signo @',
	NameMustNotContainHTMLTags: 'El nombre no debe contener etiquetas HTML',
	NameMustNotContainSpecialCharacters: 'El nombre no debe contener caracteres especiales',
	NameOnCard: 'Nombre en la tarjeta',
	NationalProviderId: 'Identificador nacional del proveedor (NPI)',
	NaturopathicDoctor: 'Médico naturópata',
	NavigateToPersonalSettings: 'Perfil',
	NavigateToSubscriptionSettings: 'Configuración de la suscripción',
	NavigateToWorkspaceSettings: 'Ajustes del espacio de trabajo',
	NavigateToYourTeam: 'Gestionar equipo',
	NavigationDrawerBilling: 'Facturación',
	NavigationDrawerBillingInfo: 'Información de facturación, facturas y Stripe',
	NavigationDrawerCommunication: 'Comunicación',
	NavigationDrawerCommunicationInfo: 'Notificaciones y plantillas',
	NavigationDrawerInsurance: 'Seguro',
	NavigationDrawerInsuranceInfo: 'Aseguradoras y reclamaciones',
	NavigationDrawerInvoices: 'Facturación',
	NavigationDrawerPersonal: 'Mi Perfil',
	NavigationDrawerPersonalInfo: 'Detalles personales',
	NavigationDrawerProfile: 'Perfil',
	NavigationDrawerProviderSettings: 'Configuración',
	NavigationDrawerScheduling: 'Programación',
	NavigationDrawerSchedulingInfo: 'Detalles de servicios y reservas',
	NavigationDrawerSettings: 'Configuración',
	NavigationDrawerTemplates: 'Plantillas',
	NavigationDrawerTemplatesV2: 'Plantillas V2',
	NavigationDrawerTrash: 'Papelera',
	NavigationDrawerTrashInfo: 'Restaurar elementos eliminados',
	NavigationDrawerWorkspace: 'Configuración de Espacios',
	NavigationDrawerWorkspaceInfo: 'Información de suscripción y espacio de trabajo',
	NegativeBalanceNotSupported: 'No se admiten saldos de cuenta negativos',
	Nephew: 'Sobrino',
	NetworkQualityFair: 'Conexión regular',
	NetworkQualityGood: 'Buena conexión',
	NetworkQualityPoor: 'Conexión pobre',
	Neurologist: 'Neurólogo',
	Never: 'Nunca',
	New: 'Nuevo',
	NewAppointment: 'Nueva cita',
	NewClaim: 'Nueva reclamación',
	NewClient: 'Nuevo cliente',
	NewClientNextStepsModalAddAnotherClient: 'Agregar otro cliente',
	NewClientNextStepsModalBookAppointment: 'Reservar cita',
	NewClientNextStepsModalBookAppointmentDescription: 'Reserva una próxima cita o crea una tarea.',
	NewClientNextStepsModalCompleteBasicInformation: 'Registro completo del cliente',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Agregue información del cliente y capture los próximos pasos.',
	NewClientNextStepsModalCreateInvoice: 'Crear factura',
	NewClientNextStepsModalCreateInvoiceDescription: 'Agregue información de pago del cliente o cree una factura.',
	NewClientNextStepsModalCreateNote: 'Crear nota o cargar documento',
	NewClientNextStepsModalCreateNoteDescription: 'Capturar notas y documentación del cliente.',
	NewClientNextStepsModalDescription:
		'A continuación se muestran algunas acciones que puede realizar ahora que ha creado un registro de cliente.',
	NewClientNextStepsModalSendIntake: 'Enviar admisión',
	NewClientNextStepsModalSendIntakeDescription:
		'Recopilar información del cliente y enviar formularios adicionales para completar y firmar.',
	NewClientNextStepsModalSendMessage: 'Enviar mensaje',
	NewClientNextStepsModalSendMessageDescription: 'Redacte y envíe un mensaje a su cliente.',
	NewClientNextStepsModalTitle: 'Próximos pasos',
	NewClientSuccess: 'Nuevo cliente creado exitosamente',
	NewClients: 'Nuevos clientes',
	NewConnectedApp: 'Nueva aplicación conectada',
	NewContact: 'Nuevo contacto',
	NewContactNextStepsModalAddRelationship: 'Añadir relación',
	NewContactNextStepsModalAddRelationshipDescription: 'Vincula este contacto con clientes o grupos relacionados.',
	NewContactNextStepsModalBookAppointment: 'Reservar cita',
	NewContactNextStepsModalBookAppointmentDescription: 'Reserva una cita próxima o crea una tarea.',
	NewContactNextStepsModalCompleteProfile: 'Perfil completo',
	NewContactNextStepsModalCompleteProfileDescription: 'Añade información de contacto y captura los siguientes pasos.',
	NewContactNextStepsModalCreateNote: 'Crear nota o subir documento',
	NewContactNextStepsModalCreateNoteDescription: 'Capturar notas y documentación del cliente.',
	NewContactNextStepsModalDescription: 'Aquí hay algunas acciones que puede tomar ahora que ha creado un contacto.',
	NewContactNextStepsModalInviteToPortal: 'Invitación al portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Envía una invitación para acceder al portal.',
	NewContactNextStepsModalTitle: 'Próximos pasos',
	NewContactSuccess: 'Nuevo contacto creado con éxito',
	NewDateOverrideButton: 'Anulación de nueva fecha',
	NewDiagnosis: 'Añadir diagnóstico',
	NewField: 'Nuevo campo',
	NewFolder: 'Nueva carpeta',
	NewInvoice: 'Nueva factura',
	NewLocation: 'Nueva ubicación',
	NewLocationFailure: 'No se pudo crear la nueva ubicación',
	NewLocationSuccess: 'Se ha creado una nueva ubicación con éxito',
	NewManualPayer: 'Nuevo pagador manual',
	NewNote: 'Nueva nota',
	NewNoteCreated: 'Nueva nota creada con éxito',
	NewPassword: 'Nueva contraseña',
	NewPayer: 'Nuevo pagador',
	NewPaymentMethod: 'Nuevo método de pago',
	NewPolicy: 'Nueva política',
	NewRelationship: 'Nueva relación',
	NewReminder: 'Nuevo recordatorio',
	NewSchedule: 'Nuevo horario',
	NewSection: 'Nueva sección',
	NewSectionOld: 'Nueva sección [antiguo]',
	NewSectionWithGrid: 'Nueva sección con rejilla',
	NewService: 'Nuevo servicio',
	NewServiceFailure: 'No se pudo crear el nuevo servicio',
	NewServiceSuccess: 'Se ha creado el nuevo servicio con éxito',
	NewStatus: 'Nuevo estado',
	NewTask: 'Nueva tarea',
	NewTaxRate: 'Nueva tasa de impuesto',
	NewTeamMemberNextStepsModalAssignClients: 'Asignar clientes',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Asigna clientes específicos a tu miembro del equipo.',
	NewTeamMemberNextStepsModalAssignServices: 'Asignar servicios',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Administrar los servicios asignados y ajustar los precios según sea necesario.',
	NewTeamMemberNextStepsModalBookAppointment: 'Reservar cita',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Reserva una cita próxima o crea una tarea.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Perfil completo',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Agrega detalles sobre tu miembro del equipo para completar su perfil.',
	NewTeamMemberNextStepsModalDescription:
		'Aquí hay algunas acciones para tomar ahora que has creado un miembro del equipo.',
	NewTeamMemberNextStepsModalEditPermissions: 'Permisos de edición',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Ajusta sus niveles de acceso para garantizar que tengan los permisos adecuados.',
	NewTeamMemberNextStepsModalSetAvailability: 'Establecer disponibilidad',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Configure su disponibilidad para crear horarios.',
	NewTeamMemberNextStepsModalTitle: 'Próximos pasos',
	NewTemplateFolderDescription: 'Crea una nueva carpeta para organizar tu documentación.',
	NewUIUpdateBannerButton: 'Recargar aplicación',
	NewUIUpdateBannerTitle: '¡Hay una nueva actualización lista!',
	NewZealand: 'Nueva Zelanda',
	Newest: 'Más recientes',
	NewestUnreplied: 'Más recientes sin respuesta',
	Next: 'Siguiente',
	NextInvoiceIssueDate: 'Próxima fecha de emisión de factura',
	NextNDays: 'Próximos {number} días',
	Niece: 'Sobrina',
	No: 'No',
	NoAccessGiven: 'No se ha concedido acceso',
	NoActionConfigured: 'No hay ninguna acción configurada',
	NoActivePolicies: 'No hay políticas activas',
	NoActiveReferrals: 'No tienes referidos activos',
	NoAppointmentsFound: 'No se han encontrado citas',
	NoAppointmentsHeading: 'Gestiona las citas y actividades de los clientes',
	NoArchivedPolicies: 'No hay políticas archivadas',
	NoAvailableTimes: 'No se encontraron horarios disponibles.',
	NoBillingItemsFound: 'No se encontraron artículos de facturación',
	NoCalendarsSynced: 'No hay calendarios sincronizados',
	NoClaimsFound: 'No se encontraron reclamos',
	NoClaimsHeading: 'Agiliza la presentación de reclamos para reembolso',
	NoClientsHeading: 'Reúne los registros de tus clientes',
	NoCompletedReferrals: 'No tienes referencias completas',
	NoConnectionsHeading: 'Optimiza tus comunicaciones con los clientes',
	NoContactsGivenAccess: 'Ningún cliente o contacto ha sido autorizado para acceder a esta nota',
	NoContactsHeading: 'Mantente conectado con quienes apoyan tu práctica',
	NoCopayOrCoinsurance: 'Sin copago ni coaseguro',
	NoCustomServiceSchedule:
		'No se estableció un horario personalizado: la disponibilidad depende de la disponibilidad del miembro del equipo',
	NoDescription: 'Sin descripción',
	NoDocumentationHeading: 'Cree y almacene notas de forma segura',
	NoDuplicateRecordsHeading: 'Su registro de cliente está libre de duplicados',
	NoEffect: 'Sin efecto',
	NoEnrolmentProfilesFound: 'No se encontraron perfiles de inscripción',
	NoGlossaryItems: 'No hay elementos del glosario',
	NoInvitedReferrals: 'No tienes referidos invitados',
	NoInvoicesFound: 'No se encontraron facturas',
	NoInvoicesHeading: 'Automatice su facturación y pagos',
	NoLimit: 'Sin límite',
	NoLocationsFound: 'No se han encontrado ubicaciones',
	NoLocationsWillBeAdded: 'No se agregarán ubicaciones.',
	NoNoteFound: 'No se encontró ninguna nota',
	NoPaymentMethods: 'No tienes métodos de pago guardados, puedes agregar uno al realizar un pago.',
	NoPermissionError: 'No tienes permiso',
	NoPermissions: 'No tienes permiso para ver esta página',
	NoPolicy: 'No se ha agregado ninguna política de cancelación',
	NoRecordsHeading: 'Personaliza los registros de tus clientes',
	NoRecordsToDisplay: 'No hay {resource} para mostrar',
	NoRelationshipsHeading: 'Mantente conectado con quienes apoyan a tu cliente',
	NoRemindersFound: 'No se encontraron recordatorios',
	NoResultsFound: 'No se encontraron resultados',
	NoResultsFoundDescription: 'No podemos encontrar ningún elemento que coincida con tu búsqueda',
	NoServicesAdded: 'No se han agregado servicios',
	NoServicesApplied: 'No se aplicaron servicios',
	NoServicesWillBeAdded: 'No se añadirán servicios.',
	NoTemplate: 'No tienes plantillas guardadas',
	NoTemplatesHeading: 'Crea tus propias plantillas',
	NoTemplatesInFolder: 'No plantillas en esta carpeta',
	NoTitle: 'Sin título',
	NoTrashItemsHeading: 'No se encontró ningún elemento eliminado',
	NoTriggerConfigured: 'No hay ningún disparador configurado',
	NoUnclaimedItemsFound: 'No se encontraron artículos no reclamados.',
	NonAiTemplates: 'Plantillas que no son de IA',
	None: 'Ninguno',
	NotAvailable: 'No disponible',
	NotCovered: 'No cubierto',
	NotFoundSnackbar: 'Recurso no encontrado.',
	NotRequiredField: 'No requerido',
	Note: 'Nota',
	NoteDuplicateSuccess: 'Nota duplicada con éxito',
	NoteEditModeViewSwitcherDescription: 'Crear y editar nota',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} envió el formulario {noteTitle}',
	NoteLockSuccess: '{title} ha sido bloqueado',
	NoteModalAttachmentButton: 'Agregar archivos adjuntos',
	NoteModalPhotoButton: 'Agregar/capturar fotos',
	NoteModalTrascribeButton: 'Transcribir audio en vivo',
	NoteResponderModeViewSwitcherDescription: 'Enviar formularios y revisar respuestas',
	NoteResponderModeViewSwitcherTooltipTitle: 'Responda y envíe formularios en nombre de sus clientes',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Complete y envíe formularios como cliente',
	NoteUnlockSuccess: '{title} desbloqueada',
	NoteViewModeViewSwitcherDescription: 'Acceso de solo lectura',
	Notes: 'Notas',
	NotesAndForms: 'Notas y formularios',
	NotesCategoryDescription: 'Para documentar las interacciones con los clientes',
	NothingToSeeHere: 'Nada que ver aquí',
	Notification: 'Notificaciones',
	NotificationIgnoredMessage: 'Todas las notificaciones {notificationType} serán ignoradas',
	NotificationRestoredMessage: 'Todas las notificaciones {notificationType} restauradas',
	NotificationSettingBillingDescription:
		'Recibe notificaciones de actualizaciones y recordatorios de pago del cliente.',
	NotificationSettingBillingTitle: 'Facturación y pago',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} solo} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Recibe notificaciones de actualizaciones y recordatorios de pago del cliente.',
	NotificationSettingClientDocumentationTitle: 'Cliente y documentación',
	NotificationSettingCommunicationsDescription:
		'Recibe notificaciones en tu bandeja de entrada y actualizaciones de tus canales conectados',
	NotificationSettingCommunicationsTitle: 'Comunicaciones',
	NotificationSettingEmail: 'Correo electrónico',
	NotificationSettingInApp: 'En la aplicación',
	NotificationSettingPanelDescription:
		'Elige las notificaciones que deseas recibir para actividades y recomendaciones.',
	NotificationSettingPanelTitle: 'Preferencias de notificación',
	NotificationSettingSchedulingDescription:
		'Reciba notificaciones cuando un miembro del equipo o un cliente reserva, reprograma o cancela su cita.',
	NotificationSettingSchedulingTitle: 'Programación',
	NotificationSettingUpdateSuccess: 'Configuración de notificaciones actualizada correctamente',
	NotificationSettingWhereYouReceiveNotifications: 'Dónde quieres recibir estas notificaciones',
	NotificationSettingWorkspaceDescription:
		'Recibe notificaciones sobre cambios en el sistema, problemas, transferencias de datos y recordatorios de suscripción.',
	NotificationSettingWorkspaceTitle: 'Espacio de trabajo',
	NotificationTemplateUpdateFailed: 'No se pudo actualizar la plantilla de notificación',
	NotificationTemplateUpdateSuccess: 'Plantilla de notificación actualizada correctamente',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'¿Te gustaría enviar un correo electrónico de notificación de cancelación a los asistentes?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Enviar cancelación',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'¿Te gustaría enviar un correo electrónico de notificación de confirmación a los asistentes?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Enviar confirmación',
	NotifyAttendeesOfTaskDeletedModalTitle: '¿Desea enviar correos electrónicos de cancelación a los asistentes?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {no tiene} other {no tienen}} una dirección de correo electrónico, por lo que no recibirán notificaciones y recordatorios automatizados.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {no tiene} other {no tienen}} una dirección de correo electrónico, por lo que no recibirán notificaciones y recordatorios automatizados.',
	NotifyAttendeesOfTaskModalTitle: '¿Desea enviar un correo electrónico de notificación a los asistentes?',
	NotifyAttendeesOfTaskSnackbar: 'Enviando notificación',
	NuclearMedicineTechnologist: 'Tecnólogo en medicina nuclear',
	NumberOfClaims: '{number, plural, one {# Reclamo} other {# Reclamos}}',
	NumberOfClients: '{number, plural, one {# Clientes} other {# Clientes}}',
	NumberOfContacts: '{number, plural, one {# Contacto} other {# Contactos}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entrada} other {entradas}} encontradas',
	NumberOfErrors: '{count, plural, one {# error} other {# errores}}',
	NumberOfInvoices: '{number, plural, one {# Factura} other {# Facturas}}',
	NumberOfLineitemsToCredit:
		'Tienes <mark>{count} {count, plural, one {elemento de línea} other {elementos de línea}}</mark> para emitir un crédito.',
	NumberOfPayments: '{number, plural, one {# Pago} other {# Pagos}}',
	NumberOfRelationships: '{number, plural, one {# Relación} other {# Relaciones}}',
	NumberOfResources: '{number, plural, one {# Recurso} other {# Recursos}}',
	NumberOfTeamMembers: '{number, plural, one {# Miembro del equipo} other {# Miembros del equipo}}',
	NumberOfTrashItems: '{number, plural, one {# artículo} other {# artículos}}',
	NumberOfUninvoicedAmounts:
		'Usted tiene <mark>{count} {count, plural, one {monto} other {montos}} sin facturar</mark> que deben facturarse',
	NumberedList: 'Lista numerada',
	Nurse: 'Enfermero/Enfermera',
	NurseAnesthetist: 'Enfermero anestesista',
	NurseAssistant: 'Asistente de enfermería',
	NurseEducator: 'Enfermero educador',
	NurseMidwife: 'Enfermera matrona',
	NursePractitioner: 'Enfermero Practicante',
	Nurses: 'Enfermeras',
	Nursing: 'Enfermería',
	Nutritionist: 'Nutricionista',
	Nutritionists: 'Nutricionistas',
	ObstetricianOrGynecologist: 'Obstetra/ginecólogo',
	Occupation: 'Ocupación',
	OccupationalTherapist: 'Terapeuta ocupacional',
	OccupationalTherapists: 'Terapeutas ocupacionales',
	OccupationalTherapy: 'Terapia ocupacional',
	Occurrences: 'Ocurrencias',
	Of: 'de',
	Ohio: 'Ohio',
	OldPassword: 'Contraseña antigua',
	OlderMessages: '{count} mensajes antiguos',
	Oldest: 'Más antiguos',
	OldestUnreplied: 'Más antiguos sin respuesta',
	On: 'en',
	OnboardingBusinessAgreement: 'En nombre mío y del negocio, acepto el {businessAssociateAgreement}.',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Los terapeutas ocupacionales</mark> representan una cuarta parte de nuestros clientes en Carepatron',
	OnboardingLoadingProfession: 'Tenemos muchos <mark>{profession}</mark> utilizando y prosperando en Carepatron.',
	OnboardingLoadingPsychologist:
		'<mark>Los psicólogos</mark> representan más de la mitad de nuestros clientes en Carepatron',
	OnboardingLoadingSubtitleFive:
		'Nuestra misión es hacer que el <mark>software de atención médica sea accesible</mark> para todos.',
	OnboardingLoadingSubtitleFour:
		'<mark>Software de salud simplificado</mark> para más de 10,000 personas en todo el mundo.',
	OnboardingLoadingSubtitleThree:
		'Ahorre <mark>1 día por semana</mark> en tareas administrativas con la ayuda de Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Ahorre <mark>2 horas</mark> diarias en tareas administrativas con la ayuda de Carepatron.',
	OnboardingReviewLocationOne: 'Holland Park Mental Health Centre',
	OnboardingReviewLocationThree: 'Practice Nurse, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Life House Clinic',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E',
	OnboardingReviewNameTwo: 'Clara W',
	OnboardingReviewOne:
		'"Carepatron es súper intuitivo de usar. Nos ayuda a dirigir nuestra práctica tan bien que ya no necesitamos un equipo de administradores"',
	OnboardingReviewThree:
		'"Es la mejor solución de práctica que he utilizado tanto en términos de características como de costo. Tiene todo lo que necesito para hacer crecer mi negocio"',
	OnboardingReviewTwo:
		'"También me encanta la aplicación de Carepatron. Me ayuda a hacer un seguimiento de mis clientes y trabajo mientras estoy en movimiento"',
	OnboardingTitle: 'Vamos a <mark>conocerte mejor</mark>',
	Oncologist: 'Oncólogo',
	Online: 'En línea',
	OnlineBookingColorTheme: 'Tema de color de reservas en línea',
	OnlineBookings: 'Reservas en línea',
	OnlineBookingsHelper: 'Elige cuándo se pueden realizar reservas en línea y por qué tipo de clientes',
	OnlinePayment: 'Pago en línea',
	OnlinePaymentSettingCustomInfo:
		'Los ajustes de pago en línea para este servicio difieren de los ajustes globales de reserva.',
	OnlinePaymentSettings: 'Configuración de pagos en línea',
	OnlinePaymentSettingsInfo:
		'Reciba pagos por servicios al momento de la reserva en línea para asegurar y simplificar los pagos',
	OnlinePaymentSettingsPaymentsDisabled:
		'Los pagos están deshabilitados, por lo que no se pueden cobrar durante la reserva en línea. Por favor, revise la configuración de pago para habilitar los pagos.',
	OnlinePaymentSettingsStripeNote: '{action} para recibir pagos de reserva en línea y simplificar su proceso de pago',
	OnlinePaymentsNotSupportedForCurrency: 'Los pagos en línea no están disponibles con esta {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'Lo siento, la moneda seleccionada no es compatible',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Lo siento, los pagos en línea aún no son compatibles en tu país',
	OnlineScheduling: 'Programación en línea',
	OnlyVisibleToYou: 'Sólo visible para ti',
	OnlyYou: 'Solo tú',
	OnsetDate: 'Fecha de inicio',
	OnsetOfCurrentSymptomsOrIllness: 'Aparición de los síntomas o enfermedad actuales',
	Open: 'Abrir',
	OpenFile: 'Abrir archivo',
	OpenSettings: 'Abrir configuración',
	Ophthalmologist: 'Oftalmólogo',
	OptimiseTelehealthCalls: 'Optimice sus llamadas de Telehealth',
	OptimizeServiceTimes: 'Optimizar tiempos de servicio',
	Options: 'Opciones',
	Optometrist: 'Optometrista',
	Or: 'ou',
	OrAttachSingleFile: 'adjuntar un archivo',
	OrDragAndDrop: 'o arrastra y suelta',
	OrderBy: 'Ordenar por',
	Oregon: 'Oregón',
	OrganisationOrIndividual: 'Organización o individuo',
	OrganizationPlanInclusion1: 'Permisos avanzados',
	OrganizationPlanInclusion2: 'Soporte gratuito para la importación de datos de clientes',
	OrganizationPlanInclusion3: 'Gestor de éxito dedicado',
	OrganizationPlanInclusionHeader: 'Todo en Profesional, más...',
	Orthodontist: 'Ortodoncista',
	Orthotist: 'Ortopedista',
	Other: 'Otro',
	OtherAdjustments: 'Otros ajustes',
	OtherAdjustmentsTableEmptyState: 'No se encontraron ajustes',
	OtherEvents: 'Otros acontecimientos',
	OtherId: 'Otra identificación',
	OtherIdQualifier: 'Otro calificador de identificación',
	OtherPaymentMethod: 'Otro método de pago',
	OtherPlanMessage:
		'Mantente al control de las necesidades de tu práctica. Revisa tu plan actual, monitorea el uso y explora opciones de actualización para desbloquear más funciones a medida que tu equipo crece.',
	OtherPolicy: 'Otros seguros',
	OtherProducts: '¿Qué otros productos o herramientas usa?',
	OtherServices: 'Otros servicios',
	OtherTemplates: 'Otras plantillas',
	Others: 'Otros',
	OthersPeople: `
	{n, plural, 
		one {1 otra persona
		other {# otras personas}
	}
	`,
	OurResearchTeamReachOut:
		'¿Puede nuestro equipo de investigación contactarte para obtener más información sobre cómo Carepatron podría haber sido mejor para tus necesidades?',
	OutOfOffice: 'Fuera de la oficina',
	OutOfOfficeColor: 'Color fuera de la oficina',
	OutOfOfficeHelper: 'Algunos miembros del personal elegidos están fuera de la oficina',
	OutsideLabCharges: 'Cargos de laboratorio externos',
	OutsideOfWorkingHours: 'Fuera del horario laboral',
	OutsideWorkingHoursHelper: 'Algunos miembros del personal elegidos están fuera de las horas de trabajo',
	Overallocated: 'Sobreasignado',
	OverallocatedPaymentDescription: `Este pago se ha asignado en exceso a artículos facturables.
 Agregue una asignación a los artículos no pagados o emita un crédito o reembolso.`,
	OverallocatedPaymentTitle: 'Pago sobreasignado',
	OverdueTerm: 'Término vencido (días)',
	OverinvoicedAmount: 'Importe sobrefacturado',
	Overpaid: 'Sobrepagado',
	OverpaidAmount: 'Monto pagado en exceso',
	Overtime: 'con el tiempo',
	Owner: 'Propietario',
	POS: 'POS',
	POSCode: 'código POS',
	POSPlaceholder: 'Punto de venta',
	PageBlockerDescription: 'Los cambios no guardados se perderán. ¿Aún quieres salir?',
	PageBlockerTitle: '¿Descartar cambios?',
	PageFormat: 'Formato de página',
	PageNotFound: 'Página no encontrada',
	PageNotFoundDescription: 'Ya no tienes acceso a esta página o no se puede encontrar',
	PageUnauthorised: 'Acceso no autorizado',
	PageUnauthorisedDescription: 'No tienes permiso para acceder a esta página',
	Paid: 'Pagado',
	PaidAmount: 'Monto pagado',
	PaidAmountMinimumValueError: 'La cantidad pagada debe ser mayor que 0',
	PaidAmountRequiredError: 'Se requiere la cantidad pagada',
	PaidItems: 'Artículos pagados',
	PaidMultiple: 'Pagado',
	PaidOut: 'Pagado',
	ParagraphStyles: 'Estilos de párrafo',
	Parent: 'Padre',
	Paris: 'París',
	PartialRefundAmount: 'Parcialmente reembolsado ({amount} restante)',
	PartiallyFull: 'Parcialmente lleno',
	PartiallyPaid: 'Parcialmente pagado',
	PartiallyRefunded: 'Reembolso parcial',
	Partner: 'Pareja',
	Password: 'Contraseña',
	Past: 'Pasadas',
	PastDateOverridesEmpty: 'Sus anulaciones de fecha aparecerán aquí tan pronto como el evento haya pasado',
	Pathologist: 'Patólogo',
	Patient: 'Paciente',
	Pause: 'Pausa',
	Paused: 'En pausa',
	Pay: 'Pagar',
	PayMonthly: 'Paga mensualmente',
	PayNow: 'Pagar ahora',
	PayValue: 'Pagar {showPrice, select, true {{price}} other {ahora}}',
	PayWithOtherCard: 'Pagar con otra tarjeta',
	PayYearly: 'Pagar anualmente',
	PayYearlyPercentOff: 'Paga anualmente <mark>{percent}% de descuento</mark>',
	Payer: 'Pagador',
	PayerClaimId: 'ID de reclamación del pagador',
	PayerCoverage: 'Cobertura',
	PayerDetails: 'Detalles del pagador',
	PayerDetailsDescription:
		'Vea los detalles de los pagadores que se han agregado a su cuenta y administre la inscripción.',
	PayerID: 'ID del pagador',
	PayerId: 'ID del pagador',
	PayerName: 'Nombre del pagador',
	PayerPhoneNumber: 'Número de teléfono del pagador',
	Payers: 'Pagadores',
	Payment: 'Pago',
	PaymentAccountUpdated: '¡Tu cuenta ha sido actualizada!',
	PaymentAccountUpgraded: '¡Su cuenta ha sido actualizada!',
	PaymentAmount: 'Importe del pago',
	PaymentDate: 'Fecha de pago',
	PaymentDetails: 'Detalles de pago',
	PaymentForUsersPerMonth: 'Pago por {billedUsers, plural, one {# usuario} other {# usuarios}} al mes',
	PaymentInfoFormPrimaryText: 'Información de pago',
	PaymentInfoFormSecondaryText: 'Recopile detalles de pago',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Esta factura ya ha sido pagada.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Esta factura ya está siendo procesada.',
	PaymentIntentAmountMismatchSnackbar:
		'El monto total de la factura ha sido modificado. Por favor, revise los cambios antes de pagar.',
	PaymentIntentSyncTimeoutSnackbar:
		'Su pago se realizó correctamente, pero se agotó el tiempo de espera. Actualice la página y, si no se muestra su pago, póngase en contacto con el servicio de atención al cliente',
	PaymentMethod: 'Método de pago',
	PaymentMethodDescription:
		'Agregue y administre su método de pago de práctica para agilizar su proceso de facturación de suscripción.',
	PaymentMethodLabelBank: 'cuenta bancaria',
	PaymentMethodLabelCard: 'tarjeta',
	PaymentMethodLabelFallback: 'método de pago',
	PaymentMethodRequired: 'Agregue un método de pago antes de cambiar las suscripciones',
	PaymentMethods: 'Métodos de pago',
	PaymentProcessing: 'Procesando pago!',
	PaymentProcessingFee: 'El pago incluye {amount} de gastos de procesamiento',
	PaymentReports: 'Reportes de pago (ERA)',
	PaymentSettings: 'Configuración de pago',
	PaymentSuccessful: '¡Pago exitoso!',
	PaymentType: 'Tipo de pago',
	Payments: 'Pagos',
	PaymentsAccountDisabledNotificationSubject: `Los pagos en línea a través de {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} han sido desactivados.
Por favor, revisa tu configuración de pago para habilitar los pagos.`,
	PaymentsEmptyStateDescription: 'No se han encontrado pagos',
	PaymentsUnallocated: 'Pagos no asignados',
	PayoutDate: 'Fecha de pago',
	PayoutsDisabled: 'Pagos deshabilitados',
	PayoutsEnabled: 'Pagos habilitados',
	PayoutsStatus: 'Estado de pagos',
	Pediatrician: 'Pediatra',
	Pen: 'Pluma',
	Pending: 'Pendiente',
	People: '{rosterSize} personas',
	PeopleCount: 'Personas ({count})',
	PerMonth: '/ Mes',
	PerUser: 'Por usuario',
	Permission: 'Permiso',
	PermissionRequired: 'Se requiere permiso',
	Permissions: 'Permisos',
	PermissionsClientAndContactDocumentation: 'Documentación de cliente y contacto',
	PermissionsClientAndContactProfiles: 'Perfiles de cliente y contacto',
	PermissionsEditAccess: 'Acceso de edición',
	PermissionsInvoicesAndPayments: 'Facturas y pagos',
	PermissionsScheduling: 'Programación',
	PermissionsUnassignClients: 'Desasignar clientes',
	PermissionsUnassignClientsConfirmation: '¿Estás seguro de que quieres desasignar a estos clientes?',
	PermissionsValuesAssigned: 'Asignado solamente',
	PermissionsValuesEverything: 'Todo',
	PermissionsValuesNone: 'Ninguno',
	PermissionsValuesOwnCalendar: 'Calendario propio',
	PermissionsViewAccess: 'Acceso de vista',
	PermissionsWorkspaceSettings: 'Configuración del espacio de trabajo',
	Person: '{rosterSize} persona',
	PersonalDetails: 'Detalles personales',
	PersonalHealthcareHistoryStoreDescription:
		'Responda y almacene de forma segura su historial de salud personal en un solo lugar',
	PersonalTrainer: 'Entrenador personal',
	PersonalTraining: 'Entrenamiento personal',
	PersonalizeWorkspace: 'Personaliza tu espacio de trabajo',
	PersonalizingYourWorkspace: 'Personalizando tu espacio de trabajo',
	Pharmacist: 'Farmacéutico',
	Pharmacy: 'Farmacia',
	PhoneCall: 'Llamada telefónica',
	PhoneNumber: 'Número de teléfono',
	PhoneNumberOptional: 'Número de teléfono (opcional)',
	PhotoBy: 'Foto por',
	PhysicalAddress: 'Dirección física',
	PhysicalTherapist: 'Fisioterapeuta',
	PhysicalTherapists: 'Terapeutas físicos',
	PhysicalTherapy: 'Fisioterapia',
	Physician: 'Médico',
	PhysicianAssistant: 'Asistente médico',
	Physicians: 'Médicos',
	Physiotherapist: 'Fisioterapeuta',
	PlaceOfService: 'Lugar de servicio',
	Plan: 'Plan',
	PlanAndReport: 'Plan/Informe',
	PlanId: 'Identificación del plan',
	PlansAndReportsCategoryDescription: 'Para la planificación del tratamiento y el resumen de los resultados',
	PleaseRefreshThisPageToTryAgain: 'Por favor, actualice esta página para intentarlo de nuevo.',
	PleaseWait: 'Por favor, espera...',
	PleaseWaitForHostToJoin: 'Esperando que el anfitrión se una...',
	PleaseWaitForHostToStart: 'Por favor, espera a que el anfitrión inicie esta reunión.',
	PlusAdd: '+ Añadir',
	PlusOthers: '+{count} otras',
	PlusPlanInclusionFive: 'Bandejas de entrada compartidas',
	PlusPlanInclusionFour: 'Videollamadas grupales',
	PlusPlanInclusionHeader: 'Todo en Essential  ',
	PlusPlanInclusionOne: 'IA ilimitada',
	PlusPlanInclusionSix: 'Marca personalizada',
	PlusPlanInclusionThree: 'Programación de grupos',
	PlusPlanInclusionTwo: 'Almacenamiento ilimitado ',
	PlusSubscriptionPlanSubtitle: 'Para que las prácticas se optimicen y crezcan',
	PlusSubscriptionPlanTitle: 'Más',
	PoliceOfficer: 'Oficial de policía',
	PolicyDates: 'Fechas de la póliza',
	PolicyHolder: 'Titular de la póliza',
	PolicyHoldersAddress: 'Dirección del asegurado',
	PolicyMemberId: 'ID del Miembro de la Póliza',
	PolicyStatus: 'Estado de la póliza',
	Popular: 'Popular',
	PortalAccess: 'Acceso al portal',
	PortalNoAppointmentsHeading: 'Lleva un registro de todas las citas futuras y pasadas',
	PortalNoDocumentationHeading: 'Crea y almacena tus documentos de forma segura',
	PortalNoRelationshipsHeading: 'Reúne a quienes apoyan tu viaje',
	PosCodeErrorMessage: 'Se requiere código POS',
	PosoNumber: 'Número de PO/SO',
	PossibleClientDuplicate: 'Posible cliente duplicado',
	PotentialClientDuplicateTitle: 'Registro de cliente potencial duplicado',
	PotentialClientDuplicateWarning:
		'Es posible que esta información de cliente ya exista en su lista de clientes. Por favor, verifique y actualice el registro existente si es necesario o continúe creando un nuevo cliente.',
	PoweredBy: 'Proporcionado por',
	Practice: 'Práctica',
	PracticeDetails: 'Detalles de la práctica',
	PracticeInfoHeader: 'Información de la empresa',
	PracticeInfoPlaceholder: `Nombre de la práctica,
Identificador nacional de proveedor,
Número de identificación del empleador`,
	PracticeLocation: '	Parece que tu práctica está en',
	PracticeSettingsAvailabilityTab: 'Disponibilidad',
	PracticeSettingsBillingTab: 'Configuración de facturación',
	PracticeSettingsClientSettingsTab: 'Configuración del cliente',
	PracticeSettingsGeneralTab: 'General',
	PracticeSettingsOnlineBookingTab: 'Reserva en línea',
	PracticeSettingsServicesTab: 'Servicios',
	PracticeSettingsTaxRatesTab: 'Tasas de impuestos',
	PracticeTemplate: 'Plantilla de práctica',
	Practitioner: 'Profesional',
	PreferredLanguage: 'Idioma preferido',
	PreferredName: 'Nombre preferido',
	Prescription: 'Prescripción',
	PreventionSpecialist: 'Especialista en prevención',
	Preview: 'Vista previa',
	PreviewAndSend: 'Previsualizar y enviar',
	PreviewUnavailable: 'Vista previa no disponible para este tipo de archivo',
	PreviousNotes: 'Notas anteriores',
	Price: 'Precio',
	PriceError: 'El precio debe ser mayor que 0',
	PricePerClient: 'Precio por cliente',
	PricePerUser: 'Por usuario',
	PricePerUserBilledAnnually: 'Por usuario facturado anualmente',
	PricePerUserPerPeriod: '{price} por usuario / {isMonthly, select, true {mes} other {año}}',
	PricingGuide: 'Guía de planes de precios',
	PricingPlanPerMonth: '/ mes',
	PricingPlanPerYear: '/ año',
	Primary: 'Primario',
	PrimaryInsurance: 'Seguro primario',
	PrimaryPolicy: 'Seguro primario',
	PrimaryTimezone: 'Zona horaria principal',
	Print: 'Imprimir',
	PrintToCms1500: 'Imprimir en CMS1500',
	PrivatePracticeConsultant: 'Consultor privado',
	Proceed: 'Continuar',
	ProcessAtTimeOfBookingDesc: 'Los clientes deben pagar el precio completo del servicio para reservar en línea',
	ProcessAtTimeOfBookingLabel: 'Procesar pagos al momento de la reserva',
	Processing: 'Procesando',
	ProcessingFee: 'Costo de procesamiento',
	ProcessingFeeToolTip: `Carepatron te permite repercutir los costos de procesamiento a tus clientes. 
 En algunas jurisdicciones está prohibido cobrar costos de procesamiento a tus clientes. Es tu responsabilidad cumplir con las leyes aplicables.`,
	ProcessingRequest: 'Procesando solicitud...',
	Product: 'Producto',
	Profession: 'Profesión',
	ProfessionExample: 'Terapeuta, Nutricionista, Dentista',
	ProfessionPlaceholder: 'Empieza a escribir tu profesión o elige de la lista',
	ProfessionalPlanInclusion1: 'Almacenamiento ilimitado',
	ProfessionalPlanInclusion2: 'Tareas ilimitadas',
	ProfessionalPlanInclusion3: 'Garantía del tiempo de actividad del 99,99%',
	ProfessionalPlanInclusion4: 'Soporte al cliente 24/7',
	ProfessionalPlanInclusion5: 'Recordatorios por SMS',
	ProfessionalPlanInclusionHeader: 'Todo en Starter, más...',
	Professions: 'Profesiones',
	Profile: 'Perfil',
	ProfilePhotoFileSizeLimit: 'Límite de tamaño de archivo de 5MB',
	ProfilePopoverSubTitle: 'Ha iniciado sesión como <strong>{email}</strong>',
	ProfilePopoverTitle: 'Tus espacios de trabajo',
	PromoCode: 'Código promocional',
	PromotionCodeApplied: '{promo} aplicada',
	ProposeNewDateTime: 'Proponer una nueva fecha/hora',
	Prosthetist: 'Protésico',
	Provider: 'Proveedor',
	ProviderBillingPlanExpansionManageButton: 'Administrar plan',
	ProviderCommercialNumber: 'Número comercial del proveedor',
	ProviderDetails: 'Detalles del proveedor',
	ProviderDetailsAddress: 'Dirección',
	ProviderDetailsName: 'Nombre',
	ProviderDetailsPhoneNumber: 'Número de teléfono',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'Lo siento, este proveedor ya tiene una cuenta de facturación existente',
	ProviderInfoPlaceholder: `Nombre del personal, 
Dirección de correo electrónico, 
Número de teléfono, 
Identificador nacional de proveedor, 
Número de licencia`,
	ProviderIsChargedProcessingFee: 'Usted pagará la tarifa de procesamiento',
	ProviderPaymentFormBackButton: 'Atrás',
	ProviderPaymentFormBillingAddressCity: 'Ciudad',
	ProviderPaymentFormBillingAddressCountry: 'País',
	ProviderPaymentFormBillingAddressLine1: 'Línea 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Código postal',
	ProviderPaymentFormBillingEmail: 'Correo electrónico',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Detalles de la tarjeta de crédito',
	ProviderPaymentFormCardExpiry: 'Vencimiento',
	ProviderPaymentFormCardHolderAddressTitle: 'Dirección',
	ProviderPaymentFormCardHolderName: 'Nombre del titular de la tarjeta',
	ProviderPaymentFormCardHolderTitle: 'Detalles del titular de la tarjeta',
	ProviderPaymentFormCardNumber: 'Número de tarjeta',
	ProviderPaymentFormPlanTitle: 'Plan elegido',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}): ',
	ProviderPaymentFormSaveButton: 'Guardar suscripción',
	ProviderPaymentFreePlanDescription:
		'Elegir el plan gratuito eliminará el acceso de cada miembro del personal a sus clientes en su proveedor. Sin embargo, su acceso seguirá activo y aún podrá usar la plataforma.',
	ProviderPaymentStepName: 'Revisar y pagar',
	ProviderPaymentSuccessSnackbar: '¡Genial! Tu nuevo plan ha sido guardado exitosamente.',
	ProviderPaymentTitle: 'Revisar y pagar',
	ProviderPlanNetworkIdentificationNumber: 'Número de identificación de la red del plan del proveedor',
	ProviderRemindersSettingsBannerAction: 'Ir a Gestión de Flujo de Trabajo',
	ProviderRemindersSettingsBannerDescription:
		'Encuentra todos los recordatorios en la nueva pestaña de **Gestión de Flujo de Trabajo** en **Configuración**. Esta actualización trae potentes funciones nuevas, plantillas mejoradas y herramientas de automatización más inteligentes para aumentar tu productividad. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Tu experiencia de recordatorios está mejorando',
	ProviderTaxonomy: 'Taxonomía de proveedores',
	ProviderUPINNumber: 'Número UPIN del proveedor',
	ProviderUsedStoragePercentage: '¡El almacenamiento de {providerName} está {usedStoragePercentage}% lleno!',
	PsychiatricNursePractitioner: 'Enfermera psiquiátrica',
	Psychiatrist: 'Psiquiatra',
	Psychiatrists: 'Psiquiatras',
	Psychiatry: 'Psiquiatría',
	Psychoanalyst: 'Psicoanalista',
	Psychologist: 'Psicólogo',
	Psychologists: 'Psicólogos',
	Psychology: 'Psicología',
	Psychometrician: 'Psicometrista',
	PsychosocialRehabilitationSpecialist: 'Especialista en rehabilitación psicosocial',
	Psychotheraphy: 'Psicoterapia',
	Psychotherapists: 'Psicoterapeutas',
	Psychotherapy: 'Psicoterapia',
	PublicCallDialogTitle: 'Llamada de video con ',
	PublicCallDialogTitlePlaceholder: 'Llamada de video proporcionada por Carepatron',
	PublicFormBackToForm: 'Enviar otra respuesta',
	PublicFormConfirmSubmissionHeader: 'Confirmar envío',
	PublicFormNotFoundDescription:
		'El formulario que está buscando puede haber sido eliminado o el enlace puede ser incorrecto. Por favor, compruebe la URL e inténtelo de nuevo.',
	PublicFormNotFoundTitle: 'Formulario no encontrado',
	PublicFormSubmissionError: 'Envío fallido. Por favor, inténtalo de nuevo.',
	PublicFormSubmissionSuccess: 'Formulario enviado exitosamente',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} envió el formulario público {noteTitle}',
	PublicFormSubmittedSubtitle: 'Su envío ha sido recibido.',
	PublicFormSubmittedTitle: '¡Gracias!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Te hemos enviado un código de confirmación a tu correo electrónico',
	PublicFormsInvalidConfirmationCode: 'Código de confirmación no válido',
	PublicHealthInspector: 'Inspector de Salud Pública',
	PublicTemplates: 'Plantillas públicas',
	Publish: 'Publicar',
	PublishTemplate: 'Publicar plantilla',
	PublishTemplateFeatureBannerSubheader: 'Plantillas diseñadas para beneficiar a la comunidad',
	PublishTemplateHeader: 'Publicar {title}',
	PublishTemplateToCommunity: 'Publicar plantilla a la comunidad',
	PublishToCommunity: 'Publicar en la comunidad',
	PublishToCommunitySuccessMessage: 'Publicado con éxito en la comunidad',
	Published: 'Publicado',
	PublishedBy: 'Publicado por {name}',
	PublishedNotesAreNotAutosaved: 'Las notas publicadas no se guardan automáticamente',
	PublishedOnCarepatronCommunity: 'Publicado en la comunidad Carepatron',
	Purchase: 'Compra',
	PushToCalendar: 'Enviar a calendario',
	Question: 'Pregunta',
	QuestionOrTitle: 'Pregunta o título',
	QuickActions: 'Acciones rápidas',
	QuickThemeSwitcherColorBasil: 'Albahaca',
	QuickThemeSwitcherColorBlueberry: 'Arándano',
	QuickThemeSwitcherColorFushcia: 'Fucsia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Musgo',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Squash',
	RadiationTherapist: 'Radioterapeuta',
	Radiologist: 'Radiólogo',
	Read: 'Leído',
	ReadOnly: 'Solo lectura',
	ReadOnlyAppointment: 'Cita de solo lectura',
	ReadOnlyEventBanner: 'Esta cita está sincronizada desde un calendario de solo lectura y no se puede editar.',
	ReaderMaxDepthHasBeenExceededCode:
		'La nota está demasiado anidada. Intente quitar la sangría de algunos elementos.',
	ReadyForMapping: 'Listo para el mapeo',
	RealEstateAgent: 'Agente de la propiedad inmobiliaria',
	RearrangeClientFields: 'Reorganizar campos de cliente en configuración de cliente',
	Reason: 'Razón',
	ReasonForChange: 'Motivo del cambio',
	RecentAppointments: 'Citas recientes',
	RecentServices: 'Servicios recientes',
	RecentTemplates: 'Plantillas recientes',
	RecentlyUsed: 'Usado recientemente',
	Recommended: 'Recomendado',
	RecommendedTemplates: 'Plantillas recomendadas',
	Recording: 'Grabación',
	RecordingEnded: 'La grabación finalizó',
	RecordingInProgress: 'Grabación en curso',
	RecordingMicrophoneAccessErrorMessage:
		'Permita el acceso al micrófono en su navegador y actualice para comenzar a grabar.',
	RecurrenceCount: ', {count, plural, one {una vez} other {# veces}}',
	RecurrenceDaily: '{count, plural, one {Diario} other {Días}}',
	RecurrenceEndAfter: 'Después',
	RecurrenceEndNever: 'Nunca',
	RecurrenceEndOn: 'En',
	RecurrenceEvery: 'Cada {description}',
	RecurrenceMonthly: '{count, plural, one {Mensual} other {Meses}}',
	RecurrenceOn: 'en {description}',
	RecurrenceOnAllDays: 'en todos los días',
	RecurrenceUntil: 'hasta {description}',
	RecurrenceWeekly: '{count, plural, one {Semanal} other {Semanas}}',
	RecurrenceYearly: '{count, plural, one {Anual} other {Años}}',
	Recurring: 'Periódico',
	RecurringAppointment: 'Cita recurrente',
	RecurringAppointmentsLimitedBannerText:
		'No se muestran todas las citas recurrentes. Reduzca el rango de fechas para ver todas las citas recurrentes del período.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# evento} other {# eventos}}</b> se crearán en las siguientes fechas',
	Redo: 'Rehacer',
	ReferFriends: 'Recomiende amigos',
	Reference: 'Referencia',
	ReferralCreditedNotificationSubject: 'Su crédito de recomendación de {currency} {amount} se ha aplicado',
	ReferralEmailDefaultBody: `Gracias a {name}, se te ha enviado una actualización GRATUITA de 6 meses a Carepatron. Únete a nuestra comunidad de más de 3 millones de profesionales de la salud creada para una nueva forma de trabajar!

Gracias,
El equipo de Carepatron`,
	ReferralEmailDefaultSubject: 'Has sido invitado a unirte a Carepatron',
	ReferralHasNotSignedUpDescription: 'Tu amigo no se ha registrado todavía',
	ReferralHasSignedUpDescription: 'Tu amigo se ha registrado.',
	ReferralInformation: 'Información de referencia',
	ReferralJoinedNotificationSubject: '{actorProfileName} se ha unido a Carepatron',
	ReferralListErrorDescription: 'No se pudo cargar la lista de referencias.',
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {mes} other {meses}}</b> activo',
	ReferralRewardBanner: '¡Regístrate y reclama tu recompensa por referido!',
	Referrals: 'Referencias',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} mes {percentOff, select, 100 {pago gratis} other {{percentOff}% de descuento}} {type, select, SubscriptionUpgrade {actualizar} other {}}',
	ReferredUserBenefitTitle: 'Ellas consiguen!',
	Referrer: 'Referente',
	ReferringProvider: 'Proveedor referente',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} crédito cuando se activan 3 amigos.',
	ReferringUserBenefitTitle: '¡Usted obtiene!',
	RefreshPage: 'Recargar página',
	Refund: 'Reembolso',
	RefundAcknowledgement: 'He reembolsado a {clientName} fuera de Carepatron',
	RefundAcknowledgementValidationMessage: 'Por favor, confirme que ha reembolsado este importe',
	RefundAmount: 'Monto del reembolso',
	RefundContent:
		'Los reembolsos tardan entre 7 y 10 días en aparecer en la cuenta de tu cliente. No se reembolsarán las tarifas de pago, pero no hay cargos adicionales por los reembolsos. Los reembolsos no se pueden cancelar y es posible que algunos deban revisarse antes de procesarse.',
	RefundCouldNotBeProcessed: 'No se pudo procesar el reembolso',
	RefundError:
		'Este reembolso no se puede procesar automáticamente en este momento. Comuníquese con el servicio de asistencia de Carepatron para solicitar el reembolso de este pago.',
	RefundExceedTotalValidationError: 'El importe no debe exceder el total pagado',
	RefundFailed: 'Reembolso fallido',
	RefundFailedTooltip:
		'El reembolso de este pago falló anteriormente y no se puede volver a intentar. Comuníquese con el servicio de asistencia.',
	RefundNonStripePaymentContent:
		'Este pago se realizó mediante un método externo a Carepatron (por ejemplo, efectivo, banca por Internet). La emisión de un reembolso dentro de Carepatron no devolverá fondos al cliente.',
	RefundReasonDescription: 'Agregar un motivo de reembolso puede ayudar al revisar las transacciones de sus clientes',
	Refunded: 'Reintegrado',
	Refunds: 'Reembolsos',
	RefundsTableEmptyState: 'No se encontraron reembolsos',
	Regenerate: 'Regenerar',
	RegisterButton: 'Registrarse',
	RegisterEmail: 'Correo electrónico',
	RegisterFirstName: 'Nombre',
	RegisterLastName: 'Apellido',
	RegisterPassword: 'Contraseña',
	RegisteredNurse: 'Enfermero diplomado',
	RehabilitationCounselor: 'Asesor de rehabilitación',
	RejectAppointmentFormTitle: '¿No puedes asistir? Por favor, haznos saber por qué y propón una nueva fecha y hora.',
	Rejected: 'Rechazado',
	Relationship: 'Relación',
	RelationshipDetails: 'Detalles de la relación',
	RelationshipEmptyStateTitle: 'Manténgase conectado con aquellos que apoyan a su cliente',
	RelationshipPageAccessTypeColumnName: 'Acceso al perfil',
	RelationshipSavedSuccessSnackbar: '¡Relación guardada exitosamente!',
	RelationshipSelectorFamilyAdmin: 'Familia',
	RelationshipSelectorFamilyMember: 'Miembro de la familia',
	RelationshipSelectorProviderAdmin: 'Administrador del proveedor',
	RelationshipSelectorProviderStaff: 'Personal del proveedor',
	RelationshipSelectorSupportNetworkPrimary: 'Amigo',
	RelationshipSelectorSupportNetworkSecondary: 'Red de apoyo',
	RelationshipStatus: 'Estado civil',
	RelationshipType: 'Tipo de relación',
	RelationshipTypeClientOwner: 'Cliente',
	RelationshipTypeFamilyAdmin: 'Relaciones',
	RelationshipTypeFamilyMember: 'Familia',
	RelationshipTypeFriendOrSupport: 'Amigo o red de apoyo',
	RelationshipTypeProviderAdmin: 'Administrador del proveedor',
	RelationshipTypeProviderStaff: 'Personal',
	RelationshipTypeSelectorPlaceholder: 'Buscar tipos de relaciones',
	Relationships: 'Relaciones',
	Remaining: 'restante',
	RemainingTime: '{time} tiempo',
	Reminder: 'Recordatorio',
	ReminderColor: 'Color recordatorio',
	ReminderDetails: 'Detalles del recordatorio',
	ReminderEditDisclaimer: 'Los cambios solo se reflejarán en nuevas citas',
	ReminderSettings: 'Configuración de recordatorios de citas',
	Reminders: 'Recordatorios',
	Remove: 'Eliminar',
	RemoveAccess: 'Eliminar acceso',
	RemoveAllGuidesBtn: 'Eliminar todas las guías',
	RemoveAllGuidesPopoverBody:
		'Cuando hayas terminado con las guías de incorporación, simplemente utiliza el botón de eliminar guías en cada panel.',
	RemoveAllGuidesPopoverTitle: '¿Ya no necesitas tus guías de incorporación?',
	RemoveAsDefault: 'Eliminar como predeterminado"',
	RemoveAsIntake: 'Quitar como entrada',
	RemoveCol: 'Quitar columna',
	RemoveColor: 'Eliminar color',
	RemoveField: 'Eliminar campo',
	RemoveFromCall: 'Quitar de la llamada',
	RemoveFromCallDescription: '¿Está seguro de que desea eliminar <mark>{attendeeName}</mark> de esta videollamada?',
	RemoveFromCollection: 'Eliminar de la colección',
	RemoveFromCommunity: 'Eliminar de la comunidad',
	RemoveFromFolder: 'Eliminar de la carpeta',
	RemoveFromFolderConfirmationDescription:
		'¿Está seguro de que desea eliminar esta plantilla de esta carpeta? Esta acción no se puede deshacer, pero puede optar por moverla de nuevo más tarde.',
	RemoveFromIntakeDefault: 'Eliminar de la entrada predeterminada.',
	RemoveGuides: 'Quitar guías',
	RemoveMfaConfirmationDescription:
		'Eliminar la autenticación multifactor (MFA) reducirá la seguridad de su cuenta. ¿Desea continuar?',
	RemoveMfaConfirmationTitle: '¿Quitar MFA?',
	RemovePaymentMethodDescription: `Esto eliminará todo acceso y uso futuro de este método de pago.
Esta acción no se puede deshacer.`,
	RemoveRow: 'Quitar fila',
	RemoveTable: 'Quitar tabla',
	RemoveTemplateAsDefaultIntakeSuccess:
		'<strong>{templateTitle}</strong> se eliminó correctamente como plantilla de admisión predeterminada',
	RemoveTemplateFromCommunity: 'Quitar plantilla de la comunidad',
	RemoveTemplateFromFolder: '{templateTitle} eliminado correctamente de {folderTitle}',
	Rename: 'Rebautizar',
	RenderingProvider: 'Proveedor de renderizado',
	Reopen: 'Reabrir',
	ReorderServiceGroupFailure: 'Fallo al reordenar la recogida',
	ReorderServiceGroupSuccess: 'Colección reordenada con éxito',
	ReorderServicesFailure: 'Error al reordenar los servicios',
	ReorderServicesSuccess: 'Servicios reordenados con éxito',
	ReorderYourServiceList: 'Reordenar la lista de servicios',
	ReorderYourServiceListDescription:
		'La forma en que organices tus servicios y colecciones se reflejará en tu página de reservas online para que la vean todos tus clientes.',
	RepeatEvery: 'Repetir cada',
	RepeatOn: 'Repetir en',
	Repeating: 'Repetición',
	Repeats: 'Se repite',
	RepeatsEvery: 'Se repite cada',
	Rephrase: 'Reformular',
	Replace: 'Reemplazar',
	ReplaceBackground: 'Reemplazar fondo',
	ReplacementOfPriorClaim: 'Reemplazo de reclamación anterior',
	Report: 'Informe',
	Reprocess: 'Reprocesar',
	RepublishTemplateToCommunity: 'Republica la plantilla a la comunidad',
	RequestANewVerificationLink: 'Solicitar un nuevo enlace de verificación',
	RequestCoverageReport: 'Solicitar informe de cobertura',
	RequestingDevicePermissions: 'Solicitando permisos del dispositivo...',
	RequirePaymentMethodDesc:
		'Los clientes deben ingresar los detalles de su tarjeta de crédito para reservar en línea',
	RequirePaymentMethodLabel: 'Requerir detalles de la tarjeta de crédito',
	Required: 'obligatorio',
	RequiredField: 'Obligatorio',
	RequiredUrl: 'La URL es obligatoria.',
	Reschedule: 'Reprogramar',
	RescheduleBookingLinkModalDescription: 'Tu cliente puede cambiar la fecha y hora de su cita usando este enlace.',
	RescheduleBookingLinkModalTitle: 'Enlace de reprogramación de reserva',
	RescheduleLink: 'Enlace de reprogramación',
	Resend: 'Reenviar',
	ResendConfirmationCode: 'Reenviar código de confirmación',
	ResendConfirmationCodeDescription:
		'Por favor ingresa tu dirección de correo electrónico y te enviaremos otro código de confirmación',
	ResendConfirmationCodeSuccess: 'Se ha reenviado el código de confirmación, por favor revisa tu bandeja de entrada',
	ResendNewEmailVerificationSuccess: 'Se ha enviado un nuevo enlace de verificación a {email}',
	ResendVerificationEmail: 'Reenviar correo electrónico de verificación',
	Reset: 'Reiniciar',
	Resources: 'Recursos',
	RespiratoryTherapist: 'Terapeuta respiratorio',
	RespondToHistoricAppointmentError:
		'Esta es una cita histórica, por favor contacta a tu profesional si tienes alguna pregunta.',
	Responder: 'Respondedor',
	RestorableItemModalDescription:
		'¿Estás seguro de que quieres eliminar {context}?{canRestore, select, true { Puedes restaurarlo más tarde.} other {}}',
	RestorableItemModalTitle: 'Eliminar {type}',
	Restore: 'Restaurar',
	RestoreAll: 'Restaurar todo',
	Restricted: 'Restringido',
	ResubmissionCodeReferenceNumber: 'Código de reenvío y número de referencia',
	Resubmit: 'Volver a enviar',
	Resume: 'Reanudar',
	Retry: 'Reintentar',
	RetryingConnectionAttempt: 'Reintentando conexión... (Intento {retryCount} de {maxRetries})',
	ReturnToForm: 'Regresar al formulario',
	RevertClaimStatus: 'Revertir estado de la reclamación',
	RevertClaimStatusDescriptionBody:
		'Esta reclamación tiene pagos vinculados, y cambiar el estado puede afectar el seguimiento o el procesamiento de los pagos, lo que podría requerir una conciliación manual.',
	RevertClaimStatusDescriptionTitle: '¿Está seguro de que desea volver a {status}?',
	RevertClaimStatusError: 'No se pudo revertir el estado de la reclamación',
	RevertToDraft: 'Volver al borrador',
	Review: 'Revisar',
	ReviewsFirstQuote: 'Citas en cualquier lugar y en cualquier momento',
	ReviewsSecondJobTitle: 'Lifehouse Clinic',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'También me encanta la aplicación de Carepatron. Me ayuda a hacer un seguimiento de mis clientes y mi trabajo mientras estoy en movimiento.',
	ReviewsThirdJobTitle: 'Manila Bay Clinic',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote: 'La facilidad de navegación y la hermosa interfaz de usuario me hace sonreír todos los días.',
	RightAlign: 'Alinear a la derecha',
	Role: 'Role',
	Roster: 'Asistentes',
	RunInBackground: 'Ejecutar en segundo plano',
	SMS: 'Mensaje de texto',
	SMSAndEmailReminder: 'Recordatorio por SMS y correo electrónico',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Te vas de Carepatron',
	SafetyRedirectSubtext: 'Si confías en este enlace, selecciónalo para continuar.',
	SalesRepresentative: 'Representante de ventas',
	SalesTax: 'Impuesto a las Ventas',
	SalesTaxHelp: 'Incluye el impuesto sobre las ventas en las facturas generadas',
	SalesTaxIncluded: 'Sí',
	SalesTaxNotIncluded: 'No',
	SaoPaulo: 'São Paulo',
	Saturday: 'Sábado',
	Save: 'Guardar',
	SaveAndClose: 'Guardar y cerrar',
	SaveAndExit: 'Guardar y salir',
	SaveAndLock: 'Guardar y bloquear',
	SaveAsDraft: 'Guardar como borrador',
	SaveCardForFuturePayments: 'Guardar tarjeta para pagos futuros',
	SaveChanges: 'Guardar cambios',
	SaveCollection: 'Guardar colección',
	SaveField: 'Guardar campo',
	SavePaymentMethod: 'Guardar método de pago',
	SavePaymentMethodDescription: 'No se te cobrará hasta tu primera cita.',
	SavePaymentMethodSetupError: 'Ocurrió un error inesperado y no pudimos configurar los pagos en este momento.',
	SavePaymentMethodSetupInvoiceLater: 'Los pagos pueden configurarse y guardarse al pagar tu primera factura.',
	SaveSection: 'Guardar sección',
	SaveService: 'Crear nuevo servicio',
	SaveTemplate: 'Guardar plantilla',
	Saved: 'Guardado',
	SavedCards: 'Tarjetas guardadas',
	SavedPaymentMethods: 'Guardados',
	Saving: 'Guardando...',
	ScheduleAppointmentsAndOnlineServices: 'Agendar citas y servicios en línea',
	ScheduleName: 'Nombre del horario',
	ScheduleNew: 'Programar nueva',
	ScheduleSend: 'Programar envío',
	ScheduleSendAlertInfo: 'Las conversaciones programadas se enviarán en su tiempo programado.',
	ScheduleSendByName: '<strong>Enviar programa</strong> • {time} por {displayName}',
	ScheduleSetupCall: 'Programar llamada de configuración',
	Scheduled: 'Programado',
	SchedulingSend: 'Programar envío',
	School: 'Escuela',
	ScrollToTop: 'Desplazarse hacia arriba',
	Search: 'Buscar',
	SearchAndConvertToLanguage: 'Buscar y convertir a idioma',
	SearchBasicBlocks: 'Buscar bloques básicos',
	SearchByName: 'Buscar por nombre',
	SearchClaims: 'Buscar reclamaciones',
	SearchClientFields: 'Buscar campos de cliente',
	SearchClients: 'Búsqueda por nombre de cliente, ID de cliente o número de teléfono',
	SearchCommandNotFound: 'No se encontraron resultados para "{searchTerm}"',
	SearchContacts: 'Cliente o contacto',
	SearchContactsPlaceholder: 'Buscar contactos',
	SearchConversations: 'Buscar conversaciones',
	SearchInputPlaceholder: 'Buscar todos los recursos',
	SearchInvoiceNumber: 'Buscar número de factura',
	SearchInvoices: 'Buscar facturas',
	SearchMultipleContacts: 'Clientes o contactos',
	SearchMultipleContactsOptional: 'Clientes o contactos (opcional)',
	SearchOrCreateATag: 'Buscar o crear un tag',
	SearchPayments: 'Buscar pagos',
	SearchPrepopulatedData: 'Buscar campos de datos pre-poblados',
	SearchRelationships: 'Buscar relaciones',
	SearchRemindersAndWorkflows: 'Recordatorios y flujos de trabajo de búsqueda',
	SearchServices: 'Servicios de búsqueda',
	SearchTags: 'Buscar etiquetas',
	SearchTeamMembers: 'Buscar miembros del equipo',
	SearchTemplatePlaceholder: 'Buscar {templateCount}+ recursos',
	SearchTimezone: 'Buscar zona horaria...',
	SearchTrashItems: 'Buscar elementos',
	SearchUnsplashPlaceholder: 'Buscar fotos gratuitas de alta resolución desde Unsplash',
	Secondary: 'Secundario',
	SecondaryInsurance: 'Seguro secundario',
	SecondaryPolicy: 'Seguro secundario',
	SecondaryTimezone: 'Zona horaria secundaria',
	Secondly: 'En segundo lugar',
	Section: 'Sección',
	SectionCannotBeEmpty: 'Una sección debe tener al menos una fila',
	SectionFormSecondaryText: 'Título y descripción de la sección',
	SectionName: 'Nombre de la sección',
	Sections: 'Secciones',
	SeeLess: 'Ver menos',
	SeeLessUpcomingAppointments: 'Ver menos citas próximas',
	SeeMore: 'Ver más',
	SeeMoreUpcomingAppointments: 'Ver más citas próximas',
	SeeTemplateLibrary: 'Ver biblioteca de plantillas',
	Seen: 'Vista',
	SeenByName: '<strong>Vista</strong> • {time} por {displayName}',
	SelectAll: 'Seleccionar todo',
	SelectAssignees: 'Seleccionar asignados',
	SelectAttendees: 'Seleccionar asistentes',
	SelectCollection: 'Seleccionar colección',
	SelectCorrespondingAttributes: 'Seleccione los atributos correspondientes',
	SelectPayers: 'Seleccionar pagadores',
	SelectProfile: 'Seleccionar perfil',
	SelectServices: 'Seleccionar servicios',
	SelectTags: 'Seleccionar etiquetas',
	SelectTeamOrCommunity: 'Seleccionar Equipo o Comunidad',
	SelectTemplate: 'Seleccionar plantilla',
	SelectType: 'Seleccionar tipo',
	Selected: 'Seleccionado',
	SelfPay: 'Pago por cuenta propia',
	Send: 'Enviar',
	SendAndClose: 'Enviar & cerrar',
	SendAndStopIgnore: 'Enviar y dejar de ignorar',
	SendEmail: 'Enviar correo electrónico',
	SendIntake: 'Enviar admisión',
	SendIntakeAndForms: 'Enviar formularios de admisión',
	SendMeACopy: 'Enviar una copia a mí mismo',
	SendNotificationEmailWarning:
		'Algunos asistentes no tienen una dirección de correo electrónico y no recibirán notificaciones y recordatorios automáticos.',
	SendNotificationLabel: 'Elegir asistentes para notificar por correo electrónico',
	SendOnlinePayment: 'Enviar pago en línea',
	SendOnlinePaymentTooltipTitleAdmin: 'Por favor, agregue su configuración de pago preferida',
	SendOnlinePaymentTooltipTitleStaff:
		'Por favor, pida al propietario del proveedor que configure los pagos en línea.',
	SendPaymentLink: 'Enviar enlace de pago',
	SendReaction: 'Enviar una reacción',
	SendScheduledForDate: 'Enviar programado para {date}',
	SendVerificationEmail: 'Enviar correo electrónico de verificación',
	SendingFailed: 'Envío fallido',
	Sent: 'Enviado',
	SentByName: '<strong>Enviado</strong> • {time} por {displayName}',
	Seoul: 'Seúl',
	SeparateDuplicateClientsDescription:
		'Los registros de clientes seleccionados permanecerán separados del resto a menos que elija fusionarlos.',
	Service: 'Servicio',
	'Service/s': 'Servicio/s',
	ServiceAdjustment: 'Ajuste del servicio',
	ServiceAllowNewClientsIndicator: 'Permitir nuevos clientes',
	ServiceAlreadyExistsInCollection: 'El servicio ya existe en la colección',
	ServiceBookableOnlineIndicator: 'Reservable en línea',
	ServiceCode: 'Código',
	ServiceCodeErrorMessage: 'Se requiere un código de servicio',
	ServiceCodeSelectorPlaceholder: 'Agregar un código de servicio',
	ServiceColour: 'Color del servicio',
	ServiceCoverageDescription: 'Elija los servicios elegibles y el copago para esta póliza de seguro.',
	ServiceCoverageGoToServices: 'Ir a servicios',
	ServiceCoverageNoServicesDescription:
		'Personalice los montos de copago del servicio para anular el copago predeterminado de la póliza. Desactive la cobertura para evitar que se reclamen servicios contra la póliza.',
	ServiceCoverageNoServicesLabel: 'No se han encontrado servicios.',
	ServiceCoverageTitle: 'Cobertura del servicio',
	ServiceDate: 'Fecha de servicio',
	ServiceDetails: 'Detalles del servicio',
	ServiceDuration: 'Duración',
	ServiceEmptyState: 'Aún no hay servicios',
	ServiceErrorMessage: 'Se requiere un servicio',
	ServiceFacility: 'Instalación de servicio',
	ServiceName: 'Nombre del servicio',
	ServiceRate: 'Tarifa',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} para {serviceReceiptNumber, select, undefined {user} other {{clientName}}} requiere información adicional',
	ServiceSalesTax: 'Impuesto de ventas',
	ServiceType: 'Servicio',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Actualiza la página para obtener las últimas novedades de CarePatron.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'Recargar la página',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Está utilizando una versión anterior.',
	ServiceWorkerForceUIUpdateDialogTitle: '¡Bienvenido de nuevo!',
	Services: 'Servicios',
	ServicesAndAvailability: 'Servicios y disponibilidad',
	ServicesAndDiagnosisCodesHeader: 'Agregar servicios y códigos de diagnóstico',
	ServicesCount: '{count,plural,=0{Servicios}one{Servicio}other{Servicios}}',
	ServicesPlaceholder: 'Servicios',
	ServicesProvidedBy: 'Servicio/s proporcionado/s por',
	SetAPhysicalAddress: 'Establecer una dirección física',
	SetAVirtualLocation: 'Establecer una ubicación virtual',
	SetAsDefault: 'Establecer como predeterminado',
	SetAsIntake: 'Establecer como entrada',
	SetAsIntakeDefault: 'Establecer como predeterminado de entrada.',
	SetAvailability: 'Establecer disponibilidad',
	SetTemplateAsDefaultIntakeSuccess:
		'Estableció correctamente <strong>{templateTitle}</strong> como plantilla de admisión predeterminada',
	SetUpMfaButton: 'Configurar MFA',
	SetYourLocation: 'Establece tu <mark>ubicación</mark>',
	SetYourLocationDescription: 'No tengo dirección comercial <span>(solo servicios en línea y móviles)</span>',
	SettingUpPayers: 'Configuración de pagadores',
	Settings: 'Configuración',
	SettingsNewUserPasswordDescription:
		'Una vez que te hayas registrado, te enviaremos un código de confirmación que puedes usar para confirmar tu cuenta',
	SettingsNewUserPasswordTitle: 'Registrarse en Carepatron',
	SettingsTabAutomation: 'Automatización',
	SettingsTabBillingDetails: 'Detalles de facturación',
	SettingsTabConnectedApps: 'Aplicaciones conectadas',
	SettingsTabCustomFields: 'Campos personalizados',
	SettingsTabDetails: 'Detalles',
	SettingsTabInvoices: 'Facturas',
	SettingsTabLocations: 'Ubicaciones',
	SettingsTabNotifications: 'Notificaciones',
	SettingsTabOnlineBooking: 'Reserva en línea',
	SettingsTabPayers: 'Pagadores',
	SettingsTabReminders: 'Recordatorios',
	SettingsTabServices: 'Servicios',
	SettingsTabServicesAndAvailability: 'Servicios y disponibilidad',
	SettingsTabSubscriptions: 'Suscripciones',
	SettingsTabWorkflowAutomations: 'Automatizaciones',
	SettingsTabWorkflowReminders: 'Recordatorios básicos',
	SettingsTabWorkflowTemplates: 'Plantillas',
	Setup: 'Configurar',
	SetupGuide: 'Guía de configuración',
	SetupGuideAddServicesActionLabel: 'Comenzar',
	SetupGuideAddServicesSubtitle: '4 pasos • 2 min',
	SetupGuideAddServicesTitle: 'Agrega tus servicios',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Inicio',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 pasos • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Habilitar pagos en línea',
	SetupGuideImportClientsActionLabel: 'Inicio',
	SetupGuideImportClientsSubtitle: '4 pasos • 3 min',
	SetupGuideImportClientsTitle: 'Importa tus clientes',
	SetupGuideImportTemplatesActionLabel: 'Inicio',
	SetupGuideImportTemplatesSubtitle: '2 pasos • 1 min',
	SetupGuideImportTemplatesTitle: 'Importa tus plantillas',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Inicio',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 pasos • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personaliza tu espacio de trabajo',
	SetupGuideSetLocationActionLabel: 'Comenzar',
	SetupGuideSetLocationSubtitle: '4 pasos • 1 min',
	SetupGuideSetLocationTitle: 'Establece tu ubicación',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Invitar equipo',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Invita a tu equipo a comunicarse y gestionar tareas sin esfuerzo.',
	SetupGuideSuggestedAddTeamMembersTag: 'Configuración',
	SetupGuideSuggestedAddTeamMembersTitle: 'Añadir miembros del equipo',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Personalizar',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Siéntete profesional con tu logotipo único y los colores de tu marca.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Personalizar marca',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Descargar',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Accede a tu espacio de trabajo en cualquier lugar, en cualquier momento y en cualquier dispositivo.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Configuración',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Descarga la aplicación',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Establecer disponibilidad',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Previene las reservas dobles configurando tu disponibilidad.',
	SetupGuideSuggestedEditAvailabilityTag: 'Programación',
	SetupGuideSuggestedEditAvailabilityTitle: 'Editar disponibilidad',
	SetupGuideSuggestedImportClientsActionLabel: 'Importar',
	SetupGuideSuggestedImportClientsSubtitle: 'Sube al instante tus datos de clientes existentes con solo un clic.',
	SetupGuideSuggestedImportClientsTag: 'Configuración',
	SetupGuideSuggestedImportClientsTitle: 'Importar clientes',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Editar recordatorios',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Reduce las ausencias sin aviso con recordatorios de citas automáticos.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Personalizar recordatorios',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Iniciar llamada',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Organiza una llamada y conecta con clientes usando nuestras herramientas de video con tecnología de IA.',
	SetupGuideSuggestedStartVideoCallTag: 'Telemedicina',
	SetupGuideSuggestedStartVideoCallTitle: 'Iniciar videollamada',
	SetupGuideSuggestedTryActionsTitle: 'Cosas para intentar 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Prueba la asistencia de IA',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'Obtén respuestas instantáneas a todas tus preguntas de trabajo.',
	SetupGuideSuggestedUseAIAssistantTag: 'Nuevo',
	SetupGuideSuggestedUseAIAssistantTitle: 'Usa asistente de IA',
	SetupGuideSyncCalendarActionLabel: 'Inicio',
	SetupGuideSyncCalendarSubtitle: '1 paso • menos de 1 min',
	SetupGuideSyncCalendarTitle: 'Sincroniza tu calendario',
	SetupGuideVerifyEmailLabel: 'Verificar',
	SetupGuideVerifyEmailSubtitle: '2 pasos • 2 min',
	SetupOnlineStripePayments: 'Usa Stripe para pagos en línea',
	SetupPayments: 'Configurar pagos',
	Sex: 'Sexo',
	SexSelectorPlaceholder: 'Masculino / Femenino / Prefiero no decirlo',
	Share: 'Compartir',
	ShareBookingLink: 'Compartir enlace de reserva',
	ShareNoteDefaultMessage: `Hola
{name} ha compartido "{documentName}" contigo.

Gracias,
{practiceName}`,
	ShareNoteMessage: `Hola

{name} ha compartido "{documentName}" {isResponder, select, true {con algunas preguntas para que las completes.} other {contigo.}}

Gracias,
{practiceName}`,
	ShareNoteTitle: 'Compartir ‘{noteTitle}‘',
	ShareNotesWithClients: 'Compartir con clientes o contactos',
	ShareScreen: 'Compartir pantalla',
	ShareScreenNotSupported: 'Tu dispositivo/navegador no admite la función de compartir pantalla',
	ShareScreenWithId: 'Pantalla {screenId}',
	ShareTemplateAsPublicFormModalDescription: 'Permitir que otros vean esta plantilla y la envíen como un formulario.',
	ShareTemplateAsPublicFormModalTitle: 'Compartir enlace para ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Configuración del formulario público actualizada correctamente',
	ShareTemplateAsPublicFormSectionCustomization: 'Personalización',
	ShareTemplateAsPublicFormShowPoweredBy: 'Mostrar "Powered by Carepatron" en mi formulario',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Mostrar/ocultar “Powered by Carepatron” en mi formulario',
	ShareTemplateAsPublicFormTrigger: 'Compartir',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Use workspace branding',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Mostrar/ocultar la marca del espacio de trabajo',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Envía código para clientes existentes y no existentes',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Las firmas siempre requieren que el correo electrónico se verifique',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Sólo envía código para clientes existentes',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Nunca envía código',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Seleccionar 'Nunca' puede permitir que usuarios no verificados sobrescriban los datos del cliente si utilizan la dirección de correo electrónico de un cliente existente.`,
	ShareWithCommunity: 'Compartir con la comunidad',
	ShareYourReferralLink: 'Comparte tu enlace de referencia',
	ShareYourScreen: 'Compartir tu pantalla',
	SheHer: 'Ella/Ella',
	ShortTextAnswer: 'Respuesta de texto corto',
	ShortTextFormPrimaryText: 'Texto corto',
	ShortTextFormSecondaryText: 'Respuesta de menos de 300 caracteres',
	Show: 'Mostrar',
	ShowColumn: 'Mostrar columna',
	ShowColumnButton: 'Mostrar columna {value} botón',
	ShowColumns: 'Mostrar columnas',
	ShowColumnsMenu: 'Mostrar menú de columnas',
	ShowDateDurationDescription: 'ej. 29 años',
	ShowDateDurationLabel: 'Mostrar duración de la fecha',
	ShowDetails: 'Mostrar detalles',
	ShowField: 'Mostrar campo',
	ShowFullAddress: 'Mostrar dirección',
	ShowHideFields: 'Mostrar / Ocultar campos',
	ShowIcons: 'Mostrar iconos',
	ShowLess: 'Mostrar menos',
	ShowMeetingTimers: 'Mostrar cronómetros',
	ShowMenu: 'Muestrame el menu',
	ShowMergeSummarySidebar: 'Mostrar resumen de fusión',
	ShowMore: 'Mostrar más',
	ShowOnTranscript: 'Mostrar en transcripción',
	ShowReactions: 'Mostrar reacciones',
	ShowSection: 'Mostrar sección',
	ShowServiceCode: 'Mostrar código de servicio',
	ShowServiceDescription: 'Mostrar descripción en las reservas de servicios',
	ShowServiceDescriptionDesc: 'Los clientes pueden ver las descripciones de los servicios al hacer la reserva.',
	ShowServiceGroups: 'Mostrar colecciones',
	ShowServiceGroupsDesc: 'Los clientes verán los servicios agrupados por colección al reservar',
	ShowSpeakers: 'Mostrar oradores',
	ShowTax: 'Mostrar impuesto',
	ShowTimestamp: 'Mostrar marca de tiempo',
	ShowUnits: 'Mostrar unidades',
	ShowWeekends: 'Mostrar fines de semana',
	ShowYourView: 'Muestra tu punto de vista',
	SignInWithApple: 'Iniciar sesión con Apple',
	SignInWithGoogle: 'Iniciar sesión con Google',
	SignInWithMicrosoft: 'Iniciar sesión con Microsoft',
	SignUpTitleReferralDefault: '<mark>Regístrate</mark> y reclama tu recompensa por referido',
	SignUpTitleReferralUpgrade:
		'Comience su mes de {durationInMonths} ​​<mark>{percentOff, select, 100 {gratis} other {{percentOff}% de descuento}} actualización</mark>',
	SignatureCaptureError: 'No se pudo capturar la firma. Por favor, inténtelo de nuevo.',
	SignatureFormPrimaryText: 'Firma',
	SignatureFormSecondaryText: 'Obtenga una firma digital',
	SignatureInfoTooltip: 'Esta representación visual no es una firma electrónica válida.',
	SignaturePlaceholder: 'Dibuje su firma aquí',
	SignedBy: 'Firmado por',
	Signup: 'Inscribirse',
	SignupAgreements: 'Estoy de acuerdo con los {termsOfUse} y la {privacyStatement} para mi cuenta.',
	SignupBAA: 'Acuerdo de asociación comercial',
	SignupBusinessAgreements:
		'En mi nombre y en nombre de la empresa, acepto el {businessAssociateAgreement}, los {termsOfUse} y la {privacyStatement} para mi cuenta.',
	SignupInvitationForYou: 'Has sido invitado a usar Carepatron.',
	SignupPageProviderWarning:
		'Si su administrador ya ha creado una cuenta, debe pedirle que lo invite a ese proveedor. No utilice este formulario de registro. Para obtener más información, consulte',
	SignupPageProviderWarningLink: 'este enlace.',
	SignupPrivacy: 'Política de privacidad',
	SignupProfession: '¿Cuál es tu profesión?',
	SignupSubtitle:
		'El software de gestión de prácticas de Carepatron está hecho para profesionales solitarios y equipos. Deje de pagar tarifas excesivas y sea parte de la revolución.',
	SignupSuccessDescription:
		'Confirme su dirección de correo electrónico para comenzar su incorporación. Si no lo recibe de inmediato, revise su carpeta de correo no deseado.',
	SignupSuccessTitle: 'Por favor revise su correo electrónico',
	SignupTermsOfUse: 'Términos de uso',
	SignupTitleClient: '<mark>Gestione su salud</mark> desde un solo lugar',
	SignupTitleLast: '¡y todo el trabajo que realiza! - Es gratis',
	SignupTitleOne: '<mark>Potenciando a usted</mark>, ',
	SignupTitleThree: '<mark>Potenciando a sus clientes</mark>, ',
	SignupTitleTwo: '<mark>Potenciando a su equipo</mark>, ',
	Simple: 'Simple',
	SimplifyBillToDetails: 'Simplificar los detalles de facturación',
	SimplifyBillToHelperText: 'Solo se utiliza la primera línea cuando coincide con el cliente',
	Singapore: 'Singapur',
	Single: 'Sencillo',
	SingleChoiceFormPrimaryText: 'Opción única',
	SingleChoiceFormSecondaryText: 'Elija solo una opción',
	Sister: 'Hermana',
	SisterInLaw: 'Cuñada',
	Skip: 'Omitir',
	SkipLogin: 'Omitir inicio de sesión',
	SlightBlur: 'Desenfoca ligeramente tu fondo',
	Small: 'Pequeño',
	SmartChips: 'Chips inteligentes',
	SmartDataChips: 'Chips de datos inteligentes',
	SmartReply: 'Respuesta inteligente',
	SmartSuggestNewClient: '<strong>Smart Suggest</strong> crea {name} como un nuevo cliente',
	SmartSuggestedFieldDescription: 'Este campo es una sugerencia inteligente',
	SocialSecurityNumber: 'Número de seguro social',
	SocialWork: 'Trabajo social',
	SocialWorker: 'Trabajador social',
	SoftwareDeveloper: 'Desarrollador de software',
	Solo: 'Solo',
	Someone: 'Alguien',
	Son: 'Hijo',
	SortBy: 'Ordenar por',
	SouthAmerica: 'Sudamérica',
	Speaker: 'Vocero',
	SpeakerSource: 'Fuente del altavoz',
	Speakers: 'Altavoces',
	SpecifyPaymentMethod: 'Especificar método de pago',
	SpeechLanguagePathology: 'Logopedia',
	SpeechTherapist: 'Logopeda',
	SpeechTherapists: 'Terapeutas del habla',
	SpeechTherapy: 'Terapia del Habla',
	SportsMedicinePhysician: 'Médico deportivo',
	Spouse: 'Cónyuge',
	SpreadsheetColumnExample: 'por ejemplo ',
	SpreadsheetColumns: 'Columnas de la hoja de cálculo',
	SpreadsheetUploaded: 'Hoja de cálculo cargada',
	SpreadsheetUploading: 'Cargando...',
	Staff: 'Personal',
	StaffAccessDescriptionAdmin: 'Los administradores pueden administrar todo en la plataforma.',
	StaffAccessDescriptionStaff:
		'Los miembros del personal pueden administrar clientes, notas y documentación que hayan creado o que se les haya compartido, programar citas y gestionar facturas.',
	StaffContactAssignedSubject:
		'{actorProfileName} ha asignado {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} y {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { y 1 otro cliente} other { y # otros clientes}}',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} ha compartido la bandeja de entrada {inboxName} contigo',
	StaffInboxUnassignedNotificationSubject:
		'{actorProfileName} ha eliminado tu acceso a la bandeja de entrada {inboxName}',
	StaffMembers: 'Miembros del personal',
	StaffMembersNumber: '{billedUsers, plural, one {# miembro del equipo} other {# miembros del equipo}}',
	StaffSavedSuccessSnackbar: '¡Información de los miembros del equipo guardada exitosamente!',
	StaffSelectorAdminRole: 'Administradora',
	StaffSelectorStaffRole: 'Miembro del personal',
	StandardAppointment: 'Cita estándar',
	StandardColor: 'Color de la tarea',
	StartAndEndTime: 'Hora de inicio y fin',
	StartCall: 'Iniciar llamada',
	StartDate: 'Fecha de inicio',
	StartDictating: 'Empieza a dictar',
	StartImport: 'Comenzar importación',
	StartRecordErrorTitle: 'Se produjo un error al iniciar la grabación.',
	StartRecording: 'Iniciar grabación',
	StartTimeIncrements: 'Incrementos de hora de inicio',
	StartTimeIncrementsView: 'Intervalos de {startTimeIncrements} minutos',
	StartTranscribing: 'Empezar a transcribir',
	StartTranscribingNotes: 'Comienza a transcribir tus notas de audio y video en texto.',
	StartTranscription: 'Comenzar transcripción',
	StartVideoCall: 'Iniciar llamada de video',
	StartWeekOn: 'Iniciar semana en',
	StartedBy: 'Iniciado por ',
	Starter: 'Inicio',
	State: 'Estado',
	StateIndustrialAccidentProviderNumber: 'Número de proveedor de accidentes laborales del estado',
	StateLicenseNumber: 'Número de licencia estatal',
	Statement: 'Declaración',
	StatementDescriptor: 'Descriptor de la factura',
	StatementDescriptorToolTip:
		'El descriptor del extracto se muestra en los estados de cuenta bancarios o de tarjetas de crédito de sus clientes. Debe tener entre 5 y 22 caracteres y reflejar el nombre de su empresa.',
	StatementNumber: 'N° de declaración',
	Status: 'Estado',
	StatusFieldPlaceholder: 'Introduzca una etiqueta de estado',
	StepFather: 'Padrastro',
	StepMother: 'Madrastra',
	Stockholm: 'Estocolmo',
	StopIgnoreSendersDescription:
		'¿Está seguro de que desea dejar de ignorar a estos remitentes? Las conversaciones futuras de estos remitentes se moverán automáticamente a "Otros".',
	StopIgnoring: 'Dejar de ignorar',
	StopIgnoringSenders: 'Dejar de ignorar remitentes',
	StopIgnoringSendersSuccess: 'Se dejó de ignorar la dirección de correo electrónico <mark>{addresses}</mark>',
	StopSharing: 'Dejar de compartir',
	StopSharingLabel: 'carepatron.com está compartiendo tu pantalla.',
	Storage: 'Almacenamiento',
	StorageAlmostFullDescription: '🚀 Actualice ahora para mantener su cuenta funcionando sin problemas.',
	StorageAlmostFullTitle: '¡Has usado {percentage}% de tu límite de almacenamiento de espacio de trabajo!',
	StorageFullDescription: 'Obtenga más almacenamiento actualizando su plan.',
	StorageFullTitle: 'Su almacenamiento está lleno.',
	Street: 'Calle',
	StripeAccountNotCompleteErrorCode:
		'Los pagos en línea no están {hasProviderName, select, true {configurado para {providerName}} other {disponible para este proveedor}}.',
	StripeAccountRejectedError: 'La cuenta de Stripe ha sido rechazada. Comuníquese con el servicio de asistencia.',
	StripeBalance: 'Saldo Stripe',
	StripeChargesInfoToolTip: 'Permite cobrar tarjetas de débito y crédito',
	StripeFeesDescription:
		'Carepatron utiliza Stripe para que te paguen rápidamente y mantener tu información de pago segura. Los métodos de pago disponibles varían según la región, se aceptan todas las principales tarjetas de débito y crédito.',
	StripeFeesDescriptionItem1: 'Se aplican tarifas de procesamiento a cada transacción exitosa, puedes {link}.',
	StripeFeesDescriptionItem2: 'Los pagos se realizan diariamente pero se retienen hasta por 4 días.',
	StripeFeesLinkToRatesText: 'ver nuestras tarifas aquí',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Lo siento, se requiere un mínimo de {minimumAmount} para las facturas que utilizan pagos en línea',
	StripePaymentsDisabled: 'Pagos en línea deshabilitados. Por favor, verifica la configuración de tu pago.',
	StripePaymentsUnavailable: 'Pagos no disponibles',
	StripePaymentsUnavailableDescription:
		'Se ha producido un error al cargar los pagos. Por favor, inténtelo de nuevo más tarde.',
	StripePayoutsInfoToolTip: 'Permite recibir pagos en tu cuenta bancaria',
	StyleYourWorkspace: '<mark>Estiliza</mark> tu espacio de trabajo',
	StyleYourWorkspaceDescription1:
		'Hemos recuperado los recursos de marca de tu sitio web. Siéntete libre de editarlos o continúa a tu espacio de trabajo Carepatron',
	StyleYourWorkspaceDescription2:
		'Utilice sus activos de marca para personalizar las facturas y las reservas en línea para una experiencia de cliente perfecta',
	SubAdvanced: 'Avanzado',
	SubEssential: 'Esencial',
	SubOrganization: 'Organización',
	SubPlus: 'Más',
	SubProfessional: 'Profesional',
	Subject: 'Asunto',
	Submit: 'Enviar',
	SubmitElectronically: 'Enviar electrónicamente',
	SubmitFeedback: 'Enviar comentarios',
	SubmitFormValidationError:
		'Por favor, asegúrese de que todos los campos requeridos estén llenados correctamente e intente enviar nuevamente.',
	Submitted: 'Enviado',
	SubmittedDate: 'Fecha de envío',
	SubscribePerMonth: 'Suscribirse a {price} {isMonthly, select, true {por mes} other {por año}}',
	SubscriptionDiscountDescription:
		'{percentOff}% de descuento {months, select, null { } other { {months, plural, one {durante # mes} other {durante # meses}}}}',
	SubscriptionFreeTrialDescription: 'Gratis hasta {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'No pudimos completar el pago de su suscripción. Por favor verifique sus datos de pago',
	SubscriptionPlanDetailsHeader: 'Por usuario/mensual facturado anualmente',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} facturado mensualmente (USD)',
	SubscriptionPlans: 'Planes de suscripción',
	SubscriptionPlansDescription:
		'Actualice su plan para desbloquear beneficios adicionales y mantener su práctica funcionando sin problemas.',
	SubscriptionPlansDescriptionNoPermission:
		'Parece que no tienes acceso a la actualización en este momento. Ponte en contacto con tu administrador para obtener ayuda.',
	SubscriptionSettings: 'Configuración de suscripción',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> de almacenamiento usado',
	SubscriptionSettingsStorageUsed: '{used} de {limit} usado',
	SubscriptionSettingsUnlimitedStorage: 'Almacenamiento ilimitado disponible',
	SubscriptionSummary: 'Resumen de suscripción',
	SubscriptionUnavailableOverStorageLimit: 'Su uso actual excede el límite de almacenamiento de este plan.',
	SubscriptionUnpaidBannerButton: 'Ir a suscripciones',
	SubscriptionUnpaidBannerDescription: 'Comprueba que tus datos de pago son correctos e inténtalo de nuevo',
	SubscriptionUnpaidBannerTitle: 'No hemos podido completar el pago de su suscripción.',
	Subscriptions: 'Suscripciones',
	SubscriptionsAndPayments: 'Suscripciones y pagos',
	Subtotal: 'Subtotal',
	SuburbOrProvince: 'Suburbio/Provincia',
	SuburbOrState: 'Barrio/Estado',
	SuccessSavedNoteChanges: 'Cambios de nota guardados con éxito',
	SuccessShareDocument: 'Documento compartido con éxito',
	SuccessShareNote: 'Nota compartida con éxito',
	SuccessfullyCreatedValue: 'Creado con éxito {value}',
	SuccessfullyDeletedTranscriptionPart: 'Se eliminó correctamente la parte de la transcripción',
	SuccessfullyDeletedValue: 'Eliminado exitosamente {value}',
	SuccessfullySubmitted: 'Enviado con éxito',
	SuccessfullyUpdatedClientSettings: 'Configuración del cliente actualizada con éxito',
	SuccessfullyUpdatedTranscriptionPart: 'Parte de transcripción actualizada con éxito',
	SuccessfullyUpdatedValue: 'Actualizado exitosamente {value}',
	SuggestedAIPoweredTemplates: 'Plantillas sugeridas con tecnología de IA',
	SuggestedAITemplates: 'Plantillas de IA sugeridas',
	SuggestedActions: 'Acciones sugeridas',
	SuggestedLocations: 'Sugerencias de ubicaciones',
	Suggestions: 'Sugerencias',
	Summarise: 'AI resuma',
	SummarisingContent: 'Resumiendo {title}',
	Sunday: 'Domingo',
	Superbill: 'Superfactura',
	SuperbillAndInsuranceBilling: 'Superbill y Facturación de seguros',
	SuperbillAutomationMonthly: 'Activo • Último día del mes',
	SuperbillAutomationNoEmail:
		'Para enviar documentos de facturación automáticamente con éxito, agregue una dirección de correo electrónico para este cliente',
	SuperbillAutomationNotActive: 'No activo',
	SuperbillAutomationUpdateFailure: 'Error al actualizar la configuración de automatización de Superbill',
	SuperbillAutomationUpdateSuccess: 'Configuración de automatización de Superbill actualizada correctamente',
	SuperbillClientHelperText: 'Esta información se prepopula a partir de los detalles del cliente',
	SuperbillNotFoundDescription:
		'Por favor, póngase en contacto con su proveedor y pídale más información o que reenvíe el superbill.',
	SuperbillNotFoundTitle: 'Superbill no encontrado',
	SuperbillNumber: 'Superfactura #{number}',
	SuperbillNumberAlreadyExists: 'El número de recibo de Superbill ya existe',
	SuperbillPracticeHelperText:
		'Esta información se prepopula a partir de la configuración de facturación de la práctica',
	SuperbillProviderHelperText: 'Esta información se prepopula a partir de los detalles del personal',
	SuperbillReceipts: 'Recibos de superbill',
	SuperbillsEmptyStateDescription: 'No se han encontrado superfacturas.',
	Surgeon: 'Cirujano',
	Surgeons: 'Cirujanos',
	SurgicalTechnologist: 'Tecnólogo quirúrgico',
	SwitchFromAnotherPlatform: 'Estoy cambiando de otra plataforma',
	SwitchToMyPortal: 'Cambiar a Mi portal',
	SwitchToMyPortalTooltip: `Acceda a su propio portal personal,
 lo que le permitirá explorar la experiencia
 del portal de su cliente.`,
	SwitchWorkspace: 'Cambiar espacio de trabajo',
	SwitchingToADifferentPlatform: 'Cambiando a una plataforma diferente',
	Sydney: 'Sídney',
	SyncCalendar: 'Sincronizar calendario',
	SyncCalendarModalDescription: `Les autres membres de l'équipe ne pourront pas voir vos calendriers synchronisés. Les rendez-vous avec les clients ne peuvent être mis à jour ou supprimés que depuis l'intérieur de Carepatron.`,
	SyncCalendarModalDisplayCalendar: 'Mostrar mi calendario en Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Sincronizar mi calendario con Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Sincronizar citas de Carepatron con mi calendario',
	SyncCarepatronAppointmentsWithMyCalendar: 'Sincronizar citas de Carepatron con mi calendario',
	SyncGoogleCalendar: 'Sincronizar el calendario de Google',
	SyncInbox: 'Sync inbox with Carepatron',
	SyncMyCalendarToCarepatron: 'Sincronizar mi calendario con Carepatron',
	SyncOutlookCalendar: 'Sincronizar el calendario de Outlook',
	SyncedFromExternalCalendar: 'Sincronizado desde el calendario externo',
	SyncingCalendarName: 'Sincronizando el calendario {calendarName}',
	SyncingFailed: 'Error al sincronizar',
	SystemGenerated: 'Generado por el sistema',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Mesa',
	TableRowLabel: 'Fila de tabla para {value}',
	TagSelectorNoOptionsText: 'Haz clic en "crear nuevo" para agregar una nueva etiqueta',
	Tags: 'Etiquetas',
	TagsInputPlaceholder: 'Buscar o crear etiquetas',
	Task: 'Tarea',
	TaskAttendeeStatusUpdatedSuccess: 'Estados de las citas actualizados correctamente',
	Tasks: 'Tareas',
	Tax: 'Impuesto',
	TaxAmount: 'Importe del Impuesto',
	TaxID: 'ID fiscal',
	TaxIdType: 'Tipo de identificación fiscal',
	TaxName: 'Nombre del impuesto',
	TaxNumber: 'Número de impuesto',
	TaxNumberType: 'Tipo de número de impuestos',
	TaxNumberTypeInvalid: '{type} no es válido',
	TaxPercentageOfAmount: '{taxName} ({percentage}% de {amount})',
	TaxRate: 'Tasa impositiva',
	TaxRatesDescription: 'Administra las tasas de impuestos que se aplicarán a los elementos de línea de tu factura.',
	Taxable: 'Imponible',
	TaxonomyCode: 'Código de taxonomía',
	TeacherAssistant: 'Ayudante de profesor',
	Team: 'Equipo',
	TeamMember: 'Miembro del equipo',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {está} other {están}} ya reservado a esta hora.{br}Elige una nueva hora para evitar una doble reserva.',
	TeamMembers: 'Miembros del equipo',
	TeamMembersColour: 'Farbe der Teammitglieder',
	TeamMembersDetails: 'Detalles de los miembros del equipo',
	TeamSize: '¿Cuántas personas hay en su equipo?',
	TeamTemplates: 'Plantillas de equipo',
	TeamTemplatesSectionDescription: 'Creado por ti y tu equipo',
	TelehealthAndVideoCalls: 'Telemedicina y videollamadas',
	TelehealthProvidedOtherThanInPatientCare:
		'Telesalud proporcionada para otros fines que no sean la atención hospitalaria',
	TelehealthVideoCall: 'Llamada de video telehealth',
	Template: 'Plantilla',
	TemplateDescription: 'Descripción de la plantilla',
	TemplateDetails: 'Detalles de la plantilla',
	TemplateEditModeViewSwitcherDescription: 'Crear y editar plantilla',
	TemplateGallery: 'Plantillas de la comunidad',
	TemplateImportCompletedNotificationSubject:
		'¡Importación de plantilla completada! {templateTitle} está listo para usarse.',
	TemplateImportFailedNotificationSubject: 'Falló la importación del archivo {fileName}.',
	TemplateName: 'Nombre de la plantilla',
	TemplateNotFound: 'La plantilla no se pudo encontrar.',
	TemplatePreviewErrorMessage: 'Se ha producido un error al cargar la vista previa de la plantilla',
	TemplateResponderModeViewSwitcherDescription: 'Obtenga una vista previa e interactúe con los formularios',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Compruebe cómo aparecen sus formularios cuando los completan los encuestados',
	TemplateSaved: 'Cambios guardados',
	TemplateTitle: 'Título de la plantilla',
	TemplateType: 'Tipo de plantilla',
	Templates: 'Plantillas',
	TemplatesCategoriesFilter: 'Filtrar por categoría',
	TemplatesPublicTemplatesFilter: 'Filtrar por Comunidad/Equipo',
	Text: 'Texto',
	TextAlign: 'Alineación del texto',
	TextColor: 'Color del texto',
	ThankYouForYourFeedback: '¡Gracias por tus comentarios!',
	ThanksForLettingKnow: 'Gracias por hacérnoslo saber.',
	ThePaymentMethod: 'El método de pago',
	ThemThey: 'Ellos/Ellos',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Más temas',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Oscuro',
	ThemeColorLightMode: 'Luz',
	ThemeColorModePickerTitle: 'Modo de color',
	ThemeColorSystemMode: 'Sistema',
	ThemeCpColorPickerTitle: 'Temas de Carepatron',
	ThemePanelDescription: 'Elige entre el modo claro y oscuro, y personaliza tus preferencias de tema',
	ThemePanelTitle: 'Apariencia',
	Then: 'Entonces',
	Therapist: 'Terapeuta',
	Therapists: 'Terapeutas',
	Therapy: 'Terapia',
	Thick: 'Denso',
	Thin: 'Ligero',
	ThirdPerson: '3rd person',
	ThisAndFollowingAppointments: 'Esta y las siguientes citas',
	ThisAndFollowingMeetings: 'Esta y las siguientes reuniones',
	ThisAndFollowingReminders: 'Este y los siguientes recordatorios',
	ThisAndFollowingTasks: 'Esta y las siguientes tareas',
	ThisAppointment: 'Esta cita',
	ThisMeeting: 'Esta reunión',
	ThisMonth: 'Este mes',
	ThisPerson: 'Esta persona',
	ThisReminder: 'Este recordatorio',
	ThisTask: 'Esta tarea',
	ThisWeek: 'Esta semana',
	ThreeDay: '3 Día',
	Thursday: 'Jueves',
	Time: 'Tiempo',
	TimeAgoDays: '{number}d',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{minutes, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Formato de tiempo',
	TimeIncrement: 'Incremento de tiempo',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Tamaño del intervalo de tiempo',
	Timestamp: 'Marca de tiempo',
	Timezone: 'Zona horaria',
	TimezoneDisplay: 'Visualización de zona horaria',
	TimezoneDisplayDescription: 'Administra la configuración de visualización de tu zona horaria.',
	Title: 'Título',
	To: 'Para',
	ToYourWorkspace: 'en tu espacio de trabajo',
	Today: 'Hoy',
	TodayInHoursPlural: 'Hoy en {count} {count, plural, one {hora} other {horas}}',
	TodayInMinsAbbreviated: 'Hoy en {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Alternar celda de encabezado',
	ToggleHeaderCol: 'Alternar columna de encabezado',
	ToggleHeaderRow: 'Alternar fila de encabezado',
	Tokyo: 'Tokio',
	Tomorrow: 'Mañana',
	TomorrowAfternoon: 'Mañana por la tarde',
	TomorrowMorning: 'Mañana por la mañana',
	TooExpensive: 'Demasiado caro',
	TooHardToSetUp: 'Demasiado difícil de configurar',
	TooManyFiles: 'Se detectó más de 1 archivo.',
	ToolsExample: 'Ejemplo: SimplePractice, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Total',
	TotalAccountCredit: 'Crédito total de la cuenta',
	TotalAdjustments: 'Total de ajustes',
	TotalAmountToCreditInCurrency: 'Importe total a acreditar ({currency})',
	TotalBilled: 'Total facturado',
	TotalConversations: '{total} {total, plural, =0 {conversación} one {conversación} other {conversacións}}',
	TotalOverdue: 'Total vencido',
	TotalOverdueTooltip:
		'El saldo total vencido incluye todas las facturas impagas, independientemente del rango de fechas, que no están anuladas ni procesadas.',
	TotalPaid: 'Total pagado',
	TotalPaidTooltip:
		'El saldo total pagado incluye todas las cantidades de facturas que se han pagado dentro del rango de fechas especificado.',
	TotalUnpaid: 'Total sin pagar',
	TotalUnpaidTooltip:
		'El saldo total no pagado incluye todos los montos pendientes de facturas procesadas, no pagadas y enviadas que vencen dentro del rango de fechas especificado.',
	TotalWorkflows: '{count} {count, plural, one {flujo de trabajo} other {flujos de trabajo}}',
	TotpSetUpManualEntryInstruction:
		'Alternativamente, puede ingresar manualmente el código a continuación en la aplicación:',
	TotpSetUpModalDescription:
		'Escanee el código QR con su aplicación de autenticación para configurar la autenticación multifactor.',
	TotpSetUpModalTitle: 'Configurar dispositivo MFA',
	TotpSetUpSuccess: '¡Ya está todo listo! Se ha habilitado la autenticación multifactor.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Ingresa el código generado por tu aplicación de autenticación',
	Transcribe: 'Transcribir',
	TranscribeLanguageSelector: 'Seleccionar idioma de entrada',
	TranscribeLiveAudio: 'Transcribir audio en vivo',
	Transcribing: 'Transcribiendo audio...',
	TranscribingIn: 'Transcripción en',
	Transcript: 'Transcripción',
	TranscriptRecordingCompleteInfo: 'Verá su transcripción aquí una vez que se haya completado la grabación.',
	TranscriptSuccessSnackbar: 'Transcripción procesada exitosamente.',
	Transcription: 'Transcripción',
	TranscriptionEmpty: 'No hay transcripción disponible',
	TranscriptionEmptyHelperMessage: 'Esta transcripción no detectó nada. Reiníciela y vuelva a intentarlo.',
	TranscriptionFailedNotice: 'Esta transcripción no se procesó correctamente',
	TranscriptionIdleMessage:
		'No estamos escuchando ningún audio. Si necesitas más tiempo, por favor responde dentro de {timeValue} segundos, o la sesión terminará.',
	TranscriptionInProcess: 'Transcripción en proceso...',
	TranscriptionIncompleteNotice: 'Algunas partes de esta transcripción no se procesaron correctamente',
	TranscriptionOvertimeWarning: '{scribeType} sesión termina en <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: '¿Está seguro de que desea eliminar esta parte de la transcripción?',
	TranscriptionText: 'Voz a texto',
	TranscriptsPending: 'Su transcripción estará disponible aquí después de que finalice la sesión.',
	Transfer: 'Transferir',
	TransferAndDelete: 'Transferir y borrar',
	TransferOwnership: 'Transferir propiedad',
	TransferOwnershipConfirmationModalDescription:
		'Esta acción solo se puede deshacer si transfieren la propiedad de nuevo a usted.',
	TransferOwnershipDescription: 'Transfiera la propiedad de este espacio de trabajo a otro miembro del equipo.',
	TransferOwnershipSuccessSnackbar: '¡Propiedad transferida correctamente!',
	TransferOwnershipToMember: '¿Está seguro de que desea transferir este espacio de trabajo a {staff}?',
	TransferStatusAlert:
		'La eliminación {numberOfStatuses, plural, one {de este estado} other {de estos estados}} afectará {numberOfAffectedRecords, plural, one {<strong>a {numberOfAffectedRecords} estado de cliente.</strong>} other {<strong>a {numberOfAffectedRecords} estados de cliente.</strong>}}',
	TransferStatusDescription:
		'Elija otro estado para estos clientes antes de proceder a la eliminación. Esta acción no se puede deshacer',
	TransferStatusLabel: 'Transferir a nuevo estado',
	TransferStatusPlaceholder: 'Elige un estado existente',
	TransferStatusTitle: 'Estado de la transferencia antes de la eliminación',
	TransferTaskAttendeeStatusAlert:
		'Eliminar este estado afectará a <strong>{number} {number, plural, one {estado} other {estados}} futuras</strong>.',
	TransferTaskAttendeeStatusDescription:
		'Elige otro estado para estos clientes antes de continuar con la eliminación. Esta acción no se puede deshacer.',
	TransferTaskAttendeeStatusSubtitle: 'Estado de la cita',
	TransferTaskAttendeeStatusTitle: 'Estado de transferencia antes de la eliminación',
	Trash: 'Papelera',
	TrashDeleteItemsModalConfirm: 'Para confirmar, escriba {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Los siguientes {count, plural, one {elemento} other {elementos}} se eliminarán permanentemente y no se podrán restaurar.',
	TrashDeleteItemsModalTitle: 'Eliminar {count, plural, one {elemento} other {elementos}} para siempre',
	TrashDeletedAllItems: 'Eliminado todos los elementos',
	TrashDeletedItems: 'Eliminado {count, plural, one {elemento} other {elementos}}',
	TrashDeletedItemsFailure: 'No se pudo eliminar los elementos de la papelera',
	TrashLocationAppointmentType: 'Calendario',
	TrashLocationBillingAndPaymentsType: 'Facturación y pagos',
	TrashLocationContactType: 'Clientes',
	TrashLocationNoteType: 'Notas y documentos',
	TrashRestoreItemsModalDescription: 'Se restaurarán los siguientes {count, plural, one {item} other {items}}.',
	TrashRestoreItemsModalTitle: 'Restaurar {count, plural, one {elemento} other {elementos}}',
	TrashRestoredAllItems: 'Restaurado todos los elementos',
	TrashRestoredItems: 'Restaurado {count, plural, one {elemento} other {elementos}}',
	TrashRestoredItemsFailure: 'No se pudo restaurar los elementos de la papelera',
	TrashSuccessfullyDeletedItem: 'Eliminado correctamente {type}',
	Trigger: 'Desencadenar',
	Troubleshoot: 'Solucionar problemas',
	TryAgain: 'Inténtalo de nuevo',
	Tuesday: 'Martes',
	TwoToTen: '2 - 10',
	Type: 'Tipo',
	TypeHere: 'Escriba aquí...',
	TypeToConfirm: 'Para confirmar, escribe {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Encabezado 1',
	TypographyHeading2: 'Encabezado 2',
	TypographyHeading3: 'Encabezado 3',
	TypographyHeading4: 'Encabezado 4',
	TypographyHeading5: 'Encabezado 5',
	TypographyP: 'P',
	TypographyParagraph: 'Párrafo',
	UnableToCompleteAction: 'No se puede completar la acción.',
	UnableToPrintDocument: 'No se puede imprimir el documento. Por favor, inténtelo de nuevo más tarde.',
	Unallocated: 'Sin asignar',
	UnallocatedPaymentDescription: `Este pago no se ha asignado en su totalidad a los artículos facturables.
 Agregue una asignación a los artículos no pagados o emita un crédito o reembolso.`,
	UnallocatedPaymentTitle: 'Pago no asignado',
	UnallocatedPayments: 'Pagos no asignados',
	Unarchive: 'Desarchivar',
	Unassigned: 'Sin asignar',
	UnauthorisedInvoiceSnackbar: 'No tienes acceso para administrar facturas de este cliente.',
	UnauthorisedSnackbar: 'No tienes permiso para hacer esto.',
	Unavailable: 'No disponible',
	Uncategorized: 'No clasificado',
	Unclaimed: 'No reclamado',
	UnclaimedAmount: 'Cantidad no reclamada',
	UnclaimedItems: 'Artículos no reclamados',
	UnclaimedItemsMustBeInCurrency: 'Solo se admiten artículos en las siguientes monedas: {currencies}',
	Uncle: 'Tío',
	Unconfirmed: 'Sin confirmar',
	Underline: 'Subrayado',
	Undo: 'Deshacer',
	Unfavorite: 'Quitar de favoritos',
	Uninvoiced: 'Sin facturar',
	UninvoicedAmount: 'Importe no facturado',
	UninvoicedAmounts:
		'{count, plural, =0 {No cantidades sin facturar} one {Cantidad sin facturar} other {Cantidades sin facturar}}',
	Unit: 'Unidad',
	UnitedKingdom: 'Reino Unido',
	UnitedStates: 'Estados Unidos',
	UnitedStatesEast: 'Estados Unidos - Este',
	UnitedStatesWest: 'Estados Unidos - Oeste',
	Units: 'Unidades',
	UnitsIsRequired: 'Se requieren unidades',
	UnitsMustBeGreaterThanZero: 'Las unidades deben ser mayores que 0',
	UnitsPlaceholder: '1',
	Unknown: 'Desconocido',
	Unlimited: 'Ilimitado',
	Unlock: 'Desbloquear',
	UnlockNoteHelper: 'Antes de realizar nuevos cambios, los editores deben desbloquear la nota.',
	UnmuteAudio: 'Activar el audio',
	UnmuteEveryone: 'Activar el sonido de todos',
	Unpaid: 'Pendiente de pago',
	UnpaidInvoices: 'Facturas impagas',
	UnpaidItems: 'Artículos no pagados',
	UnpaidMultiple: 'Pendiente de pago',
	Unpublish: 'Despublicar',
	UnpublishTemplateConfirmationModalPrompt:
		'Eliminar <span>{title}</span> eliminará este recurso de la comunidad de Carepatron. Esta acción no se puede deshacer.',
	UnpublishToCommunitySuccessMessage: 'Se eliminó con éxito ‛{title}’ de la comunidad',
	Unread: 'No leído',
	Unrecognised: 'Desconocido',
	UnrecognisedDescription:
		'Este método de pago no es reconocido por la versión actual de tu aplicación. Por favor, actualiza tu navegador para obtener la última versión y poder ver y editar este método de pago.',
	UnsavedChanges: 'Cambios no guardados',
	UnsavedChangesPromptContent: 'Quieres guardar los cambios antes de cerrar?',
	UnsavedChangesPromptTitle: 'Tienes cambios sin guardar',
	UnsavedNoteChangesWarning: 'Es posible que los cambios que haya realizado no se guarden',
	UnsavedTemplateChangesWarning: 'Es posible que los cambios que haya realizado no se guarden',
	UnselectAll: 'Desmarcar todo',
	Until: 'Hasta',
	UntitledConversation: 'Conversación sin título',
	UntitledFolder: 'Carpeta sin título',
	UntitledNote: 'Nota sin título',
	UntitledSchedule: 'Horario sin título',
	UntitledSection: 'Sección sin título',
	UntitledTemplate: 'Plantilla sin título',
	Unverified: 'Inconfirmado',
	Upcoming: 'Próximas',
	UpcomingAppointments: 'Próximas citas',
	UpcomingDateOverridesEmpty: 'No se han encontrado anulaciones de fecha',
	UpdateAvailabilityScheduleFailure: 'No se pudo actualizar el horario de disponibilidad',
	UpdateAvailabilityScheduleSuccess: 'Horario de disponibilidad actualizado exitosamente',
	UpdateInvoicesOrClaimsAgainstBillable:
		'¿Quieres que los nuevos precios se apliquen a las facturas y reclamaciones de los asistentes?',
	UpdateLink: 'Actualizar enlace',
	UpdatePrimaryEmailWarningDescription:
		'Cambiar la dirección de correo electrónico de tu cliente resultará en la pérdida de acceso a sus citas y notas existentes.',
	UpdatePrimaryEmailWarningTitle: 'Cambio de correo electrónico del cliente',
	UpdateSettings: 'Actualizar configuración',
	UpdateStatus: 'Actualizar estado',
	UpdateSuperbillReceiptFailure: 'Error al actualizar el recibo de Superbill',
	UpdateSuperbillReceiptSuccess: 'Recibo de Superbill actualizado correctamente',
	UpdateTaskBillingDetails: 'Actualizar detalles de facturación',
	UpdateTaskBillingDetailsDescription:
		'El precio de las citas ha cambiado. ¿Quieres que el nuevo precio se aplique a los elementos de facturación, facturas y reclamaciones de los asistentes? Elige las actualizaciones que deseas realizar.',
	UpdateTemplateFolderSuccessMessage: 'Carpeta actualizada correctamente',
	UpdateUnpaidInvoices: 'Actualizar facturas impagadas',
	UpdateUserInfoSuccessSnackbar: '¡Información de usuario actualizada con éxito!',
	UpdateUserSettingsSuccessSnackbar: '¡Configuración de usuario actualizada con éxito!',
	Upgrade: 'Actualizar',
	UpgradeForSMSReminder: 'Actualizar a <b>Profesional<b> para recordatorios SMS ilimitados',
	UpgradeNow: 'Actualizar ahora',
	UpgradePlan: 'Actualizar plan',
	UpgradeSubscriptionAlertDescription:
		'Te estás quedando sin almacenamiento. ¡Actualiza tu plan para desbloquear almacenamiento adicional y mantener tu práctica funcionando sin problemas!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Te estás quedando sin almacenamiento. ¡Pregunta a alguien de tu práctica con <span>acceso de administrador</span> sobre actualizar tu plan para desbloquear almacenamiento adicional y mantener tu práctica funcionando sin problemas!',
	UpgradeSubscriptionAlertTitle: 'Es hora de actualizar su suscripción',
	UpgradeYourPlan: 'Mejora tu plan',
	UploadAudio: 'Subir audio',
	UploadFile: 'Subir archivo',
	UploadFileDescription: '¿De qué plataforma de software está cambiando?',
	UploadFileMaxSizeError: 'El archivo es demasiado grande. El tamaño máximo del archivo es {fileSizeLimit}.',
	UploadFileSizeLimit: 'Límite de tamaño {size}MB',
	UploadFileTileDescription: 'Use CSV, XLS, XLSX, or ZIP files to upload your clients.',
	UploadFileTileLabel: 'Subir un archivo',
	UploadFiles: 'Subir archivos',
	UploadIndividually: 'Subir archivos individualmente',
	UploadLogo: 'Cargar logotipo',
	UploadPhoto: 'Cargar foto',
	UploadToCarepatron: 'Subir a Carepatron',
	UploadYourLogo: 'Sube tu logotipo',
	UploadYourTemplates: 'Sube tus plantillas y nosotros las convertiremos para ti.',
	Uploading: 'Cargando',
	UploadingAudio: 'Subiendo tu audio...',
	UploadingFiles: 'Cargando archivos',
	UrlLink: 'Enlace URL',
	UsageCount: 'Usado {count} veces',
	UsageLimitValue: '{used} de {limit} usados',
	UsageValue: '{used} usado',
	Use: 'Usar',
	UseAiToAutomateYourWorkflow: '¡Utilice IA para automatizar su flujo de trabajo!',
	UseAsDefault: 'Usar como predeterminado',
	UseCustom: 'Usar personalizado',
	UseDefault: 'Usar predeterminado',
	UseDefaultFilters: 'Utilizar filtros por defecto',
	UseTemplate: 'Usar plantilla',
	UseThisCard: 'Usar esta tarjeta',
	UseValue: 'Usar "{value}"',
	UseWorkspaceDefault: 'Usar valores predeterminados del espacio de trabajo',
	UserIsTyping: '{name} está escribiendo...',
	Username: 'Nombre de usuario',
	Users: 'Usuarios',
	VAT: 'IVA',
	ValidUrl: 'El enlace URL debe ser una URL válida.',
	Validate: 'Validar',
	Validated: 'Validado',
	Validating: 'Validando',
	ValidatingContent: 'Validando contenido...',
	ValidatingTranscripts: 'Validando transcripciones...',
	ValidationConfirmPasswordRequired: 'Se requiere confirmar la contraseña',
	ValidationDateMax: 'Debe ser antes de {max}',
	ValidationDateMin: 'Debe ser después de {min}',
	ValidationDateRange: 'La fecha de inicio y fin son obligatorias',
	ValidationEndDateMustBeAfterStartDate: 'La fecha de finalización debe ser posterior a la fecha de inicio',
	ValidationMixedDefault: 'Esto es inválido',
	ValidationMixedRequired: 'Esto es obligatorio',
	ValidationNumberInteger: 'Debe ser un número entero',
	ValidationNumberMax: 'Debe ser {max} o menos',
	ValidationNumberMin: 'Debe ser {min} o más',
	ValidationPasswordNotMatching: 'Las contraseñas no coinciden',
	ValidationPrimaryAddressIsRequired: 'La dirección es obligatoria cuando se establece como predeterminada',
	ValidationPrimaryPhoneNumberIsRequired: 'Se requiere un número de teléfono cuando se establece como predeterminado',
	ValidationServiceMustBeNotBeFuture: 'El servicio no debe ser del día actual o en el futuro',
	ValidationStringEmail: 'Debe ser un correo electrónico válido',
	ValidationStringMax: 'Debe tener {max} o menos caracteres',
	ValidationStringMin: 'Debe tener {min} o más caracteres',
	ValidationStringPhoneNumber: 'Debe ser un número de teléfono válido',
	ValueMinutes: '{value} minutos',
	VerbosityConcise: 'Conciso',
	VerbosityDetailed: 'Detallado',
	VerbosityStandard: 'Estándar',
	VerbositySuperDetailed: 'Súper detallado',
	VerificationCode: 'Código de verificación',
	VerificationEmailDescription:
		'Por favor, ingrese su dirección de correo electrónico y el código de verificación que le acabamos de enviar.',
	VerificationEmailSubtitle: 'Revisa la carpeta de spam - si el correo electrónico no ha llegado',
	VerificationEmailTitle: 'Verificar correo electrónico',
	VerificationOption: 'Verificación de correo electrónico',
	Verified: 'Verificado',
	Verify: 'Verificar',
	VerifyAndSubmit: 'Verificar y enviar',
	VerifyEmail: 'Verificar correo electrónico',
	VerifyEmailAccessCode: 'Código de confirmación',
	VerifyEmailAddress: 'Verificar dirección de correo electrónico',
	VerifyEmailButton: 'Verificar y cerrar sesión',
	VerifyEmailSentSnackbar: 'Correo electrónico de verificación enviado. Revisa tu bandeja de entrada.',
	VerifyEmailSubTitle: 'Verifique en la carpeta de correo no deseado si el correo electrónico no ha llegado',
	VerifyEmailSuccessLogOutSnackbar: '¡Éxito! Por favor, cierre sesión para aplicar los cambios.',
	VerifyEmailSuccessSnackbar:
		'¡Éxito! Correo electrónico verificado. Por favor, inicie sesión para continuar como una cuenta verificada.',
	VerifyEmailTitle: 'Verifique su correo electrónico',
	VerifyNow: 'Verificar ahora',
	Veterinarian: 'Veterinario',
	VideoCall: 'Videollamada',
	VideoCallAudioInputFailed: 'Dispositivo de entrada de audio no funciona',
	VideoCallAudioInputFailedMessage:
		'Abre la configuración y verifica si tienes la fuente del micrófono configurada correctamente',
	VideoCallChatBanner:
		'Todos los participantes en esta llamada pueden ver los mensajes y se eliminarán cuando finalice la llamada.',
	VideoCallChatSendBtn: 'Enviar un mensaje',
	VideoCallChatTitle: 'Charlar',
	VideoCallDisconnectedMessage: 'Perdiste tu conexión de red. Intentando reconectarte',
	VideoCallOptionInfo: 'Carepatron gestionará las videollamadas para sus citas si no se ha conectado Zoom',
	VideoCallTilePaused: 'Este video está en pausa debido a problemas con tu red.',
	VideoCallTranscriptionFormDescription: 'Puede ajustar esta configuración en cualquier momento.',
	VideoCallTranscriptionFormHeading: 'Personaliza tu AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'El idioma de salida generado',
	VideoCallTranscriptionFormNoteTemplateField: 'Establecer plantilla de nota predeterminada',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'No se encontraron plantillas con IA',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Elige una plantilla',
	VideoCallTranscriptionPronounField: 'Tu pronombre',
	VideoCallTranscriptionRecordingNote:
		'Al final de la sesión, recibirás una nota <strong>{noteTemplate}</strong> generada y una transcripción.',
	VideoCallTranscriptionReferClientField: 'Refiera a la clienta como',
	VideoCallTranscriptionReferPractitionerField: 'Consulte al practicante como',
	VideoCallTranscriptionTitle: 'AI Scribe',
	VideoCallTranscriptionVerbosityField: 'Verbosidad',
	VideoCallTranscriptionWritingPerspectiveField: 'Perspectiva de la escritura',
	VideoCalls: 'Videollamadas',
	VideoConferencing: 'Videoconferencias',
	VideoOff: 'El video está apagado',
	VideoOn: 'El video está encendido',
	VideoQual360: 'Baja calidad (360p)',
	VideoQual540: 'Calidad media (540p)',
	VideoQual720: 'Alta calidad (720p)',
	View: 'Ver',
	ViewAll: 'Ver todo',
	ViewAppointment: 'Ver cita',
	ViewBy: 'Ver por',
	ViewClaim: 'Ver reclamo',
	ViewCollection: 'Ver colección',
	ViewDetails: 'Ver detalles',
	ViewEnrollment: 'Ver inscripción',
	ViewPayment: 'Ver pago',
	ViewRecord: 'Ver registro',
	ViewRemittanceAdvice: 'Ver el comprobante de pago',
	ViewRemittanceAdviceHeader: 'Recibo de remesa de reclamación',
	ViewRemittanceAdviceSubheader: 'Reclamación {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Ver configuración',
	ViewStripeDashboard: 'Ver panel de control de Stripe',
	ViewTemplate: 'Ver plantilla',
	ViewTemplates: 'Ver plantillas',
	ViewableBy: 'Visible por',
	ViewableByHelper:
		'Usted y el equipo siempre tienen acceso a las notas que publica. Puede elegir compartir esta nota con el cliente y/o sus relaciones',
	Viewer: 'Espectador',
	VirtualLocation: 'Ubicación virtual',
	VisibleTo: 'Visible para',
	VisitOurHelpCentre: 'Visita nuestro centro de ayuda',
	VisualEffects: 'Efectos visuales',
	VoiceFocus: 'Enfoque de voz',
	VoiceFocusLabel: 'Filtra el sonido de tu micrófono que no es habla',
	Void: 'Anulada',
	VoidCancelPriorClaim: 'Anular/Cancelar reclamación anterior',
	WaitingforMins: 'Esperando {count} mins',
	Warning: 'Advertencia',
	WatchAVideo: 'ver un video',
	WatchDemoVideo: 'Ver video de demostración',
	WebConference: 'Conferencia web',
	WebConferenceOrVirtualLocation: 'Conferencia web / ubicación virtual',
	WebDeveloper: 'Desarrollador web',
	WebsiteOptional: 'Sitio web<span>(Opcional)</span>',
	WebsiteUrl: 'URL del sitio web',
	Wednesday: 'Miércoles',
	Week: 'Semana',
	WeekPlural: '{count, plural, one {semana} other {semanas}}',
	Weekly: 'Semanal',
	WeeksPlural: '{age, plural, one {# semana} other {# semanas}}',
	WelcomeBack: 'Bienvenido de nuevo',
	WelcomeBackName: 'Bienvenido de nuevo, {name}',
	WelcomeName: 'Bienvenido/a {name}',
	WelcomeToCarepatron: 'Bienvenido a Carepatron',
	WhatCanIHelpWith: '¿En qué puedo ayudar?',
	WhatDidYouLikeResponse: '¿Qué te gustó de esta respuesta?',
	WhatIsCarepatron: '¿Qué es Carepatron?',
	WhatMadeYouCancel: `¿Qué te hizo cancelar tu plan?
Marca todas las opciones que correspondan.`,
	WhatServicesDoYouOffer: '¿Qué <mark>servicios</mark> ofreces?',
	WhatServicesDoYouOfferDescription: 'Puedes editar o añadir más servicios más tarde.',
	WhatsYourAvailability: '¿Cuál es tu <mark>disponibilidad?</mark>',
	WhatsYourAvailabilityDescription: 'Puedes agregar más horarios más tarde.',
	WhatsYourBusinessName: '¿Cuál es el <mark>nombre de tu negocio</mark>?',
	WhatsYourTeamSize: '¿Cuál es el <mark>tamaño de tu equipo?</mark>',
	WhatsYourTeamSizeDescription: 'Esto nos ayudará a configurar tu espacio de trabajo correctamente.',
	WhenThisHappens: 'Cuando esto sucede:',
	WhichBestDescribesYou: '¿Cuál <mark>te describe</mark> mejor?',
	WhichPlatforms: '¿Qué plataformas?',
	Wife: 'Esposa',
	WorkflowDescription: 'Descripción del flujo de trabajo',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Las plantillas pueden vincularse a flujos de trabajo para procesos más fluidos. Vea los flujos de trabajo vinculados para realizar un seguimiento y actualizarlos fácilmente.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription:
		'Conecta tus SMS + correos electrónicos basados en desencadenantes comunes',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Automatizaciones de flujo de trabajo',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Flujos de trabajo automatizados',
	WorkflowTemplateConfigKey_Body: 'Cuerpo',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Mostrar marca',
	WorkflowTemplateConfigKey_Content: 'Contenido',
	WorkflowTemplateConfigKey_Footer: 'Pie de página',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Mostrar pie de página',
	WorkflowTemplateConfigKey_Header: 'Encabezado',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Mostrar encabezado',
	WorkflowTemplateConfigKey_SecurityFooter: 'Pie de página de seguridad',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Mostrar pie de página de seguridad',
	WorkflowTemplateConfigKey_Subject: 'Asunto',
	WorkflowTemplateConfigKey_Title: 'Título',
	WorkflowTemplateDeleteConfirmationMessage:
		'¿Está seguro de que desea eliminar esta plantilla? Esta acción no se puede deshacer.',
	WorkflowTemplateDeleteConfirmationTitle: 'Eliminar plantilla de notificación',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'¿Estás seguro? Esto eliminará solo la versión {locale} —otros idiomas no se verán afectados. Esta acción no se puede deshacer.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Eliminar la plantilla ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Plantilla de notificación eliminada correctamente',
	WorkflowTemplateEditorDetailsTab: 'Detalles de la plantilla',
	WorkflowTemplateEditorEmailContent: 'Contenido del correo electrónico',
	WorkflowTemplateEditorEmailContentTab: 'Contenido del correo electrónico',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert: 'Las vistas previas usan datos de muestra para mostrar lo que verán tus clientes.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'¿Estás seguro? Esto restablecerá la versión al modelo predeterminado del sistema. Esta acción no se puede deshacer.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Restablecer plantilla',
	WorkflowTemplateSendTestEmail: 'Enviar correo electrónico de prueba',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Prueba la configuración de tu correo electrónico enviándote un correo de prueba a ti mismo.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Correo electrónico del destinatario',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Enviar prueba',
	WorkflowTemplateSendTestEmailDialogTitle: 'Enviar un correo electrónico de prueba',
	WorkflowTemplateSendTestEmailSuccess:
		'¡Éxito! Tu <mark>{templateName}</mark> correo electrónico de prueba se ha enviado.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Administre sus plantillas y agregue versiones en varios idiomas para comunicarse de manera efectiva con los clientes.',
	WorkflowTemplateTemplateEditor: 'Editor de plantillas',
	WorkflowTemplateTranslateLocaleError: 'Algo salió mal al traducir el contenido',
	WorkflowTemplateTranslateLocaleSuccess: 'Traducido correctamente el contenido a **{locale}**',
	WorkflowsAndReminders: 'Flujos de trabajo ',
	WorkflowsManagement: 'Gestión de flujos de trabajo',
	WorksheetAndHandout: 'Hoja de trabajo/folleto',
	WorksheetsAndHandoutsDescription: 'Para el compromiso y la educación del cliente',
	Workspace: 'Espacio de trabajo',
	WorkspaceBranding: 'Marca de espacio de trabajo',
	WorkspaceBrandingDescription:
		'Personaliza tu espacio de trabajo con un estilo cohesivo que refleje tu profesionalismo y personalidad. Personaliza facturas y reservas en línea para una experiencia de cliente hermosa.',
	WorkspaceName: 'Nombre del espacio de trabajo',
	Workspaces: 'Espacios de trabajo',
	WriteOff: 'Pedir por escrito',
	WriteOffModalDescription:
		'Tienes <mark>{count} {count, plural, one {elemento de línea} other {elementos de línea}}</mark> para dar de baja',
	WriteOffModalTitle: 'Ajuste de amortización',
	WriteOffReasonHelperText: 'Esta es una nota interna y no será visible para su cliente.',
	WriteOffReasonPlaceholder: 'Agregar un motivo de cancelación puede ayudar al revisar las transacciones facturables',
	WriteOffTotal: 'Descargo total ({currencyCode})',
	Writer: 'Escritor',
	Yearly: 'Anual',
	YearsPlural: '{age, plural, one {# año} other {# años}}',
	Yes: 'Sí',
	YesArchive: 'Sí, archivar',
	YesDelete: 'Sí, eliminar',
	YesDeleteOverride: 'Sí, anulación de eliminación',
	YesDeleteSection: 'Sí, eliminar',
	YesDisconnect: 'Sí, desconectar',
	YesEnd: 'Sí, fin',
	YesEndTranscription: 'Sí, finalizar la transcripción',
	YesImFineWithThat: 'Sí, estoy de acuerdo con eso',
	YesLeave: 'Sí, salir',
	YesMinimize: 'Sí, minimizar',
	YesOrNoAnswerTypeDescription: 'Configurar el tipo de respuesta',
	YesOrNoFormPrimaryText: 'Sí | No',
	YesOrNoFormSecondaryText: 'Elija opciones de sí o no',
	YesProceed: 'Sí, proceda',
	YesRemove: 'Si, eliminar',
	YesRestore: 'Sí, restaurar',
	YesStopIgnoring: 'Sí, dejar de ignorar',
	YesTransfer: 'Sí, transferir',
	Yesterday: 'Ayer',
	YogaInstructor: 'Instructor de yoga',
	You: 'Tú',
	YouArePresenting: 'Estás presentando',
	YouCanChooseMultiple: 'Puedes elegir varios',
	YouCanSelectMultiple: 'Puedes seleccionar varios',
	YouHaveOngoingTranscription: 'Tiene una transcripción en curso',
	YourAnswer: 'Tu respuesta',
	YourDisplayName: 'Tu nombre de pantalla',
	YourSpreadsheetColumns: 'Las columnas de su hoja de cálculo',
	YourTeam: 'Tu equipo',
	ZipCode: 'Código postal',
	Zoom: 'Zoom',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'No puedes agregar una llamada de Zoom para este miembro del equipo. Por favor, consulta la <a>documentación de soporte para más información.</a>',
};

export default items;
