import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'एबीएन',
	AIPrompts: 'एआई संकेत',
	ATeamMemberIsRequired: 'एक टीम सदस्य की आवश्यकता है',
	AboutClient: 'ग्राहक के बारे में',
	AcceptAppointment: 'आपकी नियुक्ति की पुष्टि करने के लिए धन्यवाद',
	AcceptTermsAndConditionsRequired: 'शर्तें स्वीकार करें ',
	Accepted: 'स्वीकृत',
	AccessGiven: 'पहुँच दी गई',
	AccessPermissions: 'पहुँच अनुमतियाँ',
	AccessType: 'पहुंच प्रकार',
	Accident: 'दुर्घटना',
	Account: 'खाता',
	AccountCredit: 'खाता क्रेडिट',
	Accountant: 'लेखाकार',
	Action: 'कार्रवाई',
	Actions: 'कार्रवाई',
	Active: 'सक्रिय',
	ActiveTags: 'सक्रिय टैग',
	ActiveUsers: 'सक्रिय उपयोगकर्ता',
	Activity: 'गतिविधि',
	Actor: 'अभिनेता',
	Acupuncture: 'एक्यूपंक्चर',
	Acupuncturist: 'एक्यूपंक्चर',
	Acupuncturists: 'एक्यूपंक्चर',
	AcuteManifestationOfAChronicCondition: 'किसी दीर्घकालिक स्थिति का तीव्र प्रकटीकरण',
	Add: 'जोड़ना',
	AddADescription: 'एक विवरण जोड़ दो',
	AddALocation: 'स्थान जोड़ना',
	AddASecondTimezone: 'दूसरा समयक्षेत्र जोड़ें',
	AddAddress: 'पता जोड़ें',
	AddAnother: '  एक और जोड़ें',
	AddAnotherAccount: 'दूसरा खाता जोड़ें',
	AddAnotherContact: 'एक और संपर्क जोड़ें',
	AddAnotherOption: 'अन्य विकल्प जोड़ें',
	AddAnotherTeamMember: 'दूसरे टीम सदस्य को जोड़ें',
	AddAvailablePayers: '+ उपलब्ध भुगतानकर्ताओं को जोड़ें',
	AddAvailablePayersDescription:
		'अपने कार्यक्षेत्र के भुगतानकर्ता सूची में जोड़ने के लिए भुगतानकर्ता खोजें। उन्हें जोड़ने के बाद, आप आवश्यकतानुसार नामांकन का प्रबंधन कर सकते हैं या भुगतानकर्ता विवरण समायोजित कर सकते हैं।',
	AddCaption: 'कैप्शन जोड़ें',
	AddClaim: 'दावा जोड़ें',
	AddClientFilesModalDescription:
		'पहुंच को प्रतिबंधित करने के लिए "द्वारा देखे जा सकने वाले" चेकबॉक्स में विकल्प चुनें',
	AddClientFilesModalTitle: '{name} के लिए फ़ाइलें अपलोड करें',
	AddClientNoteButton: 'नोट जोड़े',
	AddClientNoteModalDescription:
		'अपने नोट में सामग्री जोड़ें। "द्वारा देखे जाने योग्य" अनुभाग का उपयोग करके एक या अधिक समूहों का चयन करें जो इस विशिष्ट नोट को देख सकते हैं।',
	AddClientNoteModalTitle: 'नोट जोड़े',
	AddClientOwnerRelationshipModalDescription:
		'ग्राहक को आमंत्रित करने से उन्हें अपनी प्रोफ़ाइल जानकारी प्रबंधित करने और अपनी प्रोफ़ाइल जानकारी तक उपयोगकर्ता की पहुंच प्रबंधित करने की अनुमति मिलेगी।',
	AddClientOwnerRelationshipModalTitle: 'ग्राहक को आमंत्रित करें',
	AddCode: 'कोड जोड़ें',
	AddColAfter: 'इसके बाद कॉलम जोड़ें',
	AddColBefore: 'इससे पहले कॉलम जोड़ें',
	AddCollection: 'संग्रह जोड़ें',
	AddColor: 'रंग जोड़ें',
	AddColumn: 'कॉलम जोड़ें',
	AddContactRelationship: 'संपर्क संबंध जोड़ें',
	AddContacts: 'संपर्क जोड़ें',
	AddCustomField: 'कस्टम फ़ील्ड जोड़ें',
	AddDate: 'तारीख जोड़ें',
	AddDescription: 'विवरण जोड़ें',
	AddDetail: 'विवरण जोड़ें',
	AddDisplayName: 'प्रदर्शन नाम जोड़ें',
	AddDxCode: 'निदान कोड जोड़ें',
	AddEmail: 'ईमेल जोड़ें',
	AddFamilyClientRelationshipModalDescription:
		'परिवार के किसी सदस्य को आमंत्रित करने से उन्हें देखभाल की कहानियाँ और क्लाइंट की प्रोफ़ाइल जानकारी देखने की अनुमति मिलेगी। यदि उन्हें प्रशासक के रूप में आमंत्रित किया जाता है, तो उनके पास क्लाइंट की प्रोफ़ाइल जानकारी अपडेट करने और उपयोगकर्ता पहुँच प्रबंधित करने की पहुँच होगी।',
	AddFamilyClientRelationshipModalTitle: 'परिवार के सदस्य को आमंत्रित करें',
	AddField: 'क्षेत्र जोड़ें',
	AddFormField: 'फॉर्म फ़ील्ड जोड़ें',
	AddImages: 'छवियां जोड़ें',
	AddInsurance: 'बीमा जोड़ें',
	AddInvoice: 'चालान जोड़ें',
	AddLabel: 'लेबल जोड़ें',
	AddLanguage: 'भाषा जोड़ें',
	AddLocation: 'स्थान जोड़ना',
	AddManually: 'मैन्युअली जोड़ें',
	AddMessage: 'सन्देश जोड़ें',
	AddNewAction: 'नई कार्रवाई जोड़ें',
	AddNewSection: 'नया अनुभाग जोड़ें',
	AddNote: 'नोट जोड़े',
	AddOnlineBookingDetails: 'ऑनलाइन बुकिंग विवरण जोड़ें',
	AddPOS: 'पीओएस जोड़ें',
	AddPaidInvoices: 'भुगतान किए गए चालान जोड़ें',
	AddPayer: 'भुगतानकर्ता जोड़ें',
	AddPayment: 'भुगतान जोड़ें',
	AddPaymentAdjustment: 'भुगतान समायोजन जोड़ें',
	AddPaymentAdjustmentDisabledDescription: 'भुगतान आबंटन में कोई परिवर्तन नहीं किया जाएगा।',
	AddPaymentAdjustmentEnabledDescription: 'आबंटित करने के लिए उपलब्ध राशि कम कर दी जाएगी।',
	AddPhoneNumber: 'फ़ोन नंबर जोड़ें',
	AddPhysicalOrVirtualLocations: 'भौतिक या आभासी स्थान जोड़ें',
	AddQuestion: 'प्रश्न जोड़ें',
	AddQuestionOrTitle: 'कोई प्रश्न या शीर्षक जोड़ें',
	AddRelationship: 'संबंध जोड़ें',
	AddRelationshipModalTitle: 'मौजूदा संपर्क से जुड़ें',
	AddRelationshipModalTitleNewClient: 'नया संपर्क जोड़ें',
	AddRow: 'लाइन जोड़ो',
	AddRowAbove: 'ऊपर पंक्ति जोड़ें',
	AddRowBelow: 'नीचे पंक्ति जोड़ें',
	AddService: 'सेवा जोड़ें',
	AddServiceLocation: 'सेवा स्थान जोड़ें',
	AddServiceToCollections: 'संग्रह में सेवा जोड़ें',
	AddServiceToOneOrMoreCollections: 'एक या अधिक संग्रहों में सेवा जोड़ें',
	AddServices: 'सेवाएँ जोड़ें',
	AddSignature: 'हस्ताक्षर जोड़ें',
	AddSignaturePlaceholder: 'अपने हस्ताक्षर के साथ शामिल करने के लिए अतिरिक्त विवरण लिखें',
	AddSmartDataChips: 'स्मार्ट डेटा चिप्स जोड़ें',
	AddStaffClientRelationshipsModalDescription:
		'कर्मचारियों का चयन करने से उन्हें इस क्लाइंट के लिए देखभाल संबंधी कहानियाँ बनाने और देखने की सुविधा मिलेगी। वे क्लाइंट की जानकारी भी देख सकेंगे।',
	AddStaffClientRelationshipsModalTitle: 'स्टाफ़ संबंध जोड़ें',
	AddTag: 'एक टैग जोड़ना',
	AddTags: 'टैगों को जोड़ें',
	AddTemplate: 'टेम्पलेट जोड़ें',
	AddTimezone: 'समयक्षेत्र जोड़ें',
	AddToClaim: 'दावे में जोड़ें',
	AddToCollection: 'संग्रह में जोड़ें',
	AddToExisting: 'मौजूदा में जोड़ें',
	AddToStarred: 'तारांकित में जोड़ें',
	AddUnclaimedItems: 'दावा न किए गए आइटम जोड़ें',
	AddUnrelatedContactWarning:
		'आपने किसी ऐसे व्यक्ति को जोड़ा है जो {contact} का संपर्क नहीं है। साझा करने से पहले सुनिश्चित करें कि सामग्री प्रासंगिक है।',
	AddValue: '"{value}" जोड़ें',
	AddVideoCall: 'वीडियो कॉल जोड़ें',
	AddVideoOrVoiceCall: 'वीडियो या वॉयस कॉल जोड़ें',
	AddictionCounselor: 'व्यसन परामर्शदाता',
	AddingManualPayerDisclaimer:
		'किसी भुगतानकर्ता को अपने प्रदाता सूची में मैन्युअल रूप से जोड़ने से उस भुगतानकर्ता के साथ इलेक्ट्रॉनिक दावा फाइलिंग कनेक्शन स्थापित नहीं होता है, लेकिन इसका उपयोग मैन्युअल रूप से दावे बनाने के लिए किया जा सकता है।',
	AddingTeamMembersIncreaseCostAlert: 'नये टीम सदस्यों को जोड़ने से आपकी मासिक सदस्यता बढ़ जायेगी।',
	Additional: 'अतिरिक्त',
	AdditionalBillingProfiles: 'अतिरिक्त बिलिंग प्रोफ़ाइल',
	AdditionalBillingProfilesSectionDescription:
		'विशिष्ट टीम सदस्यों, भुगतानकर्ताओं या चालान टेम्प्लेट के लिए उपयोग की जाने वाली डिफ़ॉल्ट बिलिंग जानकारी को ओवरराइड करें।',
	AdditionalFeedback: 'अतिरिक्त प्रतिक्रिया',
	AddnNewWorkspace: 'नया कार्यक्षेत्र',
	AddnNewWorkspaceSuccessSnackbar: 'कार्यक्षेत्र बनाया गया है!',
	Address: 'पता',
	AddressNumberStreet: 'पता (संख्या, सड़क)',
	Adjustment: 'समायोजन',
	AdjustmentType: 'समायोजन प्रकार',
	Admin: 'एडमिन',
	Admins: 'व्यवस्थापक',
	AdminsOnly: 'केवल व्यवस्थापक',
	AdvancedPlanInclusionFive: 'खाता प्रबंधक',
	AdvancedPlanInclusionFour: 'गूगल एनालिटिक्स',
	AdvancedPlanInclusionHeader: 'प्लस में सब कुछ  ',
	AdvancedPlanInclusionOne: 'भूमिकाएँ ',
	AdvancedPlanInclusionSix: 'डेटा आयात समर्थन',
	AdvancedPlanInclusionThree: 'श्वेत लेबलिंग',
	AdvancedPlanInclusionTwo: '90 दिनों तक हटाया गया डेटा सुरक्षित रखें',
	AdvancedPlanMessage:
		'अपने अभ्यास की आवश्यकताओं पर नियंत्रण रखें। अपनी वर्तमान योजना की समीक्षा करें और उपयोग की निगरानी करें।',
	AdvancedSettings: 'एडवांस सेटिंग',
	AdvancedSubscriptionPlanSubtitle: 'सभी सुविधाओं के साथ अपने अभ्यास का विस्तार करें',
	AdvancedSubscriptionPlanTitle: 'विकसित',
	AdvertisingManager: 'एडवर्टाइजिंग प्रबंधक',
	AerospaceEngineer: 'एयरोस्पेस इंजीनियर',
	AgeYearsOld: '{age} साल पुराना',
	Agenda: 'कार्यसूची',
	AgendaView: 'एजेंडा दृश्य',
	AiAskSupportedFileTypes: 'समर्थित फ़ाइल प्रकार: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'आपकी उंगलियों पर एक सहायक',
	AiCopilotDisclaimer: 'AI कोपायलट गलतियाँ कर सकता है। महत्वपूर्ण जानकारी देखें।',
	AiCreateNewConversation: 'नया संवाद बनाएँ',
	AiEnhanceYourProductivity: 'अपनी उत्पादकता बढ़ाएँ',
	AiPoweredTemplates: 'AI संचालित टेम्पलेट्स',
	AiScribeNoDeviceFoundErrorMessage:
		'ऐसा लगता है कि आपका ब्राउज़र इस सुविधा का समर्थन नहीं करता है, या कोई संगत डिवाइस उपलब्ध नहीं है।',
	AiScribeUploadFormat: 'समर्थित फ़ाइल प्रकार: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'एक समय में केवल 1 फ़ाइल',
	AiShowConversationHistory: 'बातचीत का इतिहास दिखाएं',
	AiSmartPromptNodePlaceholderText:
		'यहाँ सटीक और वैयक्तिकृत AI परिणाम उत्पन्न करने में मदद करने के लिए अपना कस्टम प्रॉम्प्ट टाइप करें।',
	AiSmartPromptPrimaryText: 'AI स्मार्ट प्रॉम्प्ट',
	AiSmartPromptSecondaryText: 'कस्टम AI स्मार्ट प्रॉम्प्ट डालें',
	AiSmartReminders: 'AI स्मार्ट रिमाइंडर',
	AiTemplateBannerTitle: 'AI-संचालित टेम्प्लेट के साथ अपना काम आसान बनाएं',
	AiTemplates: 'एआई टेम्पलेट्स',
	AiTokens: 'एआई टोकन',
	AiWorkBetterWithAi: `<br>AI के साथ बेहतर काम करें 
<br>`,
	All: 'सभी',
	AllAppointments: 'सभी नियुक्तियाँ',
	AllCategories: 'सभी श्रेणियाँ',
	AllClients: 'सभी ग्राहक',
	AllContactPolicySelectorLabel: '<mark>{client}</mark> के सभी संपर्क',
	AllContacts: 'सभी संपर्क',
	AllContactsOf: `'{name}' के सभी संपर्क`,
	AllDay: 'पूरे दिन',
	AllInboxes: 'सभी इनबॉक्स',
	AllIndustries: 'सभी उद्योग',
	AllLocations: 'सभी स्थान',
	AllMeetings: 'सभी बैठकें',
	AllNotificationsRestoredMessage: 'सभी सूचनाएं बहाल कर दी गईं',
	AllProfessions: 'सभी व्यवसाय',
	AllReminders: 'सभी अनुस्मारक',
	AllServices: 'सभी सेवाएँ',
	AllStatuses: 'सभी स्थितियां',
	AllTags: 'सभी टैग',
	AllTasks: 'सभी कार्य',
	AllTeamMembers: 'सभी टीम सदस्य',
	AllTypes: 'सभी प्रकार',
	Allocated: 'आवंटित',
	AllocatedItems: 'आवंटित आइटम',
	AllocationTableEmptyState: 'कोई भुगतान आबंटन नहीं मिला',
	AllocationTotalWarningMessage: `आबंटित राशि कुल भुगतान राशि से अधिक है।
 कृपया नीचे दी गई पंक्ति वस्तुओं की समीक्षा करें।`,
	AllowClientsToCancelAnytime: 'ग्राहकों को किसी भी समय रद्द करने की अनुमति दें',
	AllowNewClient: 'नए ग्राहकों के लिए अनुमति दें',
	AllowNewClientHelper: 'नये ग्राहक इस सेवा को बुक कर सकते हैं',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'अपॉइंटमेंट से कम से कम {hours} घंटे पहले अनुमति दें',
	AllowToUseSavedCard: 'भविष्य में सहेजे गए कार्ड का उपयोग करने के लिए {provider} को अनुमति दें',
	AllowVideoCalls: 'वीडियो कॉल की अनुमति दें',
	AlreadyAdded: 'पहले से ही जोड़ा गया',
	AlreadyHasAccess: 'पहुँच है',
	AlreadyHasAccount: 'क्या आपके पास पहले से एक खाता मौजूद है?',
	Always: 'हमेशा',
	AlwaysIgnore: 'हमेशा अनदेखा करें',
	Amount: 'मात्रा',
	AmountDue: 'देय राशि',
	AmountOfReferralRequests: '{amount, plural, one {# रेफ़रल अनुरोध} other {# रेफ़रल अनुरोध}}',
	AmountPaid: 'राशि का भुगतान',
	AnalyzingAudio: 'ऑडियो का विश्लेषण किया जा रहा है...',
	AnalyzingInputContent: 'इनपुट सामग्री का विश्लेषण किया जा रहा है...',
	AnalyzingRequest: 'अनुरोध का विश्लेषण किया जा रहा है...',
	AnalyzingTemplateContent: 'टेम्पलेट सामग्री का विश्लेषण किया जा रहा है...',
	And: 'और',
	Annually: 'वार्षिक रूप से',
	Anonymous: 'गुमनाम',
	AnswerExceeded: 'आपका उत्तर 300 अक्षरों से कम होना चाहिए.',
	AnyStatus: 'कोई भी स्थिति',
	AppNotifications: 'सूचनाएं',
	AppNotificationsClearanceHeading: 'बढ़िया काम! आपने सारी गतिविधियाँ साफ़ कर दी हैं',
	AppNotificationsEmptyHeading: 'आपकी कार्यस्थान गतिविधि शीघ्र ही यहां दिखाई देगी',
	AppNotificationsEmptySubtext: 'फिलहाल कोई कार्रवाई नहीं की जा सकती',
	AppNotificationsIgnoredCount: '{total} अनदेखा',
	AppNotificationsUnread: '{total} अपठित',
	Append: 'संलग्न',
	Apply: 'आवेदन करना',
	ApplyAccountCredit: 'खाता क्रेडिट लागू करें',
	ApplyDiscount: 'छूट लागू करें',
	ApplyVisualEffects: 'दृश्य प्रभाव लागू करें',
	ApplyVisualEffectsNotSupported: 'दृश्य प्रभाव लागू करना समर्थित नहीं है',
	Appointment: 'नियुक्ति',
	AppointmentAssignedNotificationSubject: '{actorProfileName} ने आपको {appointmentName} असाइन किया है',
	AppointmentCancelledNotificationSubject: '{actorProfileName} ने {appointmentName} रद्द कर दिया है',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} ने {appointmentName} की पुष्टि की है',
	AppointmentDetails: 'नियुक्ति विवरण',
	AppointmentLocation: 'अॅपॉइंटमेंट लोकेशन',
	AppointmentLocationDescription:
		'अपने डिफ़ॉल्ट वर्चुअल और भौतिक स्थानों का प्रबंधन करें। जब कोई अपॉइंटमेंट शेड्यूल किया जाता है तो ये स्थान स्वचालित रूप से लागू हो जाएंगे।',
	AppointmentNotFound: 'नियुक्ति नहीं मिली',
	AppointmentReminder: 'नियुक्ति अनुस्मारक',
	AppointmentReminders: 'नियुक्ति अनुस्मारक',
	AppointmentRemindersInfo:
		'क्लाइंट की अपॉइंटमेंट न आने और रद्द होने से बचने के लिए उनके लिए स्वचालित अनुस्मारक सेट करें',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} ने {appointmentName} को पुनर्निर्धारित किया है',
	AppointmentSaved: 'अपॉइंटमेंट सहेजा गया',
	AppointmentStatus: 'नियुक्ति स्थिति',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0 मिनट} other {{minutes}मिनट}} } one {{hours}घंटा {minutes, plural, =0 {} other {{minutes}मिनट}}} other {{hours}घंटे {minutes, plural, =0 {} other {{minutes}मिनट}}} }',
	AppointmentUndone: 'नियुक्ति रद्द की गई',
	Appointments: 'नियुक्ति',
	Archive: 'पुरालेख',
	ArchiveClients: 'पुरालेख क्लाइंट',
	Archived: 'संग्रहीत',
	AreYouAClient: 'क्या आप ग्राहक हैं?',
	AreYouStillThere: 'क्या आप अभी भी हैं?',
	AreYouSure: 'क्या आपको यकीन है?',
	Arrangements: 'व्यवस्था',
	ArtTherapist: 'कला चिकित्सक',
	Articles: 'लेख',
	Artist: 'कलाकार',
	AskAI: 'एआई से पूछें',
	AskAiAddFormField: 'फ़ॉर्म फ़ील्ड जोड़ें',
	AskAiChangeFormality: 'औपचारिकता बदलें',
	AskAiChangeToneToBeMoreProfessional: 'अधिक पेशेवर बनने के लिए स्वर बदलें',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'बताएं कि यह दस्तावेज़ किस बारे में है',
	AskAiExplainWhatThisImageIsAbout: 'बताएं कि यह छवि किस बारे में है',
	AskAiFixSpellingAndGrammar: 'वर्तनी और व्याकरण ठीक करें',
	AskAiGenerateACaptionForThisImage: 'इस छवि के लिए एक कैप्शन बनाएं',
	AskAiGenerateFromThisPage: 'इस पृष्ठ से उत्पन्न करें',
	AskAiGetStarted: 'शुरू हो जाओ',
	AskAiGiveItAFriendlyTone: 'इसे एक दोस्ताना लहज़ा दें',
	AskAiGreeting: 'नमस्ते {firstName}! आज मैं आपकी कैसे मदद कर सकता हूँ?',
	AskAiHowCanIHelpWithYourContent: 'मैं आपकी सामग्री में कैसे मदद कर सकता हूँ?',
	AskAiInsert: 'डालना',
	AskAiMakeItMoreCasual: 'इसे और अधिक अनौपचारिक बनाएं',
	AskAiMakeThisTextMoreConcise: 'इस पाठ को अधिक संक्षिप्त बनाएं',
	AskAiMoreProfessional: 'अधिक पेशेवर',
	AskAiOpenPreviousNote: 'पिछला नोट खोलें',
	AskAiPondering: 'विचार',
	AskAiReplace: 'प्रतिस्थापित करें',
	AskAiReviewOrEditSelection: 'चयन की समीक्षा करें या संपादित करें',
	AskAiRuminating: 'जुगाली',
	AskAiSeeMore: 'और देखें',
	AskAiSimplifyLanguage: 'भाषा को सरल बनाएं',
	AskAiSomethingWentWrong:
		'कुछ गड़बड़ हो गई. अगर यह समस्या बनी रहती है, तो कृपया हमारे मदद केंद्र के माध्यम से हमसे संपर्क करें।',
	AskAiStartWithATemplate: 'टेम्पलेट से शुरू करें',
	AskAiSuccessfullyCopiedResponse: 'AI प्रतिक्रिया सफलतापूर्वक कॉपी की गई',
	AskAiSuccessfullyInsertedResponse: 'AI प्रतिक्रिया सफलतापूर्वक डाली गई',
	AskAiSuccessfullyReplacedResponse: 'AI प्रतिक्रिया को सफलतापूर्वक प्रतिस्थापित किया गया',
	AskAiSuggested: 'सुझाव दिया',
	AskAiSummariseTextIntoBulletPoints: 'पाठ को बुलेट पॉइंट में सारांशित करें',
	AskAiSummarizeNote: 'सारांशित नोट',
	AskAiThinking: 'सोच',
	AskAiToday: 'आज {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'आप इस फॉर्म के साथ क्या करना चाहते हैं?',
	AskAiWriteProfessionalNoteUsingTemplate: 'टेम्पलेट का उपयोग करके एक पेशेवर नोट लिखें',
	AskAskAiAnything: 'AI से कुछ भी पूछें',
	AskWriteSearchAnything: `पूछें, '@' लिखें या कुछ भी खोजें...`,
	Asking: 'पूछ',
	Assessment: 'आकलन',
	Assessments: 'आकलन',
	AssessmentsCategoryDescription: 'क्लाइंट मूल्यांकन रिकॉर्ड करने के लिए',
	AssignClients: 'क्लाइंट असाइन करें',
	AssignNewClients: 'क्लाइंट असाइन करें',
	AssignServices: 'सेवाएँ असाइन करें',
	AssignTeam: 'टीम नियुक्त करें',
	AssignTeamMember: 'टीम सदस्य नियुक्त करें',
	Assigned: 'सौंपा गया',
	AssignedClients: 'असाइन किए गए ग्राहक',
	AssignedServices: 'सौंपी गई सेवाएं',
	AssignedServicesDescription:
		'अपनी निर्दिष्ट सेवाओं को देखें और प्रबंधित करें, अपनी कस्टम दरों को दर्शाने के लिए कीमतों को समायोजित करें। ',
	AssignedTeam: 'नियुक्त टीम',
	AthleticTrainer: 'बलिष्ठ प्रशिक्षक',
	AttachFiles: 'फ़ाइलों को संलग्न करें',
	AttachLogo: 'संलग्न करना',
	Attachment: 'लगाव',
	AttachmentBlockedFileType: 'सुरक्षा कारणों से अवरुद्ध!',
	AttachmentTooLargeFileSize: 'फ़ाइल बहुत बड़ी है',
	AttachmentUploadItemComplete: 'पूरा',
	AttachmentUploadItemError: 'अपलोड विफल',
	AttachmentUploadItemLoading: 'लोड हो रहा है',
	AttemptingToReconnect: 'पुनः कनेक्ट करने का प्रयास किया जा रहा है...',
	Attended: 'में भाग लिया',
	AttendeeBeingMutedTooltip: `होस्ट ने आपको म्यूट कर दिया है। अनम्यूट करने के लिए 'हाथ उठाएँ' का उपयोग करें`,
	AttendeeWithId: 'सहभागी {attendeeId}',
	Attendees: 'सहभागी',
	AttendeesCount: '{count} उपस्थित',
	Attending: 'में भाग लेने',
	Audiologist: 'ऑडियोलॉजिस्ट',
	Aunt: 'चाची',
	Australia: 'ऑस्ट्रेलिया',
	AuthenticationCode: 'प्रमाणीकरण कोड',
	AuthoriseProvider: '{provider} को अधिकृत करें',
	AuthorisedProviders: 'अधिकृत प्रदाता',
	AutoDeclineAllFutureOption: 'केवल नए इवेंट या अपॉइंटमेंट',
	AutoDeclineAllOption: 'नए और मौजूदा कार्यक्रम या नियुक्तियाँ',
	AutoDeclinePrimaryText: 'स्वचालित रूप से इवेंट अस्वीकार करें',
	AutoDeclineSecondaryText:
		'आपके ऑफिस से बाहर रहने की अवधि के दौरान होने वाले इवेंट्स स्वचालित रूप से अस्वीकार कर दिए जाएँगे',
	AutogenerateBillings: 'बिलिंग दस्तावेज़ स्वचालित रूप से तैयार करें',
	AutogenerateBillingsDescription:
		'महीने के आखिरी दिन स्वचालित बिलिंग दस्तावेज़ तैयार किए जाएँगे। चालान और सुपरबिल रसीदें कभी भी मैन्युअल रूप से बनाई जा सकती हैं।',
	AutomateWorkflows: 'वर्कफ़्लोज़ को स्वचालित करें',
	AutomaticallySendSuperbill: 'सुपरबिल रसीदें स्वचालित रूप से भेजें',
	AutomaticallySendSuperbillHelperText:
		'सुपरबिल बीमा प्रतिपूर्ति के लिए ग्राहक को प्रदान की गई सेवाओं की एक विस्तृत रसीद है',
	Automation: 'स्वचालन',
	AutomationActionSendEmailLabel: 'ईमेल भेजें',
	AutomationActionSendSMSLabel: 'एसएमएस भेजें',
	AutomationAndReminders: 'स्वचालन ',
	AutomationDeletedSuccessMessage: 'स्वचालन सफलतापूर्वक हटा दिया गया',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'समय घटना',
	AutomationParams_timeUnit: 'इकाई',
	AutomationParams_timeValue: 'संख्या',
	AutomationPublishSuccessMessage: 'स्वचालन सफलतापूर्वक प्रकाशित हुआ',
	AutomationPublishWarningTooltip:
		'कृपया स्वचालन कॉन्फ़िगरेशन की पुनः जाँच करें और सुनिश्चित करें कि यह ठीक से कॉन्फ़िगर किया गया है',
	AutomationTriggerEventCancelledDescription: 'जब कोई ईवेंट रद्द या हटा दिया जाता है तो ट्रिगर होता है',
	AutomationTriggerEventCancelledLabel: 'इवेंट रद्द',
	AutomationTriggerEventCreatedDescription: 'जब कोई ईवेंट बनाया जाता है तो ट्रिगर होता है',
	AutomationTriggerEventCreatedLabel: 'नई इवैंट',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'जब कोई ईवेंट बनाया या अपडेट किया जाता है तो ट्रिगर होता है (सिवाय इसके कि जब इसे रद्द कर दिया गया हो)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'नया या अपडेट किया गया ईवेंट',
	AutomationTriggerEventEndedDescription: 'जब कोई ईवेंट समाप्त होता है तो ट्रिगर होता है',
	AutomationTriggerEventEndedLabel: 'इवेंट समाप्त हुआ',
	AutomationTriggerEventStartsDescription: 'किसी ईवेंट के शुरू होने से पहले निर्दिष्ट समय बीत जाने पर ट्रिगर होता है',
	AutomationTriggerEventStartsLabel: 'इवेंट शुरू',
	Automations: 'स्वचालन',
	Availability: 'उपलब्धता',
	AvailabilityDisableSchedule: 'शेड्यूल अक्षम करें',
	AvailabilityDisabled: 'अक्षम',
	AvailabilityEnableSchedule: 'शेड्यूल सक्षम करें',
	AvailabilityEnabled: 'सक्रिय',
	AvailabilityNoActiveBanner:
		'आपने अपने सभी शेड्यूल बंद कर दिए हैं। क्लाइंट आपको ऑनलाइन बुक नहीं कर सकते हैं, और भविष्य की सभी अपॉइंटमेंट्स को मैन्युअल रूप से पुष्टि करने की आवश्यकता है।',
	AvailabilityNoActiveConfirmationDescription:
		'इस उपलब्धता को अक्षम करने से कोई सक्रिय शेड्यूल नहीं होगा। क्लाइंट आपको ऑनलाइन बुक नहीं कर पाएंगे, और चिकित्सकों द्वारा की गई कोई भी बुकिंग आपके कार्य घंटों के बाहर होगी।',
	AvailabilityNoActiveConfirmationProceed: 'हाँ, आगे बढें',
	AvailabilityNoActiveConfirmationTitle: 'कोई सक्रिय शेड्यूल नहीं',
	AvailabilityToggle: 'सक्षम शेड्यूल',
	AvailabilityUnsetDate: 'कोई तारीख तय नहीं है',
	AvailableLocations: 'उपलब्ध स्थान',
	AvailablePayers: 'उपलब्ध भुगतानकर्ता',
	AvailablePayersEmptyState: 'कोई भुगतानकर्ता नहीं चुना गया',
	AvailableTimes: 'उपलब्ध समय',
	Back: 'पीछे',
	BackHome: 'वापस घर',
	BackToAppointment: 'अॅपॉइंटमेंटवर परत जा',
	BackToLogin: 'लॉगिन पर वापस जाएं',
	BackToMapColumns: 'नक्शा स्तंभों पर वापस',
	BackToTemplates: 'टेम्प्लेट्स पर वापस',
	BackToUploadFile: 'फ़ाइल अपलोड करने के लिए वापस',
	Banker: 'बैंकर',
	BasicBlocks: 'बुनियादी ब्लॉक',
	BeforeAppointment: 'अपॉइंटमेंट से {interval} {unit} पहले {deliveryType} रिमाइंडर भेजें',
	BehavioralAnalyst: 'व्यवहार विश्लेषक',
	BehavioralHealthTherapy: 'व्यवहारिक स्वास्थ्य चिकित्सा',
	Beta: 'बीटा',
	BillTo: 'बिल प्राप्तकर्ता',
	BillableItems: 'बिल योग्य आइटम',
	BillableItemsEmptyState: 'कोई बिल योग्य आइटम नहीं मिला',
	Biller: 'बिलर',
	Billing: 'बिलिंग',
	BillingAddress: 'बिलिंग पता',
	BillingAndReceiptsUnauthorisedMessage: 'इस जानकारी तक पहुंचने के लिए चालान और भुगतान दृश्य तक पहुंच आवश्यक है।',
	BillingBillablesTab: 'बिल योग्य',
	BillingClaimsTab: 'दावा',
	BillingDetails: 'बिलिंग विवरण',
	BillingDocuments: 'बिलिंग दस्तावेज़',
	BillingDocumentsClaimsTab: 'दावा',
	BillingDocumentsEmptyState: 'कोई {tabType} नहीं मिले हैं',
	BillingDocumentsInvoicesTab: 'चालान',
	BillingDocumentsSuperbillsTab: 'सुपरबिल्स',
	BillingInformation: 'बिलिंग जानकारी',
	BillingInvoicesTab: 'चालान',
	BillingItems: 'बिलिंग आइटम',
	BillingPaymentsTab: 'भुगतान',
	BillingPeriod: 'बिलिंग अवधि',
	BillingProfile: 'बिलिंग प्रोफ़ाइल',
	BillingProfileOverridesDescription: 'इस बिलिंग प्रोफाइल का उपयोग केवल कुछ टीम सदस्यों तक सीमित करें',
	BillingProfileOverridesHeader: 'पहुँच सीमित करें',
	BillingProfileProviderType: 'प्रदाता प्रकार',
	BillingProfileTypeIndividual: 'व्यवसायी',
	BillingProfileTypeIndividualSubLabel: 'टाइप 1 एनपीआई',
	BillingProfileTypeOrganisation: 'संगठन',
	BillingProfileTypeOrganisationSubLabel: 'टाइप 2 एनपीआई',
	BillingProfiles: 'बिलिंग प्रोफाइल',
	BillingProfilesEditHeader: '{name} बिलिंग प्रोफ़ाइल संपादित करें',
	BillingProfilesNewHeader: 'नई बिलिंग प्रोफ़ाइल',
	BillingProfilesSectionDescription:
		'चिकित्सकों और बीमा भुगतानकर्ताओं के लिए अपनी बिलिंग जानकारी को प्रबंधित करने के लिए बिलिंग प्रोफाइल सेट अप करें, जिसे चालान और बीमा भुगतान पर लागू किया जा सके।',
	BillingSearchPlaceholder: 'खोज आइटम',
	BillingSettings: 'बिलिंग सेटिंग',
	BillingSuperbillsTab: 'सुपरबिल्स',
	BiomedicalEngineer: 'बायोमेडिकल इंजीनियर',
	BlankInvoice: 'रिक्त चालान',
	BlueShieldProviderNumber: 'ब्लू शील्ड प्रदाता संख्या',
	Body: 'शरीर',
	Bold: 'बोल्ड',
	BookAgain: 'पुनः बुक करें',
	BookAppointment: 'अपॉइंटमेंट बुक करें',
	BookableOnline: 'ऑनलाइन बुक करने योग्य',
	BookableOnlineHelper: 'ग्राहक इस सेवा को ऑनलाइन बुक कर सकते हैं',
	BookedOnline: 'ऑनलाइन बुक किया',
	Booking: 'बुकिंग',
	BookingAnalyticsIntegrationPanelDescription:
		'अपने ऑनलाइन बुकिंग प्रवाह में मुख्य क्रियाकलापों और रूपांतरणों को ट्रैक करने के लिए Google टैग प्रबंधक सेट अप करें। मार्केटिंग प्रयासों को बेहतर बनाने और बुकिंग अनुभव को अनुकूलित करने के लिए उपयोगकर्ता इंटरैक्शन पर मूल्यवान डेटा एकत्र करें।',
	BookingAnalyticsIntegrationPanelTitle: 'एनालिटिक्स एकीकरण',
	BookingAndCancellationPolicies: 'बुकिंग ',
	BookingButtonEmbed: 'बटन',
	BookingButtonEmbedDescription: 'आपकी वेबसाइट पर ऑनलाइन बुकिंग बटन जोड़ता है',
	BookingDirectTextLink: 'प्रत्यक्ष पाठ लिंक',
	BookingDirectTextLinkDescription: 'ऑनलाइन बुकिंग पृष्ठ खोलता है',
	BookingFormatLink: 'प्रारूप लिंक',
	BookingFormatLinkButtonTitle: 'बटन शीर्षक',
	BookingInlineEmbed: 'इनलाइन एम्बेड',
	BookingInlineEmbedDescription: 'ऑनलाइन बुकिंग पृष्ठ को सीधे आपकी वेबसाइट पर लोड करता है',
	BookingLink: 'बुकिंग लिंक',
	BookingLinkModalCopyText: 'प्रतिलिपि',
	BookingLinkModalDescription: 'इस लिंक के ज़रिए ग्राहकों को किसी भी टीम सदस्य या सेवाओं को बुक करने की अनुमति दें',
	BookingLinkModalHelpText: 'ऑनलाइन बुकिंग सेट अप करने का तरीका जानें',
	BookingLinkModalTitle: 'अपना बुकिंग लिंक साझा करें',
	BookingPolicies: 'बुकिंग नीतियां',
	BookingPoliciesDescription: 'यह निर्धारित करें कि ग्राहक कब ऑनलाइन बुकिंग कर सकते हैं',
	BookingTimeUnitDays: 'दिन',
	BookingTimeUnitHours: 'घंटे',
	BookingTimeUnitMinutes: 'मिनट',
	BookingTimeUnitMonths: 'महीने',
	BookingTimeUnitWeeks: 'हफ्तों',
	BottomNavBilling: 'बिलिंग',
	BottomNavGettingStarted: 'घर',
	BottomNavMore: 'अधिक',
	BottomNavNotes: 'नोट्स',
	Brands: 'ब्रांड',
	Brother: 'भाई',
	BrotherInLaw: 'साला',
	BrowseOrDragFileHere: '<link>ब्राउज़ करें</link> या फ़ाइल को यहाँ खींचें',
	BrowseOrDragFileHereDescription: 'PNG, JPG (अधिकतम. {limit})',
	BufferAfterTime: '{time} मिनट बाद',
	BufferAndLabel: 'और',
	BufferAppointmentLabel: 'एक नियुक्ति',
	BufferBeforeTime: '{time} मिनट पहले',
	BufferTime: 'बफर समय',
	BufferTimeViewLabel: '{bufferBefore} मिनट पहले और {bufferAfter} मिनट बाद नियुक्तियाँ',
	BulkArchiveClientsDescription:
		'क्या आप वाकई इन क्लाइंट को संग्रहित करना चाहते हैं? आप उन्हें बाद में पुनः सक्रिय कर सकते हैं।',
	BulkArchiveSuccess: 'क्लाइंट को सफलतापूर्वक संग्रहीत किया गया',
	BulkArchiveUndone: 'बल्क संग्रह पूर्ववत',
	BulkPermanentDeleteDescription: 'यह **{count} बातचीत** को हटा देगा। इस कार्य को पूर्ववत नहीं किया जा सकता है।',
	BulkPermanentDeleteTitle: 'बातचीत को हमेशा के लिए मिटाएँ',
	BulkUnarchiveSuccess: 'क्लाइंट को सफलतापूर्वक अनआर्काइव किया गया',
	BulletedList: 'बुलेटेड सूची',
	BusinessAddress: 'व्यावसायिक पता',
	BusinessAddressOptional: 'व्यावसायिक पता <span>(वैकल्पिक)</span>',
	BusinessName: 'व्यवसाय का नाम',
	Button: 'बटन',
	By: 'द्वारा',
	CHAMPUSIdentificationNumber: 'चैम्पस पहचान संख्या',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'सीवीसी आवश्यक है',
	Calendar: 'कैलेंडर',
	CalendarAppSyncFormDescription: 'Carepatron ईवेंट को सिंक करें',
	CalendarAppSyncPanelTitle: 'कनेक्टेड ऐप सिंक',
	CalendarDescription: 'अपनी नियुक्तियाँ प्रबंधित करें या व्यक्तिगत कार्य और अनुस्मारक सेट करें',
	CalendarDetails: 'कैलेंडर विवरण',
	CalendarDetailsDescription: 'अपने कैलेंडर और अपॉइंटमेंट प्रदर्शन सेटिंग प्रबंधित करें.',
	CalendarScheduleNew: 'नया शेड्यूल करें',
	CalendarSettings: 'कैलेंडर सेटिंग्स',
	Call: 'पुकारना',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> वीडियो कॉल में शामिल हो गया है',
	CallChangeLayoutTextContent: 'चयन को भविष्य की बैठकों के लिए सहेजा जाता है',
	CallIdlePrompt: 'क्या आप शामिल होने के लिए प्रतीक्षा करना पसंद करेंगे, या बाद में पुनः प्रयास करना चाहेंगे?',
	CallLayoutOptionAuto: 'ऑटो',
	CallLayoutOptionSidebar: 'साइड बार',
	CallLayoutOptionSpotlight: 'सुर्खियों',
	CallLayoutOptionTiled: 'टाइलों',
	CallNoAttendees: 'बैठक में कोई उपस्थित नहीं हुआ।',
	CallSessionExpiredError: 'सत्र समाप्त हो गया है। कॉल समाप्त हो गई है। कृपया फिर से शामिल होने का प्रयास करें।',
	CallWithPractitioner: '{practitioner} के साथ कॉल',
	CallsListCreateButton: 'नया कॉल',
	CallsListEmptyState: 'कोई सक्रिय कॉल नहीं',
	CallsListItemEndCall: 'कॉल समाप्त करें',
	CamWarningMessage: 'आपके कैमरे में कोई समस्या पाई गई है',
	Camera: 'कैमरा',
	CameraAndMicIssueModalDescription: `कृपया केयरपेट्रॉन को अपने कैमरे और माइक्रोफ़ोन तक पहुंच सक्षम करें।
 अधिक जानकारी के लिए <a>इस गाइड का पालन करें</a>`,
	CameraAndMicIssueModalTitle: 'कैमरा और माइक्रोफ़ोन अवरुद्ध हैं',
	CameraQuality: 'कैमरा गुणवत्ता',
	CameraSource: 'कैमरा स्रोत',
	CanModifyReadOnlyEvent: 'आप इस ईवेंट को संशोधित नहीं कर सकते',
	Canada: 'कनाडा',
	Cancel: 'रद्द करना',
	CancelClientImportDescription: 'क्या आप निश्चित हैं कि आप इस आयात को रद्द करना चाहते हैं?',
	CancelClientImportPrimaryAction: 'हाँ, आयात रद्द करें',
	CancelClientImportSecondaryAction: 'संपादन जारी रखें',
	CancelClientImportTitle: 'क्लाइंट्स को इम्पोर्ट करना रद्द करें',
	CancelImportButton: 'आयात रद्द करें',
	CancelPlan: 'योजना रद्द करें',
	CancelPlanConfirmation: `योजना को रद्द करने से आपके खाते में इस माह की बकाया राशि स्वतः ही जमा हो जाएगी।
 यदि आप अपने बिल वाले उपयोगकर्ताओं को डाउनग्रेड करना चाहते हैं, तो आप बस टीम के सदस्यों को हटा सकते हैं और केयरपेट्रॉन स्वचालित रूप से आपकी सदस्यता मूल्य को अपडेट कर देगा।`,
	CancelSend: 'भेजना रद्द करें',
	CancelSubscription: 'सदस्यता रद्द करें',
	Canceled: 'रद्द',
	CancellationPolicy: 'रद्दीकरण नीति',
	Cancelled: 'रद्द',
	CannotContainSpecialCharactersError: '{specialCharacters} नहीं रख सकता',
	CannotDeleteInvoice: 'ऑनलाइन भुगतान के माध्यम से भुगतान किए गए चालान हटाए नहीं जा सकते',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> को <b>{group}</b> समूह से बाहर नहीं ले जाया जा सकता',
	CannotMoveServiceOutsideCollections: 'सेवा को संग्रह से बाहर नहीं ले जाया जा सकता',
	CapeTown: 'केप टाउन',
	Caption: 'कैप्शन',
	CaptureNameFieldLabel: 'वह नाम जिससे आप चाहते हैं कि दूसरे आपको पुकारें',
	CapturePaymentMethod: 'भुगतान विधि कैप्चर करें',
	CapturingAudio: 'ऑडियो कैप्चर करना',
	CapturingSignature: 'हस्ताक्षर कैप्चर किया जा रहा है...',
	CardInformation: 'कार्ड की जानकारी',
	CardNumberRequired: 'कार्ड नंबर आवश्यक है',
	CardiacRehabilitationSpecialist: 'हृदय पुनर्वास विशेषज्ञ',
	Cardiologist: 'हृदय रोग विशेषज्ञ',
	CareAiNoConversations: 'अभी तक कोई बातचीत नहीं हुई है',
	CareAiNoConversationsDescription: '{aiName} के साथ बातचीत शुरू करने के लिए, शुरू करें',
	CareAssistant: 'देखभाल सहायक',
	CareManager: 'देखभाल प्रबंधक',
	Caregiver: 'केयरगिवर',
	CaregiverCreateModalDescription:
		'कर्मचारियों को प्रशासक के रूप में जोड़ने से उन्हें देखभाल संबंधी कहानियाँ बनाने और प्रबंधित करने की सुविधा मिलेगी। इससे उन्हें क्लाइंट बनाने और प्रबंधित करने की पूरी पहुँच भी मिलेगी।',
	CaregiverCreateModalTitle: 'नये टीम सदस्य',
	CaregiverListCantAddStaffInfoTitle:
		'आप अपनी सदस्यता के लिए कर्मचारियों की अधिकतम संख्या तक पहुँच चुके हैं। अधिक कर्मचारी सदस्यों को जोड़ने के लिए कृपया अपनी योजना को अपग्रेड करें।',
	CaregiverListCreateButton: 'नये टीम सदस्य',
	CaregiverListEmptyState: 'कोई देखभालकर्ता नहीं जोड़ा गया',
	CaregiversListItemRemoveStaff: 'स्टाफ़ हटाएँ',
	CarepatronApp: 'केयरपैट्रॉन ऐप',
	CarepatronCommunity: 'समुदाय',
	CarepatronFieldAddress: 'पता',
	CarepatronFieldAssignedStaff: 'नियुक्त कर्मचारी',
	CarepatronFieldBirthDate: 'जन्म तिथि',
	CarepatronFieldEmail: 'ईमेल',
	CarepatronFieldEmploymentStatus: 'रोज़गार की स्थिति',
	CarepatronFieldEthnicity: 'जातीयता',
	CarepatronFieldFirstName: 'पहला नाम',
	CarepatronFieldGender: 'लिंग',
	CarepatronFieldIdentificationNumber: 'पहचान संख्या',
	CarepatronFieldIsArchived: 'स्थिति',
	CarepatronFieldLabel: 'लेबल',
	CarepatronFieldLastName: 'उपनाम',
	CarepatronFieldLivingArrangements: 'रहने की व्यवस्था',
	CarepatronFieldMiddleNames: 'मध्य नाम',
	CarepatronFieldOccupation: 'पेशा',
	CarepatronFieldPhoneNumber: 'फ़ोन नंबर',
	CarepatronFieldRelationshipStatus: 'रिश्ते की स्थिति',
	CarepatronFieldStatus: 'स्थिति',
	CarepatronFieldStatusHelperText: 'अधिकतम 10 स्थितियां.',
	CarepatronFieldTags: 'टैग',
	CarepatronFields: 'केयरपैट्रन क्षेत्र',
	Cash: 'नकद',
	Category: 'वर्ग',
	CategoryInputPlaceholder: 'टेम्पलेट श्रेणी चुनें',
	CenterAlign: 'केंद्र संरेखित करें',
	Central: 'केंद्रीय',
	ChangeLayout: 'लेआउट बदलें',
	ChangeLogo: 'परिवर्तन',
	ChangePassword: 'पासवर्ड बदलें',
	ChangePasswordFailureSnackbar: 'क्षमा करें, आपका पासवर्ड नहीं बदला गया। जाँच लें कि आपका पुराना पासवर्ड सही है।',
	ChangePasswordHelperInfo: 'न्यूनतम लंबाई {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'पासवर्ड सफलतापूर्वक बदला गया! अगली बार जब आप लॉग इन करें, तो उस पासवर्ड का उपयोग करना सुनिश्चित करें।',
	ChangeSubscription: 'सदस्यता बदलें',
	ChangesNotAllowed: 'इस फ़ील्ड में परिवर्तन नहीं किया जा सकता',
	ChargesDisabled: 'शुल्क अक्षम',
	ChargesEnabled: 'शुल्क सक्षम',
	ChargesStatus: 'शुल्क स्थिति',
	ChartAndDiagram: 'चार्ट/डायग्राम',
	ChartsAndDiagramsCategoryDescription: 'क्लाइंट डेटा और प्रगति को दर्शाने के लिए',
	ChatEditMessage: 'सन्देश संपादित करें',
	ChatReplyTo: '{name} को जवाब दें',
	ChatTypeMessageTo: 'संदेश {name}',
	Check: 'जाँच करना',
	CheckList: 'जांच सूची',
	Chef: 'बावर्ची',
	Chiropractic: 'चिरोप्रैक्टिक',
	Chiropractor: 'हाड वैद्य',
	Chiropractors: 'काइरोप्रैक्टर्स',
	ChooseACollection: 'एक संग्रह चुनें',
	ChooseAContact: 'कोई संपर्क चुनें',
	ChooseAccountTypeHeader: 'कौन सा आपको सबसे अच्छा वर्णित करता है?',
	ChooseAction: 'कार्रवाई का चयन',
	ChooseAnAccount: 'एक खाता चुनें',
	ChooseAnOption: 'एक विकल्प चुनें',
	ChooseBillingProfile: 'बिलिंग प्रोफ़ाइल चुनें',
	ChooseClaim: 'दावा चुनें',
	ChooseCollection: 'संग्रह चुनें',
	ChooseColor: 'रंग चुनें',
	ChooseCustomDate: 'कस्टम तिथि चुनें',
	ChooseDateAndTime: 'दिनांक और समय चुनें',
	ChooseDxCodes: 'निदान कोड चुनें',
	ChooseEventType: 'इवेंट का प्रकार चुनें',
	ChooseFileButton: 'एक फ़ाइल चुनें',
	ChooseFolder: 'फ़ोल्डर चुनें',
	ChooseInbox: 'इनबॉक्स चुनें',
	ChooseMethod: 'विधि चुनें',
	ChooseNewOwner: 'नया मालिक चुनें',
	ChooseOrganization: 'संगठन चुनें',
	ChoosePassword: 'पासवर्ड चुनें',
	ChoosePayer: 'भुगतानकर्ता चुनें',
	ChoosePaymentMethod: 'एक भुगतान विधि का चयन करें',
	ChoosePhysicalOrRemoteLocations: 'स्थान दर्ज करें या चुनें',
	ChoosePlan: '{plan} चुनें',
	ChooseProfessional: 'प्रोफेशनल चुनें',
	ChooseServices: 'सेवाएँ चुनें',
	ChooseSource: 'स्रोत चुनें',
	ChooseSourceDescription:
		'क्लाइंट्स को इम्पोर्ट करने के लिए जगह चुनें - चाहे वह कोई फ़ाइल हो या कोई अन्य सॉफ़्टवेयर प्लेटफ़ॉर्म.',
	ChooseTags: 'टैग चुनें',
	ChooseTaxName: 'कर का नाम चुनें',
	ChooseTeamMembers: 'टीम के सदस्य चुनें',
	ChooseTheme: 'थीम चुनें',
	ChooseTrigger: 'ट्रिगर चुनें',
	ChooseYourProvider: 'अपना प्रदाता चुनें',
	CircularProgressWithLabel: '{value}%',
	City: 'शहर',
	CivilEngineer: 'सिविल इंजीनियर',
	Claim: 'दावा',
	ClaimAddReferringProvider: 'रेफरिंग प्रोवाइडर जोड़ें',
	ClaimAddRenderingProvider: 'रेंडरिंग प्रदाता जोड़ें',
	ClaimAmount: 'दावा राशि',
	ClaimAmountPaidHelpContent:
		'भुगतान की गई राशि मरीज़ या अन्य भुगतानकर्ताओं से प्राप्त भुगतान है। मरीज़ और/या अन्य भुगतानकर्ताओं द्वारा कवर की गई सेवाओं पर भुगतान की गई कुल राशि दर्ज करें।',
	ClaimAmountPaidHelpSubtitle: 'फ़ील्ड 29',
	ClaimAmountPaidHelpTitle: 'राशि का भुगतान',
	ClaimBillingProfileTypeIndividual: 'व्यक्ति',
	ClaimBillingProfileTypeOrganisation: 'संगठन',
	ClaimChooseRenderingProviderOrTeamMember: 'रेंडरिंग प्रदाता या टीम सदस्य चुनें',
	ClaimClientInsurancePolicies: 'ग्राहक बीमा पॉलिसियाँ',
	ClaimCreatedAction: '<mark>दावा {claimNumber}</mark> बनाया गया',
	ClaimDeniedAction:
		'<mark>दावा {claimNumber}</mark> को <b>{payerNumber} {payerName}</b> द्वारा अस्वीकार कर दिया गया था',
	ClaimDiagnosisCodeSelectorPlaceholder: 'ICD 10 निदान कोड खोजें',
	ClaimDiagnosisSelectorHelpContent: `"निदान या चोट" दावे पर सेवा(ओं) से संबंधित रोगी का संकेत, लक्षण, शिकायत या स्थिति है।
 अधिकतम 12 ICD 10 निदान कोड का चयन किया जा सकता है।`,
	ClaimDiagnosisSelectorHelpSubtitle: 'फ़ील्ड 21',
	ClaimDiagnosisSelectorHelpTitle: 'निदान या चोट',
	ClaimDiagnosticCodesEmptyError: 'कम से कम एक निदान कोड की आवश्यकता है',
	ClaimDoIncludeReferrerInformation: 'CMS1500 पर रेफ़रर जानकारी अवश्य शामिल करें',
	ClaimERAReceivedAction: 'इलेक्ट्रॉनिक रेमिटेंस <b>{payerNumber} {payerName}</b> से प्राप्त हुआ।',
	ClaimElectronicPaymentAction:
		'इलेक्ट्रॉनिक धनराशि प्राप्त हुई	<mark>भुगतान {paymentReference}</mark> के लिए <b>{paymentAmount}</b> द्वारा <b>{payerNumber} {payerName}</b> दर्ज किया गया',
	ClaimExportedAction: '<mark>दावा {claimNumber}</mark> को <b>{attachmentType}</b> के रूप में निर्यात किया गया था',
	ClaimFieldClient: 'ग्राहक या संपर्क नाम',
	ClaimFieldClientAddress: 'ग्राहक का पता',
	ClaimFieldClientAddressDescription:
		'क्लाइंट का पता दर्ज करें। पहली पंक्ति सड़क के पते के लिए है। पते में विराम चिह्न (अल्पविराम या अवधि) या किसी भी प्रतीक का उपयोग न करें। यदि विदेशी पते की रिपोर्ट कर रहे हैं, तो विशिष्ट रिपोर्टिंग निर्देशों के लिए भुगतानकर्ता से संपर्क करें।',
	ClaimFieldClientAddressSubtitle: 'फ़ील्ड 5',
	ClaimFieldClientDateOfBirth: 'ग्राहक की जन्म तिथि',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'फ़ील्ड 3',
	ClaimFieldClientDateOfBirthDescription:
		'ग्राहक की 8-अंकीय जन्म तिथि (MM/DD/YYYY) दर्ज करें। ग्राहक की जन्म तिथि वह जानकारी है जो ग्राहक की पहचान करेगी और यह समान नाम वाले व्यक्तियों को अलग करती है।',
	ClaimFieldClientDescription: `'ग्राहक का नाम' उस व्यक्ति का नाम है जिसने उपचार या आपूर्ति प्राप्त की।`,
	ClaimFieldClientSexDescription: `'लिंग' वह जानकारी है जो ग्राहक की पहचान करेगी और यह समान नाम वाले व्यक्तियों में अंतर बताती है।`,
	ClaimFieldClientSubtitle: 'फ़ील्ड 2',
	ClaimFiling: 'दावा दाखिल करना',
	ClaimHistorySubtitle: 'बीमा • दावा {number}',
	ClaimIncidentAutoAccident: 'वाहन दुर्घटना?',
	ClaimIncidentConditionRelatedTo: 'क्या ग्राहक की स्थिति इससे संबंधित है?',
	ClaimIncidentConditionRelatedToHelpContent:
		'यह जानकारी बताती है कि क्लाइंट की बीमारी या चोट रोजगार, ऑटो दुर्घटना या अन्य दुर्घटना से संबंधित है या नहीं। रोजगार (वर्तमान या पिछला) यह संकेत देगा कि स्थिति क्लाइंट की नौकरी या कार्यस्थल से संबंधित है। ऑटो दुर्घटना यह संकेत देगी कि स्थिति ऑटोमोबाइल दुर्घटना का परिणाम है। अन्य दुर्घटना यह संकेत देगी कि स्थिति किसी अन्य प्रकार की दुर्घटना का परिणाम है।',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'फ़ील्ड 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'क्या ग्राहक की स्थिति इससे संबंधित है?',
	ClaimIncidentCurrentIllness: 'वर्तमान बीमारी, चोट या गर्भावस्था',
	ClaimIncidentCurrentIllnessHelpContent:
		'वर्तमान बीमारी, चोट या गर्भावस्था की तारीख बीमारी की शुरुआत की पहली तारीख, चोट की वास्तविक तारीख या गर्भावस्था के लिए एलएमपी की पहचान करती है।',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'फ़ील्ड 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'वर्तमान बीमारी, चोट या गर्भावस्था की तिथियाँ (एलएमपी)',
	ClaimIncidentDate: 'तारीख',
	ClaimIncidentDateFrom: 'दिनांक से',
	ClaimIncidentDateTo: 'दिनांक',
	ClaimIncidentEmploymentRelated: 'रोज़गार',
	ClaimIncidentEmploymentRelatedDesc: '(वर्तमान या पूर्व)',
	ClaimIncidentHospitalizationDatesLabel: 'वर्तमान सेवाओं से संबंधित अस्पताल में भर्ती होने की तिथियाँ',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'वर्तमान सेवाओं से संबंधित अस्पताल में भर्ती होने की तारीखें ग्राहक के ठहरने को संदर्भित करती हैं और दावे पर सेवा(ओं) से संबंधित प्रवेश और छुट्टी की तारीखों को इंगित करती हैं।',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'फ़ील्ड 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'वर्तमान सेवाओं से संबंधित अस्पताल में भर्ती होने की तिथियाँ',
	ClaimIncidentInformation: 'घटना की जानकारी',
	ClaimIncidentOtherAccident: 'अन्य दुर्घटना?',
	ClaimIncidentOtherAssociatedDate: 'अन्य संबद्ध तिथि',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'अन्य तिथि ग्राहक की स्थिति या उपचार के बारे में अतिरिक्त तिथि की जानकारी देती है।',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'फ़ील्ड 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'अन्य तिथि',
	ClaimIncidentQualifier: 'क्वालीफायर',
	ClaimIncidentQualifierPlaceholder: 'क्वालीफायर चुनें',
	ClaimIncidentUnableToWorkDatesLabel: 'ग्राहक वर्तमान व्यवसाय में काम करने में असमर्थ था',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'वह तिथि जब ग्राहक वर्तमान व्यवसाय में काम करने में असमर्थ था, वह समय अवधि है जब ग्राहक काम करने में असमर्थ था।',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'फ़ील्ड 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'दिनांक ग्राहक वर्तमान व्यवसाय में काम करने में असमर्थ था',
	ClaimIncludeReferrerInformation: 'CMS1500 पर रेफ़रर जानकारी शामिल करें',
	ClaimInsuranceCoverageTypeHelpContent: `इस दावे पर लागू स्वास्थ्य बीमा कवरेज का प्रकार। अन्य में HMOs, वाणिज्यिक बीमा, ऑटोमोबाइल दुर्घटना, देयता, या श्रमिक मुआवज़ा सहित स्वास्थ्य बीमा शामिल है।
 यह जानकारी दावे को सही कार्यक्रम की ओर निर्देशित करती है तथा प्राथमिक दायित्व स्थापित कर सकती है।`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'फ़ील्ड 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'कवरेज प्रकार',
	ClaimInsuranceGroupIdHelpContent: `बीमाधारक की पॉलिसी या समूह संख्या उसी प्रकार दर्ज करें जैसा कि बीमाधारक के स्वास्थ्य देखभाल पहचान पत्र पर अंकित है।

 "बीमित व्यक्ति की पॉलिसी, समूह या FECA नंबर" स्वास्थ्य, ऑटो या अन्य बीमा योजना कवरेज के लिए अल्फ़ान्यूमेरिक पहचानकर्ता है। FECA नंबर 9-वर्णों वाला अल्फ़ान्यूमेरिक पहचानकर्ता है जो काम से संबंधित स्थिति का दावा करने वाले मरीज़ को दिया जाता है।`,
	ClaimInsuranceGroupIdHelpSubtitle: 'फ़ील्ड 11',
	ClaimInsuranceGroupIdHelpTitle: 'बीमाधारक की पॉलिसी, समूह या FECA संख्या',
	ClaimInsuranceMemberIdHelpContent: `जिस भुगतानकर्ता को दावा प्रस्तुत किया जा रहा है, उसके लिए बीमाधारक के आईडी कार्ड पर दर्शाई गई बीमाधारक की आईडी संख्या दर्ज करें।
 यदि मरीज के पास भुगतानकर्ता द्वारा निर्दिष्ट विशिष्ट सदस्य पहचान संख्या है, तो उस संख्या को इस फ़ील्ड में दर्ज करें।`,
	ClaimInsuranceMemberIdHelpSubtitle: 'फ़ील्ड 1a',
	ClaimInsuranceMemberIdHelpTitle: 'बीमाधारक की सदस्य आईडी',
	ClaimInsurancePayer: 'बीमा भुगतानकर्ता',
	ClaimManualPaymentAction: '<mark>भुगतान {paymentReference}</mark> <b>{paymentAmount}</b> के लिए दर्ज किया गया',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'अतिरिक्त दावा जानकारी',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'कृपया इस फ़ील्ड के उपयोग के संबंध में सार्वजनिक या निजी भुगतानकर्ता के वर्तमान निर्देशों का संदर्भ लें। दर्ज की जा रही जानकारी के लिए, जब उपलब्ध हो, तो उपयुक्त योग्यताकर्ता की रिपोर्ट करें।योग्यताकर्ता और जानकारी के बीच स्थान, हाइफ़न या अन्य विभाजक दर्ज न करें।',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'फ़ील्ड 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'अतिरिक्त दावे की जानकारी',
	ClaimMiscClaimCodes: 'दावा कोड',
	ClaimMiscOriginalReferenceNumber: 'मूल संदर्भ संख्या',
	ClaimMiscPatientsAccountNumber: 'मरीज का खाता नंबर',
	ClaimMiscPatientsAccountNumberHelpContent: 'रोगी का खाता नंबर प्रदाता द्वारा निर्दिष्ट पहचानकर्ता है।',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'फ़ील्ड 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'मरीज का खाता नंबर',
	ClaimMiscPriorAuthorizationNumber: 'पूर्व प्राधिकरण संख्या',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'पूर्व प्राधिकरण संख्या, भुगतानकर्ता को दी गई वह संख्या है जो सेवा/सेवाओं को अधिकृत करती है।',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'फ़ील्ड 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'पूर्व प्राधिकरण संख्या',
	ClaimMiscResubmissionCode: 'पुनः प्रस्तुतीकरण कोड',
	ClaimMiscResubmissionCodeHelpContent:
		'पुनः प्रस्तुतीकरण का अर्थ है, गंतव्य भुगतानकर्ता या प्राप्तकर्ता द्वारा निर्दिष्ट कोड और मूल संदर्भ संख्या, जो पहले प्रस्तुत किए गए दावे या मुठभेड़ को इंगित करता है।',
	ClaimMiscResubmissionCodeHelpSubtitle: 'फ़ील्ड 22',
	ClaimMiscResubmissionCodeHelpTitle: 'पुनः प्रस्तुतीकरण और/या मूल संदर्भ संख्या',
	ClaimNumber: 'दावा संख्या',
	ClaimNumberFormat: 'दावा #{number}',
	ClaimOrderingProvider: 'ऑर्डर प्रदाता',
	ClaimOtherId: 'अन्य आईडी',
	ClaimOtherIdPlaceholder: 'एक विकल्प चुनें',
	ClaimOtherIdQualifier: 'अन्य आईडी क्वालीफायर',
	ClaimOtherIdQualifierPlaceholder: 'आईडी क्वालिफायर चुनें',
	ClaimPlaceOfService: 'सेवा का स्थान',
	ClaimPlaceOfServicePlaceholder: 'पीओएस जोड़ें',
	ClaimPolicyHolderRelationship: 'पॉलिसीधारक संबंध',
	ClaimPolicyInformation: 'नीति जानकारी',
	ClaimPolicyTelephone: 'टेलीफोन (क्षेत्र कोड शामिल करें)',
	ClaimReceivedAction: '<mark>दावा {claimNumber}</mark>  द्वारा प्राप्त किया गया <b>{name}</b>',
	ClaimReferringProvider: 'संदर्भित प्रदाता',
	ClaimReferringProviderEmpty: 'कोई रेफ़रिंग प्रोवाइडर/एस जोड़ा नहीं गया',
	ClaimReferringProviderHelpContent:
		'दर्ज किया गया नाम संदर्भित प्रदाता, आदेश प्रदाता या पर्यवेक्षण प्रदाता है जिसने दावे पर सेवा(ओं) या आपूर्ति(ओं) को संदर्भित, आदेशित या पर्यवेक्षण किया है। क्वालीफायर रिपोर्ट किए जा रहे प्रदाता की भूमिका को इंगित करता है।',
	ClaimReferringProviderHelpSubtitle: 'फ़ील्ड 17',
	ClaimReferringProviderHelpTitle: 'संदर्भित प्रदाता या स्रोत का नाम',
	ClaimReferringProviderQualifier: 'क्वालीफायर',
	ClaimReferringProviderQualifierPlaceholder: 'क्वालीफायर चुनें',
	ClaimRejectedAction: '<mark>दावा {claimNumber}</mark> को <b>{name}</b> ने अस्वीकार कर दिया था',
	ClaimRenderingProviderIdNumber: 'आईडी नंबर',
	ClaimRenderingProviderOrTeamMember: 'रेंडरिंग प्रदाता या टीम सदस्य',
	ClaimRestoredAction: '<mark>दावा {claimNumber}</mark> बहाल कर दिया गया',
	ClaimServiceFacility: 'सेवा सुविधा',
	ClaimServiceFacilityLocationHelpContent:
		'उस सुविधा का नाम और पता जहां सेवाएं प्रदान की गईं, उस साइट की पहचान करता है जहां सेवाएं प्रदान की गईं।',
	ClaimServiceFacilityLocationHelpLabel: '32, 32ए और 32बी',
	ClaimServiceFacilityLocationHelpSubtitle: 'क्षेत्र 32, 32a और 32b',
	ClaimServiceFacilityLocationHelpTitle: 'सेवा सुविधा',
	ClaimServiceFacilityPlaceholder: 'सेवा सुविधा या स्थान चुनें',
	ClaimServiceLabChargesHelpContent: `बिलिंग प्रदाता के अलावा किसी अन्य संस्था द्वारा प्रदान की गई खरीदी गई सेवाओं के लिए दावा करते समय इस फ़ील्ड को पूरा करें।
 प्रत्येक खरीदी गई सेवा को अलग दावे में दर्ज किया जाना चाहिए, क्योंकि CMS1500 फॉर्म पर केवल एक ही शुल्क दर्ज किया जा सकता है।`,
	ClaimServiceLabChargesHelpSubtitle: 'फ़ील्ड 20',
	ClaimServiceLabChargesHelpTitle: 'प्रयोगशाला के बाहर के शुल्क',
	ClaimServiceLineServiceHelpContent:
		'"प्रक्रियाएं, सेवाएं या आपूर्तियां" रोगी को प्रदान की जाने वाली चिकित्सा सेवाओं और प्रक्रियाओं की पहचान करती हैं।',
	ClaimServiceLineServiceHelpSubtitle: 'फ़ील्ड 24d',
	ClaimServiceLineServiceHelpTitle: 'प्रक्रियाएँ, सेवाएँ या आपूर्तियाँ',
	ClaimServiceLinesEmptyError: 'कम से कम एक सेवा लाइन की आवश्यकता है',
	ClaimServiceSupplementaryInfoHelpContent: `लागू योग्यताओं का उपयोग करके प्रदान की गई सेवाओं का अतिरिक्त वर्णनात्मक विवरण जोड़ें।
 क्वालिफायर और सूचना के बीच कोई स्थान, हाइफन या अन्य विभाजक न डालें।

 पूरक जानकारी जोड़ने के पूर्ण निर्देशों के लिए सीएमएस 1500 दावा प्रपत्र निर्देश देखें।`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'फ़ील्ड 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'पूरक जानकारी',
	ClaimSettingsBillingMethodTitle: 'ग्राहक बिलिंग विधि',
	ClaimSettingsClientSignatureDescription:
		'मुझे बीमा दावों पर कार्रवाई करने के लिए आवश्यक चिकित्सा या अन्य जानकारी जारी करने की सहमति है।',
	ClaimSettingsClientSignatureTitle: 'फ़ाइल पर क्लाइंट के हस्ताक्षर',
	ClaimSettingsConsentLabel: 'बीमा दावों पर कार्रवाई के लिए सहमति आवश्यक:',
	ClaimSettingsDescription: 'सुचारू भुगतान प्रक्रिया सुनिश्चित करने के लिए ग्राहक बिलिंग विधि चुनें:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} की एक सक्रिय बीमा पॉलिसी है। बीमा बिलिंग को सक्षम करने के लिए क्लाइंट बिलिंग विधि को बीमा पर अपडेट करें।',
	ClaimSettingsInsuranceDescription: 'बीमा द्वारा प्रतिपूर्ति की गई लागत',
	ClaimSettingsInsuranceTitle: 'बीमा',
	ClaimSettingsNoPoliciesAlertDescription: 'बीमा दावे सक्षम करने के लिए बीमा पॉलिसी जोड़ें.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'मुझे प्रदान की गई सेवाओं के लिए बीमा भुगतान प्राप्त करने की सहमति है।',
	ClaimSettingsPolicyHolderSignatureTitle: 'फ़ाइल पर पॉलिसी धारक के हस्ताक्षर',
	ClaimSettingsSelfPayDescription: 'ग्राहक नियुक्तियों के लिए भुगतान करेगा',
	ClaimSettingsSelfPayTitle: 'स्वयं भुगतान',
	ClaimSettingsTitle: 'दावा सेटिंग',
	ClaimSexSelectorPlaceholder: 'पुरुष महिला',
	ClaimStatusChangedAction: '<mark>दावा {claimNumber}</mark> स्थिति अद्यतित',
	ClaimSubmittedAction:
		'<mark>दावा {claimNumber}</mark>  <b>{payerClearingHouse}</b> को <b>{payerNumber} {payerName}</b> के लिए प्रस्तुत किया गया',
	ClaimSubtitle: 'दावा #{claimNumber}',
	ClaimSupervisingProvider: 'पर्यवेक्षक प्रदाता',
	ClaimSupplementaryInfo: 'पूरक जानकारी',
	ClaimSupplementaryInfoPlaceholder: 'पूरक जानकारी जोड़ें',
	ClaimTrashedAction: '<mark>दावा {claimNumber}</mark> हटा दिया गया',
	ClaimValidationFailure: 'दावा मान्य करने में विफल',
	ClaimsEmptyStateDescription: 'कोई दावा नहीं मिला है.',
	ClainInsuranceTelephone: 'बीमा टेलीफोन (क्षेत्र कोड शामिल करें)',
	Classic: 'क्लासिक',
	Clear: 'स्पष्ट',
	ClearAll: 'सब साफ़ करें',
	ClearSearchFilter: 'स्पष्ट',
	ClearingHouse: 'क्लीयरिंग हाउस',
	ClearingHouseClaimId: 'Claim.MD आईडी',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'क्लियरिंग हाउस संदर्भ',
	ClearingHouseUnavailableError: 'क्लियरिंग हाउस वर्तमान में अनुपलब्ध है। कृपया बाद में पुनः प्रयास करें।',
	ClickToUpload: 'अपलोड करने के लिए क्लिक करें',
	Client: 'ग्राहक',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} ने {noteTitle, select, undefined { एक नोट } other {{noteTitle}}} जोड़ा',
	ClientAndRelationshipSelectorPlaceholder: 'ग्राहकों और उनके संबंधों का चयन करें',
	ClientAndRelationshipSelectorTitle: 'सभी ग्राहक और उनके रिश्ते',
	ClientAndRelationshipSelectorTitle1: `'{name}' के सभी संबंध`,
	ClientAppCallsPageNoOptionsText:
		'अगर आप वीडियो कॉल का इंतज़ार कर रहे हैं, तो यह जल्द ही यहाँ दिखाई देगा। अगर आपको कोई समस्या आ रही है, तो कृपया उस व्यक्ति से संपर्क करें जिसने इसे शुरू किया था।',
	ClientAppSubHeaderMyDocumentation: 'मेरा दस्तावेज़ीकरण',
	ClientAppointment: 'ग्राहक नियुक्ति',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} ने {appointmentName} बुक किया है',
	ClientAppointmentsEmptyStateDescription: 'कोई नियुक्ति नहीं मिली',
	ClientAppointmentsEmptyStateTitle: 'अपने ग्राहकों की आगामी और ऐतिहासिक नियुक्तियों और उनकी उपस्थिति पर नज़र रखें',
	ClientArchivedSuccessfulSnackbar: 'सफलतापूर्वक संग्रहीत <b>{name}</b>',
	ClientBalance: 'ग्राहक शेष',
	ClientBilling: 'बिलिंग',
	ClientBillingAddPaymentMethodDescription:
		'अपने ग्राहकों की भुगतान विधियों को जोड़ें और प्रबंधित करें ताकि उनकी चालान और बिलिंग प्रक्रिया को सरल बनाया जा सके।',
	ClientBillingAndPaymentDueDate: 'नियत तारीख',
	ClientBillingAndPaymentHistory: 'बिलिंग और भुगतान इतिहास',
	ClientBillingAndPaymentInvoices: 'चालान',
	ClientBillingAndPaymentIssueDate: 'जारी करने की तिथि',
	ClientBillingAndPaymentPrice: 'कीमत',
	ClientBillingAndPaymentReceipt: 'रसीद',
	ClientBillingAndPaymentServices: 'सेवाएं',
	ClientBillingAndPaymentStatus: 'स्थिति',
	ClientBulkStaffAssignedSuccessSnackbar: 'टीम {count, plural, one {सदस्य} other {सदस्य}} सौंपा गया!',
	ClientBulkStaffUnassignedSuccessSnackbar:
		'टीम में {count, plural, one {सदस्य} other {सदस्य}} असाइन नहीं किए गए हैं!',
	ClientBulkTagsAddedSuccessSnackbar: 'टैग जोड़े गए!',
	ClientDuplicatesDeviewDescription:
		'सभी डेटा को एकीकृत करने के लिए एकाधिक क्लाइंट रिकॉर्ड को एक में मर्ज करें - नोट्स, दस्तावेज़, अपॉइंटमेंट, चालान और वार्तालाप।',
	ClientDuplicatesPageMergeHeader: 'वह डेटा चुनें जिसे आप रखना चाहते हैं',
	ClientDuplicatesReviewHeader: 'मर्ज के लिए संभावित डुप्लिकेट रिकॉर्ड की तुलना करें',
	ClientEmailChangeWarningDescription:
		'क्लाइंट के ईमेल को अपडेट करने से किसी भी साझा दस्तावेज़ तक उनकी पहुंच समाप्त हो जाएगी, और नए ईमेल वाले उपयोगकर्ता को पहुंच प्रदान की जाएगी',
	ClientFieldDateDescription: 'प्रारूप तिथि',
	ClientFieldDateLabel: 'तारीख',
	ClientFieldDateRangeDescription: 'तारीखों की एक श्रृंखला',
	ClientFieldDateRangeLabel: 'तिथि सीमा',
	ClientFieldDateShowDateDescription: 'जैसे 29 वर्ष',
	ClientFieldDateShowDateRangeDescription: 'जैसे 2 सप्ताह',
	ClientFieldEmailDescription: 'मेल पता',
	ClientFieldEmailLabel: 'ईमेल',
	ClientFieldLabel: 'फील्ड लेबल',
	ClientFieldLinearScaleDescription: 'स्केल विकल्प 1-10',
	ClientFieldLinearScaleLabel: 'रेखीय पैमाना',
	ClientFieldLocationDescription: 'भौतिक या डाक पता',
	ClientFieldLocationLabel: 'जगह',
	ClientFieldLongTextDescription: 'लंबा पाठ क्षेत्र',
	ClientFieldLongTextLabel: 'अनुच्छेद',
	ClientFieldMultipleChoiceDropdownDescription: 'सूची से अनेक विकल्प चुनें',
	ClientFieldMultipleChoiceDropdownLabel: 'बहुविकल्पीय ड्रॉपडाउन',
	ClientFieldPhoneNumberDescription: 'फ़ोन नंबर',
	ClientFieldPhoneNumberLabel: 'फ़ोन',
	ClientFieldPlaceholder: 'क्लाइंट फ़ील्ड प्रकार चुनें',
	ClientFieldSingleChoiceDropdownDescription: 'सूची से केवल एक विकल्प चुनें',
	ClientFieldSingleChoiceDropdownLabel: 'एकल विकल्प ड्रॉपडाउन',
	ClientFieldTextDescription: 'पाठ इनपुट फ़ील्ड',
	ClientFieldTextLabel: 'मूलपाठ',
	ClientFieldYesOrNoDescription: 'हाँ या नहीं विकल्प चुनें',
	ClientFieldYesOrNoLabel: 'हाँ | नहीं',
	ClientFileFormAccessLevelDescription:
		'आप और टीम हमेशा आपके द्वारा अपलोड की गई फ़ाइलों तक पहुँच रखते हैं। आप इस फ़ाइल को क्लाइंट और/या उनके संबंधों के साथ साझा करना चुन सकते हैं',
	ClientFileSavedSuccessSnackbar: 'फ़ाइल सहेजी गई!',
	ClientFilesPageEmptyStateText: 'कोई फ़ाइल अपलोड नहीं की गई',
	ClientFilesPageUploadFileButton: 'फाइलें अपलोड करें',
	ClientHeaderBilling: 'बिलिंग',
	ClientHeaderBillingAndReceipts: 'बिलिंग ',
	ClientHeaderDocumentation: 'प्रलेखन',
	ClientHeaderDocuments: 'दस्तावेज़',
	ClientHeaderFile: 'दस्तावेज़',
	ClientHeaderHistory: 'चिकित्सा का इतिहास',
	ClientHeaderInbox: 'इनबॉक्स',
	ClientHeaderNote: 'टिप्पणी',
	ClientHeaderOverview: 'अवलोकन',
	ClientHeaderProfile: 'निजी',
	ClientHeaderRelationship: 'संबंध',
	ClientHeaderRelationships: 'रिश्ते',
	ClientId: 'ग्राहक आईडी',
	ClientImportProcessingDescription: 'फ़ाइल अभी भी प्रोसेस हो रही है। हम आपको सूचित करेंगे जब यह हो जाएगा।',
	ClientImportReadyForMappingDescription:
		'हमने आपकी फ़ाइल का प्री-प्रोसेसिंग पूरा कर लिया है। क्या आप इस इम्पोर्ट को पूरा करने के लिए कॉलम मैप करना चाहेंगे?',
	ClientImportReadyForMappingNotificationSubject:
		'क्लाइंट इम्पोर्ट प्री-प्रोसेसिंग पूरी हो चुकी है। फ़ाइल अब मैपिंग के लिए तैयार है।',
	ClientInAppMessaging: 'क्लाइंट इन-ऐप मैसेजिंग',
	ClientInfoAddField: 'एक और फ़ील्ड जोड़ें',
	ClientInfoAddRow: 'लाइन जोड़ो',
	ClientInfoAlertMessage: 'इस अनुभाग में भरी गई कोई भी जानकारी ग्राहक रिकॉर्ड में भर दी जाएगी।',
	ClientInfoFormPrimaryText: 'ग्राहक जानकारी',
	ClientInfoFormSecondaryText: 'संपर्क विवरण इकट्ठा करें',
	ClientInfoPlaceholder: `ग्राहक का नाम, ईमेल पता, फ़ोन नंबर
 भौतिक पता,
 जन्म तिथि`,
	ClientInformation: 'ग्राहक जानकारी',
	ClientInsuranceTabLabel: 'बीमा',
	ClientIntakeFormsNotSupported: `फॉर्म टेम्पलेट्स वर्तमान में क्लाइंट इनटेक के माध्यम से समर्थित नहीं हैं।
 इसके बजाय उन्हें क्लाइंट नोट्स के रूप में बनाएं और साझा करें।`,
	ClientIntakeModalDescription:
		'आपके क्लाइंट को एक इनटेक ईमेल भेजा जाएगा जिसमें उनसे अपना प्रोफ़ाइल पूरा करने, प्रासंगिक मेडिकल या रेफरल दस्तावेज़ अपलोड करने के लिए कहा जाएगा। उन्हें क्लाइंट पोर्टल एक्सेस दिया जाएगा।',
	ClientIntakeModalTitle: '{name} को इंटेक भेजें',
	ClientIntakeSkipPasswordSuccessSnackbar: 'सफल! आपका इनटेक सहेज लिया गया है।',
	ClientIntakeSuccessSnackbar: 'सफल! आपका इनटेक सहेज लिया गया है और एक पुष्टिकरण ईमेल भेजा गया है।',
	ClientIsChargedProcessingFee: 'आपके ग्राहक प्रसंस्करण शुल्क का भुगतान करेंगे',
	ClientListCreateButton: 'नए ग्राहक',
	ClientListEmptyState: 'कोई ग्राहक नहीं जोड़ा गया',
	ClientListPageItemArchive: 'क्लाइंट हटाएँ',
	ClientListPageItemRemoveAccess: 'मेरी पहुँच हटाएँ',
	ClientLocalizationPanelDescription: 'क्लाइंट की पसंदीदा भाषा और समय क्षेत्र।',
	ClientLocalizationPanelTitle: 'भाषा और समय क्षेत्र',
	ClientManagementAndEHR: 'ग्राहक प्रबंधन ',
	ClientMergeResultSummaryBanner:
		'रिकॉर्ड्स को मिलाने से सभी क्लाइंट डेटा एकत्रित हो जाता है, जिसमें नोट्स, दस्तावेज़, अपॉइंटमेंट, इनवॉइस और बातचीत शामिल हैं। जारी रखने से पहले सटीकता सत्यापित करें।',
	ClientMergeResultSummaryTitle: 'मर्ज परिणाम सारांश',
	ClientModalTitle: 'नए ग्राहक',
	ClientMustHaveEmaillAccessErrorText: 'ग्राहक/संपर्क जिनके पास ईमेल नहीं है',
	ClientMustHavePortalAccessErrorText: 'ग्राहकों/संपर्कों को साइन अप करना आवश्यक होगा',
	ClientMustHaveZoomAppConnectedErrorText: 'सेटिंग्स > कनेक्टेड ऐप्स के माध्यम से ज़ूम कनेक्ट करें',
	ClientNameFormat: 'ग्राहक नाम प्रारूप',
	ClientNotFormAccessLevel: 'द्वारा देखा जा सकता है:',
	ClientNotFormAccessLevelDescription:
		'आपके और टीम के पास हमेशा आपके द्वारा प्रकाशित नोट्स तक पहुंच होती है। आप इस नोट को क्लाइंट और/या उनके संबंधों के साथ साझा करना चुन सकते हैं',
	ClientNotRegistered: 'पंजीकृत नहीं है',
	ClientNoteFormAddFileButton: 'फ़ाइलों को संलग्न करें',
	ClientNoteFormChooseAClient: 'जारी रखने के लिए कोई ग्राहक/संपर्क चुनें',
	ClientNoteFormContent: 'सामग्री',
	ClientNoteItemDeleteConfirmationModalDescription:
		'एक बार हटा दिए जाने के बाद आप इस नोट को पुनः प्राप्त नहीं कर सकते.',
	ClientNotePublishedAndLockSuccessSnackbar: 'नोट प्रकाशित और लॉक किया गया.',
	ClientNotePublishedSuccessSnackbar: 'नोट प्रकाशित!',
	ClientNotes: 'ग्राहक नोट्स',
	ClientNotesEmptyStateText: 'नोट्स जोड़ने के लिए, ग्राहक की प्रोफ़ाइल पर जाएं और नोट्स टैब पर क्लिक करें।',
	ClientOnboardingChoosePasswordTitle1: 'लगभग पूरा हो गया!',
	ClientOnboardingChoosePasswordTitle2: 'एक पासवर्ड चुनें',
	ClientOnboardingCompleteIntake: 'पूर्ण सेवन',
	ClientOnboardingConfirmationScreenText:
		'आपने वह सारी जानकारी दे दी है जिसकी {providerName} को ज़रूरत है।	अपनी ईमेल पता की पुष्टि करें ताकि अपना ऑनबोर्डिंग शुरू कर सकें। अगर आपको यह तुरंत नहीं मिलता है, तो कृपया अपने स्पैम फ़ोल्डर को चेक करें।',
	ClientOnboardingConfirmationScreenTitle: 'बढ़िया! अपना इनबॉक्स चेक करें।',
	ClientOnboardingDashboardButton: 'डैशबोर्ड पर जाएँ',
	ClientOnboardingHealthRecordsDesc1: 'क्या आप {providerName} के साथ कोई रेफ़रल लेटर, दस्तावेज़ साझा करना चाहते हैं?',
	ClientOnboardingHealthRecordsDescription: 'विवरण जोड़ें (वैकल्पिक)',
	ClientOnboardingHealthRecordsTitle: 'प्रलेखन',
	ClientOnboardingPasswordRequirements: 'आवश्यकताएं',
	ClientOnboardingPasswordRequirementsConditions1: 'न्यूनतम 9 अक्षर आवश्यक',
	ClientOnboardingProviderIntroSignupButton: 'अपने लिए साइन अप करें',
	ClientOnboardingProviderIntroSignupFamilyButton: 'परिवार के किसी सदस्य के लिए साइन अप करें',
	ClientOnboardingProviderIntroTitle: '{name} ने आपको उनके Carepatron प्लेटफ़ॉर्म से जुड़ने के लिए आमंत्रित किया है',
	ClientOnboardingRegistrationInstructions: 'नीचे अपना व्यक्तिगत विवरण दर्ज करें।',
	ClientOnboardingRegistrationTitle: 'सबसे पहले हमें कुछ व्यक्तिगत विवरण की आवश्यकता होगी',
	ClientOnboardingStepFormsAndAgreements: 'फॉर्म और समझौते',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'कृपया {providerName} इंटेक प्रक्रिया के लिए निम्नलिखित फॉर्म पूरा करें',
	ClientOnboardingStepHealthDetails: 'स्वास्थ्य विवरण',
	ClientOnboardingStepPassword: 'पासवर्ड',
	ClientOnboardingStepYourDetails: 'आपका विवरण',
	ClientPaymentMethodDescription:
		'अपनी अगली अपॉइंटमेंट बुकिंग और इनवॉइसिंग को अधिक तेज़ और सुरक्षित बनाने के लिए अपनी प्रोफ़ाइल में भुगतान विधि सहेजें।',
	ClientPortal: 'ग्राहक पोर्टल',
	ClientPortalDashboardEmptyDescription: 'आपकी नियुक्ति का इतिहास और उपस्थिति यहां दिखाई देगी।',
	ClientPortalDashboardEmptyTitle: 'अपनी उपस्थिति के साथ-साथ सभी आगामी, अनुरोधित और पिछली नियुक्तियों पर नज़र रखें',
	ClientPreferredNotificationPanelDescription:
		'अपडेट और सूचनाएं प्राप्त करने के लिए अपने ग्राहक की पसंदीदा विधि प्रबंधित करें:',
	ClientPreferredNotificationPanelTitle: 'पसंदीदा अधिसूचना विधि',
	ClientProcessingFee: 'भुगतान में ({currencyCode}) {amount} प्रसंस्करण शुल्क शामिल है',
	ClientProfileAddress: 'पता',
	ClientProfileDOB: 'जन्म तिथि',
	ClientProfileEmailHelperText: 'ईमेल जोड़ने से पोर्टल तक पहुंच मिलती है',
	ClientProfileEmailHelperTextMoreInfo:
		'क्लाइंट को पोर्टल तक पहुंच प्रदान करने से टीम के सदस्यों को नोट्स, फ़ाइलें और अन्य दस्तावेज़ साझा करने की सुविधा मिलती है',
	ClientProfileId: 'आईडी',
	ClientProfileIdentificationNumber: 'पहचान संख्या',
	ClientRelationshipsAddClientOwnerButton: 'ग्राहक को आमंत्रित करें',
	ClientRelationshipsAddFamilyButton: 'परिवार के सदस्य को आमंत्रित करें',
	ClientRelationshipsAddStaffButton: 'स्टाफ़ एक्सेस जोड़ें',
	ClientRelationshipsEmptyStateText: 'कोई संबंध नहीं जोड़ा गया',
	ClientRemovedSuccessSnackbar: 'क्लाइंट सफलतापूर्वक हटा दिया गया.',
	ClientResponsibility: 'क्लाइंट की ज़िम्मेदारी',
	ClientSavedSuccessSnackbar: 'क्लाइंट सफलतापूर्वक सहेजा गया.',
	ClientTableClientName: 'ग्राहक नाम',
	ClientTablePhone: 'फ़ोन',
	ClientTableStatus: 'स्थिति',
	ClientUnarchivedSuccessfulSnackbar: 'सफलतापूर्वक अनआर्काइव किया गया <b>{name}</b>',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} ने {noteTitle, select, undefined { एक नोट } other {{noteTitle}}} संपादित किया',
	ClientView: 'ग्राहक दृश्य',
	Clients: 'ग्राहकों',
	ClientsTable: 'ग्राहक तालिका',
	ClinicalFormat: 'नैदानिक प्रारूप',
	ClinicalPsychologist: 'नैदानिक मनोविज्ञानी',
	Close: 'बंद करना',
	CloseImportClientsModal: 'क्या आप वाकई ग्राहकों का आयात रद्द करना चाहते हैं?',
	CloseReactions: 'करीबी प्रतिक्रियाएं',
	Closed: 'बंद किया हुआ',
	Coaching: 'सिखाना',
	Code: 'कोड',
	CodeErrorMessage: 'कोड आवश्यक है',
	CodePlaceholder: 'कोड',
	Coinsurance: 'सह-बीमा',
	Collection: 'संग्रह',
	CollectionName: 'संग्रह का नाम',
	Collections: 'संग्रह',
	ColorAppointmentsBy: 'रंग नियुक्तियाँ',
	ColorTheme: 'रंग थीम',
	ColourCalendarBy: 'रंग कैलेंडर',
	ComingSoon: 'जल्द आ रहा है',
	Community: 'समुदाय',
	CommunityHealthLead: 'सामुदायिक स्वास्थ्य प्रमुख',
	CommunityHealthWorker: 'सामुदायिक स्वास्थ्य कार्यकर्ता',
	CommunityTemplatesSectionDescription: 'केयरपेट्रॉन समुदाय द्वारा निर्मित',
	CommunityTemplatesSectionTitle: 'समुदाय',
	CommunityUser: 'समुदाय उपयोगकर्ता',
	Complete: 'पूरा',
	CompleteAndLock: 'पूर्ण करें और लॉक करें',
	CompleteSetup: 'पूरा सेटअप',
	CompleteSetupSuccessDescription:
		'आपने Carepatron में महारत हासिल करने की दिशा में कुछ महत्वपूर्ण कदम पूरे कर लिए हैं।',
	CompleteSetupSuccessDescription2:
		'अपने अभ्यास को सुव्यवस्थित करने और अपने ग्राहकों का समर्थन करने के अधिक तरीके अनलॉक करें।',
	CompleteSetupSuccessTitle: 'सफलता! आप अद्भुत कर रहे हैं!',
	CompleteStripeSetup: 'स्ट्राइप सेटअप पूरा करें',
	Completed: 'पूर्ण',
	ComposeSms: 'एसएमएस लिखें',
	ComputerSystemsAnalyst: 'कंप्यूटर सिस्टम विश्लेषक',
	Confirm: 'पुष्टि करना',
	ConfirmDeleteAccountDescription:
		'आप अपना खाता हटाने जा रहे हैं। इस कार्रवाई को पूर्ववत नहीं किया जा सकता। यदि आप आगे बढ़ना चाहते हैं, तो कृपया नीचे पुष्टि करें।',
	ConfirmDeleteActionDescription: 'क्या आप वाकई इस कार्रवाई को हटाना चाहते हैं? इसे पूर्ववत नहीं किया जा सकता',
	ConfirmDeleteAutomationDescription:
		'क्या आप वाकई इस स्वचालन को हटाना चाहते हैं? इस कार्रवाई को पूर्ववत नहीं किया जा सकता।',
	ConfirmDeleteScheduleDescription:
		'<strong>{scheduleName}</strong> शेड्यूल हटाने से यह आपके शेड्यूल से हट जाएगा और इससे आपकी ऑनलाइन सेवा उपलब्ध हो सकती है. यह क्रिया वापस नहीं की जा सकती.',
	ConfirmDraftResponseContinue: 'प्रतिक्रिया जारी रखें',
	ConfirmDraftResponseDescription:
		'यदि आप यह पेज बंद कर देते हैं तो आपका जवाब ड्राफ्ट के रूप में रहेगा। आप किसी भी समय वापस आकर जारी रख सकते हैं।',
	ConfirmDraftResponseSubmitResponse: 'प्रतिक्रिया सबमिट करें',
	ConfirmDraftResponseTitle: 'आपका जवाब सबमिट नहीं किया गया है',
	ConfirmIfUserIsClientDescription: `आपके द्वारा भरा गया साइन-अप फॉर्म प्रदाताओं (अर्थात स्वास्थ्य टीम/संगठन) के लिए है।
 यदि यह गलती है तो आप "ग्राहक के रूप में जारी रखें" चुन सकते हैं और हम आपके ग्राहक पोर्टल के लिए सेटअप कर देंगे`,
	ConfirmIfUserIsClientNoButton: 'प्रदाता के रूप में साइन अप करें',
	ConfirmIfUserIsClientTitle: 'ऐसा लगता है कि आप ग्राहक हैं',
	ConfirmIfUserIsClientYesButton: 'ग्राहक के रूप में जारी रखें',
	ConfirmKeepSeparate: 'अलग रखें की पुष्टि करें',
	ConfirmMerge: 'मर्ज की पुष्टि करें',
	ConfirmPassword: 'पासवर्ड की पुष्टि कीजिये',
	ConfirmRevertClaim: 'हाँ, स्थिति वापस करें',
	ConfirmSignupAccessCode: 'पुष्टि कोड',
	ConfirmSignupButtom: 'पुष्टि करना',
	ConfirmSignupDescription: 'कृपया अपना ईमेल पता और वह पुष्टिकरण कोड दर्ज करें जो हमने आपको अभी भेजा है।',
	ConfirmSignupSubTitle: 'स्पैम फ़ोल्डर की जाँच करें - यदि ईमेल नहीं आया है',
	ConfirmSignupSuccessSnackbar:
		'बढ़िया, हमने आपके खाते की पुष्टि कर दी है! अब आप अपने ईमेल और पासवर्ड का उपयोग करके लॉगिन कर सकते हैं',
	ConfirmSignupTitle: 'खाते की पुष्टि करें',
	ConfirmSignupUsername: 'ईमेल',
	ConfirmSubscriptionUpdate:
		'सदस्यता की पुष्टि करें {price} {isMonthly, select, true {प्रति माह} other {प्रति वर्ष}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'एक बार ग्राहक हटा दिए जाने के बाद, आप उनकी जानकारी तक पहुंच नहीं पाएंगे।',
	ConfirmationModalBulkDeleteClientsTitleId: '{count, plural, one {# क्लाइंट} other {# क्लाइंट}} हटाएँ?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'एक बार संपर्क हटा दिए जाने के बाद, आप उनकी जानकारी तक पहुंच नहीं पाएंगे।',
	ConfirmationModalBulkDeleteContactsTitleId: '{count, plural, one {# संपर्क} other {# संपर्क}} हटाएँ?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'यह एक स्थायी कार्रवाई है। एक बार टीम के सदस्यों को हटा दिए जाने के बाद, आप उनकी जानकारी तक पहुँच नहीं पाएँगे।',
	ConfirmationModalBulkDeleteMembersTitleId: '{count, plural, one {# टीम सदस्य} other {# टीम सदस्य}} हटाएँ?',
	ConfirmationModalCloseOnGoingTranscription:
		'इस नोट को बंद करने से सभी चल रहे ट्रांसक्रिप्शन समाप्त हो जाएंगे। क्या आप वाकई आगे बढ़ना चाहते हैं?',
	ConfirmationModalDeleteClientField:
		'यह एक स्थायी कार्रवाई है। फ़ील्ड हटा दिए जाने के बाद यह आपके शेष क्लाइंट पर उपलब्ध नहीं रहेगी।',
	ConfirmationModalDeleteSectionMessage:
		'एक बार हटा दिए जाने के बाद, इस अनुभाग के सभी प्रश्न हटा दिए जाएंगे। इस क्रिया को पूर्ववत नहीं किया जा सकता।',
	ConfirmationModalDeleteService:
		'यह एक स्थायी कार्रवाई है। एक बार सेवा हटा दिए जाने के बाद यह आपके कार्यक्षेत्र पर उपलब्ध नहीं रहेगी।',
	ConfirmationModalDeleteServiceGroup:
		'किसी संग्रह को हटाने से समूह से सभी सेवाएँ हट जाएँगी और आपकी सेवा सूची में वापस आ जाएँगी। इस क्रिया को पूर्ववत नहीं किया जा सकता।',
	ConfirmationModalDeleteTranscript: 'क्या आप वाकई ट्रांसक्रिप्ट हटाना चाहते हैं?',
	ConfirmationModalDescriptionDeleteClient:
		'एक बार क्लाइंट हटा दिए जाने के बाद, आप क्लाइंट जानकारी तक पहुंच नहीं पाएंगे।',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'एक बार जब आप अपनी पहुंच हटा लेंगे, तो आप ग्राहक जानकारी नहीं देख पाएंगे।',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'उनकी प्रोफ़ाइल हटाई नहीं जाएगी, केवल इस ग्राहक के संबंध के रूप में हटा दी जाएगी।',
	ConfirmationModalDescriptionRemoveStaff: 'क्या आप वाकई इस व्यक्ति को प्रदाता से हटाना चाहते हैं?',
	ConfirmationModalEndSession: 'क्या आप वाकई सत्र समाप्त करना चाहते हैं?',
	ConfirmationModalTitle: 'क्या आपको यकीन है?',
	Confirmed: 'की पुष्टि',
	ConflictTimezoneWarningMessage: 'अनेक समयक्षेत्रों के कारण टकराव हो सकता है',
	Connect: 'जोड़ना',
	ConnectExistingClientOrContact: 'नया ग्राहक/संपर्क बनाएं',
	ConnectInboxGoogleDescription: 'Gmail खाता या Google समूह सूची जोड़ें',
	ConnectInboxMicrosoftDescription: 'Outlook, Office365 या Exchange खाता जोड़ें',
	ConnectInboxModalDescription:
		'अपने सभी संचारों को एक ही केंद्रीकृत स्थान पर निर्बाध रूप से भेजने, प्राप्त करने और ट्रैक करने के लिए अपने ऐप्स को कनेक्ट करें।',
	ConnectInboxModalExistingDescription:
		'कॉन्फ़िगरेशन प्रक्रिया को सरल बनाने के लिए अपने कनेक्टेड ऐप्स सेटिंग से मौजूदा कनेक्शन का उपयोग करें.',
	ConnectInboxModalExistingTitle: 'केयरपेट्रॉन में मौजूदा कनेक्टेड ऐप',
	ConnectInboxModalTitle: 'इनबॉक्स कनेक्ट करें',
	ConnectToStripe: 'स्ट्राइप से कनेक्ट करें',
	ConnectZoom: 'ज़ूम कनेक्ट करें',
	ConnectZoomModalDescription: 'अपने अपॉइंटमेंट के लिए वीडियो कॉल प्रबंधित करने के लिए केयरपेट्रॉन को अनुमति दें।',
	ConnectedAppDisconnectedNotificationSubject: '{account} खाते से कनेक्शन टूट गया है। कृपया दोबारा कनेक्ट करें',
	ConnectedAppSyncDescription:
		'Carepatron से सीधे तृतीय-पक्ष कैलेंडर में ईवेंट बनाने के लिए कनेक्टेड ऐप्स को प्रबंधित करें।',
	ConnectedApps: 'कनेक्टेड ऐप्स',
	ConnectedAppsGMailDescription: 'Gmail खाते या Google समूह सूची जोड़ें',
	ConnectedAppsGoogleCalendarDescription: 'कैलेंडर अकाउंट या Google ग्रुप लिस्ट जोड़ें',
	ConnectedAppsGoogleDescription: 'अपना Gmail खाता जोड़ें और Google कैलेंडर सिंक करें',
	ConnectedAppsMicrosoftDescription: 'Outlook, Office365 या Exchange खाता जोड़ें',
	ConnectedCalendars: 'कनेक्टेड कैलेंडर',
	ConsentDocumentation: 'फॉर्म और समझौते',
	ConsentDocumentationPublicTemplateError:
		'सुरक्षा कारणों से, आप केवल अपनी टीम (गैर-सार्वजनिक) से ही टेम्पलेट चुन सकते हैं।',
	ConstructionWorker: 'निर्माण मजदूर',
	Consultant: 'सलाहकार',
	Contact: 'संपर्क',
	ContactAccessTypeHelperText: 'परिवार व्यवस्थापकों को जानकारी अपडेट करने की अनुमति देता है',
	ContactAccessTypeHelperTextMoreInfo:
		'यह आपको {clientFirstName} के बारे में नोट्स/दस्तावेज़ साझा करने की अनुमति देगा',
	ContactAddressLabelBilling: 'बिलिंग',
	ContactAddressLabelHome: 'घर',
	ContactAddressLabelOthers: 'अन्य',
	ContactAddressLabelWork: 'काम',
	ContactChangeConfirmation:
		'इन्‍वॉइस संपर्क बदलने से <mark>{contactName}</mark> से संबंधित सभी लाइन आइटम हटा दिए जाएँगे',
	ContactDetails: 'सम्पर्क करने का विवरण',
	ContactEmailLabelOthers: 'अन्य',
	ContactEmailLabelPersonal: 'निजी',
	ContactEmailLabelSchool: 'विद्यालय',
	ContactEmailLabelWork: 'काम',
	ContactInformation: 'संपर्क जानकारी',
	ContactInformationText: 'संपर्क जानकारी',
	ContactListCreateButton: 'नया संपर्क',
	ContactName: 'संपर्क नाम',
	ContactPhoneLabelHome: 'घर',
	ContactPhoneLabelMobile: 'गतिमान',
	ContactPhoneLabelSchool: 'विद्यालय',
	ContactPhoneLabelWork: 'काम',
	ContactRelationship: 'संपर्क संबंध',
	ContactRelationshipFormAccessType: 'साझा की गई जानकारी तक पहुँच प्रदान करें',
	ContactRelationshipGrantAccessInfo: 'इससे आप नोट्स साझा कर सकेंगे ',
	ContactSupport: 'समर्थन से संपर्क करें',
	Contacts: 'संपर्क',
	ContainerIdNotSet: 'कंटेनर आईडी सेट नहीं है',
	Contemporary: 'समकालीन',
	Continue: 'जारी रखना',
	ContinueDictating: 'डिक्टेशन जारी रखें',
	ContinueEditing: 'संपादन जारी रखें',
	ContinueImport: 'इम्पोर्ट जारी रखें',
	ContinueTranscription: 'प्रतिलेखन जारी रखें',
	ContinueWithApple: 'एप्पल के साथ जारी रखें',
	ContinueWithGoogle: 'Google के साथ जारी रखें',
	Conversation: 'बातचीत',
	Copay: 'सह-भुगतान',
	CopayOrCoinsurance: 'सह-भुगतान या सह-बीमा',
	Copayment: 'सह-भुगतान',
	CopiedToClipboard: 'क्लिपबोर्ड पर कॉपी किया गया',
	Copy: 'प्रतिलिपि',
	CopyAddressSuccessSnackbar: 'क्लिपबोर्ड में पता कॉपी किया गया',
	CopyCode: 'कोड कॉपी करें',
	CopyCodeToClipboardSuccess: 'कोड क्लिपबोर्ड में कॉपी किया गया',
	CopyEmailAddressSuccessSnackbar: 'ईमेल पता क्लिपबोर्ड में कॉपी किया गया',
	CopyLink: 'लिंक की प्रतिलिपि करें',
	CopyLinkForCall: 'इस कॉल को साझा करने के लिए इस लिंक को कॉपी करें:',
	CopyLinkSuccessSnackbar: 'लिंक को क्लिपबोर्ड पर कॉपी किया गया',
	CopyMeetingLink: 'मीटिंग लिंक कॉपी करें',
	CopyPaymentLink: 'भुगतान लिंक कॉपी करें',
	CopyPhoneNumberSuccessSnackbar: 'क्लिपबोर्ड में फ़ोन नंबर कॉपी किया गया',
	CopyTemplateLink: 'टेम्पलेट का लिंक कॉपी करें',
	CopyTemplateLinkSuccess: 'लिंक को क्लिपबोर्ड पर कॉपी किया गया',
	CopyToClipboardError: 'क्लिपबोर्ड पर कॉपी नहीं किया जा सका। कृपया पुनः प्रयास करें।',
	CopyToTeamTemplates: 'टीम टेम्पलेट्स में कॉपी करें',
	CopyToWorkspace: 'कार्यस्थान पर कॉपी करें',
	Cosmetologist: 'cosmetologist',
	Cost: 'लागत',
	CostErrorMessage: 'लागत आवश्यक है',
	Counseling: 'काउंसिलिंग',
	Counselor: 'काउंसलर',
	Counselors: 'सलाहकार',
	CountInvoicesAdded: '{count, plural, one {# इनवॉइस जोड़ा गया} other {# इनवॉइस जोड़े गए}}',
	CountNotesAdded: '{count, plural, one {# नोट जोड़ा गया} other {# नोट जोड़े गए}}',
	CountSelected: '{count} चयनित',
	CountTimes: '{count} बार',
	Country: 'देश',
	Cousin: 'चचेरा',
	CoverageType: 'कवरेज प्रकार',
	Covered: 'ढका हुआ',
	Create: 'बनाएं',
	CreateANewClient: 'नया ग्राहक बनाएं',
	CreateAccount: 'खाता बनाएं',
	CreateAndSignNotes: 'ग्राहकों के साथ नोट बनाएं और हस्ताक्षर करें',
	CreateAvailabilityScheduleFailure: 'नया उपलब्धता शेड्यूल बनाने में विफल',
	CreateAvailabilityScheduleSuccess: 'नया उपलब्धता शेड्यूल सफलतापूर्वक बनाया गया',
	CreateBillingItems: 'बिलिंग आइटम बनाएं',
	CreateCallFormButton: 'कॉल शुरू करें',
	CreateCallFormInviteOnly: 'केवल आमंत्रित',
	CreateCallFormInviteOnlyMoreInfo:
		'इस कॉल में केवल आमंत्रित लोग ही शामिल हो सकते हैं। इस कॉल को दूसरों के साथ साझा करने के लिए, बस इसे अनचेक करें और अगले पेज पर लिंक को कॉपी/पेस्ट करें',
	CreateCallFormRecipients: 'प्राप्तकर्ता',
	CreateCallFormRegion: 'होस्टिंग क्षेत्र',
	CreateCallModalAddClientContactSelectorLabel: 'ग्राहक संपर्क',
	CreateCallModalAddClientContactSelectorPlaceholder: 'ग्राहक नाम से खोजें',
	CreateCallModalAddStaffSelectorLabel: 'टीम के सदस्य (वैकल्पिक)',
	CreateCallModalAddStaffSelectorPlaceholder: 'स्टाफ़ के नाम से खोजें',
	CreateCallModalDescription:
		'कॉल शुरू करें और स्टाफ़ सदस्यों और/या संपर्कों को आमंत्रित करें। वैकल्पिक रूप से, आप इस कॉल को Carepatron के साथ किसी के साथ साझा करने योग्य बनाने के लिए "निजी" बॉक्स को अनचेक कर सकते हैं',
	CreateCallModalTitle: 'कॉल शुरू करें',
	CreateCallModalTitleLabel: 'शीर्षक (वैकल्पिक)',
	CreateCallNoPersonIdToolTip: 'केवल पोर्टल एक्सेस वाले संपर्क/ग्राहक ही कॉल में शामिल हो सकते हैं',
	CreateClaim: 'दावा बनाएँ',
	CreateClaimCompletedMessage: 'आपका दावा बना दिया गया है.',
	CreateClientModalTitle: 'नए ग्राहक',
	CreateContactModalTitle: 'नया संपर्क',
	CreateContactRelationshipButton: 'संबंध जोड़ें',
	CreateContactSelectorDefaultOption: '  संपर्क बनाएं',
	CreateContactWithRelationshipFormAccessType: 'साझा की गई जानकारी तक पहुंच प्रदान करें ',
	CreateDocumentDnDPrompt: 'फ़ाइलें अपलोड करने के लिए खींचें और छोड़ें',
	CreateDocumentSizeLimit: 'प्रति फ़ाइल आकार सीमा {size}MB. कुल {total} फाइलें.',
	CreateFreeAccount: 'मुफ्त खाता बनाओ',
	CreateInvoice: 'चालान बनाएं',
	CreateLink: 'लिंक बनाएं',
	CreateNew: 'नया निर्माण',
	CreateNewAppointment: 'नई नियुक्ति बनाएं',
	CreateNewClaim: 'नया दावा बनाएँ',
	CreateNewClaimForAClient: 'क्लाइंट के लिए नया दावा बनाएं।',
	CreateNewClient: 'नया ग्राहक बनाएं',
	CreateNewConnection: 'नया कनेक्शन',
	CreateNewContact: 'नया संपर्क बनाएं',
	CreateNewField: 'नया फ़ील्ड बनाएँ',
	CreateNewLocation: 'नया स्थान',
	CreateNewService: 'नई सेवा बनाएं',
	CreateNewServiceGroupFailure: 'नया संग्रह बनाने में विफल',
	CreateNewServiceGroupMenu: 'नया संग्रह',
	CreateNewServiceGroupSuccess: 'नया संग्रह सफलतापूर्वक बनाया गया',
	CreateNewServiceMenu: 'नई सेवा',
	CreateNewTeamMember: 'नया टीम सदस्य बनाएं',
	CreateNewTemplate: 'नया टेम्पलेट',
	CreateNote: 'नोट बनाएँ',
	CreateSuperbillReceipt: 'नया सुपरबिल',
	CreateSuperbillReceiptSuccess: 'सुपरबिल रसीद सफलतापूर्वक बनाई गई',
	CreateTemplateFolderSuccessMessage: 'सफलतापूर्वक {folderTitle} बनाया गया',
	Created: 'बनाया था',
	CreatedAt: 'निर्मित {timestamp}',
	Credit: 'श्रेय',
	CreditAdded: 'क्रेडिट लागू',
	CreditAdjustment: 'क्रेडिट समायोजन',
	CreditAdjustmentReasonHelperText: 'यह एक आंतरिक नोट है और यह आपके ग्राहक को दिखाई नहीं देगा।',
	CreditAdjustmentReasonPlaceholder: 'बिल योग्य लेन-देन की समीक्षा करते समय समायोजन कारण जोड़ने से मदद मिल सकती है',
	CreditAmount: '{amount} एनसी',
	CreditBalance: 'क्रेडिट शेष',
	CreditCard: 'क्रेडिट कार्ड',
	CreditCardExpire: 'समाप्त होता है {exp_month}/{exp_year}',
	CreditCardNumber: 'क्रेडिट कार्ड नंबर',
	CreditDebitCard: 'कार्ड',
	CreditIssued: 'क्रेडिट जारी किया गया',
	CreditsUsed: 'क्रेडिट का उपयोग किया गया',
	Crop: 'काटना',
	Currency: 'मुद्रा',
	CurrentCredit: 'वर्तमान क्रेडिट',
	CurrentEventTime: 'वर्तमान घटना का समय',
	CurrentPlan: 'वर्तमान योजना',
	Custom: 'रिवाज़',
	CustomRange: 'कस्टम रेंज',
	CustomRate: 'कस्टम दर',
	CustomRecurrence: 'कस्टम पुनरावृत्ति',
	CustomServiceAvailability: 'सेवा की उपलब्धता',
	CustomerBalance: 'ग्राहक शेष',
	CustomerName: 'ग्राहक का नाम',
	CustomerNameIsRequired: 'ग्राहक का नाम आवश्यक है',
	CustomerServiceRepresentative: 'ग्राहक सेवा प्रतिनिधि',
	CustomiseAppointments: 'नियुक्तियों को अनुकूलित करें',
	CustomiseBookingLink: 'बुकिंग विकल्प अनुकूलित करें',
	CustomiseBookingLinkServicesInfo: 'ग्राहक केवल बुक करने योग्य सेवाएं ही चुन सकते हैं',
	CustomiseBookingLinkServicesLabel: 'सेवाएं',
	CustomiseClientRecordsAndWorkspace: 'अपने ग्राहक रिकॉर्ड और कार्यक्षेत्र को अनुकूलित करें',
	CustomiseClientSettings: 'क्लाइंट सेटिंग अनुकूलित करें',
	Customize: 'अनुकूलित करें',
	CustomizeAppearance: 'उपस्थिति अनुकूलित करें',
	CustomizeAppearanceDesc:
		'अपनी ऑनलाइन बुकिंग के स्वरूप को अपने ब्रांड के अनुरूप बनाएं तथा ग्राहकों को अपनी सेवाएं कैसे दिखाई जाएं, इसे अनुकूलित करें।',
	CustomizeClientFields: 'क्लाइंट फ़ील्ड अनुकूलित करें',
	CustomizeInvoiceTemplate: 'इनवॉइस टेम्पलेट को अनुकूलित करें',
	CustomizeInvoiceTemplateDescription: 'आसानी से पेशेवर चालान बनाएं जो आपके ब्रांड को प्रतिबिंबित करें।',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX आवश्यक है',
	Daily: 'दैनिक',
	DanceTherapist: 'नृत्य चिकित्सक',
	DangerZone: 'खतरा क्षेत्र',
	Dashboard: 'डैशबोर्ड',
	Date: 'तारीख',
	DateAndTime: 'तारीख ',
	DateDue: 'देय तिथि',
	DateErrorMessage: 'तारीख आवश्यक है',
	DateFormPrimaryText: 'तारीख',
	DateFormSecondaryText: 'दिनांक चयनकर्ता से चुनें',
	DateIssued: 'तिथि जारी',
	DateOfPayment: 'भुगतान की तिथि',
	DateOfService: 'सेवा की तिथि',
	DateOverride: 'तिथि ओवरराइड',
	DateOverrideColor: 'दिनांक ओवरराइड रंग',
	DateOverrideInfo:
		'तिथि ओवरराइड्स चिकित्सकों को नियमित शेड्यूल को ओवरराइड करके विशिष्ट तिथियों के लिए अपनी उपलब्धता को मैन्युअल रूप से समायोजित करने की अनुमति देता है।',
	DateOverrideInfoBanner:
		'इन समयावधियों में केवल इस तिथि के लिए निर्दिष्ट सेवाओं को ही बुक किया जा सकता है; अन्य किसी ऑनलाइन बुकिंग की अनुमति नहीं है।',
	DateOverrides: 'तिथि ओवरराइड',
	DatePickerFormPrimaryText: 'तारीख',
	DatePickerFormSecondaryText: 'एक तारीख चुनें',
	DateRange: 'तिथि सीमा',
	DateRangeFormPrimaryText: 'तिथि सीमा',
	DateRangeFormSecondaryText: 'दिनांक सीमा चुनें',
	DateReceived: 'प्राप्ति दिनांक',
	DateSpecificHours: 'तिथि विशिष्ट घंटे',
	DateSpecificHoursDescription:
		'जब आपकी उपलब्धता आपके निर्धारित घंटों से बदल जाए या किसी विशिष्ट तिथि पर सेवा प्रदान करने के लिए तिथियां जोड़ें।',
	DateUploaded: 'अपलोड किया गया {date, date, medium}',
	Dates: 'खजूर',
	Daughter: 'बेटी',
	Day: 'दिन',
	DayPlural: '{count, plural, one {दिन} other {दिन}}',
	Days: 'दिन',
	DaysPlural: '{age, plural, one {# दिन} other {# दिन}}',
	DeFacto: 'वास्तव में',
	Deactivated: 'निष्क्रिय',
	Debit: 'खर्चे में लिखना',
	DecreaseIndent: 'इंडेंट घटाएँ',
	Deductibles: 'कटौतियां',
	Default: 'गलती करना',
	DefaultBillingProfile: 'डिफ़ॉल्ट बिलिंग प्रोफ़ाइल',
	DefaultDescription: 'डिफ़ॉल्ट विवरण',
	DefaultEndOfLine: 'कोई और आइटम नहीं',
	DefaultInPerson: 'ग्राहक नियुक्तियाँ',
	DefaultInvoiceTitle: 'डिफ़ॉल्ट शीर्षक',
	DefaultNotificationSubject: 'आपको {notificationType} के लिए एक नया नोटिफिकेशन प्राप्त हुआ है',
	DefaultPaymentMethod: 'पूर्व निर्धारित भुगतान प्रणाली',
	DefaultService: 'डिफ़ॉल्ट सेवा',
	DefaultValue: 'गलती करना',
	DefaultVideo: 'क्लाइंट वीडियो अपॉइंटमेंट ईमेल',
	DefinedTemplateType: '{invoiceTemplate} टेम्पलेट',
	Delete: 'मिटाना',
	DeleteAccountButton: 'खाता हटा दो',
	DeleteAccountDescription: 'प्लेटफ़ॉर्म से अपना खाता हटाएं',
	DeleteAccountPanelInfoAlert:
		'आपको अपनी प्रोफ़ाइल हटाने से पहले अपने वर्कस्पेस को हटाना होगा। आगे बढ़ने के लिए, वर्कस्पेस पर स्विच करें और सेटिंग्स > वर्कस्पेस सेटिंग्स चुनें।',
	DeleteAccountTitle: 'खाता हटा दो',
	DeleteAppointment: 'अपॉइंटमेंट हटाएं',
	DeleteAppointmentDescription:
		'क्या आप वाकई इस अपॉइंटमेंट को हटाना चाहते हैं? आप इसे बाद में पुनर्स्थापित कर सकते हैं।',
	DeleteAvailabilityScheduleFailure: 'उपलब्धता शेड्यूल हटाने में विफल',
	DeleteAvailabilityScheduleSuccess: 'उपलब्धता शेड्यूल सफलतापूर्वक हटा दिया गया',
	DeleteBillable: 'बिल योग्य हटाएं',
	DeleteBillableConfirmationMessage:
		'क्या आप वाकई इस बिलयोग्य को हटाना चाहते हैं? यह क्रिया पूर्ववत नहीं की जा सकती।',
	DeleteBillingProfileConfirmationMessage: 'इससे बिलिंग प्रोफ़ाइल स्थायी रूप से हट जाएगी.',
	DeleteCardConfirmation:
		'यह एक स्थायी कार्रवाई है। एक बार कार्ड डिलीट हो जाने के बाद, आप इसे एक्सेस नहीं कर पाएंगे।',
	DeleteCategory: 'श्रेणी हटाएं (यह तब तक स्थायी नहीं है जब तक परिवर्तन सहेजे नहीं जाते)',
	DeleteClientEventConfirmationDescription: 'इसे स्थायी रूप से हटा दिया जाएगा.',
	DeleteClients: 'क्लाइंट हटाएँ',
	DeleteCollection: 'संग्रह हटाएं',
	DeleteColumn: 'स्तंभ हटाएं',
	DeleteConversationConfirmationDescription:
		'इस वार्तालाप को हमेशा के लिए मिटाएँ। इस कार्रवाई को पूर्ववत नहीं किया जा सकता।',
	DeleteConversationConfirmationTitle: 'वार्तालाप को हमेशा के लिए हटाएँ',
	DeleteExternalEventDescription: 'क्या आप यह अपॉइंटमेंट हटाना चाहते हैं?',
	DeleteFileConfirmationModalPrompt: 'एक बार डिलीट होने के बाद आप इस फ़ाइल को पुनः प्राप्त नहीं कर सकते।',
	DeleteFolder: 'फ़ोल्डर मिटाएँ',
	DeleteFolderConfirmationMessage:
		'क्या आप सुनिश्चित हैं कि आप इस फ़ोल्डर {name} को हटाना चाहते हैं? इस फ़ोल्डर के अंदर की सभी वस्तुएँ भी हटा दी जाएँगी. आप इसे बाद में पुनर्स्थापित कर सकते हैं.',
	DeleteForever: 'हमेशा के लिए हटाएं',
	DeleteInsurancePayerConfirmationMessage:
		'{payer} को हटाने से आपकी बीमा भुगतानकर्ताओं की सूची से इसे हटा दिया जाएगा। यह क्रिया स्थायी है और इसे पुनर्स्थापित नहीं किया जा सकता है।',
	DeleteInsurancePayerFailure: 'बीमा भुगतानकर्ता को हटाने में विफल',
	DeleteInsurancePolicyConfirmationMessage: 'इससे बीमा पॉलिसी स्थायी रूप से हटा दी जाएगी।',
	DeleteInvoiceConfirmationDescription:
		'इस कार्रवाई को पूर्ववत नहीं किया जा सकता। इससे चालान और उससे जुड़े सभी भुगतान हमेशा के लिए मिट जाएँगे।',
	DeleteLocationConfirmation:
		'किसी स्थान को हटाना एक स्थायी क्रिया है। एक बार जब आप इसे हटा देते हैं, तो आपके पास उस तक पहुँच नहीं होगी। इस क्रिया को पूर्ववत नहीं किया जा सकता।',
	DeletePayer: 'भुगतानकर्ता को हटाएँ',
	DeletePracticeWorkspace: 'अभ्यास कार्यस्थान हटाएं',
	DeletePracticeWorkspaceDescription: 'इस अभ्यास कार्यक्षेत्र को स्थायी रूप से हटाएँ',
	DeletePracticeWorkspaceFailedSnackbar: 'कार्यस्थान हटाना विफल',
	DeletePracticeWorkspaceModalCancelButton: 'हां, मेरी सदस्यता रद्द करें',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'अपने कार्यक्षेत्र को हटाने से पहले, आपको अपनी सदस्यता रद्द करनी होगी।',
	DeletePracticeWorkspaceModalConfirmButton: 'हां, कार्यस्थान को स्थायी रूप से हटाएं',
	DeletePracticeWorkspaceModalDescription:
		'{name} कार्यक्षेत्र स्थायी रूप से हटा दिया जाएगा और सभी टीम सदस्यों की पहुँच समाप्त हो जाएगी। हटाने से पहले आपको कोई भी महत्वपूर्ण डेटा या संदेश डाउनलोड कर लेना चाहिए। इस कार्रवाई को पूर्ववत नहीं किया जा सकता है।',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'यह निर्णय निम्नलिखित कारणों से लिया गया:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'कारण',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder:
		'कृपया हमें बताएं कि आप अपना खाता क्यों हटाना चाहते हैं।',
	DeletePracticeWorkspaceModalTitle: 'क्या आपको यकीन है?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'सभी टीम सदस्यों की पहुँच हटा दी गई है',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} सफलतापूर्वक हटवले गेले आहे',
	DeletePublicTemplateContent: 'इससे केवल सार्वजनिक टेम्पलेट ही हटेगा, आपकी टीम का टेम्पलेट नहीं।',
	DeleteRecurringAppointmentModalTitle: 'दोहराई जाने वाली अपॉइंटमेंट हटाएं',
	DeleteRecurringEventModalTitle: 'दोहराई जाने वाली मीटिंग हटाएं',
	DeleteRecurringReminderModalTitle: 'दोहराए जाने वाले अनुस्मारक हटाएं',
	DeleteRecurringTaskModalTitle: 'दोहराए जाने वाले कार्य को हटाएं',
	DeleteReminderConfirmation:
		'यह एक स्थायी कार्रवाई है। एक बार रिमाइंडर हटा दिए जाने के बाद, आप इसे एक्सेस नहीं कर पाएंगे। इसका असर सिर्फ़ नए अपॉइंटमेंट पर ही पड़ेगा',
	DeleteSection: 'अनुभाग हटाएं',
	DeleteSectionInfo:
		'सेक्शन **{section}** को हटाने से उसमें मौजूद सभी मौजूदा फ़ील्ड छुप जाएँगे। यह कार्रवाई पूर्ववत नहीं की जा सकती।',
	DeleteSectionWarning: 'कोर फ़ील्ड हटाए नहीं जा सकते और उन्हें मौजूदा सेक्शन **{section}** में ले जाया जाएगा।',
	DeleteServiceFailure: 'सेवा हटाने में विफल',
	DeleteServiceSuccess: 'सेवा सफलतापूर्वक हटा दी गई',
	DeleteStaffScheduleOverrideDescription:
		'{value} पर इस तिथि अधिलेखन को हटाने से यह आपके शेड्यूल से हट जाएगा और इससे आपकी ऑनलाइन सेवा उपलब्ध हो सकती है। यह कार्रवाई पूर्ववत नहीं की जा सकती है।',
	DeleteSuperbillConfirmationDescription:
		'इस कार्रवाई को पूर्ववत नहीं किया जा सकता। इससे सुपरबिल रसीद हमेशा के लिए मिट जाएगी।',
	DeleteSuperbillFailure: 'सुपरबिल रसीद हटाने में विफल',
	DeleteSuperbillSuccess: 'सुपरबिल रसीद सफलतापूर्वक हटा दी गई',
	DeleteTaxRateConfirmationDescription: 'क्या आप वाकई इस कर दर को हटाना चाहते हैं?',
	DeleteTemplateContent: 'इस एक्शन को वापस नहीं किया जा सकता',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} सफलतापूर्वक हटा दिया गया',
	DeleteTemplateSuccessMessage: '{templateTitle} सफलतापूर्वक हटा दिया गया',
	DeleteTemplateTitle: 'क्या आप वाकई इस टेम्पलेट को हटाना चाहते हैं?',
	DeleteTranscript: 'ट्रांसक्रिप्ट हटाएं',
	DeleteWorkspace: 'कार्यस्थान हटाएं',
	Deleted: 'हटाए गए',
	DeletedBy: 'द्वारा हटाया गया',
	DeletedContact: 'हटाया गया संपर्क',
	DeletedOn: 'दिनांक को हटा दिया गया',
	DeletedStatusLabel: 'हटाई गई स्थिति',
	DeletedUserTooltip: 'यह ग्राहक हटा दिया गया है',
	DeliveryMethod: 'वितरण विधि',
	Demo: 'डेमो',
	Denied: 'अस्वीकृत',
	Dental: 'चिकित्सकीय',
	DentalAssistant: 'दंत सहायक',
	DentalHygienist: 'दंत चिकित्सक',
	Dentist: 'दाँतों का डॉक्टर',
	Dentists: 'दंत चिकित्सकों',
	Description: 'विवरण',
	DescriptionMustNotExceed: 'वर्णन {max} वर्णों से अधिक नहीं होना चाहिए',
	DetailDurationWithStaff: '{duration} मिनट{staffName, select, null {} other { {staffName} के साथ}}',
	Details: 'विवरण',
	Devices: 'उपकरण',
	Diagnosis: 'निदान',
	DiagnosisAndBillingItems: 'निदान ',
	DiagnosisCode: 'निदान कोड',
	DiagnosisCodeErrorMessage: 'निदान कोड आवश्यक है',
	DiagnosisCodeSelectorPlaceholder: 'ICD-10 डायग्नोस्टिक कोड खोजें और जोड़ें',
	DiagnosisCodeSelectorTooltip:
		'निदान कोड का उपयोग बीमा प्रतिपूर्ति के लिए सुपरबिल रसीदों को स्वचालित करने के लिए किया जाता है',
	DiagnosticCodes: 'डायग्नोस्टिक कोड',
	Dictate: 'हुक्म',
	DictatingIn: 'हुक्म चलाना',
	Dictation: 'श्रुतलेख',
	DidNotAttend: 'में शामिल नहीं हुए',
	DidNotComplete: 'पूरा नहीं हुआ',
	DidNotProviderEnoughValue: 'पर्याप्त मूल्य प्रदान नहीं किया गया',
	DidntProvideEnoughValue: 'पर्याप्त मूल्य प्रदान नहीं किया गया',
	DieteticsOrNutrition: 'आहार विज्ञान या पोषण',
	Dietician: 'आहार विशेषज्ञ',
	Dieticians: 'आहार विशेषज्ञों',
	Dietitian: 'आहार विशेषज्ञ',
	DigitalSign: 'यहाँ हस्ताक्षर कीजिए:',
	DigitalSignHelp: '(चित्र बनाने के लिए क्लिक करें/नीचे दबाएँ)',
	DirectDebit: 'सीधे डेबिट',
	DirectTextLink: 'सीधा टेक्स्ट लिंक',
	Disable: 'अक्षम करना',
	DisabledEmailInfo: 'हम आपका ईमेल पता अपडेट नहीं कर सकते क्योंकि आपका खाता हमारे द्वारा प्रबंधित नहीं है',
	Discard: 'खारिज करना',
	DiscardChanges: 'परिवर्तनों को निरस्त करें',
	DiscardDrafts: 'ड्राफ्ट त्यागें',
	Disconnect: 'डिस्कनेक्ट',
	DisconnectAppConfirmation: 'क्या आप इस ऐप को डिस्कनेक्ट करना चाहते हैं?',
	DisconnectAppConfirmationDescription: 'क्या आप वाकई इस ऐप को डिस्कनेक्ट करना चाहते हैं?',
	DisconnectAppConfirmationTitle: 'ऐप डिस्कनेक्ट करें',
	Discount: 'छूट',
	DisplayCalendar: 'केयरपेट्रॉन में प्रदर्शित करें',
	DisplayName: 'प्रदर्शित होने वाला नाम',
	DisplayedToClients: 'ग्राहकों को प्रदर्शित',
	DiversionalTherapist: 'डायवर्सनल थेरेपिस्ट',
	DoItLater: 'इसे बाद में करें',
	DoNotImport: 'आयात न करें',
	DoNotSend: 'न भेजें',
	DoThisLater: 'इसे बाद में करें',
	DoYouWantToEndSession: 'क्या आप अपना सत्र जारी रखना चाहते हैं या अभी समाप्त करना चाहते हैं?',
	Doctor: 'चिकित्सक',
	Doctors: 'डॉक्टरों',
	DoesNotRepeat: 'दोहराता नहीं',
	DoesntWorkWellWithExistingTools: 'हमारे मौजूदा टूल या वर्कफ़्लो के साथ ठीक से काम नहीं करता',
	DogWalker: 'कुत्ते को वॉकर',
	Done: 'हो गया',
	DontAllowClientsToCancel: 'ग्राहकों को रद्द करने की अनुमति न दें',
	DontHaveAccount: 'क्या आपके पास खाता नहीं है?',
	DontSend: 'न भेजें',
	Double: 'दोहरा',
	DowngradeTo: '{plan} में डाउनग्रेड करें',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'क्षमा करें, आप अपनी योजना को डाउनग्रेड नहीं कर सकते क्योंकि आपकी टीम में बहुत अधिक सदस्य हैं। कृपया अपने प्रदाता से कुछ सदस्यों को हटाएँ और पुनः प्रयास करें।',
	Download: 'डाउनलोड करना',
	DownloadAsPdf: 'पीडीएफ के रूप में डाउनलोड करें',
	DownloadERA: 'ERA डाउनलोड करें',
	DownloadPDF: 'पीडीएफ डाउनलोड करें',
	DownloadTemplateFileName: 'Carepatron स्विचिंग टेम्पलेट.csv',
	DownloadTemplateTileDescription:
		'अपने क्लाइंट्स को व्यवस्थित करने और अपलोड करने के लिए हमारे स्प्रेडशीट टेम्पलेट का इस्तेमाल करें।',
	DownloadTemplateTileLabel: 'टेम्पलेट डाउनलोड करें',
	Downloads: '{number, plural, one {<span>#</span> डाउनलोड} other {<span>#</span> डाउनलोड}}',
	DoxyMe: 'डॉक्सी.मी',
	Draft: 'मसौदा',
	DraftResponses: 'मसौदा प्रतिक्रिया',
	DraftSaved: 'सहेजे गए परिवर्तन',
	DragAndDrop: 'खींचें और छोड़ें',
	DragDropText: 'स्वास्थ्य दस्तावेज़ों को खींचें और छोड़ें',
	DragToMove: 'स्थानांतरित करने के लिए खींचें',
	DragToMoveOrActivate: 'स्थानांतरित करने या सक्रिय करने के लिए खींचें',
	DramaTherapist: 'ड्रामा थेरेपिस्ट',
	DropdownFormFieldPlaceHolder: 'सूची से विकल्प चुनें',
	DropdownFormPrimaryText: 'ड्रॉप डाउन',
	DropdownFormSecondaryText: 'विकल्पों की सूची में से चुनें',
	DropdownTextFieldError: 'ड्रॉपडाउन विकल्प पाठ रिक्त नहीं हो सकता',
	DropdownTextFieldPlaceholder: 'ड्रॉपडाउन विकल्प जोड़ें',
	Due: 'नियत तारीख',
	DueDate: 'नियत तारीख',
	Duplicate: 'डुप्लिकेट',
	DuplicateAvailabilityScheduleFailure: 'उपलब्धता शेड्यूल की प्रतिलिपि बनाने में विफल',
	DuplicateAvailabilityScheduleSuccess: '{name} शेड्यूल सफलतापूर्वक डुप्लिकेट केला.',
	DuplicateClientBannerAction: 'समीक्षा',
	DuplicateClientBannerDescription:
		'डुप्लिकेट क्लाइंट रिकॉर्ड को मर्ज करने से उन्हें एक में एकीकृत कर दिया जाता है, तथा सभी अद्वितीय क्लाइंट जानकारी सुरक्षित रहती है।',
	DuplicateClientBannerTitle: '{count} डुप्लिकेट मिले',
	DuplicateColumn: 'डुप्लिकेट कॉलम',
	DuplicateContactFieldSettingErrorSnackbar: 'अनुभाग नाम डुप्लिकेट नहीं हो सकते',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'डुप्लिकेट फ़ील्ड नाम नहीं हो सकते',
	DuplicateEmailError: 'डुप्लिकेट ईमेल',
	DuplicateHeadingName: 'खंड {name} पहले से मौजूद है',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'समान "इनवॉइस संख्या" वाला एक इनवॉइस पहले से मौजूद है।',
	DuplicateRecords: 'डुप्लिकेट रिकॉर्ड',
	DuplicateRecordsMinimumError: 'कम से कम 2 रिकॉर्ड का चयन किया जाना चाहिए',
	DuplicateRecordsRequired: 'अलग करने के लिए कम से कम 1 रिकॉर्ड चुनें',
	DuplicateServiceFailure: '**{title}** की नकल करने में विफल रहा',
	DuplicateServiceSuccess: 'सफलतापूर्वक <strong>{title}</strong> की नकल की गई',
	DuplicateTemplateFolderSuccessMessage: 'फ़ोल्डर सफलतापूर्वक डुप्लिकेट किया गया',
	DuplicateTemplateSuccess: 'टेम्पलेट सफलतापूर्वक डुप्लिकेट किया गया',
	DurationInMinutes: '{duration} मिनट',
	Dx: 'डीएक्स',
	DxCode: 'डीएक्स कोड',
	DxCodeSelectPlaceholder: 'ICD-10 कोड खोजें और जोड़ें',
	EIN: 'ईआईऍन',
	EMG: 'ईएमजी',
	EPSDT: 'ईपीएसडीटी',
	EPSDTPlaceholder: 'कोई नहीं',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>समायोजन शामिल हैं</i>} other {}}',
	EarnReferralCredit: '${creditAmount} कमाएँ',
	Economist: 'अर्थशास्त्री',
	Edit: 'संपादन करना',
	EditArrangements: 'व्यवस्था संपादित करें',
	EditBillTo: 'बिल संपादित करें',
	EditClient: 'क्लाइंट संपादित करें',
	EditClientFileModalDescription:
		'"द्वारा देखे जा सकने वाले" चेकबॉक्स में विकल्प चुनकर इस फ़ाइल तक पहुंच संपादित करें',
	EditClientFileModalTitle: 'फ़ाइल संपादित करें',
	EditClientNoteModalDescription:
		'नोट में सामग्री संपादित करें। नोट को कौन देख सकता है, इसे बदलने के लिए "द्वारा देखा जा सकने वाला" अनुभाग का उपयोग करें।',
	EditClientNoteModalTitle: 'नोट संपादित करें',
	EditConnectedAppButton: 'संपादन करना',
	EditConnections: 'संबंध संपादित करें{account, select, null { } undefined { } other { के लिए {account}}}',
	EditContactDetails: 'संपर्क विवरण संपादित करें',
	EditContactFormIsClientLabel: 'क्लाइंट में परिवर्तित करें',
	EditContactIsClientCheckboxWarning: 'किसी संपर्क को क्लाइंट में परिवर्तित करना पूर्ववत नहीं किया जा सकता',
	EditContactIsClientWanringModal:
		'इस संपर्क को क्लाइंट में बदलना पूर्ववत नहीं किया जा सकता। हालाँकि, सभी संबंध अभी भी बने रहेंगे और अब आपके पास उनके नोट्स, फ़ाइलों और अन्य दस्तावेज़ों तक पहुँच होगी।',
	EditContactRelationship: 'संपर्क संबंध संपादित करें',
	EditDetails: 'विवरण संपादित करें',
	EditFileModalTitle: '{name} के लिए फ़ाइल संपादित करें',
	EditFolder: 'फ़ोल्डर संपादित करें',
	EditFolderDescription: 'फ़ोल्डर का नाम बदलें...',
	EditInvoice: 'चालान संपादित करें',
	EditInvoiceDetails: 'चालान विवरण संपादित करें',
	EditLink: 'लिंक संपादित करें',
	EditLocation: 'स्थान संपादित करें',
	EditLocationFailure: 'स्थान अपडेट करने में विफल',
	EditLocationSucess: 'स्थान सफलतापूर्वक अपडेट किया गया',
	EditPaymentDetails: 'भुगतान विवरण संपादित करें',
	EditPaymentMethod: 'भुगतान विधि संपादित करें',
	EditPersonalDetails: 'व्यक्तिगत विवरण संपादित करें',
	EditPractitioner: 'संपादित करें व्यवसायी',
	EditProvider: 'प्रदाता संपादित करें',
	EditProviderDetails: 'प्रदाता विवरण संपादित करें',
	EditRecurrence: 'दोहराव संपादित करें',
	EditRecurringAppointmentModalTitle: 'दोहराई जाने वाली नियुक्ति संपादित करें',
	EditRecurringEventModalTitle: 'दोहराई जाने वाली मीटिंग संपादित करें',
	EditRecurringReminderModalTitle: 'दोहराए जाने वाले अनुस्मारक को संपादित करें',
	EditRecurringTaskModalTitle: 'दोहराए जाने वाले कार्य को संपादित करें',
	EditRelationshipModalTitle: 'संबंध संपादित करें',
	EditService: 'सेवा संपादित करें',
	EditServiceFailure: 'नई सेवा अपडेट करने में विफल',
	EditServiceGroup: 'संग्रह संपादित करें',
	EditServiceGroupFailure: 'संग्रह अपडेट करने में विफल',
	EditServiceGroupSuccess: 'संग्रह सफलतापूर्वक अपडेट किया गया',
	EditServiceSuccess: 'नई सेवा सफलतापूर्वक अपडेट की गई',
	EditStaffDetails: 'स्टाफ विवरण संपादित करें',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'ईमेल पता अपडेट नहीं किया जा सकता। कृपया नए ईमेल पते के साथ एक नया टीम सदस्य बनाएँ।',
	EditSubscriptionBilledQuantity: 'बिल की गई मात्रा',
	EditSubscriptionBilledQuantityValue: '{billedUsers} टीम के सदस्य',
	EditSubscriptionLimitedTimeOffer: 'सीमित समय का प्रस्ताव! 6 महीने के लिए 50% की छूट।',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'टीम के सदस्यों को जोड़ने या हटाने पर आपकी सदस्यता की लागत समायोजित की जाएगी।',
	EditSubscriptionUpgradeContent:
		'आपका खाता तुरंत नई योजना और बिलिंग अवधि में अपडेट हो जाएगा।  किसी भी कीमत में बदलाव को स्वचालित रूप से आपके सहेजे गए भुगतान विधि से लिया जाएगा या आपके खाते में जमा कर दिया जाएगा।',
	EditSubscriptionUpgradePlanTitle: 'सदस्यता योजना अपग्रेड करें',
	EditSuperbillReceipt: 'सुपरबिल संपादित करें',
	EditTags: 'जोड़ संपादित करें',
	EditTemplate: 'टेम्पलेट संपादित करें',
	EditTemplateFolderSuccessMessage: 'टेम्पलेट फ़ोल्डर सफलतापूर्वक संपादित किया गया',
	EditValue: 'संपादित करें {value}',
	Edited: 'संपादित',
	Editor: 'संपादक',
	EditorAlertDescription:
		'एक असमर्थित प्रारूप का पता चला है। ऐप को पुनः लोड करें या हमारी सहायता टीम से संपर्क करें।',
	EditorAlertTitle: 'हमें यह सामग्री प्रदर्शित करने में समस्या आ रही है',
	EditorPlaceholder:
		'लिखना शुरू करें, एक टेम्पलेट चुनें या अपने ग्राहकों से उत्तर प्राप्त करने के लिए बुनियादी ब्लॉक जोड़ें।',
	EditorTemplatePlaceholder: 'टेम्पलेट बनाने के लिए लिखना शुरू करें या घटक जोड़ें',
	EditorTemplateWithSlashCommandPlaceholder:
		'क्लाइंट प्रतिक्रियाएँ कैप्चर करने के लिए लिखना शुरू करें या बेसिक ब्लॉक जोड़ें। त्वरित कार्रवाइयों के लिए स्लैश कमांड (/) का उपयोग करें।',
	EditorWithSlashCommandPlaceholder:
		'लिखना शुरू करें, कोई टेम्पलेट चुनें, या क्लाइंट की प्रतिक्रियाएँ प्राप्त करने के लिए बुनियादी ब्लॉक जोड़ें। त्वरित कार्रवाई के लिए स्लैश कमांड ( / ) का उपयोग करें।',
	EffectiveStartEndDate: 'प्रभावी प्रारंभ - समाप्ति तिथि',
	ElectricalEngineer: 'विद्युत इंजीनियर',
	Electronic: 'इलेक्ट्रॉनिक',
	ElectronicSignature: 'इलेक्ट्रॉनिक हस्ताक्षर',
	ElementarySchoolTeacher: 'प्राथमिक विद्यालय शिक्षक',
	Eligibility: 'योग्यता',
	Email: 'ईमेल',
	EmailAlreadyExists: 'ईमेल पता पहले से मौजूद है',
	EmailAndSms: 'ईमेल ',
	EmailBody: 'ईमेल बॉडी',
	EmailContainsIgnoredDescription:
		'निम्न ईमेल में एक प्रेषक/प्रेषकों का ईमेल शामिल है जिसे वर्तमान में अनदेखा किया गया है। क्या आप जारी रखना चाहते हैं?',
	EmailInviteToPortalBody: `नमस्ते {contactName},
कृपया आपल्या सुरक्षित क्लायंट पोर्टलमध्ये साइन इन करण्यासाठी आणि सहजपणे आपली काळजी व्यवस्थापित करण्यासाठी या लिंकचे अनुसरण करा.

सादर,

{providerName}`,
	EmailInviteToPortalSubject: '{providerName} में आपका स्वागत है',
	EmailInvoice: 'ईमेल चालान',
	EmailInvoiceOverdueBody: `नमस्ते {contactName}
आपका चालान {invoiceNumber} देर से भुगतान हुआ है।
कृपया नीचे दिए गए लिंक का उपयोग करके ऑनलाइन अपने चालान का भुगतान करें।

यदि आपके कोई प्रश्न हैं, तो कृपया हमें बताएं।

धन्यवाद,
{providerName}`,
	EmailInvoicePaidBody: `नमस्ते {contactName}
आपका चालान {invoiceNumber} का भुगतान हो गया है।
अपने चालान की एक प्रति देखने और डाउनलोड करने के लिए नीचे दिए गए लिंक का पालन करें।

यदि आपके कोई प्रश्न हैं, तो कृपया हमें बताएं।

धन्यवाद,
{providerName}`,
	EmailInvoiceProcessingBody: `नमस्ते {contactName}
आपका बिल {invoiceNumber} तैयार है।
अपना बिल देखने के लिए नीचे दिए गए लिंक पर क्लिक करें।

अगर आपके कोई सवाल हैं, तो कृपया हमें बताएं।

धन्यवाद,
{providerName}`,
	EmailInvoiceUnpaidBody: `नमस्ते {contactName}
आपका बिल {invoiceNumber} तैयार है, जिसका भुगतान {dueDate} तक किया जाना है।
ऑनलाइन अपने बिल को देखने और भुगतान करने के लिए नीचे दिए गए लिंक का पालन करें।

यदि आपके कोई प्रश्न हैं, तो कृपया हमें बताएं।

धन्यवाद,
{providerName}`,
	EmailInvoiceVoidBody: `नमस्ते {contactName}
आपका चालान {invoiceNumber} रद्द हो गया है।
इस चालान को देखने के लिए नीचे दिए गए लिंक का पालन करें।

अगर आपके कोई प्रश्न हैं, तो कृपया हमें बताएं।

धन्यवाद,
{providerName}`,
	EmailNotFound: 'ईमेल नहीं मिला',
	EmailNotVerifiedErrorCodeSnackbar: 'कार्रवाई करने में असमर्थ। आपको अपना ईमेल पता सत्यापित करना होगा।',
	EmailNotVerifiedTitle: 'आपका ईमेल सत्यापित नहीं है। कुछ सुविधाएँ सीमित होंगी।',
	EmailSendClientIntakeBody: `नमस्ते {contactName},
{providerName} आपसे कुछ जानकारी प्रदान करने और महत्वपूर्ण दस्तावेज़ों की समीक्षा करने का अनुरोध करता है। कृपया आरंभ करने के लिए नीचे दिए गए लिंक का पालन करें।

साभार,

{providerName}`,
	EmailSendClientIntakeSubject: '{providerName} में आपका स्वागत है',
	EmailSuperbillReceipt: 'ईमेल सुपरबिल',
	EmailSuperbillReceiptBody: `नमस्ते {contactName},
{providerName} ने आपको आपकी प्रतिपूर्ति रसीद का विवरण {date} भेजा है।

आप इसे सीधे अपनी बीमा कंपनी को डाउनलोड और सबमिट कर सकते हैं।`,
	EmailSuperbillReceiptFailure: 'सुपरबिल रसीद भेजने में विफल',
	EmailSuperbillReceiptSubject: '{providerName} ने प्रतिपूर्ति प्राप्ति का विवरण भेजा है',
	EmailSuperbillReceiptSuccess: 'सुपरबिल रसीद सफलतापूर्वक भेजी गई',
	EmailVerificationDescription: 'हम अभी आपके खाते का <span>सत्यापन</span> कर रहे हैं',
	EmailVerificationNotification: 'एक सत्यापन ईमेल {email} पर भेजा गया है',
	EmailVerificationSuccess: 'आपका ईमेल पता सफलतापूर्वक {email} में बदल दिया गया है',
	Emails: 'ईमेल',
	EmergencyContact: 'आपातकालीन संपर्क',
	EmployeesIdentificationNumber: 'कर्मचारी पहचान संख्या',
	EmploymentStatus: 'रोज़गार की स्थिति',
	EmptyAgendaViewDescription: 'प्रदर्शित करने के लिए कोई घटना नहीं.<mark> अभी अपॉइंटमेंट बनाएं</mark>',
	EmptyBin: 'खाली डिब्बा',
	EmptyBinConfirmationDescription:
		'खाली बिन हटाई गेल्यातील सर्व **{total} संभाषणे** काढून टाकेल. हे कृती परत रद्द करता येत नाही.',
	EmptyBinConfirmationTitle: 'बातचीत को हमेशा के लिए मिटाएँ',
	EmptyTrash: 'कचरा खाली करें',
	Enable: 'सक्षम',
	EnableCustomServiceAvailability: 'सेवा उपलब्धता सक्षम करें',
	EnableCustomServiceAvailabilityDescription:
		'उदाहरणार्थ, प्रारंभिक अपॉइंटमेंट केवल प्रतिदिन सुबह 9-10 बजे तक ही बुक किए जा सकते हैं।',
	EndCall: 'कॉल समाप्त करें',
	EndCallConfirmationForCreator: 'आप इसे सभी के लिए समाप्त कर देंगे क्योंकि आप कॉल के आरंभकर्ता हैं।',
	EndCallConfirmationHasActiveAttendees:
		'आप कॉल समाप्त करने वाले हैं लेकिन क्लाइंट पहले ही शामिल हो चुके हैं। क्या आप भी शामिल होना चाहते हैं?',
	EndCallForAll: 'सभी के लिए कॉल समाप्त',
	EndDate: 'अंतिम तिथि',
	EndDictation: 'श्रुतलेख समाप्त करें',
	EndOfLine: 'अब कोई नियुक्ति नहीं',
	EndSession: 'सत्र समाप्त',
	EndTranscription: 'प्रतिलेखन समाप्त',
	Ends: 'समाप्त',
	EndsOnDate: '{date} को समाप्त होता है',
	Enrol: 'नामांकन',
	EnrollmentRejectedSubject: 'आपका {payerName} के साथ नामांकन अस्वीकार कर दिया गया है',
	Enrolment: 'प्रवेश',
	Enrolments: 'नामांकन',
	EnrolmentsDescription: 'पेयर के साथ प्रदाता नामांकन देखें और प्रबंधित करें।',
	EnterAName: 'नाम डालें...',
	EnterFieldLabel: 'फ़ील्ड लेबल दर्ज करें...',
	EnterPaymentDetailsDescription:
		'उपयोगकर्ताओं को जोड़ने या हटाने पर आपकी सदस्यता लागत स्वचालित रूप से समायोजित हो जाएगी.',
	EnterSectionName: 'अनुभाग का नाम दर्ज करें...',
	EnterSubscriptionPaymentDetails: 'भुगतान विवरण दर्ज करें',
	EnvironmentalScientist: 'पर्यावरण वैज्ञानिक',
	Epidemiologist: 'महामारी',
	Eraser: 'रबड़',
	Error: 'गलती',
	ErrorBoundaryAction: 'पृष्ठ पुनः लोड करें',
	ErrorBoundaryDescription: 'पृष्ठ को रीफ्रेश करें और पुन: प्रयास करें।',
	ErrorBoundaryTitle: 'ओह! कुछ ग़लत हो गया',
	ErrorCallNotFound:
		'कॉल नहीं मिल पा रही है। हो सकता है कि इसकी समय-सीमा समाप्त हो गई हो या फिर निर्माता ने इसे समाप्त कर दिया हो।',
	ErrorCannotAccessCallUninvitedCode: 'क्षमा करें, ऐसा लगता है कि आपको इस कॉल में आमंत्रित नहीं किया गया है।',
	ErrorFileUploadCustomMaxFileCount: 'एक साथ {count} से ज़्यादा फ़ाइलें अपलोड नहीं कर सकते',
	ErrorFileUploadCustomMaxFileSize: 'फ़ाइल का आकार {mb} MB से ज़्यादा नहीं हो सकता',
	ErrorFileUploadInvalidFileType: 'अमान्य फ़ाइल प्रकार जिसमें संभावित वायरस और हानिकारक सॉफ़्टवेयर हो सकते हैं',
	ErrorFileUploadMaxFileCount: 'एक बार में 150 से अधिक फ़ाइलें अपलोड नहीं की जा सकतीं',
	ErrorFileUploadMaxFileSize: 'फ़ाइल का आकार 100 MB से अधिक नहीं हो सकता',
	ErrorFileUploadNoFileSelected: 'कृपया अपलोड करने के लिए फ़ाइलें चुनें',
	ErrorInvalidNationalProviderId: 'दिया गया राष्ट्रीय प्रदाता आईडी मान्य नहीं है',
	ErrorInvalidPayerId: 'दिया गया भुगतानकर्ता आईडी मान्य नहीं है',
	ErrorInvalidTaxNumber: 'प्रदान किया गया टैक्स नंबर मान्य नहीं है',
	ErrorInviteExistingProviderStaffCode: 'यह यूज़र पहले से ही वर्कस्पेस में है।',
	ErrorInviteStaffExistingUser:
		'क्षमा करें, ऐसा लगता है कि आपके द्वारा जोड़ा गया उपयोगकर्ता पहले से ही हमारे सिस्टम में मौजूद है।',
	ErrorOnlySingleCallAllowed:
		'आप एक समय में केवल एक ही कॉल कर सकते हैं। कृपया नई कॉल शुरू करने के लिए वर्तमान कॉल समाप्त करें।',
	ErrorPayerNotFound: 'भुगतानकर्ता नहीं मिला',
	ErrorProfilePhotoMaxFileSize: 'अपलोड विफल! फ़ाइल आकार सीमा तक पहुँच गया - 5MB',
	ErrorRegisteredExistingUser: 'क्षमा करें, ऐसा लगता है कि आप पहले से ही पंजीकृत हैं।',
	ErrorUserSignInIncorrectCredentials: 'ईमेल या पासवर्ड अमान्य है। कृपया पुनः प्रयास करें।',
	ErrorUserSigninGeneric: 'क्षमा करें, कुछ ग़लत हो गया।',
	ErrorUserSigninUserNotConfirmed:
		'क्षमा करें, साइन इन करने से पहले आपको अपने खाते की पुष्टि करनी होगी। निर्देशों के लिए अपना इनबॉक्स देखें।',
	Errors: 'त्रुटियाँ',
	EssentialPlanInclusionFive: 'टेम्पलेट आयात',
	EssentialPlanInclusionFour: '5 जीबी स्टोरेज',
	EssentialPlanInclusionHeader: 'सब कुछ मुफ़्त में  ',
	EssentialPlanInclusionOne: 'स्वचालित और कस्टम रिमाइंडर',
	EssentialPlanInclusionSix: 'प्राथमिकता समर्थन',
	EssentialPlanInclusionThree: 'वीडियो चैट',
	EssentialPlanInclusionTwo: '2-तरफ़ा कैलेंडर सिंक',
	EssentialSubscriptionPlanSubtitle: 'आवश्यक चीजों के साथ अपने अभ्यास को सरल बनाएं',
	EssentialSubscriptionPlanTitle: 'आवश्यक',
	Esthetician: 'esthetician',
	Estheticians: 'एस्थेटिशियन',
	EstimatedArrivalDate: 'अनुमानित आगमन {numberOfDaysFromNow}',
	Ethnicity: 'जातीयता',
	Europe: 'यूरोप',
	EventColor: 'बैठक का रंग',
	EventName: 'घटना नाम',
	EventType: 'इवेंट का प्रकार',
	Every: 'हर',
	Every2Weeks: 'प्रत्येक 2 हफ्ते',
	EveryoneInWorkspace: 'कार्यस्थल पर मौजूद हर व्यक्ति',
	ExercisePhysiologist: 'व्यायाम फिजियोलॉजिस्ट',
	Existing: 'मौजूदा',
	ExistingClients: 'मौजूदा ग्राहक',
	ExistingFolders: 'मौजूदा फ़ोल्डर्स',
	ExpiredPromotionCode: 'प्रमोशन कोड की समयसीमा समाप्त हो गई है',
	ExpiredReferralDescription: 'रेफरल की अवधि समाप्त हो गई है',
	ExpiredVerificationLink: 'सत्यापन लिंक की समय-सीमा समाप्त हो गई',
	ExpiredVerificationLinkDescription: `हमें खेद है, लेकिन आपने जिस सत्यापन लिंक पर क्लिक किया था, उसकी वैधता समाप्त हो गई है। ऐसा तब हो सकता है जब आपने लिंक पर क्लिक करने के लिए 24 घंटे से ज़्यादा इंतज़ार किया हो या फिर आपने अपने ईमेल पते को सत्यापित करने के लिए पहले ही लिंक का इस्तेमाल कर लिया हो।

 कृपया अपना ईमेल पता सत्यापित करने के लिए एक नया सत्यापन लिंक का अनुरोध करें।`,
	ExpiryDateRequired: 'समाप्ति तिथि आवश्यक है',
	ExploreFeature: 'आप सबसे पहले क्या जानना चाहेंगे?',
	ExploreOptions: 'अन्वेषण हेतु एक या अधिक विकल्प चुनें...',
	Export: 'निर्यात',
	ExportAppointments: 'नियुक्तियाँ निर्यात करें',
	ExportClaims: 'एक्सपोर्ट क्लेम',
	ExportClaimsFilename: 'दावा {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'किसी त्रुटि के कारण आपकी फ़ाइल डाउनलोड नहीं की जा सकी.',
	ExportClientsDownloadFailureSnackbarTitle: 'डाउनलोड विफल',
	ExportClientsFailureSnackbarDescription: 'किसी त्रुटि के कारण आपकी फ़ाइल सफलतापूर्वक निर्यात नहीं की जा सकी.',
	ExportClientsFailureSnackbarTitle: 'निर्यात विफल',
	ExportClientsModalDescription: `इस डेटा निर्यात प्रक्रिया में निर्यात किए जा रहे डेटा की मात्रा के आधार पर कुछ मिनट लग सकते हैं। डाउनलोड के लिए तैयार होने पर आपको एक लिंक के साथ एक ईमेल सूचना प्राप्त होगी।

 क्या आप ग्राहक डेटा निर्यात करना चाहते हैं?`,
	ExportClientsModalTitle: 'ग्राहक डेटा निर्यात करें',
	ExportCms1500: 'CMS1500 निर्यात करें',
	ExportContactFailedNotificationSubject: 'आपका डेटा निर्यात विफल हो गया है',
	ExportFailed: 'निर्यात विफल',
	ExportGuide: 'निर्यात गाइड',
	ExportInvoiceFileName: 'लेनदेन {fromDate}-{toDate}.csv',
	ExportPayments: 'निर्यात भुगतान',
	ExportPaymentsFilename: 'भुगतान {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'आपका दस्तावेज़ डाउनलोड के लिए तैयार है.',
	ExportPrintWaitMessage: 'आपका दस्तावेज़ तैयार हो रहा है। कृपया प्रतीक्षा करें...',
	ExportTextOnly: 'केवल पाठ निर्यात करें',
	ExportTransactions: 'लेनदेन निर्यात करें',
	Exporting: 'निर्यात',
	ExportingData: 'डेटा निर्यात करना',
	ExtendedFamilyMember: 'विस्तारित परिवार का सदस्य',
	External: 'बाहरी',
	ExternalEventInfoBanner: 'यह अपॉइंटमेंट सिंक किए गए कैलेंडर से है और इसमें कुछ चीजें गायब हो सकती हैं।',
	ExtraLarge: 'एक्स्ट्रा लार्ज',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'असफल',
	FailedToJoinTheMeeting: 'मीटिंग में शामिल होने में असफल.',
	FallbackPageDescription: `ऐसा लग रहा है कि यह पृष्ठ मौजूद नहीं है, आपको नवीनतम परिवर्तन प्राप्त करने के लिए इस पृष्ठ को {refreshButton} करने की आवश्यकता हो सकती है।
अन्यथा, कृपया Carepatron सहायता से संपर्क करें।`,
	FallbackPageDescriptionUpdateButton: 'ताज़ा',
	FallbackPageTitle: 'उफ़...',
	FamilyPlanningService: 'परिवार नियोजन सेवा',
	FashionDesigner: 'फैशन डिजाइनर',
	FastTrackInvoicingAndBilling: 'अपने चालान और बिलिंग को तेजी से ट्रैक करें',
	Father: 'पिता',
	FatherInLaw: 'ससुर',
	Favorite: 'पसंदीदा',
	FeatureBannerCalendarTile1ActionLabel: 'ऑनलाइन बुकिंग • 2 मिनट',
	FeatureBannerCalendarTile1Description: 'बस ईमेल, टेक्स्ट भेजें या अपनी वेबसाइट पर उपलब्धता जोड़ें',
	FeatureBannerCalendarTile1Title: 'अपने ग्राहकों को ऑनलाइन बुकिंग करने में सक्षम बनाएं',
	FeatureBannerCalendarTile2ActionLabel: 'अनुस्मारक स्वचालित करें • 2 मिनट',
	FeatureBannerCalendarTile2Description: 'स्वचालित अनुस्मारक के साथ ग्राहक उपस्थिति बढ़ाएँ',
	FeatureBannerCalendarTile2Title: 'अनुपस्थिति को कम करें',
	FeatureBannerCalendarTile3Title: 'शेड्यूलिंग और वर्कफ़्लो',
	FeatureBannerCalendarTitle: 'शेड्यूलिंग को आसान बनाएं',
	FeatureBannerCallsTile1ActionLabel: 'टेलीहेल्थ कॉल शुरू करें',
	FeatureBannerCallsTile1Description: 'सिर्फ़ एक लिंक से क्लाइंट एक्सेस। कोई लॉगिन, पासवर्ड या परेशानी नहीं',
	FeatureBannerCallsTile1Title: 'कहीं से भी वीडियो कॉल शुरू करें',
	FeatureBannerCallsTile2ActionLabel: 'ऐप्स कनेक्ट करें • 4 मिनट',
	FeatureBannerCallsTile2Description: 'अन्य पसंदीदा टेलीहेल्थ प्रदाताओं से सहजता से जुड़ें',
	FeatureBannerCallsTile2Title: 'अपने टेलीहेल्थ ऐप्स को कनेक्ट करें',
	FeatureBannerCallsTile3Title: 'कॉल',
	FeatureBannerCallsTitle: 'ग्राहकों से जुड़ें - कहीं भी, कभी भी',
	FeatureBannerClientsTile1ActionLabel: 'अभी आयात करें • 2 मिनट',
	FeatureBannerClientsTile1Description: 'हमारे स्वचालित क्लाइंट आयात टूल के साथ जल्दी से आरंभ करें',
	FeatureBannerClientsTile1Title: 'क्या आपके पास बहुत सारे ग्राहक हैं?',
	FeatureBannerClientsTile2ActionLabel: 'सेवन को अनुकूलित करें • 2 मिनट',
	FeatureBannerClientsTile2Description: 'इनटेक पेपरवर्क हटाएं और ग्राहक अनुभव में सुधार करें',
	FeatureBannerClientsTile2Title: 'काग़ज़ मुक्त बनना',
	FeatureBannerClientsTile3Title: 'ग्राहक पोर्टल',
	FeatureBannerClientsTitle: 'यह सब आपके ग्राहकों से शुरू होता है',
	FeatureBannerHeader: 'समुदाय द्वारा, समुदाय के लिए!',
	FeatureBannerInvoicesTile1ActionLabel: 'भुगतान स्वचालित करें • 2 मिनट',
	FeatureBannerInvoicesTile1Description: 'स्वचालित भुगतान के साथ अजीब बातचीत से बचें',
	FeatureBannerInvoicesTile1Title: '2x तेजी से भुगतान प्राप्त करें',
	FeatureBannerInvoicesTile2ActionLabel: 'नकदी प्रवाह पर नज़र रखें • 2 मिनट',
	FeatureBannerInvoicesTile2Description: 'अवैतनिक बिलों को कम करें और अपनी आय पर नज़र रखें',
	FeatureBannerInvoicesTile2Title: 'अपनी आय पर बिना किसी परेशानी के नज़र रखें',
	FeatureBannerInvoicesTile3Title: 'बिलिंग और भुगतान',
	FeatureBannerInvoicesTitle: 'चिंता करने के लिए एक चीज़ कम हुई',
	FeatureBannerSubheader:
		'हमारी टीम और समुदाय द्वारा बनाए गए केयरपैट्रॉन टेम्पलेट। नए संसाधन आज़माएँ या अपने खुद के संसाधन साझा करें!',
	FeatureBannerTeamTile1ActionLabel: 'अभी आमंत्रित करें',
	FeatureBannerTeamTile1Description: 'टीम के सदस्यों को अपने खाते में आमंत्रित करें और सहयोग को आसान बनाएं',
	FeatureBannerTeamTile1Title: 'अपनी टीम को एक साथ लाएँ',
	FeatureBannerTeamTile2ActionLabel: 'उपलब्धता सेट करें • 2 मिनट',
	FeatureBannerTeamTile2Description: 'डबल-बुकिंग से बचने के लिए अपनी टीम की उपलब्धता प्रबंधित करें',
	FeatureBannerTeamTile2Title: 'अपनी उपलब्धता निर्धारित करें',
	FeatureBannerTeamTile3ActionLabel: 'अनुमतियाँ सेट करें • 2 मिनट',
	FeatureBannerTeamTile3Description: 'अनुपालन के लिए संवेदनशील डेटा और उपकरणों तक पहुंच को नियंत्रित करें',
	FeatureBannerTeamTile3Title: 'अनुमतियाँ और पहुँच अनुकूलित करें',
	FeatureBannerTeamTitle: 'अकेले कुछ भी महान हासिल नहीं किया जा सकता',
	FeatureBannerTemplatesTile1ActionLabel: 'लाइब्रेरी देखें • 2 मिनट',
	FeatureBannerTemplatesTile1Description: 'अनुकूलन योग्य संसाधनों की एक अद्भुत लाइब्रेरी से चुनें ',
	FeatureBannerTemplatesTile1Title: 'अपना कार्यभार कम करें',
	FeatureBannerTemplatesTile2ActionLabel: 'अभी भेजें • 2 मिनट',
	FeatureBannerTemplatesTile2Description: 'ग्राहकों को पूरा करने के लिए सुंदर टेम्पलेट भेजें',
	FeatureBannerTemplatesTile2Title: 'दस्तावेज़ीकरण को मज़ेदार बनाएं',
	FeatureBannerTemplatesTile3Title: 'टेम्पलेट्स',
	FeatureBannerTemplatesTitle: 'किसी भी चीज़ के लिए टेम्पलेट्स',
	FeatureLimitBannerDescription:
		'अभी अपग्रेड करें ताकि {featureName} बनाना और प्रबंधित करना बिना किसी रुकावट के जारी रख सकें और Carepatron का अधिकतम लाभ उठा सकें!',
	FeatureLimitBannerTitle: 'आप अपने {featureName} सीमा तक {percentage}% पहुँच चुके हैं',
	FeatureRequiresUpgrade: 'इस सुविधा के लिए अपग्रेड की आवश्यकता है',
	Fee: 'शुल्क',
	Female: 'महिला',
	FieldLabelTooltip: '{isHidden, select, true {दिखाएँ} other {छिपाएँ}} क्षेत्र लेबल',
	FieldName: 'फ़ील्ड का नाम',
	FieldOptionsFirstPart: 'पहला शब्द',
	FieldOptionsMiddlePart: 'मध्य शब्द',
	FieldOptionsSecondPart: 'अंतिम शब्द',
	FieldOptionsWholeField: 'संपूर्ण क्षेत्र',
	FieldType: 'क्षेत्र प्रकार',
	Fields: 'फ़ील्ड',
	File: 'फ़ाइल',
	FileDownloaded: '<strong>{fileName}</strong> डाउनलोड किया गया',
	FileInvalidType: 'फ़ाइल समर्थित नहीं है.',
	FileNotFound: 'फ़ाइल प्राप्त नहीं हुई',
	FileNotFoundDescription: 'आप जिस फ़ाइल को खोज रहे हैं वह उपलब्ध नहीं है या हटा दी गई है',
	FileTags: 'फ़ाइल टैग',
	FileTagsHelper: 'टैग सभी फ़ाइलों पर लागू किए जाएँगे',
	FileTooLarge: 'फ़ाइल बहुत बड़ी है.',
	FileTooSmall: 'फ़ाइल बहुत छोटी है.',
	FileUploadComplete: 'पूरा',
	FileUploadFailed: 'असफल',
	FileUploadInProgress: 'लोड हो रहा है',
	FileUploadedNotificationSubject: '{actorProfileName} ने एक फ़ाइल अपलोड की है',
	Files: 'फ़ाइलें',
	FillOut: 'भरना',
	Filter: 'फ़िल्टर',
	FilterBy: 'फिल्टर के द्वारा',
	FilterByAmount: 'राशि के अनुसार फ़िल्टर करें',
	FilterByClient: 'ग्राहक द्वारा फ़िल्टर करें',
	FilterByLocation: 'स्थान के अनुसार फ़िल्टर करें',
	FilterByService: 'सेवा के अनुसार फ़िल्टर करें',
	FilterByStatus: 'स्थिति के अनुसार फ़िल्टर करें',
	FilterByTags: 'टैग द्वारा फ़िल्टर करें',
	FilterByTeam: 'टीम के अनुसार फ़िल्टर करें',
	Filters: 'फिल्टर',
	FiltersAppliedToView: 'फ़िल्टर दृश्य पर लागू किए गए',
	FinalAppointment: 'अंतिम नियुक्ति',
	FinalizeImport: 'इम्पोर्ट को अंतिम रूप दें',
	FinancialAnalyst: 'वित्तीय विश्लेषक',
	Finish: 'खत्म करना',
	Firefighter: 'फायर फाइटर',
	FirstName: 'पहला नाम',
	FirstNameLastInitial: 'प्रथम नाम, अंतिम नाम का पहला अक्षर',
	FirstPerson: 'प्रथम व्यक्ति',
	FolderName: 'फ़ोल्डर का नाम',
	Folders: 'फ़ोल्डर',
	FontFamily: 'फुहारा परिवार',
	ForClients: 'ग्राहकों के लिए',
	ForClientsDetails: 'मुझे देखभाल या स्वास्थ्य संबंधी सेवाएं प्राप्त होती हैं',
	ForPractitioners: 'चिकित्सकों के लिए',
	ForPractitionersDetails: 'अपने अभ्यास का प्रबंधन करें और उसे आगे बढ़ाएं',
	ForgotPasswordConfirmAccessCode: 'पुष्टि कोड',
	ForgotPasswordConfirmNewPassword: 'नया पासवर्ड',
	ForgotPasswordConfirmPageDescription:
		'कृपया अपना ईमेल पता, नया पासवर्ड और वह पुष्टिकरण कोड दर्ज करें जो हमने आपको अभी भेजा है।',
	ForgotPasswordConfirmPageTitle: 'पासवर्ड रीसेट',
	ForgotPasswordPageButton: 'रीसेट लिंक भेजें',
	ForgotPasswordPageDescription: 'अपना ईमेल दर्ज करें और हम आपको अपना पासवर्ड रीसेट करने के लिए एक लिंक भेजेंगे।',
	ForgotPasswordPageTitle: 'पासवर्ड भूल गए',
	ForgotPasswordSuccessPageDescription: 'कृपया अपने इनबॉक्स में रीसेट लिंक की जांच करें।',
	ForgotPasswordSuccessPageTitle: 'रीसेट लिंक भेजा गया!',
	Form: 'फ़ॉर्म',
	FormAnswersSentToEmailNotification: 'हमने आपके उत्तरों की एक प्रति भेज दी है',
	FormBlocks: 'फॉर्म ब्लॉक',
	FormFieldAddOption: 'विकल्प जोड़ें',
	FormFieldAddOtherOption: '"अन्य" जोड़ें',
	FormFieldOptionPlaceholder: 'विकल्प {index}',
	FormStructures: 'प्रपत्र संरचनाएं',
	Format: 'प्रारूप',
	FormatLinkButtonColor: 'बटन रंग',
	Forms: 'फार्म',
	FormsAndAgreementsValidationMessage: 'प्रवेश प्रक्रिया जारी रखने के लिए सभी फॉर्म और समझौते पूरे किए जाने चाहिए।',
	FormsCategoryDescription: 'रोगी विवरण एकत्रित करने और व्यवस्थित करने के लिए',
	Frankfurt: 'फ्रैंकफर्ट',
	Free: 'मुक्त',
	FreePlanInclusionFive: 'स्वचालित बिलिंग ',
	FreePlanInclusionFour: 'ग्राहक पोर्टल',
	FreePlanInclusionHeader: 'आरंभ करें',
	FreePlanInclusionOne: 'असीमित ग्राहक',
	FreePlanInclusionSix: 'लाइव समर्थन',
	FreePlanInclusionThree: '1 जीबी स्टोरेज',
	FreePlanInclusionTwo: 'टेलीहेल्थ',
	FreeSubscriptionPlanSubtitle: 'सभी के लिए निःशुल्क',
	FreeSubscriptionPlanTitle: 'मुक्त',
	Friday: 'शुक्रवार',
	From: 'से',
	FullName: 'पूरा नाम',
	FunctionalMedicineOrNaturopath: 'कार्यात्मक चिकित्सा या प्राकृतिक चिकित्सा',
	FuturePaymentsAuthoriseProvider: 'भविष्य में सहेजे गए भुगतान का उपयोग करने के लिए {provider} को अनुमति दें',
	FuturePaymentsSavePaymentMethod: 'भविष्य के भुगतान के लिए {paymentMethod} सहेजें',
	GST: 'जीएसटी',
	Gender: 'लिंग',
	GeneralAvailability: 'आम तौर पर कब मिलते हैं',
	GeneralAvailabilityDescription:
		'यह निर्धारित करें कि आप नियमित रूप से कब उपलब्ध हैं। ग्राहक केवल उपलब्ध घंटों के दौरान ही आपकी सेवाएँ बुक कर सकेंगे।',
	GeneralAvailabilityDescription2:
		'अपनी ऑनलाइन बुकिंग की उपलब्धता निर्धारित करने के लिए विशिष्ट समय पर अपनी उपलब्धता और वांछित सेवा पेशकश के आधार पर कार्यक्रम बनाएं।',
	GeneralAvailabilityInfo: 'आपके उपलब्ध घंटे आपकी ऑनलाइन बुकिंग की उपलब्धता निर्धारित करेंगे',
	GeneralAvailabilityInfo2:
		'समूह कार्यक्रम आयोजित करने वाली सेवाओं को एक नई समय-सारणी का उपयोग करना चाहिए, ताकि ग्राहकों द्वारा ऑनलाइन बुक किए जाने वाले उपलब्ध घंटों को कम किया जा सके।',
	GeneralHoursPlural: '{count} {count, plural, one {घंटा} other {घंटे}}',
	GeneralPractitioner: 'सामान्य चिकित्सक',
	GeneralPractitioners: 'सामान्य चिकित्सकों',
	GeneralServiceAvailabilityInfo: 'यह शेड्यूल असाइन किए गए टीम सदस्यों के व्यवहार को ओवरराइड करेगा',
	Generate: 'उत्पन्न',
	GenerateBillingItemsBannerContent: 'आवर्ती अपॉइंटमेंट के लिए बिलिंग आइटम स्वचालित रूप से नहीं बनाए जाते हैं।',
	GenerateItems: 'आइटम उत्पन्न करें',
	GenerateNote: 'नोट उत्पन्न करें',
	GenerateNoteConfirmationModalDescription:
		'आप क्या करना चाहेंगे? नया नोट बनाना, मौजूदा नोट में कुछ जोड़ना या उसकी सामग्री बदलना?',
	GenerateNoteFor: 'के लिए नोट बनाएं',
	GeneratingContent: 'सामग्री तैयार की जा रही है...',
	GeneratingNote: 'आपका नोट तैयार कर रहा हूँ...',
	GeneratingTranscript: 'प्रतिलिपि तैयार करना',
	GeneratingTranscriptDescription: 'इसे संसाधित होने में कुछ मिनट लग सकते हैं',
	GeneratingYourTranscript: 'अपनी प्रतिलिपि तैयार करना',
	GenericErrorDescription: '{module} लोड नहीं हो सका. कृपया बाद में फिर से कोशिश करें.',
	GenericErrorTitle: 'अप्रत्याशित त्रुटि घटित हुई',
	GenericFailureSnackbar: 'क्षमा करें, कुछ अप्रत्याशित हुआ। कृपया पृष्ठ को ताज़ा करें और पुनः प्रयास करें।',
	GenericSavedSuccessSnackbar: 'सफल! परिवर्तन सहेजे गए',
	GeneticCounselor: 'जेनेटिक काउंसलर',
	Gerontologist: 'gerontologist',
	Get50PercentOff: '50% तक छूट पाएँ!',
	GetHelp: 'मदद लें',
	GetStarted: 'शुरू हो जाओ',
	GettingStartedAppointmentTypes: 'अपॉइंटमेंट प्रकार बनाएँ',
	GettingStartedAppointmentTypesDescription:
		'अपनी सेवाओं, कीमतों और बिलिंग कोड को अनुकूलित करके अपने शेड्यूलिंग और बिलिंग को सुव्यवस्थित करें',
	GettingStartedAppointmentTypesTitle: 'अनुसूची ',
	GettingStartedClients: 'अपने ग्राहकों को जोड़ें',
	GettingStartedClientsDescription:
		'भविष्य की नियुक्तियों, नोट्स और भुगतानों के लिए ग्राहकों के साथ बातचीत शुरू करें',
	GettingStartedClientsTitle: 'यह सब ग्राहकों से शुरू होता है',
	GettingStartedCreateClient: 'ग्राहक बनाएं',
	GettingStartedImportClients: 'क्लाइंट आयात करें',
	GettingStartedInvoices: 'एक पेशेवर की तरह चालान बनाएँ',
	GettingStartedInvoicesDescription: `व्यावसायिक चालान बनाना सरल है।
 अपना लोगो, स्थान और भुगतान शर्तें जोड़ें`,
	GettingStartedInvoicesTitle: 'अच्छा प्रभाव डालने का प्रयत्न',
	GettingStartedMobileApp: 'मोबाइल ऐप प्राप्त करें',
	GettingStartedMobileAppDescription:
		'आप आसानी से उपयोग के लिए अपने iOS, Android या डेस्कटॉप डिवाइस पर Carepatron डाउनलोड कर सकते हैं',
	GettingStartedMobileAppTitle: 'कहीं से भी काम करें',
	GettingStartedNavItem: 'शुरू करना',
	GettingStartedPageTitle: 'केयरपेट्रॉन पर शुरुआत कैसे करें',
	GettingStartedPayments: 'ऑनलाइन भुगतान स्वीकार करें',
	GettingStartedPaymentsDescription: `अपने ग्राहकों को ऑनलाइन भुगतान की सुविधा देकर तेजी से भुगतान प्राप्त करें।
 अपने सभी चालान और भुगतान एक ही स्थान पर देखें`,
	GettingStartedPaymentsTitle: 'भुगतान को आसान बनाएं',
	GettingStartedSaveBranding: 'ब्रांडिंग सहेजें',
	GettingStartedSyncCalendars: 'अन्य कैलेंडर सिंक करें',
	GettingStartedSyncCalendarsDescription:
		'केयरपेट्रॉन आपके कैलेंडर में टकराव की जांच करता है, इसलिए अपॉइंटमेंट केवल तभी निर्धारित किए जाते हैं जब आप उपलब्ध हों',
	GettingStartedSyncCalendarsTitle: 'हमेशा अपडेट रहें',
	GettingStartedVideo: 'परिचयात्मक वीडियो देखें',
	GettingStartedVideoDescription: 'छोटी टीमों और उनके ग्राहकों के लिए पहला ऑल-इन-वन हेल्थकेयर कार्यस्थल',
	GettingStartedVideoTitle: 'केयरपेट्रॉन में आपका स्वागत है',
	GetttingStartedGetMobileDownload: 'ऐप डाउनलोड करें',
	GetttingStartedGetMobileNoDownload:
		'इस ब्राउज़र के साथ संगत नहीं है। यदि आप iPhone या iPad का उपयोग कर रहे हैं, तो कृपया इस पृष्ठ को Safari में खोलें। अन्यथा, इसे Chrome में खोलने का प्रयास करें।',
	Glossary: 'शब्दावली',
	Gmail: 'जीमेल लगीं',
	GmailSendMessagesLimitWarning:
		'जीमेल आपके खाते से एक दिन में केवल 500 संदेश भेजने की अनुमति देता है। कुछ संदेश विफल हो सकते हैं। क्या आप जारी रखना चाहते हैं?',
	GoToAppointment: 'अपॉइंटमेंट पर जाएं',
	GoToApps: 'ऐप्स पर जाएं',
	GoToAvailability: 'उपलब्धता पर जाएं',
	GoToClientList: 'ग्राहक सूची पर जाएँ',
	GoToClientRecord: 'क्लाइंट रिकॉर्ड पर जाएं',
	GoToClientSettings: 'अब क्लाइंट सेटिंग पर जाएं',
	GoToInvoiceTemplates: 'इनवॉइस टेम्प्लेट पर जाएं',
	GoToNotificationSettings: 'अधिसूचना सेटिंग पर जाएं',
	GoToPaymentSettings: 'भुगतान सेटिंग पर जाएं',
	Google: 'गूगल',
	GoogleCalendar: 'गूगल कैलेंडर',
	GoogleColor: 'गूगल कैलेंडर का रंग',
	GoogleMeet: 'गूगल मीट',
	GoogleTagManagerContainerId: 'Google टैग प्रबंधक कंटेनर आईडी',
	GotIt: 'समझ गया!',
	Goto: 'जाओ',
	Granddaughter: 'पोती',
	Grandfather: 'दादा',
	Grandmother: 'दादी',
	Grandparent: 'दादा-दादी',
	Grandson: 'पोता',
	GrantPortalAccess: 'पोर्टल तक पहुंच प्रदान करें',
	GraphicDesigner: 'ग्राफिक डिजाइनर',
	Grid: 'ग्रिड',
	GridView: 'जालक दृश्य',
	Group: 'समूह',
	GroupBy: 'द्वारा समूह बनाएं',
	GroupEvent: 'समूह कार्यक्रम',
	GroupEventHelper: 'सेवा के लिए उपस्थिति सीमा निर्धारित करें',
	GroupFilterLabel: 'सभी {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'समूह आईडी',
	GroupInputFieldsFormPrimaryText: 'समूह इनपुट फ़ील्ड',
	GroupInputFieldsFormSecondaryText: 'कस्टम फ़ील्ड चुनें या जोड़ें',
	GuideTo: '{value} के लिए गाइड',
	GuideToImproveVideoQuality: 'वीडियो की गुणवत्ता सुधारने के लिए मार्गदर्शिका',
	GuideToManagingPayers: 'भुगतानकर्ताओं का प्रबंधन',
	GuideToSubscriptionsBilling: 'सदस्यता बिलिंग के लिए मार्गदर्शिका',
	GuideToTroubleshooting: 'समस्या निवारण हेतु मार्गदर्शिका',
	Guidelines: 'दिशानिर्देश',
	GuidelinesCategoryDescription: 'नैदानिक निर्णय लेने के लिए मार्गदर्शन के लिए',
	HST: 'एचएसटी',
	HairStylist: 'बालों की स्टाइल बनाने वाला',
	HaveBeenWaiting: 'आप लंबे समय से इंतजार कर रहे हैं',
	HeHim: 'वह उसे',
	HeaderAccountSettings: 'प्रोफ़ाइल',
	HeaderCalendar: 'कैलेंडर',
	HeaderCalls: 'कॉल',
	HeaderClientAppAccountSettings: 'अकाउंट सेटिंग',
	HeaderClientAppCalls: 'कॉल',
	HeaderClientAppMyDocumentation: 'प्रलेखन',
	HeaderClientAppMyRelationships: 'मेरे रिश्ते',
	HeaderClients: 'ग्राहकों',
	HeaderHelp: 'मदद',
	HeaderMoreOptions: 'अधिक विकल्प',
	HeaderStaff: 'कर्मचारी',
	HealthCoach: 'स्वास्थ्य कोच',
	HealthCoaches: 'स्वास्थ्य प्रशिक्षक',
	HealthEducator: 'स्वास्थ्य शिक्षक',
	HealthInformationTechnician: 'स्वास्थ्य सूचना तकनीशियन',
	HealthPolicyExpert: 'स्वास्थ्य नीति विशेषज्ञ',
	HealthServicesAdministrator: 'स्वास्थ्य सेवा प्रशासक',
	HelpArticles: 'सहायता लेख',
	HiddenColumns: 'छिपे हुए कॉलम',
	HiddenFields: 'छिपे हुए क्षेत्र',
	HiddenSections: 'छिपे हुए अनुभाग',
	HiddenSectionsAndFields: 'छिपे हुए अनुभाग/क्षेत्र',
	HideColumn: 'कॉलम छुपाएं',
	HideColumnButton: 'कॉलम {value} बटन छिपाएँ',
	HideDetails: 'विवरण छुपाओ',
	HideField: 'फ़ील्ड छिपाएँ',
	HideFullAddress: 'छिपाना',
	HideMenu: 'मेनू छुपाएं',
	HideMergeSummarySidebar: 'मर्ज सारांश छुपाएँ',
	HideSection: 'अनुभाग छिपाएँ',
	HideYourView: 'अपना दृश्य छिपाएँ',
	Highlight: 'हाइलाइट रंग',
	Highlighter: 'हाइलाइटर',
	History: 'इतिहास',
	HistoryItemFooter:
		'{actors, select, undefined {{date} को {time} पर} other {द्वारा {actors} • {date} को {time} पर}}',
	HistorySidePanelEmptyState: 'कोई इतिहास रिकॉर्ड नहीं मिला',
	HistoryTitle: 'गतिविधि लॉग',
	HolisticHealthPractitioner: 'समग्र स्वास्थ्य व्यवसायी',
	HomeCaregiver: 'गृह देखभालकर्ता',
	HomeHealthAide: 'गृह स्वास्थ्य सहायक',
	HomelessShelter: 'बेघर आश्रय',
	HourAbbreviation: '{count} {count, plural, one {घंटा} other {घंटे}}',
	Hourly: 'घंटेवार',
	HoursPlural: '{age, plural, one {# घंटा} other {# घंटे}}',
	HowCanWeImprove: 'हम इसे कैसे बेहतर बना सकते हैं?',
	HowCanWeImproveResponse: 'इस प्रतिक्रिया को हम कैसे बेहतर बना सकते हैं?',
	HowDidWeDo: 'हमने कैसा प्रदर्शन किया?',
	HowDoesReferralWork: 'रेफरल कार्यक्रम के लिए मार्गदर्शिका',
	HowToUseAiSummarise: 'AI सारांश का उपयोग कैसे करें',
	HumanResourcesManager: 'मानव संसाधन प्रबंधक',
	Husband: 'पति',
	Hypnotherapist: 'सम्मोहन चिकित्सक',
	IVA: 'आईवीए',
	IgnoreNotification: 'अधिसूचना को अनदेखा करें',
	IgnoreOnce: 'एक बार उपेक्षा करें',
	IgnoreSender: 'प्रेषक को अनदेखा करें',
	IgnoreSenderDescription: `इस प्रेषक से भविष्य में होने वाली बातचीत स्वचालित रूप से 'अन्य' में स्थानांतरित कर दी जाएगी। क्या आप वाकई इन प्रेषकों को अनदेखा करना चाहते हैं?`,
	IgnoreSenders: 'प्रेषकों को अनदेखा करें',
	IgnoreSendersSuccess: 'नज़रअंदाज़ ईमेल पता <mark>{addresses}</mark>',
	Ignored: 'अवहेलना करना',
	Image: 'छवि',
	Import: 'आयात',
	ImportActivity: 'आयात गतिविधि',
	ImportClientSuccessSnackbarDescription: 'आपकी फ़ाइल सफलतापूर्वक आयात कर ली गई है',
	ImportClientSuccessSnackbarTitle: 'आयात सफल!',
	ImportClients: 'क्लाइंट आयात करें',
	ImportClientsFailureSnackbarDescription: 'किसी त्रुटि के कारण आपकी फ़ाइल सफलतापूर्वक आयात नहीं की जा सकी.',
	ImportClientsFailureSnackbarTitle: 'आयात असफल!',
	ImportClientsGuide: 'क्लाइंट आयात करने के लिए गाइड',
	ImportClientsInProgressSnackbarDescription: 'इसे पूरा होने में केवल एक मिनट लगेगा।',
	ImportClientsInProgressSnackbarTitle: '<br>**{fileName}** आयात करना',
	ImportClientsModalDescription:
		'चुनें कि आपका डेटा कहां से आ रहा है - चाहे वह आपके डिवाइस पर मौजूद फ़ाइल हो, किसी तृतीय-पक्ष सेवा से हो या किसी अन्य सॉफ़्टवेयर प्लेटफ़ॉर्म से हो।',
	ImportClientsModalFileUploadHelperText: '{fileTypes} का समर्थन करता है। आकार सीमा {fileSizeLimit}।',
	ImportClientsModalImportGuideLabel: 'क्लाइंट डेटा आयात करने के लिए मार्गदर्शिका',
	ImportClientsModalStep1Label: 'डेटा स्रोत चुनें',
	ImportClientsModalStep2Label: 'फ़ाइल अपलोड करें',
	ImportClientsModalStep3Label: 'समीक्षा फ़ील्ड',
	ImportClientsModalTitle: 'अपने क्लाइंट डेटा को आयात करना',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {क्लाइंट} other {क्लाइंट्स}} आयात के लिए तैयार',
	ImportContactFailedNotificationSubject: 'आपका डेटा आयात विफल हो गया है',
	ImportDataSourceSelectorLabel: 'डेटा स्रोत यहाँ से आयात करें',
	ImportDataSourceSelectorPlaceholder: 'आयात डेटा स्रोत खोजें या चुनें',
	ImportExportButton: 'आयात/निर्यात',
	ImportFailed: 'आयात विफल',
	ImportFromAnotherPlatformTileDescription:
		'अपने क्लाइंट फ़ाइलों का एक निर्यात डाउनलोड करें और उन्हें यहाँ अपलोड करें।',
	ImportFromAnotherPlatformTileLabel: 'दूसरे प्लेटफ़ॉर्म से इम्पोर्ट करें',
	ImportGuide: 'आयात गाइड',
	ImportInProgress: 'आयात जारी है',
	ImportProcessing: 'आयात प्रसंस्करण...',
	ImportSpreadsheetDescription:
		'आप अपनी मौजूदा क्लाइंट सूची को .CSV, .XLS, या .XLSX जैसे सारणीबद्ध डेटा वाली स्प्रेडशीट फ़ाइल अपलोड करके Carepatron में आयात कर सकते हैं',
	ImportSpreadsheetTitle: 'अपनी स्प्रेडशीट फ़ाइल आयात करें',
	ImportTemplates: 'टेम्पलेट इम्पोर्ट करें',
	Importing: 'आयात कर रहा है',
	ImportingCalendarProductEvents: '{product} घटनाएँ आयात करना',
	ImportingData: 'डेटा आयात करना',
	ImportingSpreadsheetDescription: 'इसे पूरा होने में केवल एक मिनट लगेगा',
	ImportingSpreadsheetTitle: 'अपनी स्प्रेडशीट आयात करना',
	ImportsInProgress: 'आयात प्रगति पर हैं',
	InPersonMeeting: 'व्यक्तिगत बैठक',
	InProgress: 'प्रगति पर है',
	InTransit: 'पारगमन में',
	InTransitTooltip:
		'ट्रांज़िट बैलेंस में स्ट्राइप से आपके बैंक खाते में किए गए सभी भुगतान किए गए इनवॉइस भुगतान शामिल हैं। इन फंडों को निपटाने में आमतौर पर 3-5 दिन लगते हैं।',
	Inactive: 'निष्क्रिय',
	InboundOrOutboundCalls: 'इनबाउंड या आउटबाउंड कॉल',
	Inbox: 'इनबॉक्स',
	InboxAccessRestricted: 'पहुँच प्रतिबंधित है। अनुमति के लिए कृपया इनबॉक्स स्वामी से संपर्क करें।',
	InboxAccountAlreadyConnected: 'जिस चैनल से आपने जुड़ने का प्रयास किया है वह पहले से ही Carepatron से जुड़ा हुआ है',
	InboxAddAttachments: 'अनुलग्नक जोड़ें',
	InboxAreYouSureDeleteMessage: 'क्या आप निश्चित रूप से यह संदेश हटाना चाहते हैं?',
	InboxBulkCloseSuccess:
		'{count, plural, one {# बातचीत सफलतापूर्वक बंद झाली} other {# बातचीत सफलतापूर्वक बंद झाल्या}}',
	InboxBulkComposeModalTitle: 'बल्क संदेश लिखें',
	InboxBulkDeleteSuccess:
		'{count, plural, one {# वार्तालाप सफलतापूर्वक काढून टाकला} other {# वार्तालाप सफलतापूर्वक काढून टाकले}}',
	InboxBulkReadSuccess:
		'{count, plural, one {# वार्तालाप सफलतापूर्वक वाचलेले मार्क केलेले आहे} other {# वार्तालापे सफलतापूर्वक वाचलेले मार्क केलेले आहेत}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {सफलतापूर्वक # वार्तालाप पुन्हा उघडला} other {सफलतापूर्वक # वार्तालाप पुन्हा उघडले}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {सफलतापूर्वक # बातचीत अपठित के रूप में चिह्नित की गई} other {सफलतापूर्वक # बातचीत अपठित के रूप में चिह्नित की गई}}',
	InboxChatCreateGroup: 'समूह बनाएँ',
	InboxChatDeleteGroupModalDescription:
		'क्या आप सुनिश्चित हैं कि आप इस समूह को हटाना चाहते हैं? सभी संदेश और संलग्नक हटा दिए जाएँगे।',
	InboxChatDeleteGroupModalTitle: 'समूह हटाएँ',
	InboxChatDiscardDraft: 'ड्राफ्ट हटाएँ',
	InboxChatDragDropText: 'फ़ाइलें अपलोड करने के लिए यहां छोड़ें',
	InboxChatGroupConversation: 'समूह वार्तालाप',
	InboxChatGroupCreateModalDescription:
		'अपनी टीम, ग्राहकों या समुदाय के साथ संदेश भेजने और सहयोग करने के लिए एक नया समूह शुरू करें।',
	InboxChatGroupCreateModalTitle: 'समूह बनाएं',
	InboxChatGroupMembers: 'समूह सदस्य',
	InboxChatGroupModalGroupNameFieldLabel: 'समूह का नाम',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'जैसे ग्राहक सहायता, व्यवस्थापक',
	InboxChatGroupModalGroupNameFieldRequired: 'यह क्षेत्र आवश्यक है',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'कम से कम एक सदस्य आवश्यक है',
	InboxChatGroupModalMembersFieldLabel: 'समूह के सदस्य चुनें',
	InboxChatGroupModalMembersFieldPlaceholder: 'इस समूह में जोड़ने के लिए टीम के सदस्यों का चयन करें',
	InboxChatGroupUpdateModalTitle: 'समूह प्रबंधित करें',
	InboxChatLeaveGroup: 'समूह छोड़ें',
	InboxChatLeaveGroupModalDescription:
		'क्या आप निश्चित हैं कि आप यह समूह छोड़ना चाहते हैं? आपको अब संदेश या अपडेट प्राप्त नहीं होंगे।',
	InboxChatLeaveGroupModalTitle: 'समूह छोड़ें',
	InboxChatLeftGroupMessage: 'बाएँ समूह संदेश',
	InboxChatManageGroup: 'समूह का प्रबंधन करें',
	InboxChatSearchParticipants: 'प्रतिभागियों का चयन करें',
	InboxCloseConversationSuccess: 'बातचीत सफलतापूर्वक बंद हुई',
	InboxCompose: 'लिखें',
	InboxComposeBulk: 'थोक संदेश',
	InboxComposeCarepatronChat: 'मैसेंजर',
	InboxComposeChat: 'चैट लिखें',
	InboxComposeDisabledNoConnection: 'संदेश भेजने के लिए ईमेल खाता कनेक्ट करें',
	InboxComposeDisabledNoPermissionTooltip: 'आपको इस इनबॉक्स से संदेश भेजने की अनुमति नहीं है',
	InboxComposeEmail: 'ईमेल लिखें',
	InboxComposeMessageFrom: 'से',
	InboxComposeMessageRecipientBcc: 'गुप्त प्रतिलिपि',
	InboxComposeMessageRecipientCc: 'प्रतिलिपि',
	InboxComposeMessageRecipientTo: 'को',
	InboxComposeMessageSubject: 'विषय:',
	InboxConnectAccountButton: 'अपना ईमेल कनेक्ट करें',
	InboxConnectedDescription: 'आपके इनबॉक्स में कोई संचार नहीं है',
	InboxConnectedHeading: 'जैसे ही आप संचार का आदान-प्रदान शुरू करेंगे, आपकी बातचीत यहाँ दिखाई देगी',
	InboxConnectedHeadingClientView: 'अपने ग्राहक संचार को सुव्यवस्थित करें',
	InboxCreateFirstInboxButton: 'अपना पहला इनबॉक्स बनाएं',
	InboxCreationSuccess: 'इनबॉक्स सफलतापूर्वक बनाया गया',
	InboxDeleteAttachment: 'अनुलग्नक हटाएं',
	InboxDeleteConversationSuccess: 'वार्तालाप सफलतापूर्वक हटा दिया गया',
	InboxDeleteMessage: 'संदेश को हटाएं?',
	InboxDirectMessage: 'डायरेक्ट मैसेज',
	InboxEditDraft: 'ड्राफ्ट संपादित करें',
	InboxEmailComposeReplyEmail: 'उत्तर लिखें',
	InboxEmailDraft: 'मसौदा',
	InboxEmailNotFound: 'ईमेल नहीं मिला',
	InboxEmailSubjectFieldInformation: 'विषय पंक्ति बदलने से एक नया थ्रेडेड ईमेल बन जाएगा।',
	InboxEmptyArchiveDescription: 'कोई संग्रहीत वार्तालाप नहीं मिला',
	InboxEmptyBinDescription: 'कोई हटाई गई बातचीत नहीं मिली',
	InboxEmptyBinHeading: 'सब कुछ साफ़ है, यहाँ देखने को कुछ भी नहीं है',
	InboxEmptyBinSuccess: 'बातचीत सफलतापूर्वक हटा दी गई',
	InboxEmptyCongratsHeading: 'बढ़िया काम! अगली बातचीत तक आराम से बैठो',
	InboxEmptyDraftDescription: 'कोई मसौदा वार्तालाप नहीं मिला',
	InboxEmptyDraftHeading: 'सब कुछ साफ़ है, यहाँ देखने को कुछ भी नहीं है',
	InboxEmptyOtherDescription: 'कोई अन्य वार्तालाप नहीं मिला',
	InboxEmptyScheduledHeading: 'सब कुछ साफ़ है, कोई भी बातचीत भेजने के लिए निर्धारित नहीं है',
	InboxEmptySentDescription: 'कोई भेजा गया वार्तालाप नहीं मिला',
	InboxForward: 'आगे',
	InboxGroupClientsLabel: 'सभी ग्राहक',
	InboxGroupClientsOverviewLabel: 'ग्राहकों',
	InboxGroupClientsSelectedItemPrefix: 'ग्राहक',
	InboxGroupStaffsLabel: 'पूरी टीम',
	InboxGroupStaffsOverviewLabel: 'टीम',
	InboxGroupStaffsSelectedItemPrefix: 'टीम',
	InboxGroupStatusLabel: 'सभी स्थिति',
	InboxGroupStatusOverviewLabel: 'किसी स्थिति पर भेजें',
	InboxGroupStatusSelectedItemPrefix: 'स्थिति',
	InboxGroupTagsLabel: 'सभी टैग',
	InboxGroupTagsOverviewLabel: 'टैग पर भेजें',
	InboxGroupTagsSelectedItemPrefix: 'टैग',
	InboxHideQuotedText: 'उद्धृत पाठ छुपाएँ',
	InboxIgnoreConversationSuccess: 'बातचीत को सफलतापूर्वक अनदेखा किया गया',
	InboxMessageAllLabelRecipientsCount: 'सभी {label} प्राप्तकर्ता ({count})',
	InboxMessageBodyPlaceholder: 'अपना संदेश जोड़ें',
	InboxMessageDeleted: 'संदेश हटा दिया गया',
	InboxMessageMarkedAsRead: 'संदेश पढ़ा हुआ चिह्नित किया गया',
	InboxMessageMarkedAsUnread: 'संदेश अपठित के रूप में चिह्नित',
	InboxMessageSentViaChat: '**चैट के माध्यम से भेजा गया**  • {time} by {name}',
	InboxMessageShowMoreRecipients: '+{count} और',
	InboxMessageWasDeleted: 'यह संदेश हटा दिया गया है',
	InboxNoConnectionDescription: 'अपना ईमेल खाता कनेक्ट करें या एकाधिक ईमेल वाले इनबॉक्स बनाएं',
	InboxNoConnectionHeading: 'अपने ग्राहक संचार को एकीकृत करें',
	InboxNoDirectMessage: 'कोई हालिया संदेश नहीं',
	InboxRecentConversations: 'हाल ही में',
	InboxReopenConversationSuccess: 'बातचीत सफलतापूर्वक पुनः खोली गई',
	InboxReply: 'जवाब',
	InboxReplyAll: 'सभी को उत्तर दें',
	InboxRestoreConversationSuccess: 'वार्तालाप सफलतापूर्वक बहाल किया गया',
	InboxScheduleSendCancelSendSuccess:
		'शेड्यूल किया गया भेजना रद्द कर दिया गया और संदेश को ड्राफ्ट में वापस कर दिया गया',
	InboxScheduleSendMessageSuccessDescription: '{date} के लिए निर्धारित भेजें',
	InboxScheduleSendMessageSuccessTitle: 'भेजने का समय निर्धारण',
	InboxSearchForConversations: '"{query}" के लिए खोजें',
	InboxSendMessageSuccess: 'वार्तालाप सफलतापूर्वक भेजा गया',
	InboxSettings: 'इनबॉक्स सेटिंग्स',
	InboxSettingsAppsDesc:
		'इस साझा इनबॉक्स के लिए कनेक्ट किए गए एप्लिकेशन प्रबंधित करें: आवश्यकतानुसार कनेक्शन जोड़ें या निकालें.',
	InboxSettingsAppsNewConnectedApp: 'नया कनेक्टेड ऐप',
	InboxSettingsAppsTitle: 'कनेक्टेड ऐप्स',
	InboxSettingsDeleteAccountFailed: 'इनबॉक्स खाता हटाना विफल',
	InboxSettingsDeleteAccountSuccess: 'इनबॉक्स खाता सफलतापूर्वक हटा दिया गया',
	InboxSettingsDeleteAccountWarning:
		'{email} हटाने पर उसे इनबॉक्स {inboxName} से डिस्कनेक्ट कर दिया जाएगा और मैसेज सिंक होना बंद हो जाएँगे।',
	InboxSettingsDeleteInboxFailed: 'इनबॉक्स हटाने में विफल',
	InboxSettingsDeleteInboxSuccess: 'इनबॉक्स सफलतापूर्वक हटा दिया गया',
	InboxSettingsDeleteInboxWarning:
		'{inboxName} को हटाने से सभी जुड़े चैनल डिस्कनेक्ट हो जाएँगे और इस इनबॉक्स से जुड़े सभी संदेश हट जाएँगे। 		यह कार्रवाई स्थायी है और इसे पूर्ववत नहीं किया जा सकता है।',
	InboxSettingsDetailsDesc:
		'आपकी टीम के लिए संचार इनबॉक्स, जिससे ग्राहक संदेशों को कुशलतापूर्वक प्रबंधित किया जा सके।',
	InboxSettingsDetailsTitle: 'इनबॉक्स विवरण',
	InboxSettingsEmailSignatureLabel: 'ईमेल हस्ताक्षर डिफ़ॉल्ट',
	InboxSettingsReplyFormatDesc:
		'अपना डिफॉल्ट उत्तर-पता और ईमेल हस्ताक्षर एक समान प्रदर्शित करने के लिए सेट करें, चाहे ईमेल भेजने वाला कोई भी हो।',
	InboxSettingsReplyFormatTitle: 'उत्तर प्रारूप',
	InboxSettingsSendFromLabel: 'डिफ़ॉल्ट उत्तर सेट करें ',
	InboxSettingsStaffDesc: 'निर्बाध सहयोग के लिए इस साझा इनबॉक्स तक टीम के सदस्यों की पहुंच प्रबंधित करें।',
	InboxSettingsStaffTitle: 'टीम के सदस्यों को नियुक्त करें',
	InboxSettingsUpdateInboxDetailsFailed: 'इनबॉक्स विवरण अपडेट करने में विफल',
	InboxSettingsUpdateInboxDetailsSuccess: 'इनबॉक्स विवरण सफलतापूर्वक अपडेट किया गया',
	InboxSettingsUpdateInboxStaffsFailed: 'इनबॉक्स टीम सदस्यों को अपडेट करने में विफल',
	InboxSettingsUpdateInboxStaffsSuccess: 'इनबॉक्स टीम के सदस्यों को सफलतापूर्वक अपडेट किया गया',
	InboxSettingsUpdateReplyFormatFailed: 'उत्तर प्रारूप अपडेट करने में विफल',
	InboxSettingsUpdateReplyFormatSuccess: 'उत्तर प्रारूप सफलतापूर्वक अपडेट किया गया',
	InboxShowQuotedText: 'उद्धृत पाठ दिखाएँ',
	InboxStaffRoleAdminDescription: 'इनबॉक्स देखें, उत्तर दें और प्रबंधित करें',
	InboxStaffRoleResponderDescription: 'देखें और उत्तर दें',
	InboxStaffRoleViewerDescription: 'केवल देखें',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'संपादन जारी रखें',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'हां, बल्क भेजने पर स्विच करें',
	InboxSuggestMoveToBulkComposeMessageContent:
		'आपने {count} से ज़्यादा प्राप्तकर्ता चुने हैं. क्या आप इसे सामूहिक ईमेल के रूप में भेजना चाहते हैं?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'चेतावनी',
	InboxSwitchToOtherInbox: 'दूसरे इनबॉक्स पर स्विच करें',
	InboxUndoSendMessageSuccess: 'भेजना अधूरा',
	IncludeLineItems: 'लाइन आइटम शामिल करें',
	IncludeSalesTax: 'कर योग्य',
	IncludesAiSmartPrompt: 'AI स्मार्ट प्रॉम्प्ट शामिल हैं',
	Incomplete: 'अधूरा',
	IncreaseIndent: 'बढ़ते हुए अंतर में',
	IndianHealthServiceFreeStandingFacility: 'भारतीय स्वास्थ्य सेवा की स्वतंत्र सुविधा',
	IndianHealthServiceProviderFacility: 'भारतीय स्वास्थ्य सेवा प्रदाता-आधारित सुविधा',
	Information: 'जानकारी',
	InitialAssessment: 'आरंभिक आकलन',
	InitialSignupPageClientFamilyTitle: 'ग्राहक या परिवार का सदस्य',
	InitialSignupPageProviderTitle: 'स्वास्थ्य ',
	InitialTreatment: 'प्रारंभिक उपचार',
	Initials: 'आद्याक्षर',
	InlineEmbed: 'इनलाइन एम्बेड',
	InputPhraseToConfirm: 'पुष्टी करण्यासाठी, {confirmationPhrase} टाइप करा.',
	Insert: 'डालना',
	InsertTable: 'टेबल इंसर्ट करें',
	InstallCarepatronOnYourIphone1: 'अपने iOS पर Carepatron इंस्टॉल करें: टैप करें',
	InstallCarepatronOnYourIphone2: 'और फिर होम स्क्रीन पर जोड़ें',
	InsufficientCalendarScopesSnackbar: 'समन्वयन विफल - कृपया केयरपेट्रॉन को कैलेंडर अनुमतियाँ दें',
	InsufficientInboxScopesSnackbar: 'समन्वयन विफल - कृपया Carepatron को ईमेल अनुमति दें',
	InsufficientScopeErrorCodeSnackbar: 'समन्वयन विफल - कृपया Carepatron को सभी अनुमतियाँ दें',
	Insurance: 'बीमा',
	InsuranceAmount: 'बीमा राशि',
	InsuranceClaim: 'बीमा की दावा',
	InsuranceClaimAiChatPlaceholder: 'बीमा दावे के बारे में पूछें...',
	InsuranceClaimAiClaimNumber: 'दावा {number}',
	InsuranceClaimAiSubtitle: 'बीमा बिलिंग • दावा सत्यापन',
	InsuranceClaimDeniedSubject: 'दावा {claimNumber} {payerNumber} {payerName} को सबमिट किया गया अस्वीकृत कर दिया गया।',
	InsuranceClaimErrorDescription:
		'दावा में भुगतानकर्ता या क्लीयरिंग हाउस से रिपोर्ट की गई त्रुटियां हैं। कृपया निम्नलिखित त्रुटि संदेशों की समीक्षा करें और दावे को फिर से सबमिट करें।',
	InsuranceClaimErrorGuideLink: 'बीमा दावों के लिए मार्गदर्शिका',
	InsuranceClaimErrorTitle: 'दावा प्रस्तुत करने में त्रुटियाँ',
	InsuranceClaimNotFound: 'बीमा दावा नहीं मिला',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {दावा {claimNumber} के लिए {payerNumber} {payerName} द्वारा {paymentAmount} का आंशिक भुगतान रिकॉर्ड किया गया} other {दावा {claimNumber} के लिए {payerNumber} {payerName} द्वारा {paymentAmount} का भुगतान रिकॉर्ड किया गया}}',
	InsuranceClaimRejectedSubject: 'दावा {claimNumber} {payerNumber} {payerName} को भेजा गया अस्वीकृत कर दिया गया।',
	InsuranceClaims: 'बीमा दावे',
	InsuranceInformation: 'बीमा की जानकारी',
	InsurancePaid: 'बीमा भुगतान',
	InsurancePayer: 'बीमा भुगतानकर्ता',
	InsurancePayers: 'बीमा भुगतानकर्ता',
	InsurancePayersDescription: 'अपने खाते में जोड़े गए भुगतानकर्ताओं को देखें और नामांकन का प्रबंधन करें।',
	InsurancePayment: 'बीमा भुगतान',
	InsurancePoliciesDetailsSubtitle: 'दावों के समर्थन के लिए ग्राहक बीमा जानकारी जोड़ें।',
	InsurancePoliciesDetailsTitle: 'नीतियों का विवरण',
	InsurancePoliciesListSubtitle: 'दावों के समर्थन के लिए ग्राहक बीमा जानकारी जोड़ें।',
	InsurancePoliciesListTitle: 'बीमा पॉलिसियां',
	InsuranceSelfPay: 'स्वयं भुगतान',
	InsuranceType: 'बीमा का प्रकार',
	InsuranceUnpaid: 'बीमा अवैतनिक',
	Intake: 'प्रवेश',
	IntakeExpiredErrorCodeSnackbar:
		'यह इनटेक समाप्त हो चुका है। कृपया दूसरा इनटेक पुनः भेजने के लिए अपने प्रदाता से संपर्क करें।',
	IntakeNotFoundErrorSnackbar:
		'यह इनटेक नहीं मिल सका। कृपया दूसरा इनटेक पुनः भेजने के लिए अपने प्रदाता से संपर्क करें।',
	IntakeProcessLearnMoreInstructions: 'अपने प्रवेश फॉर्म सेट करने के लिए गाइड',
	IntakeTemplateSelectorPlaceholder: 'अपने ग्राहक को भेजने के लिए फॉर्म और समझौते चुनें',
	Integration: 'एकीकरण',
	IntenseBlur: 'अपनी पृष्ठभूमि को तीव्रता से धुंधला करें',
	InteriorDesigner: 'आंतरिक डिज़ाइनर',
	InternetBanking: 'बैंक ट्रांसफर',
	Interval: 'अंतराल',
	IntervalDays: 'अंतराल (दिन)',
	IntervalHours: 'अंतराल (घण्टे में)',
	Invalid: 'अमान्य',
	InvalidDate: 'अमान्य दिनांक',
	InvalidDateFormat: 'तारीख {format} फॉर्मेट में होनी चाहिए',
	InvalidDisplayName: 'प्रदर्शन नाम में {value} शामिल नहीं हो सकता',
	InvalidEmailFormat: 'अमान्य ईमेल प्रारूप',
	InvalidFileType: 'अमान्य फ़ाइल प्रकार',
	InvalidGTMContainerId: 'अमान्य GTM कंटेनर ID प्रारूप',
	InvalidPaymentMethodCode: 'चयनित भुगतान विधि मान्य नहीं है। कृपया कोई अन्य चुनें।',
	InvalidPromotionCode: 'प्रमोशन कोड अवैध है',
	InvalidReferralDescription: 'पहले से ही Carepatron का उपयोग कर रहे हैं',
	InvalidStatementDescriptor: `कथन विवरणक 5 से 22 अक्षरों के बीच होना चाहिए और इसमें केवल अक्षर, संख्याएं, रिक्त स्थान होने चाहिए, तथा इसमें <, >, \\, ', ", * शामिल नहीं होना चाहिए`,
	InvalidToken: 'अमान्य टोकन',
	InvalidTotpSetupVerificationCode: 'अवैध सत्यापन संकेत।',
	InvalidURLErrorText: 'यह एक वैध URL होना चाहिए',
	InvalidZoomTokenErrorCodeSnackbar:
		'ज़ूम टोकन की समय-सीमा समाप्त हो गई है। कृपया अपना ज़ूम ऐप फिर से कनेक्ट करें और पुनः प्रयास करें।',
	Invite: 'आमंत्रित करना',
	InviteRelationships: 'रिश्तों को आमंत्रित करें',
	InviteToPortal: 'पोर्टल पर आमंत्रित करें',
	InviteToPortalModalDescription: 'आपके ग्राहक को केयरपेट्रॉन पर साइन अप करने के लिए एक आमंत्रण ईमेल भेजा जाएगा।',
	InviteToPortalModalTitle: '{name} को Carepatron पोर्टल में आमंत्रित करें',
	InviteUserDescription: ' ',
	InviteUserTitle: 'नये उपयोगकर्ता को आमंत्रित करें',
	Invited: 'आमंत्रित',
	Invoice: 'चालान',
	InvoiceColorPickerDescription: 'चालान में उपयोग की जाने वाली रंग थीम',
	InvoiceColorTheme: 'इनवॉइस रंग थीम',
	InvoiceContactDeleted: 'इनवॉइस संपर्क हटा दिया गया है और इस इनवॉइस को अद्यतन नहीं किया जा सकता.',
	InvoiceDate: 'तिथि जारी',
	InvoiceDetails: 'चालान विवरण',
	InvoiceFieldsPlaceholder: 'फ़ील्ड खोजें...',
	InvoiceFrom: 'इन्वॉइस {number} से {fromProvider}',
	InvoiceInvalidCredit: 'क्रेडिट राशि अमान्य है, क्रेडिट राशि चालान की कुल राशि से अधिक नहीं हो सकती',
	InvoiceNotFoundDescription:
		'कृपया अपने प्रदाता से संपर्क करें और उनसे अधिक जानकारी मांगें या चालान पुनः भेजने के लिए कहें।',
	InvoiceNotFoundTitle: 'चालान नहीं मिला',
	InvoiceNumber: 'चालान {हैशटैग}',
	InvoiceNumberFormat: 'इनवॉइस #{number}',
	InvoiceNumberMustEndWithDigit: 'चालान संख्या किसी अंक (0-9) से समाप्त होनी चाहिए',
	InvoicePageHeader: 'चालान',
	InvoicePaidNotificationSubject: 'इन्वॉइस {invoiceNumber} का भुगतान किया गया',
	InvoiceReminder: 'चालान अनुस्मारक',
	InvoiceReminderSentence: '{deliveryType} रिमाइंडर भेजें {interval} {unit} {beforeAfter} इनवॉइस देय तिथि',
	InvoiceReminderSettings: 'इनवॉइस अनुस्मारक सेटिंग',
	InvoiceReminderSettingsInfo: 'अनुस्मारक केवल केयरपेट्रॉन पर भेजे गए चालानों पर लागू होते हैं',
	InvoiceReminders: 'चालान अनुस्मारक',
	InvoiceRemindersInfo:
		'चालान की देय तिथियों के लिए स्वचालित अनुस्मारक सेट करें। अनुस्मारक केवल Carepatron के माध्यम से भेजे गए चालानों पर लागू होते हैं',
	InvoiceSettings: 'इनवॉइस सेटिंग',
	InvoiceStatus: 'चालान स्थिति',
	InvoiceTemplateAddressPlaceholder: '123 मेन स्ट्रीट, एनीटाउन, यूएसए',
	InvoiceTemplateDescriptionPlaceholder:
		'वैकल्पिक भुगतान के लिए नोट्स, बैंक हस्तांतरण विवरण या नियम और शर्तें जोड़ें',
	InvoiceTemplateEmploymentStatusPlaceholder: 'स्वनियोजित',
	InvoiceTemplateEthnicityPlaceholder: 'कोकेशियान',
	InvoiceTemplateNotFoundDescription: 'कृपया अपने प्रदाता से संपर्क करें और उनसे अधिक जानकारी मांगें।',
	InvoiceTemplateNotFoundTitle: 'इनवॉइस टेम्प्लेट नहीं मिला',
	InvoiceTemplates: 'इनवॉइस टेम्पलेट्स',
	InvoiceTemplatesDescription:
		'अपने ब्रांड को प्रतिबिंबित करने, विनियामक आवश्यकताओं को पूरा करने, और हमारे उपयोगकर्ता-अनुकूल टेम्पलेट्स के साथ ग्राहक की प्राथमिकताओं को पूरा करने के लिए अपने इनवॉइस टेम्पलेट्स को अनुकूलित करें।',
	InvoiceTheme: 'चालान विषय',
	InvoiceTotal: 'इनवॉइस कुल',
	InvoiceUninvoicedAmounts: 'चालान रहित राशि का चालान करें',
	InvoiceUpdateVersionMessage:
		'इस इनवॉइस को संपादित करने के लिए नवीनतम संस्करण की आवश्यकता है। कृपया Carepatron को पुनः लोड करें और पुनः प्रयास करें।',
	Invoices: '{count, plural, one {चालान} other {चालान}}',
	InvoicesEmptyStateDescription: 'कोई चालान नहीं मिला',
	InvoicingAndPayment: 'चालान-प्रक्रिया ',
	Ireland: 'आयरलैंड',
	IsA: 'एक है',
	IsBetween: 'के बीच है',
	IsEqualTo: 'के बराबर है',
	IsGreaterThan: 'से बड़ा है',
	IsGreaterThanOrEqualTo: 'से बड़ा या बराबर है',
	IsLessThan: 'मै रुक जाना',
	IsLessThanOrEqualTo: 'से कम या बराबर है',
	IssueCredit: 'क्रेडिट जारी करें',
	IssueCreditAdjustment: 'क्रेडिट समायोजन जारी करें',
	IssueDate: 'जारी करने की तिथि',
	Italic: 'तिरछा',
	Items: 'सामान',
	ItemsAndAdjustments: 'आइटम और एडजस्टमेंट',
	ItemsRemaining: '+{count} शेष आइटम',
	JobTitle: 'नौकरी का शीर्षक',
	Join: 'जोड़ना',
	JoinCall: 'कॉल में शामिल हों',
	JoinNow: 'अब शामिल हों',
	JoinProduct: '{product} से जुड़ें',
	JoinVideoCall: 'वीडियो कॉल में शामिल हों',
	JoinWebinar: 'वेबिनार में शामिल हों',
	JoinWithVideoCall: '{product} से जुड़ें',
	Journalist: 'पत्रकार',
	JustMe: 'केवल मैं',
	JustYou: 'सिर्फ तुम',
	Justify: 'औचित्य',
	KeepSeparate: 'अलग रखें',
	KeepSeparateSuccessMessage: 'आपने {clientNames} के लिए अलग-अलग रिकॉर्ड सफलतापूर्वक रखे हैं।',
	KeepWaiting: 'इंतज़ार करते रहें',
	Label: 'लेबल',
	LabelOptional: 'लेबल (वैकल्पिक)',
	LactationConsulting: 'स्तनपान परामर्श',
	Language: 'भाषा',
	Large: 'बड़ा',
	LastDxCode: 'पिछला DX कोड',
	LastLoggedIn: 'आखिरी बार लॉग इन {date} को {time} पर किया गया',
	LastMenstrualPeriod: 'अंतिम मासिक धर्म',
	LastMonth: 'पिछला महीना',
	LastNDays: 'पिछले {number} दिन',
	LastName: 'उपनाम',
	LastNameFirstInitial: 'अंतिम नाम, प्रथम अक्षर',
	LastWeek: 'पिछले सप्ताह',
	LastXRay: 'अंतिम एक्स-रे',
	LatestVisitOrConsultation: 'नवीनतम यात्रा या परामर्श',
	Lawyer: 'वकील',
	LearnMore: 'और अधिक जानें',
	LearnMoreTipsToGettingStarted: 'शुरू करने के लिए और सुझाव जानें',
	LearnToSetupInbox: 'इनबॉक्स खाता सेट अप करने के लिए मार्गदर्शिका',
	Leave: 'छुट्टी',
	LeaveCall: 'कॉल छोड़ें',
	LeftAlign: 'बाएं संरेखित करें',
	LegacyBillingItemsNotAvailable:
		'इस अपॉइंटमेंट के लिए अलग-अलग बिलिंग आइटम अभी उपलब्ध नहीं हैं। आप इसे सामान्य रूप से इन्वॉइस कर सकते हैं।',
	LegacyBillingItemsNotAvailableTitle: 'पुराना बिलिंग',
	LegalAndConsent: 'कानूनी और सहमति',
	LegalConsentFormPrimaryText: 'कानूनी सहमति',
	LegalConsentFormSecondaryText: 'विकल्प स्वीकार या अस्वीकार करें',
	LegalGuardian: 'कानूनी अभिभावक',
	Letter: 'पत्र',
	LettersCategoryDescription: 'नैदानिक ​​और प्रशासनिक पत्राचार बनाने के लिए',
	Librarian: 'लाइब्रेरियन',
	LicenseNumber: 'लाइसेंस संख्या',
	LifeCoach: 'जीवन का कोच',
	LifeCoaches: 'जीवन कोच',
	Limited: 'सीमित',
	LineSpacing: 'पंक्ति और पैराग्राफ़ स्पेसिंग',
	LinearScaleFormPrimaryText: 'रेखीय पैमाना',
	LinearScaleFormSecondaryText: 'स्केल विकल्प 1-10',
	Lineitems: 'लाइन आइटम',
	Link: 'जोड़ना',
	LinkClientFormSearchClientLabel: 'ग्राहक खोजें',
	LinkClientModalTitle: 'मौजूदा ग्राहक से लिंक करें',
	LinkClientSuccessDescription:
		'<strong>{newName}</strong> का संपर्क जानकारी <strong>{existingName}</strong> के रिकॉर्ड में जोड़ा गया है।',
	LinkClientSuccessTitle: 'मौजूदा संपर्क से सफलतापूर्वक लिंक किया गया',
	LinkForCallCopied: 'लिंक कॉपी किया गया!',
	LinkToAnExistingClient: 'किसी मौजूदा ग्राहक से लिंक करें',
	LinkToClient: 'ग्राहक से लिंक करें',
	ListAndTracker: 'सूची/ट्रैकर',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} इस कॉल में है}
			other {{attendees} इस कॉल में हैं}
		}`,
	ListStyles: 'सूची शैलियाँ',
	ListsAndTrackersCategoryDescription: 'काम को व्यवस्थित और ट्रैक करने के लिए',
	LivingArrangements: 'रहने की व्यवस्था',
	LoadMore: 'और लोड करें',
	Loading: 'लोड हो रहा है...',
	LocalizationPanelDescription: 'अपनी भाषा और समयक्षेत्र के लिए सेटिंग प्रबंधित करें',
	LocalizationPanelTitle: 'भाषा और समयक्षेत्र',
	Location: 'जगह',
	LocationDescription:
		'अपॉइंटमेंट और वीडियो कॉल को आसान बनाने के लिए विशिष्ट पते, कमरे के नाम और वर्चुअल स्थानों के प्रकार के साथ भौतिक और वर्चुअल स्थान सेट करें।',
	LocationNumber: 'स्थान संख्या',
	LocationOfService: 'सेवा का स्थान',
	LocationOfServiceRecommendedActionInfo: 'इस सेवा में एक विशिष्ट स्थान जोड़ने से आपकी उपलब्धता प्रभावित हो सकती है।',
	LocationRemote: 'दूरस्थ',
	LocationType: 'स्थान का प्रकार',
	Locations: 'स्थानों',
	Lock: 'ताला',
	Locked: 'बंद',
	LockedNote: 'लॉक किया गया नोट',
	LogInToSaveOrAuthoriseCard: 'कार्ड को सहेजने या अधिकृत करने के लिए लॉग इन करें',
	LogInToSaveOrAuthorisePayment: 'भुगतान को सहेजने या अधिकृत करने के लिए लॉग इन करें',
	Login: 'लॉग इन करें',
	LoginButton: 'दाखिल करना',
	LoginEmail: 'ईमेल',
	LoginForgotPasswordLink: 'पासवर्ड भूल गए',
	LoginPassword: 'पासवर्ड',
	Logo: 'प्रतीक चिन्ह',
	LogoutAreYouSure: 'इस डिवाइस से साइन आउट करें.',
	LogoutButton: 'साइन आउट',
	London: 'लंदन',
	LongTextAnswer: 'लंबा पाठ उत्तर',
	LongTextFormPrimaryText: 'लंबा पाठ',
	LongTextFormSecondaryText: 'पैराग्राफ़ शैली विकल्प',
	Male: 'पुरुष',
	Manage: 'प्रबंधित करना',
	ManageAllClientTags: 'सभी क्लाइंट टैग प्रबंधित करें',
	ManageAllNoteTags: 'सभी नोट टैग प्रबंधित करें',
	ManageAllTemplateTags: 'सभी टेम्पलेट टैग प्रबंधित करें',
	ManageConnections: 'संबंधों का प्रबंधन',
	ManageConnectionsGmailDescription: 'अन्य टीम सदस्य आपके सिंक किए गए Gmail को नहीं देख पाएंगे.',
	ManageConnectionsGoogleCalendarDescription:
		'अन्य टीम सदस्य आपके सिंक किए गए कैलेंडर नहीं देख पाएंगे। क्लाइंट की अपॉइंटमेंट केवल केयरपेट्रॉन के भीतर से ही अपडेट या डिलीट की जा सकती हैं।',
	ManageConnectionsInboxSyncHelperText: 'कृपया सिंक इनबॉक्स सेटिंग्स को प्रबंधित करने के लिए इनबॉक्स पृष्ठ पर जाएं।',
	ManageConnectionsMicrosoftCalendarDescription:
		'अन्य टीम सदस्य आपके सिंक किए गए कैलेंडर नहीं देख पाएंगे। क्लाइंट की अपॉइंटमेंट केवल केयरपेट्रॉन के भीतर से ही अपडेट या डिलीट की जा सकती हैं।',
	ManageConnectionsOutlookDescription: 'अन्य टीम सदस्य आपके सिंक किए गए Microsoft Outlook को नहीं देख पाएंगे.',
	ManageInboxAccountButton: 'नया इनबॉक्स',
	ManageInboxAccountEdit: 'इनबॉक्स प्रबंधित करें',
	ManageInboxAccountPanelTitle: 'इनबॉक्स',
	ManageInboxAssignTeamPlaceholder: 'इनबॉक्स एक्सेस के लिए टीम के सदस्यों को चुनें',
	ManageInboxBasicInfoColor: 'रंग',
	ManageInboxBasicInfoDescription: 'विवरण',
	ManageInboxBasicInfoDescriptionPlaceholder: 'आप या आपकी टीम इस इनबॉक्स का उपयोग किस लिए करेंगे?',
	ManageInboxBasicInfoName: 'इनबॉक्स नाम',
	ManageInboxBasicInfoNamePlaceholder: 'जैसे ग्राहक सहायता, व्यवस्थापक',
	ManageInboxConnectAppAlreadyConnectedError:
		'जिस चैनल से आपने जुड़ने का प्रयास किया है वह पहले से ही Carepatron से जुड़ा हुआ है',
	ManageInboxConnectAppConnect: 'जोड़ना',
	ManageInboxConnectAppConnectedInfo: 'किसी खाते से कनेक्ट किया गया',
	ManageInboxConnectAppContinue: 'जारी रखना',
	ManageInboxConnectAppEmail: 'ईमेल',
	ManageInboxConnectAppSignInWith: 'के साथ साइन इन करें',
	ManageInboxConnectAppSubtitle:
		'अपने सभी संचारों को एक ही केंद्रीकृत स्थान पर निर्बाध रूप से भेजने, प्राप्त करने और ट्रैक करने के लिए अपने ऐप्स को कनेक्ट करें।',
	ManageInboxNewInboxTitle: 'नया इनबॉक्स',
	ManagePlan: 'योजना प्रबंधित करें',
	ManageProfile: 'प्रोफ़ाइल प्रबंधित करें',
	ManageReferralsModalDescription:
		'हमारे स्वास्थ्य सेवा प्लेटफ़ॉर्म के बारे में लोगों को बताने में हमारी मदद करें और पुरस्कार अर्जित करें।',
	ManageReferralsModalTitle: 'किसी मित्र को रेफर करें, पुरस्कार अर्जित करें!',
	ManageStaffRelationshipsAddButton: 'रिश्तों का प्रबंधन करें',
	ManageStaffRelationshipsEmptyStateText: 'कोई संबंध नहीं जोड़ा गया',
	ManageStaffRelationshipsModalDescription:
		'ग्राहकों का चयन करने से नए संबंध जुड़ेंगे, जबकि उनका चयन रद्द करने से मौजूदा संबंध हट जाएंगे।',
	ManageStaffRelationshipsModalTitle: 'रिश्तों का प्रबंधन करें',
	ManageStatuses: 'स्थितियाँ प्रबंधित करें',
	ManageStatusesActiveStatusHelperText: 'कम से कम एक सक्रिय स्थिति आवश्यक है',
	ManageStatusesDescription:
		'अपने स्टेटस लेबल को अनुकूलित करें और अपने वर्कफ़्लो के साथ संरेखित करने के लिए रंग चुनें.',
	ManageStatusesSuccessSnackbar: 'स्थितियाँ सफलतापूर्वक अपडेट की गईं',
	ManageTags: 'टैग प्रबंधित करें',
	ManageTaskAttendeeStatus: 'अपॉइंटमेंट की स्थिति प्रबंधित करें',
	ManageTaskAttendeeStatusDescription:
		'अपनी कार्यप्रणाली के साथ संरेखित करने के लिए अपनी अपॉइंटमेंट स्थितियों को कस्टमाइज़ करें।',
	ManageTaskAttendeeStatusHelperText: 'कम से कम एक स्थिति की आवश्यकता है',
	ManageTaskAttendeeStatusSubtitle: 'कस्टम स्टेटस',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'नियमावली',
	ManualAppointment: 'मैन्युअल अपॉइंटमेंट',
	ManualPayment: 'मैन्युअल भुगतान',
	ManuallyTypeLocation: 'स्थान मैन्युअल रूप से टाइप करें',
	MapColumns: 'नक्शा स्तंभ',
	MappingRequired: 'मैपिंग आवश्यक',
	MarkAllAsRead: 'सभी को पढ़ा हुआ मार्क करें',
	MarkAsCompleted: 'पूर्ण के रूप में चिह्नित करें',
	MarkAsManualSubmission: 'जमा कर दिया गया चिह्नित करें',
	MarkAsPaid: 'भुगतान के रूप में चिह्नित करें',
	MarkAsRead: 'पढ़े हुए का चिह्न',
	MarkAsUnpaid: 'अवैतनिक के रूप में चिह्नित करें',
	MarkAsUnread: 'अपठित के रूप में चिह्नित करें',
	MarkAsVoid: 'शून्य के रूप में चिह्नित करें',
	Marker: 'निशान',
	MarketingManager: 'विपणन प्रबंधक',
	MassageTherapist: 'मालिश चिकित्सक',
	MassageTherapists: 'मालिश चिकित्सक',
	MassageTherapy: 'मसाज थैरेपी',
	MaxBookingTimeDescription1: 'ग्राहक अधिकतम समय निर्धारित कर सकते हैं',
	MaxBookingTimeDescription2: 'भविष्य में',
	MaxBookingTimeLabel: '{timePeriod} पहले',
	MaxCapacity: 'अधिकतम क्षमता',
	Maximize: 'अधिकतम',
	MaximumAttendeeLimit: 'अधिकतम सीमा',
	MaximumBookingTime: 'अधिकतम बुकिंग समय',
	MaximumBookingTimeError: 'अधिकतम बुकिंग समय {valueUnit} से अधिक नहीं होना चाहिए',
	MaximumMinimizedPanelsReachedDescription:
		'आप एक समय में अधिकतम {count} साइड पैनल कम कर सकते हैं। आगे बढ़ने पर सबसे पहले कम किया गया पैनल बंद हो जाएगा। क्या आप जारी रखना चाहते हैं?',
	MaximumMinimizedPanelsReachedTitle: 'आपके पास बहुत ज़्यादा पैनल खुले हैं।',
	MechanicalEngineer: 'यांत्रिक इंजीनियर',
	MediaGallery: 'मीडिया गैलरी',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'मेडिकेड प्रदाता संख्या',
	MedicalAssistant: 'चिकित्सा सहायक',
	MedicalCoder: 'मेडिकल कोडर',
	MedicalDoctor: 'चिकित्सा वैद्य',
	MedicalIllustrator: 'मेडिकल इलस्ट्रेटर',
	MedicalInterpreter: 'चिकित्सा दुभाषिया',
	MedicalTechnologist: 'मेडिकल टेक्नोलॉजिस्ट',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'मेडिकेयर प्रदाता संख्या',
	Medicine: 'दवा',
	Medium: 'मध्यम',
	Meeting: 'बैठक',
	MeetingEnd: 'बैठक समाप्त करें',
	MeetingEnded: 'बैठक समाप्त हुई',
	MeetingHost: 'मीटिंग होस्ट',
	MeetingLowerHand: 'निचला हाथ',
	MeetingOpenChat: 'चैट खोलें',
	MeetingPersonRaisedHand: '{name} ने अपना हाथ उठाया',
	MeetingRaiseHand: 'हाथ उठाओ',
	MeetingReady: 'बैठक तैयार',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {मिनट} other {मिनट}} {status}',
	Meetings: 'बैठक',
	MemberId: 'सदस्य पहचान पत्र',
	MentalHealth: 'मानसिक स्वास्थ्य',
	MentalHealthPractitioners: 'मानसिक स्वास्थ्य चिकित्सक',
	MentalHealthProfessional: 'मानसिक स्वास्थ्य पेशेवर',
	Merge: 'मर्ज',
	MergeClientRecords: 'क्लाइंट रिकॉर्ड्स मर्ज करें',
	MergeClientRecordsDescription:
		'क्लाइंट रिकॉर्ड्स को मर्ज करने से उनके सभी डेटा को मिला दिया जाएगा, जिसमें शामिल हैं:',
	MergeClientRecordsDescription2: 'क्या आप मर्ज जारी रखना चाहते हैं? यह क्रिया पूर्ववत नहीं की जा सकती',
	MergeClientRecordsItem1: 'नोट्स और दस्तावेज़',
	MergeClientRecordsItem2: 'नियुक्तियाँ',
	MergeClientRecordsItem3: 'इनवॉइस',
	MergeClientRecordsItem4: 'बातचीत',
	MergeClientsSuccess: 'क्लाइंट रिकॉर्ड सफलतापूर्वक मर्ज किया गया',
	MergeLimitExceeded: 'आप एक बार में अधिकतम 4 क्लाइंट्स को मर्ज कर सकते हैं।',
	Message: 'संदेश',
	MessageAttachments: '{total} संलग्नक',
	Method: 'तरीका',
	MfaAvailabilityDisclaimer:
		'MFA केवल ईमेल और पासवर्ड लॉगिन के लिए उपलब्ध है। अपनी MFA सेटिंग में बदलाव करने के लिए, अपने ईमेल और पासवर्ड का उपयोग करके लॉग इन करें।',
	MfaDeviceLostPanelDescription:
		'वैकल्पिक रूप से, आप ईमेल के माध्यम से कोड प्राप्त करके अपनी पहचान सत्यापित कर सकते हैं।',
	MfaDeviceLostPanelTitle: 'क्या आपका MFA डिवाइस खो गया है?',
	MfaDidntReceiveEmailCode: 'क्या आपको कोड नहीं मिला? सहायता से संपर्क करें',
	MfaEmailOtpSendFailureSnackbar: 'ईमेल OTP भेजने में विफल.',
	MfaEmailOtpSentSnackbar: 'एक कोड {maskedEmail} पर भेज दिया गया है',
	MfaEmailOtpVerificationFailedSnackbar: 'ईमेल OTP सत्यापित करने में विफल.',
	MfaHasBeenSetUpText: 'आपने MFA सेट अप कर लिया है',
	MfaPanelDescription:
		'सुरक्षा की एक अतिरिक्त परत के लिए मल्टी-फ़ैक्टर ऑथेंटिकेशन (MFA) सक्षम करके अपने खाते को सुरक्षित करें। अनधिकृत पहुँच को रोकने के लिए एक द्वितीयक विधि के माध्यम से अपनी पहचान सत्यापित करें।',
	MfaPanelNotAuthorizedError: 'आपको उपयोगकर्ता नाम से साइन इन होना चाहिए ',
	MfaPanelRecommendationDescription:
		'आपने हाल ही में अपनी पहचान सत्यापित करने के लिए किसी वैकल्पिक विधि का उपयोग करके साइन इन किया है। अपने खाते को सुरक्षित रखने के लिए, एक नया MFA डिवाइस सेट अप करने पर विचार करें।',
	MfaPanelRecommendationTitle: '**सिफारिश:** अपने MFA डिवाइस को अपडेट करें',
	MfaPanelTitle: 'बहु-कारक प्रमाणीकरण (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'अपनी MFA सेटिंग अपडेट करने से पहले आपको अपना ईमेल सत्यापित करना होगा।',
	MfaRecommendationBannerDescription:
		'आपने हाल ही में अपनी पहचान सत्यापित करने के लिए किसी वैकल्पिक विधि का उपयोग करके साइन इन किया है। अपने खाते को सुरक्षित रखने के लिए, एक नया MFA डिवाइस सेट अप करने पर विचार करें।',
	MfaRecommendationBannerPrimaryAction: 'एमएफए स्थापित करें',
	MfaRecommendationBannerTitle: 'अनुशंसित',
	MfaRemovedSnackbarTitle: 'एमएफए हटा दिया गया है।',
	MfaSendEmailCode: 'कोड भेजें',
	MfaVerifyIdentityLostDeviceButton: 'मैंने अपने MFA डिवाइस तक पहुंच खो दी',
	MfaVerifyYourIdentityPanelDescription: 'अपने प्रमाणक ऐप में कोड की जांच करें और उसे नीचे दर्ज करें.',
	MfaVerifyYourIdentityPanelTitle: 'अपनी पहचान सत्यापित करो',
	MicCamWarningMessage: 'ब्राउज़र एड्रेस बार में अवरुद्ध आइकन पर क्लिक करके कैमरा और माइक्रोफ़ोन को अनब्लॉक करें।',
	MicCamWarningTitle: 'कैमरा और माइक्रोफ़ोन अवरुद्ध हैं',
	MicOff: 'माइक्रोफ़ोन बंद है',
	MicOn: 'माइक्रोफ़ोन चालू है',
	MicSource: 'माइक्रोफ़ोन स्रोत',
	MicWarningMessage: 'आपके माइक्रोफ़ोन में कोई समस्या पाई गई है',
	Microphone: 'माइक्रोफ़ोन',
	MicrophonePermissionBlocked: 'माइक्रोफ़ोन तक पहुँच अवरुद्ध',
	MicrophonePermissionBlockedDescription: 'रिकॉर्डिंग शुरू करने के लिए अपने माइक्रोफ़ोन अनुमतियों को अपडेट करें।',
	MicrophonePermissionError: 'कृपया जारी रखने के लिए ब्राउज़र सेटिंग में माइक्रोफ़ोन की अनुमति दें',
	MicrophonePermissionPrompt: 'कृपया जारी रखने के लिए माइक्रोफ़ोन एक्सेस की अनुमति दें',
	Microsoft: 'माइक्रोसॉफ्ट',
	MicrosoftCalendar: 'माइक्रोसॉफ्ट',
	MicrosoftColor: 'आउटलुक कैलेंडर रंग',
	MicrosoftOutlook: 'माइक्रोसॉफ्ट दृष्टिकोण',
	MicrosoftTeams: 'माइक्रोसॉफ्ट टीम्स',
	MiddleEast: 'मध्य पूर्व',
	MiddleName: 'मध्य नाम',
	MiddleNames: 'मध्य नाम',
	Midwife: 'दाई',
	Midwives: 'धात्रियों',
	Milan: 'मिलान',
	MinBookingTimeDescription1: 'ग्राहक समय-सीमा के भीतर शेड्यूल नहीं कर सकते',
	MinBookingTimeDescription2: 'किसी अपॉइंटमेंट के प्रारंभ समय का',
	MinBookingTimeLabel: 'अपॉइंटमेंट से {timePeriod} पहले',
	MinCancellationTimeEditModeDescription: 'निर्धारित करें कि ग्राहक बिना किसी दंड के कितने घंटे रद्द कर सकता है',
	MinCancellationTimeUnset: 'कोई न्यूनतम रद्दीकरण समय निर्धारित नहीं',
	MinCancellationTimeViewModeDescription: 'बिना जुर्माने के रद्दीकरण अवधि',
	MinMaxBookingTimeUnset: 'कोई समय निर्धारित नहीं',
	Minimize: 'छोटा करना',
	MinimizeConfirmationDescription:
		'आपका एक सक्रिय न्यूनतम पैनल है। यदि आप जारी रखते हैं, तो यह बंद हो जाएगा, और आप असुरक्षित डेटा खो सकते हैं।',
	MinimizeConfirmationTitle: 'बंद करें छोटा पैनल?',
	MinimumBookingTime: 'न्यूनतम बुकिंग समय',
	MinimumCancellationTime: 'न्यूनतम रद्दीकरण समय',
	MinimumPaymentError: 'ऑनलाइन भुगतान के लिए {minimumAmount} की न्यूनतम राशि आवश्यक है।',
	MinuteAbbreviated: 'मिनट',
	MinuteAbbreviation: '{count} {count, plural, one {मिनट} other {मिनट}}',
	Minutely: 'नियमित रूप से',
	MinutesPlural: '{age, plural, one {# मिनट} other {# मिनट}}',
	MiscellaneousInformation: 'विविध जानकारी',
	MissingFeatures: 'अनुपलब्ध सुविधाएँ',
	MissingPaymentMethod: 'कृपया अधिक स्टाफ सदस्यों को जोड़ने के लिए अपनी सदस्यता में भुगतान विधि जोड़ें।',
	MobileNumber: 'मोबाइल नंबर',
	MobileNumberOptional: 'मोबाइल नंबर (वैकल्पिक)',
	Modern: 'आधुनिक',
	Modifiers: 'संशोधक',
	ModifiersPlaceholder: 'संशोधक',
	Monday: 'सोमवार',
	Month: 'महीना',
	Monthly: 'महीने के',
	MonthlyCost: 'मासिक लागत',
	MonthlyOn: 'मासिक {date} को',
	MonthsPlural: '{age, plural, one {# महीना} other {# महीने}}',
	More: 'अधिक',
	MoreActions: 'अधिक क्रियाएँ',
	MoreSettings: 'अधिक सेटिंग',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'अधिकतर प्रयोग होने वाला',
	MostDownloaded: 'सर्वाधिक डाउनलोड',
	MostPopular: 'सबसे लोकप्रिय',
	Mother: 'माँ',
	MotherInLaw: 'सास',
	MoveDown: 'नीचे की ओर',
	MoveInboxConfirmationDescription:
		'इस ऐप कनेक्शन को फिर से असाइन करने से यह <strong>{currentInboxName}</strong> इनबॉक्स से हट जाएगा।',
	MoveTemplateToFolder: '"{templateTitle}" को ले जाएँ',
	MoveTemplateToFolderSuccess: '{templateTitle} को {folderTitle} में ले जाया गया।',
	MoveTemplateToIntakeFolderSuccessMessage: 'सफलतापूर्वक डिफ़ॉल्ट इनटेक फ़ोल्डर में ले जाया गया',
	MoveTemplateToNewFolder: 'इस आइटम को ले जाने के लिए एक नया फ़ोल्डर बनाएँ।',
	MoveToChosenFolder:
		'इस आइटम को स्थानांतरित करने के लिए एक फ़ोल्डर चुनें। यदि आवश्यक हो तो आप एक नया फ़ोल्डर बना सकते हैं।',
	MoveToFolder: 'फ़ोल्डर में जाएं',
	MoveToInbox: 'इनबॉक्स में ले जाएँ',
	MoveToNewFolder: 'नए फ़ोल्डर में जाएँ',
	MoveToSelectedFolder:
		'एक बार स्थानांतरित करने के बाद, आइटम चयनित फ़ोल्डर के अंतर्गत व्यवस्थित हो जाएगा और अपने वर्तमान स्थान पर दिखाई नहीं देगा।',
	MoveUp: 'बढ़ाना',
	MultiSpeciality: 'मल्टी-स्पेशियलिटी',
	MultipleChoiceFormPrimaryText: 'बहुविकल्पी',
	MultipleChoiceFormSecondaryText: 'अनेक विकल्प चुनें',
	MultipleChoiceGridFormPrimaryText: 'बहुविकल्पीय ग्रिड',
	MultipleChoiceGridFormSecondaryText: 'मैट्रिक्स से विकल्प चुनें',
	Mumbai: 'मुंबई',
	MusicTherapist: 'संगीत चिकित्सक',
	MustContainOneLetterError: 'इसमें कम से कम एक अक्षर अवश्य होना चाहिए',
	MustEndWithANumber: 'किसी संख्या से समाप्त होना चाहिए',
	MustHaveAtLeastXItems: 'कम से कम {count, plural, one {# आइटम} other {# आइटम}} होना चाहिए',
	MuteAudio: 'ऑडियो म्यूट करें',
	MuteEveryone: 'सभी को म्यूट करें',
	MyAvailability: 'मेरी उपलब्धता',
	MyGallery: 'मेरी गैलरी',
	MyPortal: 'मेरा पोर्टल',
	MyRelationships: 'मेरे रिश्ते',
	MyTemplates: 'टीम टेम्पलेट्स',
	MyofunctionalTherapist: 'मायोफंक्शनल थेरेपिस्ट',
	NCalifornia: 'उत्तरी कैलिफोर्निया',
	NPI: 'एनपीआई',
	NVirginia: 'उत्तर वर्जीनिया',
	Name: 'नाम',
	NameIsRequired: 'नाम आवश्यक है',
	NameMustNotBeAWebsite: 'नाम किसी वेबसाइट का नहीं होना चाहिए',
	NameMustNotBeAnEmail: 'नाम ईमेल नहीं होना चाहिए',
	NameMustNotContainAtSign: 'नाम में @ चिह्न नहीं होना चाहिए',
	NameMustNotContainHTMLTags: 'नाम में HTML टैग नहीं होना चाहिए',
	NameMustNotContainSpecialCharacters: 'नाम में विशेष वर्ण नहीं होने चाहिए',
	NameOnCard: 'कार्ड पर नाम',
	NationalProviderId: 'राष्ट्रीय प्रदाता पहचानकर्ता (एनपीआई)',
	NaturopathicDoctor: 'प्राकृतिक चिकित्सक',
	NavigateToPersonalSettings: 'प्रोफ़ाइल',
	NavigateToSubscriptionSettings: 'सदस्यता सेटिंग्स',
	NavigateToWorkspaceSettings: 'कार्यक्षेत्र सेटिंग्स',
	NavigateToYourTeam: 'टीम प्रबंधित करें',
	NavigationDrawerBilling: 'बिलिंग',
	NavigationDrawerBillingInfo: 'बिलिंग जानकारी, चालान और स्ट्राइप',
	NavigationDrawerCommunication: 'संचार',
	NavigationDrawerCommunicationInfo: 'सूचनाएं और टेम्पलेट्स',
	NavigationDrawerInsurance: 'बीमा',
	NavigationDrawerInsuranceInfo: 'बीमा भुगतानकर्ता और दावे',
	NavigationDrawerInvoices: 'बिलिंग',
	NavigationDrawerPersonal: 'मेरी प्रोफाइल',
	NavigationDrawerPersonalInfo: 'आपकी व्यक्तिगत जानकारी',
	NavigationDrawerProfile: 'प्रोफ़ाइल',
	NavigationDrawerProviderSettings: 'सेटिंग्स',
	NavigationDrawerScheduling: 'निर्धारण',
	NavigationDrawerSchedulingInfo: 'सेवा विवरण और बुकिंग',
	NavigationDrawerSettings: 'सेटिंग्स',
	NavigationDrawerTemplates: 'टेम्पलेट्स',
	NavigationDrawerTemplatesV2: 'टेम्पलेट्स V2',
	NavigationDrawerTrash: 'कचरा',
	NavigationDrawerTrashInfo: 'हटाए गए आइटम पुनर्स्थापित करें',
	NavigationDrawerWorkspace: 'कार्यस्थान सेटिंग्स',
	NavigationDrawerWorkspaceInfo: 'सदस्यता और कार्यक्षेत्र जानकारी',
	NegativeBalanceNotSupported: 'ऋणात्मक खाता शेष समर्थित नहीं हैं',
	Nephew: 'भतीजा',
	NetworkQualityFair: 'निष्पक्ष कनेक्शन',
	NetworkQualityGood: 'अच्छा कनेक्शन',
	NetworkQualityPoor: 'खराब कनेक्शन',
	Neurologist: 'न्यूरोलॉजिस्ट',
	Never: 'कभी नहीं',
	New: 'नया',
	NewAppointment: 'नई नियुक्ति',
	NewClaim: 'नया दावा',
	NewClient: 'नए ग्राहक',
	NewClientNextStepsModalAddAnotherClient: 'एक और ग्राहक जोड़ें',
	NewClientNextStepsModalBookAppointment: 'अपॉइंटमेंट बुक करें',
	NewClientNextStepsModalBookAppointmentDescription: 'आगामी अपॉइंटमेंट बुक करें या कोई कार्य बनाएं.',
	NewClientNextStepsModalCompleteBasicInformation: 'संपूर्ण ग्राहक रिकॉर्ड',
	NewClientNextStepsModalCompleteBasicInformationDescription: 'ग्राहक जानकारी जोड़ें और अगले चरण कैप्चर करें.',
	NewClientNextStepsModalCreateInvoice: 'चालान बनाएं',
	NewClientNextStepsModalCreateInvoiceDescription: 'ग्राहक भुगतान जानकारी जोड़ें या चालान बनाएं.',
	NewClientNextStepsModalCreateNote: 'नोट बनाएं या दस्तावेज़ अपलोड करें',
	NewClientNextStepsModalCreateNoteDescription: 'ग्राहक नोट्स और दस्तावेज़ कैप्चर करें.',
	NewClientNextStepsModalDescription: 'अब जब आपने ग्राहक रिकॉर्ड बना लिया है तो आपको कुछ कार्यवाहियां करनी होंगी।',
	NewClientNextStepsModalSendIntake: 'इनटेक भेजें',
	NewClientNextStepsModalSendIntakeDescription:
		'ग्राहक की जानकारी एकत्रित करें तथा पूरा करने तथा हस्ताक्षर करने के लिए अतिरिक्त फॉर्म भेजें।',
	NewClientNextStepsModalSendMessage: 'मेसेज भेजें',
	NewClientNextStepsModalSendMessageDescription: 'अपने ग्राहक को एक संदेश लिखें और भेजें।',
	NewClientNextStepsModalTitle: 'अगले कदम',
	NewClientSuccess: 'नया ग्राहक सफलतापूर्वक बनाया गया',
	NewClients: 'नये ग्राहक',
	NewConnectedApp: 'नया कनेक्टेड ऐप',
	NewContact: 'नया संपर्क',
	NewContactNextStepsModalAddRelationship: 'रिश्ते जोड़ें',
	NewContactNextStepsModalAddRelationshipDescription: 'इस संपर्क को संबंधित क्लाइंट या समूहों से जोड़ें।',
	NewContactNextStepsModalBookAppointment: 'अपॉइंटमेंट बुक करें',
	NewContactNextStepsModalBookAppointmentDescription: 'आगामी अपॉइंटमेंट बुक करें या कोई कार्य बनाएँ।',
	NewContactNextStepsModalCompleteProfile: 'पूर्ण प्रोफ़ाइल',
	NewContactNextStepsModalCompleteProfileDescription: 'संपर्क जानकारी जोड़ें और अगले चरणों को कैप्चर करें।',
	NewContactNextStepsModalCreateNote: 'नोट बनाएँ या दस्तावेज़ अपलोड करें',
	NewContactNextStepsModalCreateNoteDescription: 'क्लाइंट नोट्स और दस्तावेज़ कैप्चर करें।',
	NewContactNextStepsModalDescription:
		'यहाँ कुछ क्रियाएँ दी गई हैं जिन्हें अब संपर्क बनाए जाने के बाद करने की आवश्यकता है।',
	NewContactNextStepsModalInviteToPortal: 'पोर्टल पर आमंत्रित करें',
	NewContactNextStepsModalInviteToPortalDescription: 'पोर्टल तक पहुँचने के लिए एक आमंत्रण भेजें।',
	NewContactNextStepsModalTitle: 'अगले कदम',
	NewContactSuccess: 'नया संपर्क सफलतापूर्वक बनाया गया',
	NewDateOverrideButton: 'नई तिथि ओवरराइड',
	NewDiagnosis: 'निदान जोड़ें',
	NewField: 'नया क्षेत्र',
	NewFolder: 'नया फ़ोल्डर',
	NewInvoice: 'नया चालान',
	NewLocation: 'नया स्थान',
	NewLocationFailure: 'नया स्थान बनाने में विफल',
	NewLocationSuccess: 'नया स्थान सफलतापूर्वक बनाया गया',
	NewManualPayer: 'नया मैन्युअल भुगतानकर्ता',
	NewNote: 'नया नोट',
	NewNoteCreated: 'नया नोट सफलतापूर्वक बनाया गया',
	NewPassword: 'नया पासवर्ड',
	NewPayer: 'नया भुगतानकर्ता',
	NewPaymentMethod: 'नई भुगतान विधि',
	NewPolicy: 'नई नीति',
	NewRelationship: 'नए रिश्ते',
	NewReminder: 'नया अनुस्मारक',
	NewSchedule: 'नया शेड्यूल',
	NewSection: 'नया अनुभाग',
	NewSectionOld: 'नया अनुभाग [पुराना]',
	NewSectionWithGrid: 'ग्रिड के साथ नया अनुभाग',
	NewService: 'नई सेवा',
	NewServiceFailure: 'नई सेवा बनाने में विफल',
	NewServiceSuccess: 'नई सेवा सफलतापूर्वक बनाई गई',
	NewStatus: 'नई स्थिति',
	NewTask: 'नया कार्य',
	NewTaxRate: 'नई कर दर',
	NewTeamMemberNextStepsModalAssignClients: 'क्लाइंट असाइन करें',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'अपने टीम सदस्य को विशिष्ट क्लाइंट असाइन करें।',
	NewTeamMemberNextStepsModalAssignServices: 'सेवाएँ असाइन करें',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'उनके असाइन किए गए सेवाओं का प्रबंधन करें और आवश्यकतानुसार मूल्य निर्धारण को समायोजित करें।',
	NewTeamMemberNextStepsModalBookAppointment: 'अपॉइंटमेंट बुक करें',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'आगामी अपॉइंटमेंट बुक करें या कोई कार्य बनाएँ।',
	NewTeamMemberNextStepsModalCompleteProfile: 'पूर्ण प्रोफ़ाइल',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'अपने टीम सदस्य के बारे में विवरण जोड़कर उनके प्रोफ़ाइल को पूरा करें।',
	NewTeamMemberNextStepsModalDescription: 'यहाँ कुछ कार्रवाइयाँ दी गई हैं जो आप टीम सदस्य बनाने के बाद कर सकते हैं।',
	NewTeamMemberNextStepsModalEditPermissions: 'संपादन अनुमतियों',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'उनके पहुँच स्तरों को समायोजित करें ताकि यह सुनिश्चित हो सके कि उनके पास सही अनुमतियाँ हैं।',
	NewTeamMemberNextStepsModalSetAvailability: 'उपलब्धता सेट करें',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'उनकी उपलब्धता को अनुसूची बनाने के लिए कॉन्फ़िगर करें।',
	NewTeamMemberNextStepsModalTitle: 'अगला कदम',
	NewTemplateFolderDescription: 'अपने दस्तावेज़ों को व्यवस्थित करने के लिए एक नया फ़ोल्डर बनाएँ.',
	NewUIUpdateBannerButton: 'ऐप पुनः लोड करें',
	NewUIUpdateBannerTitle: 'एक नया अपडेट तैयार है!',
	NewZealand: 'न्यूज़ीलैंड',
	Newest: 'नवीनतम',
	NewestUnreplied: 'नवीनतम अनुत्तरित',
	Next: 'अगला',
	NextInvoiceIssueDate: 'अगला इनवॉइस जारी करने की तिथि',
	NextNDays: 'अगले {number} दिन',
	Niece: 'भतीजी',
	No: 'नहीं',
	NoAccessGiven: 'कोई पहुँच नहीं दी गई',
	NoActionConfigured: 'कोई कार्रवाई कॉन्फ़िगर नहीं की गई',
	NoActivePolicies: 'कोई सक्रिय नीति नहीं',
	NoActiveReferrals: 'आपके पास कोई सक्रिय रेफ़रल नहीं है',
	NoAppointmentsFound: 'कोई नियुक्ति नहीं मिली',
	NoAppointmentsHeading: 'क्लाइंट अपॉइंटमेंट और गतिविधि प्रबंधित करें',
	NoArchivedPolicies: 'कोई संग्रहीत नीतियाँ नहीं',
	NoAvailableTimes: 'कोई उपलब्ध समय नहीं मिला।',
	NoBillingItemsFound: 'कोई बिलिंग आइटम नहीं मिला',
	NoCalendarsSynced: 'कोई कैलेंडर समन्वयित नहीं किया गया',
	NoClaimsFound: 'कोई दावे नहीं मिले',
	NoClaimsHeading: 'वापसी के लिए दावे जमा करने की प्रक्रिया को सुव्यवस्थित करें',
	NoClientsHeading: 'अपने ग्राहकों के रिकॉर्ड एक साथ लाएँ',
	NoCompletedReferrals: 'आपके पास कोई पूर्ण रेफ़रल नहीं है',
	NoConnectionsHeading: 'अपने ग्राहक संचार को सुव्यवस्थित करें',
	NoContactsGivenAccess: 'किसी भी ग्राहक या संपर्क को इस नोट तक पहुंच नहीं दी गई है',
	NoContactsHeading: 'अपने अभ्यास का समर्थन करने वालों के साथ जुड़े रहें',
	NoCopayOrCoinsurance: 'कोई सह-भुगतान या सह-बीमा नहीं',
	NoCustomServiceSchedule: 'कोई कस्टम शेड्यूल सेट नहीं है - उपलब्धता टीम के सदस्य की उपलब्धता पर निर्भर है',
	NoDescription: 'कोई विवरण नहीं',
	NoDocumentationHeading: 'सुरक्षित रूप से नोट्स बनाएं और संग्रहीत करें',
	NoDuplicateRecordsHeading: 'आपका क्लाइंट रिकॉर्ड डुप्लिकेट से मुक्त है',
	NoEffect: 'कोई प्रभाव नहीं',
	NoEnrolmentProfilesFound: 'कोई नामांकन प्रोफ़ाइल नहीं मिली',
	NoGlossaryItems: 'कोई शब्दावली आइटम नहीं',
	NoInvitedReferrals: 'आपके पास कोई आमंत्रित रेफ़रल नहीं है',
	NoInvoicesFound: 'कोई चालान नहीं मिला',
	NoInvoicesHeading: 'अपने बिलिंग और भुगतान को स्वचालित करें',
	NoLimit: 'कोई सीमा नहीं',
	NoLocationsFound: 'कोई स्थान नहीं मिला',
	NoLocationsWillBeAdded: 'कोई भी स्थान नहीं जोड़ा जाएगा।',
	NoNoteFound: 'कोई नोट नहीं मिला',
	NoPaymentMethods: 'आपके पास कोई सहेजी हुई भुगतान विधि नहीं है, आप भुगतान करते समय कोई विधि जोड़ सकते हैं.',
	NoPermissionError: 'आपको अनुमति नहीं है',
	NoPermissions: 'आपको यह पृष्ठ देखने की अनुमति नहीं है',
	NoPolicy: 'कोई रद्दीकरण नीति नहीं जोड़ी गई',
	NoRecordsHeading: 'अपने ग्राहक रिकॉर्ड को निजीकृत करें',
	NoRecordsToDisplay: 'प्रदर्शित करने के लिए कोई {resource} नहीं',
	NoRelationshipsHeading: 'अपने ग्राहक का समर्थन करने वालों से जुड़े रहें',
	NoRemindersFound: 'कोई अनुस्मारक नहीं मिला',
	NoResultsFound: 'कोई परिणाम नहीं मिला',
	NoResultsFoundDescription: 'हमें आपकी खोज से मेल खाने वाला कोई आइटम नहीं मिला',
	NoServicesAdded: 'कोई सेवाएँ नहीं जोड़ी गईं',
	NoServicesApplied: 'कोई सेवा लागू नहीं की गई',
	NoServicesWillBeAdded: 'कोई भी सेवा नहीं जोड़ी जाएगी.',
	NoTemplate: 'आपके पास कोई अभ्यास टेम्पलेट सहेजा नहीं गया है',
	NoTemplatesHeading: 'अपने स्वयं के टेम्पलेट बनाएं',
	NoTemplatesInFolder: 'इस फ़ोल्डर में कोई टेम्पलेट नहीं हैं',
	NoTitle: 'कोई शीर्षक नहीं',
	NoTrashItemsHeading: 'कोई हटाया गया आइटम नहीं मिला',
	NoTriggerConfigured: 'कोई ट्रिगर कॉन्फ़िगर नहीं किया गया',
	NoUnclaimedItemsFound: 'कोई भी दावा न किया गया आइटम नहीं मिला.',
	NonAiTemplates: 'गैर-एआई टेम्पलेट्स',
	None: 'कोई नहीं',
	NotAvailable: 'उपलब्ध नहीं',
	NotCovered: 'शामिल नहीं किया हुआ',
	NotFoundSnackbar: 'संसाधन नहीं मिला.',
	NotRequiredField: 'आवश्यक नहीं',
	Note: 'टिप्पणी',
	NoteDuplicateSuccess: 'नोट सफलतापूर्वक डुप्लिकेट किया गया',
	NoteEditModeViewSwitcherDescription: 'नोट बनाएं और संपादित करें',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} ने {noteTitle} फॉर्म सबमिट किया',
	NoteLockSuccess: '{title} लॉक हो गया है',
	NoteModalAttachmentButton: 'अनुलग्नक जोड़ें',
	NoteModalPhotoButton: 'फ़ोटो जोड़ें/कैप्चर करें',
	NoteModalTrascribeButton: 'लाइव ऑडियो ट्रांसक्राइब करें',
	NoteResponderModeViewSwitcherDescription: 'फ़ॉर्म भेजें और प्रतिक्रियाओं की समीक्षा करें',
	NoteResponderModeViewSwitcherTooltipTitle: 'अपने ग्राहकों की ओर से जवाब दें और फॉर्म जमा करें',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'ग्राहक के रूप में फॉर्म भरें और सबमिट करें',
	NoteUnlockSuccess: '{title} अनलॉक हो गया है',
	NoteViewModeViewSwitcherDescription: 'केवल देखने की पहुंच',
	Notes: 'नोट्स',
	NotesAndForms: 'नोट्स और फॉर्म',
	NotesCategoryDescription: 'क्लाइंट इंटरैक्शन को डॉक्यूमेंट करने के लिए',
	NothingToSeeHere: 'यहाँ देखने के लिए कुछ नहीं है',
	Notification: 'अधिसूचना',
	NotificationIgnoredMessage: 'सभी {notificationType} सूचनाओं को अनदेखा किया जाएगा',
	NotificationRestoredMessage: 'सभी {notificationType} सूचनाएँ बहाल कर दी गई हैं',
	NotificationSettingBillingDescription: 'क्लाइंट भुगतान अपडेट और रिमाइंडर के लिए सूचनाएं प्राप्त करें।',
	NotificationSettingBillingTitle: 'बिलिंग और भुगतान',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} केवल} other{{channels}} }',
	NotificationSettingClientDocumentationDescription: 'क्लाइंट भुगतान अपडेट और रिमाइंडर के लिए सूचनाएं प्राप्त करें।',
	NotificationSettingClientDocumentationTitle: 'क्लाइंट और दस्तावेज़ीकरण',
	NotificationSettingCommunicationsDescription:
		'अपने कनेक्टेड चैनलों से इनबॉक्स और अपडेट के लिए सूचनाएं प्राप्त करें',
	NotificationSettingCommunicationsTitle: 'संचार',
	NotificationSettingEmail: 'ईमेल',
	NotificationSettingInApp: 'ऐप में',
	NotificationSettingPanelDescription:
		'उन सूचनाओं को चुनें जिन्हें आप गतिविधियों और सिफारिशों के लिए प्राप्त करना चाहते हैं।',
	NotificationSettingPanelTitle: 'सूचना वरीयताएँ',
	NotificationSettingSchedulingDescription:
		'जब कोई टीम सदस्य या ग्राहक अपनी अपॉइंटमेंट बुक करता है, पुनर्निर्धारित करता है या रद्द करता है तो सूचनाएं प्राप्त करें।',
	NotificationSettingSchedulingTitle: 'निर्धारण',
	NotificationSettingUpdateSuccess: 'सूचना सेटिंग्स सफलतापूर्वक अपडेट की गईं',
	NotificationSettingWhereYouReceiveNotifications: 'आप इन सूचनाओं को कहां प्राप्त करना चाहते हैं',
	NotificationSettingWorkspaceDescription:
		'सिस्टम परिवर्तनों, समस्याओं, डेटा ट्रांसफर और सदस्यता अनुस्मारकों के लिए सूचनाएं प्राप्त करें।',
	NotificationSettingWorkspaceTitle: 'कार्यस्थान',
	NotificationTemplateUpdateFailed: 'अधिसूचना टेम्प्लेट अपडेट करने में विफल',
	NotificationTemplateUpdateSuccess: 'अधिसूचना टेम्प्लेट सफलतापूर्वक अपडेट किया गया',
	NotifyAttendeesOfTaskCancellationModalDescription: 'क्या आप उपस्थित लोगों को रद्दीकरण सूचना ईमेल भेजना चाहेंगे?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'रद्दीकरण भेजें',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'क्या आप उपस्थित लोगों को एक पुष्टिकरण अधिसूचना ईमेल भेजना चाहेंगे?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'पुष्टि भेजें',
	NotifyAttendeesOfTaskDeletedModalTitle: 'क्या आप उपस्थित लोगों को रद्दीकरण ईमेल भेजना चाहेंगे?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {करता} other {करते}} ईमेल पता नहीं है, इसलिए उन्हें स्वचालित सूचनाएँ और अनुस्मारक नहीं मिलेंगे।',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {के पास} other {के पास}} ईमेल पता नहीं है, इसलिए स्वचालित सूचनाएँ और अनुस्मारक प्राप्त नहीं होंगे।',
	NotifyAttendeesOfTaskModalTitle: 'क्या आप उपस्थित लोगों को सूचना ईमेल भेजना चाहेंगे?',
	NotifyAttendeesOfTaskSnackbar: 'अधिसूचना भेजना',
	NuclearMedicineTechnologist: 'न्यूक्लियर मेडिसिन टेक्नोलॉजिस्ट',
	NumberOfClaims: '{number, plural, one {# दावा} other {# दावे}}',
	NumberOfClients: '{number, plural, one {# क्लाइंट} other {# क्लाइंट्स}}',
	NumberOfContacts: '{number, plural, one {# संपर्क} other {# संपर्क}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {प्रविष्टि} other {प्रविष्टियाँ}} मिली',
	NumberOfErrors: '{count, plural, one {# त्रुटि} other {# त्रुटियाँ}}',
	NumberOfInvoices: '{number, plural, one {# चालान} other {# चालान}}',
	NumberOfLineitemsToCredit:
		'आपके पास <mark>{count} {count, plural, one {line item} other {line items}}</mark> क्रेडिट जारी करने के लिए हैं।',
	NumberOfPayments: '{number, plural, one {# भुगतान} other {# भुगतान}}',
	NumberOfRelationships: '{number, plural, one {# संबंध} other {# संबंध}}',
	NumberOfResources: '{number, plural, one {# संसाधन} other {# संसाधन}}',
	NumberOfTeamMembers: '{number, plural, one {# टीम सदस्य} other {# टीम सदस्य}}',
	NumberOfTrashItems: '{number, plural, one {# आइटम} other {# आइटम}}',
	NumberOfUninvoicedAmounts:
		'आपके पास <mark>{count} अनइन्वॉयस्ड {count, plural, one {संपूर्ण} other {संपूर्ण}}</mark> इन्वॉइस करने के लिए हैं',
	NumberedList: 'क्रमांकित सूची',
	Nurse: 'देखभाल करना',
	NurseAnesthetist: 'नर्स एनेस्थेटिस्ट',
	NurseAssistant: 'नर्स सहायक',
	NurseEducator: 'नर्स शिक्षक',
	NurseMidwife: 'नर्स दाई',
	NursePractitioner: 'नर्स प्रैक्टिशनर',
	Nurses: 'नर्स',
	Nursing: 'नर्सिंग',
	Nutritionist: 'पोषण विशेषज्ञ',
	Nutritionists: 'पोषण विशेषज्ञ',
	ObstetricianOrGynecologist: 'प्रसूति/स्त्री रोग विशेषज्ञ',
	Occupation: 'पेशा',
	OccupationalTherapist: 'व्यावसायिक चिकित्सक',
	OccupationalTherapists: 'व्यावसायिक चिकित्सक',
	OccupationalTherapy: 'व्यावसायिक चिकित्सा',
	Occurrences: 'पुनरावृत्तियां',
	Of: 'का',
	Ohio: 'ओहियो',
	OldPassword: 'पुराना पासवर्ड',
	OlderMessages: '{count} पुराने संदेश',
	Oldest: 'सबसे पुराने',
	OldestUnreplied: 'सबसे पुराना अनुत्तरित',
	On: 'पर',
	OnboardingBusinessAgreement: 'मेरी और व्यापार की ओर से, मैं {businessAssociateAgreement} से सहमत हूँ',
	OnboardingLoadingOccupationalTherapist:
		'<mark>व्यावसायिक चिकित्सक</mark> केयरपेट्रॉन पर हमारे ग्राहकों का एक चौथाई हिस्सा बनता है',
	OnboardingLoadingProfession:
		'हमारे पास Carepatron का उपयोग करने और पनपने वाले <mark>{profession}</mark> की भरमार है।',
	OnboardingLoadingPsychologist: '<mark>मनोवैज्ञानिकों</mark> केयरपेट्रॉन पर हमारे आधे से अधिक ग्राहक हैं',
	OnboardingLoadingSubtitleFive: 'हमारा मिशन है<mark> स्वास्थ्य सेवा सॉफ्टवेयर सुलभ</mark> सबके लिए।',
	OnboardingLoadingSubtitleFour:
		'<mark>सरलीकृत स्वास्थ्य सॉफ्टवेयर</mark> दुनिया भर में 10,000 से अधिक लोगों के लिए।',
	OnboardingLoadingSubtitleThree:
		'बचाना<mark> प्रति सप्ताह 1 दिन</mark> केयरपेट्रॉन की सहायता से प्रशासनिक कार्यों पर ध्यान केन्द्रित करना।',
	OnboardingLoadingSubtitleTwo:
		'बचाना<mark> 2 घंटे</mark> केयरपेट्रॉन की सहायता से प्रतिदिन प्रशासनिक कार्यों पर ध्यान केन्द्रित करता हूँ।',
	OnboardingReviewLocationOne: 'हॉलैंड पार्क मानसिक स्वास्थ्य केंद्र',
	OnboardingReviewLocationThree: 'प्रैक्टिस नर्स, माउंट ईडन हेल्थकेयर',
	OnboardingReviewLocationTwo: 'लाइफ हाउस क्लिनिक',
	OnboardingReviewNameOne: 'अनुल पी',
	OnboardingReviewNameThree: 'ऐलिस ई',
	OnboardingReviewNameTwo: 'क्लारा डब्ल्यू',
	OnboardingReviewOne:
		'"केयरपेट्रॉन का उपयोग करना बहुत ही सहज है। यह हमें अपना व्यवसाय इतनी अच्छी तरह से चलाने में मदद करता है कि हमें अब प्रशासकों की टीम की भी आवश्यकता नहीं है"',
	OnboardingReviewThree:
		'"यह सुविधाओं और लागत दोनों के संदर्भ में मेरे द्वारा उपयोग किया गया सर्वोत्तम अभ्यास समाधान है। इसमें वह सब कुछ है जो मुझे अपना व्यवसाय बढ़ाने के लिए चाहिए"',
	OnboardingReviewTwo:
		'"मुझे केयरपेट्रॉन ऐप भी बहुत पसंद है। इससे मुझे चलते-फिरते अपने ग्राहकों और काम पर नज़र रखने में मदद मिलती है।"',
	OnboardingTitle: `आइये शुरू करें<mark> जानना
 तुम बेहतर</mark>`,
	Oncologist: 'ऑन्कोलॉजिस्ट',
	Online: 'ऑनलाइन',
	OnlineBookingColorTheme: 'ऑनलाइन बुकिंग रंग थीम',
	OnlineBookings: 'ऑनलाइन बुकिंग',
	OnlineBookingsHelper: 'चुनें कि ऑनलाइन बुकिंग कब की जा सकती है और किस प्रकार के ग्राहकों द्वारा की जा सकती है',
	OnlinePayment: 'ऑनलाइन भुगतान',
	OnlinePaymentSettingCustomInfo: 'इस सेवा के लिए ऑनलाइन भुगतान सेटिंग वैश्विक बुकिंग सेटिंग से भिन्न होती है।',
	OnlinePaymentSettings: 'ऑनलाइन भुगतान सेटिंग',
	OnlinePaymentSettingsInfo:
		'भुगतान को सुरक्षित और सरल बनाने के लिए ऑनलाइन बुकिंग के समय सेवाओं के लिए भुगतान एकत्रित करें',
	OnlinePaymentSettingsPaymentsDisabled:
		'ऑनलाइन बुकिंग के दौरान भुगतान अक्षम हैं इसलिए एकत्र नहीं किए जा सकते हैं. कृपया भुगतान सक्षम करने के लिए अपनी भुगतान सेटिंग्स जांचें.',
	OnlinePaymentSettingsStripeNote:
		'ऑनलाइन बुकिंग भुगतान प्राप्त करने और अपनी भुगतान प्रक्रिया को सुव्यवस्थित करने के लिए {action}',
	OnlinePaymentsNotSupportedForCurrency: '{currency} में ऑनलाइन भुगतान का समर्थन नहीं किया जाता है।',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar: 'क्षमा करें, इस मुद्रा में ऑनलाइन भुगतान समर्थित नहीं हैं',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'क्षमा करें, आपके देश में अभी तक ऑनलाइन भुगतान समर्थित नहीं है',
	OnlineScheduling: 'ऑनलाइन शेड्यूलिंग',
	OnlyVisibleToYou: 'केवल आपको ही दिखाई देगा',
	OnlyYou: 'केवल आप',
	OnsetDate: 'प्रारंभ तिथि',
	OnsetOfCurrentSymptomsOrIllness: 'वर्तमान लक्षण या बीमारी की शुरुआत',
	Open: 'खुला',
	OpenFile: 'खुली फाइल',
	OpenSettings: 'खुली सेटिंग',
	Ophthalmologist: 'नेत्र-विशेषज्ञ',
	OptimiseTelehealthCalls: 'अपने टेलीहेल्थ कॉल को अनुकूलित करें',
	OptimizeServiceTimes: 'सेवा समय को अनुकूलित करें',
	Options: 'विकल्प',
	Optometrist: 'ऑप्टोमेट्रिस्ट',
	Or: 'या',
	OrAttachSingleFile: 'दस्तावेज संलग्न करें',
	OrDragAndDrop: 'या तो खींचें और छोड़ें',
	OrderBy: 'द्वारा आदेश',
	Oregon: 'ओरेगन',
	OrganisationOrIndividual: 'संगठन या व्यक्ति',
	OrganizationPlanInclusion1: 'उन्नत अनुमतियाँ',
	OrganizationPlanInclusion2: 'निःशुल्क क्लाइंट डेटा आयात समर्थन',
	OrganizationPlanInclusion3: 'समर्पित सफलता प्रबंधक',
	OrganizationPlanInclusionHeader: 'सब कुछ प्रोफेशनल में, प्लस...',
	Orthodontist: 'ओथडोटिस',
	Orthotist: 'ऑर्थोटिस्ट',
	Other: 'अन्य',
	OtherAdjustments: 'अन्य समायोजन',
	OtherAdjustmentsTableEmptyState: 'कोई समायोजन नहीं मिला',
	OtherEvents: 'अन्य घटनाएँ',
	OtherId: 'अन्य आईडी',
	OtherIdQualifier: 'अन्य आईडी क्वालीफायर',
	OtherPaymentMethod: 'अन्य भुगतान विधि',
	OtherPlanMessage:
		'अपने अभ्यास की आवश्यकताओं पर नियंत्रण रखें। अपनी वर्तमान योजना की समीक्षा करें, उपयोग की निगरानी करें, और अपनी टीम के बढ़ने पर अधिक सुविधाओं को अनलॉक करने के लिए उन्नयन विकल्पों का पता लगाएं।',
	OtherPolicy: 'अन्य बीमा',
	OtherProducts: 'आप अन्य कौन से उत्पाद या उपकरण उपयोग करते हैं?',
	OtherServices: 'अन्य सेवाएं',
	OtherTemplates: 'अन्य टेम्पलेट्स',
	Others: 'अन्य',
	OthersPeople: `{n, plural, 		one {1 अन्य व्यक्ति}
		other {# अन्य लोग}
	}`,
	OurResearchTeamReachOut:
		'क्या हमारी शोध टीम इस बारे में अधिक जानने के लिए आपसे संपर्क कर सकती है कि केयरपेट्रॉन आपकी आवश्यकताओं के लिए किस प्रकार बेहतर हो सकता था?',
	OutOfOffice: 'कार्यालय से बाहर',
	OutOfOfficeColor: 'कार्यालय से बाहर का रंग',
	OutOfOfficeHelper: 'चुने गए कुछ टीम सदस्य पद से बाहर हैं',
	OutsideLabCharges: 'प्रयोगशाला के बाहर के शुल्क',
	OutsideOfWorkingHours: 'कार्य समय के बाहर',
	OutsideWorkingHoursHelper: 'चुने गए कुछ टीम सदस्य कार्य समय के बाहर हैं',
	Overallocated: 'ओवरआबंटित',
	OverallocatedPaymentDescription: `यह भुगतान बिल योग्य मदों में अधिक आवंटित कर दिया गया है।
 अवैतनिक वस्तुओं के लिए आवंटन जोड़ें, या क्रेडिट या धन वापसी जारी करें।`,
	OverallocatedPaymentTitle: 'आवंटित भुगतान से अधिक',
	OverdueTerm: 'अतिदेय अवधि (दिन)',
	OverinvoicedAmount: 'अधिक चालान राशि',
	Overpaid: 'अधिक भुगतान',
	OverpaidAmount: 'अधिक भुगतान की गई राशि',
	Overtime: 'अधिक समय तक',
	Owner: 'मालिक',
	POS: 'पीओ',
	POSCode: 'पीओएस कोड',
	POSPlaceholder: 'पीओ',
	PageBlockerDescription: 'असुरक्षित परिवर्तन खो जाएँगे। फिर भी जाना चाहते हैं?',
	PageBlockerTitle: 'परिवर्तन त्यागें?',
	PageFormat: 'पृष्ठ प्रारूप',
	PageNotFound: 'पृष्ठ नहीं मिला',
	PageNotFoundDescription: 'अब आपके पास इस पृष्ठ तक पहुंच नहीं है या यह नहीं मिल सकता',
	PageUnauthorised: 'अनधिकृत पहुंच',
	PageUnauthorisedDescription: 'आपको इस पेज को एक्सेस करने की अनुमति नहीं है',
	Paid: 'चुकाया गया',
	PaidAmount: 'भुगतान की गई राशि',
	PaidAmountMinimumValueError: 'भुगतान की गई राशि 0 से अधिक होनी चाहिए',
	PaidAmountRequiredError: 'भुगतान की गई राशि आवश्यक है',
	PaidItems: 'भुगतान वाली वस्तुएं',
	PaidMultiple: 'चुकाया गया',
	PaidOut: 'बाहर का भुगतान किया',
	ParagraphStyles: 'पैराग्राफ शैलियाँ',
	Parent: 'माता-पिता',
	Paris: 'पेरिस',
	PartialRefundAmount: 'आंशिक रूप से वापस कर दिया गया ({amount} शेष)',
	PartiallyFull: 'आंशिक रूप से भरा हुआ',
	PartiallyPaid: 'आंशिक रूप से भुगतान किया',
	PartiallyRefunded: 'आशिंक रूप से की गई धनवापसी',
	Partner: 'साथी',
	Password: 'पासवर्ड',
	Past: 'अतीत',
	PastDateOverridesEmpty: 'जैसे ही इवेंट समाप्त हो जाएगा, आपकी तिथि ओवरराइड यहां दिखाई देगी',
	Pathologist: 'चिकित्सक',
	Patient: 'मरीज़',
	Pause: 'विराम',
	Paused: 'रोका गया',
	Pay: 'वेतन',
	PayMonthly: 'मासिक भुगतान करें',
	PayNow: 'अब भुगतान करें',
	PayValue: 'भुगतान करें {showPrice, select, true {{price}} अन्य {अभी}}',
	PayWithOtherCard: 'अन्य कार्ड से भुगतान करें',
	PayYearly: 'वार्षिक भुगतान करें',
	PayYearlyPercentOff: 'वार्षिक भुगतान करें <mark>{percent}% की छूट</mark>',
	Payer: 'भुगतानकर्ता',
	PayerClaimId: 'भुगतानकर्ता दावा आईडी',
	PayerCoverage: 'कवरेज',
	PayerDetails: 'भुगतानकर्ता विवरण',
	PayerDetailsDescription: 'अपने खाते में जोड़े गए भुगतानकर्ता विवरण देखें और नामांकन प्रबंधित करें।',
	PayerID: 'भुगतानकर्ता आईडी',
	PayerId: 'भुगतानकर्ता आईडी',
	PayerName: 'भुगतानकर्ता का नाम',
	PayerPhoneNumber: 'भुगतानकर्ता का फ़ोन नंबर',
	Payers: 'दाताओं',
	Payment: 'भुगतान',
	PaymentAccountUpdated: 'आपका खाता नवीनीकृत हो चुका है!',
	PaymentAccountUpgraded: 'आपका खाता अपग्रेड कर दिया गया है!',
	PaymentAmount: 'भुगतान राशि',
	PaymentDate: 'भुगतान तिथि',
	PaymentDetails: 'भुगतान विवरण',
	PaymentForUsersPerMonth: '{billedUsers, plural, one {# उपयोगकर्ता} other {# उपयोगकर्ता}} के लिए एक महीने का भुगतान',
	PaymentInfoFormPrimaryText: 'भुगतान जानकारी',
	PaymentInfoFormSecondaryText: 'भुगतान विवरण इकट्ठा करें',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'इस चालान का भुगतान पहले ही किया जा चुका है।',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'यह चालान पहले से ही संसाधित है.',
	PaymentIntentAmountMismatchSnackbar:
		'चालान की कुल राशि में संशोधन किया गया है। कृपया भुगतान करने से पहले परिवर्तनों की समीक्षा करें।',
	PaymentIntentSyncTimeoutSnackbar:
		'आपका भुगतान सफल रहा लेकिन समय समाप्त हो गया। कृपया पृष्ठ को रीफ़्रेश करें और यदि आपका भुगतान नहीं दिखता है तो कृपया सहायता से संपर्क करें।',
	PaymentMethod: 'भुगतान विधि',
	PaymentMethodDescription:
		'अपनी सदस्यता बिलिंग प्रक्रिया को सुव्यवस्थित करने के लिए अपनी प्रैक्टिस भुगतान विधि जोड़ें और प्रबंधित करें।',
	PaymentMethodLabelBank: 'बैंक खाता',
	PaymentMethodLabelCard: 'कार्ड',
	PaymentMethodLabelFallback: 'भुगतान विधि',
	PaymentMethodRequired: 'कृपया सदस्यता बदलने से पहले भुगतान विधि जोड़ें',
	PaymentMethods: 'भुगतान विधियाँ',
	PaymentProcessing: 'भुगतान प्रसंस्करण!',
	PaymentProcessingFee: 'भुगतान में {amount} प्रोसेसिंग शुल्क शामिल है',
	PaymentReports: 'भुगतान रिपोर्ट (ERA)',
	PaymentSettings: 'भुगतान सेटिंग',
	PaymentSuccessful: 'भुगतान सफल!',
	PaymentType: 'भुगतान प्रकार',
	Payments: 'भुगतान',
	PaymentsAccountDisabledNotificationSubject: `{paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} के माध्यम से ऑनलाइन भुगतान अक्षम कर दिए गए हैं।
कृपया भुगतान सक्षम करने के लिए अपनी भुगतान सेटिंग्स जांचें।`,
	PaymentsEmptyStateDescription: 'कोई भुगतान नहीं मिला है.',
	PaymentsUnallocated: 'असंबद्ध भुगतान',
	PayoutDate: 'भुगतान तिथि',
	PayoutsDisabled: 'भुगतान अक्षम',
	PayoutsEnabled: 'भुगतान सक्षम',
	PayoutsStatus: 'भुगतान स्थिति',
	Pediatrician: 'बच्चों का चिकित्सक',
	Pen: 'कलम',
	Pending: 'लंबित',
	People: '{rosterSize } लोग',
	PeopleCount: 'लोग ({count})',
	PerMonth: '/ महीना',
	PerUser: 'प्रति उपयोगकर्ता',
	Permission: 'अनुमति',
	PermissionRequired: 'अनुमति आवश्यक',
	Permissions: 'अनुमतियां',
	PermissionsClientAndContactDocumentation: 'ग्राहक ',
	PermissionsClientAndContactProfiles: 'ग्राहक ',
	PermissionsEditAccess: 'संपादन पहुंच',
	PermissionsInvoicesAndPayments: 'चालान ',
	PermissionsScheduling: 'निर्धारण',
	PermissionsUnassignClients: 'क्लाइंट को अनअसाइन करें',
	PermissionsUnassignClientsConfirmation: 'क्या आप वाकई इन ग्राहकों को अनअसाइन करना चाहते हैं?',
	PermissionsValuesAssigned: 'केवल असाइन किया गया',
	PermissionsValuesEverything: 'सब कुछ',
	PermissionsValuesNone: 'कोई नहीं',
	PermissionsValuesOwnCalendar: 'अपना कैलेंडर',
	PermissionsViewAccess: 'पहुंच देखें',
	PermissionsWorkspaceSettings: 'कार्यस्थान सेटिंग्स',
	Person: '{rosterSize} व्यक्ति',
	PersonalDetails: 'व्यक्तिगत विवरण',
	PersonalHealthcareHistoryStoreDescription:
		'उत्तर दें और अपने व्यक्तिगत स्वास्थ्य देखभाल इतिहास को एक स्थान पर सुरक्षित रूप से संग्रहीत करें',
	PersonalTrainer: 'निजी प्रशिक्षक',
	PersonalTraining: 'व्यक्तिगत प्रशिक्षण',
	PersonalizeWorkspace: 'अपना कार्यक्षेत्र वैयक्तिकृत करें',
	PersonalizingYourWorkspace: 'अपने कार्यस्थल को निजीकृत करना',
	Pharmacist: 'फार्मेसिस्ट',
	Pharmacy: 'फार्मेसी',
	PhoneCall: 'फोन कॉल',
	PhoneNumber: 'फ़ोन नंबर',
	PhoneNumberOptional: 'दूरभाष क्रमांक (वैकल्पिक)',
	PhotoBy: 'द्वारा तसवीर',
	PhysicalAddress: 'भौतिक पता',
	PhysicalTherapist: 'भौतिक चिकित्सक',
	PhysicalTherapists: 'भौतिक चिकित्सक',
	PhysicalTherapy: 'शारीरिक चिकित्सा',
	Physician: 'चिकित्सक',
	PhysicianAssistant: 'चिकित्सक सहायक',
	Physicians: 'चिकित्सकों',
	Physiotherapist: 'फ़िज़ियोथेरेपिस्ट',
	PlaceOfService: 'सेवा का स्थान',
	Plan: 'योजना',
	PlanAndReport: 'योजना/रिपोर्ट',
	PlanId: 'योजना आईडी',
	PlansAndReportsCategoryDescription: 'इलाज की योजना बनाने और परिणामों का सारांशित करने के लिए',
	PleaseRefreshThisPageToTryAgain: 'कृपया फिर से प्रयास करने के लिए इस पृष्ठ को रीफ्रेश करें।',
	PleaseWait: 'कृपया प्रतीक्षा करें...',
	PleaseWaitForHostToJoin: 'होस्ट के शामिल होने की प्रतीक्षा कर रहा हूँ...',
	PleaseWaitForHostToStart: 'कृपया मेज़बान द्वारा इस बैठक को शुरू करने की प्रतीक्षा करें।',
	PlusAdd: '+ जोड़ें',
	PlusOthers: '+{count} अन्य',
	PlusPlanInclusionFive: 'साझा इनबॉक्स',
	PlusPlanInclusionFour: 'समूह वीडियो कॉल',
	PlusPlanInclusionHeader: 'सब कुछ आवश्यक  ',
	PlusPlanInclusionOne: 'असीमित एआई',
	PlusPlanInclusionSix: 'कस्टम ब्रांडिंग',
	PlusPlanInclusionThree: 'समूह शेड्यूलिंग',
	PlusPlanInclusionTwo: 'असीमित भंडारण ',
	PlusSubscriptionPlanSubtitle: 'प्रथाओं को अनुकूलित करने और विकसित करने के लिए',
	PlusSubscriptionPlanTitle: 'प्लस',
	PoliceOfficer: 'पुलिस अधिकारी',
	PolicyDates: 'पॉलिसी तिथियां',
	PolicyHolder: 'पॉलिसी धारक',
	PolicyHoldersAddress: 'पॉलिसी धारक का पता',
	PolicyMemberId: 'पॉलिसी सदस्य आईडी',
	PolicyStatus: 'पॉलिसी की स्थिति',
	Popular: 'लोकप्रिय',
	PortalAccess: 'पोर्टल तक पहुंच',
	PortalNoAppointmentsHeading: 'सभी आगामी और पिछली नियुक्तियों पर नज़र रखें',
	PortalNoDocumentationHeading: 'अपने दस्तावेज़ सुरक्षित रूप से बनाएँ और संग्रहीत करें',
	PortalNoRelationshipsHeading: 'अपनी यात्रा का समर्थन करने वालों को साथ लाएँ',
	PosCodeErrorMessage: 'POS कोड आवश्यक है',
	PosoNumber: 'पीओ/एसओ नंबर',
	PossibleClientDuplicate: 'संभावित क्लाइंट डुप्लिकेट',
	PotentialClientDuplicateTitle: 'संभावित डुप्लिकेट क्लाइंट रिकॉर्ड',
	PotentialClientDuplicateWarning:
		'यह क्लाइंट जानकारी आपकी क्लाइंट सूची में पहले से मौजूद हो सकती है। यदि आवश्यक हो तो कृपया मौजूदा रिकॉर्ड को सत्यापित और अपडेट करें या नया क्लाइंट बनाना जारी रखें।',
	PoweredBy: 'द्वारा संचालित',
	Practice: 'अभ्यास',
	PracticeDetails: 'अभ्यास विवरण',
	PracticeInfoHeader: 'व्यावसायिक जानकारी',
	PracticeInfoPlaceholder: `अभ्यास का नाम,
 राष्ट्रीय प्रदाता पहचानकर्ता,
 नियोक्ता पहचान संख्या`,
	PracticeLocation: 'ऐसा लगता है कि आपका अभ्यास चल रहा है',
	PracticeSettingsAvailabilityTab: 'उपलब्धता',
	PracticeSettingsBillingTab: 'बिलिंग सेटिंग',
	PracticeSettingsClientSettingsTab: 'क्लाइंट सेटिंग्स',
	PracticeSettingsGeneralTab: 'सामान्य',
	PracticeSettingsOnlineBookingTab: 'ऑनलाइन बुकिंग',
	PracticeSettingsServicesTab: 'सेवाएं',
	PracticeSettingsTaxRatesTab: 'कर की दरें',
	PracticeTemplate: 'अभ्यास टेम्पलेट',
	Practitioner: 'व्यवसायी',
	PreferredLanguage: 'पसंदीदा भाषा',
	PreferredName: 'पसंदीदा नाम',
	Prescription: 'नुस्खा',
	PreventionSpecialist: 'रोकथाम विशेषज्ञ',
	Preview: 'पूर्व दर्शन',
	PreviewAndSend: 'पूर्वावलोकन करें और भेजें',
	PreviewUnavailable: 'इस फ़ाइल प्रकार के लिए पूर्वावलोकन उपलब्ध नहीं है',
	PreviousNotes: 'पिछले नोट्स',
	Price: 'कीमत',
	PriceError: 'मूल्य 0 से अधिक होना चाहिए',
	PricePerClient: 'प्रति ग्राहक मूल्य',
	PricePerUser: 'प्रति उपयोगकर्ता',
	PricePerUserBilledAnnually: 'प्रति उपयोगकर्ता वार्षिक बिल',
	PricePerUserPerPeriod: '{price} प्रति उपयोगकर्ता / {isMonthly, select, true {महीना} other {वर्ष}}',
	PricingGuide: 'मूल्य निर्धारण योजनाओं के लिए मार्गदर्शिका',
	PricingPlanPerMonth: '/ महीना',
	PricingPlanPerYear: '/ वर्ष',
	Primary: 'प्राथमिक',
	PrimaryInsurance: 'प्राथमिक बीमा',
	PrimaryPolicy: 'प्राथमिक बीमा',
	PrimaryTimezone: 'प्राथमिक समयक्षेत्र',
	Print: 'छाप',
	PrintToCms1500: 'CMS1500 पर प्रिंट करें',
	PrivatePracticeConsultant: 'निजी प्रैक्टिस सलाहकार',
	Proceed: 'आगे बढ़ें',
	ProcessAtTimeOfBookingDesc: 'ग्राहकों को ऑनलाइन बुकिंग के लिए पूरी सेवा कीमत चुकानी होगी',
	ProcessAtTimeOfBookingLabel: 'बुकिंग के समय भुगतान की प्रक्रिया करें',
	Processing: 'प्रसंस्करण',
	ProcessingFee: 'प्रक्रमण संसाधन शुल्क',
	ProcessingFeeToolTip: `केयरपेट्रॉन आपको अपने ग्राहकों से प्रसंस्करण शुल्क वसूलने की अनुमति देता है।
 कुछ अधिकार क्षेत्रों में अपने ग्राहकों से प्रोसेसिंग शुल्क लेना प्रतिबंधित है। लागू कानूनों का पालन करना आपकी ज़िम्मेदारी है।`,
	ProcessingRequest: 'अनुरोध संसाधित किया जा रहा है...',
	Product: 'उत्पाद',
	Profession: 'पेशा',
	ProfessionExample: 'चिकित्सक, पोषण विशेषज्ञ, दंत चिकित्सक',
	ProfessionPlaceholder: 'अपना पेशा टाइप करना शुरू करें या सूची में से चुनें',
	ProfessionalPlanInclusion1: 'असीमित भंडारण',
	ProfessionalPlanInclusion2: 'असीमित कार्य',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: '24/7 ग्राहक सहायता',
	ProfessionalPlanInclusion5: 'एसएमएस अनुस्मारक',
	ProfessionalPlanInclusionHeader: 'स्टार्टर में सब कुछ, प्लस...',
	Professions: 'पेशे',
	Profile: 'प्रोफ़ाइल',
	ProfilePhotoFileSizeLimit: '5MB फ़ाइल आकार सीमा',
	ProfilePopoverSubTitle: 'आप <strong>{email}</strong> के रूप में साइन इन हैं',
	ProfilePopoverTitle: 'आपके कार्यस्थान',
	PromoCode: 'प्रचार कोड',
	PromotionCodeApplied: '{promo} लागू किया गया',
	ProposeNewDateTime: 'नई तिथि/समय प्रस्तावित करें',
	Prosthetist: 'प्रोस्थेटिस्ट',
	Provider: 'प्रदाता',
	ProviderBillingPlanExpansionManageButton: 'योजना प्रबंधित करें',
	ProviderCommercialNumber: 'प्रदाता वाणिज्यिक संख्या',
	ProviderDetails: 'प्रदाता विवरण',
	ProviderDetailsAddress: 'पता',
	ProviderDetailsName: 'नाम',
	ProviderDetailsPhoneNumber: 'फ़ोन नंबर',
	ProviderHasExistingBillingAccountErrorCodeSnackbar:
		'क्षमा करें, इस प्रदाता के पास पहले से ही एक बिलिंग खाता मौजूद है',
	ProviderInfoPlaceholder: `स्टाफ का नाम,
 मेल पता,
 फ़ोन नंबर,
 राष्ट्रीय प्रदाता पहचानकर्ता,
 लाइसेंस संख्या`,
	ProviderIsChargedProcessingFee: 'आपको प्रोसेसिंग शुल्क का भुगतान करना होगा',
	ProviderPaymentFormBackButton: 'पीछे',
	ProviderPaymentFormBillingAddressCity: 'शहर',
	ProviderPaymentFormBillingAddressCountry: 'देश',
	ProviderPaymentFormBillingAddressLine1: 'लाइन 1',
	ProviderPaymentFormBillingAddressPostalCode: 'डाक कोड',
	ProviderPaymentFormBillingEmail: 'ईमेल',
	ProviderPaymentFormCardCvc: 'सीवीसी',
	ProviderPaymentFormCardDetailsTitle: 'क्रेडिट कार्ड के विवरण',
	ProviderPaymentFormCardExpiry: 'समाप्ति',
	ProviderPaymentFormCardHolderAddressTitle: 'पता',
	ProviderPaymentFormCardHolderName: 'कार्डधारक का नाम',
	ProviderPaymentFormCardHolderTitle: 'कार्डधारक विवरण',
	ProviderPaymentFormCardNumber: 'कार्ड संख्या',
	ProviderPaymentFormPlanTitle: 'चुनी गई योजना',
	ProviderPaymentFormPlanTotalTitle: 'कुल ({currency}):',
	ProviderPaymentFormSaveButton: 'सदस्यता सहेजें',
	ProviderPaymentFreePlanDescription:
		'मुफ़्त प्लान चुनने से आपके प्रदाता में प्रत्येक कर्मचारी की अपने क्लाइंट तक पहुँच समाप्त हो जाएगी। हालाँकि, आपकी पहुँच बनी रहेगी और आप अभी भी प्लेटफ़ॉर्म का उपयोग कर पाएँगे।',
	ProviderPaymentStepName: 'समीक्षा ',
	ProviderPaymentSuccessSnackbar: 'बढ़िया! आपकी नई योजना सफलतापूर्वक सहेज ली गई।',
	ProviderPaymentTitle: 'समीक्षा ',
	ProviderPlanNetworkIdentificationNumber: 'प्रदाता योजना नेटवर्क पहचान संख्या',
	ProviderRemindersSettingsBannerAction: 'वर्कफ़्लो प्रबंधन पर जाएँ',
	ProviderRemindersSettingsBannerDescription:
		'<b>सेटिंग्स</b> में नए <b>वर्कफ़्लो प्रबंधन</b> टैब के अंतर्गत सभी रिमाइंडर ढूँढ़ें। यह अपडेट आपकी उत्पादकता को बढ़ावा देने के लिए शक्तिशाली नई सुविधाएँ, बेहतर टेम्प्लेटिंग और स्मार्ट ऑटोमेशन टूल लाता है। 🚀',
	ProviderRemindersSettingsBannerTitle: 'आपका रिमाइंडर अनुभव बेहतर हो रहा है',
	ProviderTaxonomy: 'प्रदाता वर्गीकरण',
	ProviderUPINNumber: 'प्रदाता UPIN नंबर',
	ProviderUsedStoragePercentage: '{providerName} स्टोरेज {usedStoragePercentage}% भरा हुआ है!',
	PsychiatricNursePractitioner: 'मनोरोग नर्स प्रैक्टिशनर',
	Psychiatrist: 'मनोचिकित्सक',
	Psychiatrists: 'मनोचिकित्सकों',
	Psychiatry: 'मनोचिकित्सा',
	Psychoanalyst: 'मनोविश्लेषक',
	Psychologist: 'मनोविज्ञानी',
	Psychologists: 'मनोवैज्ञानिकों',
	Psychology: 'मनोविज्ञान',
	Psychometrician: 'मनोचिकित्सक',
	PsychosocialRehabilitationSpecialist: 'मनोसामाजिक पुनर्वास विशेषज्ञ',
	Psychotheraphy: 'मनोचिकित्सा',
	Psychotherapists: 'मनोचिकित्सकों',
	Psychotherapy: 'मनोचिकित्सा',
	PublicCallDialogTitle: 'वीडियो कॉल ',
	PublicCallDialogTitlePlaceholder: 'केयरपेट्रॉन द्वारा संचालित वीडियो कॉल',
	PublicFormBackToForm: 'एक और प्रतिक्रिया भेजें',
	PublicFormConfirmSubmissionHeader: 'सबमिशन की पुष्टि करें',
	PublicFormNotFoundDescription:
		'आप जिस फॉर्म की तलाश कर रहे हैं, वह हटा दिया गया होगा या लिंक गलत हो सकता है। कृपया URL जांचें और पुनः प्रयास करें।',
	PublicFormNotFoundTitle: 'फ़ॉर्म नहीं मिला',
	PublicFormSubmissionError: 'सबमिशन विफल रहा. कृपया पुनः प्रयास करें.',
	PublicFormSubmissionSuccess: 'फॉर्म सफलतापूर्वक सबमिट किया गया',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} ने {noteTitle} सार्वजनिक फॉर्म जमा किया',
	PublicFormSubmittedSubtitle: 'आपकी बिनती को प्राप्त किया गया है।',
	PublicFormSubmittedTitle: 'धन्यवाद!',
	PublicFormVerifyClientEmailDialogSubtitle: 'हमने आपके ईमेल पर एक पुष्टिकरण कोड भेज दिया है',
	PublicFormsInvalidConfirmationCode: 'अमान्य पुष्टिकरण कोड',
	PublicHealthInspector: 'सार्वजनिक स्वास्थ्य निरीक्षक',
	PublicTemplates: 'सार्वजनिक टेम्पलेट',
	Publish: 'प्रकाशित करना',
	PublishTemplate: 'टेम्पलेट प्रकाशित करें',
	PublishTemplateFeatureBannerSubheader: 'समुदाय के लाभ के लिए डिज़ाइन किए गए टेम्पलेट',
	PublishTemplateHeader: 'प्रकाशित करें {title}',
	PublishTemplateToCommunity: 'समुदाय को टेम्प्लेट प्रकाशित करें',
	PublishToCommunity: 'समुदाय में प्रकाशित करें',
	PublishToCommunitySuccessMessage: 'समुदाय में सफलतापूर्वक प्रकाशित',
	Published: 'प्रकाशित',
	PublishedBy: '{name} द्वारा प्रकाशित',
	PublishedNotesAreNotAutosaved: 'प्रकाशित नोट स्वतः सहेजे नहीं जाएंगे',
	PublishedOnCarepatronCommunity: 'केयरपेट्रॉन समुदाय पर प्रकाशित',
	Purchase: 'खरीदना',
	PushToCalendar: 'कैलेंडर पर पुश करें',
	Question: 'सवाल',
	QuestionOrTitle: 'प्रश्न या शीर्षक',
	QuickActions: 'त्वरित कार्रवाई',
	QuickThemeSwitcherColorBasil: 'तुलसी',
	QuickThemeSwitcherColorBlueberry: 'ब्लूबेरी',
	QuickThemeSwitcherColorFushcia: 'फुशिया',
	QuickThemeSwitcherColorLapis: 'लापिस',
	QuickThemeSwitcherColorMoss: 'मॉस',
	QuickThemeSwitcherColorRose: 'गुलाब',
	QuickThemeSwitcherColorSquash: 'स्क्वैश',
	RadiationTherapist: 'विकिरण चिकित्सक',
	Radiologist: 'रेडियोलोकेशन करनेवाला',
	Read: 'पढ़ना',
	ReadOnly: 'केवल पढ़ने के लिए',
	ReadOnlyAppointment: 'केवल पठन नियुक्ति',
	ReadOnlyEventBanner: 'यह अपॉइंटमेंट एक रीड-ओनली कैलेंडर से सिंक किया गया है और इसे एडिट नहीं किया जा सकता है।',
	ReaderMaxDepthHasBeenExceededCode: 'नोट बहुत ज़्यादा नेस्टेड है। कुछ आइटम अनइंडेंट करने का प्रयास करें।',
	ReadyForMapping: 'मैपिंग के लिए तैयार',
	RealEstateAgent: 'रियल एस्टेट एजेंट',
	RearrangeClientFields: 'क्लाइंट सेटिंग में क्लाइंट फ़ील्ड को पुनर्व्यवस्थित करें',
	Reason: 'कारण',
	ReasonForChange: 'परिवर्तन के लिए कारण',
	RecentAppointments: 'हाल की नियुक्तियाँ',
	RecentServices: 'हालिया सेवाएँ',
	RecentTemplates: 'हाल के टेम्पलेट्स',
	RecentlyUsed: 'हाल ही में उपयोग किया गया',
	Recommended: 'सिफारिशी',
	RecommendedTemplates: 'अनुशंसित टेम्पलेट्स',
	Recording: 'रिकॉर्डिंग',
	RecordingEnded: 'रिकॉर्डिंग समाप्त',
	RecordingInProgress: 'रिकॉर्डिंग जारी है',
	RecordingMicrophoneAccessErrorMessage:
		'कृपया अपने ब्राउज़र में माइक्रोफ़ोन एक्सेस की अनुमति दें और रिकॉर्डिंग शुरू करने के लिए रीफ्रेश करें।',
	RecurrenceCount: ', {count, plural, one {एक बार} other {# बार}}',
	RecurrenceDaily: '{count, plural, one {रोज़ाना} other {दिन}}',
	RecurrenceEndAfter: 'बाद में',
	RecurrenceEndNever: 'कभी नहीं',
	RecurrenceEndOn: 'पर',
	RecurrenceEvery: 'हर {description}',
	RecurrenceMonthly: '{count, plural, one {मासिक} other {महीने}}',
	RecurrenceOn: 'पर {description}',
	RecurrenceOnAllDays: 'सभी दिनों',
	RecurrenceUntil: 'जब तक {description}',
	RecurrenceWeekly: '{count, plural, one {साप्ताहिक} other {सप्ताह}}',
	RecurrenceYearly: '{count, plural, one {वार्षिक} other {वर्ष}}',
	Recurring: 'पुनरावर्ती',
	RecurringAppointment: 'आवर्ती नियुक्ति',
	RecurringAppointmentsLimitedBannerText:
		'सभी आवर्ती अपॉइंटमेंट नहीं दिखाए गए हैं। अवधि के लिए सभी आवर्ती अपॉइंटमेंट देखने के लिए दिनांक सीमा कम करें।',
	RecurringEventListDescription:
		'<b>{count, plural, one {# घटना} other {# घटनाएँ}}</b> निम्नलिखित तिथियों पर बनाई जाएँगी',
	Redo: 'फिर से करना',
	ReferFriends: 'मित्रों को रेफर करें',
	Reference: 'संदर्भ',
	ReferralCreditedNotificationSubject: 'आपका रेफ़रल क्रेडिट {currency} {amount} लागू किया गया है',
	ReferralEmailDefaultBody: `{name} के धन्यवाद से, आपको Carepatron का मुफ़्त 3 महीने का अपग्रेड भेजा गया है। काम करने के एक नए तरीके के लिए बने 3 मिलियन से ज़्यादा स्वास्थ्य सेवा व्यवसायियों के हमारे समुदाय में शामिल हों!
धन्यवाद,
Carepatron टीम`,
	ReferralEmailDefaultSubject: 'आपको Carepatron में शामिल होने के लिए आमंत्रित किया गया है',
	ReferralHasNotSignedUpDescription: 'आपके मित्र ने अभी तक साइन अप नहीं किया है',
	ReferralHasSignedUpDescription: 'आपके मित्र ने साइन अप कर लिया है।',
	ReferralInformation: 'रेफरल जानकारी',
	ReferralJoinedNotificationSubject: '{actorProfileName} Carepatron में शामिल हो गए हैं',
	ReferralListErrorDescription: 'रेफरल सूची लोड नहीं की जा सकी.',
	ReferralProgress:
		'<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {महीना} other {महीने}}</b> सक्रिय',
	ReferralRewardBanner: 'साइन अप करें और अपना रेफरल इनाम प्राप्त करें!',
	Referrals: 'रेफरल',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} महीने {percentOff, select, 100 {मुफ्त भुगतान किया गया} other {{percentOff}% छूट}} {type, select, SubscriptionUpgrade {अपग्रेड} other {}}',
	ReferredUserBenefitTitle: 'वे मिलते हैं!',
	Referrer: 'संदर्भ',
	ReferringProvider: 'संदर्भित प्रदाता',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} क्रेडिट जब <mark>3 दोस्त</mark> सक्रिय होते हैं।',
	ReferringUserBenefitTitle: 'आपको मिला!',
	RefreshPage: 'पृष्ठ रीफ़्रेश करें',
	Refund: 'धनवापसी',
	RefundAcknowledgement: 'मैंने {clientName} को Carepatron के बाहर वापस कर दिया है',
	RefundAcknowledgementValidationMessage: 'कृपया पुष्टि करें कि आपने यह राशि वापस कर दी है',
	RefundAmount: 'वापसी राशि',
	RefundContent:
		'आपके क्लाइंट के खाते में रिफ़ंड आने में 7-10 दिन लगते हैं। भुगतान शुल्क वापस नहीं किया जाएगा, लेकिन रिफ़ंड के लिए कोई अतिरिक्त शुल्क नहीं है। रिफ़ंड को रद्द नहीं किया जा सकता है, और कुछ को संसाधित करने से पहले समीक्षा की आवश्यकता हो सकती है।',
	RefundCouldNotBeProcessed: 'धन वापसी संसाधित नहीं की जा सकी',
	RefundError:
		'इस समय यह धनवापसी स्वचालित रूप से संसाधित नहीं की जा सकती। कृपया इस भुगतान को वापस करने के लिए Carepatron सहायता से संपर्क करें।',
	RefundExceedTotalValidationError: 'राशि कुल भुगतान से अधिक नहीं होनी चाहिए',
	RefundFailed: 'धन वापसी विफल',
	RefundFailedTooltip:
		'इस भुगतान को वापस करना पहले विफल हो गया था और अब इसे फिर से नहीं किया जा सकता। कृपया सहायता से संपर्क करें।',
	RefundNonStripePaymentContent:
		'यह भुगतान केयरपैट्रॉन के बाहर के तरीके (जैसे, नकद, इंटरनेट बैंकिंग) का उपयोग करके किया गया था। केयरपैट्रॉन के भीतर रिफंड जारी करने से ग्राहक को कोई धनराशि वापस नहीं मिलेगी।',
	RefundReasonDescription: 'अपने ग्राहकों के लेन-देन की समीक्षा करते समय रिफ़ंड का कारण जोड़ने से मदद मिल सकती है',
	Refunded: 'वापसी की गई है',
	Refunds: 'रिफंड',
	RefundsTableEmptyState: 'कोई रिफ़ंड नहीं मिला',
	Regenerate: 'पुनर्जीवित करें',
	RegisterButton: 'पंजीकरण करवाना',
	RegisterEmail: 'ईमेल',
	RegisterFirstName: 'पहला नाम',
	RegisterLastName: 'उपनाम',
	RegisterPassword: 'पासवर्ड',
	RegisteredNurse: 'पंजीकृत नर्स',
	RehabilitationCounselor: 'पुनर्वास परामर्शदाता',
	RejectAppointmentFormTitle: 'क्या आप नहीं आ सकते? कृपया हमें कारण बताएं और नया समय सुझाएँ।',
	Rejected: 'अस्वीकार कर दिया',
	Relationship: 'संबंध',
	RelationshipDetails: 'संबंध विवरण',
	RelationshipEmptyStateTitle: 'अपने ग्राहक का समर्थन करने वालों से जुड़े रहें',
	RelationshipPageAccessTypeColumnName: 'प्रोफ़ाइल एक्सेस',
	RelationshipSavedSuccessSnackbar: 'रिश्ता सफलतापूर्वक बचाया गया!',
	RelationshipSelectorFamilyAdmin: 'परिवार',
	RelationshipSelectorFamilyMember: 'परिवार का सदस्य',
	RelationshipSelectorProviderAdmin: 'प्रदाता व्यवस्थापक',
	RelationshipSelectorProviderStaff: 'प्रदाता स्टाफ',
	RelationshipSelectorSupportNetworkPrimary: 'दोस्त',
	RelationshipSelectorSupportNetworkSecondary: 'प्रसार का समर्थन',
	RelationshipStatus: 'रिश्ते की स्थिति',
	RelationshipType: 'रिश्ते का प्रकार',
	RelationshipTypeClientOwner: 'ग्राहक',
	RelationshipTypeFamilyAdmin: 'रिश्ते',
	RelationshipTypeFamilyMember: 'परिवार',
	RelationshipTypeFriendOrSupport: 'मित्र या सहायता नेटवर्क',
	RelationshipTypeProviderAdmin: 'प्रदाता व्यवस्थापक',
	RelationshipTypeProviderStaff: 'कर्मचारी',
	RelationshipTypeSelectorPlaceholder: 'संबंध प्रकार खोजें',
	Relationships: 'रिश्ते',
	Remaining: 'शेष',
	RemainingTime: '{time} शेष',
	Reminder: 'अनुस्मारक',
	ReminderColor: 'अनुस्मारक रंग',
	ReminderDetails: 'अनुस्मारक विवरण',
	ReminderEditDisclaimer: 'परिवर्तन केवल नई नियुक्तियों में ही परिलक्षित होंगे',
	ReminderSettings: 'अपॉइंटमेंट रिमाइंडर सेटिंग',
	Reminders: 'अनुस्मारक',
	Remove: 'निकालना',
	RemoveAccess: 'पहुँच हटाएँ',
	RemoveAllGuidesBtn: 'सभी गाइड हटाएँ',
	RemoveAllGuidesPopoverBody:
		'जब आप ऑनबोर्डिंग गाइड के साथ समाप्त कर लें तो बस प्रत्येक पैनल पर गाइड हटाएँ बटन का उपयोग करें।',
	RemoveAllGuidesPopoverTitle: 'क्या अब आपको ऑनबोर्डिंग गाइड की आवश्यकता नहीं है?',
	RemoveAsDefault: 'डिफ़ॉल्ट के रूप में निकालें',
	RemoveAsIntake: 'सेवन के रूप में निकालें',
	RemoveCol: 'स्तंभ हटाएं',
	RemoveColor: 'रंग हटाएँ',
	RemoveField: 'फ़ील्ड हटाएं',
	RemoveFromCall: 'कॉल से निकालें',
	RemoveFromCallDescription:
		'क्या आप सुनिश्चित हैं कि आप इस वीडियो कॉल से <mark>{attendeeName}</mark> को हटाना चाहते हैं?',
	RemoveFromCollection: 'संग्रह से निकालें',
	RemoveFromCommunity: 'समुदाय से निकालें',
	RemoveFromFolder: 'फ़ोल्डर से हटाएँ',
	RemoveFromFolderConfirmationDescription:
		'क्या आप सुनिश्चित हैं कि आप इस टेम्पलेट को इस फोल्डर से हटाना चाहते हैं? यह कार्रवाई पूर्ववत नहीं की जा सकती, लेकिन आप इसे बाद में वापस ले जाने का विकल्प चुन सकते हैं।',
	RemoveFromIntakeDefault: 'इनटेक डिफ़ॉल्ट से निकालें',
	RemoveGuides: 'गाइड हटाएँ',
	RemoveMfaConfirmationDescription:
		'मल्टी-फैक्टर ऑथेंटिकेशन (MFA) हटाने से आपके खाते की सुरक्षा कम हो जाएगी। क्या आप आगे बढ़ना चाहते हैं?',
	RemoveMfaConfirmationTitle: 'एमएफए हटाएँ?',
	RemovePaymentMethodDescription: `इससे इस भुगतान विधि तक सभी पहुंच और भविष्य में इसका उपयोग समाप्त हो जाएगा।
 इस कार्रवाई को पूर्ववत नहीं किया जा सकता.`,
	RemoveRow: 'पंक्ति हटाएं',
	RemoveTable: 'तालिका हटाएँ',
	RemoveTemplateAsDefaultIntakeSuccess: 'सफलतापूर्वक डिफ़ॉल्ट इनटेक टेम्पलेट के रूप में {templateTitle} हटा दिया गया',
	RemoveTemplateFromCommunity: 'समुदाय से टेम्पलेट हटाएँ',
	RemoveTemplateFromFolder: '{templateTitle} को {folderTitle} से सफलतापूर्वक हटा दिया गया',
	Rename: 'नाम बदलें',
	RenderingProvider: 'रेंडरिंग प्रदाता',
	Reopen: 'फिर से खोलना',
	ReorderServiceGroupFailure: 'संग्रह को पुनः क्रमित करने में विफल',
	ReorderServiceGroupSuccess: 'संग्रह को सफलतापूर्वक पुनः क्रमित किया गया',
	ReorderServicesFailure: 'सेवाओं को पुनः क्रमित करने में विफल',
	ReorderServicesSuccess: 'सेवाओं को सफलतापूर्वक पुनःक्रमित किया गया',
	ReorderYourServiceList: 'अपनी सेवा सूची पुनः क्रमित करें',
	ReorderYourServiceListDescription:
		'आप अपनी सेवाओं और संग्रहों को जिस प्रकार व्यवस्थित करते हैं, वह आपके ऑनलाइन बुकिंग पृष्ठ पर आपके सभी ग्राहकों के लिए दिखाई देगा!',
	RepeatEvery: 'हर',
	RepeatOn: 'दोहराएँ',
	Repeating: 'दोहरा',
	Repeats: 'पुनर्प्रसारण',
	RepeatsEvery: 'हर',
	Rephrase: 'इस प्रकार संशोधित',
	Replace: 'प्रतिस्थापित करें',
	ReplaceBackground: 'पृष्ठभूमि बदलें',
	ReplacementOfPriorClaim: 'पूर्व दावे का प्रतिस्थापन',
	Report: 'प्रतिवेदन',
	Reprocess: 'पुन: संसाधित',
	RepublishTemplateToCommunity: 'समुदाय को फिर से प्रकाशित करने का टेम्पलेट',
	RequestANewVerificationLink: 'नए सत्यापन लिंक का अनुरोध करें',
	RequestCoverageReport: 'कवरेज रिपोर्ट का अनुरोध करें',
	RequestingDevicePermissions: 'डिवाइस अनुमतियों के लिए अनुरोध किया जा रहा है...',
	RequirePaymentMethodDesc: 'ग्राहकों को ऑनलाइन बुकिंग के लिए अपना क्रेडिट कार्ड विवरण दर्ज करना होगा',
	RequirePaymentMethodLabel: 'क्रेडिट कार्ड विवरण की आवश्यकता है',
	Required: 'आवश्यक',
	RequiredField: 'आवश्यक',
	RequiredUrl: 'यूआरएल आवश्यक है.',
	Reschedule: 'पुनर्निर्धारित',
	RescheduleBookingLinkModalDescription:
		'आपके ग्राहक इस लिंक का उपयोग करके अपनी अपॉइंटमेंट की तारीख और समय बदल सकते हैं।',
	RescheduleBookingLinkModalTitle: 'पुनर्निर्धारित बुकिंग लिंक',
	RescheduleLink: 'पुनर्निर्धारित लिंक',
	Resend: 'पुन: भेजें',
	ResendConfirmationCode: 'पुष्टि कोड पुनः भेजें',
	ResendConfirmationCodeDescription: 'कृपया अपना ईमेल पता दर्ज करें और हम आपको एक और पुष्टिकरण कोड ईमेल करेंगे',
	ResendConfirmationCodeSuccess: 'पुष्टिकरण कोड पुनः भेजा गया है, कृपया अपना इनबॉक्स जांचें',
	ResendNewEmailVerificationSuccess: 'नया सत्यापन लिंक {email} पर भेज दिया गया है',
	ResendVerificationEmail: 'सत्यापन ईमेल पुनः भेजें',
	Reset: 'रीसेट करें',
	Resources: 'संसाधन',
	RespiratoryTherapist: 'श्वसन चिकित्सक',
	RespondToHistoricAppointmentError:
		'यह एक ऐतिहासिक नियुक्ति है, यदि आपके कोई प्रश्न हों तो कृपया अपने चिकित्सक से संपर्क करें।',
	Responder: 'प्रत्युत्तर',
	RestorableItemModalDescription:
		'क्या आप सुनिश्चित हैं कि आप {context} हटाना चाहते हैं?{canRestore, select, true { आप इसे बाद में पुनर्स्थापित कर सकते हैं।} other {}}',
	RestorableItemModalTitle: '{type} हटाएँ',
	Restore: 'पुनर्स्थापित करना',
	RestoreAll: 'सभी बहाल करो',
	Restricted: 'वर्जित',
	ResubmissionCodeReferenceNumber: 'पुनर्प्रस्तुति कोड और संदर्भ संख्या',
	Resubmit: 'पुनः सबमिट करें',
	Resume: 'फिर शुरू करना',
	Retry: 'पुन: प्रयास करें',
	RetryingConnectionAttempt: 'कनेक्शन पुनः प्रयास किया जा रहा है... (प्रयास {retryCount} का {maxRetries})',
	ReturnToForm: 'फॉर्म पर वापस लौटें',
	RevertClaimStatus: 'दावा स्थिति वापस करें',
	RevertClaimStatusDescriptionBody:
		'इस दावे से भुगतान जुड़े हुए हैं, और स्थिति बदलने से भुगतान ट्रैकिंग या प्रसंस्करण प्रभावित हो सकता है, जिसके लिए मैन्युअल सुलह की आवश्यकता हो सकती है।',
	RevertClaimStatusDescriptionTitle: 'क्या आप वाकई {status} पर वापस जाना चाहते हैं?',
	RevertClaimStatusError: 'दावा स्थिति वापस करने में विफल',
	RevertToDraft: 'ड्राफ्ट पर वापस लौटें',
	Review: 'समीक्षा',
	ReviewsFirstQuote: 'कहीं भी, किसी भी समय नियुक्तियाँ',
	ReviewsSecondJobTitle: 'लाइफहाउस क्लिनिक',
	ReviewsSecondName: 'क्लारा डब्ल्यू.',
	ReviewsSecondQuote:
		'मुझे केयरपैट्रॉन ऐप भी बहुत पसंद है। इससे मुझे चलते-फिरते अपने क्लाइंट और काम पर नज़र रखने में मदद मिलती है।',
	ReviewsThirdJobTitle: 'मनीला बे क्लिनिक',
	ReviewsThirdName: 'जैकी एच.',
	ReviewsThirdQuote: 'नेविगेशन की आसानी और सुंदर यूजर इंटरफेस हर दिन मेरे चेहरे पर मुस्कान ले आता है।',
	RightAlign: 'दाएँ संरेखित करें',
	Role: 'भूमिका',
	Roster: 'सहभागी',
	RunInBackground: 'पृष्ठभूमि में चलाएँ',
	SMS: 'एसएमएस',
	SMSAndEmailReminder: 'एसएमएस ',
	SSN: 'एसएसएन',
	SafetyRedirectHeading: 'आप Carepatron छोड़ रहे हैं',
	SafetyRedirectSubtext: 'यदि आप इस लिंक पर भरोसा करते हैं, तो जारी रखने के लिए इसे चुनें',
	SalesRepresentative: 'बिक्री प्रतिनिधि',
	SalesTax: 'बिक्री कर',
	SalesTaxHelp: 'उत्पन्न चालान पर बिक्री कर शामिल है',
	SalesTaxIncluded: 'हाँ',
	SalesTaxNotIncluded: 'नहीं',
	SaoPaulo: 'साओ पाउलो',
	Saturday: 'शनिवार',
	Save: 'बचाना',
	SaveAndClose: 'बचाना ',
	SaveAndExit: 'बचाना ',
	SaveAndLock: 'सहेजें और लॉक करें',
	SaveAsDraft: 'ड्राफ्ट के रूप में सेव करें',
	SaveCardForFuturePayments: 'भविष्य के भुगतान के लिए कार्ड सुरक्षित रखें',
	SaveChanges: 'परिवर्तनों को सुरक्षित करें',
	SaveCollection: 'संग्रह सहेजें',
	SaveField: 'फ़ील्ड सहेजें',
	SavePaymentMethod: 'भुगतान विधि सहेजें',
	SavePaymentMethodDescription: 'आपकी पहली नियुक्ति तक आपसे कोई शुल्क नहीं लिया जाएगा।',
	SavePaymentMethodSetupError: 'एक अप्रत्याशित त्रुटि हुई और हम इस समय भुगतान कॉन्फ़िगर करने में असमर्थ थे।',
	SavePaymentMethodSetupInvoiceLater: 'अपना पहला चालान भरते समय भुगतान सेट अप और सहेजा जा सकता है।',
	SaveSection: 'अनुभाग सहेजें',
	SaveService: 'नई सेवा बनाएं',
	SaveTemplate: 'टेम्पलेट सहेजें',
	Saved: 'सहेजा गया',
	SavedCards: 'सहेजे गए कार्ड',
	SavedPaymentMethods: 'सहेजा गया',
	Saving: 'सहेजा जा रहा है...',
	ScheduleAppointmentsAndOnlineServices: 'अपॉइंटमेंट और ऑनलाइन सेवाएं शेड्यूल करें',
	ScheduleName: 'शेड्यूल का नाम',
	ScheduleNew: 'नया शेड्यूल करें',
	ScheduleSend: 'शेड्यूल भेजें',
	ScheduleSendAlertInfo: 'निर्धारित वार्तालाप अपने निर्धारित समय पर भेजे जाएंगे।',
	ScheduleSendByName: '**शेड्यूल भेजें** • {time} द्वारा {displayName}',
	ScheduleSetupCall: 'शेड्यूल सेटअप कॉल',
	Scheduled: 'अनुसूचित',
	SchedulingSend: 'भेजने का समय निर्धारण',
	School: 'विद्यालय',
	ScrollToTop: 'ऊपर स्क्रॉल करें',
	Search: 'खोज',
	SearchAndConvertToLanguage: 'खोजें और भाषा में बदलें',
	SearchBasicBlocks: 'बुनियादी ब्लॉक खोजें',
	SearchByName: 'नाम से खोजें',
	SearchClaims: 'दावा खोजें',
	SearchClientFields: 'क्लाइंट फ़ील्ड खोजें',
	SearchClients: 'ग्राहक नाम, ग्राहक आईडी या फ़ोन नंबर द्वारा खोजें',
	SearchCommandNotFound: '"{searchTerm}" के लिए कोई परिणाम नहीं मिले',
	SearchContacts: 'ग्राहक या संपर्क',
	SearchContactsPlaceholder: 'संपर्क खोजें',
	SearchConversations: 'वार्तालाप खोजें',
	SearchInputPlaceholder: 'सभी संसाधन खोजें',
	SearchInvoiceNumber: 'इनवॉइस नंबर खोजें',
	SearchInvoices: 'चालान खोजें',
	SearchMultipleContacts: 'ग्राहक या संपर्क',
	SearchMultipleContactsOptional: 'ग्राहक या संपर्क (वैकल्पिक)',
	SearchOrCreateATag: 'टैग खोजें या बनाएं',
	SearchPayments: 'भुगतान खोजें',
	SearchPrepopulatedData: 'पहले से भरे गए डेटा फ़ील्ड खोजें',
	SearchRelationships: 'संबंध खोजें',
	SearchRemindersAndWorkflows: 'अनुस्मारक और वर्कफ़्लो खोजें',
	SearchServices: 'खोज सेवाएँ',
	SearchTags: 'खोज टैग',
	SearchTeamMembers: 'टीम के सदस्यों को खोजें',
	SearchTemplatePlaceholder: '{templateCount}+ संसाधनों की खोज करें',
	SearchTimezone: 'समयक्षेत्र खोजें...',
	SearchTrashItems: 'खोज आइटम',
	SearchUnsplashPlaceholder: 'Unsplash से निःशुल्क उच्च-रिज़ॉल्यूशन फ़ोटो खोजें',
	Secondary: 'माध्यमिक',
	SecondaryInsurance: 'द्वितीयक बीमा',
	SecondaryPolicy: 'द्वितीयक बीमा',
	SecondaryTimezone: 'द्वितीयक समयक्षेत्र',
	Secondly: 'दूसरा',
	Section: 'अनुभाग',
	SectionCannotBeEmpty: 'एक अनुभाग में कम से कम एक पंक्ति होनी चाहिए',
	SectionFormSecondaryText: 'अनुभाग शीर्षक और विवरण',
	SectionName: 'अनुभाग का नाम',
	Sections: 'खंड',
	SeeLess: 'कम देखें',
	SeeLessUpcomingAppointments: 'कम आगामी नियुक्तियाँ देखें',
	SeeMore: 'और देखें',
	SeeMoreUpcomingAppointments: 'आगामी नियुक्तियों को देखें',
	SeeTemplateLibrary: 'टेम्पलेट लाइब्रेरी देखें',
	Seen: 'देखा',
	SeenByName: '**देखा** • {time} द्वारा {displayName}',
	SelectAll: 'सबका चयन करें',
	SelectAssignees: 'असाइनी चुनें',
	SelectAttendees: 'उपस्थित लोगों का चयन करें',
	SelectCollection: 'संग्रह चुनें',
	SelectCorrespondingAttributes: 'संगत विशेषताएँ चुनें',
	SelectPayers: 'भुगतानकर्ता चुनें',
	SelectProfile: 'प्रोफ़ाइल चुनें',
	SelectServices: 'सेवाएँ चुनें',
	SelectTags: 'टैग चुनें',
	SelectTeamOrCommunity: 'टीम या समुदाय का चयन करें',
	SelectTemplate: 'टेम्पलेट चुनें',
	SelectType: 'प्रकार चुनें',
	Selected: 'चयनित',
	SelfPay: 'स्वयं भुगतान',
	Send: 'भेजना',
	SendAndClose: 'भेजना ',
	SendAndStopIgnore: 'भेजें और अनदेखा करना बंद करें',
	SendEmail: 'ईमेल भेजें',
	SendIntake: 'इनटेक भेजें',
	SendIntakeAndForms: 'इनटेक भेजें ',
	SendMeACopy: 'मुझे एक प्रति भेजें',
	SendNotificationEmailWarning:
		'कुछ उपस्थितियों में ईमेल पता नहीं है और उन्हें स्वचालित सूचनाएँ और अनुस्मारक प्राप्त नहीं होंगे।',
	SendNotificationLabel: 'ईमेल के साथ सूचित करने के लिए उपस्थित लोगों का चयन करें',
	SendOnlinePayment: 'ऑनलाइन भुगतान भेजें',
	SendOnlinePaymentTooltipTitleAdmin: 'कृपया अपनी पसंदीदा भुगतान सेटिंग जोड़ें',
	SendOnlinePaymentTooltipTitleStaff: 'कृपया प्रदाता के मालिक से ऑनलाइन भुगतान की व्यवस्था करने के लिए कहें।',
	SendPaymentLink: 'भुगतान लिंक भेजें',
	SendReaction: 'प्रतिक्रिया भेजें',
	SendScheduledForDate: 'Send scheduled for {date}',
	SendVerificationEmail: 'सत्यापन ईमेल भेजें',
	SendingFailed: 'भेजना विफल',
	Sent: 'भेजा',
	SentByName: '<strong>भेजा गया</strong> • {time} द्वारा {displayName}',
	Seoul: 'सोल',
	SeparateDuplicateClientsDescription:
		'चुने गए क्लाइंट रिकॉर्ड बाकी से अलग रहेंगे जब तक कि आप उन्हें मर्ज करने का विकल्प नहीं चुनते',
	Service: 'सेवा',
	'Service/s': 'सेवा/सेवाएँ',
	ServiceAdjustment: 'सेवा समायोजन',
	ServiceAllowNewClientsIndicator: 'नये ग्राहकों को अनुमति दें',
	ServiceAlreadyExistsInCollection: 'सेवा पहले से ही संग्रह में मौजूद है',
	ServiceBookableOnlineIndicator: 'ऑनलाइन बुक करने योग्य',
	ServiceCode: 'कोड',
	ServiceCodeErrorMessage: 'सेवा कोड आवश्यक है',
	ServiceCodeSelectorPlaceholder: 'सेवा कोड जोड़ें',
	ServiceColour: 'सेवा का रंग',
	ServiceCoverageDescription: 'इस बीमा पॉलिसी के लिए पात्र सेवाएं चुनें और सह-भुगतान करें।',
	ServiceCoverageGoToServices: 'सेवाओं पर जाएँ',
	ServiceCoverageNoServicesDescription:
		'डिफ़ॉल्ट पॉलिसी सह-भुगतान को ओवरराइड करने के लिए सेवा सह-भुगतान राशि को कस्टमाइज़ करें। पॉलिसी के विरुद्ध दावा की जाने वाली सेवाओं को रोकने के लिए कवरेज अक्षम करें।',
	ServiceCoverageNoServicesLabel: 'कोई सेवा नहीं मिली.',
	ServiceCoverageTitle: 'सेवा कवरेज',
	ServiceDate: 'सेवा की तिथि',
	ServiceDetails: 'सेवा विवरण',
	ServiceDuration: 'अवधि',
	ServiceEmptyState: 'अभी तक कोई सेवा उपलब्ध नहीं है',
	ServiceErrorMessage: 'सेवा आवश्यक है',
	ServiceFacility: 'सेवा सुविधा',
	ServiceName: 'सेवा का नाम',
	ServiceRate: 'दर',
	ServiceReceiptRequiresReviewNotificationSubject:
		'सुपरबिल {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} के लिए {serviceReceiptNumber, select, undefined {user} other {{clientName}}} को अतिरिक्त जानकारी की आवश्यकता है',
	ServiceSalesTax: 'बिक्री कर',
	ServiceType: 'सेवा',
	ServiceWorkerForceUIUpdateDialogDescription:
		'रिफ्रेश करने और नवीनतम केयरपेट्रॉन अपडेट प्राप्त करने के लिए पुनः लोड करें।',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'पुनः लोड करें',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'आप पुराने संस्करण का उपयोग कर रहे हैं',
	ServiceWorkerForceUIUpdateDialogTitle: 'वापसी पर स्वागत है!',
	Services: 'सेवाएं',
	ServicesAndAvailability: 'सेवाएं ',
	ServicesAndDiagnosisCodesHeader: 'सेवाएँ और निदान कोड जोड़ें',
	ServicesCount: '{count,plural,=0{सेवाएँ}one{सेवा}other{सेवाएँ}}',
	ServicesPlaceholder: 'सेवाएं',
	ServicesProvidedBy: 'इनके द्वारा प्रदान की गई सेवा/सेवाएँ',
	SetAPhysicalAddress: 'भौतिक पता सेट करें',
	SetAVirtualLocation: 'वर्चुअल स्थान सेट करें',
	SetAsDefault: 'डिफाल्ट के रूप में सेट',
	SetAsIntake: 'सेवन के रूप में सेट करें',
	SetAsIntakeDefault: 'इनटेक डिफ़ॉल्ट के रूप में सेट करें',
	SetAvailability: 'उपलब्धता सेट करें',
	SetTemplateAsDefaultIntakeSuccess: 'सफलतापूर्वक {templateTitle} डिफ़ॉल्ट इनटेक टेम्पलेट के रूप में सेट किया गया',
	SetUpMfaButton: 'एमएफए स्थापित करें',
	SetYourLocation: 'अपन सेट करें<mark> जगह</mark>',
	SetYourLocationDescription:
		'मेरे पास कोई व्यावसायिक पता नहीं है <span>(केवल ऑनलाइन और मोबाइल सेवाओं के लिए)</span>',
	SettingUpPayers: 'पेयर सेट करना',
	Settings: 'सेटिंग्स',
	SettingsNewUserPasswordDescription:
		'एक बार जब आप साइन अप कर लेंगे, तो हम आपको एक पुष्टिकरण कोड भेजेंगे जिसका उपयोग आप अपने खाते की पुष्टि के लिए कर सकते हैं',
	SettingsNewUserPasswordTitle: 'केयरपेट्रॉन पर साइन अप करें',
	SettingsTabAutomation: 'स्वचालन',
	SettingsTabBillingDetails: 'बिलिंग विवरण',
	SettingsTabConnectedApps: 'कनेक्टेड ऐप्स',
	SettingsTabCustomFields: 'तटकर क्षेत्र',
	SettingsTabDetails: 'विवरण',
	SettingsTabInvoices: 'चालान',
	SettingsTabLocations: 'स्थानों',
	SettingsTabNotifications: 'सूचनाएं',
	SettingsTabOnlineBooking: 'ऑनलाइन बुकिंग',
	SettingsTabPayers: 'दाताओं',
	SettingsTabReminders: 'अनुस्मारक',
	SettingsTabServices: 'सेवाएं',
	SettingsTabServicesAndAvailability: 'सेवाएँ और उपलब्धता',
	SettingsTabSubscriptions: 'सदस्यता',
	SettingsTabWorkflowAutomations: 'स्वचालन',
	SettingsTabWorkflowReminders: 'बुनियादी अनुस्मारक',
	SettingsTabWorkflowTemplates: 'टेम्पलेट्स',
	Setup: 'स्थापित करना',
	SetupGuide: 'सेट अप गाइड',
	SetupGuideAddServicesActionLabel: 'शुरू',
	SetupGuideAddServicesSubtitle: '4 कदम • 2 मिनट',
	SetupGuideAddServicesTitle: 'अपनी सेवाएँ जोड़ें',
	SetupGuideEnableOnlinePaymentsActionLabel: 'शुरू',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 चरण • 3 मिनट',
	SetupGuideEnableOnlinePaymentsTitle: 'ऑनलाइन भुगतान सक्षम करें',
	SetupGuideImportClientsActionLabel: 'शुरू',
	SetupGuideImportClientsSubtitle: '4 चरण • 3 मिनट',
	SetupGuideImportClientsTitle: 'अपने क्लाइंट आयात करें',
	SetupGuideImportTemplatesActionLabel: 'शुरू',
	SetupGuideImportTemplatesSubtitle: '2 चरण • 1 मिनट',
	SetupGuideImportTemplatesTitle: 'अपने टेम्प्लेट इम्पोर्ट करें',
	SetupGuidePersonalizeWorkspaceActionLabel: 'शुरू',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 चरण • 2 मिनट',
	SetupGuidePersonalizeWorkspaceTitle: 'अपना कार्यस्थल निजीकृत करें',
	SetupGuideSetLocationActionLabel: 'शुरू',
	SetupGuideSetLocationSubtitle: '4 चरण • 1 मिनट',
	SetupGuideSetLocationTitle: 'अपना स्थान निर्धारित करें',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'टीम को आमंत्रित करें',
	SetupGuideSuggestedAddTeamMembersSubtitle:
		'अपनी टीम को आसानी से संवाद करने और कार्यों को प्रबंधित करने के लिए आमंत्रित करें।',
	SetupGuideSuggestedAddTeamMembersTag: 'स्थापना',
	SetupGuideSuggestedAddTeamMembersTitle: 'टीम के सदस्य जोड़ें',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'अनुकूलित करें',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'अपने अनोखे लोगो और ब्रांड रंगों के साथ पेशेवर महसूस करें।',
	SetupGuideSuggestedCustomizeBrandTitle: 'ब्रांड को कस्टमाइज़ करें',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'डाउनलोड',
	SetupGuideSuggestedDownloadMobileAppSubtitle: 'अपने कार्यक्षेत्र को कहीं भी, कभी भी किसी भी डिवाइस पर एक्सेस करें।',
	SetupGuideSuggestedDownloadMobileAppTag: 'स्थापना',
	SetupGuideSuggestedDownloadMobileAppTitle: 'ऐप डाउनलोड करें',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'उपलब्धता सेट करें',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'अपनी उपलब्धता निर्धारित करके दोहरी बुकिंग से बचें।',
	SetupGuideSuggestedEditAvailabilityTag: 'समय सारिणी',
	SetupGuideSuggestedEditAvailabilityTitle: 'उपलब्धता संपादित करें',
	SetupGuideSuggestedImportClientsActionLabel: 'आयात',
	SetupGuideSuggestedImportClientsSubtitle: 'बस एक क्लिक में अपने मौजूदा क्लाइंट डेटा को तुरंत अपलोड करें।',
	SetupGuideSuggestedImportClientsTag: 'स्थापित करें',
	SetupGuideSuggestedImportClientsTitle: 'कलाइंट इम्पोर्ट करें',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'रिमाइंडर संपादित करें',
	SetupGuideSuggestedPersonalizeRemindersSubtitle: 'स्वचालित अपॉइंटमेंट रिमाइंडर के साथ नो-शो कम करें।',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'व्यक्तिगत अनुस्मारक',
	SetupGuideSuggestedStartVideoCallActionLabel: 'कॉल शुरू करें',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'हमारे AI-Powered वीडियो टूल्स का उपयोग करके कॉल होस्ट करें और ग्राहकों से जुड़ें।',
	SetupGuideSuggestedStartVideoCallTag: 'टेलीहेल्थ',
	SetupGuideSuggestedStartVideoCallTitle: 'वीडियो कॉल शुरू करें',
	SetupGuideSuggestedTryActionsTitle: 'कोशिश करने योग्य चीजें 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'AI सहायता का प्रयास करें',
	SetupGuideSuggestedUseAIAssistantSubtitle: 'अपने सभी काम के सवालों के तुरंत जवाब पाएँ।',
	SetupGuideSuggestedUseAIAssistantTag: 'नया',
	SetupGuideSuggestedUseAIAssistantTitle: 'एआई असिस्टेंट का इस्तेमाल करें',
	SetupGuideSyncCalendarActionLabel: 'शुरू',
	SetupGuideSyncCalendarSubtitle: '1 कदम • 1 मिनट से कम',
	SetupGuideSyncCalendarTitle: 'अपना कैलेंडर सिंक करें',
	SetupGuideVerifyEmailLabel: 'सत्यापित करें',
	SetupGuideVerifyEmailSubtitle: '2 कदम • 2 मिनट',
	SetupOnlineStripePayments: 'ऑनलाइन भुगतान के लिए स्ट्राइप का उपयोग करें',
	SetupPayments: 'भुगतान सेट करें',
	Sex: 'सेक्स',
	SexSelectorPlaceholder: 'पुरुष / महिला / कहना पसंद नहीं',
	Share: 'शेयर करना',
	ShareBookingLink: 'बुकिंग लिंक साझा करें',
	ShareNoteDefaultMessage: `नमस्ते{name} ने आपके साथ "{documentName}" साझा किया है।

धन्यवाद,
{practiceName}`,
	ShareNoteMessage: `नमस्ते
{name} ने "{documentName}" {isResponder, select, true {आपके लिए भरने के लिए कुछ प्रश्न के साथ} other {आपके साथ}} साझा किया है।

धन्यवाद,
{practiceName}`,
	ShareNoteTitle: '‘{noteTitle}’ साझा करें',
	ShareNotesWithClients: 'ग्राहकों या संपर्कों के साथ साझा करें',
	ShareScreen: 'स्क्रीन साझा करना',
	ShareScreenNotSupported: 'आपका डिवाइस/ब्राउज़र स्क्रीन शेयर सुविधा का समर्थन नहीं करता है',
	ShareScreenWithId: 'स्क्रीन {screenId}',
	ShareTemplateAsPublicFormModalDescription:
		'दूसरों को इस टेम्पलेट को देखने और इसे एक फॉर्म के रूप में सबमिट करने की अनुमति दें।',
	ShareTemplateAsPublicFormModalTitle: '‘{title}’ के लिए लिंक साझा करें',
	ShareTemplateAsPublicFormSaved: 'सार्वजनिक फॉर्म कॉन्फ़िगरेशन सफलतापूर्वक अपडेट हो गया',
	ShareTemplateAsPublicFormSectionCustomization: 'अनुकूलन',
	ShareTemplateAsPublicFormShowPoweredBy: 'मेरे फॉर्म पर "Powered by Carepatron" दिखाएं',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'मेरे फॉर्म पर "Powered by Carepatron" दिखाएँ/छिपाएँ',
	ShareTemplateAsPublicFormTrigger: 'साझा करें',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'कार्यक्षेत्र ब्रांडिंग का उपयोग करें',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'कार्यक्षेत्र ब्रांडिंग दिखाएँ/छिपाएँ',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription: 'मौजूदा और गैर-मौजूदा ग्राहकों के लिए कोड भेजता है',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning: 'हस्ताक्षर के लिए ईमेल सत्यापित होना आवश्यक है',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'केवल मौजूदा ग्राहकों के लिए कोड भेजता है',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'कभी कोड नहीं भेजता',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `'कभी नहीं' चुनने से असत्यापित उपयोगकर्ता क्लाइंट डेटा को ओवरराइट कर सकते हैं यदि वे किसी मौजूदा क्लाइंट के ईमेल पते का उपयोग करते हैं।`,
	ShareWithCommunity: 'समुदाय के साथ साझा करें',
	ShareYourReferralLink: 'अपना रेफरल लिंक साझा करें',
	ShareYourScreen: 'अपनी स्क्रीन साझा करें',
	SheHer: 'वह/उसकी',
	ShortTextAnswer: 'संक्षिप्त पाठ उत्तर',
	ShortTextFormPrimaryText: 'संक्षिप्त पाठ',
	ShortTextFormSecondaryText: '300 से कम अक्षरों का उत्तर',
	Show: 'दिखाओ',
	ShowColumn: 'कॉलम दिखाएं',
	ShowColumnButton: 'कॉलम {value} बटन दिखाएँ',
	ShowColumns: 'कॉलम दिखाएं',
	ShowColumnsMenu: 'कॉलम मेनू दिखाएं',
	ShowDateDurationDescription: 'जैसे 29 वर्ष की उम्र',
	ShowDateDurationLabel: 'दिनांक अवधि दिखाएँ',
	ShowDetails: 'प्रदर्शन का विवरण',
	ShowField: 'फ़ील्ड दिखाएँ',
	ShowFullAddress: 'पता दिखाएं',
	ShowHideFields: 'फ़ील्ड दिखाएँ/छिपाएँ',
	ShowIcons: 'चिह्न दिखाएं',
	ShowLess: 'कम दिखाएं',
	ShowMeetingTimers: 'मीटिंग टाइमर दिखाएं',
	ShowMenu: 'मेनू दिखाओ',
	ShowMergeSummarySidebar: 'मर्ज सारांश दिखाएँ',
	ShowMore: 'और दिखाओ',
	ShowOnTranscript: 'ट्रांसक्रिप्ट पर दिखाएँ',
	ShowReactions: 'प्रतिक्रियाएँ दिखाएँ',
	ShowSection: 'अनुभाग दिखाएँ',
	ShowServiceCode: 'सेवा कोड दिखाएं',
	ShowServiceDescription: 'सेवा बुकिंग पर विवरण दिखाएं',
	ShowServiceDescriptionDesc: 'बुकिंग करते समय ग्राहक सेवाओं का विवरण देख सकते हैं',
	ShowServiceGroups: 'संग्रह दिखाएं',
	ShowServiceGroupsDesc: 'बुकिंग के समय ग्राहकों को संग्रह के आधार पर समूहीकृत सेवाएँ दिखाई जाएंगी',
	ShowSpeakers: 'वक्ताओं को दिखाएं',
	ShowTax: 'कर दिखाएं',
	ShowTimestamp: 'टाइमस्टैम्प दिखाएं',
	ShowUnits: 'इकाइयाँ दिखाएँ',
	ShowWeekends: 'सप्ताहांत दिखाएँ',
	ShowYourView: 'अपना दृष्टिकोण दिखाएं',
	SignInWithApple: 'Apple के साथ साइन इन करें',
	SignInWithGoogle: 'Google के साथ साइन इन करें',
	SignInWithMicrosoft: 'Microsoft के साथ साइन इन करें',
	SignUpTitleReferralDefault: '<mark>साइन अप करें</mark> और अपने रेफरल रिवॉर्ड का दावा करें',
	SignUpTitleReferralUpgrade:
		'अपना {durationInMonths} महीने <mark>{percentOff, select, 100 {मुफ़्त} other {{percentOff}% छूट}} अपग्रेड</mark> शुरू करें',
	SignatureCaptureError: 'हस्ताक्षर कैप्चर करने में असमर्थ। कृपया पुनः प्रयास करें।',
	SignatureFormPrimaryText: 'हस्ताक्षर',
	SignatureFormSecondaryText: 'डिजिटल हस्ताक्षर प्राप्त करें',
	SignatureInfoTooltip: 'यह दृश्य प्रतिनिधित्व वैध इलेक्ट्रॉनिक हस्ताक्षर नहीं है।',
	SignaturePlaceholder: 'अपना हस्ताक्षर यहां बनाएं',
	SignedBy: 'द्वारा हस्ताक्षर किए',
	Signup: 'साइन अप करें',
	SignupAgreements: 'मैं अपने खाते के लिए {termsOfUse} और {privacyStatement} से सहमत हूँ।',
	SignupBAA: 'व्यवसाय सहयोगी समझौता',
	SignupBusinessAgreements:
		'मेरी और व्यवसाय की ओर से, मैं अपने खाते के लिए {businessAssociateAgreement}, {termsOfUse} और {privacyStatement} से सहमत हूँ।',
	SignupInvitationForYou: 'आपको केयरपेट्रॉन का उपयोग करने के लिए आमंत्रित किया गया है।',
	SignupPageProviderWarning:
		'यदि आपके व्यवस्थापक ने पहले ही खाता बना लिया है, तो आपको उनसे उस प्रदाता में आमंत्रित करने के लिए कहना होगा। इस साइन अप फ़ॉर्म का उपयोग न करें। अधिक जानकारी के लिए देखें',
	SignupPageProviderWarningLink: 'इस लिंक।',
	SignupPrivacy: 'गोपनीयता नीति',
	SignupProfession: 'कौन सा पेशा आपको सबसे बेहतर ढंग से वर्णित करता है?',
	SignupSubtitle:
		'केयरपैट्रॉन का प्रैक्टिस मैनेजमेंट सॉफ्टवेयर अकेले प्रैक्टिस करने वालों और टीमों के लिए बनाया गया है। अत्यधिक फीस देना बंद करें और क्रांति का हिस्सा बनें।',
	SignupSuccessDescription:
		'अपना ऑनबोर्डिंग शुरू करने के लिए अपना ईमेल पता पुष्टि करें। अगर आपको यह तुरंत प्राप्त नहीं होता है, तो कृपया अपना स्पैम फ़ोल्डर जांचें।',
	SignupSuccessTitle: 'कृपया अपनी ईमेल देखें',
	SignupTermsOfUse: 'उपयोग की शर्तें',
	SignupTitleClient: '<mark>अपने स्वास्थ्य का प्रबंधन करें</mark> एक जगह से',
	SignupTitleLast: 'और आपके द्वारा किया गया सारा काम! — यह मुफ़्त है',
	SignupTitleOne: '<mark>आपको शक्ति प्रदान करना</mark> , ',
	SignupTitleThree: '<mark>अपने ग्राहकों को सशक्त बनाना</mark> , ',
	SignupTitleTwo: '<mark>अपनी टीम को सशक्त बनाना</mark> , ',
	Simple: 'सरल',
	SimplifyBillToDetails: 'बिल को सरल बनाकर विस्तृत जानकारी प्राप्त करें',
	SimplifyBillToHelperText: 'केवल पहली पंक्ति का उपयोग तब किया जाता है जब वह क्लाइंट से मेल खाती है',
	Singapore: 'सिंगापुर',
	Single: 'अकेला',
	SingleChoiceFormPrimaryText: 'एकल विकल्प',
	SingleChoiceFormSecondaryText: 'केवल एक विकल्प चुनें',
	Sister: 'बहन',
	SisterInLaw: 'भाभी',
	Skip: 'छोडना',
	SkipLogin: 'लॉगिन छोड़ दें',
	SlightBlur: 'अपनी पृष्ठभूमि को थोड़ा धुंधला करें',
	Small: 'छोटा',
	SmartChips: 'स्मार्टचिप्स',
	SmartDataChips: 'स्मार्ट डेटा चिप्स',
	SmartReply: 'स्मार्ट जवाब',
	SmartSuggestNewClient: '**स्मार्ट सुझाव** {name} को एक नए क्लाइंट के रूप में बनाएँ',
	SmartSuggestedFieldDescription: 'यह फ़ील्ड एक स्मार्ट सुझाव है',
	SocialSecurityNumber: 'सामाजिक सुरक्षा संख्या',
	SocialWork: 'सामाजिक कार्य',
	SocialWorker: 'सामाजिक कार्यकर्ता',
	SoftwareDeveloper: 'सॉफ्टवेयर डेवलपर',
	Solo: 'एकल',
	Someone: 'कोई',
	Son: 'बेटा',
	SortBy: 'इसके अनुसार क्रमबद्ध करें',
	SouthAmerica: 'दक्षिण अमेरिका',
	Speaker: 'वक्ता',
	SpeakerSource: 'स्पीकर स्रोत',
	Speakers: 'वक्ताओं',
	SpecifyPaymentMethod: 'भुगतान विधि निर्दिष्ट करें',
	SpeechLanguagePathology: 'भाषण भाषा पैथोलॉजी',
	SpeechTherapist: 'भाषण चिकित्सक',
	SpeechTherapists: 'भाषण चिकित्सक',
	SpeechTherapy: 'वाक उपचार',
	SportsMedicinePhysician: 'खेल चिकित्सा चिकित्सक',
	Spouse: 'जीवनसाथी',
	SpreadsheetColumnExample: 'उदाहरण के लिए ',
	SpreadsheetColumns: 'स्प्रेडशीट कॉलम',
	SpreadsheetUploaded: 'स्प्रेडशीट अपलोड की गई',
	SpreadsheetUploading: 'अपलोड हो रहा है...',
	Staff: 'कर्मचारी',
	StaffAccessDescriptionAdmin: 'व्यवस्थापक प्लेटफ़ॉर्म पर सब कुछ प्रबंधित कर सकते हैं।',
	StaffAccessDescriptionStaff: `स्टाफ सदस्य अपने द्वारा बनाए गए या साझा किए गए ग्राहकों, नोट्स और दस्तावेज़ों का प्रबंधन कर सकते हैं
 उनके साथ काम करें, अपॉइंटमेंट शेड्यूल करें, इनवॉइस प्रबंधित करें।`,
	StaffContactAssignedSubject:
		'{actorProfileName} ने {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} और {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { और 1 अन्य ग्राहक} other { और # अन्य ग्राहकों को}} आपको असाइन किया है',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} ने {inboxName} इनबॉक्स आपके साथ शेयर किया है',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} ने {inboxName} इनबॉक्स तक आपकी पहुँच हटा दी है',
	StaffMembers: 'स्टाफ के सदस्यों को',
	StaffMembersNumber: '{billedUsers, plural, one {# टीम सदस्य} other {# टीम सदस्य}}',
	StaffSavedSuccessSnackbar: 'टीम सदस्यों की जानकारी सफलतापूर्वक सहेज ली गई!',
	StaffSelectorAdminRole: 'प्रशासक',
	StaffSelectorStaffRole: 'स्टाफ के सदस्य',
	StandardAppointment: 'मानक नियुक्ति',
	StandardColor: 'कार्य का रंग',
	StartAndEndTime: 'शुरू और समाप्त समय',
	StartCall: 'कॉल शुरू करें',
	StartDate: 'आरंभ करने की तिथि',
	StartDictating: 'डिक्टेट करना शुरू करें',
	StartImport: 'आयात प्रारंभ करें',
	StartRecordErrorTitle: 'आपकी रिकॉर्डिंग शुरू करते समय कोई त्रुटि हुई',
	StartRecording: 'रिकॉर्डिंग शुरू करें',
	StartTimeIncrements: 'प्रारंभ समय वृद्धि',
	StartTimeIncrementsView: '{startTimeIncrements} मिनट अंतराल',
	StartTranscribing: 'ट्रांस्क्राइब करना शुरू करें',
	StartTranscribingNotes:
		'कृपया उन क्लाइंट्स को चुनें जिनके लिए आप नोट बनाना चाहते हैं। फिर रिकॉर्डिंग शुरू करने के लिए "स्टार्ट ट्रांसक्राइबिंग" बटन पर क्लिक करें',
	StartTranscription: 'ट्रांसक्रिप्शन शुरू करें',
	StartVideoCall: 'वीडियो कॉल प्रारंभ करें',
	StartWeekOn: 'सप्ताह की शुरुआत',
	StartedBy: 'द्वारा शुरू किया गया ',
	Starter: 'स्टार्टर',
	State: 'राज्य',
	StateIndustrialAccidentProviderNumber: 'राज्य औद्योगिक दुर्घटना प्रदाता संख्या',
	StateLicenseNumber: 'राज्य लाइसेंस संख्या',
	Statement: 'कथन',
	StatementDescriptor: 'कथन विवरणक',
	StatementDescriptorToolTip:
		'स्टेटमेंट डिस्क्रिप्टर आपके क्लाइंट के बैंक या क्रेडिट कार्ड स्टेटमेंट पर दिखाया जाता है। यह 5 से 22 अक्षरों के बीच होना चाहिए और आपके व्यवसाय का नाम दर्शाना चाहिए।',
	StatementNumber: 'कथन {हैशटैग}',
	Status: 'स्थिति',
	StatusFieldPlaceholder: 'स्थिति लेबल दर्ज करें',
	StepFather: 'स्टेप-पिता',
	StepMother: 'चरण-माँ',
	Stockholm: 'स्टॉकहोम',
	StopIgnoreSendersDescription: `इन प्रेषकों को अनदेखा करना बंद करने से भविष्य की बातचीत 'इनबॉक्स' में भेजी जाएगी। क्या आप वाकई इन प्रेषकों को अनदेखा करना बंद करना चाहते हैं?`,
	StopIgnoring: 'अनदेखा करना बंद करें',
	StopIgnoringSenders: 'प्रेषकों को अनदेखा करना बंद करें',
	StopIgnoringSendersSuccess: 'ईमेल पता <mark>{addresses}</mark> को नज़रअंदाज़ करना बंद कर दिया',
	StopSharing: 'साझा करना बंद',
	StopSharingLabel: 'carepatron.com आपकी स्क्रीन साझा कर रहा है.',
	Storage: 'भंडारण',
	StorageAlmostFullDescription: '🚀 अपने खाते को सुचारू रूप से चलाने के लिए अभी अपग्रेड करें।',
	StorageAlmostFullTitle: 'आपने अपनी कार्यक्षेत्र संग्रहण सीमा का {percentage}% उपयोग कर लिया है!',
	StorageFullDescription: 'अपनी योजना को अपग्रेड करके अधिक संग्रहण प्राप्त करें.',
	StorageFullTitle: '	आपका संग्रहण भर गया है.',
	Street: 'गली',
	StripeAccountNotCompleteErrorCode:
		'ऑनलाइन भुगतान {hasProviderName, select, true { {providerName} के लिए सेट नहीं हैं} other {इस प्रदाता के लिए सक्षम नहीं हैं}}.',
	StripeAccountRejectedError: 'स्ट्राइप अकाउंट को अस्वीकार कर दिया गया है। कृपया सहायता से संपर्क करें।',
	StripeBalance: 'स्ट्राइप बैलेंस',
	StripeChargesInfoToolTip: 'आपको डेबिट चार्ज करने की अनुमति देता है ',
	StripeFeesDescription:
		'केयरपेट्रॉन आपको जल्दी से भुगतान करने और आपकी भुगतान जानकारी को सुरक्षित रखने के लिए स्ट्राइप का उपयोग करता है। उपलब्ध भुगतान विधियाँ क्षेत्र के अनुसार अलग-अलग होती हैं, सभी प्रमुख डेबिट ',
	StripeFeesDescriptionItem1: 'प्रत्येक सफल लेनदेन पर प्रोसेसिंग शुल्क लागू होता है, आप {link} कर सकते हैं।',
	StripeFeesDescriptionItem2: 'भुगतान प्रतिदिन होता है लेकिन 4 दिनों तक रोक कर रखा जाता है।',
	StripeFeesLinkToRatesText: 'हमारी दरें यहाँ देखें',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'माफ़ करना, ऑनलाइन भुगतान का उपयोग करने वाले चालानों के लिए कम से कम {minimumAmount} की आवश्यकता होती है',
	StripePaymentsDisabled: 'ऑनलाइन भुगतान अक्षम हैं। कृपया अपनी भुगतान सेटिंग जांचें।',
	StripePaymentsUnavailable: 'भुगतान उपलब्ध नहीं',
	StripePaymentsUnavailableDescription: 'भुगतान लोड करते समय कोई त्रुटि हुई। कृपया बाद में पुनः प्रयास करें।',
	StripePayoutsInfoToolTip: 'आपको अपने बैंक खाते में भुगतान प्राप्त करने की सुविधा देता है',
	StyleYourWorkspace: '<mark>शैली</mark> अपना कार्यक्षेत्र',
	StyleYourWorkspaceDescription1:
		'हमने आपकी वेबसाइट से ब्रांड एसेट्स लाए हैं। बेझिझक उन्हें संपादित करें या अपने Carepatron कार्यक्षेत्र में जारी रखें',
	StyleYourWorkspaceDescription2:
		'अपने ब्रांड एसेट का उपयोग करके इनवॉइस और ऑनलाइन बुकिंग को कस्टमाइज़ करें ताकि ग्राहक अनुभव निर्बाध हो',
	SubAdvanced: 'उन्नत',
	SubEssential: 'आवश्यक',
	SubOrganization: 'संगठन',
	SubPlus: 'प्लस',
	SubProfessional: 'पेशेवर',
	Subject: 'विषय',
	Submit: 'जमा करना',
	SubmitElectronically: 'इलेक्ट्रॉनिक रूप से जमा करें',
	SubmitFeedback: 'प्रतिक्रिया सबमिट करें',
	SubmitFormValidationError:
		'कृपया सुनिश्चित करें कि सभी आवश्यक फ़ील्ड सही ढंग से भरी गई हैं और पुनः सबमिट करने का प्रयास करें।',
	Submitted: 'प्रस्तुत',
	SubmittedDate: 'जमा की गई तिथि',
	SubscribePerMonth: 'सदस्यता लें {price} {isMonthly, select, true {प्रति माह} other {प्रति वर्ष}}',
	SubscriptionDiscountDescription:
		'{percentOff}% छूट {months, select, null { } other { {months, plural, one {# महीने के लिए} other {# महीनों के लिए}}}}',
	SubscriptionFreeTrialDescription: '{endDate} तक मुफ़्त',
	SubscriptionPaymentFailedNotificationSubject:
		'हम आपकी सदस्यता के लिए भुगतान पूरा करने में असमर्थ रहे। कृपया अपना भुगतान विवरण जांचें',
	SubscriptionPlanDetailsHeader: 'प्रति उपयोगकर्ता/मासिक वार्षिक बिल',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} मासिक बिल किया गया (USD)',
	SubscriptionPlans: 'सदस्यता योजनाएँ',
	SubscriptionPlansDescription:
		'अपने प्लान को अपग्रेड करें ताकि अतिरिक्त लाभों को अनलॉक किया जा सके और अपने अभ्यास को सुचारू रूप से चलाया जा सके।',
	SubscriptionPlansDescriptionNoPermission:
		'ऐसा लगता है कि आपके पास अभी अपग्रेड करने की अनुमति नहीं है - कृपया मदद के लिए अपने व्यवस्थापक से संपर्क करें।',
	SubscriptionSettings: 'सदस्यता सेटिंग',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> भंडारण का उपयोग किया गया',
	SubscriptionSettingsStorageUsed: '{प्रयुक्त} में से {सीमा} प्रयुक्त',
	SubscriptionSettingsUnlimitedStorage: 'असीमित भंडारण उपलब्ध',
	SubscriptionSummary: 'सदस्यता सारांश',
	SubscriptionUnavailableOverStorageLimit: 'आपका वर्तमान उपयोग इस योजना की संग्रहण सीमा से अधिक है।',
	SubscriptionUnpaidBannerButton: 'सदस्यता पर जाएँ',
	SubscriptionUnpaidBannerDescription: 'कृपया जाँच लें कि आपके भुगतान विवरण सही हैं और पुनः प्रयास करें',
	SubscriptionUnpaidBannerTitle: 'हम आपकी सदस्यता के लिए भुगतान पूरा करने में असमर्थ रहे.',
	Subscriptions: 'सदस्यता',
	SubscriptionsAndPayments: 'सदस्यता ',
	Subtotal: 'उप-योग',
	SuburbOrProvince: 'उपनगर/प्रांत',
	SuburbOrState: 'उपनगर/राज्य',
	SuccessSavedNoteChanges: 'नोट परिवर्तन सफलतापूर्वक सहेजे गए',
	SuccessShareDocument: 'दस्तावेज़ सफलतापूर्वक साझा किया गया',
	SuccessShareNote: 'नोट सफलतापूर्वक साझा किया गया',
	SuccessfullyCreatedValue: 'सफलतापूर्वक {value} बनाया गया',
	SuccessfullyDeletedTranscriptionPart: 'ट्रांस्क्रिप्शन भाग सफलतापूर्वक हटा दिया गया',
	SuccessfullyDeletedValue: 'सफलतापूर्वक {value} हटाला',
	SuccessfullySubmitted: 'सफलतापूर्वक प्रस्तुत किया गया ',
	SuccessfullyUpdatedClientSettings: 'क्लाइंट सेटिंग सफलतापूर्वक अपडेट की गई',
	SuccessfullyUpdatedTranscriptionPart: 'ट्रांसक्रिप्शन भाग को सफलतापूर्वक अपडेट किया गया',
	SuccessfullyUpdatedValue: 'सफलतापूर्वक {value} अपडेट किया गया',
	SuggestedAIPoweredTemplates: 'सुझाए गए AI-संचालित टेम्पलेट्स',
	SuggestedAITemplates: 'सुझाए गए AI टेम्पलेट',
	SuggestedActions: 'सुझावित गतिविधियां',
	SuggestedLocations: 'सुझाए गए स्थान',
	Suggestions: 'सुझाव',
	Summarise: 'एआई सारांश',
	SummarisingContent: '{title} का सारांश',
	Sunday: 'रविवार',
	Superbill: 'सुपरबिल',
	SuperbillAndInsuranceBilling: 'सुपरबिल ',
	SuperbillAutomationMonthly: 'सक्रिय • महीने का अंतिम दिन',
	SuperbillAutomationNoEmail:
		'स्वचालित बिलिंग दस्तावेज़ों को सफलतापूर्वक भेजने के लिए, इस क्लाइंट के लिए एक ईमेल पता जोड़ें',
	SuperbillAutomationNotActive: 'सक्रिय नहीं',
	SuperbillAutomationUpdateFailure: 'सुपरबिल स्वचालन सेटिंग अपडेट करने में विफल',
	SuperbillAutomationUpdateSuccess: 'सुपरबिल ऑटोमेशन सेटिंग सफलतापूर्वक अपडेट की गई',
	SuperbillClientHelperText: 'यह जानकारी ग्राहक विवरण से पहले से भरी हुई है',
	SuperbillNotFoundDescription:
		'कृपया अपने प्रदाता से संपर्क करें और उनसे अधिक जानकारी मांगें या सुपरबिल पुनः भेजने के लिए कहें।',
	SuperbillNotFoundTitle: 'सुपरबिल नहीं मिला',
	SuperbillNumber: 'सुपरबिल #{number}',
	SuperbillNumberAlreadyExists: 'सुपरबिल रसीद संख्या पहले से मौजूद है',
	SuperbillPracticeHelperText: 'यह जानकारी प्रैक्टिस बिलिंग सेटिंग से पहले से भरी हुई है',
	SuperbillProviderHelperText: 'यह जानकारी स्टाफ विवरण से पहले से भरी हुई है',
	SuperbillReceipts: 'सुपरबिल रसीदें',
	SuperbillsEmptyStateDescription: 'कोई सुपरबिल नहीं मिला है।',
	Surgeon: 'शल्य चिकित्सक',
	Surgeons: 'सर्जनों',
	SurgicalTechnologist: 'सर्जिकल टेक्नोलॉजिस्ट',
	SwitchFromAnotherPlatform: 'मैं दूसरे प्लेटफॉर्म से स्विच कर रहा हूँ',
	SwitchToMyPortal: 'मेरे पोर्टल पर जाएँ',
	SwitchToMyPortalTooltip: `अपने निजी पोर्टल तक पहुंचें,
 आपको अपने अन्वेषण में सक्षम बनाना
 ग्राहक के पोर्टल अनुभव.`,
	SwitchWorkspace: 'कार्यक्षेत्र स्विच करें',
	SwitchingToADifferentPlatform: 'किसी दूसरे प्लेटफ़ॉर्म पर स्विच करना',
	Sydney: 'सिडनी',
	SyncCalendar: 'कैलेंडर सिंक करें',
	SyncCalendarModalDescription:
		'अन्य टीम सदस्य आपके सिंक किए गए कैलेंडर नहीं देख पाएंगे। क्लाइंट अपॉइंटमेंट को केवल केयरपेट्रॉन के भीतर से ही अपडेट या डिलीट किया जा सकता है।',
	SyncCalendarModalDisplayCalendar: 'Carepatron में मेरा कैलेंडर प्रदर्शित करें',
	SyncCalendarModalSyncToCarepatron: 'मेरे कैलेंडर को Carepatron से सिंक करें',
	SyncCalendarModalSyncWithCalendar: 'Carepatron अपॉइंटमेंट को मेरे कैलेंडर के साथ सिंक करें',
	SyncCarepatronAppointmentsWithMyCalendar: 'Carepatron अपॉइंटमेंट को मेरे कैलेंडर के साथ सिंक करें',
	SyncGoogleCalendar: 'Google कैलेंडर सिंक करें',
	SyncInbox: 'Carepatron के साथ इनबॉक्स सिंक करें',
	SyncMyCalendarToCarepatron: 'अपना कैलेंडर Carepatron से सिंक करें',
	SyncOutlookCalendar: 'Outlook कैलेंडर सिंक करें',
	SyncedFromExternalCalendar: 'बाहरी कैलेंडर से सिंक किया गया',
	SyncingCalendarName: '{calendarName} कैलेंडर सिंक कर रहा है',
	SyncingFailed: 'समन्वयन विफल',
	SystemGenerated: 'सिस्टम द्वारा उत्पन्न',
	TFN: 'टीएफएन',
	TRICARE: 'TRICARE',
	TRN: 'टीआरएन',
	Table: 'मेज़',
	TableRowLabel: '{value} के लिए तालिका पंक्ति',
	TagSelectorNoOptionsText: 'नया टैग जोड़ने के लिए "नया बनाएं" पर क्लिक करें',
	Tags: 'टैग',
	TagsInputPlaceholder: 'टैग खोजें या बनाएं',
	Task: 'काम',
	TaskAttendeeStatusUpdatedSuccess: 'सफलतापूर्वक अपॉइंटमेंट की स्थिति अपडेट की गई',
	Tasks: 'कार्य',
	Tax: 'कर',
	TaxAmount: 'कर राशि',
	TaxID: 'टैक्स आईडी',
	TaxIdType: 'कर आईडी प्रकार',
	TaxName: 'कर का नाम',
	TaxNumber: 'कर नंबर',
	TaxNumberType: 'कर संख्या का प्रकार',
	TaxNumberTypeInvalid: '{type} अमान्य है',
	TaxPercentageOfAmount: '{taxName} ({percentage}% {amount} का)',
	TaxRate: 'कर की दर',
	TaxRatesDescription: 'अपने इनवॉइस लाइन आइटम पर लागू होने वाली कर दरों का प्रबंधन करें.',
	Taxable: 'कर योग्य',
	TaxonomyCode: 'टैक्सोनॉमी कोड',
	TeacherAssistant: 'शिक्षक सहायक',
	Team: 'टीम',
	TeamMember: 'टीम के सदस्य',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {यह समय पहले से ही बुक है} other {ये समय पहले से ही बुक हैं}}.{br}डबल बुकिंग से बचने के लिए एक नया समय चुनें।',
	TeamMembers: 'टीम के सदस्य',
	TeamMembersColour: 'टीम के सदस्यों का रंग',
	TeamMembersDetails: 'टीम के सदस्यों का विवरण',
	TeamSize: 'आपकी टीम में कितने लोग हैं?',
	TeamTemplates: 'टीम टेम्पलेट्स',
	TeamTemplatesSectionDescription: 'आप और आपकी टीम द्वारा बनाया गया',
	TelehealthAndVideoCalls: 'टेलीहेल्थ ',
	TelehealthProvidedOtherThanInPatientCare: 'इन-पेशेंट देखभाल के अलावा अन्य के लिए टेलीहेल्थ प्रदान किया गया',
	TelehealthVideoCall: 'टेलीहेल्थ वीडियो कॉल',
	Template: 'खाका',
	TemplateDescription: 'टेम्पलेट विवरण',
	TemplateDetails: 'टेम्पलेट विवरण',
	TemplateEditModeViewSwitcherDescription: 'टेम्पलेट बनाएं और संपादित करें',
	TemplateGallery: 'समुदाय टेम्पलेट्स',
	TemplateImportCompletedNotificationSubject: 'टेम्प्लेट आयात पूर्ण हो गया! {templateTitle} उपयोग के लिए तैयार है।',
	TemplateImportFailedNotificationSubject: 'फ़ाइल {fileName} इम्पोर्ट करने में विफल रहा।',
	TemplateName: 'टेम्पलेट का नाम',
	TemplateNotFound: 'टेम्पलेट नहीं मिल सका.',
	TemplatePreviewErrorMessage: 'टेम्पलेट पूर्वावलोकन लोड करते समय एक त्रुटि हुई',
	TemplateResponderModeViewSwitcherDescription: 'फ़ॉर्म का पूर्वावलोकन करें और उनसे इंटरैक्ट करें',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'जाँचें कि उत्तरदाताओं द्वारा भरे जाने पर आपके फ़ॉर्म कैसे दिखाई देते हैं',
	TemplateSaved: 'सहेजे गए परिवर्तन',
	TemplateTitle: 'टेम्पलेट शीर्षक',
	TemplateType: 'टेम्पलेट प्रकार',
	Templates: 'टेम्प्लेट्स',
	TemplatesCategoriesFilter: 'श्रेणी के अनुसार फ़िल्टर करें',
	TemplatesPublicTemplatesFilter: ' समुदाय/टीम द्वारा फ़िल्टर करें',
	Text: 'मूलपाठ',
	TextAlign: 'पाठ संरेखण',
	TextColor: 'पाठ का रंग',
	ThankYouForYourFeedback: 'आपकी प्रतिक्रिया के लिए धन्यवाद!',
	ThanksForLettingKnow: 'हमें बताने के लिए धन्यवाद.',
	ThePaymentMethod: 'भुगतान विधि',
	ThemThey: 'उन्हें वे',
	Theme: 'विषय',
	ThemeAllColorsPickerTitle: 'और थीम',
	ThemeColor: 'विषय',
	ThemeColorDarkMode: 'अंधेरा',
	ThemeColorLightMode: 'प्रकाश',
	ThemeColorModePickerTitle: 'कलर मोड',
	ThemeColorSystemMode: 'सिस्टम',
	ThemeCpColorPickerTitle: 'Carepatron themes',
	ThemePanelDescription: 'लाइट और डार्क मोड में से चुनें, और अपनी थीम प्राथमिकताएँ अनुकूलित करें',
	ThemePanelTitle: 'दिखावट',
	Then: 'तब',
	Therapist: 'चिकित्सक',
	Therapists: 'चिकित्सक',
	Therapy: 'चिकित्सा',
	Thick: 'मोटा',
	Thin: 'पतला',
	ThirdPerson: '3 रा आदमी',
	ThisAndFollowingAppointments: 'यह और इसके बाद की नियुक्तियाँ',
	ThisAndFollowingMeetings: 'यह और इसके बाद की बैठकें',
	ThisAndFollowingReminders: 'यह और निम्नलिखित अनुस्मारक',
	ThisAndFollowingTasks: 'यह और निम्नलिखित कार्य',
	ThisAppointment: 'यह नियुक्ति',
	ThisMeeting: 'यह बैठक',
	ThisMonth: 'इस महीने',
	ThisPerson: 'इस व्यक्ति',
	ThisReminder: 'यह अनुस्मारक',
	ThisTask: 'इस कार्य',
	ThisWeek: 'इस सप्ताह',
	ThreeDay: '3 दिन',
	Thursday: 'गुरुवार',
	Time: 'समय',
	TimeAgoDays: '{number}वाँ',
	TimeAgoHours: '{number} घंटे',
	TimeAgoMinutes: '{number}{number, plural, one {मिनट} other {मिनट}}',
	TimeAgoSeconds: '{संख्या}s',
	TimeFormat: 'समय प्रारूप',
	TimeIncrement: 'समय वृद्धि',
	TimeRangeFormula: '{घंटे}:{मिनट}{isAM, select, true {सुबह} other {शाम}}',
	TimeslotSize: 'समय स्लॉट आकार',
	Timestamp: 'समय-चिह्न',
	Timezone: 'समय क्षेत्र',
	TimezoneDisplay: 'समयक्षेत्र प्रदर्शन',
	TimezoneDisplayDescription: 'अपनी समयक्षेत्र प्रदर्शन सेटिंग प्रबंधित करें.',
	Title: 'शीर्षक',
	To: 'को',
	ToYourWorkspace: 'आपके कार्यस्थल पर',
	Today: 'आज',
	TodayInHoursPlural: 'आज {count} {count, plural, one {घंटा} other {घंटे}} में',
	TodayInMinsAbbreviated: 'आज {count} {count, plural, one {min} other {mins}} में',
	ToggleHeaderCell: 'हेडर सेल टॉगल करें',
	ToggleHeaderCol: 'हेडर कॉलम टॉगल करें',
	ToggleHeaderRow: 'हेडर पंक्ति टॉगल करें',
	Tokyo: 'टोक्यो',
	Tomorrow: 'कल',
	TomorrowAfternoon: 'कल दोपहर',
	TomorrowMorning: 'कल सुबह',
	TooExpensive: 'अधिक महंगा',
	TooHardToSetUp: 'स्थापित करना बहुत कठिन',
	TooManyFiles: '1 से अधिक फ़ाइल का पता चला.',
	ToolsExample: 'सरल अभ्यास, माइक्रोसॉफ्ट, कैलेंड्ली, आसन, Doxy.me ...',
	Total: 'कुल',
	TotalAccountCredit: 'कुल खाता क्रेडिट',
	TotalAdjustments: 'कुल समायोजन',
	TotalAmountToCreditInCurrency: 'कुल क्रेडिट राशि ({currency})',
	TotalBilled: 'कुल बिल किया गया',
	TotalConversations: '{total} {total, plural, =0 {बातचीत} one {बातचीत} other {बातचीतें}}',
	TotalOverdue: 'कुल अतिदेय',
	TotalOverdueTooltip:
		'कुल अतिदेय शेष में वे सभी अवैतनिक चालान शामिल हैं, चाहे उनकी तिथि कुछ भी हो, जिन्हें न तो रद्द किया गया है और न ही संसाधित किया गया है।',
	TotalPaid: 'पूर्ण भुगतान',
	TotalPaidTooltip:
		'कुल भुगतान शेष में उन चालानों की सभी राशियाँ शामिल होती हैं जिनका भुगतान निर्दिष्ट तिथि सीमा के भीतर किया गया है।',
	TotalUnpaid: 'कुल अवैतनिक',
	TotalUnpaidTooltip:
		'कुल अवैतनिक शेष राशि में प्रसंस्करण, अवैतनिक, तथा निर्दिष्ट तिथि सीमा के भीतर देय भेजे गए चालानों से संबंधित सभी बकाया राशियां शामिल हैं।',
	TotalWorkflows: '{count} {count, plural, one {कार्यप्रवाह} other {कार्यप्रवाह}}',
	TotpSetUpManualEntryInstruction: 'वैकल्पिक रूप से, आप नीचे दिए गए कोड को मैन्युअल रूप से ऐप में दर्ज कर सकते हैं:',
	TotpSetUpModalDescription: 'मल्टी-फैक्टर प्रमाणीकरण सेट अप करने के लिए अपने प्रमाणक ऐप से QR कोड को स्कैन करें।',
	TotpSetUpModalTitle: 'MFA डिवाइस सेट अप करें',
	TotpSetUpSuccess: 'आप पूरी तरह तैयार हैं! MFA सक्षम कर दिया गया है।',
	TotpSetupEnterAuthenticatorCodeInstruction: 'अपने प्रमाणक ऐप द्वारा जनरेट किया गया कोड दर्ज करें',
	Transcribe: 'लिप्यंतरित',
	TranscribeLanguageSelector: 'इनपुट भाषा चुनें',
	TranscribeLiveAudio: 'लाइव ऑडियो ट्रांसक्राइब करें',
	Transcribing: 'ऑडियो का प्रतिलेखन किया जा रहा है...',
	TranscribingIn: 'प्रतिलेखन',
	Transcript: 'प्रतिलिपि',
	TranscriptRecordingCompleteInfo: 'रिकॉर्डिंग पूरी हो जाने पर आप अपनी प्रतिलिपि यहां देखेंगे।',
	TranscriptSuccessSnackbar: 'प्रतिलिपि सफलतापूर्वक संसाधित की गई.',
	Transcription: 'प्रतिलिपि',
	TranscriptionEmpty: 'कोई प्रतिलेखन उपलब्ध नहीं है',
	TranscriptionEmptyHelperMessage:
		'इस ट्रांस्क्रिप्शन ने कुछ भी नहीं पकड़ा। इसे पुनः प्रारंभ करें और पुनः प्रयास करें।',
	TranscriptionFailedNotice: 'यह प्रतिलेखन सफलतापूर्वक संसाधित नहीं किया गया',
	TranscriptionIdleMessage:
		'हमें कोई ऑडियो नहीं सुनाई दे रहा है। यदि आपको और समय चाहिए, तो कृपया {timeValue} सेकंड के भीतर जवाब दें, नहीं तो सत्र समाप्त हो जाएगा।',
	TranscriptionInProcess: 'ट्रांसक्रिप्शन प्रक्रिया में...',
	TranscriptionIncompleteNotice: 'इस प्रतिलेखन के कुछ भाग सफलतापूर्वक संसाधित नहीं किए गए',
	TranscriptionOvertimeWarning: '{scribeType} सत्र <strong>{timeValue} {unit}</strong> में समाप्त होता है',
	TranscriptionPartDeleteMessage: 'क्या आप वाकई इस प्रतिलेखन भाग को हटाना चाहते हैं?',
	TranscriptionText: 'आवाज़ से पाठ तक',
	TranscriptsPending: 'सत्र समाप्त होने के बाद आपकी प्रतिलिपि यहां उपलब्ध होगी।',
	Transfer: 'स्थानांतरण',
	TransferAndDelete: 'स्थानांतरित करें और हटाएं',
	TransferOwnership: 'स्थानांतरण स्वामित्व',
	TransferOwnershipConfirmationModalDescription:
		'यह कार्रवाई केवल तभी पूर्ववत की जा सकती है जब वे स्वामित्व आपको वापस हस्तांतरित कर दें।',
	TransferOwnershipDescription: 'इस कार्यस्थान का स्वामित्व किसी अन्य टीम सदस्य को हस्तांतरित करें.',
	TransferOwnershipSuccessSnackbar: 'स्वामित्व सफलतापूर्वक हस्तांतरित!',
	TransferOwnershipToMember: 'क्या आप सुनिश्चित हैं कि आप इस कार्यक्षेत्र को {staff} को स्थानांतरित करना चाहते हैं?',
	TransferStatusAlert:
		'{numberOfStatuses, plural, one {यह स्थिति} other {ये स्थितियाँ}} को हटाने से {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} क्लाइंट स्थिति.</strong>} other {<strong>{numberOfAffectedRecords} क्लाइंट स्थितियाँ.</strong>}} पर असर पड़ेगा।',
	TransferStatusDescription:
		'हटाने की प्रक्रिया शुरू करने से पहले इन क्लाइंट के लिए कोई दूसरी स्थिति चुनें। इस कार्रवाई को पूर्ववत नहीं किया जा सकता।',
	TransferStatusLabel: 'नई स्थिति में स्थानांतरण',
	TransferStatusPlaceholder: 'कोई मौजूदा स्थिति चुनें',
	TransferStatusTitle: 'हटाने से पहले स्थानांतरण की स्थिति',
	TransferTaskAttendeeStatusAlert:
		'इस स्थिति को हटाने से <strong>{number} भविष्य की नियुक्ति {number, plural, one {स्थिति} other {कई स्थितियां}}</strong> प्रभावित होगी।',
	TransferTaskAttendeeStatusDescription:
		'इन क्लाइंट्स के लिए कोई दूसरा स्टेटस चुनें, फिर डिलीट करने के लिए आगे बढ़ें। यह एक्शन वापस नहीं किया जा सकता।',
	TransferTaskAttendeeStatusSubtitle: 'अपॉइंटमेंट स्थिति',
	TransferTaskAttendeeStatusTitle: 'हटाने से पहले स्थानांतरण की स्थिति',
	Trash: 'कचरा',
	TrashDeleteItemsModalConfirm: 'पुष्टि करने के लिए, {confirmationText} टाइप करें',
	TrashDeleteItemsModalDescription:
		'निम्नलिखित {count, plural, one {आइटम} other {आइटम}} स्थायी रूप से हटा दिए जाएँगे और पुनर्स्थापित नहीं किए जा सकते हैं।',
	TrashDeleteItemsModalTitle: '{count, plural, one {आइटम} other {आइटम}} को हमेशा के लिए हटा दें',
	TrashDeletedAllItems: 'सभी आइटम हटा दिए गए',
	TrashDeletedItems: 'हटाया गया {count, plural, one {आइटम} other {आइटम}}',
	TrashDeletedItemsFailure: 'ट्रैश से आइटम हटाने में विफल',
	TrashLocationAppointmentType: 'कैलेंडर',
	TrashLocationBillingAndPaymentsType: 'बिलिंग और भुगतान',
	TrashLocationContactType: 'ग्राहकों',
	TrashLocationNoteType: 'नोट्स ',
	TrashRestoreItemsModalDescription: 'निम्नलिखित {count, plural, one {item} other {items}} पुनर्स्थापित किया जाएगा।',
	TrashRestoreItemsModalTitle: '{count, plural, one {आइटम} other {आइटम}} पुनर्स्थापित करें',
	TrashRestoredAllItems: 'सभी आइटम बहाल किए गए',
	TrashRestoredItems: 'पुनर्स्थापित {count, plural, one {आइटम} other {आइटम}}',
	TrashRestoredItemsFailure: 'ट्रैश से आइटम पुनर्स्थापित करने में विफल',
	TrashSuccessfullyDeletedItem: 'सफलतापूर्वक हटवलेले {type}',
	Trigger: 'चालू कर देना',
	Troubleshoot: 'समस्याओं का निवारण',
	TryAgain: 'पुनः प्रयास करें',
	Tuesday: 'मंगलवार',
	TwoToTen: '2 - 10',
	Type: 'प्रकार',
	TypeHere: 'यहाँ टाइप करें...',
	TypeToConfirm: 'पुष्टि करने के लिए, {keyword} टाइप करें',
	TypographyH1: 'एच 1',
	TypographyH2: 'एच 2',
	TypographyH3: 'एच3',
	TypographyH4: 'एच 4',
	TypographyH5: 'एच5',
	TypographyHeading1: 'शीर्षक 1',
	TypographyHeading2: 'शीर्षक 2',
	TypographyHeading3: 'शीर्षक 3',
	TypographyHeading4: 'शीर्षक 4',
	TypographyHeading5: 'शीर्षक 5',
	TypographyP: 'पी',
	TypographyParagraph: 'अनुच्छेद',
	UnableToCompleteAction: 'कार्रवाई पूरी करने में असमर्थ.',
	UnableToPrintDocument: 'दस्तावेज़ प्रिंट करने में असमर्थ। कृपया बाद में पुनः प्रयास करें।',
	Unallocated: 'आवंटित नहीं की गई',
	UnallocatedPaymentDescription: `यह भुगतान बिल योग्य मदों में पूर्णतः आवंटित नहीं किया गया है।
 अवैतनिक वस्तुओं के लिए आवंटन जोड़ें, या क्रेडिट या धन वापसी जारी करें।`,
	UnallocatedPaymentTitle: 'असंबद्ध भुगतान',
	UnallocatedPayments: 'असंबद्ध भुगतान',
	Unarchive: 'संग्रह से निकालें',
	Unassigned: 'सौंपे नहीं गए',
	UnauthorisedInvoiceSnackbar: 'आपके पास इस ग्राहक के लिए चालान प्रबंधित करने की पहुंच नहीं है.',
	UnauthorisedSnackbar: 'तुमको यह करने की इजाजत नहीं है।',
	Unavailable: 'अनुपलब्ध',
	Uncategorized: 'अवर्गीकृत',
	Unclaimed: 'लावारिस',
	UnclaimedAmount: 'दावा न किया गया राशि',
	UnclaimedItems: 'दावा न किए गए आइटम',
	UnclaimedItemsMustBeInCurrency: 'केवल निम्नलिखित मुद्राओं में आइटम समर्थित हैं: {currencies}',
	Uncle: 'चाचा',
	Unconfirmed: 'अपुष्ट',
	Underline: 'रेखांकन',
	Undo: 'पूर्ववत',
	Unfavorite: 'अनफ़ेवरेट',
	Uninvoiced: 'गैर-चालान',
	UninvoicedAmount: 'अनबिल्ड राशि',
	UninvoicedAmounts: '{count, plural, =0 {कोई बिल नहीं बनाया गया} one {बिल नहीं बनाया गया} other {बिल नहीं बनाए गए}}',
	Unit: 'इकाई',
	UnitedKingdom: 'यूनाइटेड किंगडम',
	UnitedStates: 'संयुक्त राज्य अमेरिका',
	UnitedStatesEast: 'संयुक्त राज्य अमेरिका - पूर्व',
	UnitedStatesWest: 'संयुक्त राज्य अमेरिका - पश्चिम',
	Units: 'इकाइयों',
	UnitsIsRequired: 'यूनिट आवश्यक है',
	UnitsMustBeGreaterThanZero: 'इकाइयाँ 0 से अधिक होनी चाहिए',
	UnitsPlaceholder: '1',
	Unknown: 'अज्ञात',
	Unlimited: 'असीमित',
	Unlock: 'अनलॉक',
	UnlockNoteHelper: 'कोई भी नया परिवर्तन करने से पहले संपादकों को नोट को अनलॉक करना आवश्यक है।',
	UnmuteAudio: 'ऑडियो अनम्यूट करें',
	UnmuteEveryone: 'सभी को अनम्यूट करें',
	Unpaid: 'अवैतनिक',
	UnpaidInvoices: 'अवैतनिक चालान',
	UnpaidItems: 'अवैतनिक आइटम',
	UnpaidMultiple: 'अवैतनिक',
	Unpublish: 'प्रकाशित न करें',
	UnpublishTemplateConfirmationModalPrompt:
		'<span>{title}</span> को हटाने से यह संसाधन Carepatron समुदाय से हटा दिया जाएगा। इस कार्रवाई को पूर्ववत नहीं किया जा सकता है।',
	UnpublishToCommunitySuccessMessage: 'सफलतापूर्वक समुदाय से ‛{title}’ हटा दिया गया',
	Unread: 'अपठित ग',
	Unrecognised: 'पहचानने अयोग्य',
	UnrecognisedDescription:
		'यह भुगतान विधि आपके वर्तमान एप्लिकेशन संस्करण द्वारा मान्यता प्राप्त नहीं है। कृपया इस भुगतान विधि को देखने और संपादित करने के लिए नवीनतम संस्करण प्राप्त करने के लिए अपने ब्राउज़र को रीफ़्रेश करें।',
	UnsavedChanges: 'सहेजे न गए परिवर्तन',
	UnsavedChangesPromptContent: 'क्या आप बंद करने से पहले अपने परिवर्तनों को सहेजना चाहते हैं?',
	UnsavedChangesPromptTitle: 'आपके पास सहेजे नहीं गए परिवर्तन हैं',
	UnsavedNoteChangesWarning: 'आपके द्वारा किए गए परिवर्तन सहेजे नहीं जा सकेंगे',
	UnsavedTemplateChangesWarning: 'आपके द्वारा किए गए परिवर्तन सहेजे नहीं जा सकेंगे',
	UnselectAll: 'सब अनचेक करें',
	Until: 'तक',
	UntitledConversation: 'शीर्षक रहित बातचीत',
	UntitledFolder: 'शीर्षक रहित फ़ोल्डर',
	UntitledNote: 'शीर्षक रहित नोट',
	UntitledSchedule: 'शीर्षकहीन अनुसूची',
	UntitledSection: 'शीर्षक रहित अनुभाग',
	UntitledTemplate: 'शीर्षक रहित टेम्पलेट',
	Unverified: 'असत्यापित',
	Upcoming: 'आगामी',
	UpcomingAppointments: 'आगामी नियुक्तियाँ',
	UpcomingDateOverridesEmpty: 'कोई तिथि ओवरराइड नहीं मिला',
	UpdateAvailabilityScheduleFailure: 'उपलब्धता शेड्यूल अपडेट करने में विफल',
	UpdateAvailabilityScheduleSuccess: 'उपलब्धता शेड्यूल सफलतापूर्वक अपडेट किया गया',
	UpdateInvoicesOrClaimsAgainstBillable:
		'क्या आप चाहते हैं कि नया मूल्य निर्धारण सहभागी के चालान और दावों पर लागू हो?',
	UpdateLink: 'लिंक अपडेट करें',
	UpdatePrimaryEmailWarningDescription:
		'अपने क्लाइंट का ईमेल पता बदलने से उनकी मौजूदा अपॉइंटमेंट और नोट्स तक उनकी पहुँच समाप्त हो जाएगी।',
	UpdatePrimaryEmailWarningTitle: 'क्लाइंट ईमेल बदलें',
	UpdateSettings: 'सेटिंग्स अपडेट करें',
	UpdateStatus: 'अद्यतन स्थिति',
	UpdateSuperbillReceiptFailure: 'सुपरबिल रसीद अपडेट करने में विफल',
	UpdateSuperbillReceiptSuccess: 'सुपरबिल रसीद सफलतापूर्वक अपडेट की गई',
	UpdateTaskBillingDetails: 'बिलिंग विवरण अपडेट करें',
	UpdateTaskBillingDetailsDescription:
		'अपॉइंटमेंट की कीमत बदल गई है। क्या आप चाहते हैं कि नई कीमत सहभागी के बिलिंग आइटम, इनवॉइस और दावों पर लागू हो? वे अपडेट चुनें जिनके साथ आप आगे बढ़ना चाहते हैं।',
	UpdateTemplateFolderSuccessMessage: 'फ़ोल्डर सफलतापूर्वक अपडेट किया गया',
	UpdateUnpaidInvoices: 'अवैतनिक चालान अपडेट करें',
	UpdateUserInfoSuccessSnackbar: 'उपयोगकर्ता जानकारी सफलतापूर्वक अपडेट की गई!',
	UpdateUserSettingsSuccessSnackbar: 'उपयोगकर्ता सेटिंग्स सफलतापूर्वक अपडेट की गईं!',
	Upgrade: 'उन्नत करना',
	UpgradeForSMSReminder: 'असीमित SMS रिमाइंडर के लिए <b>प्रोफेशनल</b> में अपग्रेड करें',
	UpgradeNow: 'अभी अपग्रेड करें',
	UpgradePlan: 'अपग्रेड योजना',
	UpgradeSubscriptionAlertDescription:
		'आपका स्टोरेज कम हो रहा है. अतिरिक्त स्टोरेज अनलॉक करने और अपने अभ्यास को सुचारू रूप से चलाने के लिए अपनी योजना को अपग्रेड करें!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'आपके पास स्टोरेज कम है. अपने अभ्यास में <span>व्यवस्थापक पहुँच</span> वाले किसी व्यक्ति से अपने योजना को अपग्रेड करने के बारे में पूछें ताकि आप अतिरिक्त स्टोरेज अनलॉक कर सकें और अपने अभ्यास को सुचारू रूप से चला सकें!',
	UpgradeSubscriptionAlertTitle: 'अब समय है अपनी सदस्यता को अपग्रेड करने का',
	UpgradeYourPlan: 'अपनी योजना को अपग्रेड करें',
	UploadAudio: 'ऑडियो अपलोड करें',
	UploadFile: 'फ़ाइल अपलोड करें',
	UploadFileDescription: 'आप किस सॉफ्टवेयर प्लेटफ़ॉर्म से बदल रहे हैं?',
	UploadFileMaxSizeError: 'फ़ाइल बहुत बड़ी है. अधिकतम फ़ाइल आकार {fileSizeLimit} है.',
	UploadFileSizeLimit: 'आकार सीमा {size}MB',
	UploadFileTileDescription: 'अपने ग्राहकों को अपलोड करने के लिए CSV, XLS, XLSX, या ZIP फ़ाइलों का उपयोग करें।',
	UploadFileTileLabel: 'फ़ाइल अपलोड करें',
	UploadFiles: 'फाइलें अपलोड करें',
	UploadIndividually: 'फ़ाइलें अलग-अलग अपलोड करें',
	UploadLogo: 'लोगो अपलोड करें',
	UploadPhoto: 'फोटो अपलोड करें',
	UploadToCarepatron: 'केयरपेट्रॉन पर अपलोड करें',
	UploadYourLogo: 'अपना लोगो अपलोड करें',
	UploadYourTemplates: 'अपने टेम्प्लेट अपलोड करें और हम उन्हें आपके लिए बदल देंगे',
	Uploading: 'अपलोड हो रहा है',
	UploadingAudio: 'आपका ऑडियो अपलोड हो रहा है...',
	UploadingFiles: 'फ़ाइलें अपलोड करना',
	UrlLink: 'यूआरएल लिंक',
	UsageCount: '{count} बार उपयोग किया गया',
	UsageLimitValue: '{used} में से {limit} इस्तेमाल किया गया',
	UsageValue: '{used} प्रयोग किया हुआ',
	Use: 'उपयोग',
	UseAiToAutomateYourWorkflow: 'अपने वर्कफ़्लो को स्वचालित करने के लिए AI का उपयोग करें!',
	UseAsDefault: 'डिफ़ॉल्ट के रूप में उपयोग करें',
	UseCustom: 'कस्टम का उपयोग करें',
	UseDefault: 'डिफ़ॉल्ट उपयोग करें',
	UseDefaultFilters: 'डिफ़ॉल्ट फ़िल्टर का उपयोग करें',
	UseTemplate: 'टेम्पलेट का इस्तेमाल करें',
	UseThisCard: 'इस कार्ड का उपयोग करें',
	UseValue: '"{value}" का उपयोग करें',
	UseWorkspaceDefault: 'कार्यस्थान डिफ़ॉल्ट का उपयोग करें',
	UserIsTyping: '{name} टाइप कर रहे हैं...',
	Username: 'उपयोगकर्ता नाम',
	Users: 'उपयोगकर्ताओं',
	VAT: 'टब',
	ValidUrl: 'URL लिंक एक वैध URL होना चाहिए.',
	Validate: 'मान्य',
	Validated: 'सत्यापित',
	Validating: 'सत्यापित किया जा रहा',
	ValidatingContent: 'सामग्री मान्य की जा रही है...',
	ValidatingTranscripts: 'प्रतिलिपियाँ मान्य की जा रही हैं...',
	ValidationConfirmPasswordRequired: 'पासवर्ड की पुष्टि आवश्यक है',
	ValidationDateMax: '{max} से पहले होना चाहिए',
	ValidationDateMin: '{min} के बाद होना चाहिए',
	ValidationDateRange: 'आरंभ और समाप्ति तिथि आवश्यक है',
	ValidationEndDateMustBeAfterStartDate: 'समाप्ति तिथि आरंभ तिथि के बाद होनी चाहिए',
	ValidationMixedDefault: 'यह अमान्य है',
	ValidationMixedRequired: 'यह आवश्यक है',
	ValidationNumberInteger: 'पूर्ण संख्या होनी चाहिए',
	ValidationNumberMax: '{max} या इससे कम होना चाहिए',
	ValidationNumberMin: '{min} या उससे ज़्यादा होना चाहिए',
	ValidationPasswordNotMatching: 'पासवर्ड मेल नहीं खाते',
	ValidationPrimaryAddressIsRequired: 'डिफ़ॉल्ट के रूप में सेट किए जाने पर पता आवश्यक है',
	ValidationPrimaryPhoneNumberIsRequired: 'डिफ़ॉल्ट के रूप में सेट होने पर फ़ोन नंबर आवश्यक है',
	ValidationServiceMustBeNotBeFuture: 'सेवा वर्तमान दिन या भविष्य में नहीं होनी चाहिए',
	ValidationStringEmail: 'ईमेल मान्य होना चाहिए',
	ValidationStringMax: '{max} या इससे कम वर्ण होने चाहिए',
	ValidationStringMin: '{min} या अधिक वर्ण असणे आवश्यक आहे',
	ValidationStringPhoneNumber: 'वैध फ़ोन नंबर होना चाहिए',
	ValueMinutes: '{value} मिनट',
	VerbosityConcise: 'संक्षिप्त',
	VerbosityDetailed: 'विस्तृत',
	VerbosityStandard: 'मानक',
	VerbositySuperDetailed: 'अति विस्तृत',
	VerificationCode: 'सत्यापन कोड',
	VerificationEmailDescription: 'कृपया अपना ईमेल पता और सत्यापन कोड दर्ज करें जो हमने आपको अभी भेजा है।',
	VerificationEmailSubtitle: 'स्पैम फ़ोल्डर की जाँच करें - यदि ईमेल नहीं आया है',
	VerificationEmailTitle: 'ईमेल सत्यापित करें',
	VerificationOption: 'ईमेल सत्यापन',
	Verified: 'सत्यापित',
	Verify: 'सत्यापित करें',
	VerifyAndSubmit: 'सत्यापित करें और सबमिट करें',
	VerifyEmail: 'ईमेल सत्यापित करें',
	VerifyEmailAccessCode: 'पुष्टि कोड',
	VerifyEmailAddress: 'ईमेल पते की पुष्टि करें',
	VerifyEmailButton: 'सत्यापित करें और लॉगआउट करें',
	VerifyEmailSentSnackbar: 'सत्यापन ईमेल भेजा गया। अपना इनबॉक्स देखें।',
	VerifyEmailSubTitle: 'यदि ईमेल नहीं आया है तो स्पैम फ़ोल्डर की जाँच करें',
	VerifyEmailSuccessLogOutSnackbar: 'सफल! कृपया परिवर्तन लागू करने के लिए लॉग आउट करें।',
	VerifyEmailSuccessSnackbar: 'सफल! ईमेल सत्यापित। कृपया सत्यापित खाते के रूप में जारी रखने के लिए लॉग इन करें।',
	VerifyEmailTitle: 'अपना ईमेल सत्यापित करें',
	VerifyNow: 'अभी सत्यापित करें',
	Veterinarian: 'पशुचिकित्सा',
	VideoCall: 'वीडियो कॉल',
	VideoCallAudioInputFailed: 'ऑडियो इनपुट डिवाइस काम नहीं कर रहा है',
	VideoCallAudioInputFailedMessage: 'सेटिंग खोलें और जांचें कि क्या आपके पास माइक्रोफ़ोन स्रोत सही ढंग से सेट है',
	VideoCallChatBanner: 'इस कॉल पर मौजूद सभी लोग संदेश देख सकेंगे और कॉल समाप्त होने पर उसे हटा दिया जाएगा।',
	VideoCallChatSendBtn: 'एक संदेश भेजो',
	VideoCallChatTitle: 'बात करना',
	VideoCallDisconnectedMessage: 'आपका नेटवर्क कनेक्शन टूट गया है। पुनः कनेक्ट करने का प्रयास कर रहे हैं',
	VideoCallOptionInfo: 'यदि ज़ूम कनेक्ट नहीं है तो केयरपैट्रॉन आपकी अपॉइंटमेंट के लिए वीडियो कॉल का प्रबंधन करेगा',
	VideoCallTilePaused: 'यह वीडियो आपके नेटवर्क में समस्या के कारण रोका गया है',
	VideoCallTranscriptionFormDescription: 'आप इन सेटिंग्स को किसी भी समय समायोजित कर सकते हैं',
	VideoCallTranscriptionFormHeading: 'अपने AI स्क्राइब को अनुकूलित करें',
	VideoCallTranscriptionFormLanguageField: 'उत्पन्न आउटपुट भाषा',
	VideoCallTranscriptionFormNoteTemplateField: 'डिफ़ॉल्ट नोट टेम्प्लेट सेट करें',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'AI वाला कोई टेम्प्लेट नहीं मिला',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'एक टेम्पलेट चुनें',
	VideoCallTranscriptionPronounField: 'आपके सर्वनाम',
	VideoCallTranscriptionRecordingNote:
		'सत्र के अंत में, आपको एक जनरेट किया गया <strong>{noteTemplate} नोट</strong> और ट्रांसक्रिप्ट प्राप्त होगा।',
	VideoCallTranscriptionReferClientField: 'ग्राहक को इस प्रकार संदर्भित करें',
	VideoCallTranscriptionReferPractitionerField: 'प्रैक्टिशनर को इस प्रकार देखें',
	VideoCallTranscriptionTitle: 'एआई स्क्राइब',
	VideoCallTranscriptionVerbosityField: 'शब्दाडंबर',
	VideoCallTranscriptionWritingPerspectiveField: 'लेखन परिप्रेक्ष्य',
	VideoCalls: 'वीडियो कॉल्स',
	VideoConferencing: 'वीडियो कॉन्फ्रेंसिंग',
	VideoOff: 'वीडियो बंद है',
	VideoOn: 'वीडियो बंद है',
	VideoQual360: 'निम्न गुणवत्ता (360p)',
	VideoQual540: 'मध्यम गुणवत्ता (540p)',
	VideoQual720: 'उच्च गुणवत्ता (720p)',
	View: 'देखना',
	ViewAll: 'सभी को देखें',
	ViewAppointment: 'अपॉइंटमेंट देखें',
	ViewBy: 'द्वारा देखें',
	ViewClaim: 'दावा देखें',
	ViewCollection: 'संग्रह देखें',
	ViewDetails: 'विवरण देखें',
	ViewEnrollment: 'नामांकन देखें',
	ViewPayment: 'भुगतान देखें',
	ViewRecord: 'रिकॉर्ड देखें',
	ViewRemittanceAdvice: 'रेमिटेंस सलाह देखें',
	ViewRemittanceAdviceHeader: 'दावा प्रेषण सलाह',
	ViewRemittanceAdviceSubheader: 'दावा {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'सेटिंग देखें',
	ViewStripeDashboard: 'स्ट्राइप डैशबोर्ड देखें',
	ViewTemplate: 'टेम्पलेट देखें',
	ViewTemplates: 'टेम्पलेट्स देखें',
	ViewableBy: 'द्वारा देखा जा सकता है',
	ViewableByHelper:
		'आपके और टीम के पास हमेशा आपके द्वारा प्रकाशित नोट्स तक पहुंच होती है। आप इस नोट को क्लाइंट और/या उनके संबंधों के साथ साझा करना चुन सकते हैं',
	Viewer: 'दर्शक',
	VirtualLocation: 'आभासी ठिकाना',
	VisibleTo: 'दिख रहा है',
	VisitOurHelpCentre: 'हमारे मदद केंद्र पर जाएँ',
	VisualEffects: 'दृश्य प्रभाव',
	VoiceFocus: 'आवाज़ पर ध्यान केंद्रित करना',
	VoiceFocusLabel: 'आपके माइक से ऐसी आवाज़ को फ़िल्टर करता है जो बोली नहीं है',
	Void: 'खालीपन',
	VoidCancelPriorClaim: 'शून्य/पहले के दावे को रद्द करें',
	WaitingforMins: '{count} मिनट इंतज़ार कर रहे हैं',
	Warning: 'चेतावनी',
	WatchAVideo: 'वीडियो देखें',
	WatchDemoVideo: 'डेमो वीडियो देखें',
	WebConference: 'वेब सम्मेलन',
	WebConferenceOrVirtualLocation: 'वेब कॉन्फ्रेंस / वर्चुअल लोकेशन',
	WebDeveloper: 'वेब डेवलपर',
	WebsiteOptional: 'वेबसाइट <span>(वैकल्पिक)</span>',
	WebsiteUrl: 'वेबसाइट यूआरएल',
	Wednesday: 'बुधवार',
	Week: 'सप्ताह',
	WeekPlural: '{count, plural, one {सप्ताह} other {सप्ताह}}',
	Weekly: 'साप्ताहिक',
	WeeksPlural: '{age, plural, one {# सप्ताह} other {# सप्ताह}}',
	WelcomeBack: 'वापसी पर स्वागत है',
	WelcomeBackName: 'स्वागत है वापस, {name}',
	WelcomeName: 'स्वागत है {name}',
	WelcomeToCarepatron: 'केयरपेट्रॉन में आपका स्वागत है',
	WhatCanIHelpWith: 'मैं किस प्रकार सहायता कर सकता हूं?',
	WhatDidYouLikeResponse: 'इस जवाब में आपको क्या पसंद आया?',
	WhatIsCarepatron: 'केयरपेट्रॉन क्या है?',
	WhatMadeYouCancel: `आपने अपनी योजना रद्द क्यों कर दी?
 लागू होने वाले सभी को जाँचे।`,
	WhatServicesDoYouOffer: 'क्या<mark> सेवा</mark> क्या आप पेशकश करते हैं?',
	WhatServicesDoYouOfferDescription: 'आप बाद में और सेवाएँ संपादित या जोड़ सकते हैं.',
	WhatsYourAvailability: 'आपकी <mark>उपलब्धता</mark> क्या है?',
	WhatsYourAvailabilityDescription: 'आप बाद में और शेड्यूल जोड़ सकते हैं।',
	WhatsYourBusinessName: 'आपका क्या है<mark> व्यवसाय का नाम?</mark>',
	WhatsYourTeamSize: 'आपका क्या है<mark> टीम का आकार?</mark>',
	WhatsYourTeamSizeDescription: 'इससे हमें आपके कार्यस्थल को सही ढंग से स्थापित करने में मदद मिलेगी।',
	WhenThisHappens: 'ऐसा कब होता है:',
	WhichBestDescribesYou: 'कौन सा सर्वोत्तम<mark> आपका वर्णन क्या है?</mark>',
	WhichPlatforms: 'कौन से प्लेटफॉर्म?',
	Wife: 'पत्नी',
	WorkflowDescription: 'वर्कफ़्लो विवरण',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'टेम्प्लेट्स, सुचारू प्रक्रियाओं के लिए वर्कफ़्लो से जुड़ सकते हैं। जुड़े हुए वर्कफ़्लो को आसानी से ट्रैक और अपडेट करने के लिए देखें।',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'साझा ट्रिगर के आधार पर अपने SMS + ईमेल कनेक्ट करें',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'कार्यप्रवाह स्वचालन',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'स्वचालित कार्यप्रवाह',
	WorkflowTemplateConfigKey_Body: 'शरीर',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'ब्रांडिंग दिखाएँ',
	WorkflowTemplateConfigKey_Content: 'सामग्री',
	WorkflowTemplateConfigKey_Footer: 'फ़ुटर',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'फुटर दिखाएं',
	WorkflowTemplateConfigKey_Header: 'शीर्षलेख',
	WorkflowTemplateConfigKey_Header_IsVisible: 'हेडर दिखाएं',
	WorkflowTemplateConfigKey_SecurityFooter: 'सुरक्षा फ़ुटर',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'सुरक्षा फ़ुटर दिखाएँ',
	WorkflowTemplateConfigKey_Subject: 'विषय',
	WorkflowTemplateConfigKey_Title: 'शीर्षक',
	WorkflowTemplateDeleteConfirmationMessage:
		'क्या आप यह टेम्पलेट डिलीट करना चाहते हैं? यह एक्शन वापस नहीं लिया जा सकता.',
	WorkflowTemplateDeleteConfirmationTitle: 'नोटिफिकेशन टेम्पलेट डिलीट करें',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'क्या आप सुनिश्चित हैं? इससे केवल {locale} संस्करण हट जाएगा—अन्य भाषाएँ प्रभावित नहीं होंगी. यह क्रिया पूर्ववत नहीं की जा सकती.',
	WorkflowTemplateDeleteLocalisationDialogTitle: '‘{locale}’ टेम्पलेट हटाएँ',
	WorkflowTemplateDeletedSuccess: 'नोटिफ़िकेशन टेम्प्लेट सफ़लतापूर्वक डिलीट हो गया है',
	WorkflowTemplateEditorDetailsTab: 'टेम्प्लेट विवरण',
	WorkflowTemplateEditorEmailContent: 'ईमेल सामग्री',
	WorkflowTemplateEditorEmailContentTab: 'ईमेल सामग्री',
	WorkflowTemplateEditorThemeTab: 'थीम',
	WorkflowTemplatePreviewerAlert: 'पूर्वावलोकन नमूना डेटा का उपयोग करके दिखाते हैं कि आपके क्लाइंट क्या देखेंगे।',
	WorkflowTemplateResetEmailContentDialogDescription:
		'क्या आप सुनिश्चित हैं? यह सिस्टम के डिफ़ॉल्ट टेम्पलेट पर वापस संस्करण को रीसेट कर देगा। इस कार्रवाई को पूर्ववत नहीं किया जा सकता है।',
	WorkflowTemplateResetEmailContentDialogTitle: 'टेम्पलेट रीसेट करें',
	WorkflowTemplateSendTestEmail: 'टेस्ट ईमेल भेजें',
	WorkflowTemplateSendTestEmailDialogDescription:
		'अपने ईमेल सेटअप का परीक्षण करने के लिए खुद को एक टेस्ट ईमेल भेजें।',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'प्राप्तकर्ता ईमेल',
	WorkflowTemplateSendTestEmailDialogSendButton: 'टेस्ट भेजें',
	WorkflowTemplateSendTestEmailDialogTitle: 'टेस्ट ईमेल भेजें',
	WorkflowTemplateSendTestEmailSuccess: 'सफलता! आपका <mark>{templateName}</mark> टेस्ट ईमेल भेजा जा चुका है।',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'अपने टेम्प्लेट प्रबंधित करें और ग्राहकों के साथ प्रभावी ढंग से संवाद करने के लिए कई भाषा संस्करण जोड़ें।',
	WorkflowTemplateTemplateEditor: 'टेम्पलेट एडिटर',
	WorkflowTemplateTranslateLocaleError: 'सामग्री का अनुवाद करते समय कुछ गलत हो गया',
	WorkflowTemplateTranslateLocaleSuccess: 'सामग्री को <strong>{locale}</strong> में सफलतापूर्वक अनुवादित किया गया',
	WorkflowsAndReminders: 'वर्कफ़्लो ',
	WorkflowsManagement: 'वर्कफ़्लो प्रबंधन',
	WorksheetAndHandout: 'कार्यपत्रक/हस्तलिखित',
	WorksheetsAndHandoutsDescription: 'क्लाइंट इंगेजमेंट और शिक्षा के लिए',
	Workspace: 'कार्यस्थान',
	WorkspaceBranding: 'कार्यस्थल ब्रांडिंग',
	WorkspaceBrandingDescription: `अपने कार्यस्थल को एक सुसंगत शैली के साथ सहजता से ब्रांड करें जो आपकी ब्रांडिंग को दर्शाता है
 व्यावसायिकता और व्यक्तित्व। एक सुंदर के लिए ऑनलाइन बुकिंग के लिए चालान अनुकूलित करें
 ग्राहक अनुभव.`,
	WorkspaceName: 'कार्यस्थान का नाम',
	Workspaces: 'कार्यस्थानों',
	WriteOff: 'ख़ारिज करना',
	WriteOffModalDescription:
		'आपके पास <mark>{count} {count, plural, one {लाइन आइटम} other {लाइन आइटम}}</mark> लिखने के लिए हैं',
	WriteOffModalTitle: 'बट्टे खाते में डालने का समायोजन',
	WriteOffReasonHelperText: 'यह एक आंतरिक नोट है और यह आपके ग्राहक को दिखाई नहीं देगा।',
	WriteOffReasonPlaceholder: 'बिल योग्य लेन-देन की समीक्षा करते समय राइट-ऑफ का कारण जोड़ने से मदद मिल सकती है',
	WriteOffTotal: 'कुल राइट-ऑफ ({currencyCode})',
	Writer: 'लेखक',
	Yearly: 'वार्षिक',
	YearsPlural: '{age, plural, one {# वर्ष} other {# वर्ष}}',
	Yes: 'हाँ',
	YesArchive: 'हाँ, संग्रह',
	YesDelete: 'हां, हटाएं',
	YesDeleteOverride: 'हां, ओवरराइड हटाएं',
	YesDeleteSection: 'हां, हटाएं',
	YesDisconnect: 'हाँ, डिस्कनेक्ट',
	YesEnd: 'हाँ, अंत',
	YesEndTranscription: 'हां, प्रतिलेखन समाप्त',
	YesImFineWithThat: 'हां, मुझे इससे कोई परेशानी नहीं है',
	YesLeave: 'हाँ, चले जाएँ',
	YesMinimize: 'हाँ, कम करें',
	YesOrNoAnswerTypeDescription: 'उत्तर प्रकार कॉन्फ़िगर करें',
	YesOrNoFormPrimaryText: 'हाँ | नहीं',
	YesOrNoFormSecondaryText: 'हाँ या नहीं विकल्प चुनें',
	YesProceed: 'हाँ, आगे बढ़ें',
	YesRemove: 'हां, हटाएँ',
	YesRestore: 'हाँ, पुनर्स्थापित करें',
	YesStopIgnoring: 'हाँ, अनदेखा करना बंद करो',
	YesTransfer: 'हाँ, स्थानांतरित करें',
	Yesterday: 'कल',
	YogaInstructor: 'योग प्रशिक्षक',
	You: 'आप',
	YouArePresenting: 'आप प्रस्तुत कर रहे हैं',
	YouCanChooseMultiple: 'आप एकाधिक चुन सकते हैं',
	YouCanSelectMultiple: 'आप एकाधिक चयन कर सकते हैं',
	YouHaveOngoingTranscription: 'आपके पास एक चालू प्रतिलेखन है',
	YourAnswer: 'आपका उत्तर',
	YourDisplayName: 'आपका प्रदर्शन नाम',
	YourSpreadsheetColumns: 'आपके स्प्रेडशीट कॉलम',
	YourTeam: 'आपकी टीम',
	ZipCode: 'ज़िप कोड',
	Zoom: 'ज़ूम',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'आप इस टीम के सदस्य के लिए ज़ूम कॉल नहीं जोड़ सकते। <a>अधिक जानकारी के लिए कृपया सहायता दस्तावेज़ देखें।</a>',
};

export default items;
