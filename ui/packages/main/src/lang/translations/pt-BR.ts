import { TranslationLanguage } from '../langIds';

const items: TranslationLanguage = {
	ABN: 'CNPJ',
	AIPrompts: 'Avisos de IA',
	ATeamMemberIsRequired: 'É necessário um membro da equipe',
	AboutClient: 'Sobre o cliente',
	AcceptAppointment: 'Obri<PERSON> por confirmar sua consulta',
	AcceptTermsAndConditionsRequired: 'Aceitar os termos ',
	Accepted: '<PERSON><PERSON>',
	AccessGiven: 'Acesso concedido',
	AccessPermissions: 'Permissões de acesso',
	AccessType: 'Tipo de acesso',
	Accident: 'Acidente',
	Account: 'Conta',
	AccountCredit: 'Crédito da conta',
	Accountant: 'Contador',
	Action: 'Ação',
	Actions: 'Ações',
	Active: 'Ativo',
	ActiveTags: 'Etiquetas ativas',
	ActiveUsers: 'Usuários ativos',
	Activity: 'Atividade',
	Actor: 'Ator',
	Acupuncture: 'Acupuntura',
	Acupuncturist: 'Acupunturista',
	Acupuncturists: 'Acupunturistas',
	AcuteManifestationOfAChronicCondition: 'Manifestação aguda de uma condição crônica',
	Add: 'Adicionar',
	AddADescription: 'Acrescente uma descrição',
	AddALocation: 'Adicionar local',
	AddASecondTimezone: 'Adicione um segundo fuso horário',
	AddAddress: 'Adicionar endereço',
	AddAnother: '  Adicionar outro',
	AddAnotherAccount: 'Adicionar outra conta',
	AddAnotherContact: 'Adicionar outro contato',
	AddAnotherOption: 'Adicione outra opção',
	AddAnotherTeamMember: 'Adicionar outro membro da equipe',
	AddAvailablePayers: '+ Adicionar pagadores disponíveis',
	AddAvailablePayersDescription:
		'Pesquise pagadores para adicionar à lista de pagadores do seu espaço de trabalho. Depois de adicioná-los, você pode gerenciar as inscrições ou ajustar os detalhes do pagador conforme necessário.',
	AddCaption: 'Adicionar legenda',
	AddClaim: 'Adicionar reivindicação',
	AddClientFilesModalDescription:
		'Para restringir o acesso escolha as opções nas caixas de seleção &quot;Visível por&quot;',
	AddClientFilesModalTitle: 'Carregar arquivos para {name}',
	AddClientNoteButton: 'Adicionar nota',
	AddClientNoteModalDescription:
		'Adicione conteúdo à sua nota. Use a seção &quot;Visualizável por&quot; para selecionar um ou mais grupos que podem ver esta nota específica.',
	AddClientNoteModalTitle: 'Adicionar nota',
	AddClientOwnerRelationshipModalDescription:
		'Convidar o cliente permitirá que ele gerencie suas próprias informações de perfil e gerencie o acesso do usuário às informações de seu perfil.',
	AddClientOwnerRelationshipModalTitle: 'Convide o cliente',
	AddCode: 'Adicionar código',
	AddColAfter: 'Adicionar coluna depois',
	AddColBefore: 'Adicionar coluna antes',
	AddCollection: 'Adicionar coleção',
	AddColor: 'Adicionar cor',
	AddColumn: 'Adicionar coluna',
	AddContactRelationship: 'Adicionar relacionamento de contato',
	AddContacts: 'Adicionar contatos',
	AddCustomField: 'Adicionar campo personalizado',
	AddDate: 'Adicionar data',
	AddDescription: 'Adicionar descrição',
	AddDetail: 'Adicionar detalhe',
	AddDisplayName: 'Adicionar nome de exibição',
	AddDxCode: 'Adicionar código de diagnóstico',
	AddEmail: 'Adicionar e-mail',
	AddFamilyClientRelationshipModalDescription:
		'Convidar um membro da família permitirá que ele veja histórias de cuidados e informações do perfil do cliente. Se forem convidados como administradores, terão acesso para atualizar as informações do perfil do cliente e gerenciar o acesso do usuário.',
	AddFamilyClientRelationshipModalTitle: 'Convidar membro da família',
	AddField: 'Adicionar campo',
	AddFormField: 'Adicionar campo de formulário',
	AddImages: 'Adicionar imagens',
	AddInsurance: 'Adicionar seguro',
	AddInvoice: 'Adicionar fatura',
	AddLabel: 'Adicionar rótulo',
	AddLanguage: 'Adicionar idioma',
	AddLocation: 'Adicionar local',
	AddManually: 'Adicionar manualmente',
	AddMessage: 'Adicionar mensagem',
	AddNewAction: 'Adicionar nova ação',
	AddNewSection: 'Adicionar nova seção',
	AddNote: 'Adicionar nota',
	AddOnlineBookingDetails: 'Adicione detalhes da reserva on-line',
	AddPOS: 'Adicionar PDV',
	AddPaidInvoices: 'Adicionar faturas pagas',
	AddPayer: 'Adicionar pagador',
	AddPayment: 'Adicionar pagamento',
	AddPaymentAdjustment: 'Adicionar ajuste de pagamento',
	AddPaymentAdjustmentDisabledDescription: 'As alocações de pagamento não serão alteradas.',
	AddPaymentAdjustmentEnabledDescription: 'O valor disponível para alocação será reduzido.',
	AddPhoneNumber: 'Adicionar número de telefone',
	AddPhysicalOrVirtualLocations: 'Adicione locais físicos ou virtuais',
	AddQuestion: 'Adicionar pergunta',
	AddQuestionOrTitle: 'Adicione uma pergunta ou título',
	AddRelationship: 'Adicionar relacionamento',
	AddRelationshipModalTitle: 'Conectar contato existente',
	AddRelationshipModalTitleNewClient: 'Conectar novo contato',
	AddRow: 'Adicionar linha',
	AddRowAbove: 'Adicionar linha acima',
	AddRowBelow: 'Adicionar linha abaixo',
	AddService: 'Adicionar serviço',
	AddServiceLocation: 'Adicionar local de serviço',
	AddServiceToCollections: 'Adicionar serviço às coleções',
	AddServiceToOneOrMoreCollections: 'Adicionar serviço a uma ou mais coleções',
	AddServices: 'Adicionar serviços',
	AddSignature: 'Adicionar assinatura',
	AddSignaturePlaceholder: 'Digite detalhes adicionais para incluir em sua assinatura',
	AddSmartDataChips: 'Adicione chips de dados inteligentes',
	AddStaffClientRelationshipsModalDescription:
		'A seleção da equipe permitirá que eles criem e visualizem histórias de atendimento para esse cliente. Eles também poderão visualizar informações do cliente.',
	AddStaffClientRelationshipsModalTitle: 'Adicionar relacionamentos de equipe',
	AddTag: 'Adicionar uma etiqueta',
	AddTags: 'Adicionar tags',
	AddTemplate: 'Adicionar modelo',
	AddTimezone: 'Adicionar fuso horário',
	AddToClaim: 'Adicionar à reivindicação',
	AddToCollection: 'Adicionar à coleção',
	AddToExisting: 'Adicionar ao existente',
	AddToStarred: 'Adicionar aos favoritos',
	AddUnclaimedItems: 'Adicionar itens não reclamados',
	AddUnrelatedContactWarning:
		'Você adicionou alguém que não é um contato de {contact}. Certifique-se de que o conteúdo seja relevante antes de prosseguir com o compartilhamento.',
	AddValue: 'Adicione "{value}"',
	AddVideoCall: 'Adicionar videochamada',
	AddVideoOrVoiceCall: 'Adicionar vídeo ou chamada de voz',
	AddictionCounselor: 'Conselheiro de dependência',
	AddingManualPayerDisclaimer:
		'Adicionar um pagador manualmente à sua lista de fornecedores não configura uma conexão para envio eletrônico de reivindicações com esse pagador, mas pode ser usado para criar reivindicações manualmente.',
	AddingTeamMembersIncreaseCostAlert: 'Adicionar novos membros à equipe aumentará sua assinatura mensal.',
	Additional: 'Adicional',
	AdditionalBillingProfiles: 'Perfis de faturamento adicionais',
	AdditionalBillingProfilesSectionDescription:
		'Substitua as informações de cobrança padrão usadas para membros da equipe, pagadores ou modelos de fatura específicos.',
	AdditionalFeedback: 'Comentários adicionais',
	AddnNewWorkspace: 'Novo espaço de trabalho',
	AddnNewWorkspaceSuccessSnackbar: 'O espaço de trabalho foi criado!',
	Address: 'Endereço',
	AddressNumberStreet: 'Endereço (Nº, rua)',
	Adjustment: 'Ajuste',
	AdjustmentType: 'Tipo de ajuste',
	Admin: 'Administrador',
	Admins: 'Administradores',
	AdminsOnly: 'Somente administradores',
	AdvancedPlanInclusionFive: 'Gerente de contas',
	AdvancedPlanInclusionFour: 'Análise do Google',
	AdvancedPlanInclusionHeader: 'Tudo em Plus  ',
	AdvancedPlanInclusionOne: 'Funções ',
	AdvancedPlanInclusionSix: 'Suporte para importação de dados',
	AdvancedPlanInclusionThree: 'Marcação branca',
	AdvancedPlanInclusionTwo: 'Retenção de dados excluídos por 90 dias',
	AdvancedPlanMessage:
		'Mantenha o controle das necessidades da sua prática. Revise seu plano atual e monitore o uso.',
	AdvancedSettings: 'Configurações avançadas',
	AdvancedSubscriptionPlanSubtitle: 'Expanda sua prática com todos os recursos',
	AdvancedSubscriptionPlanTitle: 'Avançado',
	AdvertisingManager: 'Gerente de publicidade',
	AerospaceEngineer: 'Engenheiro aeroespacial',
	AgeYearsOld: '{age} anos de idade',
	Agenda: 'Agenda',
	AgendaView: 'Visualização da agenda',
	AiAskSupportedFileTypes: 'Tipos de arquivo suportados: JPEG, PNG, PDF, Word',
	AiAssistantAtYourFingertips: 'Um assistente ao seu alcance',
	AiCopilotDisclaimer: 'O AI Copilot pode cometer erros. Verifique informações importantes.',
	AiCreateNewConversation: 'Criar nova conversa',
	AiEnhanceYourProductivity: 'Aumente sua produtividade',
	AiPoweredTemplates: 'Modelos com tecnologia de IA',
	AiScribeNoDeviceFoundErrorMessage:
		'Parece que seu navegador não suporta esse recurso ou não há dispositivos compatíveis disponíveis.',
	AiScribeUploadFormat: 'Tipos de arquivo suportados: MP3, WAV, MP4',
	AiScribeUploadSizeLimit: 'apenas 1 arquivo por vez',
	AiShowConversationHistory: 'Mostrar histórico da conversa',
	AiSmartPromptNodePlaceholderText:
		'Digite sua solicitação personalizada aqui para ajudar a gerar resultados de IA precisos e personalizados.',
	AiSmartPromptPrimaryText: 'Ai prompt inteligente',
	AiSmartPromptSecondaryText: 'Insira prompt inteligente personalizado de IA',
	AiSmartReminders: 'Lembretes inteligentes de IA',
	AiTemplateBannerTitle: 'Simplifique seu trabalho com modelos com tecnologia de IA',
	AiTemplates: 'Modelos de IA',
	AiTokens: 'Tokens de IA',
	AiWorkBetterWithAi: 'Trabalhe melhor com IA',
	All: 'Todos',
	AllAppointments: 'Todos os compromissos',
	AllCategories: 'Todas as categorias',
	AllClients: 'Todos os clientes',
	AllContactPolicySelectorLabel: 'Todos os contatos de <mark>{client}</mark>',
	AllContacts: 'Todos os contatos',
	AllContactsOf: 'Todos os contatos de ‘{name}’',
	AllDay: 'Dia todo',
	AllInboxes: 'Todas as caixas de entrada',
	AllIndustries: 'Todas as indústrias',
	AllLocations: 'Todos os locais',
	AllMeetings: 'Todas as reuniões',
	AllNotificationsRestoredMessage: 'Todas as notificações restauradas',
	AllProfessions: 'Todas as profissões',
	AllReminders: 'Todos os lembretes',
	AllServices: 'Todos os serviços',
	AllStatuses: 'Todos os status',
	AllTags: 'Todas as tags',
	AllTasks: 'Todas as tarefas',
	AllTeamMembers: 'Todos os membros da equipe',
	AllTypes: 'Todos os tipos',
	Allocated: 'Alocado',
	AllocatedItems: 'Itens alocados',
	AllocationTableEmptyState: 'Nenhuma alocação de pagamento encontrada',
	AllocationTotalWarningMessage: `O valor alocado excede o valor total do pagamento.
 Por favor, revise os itens abaixo.`,
	AllowClientsToCancelAnytime: 'Permitir que os clientes cancelem a qualquer momento',
	AllowNewClient: 'Permitir novos clientes',
	AllowNewClientHelper: 'Novos clientes podem reservar este serviço',
	AllowToCancelAtLeastBlankHoursBeforeAppointment: 'Agendar com no mínimo {hours} horas de antecedência',
	AllowToUseSavedCard: 'Permitir que {provider} use o cartão salvo no futuro',
	AllowVideoCalls: 'Permitir videochamadas',
	AlreadyAdded: 'Já adicionado',
	AlreadyHasAccess: 'Tem acesso',
	AlreadyHasAccount: 'já tem uma conta?',
	Always: 'Sempre',
	AlwaysIgnore: 'Sempre ignorar',
	Amount: 'Quantia',
	AmountDue: 'Valor devido',
	AmountOfReferralRequests: '{amount, plural, one {# solicitação de indicação} other {# solicitações de indicação}}',
	AmountPaid: 'Valor pago',
	AnalyzingAudio: 'Analisando áudio...',
	AnalyzingInputContent: 'Analisando conteúdo de entrada...',
	AnalyzingRequest: 'Analisando solicitação...',
	AnalyzingTemplateContent: 'Analisando o conteúdo do modelo...',
	And: 'e',
	Annually: 'Anualmente',
	Anonymous: 'Anônimo',
	AnswerExceeded: 'Sua resposta deve ter menos de 300 caracteres.',
	AnyStatus: 'Qualquer status',
	AppNotifications: 'Notificações',
	AppNotificationsClearanceHeading: 'Bom trabalho! Você limpou todas as atividades',
	AppNotificationsEmptyHeading: 'A atividade do seu espaço de trabalho aparecerá aqui em breve',
	AppNotificationsEmptySubtext: 'Não há ações a serem tomadas no momento',
	AppNotificationsIgnoredCount: '{total} ignored',
	AppNotificationsUnread: '{total} não lidos',
	Append: 'Acrescentar',
	Apply: 'Aplicar',
	ApplyAccountCredit: 'Aplicar crédito na conta',
	ApplyDiscount: 'Aplicar desconto',
	ApplyVisualEffects: 'Aplicar efeitos visuais',
	ApplyVisualEffectsNotSupported: 'Aplicar efeitos visuais não suportados',
	Appointment: 'Encontro',
	AppointmentAssignedNotificationSubject: '{actorProfileName} atribuiu a você {appointmentName}',
	AppointmentCancelledNotificationSubject: '{actorProfileName} cancelou {appointmentName}',
	AppointmentConfirmedNotificationSubject: '{actorProfileName} confirmou {appointmentName}',
	AppointmentDetails: 'Detalhes do compromisso',
	AppointmentLocation: 'Local do agendamento',
	AppointmentLocationDescription:
		'Gerencie seus locais virtuais e físicos padrão. Quando um agendamento for feito, esses locais serão aplicados automaticamente.',
	AppointmentNotFound: 'Agendamento não encontrado',
	AppointmentReminder: 'Lembrete de compromisso',
	AppointmentReminders: 'Lembretes de compromissos',
	AppointmentRemindersInfo:
		'Defina lembretes automatizados para compromissos de clientes para evitar não comparecimentos e cancelamentos',
	AppointmentRescheduledNotificationSubject: '{actorProfileName} reagendou {appointmentName}',
	AppointmentSaved: 'Compromisso salvo',
	AppointmentStatus: 'Status do compromisso',
	AppointmentTotalDuration:
		'{hours, plural, =0 { {minutes, plural, =0 {0mins} other {{minutes}mins}} } one {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} other {{hours}hr {minutes, plural, =0 {} other {{minutes}mins}}} }',
	AppointmentUndone: 'Compromisso desfeito',
	Appointments: 'Compromissos',
	Archive: 'Arquivo',
	ArchiveClients: 'Clientes de arquivo',
	Archived: 'Arquivado',
	AreYouAClient: 'Você é um cliente?',
	AreYouStillThere: 'Você ainda está aí?',
	AreYouSure: 'Tem certeza?',
	Arrangements: 'Arranjos',
	ArtTherapist: 'Arteterapeuta',
	Articles: 'Artigos',
	Artist: 'Artista',
	AskAI: 'Pergunte à IA',
	AskAiAddFormField: 'Adicionar um campo de formulário',
	AskAiChangeFormality: 'Change formality',
	AskAiChangeToneToBeMoreProfessional: 'Mude o tom para ser mais profissional',
	AskAiExplainThis: 'AskAiExplainThis',
	AskAiExplainWhatThisDocumentIsAbout: 'Explique do que se trata este documento',
	AskAiExplainWhatThisImageIsAbout: 'Explique do que se trata esta imagem',
	AskAiFixSpellingAndGrammar: 'Corrigir ortografia e gramática',
	AskAiGenerateACaptionForThisImage: 'Gere uma legenda para esta imagem',
	AskAiGenerateFromThisPage: 'Generate from this page',
	AskAiGetStarted: 'Get started',
	AskAiGiveItAFriendlyTone: 'Give it a friendly tone',
	AskAiGreeting: 'Oi {firstName}! Como posso ajudar você hoje?',
	AskAiHowCanIHelpWithYourContent: 'Como posso ajudar com seu conteúdo?',
	AskAiInsert: 'Inserir',
	AskAiMakeItMoreCasual: 'Make it more casual',
	AskAiMakeThisTextMoreConcise: 'Torne este texto mais conciso',
	AskAiMoreProfessional: 'More professional',
	AskAiOpenPreviousNote: 'Abrir nota anterior',
	AskAiPondering: 'Ponderando',
	AskAiReplace: 'Substituir',
	AskAiReviewOrEditSelection: 'Review or edit selection',
	AskAiRuminating: 'Ruminando',
	AskAiSeeMore: 'Ver mais',
	AskAiSimplifyLanguage: 'Simplify language',
	AskAiSomethingWentWrong:
		'Algo deu errado. Se o problema persistir, entre em contato conosco através do nosso centro de ajuda.',
	AskAiStartWithATemplate: 'Comece com um modelo',
	AskAiSuccessfullyCopiedResponse: 'Resposta de IA copiada com sucesso',
	AskAiSuccessfullyInsertedResponse: 'Resposta de IA inserida com sucesso',
	AskAiSuccessfullyReplacedResponse: 'Resposta de IA substituída com sucesso',
	AskAiSuggested: 'Sugerido',
	AskAiSummariseTextIntoBulletPoints: 'Resumir o texto em marcadores',
	AskAiSummarizeNote: 'Summarize note',
	AskAiThinking: 'Pensamento',
	AskAiToday: 'Hoje {time}',
	AskAiWhatDoYouWantToDoWithThisForm: 'O que você quer fazer com este formulário?',
	AskAiWriteProfessionalNoteUsingTemplate: 'Escreva uma nota profissional usando o modelo',
	AskAskAiAnything: 'Pergunte qualquer coisa à IA',
	AskWriteSearchAnything: `Pergunte, escreva '@' ou pesquise qualquer coisa...`,
	Asking: 'Perguntando',
	Assessment: 'Avaliação',
	Assessments: 'Avaliações',
	AssessmentsCategoryDescription: 'Para registrar avaliações de clientes',
	AssignClients: 'Atribuir clientes',
	AssignNewClients: 'Atribuir clientes',
	AssignServices: 'Atribuir serviços',
	AssignTeam: 'Atribuir equipe',
	AssignTeamMember: 'Atribuir membro da equipe',
	Assigned: 'Atribuído',
	AssignedClients: 'Clientes atribuídos',
	AssignedServices: 'Serviços atribuídos',
	AssignedServicesDescription:
		'Visualize e gerencie seus serviços atribuídos, ajustando os preços para refletir suas tarifas personalizadas. ',
	AssignedTeam: 'Equipe designada',
	AthleticTrainer: 'Treinador atlético',
	AttachFiles: 'Anexar arquivos',
	AttachLogo: 'Anexar',
	Attachment: 'Anexo',
	AttachmentBlockedFileType: 'Bloqueado por motivos de segurança!',
	AttachmentTooLargeFileSize: 'Arquivo muito grande',
	AttachmentUploadItemComplete: 'Completo',
	AttachmentUploadItemError: 'Falha no upload',
	AttachmentUploadItemLoading: 'Carregando',
	AttemptingToReconnect: 'Tentando reconectar...',
	Attended: 'Participaram',
	AttendeeBeingMutedTooltip: `O anfitrião silenciou você. Use 'levantar a mão' para solicitar o cancelamento do som`,
	AttendeeWithId: 'Participante {attendeeId}',
	Attendees: 'Participantes',
	AttendeesCount: '{count} participantes',
	Attending: 'Participando',
	Audiologist: 'Audiologista',
	Aunt: 'Tia',
	Australia: 'Austrália',
	AuthenticationCode: 'Código de autenticação',
	AuthoriseProvider: 'Autorizar {provider}',
	AuthorisedProviders: 'Fornecedores autorizados',
	AutoDeclineAllFutureOption: 'Somente eventos ou compromissos novos',
	AutoDeclineAllOption: 'Eventos e compromissos novos e existentes',
	AutoDeclinePrimaryText: 'Recusar eventos automaticamente',
	AutoDeclineSecondaryText: 'Eventos durante seu período de ausência serão automaticamente recusados.',
	AutogenerateBillings: 'Gerar automaticamente documentos de cobrança',
	AutogenerateBillingsDescription:
		'Os documentos de cobrança automatizados serão gerados no último dia do mês. Faturas e recibos superbill podem ser criados manualmente a qualquer momento.',
	AutomateWorkflows: 'Automatize fluxos de trabalho',
	AutomaticallySendSuperbill: 'Envie automaticamente recibos de superbill',
	AutomaticallySendSuperbillHelperText:
		'Uma superfatura é um recibo detalhado dos serviços prestados a um cliente para reembolso de seguro',
	Automation: 'Automação',
	AutomationActionSendEmailLabel: 'Enviar e-mail',
	AutomationActionSendSMSLabel: 'Enviar SMS',
	AutomationAndReminders: 'Automação ',
	AutomationDeletedSuccessMessage: 'Automação excluída com sucesso',
	AutomationDisable: 'Disable automation',
	AutomationEnable: 'Enable automation',
	AutomationParams_timeTrigger: 'Evento de tempo',
	AutomationParams_timeUnit: 'Unidade',
	AutomationParams_timeValue: 'Número',
	AutomationPublishSuccessMessage: 'Automação publicada com sucesso',
	AutomationPublishWarningTooltip:
		'Verifique novamente a configuração de automação e certifique-se de que ela foi configurada corretamente',
	AutomationTriggerEventCancelledDescription: 'Acionado quando um evento é cancelado ou excluído',
	AutomationTriggerEventCancelledLabel: 'Evento cancelado',
	AutomationTriggerEventCreatedDescription: 'Aciona quando um evento é criado',
	AutomationTriggerEventCreatedLabel: 'Novo evento',
	AutomationTriggerEventCreatedOrUpdatedDescription:
		'Acionado quando um evento é criado ou atualizado (exceto quando é cancelado)',
	AutomationTriggerEventCreatedOrUpdatedLabel: 'Evento novo ou atualizado',
	AutomationTriggerEventEndedDescription: 'Aciona quando um evento termina',
	AutomationTriggerEventEndedLabel: 'Evento encerrado',
	AutomationTriggerEventStartsDescription:
		'Aciona quando um período de tempo especificado antes do início de um evento',
	AutomationTriggerEventStartsLabel: 'Início do evento',
	Automations: 'Automações',
	Availability: 'Disponibilidade',
	AvailabilityDisableSchedule: 'Desativar agendamento',
	AvailabilityDisabled: 'Desabilitado',
	AvailabilityEnableSchedule: 'Ativar agendamento',
	AvailabilityEnabled: 'Habilitado',
	AvailabilityNoActiveBanner:
		'Você desligou todas as suas programações. Os clientes não podem agendar você on-line e todos os agendamentos futuros precisam ser confirmados manualmente.',
	AvailabilityNoActiveConfirmationDescription:
		'Desabilitar esta disponibilidade resultará na ausência de programações ativas. Os clientes não poderão fazer reservas on-line e quaisquer reservas feitas por profissionais ficarão fora do seu horário de trabalho.',
	AvailabilityNoActiveConfirmationProceed: 'Sim, prossiga',
	AvailabilityNoActiveConfirmationTitle: 'Nenhuma programação ativa',
	AvailabilityToggle: 'Agendamento ativado',
	AvailabilityUnsetDate: 'Sem data definida',
	AvailableLocations: 'Locais disponíveis',
	AvailablePayers: 'Pagadores disponíveis',
	AvailablePayersEmptyState: 'Nenhum pagador selecionado',
	AvailableTimes: 'Horários disponíveis',
	Back: 'Voltar',
	BackHome: 'De volta para casa',
	BackToAppointment: 'Voltar para agendamento',
	BackToLogin: 'Voltar para o login',
	BackToMapColumns: 'Voltar às colunas do mapa',
	BackToTemplates: 'Voltar para Modelos',
	BackToUploadFile: 'Voltar para Fazer upload de arquivo',
	Banker: 'Banqueiro',
	BasicBlocks: 'Blocos básicos',
	BeforeAppointment: 'Enviar lembrete de {deliveryType} {interval} {unit} antes do compromisso',
	BehavioralAnalyst: 'Analista Comportamental',
	BehavioralHealthTherapy: 'Terapia de saúde comportamental',
	Beta: 'Beta',
	BillTo: 'projeto de lei para',
	BillableItems: 'Itens faturáveis',
	BillableItemsEmptyState: 'Nenhum item faturável foi encontrado',
	Biller: 'Faturador',
	Billing: 'Cobrança',
	BillingAddress: 'Endereço de Cobrança',
	BillingAndReceiptsUnauthorisedMessage:
		'O acesso à visualização de faturas e pagamentos é necessário para acessar essas informações.',
	BillingBillablesTab: 'Faturáveis',
	BillingClaimsTab: 'Reivindicações',
	BillingDetails: 'Detalhes de faturamento',
	BillingDocuments: 'Documentos de faturamento',
	BillingDocumentsClaimsTab: 'Reivindicações',
	BillingDocumentsEmptyState: 'Nenhum {tabType} foi encontrado',
	BillingDocumentsInvoicesTab: 'Faturas',
	BillingDocumentsSuperbillsTab: 'Superfaturas',
	BillingInformation: 'Informações de pagamento',
	BillingInvoicesTab: 'Faturas',
	BillingItems: 'Billing items',
	BillingPaymentsTab: 'Pagamentos',
	BillingPeriod: 'Período de pagamento',
	BillingProfile: 'Perfil de cobrança',
	BillingProfileOverridesDescription: 'Limite o uso deste perfil de cobrança a membros específicos da equipe',
	BillingProfileOverridesHeader: 'Limitar acesso',
	BillingProfileProviderType: 'Tipo de provedor',
	BillingProfileTypeIndividual: 'Opcional',
	BillingProfileTypeIndividualSubLabel: 'NPI Tipo 1',
	BillingProfileTypeOrganisation: 'Organização',
	BillingProfileTypeOrganisationSubLabel: 'NPI Tipo 2',
	BillingProfiles: 'Perfis de cobrança',
	BillingProfilesEditHeader: 'Editar perfil de cobrança de {name}',
	BillingProfilesNewHeader: 'Novo perfil de cobrança',
	BillingProfilesSectionDescription:
		'Gerencie suas informações de cobrança para profissionais e pagadores de seguros configurando perfis de cobrança que podem ser aplicados a faturas e pagamentos de seguros.',
	BillingSearchPlaceholder: 'Pesquisar itens',
	BillingSettings: 'Configurações de faturamento',
	BillingSuperbillsTab: 'Superbills',
	BiomedicalEngineer: 'Engenheiro Biomédico',
	BlankInvoice: 'Fatura em branco',
	BlueShieldProviderNumber: 'Número do provedor Blue Shield',
	Body: 'Corpo',
	Bold: 'Audacioso',
	BookAgain: 'Reserve novamente',
	BookAppointment: 'Anotação de livro',
	BookableOnline: 'Reserva on-line',
	BookableOnlineHelper: 'Os clientes podem reservar este serviço online',
	BookedOnline: 'Agendado online',
	Booking: 'Reserva',
	BookingAnalyticsIntegrationPanelDescription:
		'Configure o Google Tag Manager para rastrear ações e conversões importantes no seu fluxo de reservas on-line. Reúna dados valiosos sobre interações do usuário para melhorar os esforços de marketing e otimizar a experiência de reserva.',
	BookingAnalyticsIntegrationPanelTitle: 'Integração analítica',
	BookingAndCancellationPolicies: 'Reserva ',
	BookingButtonEmbed: 'Botão',
	BookingButtonEmbedDescription: 'Adiciona um botão de reserva online ao seu site',
	BookingDirectTextLink: 'Link de texto direto',
	BookingDirectTextLinkDescription: 'Abre a página de reserva on-line',
	BookingFormatLink: 'Link de formato',
	BookingFormatLinkButtonTitle: 'Título do botão',
	BookingInlineEmbed: 'Incorporação in-line',
	BookingInlineEmbedDescription: 'Carrega a página de reserva online diretamente no seu site',
	BookingLink: 'Link de reserva',
	BookingLinkModalCopyText: 'cópia de',
	BookingLinkModalDescription:
		'Permitir que os clientes com este link reservem qualquer membro da equipe ou serviços',
	BookingLinkModalHelpText: 'Aprenda como configurar reservas online',
	BookingLinkModalTitle: 'Compartilhe seu link de reserva',
	BookingPolicies: 'Políticas de reserva',
	BookingPoliciesDescription: 'Defina quando as reservas online podem ser feitas pelos clientes',
	BookingTimeUnitDays: 'dias',
	BookingTimeUnitHours: 'horas',
	BookingTimeUnitMinutes: 'minutos',
	BookingTimeUnitMonths: 'meses',
	BookingTimeUnitWeeks: 'semanas',
	BottomNavBilling: 'Cobrança',
	BottomNavGettingStarted: 'Lar',
	BottomNavMore: 'Mais',
	BottomNavNotes: 'Notas',
	Brands: 'Marcas',
	Brother: 'Irmão',
	BrotherInLaw: 'Cunhado',
	BrowseOrDragFileHere: '<link>Navegar</link> ou arrastar arquivo aqui',
	BrowseOrDragFileHereDescription: 'PNG, JPG (máx. {limit})',
	BufferAfterTime: '{time} mins depois',
	BufferAndLabel: 'e',
	BufferAppointmentLabel: 'um compromisso',
	BufferBeforeTime: '{time} mins antes',
	BufferTime: 'Tempo de atraso',
	BufferTimeViewLabel: '{bufferBefore} mins antes e {bufferAfter} mins depois dos compromissos',
	BulkArchiveClientsDescription:
		'Tem certeza de que deseja arquivar esses clientes? Você pode reativá-los mais tarde.',
	BulkArchiveSuccess: 'Clientes arquivados com sucesso',
	BulkArchiveUndone: 'Arquivo em massa desfeito',
	BulkPermanentDeleteDescription: 'Isso irá apagar **{count} conversas**. Esta ação não pode ser desfeita.',
	BulkPermanentDeleteTitle: 'Apagar conversas para sempre',
	BulkUnarchiveSuccess: 'Clientes desarquivados com sucesso',
	BulletedList: 'Lista com marcadores',
	BusinessAddress: 'Endereço da empresa',
	BusinessAddressOptional: 'Endereço da empresa<span>(Opcional)</span>',
	BusinessName: 'Nome da empresa',
	Button: 'Botão',
	By: 'Por',
	CHAMPUSIdentificationNumber: 'Número de identificação CHAMPUS',
	CHAMPVA: 'CHAMPVA',
	CVCRequired: 'CVC é obrigatório',
	Calendar: 'Calendário',
	CalendarAppSyncFormDescription: 'Sincronizar eventos do Carepatron com',
	CalendarAppSyncPanelTitle: 'Sincronização de aplicativo conectado',
	CalendarDescription: 'Gerencie seus compromissos ou defina tarefas e lembretes pessoais',
	CalendarDetails: 'Detalhes do calendário',
	CalendarDetailsDescription: 'Gerencie suas configurações de exibição de calendário e compromissos.',
	CalendarScheduleNew: 'Agendar novo',
	CalendarSettings: 'Configurações do calendário',
	Call: 'Chamada',
	CallAttendeeJoinAttemptedNotificationSubject: '<strong>{attendeeName}</strong> entrou na chamada de vídeo',
	CallChangeLayoutTextContent: 'A seleção é salva para reuniões futuras',
	CallIdlePrompt: 'Você prefere continuar esperando para ingressar ou tentar novamente mais tarde?',
	CallLayoutOptionAuto: 'Automático',
	CallLayoutOptionSidebar: 'Barra lateral',
	CallLayoutOptionSpotlight: 'Destaque',
	CallLayoutOptionTiled: 'Em grade',
	CallNoAttendees: 'Nenhum participante na reunião.',
	CallSessionExpiredError: 'Sessão expirada. A chamada foi encerrada. Por favor, tente entrar novamente.',
	CallWithPractitioner: 'Chamada com {practitioner}',
	CallsListCreateButton: 'Nova chamada',
	CallsListEmptyState: 'Nenhuma chamada ativa',
	CallsListItemEndCall: 'Encerrar chamada',
	CamWarningMessage: 'Foi detectado um problema com sua câmera',
	Camera: 'Câmera',
	CameraAndMicIssueModalDescription: `Ative o acesso do Carepatron à sua câmera e microfone.
 Para mais informações, <a>siga este guia</a>`,
	CameraAndMicIssueModalTitle: 'Câmera e microfone estão bloqueados',
	CameraQuality: 'Qualidade da câmera',
	CameraSource: 'Fonte da câmera',
	CanModifyReadOnlyEvent: 'Você não pode modificar este evento',
	Canada: 'Canadá',
	Cancel: 'Cancelar',
	CancelClientImportDescription: 'Tem certeza de que deseja cancelar esta importação?',
	CancelClientImportPrimaryAction: 'Sim, cancelar importação',
	CancelClientImportSecondaryAction: 'Continue editando',
	CancelClientImportTitle: 'Cancelar a importação de clientes',
	CancelImportButton: 'Cancelar importação',
	CancelPlan: 'Cancelar plano',
	CancelPlanConfirmation: `O cancelamento do plano cobrará automaticamente em sua conta todos os saldos pendentes que você tiver neste mês.
 Se quiser fazer downgrade de seus usuários faturados, você pode simplesmente remover membros da equipe e o Carepatron atualizará automaticamente o preço da sua assinatura.`,
	CancelSend: 'Cancelar envio',
	CancelSubscription: 'Cancelar assinatura',
	Canceled: 'Cancelado',
	CancellationPolicy: 'Política de cancelamento',
	Cancelled: 'Cancelado',
	CannotContainSpecialCharactersError: 'Não pode conter {specialCharacters}',
	CannotDeleteInvoice: 'As faturas pagas através de pagamentos online não podem ser excluídas',
	CannotMoveCarepatronStatusOutsideGroup: '<b>{status}</b> não pode ser movido para fora do grupo <b>{group}</b>',
	CannotMoveServiceOutsideCollections: 'O serviço não pode ser movido para fora das coleções',
	CapeTown: 'cidade do Cabo',
	Caption: 'Rubrica',
	CaptureNameFieldLabel: 'O nome que você gostaria que outras pessoas se referissem a você',
	CapturePaymentMethod: 'Capturar método de pagamento',
	CapturingAudio: 'Capturando áudio',
	CapturingSignature: 'Capturando assinatura...',
	CardInformation: 'Informações do cartão',
	CardNumberRequired: 'O número do cartão é obrigatório',
	CardiacRehabilitationSpecialist: 'Especialista em Reabilitação Cardíaca',
	Cardiologist: 'Cardiologista',
	CareAiNoConversations: 'Ainda não há conversas',
	CareAiNoConversationsDescription: 'Comece uma conversa com {aiName} para começar',
	CareAssistant: 'Assistente de cuidados',
	CareManager: 'Gerente de cuidados',
	Caregiver: 'Cuidador',
	CaregiverCreateModalDescription:
		'Adicionar funcionários como administradores permitirá que eles criem e gerenciem histórias de atendimento. Também lhes dá acesso total para criar e gerenciar clientes.',
	CaregiverCreateModalTitle: 'Novo membro da equipe',
	CaregiverListCantAddStaffInfoTitle:
		'Você atingiu o número máximo de funcionários para sua assinatura. Atualize seu plano para adicionar mais membros da equipe.',
	CaregiverListCreateButton: 'Novo membro da equipe',
	CaregiverListEmptyState: 'Nenhum cuidador adicionado',
	CaregiversListItemRemoveStaff: 'Remover equipe',
	CarepatronApp: 'Aplicativo Carepatron',
	CarepatronCommunity: 'Comunidade',
	CarepatronFieldAddress: 'Endereço',
	CarepatronFieldAssignedStaff: 'Pessoal designado',
	CarepatronFieldBirthDate: 'Data de nascimento',
	CarepatronFieldEmail: 'E-mail',
	CarepatronFieldEmploymentStatus: 'Situação de emprego',
	CarepatronFieldEthnicity: 'Etnia',
	CarepatronFieldFirstName: 'Primeiro nome',
	CarepatronFieldGender: 'Gênero',
	CarepatronFieldIdentificationNumber: 'Número de identificação',
	CarepatronFieldIsArchived: 'Status',
	CarepatronFieldLabel: 'Rótulo',
	CarepatronFieldLastName: 'Sobrenome',
	CarepatronFieldLivingArrangements: 'Arranjos de vida',
	CarepatronFieldMiddleNames: 'Nome do meio',
	CarepatronFieldOccupation: 'Ocupação',
	CarepatronFieldPhoneNumber: 'Número de telefone',
	CarepatronFieldRelationshipStatus: 'Status de relacionamento',
	CarepatronFieldStatus: 'Status',
	CarepatronFieldStatusHelperText: '10 status no máximo.',
	CarepatronFieldTags: 'Tag',
	CarepatronFields: 'Campos de cuidadores',
	Cash: 'Dinheiro',
	Category: 'Categoria',
	CategoryInputPlaceholder: 'Escolha uma categoria de modelo',
	CenterAlign: 'Alinhamento central',
	Central: 'Central',
	ChangeLayout: 'Alterar layout',
	ChangeLogo: 'Mudar',
	ChangePassword: 'Alterar a senha',
	ChangePasswordFailureSnackbar: 'Desculpe, sua senha não foi alterada. Verifique se sua senha antiga está correta.',
	ChangePasswordHelperInfo: 'Comprimento mínimo de {minLength}',
	ChangePasswordSuccessfulSnackbar:
		'Senha alterada com sucesso! Na próxima vez que você fizer login, certifique-se de usar essa senha.',
	ChangeSubscription: 'Alterar assinatura',
	ChangesNotAllowed: 'Não é possível fazer alterações neste campo',
	ChargesDisabled: 'Cobranças desativadas',
	ChargesEnabled: 'Cobranças ativadas',
	ChargesStatus: 'Status das cobranças',
	ChartAndDiagram: 'Gráfico/Diagrama',
	ChartsAndDiagramsCategoryDescription: 'Para ilustrar dados e progresso do cliente',
	ChatEditMessage: 'Editar mensagem',
	ChatReplyTo: 'Responder para {name}',
	ChatTypeMessageTo: 'Mensagem {name}',
	Check: 'Verificar',
	CheckList: 'Lista de controle',
	Chef: 'Chefe de cozinha',
	Chiropractic: 'Quiropraxia',
	Chiropractor: 'Quiroprático',
	Chiropractors: 'Quiropráticos',
	ChooseACollection: 'Escolha uma coleção',
	ChooseAContact: 'Escolha um contato',
	ChooseAccountTypeHeader: 'O que melhor descreve você?',
	ChooseAction: 'Escolha a ação',
	ChooseAnAccount: 'Escolha uma conta',
	ChooseAnOption: 'Escolha uma opção',
	ChooseBillingProfile: 'Escolha o perfil de cobrança',
	ChooseClaim: 'Escolha a reivindicação',
	ChooseCollection: 'Escolha a coleção',
	ChooseColor: 'Escolha a cor',
	ChooseCustomDate: 'Escolha uma data personalizada',
	ChooseDateAndTime: 'Escolher data e hora',
	ChooseDxCodes: 'Escolha códigos de diagnóstico',
	ChooseEventType: 'Escolha o tipo de evento',
	ChooseFileButton: 'Escolha um arquivo',
	ChooseFolder: 'Escolha pasta',
	ChooseInbox: 'Escolha a caixa de entrada',
	ChooseMethod: 'Escolha o método',
	ChooseNewOwner: 'Escolha o novo proprietário',
	ChooseOrganization: 'Escolha a organização',
	ChoosePassword: 'Escolha a senha',
	ChoosePayer: 'Escolha o pagador',
	ChoosePaymentMethod: 'Escolha um método de pagamento',
	ChoosePhysicalOrRemoteLocations: 'Insira ou escolha a localização',
	ChoosePlan: 'Escolha {plan}',
	ChooseProfessional: 'Escolha Profissional',
	ChooseServices: 'Escolha serviços',
	ChooseSource: 'Escolha a fonte',
	ChooseSourceDescription:
		'Escolha de onde você está importando clientes - seja de um arquivo ou de outra plataforma de software.',
	ChooseTags: 'Escolha tags',
	ChooseTaxName: 'Escolha o nome do imposto',
	ChooseTeamMembers: 'Escolha os membros da equipe',
	ChooseTheme: 'Escolha o tema',
	ChooseTrigger: 'Escolha o gatilho',
	ChooseYourProvider: 'Escolha seu provedor',
	CircularProgressWithLabel: '{value}%',
	City: 'Cidade',
	CivilEngineer: 'Engenheiro civil',
	Claim: 'Alegar',
	ClaimAddReferringProvider: 'Adicionar provedor referenciador',
	ClaimAddRenderingProvider: 'Adicionar provedor de renderização',
	ClaimAmount: 'Valor da fatura',
	ClaimAmountPaidHelpContent:
		'O valor pago é o pagamento recebido do paciente ou de outros pagadores. Insira o valor total que o paciente e/ou outros pagadores pagaram somente nos serviços cobertos.',
	ClaimAmountPaidHelpSubtitle: 'Campo 29',
	ClaimAmountPaidHelpTitle: 'Valor pago',
	ClaimBillingProfileTypeIndividual: 'Individual',
	ClaimBillingProfileTypeOrganisation: 'Organização',
	ClaimChooseRenderingProviderOrTeamMember: 'Escolha o provedor de renderização ou membro da equipe',
	ClaimClientInsurancePolicies: 'Apólices de seguro do cliente',
	ClaimCreatedAction: '<mark>Reivindicação {claimNumber}</mark> criada',
	ClaimDeniedAction: '<mark>Reivindicação {claimNumber}</mark> foi negada por <b>{payerNumber} {payerName}</b>',
	ClaimDiagnosisCodeSelectorPlaceholder: 'Search ICD 10 diagnosis codes',
	ClaimDiagnosisSelectorHelpContent: `O “Diagnóstico ou lesão” é o sinal, sintoma, reclamação ou condição do paciente em relação ao(s) serviço(s) na reivindicação.
 É possível selecionar até 12 códigos de diagnóstico do CID 10.`,
	ClaimDiagnosisSelectorHelpSubtitle: 'Campo 21',
	ClaimDiagnosisSelectorHelpTitle: 'Diagnóstico ou lesão',
	ClaimDiagnosticCodesEmptyError: 'Pelo menos um código de diagnóstico é necessário',
	ClaimDoIncludeReferrerInformation: 'Inclua informações de referência no CMS1500',
	ClaimERAReceivedAction: 'Remessa eletrônica recebida de <b>{payerNumber} {payerName}</b>',
	ClaimElectronicPaymentAction:
		'Remessa eletrônica recebida	<mark>Pagamento {paymentReference}</mark> para <b>{paymentAmount}</b> por <b>{payerNumber} {payerName}</b> foi registrada',
	ClaimExportedAction: '<mark>Reivindicação {claimNumber}</mark> foi exportada como <b>{attachmentType}</b>',
	ClaimFieldClient: 'Nome do cliente ou contato',
	ClaimFieldClientAddress: 'Endereço do cliente',
	ClaimFieldClientAddressDescription:
		'Insira o endereço do cliente. A primeira linha é para o endereço da rua. Não use pontuação (vírgulas ou pontos) ou quaisquer símbolos no endereço. Se estiver relatando um endereço estrangeiro, entre em contato com o pagador para obter instruções específicas de relatório.',
	ClaimFieldClientAddressSubtitle: 'Campo 5',
	ClaimFieldClientDateOfBirth: 'Data de nascimento do cliente',
	ClaimFieldClientDateOfBirthAndSexSubtitle: 'Campo 3',
	ClaimFieldClientDateOfBirthDescription:
		'Insira a data de nascimento de 8 dígitos do cliente (MM/DD/AAAA). A data de nascimento do cliente é uma informação que identificará o cliente e distinguirá pessoas com nomes semelhantes.',
	ClaimFieldClientDescription: `O 'nome do cliente' é o nome da pessoa que recebeu o tratamento ou os suprimentos.`,
	ClaimFieldClientSexDescription: `O 'sexo' é uma informação que identificará o cliente e distinguirá pessoas com nomes semelhantes.`,
	ClaimFieldClientSubtitle: 'Campo 2',
	ClaimFiling: 'Solicitação de Reembolso',
	ClaimHistorySubtitle: 'Seguro • Sinistro {number}',
	ClaimIncidentAutoAccident: 'Acidente de carro?',
	ClaimIncidentConditionRelatedTo: 'A condição do cliente está relacionada a',
	ClaimIncidentConditionRelatedToHelpContent:
		'Essas informações indicam se a doença ou lesão do cliente está relacionada ao emprego, acidente automobilístico ou outro acidente. Emprego (atual ou anterior) indicaria que a condição está relacionada ao trabalho ou local de trabalho do cliente. Acidente automobilístico indicaria que as condições são o resultado de um acidente automobilístico. Outro acidente indicaria que a condição é o resultado de qualquer outro tipo de acidente.',
	ClaimIncidentConditionRelatedToHelpSubtitle: 'Campos 10a - 10c',
	ClaimIncidentConditionRelatedToHelpTitle: 'A condição do cliente está relacionada a',
	ClaimIncidentCurrentIllness: 'Doença atual, lesão ou gravidez',
	ClaimIncidentCurrentIllnessHelpContent:
		'A data da doença, lesão ou gravidez atual identifica a primeira data de início da doença, a data real da lesão ou a DUM da gravidez.',
	ClaimIncidentCurrentIllnessHelpSubtitle: 'Campo 14',
	ClaimIncidentCurrentIllnessHelpTitle: 'Datas da doença atual, lesão ou gravidez (LMP)',
	ClaimIncidentDate: 'Data',
	ClaimIncidentDateFrom: 'Data de',
	ClaimIncidentDateTo: 'Data para',
	ClaimIncidentEmploymentRelated: 'Emprego',
	ClaimIncidentEmploymentRelatedDesc: '(Atual ou anterior)',
	ClaimIncidentHospitalizationDatesLabel: 'Datas de internação hospitalar relacionadas aos serviços atuais',
	ClaimIncidentHospitalizationDatesLabelHelpContent:
		'As datas de hospitalização relacionadas aos serviços atuais referem-se à estadia do cliente e indicam as datas de admissão e alta associadas ao(s) serviço(s) na solicitação.',
	ClaimIncidentHospitalizationDatesLabelHelpSubtitle: 'Campo 18',
	ClaimIncidentHospitalizationDatesLabelHelpTitle: 'Datas de internação hospitalar relacionadas aos serviços atuais',
	ClaimIncidentInformation: 'Informações sobre o incidente',
	ClaimIncidentOtherAccident: 'Outro acidente?',
	ClaimIncidentOtherAssociatedDate: 'Outra data associada',
	ClaimIncidentOtherAssociatedDateHelpContent:
		'A outra data identifica informações adicionais sobre a condição ou tratamento do cliente.',
	ClaimIncidentOtherAssociatedDateHelpSubtitle: 'Campo 15',
	ClaimIncidentOtherAssociatedDateHelpTitle: 'Outra data',
	ClaimIncidentQualifier: 'Qualificador',
	ClaimIncidentQualifierPlaceholder: 'Escolha o qualificador',
	ClaimIncidentUnableToWorkDatesLabel: 'O cliente não conseguiu trabalhar na ocupação atual',
	ClaimIncidentUnableToWorkDatesLabelHelpContent:
		'As datas em que o cliente não pôde trabalhar na ocupação atual são o período em que o cliente não pôde ou não pôde trabalhar',
	ClaimIncidentUnableToWorkDatesLabelHelpSubtitle: 'Campo 16',
	ClaimIncidentUnableToWorkDatesLabelHelpTitle: 'Datas em que o cliente não pôde trabalhar na ocupação atual',
	ClaimIncludeReferrerInformation: 'Incluir informações de referência no CMS1500',
	ClaimInsuranceCoverageTypeHelpContent: `O tipo de cobertura de seguro saúde aplicável a esta reivindicação. Outro indica seguro saúde, incluindo HMOs, seguro comercial, acidente automobilístico, responsabilidade civil ou compensação trabalhista.
 Essas informações direcionam a reclamação ao programa correto e podem estabelecer a responsabilidade primária.`,
	ClaimInsuranceCoverageTypeHelpSubtitle: 'Campo 1',
	ClaimInsuranceCoverageTypeHelpTitle: 'Tipo de cobertura',
	ClaimInsuranceGroupIdHelpContent: `Insira o número da apólice ou do grupo do segurado conforme aparece no cartão de identificação de assistência médica do segurado.

 O “Insured's Policy, Group, or FECA Number” é o identificador alfanumérico para a cobertura do plano de saúde, automóvel ou outro plano de seguro. O número FECA é o identificador alfanumérico de 9 caracteres atribuído a um paciente que reivindica uma condição relacionada ao trabalho.`,
	ClaimInsuranceGroupIdHelpSubtitle: 'Campo 11',
	ClaimInsuranceGroupIdHelpTitle: 'Número da apólice, grupo ou FECA do segurado',
	ClaimInsuranceMemberIdHelpContent: `Insira o número de identificação do segurado, conforme mostrado no cartão de identificação do segurado para o pagador ao qual a reivindicação está sendo enviada.
 Se o paciente tiver um Número de Identificação de Membro exclusivo atribuído pelo pagador, insira esse número neste campo.`,
	ClaimInsuranceMemberIdHelpSubtitle: 'Campo 1a',
	ClaimInsuranceMemberIdHelpTitle: 'ID do segurado',
	ClaimInsurancePayer: 'Pagador de seguro',
	ClaimManualPaymentAction: '<mark>Pagamento {paymentReference}</mark> para <b>{paymentAmount}</b> registrado',
	ClaimMd: 'Claim.MD',
	ClaimMiscAdditionalClaimInformation: 'Informações adicionais sobre a reivindicação',
	ClaimMiscAdditionalClaimInformationHelpContent:
		'Por favor, consulte as instruções atuais do pagador público ou privado em relação ao uso deste campo. Relate o qualificador apropriado, quando disponível, para a informação que está sendo inserida.Não insira um espaço, hífen ou outro separador entre o qualificador e a informação.',
	ClaimMiscAdditionalClaimInformationHelpSubtitle: 'Campo 19',
	ClaimMiscAdditionalClaimInformationHelpTitle: 'Informações adicionais sobre a reclamação',
	ClaimMiscClaimCodes: 'Códigos de reivindicação',
	ClaimMiscOriginalReferenceNumber: 'Número de referência original',
	ClaimMiscPatientsAccountNumber: 'Número da conta do paciente',
	ClaimMiscPatientsAccountNumberHelpContent:
		'O número da conta do paciente é o identificador atribuído pelo provedor.',
	ClaimMiscPatientsAccountNumberHelpSubtitle: 'Campo 26',
	ClaimMiscPatientsAccountNumberHelpTitle: 'Número da conta do paciente',
	ClaimMiscPriorAuthorizationNumber: 'Número de autorização prévia',
	ClaimMiscPriorAuthorizationNumberHelpContent:
		'O número de autorização prévia é o número atribuído ao pagador que autoriza o(s) serviço(s).',
	ClaimMiscPriorAuthorizationNumberHelpSubtitle: 'Campo 23',
	ClaimMiscPriorAuthorizationNumberHelpTitle: 'Número de autorização prévia',
	ClaimMiscResubmissionCode: 'Código de reenvio',
	ClaimMiscResubmissionCodeHelpContent:
		'Reenvio significa o código e o número de referência original atribuídos pelo pagador ou destinatário de destino para indicar uma reclamação ou encontro enviado anteriormente.',
	ClaimMiscResubmissionCodeHelpSubtitle: 'Campo 22',
	ClaimMiscResubmissionCodeHelpTitle: 'Reenvio e/ou Número de Referência Original',
	ClaimNumber: 'Número do pedido',
	ClaimNumberFormat: 'Reivindicação #{number}',
	ClaimOrderingProvider: 'Fornecedor de pedidos',
	ClaimOtherId: 'Outra identificação',
	ClaimOtherIdPlaceholder: 'Escolha uma opção',
	ClaimOtherIdQualifier: 'Outro qualificador de ID',
	ClaimOtherIdQualifierPlaceholder: 'Escolha o qualificador de ID',
	ClaimPlaceOfService: 'Local de serviço',
	ClaimPlaceOfServicePlaceholder: 'Adicionar POS',
	ClaimPolicyHolderRelationship: 'Relação com o segurado',
	ClaimPolicyInformation: 'Informações sobre a política',
	ClaimPolicyTelephone: 'Telefone (incluir código de área)',
	ClaimReceivedAction: '<mark>Reivindicação {claimNumber}</mark> recebida por <b>{name}</b>',
	ClaimReferringProvider: 'Provedor de referência',
	ClaimReferringProviderEmpty: 'Nenhum médico referenciador adicionado',
	ClaimReferringProviderHelpContent:
		'O nome inserido é o provedor de referência, provedor de solicitação ou provedor de supervisão que indicou, solicitou ou supervisionou o(s) serviço(s) ou suprimento(s) na reivindicação. O qualificador indica a função do provedor que está sendo reportado.',
	ClaimReferringProviderHelpSubtitle: 'Campo 17',
	ClaimReferringProviderHelpTitle: 'Nome do provedor ou fonte de referência',
	ClaimReferringProviderQualifier: 'Qualificador',
	ClaimReferringProviderQualifierPlaceholder: 'Escolha o qualificador',
	ClaimRejectedAction: '<mark>Reivindicação {claimNumber}</mark> foi rejeitada por <b>{name}</b>',
	ClaimRenderingProviderIdNumber: 'Número de identificação',
	ClaimRenderingProviderOrTeamMember: 'Provedor de renderização ou membro da equipe',
	ClaimRestoredAction: '<mark>Reivindicação {claimNumber}</mark> foi restaurada',
	ClaimServiceFacility: 'Instalação de serviço',
	ClaimServiceFacilityLocationHelpContent:
		'O nome e o endereço da instalação onde os serviços foram prestados identificam o local onde os serviços foram prestados.',
	ClaimServiceFacilityLocationHelpLabel: '32, 32a e 32b',
	ClaimServiceFacilityLocationHelpSubtitle: 'Campo 32, 32a e 32b',
	ClaimServiceFacilityLocationHelpTitle: 'Instalação de serviço',
	ClaimServiceFacilityPlaceholder: 'Escolha a unidade de serviço ou local',
	ClaimServiceLabChargesHelpContent: `Preencha este campo ao reivindicar serviços adquiridos fornecidos por uma entidade diferente do provedor de cobrança.
 Cada serviço adquirido deve ser relatado em uma solicitação separada, pois apenas uma cobrança pode ser inserida no formulário CMS1500.`,
	ClaimServiceLabChargesHelpSubtitle: 'Campo 20',
	ClaimServiceLabChargesHelpTitle: 'Taxas de laboratório externo',
	ClaimServiceLineServiceHelpContent:
		'“Procedimentos, Serviços ou Suprimentos” identificam os serviços e procedimentos médicos fornecidos ao paciente.',
	ClaimServiceLineServiceHelpSubtitle: 'Campo 24d',
	ClaimServiceLineServiceHelpTitle: 'Procedimentos, Serviços ou Suprimentos',
	ClaimServiceLinesEmptyError: 'Pelo menos uma linha de serviço é obrigatória',
	ClaimServiceSupplementaryInfoHelpContent: `Adicione uma descrição narrativa adicional dos serviços fornecidos usando qualificadores aplicáveis.
 Não insira espaço, hífen ou outro separador entre o qualificador e a informação.

 Para obter instruções completas sobre como adicionar informações suplementares, revise as instruções do formulário de solicitação do CMS 1500.`,
	ClaimServiceSupplementaryInfoHelpSubtitle: 'Campo 24',
	ClaimServiceSupplementaryInfoHelpTitle: 'Informações suplementares',
	ClaimSettingsBillingMethodTitle: 'Método de cobrança do cliente',
	ClaimSettingsClientSignatureDescription:
		'Tenho consentimento para divulgar informações médicas ou outras informações necessárias para processar reivindicações de seguro.',
	ClaimSettingsClientSignatureTitle: 'Assinatura do cliente em arquivo',
	ClaimSettingsConsentLabel: 'Consentimento necessário para processar reivindicações de seguro:',
	ClaimSettingsDescription:
		'Escolha o método de cobrança do cliente para garantir um processamento de pagamento tranquilo:',
	ClaimSettingsHasActivePoliciesAlertDescription:
		'{name} possui uma apólice de seguro ativa. Para ativar o faturamento do seguro, atualize o método de cobrança do cliente para Seguro.',
	ClaimSettingsInsuranceDescription: 'Custos reembolsados pelo seguro',
	ClaimSettingsInsuranceTitle: 'Seguro',
	ClaimSettingsNoPoliciesAlertDescription: 'Adicione uma apólice de seguro para habilitar reivindicações de seguro.',
	ClaimSettingsPolicyHolderSignatureDescription:
		'Tenho consentimento para receber pagamentos de seguro pelos serviços prestados.',
	ClaimSettingsPolicyHolderSignatureTitle: 'Assinatura do segurado em arquivo',
	ClaimSettingsSelfPayDescription: 'O cliente pagará pelos compromissos',
	ClaimSettingsSelfPayTitle: 'Pagamento próprio',
	ClaimSettingsTitle: 'Configurações de reivindicação',
	ClaimSexSelectorPlaceholder: 'Masculino / Feminino',
	ClaimStatusChangedAction: '<mark>Reivindicação {claimNumber}</mark> status atualizado',
	ClaimSubmittedAction:
		'<mark>Reivindicação {claimNumber}</mark> enviada para <b>{payerClearingHouse}</b> para <b>{payerNumber} {payerName}</b>',
	ClaimSubtitle: 'Reivindicação #{claimNumber}',
	ClaimSupervisingProvider: 'Provedor supervisor',
	ClaimSupplementaryInfo: 'Informações suplementares',
	ClaimSupplementaryInfoPlaceholder: 'Adicionar informações suplementares',
	ClaimTrashedAction: '<mark>Reivindicação {claimNumber}</mark> foi excluída',
	ClaimValidationFailure: 'Falha na validação da reclamação',
	ClaimsEmptyStateDescription: 'Nenhuma reivindicação foi encontrada.',
	ClainInsuranceTelephone: 'Telefone do seguro (incluir código de área)',
	Classic: 'Clássico',
	Clear: 'Claro',
	ClearAll: 'Limpar tudo',
	ClearSearchFilter: 'Claro',
	ClearingHouse: 'Clearing house',
	ClearingHouseClaimId: 'ID do Claim.MD',
	ClearingHouseGeneralError: '{message}',
	ClearingHouseReference: 'Referência da câmara de compensação',
	ClearingHouseUnavailableError:
		'O sistema de compensação está indisponível no momento. Por favor, tente novamente mais tarde.',
	ClickToUpload: 'Clique para carregar',
	Client: 'Cliente',
	ClientAddedNoteNotificationSubject:
		'{actorProfileName} adicionou {noteTitle, select, undefined { uma nota } other {{noteTitle}}}',
	ClientAndRelationshipSelectorPlaceholder: 'Escolha clientes e seus relacionamentos',
	ClientAndRelationshipSelectorTitle: 'Todos os clientes e seus relacionamentos',
	ClientAndRelationshipSelectorTitle1: 'Todos os relacionamentos de ‘{name}’',
	ClientAppCallsPageNoOptionsText:
		'Se você estiver aguardando uma videochamada, ela aparecerá aqui em breve. Se você estiver tendo algum problema, entre em contato com a pessoa que o iniciou.',
	ClientAppSubHeaderMyDocumentation: 'Minha documentação',
	ClientAppointment: 'Consulta do cliente',
	ClientAppointmentBookedNotificationSubject: '{actorProfileName} agendou {appointmentName}',
	ClientAppointmentsEmptyStateDescription: 'Nenhum compromisso foi encontrado',
	ClientAppointmentsEmptyStateTitle: 'Acompanhe os compromissos futuros e históricos de seus clientes e sua presença',
	ClientArchivedSuccessfulSnackbar: 'Arquivado com sucesso **{name}**',
	ClientBalance: 'Saldo do cliente',
	ClientBilling: 'Cobrança',
	ClientBillingAddPaymentMethodDescription:
		'Adicione e gerencie os métodos de pagamento do seu cliente para agilizar o processo de faturamento e cobrança.',
	ClientBillingAndPaymentDueDate: 'Data de vencimento',
	ClientBillingAndPaymentHistory: 'Histórico de faturamento e pagamento',
	ClientBillingAndPaymentInvoices: 'Faturas',
	ClientBillingAndPaymentIssueDate: 'Data de emissão',
	ClientBillingAndPaymentPrice: 'Preço',
	ClientBillingAndPaymentReceipt: 'Recibo',
	ClientBillingAndPaymentServices: 'Serviços',
	ClientBillingAndPaymentStatus: 'Status',
	ClientBulkStaffAssignedSuccessSnackbar: 'Time {count, plural, one {membro} other {membros}} atribuído!',
	ClientBulkStaffUnassignedSuccessSnackbar: 'Equipe com {count, plural, one {membro} other {membros}} não atribuída!',
	ClientBulkTagsAddedSuccessSnackbar: 'Etiquetas adicionadas!',
	ClientDuplicatesDeviewDescription:
		'Mescle vários registros de clientes em um para unificar todos os dados: notas, documentos, compromissos, faturas e conversas.',
	ClientDuplicatesPageMergeHeader: 'Escolha os dados que você gostaria de manter',
	ClientDuplicatesReviewHeader: 'Comparar registros duplicados em potencial para mesclagem',
	ClientEmailChangeWarningDescription:
		'Atualizar o e-mail do cliente removerá o acesso a qualquer documentação compartilhada e concederá acesso ao usuário com o novo e-mail',
	ClientFieldDateDescription: 'Formatar data',
	ClientFieldDateLabel: 'Data',
	ClientFieldDateRangeDescription: 'Um intervalo de datas',
	ClientFieldDateRangeLabel: 'Período',
	ClientFieldDateShowDateDescription: 'por exemplo, 29 anos',
	ClientFieldDateShowDateRangeDescription: 'por exemplo, 2 semanas',
	ClientFieldEmailDescription: 'Endereço de email',
	ClientFieldEmailLabel: 'E-mail',
	ClientFieldLabel: 'Legenda do campo',
	ClientFieldLinearScaleDescription: 'Opções de escala 1-10',
	ClientFieldLinearScaleLabel: 'Escala linear',
	ClientFieldLocationDescription: 'Endereço físico ou postal',
	ClientFieldLocationLabel: 'Localização',
	ClientFieldLongTextDescription: 'Área de texto longo',
	ClientFieldLongTextLabel: 'Parágrafo',
	ClientFieldMultipleChoiceDropdownDescription: 'Escolha várias opções da lista',
	ClientFieldMultipleChoiceDropdownLabel: 'Menu suspenso de múltipla escolha',
	ClientFieldPhoneNumberDescription: 'Número de telefone',
	ClientFieldPhoneNumberLabel: 'Telefone',
	ClientFieldPlaceholder: 'Escolha um tipo de campo do cliente',
	ClientFieldSingleChoiceDropdownDescription: 'Escolha apenas uma opção da lista',
	ClientFieldSingleChoiceDropdownLabel: 'Menu suspenso de escolha única',
	ClientFieldTextDescription: 'Campo de entrada de texto',
	ClientFieldTextLabel: 'Texto',
	ClientFieldYesOrNoDescription: 'Escolha entre opções sim ou não',
	ClientFieldYesOrNoLabel: 'Sim | Não',
	ClientFileFormAccessLevelDescription:
		'Você e a equipe sempre terão acesso aos arquivos enviados. Você pode optar por compartilhar este arquivo com o cliente e/ou seus relacionamentos',
	ClientFileSavedSuccessSnackbar: 'Arquivo salvo!',
	ClientFilesPageEmptyStateText: 'Nenhum arquivo enviado',
	ClientFilesPageUploadFileButton: 'Fazer upload de arquivos',
	ClientHeaderBilling: 'Faturamento',
	ClientHeaderBillingAndReceipts: 'Cobrança ',
	ClientHeaderDocumentation: 'Documentação',
	ClientHeaderDocuments: 'Documentos',
	ClientHeaderFile: 'Documento',
	ClientHeaderHistory: 'Histórico médico',
	ClientHeaderInbox: 'Caixa de entrada',
	ClientHeaderNote: 'Observação',
	ClientHeaderOverview: 'Visão geral',
	ClientHeaderProfile: 'Pessoal',
	ClientHeaderRelationship: 'Relação',
	ClientHeaderRelationships: 'Relacionamentos',
	ClientId: 'ID do cliente',
	ClientImportProcessingDescription:
		'Arquivo ainda em processamento. Notificaremos você quando isso estiver concluído.',
	ClientImportReadyForMappingDescription:
		'Finalizamos o pré-processamento do seu arquivo. Gostaria de mapear as colunas para concluir esta importação?',
	ClientImportReadyForMappingNotificationSubject:
		'Importação pré-processamento do cliente está completa. O arquivo está agora pronto para mapeamento.',
	ClientInAppMessaging: 'Mensagens no aplicativo do cliente',
	ClientInfoAddField: 'Adicione outro campo',
	ClientInfoAddRow: 'Adicionar linha',
	ClientInfoAlertMessage: 'Qualquer informação preenchida nesta seção preencherá o registro do cliente.',
	ClientInfoFormPrimaryText: 'Informação ao cliente',
	ClientInfoFormSecondaryText: 'Reúna detalhes de contato',
	ClientInfoPlaceholder: `Nome do cliente, endereço de e-mail, número de telefone
 Endereço físico,
 Data de nascimento`,
	ClientInformation: 'Informações do cliente',
	ClientInsuranceTabLabel: 'Seguro',
	ClientIntakeFormsNotSupported: `Atualmente, os modelos de formulário não são suportados por meio de entradas de clientes.
 Crie e compartilhe-os como notas do cliente.`,
	ClientIntakeModalDescription:
		'Um e-mail de admissão será enviado ao seu cliente solicitando que ele preencha seu perfil, carregue documentos médicos ou de referência relevantes. Eles receberão acesso ao Portal do Cliente.',
	ClientIntakeModalTitle: 'Enviar admissão para {name}',
	ClientIntakeSkipPasswordSuccessSnackbar: 'Sucesso! Sua ingestão foi salva.',
	ClientIntakeSuccessSnackbar: 'Sucesso! Sua entrada foi salva e um e-mail de confirmação enviado.',
	ClientIsChargedProcessingFee: 'Seus clientes pagarão a taxa de processamento',
	ClientListCreateButton: 'Novo cliente',
	ClientListEmptyState: 'Nenhum cliente adicionado',
	ClientListPageItemArchive: 'Remover cliente',
	ClientListPageItemRemoveAccess: 'Remover meu acesso',
	ClientLocalizationPanelDescription: 'O idioma e o fuso horário preferidos do cliente.',
	ClientLocalizationPanelTitle: 'Idioma e fuso horário',
	ClientManagementAndEHR: 'Gestão de clientes ',
	ClientMergeResultSummaryBanner:
		'A fusão de registros consolida todos os dados do cliente, incluindo notas, documentos, agendamentos, faturas e conversas. Verifique a precisão antes de continuar.',
	ClientMergeResultSummaryTitle: 'Resumo da Mesclagem',
	ClientModalTitle: 'Novo cliente',
	ClientMustHaveEmaillAccessErrorText: 'Clientes/Contatos sem e-mails',
	ClientMustHavePortalAccessErrorText: 'Clientes/Contatos serão obrigados a se inscrever',
	ClientMustHaveZoomAppConnectedErrorText: 'Conecte o Zoom em Configurações&gt; Aplicativos conectados',
	ClientNameFormat: 'Formato do nome do cliente',
	ClientNotFormAccessLevel: 'Visível por:',
	ClientNotFormAccessLevelDescription:
		'Você e a equipe sempre terão acesso às notas que publicarem. Você pode optar por compartilhar esta nota com o cliente e/ou seus relacionamentos',
	ClientNotRegistered: 'Não registrado',
	ClientNoteFormAddFileButton: 'Anexar arquivos',
	ClientNoteFormChooseAClient: 'Escolha um cliente/contato para continuar',
	ClientNoteFormContent: 'Contente',
	ClientNoteItemDeleteConfirmationModalDescription:
		'Depois de excluída, você não poderá recuperar esta nota novamente.',
	ClientNotePublishedAndLockSuccessSnackbar: 'Nota publicada e bloqueada.',
	ClientNotePublishedSuccessSnackbar: 'Nota publicada!',
	ClientNotes: 'Notas do cliente',
	ClientNotesEmptyStateText: 'Para adicionar notas, acesse o perfil de um cliente e clique na guia Notas.',
	ClientOnboardingChoosePasswordTitle1: 'Quase pronto!',
	ClientOnboardingChoosePasswordTitle2: 'Escolha uma senha',
	ClientOnboardingCompleteIntake: 'Ingestão completa',
	ClientOnboardingConfirmationScreenText:
		'Você forneceu todas as informações que {providerName} precisa.	Confirme seu endereço de e-mail para iniciar sua integração. Se você não recebê-lo imediatamente, verifique sua caixa de spam.',
	ClientOnboardingConfirmationScreenTitle: 'Ótimo! Verifique sua caixa de entrada.',
	ClientOnboardingDashboardButton: 'Ir para a dashboard',
	ClientOnboardingHealthRecordsDesc1:
		'Deseja compartilhar alguma carta de referência, documentos com {providerName}?',
	ClientOnboardingHealthRecordsDescription: 'Adicionar descrição (opcional)',
	ClientOnboardingHealthRecordsTitle: 'Documentação',
	ClientOnboardingPasswordRequirements: 'Requisitos',
	ClientOnboardingPasswordRequirementsConditions1: 'Mínimo 9 caracteres necessários',
	ClientOnboardingProviderIntroSignupButton: 'Inscreva-se para mim',
	ClientOnboardingProviderIntroSignupFamilyButton: 'Inscreva-se para um membro da família',
	ClientOnboardingProviderIntroTitle: '{name} convidou você para se juntar à plataforma Carepatron',
	ClientOnboardingRegistrationInstructions: 'Insira seus dados pessoais abaixo.',
	ClientOnboardingRegistrationTitle: 'Primeiro precisaremos de alguns dados pessoais',
	ClientOnboardingStepFormsAndAgreements: 'Formulários e Acordos',
	ClientOnboardingStepFormsAndAgreementsDesc1:
		'Por favor, preencha os seguintes formulários para o processo de entrada do {providerName}',
	ClientOnboardingStepHealthDetails: 'Detalhes de saúde',
	ClientOnboardingStepPassword: 'Senha',
	ClientOnboardingStepYourDetails: 'Seus detalhes',
	ClientPaymentMethodDescription:
		'Salve uma forma de pagamento em seu perfil para tornar seu próximo agendamento e faturamento mais rápido e seguro.',
	ClientPortal: 'Portal do cliente',
	ClientPortalDashboardEmptyDescription: 'Seu histórico de compromissos e presença aparecerão aqui.',
	ClientPortalDashboardEmptyTitle:
		'Acompanhe todos os compromissos futuros, solicitados e anteriores, juntamente com sua presença',
	ClientPreferredNotificationPanelDescription:
		'Gerencie o método preferido do seu cliente para receber atualizações e notificações via:',
	ClientPreferredNotificationPanelTitle: 'Método de notificação preferido',
	ClientProcessingFee: 'Pagamento inclui ({currencyCode}) {amount} de taxa de processamento',
	ClientProfileAddress: 'Endereço',
	ClientProfileDOB: 'Data de nascimento',
	ClientProfileEmailHelperText: 'Adicionar um e-mail concede acesso ao portal',
	ClientProfileEmailHelperTextMoreInfo:
		'Conceder ao cliente acesso ao portal permite que os membros da equipe compartilhem notas, arquivos e outras documentações',
	ClientProfileId: 'ID',
	ClientProfileIdentificationNumber: 'Número de identificação',
	ClientRelationshipsAddClientOwnerButton: 'Convide o cliente',
	ClientRelationshipsAddFamilyButton: 'Convidar membro da família',
	ClientRelationshipsAddStaffButton: 'Adicionar acesso de equipe',
	ClientRelationshipsEmptyStateText: 'Nenhum relacionamento adicionado',
	ClientRemovedSuccessSnackbar: 'Cliente removido com sucesso.',
	ClientResponsibility: 'Responsabilidade do cliente',
	ClientSavedSuccessSnackbar: 'Cliente salvo com sucesso.',
	ClientTableClientName: 'Nome do cliente',
	ClientTablePhone: 'Telefone',
	ClientTableStatus: 'Status',
	ClientUnarchivedSuccessfulSnackbar: 'Desarquivado com sucesso **{name}**',
	ClientUpdatedNoteNotificationSubject:
		'{actorProfileName} editou {noteTitle, select, undefined { uma nota } other {{noteTitle}}}',
	ClientView: 'Visualização do cliente',
	Clients: 'Clientes',
	ClientsTable: 'Tabela de Clientes',
	ClinicalFormat: 'Formato clínico',
	ClinicalPsychologist: 'Psicólogo clínico',
	Close: 'Fechar',
	CloseImportClientsModal: 'Tem certeza de que deseja cancelar a importação de clientes?',
	CloseReactions: 'Reações próximas',
	Closed: 'Fechado',
	Coaching: 'Treinamento',
	Code: 'Código',
	CodeErrorMessage: 'O código é obrigatório',
	CodePlaceholder: 'Código',
	Coinsurance: 'Co-seguro',
	Collection: 'Coleção',
	CollectionName: 'Nome da coleção',
	Collections: 'Coleções',
	ColorAppointmentsBy: 'Marcações de cores por',
	ColorTheme: 'Tema de cores',
	ColourCalendarBy: 'Calendário de cores por',
	ComingSoon: 'Em breve',
	Community: 'Comunidade',
	CommunityHealthLead: 'Líder de Saúde Comunitária',
	CommunityHealthWorker: 'Agente Comunitário de Saúde',
	CommunityTemplatesSectionDescription: 'Criado pela comunidade Carepatron',
	CommunityTemplatesSectionTitle: 'Comunidade',
	CommunityUser: 'Usuário da Comunidade',
	Complete: 'Completo',
	CompleteAndLock: 'Completo e bloqueado',
	CompleteSetup: 'Configuração completa',
	CompleteSetupSuccessDescription: 'Você completou algumas etapas importantes para dominar o Carepatron.',
	CompleteSetupSuccessDescription2:
		'Desbloqueie mais maneiras de ajudar a otimizar sua prática e apoiar seus clientes.',
	CompleteSetupSuccessTitle: 'Sucesso! Você está incrível!',
	CompleteStripeSetup: 'Configuração completa do Stripe',
	Completed: 'Concluído',
	ComposeSms: 'Compor SMS',
	ComputerSystemsAnalyst: 'Analista de Sistemas de Computação',
	Confirm: 'confirme',
	ConfirmDeleteAccountDescription:
		'Você está prestes a excluir sua conta. Esta ação não pode ser desfeita. Se você deseja prosseguir, confirme abaixo.',
	ConfirmDeleteActionDescription: 'Tem certeza de que deseja excluir esta ação? Isso não pode ser desfeito',
	ConfirmDeleteAutomationDescription:
		'Tem certeza de que deseja excluir esta automação? Esta ação não pode ser desfeita.',
	ConfirmDeleteScheduleDescription:
		'Excluindo o cronograma **{scheduleName}** irá removê-lo de seus cronogramas e pode alterar o seu serviço online disponível. Esta ação não pode ser desfeita.',
	ConfirmDraftResponseContinue: 'Continuar com a resposta',
	ConfirmDraftResponseDescription:
		'Se você fechar esta página, sua resposta permanecerá como rascunho. Você pode voltar e continuar a qualquer momento.',
	ConfirmDraftResponseSubmitResponse: 'Enviar resposta',
	ConfirmDraftResponseTitle: 'Sua resposta não foi enviada',
	ConfirmIfUserIsClientDescription: `O formulário de inscrição que você preencheu é para prestadores (ou seja, equipes/organizações de saúde).
 Se isso for um erro, você pode escolher &quot;Continuar como cliente&quot; e nós configuraremos seu portal de cliente`,
	ConfirmIfUserIsClientNoButton: 'Cadastre-se como provedor',
	ConfirmIfUserIsClientTitle: 'Parece que você é um cliente',
	ConfirmIfUserIsClientYesButton: 'Continuar como cliente',
	ConfirmKeepSeparate: 'Confirmar manter separado',
	ConfirmMerge: 'Confirmar mesclagem',
	ConfirmPassword: 'Confirme sua senha',
	ConfirmRevertClaim: 'Sim, reverter status',
	ConfirmSignupAccessCode: 'Código de confirmação',
	ConfirmSignupButtom: 'confirme',
	ConfirmSignupDescription:
		'Por favor, insira seu endereço de e-mail e o código de confirmação que acabamos de enviar.',
	ConfirmSignupSubTitle: 'Verifique a pasta Spam - se o e-mail não chegou',
	ConfirmSignupSuccessSnackbar: 'Ótimo, confirmamos sua conta! Agora você pode fazer login usando seu e-mail e senha',
	ConfirmSignupTitle: 'Confirmar conta',
	ConfirmSignupUsername: 'E-mail',
	ConfirmSubscriptionUpdate: 'Confirmar assinatura {price} {isMonthly, select, true {por mês} other {por ano}}',
	ConfirmationModalBulkDeleteClientsDescriptionId:
		'Depois que os clientes forem excluídos, você não poderá mais acessar suas informações.',
	ConfirmationModalBulkDeleteClientsTitleId: 'Excluir {count, plural, one {# cliente} other {# clientes}}?',
	ConfirmationModalBulkDeleteContactsDescriptionId:
		'Depois que os contatos forem excluídos, você não poderá mais acessar suas informações.',
	ConfirmationModalBulkDeleteContactsTitleId: 'Excluir {count, plural, one {# contato} other {# contatos}}?',
	ConfirmationModalBulkDeleteMembersDescriptionId:
		'Esta é uma ação permanente. Depois que os membros da equipe forem excluídos, você não poderá mais acessar suas informações.',
	ConfirmationModalBulkDeleteMembersTitleId:
		'Excluir {count, plural, one {# membro da equipe} other {# membros da equipe}}?',
	ConfirmationModalCloseOnGoingTranscription:
		'Fechar esta nota encerrará quaisquer transcrições em andamento. Tem certeza de que deseja prosseguir?',
	ConfirmationModalDeleteClientField:
		'Esta é uma ação permanente. Depois que o campo for excluído, ele não estará mais acessível aos clientes restantes.',
	ConfirmationModalDeleteSectionMessage:
		'Depois de excluídas, todas as perguntas desta seção serão removidas. Essa ação não pode ser desfeita.',
	ConfirmationModalDeleteService:
		'Esta é uma ação permanente. Depois que o serviço for excluído, ele não estará mais acessível em seu espaço de trabalho.',
	ConfirmationModalDeleteServiceGroup:
		'A exclusão de uma coleção removerá todos os serviços do grupo e retornará à sua lista de serviços. Esta ação não pode ser desfeita.',
	ConfirmationModalDeleteTranscript: 'Tem certeza de que deseja excluir a transcrição?',
	ConfirmationModalDescriptionDeleteClient:
		'Depois que o cliente for excluído, você não poderá mais acessar as informações do cliente.',
	ConfirmationModalDescriptionRemoveMyAccessFromClient:
		'Depois de remover seu acesso, você não poderá mais visualizar as informações do cliente.',
	ConfirmationModalDescriptionRemoveRelationshipToClient:
		'O perfil deles não será excluído, apenas removido conforme relacionamento deste cliente.',
	ConfirmationModalDescriptionRemoveStaff: 'Tem certeza de que deseja remover esta pessoa do provedor?',
	ConfirmationModalEndSession: 'Tem certeza de que deseja encerrar a sessão?',
	ConfirmationModalTitle: 'Tem certeza?',
	Confirmed: 'Confirmado',
	ConflictTimezoneWarningMessage: 'Podem ocorrer conflitos devido a vários fusos horários',
	Connect: 'Conectar',
	ConnectExistingClientOrContact: 'Criar novo cliente/contato',
	ConnectInboxGoogleDescription: 'Adicione uma conta do Gmail ou lista de grupos do Google',
	ConnectInboxMicrosoftDescription: 'Adicione uma conta Outlook, Office365 ou Exchange',
	ConnectInboxModalDescription:
		'Conecte seus aplicativos para enviar, receber e rastrear todas as suas comunicações em um local centralizado.',
	ConnectInboxModalExistingDescription:
		'Use uma conexão existente nas configurações dos seus aplicativos conectados para agilizar o processo de configuração.',
	ConnectInboxModalExistingTitle: 'Aplicativo conectado existente no Carepatron',
	ConnectInboxModalTitle: 'Conectar caixa de entrada',
	ConnectToStripe: 'Conecte-se ao Stripe',
	ConnectZoom: 'Conectar zoom',
	ConnectZoomModalDescription: 'Permita que o Carepatron gerencie videochamadas para seus compromissos.',
	ConnectedAppDisconnectedNotificationSubject: 'Perdemos a conexão com a conta {account}. Por favor, reconecte-se.',
	ConnectedAppSyncDescription:
		'Gerencie aplicativos conectados para criar eventos em calendários de terceiros diretamente do Carepatron.',
	ConnectedApps: 'Aplicativos conectados',
	ConnectedAppsGMailDescription: 'Adicionar contas do Gmail ou lista de grupos do Google',
	ConnectedAppsGoogleCalendarDescription: 'Adicionar contas de calendário ou lista de grupos do Google',
	ConnectedAppsGoogleDescription: 'Adicione sua conta do Gmail e sincronize os calendários do Google',
	ConnectedAppsMicrosoftDescription: 'Adicione uma conta Outlook, Office365 ou Exchange',
	ConnectedCalendars: 'Calendários conectados',
	ConsentDocumentation: 'Formulários e acordos',
	ConsentDocumentationPublicTemplateError:
		'Por motivos de segurança, você só pode escolher modelos da sua equipe (não públicos).',
	ConstructionWorker: 'Trabalhador da construção',
	Consultant: 'Consultor',
	Contact: 'Contato',
	ContactAccessTypeHelperText: 'Permite que os administradores da família atualizem informações',
	ContactAccessTypeHelperTextMoreInfo: 'Isso permitirá que você compartilhe notas/documentos sobre {clientFirstName}',
	ContactAddressLabelBilling: 'Cobrança',
	ContactAddressLabelHome: 'Lar',
	ContactAddressLabelOthers: 'Outros',
	ContactAddressLabelWork: 'Trabalhar',
	ContactChangeConfirmation:
		'Alterar o contato da fatura removerá todos os itens relacionados a <mark>{contactName}</mark>',
	ContactDetails: 'Detalhes do contato',
	ContactEmailLabelOthers: 'Outros',
	ContactEmailLabelPersonal: 'Pessoal',
	ContactEmailLabelSchool: 'Escola',
	ContactEmailLabelWork: 'Trabalhar',
	ContactInformation: 'Informações de contato',
	ContactInformationText: 'Informações de contato',
	ContactListCreateButton: 'Novo contato',
	ContactName: 'Nome do contato',
	ContactPhoneLabelHome: 'Lar',
	ContactPhoneLabelMobile: 'Móvel',
	ContactPhoneLabelSchool: 'Escola',
	ContactPhoneLabelWork: 'Trabalhar',
	ContactRelationship: 'Relacionamento de contato',
	ContactRelationshipFormAccessType: 'Conceder acesso às informações compartilhadas',
	ContactRelationshipGrantAccessInfo: 'Isso permitirá que você compartilhe notas e documentos',
	ContactSupport: 'Entre em contato com o suporte',
	Contacts: 'Contatos',
	ContainerIdNotSet: 'ID do contêiner não definido',
	Contemporary: 'Contemporâneo',
	Continue: 'Continuar',
	ContinueDictating: 'Continue ditando',
	ContinueEditing: 'Continuar editando',
	ContinueImport: 'Continuar importação',
	ContinueTranscription: 'Continuar transcrição',
	ContinueWithApple: 'Continuar com a Apple',
	ContinueWithGoogle: 'Continuar com o Google',
	Conversation: 'Conversação',
	Copay: 'Co-pagamento',
	CopayOrCoinsurance: 'Copagamento ou cosseguro',
	Copayment: 'Co-pagamento',
	CopiedToClipboard: 'Copiado para a área de transferência',
	Copy: 'cópia de',
	CopyAddressSuccessSnackbar: 'Endereço copiado para a área de transferência',
	CopyCode: 'Copiar código',
	CopyCodeToClipboardSuccess: 'Código copiado para a área de transferência',
	CopyEmailAddressSuccessSnackbar: 'Endereço de email copiado para a área de transferência',
	CopyLink: 'Link de cópia',
	CopyLinkForCall: 'Copie este link para compartilhar esta chamada:',
	CopyLinkSuccessSnackbar: 'Link copiado para a área de transferência',
	CopyMeetingLink: 'Copiar link da reunião',
	CopyPaymentLink: 'Copiar link de pagamento',
	CopyPhoneNumberSuccessSnackbar: 'Número de telefone copiado para a área de transferência',
	CopyTemplateLink: 'Copiar link para modelo',
	CopyTemplateLinkSuccess: 'Link copiado para a área de transferência',
	CopyToClipboardError: 'Não foi possível copiar para a área de transferência. Por favor, tente novamente.',
	CopyToTeamTemplates: 'Copiar para modelos de equipe',
	CopyToWorkspace: 'Copiar para o espaço de trabalho',
	Cosmetologist: 'Cosmetologista',
	Cost: 'Custo',
	CostErrorMessage: 'O custo é obrigatório',
	Counseling: 'Aconselhamento',
	Counselor: 'Conselheiro',
	Counselors: 'Conselheiros',
	CountInvoicesAdded: '{count, plural, one {# Fatura adicionada} other {# Faturas adicionadas}}',
	CountNotesAdded: '{count, plural, one {# Nota adicionada} other {# Notas adicionadas}}',
	CountSelected: '{count} selecionados',
	CountTimes: '{count} vezes',
	Country: 'País',
	Cousin: 'Primo',
	CoverageType: 'Tipo de cobertura',
	Covered: 'Abordado',
	Create: 'Criar',
	CreateANewClient: 'Criar um novo cliente',
	CreateAccount: 'Criar uma conta',
	CreateAndSignNotes: 'Crie e assine notas com clientes',
	CreateAvailabilityScheduleFailure: 'Falha ao criar nova programação de disponibilidade',
	CreateAvailabilityScheduleSuccess: 'Nova programação de disponibilidade criada com sucesso',
	CreateBillingItems: 'Criar itens de faturamento',
	CreateCallFormButton: 'Iniciar chamada',
	CreateCallFormInviteOnly: 'Somente convite',
	CreateCallFormInviteOnlyMoreInfo:
		'Somente pessoas convidadas para esta chamada podem participar. Para compartilhar esta chamada com outras pessoas, basta desmarcar esta opção e copiar/colar o link na próxima página',
	CreateCallFormRecipients: 'Destinatários',
	CreateCallFormRegion: 'Região de hospedagem',
	CreateCallModalAddClientContactSelectorLabel: 'Contatos do cliente',
	CreateCallModalAddClientContactSelectorPlaceholder: 'Pesquise por nome do cliente',
	CreateCallModalAddStaffSelectorLabel: 'Membros da equipe (opcional)',
	CreateCallModalAddStaffSelectorPlaceholder: 'Pesquise pelo nome da equipe',
	CreateCallModalDescription:
		'Inicie uma chamada e convide membros da equipe e/ou contatos. Alternativamente, você pode desmarcar a caixa &quot;Privada&quot; para tornar esta chamada compartilhável com qualquer pessoa com Carepatron',
	CreateCallModalTitle: 'Iniciar uma chamada',
	CreateCallModalTitleLabel: 'Título (opcional)',
	CreateCallNoPersonIdToolTip: 'Somente contatos/clientes com acesso ao portal podem participar de chamadas',
	CreateClaim: 'Criar reclamação',
	CreateClaimCompletedMessage: 'Sua reivindicação foi criada.',
	CreateClientModalTitle: 'Novo cliente',
	CreateContactModalTitle: 'Novo contato',
	CreateContactRelationshipButton: 'Adicionar relacionamento',
	CreateContactSelectorDefaultOption: '  Criar contato',
	CreateContactWithRelationshipFormAccessType: 'Conceder acesso a informações compartilhadas ',
	CreateDocumentDnDPrompt: 'Arraste e solte para fazer upload de arquivos',
	CreateDocumentSizeLimit: 'Limite de tamanho por arquivo {size}MB. {total} arquivos no total.',
	CreateFreeAccount: 'Crie uma conta gratuita',
	CreateInvoice: 'Criar recibo',
	CreateLink: 'Criar link',
	CreateNew: 'Crie um novo',
	CreateNewAppointment: 'Criar novo compromisso',
	CreateNewClaim: 'Criar uma nova reivindicação',
	CreateNewClaimForAClient: 'Criar novo pedido para um cliente.',
	CreateNewClient: 'Criar novo cliente',
	CreateNewConnection: 'Nova conexão',
	CreateNewContact: 'Criar novo contato',
	CreateNewField: 'Criar novo campo',
	CreateNewLocation: 'Nova localização',
	CreateNewService: 'Criar novo serviço',
	CreateNewServiceGroupFailure: 'Falha ao criar nova coleção',
	CreateNewServiceGroupMenu: 'Nova coleção',
	CreateNewServiceGroupSuccess: 'Nova coleção criada com sucesso',
	CreateNewServiceMenu: 'Novo serviço',
	CreateNewTeamMember: 'Criar novo membro da equipe',
	CreateNewTemplate: 'Novo modelo',
	CreateNote: 'Criar nota',
	CreateSuperbillReceipt: 'Nova superfatura',
	CreateSuperbillReceiptSuccess: 'Recibo Superbill criado com sucesso',
	CreateTemplateFolderSuccessMessage: 'Criado com sucesso {folderTitle}',
	Created: 'Criado',
	CreatedAt: 'Criado {timestamp}',
	Credit: 'Crédito',
	CreditAdded: 'Crédito aplicado',
	CreditAdjustment: 'Ajuste de crédito',
	CreditAdjustmentReasonHelperText: 'Esta é uma nota interna e não ficará visível para o seu cliente.',
	CreditAdjustmentReasonPlaceholder: 'Adicionar um motivo de ajuste pode ajudar na análise de transações faturáveis',
	CreditAmount: '{amount} NC',
	CreditBalance: 'Saldo de crédito',
	CreditCard: 'Cartão de crédito',
	CreditCardExpire: 'Expira {exp_month}/{exp_year}',
	CreditCardNumber: 'Número do cartão de crédito',
	CreditDebitCard: 'Cartão',
	CreditIssued: 'Crédito emitido',
	CreditsUsed: 'Créditos usados',
	Crop: 'Cortar',
	Currency: 'Moeda',
	CurrentCredit: 'Crédito atual',
	CurrentEventTime: 'Hora atual do evento',
	CurrentPlan: 'Plano atual',
	Custom: 'Personalizado',
	CustomRange: 'Intervalo personalizado',
	CustomRate: 'Taxa personalizada',
	CustomRecurrence: 'Recorrência personalizada',
	CustomServiceAvailability: 'Serviço disponível',
	CustomerBalance: 'Saldo do cliente',
	CustomerName: 'Nome do cliente',
	CustomerNameIsRequired: 'O nome do cliente é obrigatório',
	CustomerServiceRepresentative: 'Representante de atendimento ao cliente',
	CustomiseAppointments: 'Personalize compromissos',
	CustomiseBookingLink: 'Personalize as opções de reserva',
	CustomiseBookingLinkServicesInfo: 'Os clientes só podem escolher serviços reserváveis',
	CustomiseBookingLinkServicesLabel: 'Serviços',
	CustomiseClientRecordsAndWorkspace: 'Personalize seus registros de clientes e espaço de trabalho',
	CustomiseClientSettings: 'Personalize as configurações do cliente',
	Customize: 'Customizar',
	CustomizeAppearance: 'Personalize a aparência',
	CustomizeAppearanceDesc:
		'Personalize a aparência da sua reserva online para combinar com sua marca e otimizar a forma como seus serviços são exibidos aos clientes.',
	CustomizeClientFields: 'Personalize os campos do cliente',
	CustomizeInvoiceTemplate: 'Personalizar modelo de fatura',
	CustomizeInvoiceTemplateDescription: 'Crie facilmente faturas profissionais que reflitam sua marca.',
	DXCodePlaceholder: '1.',
	DXErrorMessage: 'DX é obrigatório',
	Daily: 'Diário',
	DanceTherapist: 'Terapeuta de Dança',
	DangerZone: 'Zona de perigo',
	Dashboard: 'Painel',
	Date: 'Data',
	DateAndTime: 'Data ',
	DateDue: 'Data de vencimento',
	DateErrorMessage: 'A data é obrigatória',
	DateFormPrimaryText: 'Data',
	DateFormSecondaryText: 'Escolha a partir de um selecionador de data',
	DateIssued: 'Data de emissão',
	DateOfPayment: 'Dia do pagamento',
	DateOfService: 'Data de serviço',
	DateOverride: 'Substituição de data',
	DateOverrideColor: 'Cor de substituição de data',
	DateOverrideInfo:
		'As substituições de data permitem que os profissionais ajustem manualmente sua disponibilidade para datas específicas, substituindo as programações regulares.',
	DateOverrideInfoBanner:
		'Apenas os serviços especificados para esta substituição de data podem ser reservados nestes intervalos de tempo; nenhuma outra reserva on-line é permitida.',
	DateOverrides: 'Substituições de data',
	DatePickerFormPrimaryText: 'Data',
	DatePickerFormSecondaryText: 'Escolha uma data',
	DateRange: 'Período',
	DateRangeFormPrimaryText: 'Período',
	DateRangeFormSecondaryText: 'Escolha um período',
	DateReceived: 'Data recebida',
	DateSpecificHours: 'Horário específico da data',
	DateSpecificHoursDescription:
		'Adicione datas quando sua disponibilidade mudar em relação ao horário programado ou para oferecer um serviço em uma data específica.',
	DateUploaded: 'Enviado {date, date, medium}',
	Dates: 'datas',
	Daughter: 'Filha',
	Day: 'Dia',
	DayPlural: '{count, plural, one {dia} other {dias}}',
	Days: 'Dias',
	DaysPlural: '{age, plural, one {# dia} other {# dias}}',
	DeFacto: 'De facto',
	Deactivated: 'Desativado',
	Debit: 'Débito',
	DecreaseIndent: 'Recuar devagar',
	Deductibles: 'Franquias',
	Default: 'Padrão',
	DefaultBillingProfile: 'Perfil de cobrança padrão',
	DefaultDescription: 'Descrição padrão',
	DefaultEndOfLine: 'Não há mais itens',
	DefaultInPerson: 'Compromissos de clientes',
	DefaultInvoiceTitle: 'Título padrão',
	DefaultNotificationSubject: 'Você recebeu uma nova notificação para {notificationType}',
	DefaultPaymentMethod: 'Metodo de pagamento padrão',
	DefaultService: 'Serviço padrão',
	DefaultValue: 'Padrão',
	DefaultVideo: 'E-mail de agendamento de vídeo do cliente',
	DefinedTemplateType: '{invoiceTemplate} modelo',
	Delete: 'Excluir',
	DeleteAccountButton: 'Excluir conta',
	DeleteAccountDescription: 'Exclua sua conta da plataforma',
	DeleteAccountPanelInfoAlert:
		'Você deve excluir seus workspaces antes de excluir seu perfil. Para prosseguir, alterne para um workspace e selecione Configurações > Configurações do Workspace.',
	DeleteAccountTitle: 'Excluir conta',
	DeleteAppointment: 'Excluir compromisso',
	DeleteAppointmentDescription:
		'Tem certeza de que deseja excluir este compromisso? Você pode restaurá-lo mais tarde.',
	DeleteAvailabilityScheduleFailure: 'Falha ao excluir a programação de disponibilidade',
	DeleteAvailabilityScheduleSuccess: 'Agenda de disponibilidade excluída com sucesso',
	DeleteBillable: 'Excluir faturável',
	DeleteBillableConfirmationMessage:
		'Tem certeza de que deseja excluir este faturável? Esta ação não pode ser desfeita.',
	DeleteBillingProfileConfirmationMessage: 'Isso removerá permanentemente o perfil de cobrança.',
	DeleteCardConfirmation:
		'Esta é uma ação permanente. Depois que o cartão for excluído, você não poderá mais acessá-lo.',
	DeleteCategory: 'Excluir categoria (isso não é permanente, a menos que as alterações sejam salvas)',
	DeleteClientEventConfirmationDescription: 'Isso será removido permanentemente.',
	DeleteClients: 'Excluir clientes',
	DeleteCollection: 'Excluir coleção',
	DeleteColumn: 'Excluir coluna',
	DeleteConversationConfirmationDescription: 'Exclua esta conversa para sempre. Esta ação não pode ser desfeita.',
	DeleteConversationConfirmationTitle: 'Excluir conversa para sempre',
	DeleteExternalEventDescription: 'Tem certeza de que deseja excluir este agendamento?',
	DeleteFileConfirmationModalPrompt: 'Depois de excluído, você não poderá recuperar este arquivo novamente.',
	DeleteFolder: 'Excluir pasta',
	DeleteFolderConfirmationMessage:
		'Tem certeza de que deseja excluir esta pasta {name}? Todos os itens dentro desta pasta também serão excluídos. Você poderá restaurar isso mais tarde.',
	DeleteForever: 'Apagar para sempre',
	DeleteInsurancePayerConfirmationMessage:
		'Remover {payer} irá excluí-lo da sua lista de pagadores de seguro. Esta ação é permanente e não pode ser restaurada.',
	DeleteInsurancePayerFailure: 'Falha ao excluir o pagador do seguro',
	DeleteInsurancePolicyConfirmationMessage: 'Isso removerá permanentemente a apólice de seguro.',
	DeleteInvoiceConfirmationDescription:
		'Essa ação não pode ser desfeita. Isso excluirá permanentemente a fatura e todos os pagamentos associados a ela.',
	DeleteLocationConfirmation:
		'Excluir um local é uma ação permanente. Depois de excluí-lo, você não terá mais acesso a ele. Essa ação não pode ser desfeita.',
	DeletePayer: 'Excluir pagador',
	DeletePracticeWorkspace: 'Excluir espaço de trabalho de prática',
	DeletePracticeWorkspaceDescription: 'Excluir permanentemente este espaço de trabalho prático',
	DeletePracticeWorkspaceFailedSnackbar: 'Falha ao excluir o espaço de trabalho',
	DeletePracticeWorkspaceModalCancelButton: 'Sim, cancelar minha assinatura',
	DeletePracticeWorkspaceModalCancelSubscriptionDescription:
		'Antes de prosseguir com a exclusão do seu espaço de trabalho, você deve primeiro cancelar sua assinatura.',
	DeletePracticeWorkspaceModalConfirmButton: 'Sim, excluir permanentemente o espaço de trabalho',
	DeletePracticeWorkspaceModalDescription:
		'O espaço de trabalho {name} será permanentemente excluído e todos os membros da equipe perderão o acesso. Faça o download de quaisquer dados ou mensagens importantes de que você possa precisar antes que a exclusão ocorra. Esta ação não pode ser desfeita.',
	DeletePracticeWorkspaceModalReasonFormGroupLabel: 'Esta decisão foi tomada devido a:',
	DeletePracticeWorkspaceModalSpecificReasonFieldLabel: 'Razão',
	DeletePracticeWorkspaceModalSpecificReasonFieldPlaceholder: 'Diga-nos por que você deseja excluir sua conta.',
	DeletePracticeWorkspaceModalTitle: 'Tem certeza?',
	DeletePracticeWorkspaceSuccessSnackbarDescription: 'O acesso de todos os membros da equipe foi removido',
	DeletePracticeWorkspaceSuccessSnackbarTitle: '{providerName} foi excluído com sucesso',
	DeletePublicTemplateContent: 'Isso excluirá apenas o modelo público e não o modelo da sua equipe.',
	DeleteRecurringAppointmentModalTitle: 'Excluir compromisso repetido',
	DeleteRecurringEventModalTitle: 'Excluir reunião repetida',
	DeleteRecurringReminderModalTitle: 'Excluir lembrete repetido',
	DeleteRecurringTaskModalTitle: 'Excluir tarefa repetida',
	DeleteReminderConfirmation:
		'Esta é uma ação permanente. Depois que o lembrete for excluído, você não poderá mais acessá-lo. Afetará apenas novos compromissos',
	DeleteSection: 'Excluir seção',
	DeleteSectionInfo:
		'Excluindo a seção <strong>{section}</strong> ocultará todos os campos existentes dentro dela. Esta ação não pode ser desfeita.',
	DeleteSectionWarning:
		'Campos essenciais não podem ser excluídos e serão movidos para a seção existente **{section}**.',
	DeleteServiceFailure: 'Falha ao excluir o serviço',
	DeleteServiceSuccess: 'Serviço excluído com sucesso',
	DeleteStaffScheduleOverrideDescription:
		'Excluindo esta substituição de data em {value} irá removê-la de suas agendas e pode alterar a disponibilidade do seu serviço online. Esta ação não pode ser desfeita.',
	DeleteSuperbillConfirmationDescription:
		'Essa ação não pode ser desfeita. Isso excluirá permanentemente o recibo do Superbill.',
	DeleteSuperbillFailure: 'Falha ao excluir o recibo do Superbill',
	DeleteSuperbillSuccess: 'Recibo do Superbill excluído com sucesso',
	DeleteTaxRateConfirmationDescription: 'Tem certeza de que deseja excluir esta alíquota?',
	DeleteTemplateContent: 'Essa ação não pode ser desfeita',
	DeleteTemplateFolderSuccessMessage: '{folderTitle} deletado com sucesso',
	DeleteTemplateSuccessMessage: '{templateTitle} deletado com sucesso',
	DeleteTemplateTitle: 'Tem certeza de que deseja excluir este modelo?',
	DeleteTranscript: 'Excluir transcrição',
	DeleteWorkspace: 'Excluir espaço de trabalho',
	Deleted: 'Excluído',
	DeletedBy: 'Excluído por',
	DeletedContact: 'Contato excluído',
	DeletedOn: 'Excluído em',
	DeletedStatusLabel: 'Status excluído',
	DeletedUserTooltip: 'Este cliente foi excluído',
	DeliveryMethod: 'Método de Entrega',
	Demo: 'Demo',
	Denied: 'Negado',
	Dental: 'Dental',
	DentalAssistant: 'Assistente de dentista',
	DentalHygienist: 'Higienista dental',
	Dentist: 'Dentista',
	Dentists: 'Dentistas',
	Description: 'Descrição',
	DescriptionMustNotExceed: 'Descrição não pode exceder {max} caracteres',
	DetailDurationWithStaff: '{duration} min{staffName, select, null {} other { com {staffName}}}',
	Details: 'Detalhes',
	Devices: 'Dispositivos',
	Diagnosis: 'Diagnosis',
	DiagnosisAndBillingItems: 'Diagnóstico ',
	DiagnosisCode: 'Código de diagnóstico',
	DiagnosisCodeErrorMessage: 'É necessário um código de diagnóstico',
	DiagnosisCodeSelectorPlaceholder: 'Pesquise e adicione códigos de diagnóstico CID-10',
	DiagnosisCodeSelectorTooltip:
		'Os códigos de diagnóstico são usados para automatizar o recebimento de superbills para reembolso de seguros',
	DiagnosticCodes: 'Códigos de diagnóstico',
	Dictate: 'Ditar',
	DictatingIn: 'Ditando em',
	Dictation: 'Ditado',
	DidNotAttend: 'Não compareceu',
	DidNotComplete: 'Não concluído',
	DidNotProviderEnoughValue: 'Não forneceu valor suficiente',
	DidntProvideEnoughValue: 'Não forneceu valor suficiente',
	DieteticsOrNutrition: 'Dietética ou nutrição',
	Dietician: 'Nutricionista',
	Dieticians: 'Dietistas',
	Dietitian: 'Dietista',
	DigitalSign: 'Assine aqui:',
	DigitalSignHelp: '(Clique/pressione para baixo para desenhar)',
	DirectDebit: 'Débito Direto',
	DirectTextLink: 'Link direto para o texto',
	Disable: 'Desabilitar',
	DisabledEmailInfo: 'Não podemos atualizar seu endereço de e-mail porque sua conta não é gerenciada por nós',
	Discard: 'Descartar',
	DiscardChanges: 'Descartar mudanças',
	DiscardDrafts: 'Descartar rascunhos',
	Disconnect: 'desconectar',
	DisconnectAppConfirmation: 'Deseja desconectar este aplicativo?',
	DisconnectAppConfirmationDescription: 'Tem certeza de que deseja desconectar este aplicativo?',
	DisconnectAppConfirmationTitle: 'Desconectar aplicativo',
	Discount: 'Desconto',
	DisplayCalendar: 'Exibição no Carepatron',
	DisplayName: 'Nome de exibição',
	DisplayedToClients: 'Exibido para clientes',
	DiversionalTherapist: 'Terapeuta Diversional',
	DoItLater: 'Fazer mais tarde',
	DoNotImport: 'Não importe',
	DoNotSend: 'Não envie',
	DoThisLater: 'Faça isso mais tarde',
	DoYouWantToEndSession: 'Você quer continuar ou encerrar sua sessão agora?',
	Doctor: 'Doutor',
	Doctors: 'Médicos',
	DoesNotRepeat: 'Não repete',
	DoesntWorkWellWithExistingTools: 'Não funciona bem com nossas ferramentas ou fluxos de trabalho existentes',
	DogWalker: 'Passeador de cães',
	Done: 'Feito',
	DontAllowClientsToCancel: 'Não permitir que os clientes cancelem',
	DontHaveAccount: 'Não tem uma conta?',
	DontSend: 'Não envie',
	Double: 'Dobro',
	DowngradeTo: 'Rebaixar para {plan}',
	DowngradingPricingPlanTooManyStaffErrorCodeSnackbar:
		'Desculpe, você não pode fazer downgrade do seu plano porque tem muitos membros na equipe. Remova alguns do seu provedor e tente novamente.',
	Download: 'Download',
	DownloadAsPdf: 'Baixar como PDF',
	DownloadERA: 'Baixar ERA',
	DownloadPDF: 'baixar PDF',
	DownloadTemplateFileName: 'Carepatron Template de Troca.csv',
	DownloadTemplateTileDescription: 'Use nosso modelo de planilha para organizar e carregar seus clientes.',
	DownloadTemplateTileLabel: 'Baixar modelo',
	Downloads: '{number, plural, one {<span>#</span> Download} other {<span>#</span> Downloads}}',
	DoxyMe: 'Doxy.me',
	Draft: 'Rascunho',
	DraftResponses: 'Rascunho de resposta',
	DraftSaved: 'Alterações salvas',
	DragAndDrop: 'arrastar e soltar',
	DragDropText: 'Arraste e solte documentos de saúde',
	DragToMove: 'Arraste para mover',
	DragToMoveOrActivate: 'Arraste para mover ou ativar',
	DramaTherapist: 'Terapeuta Dramático',
	DropdownFormFieldPlaceHolder: 'Escolha opções da lista',
	DropdownFormPrimaryText: 'Suspenso',
	DropdownFormSecondaryText: 'Escolha em uma lista de opções',
	DropdownTextFieldError: 'O texto da opção suspensa não pode ficar vazio',
	DropdownTextFieldPlaceholder: 'Adicione uma opção suspensa',
	Due: 'Data de vencimento',
	DueDate: 'Data de vencimento',
	Duplicate: 'Duplicado',
	DuplicateAvailabilityScheduleFailure: 'Falha ao duplicar a programação de disponibilidade',
	DuplicateAvailabilityScheduleSuccess: 'Agendamento de {name} duplicado com sucesso',
	DuplicateClientBannerAction: 'Análise',
	DuplicateClientBannerDescription:
		'Mesclar registros de clientes duplicados os consolida em um só, mantendo todas as informações exclusivas do cliente.',
	DuplicateClientBannerTitle: '{count} Duplicatas encontradas',
	DuplicateColumn: 'Coluna duplicada',
	DuplicateContactFieldSettingErrorSnackbar: 'Não é possível ter nomes de seções duplicados',
	DuplicateContactFieldSettingFieldErrorSnackbar: 'Não é possível ter nomes de campos duplicados',
	DuplicateEmailError: 'E-mail duplicado',
	DuplicateHeadingName: 'Seção {name} já existe',
	DuplicateInvoiceNumberErrorCodeSnackbar: 'Já existe uma fatura com o mesmo &quot;número de fatura&quot;.',
	DuplicateRecords: 'Registros duplicados',
	DuplicateRecordsMinimumError: 'Devem ser selecionados no mínimo 2 registros',
	DuplicateRecordsRequired: 'Selecione pelo menos 1 registro para separar',
	DuplicateServiceFailure: 'Falha ao duplicar <strong>{title}</strong>',
	DuplicateServiceSuccess: 'Duplicado com sucesso **{title}**',
	DuplicateTemplateFolderSuccessMessage: 'Pasta duplicada com sucesso',
	DuplicateTemplateSuccess: 'Modelo duplicado com sucesso',
	DurationInMinutes: '{duration}min',
	Dx: 'DX',
	DxCode: 'Código DX',
	DxCodeSelectPlaceholder: 'Pesquise e adicione códigos CID-10',
	EIN: 'EIN',
	EMG: 'EMG',
	EPSDT: 'EPSDT',
	EPSDTPlaceholder: 'Nenhum',
	ERAAdjustment: '<b>ERA {number}</b>{isAdjusted, select, true { <i>contém ajustes</i>} other {}}',
	EarnReferralCredit: 'Ganhe ${creditAmount}',
	Economist: 'Economista',
	Edit: 'Editar',
	EditArrangements: 'Editar arranjos',
	EditBillTo: 'Editar fatura para',
	EditClient: 'Editar cliente',
	EditClientFileModalDescription:
		'Edite o acesso a este arquivo escolhendo as opções nas caixas de seleção &quot;Visualizável por&quot;',
	EditClientFileModalTitle: 'Editar arquivo',
	EditClientNoteModalDescription:
		'Edite o conteúdo da nota. Use a seção &quot;Visível por&quot; para alterar quem pode ver a nota.',
	EditClientNoteModalTitle: 'Editar nota',
	EditConnectedAppButton: 'Editar',
	EditConnections: 'Editar conexões{account, select, null { } undefined { } other { para {account}}}',
	EditContactDetails: 'Editar detalhes de contato',
	EditContactFormIsClientLabel: 'Converter em cliente',
	EditContactIsClientCheckboxWarning: 'A conversão de um contato em cliente não pode ser desfeita',
	EditContactIsClientWanringModal:
		'A conversão deste contato em Cliente não pode ser desfeita. No entanto, todos os relacionamentos permanecerão e agora você terá acesso às suas notas, arquivos e outras documentações.',
	EditContactRelationship: 'Editar relacionamento de contato',
	EditDetails: 'Editar Detalhes',
	EditFileModalTitle: 'Editar arquivo para {name}',
	EditFolder: 'Editar pasta',
	EditFolderDescription: 'Renomeie a pasta como...',
	EditInvoice: 'Editar fatura',
	EditInvoiceDetails: 'Editar detalhes da fatura',
	EditLink: 'Editar link',
	EditLocation: 'Editar localização',
	EditLocationFailure: 'Falha ao atualizar o local',
	EditLocationSucess: 'Local atualizado com sucesso',
	EditPaymentDetails: 'Editar detalhes de pagamento',
	EditPaymentMethod: 'Editar forma de pagamento',
	EditPersonalDetails: 'Editar dados pessoais',
	EditPractitioner: 'Editar praticante',
	EditProvider: 'Editar provedor',
	EditProviderDetails: 'Editar detalhes do provedor',
	EditRecurrence: 'Editar recorrência',
	EditRecurringAppointmentModalTitle: 'Editar compromisso repetido',
	EditRecurringEventModalTitle: 'Editar reunião repetida',
	EditRecurringReminderModalTitle: 'Editar lembrete repetido',
	EditRecurringTaskModalTitle: 'Editar tarefa repetida',
	EditRelationshipModalTitle: 'Editar relacionamento',
	EditService: 'Editar serviço',
	EditServiceFailure: 'Falha ao atualizar o novo serviço',
	EditServiceGroup: 'Editar coleção',
	EditServiceGroupFailure: 'Falha ao atualizar a coleção',
	EditServiceGroupSuccess: 'Coleção atualizada com sucesso',
	EditServiceSuccess: 'Novo serviço atualizado com sucesso',
	EditStaffDetails: 'Editar detalhes da equipe',
	EditStaffDetailsCantUpdatedEmailTooltip:
		'Não é possível atualizar o endereço de e-mail. Crie um novo membro da equipe com um novo endereço de e-mail.',
	EditSubscriptionBilledQuantity: 'Quantidade Faturada',
	EditSubscriptionBilledQuantityValue: '{billedUsers} membros da equipe',
	EditSubscriptionLimitedTimeOffer: 'Oferta por tempo limitado! 50% de desconto por 6 meses.',
	EditSubscriptionUpgradeAdjustTeamBanner:
		'O custo da sua assinatura será ajustado ao adicionar ou remover membros da equipe.',
	EditSubscriptionUpgradeContent:
		'Sua conta será atualizada imediatamente para o novo plano e período de cobrança. Quaisquer alterações de preços serão cobradas automaticamente no seu método de pagamento salvo ou creditadas na sua conta.',
	EditSubscriptionUpgradePlanTitle: 'Atualizar plano de assinatura',
	EditSuperbillReceipt: 'Editar superfatura',
	EditTags: 'Editar etiquetas',
	EditTemplate: 'Editar modelo',
	EditTemplateFolderSuccessMessage: 'Pasta de modelos atualizada com sucesso',
	EditValue: 'Editar {value}',
	Edited: 'Editado',
	Editor: 'editor',
	EditorAlertDescription:
		'Um formato não suportado foi detectado. Recarregue o aplicativo ou entre em contato com nossa equipe de suporte.',
	EditorAlertTitle: 'Estamos com problemas para exibir este conteúdo',
	EditorPlaceholder:
		'Comece a escrever, escolha um modelo ou adicione blocos básicos para capturar respostas de seus clientes.',
	EditorTemplatePlaceholder: 'Comece a escrever ou adicione componentes para construir um modelo',
	EditorTemplateWithSlashCommandPlaceholder:
		'Comece a escrever ou adicione blocos básicos para capturar as respostas dos clientes. Use comandos de barra (/) para ações rápidas.',
	EditorWithSlashCommandPlaceholder:
		'Comece a escrever, escolha um modelo ou adicione blocos básicos para capturar respostas do cliente. Use comandos de barra ( / ) para ações rápidas.',
	EffectiveStartEndDate: 'Data efetiva de início - término',
	ElectricalEngineer: 'Engenheiro elétrico',
	Electronic: 'Eletrônico',
	ElectronicSignature: 'Assinatura Eletrônica',
	ElementarySchoolTeacher: 'Professor de escola fundamental',
	Eligibility: 'Elegibilidade',
	Email: 'E-mail',
	EmailAlreadyExists: 'O endereço de e-mail já existe',
	EmailAndSms: 'E-mail ',
	EmailBody: 'Corpo do e-mail',
	EmailContainsIgnoredDescription:
		'Este e-mail foi ignorado anteriormente e não será exibido na sua caixa de entrada. Você pode parar de ignorar este e-mail a qualquer momento.',
	EmailInviteToPortalBody: `Olá {contactName},
Por favor, siga este link para entrar em seu portal de cliente seguro e gerenciar facilmente seus cuidados.

Atenciosamente,

{providerName}`,
	EmailInviteToPortalSubject: 'Bem-vindo ao {providerName}',
	EmailInvoice: 'Fatura por e-mail',
	EmailInvoiceOverdueBody: `Oi {contactName}
Sua fatura {invoiceNumber} está vencida.
Por favor, pague sua fatura online usando o link abaixo.

Se você tiver alguma dúvida, por favor, nos avise.

Obrigado,
{providerName}`,
	EmailInvoicePaidBody: `Olá {contactName}
Sua fatura {invoiceNumber} foi paga.
Para visualizar e baixar uma cópia da sua fatura, siga o link abaixo.

Se você tiver alguma dúvida, por favor, nos avise.

Obrigado,
{providerName}`,
	EmailInvoiceProcessingBody: `Olá {contactName}
Sua fatura {invoiceNumber} está pronta.
Siga o link abaixo para visualizar sua fatura.

Caso tenha alguma dúvida, por favor, entre em contato.

Obrigado,
{providerName}`,
	EmailInvoiceUnpaidBody: `Olá {contactName}
Sua fatura {invoiceNumber} está pronta para pagamento até {dueDate}.
Para visualizar e pagar sua fatura online, siga o link abaixo.

Caso tenha alguma dúvida, por favor, entre em contato.

Obrigado,
{providerName}`,
	EmailInvoiceVoidBody: `Olá {contactName}
Sua fatura {invoiceNumber} foi anulada.
Para visualizar esta fatura, siga o link abaixo.

Caso tenha alguma dúvida, entre em contato conosco.

Obrigado,
{providerName}`,
	EmailNotFound: 'email não encontrado',
	EmailNotVerifiedErrorCodeSnackbar: 'Não é possível executar a ação. Você precisa verificar seu endereço de e-mail.',
	EmailNotVerifiedTitle: 'Verifique seu endereço de e-mail',
	EmailSendClientIntakeBody: `Olá {contactName},
{providerName} gostaria que você fornecesse algumas informações e revisasse documentos importantes. Por favor, siga o link abaixo para começar.

Atenciosamente,

{providerName}`,
	EmailSendClientIntakeSubject: 'Bem-vindo ao {providerName}',
	EmailSuperbillReceipt: 'Superfatura por e-mail',
	EmailSuperbillReceiptBody: `Olá {contactName},
{providerName} enviou uma cópia do seu comprovante de reembolso {date}.

Você pode baixar e enviar este diretamente para sua seguradora.`,
	EmailSuperbillReceiptFailure: 'Falha ao enviar recibo do Superbill',
	EmailSuperbillReceiptSubject: '{providerName} enviou um extrato de recebimento de reembolso',
	EmailSuperbillReceiptSuccess: 'Recibo do Superbill enviado com sucesso',
	EmailVerificationDescription: 'Estamos <span>verificando</span> sua conta agora',
	EmailVerificationNotification: 'Um email de verificação foi enviado para {email}',
	EmailVerificationSuccess: 'Seu endereço de e-mail foi alterado com sucesso para {email}',
	Emails: 'E-mails',
	EmergencyContact: 'Contato de emergência',
	EmployeesIdentificationNumber: 'Número de identificação dos funcionários',
	EmploymentStatus: 'situação de emprego',
	EmptyAgendaViewDescription: 'Nenhum evento para exibir.<mark> Crie um compromisso agora</mark>',
	EmptyBin: 'Lixeira vazia',
	EmptyBinConfirmationDescription:
		'Esvaziar a lixeira excluirá todas as **{total} conversas** em Excluídas. Essa ação não pode ser desfeita.',
	EmptyBinConfirmationTitle: 'Exclua conversas para sempre',
	EmptyTrash: 'Esvaziar lixeira',
	Enable: 'Habilitar',
	EnableCustomServiceAvailability: 'Habilitar disponibilidade de serviço',
	EnableCustomServiceAvailabilityDescription:
		'Por exemplo, as consultas iniciais só podem ser agendadas todos os dias das 9h às 10h',
	EndCall: 'Encerrar chamada',
	EndCallConfirmationForCreator: 'Você encerrará isso para todos porque é o iniciador da chamada.',
	EndCallConfirmationHasActiveAttendees:
		'Você está prestes a encerrar a chamada, mas o(s) cliente(s) já entrou(m). Você também quer participar?',
	EndCallForAll: 'Encerrar chamada para todos',
	EndDate: 'Data final',
	EndDictation: 'Fim do ditado',
	EndOfLine: 'Não há mais compromissos',
	EndSession: 'Fim da sessão',
	EndTranscription: 'Fim da transcrição',
	Ends: 'Termina',
	EndsOnDate: 'Termina em {date}',
	Enrol: 'Inscreva-se',
	EnrollmentRejectedSubject: 'Sua inscrição com {payerName} foi rejeitada',
	Enrolment: 'Ingestão',
	Enrolments: 'Inscrições',
	EnrolmentsDescription: 'Visualizar e gerenciar inscrições de provedores com a seguradora.',
	EnterAName: 'Insira o nome...',
	EnterFieldLabel: 'Insira o rótulo do campo...',
	EnterPaymentDetailsDescription:
		'O custo da sua assinatura será ajustado automaticamente ao adicionar ou remover usuários.',
	EnterSectionName: 'Digite o nome da seção...',
	EnterSubscriptionPaymentDetails: 'Insira os detalhes de pagamento',
	EnvironmentalScientist: 'Cientista ambiental',
	Epidemiologist: 'Epidemiologista',
	Eraser: 'Apagador',
	Error: 'Erro',
	ErrorBoundaryAction: 'Recarregar página',
	ErrorBoundaryDescription: 'Atualize a página e tente novamente.',
	ErrorBoundaryTitle: 'Opa! Algo deu errado',
	ErrorCallNotFound: 'A chamada não pode ser encontrada. Pode ter expirado ou o criador o encerrou.',
	ErrorCannotAccessCallUninvitedCode: 'Desculpe, parece que você não foi convidado para esta chamada.',
	ErrorFileUploadCustomMaxFileCount: 'Não é possível carregar mais de {count} arquivos ao mesmo tempo',
	ErrorFileUploadCustomMaxFileSize: 'O tamanho do arquivo não pode exceder {mb} MB',
	ErrorFileUploadInvalidFileType: 'Tipo de arquivo inválido que pode conter possíveis vírus e software prejudicial',
	ErrorFileUploadMaxFileCount: 'Não é possível fazer upload de mais de 150 arquivos de uma vez',
	ErrorFileUploadMaxFileSize: 'O tamanho do arquivo não pode exceder 100 MB',
	ErrorFileUploadNoFileSelected: 'Selecione os arquivos para upload',
	ErrorInvalidNationalProviderId: 'O ID Nacional do Fornecedor fornecido não é válido.',
	ErrorInvalidPayerId: 'O ID do Pagador fornecido não é válido',
	ErrorInvalidTaxNumber: 'O Número de Imposto fornecido não é válido',
	ErrorInviteExistingProviderStaffCode: 'Este usuário já está no workspace.',
	ErrorInviteStaffExistingUser: 'Desculpe, parece que o usuário que você adicionou já existe em nosso sistema.',
	ErrorOnlySingleCallAllowed: 'Você só pode ter uma chamada ativa por vez.',
	ErrorPayerNotFound: 'Pagador não encontrado',
	ErrorProfilePhotoMaxFileSize: 'Falha no upload! Limite de tamanho de arquivo atingido: 5 MB',
	ErrorRegisteredExistingUser: 'Desculpe, parece que você já está registrado.',
	ErrorUserSignInIncorrectCredentials: 'E-mail ou senha inválidos. Tente novamente.',
	ErrorUserSigninGeneric: 'Desculpe, algo deu errado.',
	ErrorUserSigninUserNotConfirmed:
		'Desculpe, você precisa confirmar sua conta antes de fazer login. Verifique sua caixa de entrada para obter instruções.',
	Errors: 'Erros',
	EssentialPlanInclusionFive: 'Importação de modelo',
	EssentialPlanInclusionFour: '5 GB de armazenamento',
	EssentialPlanInclusionHeader: 'Tudo em Grátis  ',
	EssentialPlanInclusionOne: 'Lembretes automáticos e personalizados',
	EssentialPlanInclusionSix: 'Suporte prioritário',
	EssentialPlanInclusionThree: 'Video chat',
	EssentialPlanInclusionTwo: 'Sincronização de calendário bidirecional',
	EssentialSubscriptionPlanSubtitle: 'Simplifique sua prática com o essencial',
	EssentialSubscriptionPlanTitle: 'Essencial',
	Esthetician: 'Esteticista',
	Estheticians: 'Esteticistas',
	EstimatedArrivalDate: 'Chegada prevista em {numberOfDaysFromNow} dias',
	Ethnicity: 'Etnia',
	Europe: 'Europa',
	EventColor: 'Cor da reunião',
	EventName: 'Nome do evento',
	EventType: 'Tipo de evento',
	Every: 'Cada',
	Every2Weeks: 'A cada 2 semanas',
	EveryoneInWorkspace: 'Todos no espaço de trabalho',
	ExercisePhysiologist: 'Fisiologista do Exercício',
	Existing: 'Existente',
	ExistingClients: 'Clientes existentes',
	ExistingFolders: 'Pastas existentes',
	ExpiredPromotionCode: 'O código promocional expirou',
	ExpiredReferralDescription: 'A indicação expirou',
	ExpiredVerificationLink: 'Link de verificação expirado',
	ExpiredVerificationLinkDescription: `Lamentamos, mas o link de verificação em que você clicou expirou. Isso pode acontecer se você esperou mais de 24 horas para clicar no link ou se já tiver usado o link para verificar seu endereço de e-mail.

 Solicite um novo link de verificação para verificar seu endereço de e-mail.`,
	ExpiryDateRequired: 'A data de validade é obrigatória',
	ExploreFeature: 'O que você gostaria de explorar primeiro?',
	ExploreOptions: 'Escolha uma ou mais opções para explorar...',
	Export: 'Exportar',
	ExportAppointments: 'Exportar compromissos',
	ExportClaims: 'Exportar reivindicações',
	ExportClaimsFilename: 'Reivindicações {fromDate}-{toDate}.csv',
	ExportClientsDownloadFailureSnackbarDescription: 'Seu arquivo não pôde ser baixado devido a um erro.',
	ExportClientsDownloadFailureSnackbarTitle: 'Falha no download',
	ExportClientsFailureSnackbarDescription: 'Não foi possível exportar os clientes. Tente novamente mais tarde.',
	ExportClientsFailureSnackbarTitle: 'Falha na exportação',
	ExportClientsModalDescription: 'Selecione os clientes que deseja exportar',
	ExportClientsModalTitle: 'Exportar clientes',
	ExportCms1500: 'Exportar CMS1500',
	ExportContactFailedNotificationSubject: 'A exportação de seus dados falhou',
	ExportFailed: 'Falha na exportação',
	ExportGuide: 'Guia de exportação',
	ExportInvoiceFileName: 'Transações {fromDate}-{toDate}.csv',
	ExportPayments: 'Exportação de pagamentos',
	ExportPaymentsFilename: 'Pagamentos {fromDate}-{toDate}.csv',
	ExportPrintCompletedMessage: 'Seu documento está pronto para download.',
	ExportPrintWaitMessage: 'Preparando seu documento. Por favor aguarde...',
	ExportTextOnly: 'Exportar somente texto',
	ExportTransactions: 'Transações de exportação',
	Exporting: 'Exportando',
	ExportingData: 'Exportando dados',
	ExtendedFamilyMember: 'Membro da família extensa',
	External: 'Externo',
	ExternalEventInfoBanner: 'Este compromisso é de um calendário sincronizado e pode estar faltando itens.',
	ExtraLarge: 'Extra grande',
	FECABlackLung: 'FECA Black Lung',
	Failed: 'Fracassado',
	FailedToJoinTheMeeting: 'Falha ao participar da reunião.',
	FallbackPageDescription: `Parece que esta página não existe, você pode precisar {refreshButton} esta página para obter as alterações mais recentes.
Caso contrário, entre em contato com o suporte do Carepatron.`,
	FallbackPageDescriptionUpdateButton: 'atualizar',
	FallbackPageTitle: 'Ops...',
	FamilyPlanningService: 'Family planning service',
	FashionDesigner: 'Designer de moda',
	FastTrackInvoicingAndBilling: 'Acelere seu faturamento e cobrança',
	Father: 'Pai',
	FatherInLaw: 'Sogro',
	Favorite: 'Favorito',
	FeatureBannerCalendarTile1ActionLabel: 'Reserva on-line • 2 minutos',
	FeatureBannerCalendarTile1Description:
		'Basta enviar um e-mail, enviar uma mensagem de texto ou adicionar disponibilidade ao seu site',
	FeatureBannerCalendarTile1Title: 'Permita que seus clientes façam reservas on-line',
	FeatureBannerCalendarTile2ActionLabel: 'Automatize lembretes • 2 minutos',
	FeatureBannerCalendarTile2Description: 'Aumente o atendimento do cliente com lembretes automatizados',
	FeatureBannerCalendarTile2Title: 'Reduza os não comparecimentos',
	FeatureBannerCalendarTile3Title: 'Agendamento e fluxo de trabalho',
	FeatureBannerCalendarTitle: 'Facilite o agendamento',
	FeatureBannerCallsTile1ActionLabel: 'Iniciar chamada de telessaúde',
	FeatureBannerCallsTile1Description: 'Acesso do cliente com apenas um link. Sem logins, senhas ou complicações',
	FeatureBannerCallsTile1Title: 'Inicie uma videochamada de qualquer lugar',
	FeatureBannerCallsTile2ActionLabel: 'Conectar aplicativos • 4 minutos',
	FeatureBannerCallsTile2Description: 'Conecte-se perfeitamente a outros provedores de telessaúde preferenciais',
	FeatureBannerCallsTile2Title: 'Conecte seus aplicativos de telessaúde',
	FeatureBannerCallsTile3Title: 'Chamadas',
	FeatureBannerCallsTitle: 'Conecte-se com clientes – em qualquer lugar, a qualquer hora',
	FeatureBannerClientsTile1ActionLabel: 'Importe agora • 2 minutos',
	FeatureBannerClientsTile1Description:
		'Comece rapidamente com nossa ferramenta automatizada de importação de clientes',
	FeatureBannerClientsTile1Title: 'Tem muitos clientes?',
	FeatureBannerClientsTile2ActionLabel: 'Personalize a ingestão • 2 minutos',
	FeatureBannerClientsTile2Description: 'Remova a papelada de admissão e melhore a experiência do cliente',
	FeatureBannerClientsTile2Title: 'Deixe de usar papel',
	FeatureBannerClientsTile3Title: 'Portal do cliente',
	FeatureBannerClientsTitle: 'Tudo começa com seus clientes',
	FeatureBannerHeader: 'Pela Comunidade, para a Comunidade!',
	FeatureBannerInvoicesTile1ActionLabel: 'Automatize pagamentos • 2 minutos',
	FeatureBannerInvoicesTile1Description: 'Evite conversas estranhas com pagamentos automatizados',
	FeatureBannerInvoicesTile1Title: 'Receba pagamentos 2x mais rápido',
	FeatureBannerInvoicesTile2ActionLabel: 'Acompanhe o fluxo de caixa • 2 minutos',
	FeatureBannerInvoicesTile2Description: 'Reduza faturas não pagas e controle sua receita',
	FeatureBannerInvoicesTile2Title: 'Acompanhe sua renda, sem dor',
	FeatureBannerInvoicesTile3Title: 'Faturamento e Pagamentos',
	FeatureBannerInvoicesTitle: 'Uma coisa a menos para se preocupar',
	FeatureBannerSubheader:
		'Modelos Carepatron feitos por nossa equipe e comunidade. Experimente novos recursos ou compartilhe os seus próprios!',
	FeatureBannerTeamTile1ActionLabel: 'Convide agora',
	FeatureBannerTeamTile1Description: 'Convide membros da equipe para sua conta e facilite a colaboração',
	FeatureBannerTeamTile1Title: 'Reúna sua equipe',
	FeatureBannerTeamTile2ActionLabel: 'Definir disponibilidade • 2 minutos',
	FeatureBannerTeamTile2Description: 'Gerencie a disponibilidade de suas equipes para evitar reservas duplicadas',
	FeatureBannerTeamTile2Title: 'Defina sua disponibilidade',
	FeatureBannerTeamTile3ActionLabel: 'Definir permissões • 2 minutos',
	FeatureBannerTeamTile3Description: 'Controle o acesso a dados e ferramentas confidenciais para conformidade',
	FeatureBannerTeamTile3Title: 'Personalize permissões e acesso',
	FeatureBannerTeamTitle: 'Nada de grande é alcançado sozinho',
	FeatureBannerTemplatesTile1ActionLabel: 'Explorar a biblioteca • 2 minutos',
	FeatureBannerTemplatesTile1Description: 'Escolha entre uma incrível biblioteca de recursos personalizáveis ',
	FeatureBannerTemplatesTile1Title: 'Reduza sua carga de trabalho',
	FeatureBannerTemplatesTile2ActionLabel: 'Enviar agora • 2 minutos',
	FeatureBannerTemplatesTile2Description: 'Envie lindos modelos aos clientes para conclusão',
	FeatureBannerTemplatesTile2Title: 'Torne a documentação divertida',
	FeatureBannerTemplatesTile3Title: 'Modelos',
	FeatureBannerTemplatesTitle: 'Modelos para absolutamente tudo',
	FeatureLimitBannerDescription:
		'Faça o upgrade agora para continuar criando e gerenciando {featureName} sem interrupções e aproveite ao máximo o Carepatron!',
	FeatureLimitBannerTitle: 'Você está {percentage}% do caminho para o seu limite de {featureName}',
	FeatureRequiresUpgrade: 'Este recurso requer uma atualização',
	Fee: 'Fee',
	Female: 'Fêmea',
	FieldLabelTooltip: '{isHidden, select, true {Mostrar} other {Ocultar}} rótulo do campo',
	FieldName: 'Nome do campo',
	FieldOptionsFirstPart: 'Primeira palavra',
	FieldOptionsMiddlePart: 'Palavras do meio',
	FieldOptionsSecondPart: 'Última palavra',
	FieldOptionsWholeField: 'Campo inteiro',
	FieldType: 'Tipo de campo',
	Fields: 'Campos',
	File: 'Arquivo',
	FileDownloaded: '**{fileName}** baixado',
	FileInvalidType: 'Arquivo não suportado.',
	FileNotFound: 'Arquivo não encontrado',
	FileNotFoundDescription: 'O arquivo que você está procurando não pode ser encontrado',
	FileTags: 'Tags de arquivo',
	FileTagsHelper: 'As tags serão aplicadas a todos os arquivos',
	FileTooLarge: 'Arquivo muito grande.',
	FileTooSmall: 'Arquivo muito pequeno.',
	FileUploadComplete: 'Completo',
	FileUploadFailed: 'Fracassado',
	FileUploadInProgress: 'Carregando',
	FileUploadedNotificationSubject: '{actorProfileName} carregou um arquivo',
	Files: 'arquivos',
	FillOut: 'Preencher',
	Filter: 'Filtro',
	FilterBy: 'Filtrar por',
	FilterByAmount: 'Filtrar por quantidade',
	FilterByClient: 'Filtrar por cliente',
	FilterByLocation: 'Filtrar por localização',
	FilterByService: 'Filtrar por serviço',
	FilterByStatus: 'Filtrar por status',
	FilterByTags: 'Filtrar por tags',
	FilterByTeam: 'Filtrar por equipe',
	Filters: 'Filtros',
	FiltersAppliedToView: 'Filtros aplicados à visualização',
	FinalAppointment: 'Consulta Final',
	FinalizeImport: 'Finalizar importação',
	FinancialAnalyst: 'Analista financeiro',
	Finish: 'Terminar',
	Firefighter: 'Bombeiro',
	FirstName: 'Primeiro nome',
	FirstNameLastInitial: 'Primeiro nome, última inicial',
	FirstPerson: '1ª pessoa',
	FolderName: 'Nome da pasta',
	Folders: 'Pastas',
	FontFamily: 'Família de fontes',
	ForClients: 'Para clientes',
	ForClientsDetails: 'Recebo cuidados ou serviços relacionados à saúde',
	ForPractitioners: 'Para praticantes',
	ForPractitionersDetails: 'Gerencie e expanda sua prática',
	ForgotPasswordConfirmAccessCode: 'Código de confirmação',
	ForgotPasswordConfirmNewPassword: 'Nova Senha',
	ForgotPasswordConfirmPageDescription:
		'Por favor, insira seu endereço de e-mail, uma nova senha e o código de confirmação que acabamos de enviar.',
	ForgotPasswordConfirmPageTitle: 'Redefinir senha',
	ForgotPasswordPageButton: 'Enviar link de redefinição',
	ForgotPasswordPageDescription: 'Digite seu e-mail e enviaremos um link para redefinir sua senha.',
	ForgotPasswordPageTitle: 'Palavra-chave esquecida',
	ForgotPasswordSuccessPageDescription: 'Verifique sua caixa de entrada para obter o link de redefinição.',
	ForgotPasswordSuccessPageTitle: 'Link de redefinição enviado!',
	Form: 'Formulário',
	FormAnswersSentToEmailNotification: 'Enviamos uma cópia de suas respostas para',
	FormBlocks: 'Blocos de formulário',
	FormFieldAddOption: 'Adicionar opção',
	FormFieldAddOtherOption: 'Adicione &quot;outro&quot;',
	FormFieldOptionPlaceholder: 'Opção {index}',
	FormStructures: 'Estruturas de formulários',
	Format: 'Formato',
	FormatLinkButtonColor: 'Cor do botão',
	Forms: 'Formulários',
	FormsAndAgreementsValidationMessage:
		'Todos os formulários e acordos devem ser preenchidos para continuar com o processo de admissão.',
	FormsCategoryDescription: 'Para coletar e organizar os detalhes do paciente',
	Frankfurt: 'Francoforte',
	Free: 'Livre',
	FreePlanInclusionFive: 'Faturamento automatizado ',
	FreePlanInclusionFour: 'Portal do cliente',
	FreePlanInclusionHeader: 'Comece com',
	FreePlanInclusionOne: 'Clientes ilimitados',
	FreePlanInclusionSix: 'Suporte ao vivo',
	FreePlanInclusionThree: '1 GB de armazenamento',
	FreePlanInclusionTwo: 'Telessaúde',
	FreeSubscriptionPlanSubtitle: 'Grátis para todos',
	FreeSubscriptionPlanTitle: 'Livre',
	Friday: 'Sexta-feira',
	From: 'De',
	FullName: 'Nome completo',
	FunctionalMedicineOrNaturopath: 'Medicina Funcional ou Naturopata',
	FuturePaymentsAuthoriseProvider: 'Permitir que {provider} use o pagamento salvo no futuro',
	FuturePaymentsSavePaymentMethod: 'Salvar {paymentMethod} para pagamentos futuros',
	GST: 'ICMS',
	Gender: 'Gênero',
	GeneralAvailability: 'Disponibilidade Geral',
	GeneralAvailabilityDescription:
		'Defina quando você estará regularmente disponível. Os clientes só poderão reservar os seus serviços nos horários disponíveis.',
	GeneralAvailabilityDescription2:
		'Crie horários com base na sua disponibilidade e nas ofertas de serviços desejadas em horários específicos para determinar a disponibilidade da sua reserva online.',
	GeneralAvailabilityInfo: 'Seus horários disponíveis determinarão a disponibilidade de sua reserva on-line',
	GeneralAvailabilityInfo2:
		'Os serviços que oferecem eventos coletivos devem adotar um novo horário para reduzir os horários disponíveis que podem ser reservados pelos clientes online.',
	GeneralHoursPlural: '{count} {count, plural, one {hora} other {horas}}',
	GeneralPractitioner: 'Clínico Geral',
	GeneralPractitioners: 'Clínicos Gerais',
	GeneralServiceAvailabilityInfo: 'Esta programação substituirá o comportamento dos membros da equipe atribuídos',
	Generate: 'Gerar',
	GenerateBillingItemsBannerContent:
		'Itens de cobrança não são criados automaticamente para compromissos recorrentes.',
	GenerateItems: 'Gerar itens',
	GenerateNote: 'Gerar nota',
	GenerateNoteConfirmationModalDescription:
		'O que você gostaria de fazer? Criar uma nova nota gerada, adicionar à existente ou substituir seu conteúdo?',
	GenerateNoteFor: 'Gerar nota para',
	GeneratingContent: 'Gerando conteúdo...',
	GeneratingNote: 'Gerando sua nota...',
	GeneratingTranscript: 'Gerando transcrição',
	GeneratingTranscriptDescription: 'Isso pode levar alguns minutos para ser processado',
	GeneratingYourTranscript: 'Gerando sua transcrição',
	GenericErrorDescription: '{module} não pôde ser carregado. Por favor, tente novamente mais tarde.',
	GenericErrorTitle: 'Erro inesperado ocorreu',
	GenericFailureSnackbar: 'Desculpe, algo inesperado ocorreu. Atualize a página e tente novamente.',
	GenericSavedSuccessSnackbar: 'Sucesso! Alterações salvas',
	GeneticCounselor: 'Conselheiro Genético',
	Gerontologist: 'Gerontologista',
	Get50PercentOff: '<h1>Obtenha 50% de desconto!</h1>',
	GetHelp: 'Obter ajuda',
	GetStarted: 'iniciar',
	GettingStartedAppointmentTypes: 'Crie tipos de compromisso',
	GettingStartedAppointmentTypesDescription:
		'Simplifique seu agendamento e faturamento personalizando seus serviços, preços e códigos de faturamento',
	GettingStartedAppointmentTypesTitle: 'Agendar ',
	GettingStartedClients: 'Adicione seus clientes',
	GettingStartedClientsDescription:
		'Prepare-se e trabalhe com os clientes para compromissos, notas e pagamentos futuros',
	GettingStartedClientsTitle: 'Tudo começa com os clientes',
	GettingStartedCreateClient: 'Criar cliente',
	GettingStartedImportClients: 'Importar clientes',
	GettingStartedInvoices: 'Faturar como um profissional',
	GettingStartedInvoicesDescription: `É simples criar faturas profissionais.
 Adicione seu logotipo, localização e condições de pagamento`,
	GettingStartedInvoicesTitle: 'Coloque o seu melhor no que faz',
	GettingStartedMobileApp: 'Obtenha o aplicativo móvel',
	GettingStartedMobileAppDescription:
		'Você pode baixar o Carepatron em seu dispositivo iOS, Android ou desktop para fácil acesso em qualquer lugar',
	GettingStartedMobileAppTitle: 'Trabalhe de qualquer lugar',
	GettingStartedNavItem: 'Começando',
	GettingStartedPageTitle: 'Primeiros passos no Carepatron',
	GettingStartedPayments: 'Aceite pagamentos on-line',
	GettingStartedPaymentsDescription: `Receba pagamentos mais rapidamente, permitindo que seus clientes paguem on-line.
 Veja todas as suas faturas e pagamentos em um só lugar`,
	GettingStartedPaymentsTitle: 'Facilite os pagamentos',
	GettingStartedSaveBranding: 'Salvar marca',
	GettingStartedSyncCalendars: 'Sincronize outros calendários',
	GettingStartedSyncCalendarsDescription:
		'Carepatron verifica se há conflitos em seu calendário, então os compromissos só são agendados quando você estiver disponível',
	GettingStartedSyncCalendarsTitle: 'Mantenha-se sempre atualizado',
	GettingStartedVideo: 'Assista a um vídeo de introdução',
	GettingStartedVideoDescription:
		'Os primeiros espaços de trabalho de saúde completos para pequenas equipes e seus clientes',
	GettingStartedVideoTitle: 'Bem-vindo ao Carepatron',
	GetttingStartedGetMobileDownload: 'Baixe o aplicativo',
	GetttingStartedGetMobileNoDownload:
		'Não é compatível com este navegador. Se você estiver usando um iPhone ou iPad, abra esta página no Safari. Caso contrário, tente abri-lo no Chrome.',
	Glossary: 'Glossário',
	Gmail: 'Gmail',
	GmailSendMessagesLimitWarning:
		'O Gmail permite apenas 500 mensagens enviadas da sua conta por dia. Algumas mensagens podem falhar. Deseja continuar?',
	GoToAppointment: 'Ir para consulta',
	GoToApps: 'Ir para aplicativos',
	GoToAvailability: 'Ir para disponibilidade',
	GoToClientList: 'Ir para a lista de clientes',
	GoToClientRecord: 'Ir para o registro do cliente',
	GoToClientSettings: 'Vá para as configurações do cliente agora',
	GoToInvoiceTemplates: 'Ir para modelos de fatura',
	GoToNotificationSettings: 'Vá para as configurações de notificação',
	GoToPaymentSettings: 'Vá para as configurações de pagamento',
	Google: 'Google',
	GoogleCalendar: 'calendário do Google',
	GoogleColor: 'Cor do calendário do Google',
	GoogleMeet: 'Google Meet',
	GoogleTagManagerContainerId: 'ID do contêiner do Gerenciador de tags do Google',
	GotIt: 'Entendi!',
	Goto: 'Vá para',
	Granddaughter: 'Neta',
	Grandfather: 'Avô',
	Grandmother: 'Avó',
	Grandparent: 'Avó',
	Grandson: 'Neto',
	GrantPortalAccess: 'Conceder acesso ao portal',
	GraphicDesigner: 'Designer gráfico',
	Grid: 'Grade',
	GridView: 'Visualização em grade',
	Group: 'Grupo',
	GroupBy: 'Agrupar por',
	GroupEvent: 'Evento de grupo',
	GroupEventHelper: 'Defina limites de participantes para o serviço',
	GroupFilterLabel: 'Todos {group}',
	GroupHealthPlan: 'Group health plan',
	GroupId: 'ID do grupo',
	GroupInputFieldsFormPrimaryText: 'Campos de entrada de grupo',
	GroupInputFieldsFormSecondaryText: 'Escolha ou adicione campos personalizados',
	GuideTo: 'Guia para {value}',
	GuideToImproveVideoQuality: 'Guia para melhorar a qualidade do vídeo',
	GuideToManagingPayers: 'Gerenciando pagadores',
	GuideToSubscriptionsBilling: 'Guia para faturamento de assinaturas',
	GuideToTroubleshooting: 'Guia para solução de problemas',
	Guidelines: 'Diretrizes',
	GuidelinesCategoryDescription: 'Para orientar a tomada de decisão clínica',
	HST: 'HST',
	HairStylist: 'Estilista de cabelo',
	HaveBeenWaiting: 'Você está esperando há muito tempo',
	HeHim: 'Ele/Dele',
	HeaderAccountSettings: 'Perfil',
	HeaderCalendar: 'Calendário',
	HeaderCalls: 'Chamadas',
	HeaderClientAppAccountSettings: 'Configurações de Conta',
	HeaderClientAppCalls: 'Chamadas',
	HeaderClientAppMyDocumentation: 'Documentação',
	HeaderClientAppMyRelationships: 'Meus relacionamentos',
	HeaderClients: 'Clientes',
	HeaderHelp: 'Ajuda',
	HeaderMoreOptions: 'Mais opções',
	HeaderStaff: 'Funcionários',
	HealthCoach: 'Treinador de saúde',
	HealthCoaches: 'Treinadores de saúde',
	HealthEducator: 'Educador de saúde',
	HealthInformationTechnician: 'Técnico de Informação em Saúde',
	HealthPolicyExpert: 'Especialista em Políticas de Saúde',
	HealthServicesAdministrator: 'Administrador de Serviços de Saúde',
	HelpArticles: 'Artigos de ajuda',
	HiddenColumns: 'Colunas ocultas',
	HiddenFields: 'Campos Ocultos',
	HiddenSections: 'Seções ocultas',
	HiddenSectionsAndFields: 'Seções/campos ocultos',
	HideColumn: 'Ocultar coluna',
	HideColumnButton: 'Ocultar botão da coluna {value}',
	HideDetails: 'Detalhes ocultos',
	HideField: 'Ocultar campo',
	HideFullAddress: 'Esconder',
	HideMenu: 'Ocultar menu',
	HideMergeSummarySidebar: 'Ocultar resumo da mesclagem',
	HideSection: 'Ocultar seção',
	HideYourView: 'Esconda sua visão',
	Highlight: 'Cor de destaque',
	Highlighter: 'Marcador',
	History: 'História',
	HistoryItemFooter: '{actors, select, undefined {{date} às {time}} other {Por {actors} • {date} às {time}}}',
	HistorySidePanelEmptyState: 'Nenhum registro de histórico encontrado',
	HistoryTitle: 'Log de atividades',
	HolisticHealthPractitioner: 'Praticante de saúde holística',
	HomeCaregiver: 'Cuidador Domiciliar',
	HomeHealthAide: 'Auxiliar de saúde domiciliar',
	HomelessShelter: 'Abrigo para sem-teto',
	HourAbbreviation: '{count} {count, plural, one {hora} other {horas}}',
	Hourly: 'Horário',
	HoursPlural: '{age, plural, one {# hora} other {# horas}}',
	HowCanWeImprove: 'Como podemos melhorar isso?',
	HowCanWeImproveResponse: 'Como podemos melhorar esta resposta?',
	HowDidWeDo: 'Como nós fizemos?',
	HowDoesReferralWork: 'Guia para o programa de referência',
	HowToUseAiSummarise: 'Como usar o resumo de IA',
	HumanResourcesManager: 'gerente de Recursos Humanos',
	Husband: 'Marido',
	Hypnotherapist: 'Hipnoterapeuta',
	IVA: 'IVA',
	IgnoreNotification: 'Ignorar notificação',
	IgnoreOnce: 'Ignorar uma vez',
	IgnoreSender: 'Ignorar remetente',
	IgnoreSenderDescription:
		'Os remetentes ignorados não serão mais exibidos na sua caixa de entrada. Você pode reverter essa ação a qualquer momento.',
	IgnoreSenders: 'Remetentes ignorados',
	IgnoreSendersSuccess: 'Endereço de e-mail ignorado <mark>{addresses}</mark>',
	Ignored: 'Ignorado',
	Image: 'Imagem',
	Import: 'Importar',
	ImportActivity: 'Importar atividade',
	ImportClientSuccessSnackbarDescription: 'Seu arquivo foi importado com sucesso',
	ImportClientSuccessSnackbarTitle: 'Importação bem sucedida!',
	ImportClients: 'Importar clientes',
	ImportClientsFailureSnackbarDescription: 'Não foi possível importar seu arquivo devido a um erro.',
	ImportClientsFailureSnackbarTitle: 'Importação sem sucesso!',
	ImportClientsGuide: '<h1>Guia para importar clientes</h1>',
	ImportClientsInProgressSnackbarDescription: 'Isso deve levar apenas um minuto para ser concluído.',
	ImportClientsInProgressSnackbarTitle: 'Importando {fileName}',
	ImportClientsModalDescription:
		'Escolha a origem dos seus dados: seja um arquivo no seu dispositivo, um serviço de terceiros ou outra plataforma de software.',
	ImportClientsModalFileUploadHelperText: 'Suporta {fileTypes}. Limite de tamanho {fileSizeLimit}.',
	ImportClientsModalImportGuideLabel: 'Guia para importar dados do cliente',
	ImportClientsModalStep1Label: 'Escolha a fonte de dados',
	ImportClientsModalStep2Label: 'Subir arquivo',
	ImportClientsModalStep3Label: 'Campos de revisão',
	ImportClientsModalTitle: 'Importando os dados do seu cliente',
	ImportClientsPreviewClientsReadyForImport:
		'{count} {count, plural, one {cliente} other {clientes}} pronto para importar',
	ImportContactFailedNotificationSubject: 'A importação dos seus dados falhou',
	ImportDataSourceSelectorLabel: 'Importar fonte de dados de',
	ImportDataSourceSelectorPlaceholder: 'Pesquise ou escolha a fonte de dados de importação',
	ImportExportButton: 'Importar/Exportar',
	ImportFailed: 'Falha na importação',
	ImportFromAnotherPlatformTileDescription: 'Baixe um arquivo de exportação dos seus clientes e faça o upload aqui.',
	ImportFromAnotherPlatformTileLabel: 'Importar de outra plataforma',
	ImportGuide: 'Guia de importação',
	ImportInProgress: 'Importação em andamento',
	ImportProcessing: 'Importando o processamento...',
	ImportSpreadsheetDescription:
		'Você pode importar sua lista de clientes existente para o Carepatron enviando um arquivo de planilha com dados tabulares, como .CSV, .XLS ou .XLSX',
	ImportSpreadsheetTitle: 'Importe seu arquivo de planilha',
	ImportTemplates: 'Importar modelos',
	Importing: 'Importando',
	ImportingCalendarProductEvents: 'Importando eventos de {product}',
	ImportingData: 'Importando dados',
	ImportingSpreadsheetDescription: 'Isso deve levar apenas um minuto para ser concluído',
	ImportingSpreadsheetTitle: 'Importando sua planilha',
	ImportsInProgress: 'Importações em andamento',
	InPersonMeeting: 'Reunião presencial',
	InProgress: 'Em andamento',
	InTransit: 'Em trânsito',
	InTransitTooltip:
		'O saldo em trânsito inclui todos os pagamentos de faturas pagas do Stripe para sua conta bancária. Esses fundos normalmente levam de 3 a 5 dias para serem liquidados.',
	Inactive: 'Inativo',
	InboundOrOutboundCalls: 'Chamadas recebidas ou efetuadas',
	Inbox: 'Caixa de entrada',
	InboxAccessRestricted:
		'Acesso restrito. Entre em contato com o proprietário da caixa de entrada para obter permissões.',
	InboxAccountAlreadyConnected: 'O canal que você tentou conectar já está conectado ao Carepatron',
	InboxAddAttachments: 'Adicionar Anexos',
	InboxAreYouSureDeleteMessage: 'Tem certeza de que deseja excluir esta mensagem?',
	InboxBulkCloseSuccess:
		'{count, plural, one {Fechado com sucesso # conversa} other {Fechado com sucesso # conversas}}',
	InboxBulkComposeModalTitle: 'Compor mensagem em massa',
	InboxBulkDeleteSuccess:
		'{count, plural, one {Conversação excluída com sucesso} other {Conversações excluídas com sucesso}}',
	InboxBulkReadSuccess:
		'{count, plural, one {Marcou # conversa como lida com sucesso} other {Marcou # conversas como lidas com sucesso}}',
	InboxBulkReopenSuccess:
		'{count, plural, one {Re abriu com sucesso # conversa} other {Re abriu com sucesso # conversas}}',
	InboxBulkUnreadSuccess:
		'{count, plural, one {Marcada # conversa como não lida com sucesso} other {Marcadas # conversas como não lidas com sucesso}}',
	InboxChatCreateGroup: 'Criar grupo',
	InboxChatDeleteGroupModalDescription:
		'Tem certeza de que deseja excluir este grupo? Todas as mensagens e anexos serão excluídos.',
	InboxChatDeleteGroupModalTitle: 'Excluir grupo',
	InboxChatDiscardDraft: 'Descartar rascunho',
	InboxChatDragDropText: 'Arraste os arquivos aqui para enviar',
	InboxChatGroupConversation: 'Conversa em grupo',
	InboxChatGroupCreateModalDescription:
		'Comece um novo grupo para Mensagens e colaboração com sua equipe, clientes ou comunidade.',
	InboxChatGroupCreateModalTitle: 'Criar grupo',
	InboxChatGroupMembers: 'Membros do grupo',
	InboxChatGroupModalGroupNameFieldLabel: 'Nome do grupo',
	InboxChatGroupModalGroupNameFieldPlaceholder: 'E.g. suporte ao cliente, administrador',
	InboxChatGroupModalGroupNameFieldRequired: 'Este campo é obrigatório',
	InboxChatGroupModalMembersFieldErrorMinimumOne: 'Mínimo um membro necessário',
	InboxChatGroupModalMembersFieldLabel: 'Escolha os membros do grupo',
	InboxChatGroupModalMembersFieldPlaceholder: 'Escolha membros',
	InboxChatGroupUpdateModalTitle: 'Gerenciar grupo',
	InboxChatLeaveGroup: 'Sair do grupo',
	InboxChatLeaveGroupModalDescription:
		'Tem certeza de que deseja sair deste grupo? Você não receberá mais mensagens ou atualizações.',
	InboxChatLeaveGroupModalTitle: 'Sair do grupo',
	InboxChatLeftGroupMessage: 'Mensagem do grupo à esquerda',
	InboxChatManageGroup: 'Gerenciar grupo',
	InboxChatSearchParticipants: 'Escolher destinatários',
	InboxCloseConversationSuccess: 'Conversa encerrada com sucesso',
	InboxCompose: 'Compor',
	InboxComposeBulk: 'Mensagem em massa',
	InboxComposeCarepatronChat: 'Mensageiro',
	InboxComposeChat: 'Compor bate-papo',
	InboxComposeDisabledNoConnection: 'Conecte uma conta de e-mail para enviar mensagens.',
	InboxComposeDisabledNoPermissionTooltip: 'Você não tem permissão para enviar mensagens desta caixa de entrada.',
	InboxComposeEmail: 'Escrever um email',
	InboxComposeMessageFrom: 'De',
	InboxComposeMessageRecipientBcc: 'Cco',
	InboxComposeMessageRecipientCc: 'CC',
	InboxComposeMessageRecipientTo: 'Para',
	InboxComposeMessageSubject: 'Assunto:',
	InboxConnectAccountButton: 'Conecte seu e-mail',
	InboxConnectedDescription: 'Sua caixa de entrada não tem comunicações',
	InboxConnectedHeading: 'Suas conversas aparecerão aqui assim que você começar a trocar comunicações',
	InboxConnectedHeadingClientView: 'Simplifique as comunicações com seus clientes',
	InboxCreateFirstInboxButton: 'Crie sua primeira caixa de entrada',
	InboxCreationSuccess: 'Caixa de entrada criada com sucesso',
	InboxDeleteAttachment: 'Excluir anexo',
	InboxDeleteConversationSuccess: 'Conversa excluída com sucesso',
	InboxDeleteMessage: 'Excluir mensagem?',
	InboxDirectMessage: 'Mensagem direta',
	InboxEditDraft: 'Editar rascunho',
	InboxEmailComposeReplyEmail: 'Escrever resposta',
	InboxEmailDraft: 'Rascunho',
	InboxEmailNotFound: 'email não encontrado',
	InboxEmailSubjectFieldInformation: 'Alterar a linha de assunto criará um novo e-mail encadeado.',
	InboxEmptyArchiveDescription: 'Nenhuma conversa arquivada foi encontrada',
	InboxEmptyBinDescription: 'Nenhuma conversa excluída foi encontrada',
	InboxEmptyBinHeading: 'Tudo claro, nada para ver aqui',
	InboxEmptyBinSuccess: 'Conversas excluídas com sucesso',
	InboxEmptyCongratsHeading: 'Bom trabalho! Sente-se e relaxe até a próxima conversa',
	InboxEmptyDraftDescription: 'Nenhum rascunho de conversa foi encontrado',
	InboxEmptyDraftHeading: 'Tudo claro, nada para ver aqui',
	InboxEmptyOtherDescription: 'Nenhuma conversa foi encontrada',
	InboxEmptyScheduledHeading: 'Tudo limpo, nenhuma conversa agendada para envio',
	InboxEmptySentDescription: 'Nenhuma conversa enviada foi encontrada',
	InboxForward: 'Avançar',
	InboxGroupClientsLabel: 'Todos os clientes',
	InboxGroupClientsOverviewLabel: 'Clientes',
	InboxGroupClientsSelectedItemPrefix: 'Cliente',
	InboxGroupStaffsLabel: 'Toda a equipe',
	InboxGroupStaffsOverviewLabel: 'Equipe',
	InboxGroupStaffsSelectedItemPrefix: 'Equipe',
	InboxGroupStatusLabel: 'Todos os status',
	InboxGroupStatusOverviewLabel: 'Enviar para um status',
	InboxGroupStatusSelectedItemPrefix: 'Status',
	InboxGroupTagsLabel: 'Todas as tags',
	InboxGroupTagsOverviewLabel: 'Enviar para uma tag',
	InboxGroupTagsSelectedItemPrefix: 'Tag',
	InboxHideQuotedText: 'Ocultar texto citado',
	InboxIgnoreConversationSuccess: 'Conversa ignorada com sucesso',
	InboxMessageAllLabelRecipientsCount: 'Todos os Destinatários {label} ({count})',
	InboxMessageBodyPlaceholder: 'Adicione sua mensagem',
	InboxMessageDeleted: 'Mensagem apagada',
	InboxMessageMarkedAsRead: 'Mensagem marcada como lida',
	InboxMessageMarkedAsUnread: 'Mensagem marcada como não lida',
	InboxMessageSentViaChat: '**Enviado via chat**  • {time} por {name}',
	InboxMessageShowMoreRecipients: '+{count} mais',
	InboxMessageWasDeleted: 'Esta mensagem foi deletada',
	InboxNoConnectionDescription: 'Conecte sua conta de e-mail ou crie caixas de entrada com vários e-mails',
	InboxNoConnectionHeading: 'Integre as comunicações do seu cliente',
	InboxNoDirectMessage: 'Sem mensagens recentes',
	InboxRecentConversations: 'Recente',
	InboxReopenConversationSuccess: 'Conversa reaberta com sucesso',
	InboxReply: 'Responder',
	InboxReplyAll: 'Responder todos',
	InboxRestoreConversationSuccess: 'Conversa restaurada com sucesso',
	InboxScheduleSendCancelSendSuccess: 'Envio agendado cancelado e mensagem revertida para rascunho',
	InboxScheduleSendMessageSuccessDescription: 'Enviar agendado para {date}',
	InboxScheduleSendMessageSuccessTitle: 'Agendamento de envio',
	InboxSearchForConversations: 'Pesquisar por "{query}"',
	InboxSendMessageSuccess: 'Conversa enviada com sucesso',
	InboxSettings: 'Configurações da caixa de entrada',
	InboxSettingsAppsDesc:
		'Gerencie aplicativos conectados para esta caixa de entrada compartilhada: adicione ou remova conexões conforme necessário.',
	InboxSettingsAppsNewConnectedApp: 'Novo aplicativo conectado',
	InboxSettingsAppsTitle: 'Aplicativos conectados',
	InboxSettingsDeleteAccountFailed: 'Falha ao excluir conta da caixa de entrada',
	InboxSettingsDeleteAccountSuccess: 'Conta da caixa de entrada excluída com sucesso',
	InboxSettingsDeleteAccountWarning:
		'Remover {email} irá desconectá-lo da caixa de entrada {inboxName} e interromperá a sincronização de mensagens.',
	InboxSettingsDeleteInboxFailed: 'Falha ao excluir a caixa de entrada',
	InboxSettingsDeleteInboxSuccess: 'Caixa de entrada excluída com sucesso',
	InboxSettingsDeleteInboxWarning:
		'Excluir {inboxName} desconectará todos os canais conectados e excluirá todas as mensagens associadas a esta caixa de entrada. 		Esta ação é permanente e não pode ser desfeita.',
	InboxSettingsDetailsDesc:
		'Caixa de entrada de comunicação para sua equipe gerenciar as mensagens dos clientes com eficiência.',
	InboxSettingsDetailsTitle: 'Detalhes da caixa de entrada',
	InboxSettingsEmailSignatureLabel: 'Assinatura de email padrão',
	InboxSettingsReplyFormatDesc:
		'Configure seu endereço de resposta padrão e assinatura de email para serem exibidos consistentemente, independentemente de quem está enviando o email.',
	InboxSettingsReplyFormatTitle: 'Formato de resposta',
	InboxSettingsSendFromLabel: 'Definir um remetente padrão',
	InboxSettingsStaffDesc:
		'Gerencie o acesso dos membros da equipe a esta caixa de entrada compartilhada para uma colaboração perfeita.',
	InboxSettingsStaffTitle: 'Atribuir membros da equipe',
	InboxSettingsUpdateInboxDetailsFailed: 'Falha ao atualizar os detalhes da caixa de entrada',
	InboxSettingsUpdateInboxDetailsSuccess: 'Detalhes da caixa de entrada atualizados com sucesso',
	InboxSettingsUpdateInboxStaffsFailed: 'Falha ao atualizar os membros da equipe da caixa de entrada',
	InboxSettingsUpdateInboxStaffsSuccess: 'Membros da equipe da caixa de entrada atualizados com sucesso',
	InboxSettingsUpdateReplyFormatFailed: 'Falha ao atualizar o formato de resposta',
	InboxSettingsUpdateReplyFormatSuccess: 'Formato de resposta atualizado com sucesso',
	InboxShowQuotedText: 'Mostrar texto citado',
	InboxStaffRoleAdminDescription: 'Visualize, responda e gerencie caixas de entrada',
	InboxStaffRoleResponderDescription: 'Ver e responder',
	InboxStaffRoleViewerDescription: 'Somente visualizar',
	InboxSuggestMoveToBulkComposeMessageActionCancel: 'Continuar editando',
	InboxSuggestMoveToBulkComposeMessageActionConfirm: 'Sim, mudar para envio em massa',
	InboxSuggestMoveToBulkComposeMessageContent:
		'Você escolheu mais de {count} destinatários. Deseja enviar como e-mail em massa?',
	InboxSuggestMoveToBulkComposeMessageTitle: 'Aviso',
	InboxSwitchToOtherInbox: 'Mudar para outra caixa de entrada',
	InboxUndoSendMessageSuccess: 'Envio desfeito',
	IncludeLineItems: 'Incluir itens de linha',
	IncludeSalesTax: 'Tributável',
	IncludesAiSmartPrompt: 'Inclui prompts inteligentes de IA',
	Incomplete: 'Incompleto',
	IncreaseIndent: 'Aumentar recuo',
	IndianHealthServiceFreeStandingFacility: 'Instalação autônoma do Indian Health Service',
	IndianHealthServiceProviderFacility: 'Instalação baseada em provedor de serviços de saúde indianos',
	Information: 'Informação',
	InitialAssessment: 'Avaliação Inicial',
	InitialSignupPageClientFamilyTitle: 'Cliente ou familiar',
	InitialSignupPageProviderTitle: 'Saúde ',
	InitialTreatment: 'Tratamento inicial',
	Initials: 'Iniciais',
	InlineEmbed: 'Embed inline',
	InputPhraseToConfirm: 'Para confirmar, digite {confirmationPhrase}.',
	Insert: 'Inserir',
	InsertTable: 'Insira a tabela',
	InstallCarepatronOnYourIphone1: 'Instale o Carepatron no seu iOS: toque em',
	InstallCarepatronOnYourIphone2: 'e depois Adicionar à tela inicial',
	InsufficientCalendarScopesSnackbar: 'Falha na sincronização - conceda permissões de calendário ao Carepatron',
	InsufficientInboxScopesSnackbar: 'Falha na sincronização - conceda permissões de e-mail ao Carepatron',
	InsufficientScopeErrorCodeSnackbar: 'Falha na sincronização - conceda todas as permissões ao Carepatron',
	Insurance: 'Seguros',
	InsuranceAmount: 'Valor do seguro',
	InsuranceClaim: 'Reivindicação de seguro',
	InsuranceClaimAiChatPlaceholder: 'Pergunte sobre o pedido de seguro...',
	InsuranceClaimAiClaimNumber: 'Reivindicação {number}',
	InsuranceClaimAiSubtitle: 'Faturamento de seguros • Validação de sinistros',
	InsuranceClaimDeniedSubject: 'A Reivindicação {claimNumber} enviada para {payerNumber} {payerName} foi negada',
	InsuranceClaimErrorDescription:
		'A declaração contém erros relatados pelo pagador ou clearing house. Por favor, revise as mensagens de erro a seguir e reenvie a declaração.',
	InsuranceClaimErrorGuideLink: 'Guia de Reivindicações de Seguros',
	InsuranceClaimErrorTitle: 'Erros no envio de reivindicações',
	InsuranceClaimNotFound: 'Reivindicação de seguro não encontrada',
	InsuranceClaimPaidSubject:
		'{isPartiallyPaid, select, true {Um pagamento parcial de {paymentAmount}} other {Um pagamento de {paymentAmount}}} para a reclamação {claimNumber} por {payerNumber} {payerName} foi registrado',
	InsuranceClaimRejectedSubject: 'Claim {claimNumber} submetido a {payerNumber} {payerName} foi rejeitado',
	InsuranceClaims: 'Sinistros de seguro',
	InsuranceInformation: 'Informações sobre seguros',
	InsurancePaid: 'Seguro pago',
	InsurancePayer: 'Pagador de seguro',
	InsurancePayers: 'Pagadores de seguros',
	InsurancePayersDescription: 'Visualize os pagadores que foram adicionados à sua conta e gerencie a inscrição.',
	InsurancePayment: 'Pagamento de seguro',
	InsurancePoliciesDetailsSubtitle: 'Adicione informações de seguro do cliente para dar suporte às reivindicações.',
	InsurancePoliciesDetailsTitle: 'Detalhes das políticas',
	InsurancePoliciesListSubtitle: 'Adicione informações de seguro do cliente para dar suporte às reivindicações.',
	InsurancePoliciesListTitle: 'Apólices de seguro',
	InsuranceSelfPay: 'Pagamento próprio',
	InsuranceType: 'Tipo de seguro',
	InsuranceUnpaid: 'Seguro não pago',
	Intake: 'Ingestão',
	IntakeExpiredErrorCodeSnackbar:
		'Esta ingestão expirou. Entre em contato com seu provedor para reenviar outra entrada.',
	IntakeNotFoundErrorSnackbar:
		'Esta ingestão não foi encontrada. Entre em contato com seu provedor para reenviar outra ingestão.',
	IntakeProcessLearnMoreInstructions: 'Guia para configurar seus formulários de admissão',
	IntakeTemplateSelectorPlaceholder: 'Escolha formulários e contratos para enviar ao seu cliente para preenchimento',
	Integration: 'Integração',
	IntenseBlur: 'Desfoque intensamente o fundo',
	InteriorDesigner: 'Designer de interiores',
	InternetBanking: 'transferência bancária',
	Interval: 'Intervalo',
	IntervalDays: 'Intervalo (dias)',
	IntervalHours: 'Intervalo (horas)',
	Invalid: 'Inválido',
	InvalidDate: 'Data inválida',
	InvalidDateFormat: 'Data deve estar no formato {format}',
	InvalidDisplayName: 'Nome de exibição não pode conter {value}',
	InvalidEmailFormat: 'Formato de email inválido',
	InvalidFileType: 'Tipo de arquivo inválido',
	InvalidGTMContainerId: 'Formato de ID de contêiner GTM inválido',
	InvalidPaymentMethodCode: 'O método de pagamento selecionado não é válido. Por favor escolha outro.',
	InvalidPromotionCode: 'Código de Promoção inválido',
	InvalidReferralDescription: 'Já usando Carepatron',
	InvalidStatementDescriptor: `O descritor de instrução deve ter entre 5 e 22 caracteres e conter apenas letras, números, espaços e não deve incluir  <, >, \\, ', ", *`,
	InvalidToken: 'Token inválido',
	InvalidTotpSetupVerificationCode: 'Código de verificação inválido.',
	InvalidURLErrorText: 'Este deve ser um URL válido',
	InvalidZoomTokenErrorCodeSnackbar: 'O token Zoom expirou. Reconecte seu aplicativo Zoom e tente novamente.',
	Invite: 'Convidar',
	InviteRelationships: 'Convide relacionamentos',
	InviteToPortal: 'Convidar para o portal',
	InviteToPortalModalDescription: 'Um e-mail de convite será enviado ao seu cliente para se inscrever no Carepatron.',
	InviteToPortalModalTitle: 'Convide {name} para o Portal Carepatron',
	InviteUserDescription: ' ',
	InviteUserTitle: 'Convidar novo usuário',
	Invited: 'Convidamos',
	Invoice: 'Fatura',
	InvoiceColorPickerDescription: 'Tema de cores a ser usado na fatura',
	InvoiceColorTheme: 'Tema de cores da fatura',
	InvoiceContactDeleted: 'O contato da fatura foi excluído e esta fatura não pode ser atualizada.',
	InvoiceDate: 'Data de emissão',
	InvoiceDetails: 'Detalhes da fatura',
	InvoiceFieldsPlaceholder: 'Pesquisar campos...',
	InvoiceFrom: 'Fatura {number} de {fromProvider}',
	InvoiceInvalidCredit: 'Valor de crédito inválido, o valor do crédito não pode exceder o total da fatura',
	InvoiceNotFoundDescription: 'Entre em contato com seu fornecedor e peça mais informações ou reenvie a fatura.',
	InvoiceNotFoundTitle: 'Fatura não encontrada',
	InvoiceNumber: 'Fatura #',
	InvoiceNumberFormat: 'Fatura #{number}',
	InvoiceNumberMustEndWithDigit: 'O número da fatura deve terminar com um dígito (0-9)',
	InvoicePageHeader: 'Faturas',
	InvoicePaidNotificationSubject: 'Fatura {invoiceNumber} paga',
	InvoiceReminder: 'Lembretes de fatura',
	InvoiceReminderSentence:
		'Enviar lembrete de {deliveryType} {interval} {unit} {beforeAfter} a data de vencimento da fatura',
	InvoiceReminderSettings: 'Configurações de lembrete de fatura',
	InvoiceReminderSettingsInfo: 'Os lembretes se aplicam apenas a faturas enviadas no Carepatron',
	InvoiceReminders: 'Lembretes de fatura',
	InvoiceRemindersInfo:
		'Defina lembretes automáticos para datas de vencimento de faturas. Os lembretes aplicam-se apenas a faturas enviadas através da Carepatron',
	InvoiceSettings: 'Configurações de fatura',
	InvoiceStatus: 'Status da fatura',
	InvoiceTemplateAddressPlaceholder: '123 Main St, Anytown, EUA',
	InvoiceTemplateDescriptionPlaceholder:
		'Adicione notas, detalhes de transferência bancária ou termos e condições para pagamentos alternativos',
	InvoiceTemplateEmploymentStatusPlaceholder: 'Trabalhadores por conta própria',
	InvoiceTemplateEthnicityPlaceholder: 'caucasiano',
	InvoiceTemplateNotFoundDescription: 'Entre em contato com seu provedor e peça mais informações.',
	InvoiceTemplateNotFoundTitle: 'Modelo de fatura não encontrado',
	InvoiceTemplates: 'Modelos de fatura',
	InvoiceTemplatesDescription:
		'Personalize seus modelos de fatura para refletir sua marca, atender aos requisitos regulatórios e atender às preferências do cliente com nossos modelos fáceis de usar.',
	InvoiceTheme: 'Tema da fatura',
	InvoiceTotal: 'Total da Fatura',
	InvoiceUninvoicedAmounts: 'Faturar valores não faturados',
	InvoiceUpdateVersionMessage:
		'A edição desta fatura requer a versão mais recente. Por favor, recarregue o Carepatron e tente novamente.',
	Invoices: '{count, plural, one {Fatura} other {Faturas}}',
	InvoicesEmptyStateDescription: 'Nenhuma fatura foi encontrada',
	InvoicingAndPayment: 'Faturamento ',
	Ireland: 'Irlanda',
	IsA: 'é um',
	IsBetween: 'está entre',
	IsEqualTo: 'é igual a',
	IsGreaterThan: 'é maior que',
	IsGreaterThanOrEqualTo: 'é maior ou igual a',
	IsLessThan: 'é menor que',
	IsLessThanOrEqualTo: 'é menor ou igual a',
	IssueCredit: 'Emitir crédito',
	IssueCreditAdjustment: 'Emitir ajuste de crédito',
	IssueDate: 'Data de emissão',
	Italic: 'itálico',
	Items: 'Unid',
	ItemsAndAdjustments: 'Itens e ajustes',
	ItemsRemaining: '+{count} itens restantes',
	JobTitle: 'Cargo',
	Join: 'Juntar',
	JoinCall: 'Participar da chamada',
	JoinNow: 'Entrar',
	JoinProduct: 'Junte-se ao {product}',
	JoinVideoCall: 'Participe da videochamada',
	JoinWebinar: 'Participe do webinar',
	JoinWithVideoCall: 'Junte-se ao {product}',
	Journalist: 'Jornalista',
	JustMe: 'Apenas eu',
	JustYou: 'Só você',
	Justify: 'Justificar',
	KeepSeparate: 'Mantenha separado',
	KeepSeparateSuccessMessage: 'Você manteve registros separados para {clientNames} com sucesso.',
	KeepWaiting: 'Continue esperando',
	Label: 'Rótulo',
	LabelOptional: 'Etiqueta (opcional)',
	LactationConsulting: 'Consultoria em lactação',
	Language: 'Linguagem',
	Large: 'Grande',
	LastDxCode: 'Último código DX',
	LastLoggedIn: 'Último acesso em {date} às {time}',
	LastMenstrualPeriod: 'Último período menstrual',
	LastMonth: 'Mês passado',
	LastNDays: 'Últimos {number} dias',
	LastName: 'Sobrenome',
	LastNameFirstInitial: 'Sobrenome, primeira inicial',
	LastWeek: 'Semana passada',
	LastXRay: 'Último raio-x',
	LatestVisitOrConsultation: 'Última visita ou consulta',
	Lawyer: 'Advogado',
	LearnMore: 'Saber mais',
	LearnMoreTipsToGettingStarted: 'Saiba mais dicas para começar',
	LearnToSetupInbox: 'Aprenda como configurar uma conta de caixa de entrada',
	Leave: 'Deixar',
	LeaveCall: 'Sair da chamada',
	LeftAlign: 'Alinhar à esquerda',
	LegacyBillingItemsNotAvailable:
		'Itens de cobrança individuais ainda não estão disponíveis para este agendamento. Você ainda pode faturá-lo normalmente.',
	LegacyBillingItemsNotAvailableTitle: 'Faturamento legado',
	LegalAndConsent: 'Legal e consentimento',
	LegalConsentFormPrimaryText: 'Consentimento legal',
	LegalConsentFormSecondaryText: 'Aceitar ou recusar opções',
	LegalGuardian: 'Guardião legal',
	Letter: 'Carta',
	LettersCategoryDescription: 'Para criar correspondências clínicas e administrativas',
	Librarian: 'Bibliotecário',
	LicenseNumber: 'Número de licença',
	LifeCoach: 'Treinador de vida',
	LifeCoaches: 'Treinadores de vida',
	Limited: 'Limitado',
	LineSpacing: 'Espaçamento entre linhas e parágrafos',
	LinearScaleFormPrimaryText: 'Escala linear',
	LinearScaleFormSecondaryText: 'Opções de escala 1-10',
	Lineitems: 'Itens de linha',
	Link: 'Link',
	LinkClientFormSearchClientLabel: 'Procurar um cliente',
	LinkClientModalTitle: 'Link para cliente existente',
	LinkClientSuccessDescription:
		'**{newName}**’s informações de contato foram adicionadas ao registro de **{existingName}**.',
	LinkClientSuccessTitle: 'Vinculado com sucesso ao contato existente',
	LinkForCallCopied: 'Link copiado!',
	LinkToAnExistingClient: 'Link para um cliente existente',
	LinkToClient: 'Link para o cliente',
	ListAndTracker: 'Lista/Rastreador',
	ListPeopleInThisMeeting: `{n, plural, 			one {{attendees} está nesta chamada}
			other {{attendees} estão nesta chamada}
		}`,
	ListStyles: 'Estilos de lista',
	ListsAndTrackersCategoryDescription: 'Para organizar e acompanhar o trabalho',
	LivingArrangements: 'Arranjos de Vida',
	LoadMore: 'Carregue mais',
	Loading: 'Carregando...',
	LocalizationPanelDescription: 'Gerencie as configurações de seu idioma e fuso horário',
	LocalizationPanelTitle: 'Idioma e fuso horário',
	Location: 'Localização',
	LocationDescription:
		'Configure locais físicos e virtuais com endereços, nomes de salas e tipos de espaços virtuais específicos para facilitar o agendamento de compromissos e videochamadas.',
	LocationNumber: 'Número de localização',
	LocationOfService: 'Localização do serviço',
	LocationOfServiceRecommendedActionInfo:
		'Adicionar uma localização específica a este serviço pode afetar sua disponibilidade.',
	LocationRemote: 'Remoto',
	LocationType: 'Tipo de localização',
	Locations: 'Localizações',
	Lock: 'Trancar',
	Locked: 'Bloqueado',
	LockedNote: 'Nota bloqueada',
	LogInToSaveOrAuthoriseCard: 'Faça login para salvar ou autorizar o cartão',
	LogInToSaveOrAuthorisePayment: 'Faça login para salvar ou autorizar o pagamento',
	Login: 'Conecte-se',
	LoginButton: 'Entrar',
	LoginEmail: 'E-mail',
	LoginForgotPasswordLink: 'Esqueceu sua senha',
	LoginPassword: 'Senha',
	Logo: 'Logotipo',
	LogoutAreYouSure: 'Saia deste dispositivo.',
	LogoutButton: 'sair',
	London: 'Londres',
	LongTextAnswer: 'Resposta de texto longo',
	LongTextFormPrimaryText: 'Texto longo',
	LongTextFormSecondaryText: 'Opções de estilo de parágrafo',
	Male: 'Macho',
	Manage: 'Gerenciar',
	ManageAllClientTags: 'Gerenciar todas as tags do cliente',
	ManageAllNoteTags: 'Gerenciar todas as tags de notas',
	ManageAllTemplateTags: 'Gerenciar todas as tags do modelo',
	ManageConnections: 'Administrar conexões',
	ManageConnectionsGmailDescription: 'Outros membros da equipe não poderão ver seu Gmail sincronizado.',
	ManageConnectionsGoogleCalendarDescription:
		'Outros membros da equipe não poderão ver seus calendários sincronizados. Os compromissos dos clientes só podem ser atualizados ou excluídos no Carepatron.',
	ManageConnectionsInboxSyncHelperText:
		'Vá para a página Caixa de entrada para gerenciar as configurações da Caixa de entrada sincronizada.',
	ManageConnectionsMicrosoftCalendarDescription:
		'Outros membros da equipe não poderão ver seus calendários sincronizados. Os compromissos dos clientes só podem ser atualizados ou excluídos de dentro do Carepatron.',
	ManageConnectionsOutlookDescription: 'Outros membros da equipe não poderão ver seu Microsoft Outlook sincronizado.',
	ManageInboxAccountButton: 'Nova caixa de entrada',
	ManageInboxAccountEdit: 'Gerenciar caixa de entrada',
	ManageInboxAccountPanelTitle: 'Caixas de entrada',
	ManageInboxAssignTeamPlaceholder: 'Escolha membros da equipe para acesso à caixa de entrada',
	ManageInboxBasicInfoColor: 'Cor',
	ManageInboxBasicInfoDescription: 'Descrição',
	ManageInboxBasicInfoDescriptionPlaceholder: 'Para que você ou sua equipe usarão esta caixa de entrada?',
	ManageInboxBasicInfoName: 'Nome da caixa de entrada',
	ManageInboxBasicInfoNamePlaceholder: 'Por exemplo, suporte ao cliente, administrador',
	ManageInboxConnectAppAlreadyConnectedError: 'O canal que você tentou conectar já está conectado ao Carepatron',
	ManageInboxConnectAppConnect: 'Conectar',
	ManageInboxConnectAppConnectedInfo: 'Conectado a uma conta',
	ManageInboxConnectAppContinue: 'Continuar',
	ManageInboxConnectAppEmail: 'E-mail',
	ManageInboxConnectAppSignInWith: 'Entrar com',
	ManageInboxConnectAppSubtitle:
		'Conecte seus aplicativos para enviar, receber e rastrear todas as suas comunicações em um local centralizado.',
	ManageInboxNewInboxTitle: 'Nova caixa de entrada',
	ManagePlan: 'Gerenciar plano',
	ManageProfile: 'Gerenciar perfil',
	ManageReferralsModalDescription: 'Ajude-nos a divulgar nossa plataforma de saúde e ganhe recompensas.',
	ManageReferralsModalTitle: 'Indique um amigo, ganhe recompensas!',
	ManageStaffRelationshipsAddButton: 'Gerenciar relacionamentos',
	ManageStaffRelationshipsEmptyStateText: 'Nenhum relacionamento adicionado',
	ManageStaffRelationshipsModalDescription:
		'Selecionar clientes adicionará novos relacionamentos, enquanto desmarcá-los removerá os relacionamentos existentes.',
	ManageStaffRelationshipsModalTitle: 'Gerenciar relacionamentos',
	ManageStatuses: 'Gerenciar status',
	ManageStatusesActiveStatusHelperText: 'É necessário pelo menos um status ativo',
	ManageStatusesDescription:
		'Personalize seus rótulos de status e escolha cores para alinhá-los ao seu fluxo de trabalho.',
	ManageStatusesSuccessSnackbar: 'Status atualizados com sucesso',
	ManageTags: 'Gerenciar tags',
	ManageTaskAttendeeStatus: 'Gerenciar status de agendamento',
	ManageTaskAttendeeStatusDescription:
		'Personalize seus status de agendamento para se alinhar ao seu fluxo de trabalho.',
	ManageTaskAttendeeStatusHelperText: 'Pelo menos um status é necessário',
	ManageTaskAttendeeStatusSubtitle: 'Status personalizados',
	ManagedClaimMd: 'Claim.MD',
	Manual: 'Manual',
	ManualAppointment: 'Agendamento manual',
	ManualPayment: 'Pagamento manual',
	ManuallyTypeLocation: 'Digite manualmente o local',
	MapColumns: 'Colunas do mapa',
	MappingRequired: 'Mapeamento necessário',
	MarkAllAsRead: 'marcar tudo como lido',
	MarkAsCompleted: 'Marcar como concluído',
	MarkAsManualSubmission: 'Marcar como enviado',
	MarkAsPaid: 'Marcar como pago',
	MarkAsRead: 'marcar como Lido',
	MarkAsUnpaid: 'Marcar como não pago',
	MarkAsUnread: 'marcar como não lido',
	MarkAsVoid: 'Marcar como nulo',
	Marker: 'Marcador',
	MarketingManager: 'Gerente de marketing',
	MassageTherapist: 'Massoterapeuta',
	MassageTherapists: 'Massoterapeutas',
	MassageTherapy: 'Massoterapia',
	MaxBookingTimeDescription1: 'Os clientes podem agendar até',
	MaxBookingTimeDescription2: 'no futuro',
	MaxBookingTimeLabel: '{timePeriod} com antecedência',
	MaxCapacity: 'capacidade máxima',
	Maximize: 'Maximizar',
	MaximumAttendeeLimit: 'Limite máximo',
	MaximumBookingTime: 'Tempo máximo de reserva',
	MaximumBookingTimeError: 'Tempo máximo de reserva não deve exceder {valueUnit}',
	MaximumMinimizedPanelsReachedDescription:
		'Você pode minimizar até {count} painéis laterais por vez. Prosseguir fechará o painel minimizado mais antigo. Você deseja continuar?',
	MaximumMinimizedPanelsReachedTitle: 'Você tem muitos painéis abertos.',
	MechanicalEngineer: 'Engenheiro mecânico',
	MediaGallery: 'Galeria de mídia',
	Medicaid: 'Medicaid',
	MedicaidProviderNumber: 'Número do provedor do Medicaid',
	MedicalAssistant: 'Assistente médico',
	MedicalCoder: 'Codificador Médico',
	MedicalDoctor: 'Médico',
	MedicalIllustrator: 'Ilustrador Médico',
	MedicalInterpreter: 'Intérprete Médico',
	MedicalTechnologist: 'Tecnólogo Médico',
	Medicare: 'Medicare',
	MedicareProviderNumber: 'Número do provedor do Medicare',
	Medicine: 'Medicamento',
	Medium: 'Médio',
	Meeting: 'Reunião',
	MeetingEnd: 'Encerrar reunião',
	MeetingEnded: 'Reunião encerrada',
	MeetingHost: 'Anfitrião da reunião',
	MeetingLowerHand: 'Mão inferior',
	MeetingOpenChat: 'Abrir bate-papo',
	MeetingPersonRaisedHand: '{name} levantou a mão',
	MeetingRaiseHand: 'Levante a mão',
	MeetingReady: 'Reunião pronta',
	MeetingTimerMessage: '{timer} {timerMin, plural, one {min} other {mins}} {status}',
	Meetings: 'Reuniões',
	MemberId: 'ID do membro',
	MentalHealth: 'Saúde mental',
	MentalHealthPractitioners: 'Profissionais de saúde mental',
	MentalHealthProfessional: 'Profissional de Saúde Mental',
	Merge: 'Mesclar',
	MergeClientRecords: 'Mesclar registros de clientes',
	MergeClientRecordsDescription: 'Mesclar registros de clientes combinará todos os seus dados, incluindo:',
	MergeClientRecordsDescription2: 'Você deseja continuar com a mesclagem? Esta ação não pode ser desfeita',
	MergeClientRecordsItem1: 'Notas e documentos',
	MergeClientRecordsItem2: 'Agendamentos',
	MergeClientRecordsItem3: 'Faturas',
	MergeClientRecordsItem4: 'Conversas',
	MergeClientsSuccess: 'Registro do cliente mesclado com sucesso',
	MergeLimitExceeded: 'Você só pode mesclar até 4 clientes por vez.',
	Message: 'Mensagem',
	MessageAttachments: '{total} anexos',
	Method: 'Método',
	MfaAvailabilityDisclaimer:
		'O MFA está disponível apenas para logins por e-mail e senha. Para fazer alterações nas suas configurações de MFA, faça login usando seu e-mail e senha.',
	MfaDeviceLostPanelDescription:
		'Como alternativa, você pode verificar sua identidade recebendo um código por e-mail.',
	MfaDeviceLostPanelTitle: 'Perdeu seu dispositivo MFA?',
	MfaDidntReceiveEmailCode: 'Não recebeu um código? Entre em contato com o suporte',
	MfaEmailOtpSendFailureSnackbar: 'Falha ao enviar e-mail OTP.',
	MfaEmailOtpSentSnackbar: 'Um código foi enviado para {maskedEmail}',
	MfaEmailOtpVerificationFailedSnackbar: 'Falha ao verificar o OTP do e-mail.',
	MfaHasBeenSetUpText: 'Você configurou o MFA',
	MfaPanelDescription:
		'Proteja sua conta habilitando a Autenticação Multifator (MFA) para uma camada extra de proteção. Verifique sua identidade por meio de um método secundário para evitar acesso não autorizado.',
	MfaPanelNotAuthorizedError: 'Você deve estar conectado com nome de usuário ',
	MfaPanelRecommendationDescription:
		'Você fez login recentemente usando um método alternativo para verificar sua identidade. Para manter sua conta segura, considere configurar um novo dispositivo MFA.',
	MfaPanelRecommendationTitle: '**Recomendado:** Atualize seu dispositivo de MFA',
	MfaPanelTitle: 'Autenticação multifator (MFA)',
	MfaPanelVerifyEmailFirstAlert: 'Você precisará verificar seu e-mail antes de atualizar suas configurações do MFA.',
	MfaRecommendationBannerDescription:
		'Você entrou recentemente usando um método alternativo para verificar sua identidade. Para manter sua conta segura, considere configurar um novo dispositivo MFA.',
	MfaRecommendationBannerPrimaryAction: 'Configurar MFA',
	MfaRecommendationBannerTitle: 'Recomendado',
	MfaRemovedSnackbarTitle: 'O MFA foi removido.',
	MfaSendEmailCode: 'Enviar código',
	MfaVerifyIdentityLostDeviceButton: 'Perdi o acesso ao meu dispositivo MFA',
	MfaVerifyYourIdentityPanelDescription: 'Verifique o código no seu aplicativo autenticador e insira-o abaixo.',
	MfaVerifyYourIdentityPanelTitle: 'Verifique sua identidade',
	MicCamWarningMessage:
		'Desbloqueie a câmera e o microfone clicando nos ícones bloqueados na barra de endereço do navegador.',
	MicCamWarningTitle: 'Câmera e microfone estão bloqueados',
	MicOff: 'O microfone está desligado',
	MicOn: 'O microfone está ligado',
	MicSource: 'Fonte de microfone',
	MicWarningMessage: 'Foi detectado um problema com seu microfone',
	Microphone: 'Microfone',
	MicrophonePermissionBlocked: 'Acesso ao microfone bloqueado',
	MicrophonePermissionBlockedDescription: 'Atualize as permissões do seu microfone para iniciar a gravação.',
	MicrophonePermissionError: 'Conceda permissão de microfone nas configurações do navegador para continuar',
	MicrophonePermissionPrompt: 'Permita que o acesso ao microfone continue',
	Microsoft: 'Microsoft',
	MicrosoftCalendar: 'Microsoft Calendar',
	MicrosoftColor: 'Cor do calendário do Outlook',
	MicrosoftOutlook: 'Microsoft Outlook',
	MicrosoftTeams: 'Equipes da Microsoft',
	MiddleEast: 'Médio Oriente',
	MiddleName: 'Nome do meio',
	MiddleNames: 'Nome do meio',
	Midwife: 'Parteira',
	Midwives: 'Parteiras',
	Milan: 'Milão',
	MinBookingTimeDescription1: 'Os clientes não podem agendar dentro',
	MinBookingTimeDescription2: 'da hora de início de um compromisso',
	MinBookingTimeLabel: '{timePeriod} antes do agendamento',
	MinCancellationTimeEditModeDescription: 'Defina quantas horas um cliente pode cancelar sem penalidade',
	MinCancellationTimeUnset: 'Não há tempo mínimo de cancelamento definido',
	MinCancellationTimeViewModeDescription: 'Período de cancelamento sem penalidade',
	MinMaxBookingTimeUnset: 'Sem horário definido',
	Minimize: 'Minimizar',
	MinimizeConfirmationDescription:
		'Você tem um painel minimizado ativo. Se você continuar, ele será fechado e você poderá perder dados não salvos.',
	MinimizeConfirmationTitle: 'Fechar painel minimizado?',
	MinimumBookingTime: 'Tempo mínimo de reserva',
	MinimumCancellationTime: 'Tempo mínimo de cancelamento',
	MinimumPaymentError: 'É necessário um valor mínimo de {minimumAmount} para pagamentos online.',
	MinuteAbbreviated: 'minutos',
	MinuteAbbreviation: '{count} {count, plural, one {min} other {mins}}',
	Minutely: 'Minuto a Minuto',
	MinutesPlural: '{age, plural, one {# minuto} other {# minutos}}',
	MiscellaneousInformation: 'Informações diversas',
	MissingFeatures: 'Recursos ausentes',
	MissingPaymentMethod:
		'Por favor, adicione um método de pagamento à sua assinatura para adicionar mais membros à equipe.',
	MobileNumber: 'Número de telemóvel',
	MobileNumberOptional: 'Número de celular (opcional)',
	Modern: 'Moderno',
	Modifiers: 'Modificadores',
	ModifiersPlaceholder: 'Modificadores',
	Monday: 'Segunda-feira',
	Month: 'Mês',
	Monthly: 'Por mês',
	MonthlyCost: 'Custo mensal',
	MonthlyOn: 'Mensalmente em {date}',
	MonthsPlural: '{age, plural, one {# mês} other {# meses}}',
	More: 'Mais',
	MoreActions: 'Mais ações',
	MoreSettings: 'Mais configurações',
	MoreThanTen: '10 ',
	MostCommonlyUsed: 'Mais comumente usado',
	MostDownloaded: 'Mais baixados',
	MostPopular: 'Mais popular',
	Mother: 'Mãe',
	MotherInLaw: 'Sogra',
	MoveDown: 'Mover para baixo',
	MoveInboxConfirmationDescription:
		'Reajustar esta conexão de aplicativo irá removê-la da caixa de entrada <strong>{currentInboxName}</strong>.',
	MoveTemplateToFolder: 'Mover `{templateTitle}`',
	MoveTemplateToFolderSuccess: '{templateTitle} movido para {folderTitle}.',
	MoveTemplateToIntakeFolderSuccessMessage: 'Movido com sucesso para a pasta de entrada padrão',
	MoveTemplateToNewFolder: 'Crie uma nova pasta para mover este item.',
	MoveToChosenFolder: 'Escolha uma pasta para mover este item. Você pode criar uma nova pasta se necessário.',
	MoveToFolder: 'Mover para a pasta',
	MoveToInbox: 'Mova para a caixa de entrada',
	MoveToNewFolder: 'Mover para a nova pasta',
	MoveToSelectedFolder:
		'Uma vez movido, o item será organizado na pasta selecionada e não aparecerá mais em sua localização atual.',
	MoveUp: 'Subir',
	MultiSpeciality: 'Multi-especialidade',
	MultipleChoiceFormPrimaryText: 'Múltipla escolha',
	MultipleChoiceFormSecondaryText: 'Escolha várias opções',
	MultipleChoiceGridFormPrimaryText: 'Grade de múltipla escolha',
	MultipleChoiceGridFormSecondaryText: 'Escolha opções de uma matriz',
	Mumbai: 'Bombaim',
	MusicTherapist: 'Musicoterapeuta',
	MustContainOneLetterError: 'Deve conter pelo menos uma letra',
	MustEndWithANumber: 'Deve terminar com um número',
	MustHaveAtLeastXItems: 'Deve ter pelo menos {count, plural, one {# item} other {# itens}}',
	MuteAudio: 'Silenciar áudio',
	MuteEveryone: 'Silenciar todos',
	MyAvailability: 'Minha disponibilidade',
	MyGallery: 'Minha galeria',
	MyPortal: 'Meu portal',
	MyRelationships: 'Meus relacionamentos',
	MyTemplates: 'Modelos de equipe',
	MyofunctionalTherapist: 'Terapeuta Miofuncional',
	NCalifornia: 'Norte da Califórnia',
	NPI: 'INP',
	NVirginia: 'Virgínia do Norte',
	Name: 'Nome',
	NameIsRequired: 'O nome é obrigatório',
	NameMustNotBeAWebsite: 'O nome não deve ser um site',
	NameMustNotBeAnEmail: 'O nome não deve ser um e-mail',
	NameMustNotContainAtSign: 'O nome não deve conter o sinal @',
	NameMustNotContainHTMLTags: 'O nome não deve conter tags HTML',
	NameMustNotContainSpecialCharacters: 'O nome não deve conter caracteres especiais',
	NameOnCard: 'Nome no cartão',
	NationalProviderId: 'Identificador de provedor nacional (NPI)',
	NaturopathicDoctor: 'Médico Naturopata',
	NavigateToPersonalSettings: 'Perfil',
	NavigateToSubscriptionSettings: 'Configurações de assinatura',
	NavigateToWorkspaceSettings: 'Configurações do Workspace',
	NavigateToYourTeam: 'Gerenciar equipe',
	NavigationDrawerBilling: 'Faturamento',
	NavigationDrawerBillingInfo: 'Informações de faturamento, faturas e Stripe',
	NavigationDrawerCommunication: 'Comunicação',
	NavigationDrawerCommunicationInfo: 'Notificações e modelos',
	NavigationDrawerInsurance: 'Seguro',
	NavigationDrawerInsuranceInfo: 'Pagadores de seguros e sinistros',
	NavigationDrawerInvoices: 'Cobrança',
	NavigationDrawerPersonal: 'Meu perfil',
	NavigationDrawerPersonalInfo: 'Seus dados pessoais',
	NavigationDrawerProfile: 'Perfil',
	NavigationDrawerProviderSettings: 'Configurações',
	NavigationDrawerScheduling: 'Agendamento',
	NavigationDrawerSchedulingInfo: 'Detalhes dos serviços e reservas',
	NavigationDrawerSettings: 'Configurações',
	NavigationDrawerTemplates: 'Modelos',
	NavigationDrawerTemplatesV2: 'Modelos V2',
	NavigationDrawerTrash: 'Lixeira',
	NavigationDrawerTrashInfo: 'Restaurar itens excluídos',
	NavigationDrawerWorkspace: 'Configurações do espaço de trabalho',
	NavigationDrawerWorkspaceInfo: 'Informações sobre assinatura e espaço de trabalho',
	NegativeBalanceNotSupported: 'Saldos de conta negativos não são suportados',
	Nephew: 'Sobrinho',
	NetworkQualityFair: 'Conexão justa',
	NetworkQualityGood: 'Boa conexão',
	NetworkQualityPoor: 'Conexão ruim',
	Neurologist: 'Neurologista',
	Never: 'Nunca',
	New: 'Novo',
	NewAppointment: 'Novo compromisso',
	NewClaim: 'Nova reivindicação',
	NewClient: 'Novo cliente',
	NewClientNextStepsModalAddAnotherClient: 'Adicionar outro cliente',
	NewClientNextStepsModalBookAppointment: 'Agendar consulta',
	NewClientNextStepsModalBookAppointmentDescription: 'Marque um compromisso futuro ou crie uma tarefa.',
	NewClientNextStepsModalCompleteBasicInformation: 'Registro completo do cliente',
	NewClientNextStepsModalCompleteBasicInformationDescription:
		'Adicione informações do cliente e capture as próximas etapas.',
	NewClientNextStepsModalCreateInvoice: 'Criar fatura',
	NewClientNextStepsModalCreateInvoiceDescription: 'Adicione informações de pagamento do cliente ou crie uma fatura.',
	NewClientNextStepsModalCreateNote: 'Criar nota ou carregar documento',
	NewClientNextStepsModalCreateNoteDescription: 'Capture notas e documentação do cliente.',
	NewClientNextStepsModalDescription:
		'Aqui estão algumas ações que você pode tomar agora que criou um registro de cliente.',
	NewClientNextStepsModalSendIntake: 'Enviar entrada',
	NewClientNextStepsModalSendIntakeDescription:
		'Colete informações do cliente e envie formulários adicionais para preenchimento e assinatura.',
	NewClientNextStepsModalSendMessage: 'Enviar mensagem',
	NewClientNextStepsModalSendMessageDescription: 'Componha e envie uma mensagem ao seu cliente.',
	NewClientNextStepsModalTitle: 'Próximos passos',
	NewClientSuccess: 'Novo cliente criado com sucesso',
	NewClients: 'Novos clientes',
	NewConnectedApp: 'Novo aplicativo conectado',
	NewContact: 'Novo contato',
	NewContactNextStepsModalAddRelationship: 'Adicionar relacionamento',
	NewContactNextStepsModalAddRelationshipDescription: 'Vincule este contato a clientes ou grupos relacionados.',
	NewContactNextStepsModalBookAppointment: 'Agendar consulta',
	NewContactNextStepsModalBookAppointmentDescription: 'Agende uma consulta futura ou crie uma tarefa.',
	NewContactNextStepsModalCompleteProfile: 'Perfil completo',
	NewContactNextStepsModalCompleteProfileDescription: 'Adicione informações de contato e capture as próximas etapas.',
	NewContactNextStepsModalCreateNote: 'Criar nota ou enviar documento',
	NewContactNextStepsModalCreateNoteDescription: 'Capture notas e documentação do cliente.',
	NewContactNextStepsModalDescription: 'Aqui estão algumas ações para você tomar agora que você criou um contato.',
	NewContactNextStepsModalInviteToPortal: 'Convite para o portal',
	NewContactNextStepsModalInviteToPortalDescription: 'Envie um convite para acessar o portal.',
	NewContactNextStepsModalTitle: 'Próximos passos',
	NewContactSuccess: 'Novo contato criado com sucesso',
	NewDateOverrideButton: 'Nova substituição de data',
	NewDiagnosis: 'Adicionar diagnóstico',
	NewField: 'Novo campo',
	NewFolder: 'Nova pasta',
	NewInvoice: 'Nova fatura',
	NewLocation: 'Nova localização',
	NewLocationFailure: 'Falha ao criar novo local',
	NewLocationSuccess: 'Novo local criado com sucesso',
	NewManualPayer: 'Novo pagador manual',
	NewNote: 'Nova nota',
	NewNoteCreated: 'Nova nota criada com sucesso',
	NewPassword: 'Nova Senha',
	NewPayer: 'Novo pagador',
	NewPaymentMethod: 'Nova forma de pagamento',
	NewPolicy: 'Nova política',
	NewRelationship: 'Novo relacionamento',
	NewReminder: 'Novo lembrete',
	NewSchedule: 'Novo cronograma',
	NewSection: 'Nova seção',
	NewSectionOld: 'Nova seção [OLD]',
	NewSectionWithGrid: 'Nova seção com grade',
	NewService: 'Novo serviço',
	NewServiceFailure: 'Falha ao criar novo serviço',
	NewServiceSuccess: 'Novo serviço criado com sucesso',
	NewStatus: 'Novo status',
	NewTask: 'Nova tarefa',
	NewTaxRate: 'Nova taxa de imposto',
	NewTeamMemberNextStepsModalAssignClients: 'Atribuir clientes',
	NewTeamMemberNextStepsModalAssignClientsDescription: 'Atribua clientes específicos ao seu membro da equipe.',
	NewTeamMemberNextStepsModalAssignServices: 'Atribuir serviços',
	NewTeamMemberNextStepsModalAssignServicesDescription:
		'Gerenciar seus serviços atribuídos e ajustar os preços conforme necessário.',
	NewTeamMemberNextStepsModalBookAppointment: 'Agendar consulta',
	NewTeamMemberNextStepsModalBookAppointmentDescription: 'Agende uma consulta futura ou crie uma tarefa.',
	NewTeamMemberNextStepsModalCompleteProfile: 'Perfil completo',
	NewTeamMemberNextStepsModalCompleteProfileDescription:
		'Adicione detalhes sobre o membro da sua equipe para completar o perfil.',
	NewTeamMemberNextStepsModalDescription:
		'Aqui estão algumas ações para tomar agora que você criou um membro da equipe.',
	NewTeamMemberNextStepsModalEditPermissions: 'Permissões de edição',
	NewTeamMemberNextStepsModalEditPermissionsDescription:
		'Ajuste seus níveis de acesso para garantir que eles tenham as permissões corretas.',
	NewTeamMemberNextStepsModalSetAvailability: 'Definir disponibilidade',
	NewTeamMemberNextStepsModalSetAvailabilityDescription: 'Configure a disponibilidade deles para criar horários.',
	NewTeamMemberNextStepsModalTitle: 'Próximos passos',
	NewTemplateFolderDescription: 'Crie uma nova pasta para organizar sua documentação.',
	NewUIUpdateBannerButton: 'Recarregar aplicativo',
	NewUIUpdateBannerTitle: 'Há uma nova atualização pronta!',
	NewZealand: 'Nova Zelândia',
	Newest: 'O mais novo',
	NewestUnreplied: 'Mais recentes sem resposta',
	Next: 'Próximo',
	NextInvoiceIssueDate: 'Próxima data de emissão da fatura',
	NextNDays: 'Próximos {number} dias',
	Niece: 'Sobrinha',
	No: 'Não',
	NoAccessGiven: 'Nenhum acesso concedido',
	NoActionConfigured: 'Nenhuma ação configurada',
	NoActivePolicies: 'Nenhuma política ativa',
	NoActiveReferrals: 'Você não tem referências ativas',
	NoAppointmentsFound: 'Nenhum compromisso foi encontrado',
	NoAppointmentsHeading: 'Gerencie os compromissos e atividades dos clientes',
	NoArchivedPolicies: 'Nenhuma política arquivada',
	NoAvailableTimes: 'Não foram encontrados horários disponíveis.',
	NoBillingItemsFound: 'Nenhum item de faturamento encontrado',
	NoCalendarsSynced: 'Nenhum calendário sincronizado',
	NoClaimsFound: 'Nenhuma reclamação encontrada',
	NoClaimsHeading: 'Agilize o envio de solicitações de reembolso',
	NoClientsHeading: 'Reúna os registros dos seus clientes',
	NoCompletedReferrals: 'Você não tem referências completas',
	NoConnectionsHeading: 'Simplifique suas comunicações com os clientes',
	NoContactsGivenAccess: 'Nenhum cliente ou contato recebeu acesso a esta nota',
	NoContactsHeading: 'Mantenha-se conectado com aqueles que apoiam sua prática',
	NoCopayOrCoinsurance: 'Sem co-pagamento ou co-seguro',
	NoCustomServiceSchedule:
		'Nenhuma programação personalizada definida — a disponibilidade depende da disponibilidade do membro da equipe',
	NoDescription: 'Sem descrição',
	NoDocumentationHeading: 'Crie e armazene notas com segurança',
	NoDuplicateRecordsHeading: 'Seu registro de cliente está livre de duplicatas',
	NoEffect: 'Sem efeito',
	NoEnrolmentProfilesFound: 'Nenhum perfil de inscrição encontrado',
	NoGlossaryItems: 'Nenhum item de glossário',
	NoInvitedReferrals: 'Você não tem referências convidadas',
	NoInvoicesFound: 'Nenhuma fatura encontrada',
	NoInvoicesHeading: 'Automatize sua cobrança e pagamentos',
	NoLimit: 'Sem limite',
	NoLocationsFound: 'Nenhum local foi encontrado',
	NoLocationsWillBeAdded: 'Nenhuma localização será adicionada.',
	NoNoteFound: 'Nenhuma nota encontrada',
	NoPaymentMethods: 'Você não tem métodos de pagamento salvos, você pode adicionar um ao efetuar um pagamento.',
	NoPermissionError: 'Você não tem permissão',
	NoPermissions: 'Você não tem permissão para visualizar esta página',
	NoPolicy: 'Nenhuma política de cancelamento adicionada',
	NoRecordsHeading: 'Personalize os registros dos seus clientes',
	NoRecordsToDisplay: 'Sem {resource} para exibir',
	NoRelationshipsHeading: 'Mantenha-se conectado com aqueles que apoiam seu cliente',
	NoRemindersFound: 'Nenhum lembrete encontrado',
	NoResultsFound: 'Nenhum resultado encontrado',
	NoResultsFoundDescription: 'Não conseguimos encontrar nenhum item que corresponda à sua pesquisa',
	NoServicesAdded: 'Nenhum serviço adicionado',
	NoServicesApplied: 'Nenhum serviço aplicado',
	NoServicesWillBeAdded: 'Nenhum serviço será adicionado.',
	NoTemplate: 'Você não tem modelos de prática salvos',
	NoTemplatesHeading: 'Crie seus próprios modelos',
	NoTemplatesInFolder: 'Nenhum modelo nesta pasta',
	NoTitle: 'Sem título',
	NoTrashItemsHeading: 'Nenhum item excluído encontrado',
	NoTriggerConfigured: 'Nenhum gatilho configurado',
	NoUnclaimedItemsFound: 'Nenhum item não reclamado encontrado.',
	NonAiTemplates: 'Modelos não-IA',
	None: 'Nenhum',
	NotAvailable: 'Não disponível',
	NotCovered: 'Não coberto',
	NotFoundSnackbar: 'Recurso não encontrado.',
	NotRequiredField: 'Não necessário',
	Note: 'Observação',
	NoteDuplicateSuccess: 'Nota duplicada com sucesso',
	NoteEditModeViewSwitcherDescription: 'Criar e editar nota',
	NoteFormSubmittedNotificationSubject: '{actorProfileName} enviou o formulário {noteTitle}',
	NoteLockSuccess: '{title} foi bloqueado',
	NoteModalAttachmentButton: 'Adicionar Anexos',
	NoteModalPhotoButton: 'Adicionar/capturar fotos',
	NoteModalTrascribeButton: 'Transcrever áudio ao vivo',
	NoteResponderModeViewSwitcherDescription: 'Enviar formulários e revisar respostas',
	NoteResponderModeViewSwitcherTooltipTitle: 'Responda e envie formulários em nome de seus clientes',
	NoteResponderModeViewSwitcherTooltipTitleAsClient: 'Preencha e envie formulários como cliente',
	NoteUnlockSuccess: '{title} foi desbloqueado',
	NoteViewModeViewSwitcherDescription: 'Acesso somente para visualização',
	Notes: 'Notas',
	NotesAndForms: 'Notas e Formulários',
	NotesCategoryDescription: 'Para documentar interações com clientes',
	NothingToSeeHere: 'Nada para ver aqui',
	Notification: 'Notificação',
	NotificationIgnoredMessage: 'Todas as notificações {notificationType} serão ignoradas',
	NotificationRestoredMessage: 'Todas as notificações {notificationType} restauradas',
	NotificationSettingBillingDescription:
		'Receba notificações sobre atualizações e lembretes de pagamento de clientes.',
	NotificationSettingBillingTitle: 'Faturamento e pagamento',
	NotificationSettingChannelSummary: '{count, plural, one{{channels} somente} other{{channels}} }',
	NotificationSettingClientDocumentationDescription:
		'Receba notificações sobre atualizações e lembretes de pagamento de clientes.',
	NotificationSettingClientDocumentationTitle: 'Cliente e documentação',
	NotificationSettingCommunicationsDescription:
		'Receba notificações para caixa de entrada e atualizações de seus canais conectados',
	NotificationSettingCommunicationsTitle: 'Comunicações',
	NotificationSettingEmail: 'E-mail',
	NotificationSettingInApp: 'No aplicativo',
	NotificationSettingPanelDescription: 'Escolha as notificações que deseja receber para atividades e recomendações.',
	NotificationSettingPanelTitle: 'Preferências de notificação',
	NotificationSettingSchedulingDescription:
		'Receba notificações quando um membro da equipe ou cliente reservar, remarcar ou cancelar seu compromisso.',
	NotificationSettingSchedulingTitle: 'Agendamento',
	NotificationSettingUpdateSuccess: 'Configurações de notificação atualizadas com sucesso',
	NotificationSettingWhereYouReceiveNotifications: 'Onde você deseja receber essas notificações',
	NotificationSettingWorkspaceDescription:
		'Receba notificações sobre mudanças no sistema, problemas, transferências de dados e lembretes de assinatura.',
	NotificationSettingWorkspaceTitle: 'Espaço de trabalho',
	NotificationTemplateUpdateFailed: 'Falha ao atualizar o modelo de notificação',
	NotificationTemplateUpdateSuccess: 'Modelo de notificação atualizado com sucesso',
	NotifyAttendeesOfTaskCancellationModalDescription:
		'Gostaria de enviar um e-mail de notificação de cancelamento aos participantes?',
	NotifyAttendeesOfTaskCancellationModalTitle: 'Enviar cancelamento',
	NotifyAttendeesOfTaskConfirmationModalDescription:
		'Gostaria de enviar um e-mail de notificação de confirmação aos participantes?',
	NotifyAttendeesOfTaskConfirmationModalTitle: 'Enviar confirmação',
	NotifyAttendeesOfTaskDeletedModalTitle: 'Gostaria de enviar e-mails de cancelamento aos participantes?',
	NotifyAttendeesOfTaskMissingEmails:
		'{names} {count, plural, one {não tem} other {não têm}} endereço de e-mail, portanto não receberão notificações e lembretes automáticos.',
	NotifyAttendeesOfTaskMissingEmailsV2:
		'<b>{names}</b> {count, plural, one {não tem} other {não têm}} um endereço de email, portanto não receberão notificações e lembretes automáticos.',
	NotifyAttendeesOfTaskModalTitle: 'Gostaria de enviar um e-mail de notificação aos participantes?',
	NotifyAttendeesOfTaskSnackbar: 'Enviando notificação',
	NuclearMedicineTechnologist: 'Tecnólogo em Medicina Nuclear',
	NumberOfClaims: '{number, plural, one {# Reivindicação} other {# Reivindicações}}',
	NumberOfClients: '{number, plural, one {# Cliente} other {# Clientes}}',
	NumberOfContacts: '{number, plural, one {# Contato} other {# Contatos}}',
	NumberOfDataEntriesFound: '{count} {count, plural, one {entrada} other {entradas}} encontradas',
	NumberOfErrors: '{count, plural, one {# erro} other {# erros}}',
	NumberOfInvoices: '{number, plural, one {# Fatura} other {# Faturas}}',
	NumberOfLineitemsToCredit:
		'Você tem <mark>{count} {count, plural, one {item de linha} other {itens de linha}}</mark> para emitir um crédito.',
	NumberOfPayments: '{number, plural, one {# Pagamento} other {# Pagamentos}}',
	NumberOfRelationships: '{number, plural, one {# Relacionamento} other {# Relacionamentos}}',
	NumberOfResources: '{number, plural, one {# Recurso} other {# Recursos}}',
	NumberOfTeamMembers: '{number, plural, one {# Membro da equipe} other {# Membros da equipe}}',
	NumberOfTrashItems: '{number, plural, one {# item} other {# itens}}',
	NumberOfUninvoicedAmounts:
		'Você tem <mark>{count} {count, plural, one {valor} other {valores}} não faturados</mark> a serem faturados',
	NumberedList: 'Lista numerada',
	Nurse: 'Enfermeira',
	NurseAnesthetist: 'Enfermeira Anestesista',
	NurseAssistant: 'Auxiliar de enfermagem',
	NurseEducator: 'Enfermeira Educadora',
	NurseMidwife: 'Enfermeira Parteira',
	NursePractitioner: 'Enfermeira',
	Nurses: 'Enfermeiras',
	Nursing: 'Enfermagem',
	Nutritionist: 'Nutricionista',
	Nutritionists: 'Nutricionistas',
	ObstetricianOrGynecologist: 'Ginecologista obstetra',
	Occupation: 'Ocupação',
	OccupationalTherapist: 'Terapeuta ocupacional',
	OccupationalTherapists: 'Terapia ocupacional',
	OccupationalTherapy: 'Terapia ocupacional',
	Occurrences: 'Ocorrências',
	Of: 'de',
	Ohio: 'Ohio',
	OldPassword: 'Senha Antiga',
	OlderMessages: '{count} mensagens mais antigas',
	Oldest: 'Mais antigo',
	OldestUnreplied: 'Mais antigo sem resposta',
	On: 'sobre',
	OnboardingBusinessAgreement: 'Em nome próprio e da empresa, concordo com o {businessAssociateAgreement}',
	OnboardingLoadingOccupationalTherapist:
		'<mark>Terapia ocupacional</mark> representam um quarto de nossos clientes no Carepatron',
	OnboardingLoadingProfession: 'Temos muitos <mark>{profession}</mark> usando e prosperando no Carepatron.',
	OnboardingLoadingPsychologist:
		'<mark>Psicólogos</mark> representam mais da metade de nossos clientes no Carepatron',
	OnboardingLoadingSubtitleFive: 'Nossa missão é fazer<mark> software de saúde acessível</mark> para todos.',
	OnboardingLoadingSubtitleFour:
		'<mark>Software de saúde simplificado</mark> para mais de 10.000 pessoas em todo o mundo.',
	OnboardingLoadingSubtitleThree:
		'Salvar<mark> 1 dia por semana</mark> em tarefas administrativas com a ajuda do Carepatron.',
	OnboardingLoadingSubtitleTwo:
		'Salvar<mark> 2 horas</mark> diariamente em tarefas administrativas com a ajuda do Carepatron.',
	OnboardingReviewLocationOne: 'Centro de Saúde Mental Holland Park',
	OnboardingReviewLocationThree: 'Enfermeira prática, Mt Eden Healthcare',
	OnboardingReviewLocationTwo: 'Clínica Casa da Vida',
	OnboardingReviewNameOne: 'Anul P',
	OnboardingReviewNameThree: 'Alice E.',
	OnboardingReviewNameTwo: 'Clara W.',
	OnboardingReviewOne:
		'&quot;O uso do Carepatron é super intuitivo. Ele nos ajuda a administrar nossa clínica tão bem que nem precisamos mais de uma equipe de administradores&quot;',
	OnboardingReviewThree:
		'&quot;É a solução de melhores práticas que usei tanto em termos de recursos quanto de custo. Ela tem tudo que preciso para expandir meus negócios&quot;',
	OnboardingReviewTwo:
		'&quot;Também adoro o aplicativo carepatron. Me ajuda a acompanhar meus clientes e trabalhar em qualquer lugar.&quot;',
	OnboardingTitle: `Vamos chegar<mark> saber
 é melhor você</mark>`,
	Oncologist: 'Oncologista',
	Online: 'On-line',
	OnlineBookingColorTheme: 'Tema de cores de reserva online',
	OnlineBookings: 'Reservas on-line',
	OnlineBookingsHelper: 'Escolha quando as reservas online podem ser feitas e por que tipo de clientes',
	OnlinePayment: 'Pagamento online',
	OnlinePaymentSettingCustomInfo:
		'As configurações de pagamento online para este serviço diferem das configurações globais de reserva.',
	OnlinePaymentSettings: 'Configurações de pagamento on-line',
	OnlinePaymentSettingsInfo:
		'Receba pagamentos por serviços no momento da reserva on-line para proteger e agilizar os pagamentos',
	OnlinePaymentSettingsPaymentsDisabled:
		'Pagamentos estão desabilitados, portanto não podem ser coletados durante a reserva online. Por favor, verifique suas configurações de pagamento para habilitar pagamentos.',
	OnlinePaymentSettingsStripeNote:
		'{action} para receber pagamentos de reservas online e otimizar seu processo de pagamento',
	OnlinePaymentsNotSupportedForCurrency: 'Pagamentos online não são suportados em {currency}.',
	OnlinePaymentsNotSupportedForCurrencyErrorCodeSnackbar:
		'Lamentamos, mas os pagamentos online não são suportados nesta moeda',
	OnlinePaymentsNotSupportedInCountryErrorCodeSnackbar:
		'Lamentamos, mas os pagamentos online ainda não são suportados no seu país',
	OnlineScheduling: 'Agendamento On-line',
	OnlyVisibleToYou: 'Visível apenas para você',
	OnlyYou: 'Só você',
	OnsetDate: 'Onset date',
	OnsetOfCurrentSymptomsOrIllness: 'Início dos sintomas ou doenças atuais',
	Open: 'Abrir',
	OpenFile: 'Abrir arquivo',
	OpenSettings: 'Abrir configurações',
	Ophthalmologist: 'Oftalmologista',
	OptimiseTelehealthCalls: 'Otimize suas chamadas de Telessaúde',
	OptimizeServiceTimes: 'Otimize os tempos de serviço',
	Options: 'Opções',
	Optometrist: 'Optometrista',
	Or: 'ou',
	OrAttachSingleFile: 'anexar um arquivo',
	OrDragAndDrop: 'ou arraste e solte',
	OrderBy: 'Ordenar por',
	Oregon: 'Óregon',
	OrganisationOrIndividual: 'Organização ou indivíduo',
	OrganizationPlanInclusion1: 'Permissões avançadas',
	OrganizationPlanInclusion2: 'Suporte gratuito para importação de dados do cliente',
	OrganizationPlanInclusion3: 'Gerente de sucesso dedicado',
	OrganizationPlanInclusionHeader: 'Tudo no Professional, mais...',
	Orthodontist: 'Ortodontista',
	Orthotist: 'Ortopedista',
	Other: 'Outro',
	OtherAdjustments: 'Outros ajustes',
	OtherAdjustmentsTableEmptyState: 'Nenhum ajuste encontrado',
	OtherEvents: 'Outros eventos',
	OtherId: 'Outra identificação',
	OtherIdQualifier: 'Outro qualificador de ID',
	OtherPaymentMethod: 'Outro método de pagamento',
	OtherPlanMessage:
		'Mantenha o controle das necessidades do seu consultório. Revise seu plano atual, monitore o uso e explore opções de atualização para desbloquear mais recursos à medida que sua equipe cresce.',
	OtherPolicy: 'Outros seguros',
	OtherProducts: 'Que outros produtos ou ferramentas você usa?',
	OtherServices: 'Outros serviços',
	OtherTemplates: 'Outros modelos',
	Others: 'Outros',
	OthersPeople: `{n, plural, 		one {1 outra pessoa}
		other {# outras pessoas}
	}`,
	OurResearchTeamReachOut:
		'Nossa equipe de pesquisa pode entrar em contato para saber mais sobre como a Carepatron poderia ter sido melhor para suas necessidades?',
	OutOfOffice: 'Fora do escritório',
	OutOfOfficeColor: 'Cor fora do escritório',
	OutOfOfficeHelper: 'Alguns membros da equipe escolhidos estão fora do cargo',
	OutsideLabCharges: 'Taxas de laboratório externo',
	OutsideOfWorkingHours: 'Fora do horário de trabalho',
	OutsideWorkingHoursHelper: 'Alguns membros da equipe escolhidos estão fora do horário de trabalho',
	Overallocated: 'Superalocado',
	OverallocatedPaymentDescription: `Este pagamento foi alocado em excesso para itens faturáveis.
 Adicione uma alocação para itens não pagos ou emita um crédito ou reembolso.`,
	OverallocatedPaymentTitle: 'Pagamento superalocado',
	OverdueTerm: 'Prazo vencido (dias)',
	OverinvoicedAmount: 'Valor superfaturado',
	Overpaid: 'Pago em excesso',
	OverpaidAmount: 'Valor pago em excesso',
	Overtime: 'ao longo do tempo',
	Owner: 'Proprietário',
	POS: 'PDV',
	POSCode: 'Código PDV',
	POSPlaceholder: 'POS',
	PageBlockerDescription: 'Alterações não salvas serão perdidas. Deseja sair mesmo assim?',
	PageBlockerTitle: 'Descartar alterações?',
	PageFormat: 'Formato de página',
	PageNotFound: 'página não encontrada',
	PageNotFoundDescription: 'Você não tem mais acesso a esta página ou ela não pode ser encontrada',
	PageUnauthorised: 'Acesso não autorizado',
	PageUnauthorisedDescription: 'Você não tem permissão para acessar esta página',
	Paid: 'Pago',
	PaidAmount: 'Valor Pago',
	PaidAmountMinimumValueError: 'O valor pago deve ser maior que 0',
	PaidAmountRequiredError: 'O valor pago é obrigatório',
	PaidItems: 'Itens pagos',
	PaidMultiple: 'Pago',
	PaidOut: 'Pago',
	ParagraphStyles: 'Estilos de parágrafo',
	Parent: 'Pai',
	Paris: 'Paris',
	PartialRefundAmount: 'Parcialmente reembolsado ({amount} restante)',
	PartiallyFull: 'Parcialmente cheio',
	PartiallyPaid: 'Parcialmente pago',
	PartiallyRefunded: 'Parcialmente reembolsado',
	Partner: 'Parceiro',
	Password: 'Senha',
	Past: 'Passado',
	PastDateOverridesEmpty: 'Suas substituições de data aparecerão aqui assim que o evento terminar',
	Pathologist: 'Patologista',
	Patient: 'Paciente',
	Pause: 'Pausa',
	Paused: 'Pausado',
	Pay: 'Pagar',
	PayMonthly: 'Pague mensalmente',
	PayNow: 'Pague agora',
	PayValue: 'Pague {showPrice, select, true {{price}} other {agora}}',
	PayWithOtherCard: 'Pague com outro cartão',
	PayYearly: 'Pague anualmente',
	PayYearlyPercentOff: 'Pague anualmente <mark>{percent}% off</mark>',
	Payer: 'Pagador',
	PayerClaimId: 'ID do pedido do pagador',
	PayerCoverage: 'Cobertura',
	PayerDetails: 'Detalhes do pagador',
	PayerDetailsDescription: 'Veja os detalhes dos pagadores que foram adicionados à sua conta e gerencie a inscrição.',
	PayerID: 'ID do pagador',
	PayerId: 'ID do pagador',
	PayerName: 'Nome do pagador',
	PayerPhoneNumber: 'Número de telefone do pagador',
	Payers: 'Pagadores',
	Payment: 'Pagamento',
	PaymentAccountUpdated: 'Sua conta foi atualizada!',
	PaymentAccountUpgraded: 'Sua conta foi atualizada!',
	PaymentAmount: 'Valor do pagamento',
	PaymentDate: 'Data de pagamento',
	PaymentDetails: 'Detalhes do pagamento',
	PaymentForUsersPerMonth: 'Pagamento para {billedUsers, plural, one {# usuário} other {# usuários}} por mês',
	PaymentInfoFormPrimaryText: 'Informação de pagamento',
	PaymentInfoFormSecondaryText: 'Reúna detalhes de pagamento',
	PaymentIntentAlreadyPaidErrorCodeSnackbar: 'Esta fatura já foi paga.',
	PaymentIntentAlreadyProcessedErrorCodeSnackbar: 'Esta fatura já está em processamento.',
	PaymentIntentAmountMismatchSnackbar:
		'O valor total da fatura foi modificado. Por favor, revise as alterações antes de pagar.',
	PaymentIntentSyncTimeoutSnackbar:
		'Seu pagamento foi bem-sucedido, mas ocorreu um tempo limite. Atualize a página e se o seu pagamento não for exibido, entre em contato com o suporte.',
	PaymentMethod: 'Forma de pagamento',
	PaymentMethodDescription:
		'Adicione e gerencie sua forma de pagamento prática para agilizar o processo de cobrança de sua assinatura.',
	PaymentMethodLabelBank: 'conta bancária',
	PaymentMethodLabelCard: 'cartão',
	PaymentMethodLabelFallback: 'Forma de pagamento',
	PaymentMethodRequired: 'Adicione um método de pagamento antes de alterar as assinaturas',
	PaymentMethods: 'Métodos de Pagamento',
	PaymentProcessing: 'Processo de pagamento!',
	PaymentProcessingFee: 'Pagamento inclui taxa de processamento de {amount}',
	PaymentReports: 'Relatórios de Pagamento (ERA)',
	PaymentSettings: 'Configurações de pagamento',
	PaymentSuccessful: 'Pagamento realizado com sucesso!',
	PaymentType: 'Tipo de pagamento',
	Payments: 'Pagamentos',
	PaymentsAccountDisabledNotificationSubject: `Pagamentos online através de {paymentProvider, select, undefined { Stripe } other {{paymentProvider}}} foram desativados.
Verifique suas configurações de pagamento para habilitar os pagamentos.`,
	PaymentsEmptyStateDescription: 'Nenhum pagamento foi encontrado.',
	PaymentsUnallocated: 'Pagamentos não alocados',
	PayoutDate: 'Data de pagamento',
	PayoutsDisabled: 'Pagamentos desativados',
	PayoutsEnabled: 'Pagamentos ativados',
	PayoutsStatus: 'Status do pagamento',
	Pediatrician: 'Pediatra',
	Pen: 'Caneta',
	Pending: 'Pendente',
	People: '{rosterSize } pessoas',
	PeopleCount: 'Pessoas ({count})',
	PerMonth: '/ Mês',
	PerUser: 'Por usuário',
	Permission: 'Permissão',
	PermissionRequired: 'Permissão necessária',
	Permissions: 'Permissões',
	PermissionsClientAndContactDocumentation: 'Cliente ',
	PermissionsClientAndContactProfiles: 'Cliente ',
	PermissionsEditAccess: 'Editar acesso',
	PermissionsInvoicesAndPayments: 'Faturas ',
	PermissionsScheduling: 'Agendamento',
	PermissionsUnassignClients: 'Cancelar atribuição de clientes',
	PermissionsUnassignClientsConfirmation: 'Tem certeza de que deseja cancelar a atribuição desses clientes?',
	PermissionsValuesAssigned: 'Atribuído apenas',
	PermissionsValuesEverything: 'Tudo',
	PermissionsValuesNone: 'Nenhum',
	PermissionsValuesOwnCalendar: 'Calendário próprio',
	PermissionsViewAccess: 'Ver acesso',
	PermissionsWorkspaceSettings: 'Configurações do espaço de trabalho',
	Person: '{rosterSize} pessoa',
	PersonalDetails: 'Detalhes pessoais',
	PersonalHealthcareHistoryStoreDescription:
		'Responda e armazene com segurança seu histórico pessoal de saúde em um só lugar',
	PersonalTrainer: 'Treinador pessoal',
	PersonalTraining: 'Treinamento pessoal',
	PersonalizeWorkspace: 'Personalize seu espaço de trabalho',
	PersonalizingYourWorkspace: 'Personalizando seu espaço de trabalho',
	Pharmacist: 'Farmacêutico',
	Pharmacy: 'Farmácia',
	PhoneCall: 'Chamada telefónica',
	PhoneNumber: 'Número de telefone',
	PhoneNumberOptional: 'Número de telefone (opcional)',
	PhotoBy: 'foto por',
	PhysicalAddress: 'Endereço físico',
	PhysicalTherapist: 'Fisioterapeuta',
	PhysicalTherapists: 'Fisioterapeutas',
	PhysicalTherapy: 'Fisioterapia',
	Physician: 'Médico',
	PhysicianAssistant: 'Médico Assistente',
	Physicians: 'Médicos',
	Physiotherapist: 'Fisioterapeuta',
	PlaceOfService: 'Local de serviço',
	Plan: 'Plano',
	PlanAndReport: 'Plano/Relatório',
	PlanId: 'ID do plano',
	PlansAndReportsCategoryDescription: 'Para planejamento de tratamento e resumo de resultados',
	PleaseRefreshThisPageToTryAgain: 'Por favor, atualize esta página para tentar novamente.',
	PleaseWait: 'Por favor, aguarde...',
	PleaseWaitForHostToJoin: 'Aguardando a adesão do anfitrião...',
	PleaseWaitForHostToStart: 'Aguarde o anfitrião iniciar esta reunião.',
	PlusAdd: '+ Adicionar',
	PlusOthers: '+{count} outros',
	PlusPlanInclusionFive: 'Caixas de entrada compartilhadas',
	PlusPlanInclusionFour: 'Videochamadas em grupo',
	PlusPlanInclusionHeader: 'Tudo em Essencial  ',
	PlusPlanInclusionOne: 'IA ilimitada',
	PlusPlanInclusionSix: 'Marca personalizada',
	PlusPlanInclusionThree: 'Agendamento de grupo',
	PlusPlanInclusionTwo: 'Armazenamento ilimitado ',
	PlusSubscriptionPlanSubtitle: 'Para práticas otimizadas e crescentes',
	PlusSubscriptionPlanTitle: 'Mais',
	PoliceOfficer: 'Policial',
	PolicyDates: 'Datas da apólice',
	PolicyHolder: 'Segurado',
	PolicyHoldersAddress: 'Endereço do segurado',
	PolicyMemberId: 'ID do Membro da Política',
	PolicyStatus: 'Status da política',
	Popular: 'Popular',
	PortalAccess: 'Acesso ao portal',
	PortalNoAppointmentsHeading: 'Acompanhe todas as consultas futuras e passadas',
	PortalNoDocumentationHeading: 'Crie e armazene seus documentos com segurança',
	PortalNoRelationshipsHeading: 'Reúna aqueles que apoiam sua jornada',
	PosCodeErrorMessage: 'Código POS é necessário',
	PosoNumber: 'Número do pedido/SO',
	PossibleClientDuplicate: 'Possível duplicata do cliente',
	PotentialClientDuplicateTitle: 'Possível registro de cliente duplicado',
	PotentialClientDuplicateWarning:
		'Essas informações do cliente podem já existir na sua lista de clientes. Verifique e atualize o registro existente, se necessário, ou continue a criar um novo cliente.',
	PoweredBy: 'Distribuído por',
	Practice: 'Prática',
	PracticeDetails: 'Detalhes da prática',
	PracticeInfoHeader: 'Informação de negócios',
	PracticeInfoPlaceholder: `Nome da prática,
 Identificador nacional do provedor,
 Número de identificação do empregado`,
	PracticeLocation: 'Parece que sua prática está em',
	PracticeSettingsAvailabilityTab: 'Disponibilidade',
	PracticeSettingsBillingTab: 'Configurações de faturamento',
	PracticeSettingsClientSettingsTab: 'Configurações do cliente',
	PracticeSettingsGeneralTab: 'Em geral',
	PracticeSettingsOnlineBookingTab: 'Marcação online',
	PracticeSettingsServicesTab: 'Serviços',
	PracticeSettingsTaxRatesTab: 'Taxas de imposto',
	PracticeTemplate: 'Modelo de prática',
	Practitioner: 'Praticante',
	PreferredLanguage: 'Idioma preferido',
	PreferredName: 'Nome preferido',
	Prescription: 'Prescrição',
	PreventionSpecialist: 'Especialista em Prevenção',
	Preview: 'Visualização',
	PreviewAndSend: 'Visualizar e enviar',
	PreviewUnavailable: 'Visualização indisponível para este tipo de arquivo',
	PreviousNotes: 'Notas anteriores',
	Price: 'Preço',
	PriceError: 'O preço deve ser maior que 0',
	PricePerClient: 'Preço por cliente',
	PricePerUser: 'Por usuário',
	PricePerUserBilledAnnually: 'Por usuário cobrado anualmente',
	PricePerUserPerPeriod: '{price} por usuário / {isMonthly, select, true {mês} other {ano}}',
	PricingGuide: 'Guia para planos de preços',
	PricingPlanPerMonth: '/ mês',
	PricingPlanPerYear: '/ ano',
	Primary: 'Primário',
	PrimaryInsurance: 'Seguro primário',
	PrimaryPolicy: 'Seguro primário',
	PrimaryTimezone: 'Fuso horário principal',
	Print: 'Imprimir',
	PrintToCms1500: 'Imprimir para CMS1500',
	PrivatePracticeConsultant: 'Consultor de consultório particular',
	Proceed: 'Prosseguir',
	ProcessAtTimeOfBookingDesc: 'Os clientes devem pagar o preço total do serviço para reservar online',
	ProcessAtTimeOfBookingLabel: 'Processar pagamentos no momento da reserva',
	Processing: 'Em processamento',
	ProcessingFee: 'Taxa de processamento',
	ProcessingFeeToolTip: `Carepatron permite que você cobre taxas de processamento de seus clientes.
 Em algumas jurisdições é proibido cobrar taxas de processamento aos seus clientes. É sua responsabilidade cumprir as leis aplicáveis.`,
	ProcessingRequest: 'Processando solicitação...',
	Product: 'produtos',
	Profession: 'Profissão',
	ProfessionExample: 'Terapeuta, Nutricionista, Dentista',
	ProfessionPlaceholder: 'Comece a digitar sua profissão ou escolha na lista',
	ProfessionalPlanInclusion1: 'Armazenamento ilimitado',
	ProfessionalPlanInclusion2: 'Tarefas ilimitadas',
	ProfessionalPlanInclusion3: '99.99% guaranteed uptime SLA',
	ProfessionalPlanInclusion4: 'Suporte ao cliente 24 horas por dia, 7 dias por semana',
	ProfessionalPlanInclusion5: 'Lembretes por SMS',
	ProfessionalPlanInclusionHeader: 'Tudo no Starter, mais...',
	Professions: 'Profissões',
	Profile: 'Perfil',
	ProfilePhotoFileSizeLimit: 'Limite de tamanho de arquivo de 5 MB',
	ProfilePopoverSubTitle: 'Você está conectado como <strong>{email}</strong>',
	ProfilePopoverTitle: 'Seus espaços de trabalho',
	PromoCode: 'Código promocional',
	PromotionCodeApplied: '{promo} aplicado',
	ProposeNewDateTime: 'Proponha uma nova data/hora',
	Prosthetist: 'Protesista',
	Provider: 'Fornecedor',
	ProviderBillingPlanExpansionManageButton: 'Gerenciar plano',
	ProviderCommercialNumber: 'Número comercial do provedor',
	ProviderDetails: 'Detalhes do provedor',
	ProviderDetailsAddress: 'Endereço',
	ProviderDetailsName: 'Nome',
	ProviderDetailsPhoneNumber: 'Número de telefone',
	ProviderHasExistingBillingAccountErrorCodeSnackbar: 'Desculpe, este provedor já tem uma conta de faturamento',
	ProviderInfoPlaceholder: `Nome da equipe,
 Endereço de email,
 Número de telefone,
 Identificador nacional do provedor,
 Número de licença`,
	ProviderIsChargedProcessingFee: 'Você pagará a taxa de processamento',
	ProviderPaymentFormBackButton: 'Voltar',
	ProviderPaymentFormBillingAddressCity: 'Cidade',
	ProviderPaymentFormBillingAddressCountry: 'País',
	ProviderPaymentFormBillingAddressLine1: 'Linha 1',
	ProviderPaymentFormBillingAddressPostalCode: 'Código postal',
	ProviderPaymentFormBillingEmail: 'E-mail',
	ProviderPaymentFormCardCvc: 'CVC',
	ProviderPaymentFormCardDetailsTitle: 'Detalhes do cartão de crédito',
	ProviderPaymentFormCardExpiry: 'Termo',
	ProviderPaymentFormCardHolderAddressTitle: 'Endereço',
	ProviderPaymentFormCardHolderName: 'Nome do Titular',
	ProviderPaymentFormCardHolderTitle: 'Detalhes do titular do cartão',
	ProviderPaymentFormCardNumber: 'Número do cartão',
	ProviderPaymentFormPlanTitle: 'Plano escolhido',
	ProviderPaymentFormPlanTotalTitle: 'Total ({currency}):',
	ProviderPaymentFormSaveButton: 'Salvar assinatura',
	ProviderPaymentFreePlanDescription:
		'A escolha do plano gratuito removerá o acesso de cada membro da equipe aos seus clientes em seu provedor. Porém, seu acesso permanecerá e você ainda poderá utilizar a plataforma.',
	ProviderPaymentStepName: 'Análise ',
	ProviderPaymentSuccessSnackbar: 'Ótimo! Seu novo plano foi salvo com sucesso.',
	ProviderPaymentTitle: 'Análise ',
	ProviderPlanNetworkIdentificationNumber: 'Número de identificação da rede do plano do provedor',
	ProviderRemindersSettingsBannerAction: 'Ir para Gerenciamento de Fluxo de Trabalho',
	ProviderRemindersSettingsBannerDescription:
		'Encontre todos os lembretes na nova guia **Gerenciamento de Fluxo de Trabalho** em **Configurações**. Esta atualização traz novos recursos poderosos, aprimoramento de modelos e ferramentas de automação inteligentes para aumentar sua produtividade. 🚀',
	ProviderRemindersSettingsBannerTitle: 'Sua experiência com lembretes está melhorando',
	ProviderTaxonomy: 'Taxonomia do provedor',
	ProviderUPINNumber: 'Número UPIN do provedor',
	ProviderUsedStoragePercentage: '{providerName} armazenamento está {usedStoragePercentage}% cheio!',
	PsychiatricNursePractitioner: 'Enfermeira Psiquiátrica',
	Psychiatrist: 'Psiquiatra',
	Psychiatrists: 'Psiquiatras',
	Psychiatry: 'Psiquiatria',
	Psychoanalyst: 'Psicanalista',
	Psychologist: 'Psicólogo',
	Psychologists: 'Psicólogos',
	Psychology: 'Psicologia',
	Psychometrician: 'Psicometrista',
	PsychosocialRehabilitationSpecialist: 'Especialista em Reabilitação Psicossocial',
	Psychotheraphy: 'Psicoterapia',
	Psychotherapists: 'Psicoterapeutas',
	Psychotherapy: 'Psicoterapia',
	PublicCallDialogTitle: 'Videochamada com ',
	PublicCallDialogTitlePlaceholder: 'Videochamada fornecida pela Carepatron',
	PublicFormBackToForm: 'Enviar outra resposta',
	PublicFormConfirmSubmissionHeader: 'Confirmar envio',
	PublicFormNotFoundDescription:
		'O formulário que você está procurando pode ter sido excluído ou o link pode estar incorreto. Verifique a URL e tente novamente.',
	PublicFormNotFoundTitle: '<h1>Formulário não encontrado</h1>',
	PublicFormSubmissionError: 'Envio falhou. Tente novamente.',
	PublicFormSubmissionSuccess: 'Formulário enviado com sucesso',
	PublicFormSubmittedNotificationSubject: '{actorProfileName} enviou o formulário público {noteTitle}',
	PublicFormSubmittedSubtitle: 'Seu envio foi recebido.',
	PublicFormSubmittedTitle: 'Obrigado!',
	PublicFormVerifyClientEmailDialogSubtitle: 'Enviamos um código de confirmação para o seu email',
	PublicFormsInvalidConfirmationCode: 'Código de confirmação inválido',
	PublicHealthInspector: 'Inspetor de Saúde Pública',
	PublicTemplates: 'Modelos públicos',
	Publish: 'Publicar',
	PublishTemplate: 'Publicar modelo',
	PublishTemplateFeatureBannerSubheader: 'Modelos projetados para beneficiar a comunidade',
	PublishTemplateHeader: 'Publicar {title}',
	PublishTemplateToCommunity: 'Publicar modelo para a comunidade',
	PublishToCommunity: 'Publicar na comunidade',
	PublishToCommunitySuccessMessage: 'Publicado com sucesso para a comunidade',
	Published: 'Publicados',
	PublishedBy: 'Publicado por {name}',
	PublishedNotesAreNotAutosaved: 'As notas publicadas não serão salvas automaticamente',
	PublishedOnCarepatronCommunity: 'Publicado na comunidade Carepatron',
	Purchase: 'Comprar',
	PushToCalendar: 'Enviar para o calendário',
	Question: 'Pergunta',
	QuestionOrTitle: 'Pergunta ou título',
	QuickActions: 'Ações rápidas',
	QuickThemeSwitcherColorBasil: 'Manjericão',
	QuickThemeSwitcherColorBlueberry: 'Mirtilo',
	QuickThemeSwitcherColorFushcia: 'Fúcsia',
	QuickThemeSwitcherColorLapis: 'Lapis',
	QuickThemeSwitcherColorMoss: 'Musgo',
	QuickThemeSwitcherColorRose: 'Rose',
	QuickThemeSwitcherColorSquash: 'Abóbora',
	RadiationTherapist: 'Radioterapeuta',
	Radiologist: 'Radiologista',
	Read: 'Ler',
	ReadOnly: 'Somente leitura',
	ReadOnlyAppointment: 'Consulta somente leitura',
	ReadOnlyEventBanner: 'Este compromisso está sincronizado de um calendário somente leitura e não pode ser editado.',
	ReaderMaxDepthHasBeenExceededCode: 'A nota está muito aninhada. Tente remover o recuo de alguns itens.',
	ReadyForMapping: 'Pronto para o mapeamento',
	RealEstateAgent: 'Corretor de imóveis',
	RearrangeClientFields: 'Reorganizar os campos do cliente nas configurações do cliente',
	Reason: 'Razão',
	ReasonForChange: 'Razão para mudança',
	RecentAppointments: 'Compromissos recentes',
	RecentServices: 'Serviços recentes',
	RecentTemplates: 'Modelos recentes',
	RecentlyUsed: 'Usado recentemente',
	Recommended: 'Recomendado',
	RecommendedTemplates: 'Modelos recomendados',
	Recording: 'Gravação',
	RecordingEnded: 'Gravação encerrada',
	RecordingInProgress: 'Gravação em andamento',
	RecordingMicrophoneAccessErrorMessage:
		'Permita o acesso ao microfone no seu navegador e atualize para iniciar a gravação.',
	RecurrenceCount: ', {count, plural, one {uma vez} other {# vezes}}',
	RecurrenceDaily: '{count, plural, one {Diariamente} other {Dias}}',
	RecurrenceEndAfter: 'Depois',
	RecurrenceEndNever: 'Nunca',
	RecurrenceEndOn: 'Em',
	RecurrenceEvery: 'Cada {description}',
	RecurrenceMonthly: '{count, plural, one {Mensal} other {Meses}}',
	RecurrenceOn: 'em {description}',
	RecurrenceOnAllDays: 'em todos os dias',
	RecurrenceUntil: 'até {description}',
	RecurrenceWeekly: '{count, plural, one {Semanal} other {Semanas}}',
	RecurrenceYearly: '{count, plural, one {Anual} other {Anos}}',
	Recurring: 'Recorrente',
	RecurringAppointment: 'Consulta recorrente',
	RecurringAppointmentsLimitedBannerText:
		'Nem todos os compromissos recorrentes são mostrados. Reduza o intervalo de datas para ver todos os compromissos recorrentes do período.',
	RecurringEventListDescription:
		'<b>{count, plural, one {# evento} other {# eventos}}</b> serão criados nas seguintes datas',
	Redo: 'Refazer',
	ReferFriends: 'Indique amigos',
	Reference: 'Referência',
	ReferralCreditedNotificationSubject: 'Seu crédito de indicação de {currency} {amount} foi aplicado',
	ReferralEmailDefaultBody: `Obrigado(a) {name}, você recebeu uma atualização GRATUITA de 3 meses para o Carepatron. Junte-se à nossa comunidade de mais de 3 milhões de profissionais de saúde construída para uma nova forma de trabalhar!
Obrigado(a),
A equipe do Carepatron`,
	ReferralEmailDefaultSubject: 'Você foi convidado para se juntar ao Carepatron',
	ReferralHasNotSignedUpDescription: 'Seu amigo ainda não se inscreveu',
	ReferralHasSignedUpDescription: 'Seu amigo se inscreveu.',
	ReferralInformation: 'Informações de referência',
	ReferralJoinedNotificationSubject: '{actorProfileName} entrou para o Carepatron',
	ReferralListErrorDescription: 'A lista de referências não pôde ser carregada.',
	ReferralProgress: '<b>{monthsActive}/{monthsRequired} {monthsRequired, plural, one {mês} other {meses}}</b> ativo',
	ReferralRewardBanner: 'Cadastre-se e resgate sua recompensa de indicação!',
	Referrals: 'Referências',
	ReferredUserBenefitSubtitle:
		'{durationInMonths} mês {percentOff, select, 100 {grátis} other {{percentOff}% de desconto}} {type, select, SubscriptionUpgrade {upgrade} other {}}',
	ReferredUserBenefitTitle: 'Eles conseguem!',
	Referrer: 'Referenciador',
	ReferringProvider: 'Provedor de referência',
	ReferringUserBenefitSubtitle: 'USD${creditAmount} de crédito quando <mark>3 amigos</mark> ativarem.',
	ReferringUserBenefitTitle: 'Você consegue!',
	RefreshPage: 'Recarregar Página',
	Refund: 'Reembolso',
	RefundAcknowledgement: 'Eu efetuei o reembolso de {clientName} fora do Carepatron.',
	RefundAcknowledgementValidationMessage: 'Por favor, confirme que você reembolsou esse valor',
	RefundAmount: 'Valor do reembolso',
	RefundContent:
		'Os reembolsos levam de 7 a 10 dias para aparecer na conta do seu cliente. As taxas de pagamento não serão reembolsadas, mas não há cobranças extras para reembolsos. Os reembolsos não podem ser cancelados, e alguns podem precisar de revisão antes do processamento.',
	RefundCouldNotBeProcessed: 'O reembolso não pôde ser processado',
	RefundError:
		'Este reembolso não pode ser processado automaticamente no momento. Entre em contato com o suporte da Carepatron para solicitar o reembolso deste pagamento.',
	RefundExceedTotalValidationError: 'O valor não deve exceder o total pago',
	RefundFailed: 'Falha no reembolso',
	RefundFailedTooltip:
		'O reembolso deste pagamento falhou anteriormente e não pode ser tentado novamente. Entre em contato com o suporte.',
	RefundNonStripePaymentContent:
		'Este pagamento foi feito usando um método fora do Carepatron (por exemplo, dinheiro, internet banking). Emitir um reembolso dentro do Carepatron não retornará nenhum fundo ao cliente.',
	RefundReasonDescription: 'Adicionar um motivo de reembolso pode ajudar ao revisar as transações de seus clientes',
	Refunded: 'Reembolsado',
	Refunds: 'Reembolsos',
	RefundsTableEmptyState: 'Nenhum reembolso encontrado',
	Regenerate: 'Regenerar',
	RegisterButton: 'Registro',
	RegisterEmail: 'E-mail',
	RegisterFirstName: 'Primeiro nome',
	RegisterLastName: 'Sobrenome',
	RegisterPassword: 'Senha',
	RegisteredNurse: 'Enfermeira registrada',
	RehabilitationCounselor: 'Conselheiro de Reabilitação',
	RejectAppointmentFormTitle: 'Não consegue? Por favor, deixe-nos saber o porquê e proponha um novo horário.',
	Rejected: 'Rejeitado',
	Relationship: 'Relação',
	RelationshipDetails: 'Detalhes do relacionamento',
	RelationshipEmptyStateTitle: 'Fique conectado com quem apoia seu cliente',
	RelationshipPageAccessTypeColumnName: 'Acesso ao perfil',
	RelationshipSavedSuccessSnackbar: 'Relacionamento salvo com sucesso!',
	RelationshipSelectorFamilyAdmin: 'Família',
	RelationshipSelectorFamilyMember: 'Membro da família',
	RelationshipSelectorProviderAdmin: 'Administrador do provedor',
	RelationshipSelectorProviderStaff: 'Equipe do provedor',
	RelationshipSelectorSupportNetworkPrimary: 'Amigo',
	RelationshipSelectorSupportNetworkSecondary: 'Rede de suporte',
	RelationshipStatus: 'status de relacionamento',
	RelationshipType: 'Tipo de relacionamento',
	RelationshipTypeClientOwner: 'Cliente',
	RelationshipTypeFamilyAdmin: 'Relacionamentos',
	RelationshipTypeFamilyMember: 'Família',
	RelationshipTypeFriendOrSupport: 'Amigo ou rede de apoio',
	RelationshipTypeProviderAdmin: 'Administrador do provedor',
	RelationshipTypeProviderStaff: 'Funcionários',
	RelationshipTypeSelectorPlaceholder: 'Buscar tipos de relacionamento',
	Relationships: 'Relacionamentos',
	Remaining: 'restante',
	RemainingTime: '{time} restantes',
	Reminder: 'Lembrete',
	ReminderColor: 'Cor do lembrete',
	ReminderDetails: 'Detalhes do lembrete',
	ReminderEditDisclaimer: 'As alterações só serão refletidas em novas nomeações',
	ReminderSettings: 'Configurações de lembrete de compromisso',
	Reminders: 'Lembretes',
	Remove: 'Remover',
	RemoveAccess: 'Remover acesso',
	RemoveAllGuidesBtn: 'Remover todos os guias',
	RemoveAllGuidesPopoverBody:
		'Quando terminar de usar os guias de integração, basta usar o botão remover guias em cada painel.',
	RemoveAllGuidesPopoverTitle: 'Não precisa mais de seus guias de integração?',
	RemoveAsDefault: 'Remover como padrão',
	RemoveAsIntake: 'Remover como entrada',
	RemoveCol: 'Remover coluna',
	RemoveColor: 'Remover cor',
	RemoveField: 'Remover campo',
	RemoveFromCall: 'Remover da chamada',
	RemoveFromCallDescription: 'Tem certeza de que deseja remover <mark>{attendeeName}</mark> desta videochamada?',
	RemoveFromCollection: 'Remover da coleção',
	RemoveFromCommunity: 'Remover da comunidade',
	RemoveFromFolder: 'Remover da pasta',
	RemoveFromFolderConfirmationDescription:
		'Tem certeza de que deseja remover este modelo desta pasta? Esta ação não pode ser desfeita, mas você poderá optar por movê-lo de volta mais tarde.',
	RemoveFromIntakeDefault: 'Remover do padrão de entrada',
	RemoveGuides: 'Remover guias',
	RemoveMfaConfirmationDescription:
		'Remover a Autenticação Multifator (MFA) reduzirá a segurança da sua conta. Você quer prosseguir?',
	RemoveMfaConfirmationTitle: 'Remover MFA?',
	RemovePaymentMethodDescription: `Isto removerá todo o acesso e uso futuro deste método de pagamento.
 Esta ação não pode ser desfeita.`,
	RemoveRow: 'Remover linha',
	RemoveTable: 'Remover tabela',
	RemoveTemplateAsDefaultIntakeSuccess: 'Removido com sucesso {templateTitle} como modelo de entrada padrão',
	RemoveTemplateFromCommunity: 'Remover modelo da comunidade',
	RemoveTemplateFromFolder: '{templateTitle} removido com sucesso de {folderTitle}',
	Rename: 'Renomear',
	RenderingProvider: 'Provedor de renderização',
	Reopen: 'Reabrir',
	ReorderServiceGroupFailure: 'Falha ao reordenar a coleção',
	ReorderServiceGroupSuccess: 'Reordenação bem-sucedida da coleção',
	ReorderServicesFailure: 'Falha ao reordenar serviços',
	ReorderServicesSuccess: 'Reordenar serviços com sucesso',
	ReorderYourServiceList: 'Reordenar sua lista de serviços',
	ReorderYourServiceListDescription:
		'A maneira como você organiza seus serviços e coleções será refletida em sua página de reservas on-line para que todos os seus clientes a vejam!',
	RepeatEvery: 'Repetir a cada',
	RepeatOn: 'Repetir em',
	Repeating: 'recorrente',
	Repeats: 'Repete',
	RepeatsEvery: 'Repete a cada',
	Rephrase: 'Reformular',
	Replace: 'Substituir',
	ReplaceBackground: 'Substituir plano de fundo',
	ReplacementOfPriorClaim: 'Substituição da reivindicação anterior',
	Report: 'Relatório',
	Reprocess: 'Reprocess',
	RepublishTemplateToCommunity: 'Republique o modelo para a comunidade',
	RequestANewVerificationLink: 'Solicite um novo link de verificação',
	RequestCoverageReport: 'Solicitar relatório de cobertura',
	RequestingDevicePermissions: 'Solicitando permissões de dispositivo...',
	RequirePaymentMethodDesc: 'Os clientes devem inserir os dados do cartão de crédito para reservar on-line',
	RequirePaymentMethodLabel: 'Exigir detalhes do cartão de crédito',
	Required: 'obrigatório',
	RequiredField: 'Obrigatório',
	RequiredUrl: 'O URL é obrigatório.',
	Reschedule: 'Reprogramar',
	RescheduleBookingLinkModalDescription: 'Seu cliente pode alterar a data e hora do agendamento usando este link.',
	RescheduleBookingLinkModalTitle: 'Link para reagendar reserva',
	RescheduleLink: 'Reagendar link',
	Resend: 'Reenviar',
	ResendConfirmationCode: 'Reenviar código de confirmação',
	ResendConfirmationCodeDescription:
		'Por favor, insira seu endereço de e-mail e enviaremos outro código de confirmação por e-mail',
	ResendConfirmationCodeSuccess: 'O código de confirmação foi reenviado, verifique sua caixa de entrada',
	ResendNewEmailVerificationSuccess: 'Novo link de verificação foi enviado para {email}',
	ResendVerificationEmail: 'Reenviar email de verificação',
	Reset: 'Reiniciar',
	Resources: 'Recursos',
	RespiratoryTherapist: 'Terapeuta respiratório',
	RespondToHistoricAppointmentError:
		'Esta é uma consulta histórica. Entre em contato com seu médico se tiver alguma dúvida.',
	Responder: 'Respondente',
	RestorableItemModalDescription:
		'Tem certeza de que deseja excluir {context}?{canRestore, select, true { Você pode restaurá-lo mais tarde.} other {}}',
	RestorableItemModalTitle: 'Excluir {type}',
	Restore: 'Restaurar',
	RestoreAll: 'Restaurar tudo',
	Restricted: 'Restrito',
	ResubmissionCodeReferenceNumber: 'Código de reenvio e número de referência',
	Resubmit: 'Reenviar',
	Resume: 'Retomar',
	Retry: 'Tentar novamente',
	RetryingConnectionAttempt: 'Tentando reconectar... (Tentativa {retryCount} de {maxRetries})',
	ReturnToForm: 'Voltar ao formulário',
	RevertClaimStatus: 'Reverter status da reclamação',
	RevertClaimStatusDescriptionBody:
		'Esta solicitação possui pagamentos vinculados e alterar o status pode afetar o acompanhamento ou o processamento do pagamento, o que pode exigir conciliação manual.',
	RevertClaimStatusDescriptionTitle: 'Tem certeza de que deseja reverter para {status}?',
	RevertClaimStatusError: 'Falha ao reverter o status da reclamação',
	RevertToDraft: 'Reverter para rascunho',
	Review: 'Análise',
	ReviewsFirstQuote: 'Agendamentos em qualquer lugar, a qualquer hora',
	ReviewsSecondJobTitle: 'Clínica Vida',
	ReviewsSecondName: 'Clara W.',
	ReviewsSecondQuote:
		'Eu também adoro o aplicativo carepatron. Ajuda-me a acompanhar meus clientes e trabalhar em trânsito.',
	ReviewsThirdJobTitle: 'Clínica da Baía de Manila',
	ReviewsThirdName: 'Jackie H.',
	ReviewsThirdQuote:
		'A facilidade de navegação e a bela interface do usuário trazem um sorriso ao meu rosto todos os dias.',
	RightAlign: 'Alinhar à direita',
	Role: 'Papel',
	Roster: 'Participantes',
	RunInBackground: 'Executar em segundo plano',
	SMS: 'SMS',
	SMSAndEmailReminder: 'SMS ',
	SSN: 'SSN',
	SafetyRedirectHeading: 'Você está saindo do Carepatron',
	SafetyRedirectSubtext: 'Se você confia neste link, selecione-o para continuar',
	SalesRepresentative: 'Representante de vendas',
	SalesTax: 'Imposto sobre vendas',
	SalesTaxHelp: 'Inclui imposto sobre vendas nas faturas geradas',
	SalesTaxIncluded: 'Sim',
	SalesTaxNotIncluded: 'Não',
	SaoPaulo: 'São Paulo',
	Saturday: 'Sábado',
	Save: 'Salvar',
	SaveAndClose: 'Salvar ',
	SaveAndExit: 'Salvar ',
	SaveAndLock: 'Salvar e bloquear',
	SaveAsDraft: 'Salvar como rascunho',
	SaveCardForFuturePayments: 'Guarde o cartão para pagamentos futuros',
	SaveChanges: 'Salvar alterações',
	SaveCollection: 'Salvar coleção',
	SaveField: 'Salvar campo',
	SavePaymentMethod: 'Salvar forma de pagamento',
	SavePaymentMethodDescription: 'Você não será cobrado até sua primeira consulta.',
	SavePaymentMethodSetupError:
		'Ocorreu um erro inesperado e não foi possível configurar os pagamentos neste momento.',
	SavePaymentMethodSetupInvoiceLater: 'Os pagamentos podem ser configurados e salvos ao pagar sua primeira fatura.',
	SaveSection: 'Salvar seção',
	SaveService: 'Criar novo serviço',
	SaveTemplate: 'Salvar modelo',
	Saved: 'Salvou',
	SavedCards: 'Cartões salvos',
	SavedPaymentMethods: 'Salvou',
	Saving: 'Salvando...',
	ScheduleAppointmentsAndOnlineServices: 'Agendar consultas e serviços online',
	ScheduleName: 'Nome do agendamento',
	ScheduleNew: 'Agendar novo',
	ScheduleSend: 'Agendar envio',
	ScheduleSendAlertInfo: 'As conversas programadas serão enviadas em seu tempo programado.',
	ScheduleSendByName: '<strong>Enviar agendamento</strong> • {time} por {displayName}',
	ScheduleSetupCall: 'Agendar chamada de configuração',
	Scheduled: 'Programado',
	SchedulingSend: 'Agendar envio',
	School: 'Escola',
	ScrollToTop: 'Role para cima',
	Search: 'Procurar',
	SearchAndConvertToLanguage: 'Pesquisar e converter para idioma',
	SearchBasicBlocks: 'Pesquisar blocos básicos',
	SearchByName: 'Procura por nome',
	SearchClaims: 'Pesquisar sinistros',
	SearchClientFields: 'Pesquisar campos do cliente',
	SearchClients: 'Pesquise por nome do cliente, ID do cliente ou número de telefone',
	SearchCommandNotFound: 'Nenhum resultado para "{searchTerm}" foi encontrado',
	SearchContacts: 'Cliente ou contato',
	SearchContactsPlaceholder: 'Pesquisar contatos',
	SearchConversations: 'Pesquisar conversas',
	SearchInputPlaceholder: 'Pesquisar todos os recursos',
	SearchInvoiceNumber: 'Pesquisar número da fatura',
	SearchInvoices: 'Pesquisar faturas',
	SearchMultipleContacts: 'Clientes ou contatos',
	SearchMultipleContactsOptional: 'Clientes ou contatos (opcional)',
	SearchOrCreateATag: 'Pesquise ou crie uma tag',
	SearchPayments: 'Pesquisar pagamentos',
	SearchPrepopulatedData: 'Pesquisar campos de dados pré-preenchidos',
	SearchRelationships: 'Relacionamentos de pesquisa',
	SearchRemindersAndWorkflows: 'Pesquisar lembretes e fluxos de trabalho',
	SearchServices: 'Serviços de pesquisa',
	SearchTags: 'Tags de pesquisa',
	SearchTeamMembers: 'Pesquisar membros da equipe',
	SearchTemplatePlaceholder: 'Pesquise {templateCount}+ recursos',
	SearchTimezone: 'Pesquisar fuso horário...',
	SearchTrashItems: 'Pesquisar itens',
	SearchUnsplashPlaceholder: 'Pesquise fotos gratuitas de alta resolução no Unsplash',
	Secondary: 'Secundário',
	SecondaryInsurance: 'Seguro secundário',
	SecondaryPolicy: 'Seguro secundário',
	SecondaryTimezone: 'Fuso horário secundário',
	Secondly: 'Em segundo lugar',
	Section: 'Seção',
	SectionCannotBeEmpty: 'Uma seção deve ter pelo menos uma linha',
	SectionFormSecondaryText: 'Título e descrição da seção',
	SectionName: 'Nome da Seção',
	Sections: 'Seções',
	SeeLess: 'Ver menos',
	SeeLessUpcomingAppointments: 'Ver menos compromissos futuros',
	SeeMore: 'Ver mais',
	SeeMoreUpcomingAppointments: 'Veja mais compromissos futuros',
	SeeTemplateLibrary: 'Veja a biblioteca de modelos',
	Seen: 'Visto',
	SeenByName: '**Visto** • {time} por {displayName}',
	SelectAll: 'Selecionar tudo',
	SelectAssignees: 'Selecione os responsáveis',
	SelectAttendees: 'Selecione os participantes',
	SelectCollection: 'Selecione a coleção',
	SelectCorrespondingAttributes: 'Selecione os atributos correspondentes',
	SelectPayers: 'Selecionar pagadores',
	SelectProfile: 'Selecione o perfil',
	SelectServices: 'Selecione serviços',
	SelectTags: 'Selecione tags',
	SelectTeamOrCommunity: 'Selecione Equipe ou Comunidade',
	SelectTemplate: 'Selecione o modelo',
	SelectType: 'Selecione o tipo',
	Selected: 'Selecionado',
	SelfPay: 'Pagamento próprio',
	Send: 'Enviar',
	SendAndClose: 'Enviar e fechar',
	SendAndStopIgnore: 'Enviar e parar de ignorar',
	SendEmail: 'Enviar email',
	SendIntake: 'Enviar entrada',
	SendIntakeAndForms: 'Enviar entrada ',
	SendMeACopy: 'Envie-me uma cópia',
	SendNotificationEmailWarning:
		'Alguns participantes não possuem um endereço de e-mail e não receberão notificações e lembretes automáticos.',
	SendNotificationLabel: 'Escolha os participantes para notificar por e-mail',
	SendOnlinePayment: 'Enviar pagamento on-line',
	SendOnlinePaymentTooltipTitleAdmin: 'Adicione suas configurações de pagamento preferidas',
	SendOnlinePaymentTooltipTitleStaff: 'Peça ao proprietário do provedor para configurar pagamentos online.',
	SendPaymentLink: 'Enviar link de pagamento',
	SendReaction: 'Envie uma reação',
	SendScheduledForDate: 'Enviar programado para {date}',
	SendVerificationEmail: 'Enviar e-mail de verificação',
	SendingFailed: 'Falha no envio',
	Sent: 'Enviado',
	SentByName: '**Enviado** • {time} por {displayName}',
	Seoul: 'Seul',
	SeparateDuplicateClientsDescription:
		'Os registros do cliente escolhido permanecerão separados do restante, a menos que você opte por mesclá-los',
	Service: 'Serviço',
	'Service/s': 'Serviços',
	ServiceAdjustment: 'Ajuste de serviço',
	ServiceAllowNewClientsIndicator: 'Permitir novos clientes',
	ServiceAlreadyExistsInCollection: 'O serviço já existe na coleção',
	ServiceBookableOnlineIndicator: 'Reserva on-line',
	ServiceCode: 'Código',
	ServiceCodeErrorMessage: 'É necessário um código de serviço',
	ServiceCodeSelectorPlaceholder: 'Adicione um código de serviço',
	ServiceColour: 'Cor do serviço',
	ServiceCoverageDescription: 'Escolha os serviços qualificados e co-pague esta apólice de seguro.',
	ServiceCoverageGoToServices: 'Ir para serviços',
	ServiceCoverageNoServicesDescription:
		'Personalize os valores de co-pagamento de serviço para substituir o co-pagamento da apólice padrão. Desabilite a cobertura para evitar que serviços sejam reivindicados contra a apólice.',
	ServiceCoverageNoServicesLabel: 'Nenhum serviço foi encontrado.',
	ServiceCoverageTitle: 'Cobertura de serviço',
	ServiceDate: 'Data de serviço',
	ServiceDetails: 'Detalhes do serviço',
	ServiceDuration: 'Duração',
	ServiceEmptyState: 'Ainda não há serviços',
	ServiceErrorMessage: 'O serviço é obrigatório',
	ServiceFacility: 'Instalação de serviço',
	ServiceName: 'Nome do Serviço',
	ServiceRate: 'Avaliar',
	ServiceReceiptRequiresReviewNotificationSubject:
		'Superbill {serviceReceiptNumber, select, undefined {} other {{serviceReceiptNumber}}} para {serviceReceiptNumber, select, undefined {usuário} other {{clientName}}} requer informações adicionais',
	ServiceSalesTax: 'Imposto sobre vendas',
	ServiceType: 'Serviço',
	ServiceWorkerForceUIUpdateDialogDescription:
		'Clique em recarregar para atualizar e obter as atualizações mais recentes do Carepatron.',
	ServiceWorkerForceUIUpdateDialogReloadButton: 'recarregar',
	ServiceWorkerForceUIUpdateDialogSubTitle: 'Você está usando uma versão mais antiga',
	ServiceWorkerForceUIUpdateDialogTitle: 'Bem vindo de volta!',
	Services: 'Serviços',
	ServicesAndAvailability: 'Serviços ',
	ServicesAndDiagnosisCodesHeader: 'Adicione serviços e códigos de diagnóstico',
	ServicesCount: '{count,plural,=0{Serviços}one{Serviço}other{Serviços}}',
	ServicesPlaceholder: 'Serviços',
	ServicesProvidedBy: 'Serviços fornecidos por',
	SetAPhysicalAddress: 'Defina um endereço físico',
	SetAVirtualLocation: 'Defina um local virtual',
	SetAsDefault: 'Definir como padrão',
	SetAsIntake: 'Definir como ingestão',
	SetAsIntakeDefault: 'Definir como padrão de entrada',
	SetAvailability: 'Definir disponibilidade',
	SetTemplateAsDefaultIntakeSuccess:
		'Configuração de {templateTitle} como modelo de entrada padrão realizada com sucesso',
	SetUpMfaButton: 'Configurar MFA',
	SetYourLocation: 'Defina sua <mark>localização</mark>',
	SetYourLocationDescription: 'Não tenho endereço comercial <span>(apenas serviços online e móveis)</span>',
	SettingUpPayers: 'Configurando pagadores',
	Settings: 'Configurações',
	SettingsNewUserPasswordDescription:
		'Depois de se inscrever, enviaremos um código de confirmação que você poderá usar para confirmar sua conta',
	SettingsNewUserPasswordTitle: 'Inscreva-se no Carepatron',
	SettingsTabAutomation: 'Automação',
	SettingsTabBillingDetails: 'Detalhes de faturamento',
	SettingsTabConnectedApps: 'Aplicativos conectados',
	SettingsTabCustomFields: 'Os campos personalizados',
	SettingsTabDetails: 'Detalhes',
	SettingsTabInvoices: 'Faturas',
	SettingsTabLocations: 'Localizações',
	SettingsTabNotifications: 'Notificações',
	SettingsTabOnlineBooking: 'Marcação online',
	SettingsTabPayers: 'Pagadores',
	SettingsTabReminders: 'Lembretes',
	SettingsTabServices: 'Serviços',
	SettingsTabServicesAndAvailability: 'Serviços e disponibilidade',
	SettingsTabSubscriptions: 'Assinaturas',
	SettingsTabWorkflowAutomations: 'Automações',
	SettingsTabWorkflowReminders: 'Lembretes básicos',
	SettingsTabWorkflowTemplates: 'Modelos',
	Setup: 'Configurar',
	SetupGuide: 'Guia de configuração',
	SetupGuideAddServicesActionLabel: 'Começar',
	SetupGuideAddServicesSubtitle: '4 etapas • 2 min',
	SetupGuideAddServicesTitle: 'Adicione seus serviços',
	SetupGuideEnableOnlinePaymentsActionLabel: 'Começar',
	SetupGuideEnableOnlinePaymentsSubtitle: '4 etapas • 3 min',
	SetupGuideEnableOnlinePaymentsTitle: 'Habilitar pagamentos online',
	SetupGuideImportClientsActionLabel: 'Iniciar',
	SetupGuideImportClientsSubtitle: '4 etapas • 3 min',
	SetupGuideImportClientsTitle: 'Importe seus clientes',
	SetupGuideImportTemplatesActionLabel: 'Começar',
	SetupGuideImportTemplatesSubtitle: '2 etapas • 1 min',
	SetupGuideImportTemplatesTitle: 'Importe seus templates',
	SetupGuidePersonalizeWorkspaceActionLabel: 'Iniciar',
	SetupGuidePersonalizeWorkspaceSubtitle: '3 etapas • 2 min',
	SetupGuidePersonalizeWorkspaceTitle: 'Personalize seu espaço de trabalho',
	SetupGuideSetLocationActionLabel: 'Iniciar',
	SetupGuideSetLocationSubtitle: '4 etapas • 1 min',
	SetupGuideSetLocationTitle: 'Defina sua localização',
	SetupGuideSuggestedAddTeamMembersActionLabel: 'Convidar equipe',
	SetupGuideSuggestedAddTeamMembersSubtitle: 'Convide sua equipe para se comunicar e gerenciar tarefas sem esforço.',
	SetupGuideSuggestedAddTeamMembersTag: 'Configuração',
	SetupGuideSuggestedAddTeamMembersTitle: 'Adicionar membros da equipe',
	SetupGuideSuggestedCustomizeBrandActionLabel: 'Personalizar',
	SetupGuideSuggestedCustomizeBrandSubtitle: 'Sinta-se profissional com seu logotipo exclusivo e cores da marca.',
	SetupGuideSuggestedCustomizeBrandTitle: 'Personalizar marca',
	SetupGuideSuggestedDownloadMobileAppActionLabel: 'Baixar',
	SetupGuideSuggestedDownloadMobileAppSubtitle:
		'Acesse seu espaço de trabalho em qualquer lugar, a qualquer hora, em qualquer dispositivo.',
	SetupGuideSuggestedDownloadMobileAppTag: 'Configuração',
	SetupGuideSuggestedDownloadMobileAppTitle: 'Baixe o aplicativo',
	SetupGuideSuggestedEditAvailabilityActionLabel: 'Definir disponibilidade',
	SetupGuideSuggestedEditAvailabilitySubtitle: 'Evite duplicidades de agendamento definindo sua disponibilidade.',
	SetupGuideSuggestedEditAvailabilityTag: 'Agendamento',
	SetupGuideSuggestedEditAvailabilityTitle: 'Editar disponibilidade',
	SetupGuideSuggestedImportClientsActionLabel: 'Importar',
	SetupGuideSuggestedImportClientsSubtitle:
		'Carregue seus dados de clientes existentes instantaneamente com apenas um clique.',
	SetupGuideSuggestedImportClientsTag: 'Configuração',
	SetupGuideSuggestedImportClientsTitle: 'Importar clientes',
	SetupGuideSuggestedPersonalizeRemindersActionLabel: 'Editar lembretes',
	SetupGuideSuggestedPersonalizeRemindersSubtitle:
		'Reduza o número de faltas com lembretes automáticos de agendamento.',
	SetupGuideSuggestedPersonalizeRemindersTitle: 'Lembretes personalizados',
	SetupGuideSuggestedStartVideoCallActionLabel: 'Iniciar chamada',
	SetupGuideSuggestedStartVideoCallSubtitle:
		'Faça uma chamada e conecte-se com os clientes usando nossas ferramentas de vídeo com tecnologia de IA.',
	SetupGuideSuggestedStartVideoCallTag: 'Telemedicina',
	SetupGuideSuggestedStartVideoCallTitle: 'Iniciar chamada de vídeo',
	SetupGuideSuggestedTryActionsTitle: 'Coisas para tentar 🚀',
	SetupGuideSuggestedUseAIAssistantActionLabel: 'Experimente o Assistente de IA',
	SetupGuideSuggestedUseAIAssistantSubtitle:
		'Obtenha respostas instantâneas para todas as suas perguntas de trabalho.',
	SetupGuideSuggestedUseAIAssistantTag: 'Novo',
	SetupGuideSuggestedUseAIAssistantTitle: 'Use AI assistant',
	SetupGuideSyncCalendarActionLabel: 'Começar',
	SetupGuideSyncCalendarSubtitle: '1 etapa • menos de 1 min',
	SetupGuideSyncCalendarTitle: 'Sincronize seu calendário',
	SetupGuideVerifyEmailLabel: 'Verificar',
	SetupGuideVerifyEmailSubtitle: '2 etapas • 2 min',
	SetupOnlineStripePayments: 'Use o Stripe para pagamentos online',
	SetupPayments: 'Configurar pagamentos',
	Sex: 'Sexo',
	SexSelectorPlaceholder: 'Masculino / Feminino / Prefiro não dizer',
	Share: 'Compartilhar',
	ShareBookingLink: 'Compartilhar link de reserva',
	ShareNoteDefaultMessage: `Olá{name} compartilhou "{documentName}" com você.

Obrigado,
{practiceName}`,
	ShareNoteMessage: `Olá
{name} compartilhou "{documentName}" {isResponder, select, true {com algumas perguntas para você preencher.} other {com você.}}

Obrigado,
{practiceName}`,
	ShareNoteTitle: 'Compartilhar ‘{noteTitle}’',
	ShareNotesWithClients: 'Compartilhe com clientes ou contatos',
	ShareScreen: 'Compartilhar tela',
	ShareScreenNotSupported: 'Seu dispositivo/navegador não suporta o recurso de compartilhamento de tela',
	ShareScreenWithId: 'Tela {screenId}',
	ShareTemplateAsPublicFormModalDescription: 'Permitir que outros vejam este modelo e o enviem como um formulário.',
	ShareTemplateAsPublicFormModalTitle: 'Compartilhar link para ‘{title}’',
	ShareTemplateAsPublicFormSaved: 'Configuração do formulário público atualizada com sucesso',
	ShareTemplateAsPublicFormSectionCustomization: 'Personalização',
	ShareTemplateAsPublicFormShowPoweredBy: 'Mostrar "Powered by Carepatron" no meu formulário',
	ShareTemplateAsPublicFormShowPoweredByDisabledMessage: 'Mostrar/ocultar “Powered by Carepatron” no meu formulário',
	ShareTemplateAsPublicFormTrigger: 'Compartilhar',
	ShareTemplateAsPublicFormUseWorkspaceBranding: 'Use branding do espaço de trabalho',
	ShareTemplateAsPublicFormUseWorkspaceBrandingDisabledMessage: 'Mostrar/ocultar a marca do espaço de trabalho',
	ShareTemplateAsPublicFormVerificationOptionAlwaysDescription:
		'Envia código para clientes existentes e não existentes',
	ShareTemplateAsPublicFormVerificationOptionAlwaysSelectedWarning:
		'Assinaturas sempre exigem que o email seja verificado',
	ShareTemplateAsPublicFormVerificationOptionExistingDescription: 'Envia código apenas para clientes existentes',
	ShareTemplateAsPublicFormVerificationOptionNeverDescription: 'Nunca envia código',
	ShareTemplateAsPublicFormVerificationOptionNeverSelectedWarning: `Selecionar 'Nunca' pode permitir que usuários não verificados sobrescrevam dados do cliente se eles usarem o endereço de e-mail de um cliente existente.`,
	ShareWithCommunity: 'Compartilhe com a comunidade',
	ShareYourReferralLink: 'Compartilhe seu link de indicação',
	ShareYourScreen: 'Compartilhe sua tela',
	SheHer: 'Ela/Dela',
	ShortTextAnswer: 'Resposta curta em texto',
	ShortTextFormPrimaryText: 'Pequeno texto',
	ShortTextFormSecondaryText: 'Resposta com menos de 300 caracteres',
	Show: 'Mostrar',
	ShowColumn: 'Mostrar coluna',
	ShowColumnButton: 'Mostrar botão coluna {value}',
	ShowColumns: 'Mostrar colunas',
	ShowColumnsMenu: 'Mostrar menu de colunas',
	ShowDateDurationDescription: 'por exemplo. 29 anos',
	ShowDateDurationLabel: 'Mostrar duração da data',
	ShowDetails: 'Mostrar detalhes',
	ShowField: 'Mostrar campo',
	ShowFullAddress: 'Mostrar endereço',
	ShowHideFields: 'Mostrar ocultar campos',
	ShowIcons: 'Mostrar ícones',
	ShowLess: 'Mostre menos',
	ShowMeetingTimers: 'Exibir cronômetros de reuniões',
	ShowMenu: 'Mostrar menu',
	ShowMergeSummarySidebar: 'Mostrar resumo da mesclagem',
	ShowMore: 'Mostre mais',
	ShowOnTranscript: 'Mostrar na transcrição',
	ShowReactions: 'Mostrar reações',
	ShowSection: 'Mostrar seção',
	ShowServiceCode: 'Mostrar código de serviço',
	ShowServiceDescription: 'Mostrar descrição nas reservas de serviço',
	ShowServiceDescriptionDesc: 'Os clientes podem visualizar as descrições dos serviços ao fazer a reserva',
	ShowServiceGroups: 'Mostrar coleções',
	ShowServiceGroupsDesc: 'Os clientes verão serviços agrupados por cobrança no momento da reserva',
	ShowSpeakers: 'Mostrar palestrantes',
	ShowTax: 'Mostrar imposto',
	ShowTimestamp: 'Mostrar carimbo de data/hora',
	ShowUnits: 'Mostrar unidades',
	ShowWeekends: 'Mostrar fins de semana',
	ShowYourView: 'Mostre sua visão',
	SignInWithApple: 'Faça login com a Apple',
	SignInWithGoogle: 'Faça login no Google',
	SignInWithMicrosoft: 'Faça login com a Microsoft',
	SignUpTitleReferralDefault: '<mark>Inscrever-se</mark> e reivindique sua recompensa por indicação',
	SignUpTitleReferralUpgrade:
		'Comece sua assinatura de <mark>{durationInMonths} mês {percentOff, select, 100 {grátis} other {{percentOff}% de desconto}} </mark>',
	SignatureCaptureError: 'Não foi possível capturar a assinatura. Por favor, tente novamente.',
	SignatureFormPrimaryText: 'Assinatura',
	SignatureFormSecondaryText: 'Obtenha uma assinatura digital',
	SignatureInfoTooltip: 'Esta representação visual não é uma assinatura eletrônica válida.',
	SignaturePlaceholder: 'Desenhe sua assinatura aqui',
	SignedBy: 'Assinado por',
	Signup: 'Inscrever-se',
	SignupAgreements: 'Concordo com os {termsOfUse} e a {privacyStatement} para minha conta.',
	SignupBAA: 'Contrato de parceria comercial',
	SignupBusinessAgreements:
		'Em nome próprio e da empresa, concordo com o {businessAssociateAgreement}, o {termsOfUse} e o {privacyStatement} para minha conta.',
	SignupInvitationForYou: 'Você foi convidado a usar o Carepatron.',
	SignupPageProviderWarning:
		'Se o seu administrador já criou uma conta, você precisa pedir que ele convide você para esse provedor. Não use este formulário de inscrição. Para mais informações, veja',
	SignupPageProviderWarningLink: 'esse link.',
	SignupPrivacy: 'política de Privacidade',
	SignupProfession: 'Qual profissão melhor descreve você?',
	SignupSubtitle:
		'O software de gerenciamento de consultórios da Carepatron é feito para profissionais individuais e equipes. Pare de pagar taxas excessivas e faça parte da revolução.',
	SignupSuccessDescription:
		'Confirme seu endereço de e-mail para iniciar sua integração. Caso não receba imediatamente, verifique sua pasta de spam.',
	SignupSuccessTitle: 'Por favor verifique seu email',
	SignupTermsOfUse: 'Termos de uso',
	SignupTitleClient: '<mark>Gerencie sua saúde</mark> de um lugar',
	SignupTitleLast: 'e todo o trabalho que você faz! - É grátis',
	SignupTitleOne: '<mark>Fortalecendo você</mark> , ',
	SignupTitleThree: '<mark>Capacitando seus clientes</mark> , ',
	SignupTitleTwo: '<mark>Fortalecendo sua equipe</mark> , ',
	Simple: 'Simples',
	SimplifyBillToDetails: 'Simplifique a fatura até os detalhes',
	SimplifyBillToHelperText: 'Somente a primeira linha é usada quando corresponde ao cliente',
	Singapore: 'Cingapura',
	Single: 'Solteiro',
	SingleChoiceFormPrimaryText: 'Escolha única',
	SingleChoiceFormSecondaryText: 'Escolha apenas uma opção',
	Sister: 'Irmã',
	SisterInLaw: 'Cunhada',
	Skip: 'Pular',
	SkipLogin: 'Pular login',
	SlightBlur: 'Desfoque levemente o fundo',
	Small: 'Pequeno',
	SmartChips: 'Chips inteligentes',
	SmartDataChips: 'Chips de dados inteligentes',
	SmartReply: 'Resposta rápida',
	SmartSuggestNewClient: '<strong>Sugestões Inteligentes</strong> criar {name} como um novo cliente',
	SmartSuggestedFieldDescription: 'Este campo é uma sugestão inteligente',
	SocialSecurityNumber: 'Número da Segurança Social',
	SocialWork: 'Trabalho social',
	SocialWorker: 'Assistente social',
	SoftwareDeveloper: 'Desenvolvedor de software',
	Solo: 'Só',
	Someone: 'Alguém',
	Son: 'Filho',
	SortBy: 'Ordenar por',
	SouthAmerica: 'América do Sul',
	Speaker: 'Palestrante',
	SpeakerSource: 'Fonte do alto-falante',
	Speakers: 'caixas de som',
	SpecifyPaymentMethod: 'Especifique a forma de pagamento',
	SpeechLanguagePathology: 'Fonoaudiologia',
	SpeechTherapist: 'Terapeuta da fala',
	SpeechTherapists: 'Fonoaudiólogos',
	SpeechTherapy: 'Terapia de fala',
	SportsMedicinePhysician: 'Médico de Medicina Esportiva',
	Spouse: 'Cônjuge',
	SpreadsheetColumnExample: 'por exemplo ',
	SpreadsheetColumns: 'Colunas da planilha',
	SpreadsheetUploaded: 'Planilha enviada',
	SpreadsheetUploading: 'Enviando...',
	Staff: 'Funcionários',
	StaffAccessDescriptionAdmin: 'Os administradores podem gerenciar tudo na plataforma.',
	StaffAccessDescriptionStaff: `Os membros da equipe podem gerenciar clientes, notas e documentação que criaram ou foram compartilhadas
 com eles, agendar compromissos, gerenciar faturas.`,
	StaffContactAssignedSubject:
		'{actorProfileName} atribuiu {totalCount, plural, =1 {{contactName1}} =2 {{contactName1} e {contactName2}} other {{contactName1}, {contactName2}}}{totalCount, plural, offset:2 =0 {} =1 {} =2 {} =3 { e 1 outro cliente} other { e # outros clientes}}',
	StaffInboxAssignedNotificationSubject: '{actorProfileName} compartilhou a caixa de entrada {inboxName} com você',
	StaffInboxUnassignedNotificationSubject: '{actorProfileName} removeu seu acesso à caixa de entrada {inboxName}',
	StaffMembers: 'Membros da equipe',
	StaffMembersNumber: '{billedUsers, plural, one {# membro da equipe} other {# membros da equipe}}',
	StaffSavedSuccessSnackbar: 'Informações dos membros da equipe salvas com sucesso!',
	StaffSelectorAdminRole: 'Administrador',
	StaffSelectorStaffRole: 'Membro da equipe',
	StandardAppointment: 'Consulta Padrão',
	StandardColor: 'Cor da tarefa',
	StartAndEndTime: 'Hora de início e término',
	StartCall: 'Iniciar chamada',
	StartDate: 'Data de início',
	StartDictating: 'Comece a ditar',
	StartImport: 'Iniciar importação',
	StartRecordErrorTitle: 'Ocorreu um erro ao iniciar sua gravação',
	StartRecording: 'Iniciar gravação',
	StartTimeIncrements: 'Incrementos de horário de início',
	StartTimeIncrementsView: '{startTimeIncrements} min intervalos',
	StartTranscribing: 'Comece a Transcrever',
	StartTranscribingNotes:
		'Selecione os clientes para os quais você deseja gerar a nota. Em seguida, clique no botão "Iniciar transcrição" para iniciar a gravação',
	StartTranscription: 'Começar transcrição',
	StartVideoCall: 'Iniciar videochamada',
	StartWeekOn: 'Começar a semana em',
	StartedBy: 'Começado por ',
	Starter: 'Iniciante',
	State: 'Estado',
	StateIndustrialAccidentProviderNumber: 'Número do provedor estadual de acidentes industriais',
	StateLicenseNumber: 'Número da licença estadual',
	Statement: 'Declaração',
	StatementDescriptor: 'Descritor de declaração',
	StatementDescriptorToolTip:
		'O descritor do extrato é mostrado nos extratos bancários ou de cartão de crédito de seus clientes. Deve ter entre 5 e 22 caracteres e refletir o nome da sua empresa.',
	StatementNumber: 'Declaração #',
	Status: 'Status',
	StatusFieldPlaceholder: 'Insira um rótulo de status',
	StepFather: 'Padrasto',
	StepMother: 'Madrasta',
	Stockholm: 'Estocolmo',
	StopIgnoreSendersDescription:
		'Os remetentes que você parar de ignorar serão exibidos novamente na sua caixa de entrada.',
	StopIgnoring: 'Parar de ignorar',
	StopIgnoringSenders: 'Parar de ignorar remetentes',
	StopIgnoringSendersSuccess: 'Parou de ignorar endereço de e-mail <mark>{addresses}</mark>',
	StopSharing: 'Pare de compartilhar',
	StopSharingLabel: 'carepatron.com está compartilhando sua tela.',
	Storage: 'Armazenar',
	StorageAlmostFullDescription: '🚀 Atualize agora para manter sua conta funcionando sem problemas.',
	StorageAlmostFullTitle: 'Você usou {percentage}% do limite de armazenamento do seu workspace!',
	StorageFullDescription: 'Obtenha mais armazenamento atualizando seu plano.',
	StorageFullTitle: '	Seu armazenamento está cheio.',
	Street: 'Rua',
	StripeAccountNotCompleteErrorCode:
		'Pagamentos online não estão {hasProviderName, select, true {configurados para {providerName}} other {habilitados para este provedor}}.',
	StripeAccountRejectedError: 'A conta Stripe foi rejeitada. Entre em contato com o suporte.',
	StripeBalance: 'Equilíbrio de listras',
	StripeChargesInfoToolTip: 'Permite cobrar débito ',
	StripeFeesDescription:
		'Carepatron usa Stripe para receber pagamentos rapidamente e manter suas informações de pagamento seguras. Os métodos de pagamento disponíveis variam de acordo com a região, todos os principais débitos ',
	StripeFeesDescriptionItem1: 'Taxas de processamento são aplicadas a cada transação bem-sucedida, você pode {link}.',
	StripeFeesDescriptionItem2: 'Os pagamentos ocorrem diariamente, mas são retidos por até 4 dias.',
	StripeFeesLinkToRatesText: 'veja nossas tarifas aqui',
	StripeLink: 'Stripe Link',
	StripeMinimumPaymentErrorCodeSnackbar:
		'Desculpe, há um valor mínimo de {minimumAmount} necessário para faturas que usam pagamentos online',
	StripePaymentsDisabled: 'Pagamentos online desabilitados. Verifique suas configurações de pagamento.',
	StripePaymentsUnavailable: 'Pagamentos indisponíveis',
	StripePaymentsUnavailableDescription:
		'Ocorreu um erro ao carregar os pagamentos. Por favor, tente novamente mais tarde.',
	StripePayoutsInfoToolTip: 'Permite que você receba pagamentos em sua conta bancária',
	StyleYourWorkspace: '<mark>Estilize</mark> seu espaço de trabalho',
	StyleYourWorkspaceDescription1:
		'Nós recuperamos os ativos da marca do seu site. Sinta-se à vontade para editá-los ou continue para o seu espaço de trabalho.',
	StyleYourWorkspaceDescription2:
		'Use seus ativos de marca para personalizar faturas e reservas online para uma experiência do cliente perfeita',
	SubAdvanced: 'Avançado',
	SubEssential: 'Essencial',
	SubOrganization: 'Organização',
	SubPlus: 'Mais',
	SubProfessional: 'Profissional',
	Subject: 'Assunto',
	Submit: 'Enviar',
	SubmitElectronically: 'Enviar eletronicamente',
	SubmitFeedback: 'Enviar feedback',
	SubmitFormValidationError:
		'Certifique-se de que todos os campos obrigatórios estejam preenchidos corretamente e tente enviar novamente.',
	Submitted: 'Enviado',
	SubmittedDate: 'Data de envio',
	SubscribePerMonth: 'Assine {price} {isMonthly, select, true {por mês} other {por ano}}',
	SubscriptionDiscountDescription:
		'{percentOff}% de desconto {months, select, null { } other { {months, plural, one {por # mês} other {por # meses}}}}',
	SubscriptionFreeTrialDescription: 'Grátis até {endDate}',
	SubscriptionPaymentFailedNotificationSubject:
		'Não conseguimos concluir o pagamento da sua assinatura. Verifique os detalhes do seu pagamento',
	SubscriptionPlanDetailsHeader: 'Por usuário/mensal cobrado anualmente',
	SubscriptionPlanDetailsSubheader: '{pricePerMonth} cobrado mensalmente (USD)',
	SubscriptionPlans: 'Planos de assinatura',
	SubscriptionPlansDescription:
		'Atualize seu plano para desbloquear benefícios adicionais e manter sua prática funcionando sem problemas.',
	SubscriptionPlansDescriptionNoPermission:
		'Parece que você não tem acesso para atualizar agora — entre em contato com seu administrador para obter ajuda.',
	SubscriptionSettings: 'Configurações de assinatura',
	SubscriptionSettingsPercentageUsed: '<strong>{percentage}%</strong> de armazenamento usado',
	SubscriptionSettingsStorageUsed: '{usados} de {limite} usados',
	SubscriptionSettingsUnlimitedStorage: 'Armazenamento ilimitado disponível',
	SubscriptionSummary: 'Resumo da assinatura',
	SubscriptionUnavailableOverStorageLimit: 'Seu uso atual excede o limite de armazenamento deste plano.',
	SubscriptionUnpaidBannerButton: 'Ir para assinaturas',
	SubscriptionUnpaidBannerDescription: 'Verifique se seus dados de pagamento estão corretos e tente novamente',
	SubscriptionUnpaidBannerTitle: 'Não foi possível concluir o pagamento da sua assinatura.',
	Subscriptions: 'Assinaturas',
	SubscriptionsAndPayments: 'Assinaturas ',
	Subtotal: 'Subtotal',
	SuburbOrProvince: 'Subúrbio/Província',
	SuburbOrState: 'Subúrbio/Estado',
	SuccessSavedNoteChanges: 'Alterações de notas salvas com sucesso',
	SuccessShareDocument: 'Documento compartilhado com sucesso',
	SuccessShareNote: 'Nota compartilhada com sucesso',
	SuccessfullyCreatedValue: 'Criado com sucesso {value}',
	SuccessfullyDeletedTranscriptionPart: 'Parte da transcrição excluída com sucesso',
	SuccessfullyDeletedValue: 'Deletado com sucesso {value}',
	SuccessfullySubmitted: 'Submetido com sucesso ',
	SuccessfullyUpdatedClientSettings: 'Configurações do cliente atualizadas com sucesso',
	SuccessfullyUpdatedTranscriptionPart: 'Parte da transcrição atualizada com sucesso',
	SuccessfullyUpdatedValue: 'Atualizado com sucesso {value}',
	SuggestedAIPoweredTemplates: 'Sugestões de Modelos Baseados em IA',
	SuggestedAITemplates: 'Modelos de IA sugeridos',
	SuggestedActions: 'Ações sugeridas',
	SuggestedLocations: 'Locais sugeridos',
	Suggestions: 'Sugestões',
	Summarise: 'Resumo de IA',
	SummarisingContent: 'Resumindo {title}',
	Sunday: 'Domingo',
	Superbill: 'Superbill',
	SuperbillAndInsuranceBilling: 'Superfatura ',
	SuperbillAutomationMonthly: 'Ativo • Último dia do mês',
	SuperbillAutomationNoEmail:
		'Para enviar documentos de faturamento automatizados com êxito, adicione um endereço de e-mail para este cliente',
	SuperbillAutomationNotActive: 'Não ativo',
	SuperbillAutomationUpdateFailure: 'Falha ao atualizar as configurações de automação do Superbill',
	SuperbillAutomationUpdateSuccess: 'Configurações de automação do Superbill atualizadas com sucesso',
	SuperbillClientHelperText: 'Essas informações são pré-preenchidas a partir dos detalhes do cliente',
	SuperbillNotFoundDescription: 'Entre em contato com seu provedor e peça mais informações ou reenvie o superbill.',
	SuperbillNotFoundTitle: 'Superbill não encontrado',
	SuperbillNumber: 'Superconta #{number}',
	SuperbillNumberAlreadyExists: 'O número do recibo do Superbill já existe',
	SuperbillPracticeHelperText:
		'Essas informações são pré-preenchidas nas configurações de faturamento do consultório',
	SuperbillProviderHelperText: 'Esta informação é pré-preenchida a partir dos detalhes da equipe',
	SuperbillReceipts: 'Recibos de Superfatura',
	SuperbillsEmptyStateDescription: 'Nenhum superbill foi encontrado.',
	Surgeon: 'Cirurgião',
	Surgeons: 'Cirurgiões',
	SurgicalTechnologist: 'Tecnólogo Cirúrgico',
	SwitchFromAnotherPlatform: 'Estou migrando de outra plataforma',
	SwitchToMyPortal: 'Mudar para Meu portal',
	SwitchToMyPortalTooltip: `Acesse seu próprio portal pessoal,
 permitindo que você explore seu
 experiência do portal do cliente.`,
	SwitchWorkspace: 'Alternar espaço de trabalho',
	SwitchingToADifferentPlatform: 'Mudando para uma plataforma diferente',
	Sydney: 'Sidney',
	SyncCalendar: 'Sincronizar calendário',
	SyncCalendarModalDescription:
		'Outros membros da equipe não poderão ver seus calendários sincronizados. Os compromissos do cliente só podem ser atualizados ou excluídos no Carepatron.',
	SyncCalendarModalDisplayCalendar: 'Exibir meu calendário no Carepatron',
	SyncCalendarModalSyncToCarepatron: 'Sincronizar meu calendário com o Carepatron',
	SyncCalendarModalSyncWithCalendar: 'Sincronize compromissos do Carepatron com meu calendário',
	SyncCarepatronAppointmentsWithMyCalendar: 'Sincronizar agendamentos Carepatron com meu calendário',
	SyncGoogleCalendar: 'Sincronizar o calendário do Google',
	SyncInbox: 'Sincronizar caixa de entrada com Carepatron',
	SyncMyCalendarToCarepatron: 'Sincronizar meu calendário com Carepatron',
	SyncOutlookCalendar: 'Sincronizar calendário do Outlook',
	SyncedFromExternalCalendar: 'Sincronizado do calendário externo',
	SyncingCalendarName: 'Sincronizando o calendário {calendarName}',
	SyncingFailed: 'Falha na sincronização',
	SystemGenerated: 'Gerado pelo sistema',
	TFN: 'TFN',
	TRICARE: 'TRICARE',
	TRN: 'TRN',
	Table: 'Mesa',
	TableRowLabel: 'Linha de tabela para {value}',
	TagSelectorNoOptionsText: 'Clique em &quot;criar novo&quot; para adicionar uma nova tag',
	Tags: 'Tag',
	TagsInputPlaceholder: 'Pesquise ou crie tags',
	Task: 'Tarefa',
	TaskAttendeeStatusUpdatedSuccess: 'Status das consultas atualizado com sucesso',
	Tasks: 'Tarefas',
	Tax: 'Imposto',
	TaxAmount: 'Valor do Imposto',
	TaxID: 'CNPJ',
	TaxIdType: 'Tipo de identificação fiscal',
	TaxName: 'Nome fiscal',
	TaxNumber: 'Número de identificação fiscal',
	TaxNumberType: 'Tipo de número de imposto',
	TaxNumberTypeInvalid: '{type} é inválido',
	TaxPercentageOfAmount: '{taxName} ({percentage}% de {amount})',
	TaxRate: 'Taxa de imposto',
	TaxRatesDescription: 'Gerencie as taxas de imposto que serão aplicadas aos itens de linha da sua fatura.',
	Taxable: 'Tributável',
	TaxonomyCode: 'Código de taxonomia',
	TeacherAssistant: 'Assistente de professor',
	Team: 'Equipe',
	TeamMember: 'Membro da equipe',
	TeamMemberDoubleBookedTooltip:
		'<b>{name}</b> {count, plural, one {está} other {estão}} já reservado(s) neste horário.{br}Escolha um novo horário para evitar uma dupla reserva.',
	TeamMembers: 'Membros do time',
	TeamMembersColour: 'Cor dos membros da equipe',
	TeamMembersDetails: 'Detalhes dos membros da equipe',
	TeamSize: 'Quantas pessoas estão em sua equipe?',
	TeamTemplates: 'Modelos de equipe',
	TeamTemplatesSectionDescription: 'Criado por você e sua equipe',
	TelehealthAndVideoCalls: 'Telessaúde ',
	TelehealthProvidedOtherThanInPatientCare:
		'Telemedicina fornecida para outros fins que não o atendimento hospitalar',
	TelehealthVideoCall: 'Videochamada de telessaúde',
	Template: 'Modelo',
	TemplateDescription: 'Descrição do modelo',
	TemplateDetails: 'Detalhes do modelo',
	TemplateEditModeViewSwitcherDescription: 'Criar e editar modelo',
	TemplateGallery: 'Modelos de comunidade',
	TemplateImportCompletedNotificationSubject: 'Importação de modelo concluída! {templateTitle} está pronto para uso.',
	TemplateImportFailedNotificationSubject: 'Falha ao importar o arquivo {fileName}.',
	TemplateName: 'Nome do modelo',
	TemplateNotFound: 'Não foi possível encontrar o modelo.',
	TemplatePreviewErrorMessage: 'Ocorreu um erro ao carregar a visualização do modelo',
	TemplateResponderModeViewSwitcherDescription: 'Visualizar e interagir com formulários',
	TemplateResponderModeViewSwitcherTooltipTitle:
		'Verifique como seus formulários aparecem quando preenchidos pelos respondentes',
	TemplateSaved: 'Alterações salvas',
	TemplateTitle: 'Título do modelo',
	TemplateType: 'Tipo de modelo',
	Templates: 'Modelos',
	TemplatesCategoriesFilter: 'Filtrar por categoria',
	TemplatesPublicTemplatesFilter: ' Filtrar por comunidade/equipe',
	Text: 'Texto',
	TextAlign: 'Alinhamento de texto',
	TextColor: 'Cor do texto',
	ThankYouForYourFeedback: 'Obrigado pelo seu feedback!',
	ThanksForLettingKnow: 'Obrigado por nos informar.',
	ThePaymentMethod: 'O método de pagamento',
	ThemThey: 'Eles/Elas',
	Theme: 'Tema',
	ThemeAllColorsPickerTitle: 'Mais temas',
	ThemeColor: 'Tema',
	ThemeColorDarkMode: 'Escuro',
	ThemeColorLightMode: 'Luz',
	ThemeColorModePickerTitle: 'Modo de Cor',
	ThemeColorSystemMode: 'Sistema',
	ThemeCpColorPickerTitle: 'Temas Carepatron',
	ThemePanelDescription: 'Escolha entre o modo claro e escuro e personalize suas preferências de tema',
	ThemePanelTitle: 'Aparência',
	Then: 'Então',
	Therapist: 'Terapeuta',
	Therapists: 'Terapeutas',
	Therapy: 'Terapia',
	Thick: 'Espesso',
	Thin: 'Afinar',
	ThirdPerson: '3ª pessoa',
	ThisAndFollowingAppointments: 'Este e os compromissos seguintes',
	ThisAndFollowingMeetings: 'Esta e as reuniões seguintes',
	ThisAndFollowingReminders: 'Este e os seguintes lembretes',
	ThisAndFollowingTasks: 'Esta e as seguintes tarefas',
	ThisAppointment: 'Este compromisso',
	ThisMeeting: 'Esta reunião',
	ThisMonth: 'Este mês',
	ThisPerson: 'Esta pessoa',
	ThisReminder: 'Este lembrete',
	ThisTask: 'Esta tarefa',
	ThisWeek: 'Essa semana',
	ThreeDay: '3 dias',
	Thursday: 'Quinta-feira',
	Time: 'Tempo',
	TimeAgoDays: '{number}º',
	TimeAgoHours: '{number}h',
	TimeAgoMinutes: '{number}{number, plural, one {min} other {mins}}',
	TimeAgoSeconds: '{number}s',
	TimeFormat: 'Formato de hora',
	TimeIncrement: 'Incremento de tempo',
	TimeRangeFormula: '{hours}:{minutes}{isAM, select, true {am} other {pm}}',
	TimeslotSize: 'Tamanho do intervalo de tempo',
	Timestamp: 'Carimbo de data/hora',
	Timezone: 'Fuso horário',
	TimezoneDisplay: 'Exibição de fuso horário',
	TimezoneDisplayDescription: 'Gerencie suas configurações de exibição de fuso horário.',
	Title: 'Título',
	To: 'Para',
	ToYourWorkspace: 'para o seu espaço de trabalho',
	Today: 'Hoje',
	TodayInHoursPlural: 'Hoje em {count} {count, plural, one {hora} other {horas}}',
	TodayInMinsAbbreviated: 'Hoje em {count} {count, plural, one {min} other {mins}}',
	ToggleHeaderCell: 'Alternar célula de cabeçalho',
	ToggleHeaderCol: 'Alternar coluna de cabeçalho',
	ToggleHeaderRow: 'Alternar linha do cabeçalho',
	Tokyo: 'Tóquio',
	Tomorrow: 'Amanhã',
	TomorrowAfternoon: 'Amanhã à tarde',
	TomorrowMorning: 'Amanhã de manhã',
	TooExpensive: 'Muito caro',
	TooHardToSetUp: 'Muito difícil de configurar',
	TooManyFiles: 'Mais de 1 arquivo detectado.',
	ToolsExample: 'Prática simples, Microsoft, Calendly, Asana, Doxy.me ...',
	Total: 'Total',
	TotalAccountCredit: 'Crédito total da conta',
	TotalAdjustments: 'Ajustes totais',
	TotalAmountToCreditInCurrency: 'Total a ser creditado ({currency})',
	TotalBilled: 'Total faturado',
	TotalConversations: '{total} {total, plural, =0 {conversa} one {conversa} other {conversas}}',
	TotalOverdue: 'Total em atraso',
	TotalOverdueTooltip:
		'O saldo total em atraso inclui todas as faturas não pagas, independentemente do intervalo de datas, que não são anuladas nem processadas.',
	TotalPaid: 'Total pago',
	TotalPaidTooltip:
		'O saldo total pago inclui todos os valores das faturas que foram pagas dentro do intervalo de datas especificado.',
	TotalUnpaid: 'Total não pago',
	TotalUnpaidTooltip:
		'O saldo total não pago inclui todos os valores pendentes de faturas em processamento, não pagas e enviadas com vencimento dentro do intervalo de datas especificado.',
	TotalWorkflows: '{count} {count, plural, one {fluxo de trabalho} other {fluxos de trabalho}}',
	TotpSetUpManualEntryInstruction: 'Como alternativa, você pode inserir manualmente o código abaixo no aplicativo:',
	TotpSetUpModalDescription:
		'Digitalize o código QR com seu aplicativo autenticador para configurar a autenticação multifator.',
	TotpSetUpModalTitle: 'Configurar dispositivo MFA',
	TotpSetUpSuccess: 'Tudo pronto! O MFA foi habilitado.',
	TotpSetupEnterAuthenticatorCodeInstruction: 'Insira o código gerado pelo seu aplicativo autenticador',
	Transcribe: 'Transcrever',
	TranscribeLanguageSelector: 'Selecione o idioma de entrada',
	TranscribeLiveAudio: 'Transcrever áudio ao vivo',
	Transcribing: 'Transcrevendo áudio...',
	TranscribingIn: 'Transcrever em',
	Transcript: 'Transcrição',
	TranscriptRecordingCompleteInfo: 'Você verá sua transcrição aqui quando a gravação for concluída.',
	TranscriptSuccessSnackbar: 'Transcrição processada com sucesso.',
	Transcription: 'Transcrição',
	TranscriptionEmpty: 'Nenhuma transcrição disponível',
	TranscriptionEmptyHelperMessage: 'Esta transcrição não pegou nada. Reinicie e tente novamente.',
	TranscriptionFailedNotice: 'Esta transcrição não foi processada com sucesso',
	TranscriptionIdleMessage:
		'Não estamos ouvindo nenhum áudio. Se precisar de mais tempo, responda em {timeValue} segundos, ou a sessão será encerrada.',
	TranscriptionInProcess: 'Transcrição em andamento...',
	TranscriptionIncompleteNotice: 'Algumas partes desta transcrição não foram processadas com sucesso',
	TranscriptionOvertimeWarning: '{scribeType} sessão termina em <strong>{timeValue} {unit}</strong>',
	TranscriptionPartDeleteMessage: 'Tem certeza de que deseja excluir esta parte da transcrição?',
	TranscriptionText: 'Voz para texto',
	TranscriptsPending: 'Sua transcrição estará disponível aqui após o término da sessão.',
	Transfer: 'Transferir',
	TransferAndDelete: 'Transferir e excluir',
	TransferOwnership: 'Transferir propriedade',
	TransferOwnershipConfirmationModalDescription:
		'Esta ação só poderá ser desfeita se eles transferirem a propriedade de volta para você.',
	TransferOwnershipDescription: 'Transfira a propriedade deste espaço de trabalho para outro membro da equipe.',
	TransferOwnershipSuccessSnackbar: 'Propriedade transferida com sucesso!',
	TransferOwnershipToMember: 'Tem certeza de que deseja transferir este espaço de trabalho para {staff}?',
	TransferStatusAlert:
		'Remover {numberOfStatuses, plural, one {este status} other {esses status}} impactará {numberOfAffectedRecords, plural, one {<strong>{numberOfAffectedRecords} status de cliente.</strong>} other {<strong>{numberOfAffectedRecords} status de clientes.</strong>}}',
	TransferStatusDescription:
		'Escolha outro status para esses clientes antes de prosseguir com a exclusão. Esta ação não pode ser desfeita.',
	TransferStatusLabel: 'Transferir para novo status',
	TransferStatusPlaceholder: 'Escolha um status existente',
	TransferStatusTitle: 'Status da transferência antes da exclusão',
	TransferTaskAttendeeStatusAlert:
		'Remover este status impactará <strong>{number} consulta(s) futura(s) {number, plural, one {status} other {status}}.</strong>',
	TransferTaskAttendeeStatusDescription:
		'Escolha outro status para esses clientes antes de prosseguir com a exclusão. Esta ação não pode ser desfeita.',
	TransferTaskAttendeeStatusSubtitle: 'Status da consulta',
	TransferTaskAttendeeStatusTitle: 'Status da transferência antes da exclusão',
	Trash: 'Lixeira',
	TrashDeleteItemsModalConfirm: 'Para confirmar, digite {confirmationText}',
	TrashDeleteItemsModalDescription:
		'Os seguintes {count, plural, one {item} other {itens}} serão excluídos permanentemente e não poderão ser restaurados.',
	TrashDeleteItemsModalTitle: 'Excluir {count, plural, one {item} other {itens}} para sempre',
	TrashDeletedAllItems: 'Excluído todos os itens',
	TrashDeletedItems: 'Excluído {count, plural, one {item} other {itens}}',
	TrashDeletedItemsFailure: 'Falha ao excluir itens da lixeira',
	TrashLocationAppointmentType: 'Calendário',
	TrashLocationBillingAndPaymentsType: 'Faturamento e pagamentos',
	TrashLocationContactType: 'Clientes',
	TrashLocationNoteType: 'Notas e Documentos',
	TrashRestoreItemsModalDescription: 'Os seguintes {count, plural, one {item} other {items}} serão restaurados.',
	TrashRestoreItemsModalTitle: 'Restaurar {count, plural, one {item} other {itens}}',
	TrashRestoredAllItems: 'Restaurado todos os itens',
	TrashRestoredItems: 'Restaurado {count, plural, one {item} other {itens}}',
	TrashRestoredItemsFailure: 'Falha ao restaurar itens da lixeira',
	TrashSuccessfullyDeletedItem: 'Excluído com sucesso {type}',
	Trigger: 'Acionar',
	Troubleshoot: 'Solucionar problemas',
	TryAgain: 'Tente novamente',
	Tuesday: 'Terça-feira',
	TwoToTen: '2 - 10',
	Type: 'Tipo',
	TypeHere: 'Digite aqui...',
	TypeToConfirm: 'Para confirmar, digite {keyword}',
	TypographyH1: 'H1',
	TypographyH2: 'H2',
	TypographyH3: 'H3',
	TypographyH4: 'H4',
	TypographyH5: 'H5',
	TypographyHeading1: 'Cabeçallho 1',
	TypographyHeading2: 'Título 2',
	TypographyHeading3: 'Título 3',
	TypographyHeading4: 'Título 4',
	TypographyHeading5: 'Título 5',
	TypographyP: 'P',
	TypographyParagraph: 'Parágrafo',
	UnableToCompleteAction: 'Não foi possível concluir a ação.',
	UnableToPrintDocument: 'Não é possível imprimir o documento. Por favor, tente novamente mais tarde.',
	Unallocated: 'Não alocado',
	UnallocatedPaymentDescription: `Este pagamento não foi totalmente alocado aos itens faturáveis.
 Adicione uma alocação para itens não pagos ou emita um crédito ou reembolso.`,
	UnallocatedPaymentTitle: 'Pagamento não alocado',
	UnallocatedPayments: 'Pagamentos não alocados',
	Unarchive: 'Desarquivar',
	Unassigned: 'Não atribuído',
	UnauthorisedInvoiceSnackbar: 'Você não tem acesso para gerenciar faturas deste cliente.',
	UnauthorisedSnackbar: 'Você não tem permissão para fazer isso.',
	Unavailable: 'Indisponível',
	Uncategorized: 'Sem categoria',
	Unclaimed: 'Não reclamado',
	UnclaimedAmount: 'Valor não resgatado',
	UnclaimedItems: 'Itens não reclamados',
	UnclaimedItemsMustBeInCurrency: 'Somente itens nas seguintes moedas são suportados: {currencies}',
	Uncle: 'Tio',
	Unconfirmed: 'Não confirmado',
	Underline: 'Sublinhado',
	Undo: 'Desfazer',
	Unfavorite: 'Remover dos Favoritos',
	Uninvoiced: 'Não faturado',
	UninvoicedAmount: 'Valor não faturado',
	UninvoicedAmounts:
		'{count, plural, =0 {Nenhum valor não faturado} one {Valor não faturado} other {Valores não faturados}}',
	Unit: 'Unidade',
	UnitedKingdom: 'Reino Unido',
	UnitedStates: 'Estados Unidos',
	UnitedStatesEast: 'Estados Unidos - Leste',
	UnitedStatesWest: 'Estados Unidos - Oeste',
	Units: 'Unidades',
	UnitsIsRequired: 'Unidades são obrigatórias',
	UnitsMustBeGreaterThanZero: 'As unidades devem ser maiores que 0',
	UnitsPlaceholder: '1',
	Unknown: 'Desconhecido',
	Unlimited: 'Ilimitado',
	Unlock: 'Desbloquear',
	UnlockNoteHelper: 'Antes de fazer qualquer nova alteração, os editores são obrigados a desbloquear a nota.',
	UnmuteAudio: 'Ativar áudio',
	UnmuteEveryone: 'Ativar o som de todos',
	Unpaid: 'Não pago',
	UnpaidInvoices: 'Faturas não pagas',
	UnpaidItems: 'Itens não pagos',
	UnpaidMultiple: 'Não pago',
	Unpublish: 'Despublicar',
	UnpublishTemplateConfirmationModalPrompt:
		'Remover <span>{title}</span> removerá este recurso da comunidade Carepatron. Esta ação não pode ser desfeita.',
	UnpublishToCommunitySuccessMessage: 'Removido com sucesso ‛{title}’ da comunidade',
	Unread: 'Não lido',
	Unrecognised: 'Não reconhecido',
	UnrecognisedDescription:
		'Este método de pagamento não é reconhecido pela versão atual do seu aplicativo. Atualize seu navegador para obter a versão mais recente para visualizar e editar esta forma de pagamento.',
	UnsavedChanges: 'Alterações não salvas',
	UnsavedChangesPromptContent: 'Quer salvar suas alterações antes de fechar?',
	UnsavedChangesPromptTitle: 'Você tem alterações não salvas',
	UnsavedNoteChangesWarning: 'As alterações feitas podem não ser salvas',
	UnsavedTemplateChangesWarning: 'As alterações feitas podem não ser salvas',
	UnselectAll: 'Desmarcar tudo',
	Until: 'Até',
	UntitledConversation: 'Conversa sem título',
	UntitledFolder: 'Pasta sem título',
	UntitledNote: 'Nota sem título',
	UntitledSchedule: 'Programação sem título',
	UntitledSection: 'Seção sem título',
	UntitledTemplate: 'Modelo sem título',
	Unverified: 'Não verificado',
	Upcoming: 'Por vir',
	UpcomingAppointments: 'Próximos compromissos',
	UpcomingDateOverridesEmpty: 'Nenhuma substituição de data foi encontrada',
	UpdateAvailabilityScheduleFailure: 'Falha ao atualizar a programação de disponibilidade',
	UpdateAvailabilityScheduleSuccess: 'Cronograma de disponibilidade atualizado com sucesso',
	UpdateInvoicesOrClaimsAgainstBillable:
		'Você deseja que o novo preço seja aplicado às faturas e reivindicações dos participantes?',
	UpdateLink: 'Atualizar link',
	UpdatePrimaryEmailWarningDescription:
		'Alterar o endereço de e-mail do seu cliente resultará na perda do acesso dele às suas consultas e notas existentes.',
	UpdatePrimaryEmailWarningTitle: 'Alteração de e-mail do cliente',
	UpdateSettings: 'Atualizar configurações',
	UpdateStatus: 'Atualizar o status',
	UpdateSuperbillReceiptFailure: 'Falha ao atualizar o recibo do Superbill',
	UpdateSuperbillReceiptSuccess: 'Recibo do Superbill atualizado com sucesso',
	UpdateTaskBillingDetails: 'Atualizar detalhes de cobrança',
	UpdateTaskBillingDetailsDescription:
		'O preço do compromisso mudou. Você quer que o novo preço seja aplicado aos itens de cobrança, faturas e reivindicações do participante? Escolha as atualizações com as quais deseja prosseguir.',
	UpdateTemplateFolderSuccessMessage: 'Pasta atualizada com sucesso',
	UpdateUnpaidInvoices: 'Atualizar faturas não pagas',
	UpdateUserInfoSuccessSnackbar: 'Informações do usuário atualizadas com sucesso!',
	UpdateUserSettingsSuccessSnackbar: 'Configurações do usuário atualizadas com sucesso!',
	Upgrade: 'Atualização',
	UpgradeForSMSReminder: 'Faça upgrade para o <b>Professional</b> para receber lembretes SMS ilimitados',
	UpgradeNow: 'Atualize agora',
	UpgradePlan: 'Plano de atualização',
	UpgradeSubscriptionAlertDescription:
		'Você está ficando sem armazenamento. Faça upgrade do seu plano para desbloquear armazenamento adicional e manter sua prática funcionando perfeitamente!',
	UpgradeSubscriptionAlertDescriptionNoPermission:
		'Você está com pouco espaço de armazenamento. Peça a alguém na sua prática com <span>acesso de administrador</span> para atualizar o seu plano para desbloquear armazenamento adicional e manter sua prática funcionando sem problemas!',
	UpgradeSubscriptionAlertTitle: 'É hora de atualizar sua assinatura',
	UpgradeYourPlan: 'Atualize seu plano',
	UploadAudio: 'Carregar áudio',
	UploadFile: 'Subir arquivo',
	UploadFileDescription: 'De qual plataforma de software você está migrando?',
	UploadFileMaxSizeError: 'Arquivo muito grande. O tamanho máximo do arquivo é {fileSizeLimit}.',
	UploadFileSizeLimit: 'Limite de tamanho {size}MB',
	UploadFileTileDescription: 'Use arquivos CSV, XLS, XLSX ou ZIP para carregar seus clientes.',
	UploadFileTileLabel: 'Carregar um arquivo',
	UploadFiles: 'Fazer upload de arquivos',
	UploadIndividually: 'Carregar arquivos individualmente',
	UploadLogo: 'Carregar logotipo',
	UploadPhoto: 'Carregar foto',
	UploadToCarepatron: 'Carregar para Carepatron',
	UploadYourLogo: 'Carregue seu logotipo',
	UploadYourTemplates: 'Carregue seus modelos e nós os converteremos para você',
	Uploading: 'Enviando',
	UploadingAudio: 'Enviando seu áudio...',
	UploadingFiles: 'Fazendo upload de arquivos',
	UrlLink: 'Links URL',
	UsageCount: 'Usado {count} vezes',
	UsageLimitValue: '{usados} de {limite} usados',
	UsageValue: '{usada} usada',
	Use: 'Usar',
	UseAiToAutomateYourWorkflow: 'Use IA para automatizar seu fluxo de trabalho!',
	UseAsDefault: 'Usar como padrão',
	UseCustom: 'Usar personalizado',
	UseDefault: 'Use o padrão',
	UseDefaultFilters: 'Use filtros padrão',
	UseTemplate: 'Usar modelo',
	UseThisCard: 'Use este cartão',
	UseValue: 'Use "{value}"',
	UseWorkspaceDefault: 'Usar padrão do espaço de trabalho',
	UserIsTyping: '{name} está digitando...',
	Username: 'Nome de usuário',
	Users: 'Usuários',
	VAT: 'CUBA',
	ValidUrl: 'O link do URL deve ser um URL válido.',
	Validate: 'Validar',
	Validated: 'Validado',
	Validating: 'Validando',
	ValidatingContent: 'Validando conteúdo...',
	ValidatingTranscripts: 'Validando transcrições...',
	ValidationConfirmPasswordRequired: 'Confirmar senha é obrigatório',
	ValidationDateMax: 'Deve ser antes de {max}',
	ValidationDateMin: 'Deve ser depois de {min}',
	ValidationDateRange: 'As datas de início e término são obrigatórias',
	ValidationEndDateMustBeAfterStartDate: 'Data de término deve ser posterior à data de início',
	ValidationMixedDefault: 'Isso é inválido',
	ValidationMixedRequired: 'Isso é necessário',
	ValidationNumberInteger: 'Deve ser um número inteiro',
	ValidationNumberMax: 'Deve ter {max} ou menos',
	ValidationNumberMin: 'Deve ser {min} ou mais',
	ValidationPasswordNotMatching: 'As senhas não coincidem',
	ValidationPrimaryAddressIsRequired: 'O endereço é necessário quando definido como padrão',
	ValidationPrimaryPhoneNumberIsRequired: 'O número de telefone é necessário quando definido como padrão',
	ValidationServiceMustBeNotBeFuture: 'Serviço não pode ser do dia atual ou no futuro',
	ValidationStringEmail: 'Deve ser um e-mail válido',
	ValidationStringMax: 'Deve ter {max} caracteres ou menos',
	ValidationStringMin: 'Deve ter {min} ou mais caracteres',
	ValidationStringPhoneNumber: 'Deve ser um número de telefone válido',
	ValueMinutes: '{value} minutos',
	VerbosityConcise: 'Conciso',
	VerbosityDetailed: 'Detalhado',
	VerbosityStandard: 'Padrão',
	VerbositySuperDetailed: 'Super detalhado',
	VerificationCode: 'Código de verificação',
	VerificationEmailDescription:
		'Por favor, insira seu endereço de email e o código de verificação que acabamos de enviar.',
	VerificationEmailSubtitle: 'Verifique a pasta de spam - se o email não chegou',
	VerificationEmailTitle: 'Verificar email',
	VerificationOption: 'Verificação de e-mail',
	Verified: 'Verificado',
	Verify: 'Verificar',
	VerifyAndSubmit: 'Verificar e enviar',
	VerifyEmail: 'Verificar email',
	VerifyEmailAccessCode: 'Código de confirmação',
	VerifyEmailAddress: 'Verifique o endereço de e-mail',
	VerifyEmailButton: 'Verifique e saia',
	VerifyEmailSentSnackbar: 'E-mail de verificação enviado. Verifique sua caixa de entrada.',
	VerifyEmailSubTitle: 'Verifique a pasta Spam se o e-mail não chegou',
	VerifyEmailSuccessLogOutSnackbar: 'Sucesso! Faça logout para aplicar as alterações.',
	VerifyEmailSuccessSnackbar: 'Sucesso! Email verificado. Faça login para continuar como uma conta verificada.',
	VerifyEmailTitle: 'Verifique seu e-mail',
	VerifyNow: 'Verificar agora',
	Veterinarian: 'Veterinário',
	VideoCall: 'Video chamada',
	VideoCallAudioInputFailed: 'Dispositivo de entrada de áudio não está funcionando',
	VideoCallAudioInputFailedMessage:
		'Abra as configurações e verifique se você definiu corretamente a fonte do microfone.',
	VideoCallChatBanner:
		'As mensagens podem ser vistas por todos nesta chamada e serão excluídas quando a chamada terminar.',
	VideoCallChatSendBtn: 'Envie uma mensagem',
	VideoCallChatTitle: 'Bater papo',
	VideoCallDisconnectedMessage: 'A chamada foi desconectada. Por favor, tente novamente.',
	VideoCallOptionInfo: 'Carepatron gerenciará videochamadas para seus compromissos se o Zoom não estiver conectado',
	VideoCallTilePaused: 'Este vídeo está pausado devido a problemas com sua rede',
	VideoCallTranscriptionFormDescription: 'Você pode ajustar essas configurações a qualquer momento',
	VideoCallTranscriptionFormHeading: 'Personalize seu AI Scribe',
	VideoCallTranscriptionFormLanguageField: 'A linguagem de saída gerada',
	VideoCallTranscriptionFormNoteTemplateField: 'Definir modelo de nota padrão',
	VideoCallTranscriptionFormNoteTemplateFieldEmptyText: 'Nenhum modelo com IA encontrado',
	VideoCallTranscriptionFormNoteTemplateFieldPlaceholder: 'Escolha um modelo',
	VideoCallTranscriptionPronounField: 'Seu pronome',
	VideoCallTranscriptionRecordingNote:
		'No final da sessão, você receberá uma nota <strong>{noteTemplate} gerada</strong> e uma transcrição.',
	VideoCallTranscriptionReferClientField: 'Referir-se ao cliente como',
	VideoCallTranscriptionReferPractitionerField: 'Consulte o profissional como',
	VideoCallTranscriptionTitle: 'Escriba de AI',
	VideoCallTranscriptionVerbosityField: 'Verbosidade',
	VideoCallTranscriptionWritingPerspectiveField: 'Perspectiva de escrita',
	VideoCalls: 'Chamada de Vídeo',
	VideoConferencing: 'Videoconferência',
	VideoOff: 'O vídeo está desativado',
	VideoOn: 'O vídeo está desativado',
	VideoQual360: 'Baixa qualidade (360p)',
	VideoQual540: 'Qualidade média (540p)',
	VideoQual720: 'Alta qualidade (720p)',
	View: 'Visualizar',
	ViewAll: 'Ver tudo',
	ViewAppointment: 'Ver consulta',
	ViewBy: 'Visto por',
	ViewClaim: 'Visualizar solicitação',
	ViewCollection: 'Ver coleção',
	ViewDetails: 'Ver detalhes',
	ViewEnrollment: 'Ver matrícula',
	ViewPayment: 'Ver pagamento',
	ViewRecord: 'Ver registro',
	ViewRemittanceAdvice: 'Ver aviso de remessa',
	ViewRemittanceAdviceHeader: 'Recibo de remessa',
	ViewRemittanceAdviceSubheader: 'Reivindicação {claimNumber} • EFT# {remittanceReference}',
	ViewSettings: 'Ver configurações',
	ViewStripeDashboard: 'Ver painel do Stripe',
	ViewTemplate: 'Ver modelo',
	ViewTemplates: 'Ver modelos',
	ViewableBy: 'Visível por',
	ViewableByHelper:
		'Você e a equipe sempre terão acesso às notas que publicarem. Você pode optar por compartilhar esta nota com o cliente e/ou seus relacionamentos',
	Viewer: 'Visualizador',
	VirtualLocation: 'Localização virtual',
	VisibleTo: 'Visível para',
	VisitOurHelpCentre: 'Visite nosso centro de ajuda',
	VisualEffects: 'Efeitos visuais',
	VoiceFocus: 'Foco de voz',
	VoiceFocusLabel: 'Filtra o som do seu microfone que não seja fala',
	Void: 'Vazio',
	VoidCancelPriorClaim: 'Anular/Cancelar reivindicação anterior',
	WaitingforMins: 'Aguardando por {count} minutos',
	Warning: 'Aviso',
	WatchAVideo: 'assistir a um vídeo',
	WatchDemoVideo: 'Assistir ao vídeo de demonstração',
	WebConference: 'Webconferência',
	WebConferenceOrVirtualLocation: 'Webconferência / localização virtual',
	WebDeveloper: 'Desenvolvedor Web',
	WebsiteOptional: 'Site<span>(Opcional)</span>',
	WebsiteUrl: 'URL do site',
	Wednesday: 'Quarta-feira',
	Week: 'Semana',
	WeekPlural: '{count, plural, one {semana} other {semanas}}',
	Weekly: 'Semanalmente',
	WeeksPlural: '{age, plural, one {# semana} other {# semanas}}',
	WelcomeBack: 'bem vindo de volta',
	WelcomeBackName: 'Bem-vindo de volta, {name}',
	WelcomeName: 'Bem-vindo {name}',
	WelcomeToCarepatron: 'Bem-vindo ao Carepatron',
	WhatCanIHelpWith: 'Em que posso ajudar?',
	WhatDidYouLikeResponse: 'O que você gostou sobre esta resposta?',
	WhatIsCarepatron: 'O que é Cuidador?',
	WhatMadeYouCancel: `O que fez você cancelar seu plano?
 Marque todas as opções aplicáveis.`,
	WhatServicesDoYouOffer: 'Quais <mark>serviços</mark> você oferece?',
	WhatServicesDoYouOfferDescription: 'Você pode editar ou adicionar mais serviços depois.',
	WhatsYourAvailability: 'Qual é a sua <mark>disponibilidade?</mark>',
	WhatsYourAvailabilityDescription: 'Você pode adicionar mais horários depois.',
	WhatsYourBusinessName: 'Qual é o <mark>nome da sua empresa?</mark>',
	WhatsYourTeamSize: 'Qual é o <mark>tamanho da sua equipe?</mark>',
	WhatsYourTeamSizeDescription: 'Isso nos ajudará a configurar seu espaço de trabalho corretamente.',
	WhenThisHappens: 'Quando isso acontece:',
	WhichBestDescribesYou: 'Qual melhor <mark>descreve você?</mark>',
	WhichPlatforms: 'Quais plataformas?',
	Wife: 'Esposa',
	WorkflowDescription: 'Descrição do fluxo de trabalho',
	WorkflowTemplateAutomatedWorkflowsPanelDescription:
		'Modelos podem vincular a fluxos de trabalho para processos mais suaves. Visualize os fluxos de trabalho vinculados para rastreá-los e atualizá-los facilmente.',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyDescription: 'Conecte seus SMS + e-mails com base em gatilhos comuns',
	WorkflowTemplateAutomatedWorkflowsPanelEmptyTitle: 'Automações de fluxo de trabalho',
	WorkflowTemplateAutomatedWorkflowsPanelTitle: 'Fluxos de trabalho automatizados',
	WorkflowTemplateConfigKey_Body: 'Corpo',
	WorkflowTemplateConfigKey_Branding_IsVisible: 'Mostrar marca',
	WorkflowTemplateConfigKey_Content: 'Conteúdo',
	WorkflowTemplateConfigKey_Footer: 'Rodapé',
	WorkflowTemplateConfigKey_Footer_IsVisible: 'Mostrar rodapé',
	WorkflowTemplateConfigKey_Header: 'Cabeçalho',
	WorkflowTemplateConfigKey_Header_IsVisible: 'Mostrar cabeçalho',
	WorkflowTemplateConfigKey_SecurityFooter: 'Rodapé de segurança',
	WorkflowTemplateConfigKey_SecurityFooter_IsVisible: 'Mostrar rodapé de segurança',
	WorkflowTemplateConfigKey_Subject: 'Assunto',
	WorkflowTemplateConfigKey_Title: 'Título',
	WorkflowTemplateDeleteConfirmationMessage:
		'Tem certeza de que deseja excluir este modelo? Esta ação não pode ser desfeita.',
	WorkflowTemplateDeleteConfirmationTitle: 'Excluir modelo de notificação',
	WorkflowTemplateDeleteLocalisationDialogDescription:
		'Tem certeza? Isso removerá apenas a versão {locale} — outros idiomas não serão afetados. Essa ação não pode ser desfeita.',
	WorkflowTemplateDeleteLocalisationDialogTitle: 'Excluir modelo ‘{locale}’',
	WorkflowTemplateDeletedSuccess: 'Modelo de notificação excluído com sucesso',
	WorkflowTemplateEditorDetailsTab: 'Detalhes do modelo',
	WorkflowTemplateEditorEmailContent: 'Conteúdo do e-mail',
	WorkflowTemplateEditorEmailContentTab: 'Conteúdo do email',
	WorkflowTemplateEditorThemeTab: 'Tema',
	WorkflowTemplatePreviewerAlert: 'As visualizações usam dados de amostra para mostrar o que seus clientes verão.',
	WorkflowTemplateResetEmailContentDialogDescription:
		'Tem certeza? Isso irá redefinir a versão para o modelo padrão do sistema. Esta ação não pode ser desfeita.',
	WorkflowTemplateResetEmailContentDialogTitle: 'Reiniciar modelo',
	WorkflowTemplateSendTestEmail: 'Enviar e-mail de teste',
	WorkflowTemplateSendTestEmailDialogDescription:
		'Teste sua configuração de email enviando um email de teste para você mesmo.',
	WorkflowTemplateSendTestEmailDialogRecipientEmail: 'Email do destinatário',
	WorkflowTemplateSendTestEmailDialogSendButton: 'Enviar teste',
	WorkflowTemplateSendTestEmailDialogTitle: 'Enviar um e-mail de teste',
	WorkflowTemplateSendTestEmailSuccess: 'Sucesso! Seu email de teste <mark>{templateName}</mark> foi enviado.',
	WorkflowTemplateTemplateDetailsPanelDescription:
		'Gerencie seus modelos e adicione versões multilíngues para se comunicar efetivamente com os clientes.',
	WorkflowTemplateTemplateEditor: 'Editor de modelo',
	WorkflowTemplateTranslateLocaleError: 'Algo deu errado durante a tradução do conteúdo.',
	WorkflowTemplateTranslateLocaleSuccess: 'Conteúdo traduzido com sucesso para **{locale}**',
	WorkflowsAndReminders: 'Fluxos de trabalho ',
	WorkflowsManagement: 'Gestão de fluxos de trabalho',
	WorksheetAndHandout: 'Folha de trabalho/Apontamento',
	WorksheetsAndHandoutsDescription: 'Para engajamento e educação do cliente',
	Workspace: 'Espaço de trabalho',
	WorkspaceBranding: 'Marca do espaço de trabalho',
	WorkspaceBrandingDescription: `Marque seu espaço de trabalho sem esforço com um estilo coeso que reflita sua
 profissionalismo e personalidade. Personalize faturas para reservas on-line para um lindo
 experiência do cliente.`,
	WorkspaceName: 'Nome do espaço de trabalho',
	Workspaces: 'Espaços de trabalho',
	WriteOff: 'Eliminar',
	WriteOffModalDescription:
		'Você tem <mark>{count} {count, plural, one {item de linha} other {itens de linha}}</mark> a serem baixados',
	WriteOffModalTitle: 'Ajuste de baixa',
	WriteOffReasonHelperText: 'Esta é uma nota interna e não ficará visível para seu cliente.',
	WriteOffReasonPlaceholder: 'Adicionar um motivo de baixa pode ajudar ao revisar transações faturáveis',
	WriteOffTotal: 'Baixa total ({currencyCode})',
	Writer: 'Escritor',
	Yearly: 'Anual',
	YearsPlural: '{age, plural, one {# ano} other {# anos}}',
	Yes: 'Sim',
	YesArchive: 'Sim, arquivar',
	YesDelete: 'Sim, excluir',
	YesDeleteOverride: 'Sim, excluir substituição',
	YesDeleteSection: 'Sim, excluir',
	YesDisconnect: 'Sim, desconectar',
	YesEnd: 'Sim, fim',
	YesEndTranscription: 'Sim, fim da transcrição',
	YesImFineWithThat: 'Sim, estou bem com isso',
	YesLeave: 'Sim, sair',
	YesMinimize: 'Sim, minimizar',
	YesOrNoAnswerTypeDescription: 'Configurar tipo de resposta',
	YesOrNoFormPrimaryText: 'Sim | Não',
	YesOrNoFormSecondaryText: 'Escolha opções sim ou não',
	YesProceed: 'Sim, prossiga',
	YesRemove: 'Sim, remova',
	YesRestore: 'Sim, restaurar',
	YesStopIgnoring: 'Sim, pare de ignorar',
	YesTransfer: 'Sim, transferir',
	Yesterday: 'Ontem',
	YogaInstructor: 'Instrutor de ioga',
	You: 'Você',
	YouArePresenting: 'Você está apresentando',
	YouCanChooseMultiple: 'Você pode escolher vários',
	YouCanSelectMultiple: 'Você pode selecionar vários',
	YouHaveOngoingTranscription: 'Você tem uma transcrição em andamento',
	YourAnswer: 'Sua Resposta',
	YourDisplayName: 'Seu nome de exibição',
	YourSpreadsheetColumns: 'Colunas da sua planilha',
	YourTeam: 'Seu time',
	ZipCode: 'CEP',
	Zoom: 'Ampliação',
	ZoomUserNotInAccountErrorCodeSnackbar:
		'Você não pode adicionar uma chamada Zoom para este membro da equipe. Consulte os <a>documentos de suporte para obter mais informações.</a>',
};

export default items;
