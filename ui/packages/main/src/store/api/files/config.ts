import { AxiosRequestConfig } from 'axios';

import { ApiRequestConfig } from 'store/api/types';

export enum FileLocationType {
	Files = 'Files',
	ClientImport = 'ClientImport',
	InternalBlobStorage = 'InternalBlobStorage',
	Transcription = 'Transcription',
}

export interface FilesApiConfigs {
	initializeFileUpload: ApiRequestConfig<{
		contactId?: string | null;
		dto: {
			fileLocationType: FileLocationType;
			contentType: string;
		};
	}>;
	uploadFileChunk: ApiRequestConfig<
		{
			contactId?: string | null;
			dto: Blob;
			query: {
				fileId: string;
				uploadId: string;
				partNumber: number;
				isLastPart: boolean;
				fileLocationType: FileLocationType;
			};
		} & Pick<AxiosRequestConfig, 'onUploadProgress'>
	>;
	completeFileUpload: ApiRequestConfig<{
		contactId?: string | null;
		dto: {
			fileId: string;
			uploadId: string;
			fileLocationType: FileLocationType;
		};
	}>;
	abortFileUpload: ApiRequestConfig<{
		contactId?: string | null;
		dto: {
			fileId: string;
			uploadId: string;
			fileLocationType: FileLocationType;
		};
	}>;
	/** @desc method: 'POST'; returns [...] */
	uploadDirectFiles: ApiRequestConfig<{
		dto: { files: File[]; token: string };
	}>;
	getFile: ApiRequestConfig<{
		fileId: string;
		query?: { fileLocationType?: FileLocationType };
		options?: { asProvider?: boolean; ignoreAuth?: boolean };
	}>;
	/** @desc method: 'GET'; returns string */
	getPresignedFileUrl: ApiRequestConfig<{
		fileId: string;
		query?: { download?: boolean; fileName?: string; 'X-CP-Token'?: string };
	}>;
	/** @desc method: 'POST'; returns IContactImportFileResult */
	uploadContactImportSummary: ApiRequestConfig<{
		dto: { providerId: string; file: File };
	}>;
}

export type FilesApiConfigParams<Config extends keyof FilesApiConfigs> = Parameters<FilesApiConfigs[Config]>[0];

export const filesApiConfig: FilesApiConfigs = {
	initializeFileUpload: ({ dto, contactId }) => {
		if (contactId) {
			return {
				method: 'POST',
				uri: `portal/contacts/${contactId}/files/initialize`,
				data: dto,
			};
		}

		return {
			method: 'POST',
			uri: 'files/upload/initialize',
			options: { asProvider: true },
			data: dto,
		};
	},
	uploadFileChunk: ({ dto, query, onUploadProgress, contactId }) => {
		const form = new FormData();
		form.append('file', dto);

		if (contactId) {
			return {
				method: 'PUT',
				uri: `portal/contacts/${contactId}/files/${query.fileId}/parts`,
				data: form,
				params: query,
				onUploadProgress,
			};
		}

		return {
			method: 'PUT',
			uri: 'files/upload/parts',
			data: form,
			params: query,
			options: { asProvider: true },
			onUploadProgress,
		};
	},
	completeFileUpload: ({ dto, contactId }) => {
		if (contactId) {
			return {
				method: 'POST',
				uri: `portal/contacts/${contactId}/files/${dto.fileId}/complete`,
				data: dto,
			};
		}

		return {
			method: 'POST',
			uri: 'files/upload/complete',
			data: dto,
			options: { asProvider: true },
		};
	},
	abortFileUpload: ({ dto, contactId }) => {
		if (contactId) {
			return {
				method: 'POST',
				uri: `portal/contacts/${contactId}/files/${dto.fileId}/abort`,
				data: dto,
			};
		}

		return {
			method: 'POST',
			uri: 'files/upload/abort',
			data: dto,
			options: { asProvider: true },
		};
	},
	uploadDirectFiles: ({ dto }) => {
		const formData = new FormData();
		formData.append('token', dto.token);
		dto.files.forEach((file) => formData.append('files', file, file.name));
		return {
			method: 'POST',
			uri: 'files/direct',
			data: formData,
			options: { ignoreAuth: true },
		};
	},
	getFile: ({ fileId, query, options }) => ({
		method: 'GET',
		uri: `files/${fileId}/url`,
		params: query,
		options: {
			asProvider: options?.asProvider,
			ignoreAuth: options?.ignoreAuth,
			qs: { skipNulls: true, arrayFormat: 'repeat' },
		},
	}),
	getPresignedFileUrl: ({ fileId, query }) => ({
		method: 'GET',
		uri: `files/${fileId}/url`,
		params: query,
		options: {
			ignoreAuth: Boolean(query?.['X-CP-Token']),
			qs: { skipNulls: true, arrayFormat: 'repeat' },
		},
	}),
	uploadContactImportSummary: ({ dto, options }) => {
		const { skipAnalyticsOnError, ...restOptions } = options ?? {};
		const formData = new FormData();
		formData.append('file', dto.file, dto.file.name);
		formData.append('providerId', dto.providerId);
		return {
			method: 'POST',
			uri: 'files/contactimport/summary',
			data: formData,
			...restOptions,
		};
	},
};
