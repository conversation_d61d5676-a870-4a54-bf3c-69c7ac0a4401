import { AnyAction, ThunkDispatch } from '@reduxjs/toolkit';

import { IApplicationState } from 'types/applicationstate';
import { extractQueryCacheEntries } from 'util/extractQueryCacheData';
import { mergeUniqueByKey } from 'util/mergeUniqueByKey';

import { forceRefetchInitialBillables } from '../billables/utils';
import { dispatchMovedItemToTrashSnackbar } from '../trash/utils';
import { apiService } from '..';

import { insuranceClaimsApiConfigs, InsuranceClaimsConfigParams } from './config';

export type GetInsuranceClaimResponse = IInsuranceClaim;
export type GetInsuranceClaimRequest = InsuranceClaimsConfigParams<'getInsuranceClaim'>;

export type GetContactInsuranceClaimsResponse = IPaginatedResult<IInsuranceClaimReference>;
export type GetContactInsuranceClaimsRequest = InsuranceClaimsConfigParams<'getContactInsuranceClaims'>;

export type GetInsuranceClaimsResponse = IPaginatedResult<IInsuranceClaimListEntry>;
export type GetInsuranceClaimsRequest = InsuranceClaimsConfigParams<'getInsuranceClaims'>;

export type DeleteInsuranceClaimResponse = string;
export type DeleteInsuranceClaimRequest = InsuranceClaimsConfigParams<'deleteInsuranceClaim'>;

export type UpdateStatusInsuranceClaimRequest = InsuranceClaimsConfigParams<'updateInsuranceClaimStatus'>;

export type PayInsuranceClaimRequest = InsuranceClaimsConfigParams<'payInsuranceClaim'>;

export type GetClaimsExportResponse = string;
export type GetClaimsExportRequest = InsuranceClaimsConfigParams<'getClaimsExport'>;

export type GetInsuranceClaimErrorsResponse = IInsuranceClaimError[];
export type GetInsuranceClaimErrorsRequest = InsuranceClaimsConfigParams<'getInsuranceClaimErrors'>;

export type GetInsuranceClaimRemittanceResponse = IInsuranceClaimRemittance;
export type GetInsuranceClaimRemittanceRequest = InsuranceClaimsConfigParams<'getInsuranceClaimRemittance'>;

export type DownloadInsuranceClaimRemittanceResponse = Blob;
export type DownloadInsuranceClaimRemittanceRequest = InsuranceClaimsConfigParams<'downloadInsuranceClaimRemittance'>;

export const forceRefetchAllClaims = (state: IApplicationState, dispatch: ThunkDispatch<any, any, AnyAction>) => {
	const refetchInsuranceClaims = () => {
		const claimListCacheEntry = extractQueryCacheEntries('getInsuranceClaims', state.mainApi, true);

		if (claimListCacheEntry) {
			const args = claimListCacheEntry.args as GetInsuranceClaimsRequest;

			dispatch(
				insuranceClaimService.endpoints.getInsuranceClaims.initiate(
					{ ...args, query: { ...args.query, offset: 0 } },
					{ forceRefetch: true }
				)
			);
		}
	};

	const refetchContactInsuranceClaims = () => {
		const claimListCacheEntry = extractQueryCacheEntries('getContactInsuranceClaims', state.mainApi, true);

		if (claimListCacheEntry) {
			const args = claimListCacheEntry.args as GetContactInsuranceClaimsRequest;

			dispatch(
				insuranceClaimService.endpoints.getContactInsuranceClaims.initiate(
					{ ...args, query: { ...args.query, offset: 0 } },
					{ forceRefetch: true }
				)
			);
		}
	};

	refetchInsuranceClaims();
	refetchContactInsuranceClaims();
};

export const insuranceClaimService = apiService.injectEndpoints({
	overrideExisting: true,
	endpoints: (build) => ({
		getInsuranceClaim: build.query<GetInsuranceClaimResponse, GetInsuranceClaimRequest>({
			query: insuranceClaimsApiConfigs.getInsuranceClaim,
		}),
		getInsuranceClaims: build.query<GetInsuranceClaimsResponse, GetInsuranceClaimsRequest>({
			query: insuranceClaimsApiConfigs.getInsuranceClaims,
			serializeQueryArgs: ({ queryArgs }) => {
				// Omit offset from serializer so we can combine the results
				const { offset, ...restArgs } = queryArgs.query || {};

				return restArgs;
			},
			merge: (currentCache, newItems, { arg: { query, ...rest } }) => {
				if (!query?.offset) {
					return newItems;
				}

				return {
					items: currentCache?.items
						? mergeUniqueByKey(currentCache.items, newItems.items, 'id')
						: newItems.items,
					pagination: newItems.pagination,
					totalCount: newItems.totalCount,
				};
			},
		}),
		getContactInsuranceClaims: build.query<GetContactInsuranceClaimsResponse, GetContactInsuranceClaimsRequest>({
			query: insuranceClaimsApiConfigs.getContactInsuranceClaims,
			serializeQueryArgs: ({ queryArgs }) => {
				// Omit offset from serializer so we can combine the results
				const { offset, ...restArgs } = queryArgs.query || {};

				return restArgs;
			},
			merge: (currentCache, newItems, { arg: { query, ...rest } }) => {
				if (!query?.offset) {
					return newItems;
				}

				return {
					items: currentCache?.items
						? mergeUniqueByKey(currentCache.items, newItems.items, 'id')
						: newItems.items,
					pagination: newItems.pagination,
					totalCount: newItems.totalCount,
				};
			},
		}),
		deleteInsuranceClaim: build.mutation<DeleteInsuranceClaimResponse, DeleteInsuranceClaimRequest>({
			query: insuranceClaimsApiConfigs.deleteInsuranceClaim,
			invalidatesTags(_r, _e, { id }) {
				return [
					{ type: 'InsuranceClaimUSProfessionalDetail', id },
					{ type: 'ClaimHistory', id },
					'TaskContactDetail',
					'AppointmentBillables',
				];
			},
			async onQueryStarted({ id }, { dispatch, getState, queryFulfilled }) {
				const state = getState() as IApplicationState;

				try {
					await queryFulfilled;

					const updateCacheEntries = (requestType: 'getContactInsuranceClaims' | 'getInsuranceClaims') => {
						const cacheEntries = extractQueryCacheEntries(requestType, state.mainApi);
						cacheEntries.forEach((entry) => {
							dispatch(
								insuranceClaimService.util.updateQueryData(
									requestType,
									entry.args as GetInsuranceClaimsRequest | GetContactInsuranceClaimsRequest,
									(draft) => {
										const index = draft.items.findIndex((item) => item.id === id);
										if (index !== -1) {
											draft.items.splice(index, 1);
										}
									}
								)
							);
						});
					};

					updateCacheEntries('getContactInsuranceClaims');
					updateCacheEntries('getInsuranceClaims');

					forceRefetchInitialBillables(getState() as IApplicationState, dispatch);

					dispatchMovedItemToTrashSnackbar({
						dispatch,
						type: 'USProfessionalClaim',
						isEnabled: true,
						filter: {
							all: false,
							excludedIds: [],
							ids: [id],
						},
					});
				} catch (error) {}
			},
		}),
		updateStatusInsuranceClaim: build.mutation<void, UpdateStatusInsuranceClaimRequest>({
			query: insuranceClaimsApiConfigs.updateInsuranceClaimStatus,
			invalidatesTags(_r, _e, { id }) {
				return [
					{ type: 'InsuranceClaimUSProfessionalDetail', id },
					{ type: 'ClaimHistory', id },
					'TaskContactDetail',
				];
			},
			async onQueryStarted(_, { queryFulfilled, getState, dispatch }) {
				try {
					await queryFulfilled;
					const state = getState() as IApplicationState;
					forceRefetchAllClaims(state, dispatch);
				} catch (e) {}
			},
		}),
		payInsuranceClaim: build.mutation<void, PayInsuranceClaimRequest>({
			query: insuranceClaimsApiConfigs.payInsuranceClaim,
			invalidatesTags(_r, _e, { id }) {
				return [
					{ type: 'InsuranceClaimUSProfessionalDetail', id },
					{ type: 'ClaimHistory', id },
				];
			},
			async onQueryStarted(_, { queryFulfilled, getState, dispatch }) {
				try {
					await queryFulfilled;
					const state = getState() as IApplicationState;
					forceRefetchAllClaims(state, dispatch);
				} catch (e) {}
			},
		}),
		getClaimsExport: build.query<GetClaimsExportResponse, GetClaimsExportRequest>({
			query: insuranceClaimsApiConfigs.getClaimsExport,
		}),
		getInsuranceClaimErrors: build.query<GetInsuranceClaimErrorsResponse, GetInsuranceClaimErrorsRequest>({
			query: insuranceClaimsApiConfigs.getInsuranceClaimErrors,
			providesTags(_, _e, { id }) {
				return [{ type: 'InsuranceClaimErrors', id }];
			},
		}),
		getInsuranceClaimRemittance: build.query<
			GetInsuranceClaimRemittanceResponse,
			GetInsuranceClaimRemittanceRequest
		>({
			query: insuranceClaimsApiConfigs.getInsuranceClaimRemittance,
		}),
		downloadInsuranceClaimRemittance: build.query<
			DownloadInsuranceClaimRemittanceResponse,
			DownloadInsuranceClaimRemittanceRequest
		>({
			query: insuranceClaimsApiConfigs.downloadInsuranceClaimRemittance,
		}),
	}),
});
