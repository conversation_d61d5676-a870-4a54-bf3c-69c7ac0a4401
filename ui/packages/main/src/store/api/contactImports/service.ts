import { TypedUseQueryStateResult } from '@reduxjs/toolkit/dist/query/react';

import { analytics } from '@carepatron/utilities';

import { contactsLayoutId, contactsSchemaId } from 'components/SchemaForms/constants';
import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import { apiService } from 'store/api';
import { addSnackbar } from 'store/slices/snackbars/slice';
import { IApplicationState } from 'types/applicationstate';

import { schemasService } from '../schemas/service';
import { selectUserId } from '../user/selectors';

import { contactImportsApiConfig, ContactImportsApiConfigParams } from './config';

export const contactImportsService = apiService.injectEndpoints({
	overrideExisting: true,
	endpoints: (build) => ({
		importContacts: build.mutation<ContactImportSummary, ContactImportsApiConfigParams<'importContacts'>>({
			query: contactImportsApiConfig.importContacts,
			invalidatesTags: (_, __, arg) =>
				arg.context?.skipContactImportSummary ? [] : [{ type: 'ContactImportSummary', id: 'LIST' }],
			onQueryStarted: async (
				{ dto: { importFileId, importSource }, context = {} },
				{ dispatch, queryFulfilled }
			) => {
				try {
					await queryFulfilled;

					analytics.trackEvent({
						eventAction: 'import client data',
						importSource: importSource,
						otherDataSourceName: context?.otherDataSourceName,
					});
				} catch (error) {
					dispatch(
						addSnackbar({
							id: importFileId,
							titleId: langIds.ImportClientsFailureSnackbarTitle,
							messageId: langIds.ImportClientsFailureSnackbarDescription,
							variant: 'error',
							ignoreDuration: true,
						})
					);
				}
			},
		}),
		getContactImportSummary: build.query<
			GetContactImportSummaryResponse,
			ContactImportsApiConfigParams<'getContactImportSummary'>
		>({
			query: contactImportsApiConfig.getContactImportSummary,
			providesTags: [{ type: 'ContactImportSummary', id: 'LIST' }],
			onQueryStarted: async ({ onSuccess }, { queryFulfilled, dispatch, getState }) => {
				try {
					const { data } = await queryFulfilled;
					const currentUserId = selectUserId(getState() as IApplicationState);

					const hasSuccessfulImports = data.items.some(
						(summary) =>
							summary.createdByPersonId === currentUserId &&
							summary.status === 'Successful' &&
							summary.lastStatusSeenBy.length === 0
					);

					// If there are any successful imports, refetch the clients
					if (hasSuccessfulImports) {
						onSuccess?.();

						dispatch(
							schemasService.util.invalidateTags([
								{ type: 'Schema', id: contactsSchemaId },
								{ type: 'SchemaLayout', id: contactsLayoutId },
							])
						);
					}
				} catch (error) {
					dispatch(addErrorSnackbar(error));
				}
			},
		}),
		updateContactImportSummary: build.mutation<
			ContactImportSummary,
			ContactImportsApiConfigParams<'updateContactImportSummary'>
		>({
			query: contactImportsApiConfig.updateContactImportSummary,
			invalidatesTags: [{ type: 'ContactImportSummary', id: 'LIST' }],
		}),
		createContactImportMapping: build.mutation<
			ContactImportMappingResult,
			ContactImportsApiConfigParams<'createContactImportMapping'>
		>({
			query: contactImportsApiConfig.createContactImportMapping,
		}),
		updateContactImportSchema: build.mutation<string, ContactImportsApiConfigParams<'updateContactImportSchema'>>({
			query: contactImportsApiConfig.updateContactImportSchema,
		}),
	}),
});

export type ImportSummaryStatus =
	| 'Pending' // Initial state when import is queued but not yet started
	| 'Running' // Import process is currently being executed
	| 'Successful' // Import completed without any errors
	| 'Failed' // Import encountered errors and could not complete
	| 'Draft' // Import is in draft state, not yet submitted for processing
	| 'Preprocessing' // File is being prepared and validated before mapping
	| 'ReadyForMapping' // File is ready for column mapping configuration
	| 'Cancelled'; // Import was cancelled by the user

export interface ContactImportSummary {
	id: string;
	providerId: string;
	fileId: string;
	fileName: string;
	fileSize: number;
	fileExtension: string;
	status: ImportSummaryStatus;
	lastStatusSeenBy: string[];
	createdByPersonId: string;
	createdDateTimeUtc: Date;
	updatedDateTime?: Date;
	completedDateTimeUtc?: Date;
	importType?: ImportType;
	schemaFileId?: string;
	originalFileName?: string;
}

export interface GetContactImportSummaryResponse {
	items: ContactImportSummary[];
}

export type ContactImportSummaryGetQueryResult = TypedUseQueryStateResult<
	{ items: ContactImportSummary[] },
	unknown,
	any
>;

export type SuggestedCustomField = {
	columnMapping: IContactImportOption;
	property: ControlTypes;
	layoutContainer: string;
};

export type SuggestedLayoutContainer = {
	heading: string;
};

export interface ContactImportMappingResult {
	columnNames: string[];
	mappings: IContactImportOption[];
	suggestedCustomFields: SuggestedCustomField[];
	suggestedLayoutContainers: SuggestedLayoutContainer[];
}

export type ImportType = 'Standard' | 'Advanced';
