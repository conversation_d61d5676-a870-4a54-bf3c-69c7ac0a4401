import { ApiRequestConfig } from 'store/api/types';

import { ImportSummaryStatus, ImportType } from './service';

export interface ContactImportsApiConfigs {
	importContacts: ApiRequestConfig<{
		dto: {
			importSource: ContactImportSource;
			importFileId: string;
			fileName: string;
			fileExtension: string;
			fileSize: number;
			mappedColumns?: IContactImportOption[];
			dataSchema?: ISchema;
			status?: ImportSummaryStatus;
			importType?: ImportType;
		};
		context?: {
			otherDataSourceName?: string;
			skipContactImportSummary?: boolean;
		};
	}>;
	getContactImportSummary: ApiRequestConfig<{
		query?: {
			fromDate?: Date;
			toDate?: Date;
			statuses?: ImportSummaryStatus[];
		};
		onSuccess?: () => void;
	}>;
	updateContactImportSummary: ApiRequestConfig<{
		id: string;
		dto: {
			lastStatusSeenBy: string[];
			status?: ImportSummaryStatus;
			mappedColumns?: IContactImportOption[];
		};
	}>;
	createContactImportMapping: ApiRequestConfig<{
		dto: {
			fileId: string;
			fileName: string;
		};
	}>;
	updateContactImportSchema: ApiRequestConfig<{
		dto: {
			fileId: string;
			fileName: string;
			importOptions: IContactImportOption[];
			dataSchema: ISchema;
			layoutSchema: ISchemaLayout;
		};
	}>;
}

export type ContactImportsApiConfigParams<Config extends keyof ContactImportsApiConfigs> = Parameters<
	ContactImportsApiConfigs[Config]
>[0];

export const contactImportsApiConfig: ContactImportsApiConfigs = {
	importContacts: ({ dto }) => ({
		method: 'POST',
		uri: 'contacts/imports',
		data: dto,
		options: { asProvider: true },
	}),
	getContactImportSummary: ({ query: { fromDate, toDate, statuses } = {} }) => ({
		method: 'GET',
		uri: 'contacts/imports/summary',
		params: { fromDate, toDate, status: statuses },
		options: { asProvider: true, qs: { arrayFormat: 'repeat', skipNulls: true } },
	}),
	updateContactImportSummary: ({ id, dto }) => ({
		method: 'PUT',
		uri: `contacts/imports/summary/${id}`,
		data: dto,
		options: { asProvider: true },
	}),
	createContactImportMapping: ({ dto }) => ({
		method: 'POST',
		uri: 'contacts/imports/mappings',
		data: dto,
		options: { asProvider: true },
	}),
	updateContactImportSchema: ({ dto }) => ({
		method: 'PUT',
		uri: 'contacts/imports/schemas',
		data: dto,
		options: { asProvider: true },
	}),
};
