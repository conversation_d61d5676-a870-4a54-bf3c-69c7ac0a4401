import { isBefore } from 'date-fns';
import { createSelector } from 'reselect';

import { ContactImportSummary, ContactImportSummaryGetQueryResult } from './service';

export const selectContactImportSummariesFromResult = (
	result: ContactImportSummaryGetQueryResult
): ContactImportSummary[] => result.data?.items || [];

export const selectVisibleContactImportSummariesFromResult = (
	result: ContactImportSummaryGetQueryResult,
	userId: string
): ContactImportSummary[] => {
	return selectContactImportSummariesFromResult(result).filter((item) => {
		// `lastStatusSeenBy` contains the user id of the user who last dismissed the notification.
		const wasDismissed = item.lastStatusSeenBy.length > 0;

		// Always show pending or running import notifications
		const isPendingOrRunning = ['Pending', 'Running'].includes(item.status);

		// Show the "Successful" or "Failed" import notifications that have not been `seen` yet.
		// `Seen` in this context means the user has dismissed the notification.
		const isSuccessfulOrFailedAndUndismissed = ['Successful', 'Failed'].includes(item.status) && !wasDismissed;

		// Show the "Successful" or "Failed" import notifications that have been dismissed before they were completed.
		const wasDismissedBeforeCompletion =
			['Successful', 'Failed'].includes(item.status) &&
			wasDismissed &&
			item.updatedDateTime &&
			item.completedDateTimeUtc &&
			isBefore(new Date(item.updatedDateTime), new Date(item.completedDateTimeUtc));

		return (
			item.createdByPersonId === userId &&
			(isPendingOrRunning || isSuccessfulOrFailedAndUndismissed || wasDismissedBeforeCompletion)
		);
	});
};

export const selectActiveContactImportSummariesFromResult = createSelector(
	[selectContactImportSummariesFromResult, (_, userId) => userId],
	(contactImportSummaries, userId): ContactImportSummary | undefined => {
		const sortedContactImportSummaries = contactImportSummaries.filter((item) => item.createdByPersonId === userId);
		return sortedContactImportSummaries[sortedContactImportSummaries.length - 1];
	}
);
