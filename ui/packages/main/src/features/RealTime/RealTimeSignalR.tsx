import { useEffect } from 'react';
import { useRealTimeVideoCall } from 'features/VideoCall/RealTime/useRealTimeVideoCall';

import { useRealTimeAppNotifications } from 'components/AppNotificationsPanel/hooks/useRealTimeAppNotifications';
import { useRealTimeClientImport } from 'components/modals/ImportClientsActivity/useRealTimeClientImport';
import { useAppSelector } from 'store';
import { useCurrentProviderId } from 'store/api/providers/hooks';
import { selectIsAuthenticated } from 'store/api/user/selectors';

import { useSignalRNotificationsHub } from './useSignalRNotificationsHub';

export const RealTimeSignalR: React.FC = () => {
	const { currentProviderId } = useCurrentProviderId();
	const isAuthenticated = useAppSelector(selectIsAuthenticated);

	const { disconnectFromHub: disconnectFromNotificationsHub } = useSignalRNotificationsHub();

	// If you put the real-time notifications here, it will be available everywhere
	useRealTimeAppNotifications();
	useRealTimeVideoCall();
	useRealTimeClientImport();

	/**
	 * Disconnect from the hub when the provider changes
	 */
	useEffect(() => {
		return () => {
			disconnectFromNotificationsHub();
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [currentProviderId]);

	useEffect(() => {
		if (isAuthenticated) return;
		// Disconnect the notifications hub when the user is not authenticated
		disconnectFromNotificationsHub();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [isAuthenticated]);

	return null;
};
