import { FC, ReactNode } from 'react';
import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import RedoRoundedIcon from '@mui/icons-material/RedoRounded';
import UploadFileRoundedIcon from '@mui/icons-material/UploadFileRounded';
import { CircularProgress } from '@mui/material';

import { ImportClientsFormValuesV2 } from 'components/modals/ImportClients';
import { useCurrentClientImport } from 'components/modals/ImportClientsActivity/useCurrentClientImport';
import langIds from 'lang/langIds';
import { addModal } from 'store/slices/modals/slice';

type ClientImportSummaryWrapperProps = {
	children: (args: {
		isProcessing: boolean;
		isReadyForMapping: boolean;
		onImportClients: () => void;
		importButtonIcon: ReactNode;
		importButtonText: ReactNode;
	}) => ReactNode;
};

export const ClientImportSummaryWrapper: FC<ClientImportSummaryWrapperProps> = ({ children }) => {
	const dispatch = useDispatch();

	const { importSummary } = useCurrentClientImport();

	const isReadyForMapping = importSummary?.status === 'ReadyForMapping';
	const isProcessing = !!importSummary && !isReadyForMapping;

	const getImportButtonIcon = () => {
		if (isProcessing) return <CircularProgress size={16} />;
		if (isReadyForMapping) return <RedoRoundedIcon />;
		return <UploadFileRoundedIcon />;
	};

	const getImportButtonText = () => {
		if (isProcessing) return <FormattedMessage id={langIds.ImportProcessing} />;
		if (isReadyForMapping) return <FormattedMessage id={langIds.ContinueImport} />;
		return <FormattedMessage id={langIds.Import} />;
	};

	const openClientImportModal = () => {
		if (!importSummary) return;

		const initialValues: ImportClientsFormValuesV2 = {
			fileId: importSummary.fileId,
			fileName: importSummary.fileName,
			importSummaryId: importSummary.id,
		};

		dispatch(
			addModal({
				type: 'ImportClients',
				data: {
					initialValues,
					schemaFileId: importSummary.schemaFileId,
				},
			})
		);
	};

	const handleImportClients = () => {
		// If the import is ready for mapping, open the mapping modal directly.
		if (isReadyForMapping) {
			openClientImportModal();
			return;
		}

		// If there is an active import, open the import activity modal.
		if (!!importSummary) {
			dispatch(addModal('ImportClientsActivity'));
			return;
		}

		// Otherwise, open the import clients modal.
		dispatch(addModal('ImportClients'));
	};

	return (
		<>
			{children({
				isProcessing,
				isReadyForMapping,
				importButtonIcon: getImportButtonIcon(),
				importButtonText: getImportButtonText(),
				onImportClients: handleImportClients,
			})}
		</>
	);
};
