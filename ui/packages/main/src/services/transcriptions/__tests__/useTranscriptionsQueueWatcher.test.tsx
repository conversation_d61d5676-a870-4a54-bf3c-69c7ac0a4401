/**
 * Test for session-based transcription queue processing
 * This test focuses on ensuring the hook properly integrates with the new queue API
 */

import { renderHook } from '@testing-library/react';

// Mock all external dependencies first
jest.mock('@carepatron/utilities', () => ({
	logger: {
		info: jest.fn(),
		debug: jest.fn(),
		error: jest.fn(),
		warn: jest.fn(),
	},
}));

const mockUploadTranscriptionPart = jest.fn();
jest.mock('services/api', () => ({
	default: {
		uploadTranscriptionPart: mockUploadTranscriptionPart,
	},
}));

const mockFetchTranscriptions = jest.fn();
jest.mock('store/api/notes/hooks', () => ({
	useLazyGetNoteTranscriptionsQuery: () => [mockFetchTranscriptions],
}));

jest.mock('store/api/providers/selectors', () => ({
	selectCurrentProviderId: () => 'test-provider-id',
}));

const mockCompleteTranscriptionPartsUnwrap = jest.fn();
const mockCompleteTranscriptionParts = jest.fn(() => ({
	unwrap: mockCompleteTranscriptionPartsUnwrap,
}));

jest.mock('store/api/transcriptions/hooks', () => ({
	useCompleteTranscriptionPartMutation: () => [mockCompleteTranscriptionParts],
}));

jest.mock('store/slices/features/hooks', () => ({
	useFeatureFlag: () => ({ hasFeatureFlagged: true }),
}));

jest.mock('store', () => ({
	useAppSelector: jest.fn(() => 'test-provider-id'),
}));

// Mock the TranscriptionsQueue
const mockQueue = {
	peekAndLockFront: jest.fn(),
	deleteByKey: jest.fn(),
	getAllItems: jest.fn(),
	unlockFront: jest.fn(),
	enqueue: jest.fn(),
};

// Mock sessionStorage for session ID generation
const mockSessionStorage = {
	getItem: jest.fn(),
	setItem: jest.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
	value: mockSessionStorage,
	writable: true,
});

jest.mock('../transcriptionsQueue', () => ({
	__esModule: true,
	default: {
		getInstance: jest.fn(() => mockQueue),
	},
}));

// Mock the TranscriptionStateManager
const mockTranscriptionStateManager = {
	getActiveTranscriptions: jest.fn(() => new Set()),
	markTranscriptionActive: jest.fn(),
	markTranscriptionCompleted: jest.fn(),
	isTranscriptionActive: jest.fn(() => false),
	getAllActiveTranscriptions: jest.fn(() => ({})),
	clearActiveTranscriptions: jest.fn(),
};

jest.mock('../transcriptionStateManager', () => ({
	TranscriptionStateManager: mockTranscriptionStateManager,
}));

// Mock the session utils
const mockGetTabSessionId = jest.fn(() => 'test-session-id-123');
jest.mock('../sessionUtils', () => ({
	getTabSessionId: mockGetTabSessionId,
}));

describe('useTranscriptionsQueueWatcher - Session-based Processing', () => {
	beforeEach(() => {
		jest.clearAllMocks();
		// Reset all mocks to default successful states
		mockSessionStorage.getItem.mockReturnValue(null);
		mockGetTabSessionId.mockReturnValue('test-session-id-123');
		mockQueue.peekAndLockFront.mockResolvedValue(null);
		mockQueue.getAllItems.mockResolvedValue([]);
		mockQueue.deleteByKey.mockResolvedValue(undefined);
		mockQueue.unlockFront.mockReturnValue(undefined);
		mockQueue.enqueue.mockResolvedValue(undefined);
		mockUploadTranscriptionPart.mockResolvedValue([]);
		mockCompleteTranscriptionPartsUnwrap.mockResolvedValue({});
		mockTranscriptionStateManager.getActiveTranscriptions.mockReturnValue(new Set());
	});

	it('should integrate with the new queue API and return sessionId', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;

		const { result } = renderHook(() => useTranscriptionsQueueWatcher());

		expect(result.current.sessionId).toBeDefined();
		expect(typeof result.current.sessionId).toBe('string');
		expect(result.current.sessionId.length).toBeGreaterThan(0);
	});

	it('should maintain consistent sessionId across re-renders', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;

		const { result, rerender } = renderHook(() => useTranscriptionsQueueWatcher());
		const firstSessionId = result.current.sessionId;

		rerender();

		expect(result.current.sessionId).toBe(firstSessionId);
		expect(typeof result.current.sessionId).toBe('string');
	});

	it('should setup queue processing interval', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;
		jest.useFakeTimers();
		const setIntervalSpy = jest.spyOn(global, 'setInterval');

		renderHook(() => useTranscriptionsQueueWatcher());

		// Should setup the queue processing interval
		expect(setIntervalSpy).toHaveBeenCalledWith(expect.any(Function), 3000);

		jest.useRealTimers();
		setIntervalSpy.mockRestore();
	});

	it('should setup cleanup and resumption timeouts', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;
		jest.useFakeTimers();
		const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

		renderHook(() => useTranscriptionsQueueWatcher());

		// Should setup resumption timeout (2 seconds) and cleanup timeout (15 seconds)
		expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 2000);
		expect(setTimeoutSpy).toHaveBeenCalledWith(expect.any(Function), 15000);

		jest.useRealTimers();
		setTimeoutSpy.mockRestore();
	});

	// Basic functionality tests without complex timer manipulation
	it('should initialize with correct session ID format', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;

		const { result } = renderHook(() => useTranscriptionsQueueWatcher());

		// Session ID should be the mocked value
		expect(result.current.sessionId).toBe('test-session-id-123');
	});

	it('should properly initialize TranscriptionsQueue instance', async () => {
		const useTranscriptionsQueueWatcher = (await import('../useTranscriptionsQueueWatcher')).default;
		const TranscriptionsQueue = (await import('../transcriptionsQueue')).default;

		renderHook(() => useTranscriptionsQueueWatcher());

		// Should get the queue instance
		expect(TranscriptionsQueue.getInstance).toHaveBeenCalled();
	});
});
