import { v4 as uuid } from 'uuid';

const TAB_SESSION_KEY = 'transcription_tab_session_id';

/**
 * Generate a unique session ID for this browser tab
 */
const generateSessionId = (): string => {
	return `session_${uuid()}`;
};

/**
 * Get or create a session ID for this tab, persisted in sessionStorage
 * This ensures each tab has a consistent session ID across page refreshes
 */
export const getTabSessionId = (): string => {
	let sessionId = sessionStorage.getItem(TAB_SESSION_KEY);

	if (!sessionId) {
		sessionId = generateSessionId();
		sessionStorage.setItem(TAB_SESSION_KEY, sessionId);
	}

	return sessionId;
};
