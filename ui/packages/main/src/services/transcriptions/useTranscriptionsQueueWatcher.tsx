import { useEffect, useMemo, useRef } from 'react';

import { logger } from '@carepatron/utilities';

import api from 'services/api';
import { useAppSelector } from 'store';
import { useLazyGetNoteTranscriptionsQuery } from 'store/api/notes/hooks';
import { selectCurrentProviderId } from 'store/api/providers/selectors';
import { useCompleteTranscriptionPartMutation } from 'store/api/transcriptions/hooks';
import { useFeatureFlag } from 'store/slices/features/hooks';

import { getTabSessionId } from './sessionUtils';
import TranscriptionsQueue, { TranscriptionPartQueueMessage } from './transcriptionsQueue';
import { TranscriptionStateManager } from './transcriptionStateManager';

// Log category for all transcription queue related logs
const LOG_CATEGORY = 'ai-transcription-queue';

// Queue processing interval (ms)
const QUEUE_PROCESSING_INTERVAL = 3000;

// Cleanup interval for orphaned items (ms) - run every 30 minutes
const CLEANUP_INTERVAL = 30 * 60 * 1000;

// Session ID generation and persistence is now handled by the shared sessionUtils

/**
 * Hook that manages transcription queue processing with session-based ownership and smart resumption.
 * Each tab only processes transcription parts that it initiated, ensuring no duplicates.
 *
 * Key features:
 * - Simple session-based ownership (no complex coordination)
 * - Each tab only processes its own transcription parts
 * - Smart resumption: Can resume pending transcriptions from previous sessions
 * - Automatic cleanup of truly orphaned items from different users/transcriptions
 * - Queue automatically handles sessionId generation and assignment
 *
 * @returns sessionId - The session identifier for this tab
 */
const useTranscriptionsQueueWatcher = () => {
	const processingRef = useRef<boolean>(false);
	const queueRef = useRef(TranscriptionsQueue.getInstance());
	// Get the session ID for this tab - memoized to be stable across re-renders
	const sessionId = useMemo(() => getTabSessionId(), []);

	const providerId = useAppSelector(selectCurrentProviderId);
	const { hasFeatureFlagged: hasAgentProviderGoogleSpeechToText } = useFeatureFlag(
		'agent-provider-google-speech-to-text'
	);
	const { hasFeatureFlagged: hasDeferredTranscriptionCompletion } = useFeatureFlag(
		'deferred-transcription-completion'
	);

	const [fetchTranscriptions] = useLazyGetNoteTranscriptionsQuery();
	const [completeTranscriptionParts] = useCompleteTranscriptionPartMutation();

	/**
	 * Get currently active transcriptions for this user/provider
	 */
	const getActiveTranscriptions = (): Set<string> => {
		return TranscriptionStateManager.getActiveTranscriptions(providerId);
	};

	/**
	 * Mark a transcription as active (being processed)
	 */
	const markTranscriptionActive = (transcriptionId: string) => {
		TranscriptionStateManager.markTranscriptionActive(providerId, transcriptionId);
	};

	/**
	 * Mark a transcription as completed (no longer being processed)
	 */
	const markTranscriptionCompleted = (transcriptionId: string) => {
		TranscriptionStateManager.markTranscriptionCompleted(providerId, transcriptionId);
	};

	/**
	 * Resume pending transcriptions from previous sessions
	 */
	const resumePendingTranscriptions = async () => {
		try {
			const allItems = await queueRef.current.getAllItems();
			const activeTranscriptions = getActiveTranscriptions();
			let resumedCount = 0;

			for (const item of allItems) {
				// Skip items from current session (already being processed)
				if (item.value.sessionId === sessionId) {
					continue;
				}

				// Check if this transcription is still active and can be resumed
				if (activeTranscriptions.has(item.value.transcriptionId)) {
					// Adopt this item by updating its sessionId to current session
					const updatedItem = {
						...item.value,
						sessionId: sessionId, // Take ownership
					};

					// Remove old item and add updated one
					await queueRef.current.deleteByKey(item.key);
					await queueRef.current.enqueue({
						audioChunk: updatedItem.audioChunk,
						noteId: updatedItem.noteId,
						contactId: updatedItem.contactId,
						transcriptionId: updatedItem.transcriptionId,
						metadata: updatedItem.metadata,
					});

					resumedCount++;
				}
			}

			if (resumedCount > 0) {
				logger.info('Resumed pending transcriptions', {
					category: LOG_CATEGORY,
					sessionId,
					resumedCount,
				});
			}
		} catch (error) {
			logger.error('Error during transcription resumption', {
				category: LOG_CATEGORY,
				sessionId,
				error,
			});
		}
	};

	/**
	 * Clean up truly orphaned transcription parts (old items from inactive transcriptions)
	 */
	const cleanupOrphanedItems = async () => {
		try {
			const allItems = await queueRef.current.getAllItems();
			const activeTranscriptions = getActiveTranscriptions();
			let cleanedCount = 0;

			for (const item of allItems) {
				// Skip our own items
				if (item.value.sessionId === sessionId) {
					continue;
				}

				// Skip items from active transcriptions (these can be resumed)
				if (activeTranscriptions.has(item.value.transcriptionId)) {
					continue;
				}

				// Clean up items from inactive transcriptions (regardless of age)
				// Since we can't extract timestamp from UUID-based sessionIds,
				// we rely on the active transcriptions list to determine what to keep
				await queueRef.current.deleteByKey(item.key);
				cleanedCount++;

				logger.info('Cleaned up orphaned transcription part', {
					category: LOG_CATEGORY,
					sessionId,
					orphanedSessionId: item.value.sessionId,
					transcriptionId: item.value.transcriptionId,
					partNumber: item.value.metadata.partNumber,
					reason: 'inactive_transcription',
				});
			}

			if (cleanedCount > 0) {
				logger.info('Completed orphaned items cleanup', {
					category: LOG_CATEGORY,
					sessionId,
					cleanedCount,
					totalItemsChecked: allItems.length,
				});
			}
		} catch (error) {
			logger.error('Error during orphaned items cleanup', {
				category: LOG_CATEGORY,
				sessionId,
				error,
			});
		}
	};

	/**
	 * Process audio chunk upload for transcription
	 */
	const processAudioChunkUpload = async (message: TranscriptionPartQueueMessage) => {
		if (!providerId || !message) {
			return;
		}

		const { transcriptionId, metadata, contactId, noteId } = message;

		// Mark transcription as active when we start processing
		markTranscriptionActive(transcriptionId);

		try {
			if (metadata.isLastPart) {
				// Only complete transcription if this item belongs to our session
				if (message.sessionId === sessionId) {
					await completeTranscriptionParts({
						transcriptionId,
					}).unwrap();

					// Mark transcription as completed since this was the last part
					markTranscriptionCompleted(transcriptionId);

					// Refetch transcriptions to get the latest status
					fetchTranscriptions({
						contactId,
						noteId,
					});

					logger.info('Completed transcription processing', {
						category: LOG_CATEGORY,
						transcriptionId,
						sessionId,
						partNumber: metadata.partNumber,
					});
				}
			} else {
				// Only upload transcription part if this item belongs to our session
				if (message.sessionId === sessionId) {
					await api.uploadTranscriptionPart({
						providerId,
						transcriptionId,
						partNumber: metadata.partNumber,
						startTime: metadata.startTime,
						endTime: metadata.endTime,
						isLastPart: metadata.isLastPart,
						dto: message.audioChunk,
						context: {
							useAgentProviderGoogleSpeechToText: hasAgentProviderGoogleSpeechToText,
							deferredTranscriptionCompletion: hasDeferredTranscriptionCompletion,
						},
					});
				}
			}
		} catch (error) {
			logger.error('Error processing transcription part', {
				category: LOG_CATEGORY,
				transcriptionId,
				partNumber: metadata.partNumber,
				error,
			});
			throw error; // Re-throw to handle in processQueue
		}
	};

	/**
	 * Process the transcription queue
	 */
	const processQueue = async () => {
		if (processingRef.current) return;
		processingRef.current = true;

		try {
			const item = await queueRef.current.peekAndLockFront();

			if (item && item.value.sessionId === sessionId) {
				try {
					await processAudioChunkUpload(item.value);
					await queueRef.current.deleteByKey(item.key);
				} catch (error) {
					logger.error('Error processing audio chunk', {
						category: LOG_CATEGORY,
						transcriptionId: item.value.transcriptionId,
						partNumber: item.value.metadata.partNumber,
						error,
					});
					queueRef.current.unlockFront();
				}
			} else if (item) {
				// Item belongs to another session, unlock it
				queueRef.current.unlockFront();
			}
		} catch (error) {
			logger.error('Error processing transcription queue', {
				category: LOG_CATEGORY,
				error,
			});
		} finally {
			processingRef.current = false;
		}
	};

	useEffect(() => {
		// Set up queue processing interval
		const queueIntervalId = setInterval(processQueue, QUEUE_PROCESSING_INTERVAL);

		// Set up cleanup interval for orphaned items
		const cleanupIntervalId = setInterval(cleanupOrphanedItems, CLEANUP_INTERVAL);

		// Run initial resumption check after a short delay
		const initialResumptionTimeout = setTimeout(resumePendingTranscriptions, 2000); // 2 seconds

		// Run initial cleanup after resumption
		const initialCleanupTimeout = setTimeout(cleanupOrphanedItems, 15000); // 15 seconds

		return () => {
			clearInterval(queueIntervalId);
			clearInterval(cleanupIntervalId);
			clearTimeout(initialResumptionTimeout);
			clearTimeout(initialCleanupTimeout);
		};
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	// Return sessionId for external use (e.g., when adding items to queue)
	return { sessionId };
};

export default useTranscriptionsQueueWatcher;
