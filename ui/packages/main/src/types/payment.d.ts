type PaymentListColumnKey =
	| 'paymentDate'
	| 'client'
	| 'type'
	| 'provider'
	| 'reference'
	| 'amount'
	| 'payoutsStatus'
	| 'payoutDate';

interface IContactPayment {
	id: string;
	providerId: string;
	contactId: string;
	paymentDate: string;
	type: string;
	amount: number;
	fee: number;
	isClientChargedFee: boolean;
	displayAmount: number;
	unallocatedAmount: number;
	currencyCode: string;
	paymentProvider: PaymentProvider;
	refundStatus: string;
	invoices: IPaymentInvoiceReference[];
	refunds: IRefundReference[];
	issuedCredits: IIssuedCreditReference[];
	createdDateTimeUtc: string;
	isBillingV2: boolean;
	canDelete?: boolean;
}

interface IPaymentInvoiceReference {
	invoiceId: string;
	invoiceStatus: InvoiceStatus;
	issueDate: string;
	invoiceNumber: string;
	issuedCredits: IIssuedCreditReference[];
}

interface IIssuedCreditReference {
	id: string;
	providerId: string;
	contactId: string;
	invoiceId?: string;
	paymentId: string;
	currencyCode: string;
	amount: number;
	description: string;
	reason: string;
	type: string;
	issuedDateUtc: string;
}

interface IRefundReference {
	id: string;
	amount: number;
	currencyCode: string;
	reason: string;
	status: RefundStatus;
	failureReason?: string;
	createdDateTimeUtc: string;
}

interface IBillableEntry {
	id: string;
	type: 'Task' | 'Invoice';
	date: string;
	details: string;
	totalOwed: number;
	contactId: string;
	providerId: string;
	serviceId?: string;
	taskId?: string;
	currencyCode?: string;
	items: IBillableItem[];
	unpaid: number;
	paid: number;
	uncharged: number;
	writeOff: number;
	writeOffReason: string;
	issuedCredits: IIssuedCreditReference[];
	invoices: IBillableEntryInvoiceAllocation[];
	unclaimed: number;
	insuranceUnpaid?: number;
	insurancePaid?: number;
	claims: IClaimReference[];
}

interface IBillableEntryInvoiceAllocation {
	id: string;
	number: string;
	status: InvoiceStatus;
	issueDate: string;
	dueDate: string;
}

interface IBillableSummary {
	amount: number;
	count: number;
}

interface ICustomerBalance {
	uncharged: IBillableSummary;
	unpaid: IBillableSummary;
	insuranceUnpaid: IBillableSummary;
	overpaid: IBillableSummary;
	unclaimed: IBillableSummary;
	paymentsUnallocated: IBillableSummary;
	availableCredit: number;
	currencyCode: string;
	otherCurrencies: string[];
}

interface IBillableItem {
	id: string;
	providerId: string;
	contactId: string;
	serviceId?: string;
	billableId: string;

	date?: string;
	description: string;
	detail: string;
	code?: string;
	duration?: number;
	posCode?: string;

	currencyCode?: string;
	units: number;
	taxRates: IItemTaxRate[];
	taxAmount: number;

	price: number;
	uncharged: number;
	unchargedTax: number;
	unchargedPrice: number;
	unpaid: number;
	totalOwed: number;
	selfPayAmount: number;

	isInsuranceEnabled: boolean;
	insuranceAmount: number;
	insuranceUnpaid: number;
	insurancePaid: number;

	writeOff: number;

	isDeleted: boolean;
	isManuallyUpdated: boolean;
}

interface IContactCreditBalance {
	balance: number;
	currencyCode: string;
}

interface IInvoiceUnchargedAmountItem extends Omit<IBillableItem, 'currencyCode' | 'date'> {
	date: string;
	currencyCode: string;
}

interface IPaymentDetails {
	fee: number;
	unallocated: number;
	availableToAllocate: number;
	chargeAmount: number;
	id: string;
	currencyCode: string;
	paymentDate: string;
	paymentMethod: string;
	paymentProvider: PaymentProvider;
	isClientChargedFee: boolean;
	refundStatus?: string;
	refunds: IRefundReference[];
	issuedCredits: IIssuedCreditReference[];
	allocations: IPaymentAllocation[];
	payerName?: string;
	reference?: string;
	isBillingV2?: boolean;
	canDelete?: boolean;
}

interface IClaimReference {
	id: string;
	claimStatus: ClaimStatus;
	date: string;
	number: string;
	type: ClaimType;
	submissionMethod: ClaimSubmissionMethod;
}

interface IAllocationItem {
	id: string;
	billableItemId: string;
	date: string;
	amount: number;
	paid: number;
	description: string;
	detail: string;
	claimLineId?: string;
	claim?: IClaimReference;
	invoiceLineItemId?: string;
	invoice?: IPaymentInvoiceReference;
	currencyCode: string;
}

interface IPaymentAllocation extends IAllocationItem {
	paymentId: string;
	providerId: string;
	allocated: number;
	contactId: string;
}

type PaymentProvider = 'Manual' | 'Stripe' | 'CustomerBalance' | 'Insurance' | 'ChangeHealthcare' | 'ClaimMD';

interface IRecurringBillable {
	items: IRecurringBillableEntry[];
	isLimited: boolean;
}

interface IRecurringBillableEntry {
	providerId: string;
	contactId: string;
	taskId: string;
	details: string;
	date: string;
}

type PayoutStatus = 'Pending' | 'InTransit' | 'Paid' | 'Failed';

interface IPayment {
	id?: string;
	providerId: string;
	contactId: string;
	invoiceId?: string;
	paymentDate: Date;
	type: string;
	amount: number;
	chargeAmount: number;
	transferAmount: number;
	fee: number;
	isClientChargedFee: boolean;
	currencyCode: string;
	paymentProvider: PaymentProvider;
	payoutDateUtc?: Date;
	payoutStatus?: PayoutStatus;
	payoutAmount?: number;
	payoutCurrencyCode?: number;
	paymentIntentId?: string;
	insurancePayerId?: string;
	payerName?: string;
	payerType?: string;
	reference?: string;
	isBillingV2: boolean;
	refundStatus?: string;
	allocations: IPaymentAllocation[];
	refunds: IRefundReference[];
	issuedCredits?: IIssuedCreditReference[];
	canDelete?: boolean;
}

interface IPaymentWithContact extends IPayment {
	contact: IContactReference | null;
}

interface IUpdateBillableItemRequest {
	id?: string;
	writeOff: number;
	price: number;
	units: number;
	selfPayAmount: number;
	taxRates: IItemTaxRate[];
	isManuallyUpdated: boolean;
	isInsuranceEnabled: boolean;
	serviceId?: string;
	detail: string;
	description: string;
}

interface IPaymentsColumnType extends IColumnType<PaymentListColumnKey> {}
