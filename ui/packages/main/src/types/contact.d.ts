enum EmailType {
	Personal = 'Personal',
	Work = 'Work',
	School = 'School',
	Others = 'Others',
}

enum PhoneType {
	Mobile = 'Mobile',
	Home = 'Home',
	Work = 'Work',
	School = 'School',
}

enum AddressType {
	Residential = 'Residential',
	Billing = 'Billing',
	Work = 'Work',
	Others = 'Others',
}

type EmailTypeLabel = keyof typeof EmailType;
type PhoneTypeLabel = keyof typeof PhoneType;
type AddressTypeLabel = keyof typeof AddressType;

interface IContactMultipleField {
	id?: string;
	isPrimary: boolean;
}

interface IContactEmail extends IContactMultipleField {
	email: string;
	type: EmailTypeLabel;
}

interface IContactPhoneNumber extends IContactMultipleField {
	phoneNumber: string;
	type: PhoneTypeLabel;
}

interface IContactAddress extends IContactMultipleField {
	addressDetails: Address;
	type: AddressTypeLabel;
}

interface IContactLanguage extends IContactMultipleField {
	language: string;
}

interface IContact {
	id: string;
	firstName: string;
	lastName: string;
	fullName?: string;
	preferredName?: string;
	profileThumbnailUrl?: string;
	email?: string;
	emails?: IContactEmail[];
	phoneNumber?: string;
	phoneNumbers?: IContactPhoneNumber[];
	businessName?: string;
	profilePhoto?: IMedia;
	middleNames?: string;
	gender?: Gender;
	identificationNumber?: string;
	birthDate?: Date;
	address?: string | Address;
	addresses?: IContactAddress[];
	directRelationships?: ISimplePerson[];
	personId?: string;
	isActive?: boolean;
	ethnicity?: string[];
	occupation?: string;
	employmentStatus?: string;
	livingArrangements?: string;
	relationshipStatus?: string;
	isArchived?: boolean;
	isClient: boolean;
	provider?: IProvider;
	providerId?: string;
	assignedStaff?: ISimplePerson[];
	tags?: ITag[];
	fields?: IDictionary<any>;
	automationSettings?: IContactAutomationSettings;
	status?: string;
	sex?: IOptionSetValue;
	createdDateTimeUtc?: Date;
	settings?: ContactSettings;
	isDemo?: boolean;
	languages?: IContactLanguage[];
}

type NotificationMethod = {
	email: boolean;
	sms: boolean;
};

type ContactSettings = {
	timeZone: string;
	notificationMethod: NotificationMethod;
};

interface IContactDetail extends IContact {
	relationshipAccessType: RelationshipAccessType;
	schema?: IProviderContactFieldSettings;
	settings?: ContactSettings;
}

interface IContactsPaginatedResponse {
	result: IPaginatedContactsResult;
	isInitialLoad: boolean;
	personId: string;
	hasMoreOverride?: boolean;
	clientFilter?: ClientFilter;
}

type ClientSortingFieldName = 'firstName';

interface IClientSortingField {
	field: ClientSortingFieldName;
	direction: SortDirection;
}

interface IClientFilterState {
	searchTerm: string;
	tags: IFilterOption[];
	staff: IFilterOption[];
	statuses?: IFilterOption[];
	status: ClientStatusFilter;
	sort: IClientSortingField;
}

interface IContactFilterState {
	searchTerm: string;
	tags?: ITag[];
	staff?: IStaffMember[];
	statuses?: IFilterOption[];
	status?: ClientStatusFilter;
	sort: IClientSortingField;
	columns: ColumnType[];
	skipFetch?: boolean;
}

interface IBulkContactOptions {
	selectAll: boolean;
	selectedContactIds: string[];
	excludedContactIds: string[];
}

type ClientFiler = 'clients' | 'contacts' | 'all';

type ClientStatusFilter = 'All' | 'Active' | 'Inactive';

interface IFetchContactsRequest {
	userId?: string;
	isInitialLoad?: boolean;
	searchTerm?: string;
	clientFilter: ClientFilter;
}

interface IFetchAssignedContactsRequest {
	personId: string;
	isInitialLoad?: boolean;
	searchTerm?: string;
}

interface IFetchContactResponse {
	personId: string;
	result: IContactDetail;
}

interface ISetContactSelectorSearchRequest {
	searchTerm: string;
	isInitialLoad: boolean;
}

interface ISetContactSearchTerm {
	searchTerm: string;
	clientFilter: ClientFilter;
}

interface IBulkContactFilter {
	searchTerm: string;
	tag: string[];
	assignedStaff: string[];
	statuses: string[];
	excludedContactIds: string[];
	allClients?: boolean; // if set to true, *Request.contactIds will be ignored
	isArchived?: boolean;
	isUnassigned?: boolean;
}

interface IAssignStaffToContactsRequest {
	contactIds: string[];
	staffIds: string[];
	bulkContactFilter: IBulkContactFilter;
	isClient: boolean;
}

interface IAssignStaffToContactsResponse {
	contactIds: string[];
	staffIds: string[];
	contacts: IContact[]; // @quim-carepatron: Remove this after RTK migration
	staff: ISimplePerson[]; // @quim-carepatron: Remove this after RTK migration
}

interface IUnassignStaffToContactsResponse {
	contactIds: string[];
	staff: ISimplePerson[];
}

interface IBulkUpdateContactsStatus {
	contactIds: string[];
	isArchived: boolean;
	status: string;
	bulkContactFilter: IBulkContactFilter;
	isClient: boolean;
}

interface IBulkDeleteContacts {
	contactIds: string[];
	bulkContactFilter: IBulkContactFilter;
	isClient: boolean;
}

interface IAddTagsToContactsRequest {
	contactIds: string[];
	tagIds: string[];
	bulkContactFilter: IBulkContactFilter;
	isClient: boolean;
}
interface IAddTagsToContactsResponse {
	contactIds: string[];
	tagIds: string[];
	tags?: ITag[]; // @quim-carepatron: Remove this after RTK migration
}

interface ISimpleContact {
	id: string;
	firstName: string;
	lastName: string;
}

interface IContactReference extends ISimpleContact {
	fullName: string;
	email: string;
	phoneNumber?: string | null;
	profilePhoto?: string | null;
}

interface ISendClientIntake {
	providerId: string;
	contactId: string;
	email: string;
	subject: string;
	body: string;
	setTemplatesAsIntakeDefault: boolean;
	consentTemplates: string[];
	capturePaymentDetails: boolean;
}

interface ISendInviteToPortal {
	providerId: string;
	contactId: string;
	email: string;
	subject: string;
	body: string;
}

interface ISendInviteToPortalSuccess {
	contactId: string;
	email: string;
}

interface IContactImportFileResult {
	columnNames: string[];
	rows: Array<IDictionary<string, string>>;
	bestMatchColumnNames: IDictionary<string, string>;
	bestMatchSchemaFields: IDictionary<string, string>;
}

interface IContactImportRequest {
	providerId: string;
	file: File;
	mappedColumns: IContactImportOption[];
	dataSchemaJson: ISchema;
	importSource: string;
	importSourceName: string;
}

interface IContactImportOption {
	carepatronFieldName: string;
	spreadsheetFieldName?: string;
	spreadsheetMultipleFieldNames?: string[];
	fieldOptions: ImportContactsFieldOption;
	delimiter?: string;
	isMultiple: boolean;
	dateFormat?: DateFormat;
	countryCode?: string;
}

type ImportContactsFieldOption = 'WholeField' | 'FirstPart' | 'MiddlePart' | 'LastPart';

type DateFormat = 'DDMMYYYY' | 'MMDDYYYY' | 'YYYYMMDD';

interface IContactImportFieldOptionSelectorProps {
	spreadsheetFieldName: string;
	fieldOption: ImportContactsFieldOption;
	examples: IDictionary<any>;
}

interface IFetchContactTasksRequest extends Omit<IFetchTasksRequest, 'contactId'> {
	contactId: string;
}

interface IFetchContactTasksResponse extends IPaginatedResult<ITask> {
	contactId: string;
}

interface IManageStatusRequest {
	dataSchemaId: string;
	fieldId: string;
	property: IOptionSetPropertyV2;
	deletedStatuses: string[];
	statusChangeMap: Dictionary<string, string>;
}

interface IManageStatusResponse {
	conflict: bool;
	conflictRecords: Dictionary<string, number>;
	dataSchemaId: string;
	fieldId: string;
	property: IOptionSetPropertyV2;
	statusChangeMap: Dictionary<string, string>;
}

type ContactImportSource =
	| 'SimplePractice'
	| 'CSV'
	| 'AthenaHealth'
	| 'AI_CSV'
	| 'AI_SimplePractice'
	| 'AI_AthenaHealth';

interface IIcdCode {
	code: string;
	description: string;
}

interface IContactDiagnosis {
	timeZone: string;
	diagnosisDateUtc: string;
	icdCodes: IIcdCode[];
	diagnosisTimeZonedDate: string;
}
