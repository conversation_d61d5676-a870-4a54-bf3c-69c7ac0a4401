type ModalType =
	| 'Confirmation'
	| 'CreateClient'
	| 'CreateContact'
	| 'CreateStaff'
	| 'AddStaffRelationships'
	| 'AddStaffClientRelationships'
	| 'AddFamilyClientRelationship'
	| 'AddClientOwnerRelationship'
	| 'CreateCall'
	| 'AddClientFiles'
	| 'EditContactDetails'
	| 'EditPersonDetails'
	| 'EditProviderDetails'
	| 'EditPassword'
	| 'AssignStaff'
	| 'ManageTags'
	| 'EditClientTags'
	| 'CreateTask'
	| 'EditTask'
	| 'CreateService'
	| 'CreateServiceGroup'
	| 'EditServiceGroup'
	| 'AddServiceToGroup'
	| 'EditRecurringTaskConfirmation'
	| 'DeleteRecurringTaskConfirmation'
	| 'CreateLocation'
	| 'NotifyAttendeesOfTask'
	| 'DocuViewerInvoice'
	| 'CreateInvoice'
	| 'EmailInvoice'
	| 'ExportInvoices'
	| 'SendClientIntake'
	| 'InviteToPortal'
	| 'SyncCalendar'
	| 'ConnectZoom'
	| 'EditSubscriptionPaymentDetails'
	| 'CreateWorkspace'
	| 'VerifyEmail'
	| 'ImportClients'
	| 'ExportClients'
	| 'AssignContacts'
	| 'CreateSuperbillReceipt'
	| 'EmailSuperbillReceipt'
	| 'TransferOwnership'
	| 'BookingLink'
	| 'DeletePracticeWorkspace'
	| 'PreviewTemplate'
	| 'ShareWith'
	| 'CancelSubscription'
	| 'OpenMeeting'
	| 'PreviewDocument'
	| 'PaymentMethod'
	| 'PublishTemplate'
	| 'ShareTemplateAsPublicForm'
	| 'PreviewFile'
	| 'EditorLink'
	| 'ConnectInbox'
	| 'ManageConnections'
	| 'MediaGallery'
	| 'ManageStatuses'
	| 'TransferStatus'
	| 'UnlockNote'
	| 'PotentialClientDuplicateModal'
	| 'CallWaitingPrompt'
	| 'ImageEditor'
	| 'ManageReferrals'
	| 'ServiceWorkerForceUIUpdate'
	| 'CreateTaxRate'
	| 'CreateNewInbox'
	| 'ManageIgnoreSenders'
	| 'EmailHasIgnoredRecipients'
	| 'DeleteTrashItems'
	| 'RestoreTrashItems'
	| 'ManageContactInsurancePolicy'
	| 'ContactRelationship'
	| 'DeleteRestorableItem'
	| 'ExportCalendar'
	| 'DeleteAppointment'
	| 'TotpSetup'
	| 'SeparateDuplicateClients'
	| 'MergeClientRecords'
	| 'NewClientNextSteps'
	| 'NewContactNextSteps'
	| 'NewTeamMemberNextSteps'
	| 'LinkClient'
	| 'TypeToConfirm'
	| 'UpdateEmailWarning'
	| 'CreateTemplateFolder'
	| 'MoveTemplateToFolder'
	| 'ExportPayments'
	| 'ExportClaims'
	| 'PersonalizeWorkspace'
	| 'SuggestedServices'
	| 'SuggestedLocations'
	| 'RevertClaimStatus'
	| 'NewChatGroup'
	| 'ImportClientsActivity';

interface IModalData<T> {
	id?: string;
	type: ModalType;
	data?: T;
	isLoading?: boolean;
}

interface IModalsState {
	currentModals: IModalData<any>[];
	confirmation: IConfirmationModal | null;
	isConfirming: boolean;
}

interface AsyncConfirmationAction {
	action?: () => any;
	successActionType?: string;
	failureActionType?: string;
	timeoutMs?: number;
}

type PrimaryActionConfirmationColor = 'inherit' | 'primary' | 'secondary' | 'success' | 'error' | 'info' | 'warning';

interface IConfirmationModal {
	titleIcon?: JSX.Element;
	titleMessageId?: string;
	titleMessageValues?: { [key: string]: MessageValue | JSX.Element };
	descriptionMessageId?: string;
	descriptionMessageValues?: { [key: string]: MessageValue | JSX.Element };
	action?: () => any | Promise<any>;
	onSuccess?: () => void;
	onFailure?: (error) => void;
	asyncActions?: AsyncConfirmationAction[];
	primaryActionMessageId?: string;
	primaryActionColor?: PrimaryActionConfirmationColor;
	secondaryActionMessageId?: string;
	secondaryAction?: () => void;
	tertiaryActionMessageId?: string;
	tertiaryAction?: () => void;
	confirmationInputLabelId?: string;
	confirmationInputLabelValues?: {
		[key: string]: MessageValue | JSX.Element;
	};
	confirmationValue?: string;
	alignAllActionButtonsRight?: boolean;
	onClose?: () => void;
}

interface IModalComponentBaseProps {
	key: string;
	id: ModalType;
	open: boolean;
	onClose: () => void;
}
