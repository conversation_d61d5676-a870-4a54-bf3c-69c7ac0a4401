import { NotificationCategoryValue } from './notifications';

/**
 * List of event types for app notifications
 *
 * Note: Will be updated gradually to reflect new event types that need to have specific handling in the UI
 */
export type AppNotificationEventType =
	// SCHEDULING
	| 'AppointmentAssigned'
	| 'AppointmentCancelled'
	| 'AppointmentRescheduled'
	| 'AppointmentConfirmed'
	| 'ClientAppointmentBooked'
	// CLIENTS AND DOCUMENTATION
	| 'FileUploaded'
	| 'NoteFormSubmitted'
	| 'ClientAddedNote'
	| 'ClientUpdatedNote'
	| 'PublicFormSubmitted'
	| 'TemplateImportCompleted'
	| 'TemplateImportFailed'
	| 'PreprocessImportContactCompleted'
	// BILLING & PAYMENT
	| 'InvoicePaid'
	| 'SubscriptionPaymentFailed'
	| 'PaymentsAccountDisabled'
	| 'ServiceReceiptRequiresReview'
	| 'PayerEnrollmentRejected'
	| 'InsuranceClaimRejected'
	| 'InsuranceClaimPaid'
	| 'InsuranceClaimPartiallyPaid'
	| 'InsuranceClaimDenied'
	// WORKSPACE
	| 'ReferralCredited'
	| 'ReferralJoined'
	| 'ImportContactFailed'
	| 'ExportContactFailed'
	| 'ConnectedAppDisconnected'
	| 'StaffContactAssigned'
	// COMMUNICATIONS
	| 'StaffInboxAssigned'
	| 'StaffInboxUnssigned';

/**
 * List of IDs for system notifications that would be used for "actorId" field
 */
export type SystemNotificationIds =
	| 'SYSTEM:IMPORT'
	| 'SYSTEM:EXPORT'
	| 'SYSTEM:INVOICE'
	| 'SYSTEM:REFERRAL'
	| 'SYSTEM:SUBSCRIPTION'
	| 'SYSTEM:CONNECTEDAPP';

export interface AppNotificationActor {
	name?: string;
	email?: string;
	profilePhotoId?: string;
}

export enum AppNotificationStatus {
	Default = 0,
	Ignored = 1,
}
export interface AppNotificationItem {
	id: string;
	parameters: Array<{ key: string; value: any }>;
	eventType: AppNotificationEventType;
	eventCategory: NotificationCategoryValue;
	eventVersion: string;
	actorId: string;
	isRead: boolean;
	status: AppNotificationStatus;
	createdAt: Date;
}

export enum AppNotificationFilterTypes {
	All = 0,
	Unread = 1,
	Read = 2,
	Ignored = 3,
}
