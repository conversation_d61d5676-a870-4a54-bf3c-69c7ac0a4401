import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import { PeopleAltRounded } from '@mui/icons-material';
import AddIcon from '@mui/icons-material/Add';
import { Box, Button } from '@mui/material';

import { useBreakpoint } from '@carepatron/components';

import PageHeader from 'components/common/PageHeader';
import { ContentFitButton } from 'components/ContentFitButton';
import langIds from 'lang/langIds';
import { useHasPermission } from 'store/api/permissions/hooks';
import { addModal } from 'store/slices/modals/slice';

import MoreActionButton from './MoreActionButton';

const ClientListHeader = () => {
	const dispatch = useDispatch();

	const belowSm = useBreakpoint('sm');

	const hasViewWorkspaceSettingsPermission = useHasPermission('workspaceSettingsView');

	const onNewClient = () =>
		dispatch(
			addModal({
				type: 'CreateClient',
				data: {
					enableNextSteps: true,
				},
			})
		);

	const NewClientButton = belowSm ? Button : ContentFitButton;

	return (
		<PageHeader
			title={<FormattedMessage id={langIds.Clients} />}
			icon={<PeopleAltRounded color='action' />}
			renderActions={
				<Box display='flex' flexDirection='row' gap={1} sx={{ width: '100%' }}>
					{hasViewWorkspaceSettingsPermission && <MoreActionButton />}
					<NewClientButton
						id='client-list-new-button'
						startIcon={<AddIcon />}
						onClick={onNewClient}
						variant='contained'
						color='primary'
						fullWidth={belowSm}
					>
						<FormattedMessage id={langIds.NewClient} />
					</NewClientButton>
				</Box>
			}
		/>
	);
};

export default ClientListHeader;
