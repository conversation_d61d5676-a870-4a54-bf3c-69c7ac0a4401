import { type FC, MouseEvent, useRef, useState } from 'react';
import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import MergeTypeRoundedIcon from '@mui/icons-material/MergeTypeRounded';
import { Button, ButtonGroup, ListItemIcon, Menu, MenuItem } from '@mui/material';
import { ClientImportSummaryWrapper } from 'features/Client/components/ClientImportSummaryWrapper';

import { useBreakpoint } from '@carepatron/components';

import langIds from 'lang/langIds';
import { addModal } from 'store/slices/modals/slice';
import { routes } from 'util/routes';

const MoreActionButton: FC = () => {
	const dispatch = useDispatch();
	const navigate = useNavigate();
	const belowSm = useBreakpoint('sm');

	const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

	const dropdownButtonRef = useRef<HTMLDivElement>(null);

	const handleImportClick = (callback?: () => void) => () => {
		setAnchorEl(null);
		!!callback ? callback() : dispatch(addModal('ImportClients'));
	};

	const handleMergeClick = () => navigate(`${routes('Clients')}/Duplicates`);

	const handleOpen = (e: MouseEvent<HTMLButtonElement>) => {
		e.stopPropagation();
		setAnchorEl(dropdownButtonRef.current);
	};

	const handleClose = (e: MouseEvent<HTMLButtonElement>) => {
		e.stopPropagation();
		setAnchorEl(null);
	};

	// Client import is not supported on mobile
	if (belowSm) {
		return (
			<Button fullWidth variant='outlined' startIcon={<MergeTypeRoundedIcon />} onClick={handleMergeClick}>
				<FormattedMessage id={langIds.Merge} />
			</Button>
		);
	}

	return (
		<>
			<ClientImportSummaryWrapper>
				{({ isReadyForMapping, importButtonIcon, importButtonText, onImportClients }) => (
					<ButtonGroup
						variant='outlined'
						color={isReadyForMapping ? 'success' : 'primary'}
						ref={dropdownButtonRef}
					>
						<Button
							id='client-list-import-button'
							startIcon={importButtonIcon}
							onClick={handleImportClick(onImportClients)}
						>
							{importButtonText}
						</Button>
						<Button onClick={handleOpen}>
							<ArrowDropDownIcon />
						</Button>
					</ButtonGroup>
				)}
			</ClientImportSummaryWrapper>
			<Menu
				open={Boolean(anchorEl)}
				id='client-list-more-menu'
				anchorEl={anchorEl}
				onClose={handleClose}
				MenuListProps={{
					'aria-labelledby': 'client-list-more-button',
					sx: { width: dropdownButtonRef.current?.clientWidth },
				}}
			>
				<MenuItem onClick={handleMergeClick}>
					<ListItemIcon>
						<MergeTypeRoundedIcon />
					</ListItemIcon>
					<FormattedMessage id={langIds.Merge} />
				</MenuItem>
			</Menu>
		</>
	);
};

export default MoreActionButton;
