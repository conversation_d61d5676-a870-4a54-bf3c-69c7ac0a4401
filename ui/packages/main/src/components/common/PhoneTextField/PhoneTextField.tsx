import React, { useEffect } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Box, Divider, InputAdornment, MenuItem, Popover } from '@mui/material';
import TextField, { TextFieldProps } from '@mui/material/TextField';
import parsePhoneNumber, { CountryCode } from 'libphonenumber-js';

import { useCurrentProvider } from 'store/api/providers/hooks';
import { phoneCountryCodes, phoneCountryCodeToFlag } from 'util/phoneCountryCodes';
import { getPhoneCountryCode, getPhoneWithoutCountryCode, ICountryPhoneState } from 'util/phoneNumber';
import { useEffectEvent } from 'util/useEffectEvent';

import MaskedPhoneTextField from './MaskedPhoneTextField';
import { getPhoneNumberPlaceholder, getPhoneNumberTemplate } from './utilities';

interface CountryType {
	code: string;
	label: string;
	phone: string;
}

export type PhoneTextFieldProps = Partial<TextFieldProps> & {
	onValueChange: (value: string) => void;
	defaultValue?: string;
	value?: string;
	onCountryCodeChange?: (countryCode: string) => void;
	defaultCountryCode?: string;
	readOnly?: boolean;
	useMask?: boolean;
	mask?: string;
};

function getCountry(countryCallingCode: string): ICountryPhoneState | undefined {
	const contry = phoneCountryCodes.find(({ code }) => code === countryCallingCode);
	if (!contry) return undefined;
	return { country: contry.code, callingCode: contry.phone };
}

function PhoneTextField(props: PhoneTextFieldProps) {
	const {
		onValueChange,
		value,
		defaultValue,
		readOnly,
		defaultCountryCode,
		onCountryCodeChange,
		useMask = true,
		mask,
		...restProps
	} = props;
	const { currentProvider } = useCurrentProvider();
	const countryCode = defaultCountryCode ?? currentProvider?.countryCode;

	console.log('=== value ===', value);

	const [country, setCountry] = React.useState<ICountryPhoneState>(getPhoneCountryCode(defaultValue, countryCode));
	const [anchorEl, setAnchorEl] = React.useState<HTMLElement | null>(null);
	const refs = phoneCountryCodes.reduce((acc, value) => {
		acc[value.code] = React.createRef();
		return acc;
	}, {});
	const [countrySearch, setCountrySearch] = React.useState('');
	const [timeoutId, setTimeoutId] = React.useState(0);
	const [formattedValue, setFormattedValue] = React.useState(getPhoneWithoutCountryCode(defaultValue));

	const handleCountryCodeChange = useEffectEvent((country: ICountryPhoneState) => {
		setCountry(country);
		onCountryCodeChange?.(country.country);
	});

	const handleClick = (event: React.MouseEvent<any>) => {
		setAnchorEl(event.currentTarget);
	};

	const handleClose = () => {
		setAnchorEl(null);
	};

	const open = Boolean(anchorEl);
	const id = open ? 'phone-selector-poppover' : undefined;

	const Item = ({ index, style, selected }) => (
		<MenuItem
			data-testid={`phone-field-popover-country-${phoneCountryCodes[index].code}`}
			key={phoneCountryCodes[index].code}
			style={style}
			ref={refs[phoneCountryCodes[index].code]}
			value={`+${phoneCountryCodes[index].phone}`}
			selected={selected}
			onClick={handleItemClick(phoneCountryCodes[index])}
			aria-selected={selected}
		>
			<React.Fragment>
				<span style={{ paddingRight: 6 }}>{phoneCountryCodeToFlag(phoneCountryCodes[index].code)}</span>
				{phoneCountryCodes[index].label} (+{phoneCountryCodes[index].phone})
			</React.Fragment>
		</MenuItem>
	);

	const downHandler = (event) => {
		if (!open) return;

		const char = String.fromCharCode(event.keyCode);
		if (!char) return;

		const newCountrySearch = (countrySearch + char).toLowerCase();
		setCountrySearch(newCountrySearch);
		if (timeoutId) {
			clearTimeout(timeoutId);
		}
		const newTimeoutId = setTimeout(() => setCountrySearch(''), 333);
		setTimeoutId(newTimeoutId as any);

		const first = phoneCountryCodes.find((x) => x.label.toLowerCase().startsWith(newCountrySearch));

		if (!first) return;

		const firstRef = refs[first.code];

		firstRef.current.scrollIntoView({
			behavior: 'smooth',
			block: 'start',
		});
	};

	const handleItemClick = (country: CountryType) => () => {
		handleCountryCodeChange({ country: country.code, callingCode: country.phone });
		if (formattedValue) {
			onValueChange(`+${country.phone}${formattedValue}`);
		}
		handleClose();
	};

	const handleChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
		const { value } = evt.target;
		const phoneNumber = parsePhoneNumber(value, country.country as CountryCode);

		const nationalNumber = phoneNumber ? phoneNumber?.nationalNumber.toString() || '' : value;
		setFormattedValue(nationalNumber);

		const phoneWithoutCode = phoneNumber ? phoneNumber?.number.toString()?.trim() || '' : value;

		if (props.onChange) {
			props.onChange({ ...evt, target: { ...evt.target, value: phoneWithoutCode } });
		}

		onValueChange(phoneWithoutCode);
	};

	useEffect(() => {
		if (countryCode) {
			const country = getCountry(countryCode);
			country && handleCountryCodeChange(country);
		}
	}, [countryCode, handleCountryCodeChange]);

	useEffect(() => {
		if (value === undefined) return;
		handleCountryCodeChange(getPhoneCountryCode(value, countryCode));
		setFormattedValue(getPhoneWithoutCountryCode(value));
	}, [value, countryCode, handleCountryCodeChange]);

	return (
		<div onKeyDown={downHandler}>
			<TextField
				{...restProps}
				value={formattedValue}
				data-testid='phone-field'
				inputProps={{ name: 'phone', ...props.inputProps }}
				onChange={handleChange}
				InputProps={{
					inputComponent: useMask ? MaskedPhoneTextField : undefined,
					inputProps: {
						...restProps.inputProps,
						...(useMask
							? {
									country,
									mask,
									placeholder: getPhoneNumberPlaceholder(
										country.country as CountryCode,
										country.callingCode
									),
									template: getPhoneNumberTemplate(
										country.country as CountryCode,
										country.callingCode
									),
								}
							: {}),
					},
					startAdornment: (
						<InputAdornment
							sx={{
								minHeight: 30,
								appearance: 'none',
								border: '0',
								background: 'transparent',
								display: 'inline-flex',
								paddingLeft: 1.75,
								paddingRight: 0,
								cursor: 'pointer',
								marginLeft: -1.75,
								marginRight: 0,
							}}
							data-testid='phone-field-dropdown-btn'
							component='button'
							type='button'
							position='start'
							onClick={handleClick}
							disabled={readOnly}
							disablePointerEvents={readOnly}
						>
							<Box
								component='span'
								sx={{
									display: 'flex',
									alignItems: 'center',
									color: 'text.primary',
									fontSize: 14,
									lineHeight: '24px',
								}}
							>
								{phoneCountryCodeToFlag(country.country)}
								<Box component='span' data-testid='phone-field-country' ml={0.5}>
									{`+${country.callingCode}`}
								</Box>
							</Box>
							<ArrowDropDownIcon />
						</InputAdornment>
					),
					readOnly: !!readOnly,
				}}
				sx={{
					'& .MuiInputBase-root.Mui-readOnly.Mui-focused': {
						borderColor: 'rgba(0, 0, 0, 0.3)',
					},
					'& .MuiInputBase-root.Mui-readOnly:hover': {
						borderColor: 'rgba(0, 0, 0, 0.3)',
					},
				}}
				type='text'
			/>
			<Popover
				id={id}
				open={open}
				anchorEl={anchorEl}
				data-testd='phone-field-popover'
				onClose={handleClose}
				anchorOrigin={{
					vertical: 'bottom',
					horizontal: 'left',
				}}
				transformOrigin={{
					vertical: 'top',
					horizontal: 'left',
				}}
				sx={{ marginTop: 0.5 }}
			>
				<ol style={{ maxHeight: 260, maxWidth: 400, paddingLeft: 0 }}>
					<Item
						index={phoneCountryCodes.findIndex((x) => x.code === (countryCode || country.country))}
						selected={country.country === (countryCode || country.country)}
						style={{}}
					/>
					<Divider />
					{phoneCountryCodes.map((x, i) =>
						Item({ index: i, selected: x.code === country.country, style: {} })
					)}
				</ol>
			</Popover>
		</div>
	);
}

export default React.memo(PhoneTextField);
