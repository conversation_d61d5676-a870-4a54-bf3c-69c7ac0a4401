import { FC, ReactNode, useCallback, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFileRounded';
import { Box, CircularProgress, ListItem, ListItemIcon, ListItemProps, ListItemText, Typography } from '@mui/material';
import makeStyles from '@mui/styles/makeStyles';

import { bytesToSize } from 'components/modals/ImportClients/utils';
import langIds from 'lang/langIds';

const useStyles = makeStyles((theme) => ({
	listItem: {
		borderRadius: 4,
		paddingTop: theme.spacing(0.5),
		paddingBottom: theme.spacing(0.5),
	},
	circularProgress: {
		color: theme.palette.primary.main,
		marginTop: 'auto',
		marginBottom: 'auto',
	},
	circularProgressLabel: {
		top: 0,
		left: 0,
		bottom: 0,
		right: 0,
		position: 'absolute',
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		color: theme.palette.text.secondary,
	},
}));

type CircularProgressWithLabelProps = {
	value?: number;
};

const CircularProgressWithLabel: FC<CircularProgressWithLabelProps> = ({ value }) => {
	const classes = useStyles();

	if (value === undefined) {
		return <CircularProgress size={24} className={classes.circularProgress} />;
	}

	return (
		<Box position='relative' display='inline-flex'>
			<CircularProgress variant='determinate' value={value} size={32} className={classes.circularProgress} />
			<Typography className={classes.circularProgressLabel} fontSize={9} component='div'>
				<FormattedMessage id={langIds.CircularProgressWithLabel} values={{ value: Math.round(value) }} />
			</Typography>
		</Box>
	);
};

export interface IFileUploadListItemProps extends ListItemProps {
	fileName: string;
	fileSize?: number;
	loading?: boolean;
	error?: boolean;
	success?: boolean;
	progress?: number;
	loadingMessage?: string;
	successMessage?: string;
	errorMessage?: string;
	truncateFileName?: boolean;
	actions?: ReactNode[];
	loadingIcon?: JSX.Element;
	successIcon?: JSX.Element;
}

export const FileUploadListItem: FC<IFileUploadListItemProps> = ({
	fileName,
	fileSize,
	loading,
	success,
	error,
	progress,
	loadingMessage,
	successMessage,
	errorMessage,
	truncateFileName = true,
	actions,
	loadingIcon,
	successIcon,
	...props
}) => {
	const classes = useStyles();
	const { formatMessage } = useIntl();
	const hasFileSize = fileSize !== undefined && !isNaN(fileSize);

	const [listItemTextWidth, setListItemTextWidth] = useState(0);

	const renderStatusIcon = (): { icon: JSX.Element | null; status: string } => {
		if (error) {
			return {
				icon: null,
				status: formatMessage({ id: langIds.FileUploadFailed }),
			};
		}
		if (success) {
			return {
				icon: successIcon ?? <CheckCircleIcon color='success' />,
				status: successMessage ?? formatMessage({ id: langIds.FileUploadComplete }),
			};
		}

		if (loading) {
			return {
				icon: loadingIcon ?? <CircularProgressWithLabel value={progress} />,
				status: loadingMessage ?? formatMessage({ id: langIds.FileUploadInProgress }),
			};
		}

		return {
			icon: null,
			status: '',
		};
	};

	const listItemTextRef = useCallback((node) => {
		if (!node) return;
		setListItemTextWidth(node.offsetWidth);
	}, []);

	const { icon, status } = renderStatusIcon();

	return (
		<ListItem className={classes.listItem} {...props}>
			<ListItemIcon sx={{ minWidth: 0, marginRight: 1.5 }} color='error'>
				<InsertDriveFileIcon color={error ? 'error' : 'primary'} />
			</ListItemIcon>
			<ListItemText
				ref={listItemTextRef}
				primary={truncateFileName ? truncateMiddle(fileName, listItemTextWidth / 8) : fileName}
				secondary={
					<>
						{errorMessage ? errorMessage : hasFileSize ? bytesToSize(fileSize ?? 0) : ''}
						{!!status && hasFileSize && <span style={{ marginLeft: 8, marginRight: 8 }}>•</span>}
						{status}
					</>
				}
				sx={{ mr: 6 }}
				primaryTypographyProps={{ color: error ? 'error' : 'text.primary' }}
				secondaryTypographyProps={{ color: error ? 'error' : 'text.secondary' }}
			/>

			{actions}

			{icon && <ListItemIcon sx={{ minWidth: 0, paddingLeft: 1.5 }}>{icon}</ListItemIcon>}
		</ListItem>
	);
};

const truncateMiddle = (word: string, maxLength: number = Infinity) => {
	if (word.length <= maxLength || maxLength === 0) return word;
	const start = word.slice(0, maxLength / 2);
	const end = word.slice(-maxLength / 2);
	return `${start}...${end}`;
};
