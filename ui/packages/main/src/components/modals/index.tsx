import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import CreateChatGroupModal from 'features/Chat/CreateChatGroupModal';
import { NewContactNextStepsModal } from 'features/Contact/modals/NewContactNextSteps';
import { NewTeamMemberNextStepsModal } from 'features/Staff/modals/NewTeamMemberNextSteps';

import PreviewDocument from 'areas/PreviewDocument';
import ImageEditorMobileModal from 'components/EditorV2/Core/extensions/image/components/ImageEditor/MobileDialog';
import CreateDocuments from 'components/modals/CreateDocuments';
import PreviewFile from 'components/modals/PreviewFile';
import { TotpSetupModal } from 'components/modals/TotpSetup';
import PaymentMethod from 'components/PaymentMethodModal';
import PreviewTemplate from 'components/PreviewTemplateModal';
import { router } from 'components/Router';
import CreateTemplateFolder from 'components/TemplateFolder/CreateTemplateFolder';
import MoveTemplateToFolder from 'components/TemplateFolder/MoveTemplateToFolder';
import { selectModals } from 'store/slices/modals/selectors';
import { removeAllModals, removeModal } from 'store/slices/modals/slice';
import { usePrevious } from 'util/hooks';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { lazyWithPreload } from 'util/routes';

import { LinkClient } from './LinkClient/LinkClient';
import AddServiceToGroup from './AddServiceToGroup';
import AssignContacts from './AssignContacts';
import AssignStaffModal from './AssignStaff';
import BookingLink from './BookingLink';
import CallWaitingPrompt from './CallWaitingPrompt';
import CancelSubscription from './CancelSubscription';
import ConfirmationModal from './Confirmation';
import ConnectInbox from './ConnectInbox';
import ConnectZoom from './ConnectZoom';
import ContactRelationship from './ContactRelationship';
import CreateCall from './CreateCall';
import CreateClientModal from './CreateClient';
import CreateContactModal from './CreateContact';
import CreateInvoice from './CreateInvoice';
import CreateLocationModal from './CreateLocation';
import { CreateNewInboxModal } from './CreateNewInboxModal';
import CreateService from './CreateService';
import CreateServiceGroup from './CreateServiceGroup';
import CreateStaffModal from './CreateStaffModal';
import CreateSuperbillReceipt from './CreateSuperbillReceipt';
import CreateTask from './CreateTask';
import CreateTaxRate from './CreateTaxRate';
import CreateWorkspace from './CreateWorkspace';
import { DeleteAppointment } from './DeleteAppointment';
import DeletePracticeWorkspace from './DeletePracticeWorkspace';
import DeleteRecurringTaskConfirmation from './DeleteRecurringTaskConfirmation';
import DeleteRestorableItemModal from './DeleteRestorableItem';
import DeleteTrashItemsModal from './DeleteTrashItems';
import EditClientTags from './EditClientTags';
import EditContactDetails from './EditContactDetails';
import EditorLink from './EditorLink';
import EditPasswordModal from './EditPassword';
import EditProviderDetails from './EditProviderDetails';
import EditRecurringTaskConfirmation from './EditRecurringTaskConfirmation';
import EditSubscriptionPaymentDetails from './EditSubscriptionPaymentDetails';
import EditTask from './EditTask';
import { EmailHasIgnoredRecipientsModal } from './EmailHasIgnoredRecipients';
import EmailInvoice from './EmailInvoice';
import EmailSuperbillReceipt from './EmailSuperbillReceipt';
import ExportCalendar from './ExportCalendar';
import { ExportClaimsModal } from './ExportClaims';
import ExportClients from './ExportClients';
import ExportInvoices from './ExportInvoices';
import { ExportPaymentsModal } from './ExportPayments';
import ImportClients from './ImportClients';
import { ImportClientsActivity } from './ImportClientsActivity';
import InviteToPortal from './InviteToPortal';
import ManageConnections from './ManageConnections';
import ManageContactInsurancePolicy from './ManageContactInsurancePolicy';
import { ManageIgnoreSendersModal } from './ManageIgnoreSenders';
import ManageReferrals from './ManageReferrals';
import ManageStatuses from './ManageStatuses';
import ManageTags from './ManageTags';
import MediaGallery from './MediaGallery';
import MergeClientRecords from './MergeClientRecords';
import NewClientNextSteps from './NewClientNextSteps';
import NotifyAttendeesOfTaskModal from './NotifyAttendeesOfTask';
import PersonalizeWorkspace from './PersonalizeWorkspace';
import PotentialClientDuplicateModal from './PotentialClientDuplicateModal';
import PublishTemplate from './PublishTemplate';
import RestoreTrashItemsModal from './RestoreTrashItems';
import { RevertClaimStatusModal } from './RevertClaimStatus';
import SendClientIntake from './SendClientIntake';
import SeparateDuplicateClients from './SeparateDuplicateClients';
import ServiceWorkerForceUIUpdate from './ServiceWorkerForceUIUpdate';
import { ShareTemplateAsPublicFormModal } from './ShareTemplateAsPublicFormModal';
import ShareWith from './ShareWith';
import SuggestedLocations from './SuggestedLocations';
import SuggestedServices from './SuggestedServices';
import SyncCalendar from './SyncCalendar';
import TransferOwnershipModal from './TransferOwnership';
import TransferStatus from './TransferStatus';
import TypeToConfirmModal from './TypeToConfirmModal';
import UnlockNote from './UnlockNote';
import { UpdateEmailWarning } from './UpdateEmailWarning';
import VerifyEmail from './VerifyEmail';

const Meeting = lazyWithPreload(() => import('../../areas/Call/Meeting'));

export interface IModalsRootProps {}

function ModalsRoot(props: IModalsRootProps): JSX.Element | null {
	const isSmallScreen = useBreakpoint('md');
	const location = useLocation();
	const navigate = useNavigate();
	const dispatch = useDispatch();

	const currentModals = useSelector(selectModals);

	const prevModalCount = usePrevious(currentModals.length);

	const removeModals = useCallback(() => {
		dispatch(removeAllModals());
	}, [dispatch]);

	// In mobile, the modals take up the full screen so
	// duplicate router/changeLocation entry so that when the user navigates back,
	// the modals are removed, but the current screen persists
	useEffect(() => {
		// Conditions:
		// `currentModals.length > (prevModalCount || 0)` - makes sure to only track modal additions
		// `currentModals.length === 1` - makes sure there's only ONE extra copy of location in the history
		const hasThereBeenANewModal = currentModals.length > (prevModalCount || 0);
		if (isSmallScreen && hasThereBeenANewModal && currentModals.length === 1) {
			navigate(location);
		}
	}, [currentModals.length, isSmallScreen, location, navigate, prevModalCount]);

	// Remove modals when the user navigates back
	useEffect(() => {
		const unsubscribe = router.subscribe((state) => {
			if (state.historyAction === 'POP' && currentModals.length) {
				removeModals();
			}
		});
		return () => {
			unsubscribe();
		};
	}, [currentModals.length, removeModals]);

	/**
	 * Renders the meeting modal if present in the modal stack.
	 * OpenMeeting requires special handling because:
	 * 1. It manages persistent video/audio streams and WebRTC connections
	 * 2. It should not be subject to the index-based `open` prop logic (always open when present)
	 * 3. Toggling `open={false}` would break the Chime SDK state and corrupt the meeting
	 */
	const renderMeetingModal = () => {
		const meetingModal = currentModals.find(
			(modal) => (typeof modal === 'object' ? modal.type : modal) === 'OpenMeeting'
		);

		if (!meetingModal) return null;

		const meetingData = typeof meetingModal === 'object' ? meetingModal.data || {} : {};
		return <Meeting key='OpenMeeting' open {...meetingData} />;
	};

	return (
		<>
			{/* Render all modals except OpenMeeting with standard index-based logic */}
			{currentModals.map((modal, index) => {
				// Skip OpenMeeting - it's handled separately below
				if (modal.type === 'OpenMeeting') return null;

				const type = typeof modal === 'object' ? modal.type : modal;
				const data = typeof modal === 'object' ? modal.data : null;

				// Only the first modal (index 0) is open, others are closed
				return <ModalComponent key={type} type={type} open={index === 0} data={data} />;
			})}

			{/* Render OpenMeeting with special handling - always open when present */}
			{renderMeetingModal()}
		</>
	);
}

function ModalComponent({ type, open, data }: { type: ModalType; open: boolean; data: any }) {
	const dispatch = useDispatch();

	function handleClose() {
		dispatch(removeModal(type));
	}

	// Common props for all the modals
	const p: IModalComponentBaseProps = {
		key: type,
		id: type,
		open,
		onClose: handleClose,
	};

	switch (type) {
		case 'AssignContacts':
			return <AssignContacts {...p} {...data} />;
		case 'CreateClient':
			return <CreateClientModal {...p} {...data} />;
		case 'CreateContact':
			return <CreateContactModal {...p} {...data} />;
		case 'Confirmation':
			return <ConfirmationModal {...p} />;
		case 'CreateCall':
			return <CreateCall {...p} {...data} />;
		case 'PreviewDocument':
			return <PreviewDocument mode='modal' {...p} {...data} />;
		case 'AddClientFiles':
			return <CreateDocuments {...p} {...data} />;
		case 'EditContactDetails':
			return <EditContactDetails {...p} />;
		case 'CreateStaff':
			return <CreateStaffModal {...p} {...data} />;
		case 'EditProviderDetails':
			return <EditProviderDetails {...p} />;
		case 'EditPassword':
			return <EditPasswordModal {...p} />;
		case 'AssignStaff':
			return <AssignStaffModal {...p} {...data} />;
		case 'ManageTags':
			return <ManageTags {...p} />;
		case 'EditClientTags':
			return <EditClientTags {...p} {...data} />;
		case 'CreateTask':
			return <CreateTask {...p} />;
		case 'EditTask':
			return <EditTask {...p} task={data.task} parentData={data.parentData} />;
		case 'CreateService':
			return <CreateService {...p} serviceGroup={data?.serviceGroup} />;
		case 'CreateServiceGroup':
			return <CreateServiceGroup {...p} />;
		case 'EditServiceGroup':
			return <CreateServiceGroup {...p} serviceGroup={data.serviceGroup} />;
		case 'AddServiceToGroup':
			return <AddServiceToGroup {...p} serviceId={data.serviceId} />;
		case 'DeleteRecurringTaskConfirmation':
			return <DeleteRecurringTaskConfirmation {...p} task={data.task} />;
		case 'EditRecurringTaskConfirmation':
			return (
				<EditRecurringTaskConfirmation
					{...p}
					task={data.task.request}
					canUndo={data.canUndo}
					currentSidePanelId={data.currentSidePanelId}
				/>
			);
		case 'CreateLocation':
			return (
				<CreateLocationModal
					{...p}
					isEdit={data?.isEdit}
					location={data?.location}
					autoFocus={data?.autoFocus}
				/>
			);
		case 'NotifyAttendeesOfTask':
			return <NotifyAttendeesOfTaskModal {...p} {...data} />;
		case 'CreateInvoice':
			return <CreateInvoice {...p} {...data} />;
		case 'ExportInvoices':
			return <ExportInvoices {...p} />;
		case 'EmailInvoice':
			return <EmailInvoice {...p} {...data} />;
		case 'SendClientIntake':
			return <SendClientIntake {...p} client={data.client} />;
		case 'InviteToPortal':
			return <InviteToPortal {...p} client={data.client} />;
		case 'SyncCalendar':
			return <SyncCalendar {...p} />;
		case 'ConnectZoom':
			return <ConnectZoom {...p} />;
		case 'EditSubscriptionPaymentDetails':
			return <EditSubscriptionPaymentDetails {...p} />;
		case 'CreateWorkspace':
			return <CreateWorkspace {...p} />;
		case 'VerifyEmail':
			return <VerifyEmail {...p} />;
		case 'ImportClients':
			return <ImportClients {...p} {...data} />;
		case 'ExportClients':
			return <ExportClients {...p} onExport={data?.onExport} />;
		case 'CreateSuperbillReceipt':
			return <CreateSuperbillReceipt {...p} {...data} />;
		case 'EmailSuperbillReceipt':
			return <EmailSuperbillReceipt {...p} serviceReceipt={data.serviceReceipt} contact={data.contact} />;
		case 'TransferOwnership':
			return <TransferOwnershipModal {...p} />;
		case 'BookingLink':
			return <BookingLink {...p} {...data} />;
		case 'DeletePracticeWorkspace':
			return <DeletePracticeWorkspace {...p} />;
		case 'PreviewTemplate':
			return <PreviewTemplate {...p} {...data} />;
		case 'ShareWith':
			return <ShareWith {...p} {...data} />;
		case 'CancelSubscription':
			return <CancelSubscription {...p} {...data} />;
		case 'PaymentMethod':
			return <PaymentMethod {...p} {...data} />;
		case 'PublishTemplate':
			return <PublishTemplate {...p} {...data} editorId={data?.editorId || undefined} />;
		case 'ShareTemplateAsPublicForm':
			return <ShareTemplateAsPublicFormModal {...p} {...data} />;
		case 'PreviewFile':
			return <PreviewFile {...p} {...data} />;
		case 'EditorLink':
			return <EditorLink {...p} {...data} />;
		case 'ConnectInbox':
			return <ConnectInbox {...p} {...data} />;
		case 'ManageConnections':
			return <ManageConnections {...p} {...data} />;
		case 'MediaGallery':
			return <MediaGallery {...p} {...data} />;
		case 'ManageStatuses':
			return <ManageStatuses {...p} />;
		case 'TransferStatus':
			return <TransferStatus {...p} {...data} />;
		case 'UnlockNote':
			return <UnlockNote {...p} {...data} />;
		case 'PotentialClientDuplicateModal':
			return <PotentialClientDuplicateModal {...p} {...data} />;
		case 'CallWaitingPrompt':
			return <CallWaitingPrompt {...p} {...data} />;
		case 'ImageEditor':
			return <ImageEditorMobileModal {...p} {...data} />;
		case 'ManageReferrals':
			return <ManageReferrals {...p} {...data} />;
		case 'ServiceWorkerForceUIUpdate':
			return <ServiceWorkerForceUIUpdate {...p} {...data} />;
		case 'CreateTaxRate':
			return <CreateTaxRate {...p} {...data} />;
		case 'CreateNewInbox':
			return <CreateNewInboxModal {...p} {...data} />;
		case 'ManageIgnoreSenders':
			return <ManageIgnoreSendersModal {...p} {...data} />;
		case 'EmailHasIgnoredRecipients':
			return <EmailHasIgnoredRecipientsModal {...p} {...data} />;
		case 'DeleteTrashItems':
			return <DeleteTrashItemsModal {...p} {...data} />;
		case 'RestoreTrashItems':
			return <RestoreTrashItemsModal {...p} {...data} />;
		case 'DeleteRestorableItem':
			return <DeleteRestorableItemModal {...p} {...data} />;
		case 'ManageContactInsurancePolicy':
			return <ManageContactInsurancePolicy {...p} {...data} />;
		case 'ContactRelationship':
			return <ContactRelationship {...p} {...data} />;
		case 'ExportCalendar':
			return <ExportCalendar {...p} />;
		case 'DeleteAppointment':
			return <DeleteAppointment {...p} {...data} />;
		case 'TotpSetup':
			return <TotpSetupModal {...p} {...data} />;
		case 'SeparateDuplicateClients':
			return <SeparateDuplicateClients {...p} {...data} />;
		case 'MergeClientRecords':
			return <MergeClientRecords {...p} {...data} />;
		case 'NewClientNextSteps':
			return <NewClientNextSteps {...p} {...data} />;
		case 'NewContactNextSteps':
			return <NewContactNextStepsModal {...p} {...data} />;
		case 'NewTeamMemberNextSteps':
			return <NewTeamMemberNextStepsModal {...p} {...data} />;
		case 'LinkClient':
			return <LinkClient {...p} {...data} />;
		case 'TypeToConfirm':
			return <TypeToConfirmModal {...p} {...data} />;
		case 'UpdateEmailWarning':
			return <UpdateEmailWarning {...p} {...data} />;
		case 'ExportPayments':
			return <ExportPaymentsModal {...p} {...data} />;
		case 'CreateTemplateFolder':
			return <CreateTemplateFolder {...p} {...data} />;
		case 'MoveTemplateToFolder':
			return <MoveTemplateToFolder {...p} {...data} />;
		case 'ExportClaims':
			return <ExportClaimsModal {...p} {...data} />;
		case 'PersonalizeWorkspace':
			return <PersonalizeWorkspace {...p} {...data} />;
		case 'SuggestedServices':
			return <SuggestedServices {...p} {...data} />;
		case 'SuggestedLocations':
			return <SuggestedLocations {...p} {...data} />;
		case 'RevertClaimStatus':
			return <RevertClaimStatusModal {...p} {...data} />;
		case 'NewChatGroup':
			return <CreateChatGroupModal {...p} {...data} />;
		case 'OpenMeeting':
			return <Meeting key='OpenMeeting' open {...data} />;
		case 'ImportClientsActivity':
			return <ImportClientsActivity {...p} {...data} />;
	}

	return null;
}

export default ModalsRoot;
