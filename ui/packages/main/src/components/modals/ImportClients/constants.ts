import { ErrorCode } from 'react-dropzone/.';

import langIds from 'lang/langIds';

import { ImportDataSource } from './ImportDataSourceSelector';

export const MAX_FILE_SIZE = 1024 * 1024 * 1024 * 7; // 7 GB

export const SIMPLE_PRACTICE_OPTION: ImportDataSource = {
	id: 'SimplePractice',
	label: 'SimplePractice',
	extensions: ['.zip'],
	importSource: 'SimplePractice',
};

export const ATHENA_HEALTH_OPTION: ImportDataSource = {
	id: 'athenahealth',
	label: 'Athenahealth',
	extensions: ['.zip'],
	importSource: 'AthenaHealth',
};

export const DEFAULT_DATASOURCE_OPTIONS: ImportDataSource[] = [
	SIMPLE_PRACTICE_OPTION,
	ATHENA_HEALTH_OPTION,
	{
		id: 'TherapyNotes',
		label: 'Therapy Notes',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'Cliniko',
		label: 'Cliniko',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'Luminello',
		label: 'Luminello',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'Jane',
		label: 'Jane',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'talkEHR',
		label: 'talkEHR',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'TheraNest',
		label: 'TheraNest',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'PracticeFusion',
		label: 'Practice Fusion',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
	{
		id: 'CSV',
		label: 'CSV',
		extensions: ['.csv'],
		importSource: 'CSV',
	},
	{
		id: 'Other',
		label: 'Other',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'CSV',
	},
];

export const SIMPLE_PRACTICE_OPTION_V2: ImportDataSource = {
	id: 'SimplePractice',
	label: 'SimplePractice',
	extensions: ['.zip'],
	importSource: 'AI_SimplePractice',
};

export const ATHENA_HEALTH_OPTION_V2: ImportDataSource = {
	id: 'athenahealth',
	label: 'Athenahealth',
	extensions: ['.zip'],
	importSource: 'AI_AthenaHealth',
};

export const DEFAULT_DATASOURCE_OPTIONS_V2: ImportDataSource[] = [
	ATHENA_HEALTH_OPTION_V2,
	{
		id: 'Healthie',
		label: 'Healthie',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	{
		id: 'Jane',
		label: 'JaneApp',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	{
		id: 'PracticeBetter',
		label: 'PracticeBetter',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	SIMPLE_PRACTICE_OPTION_V2,
	{
		id: 'Tebra',
		label: 'Tebra',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	{
		id: 'EnsoraHealth',
		label: 'Ensora Health',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	{
		id: 'TherapyNotes',
		label: 'Therapy Notes',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
	{
		id: 'Zanda',
		label: 'Zanda',
		extensions: ['.csv', '.xlsx', '.xls'],
		importSource: 'AI_CSV',
	},
];

export const SUPPORTED_FILE_TYPES = {
	CSV: ['.csv'],
	XLS: ['.xls'],
	XLSX: ['.xlsx'],
	ZIP: ['.zip'],
};

export const ERROR_CODE_MAPPING: Record<ErrorCode, string> = {
	'file-too-large': langIds.FileTooLarge,
	'file-invalid-type': langIds.FileInvalidType,
	'too-many-files': langIds.TooManyFiles,
	'file-too-small': langIds.FileTooSmall,
};

export const TEST_IDS = {
	IMPORT_DATA_SOURCE_SELECTOR: 'import-data-source-selector',
	FILE_UPLOAD_INPUT: 'file-upload-input',
};
