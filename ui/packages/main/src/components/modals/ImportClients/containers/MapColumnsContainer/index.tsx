import { FC, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useC<PERSON><PERSON>er, useFormContext, useWatch } from 'react-hook-form';
import { FormattedMessage, useIntl } from 'react-intl';
import { Box, Grid, Stack, Typography } from '@mui/material';
import { produce } from 'immer';
import { isEmpty, keyBy } from 'lodash';

import { contactsLayoutId, contactsSchemaId } from 'components/SchemaForms/constants';
import { getProperty, valueHasTypeThenIs } from 'components/SchemaForms/DynamicForm/utilities';
import langIds from 'lang/langIds';
import { useCreateContactImportMappingMutation } from 'store/api/contactImports/hooks';
import {
	ContactImportMappingResult,
	SuggestedCustomField,
	SuggestedLayoutContainer,
} from 'store/api/contactImports/service';
import { FileLocationType } from 'store/api/files/config';
import { useLazyGetFileQuery } from 'store/api/files/hooks';
import { useGetSchemaLayoutQuery, useGetSchemaQuery } from 'store/api/schemas/service';
import { fallbackToEmptyArray } from 'util/fallbackToEmptyArray';

import { ImportClientsFormValuesV2 } from '../..';
import { SectionIcon } from '../../components/SectionIcon';
import { useImportClientsContext } from '../../ImportClientsContext';

import { ColumnSelectorLabel } from './ColumnSelector/ColumnSelectorLabel';
import { MultipleColumnSelector } from './ColumnSelector/MultipleColumnSelector';
import { SingleColumnSelector } from './ColumnSelector/SingleColumnSelector';
import { SectionNavItem, SectionsSkeleton } from './SectionNavItem';
import { SectionPanel, SectionPanelsSkeleton } from './SectionPanel';
import { EXCLUDED_FIELDS, FieldName, getAmbiguousNameFields, getContactImportMappingViaFileUrl } from './utilities';

const SECTIONS_CONTAINER_WIDTH = 325;

export const REQUIRED_FIELDS: string[] = [FieldName.FirstName, FieldName.LastName];

type Section = {
	id: number;
	title: string;
	layoutContainer: ILayoutContainer;
	suggestedCustomFields: string[];
};

type MapColumnsContainerProps = {};

export const MapColumnsContainer: FC<MapColumnsContainerProps> = () => {
	const { formatMessage } = useIntl();

	const { schemaFileId, suggestedCustomFields, columnNames, setSuggestedCustomFields, setColumnNames } =
		useImportClientsContext();

	const { control } = useFormContext<ImportClientsFormValuesV2>();

	const {
		field: { value: importOptions = {}, onChange: setImportOptions },
	} = useController({ control, name: 'importOptions' });

	const {
		field: { value: schema, onChange: setSchema },
	} = useController({ control, name: 'schema' });

	const {
		field: { value: schemaLayout, onChange: setSchemaLayout },
	} = useController({ control, name: 'schemaLayout' });

	const [fileId, fileName] = useWatch({ control, name: ['fileId', 'fileName'] });

	const [activeSection, setActiveSection] = useState<number>(0);
	const [isSyncingContactImportMapping, setIsSyncingContactImportMapping] = useState(isEmpty(importOptions));

	const sectionRefs = useRef<{ [key: string]: HTMLDivElement | null }>({});
	const containerRef = useRef<HTMLDivElement | null>(null);

	const { data: initialSchema, isLoading: isSchemaLoading } = useGetSchemaQuery({ id: contactsSchemaId });

	const { data: initialSchemaLayout, isLoading: isSchemaLayoutLoading } = useGetSchemaLayoutQuery({
		id: contactsLayoutId,
		schemaId: contactsSchemaId,
	});

	const [createContactImportMapping, { isLoading: isCreatingContactImportMapping }] =
		useCreateContactImportMappingMutation();

	const [triggerGetFile] = useLazyGetFileQuery();

	const removePropertyFromSchema = useCallback(
		({ property }: { property: string }) => {
			if (!schema) return;

			const newSchema = produce(schema, (draft) => {
				if (valueHasTypeThenIs<IObjectProperty>(draft?.properties?.['Fields'], 'Object')) {
					delete draft.properties['Fields'].properties[property];
				}
			});

			setSchema(newSchema);
		},
		[schema, setSchema]
	);

	const removePropertyFromSchemaLayout = useCallback(
		({ property, section }: { property: string; section: string }) => {
			if (!schemaLayout) return;

			const newSchemaLayout = produce(schemaLayout, (draft) => {
				const containerIndex = draft.elements.findIndex((container) => container.heading === section);

				if (containerIndex !== -1) {
					draft.elements[containerIndex].elements = draft.elements[containerIndex].elements.filter(
						(element) => element.property !== property
					);
				}
			});

			setSchemaLayout(newSchemaLayout);
		},
		[schemaLayout, setSchemaLayout]
	);

	const getSchemaWithSuggestions = useCallback(
		({ suggestedCustomFields }: { suggestedCustomFields: SuggestedCustomField[] }) => {
			if (!initialSchema) return;

			return produce(initialSchema, (draft) => {
				if (valueHasTypeThenIs<IObjectProperty>(draft.properties?.['Fields'], 'Object')) {
					const currentProperties = Object.keys(draft.properties['Fields'].properties);

					const newProperties: IDictionary<ControlTypes> = suggestedCustomFields.reduce((acc, field) => {
						const propertyKey = field.columnMapping.carepatronFieldName;

						// Exclude the suggested custom fields when either of the conditions is met:
						// 1. The property key is not defined
						// 2. The property display name is not defined
						// 3. The property key already exists in the schema
						if (!propertyKey || !field?.property?.displayName || currentProperties.includes(propertyKey)) {
							return acc;
						}

						acc[propertyKey] = field.property;
						return acc;
					}, {});

					draft.properties['Fields'].properties = {
						...draft.properties['Fields'].properties,
						...newProperties,
					};
				}
			});
		},
		[initialSchema]
	);

	const getSchemaLayoutWithSuggestions = useCallback(
		({
			suggestedCustomFields,
			suggestedLayoutContainers,
		}: {
			suggestedCustomFields: SuggestedCustomField[];
			suggestedLayoutContainers: SuggestedLayoutContainer[];
		}) => {
			if (!initialSchemaLayout) return;

			const newLayoutContainers: ILayoutContainer[] = suggestedLayoutContainers.map((container) => ({
				type: 'Container',
				elements: [],
				hidden: false,
				heading: container.heading,
				width: 6,
			}));

			return produce(initialSchemaLayout, (draft) => {
				draft.elements = [...draft.elements, ...newLayoutContainers];
				const currentProperties = draft.elements.reduce<string[]>((acc, container) => {
					const properties = container.elements.map((element) => element.property);
					return [...acc, ...properties];
				}, []);

				suggestedCustomFields.forEach((field) => {
					const propertyKey = field.columnMapping.carepatronFieldName;

					// Exclude the suggested custom fields when either of the conditions is met:
					// 1. The property key is not defined
					// 2. The property display name is not defined
					// 3. The property key already exists in the schema
					if (!propertyKey || !field?.property?.displayName || currentProperties.includes(propertyKey)) {
						return;
					}

					const containerIndex = draft.elements.findIndex(
						(container) => container.heading === field.layoutContainer
					);

					if (containerIndex !== -1) {
						draft.elements[containerIndex].elements.push({
							property: propertyKey,
							type: 'Control',
							width: 4,
						});
					}
				});
			});
		},
		[initialSchemaLayout]
	);

	const sections = useMemo<Section[]>(() => {
		return fallbackToEmptyArray<ILayoutContainer>(schemaLayout?.elements).map((section, index) => ({
			id: index,
			title: section.heading ?? '',
			layoutContainer: section,
			suggestedCustomFields: suggestedCustomFields
				.filter((field) => field.layoutContainer === section.heading)
				.map((field) => field.columnMapping.carepatronFieldName),
		}));
	}, [schemaLayout?.elements, suggestedCustomFields]);

	const handleChangeColumn = (property: string) => (value: IContactImportOption) => {
		setImportOptions({ ...importOptions, [property]: value });
	};

	const handleDeleteProperty = ({ property, section }: { property: string; section: Section }) => {
		const isSuggestedCustomField = section.suggestedCustomFields.includes(property);

		// Only suggested custom fields can be deleted
		if (!isSuggestedCustomField) return;

		return () => {
			const newImportOptions = produce(importOptions, (draft) => {
				delete draft[property];
			});

			setImportOptions(newImportOptions);
			removePropertyFromSchema({ property });
			removePropertyFromSchemaLayout({ property, section: section.layoutContainer.heading ?? '' });
		};
	};

	const scrollToSection = (sectionId: number) => () => {
		const element = sectionRefs.current[sectionId];
		if (element) {
			element.scrollIntoView({ behavior: 'smooth', block: 'start' });
		}
	};

	const syncContactImportMapping = useCallback((result: ContactImportMappingResult) => {
		const suggestedCustomFieldsMappings = result.suggestedCustomFields.map((field) => field.columnMapping);

		const schemaWithSuggestions = getSchemaWithSuggestions({
			suggestedCustomFields: result.suggestedCustomFields,
		});

		const schemaLayoutWithSuggestions = getSchemaLayoutWithSuggestions({
			suggestedCustomFields: result.suggestedCustomFields,
			suggestedLayoutContainers: result.suggestedLayoutContainers,
		});

		setImportOptions(keyBy([...result.mappings, ...suggestedCustomFieldsMappings], 'carepatronFieldName'));
		setSchema(schemaWithSuggestions);
		setSchemaLayout(schemaLayoutWithSuggestions);

		setSuggestedCustomFields(result.suggestedCustomFields);
		setColumnNames(result.columnNames.filter((column) => column !== 'CAREPATRON_TEMP_ID'));

		setIsSyncingContactImportMapping(false);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	const ambiguousNameField = getAmbiguousNameFields(importOptions)[0]?.spreadsheetFieldName;
	const isSectionsLoading = isCreatingContactImportMapping || isSyncingContactImportMapping || isSchemaLayoutLoading;
	const isSectionPanelsLoading = isCreatingContactImportMapping || isSyncingContactImportMapping || isSchemaLoading;

	useEffect(() => {
		const container = containerRef.current;
		if (!container) return;

		const handleScroll = () => {
			const scrollPosition = container.scrollTop + 200;

			// Find the current section
			for (const section of sections) {
				const element = sectionRefs.current[section.id];
				if (element) {
					const { offsetTop, offsetHeight } = element;
					if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
						setActiveSection(section.id);
						break;
					}
				}
			}
		};

		container.addEventListener('scroll', handleScroll);
		handleScroll(); // Initial check

		return () => container.removeEventListener('scroll', handleScroll);
	}, [sections]);

	useEffect(() => {
		if (!isEmpty(importOptions)) return;

		const onCreateContactImportMappings = async () => {
			try {
				if (!fileId || !fileName) return;
				const result = await createContactImportMapping({ dto: { fileId, fileName } }).unwrap();
				syncContactImportMapping(result);
			} catch (error) {
				console.error(error);
				setIsSyncingContactImportMapping(false);
			}
		};

		const onGetContactImportMapping = async () => {
			try {
				if (!schemaFileId) return;

				const result = await triggerGetFile({
					fileId: schemaFileId,
					query: { fileLocationType: FileLocationType.ClientImport },
					options: { asProvider: true, ignoreAuth: true },
				});

				if (!result.data) return;

				const contactImportMapping = await getContactImportMappingViaFileUrl(result.data);
				syncContactImportMapping(contactImportMapping);
			} catch (error) {
				console.error(error);
				setIsSyncingContactImportMapping(false);
			}
		};

		// If `schemaFileId` is provided, the uploaded file has already been pre-processed and is ready for mapping.
		// Otherwise, create a new contact import mapping using the uploaded file’s ID and name.
		schemaFileId ? onGetContactImportMapping() : onCreateContactImportMappings();

		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [fileId, fileName, schemaFileId]);

	return (
		<Box sx={{ display: 'flex', height: '100%', gap: 3 }}>
			<Box
				sx={{
					flexShrink: 0,
					overflow: 'auto',
					width: SECTIONS_CONTAINER_WIDTH,
					pt: 0,
					pb: 2,
					pl: 4,
				}}
			>
				<Typography fontWeight='fontWeightBold'>
					<FormattedMessage id={langIds.Sections} />
				</Typography>
				<Stack gap={1} mt={3}>
					{isSectionsLoading ? (
						<SectionsSkeleton />
					) : (
						sections.map((section) => (
							<SectionNavItem
								key={section.id}
								icon={<SectionIcon name={section.title.replace(/\s+/g, '')} />}
								label={section.title}
								isActive={activeSection === section.id}
								onClick={scrollToSection(section.id)}
							/>
						))
					)}
				</Stack>
			</Box>

			<Box
				ref={containerRef}
				sx={{
					flexGrow: 1,
					height: '100%',
					overflow: 'auto',
					pt: 0,
					pb: 2,
					pr: 4,
				}}
			>
				<Typography fontWeight='fontWeightBold'>
					<FormattedMessage id={langIds.SpreadsheetColumns} />
				</Typography>
				<Stack gap={3} mt={3}>
					{isSectionPanelsLoading ? (
						<SectionPanelsSkeleton />
					) : (
						sections.map((section) => (
							<SectionPanel
								key={section.id}
								ref={(el) => (sectionRefs.current[section.id] = el as HTMLDivElement)}
								title={section.title}
								isActive={activeSection === section.id}
							>
								<Grid container mt={1} spacing={2}>
									{section.layoutContainer.elements.map((element) => {
										if (
											typeof element.property !== 'string' ||
											EXCLUDED_FIELDS.includes(element.property)
										)
											return null;

										const propertySchema = getProperty({ property: element.property, schema });
										const isMultiple = !!propertySchema?.multiple;
										const isRequired = REQUIRED_FIELDS.includes(element.property);
										const isSuggestedCustomField = section.suggestedCustomFields.includes(
											element.property
										);

										const defaultImportOption: IContactImportOption = {
											carepatronFieldName: element.property,
											countryCode: undefined,
											dateFormat: undefined,
											delimiter: '',
											fieldOptions: 'WholeField',
											isMultiple: isMultiple,
											spreadsheetFieldName: '',
											spreadsheetMultipleFieldNames: [],
										};

										const value = importOptions[element.property] ?? defaultImportOption;

										const error =
											isRequired &&
											(isMultiple
												? !value?.spreadsheetMultipleFieldNames?.length
												: !value?.spreadsheetFieldName);

										const errorMessage = error
											? formatMessage({ id: langIds.ValidationMixedRequired })
											: '';

										return (
											<Grid key={element.property} item xs={12} md={6} lg={4}>
												{isMultiple ? (
													<MultipleColumnSelector
														label={
															<ColumnSelectorLabel
																label={propertySchema?.displayName ?? ''}
																required={isRequired}
																suggested={isSuggestedCustomField}
															/>
														}
														options={columnNames}
														value={value}
														onChange={handleChangeColumn(element.property)}
														onDelete={handleDeleteProperty({
															property: element.property,
															section,
														})}
														error={error}
														helperText={errorMessage}
													/>
												) : (
													<SingleColumnSelector
														label={
															<ColumnSelectorLabel
																label={propertySchema?.displayName ?? ''}
																required={isRequired}
																suggested={isSuggestedCustomField}
															/>
														}
														ambiguousNameField={ambiguousNameField}
														options={columnNames}
														value={value}
														onChange={handleChangeColumn(element.property)}
														onDelete={handleDeleteProperty({
															property: element.property,
															section,
														})}
														error={error}
														helperText={errorMessage}
													/>
												)}
											</Grid>
										);
									})}
								</Grid>
							</SectionPanel>
						))
					)}
				</Stack>
			</Box>
		</Box>
	);
};
