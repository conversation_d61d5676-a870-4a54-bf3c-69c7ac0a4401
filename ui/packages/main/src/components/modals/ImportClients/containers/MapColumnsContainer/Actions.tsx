import { FC } from 'react';
import { FormattedMessage } from 'react-intl';
import ArrowBackRounded from '@mui/icons-material/ArrowBackRounded';
import { LoadingButton } from '@mui/lab';
import { Button } from '@mui/material';

import langIds from 'lang/langIds';

import { useImportClientsContext } from '../../ImportClientsContext';

export const MapColumnsBackButton: FC = () => {
	const { isUpdatingContactImportSchema, onGoToPreviousStep } = useImportClientsContext();

	return (
		<Button
			variant='outlined'
			startIcon={<ArrowBackRounded />}
			disabled={isUpdatingContactImportSchema}
			onClick={onGoToPreviousStep}
		>
			<FormattedMessage id={langIds.BackToUploadFile} />
		</Button>
	);
};

export const MapColumnsSaveButton: FC = () => {
	const { isUpdatingContactImportSchema, disableUpdateContactImportSchema, onUpdateContactImportSchema } =
		useImportClientsContext();

	return (
		<LoadingButton
			variant='contained'
			disabled={disableUpdateContactImportSchema}
			loading={isUpdatingContactImportSchema}
			onClick={onUpdateContactImportSchema}
		>
			<FormattedMessage id={langIds.Continue} />
		</LoadingButton>
	);
};

export const MapColumnsCancelButton: FC = () => {
	const { isUpdatingContactImportSchema, onClose } = useImportClientsContext();

	return (
		<Button variant='outlined' disabled={isUpdatingContactImportSchema} onClick={onClose}>
			<FormattedMessage id={langIds.Cancel} />
		</Button>
	);
};
