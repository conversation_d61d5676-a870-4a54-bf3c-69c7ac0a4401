import { logger } from '@carepatron/utilities';

import langIds from 'lang/langIds';
import { ContactImportMappingResult } from 'store/api/contactImports/service';

export enum FieldName {
	FirstName = 'FirstName',
	LastName = 'LastName',
	MiddleNames = 'MiddleNames',
	BirthDate = 'BirthDate',
	Gender = 'Gender',
	Ethnicity = 'Ethnicity',
	PhoneNumber = 'PhoneNumber',
	Email = 'Email',
	Status = 'Status',
	Address = 'Address',
	IdentificationNumber = 'IdentificationNumber',
	Occupation = 'Occupation',
	EmploymentStatus = 'EmploymentStatus',
	LivingArrangements = 'LivingArrangements',
	RelationshipStatus = 'RelationshipStatus',
	AssignedStaff = 'AssignedStaff',
}

/**
 * Gets fields that have ambiguous name mappings, where multiple name fields (FirstName, LastName, MiddleNames)
 * are mapped to the same spreadsheet column.
 *
 * For example, if the spreadsheet has a "Name" column and both FirstName and LastName are mapped to "Name",
 * this would return those two fields since they conflict.
 *
 * Example input:
 * mappings = {
 *   FirstName: { spreadsheetFieldName: "Name" },
 *   LastName: { spreadsheetFieldName: "Name" },
 *   MiddleNames: { spreadsheetFieldName: "Middle" }
 * }
 *
 * Returns: [FirstName, LastName] fields since they share "Name" column
 *
 * @param mappings - Record of field name to import option mappings
 * @returns Array of fields that have conflicting spreadsheet column mappings
 */
export const getAmbiguousNameFields = (mappings: Record<string, IContactImportOption>): IContactImportOption[] => {
	const nameFields = [FieldName.FirstName, FieldName.LastName, FieldName.MiddleNames];
	const fields = nameFields.map((field) => mappings[field]).filter(Boolean);

	// Return fields that have the same spreadsheetFieldName
	return fields.filter((field) =>
		fields.some(
			(otherField) => otherField !== field && otherField.spreadsheetFieldName === field.spreadsheetFieldName
		)
	);
};

export const mapToFieldOptionNameLangId = (value: ImportContactsFieldOption) => {
	const fieldOptionToLangIdMap = {
		FirstPart: langIds.FieldOptionsFirstPart,
		LastPart: langIds.FieldOptionsSecondPart,
		MiddlePart: langIds.FieldOptionsMiddlePart,
		WholeField: langIds.FieldOptionsWholeField,
	};

	return fieldOptionToLangIdMap[value] ?? langIds.FieldOptionsWholeField;
};

export const EXCLUDED_FIELDS = [FieldName.AssignedStaff];

export const getContactImportMappingViaFileUrl = async (fileUrl: string): Promise<ContactImportMappingResult> => {
	try {
		const response = await fetch(fileUrl);
		if (!response.ok) {
			throw new Error('Failed to fetch contact import mapping');
		}

		const json = await response.json();

		return json;
	} catch (error) {
		logger.error('Error getting contact import mapping:', error);
		throw error;
	}
};
