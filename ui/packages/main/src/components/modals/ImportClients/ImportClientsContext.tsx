import { createContext, FC, PropsWithChildren, useContext, useMemo, useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import DoNotDisturbRounded from '@mui/icons-material/DoNotDisturbRounded';

import { addErrorSnackbar } from 'components/snackbar/utils';
import langIds from 'lang/langIds';
import {
	useImportContactsMutation,
	useUpdateContactImportSchemaMutation,
	useUpdateContactImportSummaryMutation,
} from 'store/api/contactImports/hooks';
import { SuggestedCustomField } from 'store/api/contactImports/service';
import { useFeatureFlag } from 'store/slices/features/hooks';
import { addConfirmationModal, addModal } from 'store/slices/modals/slice';

import { REQUIRED_FIELDS } from './containers/MapColumnsContainer';
import { useRealTimeContactImportPreprocessor } from './hooks/useRealTimeContactImportPreprocessor';
import { ATHENA_HEALTH_OPTION_V2, SIMPLE_PRACTICE_OPTION_V2 } from './constants';
import { ImportDataSource } from './ImportDataSourceSelector';
import { composeImportOptionsQuery } from './utils';
import { ImportClientsFormValuesV2, ImportStepsV2 } from '.';

type ImportClientsContextProps = {
	activeStep: ImportStepsV2;
	showImportSourceOptions: boolean;
	isImportingContacts: boolean;
	isUpdatingContactImportSchema: boolean;
	isUpdatingContactImportSummary: boolean;
	disableUpdateContactImportSchema: boolean;
	suggestedCustomFields: SuggestedCustomField[];
	columnNames: string[];
	schemaFileId?: string;
	setActiveStep: (step: ImportStepsV2) => void;
	setShowImportSourceOptions: (show: boolean) => void;
	onClose: () => void;
	onGoToPreviousStep: () => void;
	onUpdateContactImportSchema: () => void;
	onImportClients: () => void;
	onUploadSuccess: (args: { fileId: string; file: File; dataSource?: ImportDataSource }) => void;
	setSuggestedCustomFields: (customFields: SuggestedCustomField[]) => void;
	setColumnNames: (columnNames: string[]) => void;
};

export const ImportClientsContext = createContext<ImportClientsContextProps>({
	activeStep: 0,
	showImportSourceOptions: false,
	isImportingContacts: false,
	isUpdatingContactImportSchema: false,
	isUpdatingContactImportSummary: false,
	disableUpdateContactImportSchema: false,
	suggestedCustomFields: [],
	columnNames: [],
	setActiveStep: () => {},
	setShowImportSourceOptions: () => {},
	onClose: () => {},
	onGoToPreviousStep: () => {},
	onUpdateContactImportSchema: () => {},
	onImportClients: () => {},
	onUploadSuccess: () => {},
	setSuggestedCustomFields: () => {},
	setColumnNames: () => {},
});

type ImportClientsProviderProps = {
	initialValues?: Partial<ImportClientsFormValuesV2>;
	schemaFileId?: string;
	onClose: () => void;
	onImportSuccess?: () => void;
};

export const ImportClientsProvider: FC<PropsWithChildren<ImportClientsProviderProps>> = ({
	children,
	initialValues,
	schemaFileId,
	onClose,
	onImportSuccess,
}) => {
	const dispatch = useDispatch();

	const [activeStep, setActiveStep] = useState<ImportStepsV2>(
		schemaFileId ? ImportStepsV2.MapColumns : ImportStepsV2.ChooseSource
	);
	const [showImportSourceOptions, setShowImportSourceOptions] = useState(false);
	const [suggestedCustomFields, setSuggestedCustomFields] = useState<SuggestedCustomField[]>([]);
	const [columnNames, setColumnNames] = useState<string[]>([]);

	const form = useForm<ImportClientsFormValuesV2>({
		values: initialValues,
	});

	const { fileId, fileName, importOptions, schema, schemaLayout } = useWatch({
		control: form.control,
	});

	const [triggerUpdateContactImportSchema, { isLoading: isUpdatingContactImportSchema }] =
		useUpdateContactImportSchemaMutation();

	const [triggerImportContacts, { isLoading: isImportingContacts }] = useImportContactsMutation();
	const [triggerUpdateContactImportSummary, { isLoading: isUpdatingContactImportSummary }] =
		useUpdateContactImportSummaryMutation();

	const onGoToPreviousStep = () => {
		const dataSource = form.getValues('dataSource');

		if (activeStep === ImportStepsV2.UploadFile && !!dataSource) {
			setShowImportSourceOptions(true);
			form.setValue('dataSource', undefined);
			return;
		}

		if (activeStep === ImportStepsV2.UploadFile) {
			setShowImportSourceOptions(false);
			form.setValue('dataSource', undefined);

			form.setValue('file', undefined);
			form.setValue('fileId', undefined);
			form.setValue('fileName', undefined);
		}

		if (activeStep === ImportStepsV2.MapColumns) {
			setShowImportSourceOptions(false);
			form.setValue('file', undefined);
			form.setValue('fileId', undefined);
			form.setValue('fileName', undefined);

			form.setValue('importOptions', undefined);
			form.setValue('schema', undefined);
			form.setValue('schemaLayout', undefined);

			form.setValue('csvFileUrl', undefined);
			form.setValue('importSummaryId', undefined);
		}

		setActiveStep((prev) => (prev > 0 ? prev - 1 : prev));
	};

	const onUpdateContactImportSchema = async () => {
		const { fileId, fileName, importOptions, schema, schemaLayout } = form.getValues();

		if (!fileId || !fileName || !importOptions || !schema || !schemaLayout) return;

		const result = await triggerUpdateContactImportSchema({
			dto: {
				fileId,
				fileName,
				importOptions: composeImportOptionsQuery({ importOptions: Object.values(importOptions) }),
				dataSchema: schema,
				layoutSchema: schemaLayout,
			},
		}).unwrap();

		form.setValue('csvFileUrl', result);
		setActiveStep(ImportStepsV2.Preview);
	};

	const onImportClients = async () => {
		const { file, fileId, importOptions, schema, importSummaryId } = form.getValues();

		if (!importOptions) return;

		try {
			if (importSummaryId) {
				await triggerUpdateContactImportSummary({
					id: importSummaryId,
					dto: {
						lastStatusSeenBy: [],
						status: 'Pending',
						mappedColumns: Object.values(importOptions),
					},
				});
			} else {
				if (!file || !fileId) return;

				await triggerImportContacts({
					dto: {
						fileName: file.name,
						fileSize: file.size,
						fileExtension: file.name.split('.').pop() ?? '',
						importFileId: fileId,
						importSource: 'AI_CSV',
						mappedColumns: Object.values(importOptions),
						dataSchema: schema,
					},
				});
			}
		} catch (error) {
			dispatch(addErrorSnackbar(error));
		} finally {
			onImportSuccess?.();
			onClose();
		}
	};

	const onUploadSuccess = async (args: { fileId: string; file: File; dataSource?: ImportDataSource }) => {
		const { fileId, file, dataSource } = args;
		const dataSourceId = dataSource?.id ?? '';

		const optionsWithBackgroundProcessing = [SIMPLE_PRACTICE_OPTION_V2.id, ATHENA_HEALTH_OPTION_V2.id];

		const importSourceMap: Record<string, ContactImportSource> = {
			[SIMPLE_PRACTICE_OPTION_V2.id]: 'AI_SimplePractice',
			[ATHENA_HEALTH_OPTION_V2.id]: 'AI_AthenaHealth',
		};

		if (file && fileId && optionsWithBackgroundProcessing.includes(dataSourceId)) {
			try {
				const result = await triggerImportContacts({
					dto: {
						fileName: file.name,
						fileSize: file.size,
						fileExtension: file.name.split('.').pop() ?? '',
						importFileId: fileId,
						importSource: importSourceMap[dataSourceId],
						status: 'Draft',
						importType: 'Advanced',
					},
				}).unwrap();

				if (result) {
					dispatch(addModal('ImportClientsActivity'));
					onClose();
				}
			} catch (error) {
				dispatch(addErrorSnackbar(error));
			}

			return;
		}

		setActiveStep(ImportStepsV2.MapColumns);
	};

	const onCloseWithConfirmation = () => {
		if (activeStep >= ImportStepsV2.MapColumns) {
			dispatch(
				addConfirmationModal({
					titleIcon: <DoNotDisturbRounded sx={{ color: 'text.icon' }} />,
					titleMessageId: langIds.CancelClientImportTitle,
					descriptionMessageId: langIds.CancelClientImportDescription,
					primaryActionMessageId: langIds.CancelClientImportPrimaryAction,
					primaryActionColor: 'error',
					secondaryActionMessageId: langIds.CancelClientImportSecondaryAction,
					asyncActions: [
						{
							action: async () => {
								try {
									const { importSummaryId, importOptions } = form.getValues();

									if (importSummaryId && importOptions) {
										await triggerUpdateContactImportSummary({
											id: importSummaryId,
											dto: { lastStatusSeenBy: [], status: 'Cancelled' },
										}).unwrap();
									}

									onClose();
								} catch (error) {
									dispatch(addErrorSnackbar(error));
								}
							},
						},
					],
				})
			);

			return;
		}

		onClose();
	};

	useRealTimeContactImportPreprocessor({
		onContactImportPreprocessorCompleted: (args: { fileId: string; fileName: string }) => {
			form.setValue('fileId', args.fileId);
			form.setValue('fileName', args.fileName);
		},
	});

	const disableUpdateContactImportSchema = useMemo(() => {
		const hasMissingRequiredMappings = !REQUIRED_FIELDS.every(
			(field) => !!importOptions?.[field]?.spreadsheetFieldName
		);

		return (
			!fileId ||
			!fileName ||
			!importOptions ||
			!schema ||
			!schemaLayout ||
			isUpdatingContactImportSchema ||
			hasMissingRequiredMappings
		);
	}, [fileId, fileName, importOptions, schema, schemaLayout, isUpdatingContactImportSchema]);

	return (
		<ImportClientsContext.Provider
			value={{
				activeStep,
				showImportSourceOptions,
				isImportingContacts,
				isUpdatingContactImportSchema,
				isUpdatingContactImportSummary,
				disableUpdateContactImportSchema,
				suggestedCustomFields,
				columnNames,
				schemaFileId,
				setActiveStep,
				setShowImportSourceOptions,
				onClose: onCloseWithConfirmation,
				onGoToPreviousStep,
				onUpdateContactImportSchema,
				onImportClients,
				onUploadSuccess,
				setSuggestedCustomFields,
				setColumnNames,
			}}
		>
			<FormProvider {...form}>{children}</FormProvider>
		</ImportClientsContext.Provider>
	);
};

export const useImportClientsContext = () => {
	const context = useContext(ImportClientsContext);

	if (!context) {
		throw new Error('useImportClientsContext must be used within a ImportClientsProvider');
	}

	return context;
};
