import { FC, ReactNode, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import ArticleRounded from '@mui/icons-material/ArticleRounded';
import CloseIcon from '@mui/icons-material/Close';
import DeleteRoundedIcon from '@mui/icons-material/DeleteRounded';
import UploadFileRounded from '@mui/icons-material/UploadFileRounded';
import UploadRounded from '@mui/icons-material/UploadRounded';
import { Box, Button, FormHelperText, IconButton, Stack, Step, StepLabel, Stepper, useTheme } from '@mui/material';
import { useFormik } from 'formik';
import { produce } from 'immer';

import * as Dialog from '@carepatron/components/src/Dialog';
import { analytics, carepatronLinks, logger } from '@carepatron/utilities';

import { FileUploadListItem } from 'components/common/FileUpload/FileUploadListItem';
import { contactsSchemaId } from 'components/SchemaForms/constants';
import { addErrorSnackbar } from 'components/snackbar/utils';
import SquareIcon from 'components/SquareIcon';
import langIds from 'lang/langIds';
import { baseApiCall } from 'store/api';
import { useImportContactsMutation } from 'store/api/contactImports/hooks';
import { FileLocationType, filesApiConfig } from 'store/api/files/config';
import { useCurrentProviderId } from 'store/api/providers/hooks';
import { useGetSchemaQuery } from 'store/api/schemas/service';
import { selectUserHasFeature } from 'store/slices/features/selectors';
import { safeExec } from 'util/common';
import { useParamSelector } from 'util/hooks';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { useFileUpload } from 'util/hooks/useFileUpload';

import { ImportClientsGuide } from './components/ImportClientsGuide';
import { ChooseSourceContainer } from './containers/ChooseSourceContainer';
import { MapColumnsContainer } from './containers/MapColumnsContainer';
import {
	MapColumnsBackButton,
	MapColumnsCancelButton,
	MapColumnsSaveButton,
} from './containers/MapColumnsContainer/Actions';
import { PreviewContainer } from './containers/PreviewContainer';
import { PreviewBackButton, PreviewSaveButton } from './containers/PreviewContainer/Actions';
import { UploadFileContainer } from './containers/UploadFileContainer';
import { UploadFileBackButton } from './containers/UploadFileContainer/UploadFileBackButton';
import { BreadcrumbsController } from './controllers/BreadcrumbsController';
import { ChooseDataSource } from './steps/ChooseDataSource';
import { ATHENA_HEALTH_OPTION, MAX_FILE_SIZE, SIMPLE_PRACTICE_OPTION, TEST_IDS } from './constants';
import { ImportClientsProvider, useImportClientsContext } from './ImportClientsContext';
import { ImportDataSource } from './ImportDataSourceSelector';
import { MapColumns } from './MapColumns';
import { bytesToSize, composeMappedColumnsQuery, transformToContactImportOptions } from './utils';
import { getImportClientsFormSchema } from './validationSchema';

const UploadFileHelperText = ({ values, touched, errors }) => {
	const getUploadFileHelperText = () => {
		if (touched.file && errors.file) return errors.file;

		if (values.dataSource) {
			return (
				<FormattedMessage
					id={langIds.ImportClientsModalFileUploadHelperText}
					values={{
						fileTypes: values.dataSource.extensions.join(', '),
						fileSizeLimit: `${bytesToSize(MAX_FILE_SIZE)}`,
					}}
				/>
			);
		}

		return '';
	};

	return <FormHelperText error={touched.file && !!errors.file}>{getUploadFileHelperText()}</FormHelperText>;
};

enum ImportSteps {
	ChooseDataSource = 0,
	UploadingFile = 1,
	ReviewFields = 2,
}

export enum ImportStepsV2 {
	ChooseSource = 0,
	UploadFile = 1,
	MapColumns = 2,
	Preview = 3,
}

export interface ImportClientsFormValues {
	isInternalUser: boolean;
	dataSource?: ImportDataSource;
	file?: File;
	mappedColumns: IContactImportOption[];
	additionalFields: string[];
	selectedTags: string[];
	otherDataSourceName?: string;
}

export interface ImportClientsFormValuesV2 {
	dataSource?: ImportDataSource;
	file?: File;
	fileId?: string;
	fileName?: string;
	importOptions?: Record<string, IContactImportOption>;
	schema?: ISchema;
	schemaLayout?: ISchemaLayout;
	csvFileUrl?: string;
	importSummaryId?: string;
}

type ImportClientsStep = {
	id: ImportStepsV2;
	title: JSX.Element | string;
	content: JSX.Element;
	actions?: { start?: ReactNode[]; end?: ReactNode[] };
};

interface ImportClientsProps extends IModalComponentBaseProps {
	initialValues?: Partial<ImportClientsFormValuesV2>;
	schemaFileId?: string;
	onImportSuccess?: () => void;
	onCloseCallback?: () => void;
}

const LegacyImportClients: FC<ImportClientsProps> = ({ open, onClose, onImportSuccess, onCloseCallback }) => {
	const dispatch = useDispatch();
	const { formatMessage } = useIntl();
	const fullScreen = useBreakpoint('md');

	const { currentProviderId } = useCurrentProviderId();
	const { data: schema } = useGetSchemaQuery({ id: contactsSchemaId });
	const [triggerImportContacts] = useImportContactsMutation();

	const [activeStep, setActiveStep] = useState<ImportSteps>(ImportSteps.ChooseDataSource);
	const [progress, setProgress] = useState<number>(0);
	const [importSummary, setImportSummary] = useState<IContactImportFileResult>();

	const { uploadFile } = useFileUpload({ fileLocationType: FileLocationType.ClientImport });

	const hasDevFeature = useParamSelector(selectUserHasFeature, 'internal');

	const handleClose = () => {
		onClose();
		onCloseCallback?.();
	};

	const {
		values,
		touched,
		errors,
		isSubmitting,
		setFieldValue,
		setFieldTouched,
		setFieldError,
		submitForm,
		resetForm,
	} = useFormik<ImportClientsFormValues>({
		initialTouched: {
			file: true,
		},
		validationSchema: getImportClientsFormSchema(),
		initialValues: {
			isInternalUser: hasDevFeature,
			mappedColumns: [],
			additionalFields: [],
			selectedTags: [],
		},
		onSubmit: async (
			{ dataSource, file, mappedColumns, additionalFields, selectedTags, otherDataSourceName },
			formikHelpers
		) => {
			if (!schema || !file || !dataSource) return;

			formikHelpers.setSubmitting(true);

			// Create a new schema with additional fields
			const dataSchema = produce(schema, (draft: ISchema) => {
				draft.properties = {
					...additionalFields.reduce((acc, field) => {
						acc[field] = { type: 'String', displayName: field } as IStringProperty;
						return acc;
					}, {} as IDictionary<IStringProperty>),
				};
			});

			try {
				const { fileId } = await uploadFile({ file });
				await triggerImportContacts({
					dto: {
						fileName: file.name,
						fileSize: file.size,
						fileExtension: file.name.split('.').pop() || '',
						importFileId: fileId,
						importSource: 'CSV',
						mappedColumns: composeMappedColumnsQuery({ mappedColumns, selectedTags }),
						dataSchema,
					},
					context: { otherDataSourceName },
				});

				onImportSuccess?.();
			} catch (err) {
				logger.error(err);
			} finally {
				handleClose();
			}
		},
	});

	const handleImportCancellation = () => {
		setProgress(0);
		setActiveStep(ImportSteps.ChooseDataSource);
		resetForm();
	};

	const uploadContactImportSummary = async (file: File) => {
		try {
			const result = await baseApiCall<IContactImportFileResult>(
				filesApiConfig.uploadContactImportSummary({
					dto: {
						providerId: currentProviderId,
						file,
					},
					options: {
						onUploadProgress: ({ loaded, total = 0 }) => setProgress((loaded / total) * 100),
					},
				})
			);
			safeExec(() => {
				analytics.trackEvent({ eventAction: 'import clients upload', eventCategory: 'contacts' });
			});
			return result;
		} catch (error) {
			dispatch(addErrorSnackbar(error));
			handleImportCancellation();
		}
	};

	const handleFileUpload = async ({ target: { files } }) => {
		try {
			const file: File = files[0];
			const dataSourceId = values.dataSource?.id;

			if (!dataSourceId) return;

			setFieldValue('file', file);
			setActiveStep(ImportSteps.UploadingFile);

			if (!hasDevFeature && file.size > MAX_FILE_SIZE) return;

			switch (dataSourceId) {
				case SIMPLE_PRACTICE_OPTION.id:
				case ATHENA_HEALTH_OPTION.id:
					const importSource =
						dataSourceId === SIMPLE_PRACTICE_OPTION.id
							? SIMPLE_PRACTICE_OPTION.importSource
							: ATHENA_HEALTH_OPTION.importSource;

					try {
						const { fileId } = await uploadFile({ file, onProgress: (progress) => setProgress(progress) });
						triggerImportContacts({
							dto: {
								fileName: file.name,
								fileSize: file.size,
								fileExtension: file.name.split('.').pop() || '',
								importFileId: fileId,
								importSource,
							},
						});

						onImportSuccess?.();
						handleClose();
					} catch (error) {
						setFieldError('file', formatMessage({ id: langIds.ErrorBoundaryTitle }));
					}
					break;
				default:
					const response = await uploadContactImportSummary(file);

					if (!response) return;

					setFieldValue('mappedColumns', transformToContactImportOptions(response.bestMatchSchemaFields));
					setImportSummary(response);
					setActiveStep(ImportSteps.ReviewFields);

					break;
			}
		} catch (error) {
			handleImportCancellation();
		}
	};

	const handleUploadButtonClick = (e: React.MouseEvent) => {
		if (values.dataSource?.id === 'Other' && !values.otherDataSourceName?.trim()) {
			setFieldTouched('otherDataSourceName', true, true);
			e.preventDefault();
			return;
		}
		setFieldTouched('dataSource', true, true);
	};

	const steps: { id: ImportSteps; title: JSX.Element | string; content: JSX.Element; actions?: ReactNode[] }[] = [
		{
			id: ImportSteps.ChooseDataSource,
			title: <FormattedMessage id={langIds.ImportClientsModalStep1Label} />,
			content: (
				<ChooseDataSource
					values={values}
					errors={errors}
					touched={touched}
					disabled={!!progress}
					setFieldValue={setFieldValue}
					actions={
						<>
							<Button
								variant='contained'
								component='label'
								fullWidth
								startIcon={<UploadFileRounded />}
								sx={{ mt: 4 }}
								onClick={handleUploadButtonClick}
							>
								<FormattedMessage id={langIds.UploadFile} />
								{values.dataSource && (
									<input
										data-testid={TEST_IDS.FILE_UPLOAD_INPUT}
										type='file'
										hidden
										accept={values.dataSource.extensions.join(', ')}
										onChange={handleFileUpload}
									/>
								)}
							</Button>

							<UploadFileHelperText values={values} touched={touched} errors={errors} />
						</>
					}
				/>
			),
		},
		{
			id: ImportSteps.UploadingFile,
			title: <FormattedMessage id={langIds.ImportClientsModalStep2Label} />,
			content: (
				<ChooseDataSource
					disabled
					values={values}
					errors={errors}
					touched={touched}
					setFieldValue={setFieldValue}
					actions={
						<Box mt={4}>
							{values.file ? (
								<>
									<FileUploadListItem
										fileName={values.file.name || ''}
										fileSize={values.file.size || 0}
										loading={true}
										progress={progress}
										error={!!errors.file}
										errorMessage={errors.file}
										loadingMessage={formatMessage({ id: langIds.Importing })}
										actions={
											errors.file
												? [
														<IconButton
															key='close'
															aria-label='close'
															size='small'
															onClick={handleImportCancellation}
														>
															<DeleteRoundedIcon fontSize='small' />
														</IconButton>,
													]
												: []
										}
									/>
									<Button
										color='error'
										variant='outlined'
										component='label'
										fullWidth
										onClick={handleImportCancellation}
										sx={{ mt: 2 }}
									>
										<FormattedMessage id={langIds.CancelImportButton} />
									</Button>
								</>
							) : (
								<Button
									variant='contained'
									component='label'
									fullWidth
									startIcon={<UploadFileRounded />}
									sx={{ mt: 2 }}
									onClick={() => setFieldTouched('dataSource', true, true)}
								>
									<FormattedMessage id={langIds.UploadFile} />
									{values.dataSource && (
										<input
											data-testid={TEST_IDS.FILE_UPLOAD_INPUT}
											type='file'
											hidden
											accept={values.dataSource.extensions.join(', ')}
											onChange={handleFileUpload}
										/>
									)}
								</Button>
							)}
						</Box>
					}
				/>
			),
		},
		{
			id: ImportSteps.ReviewFields,
			title: <FormattedMessage id={langIds.ImportClientsModalStep3Label} />,
			content: (
				<>
					{importSummary && (
						<MapColumns
							importSummary={importSummary}
							mappedColumns={values.mappedColumns}
							additionalFields={values.additionalFields}
							selectedTags={values.selectedTags}
							errors={errors}
							touched={touched}
							setFieldValue={setFieldValue}
						/>
					)}
				</>
			),
			actions: [
				<Button
					key='cancel'
					variant='outlined'
					disabled={isSubmitting}
					fullWidth={fullScreen}
					onClick={onClose}
				>
					<FormattedMessage id={langIds.Cancel} />
				</Button>,
				<Button
					key='submit'
					variant='contained'
					disabled={isSubmitting}
					fullWidth={fullScreen}
					onClick={submitForm}
				>
					<FormattedMessage id={langIds.Submit} />
				</Button>,
			],
		},
	];

	return (
		<Dialog.Root open={open} fullScreen={fullScreen} showProgress={isSubmitting} onClose={handleClose}>
			<Dialog.Header
				title={<FormattedMessage id={langIds.ImportClientsModalTitle} />}
				icon={<UploadRounded />}
				onClose={handleClose}
			/>
			<Dialog.Content>
				<Stepper nonLinear alternativeLabel={fullScreen} activeStep={activeStep} sx={{ mt: 1, mb: 3 }}>
					{steps.map((step) => (
						<Step key={`${step.id}`} completed={activeStep > step.id}>
							<StepLabel>{step.title}</StepLabel>
						</Step>
					))}
				</Stepper>
				{steps[activeStep].content}
			</Dialog.Content>
			<Dialog.Footer
				actions={{
					start: [
						<Button
							key='guide'
							fullWidth={fullScreen}
							startIcon={<ArticleRounded />}
							href={carepatronLinks.ImportClientData}
							target='_blank'
						>
							<FormattedMessage id={langIds.ImportClientsModalImportGuideLabel} />
						</Button>,
					],
					end: steps[activeStep].actions,
				}}
			/>
		</Dialog.Root>
	);
};

const ImportClients: FC<ImportClientsProps> = ({ open }) => {
	const theme = useTheme();
	const belowMd = useBreakpoint('md');

	const { activeStep, onClose } = useImportClientsContext();

	const isFullScreen = belowMd || activeStep >= ImportStepsV2.MapColumns;

	const steps: ImportClientsStep[] = [
		{
			id: ImportStepsV2.ChooseSource,
			title: <FormattedMessage id={langIds.ChooseSource} />,
			content: <ChooseSourceContainer />,
		},
		{
			id: ImportStepsV2.UploadFile,
			title: <FormattedMessage id={langIds.UploadFile} />,
			content: <UploadFileContainer />,
			actions: {
				start: [<UploadFileBackButton />],
			},
		},
		{
			id: ImportStepsV2.MapColumns,
			title: <FormattedMessage id={langIds.MapColumns} />,
			content: <MapColumnsContainer />,
			actions: {
				start: [<MapColumnsBackButton />],
				end: [<MapColumnsCancelButton />, <MapColumnsSaveButton />],
			},
		},
		{
			id: ImportStepsV2.Preview,
			title: <FormattedMessage id={langIds.Preview} />,
			content: <PreviewContainer />,
			actions: {
				end: [<PreviewBackButton />, <PreviewSaveButton />],
			},
		},
	];

	return (
		<Dialog.Root open={open} fullScreen={isFullScreen} onClose={onClose} maxWidth='md'>
			{isFullScreen && (
				<>
					<Stack sx={(theme) => ({ backgroundColor: theme.palette.chrome.main, px: 3, py: 0.75 })}>
						<IconButton onClick={onClose}>
							<CloseIcon sx={(theme) => ({ color: theme.palette.primary.contrastText })} />
						</IconButton>
					</Stack>
					<BreadcrumbsController />
				</>
			)}

			<Dialog.Header
				title={
					<Stack direction='row' alignItems='center' gap={1} width='100%'>
						<FormattedMessage id={langIds.ImportClients} />
						<ImportClientsGuide isFullScreen={isFullScreen} />
					</Stack>
				}
				icon={
					isFullScreen ? (
						<SquareIcon
							icon={<UploadFileRounded />}
							size='md'
							sx={{ backgroundColor: theme.palette.background.default }}
						/>
					) : (
						<UploadFileRounded />
					)
				}
				sx={isFullScreen ? { padding: `${theme.spacing(0, 3, 2, 3)} !important` } : undefined}
				onClose={isFullScreen ? undefined : onClose}
			/>

			<Stepper
				nonLinear
				alternativeLabel={belowMd}
				activeStep={activeStep}
				sx={(theme) =>
					isFullScreen
						? {
								p: 3,
								borderTop: `1px solid ${theme.palette.border}`,
								backgroundColor: theme.palette.background.default,
							}
						: { pt: 1, pb: 2, px: 2 }
				}
			>
				{steps.map((step) => (
					<Step key={`${step.id}`} completed={activeStep > step.id}>
						<StepLabel>{step.title}</StepLabel>
					</Step>
				))}
			</Stepper>

			<Dialog.Content
				sx={(theme) => ({
					backgroundColor: isFullScreen ? theme.palette.background.default : theme.palette.background.paper,
					...(isFullScreen && {
						p: `0px !important`,
					}),
				})}
			>
				{steps[activeStep].content}
			</Dialog.Content>

			{steps[activeStep].actions && <Dialog.Footer fixed={isFullScreen} actions={steps[activeStep].actions} />}
		</Dialog.Root>
	);
};

const Root: FC<ImportClientsProps> = (props) => {
	const { initialValues, schemaFileId, onClose, onCloseCallback } = props;

	const handleClose = () => {
		onClose();
		onCloseCallback?.();
	};

	return (
		<ImportClientsProvider initialValues={initialValues} schemaFileId={schemaFileId} onClose={handleClose}>
			<ImportClients {...props} />
		</ImportClientsProvider>
	);
};

export default Root;
