import { FC, useState } from 'react';
import { useController, useFormContext, useWatch } from 'react-hook-form';
import { useIntl } from 'react-intl';

import langIds from 'lang/langIds';
import { FileLocationType } from 'store/api/files/config';
import { useFileUpload } from 'util/hooks/useFileUpload';

import { DataSourceIcon } from '../components/DataSourceIcon';
import { FileUploader } from '../components/FileUploader';
import { ATHENA_HEALTH_OPTION_V2, SIMPLE_PRACTICE_OPTION_V2 } from '../constants';
import { useImportClientsContext } from '../ImportClientsContext';
import { ImportClientsFormValuesV2 } from '..';

export const FileUploaderController: FC = () => {
	const { formatMessage } = useIntl();

	const { onUploadSuccess } = useImportClientsContext();
	const { control, setError } = useFormContext<ImportClientsFormValuesV2>();

	const {
		field: { value, onChange: onChangeFile },
		fieldState: { error },
	} = useController({ control, name: 'file' });

	const {
		field: { onChange: onChangeFileId },
	} = useController({ control, name: 'fileId' });

	const {
		field: { onChange: onChangeFileName },
	} = useController({ control, name: 'fileName' });

	const dataSource = useWatch({ control, name: 'dataSource' });

	const { uploadFile } = useFileUpload({ fileLocationType: FileLocationType.ClientImport });

	const [isUploading, setIsUploading] = useState<boolean>(false);
	const [progress, setProgress] = useState<number>(0);

	const handleUpload = async (file: File) => {
		try {
			onChangeFile(file);
			setIsUploading(true);

			const result = await uploadFile({
				file,
				onProgress: (progress) => setProgress(Math.min(progress, 100)),
			});

			onUploadSuccess({ fileId: result.fileId, file, dataSource });

			// If the data source is Simple Practice or Athena Health, we don't need to set the fileId and fileName automatically.
			if ([SIMPLE_PRACTICE_OPTION_V2.id, ATHENA_HEALTH_OPTION_V2.id].includes(dataSource?.id ?? '')) return;

			onChangeFileId(result.fileId);
			onChangeFileName(file.name);
		} catch (error) {
			setError('file', { message: formatMessage({ id: langIds.ErrorBoundaryTitle }) });
		} finally {
			setIsUploading(false);
		}
	};

	const handleRemove = () => {
		onChangeFile(null);
		onChangeFileId(null);
		onChangeFileName(null);
	};

	return (
		<FileUploader
			value={value}
			supportedExtensions={dataSource?.extensions}
			progress={progress}
			isUploading={isUploading}
			error={!!error}
			errorMessage={error?.message}
			icon={dataSource ? <DataSourceIcon dataSource={dataSource.id} /> : null}
			onUpload={handleUpload}
			onRemove={handleRemove}
		/>
	);
};
