import { FC, ReactNode } from 'react';

import Athenahealth from '../icons/Athenahealth';
import EnsoraHealth from '../icons/EnsoraHealth';
import Healthie from '../icons/Healthie';
import <PERSON>A<PERSON> from '../icons/JaneApp';
import PracticeBetter from '../icons/PracticeBetter';
import SimplePractice from '../icons/SimplePractice';
import Tebra from '../icons/Tebra';
import TherapyNotes from '../icons/TherapyNotes';
import Zanda from '../icons/Zanda';

type DataSourceIconProps = {
	dataSource: string;
};

export const DataSourceIcon: FC<DataSourceIconProps> = ({ dataSource }) => {
	const iconMap: Record<string, ReactNode> = {
		athenahealth: <Athenahealth />,
		healthie: <Healthie />,
		jane: <JaneApp />,
		practicebetter: <PracticeBetter />,
		simplepractice: <SimplePractice />,
		tebra: <Tebra />,
		ensorahealth: <EnsoraHealth />,
		therapynotes: <TherapyNotes />,
		zanda: <Zanda />,
	};

	return <>{iconMap[dataSource.toLocaleLowerCase()] ?? null}</>;
};
