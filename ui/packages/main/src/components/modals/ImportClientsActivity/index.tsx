import { FC } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { useDispatch } from 'react-redux';
import UploadFileRoundedIcon from '@mui/icons-material/UploadFileRounded';
import { Button } from '@mui/material';

import * as Dialog from '@carepatron/components/src/Dialog';

import { FileUploadListItem } from 'components/common/FileUpload/FileUploadListItem';
import langIds from 'lang/langIds';
import { addModal } from 'store/slices/modals/slice';

import { ImportClientsFormValuesV2 } from '../ImportClients';

import { useCurrentClientImport } from './useCurrentClientImport';

interface ImportClientsActivityProps extends IModalComponentBaseProps {}

export const ImportClientsActivity: FC<ImportClientsActivityProps> = ({ open, onClose }) => {
	const dispatch = useDispatch();
	const { formatMessage } = useIntl();

	const { importSummary } = useCurrentClientImport();

	const isReadyForMapping = importSummary?.status === 'ReadyForMapping';

	const handleReadyForMappingClick = async () => {
		if (!importSummary) return;

		try {
			const initialValues: ImportClientsFormValuesV2 = {
				fileId: importSummary.fileId,
				fileName: importSummary.fileName,
				importSummaryId: importSummary.id,
			};

			dispatch(
				addModal({
					type: 'ImportClients',
					data: {
						initialValues,
						schemaFileId: importSummary.schemaFileId,
					},
				})
			);

			onClose();
		} catch (error) {
			console.error(error);
		}
	};

	return (
		<Dialog.Root open={open} fullWidth maxWidth='sm' onClose={onClose}>
			<Dialog.Header
				title={<FormattedMessage id={isReadyForMapping ? langIds.ContinueImport : langIds.ImportProcessing} />}
				icon={<UploadFileRoundedIcon />}
				onClose={onClose}
			/>
			<Dialog.Content>
				<Dialog.Description>
					<FormattedMessage
						id={
							isReadyForMapping
								? langIds.ClientImportReadyForMappingDescription
								: langIds.ClientImportProcessingDescription
						}
					/>
				</Dialog.Description>

				{importSummary && (
					<FileUploadListItem
						fileName={importSummary.fileName}
						fileSize={importSummary.fileSize}
						successMessage={formatMessage({ id: langIds.MappingRequired })}
						loading={!isReadyForMapping}
						success={isReadyForMapping}
						successIcon={<></>}
					/>
				)}
			</Dialog.Content>
			<Dialog.Footer
				actions={{
					end: isReadyForMapping
						? [
								<Button variant='outlined' onClick={onClose}>
									<FormattedMessage id={langIds.DoThisLater} />
								</Button>,
								<Button variant='contained' onClick={handleReadyForMappingClick}>
									<FormattedMessage id={langIds.MapColumns} />
								</Button>,
							]
						: [
								<Button variant='outlined' onClick={onClose}>
									<FormattedMessage id={langIds.RunInBackground} />
								</Button>,
							],
				}}
			/>
		</Dialog.Root>
	);
};
