import { useDispatch, useSelector } from 'react-redux';

import { selectIsModalOpenById } from 'store/slices/modals/selectors';
import { addModal } from 'store/slices/modals/slice';

import { useRealTimeContactImportPreprocessor } from '../ImportClients/hooks/useRealTimeContactImportPreprocessor';

export const useRealTimeClientImport = () => {
	const dispatch = useDispatch();

	const isOpen = useSelector(selectIsModalOpenById('ImportClientsActivity'));

	useRealTimeContactImportPreprocessor({
		onContactImportPreprocessorCompleted: () => {
			// Automatically open the ImportClientsActivity modal when the contact import preprocessor is completed
			if (isOpen) return;
			dispatch(addModal('ImportClientsActivity'));
		},
	});
};
