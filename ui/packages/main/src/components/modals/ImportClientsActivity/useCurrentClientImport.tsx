import { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { useLazyGetContactImportSummaryQuery } from 'store/api/contactImports/hooks';
import { selectActiveContactImportSummariesFromResult } from 'store/api/contactImports/selectors';
import { selectUserId } from 'store/api/user/selectors';

import { useRealTimeContactImportPreprocessor } from '../ImportClients/hooks/useRealTimeContactImportPreprocessor';

/**
 * This hook is used to fetch the current active client import.
 * @returns The client import summary.
 */
export const useCurrentClientImport = () => {
	const currentUserId = useSelector(selectUserId);

	const [triggerGetContactImportSummary, { isLoading, importSummary }] = useLazyGetContactImportSummaryQuery({
		selectFromResult: (result) => ({
			...result,
			importSummary: selectActiveContactImportSummariesFromResult(result, currentUserId || ''),
		}),
	});

	const fetchContactImportSummaries = async () => {
		await triggerGetContactImportSummary({
			query: { statuses: ['Draft', 'Preprocessing', 'ReadyForMapping'] },
		});
	};

	// Initial fetch of client import summaries
	useEffect(() => {
		fetchContactImportSummaries();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	// Refetch client import summaries when a real-time client import preprocessor completion event is received
	useRealTimeContactImportPreprocessor({
		onContactImportPreprocessorCompleted: () => {
			fetchContactImportSummaries();
		},
	});

	return {
		isLoading,
		importSummary,
	};
};
