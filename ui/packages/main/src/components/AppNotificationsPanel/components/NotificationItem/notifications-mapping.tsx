import { lazy } from 'react';

import langIds from 'lang/langIds';
import { AppNotificationEventType } from 'types/appNotification';

import { ActionProps } from './Action';
import { NotificationBodyProps } from './Body';
import { SubjectProps } from './Subject';

/**
 * Mapping of notification items subject
 */
export const notificationsSubjectMapping: Record<
	AppNotificationEventType,
	React.LazyExoticComponent<React.ComponentType<SubjectProps>> | string | null
> = {
	AppointmentAssigned: langIds.AppointmentAssignedNotificationSubject,
	AppointmentCancelled: langIds.AppointmentCancelledNotificationSubject,
	AppointmentRescheduled: langIds.AppointmentRescheduledNotificationSubject,
	AppointmentConfirmed: langIds.AppointmentConfirmedNotificationSubject,
	ClientAppointmentBooked: langIds.ClientAppointmentBookedNotificationSubject,
	// CLIENTS AND DOCUMENTATION
	FileUploaded: langIds.FileUploadedNotificationSubject,
	NoteFormSubmitted: langIds.NoteFormSubmittedNotificationSubject,
	ClientAddedNote: langIds.ClientAddedNoteNotificationSubject,
	ClientUpdatedNote: langIds.ClientUpdatedNoteNotificationSubject,
	PublicFormSubmitted: langIds.PublicFormSubmittedNotificationSubject,
	TemplateImportCompleted: langIds.TemplateImportCompletedNotificationSubject,
	TemplateImportFailed: langIds.TemplateImportFailedNotificationSubject,
	PreprocessImportContactCompleted: langIds.ClientImportReadyForMappingNotificationSubject,
	// BILLING & PAYMENT
	InvoicePaid: langIds.InvoicePaidNotificationSubject,
	SubscriptionPaymentFailed: langIds.SubscriptionPaymentFailedNotificationSubject,
	PaymentsAccountDisabled: langIds.PaymentsAccountDisabledNotificationSubject,
	ServiceReceiptRequiresReview: langIds.ServiceReceiptRequiresReviewNotificationSubject,
	PayerEnrollmentRejected: langIds.EnrollmentRejectedSubject,
	InsuranceClaimRejected: langIds.InsuranceClaimRejectedSubject,
	InsuranceClaimPaid: lazy(() => import('./Subject/components/InsuranceClaimPaid')),
	InsuranceClaimPartiallyPaid: lazy(() => import('./Subject/components/InsuranceClaimPaid')),
	InsuranceClaimDenied: langIds.InsuranceClaimDeniedSubject,
	// WORKSPACE
	ReferralCredited: langIds.ReferralCreditedNotificationSubject,
	ReferralJoined: langIds.ReferralJoinedNotificationSubject,
	ImportContactFailed: langIds.ImportContactFailedNotificationSubject,
	ExportContactFailed: langIds.ExportContactFailedNotificationSubject,
	ConnectedAppDisconnected: langIds.ConnectedAppDisconnectedNotificationSubject,
	StaffContactAssigned: lazy(() => import('./Subject/components/StaffContactAssigned')),
	// COMMUNICATIONS
	StaffInboxAssigned: langIds.StaffInboxAssignedNotificationSubject,
	StaffInboxUnssigned: langIds.StaffInboxUnassignedNotificationSubject,
};

/**
 * Mapping of notification items body
 */
// @ts-expect-error - Not all notification types need to be handled
export const notificationsBodyMapping: Record<
	AppNotificationEventType,
	React.LazyExoticComponent<React.ComponentType<NotificationBodyProps>> | null
> = {
	AppointmentAssigned: lazy(() => import('./Body/components/AppointmentAssigned')),
	AppointmentCancelled: lazy(() => import('./Body/components/AppointmentCancelled')),
	AppointmentRescheduled: lazy(() => import('./Body/components/AppointmentRescheduled')),
	ClientAppointmentBooked: lazy(() => import('./Body/components/ClientAppointmentBooked')),
	FileUploaded: lazy(() => import('./Body/components/FileUploaded')),
	InvoicePaid: lazy(() => import('./Body/components/InvoicePaid')),
	NoteFormSubmitted: lazy(() => import('./Body/components/NoteFormSubmitted')),
	PublicFormSubmitted: lazy(() => import('./Body/components/NoteFormSubmitted')),
	PayerEnrollmentRejected: lazy(() => import('./Body/components/PayerEnrollmentRejected')),
	InsuranceClaimDenied: lazy(() => import('./Body/common/ViewClaimButton')),
	InsuranceClaimRejected: lazy(() => import('./Body/common/ViewClaimButton')),
	InsuranceClaimPaid: lazy(() => import('./Body/common/ViewClaimButton')),
	InsuranceClaimPartiallyPaid: lazy(() => import('./Body/common/ViewClaimButton')),
};

/**
 * Mapping of notification items action
 */
// @ts-expect-error - Not all notification types gonna need special view action
export const notificationsActionMapping: Record<
	AppNotificationEventType,
	React.LazyExoticComponent<React.ComponentType<ActionProps>> | null
> = {
	InvoicePaid: lazy(() => import('./Action/components/ViewInvoice')),
	ClientAddedNote: lazy(() => import('./Action/components/ViewNote')),
	ClientUpdatedNote: lazy(() => import('./Action/components/ViewNote')),
	NoteFormSubmitted: lazy(() => import('./Action/components/ViewNote')),
	PublicFormSubmitted: lazy(() => import('./Action/components/ViewNote')),
	TemplateImportCompleted: lazy(() => import('./Action/components/ViewTemplate')),
	TemplateImportFailed: lazy(() => import('./Action/components/ViewImportTemplateSidePanel')),
	AppointmentAssigned: lazy(() => import('./Action/components/ViewAppointment')),
	AppointmentRescheduled: lazy(() => import('./Action/components/ViewAppointment')),
	AppointmentConfirmed: lazy(() => import('./Action/components/ViewAppointment')),
	ClientAppointmentBooked: lazy(() => import('./Action/components/ViewAppointment')),
	SubscriptionPaymentFailed: lazy(() => import('./Action/components/NavigateToPage')),
	ImportContactFailed: lazy(() => import('./Action/components/OpenLink')),
	ExportContactFailed: lazy(() => import('./Action/components/OpenLink')),
	ConnectedAppDisconnected: lazy(() => import('./Action/components/NavigateToPage')),
	StaffInboxAssigned: lazy(() => import('./Action/components/NavigateToPage')),
	PaymentsAccountDisabled: lazy(() => import('./Action/components/NavigateToPage')),
	ServiceReceiptRequiresReview: lazy(() => import('./Action/components/ViewSuperbill')),
	PreprocessImportContactCompleted: lazy(() => import('./Action/components/ViewClientImport')),
};
