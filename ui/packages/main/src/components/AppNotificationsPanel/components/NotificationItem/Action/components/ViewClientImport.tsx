import { FormattedMessage } from 'react-intl';
import { useDispatch } from 'react-redux';
import { Typography } from '@mui/material';

import { useAppNotificationsPanelContext } from 'components/AppNotificationsPanel/AppNotificationsPanelContext';
import { extractParameters } from 'components/AppNotificationsPanel/components/utils';
import useMarkNotification from 'components/AppNotificationsPanel/hooks/useMarkNotification';
import { ImportClientsFormValuesV2 } from 'components/modals/ImportClients';
import langIds from 'lang/langIds';
import { addModal } from 'store/slices/modals/slice';
import { trackViewNotification } from 'util/notifications';

import { ActionProps, StyledButton } from '..';

const ViewImportTemplateSidePanelAction: React.FC<ActionProps> = ({ item, variant }) => {
	const dispatch = useDispatch();
	const { markAsRead } = useMarkNotification();
	const { closePanel } = useAppNotificationsPanelContext();

	const [importSummaryId, fileId, fileName, schemaFileId] = extractParameters(item.parameters, [
		'id',
		'fileId',
		'fileName',
		'schemaFileId',
	]);

	const onView = () => {
		trackViewNotification(item);

		const initialValues: ImportClientsFormValuesV2 = {
			fileId,
			fileName,
			importSummaryId: importSummaryId,
		};

		dispatch(
			addModal({
				type: 'ImportClients',
				data: {
					initialValues,
					schemaFileId,
				},
			})
		);
		closePanel();

		if (!item.isRead) {
			markAsRead(item.id);
		}
	};

	if (!fileId || !fileName || !schemaFileId) return null;

	return (
		<StyledButton onClick={onView}>
			<Typography variant='subtitle2'>
				<FormattedMessage id={langIds.View} />
			</Typography>
		</StyledButton>
	);
};

export default ViewImportTemplateSidePanelAction;
