import { useMemo } from 'react';
import { Extensions } from '@tiptap/core';
import Blockquote from '@tiptap/extension-blockquote';
import Bold from '@tiptap/extension-bold';
import BubbleMenu from '@tiptap/extension-bubble-menu';
import BulletList from '@tiptap/extension-bullet-list';
import Color from '@tiptap/extension-color';
import Document from '@tiptap/extension-document';
import Dropcursor from '@tiptap/extension-dropcursor';
import FontFamily from '@tiptap/extension-font-family';
import Gapcursor from '@tiptap/extension-gapcursor';
import HardBreak from '@tiptap/extension-hard-break';
import Heading from '@tiptap/extension-heading';
import Highlight from '@tiptap/extension-highlight';
import History from '@tiptap/extension-history';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Italic from '@tiptap/extension-italic';
import Link from '@tiptap/extension-link';
import ListItem from '@tiptap/extension-list-item';
import OrderedList from '@tiptap/extension-ordered-list';
import Paragraph from '@tiptap/extension-paragraph';
import Placeholder from '@tiptap/extension-placeholder';
import Strike from '@tiptap/extension-strike';
import Text from '@tiptap/extension-text';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Underline from '@tiptap/extension-underline';

import { CleanPasteTextHandler } from 'components/EditorV2/Chat/extensions/CleanPasteTextHandler';
import { FilePasteExtension } from 'components/EditorV2/Chat/extensions/FilePasteExtension';
import { BackspaceAtStart } from 'components/EditorV2/Core/extensions/backspaceAtStart/backspaceAtStart';
import { ImageEntity } from 'components/EditorV2/Core/extensions/image';
import { Indent } from 'components/EditorV2/Core/extensions/indent';
import LineHeight from 'components/EditorV2/Core/extensions/lineHeight';
import { SmartChip } from 'components/EditorV2/Core/extensions/smartChip';
import StartingContent from 'components/EditorV2/Core/extensions/startingContent';

export type OnImageUploadArgs = {
	file: File;
};

export type OnGetSecureImageUrlArg = {
	fileId: string;
	entity?: ImageEntity | string;
};

export type GetExtensionArgs = {
	editorId: string;
	onUploadImage?: (args: OnImageUploadArgs) => Promise<{ fileId: string; entity?: ImageEntity | string } | void>;
	onGetSecureImageUrl?: (args: OnGetSecureImageUrlArg) => Promise<string | void>;
	editable?: boolean;
	placeholder?: string;
	fileHandler?: (files: File[]) => Promise<void>;
};

/**
 * `getExtensions` is a function that initializes and configures Tiptap extensions for use in a chat editor instance.
 * It handles the setup of both built-in and custom extensions, including text styling and interactive components.
 *
 * @param args Configuration options for the extensions
 * @returns An array of initialized and configured extensions ready to be used in a Tiptap editor
 */
export const getExtensions = (args: GetExtensionArgs): Extensions => {
	return [
		SmartChip.configure({ selectedView: 'Edit' }),
		Blockquote,
		Bold,
		BubbleMenu,
		BulletList,
		Color.configure({
			types: ['textStyle'],
		}),
		Document,
		FontFamily,
		HardBreak,
		Heading.configure({
			HTMLAttributes: {
				style: 'margin:0',
			},
		}),
		Highlight.configure({
			multicolor: true,
		}),
		History,
		HorizontalRule,
		Italic,
		LineHeight,
		Link.configure({
			autolink: false,
			openOnClick: false,
			HTMLAttributes: {
				rel: 'noopener noreferrer',
				target: '_blank',
			},
		}),
		ListItem,
		OrderedList,
		Paragraph.configure({
			HTMLAttributes: {
				style: 'margin:0',
			},
		}),
		Placeholder.configure({
			placeholder: args.placeholder,
		}),
		Strike,
		Text,
		TextAlign.configure({
			types: ['heading', 'paragraph'],
		}),
		TextStyle,
		Underline,
		Indent.configure({
			types: ['heading', 'paragraph', 'listItem'],
			exclude: ['table'],
		}),
		StartingContent,
		BackspaceAtStart,
		Dropcursor.configure({
			width: 2,
		}),
		Gapcursor,
		CleanPasteTextHandler,
		// Add the file paste extension if a file handler is provided
		...(args.fileHandler
			? [
					FilePasteExtension.configure({
						fileHandler: args.fileHandler,
					}),
				]
			: []),
	];
};

// Function that gets all of the available Mark, Node, and Extension names to help filter unrecognized contents
export const getExtensionTypes = (args: GetExtensionArgs) => {
	const availableExtensions = getExtensions(args);

	const extensionTypesObj = {};

	availableExtensions.forEach(({ name }) => {
		extensionTypesObj[name] = name;
	});

	return extensionTypesObj;
};

/**
 * `useChatEditorExtension` is a custom hook that provides the extensions needed for a chat editor.
 * It memoizes the extensions to prevent unnecessary re-renders.
 *
 * @param args Configuration options for the extensions
 * @returns An array of initialized and configured extensions ready to be used in a Tiptap editor
 */
export const useChatEditorExtension = (args: GetExtensionArgs) => {
	const extensions = useMemo(() => getExtensions(args), [args]);

	return extensions;
};

export default useChatEditorExtension;
