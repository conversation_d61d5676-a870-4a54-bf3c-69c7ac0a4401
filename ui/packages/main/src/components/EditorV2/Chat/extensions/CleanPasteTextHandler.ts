import { Extension } from '@tiptap/core';
import { Plugin } from '@tiptap/pm/state';

/**
 * CleanPasteTextHandler is an extension that strips formatting from pasted content.
 * It intercepts the paste event and processes the content to remove any styling
 * before inserting it into the editor.
 */
export const CleanPasteTextHandler = Extension.create({
	name: 'cleanPasteTextHandler',

	addProseMirrorPlugins() {
		return [
			new Plugin({
				props: {
					handlePaste: (view, event, slice) => {
						// Get the plain text from the clipboard
						const text = event.clipboardData?.getData('text/plain');

						if (text) {
							// Insert the plain text at the current cursor position
							const { tr } = view.state;
							const transaction = tr.insertText(text);

							// Apply the transaction
							view.dispatch(transaction);

							// Return true to indicate that we've handled the paste event
							return true;
						}

						// Return false to let other paste handlers process the event
						return false;
					},
				},
			}),
		];
	},
});
