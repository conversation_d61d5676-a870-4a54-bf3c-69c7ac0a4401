import { useCallback, useMemo } from 'react';
import { Controller, useFieldArray, useFormContext } from 'react-hook-form';
import { FormattedMessage } from 'react-intl';
import Add from '@mui/icons-material/Add';
import DeleteForeverRoundedIcon from '@mui/icons-material/DeleteForeverRounded';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import { makeStyles } from '@mui/styles';

import { OverflowActionsPopperWrapper } from 'components/common/OverflowActionsPopper/OverflowActionsPopperWrapper';
import langIds from 'lang/langIds';
import { useGetContactQuery } from 'store/api/contacts';
import { selectEditorContextById } from 'store/selectors/editors';
import { ClientInfoFieldType } from 'types/client';
import { useParamSelector } from 'util/hooks';
import useBreakpoint from 'util/hooks/useBreakpoint';
import { toLowerCaseFirst } from 'util/string';
import { useEffectEvent } from 'util/useEffectEvent';

import { useFormFieldContext } from '../../hooks/useFormFieldContext';

import { DEFAULT_FIELD_VALUE } from './constants';
import DynamicField from './DynamicField';
import FieldSelector from './FieldSelector';
import { flatten } from './util';

type Props = {
	fieldId: string;
	mode: AccessLevel;
	active?: boolean;
	editorId?: string;
};

const useStyles = makeStyles({
	elevatedPaper: {
		boxShadow: '0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12)',
	},
});

const ClientInfo = ({ fieldId, mode, active = false, editorId = '' }: Props) => {
	const classes = useStyles();

	const { setFieldFocus, setFieldValue, control } = useFormFieldContext<'ClientInfo'>(fieldId);
	const { getValues } = useFormContext();
	const clientFieldsKey = `fields.${fieldId}.schema.clientFields` as const;
	const { fields, append, update, remove } = useFieldArray({
		name: clientFieldsKey,
		control,
	});

	// TODO: use EditorStateContext and template compatability
	const { contactId } = useParamSelector(selectEditorContextById<'Note'>, editorId);
	const { data: contact } = useGetContactQuery({ id: contactId ?? '' });

	const isMobile = useBreakpoint('sm');
	const isEditable = active && mode === 'Edit';

	const handleOnSelect = useEffectEvent((info: ClientInfoField, index: number) => {
		const rowIndex = fields[index]?.rowIndex || 0;
		const data = { ...info, rowIndex };

		// updates the schema client info list
		update(index, data);

		const value = contact?.[toLowerCaseFirst(data.property)] || data.value || '';

		// updates reponse value
		setFieldValue(
			`response.${data.property}`,
			{
				type: data.fieldType,
				value,
			},
			{ shouldSync: true }
		);

		setFieldFocus(`response.${data.property}.value`, true);
	});

	const handleAddField = () => {
		const rowIndex = fields[fields.length - 1]?.rowIndex || 0;

		append({ ...DEFAULT_FIELD_VALUE, rowIndex });
	};

	const handleAddRow = () => {
		const rowIndex = fields[fields.length - 1]?.rowIndex + 1 || 0;

		append({ ...DEFAULT_FIELD_VALUE, rowIndex });
		append({ ...DEFAULT_FIELD_VALUE, rowIndex });
		append({ ...DEFAULT_FIELD_VALUE, rowIndex });
	};

	const handleFieldPropertyChange = useCallback(
		(index: number) => (key: string, value: string) => {
			// Get current field data without relying on stale closure
			const currentFields = getValues(`fields.${fieldId}.schema.clientFields`);
			const currentField = currentFields[index];
			if (!currentField) return;

			const rowIndex = currentField.rowIndex || 0;
			const data = { ...currentField, rowIndex, [key]: value };

			// updates the schema client info list
			update(index, data);

			// Only update response for actual field property changes, not field configuration changes like defaultCountryCode
			if (key !== 'defaultCountryCode') {
				setFieldValue(`response.${data.property}`, { type: data.fieldType }, { shouldSync: true });
				setFieldFocus(`response.${data.property}.value`, true);
			}
		},
		[getValues, fieldId, update, setFieldValue, setFieldFocus]
	);

	const groupedFields = flatten(fields);

	const getOverflowActions = (field) => {
		const actions: ITableRowOverflowAction[] = [
			{
				key: 'delete',
				icon: <DeleteForeverRoundedIcon color='default' fontSize='small' />,
				label: <FormattedMessage id={langIds.RemoveField} />,
				onClick: () => {
					remove(field.fieldIndex);

					if (field.property) {
						setFieldValue(`response.${field.property}`, undefined, { shouldSync: true });
					}
				},
			},
		];

		return actions;
	};

	const selectedFields = useMemo(() => {
		return fields.filter((field) => field.property).map((field) => field.property);
	}, [fields]);

	return (
		<Box>
			<Box
				sx={{
					mt: isEditable ? 0 : 2,
				}}
			>
				{groupedFields.map((row, index) => (
					<Grid
						className='client-info-row'
						container
						key={`row-${index}`}
						columns={isMobile ? 1 : 3}
						rowSpacing={3}
						columnSpacing={2}
						sx={{ mb: 2 }}
					>
						{row.map((field) => (
							<Grid
								key={field.id}
								item
								xs={1}
								sx={(theme) => ({
									...(!isEditable && {
										'&.MuiGrid-item': {
											paddingTop: theme.spacing(1.5),
										},
									}),
								})}
							>
								<OverflowActionsPopperWrapper
									hidden={!isEditable}
									overflowActionsPopperProps={{
										overflowActions: getOverflowActions(field),
										disablePortal: true,
										contentProps: {
											sx: {
												mt: -2,
											},
											className: classes.elevatedPaper,
										},
									}}
								>
									{field.property && field.displayName !== 'Field label' ? (
										<Controller
											defaultValue=''
											name={`fields.${fieldId}.response.${field.property}.value`}
											render={({ field: controllerField, fieldState }) => {
												const errorMessage =
													mode === 'Responder' ? fieldState.error?.message : undefined;

												return (
													<DynamicField
														type={field.fieldType as ClientInfoFieldType}
														active={active || mode === 'Responder'}
														label={field.label || field.displayName}
														controllerRenderProps={controllerField}
														errorMessage={errorMessage}
														fieldProperty={field}
														onFieldPropertyChange={handleFieldPropertyChange(
															field.fieldIndex
														)}
													/>
												);
											}}
										/>
									) : isEditable ? (
										<FieldSelector
											onChange={handleOnSelect}
											index={field.fieldIndex}
											selectedFields={selectedFields}
										/>
									) : null}
								</OverflowActionsPopperWrapper>
							</Grid>
						))}
					</Grid>
				))}
			</Box>
			{isEditable && (
				<Stack direction='row' justifyContent='flex-start' alignItems='center' spacing={1}>
					{!isMobile && (
						<>
							<Button size='small' startIcon={<Add />} onClick={handleAddRow}>
								<FormattedMessage id={langIds.ClientInfoAddRow} />
							</Button>
							<Typography>
								<FormattedMessage id={langIds.Or} />
							</Typography>
						</>
					)}
					<Button size='small' startIcon={isMobile ? <Add /> : undefined} onClick={handleAddField}>
						<FormattedMessage id={langIds.ClientInfoAddField} />
					</Button>
				</Stack>
			)}
		</Box>
	);
};

export default ClientInfo;
