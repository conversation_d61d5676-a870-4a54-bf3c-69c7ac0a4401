import { ControllerRenderProps, FieldValues } from 'react-hook-form';
import TextField, { TextFieldProps } from '@mui/material/TextField';

import DatePicker from 'components/common/DatePicker';
import PhoneTextField from 'components/common/PhoneTextField/PhoneTextField';
import { parseDate } from 'components/EditorV2/Form/common/util';
import GooglePlacesAutoComplete from 'components/GooglePlacesAutoComplete';
import { ClientInfoFieldType } from 'types/client';
import { toDateOnlyFormat } from 'util/date';
import { emptyArray } from 'util/emptyArray';

import { isSameOrAfterMinimumSupportedYear } from '../../DatePicker/util';

import BooleanSwitchField from './BooleanSwitchField';
import ChoiceDropdownField from './ChoiceDropdownField';
import DateRangeField from './DateRangeField';
import EnumField from './EnumField';

type Props = {
	type: ClientInfoFieldType;
	active?: boolean;
	label: string;
	controllerRenderProps: ControllerRenderProps<FieldValues, `fields.${string}.response.${any}.value`>;
	errorMessage?: string;
	// Considering it's only being used for the client info fields
	// it's safe to assume it's a ClientInfoField, we can add more types if needed
	fieldProperty?: ClientInfoField;
	onFieldPropertyChange: (propertyKey: string, propertyValue: string) => void;
};

const readOnlyStyles = {
	'& .MuiInputBase-root.Mui-readOnly.Mui-focused': {
		borderColor: 'rgba(0, 0, 0, 0.3)',
	},
	'& .MuiInputBase-root.Mui-readOnly:hover': {
		borderColor: 'rgba(0, 0, 0, 0.3)',
	},
};

const DynamicField = ({
	type,
	label,
	active = false,
	controllerRenderProps: { ref, ...fieldProps },
	errorMessage,
	fieldProperty,
	onFieldPropertyChange,
}: Props) => {
	const defaultProps: TextFieldProps = {
		fullWidth: true,
		variant: 'outlined',
		label,
		InputProps: {
			readOnly: !active,
		},
		error: !!errorMessage,
		helperText: errorMessage ? errorMessage : undefined,
	};

	console.log('=== fieldProperty ===', fieldProperty);

	switch (type) {
		case 'text':
		case 'longText':
		case 'email':
			return (
				<TextField
					{...defaultProps}
					{...fieldProps}
					inputRef={ref}
					sx={readOnlyStyles}
					type={type === 'email' ? type : 'text'}
				/>
			);
		case 'phoneNumber':
			return (
				<PhoneTextField
					{...defaultProps}
					{...fieldProps}
					value={undefined}
					readOnly={!active}
					defaultValue={fieldProps.value}
					onValueChange={fieldProps.onChange}
					onCountryCodeChange={(countryCode) => onFieldPropertyChange('defaultCountryCode', countryCode)}
					defaultCountryCode={fieldProperty?.defaultCountryCode}
					inputRef={ref}
				/>
			);
		case 'date':
			return (
				<DatePicker
					readOnly={!active}
					handleDateChange={(date) => {
						const parseDateToString = toDateOnlyFormat(date);
						if (
							(date === null || parseDateToString) &&
							isSameOrAfterMinimumSupportedYear(parseDateToString)
						) {
							fieldProps.onChange(parseDateToString);
						}
					}}
					handleClear={() => fieldProps.onChange('')}
					value={parseDate(fieldProps.value)}
					componentProps={{
						actionBar: { actions: active ? ['clear'] : undefined },
					}}
					textFieldProps={{
						...defaultProps,
						size: 'small',
						inputRef: ref,
						sx: (theme) => ({
							...readOnlyStyles,
							'& .MuiInputBase-root': {
								paddingRight: 0,
							},
							'& .MuiInputBase-input': {
								fontSize: theme.typography.fontSize,
								paddingY: '5px',
							},
						}),
					}}
				/>
			);
		case 'location':
			const fieldValue: Address = fieldProps?.value ? fieldProps.value : {};

			return (
				<GooglePlacesAutoComplete
					readOnly={!active}
					placeholder='E.g. 721 Broadway'
					defaultValue={fieldValue?.streetAddress || ''}
					onAddressChange={(address) => {
						fieldProps.onChange(address);
					}}
					textFieldProps={defaultProps}
					{...fieldProps}
				/>
			);
		case 'singleChoiceDropdown':
		case 'multipleChoiceDropdown':
			const isMultiple = type === 'multipleChoiceDropdown';
			const value = fieldProps.value || (isMultiple ? emptyArray : null);

			return (
				<ChoiceDropdownField
					label={label}
					value={value}
					options={(fieldProperty?.options as IOptionSetValue[]) || emptyArray}
					onChange={fieldProps.onChange}
					error={!!errorMessage}
					errorMessage={errorMessage}
					readOnly={!active}
					disabled={!!fieldProps.disabled}
					multiple={isMultiple}
				/>
			);
		case 'yesOrNo':
			return (
				<BooleanSwitchField
					label={label}
					readOnly={!active}
					controllerRenderProps={{ ref, ...fieldProps }}
					errorMessage={errorMessage}
					falseDisplayName={fieldProperty?.falseDisplayName}
					trueDisplayName={fieldProperty?.trueDisplayName}
				/>
			);
		case 'dateRange':
			return (
				<DateRangeField
					value={fieldProps.value}
					onChange={fieldProps.onChange}
					readOnly={!active}
					error={!!errorMessage}
					errorMessage={errorMessage}
					label={label}
				/>
			);
		case 'enum':
			return (
				<EnumField
					label={label}
					value={fieldProps.value || []}
					options={(fieldProperty?.options as string[]) || emptyArray}
					onChange={fieldProps.onChange}
					readOnly={!active}
					error={!!errorMessage}
					errorMessage={errorMessage}
					disabled={!!fieldProps.disabled}
				/>
			);
		default:
			return null;
	}
};

export default DynamicField;
