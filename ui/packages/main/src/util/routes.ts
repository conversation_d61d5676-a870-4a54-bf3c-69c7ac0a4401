import { ComponentType, createElement, lazy } from 'react';
import { createSearchParams, generatePath } from 'react-router-dom';
import qs from 'qs';

import { MenuItem } from 'components/layout/NavigationDrawer/types';

import { ClaimTypeMap } from '../areas/Insurance/Claims/constants';

export const routes = (route: AllRoutes, queryParams?: string[]) => {
	const path = route.startsWith('/') ? route : `/${route}`;
	if (queryParams) {
		return `${path}?${queryParams.join('&')}`;
	}
	return path;
};

export function getAllQueryParams(toObj?: boolean, excludeParams?: string[]) {
	const params = qs.parse(window.location.search, { ignoreQueryPrefix: true });

	if (toObj) {
		const paramsObj = Array.from(Object.keys(params))
			.filter((param) => !excludeParams?.includes(param))
			.reduce((acc, val) => ({ ...acc, [val]: params[val] }), {});

		return paramsObj;
	}

	const paramsList = Array.from(Object.keys(params))
		.filter((param) => !excludeParams?.includes(param))
		.reduce((acc: string[], val) => {
			return [...acc, `${val}=${params[val]}`];
		}, []);

	return paramsList;
}

export function getQueryParamByName(name: string, url: string): string {
	if (!url) return '';

	name = name.replace(/[[\]]/g, '\\$&');
	var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
		results = regex.exec(url);
	if (!results) return '';
	if (!results[2]) return '';
	return decodeURIComponent(results[2].replace(/\+/g, ' '));
}

export function toQueryParamArray(queryParam: string, params: string[]): string {
	if (!params || params.length < 1) {
		return '';
	}

	return `${queryParam}=${params.join(`&${queryParam}=`)}`;
}

type PreloadableComponent<T extends ComponentType<any>> = T & {
	preload: () => Promise<void>;
};

export const lazyWithPreload = <T extends ComponentType<any>>(factory: () => Promise<{ default: T }>) => {
	let LoadedComponent: T | undefined;
	let factoryPromise: Promise<void> | undefined;

	const LazyComponent = lazy(factory);

	const loadComponent = () =>
		factory().then((module) => {
			LoadedComponent = module.default;
		});

	const Component = ((props) => createElement(LoadedComponent || LazyComponent, props)) as PreloadableComponent<T>;

	Component.preload = () => factoryPromise || loadComponent();

	return Component;
};

export function extractCurrentSubheader<T extends string>(
	pathname: string,
	subheaderList: T[],
	option?: { pathIndex?: number }
) {
	const pathIndex = option?.pathIndex || 2;
	const parts = pathname.split('/').filter((part) => !!part);
	const path = (parts[pathIndex] || '').toLowerCase();

	const subHeaderIndex = subheaderList.findIndex((subheader) => subheader.toLowerCase() === path);
	const isPathInSubheaderList = subHeaderIndex !== -1;

	if (isPathInSubheaderList) {
		return subheaderList[subHeaderIndex];
	}
	return subheaderList[0];
}

export function getProviderAppMainHeader(parts: string[]): SelectedProviderHeader {
	const section = parts[0]?.toLocaleLowerCase();

	switch (section) {
		case 'calendar':
			if (parts?.[1] === 'Settings') return 'CalendarSettings';
			return 'Calendar';
		case 'clients':
			if (parts[1]) {
				if (parts[1] === 'Duplicates' && parts[2] === 'Merge') return 'ClientDuplicatesMerge';
				if (parts[1] === 'Duplicates') return 'ClientDuplicates';
				return 'SelectedClient';
			}
			return 'Clients';
		case 'invoices':
			return 'Invoices';
		case 'billing':
			return 'Billing';
		case 'staff':
			if (parts[1]) return 'SelectedStaff';
			return 'Staff';
		case 'contacts':
			if (parts[1]) return 'SelectedContact';
			return 'Contacts';
		case 'templates':
			// TODO: add a feature flag here
			if (parts[2]) return 'TemplatesV2FoldersSubPage';
			if (parts[1]) return 'TemplatesV2SubPage';
			return 'Templates';
		case 'templatesv2':
			if (parts[2]) return 'TemplatesV2FoldersSubPage';
			if (parts[1]) return 'TemplatesV2SubPage';
			return 'TemplatesV2';
		case 'settings':
			if (parts[1] === 'Services' && !!parts[2]) return 'SelectedService';
			return 'Settings';
		case 'inbox':
			return 'Inbox';
		case 'trash':
			return 'Trash';
		default:
			return 'Default';
	}
}

export function getClientPortalMainHeader(parts: string[]): SelectedClientHeader {
	const section = parts[1]?.toLocaleLowerCase(); // Client portal routes are prefixed with "Portal", e.g. "Portal/Documentation"

	switch (section) {
		case 'dashboard':
			return 'ClientPortalDashboard';
		case 'documentation':
			return 'ClientPortalDocumentation';
		case 'myrelationships':
			if (parts[2]) return 'ClientPortalSelectedRelationship';
			return 'ClientPortalMyRelationships';
		case 'settings':
			return 'ClientPortalSettings';
		default:
			return 'Default';
	}
}

export function getProviderAppRoute(pathname: string): ProviderRoute {
	const paths = pathname.split('/');
	const firstPath = (paths.length <= 1 ? pathname : paths[1])?.toLocaleLowerCase();

	switch (firstPath) {
		case '':
			return 'Inbox';
		case 'clients':
			return 'Clients';
		case 'feed':
			return 'Feed';
		case 'contacts':
			return 'Contacts';
		case 'settings':
			return 'Settings';
		case 'staff':
			return 'Staff';
		case 'inbox':
			return 'Inbox';
		case 'invite':
			return 'Invite';
		case 'calendar':
			return 'Calendar';
		case 'invoices':
			return 'Invoices';
		case 'templates':
			return 'Templates';
		case 'templatesv2':
			return 'TemplatesV2';
		case 'gettingstarted':
			return 'GettingStarted';
		case 'login':
			return 'Login';
		case 'signup':
			return 'Signup';
		case 'register':
			return 'Register';
		case 'onboarding':
			return 'Onboarding';
		case 'billing':
			return 'Billing';
	}
	return 'Settings';
}

export function getClientPortalRoute(pathname: string): ClientPortalRoute {
	const paths = pathname.split('/');
	// Client portal routes have prefix "Portal", e.g. "Portal/Documentation"
	const path = paths[2]?.toLocaleLowerCase();

	switch (path) {
		case 'dashboard':
			return 'Portal/Dashboard';
		case 'documentation':
			return 'Portal/Documentation';
		case 'myrelationships':
			return 'Portal/MyRelationships';
		case 'settings':
			return 'Portal/Settings';
		default:
			return 'Portal/Documentation';
	}
}

export function createUrlWithToken(uri: string, token: string) {
	const url = new URL(uri);
	if (token) url.searchParams.set('X-CP-Token', token);
	return url.toString();
}

export function addTokenToPath(
	path: string,
	token?: string,
	options?: { params?: Record<string, unknown>; searchParams?: Record<string, string> }
) {
	const tokenParams = token ? { 'X-CP-Token': token } : null;
	const searchParams = createSearchParams({ ...options?.searchParams, ...tokenParams });
	const queryParams = searchParams.toString();
	return `${generatePath(path, options?.params)}${queryParams ? '?' + queryParams : ''}`;
}

export const getSettingsOptionParts = (pathname: string, paramId?: string) => {
	const [, settingsOption, settingsOptionSubRoute, ...parts] = pathname.slice(1, pathname.length).split('/');
	const hasSubId = !!parts?.[0];
	const hasParamId = !!paramId;
	const firstPartOrEmpty = !hasParamId ? parts[0] : '';
	const subId = hasSubId && hasParamId ? paramId : firstPartOrEmpty;
	return [settingsOption, settingsOptionSubRoute, subId] as [SettingsOptionsRoute, SettingsOptionsSubRoutes, string];
};

export const getHomePageRoute = ({
	isProvider,
	redirect,
	isLoadingPermissions,
	menuItems,
}: {
	isProvider: boolean;
	isLoadingPermissions: boolean;
	menuItems: MenuItem[];
	redirect?: AllRoutes;
}) => {
	// If the user is not a provider user, redirect to the portal dashboard.
	if (!isProvider) return routes('Portal/Dashboard');

	// If there is a redirect query param
	if (redirect) return `${routes(redirect)}`;

	// If permissions are still loading, return an empty string, some menu items require permissions
	if (isLoadingPermissions) return '';

	// Default to the first route with a route property
	return menuItems.filter((menuItem) => !!menuItem.route)[0]?.route ?? '';
};

export const getClaimDetailsRoute = (contactId?: string, type?: ClaimType, id?: string) => {
	if (!contactId || !type || !id) return '';
	return `${routes('Clients')}/${contactId}/Billing/${ClaimTypeMap[type]}/${id}`;
};
