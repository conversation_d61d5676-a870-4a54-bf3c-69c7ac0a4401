name: 'CDK: service-discovery Tests'
on:
  pull_request:
    branches:
      - master
    paths:
      - infra/stacks/application/**/service-discovery.ts
      - infra/stacks/application/**/serviceDiscoveryConfig.ts
      - infra/stacks/application/**/config.base.ts
      - infra/stacks/application/**/config.service-discovery.ts
      - .github/workflows/cdk-service-discovery-*.yml
jobs:
    run-cdk-tests:
        uses: Carepatron/github-shared-workflows/.github/workflows/cdk_test_base.yml@master
        with:
            working-directory: ./infra/stacks/application
            stack-name: service-discovery
            role-to-assume: arn:aws:iam::907270438193:role/github-actions-minimal-role
            aws-region: ap-southeast-2
            aws-environment: staging