﻿using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using System;

namespace carepatron.api.Contracts.Requests.Booking
{
    public class BookingIntentRequest
    {
        public Guid StaffId { get; set; }

        public Guid ItemId { get; set; }

        public Guid? LocationId { get; set; }

        public LocationType LocationType { get; set; }

        public VirtualLocationProductType? VirtualLocationProduct { get; set; }

        public bool? IsOnline { get; set; }

        public DateTime StartDate { get; set; }

        public DateTime EndDate { get; set; }

        public BookingContactDetail ContactDetails { get; set; }

        public string Message { get; set; }

        public string TimeZone { get; set; }

        public Guid? TaskId { get; set; }
        
        public BookingRelatedContactDetail RelatedContactDetails { get; set; }
        
        public string Token { get; set; }
    }
}
