﻿using carepatron.api.Contracts.Requests.Booking;
using carepatron.core.Application.Booking.Commands;
using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Booking.Queries;
using carepatron.core.Identity;
using carepatron.core.Models.Common;
using carepatron.core.Models.Pagination;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Localisation;

namespace carepatron.api.Controllers.Bookings
{
    [ControllerProperties(CodeOwner.TasksAndScheduling)]
    [Route("api/booking/{providerId}")]
    [ApiController]
    [AllowAnonymous]
    public class BookingController : BaseController
    {
        private readonly IMediator mediator;

        public BookingController(IIdentityContextFactory identityContextFactory, IMediator mediator)
            : base(identityContextFactory)
        {
            this.mediator = mediator;
        }

        [HttpGet("~/api/booking/context")]
        public async Task<BookingContext> GetContext(
            [FromQuery] Guid? providerId,
            [FromQuery] Guid? staffId = null,
            [FromQuery] Guid? itemId = null,
            [FromQuery] Guid? taskId = null,
            [FromQuery] string staffIdPart = null,
            [FromQuery] string itemIdPart = null,
            [FromQuery] string contactIdPart = null,
            [FromQuery] string token = null)
        {
            var query = new GetBookingContextQuery(providerId, staffId, itemId, taskId, staffIdPart, itemIdPart, contactIdPart, token);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpGet("items")]
        public async Task<PaginatedResult<BookingProviderItem>> GetBookingItems(
            [FromRoute] Guid providerId,
            [FromQuery] Guid? staffId = null,
            [FromQuery] Guid? providerItemGroupId = null,
            [FromQuery] bool? allowNewClients = null)
        {
            var query = new GetBookingItemsQuery(providerId, allowNewClients, staffId, providerItemGroupId);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpGet("itemGroups")]
        public async Task<PaginatedResult<BookingProviderItemGroup>> GetBookingItemGroups([FromRoute] Guid providerId, [FromQuery] Guid? staffId = null)
        {
            var query = new GetBookingItemGroupsQuery(providerId, staffId);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpGet("staff")]
        public async Task<List<BookingStaffMemberNextAvailability>> GetBookingStaff([FromRoute] Guid providerId, [FromQuery] GetBookingStaffRequest request)
        {
            // explicitly specify locationId for non video calls
            // locationIds can be physical and/or virtual locations
            Guid[] locationIds = request.LocationIds ?? Array.Empty<Guid>();
            if (request.LocationId.HasValue)
                locationIds = locationIds.Concat(new Guid[] { request.LocationId.Value }).ToArray();

            var query = new GetBookingStaffQuery(providerId,
                request.ItemId,
                locationIds,
                request.IsOnline,
                request.TimeZone);
            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpGet("availability/times")]
        public async Task<Dictionary<DateOnly, List<BookingStaffAvailableTimeBlock>>> GetBookingAvailability([FromRoute] Guid providerId, [FromQuery] GetBookingAvailabilityTimesRequest request)
        {
            // explicitly specify locationId for non video calls
            // locationIds can be physical and/or virtual locations
            Guid[] locationIds = request.LocationIds ?? Array.Empty<Guid>();
            if (request.LocationId.HasValue)
                locationIds = locationIds.Concat(new Guid[] { request.LocationId.Value }).ToArray();

            var query = new GetBookingStaffTimeBlockAvailabilityQuery(providerId,
                request.StaffId ?? Array.Empty<Guid>(),
                request.ItemId,
                locationIds,
                new DateRange(request.StartDateTimeUtc, request.EndDateTimeUtc),
                request.TimeZone,
                request.IsOnline.GetValueOrDefault());

            var result = await mediator.Send(query);
            return ExecutionResult(result);
        }

        [HttpPut("tasks/{taskId}")]
        public async Task<Guid> UpdateBookingAppointment([FromRoute] Guid taskId, [FromBody] UpdateBookingRequest request)
        {
            var result = await mediator.Send(new RescheduleBookingCommand(taskId, request.ContactId, new DateRange(request.StartDate, request.EndDate)));

            return ExecutionResult(result);
        }

        [HttpPost("bookingIntent")]
        public async Task<BookingIntentResponse> CreateBookingIntent([FromRoute] Guid providerId,
            [FromHeader(Name = "Accept-Language")] string locale,
            [FromBody] BookingIntentRequest request)
        {
            var command = new CreateBookingIntentCommand(
                providerId,
                request.StaffId,
                request.ItemId,
                request.LocationId,
                request.IsOnline,
                new DateRange(request.StartDate, request.EndDate),
                request.ContactDetails,
                request.Message,
                request.TimeZone,
                Localization.GetCultureOrDefault(locale),
                request.LocationType,
                request.VirtualLocationProduct,
                request.TaskId,
                request.RelatedContactDetails,
                request.Token);
            var result = await mediator.Send(command);

            return ExecutionResult(result);
        }

        [HttpPost("confirm")]
        public async Task<Guid> ConfirmBookingIntent([FromRoute] Guid providerId, [FromBody] ConfirmBookingRequest request)
        {
            var result = await mediator.Send(new ConfirmBookingIntentCommand(
                providerId,
                request.BookingIntentId,
                request.PaymentMethodId,
                request.PaymentIntentId,
                request.CustomerId));

            return ExecutionResult(result);
        }

        [HttpGet("~/api/booking/recommended")]
        [Authorize]
        public async Task<BookingRecommendation> GetRecommendedBookings()
        {
            var query = new GetBookingRecommendationQuery(IdentityContext.PersonId);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }

        [HttpGet("staff/{staffId}/locations")]
        public async Task<BookingProviderLocationResponse> GetBookingProviderLocations([FromRoute] Guid providerId, [FromRoute] Guid staffId, [FromQuery] Guid itemId)
        {
            var query = new GetBookingProviderLocationsQuery(providerId, staffId, itemId);
            var result = await mediator.Send(query);

            return ExecutionResult(result);
        }
    }
}
