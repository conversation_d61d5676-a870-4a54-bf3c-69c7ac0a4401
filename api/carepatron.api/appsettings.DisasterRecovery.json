﻿{
  "InfrastructureConfig": {
    "Environment": "DisasterRecovery"
  },
  "CorsPolicy": {
      "Name": "default",
      "Origins": [
          "https://app.carepatron.com",
          "https://book.carepatron.com",
          "https://form.carepatron.com"
      ],
      "Headers": [
          "Content-Type",
          "X-Amz-Date",
          "Authorization",
          "X-Api-Key",
          "X-Amz-Security-Token",
          "X-Forwarded-For"
      ]
  },
  "Logging": {
    "Region": "us-east-2",
    "LogGroup": "/ecs/carepatron-api-production",
    "IncludeLogLevel": true,
    "IncludeCategory": true,
    "IncludeNewline": true,
    "IncludeException": true,
    "IncludeEventId": false,
    "IncludeScopes": false,
    "LogLevel": {
      "Default": "Information",
      "System": "Information",
      "Microsoft": "Information"
    }
  },
  "Serilog": {
    "MinimumLevel": {
      "Override": {
        "Microsoft.EntityFrameworkCore.Database.Command": "Warning"
      }
    }
  },
  "AwsConfiguration": {
      "AccessKey": "",
      "SecretKey": "",
      "Region": "us-east-2",
      "ServiceURL": ""
  },
  "CognitoConfig": {
    "AccessKey": "",
    "SecretKey": "",
    "PoolId": "ap-southeast-2_dqGsAXZDG",
    "ClientId": "6t48kmi1ksaifr6vpm6dap6obg",
    "ServiceEndpoint": "https://cognito-idp.ap-southeast-2.amazonaws.com"
  },
  "ConnectionStrings": {
      "CarePatronDb": "set_in_ecs_env"
  },
  "S3Config": {
      "DefaultRegion": "ap-southeast-2",
      "DefaultServiceUrl": "https://s3.ap-southeast-2.amazonaws.com/",
      "ForcePathStyle": true,
      "BucketDefinitions": {
          "Files": {
              "BucketName": "carepatron-files",
              "BucketUrl": "https://carepatron-files.s3.ap-southeast-2.amazonaws.com/"
          },
          "ClientImport": {
              "BucketName": "carepatron-client-import-bucket-production",
              "BucketUrl": "https://carepatron-client-import-bucket-production.s3.us-east-1.amazonaws.com/",
              "Region": "us-east-1",
              "ServiceUrl": "https://s3.us-east-1.amazonaws.com/"
          },
          "InternalBlobStorage": {
              "BucketName": "carepatron-monolith-internal-blob-storage-bucket-production",
              "BucketUrl": "https://carepatron-monolith-internal-blob-storage-bucket-production.s3.us-east-1.amazonaws.com/",
              "Region": "us-east-1",
              "ServiceUrl": "https://s3.us-east-1.amazonaws.com/"
          },
          "Transcription": {
              "BucketName": "carepatron-transcription-bucket-production",
              "BucketUrl": "https://carepatron-transcription-bucket-production.s3.us-east-1.amazonaws.com/",
              "Region": "us-east-1",
              "ServiceUrl": "https://s3.us-east-1.amazonaws.com/"
          },
          "ClientExport": {
              "BucketName": "carepatron-client-export-bucket-production",
              "BucketUrl": "https://carepatron-client-export-bucket-production.s3.us-east-1.amazonaws.com/",
              "Region": "us-east-1",
              "ServiceUrl": "https://s3.us-east-1.amazonaws.com/"
          },
          "ChimeCalls": {
              "BucketName": "carepatron-chime-media-capture-bucket-production",
              "BucketUrl": "https://carepatron-chime-media-capture-bucket-production.s3.ap-southeast-2.amazonaws.com/"
          },
          "Export": {
              "BucketName": "carepatron-data-export-bucket-production",
              "BucketUrl": "https://carepatron-data-export-bucket-production.s3.us-east-1.amazonaws.com/",
              "Region": "us-east-1",
              "ServiceUrl": "https://s3.us-east-1.amazonaws.com/"
          },
          "PublicFiles": {
              "BucketName": "carepatron-files-public",
              "BucketUrl": "https://carepatron-files-public.s3.ap-southeast-2.amazonaws.com/"
          }
      }
  },
  "ChimeConfig": {
      "AccessKey": "",
      "SecretKey": "",
      "Region": "ap-southeast-2",
      "CallUrl": "https://app.carepatron.com/Calls/{id}",
      "ClientCallUrl": "https://app.carepatron.com/ClientCalls/{id}",
      "MeetingArn": "arn:aws:chime::418502688622:meeting",
      "MediaCaptureBucketArn": "arn:aws:s3::418502688622:carepatron-chime-media-capture-bucket-production"
  },
  "DynamoDbConfig": {
      "AccessKey": "",
      "SecretKey": "",
      "ServiceUrl": "https://dynamodb.ap-southeast-2.amazonaws.com/"
  },
  "SimpleEmailConfig": {
      "AccessKey": "",
      "SecretKey": "",
      "ServiceUrl": "https://ses.ap-southeast-2.amazonaws.com/",
      "AcceptInviteUrl": "https://app.carepatron.com/Register",
      "Region": "ap-southeast-2",
      "PublicFilesBucketUrl": "https://carepatron-files-public.s3.ap-southeast-2.amazonaws.com/",
      "GoingLink": "https://app.carepatron.com/AppointmentResponse/{id}/Accept?cId={cId}&startDate={startDate}",
      "CantGoLink": "https://app.carepatron.com/AppointmentResponse/{id}/Reject?cId={cId}&startDate={startDate}",
      "VideoCallLink": "https://app.carepatron.com/ClientCalls?email={email}&type=client",
      "PayNowLink": "https://app.carepatron.com/Invoices/{id}/Pay",
      "ClientEnrolmentLink": "https://app.carepatron.com/ClientOnboard/{contactId}?token={token}",
      "ViewInvoiceLink": "https://app.carepatron.com/Invoices/{id}/View",
      "ViewPaidInvoiceLink": "https://app.carepatron.com/Invoices/{id}",
      "ChangeEmailVerificationLink": "https://app.carepatron.com/VerifyEmail?pid={0}&token={1}",
      "StripeDashboardLink": "https://dashboard.stripe.com",
      "InternalStripeSupportAddresses": "<EMAIL>;<EMAIL>;<EMAIL>",
      "ForgotPasswordLink": "https://app.carepatron.com/ForgotPasswordConfirm?cc={0}",
      "InternalRiskAssessmentReviewAddresses": "<EMAIL>;<EMAIL>"
  },
  "StripeConfig": {
      "ApiKey": "{{StripeConfigApiKey}}",
      "RefreshUrl": "https://app.carepatron.com/settings/billing/details?redirect=true",
      "ReturnUrl": "https://app.carepatron.com",
      "WebhookSecret": "{{StripeWebhookSecret}}",
      "WebhookConnectSecret": "",
      "PartnerWebhookSecret": "",
      "ValidateWebhookSignature": true,
      "AccountId": "acct_1IQhinKbTpdLcdJu",
      "StripeAuthUri": "https://connect.stripe.com/oauth/token",
      "StripeClientId": "ca_J2nNoF8JFehsoIdlwlRxB6shdSCoBrOE"
  },
  "SubscriptionConfiguration": {
      "Products": {
          "Professional": {
              "Id": "prod_M7ZnIDLurEIjb0",
              "Pricing": {
                  "Annually": "price_1LPKeNKbTpdLcdJuEEL6mz1a",
                  "Monthly": "price_1LPKdXKbTpdLcdJuBN5lnWxz"
              }
          },
          "Organization": {
              "Id": "prod_JKPmH2ZexpUWGA",
              "Pricing": {
                  "Annually": "price_1MtM6pKbTpdLcdJu9VAaMEun",
                  "Monthly": "price_1JbXrnKbTpdLcdJuKPbLSdly"
              }
          },
          "Essential": {
              "Id": "prod_R9sufgva3Re0l9",
              "Pricing": {
                  "Annually": "price_1QHZ94KbTpdLcdJuKWCurE0d",
                  "Monthly": "price_1QHZ94KbTpdLcdJuHIlqIas0"
              }
          },
          "Plus": {
              "Id": "prod_R9suONEwTUqoPQ",
              "Pricing": {
                  "Annually": "price_1QHZ96KbTpdLcdJu0ATCE6DJ",
                  "Monthly": "price_1QHZ96KbTpdLcdJutP47lDlY"
              }
          },
          "Advanced": {
              "Id": "prod_R9sud57bQv94TS",
              "Pricing": {
                  "Annually": "price_1QHZ98KbTpdLcdJuOHLdhcDt",
                  "Monthly": "price_1QHZ98KbTpdLcdJusz8WnN6G"
              }
          }
      },
      "FirstTimeSubscriberCodes": {
          "Annually": "Xu6ULgTn",
          "Monthly": "ekEao4Qk"
      }
  },
  "WebPush": {
      "PublicKey": "BJRpjx8ELPfaVRQnTQXOg9cwy7IuRp5q19gJqE_kzVOS671Gw-D6_T6b7tL-mQWGSfo6v3ruBgH8Ou3uHfkKq68",
      "PrivateKey": "0GBzP48XK-SrAkij3TxNjx3w1HJDhZP5sLdd0zgYyp8"
  },
  "CalendarConfig": {
      "GoogleAuthUri": "https://oauth2.googleapis.com/token",
      "GoogleClientId": "546220662871-s2v1ag2b0e9gtrseis8554m7tskoj3vm.apps.googleusercontent.com",
      "GoogleClientSecret": "",
      "MicrosoftAuthUri": "https://login.microsoftonline.com/common/oauth2/v2.0/token",
      "MicrosoftClientId": "8e40b48a-75b7-45b8-ab74-6a2941caee53",
      "MicrosoftClientSecret": "",
      "RedirectUri": "https://app.carepatron.com/api/connectedapps/oauth/callback",
      "CarepatronClient": "https://app.carepatron.com/Calendar",
      "CallsUri": "https://app.carepatron.com/Calls/{callId}",
      "MicrosoftCarepatronNotificationsUri": "https://app.carepatron.com/api/connectedapps/notifications/microsoft",
      "GoogleCarepatronNotificationsUri": "https://app.carepatron.com/api/connectedapps/notifications/google",
      "GoogleCalendarCarepatronWebhookUri": "https://app.carepatron.com/api/webhooks/google/calendar",
      "ZoomClientId": "q4vJLySGStiPfJyymwM8eQ",
      "ZoomClientSecret": ""
  },
  "SqsConfiguration": {
      "AccessKey": "",
      "SecretKey": "",
      "Region": "us-east-2",
      "ServiceURL": "",
      "TaskQueueUrl": "https://sqs.us-east-2.amazonaws.com/418502688622/tasks-queue-ohio.fifo",
      "TranscriptionQueueUrl": "https://sqs.us-east-2.amazonaws.com/418502688622/documentation-transcriptions-queue"
  },
  "SqsQueueConfiguration": {
      "Queues": {
          "ClaimMdMessageQueue": {
              "QueueUrl": "https://sqs.us-east-2.amazonaws.com/418502688622/claimmd-queue-ohio.fifo",
              "Region": "us-east-2"
          }
      }
  },
  "CommonConfiguration": {
      "CarepatronApiUri": "https://app.carepatron.com/",
      "CarepatronUiUri": "https://app.carepatron.com/",
      "CarepatronBookingUiUri": "https://book.carepatron.com/"
  },
  "EventBridgeConfiguration": {
      "ServiceURL": "",
      "EventBusName": "ohio-carepatron-events",
      "AccessKey": "",
      "SecretKey": "",
      "Region": "us-east-2",
      "SchedulerQueueDestinationArn": "arn:aws:sqs:us-east-2:418502688622:tasks-queue-ohio.fifo",
      "SchedulerQueueRoleArn": "arn:aws:iam::418502688622:role/scheduler-sqs-role-ohio"
  },
  "ApiDestinationConfiguration": {
      "ApiDestinationArn": "arn:aws:events:us-east-2:418502688622:api-destination/ohio-carepatron-api-destination/0e990cb5-6c80-4b54-bbe7-c5188bdaafa2",
      "InvokeApiDestinationRoleArn": "arn:aws:iam::418502688622:role/ohio-api-events-stack-InvokeApiRole-JmD1lrA81V8H",
      "DeadLetterQueueArn": "arn:aws:sqs:us-east-2:418502688622:ohio-api-events-stack-ApiDestinationDLQ-fAX8WQJRxJiE",
      "BindingPrefix": "ohio-carepatron-api"
  },
  "ApiKeyAuthConfiguration": {
      "ApiKeySecretId": "ohio-carepatron-api-api-secret"
  },
  "SecretsManagerConfiguration": {
      "ServiceURL": "",
      "AccessKey": "",
      "SecretKey": "",
      "Region": "us-east-2"
  },
  "RecaptchaConfig": {
      "Secret": "",
      "GoogleReCaptchaUri": "https://www.google.com/recaptcha/api/siteverify"
  },
  "GoogleVertexAIConfiguration": {
      "ProjectId": "carepatron",
      "Publisher": "google",
      "Type": "service_account",
      "PrivateKey": "",
      "PrivateKeyId": "8bd5e2b0c9df7e8d1268730310bab214fb16f245",
      "ClientEmail": "<EMAIL>",
      "ClientId": "114396689439008457959",
      "AuthUri": "https://accounts.google.com/o/oauth2/auth",
      "TokenUri": "https://oauth2.googleapis.com/token",
      "AuthProviderX509CertUrl": "https://www.googleapis.com/oauth2/v1/certs",
      "ClientX509CertUrl": "https://www.googleapis.com/robot/v1/metadata/x509/vertex-account%40carepatron.iam.gserviceaccount.com",
      "UniverseDomain": "googleapis.com"
  },
  "GooglePubSubConfiguration": {
      "ProjectId": "carepatron",
      "TopicId": "projects/carepatron/topics/inbox-sync",
      "SubscriptionId": "projects/carepatron/subscriptions/inbox-sync-sub",
      "ServiceAccountId": "<EMAIL>",
      "AuthProviderX509CertUrl": "https://www.googleapis.com/oauth2/v1/certs"
  },
  "ProviderProvisioningConfiguration": {
      "DefaultTemplateIds": [{}],
      "TemplateConfigurations": [
          {
              "Country": "US",
              "Profession": "Psychologist",
              "TemplateIds": [{}]
          },
          {
              "Country": "NZ",
              "Profession": "Physiotherapist",
              "TemplateIds": [{}]
          }
      ]
  },
  "TranscriptionConfiguration": {
      "MediaFormat": "mp3",
      "ServiceUrl": "https://s3.us-east-1.amazonaws.com/",
      "OutputBucketName": "carepatron-transcription-bucket-production"
  },
  "GoogleTranscriptionConfiguration": {
      "ProjectId": "carepatron",
      "BucketName": "us-carepatron-ai-scribe-production",
      "Publisher": "google",
      "Type": "service_account",
      "PrivateKey": "",
      "PrivateKeyId": "1d6da999a8d1c9d5279537209f993d7413641f41",
      "ClientEmail": "<EMAIL>",
      "ClientId": "114396689439008457959",
      "AuthUri": "https://accounts.google.com/o/oauth2/auth",
      "TokenUri": "https://oauth2.googleapis.com/token",
      "AuthProviderX509CertUrl": "https://www.googleapis.com/oauth2/v1/certs",
      "ClientX509CertUrl": "https://www.googleapis.com/robot/v1/metadata/x509/vertex-account%40carepatron.iam.gserviceaccount.com",
      "UniverseDomain": "googleapis.com"
  },
  "PostHogConfiguration": {
      "BaseUrl": "https://post.t.carepatron.com",
      "ProjectApiKey": "phc_VdUkyfC2Uae7em1DPbDNB9y1ZrmLbHSsu44I8fhnXvp"
  },
  "AiTemplatesConfiguration": {
      "RecommendedAiTemplateIds": [
          "b829ccf0-3689-4952-a941-21be84ff429b",
          "1cea3ba7-b940-4ee9-bd2e-d648dca262d1",
          "2126854a-e2d1-44cc-a74c-b3288d6989ae",
          "6ade0179-ca56-461b-a2fd-299b10747dce",
          "bdeeedb9-b541-4b63-82d3-d264ee98bbaf",
          "036a8dbb-4ae0-4116-80fc-a23c631f742b",
          "d3fcb322-d291-46c9-bff2-1b507dd2c320",
          "d4a0a6a1-53ba-4f43-b4c4-0d4a8035535a",
          "39896ab7-405f-4724-adf6-62f349a7d3ff"
      ],
      "DefaultAiTemplateId": "1cea3ba7-b940-4ee9-bd2e-d648dca262d1"
  },
  "PartnerStackConfiguration": {
      "BaseUrl": "https://api.partnerstack.com",
      "PublicKey": "",
      "SecretKey": "",
      "ConversionUrl": "https://partnerlinks.io/conversion/xid",
      "ConversionToken": "",
      "IsTestMode": false
  },
  "GoogleAgentConfiguration": {
      "ProjectId": "carepatron",
      "Publisher": "google",
      "Type": "service_account",
      "PrivateKey": "",
      "PrivateKeyId": "8bd5e2b0c9df7e8d1268730310bab214fb16f245",
      "ClientEmail": "<EMAIL>",
      "ClientId": "114396689439008457959",
      "AuthUri": "https://accounts.google.com/o/oauth2/auth",
      "TokenUri": "https://oauth2.googleapis.com/token",
      "AuthProviderX509CertUrl": "https://www.googleapis.com/oauth2/v1/certs",
      "ClientX509CertUrl": "https://www.googleapis.com/robot/v1/metadata/x509/vertex-account%40carepatron.iam.gserviceaccount.com",
      "UniverseDomain": "googleapis.com"
  },
  "PdfUtilsClient": {
    "FunctionName": "carepatron-pdf-utils-function"
  },
  "TemplateImportUtilsClient": {
    "FunctionName": "carepatron-template-import-utils-function"
  },
  "LambdaConfig": {
    "ServiceURL": "",
    "AccessKey": "",
    "SecretKey": "",
    "AwsRegion": "ap-southeast-2"
  },
  "MicrosoftOutlookConfiguration": {
    "NotificationUrl": "https://app.carepatron.com/api/webhooks/microsoft/outlook",
    "ChangeType": "created,deleted",
    "Resource": "me/mailfolders('inbox')/messages",
    "ExpirationInMinutes": 10070,
    "RetryTimes": 3,
    "RetryDelayInSeconds": 5
  },
  "Redis": {
    "ConnectionString": "set_via_environment_variable",
    "ElastiCacheAuthToken": { "authToken": "prod-elasticache-auth-secret" },
    "InstanceName": "",
    "ConnectRetry": 3,
    "ConnectTimeout": 5000
  }
}