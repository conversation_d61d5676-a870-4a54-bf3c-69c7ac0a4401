using System;
using System.Net;
using System.Threading.Tasks;
using carepatron.infra.common.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Http.Resilience;
using Polly;

namespace carepatron.infra.common.InversionOfControl;

public static class InversionOfControl
{
    public static IServiceCollection RegisterInfra(this IServiceCollection services)
    {
        // Register the HttpClient for the refresh token
        services.AddSingleton(ConnectedAppRefreshConfiguration.Instance);

        services.AddHttpClient(ConnectedAppRefreshConfiguration.HttpClientName)
            .AddResilienceHandler(
            "RefreshTokenClient-CustomPipeline",
            static (builder, context) =>
        {
            var config = context.ServiceProvider.GetRequiredService<ConnectedAppRefreshConfiguration>();

            // See: https://www.pollydocs.org/strategies/retry.html
            builder.AddRetry(new HttpRetryStrategyOptions
            {
                BackoffType = DelayBackoffType.Exponential,
                MaxRetryAttempts = config.MaxRetryAttempts,
                UseJitter = true,
                Delay = TimeSpan.FromMilliseconds(config.RetryDelayInMilliseconds),
                ShouldHandle = static args =>
                {
                    return ValueTask.FromResult(args is
                    {
                        Outcome.Result.StatusCode:
                            HttpStatusCode.InternalServerError or
                            HttpStatusCode.Forbidden or
                            HttpStatusCode.TooManyRequests or 
                            HttpStatusCode.BadGateway or
                            HttpStatusCode.ServiceUnavailable or
                            HttpStatusCode.GatewayTimeout
                    });
                }
            });

            // See: https://www.pollydocs.org/strategies/timeout.html
            builder.AddTimeout(TimeSpan.FromSeconds(config.CircuitBreakerDurationInSeconds));
        });

        return services;
    }
}