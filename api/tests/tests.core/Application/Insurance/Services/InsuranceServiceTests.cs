using Bogus;
using carepatron.core.Application.Billables.Models;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Insurance.Notifications.Models;
using carepatron.core.Application.Insurance.Services;
using carepatron.core.Application.Workspace.ServiceItems.Models;
using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Repositories.Billables;
using carepatron.core.Repositories.Insurance;
using LinqKit;
using Notifications.Sdk.Client.Abstract;

namespace tests.core.Application.Insurance.Services;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class InsuranceServiceTests
{
    private readonly Faker faker = new();
    private readonly Guid contactId = Guid.NewGuid();
    private readonly ProviderItem providerItem = new ProviderItemFaker().RuleFor(x => x.Price, 100).Generate();
    private readonly InsuranceService service;
    private readonly Mock<IContactInsurancePolicyRepository> contactInsurancePolicyRepositoryMock = new();
    private readonly Mock<IServiceCoverageRepository> serviceCoverageRepositoryMock = new();
    private readonly Mock<IContactInsuranceSettingsRepository> contactInsuranceSettingsRepositoryMock = new();
    private readonly Mock<IBillableRepository> billableRepositoryMock = new();
    private readonly Mock<IProviderStaffRepository> providerStaffRepositoryMock = new();
    private readonly Mock<INotificationsService> notificationsServiceMock = new();

    public InsuranceServiceTests()
    {
        service = new InsuranceService(
            contactInsurancePolicyRepositoryMock.Object,
            serviceCoverageRepositoryMock.Object,
            contactInsuranceSettingsRepositoryMock.Object,
            billableRepositoryMock.Object,
            providerStaffRepositoryMock.Object,
            notificationsServiceMock.Object
        );
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseOriginalAmount_WhenBillingMethodNotInsurance()
    {
        // assemble
        var (settings, policies, _) = PrepTestData();
        settings.ForEach(x => x.BillingMethod = InsuranceBillingMethod.SelfPay);
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([]);

        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new BillableItem
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeFalse();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseOriginalAmount_WhenCoveragesNotFound()
    {
        // assemble
        var (settings, policies, _) = PrepTestData();
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeFalse();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseOriginalAmount_WhenCoveragesFoundButDisabled()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.ForEach(x =>
        {
            x.Coverage.Coinsurance = 0.1m;
            x.Coverage.Copay = 0.0m;
            x.Coverage.Enabled = false;
        });
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync(settings);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync(policies);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync(coverages);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeFalse();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseCoverageCopay_IfCoverageIsEnabled()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(coverages.First().Coverage.Copay);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseCoverageCoinsurance_IfCoverageIsEnabledAndCopayNotAvailable()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.ForEach(x =>
        {
            x.Coverage.Coinsurance = 0.1m;
            x.Coverage.Copay = null;
        });
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(
            billables.First().Items.First().TotalOwed * coverages.First().Coverage.Coinsurance
        );
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldSetInsuranceDisabled_IfBothPolicyAnServiceCopayIsNull()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        policies.ForEach(x =>
        {
            x.Copay = null;
            x.Coinsurance = null;
        });
        coverages.ForEach(x =>
        {
            x.Coverage.Coinsurance = null;
            x.Coverage.Copay = null;
        });
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeFalse();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseTotalOwed_IfCopayGreaterThanTotalOwedAmount()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.ForEach(x =>
        {
            x.Coverage.Coinsurance = null;
            x.Coverage.Copay = 1000;
        });
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseTotalOwed_IfCoinsuranceGreaterThanTotalOwedAmount()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.ForEach(x =>
        {
            x.Coverage.Coinsurance = 1.5m;
            x.Coverage.Copay = null;
        });
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(billables.First().Items.First().TotalOwed);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldUseCoverageCopay_WithMultiplePoliciesIfCoverageIsEnabled()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        var addtlPolicy = new ContactInsurancePolicyFaker(Guid.NewGuid(), contactId)
            .RuleFor(x => x.Copay, f => f.Random.Decimal(1, 50))
            .RuleFor(x => x.PolicyStartDate, x => DateTime.UtcNow.AddDays(-7).ToDateOnly())
            .RuleFor(x => x.Status, InsurancePolicyStatus.Verified)
            .Generate();
        var policiesList = new List<ContactInsurancePolicy>(policies)
        {
            addtlPolicy
        };
        var addtlCoverage = new ContactInsurancePolicyServiceCoverageSetting(
            contactId,
            providerItem.Id,
            addtlPolicy.Id,
            new InsurancePolicyServiceSettings
            {
                Copay = faker.Random.Number(1, 50),
                Enabled = true
            }
        );
        var coveragesList = new List<ContactInsurancePolicyServiceCoverageSetting>(coverages)
        {
            addtlCoverage
        };
        var expectedCoverage = coveragesList.FirstOrDefault(
            x => x.InsurancePolicyId == policiesList.First().Id
        );
        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policiesList]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coveragesList]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(expectedCoverage.Coverage.Copay);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldHaveZeroSelfPayWithFullAmountCovered_IfCopayIsZero()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.First().Coverage.Copay = 0;

        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(0);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
        billables.First().Items.First().InsuranceAmount.Should().Be(providerItem.Price);
    }

    [Fact]
    public async Task SetSelfPayForBillables_ShouldHaveZeroSelfPayWithFullAmountCovered_IfCoinsuranceIsZero()
    {
        // assemble
        var (settings, policies, coverages) = PrepTestData();
        coverages.First().Coverage.Copay = null;
        coverages.First().Coverage.Coinsurance = 0;

        contactInsuranceSettingsRepositoryMock
            .Setup(
                x =>
                    x.GetForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId))
                    )
            )
            .ReturnsAsync([.. settings]);
        contactInsurancePolicyRepositoryMock
            .Setup(
                x =>
                    x.GetActivePolicies(
                        It.IsAny<Guid>(),
                        It.IsAny<ContactActivePolicyRequest[]>()
                    )
            )
            .ReturnsAsync([.. policies]);
        serviceCoverageRepositoryMock
            .Setup(
                x =>
                    x.GetServiceCoveragesForContacts(
                        It.IsAny<Guid>(),
                        It.Is<Guid[]>(s => s.Contains(contactId)),
                        It.IsAny<GetServiceCoveragesListOptions>()
                    )
            )
            .ReturnsAsync([.. coverages]);

        // act
        var billables = new List<Billable>
        {
            new()
            {
                ContactId = contactId,
                Items =
                [
                    new()
                    {
                        ContactId = contactId,
                        ServiceId = providerItem.Id,
                        Price = providerItem.Price,
                        CurrencyCode = "USD",
                        Units = 1
                    }
                ]
            }
        };
        await service.SetSelfPayForBillables(Guid.NewGuid(), [.. billables]);

        // assert
        billables.First().Items.First().SelfPayAmount.Should().Be(0);
        billables.First().Items.First().IsInsuranceEnabled.Should().BeTrue();
        billables.First().Items.First().InsuranceAmount.Should().Be(providerItem.Price);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPaidStatus_WhenBalancePaidEqualsBalanceDue()
    {
        // assemble
        var providerId = Guid.NewGuid();
        var claim = new USProfessionalClaimFaker(providerId)
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(providerId, Guid.NewGuid())
                .RuleFor(x => x.Amount, 100)
                .Generate()
        };

        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.Paid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPaidStatus_WhenBalancePaidGreaterThanBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid())
                .RuleFor(x => x.Amount, 120)
                .Generate()
        };

        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.Paid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPartiallyPaidStatus_WhenBalancePaidLessThanBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid())
                .RuleFor(x => x.Amount, 50)
                .Generate()
        };

        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.PartiallyPaid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPartiallyPaidStatus_WhenExistingBalancePaidLessThanBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 50)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();

        // act
        var result = service.GetClaimStatusByPayments(claim, []);

        // assert
        result.Should().Be(ClaimStatus.PartiallyPaid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPartiallyPaidStatus_WhenExistingBalancePaidAndAllocationsAreLessThanBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 50)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid())
                .RuleFor(x => x.Amount, 25)
                .Generate()
        };
        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.PartiallyPaid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPaidStatus_WhenExistingBalancePaidAndAllocationsAreEqualToBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 50)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid())
                .RuleFor(x => x.Amount, 25)
                .Generate(),
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid())
                .RuleFor(x => x.Amount, 25)
                .Generate()
        };
        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.Paid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnOriginalStatus_WhenNoAllocations()
    {
        // assemble
        var originalStatus = ClaimStatus.Submitted;
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, originalStatus)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();

        // act
        var result = service.GetClaimStatusByPayments(claim, []);

        // assert
        result.Should().Be(originalStatus);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnOriginalStatus_WhenAllocationsAreNull()
    {
        // assemble
        var originalStatus = ClaimStatus.Rejected;
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, originalStatus)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();

        // act
        var result = service.GetClaimStatusByPayments(claim, null);

        // assert
        result.Should().Be(originalStatus);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPartiallyPaidStatus_WhenMultipleAllocationsAddToLessThanBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid()).RuleFor(x => x.Amount, 25).Generate(),
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid()).RuleFor(x => x.Amount, 25).Generate()
        };

        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.PartiallyPaid);
    }

    [Fact]
    public void GetClaimStatusByPayments_ShouldReturnPaidStatus_WhenMultipleAllocationsAddToExactlyBalanceDue()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(Guid.NewGuid())
            .RuleFor(x => x.Status, ClaimStatus.Draft)
            .RuleFor(x => x.BalancePaid, 0)
            .RuleFor(x => x.Amount, 100)
            .RuleFor(x => x.AmountPaid, 0)
            .Generate();
        var allocations = new[]
        {
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid()).RuleFor(x => x.Amount, 40).Generate(),
            new PaymentAllocationFaker(Guid.NewGuid(), Guid.NewGuid()).RuleFor(x => x.Amount, 60).Generate()
        };

        // act
        var result = service.GetClaimStatusByPayments(claim, allocations);

        // assert
        result.Should().Be(ClaimStatus.Paid);
    }

    [Fact]
    public async Task GetAmountPaidFromSingleBillableItem_ShouldReturnSumOfSelfPayAndInsurancePaid()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.BalancePaid, 0)
            .Generate();
        var billableItem = new BillableItemFaker(providerItem.ProviderId, contactId)
            .RuleFor(x => x.Amount, 100m)
            .RuleFor(x => x.SelfPayAmount, 20m)
            .RuleFor(x => x.InsurancePaid, 0m)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem.Id).Generate();
        claim.ServiceLines = [serviceLine];

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(
                It.IsAny<Guid>(),
                new[] { billableItem.Id }
            ))
            .ReturnsAsync([billableItem]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(20m); //(20 + 0) - 0
    }

    [Fact]
    public async Task GetAmountPaidFromBillableItems_ShouldReturnSumOfSelfPayAndInsurancePaid()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.BalancePaid, 0)
            .Generate();
        var billableItem1 = new BillableItemFaker(providerItem.ProviderId, contactId)
            .RuleFor(x => x.SelfPayAmount, 25m)
            .RuleFor(x => x.InsurancePaid, 75m)
            .Generate();
        var billableItem2 = new BillableItemFaker(providerItem.ProviderId, contactId)
            .RuleFor(x => x.SelfPayAmount, 15m)
            .RuleFor(x => x.InsurancePaid, 85m)
            .Generate();
        var serviceLine1 = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem1.Id).Generate();
        var serviceLine2 = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem2.Id).Generate();
        claim.ServiceLines = [serviceLine1, serviceLine2];

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(
                It.IsAny<Guid>(),
                It.Is<Guid[]>(ids => ids.Contains(billableItem1.Id) && ids.Contains(billableItem2.Id))
            ))
            .ReturnsAsync([billableItem1, billableItem2]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(200m); // ((25 + 75) + (15 + 85)) - 0
    }

    [Fact]
    public async Task GetAmountPaidFromBillableItems_ShouldReturnExistingAmountPaidWhenNoServiceLinesExist()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.ServiceLines, [])
            .Generate();

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(It.IsAny<Guid>(), It.IsAny<Guid[]>()))
            .ReturnsAsync([]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(claim.AmountPaid);
    }

    [Fact]
    public async Task GetAmountPaidFromBillableItems_ShouldReturnExistingAmountPaidWhenServiceLinesHaveNoBillableItems()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.ServiceLines, [
                new ClaimServiceLineFaker(providerItem.ProviderId, contactId).Generate()
            ])
            .RuleFor(x => x.BalancePaid, 0)
            .Generate();

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(It.IsAny<Guid>(), It.IsAny<Guid[]>()))
            .ReturnsAsync([]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(claim.AmountPaid);
    }

    [Fact]
    public async Task GetAmountPaid_WhereClaimHasPaymentMade_ShouldSubtractBalancePaid()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.BalancePaid, 30m)
            .Generate();
        var billableItem = new BillableItemFaker(providerItem.ProviderId, contactId)
            .RuleFor(x => x.Amount, 100m)
            .RuleFor(x => x.SelfPayAmount, 20m)
            .RuleFor(x => x.InsurancePaid, 30m)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem.Id).Generate();
        claim.ServiceLines = [serviceLine];

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(
                It.IsAny<Guid>(),
                new[] { billableItem.Id }
            ))
            .ReturnsAsync([billableItem]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(20m); //(20 + 30) - 30
    }

    [Fact]
    public async Task GetAmountPaid_WhereBothCurrentClaimAndAdditionalClaimPayments_ShouldOnlyIncludeAdditionalClaimPayment()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.BalancePaid, 20m)
            .Generate();
        var billableItem = new BillableItemFaker(providerItem.ProviderId, contactId)
            .RuleFor(x => x.Amount, 100m)
            .RuleFor(x => x.SelfPayAmount, 20m)
            .RuleFor(x => x.InsurancePaid, 40m)
            .Generate();
        var serviceLine = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem.Id).Generate();
        claim.ServiceLines = [serviceLine];

        billableRepositoryMock
            .Setup(x => x.GetBillableItems(
                It.IsAny<Guid>(),
                new[] { billableItem.Id }
            ))
            .ReturnsAsync([billableItem]);

        // act
        var result = await service.GetAmountPaidFromBillableItems(claim);

        // assert
        result.Should().Be(40m); // (20 + 40) - 20 
    }

    [Fact]
    public async Task GetTasksForBillableItems_ShouldReturnMappingsForAllDistinctTaskIds()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId).Generate();
        var billableItem1 = new BillableItemFaker(providerItem.ProviderId, contactId).Generate();
        var billableItem2 = new BillableItemFaker(providerItem.ProviderId, contactId).Generate();
        var serviceLine1 = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem1.Id).Generate();
        var serviceLine2 = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem2.Id).Generate();
        claim.ServiceLines = [serviceLine1, serviceLine2];

        var taskId1 = Guid.NewGuid();
        var taskId2 = Guid.NewGuid();

        billableRepositoryMock
            .Setup(x => x.GetTaskIdsByBillableItemId(
                It.Is<Guid[]>(ids => ids.Contains(billableItem1.Id) && ids.Contains(billableItem2.Id)),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync([taskId1, taskId2, taskId1]); // Duplicate taskId1 to test distinct

        // act
        var result = await service.GetTasksForBillableItems(claim, CancellationToken.None);

        // assert
        result.Should().HaveCount(2);
        result.Should().Contain(x => x.InsuranceClaimId == claim.Id && x.TaskId == taskId1);
        result.Should().Contain(x => x.InsuranceClaimId == claim.Id && x.TaskId == taskId2);
    }

    [Fact]
    public async Task GetTasksForBillableItems_ShouldReturnEmptyArrayWhenNoServiceLines()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId)
            .RuleFor(x => x.ServiceLines, [])
            .Generate();

        // act
        var result = await service.GetTasksForBillableItems(claim, CancellationToken.None);

        // assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetTasksForBillableItems_ShouldReturnEmptyArrayWhenNoTasksFound()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId).Generate();
        var billableItem = new BillableItemFaker(providerItem.ProviderId, contactId).Generate();
        var serviceLine = new ClaimServiceLineFaker(providerItem.ProviderId, contactId, billableItem.Id).Generate();
        claim.ServiceLines = [serviceLine];

        billableRepositoryMock
            .Setup(x => x.GetTaskIdsByBillableItemId(
                It.Is<Guid[]>(ids => ids.Contains(billableItem.Id)),
                It.IsAny<CancellationToken>()
            ))
            .ReturnsAsync([]);

        // act
        var result = await service.GetTasksForBillableItems(claim, CancellationToken.None);

        // assert
        result.Should().BeEmpty();
    }

    [Fact]
    public async Task GetTasksForBillableItems_ShouldHandleServiceLinesWithoutBillableItems()
    {
        // assemble
        var claim = new USProfessionalClaimFaker(providerItem.ProviderId).Generate();
        var serviceLine = new ClaimServiceLineFaker(providerItem.ProviderId, contactId).Generate();
        claim.ServiceLines = [serviceLine];

        // act
        var result = await service.GetTasksForBillableItems(claim, CancellationToken.None);

        // assert
        result.Should().BeEmpty();
    }
    
    [Fact]
    public async Task SendClaimPaymentNotification_WithPaidClaim_ShouldSendNotification()
    {
        var providerId = Guid.NewGuid();
        var staffMember = new ClaimProviderStaffFaker(providerId, new PersonFaker().Generate()).Generate();
        var claim = new USProfessionalClaimFaker(providerId).WithRenderingProviders(providerId, staffMember).AsPaid().Generate();
        var payer = new ProviderInsurancePayerFaker(providerId).Generate();
        var payment = new PaymentFaker(providerId).RuleFor(x => x.Amount, claim.AmountPaid).Generate();

        var actualContent = default(InsuranceClaimPaidNotificationContent);
        
        notificationsServiceMock
            .Setup(x => x.Send(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<INotificationsContent>(), It.IsAny<Guid[]>()))
            .Callback<Guid, string, INotificationsContent, Guid[]>((_, _, content, _) =>
            {
                actualContent = content as InsuranceClaimPaidNotificationContent;
            })
            .Returns(Task.CompletedTask);
        
        await service.SendClaimPaymentNotification(providerId, claim, payment, payer);

        notificationsServiceMock.Verify(x => x.Send<INotificationsContent>(
            providerId,
            SystemActors.InsuranceJobActorId,
            It.IsAny<InsuranceClaimPaidNotificationContent>(),
            It.Is<Guid[]>(arr => arr.Single() == staffMember.PersonId)), Times.Once);

        var expectedContent = new InsuranceClaimPaidNotificationContent(
            providerId,
            ClaimNotificationContent.Create(claim),
            PaymentNotificationContent.Create(payment),
            PayerNotificationContent.Create(payer));
        
        actualContent.Should().BeEquivalentTo(expectedContent);
    }
    
    [Fact]
    public async Task SendClaimPaymentNotification_WithPartiallyPaidClaim_ShouldSendNotification()
    {
        var providerId = Guid.NewGuid();
        var staffMember = new ClaimProviderStaffFaker(providerId, new PersonFaker().Generate()).Generate();
        var claim = new USProfessionalClaimFaker(providerId).WithRenderingProviders(providerId, staffMember).AsPartiallyPaid().Generate();
        var payer = new ProviderInsurancePayerFaker(providerId).Generate();
        var payment = new PaymentFaker(providerId).RuleFor(x => x.Amount, claim.AmountPaid).Generate();
 
        var actualContent = default(InsuranceClaimPartiallyPaidNotificationContent);
        
        notificationsServiceMock
            .Setup(x => x.Send(It.IsAny<Guid>(), It.IsAny<string>(), It.IsAny<INotificationsContent>(), It.IsAny<Guid[]>()))
            .Callback<Guid, string, INotificationsContent, Guid[]>((_, _, content, _) =>
            {
                actualContent = content as InsuranceClaimPartiallyPaidNotificationContent;
            })
            .Returns(Task.CompletedTask);
        
        await service.SendClaimPaymentNotification(providerId, claim, payment, payer);

        notificationsServiceMock.Verify(x => x.Send<INotificationsContent>(
            providerId,
            SystemActors.InsuranceJobActorId,
            It.IsAny<InsuranceClaimPartiallyPaidNotificationContent>(),
            It.Is<Guid[]>(arr => arr.Single() == staffMember.PersonId)), Times.Once);

        var expectedContent = new InsuranceClaimPaidNotificationContent(
            providerId,
            ClaimNotificationContent.Create(claim),
            PaymentNotificationContent.Create(payment),
            PayerNotificationContent.Create(payer));
        
        actualContent.Should().BeEquivalentTo(expectedContent);
    }

    [Theory]
    [InlineData(ClaimStatus.Accepted)]
    [InlineData(ClaimStatus.Rejected)]
    [InlineData(ClaimStatus.Closed)]
    [InlineData(ClaimStatus.Denied)]
    [InlineData(ClaimStatus.Draft)]
    [InlineData(ClaimStatus.Submitted)]
    [InlineData(ClaimStatus.Unknown)]
    [InlineData(ClaimStatus.Validated)]
    public async Task SendClaimPaymentNotification_WithUnsupportedStatus_ShouldNotSendNotification(ClaimStatus claimStatus)
    {
        var providerId = Guid.NewGuid();
        var staffMember = new ClaimProviderStaffFaker(providerId, new PersonFaker().Generate()).Generate();
        var claim = new USProfessionalClaimFaker(providerId)
            .WithRenderingProviders(providerId, staffMember)
            .RuleFor(x => x.Status, claimStatus)
            .Generate();
        var payer = new ProviderInsurancePayerFaker(providerId).Generate();
        var payment = new PaymentFaker(providerId).RuleFor(x => x.Amount, claim.AmountPaid).Generate();
        
        await service.SendClaimPaymentNotification(providerId, claim, payment, payer);

        notificationsServiceMock.Verify(x => x.Send(
            It.IsAny<Guid>(),
            It.IsAny<string>(),
            It.IsAny<INotificationsContent>(),
            It.IsAny<Guid[]>()), Times.Never());
    }

    [Fact]
    public async Task SendClaimPaymentNotification_WhenRenderingProviderStaffCannotBeResolved_ShouldNotSendNotification()
    {
        var providerId = Guid.NewGuid();
        
        // Create rendering provider with no staff member
        var claim = new USProfessionalClaimFaker(providerId).WithRenderingProviders(providerId).AsPaid().Generate();
        var payer = new ProviderInsurancePayerFaker(providerId).Generate();
        var payment = new PaymentFaker(providerId).RuleFor(x => x.Amount, claim.AmountPaid).Generate();
        
        await service.SendClaimPaymentNotification(providerId, claim, payment, payer);

        notificationsServiceMock.Verify(x => x.Send(
            It.IsAny<Guid>(),
            It.IsAny<string>(),
            It.IsAny<INotificationsContent>(),
            It.IsAny<Guid[]>()), Times.Never());
    }

    private (ContactInsuranceSettings[] Settings, ContactInsurancePolicy[] Policies, ContactInsurancePolicyServiceCoverageSetting[] Coverages) PrepTestData()
    {
        var settings = new ContactInsuranceSettingsFaker(providerItem.Id, contactId)
            .RuleFor(x => x.BillingMethod, InsuranceBillingMethod.Insurance)
            .Generate(1);
        var policies = new ContactInsurancePolicyFaker(Guid.NewGuid(), contactId)
            .RuleFor(x => x.Copay, f => f.Random.Decimal(1, 50))
            .RuleFor(x => x.PolicyStartDate, _ => DateTime.UtcNow.AddMonths(-1).ToDateOnly())
            .Generate(1);
        var coverages = new InsuranceServiceCoverageDataModelFaker(providerItem.Id, policies.First().Id)
            .Generate(1)
            .Select(x => new ContactInsurancePolicyServiceCoverageSetting(
                contactId,
                providerItem.Id,
                x.InsurancePolicyId,
                new InsurancePolicyServiceSettings
                {
                    Copay = faker.Random.Number(1, 50),
                    Enabled = true
                }
            ));

        return ([.. settings], [.. policies], [.. coverages]);
    }
}
