﻿using AutoMapper;
using Bogus;
using carepatron.api.Contracts.Requests.Booking;
using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.Schema.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Localisation;
using carepatron.core.Models.Common;
using carepatron.core.Models.Execution;
using carepatron.core.Repositories.Provider;
using carepatron.infra.sql.Models.Items;
using carepatron.infra.sql.Models.Provider;
using carepatron.infra.sql.Models.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Net;
using carepatron.core.Application.Tasks.Models;
using Moq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Extensions;
using tests.common.Mocks;
using tests.component.Builders;
using tests.component.Builders.Requests;
using static carepatron.core.Application.Schema.Models.ContactCoreSchema;

namespace tests.component.Features.Booking;

[Trait(nameof(CodeOwner), CodeOwner.TasksAndScheduling)]
public class CreateBookingIntentTests : BaseTestClass
{
    private DateRange bookingDateTime;

    public CreateBookingIntentTests(ComponentTestFixture testFixture) : base(testFixture)
    {
        var now = DateTime.UtcNow;
        bookingDateTime = new DateRange(now.AddMinutes(30), now.AddMinutes(90));
    }

    [Fact]
    public async Task BookingSubmit_ShouldAssignStaffToClient()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = await DataContext.Tasks
            .Include(taskDataModel => taskDataModel.Contacts)
            .FirstAsync(x => x.Id == result.TaskId && x.ProviderId == ProviderId);

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contactIds = taskDb.Contacts.Select(x => x.ContactId).ToArray();

        var contactDb = await DataContext.Contacts
            .Where(x => contactIds.Contains(x.Id))
            .Include(contactDataModel => contactDataModel.AssignedStaff)
            .ToListAsync();

        contactDb.Should().NotBeNullOrEmpty();

        foreach (var contact in contactDb)
        {
            contact.AssignedStaff.Should().ContainEquivalentOf(new
            {
                PersonId = requestModel.StaffId
            }, opt => opt.ExcludingMissingMembers());
        }
    }

    [Fact]
    public async Task BookingSubmit_ShouldCreateAContactForAnExistingPerson()
    {
        var person = new PersonFaker()
            .Generate()
            .ToDataModel();

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(person, location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = person.Email,
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = DataContext.Tasks
            .Where(x => x.Id == result.TaskId && x.ProviderId == ProviderId)
            .FirstOrDefault();

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contactId = taskDb.Contacts.Select(x => x.ContactId).First();

        var contact = DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.Id == contactId)
            .FirstOrDefault();

        contact.Should().NotBeNull();
        contact.Should().BeEquivalentTo(new
        {
            person.FirstName,
            person.LastName,
            person.Email,
            personId = person.Id,
            PhoneNumber = faker.Phone,
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task BookingSubmit_ShouldAttachAPersonToAnExistingContact()
    {
        var contact = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.PersonId, (Guid?)null)
            .Generate()
            .ToDataModel();

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(contact, location, providerItem);
        await DataContext.SaveChangesAsync();

        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contact.FirstName,
                LastName = contact.LastName,
                Email = new Email(contact.Email),
                PhoneNumber = contact.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = DataContext.Tasks
            .Where(x => x.Id == result.TaskId && x.ProviderId == ProviderId)
            .FirstOrDefault();

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contactDb = DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.Id == contact.Id)
            .FirstOrDefault();

        contactDb.Should().NotBeNull();
        contactDb.PersonId.Should().NotBeEmpty();

        var person = DataContext.Persons
            .Where(x => x.Id == contactDb.PersonId)
            .FirstOrDefault();

        person.Should().NotBeNull();
        person.Should().BeEquivalentTo(new
        {
            contact.FirstName,
            contact.LastName,
            contact.Email,
            contact.PhoneNumber
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task BookingSubmit_ShouldMatchMultipleContactsMatchByName()
    {
        var email = new Faker().CarePatron().Email();

        var contacts = new ContactFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.Email, email)
            .RuleFor(x => x.PersonId, (Guid?)null)
            .Generate(2)
            .ToDataModel();

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        DataContext.AddRange(contacts);
        DataContext.AddRange(location, providerItem);
        await DataContext.SaveChangesAsync();

        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contacts[1].FirstName.ToUpper(),
                LastName = contacts[1].LastName,
                Email = email,
                PhoneNumber = contacts[1].PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = DataContext.Tasks
            .Where(x => x.Id == result.TaskId && x.ProviderId == ProviderId)
            .FirstOrDefault();

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty()
            .And.Contain(t => t.ContactId == contacts[1].Id);
    }

    [Fact]
    public async Task BookingSubmit_ShouldUseStaffsOverriddenPrice()
    {
        var person = new PersonFaker()
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker()
           .RuleFor(x => x.PersonId, person.Id)
           .RuleFor(x => x.ProviderId, ProviderId)
           .Generate()
           .ToDataModel();

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;
        providerItem.ProviderStaffItems.First().Price = 150;

        var call = new CallFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CallProvider, carepatron.core.Models.Call.CallProvider.AWS)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(person, contact, location, providerItem);
        await DataContext.SaveChangesAsync();

        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contact.FirstName,
                LastName = contact.LastName,
                Email = new Email(contact.Email),
                PhoneNumber = contact.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
          .WithBearerAuthorization(IdentityToken)
          .WithPayload(requestModel)
           .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = DataContext.Tasks
            .Where(x => x.Id == result.TaskId && x.ProviderId == ProviderId)
            .FirstOrDefault();
        taskDb.ItemsSnapshot.Should().NotBeNullOrEmpty();
        taskDb.ItemsSnapshot.Should().ContainEquivalentOf(new
        {
            providerItem.Id,
            Price = 150
        });
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_Test()
    {
        GenerateData(out var location, out var providerItem, 120);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();
        setupIntentResponse.PaymentBreakdown.Should().NotBeNull();
    }

    [Fact]
    public async Task BookingSubmit_WithoutStaffService_StillCreatesBooking()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);
        providerItem.ProviderStaffItems.Clear();

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();
    }

    [Fact]
    public async Task BookingSubmit_WithUnknownItemId_ReturnsBadRequest()
    {
        GenerateData(out var location, out var _);

        await DataContext.AddAsync(location);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = Guid.NewGuid()
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.ProviderItemIsRequiredCode);
        result.Details.Should().Be(Errors.ProviderItemIsRequiredDetails);
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_WithoutStripeCustomerId_CreatesCustomer()
    {
        GenerateData(out var location, out var providerItem, 120);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();
        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();

        setupIntentResponse.PaymentBreakdown.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_IsSalesTaxIncludedFalse_IsClientChargedFalse_Test()
    {
        GenerateData(out var location, out var providerItem, 120);

        providerItem.IsSalesTaxIncluded = false;
        Data.CurrentProviderBillingSettings.IsClientChargedPaymentProcessingFee = false;

        var providerRepository = ResolveService<IProviderRepository>();
        await providerRepository.SaveBillingSettings(Data.CurrentProviderBillingSettings);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();

        setupIntentResponse.PaymentBreakdown.SubTotal.Should().Be(120);
        setupIntentResponse.PaymentBreakdown.Tax.Should().Be(0);
        setupIntentResponse.PaymentBreakdown.Fees.Should().Be(0);
        setupIntentResponse.PaymentBreakdown.Total.Should().Be(120);
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_IsSalesTaxIncludedFalse_IsClientChargedTrue_Test()
    {
        GenerateData(out var location, out var providerItem, 120);

        providerItem.IsSalesTaxIncluded = false;
        Data.CurrentProviderBillingSettings.IsClientChargedPaymentProcessingFee = true;

        var providerRepository = ResolveService<IProviderRepository>();
        await providerRepository.SaveBillingSettings(Data.CurrentProviderBillingSettings);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();

        setupIntentResponse.PaymentBreakdown.SubTotal.Should().Be(120);
        setupIntentResponse.PaymentBreakdown.Tax.Should().Be(0);
        setupIntentResponse.PaymentBreakdown.Fees.Should().Be(4.20m);
        setupIntentResponse.PaymentBreakdown.Total.Should().Be(124.20m);
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_IsSalesTaxIncludedTrue_IsClientChargedFalse_Test()
    {
        GenerateData(out var location, out var providerItem, 120);

        providerItem.IsSalesTaxIncluded = true;
        Data.CurrentProviderBillingSettings.IsClientChargedPaymentProcessingFee = false;

        var providerRepository = ResolveService<IProviderRepository>();
        await providerRepository.SaveBillingSettings(Data.CurrentProviderBillingSettings);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();

        setupIntentResponse.PaymentBreakdown.SubTotal.Should().Be(120);
        setupIntentResponse.PaymentBreakdown.Tax.Should().Be(14.4m);
        setupIntentResponse.PaymentBreakdown.Fees.Should().Be(0);
        setupIntentResponse.PaymentBreakdown.Total.Should().Be(134.4m);
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_IsSalesTaxIncludedTrue_IsClientChargedTrue_Test()
    {
        GenerateData(out var location, out var providerItem, 120);

        providerItem.IsSalesTaxIncluded = true;
        Data.CurrentProviderBillingSettings.IsClientChargedPaymentProcessingFee = true;

        var providerRepository = ResolveService<IProviderRepository>();
        await providerRepository.SaveBillingSettings(Data.CurrentProviderBillingSettings);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.CustomerId.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNull();
        setupIntentResponse.PaymentIntentId.Should().NotBeNullOrEmpty();

        setupIntentResponse.PaymentBreakdown.SubTotal.Should().Be(120);
        setupIntentResponse.PaymentBreakdown.Tax.Should().Be(14.4m);
        setupIntentResponse.PaymentBreakdown.Fees.Should().Be(4.63m);
        setupIntentResponse.PaymentBreakdown.Total.Should().Be(139.03m);
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPaymentMethod_ReturnsStripeSecret_AndNoPaymentInfo()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, true);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();

        setupIntentResponse.Should().NotBeNull();

        setupIntentResponse.ClientSecret.Should().NotBeNullOrEmpty();
        setupIntentResponse.CustomerId.Should().NotBeNullOrEmpty();
        setupIntentResponse.BookingIntentId.Should().NotBeEmpty();

        setupIntentResponse.PaymentBreakdown.Should().BeNull();
        setupIntentResponse.PaymentIntentId.Should().BeNull();
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPaymentMethod_WithExistingUser_UsesExistingStripeCustomerDetails()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, true);

        var person = new PersonFaker()
            .RuleFor(x => x.StripeCustomerId, f => f.Stripe().CustomerId)
            .Generate()
            .ToDataModel();

        var providerStaff = new ProviderStaffFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.PersonId, person.Id)
                .RuleFor(x => x.Role, ProviderRole.Staff)
                .Generate()
                .ToDataModel();

        await DataContext.AddRangeAsync(location, providerItem, person, providerStaff);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = person.Email,
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();

        setupIntentResponse.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNullOrEmpty();
        setupIntentResponse.CustomerId.Should().Be(person.StripeCustomerId);
        setupIntentResponse.BookingIntentId.Should().NotBeEmpty();

        setupIntentResponse.PaymentBreakdown.Should().BeNull();
        setupIntentResponse.PaymentIntentId.Should().BeNull();
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPaymentMethod_WithNewUser_CreatesNewStripeCustomerDetails()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, true);

        var person = new PersonFaker()
          .Generate()
          .ToDataModel();

        var providerStaff = new ProviderStaffFaker()
                .RuleFor(x => x.ProviderId, ProviderId)
                .RuleFor(x => x.PersonId, person.Id)
                .RuleFor(x => x.Role, ProviderRole.Staff)
                .Generate()
                .ToDataModel();

        await DataContext.AddRangeAsync(location, providerItem, person, providerStaff);
        await DataContext.SaveChangesAsync();


        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = person.Email,
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };
        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();

        setupIntentResponse.Should().NotBeNull();
        setupIntentResponse.ClientSecret.Should().NotBeNullOrEmpty();
        setupIntentResponse.CustomerId.Should().NotBeNullOrEmpty();
        setupIntentResponse.BookingIntentId.Should().NotBeEmpty();
    }

    [Fact]
    public async Task BookingSubmit_WithOnlineBookingOptionsAsNull()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var contactDb = DataContext.Contacts.AsNoTracking()
            .Where(x => x.Email == requestModel.ContactDetails.Email)
            .FirstOrDefault();

        contactDb.Should().NotBeNull();

        var personDb = DataContext.Persons.AsNoTracking()
            .Where(x => x.Id == contactDb.PersonId)
            .FirstOrDefault();

        personDb.Should().NotBeNull();
        personDb.Should().BeEquivalentTo(new
        {
            Email = requestModel.ContactDetails.Email,
            FirstName = requestModel.ContactDetails.FirstName,
            LastName = requestModel.ContactDetails.LastName,
            PhoneNumber = requestModel.ContactDetails.PhoneNumber
        });

        var taskDb = DataContext.Tasks
            .Where(x => x.Id == result.TaskId && x.ProviderId == ProviderId)
            .FirstOrDefault();

        taskDb.Should().NotBeNull();
        taskDb.Should().BeEquivalentTo(new
        {
            ProviderId,
            Location = location.Address,
            Title = $"{requestModel.ContactDetails.FirstName} {requestModel.ContactDetails.LastName.GetInitial()} - {providerItem.Title}",
            StartDate = requestModel.StartDate,
            EndDate = requestModel.EndDate
        }, opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());

        taskDb.Contacts.Should().NotBeNullOrEmpty();
        taskDb.Contacts.Should().ContainEquivalentOf(new
        {
            Contact = new
            {
                FirstName = requestModel.ContactDetails.FirstName,
                LastName = requestModel.ContactDetails.LastName,
                Email = requestModel.ContactDetails.Email,
                Status = "Lead",
                Reason = requestModel.Message
            }
        }, opt => opt.ExcludingMissingMembers());

        taskDb.Staff.Should().NotBeNullOrEmpty();
        taskDb.Staff.Should().ContainEquivalentOf(new
        {
            PersonId = PersonId
        }, opt => opt.ExcludingMissingMembers());

        taskDb.ItemsSnapshot.Should().NotBeNullOrEmpty();
        taskDb.ItemsSnapshot.Should().ContainEquivalentOf(new
        {
            Id = providerItem.Id
        }, opt => opt.ExcludingMissingMembers());

        var paymentMethod = await DataContext.PaymentMethods.AsNoTracking()
            .FirstOrDefaultAsync(x => x.PersonId == personDb.Id);
        paymentMethod.Should().BeNull();
    }

    [Fact]
    public async Task CreateBookingIntent_NeedingPayment_FreeService_RequiresCapturingPaymentMethod()
    {
        GenerateData(out var location, out var providerItem, 0);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        await SaveProviderOnlineBookingOptions(requirePaymentMethod: true, processAtTimeOfBooking: true);

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        result.BookingIntentId.Should().NotBeEmpty();
        result.ClientSecret.Should().NotBeEmpty();
        result.CustomerId.Should().NotBeEmpty();
        result.PaymentIntentId.Should().BeNull();
        result.PaymentBreakdown.Should().BeNull();
    }

    [Fact]
    public async Task Create_booking_intent_should_have_virtual_location_types()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        await SaveProviderOnlineBookingOptions(requirePaymentMethod: true, processAtTimeOfBooking: false);

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            LocationType = location.Type,
            VirtualLocationProduct = location.Product
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var bookingIntentDb = DataContext.BookingIntents
            .AsNoTracking()
            .Where(x => x.Id == result.BookingIntentId)
            .FirstOrDefault();

        bookingIntentDb.Should().NotBeNull();
        bookingIntentDb.Should().BeEquivalentTo(new
        {
            LocationType = location.Type,
            VirtualLocationProduct = location.Product
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_intent_should_have_task_virtual_location_types()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            LocationType = location.Type,
            VirtualLocationProduct = location.Product
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = DataContext.Tasks
            .AsNoTracking()
            .Where(x => x.Id == result.TaskId)
            .FirstOrDefault();
        taskDb.Should().NotBeNull();
        taskDb.Should().BeEquivalentTo(new
        {
            LocationType = location.Type,
            VirtualLocationProduct = location.Product
        }, opt => opt.ExcludingMissingMembers());
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("not a valid time zone")]
    public async Task Create_booking_intent_should_return_bad_request_when_time_zone_is_invalid(string timeZone)
    {
        GenerateData(out var location, out var providerItem);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = timeZone,
            ItemId = providerItem.Id,
            LocationType = location.Type,
            VirtualLocationProduct = location.Product
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
         .WithBearerAuthorization(IdentityToken)
         .WithPayload(requestModel)
         .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Should().BeEquivalentTo(new ValidationError(Errors.TimeZoneIsRequiredCode, Errors.TimeZoneIsRequiredDetail, ValidationType.BadRequest));
    }

    [Fact]
    public async Task Create_booking_intent_should_default_contact_status_active_when_no_lead_status_option()
    {
        var contactSchema = new Dictionary<string, Property>()
        {
            { "Status",
                new OptionSetV2Property()
                {
                    IsRequired = true,
                    DisplayName = "Status",
                    Options = new Dictionary<string, OptionSetValue>
                    {
                        {
                            "Active",
                            new OptionSetValue
                            {
                                Id = "Active",
                                DisplayName = "Active",
                                ColorHex = "#4CAF50",
                                GroupPath = StatusGroup.Active,
                                OrderIndex = 0
                            }
                        },
                        {
                            "Inactive",
                            new OptionSetValue
                            {
                                Id = "Inactive",
                                DisplayName = "Inactive",
                                ColorHex = "#EF6C00",
                                GroupPath = StatusGroup.Inactive,
                                OrderIndex = 0
                            }
                        },
                    },
                    Groups = new Dictionary<string, GroupOptionSetValue>
                    {
                        { "Lead", new GroupOptionSetValue{ Id = "Lead", DisplayName = "Lead" } },
                        { "Active", new GroupOptionSetValue{ Id = "Active", DisplayName = "Active" } },
                        { "Inactive", new GroupOptionSetValue{ Id = "Inactive", DisplayName = "Inactive" } },
                    }
                }
            }
        };

        var dataSchema = new DataSchemaFaker(SchemaTypes.ContactSchema, ProviderId, contactSchema)
           .Generate();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
            .RuleFor(x => x.LocationIds, new[] { location.Id })
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(location, providerItem, dataSchema.ToDataModel());
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var contactDb = DataContext.Contacts.AsNoTracking()
            .Where(x => x.Email == requestModel.ContactDetails.Email)
            .FirstOrDefault();

        contactDb.Should().NotBeNull();

        contactDb.Status.Should().Be("Active");
    }

    [Fact]
    public async Task Create_booking_intent_should_not_resolve_contact_by_phone_number()
    {
        var location = new ProviderLocationFaker()
          .RuleFor(x => x.ProviderId, ProviderId)
          .Generate()
          .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
          .Generate()
          .ToDataModel();

        await DataContext.AddRangeAsync(location, providerItem, contact);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = contact.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var contactsDb = await DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.PhoneNumber == contact.PhoneNumber)
            .ToListAsync();

        contactsDb.Should().NotBeNullOrEmpty();
        contactsDb.Should().HaveCount(2);

        var expected = new[]
        {
            new { FirstName = contact.FirstName, LastName = contact.LastName, Email = contact.Email, PhoneNumber = contact.PhoneNumber },
            new { FirstName = faker.FirstName, LastName = faker.LastName, Email = new Email(faker.Email), PhoneNumber = contact.PhoneNumber }
        };
        contactsDb.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_intent_should_not_resolve_contact_by_email()
    {
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(location, providerItem, contact);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = contact.Email,
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var contactsDb = await DataContext.Contacts
            .AsNoTracking()
            .Where(x => x.Email == contact.Email)
            .ToListAsync();

        contactsDb.Should().NotBeNullOrEmpty();
        contactsDb.Should().HaveCount(2);

        var expected = new[]
        {
            new { FirstName = contact.FirstName, LastName = contact.LastName, Email = contact.Email, PhoneNumber = contact.PhoneNumber },
            new { FirstName = faker.FirstName, LastName = faker.LastName, Email = contact.Email, PhoneNumber = faker.Phone }
        };
        contactsDb.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers());
    }


    [Fact]
    public async Task Create_booking_intent_title_should_be_name_and_initial()
    {
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();


        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = "John",
                LastName = "Doe",
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks.AsNoTracking().FirstOrDefaultAsync(x => x.Id == result.TaskId);
        task.Title.Should().Be("John D - " + providerItem.Title);
        task.Description.Should().Be(requestModel.Message);
    }

    [Fact]
    public async Task Create_booking_intent_null_lastname_title_should_be_firstname_only()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, (string)null)
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();


        await DataContext.AddRangeAsync(person, contact.ToDataModel(), location, providerItem);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = "John",
                LastName = "Doe",
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks.FirstOrDefaultAsync(x => x.Id == result.TaskId);
        var expectedTitle = $"{contact.ToTaskTitle()} - {providerItem.Title}";
        task.Title.Should().Be(expectedTitle);
    }

    [Fact]
    public async Task Create_booking_intent_empty_lastname_title_should_be_firstname_only()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "")
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();


        await DataContext.AddRangeAsync(person, contact.ToDataModel(), location, providerItem);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = "John",
                LastName = "Doe",
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);
        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks.FirstOrDefaultAsync(x => x.Id == result.TaskId);

        var expectedTitle = $"{contact.ToTaskTitle()} - {providerItem.Title}";
        task.Title.Should().Be(expectedTitle);
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_booking_if_has_overlapping_task()
    {
        var faker = new Faker();
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            });

        await DataContext.AddRangeAsync(location, providerItem, task);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FindName(),
                LastName = faker.Name.LastName(),
                Email = new Email(faker.Internet.Email()),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);
        result.Details.Should().Be(Errors.BookingTimeslotNotAvailableDetails);

        var itemDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .FirstOrDefaultAsync();
        itemDb.Should().NotBeNull();
        itemDb.Contacts.Should().BeEmpty();
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_booking_if_has_overlapping_task_with_new_attendee_status()
    {
        var faker = new Faker();
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            })
            .WithContact(new List<TaskContactDataModel>
            {
                new()
                {
                    ContactId = contact.Id,
                    AttendeeStatusId = nameof(TaskContactStatus.Confirmed)
                }
            });

        await DataContext.AddRangeAsync(location, providerItem, task, contact);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FindName(),
                LastName = faker.Name.LastName(),
                Email = new Email(faker.Internet.Email()),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);
        result.Details.Should().Be(Errors.BookingTimeslotNotAvailableDetails);
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_booking_if_has_overlapping_task_with_different_timezone()
    {
        var faker = new Faker();
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            });

        await DataContext.AddRangeAsync(location, providerItem, task);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FindName(),
                LastName = faker.Name.LastName(),
                Email = new Email(faker.Internet.Email()),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "America/Chicago",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);
        result.Details.Should().Be(Errors.BookingTimeslotNotAvailableDetails);

        var itemDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .FirstOrDefaultAsync();
        itemDb.Should().NotBeNull();
        itemDb.Contacts.Should().BeEmpty();
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_over_booking()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            });


        await DataContext.AddRangeAsync(person, contact, location, providerItem, task);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);
        result.Details.Should().Be(Errors.BookingTimeslotNotAvailableDetails);

        var itemDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == task.Id);
        itemDb.Should().NotBeNull();

        itemDb.Contacts.Should().BeEmpty();
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_over_booking_for_task_with_group_events()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jane")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person2.Id)
            .RuleFor(x => x.Email, person2.Email)
            .RuleFor(x => x.PhoneNumber, person2.PhoneNumber)
            .RuleFor(x => x.FirstName, person2.FirstName)
            .RuleFor(x => x.LastName, person2.LastName)
            .Generate()
            .ToDataModel();

        var person3 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jack")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact3 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person3.Id)
            .RuleFor(x => x.Email, person3.Email)
            .RuleFor(x => x.PhoneNumber, person3.PhoneNumber)
            .RuleFor(x => x.FirstName, person3.FirstName)
            .RuleFor(x => x.LastName, person3.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithContact(new List<TaskContactDataModel> {
                new TaskContactDataModel() { ContactId = contact1.Id },
                new TaskContactDataModel() { ContactId = contact2.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            });


        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            person3, contact3,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person3.FirstName,
                LastName = person3.LastName,
                Email = new Email(person3.Email),
                PhoneNumber = person3.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);
        result.Details.Should().Be(Errors.BookingTimeslotNotAvailableDetails);

        var itemDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == task.Id);
        itemDb.Should().NotBeNull();

        itemDb.Contacts.Should().HaveCount(2);
    }

    [Fact]
    public async Task Create_booking_intent_should_allow_for_first_time_group_booking()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var otherTask = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id }
            });

        await DataContext.AddRangeAsync(
            person1, contact1,
            location, providerItem, otherTask
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person1.FirstName,
                LastName = person1.LastName,
                Email = new Email(person1.Email),
                PhoneNumber = person1.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        result.TaskId.Should().NotBe(Guid.Empty);


        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.ProviderId == ProviderId && i.Id == result.TaskId);

        taskDb.Should().NotBeNull();

        taskDb.Contacts.Should().BeEquivalentTo(new[]
        {
            new { ContactId = contact1.Id }
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_intent_should_allow_for_existing_group_booking_without_task_id()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = true,
                    MaximumAttendeeLimit = 2
                }
            });

        await DataContext.AddRangeAsync(
            person1, contact1,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person1.FirstName,
                LastName = person1.LastName,
                Email = new Email(person1.Email),
                PhoneNumber = person1.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        result.TaskId.Should().NotBe(Guid.Empty);


        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.ProviderId == ProviderId && i.Id == result.TaskId);

        taskDb.Should().NotBeNull();

        taskDb.Contacts.Should().BeEquivalentTo(new[]
        {
            new { ContactId = contact1.Id }
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_intent_should_not_allow_for_existing_group_booking_without_task_id()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();
        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 1)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id, AboutContact = true}
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = true,
                    MaximumAttendeeLimit = 1
                }
            });

        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person2.FirstName,
                LastName = person2.LastName,
                Email = new Email(person2.Email),
                PhoneNumber = person2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.BookingTimeslotNotAvailableCode);

        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.ProviderId == ProviderId && i.Id == task.Id);

        taskDb.Should().NotBeNull();

        taskDb.Contacts.Should().BeEquivalentTo(new[]
        {
            new { ContactId = contact1.Id }
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_intent_should_allow_if_another_task_has_same_end_date()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        var fromDate = bookingDateTime.FromDate.AddDays(1).Truncate(TimeSpan.TicksPerSecond);
        var toDate = bookingDateTime.ToDate.AddDays(1).Truncate(TimeSpan.TicksPerSecond);

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, fromDate)
            .RuleFor(x => x.EndDate, toDate)
            .RuleFor(x => x.TimeZone, "Asia/Manila")
            .Generate()
            .ToDataModel()
            .WithStaff(new List<TaskStaffDataModel> { new TaskStaffDataModel { PersonId = PersonId } })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration
                }
            });


        await DataContext.AddRangeAsync(person, contact, location, providerItem, task);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = toDate,
            EndDate = toDate.AddMinutes(30),
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var tasksDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        tasksDb.Should().HaveCount(2);
    }

    [Fact]
    public async Task Create_booking_intent_with_spots_left()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jane")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person2.Id)
            .RuleFor(x => x.Email, person2.Email)
            .RuleFor(x => x.PhoneNumber, person2.PhoneNumber)
            .RuleFor(x => x.FirstName, person2.FirstName)
            .RuleFor(x => x.LastName, person2.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.Description, f => f.Lorem.Sentence())
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person2.FirstName,
                LastName = person2.LastName,
                Email = new Email(person2.Email),
                PhoneNumber = person2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var tasksDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        tasksDb.Should().HaveCount(1);

        var taskDb = tasksDb.FirstOrDefault(x => x.Id == task.Id);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().HaveCount(2);
        taskDb.Description.Should().Be($"{task.Description}{Environment.NewLine}{requestModel.Message}");
    }

    [Fact]
    public async Task Create_booking_intent_with_task_using_attendee_status_spots_left()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jane")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person2.Id)
            .RuleFor(x => x.Email, person2.Email)
            .RuleFor(x => x.PhoneNumber, person2.PhoneNumber)
            .RuleFor(x => x.FirstName, person2.FirstName)
            .RuleFor(x => x.LastName, person2.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithContact(
            [
                new()
                {
                    ContactId = contact1.Id,
                    AttendeeStatusId = nameof(TaskContactStatus.Confirmed),
                }
            ])
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person2.FirstName,
                LastName = person2.LastName,
                Email = new Email(person2.Email),
                PhoneNumber = person2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var itemsDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        itemsDb.Should().HaveCount(1);

        var itemDb = itemsDb.FirstOrDefault(x => x.Id == task.Id);
        itemDb.Should().NotBeNull();
        itemDb.Contacts.Should().HaveCount(2);
    }

    [Fact]
    public async Task Create_booking_intent_existing_task_contact_attendee_status_id_should_not_change()
    {
        var contact1 = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        var contact2 = new ContactFaker(ProviderId)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 10)
            .Generate()
            .ToDataModel();

        var customStatusId = Guid.NewGuid().ToString();
        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithContact(
            [
                new()
                {
                    ContactId = contact1.Id,
                    AttendeeStatusId = customStatusId,
                }
            ])
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            contact1,
            contact2,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contact2.FirstName,
                LastName = contact2.LastName,
                Email = contact2.Email,
                PhoneNumber = contact2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var itemsDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        itemsDb.Should().HaveCount(1);

        var itemDb = itemsDb.FirstOrDefault(x => x.Id == task.Id);
        itemDb.Should().NotBeNull();
        itemDb.Contacts.Should().BeEquivalentTo([
            new { ContactId = contact1.Id, AttendeeStatusId = customStatusId },
            new { ContactId = contact2.Id, AttendeeStatusId = nameof(TaskContactStatus.Confirmed) }
        ], opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task Create_booking_intent_already_a_contact()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel> {
                new TaskContactDataModel() { ContactId = contact.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            person1, contact,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person1.FirstName,
                LastName = person1.LastName,
                Email = new Email(person1.Email),
                PhoneNumber = person1.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(i => i.ProviderId == ProviderId && i.Id == task.Id);

        taskDb.Should().NotBeNull();

        var contactDb = taskDb.Contacts.FirstOrDefault(x => x.ContactId == contact.Id);
        taskDb.Contacts.Should().HaveCount(1);
        contactDb.Should().NotBeNull();
    }

    [Fact]
    public async Task Create_booking_intent_should_use_new_info_for_contact_with_same_email()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel> {
                new TaskContactDataModel() { ContactId = contact.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            person, contact,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = "Tom",
                LastName = "Green",
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var taskDb = await DataContext.Tasks
            .AsNoTracking().Include(taskDataModel => taskDataModel.Contacts)
            .FirstOrDefaultAsync(i => i.ProviderId == ProviderId && i.Id == task.Id);

        var contactsDb = await DataContext.Contacts
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId && i.Email == person.Email)
            .ToListAsync();

        var expected = new[]
        {
            new { person.FirstName, person.LastName, person.Email, person.PhoneNumber },
            new { FirstName = "Tom", LastName = "Green", person.Email, person.PhoneNumber }
        };

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().HaveCount(2);
        taskDb.Contacts.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());

        contactsDb.Should().NotBeNull();
        contactsDb.Should().HaveCount(2);
        contactsDb.Should().BeEquivalentTo(expected, opt => opt.ExcludingMissingMembers().WithoutStrictOrdering());
    }

    [Fact]
    public async Task Create_booking_intent_added_succeeding_attendee_should_have_about_contact_set_to_true()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 2)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel> {
                new TaskContactDataModel() { ContactId = contact.Id },
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });


        await DataContext.AddRangeAsync(
            person, contact,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var faker = new PersonFaker().Generate();
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var itemDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == task.Id);
        itemDb.Should().NotBeNull();

        var taskContactDb = await DataContext.TaskContacts
            .AsNoTracking()
            .Where(x => x.TaskId == task.Id)
            .ToListAsync();

        taskContactDb.Should().HaveCount(2);

        var targetContact = taskContactDb.FirstOrDefault(x => x.Contact.Email == requestModel.ContactDetails.Email);
        targetContact.Should().NotBeNull();
        targetContact.AboutContact.Should().BeTrue();
    }

    [Fact]
    public async Task Create_booking_intent_should_handle_contact_with_whitespace_on_first_name()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var existingContact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .Generate()
            .ToDataModel();

        await DataContext.AddRangeAsync(
            person, existingContact,
            location, providerItem
        );
        await DataContext.SaveChangesAsync();

        var firstNameWithWhiteSpaces = " John ";

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = firstNameWithWhiteSpaces,
                LastName = "Doe",
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var contactsDb = await DataContext.Contacts
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId && i.Email == person.Email)
            .ToListAsync();

        contactsDb.Should().NotBeNull();
        contactsDb.Should().HaveCount(1);
    }

    [Fact]
    public async Task Create_booking_intent_should_use_provider_currency_code()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);
        providerItem.CurrencyCode = string.Empty;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        // change provider currency code
        var billingSettings = DataContext.ProviderBillingSettings
            .Where(x => x.ProviderId == ProviderId)
            .FirstOrDefault();

        billingSettings.CurrencyCode = "USD";
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var setupIntentResponse = await response.Content.ReadAsAsync<BookingIntentResponse>();
        setupIntentResponse.Should().NotBeNull();
        setupIntentResponse.TaskId.Should().NotBeEmpty();

        var taskDb = await DataContext.Tasks.AsNoTracking().FirstOrDefaultAsync(x => x.Id == setupIntentResponse.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.ItemsSnapshot.Should().NotBeNullOrEmpty();
        taskDb.ItemsSnapshot.FirstOrDefault().CurrencyCode.Should().Be("USD");
    }

    [Fact]
    public async Task Create_booking_immediately_should_create_relationship_with_client()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;
        var bookingContact = new BookingContactDetail()
        {
            FirstName = faker.FirstName,
            LastName = faker.LastName,
            Email = new Email(faker.Email),
            PhoneNumber = faker.Phone
        };

        var relatedContactPerson = new Faker().Person;
        var bookingRelatedContact = new BookingRelatedContactDetail()
        {
            FirstName = relatedContactPerson.FirstName,
            LastName = relatedContactPerson.LastName,
            Email = new Email(relatedContactPerson.Email),
            PhoneNumber = relatedContactPerson.Phone,
            Relationship = "Sibling",
            IsAttending = true
        };
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = bookingContact,
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            RelatedContactDetails = bookingRelatedContact
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();

        var contactDb = await DataContext.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Email == bookingContact.Email);
        contactDb.Should().NotBeNull();
        contactDb.Should().BeEquivalentTo(new
        {
            bookingContact.FirstName,
            bookingContact.LastName,
            bookingContact.Email,
            bookingContact.PhoneNumber
        }, opt => opt.ExcludingMissingMembers());

        var relatedContactDb = await DataContext.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Email == bookingRelatedContact.Email);
        relatedContactDb.Should().NotBeNull();
        relatedContactDb.Should().BeEquivalentTo(new
        {
            bookingRelatedContact.FirstName,
            bookingRelatedContact.LastName,
            bookingRelatedContact.Email,
            bookingRelatedContact.PhoneNumber
        }, opt => opt.ExcludingMissingMembers());

        var contactDbRelationships = await DataContext.ContactRelationships
            .Include(x => x.Contact)
            .AsNoTracking()
            .Where(x => x.ContactId == contactDb.Id)
            .ToArrayAsync();
        contactDbRelationships.Should().NotBeNullOrEmpty();

        contactDbRelationships.Should().ContainEquivalentOf(new
        {
            ContactId = contactDb.Id,
            ToContactId = relatedContactDb.Id,
            RelationshipType = bookingRelatedContact.Relationship,
        }, opt => opt.ExcludingMissingMembers());

        var relatedContactDbRelationships = await DataContext.ContactRelationships
            .Include(x => x.Contact)
            .AsNoTracking()
            .Where(x => x.ContactId == relatedContactDb.Id)
            .ToArrayAsync();
        relatedContactDbRelationships.Should().NotBeNullOrEmpty();

        relatedContactDbRelationships.Should().ContainEquivalentOf(new
        {
            ContactId = relatedContactDb.Id,
            ToContactId = contactDb.Id,
            RelationshipType = bookingRelatedContact.Relationship,
        }, opt => opt.ExcludingMissingMembers());

        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        // assert contact attendee
        taskDb.Contacts.Should().ContainEquivalentOf(new
        {
            ContactId = contactDb.Id,
            AboutContact = true,
            Attending = true,
            IsWithClient = false
        }, opt => opt.ExcludingMissingMembers());

        // assert related contact attendee
        taskDb.Contacts.Should().ContainEquivalentOf(new
        {
            ContactId = relatedContactDb.Id,
            AboutContact = false,
            Attending = true,
            IsWithClient = true
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_immediately_should_create_relationship_to_client_and_not_include_to_attendees()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;
        var bookingContact = new BookingContactDetail()
        {
            FirstName = faker.FirstName,
            LastName = faker.LastName,
            Email = new Email(faker.Email),
            PhoneNumber = faker.Phone
        };

        var relatedContactPerson = new Faker().Person;
        var bookingRelatedContact = new BookingRelatedContactDetail()
        {
            FirstName = relatedContactPerson.FirstName,
            LastName = relatedContactPerson.LastName,
            Email = new Email(relatedContactPerson.Email),
            PhoneNumber = relatedContactPerson.Phone,
            Relationship = "Sibling",
            IsAttending = false
        };
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = bookingContact,
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            RelatedContactDetails = bookingRelatedContact
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();

        var contactDb = await DataContext.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Email == bookingContact.Email);
        contactDb.Should().NotBeNull();
        contactDb.Should().BeEquivalentTo(new
        {
            bookingContact.FirstName,
            bookingContact.LastName,
            bookingContact.Email,
            bookingContact.PhoneNumber
        }, opt => opt.ExcludingMissingMembers());

        var relatedContactDb = await DataContext.Contacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Email == bookingRelatedContact.Email);
        relatedContactDb.Should().NotBeNull();
        relatedContactDb.Should().BeEquivalentTo(new
        {
            bookingRelatedContact.FirstName,
            bookingRelatedContact.LastName,
            bookingRelatedContact.Email,
            bookingRelatedContact.PhoneNumber
        }, opt => opt.ExcludingMissingMembers());

        var contactDbRelationships = await DataContext.ContactRelationships
            .Include(x => x.Contact)
            .AsNoTracking()
            .Where(x => x.ContactId == contactDb.Id)
            .ToArrayAsync();
        contactDbRelationships.Should().NotBeNullOrEmpty();

        contactDbRelationships.Should().ContainEquivalentOf(new
        {
            ContactId = contactDb.Id,
            ToContactId = relatedContactDb.Id,
            RelationshipType = bookingRelatedContact.Relationship,
        }, opt => opt.ExcludingMissingMembers());

        var relatedContactDbRelationships = await DataContext.ContactRelationships
            .Include(x => x.Contact)
            .AsNoTracking()
            .Where(x => x.ContactId == relatedContactDb.Id)
            .ToArrayAsync();
        relatedContactDbRelationships.Should().NotBeNullOrEmpty();

        relatedContactDbRelationships.Should().ContainEquivalentOf(new
        {
            ContactId = relatedContactDb.Id,
            ToContactId = contactDb.Id,
            RelationshipType = bookingRelatedContact.Relationship,
        }, opt => opt.ExcludingMissingMembers());

        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        taskDb.Contacts.Should().NotBeNullOrEmpty();
        taskDb.Contacts.Should().HaveCount(1);
        // assert contact attendee
        taskDb.Contacts.Should().ContainEquivalentOf(new
        {
            ContactId = contactDb.Id,
            AboutContact = true,
            Attending = true,
            IsWithClient = false
        }, opt => opt.ExcludingMissingMembers());

        // assert related contact attendee
        taskDb.Contacts.Should().NotContainEquivalentOf(new
        {
            ContactId = relatedContactDb.Id,
            AboutContact = false,
            Attending = true,
            IsWithClient = true
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_immediately_with_related_contact_should_related_to_the_same_person_record()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;
        var bookingContact = new BookingContactDetail()
        {
            FirstName = faker.FirstName,
            LastName = faker.LastName,
            Email = new Email(faker.Email),
            PhoneNumber = faker.Phone
        };

        var relatedContactPerson = new Faker().Person;
        var bookingRelatedContact = new BookingRelatedContactDetail()
        {
            FirstName = relatedContactPerson.FirstName,
            LastName = relatedContactPerson.LastName,
            Email = bookingContact.Email,
            PhoneNumber = bookingContact.PhoneNumber,
            Relationship = "Sibling",
            IsAttending = true
        };
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = bookingContact,
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            RelatedContactDetails = bookingRelatedContact
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
                .ThenInclude(x => x.Contact)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();
        taskDb.Contacts.Should().HaveCount(2);

        var contacts = taskDb.Contacts.Select(x => x.Contact).ToArray();
        var bookedContact = contacts.First();
        contacts.Should().AllSatisfy(x => x.PersonId.Should().NotBeNull().And.Be(bookedContact.PersonId.Value));
    }

    [Fact]
    public async Task CreateBooking_WhenContactExists_AndRelatedContactIsTheSame_ShouldIgnoreRelatedContact_AndNotCreateRelationship()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        var existingContact = new ContactFaker(ProviderId).Generate();
        DataContext.AddRange(location, providerItem, existingContact.ToDataModel());
        await DataContext.SaveChangesAsync();

        var bookingContact = new BookingContactDetail()
        {
            FirstName = existingContact.FirstName,
            LastName = existingContact.LastName,
            Email = existingContact.Email,
            PhoneNumber = existingContact.PhoneNumber,
        };

        var bookingRelatedContact = new BookingRelatedContactDetail()
        {
            FirstName = bookingContact.FirstName,
            LastName = bookingContact.LastName,
            Email = bookingContact.Email,
            PhoneNumber = bookingContact.PhoneNumber,
            Relationship = "Sibling",
            IsAttending = true
        };

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = bookingContact,
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            RelatedContactDetails = bookingRelatedContact
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
                .ThenInclude(x => x.Contact)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();
        taskDb.Contacts.Should().HaveCount(1);

        var contacts = taskDb.Contacts.Select(x => x.Contact).ToArray();
        var bookedContact = contacts.First();

        var relationships = await this.DataContext.ContactRelationships.AsNoTracking()
            .Where(r => r.ToContactId == bookedContact.Id || r.ContactId == bookedContact.Id)
            .ToListAsync();
        relationships.Should().BeEmpty();
    }


    [Fact]
    public async Task Create_booking_immediately_should_save_timezone_for_new_contact()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
                .ThenInclude(x => x.Contact)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();
        taskDb.Contacts.Should().HaveCount(1);

        var contact = taskDb.Contacts.Select(x => x.Contact).FirstOrDefault();
        contact.Should().NotBeNull();
        contact.Settings.TimeZone.Should().Be("Asia/Manila");
    }

    [Fact]
    public async Task Create_booking_immediately_should_save_timezone_for_existing_contact_with_no_timezone()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        var contact = new ContactFaker(ProviderId)
            .Generate().ToDataModel();

        await DataContext.AddAsync(contact);
        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contact.FirstName,
                LastName = contact.LastName,
                Email = new Email(contact.Email),
                PhoneNumber = contact.PhoneNumber
            },
            TimeZone = "Australia/Sydney",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        var taskDb = await DataContext.Tasks

            .Include(x => x.Contacts)
                .ThenInclude(x => x.Contact)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contactInDb = taskDb.Contacts.Select(x => x.Contact).FirstOrDefault();
        contactInDb.Should().NotBeNull();
        contactInDb.Settings.TimeZone.Should().Be("Australia/Sydney");
    }


    [Fact]
    public async Task Create_booking_immediately_should_not_overwrite_timezone_for_existing_contact_with_timezone()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.Settings, new ContactSettings { TimeZone = null })
            .Generate().ToDataModel();

        await DataContext.AddAsync(contact);
        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();


        var faker = new Faker().Person;

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = contact.FirstName,
                LastName = contact.LastName,
                Email = new Email(contact.Email),
                PhoneNumber = contact.PhoneNumber
            },
            TimeZone = "Australia/Sydney",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();
        var taskDb = await DataContext.Tasks
            .Include(x => x.Contacts)
                .ThenInclude(x => x.Contact)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);
        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contactInDb = taskDb.Contacts.Select(x => x.Contact).FirstOrDefault();
        contactInDb.Should().NotBeNull();
        contactInDb.Settings.TimeZone.Should().Be("Australia/Sydney");
    }

    [Fact]
    public async Task Create_booking_intent_for_recurring_group_event_should_split_task()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jane")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person2.Id)
            .RuleFor(x => x.Email, person2.Email)
            .RuleFor(x => x.PhoneNumber, person2.PhoneNumber)
            .RuleFor(x => x.FirstName, person2.FirstName)
            .RuleFor(x => x.LastName, person2.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 5)
            .Generate()
            .ToDataModel();

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, bookingDateTime.FromDate)
            .RuleFor(x => x.EndDate, bookingDateTime.ToDate)
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY;COUNT=3"))
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });

        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            location, providerItem, task
        );
        await DataContext.SaveChangesAsync();

        var fromDate = bookingDateTime.FromDate.AddDays(1);
        var toDate = bookingDateTime.ToDate.AddDays(1);

        var requestModel = new BookingIntentRequest
        {
            StartDate = fromDate,
            EndDate = toDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person2.FirstName,
                LastName = person2.LastName,
                Email = new Email(person2.Email),
                PhoneNumber = person2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var itemsDb = await DataContext.Tasks
            .Include(x => x.Contacts)
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        itemsDb.Should().HaveCount(2);

        var parentTask = itemsDb.FirstOrDefault(x => x.Id == task.Id);
        var childTask = itemsDb.FirstOrDefault(x => x.ParentId == task.Id);

        // parent task should be untouched
        parentTask.Should().NotBeNull();
        parentTask.Contacts.Should().HaveCount(1);

        // child task should have the new contact
        childTask.Should().NotBeNull();
        childTask.Contacts.Should().HaveCount(2);
        childTask.Should().BeEquivalentTo(new
        {
            StartDate = fromDate,
            EndDate = toDate,
            ExDate = (string)null,
            RRule = (string)null,
            OccurrenceEndDate = (DateTime?)null
        },
            opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());
    }

    [Fact]
    public async Task Create_booking_intent_for_recurring_group_event_should_resolve_existing_exception()
    {
        var person1 = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact1 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person1.Id)
            .RuleFor(x => x.Email, person1.Email)
            .RuleFor(x => x.PhoneNumber, person1.PhoneNumber)
            .RuleFor(x => x.FirstName, person1.FirstName)
            .RuleFor(x => x.LastName, person1.LastName)
            .Generate()
            .ToDataModel();

        var person2 = new PersonFaker()
            .RuleFor(x => x.FirstName, "Jane")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();
        var contact2 = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person2.Id)
            .RuleFor(x => x.Email, person2.Email)
            .RuleFor(x => x.PhoneNumber, person2.PhoneNumber)
            .RuleFor(x => x.FirstName, person2.FirstName)
            .RuleFor(x => x.LastName, person2.LastName)
            .Generate()
            .ToDataModel();

        var location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();

        var providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, [PersonId])
            .RuleFor(x => x.LocationIds, [location.Id])
            .RuleFor(x => x.AllowGroupEvents, true)
            .RuleFor(x => x.MaximumAttendeeLimit, 5)
            .Generate()
            .ToDataModel();

        var startDateTime = bookingDateTime.FromDate.WithoutSecsAndMs();
        var endDateTime = startDateTime.AddHours(1);

        var task = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, startDateTime)
            .RuleFor(x => x.EndDate, endDateTime)
            .RuleFor(x => x.RRule, new RRule("FREQ=DAILY;COUNT=3"))
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });

        var exceptionTask = new TaskFaker()
            .RuleFor(x => x.Id, Guid.NewGuid())
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.CreatedByPersonId, PersonId)
            .RuleFor(x => x.LastUpdatedByPersonId, PersonId)
            .RuleFor(x => x.StartDate, startDateTime)
            .RuleFor(x => x.EndDate, endDateTime)
            .RuleFor(x => x.ParentId, task.Id)
            .Generate()
            .ToDataModel()
            .WithContact(new List<TaskContactDataModel>
            {
                new TaskContactDataModel() { ContactId = contact1.Id }
            })
            .WithServices(new List<SimpleProviderItemDataModel>
            {
                new SimpleProviderItemDataModel()
                {
                    Id = providerItem.Id,
                    Title = providerItem.Title,
                    DisplayName = providerItem.DisplayName,
                    Price = providerItem.Price,
                    Duration = providerItem.Duration,
                    AllowGroupEvents = providerItem.AllowGroupEvents,
                    MaximumAttendeeLimit = providerItem.MaximumAttendeeLimit
                }
            });

        await DataContext.AddRangeAsync(
            person1, contact1,
            person2, contact2,
            location, providerItem, task, exceptionTask
        );
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = startDateTime,
            EndDate = endDateTime,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person2.FirstName,
                LastName = person2.LastName,
                Email = new Email(person2.Email),
                PhoneNumber = person2.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
            TaskId = task.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var itemsDb = await DataContext.Tasks
            .AsNoTracking()
            .Where(i => i.ProviderId == ProviderId)
            .ToListAsync();

        itemsDb.Should().HaveCount(2);

        var parentTask = itemsDb.FirstOrDefault(x => x.Id == task.Id);
        var childTask = itemsDb.FirstOrDefault(x => x.Id == exceptionTask.Id);

        // parent task should be untouched
        parentTask.Should().NotBeNull();
        parentTask.Contacts.Should().HaveCount(1);

        // child task should have the new contact
        childTask.Should().NotBeNull();
        childTask.Contacts.Should().HaveCount(2);
        childTask.Should().BeEquivalentTo(new
        {
            StartDate = startDateTime,
            EndDate = endDateTime,
            ExDate = (string)null,
            RRule = (string)null,
            OccurrenceEndDate = (DateTime?)null
        },
            opt => opt.ExcludingMissingMembers().UsingSimpleDateTimePrecision());
    }

    [Fact]
    public async Task Create_booking_immediately_no_person_should_resolve_default_language_to_en()
    {

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);

        task.Should().NotBeNull();

        var contactId = task.Contacts.FirstOrDefault().ContactId;

        var contactLanguages = await DataContext.ContactLanguages
            .AsNoTracking()
            .Where(x => x.ContactId == contactId)
            .ToArrayAsync();

        contactLanguages.Should().NotBeNullOrEmpty();
        contactLanguages.Should().ContainSingle(x => x.Language == "en");
    }

    [Fact]
    public async Task Create_booking_immediately_has_no_primary_language_should_set_default_contact_language()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();

        var personalSettings = new PersonalSettingsFaker(person.Id)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Locale, "ar")
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(person, personalSettings, location, providerItem, contact);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);

        task.Should().NotBeNull();

        var contactId = task.Contacts.FirstOrDefault().ContactId;

        var contactLanguages = await DataContext.ContactLanguages
            .AsNoTracking()
            .Where(x => x.ContactId == contactId)
            .ToArrayAsync();

        contactLanguages.Should().NotBeNullOrEmpty();

        contactLanguages.Should().HaveCount(1);

        contactLanguages.Should().ContainSingle(x => x.Language == Localization.DefaultCulture.Name && x.IsPrimary);
    }

    [Fact]
    public async Task Create_booking_immediately_has_no_primary_language_make_existing_as_primary()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();

        var personalSettings = new PersonalSettingsFaker(person.Id)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Locale, "ar")
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var primaryLanguage = new ContactLanguageFaker(false)
            .RuleFor(x => x.Language, "tl-PH")
            .Generate()
            .ToDataModel(ProviderId, contact.Id);

        var secondaryLanguage = new ContactLanguageFaker(false)
            .RuleFor(x => x.Language, "en")
            .Generate()
            .ToDataModel(ProviderId, contact.Id);

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(person, personalSettings, location, providerItem, contact, primaryLanguage, secondaryLanguage);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);

        task.Should().NotBeNull();

        var contactId = task.Contacts.FirstOrDefault().ContactId;

        var contactLanguages = await DataContext.ContactLanguages
            .AsNoTracking()
            .Where(x => x.ContactId == contactId)
            .ToArrayAsync();

        contactLanguages.Should().NotBeNullOrEmpty();

        contactLanguages.Should().ContainSingle(x => x.Language == "en" && x.IsPrimary);
    }


    [Fact]
    public async Task Create_booking_immediately_has_primary_language_should_not_resolve()
    {
        var person = new PersonFaker()
            .RuleFor(x => x.FirstName, "John")
            .RuleFor(x => x.LastName, "Doe")
            .Generate()
            .ToDataModel();

        var personalSettings = new PersonalSettingsFaker(person.Id)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Locale, "ar")
            .Generate()
            .ToDataModel();

        var contact = new ContactFaker(ProviderId)
            .RuleFor(x => x.PersonId, person.Id)
            .RuleFor(x => x.Email, person.Email)
            .RuleFor(x => x.PhoneNumber, person.PhoneNumber)
            .RuleFor(x => x.FirstName, person.FirstName)
            .RuleFor(x => x.LastName, person.LastName)
            .Generate()
            .ToDataModel();

        var primaryLanguage = new ContactLanguageFaker(true)
            .RuleFor(x => x.Language, "tl-PH")
            .Generate()
            .ToDataModel(ProviderId, contact.Id);

        var secondaryLanguage = new ContactLanguageFaker(false)
            .RuleFor(x => x.Language, "en")
            .Generate()
            .ToDataModel(ProviderId, contact.Id);

        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(person, personalSettings, location, providerItem, contact, primaryLanguage, secondaryLanguage);
        await DataContext.SaveChangesAsync();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = person.FirstName,
                LastName = person.LastName,
                Email = new Email(person.Email),
                PhoneNumber = person.PhoneNumber
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        // Act
        var response = await ClientApi.SendAsync(request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var task = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);

        task.Should().NotBeNull();

        var contactId = task.Contacts.FirstOrDefault().ContactId;

        var contactLanguages = await DataContext.ContactLanguages
            .AsNoTracking()
            .Where(x => x.ContactId == contactId)
            .ToArrayAsync();

        contactLanguages.Should().NotBeNullOrEmpty();

        contactLanguages.Should().ContainSingle(x => x.Language == "tl-PH" && x.IsPrimary);

        contactLanguages.Should().ContainSingle(x => x.Language == "en" && !x.IsPrimary);

    }

    [Fact]
    public async Task Create_booking_immediately_should_set_locations_snapshots()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Person.FirstName,
                LastName = faker.Person.LastName,
                Email = new Email(faker.Person.Email),
                PhoneNumber = faker.Person.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id,
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = await DataContext.Tasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.TaskId);

        var locationSnapshot = new TaskLocationSnapshot
        {
            LocationId = location.Id,
            Name = location.Name,
            AddressDetails = location.Address,
            Address = location.Address,
            PosCode = location.PosCode,
            Type = location.Type,
            Product = location.Product
        };

        TaskLocationSnapshot[] expected = [locationSnapshot];

        taskDb.Should().BeEquivalentTo(new
        {
            LocationsSnapshot = expected
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task CreateBooking_should_save_Message_to_task_contact()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker().Person;
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test message from booking",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.FirstName,
                LastName = faker.LastName,
                Email = new Email(faker.Email),
                PhoneNumber = faker.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskDb = await DataContext.Tasks
            .Include(taskDataModel => taskDataModel.Contacts)
            .FirstAsync(x => x.Id == result.TaskId && x.ProviderId == ProviderId);

        taskDb.Should().NotBeNull();
        taskDb.Contacts.Should().NotBeNullOrEmpty();

        var contact = taskDb.Contacts.First();
        contact.Reason.Should().Be("this is a test message from booking");
    }

    [Fact]
    public async Task CreateBooking_with_relatedontact_should_save_Message_to_task_related_contact()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker1 = new Faker().Person;
        var faker2 = new Faker().Person;
        var contactEmail = faker1.Email.ToLower();
        var relatedContactEmail = faker2.Email.ToLower();
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "this is a test message from booking by related attendee",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker1.FirstName,
                LastName = faker1.LastName,
                Email = new Email(contactEmail),
                PhoneNumber = faker1.Phone
            },
            RelatedContactDetails = new BookingRelatedContactDetail()
            {
                FirstName = faker1.FirstName,
                LastName = faker1.LastName,
                Email = new Email(relatedContactEmail),
                PhoneNumber = faker1.Phone,
                Relationship = "Sibling",
                IsAttending = true
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskContacts = await DataContext.TaskContacts
            .Include(taskContactDataModel => taskContactDataModel.Contact)
            .Where(x => x.TaskId == result.TaskId && x.Task.ProviderId == ProviderId)
            .ToArrayAsync();

        taskContacts.Should().NotBeNull();
        taskContacts.Should().NotBeNullOrEmpty();
        taskContacts.Should().HaveCount(2);

        var contact = taskContacts.First(x => x.Contact.Email == contactEmail);
        contact.Reason.Should().BeEmpty();

        var relatedContact = taskContacts.First(x => x.Contact.Email == relatedContactEmail);
        relatedContact.Reason.Should().Be("this is a test message from booking by related attendee");
    }
    
    [Fact]
    public async Task Create_booking_should_set_client_language()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker1 = new Faker().Person;
        var faker2 = new Faker().Person;
        var contactEmail = faker1.Email.ToLower();
        var relatedContactEmail = faker2.Email.ToLower();
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "some message",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker1.FirstName,
                LastName = faker1.LastName,
                Email = new Email(contactEmail),
                PhoneNumber = faker1.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
           .WithHeader("Accept-Language", "fr")
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskContacts = await DataContext.TaskContacts
            .Include(taskContactDataModel => taskContactDataModel.Contact)
            .Where(x => x.TaskId == result.TaskId && x.Task.ProviderId == ProviderId)
            .ToArrayAsync();

        taskContacts.Should().NotBeNull();
        taskContacts.Should().NotBeNullOrEmpty();

        var contact = taskContacts.First();
        var contactDb = await DataContext.Contacts
            .Include(x => x.Languages)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == contact.ContactId);
        contactDb.Should().NotBeNull();
        contactDb.Languages.Should().NotBeNullOrEmpty();
        contactDb.Languages.Should().BeEquivalentTo(new[]
        {
            new
            {
                Language = "fr",
            }
        }, opt => opt.ExcludingMissingMembers());
    }
    
    [Fact]
    public async Task Create_booking_should_set_default_client_language_to_english()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = null;

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker1 = new Faker().Person;
        var faker2 = new Faker().Person;
        var contactEmail = faker1.Email.ToLower();
        var relatedContactEmail = faker2.Email.ToLower();
        var currentUtcDate = DateTime.UtcNow;
        var requestModel = new BookingIntentRequest
        {
            StartDate = currentUtcDate.AddMinutes(15),
            EndDate = currentUtcDate.AddMinutes(45),
            LocationId = location.Id,
            Message = "some message",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker1.FirstName,
                LastName = faker1.LastName,
                Email = new Email(contactEmail),
                PhoneNumber = faker1.Phone
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
           .WithBearerAuthorization(IdentityToken)
           .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);
        await response.Should().HaveStatusCodeAsync(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var taskContacts = await DataContext.TaskContacts
            .Include(taskContactDataModel => taskContactDataModel.Contact)
            .Where(x => x.TaskId == result.TaskId && x.Task.ProviderId == ProviderId)
            .ToArrayAsync();

        taskContacts.Should().NotBeNull();
        taskContacts.Should().NotBeNullOrEmpty();

        var contact = taskContacts.First();
        var contactDb = await DataContext.Contacts
            .Include(x => x.Languages)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == contact.ContactId);
        contactDb.Should().NotBeNull();
        contactDb.Languages.Should().NotBeNullOrEmpty();
        contactDb.Languages.Should().BeEquivalentTo(new[]
        {
            new
            {
                Language = "en",
            }
        }, opt => opt.ExcludingMissingMembers());
    }

    [Fact]
    public async Task Create_booking_immediately_should_set_attendee_status()
    {
        GenerateData(out var location, out var providerItem);
        providerItem.OnlineBookingOptions = new ProviderItemOnlineBookingOptions(false, false);

        await DataContext.AddRangeAsync(location, providerItem);
        await DataContext.SaveChangesAsync();

        var faker = new Faker();

        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = location.Id,
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                Email = faker.CarePatron().Email(),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "Asia/Manila",
            ItemId = providerItem.Id
        };

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();

        var response = await ClientApi.SendAsync(request);

        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<BookingIntentResponse>();
        result.Should().NotBeNull();

        var contact = await DataContext.TaskContacts
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.TaskId == result.TaskId);

        contact.Should().NotBeNull();

        contact.AttendeeStatusId.Should().Be(nameof(TaskContactStatus.Confirmed));
    }

    [Fact]
    public async Task Create_booking_intent_should_not_validate_token_when_no_token_is_provided()
    {
        var faker = new Faker();
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = Guid.NewGuid(),
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                Email = faker.CarePatron().Email(),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "Asia/Manila",
            ItemId = Guid.NewGuid(),
            Token = null
        };
        
        var recaptchaRepository = ResolveService<GoogleReCaptchaRepositoryMock>();
        recaptchaRepository.Setup(x => x.Validate(null)).ReturnsAsync(false);

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();
        
        var response = await ClientApi.SendAsync(request);
        recaptchaRepository.Verify(x => x.Validate(null), Times.Never);
    }
    
    [Fact]
    public async Task Create_booking_intent_should_validate_token_when_provided()
    {
        var faker = new Faker();
        var requestModel = new BookingIntentRequest
        {
            StartDate = bookingDateTime.FromDate,
            EndDate = bookingDateTime.ToDate,
            LocationId = Guid.NewGuid(),
            Message = "this is a test",
            StaffId = PersonId,
            ContactDetails = new BookingContactDetail()
            {
                FirstName = faker.Name.FirstName(),
                LastName = faker.Name.LastName(),
                Email = faker.CarePatron().Email(),
                PhoneNumber = faker.Phone.PhoneNumber()
            },
            TimeZone = "Asia/Manila",
            ItemId = Guid.NewGuid(),
            Token = "some token"
        };
        
        var recaptchaRepository = ResolveService<GoogleReCaptchaRepositoryMock>();
        recaptchaRepository.Setup(x => x.Validate(requestModel.Token)).ReturnsAsync(false);

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, $"api/booking/{ProviderId}/bookingIntent")
            .WithBearerAuthorization(IdentityToken)
            .WithPayload(requestModel)
            .Create();
        
        var response = await ClientApi.SendAsync(request);
        recaptchaRepository.Verify(x => x.Validate(requestModel.Token), Times.Once);
    }


    private void GenerateData(out ProviderLocationDataModel location, out ProviderItemDataModel providerItem, decimal? servicePrice = null)
    {
        location = new ProviderLocationFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .Generate()
            .ToDataModel();
        providerItem = new ProviderItemFaker()
            .RuleFor(x => x.ProviderId, ProviderId)
            .RuleFor(x => x.BookableOnline, true)
            .RuleFor(x => x.AllowNewClients, true)
            .RuleFor(x => x.AssignedStaffIds, new Guid[] { PersonId })
            .RuleFor(x => x.OnlineBookingOptions, f => new OnlineBookingPaymentPolicy(true, true))
            .RuleFor(x => x.LocationIds, new[] { location.Id })
            .RuleFor(x => x.Price, (Faker f) => servicePrice ?? f.Finance.Amount(1))
            .Generate()
            .ToDataModel();
    }


    private async Task<ProviderOnlineBookingOptions> SaveProviderOnlineBookingOptions(bool requirePaymentMethod, bool processAtTimeOfBooking)
    {
        var modelRequest = SaveProviderOnlineBookingOptionsRequestBuilder.Any()
                        .WithProviderId(ProviderId)
                        .WithPaymentPolicy(new OnlineBookingPaymentPolicy
                        {
                            RequirePaymentMethod = requirePaymentMethod,
                            ProcessAtTimeOfBooking = processAtTimeOfBooking,
                        })
                        .CreateRequest();

        var mapper = ResolveService<IMapper>();
        var providerRepository = ResolveService<IProviderRepository>();
        var dataModel = mapper.Map<ProviderOnlineBookingOptions>(modelRequest);

        await providerRepository.SaveProviderOnlineBookingOptions(dataModel);
        return modelRequest;
    }
}