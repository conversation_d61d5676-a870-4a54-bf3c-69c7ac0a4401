using System.Net;
using AutoBogus;
using Bogus;
using carepatron.core.Application.Contacts.Models;
using carepatron.core.Application.History.Models;
using carepatron.core.Application.Insurance.Events;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.Schema.Models.Properties;
using carepatron.core.Application.Workspace.Providers.Models;
using carepatron.core.Models.Execution;
using carepatron.core.Models.Notes;
using carepatron.core.Utilities;
using carepatron.infra.sql.Models.ICDCode;
using LinqKit;
using Microsoft.EntityFrameworkCore;
using tests.common.Builders.DataModels;
using tests.common.Data.Mappers;
using tests.common.Mocks;
using tests.component.Builders;

namespace tests.component.Features.Insurance;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class CreateClaimsTests : BaseClaimsTests
{
    public CreateClaimsTests(ComponentTestFixture fixture) : base(fixture)
    {
        var fileStorageRepositoryMock = ResolveService<FileStorageRepositoryMock>();
        fileStorageRepositoryMock.UseRealImplementation();
    }

    [Fact]
    public async Task CreateClaim_ForbiddenIfNoPermissions()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin(noPermissions: true);

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.Add(contact.ToDataModel());
        await DataContext.SaveChangesAsync();

        var payload = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate()
            .ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.Forbidden);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
        result.Code.Should().Be(Errors.UnauthorisedErrorCode);
        result.Details.Should().Be(Errors.UnauthorisedErrorDetails);
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithNumberGenerated()
    {
        Fixture.ClearEvents();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());
        
        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new[]
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };
        
        var policy = CreateContactInsurancePolicy(provider, contact, memberContact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines(serviceLines)
            .WithAmountPaid(billableItem)
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.Number.Should().NotBeNullOrWhiteSpace();
        result.Status.Should().Be(payload.Status);
        result.SubmissionMethod.Should().Be(payload.SubmissionMethod);
        result.Type.Should().Be(ClaimType.USProfessional);
        result.ContactId.Should().Be(payload.Client.ContactId);
        result.AmountPaid.Should().Be(payload.AmountPaid);
        result.OriginalReferenceNumber.Should().Be(payload.OriginalReferenceNumber);
        result.PatientsAccountNumber.Should().Be(payload.PatientsAccountNumber);
        result.PriorAuthorizationNumber.Should().Be(payload.PriorAuthorizationNumber);
        result.ResubmissionCode.Should().Be(payload.ResubmissionCode);
        result.AdditionalClaimInformation.Should().Be(payload.AdditionalClaimInformation);
        result.ContactInsurancePolicy.CoverageType.Should().Be(payload.ContactInsurancePolicy.CoverageType);
        result.ContactInsurancePolicy.OtherCoverageTypeName.Should().BeNullOrEmpty();
        result.ContactInsurancePolicy.PayerNumber.Should().Be(payload.ContactInsurancePolicy.PayerNumber);
        result.ContactInsurancePolicy.PolicyHolderAddress.StreetAddress.Should().Be(payload.ContactInsurancePolicy.PolicyHolderAddress.StreetAddress);
        result.ContactInsurancePolicy.PolicyHolderAddress.ZipCode.Should().Be(payload.ContactInsurancePolicy.PolicyHolderAddress.ZipCode);
        result.ContactInsurancePolicy.PolicyHolderAddress.State.Should().Be(payload.ContactInsurancePolicy.PolicyHolderAddress.State);
        result.ContactInsurancePolicy.PolicyHolderAddress.City.Should().Be(payload.ContactInsurancePolicy.PolicyHolderAddress.City);
        result.ContactInsurancePolicy.PolicyHolderAddress.Country.Should().Be(payload.ContactInsurancePolicy.PolicyHolderAddress.Country);

        // Ensure client control number is set and lookup saved 
        var lookup = await DataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == result.ContactId);
        lookup.Should().NotBeNull();
        var control = long.TryParse(result.ClientControlNumber, out var controlNumber).Should().BeTrue();
        controlNumber.Should().Be(lookup.Identity);

        result.ServiceLines.ForEach(line =>
        {
            var claimLine = payload.ServiceLines.First(s => s.Id == line.Id);
            line.POSCode.Should().Be(claimLine.POSCode);
            line.Modifiers.ForEach(modifier => claimLine.Modifiers.Should().Contain(modifier));
            line.DiagnosticCodeReferences.ForEach(code => claimLine.DiagnosticCodeReferences.Should().Contain(code));
            line.Description.Should().Be(claimLine.Description);
            line.Detail.Should().Be(claimLine.Detail);
            line.ServiceId.Should().Be(claimLine.ServiceId);
        });

        var dbClaimHeader = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbClaimHeader.Should().NotBeNull();
        dbClaimHeader.Number.Should().NotBeNullOrEmpty();
        dbClaimHeader.Amount.Should().Be(result.ServiceLines.Sum(x => x.Amount));
        dbClaimHeader.FromDate.Should().Be(result.ServiceLines.Min(x => x.Date));
        dbClaimHeader.ToDate.Should().Be(result.ServiceLines.Max(x => x.Date));

        var claimEvents = Fixture.GetPublishedEventsOfType<USProfessionalClaimCreatedEvent>();
        claimEvents.Should().HaveCountGreaterThan(0);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbInsuranceClaim.Should().NotBeNull();

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithOtherCoverageTypePopulated()
    {
        Fixture.ClearEvents();

        var faker = new Faker();
        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id)
            .RuleFor(x => x.CoverageType, InsuranceCoverageType.Other)
            .RuleFor(x => x.OtherCoverageTypeName, f => f.Random.Word())
            .Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id);
        var serviceLines = new[]
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };
        
        var policy = CreateContactInsurancePolicy(provider, contact, memberContact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines(serviceLines)
            .WithAmountPaid(billableItem)
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.Number.Should().NotBeNullOrWhiteSpace();
        result.Status.Should().Be(payload.Status);
        result.SubmissionMethod.Should().Be(payload.SubmissionMethod);
        result.Type.Should().Be(ClaimType.USProfessional);
        result.ContactId.Should().Be(payload.Client.ContactId);
        result.AmountPaid.Should().Be(payload.AmountPaid);
        result.OriginalReferenceNumber.Should().Be(payload.OriginalReferenceNumber);
        result.PatientsAccountNumber.Should().Be(payload.PatientsAccountNumber);
        result.PriorAuthorizationNumber.Should().Be(payload.PriorAuthorizationNumber);
        result.ResubmissionCode.Should().Be(payload.ResubmissionCode);
        result.ContactInsurancePolicy.CoverageType.Should().Be(InsuranceCoverageType.Other);
        result.ContactInsurancePolicy.OtherCoverageTypeName.Should().Be(payer.OtherCoverageTypeName);
        result.ServiceLines.ForEach(line =>
        {
            var claimLine = payload.ServiceLines.First(s => s.Id == line.Id);
            line.POSCode.Should().Be(claimLine.POSCode);
            line.Modifiers.ForEach(modifier => claimLine.Modifiers.Should().Contain(modifier));
            line.DiagnosticCodeReferences.ForEach(code => claimLine.DiagnosticCodeReferences.Should().Contain(code));
            line.Description.Should().Be(claimLine.Description);
            line.Detail.Should().Be(claimLine.Detail);
            line.ServiceId.Should().Be(claimLine.ServiceId);
        });

        var dbClaimHeader = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbClaimHeader.Should().NotBeNull();
        dbClaimHeader.Number.Should().NotBeNullOrEmpty();
        dbClaimHeader.Amount.Should().Be(result.ServiceLines.Sum(x => x.Amount));
        dbClaimHeader.FromDate.Should().Be(result.ServiceLines.Min(x => x.Date));
        dbClaimHeader.ToDate.Should().Be(result.ServiceLines.Max(x => x.Date));

        var claimEvents = Fixture.GetPublishedEventsOfType<USProfessionalClaimCreatedEvent>();
        claimEvents.Should().HaveCountGreaterThan(0);

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbInsuranceClaim.Should().NotBeNull();

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithTasksMapped()
    {
        Fixture.ClearEvents();

        var faker = new Faker();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var location = new ProviderLocationFaker().RuleFor(x => x.ProviderId, provider.Id).Generate();
        var taskLocation = new TaskLocationSnapshotFaker(provider.Id).Generate();
        var task1 = TaskDataModelBuilder.AnyClientEvent()
            .WithContacts(contact.Id)
            .WithStaff(staffMember.Id)
            .WithProvider(provider.Id)
            .WithLocationsSnapshot([taskLocation])
            .Create();
        var task2 = TaskDataModelBuilder.AnyClientEvent()
            .WithContacts(contact.Id)
            .WithStaff(staffMember.Id)
            .WithProvider(provider.Id)
            .WithLocationsSnapshot([taskLocation])
            .Create();
        DataContext.Tasks.AddRange(task1, task2);

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        // Since task ids are indexed for claim mapping, api should handle multiple service
        // lines from billable items that are coming from a same task
        var billableItem1 = CreateBillableTestData(contact, provider.Id, null, task1);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, null, task2);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id).Generate(),
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id).Generate()
        };

        var policy = CreateContactInsurancePolicy(provider, contact, memberContact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());
        
        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithBillingDetail(provider.Id, defaultBillingProfile)
            .WithClient(provider.Id, contact)
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .WithDiagnosticCodes(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithRenderingProviders(provider.Id, staffMember)
            .WithServiceLines([.. serviceLines])
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();

        // Since a task with location was attached to the claim, service facility should be mapped
        result.ServiceFacility.Should().NotBeNull();
        result.ServiceFacility.ProviderLocationId.Should().Be(taskLocation.LocationId);
        result.ServiceFacility.Name.Should().Be(taskLocation.Name);
        result.ServiceFacility.Address.StreetAddress.Should().Be(taskLocation.AddressDetails.StreetAddress);
        result.ServiceFacility.Address.ZipCode.Should().Be(taskLocation.AddressDetails.ZipCode);
        result.ServiceFacility.Address.State.Should().Be(taskLocation.AddressDetails.State);
        result.ServiceFacility.Address.City.Should().Be(taskLocation.AddressDetails.City);
        result.ServiceFacility.Address.Country.Should().Be(taskLocation.AddressDetails.Country);
        result.ServiceFacility.PlaceOfService.Should().Be(taskLocation.PosCode);
        result.ServiceFacility.ProviderId.Should().Be(provider.Id);

        var dbInsuranceClaimTask = await DataContext.InsuranceClaimTasks
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.InsuranceClaimId == result.Id && x.TaskId == task1.Id);
        dbInsuranceClaimTask.Should().NotBeNull();

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithClientContactOnly()
    {
        Fixture.ClearEvents();

        var faker = new Faker();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        var memberContact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([
            contact.ToDataModel(),
            memberContact.ToDataModel()
        ]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(provider.Id, contact.Id, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = memberContact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var task = TaskDataModelBuilder.AnyClientEvent().WithContacts(contact.Id).WithStaff(staffMember.Id).WithProvider(provider.Id).Create();
        DataContext.Tasks.Add(task);

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var billableItem = CreateBillableTestData(contact, provider.Id, null, task);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem.Id).Generate()
        };

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.Client.Should().NotBeNull();

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithPrepopulatedData()
    {
        Fixture.ClearEvents();

        var faker = new Faker();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var icdCodes = await DataContext.ICDCodes
            .AsNoTracking()
            .Take(5)
            .ToListAsync();

        var contactNote = NoteModelBuilder.Any()
            .WithContactId(contact.Id)
            .WithProviderId(provider.Id)
            .WithDiagnoses([.. icdCodes.Select(s => new ICDCodeDataModel { Code = s.Code, Description = s.Description }).ToList()])
            .WithStatus(NoteStatus.Published)
            .WithCreatedByPersonId(staffPerson.Id)
            .Create();
        contactNote.OccurrenceDateTimeUtc = DateTime.UtcNow.AddDays(-4);
        DataContext.ContactNotes.Add(contactNote);

        var billableItem1 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var serviceLines = new[]
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.AddDays(-7).ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate(),
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate()
        };
        
        var policy = CreateContactInsurancePolicy(provider, contact, contact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithServiceLines(serviceLines)
            .WithAmountPaid(billableItem1, billableItem2)
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.Number.Should().NotBeNullOrEmpty();
        result.Status.Should().Be(payload.Status);
        result.SubmissionMethod.Should().Be(payload.SubmissionMethod);
        result.Type.Should().Be(ClaimType.USProfessional);
        result.ContactId.Should().Be(payload.Client.ContactId);
        result.AmountPaid.Should().Be(payload.AmountPaid);
        result.OriginalReferenceNumber.Should().Be(payload.OriginalReferenceNumber);
        result.PatientsAccountNumber.Should().Be(payload.PatientsAccountNumber);
        result.PriorAuthorizationNumber.Should().Be(payload.PriorAuthorizationNumber);
        result.ResubmissionCode.Should().Be(payload.ResubmissionCode);

        // Ensure client control number is set and lookup saved 
        var lookup = await DataContext.ClearingHouseIdLookups
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == result.ContactId);
        lookup.Should().NotBeNull();
        var control = long.TryParse(result.ClientControlNumber, out var controlNumber).Should().BeTrue();
        controlNumber.Should().Be(lookup.Identity);

        // Assert service lines are prepopulated with data
        var icdCodeCodes = icdCodes.Select(s => s.Code).ToList();
        result.ServiceLines.ForEach(line =>
        {
            var claimLine = payload.ServiceLines.First(s => s.Id == line.Id);
            line.POSCode.Should().Be(claimLine.POSCode);
            line.Modifiers.ForEach(modifier => claimLine.Modifiers.Should().Contain(modifier));
            line.DiagnosticCodeReferences.ForEach(code => icdCodeCodes.Should().Contain(code));
            line.Description.Should().Be(claimLine.Description);
            line.Detail.Should().Be(claimLine.Detail);
            line.ServiceId.Should().Be(claimLine.ServiceId);
        });

        // Assert diagnostic codes are prepopulated with data
        result.DiagnosticCodes.Should().NotBeNullOrEmpty();
        result.DiagnosticCodes.Should().HaveCount(5);
        result.DiagnosticCodes.ForEach(code =>
        {
            var icdCode = icdCodes.First(c => c.Code == code.Code);
            code.Code.Should().Be(icdCode.Code);
        });

        // Assert rendering providers are prepopulated with staff members
        result.RenderingProviders.Should().NotBeNullOrEmpty();
        result.RenderingProviders.Should().HaveCount(1);
        result.RenderingProviders[0].StaffMember.Id.Should().Be(staffPerson.Id);
        result.RenderingProviders[0].FirstName.Should().Be(staffPerson.FirstName);
        result.RenderingProviders[0].LastName.Should().Be(staffPerson.LastName);

        // Assert contact insurance policy is populated
        result.ContactInsurancePolicy.Should().NotBeNull();
        result.ContactInsurancePolicy.ContactInsurancePolicy.Id.Should().Be(policy.Id);
        result.ContactInsurancePolicy.InsuranceType.Should().Be(policy.InsuranceType);
        result.ContactInsurancePolicy.PolicyHolderFirstName.Should().Be(policy.PolicyHolder.Contact.FirstName);
        result.ContactInsurancePolicy.PolicyHolderMiddleName.Should().Be(policy.PolicyHolder.Contact.MiddleNames);
        result.ContactInsurancePolicy.PolicyHolderLastName.Should().Be(policy.PolicyHolder.Contact.LastName);
        result.ContactInsurancePolicy.PolicyHolderDateOfBirth.Should().Be(policy.PolicyHolder.Contact.BirthDate);
        result.ContactInsurancePolicy.PolicyHolderPhoneNumberDetails.Number.Should().Be(policy.PolicyHolder.Contact.PhoneNumber);
        result.ContactInsurancePolicy.PolicyHolderSex.Should().Be(ContactUtilities.GetSexValueFromOptionSetValue(policy.PolicyHolder.Contact.Sex));
        result.ContactInsurancePolicy.PayerId.Should().Be(policy.Payer.Id);
        result.ContactInsurancePolicy.PayerName.Should().Be(policy.Payer.Name);
        result.ContactInsurancePolicy.PayerPhoneNumberDetails.Number.Should().Be(policy.Payer.PhoneNumber);
        result.ContactInsurancePolicy.PayerNumber.Should().Be(policy.Payer.PayerId);
        result.ContactInsurancePolicy.Address.StreetAddress.Should().Be(policy.Payer.Address.StreetAddress);
        result.ContactInsurancePolicy.Address.ZipCode.Should().Be(policy.Payer.Address.ZipCode);
        result.ContactInsurancePolicy.Address.State.Should().Be(policy.Payer.Address.State);
        result.ContactInsurancePolicy.Address.City.Should().Be(policy.Payer.Address.City);
        result.ContactInsurancePolicy.Address.Country.Should().Be(policy.Payer.Address.Country);
        result.ContactInsurancePolicy.CoverageType.Should().Be(policy.Payer.CoverageType);
        result.ContactInsurancePolicy.OtherCoverageTypeName.Should().Be(policy.Payer.OtherCoverageTypeName);
        result.ContactInsurancePolicy.PolicyHolderRelationshipType.Should().Be(policy.PolicyHolder.Type);
        result.ContactInsurancePolicy.PolicyHolderGroupId.Should().Be(policy.GroupId);
        result.ContactInsurancePolicy.PolicyHolderMemberId.Should().Be(policy.MemberId);
        result.ContactInsurancePolicy.PolicyHolderAddress.StreetAddress.Should().Be(policy.PolicyHolder.Contact.Address.StreetAddress);
        result.ContactInsurancePolicy.PolicyHolderAddress.ZipCode.Should().Be(policy.PolicyHolder.Contact.Address.ZipCode);
        result.ContactInsurancePolicy.PolicyHolderAddress.State.Should().Be(policy.PolicyHolder.Contact.Address.State);
        result.ContactInsurancePolicy.PolicyHolderAddress.City.Should().Be(policy.PolicyHolder.Contact.Address.City);
        result.ContactInsurancePolicy.PolicyHolderAddress.Country.Should().Be(policy.PolicyHolder.Contact.Address.Country);

        // Assert billing profile is populated
        result.BillingDetail.Should().NotBeNull();
        result.BillingDetail.ProviderId.Should().Be(defaultBillingProfile.ProviderId);
        result.BillingDetail.Type.Should().Be(defaultBillingProfile.Type);
        result.BillingDetail.TaxNumberType.Should().Be(defaultBillingProfile.TaxNumberType);
        result.BillingDetail.TaxNumber.Should().Be(defaultBillingProfile.TaxNumber);
        result.BillingDetail.NationalProviderId.Should().Be(defaultBillingProfile.NationalProviderId);
        result.BillingDetail.TaxonomyCode.Should().Be(defaultBillingProfile.TaxonomyCode);
        result.BillingDetail.Address.StreetAddress.Should().Be(defaultBillingProfile.Address.StreetAddress);
        result.BillingDetail.Address.ZipCode.Should().Be(defaultBillingProfile.Address.ZipCode);
        result.BillingDetail.Address.State.Should().Be(defaultBillingProfile.Address.State);
        result.BillingDetail.Address.City.Should().Be(defaultBillingProfile.Address.City);
        result.BillingDetail.Address.Country.Should().Be(defaultBillingProfile.Address.Country);
        result.BillingDetail.PhoneNumberDetails.Should().BeEquivalentTo(defaultBillingProfile.PhoneNumberDetails);

        var claimEvents = Fixture.GetPublishedEventsOfType<USProfessionalClaimCreatedEvent>();
        claimEvents.Should().HaveCountGreaterThan(0);

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );

        var dbInsuranceClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == result.Id);
        dbInsuranceClaim.AmountPaid.Should().Be(payload.AmountPaid);
    }

    [Fact]
    public async Task CreateClaim_WithElectronicPayer_ShouldCreateWithElectronicSubmissionMethod()
    {
        Fixture.ClearEvents();

        var faker = new Faker();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var insurancePayer = await CreateTestInsurancePayer();
        var payer = new ProviderInsurancePayerFaker(provider.Id)
            .RuleFor(x => x.PayerId, insurancePayer.PayerId)
            .Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var icdCodes = await DataContext.ICDCodes
            .AsNoTracking()
            .Take(5)
            .ToListAsync();

        var contactNote = NoteModelBuilder.Any()
            .WithContactId(contact.Id)
            .WithProviderId(provider.Id)
            .WithDiagnoses([.. icdCodes.Select(s => new ICDCodeDataModel { Code = s.Code, Description = s.Description }).ToList()])
            .WithStatus(NoteStatus.Published)
            .WithCreatedByPersonId(staffPerson.Id)
            .Create();
        contactNote.OccurrenceDateTimeUtc = DateTime.UtcNow.AddDays(-4);
        DataContext.ContactNotes.Add(contactNote);

        var billableItem1 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.AddDays(-7).ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate(),
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate()
        };
        
        var policy = CreateContactInsurancePolicy(provider, contact, contact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithServiceLines([.. serviceLines])
            .WithContactInsurancePolicy(provider.Id, contact.Id, policy)
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.SubmissionMethod.Should().Be(ClaimSubmissionMethod.Electronic);

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );
    }

    [Fact]
    public async Task CreateClaim_ShouldFailIfNoContact()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.Add(contact.ToDataModel());
        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .Generate();
        var payload = claim.ToSaveRequest();

        payload.Client.ContactId = Guid.Empty;

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
            )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);

        var result = await response.Content.ReadAsAsync<ValidationError>();
        result.Should().NotBeNull();
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithDiagnosisCodesEvenIfAllServiceFallsUnderSameDay()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var icdCodes = await DataContext.ICDCodes
            .AsNoTracking()
            .Take(5)
            .ToListAsync();

        var contactNote = NoteModelBuilder.Any()
            .WithContactId(contact.Id)
            .WithProviderId(provider.Id)
            .WithDiagnoses([.. icdCodes.Select(s => new ICDCodeDataModel { Code = s.Code, Description = s.Description }).ToList()])
            .WithStatus(NoteStatus.Published)
            .WithCreatedByPersonId(staffPerson.Id)
            .Create();
        contactNote.OccurrenceDateTimeUtc = DateTime.UtcNow;
        DataContext.ContactNotes.Add(contactNote);

        var billableItem1 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.ToDateOnly())
                .Generate(),
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.ToDateOnly())
                .Generate()
        };
        
        var policy = CreateContactInsurancePolicy(provider, contact, contact.ToPolicyHolder(), payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());
        
        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithServiceLines([.. serviceLines])
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
        result.Should().NotBeNull();
        result.Should().BeOfType<InsuranceClaimUSProfessional>();
        result.Number.Should().NotBeNullOrEmpty();
        result.Status.Should().Be(payload.Status);
        result.SubmissionMethod.Should().Be(payload.SubmissionMethod);
        result.Type.Should().Be(ClaimType.USProfessional);
        result.ContactId.Should().Be(payload.Client.ContactId);
        result.OriginalReferenceNumber.Should().Be(payload.OriginalReferenceNumber);
        result.PatientsAccountNumber.Should().Be(payload.PatientsAccountNumber);
        result.PriorAuthorizationNumber.Should().Be(payload.PriorAuthorizationNumber);
        result.ResubmissionCode.Should().Be(payload.ResubmissionCode);

        // Assert diagnostic codes are prepopulated with data
        result.DiagnosticCodes.Should().NotBeNullOrEmpty();
        result.DiagnosticCodes.Should().HaveCount(5);
        result.DiagnosticCodes.ForEach(code =>
        {
            var icdCode = icdCodes.First(c => c.Code == code.Code);
            code.Code.Should().Be(icdCode.Code);
        });

        // Assert service line to contain diagnosis codes
        result.ServiceLines.ForEach(line =>
        {
            var claimLine = payload.ServiceLines.First(s => s.Id == line.Id);
            line.POSCode.Should().Be(claimLine.POSCode);
            line.Modifiers.ForEach(modifier => claimLine.Modifiers.Should().Contain(modifier));
            line.Description.Should().Be(claimLine.Description);
            line.Detail.Should().Be(claimLine.Detail);
            line.ServiceId.Should().Be(claimLine.ServiceId);

            var icdCodeIds = icdCodes.Select(s => s.Code).ToList();
            line.DiagnosticCodeReferences.ForEach(code => icdCodeIds.Should().Contain(code));
        });

        await CheckHistoryRecordExists(
            result.Id,
            HistoryAction.ClaimCreated,
            new ClaimCreatedHistoryActionDetail(
                ClaimHistoryModel.Create(result)
            )
        );

        // TODO CU-86etjwgjz: Self paid amount is getting set to the full amount of the billable items
        // need to introduce scenarios where self pay amount is different from the total amount.
        var billableItemIds = new[] { billableItem1.Id, billableItem2.Id };
        var dbBillingItems = await DataContext.BillableItems.AsNoTracking()
            .Where(x => billableItemIds.Contains(x.Id))
            .ToArrayAsync();
        var expectedAmountPaid = dbBillingItems
            .Select(x => x.SelfPayAmount + x.InsurancePaid)
            .Sum();
        result.AmountPaid.Should().Be(expectedAmountPaid);
    }

    [Fact]
    public async Task CreateClaim_ShouldCreateSuccessfullyWithEmptyGender()
    {
        Fixture.ClearEvents();

        var (_, provider, token) = await SetupProviderAndLogin();

        var contact = new ContactFaker(provider.Id).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(provider.Id, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(provider.Id).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());

        var defaultBillingProfile = new ProviderBillingProfileFaker(provider.Id).Generate();
        defaultBillingProfile.IsDefault = true;
        DataContext.ProviderBillingProfiles.Add(defaultBillingProfile.ToDataModel());

        var icdCodes = await DataContext.ICDCodes
            .AsNoTracking()
            .Take(5)
            .ToListAsync();

        var contactNote = NoteModelBuilder.Any()
            .WithContactId(contact.Id)
            .WithProviderId(provider.Id)
            .WithDiagnoses([.. icdCodes.Select(s => new ICDCodeDataModel { Code = s.Code, Description = s.Description }).ToList()])
            .WithStatus(NoteStatus.Published)
            .WithCreatedByPersonId(staffPerson.Id)
            .Create();
        contactNote.OccurrenceDateTimeUtc = DateTime.UtcNow.AddDays(-4);
        DataContext.ContactNotes.Add(contactNote);

        var billableItem1 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var billableItem2 = CreateBillableTestData(contact, provider.Id, staffPerson);
        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem1.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.AddDays(-7).ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate(),
            new ClaimServiceLineFaker(provider.Id, contact.Id, billableItem2.Id)
                .RuleFor(x => x.Date, DateTime.UtcNow.ToDateOnly())
                .RuleFor(x => x.DiagnosticCodeReferences, [])
                .Generate()
        };
        
        var policyHolder = contact.ToPolicyHolder();
        policyHolder.Sex = new AutoFaker<OptionSetValue>().Generate();
        var policy = CreateContactInsurancePolicy(provider, contact, policyHolder, payer, serviceLines);
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        await DataContext.SaveChangesAsync();

        var claim = new USProfessionalClaimFaker(provider.Id)
            .WithClient(provider.Id, contact)
            .WithFacility(provider.Id)
            .WithIncident(provider.Id)
            .WithReferringProviders(provider.Id)
            .WithServiceLines([.. serviceLines])
            .Generate();
        var payload = claim.ToSaveRequest();

        var request = new HttpRequestMessageBuilder(
                HttpMethod.Post, $"api/providers/{provider.Id}/contacts/{contact.Id}/claims/us-professional"
        )
            .WithPayload(payload)
            .WithBearerAuthorization(token)
            .Create();
        var response = await ClientApi.SendAsync(request);
        response.StatusCode.Should().Be(HttpStatusCode.OK);

        var result = await response.Content.ReadAsAsync<InsuranceClaimUSProfessional>();
  
        // Assert contact insurance policy is populated
        result.ContactInsurancePolicy.Should().NotBeNull();
        result.ContactInsurancePolicy.PolicyHolderSex.Should().BeNull();
    }

    private static ContactInsurancePolicy CreateContactInsurancePolicy(
        Provider provider, 
        Contact contact,
        InsurancePolicyHolderContact policyHolder,
        ProviderInsurancePayer payer, 
        IList<ClaimServiceLine> serviceLines)
    {
        var policy = new ContactInsurancePolicyFaker(provider.Id, contact.Id, payer)
            .WithDateRange(serviceLines)
            .RuleFor(x => x.Status, InsurancePolicyStatus.Verified)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = policyHolder,
                RelationshipType = "Client"
            }).Generate();

        return policy;
    }
}
