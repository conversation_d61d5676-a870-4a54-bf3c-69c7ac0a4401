﻿using carepatron.core.Constants;
using carepatron.core.Extensions;
using carepatron.core.Validation;
using FluentValidation;
using System;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Repositories.Registration;

namespace carepatron.core.Application.Booking.Commands
{
    public class CreateBookingIntentCommandValidator : AbstractValidator<CreateBookingIntentCommand>
    {
        private readonly IReCaptchaRepository reCaptchaRepository;

        public CreateBookingIntentCommandValidator(IReCaptchaRepository reCaptchaRepository)
        {
            this.reCaptchaRepository = reCaptchaRepository;
            RuleFor(x => x.ProviderId).IsRequired();
            RuleFor(x => x.ItemId).IsRequired();
            RuleFor(x => x.StaffId).IsRequired();

            RuleFor(x => x.IsOnline).IsRequired()
                .When(x => x.LocationId.IsNullOrEmpty());
            RuleFor(x => x.LocationId).IsRequired()
                .When(x => x.IsOnline != true);

            RuleFor(x => x.BookingContactDetail)
                .IsRequired()
                .SetValidator(new BookingContactDetailValidator());

            RuleFor(x => x.TimeZone)
                .IsValidTimeZone();

            RuleFor(x => x.DateRange)
                .IsRequired()
                .Must((x, y) => y.FromDate > DateTime.UtcNow)
                    .WithErrorCode(Errors.BookingCannotBeInThePastCode);

            RuleFor(x => x.Token)
                .MustAsync(BeAValidReCaptchaToken)
                .WithErrorCode(Errors.InvalidRecaptchaTokenCode)
                .WithMessage(Errors.InvalidRecaptchaTokenDetail);
        }

        private async Task<bool> BeAValidReCaptchaToken(string token, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(token)) return true;

            return await reCaptchaRepository.Validate(token);
        }
    }
}
