﻿using carepatron.core.Application.Booking.Models;
using carepatron.core.Application.Workspace.Locations.Models;
using carepatron.core.Models.Common;
using carepatron.core.Pipeline.Abstractions;
using System;
using System.Globalization;

namespace carepatron.core.Application.Booking.Commands
{
    public record CreateBookingIntentCommand(
        Guid ProviderId,
        Guid StaffId,
        Guid ItemId,
        Guid? LocationId,
        bool? IsOnline,
        DateRange DateRange,
        BookingContactDetail BookingContactDetail,
        string Message,
        string TimeZone,
        CultureInfo PreferredCulture,
        LocationType LocationType,
        VirtualLocationProductType? VirtualLocationProduct,
        Guid? TaskId,
        BookingRelatedContactDetail RelatedContactDetail,
        string Token) : IMediatrCommand<BookingIntentResponse>;
}
