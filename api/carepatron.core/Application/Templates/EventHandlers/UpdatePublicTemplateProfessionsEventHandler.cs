using carepatron.core.Abstractions;
using carepatron.core.Application.Templates.Models;
using carepatron.core.Constants;
using carepatron.core.Events;
using carepatron.core.Events.Events;
using carepatron.core.Events.Handlers;
using carepatron.core.Extensions;
using carepatron.core.Models.QueueMessages;
using carepatron.core.Repositories.Person;
using carepatron.core.Repositories.SQS;
using carepatron.core.Repositories.Templates;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog.Context;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace carepatron.core.Application.Templates.EventHandlers;

public class UpdatePublicTemplateProfessionsEventHandler(
    IPersonRepository personRepository,
    ISqsRepository sqsRepository,
    ITempTemplateProfessionRepository tempTemplateProfessionRepository,
    ITempTemplateProfessionCsvRepository tempTemplateProfessionCsvRepository,
    ILogger<UpdatePublicTemplateProfessionsEventHandler> logger,
    IUnitOfWork unitOfWork) : IEventHandler<EventData<UpdatePublicTemplateProfessionsEventData>>
{
    public EventType EventType => EventType.UpdatePublicTemplateProfessions;

    public async Task Handle(EventData<UpdatePublicTemplateProfessionsEventData> evt)
    {
        logger.LogInformation("Reading event data for update public template professions");

        var data = evt.Data;

        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.NotesAndDocuments);
        logger.LogInformation("Starting update process for public template professions. From: {From}, To: {To}, BatchSize: {BatchSize}",
            data.From, data.To, data.BatchSize);

        // Check parameters validity
        if (!IsValidEventParameters()) return;

        // If To is greater than 0, we are doing a range migration without batching
        // If To is 0 and BatchSize is greater than 0, we are doing a batch migration
        var limit = (IsRangeMigration())
            ? data.To - data.From
            : data.BatchSize;

        var templatesToUpdate = await tempTemplateProfessionCsvRepository.GetTempTemplateProfessions(data.From, limit);

        if (templatesToUpdate.IsNullOrEmpty())
        {
            logger.LogInformation("Completed processing all records.");
            return;
        }

        foreach (var templateToUpdate in templatesToUpdate)
        {
            try
            {
                LogContext.PushProperty("PublicTemplateId", templateToUpdate.PublicTemplateId);

                var templateBackup = await tempTemplateProfessionRepository.GetTempPublicTemplateProfessions(templateToUpdate.PublicTemplateId);

                if (templateBackup.IsNullOrEmpty())
                {
                    // Log information, if template has no backup means that professions are empty originally
                    logger.LogInformation("No backup found for template: {TemplateId}", templateToUpdate.PublicTemplateId);
                }

                var publicTemplate =
                    await tempTemplateProfessionRepository.GetLivePublicTemplate(templateToUpdate.PublicTemplateId);

                if (publicTemplate == null)
                {
                    logger.LogError("Public template not found: {TemplateId}", templateToUpdate.PublicTemplateId);
                    continue;
                }

                var person = await personRepository.Get(templateToUpdate.PersonId);

                if (person == null)
                {
                    logger.LogError("Author not found: {PersonId}", templateToUpdate.PersonId);
                    continue;
                }

                unitOfWork.UseUnitOfWork();
                
                // update professions
                await tempTemplateProfessionRepository.UpdatePublicTemplateProfessions(publicTemplate.Id, templateToUpdate.Professions.ToArray());

                // update author
                var author = new Author
                {
                    PersonId = person.Id,
                    ProviderId = publicTemplate.Author.ProviderId,
                    Name = $"{person.FirstName} {person.LastName}".Trim(),
                    ProviderName = publicTemplate.Author.ProviderName
                };

                await tempTemplateProfessionRepository.SetPublicTemplateAuthor(publicTemplate.Id, author);

                if (!data.DryRun)
                {
                    await unitOfWork.SaveUnitOfWork();
                }
            }
            catch (Exception e)
            {
                logger.LogError(e, "Error while updating template professions and author. Data: {Data}",
                    JsonConvert.SerializeObject(templateToUpdate));
                unitOfWork.UseUnitOfWork();
            }
        }

        // Queue the next batch when
        // 1. Not in dry run mode
        // 2. In batch migration (from and batch size specified)
        if (!data.DryRun && !IsRangeMigration())
        {
            var nextOffset = data.From + templatesToUpdate.Count;
            logger.LogInformation("Queueing next batch. Next offset: {NextOffset}", nextOffset);

            await sqsRepository.SendMessage(
                QueueType.Task,
                new EventMessage(
                    EventType.UpdatePublicTemplateProfessions,
                    new EventData<UpdatePublicTemplateProfessionsEventData>(data with { From = nextOffset }),
                    Guid.NewGuid().ToString(),
                    null
                )
            );
        }

        logger.LogInformation("Completed update process for public template professions. From: {From}, To: {To}, BatchSize: {BatchSize}",
            data.From, data.To, data.BatchSize);

        bool IsValidEventParameters()
        {
            // To and Batch size cannot be both zero
            if (data.To <= 0 && data.BatchSize <= 0)
            {
                logger.LogError("Both To and BatchSize cannot be zero. Please provide valid values.");
                return false;
            }

            // To and Batch size cannot be both greater than zero
            if (data.To > 0 && data.BatchSize > 0)
            {
                logger.LogError("Both To and BatchSize cannot be greater than zero. Please provide valid values.");
                return false;
            }

            // From must be greater than to when batchsize is less than or equal to zero
            if (data.From >= data.To && data.BatchSize <= 0)
            {
                logger.LogError("Invalid Range value provided: From - {From} To - {To}. It must be greater than or equal to 0.", data.From,
                    data.To);
                return false;
            }

            return true;
        }

        // 2 types of migration
        // Range migration - when To is greater than 0 and BatchSize is less than or equal to 0
        // Batch migration - when To is 0 and BatchSize is greater than 0
        bool IsRangeMigration() => (data.To > 0 && data.BatchSize <= 0);
    }
}