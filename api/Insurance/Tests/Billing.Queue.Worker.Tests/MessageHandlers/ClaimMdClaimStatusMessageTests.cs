using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.History.Models;
using Xunit;
using carepatron.core.Constants;
using Billing.Queue.Worker.Tests.Setup;
using System.Threading.Tasks;
using System;
using System.Threading;
using AutoBogus;
using ClaimMD.Module.Messages;
using tests.common.Data.Fakers.ClaimMD;
using carepatron.core.Extensions;
using ClaimMD.Module.Utilities;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.infra.sql.Models.ClearingHouse;
using System.Text.Json;
using Newtonsoft.Json;
using carepatron.core.Application.Contacts.Models;
using ClaimMD.Module.Models.Http.Dtos;
using Bogus;
using carepatron.core.Application.Insurance.Notifications.Models;
using carepatron.core.Utilities;
using Moq;
using Notifications.Sdk.Client.Abstract;
using tests.common.Mocks;

namespace Billing.Queue.Worker.Tests.MessageHandlers;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ClaimMdClaimStatusMessageTests(BillingWorkerQueueTestFixture fixture)
 : WorkerComponentTestBase<BillingWorkerQueueTestFixture>(fixture)
{
    [Fact]
    public async Task ClaimAcknowledged_ShouldCreateMetadataAndHistory()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, _) = SetupAcknowledgedClaimStatusMessage(contact, claim);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbMetadata = await DataContext.InsuranceClaimClearingHouseMetadata
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.InsuranceClaimId == claim.Id && x.ClearingHouse == ClearingHouseType.ManagedClaimMd);
        dbMetadata.Should().NotBeNull();
        dbMetadata.Should().BeEquivalentTo(new
        {
            InsuranceClaimId = Base36GuidEncoder.Decode(statusClaimMessage.ClaimUpdate.RemoteClaimId),
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            payer.PayerId,
            statusClaimMessage.ClaimUpdate.ClaimMdId,
            statusClaimMessage.ClaimUpdate.SenderICN
        }, opt => opt.ExcludingMissingMembers());

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimReceived);
        dbHistory.Should().NotBeNull();
        dbHistory.CreatedDateTimeUtc.ToTimeZoneDateTime(ClaimMdDateHelper.ClaimMdTimeZoneId).ToString(ClaimMdDateHelper.Formats.DateTime12hAmPm).Should().Be(statusClaimMessage.ClaimUpdate.ResponseTime);

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Processed);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claim.Status);
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());
        
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimAcknowledged_ShouldUpdateMetadata()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, _) = SetupAcknowledgedClaimStatusMessage(contact, claim);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbMetadata = await DataContext.InsuranceClaimClearingHouseMetadata
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == metadata.Id);
        dbMetadata.Should().NotBeNull();
        dbMetadata.Should().BeEquivalentTo(new
        {
            metadata.Id,
            InsuranceClaimId = claim.Id,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            statusClaimMessage.ClaimUpdate.PayerId,
            statusClaimMessage.ClaimUpdate.ClaimMdId,
            statusClaimMessage.ClaimUpdate.SenderICN
        }, opt => opt.ExcludingMissingMembers());

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Processed);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claim.Status);
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());
        
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimAcknowledged_ShouldUpdateMetadataIfContactIsSoftDeleted()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, _) = SetupAcknowledgedClaimStatusMessage(contact, claim);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);
        await DataContext.SaveChangesAsync();

        // Simulate a deleted contact
        var dbDeletedContact = await DataContext.Contacts
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == contact.Id);
        dbDeletedContact.Should().NotBeNull();
        dbDeletedContact.DeletedAtUtc = DateTime.UtcNow;
        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbMetadata = await DataContext.InsuranceClaimClearingHouseMetadata
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == metadata.Id);
        dbMetadata.Should().NotBeNull();
        dbMetadata.Should().BeEquivalentTo(new
        {
            metadata.Id,
            InsuranceClaimId = claim.Id,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            statusClaimMessage.ClaimUpdate.PayerId,
            statusClaimMessage.ClaimUpdate.ClaimMdId,
            statusClaimMessage.ClaimUpdate.SenderICN
        }, opt => opt.ExcludingMissingMembers());

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Processed);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claim.Status);
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());
        
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimAcknowledged_ShouldNotUpdateMetadataIfClaimNotFound()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, claimErrors) = SetupAcknowledgedClaimStatusMessage(contact, claim);
        statusClaimMessage.ClaimUpdate.RemoteClaimId = Base36GuidEncoder.Encode(Guid.NewGuid());
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbMetadata = await DataContext.InsuranceClaimClearingHouseMetadata
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == metadata.Id);
        dbMetadata.Should().NotBeNull();
        dbMetadata.Should().BeEquivalentTo(new
        {
            metadata.Id,
            InsuranceClaimId = claim.Id,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            metadata.PayerId,
            metadata.ClearingHouseClaimId,
            metadata.PayerClaimId
        }, opt => opt.ExcludingMissingMembers());

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Pending);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claim.Status);
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());
        
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimAcknowledged_ShouldNotSaveMetadataInHistoryIfMetadataDidNotChange()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, claimErrors) = SetupAcknowledgedClaimStatusMessage(contact, claim);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);
        statusClaimMessage.ClaimUpdate.SenderICN = metadata.PayerClaimId;
        statusClaimMessage.ClaimUpdate.ClaimMdId = metadata.ClearingHouseClaimId;

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbMetadata = await DataContext.InsuranceClaimClearingHouseMetadata
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == metadata.Id);
        dbMetadata.Should().NotBeNull();
        dbMetadata.Should().BeEquivalentTo(new
        {
            metadata.Id,
            InsuranceClaimId = claim.Id,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            metadata.PayerId,
            metadata.ClearingHouseClaimId,
            metadata.PayerClaimId
        }, opt => opt.ExcludingMissingMembers());

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimReceived);
        dbHistory.Should().NotBeNull();
        var historyDetails = JsonConvert.DeserializeObject<ClaimReceivedHistoryActionDetail>(dbHistory.Details.RootElement.GetRawText());
        historyDetails.Should().NotBeNull();
        historyDetails.References.ClearingHouseClaimId.Should().BeNullOrEmpty();
        historyDetails.References.PayerClaimId.Should().BeNullOrEmpty();
    }

    [Fact]
    public async Task ClaimRejected_ShouldUpdateStatusAndHistoryAndErrors()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim();
        var (statusClaimMessage, claimErrors) = SetupRejectedClaimStatusMessage(claim, payer);
        SetupResponseLog(statusClaimMessage.ClaimUpdate);
        SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaimErrors = await DataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == claim.Id && x.Status == ClaimErrorStatus.Active)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(claimErrors.Length);
        dbClaimErrors.Should().BeEquivalentTo(claimErrors.Select(s =>
            new
            {
                s.Field,
                s.Message,
                s.Status
            }
        ));

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimRejected);
        dbHistory.Should().NotBeNull();
        var details = JsonConvert.DeserializeObject<ClaimRejectedHistoryActionDetail>(dbHistory.Details.RootElement.GetRawText());
        details.Should().NotBeNull();
        details.Claim.Status.Should().Be(ClaimStatus.Rejected);
        dbHistory.CreatedDateTimeUtc.ToTimeZoneDateTime(ClaimMdDateHelper.ClaimMdTimeZoneId).ToString(ClaimMdDateHelper.Formats.DateTime12hAmPm).Should().Be(statusClaimMessage.ClaimUpdate.ResponseTime);

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(ClaimStatus.Rejected);
        dbClaim.StatusReason.Should().BeNullOrEmpty();

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Processed);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());
        
        AssertClaimRejectedNotificationSent(claim.Id);
    }

    [Fact]
    public async Task ClaimRejected_ShouldUpdateHistoryAndStatusIfContactIsDeleted()
    {
        // Assemble
        var (contact, payer, claim) = SetupClaim();
        var (statusClaimMessage, claimErrors) = SetupRejectedClaimStatusMessage(claim, payer);
        SetupResponseLog(statusClaimMessage.ClaimUpdate);
        SetupMetadata(claim, payer);
        await DataContext.SaveChangesAsync();

        // Simulate a deleted contact
        var dbDeletedContact = await DataContext.Contacts
            .IgnoreQueryFilters()
            .FirstOrDefaultAsync(x => x.Id == contact.Id);
        dbDeletedContact.Should().NotBeNull();
        dbDeletedContact.DeletedAtUtc = DateTime.UtcNow;
        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaimErrors = await DataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == claim.Id && x.Status == ClaimErrorStatus.Active)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(claimErrors.Length);
        dbClaimErrors.Should().BeEquivalentTo(claimErrors.Select(s =>
            new
            {
                s.Field,
                s.Message,
                s.Status
            }
        ));

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id
                && x.EntityType == EntityType.USProfessionalInsuranceClaim
                && x.Action == HistoryAction.ClaimRejected
            );
        dbHistory.Should().NotBeNull();
        var details = JsonConvert.DeserializeObject<ClaimRejectedHistoryActionDetail>(dbHistory.Details.RootElement.GetRawText());
        details.Should().NotBeNull();
        details.Claim.Status.Should().Be(ClaimStatus.Rejected);

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(ClaimStatus.Rejected);
        dbClaim.StatusReason.Should().BeNullOrEmpty();

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Processed);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());

        AssertClaimRejectedNotificationSent(claim.Id);
    }

    [Fact]
    public async Task ClaimRejected_ShouldNotUpdateMetadataIfClaimNotFound()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim();
        var (statusClaimMessage, _) = SetupRejectedClaimStatusMessage(claim, payer);
        statusClaimMessage.ClaimUpdate.RemoteClaimId = Base36GuidEncoder.Encode(Guid.NewGuid());
        SetupResponseLog(statusClaimMessage.ClaimUpdate);
        SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaimErrors = await DataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == claim.Id && x.Status == ClaimErrorStatus.Active)
            .ToListAsync();
        dbClaimErrors.Count.Should().Be(0);

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimRejected);
        dbHistory.Should().BeNull();

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claim.Status);

        var externalId = statusClaimMessage.ClaimUpdate.GetExternalId();
        var dbResponseLogs = await DataContext.ClearingHouseLogs
            .AsNoTracking()
            .Where(x => x.ClearingHouse == ClearingHouseType.ManagedClaimMd && x.ExternalId == externalId)
            .ToArrayAsync();
        dbResponseLogs.Should().NotBeEmpty().And.AllSatisfy(x =>
        {
            x.Status.Should().Be(ClearingHouseLogStatus.Pending);
            x.ClearingHouse.Should().Be(ClearingHouseType.ManagedClaimMd);
            x.Type.Should().Be(ClearingHouseLogType.Claim);
            x.RawResponse.Should().NotBeNull();
        });
        Fixture.VerifyMessageDeleted(messageResponse.Messages.First());

        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimRejected_ShouldUpdateResubmissionCodeAndReferenceNumber()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim();
        var (statusClaimMessage, _) = SetupRejectedClaimStatusMessage(claim, payer);
        SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaim = await DataContext.InsuranceClaimsUSProfessional
            .AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ResubmissionCode.Should().Be(ClaimResubmissionCodes.ReplacementOfPriorClaim);
        dbClaim.OriginalReferenceNumber.Should().Be(metadata.PayerClaimId);
    }
    
    [Fact]
    public async Task ClaimRejected_WithMissingPayer_ShouldSendNotificationWithPayerNumberOnly()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim();
        
        // Set random payer number so we can't match
        var statusClaim = new StatusUpdateClaimDtoFaker(claim.ClientControlNumber, claim.Id)
            .AsRejected()
            .Generate();
 
        var claimStatusMessage = new AutoFaker<ClaimMdClaimStatusMessage>()
            .RuleFor(x => x.ClaimUpdate, statusClaim)
            .Generate();
        
        SetupResponseLog(claimStatusMessage.ClaimUpdate);
        SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        Fixture.QueueMessage(claimStatusMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimRejected);
        
        dbHistory.Should().NotBeNull();
        
        var details = JsonConvert.DeserializeObject<ClaimRejectedHistoryActionDetail>(dbHistory.Details.RootElement.GetRawText());
        
        details.Should().NotBeNull();
        details.Claim.Status.Should().Be(ClaimStatus.Rejected);
        dbHistory.CreatedDateTimeUtc.ToTimeZoneDateTime(ClaimMdDateHelper.ClaimMdTimeZoneId).ToString(ClaimMdDateHelper.Formats.DateTime12hAmPm).Should().Be(claimStatusMessage.ClaimUpdate.ResponseTime);

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(ClaimStatus.Rejected);
        dbClaim.StatusReason.Should().BeNullOrEmpty();

        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<InsuranceClaimRejectedNotificationContent>(content => 
                content.ClaimId == claim.Id &&
                content.PayerNumber == statusClaim.PayerId &&
                content.PayerName == null &&
                content.PayerId == null),
            It.IsAny<Guid[]>()), Times.Once);
    }
    
    [Fact]
    public async Task ClaimRejected_WithAlreadyRejectedStatus_ShouldNotSendNotification()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim(ClaimStatus.Rejected);
        var (statusClaimMessage, _) = SetupRejectedClaimStatusMessage(claim, payer);
        
        SetupResponseLog(statusClaimMessage.ClaimUpdate);
        SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimDenied_ShouldSkipHistoryIfClaimAlreadyDenied()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim(ClaimStatus.Denied);
        var (statusClaimMessage, claimErrors) = SetupRejectedClaimStatusMessage(claim, payer);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaimErrors = await DataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == claim.Id && x.Status == ClaimErrorStatus.Active)
            .ToListAsync();
        dbClaimErrors.Should().NotBeNull();
        dbClaimErrors.Count.Should().Be(claimErrors.Length);
        dbClaimErrors.Should().BeEquivalentTo(claimErrors.Select(s =>
            new
            {
                s.Field,
                s.Message,
                s.Status
            }
        ));

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimDenied);
        dbHistory.Should().BeNull();

        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(ClaimStatus.Denied);
        
        AssertNoNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ClaimRejected_ShouldNotProcessRejectionsBeforeSubmittedTime()
    {
        // Assemble
        var (_, payer, claim) = SetupClaim(ClaimStatus.Submitted, DateTime.UtcNow);
        var (statusClaimMessage, _) = SetupRejectedClaimStatusMessage(claim, payer);
        statusClaimMessage.ClaimUpdate.ResponseTime = DateTime.UtcNow.AddDays(-1).ToString(ClaimMdDateHelper.Formats.DateTime12hAmPm);
        var responseLog = SetupResponseLog(statusClaimMessage.ClaimUpdate);
        var metadata = SetupMetadata(claim, payer);

        await DataContext.SaveChangesAsync();

        // Act
        var messageResponse = Fixture.QueueMessage(statusClaimMessage);
        await Fixture.Run(CancellationToken.None);

        // Assert
        var dbClaimErrors = await DataContext.InsuranceClaimErrors
            .AsNoTracking()
            .Where(x => x.InsuranceClaimId == claim.Id && x.ProviderId == ProviderId)
            .ToListAsync();
        dbClaimErrors.Should().BeEmpty();

        var dbClaim = await DataContext.InsuranceClaimsUSProfessional
            .AsNoTracking()
            .Include(x => x.ClaimHeader)
            .FirstOrDefaultAsync(x => x.Id == claim.Id);
        dbClaim.Should().NotBeNull();
        dbClaim.ClaimHeader.Status.Should().Be(claim.Status);

        var dbHistory = await DataContext.EntityHistory
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.EntityId == claim.Id && x.EntityType == EntityType.USProfessionalInsuranceClaim && x.Action == HistoryAction.ClaimRejected);
        dbHistory.Should().NotBeNull();
        var details = JsonConvert.DeserializeObject<ClaimRejectedHistoryActionDetail>(dbHistory.Details.RootElement.GetRawText());
        details.Should().NotBeNull();
        details.Claim.Status.Should().Be(ClaimStatus.Rejected);
        
        AssertNoNotificationsSent(claim.Id);
    }

    private (
        Contact Contact,
        ProviderInsurancePayer Payer,
        InsuranceClaimUSProfessional Claim
    ) SetupClaim(ClaimStatus claimStatus = ClaimStatus.Submitted, DateTime? lastSubmittedDateTimeUtc = null)
    {
        var contact = new ContactFaker(ProviderId).Generate();
        DataContext.Contacts.AddRange([contact.ToDataModel()]);

        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(ProviderId, staffPerson).Generate();
        DataContext.Persons.Add(staffPerson.ToDataModel());
        DataContext.ProviderStaff.AddRange(staffMember.ToDataModel());

        var payer = new ProviderInsurancePayerFaker(ProviderId).Generate();
        DataContext.ProviderInsurancePayers.Add(payer.ToDataModel());
        
        var insurancePayer = new InsurancePayerFaker()
            .RuleFor(x => x.Name, payer.Name)
            .RuleFor(x => x.PayerId, payer.PayerId)
            .Generate();
        DataContext.InsurancePayers.Add(insurancePayer.ToDataModel());

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Payer, payer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = contact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();
        DataContext.ContactInsurancePolicies.AddRange(policy.ToDataModel());

        var claim = new USProfessionalClaimFaker(ProviderId)
            .WithClient(ProviderId, contact)
            .WithContactInsurancePolicy(ProviderId, contact.Id, policy)
            .WithRenderingProviders(ProviderId, staffMember)
            .RuleFor(x => x.Status, claimStatus)
            .RuleFor(x => x.LastSubmittedDateTimeUtc, lastSubmittedDateTimeUtc)
            .Generate();

        DataContext.InsuranceClaimsUSProfessional.Add(claim.ToDataModel());
        DataContext.InsuranceClaims.Add(claim.ToClaimHeaderDataModel());
        DataContext.ClearingHouseIdLookups.Add(claim.ToClearingHouseIdLookupDataModel());

        return (Contact: contact, Payer: payer, Claim: claim);
    }

    private (ClaimMdClaimStatusMessage Message, InsuranceClaimError[] Errors) SetupRejectedClaimStatusMessage(InsuranceClaimUSProfessional claim, ProviderInsurancePayer payer)
    {
        var statusClaim = new StatusUpdateClaimDtoFaker(claim.ClientControlNumber, claim.Id)
            .WithPayer(payer)
            .AsRejected()
            .Generate();
        
        var claimErrors = ClaimMdMapper.MapClaimErrors(statusClaim, ProviderId);
        var claimStatusMessage = new AutoFaker<ClaimMdClaimStatusMessage>()
            .RuleFor(x => x.ClaimUpdate, statusClaim)
            .Generate();
        
        return (Message: claimStatusMessage, Errors: claimErrors);
    }

    private (ClaimMdClaimStatusMessage Message, InsuranceClaimError[] Errors) SetupAcknowledgedClaimStatusMessage(Contact contact, InsuranceClaimUSProfessional claim)
    {
        var statusClaim = new StatusUpdateClaimDtoFaker(claim.ClientControlNumber, claim.Id)
            .RuleFor(x => x.PayerId, claim.ContactInsurancePolicy.PayerNumber)
            .Generate();
        var claimStatusMessage = new AutoFaker<ClaimMdClaimStatusMessage>()
            .RuleFor(x => x.ClaimUpdate, statusClaim)
            .Generate();
        return (Message: claimStatusMessage, Errors: []);
    }

    private ClearingHouseLog SetupResponseLog(StatusUpdateClaimDto statusClaim)
    {
        var responseLog = new ClearingHouseLogFaker()
            .RuleFor(x => x.Type, ClearingHouseLogType.Claim)
            .RuleFor(x => x.ClearingHouse, ClearingHouseType.ManagedClaimMd)
            .RuleFor(x => x.ExternalId, statusClaim.GetExternalId())
            .Generate();
        DataContext.ClearingHouseLogs.Add(new ClearingHouseLogDataModel
        {
            Type = ClearingHouseLogType.Claim,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            ExternalId = statusClaim.GetExternalId(),
            Status = ClearingHouseLogStatus.Pending,
            CreatedDateTimeUtc = DateTime.UtcNow,
            UpdatedDateTimeUtc = DateTime.UtcNow,
            RawResponse = JsonDocument.Parse(JsonConvert.SerializeObject(statusClaim))
        });
        return responseLog;
    }

    private InsuranceClaimClearingHouseMetadata SetupMetadata(InsuranceClaimUSProfessional claim, ProviderInsurancePayer payer)
    {
        var metadata = new InsuranceClaimClearingHouseMetadataFaker(ProviderId, claim)
            .RuleFor(x => x.PayerId, payer.PayerId)
            .Generate();
        DataContext.InsuranceClaimClearingHouseMetadata.Add(metadata.ToDataModel());
        return metadata;
    }
    
    
    private void AssertClaimRejectedNotificationSent(Guid claimId)
    {
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<InsuranceClaimRejectedNotificationContent>(content => content.ClaimId == claimId),
            It.IsAny<Guid[]>()), Times.Once);
    }
        
    private void AssertNoNotificationsSent(Guid claimId)
    {
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<ClaimResponseNotificationContent>(content => content.ClaimId == claimId),
            It.IsAny<Guid[]>()), Times.Never);
    }
}
