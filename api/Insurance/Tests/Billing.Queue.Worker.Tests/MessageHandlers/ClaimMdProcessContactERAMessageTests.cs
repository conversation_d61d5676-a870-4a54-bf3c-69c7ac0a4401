using FluentAssertions;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using tests.common.Data.Fakers;
using tests.common.Data.Mappers;
using tests.common.Fixtures;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Application.History.Models;
using Xunit;
using carepatron.core.Constants;
using Billing.Queue.Worker.Tests.Setup;
using System.Threading.Tasks;
using System;
using System.Threading;
using ClaimMD.Module.Messages;
using tests.common.Data.Fakers.ClaimMD;
using carepatron.core.Extensions;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.infra.sql.Models.ClearingHouse;
using System.Text.Json;
using Newtonsoft.Json;
using carepatron.core.Application.Contacts.Models;
using System.Collections.Generic;
using Amazon.SQS.Model;
using carepatron.core.Application.Billables.Models;
using carepatron.infra.sql.Models.BillableItem;
using tests.common.Mocks;
using Moq;
using carepatron.core.Application.Payments.Models;
using carepatron.core.Common;
using carepatron.core.Application.Billing.Models;
using carepatron.core.Application.Files.Models;
using carepatron.core.Application.Insurance.Notifications.Models;
using carepatron.core.Models.Media;
using tests.common.Data.Fakers.Insurance;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Models.Http.Requests;
using ClaimMD.Module.Models.Http.Responses;
using ClaimMD.Module.Utilities;
using Notifications.Sdk.Client.Abstract;
using EntityType = carepatron.core.Application.History.Models.EntityType;
using static ClaimMD.Module.Constants;
using carepatron.core.Utilities;

namespace Billing.Queue.Worker.Tests.MessageHandlers;

[Trait(nameof(CodeOwner), CodeOwner.BillingAndPayments)]
public class ClaimMdProcessContactERAMessageTests(BillingWorkerQueueTestFixture fixture)
    : WorkerComponentTestBase<BillingWorkerQueueTestFixture>(fixture)
{
    [Fact]
    public async Task ProcessContactERAMessage_ShouldCreatePaymentAndRemittances()
    {
        // Assemble
        var (contact, payer, claim, billingProfile, metadata) = CreateClaimTestData();

        // Ensure no adjustments are included so we process a fully paid claim
        var (eraDetailResponse, eraClaim) = CreateEraResponse(contact, payer, claim, metadata, []);
        var remittanceAdvice = CreateEraData(eraDetailResponse, payer, billingProfile);

        await DataContext.SaveChangesAsync();

        SetupFileRepositoryMock();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertPaymentAndAllocationsExist(eraDetailResponse, contact.Id, claim.ServiceLines.Length, payer);
        await AssertHistoryItemExists(claim.Id);
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Paid, ERAClaimStatusCode.Descriptions[eraClaim.StatusCode]);
        await AssertClaimRemittanceExists(eraClaim, ProviderId, claimId: claim.Id);

        AssertClaimPaidNotificationSent(claim.Id, eraDetailResponse.CheckNumber, payer.Id);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfContactIdEmpty()
    {
        // Assemble
        var (eraDetailResponse, claim, _, remittanceAdvice) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: Guid.Empty,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfProviderIdEmpty()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        var messageResponse = await SendMessage(
            providerId: Guid.Empty,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfERAEmpty()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        var messageResponse = Fixture.QueueMessage(new ClaimMDProcessContactERAMessage
        {
            ProviderId = ProviderId,
            ContactId = contact.Id,
            InsuranceRemittanceAdviceId = remittanceAdvice.Id,
            ERADetails = null
        });

        await Fixture.Run(new CancellationTokenSource(TimeSpan.FromSeconds(60)).Token);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfNoClaimsInERA()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();

        eraDetailResponse.Claims = [];

        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfRemittanceNotFound()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, _) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: Guid.NewGuid(),
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldCreatePaymentAndRemittancesEvenIfPayerDoesNotExist()
    {
        // Assemble
        var (contact, payer, claim, billingProfile, metadata) = CreateClaimTestData();
        var (eraDetailResponse, eraClaim) = CreateEraResponse(contact, payer, claim, metadata);
        var remittanceAdvice = CreateEraData(eraDetailResponse, payer, billingProfile);

        // Set a random payer ID so we cannot find the payer in the database
        eraDetailResponse.PayerId = Guid.NewGuid().ToString();
        eraDetailResponse.PayerName = "The Payer";
        eraDetailResponse.PayerId = "Payer XYZ";

        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertPaymentAndAllocationsExist(eraDetailResponse, contact.Id, claim.ServiceLines.Length);
        await AssertHistoryItemExists(claim.Id);
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Paid, ERAClaimStatusCode.Descriptions[eraClaim.StatusCode]);
        await AssertClaimRemittanceExists(eraClaim, ProviderId, claimId: claim.Id);

        // Ensure the payment notification pulls the payer details from the ERA response as a fallback
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<ClaimPaymentNotificationContent>(content =>
                content.ClaimId == claim.Id &&
                content.PayerName == eraDetailResponse.PayerName &&
                content.PayerNumber == eraDetailResponse.PayerId &&
                content.PayerId == null),
            It.IsAny<Guid[]>()), Times.Once);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfFailedToGetPdf()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();
        SetupFileRepositoryMock();

        ResolveService<ClaimMdHttpClientMock>()
            .Setup(s => s.GetERADetailsFile(It.Is<ERADetailsPDFRequest>(request =>
                request.EraId == eraDetailResponse.EraId.ToString() &&
                request.PCN == claim.ClientControlNumber)))
            .ReturnsAsync(default(ERADetailsPDFResponse));

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfFailedToUploadToS3()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        await DataContext.SaveChangesAsync();

        ResolveService<FileStorageRepositoryMock>()
            .Setup(s => s.Upload(It.IsAny<FileLocationType>(), It.IsAny<UploadableFile[]>()))
            .ReturnsAsync([]);

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldCreatePaymentAndERAIfInternalClaimNotFound()
    {
        // Assemble
        var (contact, payer, claim, billingProfile, _) = CreateClaimTestData();

        // ERA details response
        var adjustments = new ChargeAdjustmentDtoFaker().Generate(2).ToArray();

        // Create a charge with mismatched service lines / charge id so we can't match it with the existing claim
        var charges = claim.ServiceLines.Select(s => new ERAClaimChargeDtoFaker(Guid.NewGuid())
            .WithAdjustments(adjustments)
            .Recalculate()
            .Generate());

        var eraClaim = new ERAClaimDtoFaker()
            .WithCharges([.. charges])
            .RuleFor(x => x.PCN, claim.ClientControlNumber)
            .RuleFor(x => x.PayerICN, f => f.Random.AlphaNumeric(10))
            .Generate();

        var eraDetailResponse = new ERADetailsResponseFaker([eraClaim])
            .RuleFor(c => c.PayerName, payer.Name)
            .RuleFor(c => c.PayerId, payer.PayerId)
            .Generate();

        var remittanceAdvice = CreateEraData(eraDetailResponse, payer, billingProfile);

        await DataContext.SaveChangesAsync();

        SetupFileRepositoryMock();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertPaymentAndAllocationsExist(eraDetailResponse, contact.Id, 0, payer);
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Draft);
        await AssertHistoryItemDoesNotExist(claim.Id);
        await AssertClaimRemittanceExists(eraClaim, ProviderId, remittanceAdviceId: remittanceAdvice.Id, claimId: null);

        AssertNoClaimPaymentNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldNotCreatePaymentAndRemittancesIfRemittancePaymentAlreadyExists()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();

        var payment = new PaymentFaker(ProviderId)
            .RuleFor(x => x.ContactId, contact.Id)
            .RuleFor(x => x.InvoiceId, default(Guid?))
            .Generate();

        var insuranceRemittancePayment = new InsuranceRemittancePayment
        {
            Id = Guid.NewGuid(),
            ProviderId = ProviderId,
            Payment = payment,
            InsuranceRemittanceAdvice = remittanceAdvice
        };

        DataContext.AddRange(payment.ToDataModel(), insuranceRemittancePayment.ToDataModel());

        await DataContext.SaveChangesAsync();

        SetupFileRepositoryMock();

        // Act
        var messageResponse = await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert
        await AssertNoChanges(eraDetailResponse, claim.Id);

        // Clean up message that failed to process
        Fixture.DeleteMessage(messageResponse);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldCreateERAReceivedHistory()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        eraDetailResponse.Claims[0].TotalPaid = "0.00";
        await DataContext.SaveChangesAsync();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        await AssertEraReceivedHistoryExists(claim.Id);
        await AssertClaimRemittanceExists(eraDetailResponse.Claims[0], ProviderId, remittanceAdviceId: remittanceAdvice.Id, claimId: claim.Id);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldSetClaimToDeniedIfERAStatusIsDenied()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        eraDetailResponse.Claims.First().StatusCode = ERAClaimStatusCode.Denied;
        eraDetailResponse.Claims.First().TotalPaid = "0.00";

        await DataContext.SaveChangesAsync();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert changes
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Denied, ERAClaimStatusCode.Descriptions[eraDetailResponse.Claims[0].StatusCode]);
        await AssertPaymentAllocationsDoesNotExist(eraDetailResponse, claim);
        await AssertPaymentDoesNotExist(eraDetailResponse);
        
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<InsuranceClaimDeniedNotificationContent>(content => content.ClaimId == claim.Id),
            It.IsAny<Guid[]>()), Times.Once);
    }
    
    [Fact]
    public async Task ProcessContactERAMessage_ShouldSetClaimToRejectedIfERAStatusIsRejected()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        eraDetailResponse.Claims.First().StatusCode = ERAClaimStatusCode.RejectedPleaseResubmit;
        eraDetailResponse.Claims.First().TotalPaid = "0.00";

        await DataContext.SaveChangesAsync();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert changes
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Rejected, ERAClaimStatusCode.Descriptions[eraDetailResponse.Claims[0].StatusCode]);
        await AssertPaymentAllocationsDoesNotExist(eraDetailResponse, claim);
        await AssertPaymentDoesNotExist(eraDetailResponse);

        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<InsuranceClaimRejectedNotificationContent>(content => content.ClaimId == claim.Id),
            It.IsAny<Guid[]>()), Times.Once);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldSetClaimToAcceptedIfERAStatusIsAnyOfAcceptedStates()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        eraDetailResponse.Claims.First().StatusCode = ERAClaimStatusCode.Pended;

        await DataContext.SaveChangesAsync();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert claim changes
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Accepted, ERAClaimStatusCode.Descriptions[eraDetailResponse.Claims[0].StatusCode]);
        
        AssertNoClaimStatusNotificationsSent(claim.Id);
    }

    [Fact]
    public async Task ProcessContactERAMessage_ShouldSetClaimToAcceptedIfNoPaymentOnERAClaimAndAnyOfPaidStates()
    {
        // Assemble
        var (eraDetailResponse, claim, contact, remittanceAdvice) = CreateDefaultTestData();
        eraDetailResponse.Claims.First().TotalPaid = "0.00"; // No payment on the claim

        await DataContext.SaveChangesAsync();

        // Act
        await SendMessage(
            providerId: ProviderId,
            contactId: contact.Id,
            remittanceAdviceId: remittanceAdvice.Id,
            eraDetailResponse);

        // Assert claim changes
        await AssertClaimStatusAndReason(claim.Id, ClaimStatus.Accepted, ERAClaimStatusCode.Descriptions[eraDetailResponse.Claims[0].StatusCode]);

        AssertNoClaimStatusNotificationsSent(claim.Id);
    }

    private async Task AssertNoChanges(ERADetailsResponse eraDetailsResponse, Guid claimId)
    {
        await AssertPaymentDoesNotExist(eraDetailsResponse);
        await AssertClaimStatusAndReason(claimId, ClaimStatus.Draft);
        await AssertHistoryItemDoesNotExist(claimId);
        await AssertClaimRemittanceDoesNotExist(claimId);

        AssertNoClaimPaymentNotificationsSent(claimId);
    }

    private async Task AssertPaymentAndAllocationsExist(
        ERADetailsResponse eraDetailsResponse,
        Guid contactId,
        int expectedAllocationCount,
        ProviderInsurancePayer payer = null
        )
    {
        // payment date should be converted to UTC.
        var paymentDate = ClaimMdDateHelper.MountainDateOnlyToUtc(eraDetailsResponse.PaidDate.ToDateOnly(ClaimMdDateHelper.Formats.CompactISODate));
        var paidAmount = Convert.ToDecimal(eraDetailsResponse.Claims.Sum(s => Convert.ToDecimal(s.TotalPaid)));
        var stripeAmount = CurrencyHandler.Get(CurrencyCodes.USD).ToStripeAmount(paidAmount);

        var dbPayment = await DataContext.Payments
            .Include(x => x.Allocations)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Reference == eraDetailsResponse.CheckNumber);

        dbPayment.Should().NotBeNull()
        .And.BeEquivalentTo(new
        {
            ProviderId = ProviderId,
            ContactId = contactId,
            Reference = eraDetailsResponse.CheckNumber,
            PaymentDate = paymentDate,
            PayerType = PayerType.Insurance,
            Type = PaymentTypes.Insurance,
            PaymentProvider = PaymentProviders.ClaimMD,
            PayerName = eraDetailsResponse.PayerName,
            InsurancePayerId = payer?.Id,
            Amount = stripeAmount,
            ChargeAmount = paidAmount,
            TransferAmount = paidAmount,
            Fee = 0m,
            IsClientChargedFee = false,
            CurrencyCode = CurrencyCodes.USD
        }, opt => opt.ExcludingMissingMembers());

        dbPayment.Allocations.Should().HaveCount(expectedAllocationCount);
    }

    private async Task AssertPaymentDoesNotExist(ERADetailsResponse eraDetailsResponse)
    {
        var dbPayment = await DataContext.Payments
            .Include(x => x.Allocations)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Reference == eraDetailsResponse.CheckNumber);

        dbPayment.Should().BeNull();
    }

    private async Task AssertHistoryItemExists(Guid claimId)
    {
        var dbHistory = await DataContext.EntityHistory.AsNoTracking().FirstOrDefaultAsync(x =>
            x.EntityId == claimId &&
            x.EntityType == EntityType.USProfessionalInsuranceClaim &&
            x.Action == HistoryAction.ClaimElectronicPayment);

        dbHistory.Should().NotBeNull();
    }

    private async Task AssertHistoryItemDoesNotExist(Guid claimId)
    {
        var dbHistory = await DataContext.EntityHistory.AsNoTracking().FirstOrDefaultAsync(x =>
            x.EntityId == claimId &&
            x.EntityType == EntityType.USProfessionalInsuranceClaim &&
            x.Action == HistoryAction.ClaimElectronicPayment);

        dbHistory.Should().BeNull();
    }

    private async Task AssertEraReceivedHistoryExists(Guid claimId)
    {
        var dbHistory = await DataContext.EntityHistory.AsNoTracking().FirstOrDefaultAsync(x =>
            x.EntityId == claimId &&
            x.EntityType == EntityType.USProfessionalInsuranceClaim &&
            x.Action == HistoryAction.ClaimERAReceived
        );
        dbHistory.Should().NotBeNull();
    }

    private async Task AssertClaimStatusAndReason(Guid claimId, ClaimStatus claimStatus, string statusReason = null)
    {
        var dbClaim = await DataContext.InsuranceClaims
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.Id == claimId);

        dbClaim.Should().NotBeNull();
        dbClaim.Status.Should().Be(claimStatus);
        dbClaim.StatusReason.Should().Be(statusReason);
    }

    private async Task AssertPaymentAllocationsDoesNotExist(
        ERADetailsResponse eraDetailsResponse,
        InsuranceClaimUSProfessional claim
        )
    {
        var serviceLineIds = claim.ServiceLines.Select(s => s.Id).ToArray();
        var dbAllocations = await DataContext.PaymentAllocations
            .AsNoTracking()
            .Where(x => x.ClaimLineId.HasValue && serviceLineIds.Contains(x.ClaimLineId.Value))
            .ToListAsync();

        dbAllocations.Should().BeEmpty();
    }

    private async Task AssertClaimRemittanceExists(ERAClaimDto eraClaim, Guid providerId, Guid? remittanceAdviceId = null, Guid? claimId = null)
    {
        var query = DataContext.InsuranceClaimRemittances
            .Include(x => x.Items)
            .ThenInclude(x => x.Adjustments)
            .AsNoTracking();

        var dbClaimRemittance = await (remittanceAdviceId.HasValue
            ? query.FirstOrDefaultAsync(x => x.ProviderId == providerId && x.InsuranceRemittanceAdviceId == remittanceAdviceId)
            : query.FirstOrDefaultAsync(x => x.ProviderId == providerId && x.ClaimId == claimId.Value));

        dbClaimRemittance.Should().NotBeNull();
        dbClaimRemittance.Should().BeEquivalentTo(new
        {
            ClaimId = claimId,
            ProviderId = providerId,
            TotalAmount = Convert.ToDecimal(eraClaim.TotalCharge),
            TotalPaid = Convert.ToDecimal(eraClaim.TotalPaid),
            Items = eraClaim.Charges.Select(c => new
            {
                Date = c.FromDate.ToDateOnly("yyyyMMdd"),
                Code = c.ProcedureCode,
                Units = Convert.ToDecimal(c.Units),
                CurrencyCode = CurrencyCodes.USD,
                Amount = Convert.ToDecimal(c.Charge),
                Allowed = Convert.ToDecimal(c.Allowed),
                Paid = Convert.ToDecimal(c.Paid),
                Adjustments = c.Adjustments.Select(a => new
                {
                    Amount = Convert.ToDecimal(a.Amount),
                    a.Code,
                    a.Group
                })
            })
                .ToArray()
        }, opt => opt.ExcludingMissingMembers());
    }

    private async Task AssertClaimRemittanceDoesNotExist(Guid claimId)
    {
        var dbClaimRemittance = await DataContext.InsuranceClaimRemittances
            .Include(x => x.Items)
            .ThenInclude(x => x.Adjustments)
            .AsNoTracking()
            .FirstOrDefaultAsync(x => x.ClaimId == claimId);

        dbClaimRemittance.Should().BeNull();
    }

    private void AssertClaimPaidNotificationSent(Guid claimId, string paymentReference, Guid payerId)
    {
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send<INotificationsContent>(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<ClaimPaymentNotificationContent>(content =>
                content.ClaimId == claimId &&
                content.PaymentReference == paymentReference &&
                content.PayerId == payerId),
            It.IsAny<Guid[]>()), Times.Once);
    }

    private void AssertNoClaimPaymentNotificationsSent(Guid claimId)
    {
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send(
            ProviderId,
            SystemActors.InsuranceJobActorId,
            It.Is<INotificationsContent>(content => content is ClaimPaymentNotificationContent && ((ClaimPaymentNotificationContent)content).ClaimId == claimId),
            It.IsAny<Guid[]>()), Times.Never);
    }
    
    private void AssertNoClaimStatusNotificationsSent(Guid claimId)
    {
        ResolveService<NotificationsServiceMock>().Verify(s => s.Send(
            ProviderId, 
            SystemActors.InsuranceJobActorId,
            It.Is<INotificationsContent>(content => content is ClaimResponseNotificationContent && ((ClaimResponseNotificationContent)content).ClaimId == claimId),
            It.IsAny<Guid[]>()), Times.Never); 
    }

    private (ERADetailsResponse eraDetailsResponse, ERAClaimDto eraClaim) CreateEraResponse(
        Contact contact,
        ProviderInsurancePayer payer,
        InsuranceClaimUSProfessional claim,
        InsuranceClaimClearingHouseMetadata metadata,
        ChargeAdjustmentDto[] adjustments = null)
    {
        adjustments ??= new ChargeAdjustmentDtoFaker().Generate(2).ToArray();

        var charges = claim.ServiceLines.Select(s => new ERAClaimChargeDtoFaker(s.Id)
            .FromServiceLine(s)
            .WithAdjustments(adjustments)
            .Recalculate()
            .Generate());

        var eraClaim = new ERAClaimDtoFaker()
            .WithCharges([.. charges])
            .RuleFor(x => x.PCN, claim.ClientControlNumber)
            .RuleFor(x => x.PayerICN, metadata.PayerClaimId)
            .Generate();

        var eraDetailResponse = new ERADetailsResponseFaker([eraClaim])
            .RuleFor(c => c.PayerName, payer.Name)
            .RuleFor(c => c.PayerId, payer.PayerId)
            .Generate();

        return (eraDetailResponse, eraClaim);
    }

    private async Task<ReceiveMessageResponse> SendMessage(
        Guid providerId,
        Guid contactId,
        Guid remittanceAdviceId,
        ERADetailsResponse eraDetailsResponse)
    {
        var messageResponse = Fixture.QueueMessage(new ClaimMDProcessContactERAMessage
        {
            ProviderId = providerId,
            ContactId = contactId,
            InsuranceRemittanceAdviceId = remittanceAdviceId,
            ERADetails = ERADetail.Create(eraDetailsResponse)
        });

        await Fixture.Run(new CancellationTokenSource(TimeSpan.FromSeconds(60)).Token);

        return messageResponse;
    }

    private void SetupFileRepositoryMock()
    {
        var fileRepositoryMock = ResolveService<FileStorageRepositoryMock>();

        fileRepositoryMock
            .Setup(s => s.Upload(It.IsAny<FileLocationType>(), It.IsAny<UploadableFile[]>()))
            .ReturnsAsync([.. new SimpleFileFaker().Generate(1)]);
    }

    private InsuranceRemittanceAdvice CreateEraData(ERADetailsResponse eraDetailResponse, ProviderInsurancePayer payer, ProviderBillingProfile billingProfile)
    {
        var clearingHouseLog = new ClearingHouseLogDataModel
        {
            Type = ClearingHouseLogType.Era,
            ClearingHouse = ClearingHouseType.ManagedClaimMd,
            ExternalId = eraDetailResponse.EraId.ToString(),
            Status = ClearingHouseLogStatus.Pending,
            CreatedDateTimeUtc = DateTime.UtcNow,
            UpdatedDateTimeUtc = DateTime.UtcNow,
            RawResponse = JsonDocument.Parse(JsonConvert.SerializeObject(eraDetailResponse))
        };

        var remittanceAdvice = new InsuranceRemittanceAdviceFaker(ProviderId)
            .RuleFor(x => x.BillingProfileId, billingProfile.Id)
            .RuleFor(x => x.PayerId, payer.Id)
            .RuleFor(x => x.PaymentReference, eraDetailResponse.CheckNumber)
            .Generate();

        DataContext.AddRange(clearingHouseLog, remittanceAdvice.ToDataModel());

        return remittanceAdvice;
    }

    private (ERADetailsResponse eraDetailsResponse, InsuranceClaimUSProfessional claim, Contact contact, InsuranceRemittanceAdvice remittaceAdvice) CreateDefaultTestData()
    {
        var (contact, payer, claim, billingProfile, metadata) = CreateClaimTestData();
        var (eraDetailResponse, _) = CreateEraResponse(contact, payer, claim, metadata);
        var remittanceAdvice = CreateEraData(eraDetailResponse, payer, billingProfile);

        return (eraDetailResponse, claim, contact, remittanceAdvice);
    }

    private (
        Contact Contact,
        ProviderInsurancePayer Payer,
        InsuranceClaimUSProfessional Claim,
        ProviderBillingProfile BillingProfile,
        InsuranceClaimClearingHouseMetadata Metadata
    ) CreateClaimTestData(bool createClaimErrors = false)
    {
        var contact = new ContactFaker(ProviderId).Generate();
        var staffPerson = new PersonFaker().Generate();
        var staffMember = new ClaimProviderStaffFaker(ProviderId, staffPerson).Generate();
        var providerInsurancePayer = new ProviderInsurancePayerFaker(ProviderId).Generate();

        var payer = new InsurancePayerFaker()
            .RuleFor(x => x.Name, providerInsurancePayer.Name)
            .RuleFor(x => x.PayerId, providerInsurancePayer.PayerId)
            .Generate();

        var policy = new ContactInsurancePolicyFaker(ProviderId, contact.Id)
            .RuleFor(x => x.Payer, providerInsurancePayer)
            .RuleFor(x => x.PolicyHolder, new InsurancePolicyHolder
            {
                Type = InsurancePolicyHolderType.Client,
                Contact = contact.ToPolicyHolder(),
                RelationshipType = "Client"
            }).Generate();

        var billableItem = CreateBillableTestData(contact, ProviderId);

        var serviceLines = new List<ClaimServiceLine>
        {
            new ClaimServiceLineFaker(ProviderId, contact.Id, billableItem.Id).Generate()
        };

        var defaultBillingProfile = new ProviderBillingProfileFaker(ProviderId)
            .RuleFor(x => x.IsDefault, true)
            .Generate();

        var claim = new USProfessionalClaimFaker(ProviderId)
            .WithBillingDetail(ProviderId, defaultBillingProfile)
            .WithClient(ProviderId, contact)
            .WithContactInsurancePolicy(ProviderId, contact.Id, policy)
            .WithDiagnosticCodes(ProviderId)
            .WithFacility(ProviderId)
            .WithIncident(ProviderId)
            .WithReferringProviders(ProviderId)
            .WithServiceLines([.. serviceLines])
            .WithRenderingProviders(ProviderId, staffMember)
            .RuleFor(x => x.AmountPaid, 0M)
            .RuleFor(x => x.BalancePaid, 0M)
            .Generate();

        var metadata = new InsuranceClaimClearingHouseMetadataFaker(ProviderId, claim).Generate();

        if (createClaimErrors)
        {
            var claimErrors = new InsuranceClaimErrorFaker(ProviderId, claim).Generate(3);
            DataContext.AddRange(claimErrors.Select(s => s.ToDataModel()));
        }

        DataContext.AddRange(
            contact.ToDataModel(),
            staffPerson.ToDataModel(),
            staffMember.ToDataModel(),
            providerInsurancePayer.ToDataModel(),
            payer.ToDataModel(),
            policy.ToDataModel(),
            defaultBillingProfile.ToDataModel(),
            claim.ToDataModel(),
            claim.ToClaimHeaderDataModel(),
            claim.ToClearingHouseIdLookupDataModel(),
            metadata.ToDataModel());

        return (contact, providerInsurancePayer, claim, defaultBillingProfile, metadata);
    }

    private BillableItem CreateBillableTestData(Contact contact, Guid providerId)
    {
        var unchargedBillableItem = new BillableItemFaker(providerId, contact.Id)
            .RuleFor(x => x.Id, Guid.NewGuid)
            .RuleFor(x => x.Price, 100)
            .RuleFor(x => x.ServiceId, (Guid?)null)
            .Generate();

        var unchargedBillable = new BillableDataModel
        {
            Id = Guid.NewGuid(),
            TaskId = null,
            ContactId = contact.Id,
            ProviderId = providerId,
            TotalOwed = 100,
            Date = DateTime.UtcNow.WithoutSecsAndMs(),
            Type = BillableType.Invoice,
            BillableItems = [unchargedBillableItem.ToDataModel()],
        };

        DataContext.AddRangeAsync(unchargedBillable);

        return unchargedBillableItem;
    }
}
