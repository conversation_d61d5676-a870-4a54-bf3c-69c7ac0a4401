using carepatron.core.Abstractions;
using carepatron.core.Application.ClearingHouse.Models;
using carepatron.core.Application.Insurance.Extensions;
using carepatron.core.Application.Insurance.Models;
using carepatron.core.Constants;
using carepatron.core.Repositories.ClearingHouse;
using carepatron.core.Repositories.History;
using carepatron.core.Repositories.Insurance;
using ClaimMD.Module.Models.Http.Dtos;
using ClaimMD.Module.Utilities;
using Messaging;
using Messaging.Handling;
using Serilog;
using Serilog.Context;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using carepatron.core.Application.Insurance.Services;
using ClaimMDConstants = ClaimMD.Module.Constants;

namespace ClaimMD.Module.Messages.Handlers;

[MessageHandler(nameof(ClaimMdClaimStatusMessage))]
public class ClaimMdClaimStatusMessageHandler(
    IInsuranceClaimsRepository insuranceClaimRepository,
    IClearingHouseIdLookupRepository clearingHouseIdLookupRepository,
    IInsuranceClaimUSProfessionalsRepository insuranceClaimUSProfessionalsRepository,
    IInsuranceClaimClearingHouseMetadataRepository insuranceClaimClearingHouseMetadataRepository,
    IInsuranceClaimErrorsRepository insuranceClaimErrorsRepository,
    IEntityHistoryRepository entityHistoryRepository,
    IClearingHouseLogRepository clearingHouseLogRepository,
    IInsuranceService insuranceService,
    IInsurancePayerRepository insurancePayerRepository,
    IUnitOfWork unitOfWork
) : MessageHandlerBase<ClaimMdClaimStatusMessage>
{
    protected override async Task HandleMessage(ClaimMdClaimStatusMessage message, CancellationToken cancellationToken)
    {
        LogContext.PushProperty(nameof(CodeOwner), CodeOwner.BillingAndPayments);
        unitOfWork.UseUnitOfWork();
        await ProcessMessage(message, cancellationToken);
    }

    private async Task ProcessMessage(ClaimMdClaimStatusMessage message, CancellationToken cancellationToken)
    {
        if (message is null) throw new ArgumentNullException(nameof(message));

        var claimUpdate = message.ClaimUpdate;
        if (claimUpdate is null)
        {
            Log.Warning("No claim found in event data");
            return;
        }

        var messageMetadata = ClaimMdMapper.MapMetadata(claimUpdate);
        if (messageMetadata is null)
        {
            Log.Warning("Unable to map claim metadata from event data");
            return;
        }

        var contactId = await clearingHouseIdLookupRepository.Lookup(claimUpdate.PCN, cancellationToken);
        if (contactId == null)
        {
            Log
                .ForContext("PCN", claimUpdate.PCN)
                .Warning("PCN contact Id could not be resolved");
            throw new InvalidOperationException("PCN contact Id could not be resolved");

        }
        using (LogContext.PushProperty("ContactId", contactId.Value))
        using (LogContext.PushProperty("ClaimId", messageMetadata.InsuranceClaimId))
        {
            var claimId = messageMetadata.InsuranceClaimId;
            var claim = await insuranceClaimUSProfessionalsRepository.GetById(id: claimId, cancellationToken);
            if (claim is null)
            {
                Log.Warning("No claim found with id");
                return;
            }

            // TODO 
            // Need to check how many "Acknowledgements" we can get for a submission
            // and whether we need to include any additional messages in the history
            var sender = ResolveSenderType(claimUpdate);

            var messageTimeUtc = ClaimMdDateHelper.MountainDateTimeToUtc(claimUpdate.ResponseTime);
            var pendingNotifications = new List<Func<Task>>();
            switch (claimUpdate.Status?.ToUpper())
            {
                case ClaimMDConstants.ClaimStatusAcknowledged:
                    await ProcessAcknowledgedClaim(
                        messageMetadata,
                        claimUpdate,
                        claim,
                        sender,
                        messageTimeUtc,
                        cancellationToken
                    );
                    break;
                case ClaimMDConstants.ClaimStatusRejected:
                    pendingNotifications.Add(await ProcessRejectedClaim(
                        messageMetadata,
                        claimUpdate,
                        claim,
                        sender,
                        messageTimeUtc,
                        cancellationToken
                    ));
                    break;
                default:
                    Log.Warning("Unknown status {Status} for claim", claimUpdate.Status);
                    break;
            }
            
            Log.Information("Updating clearing house log to processed");

            await clearingHouseLogRepository.UpdateStatus(
                claimUpdate.GetExternalId(),
                messageMetadata.ClearingHouse,
                ClearingHouseLogType.Claim,
                ClearingHouseLogStatus.Processed,
                cancellationToken
            );
            
            await unitOfWork.SaveUnitOfWork(cancellationToken);

            foreach (var notification in pendingNotifications)
            {
                await notification();
            }
        }
    }

    private ClaimHistorySenderType ResolveSenderType(StatusUpdateClaimDto claimUpdate)
    {
        // If sender ID is not provided, we cannot determine the sender type
        if (string.IsNullOrWhiteSpace(claimUpdate.SenderId)) return ClaimHistorySenderType.Other;

        // If sender ID is the same as payer ID, then the sender is the Payer
        var isPayerSender = claimUpdate.SenderId.Equals(claimUpdate.PayerId?.ToUpperInvariant(), StringComparison.InvariantCultureIgnoreCase);
        if (isPayerSender) return ClaimHistorySenderType.Payer;

        // If sender ID is CLM.MD, then the sender is the Clearing House
        if (claimUpdate.SenderId.Equals(Constants.ClaimMdSenderId, StringComparison.InvariantCultureIgnoreCase))
            return ClaimHistorySenderType.ClearingHouse;

        // Anything under the sun is considered Other
        return ClaimHistorySenderType.Other;
    }

    private async Task ProcessAcknowledgedClaim(
        InsuranceClaimClearingHouseMetadata messageMetadata,
        StatusUpdateClaimDto claimUpdate,
        InsuranceClaimUSProfessional claim,
        ClaimHistorySenderType sender,
        DateTime messageTimeUtc,
        CancellationToken cancellationToken
    )
    {
        Log.Information("Processing acknowledged claim");
        var providerId = claim.ProviderId;

        // Update claim metadata
        var clearingHouse = messageMetadata.ClearingHouse;
        var existingMetadata = await insuranceClaimClearingHouseMetadataRepository.GetByClaimId(
            providerId: providerId,
            claimId: claim.Id,
            clearingHouse,
            cancellationToken
        );
        var metadata = existingMetadata?.Clone();

        // Upsert metadata
        if (existingMetadata is null)
        {
            Log.Warning("No metadata found for claim. Creating new metadata");
            metadata = messageMetadata.Clone();
            metadata.ProviderId = providerId;
            metadata.InsuranceClaimId = claim.Id;
            metadata = await insuranceClaimClearingHouseMetadataRepository.Create(metadata, cancellationToken);
        }
        else
        {
            Log.Information("Updating claim metadata");
            // TODO - do we need to make sure we're not overwriting values with empty ones?
            metadata.PayerClaimId = messageMetadata.PayerClaimId;
            metadata.ClearingHouseClaimId = messageMetadata.ClearingHouseClaimId;
            metadata.PayerId = messageMetadata.PayerId;
            metadata.ClearingHouse = messageMetadata.ClearingHouse;
            metadata = await insuranceClaimClearingHouseMetadataRepository.Update(metadata, cancellationToken);
        }

        // History
        var historyMetadata = ClaimReceivedReferenceHistoryModel.Create(existingMetadata, metadata);
        var acknowledgedMessage = claimUpdate.Messages?.FirstOrDefault(m => m.Status == ClaimMDConstants.ClaimStatusAcknowledged);
        var messageDescription = acknowledgedMessage?.Content;
        var history = claim.ToClaimReceivedHistory(historyMetadata, sender, messageTimeUtc, claimUpdate.SenderName, messageDescription);

        await entityHistoryRepository.Create(history, cancellationToken);
    }

    private async Task<Func<Task>> ProcessRejectedClaim(
        InsuranceClaimClearingHouseMetadata messageMetadata,
        StatusUpdateClaimDto claimUpdate,
        InsuranceClaimUSProfessional claim,
        ClaimHistorySenderType sender,
        DateTime messageTimeUtc,
        CancellationToken cancellationToken
    )
    {
        Log.Information("Processing rejected claim");
        var providerId = claim.ProviderId;
        var errors = ClaimMdMapper.MapClaimErrors(claimUpdate, providerId);
        var hasErrors = errors is not null && errors.Length > 0;

        // Check if claim is already paid or partially paid
        // If claim is paid, skip saving errors to prevent showing on client
        // If claim is denied though, we still want to save errors
        // so that we can show them to the user
        var isUpdatePastSubmissionDate = IsUpdatePastSubmissionDate(claim, claimUpdate);
        if (hasErrors && isUpdatePastSubmissionDate)
        {
            await insuranceClaimErrorsRepository.Upsert(claim.Id, providerId, errors, cancellationToken);
        }

        // If a claim is denied, that means the ERA handler has processed the claim
        // already so no need to update status and create history
        if (claim.Status == ClaimStatus.Denied)
        {
            Log.Information("Claim is already denied, skipping updates");
            return () => Task.CompletedTask;
        }

        // If claim is not yet denied, we create a history along with errors`
        var history = claim.ToClaimRejectedHistory(errors, sender, messageTimeUtc);
        await entityHistoryRepository.Create(history, cancellationToken);

        // If update is not past claim's submission date, we skip updating status
        // and saving resubmission code and reference number
        if (!isUpdatePastSubmissionDate) return () => Task.CompletedTask;

        // Update claim status to Rejected, status reason can be null
        // since we're showing claim errors to prevent showing duplicate content
        await insuranceClaimRepository.UpdateStatus(claim.Id, providerId, ClaimStatus.Rejected, null, cancellationToken);
        
        var hasClaimStatusChanged = claim.Status != ClaimStatus.Rejected;

        // Save resubmission code and reference number
        claim.SetStatus(ClaimStatus.Rejected);
        await SaveResubmissionCodeAndReferenceNumber(
            messageMetadata,
            claim,
            providerId,
            claimUpdate,
            cancellationToken
        );
        
        // Only send a notification if the claim has moved to rejected status
        return hasClaimStatusChanged
            ? await CreateRejectionNotification(claim, messageMetadata, cancellationToken)
            : () => Task.CompletedTask;
    }

    private async Task SaveResubmissionCodeAndReferenceNumber(
        InsuranceClaimClearingHouseMetadata messageMetadata,
        InsuranceClaimUSProfessional claim,
        Guid providerId,
        StatusUpdateClaimDto statusUpdateClaim,
        CancellationToken cancellationToken
    )
    {
        var clearingHouse = messageMetadata.ClearingHouse;
        string payerClaimId = null;
        if (!string.IsNullOrEmpty(statusUpdateClaim.SenderId) && !string.IsNullOrEmpty(messageMetadata.PayerId) &&
            statusUpdateClaim.SenderId.Equals(messageMetadata.PayerId, StringComparison.OrdinalIgnoreCase)
        )
        {
            payerClaimId = statusUpdateClaim.SenderICN;
        }
        else
        {
            var metadata = await insuranceClaimClearingHouseMetadataRepository.GetByClaimId(
                providerId: providerId,
                claimId: claim.Id,
                clearingHouse,
                cancellationToken
            );
            if (!string.IsNullOrEmpty(metadata?.PayerClaimId))
            {
                payerClaimId = metadata?.PayerClaimId;
            }
        }

        claim.SetResubmissionCode(ClaimResubmissionCodes.ReplacementOfPriorClaim, payerClaimId);
        await insuranceClaimUSProfessionalsRepository.Update(claim, cancellationToken);
    }

    private bool IsUpdatePastSubmissionDate(InsuranceClaimUSProfessional claim, StatusUpdateClaimDto statusUpdate)
    {
        if (claim is null || statusUpdate is null) return true;

        var statusUpdateDateTime = ClaimMdDateHelper.MountainDateTimeToUtc(statusUpdate.ResponseTime);
        if (claim.LastSubmittedDateTimeUtc.HasValue && claim.LastSubmittedDateTimeUtc.Value >= statusUpdateDateTime)
        {
            Log
                .ForContext("LastSubmittedDateTimeUtc", claim.LastSubmittedDateTimeUtc)
                .Information("Skipping update status for claim as it was already processed");
            return false;
        }

        return true;
    }

    private async Task<Func<Task>> CreateRejectionNotification(InsuranceClaimUSProfessional claim, InsuranceClaimClearingHouseMetadata messageMetadata, CancellationToken cancellationToken)
    {
        var payer = await insurancePayerRepository.GetByPayerNumber(claim.ProviderId, messageMetadata.PayerId, messageMetadata.ClearingHouse, cancellationToken);
        
        return () => insuranceService.SendClaimResponseNotification(
            claim.ProviderId, 
            claim, 
            payerNumber: payer?.PayerId ?? messageMetadata.PayerId, // if we can't find the payer, we need at minimum, the payer number for the notification
            payerName: payer?.Name,
            payerId: payer?.Id);
    }
}
