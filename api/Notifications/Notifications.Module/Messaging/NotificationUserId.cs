using System;

namespace Notifications.Module.Messaging;

public class NotificationUserId
{
    public string? ProviderId { get; init; }

    public string PersonId { get; init; }

    public NotificationUserId(Guid personId, Guid? providerId = null)
    {
        ProviderId = providerId?.ToString();;
        PersonId = personId.ToString();
    }

    public NotificationUserId(string personId, string? providerId = null)
    {
        ProviderId = providerId;
        PersonId = personId;
    }

    public static implicit operator string (NotificationUserId userId)
    {
        return !string.IsNullOrWhiteSpace(userId.ProviderId) ? $"{userId.ProviderId}:{userId.PersonId}" : userId.PersonId;
    }
}
