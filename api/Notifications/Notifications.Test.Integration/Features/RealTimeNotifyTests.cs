using System;
using System.Text.Json;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.AspNetCore.TestHost;
using Newtonsoft.Json.Linq;
using Notifications.Api.Hubs;
using Notifications.Sdk.Contracts.NotificationEvent;
using Notifications.Test.Common.Data.Fakers;
using Notifications.Tests.Component.Builders;
using Notifications.Tests.Component.Setup;

namespace Notifications.Tests.Component.Features;

public class RealTimeNotifyTests : TestBase
{
    public RealTimeNotifyTests(TestFixture fixture) : base(fixture)
    {
    }

    [Fact]
    public async Task SendNotifications_Can_Add_Notifications()
    {
        // Arrange
        var groupId = Guid.NewGuid();
        var url = new Uri($"{Server.BaseAddress}api/notifications/hub?groupId={groupId}");
        var connection = new HubConnectionBuilder()
            .WithUrl(url, o =>
            {
                o.HttpMessageHandlerFactory = _ => Server.CreateHandler();
                o.AccessTokenProvider = () => Task.FromResult(JwtTokenBuilder.BuildResourceAccessAuth(Data.IdentityContext));
            })
            .WithAutomaticReconnect()
            .Build();

        string actualNotificationType = null;
        JObject actualPayload = null;

        connection.On<object>(nameof(INotificationClient.NotificationReceived), (notification) =>
        {
            var j = JObject.Parse(notification.ToString());

            var notificationType = j["notificationType"].Value<string>();
            var payload = j["payload"].Value<JObject>();

            actualNotificationType = notificationType;
            actualPayload = payload;
        });

        await connection.StartAsync();

        var recipients = new NotificationRecipientsRequestFaker()
            .WithPersonIds(new[] { Data.IdentityContext.PersonId })
            .WithGroupId(groupId)
            .Generate(1);

        var payload = new SendRealTimeNotificationRequestFaker()
            .WithNotificationType("test-real-time-notification")
            .WithPayload(new { test = "test" })
            .WithRecipients([.. recipients])
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, "api/notifications/notify")
            .WithAuthToken(JwtTokenBuilder.BuildWriteScopeAuth())
            .WithPayload(payload)
            .Build();

        var response = await Client.SendAsync(request);

        await Task.Delay(500);

        // Cleanup
        await connection.StopAsync();
        await connection.DisposeAsync();

        // Assert
        actualNotificationType.Should().Be("test-real-time-notification");
        actualPayload["test"].Value<string>().Should().Be("test");
    }
    

    [Fact]
    public async Task SendNotifications_Can_Notify_To_Anonymous_Users()
    {
        // Arrange
        var groupId = Guid.NewGuid();
        var anonymousUserId = Guid.NewGuid();
        var url = new Uri($"{Server.BaseAddress}api/notifications/hub?groupId={groupId}&anonymousUserId={anonymousUserId}");
        var connection = new HubConnectionBuilder()
            .WithUrl(url, o => 
            {
                o.HttpMessageHandlerFactory = _ => Server.CreateHandler();
                o.AccessTokenProvider = () => Task.FromResult<string?>(null);
            })
            .WithAutomaticReconnect()
            .Build();

        string actualNotificationType = null;
        JObject actualPayload = null;
        
        connection.On<object>(nameof(INotificationClient.NotificationReceived), (notification) =>
        {
            var j = JObject.Parse(notification.ToString());

            var notificationType = j["notificationType"].Value<string>();
            var payload = j["payload"].Value<JObject>();

            actualNotificationType = notificationType;
            actualPayload = payload;
        });

        await connection.StartAsync();

        var recipients = new NotificationRecipientsRequestFaker()
            .WithPersonIds(new[] { anonymousUserId})
            .WithGroupId(groupId)
            .Generate(1);

        var payload = new SendRealTimeNotificationRequestFaker()
            .WithNotificationType("test-real-time-notification")
            .WithPayload(new { test = "test" })
            .WithRecipients([.. recipients])
            .Generate();

        var request = new HttpRequestMessageBuilder(HttpMethod.Post, "api/notifications/notify")
            .WithAuthToken(JwtTokenBuilder.BuildWriteScopeAuth())
            .WithPayload(payload)
            .Build();

        var response = await Client.SendAsync(request);

        await Task.Delay(500);

        // Cleanup
        await connection.StopAsync();
        await connection.DisposeAsync();

        // Assert
        actualNotificationType.Should().Be("test-real-time-notification");
        actualPayload["test"].Value<string>().Should().Be("test");
    }
}
