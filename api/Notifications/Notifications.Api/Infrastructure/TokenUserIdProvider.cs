using System;
using Microsoft.AspNetCore.SignalR;
using Notifications.Module.Messaging;
using Serilog;
using Shared.Module.Authorization.Identity;

namespace Notifications.Api.Infrastructure;

public class TokenUserIdProvider : IUserIdProvider
{
    private const string AnonymousUserIdKey = "anonymousUserId";

    public string? GetUserId(HubConnectionContext connection)
    {
        var providerId = connection.GetHttpContext()?.Request?.Query["groupId"];

        // Try solving the anonymous user issue by checking parameters first
        if (connection.User?.Identity?.IsAuthenticated != true)
        {
            // If the user is not authenticated, we cannot provide a user ID
            var anonymousUserId = connection.GetHttpContext()?.Request?.Query[AnonymousUserIdKey];

            if (!string.IsNullOrWhiteSpace(anonymousUserId))
            {
                return new NotificationUserId(anonymousUserId, providerId);
            }

            Log.Warning($"Anonymous user id not found for connection: {connection.ConnectionId}");

            return null;
        }

        // Try getting from profile claim first
        var userId = connection.User.GetClaimAsGuid(ClaimTypeValues.Profile);

        // If not found, try the custom:externalPersonId claim
        if (userId == null)
        {
            userId = connection.User.GetClaimAsGuid(ClaimTypeValues.PersonId);
        }

        if (userId == null)
        {
            Log.Warning($"No user id found for groupId: {providerId} - connection: {connection.ConnectionId}");
        }

        return new NotificationUserId(userId?.ToString(), providerId);
    }
}
